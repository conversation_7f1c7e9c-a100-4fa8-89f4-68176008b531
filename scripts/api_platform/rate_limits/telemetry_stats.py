#!/usr/bin/env python3
"""
  This script collects API usage statistics for Rate Limit Cross-epic using API Telemetry via Trino:
    bazel run //scripts/api_platform:ratelimits_telemetry_stats -- --csv=FILE_PATH --group-name=ENDPOINT_SET_NAME

  Usage:
    {filename} --csv=FILE_PATH --group-name=ENDPOINT_SET_NAME

  Options:
      -h --help                         Show this message
      --csv=FILE_PATH                   Cross-epic spreadsheet exported as CSV file
      --group-name=ENDPOINT_SET_NAME    Endpoint set name from column G
                                        (if default values are good, just copy the values from column F for your team)
"""

import csv
import os
import re
import sys
from collections import defaultdict

import docopt
import trino
from rich.console import Console
from rich.progress import Progress, TextColumn, SpinnerColumn
from rich.table import Column, Table

# Replace current filename in docopt
__doc__ = __doc__.format(filename=os.path.basename(__file__))

WINDOW_IN_DAYS = 30
RESULTS_NUM = 30

ENDPOINT_PREFIXES = {
    "v1": "/api/atlas/v1.0",
    "v1.5": "/api/atlas/v1.5",
    "v2": "/api/atlas/v2",
}

REQUEST_TEMPLATE = """
WITH
requests AS (
	SELECT
    	*,
    	SUBSTR(timestamp, 1, 16) AS ts_minute
	FROM
    	awsdatacatalog.raw_cloud_apix.dw__apix__api_telemetry
	WHERE
    	processed_date > current_date - interval ':window:' day AND
    	endpoint IN (:endpoints:) AND
        (:key: <> '' AND :key: IS NOT NULL)
),
top_keys AS (
	SELECT :key:, ts_minute, COUNT(*) AS requests_per_minute
	FROM requests
	GROUP BY :key:, ts_minute
),
requests_per_minute_per_key AS (
	SELECT
    	:key:,
    	MAX(requests_per_minute) AS max_requests_per_minute,
    	CEIL(AVG(requests_per_minute)) AS avg_requests_per_minute,
    	ROUND(MAX(requests_per_minute) / CEIL(AVG(requests_per_minute)), 1) AS max_to_avg
	FROM top_keys
	GROUP BY :key:
)
SELECT
	*
FROM requests_per_minute_per_key
ORDER BY :order_by: DESC
LIMIT :results_num:
"""

SCOPE_TO_KEY = {
    "GROUP": "groupid",
    "ORG": "orgid",
    "USER": "clouduserid",
}


def get_endpoints_by_scope(data):
    result = defaultdict(list)
    for row in data:
        scope, endpoint, versions = row
        versions = versions.split("+")
        for ver in versions:
            prefix = ENDPOINT_PREFIXES[ver]
            result[scope].append(prefix + endpoint)

    return result


def build_request(key, endpoints, order_by):
    replacements = {
        ":key:": key,
        ":endpoints:": ", ".join(["'" + endpoint + "'" for endpoint in endpoints]),
        ":order_by:": order_by,
        ":window:": str(WINDOW_IN_DAYS),
        ":results_num:": str(RESULTS_NUM),
    }

    result = REQUEST_TEMPLATE
    for placeholder, value in replacements.items():
        result = re.sub(placeholder, value, result)
    return result


def get_key(scope):
    key = SCOPE_TO_KEY.get(scope)
    if key is None:
        raise NotImplementedError(f"Scope {scope} is not supported")
    return key


def main():
    args = docopt.docopt(__doc__)

    csv_file = args.get("--csv")
    if not csv_file:
        print("CSV file is required")
        return 1

    group_name = args.get("--group-name")
    if not group_name:
        print("Endpoint set name is required")
        return 1

    print(f"Analyzing rate limits for endpoint group: '{group_name}'")

    data = []
    available_groups = set()
    with open(csv_file, newline="", encoding="utf-8") as csvfile:
        reader = csv.reader(csvfile)
        for row in reader:
            if len(row) > 5:  # Skip header rows
                available_groups.add(row[5])
                if row[5] == group_name:
                    data.append(row[1:4])

    if not data:
        print(f"No endpoints found for group '{group_name}'.")
        print("Available group names:")
        for group in sorted(available_groups):
            if group:  # Skip empty group names
                print(f"  - {group}")
        return 1

    print(f"Found {len(data)} endpoints for analysis")
    endpoints_by_scope = get_endpoints_by_scope(data)

    conn = trino.dbapi.connect(
        host="trino-oauth-gateway.staging.corp.mongodb.com",
        port="443",
        auth=trino.auth.OAuth2Authentication(),
        http_scheme="https",
        catalog="awsdatacatalog",
        schema="awsdatacatalog",
    )

    cur = conn.cursor()

    console = Console()
    text_column = TextColumn("Processing ", table_column=Column(ratio=1))
    progress = Progress(
        text_column, SpinnerColumn(table_column=Column(ratio=2)), console=console, transient=True
    )

    console.print()

    with progress:
        for scope, endpoints in progress.track(endpoints_by_scope.items()):
            if len(endpoints) == 0:
                console.print("Scope", scope, "has no endpoints, skipping")
                continue
            key = get_key(scope)

            results = {}

            sort_keys = ["avg_requests_per_minute", "max_requests_per_minute"]
            for sort_key in sort_keys:
                request = build_request(key, endpoints, sort_key)
                cur.execute(request)

                table = Table(title="Sort key: " + sort_key)
                table.add_column(key, style="cyan")

                columns = ["max_requests_per_minute", "avg_requests_per_minute", "max_to_avg"]
                for col in columns:
                    name = col if sort_key != col else col + " (^)"
                    table.add_column(name, style="magenta")

                for row in cur.fetchall():
                    table.add_row(*[str(i) for i in row])

                results[sort_key] = table

            console.line()
            console.print("==========")
            console.print("Scope:", scope)
            console.print("Endpoints:", ", ".join(endpoints))
            console.print("----------")
            for sort_key, table in results.items():
                console.print(table)

    return 0


if __name__ == "__main__":
    sys.exit(main())
