import itertools
import os
import shlex
from collections import OrderedDict
from mongodb_cloud.evergreen import bazel_test_task_config
from mongodb_cloud.evergreen.bazel_test_task_config import TaskConfiguration
from semver import VersionInfo
from typing import List, Sequence, Dict, Any, Generator

from .get_flaky_enabled_tags import FlakyEnabledTags
from .get_team_mapping_tags import TeamMappingTags

BRS_VERSIONS = list(map(VersionInfo.parse, ["4.0.0", "4.2.0", "7.0.0"]))
BRS_INT_MATRIX_TASKS = [
    "brs_bslurp",
    "brs_bslurp_web",
    "brs_bgrid",
    "brs_bgrid_dao",
    "brs_bgrid_svc",
    "brs_bgrid_san",
    "brs_bgrid_store",
]

BRS_CONTROLLED_VERSION = [VersionInfo.parse("4.2.0")]
BRS_CONTROLLED_VERSION_TASKS = ["brs", "brs_other"]

INT_TEST_TAGS = [
    # This ensures that Foliage tickets will be opened as a P2 for any failing or flakey tests
    "foliage_health",
    # There are generally many tests in an int test task and if all of them fail we do not want them to consume all of our foliage quota
    "foliage_check_task_only",
]

EVERGREEN_OPEN_PORT = "9080"

SIX_MIN_IN_SECS = 6 * 60
FOUR_HOUR_IN_SECS = 4 * 60 * 60

SECOND_MONGOD_BASE_PORT = "28500"


def _is_brs_int(task_configuration: TaskConfiguration, package: str) -> bool:
    return task_configuration.name in BRS_INT_MATRIX_TASKS and package.startswith(
        "//server/src/test"
    )


def _is_brs_controlled_version_task(task_configuration: TaskConfiguration, package: str) -> bool:
    return task_configuration.name in BRS_CONTROLLED_VERSION_TASKS and package.startswith(
        "//server/src/test"
    )


def _is_regional_int(task_configuration: TaskConfiguration, package: str) -> bool:
    return task_configuration.name == "mms_regionalization" and package.startswith(
        "//server/src/test"
    )


INTEGRATION_BRS_CONTROLLED_VERSION_ENGINE_MATRIX: Any = {
    "wiredTiger": {
        "task_name_format_string": "{task_name}_{version}_WT",
        "versions": BRS_CONTROLLED_VERSION,
    },
}

INTEGRATION_BRS_VERSION_ENGINE_MATRIX: Any = {
    "wiredTiger": {
        "task_name_format_string": "{task_name}_{version}_WT",
        "versions": BRS_VERSIONS,
    },
}

# These don't need to track GridTestConstants because the system properties are not read in other test suites than BRS
ALL_OTHERS_VERSION_ENGINE_MATRIX = {
    "wiredTiger": {
        "task_name_format_string": "{task_name}",
        "versions": [VersionInfo.parse("4.0.0")],
    }
}

AUTHZ_SERVICE_TASK_NAMES = [
    "INT_JAVA_AUTHZ",
]

CLUSTER_CONNECTION_SERVICE_TASK_NAMES = [
    "INT_JAVA_CLUSTERCONNECTION",
]

LOCALSTACK_TASK_NAMES = [
    "INT_JAVA_MMS_CAP",
    "INT_JAVA_MMS_MODULES",
    "INT_JAVA_MMS_INTEL_METRICS",
]

# Currently only CpsSliceResourceIntTests, CpsSvcIntTests and MetricRollupScheduleForDualReadsAndCutoverIntTests require a
# second mongod
SECOND_MONGOD_TASK_CONFIG_NAMES = [
    "nds_res",
    "nds_common_svc_cps",
    "mms_intel_metrics_dual_reads",
]

FERN_EXCLUDE_LIST = []


def generate_platforms(distro: str) -> Sequence[Dict[str, Any]]:
    default_platform = {
        "platform": "linux",
        "arch": "x86_64",
        "aojdk_arch": "x64",
        "mms_build_arch": "amd64",
        "distro": distro,
        "discriminator": "",
        "display_name": {
            "platform": "Amazon Linux 2",
            "arch": "Intel",
            "prefix": "a",
        },
        "mdb_latest": True,
    }
    return [default_platform]


def generate_tasks(
    package: str,
    task_configs: List[bazel_test_task_config.TaskConfiguration],
    task_name_format_string: str,
    variant_name_format_string: str,
    variant_display_name_format_string: str,
    task_id: str = None,
    is_patch: bool = False,
    is_stepback: bool = True,
    github_pr_number: str = None,
    use_engflow_cache: bool = False,
    use_fern_int_percent: str = None,
    distro: str = "",
    is_merge: bool = False,
) -> Dict[str, Any]:
    is_github_pr = github_pr_number is not None
    use_engflow_cache = False if use_engflow_cache is None else use_engflow_cache
    use_fern_int_percent = "0" if use_fern_int_percent is None else use_fern_int_percent
    distro = distro if distro else "amazon2-cloud-large"

    tasks: Dict = {
        "buildvariants": [],
        "task_groups": [],
        "tasks": [],
        "fern_int_count": 0,
        "fern_int_tests": [],
    }
    team_tags = TeamMappingTags()
    platforms = generate_platforms(distro=distro)

    variants = _get_build_variants(
        platforms,
        package,
        variant_name_format_string,
        variant_display_name_format_string,
        is_github_pr,
        is_patch,
        is_stepback,
        use_engflow_cache,
    )

    use_fern_int_percent_float = 0.0
    try:
        use_fern_int_percent_float = float(use_fern_int_percent)
    except ValueError:
        use_fern_int_percent_float = 0.0

    # Stick a task host in the middle if requested
    task_group_max_hosts = _task_group_max_hosts()
    is_running_in_task_group = task_group_max_hosts > 0
    if is_running_in_task_group > 0:
        task_group_name = variants[0]["name"] + "-group"

        tasks["task_groups"].append(
            {
                "name": task_group_name,
                "max_hosts": task_group_max_hosts,
                "tasks": [],
                "setup_group": [
                    {"func": "install AL2 packages and Chrome"},
                    {"func": "fetch source"},
                    {"func": "install bazel"},
                ],
                "setup_group_can_fail_task": True,
                "setup_task": [
                    {"func": "generate cloning token"},
                    {"func": "reset git credentials"},
                ],
                "setup_task_can_fail_task": True,
                "teardown_task": [{"func": "post task"}],
                "teardown_group": [{"func": "post machine"}],
            }
        )

        if package.startswith("//server/src/test"):
            tasks["task_groups"][-1]["setup_group"] += [
                _get_install_mongod_command(
                    True
                ),  # Safe to default to True so version can be pulled from os.environment variables
            ]

        # If we are supposed to use fern, set up the cluster once in the setup group
        if use_fern_int_percent_float > 0.0:
            tasks["task_groups"][-1]["setup_group"] += [
                {"func": "start k0s cluster"},
            ]

        for variant in variants:
            variant["tasks"].append({"name": task_group_name})

    tasks["buildvariants"].extend(variants)

    # Load the flaky enabled mapping file
    variant_flaky_enabled_mapping = FlakyEnabledTags()

    if not _is_existing_variant():
        # Integration test specific expansions
        # remote cache specific expansions
        for variant in tasks["buildvariants"]:
            variant["expansions"]["mongodBasePort"] = 26000

            # Add the enable flaky detection where needed.
            is_enabled_for_flaky_detection: bool = variant_flaky_enabled_mapping.get_value_for_name(
                variant["name"]
            )
            if is_enabled_for_flaky_detection:
                if "tags" not in variant:
                    variant["tags"] = ["foliage_flaky_detection_enabled"]
                else:
                    variant["tags"].append("foliage_flaky_detection_enabled")

    task_params = []
    for task_configuration in task_configs:
        task_name_base = task_name_format_string.format(
            task_configuration.name.upper(), package.upper()
        )

        if _is_brs_int(task_configuration, package):
            version_engine_matrix = INTEGRATION_BRS_VERSION_ENGINE_MATRIX
        elif _is_brs_controlled_version_task(task_configuration, package):
            version_engine_matrix = INTEGRATION_BRS_CONTROLLED_VERSION_ENGINE_MATRIX
        else:
            version_engine_matrix = ALL_OTHERS_VERSION_ENGINE_MATRIX

        for storage_engine, versions_for_engine in version_engine_matrix.items():
            for mongodb_version in versions_for_engine["versions"]:
                task_name = versions_for_engine["task_name_format_string"].format(
                    task_name=task_name_base,
                    version=str(mongodb_version.major) + str(mongodb_version.minor),
                )

                task_params += [(task_name, task_configuration, storage_engine, mongodb_version)]

    task_count = len(task_params)

    for i, task_param in enumerate(task_params):
        (task_name, task_configuration, storage_engine, mongodb_version) = task_param
        use_fern = i / float(task_count) < use_fern_int_percent_float / 100.0
        use_fern = use_fern and task_name not in FERN_EXCLUDE_LIST
        _append_task(
            task_name,
            task_configuration,
            storage_engine,
            mongodb_version,
            package,
            tasks,
            team_tags,
            task_id=task_id,
            is_running_in_task_group=is_running_in_task_group,
            is_merge=is_merge,
            use_fern=use_fern,
        )

    filtered_variants = [variant for variant in tasks["buildvariants"] if len(variant["tasks"]) > 0]
    tasks["buildvariants"] = filtered_variants
    return tasks


def _append_task(
    task_name: str,
    task_configuration: bazel_test_task_config.TaskConfiguration,
    storage_engine: str,
    mongodb_version: VersionInfo,
    package: str,
    tasks: Any,
    team_tags: TeamMappingTags,
    task_id: str = None,
    is_running_in_task_group: bool = False,
    is_merge: bool = False,
    use_fern: bool = False,
):
    tasks["tasks"].append({"name": task_name, "commands": []})

    # If we find an owner for the task in the mapping file, add an assignment tag for the team
    team_assignment_tag = team_tags.get_value_for_name(task_name)
    if not team_assignment_tag:
        raise KeyError(
            f"{task_name} is missing a Foliage owner. "
            f"All generated tasks must have assigned Foliage owners in "
            f"`mms/scripts/evergreen/dynamic_task_team_mapping.yaml`"
        )
    tasks["tasks"][-1]["tags"] = [team_assignment_tag]

    # Integration test setup requires a mongod
    if package.startswith("//server/src/test"):  # Integration tests only
        # Add int-test-specific tags
        tasks["tasks"][-1]["tags"] += INT_TEST_TAGS

        if not is_running_in_task_group:
            # Test prereqs
            tasks["tasks"][-1]["commands"] += [
                {"func": "install mongodb and tools"},
            ]

        tasks["tasks"][-1]["commands"] += [
            {"func": "setup conf-test-secure for int"},
            {"func": "setup service-test-secure for standalone services"},
            {"func": "install mongosh"},
        ]

        if use_fern:
            # If we are running in a task group, we already have started the cluster
            if not is_running_in_task_group:
                tasks["tasks"][-1]["commands"] += [
                    {"func": "start k0s cluster"},
                ]
            tasks["tasks"][-1]["commands"] += [
                {"func": "setup aws config for fern"},
                {"func": "setup fern"},
            ]
            tasks["fern_int_count"] += 1
            tasks["fern_int_tests"] += [task_name]

        if task_name in CLUSTER_CONNECTION_SERVICE_TASK_NAMES:
            tasks["tasks"][-1]["commands"] += [
                {"func": "setup cluster on port 29019 with auth"},
            ]

        tasks["tasks"][-1]["commands"] += _get_start_and_wait_mongod_funcs(
            use_fern,
            task_configuration.name in SECOND_MONGOD_TASK_CONFIG_NAMES,
        )

        if task_name in LOCALSTACK_TASK_NAMES:
            tasks["tasks"][-1]["commands"] += [{"func": "start localstack"}]

        if task_name in AUTHZ_SERVICE_TASK_NAMES:
            tasks["tasks"][-1]["commands"] += [
                {"func": "start local redis for authz service"},
            ]

        if task_configuration.name in ["mms_intel_metrics_dual_reads"]:
            tasks["tasks"][-1]["commands"] += [
                {
                    "func": "add metrics properties",
                },
            ]

        # metrics standalone service owned by the intel team needs a clickhouse cluster and redis as int test dependencies
        if task_configuration.name in ["metrics"]:
            tasks["tasks"][-1]["commands"] += [{"func": "prepare for metrics service"}]

    tasks["tasks"][-1]["commands"] += [
        {
            "command": "s3.get",  # Load the BUILD.bazel file generated by the generator task into each generated task
            "params": {
                "bucket": "mciuploads",
                "remote_file": r"${mciuploads_dir|mms}" + f"/{task_id}/BUILD.bazel",
                "aws_key": r"${mciuploads_aws_key}",
                "aws_secret": r"${mciuploads_aws_secret}",
                "local_file": f"mms/{package[2:]}/BUILD.bazel",
            },
        },
    ]

    if tasks["buildvariants"][0]["name"].startswith("int_external"):
        test_grouping = package + ":nds"  # int_external always uses :nds_init

        # Custom logback config
        tasks["tasks"][-1]["commands"] += [
            {
                "command": "subprocess.exec",
                "params": {
                    "command": r'cp "${workdir}/mms/scripts/evergreen/logback-test-external-int.xml" "${workdir}/mms/server/conf/logback/logback-test-custom.xml"'
                },
            }
        ]
    elif tasks["buildvariants"][0]["name"].startswith("nds_load_tests"):
        test_grouping = package + ":nds"

        tasks["tasks"][-1]["commands"] += [
            {
                "func": "configure and run prometheus",
            },
        ]
    else:
        test_grouping = f"{package}:{task_configuration.name}"

    if package.startswith("//server/src/test"):  # Integration tests only
        tasks["tasks"][-1]["commands"] += [
            {
                "func": "run bazel test init",
                "vars": {"test_grouping": test_grouping},
            },
        ]

    # Run the targets
    for target_spec in task_configuration.targets:
        # Case when target_spec as a single test to run
        if target_spec.filters is None:
            (target_specific_package, target) = target_spec.name.split(":")
            absolute_target = target_spec.name

        # Case when target_spec has query filters of which tests to run
        else:
            target_specific_package = package
            target = target_spec.name
            absolute_target = f"{target_specific_package}:{target}"

        log_path = f"{target_specific_package[2:]}/{target}"

        jvm_flags = [
            "-DSTORAGE.ENGINE.FOR.BRS.TESTS=" + storage_engine,
            "-DMONGODB.VERSION.FOR.BRS.TESTS=" + str(mongodb_version),
            "-Dskip.ppc.builds=${skip_ppc_builds}",
            "-Dskip.s390x.builds=${skip_s390x_builds}",
            "-Dmms.buildIndexesAndShardCollections=true",
        ]

        if _is_regional_int(task_configuration, package):
            jvm_flags.append("-Dmms.regionName=eu-west-1")

        if tasks["buildvariants"][0]["name"].startswith("int_external"):
            jvm_flags += [
                "-Dhttp-test-port=" + EVERGREEN_OPEN_PORT,
                "-Dbase-port=" + EVERGREEN_OPEN_PORT,
            ]

        jvm_flags += shlex.split(os.environ.get("JVM_FLAGS", ""))

        bazel_options = (
            f"--test_output=streamed --test_arg=--jvm_flags={shlex.quote(' '.join(jvm_flags))}"
        )

        if package.startswith("//server/src/test"):
            bazel_options += " --test_env=PATH=/bin:/usr/bin:${workdir}/mms/mongodb/bin"

        bazel_test_vars = {
            "bazel_target": absolute_target,
            "bazel_options": bazel_options,
        }

        if tasks["buildvariants"][0]["name"].startswith("int_external"):
            bazel_test_vars["test_timeout"] = "--test_timeout=" + str(FOUR_HOUR_IN_SECS)

        if tasks["buildvariants"][0]["name"] == "unit_java":
            bazel_test_vars["test_timeout"] = "--test_timeout=" + str(SIX_MIN_IN_SECS)

        tasks["tasks"][-1]["commands"] += [
            {
                "func": "run bazel test",
                "vars": bazel_test_vars,
            },
            {
                "func": "attach bazel test logs",
                "vars": {"bazel_test_target": log_path},
            },
        ]
        if tasks["buildvariants"][0]["name"].startswith("nds_load_tests"):
            tasks["tasks"][-1]["commands"] += [
                {
                    "func": "save prometheus data files",
                },
            ]

    # Add the task to the task group if using task groups
    if len(tasks["task_groups"]) > 0:
        tasks["task_groups"][0]["tasks"].append(task_name)

    # Add the task directly to each variant otherwise
    else:
        for variant in tasks["buildvariants"]:
            # Skipping old storage engines / versions for brs tests in int_mdb_latest
            if (
                _is_brs_int(task_configuration, package)
                and "expansions" in variant
                and variant["expansions"]["mongodb_version"] == "latest"
                and (storage_engine == "mmapv1" or mongodb_version.compare("4.0.0") < 0)
            ):
                continue

            if is_merge and not task_configuration.run_on_merge:
                continue

            if "not_required" in variant["name"] and not task_configuration.required:
                variant["tasks"].append(task_name)
            elif "not_required" not in variant["name"] and task_configuration.required:
                variant["tasks"].append(task_name)


def _get_start_and_wait_mongod_funcs(use_fern: bool, need_second_mongod: bool) -> List[Any]:
    post_fix = "" if not use_fern else " (fern)"

    if not need_second_mongod:
        return [
            {"func": "start mongod single node replicaset" + post_fix},
            {"func": "wait for single node mongod replicaset"},
        ]

    return [
        {"func": "start mongod single node replicaset" + post_fix},
        {"func": "wait for single node mongod replicaset"},
        {
            "func": "start mongod single node replicaset" + post_fix,
            "vars": {"mongodBasePort": SECOND_MONGOD_BASE_PORT},
        },
        {
            "func": "wait for single node mongod replicaset",
            "vars": {"mongodBasePort": SECOND_MONGOD_BASE_PORT},
        },
    ]


def _get_install_mongod_command(is_nds):
    install_mongod: Dict[str, Any] = {"func": "install mongodb and tools", "vars": {}}
    if is_nds:
        # This is for if Atlas cluster can be on a newer version of MongoDB
        install_mongod["vars"]["mongodb_version"] = os.environ["MONGODB_VERSION"]
    # Including MongoDB arch to support versions after 4.2, which removed generic linux
    install_mongod["vars"]["mongodb_arch"] = os.environ["MONGODB_ARCH"]

    return install_mongod


def _cache_expansion_variants(
    use_engflow_cache: bool,
) -> Generator[Dict[str, str], None, None]:
    # which cache mechanisms are enabled as per vars passed to the generator
    available_cache_expansions = OrderedDict(
        [
            ("use_engflow_cache", use_engflow_cache),
        ]
    )

    if not any(available_cache_expansions.values()):
        yield {k: "false" for k in available_cache_expansions.keys()}
        return

    # yield a set of expansions for enabling each one, with the others disabled
    for k, v in available_cache_expansions.items():
        if v:
            expansions_list = [(k, "true")]
            expansions_list += list(
                map(
                    lambda other_k: (other_k, "false"),
                    available_cache_expansions.keys() - {k},
                )
            )
            yield dict(expansions_list)


def _get_build_variants(
    platforms: Sequence[Dict[str, Any]],
    package: str,
    variant_name_format_string: str,
    variant_display_name_format_string: str,
    is_pr: bool,
    is_patch: bool,
    is_stepback: bool,
    use_engflow_cache: bool,
) -> Sequence[Dict[str, Any]]:
    variants = []

    # Expansions for Bazel remote cache
    cache_expansion_variants = list(_cache_expansion_variants(use_engflow_cache=use_engflow_cache))
    default_cache_mechanism = next(
        filter(lambda item: item[1] == "true", cache_expansion_variants[0].items()),
        (None, "false"),
    )[0]

    default_backing_db_version = os.environ["MONGODB_VERSION"]

    # mdb_latest only applies to integration tests, but don't generate the variant for PR checks / patches
    if package == "//server/src/test" and not is_pr and not is_patch:
        backing_db_versions = [default_backing_db_version, "current", "latest"]
    else:
        backing_db_versions = [default_backing_db_version]

    jdk_versions = [17]
    for (
        platform,
        backing_db_version,
        cache_expansions,
        jdk_version,
    ) in itertools.product(platforms, backing_db_versions, cache_expansion_variants, jdk_versions):
        # Don't make a variant for mdb_current and mdb_latest if the platform shouldn't get it
        if backing_db_version in ("current", "latest") and not platform["mdb_latest"]:
            continue

        # look at which one is enabled to pick the variant name
        current_cache_mechanism = next(
            filter(lambda item: item[1] == "true", cache_expansions.items()),
            (None, "false"),
        )[0]

        variant_name = variant_name_format_string.format(discriminator=platform["discriminator"])

        default_batch_time = ""
        if "variant_id_suffix" in platform:
            variant_name += platform["variant_id_suffix"]
        if backing_db_version == "current":
            default_batch_time = "1440"
            variant_name += "_mdb_current"
        elif backing_db_version == "latest":
            default_batch_time = "1440"
            variant_name += "_mdb_latest_master"

        if current_cache_mechanism != default_cache_mechanism:
            variant_name += f"_{current_cache_mechanism}"

        variant_display_name = variant_display_name_format_string.format(
            prefix=platform["display_name"]["prefix"],
            platform_friendly=f'{platform["display_name"]["platform"]} - {platform["display_name"]["arch"]}',
            variant_name=variant_name,
        )
        if backing_db_version == "latest" and "sort_char" not in platform["display_name"]:
            sort_char = "b"  # beginning of the end of the variant list
        elif backing_db_version == "current" and "sort_char" not in platform["display_name"]:
            sort_char = "c"  # beginning of the end of the variant list
        elif backing_db_version == "latest" and "sort_char" in platform["display_name"]:
            # One after the non-latest variant
            sort_char = chr(ord(platform["display_name"]["sort_char"]) + 1)
        elif "sort_char" in platform["display_name"]:
            # The non latest variant
            sort_char = platform["display_name"]["sort_char"]
        else:
            sort_char = ""  # already sorted

        variant_display_name = sort_char + variant_display_name

        variant = {
            "name": variant_name,
            "tasks": [],
        }

        if not _is_existing_variant():
            # for variants that already exist, we're just appending tasks, and the only property that can
            #   be defined is 'name'. so we only assemble the full set of properties for a buildvariant if the
            #   'variant_exists' expansion is not present in the environment.
            variant = {
                "name": variant_name,
                "display_name": variant_display_name,
                "run_on": platform["distro"],
                "stepback": is_stepback,
                "modules": ["fern"],
                "expansions": {
                    "jdk_version": jdk_version,
                    "platform": platform["platform"],
                    "arch": platform["arch"],
                    "aojdk_arch": platform["aojdk_arch"],
                    "bazel_buildtools_version": os.environ["BAZEL_BUILDTOOLS_VERSION"],
                    "mongodb_arch": os.environ["MONGODB_ARCH"],
                    **cache_expansions,
                },
                "tasks": [],
            }
            if "mms_build_arch" in platform:
                variant["expansions"]["mms_build_arch"] = platform["mms_build_arch"]

            if backing_db_version == "latest":
                variant["expansions"]["mongodb_version"] = "latest"
                variant["expansions"]["mongodb_version_override"] = "latest"
            elif backing_db_version == "current":
                variant["expansions"]["mongodb_version"] = "current"
                variant["expansions"]["mongodb_version_override"] = os.environ[
                    "MONGODB_CURRENT_VERSION"
                ]
            elif len(backing_db_version) > 0:
                variant["expansions"]["mongodb_version"] = backing_db_version

            batch_time = os.environ.get(
                "batch_time", default_batch_time
            )  # generator level batchtime
            if len(batch_time) > 0:
                variant["batchtime"] = int(batch_time)
            elif "batch_time" in platform:  # platform-level batchtime
                variant["batchtime"] = platform["batch_time"]

            cron = os.environ.get("cron", None)
            if cron:
                variant["cron"] = cron

        if _single_variant():
            return [variant]

        variants.append(variant)

    return variants


def _is_cloud_release_project() -> bool:
    if "deploy_step" not in os.environ:
        return False  # fail safe

    return os.environ["deploy_step"] in ("QA", "PROD")


def _single_variant() -> bool:
    return os.environ.get("single_variant", "0") == "1"


def _is_existing_variant() -> bool:
    return os.environ.get("variant_exists", "0") == "1"


def _task_group_max_hosts() -> int:
    """
    Return the number of hosts to use for the tax group

    Returns 0 if it shouldn't use a task group
    """

    return int(os.environ.get("task_group_max_hosts", "0"))
