["server/src/features/com/xgen/svc/cukes/helpers/BaseAgentManager.java", "server/src/features/com/xgen/svc/cukes/helpers/nds/E2EClusterConfig.java", "server/src/features/com/xgen/svc/cukes/net/AWSClient.java", "server/src/features/com/xgen/svc/cukes/steps/nds/CloudProviderAccessStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/DataLakeStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/EncryptionAtRestStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NDSMigrationStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NDSPrivateLinkStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NdsAWSPeeringStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NdsAtlasTelemetryStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NdsBaseStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NdsDBConnectSecurityStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NdsDataValidationStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NdsDiskSnapshotRestoreStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NdsKinesisIngestionStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NdsOnlineArchiveDataExpirationStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NdsOnlineArchiveStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/NdsPrivateNetworkSettingsStepDefs.java", "server/src/features/com/xgen/svc/cukes/steps/nds/PushBasedLogExportStepDefs.java", "server/src/main/com/xgen/cloud/alerts/checks/system/_public/svc/AWSAZStatusCheckSvc.java", "server/src/main/com/xgen/cloud/apiusagedata/_private/config/KinesisClientConfig.java", "server/src/main/com/xgen/cloud/apiusagedata/_private/svc/KinesisClientBatchDispatcher.java", "server/src/main/com/xgen/cloud/apiusagedata/_private/svc/KinesisClientProducer.java", "server/src/main/com/xgen/cloud/billingimport/aws/_public/svc/AwsImportedUsageSvc.java", "server/src/main/com/xgen/cloud/billingplatform/model/sku/_public/model/SKU.java", "server/src/main/com/xgen/cloud/billingplatform/model/sku/_public/model/SKUInfo.java", "server/src/main/com/xgen/cloud/brs/core/_private/dao/S3BaseDao.java", "server/src/main/com/xgen/cloud/brs/core/_private/dao/S3OplogStoreDao.java", "server/src/main/com/xgen/cloud/brs/core/_public/client/S3ClientV1.java", "server/src/main/com/xgen/cloud/brs/core/_public/client/S3ClientV1Factory.java", "server/src/main/com/xgen/cloud/brs/core/_public/model/AwsCredentialsWrapper.java", "server/src/main/com/xgen/cloud/brs/core/_public/model/CopyS3Request.java", "server/src/main/com/xgen/cloud/brs/core/_public/san/s3/S3BlockDao.java", "server/src/main/com/xgen/cloud/brs/core/_public/san/s3/S3MetadataDao.java", "server/src/main/com/xgen/cloud/brs/core/_public/store/oplog/S3OplogStore.java", "server/src/main/com/xgen/cloud/brs/core/_public/svc/AwsBackupSvc.java", "server/src/main/com/xgen/cloud/brs/core/_public/svc/S3ValidatorSvc.java", "server/src/main/com/xgen/cloud/brs/core/_public/util/S3TaskExecutor.java", "server/src/main/com/xgen/cloud/brs/daemon/_public/grid/groom/S3Groom.java", "server/src/main/com/xgen/cloud/brs/daemon/_public/grid/job/S3OplogOrphanSliceJob.java", "server/src/main/com/xgen/cloud/common/appsettings/_public/svc/AppSettings.java", "server/src/main/com/xgen/cloud/common/aws/_public/AwsUtils.java", "server/src/main/com/xgen/cloud/common/aws/_public/clients/AWSClientsFactory.java", "server/src/main/com/xgen/cloud/common/aws/_public/clients/AWSCredentialsUtil.java", "server/src/main/com/xgen/cloud/common/aws/_public/s3/S3Uploader.java", "server/src/main/com/xgen/cloud/common/aws/_public/secret/AwsManagedSecrets.java", "server/src/main/com/xgen/cloud/common/awsv2/_public/clients/AwsCredentialsUtilV2.java", "server/src/main/com/xgen/cloud/common/awsv2/_public/sesv2/AwsSesV2ClientProvider.java", "server/src/main/com/xgen/cloud/dataexport/_public/svc/DataExportSvc.java", "server/src/main/com/xgen/cloud/nds/aws/_private/model/registry/AWSInstanceHardwareProvider.java", "server/src/main/com/xgen/cloud/nds/aws/_public/model/AWSHardwareSpec.java", "server/src/main/com/xgen/cloud/nds/aws/_public/model/AWSInstanceHardware.java", "server/src/main/com/xgen/cloud/nds/aws/_public/model/AWSNDSInstanceSize.java", "server/src/main/com/xgen/cloud/nds/aws/_public/model/AWSPeerVpc.java", "server/src/main/com/xgen/cloud/nds/aws/_public/model/ChunkedS3InputStream.java", "server/src/main/com/xgen/cloud/nds/aws/_public/model/healthcheck/AWSHardwareResourceStatus.java", "server/src/main/com/xgen/cloud/nds/aws/_public/model/ui/NDSAWSDescribeInstanceStatusView.java", "server/src/main/com/xgen/cloud/nds/aws/_public/model/ui/NDSAWSDescribeVolumeStatusView.java", "server/src/main/com/xgen/cloud/nds/aws/_public/svc/AWSApiSvc.java", "server/src/main/com/xgen/cloud/nds/aws/_public/svc/AWSDNSRecordObservable.java", "server/src/main/com/xgen/cloud/nds/aws/_public/svc/healthcheck/AWSHardwareResourceStatusProvider.java", "server/src/main/com/xgen/cloud/nds/aws/_public/util/AWSApiUtils.java", "server/src/main/com/xgen/cloud/nds/aws/_public/util/PolicyUtils.java", "server/src/main/com/xgen/cloud/nds/aws/_public/view/admincapacity/ScheduledAWSInstanceCapacitySpecView.java", "server/src/main/com/xgen/cloud/nds/billing/_private/svc/EstimateSvcImpl.java", "server/src/main/com/xgen/cloud/nds/billing/_public/model/NDSStorageUsage.java", "server/src/main/com/xgen/cloud/nds/billing/_public/svc/EstimateSvc.java", "server/src/main/com/xgen/cloud/nds/billing/_public/util/NDSMeterIdMappingUtil.java", "server/src/main/com/xgen/cloud/nds/cloudprovider/_public/model/registry/InstanceHardwareProvider.java", "server/src/main/com/xgen/cloud/nds/cloudprovider/_public/model/ui/chef/TempCredentialView.java", "server/src/main/com/xgen/cloud/nds/dataexfiltrationprevention/_public/svc/AWSSecurityGroupsPerNetworkInterfaceValidationCronSvc.java", "server/src/main/com/xgen/cloud/nds/datavalidation/_public/util/AWSDataValidationInstanceUtil.java", "server/src/main/com/xgen/cloud/nds/dns/_public/model/DNSChangeItem.java", "server/src/main/com/xgen/cloud/nds/dns/_public/util/DNSRecordUtil.java", "server/src/main/com/xgen/cloud/nds/hostname/_public/svc/NDSHostnameSvc.java", "server/src/main/com/xgen/cloud/nds/ifr/_public/svc/IFRSvc.java", "server/src/main/com/xgen/cloud/nds/project/_public/model/ClusterDescription.java", "server/src/main/com/xgen/cloud/nds/temporarydownloadlinks/_public/svc/TemporaryDownloadLinkSvc.java", "server/src/main/com/xgen/cloud/nds/vmimage/_public/store/NDSRemoteImageStore.java", "server/src/main/com/xgen/cloud/partners/aws/_private/svc/AwsSelfServeApiSvcImpl.java", "server/src/main/com/xgen/cloud/partners/aws/_private/svc/AwsSnsNotificationApiSvcImpl.java", "server/src/main/com/xgen/cloud/partners/aws/_private/svc/AwsSnsNotificationSvc.java", "server/src/main/com/xgen/cloud/partners/aws/_private/svc/SnsSubscriptionsHandler.java", "server/src/main/com/xgen/cloud/partners/aws/_public/svc/AwsResolveCustomerApiSvc.java", "server/src/main/com/xgen/cloud/partners/aws/runtime/res/AwsMarketplaceApiResource.java", "server/src/main/com/xgen/cloud/partners/disbursement/_private/svc/aws/AwsDisbursementReportDataIngestorImpl.java", "server/src/main/com/xgen/cloud/partners/disbursement/_private/svc/aws/athena/AmazonAthenaProvider.java", "server/src/main/com/xgen/cloud/partners/disbursement/_private/svc/aws/athena/AthenaAwsCredentialsProviderImpl.java", "server/src/main/com/xgen/cloud/partners/disbursement/_private/svc/aws/athena/AthenaDisbursementReportRowSerializer.java", "server/src/main/com/xgen/cloud/partners/disbursement/_private/svc/aws/athena/AthenaMapRowSerializer.java", "server/src/main/com/xgen/cloud/partners/disbursement/_private/svc/aws/athena/AthenaQueryExecutorImpl.java", "server/src/main/com/xgen/cloud/partners/disbursement/_public/svc/aws/athena/AthenaRowSerializer.java", "server/src/main/com/xgen/cloud/partners/disbursement/runtime/guice/DisbursementModule.java", "server/src/main/com/xgen/cloud/partners/registration/_private/svc/AwsRegistrationSvc.java", "server/src/main/com/xgen/cloud/partners/usage/aws/_public/svc/AwsApiSvc.java", "server/src/main/com/xgen/cloud/partners/usage/aws/_public/svc/AwsApiSvcImpl.java", "server/src/main/com/xgen/cloud/partners/usage/report/_private/svc/AgreementSearchAdminSvcImpl.java", "server/src/main/com/xgen/cloud/partners/usage/report/_private/svc/AwsUsageReportSvcImpl.java", "server/src/main/com/xgen/cloud/partners/usage/report/_private/svc/ReportingFailuresAdminSvcImpl.java", "server/src/main/com/xgen/cloud/partners/usage/reporting/_private/svc/AwsUsageReportingSchedulerImpl.java", "server/src/main/com/xgen/cloud/payments/standalone/partners/aws/_private/model/AwsSnsSubscriptionMessage.java", "server/src/main/com/xgen/cloud/payments/standalone/partners/aws/_private/model/AwsSnsSubscriptionNotification.java", "server/src/main/com/xgen/cloud/payments/standalone/partners/common/_public/constant/PartnerCustomerErrorCode.java", "server/src/main/com/xgen/cloud/payments/standalone/partners/common/_public/view/response/PartnerApiError.java", "server/src/main/com/xgen/cloud/performanceadvisor/_public/jobs/IndexStatsJob.java", "server/src/main/com/xgen/cloud/search/decoupled/blobstore/_public/svc/BlobstoreAwsParamsSvc.java", "server/src/main/com/xgen/cloud/search/decoupled/healthcheck/_public/check/AWSInstanceStateHealthCheck.java", "server/src/main/com/xgen/cloud/search/decoupled/healthcheck/_public/check/AWSInstanceStatusesHealthCheck.java", "server/src/main/com/xgen/cloud/services/apiregistry/_private/svc/AwsApiSvc.java", "server/src/main/com/xgen/cloud/services/apiregistry/_private/svc/AwsOperation.java", "server/src/main/com/xgen/cloud/services/apiregistry/_private/svc/impl/BlobStorage.java", "server/src/main/com/xgen/cloud/services/communication/_private/providers/email/sending/awsv2/AwsSesV2ClientProvider.java", "server/src/main/com/xgen/cloud/services/config/runtime/cron/IngestFeatureFlagsRunnable.java", "server/src/main/com/xgen/cloud/services/metrics/_private/svc/apikey/AwsKMSApiSvc.java", "server/src/main/com/xgen/cloud/services/metrics/_private/svc/apikey/AwsKmsEcdsaAlgorithm.java", "server/src/main/com/xgen/cloud/services/metrics/_private/svc/streaming/KinesisStreamProcessor.java", "server/src/main/com/xgen/cloud/services/metrics/_private/svc/streaming/MetricsStreamingPublisher.java", "server/src/main/com/xgen/cloud/services/metrics/_private/svc/streaming/MetricsStreamingSvc.java", "server/src/main/com/xgen/cloud/services/telemetryconsumers/_private/consumers/ExampleCustomConsumer.java", "server/src/main/com/xgen/cloud/services/telemetryconsumers/_private/consumers/integrations/DataWarehouseConsumer.java", "server/src/main/com/xgen/cloud/services/telemetryconsumers/_private/consumers/integrations/DatadogConsumer.java", "server/src/main/com/xgen/cloud/services/telemetryconsumers/_private/consumers/kcl/KinesisConsumer.java", "server/src/main/com/xgen/cloud/services/telemetryconsumers/_private/consumers/kcl/StreamRecordProcessor.java", "server/src/main/com/xgen/cloud/streams/_public/svc/VPCPeeringConnectionSvc.java", "server/src/main/com/xgen/module/usagedata/dao/UsageDataPayloadS3Store.java", "server/src/main/com/xgen/svc/atm/dao/LogCollectionS3Dao.java", "server/src/main/com/xgen/svc/mms/api/view/ApiClusterDescriptionView.java", "server/src/main/com/xgen/svc/mms/api/view/atlas/ApiAtlasLegacyClusterDescriptionView.java", "server/src/main/com/xgen/svc/mms/misc/AWSAccountAvailabilityZoneTool.java", "server/src/main/com/xgen/svc/mms/misc/AWSAccountCreationTool.java", "server/src/main/com/xgen/svc/mms/misc/AWSCapacityReservationTool.java", "server/src/main/com/xgen/svc/mms/misc/AWSDNSReaper.java", "server/src/main/com/xgen/svc/mms/misc/AWSHostnameBackfillTool.java", "server/src/main/com/xgen/svc/mms/misc/AWSProxyProtocolIngressInspectionTool.java", "server/src/main/com/xgen/svc/mms/misc/AWSReaper.java", "server/src/main/com/xgen/svc/mms/misc/AWSServerlessDeploymentUpdateForLockdownTool.java", "server/src/main/com/xgen/svc/mms/misc/AzureReaper.java", "server/src/main/com/xgen/svc/mms/misc/BackfillGCPHostedZoneDNSRecordsForGovAWSClusters.java", "server/src/main/com/xgen/svc/mms/misc/CloudSecretManager.java", "server/src/main/com/xgen/svc/mms/misc/CreateDNSRecordsPerPrivateLinkConnectionTool.java", "server/src/main/com/xgen/svc/mms/misc/DNSChangeLoadTestTool.java", "server/src/main/com/xgen/svc/mms/misc/ForcePauseM0ClustersTool.java", "server/src/main/com/xgen/svc/mms/misc/GCPReaper.java", "server/src/main/com/xgen/svc/mms/misc/GP3CheckTool.java", "server/src/main/com/xgen/svc/mms/misc/GenerateMockOADataExpirationS3InventoryTool.java", "server/src/main/com/xgen/svc/mms/misc/GenerateOnlineArchiveRehydrateInventoryFilesTool.java", "server/src/main/com/xgen/svc/mms/misc/LockUserAccountsTool.java", "server/src/main/com/xgen/svc/mms/misc/OnlineArchiveDLZManifestFileBackfillTool.java", "server/src/main/com/xgen/svc/mms/misc/OnlineArchiveDeleteMigratedV1DataTool.java", "server/src/main/com/xgen/svc/mms/misc/OnlineArchiveDeleteUnusedInvalidUUIDJobManifestsTool.java", "server/src/main/com/xgen/svc/mms/misc/OnlineArchiveDetectCorruptedFilesTool.java", "server/src/main/com/xgen/svc/mms/misc/OnlineArchiveLeakedDLZFilesForDeletedV3ArchivesTool.java", "server/src/main/com/xgen/svc/mms/misc/OnlineArchiveLeakedItemDeleterAWSTool.java", "server/src/main/com/xgen/svc/mms/misc/OnlineArchiveRunGetDocIdTool.java", "server/src/main/com/xgen/svc/mms/misc/OnlineArchiveV1FilesForMigratedArchivesCopyAndDeletionTool.java", "server/src/main/com/xgen/svc/mms/misc/OnlineArchiveXCountBackfillTool.java", "server/src/main/com/xgen/svc/mms/misc/SeedLocalEnvData.java", "server/src/main/com/xgen/svc/mms/misc/SetRegionContainerNeedsSubnetsUpdatedTool.java", "server/src/main/com/xgen/svc/mms/misc/SuspendOrganizationsTool.java", "server/src/main/com/xgen/svc/mms/misc/SynchronizePrivateDNSRecordsPerInstanceHardwareForGravitonRolloutTool.java", "server/src/main/com/xgen/svc/mms/misc/TenantGCPHostnameMappingTool.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/DNSRecordResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/DanglingVPCEndpointResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/EC2InstanceBulkResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/EC2InstanceResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/ElasticIpResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/IngressSecurityGroupRuleIPResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/InternetGatewayResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/PeeringConnectionResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/ResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/ResourceReaperBase.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/ResourceTag.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/RouteTableAssociationResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/RouteTableResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/SecurityGroupResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/SnapshotResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/SubnetResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/VPCEndpointBulkResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/VPCEndpointResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/VPCEndpointServiceResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/VolumeResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/reaper/aws/VpcResourceReaper.java", "server/src/main/com/xgen/svc/mms/misc/util/AWSDNSReaperUtil.java", "server/src/main/com/xgen/svc/mms/model/billing/NDSBackupSnapshotDownloadVMUsage.java", "server/src/main/com/xgen/svc/mms/svc/ChartsSvc.java", "server/src/main/com/xgen/svc/mms/svc/atlasbilling/NDSMeterUsageSubmissionSvc.java", "server/src/main/com/xgen/svc/mms/svc/event/SnsChartsFreemiumEventBroadcaster.java", "server/src/main/com/xgen/svc/mms/svc/event/SnsEventBroadcaster.java", "server/src/main/com/xgen/svc/mms/svc/ping/kinesis/KinesisBatchedStreamEmitter.java", "server/src/main/com/xgen/svc/mms/util/res/InternalTestUtilsResource.java", "server/src/main/com/xgen/svc/nds/aws/dns/DNSChangeQueueDao.java", "server/src/main/com/xgen/svc/nds/aws/dns/DNSChangeSvc.java", "server/src/main/com/xgen/svc/nds/aws/healthCheck/AWSInstanceStateHealthCheck.java", "server/src/main/com/xgen/svc/nds/aws/healthCheck/AWSInstanceStatusesHealthCheck.java", "server/src/main/com/xgen/svc/nds/aws/healthCheck/AWSVolumeHealthCheck.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSAcceptEndpointConnectionStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSAddTargetToExistingTargetGroupStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSAttachEniStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSAttachVolumeStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSCreateInstanceAbstractStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSCreateInternetGatewayStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSCreateSlsEniStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSCreateSubnetsStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSCreateVolumeStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSCreateVpcPeeringConnectionStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSCreateVpcStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSDeleteEndpointServiceStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSDeleteInstanceStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSDeleteInternetGatewayStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSDeleteVpcPeeringConnectionStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSDeregisterEnvoyInstanceWithLoadBalancerStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSDestroyEnvoyInstanceMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSDestroyMachineMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSDestroyNetworkLoadBalancerStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSDetachVolumeStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSEnsureSecurityGroupAbstractStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSEnsureSecurityGroupStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSEnsureServerlessNetworkSecurityGroupStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSKMSCreateInterfaceVPCEndpointStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSModifyInstanceStateStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSModifyVolumeStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSModifyVolumeStorageSettingsStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSOrphanVolumeStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSProvisionConnectionRuleStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSProvisionEnvoyInstanceMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSProvisionMachineMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSProvisionNetworkLoadBalancerStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSReserveInstanceCapacityStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSS3CreateInterfaceVPCEndpointStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSetupEndpointConnectionStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSetupInstanceTargetGroupBaseStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSetupInstanceTargetGroupForProxyProtocolStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSetupInstanceTargetGroupStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSetupMultiInstanceTargetGroupStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSetupPeeringConnectionStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSnapshotAndOrphanVolumeStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSStartInstanceStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSStopInstanceStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSwapMachineMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSyncDataLakePrivateEndpointHostnameMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSyncPauseStateMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSyncPrivateLinkConnectionMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSSyncVpcPeeringConnectionMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSTerminateEndpointConnectionsStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSUpdateEndpointConnectionStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSUpdateInstanceSizeStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AWSUpdateInstanceStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/AbstractAWSCreateInterfaceVPCEndpointStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/admincapacity/AWSAcceptAssistedCapacityReservationStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/admincapacity/AWSCheckCapacityStepHelper.java", "server/src/main/com/xgen/svc/nds/aws/planner/admincapacity/AWSCheckInstanceCapacityStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/privatenetworking/AWSTenantConsumerAcceptPrivateEndpointMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/privatenetworking/AWSTenantConsumerAcceptPrivateEndpointStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/privatenetworking/AWSTenantConsumerRejectPrivateEndpointMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/privatenetworking/AWSTenantConsumerRejectPrivateEndpointStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/privatenetworking/AWSTenantProducerCreatePrivateEndpointServiceStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/privatenetworking/AWSTenantProducerDeletePrivateEndpointServiceStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/snapshot/AWSCleanUpDirectAttachRestoreMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/snapshot/AWSCreateEbsSnapshotStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/snapshot/AWSCreateVolumeFromSnapshotStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/snapshot/AWSDestroyRestoreMachineMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/snapshot/AWSDirectAttachRestoreMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/snapshot/AWSOptimizedDirectAttachRestoreMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/snapshot/AWSProvisionRestoreMachineMove.java", "server/src/main/com/xgen/svc/nds/aws/planner/snapshot/AWSWaitEbsSnapshotCompleteStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/snapshot/AWSWaitInstanceTargetGroupHealthyStep.java", "server/src/main/com/xgen/svc/nds/aws/planner/snapshot/AwsCopySnapshotStep.java", "server/src/main/com/xgen/svc/nds/aws/svc/AWSCapacityReservationProcessorSvc.java", "server/src/main/com/xgen/svc/nds/aws/svc/AWSLeakedItemProcessorSvc.java", "server/src/main/com/xgen/svc/nds/aws/svc/AWSNetworkLoadBalancerProcessorSvc.java", "server/src/main/com/xgen/svc/nds/aws/svc/AWSOplogPurgeSvc.java", "server/src/main/com/xgen/svc/nds/aws/svc/AWSOrphanedItemSvc.java", "server/src/main/com/xgen/svc/nds/aws/svc/AWSSnapshotProcessorSvc.java", "server/src/main/com/xgen/svc/nds/aws/svc/EBSVolumeProcessorSvc.java", "server/src/main/com/xgen/svc/nds/aws/svc/EC2InstanceProcessorSvc.java", "server/src/main/com/xgen/svc/nds/aws/svc/NDSAWSLogDownloadSvc.java", "server/src/main/com/xgen/svc/nds/aws/svc/VpcProcessorSvc.java", "server/src/main/com/xgen/svc/nds/aws/svc/admincapacity/AWSCheckCapacityRequestSvc.java", "server/src/main/com/xgen/svc/nds/aws/util/AWSPrivateNetworkingUtils.java", "server/src/main/com/xgen/svc/nds/aws/util/DNSInvalidBatchExceptionMessageParser.java", "server/src/main/com/xgen/svc/nds/azure/planner/AzureDestroyEnvoyInstanceMove.java", "server/src/main/com/xgen/svc/nds/azure/planner/AzureDestroyMachineMove.java", "server/src/main/com/xgen/svc/nds/azure/planner/AzureProvisionEnvoyInstanceMove.java", "server/src/main/com/xgen/svc/nds/azure/planner/AzureProvisionMachineMove.java", "server/src/main/com/xgen/svc/nds/azure/planner/AzureSetProjectLegacyHostnamesToPublicIpsMove.java", "server/src/main/com/xgen/svc/nds/azure/planner/AzureSwapMachineMove.java", "server/src/main/com/xgen/svc/nds/azure/planner/AzureSyncDataLakePrivateEndpointHostnameMove.java", "server/src/main/com/xgen/svc/nds/azure/planner/AzureSyncPrivateLinkConnectionMove.java", "server/src/main/com/xgen/svc/nds/azure/planner/privateNetworking/AzureTenantConsumerAcceptPrivateEndpointMove.java", "server/src/main/com/xgen/svc/nds/azure/planner/privateNetworking/AzureTenantConsumerRejectPrivateEndpointMove.java", "server/src/main/com/xgen/svc/nds/azure/planner/snapshot/AzureDestroyRestoreMachineMove.java", "server/src/main/com/xgen/svc/nds/azure/planner/snapshot/AzureProvisionRestoreMachineMove.java", "server/src/main/com/xgen/svc/nds/cloudproviderobserver/TestAWSDNSObserver.java", "server/src/main/com/xgen/svc/nds/cloudproviderobserver/TestCloudProviderResourceObserver.java", "server/src/main/com/xgen/svc/nds/datavalidation/planner/AWSDataValidationMove.java", "server/src/main/com/xgen/svc/nds/datavalidation/planner/AzureDataValidationMove.java", "server/src/main/com/xgen/svc/nds/datavalidation/planner/GCPDataValidationMove.java", "server/src/main/com/xgen/svc/nds/exmaintenance/maintenance/AWSGP3RootVolumeMaintenance.java", "server/src/main/com/xgen/svc/nds/exmaintenance/maintenance/AWSIO2DataVolumeMaintenance.java", "server/src/main/com/xgen/svc/nds/flex/planner/FlexProcessProxyConfigPerClusterMove.java", "server/src/main/com/xgen/svc/nds/gcp/planner/GCPDestroyEnvoyInstanceMove.java", "server/src/main/com/xgen/svc/nds/gcp/planner/GCPDestroyMachineMove.java", "server/src/main/com/xgen/svc/nds/gcp/planner/GCPProvisionEnvoyInstanceMove.java", "server/src/main/com/xgen/svc/nds/gcp/planner/GCPProvisionMachineMove.java", "server/src/main/com/xgen/svc/nds/gcp/planner/GCPSetProjectLegacyHostnamesToPublicIpsMove.java", "server/src/main/com/xgen/svc/nds/gcp/planner/GCPSwapMachineMove.java", "server/src/main/com/xgen/svc/nds/gcp/planner/networking/GCPSyncPrivateServiceConnectRegionGroupMove.java", "server/src/main/com/xgen/svc/nds/gcp/planner/snapshot/GCPDestroyRestoreMachineMove.java", "server/src/main/com/xgen/svc/nds/gcp/planner/snapshot/GCPProvisionRestoreMachineMove.java", "server/src/main/com/xgen/svc/nds/model/ui/ClusterDescriptionView.java", "server/src/main/com/xgen/svc/nds/model/ui/ClusterDescriptionViewUtils.java", "server/src/main/com/xgen/svc/nds/model/ui/HardwareSpecView.java", "server/src/main/com/xgen/svc/nds/model/ui/RegionConfigView.java", "server/src/main/com/xgen/svc/nds/model/ui/VolumeTypeView.java", "server/src/main/com/xgen/svc/nds/onlinearchive/restorationtool/jobs/OnlineArchiveRestoreAWSJobHandler.java", "server/src/main/com/xgen/svc/nds/onlinearchive/restorationtool/planner/ScanDLZJobsStep.java", "server/src/main/com/xgen/svc/nds/onlinearchive/v3migration/jobs/CutoverRollbackJobHandler.java", "server/src/main/com/xgen/svc/nds/onlinearchive/v3migration/planner/BackfillExistingDataStep.java", "server/src/main/com/xgen/svc/nds/onlinearchive/v3migration/svc/OnlineArchiveV3MigrationSvc.java", "server/src/main/com/xgen/svc/nds/onlinearchive/v3migration/util/ManifestUtil.java", "server/src/main/com/xgen/svc/nds/planner/AbandonPlanForPendingDiskSizeUpscaleStep.java", "server/src/main/com/xgen/svc/nds/planner/BackupSnapshotUtils.java", "server/src/main/com/xgen/svc/nds/planner/CpsBackupRestoreUtils.java", "server/src/main/com/xgen/svc/nds/planner/EnsureConnectivityForTopologyChangeMove.java", "server/src/main/com/xgen/svc/nds/planner/IngestionPipelineAssembleAndWriteManifestsStep.java", "server/src/main/com/xgen/svc/nds/planner/IngestionPipelineCleanUpDLZObjectsStep.java", "server/src/main/com/xgen/svc/nds/planner/SyncBiConnectorSettingsMove.java", "server/src/main/com/xgen/svc/nds/planner/SyncDNSRecordStep.java", "server/src/main/com/xgen/svc/nds/planner/UploadSnapshotExportCompleteFileMove.java", "server/src/main/com/xgen/svc/nds/planner/WaitForSystemClusterCreationStep.java", "server/src/main/com/xgen/svc/nds/planner/snapshot/AwsReplSetSnapshotter.java", "server/src/main/com/xgen/svc/nds/res/NDSAdminResource.java", "server/src/main/com/xgen/svc/nds/sampleDatasetLoad/planner/ImportSampleDatasetStep.java", "server/src/main/com/xgen/svc/nds/sampleDatasetLoad/planner/ValidateNamespacesStep.java", "server/src/main/com/xgen/svc/nds/security/planner/ACMEPreAuthSteps.java", "server/src/main/com/xgen/svc/nds/security/planner/ACMEProcessDNSChallengeStep.java", "server/src/main/com/xgen/svc/nds/serverless/planner/ServerlessDestroyPreProvisionedFastRecordResourcesMove.java", "server/src/main/com/xgen/svc/nds/serverless/planner/ServerlessPreProvisionFastRecordResourcesMove.java", "server/src/main/com/xgen/svc/nds/serverless/planner/ServerlessProcessProxyConfigPerClusterMove.java", "server/src/main/com/xgen/svc/nds/serverless/svc/ServerlessLoadBalancerMetricsSvc.java", "server/src/main/com/xgen/svc/nds/serverless/svc/healthcheck/AWSEnvoyHealthCheck.java", "server/src/main/com/xgen/svc/nds/svc/CloudChefConfSvc.java", "server/src/main/com/xgen/svc/nds/svc/DataSetCredentialsSvc.java", "server/src/main/com/xgen/svc/nds/svc/DeleteReapedClusterSvc.java", "server/src/main/com/xgen/svc/nds/svc/NDSAdminSvc.java", "server/src/main/com/xgen/svc/nds/svc/NDSCloudProviderAccessSvc.java", "server/src/main/com/xgen/svc/nds/svc/NDSClusterConversionSvc.java", "server/src/main/com/xgen/svc/nds/svc/NDSDataLakeBaseSvc.java", "server/src/main/com/xgen/svc/nds/svc/NDSDataLakePrivateSvc.java", "server/src/main/com/xgen/svc/nds/svc/NDSDataLakePublicSvc.java", "server/src/main/com/xgen/svc/nds/svc/NDSEncryptionAtRestSvc.java", "server/src/main/com/xgen/svc/nds/svc/NDSPeerStatusRefreshSvc.java", "server/src/main/com/xgen/svc/nds/svc/NDSProxySvc.java", "server/src/main/com/xgen/svc/nds/svc/NDSServerAccessSvc.java", "server/src/main/com/xgen/svc/nds/svc/NDSTenantSnapshotRestoreSvc.java", "server/src/main/com/xgen/svc/nds/svc/PushBasedLogExportSvc.java", "server/src/main/com/xgen/svc/nds/svc/StreamsSvc.java", "server/src/main/com/xgen/svc/nds/svc/SystemClusterExportJobSvc.java", "server/src/main/com/xgen/svc/nds/svc/TemporaryCredentialSvc.java", "server/src/main/com/xgen/svc/nds/svc/cps/CpsCollectionMetadataBackupSvc.java", "server/src/main/com/xgen/svc/nds/svc/cps/CpsConfSvc.java", "server/src/main/com/xgen/svc/nds/svc/cps/CpsEncryptionAtRestSvc.java", "server/src/main/com/xgen/svc/nds/svc/cps/CpsExportSvc.java", "server/src/main/com/xgen/svc/nds/svc/cps/CpsResurrectSvc.java", "server/src/main/com/xgen/svc/nds/svc/cps/CpsSvc.java", "server/src/main/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveBackfillMissingDLSJobHandler.java", "server/src/main/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveCleanupSvc.java", "server/src/main/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveDLZLeakedItemSvc.java", "server/src/main/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveExpirationArchiveChunkAWSJobHandler.java", "server/src/main/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveExpirationJobIdChunkFileDeletionAWSJobHandler.java", "server/src/main/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveExpirationS3InventoryConfigSvc.java", "server/src/main/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveExpiredFilesDeleterSvc.java", "server/src/main/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveInventoryScannerSvc.java", "server/src/main/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveSvc.java", "server/src/main/com/xgen/svc/nds/svc/project/NDSGroupSvc.java", "server/src/main/com/xgen/svc/nds/svc/project/NDSUISvc.java", "server/src/main/com/xgen/svc/nds/tenant/planner/FastTenantProcessProxyConfigPerClusterMove.java", "server/src/main/com/xgen/svc/nds/tenant/planner/TenantDestroyPreProvisionedFastRecordResourcesMove.java", "server/src/main/com/xgen/svc/nds/tenant/planner/TenantProcessProxyConfigPerClusterMove.java", "server/src/main/com/xgen/svc/nds/tenantUpgrade/planner/DoServerlessDowngradeToFreeMove.java", "server/src/main/com/xgen/svc/nds/tenantUpgrade/planner/DoServerlessUpgradeToDedicatedMove.java", "server/src/main/com/xgen/svc/nds/tenantUpgrade/planner/RestoreSnapshotStep.java", "server/src/main/com/xgen/svc/nds/util/ClusterValidationUtil.java", "server/src/main/com/xgen/svc/nds/util/IOUtil.java", "server/src/main/com/xgen/svc/nds/util/PrivateLinkDNSUtil.java", "server/src/main/com/xgen/svc/nds/util/TenantPrivateEndpointDNSUtil.java", "server/src/main/com/xgen/svc/search/planner/step/aws/AWSSelectSearchSubnetStep.java", "server/src/main/com/xgen/svc/streams/planner/StreamsUtil.java", "server/src/main/com/xgen/svc/streams/planner/move/AWSDeleteVPCProxyRoute53Move.java", "server/src/main/com/xgen/svc/streams/planner/move/AWSDisassociateVPCWithHostedZoneMove.java", "server/src/main/com/xgen/svc/streams/planner/move/AWSProvisionVPCInterfaceEndpointMove.java", "server/src/main/com/xgen/svc/streams/planner/move/AWSProvisionVPCProxyRoute53Move.java", "server/src/main/com/xgen/svc/streams/planner/move/AWSVPCPeeringConnScanMove.java", "server/src/main/com/xgen/svc/streams/planner/move/AWSVPCPeeringConnUpdateMove.java", "server/src/main/com/xgen/svc/streams/planner/move/azure/AzureProvisionVPCInterfaceEndpointMove.java", "server/src/main/com/xgen/svc/streams/planner/move/gcp/GCPProvisionVPCProxyRoute53Move.java", "server/src/main/com/xgen/svc/streams/planner/step/AWSAcceptAssociateVPCWithHostedZoneStep.java", "server/src/main/com/xgen/svc/streams/planner/step/AWSCreateInterfaceEndpointStep.java", "server/src/main/com/xgen/svc/streams/planner/step/AWSCreateMSKManagedConnectionStep.java", "server/src/main/com/xgen/svc/streams/planner/step/AWSDeleteInterfaceEndpointStep.java", "server/src/main/com/xgen/svc/streams/planner/step/AWSDisassociateVPCWithHostedZoneStep.java", "server/src/main/com/xgen/svc/streams/planner/step/AWSEnsureKafkaNetworkSecurityGroupStep.java", "server/src/main/com/xgen/svc/streams/planner/step/AWSRequestVPCAssociationWithHostedZoneStep.java", "server/src/test/com/xgen/cloud/activity/dataexport/ActivityDataExportIntTests.java", "server/src/test/com/xgen/cloud/dataexport/_public/svc/DataExportSvcCriticalAnalyticsTests.java", "server/src/test/com/xgen/cloud/dataexport/_public/svc/DataExportSvcIntTests.java", "server/src/test/com/xgen/cloud/nds/aws/_public/model/ChunkedS3InputStreamIntTests.java", "server/src/test/com/xgen/cloud/nds/aws/_public/svc/AWSApiSvcIntTests.java", "server/src/test/com/xgen/cloud/nds/cloudprovider/runtime/res/api_2023_01_01/ApiAtlasCloudProviderAccessResourceIntTests.java", "server/src/test/com/xgen/cloud/nds/cloudprovider/runtime/res/api_2023_01_01/ApiAtlasEncryptionAtRestResourceIntTests.java", "server/src/test/com/xgen/cloud/nds/cloudprovider/runtime/res/api_2023_01_01/ApiAtlasTenantBackupResourceIntTests.java", "server/src/test/com/xgen/cloud/nds/datalake/runtime/res/api_2023_01_01/ApiAtlasDataFederationResourceIntTests.java", "server/src/test/com/xgen/cloud/nds/datalake/runtime/res/api_2023_01_01/ApiAtlasDataLakeResourceIntTests.java", "server/src/test/com/xgen/cloud/nds/flex/runtime/res/api_2024_11_13/ApiAtlasFlexBackupResourceIntTests.java", "server/src/test/com/xgen/cloud/nds/ifr/_public/svc/IFRStateSyncCronSvcIntTests.java", "server/src/test/com/xgen/cloud/nds/ifr/_public/svc/IFRSvcIntTests.java", "server/src/test/com/xgen/cloud/nds/project/runtime/res/api_2023_01_01/ApiAtlasLegacyClusterDescriptionResourceIntTests.java", "server/src/test/com/xgen/cloud/nds/project/runtime/res/api_2023_01_01/ApiAtlasPushBasedLogExportResourceIntTests.java", "server/src/test/com/xgen/cloud/nds/project/runtime/res/api_2024_08_05/ApiAtlasClusterDescriptionResource20240805IntTests.java", "server/src/test/com/xgen/cloud/nds/project/runtime/res/api_2024_10_23/ApiAtlasClusterDescriptionResource20241023IntTests.java", "server/src/test/com/xgen/cloud/nds/temporarydownloadlinks/runtime/res/TemporaryDownloadLinkResourceIntTests.java", "server/src/test/com/xgen/cloud/partners/aws/_private/svc/AwsSnsNotificationSvcIntTests.java", "server/src/test/com/xgen/cloud/partners/registration/_private/svc/AwsRegistrationSvcIntTests.java", "server/src/test/com/xgen/cloud/partners/usage/report/_private/svc/AgreementSearchResultAdminSvcImplIntTests.java", "server/src/test/com/xgen/cloud/partners/usage/report/_private/svc/AwsUsageReportSvcImplIntTests.java", "server/src/test/com/xgen/cloud/partners/usage/report/_private/svc/ReportingFailuresAdminSvcImplIntTests.java", "server/src/test/com/xgen/cloud/search/decoupled/blobstore/_public/svc/BlobstoreParamsSvcAwsExternalIntTests.java", "server/src/test/com/xgen/cloud/search/decoupled/blobstore/_public/svc/BlobstoreParamsSvcAwsIntTests.java", "server/src/test/com/xgen/cloud/streams/runtime/res/api_preview/ApiStreamsAcceptTgwResourceShareResourceIntTests.java", "server/src/test/com/xgen/cloud/streams/runtime/res/api_preview/ApiStreamsRejectTransitGatewayInvitationResourceIntTests.java", "server/src/test/com/xgen/cloud/streams/runtime/res/api_preview/ApiStreamsTransitGatewayAttachmentsResourceIntTests.java", "server/src/test/com/xgen/svc/brs/dao/S3OplogStoreDaoIntTests.java", "server/src/test/com/xgen/svc/brs/san/S3GroomIntTests.java", "server/src/test/com/xgen/svc/brs/san/s3/S3ClientWrapperIntTests.java", "server/src/test/com/xgen/svc/brs/store/oplog/S3OplogStoreIntTests.java", "server/src/test/com/xgen/svc/brs/svc/S3ValidatorIntTests.java", "server/src/test/com/xgen/svc/mms/api/res/ApiLogCollectionJobsResourceIntTests.java", "server/src/test/com/xgen/svc/mms/api/res/_private/ApiChartsResourceIntTests.java", "server/src/test/com/xgen/svc/mms/api/res/atlas/ApiAtlasCloudProviderAccessResourceIntTests.java", "server/src/test/com/xgen/svc/mms/api/res/atlas/ApiAtlasDataFederationResourceIntTests.java", "server/src/test/com/xgen/svc/mms/api/res/atlas/ApiAtlasDataLakeResourceIntTests.java", "server/src/test/com/xgen/svc/mms/api/res/atlas/ApiAtlasEncryptionAtRestResourceIntTests.java", "server/src/test/com/xgen/svc/mms/api/res/atlas/ApiAtlasLegacyClusterDescriptionResourceIntTests.java", "server/src/test/com/xgen/svc/mms/api/res/atlas/ApiAtlasTenantBackupResourceIntTests.java", "server/src/test/com/xgen/svc/mms/api/res/atlas/_private/ApiPrivateAtlasClusterDescriptionResourceIntTests.java", "server/src/test/com/xgen/svc/mms/api/res/atlas/_private/ApiPrivateAtlasRealmResourceIntTests.java", "server/src/test/com/xgen/svc/mms/api/res/atlas/_private/dataLake/ApiPrivateAtlasDataLakeResourceIntTests.java", "server/src/test/com/xgen/svc/mms/misc/AWSAccountAvailabilityZoneToolIntTests.java", "server/src/test/com/xgen/svc/mms/misc/AWSAccountCreationToolIntTests.java", "server/src/test/com/xgen/svc/mms/misc/AWSCapacityReservationToolIntTests.java", "server/src/test/com/xgen/svc/mms/misc/AWSHostnameBackfillToolIntTests.java", "server/src/test/com/xgen/svc/mms/misc/AWSServerlessDeploymentUpdateForLockdownToolIntTests.java", "server/src/test/com/xgen/svc/mms/misc/BackfillGCPHostedZoneDNSRecordsForGovAWSClustersIntTests.java", "server/src/test/com/xgen/svc/mms/misc/OnlineArchiveDLZManifestFileBackfillToolExternalIntTests.java", "server/src/test/com/xgen/svc/mms/misc/OnlineArchiveDeleteMigratedV1DataToolIntTests.java", "server/src/test/com/xgen/svc/mms/misc/OnlineArchiveDeleteUnusedInvalidUUIDJobManifestsToolExternalIntTests.java", "server/src/test/com/xgen/svc/mms/misc/OnlineArchiveLeakedDLZFilesForDeletedV3ArchivesToolExternalIntTests.java", "server/src/test/com/xgen/svc/mms/misc/OnlineArchiveLeakedItemDeleterAWSToolExternalIntTests.java", "server/src/test/com/xgen/svc/mms/svc/atlasbilling/NDSMeterUsageSubmissionSvcIntTests.java", "server/src/test/com/xgen/svc/mms/svc/atlasbilling/SubscriptionUsageTestUtil.java", "server/src/test/com/xgen/svc/mms/svc/billing/AwsApiSvcIntTests.java", "server/src/test/com/xgen/svc/mms/svc/billing/SubscriptionUsageSvcIntTests.java", "server/src/test/com/xgen/svc/mms/svc/billing/pricing/PremiumUpchargeAbstractPricingStrategyFixtures.java", "server/src/test/com/xgen/svc/mms/svc/discovery/ServerlessXDSServerIntTests.java", "server/src/test/com/xgen/svc/mms/util/AwsUtilsIntTests.java", "server/src/test/com/xgen/svc/mms/util/DiagnosticArchiveTestHelpers.java", "server/src/test/com/xgen/svc/nds/NDSLocalSecretsManager.java", "server/src/test/com/xgen/svc/nds/aws/AWSExternalIntTest.java", "server/src/test/com/xgen/svc/nds/aws/dao/AWSInstanceHardwareDaoIntTests.java", "server/src/test/com/xgen/svc/nds/aws/dns/DNSChangeQueueDaoIntTests.java", "server/src/test/com/xgen/svc/nds/aws/dns/DNSChangeSvcIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSAdminRestartServerMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSAdminUpdateInstanceSizeMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSAttachElasticIPStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSAttachEniStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSCreateInstanceStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSCreateInternetGatewayStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSCreateOSSwapInstanceStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSCreateSlsEniStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSCreateSubnetsStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSCreateVolumeStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSCreateVpcStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSDeleteInstanceStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSDetachElasticIPStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSEnsureNetworkPermissionsAppliedMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSEnsureSecurityGroupStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSEnsureServerlessNetworkSecurityGroupStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSInstancePowerCycleMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSModifyDiskMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSProvisionContainerMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSProvisionMachineMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSReplaceDiskMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSReserveInstanceCapacityStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSSelectSubnetStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSStopStartInstanceMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSSyncBiConnectorSettingsMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSSyncDataLakePrivateEndpointHostnameMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSSyncVpcPeeringConnectionMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSUpdateContainerSubnetsMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/AWSUpdateInstanceSizeMoveIntTests.java", "server/src/test/com/xgen/svc/nds/aws/planner/BaseAWSIpv6NetworkingIntTest.java", "server/src/test/com/xgen/svc/nds/aws/planner/snapshot/AWSCreateEbsSnapshotMoveBaseIntTest.java", "server/src/test/com/xgen/svc/nds/aws/planner/snapshot/AWSCreateVolumeFromSnapshotStepIntTests.java", "server/src/test/com/xgen/svc/nds/aws/svc/AWSAZStatusCheckSvcIntTests.java", "server/src/test/com/xgen/svc/nds/aws/svc/AWSOrphanedItemSvcIntTests.java", "server/src/test/com/xgen/svc/nds/aws/svc/NDSAWSLogDownloadSvcIntTests.java", "server/src/test/com/xgen/svc/nds/aws/svc/admincapacity/AWSCheckCapacityRequestSvcIntTests.java", "server/src/test/com/xgen/svc/nds/azure/AzureExternalIntTest.java", "server/src/test/com/xgen/svc/nds/azure/planner/AzureSyncDataLakePrivateEndpointHostnameMoveIntTests.java", "server/src/test/com/xgen/svc/nds/dao/BackupSnapshotDaoIntTests.java", "server/src/test/com/xgen/svc/nds/exmaintenance/maintenance/AWSGP3RootVolumeMaintenanceIntTests.java", "server/src/test/com/xgen/svc/nds/exmaintenance/maintenance/AWSIO2DataVolumeMaintenanceIntTests.java", "server/src/test/com/xgen/svc/nds/exmaintenance/maintenance/AzureOSSwapMaintenanceIntTests.java", "server/src/test/com/xgen/svc/nds/exmaintenance/maintenance/ClusterDescriptionRegionConfigsConsolidationMaintenanceIntTests.java", "server/src/test/com/xgen/svc/nds/exmaintenance/maintenance/CorrectClusterAsymmetricNodesMaintenanceIntTests.java", "server/src/test/com/xgen/svc/nds/gcp/GCPExternalIntTest.java", "server/src/test/com/xgen/svc/nds/gcp/planner/GCPDestroyMachineMoveIntTests.java", "server/src/test/com/xgen/svc/nds/gcp/planner/networking/GCPAttachPublicIPStepIntTests.java", "server/src/test/com/xgen/svc/nds/gcp/planner/networking/GCPDetachPublicIPStepIntTests.java", "server/src/test/com/xgen/svc/nds/onlinearchive/restorationtool/jobs/OnlineArchiveRestoreAWSJobHandlerExternalIntTests.java", "server/src/test/com/xgen/svc/nds/onlinearchive/restorationtool/planner/ScanDLZJobsStepIntTests.java", "server/src/test/com/xgen/svc/nds/onlinearchive/v3migration/jobs/CutoverRollbackJobHandlerExternalIntTests.java", "server/src/test/com/xgen/svc/nds/onlinearchive/v3migration/planner/BackfillExistingDataStepIntTests.java", "server/src/test/com/xgen/svc/nds/planner/BasePlannerIntTest.java", "server/src/test/com/xgen/svc/nds/planner/SyncDNSRecordStepIntTests.java", "server/src/test/com/xgen/svc/nds/res/ClusterDescriptionResourceIntTests.java", "server/src/test/com/xgen/svc/nds/res/CpsConfResourceIntTests.java", "server/src/test/com/xgen/svc/nds/res/CpsResourceIntTests.java", "server/src/test/com/xgen/svc/nds/res/FTSIndexConfigMongotApiResourceBlobstoreExternalIntTests.java", "server/src/test/com/xgen/svc/nds/res/NDSAdminResourceIntTests.java", "server/src/test/com/xgen/svc/nds/res/NDSCloudProviderAccessResourceIntTests.java", "server/src/test/com/xgen/svc/nds/res/NDSDataLakeTenantResourceIntTests.java", "server/src/test/com/xgen/svc/nds/res/NDSGroupCloudProviderAccessResourceIntTests.java", "server/src/test/com/xgen/svc/nds/res/NDSGroupResourceIntTests.java", "server/src/test/com/xgen/svc/nds/res/NDSPushBasedLogExportResourceIntTests.java", "server/src/test/com/xgen/svc/nds/res/OnlineArchiveConfResourceAwsExternalIntTests.java", "server/src/test/com/xgen/svc/nds/security/planner/ACMEProcessDNSChallengeStepIntTests.java", "server/src/test/com/xgen/svc/nds/serverless/svc/EnvoyHealthCheckJobHandlerIntTests.java", "server/src/test/com/xgen/svc/nds/serverless/svc/ServerlessLoadBalancerMetricsSvcIntTests.java", "server/src/test/com/xgen/svc/nds/serverless/util/EnvoyConfigUtilIntTests.java", "server/src/test/com/xgen/svc/nds/svc/FTSIndexConfigMongotApiSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/NDSDataLakeSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/NDSEncryptionAtRestSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/OnlineArchiveExpirationS3InventoryConfigSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/OnlineArchiveExpiredFilesDeleterSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/PushBasedLogExportSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/StreamsSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/TemporaryCredentialSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/cps/CpsMetadataPruningSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/cps/CpsOplogExpirationSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/cps/CpsOplogIngestionSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/cps/CpsPitSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/cps/CpsSnapshotStatusCleanupSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveInventoryScannerSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/planning/NDSPlanningSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/project/NDSClusterSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/project/NDSGroupMaintenanceSvcIntTests.java", "server/src/test/com/xgen/svc/nds/svc/project/NDSGroupSvcIntTests.java", "server/src/test/com/xgen/svc/nds/tenant/planner/FastTenantProcessProxyConfigPerClusterMoveIntTests.java", "server/src/test/com/xgen/svc/nds/tenant/planner/FastTenantProvisionMachineMoveIntTests.java", "server/src/test/com/xgen/svc/nds/tenant/planner/TenantDestroyPreProvisionedFastRecordResourcesMoveIntTests.java", "server/src/test/com/xgen/svc/nds/tenant/planner/TenantProcessProxyConfigPerClusterMoveIntTests.java", "server/src/test/com/xgen/svc/security/svc/ACMEExternalIntTest.java", "server/src/test/com/xgen/svc/streams/planner/AWSProvisionPrivateLinkIntTests.java", "server/src/test/com/xgen/testlib/base/nds/JUnit5NDSBaseTest.java", "server/src/unit/com/xgen/cloud/apiusagedata/_private/svc/KinesisClientBatchDispatcherUnitTests.java", "server/src/unit/com/xgen/cloud/apiusagedata/_private/svc/KinesisClientProducerUnitTests.java", "server/src/unit/com/xgen/cloud/brs/core/_private/dao/S3OplogStoreDaoUnitTests.java", "server/src/unit/com/xgen/cloud/brs/core/_public/san/s3/S3BlockDaoUnitTests.java", "server/src/unit/com/xgen/cloud/common/aws/_public/secret/AwsManagedSecretsUnitTests.java", "server/src/unit/com/xgen/cloud/nds/aws/_public/model/AWSNDSInstanceSizeUnitTests.java", "server/src/unit/com/xgen/cloud/nds/aws/_public/svc/AWSApiSvcUnitTests.java", "server/src/unit/com/xgen/cloud/nds/aws/_public/svc/AWSApiSvcV2FrameworkUnitTests.java", "server/src/unit/com/xgen/cloud/nds/aws/_public/util/AWSApiUtilsUnitTests.java", "server/src/unit/com/xgen/cloud/nds/aws/_public/util/PolicyUtilsUnitTests.java", "server/src/unit/com/xgen/cloud/nds/billing/_private/svc/EstimateSvcImplUnitTests.java", "server/src/unit/com/xgen/cloud/nds/billing/_public/model/SubscriptionUsageUnitTests.java", "server/src/unit/com/xgen/cloud/nds/billing/_public/util/MeterIdMappingUtilUnitTests.java", "server/src/unit/com/xgen/cloud/nds/cloudprovider/_public/model/HardwareSpecUnitTests.java", "server/src/unit/com/xgen/cloud/nds/cloudprovider/_public/model/InstanceHardwareModelTestFactory.java", "server/src/unit/com/xgen/cloud/nds/cloudprovider/_public/model/RegionConfigUnitTests.java", "server/src/unit/com/xgen/cloud/nds/cloudprovider/_public/model/ShardRegionConfigUnitTests.java", "server/src/unit/com/xgen/cloud/nds/dataexfiltrationprevention/_public/svc/AWSSecurityGroupsPerNetworkInterfaceValidationCronSvcUnitTests.java", "server/src/unit/com/xgen/cloud/nds/datavalidation/_public/util/AWSDataValidationInstanceUtilUnitTests.java", "server/src/unit/com/xgen/cloud/nds/deployment/_public/util/AutomationConfigReplicaSetDeploymentBuilderUnitTests.java", "server/src/unit/com/xgen/cloud/nds/deployment/_public/util/ClusterDeploymentProcessUtilUnitTests.java", "server/src/unit/com/xgen/cloud/nds/dns/_public/util/DNSRecordUtilUnitTests.java", "server/src/unit/com/xgen/cloud/nds/hostname/_public/svc/NDSHostnameSvcUnitTests.java", "server/src/unit/com/xgen/cloud/nds/ifr/_public/svc/IFRSvcUnitTests.java", "server/src/unit/com/xgen/cloud/nds/project/_public/model/ReplicationSpecUnitTests.java", "server/src/unit/com/xgen/cloud/nds/project/_public/util/ClusterDescriptionValidationUtilUnitTests.java", "server/src/unit/com/xgen/cloud/nds/temporarydownloadlinks/_public/svc/TemporaryDownloadLinkSvcUnitTests.java", "server/src/unit/com/xgen/cloud/partners/aws/_private/handler/SnsSubscriptionsHandlerUnitTests.java", "server/src/unit/com/xgen/cloud/partners/aws/_private/svc/AwsSnsNotificationSvcUnitTests.java", "server/src/unit/com/xgen/cloud/partners/aws/fixtures/SnsNotificationTestFixtures.java", "server/src/unit/com/xgen/cloud/partners/usage/report/_private/svc/AwsUsageReportSvcImplUnitTests.java", "server/src/unit/com/xgen/cloud/partners/usage/reporting/_private/svc/AwsApiSvcImplUnitTests.java", "server/src/unit/com/xgen/cloud/search/decoupled/blobstore/_public/svc/BlobstoreAwsParamsSvcUnitTests.java", "server/src/unit/com/xgen/cloud/search/decoupled/healthcheck/_public/check/AWSInstanceStateHealthCheckUnitTests.java", "server/src/unit/com/xgen/cloud/search/decoupled/healthcheck/_public/check/AWSInstanceStatusesHealthCheckUnitTests.java", "server/src/unit/com/xgen/cloud/services/apiregistry/_private/svc/impl/BlobStorageUnitTest.java", "server/src/unit/com/xgen/cloud/services/authn/_private/svc/CloudJwtSigningAwsKeySvcUnitTests.java", "server/src/unit/com/xgen/cloud/services/config/runtime/cron/IngestFeatureFlagsRunnableUnitTests.java", "server/src/unit/com/xgen/cloud/services/metrics/_private/svc/streaming/KinesisStreamProcessorUnitTests.java", "server/src/unit/com/xgen/cloud/services/metrics/_private/svc/streaming/MetricsStreamingPublisherUnitTests.java", "server/src/unit/com/xgen/cloud/services/metrics/_private/svc/streaming/MetricsStreamingSvcUnitTests.java", "server/src/unit/com/xgen/devtools/bugchecker/planlogger/testfiles/ResourceEntryLoggingAWSInstanceTest.java", "server/src/unit/com/xgen/devtools/bugchecker/planlogger/testfiles/ResourceEntryLoggingEnumTest.java", "server/src/unit/com/xgen/svc/mms/api/util/ApiAtlasClusterDescriptionUtilUnitTests.java", "server/src/unit/com/xgen/svc/mms/api/view/ApiLogCollectionJobUpdateRequestViewUnitTests.java", "server/src/unit/com/xgen/svc/mms/api/view/ApiLogCollectionJobViewUnitTests.java", "server/src/unit/com/xgen/svc/mms/api/view/atlas/ApiAtlasAWSHardwareSpecViewUnitTests.java", "server/src/unit/com/xgen/svc/mms/api/view/atlas/ApiAtlasDedicatedRegionConfigViewUnitTests.java", "server/src/unit/com/xgen/svc/mms/api/view/atlas/ApiAtlasLegacyClusterDescriptionViewUnitTests.java", "server/src/unit/com/xgen/svc/mms/misc/AWSProxyProtocolIngressInspectionToolUnitTests.java", "server/src/unit/com/xgen/svc/mms/misc/OnlineArchiveLeakedItemDeleterAWSToolUnitTests.java", "server/src/unit/com/xgen/svc/mms/misc/util/AWSDNSReaperUtilUnitTests.java", "server/src/unit/com/xgen/svc/mms/svc/event/SnsChartsFreemiumEventBroadcasterUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/AWSClientsFactoryUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/AwsCredentialsUtilV2UnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/healthCheck/AWSInstanceStateHealthCheckUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/healthCheck/AWSInstanceStatusesHealthCheckUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/model/AWSClusterDescriptionUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/model/AWSHardwareSpecUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/model/AWSInstanceHardwareUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/model/AWSShardedClusterDescriptionUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/model/ui/AWSClusterDescriptionViewUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSAcceptEndpointConnectionStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSAddTargetToExistingTargetGroupStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSCreateElasticIpStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSCreateInstanceStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSCreateInstanceTestHelper.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSCreateInternetGatewayStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSCreateSubnetsStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSCreateVolumeStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSCreateVpcPeeringConnectionStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSCreateVpcStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSDeleteEndpointServiceStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSDeleteInstanceStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSDeleteInterfaceVPCEndpointStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSDeleteInternetGatewayStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSDeleteTargetGroupStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSDeleteVpcPeeringConnectionStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSDeregisterEnvoyInstanceWithLoadBalancerStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSDestroyNetworkLoadBalancerStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSDetachVolumeStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSEnsureSecurityGroupStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSKMSCreateInterfaceVPCEndpointStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSModifyDiskMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSModifyInstanceStateStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSModifyVolumeStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSModifyVolumeStorageSettingsStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSProvisionConnectionRuleStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSProvisionMachineMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSProvisionNetworkLoadBalancerStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSS3CreateInterfaceVPCEndpointStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSSetupEndpointConnectionStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSSetupInstanceTargetGroupForProxyProtocolStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSSetupInstanceTargetGroupStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSSetupMultiInstanceTargetGroupStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSSetupPeeringConnectionStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSSnapshotAndOrphanVolumeStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSStartInstanceStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSSyncDataLakePrivateEndpointHostnameMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSSyncPauseStateMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSSyncVpcPeeringConnectionMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSTerminateEndpointConnectionsStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSUpdateEndpointConnectionStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AWSUpdateInstanceSizeStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/AwsSwapIpMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/admincapacity/AWSCheckCapacityStepHelperUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/admincapacity/AWSProcessAcceptAssistedCapacityRequestMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/privatenetworking/AWSTenantConsumerAcceptPrivateEndpointMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/privatenetworking/AWSTenantConsumerAcceptPrivateEndpointStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/privatenetworking/AWSTenantConsumerRejectPrivateEndpointStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/privatenetworking/AWSTenantProducerCreatePrivateEndpointServiceStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/privatenetworking/AWSTenantProducerDeletePrivateEndpointServiceStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/snapshot/AWSCleanUpDirectAttachRestoreMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/snapshot/AWSCopySnapshotStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/snapshot/AWSCreateEbsSnapshotStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/snapshot/AWSCreateVolumeFromSnapshotStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/snapshot/AWSDirectAttachRestoreMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/snapshot/AWSOptimizedDirectAttachRestoreMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/snapshot/AWSProvisionRestoreMachineMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/snapshot/AWSProvisionServerlessRestoreMachineMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/snapshot/AWSWaitEbsSnapshotCompleteStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/planner/snapshot/AWSWaitInstanceTargetGroupHealthyStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/svc/AWSAZStatusCheckSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/svc/AWSCapacityReservationProcessorSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/svc/AWSNetworkLoadBalancerProcessorSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/svc/AWSOplogPurgeSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/svc/AWSOrphanedItemSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/svc/AWSSnapshotProcessorSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/svc/EBSVolumeProcessorSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/svc/EC2InstanceProcessorSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/svc/NDSAWSLogDownloadSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/svc/NestedS3LogsStreamingOutputUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/svc/VpcProcessorSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/util/AWSPrivateNetworkingUtilsUnitTests.java", "server/src/unit/com/xgen/svc/nds/aws/util/DNSInvalidBatchExceptionMessageParserUnitTests.java", "server/src/unit/com/xgen/svc/nds/azure/planner/AzureSyncDataLakePrivateEndpointHostnameMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/azure/planner/privateNetworking/AzureTenantConsumerAcceptPrivateEndpointMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/flex/planner/FlexProcessProxyConfigPerClusterMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/healthCheck/AWSHardwareResourceStatusProviderUnitTests.java", "server/src/unit/com/xgen/svc/nds/model/ClusterDescriptionUnitTests.java", "server/src/unit/com/xgen/svc/nds/model/CrossCloudReplicationSpecModelTestFactory.java", "server/src/unit/com/xgen/svc/nds/model/NDSModelTestFactory.java", "server/src/unit/com/xgen/svc/nds/model/SecureSSHKeyUnitTests.java", "server/src/unit/com/xgen/svc/nds/model/TestAWSRegionConfigBuilder.java", "server/src/unit/com/xgen/svc/nds/model/ui/HardwareSpecViewUnitTests.java", "server/src/unit/com/xgen/svc/nds/model/ui/RegionConfigViewUnitTests.java", "server/src/unit/com/xgen/svc/nds/onlinearchive/restorationtool/jobs/OnlineArchiveRestoreAWSJobHandlerUnitTests.java", "server/src/unit/com/xgen/svc/nds/onlinearchive/restorationtool/planner/ScanDLZJobsStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/onlinearchive/v3migration/planner/BackfillExistingDataStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/planner/AbandonPlanForPendingDiskSizeUpscaleStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/planner/BackupSnapshotUtilsUnitTests.java", "server/src/unit/com/xgen/svc/nds/planner/CpsBackupRestoreUtilsUnitTests.java", "server/src/unit/com/xgen/svc/nds/planner/GenerateAutomationConfigStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/planner/IngestionPipelineAssembleAndWriteManifestsStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/planner/IngestionPipelineCleanUpDLZObjectsStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/planner/TenantConsumerAcceptPrivateEndpointMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/planner/UploadSnapshotExportCompleteFileMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/res/NDSAdminResourceUnitTests.java", "server/src/unit/com/xgen/svc/nds/sampleDatasetLoad/planner/ImportSampleDatasetStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/sampleDatasetLoad/planner/ValidateNamespacesStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/serverless/planner/ServerlessProcessProxyConfigPerClusterMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/serverless/svc/NDSServerlessLoadSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/serverless/svc/healthcheck/AWSEnvoyHealthCheckUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/CloudChefConfSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/DataSetCredentialsSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/NDSCloudProviderAccessSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/NDSCloudProviderContainerSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/NDSClusterConversionSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/NDSDataLakeBaseSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/NDSDataLakePrivateSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/NDSDataLakePublicSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/NDSEncryptionAtRestSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/NDSPeerStatusRefreshSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/NDSProxySvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/NDSServerAccessSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/NDSTenantSnapshotRestoreSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/PushBasedLogExportSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/SystemClusterExportJobSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/cps/CpsConfSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/cps/CpsEncryptionAtRestSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/cps/CpsExportSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/cps/CpsResurrectSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/cps/CpsSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveDLZLeakedItemSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveExpirationArchiveChunkAWSJobHandlerUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveExpirationJobIdChunkFileDeletionAWSJobHandlerUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveExpiredFilesDeleterSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveInventoryScannerSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/onlinearchive/OnlineArchiveSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/planning/PlanResultUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/project/DeleteReapedClusterSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/project/NDSClusterSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/svc/streams/StreamsSvcUnitTests.java", "server/src/unit/com/xgen/svc/nds/tenant/planner/FastTenantProvisionMachineMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/tenant/planner/TenantDestroyMachineMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/tenant/planner/TenantProvisionMachineMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/tenantUpgrade/planner/DoServerlessUpgradeToDedicatedMoveUnitTests.java", "server/src/unit/com/xgen/svc/nds/tenantUpgrade/planner/RestoreSnapshotStepUnitTests.java", "server/src/unit/com/xgen/svc/nds/util/ClusterValidationUtilUnitTests.java", "server/src/unit/com/xgen/svc/nds/util/IndexConfigUtilUnitTests.java", "server/src/unit/com/xgen/svc/nds/util/PrivateLinkDNSUtilUnitTests.java", "server/src/unit/com/xgen/svc/nds/util/TenantPrivateEndpointDNSUtilUnitTests.java", "server/src/unit/com/xgen/svc/search/planner/step/aws/AWSSelectSearchSubnetStepUnitTests.java", "server/src/unit/com/xgen/svc/streams/planner/step/AWSCreateInterfaceEndpointStepUnitTests.java"]