load("@io_bazel_rules_docker//container:container.bzl", "container_image", "container_push")
load("@io_bazel_rules_docker//python:image.bzl", "py_layer")
load("@io_bazel_rules_docker//python3:image.bzl", "py3_image")
load("@rules_python//python:defs.bzl", "py_binary", "py_library")
load("@rules_shellcheck//:def.bzl", "shellcheck_test")
load("//scripts/bazel_python:defs.bzl", "lint_test", "typecheck_test")

PACKAGES = [
    "mms_telemetry",
]

LIBRARIES = [
    "//scripts/bazel_telemetry/mms_telemetry/common",
    "//scripts/bazel_telemetry/mms_telemetry/server",
]

py_binary(
    name = "server_standalone",
    srcs = ["server.py"],
    main = "server.py",
    deps = ["//scripts/bazel_telemetry/mms_telemetry/server"],
)

container_image(
    name = "telemetry_server_base",
    base = "//scripts/bazel_python:python_base",
    ports = ["8080"],
)

py_layer(
    name = "external_deps",
    filter = "@",
    deps = [
        "//scripts/bazel_telemetry/mms_telemetry/server",
    ],
)

py3_image(
    name = "server",
    srcs = ["server.py"],
    base = ":telemetry_server_base",
    layers = [
        ":external_deps",
        "//scripts/bazel_telemetry/mms_telemetry/server",
    ],
    stamp = 1,
)

container_push(
    name = "push_server",
    format = "Docker",
    image = ":server",
    registry = "{STABLE_DOCKER_REGISTRY}",
    repository = "{STABLE_DOCKER_REPOSITORY}",
    tag = "{STABLE_DOCKER_TAG}",
)

filegroup(
    name = "all_srcs",
    srcs = [
        "//scripts/bazel_telemetry/mms_telemetry/common:srcs",
        "//scripts/bazel_telemetry/mms_telemetry/server:srcs",
    ],
)

lint_test(
    args = PACKAGES,
    data = [":all_srcs"],
    deps = LIBRARIES,
)

typecheck_test(
    args = PACKAGES,
    data = [":all_srcs"],
    deps = LIBRARIES,
)

# Generated by:
# poetry run python3 -m grpc_tools.protoc \
#    -I${BAZEL_SRC}/third_party/googleapis \
#    --python_out=. --grpc_python_out=. \
#    google/devtools/build/v1/build_events.proto \
#    google/devtools/build/v1/build_status.proto \
#    google/devtools/build/v1/publish_build_event.proto \
#    google/api/annotations.proto \
#    google/api/http.proto
py_library(
    name = "bes_protos",
    srcs = glob(["google/**/*_pb2*.py"]),
)

# Generated by
# poetry run python3 -m grpc_tools.protoc \
#    -I${BAZEL_SRC}/src/main/java/com/google/devtools/build/lib/buildeventstream/proto \
#    -I${BAZEL_SRC} \
#    --python_out=. \
#    build_event_stream.proto \
#    src/main/protobuf/command_line.proto src/main/protobuf/option_filters.proto \
#    src/main/protobuf/invocation_policy.proto
py_library(
    name = "bep_protos",
    srcs = ["build_event_stream_pb2.py"] + glob(["src/main/protobuf/**/*_pb2*.py"]),
)

sh_library(
    name = "wrapper_functions",
    srcs = ["wrapper_functions.bash"],
    visibility = ["//tools:__pkg__"],
)

shellcheck_test(
    name = "wrapper_functions_check",
    data = [":wrapper_functions"],
    tags = ["tools-bazel-sanity"],
)

sh_test(
    name = "wrapper_function_tests",
    srcs = ["wrapper_function_tests.sh"],
    tags = ["tools-bazel-sanity"],
    deps = [":wrapper_functions"],
)

genrule(
    name = "intellij_full_sync",
    outs = ["intellij_full_sync"],
    cmd = "touch $(location intellij_full_sync)",
)
