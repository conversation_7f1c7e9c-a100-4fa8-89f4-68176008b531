{"prod": [{"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"25\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAutomationAgentIsStarted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed getting target hostname to snapshot. Will retry | performInternal | CpsReplSetSnapshotMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Last ping date not fresh. Validation did not complete. | performInternal | WaitForDataValidationCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"waited for a second ping but the MongoDB version or FCV changed | performInternal | WaitForPingVersionDataStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForSecondPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 25 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"10\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error creating peering connection in resource group | processCreateConnectionData | AzureSyncPeeringConnectionMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"ebsVolumeId not found | performSnapshotStep | AwsReplSetSnapshotter\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'createACMEOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureResourceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to RESUME instance | execute | NDSSyncPauseStateExecutor\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForEbsSnapshotCompleted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to retrieve and save cert | performInternal | ACMEDownloadCertStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'CapacityReservationRequest' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeAutomationAgentRetry' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForKMSIPAccessListConfiguration' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForClusterProvisioned' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'checkMongosyncProgress_kubeJob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getMongoMirrorHttpStatusForKube' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'startInstance' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 10 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"50\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalStateException | getProcesses | com.xgen.svc.nds.planner.DoSnapshotRestoreMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | org.shredzone.acme4j.exception.AcmeLazyLoadingException | runIfReady | com.xgen.svc.mms.planner.model.Move\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAllProcessesHealthy' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForHostGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAccess' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handlePlanExecutionCloudProviderException | AWSMoveUtilityProvider\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status READY' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status VALID' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No eligible hardware to take a snapshot from | pickInstanceIdToSnapshot | BackupSnapshotUtils\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"oplog too short despite multiple consecutive attempts to snapshot | createEBSSnapshot | Move\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'importSampleDataset' | evaluateAttempt | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 50 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"200\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Authorization in an unexpected state. | ensureAuthorizationsInExpectedStates | ACMEPreAuthSteps\"}], \"name\": \"Tracked Plan Execution Failure - 200 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to insufficient free private IP addresses in subnet. | handleAwsException | AWSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to reserve capacity exceeded error. | handleAwsException | AWSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEC2InstanceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'confirmHostIPIsReachable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getUsedDiskSpace' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Retrying linearizable read job failed | performInternal | RunLinearizableReadStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Restore failed for cluster | performInternal | DoTenantToTenantRestoreMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'verifyElasticIpExistAfterCreation' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"New cloud chef conf expired before it was fetched | performInternal | UpdateCloudConfStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Non-provisioned InstanceHardware with expected index for mtm | performInternal | FastFreeProvisionMachineMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'createDisk' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to setup some region groups for endpoint service | performInternal | GCPSyncPrivateServiceConnectRegionGroupMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The push based migration step failed. | printResultLog | PushLiveImportMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveLoadBalancerProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'takeSnapshot' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error inserting tenant restore for M0 unpause. | insertTenantRestore | NDSInitiateTenantRestoreStep\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"30\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForFileSystemResize' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 30 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"20\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import failed for all involved replica sets. | performInternal | WaitForLiveImportCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import for Mongosync failed on only replica set in unrestartable way | performInternal | WaitForMongosyncLiveImportCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEBSVolumeIsAvailable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'isRebootedWaitUptimeDecrease' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalConfigVersion' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Max number of regions for GCP container exceeded | performInternal | GCPCreateSubnetStep\"}], \"name\": \"Tracked Plan Execution Failure - 20 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"12\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The agent encountered an error while opening or querying the cursor for snapshot | performInternal | WaitForWtcSnapshotToReachState\"}], \"name\": \"Tracked Plan Execution Failure - 12 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"15\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Shards topology is changing in the middle of a snapshot move, failing the snapshot. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'provisionVirtualMachine' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handleNDSException | NDSMove\"}], \"name\": \"Tracked Plan Execution Failure - 15 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"100\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForCpsStreamingRestoreJobs' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 100 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"50\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveChallengeProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed rolling index build detected | cleanupFailedRollingIndexBuild | WaitForAutomationAgentsGoalStateStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Validation completed with resource exhausted error. | isValidationComplete | Step\"}], \"name\": \"Tracked Plan Execution Failure - 50 in 1h (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to create virtual machine | performInternal | AbstractAzureCreateVirtualMachineStep\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 1h (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1d\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForBackupTerminated' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 1d (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"500\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'hasLastBatchArrived' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForRestoreServerReady' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'backupCursorExtended' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 500 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"75\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForValidationFirstPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 75 in 1h (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"25\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to submit the given CSR | executeOrder | ACMEExecuteOrderStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | perform | NVMeRealHandler\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | performInternal | CpsShardedSnapshotMove\"}], \"name\": \"Tracked Plan Execution Failure - 25 in 1h (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1d\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"50\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to initialize PIT settings | syncPitSettings | SyncBackupSettingsMove\"}], \"name\": \"Tracked Plan Execution Failure - 50 in 1d (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"100\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoSocketReadException\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoTimeoutException | createTimeoutException | com.mongodb.internal.connection.DefaultConnectionPool\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoQueryException\"}], \"name\": \"Tracked Plan Generation Failures - 100 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"50\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoCommandException\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | java.lang.IllegalStateException | shouldEnableOplogEncryption | com.xgen.svc.nds.util.PitEncryptionUtil\"}], \"name\": \"Tracked Plan Generation Failures - 50 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalArgumentException | initialize | com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\"}], \"name\": \"Tracked Plan Generation Failures - 5 in 15m (prod)\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"UNTRACKED_PLAN_FAILURE": "{\"actionMatch\": \"all\", \"filterMatch\": \"all\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [], \"filters\": [{\"match\": \"eq\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"event_type\", \"value\": \"planner-failure\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status READY' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'provisionVirtualMachine' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status VALID' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handleNDSException | NDSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import for Mongosync failed on only replica set in unrestartable way | performInternal | WaitForMongosyncLiveImportCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'confirmHostIPIsReachable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForBackupTerminated' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Retrying linearizable read job failed | performInternal | RunLinearizableReadStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getUsedDiskSpace' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'hasLastBatchArrived' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to insufficient free private IP addresses in subnet. | handleAwsException | AWSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'backupCursorExtended' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForRestoreServerReady' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handlePlanExecutionCloudProviderException | AWSMoveUtilityProvider\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed rolling index build detected | cleanupFailedRollingIndexBuild | WaitForAutomationAgentsGoalStateStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEBSVolumeIsAvailable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForEbsSnapshotCompleted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'importSampleDataset' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEC2InstanceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to reserve capacity exceeded error. | handleAwsException | AWSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'startInstance' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalConfigVersion' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Restore failed for cluster | performInternal | DoTenantToTenantRestoreMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to retrieve and save cert | performInternal | ACMEDownloadCertStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getMongoMirrorHttpStatusForKube' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The agent encountered an error while opening or querying the cursor for snapshot | performInternal | WaitForWtcSnapshotToReachState\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Last ping date not fresh. Validation did not complete. | performInternal | WaitForDataValidationCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"oplog too short despite multiple consecutive attempts to snapshot | createEBSSnapshot | Move\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import failed for all involved replica sets. | performInternal | WaitForLiveImportCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Shards topology is changing in the middle of a snapshot move, failing the snapshot. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Validation completed with resource exhausted error. | isValidationComplete | Step\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to submit the given CSR | executeOrder | ACMEExecuteOrderStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForValidationFirstPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Authorization in an unexpected state. | ensureAuthorizationsInExpectedStates | ACMEPreAuthSteps\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to initialize PIT settings | syncPitSettings | SyncBackupSettingsMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeAutomationAgentRetry' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | perform | NVMeRealHandler\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForFileSystemResize' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForCpsStreamingRestoreJobs' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"New cloud chef conf expired before it was fetched | performInternal | UpdateCloudConfStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'createDisk' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Non-provisioned InstanceHardware with expected index for mtm | performInternal | FastFreeProvisionMachineMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"ebsVolumeId not found | performSnapshotStep | AwsReplSetSnapshotter\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForHostGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'verifyElasticIpExistAfterCreation' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveLoadBalancerProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAccess' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"waited for a second ping but the MongoDB version or FCV changed | performInternal | WaitForPingVersionDataStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'CapacityReservationRequest' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Max number of regions for GCP container exceeded | performInternal | GCPCreateSubnetStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No eligible hardware to take a snapshot from | pickInstanceIdToSnapshot | BackupSnapshotUtils\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to setup some region groups for endpoint service | performInternal | GCPSyncPrivateServiceConnectRegionGroupMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForKMSIPAccessListConfiguration' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAutomationAgentIsStarted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed getting target hostname to snapshot. Will retry | performInternal | CpsReplSetSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The push based migration step failed. | printResultLog | PushLiveImportMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'takeSnapshot' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error inserting tenant restore for M0 unpause. | insertTenantRestore | NDSInitiateTenantRestoreStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'checkMongosyncProgress_kubeJob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForClusterProvisioned' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForSecondPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalStateException | getProcesses | com.xgen.svc.nds.planner.DoSnapshotRestoreMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoCommandException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to RESUME instance | execute | NDSSyncPauseStateExecutor\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoTimeoutException | createTimeoutException | com.mongodb.internal.connection.DefaultConnectionPool\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoSocketReadException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalArgumentException | initialize | com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoQueryException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveChallengeProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | java.lang.IllegalStateException | shouldEnableOplogEncryption | com.xgen.svc.nds.util.PitEncryptionUtil\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAllProcessesHealthy' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'createACMEOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | org.shredzone.acme4j.exception.AcmeLazyLoadingException | runIfReady | com.xgen.svc.mms.planner.model.Move\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to create virtual machine | performInternal | AbstractAzureCreateVirtualMachineStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'isRebootedWaitUptimeDecrease' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureResourceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error creating peering connection in resource group | processCreateConnectionData | AzureSyncPeeringConnectionMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Untracked Plan Failures prod\", \"frequency\": \"720\", \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}, {"CUSTOM": "{\"actionMatch\": \"all\", \"filterMatch\": \"all\", \"actions\": [{\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"id\": \"sentry.rules.conditions.regression_event.RegressionEventCondition\"}], \"filters\": [{\"match\": \"co\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"event_type\", \"value\": \"planner-failure\"}], \"name\": \"[Planner Failures] Regression Alert Handling\", \"frequency\": 60, \"environment\": \"prod\", \"owner\": \"team:4505517806583808\"}", "env": "prod"}], "staging": [{"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"15\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAutomationAgentIsStarted' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 15 in 15m (staging)\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to insufficient free private IP addresses in subnet. | handleAwsException | AWSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to reserve capacity exceeded error. | handleAwsException | AWSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEBSVolumeIsAvailable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The agent encountered an error while opening or querying the cursor for snapshot | performInternal | WaitForWtcSnapshotToReachState\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error creating peering connection in resource group | processCreateConnectionData | AzureSyncPeeringConnectionMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"ebsVolumeId not found | performSnapshotStep | AwsReplSetSnapshotter\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'createACMEOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureResourceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'isRebootedWaitUptimeDecrease' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEC2InstanceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'confirmHostIPIsReachable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'provisionVirtualMachine' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getUsedDiskSpace' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Retrying linearizable read job failed | performInternal | RunLinearizableReadStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handleNDSException | NDSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to RESUME instance | execute | NDSSyncPauseStateExecutor\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Restore failed for cluster | performInternal | DoTenantToTenantRestoreMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalConfigVersion' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Last ping date not fresh. Validation did not complete. | performInternal | WaitForDataValidationCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForEbsSnapshotCompleted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to retrieve and save cert | performInternal | ACMEDownloadCertStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'CapacityReservationRequest' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeAutomationAgentRetry' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'verifyElasticIpExistAfterCreation' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"New cloud chef conf expired before it was fetched | performInternal | UpdateCloudConfStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Non-provisioned InstanceHardware with expected index for mtm | performInternal | FastFreeProvisionMachineMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'createDisk' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to setup some region groups for endpoint service | performInternal | GCPSyncPrivateServiceConnectRegionGroupMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Max number of regions for GCP container exceeded | performInternal | GCPCreateSubnetStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The push based migration step failed. | printResultLog | PushLiveImportMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveLoadBalancerProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'takeSnapshot' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'importSampleDataset' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"waited for a second ping but the MongoDB version or FCV changed | performInternal | WaitForPingVersionDataStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForKMSIPAccessListConfiguration' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error inserting tenant restore for M0 unpause. | insertTenantRestore | NDSInitiateTenantRestoreStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForClusterProvisioned' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'checkMongosyncProgress_kubeJob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getMongoMirrorHttpStatusForKube' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'startInstance' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 15m (staging)\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"10\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalStateException | getProcesses | com.xgen.svc.nds.planner.DoSnapshotRestoreMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForFileSystemResize' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import failed for all involved replica sets. | performInternal | WaitForLiveImportCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import for Mongosync failed on only replica set in unrestartable way | performInternal | WaitForMongosyncLiveImportCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No eligible hardware to take a snapshot from | pickInstanceIdToSnapshot | BackupSnapshotUtils\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed getting target hostname to snapshot. Will retry | performInternal | CpsReplSetSnapshotMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Shards topology is changing in the middle of a snapshot move, failing the snapshot. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"oplog too short despite multiple consecutive attempts to snapshot | createEBSSnapshot | Move\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForSecondPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 10 in 15m (staging)\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"20\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Authorization in an unexpected state. | ensureAuthorizationsInExpectedStates | ACMEPreAuthSteps\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | org.shredzone.acme4j.exception.AcmeLazyLoadingException | runIfReady | com.xgen.svc.mms.planner.model.Move\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAllProcessesHealthy' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForHostGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAccess' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handlePlanExecutionCloudProviderException | AWSMoveUtilityProvider\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status READY' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status VALID' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForCpsStreamingRestoreJobs' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 20 in 15m (staging)\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"20\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveChallengeProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 20 in 1h (staging)\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to create virtual machine | performInternal | AbstractAzureCreateVirtualMachineStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed rolling index build detected | cleanupFailedRollingIndexBuild | WaitForAutomationAgentsGoalStateStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForValidationFirstPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Validation completed with resource exhausted error. | isValidationComplete | Step\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to submit the given CSR | executeOrder | ACMEExecuteOrderStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | perform | NVMeRealHandler\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | performInternal | CpsShardedSnapshotMove\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 1h (staging)\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1d\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForBackupTerminated' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to initialize PIT settings | syncPitSettings | SyncBackupSettingsMove\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 1d (staging)\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"500\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'hasLastBatchArrived' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForRestoreServerReady' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'backupCursorExtended' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 500 in 15m (staging)\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"15\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoSocketReadException\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoTimeoutException | createTimeoutException | com.mongodb.internal.connection.DefaultConnectionPool\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoQueryException\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoCommandException\"}], \"name\": \"Tracked Plan Generation Failures - 15 in 15m (staging)\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"50\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | java.lang.IllegalStateException | shouldEnableOplogEncryption | com.xgen.svc.nds.util.PitEncryptionUtil\"}], \"name\": \"Tracked Plan Generation Failures - 50 in 15m (staging)\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalArgumentException | initialize | com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\"}], \"name\": \"Tracked Plan Generation Failures - 5 in 15m (staging)\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"UNTRACKED_PLAN_FAILURE": "{\"actionMatch\": \"all\", \"filterMatch\": \"all\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [], \"filters\": [{\"match\": \"eq\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"event_type\", \"value\": \"planner-failure\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status READY' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'provisionVirtualMachine' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status VALID' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handleNDSException | NDSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import for Mongosync failed on only replica set in unrestartable way | performInternal | WaitForMongosyncLiveImportCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'confirmHostIPIsReachable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForBackupTerminated' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Retrying linearizable read job failed | performInternal | RunLinearizableReadStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getUsedDiskSpace' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'hasLastBatchArrived' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to insufficient free private IP addresses in subnet. | handleAwsException | AWSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'backupCursorExtended' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForRestoreServerReady' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handlePlanExecutionCloudProviderException | AWSMoveUtilityProvider\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed rolling index build detected | cleanupFailedRollingIndexBuild | WaitForAutomationAgentsGoalStateStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEBSVolumeIsAvailable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForEbsSnapshotCompleted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'importSampleDataset' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEC2InstanceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to reserve capacity exceeded error. | handleAwsException | AWSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'startInstance' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalConfigVersion' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Restore failed for cluster | performInternal | DoTenantToTenantRestoreMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to retrieve and save cert | performInternal | ACMEDownloadCertStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getMongoMirrorHttpStatusForKube' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The agent encountered an error while opening or querying the cursor for snapshot | performInternal | WaitForWtcSnapshotToReachState\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Last ping date not fresh. Validation did not complete. | performInternal | WaitForDataValidationCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"oplog too short despite multiple consecutive attempts to snapshot | createEBSSnapshot | Move\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import failed for all involved replica sets. | performInternal | WaitForLiveImportCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Shards topology is changing in the middle of a snapshot move, failing the snapshot. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Validation completed with resource exhausted error. | isValidationComplete | Step\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to submit the given CSR | executeOrder | ACMEExecuteOrderStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForValidationFirstPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Authorization in an unexpected state. | ensureAuthorizationsInExpectedStates | ACMEPreAuthSteps\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to initialize PIT settings | syncPitSettings | SyncBackupSettingsMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeAutomationAgentRetry' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | perform | NVMeRealHandler\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForFileSystemResize' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForCpsStreamingRestoreJobs' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"New cloud chef conf expired before it was fetched | performInternal | UpdateCloudConfStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'createDisk' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Non-provisioned InstanceHardware with expected index for mtm | performInternal | FastFreeProvisionMachineMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"ebsVolumeId not found | performSnapshotStep | AwsReplSetSnapshotter\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForHostGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'verifyElasticIpExistAfterCreation' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveLoadBalancerProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAccess' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"waited for a second ping but the MongoDB version or FCV changed | performInternal | WaitForPingVersionDataStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'CapacityReservationRequest' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Max number of regions for GCP container exceeded | performInternal | GCPCreateSubnetStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No eligible hardware to take a snapshot from | pickInstanceIdToSnapshot | BackupSnapshotUtils\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to setup some region groups for endpoint service | performInternal | GCPSyncPrivateServiceConnectRegionGroupMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForKMSIPAccessListConfiguration' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAutomationAgentIsStarted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed getting target hostname to snapshot. Will retry | performInternal | CpsReplSetSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The push based migration step failed. | printResultLog | PushLiveImportMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'takeSnapshot' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error inserting tenant restore for M0 unpause. | insertTenantRestore | NDSInitiateTenantRestoreStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'checkMongosyncProgress_kubeJob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForClusterProvisioned' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForSecondPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalStateException | getProcesses | com.xgen.svc.nds.planner.DoSnapshotRestoreMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoCommandException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to RESUME instance | execute | NDSSyncPauseStateExecutor\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoTimeoutException | createTimeoutException | com.mongodb.internal.connection.DefaultConnectionPool\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoSocketReadException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalArgumentException | initialize | com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoQueryException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveChallengeProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | java.lang.IllegalStateException | shouldEnableOplogEncryption | com.xgen.svc.nds.util.PitEncryptionUtil\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAllProcessesHealthy' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'createACMEOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | org.shredzone.acme4j.exception.AcmeLazyLoadingException | runIfReady | com.xgen.svc.mms.planner.model.Move\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to create virtual machine | performInternal | AbstractAzureCreateVirtualMachineStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'isRebootedWaitUptimeDecrease' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureResourceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error creating peering connection in resource group | processCreateConnectionData | AzureSyncPeeringConnectionMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Untracked Plan Failures staging\", \"frequency\": \"720\", \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}, {"CUSTOM": "{\"actionMatch\": \"all\", \"filterMatch\": \"all\", \"actions\": [{\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"id\": \"sentry.rules.conditions.regression_event.RegressionEventCondition\"}], \"filters\": [{\"match\": \"co\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"event_type\", \"value\": \"planner-failure\"}], \"name\": \"[Planner Failures] Regression Alert Handling\", \"frequency\": 60, \"environment\": \"staging\", \"owner\": \"team:4505517806583808\"}", "env": "staging"}], "qa": [{"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"15\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAutomationAgentIsStarted' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 15 in 15m (qa)\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to insufficient free private IP addresses in subnet. | handleAwsException | AWSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to reserve capacity exceeded error. | handleAwsException | AWSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEBSVolumeIsAvailable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The agent encountered an error while opening or querying the cursor for snapshot | performInternal | WaitForWtcSnapshotToReachState\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error creating peering connection in resource group | processCreateConnectionData | AzureSyncPeeringConnectionMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"ebsVolumeId not found | performSnapshotStep | AwsReplSetSnapshotter\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'createACMEOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureResourceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'isRebootedWaitUptimeDecrease' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEC2InstanceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'confirmHostIPIsReachable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'provisionVirtualMachine' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getUsedDiskSpace' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Retrying linearizable read job failed | performInternal | RunLinearizableReadStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handleNDSException | NDSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to RESUME instance | execute | NDSSyncPauseStateExecutor\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Restore failed for cluster | performInternal | DoTenantToTenantRestoreMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalConfigVersion' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Last ping date not fresh. Validation did not complete. | performInternal | WaitForDataValidationCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForEbsSnapshotCompleted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to retrieve and save cert | performInternal | ACMEDownloadCertStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'CapacityReservationRequest' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeAutomationAgentRetry' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'verifyElasticIpExistAfterCreation' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"New cloud chef conf expired before it was fetched | performInternal | UpdateCloudConfStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Non-provisioned InstanceHardware with expected index for mtm | performInternal | FastFreeProvisionMachineMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'createDisk' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to setup some region groups for endpoint service | performInternal | GCPSyncPrivateServiceConnectRegionGroupMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Max number of regions for GCP container exceeded | performInternal | GCPCreateSubnetStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The push based migration step failed. | printResultLog | PushLiveImportMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveLoadBalancerProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'takeSnapshot' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'importSampleDataset' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"waited for a second ping but the MongoDB version or FCV changed | performInternal | WaitForPingVersionDataStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForKMSIPAccessListConfiguration' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error inserting tenant restore for M0 unpause. | insertTenantRestore | NDSInitiateTenantRestoreStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForClusterProvisioned' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'checkMongosyncProgress_kubeJob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getMongoMirrorHttpStatusForKube' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'startInstance' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 15m (qa)\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"10\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalStateException | getProcesses | com.xgen.svc.nds.planner.DoSnapshotRestoreMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForFileSystemResize' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import failed for all involved replica sets. | performInternal | WaitForLiveImportCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import for Mongosync failed on only replica set in unrestartable way | performInternal | WaitForMongosyncLiveImportCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No eligible hardware to take a snapshot from | pickInstanceIdToSnapshot | BackupSnapshotUtils\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed getting target hostname to snapshot. Will retry | performInternal | CpsReplSetSnapshotMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Shards topology is changing in the middle of a snapshot move, failing the snapshot. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"oplog too short despite multiple consecutive attempts to snapshot | createEBSSnapshot | Move\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForSecondPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 10 in 15m (qa)\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"20\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Authorization in an unexpected state. | ensureAuthorizationsInExpectedStates | ACMEPreAuthSteps\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | org.shredzone.acme4j.exception.AcmeLazyLoadingException | runIfReady | com.xgen.svc.mms.planner.model.Move\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAllProcessesHealthy' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForHostGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAccess' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handlePlanExecutionCloudProviderException | AWSMoveUtilityProvider\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status READY' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status VALID' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForCpsStreamingRestoreJobs' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 20 in 15m (qa)\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"20\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveChallengeProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 20 in 1h (qa)\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to create virtual machine | performInternal | AbstractAzureCreateVirtualMachineStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed rolling index build detected | cleanupFailedRollingIndexBuild | WaitForAutomationAgentsGoalStateStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForValidationFirstPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Validation completed with resource exhausted error. | isValidationComplete | Step\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to submit the given CSR | executeOrder | ACMEExecuteOrderStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | perform | NVMeRealHandler\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | performInternal | CpsShardedSnapshotMove\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 1h (qa)\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1d\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForBackupTerminated' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to initialize PIT settings | syncPitSettings | SyncBackupSettingsMove\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 1d (qa)\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"500\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'hasLastBatchArrived' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForRestoreServerReady' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'backupCursorExtended' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 500 in 15m (qa)\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"15\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoSocketReadException\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoTimeoutException | createTimeoutException | com.mongodb.internal.connection.DefaultConnectionPool\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoQueryException\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoCommandException\"}], \"name\": \"Tracked Plan Generation Failures - 15 in 15m (qa)\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"50\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | java.lang.IllegalStateException | shouldEnableOplogEncryption | com.xgen.svc.nds.util.PitEncryptionUtil\"}], \"name\": \"Tracked Plan Generation Failures - 50 in 15m (qa)\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalArgumentException | initialize | com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\"}], \"name\": \"Tracked Plan Generation Failures - 5 in 15m (qa)\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"UNTRACKED_PLAN_FAILURE": "{\"actionMatch\": \"all\", \"filterMatch\": \"all\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [], \"filters\": [{\"match\": \"eq\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"event_type\", \"value\": \"planner-failure\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status READY' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'provisionVirtualMachine' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status VALID' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handleNDSException | NDSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import for Mongosync failed on only replica set in unrestartable way | performInternal | WaitForMongosyncLiveImportCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'confirmHostIPIsReachable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForBackupTerminated' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Retrying linearizable read job failed | performInternal | RunLinearizableReadStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getUsedDiskSpace' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'hasLastBatchArrived' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to insufficient free private IP addresses in subnet. | handleAwsException | AWSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'backupCursorExtended' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForRestoreServerReady' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handlePlanExecutionCloudProviderException | AWSMoveUtilityProvider\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed rolling index build detected | cleanupFailedRollingIndexBuild | WaitForAutomationAgentsGoalStateStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEBSVolumeIsAvailable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForEbsSnapshotCompleted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'importSampleDataset' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEC2InstanceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to reserve capacity exceeded error. | handleAwsException | AWSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'startInstance' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalConfigVersion' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Restore failed for cluster | performInternal | DoTenantToTenantRestoreMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to retrieve and save cert | performInternal | ACMEDownloadCertStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getMongoMirrorHttpStatusForKube' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The agent encountered an error while opening or querying the cursor for snapshot | performInternal | WaitForWtcSnapshotToReachState\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Last ping date not fresh. Validation did not complete. | performInternal | WaitForDataValidationCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"oplog too short despite multiple consecutive attempts to snapshot | createEBSSnapshot | Move\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import failed for all involved replica sets. | performInternal | WaitForLiveImportCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Shards topology is changing in the middle of a snapshot move, failing the snapshot. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Validation completed with resource exhausted error. | isValidationComplete | Step\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to submit the given CSR | executeOrder | ACMEExecuteOrderStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForValidationFirstPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Authorization in an unexpected state. | ensureAuthorizationsInExpectedStates | ACMEPreAuthSteps\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to initialize PIT settings | syncPitSettings | SyncBackupSettingsMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeAutomationAgentRetry' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | perform | NVMeRealHandler\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForFileSystemResize' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForCpsStreamingRestoreJobs' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"New cloud chef conf expired before it was fetched | performInternal | UpdateCloudConfStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'createDisk' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Non-provisioned InstanceHardware with expected index for mtm | performInternal | FastFreeProvisionMachineMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"ebsVolumeId not found | performSnapshotStep | AwsReplSetSnapshotter\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForHostGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'verifyElasticIpExistAfterCreation' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveLoadBalancerProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAccess' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"waited for a second ping but the MongoDB version or FCV changed | performInternal | WaitForPingVersionDataStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'CapacityReservationRequest' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Max number of regions for GCP container exceeded | performInternal | GCPCreateSubnetStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No eligible hardware to take a snapshot from | pickInstanceIdToSnapshot | BackupSnapshotUtils\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to setup some region groups for endpoint service | performInternal | GCPSyncPrivateServiceConnectRegionGroupMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForKMSIPAccessListConfiguration' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAutomationAgentIsStarted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed getting target hostname to snapshot. Will retry | performInternal | CpsReplSetSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The push based migration step failed. | printResultLog | PushLiveImportMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'takeSnapshot' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error inserting tenant restore for M0 unpause. | insertTenantRestore | NDSInitiateTenantRestoreStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'checkMongosyncProgress_kubeJob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForClusterProvisioned' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForSecondPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalStateException | getProcesses | com.xgen.svc.nds.planner.DoSnapshotRestoreMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoCommandException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to RESUME instance | execute | NDSSyncPauseStateExecutor\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoTimeoutException | createTimeoutException | com.mongodb.internal.connection.DefaultConnectionPool\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoSocketReadException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalArgumentException | initialize | com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoQueryException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveChallengeProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | java.lang.IllegalStateException | shouldEnableOplogEncryption | com.xgen.svc.nds.util.PitEncryptionUtil\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAllProcessesHealthy' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'createACMEOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | org.shredzone.acme4j.exception.AcmeLazyLoadingException | runIfReady | com.xgen.svc.mms.planner.model.Move\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to create virtual machine | performInternal | AbstractAzureCreateVirtualMachineStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'isRebootedWaitUptimeDecrease' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureResourceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error creating peering connection in resource group | processCreateConnectionData | AzureSyncPeeringConnectionMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Untracked Plan Failures qa\", \"frequency\": \"720\", \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}, {"CUSTOM": "{\"actionMatch\": \"all\", \"filterMatch\": \"all\", \"actions\": [{\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"id\": \"sentry.rules.conditions.regression_event.RegressionEventCondition\"}], \"filters\": [{\"match\": \"co\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"event_type\", \"value\": \"planner-failure\"}], \"name\": \"[Planner Failures] Regression Alert Handling\", \"frequency\": 60, \"environment\": \"qa\", \"owner\": \"team:4505517806583808\"}", "env": "qa"}], "dev": [{"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"15\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAutomationAgentIsStarted' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 15 in 15m (dev)\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to insufficient free private IP addresses in subnet. | handleAwsException | AWSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to reserve capacity exceeded error. | handleAwsException | AWSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEBSVolumeIsAvailable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The agent encountered an error while opening or querying the cursor for snapshot | performInternal | WaitForWtcSnapshotToReachState\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error creating peering connection in resource group | processCreateConnectionData | AzureSyncPeeringConnectionMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"ebsVolumeId not found | performSnapshotStep | AwsReplSetSnapshotter\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'createACMEOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureResourceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'isRebootedWaitUptimeDecrease' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEC2InstanceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'confirmHostIPIsReachable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'provisionVirtualMachine' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getUsedDiskSpace' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Retrying linearizable read job failed | performInternal | RunLinearizableReadStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handleNDSException | NDSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to RESUME instance | execute | NDSSyncPauseStateExecutor\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Restore failed for cluster | performInternal | DoTenantToTenantRestoreMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalConfigVersion' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Last ping date not fresh. Validation did not complete. | performInternal | WaitForDataValidationCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForEbsSnapshotCompleted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to retrieve and save cert | performInternal | ACMEDownloadCertStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'CapacityReservationRequest' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeAutomationAgentRetry' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'verifyElasticIpExistAfterCreation' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"New cloud chef conf expired before it was fetched | performInternal | UpdateCloudConfStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Non-provisioned InstanceHardware with expected index for mtm | performInternal | FastFreeProvisionMachineMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'createDisk' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to setup some region groups for endpoint service | performInternal | GCPSyncPrivateServiceConnectRegionGroupMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Max number of regions for GCP container exceeded | performInternal | GCPCreateSubnetStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The push based migration step failed. | printResultLog | PushLiveImportMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveLoadBalancerProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'takeSnapshot' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'importSampleDataset' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"waited for a second ping but the MongoDB version or FCV changed | performInternal | WaitForPingVersionDataStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForKMSIPAccessListConfiguration' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error inserting tenant restore for M0 unpause. | insertTenantRestore | NDSInitiateTenantRestoreStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForClusterProvisioned' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'checkMongosyncProgress_kubeJob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getMongoMirrorHttpStatusForKube' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'startInstance' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 15m (dev)\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"10\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalStateException | getProcesses | com.xgen.svc.nds.planner.DoSnapshotRestoreMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForFileSystemResize' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import failed for all involved replica sets. | performInternal | WaitForLiveImportCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import for Mongosync failed on only replica set in unrestartable way | performInternal | WaitForMongosyncLiveImportCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No eligible hardware to take a snapshot from | pickInstanceIdToSnapshot | BackupSnapshotUtils\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed getting target hostname to snapshot. Will retry | performInternal | CpsReplSetSnapshotMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Shards topology is changing in the middle of a snapshot move, failing the snapshot. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"oplog too short despite multiple consecutive attempts to snapshot | createEBSSnapshot | Move\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForSecondPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 10 in 15m (dev)\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"20\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Authorization in an unexpected state. | ensureAuthorizationsInExpectedStates | ACMEPreAuthSteps\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | org.shredzone.acme4j.exception.AcmeLazyLoadingException | runIfReady | com.xgen.svc.mms.planner.model.Move\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAllProcessesHealthy' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForHostGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAccess' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handlePlanExecutionCloudProviderException | AWSMoveUtilityProvider\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status READY' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status VALID' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForCpsStreamingRestoreJobs' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 20 in 15m (dev)\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"20\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveChallengeProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 20 in 1h (dev)\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to create virtual machine | performInternal | AbstractAzureCreateVirtualMachineStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed rolling index build detected | cleanupFailedRollingIndexBuild | WaitForAutomationAgentsGoalStateStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForValidationFirstPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Validation completed with resource exhausted error. | isValidationComplete | Step\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to submit the given CSR | executeOrder | ACMEExecuteOrderStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | perform | NVMeRealHandler\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | performInternal | CpsShardedSnapshotMove\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 1h (dev)\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1d\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForBackupTerminated' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to initialize PIT settings | syncPitSettings | SyncBackupSettingsMove\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 1d (dev)\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"500\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'hasLastBatchArrived' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForRestoreServerReady' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'backupCursorExtended' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 500 in 15m (dev)\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"15\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoSocketReadException\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoTimeoutException | createTimeoutException | com.mongodb.internal.connection.DefaultConnectionPool\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoQueryException\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoCommandException\"}], \"name\": \"Tracked Plan Generation Failures - 15 in 15m (dev)\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"50\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | java.lang.IllegalStateException | shouldEnableOplogEncryption | com.xgen.svc.nds.util.PitEncryptionUtil\"}], \"name\": \"Tracked Plan Generation Failures - 50 in 15m (dev)\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalArgumentException | initialize | com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\"}], \"name\": \"Tracked Plan Generation Failures - 5 in 15m (dev)\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"UNTRACKED_PLAN_FAILURE": "{\"actionMatch\": \"all\", \"filterMatch\": \"all\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582SR9N8K\", \"channel\": \"sentry-planner-failures-non-prod\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [], \"filters\": [{\"match\": \"eq\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"event_type\", \"value\": \"planner-failure\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status READY' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'provisionVirtualMachine' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status VALID' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handleNDSException | NDSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import for Mongosync failed on only replica set in unrestartable way | performInternal | WaitForMongosyncLiveImportCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'confirmHostIPIsReachable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForBackupTerminated' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Retrying linearizable read job failed | performInternal | RunLinearizableReadStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getUsedDiskSpace' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'hasLastBatchArrived' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to insufficient free private IP addresses in subnet. | handleAwsException | AWSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'backupCursorExtended' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForRestoreServerReady' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handlePlanExecutionCloudProviderException | AWSMoveUtilityProvider\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed rolling index build detected | cleanupFailedRollingIndexBuild | WaitForAutomationAgentsGoalStateStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEBSVolumeIsAvailable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForEbsSnapshotCompleted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'importSampleDataset' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEC2InstanceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to reserve capacity exceeded error. | handleAwsException | AWSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'startInstance' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalConfigVersion' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Restore failed for cluster | performInternal | DoTenantToTenantRestoreMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to retrieve and save cert | performInternal | ACMEDownloadCertStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getMongoMirrorHttpStatusForKube' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The agent encountered an error while opening or querying the cursor for snapshot | performInternal | WaitForWtcSnapshotToReachState\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Last ping date not fresh. Validation did not complete. | performInternal | WaitForDataValidationCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"oplog too short despite multiple consecutive attempts to snapshot | createEBSSnapshot | Move\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import failed for all involved replica sets. | performInternal | WaitForLiveImportCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Shards topology is changing in the middle of a snapshot move, failing the snapshot. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Validation completed with resource exhausted error. | isValidationComplete | Step\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to submit the given CSR | executeOrder | ACMEExecuteOrderStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForValidationFirstPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Authorization in an unexpected state. | ensureAuthorizationsInExpectedStates | ACMEPreAuthSteps\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to initialize PIT settings | syncPitSettings | SyncBackupSettingsMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeAutomationAgentRetry' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | perform | NVMeRealHandler\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForFileSystemResize' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForCpsStreamingRestoreJobs' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"New cloud chef conf expired before it was fetched | performInternal | UpdateCloudConfStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'createDisk' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Non-provisioned InstanceHardware with expected index for mtm | performInternal | FastFreeProvisionMachineMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"ebsVolumeId not found | performSnapshotStep | AwsReplSetSnapshotter\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForHostGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'verifyElasticIpExistAfterCreation' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveLoadBalancerProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAccess' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"waited for a second ping but the MongoDB version or FCV changed | performInternal | WaitForPingVersionDataStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'CapacityReservationRequest' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Max number of regions for GCP container exceeded | performInternal | GCPCreateSubnetStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No eligible hardware to take a snapshot from | pickInstanceIdToSnapshot | BackupSnapshotUtils\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to setup some region groups for endpoint service | performInternal | GCPSyncPrivateServiceConnectRegionGroupMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForKMSIPAccessListConfiguration' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAutomationAgentIsStarted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed getting target hostname to snapshot. Will retry | performInternal | CpsReplSetSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The push based migration step failed. | printResultLog | PushLiveImportMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'takeSnapshot' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error inserting tenant restore for M0 unpause. | insertTenantRestore | NDSInitiateTenantRestoreStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'checkMongosyncProgress_kubeJob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForClusterProvisioned' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForSecondPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalStateException | getProcesses | com.xgen.svc.nds.planner.DoSnapshotRestoreMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoCommandException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to RESUME instance | execute | NDSSyncPauseStateExecutor\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoTimeoutException | createTimeoutException | com.mongodb.internal.connection.DefaultConnectionPool\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoSocketReadException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalArgumentException | initialize | com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoQueryException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveChallengeProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | java.lang.IllegalStateException | shouldEnableOplogEncryption | com.xgen.svc.nds.util.PitEncryptionUtil\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAllProcessesHealthy' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'createACMEOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | org.shredzone.acme4j.exception.AcmeLazyLoadingException | runIfReady | com.xgen.svc.mms.planner.model.Move\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to create virtual machine | performInternal | AbstractAzureCreateVirtualMachineStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'isRebootedWaitUptimeDecrease' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureResourceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error creating peering connection in resource group | processCreateConnectionData | AzureSyncPeeringConnectionMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Untracked Plan Failures dev\", \"frequency\": \"720\", \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}, {"CUSTOM": "{\"actionMatch\": \"all\", \"filterMatch\": \"all\", \"actions\": [{\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"id\": \"sentry.rules.conditions.regression_event.RegressionEventCondition\"}], \"filters\": [{\"match\": \"co\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"event_type\", \"value\": \"planner-failure\"}], \"name\": \"[Planner Failures] Regression Alert Handling\", \"frequency\": 60, \"environment\": \"dev\", \"owner\": \"team:4505517806583808\"}", "env": "dev"}], "internal": [{"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"15\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAutomationAgentIsStarted' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 15 in 15m (internal)\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to insufficient free private IP addresses in subnet. | handleAwsException | AWSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to reserve capacity exceeded error. | handleAwsException | AWSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEBSVolumeIsAvailable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The agent encountered an error while opening or querying the cursor for snapshot | performInternal | WaitForWtcSnapshotToReachState\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error creating peering connection in resource group | processCreateConnectionData | AzureSyncPeeringConnectionMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"ebsVolumeId not found | performSnapshotStep | AwsReplSetSnapshotter\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'createACMEOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureResourceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'isRebootedWaitUptimeDecrease' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEC2InstanceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'confirmHostIPIsReachable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'provisionVirtualMachine' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getUsedDiskSpace' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Retrying linearizable read job failed | performInternal | RunLinearizableReadStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handleNDSException | NDSMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to RESUME instance | execute | NDSSyncPauseStateExecutor\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Restore failed for cluster | performInternal | DoTenantToTenantRestoreMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalConfigVersion' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Last ping date not fresh. Validation did not complete. | performInternal | WaitForDataValidationCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForEbsSnapshotCompleted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to retrieve and save cert | performInternal | ACMEDownloadCertStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'CapacityReservationRequest' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeAutomationAgentRetry' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'verifyElasticIpExistAfterCreation' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"New cloud chef conf expired before it was fetched | performInternal | UpdateCloudConfStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Non-provisioned InstanceHardware with expected index for mtm | performInternal | FastFreeProvisionMachineMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'createDisk' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to setup some region groups for endpoint service | performInternal | GCPSyncPrivateServiceConnectRegionGroupMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Max number of regions for GCP container exceeded | performInternal | GCPCreateSubnetStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The push based migration step failed. | printResultLog | PushLiveImportMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveLoadBalancerProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'takeSnapshot' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'importSampleDataset' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"waited for a second ping but the MongoDB version or FCV changed | performInternal | WaitForPingVersionDataStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForKMSIPAccessListConfiguration' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error inserting tenant restore for M0 unpause. | insertTenantRestore | NDSInitiateTenantRestoreStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForClusterProvisioned' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'checkMongosyncProgress_kubeJob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getMongoMirrorHttpStatusForKube' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'startInstance' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 15m (internal)\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"10\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalStateException | getProcesses | com.xgen.svc.nds.planner.DoSnapshotRestoreMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForFileSystemResize' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import failed for all involved replica sets. | performInternal | WaitForLiveImportCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import for Mongosync failed on only replica set in unrestartable way | performInternal | WaitForMongosyncLiveImportCompleteStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No eligible hardware to take a snapshot from | pickInstanceIdToSnapshot | BackupSnapshotUtils\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed getting target hostname to snapshot. Will retry | performInternal | CpsReplSetSnapshotMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Shards topology is changing in the middle of a snapshot move, failing the snapshot. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"oplog too short despite multiple consecutive attempts to snapshot | createEBSSnapshot | Move\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForSecondPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 10 in 15m (internal)\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"20\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Authorization in an unexpected state. | ensureAuthorizationsInExpectedStates | ACMEPreAuthSteps\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | org.shredzone.acme4j.exception.AcmeLazyLoadingException | runIfReady | com.xgen.svc.mms.planner.model.Move\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAllProcessesHealthy' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForHostGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAccess' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handlePlanExecutionCloudProviderException | AWSMoveUtilityProvider\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status READY' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status VALID' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForCpsStreamingRestoreJobs' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 20 in 15m (internal)\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"20\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveChallengeProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 20 in 1h (internal)\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1h\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to create virtual machine | performInternal | AbstractAzureCreateVirtualMachineStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed rolling index build detected | cleanupFailedRollingIndexBuild | WaitForAutomationAgentsGoalStateStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForValidationFirstPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Validation completed with resource exhausted error. | isValidationComplete | Step\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to submit the given CSR | executeOrder | ACMEExecuteOrderStep\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | perform | NVMeRealHandler\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | performInternal | CpsShardedSnapshotMove\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 1h (internal)\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"1d\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForBackupTerminated' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to initialize PIT settings | syncPitSettings | SyncBackupSettingsMove\"}], \"name\": \"Tracked Plan Execution Failure - 5 in 1d (internal)\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"PLAN_EXECUTION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"500\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'hasLastBatchArrived' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForRestoreServerReady' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'backupCursorExtended' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Tracked Plan Execution Failure - 500 in 15m (internal)\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"15\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoSocketReadException\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoTimeoutException | createTimeoutException | com.mongodb.internal.connection.DefaultConnectionPool\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoQueryException\"}, {\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoCommandException\"}], \"name\": \"Tracked Plan Generation Failures - 15 in 15m (internal)\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"50\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | java.lang.IllegalStateException | shouldEnableOplogEncryption | com.xgen.svc.nds.util.PitEncryptionUtil\"}], \"name\": \"Tracked Plan Generation Failures - 50 in 15m (internal)\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"PLAN_GENERATION": "{\"actionMatch\": \"all\", \"filterMatch\": \"any\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"interval\": \"15m\", \"id\": \"sentry.rules.conditions.event_frequency.EventFrequencyCondition\", \"comparisonType\": \"count\", \"value\": \"5\"}], \"filters\": [{\"match\": \"sw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalArgumentException | initialize | com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\"}], \"name\": \"Tracked Plan Generation Failures - 5 in 15m (internal)\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"UNTRACKED_PLAN_FAILURE": "{\"actionMatch\": \"all\", \"filterMatch\": \"all\", \"actions\": [{\"workspace\": 69264, \"id\": \"sentry.integrations.slack.notify_action.SlackNotifyServiceAction\", \"channel_id\": \"C0582PZ6PU2\", \"channel\": \"sentry-planner-failures\"}, {\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [], \"filters\": [{\"match\": \"eq\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"event_type\", \"value\": \"planner-failure\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status READY' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'provisionVirtualMachine' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'Check ACME Order status VALID' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handleNDSException | NDSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import for Mongosync failed on only replica set in unrestartable way | performInternal | WaitForMongosyncLiveImportCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'confirmHostIPIsReachable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForBackupTerminated' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Retrying linearizable read job failed | performInternal | RunLinearizableReadStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getUsedDiskSpace' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'hasLastBatchArrived' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to insufficient free private IP addresses in subnet. | handleAwsException | AWSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'backupCursorExtended' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForRestoreServerReady' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to capacity error. | handlePlanExecutionCloudProviderException | AWSMoveUtilityProvider\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed rolling index build detected | cleanupFailedRollingIndexBuild | WaitForAutomationAgentsGoalStateStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEBSVolumeIsAvailable' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForEbsSnapshotCompleted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'importSampleDataset' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyEC2InstanceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan failed due to reserve capacity exceeded error. | handleAwsException | AWSMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'startInstance' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForGoalConfigVersion' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Restore failed for cluster | performInternal | DoTenantToTenantRestoreMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to retrieve and save cert | performInternal | ACMEDownloadCertStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'getMongoMirrorHttpStatusForKube' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The agent encountered an error while opening or querying the cursor for snapshot | performInternal | WaitForWtcSnapshotToReachState\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Last ping date not fresh. Validation did not complete. | performInternal | WaitForDataValidationCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"oplog too short despite multiple consecutive attempts to snapshot | createEBSSnapshot | Move\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Live import failed for all involved replica sets. | performInternal | WaitForLiveImportCompleteStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Shards topology is changing in the middle of a snapshot move, failing the snapshot. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Validation completed with resource exhausted error. | isValidationComplete | Step\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error while trying to submit the given CSR | executeOrder | ACMEExecuteOrderStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForValidationFirstPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Authorization in an unexpected state. | ensureAuthorizationsInExpectedStates | ACMEPreAuthSteps\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time | performInternal | ExecuteAutomationAgentJobStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to initialize PIT settings | syncPitSettings | SyncBackupSettingsMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'executeAutomationAgentRetry' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | performInternal | CpsShardedSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move. | perform | NVMeRealHandler\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForFileSystemResize' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForCpsStreamingRestoreJobs' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"New cloud chef conf expired before it was fetched | performInternal | UpdateCloudConfStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'createDisk' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Non-provisioned InstanceHardware with expected index for mtm | performInternal | FastFreeProvisionMachineMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"ebsVolumeId not found | performSnapshotStep | AwsReplSetSnapshotter\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForHostGoalState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'verifyElasticIpExistAfterCreation' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveLoadBalancerProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAccess' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"waited for a second ping but the MongoDB version or FCV changed | performInternal | WaitForPingVersionDataStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'CapacityReservationRequest' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Max number of regions for GCP container exceeded | performInternal | GCPCreateSubnetStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No eligible hardware to take a snapshot from | pickInstanceIdToSnapshot | BackupSnapshotUtils\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to setup some region groups for endpoint service | performInternal | GCPSyncPrivateServiceConnectRegionGroupMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForKMSIPAccessListConfiguration' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAutomationAgentIsStarted' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed getting target hostname to snapshot. Will retry | performInternal | CpsReplSetSnapshotMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"The push based migration step failed. | printResultLog | PushLiveImportMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'takeSnapshot' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error inserting tenant restore for M0 unpause. | insertTenantRestore | NDSInitiateTenantRestoreStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'checkMongosyncProgress_kubeJob' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForClusterProvisioned' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForSecondPing' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalStateException | getProcesses | com.xgen.svc.nds.planner.DoSnapshotRestoreMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoCommandException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to RESUME instance | execute | NDSSyncPauseStateExecutor\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoTimeoutException | createTimeoutException | com.mongodb.internal.connection.DefaultConnectionPool\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoSocketReadException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | java.lang.IllegalArgumentException | initialize | com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | com.mongodb.MongoQueryException\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'reserveChallengeProcessingRights' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Plan Generation Failure | doPlanning | NDSPlanningSvc | java.lang.IllegalStateException | shouldEnableOplogEncryption | com.xgen.svc.nds.util.PitEncryptionUtil\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'waitForAllProcessesHealthy' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureVirtualMachineIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"No attempts remaining for 'createACMEOrder' | evaluateAttempt | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Don't know how to handle exception; rethrowing | handleException | Move | org.shredzone.acme4j.exception.AcmeLazyLoadingException | runIfReady | com.xgen.svc.mms.planner.model.Move\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Failed to create virtual machine | performInternal | AbstractAzureCreateVirtualMachineStep\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'isRebootedWaitUptimeDecrease' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyAzureResourceIsInState' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'verifyInstanceSizeUpdate' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired | hasAttemptTimePassed | EvaluateAttempts\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Error creating peering connection in resource group | processCreateConnectionData | AzureSyncPeeringConnectionMove\"}, {\"match\": \"nsw\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"alert_rule_filter\", \"value\": \"Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts\"}], \"name\": \"Untracked Plan Failures internal\", \"frequency\": \"720\", \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}, {"CUSTOM": "{\"actionMatch\": \"all\", \"filterMatch\": \"all\", \"actions\": [{\"service\": \"mms-atlas-planner-87080b\", \"id\": \"sentry.rules.actions.notify_event_service.NotifyEventServiceAction\"}], \"conditions\": [{\"id\": \"sentry.rules.conditions.regression_event.RegressionEventCondition\"}], \"filters\": [{\"match\": \"co\", \"id\": \"sentry.rules.filters.tagged_event.TaggedEventFilter\", \"key\": \"event_type\", \"value\": \"planner-failure\"}], \"name\": \"[Planner Failures] Regression Alert Handling\", \"frequency\": 60, \"environment\": \"internal\", \"owner\": \"team:4505517806583808\"}", "env": "internal"}]}