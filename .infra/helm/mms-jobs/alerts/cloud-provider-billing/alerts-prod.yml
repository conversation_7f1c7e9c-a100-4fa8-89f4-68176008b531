---
# yamllint disable rule:quoted-strings
prometheusRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: import_jobs_not_running
      rules:
        - alert: AWS Import Job Not Running PROD for last 24 hours
          expr: time() - max(cloud_provider_job_finish_time_seconds{namespace = "core-systems-prod", job_type="IMPORT", cloud_provider="AWS"} > 0) > 86400
          labels:
            severity: warning
          annotations:
            summary: AWS Import Job is not running in PROD
            description: "AWS Import Job isn't running in PROD"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=8'
        - alert: GCP Import Job Not Running PROD for last 24 hours
          expr: time() - max(cloud_provider_job_finish_time_seconds{namespace = "core-systems-prod", job_type="IMPORT", cloud_provider="GCP"} > 0) > 86400
          labels:
            severity: warning
          annotations:
            summary: GCP Import Job is not running in PROD
            description: "GCP Import Job isn't running in PROD"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=21'
        - alert: Azure Import Job Not Running PROD for last 24 hours
          expr: sum(rate(billing_import_azure_unaggregated_job_runtime_seconds_count{xgen_environment="prod"}[1h])) == 0
          for: 24h
          labels:
            severity: warning
          annotations:
            summary: Azure Import Job is not running in PROD
            description: "Azure Import Job isn't running in PROD"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=189'
        - alert: Azure Aggregation Job Not Running PROD for last 24 hours
          expr: sum(rate(billing_import_azure_aggregate_job_runtime_seconds_count{xgen_environment="prod"}[1h])) == 0
          for: 24h
          labels:
            severity: warning
          annotations:
            summary: Azure Import Job is not running in PROD
            description: "Azure Import Job isn't running in PROD"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=190'
    - name: import_jobs_taking_long_time_to_run
      rules:
        - alert: AWS Import Taking Long Time to Run
          expr: sum(increase(billing_aws_usage_import_job_duration_seconds_sum{xgen_environment="prod", namespace = "core-systems-prod"}[1h])) > 10800
          labels:
            severity: warning
          annotations:
            summary: AWS import job taking more than 3 hours to run
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=8'
        - alert: GCP Import Taking Long Time to Run
          expr: sum(increase(billing_gcp_bigquery_snapshot_import_svc_dur_seconds_sum{xgen_environment="prod", namespace = "core-systems-prod"}[1h])) > 1200
          labels:
            severity: warning
          annotations:
            summary: GCP import job taking more than 10 minutes to run
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=21'
        - alert: Azure Import Taking Long Time to Run
          expr: max(increase(billing_import_azure_unaggregated_job_runtime_seconds_sum{xgen_environment="prod", namespace = "core-systems-prod"}[1h])) > 8100
          labels:
            severity: warning
          annotations:
            summary: Azure unaggregated import job taking more than 1 hour to run
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=189'
        - alert: Azure Aggregation Taking Long Time to Run
          expr: max(increase(billing_import_azure_aggregate_job_runtime_seconds_sum{xgen_environment="prod", namespace = "core-systems-prod"}[1h])) > 10800
          labels:
            severity: warning
          annotations:
            summary: Azure aggregation job taking more than 3 hours to run
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=190'
        - alert: Azure Manifest is Invalid on Last Day of Month
          expr: sum(increase(azure_billing_import_manifest_invalid_total{xgen_environment="prod", namespace="core-systems-prod", isLastDayOfMonth="true"}[1h])) > 0
          labels:
            severity: warning
          annotations:
            summary: "Azure manifest file is invalid on the last day of the month"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=6'
            repeat_interval: 24h # Alert will not fire again until 24 hours pass
    - name: metering_submission_jobs_not_running
      rules:
        - alert: AWS Submission Job Not Running PROD for last 24 hours
          expr: sum(rate(billing_submission_svc_duration_seconds_count{xgen_environment="prod", class="com.xgen.cloud.billingimport.aws._public.svc.AwsMeterSubmissionSvc", operation="exportUsageToBilling"}[1h])) == 0
          for: 24h
          labels:
            severity: warning
          annotations:
            summary: AWS Submission Job is not running in PROD
            description: "AWS Submission Job isn't running in PROD"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=4'
        - alert: Azure Submission Job Not Running PROD for last 24 hours
          expr: sum(rate(billing_submission_svc_duration_seconds_count{xgen_environment="prod", class="com.xgen.cloud.billingimport.meteringclient.datatransfer.azure._private.svc.AzureDataTransferMeterSubmissionSvc", operation="exportUsageToBilling"}[1h])) == 0
          for: 24h
          labels:
            severity: warning
          annotations:
            summary: Azure Submission Job is not running in PROD
            description: "Azure Submission Job isn't running in PROD"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=20'
        - alert: GCP Submission Job Not Running PROD for last 24 hours
          expr: sum(rate(billing_submission_svc_duration_seconds_count{xgen_environment="prod", class="com.xgen.cloud.billingimport.meteringclient.datatransfer.gcp._private.svc.GcpDataTransferMeterSubmissionSvc", operation="exportUsageToBilling"}[1h])) == 0
          for: 24h
          labels:
            severity: warning
          annotations:
            summary: GCP Submission Job is not running in PROD
            description: "GCP Submission Job isn't running in PROD"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=12'
    - name: metering_submission_jobs_taking_long_time_to_run
      rules:
        - alert: AWS Metering Submission Taking Long Time to Run
          expr: sum(increase(billing_submission_svc_duration_seconds_sum{xgen_environment="prod", namespace="core-systems-prod", operation="exportUsageToBilling", class="com.xgen.cloud.billingimport.aws._public.svc.AwsMeterSubmissionSvc"}[1m])) > 5400
          labels:
            severity: warning
          annotations:
            summary: AWS metering submission job taking more than 1.5 hours to run
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=4'
        - alert: Azure Metering Submission Taking Long Time to Run
          expr: sum(increase(billing_submission_svc_duration_seconds_sum{xgen_environment="prod", namespace="core-systems-prod", operation="exportUsageToBilling", class="com.xgen.cloud.billingimport.meteringclient.datatransfer.azure._private.svc.AzureDataTransferMeterSubmissionSvc"}[1m])) > 3600
          labels:
            severity: warning
          annotations:
            summary: Azure metering submission job taking more than 1 hour to run
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=20'
        - alert: GCP Metering Submission Taking Long Time to Run
          expr: sum(increase(billing_submission_svc_duration_seconds_sum{xgen_environment="prod", namespace="core-systems-prod", operation="exportUsageToBilling", class="com.xgen.cloud.billingimport.meteringclient.datatransfer.gcp._private.svc.GcpDataTransferMeterSubmissionSvc"}[1m])) > 3600
          labels:
            severity: warning
          annotations:
            summary: GCP metering submission job taking more than 1 hour to run
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=12'
    - name: cps_submission_jobs_not_running
      rules:
        - alert: AWS CPS Submission Job Not Running PROD for last 24 hours
          expr: sum(rate(billing_aws_snapshot_import_svc_dur_seconds_sum{xgen_environment="prod", action="findAggregatedUsage"}[1h])) == 0
          for: 24h
          labels:
            severity: warning
          annotations:
            summary: AWS CPS Submission Job is not running in PROD
            description: "AWS Submission Job isn't running in PROD"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=23'
        - alert: Azure CPS Submission Job Not Running PROD for last 24 hours
          expr: sum(rate(billing_azure_snapshot_import_svc_dur_seconds_sum{xgen_environment="prod", action="findAggregatedUsage"}[1h])) == 0
          for: 24h
          labels:
            severity: warning
          annotations:
            summary: Azure CPS Submission Job is not running in PROD
            description: "Azure Submission Job isn't running in PROD"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=24'
        - alert: GCP CPS Submission Job Not Running PROD for last 24 hours
          expr: sum(rate(billing_gcp_snapshot_import_svc_dur_seconds_sum{xgen_environment="prod", action="findAggregatedUsage"}[1h])) == 0
          for: 24h
          labels:
            severity: warning
          annotations:
            summary: GCP CPS Submission Job is not running in PROD
            description: "GCP Submission Job isn't running in PROD"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=25'
    - name: cps_submission_jobs_taking_long_time_to_run
      rules:
        - alert: AWS CPS Submission Taking Long Time to Run
          expr: sum(increase(billing_aws_snapshot_import_svc_dur_seconds_sum{xgen_environment="prod", namespace="core-systems-prod", action="findAggregatedUsage"}[1m])) > 3600
          labels:
            severity: warning
          annotations:
            summary: AWS CPS submission job taking more than 1 hour to run
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=23'
        - alert: Azure CPS Submission Taking Long Time to Run
          expr: sum(increase(billing_azure_snapshot_import_svc_dur_seconds_sum{xgen_environment="prod", namespace="core-systems-prod", action="findAggregatedUsage"}[1m])) > 3600
          labels:
            severity: warning
          annotations:
            summary: Azure CPS submission job taking more than 1 hour to run
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=24'
        - alert: GCP CPS Submission Taking Long Time to Run
          expr: sum(increase(billing_gcp_snapshot_import_svc_dur_seconds_sum{xgen_environment="prod", namespace="core-systems-prod", action="findAggregatedUsage"}[1m])) > 3600
          labels:
            severity: warning
          annotations:
            summary: GCP CPS submission job taking more than 1 hour to run
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=25'
    - name: cloud_provider_business_slos
      rules:
        - alert: AWS Import Job High Download Latency in PROD
          expr: max(max_over_time(cloud_provider_download_latency_seconds{xgen_environment="prod", cloud_provider="AWS"}[24h])) > (3600 * 20)
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: "AWS Import Job High Download Latency PROD greater than 20 hours"
            description: "More than 5% of AWS Import jobs are taking longer than 20 hours to trigger."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=25"
        - alert: AWS Import Job Low Service Reliability in PROD
          expr: (sum(increase(cloud_provider_service_reliability_total{xgen_environment="prod", cloud_provider="AWS", status="FAILED"}[24h])) / sum(increase(cloud_provider_service_reliability_total{xgen_environment="prod", cloud_provider="AWS", status=~"SUCCESSFUL|FAILED"}[24h]))) * 100 > 1
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: "AWS Import Job Low Service Reliability PROD for last 1 hour"
            description: "Less than 99% of scheduled AWS import jobs are completing on time."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=25"
        - alert: AWS Data Validation Low Success Rate in PROD
          expr: (sum(increase(cloud_provider_data_validation_success_rate_total{xgen_environment="prod", cloud_provider="AWS", status="FAILED"}[24h])) / sum(increase(cloud_provider_data_validation_success_rate_total{xgen_environment="prod", cloud_provider="AWS", status=~"SUCCESSFUL|FAILED"}[24h]))) * 100 > 0.1
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: "AWS Data Validation Low Success Rate PROD for last 24 hours"
            description: "More than 0.1% of AWS Auditors are failing validation (SLO: ≥ 99.9% success rate)."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=25"
        - alert: AZURE Import Job High Download Latency in PROD
          expr: max(max_over_time(cloud_provider_download_latency_seconds{xgen_environment="prod", cloud_provider="AZURE"}[24h])) > (3600 * 20)
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: "AZURE Import Job High Download Latency PROD greater than 20 hours"
            description: "More than 5% of AZURE Import jobs are taking longer than 20 hours to trigger."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=25"
        - alert: AZURE Import Job Low Service Reliability in PROD
          expr: (sum(increase(cloud_provider_service_reliability_total{xgen_environment="prod", cloud_provider="AZURE", status="FAILED"}[24h])) / sum(increase(cloud_provider_service_reliability_total{xgen_environment="prod", cloud_provider="AZURE", status=~"SUCCESSFUL|FAILED"}[24h]))) * 100 > 1
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: "AZURE Import Job Low Service Reliability PROD for last 1 hour"
            description: "Less than 99% of scheduled AZURE import jobs are completing on time."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=25"
        - alert: AZURE Data Validation Low Success Rate in PROD
          expr: (sum(increase(cloud_provider_data_validation_success_rate_total{xgen_environment="prod", cloud_provider="AZURE", status="FAILED"}[24h])) / sum(increase(cloud_provider_data_validation_success_rate_total{xgen_environment="prod", cloud_provider="AZURE", status=~"SUCCESSFUL|FAILED"}[24h]))) * 100 > 0.1
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: "AZURE Data Validation Low Success Rate PROD for last 24 hours"
            description: "More than 0.1% of AZURE Auditors are failing validation (SLO: ≥ 99.9% success rate)."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=25"
        - alert: GCP Import Job High Download Latency in PROD
          expr: max(max_over_time(cloud_provider_download_latency_seconds{xgen_environment="prod", cloud_provider="GCP"}[24h])) > (3600 * 20)
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: "GCP Import Job High Download Latency PROD for last 20 hours"
            description: "More than 5% of GCP Import jobs are taking longer than 20 hours to trigger."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=25"
        - alert: GCP Import Job Low Service Reliability in PROD
          expr: (sum(increase(cloud_provider_service_reliability_total{xgen_environment="prod", cloud_provider="GCP", status="FAILED"}[24h])) / sum(increase(cloud_provider_service_reliability_total{xgen_environment="prod", cloud_provider="GCP", status=~"SUCCESSFUL|FAILED"}[24h]))) * 100 > 1
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: "GCP Import Job Low Service Reliability PROD for last 1 hour"
            description: "Less than 99% of scheduled GCP import jobs are completing on time."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=25"
        - alert: GCP Data Validation Low Success Rate in PROD
          expr: (sum(increase(cloud_provider_data_validation_success_rate_total{xgen_environment="prod", cloud_provider="GCP", status="FAILED"}[24h])) / sum(increase(cloud_provider_data_validation_success_rate_total{xgen_environment="prod", cloud_provider="GCP", status=~"SUCCESSFUL|FAILED"}[24h]))) * 100 > 0.1
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: "GCP Data Validation Low Success Rate PROD for last 24 hours"
            description: "More than 0.1% of GCP Auditors are failing validation (SLO: ≥ 99.9% success rate)."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now&viewPanel=25"
    - name: cloud_provider_database_slos
      rules:
        - alert: Low Disk Space Free - Database
          expr: |
            sum(
              hardware_disk_metrics_disk_space_free_bytes{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"}
            ) by (instance) /
            sum(
              hardware_disk_metrics_disk_space_used_bytes{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"} +
              hardware_disk_metrics_disk_space_free_bytes{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"}
            ) by (instance) * 100 < 20
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Low Disk Space Free on {{ $labels.instance }}"
            description: "Disk space on {{ $labels.instance }} has been below 20% free for more than 5 minutes. Storage usage exceeds 80%."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now"
        - alert: High CPU Usage (User Mode) - Database
          expr: |
            sum(
              rate(hardware_system_cpu_user_milliseconds{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"}[5m])
              / (1000 * hardware_platform_num_logical_cpus{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"})
            ) by (instance) * 100 > 80
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High CPU Usage (User Mode) on {{ $labels.instance }}"
            description: "CPU utilization (User Mode) on {{ $labels.instance }} has exceeded 80% for over 5 minutes."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now"
        - alert: High System Memory Utilization - Database
          expr: |
            avg(
            (hardware_system_memory_mem_total_kilobytes{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"}
            - hardware_system_memory_mem_available_kilobytes{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"})
            /
            hardware_system_memory_mem_total_kilobytes{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"}
            ) by (instance) * 100 > 80
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High Memory Utilization on {{ $labels.instance }}"
            description: "Memory utilization on {{ $labels.instance }} has exceeded 80% for over a 5 minute rolling average window."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now"
        - alert: High Network Throughput - Database
          expr: |
            sum(
              irate(mongodb_network_bytesIn{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"}[5m]) +
              irate(mongodb_network_bytesOut{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"}[5m])
            ) by (instance) / ********** * 100 > 20
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Network Saturation Alert"
            description: "The network throughput for instance {{ $labels.instance }} is exceeding 80% of the available bandwidth (1.25 GB/sec)."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now"
        - alert: High Disk IOPS Utilization - Database
          expr: |
            sum(rate(hardware_disk_metrics_read_count{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"}[5m])) by (instance) / 825000 +
            sum(rate(hardware_disk_metrics_write_count{xgen_environment="prod", group_id=~"66155617e6bcd1388cd4176c", instance=~"$host.*"}[5m])) by (instance) / 360000
            * 100 > 15
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Disk IOPS Utilization Over 80%"
            description: "Disk IOPS utilization for instance {{ $labels.instance }} exceeds 15% of the assumed IOPS capacity."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now"
    - name: cloud_provider_application_slos
      rules:
        - alert: High CPU Utilization - Application
          expr: |
            (
              sum(rate(container_cpu_usage_seconds_total{job=~"kubelet|kata-collector", xgen_environment="prod", namespace="core-systems-prod", container="mms-jobs-cloud-provider-billing", xgen_provider="aws"}[1m]))
              by (xgen_kube_cluster, pod, container)
              /
              sum(container_spec_cpu_quota{job=~"kubelet|kata-collector", xgen_environment="prod", namespace="core-systems-prod", container="mms-jobs-cloud-provider-billing", xgen_provider="aws"} / 100000)
              by (xgen_kube_cluster, pod, container)
            ) * 100 > 70
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High CPU Usage on {{ $labels.pod }}"
            description: "CPU utilization for pod {{ $labels.pod }} in cluster {{ $labels.xgen_kube_cluster }} has exceeded 70% for more than 5 minutes."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now"
        - alert: Low Available Memory - Application
          expr: |
            (
              1 - (
                sum(container_memory_working_set_bytes{job=~"kubelet|kata-collector", xgen_environment="prod", namespace="core-systems-prod", container="mms-jobs-cloud-provider-billing", xgen_provider="aws"})
                by (xgen_kube_cluster, pod, container)
                /
                sum(container_spec_memory_limit_bytes{job=~"kubelet|kata-collector", xgen_environment="prod", namespace="core-systems-prod", container="mms-jobs-cloud-provider-billing", xgen_provider="aws"})
                by (xgen_kube_cluster, pod, container)
              )
            ) * 100 < 20
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Low Available Memory on {{ $labels.pod }}"
            description: "Available memory for pod {{ $labels.pod }} in cluster {{ $labels.xgen_kube_cluster }} has dropped below 20% for over 5 minutes."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now"
        - alert: High Network Throughput - Application
          expr: |
            (
              (sum(
                rate(container_network_receive_bytes_total{job="kubelet", xgen_environment="prod", namespace="core-systems-prod", xgen_provider="aws", pod=~"mms-jobs-cloud-provider-billing.*"}[5m])
                +
                rate(container_network_transmit_bytes_total{job="kubelet", xgen_environment="prod", namespace="core-systems-prod", xgen_provider="aws", pod=~"mms-jobs-cloud-provider-billing.*"}[5m])
              ) by (xgen_kube_cluster, pod))
              /
              (1.25 * 10^9)
            ) * 100 > 50
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High Network Throughput on {{ $labels.pod }}"
            description: "Network throughput for pod {{ $labels.pod }} in cluster {{ $labels.xgen_kube_cluster }} has exceeded 50% of the assumed 1.25 Gbps total bandwidth for over 5 minutes."
            runbook_url: "https://docs.google.com/document/d/1kVu6cNVAaEpnTd_pdymqhjz6rYBMae8KQVPGp9MRUo4/edit?tab=t.0#heading=h.o3lxceqy8veq"
            grafana_url: "https://grafana.corp.mongodb.com/d/0A9ya-97k/cloud-provider-imports-import-and-submission-metrics?orgId=20&from=now-30d&to=now"
    - name: gcp_import_business_logic_issues
      rules:
        - alert: GCP Unknown SKU Detected
          expr: sum(increase(gcp_usage_import_unknown_sku_total[48h])) by (sku_id, usage_type, component) > 0
          labels:
            severity: warning
          annotations:
            summary: "Unknown GCP SKU detected: {{ $labels.sku_id }}"
            description: "Unknown SKU {{ $labels.sku_id }} encountered in {{ $labels.usage_type }} import by {{ $labels.component }}. This may indicate new GCP services or SKUs that need to be added to the metadata configuration."
            runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/219001854/Help+Rotation+Guidance+for+GCP+Import+Using+BigQuery#HelpRotationGuidanceforGCPImportUsingBigQuery-AddingNewRegions'
            grafana_url: 'https://grafana.helix.corp.mongodb.com/goto/6WRScT8Ng?orgId=20'
    - name: Metering Database Alerts
      rules:
        - alert: IAMeteringHighReadExecutionTime
          expr: |
            mongodb_opLatencies_reads_latency{
              cl_name="ia-prod-metering",
              group_name="Prod - Billing"
            } > 5000
          for: 5m
          labels:
            severity: warning
            cluster: "ia-prod-metering"
            service: metering
          annotations:
            summary: "IA Metering cluster has high read execution time"
            description: "Average read execution time is {{ $value }}ms on cluster {{ $labels.cl_name }}"

        - alert: IAMeteringHighWriteExecutionTime
          expr: |
            mongodb_opLatencies_writes_latency{
              cl_name="ia-prod-metering",
              group_name="Prod - Billing"
            } > 3000
          for: 5m
          labels:
            severity: warning
            cluster: "ia-prod-metering"
            service: metering
          annotations:
            summary: "IA Metering cluster has high write execution time"
            description: "Average write execution time is {{ $value }}ms on cluster {{ $labels.cl_name }}"

        - alert: IAMeteringDataPartitionDiskSpaceHigh
          expr: |
            (
              hardware_disk_metrics_disk_space_used_bytes{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing",
                disk_name=~".*data.*"
              } /
              (hardware_disk_metrics_disk_space_used_bytes{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing",
                disk_name=~".*data.*"
              } + hardware_disk_metrics_disk_space_free_bytes{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing",
                disk_name=~".*data.*"
              })
            ) * 100 > 80
          for: 5m
          labels:
            severity: warning
            cluster: "ia-prod-metering"
            service: metering
          annotations:
            summary: "IA Metering cluster data partition disk usage is high"
            description: "Data partition disk usage is {{ $value | humanizePercentage }} on cluster {{ $labels.cl_name }}"

        - alert: IAMeteringIndexPartitionDiskSpaceHigh
          expr: |
            (
              hardware_disk_metrics_disk_space_used_bytes{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing",
                disk_name=~".*index.*"
              } /
              (hardware_disk_metrics_disk_space_used_bytes{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing",
                disk_name=~".*index.*"
              } + hardware_disk_metrics_disk_space_free_bytes{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing",
                disk_name=~".*index.*"
              })
            ) * 100 > 80
          for: 5m
          labels:
            severity: warning
            cluster: "ia-prod-metering"
            service: metering
          annotations:
            summary: "IA Metering cluster index partition disk usage is high"
            description: "Index partition disk usage is {{ $value | humanizePercentage }} on cluster {{ $labels.cl_name }}"

        - alert: IAMeteringHighMemoryUsage
          expr: |
            (
              (hardware_system_memory_mem_total_kilobytes{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing"
              } - hardware_system_memory_mem_available_kilobytes{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing"
              }) / hardware_system_memory_mem_total_kilobytes{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing"
              }
            ) * 100 > 80
          for: 5m
          labels:
            severity: warning
            cluster: "ia-prod-metering"
            service: metering
          annotations:
            summary: "IA Metering cluster has high memory usage"
            description: "Memory usage is {{ $value | humanizePercentage }} on cluster {{ $labels.cl_name }}"

        - alert: IAMeteringHighCPUUsage
          expr: |
            (
              rate(hardware_system_cpu_user_milliseconds{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing"
              }[5m]) / 10
            ) > 90
          for: 5m
          labels:
            severity: warning
            cluster: "ia-prod-metering"
            service: metering
          annotations:
            summary: "IA Metering cluster has high CPU usage"
            description: "CPU user usage is {{ $value }}% on cluster {{ $labels.cl_name }}"

        - alert: IAMeteringHighConnectionsUsage
          expr: |
            (
              mongodb_connections_current{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing"
              } / mongodb_connections_available{
                cl_name="ia-prod-metering",
                group_name="Prod - Billing"
              }
            ) * 100 > 80
          for: 10m
          labels:
            severity: warning
            cluster: "ia-prod-metering"
            service: metering
          annotations:
            summary: "IA Metering cluster has high connections usage"
            description: "Connections usage is {{ $value | humanizePercentage }} on cluster {{ $labels.cl_name }}"

        - alert: IAMeteringHighCommandExecutionTime
          expr: |
            mongodb_opLatencies_commands_latency{
              cl_name="ia-prod-metering",
              group_name="Prod - Billing"
            } > 5
          for: 5m
          labels:
            severity: warning
            cluster: "ia-prod-metering"
            service: metering
          annotations:
            summary: "IA Metering cluster has high command execution time"
            description: "Average command execution time is {{ $value }}ms on cluster {{ $labels.cl_name }}"
