---
# yamllint disable rule:quoted-strings
pagerdutyService:
  enabled: true
  name: mms-metering-team
  fullnameOverride: mms-metering-team
  escalationPolicyName: mms-metering-team
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
prometheusRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: metering_service_kube_resource_utilization
      rules:
        - alert: Metering Service CPU Throttling
          expr: (sum by(xgen_kube_cluster, container, pod) (increase(container_cpu_cfs_throttled_periods_total{job="kubelet",xgen_environment="prod",container="mms-metering"}[1m]))/sum by(xgen_kube_cluster, container, pod) (increase(container_cpu_cfs_periods_total{job="kubelet",xgen_environment="prod",container=~"mms-metering"}[1m]))) * 100 > 1.0
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Metering Service CPU throttling rate above 1%
            description: "Metering service CPU throttling rate has gone above threshold for 5 minutes in prod. Current throttle rate: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/1KmB4TlIz?orgId=20'
        - alert: Metering Service CPU Throttling - NON-PROD
          expr: (sum by(xgen_kube_cluster, container, pod) (increase(container_cpu_cfs_throttled_periods_total{job="kubelet",xgen_environment=~"dev|qa|staging",container="mms-metering"}[1m]))/sum by(xgen_kube_cluster, container, pod) (increase(container_cpu_cfs_periods_total{job="kubelet",xgen_environment=~"dev|qa|staging",container=~"mms-metering"}[1m]))) * 100> 1.0
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Metering Service CPU throttling rate above 1%
            description: "Metering service CPU throttling rate has gone above threshold for 5 minutes in {{ $labels.xgen_environment }}. Current throttle rate: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/abqUHTlIz?orgId=20'
        - alert: Metering Service Memory Utilization High
          expr: (100 * (container_memory_usage_bytes{container="mms-metering", xgen_environment="prod"} / container_spec_memory_limit_bytes{container="mms-metering", xgen_environment="prod"})) > 90
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Metering Service Memory Utilization Above 90%
            description: "Metering service memory utilization has gone above threshold for 5 minutes in prod. Current memory utilization: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/CvGISo_Sk?orgId=20'
        - alert: Metering Service Memory Utilization High - NON-PROD
          expr: (100 * (container_memory_usage_bytes{container="mms-metering", xgen_environment=~"(dev|qa|staging)"} / container_spec_memory_limit_bytes{container="mms-metering", xgen_environment=~"(dev|qa|staging)"})) > 90
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Metering Service Memory Utilization Above 90%
            description: "Metering service memory utilization has gone above threshold for 5 minutes in {{ $labels.xgen_environment }}. Current memory utilization: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/AZeNSolIk?orgId=20'
        - alert: Metering Service Heap Utilization High
          expr: jvm_memory_bytes_used{xgen_app="mms-metering",xgen_environment="prod", area="heap"} / jvm_memory_bytes_max * 100 > 90
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Metering Service JVM Heap Utilization Above 90%
            description: "Metering service heap utilization has gone above threshold for 5 minutes in prod. Current heap utilization: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/xwLpIT_Sk?orgId=20'
        - alert: Metering Service Heap Utilization High - NON-PROD
          expr: jvm_memory_bytes_used{xgen_app="mms-metering",xgen_environment=~"(dev|qa|staging)", area="heap"} / jvm_memory_bytes_max * 100 > 90
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Metering Service JVM Heap Utilization Above 90%
            description: "Metering service heap utilization has gone above threshold for 5 minutes in {{ $labels.xgen_environment }}. Current heap utilization: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/dacoSTlSz?orgId=20'
    - name: metering_service_kube_deployments
      rules:
        - alert: Number of ready pods in mms namespace is less than 50% for 10 minutes (Staging, Prod)
          expr: (kube_deployment_status_replicas_ready{job="kube-state-metrics", namespace=~"mms.*", xgen_environment=~"(staging|prod)", deployment="mms-metering"} / kube_deployment_status_replicas) < 0.50
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: 'Number of { $labels.deployment }} ready pods is less than 50% for 10 minutes ({{ $labels.xgen_environment }},{{ $labels.xgen_region }})'
            description: 'Number of { $labels.deployment }} ready pods is less than 50% for 10 minutes ({{ $labels.xgen_environment }},{{ $labels.xgen_region }})'
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/MMS+in+Helix+Runbook
            affected_hosts_com: https://victoria-metrics.corp.mongodb.com/select/0/vmui/#/?g0.expr=(kube_deployment_status_replicas_ready%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22(staging%7Cprod)%22,%20deployment%3D%22mms-metering%22%7D%20/%20kube_deployment_status_replicas)%20%3C%200.50&g0.tab=0&g0.stacked=0&g0.range_input=6h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            affected_hosts_gov: https://thanos.corp.mongodbgov.com/graph?g0.expr=(kube_deployment_status_replicas_ready%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22prod-gov%22,%20deployment%3D%22mms-metering%22%7D%20/%20kube_deployment_status_replicas)%20%3C%200.50&g0.tab=0&g0.stacked=0&g0.range_input=6h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            impact: Mms kube service is available with limited capacity.
        - alert: Kube pod is not ready in mms namespace for 30 minutes (Staging, Prod)
          expr: kube_pod_status_ready{job="kube-state-metrics", condition!="true", pod=~"mms-metering.*", namespace=~"mms.*", xgen_environment=~"(staging|prod)"} > 0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: The pod {{ $labels.pod }} has been in a non-ready state for more than than 30 minutes ({{ $labels.xgen_environment }}, {{ $labels.xgen_region }})
            description: The pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has been in a non-ready state for longer than 30 minutes.
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/MMS+in+Helix+Runbook
            affected_hosts_com: https://victoria-metrics.corp.mongodb.com/select/0/vmui/#/?g0.expr=kube_pod_status_ready%7Bjob%3D%22kube-state-metrics%22,%20condition!%3D%22true%22,%20pod%3D~%22mms-metering.*%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22(staging%7Cprod)%22%7D%20%3E%200&g0.tab=0&g0.stacked=0&g0.range_input=6h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            affected_hosts_gov: https://thanos.corp.mongodbgov.com/graph?g0.expr=kube_pod_status_ready%7Bjob%3D%22kube-state-metrics%22,%20condition!%3D%22true%22,%20pod%3D~%22mms-metering.*%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22prod-gov%22%7D%20%3E%200&g0.tab=0&g0.stacked=0&g0.range_input=6h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            impact: Some mms pods are down. The service is running with limited capacity.
        - alert: CrashLoopBackOff
          expr: kube_pod_container_status_waiting_reason{reason="CrashLoopBackOff", namespace=~"mms-.*", xgen_environment=~"(staging|prod)", pod=~"mms-metering.+"} > bool 0
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: "{{ $value }} MMS kube pods in {{ $labels.xgen_environment }} are failing to start during the deployment."
            description: |
              The condition indicates that kube pods in {{ $labels.xgen_environment }} environment are unable to start
              during the deployment. It usually means that some secrets aren't there or app startup is broken. MMS logs
              might be the best source for information about the failure.
            runbook: https://wiki.corp.mongodb.com/display/MMS/MMS+in+Helix+Runbook
            affected_hosts_com: https://victoria-metrics.corp.mongodb.com/select/0/vmui/#/?g0.expr=kube_pod_container_status_waiting_reason%7Breason%3D"CrashLoopBackOff"%2C%20namespace%3D~"mms-.*"%2C%20xgen_environment%3D~"(staging%7Cprod)"%7D%20>%20bool%200&g0.tab=1&g0.stacked=0&g0.show_exemplars=0&g0.range_input=1h
            affected_hosts_gov: https://thanos.corp.mongodbgov.com/graph?g0.expr=kube_pod_container_status_waiting_reason%7Breason%3D"CrashLoopBackOff"%2C%20namespace%3D~"mms-.*"%2C%20xgen_environment%3D~"prod-gov"%7D%20>%20bool%200&g0.tab=1&g0.stacked=0&g0.show_exemplars=0&g0.range_input=1h
            splunk_search_com_staging: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3D%22mms-staging%22%20kube.namespace_name%20=%20%22mms-*%22&display.page.search.mode=smart&dispatch.sample_ratio=1&workload_pool=&earliest=-60m%40m&latest=now&sid=1674661035.10438130
            splunk_search_com_prod: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3D%22mms-prod%22%20kube.namespace_name%20=%20%22mms-*%22&display.page.search.mode=smart&dispatch.sample_ratio=1&workload_pool=&earliest=-60m%40m&latest=now&sid=1674661063.10438132
            spinnaker_cluster_staging: https://spinnaker.corp.mongodb.com/#/applications/xgen-service/clusters?reg=mms-staging,mms-regional-staging
            spinnaker_cluster_prod: https://spinnaker.corp.mongodb.com/#/applications/xgen-service/clusters?reg=mms-prod,mms-regional-prod
            impact: Some services are down. Multiple versions of the code may be running at the same time.
    - name: metering_service_latency
      rules:
        - alert: Metering Service POST p90 Latency Alarm - PROD
          expr: (1000 * histogram_quantile(0.90, sum(rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path=~"/api/private/metering/usage", method="POST"}[5m])) by (le))) > 12000
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Metering Service POST P90 Latency Exceeded Threshold
            description: "Metering service POST latency has exceeded threshold for 5 minutes in prod. Current value: {{ $value }} milliseconds"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/o2zBUh_Sz?orgId=20'
        - alert: Metering Service POST p99 Latency Alarm - PROD
          expr: (1000 * histogram_quantile(0.99, sum(rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path=~"/api/private/metering/usage", method="POST"}[5m])) by (le))) > 30000
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Metering Service POST P99 Latency Exceeded Threshold
            description: "Metering service POST latency has exceeded threshold for 5 minutes in prod. Current value: {{ $value }} milliseconds"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/1EFmZZIHz?orgId=20'
        - alert: Metering Service POST p90 Latency Alarm - NON PROD
          expr: (1000 * histogram_quantile(0.90, sum(rate(java_jersey_http_duration_seconds_bucket{xgen_environment=~"dev|qa|staging",path=~"/api/private/metering/usage", method="POST"}[5m])) by (le, xgen_environment))) > 12000
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Metering Service POST P90 Latency Exceeded Threshold
            description: "Metering service POST latency has exceeded threshold for 5 minutes in {{ $labels.xgen_environment }}. Current value: {{ $value }} milliseconds"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/mJAONTlIk?orgId=20'
        - alert: Metering Service POST p99 Latency Alarm - NON PROD
          expr: (1000 * histogram_quantile(0.99, sum(rate(java_jersey_http_duration_seconds_bucket{xgen_environment=~"dev|qa|staging",path=~"/api/private/metering/usage", method="POST"}[5m])) by (le, xgen_environment))) > 30000
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Metering Service POST P99 Latency Exceeded Threshold
            description: "Metering service POST latency has exceeded threshold for 5 minutes in {{ $labels.xgen_environment }}. Current value: {{ $value }} milliseconds"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/120ZWWINz?orgId=20'
    - name: metering_service_success_rate
      rules:
        - alert: Metering Service Error Response High
          expr: (1 - sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/api/private/metering/usage", method="POST", status="5xx"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/api/private/metering/usage", method="POST"}[5m]))) * 100 < 99
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Metering Service error response above 1%
            description: "Metering service error rate has exceeded threshold for 5 minutes in prod. Current success rate: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/Fl6XQh_Sz?orgId=20'
        - alert: Metering Service Error Response High - NON PROD
          expr: (1 - (sum by (xgen_environment)(increase(java_jersey_http_duration_seconds_count{xgen_environment=~"dev|qa|staging",path=~"/api/private/metering/usage", method="POST", status="5xx"}[5m])) / sum by (xgen_environment) (increase(java_jersey_http_duration_seconds_count{xgen_environment=~"dev|qa|staging",path=~"/api/private/metering/usage", method="POST"}[5m])))) * 100 < 99
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Metering Service error response above 1%
            description: "Metering service error rate has exceeded threshold for 5 minutes in {{ $labels.xgen_environment }}. Current success rate: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/Rrp1HTlSz?orgId=20'
    - name: metering_service_client_error_rate
      rules:
        - alert: Metering Service client error rate above 0.05%
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/api/private/metering/usage", method="POST", status="4xx"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/api/private/metering/usage", method="POST"}[5m])) * 100 > 0.05
          labels:
            severity: critical
          annotations:
            summary: Metering Service client error rate above 0.05%
            description: "Metering service client error rate has exceeded threshold for in prod. Current client error rate: {{ $value }}%"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/wb-eSKwSz/billing-system-overview-dashboard?orgId=20&from=1725477988907&to=1726082788907&viewPanel=24'
        - alert: Metering Service client error rate above 0.05% - NON PROD
          expr: sum by (xgen_environment)(increase(java_jersey_http_duration_seconds_count{xgen_environment=~"dev|qa",path=~"/api/private/metering/usage", method="POST", status="4xx"}[5m])) / sum by (xgen_environment)(increase(java_jersey_http_duration_seconds_count{xgen_environment=~"dev|qa",path=~"/api/private/metering/usage", method="POST"}[5m])) * 100 > 0.05
          labels:
            severity: warning
          annotations:
            summary: Metering Service client error rate above 0.05%
            description: "Metering service client error rate has exceeded threshold for in {{ $labels.xgen_environment }}. Current client error rate: {{ $value }}%"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/wb-eSKwSz/billing-system-overview-dashboard?orgId=20&from=1725477988907&to=1726082788907&viewPanel=24'
