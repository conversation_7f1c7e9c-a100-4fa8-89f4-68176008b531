---
env: {}

externalSecrets:
  - name: genkey.json
    path: mms/genkey.json
    file_to_env_var: GENKEY_PATH
    export: false
  - name: properties
    path: mms/properties
    export: false
  - name: innit_heapdumps_key_id
  - name: innit_heapdumps_secret_key
  - name: admin_secret_key
  - name: authn_internal_client_secret
  - name: exchange_signing_key

authnServiceEnvoyFilter:
  authnServiceNamespace: core-systems-prod

istio:
  global:
    # Disabled until active-active work for Gov
    enableGlobalRouting: false
