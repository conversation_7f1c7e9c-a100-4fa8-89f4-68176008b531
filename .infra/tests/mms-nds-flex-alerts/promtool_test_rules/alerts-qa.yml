---
# yamllint disable rule:quoted-strings rule:empty-values
rule_files:
  - .infra/alerts/mms-nds-flex-alerts/alerts-qa.yml

tests:
  # QA free to flex upgrade failures
  - interval: 1m
    input_series:
      - series: 'mms_nds_flex_tenant_upgrade_failure_total{xgen_environment="qa", failure_type="RESTORE_SNAPSHOT_FAILURE", source_mtm_group_id="1234", source_mtm_cluster_name="charles", source_tenant_unique_id="instance1", group_id="abcd"}'
        values: '0+0x10 1 1 0+0x30'
      - series: 'mms_nds_flex_tenant_upgrade_failure_total{xgen_environment="qa", failure_type="NONE", source_mtm_group_id="4567", source_mtm_cluster_name="jessie", source_tenant_unique_id="8765", group_id="abcd"}'
        values: '0+0x42'
      - series: 'mms_nds_flex_tenant_upgrade_failure_total{xgen_environment="qa", failure_type="RESTORE_SNAPSHOT_FAILURE", source_mtm_group_id="1234", source_mtm_cluster_name="charles", source_tenant_unique_id="instance2", group_id="abcd"}'
        values: '0 1 0+0x16 1 0+0x23'
    alert_rule_test:
      - eval_time: 10m
        alertname: (QA) Free to Flex Upgrade Failure
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              source_mtm_group_id: 1234
              source_mtm_cluster_name: charles
              source_tenant_unique_id: instance2
              group_id: abcd
              xgen_environment: qa
            exp_annotations:
              summary: "Flex upgrade for tenant with unique id instance2 on mtm 1234 charles has failed. (cc @atlas_clusters_security_3)"
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FreeToFlexUpgradeFailure'
      - eval_time: 11m
        alertname: (QA) Free to Flex Upgrade Failure
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              source_mtm_group_id: 1234
              source_mtm_cluster_name: charles
              source_tenant_unique_id: instance1
              group_id: abcd
              xgen_environment: qa
            exp_annotations:
              summary: "Flex upgrade for tenant with unique id instance1 on mtm 1234 charles has failed. (cc @atlas_clusters_security_3)"
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FreeToFlexUpgradeFailure'
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              source_mtm_group_id: 1234
              source_mtm_cluster_name: charles
              source_tenant_unique_id: instance2
              group_id: abcd
              xgen_environment: qa
            exp_annotations:
              summary: "Flex upgrade for tenant with unique id instance2 on mtm 1234 charles has failed. (cc @atlas_clusters_security_3)"
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FreeToFlexUpgradeFailure'
      - eval_time: 26m
        alertname: (QA) Free to Flex Upgrade Failure
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              source_mtm_group_id: 1234
              source_mtm_cluster_name: charles
              source_tenant_unique_id: instance2
              group_id: abcd
              xgen_environment: qa
            exp_annotations:
              summary: "Flex upgrade for tenant with unique id instance2 on mtm 1234 charles has failed. (cc @atlas_clusters_security_3)"
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FreeToFlexUpgradeFailure'
      - eval_time: 41m
        alertname: (QA) Free to Flex Upgrade Failure
        exp_alerts:

  # QA billing metering
  - interval: 1m
    input_series:
      - series: 'mms_nds_flex_billing_jobhandler_errors_total{xgen_environment="qa", type="RuntimeException"}'
        values: '0 6 6 6 8 8 8 8 10 500'
      - series: 'mms_nds_flex_billing_meter_submission_errors_total{xgen_environment="qa", type="RuntimeException"}'
        values: '0 6 6 6 8 8 8 8 10 500'

    alert_rule_test:
      # QA Alert overall job errors.
      - eval_time: 6m
        alertname: "(QA) Increased Flex Billing Metrics or Metadata Loading Error Rate"
        exp_alerts:
      - eval_time: 12m
        alertname: "(QA) Increased Flex Billing Metrics or Metadata Loading Error Rate"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: "warning"
              type: "RuntimeException"
              xgen_environment: qa
            exp_annotations:
              summary: "Flex Billing Metrics or Metadata has increased error rate of RuntimeException."
              description: "Flex Billing job handler metrics or metadata loading errors."
              runbook_url: "https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-IncreasedBillingJobHandlerMetricsOrMetadataErrorRate"
      # QA Alert meter service submission errors.
      - eval_time: 6m
        alertname: "(QA) Increased Flex Billing Meter Submission Error Rate"
        exp_alerts:
      - eval_time: 12m
        alertname: "(QA) Increased Flex Billing Meter Submission Error Rate"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: "warning"
              type: "RuntimeException"
              xgen_environment: qa
            exp_annotations:
              summary: "Flex Meter Submission for groups is experiencing increased error rate of RuntimeException."
              description: "Flex Meter Submission errors."
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-BillingIncreasedMeterSubmissionErrorRate'

  # QA billing
  - interval: 1m
    input_series:
      - series: 'mms_nds_flex_billing_clusters_successfully_billed_total{xgen_environment="qa"}'
        values: '0+1x60 0+0x180 1+1x20'
      - series: 'mms_nds_flex_billing_clusters_loaded_total{xgen_environment="qa"}'
        values: '0+1x60 0+0x90 1+1x90 1+1x20'

    alert_rule_test:
      - eval_time: 60m
        alertname: (QA) Flex Clusters Are Not Submitted for Billing
        exp_alerts: []
      - eval_time: 150m
        alertname: (QA) Flex Clusters Are Not Submitted for Billing
        exp_alerts: []
      - eval_time: 240m
        alertname: (QA) Flex Clusters Are Not Submitted for Billing
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: "warning"
              xgen_environment: qa
            exp_annotations:
              description: Flex clusters are not submitted for billing.
              summary: Flex clusters are not submitted for billing.
              runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FlexClustersAreNotSubmittedforBilling'
      - eval_time: 270m
        alertname: (QA) Flex Clusters Are Not Submitted for Billing
        exp_alerts: []

  # QA flex fast record
  - interval: 1m
    input_series:
      # fast record capacity
      - series: 'mms_nds_fast_flex_record_available_total{cloud_provider="aws", region="us_east_1", instance_size="flex", xgen_environment="qa"}'
        values: '10+0x60'
      - series: 'mms_nds_fast_flex_record_available_total{cloud_provider="aws", region="us_east_2", instance_size="flex", xgen_environment="qa"}'
        values: '10+0x15 5+0x5 10+0x40'
      - series: 'mms_nds_fast_flex_record_available_total{cloud_provider="gcp", region="us_central1", instance_size="flex", xgen_environment="qa"}'
        values: '10+0x10 3+0x50'
      # fast record stuck
      - series: 'mms_nds_flex_fast_provision_records_stuck_claimed_state{cloud_provider="azure", region="eastus2", xgen_environment="qa"}'
        values: '0+0x30'
      - series: 'mms_nds_flex_fast_provision_records_stuck_claimed_state{cloud_provider="aws", region="ap-southeast-2", xgen_environment="qa"}'
        values: '0+0x10 2+0x20' # Goes to 2 at 10m
    alert_rule_test:
      # fast record capacity
      - eval_time: 15m
        alertname: (QA) Fast Flex Record Low Capacity
        exp_alerts: []
      - eval_time: 25m
        alertname: (QA) Fast Flex Record Low Capacity
        exp_alerts: []
      - eval_time: 45m
        alertname: (QA) Fast Flex Record Low Capacity
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: "warning"
              cloud_provider: "gcp"
              region: "us_central1"
              instance_size: "flex"
              xgen_environment: qa
            exp_annotations:
              summary: Not enough fast flex records
              description: Low capacity of fast flex records.
              runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FlexFastRecordsLowCapacity'
      # fast record stuck
      - eval_time: 9m
        alertname: (QA) Fast Flex Records Stuck in CLAIMED State
        exp_alerts: []
      - eval_time: 11m
        alertname: (QA) Fast Flex Records Stuck in CLAIMED State
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              cloud_provider: aws
              region: ap-southeast-2
              xgen_environment: qa
            exp_annotations:
              summary: '2 fast flex record(s) have been stuck in CLAIMED state for over 30 minutes for aws/ap-southeast-2.'
              runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FlexFastRecordsStuckClaimed'
      - eval_time: 25m
        alertname: (QA) Fast Flex Records Stuck in CLAIMED State
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              cloud_provider: aws
              region: ap-southeast-2
              xgen_environment: qa
            exp_annotations:
              summary: '2 fast flex record(s) have been stuck in CLAIMED state for over 30 minutes for aws/ap-southeast-2.'
              runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FlexFastRecordsStuckClaimed'

  # QA flex provisioning
  - interval: 1m
    input_series:
      - series: 'mms_nds_flex_regular_provisioned_total{cloud_provider="aws", region="us_east_1", instance_size="flex", xgen_environment="qa"}'
        values: '0+0x180'
      - series: 'mms_nds_flex_regular_provisioned_total{cloud_provider="aws", region="us_east_2", instance_size="flex", xgen_environment="qa"}'
        values: '0+1x20 20+0x130'
      - series: 'mms_nds_flex_regular_provisioned_total{cloud_provider="gcp", region="us_central_1", instance_size="flex", xgen_environment="qa"}'
        values: '0+0x30 0+1x30 30+0x30'
    alert_rule_test:
      - eval_time: 30m
        alertname: (QA) Flex Clusters Slow Provisioned
        exp_alerts: []
      - eval_time: 60m
        alertname: (QA) Flex Clusters Slow Provisioned
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: "warning"
              cloud_provider: "gcp"
              region: "us_central_1"
              instance_size: "flex"
              xgen_environment: qa
            exp_annotations:
              summary: Flex clusters in gcp us_central_1 are not provisioned fast.
              description: Flex clusters should only be provisioned regularly when we run out of pre-created records.
              runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FlexClusterRegularProvision'
      - eval_time: 90m
        alertname: (QA) Flex Clusters Slow Provisioned
        exp_alerts: []

  # QA tenant backup and restore
  - interval: 1m
    input_series:
      - series: 'mms_nds_proxy_svc_tenant_snapshot_failed_errors_total{xgen_environment="qa"}'
        values: '0 11 0+0x30'
      - series: 'mms_nds_proxy_svc_tenant_restore_failed_errors_total{xgen_environment="qa"}'
        values: '0 11 0+0x30'
    alert_rule_test:
      - eval_time: 5m
        alertname: (QA) Tenant snapshot failed errors more than 10 over 15m
        exp_alerts:
      - eval_time: 15m
        alertname: (QA) Tenant snapshot failed errors more than 10 over 15m
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              xgen_environment: qa
            exp_annotations:
              summary: Tenant snapshot failed errors more than 10 over 15m
              description: Observe NDS Proxy Tenant Errors in grafana.
              grafana_url: 'https://grafana.corp.mongodb.com/d/ha9CmmK7z/qa-release-check-board?orgId=20&var-datasource=default&var-env=qa'
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/NDS+Proxy+Tenant+Failure+Debugging+Guide'
      - eval_time: 31m
        alertname: (QA) Tenant snapshot failed errors more than 10 over 15m

      - eval_time: 5m
        alertname: (QA) Tenant restore failed errors more than 10 over 15m
        exp_alerts:
      - eval_time: 15m
        alertname: (QA) Tenant restore failed errors more than 10 over 15m
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              xgen_environment: qa
            exp_annotations:
              summary: Tenant restore failed errors more than 10 over 15m
              description: Observe NDS Proxy Tenant Errors in grafana.
              grafana_url: 'https://grafana.corp.mongodb.com/d/ha9CmmK7z/qa-release-check-board?orgId=20&var-datasource=default&var-env=qa'
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/NDS+Proxy+Tenant+Failure+Debugging+Guide'
      - eval_time: 31m
        alertname: (QA) Tenant snapshot failed errors more than 10 over 15m

  # QA tenant upgrade
  - interval: 1m
    input_series:
      - series: 'mms_nds_long_running_tenant_upgrade_total{group_id="123", cluster_name="abc", source_tenant_unique_id="456", xgen_environment="qa"}'
        values: '0 1 0+0x14 2 0+0x30'
      - series: 'mms_nds_long_running_tenant_upgrade_total{group_id="123", cluster_name="def", source_tenant_unique_id="789", xgen_environment="qa"}'
        values: '0+0x14 5 0 0+0x14'
      - series: 'mms_nds_tenant_upgrade_failure_total{group_id="123", cluster_name="abc", xgen_environment="qa", failure_tye="RESTORE_SNAPSHOT_FAILURE"}'
        values: '0 2 0+0x30'
    alert_rule_test:
      - eval_time: 0m
        alertname: (QA) Long Running Tenant Upgrade
        exp_alerts:
      - eval_time: 1m
        alertname: (QA) Long Running Tenant Upgrade
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              source_tenant_unique_id: 456
              xgen_environment: qa
            exp_annotations:
              summary: "1 tenant upgrades have been running for over 24 hours."
              runbook_url: 'https://wiki.corp.mongodb.com/display/cs/Resolving+a+Failed+Tenant+Upgrade+via+Manual+mongorestore+Procedure'
      - eval_time: 17m
        alertname: (QA) Long Running Tenant Upgrade
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              source_tenant_unique_id: 456
              xgen_environment: qa
            exp_annotations:
              summary: "2 tenant upgrades have been running for over 24 hours."
              runbook_url: 'https://wiki.corp.mongodb.com/display/cs/Resolving+a+Failed+Tenant+Upgrade+via+Manual+mongorestore+Procedure'
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              source_tenant_unique_id: 789
              xgen_environment: qa
            exp_annotations:
              summary: "5 tenant upgrades have been running for over 24 hours."
              runbook_url: 'https://wiki.corp.mongodb.com/display/cs/Resolving+a+Failed+Tenant+Upgrade+via+Manual+mongorestore+Procedure'
      - eval_time: 33m
        alertname: (QA) Long Running Tenant Upgrade
        exp_alerts:

      - eval_time: 0m
        alertname: (QA) Failed Tenant Upgrade
      - eval_time: 1m
        alertname: (QA) Failed Tenant Upgrade
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              cluster_name: abc
              group_id: 123
              xgen_environment: qa
            exp_annotations:
              summary: Tenant upgrade for 123 abc has failed multiple times (cc @atlas_clusters_security_3)
              runbook_url: 'https://wiki.corp.mongodb.com/display/cs/Resolving+a+Failed+Tenant+Upgrade+via+Manual+mongorestore+Procedure'
      - eval_time: 16m
        alertname: (QA) Failed Tenant Upgrade

  # QA Large Number of Incomplete Tenant Backup
  - interval: 1m
    input_series:
      - series: 'mms_nds_incomplete_tenant_snapshots{xgen_environment="qa", mtm_group_id="eevee", proxy_host="flareon"}'
        values: '50 50 50 50 '
      - series: 'mms_nds_incomplete_tenant_snapshots{xgen_environment="qa", mtm_group_id="eevee", proxy_host="vaporeon"}'
        values: '99 100 101 100'
      - series: 'mms_nds_incomplete_tenant_snapshots{xgen_environment="qa", mtm_group_id="eevee", proxy_host="jolteon"}'
        values: '100+1x4'
    alert_rule_test:
      - eval_time: 0m
        alertname: (QA) Large Number of Incomplete Tenant Snapshot
      - eval_time: 1m
        alertname: (QA) Large Number of Incomplete Tenant Snapshot
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              mtm_group_id: eevee
              proxy_host: jolteon
              xgen_environment: qa
            exp_annotations:
              summary: 101 number of incomplete tenant snapshots for proxy host jolteon in group eevee
              runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-LargeIncompleteTenantSnapshotTotal"
      - eval_time: 2m
        alertname: (QA) Large Number of Incomplete Tenant Snapshot
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              mtm_group_id: eevee
              proxy_host: jolteon
              xgen_environment: qa
            exp_annotations:
              summary: 102 number of incomplete tenant snapshots for proxy host jolteon in group eevee
              runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-LargeIncompleteTenantSnapshotTotal"
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              mtm_group_id: eevee
              proxy_host: vaporeon
              xgen_environment: qa
            exp_annotations:
              summary: 101 number of incomplete tenant snapshots for proxy host vaporeon in group eevee
              runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-LargeIncompleteTenantSnapshotTotal"
      - eval_time: 3m
        alertname: (QA) Large Number of Incomplete Tenant Snapshot
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: nds-flex-non-prod
              severity: warning
              mtm_group_id: eevee
              proxy_host: jolteon
              xgen_environment: qa
            exp_annotations:
              summary: 103 number of incomplete tenant snapshots for proxy host jolteon in group eevee
              runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-LargeIncompleteTenantSnapshotTotal"
