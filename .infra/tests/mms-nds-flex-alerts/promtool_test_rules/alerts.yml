---
# yamllint disable rule:quoted-strings rule:empty-values
rule_files:
  - .infra/alerts/mms-nds-flex-alerts/alerts.yml

tests:
  # Prod free to flex upgrade failures
  - interval: 1m
    input_series:
      - series: 'mms_nds_flex_tenant_upgrade_failure_total{xgen_environment="prod", failure_type="RESTORE_SNAPSHOT_FAILURE", source_mtm_group_id="1234", source_mtm_cluster_name="charles", source_tenant_unique_id="instance1", group_id="abcd"}'
        values: '0+0x10 1 1 0+0x30'
      - series: 'mms_nds_flex_tenant_upgrade_failure_total{xgen_environment="prod", failure_type="NONE", source_mtm_group_id="4567", source_mtm_cluster_name="jessie", source_tenant_unique_id="8765", group_id="abcd"}'
        values: '0+0x42'
      - series: 'mms_nds_flex_tenant_upgrade_failure_total{xgen_environment="prod", failure_type="RESTORE_SNAPSHOT_FAILURE", source_mtm_group_id="1234", source_mtm_cluster_name="charles", source_tenant_unique_id="instance2", group_id="abcd"}'
        values: '0 1 0+0x16 1 0+0x23'
    alert_rule_test:
      - eval_time: 10m
        alertname: Free to Flex Upgrade Failure
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              source_mtm_group_id: 1234
              source_mtm_cluster_name: charles
              source_tenant_unique_id: instance2
              group_id: abcd
              xgen_environment: prod
            exp_annotations:
              summary: "Flex upgrade for tenant with unique id instance2 on mtm 1234 charles has failed. (cc @atlas_clusters_security_3)"
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FreeToFlexUpgradeFailure'
      - eval_time: 11m
        alertname: Free to Flex Upgrade Failure
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              source_mtm_group_id: 1234
              source_mtm_cluster_name: charles
              source_tenant_unique_id: instance1
              group_id: abcd
              xgen_environment: prod
            exp_annotations:
              summary: "Flex upgrade for tenant with unique id instance1 on mtm 1234 charles has failed. (cc @atlas_clusters_security_3)"
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FreeToFlexUpgradeFailure'
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              source_mtm_group_id: 1234
              source_mtm_cluster_name: charles
              source_tenant_unique_id: instance2
              group_id: abcd
              xgen_environment: prod
            exp_annotations:
              summary: "Flex upgrade for tenant with unique id instance2 on mtm 1234 charles has failed. (cc @atlas_clusters_security_3)"
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FreeToFlexUpgradeFailure'
      - eval_time: 26m
        alertname: Free to Flex Upgrade Failure
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              source_mtm_group_id: 1234
              source_mtm_cluster_name: charles
              source_tenant_unique_id: instance2
              group_id: abcd
              xgen_environment: prod
            exp_annotations:
              summary: "Flex upgrade for tenant with unique id instance2 on mtm 1234 charles has failed. (cc @atlas_clusters_security_3)"
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FreeToFlexUpgradeFailure'
      - eval_time: 41m
        alertname: Free to Flex Upgrade Failure
        exp_alerts:

  # Prod flex billing metering
  - interval: 1m
    input_series:
      - series: 'mms_nds_flex_billing_jobhandler_errors_total{xgen_environment="prod", type="RuntimeException"}'
        values: '0 6 6 6 8 8 8 8 10 500'
      - series: 'mms_nds_flex_billing_meter_submission_errors_total{xgen_environment="prod", type="RuntimeException"}'
        values: '0 6 6 6 8 8 8 8 10 500'
    # Prod Alert overall job errors.
    alert_rule_test:
      - eval_time: 6m
        alertname: "Increased Flex Billing Metrics or Metadata Loading Error Rate"
        exp_alerts:
      - eval_time: 12m
        alertname: "Increased Flex Billing Metrics or Metadata Loading Error Rate"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: "warning"
              type: "RuntimeException"
              xgen_environment: prod
            exp_annotations:
              summary: "Flex Billing Metrics or Metadata has increased error rate of RuntimeException."
              description: "Flex Billing job handler metrics or metadata loading errors."
              runbook_url: "https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-IncreasedBillingJobHandlerMetricsOrMetadataErrorRate"
      # Prod Alert meter service submission errors.
      - eval_time: 6m
        alertname: "Increased Flex Billing Meter Submission Error Rate"
        exp_alerts:
      - eval_time: 12m
        alertname: "Increased Flex Billing Meter Submission Error Rate"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: "warning"
              type: "RuntimeException"
              xgen_environment: prod
            exp_annotations:
              summary: "Flex Meter Submission for groups is experiencing increased error rate of RuntimeException."
              description: "Flex Meter Submission errors."
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-BillingIncreasedMeterSubmissionErrorRate'

  # Prod flex billing
  - interval: 1m
    input_series:
      - series: 'mms_nds_flex_billing_clusters_successfully_billed_total{xgen_environment="prod"}'
        values: '0+1x60 0+0x180 1+1x20'
      - series: 'mms_nds_flex_billing_clusters_loaded_total{xgen_environment="prod"}'
        values: '0+1x60 0+0x90 1+1x90 1+1x20'
    alert_rule_test:
      - eval_time: 60m
        alertname: Flex Clusters Are Not Submitted for Billing
        exp_alerts: []
      - eval_time: 150m
        alertname: Flex Clusters Are Not Submitted for Billing
        exp_alerts: []
      - eval_time: 240m
        alertname: Flex Clusters Are Not Submitted for Billing
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: "warning"
              xgen_environment: prod
            exp_annotations:
              description: Flex clusters are not submitted for billing.
              summary: Flex clusters are not submitted for billing.
              runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FlexClustersAreNotSubmittedforBilling'
      - eval_time: 270m
        alertname: Flex Clusters Are Not Submitted for Billing
        exp_alerts: []

  # Prod flex fast records
  - interval: 1m
    input_series:
      - series: 'mms_nds_fast_flex_record_available_total{cloud_provider="aws", region="us_east_1", instance_size="flex", xgen_environment="prod"}'
        values: '10+0x60'
      - series: 'mms_nds_fast_flex_record_available_total{cloud_provider="aws", region="us_east_2", instance_size="flex", xgen_environment="prod"}'
        values: '10+0x15 5+0x5 10+0x40'
      - series: 'mms_nds_fast_flex_record_available_total{cloud_provider="gcp", region="us_central1", instance_size="flex", xgen_environment="prod"}'
        values: '10+0x10 3+0x50'

      # one region stays 0, another goes to 3 after 3 mins
      - series: 'mms_nds_flex_fast_provision_records_stuck_claimed_state{cloud_provider="aws", region="us-west-2", xgen_environment="prod"}'
        values: '0+0x30'
      - series: 'mms_nds_flex_fast_provision_records_stuck_claimed_state{cloud_provider="gcp", region="asia-northeast1", xgen_environment="prod"}'
        values: '0+0x3 3+0x27' # Goes to 3 at 3m
    alert_rule_test:
      # fast record low capacity
      - eval_time: 15m
        alertname: Fast Flex Record Low Capacity
        exp_alerts: []
      - eval_time: 25m
        alertname: Fast Flex Record Low Capacity
        exp_alerts: []
      - eval_time: 45m
        alertname: Fast Flex Record Low Capacity
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: "warning"
              cloud_provider: "gcp"
              region: "us_central1"
              instance_size: "flex"
              xgen_environment: "prod"
            exp_annotations:
              summary: Not enough fast flex records
              description: Low capacity of fast flex records.
              runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FlexFastRecordsLowCapacity'
      # fast record stuck
      - eval_time: 2m
        alertname: Fast Flex Records Stuck in CLAIMED State
        exp_alerts: []
      - eval_time: 4m
        alertname: Fast Flex Records Stuck in CLAIMED State
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              cloud_provider: gcp
              region: asia-northeast1
              xgen_environment: prod
            exp_annotations:
              summary: '3 fast flex record(s) have been stuck in CLAIMED state for over 30 minutes for gcp/asia-northeast1.'
              runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FlexFastRecordsStuckClaimed'
      - eval_time: 25m
        alertname: Fast Flex Records Stuck in CLAIMED State
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              cloud_provider: gcp
              region: asia-northeast1
              xgen_environment: prod
            exp_annotations:
              summary: '3 fast flex record(s) have been stuck in CLAIMED state for over 30 minutes for gcp/asia-northeast1.'
              runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FlexFastRecordsStuckClaimed'

  # Prod flex provisioning
  - interval: 1m
    input_series:
      # provisioning
      - series: 'mms_nds_flex_regular_provisioned_total{cloud_provider="aws", region="us_east_1", instance_size="flex", xgen_environment="prod"}'
        values: '0+0x180'
      - series: 'mms_nds_flex_regular_provisioned_total{cloud_provider="aws", region="us_east_2", instance_size="flex", xgen_environment="prod"}'
        values: '0+1x20 20+0x130'
      - series: 'mms_nds_flex_regular_provisioned_total{cloud_provider="gcp", region="us_central_1", instance_size="flex", xgen_environment="prod"}'
        values: '0+0x30 0+1x30 30+0x30'
    alert_rule_test:
      - eval_time: 30m
        alertname: Flex Clusters Slow Provisioned
        exp_alerts: []
      - eval_time: 60m
        alertname: Flex Clusters Slow Provisioned
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: "warning"
              cloud_provider: "gcp"
              region: "us_central_1"
              instance_size: "flex"
              xgen_environment: prod
            exp_annotations:
              summary: Flex clusters in gcp us_central_1 are not provisioned fast.
              description: Flex clusters should only be provisioned regularly when we run out of pre-created records.
              runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-FlexClusterRegularProvision'
      - eval_time: 90m
        alertname: Flex Clusters Slow Provisioned
        exp_alerts: []

  # Prod tenant backup and restore
  - interval: 1m
    input_series:
      - series: 'mms_nds_proxy_svc_tenant_snapshot_failed_errors_total{xgen_environment="prod"}'
        values: '0 11 0+0x30'
      - series: 'mms_nds_proxy_svc_tenant_restore_failed_errors_total{xgen_environment="prod"}'
        values: '0 11 0+0x30'
    alert_rule_test:
      # backup snapshot
      - eval_time: 5m
        alertname: (Prod) Tenant snapshot failed errors more than 10 over 15m
        exp_alerts:
      - eval_time: 15m
        alertname: (Prod) Tenant snapshot failed errors more than 10 over 15m
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              xgen_environment: prod
            exp_annotations:
              summary: Tenant snapshot failed errors more than 10 over 15m
              description: Observe NDS Proxy Tenant Errors in grafana.
              grafana_url: 'https://grafana.corp.mongodb.com/d/ha9CmmK7z/qa-release-check-board?orgId=20&var-datasource=default&var-env=prod'
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/NDS+Proxy+Tenant+Failure+Debugging+Guide'
      - eval_time: 31m
        alertname: (Prod) Tenant snapshot failed errors more than 10 over 15m

      # backup restore
      - eval_time: 5m
        alertname: (Prod) Tenant restore failed errors more than 10 over 15m
        exp_alerts:
      - eval_time: 15m
        alertname: (Prod) Tenant restore failed errors more than 10 over 15m
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              xgen_environment: prod
            exp_annotations:
              summary: Tenant restore failed errors more than 10 over 15m
              description: Observe NDS Proxy Tenant Errors in grafana.
              grafana_url: 'https://grafana.corp.mongodb.com/d/ha9CmmK7z/qa-release-check-board?orgId=20&var-datasource=default&var-env=prod'
              runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/NDS+Proxy+Tenant+Failure+Debugging+Guide'
      - eval_time: 31m
        alertname: (Prod) Tenant snapshot failed errors more than 10 over 15m

  # Prod tenant upgrades
  - interval: 1m
    input_series:
      # Prod
      - series: 'mms_nds_long_running_tenant_upgrade_total{group_id="123", cluster_name="abc", source_tenant_unique_id="456", xgen_environment="prod"}'
        values: '0 1 0+0x14 2 0+0x30'
      - series: 'mms_nds_long_running_tenant_upgrade_total{group_id="123", cluster_name="def", source_tenant_unique_id="789", xgen_environment="prod"}'
        values: '0+0x14 5 0 0+0x14'
      - series: 'mms_nds_tenant_upgrade_failure_total{group_id="123", cluster_name="abc", xgen_environment="prod", failure_tye="RESTORE_SNAPSHOT_FAILURE"}'
        values: '0 2 0+0x30'
    alert_rule_test:
      - eval_time: 0m
        alertname: Long Running Tenant Upgrade
        exp_alerts:
      - eval_time: 1m
        alertname: Long Running Tenant Upgrade
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              source_tenant_unique_id: 456
              xgen_environment: prod
            exp_annotations:
              summary: "1 tenant upgrades have been running for over 24 hours."
              runbook_url: 'https://wiki.corp.mongodb.com/display/cs/Resolving+a+Failed+Tenant+Upgrade+via+Manual+mongorestore+Procedure'
      - eval_time: 17m
        alertname: Long Running Tenant Upgrade
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              source_tenant_unique_id: 456
              xgen_environment: prod
            exp_annotations:
              summary: "2 tenant upgrades have been running for over 24 hours."
              runbook_url: 'https://wiki.corp.mongodb.com/display/cs/Resolving+a+Failed+Tenant+Upgrade+via+Manual+mongorestore+Procedure'
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              source_tenant_unique_id: 789
              xgen_environment: prod
            exp_annotations:
              summary: "5 tenant upgrades have been running for over 24 hours."
              runbook_url: 'https://wiki.corp.mongodb.com/display/cs/Resolving+a+Failed+Tenant+Upgrade+via+Manual+mongorestore+Procedure'
      - eval_time: 33m
        alertname: Long Running Tenant Upgrade
        exp_alerts:

      - eval_time: 0m
        alertname: Failed Tenant Upgrade
      - eval_time: 1m
        alertname: Failed Tenant Upgrade
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: warning
              cluster_name: abc
              group_id: 123
              xgen_environment: prod
            exp_annotations:
              summary: Tenant upgrade for 123 abc has failed multiple times (cc @atlas_clusters_security_3)
              runbook_url: 'https://wiki.corp.mongodb.com/display/cs/Resolving+a+Failed+Tenant+Upgrade+via+Manual+mongorestore+Procedure'
      - eval_time: 16m
        alertname: Failed Tenant Upgrade

  # Prod tenant failure above 80% threshold
  - interval: 1m
    input_series:
      - series: 'mms_nds_tenant_upgrade_started_total{group_id="123", cluster_name="abc", xgen_environment="prod"}'
        values: '0 5 5 10 20 20 20'
      - series: 'mms_nds_tenant_upgrade_failure_total{group_id="123", cluster_name="abc", xgen_environment="prod", failure_tye="RESTORE_SNAPSHOT_FAILURE"}'
        values: '0 2 5 10 20 10 0'
    alert_rule_test:
      - eval_time: 0m
        alertname: Tenant Upgrade failure rate above 80% over the past hour
        exp_alerts:
      - eval_time: 1m
        alertname: Tenant Upgrade failure rate above 80% over the past hour
        exp_alerts:
      - eval_time: 2m
        alertname: Tenant Upgrade failure rate above 80% over the past hour
        exp_alerts:
      - eval_time: 3m
        alertname: Tenant Upgrade failure rate above 80% over the past hour
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: critical
              xgen_environment: prod
            exp_annotations:
              summary: Tenant upgrades failures have gone above 80% over the past hour
              runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-TenantUpgradeFailureRateAbove80%25OverthePastHour"
      - eval_time: 4m
        alertname: Tenant Upgrade failure rate above 80% over the past hour
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: critical
              xgen_environment: prod
            exp_annotations:
              summary: Tenant upgrades failures have gone above 80% over the past hour
              runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-TenantUpgradeFailureRateAbove80%25OverthePastHour"
      - eval_time: 5m
        alertname: Tenant Upgrade failure rate above 80% over the past hour
        exp_alerts:
      - eval_time: 6m
        alertname: Tenant Upgrade failure rate above 80% over the past hour
        exp_alerts:

  # Prod Large Number of Incomplete Tenant Backup
  - interval: 1m
    input_series:
      - series: 'mms_nds_incomplete_tenant_snapshots{xgen_environment="prod", mtm_group_id="eevee", proxy_host="flareon"}'
        values: '50 50 50 50 '
      - series: 'mms_nds_incomplete_tenant_snapshots{xgen_environment="prod", mtm_group_id="eevee", proxy_host="vaporeon"}'
        values: '99 100 101 100'
      - series: 'mms_nds_incomplete_tenant_snapshots{xgen_environment="prod", mtm_group_id="eevee", proxy_host="jolteon"}'
        values: '100+1x4'
    alert_rule_test:
      - eval_time: 0m
        alertname: Large Number of Incomplete Tenant Snapshot
      - eval_time: 1m
        alertname: Large Number of Incomplete Tenant Snapshot
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: critical
              mtm_group_id: eevee
              proxy_host: jolteon
              xgen_environment: prod
            exp_annotations:
              summary: 101 number of incomplete tenant snapshots for proxy host jolteon in group eevee
              runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-LargeIncompleteTenantSnapshotTotal"
      - eval_time: 2m
        alertname: Large Number of Incomplete Tenant Snapshot
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: critical
              mtm_group_id: eevee
              proxy_host: jolteon
              xgen_environment: prod
            exp_annotations:
              summary: 102 number of incomplete tenant snapshots for proxy host jolteon in group eevee
              runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-LargeIncompleteTenantSnapshotTotal"
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: critical
              mtm_group_id: eevee
              proxy_host: vaporeon
              xgen_environment: prod
            exp_annotations:
              summary: 101 number of incomplete tenant snapshots for proxy host vaporeon in group eevee
              runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-LargeIncompleteTenantSnapshotTotal"
      - eval_time: 3m
        alertname: Large Number of Incomplete Tenant Snapshot
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_helm_release_name: mms-nds-flex-alerts
              xgen_pagerduty_service_name: mms-nds-flex
              severity: critical
              mtm_group_id: eevee
              proxy_host: jolteon
              xgen_environment: prod
            exp_annotations:
              summary: 103 number of incomplete tenant snapshots for proxy host jolteon in group eevee
              runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/306446863/Atlas+Flex+Alerts+Playbook#AtlasFlexAlertsPlaybook-LargeIncompleteTenantSnapshotTotal"
