---
rule_files:
  - .infra/helm/authn-service/alerts-prod.yml
tests:
  # Jersey 5xx Rate alerts
  - interval: 1m
    name: Jersey 5xx Rate
    input_series:
#     Ensuring we don't fire prod alerts for non-prod data
      - series: "java_jersey_http_duration_seconds_count{xgen_environment=\"staging\", xgen_app=\"authn-service\", status=\"5xx\"}"
        values: "0 0+1x6"
      # Slowly go down below 99.5 then come back up
      - series: "java_jersey_http_duration_seconds_count{xgen_environment=\"{{ .Values.additionalLabels.xgen_environment }}\", xgen_app=\"authn-service\", status=\"5xx\"}"
        values: "0 0+1x6"
      - series: "java_jersey_http_duration_seconds_count{xgen_environment=\"{{ .Values.additionalLabels.xgen_environment }}\", xgen_app=\"authn-service\", status=\"2xx\"}"
        values: "0+200x5 0 500 2000"
    alert_rule_test:
      - eval_time: 2m
        alertname: "(Prod) AuthN Service: HTTP success rate (5XX vs total traffic) < 99.7"
        exp_alerts: [] # Should still be above warning threshold
      - eval_time: 3m
        alertname: "(Prod) AuthN Service: HTTP success rate (5XX vs total traffic) < 99.7"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_app: authn-service
              xgen_owner: "IAM Engineers"
              severity: warning
              xgen_environment: {{ .Values.additionalLabels.xgen_environment }}
            exp_annotations:
              summary: "({{ .Values.additionalLabels.xgen_environment }}) AuthN service HTTP success rate (5XX vs total traffic) is < 99.7%"
              description: >
                The AuthN service is returning too many HTTP 5XX response codes.
              runbook: https://wiki.corp.mongodb.com/display/ATLASIAM/IAM+Production+Issues+Runbook#IAMProductionIssuesRunbook-AuthNisreturning5XXresponsecodes
      - eval_time: 3m
        alertname: "(Prod) AuthN Service: HTTP success rate (5XX vs total traffic) < 99.5"
        exp_alerts: [] # Should still be under the critical threshold
      - eval_time: 5m
        alertname: "(Prod) AuthN Service: HTTP success rate (5XX vs total traffic) < 99.5"
        exp_alerts: [] # Should still be under the critical threshold
      - eval_time: 6m
        alertname: "(Prod) AuthN Service: HTTP success rate (5XX vs total traffic) < 99.5"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_app: authn-service
              xgen_owner: "IAM Engineers"
              severity: critical
              xgen_environment: {{ .Values.additionalLabels.xgen_environment }}
            exp_annotations:
              summary: "({{ .Values.additionalLabels.xgen_environment }}) AuthN service HTTP success rate (5XX vs total traffic) is < 99.5%"
              description: >
                The AuthN service is returning too many HTTP 5XX response codes.
              runbook: https://wiki.corp.mongodb.com/display/ATLASIAM/IAM+Production+Issues+Runbook#IAMProductionIssuesRunbook-AuthNisreturning5XXresponsecodes
      - eval_time: 7m
        alertname: "(Prod) AuthN Service: HTTP success rate (5XX vs total traffic) < 99.5"
        exp_alerts: [] # Critical should have stopped now
      - eval_time: 7m
        alertname: "(Prod) AuthN Service: HTTP success rate (5XX vs total traffic) < 99.7"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_app: authn-service
              xgen_owner: "IAM Engineers"
              severity: warning
              xgen_environment: prod
            exp_annotations:
              summary: "(prod) AuthN service HTTP success rate (5XX vs total traffic) is < 99.7%"
              description: >
                The AuthN service is returning too many HTTP 5XX response codes.
              runbook: https://wiki.corp.mongodb.com/display/ATLASIAM/IAM+Production+Issues+Runbook#IAMProductionIssuesRunbook-AuthNisreturning5XXresponsecodes
      - eval_time: 8m
        alertname: "(Prod) AuthN Service: HTTP success rate (5XX vs total traffic) < 99.7"
        exp_alerts: [] # Warning should have stopped now

  # gRPC Latency Alerts
  - interval: 10s
    name: gRPC Latency
    input_series:
      # prod is triggered by .1 for 6 minutes
      - series: grpc_server_completed_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le="0.01"}
        values: "0+0x72"
      - series: grpc_server_completed_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le="0.05"}
        values: "0+0x72"
      - series: grpc_server_completed_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le=".1"}
        values: "0+1x36 0+0x36"
      - series: grpc_server_completed_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le=".5"}
        values: "0+0x72"
      - series: grpc_server_completed_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le="+Inf"}
        values: "0+0x72"

    alert_rule_test:
      - eval_time: 5m
        alertname: "(Prod) AuthN Service: gRPC request p95 latency is elevated"
        exp_alerts: [] # No alerts should trigger since the full 5m has not elapsed yet
      - eval_time: 6m
        alertname: "(Prod) AuthN Service: gRPC request p95 latency is elevated"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_app: authn-service
              xgen_owner: "IAM Engineers"
              severity: critical
              xgen_environment: {{ .Values.additionalLabels.xgen_environment }}
            exp_annotations:
              summary: "({{ .Values.additionalLabels.xgen_environment }}) AuthN gRPC p95 latency is >50ms"
              description: >
                The AuthN service GRPC p95 latency is too high.
              runbook: https://wiki.corp.mongodb.com/display/ATLASIAM/IAM+Production+Issues+Runbook#IAMProductionIssuesRunbook-AuthNlatencyiselevated
      - eval_time: 7m
        alertname: "(Prod) AuthN Service: gRPC request p95 latency is elevated"
        exp_alerts: []
      - eval_time: 12m
        alertname: "(Prod) AuthN Service: gRPC request p95 latency is elevated"
        exp_alerts: []
      # Requires an extra minute to recover since it's still averaging >.75 with the .1s from before
      - eval_time: 14m
        alertname: "(Prod) AuthN Service: gRPC request p95 latency is elevated"
        exp_alerts: []

    # gRPC Failure Alerts
  - interval: 30s
    name: gRPC Failure
    input_series:
#     Ensuring we don't fire prod alerts for non-prod data
      - series: "grpc_server_completed_duration_seconds_count{xgen_environment=\"staging\", xgen_app=\"authn-service\", grpc_status=\"PERMISSION_DENIED\", grpc_method=\"TestMethod\"}"
        values: "0 0+4x12"
      - series: "grpc_server_completed_duration_seconds_count{xgen_environment=\"{{ .Values.additionalLabels.xgen_environment }}\", xgen_app=\"authn-service\", grpc_status=\"PERMISSION_DENIED\", grpc_method=\"TestMethod\"}"
        values: "0 0+4x12"
      - series: "grpc_server_completed_duration_seconds_count{xgen_environment=\"{{ .Values.additionalLabels.xgen_environment }}\", xgen_app=\"authn-service\", grpc_status=\"OK\", grpc_method=\"TestMethod\"}"
        values: "0+180x10 0 500 2000"
    alert_rule_test:
      - eval_time: 1m
        alertname: "(Prod) AuthN Service: gRPC High proportion of failed requests"
        exp_alerts: [] # Should still be below the threshold
      - eval_time: 6m
        alertname: "(Prod) AuthN Service: gRPC High proportion of failed requests"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_app: authn-service
              xgen_owner: "IAM Engineers"
              severity: warning
              xgen_environment: {{ .Values.additionalLabels.xgen_environment }}
              grpc_method: TestMethod
            exp_annotations:
              summary: "({{ .Values.additionalLabels.xgen_environment }}) AuthN Service: gRPC method TestMethod success rate is below 99%"
              description: >
                The AuthN service GRPC failure rate is too high.
              runbook: https://wiki.corp.mongodb.com/spaces/ATLASIAM/pages/218995208/IAM+Production+Issues+Runbook#IAMProductionIssuesRunbook-AuthNServicegRPCRequestFailures
      - eval_time: 14m
        alertname: "(Prod) AuthN Service: gRPC High proportion of failed requests"
        exp_alerts: [] # Alert should have stopped now

  # HTTP Latency Alerts
  - interval: 10s
    name: HTTP Latency
    input_series:
#     Ensuring we don't fire prod alerts for non-prod data
      - series: java_jersey_http_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="staging", le="0.01"}
        values: "0+0x72"
      # prod is triggered by .1 for 6 minutes
      - series: java_jersey_http_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le="0.01"}
        values: "0+0x72"
      - series: java_jersey_http_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le="0.05"}
        values: "0+0x72"
      - series: java_jersey_http_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le=".1"}
        values: "0+1x36 0+0x36"
      - series: java_jersey_http_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le=".5"}
        values: "0+0x72"
      - series: java_jersey_http_duration_seconds_bucket{xgen_app="authn-service",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le="+Inf"}
        values: "0+0x72"

    alert_rule_test:
      - eval_time: 5m
        alertname: "(Prod) AuthN Service: HTTP request p95 latency is elevated"
        exp_alerts: [] # No alerts should trigger since the full 5m has not elapsed yet
      - eval_time: 6m
        alertname: "(Prod) AuthN Service: HTTP request p95 latency is elevated"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_app: authn-service
              xgen_owner: "IAM Engineers"
              severity: critical
              xgen_environment: {{ .Values.additionalLabels.xgen_environment }}
            exp_annotations:
              summary: "({{ .Values.additionalLabels.xgen_environment }}) AuthN HTTP p95 latency is >75ms"
              description: >
                The AuthN service HTTP p95 latency is too high.
              runbook: https://wiki.corp.mongodb.com/display/ATLASIAM/IAM+Production+Issues+Runbook#IAMProductionIssuesRunbook-AuthNlatencyiselevated
      - eval_time: 7m
        alertname: "(Prod) AuthN Service: HTTP request p95 latency is elevated"
        exp_alerts: []
      - eval_time: 12m
        alertname: "(Prod) AuthN Service: HTTP request p95 latency is elevated"
        exp_alerts: []
      # Requires an extra minute to recover since it's still averaging >.75 with the .1s from before
      - eval_time: 14m
        alertname: "(Prod) AuthN Service: HTTP request p95 latency is elevated"
        exp_alerts: []

  - interval: 10s
    name: JWKs Latency
    input_series:
#     Ensuring we don't fire prod alerts for non-prod data
      - series: authn_service_get_jwks_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="staging", le="0.5"}
        values: "0+0x72"
      - series: authn_service_get_jwks_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le="0.005"}
        values: "1x6 0+0x66"
      - series: authn_service_get_jwks_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le="0.04"}
        values: "0x6 0+1x66"
      - series: authn_service_get_jwks_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le="0.1"}
        values: "0+0x72"
      - series: authn_service_get_jwks_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le="0.5"}
        values: "0+0x72"
      - series: authn_service_get_jwks_duration_seconds_bucket{xgen_app="authn-service", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", le="+Inf"}
        values: "0+0x72"

    alert_rule_test:
      - eval_time: 1m
        alertname: "(PROD) AuthN JWKs endpoint: slow response time"
        exp_alerts: []
      - eval_time: 8m
        alertname: "(PROD) AuthN JWKs endpoint: slow response time"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_app: authn-service
              xgen_owner: "IAM Engineers"
              severity: warning
              xgen_environment: {{ .Values.additionalLabels.xgen_environment }}
            exp_annotations:
              summary: JWKs requests are taking more than 30ms
              description: >
                Requests to the get the JWKs are exceeding 30ms for p95 of requests
              runbook: https://wiki.corp.mongodb.com/spaces/ATLASIAM/pages/218995208/IAM+Production+Issues+Runbook#IAMProductionIssuesRunbook-AuthNJWKsissues

  # AuthN error volume tests
  - interval: 1m
    input_series:
      - series: "envoy_cluster_upstream_rq{xgen_environment=\"{{ .Values.additionalLabels.xgen_environment }}\", xgen_region=\"us-east-2\", envoy_cluster_name=\"authn-1\", envoy_response_code=\"503\"}"
        values: "1000 1500 2000 2500 3000 3500 4000 4500 5000 5500 8100 8200 7800 7700 7500 6000 5000 3000 2000 1000"

    alert_rule_test:
      - eval_time: 5m
        alertname: "(PROD) High AuthN Service Error Volume"
        exp_alerts: []
      - eval_time: 10m
        alertname: "(PROD) High AuthN Service Error Volume"
        exp_alerts: []
      - eval_time: 15m
        alertname: "(PROD) High AuthN Service Error Volume"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_app: authn-service
              xgen_owner: "IAM Engineers"
              severity: warning
              envoy_response_code: 503
            exp_annotations:
              summary: High number of 5XXs targeting the AuthN Service
              description: The AuthN Service is seeing unexpectedly high numbers of 5XXs, most of which are 503 errors.
              runbook: https://wiki.corp.mongodb.com/spaces/ATLASIAM/pages/218995208/IAM+Production+Issues+Runbook#IAMProductionIssuesRunbook-Highnumberof5XXstargetingtheAuthNService
  # AuthN KMS Key Creation errors alert
  - interval: 30s
    name: AuthN KMS Key Creation errors
    input_series:
      - series: "authn_service_signing_key_creation_errors_total{xgen_app=\"authn-service\", xgen_environment=\"{{ .Values.additionalLabels.xgen_environment }}\"}"
        values: "0x6 0+1x6 0x12"

    alert_rule_test:
      - eval_time: 1m
        alertname: "(PROD) AuthN KMS Key Creation errors detected"
        exp_alerts: []
      - eval_time: 5m
        alertname: "(PROD) AuthN KMS Key Creation errors detected"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_app: authn-service
              xgen_owner: "IAM Engineers"
              severity: warning
              xgen_environment: {{ .Values.additionalLabels.xgen_environment }}
            exp_annotations:
              summary: "KMS Key Creation errors detected in {{ .Values.additionalLabels.xgen_environment }} environment"
              description: >
                Failed to create a new KMS signing key for AuthN
              runbook: https://wiki.corp.mongodb.com/spaces/ATLASIAM/pages/218995208/IAM+Production+Issues+Runbook#IAMProductionIssuesRunbook-AuthNKMSKeyCreationerrorsdetected
      - eval_time: 8m
        alertname: "(PROD) AuthN KMS Key Creation errors detected"
        exp_alerts: []

  # Vercel token exchange failures
  - interval: 30s
    name: Vercel token exchange failures
    input_series:
      # Fails at 11min (10min of data), but immediately starts correcting and just takes a couple minutes to recover to >.9 over the 5min window
      - series: authn_service_vercel_token_exchange_success_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}
        values: "0+10x60"
      - series: authn_service_vercel_token_exchange_attempts_total{xgen_environment="{{.Values.additionalLabels.xgen_environment}}"}
        values: "0+12x20 250+10x40"

    alert_rule_test:
      - eval_time: 10m
        alertname: "(PROD) Vercel Native token exchange success rate is low"
        exp_alerts: []
      - eval_time: 11m
        alertname: "(PROD) Vercel Native token exchange success rate is low"
        exp_alerts:
          - exp_labels:
              namespace: default
              xgen_app: authn-service
              xgen_owner: "IAM Engineers"
              severity: warning
              xgen_environment: {{.Values.additionalLabels.xgen_environment}}
            exp_annotations:
              description: Vercel Native token exchange success rate is below 90% for 10 minutes ({{ .Values.additionalLabels.xgen_environment }})
              impact: Vercel Marketplace API access is degraded
              runbook_url: https://wiki.corp.mongodb.com/spaces/ATLASIAM/pages/218995208/IAM+Production+Issues+Runbook#IAMProductionIssuesRunbook-Verceltokensuccessratelow
              summary: Vercel Native token exchange success rate is below 90% for 10 minutes ({{ .Values.additionalLabels.xgen_environment }})
      - eval_time: 13m
        alertname: "(PROD) Vercel Native token exchange success rate is low"
        exp_alerts: []
