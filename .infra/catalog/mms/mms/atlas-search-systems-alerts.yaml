---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: atlas-search-systems-alerts
spec:
  deployment:
    slack_channel: cloud
    repository:
      owner: 10gen
      name: mms
    deploy_yaml_path: .infra/deploy.yml
    application_name: atlas-search-systems-alerts
    workload_type: deployment
    argocd_project: mms
    labels:
      app: atlas-search-systems-alerts
      owner: mms
    targets:
      dev:
        namespace: mms-dev
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
      internal:
        namespace: mms-internal
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
      prod:
        namespace: mms-prod
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
      prod-gov:
        namespace: mms-prod
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
      qa:
        namespace: mms-qa
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
      staging:
        namespace: mms-staging
        argocd_profile: argocd_mgmt
        clusters:
          aws:
            - mgmt-1-us-east-1-aws-cloud-mgmt
  lifecycle: prod
  owner: 10gen-helix-mms-prod-admin
  system: mms
  type: deployment
