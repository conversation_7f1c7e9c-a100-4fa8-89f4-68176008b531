---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: mms-nds-gov-generated
spec:
  deployment:
    slack_channel: cloud
    repository:
      owner: 10gen
      name: mms
    deploy_yaml_path: .infra/deploy.yml
    application_name: mms-nds-gov-generated
    workload_type: deployment
    argocd_project: mms
    labels:
      app: mms-nds-gov-generated
      owner: mms
    targets:
      dev-gov:
        namespace: core-systems-dev
        clusters:
          aws:
            - kube-1-*-aws-cloud-gov-dev
      qa-gov:
        namespace: core-systems-qa
        clusters:
          aws:
            - kube-1-*-aws-cloud-gov-dev
      prod-gov:
        namespace: core-systems-prod
        clusters:
          aws:
            - kube-1-*-aws-cloud-gov-prod
  lifecycle: prod
  owner: 10gen-helix-mms-prod-admin
  system: core-systems
  type: deployment
