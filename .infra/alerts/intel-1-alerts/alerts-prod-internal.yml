---
# yamllint disable rule:quoted-strings
pagerdutyService:
  enabled: true
  name: intel-1
  fullnameOverride: intel-1
  escalationPolicyName: "Intel 1 - Prometheus alert escalation policy"
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: kube status
      rules:
        - alert: Number of ready pods in mms namespace is less than 50% for 10 minutes (Prod)
          expr: (kube_deployment_status_replicas_ready{job="kube-state-metrics", namespace=~"mms.*", xgen_environment="prod", deployment=~"(mms-agent-metrics|mms-agent-regional)", xgen_region="us-east-1"} / kube_deployment_status_replicas) < 0.50
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: 'Number of { $labels.deployment }} ready pods is less than 50% for 10 minutes ({{ $labels.xgen_environment }},{{ $labels.xgen_region }})'
            description: 'Number of { $labels.deployment }} ready pods is less than 50% for 10 minutes ({{ $labels.xgen_environment }},{{ $labels.xgen_region }})'
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/MMS+in+Helix+Runbook
            affected_hosts_com: https://victoria-metrics.corp.mongodb.com/select/0/vmui/#/?g0.expr=%28kube_deployment_status_replicas_ready%7Bjob%3D%22kube-state-metrics%22%2C+namespace%3D%7E%22mms.*%22%2C+xgen_environment%3D%22prod%22%2C+deployment%3D%7E%22%28mms-agent-metrics%7Cmms-agent-regional%29%22%2C+xgen_region%3D%22us-east-1%22%7D+%2F+kube_deployment_status_replicas%29+%3C+0.50&g0.tab=0&g0.stacked=0&g0.range_input=30m&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0&g0.end_input=2025-06-30T15%3A59%3A17&g0.relative_time=last_30_minutes&g0.tenantID=0
            affected_hosts_gov: https://thanos.corp.mongodbgov.com/graph?g0.expr=(kube_deployment_status_replicas_ready%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22prod-gov%22,%20deployment%3D%22mms-agent%22%7D%20/%20kube_deployment_status_replicas)%20%3C%200.50&g0.tab=0&g0.stacked=0&g0.range_input=6h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            impact: Mms kube service is available with limited capacity.
        - alert: Kube pod is not ready in mms namespace for 30 minutes (Prod)
          expr: kube_pod_status_ready{job="kube-state-metrics", condition!="true", pod=~"(mms-agent-metrics|mms-agent-regional).*", namespace=~"mms.*", xgen_environment="prod"} > 0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: The pod {{ $labels.pod }} has been in a non-ready state for more than than 30 minutes ({{ $labels.xgen_environment }}, {{ $labels.xgen_region }})
            description: The pod {{ $labels.pod }} in namespace {{ $labels.namespace }} has been in a non-ready state for longer than 30 minutes.
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/MMS+in+Helix+Runbook
            affected_hosts_com: https://victoria-metrics.corp.mongodb.com/select/0/vmui/#/?g0.expr=kube_pod_status_ready%7Bjob%3D%22kube-state-metrics%22%2C+condition%21%3D%22true%22%2C+pod%3D%7E%22%28mms-agent-metrics%7Cmms-agent-regional%29.*%22%2C+namespace%3D%7E%22mms.*%22%2C+xgen_environment%3D%22prod%22%7D+%3E+0&g0.tab=0&g0.stacked=0&g0.range_input=30m&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0&g0.end_input=2025-06-30T15%3A59%3A54&g0.relative_time=last_30_minutes&g0.tenantID=0
            affected_hosts_gov: https://thanos.corp.mongodbgov.com/graph?g0.expr=kube_pod_status_ready%7Bjob%3D%22kube-state-metrics%22,%20condition!%3D%22true%22,%20pod%3D~%22mms-agent.*%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22prod-gov%22%7D%20%3E%200&g0.tab=0&g0.stacked=0&g0.range_input=6h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            impact: Some mms pods are down. The service is running with limited capacity.
        - alert: CrashLoopBackOff
          expr: kube_pod_container_status_waiting_reason{reason="CrashLoopBackOff", namespace=~"mms-.*", xgen_environment="prod", pod=~"(mms-agent-metrics|mms-agent-regional).+"} > bool 0
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: "{{ $value }} MMS kube pods in {{ $labels.xgen_environment }} are failing to start during the deployment."
            description: |
              The condition indicates that kube pods in {{ $labels.xgen_environment }} environment are unable to start
              during the deployment. It usually means that some secrets aren't there or app startup is broken. MMS logs
              might be the best source for information about the failure.
            runbook: https://wiki.corp.mongodb.com/display/MMS/MMS+in+Helix+Runbook
            affected_hosts_com: https://victoria-metrics.corp.mongodb.com/select/0/vmui/#/?g0.expr=kube_pod_container_status_waiting_reason%7Breason%3D%22CrashLoopBackOff%22%2C+namespace%3D%7E%22mms-.*%22%2C+xgen_environment%3D%22prod%22%2C+pod%3D%7E%22%28mms-agent-metrics%7Cmms-agent-regional%29.%2B%22%7D+%3E+bool+0&g0.tab=0&g0.stacked=0&g0.range_input=30m&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0&g0.end_input=2025-06-30T16%3A00%3A49&g0.relative_time=last_30_minutes&g0.tenantID=0
            affected_hosts_gov: https://thanos.corp.mongodbgov.com/graph?g0.expr=kube_pod_container_status_waiting_reason%7Breason%3D"CrashLoopBackOff"%2C%20namespace%3D~"mms-.*"%2C%20xgen_environment%3D~"prod-gov"%7D%20>%20bool%200&g0.tab=1&g0.stacked=0&g0.show_exemplars=0&g0.range_input=1h
            splunk_search_com_prod: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3D%22mms-prod%22%20kube.namespace_name%20=%20%22mms-*%22&display.page.search.mode=smart&dispatch.sample_ratio=1&workload_pool=&earliest=-60m%40m&latest=now&sid=1674661063.10438132
            spinnaker_cluster_prod: https://spinnaker.corp.mongodb.com/#/applications/xgen-service/clusters?reg=mms-prod,mms-regional-prod
            impact: Some services are down. Multiple versions of the code may be running at the same time.
    - name: ingestion-sli-alerts
      rules:
        - alert: Ingestion SLI Below Threshold
          expr: (intel_1::prod_upstream_availability_rate * intel_1::prod_JVM_availability_rate) < 0.80
          for: 2m
          labels:
            severity: warning
          annotations:
            summary: 'Ingestion SLI is below 80% threshold for 2 minutes ({{ $labels.xgen_environment }},{{ $labels.xgen_region }})'
            description: 'The combined ingestion SLI (upstream availability * JVM availability) has been below 80% for 2 minutes. Current value: {{ $value | humanizePercentage }}'
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346724303/Ingestion+rate+into+ingestion+threadpools+from+istio+Jetty+Jersey+is+low+for+prod
            affected_hosts_com: https://victoria-metrics.corp.mongodb.com/select/0/vmui/#/?g0.expr=%28intel_1%3A%3Aprod_upstream_availability_rate+%2A+intel_1%3A%3Aprod_JVM_availability_rate%29+%3C+0.80&g0.tab=0&g0.stacked=0&g0.range_input=30m&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0&g0.end_input=2025-06-30T16%3A00%3A00&g0.relative_time=last_30_minutes&g0.tenantID=0
            impact: Ingestion pipeline performance is degraded, affecting metrics ingestion and processing capabilities. This might trigger false alerts in the alerting pipeline.
    - name: mms-agent-metrics_ERROR_or_WARN_zscore_anomaly
      rules:
        - alert: mms-agent-metrics pool WARN/ERROR zscore anomaly
          expr: (sum(increase(logback_appender_total{xgen_app=~"mms-agent-metrics", xgen_environment=~"prod", level=~"error|warn"}[1m])) by (xgen_environment,xgen_app,level) - avg_over_time(sum(increase(logback_appender_total{xgen_app=~"mms-agent-metrics", xgen_environment=~"prod", level=~"error|warn"}[1m])) by (xgen_environment,xgen_app,level)[7d:1m])) /stddev_over_time(sum(increase(logback_appender_total{xgen_app=~"mms-agent-metrics", xgen_environment=~"prod", level=~"error|warn"}[1m])) by (xgen_environment,xgen_app,level)[7d:1m]) > 2.0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: z-score breached for WARN/ERROR messages on mms-agent-metrics pool
            description: z-score breached for WARN/ERROR messages on mms-agent-metrics pool
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346195692/Increased+mms-agent-metrics+WARN+ERRORs
    - name: mms-agent-metrics_2xx_response_code_drop_sustained
      rules:
        - alert: 2xx responses sustained drop - mms-agent-metrics
          expr: sum(increase(jetty_responses_total{xgen_app=~"mms-agent-metrics",xgen_environment=~"prod|internal",code=~"2xx"}[10m])) by (xgen_environment,xgen_app) - sum(increase(jetty_responses_total{xgen_app=~"mms-agent-metrics",xgen_environment=~"prod|internal",code=~"2xx"}[10m] offset 10m)) by (xgen_environment,xgen_app) < -60000
          for: 35m
          labels:
            severity: warning
          annotations:
            summary: mms-agent-metrics sustained drop in 2xx responses
            description: 2xx responses have dropped
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346196568/2xx+responses+sustained+drop+-+mms-agent-metrics
    - name: mms-agent-metrics__ERROR_or_WARN_sustained
      rules:
        - alert: mms-agent-metrics pool ERROR logs sustained increase over 30 mins in prod/internal
          expr: sum(increase(logback_appender_total{xgen_app=~"mms-agent-metrics", xgen_environment=~"prod", level=~"error"}[10m]))  by (xgen_environment,xgen_app,level) - sum(increase(logback_appender_total{xgen_app=~"mms-agent-metrics", xgen_environment=~"prod", level=~"error"}[10m] offset 10m))  by (xgen_environment,xgen_app,level) > 50.0
          for: 30m
          labels:
            severity: critical
          annotations:
            summary: mms-agent-metrics ERROR logs sustained increase over 30 mins
            description: mms-agent-metrics job pool ERROR logs sustained increase over 30 mins
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346195692/Increased+mms-agent-metrics+WARN+ERRORs
        - alert: mms-agent-metrics pool WARN logs sustained increase over 30 mins in prod
          expr: sum(increase(logback_appender_total{xgen_app=~"mms-agent-metrics", xgen_environment=~"prod", level=~"warn"}[10m]))  by (xgen_environment,xgen_app,level) - sum(increase(logback_appender_total{xgen_app=~"mms-agent-metrics", xgen_environment=~"prod", level=~"warn"}[10m] offset 10m))  by (xgen_environment,xgen_app,level) > 5000.0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: mms-agent-metrics pool WARN logs sustained increase over 30 mins
            description: mms-agent-metrics job pool WARN logs sustained increase over 30 mins
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346195692/Increased+mms-agent-metrics+WARN+ERRORs
    - name: endpoint utilization
      rules:
        - alert: max endpoint utilization breached - low severity - mms-agent-metrics
          expr: ((max(mms_circuitbreaker_semaphore_active_permits{xgen_app=~"mms-agent-metrics", xgen_environment="prod", semaphore_group="endpointCircuitBreaker"}) by (name))/200.0)*100.0 > 50.0
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: max endpoint utilization breached - low severity - mms-agent-metrics
            description: max endpoint utilization breached - low severity - mms-agent-metrics
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346196577/max+endpoint+utilization+breached+-+low+high+severity+-+mms-agent-metrics
        - alert: max endpoint utilization breached - high severity - mms-agent-metrics
          expr: ((max(mms_circuitbreaker_semaphore_active_permits{xgen_app=~"mms-agent-metrics", xgen_environment="prod", semaphore_group="endpointCircuitBreaker"}) by (name))/200.0)*100.0 > 59.0
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: max endpoint utilization breached - high severity - mms-agent-metrics
            description: max endpoint utilization breached - high severity - mms-agent-metrics
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346196577/max+endpoint+utilization+breached+-+low+high+severity+-+mms-agent-metrics
    - name: agent_metrics_ingestion
      rules:
        - alert: mms-agent metrics ingestion thread pool queue is running at high capacity for prod
          expr: (avg(mms_monitoring_ingestion_metrics_threadpool_tasks_queued{xgen_app=~"(mms-agent-metrics)",xgen_environment=~"prod", xgen_region="us-east-1"}) - avg_over_time(avg(mms_monitoring_ingestion_metrics_threadpool_tasks_queued{xgen_app=~"(mms-agent-metrics)",xgen_environment=~"prod", xgen_region="us-east-1"})[7d:1m]))/ stddev_over_time(avg(mms_monitoring_ingestion_metrics_threadpool_tasks_queued{xgen_app=~"(mms-agent-metrics)",xgen_environment=~"prod", xgen_region="us-east-1"})[7d:1m]) > 2.0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-agent metrics ingestion thread pool queues are at at high capacity for prod
            description: z score for metrics ingestion thread pool capacity utilization is above normal for last 20m for prod
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385846274/Ingestion+Thread+Pool+Executors+Over+Capacity
        - alert: mms-agent metrics ingestion thread pool queue is running at high capacity for internal atlas
          expr: (avg(mms_monitoring_ingestion_metrics_threadpool_tasks_queued{xgen_app=~"(mms-agent)",xgen_region="us-east-2",xgen_environment=~"internal"}) - avg_over_time(avg(mms_monitoring_ingestion_metrics_threadpool_tasks_queued{xgen_app=~"(mms-agent)",xgen_region="us-east-2",xgen_environment=~"internal"})[7d:1m]))/ stddev_over_time(avg(mms_monitoring_ingestion_metrics_threadpool_tasks_queued{xgen_app=~"(mms-agent)",xgen_region="us-east-2",xgen_environment=~"internal"})[7d:1m]) > 2.0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-agent metrics ingestion thread pool queues are at at high capacity for internal atlas
            description: z score for metrics ingestion thread pool capacity utilization is above normal for last 20m for internal atlas
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385846274/Ingestion+Thread+Pool+Executors+Over+Capacity
        - alert: mms-agent metrics ingestion thread pool queue is maxed out prod
          expr: max(mms_monitoring_ingestion_metrics_threadpool_tasks_queued_remaining_capacity{xgen_app=~"(mms-agent-metrics)",xgen_environment=~"prod", xgen_region="us-east-1"}) == 0
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: mms-agent metrics ingestion thread pool queues are maxed out prod
            description: mms-agent metrics ingestion thread pool queues are maxed out on all agent server hosts last 10 mins prod
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385846274/Ingestion+Thread+Pool+Executors+Over+Capacity
        - alert: mms-agent metrics ingestion thread pool queue is maxed out internal atlas
          expr: max(mms_monitoring_ingestion_metrics_threadpool_tasks_queued_remaining_capacity{xgen_app=~"(mms-agent)",xgen_region="us-east-2",xgen_environment=~"internal"}) == 0
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: mms-agent metrics ingestion thread pool queues are maxed out internal atlas
            description: mms-agent metrics ingestion thread pool queues are maxed out on all agent server hosts last 10 mins internal atlas
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385846274/Ingestion+Thread+Pool+Executors+Over+Capacity
        - alert: Istio Proxy Ingestion Rate Low for Prod
          expr: intel_1::istio_proxy_prod_rate / intel_1::istio_proxy_prod_prev_weekly_rate_avg::no_subquery < 0.95
          for: 2m
          labels:
            severity: warning
          annotations:
            summary: Istio Proxy ingestion rate has dropped below 95% for 2 minutes in prod
            description: The ingestion rate for Istio Proxy has dropped below 95% of the weekly average for 2 minutes in the prod environment.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346724303/Ingestion+rate+into+ingestion+threadpools+from+istio+Jetty+Jersey+is+low+for+prod
        - alert: Average observer thread pool latency exceeds 1 second
          expr: sum(rate(mms_mdc_threadpool_execution_latency_sum{xgen_app="mms-agent-metrics",xgen_environment=~"prod",xgen_region="us-east-1",threadPrefix!="QueryStatsIngestionProcessor"}[30s])) by (threadPrefix) / sum(rate(mms_mdc_threadpool_execution_latency_count{xgen_app=~"mms-agent-metrics",xgen_environment=~"prod",xgen_region="us-east-1",threadPrefix!="QueryStatsIngestionProcessor"}[30s])) by (threadPrefix) > 1
          for: 2m
          labels:
            severity: warning
          annotations:
            summary: Average Latency for {{ $labels.threadPrefix }} observer thread pool to execute a task exceeds 1 second for more than 2 minutes
            description: The average observer latency for threadPrefix {{ $labels.threadPrefix }} has exceeded 1 second for more than 2 minutes in the {{ $labels.xgen_environment }} environment.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346723836/metrics+ingestion+observable+latency+is+high
        - alert: MeasurementWriter bulk insert latency is high in prod
          expr: histogram_quantile(0.95, sum by (le, measurementType) (rate(mms_monitoring_ingestion_insert_duration_seconds_bucket{ xgen_app="mms-agent-metrics", xgen_environment=~"prod", xgen_region="us-east-1"}[1m]))) > 1
          for: 2m
          labels:
            severity: warning
          annotations:
            summary: MeasurementWriter bulk insert latency for {{ $labels.measurementType }} exceeds 1 second for more than 2 minutes
            description: The p95 bulk insert latency for measurementType {{ $labels.measurementType }} has exceeded 1 second for more than 2 minutes in the {{ $labels.xgen_environment }} environment.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346724300/MeasurementWriter+bulk+insert+latency+is+high+in+prod
        - alert: mms-agent metrics ingestion observable latency is high
          expr: histogram_quantile(0.99, sum by (le, observer) (rate(mms_monitoring_ingestion_notify_duration_seconds_bucket{xgen_app=~"mms-agent-metrics",xgen_environment=~"prod",observer!~"(class com.xgen.svc.mms.svc.ping.RealtimeHostIngestableV2Svc|class com.xgen.svc.mms.svc.ping.observers.QueryStats(HashRecord)?Exporter.*)"}[1m]))) > 0.005
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: '{{ $labels.observer }} metrics ingestion observer latency has been high'
            description: '{{ $labels.observer }} metrics ingestion observer p99 latency has been greater than 5 milliseconds for the last 5 minutes'
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346723836/metrics+ingestion+observable+latency+is+high
        - alert: mms-agent metrics ingestion RealTime observable latency is high
          expr: histogram_quantile(0.99, sum by (le, observer) (rate(mms_monitoring_ingestion_notify_duration_seconds_bucket{xgen_app=~"mms-agent-metrics",xgen_environment=~"prod",observer=~"class com.xgen.svc.mms.svc.ping.RealtimeHostIngestableV2Svc"}[1m]))) > 0.05
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: '{{ $labels.observer }} metrics ingestion observer latency has been high'
            description: '{{ $labels.observer }} metrics ingestion observer p99 latency has been greater than 50 milliseconds for the last 5 minutes'
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346723836/metrics+ingestion+observable+latency+is+high
    - name: serverless_metrics_ingestion
      rules:
        - alert: No Serverless Metrics Inserted through ingestion in the past 10 minutes
          expr: sum (rate(mms_monitoring_dao_rrd_bulkinserted_items_total{ xgen_environment="prod", xgen_region="us-east-1", type="serverlessclustermeasurement", xgen_app=~"mms-agent-metrics", xgen_prom_cluster!="cloud"}[1m])) == 0
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: Serverless metrics ingestion is down.
            description: No Serverless Metrics inserted in the past 10 minutes for ingestion or rollup.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346722003/Serverless+Metrics+Ingestion+is+Down+to+x+of+Weekly+Average
        - alert: No Serverless Metrics Ingested in the past 10 minutes
          expr: sum (rate(serverless_monitoring_dao_rrd_ingestion_items_total{ xgen_environment="prod", xgen_region="us-east-1", xgen_app=~"mms-agent-metrics", xgen_prom_cluster!="cloud" }[1m])) == 0
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: Serverless metrics ingestion is down.
            description: No Serverless Metrics Ingested in the past 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346722003/Serverless+Metrics+Ingestion+is+Down+to+x+of+Weekly+Average
        - alert: Serverless Metrics Ingestion is Down to 85% of Weekly Average
          expr: (sum (rate(serverless_monitoring_dao_rrd_ingestion_items_total{ xgen_environment="prod", xgen_region="us-east-1", xgen_app=~"mms-agent-metrics", xgen_prom_cluster!="cloud"}[2m])) / avg_over_time(sum (rate(serverless_monitoring_dao_rrd_ingestion_items_total{ xgen_environment="prod", xgen_region="us-east-1", xgen_app=~"mms-agent-metrics", xgen_prom_cluster!="cloud"}[2m]))[7d:2m])) < .85
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: Serverless Metrics Ingestion is Down to 85% Weekly Average
            description: Serverless Metrics ingestion has been 15% below the weekly average for the past 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346722003/Serverless+Metrics+Ingestion+is+Down+to+x+of+Weekly+Average
        - alert: Serverless Metrics Ingestion is Down to 90% of Weekly Average
          expr: (sum (rate(serverless_monitoring_dao_rrd_ingestion_items_total{ xgen_environment="prod", xgen_region="us-east-1", xgen_app=~"mms-agent-metrics", xgen_prom_cluster!="cloud"}[2m])) / avg_over_time(sum (rate(serverless_monitoring_dao_rrd_ingestion_items_total{ xgen_environment="prod", xgen_region="us-east-1", xgen_app=~"mms-agent-metrics", xgen_prom_cluster!="cloud"}[2m]))[7d:2m])) < .90
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Serverless Metrics Ingestion is Down to 90% of Weekly Average
            description: Serverless Metrics ingestion has been 10% below the weekly average for the past 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346722003/Serverless+Metrics+Ingestion+is+Down+to+x+of+Weekly+Average
    - name: metrics_rollup
      rules:
        - alert: Lag time of rollup with a source granularity of 10 seconds has exceeded 1 hours
          expr: max(min(time() * 1000 - max_over_time(mms_monitoring_metrics_last_written_measurement_millis{ xgen_environment="prod", from=~"PT10S.*" }[1h])) by (source, from, to, bucket)) > 3600000
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Lag time of rollup with a source granularity of 10 seconds has exceeded 1 hours
            description: The lag between the current time and the timestamp of the last written 10 second rollup metric has exceeded 1 hour. Runbook https://wiki.corp.mongodb.com/display/MMS/InTel+Alerts
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/285089894/Intel+1+Rollup+Playbook
        - alert: Lag time of rollup with a source granularity of 1 minute has exceeded 12 hours
          expr: max(min(time() * 1000 - max_over_time(mms_monitoring_metrics_last_written_measurement_millis{ xgen_environment="prod", from=~"PT1M.*" }[1h])) by (source, from, to, bucket)) > 43200000
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: Lag time of rollup with a source granularity of 1 minute has exceeded 12 hours
            description: The lag between the current time and the timestamp of the last written 1 minute rollup metric has exceeded 12 hours. Runbook https://wiki.corp.mongodb.com/display/MMS/InTel+Alerts
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/285089894/Intel+1+Rollup+Playbook
        - alert: Lag time of (db/host) rollup with a source granularity of 10 seconds has exceeded 15 minutes for prod
          expr: max(min(time() * 1000 - max_over_time(mms_monitoring_metrics_last_written_measurement_millis{ xgen_environment="prod", from=~"PT10S.*", target=~"destination", source=~"host|db" }[1h])) by (source, from, to, bucket)) > 900000
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Lag time of (db/host) rollup with a source granularity of 10 seconds has exceeded 15 minutes for prod
            description: The lag between the current time and the timestamp of the last written 10 second (db/host) rollup metric has exceeded 15 minutes for prod
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/285089894/Intel+1+Rollup+Playbook
        - alert: Lag time of (non db/host) rollup with a source granularity of 10 seconds has exceeded 15 minutes for prod
          expr: max(min(time() * 1000 - max_over_time(mms_monitoring_metrics_last_written_measurement_millis{ xgen_environment="prod", from=~"PT10S.*", source!~"host|db" }[1h])) by (source, from, to, bucket)) > 900000
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Lag time of (non db/host) rollup with a source granularity of 10 seconds has exceeded 15 minutes for prod
            description: The lag between the current time and the timestamp of the last written 10 second (non db/host) rollup metric has exceeded 15 minutes for prod
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/285089894/Intel+1+Rollup+Playbook
        - alert: Lag time of rollup with a source granularity of 10 seconds has exceeded 15 minutes for internal atlas
          expr: max(min(time() * 1000 - max_over_time(mms_monitoring_metrics_last_written_measurement_millis{ xgen_environment="internal", xgen_region="us-east-2", from=~"PT10S.*" }[1h])) by (source, from, to, bucket)) > 900000
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Lag time of rollup with a source granularity of 10 seconds has exceeded 15 minutes for internal atlas
            description: The lag between the current time and the timestamp of the last written 10 second rollup metric has exceeded 15 minutes for internal atlas
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/285089894/Intel+1+Rollup+Playbook
        - alert: Lag time of (db/host) rollup with a source granularity of 1 minute has exceeded 3 hours for prod
          expr: max(min(time() * 1000 - max_over_time(mms_monitoring_metrics_last_written_measurement_millis{ xgen_environment="prod", from=~"PT1M.*", target=~"destination", source=~"host|db" }[3h])) by (source, from, to, bucket)) > 10800000
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: Lag time of (db/host) rollup with a source granularity of 1 minute has exceeded 3 hours for prod
            description: The lag between the current time and the timestamp of the last written 1 minute (db/host) rollup metric has exceeded 3 hours for prod
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/285089894/Intel+1+Rollup+Playbook
        - alert: Lag time of (non db/host) rollup with a source granularity of 1 minute has exceeded 3 hours for prod
          expr: max(min(time() * 1000 - max_over_time(mms_monitoring_metrics_last_written_measurement_millis{ xgen_environment="prod", from=~"PT1M.*" , source!~"host|db" }[3h])) by (source, from, to, bucket)) > 10800000
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: Lag time of (non db/host) rollup with a source granularity of 1 minute has exceeded 3 hours for prod
            description: The lag between the current time and the timestamp of the last written 1 minute (non db/host) rollup metric has exceeded 3 hours for prod
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/285089894/Intel+1+Rollup+Playbook
        - alert: Lag time of rollup with a source granularity of 1 minute has exceeded 3 hours for internal atlas
          expr: max(min(time() * 1000 - max_over_time(mms_monitoring_metrics_last_written_measurement_millis{ xgen_environment="internal", xgen_region="us-east-2", from=~"PT1M.*" }[3h])) by (source, from, to, bucket)) > 10800000
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: Lag time of rollup with a source granularity of 1 minute has exceeded 3 hours for internal atlas
            description: The lag between the current time and the timestamp of the last written 1 minute rollup metric has exceeded 3 hours for internal atlas
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/285089894/Intel+1+Rollup+Playbook
        - alert: Lag time of (db/host) rollup with a source granularity of 1 hour has exceeded 48 hours for prod
          expr: max(min(time() * 1000 - max_over_time(mms_monitoring_metrics_last_written_measurement_millis{ xgen_environment="prod", from=~"PT1H.*", target=~"destination", source=~"host|db" }[48h])) by (source, from, to, bucket)) > 172800000
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: Lag time of (db/host) rollup with a source granularity of 1 hour has exceeded 48 hours for prod
            description: The lag between the current time and the timestamp of the last written 1 hour (db/host) rollup metric has exceeded 48 hours for prod
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/285089894/Intel+1+Rollup+Playbook
        - alert: Lag time of (non db/host)  rollup with a source granularity of 1 hour has exceeded 48 hours for prod
          expr: max(min(time() * 1000 - max_over_time(mms_monitoring_metrics_last_written_measurement_millis{ xgen_environment="prod", from=~"PT1H.*", source!~"host|db" }[48h])) by (source, from, to, bucket)) > 172800000
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: Lag time of (non db/host) rollup with a source granularity of 1 hour has exceeded 48 hours for prod
            description: The lag between the current time and the timestamp of the last written 1 hour (non db/host) rollup metric has exceeded 48 hours for prod
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/285089894/Intel+1+Rollup+Playbook
        - alert: Lag time of rollup with a source granularity of 1 hour has exceeded 48 hours for internal atlas
          expr: max(min(time() * 1000 - max_over_time(mms_monitoring_metrics_last_written_measurement_millis{ xgen_environment="internal", xgen_region="us-east-2", from=~"PT1H.*" }[48h])) by (source, from, to, bucket)) > 172800000
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: Lag time of rollup with a source granularity of 1 hour has exceeded 48 hours for internal atlas
            description: The lag between the current time and the timestamp of the last written 1 hour rollup metric has exceeded 48 hours for internal atlas
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/285089894/Intel+1+Rollup+Playbook
    - name: collstats_latency_metrics_ingestion
      rules:
        - alert: No Coll Stats Latency Metrics Inserted through ingestion in the past 10 minutes
          expr: sum (rate(mms_monitoring_dao_rrd_bulkinserted_items_total{ xgen_environment="prod", xgen_region="us-east-1", type="collstatslatencynamespacemeasurement", xgen_app=~"mms-agent-metrics", xgen_prom_cluster!="cloud"}[1m])) == 0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Coll Stats Latency metrics ingestion is down.
            description: No Coll Stats Latency Metrics inserted in the past 10 minutes for ingestion.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Coll Stats Latency Metrics Ingestion is Down to 85% of Weekly Average
          expr: (sum (rate(coll_stats_latency_namespace_monitoring_dao_rrd_ingestion_items_total{ xgen_environment="prod", xgen_region="us-east-1", xgen_app=~"mms-agent-metrics", xgen_prom_cluster!="cloud"}[10m])) / avg_over_time(sum (rate(coll_stats_latency_namespace_monitoring_dao_rrd_ingestion_items_total{ xgen_environment="prod", xgen_region="us-east-1", xgen_app=~"mms-agent-metrics", xgen_prom_cluster!="cloud"}[10m]))[7d:10m])) < .85
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Coll Stats Latency Metrics Ingestion is Down to 85% Weekly Average
            description: Coll Stats Latency Metrics ingestion has been 15% below the weekly average for the past 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: collStatsNamespaceLatency metrics ingestion thread pool queue is running at high capacity for prod
          expr: (avg(mms_monitoring_ingestion_collstats_metrics_threadpool_tasks_queued{xgen_app=~"(mms-agent)",xgen_environment=~"prod", xgen_region="us-east-1"}) - avg_over_time(avg(mms_monitoring_ingestion_collstats_metrics_threadpool_tasks_queued{xgen_app=~"(mms-agent)",xgen_environment=~"prod", xgen_region="us-east-1"})[7d:1m])) / stddev_over_time(avg(mms_monitoring_ingestion_collstats_metrics_threadpool_tasks_queued{xgen_app=~"(mms-agent)",xgen_environment=~"prod", xgen_region="us-east-1"})[7d:1m]) > 2.0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: collStatsNamespaceLatency metrics ingestion thread pool queues are at at high capacity for prod.
            description: z score for collStatsNamespaceLatency metrics ingestion thread pool capacity utilization is above normal for last 20m for prod
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
    - name: host_metrics_page
      rules:
        - alert: Host metrics page UI endpoint latency is too long
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/metrics/v1/groups/{groupId}/hosts/.*"}[1m])) by (status, xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/metrics/v1/groups/{groupId}/hosts/.*"}[1m])) by (status, xgen_environment) > 1.0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Host metrics page UI endpoint average latency is too high.
            description: Time to return host metrics page UI endpoint (/metrics/v1/groups/{groupId}/hosts) response is greater than 1 second.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
        - alert: Host metrics page UI too many 5xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/metrics/v1/groups/{groupId}/hosts/.*", status="5xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/metrics/v1/groups/{groupId}/hosts/.*"}[5m])) by (xgen_environment) > 0.05
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Host metrics page UI endpoint is returning abnormal volume of 5xxs.
            description: More than 5% of host metrics page responses are returning 5xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
        - alert: Host metrics page UI too many 4xxs (prod)
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|prod-gov)",path=~"/metrics/v1/groups/{groupId}/hosts/.*", status="4xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|prod-gov)",path=~"/metrics/v1/groups/{groupId}/hosts/.*"}[5m])) by (xgen_environment) > 0.10
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Host metrics page UI endpoint is returning abnormal volume of 4xxs in {{ $labels.xgen_environment }}.
            description: More than 10% of host metrics page UI responses are returning 4xxs in environment {{ $labels.xgen_environment }} every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
        - alert: Host metrics page UI too many 4xxs (internal)
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(internal)",path=~"/metrics/v1/groups/{groupId}/hosts/.*", status="4xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(internal)",path=~"/metrics/v1/groups/{groupId}/hosts/.*"}[5m])) by (xgen_environment) > 0.3
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: Host metrics page UI endpoint is returning abnormal volume of 4xxs in {{ $labels.xgen_environment }}.
            description: More than 30% of host metrics page UI responses are returning 4xxs in environment {{ $labels.xgen_environment }} every rolling 5 minute, sustained over the last 15 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
    - name: cluster_metrics_page
      rules:
        - alert: Cluster metrics page UI endpoint latency is too long
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/metrics/v1/groups/{groupId}/clusters/.*"}[1m])) by (status, xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/metrics/v1/groups/{groupId}/clusters/.*"}[1m])) by (status, xgen_environment) > 1.0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Cluster metrics page UI endpoint average latency is too high.
            description: Time to return cluster metrics page UI endpoint (/metrics/v1/groups/{groupId}/clusters) response is greater than 1 second.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
        - alert: Cluster metrics page UI too many 5xxs (prod)
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|prod-gov)",path=~"/metrics/v1/groups/{groupId}/clusters/.*", status="5xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|prod-gov)",path=~"/metrics/v1/groups/{groupId}/clusters/.*"}[5m])) by (xgen_environment) > 0.05
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Cluster metrics page UI endpoint is returning abnormal volume of 5xxs.
            description: More than 5% of cluster metrics page responses are returning 5xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
        - alert: Cluster metrics page UI too many 5xxs (internal)
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment="internal",path=~"/metrics/v1/groups/{groupId}/clusters/.*", status="5xx"}[5m])) by (xgen_environment) > 10
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Cluster metrics page UI endpoint is returning abnormal volume of 5xxs in internal.
            description: More than 10 cluster metrics page responses are returning 5xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
        - alert: Cluster metrics page UI too many 4xxs (prod)
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|prod-gov)",path=~"/metrics/v1/groups/{groupId}/clusters/.*", status="4xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|prod-gov)",path=~"/metrics/v1/groups/{groupId}/clusters/.*"}[5m])) by (xgen_environment) > 0.10
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Cluster metrics page UI endpoint is returning abnormal volume of 4xxs.
            description: More than 10% of cluster metrics page UI responses are returning 4xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
        - alert: Cluster metrics page UI too many 4xxs (internal)
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment="internal",path=~"/metrics/v1/groups/{groupId}/clusters/.*", status="4xx"}[5m])) by (xgen_environment) > 10
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Cluster metrics page UI endpoint is returning abnormal volume of 4xxs in internal.
            description: More than 10 cluster metrics page responses are returning 4xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
    - name: serverless_cluster_metrics_page
      rules:
        - alert: Serverless cluster metrics page UI endpoint latency is too long
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app="mms-ui",method="GET",xgen_environment="prod",path=~"/metrics/v1/groups/{groupId}/(serverless|flex)/.*"}[1m])) by (status, xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment="prod",path=~"/metrics/v1/groups/{groupId}/(serverless|flex)/.*"}[1m])) by (status, xgen_environment) > 4.0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Serverless cluster metrics page UI endpoint average latency is too high.
            description: Time to return serverless cluster metrics page UI endpoint (/metrics/v1/groups/{groupId}/(serverless|flex)) response is greater than 4 second.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
        - alert: Serverless cluster metrics page UI too many 5xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment="prod",path=~"/metrics/v1/groups/{groupId}/(serverless|flex)/.*", status="5xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment="prod",path=~"/metrics/v1/groups/{groupId}/(serverless|flex)/.*"}[5m])) by (xgen_environment) > 0.10
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Serverless metrics page UI endpoint is returning abnormal volume of 5xxs.
            description: More than 10% of serverless metrics page responses are returning 5xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
        - alert: Serverless metrics page UI too many 4xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment="prod",path=~"/metrics/v1/groups/{groupId}/(serverless|flex)/.*", status="4xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment="prod",path=~"/metrics/v1/groups/{groupId}/(serverless|flex)/.*"}[5m])) by (xgen_environment) > 0.10
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Serverless metrics page UI endpoint is returning abnormal volume of 4xxs.
            description: More than 10% of Serverless metrics page UI responses are returning 4xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
    - name: realtime_metrics_page
      rules:
        - alert: Realtime metrics page UI endpoint latency is too long
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",status="2xx",path=~"/metrics/v2/groups/{groupId}/hosts/{hostId}/realtime"}[1m])) by (status, xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",status="2xx",path=~"/metrics/v2/groups/{groupId}/hosts/{hostId}/realtime"}[1m])) by (status, xgen_environment) > 5.0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Realtime metrics page UI endpoint average latency is too high.
            description: Time to return realtime metrics page UI endpoint (/metrics/v2/groups/{groupId}/hosts/{hostId}/realtime) response is greater than 5 seconds.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
        - alert: Realtime metrics page UI too many 5xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/metrics/v2/groups/{groupId}/hosts/{hostId}/realtime", status="5xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/metrics/v2/groups/{groupId}/hosts/{hostId}/realtime"}[5m])) by (xgen_environment) > 0.05
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Realtime metrics page UI endpoint is returning abnormal volume of 5xxs.
            description: More than 5% of realtime metrics page responses are returning 5xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
        - alert: Realtime metrics page UI too many 4xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/metrics/v2/groups/{groupId}/hosts/{hostId}/realtime", status="4xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/metrics/v2/groups/{groupId}/hosts/{hostId}/realtime"}[5m])) by (xgen_environment) > 0.10
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Realtime metrics page UI endpoint is returning abnormal volume of 4xxs.
            description: More than 10% of realtime metrics page UI responses are returning 4xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385848166/Legacy+Metrics+UI+Endpoint+Alerts+Runbook
    - name: namespace_insights_charts_table_ui
      rules:
        - alert: Namespace Insights Chart UI endpoint latency is too long
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/metrics"}[1m])) by (status, xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/metrics"}[1m])) by (status, xgen_environment) > 5.0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights Chart UI endpoint average latency is too high.
            description: Time to return Namespace Insights Charts UI endpoint (/metrics) response is greater than 5 seconds.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights Chart too many 5xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/metrics", status="5xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/metrics"}[5m])) by (xgen_environment) > 0.05
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights Chart UI endpoint is returning abnormal volume of 5xxs.
            description: More than 5% of Namespace Insights Chart responses are returning 5xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights Chart too many 4xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/metrics", status="4xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/metrics"}[5m])) by (xgen_environment) > 0.05
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights Chart UI endpoint is returning abnormal volume of 4xxs.
            description: More than 5% of Namespace Insights Chart responses are returning 4xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights Table UI endpoint latency is too long
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/statistics"}[1m])) by (status, xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/statistics"}[1m])) by (status, xgen_environment) > 5.0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights Table UI endpoint average latency is too high.
            description: Time to return Namespace Insights Table UI endpoint (/statistics) response is greater than 5 seconds.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights Table too many 5xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/statistics", status="5xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/statistics"}[5m])) by (xgen_environment) > 0.05
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights Table UI endpoint is returning abnormal volume of 5xxs.
            description: More than 5% of Namespace Insights Table responses are returning 5xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights Table too many 4xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/statistics", status="4xx"}[5m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",method="GET",xgen_environment=~"(prod|internal|prod-gov)",path=~"/groups/{groupId}/collStats/statistics"}[5m])) by (xgen_environment) > 0.05
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights Table UI endpoint is returning abnormal volume of 4xxs.
            description: More than 5% of Namespace Insights Table responses are returning 4xxs every rolling 5 minute, sustained over the last 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
    - name: namespace_insights_pinned_namespaces_ui
      rules:
        - alert: Namespace Insights Pinned Namespaces GET endpoint latency is too long
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app="mms-ui",xgen_environment=~"(prod|internal)",method="GET",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal)",method="GET",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned"}[1m])) by (xgen_environment) > 5.0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights Pinned Namespaces GET endpoint average latency is too high.
            description: Time to return Pinned Namespaces GET endpoint (/pinned) response is greater than 5 seconds.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights GET Pinned Namespaces too many 5xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="GET",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned", status="5xx"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="GET",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned"}[1m])) by (xgen_environment) > 0.01
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights GET Pinned Namespaces is returning abnormal volume of 5xxs.
            description: More than 1% of GET Pinned Namespaces responses are returning 5xxs sustained every minute, sustained over the last 5 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights Table GET Pinned Namespaces too many 4xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="GET",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned", status="4xx"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="GET",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned"}[1m])) by (xgen_environment) > 0.05
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights GET Pinned Namespaces is returning abnormal volume of 4xxs.
            description: More than 5% of GET Pinned Namespaces responses are returning 4xxs every minute, sustained over the last 5 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights Pinned Namespaces PATCH endpoint (for pinning) latency is too long
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app="mms-ui",xgen_environment=~"(prod|internal)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned"}[1m])) by (xgen_environment) > 5.0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights Pinned Namespaces PATCH endpoint average latency is too high.
            description: Time to return Pinned Namespaces PATCH endpoint (for pinning) response is greater than 5 seconds.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights PATCH Pinned Namespaces (for pinning) too many 5xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned", status="5xx"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned"}[1m])) by (xgen_environment) > 0.01
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights PATCH Pinned Namespaces is returning abnormal volume of 5xxs.
            description: More than 1% of PATCH Pinned Namespaces endpoint (for pinning) responses are returning 5xxs every minute, sustained over the last 5 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights PATCH Pinned Namespaces (for pinning) too many 4xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned", status="4xx"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/pinned"}[1m])) by (xgen_environment) > 0.05
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights PATCH Pinned Namespaces endpoint (/pinned) is returning abnormal volume of 4xxs.
            description: More than 5% of PATCH Pinned Namespaces endpoint (for pinning) responses are returning 4xxs every minute, sustained over the last 5 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights Pinned Namespaces PATCH endpoint (for unpinning) latency is too long
          expr: sum(increase(java_jersey_http_duration_seconds_sum{xgen_app="mms-ui",xgen_environment=~"(prod|internal)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/unpin"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/unpin"}[1m])) by (xgen_environment) > 5.0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights Pinned Namespaces PATCH endpoint (for unpinning) average latency is too high.
            description: Time to return Pinned Namespaces PATCH endpoint (/unpin) response to unpin namespaces is greater than 5 seconds.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights PATCH Pinned Namespaces (for unpinning) too many 5xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/unpin", status="5xx"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/unpin"}[1m])) by (xgen_environment) > 0.01
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights PATCH Pinned Namespaces (/unpin) is returning abnormal volume of 5xxs.
            description: More than 1% of PATCH Pinned Namespaces endpoint (for unpinning) responses returning 5xxs every minute, sustained over the last 5 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
        - alert: Namespace Insights PATCH Pinned Namespaces (for unpinning) too many 4xxs
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/unpin", status="4xx"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-ui",xgen_environment=~"(prod|internal|prod-gov)",method="PATCH",path=~"/groups/{groupId}/collStats/clusters/{clusterId}/unpin"}[1m])) by (xgen_environment) > 0.05
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Namespace Insights PATCH Pinned Namespaces endpoint (/unpin) is returning abnormal volume of 4xxs.
            description: More than 5% of PATCH Pinned Namespaces endpoint (for unpinning) responses returning 4xxs every minute, sustained over the last 5 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/268100595/Namespace+Insights+Runbook#NamespaceInsightsRunbook-WhatshouldIdoifIreceiveaNamespaceInsightsalert?
    - name: mms-agent-metrics-pool_high_endpoint_latency
      rules:
        - alert: mms-agent  /ping/v2/{groupId}/metrics/status high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent-metrics", xgen_environment=~"prod", method="POST", path=~"/ping/v2/.*/metrics/status"}[1m]))  / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent-metrics", xgen_environment=~"prod", method="POST", path=~"/ping/v2/.*/metrics/status"}[1m])) - intel_1::ping_v2_metrics_status_avg_over_time::no_subquery) / intel_1::ping_v2_metrics_status_avg_stddev::no_subquery > 4.0
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for POST /ping/v2/{groupId}/metrics/status endpoint
            description: max z-score breached for POST /ping/v2/{groupId}/metrics/status endpoint
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346196604/mms-agent+resource-calls+high+latency
        - alert: mms-agent  /agents/api/automation/metrics/batch/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent-metrics", xgen_environment=~"prod", method="POST", path=~"/agents/api/automation/metrics/batch/v1/.*"}[1m]))  / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent-metrics", xgen_environment=~"prod", method="POST", path=~"/agents/api/automation/metrics/batch/v1/.*"}[1m])) - intel_1::agents_api_automation_metrics_batch_v1_avg_over_time::no_subquery) / intel_1::agents_api_automation_metrics_batch_v1_avg_stddev::no_subquery > 1.25
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for POST /agents/api/automation/metrics/batch/v1/{groupId} endpoint
            description: max z-score breached for POST /agents/api/automation/metrics/batch/v1/{groupId} endpoint
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346196604/mms-agent+resource-calls+high+latency
    - name: mms-agent_high_endpoint_latency_internal
      rules:
        - alert: mms-agent internal /ping/v2/{groupId}/metrics/status high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent|mms-agent-metrics|mms-agent-logs", xgen_environment=~"internal", method="POST", path=~"/ping/v2/.*/metrics/status"}[1m]))  / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent|mms-agent-metrics|mms-agent-logs", xgen_environment=~"internal", method="POST", path=~"/ping/v2/.*/metrics/status"}[1m])) - intel_1::ping_v2_metrics_status_avg_over_time_internal::no_subquery) / intel_1::ping_v2_metrics_status_avg_stddev_internal::no_subquery > 4.0
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for internal POST /ping/v2/{groupId}/metrics/status endpoint
            description: max z-score breached for internal POST /ping/v2/{groupId}/metrics/status endpoint
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346196604/mms-agent+resource-calls+high+latency
        - alert: mms-agent internal /agents/api/automation/metrics/batch/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent|mms-agent-metrics|mms-agent-logs", xgen_environment=~"internal", method="POST", path=~"/agents/api/automation/metrics/batch/v1/.*"}[1m]))  / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent|mms-agent-metrics|mms-agent-logs", xgen_environment=~"internal", method="POST", path=~"/agents/api/automation/metrics/batch/v1/.*"}[1m])) - intel_1::agents_api_automation_metrics_batch_v1_avg_over_time_internal::no_subquery) / intel_1::agents_api_automation_metrics_batch_v1_avg_stddev_internal::no_subquery > 4.5
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for internal POST /agents/api/automation/metrics/batch/v1/{groupId} endpoint
            description: max z-score breached for internal POST /agents/api/automation/metrics/batch/v1/{groupId} endpoint
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346196604/mms-agent+resource-calls+high+latency
    - name: data_dog_integration
      rules:
        - alert: High rate of Data Dog queue offer failures sustained over 10 mins in prod
          expr: sum(rate(mms_monitoring_data_dog_push_svc_rejection_total{xgen_environment="prod", xgen_app=~"mms-agent-metrics"}[10m])) > 2000
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Data Dog queue failed offer rates are higher than expected.
            description: Rate of offer failures to Data Dog queue has been too high, sustained for over 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346196607/High+rate+of+Datadog+queue+offer+failures+sustained+over+10+mins+in+prod
        - alert: Too many 5xxs on Data Dog API POST call
          expr: sum(increase(mms_monitoring_data_dog_api_http_post_statuses_total{xgen_environment="prod", xgen_app="mms-agent-metrics", status=~"5.*"}[1m])) / sum(increase(mms_monitoring_data_dog_api_http_post_statuses_total{xgen_environment="prod", xgen_app="mms-agent-metrics"}[1m])) > 0.01
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Data Dog POST requests are returning an abnormal volume of 5xx responses.
            description: More than 1% of POST requests to the Data Dog API are returning 5xx responses, sustained for over 5 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346196610/Too+many+5xxs+on+Data+Dog+API+POST+call
        - alert: (Critical) Too many Data Dog transmission failures
          expr: sum(increase(mms_monitoring_data_dog_api_http_post_statuses_total{xgen_environment="prod", xgen_app="mms-agent-metrics", status="202"}[10m]) or vector(0)) / (sum(increase(mms_monitoring_data_dog_api_http_post_statuses_total{xgen_environment="prod", xgen_app="mms-agent-metrics"}[10m]) or vector(0)) + sum(increase(mms_monitoring_data_dog_http_client_exception_total{xgen_environment="prod", xgen_app="mms-agent-metrics", exception!~"(DataDogAuthException|DataDogException)"}[10m]) or vector(0))) < 0.90
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Data Dog POST requests are returning an abnormally low percentage of successful responses.
            description: Less than 90% of POST requests to the Data Dog API are returning 202 responses, sustained for over 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346196610/Too+many+5xxs+on+Data+Dog+API+POST+call
        - alert: Too many Data Dog transmission failures
          # (number_of_successful_responses) / (number_of_all_responses + number_of_exceptions_that_do_not_increment_response_counter) < 80%
          #
          # DataDogAuthException and DataDogException are raised after incrementing the response
          # counter, so we don't count them in the denominator. All other exception types result in
          # the response counter not being incremented.
          #
          # The `or vector(0)` expressions allow the alert to be evaluated even if the value is not
          # present, e.g., when no events of that type occurred.
          expr: sum(increase(mms_monitoring_data_dog_api_http_post_statuses_total{xgen_environment="prod", xgen_app="mms-agent-metrics", status="202"}[10m]) or vector(0)) / (sum(increase(mms_monitoring_data_dog_api_http_post_statuses_total{xgen_environment="prod", xgen_app="mms-agent-metrics"}[10m]) or vector(0)) + sum(increase(mms_monitoring_data_dog_http_client_exception_total{xgen_environment="prod", xgen_app="mms-agent-metrics", exception!~"(DataDogAuthException|DataDogException)"}[10m]) or vector(0))) < 0.998
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Data Dog POST requests are returning an abnormally low percentage of successful responses.
            description: Less than 99.8% of POST requests to the Data Dog API are returning 202 responses, sustained for over 10 minutes.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/346196610/Too+many+5xxs+on+Data+Dog+API+POST+call
        - alert: Internal metrics for Data Dog API POST call statuses are missing
          expr:
            # `absent_over_time` will give 1 if the time series is missing. This query will give 1
            # in non-us-east-1, non-prod regions. To prevent this alert from firing in
            # non-us-east-1, non-prod regions, we use the up metric. This will only give a time
            # series in prod, us-east-1.
            count(
              absent_over_time(mms_monitoring_data_dog_api_http_post_statuses_total{
                xgen_environment="prod",
                xgen_app="mms-agent-metrics",
                xgen_region="us-east-1"
              }[1m])
            > 0) by (xgen_environment, xgen_region)
            and
            (count(
              up{
                xgen_environment="prod",
                xgen_region="us-east-1"
              }[1m]
            ) by (xgen_environment, xgen_region) > 0)
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Missing DataDogHTTPTransport API POST call total response status metrics.
            description: We are failing to report Prometheus metrics on Data Dog API POST call response statuses, sustained over 5 minutes, in us-east-1.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385847562/Internal+metrics+for+Data+Dog+.+are+missing
        - alert: Internal metrics for Data Dog API POST call client exceptions are missing
          expr:
            # `absent_over_time` will give 1 if the time series is missing. This query will give 1
            # in non-us-east-1, non-prod regions. To prevent this alert from firing in
            # non-us-east-1, non-prod regions, we use the up metric. This will only give a time
            # series in prod, us-east-1.
            count(
              absent_over_time(mms_monitoring_data_dog_http_client_exception_total{
                xgen_environment="prod",
                xgen_app="mms-agent-metrics",
                xgen_region="us-east-1"
              }[1m])
            > 0) by (xgen_environment, xgen_region)
            and
            (count(
              up{
                xgen_environment="prod",
                xgen_region="us-east-1"
              }[1m]
            ) by (xgen_environment, xgen_region) > 0)
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Missing DataDogHTTPTransport API POST call total client exception metrics.
            description: We are failing to report Prometheus metrics on Data Dog API POST call client exceptions, sustained over 5 minutes, in us-east-1.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385847562/Internal+metrics+for+Data+Dog+.+are+missing
        - alert: Internal metrics for Data Dog queue offers are missing
          expr:
            # `absent_over_time` will give 1 if the time series is missing. This query will give 1
            # in non-us-east-1, non-prod regions. To prevent this alert from firing in
            # non-us-east-1, non-prod regions, we use the up metric. This will only give a time
            # series in prod, us-east-1.
            count(
              absent_over_time(mms_monitoring_data_dog_push_svc_offered_total{
                xgen_environment="prod",
                xgen_app="mms-agent-metrics",
                xgen_region="us-east-1"
              }[1m])
            > 0) by (xgen_environment, xgen_region)
            and
            (count(
              up{
                xgen_environment="prod",
                xgen_region="us-east-1"
              }[1m]
            ) by (xgen_environment, xgen_region) > 0)
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Missing DataDogPushSvc queue metrics on total offered tasks.
            description: We are failing to report Prometheus metrics on Data Dog queue offers, sustained over 5 minutes, in us-east-1.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385847562/Internal+metrics+for+Data+Dog+.+are+missing
        - alert: Internal metrics for Data Dog queue rejections are missing
          expr:
            # `absent_over_time` will give 1 if the time series is missing. This query will give 1
            # in non-us-east-1, non-prod regions. To prevent this alert from firing in
            # non-us-east-1, non-prod regions, we use the up metric. This will only give a time
            # series in prod, us-east-1.
            count(
              absent_over_time(mms_monitoring_data_dog_push_svc_rejection_total{
                xgen_environment="prod",
                xgen_app="mms-agent-metrics",
                xgen_region="us-east-1"
              }[1m])
            > 0) by (xgen_environment, xgen_region)
            and
            (count(
              up{
                xgen_environment="prod",
                xgen_region="us-east-1"
              }[1m]
            ) by (xgen_environment, xgen_region) > 0)
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Missing DataDogPushSvc queue metrics on total rejected tasks.
            description: We are failing to report Prometheus metrics on Data Dog queue rejections, sustained over 5 minutes, in us-east-1.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385847562/Internal+metrics+for+Data+Dog+.+are+missing
    - name: metrics_as_a_service_error_warn_log_alerts
      rules:
        - alert: customer-metrics-ingestion pool ERROR logs sustained increase over 1 hour in prod
          expr: sum(increase(logback_appender_total{xgen_app=~"customer-metrics-ingestion", xgen_environment=~"prod", level=~"error"}[10m]))  by (level) - sum(increase(logback_appender_total{xgen_app=~"customer-metrics-ingestion", xgen_environment=~"prod", level=~"error"}[10m] offset 10m))  by (level) > 50.0
          for: 60m
          labels:
            severity: warning
          annotations:
            summary: customer-metrics-ingestion ERROR logs sustained increase over 1 hour
            description: The increase in error rates between the current and previous 10 min window is above 50, sustained for 1 hour
            runbook_url: http://go/maas-warn-error-logs-runbook
            splunk: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3D%22mms%22%20source%3D%22customer-metrics-ingestion%22%20%7C%20spath%20level%20%7C%20search%20(level%3DERROR)%20%7C%20timechart%20count%20by%20caller%20span%3D5m%20limit%3D10&display.page.search.mode=fast&dispatch.sample_ratio=1&earliest=-30m%40m&latest=now&display.general.type=visualizations&display.page.search.tab=visualizations&sid=1744130125.9310005
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: customer-metrics-ingestion pool WARN/ERROR zscore anomaly
          expr: (sum(increase(logback_appender_total{xgen_app=~"customer-metrics-ingestion", xgen_environment=~"prod", level=~"error|warn"}[1m])) by (xgen_environment,xgen_app,level) - avg_over_time(sum(increase(logback_appender_total{xgen_app=~"customer-metrics-ingestion", xgen_environment=~"prod", level=~"error|warn"}[1m])) by (xgen_environment,xgen_app,level)[7d:1m])) /stddev_over_time(sum(increase(logback_appender_total{xgen_app=~"customer-metrics-ingestion", xgen_environment=~"prod", level=~"error|warn"}[1m])) by (xgen_environment,xgen_app,level)[7d:1m]) > 2.0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: z-score breached for WARN/ERROR messages on customer-metrics-ingestion pool
            runbook_url: http://go/maas-warn-error-logs-runbook
            splunk: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3D%22mms%22%20source%3D%22customer-metrics-ingestion%22%20%7C%20spath%20level%20%7C%20search%20(level%3DWARN%20OR%20level%3DERROR)%20%7C%20timechart%20count%20by%20caller%20span%3D5m%20limit%3D10&display.page.search.mode=fast&dispatch.sample_ratio=1&earliest=-1h%40h&latest=now&display.general.type=visualizations&display.page.search.tab=visualizations&sid=1744112939.9282299
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
    - name: metrics_as_a_service_storage_alerts
      rules:
        - alert: High sustained memory usage detected in MaaS Victoria Metrics cluster
          expr: sum(min_over_time(process_resident_memory_anon_bytes{namespace="customer-metrics-vm-prod", xgen_environment="prod"}[10m]) / vm_available_memory_bytes{namespace="customer-metrics-vm-prod", xgen_environment="prod"}) by (job, instance) > 0.8
          for: 5m
          labels:
            severity: warning
          annotations:
            description: Too high memory usage may result into multiple issues such as OOMs or degraded performance. Consider to either increase available memory or decrease the load on the process.
            summary: Sustained high memory pressure on instance {{ $labels.instance }} in job {{ $labels.job }}, where the minimum memory usage on the given instance is still > 80% of the available memory, sustained for 5 minutes.
            runbook_url: http://go/maas-vm-capacity-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: High CPU usage detected in MaaS Victoria Metrics cluster
          expr: sum(rate(process_cpu_seconds_total{namespace="customer-metrics-vm-prod", xgen_environment="prod"}[5m]) / process_cpu_cores_available{namespace="customer-metrics-vm-prod", xgen_environment="prod"}) by (job, instance) > 0.9
          for: 5m
          labels:
            severity: warning
          annotations:
            description: Too high CPU usage may be a sign of insufficient resources and make process unstable. Consider to either increase available CPU resources or decrease the load on the process.
            summary: More than 90% of CPU is used by instance {{ $labels.instance }} in job {{ $labels.job }}, sustained for 5 minutes.
            runbook_url: http://go/maas-vm-capacity-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: MaaS Redis cluster memory usage is above 85%
          expr: avg(aws_ec_database_memory_usage_percentage_average{dimension_CacheClusterId=~"mms-maas-ec-cluster-prod.*"}) by (dimension_CacheClusterId) > 85
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: The memory usage in the MaaS Redis cluster {{ $labels.dimension_CacheClusterId }} has been above 85% for the last 10 minutes
            description: The MaaS Redis cluster {{ $labels.dimension_CacheClusterId }} is almost at capacity
            runbook_url: http://go/maas-redis-capacity-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: MaaS Redis cluster CPU usage is above 80%
          expr: avg(aws_ec_engine_cpuutilization_average{dimension_CacheClusterId=~"mms-maas-ec-cluster-prod.*"}) by (dimension_CacheClusterId) > 80
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: The CPU usage in the MaaS Redis cluster {{ $labels.dimension_CacheClusterId }} has been above 80% for the last 10 minutes
            description: The MaaS Redis cluster {{ $labels.dimension_CacheClusterId }} is running at high capacity
            runbook_url: http://go/maas-redis-capacity-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
    - name: metrics_as_a_service_ingestion_alerts
      rules:
        - alert: Total number of requests accepted is down 85% of weekly average for all MaaS use cases
          expr: count(sum(rate(customer_metrics_requests_accepted_total{xgen_environment=~"prod", xgen_app="customer-metrics-ingestion"}[1m])) by (usecase) / avg_over_time(sum(rate(customer_metrics_requests_accepted_total{xgen_environment=~"prod", xgen_app="customer-metrics-ingestion"}[1m])) by (usecase)[7d:1m]) < 0.85) == bool count(sum(rate(customer_metrics_requests_accepted_total{xgen_environment=~"prod", xgen_app="customer-metrics-ingestion"}[1m])) by (usecase)) > 0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: The number of requests accepted from MaaS ingestion endpoints is down 85% of weekly average for all use cases, indicating a widespread issue.
            runbook_url: http://go/maas-ingestion-accepted-requests-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: High latency from standard MaaS metrics ingestion endpoint
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion", path=~"/metrics/v1/otel/{usecaseId}"}[1m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion", path=~"/metrics/v1/otel/{usecaseId}"}[1m]))) * 1000 > 125
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Endpoint response time for standard MaaS ingestion endpoint ("/metrics/v1/otel/{usecaseId}") is exceeding 125 ms, sustained for 10 min
            runbook_url: http://go/maas-ingestion-endpoints-latency-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: High latency from bulk MaaS metrics ingestion endpoint
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion", path=~"/metrics/v1/otel/bulk/{usecaseId}"}[1m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion", path=~"/metrics/v1/otel/bulk/{usecaseId}"}[1m]))) * 1000 > 140
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Endpoint response time for bulk MaaS metrics ingestion endpoint ("/metrics/v1/otel/bulk/{usecaseId}") is exceeding 140 ms, sustained for 10 min
            runbook_url: http://go/maas-ingestion-endpoints-latency-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Hard rate limit for IP requests breached for MaaS use case
          expr: sum(rate(customer_metrics_ip_hard_rate_limit_total{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion"}[1m])) by (usecase) > 0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: An IP is hitting the hard limit for permitted total number of requests for the MaaS use case {{ $labels.usecase }}
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385846792/MaaS+Ingestion+Rate+Limit+Runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Abnormal volume of 5xxs responses from standard MaaS ingestion endpoint
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="customer-metrics-ingestion", method="POST", xgen_environment=~"prod", path=~"/metrics/v1/otel/{usecaseId}", status="5xx"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="customer-metrics-ingestion", method="POST", xgen_environment=~"prod", path=~"/metrics/v1/otel/{usecaseId}"}[5m])) > 0.01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Abnormal volume of 5xxs from standard (/metrics/v1/otel/{usecaseId}) endpoint; more than 1% of responses are returning 5xx responses, sustained for 10 min
            runbook_url: http://go/maas-ingestion-endpoints-5xx-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Abnormal volume of 5xxs responses from bulk MaaS ingestion endpoint
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="customer-metrics-ingestion", method="POST", xgen_environment=~"prod", path=~"/metrics/v1/otel/bulk/{usecaseId}", status="5xx"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="customer-metrics-ingestion", method="POST", xgen_environment=~"prod", path=~"/metrics/v1/otel/bulk/{usecaseId}"}[5m])) > 0.01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Abnormal volume of 5xxs from bulk (/metrics/v1/otel/bulk/{usecaseId}) endpoint; more than 1% of responses are returning 5xx responses, sustained for 10 min
            runbook_url: http://go/maas-ingestion-endpoints-5xx-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Hard rate limit for total number of samples breached for MaaS use case
          expr: sum(rate(customer_metrics_samples_hard_rate_limit_total{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion"}[1m])) by (usecase) > 0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: The MaaS use case {{ $labels.usecase }} is consistently hitting the hard rate limit for permitted number of total samples
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385846792/MaaS+Ingestion+Rate+Limit+Runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Hard rate limit for total series cardinality breached for MaaS use case
          expr: sum(rate(customer_metrics_total_series_hard_rate_limit_total{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion"}[1m])) by (usecase) > 0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: The MaaS use case {{ $labels.usecase }} is consistently hitting the hard rate limit for total series cardinality
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385846792/MaaS+Ingestion+Rate+Limit+Runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Hard rate limit for day level series cardinality breached for MaaS use case
          expr: sum(rate(customer_metrics_active_series_hard_rate_limit_total{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion"}[1m])) by (usecase) > 0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: The MaaS use case {{ $labels.usecase }} is consistently hitting the hard rate limit for daily active series cardinality
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385846792/MaaS+Ingestion+Rate+Limit+Runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Number of ready pods is less than 50% of desired pods for MaaS ingestion
          expr: sum(kube_pod_container_status_ready{job="kube-state-metrics",xgen_environment="prod",namespace=~"customer-metrics-prod", container=~"customer-metrics-ingestion"}) / sum(kube_deployment_spec_replicas{job="kube-state-metrics",xgen_environment="prod",namespace=~"customer-metrics-prod"}) < 0.5
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: The number of ready pods for MaaS ingestion is less than 50% of the desired pods
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385846827/Number+of+ready+pods+is+less+than+50+of+desired+pods+for+MaaS+ingestion
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Successful export percentage to Victoria Metrics cluster is low
          expr: (sum(rate(customer_metrics_usecase_otel_victoria_metrics_exporter_success_total{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion"}[1m])) by (cluster) or vector(0)) / ((sum(rate(customer_metrics_usecase_otel_victoria_metrics_exporter_success_total{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion"}[1m])) by (cluster) or vector(0)) + (sum(rate(customer_metrics_usecase_otel_victoria_metrics_exporter_failure_total{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion"}[1m])) by (cluster) or vector(0))) < 0.9
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Percentage of successful exports to Victoria Metrics cluster {{ $labels.cluster }} is below 90%, sustained for 10 minutes
            runbook_url: http://go/maas-victoria-metrics-low-export-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Jetty queued threads are sustained elevated for MaaS ingestion
          expr: (avg(jetty_queued_thread_pool_jobs{xgen_app=~"customer-metrics-ingestion",xgen_environment=~"prod"}) -
            avg_over_time(avg(jetty_queued_thread_pool_jobs{xgen_app=~"customer-metrics-ingestion",xgen_environment=~"prod"})[7d:1m]))/
            stddev_over_time(avg(jetty_queued_thread_pool_jobs{xgen_app=~"customer-metrics-ingestion",xgen_environment=~"prod"})[7d:1m]) > 2.0
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: Customer-metrics-ingestion (ingestion and query services) jetty pool is showing anomalous queue levels.
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385850933/Jetty+queues+exhausted+for+customer-metrics-ingestion+application
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Percentage of tasks successfully offered to Victoria Metrics writer queue is below 90% (cluster-level)
          expr: sum(rate(customer_metrics_usecase_otel_victoria_metrics_writer_accepted_total{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion"}[1m])) by (cluster) / clamp_min(sum(rate(customer_metrics_usecase_otel_victoria_metrics_writer_accepted_total{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion"}[1m])) by (cluster) + sum(rate(customer_metrics_usecase_otel_victoria_metrics_writer_dropped_total{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion"}[1m])) by (cluster), 1) < 0.9
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: The percentage of pings offered to the Victoria Metrics writer queue for cluster {{ $labels.cluster }} is below 90% (indicating a high offer rejection %), sustained for 10 minutes
            runbook_url: http://go/maas-victoria-metrics-low-offer-percentage
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/NvPbHSXNg?orgId=20
    - name: metrics_as_a_service_query_alerts
      rules:
        - alert: High latency from MaaS instant query endpoint
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion", path=~"/api/v1/query"}[1m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion", path=~"/api/v1/query"}[1m]))) * 1000 > 200
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Endpoint response time for MaaS instant query endpoint ("/api/v1/query") is exceeding 200 ms, sustained for 10 min
            runbook_url: http://go/maas-query-endpoints-latency-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: High latency from MaaS range query endpoint
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion", path=~"/api/v1/query_range"}[1m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment=~"prod", xgen_app=~"customer-metrics-ingestion", path=~"/api/v1/query_range"}[1m]))) * 1000 > 200
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Endpoint response time for MaaS range query endpoint ("/api/v1/query_range") is exceeding 200 ms, sustained for 10 min
            runbook_url: http://go/maas-query-endpoints-latency-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Abnormal volume of 5xxs responses from MaaS instant query endpoint
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="customer-metrics-ingestion", method="GET", xgen_environment=~"prod", path=~"/api/v1/query", status="5xx"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="customer-metrics-ingestion", method="GET", xgen_environment=~"prod", path=~"/api/v1/query"}[5m])) > 0.01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Abnormal volume of 5xxs from instant query (/api/v1/query) endpoint; more than 1% of responses are returning 5xx responses, sustained for 10 min
            runbook_url: http://go/maas-query-endpoints-5xx-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
        - alert: Abnormal volume of 5xxs responses from MaaS range query endpoint
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="customer-metrics-ingestion", method="GET", xgen_environment=~"prod", path=~"/api/v1/query_range", status="5xx"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="customer-metrics-ingestion", method="GET", xgen_environment=~"prod", path=~"/api/v1/query_range"}[5m])) > 0.01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Abnormal volume of 5xxs from query range (/api/v1/query_range) endpoint; more than 1% of responses are returning 5xx responses, sustained for 10 min
            runbook_url: http://go/maas-query-endpoints-5xx-runbook
            grafana_url: https://grafana.helix.corp.mongodb.com/d/dwhZw1tHk/intel-i3a-maas-metrics-as-a-service?orgId=20
    - name: kinesis_ingestion
      rules:
        - alert: kinesis task queue is running at a high utilization
          expr: avg(mms_instrumented_threadpool_batched_task_queue_utilization_percent{xgen_environment=~"prod", xgen_app=~"mms-agent|mms-agent-metrics|mms-agent-logs", type=~"cloud__intel__.*"}) by (type) > 0.85
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: The kinesis thread pool queue utilization for stream {{ $labels.type }} is running greater than 85% of its total capacity
            runbook_url: https://go/intel-4-kinesis-alerts
        - alert: kinesis throttle rate is elevated
          expr: sum(rate(mms_monitoring_kinesis_throttling_errors_total{xgen_environment=~"prod", stream_name=~"cloud__intel__.*"}[1m])) by (stream_name) / sum(rate(mms_monitoring_kinesis_attempted_records_total{xgen_environment=~"prod", stream_name=~"cloud__intel__.*"}[1m])) by (stream_name) > 0.05
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: Kinesis is failing to process >5% of pings in stream {{ $labels.stream_name }}
            runbook_url: https://go/intel-4-kinesis-alerts
    - name: tokenization
      rules:
        - alert: tokenization failures detected
          expr: sum(rate(mms_monitoring_dwh_failed_tokenization_count_total{xgen_environment=~"prod", xgen_app=~"mms-agent|mms-agent-metrics|mms-agent-logs"}[1m])) by (bson_identifier) > 0
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Tokenization is failing for {{ $labels.bson_identifier }}
            runbook_url: https://go/intel-4-tokenization-alerts
        - alert: missing organizations needed for tokenization
          expr: sum(rate(mms_monitoring_kinesis_dwh_missing_orgs_count_total{xgen_environment=~"prod", xgen_app=~"mms-agent|mms-agent-metrics|mms-agent-logs"}[1m])) by (bson_identifier) > 0
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Missing org for {{ $labels.bson_identifier }}
            runbook_url: https://go/intel-4-tokenization-alerts
        - alert: missing hmac key needed for tokenization
          expr: sum(rate(mms_monitoring_kinesis_dwh_missing_hmac_key_count_total{xgen_environment=~"prod", xgen_app=~"mms-agent|mms-agent-metrics|mms-agent-logs"}[1m])) by (bson_identifier) > 0
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Missing HMAC key for {{ $labels.bson_identifier }}
            runbook_url: https://go/intel-4-tokenization-alerts
        - alert: hmac cache size over 85%
          expr: max(mms_monitoring_hmac_cache_size_percentage{xgen_environment=~"prod", xgen_app=~"mms-agent|mms-agent-metrics|mms-agent-logs"}) >= 0.85
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: HMAC cache size approaching capacity limit
            runbook_url: https://go/intel-4-tokenization-alerts
        - alert: 0 tokenizing paths retrieved for stream that is marked for tokenization
          expr: sum(rate(mms_monitoring_kinesis_dwh_empty_paths_total{xgen_environment=~"prod", xgen_app=~"mms-agent|mms-agent-metrics|mms-agent-logs"}[1m])) by (bson_identifier) > 0
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: No paths to tokenize were found for {{ $labels.bson_identifier }}
            runbook_url: https://go/intel-4-tokenization-alerts
        - alert: tokenization path cache issues
          expr: max(mms_monitoring_tokenization_cache_hit_rate{xgen_environment=~"prod", xgen_app=~"mms-agent|mms-agent-metrics|mms-agent-logs", xgen_region="us-east-1"}) < 1
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Cache hit rate for tokenization path cache is not 100% - unable to get some entries from tokenization path cache
            runbook_url: https://go/intel-4-tokenization-alerts
    - name: performance_advisor_tracking
      rules:
        - alert: kinesis errors for performance advisor tracking
          expr: sum(rate(mms_monitoring_performanceadvisor_tracking_kinesis_errors_total{xgen_environment="prod"}[1m])) > 0
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Kinesis error rate for PA impact index stats is too high
            runbook_url: https://go/intel-4-pa-impact-alerts
        - alert: too many performance advisor tracking job queue jobs enqueued
          expr: sum(rate(mms_monitoring_performanceadvisor_tracking_job_queue_jobs_enqueued_total{xgen_environment="prod"}[1m])) by (workflow) > 200
          for: 10s
          labels:
            severity: warning
          annotations:
            summary: Enqueuing rate of performance advisor tracking job queue jobs is too high for {{ $labels.workflow }}
            runbook_url: https://go/intel-4-pa-impact-alerts
        - alert: too many failures for performance advisor tracking jobs
          expr: sum(rate(mms_monitoring_performanceadvisor_tracking_error_total{xgen_environment="prod", error!="ProcessIsNotRunning"}[1m])) by (workflow) > 0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Error rate of performance advisor tracking jobs is too high for {{ $labels.workflow }}
            runbook_url: https://go/intel-4-pa-impact-alerts
        - alert: max p95 of queue ownership delay for index stats agent job submitter
          expr: max by (job_worker) (mms_core_jobqueue_job_ownership_delay_summary_seconds{quantile="0.95", xgen_environment="prod", job_worker="com.xgen.cloud.performanceadvisor._public.jobs.PerformanceAdvisorIndexStatsJobHandler"}) > 2
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: Job queue ownership delay for index stats jobs is too high
            runbook_url: https://go/intel-4-pa-impact-alerts
        - alert: max p95 of queue ownership delay for index stats regional jobs
          expr: max by (job_worker) (mms_core_jobqueue_job_ownership_delay_summary_seconds{quantile="0.95", xgen_environment="prod", job_worker="com.xgen.cloud.performanceadvisor._public.jobs.PerformanceAdvisorIndexStatsRegionalJob"}) > 2
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: Job queue ownership delay for index stats jobs is too high
            runbook_url: https://go/intel-4-pa-impact-alerts
        - alert: max p95 of queue ownership delay for list indexes agent job submitter
          expr: max by (job_worker) (mms_core_jobqueue_job_ownership_delay_summary_seconds{quantile="0.95", xgen_environment="prod", job_worker="com.xgen.cloud.performanceadvisor._public.jobs.PerformanceAdvisorListIndexesJobHandler"}) > 2
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: Job queue ownership delay for list indexes jobs is too high
            runbook_url: https://go/intel-4-pa-impact-alerts
        - alert: max p95 of queue ownership delay for list indexes regional jobs
          expr: max by (job_worker) (mms_core_jobqueue_job_ownership_delay_summary_seconds{quantile="0.95", xgen_environment="prod", job_worker="com.xgen.cloud.performanceadvisor._public.jobs.PerformanceAdvisorListIndexesRegionalJob"}) > 2
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: Job queue ownership delay for list indexes jobs is too high
            runbook_url: https://go/intel-4-pa-impact-alerts
    - name: data_export
      rules:
        - alert: daily data export for performanceadvisor.indexes has not run for 26h
          expr: sum(increase(billing_data_export_successful_total{exportNamespace="PERFORMANCEADVISOR.INDEXES", xgen_environment="prod", xgen_region="us-east-1"}[26h])) < 1e-10
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Export of performanceadvisor.indexes failed
            runbook_url: https://go/intel-4-data-export-alerts
        - alert: data export for performanceadvisor.indexes failed
          expr: sum(increase(billing_data_export_failed_total{exportNamespace="PERFORMANCEADVISOR.INDEXES", xgen_environment="prod", xgen_region="us-east-1"}[24h])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Export of performanceadvisor.indexes failed
            runbook_url: https://go/intel-4-data-export-alerts
    - name: out_of_region_error_prod
      rules:
        - alert: Prod data out of region error for Realtime Performance Panel
          expr: sum(increase(mms_intel_data_out_of_region_errors_total{out_of_region_violation="true", xgen_environment=~"prod", request_path="/realtime/v2/{groupId}"}[5m])) by (is_tenant, server_region, request_region, violation, request_path) > 0
          labels:
            severity: warning
          annotations:
            summary: Data out of region error, dash found at https://grafana.corp.mongodb.com/goto/TqUURvhHk?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/display/DRIVERS/Cluster+Data+Outside+of+Region
    - name: metrics_discovery_alerting
      rules:
        - alert: number of open HOST_DOWN alerts is higher than weekly average
          expr: sum(mms_core_open_alerts_total{xgen_environment="prod", time_cliff=~"(UNDER_ONE_HOUR|UNDER_ONE_DAY|UNDER_ONE_WEEK|UNDER_ONE_MONTH)", event_type="HOST_DOWN"} ) / avg_over_time(sum(mms_core_open_alerts_total{xgen_environment="prod", event_type="HOST_DOWN", time_cliff=~"(UNDER_ONE_HOUR|UNDER_ONE_DAY|UNDER_ONE_WEEK|UNDER_ONE_MONTH)"})[30d:1m] offset 1w) > 1.5
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: 'HOST_DOWN alerts is higher than monthly average'
            description: 'HOST_DOWN alerts is higher than monthly average (at least 50%) for the last 10 minutes'
            runbook_url: https://wiki.corp.mongodb.com/x/GaCqF
        # This alert was disabled because it was noisy.
        #        - alert: High rate of cluster reference violations in prod
        #          expr: sum(rate(mms_monitoring_discovery_clusterreferenceviolations_total{violations="some", xgen_environment="prod"}[1h])) > 0.35
        #          for: 10m
        #          labels:
        #            severity: warning
        #          annotations:
        #            summary: Rate of cluster reference violations is higher than expected
        #            description: The rate of cluster reference violations detected by Discovery's VerifyClusterReferencesCmd has been high over the past 10 minutes.
        #            runbook_url: https://wiki.corp.mongodb.com/x/npr-Fg
    - name: metrics_as_a_service_generate_publisher_key_alerts
      rules:
        - alert: High latency from MaaS generate publisher key endpoint
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{xgen_environment="prod", xgen_app="customer-metrics-ingestion", path="/apikey/v1/publisher"}[1m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="prod", xgen_app="customer-metrics-ingestion", path="/apikey/v1/publisher"}[1m]))) * 1000 > 100
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Endpoint response time for MaaS generate publisher key endpoint (/apikey/v1/publisher) is exceeding 100 ms, sustained for 10 min
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/384992666/MaaS+Publisher+Key+Runbook
            grafana_url: https://grafana.corp.mongodb.com/goto/zwGLRzLNz?orgId=20
        - alert: Abnormal volume of 5xxs responses from MaaS generate publisher key endpoint
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="customer-metrics-ingestion", xgen_environment="prod", path="/apikey/v1/publisher", status="5xx"}[1m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="customer-metrics-ingestion", xgen_environment="prod", path="/apikey/v1/publisher"}[1m])) > 0.01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Abnormal volume of 5xxs from MaaS generate publisher key (/apikey/v1/publisher) endpoint; more than 1% of responses are returning 5xx responses, sustained for 10 min
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/384992666/MaaS+Publisher+Key+Runbook
            grafana_url: https://grafana.corp.mongodb.com/goto/UoM6qRLNk?orgId=20
    - name: ping_resource_get_customer_metrics_api_key_alerts
      rules:
        - alert: High latency from get customer metrics api key endpoint
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{xgen_app="mms-agent-metrics",xgen_environment="prod", path="/ping/v2/{groupId}/customerMetricsApiKey/{usecaseId}"}[1m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_app="mms-agent-metrics",xgen_environment="prod", path="/ping/v2/{groupId}/customerMetricsApiKey/{usecaseId}"}[1m]))) * 1000 > 100
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Endpoint response time from get customer metrics api key endpoint (/ping/v2/{groupId}/customerMetricsApiKey/{usecaseId}) is exceeding 100 ms, sustained for 10 min
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/384992666/MaaS+Publisher+Key+Runbook
            grafana_url: https://grafana.corp.mongodb.com/goto/k3Jd6RLNk?orgId=20
        - alert: Abnormal volume of 5xxs responses from get customer metrics api key endpoint
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-agent-metrics",xgen_environment="prod", path="/ping/v2/{groupId}/customerMetricsApiKey/{usecaseId}", status="5xx"}[1m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-agent-metrics", xgen_environment="prod", path="/ping/v2/{groupId}/customerMetricsApiKey/{usecaseId}"}[1m]))) > .01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Abnormal volume of 5xxs from MaaS generate publisher key (ping//v2/{groupId}/customerMetricsApiKey/{usecaseId}) endpoint; more than 1% of responses are returning 5xx responses, sustained for 10 min
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/384992666/MaaS+Publisher+Key+Runbook
            grafana_url: https://grafana.corp.mongodb.com/goto/ZYSqgzYNk?orgId=20
    - name: metrics_as_a_service_streaming_publisher_alerts
      rules:
        - alert: TCS publishing is seeing elevated error rates
          expr:
            sum by(streamName) (rate(customer_metrics_streaming_successful_records_total{xgen_environment="prod", xgen_app="customer-metrics-ingestion"}[5m]))
            /
            sum by(streamName) (rate(customer_metrics_streaming_attempted_records_total{xgen_environment="prod", xgen_app="customer-metrics-ingestion"}[5m]))
            < 0.97
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: TCS publishing is seeing elevated error rates, less than 97% of records are being successfully published, sustained for 10 minutes
            runbook_url: http://go/maas-streaming
            grafana_url: https://grafana.corp.mongodb.com/d/DmZY5ffNk/intel-1-tcs?orgId=20
        - alert: TCS task queue is running at high capacity
          expr: avg(mms_instrumented_threadpool_batched_task_queue_utilization_percent{xgen_environment="prod", xgen_app="customer-metrics-ingestion"}) > 0.8
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: The Kinesis thread pool queue utilization is running greater than 80% of its total capacity, sustained for 10 minutes
            runbook_url: http://go/maas-streaming
            grafana_url: https://grafana.corp.mongodb.com/d/DmZY5ffNk/intel-1-tcs?orgId=20
    - name: metrics_as_a_service_streaming_consumer_alerts
      rules:
        - alert: KCL consumer is seeing lag
          # Exclude streams whose names begin with "event-service-stream" from the query below.
          # Those streams belong to CAP and aren't related to MaaS.
          expr:
            max(aws_kinesis_subscribe_to_shard_event_millis_behind_latest_average{
              dimension_StreamName=~".*prod.*",
              dimension_StreamName!~"event-service-stream.*"
            }[5m]) by (dimension_ConsumerName) > 3600000
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Consumer {{ $labels.dimension_ConsumerName }} is seeing lag
            runbook_url: http://go/maas-streaming
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/-FCQnK9Ng?orgId=20
        - alert: Datadog consumer is seeing metrics being dropped from the queue
          expr: sum(rate(telemetryconsumer_datadogsender_offer_timeout_total{xgen_environment="prod", xgen_app="telemetry-consumers"}[5m])) > 0.05
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Datadog consumer is seeing metrics being dropped from the offer queue, sustained for 10 minutes
            runbook_url: http://go/maas-streaming
            grafana_url: https://grafana.corp.mongodb.com/d/DmZY5ffNk/intel-1-tcs?orgId=20
        - alert: Datadog consumer is seeing non-200s responses
          expr: sum(rate(telemetryconsumer_datadogsender_handledata_datadogsubmit_response_total{xgen_environment="prod", xgen_app="telemetry-consumers", responseCode!~"2.."}[5m])) > 0.05
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: TCS streaming consumer is seeing non-200s responses from the datadog API, sustained for 10 minutes
            runbook_url: http://go/maas-streaming
            grafana_url: https://grafana.corp.mongodb.com/d/DmZY5ffNk/intel-1-tcs?orgId=20
        - alert: Telemetry consumer pods are in CrashLoopBackOff
          expr: max_over_time(kube_pod_container_status_waiting_reason{reason="CrashLoopBackOff", job="kube-state-metrics", namespace=~"telemetry-consumers-prod"}[5m]) >= 1
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Telemetry consumer pods are failing to start and are in CrashLoopBackOff state
            runbook_url: http://go/maas-streaming
            grafana_url: https://grafana.corp.mongodb.com/d/DmZY5ffNk/intel-1-tcs?orgId=20
        - alert: Low percentage of TCS ready pods
          expr:
            (
              sum(
                kube_pod_container_status_ready{
                  job="kube-state-metrics",
                  xgen_environment="prod",
                  namespace="telemetry-consumers-prod",
                  container="telemetry-consumers"
                }
              )
                /
              sum(
                kube_deployment_spec_replicas{
                  job="kube-state-metrics",
                  xgen_environment="prod",
                  namespace="telemetry-consumers-prod"
                }
              )
            )
              <
            0.5
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Less than 50% of TCS pods are ready, sustained for 10 minutes
            runbook_url: http://go/maas-streaming
            grafana_url: https://grafana.corp.mongodb.com/d/DmZY5ffNk/intel-1-tcs?orgId=20
        - alert: TCS is seeing low success rate sending to Data Warehouse
          expr:
            sum(
              rate(
                telemetryconsumer_data_warehouse_successful_records_total{
                  xgen_environment="prod",
                  xgen_app="telemetry-consumers",
                  xgen_region="us-east-1",
                }[5m]
              )
            ) by(streamName,application)
              /
            sum(
              rate(
                telemetryconsumer_data_warehouse_attempted_records_total{
                  xgen_environment="prod",
                  xgen_app="telemetry-consumers",
                  xgen_region="us-east-1",
                }[5m]
              )
            ) by(streamName,application)
              <
            0.97
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: TCS is seeing low success rate sending to Data Warehouse, sustained for 10 minutes
            runbook_url: http://go/maas-streaming
            grafana_url: https://grafana.corp.mongodb.com/d/DmZY5ffNk/intel-1-tcs?orgId=20
        - alert: TCS is seeing low success rate sending to Datadog
          expr:
            sum(
              rate(
                telemetryconsumer_datadogsender_handledata_datadogsubmit_response_total{
                  xgen_environment="prod",
                  xgen_app="telemetry-consumers",
                  xgen_region="us-east-1",
                  responseCode=~"2.."
                }[1m]
              )
            ) by(region)
              /
            (
              sum(
                rate(
                  telemetryconsumer_datadogsender_handledata_datadogsubmit_response_total{
                    xgen_environment="prod",
                    xgen_app="telemetry-consumers",
                    xgen_region="us-east-1"
                  }[1m]
                )
              ) by(region)
                +
              (
                sum(
                  rate(
                    telemetryconsumer_datadogsender_handledata_datadogsubmit_exception_total{
                      xgen_environment="prod",
                      xgen_app="telemetry-consumers",
                      xgen_region="us-east-1"
                    }[1m]
                  )
                ) by(region)
                  or
                0
              )
            )
              <
            0.90
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: TCS is seeing success rate < 90% sending to Datadog, sustained for 10 minutes
            runbook_url: http://go/maas-streaming
            grafana_url: https://grafana.corp.mongodb.com/d/DmZY5ffNk/intel-1-tcs?orgId=20
    - name: intel_1_owned_public_api_latency_alerts
      rules:
        - alert: Average latency for the V1 host measurements endpoint is elevated
          expr: ((sum(rate(java_jersey_http_duration_seconds_sum{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v1.0/groups/{groupId}/processes/{processId}/measurements"}[10m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v1.0/groups/{groupId}/processes/{processId}/measurements"}[10m])))  * 1000) > 500
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Response time for /api/atlas/v1.0/groups/{groupId}/processes/{processId}/measurements endpoint is exceeding 500 ms, sustained for 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/MDJbpMXNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: Average latency for the V2 host measurements endpoint is elevated
          expr: ((sum(rate(java_jersey_http_duration_seconds_sum{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v2/groups/{groupId}/processes/{processId}/measurements"}[10m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v2/groups/{groupId}/processes/{processId}/measurements"}[10m])))  * 1000) > 500
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Response time for /api/atlas/v2/groups/{groupId}/processes/{processId}/measurements endpoint is exceeding 500 ms, sustained for 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/MDJbpMXNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: Average latency for the V1 disk measurements endpoint is elevated
          expr: ((sum(rate(java_jersey_http_duration_seconds_sum{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v1.0/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements"}[10m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v1.0/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements"}[10m])))  * 1000) > 400
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Response time for /api/atlas/v1.0/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements endpoint is exceeding 400 ms, sustained for 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/MDJbpMXNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: Average latency for the V2 disk measurements endpoint is elevated
          expr: ((sum(rate(java_jersey_http_duration_seconds_sum{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v2/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements"}[10m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v2/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements"}[10m])))  * 1000) > 400
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Response time for /api/atlas/v2/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements endpoint is exceeding 400 ms, sustained for 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/MDJbpMXNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: Average latency for the V1 database measurements endpoint is elevated
          expr: ((sum(rate(java_jersey_http_duration_seconds_sum{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v1.0/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements"}[10m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v1.0/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements"}[10m])))  * 1000) > 200
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Response time for /api/atlas/v1.0/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements endpoint is exceeding 200 ms, sustained for 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/MDJbpMXNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: Average latency for the V2 database measurements endpoint is elevated
          expr: ((sum(rate(java_jersey_http_duration_seconds_sum{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v2/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements"}[10m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v2/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements"}[10m])))  * 1000) > 200
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Response time for /api/atlas/v2/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements endpoint is exceeding 200 ms, sustained for 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/MDJbpMXNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: Average latency for collstat measurements endpoint is elevated
          expr: ((sum(rate(java_jersey_http_duration_seconds_sum{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v2/groups/{groupId}/processes/{processId}/{databaseName}/{collectionName}/collStats/measurements"}[10m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v2/groups/{groupId}/processes/{processId}/{databaseName}/{collectionName}/collStats/measurements"}[10m])))  * 1000) > 150
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Response time for /api/atlas/v2/groups/{groupId}/processes/{processId}/{databaseName}/{collectionName}/collStats/measurements endpoint is exceeding 150 ms, sustained for 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/MDJbpMXNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: Average latency for the FTS measurements endpoint is elevated
          expr: ((sum(rate(java_jersey_http_duration_seconds_sum{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v1.0/groups/{groupId}/hosts/{processId}/fts/metricsindexes/{databaseName}/{collectionName}/{indexName}/measurements"}[10m])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path = "/api/atlas/v1.0/groups/{groupId}/hosts/{processId}/fts/metricsindexes/{databaseName}/{collectionName}/{indexName}/measurements"}[10m])))  * 1000) > 500
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Response time for /api/atlas/v1.0/groups/{groupId}/hosts/{processId}/fts/metricsindexes/{databaseName}/{collectionName}/{indexName}/measurements endpoint is exceeding 500 ms, sustained for 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/MDJbpMXNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
    - name: intel_1_owned_public_api_5xx_alerts
      rules:
        - alert: V1 host measurements endpoint 5xx rate is too high
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v1.0/groups/{groupId}/processes/{processId}/measurements", status="5xx"}[10m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v1.0/groups/{groupId}/processes/{processId}/measurements"}[10m])) > .01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: More than 1% of /api/atlas/v1.0/groups/{groupId}/processes/{processId}/measurements responses are returning 5xx over the last 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/rC6FTGuNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: V2 host measurements endpoint 5xx rate is too high
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v2/groups/{groupId}/processes/{processId}/measurements", status="5xx"}[10m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v2/groups/{groupId}/processes/{processId}/measurements"}[10m])) > .01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: More than 1% of /api/atlas/v2/groups/{groupId}/processes/{processId}/measurements responses are returning 5xx over the last 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/rC6FTGuNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: V1 disk measurements endpoint 5xx rate is too high
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v1.0/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements", status="5xx"}[10m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v1.0/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements"}[10m])) > .01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: More than 1% of /api/atlas/v1.0/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements responses are returning 5xx over the last 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/rC6FTGuNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: V2 disk measurements endpoint 5xx rate is too high
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v2/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements", status="5xx"}[10m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v2/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements"}[10m])) > .01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: More than 1% of /api/atlas/v2/groups/{groupId}/processes/{processId}/disks/{partitionName}/measurements responses are returning 5xx over the last 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/rC6FTGuNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: V1 database measurements endpoint 5xx rate is too high
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v1.0/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements", status="5xx"}[10m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v1.0/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements"}[10m])) > .01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: More than 1% of /api/atlas/v1.0/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements responses are returning 5xx over the last 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/rC6FTGuNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: V2 database measurements endpoint 5xx rate is too high
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v2/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements", status="5xx"}[10m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v2/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements"}[10m])) > .01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: More than 1% of /api/atlas/v2/groups/{groupId}/processes/{processId}/databases/{databaseName}/measurements responses are returning 5xx over the last 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/rC6FTGuNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: Collstat measurements endpoint 5xx rate is too high
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v2/groups/{groupId}/processes/{processId}/{databaseName}/{collectionName}/collStats/measurements", status="5xx"}[10m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v2/groups/{groupId}/processes/{processId}/{databaseName}/{collectionName}/collStats/measurements"}[10m])) > .01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: More than 1% of /api/atlas/v2/groups/{groupId}/processes/{processId}/{databaseName}/{collectionName}/collStats/measurements responses are returning 5xx over the last 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/rC6FTGuNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
        - alert: FTS measurements endpoint 5xx rate is too high
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v1.0/groups/{groupId}/hosts/{processId}/fts/metricsindexes/{databaseName}/{collectionName}/{indexName}/measurements", status="5xx"}[10m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app="mms-api-public",method="GET",xgen_environment=~"(prod)",path=~"/api/atlas/v1.0/groups/{groupId}/hosts/{processId}/fts/metricsindexes/{databaseName}/{collectionName}/{indexName}/measurements"}[10m])) > .01
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: More than 1% of /api/atlas/v1.0/groups/{groupId}/hosts/{processId}/fts/metricsindexes/{databaseName}/{collectionName}/{indexName}/measurements responses are returning 5xx over the last 10 minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/goto/rC6FTGuNg?orgId=20
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/385851861/Intel+1+Owned+Public+Api+Endpoint+Alerts+Runbook
