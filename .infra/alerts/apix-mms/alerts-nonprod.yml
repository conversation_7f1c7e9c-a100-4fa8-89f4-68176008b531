---
xgen_owner: apix
pagerdutyService:
  enabled: true
  escalationPolicyFromXgenOwner: true
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
prometheusRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: api_telemetry_nonprod
      rules:
        - alert: "(NON-PROD) Total number of successful submitted telemetry records to the stream < SLA (95%)"
          expr: sum(increase(apix_telemetry_atlas_track_attempted_records_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) by (xgen_environment) > 0 and (sum(increase(apix_telemetry_atlas_track_successful_records_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) by (xgen_environment) /sum(increase(apix_telemetry_atlas_track_attempted_records_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) by (xgen_environment)) < 0.95
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: Total number of successful submitted telemetry records to the stream in the last hour < SLA (95%) in development environment ({{ $labels.xgen_environment }})
            runbook_url: http://go/apix-telemetry-runbook
        - alert: "(NON-PROD) Total number of successful enqueued telemetry records < SLA (95%)"
          expr: sum(increase(apix_telemetry_atlas_track_threadpool_attempted_requests_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) by (xgen_environment) > 0 and (sum(increase(apix_telemetry_atlas_track_threadpool_enqueued_requests_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) by (xgen_environment) /sum(increase(apix_telemetry_atlas_track_threadpool_attempted_requests_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) by (xgen_environment)) < 0.95
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: Total number of successful enqueued telemetry records in the last hour < SLA (95%) in development environment ({{ $labels.xgen_environment }})
            runbook_url: http://go/apix-telemetry-runbook
    - name: api_versioning_nonprod
      rules:
        - alert: "(DEV) version match p99 duration higher than SLA"
          expr: histogram_quantile(0.99, sum(rate(apix_version_match_duration_seconds_bucket{xgen_app=~"mms-api-public", xgen_environment="dev"}[1m])) by (le)) > 0.005
          for: 3m
          labels:
            severity: warning
          annotations:
            summary: Version match p99 duration higher than SLA (5 ms) in development environment.
            description: >
              Version match p99 duration higher than SLA (5 ms). Grafana dashboard: https://grafana.corp.mongodb.com/d/OVezUKGVz/api-versioning This is not immediately actionable. Things to look into:
                - resource metrics on the affected machines (instance label)
                - matching algorithm has to be reviewed with the current API configuration considering:
                  number of endpoints, number of versions for each endpoint, endpoint path overlapping.
    - name: api_versioning_matching_error_spike_nonprod
      rules:
        - alert: "(DEV) There is a spike of 4XX error rate in the last 24 hours for at least one v2 endpoint"
          expr: |
            # This alert triggers if today’s 4xx error count for a given path is more than 50% higher than
            # the 7-day average for that path.
            sum(
              increase(
                apix_version_match_duration_seconds_count{
                  xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", xgen_environment="dev",versioning_matching_status_http_code=~"4.."
                }[1d]
              )
            ) by(service_name,http_verb,path)
              >
            (
              1.5
                *
              avg_over_time(
                (
                  sum(
                    increase(
                      apix_version_match_duration_seconds_count{
                        xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", xgen_environment="dev",versioning_matching_status_http_code=~"4.."
                      }[1d]
                    )
                  ) by(service_name,http_verb,path)
                )[7d:1d]
              )
            )
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: (DEV) There is a spike of 4xx errors rate in the last 24 hours for at least one v2 endpoint.
            description: >
              The total amount of 4xx errors for at least one v2 endpoint has increased by 50% compared to the last 7 days.

              This is not immediately actionable. Things to look into:

                - Grafana dashboard: http://go/versioning-grafana-pagerduty. This panel shows the endpoints that are
                  causing the alert.
                - Splunk: Once you now the endpoint, add the endpoint to requestURL in this splunk query http://go/splunk-matching-error-spike-dev
                  to get additional logs details.
    - name: api_versioning_qa
      rules:
        - alert: "(QA) version match p99 duration higher than SLA"
          expr: histogram_quantile(0.99, sum(rate(apix_version_match_duration_seconds_bucket{xgen_app=~"mms-api-public", xgen_environment="qa"}[1m])) by (le)) > 0.005
          for: 3m
          labels:
            severity: warning
          annotations:
            summary: Version match p99 duration higher than SLA (5 ms) in qa environment.
            description: >
              Version match p99 duration higher than SLA (5 ms). Grafana dashboard: https://grafana.corp.mongodb.com/d/OVezUKGVz/api-versioning This is not immediately actionable. Things to look into:

                - resource metrics on the affected machines (instance label)
                - matching algorithm has to be reviewed with the current API configuration considering:
                  number of endpoints, number of versions for each endpoint, endpoint path overlapping.
    - name: device_flow_nonprod
      rules:
        - alert: "(NON-PROD) OAuth Device Flow Authentication: Spike in error rates detected"
          expr: |-
            sum(
              increase(
                mms_oidc_authentication_success_total{
                  xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"
                }[1h]
              )
            )
            / sum(
              increase(
                mms_oidc_authentication_attempt_total{
                  xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"
                }[1h]
              )
            )
            < 0.95
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: A spike in OAuth Device Flow error rates was detected ({{ .Values.additionalLabels.xgen_environment }})
            runbook_url: http://go/cli-device-flow-runbook
        - alert: "(NON-PROD) OAuth Device Flow Generation: Spike in error rates detected"
          expr: |-
            sum(
              increase(
                apix_deviceCodeSvc_generation_success_total{
                  xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"
                }[1h]
              )
            )
            / sum(
              increase(
                apix_deviceCodeSvc_generation_attempt_total{
                  xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"
                }[1h]
              )
            )
            < 0.95
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: A spike in OAuth Device Flow authentication error rates was detected ({{ .Values.additionalLabels.xgen_environment }})
            runbook_url: http://go/cli-device-flow-runbook
    - name: api_rate_limit_filter_latency_nonprod
      rules:
        - alert: "(NON-PROD) rate limit filter p99 duration higher than the SLO (20 ms)"
          expr: (histogram_quantile(0.99, sum(rate(app_middleware_rate_limit_latency_bucket{xgen_app=~"mms-api-public", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1h])) by (le, xgen_environment)) or vector(0)) > 0.02
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: rate limit filter p99 duration higher than SLO (20 ms) in ({{ $labels.xgen_environment }}) environment.
            runbook_url: http://go/rate-limiting-runbook
    - name: api_rate_limit_filter_success_rate_nonprod
      rules:
        - alert: "(NON-PROD) rate limit filter success rate lower than the SLO (99.99%)"
          expr: |-
            sum(rate(app_middleware_rate_limit_latency_count{xgen_app=~"mms-api-public", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", result=~"SUCCESS|success"}[1h])) by (xgen_environment) /
            sum(rate(app_middleware_rate_limit_latency_count{xgen_app=~"mms-api-public", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1h])) by (xgen_environment) < 0.9999
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: rate limit filter success rate lower than the SLO (99.99%) in ({{ $labels.xgen_environment }}) environment.
            runbook_url: http://go/rate-limiting-runbook
    - name: api_rate_limit_filter_spike_rejected_request_nonprod
      rules:
        - alert: "(NON-PROD) rate limit filter - spike in rejected requests (>60%)"
          expr: |-
            sum(rate(app_middleware_rate_limit_latency_count{xgen_app=~"mms-api-public", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status=~"SHADOW_REJECTED|REJECTED"}[1h])) by (xgen_environment) /
            sum(rate(app_middleware_rate_limit_latency_count{xgen_app=~"mms-api-public", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1h])) by (xgen_environment) > 0.6
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: spike in rejected requests (>60%) in ({{ $labels.xgen_environment }}) environment.
            runbook_url: http://go/rate-limiting-runbook
    - name: api_rate_limit_redis_circuitbreaker_open_nonprod
      rules:
        - alert: "(NON-PROD) rate limit redis circuitbreaker was open in the last hour"
          expr: |-
            max_over_time(resilience4j_circuitbreaker_state{state=~"open|forced_open", name="ratelimit-redis-breaker", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1h]) > 0
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: rate limit redis circuitbreaker was open in the last 5m in ({{ $labels.xgen_environment }}) environment.
            runbook_url: http://go/rate-limiting-runbook
