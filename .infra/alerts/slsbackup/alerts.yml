---
# see: https://helix.corp.mongodb.com/docs/obs/alerting/onboarding/
pagerdutyService:
  enabled: true
  name: sls-backup
  fullnameOverride: sls-backup
  escalationPolicyName: sls-backup

alertmanagerConfig:
  enabled: true
  route:
    groupBy:
      - alertname
      - severity
      - xgen_environment # for pagerdutyConfig.description below
  pagerdutyConfig:
    # Whether to resolve the alert to pagerduty.
    # We set this to false so alerts stay open until someone investigates. And to avoid flapping.
    sendResolved: false

    # A short one line description of the alert
    # Override because we want these to prepend [SLSBackup] tag
    description: |-
      [{{ .Status | toUpper}} {{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{ end }}]
      [SlsBackup][{{ .GroupLabels.xgen_environment | title }}]{{ .GroupLabels.alertname }}
       ({{ with .CommonLabels.Remove .GroupLabels.Names }}{{ .Values | join " " }}{{ end }})

autoConfigAlertRouting: true

# see: https://github.com/xgen-cloud/helm-charts/blob/master/charts/xgen-metrics-library/README.md#setting-your-slo-with-prometheusservicelevels
prometheusServiceLevel:
  slos:
    ######
    # SLOs
    ######

    - name: slsbackup-snapshot-success-rate
      objective: 99.9
      description: "Monitors the ratio of snapshots that we successfully take after retries."
      sli:
        events:
          errorQuery: >
            sum(
              increase(
                mms_backup_ds_snapshots_completed_total{
                  namespace="{{ .Release.Namespace }}",status="failure"
                }[{{ .window }}]
              )
            ) by(namespace,job,xgen_owner,xgen_environment,xgen_region)
          totalQuery: >
            sum(
              increase(
                mms_backup_ds_snapshots_completed_total{
                  namespace="{{ .Release.Namespace }}"
                }[{{ .window }}]
              )
            ) by(namespace,job,xgen_owner,xgen_environment,xgen_region)
      alerting:
        name: "[SLO] Snapshot Success Rate"
        labels:
          runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/385849618/Backup+DS+Playbooks#BackupDSPlaybooks-%5BSLO%5DSnapshotFailures"
        pageAlert:
          disable: true
        ticketAlert:
          labels:
            severity: warning

    - name: slsbackup-restore-success-rate
      objective: 99
      description: "Monitors the ratio of restores to DS clusters that succeed."
      sli:
        events:
          errorQuery: >
            sum(
              increase(
                mms_cps_restore_completed_total{
                  namespace="{{ .Release.Namespace }}",
                  status="failed",
                  delivery_type="SLS"
                }[{{ .window }}]
              )
            ) by(namespace,job,xgen_owner,xgen_environment,xgen_region)
          totalQuery: >
            sum(
              increase(
                mms_cps_restore_completed_total{
                  namespace="{{ .Release.Namespace }}",
                  delivery_type="SLS"
                }[{{ .window }}]
              )
            ) by(namespace,job,xgen_owner,xgen_environment,xgen_region)
      alerting:
        name: "Restore Success Rate"
        labels:
          runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/385849618/Backup+DS+Playbooks#BackupDSPlaybooks-%5BSLO%5DRestoreFailures"
        pageAlert:
          disable: true
        ticketAlert:
          labels:
            severity: warning

    - name: slsbackup-recovery-time
      objective: 90
      description: "Monitors ratio of restores that complete in under 15m, our Recovery Time Objective."
      sli:
        events:
          # this is a pretty convoluted query because we cannot use the raw Sloth SLI:
          # https://sloth.dev/examples/default/raw-sli/
          errorQuery: >
            max(
              1 - histogram_share(
                900,
                mms_backup_ds_restore_duration_seconds_bucket{
                  namespace="{{ .Release.Namespace }}",
                }
              )
            ) by(namespace,job,xgen_owner,xgen_environment,xgen_region)
          totalQuery: >
            max(
              clamp_max(
                increase(
                  mms_backup_ds_restore_duration_seconds_bucket{
                    namespace="{{ .Release.Namespace }}",
                  }
                ),
                1
              )
            ) by(namespace,job,xgen_owner,xgen_environment,xgen_region)
      alerting:
        name: "[SLO] Recovery Time"
        labels:
          runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/385849618/Backup+DS+Playbooks#BackupDSPlaybooks-%5BSLO%5DRecoveryTime"
        pageAlert:
          disable: true
        ticketAlert:
          labels:
            severity: warning

vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"

  groups:
    ######
    # Alerts
    ######

    - name: slsbackup-alerts
      rules:
        ######
        # gRPC
        ######

        - alert: "[gRPC] Server Failures"
          expr: >
            sum(
              increase(
                grpc_server_completed_duration_seconds_count{
                  namespace="{{ .Release.Namespace }}",
                  xgen_app="slsbackup",
                  grpc_method=~".+SlsBackup.+",
                  grpc_status!~"(OK|NOT_FOUND)"
                }[10m]
              )
            ) by (grpc_method) > 0
          labels:
            severity: warning
          annotations:
            summary: "The SlsBackup gRPC server is returning non-OK status responses."
            description: "Calls to the SlsBackup gRPC service are failing. These might be from MMS or SLS."
            runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/385849618/Backup+DS+Playbooks#BackupDSPlaybooks-%5BgRPC%5DCallFailures"

        - alert: "[gRPC] Client Failures"
          expr: >
            sum(
              increase(
                mms_grpc_client_calls_total{
                  namespace="{{ .Release.Namespace }}",
                  grpc_service="com.xgen.cloud.services.slsbackup.v1.SlsBackupService",
                  grpc_status!~"(OK|NOT_FOUND)"
                }[10m]
              )
            ) by (grpc_method) > 0
          labels:
            severity: warning
          annotations:
            summary: "Client calls to SlsBackup are failing."
            description: "Calls to the SlsBackup gRPC service are failing. These are likely from MMS."
            runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/385849618/Backup+DS+Playbooks#BackupDSPlaybooks-%5BgRPC%5DCallFailures"

        ######
        # Argo
        ######

        - alert: "[Argo] Deployment Workflow Failure"
          expr: >
            sum(
              increase(
                argo_workflows_deployment_workflow_execution_total{
                  xgen_environment="mgmt",
                  app="slsbackup",
                  namespace="{{ .Release.Namespace }}",
                  status!="Succeeded"
                }[1h]
              )
            ) > 0
          labels:
            severity: warning
          annotations:
            summary: "SlsBackup Argo Workflow Execution Failure"
            description: "An Argo Workflow Execution failed, we failed to deploy SlsBackup."
            runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/385849618/Backup+DS+Playbooks#BackupDSPlaybooks-%5BArgo%5DDeploymentWorkflowFailures"

        ######
        # Pods
        ######

        - alert: "[Pods] Not Ready"
          expr: >
            (
              kube_deployment_status_replicas_ready{
                job="kube-state-metrics",
                namespace="{{ .Release.Namespace }}",
              }
                /
              kube_deployment_status_replicas
            ) < 0.5
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: "Less than half the number of desired SlsBackup pods are ready."
            description: "The number of ready SlsBackup pods is less than half the expected number for at least 10m."
            runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/385849618/Backup+DS+Playbooks#BackupDSPlaybooks-%5BPods%5DNotReady"

        - alert: "[Pods] CrashLoopBackoff"
          expr: >
            increase(
              kube_pod_container_status_waiting_reason{
                namespace="{{ .Release.Namespace }}",
                reason!="PodInitializing",
                namespace=~"slsbackup.*"
              }
            ) > 0
          labels:
            severity: warning
          annotations:
            summary: "SlsBackup pods are entering a crash loop."
            description: "The SlsBackup process is crashing causing a crash loop. The process is either not starting or not stable."
            runbook_url: "https://wiki.corp.mongodb.com/spaces/MMS/pages/385849618/Backup+DS+Playbooks#BackupDSPlaybooks-%5BPods%5DCrashLoopBackoff"

    ######
    # Recording Rules
    ######

    - name: slsbackup-recording-rules
      interval: 2m
      rules:
        ######
        # gRPC
        ######

        # gRPC call rate by method.
        - record: "grpc_method:grpc_server_completed_duration_seconds_count:sum"
          expr: >
            sum(
              grpc_server_completed_duration_seconds_count{xgen_app="slsbackup"}
            ) by(xgen_environment,xgen_region,grpc_method,grpc_status)

        # gRPC call latency (avg) by method.
        - record: "grpc_method:grpc_server_completed_duration_seconds:avg"
          expr: >
            sum(
              grpc_server_completed_duration_seconds_sum{app="slsbackup",grpc_status="OK"}
            ) by(xgen_environment,xgen_region,grpc_method)
              /
            sum(
              grpc_server_completed_duration_seconds_count{app="slsbackup",grpc_status="OK"}
            ) by(xgen_environment,xgen_region,grpc_method)

        ######
        # Pods
        ######

        # Max CPU usage (%) by pod.
        - record: "pod:container_cpu_usage_seconds_total:ratio"
          expr: >
            sum(
              container_cpu_usage_seconds_total{container="slsbackup"}
            ) by(xgen_environment,xgen_region,pod)
              /
            sum(
              container_spec_cpu_shares{container="slsbackup"}
                /
              container_spec_cpu_period{container="slsbackup"}
            ) by(xgen_environment,xgen_region,pod)

        # Max memory usage (%) by pod.
        - record: "pod:container_memory_usage_bytes:max"
          expr: >
            max(
              container_memory_usage_bytes{container="slsbackup"}
                /
              container_spec_memory_limit_bytes{container="slsbackup"}
            ) by(xgen_environment,xgen_region,pod)

        ######
        # DB
        ######

        # P90 Latency of queries to the DB by dao/method.
        - record: "method_name:slsbackup_dao_query_duration_seconds_bucket:sum"
          expr: >
            sum(
              {__name__=~"mms_slsbackup_.+dao_query_duration_seconds_bucket"}
            ) by (xgen_environment, xgen_region, le, class_name, method_name)

        # Requests per second to the DB by dao/method.
        - record: "method_name:slsbackup_dao_query_duration_seconds_count:sum"
          expr: >
            sum(
              {__name__=~"mms_slsbackup_.+dao_query_duration_seconds_count"}
            ) by (xgen_environment, xgen_region, le, class_name, method_name)
