---
pagerdutyService:
  enabled: true
  name: mms-nds-prod-gov
  fullnameOverride: mms-nds-prod-gov
  escalationPolicyName: Atlas Clusters Lead
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
  legacyEmailConfig:
    enabled: true
    externalSecretsComponent: core-systems-alerts
    to: <EMAIL>
prometheusRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: plan_execution_evaluate_attempts_alert_rules
      rules:
        - alert: (Prod Gov) verifyAutomationAgentIsStarted attempts expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'verifyAutomationAgentIsStarted' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 15"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"verifyAutomationAgentIsStarted attempts expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27verifyAutomationAgentIsStarted%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) verifyVirtualMachineIsInState attempts expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'verifyVirtualMachineIsInState' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"verifyVirtualMachineIsInState attempts expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27verifyVirtualMachineIsInState%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Failed to execute ACME order
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"No attempts remaining for 'executeOrder'\", log_class=\"EvaluateAttempts\", log_method=\"evaluateAttempt\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Failed to execute ACME order\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+evaluateAttempt+No+attempts+remaining+for+%27executeOrder%27&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) waitForGoalState expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForGoalState' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"waitForGoalState expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForGoalState%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) waitForAllProcessHealthy expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForAllProcessesHealthy' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"waitForAllProcessHealthy expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForAllProcessesHealthy%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) waitForHostGoalState expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForHostGoalState' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"waitForHostGoalState expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForHostGoalState%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) waitForAccess expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForAccess' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"waitForAccess expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForAccess%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) RunLinearizableReadStepWaitAutomation_singlejob expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"RunLinearizableReadStepWaitAutomation_singlejob expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27RunLinearizableReadStepWaitAutomation_singlejob%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) RunLinearizableReadStepWaitAutomation_retry expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"RunLinearizableReadStepWaitAutomation_retry expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27RunLinearizableReadStepWaitAutomation_retry%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) waitForFileSystemResize expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForFileSystemResize' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 10"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"waitForFileSystemResize expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForFileSystemResize%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'initializeMongosync' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\", xgen_environment=\"prod-gov\"}) >= 10"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Attempts for 'initializeMongosync' expired | hasAttemptTimePassed | EvaluateAttempts\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27initializeMongosync%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too Many ACME order not READY
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'Check ACME Order status READY' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too Many ACME order not READY\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27Check+ACME+Order+status+READY%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too Many ACME order not VALID
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'Check ACME Order status VALID' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too Many ACME order not VALID\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27Check+ACME+Order+status+VALID%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many EBS volume creation failure
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'verifyEBSVolumeIsAvailable' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many EBS volume creation failure\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27verifyEBSVolumeIsAvailable%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Too many 'waitForCpsStreamingRestoreJobs' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForCpsStreamingRestoreJobs' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'waitForCpsStreamingRestoreJobs' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForCpsStreamingRestoreJobs%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many createACMEOrder failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"No attempts remaining for 'createACMEOrder'\", log_method=\"evaluateAttempt\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many createACMEOrder failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+evaluateAttempt+No+attempts+remaining+for+%27createACMEOrder%27&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many ACME reserveChallengeProcessingRights failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'reserveChallengeProcessingRights' expired\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many ACME reserveChallengeProcessingRights failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27reserveChallengeProcessingRights%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) (Possible Azure Outage) Too many verifyAzureResourceIsInState failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'verifyAzureResourceIsInState' expired\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"(Possible Azure Outage) Too many verifyAzureResourceIsInState failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27verifyAzureResourceIsInState%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) (Possible Azure Outage) Too many verifyAzureVirtualMachineIsInState failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'verifyAzureVirtualMachineIsInState' expired\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"(Possible Azure Outage) Too many verifyAzureVirtualMachineIsInState failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27verifyAzureVirtualMachineIsInState%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) (Possible Azure Outage) Too many verifyInstanceSizeUpdate failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'verifyInstanceSizeUpdate' expired\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 60m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"(Possible Azure Outage) Too many verifyInstanceSizeUpdate failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27verifyInstanceSizeUpdate%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) (Possible Cloud Provider Reboot Issue) Too many isRebootedWaitUptimeDecrease failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'isRebootedWaitUptimeDecrease' expired\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"(Possible Cloud Provider Reboot Issue) Too many isRebootedWaitUptimeDecrease failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27isRebootedWaitUptimeDecrease%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many verifyEC2InstanceIsInState failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'verifyEC2InstanceIsInState' expired\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many verifyEC2InstanceIsInState failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27verifyEC2InstanceIsInState%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many waitForBackupTerminated failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'waitForBackupTerminated' expired\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 720m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many waitForBackupTerminated failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForBackupTerminated%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many confirmHostIPIsReachable failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'confirmHostIPIsReachable' expired\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many confirmHostIPIsReachable failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27confirmHostIPIsReachable%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many verifyInstanceSizeUpdate failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'verifyInstanceSizeUpdate' expired\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many verifyInstanceSizeUpdate failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27verifyInstanceSizeUpdate%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many provisionVirtualMachine failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"No attempts remaining for 'provisionVirtualMachine'\", log_method=\"evaluateAttempt\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many provisionVirtualMachine failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+evaluateAttempt+No+attempts+remaining+for+%27provisionVirtualMachine%27&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many getUsedDiskSpace failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'getUsedDiskSpace' expired\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many getUsedDiskSpace failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27getUsedDiskSpace%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Too Many 'hasLastBatchArrived' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'hasLastBatchArrived' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 500"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too Many 'hasLastBatchArrived' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27hasLastBatchArrived%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Too Many 'waitForRestoreServerReady' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForRestoreServerReady' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 500"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too Many 'waitForRestoreServerReady' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForRestoreServerReady%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Too Many 'backupCursorExtended' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'backupCursorExtended' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 500"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too Many 'backupCursorExtended' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27backupCursorExtended%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Too Many 'waitForGoalConfigVersion' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForGoalConfigVersion' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too Many 'waitForGoalConfigVersion' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForGoalConfigVersion%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many EBS snapshot creation failure
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForEbsSnapshotCompleted' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many EBS snapshot creation failure\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForEbsSnapshotCompleted%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Capacity reservation expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'CapacityReservationRequest' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Capacity reservation expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27CapacityReservationRequest%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Too many 'waitForValidationFirstPing' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForValidationFirstPing' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 60m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'waitForValidationFirstPing' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForValidationFirstPing%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Too many 'executeAutomationAgentRetry' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"No attempts remaining for 'executeAutomationAgentRetry'\", log_method=\"evaluateAttempt\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'executeAutomationAgentRetry' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+evaluateAttempt+No+attempts+remaining+for+%27executeAutomationAgentRetry%27&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Too many 'verifyElasticIpExistAfterCreation' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"No attempts remaining for 'verifyElasticIpExistAfterCreation'\", log_method=\"evaluateAttempt\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'verifyElasticIpExistAfterCreation' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+evaluateAttempt+No+attempts+remaining+for+%27verifyElasticIpExistAfterCreation%27&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Too many 'createDisk' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'createDisk' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'createDisk' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27createDisk%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) reserveLoadBalancerProcessingRights expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'reserveLoadBalancerProcessingRights' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"reserveLoadBalancerProcessingRights expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27reserveLoadBalancerProcessingRights%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Failed to take snapshot for tenant clusters
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"No attempts remaining for 'takeSnapshot'\", log_class=\"EvaluateAttempts\", log_method=\"evaluateAttempt\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Failed to take snapshot for tenant clusters\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+evaluateAttempt+No+attempts+remaining+for+%27takeSnapshot%27&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Failed to import sample dataset
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"No attempts remaining for 'importSampleDataset'\", log_class=\"EvaluateAttempts\", log_method=\"evaluateAttempt\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Failed to import sample dataset\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+evaluateAttempt+No+attempts+remaining+for+%27importSampleDataset%27&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Failed to configure KMS IP Access list
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForKMSIPAccessListConfiguration' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Failed to configure KMS IP Access list\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForKMSIPAccessListConfiguration%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many snapshot failures waiting for second ping
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForSecondPing' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\", xgen_environment=\"prod-gov\"}) >= 10"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many snapshot failures waiting for second ping\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForSecondPing%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) SampleDatasetLoadMove waitForClusterProvisioned attempts expired
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForClusterProvisioned' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"SampleDatasetLoadMove waitForClusterProvisioned attempts expired\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForClusterProvisioned%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many mongosync migrations have timed out in UpdateMongosyncStatusForKubeStep
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'checkMongosyncProgress_kubeJob' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many mongosync migrations have timed out in UpdateMongosyncStatusForKubeStep\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27checkMongosyncProgress_kubeJob%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many mongomirror migrations have timed out in UpdateMongomirrorStatusForKubeStep
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'getMongoMirrorHttpStatusForKube' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many mongomirror migrations have timed out in UpdateMongomirrorStatusForKubeStep\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27getMongoMirrorHttpStatusForKube%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Too many 'startInstance' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'startInstance' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'startInstance' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27startInstance%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_move_alert_rules
      rules:
        - alert: (Prod Gov) DoSnapshotRestoreMove cannot load metrics
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Don't know how to handle exception; rethrowing\", log_method=\"handleException\", log_class=\"Move\", exception_type=\"java.lang.IllegalStateException\", exception_method=\"getProcesses\", exception_class=\"com.xgen.svc.nds.planner.DoSnapshotRestoreMove\", xgen_environment=\"prod-gov\"}) >= 10"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"DoSnapshotRestoreMove cannot load metrics\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+java.lang.IllegalStateException+com.xgen.svc.nds.planner.DoSnapshotRestoreMove+getProcesses&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) AcmeLazyLoadingException
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Don't know how to handle exception; rethrowing\", log_method=\"handleException\", log_class=\"Move\", exception_type=\"org.shredzone.acme4j.exception.AcmeLazyLoadingException\", exception_method=\"runIfReady\", exception_class=\"com.xgen.svc.mms.planner.model.Move\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"AcmeLazyLoadingException\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+org.shredzone.acme4j.exception.AcmeLazyLoadingException+com.xgen.svc.mms.planner.model.Move+runIfReady&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Oplog too short snapshot failure
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"oplog too short despite multiple consecutive attempts to snapshot\", log_method=\"createEBSSnapshot\", log_class=\"Move\", xgen_environment=\"prod-gov\"}) >= 10"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Oplog too short snapshot failure\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+createEBSSnapshot+oplog+too+short+despite+multiple+consecutive+attempts+to+snapshot&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_acmepre_auth_steps_alert_rules
      rules:
        - alert: (Prod Gov) Too many ACME authorization in unexpected state
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Authorization in an unexpected state.\", log_method=\"ensureAuthorizationsInExpectedStates\", log_class=\"ACMEPreAuthSteps\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many ACME authorization in unexpected state\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+ensureAuthorizationsInExpectedStates+Authorization+in+an+unexpected+state.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_awsmove_utility_provider_alert_rules
      rules:
        - alert: (Prod Gov) Too many AWS Capacity error
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Plan failed due to capacity error.\", log_method=\"handlePlanExecutionCloudProviderException\", log_class=\"AWSMoveUtilityProvider\", xgen_environment=\"prod-gov\"}) >= 20"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many AWS Capacity error\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+handlePlanExecutionCloudProviderException+Plan+failed+due+to+capacity+error.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_awsmove_alert_rules
      rules:
        - alert: (Prod Gov) Too many AWS insufficient IP addresses in subnet error.
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Plan failed due to insufficient free private IP addresses in subnet.\", log_method=\"handleAwsException\", log_class=\"AWSMove\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many AWS insufficient IP addresses in subnet error.\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+handleAwsException+Plan+failed+due+to+insufficient+free+private+IP+addresses+in+subnet.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Too many AWS reserve capacity exceeded error.
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Plan failed due to reserve capacity exceeded error.\", log_method=\"handleAwsException\", log_class=\"AWSMove\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many AWS reserve capacity exceeded error.\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+handleAwsException+Plan+failed+due+to+reserve+capacity+exceeded+error.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_wait_for_live_import_complete_step_alert_rules
      rules:
        - alert: (Prod Gov) Too many failed mongomirror live migrations
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Live import failed for all involved replica sets.\", log_method=\"performInternal\", log_class=\"WaitForLiveImportCompleteStep\", xgen_environment=\"prod-gov\"}) >= 10"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many failed mongomirror live migrations\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Live+import+failed+for+all+involved+replica+sets.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_wait_for_mongosync_live_import_complete_step_alert_rules
      rules:
        - alert: (Prod Gov) Too many failed mongosync live migrations
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Live import for Mongosync failed on only replica set in unrestartable way\", log_method=\"performInternal\", log_class=\"WaitForMongosyncLiveImportCompleteStep\", xgen_environment=\"prod-gov\"}) >= 10"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many failed mongosync live migrations\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Live+import+for+Mongosync+failed+on+only+replica+set+in+unrestartable+way&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_wait_for_wtc_snapshot_to_reach_state_alert_rules
      rules:
        - alert: (Prod Gov) Too many WT snapshot failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"The agent encountered an error while opening or querying the cursor for snapshot\", log_method=\"performInternal\", log_class=\"WaitForWtcSnapshotToReachState\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many WT snapshot failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+The+agent+encountered+an+error+while+opening+or+querying+the+cursor+for+snapshot&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_azure_sync_peering_connection_move_alert_rules
      rules:
        - alert: (Prod Gov) Azure Peering Connection Failure
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Error creating peering connection in resource group\", log_method=\"processCreateConnectionData\", log_class=\"AzureSyncPeeringConnectionMove\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Azure Peering Connection Failure\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+processCreateConnectionData+Error+creating+peering+connection+in+resource+group&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_backup_snapshot_utils_alert_rules
      rules:
        - alert: (Prod Gov) Too many snapshots failure with no eligible instances
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"No eligible hardware to take a snapshot from\", log_method=\"pickInstanceIdToSnapshot\", log_class=\"BackupSnapshotUtils\", xgen_environment=\"prod-gov\"}) >= 10"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many snapshots failure with no eligible instances\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+pickInstanceIdToSnapshot+No+eligible+hardware+to+take+a+snapshot+from&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_cps_repl_set_snapshot_move_alert_rules
      rules:
        - alert: (Prod Gov) Too many snapshots failure with missing target hostname
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed getting target hostname to snapshot. Will retry\", log_method=\"performInternal\", log_class=\"CpsReplSetSnapshotMove\", xgen_environment=\"prod-gov\"}) >= 10"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many snapshots failure with missing target hostname\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Failed+getting+target+hostname+to+snapshot.+Will+retry&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_aws_repl_set_snapshotter_alert_rules
      rules:
        - alert: (Prod Gov) Too many EBS Volume IDs not found when trying to snapshot
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"ebsVolumeId not found\", log_method=\"performSnapshotStep\", log_class=\"AwsReplSetSnapshotter\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many EBS Volume IDs not found when trying to snapshot\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performSnapshotStep+ebsVolumeId+not+found&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_cps_sharded_snapshot_move_alert_rules
      rules:
        - alert: (Prod Gov) Too many Shards topology is changing in the middle of a snapshot move
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Shards topology is changing in the middle of a snapshot move, failing the snapshot.\", log_method=\"performInternal\", log_class=\"CpsShardedSnapshotMove\", xgen_environment=\"prod-gov\"}) >= 10"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many Shards topology is changing in the middle of a snapshot move\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Shards+topology+is+changing+in+the+middle+of+a+snapshot+move%2C+failing+the+snapshot.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: "(Prod Gov) Too many 'Backup Cursor is closed while doing an rsync' failures during server maintenance"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move.\", log_method=\"performInternal\", log_class=\"CpsShardedSnapshotMove\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'Backup Cursor is closed while doing an rsync' failures during server maintenance\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Backup+Cursor+is+closed+while+doing+an+rsync.+Invalid+state.+Failing+Snapshot+Move.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_abstract_azure_create_virtual_machine_step_alert_rules
      rules:
        - alert: "(Prod Gov) (Possible Azure Outage) Too many \"Failed to create virtual machine\" failures for Azure"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed to create virtual machine\", log_method=\"performInternal\", log_class=\"AbstractAzureCreateVirtualMachineStep\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 60m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"(Possible Azure Outage) Too many \"Failed to create virtual machine\" failures for Azure\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Failed+to+create+virtual+machine&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_run_linearizable_read_step_alert_rules
      rules:
        - alert: (Prod Gov) Too many RunLinearizableReadStep failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Retrying linearizable read job failed\", log_method=\"performInternal\", log_class=\"RunLinearizableReadStep\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many RunLinearizableReadStep failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Retrying+linearizable+read+job+failed&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_ndsmove_alert_rules
      rules:
        - alert: (Prod Gov) Too many NDSMove capacity errors
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"NDSMove\", log_message=\"Plan failed due to capacity error.\", log_method=\"handleNDSException\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many NDSMove capacity errors\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+handleNDSException+Plan+failed+due+to+capacity+error.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_wait_for_automation_agents_goal_state_step_alert_rules
      rules:
        - alert: (Prod Gov) Too Many Failed Rolling Index Build
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed rolling index build detected\", log_method=\"cleanupFailedRollingIndexBuild\", log_class=\"WaitForAutomationAgentsGoalStateStep\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 60m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too Many Failed Rolling Index Build\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+cleanupFailedRollingIndexBuild+Failed+rolling+index+build+detected&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_ndssync_pause_state_executor_alert_rules
      rules:
        - alert: (Prod Gov) Too Many Instance Resume failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed to RESUME instance\", log_method=\"execute\", log_class=\"NDSSyncPauseStateExecutor\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too Many Instance Resume failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+execute+Failed+to+RESUME+instance&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_do_tenant_to_tenant_restore_move_alert_rules
      rules:
        - alert: (Prod Gov) Too Many Tenant Restore failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Restore failed for cluster\", log_class=\"DoTenantToTenantRestoreMove\", log_method=\"performInternal\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too Many Tenant Restore failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Restore+failed+for+cluster&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_wait_for_data_validation_complete_step_alert_rules
      rules:
        - alert: (Prod Gov) Too Many Data Validation failures
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Last ping date not fresh. Validation did not complete.\", log_method=\"performInternal\", log_class=\"WaitForDataValidationCompleteStep\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too Many Data Validation failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Last+ping+date+not+fresh.+Validation+did+not+complete.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_acmedownload_cert_step_alert_rules
      rules:
        - alert: (Prod Gov) Fail to retrieve and save cert
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Error while trying to retrieve and save cert\", log_class=\"ACMEDownloadCertStep\", log_method=\"performInternal\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Fail to retrieve and save cert\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Error+while+trying+to+retrieve+and+save+cert&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_execute_automation_agent_job_step_alert_rules
      rules:
        - alert: (Prod Gov) TLS Certificate Reload Failed
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times\", log_class=\"ExecuteAutomationAgentJobStep\", log_method=\"performInternal\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 60m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"TLS Certificate Reload Failed\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Automation+agent+job+command%3DROTATE_TLS_CERTIFICATES+failed+more+than+allowed+number+of+times&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) TLS Certificate Reload Timed Out
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time\", log_class=\"ExecuteAutomationAgentJobStep\", log_method=\"performInternal\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 60m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"TLS Certificate Reload Timed Out\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Automation+agent+job+command%3DROTATE_TLS_CERTIFICATES+did+not+complete+in+allowed+time&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_step_alert_rules
      rules:
        - alert: "(Prod Gov) Too many 'isValidationComplete' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Validation completed with resource exhausted error.\", log_method=\"isValidationComplete\", log_class=\"Step\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 60m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'isValidationComplete' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+isValidationComplete+Validation+completed+with+resource+exhausted+error.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_acmeexecute_order_step_alert_rules
      rules:
        - alert: "(Prod Gov) Too many 'ACMEExecuteOrderStep' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Error while trying to submit the given CSR\", log_method=\"executeOrder\", log_class=\"ACMEExecuteOrderStep\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 60m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'ACMEExecuteOrderStep' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+executeOrder+Error+while+trying+to+submit+the+given+CSR&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_nvme_real_handler_alert_rules
      rules:
        - alert: "(Prod Gov) Too many 'Backup Cursor is closed while doing an rsync' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move.\", log_method=\"perform\", log_class=\"NVMeRealHandler\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'Backup Cursor is closed while doing an rsync' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+perform+Backup+Cursor+is+closed+while+doing+an+rsync.+Invalid+state.+Failing+Snapshot+Move.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_sync_backup_settings_move_alert_rules
      rules:
        - alert: "(Prod Gov) Too many 'Failed to initialize PIT settings' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed to initialize PIT settings\", log_method=\"syncPitSettings\", log_class=\"SyncBackupSettingsMove\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 720m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'Failed to initialize PIT settings' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+syncPitSettings+Failed+to+initialize+PIT+settings&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_update_cloud_conf_step_alert_rules
      rules:
        - alert: (Prod Gov) New cloud chef conf expired before it was fetched
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"UpdateCloudConfStep\", log_message=\"New cloud chef conf expired before it was fetched\", log_method=\"performInternal\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"New cloud chef conf expired before it was fetched\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+New+cloud+chef+conf+expired+before+it+was+fetched&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_fast_free_provision_machine_move_alert_rules
      rules:
        - alert: (Prod Gov) FastFreeProvisionMachineMove failed because MTM is provisioning
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Non-provisioned InstanceHardware with expected index for mtm\", log_class=\"FastFreeProvisionMachineMove\", log_method=\"performInternal\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"FastFreeProvisionMachineMove failed because MTM is provisioning\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Non-provisioned+InstanceHardware+with+expected+index+for+mtm&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_gcpsync_private_service_connect_region_group_move_alert_rules
      rules:
        - alert: (Prod Gov) Failed to setup some region groups for endpoint service
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed to setup some region groups for endpoint service\", log_class=\"GCPSyncPrivateServiceConnectRegionGroupMove\", log_method=\"performInternal\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Failed to setup some region groups for endpoint service\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Failed+to+setup+some+region+groups+for+endpoint+service&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_gcpcreate_subnet_step_alert_rules
      rules:
        - alert: "(Prod Gov) Too many 'Max number of regions for GCP container exceeded' failures"
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Max number of regions for GCP container exceeded\", log_class=\"GCPCreateSubnetStep\", log_method=\"performInternal\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too many 'Max number of regions for GCP container exceeded' failures\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+Max+number+of+regions+for+GCP+container+exceeded&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_push_live_import_move_alert_rules
      rules:
        - alert: (Prod Gov) Push based migration failed on the given planner step
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"The push based migration step failed.\", log_class=\"PushLiveImportMove\", log_method=\"printResultLog\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Push based migration failed on the given planner step\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+printResultLog+The+push+based+migration+step+failed.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_wait_for_ping_version_data_step_alert_rules
      rules:
        - alert: (Prod Gov) Too Many failures getting second ping but the MongoDB version or FCV changed
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"waited for a second ping but the MongoDB version or FCV changed\", log_method=\"performInternal\", log_class=\"WaitForPingVersionDataStep\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"Too Many failures getting second ping but the MongoDB version or FCV changed\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+performInternal+waited+for+a+second+ping+but+the+MongoDB+version+or+FCV+changed&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: plan_execution_ndsinitiate_tenant_restore_step_alert_rules
      rules:
        - alert: (Prod Gov) DoUnpauseTenantRestoreMove insertTenantRestore failed
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Error inserting tenant restore for M0 unpause.\", log_method=\"insertTenantRestore\", log_class=\"NDSInitiateTenantRestoreStep\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Execution failure uptick detected: increase in \"DoUnpauseTenantRestoreMove insertTenantRestore failed\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+insertTenantRestore+Error+inserting+tenant+restore+for+M0+unpause.&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: untracked_plan_execution_alert_rules
      rules:
        - alert: (Prod Gov) Untracked Plan Execution failure
          expr: "sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{xgen_environment=\"prod-gov\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'verifyAutomationAgentIsStarted' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'verifyVirtualMachineIsInState' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Don't know how to handle exception; rethrowing\", log_method=\"handleException\", log_class=\"Move\", exception_type=\"java.lang.IllegalStateException\", exception_method=\"getProcesses\", exception_class=\"com.xgen.svc.nds.planner.DoSnapshotRestoreMove\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"No attempts remaining for 'executeOrder'\", log_class=\"EvaluateAttempts\", log_method=\"evaluateAttempt\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForGoalState' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Authorization in an unexpected state.\", log_method=\"ensureAuthorizationsInExpectedStates\", log_class=\"ACMEPreAuthSteps\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Don't know how to handle exception; rethrowing\", log_method=\"handleException\", log_class=\"Move\", exception_type=\"org.shredzone.acme4j.exception.AcmeLazyLoadingException\", exception_method=\"runIfReady\", exception_class=\"com.xgen.svc.mms.planner.model.Move\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForAllProcessesHealthy' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForHostGoalState' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForAccess' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Plan failed due to capacity error.\", log_method=\"handlePlanExecutionCloudProviderException\", log_class=\"AWSMoveUtilityProvider\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Plan failed due to insufficient free private IP addresses in subnet.\", log_method=\"handleAwsException\", log_class=\"AWSMove\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Plan failed due to reserve capacity exceeded error.\", log_method=\"handleAwsException\", log_class=\"AWSMove\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'RunLinearizableReadStepWaitAutomation_singlejob' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'RunLinearizableReadStepWaitAutomation_retry' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForFileSystemResize' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Live import failed for all involved replica sets.\", log_method=\"performInternal\", log_class=\"WaitForLiveImportCompleteStep\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Live import for Mongosync failed on only replica set in unrestartable way\", log_method=\"performInternal\", log_class=\"WaitForMongosyncLiveImportCompleteStep\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'initializeMongosync' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'Check ACME Order status READY' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'Check ACME Order status VALID' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'verifyEBSVolumeIsAvailable' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"The agent encountered an error while opening or querying the cursor for snapshot\", log_method=\"performInternal\", log_class=\"WaitForWtcSnapshotToReachState\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Error creating peering connection in resource group\", log_method=\"processCreateConnectionData\", log_class=\"AzureSyncPeeringConnectionMove\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"No eligible hardware to take a snapshot from\", log_method=\"pickInstanceIdToSnapshot\", log_class=\"BackupSnapshotUtils\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed getting target hostname to snapshot. Will retry\", log_method=\"performInternal\", log_class=\"CpsReplSetSnapshotMove\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"ebsVolumeId not found\", log_method=\"performSnapshotStep\", log_class=\"AwsReplSetSnapshotter\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Shards topology is changing in the middle of a snapshot move, failing the snapshot.\", log_method=\"performInternal\", log_class=\"CpsShardedSnapshotMove\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForCpsStreamingRestoreJobs' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"No attempts remaining for 'createACMEOrder'\", log_method=\"evaluateAttempt\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'reserveChallengeProcessingRights' expired\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'verifyAzureResourceIsInState' expired\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'verifyAzureVirtualMachineIsInState' expired\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'verifyInstanceSizeUpdate' expired\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed to create virtual machine\", log_method=\"performInternal\", log_class=\"AbstractAzureCreateVirtualMachineStep\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'isRebootedWaitUptimeDecrease' expired\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'verifyEC2InstanceIsInState' expired\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'waitForBackupTerminated' expired\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'confirmHostIPIsReachable' expired\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'verifyInstanceSizeUpdate' expired\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"No attempts remaining for 'provisionVirtualMachine'\", log_method=\"evaluateAttempt\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"Attempts for 'getUsedDiskSpace' expired\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Retrying linearizable read job failed\", log_method=\"performInternal\", log_class=\"RunLinearizableReadStep\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"NDSMove\", log_message=\"Plan failed due to capacity error.\", log_method=\"handleNDSException\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed rolling index build detected\", log_method=\"cleanupFailedRollingIndexBuild\", log_class=\"WaitForAutomationAgentsGoalStateStep\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'hasLastBatchArrived' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForRestoreServerReady' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'backupCursorExtended' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed to RESUME instance\", log_method=\"execute\", log_class=\"NDSSyncPauseStateExecutor\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Restore failed for cluster\", log_class=\"DoTenantToTenantRestoreMove\", log_method=\"performInternal\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForGoalConfigVersion' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Last ping date not fresh. Validation did not complete.\", log_method=\"performInternal\", log_class=\"WaitForDataValidationCompleteStep\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForEbsSnapshotCompleted' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"oplog too short despite multiple consecutive attempts to snapshot\", log_method=\"createEBSSnapshot\", log_class=\"Move\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Error while trying to retrieve and save cert\", log_class=\"ACMEDownloadCertStep\", log_method=\"performInternal\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'CapacityReservationRequest' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Automation agent job command=ROTATE_TLS_CERTIFICATES failed more than allowed number of times\", log_class=\"ExecuteAutomationAgentJobStep\", log_method=\"performInternal\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Automation agent job command=ROTATE_TLS_CERTIFICATES did not complete in allowed time\", log_class=\"ExecuteAutomationAgentJobStep\", log_method=\"performInternal\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForValidationFirstPing' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Validation completed with resource exhausted error.\", log_method=\"isValidationComplete\", log_class=\"Step\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Error while trying to submit the given CSR\", log_method=\"executeOrder\", log_class=\"ACMEExecuteOrderStep\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move.\", log_method=\"perform\", log_class=\"NVMeRealHandler\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed to initialize PIT settings\", log_method=\"syncPitSettings\", log_class=\"SyncBackupSettingsMove\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Backup Cursor is closed while doing an rsync. Invalid state. Failing Snapshot Move.\", log_method=\"performInternal\", log_class=\"CpsShardedSnapshotMove\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"No attempts remaining for 'executeAutomationAgentRetry'\", log_method=\"evaluateAttempt\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"EvaluateAttempts\", log_message=\"No attempts remaining for 'verifyElasticIpExistAfterCreation'\", log_method=\"evaluateAttempt\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_class=\"UpdateCloudConfStep\", log_message=\"New cloud chef conf expired before it was fetched\", log_method=\"performInternal\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Non-provisioned InstanceHardware with expected index for mtm\", log_class=\"FastFreeProvisionMachineMove\", log_method=\"performInternal\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'createDisk' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Failed to setup some region groups for endpoint service\", log_class=\"GCPSyncPrivateServiceConnectRegionGroupMove\", log_method=\"performInternal\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Max number of regions for GCP container exceeded\", log_class=\"GCPCreateSubnetStep\", log_method=\"performInternal\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"The push based migration step failed.\", log_class=\"PushLiveImportMove\", log_method=\"printResultLog\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'reserveLoadBalancerProcessingRights' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"No attempts remaining for 'takeSnapshot'\", log_class=\"EvaluateAttempts\", log_method=\"evaluateAttempt\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"No attempts remaining for 'importSampleDataset'\", log_class=\"EvaluateAttempts\", log_method=\"evaluateAttempt\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"waited for a second ping but the MongoDB version or FCV changed\", log_method=\"performInternal\", log_class=\"WaitForPingVersionDataStep\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForKMSIPAccessListConfiguration' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForSecondPing' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Error inserting tenant restore for M0 unpause.\", log_method=\"insertTenantRestore\", log_class=\"NDSInitiateTenantRestoreStep\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'waitForClusterProvisioned' expired\", log_class=\"EvaluateAttempts\", log_method=\"hasAttemptTimePassed\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'checkMongosyncProgress_kubeJob' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'getMongoMirrorHttpStatusForKube' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\"} unless mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message=\"Attempts for 'startInstance' expired\", log_method=\"hasAttemptTimePassed\", log_class=\"EvaluateAttempts\"}) >= 1"
          for: 14m
          labels:
            severity: warning
          annotations:
            summary: Untracked Plan Execution failure occurred
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: "https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+\"{{ reReplaceAll \" \" \"%20\" $labels.log_message }}\"+{{$labels.log_method}}&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now"
    - name: plan_generation_alert_rules
      rules:
        - alert: (Prod Gov) Database outage - Too many MongoSocketReadException
          expr: "sum by (exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"com.mongodb.MongoSocketReadException\", xgen_environment=\"prod-gov\"}) >= 15"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Generation failure uptick detected: increase in \"Database outage - Too many MongoSocketReadException\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+com.xgen.svc.nds.svc.planning.NDSPlanningSvc-GroupPlanning+com.mongodb.MongoSocketReadException&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Database outage - Too many MongoTimeoutException
          expr: "sum by (exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"com.mongodb.MongoTimeoutException\", exception_class=\"com.mongodb.internal.connection.DefaultConnectionPool\", exception_method=\"createTimeoutException\", xgen_environment=\"prod-gov\"}) >= 15"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Generation failure uptick detected: increase in \"Database outage - Too many MongoTimeoutException\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+com.xgen.svc.nds.svc.planning.NDSPlanningSvc-GroupPlanning+com.mongodb.MongoTimeoutException&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Database outage - Too many MongoQueryException
          expr: "sum by (exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"com.mongodb.MongoQueryException\", xgen_environment=\"prod-gov\"}) >= 15"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Generation failure uptick detected: increase in \"Database outage - Too many MongoQueryException\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+com.xgen.svc.nds.svc.planning.NDSPlanningSvc-GroupPlanning+com.mongodb.MongoQueryException&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) Database outage - Too many MongoCommandException
          expr: "sum by (exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"com.mongodb.MongoCommandException\", xgen_environment=\"prod-gov\"}) >= 15"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Generation failure uptick detected: increase in \"Database outage - Too many MongoCommandException\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+com.xgen.svc.nds.svc.planning.NDSPlanningSvc-GroupPlanning+com.mongodb.MongoCommandException&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) PitEncruptionUtil Encryption At Rest error
          expr: "sum by (exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"java.lang.IllegalStateException\", exception_class=\"com.xgen.svc.nds.util.PitEncryptionUtil\", exception_method=\"shouldEnableOplogEncryption\", xgen_environment=\"prod-gov\"}) >= 50"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Generation failure uptick detected: increase in \"PitEncruptionUtil Encryption At Rest error\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+com.xgen.svc.nds.svc.planning.NDSPlanningSvc-GroupPlanning+java.lang.IllegalStateException&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod Gov) GCPInstancePowerCycleMove initialization failed
          expr: "sum by (exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"java.lang.IllegalArgumentException\", exception_class=\"com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\", exception_method=\"initialize\", xgen_environment=\"prod-gov\"}) >= 5"
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "Plan Generation failure uptick detected: increase in \"GCPInstancePowerCycleMove initialization failed\" failures"
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+com.xgen.svc.nds.svc.planning.NDSPlanningSvc-GroupPlanning+java.lang.IllegalArgumentException&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
    - name: untracked_plan_generation_alert_rules
      rules:
        - alert: (Prod Gov) Untracked Plan Generation failure
          expr: "sum by (exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_planning_failures_total:increase15m{xgen_environment=\"prod-gov\"} unless mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"com.mongodb.MongoSocketReadException\"} unless mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"com.mongodb.MongoTimeoutException\", exception_class=\"com.mongodb.internal.connection.DefaultConnectionPool\", exception_method=\"createTimeoutException\"} unless mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"com.mongodb.MongoQueryException\"} unless mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"com.mongodb.MongoCommandException\"} unless mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"java.lang.IllegalStateException\", exception_class=\"com.xgen.svc.nds.util.PitEncryptionUtil\", exception_method=\"shouldEnableOplogEncryption\"} unless mms_nds::mms_nds_planner_planning_failures_total:increase15m{exception_type=\"java.lang.IllegalArgumentException\", exception_class=\"com.xgen.svc.nds.gcp.planner.GCPInstancePowerCycleMove\", exception_method=\"initialize\"}) >= 1"
          for: 14m
          labels:
            severity: warning
          annotations:
            summary: Untracked Plan Generation failure occurred
            description: "Error log: \"{{ $labels.log_message }}\" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }})."
            splunk_url: "https://splunk.corp.mongodbgov.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+com.xgen.svc.nds.svc.planning.NDSPlanningSvc-GroupPlanning+{{$labels.exception_type}}&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now"
