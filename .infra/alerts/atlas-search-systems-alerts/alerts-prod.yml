---
# yamllint disable rule:quoted-strings
pagerdutyService:
  enabled: true
  escalationPolicyName: Search Systems- Prometheus alert escalation policy
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
  pagerdutyConfig:
    sendResolved: false
vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: atlas-search-systems-alerts
      rules:
        - alert: Concurrent conf calls approaching limit
          expr: (avg_over_time(mms_circuitbreaker_semaphore_active_permits{name="search-confCall",namespace="{{ .Release.Namespace }}"}[1m]) > 80)
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: High number of concurrent search conf calls
            description: "High number of concurrent search conf calls over last 10 mins. Requests
            might fail, control plane server might be overloaded."
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=3
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#heading=h.r89jqiwlr5hi
        - alert: Concurrent conf calls breached threshold
          expr: (avg_over_time(mms_circuitbreaker_semaphore_active_permits{name="search-confCall",namespace="{{ .Release.Namespace }}"}[1m]) >= 100)
          for: 30m
          labels:
            severity: critical
          annotations:
            summary: Conf calls failing due to high number of concurrent conf calls.
            description: "Concurrent search conf calls >= 100 over last 30 mins. Conf calls will fail
            as control plane server is overloaded."
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=3
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#heading=h.r89jqiwlr5hi
        - alert: Increased conf call error rate
          expr: sum(rate(mms_nds_fts_config_api_error_total{namespace="{{ .Release.Namespace }}"}[5m])) / sum(rate(mms_nds_fts_config_api_request_total{namespace="{{ .Release.Namespace }}"}[5m])) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: High conf call error rate
            description: Over 10% of conf call requests returned an error over last 5 mins.
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=5
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#heading=h.wmzpdfpylo7u
        - alert: Increased conf call error rate (high urgency)
          expr: sum(rate(mms_nds_fts_config_api_error_total{namespace="{{ .Release.Namespace }}"}[5m])) / sum(rate(mms_nds_fts_config_api_request_total{namespace="{{ .Release.Namespace }}"}[5m])) > 0.4
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: High conf call error rate (high urgency)
            description: Over 40% of conf call requests returned an error over last 5 mins.
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=5
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#heading=h.wmzpdfpylo7u
        - alert: Slow conf calls increased
          expr: sum(increase(mms_nds_fts_conf_call_api_duration_seconds_bucket{namespace="{{ .Release.Namespace }}",le="+Inf"}[1m]))
            - sum(increase(mms_nds_fts_conf_call_api_duration_seconds_bucket{namespace="{{ .Release.Namespace }}",le="10.0"}[1m])) > 10
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: High number of slow conf calls
            description: There are over 10 slow conf call requests (> 10s) for 10 mins.
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?viewPanel=15
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#heading=h.gvbzd1xt36bq
        - alert: Search Gateway Servers Are Down
          expr: sum(up{endpoint="metrics-port", app="mms-search-gateway", namespace="{{ .Release.Namespace }}"}) == 0
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: Search Gateway Servers are down for 10 minutes.
            description: There is no up instance for search gateway servers for 10 mins
            grafana_url: https://grafana.corp.mongodb.com/d/P3ysMrNSz/search-index-management?orgId=20&from=now-2d&to=now&viewPanel=2
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.wiiamgjn6g51#heading=h.rczdusar92yt
        - alert: Short-living Search Gateway Servers are detected
          expr: count((time() - process_start_time_seconds{namespace="{{ .Release.Namespace }}", container="mms-search-gateway", job="mms-prod/mms-search-gateway"}) < 60 * 20) >= 5
          for: 40m
          labels:
            severity: warning
          annotations:
            summary: Short-living Search Gateway nodes are detected.
            description: There are more than 5 short-living (less than 20m) Search Gateway nodes for more than 40m.
            grafana_url: https://grafana.corp.mongodb.com/d/P3ysMrNSz/search-index-management?orgId=20&from=now-2d&to=now&viewPanel=2
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.wiiamgjn6g51#heading=h.6gr7d6isyzmw
        - alert: Search Gateway CPU usage above 80%
          expr: sum(rate(container_cpu_usage_seconds_total{namespace="{{ .Release.Namespace }}", container="mms-search-gateway"}[1m])) by (container) / sum(container_spec_cpu_quota{namespace="{{ .Release.Namespace }}", container="mms-search-gateway"} / container_spec_cpu_period{namespace="{{ .Release.Namespace }}", container="mms-search-gateway"}) by (container) > 0.8
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: Search Gateway CPU usage above 80%.
            description: CPU consumption of nore or more Search Gateway nodes is above 80%.
            grafana_url: https://grafana.corp.mongodb.com/d/9XVK4n_7SG/system-metrics-search-gateway-and-search-xds?orgId=20&from=now-6h&to=now&var-datasource=VictoriaMetrics&var-xgen_environment=prod&var-namespace=mms-prod&var-provider=All&var-region=All&var-xgen_kube_cluster=All&var-pod_prefix=mms-search-gateway&var-pod=All&var-container=mms-search-gateway&var-job=mms-prod%2Fmms-search-gateway&viewPanel=16
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.wiiamgjn6g51#heading=h.a3eescrdfqo7
        - alert: Search Gateway Low Available Memory
          expr: |
            (
              1 - (
                sum(jvm_memory_bytes_used{namespace="{{ .Release.Namespace }}", container="mms-search-gateway"}) by (container)
                /
                sum(jvm_memory_bytes_max{namespace="{{ .Release.Namespace }}", container="mms-search-gateway"}) by (container)
              )
            ) * 100 < 10
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: "Search Gateway Low Available Memory"
            description: "Available memory for Search Gateway nodes has dropped below 10% for over 30 minutes."
            runbook_url: "https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.wiiamgjn6g51#heading=h.zb1ogod2ph79"
            grafana_url: "https://grafana.corp.mongodb.com/d/9XVK4n_7SG/system-metrics-search-gateway-and-search-xds?orgId=20&from=now-1h&to=now&var-datasource=VictoriaMetrics&var-xgen_environment=prod&var-namespace=mms-prod&var-provider=All&var-region=All&var-xgen_kube_cluster=All&var-pod_prefix=mms-search-gateway&var-pod=All&var-container=mms-search-gateway&var-job=mms-prod%2Fmms-search-gateway&viewPanel=20"
        - alert: Search Index Management High Error Rate
          expr: sum(rate(atlassearch_search_index_management_command_error_total{app="mms-search-gateway",namespace="{{ .Release.Namespace }}"}[5m]))
            / sum(rate(atlassearch_search_index_management_command_request_total{app="mms-search-gateway",namespace="{{ .Release.Namespace }}"}[5m])) > 0.1
          labels:
            severity: warning
          annotations:
            summary: There are too many errors in search index management server
            description: Search Index Management experience more than 10% error rate for 5 mins
            grafana_url: https://grafana.corp.mongodb.com/d/P3ysMrNSz/search-index-management?orgId=20&from=now-2d&to=now&viewPanel=10
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.1x3j34le6pu7#heading=h.vg9fk7czxsfc
        - alert: Search Index Management High Error Rate (high urgency)
          expr: sum(rate(atlassearch_search_index_management_command_error_total{app="mms-search-gateway",namespace="{{ .Release.Namespace }}"}[5m]))
            / sum(rate(atlassearch_search_index_management_command_request_total{app="mms-search-gateway",namespace="{{ .Release.Namespace }}"}[5m])) > 0.4
          labels:
            severity: critical
          annotations:
            summary: There are too many errors in search index management server (high urgency)
            description: Search Index Management experience more than 40% error rate for 5 mins
            grafana_url: https://grafana.corp.mongodb.com/d/P3ysMrNSz/search-index-management?orgId=20&from=now-2d&to=now&viewPanel=10
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.1x3j34le6pu7#heading=h.vg9fk7czxsfc
        - alert: Search Index Management High P95 Latency
          expr: histogram_quantile(0.95, sum(rate(atlassearch_search_index_management_command_latency_seconds_bucket{namespace="{{ .Release.Namespace }}"}[5m])) by (le)) > 0.8
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: Search Index Management P95 latency is too high
            description: Search Index Management P95 latency is greater than 0.8 seconds for 30 mins
            grafana_url: https://grafana.corp.mongodb.com/d/P3ysMrNSz/search-index-management?orgId=20&viewPanel=14
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.1x3j34le6pu7
        - alert: FAILED indexes due to replication errors.
          expr: sum(increase(mms_nds_search_index_failed_transitions_total{namespace="{{ .Release.Namespace }}",failureReason="Replication failed"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            summary: Indexes marked FAILED due to issues with replication.
            description: Indexes were newly marked as FAILED due to replication errors in the last 5 minutes.
            grafana_url: https://grafana.corp.mongodb.com/d/SQ_2MZhIz/atlas-search-data-plane1?orgId=20&viewPanel=74
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.6jboliidy9zm#heading=h.ostvf2yqljpr
        - alert: Stale indexes due to replication errors.
          expr: sum(increase(mms_nds_search_index_stale_transitions_total{namespace="{{ .Release.Namespace }}",staleReason="Replication failed"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            summary: Indexes marked STALE due to issues with replication.
            description: Indexes were newly marked as STALE due to replication errors in the last 5 minutes.
            grafana_url: https://grafana.corp.mongodb.com/d/SQ_2MZhIz/atlas-search-data-plane1?orgId=20&viewPanel=78
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.6jboliidy9zm#heading=h.hbecpdroue4v

        ## Blobstore Credentials Service ##
        - alert: Failing blobstore credential requests
          expr: sum(increase(atlassearch_blobstore_creds_requests_total{namespace="{{ .Release.Namespace }}", status="failure"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: At least one failing blobstore credential request.
            description: At least one failing blobstore credential request for 5m.
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.tpi4ur5dscyk#heading=h.ximv9fbaoj4v
            grafana_url: https://grafana.corp.mongodb.com/d/rHSsRVhNz/fcis-file-copy-initial-sync?orgId=20&viewPanel=4

        - alert: Failing blobstore credential generation
          expr: sum(increase(atlassearch_blobstore_creds_generated_total{namespace="{{ .Release.Namespace }}", status="failure"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: At least one failing blobstore credential generation.
            description: At least one failing blobstore credential request for 5m.
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.tpi4ur5dscyk#heading=h.x737v8sogfty
            grafana_url: https://grafana.corp.mongodb.com/d/rHSsRVhNz/fcis-file-copy-initial-sync?orgId=20&viewPanel=6

        - alert: High volume of STS requests
          expr: sum(increase(atlassearch_blobstore_creds_generated_total{namespace="{{ .Release.Namespace }}", cloudProvider="AWS"}[5m])) > 5 * 60 * 100
          labels:
            severity: warning
          annotations:
            summary: More STS requests made than expected
            description: More STS requests for federation tokens have been made than expected (>100 QPS) which may cause throttling
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.tpi4ur5dscyk#heading=h.u2l9y7j0us6f
            grafana_url: https://grafana.corp.mongodb.com/d/rHSsRVhNz/fcis-file-copy-initial-sync?orgId=20&var-environment=prod&from=now-12h&to=now&viewPanel=6

        - alert: FCIS enabled search nodes are crash looping because of OOMs
          expr: |
            (
              label_replace(
                sum(
                  increase(
                    atlassearch_oom_crash_total{
                      namespace="{{ .Release.Namespace }}",
                      searchHostType="decoupled",
                      xgen_provider="aws"
                    }[15m]
                  )
                ) by(clusterId),
                "clusterId",
                "oid:$1",
                "clusterId",
                "(.*)"
              )
                >
              5
            )
              and on(clusterId)
            (
              label_replace(
                sum(
                  increase(
                    mongot_blobstore_aws_s3_bytesTransferred_total{
                      mmsLabel_isSearchNode="true",
                      namespace="{{ .Release.Namespace }}",
                      xgen_provider="aws"
                    }[15m]
                  )
                ) by(mmsLabel_clusterId),
                "clusterId",
                "$1",
                "mmsLabel_clusterId",
                "(.*)"
              )
                >
              **********
            )
          labels:
            severity: warning
          annotations:
            summary: High OOM crash rate for mongots possibly caused by FCIS.
            description: |
              There were several OOMs within the 15 minutes in cluster {{ $labels.clusterId }} and
              there was substantial S3 data transfer indicating that these OOMs might be caused by
              FCIS.
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.tpi4ur5dscyk#heading=h.nshksdo255gd
            grafana_url: https://grafana.corp.mongodb.com/d/Jh-gpM8Sk/search-cluster-drilldown?orgId=20&var-mmsLabel_clusterId={{ $labels.clusterId }}&from=now-30d&to=now
        - alert: High number of invalid regions for instance size
          expr: sum(increase(atlassearch_invalid_region_for_instance_size_total{namespace="{{ .Release.Namespace }}"}[1h])) > 5
          labels:
            severity: warning
          annotations:
            summary: Increased number of invalid regions for instance size
            description: Number of invalid regions for instance size more than 5 over 1h
            grafana_url: https://grafana.corp.mongodb.com/d/7YE0gNJnz/atlas-search-main?orgId=20&viewPanel=56
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.txq9pgyaro8x#heading=h.wqs0ciqcugox
        - alert: Planning round high P90 duration
          expr: avg(atlassearch_planner_planning_round_duration_seconds{namespace="{{ .Release.Namespace }}", quantile="0.9"}) > 5
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: Search planner high P90 round duration
            description: Search planning round duration p90 greater than 5s for 10m.
            grafana_url: https://grafana.corp.mongodb.com/d/uYQTpAu4z/atlas-search-planner?orgId=20&viewPanel=2
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.txq9pgyaro8x#heading=h.n4voazxmlikf
        - alert: 'Search Index APIs: High proportion of HTTP 5XX statuses'
          expr: (sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app=~"mms-ui|mms-api-public",path=~"^/api/atlas/[\\w.{}/]+/(fts/indexes|fts/analyzers|fts/metrics|search/deployment|search/indexes)[\\w.{}/]*",status="5xx"}[60s]))
            / sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app=~"mms-ui|mms-api-public",path=~"^/api/atlas/[\\w.{}/]+/(fts/indexes|fts/analyzers|fts/metrics|search/deployment|search/indexes)[\\w.{}/]*"}[60s]))) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Too many 5xx responses returned by search index APIs
            description: A large proportion of HTTP requests (>10%) are resulting in 5xx response codes over last 5 mins
            grafana_url: https://grafana.corp.mongodb.com/d/_J-Wu9THz/search-apis?orgId=20&viewPanel=13
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vzjdfk63ta97#heading=h.ldgyui87p4w
        - alert: 'Search Index APIs: High proportion of HTTP 5XX statuses (high urgency)'
          expr: (sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app=~"mms-ui|mms-api-public",path=~"^/api/atlas/[\\w.{}/]+/(fts/indexes|fts/analyzers|fts/metrics|search/deployment|search/indexes)[\\w.{}/]*",status="5xx"}[60s]))
            / sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app=~"mms-ui|mms-api-public",path=~"^/api/atlas/[\\w.{}/]+/(fts/indexes|fts/analyzers|fts/metrics|search/deployment|search/indexes)[\\w.{}/]*"}[60s]))) > 0.4
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Too many 5xx responses returned by search index APIs (high urgency)
            description: A large proportion of HTTP requests (>40%) are resulting in 5xx response codes over last 5 mins
            grafana_url: https://grafana.corp.mongodb.com/d/_J-Wu9THz/search-apis?orgId=20&viewPanel=13
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vzjdfk63ta97#heading=h.ldgyui87p4w
        - alert: 'Search Web APIs: High proportion of HTTP 5XX statuses'
          expr: (sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app=~"mms-ui",path=~"(^/deployment/.*/search/.*|^/nds/clusters/[\\w.{}/]+/(fts|search|data/(indexes|deployment|migration|/databases/{databaseName}/collections/{collectionName}/sampleCollectionFieldNamesAndTypes)).*)",status="5xx"}[60s]))
            / sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app=~"mms-ui",path=~"(^/deployment/.*/search/.*|^/nds/clusters/[\\w.{}/]+/(fts|search|data/(indexes|deployment|migration|/databases/{databaseName}/collections/{collectionName}/sampleCollectionFieldNamesAndTypes)).*)"}[60s]))) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Too many 5xx responses returned by search web APIs
            description: A large proportion of HTTP requests (>10%) are resulting in 5xx response codes over last 5 mins
            grafana_url: https://grafana.corp.mongodb.com/d/_J-Wu9THz/search-apis?orgId=20&viewPanel=24
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vzjdfk63ta97#heading=h.ldgyui87p4w
        - alert: 'Search Web APIs: High proportion of HTTP 5XX statuses (high urgency)'
          expr: (sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app=~"mms-ui",path=~"(^/deployment/.*/search/.*|^/nds/clusters/[\\w.{}/]+/(fts|search|data/(indexes|deployment|migration|/databases/{databaseName}/collections/{collectionName}/sampleCollectionFieldNamesAndTypes)).*)",status="5xx"}[60s]))
            / sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app=~"mms-ui",path=~"(^/deployment/.*/search/.*|^/nds/clusters/[\\w.{}/]+/(fts|search|data/(indexes|deployment|migration|/databases/{databaseName}/collections/{collectionName}/sampleCollectionFieldNamesAndTypes)).*)"}[60s]))) > 0.4
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: Too many 5xx responses returned by search web APIs (high urgency)
            description: A large proportion of HTTP requests (>40%) are resulting in 5xx response codes over last 5 mins
            grafana_url: https://grafana.corp.mongodb.com/d/_J-Wu9THz/search-apis?orgId=20&viewPanel=24
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vzjdfk63ta97#heading=h.ldgyui87p4w
        - alert: 'Search Index APIs: High P90 latency'
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{namespace="{{ .Release.Namespace }}",app=~"mms-ui|mms-api-public",path=~"^/api/atlas/[\\w.{}/]+/(fts/indexes|fts/analyzers|fts/metrics|search/deployment|search/indexes)[\\w.{}/]*"}[60s]))
            / sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app=~"mms-ui|mms-api-public",path=~"^/api/atlas/[\\w.{}/]+/(fts/indexes|fts/analyzers|fts/metrics|search/deployment|search/indexes)[\\w.{}/]*"}[60s]))) > 1 # second
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Search Index APIs P90 latency is too high
            description: Search Index APIs P90 latency is greater than 1 seconds for 5 mins
            grafana_url: https://grafana.corp.mongodb.com/d/_J-Wu9THz/search-apis?orgId=20&viewPanel=26
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vzjdfk63ta97#heading=h.7h1jnolgcbfj
        - alert: Multiple coupled mongots in conf call behind
          expr: count(count by (groupId) (increase(mms_mongot_behind_count_total{namespace="{{ .Release.Namespace }}",searchDeploymentType="coupled"}[30m]) > 1)) >= 3
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: Multiple groups have mongots in the coupled architecture not sending conf calls
            description: More than 3 projects have coupled mongots not sending conf calls for more than 1 hour (as evaluated over last 30 mins).
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=11
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#bookmark=kix.k5f62lh89zqc
        - alert: (Potential high urgency) Multiple dedicated search node mongots in conf call behind
          expr: count(count by (groupId) (increase(mms_mongot_behind_count_total{namespace="{{ .Release.Namespace }}",searchDeploymentType="decoupled",hostsBehind!="1"}[30m]) > 0)) > 0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: Multiple mongots on dedicated search nodes in one or more groups not sending conf calls
            description: At least one dedicated search node has multiple mongots not sending conf calls for more than 1 hour (as evaluated over last 30 mins).
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=11
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#bookmark=kix.6lsvdyluzmoe
        - alert: (Potential high urgency) Dedicated search node mongot in conf call behind, verify no instance degradation
          expr: count(count by (groupId) (increase(mms_mongot_behind_count_total{namespace="{{ .Release.Namespace }}",searchDeploymentType="decoupled",hostsBehind="1"}[45m]) > 1)) > 0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: One dedicated search node mongot in one or more groups not sending conf calls
            description: One dedicated mongot not sending conf calls, fix instance degradation if root cause.
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=11
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#bookmark=kix.ultemix1gx65
        - alert: Search indexes stuck in DELETING
          expr: sum(increase(index_statuses_stuck_total{namespace="{{ .Release.Namespace }}",stuckStatus="DELETE"}[30m])) > 0
          labels:
            severity: warning
          annotations:
            summary: At least one search index is stuck in DELETING state
            description: At least one search index is stuck in DELETING state for more than 1 hour (evaluated over last 30 mins)
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=13
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#heading=h.4d1j87ry93zj
        - alert: Old Search plan may be stuck
          expr: sum by (searchDeploymentId) (atlassearch_long_search_plan_duration_hours{namespace="{{ .Release.Namespace }}", byokRelated=~"(false|)"}) >= 24
          labels:
            severity: warning
          annotations:
            summary: Search plan has been running for over 24 hours
            description: A search plan has been running for over 24 hours and may be stuck
            grafana_url: https://grafana.corp.mongodb.com/d/uYQTpAu4z/atlas-search-planner?viewPanel=28&orgId=20
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.txq9pgyaro8x#heading=h.rsr82hu04edr
        - alert: Increase in number of stuck search plans
          expr: (max_over_time(count by (searchDeploymentId) (increase(atlassearch_long_search_plan_duration_count_total{namespace="{{ .Release.Namespace }}", byokRelated=~"(false|)"}[5m]) > 0)[10m:1m])
            - (count by (searchDeploymentId) (increase(atlassearch_long_search_plan_duration_count_total{namespace="{{ .Release.Namespace }}", byokRelated=~"(false|)"}[5m] offset 10m) > 0)
            or on (searchDeploymentId) 0 * max_over_time(count by (searchDeploymentId) (increase(atlassearch_long_search_plan_duration_count_total{namespace="{{ .Release.Namespace }}", byokRelated=~"(false|)"}[5m]) > 0)[10m:1m]))) > 0
          labels:
            severity: warning
          annotations:
            summary: Increase in number of search plans that have been running for over 24 hours
            description: in addition to existing plans, an additional search plan belonging to a different deployment has been running for over 24 hours
            grafana_url: https://grafana.corp.mongodb.com/d/uYQTpAu4z/atlas-search-planner?viewPanel=28&orgId=20
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.txq9pgyaro8x#heading=h.rsr82hu04edr
        - alert: Search plan may be stuck
          expr: sum by (searchDeploymentId) (rate(atlassearch_long_search_plan_duration_count_total{namespace="{{ .Release.Namespace }}", byokRelated=~"(false|)"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            summary: Search plan has been running for over 24 hours
            description: A search plan has been running for over 24 hours and may be stuck
            grafana_url: https://grafana.corp.mongodb.com/d/uYQTpAu4z/atlas-search-planner?viewPanel=28&orgId=20
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.txq9pgyaro8x#heading=h.rsr82hu04edr
        - alert: Old Search plan may be stuck [BYOK]
          expr: sum by (searchDeploymentId) (atlassearch_long_search_plan_duration_hours{namespace="{{ .Release.Namespace }}", byokRelated="true"}) >= 8
          labels:
            severity: warning
          annotations:
            summary: Search plan for a cluster with enabled BYOK has been running for over 8 hours
            description: A search plan for a cluster with enabled BYOK has been running for over 8 hours and may be stuck
            grafana_url: https://grafana.corp.mongodb.com/d/JJ8Vsv4Hz/byok?orgId=20&var-env=prod&viewPanel=3
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.bx7k0ulv242k#heading=h.nog2328yo9gr
        - alert: Search plan may be stuck [BYOK]
          expr: sum by (searchDeploymentId) (rate(atlassearch_long_search_plan_duration_count_total{namespace="{{ .Release.Namespace }}", byokRelated="true"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            summary: Search plan for a cluster with enabled BYOK has been running for over 8 hours
            description: A search plan for a cluster with enabled BYOK has been running for over 8 hours and may be stuck
            grafana_url: https://grafana.corp.mongodb.com/d/JJ8Vsv4Hz/byok?orgId=20&var-env=prod&viewPanel=3
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.bx7k0ulv242k#heading=h.nog2328yo9gr
        - alert: Critical key rotation overdue
          expr: sum(increase(atlassearch_critical_key_rotation_overdue_total{namespace="{{ .Release.Namespace }}"}[30m])) > 0
          labels:
            severity: warning
          annotations:
            summary: Critical key rotation requested over 8 hours ago, not performed on one or more search nodes.
            description: Critical key rotation was requested more than 8 hours ago and has not been completed for one or more search nodes. Critical key rotation could be caused by changed encryption at rest (BYOK) settings. Please check logs to find details about affected clusters.
            grafana_url: https://grafana.corp.mongodb.com/d/JJ8Vsv4Hz/byok?orgId=20&var-env=prod&viewPanel=5
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.bx7k0ulv242k#heading=h.kvregtrlnde8
        - alert: Key rotation overdue
          expr: sum(increase(atlassearch_key_rotation_overdue_total{namespace="{{ .Release.Namespace }}"}[30m])) > 0
          labels:
            severity: warning
          annotations:
            summary: Key rotation overdue by more than 120 days.
            description: Atlas should rotate mongot keys every 90 days, but the key rotation has not occurred for over 120 days. Please check logs to find details about affected clusters.
            grafana_url: https://grafana.corp.mongodb.com/d/JJ8Vsv4Hz/byok?orgId=20&var-env=prod&viewPanel=5
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.bx7k0ulv242k#heading=h.bekh7d9awdgu
        - alert: Failed plan for a cluster with BYOK
          expr: sum(increase(atlassearch_finished_plan_total{namespace="{{ .Release.Namespace }}", byokRelated="true", wasFailure="true", planResult!="ABANDONED", failureCode!~"(PLAN_ABANDONED|EXPECTED_FAILURE)"}[5m])) > 0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: One or more plans failed for cluster with BYOK
            description: One or more plans failed for cluster with BYOK. Please check logs to find details about affected clusters.
            grafana_url: https://grafana.corp.mongodb.com/d/JJ8Vsv4Hz/byok?orgId=20&var-env=prod&viewPanel=4
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.bx7k0ulv242k#heading=h.aqofl965zltv
        - alert: Many failed plans over the past hour
          expr: sum(increase(atlassearch_finished_plan_total{namespace="{{ .Release.Namespace }}", wasFailure="true", failureCode!="PLAN_ABANDONED", failureCode!="EXPECTED_FAILURE", failureCode!="BLOCKED_BY_OPTIMIZING_DISK"}[1h])) > 10 #replace this?
          labels:
            severity: warning
          annotations:
            summary: More than 10 failed plans over the past hour.
            description: More than 10 plans in the past hour have ended in failure and/or were rolled back, excluding plans that were abandoned.
            grafana_url: https://grafana.corp.mongodb.com/d/uYQTpAu4z/atlas-search-planner?orgId=20&viewPanel=30
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.txq9pgyaro8x#heading=h.6r8stm1dt1uy
        - alert: (Potential high urgency) Too many unexpected mongot crashes
          expr: sum(increase(atlassearch_mongot_crashes_total{category="unknown",namespace="{{ .Release.Namespace }}"}[1h])) > 10
          labels:
            severity: warning
          annotations:
            summary: Too many unexpected mongot crashes
            description: More than 10 unexpected mongot crashes in the last hour
            grafana_url: https://grafana.corp.mongodb.com/d/7YE0gNJnz/atlas-search-main?orgId=20&viewPanel=72
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.6jboliidy9zm#bookmark=id.1u99bdlc3y5
        - alert: Index status failed on newly provisioning node
          expr: sum by (searchDeploymentId) (increase(atlassearch_search_index_status_failed_on_newly_provisioning_node_total{namespace="{{ .Release.Namespace }}"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            summary: Index FAILED on newly provisioning node
            description: One or more indexes statusCode returns failed on newly provisioning node in the last five minutes.
            grafana_url: https://grafana.helix.corp.mongodb.com/d/yfjP6BRIk/atlas-search-migration?orgId=20&var-env=prod&viewPanel=panel-10
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.cs6h1p7gi67g#heading=h.9vcll16x1x6h
        - alert: Leaked items detected
          expr: increase(atlassearch_leaked_items_total{namespace="{{ .Release.Namespace }}"}[5m]) > 0
          labels:
            severity: warning
          annotations:
            summary: Leaked items detected
            description: Leaked items related to search operations have been detected.
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.txq9pgyaro8x#heading=h.puqgup2p9585
            grafana_url: https://grafana.corp.mongodb.com/d/P3ysMrNSz/search-index-management-gateway-service?orgId=20&viewPanel=16
        - alert: Increased search metering check error rate
          expr: sum(increase(mms_search_metering_check_error_total{namespace="{{ .Release.Namespace }}"}[1h])) > 5
          labels:
            severity: warning
          annotations:
            summary: Search metering check is experiencing an increased error rate.
            description: Search metering check errors more than 5 over 1h.
            grafana_url: https://grafana.corp.mongodb.com/d/GpZXwCeVk/atlas-search-billing?orgId=20&viewPanel=2
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.bkei367nvtr8#heading=h.kexygsp0q46e
        - alert: Increased search meter usage submission error rate
          expr: sum(increase(mms_search_metering_submission_error_total{namespace="{{ .Release.Namespace }}"}[30m])) > 5
          labels:
            severity: warning
          annotations:
            summary: Search meter usage submission is experiencing an increased error rate.
            description: Search meter usage submission errors more than 5 over 30m.
            grafana_url: https://grafana.corp.mongodb.com/d/GpZXwCeVk/atlas-search-billing?orgId=20&viewPanel=4
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.bkei367nvtr8#heading=h.477w2cgc62e5
        - alert: Search metering check lapse
          expr: histogram_quantile(0.90, sum(rate(mms_search_metering_check_lapse_hours_bucket{namespace="{{ .Release.Namespace }}"}[1h])) by (le)) > 0
          labels:
            severity: warning
          annotations:
            summary: More than 10% of search deployments had a metering check lapse
            description: More than 10% of search deployments had a metering check lapse in the past 1h.
            grafana_url: https://grafana.corp.mongodb.com/d/GpZXwCeVk/atlas-search-billing?orgId=20&viewPanel=6
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.bkei367nvtr8#heading=h.f6qqopg4mxvg
        - alert: High search metering check lapse
          expr: histogram_quantile(0.99, sum(rate(mms_search_metering_check_lapse_hours_bucket{namespace="{{ .Release.Namespace }}"}[1m])) by (le)) >= 6
          labels:
            severity: warning
          annotations:
            summary: Caught large metering check lapse for search deployments
            description: More than 1% of search deployments had a metering check lapse >= 6h
            grafana_url: https://grafana.corp.mongodb.com/d/GpZXwCeVk/atlas-search-billing?orgId=20&viewPanel=6
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.bkei367nvtr8#heading=h.f6qqopg4mxvg
        - alert: AWS instance state health check failures
          expr: sum(rate(atlassearch_aws_instance_state_health_check_failed_total{namespace="{{ .Release.Namespace }}"}[1h]))
            / (sum(rate(atlassearch_aws_instance_state_health_check_failed_total{namespace="{{ .Release.Namespace }}"}[1h]))
            + sum(rate(atlassearch_aws_instance_state_health_check_completed_total{namespace="{{ .Release.Namespace }}"}[1h]))) > 0.05
          labels:
            severity: warning
          annotations:
            summary: AWS instance state health checks are failing
            description: AWS instance state health check errors more than 5% over 1h
            grafana_url: https://grafana.corp.mongodb.com/d/7YE0gNJnz/atlas-search?viewPanel=53&orgId=20
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.5l51hahfzdzb#heading=h.ogbqvy2xeyzh
        - alert: Search xDS not updating config cache
          expr: count(rate(atlassearch_xds_cache_updater_duration_seconds_count{app="mms-search-xds",namespace="{{ .Release.Namespace }}"}[5m]) > 0.1) < 2
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: The search xDS servers are not updating config cache
            description: Expected 2 search xDS servers are updating config cache every 10 seconds.
            grafana_url: https://grafana.corp.mongodb.com/d/yCMtJtIIk/search-xds-server?orgId=20&viewPanel=4
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.2plixzfpvty1#heading=h.h0cf7ec7j2tj
        - alert: Search xDS too many errors
          expr: increase(sum({app="mms-search-xds", namespace="{{ .Release.Namespace }}", __name__=~"atlassearch_xds_.*_failures_total", __name__!="atlassearch_xds_envoy_status_update_failures_total"})[5m:]) > 10
          labels:
            severity: warning
          annotations:
            summary: There are too many errors in the search xDS servers
            description: Search xDS servers encounter more than 10 errors in 5 minutes.
            grafana_url: https://grafana.corp.mongodb.com/d/yCMtJtIIk/search-xds-server?orgId=20&viewPanel=6
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.2plixzfpvty1#heading=h.j3etenikmadf
