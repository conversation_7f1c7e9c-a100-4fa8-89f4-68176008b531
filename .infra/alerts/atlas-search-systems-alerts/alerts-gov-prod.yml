---
# yamllint disable rule:quoted-strings
pagerdutyService:
  enabled: true
  escalationPolicyName: Search Systems- Prometheus alert escalation policy
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
  pagerdutyConfig:
    sendResolved: false
vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: atlas-search-systems-alerts
      rules:
        - alert: Concurrent conf calls approaching limit
          expr: (max_over_time(mms_circuitbreaker_semaphore_active_permits{name="search-confCall"}[5m]) > 80)
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: High number of concurrent search conf calls
            description: 'High number of concurrent search conf calls over last 5 mins. Requests might fail, control plane server might be overloaded.'
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=3
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#heading=h.r89jqiwlr5hi
        - alert: Concurrent conf calls breached threshold
          expr: (max_over_time(mms_circuitbreaker_semaphore_active_permits{name="search-confCall"}[5m]) >= 100)
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: Conf calls failing due to high number of concurrent conf calls.
            description: 'Concurrent search conf calls >= 100 over last 10 mins. Conf calls will fail as control plane server is overloaded.'
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=3
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#heading=h.r89jqiwlr5hi
        - alert: Increased conf call error rate
          expr: sum(rate(mms_nds_fts_config_api_error_total{namespace="{{ .Release.Namespace }}"}[5m])) / sum(rate(mms_nds_fts_config_api_request_total{namespace="{{ .Release.Namespace }}"}[5m])) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: High conf call error rate
            description: Over 10% of conf call requests returned an error over last 5 mins.
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=5
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#heading=h.wmzpdfpylo7u
        - alert: 'Search Index APIs: High proportion of HTTP 5XX statuses'
          expr: (sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app="mms-ui",path=~"^/api/atlas/[\\w.{}/]+/(fts/indexes|fts/analyzers|fts/metrics|search/deployment|search/indexes)[\\w.{}/]*",status="5xx"}[60s])) / sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app="mms-ui",path=~"^/api/atlas/[\\w.{}/]+/(fts/indexes|fts/analyzers|fts/metrics|search/deployment|search/indexes)[\\w.{}/]*"}[60s]))) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Too many 5xx responses returned by search index APIs
            description: A large proportion of HTTP requests (>10%) are resulting in 5xx response codes over last 5 mins
            grafana_url: https://grafana.corp.mongodb.com/d/iIRPNsi4k/atlas-search-public-api-usage?orgId=20
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vzjdfk63ta97#heading=h.ldgyui87p4w
        - alert: 'Search Index APIs: High P90 latency'
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{namespace="{{ .Release.Namespace }}",app="mms-ui",path=~"^/api/atlas/[\\w.{}/]+/(fts/indexes|fts/analyzers|fts/metrics|search/deployment|search/indexes)[\\w.{}/]*"}[60s])) / sum(rate(java_jersey_http_duration_seconds_count{namespace="{{ .Release.Namespace }}",app="mms-ui",path=~"^/api/atlas/[\\w.{}/]+/(fts/indexes|fts/analyzers|fts/metrics|search/deployment|search/indexes)[\\w.{}/]*"}[60s]))) > 1 # second
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Search Index APIs P90 latency is too high
            description: Search Index APIs P90 latency is greater than 1 seconds for 5 mins
            grafana_url: https://grafana.corp.mongodb.com/d/iIRPNsi4k/atlas-search-public-api-usage?orgId=20
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vzjdfk63ta97#heading=h.7h1jnolgcbfj
        - alert: FAILED indexes due to replication errors.
          expr: (sum(increase(mms_nds_search_index_failed_transitions_total{namespace="{{ .Release.Namespace }}",failureReason="Replication failed"}[1h])) > 3) or (sum(increase(mms_nds_search_index_failed_transitions_total{namespace="{{ .Release.Namespace }}",failureReason="Replication failed"}[1d])) > 5)
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Indexes marked FAILED due to issues with replication.
            description: More than 3 indexes over last hour (or 5 over the last day) were newly marked as FAILED due to replication errors.
            grafana_url: https://grafana.corp.mongodb.com/d/SQ_2MZhIz/atlas-search-data-plane1?orgId=20&viewPanel=74
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.6jboliidy9zm#heading=h.ostvf2yqljpr
        - alert: Mongot conf call behind (gov)
          expr: count(count by (groupId) (increase(mms_mongot_behind_count_total{namespace="{{ .Release.Namespace }}",searchDeploymentType="coupled"}[30m]) > 1)) > 0
          keep_firing_for: 30m
          labels:
            severity: warning
          annotations:
            summary: Mongots are not sending conf calls
            description: A project has coupled mongots not sending conf calls for more than 2 hours (as evaluated over last 30 mins).
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=11
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#heading=h.567354xuflti
        - alert: Search indexes stuck in DELETING (gov)
          expr: increase(index_statuses_stuck_total{namespace="{{ .Release.Namespace }}",stuckStatus="DELETE"}[30m]) > 0
          labels:
            severity: warning
          annotations:
            summary: At least one search index is stuck in DELETING state
            description: At least one search index is stuck in DELETING state for more than 2 hours (evaluated over last 30 mins)
            grafana_url: https://grafana.corp.mongodb.com/d/VSeht19Vz/atlas-search-conf-call?orgId=20&viewPanel=13
            runbook_url: https://docs.google.com/document/d/16-iwTwrT-a9e4yzT5GwSBqqsDflFwjGh41MrjcevhxA/edit?tab=t.vf0n00avk6c1#heading=h.4d1j87ry93zj
