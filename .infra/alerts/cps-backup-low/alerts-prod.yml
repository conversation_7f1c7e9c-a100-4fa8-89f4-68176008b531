---
# yamllint disable rule:quoted-strings
pagerdutyService:
  enabled: true
  escalationPolicyName: cps-backup
  annotations:
    pagerduty.10gen.cc/grafana-uid: vGepOCiNk

autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
  pagerdutyConfig:
    sendResolved: false

prometheusServiceLevel:
  service: cps
  slos:
    - name: cps-snapshot-success-rate
      objective: 99.5
      description: Snapshot Availability
      sli:
        events:
          errorQuery: |
            (sum(rate(mms_cps_snapshot_completed_total{state="failed",snapshot_type=~"ON_DEMAND|SCHEDULED",nvme="false",will_retry="false",provider!="none"}[{{ .window }}])) by (namespace, xgen_environment, provider))
          totalQuery: |
            (sum(rate(mms_cps_snapshot_completed_total{snapshot_type=~"ON_DEMAND|SCHEDULED",nvme="false",will_retry="false",provider!="none"}[{{ .window }}])) by (namespace, xgen_environment, provider))
      alerting:
        labels: {}
        pageAlert:
          disable: true
        ticketAlert:
          disable: true

vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: cps_alerts
      rules:
        - alert: (Prod) Too Many 'hasLastBatchArrived' failures
          expr: sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message="Attempts for 'hasLastBatchArrived' expired", log_class=~"(.+\\.)*Step", log_method="hasAttemptTimePassed", xgen_environment="prod"}) >= 5
          labels:
            severity: warning
          annotations:
            summary: 'Plan Execution failure uptick detected: increase in "Too Many ''hasLastBatchArrived'' failures" failures'
            description: 'Error log: "{{ $labels.log_message }}" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }}).'
            splunk: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27hasLastBatchArrived%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod) Too Many 'waitForRestoreServerReady' failures
          expr: sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message="Attempts for 'waitForRestoreServerReady' expired", log_class=~"(.+\\.)*Step", log_method="hasAttemptTimePassed", xgen_environment="prod"}) >= 5
          labels:
            severity: warning
          annotations:
            summary: 'Plan Execution failure uptick detected: increase in "Too Many ''waitForRestoreServerReady'' failures" failures'
            description: 'Error log: "{{ $labels.log_message }}" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }}).'
            splunk: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27waitForRestoreServerReady%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod) Too Many 'backupCursorExtended' failures
          expr: sum by (log_message, log_class, log_method, exception_type, exception_class, exception_method) (mms_nds::mms_nds_planner_plan_failures_total:increase15m{log_message="Attempts for 'backupCursorExtended' expired", log_class=~"(.+\\.)*Step", log_method="hasAttemptTimePassed", xgen_environment="prod"}) >= 5
          labels:
            severity: warning
          annotations:
            summary: 'Plan Execution failure uptick detected: increase in "Too Many ''backupCursorExtended'' failures" failures'
            description: 'Error log: "{{ $labels.log_message }}" (logged in class - {{ $labels.log_class }} and method - {{ $labels.log_method }}). Exception thrown: {{ $labels.exception_type }} (thrown in class - {{ $labels.exception_class }} and method - {{ $labels.exception_method }}).'
            splunk: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search+index%3Dmms+ERROR+planExecution+hasAttemptTimePassed+Attempts+for+%27backupCursorExtended%27+expired&display.page.search.mode=verbose&dispatch.sample_ratio=1&earliest=-24h%40h&latest=now
        - alert: (Prod) invalidOplogPurge cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="invalidOplogPurge",quantile="0.99"}[2h:]) > 8000
          labels:
            severity: warning
          annotations:
            summary: 'Invalid oplog purge takes too long to complete'
            description: 'The P99 execution time for invalidOplogPurge cron job is too high, it may cause oplog for deleted snapshot stacking up.'
            grafana_url: https://grafana.corp.mongodb.com/d/kxygEr0Sk/cron-jobs?orgId=20&from=now-7d&to=now&viewPanel=5
        - alert: (Prod) invalidOplogPurge cron job oplog purge rate dropping rapidly
          expr: avg_over_time(sum(increase(mms_cps_oplog_provider_purging_duration_seconds_bucket{xgen_environment="prod"}[1h]))[3h:]) * 2 < avg_over_time(sum(increase(mms_cps_oplog_provider_purging_duration_seconds_bucket{xgen_environment="prod"}[1h]))[12h:3h])
          labels:
            severity: warning
          annotations:
            summary: 'invalidOplogPurge cron job oplog purge rate dropping rapidly'
            description: 'The avg oplog purge rate per hour in the last 3 hours drops below 1/2 of the avg of the last 12 hours'
            grafana_url: https://grafana.corp.mongodb.com/d/kxygEr0Sk/cron?from=now-12h&to=now&orgId=20&viewPanel=37
        - alert: (Prod) ndsBackupGc cron job snapshot deletion rate dropping rapidly
          expr: avg_over_time(sum(increase(mms_nds_backup_snapshot_gcs_total{app="mms-cron-jobs",xgen_environment="prod"}[1h]))[2h:]) * 3 < avg_over_time(sum(increase(mms_nds_backup_snapshot_gcs_total{app="mms-cron-jobs",xgen_environment="prod"}[1h]))[12h:2h])
          labels:
            severity: warning
          annotations:
            summary: 'ndsBackupGc cron job snapshot deletion rate dropping rapidly'
            description: 'The avg snapshot deletion rate per hour in the last 2 hours drops below 1/3 of the avg of the last 12 hours'
            grafana_url: https://grafana.corp.mongodb.com/d/kxygEr0Sk/cron?from=now-12h&to=now&orgId=20&viewPanel=18
        - alert: (Prod) expiredOplogInvalidate cron job execution falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="expiredOplogInvalidate",quantile="0.99"}[1h:]) > 1998
          labels:
            severity: warning
          annotations:
            summary: 'expiredOplogInvalidate takes too long to complete'
            description: 'The P99 execution time for expiredOplogInvalidate cron job is too high.'
            grafana_url: https://grafana.corp.mongodb.com/d/kxygEr0Sk/cron-jobs?orgId=20&from=now-7d&to=now&viewPanel=6
        - alert: (Prod) removeOplogMetadata cron job execution falling too behind
          # This is an 8 hr threshold. At the time of writing, the cron job runs every 12h. So long as it completes in under 12h, we should be fine. The 8h
          # threshold gives us some breathing room.
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="removeOplogMetadata",quantile="0.99"}[1h:]) > 28800
          labels:
            severity: warning
          annotations:
            summary: 'removeOplogMetadata takes too long to complete'
            description: 'The P99 execution time for expiredOplogInvalidate cron job is too high.'
            grafana_url: https://grafana.corp.mongodb.com/d/kxygEr0Sk/cron-jobs?orgId=20&editPanel=9&from=now-7d&to=now
        - alert: (Prod) validateCpsOplogBehind cron job execution falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="validateCpsOplogBehind",quantile="0.99"}[1h:]) > 8200
          labels:
            severity: warning
          annotations:
            summary: 'validateCpsOplogBehind takes too long to complete'
            description: 'The P99 execution time for validateCpsOplogBehind cron job is too high.'
            grafana_url: https://grafana.corp.mongodb.com/d/kxygEr0Sk/cron-jobs?orgId=20&from=now-7d&to=now&viewPanel=7
        # There are fewer GCP/Azure copies, so any failures disproportionately affect the success rate.
        - alert: (Prod) Too many copy snapshots failed in production
          expr: sum(increase(mms_cps_snapshot_completed_total{xgen_environment="prod",snapshot_type="COPY",state="completed",provider!="AWS"}[1h])) by (provider) / sum(increase(mms_cps_snapshot_completed_total{xgen_environment="prod",snapshot_type="COPY",state=~"completed|failed",provider!="AWS"}[1h])) by (provider) < 0.97
          labels:
            severity: warning
          annotations:
            summary: Too many {{ $labels.provider }} copy snapshot failures in production.
            description: There are too many {{ $labels.provider }} copy snapshots failed in production environment, the snapshot success rate is less than 97.0%.
            grafana_url: https://grafana.corp.mongodb.com/d/vGepOCiNk/cx?orgId=20&from=now-24h&to=now-1m&viewPanel=63
        - alert: (Prod) Too many AWS copy snapshots failed in production
          expr: sum(increase(mms_cps_snapshot_completed_total{xgen_environment="prod",snapshot_type="COPY",state="completed",provider="AWS"}[1h])) / sum(increase(mms_cps_snapshot_completed_total{xgen_environment="prod",snapshot_type="COPY",state=~"completed|failed",provider="AWS"}[1h])) < 0.99
          labels:
            severity: warning
          annotations:
            summary: Too many AWS snapshot failures in production.
            description: There are too many AWS copy snapshots failed in production environment, the snapshot success rate is less than 99.0%.
            grafana_url: https://grafana.corp.mongodb.com/d/vGepOCiNk/cx?orgId=20&from=now-24h&to=now-1m&viewPanel=63
        - alert: (Prod) Too many resilient snapshots in production
          expr: sum(increase(mms_cps_snapshot_completed_total{xgen_environment="prod",state="completed",resilient="true"}[2h])) > 30
          labels:
            severity: warning
          annotations:
            summary: Too many resilient snapshots in production.
            description: There are too many resilient snapshot in production environment, there are more than 30 over 2 hours.
            grafana_url: https://grafana.corp.mongodb.com/d/n_mxFVbGz/snapshot?orgId=20&viewPanel=46
        - alert: (Prod) Too many failed resilient snapshots in production
          expr: sum(increase(mms_cps_snapshot_completed_total{xgen_environment="prod",state="failed",resilient="true"}[2h])) > 15
          labels:
            severity: warning
          annotations:
            summary: Too many resilient snapshots failures in production.
            description: There are too many resilient snapshot failed in production environment, there are more than 15 over 2 hours.
            grafana_url: https://grafana.corp.mongodb.com/d/n_mxFVbGz/snapshot?orgId=20&viewPanel=46
        - alert: (Prod) CPS Internal API Failures > 1%
          expr: (sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/nds/backup/[^serverless|^tenant].*",status="5xx"}[1h])) by (path, method) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/nds/backup/[^serverless|^tenant].*"}[1h])) by (path, method)) > 0.01
          labels:
            severity: warning
          annotations:
            summary: Too many 5xx responses returned by CPS Internal API endpoints in prod {{ $labels.method }} {{ $labels.path }}
            description: A large proportion of private HTTP requests (>1%) are returning in 5xx response status codes over last hour
            grafana_url: https://grafana.corp.mongodb.com/d/vGepOCiNk/cx?orgId=20&from=now-24h&to=now&viewPanel=72
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-PrivateAPI
        - alert: (Prod) CPS Public API Failures > 1%
          expr: (sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/api/atlas/.*clusters.*backup/[^tenant].*|/api/atlas/.*backupCompliancePolicy.*",status="5xx"}[1h])) by (path, method) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/api/atlas/.*clusters.*backup/[^tenant].*|/api/atlas/.*backupCompliancePolicy.*"}[1h])) by (path, method)) > 0.01
          labels:
            severity: warning
          annotations:
            summary: Too many 5xx responses returned by CPS Public API endpoints in prod {{ $labels.method }} {{ $labels.path }}
            description: A large proportion of public HTTP requests (>1%) are returning in 5xx response status codes over last hour
            grafana_url: https://grafana.corp.mongodb.com/d/vGepOCiNk/cx?orgId=20&from=now-24h&to=now&viewPanel=67
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-PublicAPI
            dashboardUId: vGepOCiNk
            panelId: "67"
        - alert: (Prod) Export Agent Job Failures
          expr: (sum(increase(mms_cps_export_executions_total{result="failure",xgen_environment="prod"}[1h])) by (xgen_environment)) > 2
          labels:
            severity: warning
          annotations:
            summary: Too many failed exports
            description: At least 3 exports failed over the last 1hr
            grafana_url: https://grafana.corp.mongodb.com/d/xtBd__iHk/export?orgId=20&viewPanel=4
        - alert: (Prod) Export Planner Move Failures
          expr: (sum(increase(mms_planner_move_overall_and_when_error_thrown_duration_seconds_count{xgen_environment="prod", status="FAILED", move_name=~"ProvisionSystemClusterMove|DoSnapshotExportFromSystemClusterMove|UploadSnapshotExportCompleteFileFromSystemClusterMove|DestroySystemClusterMove"}[1h]))) > 2
          labels:
            severity: warning
          annotations:
            summary: Too many Export Planner Moves failures. Maybe cluster provisioning is failing.
            description: At least 3 Export Planner move failures over the last 1hr
            grafana_url: https://grafana.corp.mongodb.com/d/Rx5jdI9Ik/exports?orgId=20&from=now-24h&to=now
        - alert: (Prod) System Project Known Unallowed Moves
          expr: (sum(increase(mms_cps_unallowed_system_export_moves_total{xgen_environment="prod", move=~"(com.xgen.svc.nds.planner.ProcessAutomationConfigPerGroupMove|com.xgen.svc.nds.planner.WaitForMachineHealthyMove|com.xgen.svc.nds.aws.planner.AWSOptionalRestartServerMove|com.xgen.svc.nds.planner.UpdateHostOSPolicyMove)"}[1h]))) > 1
          labels:
            severity: warning
          annotations:
            summary: System Project attempted to plan known unallowed moves
            description: At least 1 known unallowed move over the last 1hr
            grafana_url: https://grafana.corp.mongodb.com/d/xtBd__iHk/export?orgId=20&viewPanel=31
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-UnallowedMoves
        - alert: (Prod) System Project Unknown Unallowed Moves
          expr: (sum(increase(mms_cps_unallowed_system_export_moves_total{xgen_environment="prod", move!~"(com.xgen.svc.nds.planner.ProcessAutomationConfigPerGroupMove|com.xgen.svc.nds.planner.WaitForMachineHealthyMove|com.xgen.svc.nds.aws.planner.AWSOptionalRestartServerMove|com.xgen.svc.nds.planner.UpdateHostOSPolicyMove)"}[1h]))) > 1
          labels:
            severity: warning
          annotations:
            summary: System Project attempted to plan unknown unallowed moves
            description: At least 1 unknown unallowed move over the last 1hr
            grafana_url: https://grafana.corp.mongodb.com/d/xtBd__iHk/export?orgId=20&viewPanel=31
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-UnallowedMoves
        - alert: (Prod) Provision Restore Machine Move Failures
          expr: (sum(increase(mms_planner_move_overall_and_when_error_thrown_duration_seconds_count{xgen_environment="prod", status="FAILED", move_name=~".*ProvisionRestoreMachineMove"}[1h])) by (move_name)) > 9
          labels:
            severity: warning
          annotations:
            summary: Too many {{ $labels.move_name }} failures.
            description: At least 10 {{ $labels.move_name }} move failures over the last 1hr
            grafana_url: https://grafana.corp.mongodb.com/d/PrCWoZAGk/restore?orgId=20&viewPanel=26&from=now-24h&to=now
        - alert: (Prod) Direct Attach Move Failures
          expr: (sum(increase(mms_planner_move_overall_and_when_error_thrown_duration_seconds_count{xgen_environment="prod", status="FAILED", move_name=~".*DirectAttachRestoreMove"}[4h])) by (move_name)) > 19
          labels:
            severity: warning
          annotations:
            summary: Too many {{ $labels.move_name }} failures.
            description: At least 10 {{ $labels.move_name }} move failures over the last 4hrs
            grafana_url: https://grafana.corp.mongodb.com/d/PrCWoZAGk/restore?orgId=20&viewPanel=26&from=now-24h&to=now
        - alert: (Prod) AZURE Pv2 Disk API errors over threshold for SNAPSHOT_LIMIT_REACHED
          expr: sum(increase(mms_nds_azure_disk_api_error_total{xgen_environment="prod", disk_type="V2", error_code="SNAPSHOT_LIMIT_REACHED"}[5m])) by(error_code, request_name)  > 0
          labels:
            severity: warning
          annotations:
            summary: AZURE Pv2 API request {{ $labels.request_name }} failed with error SNAPSHOT_LIMIT_REACHED)
            grafana_url: 'https://grafana.corp.mongodb.com/d/02K-ceISk/azure-ssd-v2?orgId=20'
        - alert: (Prod) CPS Agent API Failures > 1%
          expr: (sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/agents/cps/.*|/conf/cps/.*",status="5xx"}[1h])) by (path, method) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/agents/cps/.*|/conf/cps/.*"}[1h])) by (path, method)) > 0.01
          labels:
            severity: warning
          annotations:
            summary: Too many 5xx responses returned by CPS Internal Agent API endpoints in prod {{ $labels.method }} {{ $labels.path }}
            description: A large proportion of agent HTTP requests (>1%) are returning in 5xx response status codes over last hour
            grafana_url: https://grafana.corp.mongodb.com/d/vGepOCiNk/cx?orgId=20&from=now-24h&to=now&viewPanel=72
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-AgentAPI
        - alert: (Prod) PUT Batch Agent API Latency > 0.3s
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{xgen_environment="prod", path="/agents/cps/checkpoints/batches/{groupId}/cluster/{clusterName}/{rsId}", method="PUT"}[1h])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="prod", path="/agents/cps/checkpoints/batches/{groupId}/cluster/{clusterName}/{rsId}", method="PUT"}[1h]))) > 0.3
          labels:
            severity: warning
          annotations:
            summary: Latency increase in prod for {{ $labels.method }} {{ $labels.path }}
            description: The average latency for the PUT Batch requests has exceeded 0.3 seconds in the last hour.
            grafana_url: https://grafana.corp.mongodb.com/d/Ij_lwcYIk/api?orgId=20&from=now-7d&to=now&viewPanel=39
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-AgentAPI
        - alert: (Prod) Cursor Extend Agent API Latency > 0.3s
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{xgen_environment="prod", path="/agents/cps/checkpoints/wtcExtendComplete/{groupId}/cluster/{clusterName}/{rsId}", method="POST"}[1h])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="prod", path="/agents/cps/checkpoints/wtcExtendComplete/{groupId}/cluster/{clusterName}/{rsId}", method="POST"}[1h]))) > 0.3
          labels:
            severity: warning
          annotations:
            summary: Latency increase in prod for {{ $labels.method }} {{ $labels.path }}
            description: The average latency for the POST cursor extend requests has exceeded 0.3 seconds in the last hour.
            grafana_url: https://grafana.corp.mongodb.com/d/Ij_lwcYIk/api?orgId=20&from=now-7d&to=now&viewPanel=40
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-AgentAPI
        - alert: (Prod) POST Oplog slices Agent API Latency > 2s
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{xgen_environment="prod", path="/agents/cps/oplog/slice/batch/v2/{groupId}/{clusterName}/{rsId}", method="POST"}[1h])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="prod", path="/agents/cps/oplog/slice/batch/v2/{groupId}/{clusterName}/{rsId}", method="POST"}[1h]))) > 2
          labels:
            severity: warning
          annotations:
            summary: Latency increase in prod for {{ $labels.method }} {{ $labels.path }}
            description: The average latency for the POST Oplog slices requests has exceeded 2 seconds in the last hour.
            grafana_url: https://grafana.corp.mongodb.com/d/Ij_lwcYIk/api?orgId=20&from=now-7d&to=now&viewPanel=26
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-AgentAPI
        - alert: (Prod) GET Conf Call Agent API Latency > 0.5s
          expr: (sum(rate(java_jersey_http_duration_seconds_sum{xgen_environment="prod", path="/conf/cps/{groupId}/cluster/{clusterName}", method="GET"}[1h])) / sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="prod", path="/conf/cps/{groupId}/cluster/{clusterName}", method="GET"}[1h]))) > 0.5
          labels:
            severity: warning
          annotations:
            summary: Latency increase in prod for {{ $labels.method }} {{ $labels.path }}
            description: The average latency for the GET Conf call requests has exceeded 0.5 seconds in the last hour.
            grafana_url: https://grafana.corp.mongodb.com/d/Ij_lwcYIk/api?orgId=20&from=now-7d&to=now&viewPanel=2
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-AgentAPI
        - alert: (Prod) Skipped PrivateLink Download URL Creation because of Listener Limit
          expr: sum(increase(mms_cps_download_private_links_skipped_on_limit_total{xgen_environment="prod",already_increased="false"}[1h])) by (group_id) > 0
          labels:
            severity: warning
          annotations:
            summary: We skipped creating a PrivateLink Download URL for a group {{ $labels.group_id }} because of the group's limit.
            description: We need a Lead+ to increase the limit for the group. Either "Max Nodes Per Region Per Project When AWS PrivateLink Enabled" if AWS or "Azure Private Link Max Nodes Per Private Link Region" if Azure. Limits are adjustable in the Admin UI via a search on Group ID.
            grafana_url: https://grafana.corp.mongodb.com/d/PrCWoZAGk/restore?orgId=20&viewPanel=61
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/314413102/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-SkippedPrivateLinkDownloadURLCreationbecauseofListenerLimit
        - alert: (Prod) Unhandled exceptions during snapshot plan executions
          expr: sum(increase(mms_nds_planner_plan_failures_total{xgen_environment="prod", exception_class=~".+AzureCreateManagedV2DataDiskSnapshotStep|.+AzureCreateManagedDataDiskSnapshotStep|.+GCPCreateSnapshotStep|.+AWSCreateEbsSnapshotStep|.+AWSCopySnapshotStep|.+AzureCopySnapshotStep|.+CopySnapshotMove|.+CpsReplSetSnapshotMove|.+CpsShardedSnapshotMove|.+CreateResilientSnapshotMove"}[1h])) by (exception_class) > 15
          labels:
            severity: warning
          annotations:
            summary: Unhandled exceptions found during snapshot plan executions in {{ $labels.exception_class }}
            description: The number of unhandled exceptions during snapshot plan executions is greater than than 15.
            grafana_url: https://grafana.corp.mongodb.com/d/n_mxFVbGz/snapshot?orgId=20&from=now-7d&to=now&viewPanel=49
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/314413102/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-UnhandledExceptionsInSnapshotPlans
        - alert: (Prod) Unhandled exceptions during restore plan executions
          expr: sum(increase(mms_nds_planner_plan_failures_total{xgen_environment="prod", exception_class=~".+AWSDirectAttachRestoreMove|.+AWSProvisionRestoreMachineMove|.+AzureProvisionRestoreMachineMove|.+AWSDirectAttachRestoreMove|.+AWSOptimizedDirectAttachRestoreMove|.+AzureDirectAttachRestoreMove|.+GCPDirectAttachRestoreMove|.+GCPOptimizedDirectAttachRestoreMove"}[1h])) by (exception_class) > 0
          labels:
            severity: warning
          annotations:
            summary: Unhandled exceptions found during restore plan executions in {{ $labels.exception_class }}
            description: The number of unhandled exceptions during restore plan executions is greater than than 0.
            grafana_url: https://grafana.corp.mongodb.com/d/PrCWoZAGk/restore?orgId=20&from=now-7d&to=now&viewPanel=63
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/314413102/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-UnhandledExceptionsInRestorePlans
        - alert: (Prod) validateOplogContiguity cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="validateOplogContiguity",quantile="0.99"}[1h:]) > 10800
          labels:
            severity: warning
          annotations:
            summary: 'validate oplog continuity takes too long to complete'
            description: 'The P99 execution time for validateOplogContiguity cron job is too high, it may cause alerting gaps for oplog contiguity'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-22
        - alert: (Prod) rotateOplogEncryptionKeysForPitEnabledGroups cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="rotateOplogEncryptionKeysForPitEnabledGroups",quantile="0.99"}[1h:]) > 300
          labels:
            severity: warning
          annotations:
            summary: 'rotate oplog encryption keys takes too long to complete'
            description: 'The P99 execution time for rotateOplogEncryptionKeysForPitEnabledGroups cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-58
        - alert: (Prod) sendRollingReplacementJobEmails cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="sendRollingReplacementJobEmails",quantile="0.99"}[1h:]) > 300
          labels:
            severity: warning
          annotations:
            summary: 'sending rolling replacement job emails takes too long to complete'
            description: 'The P99 execution time for sendRollingReplacementJobEmails cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-8
        - alert: (Prod) cpsMetadataPruning cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsMetadataPruning",quantile="0.99"}[1h:]) > 300
          labels:
            severity: warning
          annotations:
            summary: 'cps metadata pruning takes too long to complete'
            description: 'The P99 execution time for cpsMetadataPruning cron job is too high, it may cause old snapshots to not be pruned.'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-59
        - alert: (Prod) cpsAggregateReplicaSetBackupRestoreJobsForQuantileMetrics cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsAggregateReplicaSetBackupRestoreJobsForQuantileMetrics",quantile="0.99"}[1h:]) > 300
          labels:
            severity: warning
          annotations:
            summary: 'aggregating restore jobs for quantile metrics takes too long to complete'
            description: 'The P99 execution time for cpsAggregateReplicaSetBackupRestoreJobsForQuantileMetrics cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-60
        - alert: (Prod) submitBackupUsageToMeterService cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="submitBackupUsageToMeterService",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting backup usage to meter service takes too long to complete'
            description: 'The P99 execution time for submitBackupUsageToMeterService cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-40
        - alert: (Prod) cpsOplogUsageCollection cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsOplogUsageCollection",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'collecting oplog usage takes too long to complete'
            description: 'The P99 execution time for cpsOplogUsageCollection cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-54
        - alert: (Prod) cpsOplogUsageSubmission cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsOplogUsageSubmission",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting oplog usage takes too long to complete'
            description: 'The P99 execution time for cpsOplogUsageSubmission cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-53
        - alert: (Prod) cpsAwsExportUsageSubmission cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsAwsExportUsageSubmission",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting aws export usage takes too long to complete'
            description: 'The P99 execution time for cpsAwsExportUsageSubmission cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-56
        - alert: (Prod) cpsAzureExportUsageSubmission cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsAzureExportUsageSubmission",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting azure export usage takes too long to complete'
            description: 'The P99 execution time for cpsAzureExportUsageSubmission cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-56
        - alert: (Prod) cpsGcpExportUsageSubmission cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsGcpExportUsageSubmission",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting gcp export usage takes too long to complete'
            description: 'The P99 execution time for cpsGcpExportUsageSubmission cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-56
        - alert: (Prod) cpsExportUsageSubmission cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsExportUsageSubmission",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting export usage takes too long to complete'
            description: 'The P99 execution time for cpsExportUsageSubmission cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-51
        - alert: (Prod) cpsAwsDownloadUsageSubmission cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsAwsDownloadUsageSubmission",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting aws download usage takes too long to complete'
            description: 'The P99 execution time for cpsAwsDownloadUsageSubmission cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-57
        - alert: (Prod) cpsAzureDownloadUsageSubmission cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsAzureDownloadUsageSubmission",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting aws download usage takes too long to complete'
            description: 'The P99 execution time for cpsAzureDownloadUsageSubmission cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-57
        - alert: (Prod) cpsGcpDownloadUsageSubmission cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsGcpDownloadUsageSubmission",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting gcp download usage takes too long to complete'
            description: 'The P99 execution time for cpsGcpDownloadUsageSubmission cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-57
        - alert: (Prod) cpsAwsSnapshotUsageSubmission cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsAwsSnapshotUsageSubmission",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting aws snapshot usage takes too long to complete'
            description: 'The P99 execution time for cpsAwsSnapshotUsageSubmission cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-55
        - alert: (Prod) cpsAzureSnapshotUsageSubmission cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsAzureSnapshotUsageSubmission",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting azure snapshot usage takes too long to complete'
            description: 'The P99 execution time for cpsAzureSnapshotUsageSubmission cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-55
        - alert: (Prod) cpsGCPSnapshotUsageSubmission cron job falling too behind
          expr: max_over_time(mms_cron_job_duration_seconds{app="mms-cron-jobs",xgen_environment="prod",name="cpsGCPSnapshotUsageSubmission",quantile="0.99"}[1h:]) > 3600
          labels:
            severity: warning
          annotations:
            summary: 'submitting gcp snapshot usage takes too long to complete'
            description: 'The P99 execution time for cpsGCPSnapshotUsageSubmission cron job is too high'
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-55
        - alert: (PROD) Error expiredOplogInvalidate cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="expiredOplogInvalidate",xgen_environment="prod"}))
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: Error expiredOplogInvalidate cron job not run
            grafana_url: https://grafana.corp.mongodb.com/d/kxygEr0Sk/cron-jobs?orgId=20&from=now-7d&to=now&viewPanel=6
        - alert: (PROD) Error invalidOplogPurge cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="invalidOplogPurge",xgen_environment="prod"}))
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: Error invalidOplogPurge cron job not run
            grafana_url: https://grafana.corp.mongodb.com/d/kxygEr0Sk/cron-jobs?orgId=20&from=now-7d&to=now&viewPanel=5
        - alert: (PROD) Error removeOplogMetadata cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="removeOplogMetadata",xgen_environment="prod"}))
          # 12 hours and 20 minutes
          for: 740m
          labels:
            severity: warning
          annotations:
            summary: Error removeOplogMetadata cron job not run
            grafana_url: https://grafana.corp.mongodb.com/d/kxygEr0Sk/cron-jobs?orgId=20&editPanel=9&from=now-7d&to=now
        - alert: (PROD) Error validateOplogContiguity cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="validateOplogContiguity",xgen_environment="prod"}))
          # 2 hours and 20 minutes
          for: 140m
          labels:
            severity: warning
          annotations:
            summary: Error validateOplogContiguity cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-22
        - alert: (PROD) Error validateCpsOplogBehind cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="validateCpsOplogBehind",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error validateCpsOplogBehind cron job not run
            grafana_url: https://grafana.corp.mongodb.com/d/kxygEr0Sk/cron-jobs?orgId=20&from=now-7d&to=now&viewPanel=7
        - alert: (PROD) Error rotateOplogEncryptionKeysForPitEnabledGroups cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="rotateOplogEncryptionKeysForPitEnabledGroups",xgen_environment="prod"}))
          # 1 hour 20 minutes
          for: 80m
          labels:
            severity: warning
          annotations:
            summary: Error rotateOplogEncryptionKeysForPitEnabledGroups cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-58
        - alert: (PROD) Error sendRollingReplacementJobEmails cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="sendRollingReplacementJobEmails",xgen_environment="prod"}))
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: Error sendRollingReplacementJobEmails cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-8
        - alert: (PROD) Error cpsMetadataPruning cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsMetadataPruning",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsMetadataPruning cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-59
        - alert: (PROD) Error ndsBackupGc cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="ndsBackupGc",xgen_environment="prod"}))
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: Error ndsBackupGc cron job not run
            grafana_url: https://grafana.corp.mongodb.com/d/kxygEr0Sk/cron?from=now-12h&to=now&orgId=20&viewPanel=18
        - alert: (PROD) Error cpsAggregateReplicaSetBackupRestoreJobsForQuantileMetrics cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsAggregateReplicaSetBackupRestoreJobsForQuantileMetrics",xgen_environment="prod"}))
          # 24 hours and 20 minutes
          for: 1460m
          labels:
            severity: warning
          annotations:
            summary: Error cpsAggregateReplicaSetBackupRestoreJobsForQuantileMetrics cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-60
        - alert: (PROD) Error submitBackupUsageToMeterService cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="submitBackupUsageToMeterService",xgen_environment="prod"}))
          # 24 hours and 20 minutes
          for: 1460m
          labels:
            severity: warning
          annotations:
            summary: Error submitBackupUsageToMeterService cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-40
        - alert: (PROD) Error cpsOplogUsageCollection cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsOplogUsageCollection",xgen_environment="prod"}))
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: Error cpsOplogUsageCollection cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-54
        - alert: (PROD) Error cpsOplogUsageSubmission cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsOplogUsageSubmission",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsOplogUsageSubmission cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-53
        - alert: (PROD) Error cpsExportUsageSubmission cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsExportUsageSubmission",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsExportUsageSubmission cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-51
        - alert: (PROD) Error cpsAwsExportUsageSubmission cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsAwsExportUsageSubmission",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsAwsExportUsageSubmission cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-56
        - alert: (PROD) Error cpsAzureExportUsageSubmission cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsAzureExportUsageSubmission",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsAzureExportUsageSubmission cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-56
        - alert: (PROD) Error cpsGcpExportUsageSubmission cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsGcpExportUsageSubmission",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsGcpExportUsageSubmission cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-56
        - alert: (PROD) Error cpsAwsDownloadUsageSubmission cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsAwsDownloadUsageSubmission",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsAwsDownloadUsageSubmission cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-57
        - alert: (PROD) Error cpsAzureDownloadUsageSubmission cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsAzureDownloadUsageSubmission",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsAzureDownloadUsageSubmission cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-57
        - alert: (PROD) Error cpsGcpDownloadUsageSubmission cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsGcpDownloadUsageSubmission",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsGcpDownloadUsageSubmission cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-30d&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-57
        - alert: (PROD) Error cpsAwsSnapshotUsageSubmission cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsAwsSnapshotUsageSubmission",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsAwsSnapshotUsageSubmission cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-55
        - alert: (PROD) Error cpsAzureSnapshotUsageSubmission cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsAzureSnapshotUsageSubmission",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsAzureSnapshotUsageSubmission cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-55
        - alert: (PROD) Error cpsGCPSnapshotUsageSubmission cron job not run
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and max(absent(mms_core_batch_job_duration_seconds_count{job_name="cpsGCPSnapshotUsageSubmission",xgen_environment="prod"}))
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: Error cpsGCPSnapshotUsageSubmission cron job not run
            grafana_url: https://grafana.helix.corp.mongodb.com/d/kxygEr0Sk/cron?orgId=20&from=now-24h&to=now&timezone=utc&var-env=prod&var-datasource=BDDlLZdVz&viewPanel=panel-55
        - alert: (Prod) Missing indexes in oplog metadata collections
          expr: (sum(increase(mms_nds_cps_oplog_abnormalities_check_collections_with_missing_indexes_total{xgen_environment="prod"}[1h]))) > 1
          labels:
            severity: warning
          annotations:
            summary: Missing indexes in oplog metadata collections
            description: At least 1 cps oplog metadata collection is missing indexes
            grafana_url: https://grafana.helix.corp.mongodb.com/d/SytVAEkNk/oplog?orgId=20&from=now-24h&to=now&timezone=utc&var-datasource=BDDlLZdVz&var-env=prod&var-region=$__all&viewPanel=panel-18
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/314413102/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-MissingIndexes
        - alert: (Prod) Unexpected indexes in oplog metadata collections
          expr: (sum(increase(mms_nds_cps_oplog_abnormalities_check_collections_with_unexpected_indexes_total{xgen_environment="prod"}[1h]))) > 1
          labels:
            severity: warning
          annotations:
            summary: Unexpected indexes in oplog metadata collections
            description: At least 1 cps oplog metadata collection has an unexpected index
            grafana_url: https://grafana.helix.corp.mongodb.com/d/SytVAEkNk/oplog?orgId=20&from=now-24h&to=now&timezone=utc&var-datasource=BDDlLZdVz&var-env=prod&var-region=$__all&viewPanel=panel-19
            runbook_url: https://wiki.corp.mongodb.com/spaces/MMS/pages/314413102/CPS+Engineering+HELP+Runbook#CPSEngineeringHELPRunbook-UnexpectedIndexes
