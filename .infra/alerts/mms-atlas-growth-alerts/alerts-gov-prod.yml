---
# yamllint disable rule:quoted-strings rule:brackets rule:empty-lines
pagerdutyService:
  enabled: true
  fullnameOverride: mms-atlas-growth
  escalationPolicyName: "Atlas Growth On-Call"
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: SmartLinkSvc alerts
      rules:
        - alert: "GOV PROD - SmartLinkSvc URL validation error"
          expr: sum(increase(atlasgrowth_smartLinkSvc_url_validation_error_count_total{xgen_environment="prod-gov"}[2h])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of SmartLinkSvc URL validation errors. (Redirects users to default URL.)
            description: "Grafana: http://go/grafana-growth-smartlink-validation-errors-gov"
            runbook_url: http://go/growth-on-call-playbook
        - alert: "GOV PROD - SmartLinkSvc URL parsing error"
          expr: sum(increase(atlasgrowth_smartLinkSvc_url_parsing_error_count_total{xgen_environment="prod-gov"}[2h])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of SmartLinkSvc URL parsing errors. (Redirects users to root URL.)
            description: "Grafana: http://go/grafana-growth-smartlink-parsing-errors-gov | Splunk /go URL top-down chart: http://go/splunk-smart-link-url-top-down-chart-gov"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-url-parsing-errors-gov
        - alert: "GOV PROD - SmartLinkSvc URL resolving error"
          expr: sum(increase(atlasgrowth_smartLinkSvc_url_resolving_error_count_total{xgen_environment="prod-gov"}[2h])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of SmartLinkSvc URL resolving errors. (Redirects users to root URL.)
            description: "Grafana: http://go/grafana-growth-smartlink-resolving-errors-gov"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-url-resolving-errors-gov
