---
# yamllint disable rule:quoted-strings rule:brackets rule:empty-lines
pagerdutyService:
  enabled: true
  fullnameOverride: mms-atlas-growth
  escalationPolicyName: "Atlas Growth On-Call"
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: Pholiota alerts
      rules:
        - alert: getExperiments errors
          expr: sum_over_time(pholiota_function_getExperimentsV1_total{status="failure",xgen_environment="prod"}[5m]) > 2
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number (>2 in last 5 min) of getExperiments function call failures in Pholiota.
            description: "Grafana: https://bit.ly/3gT3T8f | Realm Errors: https://bit.ly/35E7wwn"
            runbook_url: http://go/growth-on-call-playbook
        - alert: getExperimentResults errors
          expr: sum_over_time(pholiota_function_getExperimentResultsV2_total{status="failure",xgen_environment="prod"}[60m]) > 5
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number (>5 in last 60 min) of getExperimentResults function call failures in Pholiota.
            description: "Grafana: https://bit.ly/2WN4Ojy | Realm Errors: https://bit.ly/35E7wwn"
            runbook_url: http://go/growth-on-call-playbook
        - alert: createExperiment errors over prolonged period
          expr: sum_over_time(pholiota_function_createExperimentV1_total{status="failure",xgen_environment="prod"}[5m]) > 1
          for: 10m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number (>1 in last 5 min) of createExperiment function call failures in Pholiota over prolonged period.
            description: "Grafana: https://bit.ly/3gO4B6L | Realm Errors: https://bit.ly/35E7wwn"
            runbook_url: http://go/growth-on-call-playbook-create-experiment-errors
        - alert: createExperiment errors
          expr: sum_over_time(pholiota_function_createExperimentV1_total{status="failure",xgen_environment="prod"}[5m]) > 1
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number (>1 in last 5 min) of createExperiment function call failures in Pholiota.
            description: "Grafana: https://bit.ly/3gO4B6L | Realm Errors: https://bit.ly/35E7wwn"
            runbook_url: http://go/growth-on-call-playbook
        - alert: createExperimentResults errors
          expr: sum_over_time(pholiota_function_createExperimentResultsV1_total{status="failure",xgen_environment="prod"}[60m]) > 1
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number (>1 in last 60 min) of createExperimentResults function call failures in Pholiota.
            description: "Grafana: https://bit.ly/38zmVMd | Realm Errors: https://bit.ly/35E7wwn | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: createExperimentResults MAB variant sum errors
          expr: sum_over_time(pholiota_function_createExperimentResultsV1_mab_variant_sum_total{status="failure",xgen_environment="prod"}[60m]) > 0
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number (>0 in last 60 min) of createExperimentResults MAB variant sum failures in Pholiota.
            description: "New MAB allocations not adding up to 100%. Please check Realm logs. | Realm Errors: https://bit.ly/35E7wwn | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: Pholiota createIfrExperimentResults errors
          expr: sum(increase(pholiota_function_createIfrExperimentResultsV1_ifrExperimentsResults_upsert_total{status="failure",xgen_environment="prod"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: createIfrExperimentResults endpoint failed to post IFR results in Pholiota OL.
            description: "IFR metrics from Pholiota Backend failed to be ingested into Pholiota OL. Please check App Services logs | App Services Errors: http://go/pholiota-app-services-error-logs | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
        - alert: Pholiota postWaveEngineUpdates errors
          expr: sum(increase(pholiota_function_postWaveEngineUpdatesV1_total{status="failure",xgen_environment="prod"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: postWaveEngineUpdates endpoint failed to post Wave Engine updates to Pholiota OL.
            description: "Updates from the Wave Engine (ie: wave allocations status) failed to post to Pholiota OL. Please check App Services logs | App Services Errors: http://go/pholiota-app-services-error-logs"
            runbook_url: http://go/growth-on-call-playbook
    - name: Segment event job submission (cron job) alerts
      rules:
        - alert: SegmentEventSvc job submission errors
          expr: sum(increase(atlasgrowth_segmentEventSvc_job_submissions_failure_total{xgen_environment="prod"}[5m])) > 10
          for: 2m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of SegmentEventSvc job submission failures
            description: "Grafana: bit.ly/3XiG8u7"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://bit.ly/42PWEDi
        - alert: SegmentEventJobSubmissionSvcImpl job submission errors
          expr: sum(increase(atlasgrowth_segmentEventJobSubmissionSvcImpl_job_submissions_failure_total{xgen_environment="prod"}[5m])) > 10
          for: 2m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of SegmentEventJobSubmissionSvcImpl job submission failures
            description: "Grafana: bit.ly/3Po6OYK"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://bit.ly/42PWEDi
    - name: SegmentEventJobHandler (cron job) alerts
      rules:
        - alert: SegmentEventJobHandler AUID errors
          expr: sum(increase(atlasgrowth_segmentEventJobHandler_auid_error_total{xgen_environment="prod"}[5m])) > 100
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of AUID mapping errors across all events in SegmentEventJobHandler (new Segment Event service).
            description: "Grafana: https://go/segment-event-job-handler-auid-errors-dash"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://go/segment-event-job-handler-auid-errors-splunk
        - alert: SegmentEventJobHandler Create Identify errors
          expr: sum(increase(atlasgrowth_segmentEventJobHandler_identify_error_total{xgen_environment="prod"}[5m])) > 2
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of Identify object creation errors across all events in SegmentEventJobHandler (new Segment Event service).
            description: "Grafana: https://go/segment-event-job-handler-identify-errors-dash"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://go/segment-event-job-handler-identify-errors-splunk
        - alert: SegmentEventJobHandler Create Track errors
          expr: sum(increase(atlasgrowth_segmentEventJobHandler_track_error_total{xgen_environment="prod"}[5m])) > 2
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of Track object creation errors across all events in SegmentEventJobHandler (new Segment Event service).
            description: "Grafana: https://go/segment-event-job-handler-track-errors-dash"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://go/segment-event-job-handler-track-errors-splunk
        - alert: SegmentEventJobHandler general errors
          expr: sum(increase(atlasgrowth_segmentEventJobHandler_general_error_total{xgen_environment="prod"}[20m])) > 25
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of general errors across all events in SegmentEventJobHandler (new Segment Event service).
            description: "Grafana: https://go/segment-event-job-handler-general-errors-dash"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/segment-event-job-handler-general-errors-splunk
        - alert: SegmentEventJobHandler general errors - CRITICAL
          expr: sum(increase(atlasgrowth_segmentEventJobHandler_general_error_total{xgen_environment="prod"}[20m])) > 150
          for: 2m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Very high number of general errors across all events in SegmentEventJobHandler (new Segment Event service).
            description: "Grafana: https://go/segment-event-job-handler-general-errors-dash"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://go/segment-event-job-handler-general-errors-splunk
    - name: DeprecatedSegmentEventJobHandler (cron job) alerts
      rules:
        - alert: DeprecatedSegmentEventJobHandler SvcException errors
          expr: sum(increase(atlasgrowth_deprecatedSegmentEventJobHandler_svcException_error_total{xgen_environment="prod"}[5m])) > 10
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of SvcException errors across all events in DeprecatedSegmentEventJobHandler
            description: "Grafana: https://bit.ly/3njZOfY"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3t57JA1
        - alert: DeprecatedSegmentEventJobHandler general errors
          expr: sum(increase(atlasgrowth_deprecatedSegmentEventJobHandler_general_error_total{xgen_environment="prod"}[1m])) > 10
          for: 3m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of general errors across all events in DeprecatedSegmentEventJobHandler
            description: "Grafana: https://bit.ly/3njZOfY"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3gRdg8m
        - alert: DeprecatedSegmentEventJobHandler tracking user errors
          expr: sum(increase(atlasgrowth_deprecatedSegmentEventJobHandler_tracking_user_error_total{xgen_environment="prod"}[10m])) > 20
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of failed tracking user lookups in DeprecatedSegmentEventJobHandler
            description: "Grafana: https://bit.ly/3Bt9RaW"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3cRbq8x
    - name: ABTestSvc error alerts
      rules:
        - alert: ABTestSvc allocation error
          expr: sum(increase(atlasgrowth_abTestSvc_test_allocation_error_total{xgen_environment="prod"}[60m])) > 5
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of test allocation failures
            description: "Grafana: https://bit.ly/3Bzbh0k"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3yD9MfE
        - alert: ABTestSvc duplicate allocation error
          expr: sum(increase(atlasgrowth_abTestSvc_duplicate_assignment_key_exception_total{xgen_environment="prod"}[5m])) > 5
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of test duplicate allocation failures
            description: "Grafana: https://bit.ly/3BM8PE7"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-duplicate-allocation-errors
        - alert: ABTestSvc get assignments error
          expr: sum(increase(atlasgrowth_abTestSvc_get_assignments_error_total{xgen_environment="prod"}[1m])) > 5
          for: 3m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of failures when retrieving test assignment data
            description: "Grafana: https://bit.ly/3jBOURQ"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3zDXkxA
        - alert: ABTestSvc get tests errors
          expr: sum(increase(atlasgrowth_abTestSvc_get_tests_error_total{xgen_environment="prod"}[1m])) > 5
          for: 3m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of failures when retrieving test data
            description: "Grafana: https://bit.ly/3DC495k"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3DxfJyF
        - alert: ABTestSvc delete tests errors
          expr: sum(increase(atlasgrowth_abTestSvc_delete_test_error_total{xgen_environment="prod"}[5m])) > 5
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of failures when deleting test data
            description: "Grafana: https://bit.ly/3jxnX1M"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/2WDErgg
        - alert: ABTestSvc upsert tests errors
          expr: sum(increase(atlasgrowth_abTestSvc_upsert_test_error_total{xgen_environment="prod"}[5m])) > 5
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of failures when upserting test data
            description: "Grafana: https://bit.ly/3DFk7M7"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3jCJs1g
        - alert: ABTestSvc choose new assignment errors
          expr: sum(increase(atlasgrowth_abTestSvc_choose_new_assignment_error_total{xgen_environment="prod"}[5m])) > 5
          for: 1m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of failures when choosing new assignment for users
            description: "Grafana: https://bit.ly/3Bzbh0k"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3ypdtqQ
        - alert: ABTestSvc socket exceptions
          expr: sum(increase(atlasgrowth_abTestSvc_socket_exception_total{xgen_environment="prod"}[1m])) > 50
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of socket exceptions, often associated with deploys or SRE maintenance
            description: "Grafana: https://grafana.corp.mongodb.com/d/XOjtOcM7k/experiment-allocations?orgId=20&viewPanel=15"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3D%22mms%22%20ABTestSvc%20%22com.mongodb.MongoSocketOpenException%22&display.page.search.mode=smart&dispatch.sample_ratio=1&earliest=-7d%40h&latest=now&sid=1648148472.848679
        - alert: ABTestSvc get linked holdout test for entity errors
          expr: sum(increase(atlasgrowth_abTestSvc_get_linked_holdout_test_for_entity_error_total{xgen_environment="prod"}[5m])) > 5
          for: 1m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of failures when getting the linked holdout test for an entity
            description: "Grafana: https://bit.ly/45TzP44"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/46cGcjt
        - alert: ABTestSvc get current and launched assignments error
          expr: sum(increase(atlasgrowth_abTestSvc_get_current_and_launched_assignments_error_total{xgen_environment="prod"}[1m])) > 15
          for: 5m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of failures when retrieving current and launched assignments
            description: "Grafana: https://bit.ly/3MZ3wYW"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/45tx4p6
        - alert: ABTestSvc get ghost assignments error
          expr: sum(increase(atlasgrowth_abTestSvc_get_ghost_assignments_error_total{xgen_environment="prod"}[5m])) > 5
          for: 1m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of failures when retrieving ghost assignments
            description: "Grafana: https://bit.ly/3MZ3wYW"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3que0rU
    - name: ABTestAssignmentResource /v2 endpoint alerts
      rules:
        - alert: "ABTestAssignmentResource 5xx errors GET /v2/assignment(s)"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/v2/assignment[s]*",method="GET",xgen_app="mms-ui",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High number of 5xx errors for the /v2/assignment(s) endpoint"
            description: |
              High number of 5xx errors for the /v2/assignment(s) endpoint
              For more details:
              - Grafana: http://go/grafana-growth-assignment-v2-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: "ABTestAssignmentResource High Latency GET /v2/assignment(s)"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path=~"/v2/assignment[s]*",xgen_app="mms-ui",method="GET"}[5m])))) > 800
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High Latency detected at p99 for the /v2/assignment(s) endpoint"
            description: |
              Latency at the 99th percentile for the /v2/assignment(s) endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-assignment-v2-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: "ABTestAssignmentResource Low Success Ratio GET /v2/assignment(s)"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/v2/assignment[s]*",method="GET",xgen_app="mms-ui",http_status_code=~"(2..|3..|404)"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/v2/assignment[s]*",xgen_app="mms-ui",method="GET"}[5m]))) < 0.99
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "Low success ratio detected for /v2/assignment(s) endpoint"
            description: |
              The success ratio for the /v2/assignByPoint endpoint has dropped
              For more details:
              - Grafana: http://go/grafana-growth-assignment-v2-dash
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-growth-assignment-v2-low-success-ratio
        - alert: ABTestAssignmentResource 5xx errors POST /v2/assign
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v2/assign",xgen_app="mms-ui",method="POST",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 5xx errors for the /v2/assign endpoint
            description: |
              High number of 5xx errors for the v2/assign endpoint
              For more details:
              - Grafana: http://go/grafana-growth-assign-v2-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: ABTestAssignmentResource High Latency POST /v2/assign
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path="/v2/assign",xgen_app="mms-ui",method="POST"}[5m])))) > 800
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High Latency detected at p99 for the /v2/assign endpoint
            description: |
              Latency at the 99th percentile for the assign endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-assign-latency
            runbook_url: http://go/growth-on-call-playbook
        - alert: ABTestAssignmentResource Low Success Ratio POST /v2/assign
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v2/assign",xgen_app="mms-ui",method="POST",http_status_code=~"(2..|3..|404)"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v2/assign",xgen_app="mms-ui",method="POST"}[5m]))) < 0.99
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Low success ratio detected for /v2/assign endpoint
            description: |
              The success ratio for the /v2/assign endpoint has dropped
              For more details:
              - Grafana: http://go/grafana-growth-low-success-ratio
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-growth-assignment-v2-low-success-ratio
        - alert: ABTestAssignmentResource 5xx errors POST /v2/assignByPoint
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v2/assignByPoint",xgen_app="mms-ui",method="POST",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 5xx errors for the /v2/assignByPoint endpoint
            description: |
              High number of 5xx errors for the v2/assignByPoint endpoint
              For more details:
              - Grafana: http://go/grafana-growth-assignbypoint-v2-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: ABTestAssignmentResource High Latency POST /v2/assignByPoint
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path="/v2/assignByPoint",xgen_app="mms-ui",method="POST"}[5m])))) > 800
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High Latency detected at p99 for the /v2/assignByPoint endpoint"
            description: |
              Latency at the 99th percentile for the /v2/assignByPoint endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-assignbypoint-latency
            runbook_url: http://go/growth-on-call-playbook
        - alert: ABTestAssignmentResource Low Success Ratio POST /v2/assignByPoint
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v2/assignByPoint",xgen_app="mms-ui",method="POST",http_status_code=~"(2..|3..|404)"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v2/assignByPoint",xgen_app="mms-ui",method="POST"}[5m]))) < 0.99
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "Low success ratio detected for /v2/assignByPoint endpoint"
            description: |
              The success ratio for the /v2/assignByPoint endpoint has dropped
              For more details:
              - Grafana: http://go/grafana-growth-low-success-ratio
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-growth-assignment-v2-low-success-ratio
    - name: ABTestAssignmentResource /v3 endpoint alerts
      rules:
        - alert: "ABTestAssignmentResource 5xx errors GET /v3/assignment/{testName}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v3/assignment/{testName}",xgen_app="mms-ui",method="GET",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High number of 5xx errors for the /v3/assignment/{testName} endpoint"
            description: |
              High number of 5xx errors for the /v3/assignment/{testName} endpoint
              For more details:
              - Grafana: http://go/grafana-growth-assignment-v3-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: "ABTestAssignmentResource High Latency GET /v3/assignment/{testName}"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path="/v3/assignment/{testName}",xgen_app="mms-ui",method="GET"}[5m])))) > 800
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High Latency detected at p99 for the /v3/assignment/{testName} endpoint"
            description: |
              Latency at the 99th percentile for the /v3/assignment/{testName} endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-assignment-v3-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: "ABTestAssignmentResource Low Success Ratio GET /v3/assignment/{testName}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v3/assignment/{testName}",xgen_app="mms-ui",method="GET",http_status_code=~"(2..|3..|404)"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v3/assignment/{testName}",xgen_app="mms-ui",method="GET"}[5m]))) < 0.99
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "Low success ratio detected for /v3/assignment/{testName} endpoint"
            description: |
              The success ratio for the /v3/assignment/{testName} endpoint has dropped
              For more details:
              - Grafana: http://go/grafana-growth-assignment-v3-dash
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-growth-assignment-v3-low-success-ratio
        - alert: ABTestAssignmentResource 5xx errors POST /v3/assignment
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v3/assignment",xgen_app="mms-ui",method="POST",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 5xx errors for the /v3/assignment endpoint
            description: |
              High number of 5xx errors for the /v3/assignment endpoint
              For more details:
              - Grafana: http://go/grafana-growth-assign-v3-dash
            runbook_url: http://go/growth-on-call-playbook-assign-v3
        - alert: ABTestAssignmentResource High Latency POST /v3/assignment
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path="/v3/assignment",xgen_app="mms-ui",method="POST"}[5m])))) > 800
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High Latency detected at p99 for the /v3/assignment endpoint
            description: |
              Latency at the 99th percentile for the /v3/assignment endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-assign-v3-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: ABTestAssignmentResource Low Success Ratio POST /v3/assignment
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v3/assignment",xgen_app="mms-ui",method="POST",http_status_code=~"(2..|3..|404)"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v3/assignment",xgen_app="mms-ui",method="POST"}[5m]))) < 0.99
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Low success ratio detected for /v3/assignment endpoint
            description: |
              The success ratio for the /v3/assignment endpoint has dropped
              For more details:
              - Grafana: http://go/grafana-growth-assign-v3-dash
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-growth-assignment-v3-low-success-ratio
        - alert: ABTestAssignmentResource 5xx errors POST /v3/assignmentsByAllocationPoints
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v3/assignmentsByAllocationPoints",xgen_app="mms-ui",method="POST",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 5xx errors for the /v3/assignmentsByAllocationPoints endpoint
            description: |
              High number of 5xx errors for the /v3/assignmentsByAllocationPoints endpoint
              For more details:
              - Grafana: http://go/grafana-growth-assignbypoint-v3-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: ABTestAssignmentResource High Latency POST /v3/assignmentsByAllocationPoints
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path="/v3/assignmentsByAllocationPoints",xgen_app="mms-ui",method="POST"}[5m])))) > 800
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High Latency detected at p99 for the /v3/assignmentsByAllocationPoints endpoint
            description: |
              Latency at the 99th percentile for the /v3/assignmentsByAllocationPoints endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-assignbypoint-v3-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: ABTestAssignmentResource Low Success Ratio POST /v3/assignmentsByAllocationPoints
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v3/assignmentsByAllocationPoints",xgen_app="mms-ui",method="POST",http_status_code=~"(2..|3..|404)"}[5m])) / sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/v3/assignmentsByAllocationPoints",xgen_app="mms-ui",method="POST"}[5m]))) < 0.99
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Low success ratio detected for /v3/assignmentsByAllocationPoints endpoint
            description: |
              The success ratio for the /v3/assignmentsByAllocationPoints endpoint has dropped
              For more details:
              - Grafana: http://go/grafana-growth-assignbypoint-v3-dash
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-growth-assignment-v3-low-success-ratio
    - name: CMABSvc alerts
      rules:
        - alert: CMABSvc get features error
          expr: sum(increase(atlasgrowth_CMABSvc_getFeatures_failure_total{xgen_environment="prod"}[60m])) > 5
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of get feature failures
            description: "Grafana: https://bit.ly/3CJYLxX"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3ELivSr
        - alert: CMABSvc get allocation error
          expr: sum(increase(atlasgrowth_CMABSvc_getCmabAllocation_failure_total{xgen_environment="prod"}[60m])) > 20
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of allocation failures against Okta or DSplatform model endpoint
            description: "Grafana: https://bit.ly/3CJYLxX"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3EsYhN2
        - alert: CMABSvc response time high
          expr: histogram_quantile(0.80, sum(rate(atlasgrowth_cmab_allocation_request_seconds_bucket{xgen_environment="prod"}[2h])) by (le, cmab_requests)) > 0.5
          for: 2h
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: More than 20% CMAB allocation requests operation have latency more than 500ms in 2h window
            description: "Grafana: https://bit.ly/3CJYLxX"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3Xm5uXJ
        - alert: CMAB model not deployed
          expr: sum(increase(atlasgrowth_CMABSvc_model_endpoint_not_deployed_total{xgen_environment="prod"}[1m])) > 10
          for: 70m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Getting CMAB model not deployed warnings for over an hour. We expect these to go away within the hour because the cron job which deploys models runs once an hour.
            description: "Grafana: https://bit.ly/3CJYLxX"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/46y1S8W
    - name: Cluster Active (cron job) alerts
      rules:
        - alert: Cluster Active cron job failure
          expr: sum(increase(mms_nds_atlasgrowth_cluster_active_failure_cron_svc_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Cluster Active cron job failed
            description: "Grafana: https://go/cluster-active-cron-job-error-dash"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/cluster-active-cron-job-error-splunk
        - alert: Cluster Active group process errors
          expr: sum(increase(mms_nds_atlasgrowth_cluster_active_batch_job_handler_failure_run_check_for_group_total{xgen_environment="prod"}[1h])) > 15
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of Cluster Active group processing failures
            description: "Grafana: https://go/cluster-active-group-process-failure-dash"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/cluster-active-group-process-failure-splunk
        - alert: Cluster Active event broadcast errors
          expr: sum(increase(mms_nds_atlasgrowth_cluster_active_batch_job_handler_failure_broadcast_total{xgen_environment="prod"}[10m])) > 15
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of Cluster Active Segment event broadcast errors
            description: "Grafana: https://go/cluster-active-broadcast-failures-dash"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/cluster-active-broadcast-failures-splunk
        - alert: Cluster Active update group sample times errors
          expr: sum(increase(mms_nds_atlasgrowth_cluster_active_batch_job_handler_failure_update_group_sample_times_total{xgen_environment="prod"}[10m])) > 15
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of Cluster Active Segment event update sample times errors
            description: "Grafana: https://go/cluster-active-group-sample-times-errors-dash"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/cluster-active-group-sample-times-errors-splunk
        - alert: Cluster Active event generation errors
          expr: sum(increase(mms_nds_atlasgrowth_cluster_active_batch_job_handler_failure_add_events_for_cluster_total{xgen_environment="prod"}[10m])) > 15
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of Cluster Active Segment event generation errors
            description: "Grafana: https://go/cluster-active-event-generation-errors-dash"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/cluster-active-event-generation-errors-splunk
    - name: ExperimentsSvc (cron job) alerts
      rules:
        - alert: ExperimentsSvc getExperiments errors
          expr: sum(increase(atlasgrowth_experimentsSvc_getExperiments_failure_total{xgen_environment="prod", xgen_app="mms-cron-jobs"}[6m])) > 1.5
          for: 2m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of failures when retrieving experiment configs
            description: "Grafana: https://bit.ly/3Bzdfhe"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3MCUpgC
        - alert: ExperimentsSvc getExperiments stale data in us-east-1 Region
          expr: (time() - max(max_over_time(atlasgrowth_experimentsSvc_getExperiments_last_successful_run_time_seconds{xgen_environment="prod", xgen_app="mms-cron-jobs", xgen_region="us-east-1"}[2h]))) > 1800
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Experiment configuration data hasn't been successfully refreshed for > 30 min in MMS
            description: "Grafana: http://go/grafana-atlas-growth-get-experiment-last-success"
            runbook_url: http://go/growth-on-call-playbook
        - alert: ExperimentsSvc processExperimentUpdates errors
          expr: sum(increase(atlasgrowth_experimentsSvc_processExperimentUpdates_failure_total{xgen_environment="prod", xgen_app="mms-cron-jobs"}[10m])) > 9
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of failures when processing retrieved experiments and updating
            description: "Grafana: http://go/process-experiments-updates-health-prod"
            runbook_url: http://go/growth-on-call-playbook-process-experiment-updates
            splunk: http://go/splunk-atlas-growth-experimentssvc-errors
        - alert: ExperimentsSvc processExperiments stale data in us-east-1 Region
          expr: (time() - max(max_over_time(atlasgrowth_experimentsSvc_processExperimentUpdates_last_successful_run_time_seconds{xgen_environment="prod", xgen_app="mms-cron-jobs", xgen_region="us-east-1"}[2h]))) > 1800
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Experiment configuration data hasn't been successfully processed for > 30 min in MMS
            description: "Grafana: http://go/grafana-atlas-growth-process-updates-last-success"
            runbook_url: http://go/growth-on-call-playbook
        - alert: ExperimentView invalid ExperimentView entering MMS from Pholiota
          expr: sum(increase(atlasgrowth_invalid_experiment_view_total{xgen_environment="prod"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of toABTest failures in ExperimentView
            description: "Invalid ExperimentView entering MMS from Pholiota | Grafana: http://go/growth-experimentview-invalid-grafana"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3efWWQJ
    - name: SegmentGroupSvc (cron job) alerts
      rules:
        - alert: SegmentGroupSvc Group broadcast errors
          expr: sum(increase(atlasgrowth_segmentGroupSvc_segment_call_error_total{xgen_environment="prod"}[5m])) > 1
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of Group broadcast errors
            description: "Grafana: https://bit.ly/3jD4Rr7"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3Bs5edS
        - alert: SegmentGroupSvc group queuing errors
          expr: sum(increase(atlasgrowth_segmentGroupSvc_orgs_queued_error_total{xgen_environment="prod"}[5m])) > 1
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of group/project queuing errors
            description: "Grafana: https://bit.ly/3mPV4jm"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/2WPeWbA
    - name: SegmentProfileSvc alerts
      rules:
        - alert: SegmentProfileSvc Get Org Traits High Rate Limit Errors
          expr: ( sum( rate( atlasgrowth_segmentProfileApiSvc_get_traits_failure_total{ xgen_environment="prod" , status_code="429"}[1d] ) ) or vector(0) ) / sum( rate( java_jersey_http_duration_seconds_count{ xgen_environment="prod", method="GET", path=~"/segmentTraits/{orgId}" }[1d] ) ) > 0.01
          for: 1h
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "SegmentProfileSvc: High rate of 429 (Too Many Requests) errors detected."
            description: "Grafana: http://go/segment-profile-svc-get-org-traits-rate-limit"
            runbook_url: http://go/growth-on-call-playbook
    - name: SmartLinkSvc alerts
      rules:
        - alert: SmartLinkSvc URL validation error
          expr: sum(increase(atlasgrowth_smartLinkSvc_url_validation_error_count_total{xgen_environment="prod"}[2h])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of SmartLinkSvc URL validation errors. (Redirects users to default URL.)
            description: "Grafana: https://bit.ly/3966iv3 | Amplitude: https://bit.ly/37tmHtc"
            runbook_url: http://go/growth-on-call-playbook
        - alert: SmartLinkSvc URL parsing error
          expr: sum(increase(atlasgrowth_smartLinkSvc_url_parsing_error_count_total{xgen_environment="prod"}[2h])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of SmartLinkSvc URL parsing errors. (Redirects users to root URL.)
            description: "Grafana: https://bit.ly/3soRm28 | Splunk /go URL top-down chart: http://go/splunk-smart-link-url-top-down-chart"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-url-parsing-errors"
        - alert: SmartLinkSvc URL resolving error
          expr: sum(increase(atlasgrowth_smartLinkSvc_url_resolving_error_count_total{xgen_environment="prod"}[2h])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of SmartLinkSvc URL resolving errors. (Redirects users to root URL.)
            description: "Grafana: https://bit.ly/3FsLr1m | Amplitude: https://bit.ly/37tmHtc"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3EoeGlX
        - alert: "SmartLinkResource 5xx errors GET /go"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/go",method="GET",xgen_app="mms-ui",status="5xx"}[1m]))) > 50
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High number of 5xx errors for the /go endpoint"
            description: |
              High number of 5xx errors for the /go endpoint
              For more details:
              - Grafana: http://go/grafana-growth-smartlinks-endpoints-dash
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-smartlinkresource-go-500s
    - name: InvitationCronSvc (cron job) Alerts
      rules:
        - alert: Invitation cron job failure
          expr: sum(increase(mms_nds_atlasgrowth_invitation_cron_svc_failure_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Invitation cron job failed
            description: "Grafana: http://go/grafana-atlas-growth-cron-jobs"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-invitation-cron-svc-errors
    - name: NpsSurveySender (cron job) Alerts
      rules:
        - alert: Survey failure count exceeded
          expr: sum(increase(atlasgrowth_npsSurveySender_survey_requests_total{xgen_environment="prod", status="failure"}[24h])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Survey failures exceeded threshold
            description: "At least one failed NPS survey send in the last 24 hours. Info & dashboards: http://go/NpsSurveySender"
        - alert: Mailing list failure count exceeded
          expr: sum(increase(atlasgrowth_npsSurveySender_mailinglist_requests_total{xgen_environment="prod", status="failure"}[24h])) > 4
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Mailing list failures exceeded threshold
            description: "Several qualtrics mailing list failed to be created in the last 24 hours. Info & dashboards: http://go/NpsSurveySender"
        - alert: Contact save failure count exceeded
          expr: sum(increase(atlasgrowth_npsSurveySender_contact_saves_total{xgen_environment="prod", status="failure"}[24h])) > 9
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Contact save failures exceeded threshold
            description: "Several qualtrics contact save failures in the last 24 hours. Info & dashboards: http://go/NpsSurveySender"
    - name: Cluster Template alerts
      rules:
        - alert: Template Cluster Creation IP Access Entry Adding error
          expr: sum(increase(mms_nds_template_cluster_failure_add_ip_list_entry_total{xgen_environment="prod"}[1h])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of errors related to adding a new IP Access List Entry during the Create Template Cluster flow
            description: >
              Errors adding a new IP Access List entry during the Create Template Cluster flow Grafana: http://go/atlas-growth-cluster-templates-grafana
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3sV4hwm

        - alert: Create Template Cluster Creation Failure error
          expr: sum(increase(mms_nds_create_template_cluster_failure_total{xgen_environment="prod"}[1h])) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of errors related to creating a new cluster during the Create Template Cluster flow
            description: >
              Errors creating a new cluster during the Create Template Cluster flow

              Grafana: http://go/atlas-growth-cluster-templates-create-grafana
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/atlas-growth-cluster-templates-create-splunk

        - alert: Create Template Cluster Creation recaptcha errors
          expr: sum(increase(mms_nds_create_template_cluster_null_recaptcha_total{xgen_environment="prod"}[1h])) > 40
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of recaptcha failures when creating a new cluster during the Create Template Cluster flow
            description: "ReCaptcha errors occurring during M0 creation on CST | Grafana: https://bit.ly/3vBCBhl"
            runbook_url: http://go/growth-on-call-playbook
            slunk: https://bit.ly/3Uf3uBj
        - alert: "ClusterDescriptionResource 5xx errors GET|POST /nds/clusters/{groupId}/template/{templateKey}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/nds/clusters/{groupId}/template/{templateKey}",xgen_app="mms-ui",method=~"(GET|POST)",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High number of 5xx errors for the /nds/clusters/{groupId}/template/{templateKey} endpoint"
            description: |
              High number of 5xx errors for the /nds/clusters/{groupId}/template/{templateKey} endpoint
              For more details:
              - Grafana: http://go/grafana-growth-clustertemplates-endpoints-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: "ClusterDescriptionResource High Latency GET /nds/clusters/{groupId}/template/{templateKey}"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path="/nds/clusters/{groupId}/template/{templateKey}",xgen_app="mms-ui",method="GET"}[5m])))) > 1000
          for: 10m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High Latency detected at p99 for the /nds/clusters/{groupId}/template/{templateKey} GET endpoint"
            description: |
              Latency at the 99th percentile for the /nds/clusters/{groupId}/template/{templateKey} GET endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-clustertemplates-endpoints-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: "ClusterDescriptionResource High Latency POST /nds/clusters/{groupId}/template/{templateKey}"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path="/nds/clusters/{groupId}/template/{templateKey}",xgen_app="mms-ui",method="POST"}[5m])))) > 4000
          for: 15m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High Latency detected at p99 for the /nds/clusters/{groupId}/template/{templateKey} POST endpoint"
            description: |
              Latency at the 99th percentile for the /nds/clusters/{groupId}/template/{templateKey} POST endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-clustertemplates-endpoints-dash
            runbook_url: http://go/growth-on-call-playbook
    - name: Cluster Drafts alerts
      rules:
        - alert: Cluster Draft Resource exceptions
          expr: sum(increase(mms_nds_atlasgrowth_cluster_draft_resource_exception_total{xgen_environment="prod"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Some number (>0 in last 5 min) of Cluster Draft Resource errors
            description: "Errors coming from cluster drafts endpoints | Grafana: http://go/atlas-growth-cluster-drafts-grafana"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/atlas-growth-cluster-drafts-splunk
        - alert: Cluster Draft Cron Svc Error
          expr: sum(increase(mms_nds_atlasgrowth_cluster_draft_cron_svc_failures_total{xgen_environment="prod"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Some number (>0 in last 5 min) of Cluster Draft Cron Svc errors
            description: "Errors querying for abandoned cluster drafts and sending segment events | Grafana: http://go/atlas-growth-cluster-drafts-grafana"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/atlas-growth-cluster-drafts-splunk
        - alert: Cluster Draft Dao Error
          expr: sum(increase(mms_nds_atlasgrowth_cluster_draft_dao_exception_total{xgen_environment="prod"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Some number (>0 in last 5 min) of Cluster Draft Dao errors
            description: "Errors querying for cluster drafts | Grafana: http://go/atlas-growth-cluster-drafts-grafana"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/atlas-growth-cluster-drafts-splunk
        - alert: "ClusterDraftsResource 5xx errors GET|PUT|DELETE /nds/clusterdrafts/{groupId}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/nds/clusterdrafts/{groupId}",method=~"(GET|PUT|DELETE)",xgen_app="mms-ui",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High number of 5xx errors for the /nds/clusterdrafts/{groupId} endpoint"
            description: |
              High number of 5xx errors for the /nds/clusterdrafts/{groupId} endpoint
              For more details:
              - Grafana: http://go/grafana-growth-clusterdrafts-endpoint-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: "ClusterDraftsResource High Latency GET|PUT|DELETE /nds/clusterdrafts/{groupId}"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path="/nds/clusterdrafts/{groupId}",xgen_app="mms-ui",method=~"(GET|PUT|DELETE)"}[5m])))) > 800
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High Latency detected at p99 for the /nds/clusterdrafts/{groupId} endpoint"
            description: |
              Latency at the 99th percentile for the /nds/clusterdrafts/{groupId} endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-clusterdrafts-endpoint-dash
            runbook_url: http://go/growth-on-call-playbook
    # Production Tips Wrap-up Epic: https://jira.mongodb.org/browse/CLOUDP-278324
    - name: Production Tips alerts
      rules:
        - alert: Production Tips Dismissed Toolbar Tip POST Endpoint errors (>0)
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/dismissedToolbarTip",method="POST",xgen_app="mms-ui",http_status_code=~"(5..)"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Some number (>0 in last 5 min) of errors for POST calls to dismissedToolbarTip endpoint
            description: "Grafana: http://go/grafana-dismissedtoolbartip | Epic: https://jira.mongodb.org/browse/CLOUDP-278324"
            runbook_url: http://go/growth-on-call-playbook
    - name: Personalization Wizard alerts
      rules:
        - alert: "PersonalizationWizardResource 5xx errors PUT /{groupId}/setup/personalization"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/{groupId}/setup/personalization",method="PUT",xgen_app="mms-ui",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High number of 5xx errors for the /{groupId}/setup/personalization endpoint"
            description: |
              High number of 5xx errors for the /{groupId}/setup/personalization endpoint
              For more details:
              - Grafana: http://go/grafana-growth-pw-endpoint-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: "PersonalizationWizardResource High Latency PUT /{groupId}/setup/personalization"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path="/{groupId}/setup/personalization",method="PUT",xgen_app="mms-ui"}[5m])))) > 800
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High Latency detected at p99 for the /{groupId}/setup/personalization endpoint"
            description: |
              Latency at the 99th percentile for the /{groupId}/setup/personalization endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-pw-endpoint-dash
            runbook_url: http://go/growth-on-call-playbook
    - name: Ecosystem alerts
      rules:
        - alert: "MarketplaceHomeResource 5xx errors GET /ecosystem(/integration)"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path=~"/ecosystem[/{integration:(.+)?}]*",method="GET",xgen_app="mms-ui",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High number of 5xx errors for the /ecosystem(/integration) endpoint"
            description: |
              High number of 5xx errors for the /ecosystem(/integration) endpoint
              For more details:
              - Grafana: http://go/grafana-growth-ecosystem-endpoints-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: "MarketplaceHomeResource High Latency GET /ecosystem(/integration)"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path=~"/ecosystem[/{integration:(.+)?}]*",method="GET",xgen_app="mms-ui"}[5m])))) > 2000
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High Latency detected at p99 for the /ecosystem(/integration) endpoint"
            description: |
              Latency at the 99th percentile for the /ecosystem(/integration) endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-ecosystem-endpoints-dash
            runbook_url: http://go/growth-on-call-playbook
        - alert: "MarketplaceHomeResource 5xx errors GET /ecosystem/sitemap.xml"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/ecosystem/sitemap.xml",method="GET",xgen_app="mms-ui",status="5xx"}[1m]))) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High number of 5xx errors for the /ecosystem/sitemap.xml endpoint"
            description: |
              High number of 5xx errors for the /ecosystem/sitemap.xml endpoint (allows search bots to index the partner product pages)
              For more details:
              - Grafana: http://go/grafana-growth-ecosystem-sitemap-dash
            runbook_url: http://go/growth-on-call-playbook
    - name: Atlas Home Center alerts
      rules:
        - alert: "AutomationAgentClientMetadataApiResource 5xx errors POST /agents/api/automation/clientMetadata/v1/{groupId}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/agents/api/automation/clientMetadata/v1/{groupId}",method="POST",xgen_app=~"mms-ui|mms-api-public",status="5xx"}[1m]))) > 10
          for: 10m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High number of 5xx errors for the /agents/api/automation/clientMetadata/v1/{groupId} endpoint"
            description: |
              High number of 5xx errors for the /agents/api/automation/clientMetadata/v1/{groupId} endpoint (InTel-owned, populates Home Center's App Module)
              For more details:
              - Grafana: http://go/grafana-growth-clientmetadata-endpoint-dash
            runbook_url: http://go/growth-on-call-playbook
    - name: GoTo alerts
      rules:
        - alert: "GoToResource 5xx errors GET /goto"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/goto",method="GET",xgen_app="mms-ui",status="5xx"}[1m]))) > 10
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "High number of 5xx errors for the /goto endpoint"
            description: |
              High number of 5xx errors for the /goto endpoint
              For more details:
              - Grafana: http://go/grafana-growth-goto-endpoints-dash
            runbook_url: http://go/growth-on-call-playbook
    # ANIS integration alerts (CLOUDP-256631)
    - name: Azure Native ISV Service Integration alerts
      rules:
        - alert: Azure Native Integration Resource Creation Failure
          expr: sum(increase(atlasgrowth_mms_growth_azure_native_integration_create_failure_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Azure native integration resource creation failed
            description: "Grafana: https://app.golinks.io/anis-creation-error"
            runbook_url: http://go/growth-on-call-playbook-anis-integration
            splunk: https://app.golinks.io/anis-creation-error-splunk
        - alert: Azure Native Integration Resource Deletion Failure
          expr: sum(increase(atlasgrowth_mms_growth_azure_native_integration_delete_failure_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Azure native integration resource deletion failed
            description: "Grafana: https://app.golinks.io/anis-deletion-error"
            runbook_url: http://go/growth-on-call-playbook-anis-integration
            splunk: https://app.golinks.io/anis-deletion-error-splunk
        - alert: Azure Native Integration Resource Update Failure
          expr: sum(increase(atlasgrowth_mms_growth_azure_native_integration_update_failure_total{xgen_environment="prod"}[5m])) > 0
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Azure native integration resource update failed
            description: "Grafana: https://app.golinks.io/anis-update-error"
            runbook_url: http://go/growth-on-call-playbook-anis-integration
            splunk: https://app.golinks.io/anis-update-error-splunk
        - alert: High proportion of rate-limited responses for ANIS create endpoint for 10 minutes
          expr: ((sum by (path, xgen_environment) (increase(java_jersey_http_duration_seconds_count{http_status_code="429", xgen_app="mms", path=~"/api/private/azurenative/v1/organizations", xgen_environment=~"prod"}[1m]))) / (sum by (path, xgen_environment) (increase(java_jersey_http_duration_seconds_count{xgen_app="mms", path=~"/api/private/azurenative/v1/organizations", xgen_environment=~"prod"}[1m])))) > 0.25
          for: 10m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }} {{ $labels.path }}
            summary: High proportion of rate-limited responses for ANIS create endpoint {{ $labels.path }} in {{ $labels.xgen_environment }}
            grafana_url: http://go/anis-grafana-dashboard
            splunk_dashboard: http://go/anis-creation-error-splunk
            runbook_url: http://go/growth-on-call-playbook-anis-integration
            description: >
              There is a high proportion of 429 responses being returned from the ANIS create endpoint in the {{ $labels.xgen_environment }} env.
    # Vercel Native Integration alerts (CLOUDP-307953)
    - name: Vercel Native Integration Alerts
      rules:
        - alert: Errors for Vercel Native marketplace endpoint
          # based on noisiness of 4xx errors, the status codes for this query may get narrowed down to just 429s and 500s
          # also based on data, we may want to split this into a "GET" endpoint alert and a non-GET endpoint alert with different thresholds
          expr: (sum by (path) (increase(java_jersey_http_duration_seconds_count{http_status_code=~"(4..|5..)", xgen_app="mms", path=~"/api/private/vercelnative/v1/installations(/.*)?", xgen_environment="prod"}[1m]))) > 5
          for: 3m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }} {{ $labels.path }}
            summary: Errors for Vercel Native marketplace endpoint "{{ $labels.path }}" in {{ $labels.xgen_environment }}
            description: "Grafana: http://go/growth-vercel-native-endpoint-status"
            splunk: "http://go/growth-splunk-vercel-native-errors"
            runbook_url: "http://go/growth-vercel-native-errors-playbook"
        - alert: Very High Errors for Vercel Native marketplace endpoint
          # based on noisiness of 4xx errors, the status codes for this query may get narrowed down to just 429s and 500s
          # also based on data, we may want to split this into a "GET" endpoint alert and a non-GET endpoint alert with different thresholds
          expr: (sum by (path) (increase(java_jersey_http_duration_seconds_count{http_status_code=~"(4..|5..)", xgen_app="mms", path=~"/api/private/vercelnative/v1/installations(/.*)?", xgen_environment="prod"}[1m]))) > 25
          for: 3m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }} {{ $labels.path }}
            summary: Errors for Vercel Native marketplace endpoint "{{ $labels.path }}" in {{ $labels.xgen_environment }}
            description: "Grafana: http://go/growth-vercel-native-endpoint-status"
            splunk: "http://go/growth-splunk-vercel-native-errors"
            runbook_url: "http://go/growth-vercel-native-errors-playbook"
        - alert: "High Latency for Vercel Native marketplace GET endpoint"
          expr: (1000 * histogram_quantile(.99, sum by (le, path) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path=~"/api/private/vercelnative/v1/installations(/.*)?",xgen_app="mms",method="GET"}[5m])))) > 100
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }} {{ $labels.path }}
            summary: Errors for Vercel Native marketplace GET endpoint "{{ $labels.path }}" in {{ $labels.xgen_environment }}
            description: "Grafana: http://go/growth-vercel-native-get-endpoint-latency"
            runbook_url: "http://go/growth-vercel-native-latency-playbook"
        - alert: "High Latency for Vercel Native marketplace not-GET endpoint"
          expr: (1000 * histogram_quantile(.99, sum by (le, path) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="prod",path=~"/api/private/vercelnative/v1/installations(/.*)?",xgen_app="mms",method!="GET"}[5m])))) > 6000
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }} {{ $labels.path }}
            summary: Errors for Vercel Native marketplace GET endpoint "{{ $labels.path }}" in {{ $labels.xgen_environment }}
            description: "Grafana: http://go/growth-vercel-native-not-get-endpoint-latency"
            runbook_url: "http://go/growth-vercel-native-latency-playbook"
        - alert: "Low Success Ratio for Vercel Native marketplace endpoint"
          # Counts 2xxs, 3xxs, and non-Growth-actionable 4xxs as successful requests
          expr: ((sum by (path) (increase(java_jersey_http_duration_seconds_count{http_status_code=~"(2..|3..|401|403|404|429)", xgen_app="mms", path=~"/api/private/vercelnative/v1/installations(/.*)?", xgen_environment="prod"}[5m]))) / (sum by (path) (increase(java_jersey_http_duration_seconds_count{xgen_app="mms", path=~"/api/private/vercelnative/v1/installations(/.*)?", xgen_environment="prod"}[5m])))) < 0.99
          for: 6m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }} {{ $labels.path }}
            summary: Low success ratio for Vercel Native marketplace endpoint "{{ $labels.path }}" in {{ $labels.xgen_environment }}
            description: >
              Grafana (ADJUST FILTER for the path in the alert to see the path-specific success ratio!): http://go/growth-vercel-native-dashboard
              Can also look at endpoint status overview: http://go/growth-vercel-native-endpoint-status
            splunk: "http://go/growth-splunk-vercel-native-errors"
            runbook_url: "http://go/growth-vercel-native-low-success-ratio-playbook"
        - alert: Very high proportion of rate-limited responses for Vercel Native marketplace endpoint
          expr: ((sum by (path) (increase(java_jersey_http_duration_seconds_count{http_status_code="429", xgen_app="mms", path=~"/api/private/vercelnative/v1/installations(/.*)?", xgen_environment="prod"}[1m]))) / (sum by (path) (increase(java_jersey_http_duration_seconds_count{xgen_app="mms", path=~"/api/private/vercelnative/v1/installations(/.*)?", xgen_environment="prod"}[1m])))) > 0.1
          for: 5m
          labels:
            severity: critical
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }} {{ $labels.path }}
            summary: Very high proportion of rate-limited responses for Vercel Native marketplace endpoint "{{ $labels.path }}" in {{ $labels.xgen_environment }}
            description: "Grafana: http://go/growth-vercel-native-endpoint-status"
            splunk: "http://go/growth-splunk-vercel-native-429"
            runbook_url: "http://go/growth-vercel-native-rate-limit-errors-playbook"
        - alert: VercelEventSvc Errors
          expr: sum(increase(atlasgrowth_vercel_event_svc_total{xgen_environment="prod", status="failure"}[1m])) > 1
          for: 3m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Errors for VercelEventSvc
            description: "Grafana: http://go/growth-vercel-native-verceleventsvc-health"
            splunk: "http://go/growth-splunk-vercel-native-verceleventsvc-errors"
            runbook_url: "http://go/growth-vercel-native-eventsvcjobhandler-playbook"
        - alert: VercelEventJobHandler Errors
          expr: sum(increase(atlasgrowth_vercel_native_event_job_handler_total{xgen_environment="prod", status="failure"}[1m])) > 1
          for: 3m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Errors for VercelEventJobHandler
            description: "Grafana: http://go/growth-vercel-native-verceleventjobhandler-health"
            splunk: "http://go/growth-splunk-vercel-native-verceleventjob-errors"
            runbook_url: "http://go/growth-vercel-native-eventsvcjobhandler-playbook"
        - alert: VercelNativeInstallationsSvcImpl Delete Installation Cascade Resource Deletion Requested
          expr: sum(increase(atlasgrowth_vercel_native_delete_installation_cascade_resource_deletion_total{xgen_environment="prod"}[1m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Received a Delete Installation request with "cascadeResourceDeletion" set to true
            description: "Grafana: http://go/growth-vercel-native-installationssvc-internal-err"
            splunk: "http://go/growth-splunk-vercel-native-installationssvc-del"
            runbook_url: "http://go/growth-vercel-native-cascade-resource-playbook"
    # @experiment Actionable Alert Emails | CLOUDP-296279
    - name: Actionable Alert Emails alerts
      rules:
        - alert: Actionable Alert Emails Template Render Failures
          expr: sum(increase(atlasgrowth_mms_growth_actionable_alert_emails_template_render_failure_total{xgen_environment="prod"}[5m])) > 0
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Actionable Alert Emails variant template failed to render
            description: "Grafana: http://go/grafana-actionable-alert-emails"
    # @experiment Enterprise Landing Page MVP | Jira Epic: CLOUDP-260839
    - name: Enterprise Landing Page alerts
      rules:
        - alert: Enterprise Landing Page GET Endpoint Failures
          expr: sum(increase(mms_growth_enterprise_landing_page_api_failure_total{xgen_environment="prod"}[5m])) > 0
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: Enterprise Landing Page GET endpoint failed
            description: "Grafana: http://go/grafana-enterprise-landing-page"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-enterprise-landing-page
    # @experiment Cluster Builder - Address Pain Points | Jira Epic: CLOUDP-261633
    - name: ClusterDescriptionResource endpoint alerts
      rules:
        - alert: ClusterDescriptionResource 5xx errors POST /nds/clusters/{groupId}
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/nds/clusters/{groupId}",method="POST",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 5xx errors for the /nds/clusters/{groupId} endpoint
            description: |
              High number of 5xx errors for the/nds/clusters/{groupId} endpoint
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk
        - alert: ClusterDescriptionResource 400 errors POST /nds/clusters/{groupId}
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/nds/clusters/{groupId}",method="POST",status="400"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 400 errors for the /nds/clusters/{groupId} endpoint
            description: |
              High number of 400 errors for the/nds/clusters/{groupId} endpoint
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk-400
        - alert: ClusterDescriptionResource 5xx errors PATCH /nds/clusters/{groupId}/{clusterName}
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/nds/clusters/{groupId}/{clusterName}",method="PATCH",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 5xx errors for the /nds/clusters/{groupId}/{clusterName} endpoint
            description: |
              High number of 5xx errors for the/nds/clusters/{groupId}/{clusterName} endpoint
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk
        - alert: ClusterDescriptionResource 400 errors PATCH /nds/clusters/{groupId}/{clusterName}
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/nds/clusters/{groupId}/{clusterName}",method="PATCH",status="400"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 400 errors for the /nds/clusters/{groupId}/{clusterName} endpoint
            description: |
              High number of 400 errors for the/nds/clusters/{groupId}/{clusterName} endpoint
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk-400
        - alert: ClusterDescriptionResource 5xx errors POST /nds/clusters/{groupId}/{clusterName}/processArgs
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/nds/clusters/{groupId}/{clusterName}/processArgs",method="POST",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 5xx errors for the /nds/clusters/{groupId}/{clusterName}/processArgs endpoint
            description: |
              High number of 5xx errors for the/nds/clusters/{groupId}/{clusterName}/processArgs endpoint
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk
        - alert: ClusterDescriptionResource 400 errors POST /nds/clusters/{groupId}/{clusterName}/processArgs
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/nds/clusters/{groupId}/{clusterName}/processArgs",method="POST",status="400"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 400 errors for the /nds/clusters/{groupId}/{clusterName}/processArgs endpoint
            description: |
              High number of 400 errors for the/nds/clusters/{groupId}/{clusterName}/processArgs endpoint
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation
              - Runbook: http://go/growth-on-call-playbook-cluster-description-error
              - Splunk: http://go/cluster-description-resource-splunk-400
        - alert: ClusterDescriptionResource 5xx errors PATCH /nds/clusters/{groupId}/{clusterName}/processArgs
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/nds/clusters/{groupId}/{clusterName}/processArgs",method="PATCH",status="5xx"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 5xx errors for the /nds/clusters/{groupId}/{clusterName}/processArgs endpoint
            description: |
              High number of 5xx errors for the/nds/clusters/{groupId}/{clusterName}/processArgs endpoint
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk
        - alert: ClusterDescriptionResource 400 errors PATCH /nds/clusters/{groupId}/{clusterName}/processArgs
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="prod",path="/nds/clusters/{groupId}/{clusterName}/processArgs",method="PATCH",status="400"}[1m]))) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: High number of 400 errors for the /nds/clusters/{groupId}/{clusterName}/processArgs endpoint
            description: |
              High number of 400 errors for the/nds/clusters/{groupId}/{clusterName}/processArgs endpoint
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk-400
