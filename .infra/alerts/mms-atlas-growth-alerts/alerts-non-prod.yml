---
# yamllint disable rule:quoted-strings rule:brackets rule:empty-lines
pagerdutyService:
  enabled: true
  fullnameOverride: mms-atlas-growth
  escalationPolicyName: "Atlas Growth On-Call"
  annotations:
    pagerduty.10gen.cc/grafana-uid: MQ5OJEENz
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: Segment event job submission (cron job) alerts
      rules:
        - alert: "[NON-PROD] SegmentEventSvc job submission errors"
          expr: sum(increase(atlasgrowth_segmentEventSvc_job_submissions_failure_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 2
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of SegmentEventSvc job submission failures"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: bit.ly/3XiG8u7"
            runbook_url: http://go/growth-on-call-playbook
            splunk: "bit.ly/42PWEDi"
        - alert: "[NON-PROD] SegmentEventJobSubmissionSvcImpl job submission errors"
          expr: sum(increase(atlasgrowth_segmentEventJobSubmissionSvcImpl_job_submissions_failure_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 2
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of SegmentEventJobSubmissionSvcImpl job submission failures"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: bit.ly/3Po6OYK"
            runbook_url: http://go/growth-on-call-playbook
            splunk: bit.ly/42PWEDi
    - name: SegmentEventJobHandler (cron job) alerts
      rules:
        - alert: "[NON-PROD] SegmentEventJobHandler Create Identify errors"
          expr: sum(increase(atlasgrowth_segmentEventJobHandler_identify_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 2
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of Identify object creation errors across all events in SegmentEventJobHandler (new Segment Event service)."
            description: "Grafana: https://go/segment-event-job-handler-identify-errors-dash/{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://go/segment-event-job-handler-identify-errors-splunk/{{ $labels.xgen_environment }}
        - alert: "[NON-PROD] SegmentEventJobHandler Create Track errors"
          expr: sum(increase(atlasgrowth_segmentEventJobHandler_track_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 2
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of Track object creation errors across all events in SegmentEventJobHandler (new Segment Event service)."
            description: "Grafana: https://go/segment-event-job-handler-track-errors-dash/{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://go/segment-event-job-handler-track-errors-splunk/{{ $labels.xgen_environment }}
        - alert: "[NON-PROD] SegmentEventJobHandler general errors"
          expr: sum(increase(atlasgrowth_segmentEventJobHandler_general_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[20m])) > 5
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of general errors across all events in SegmentEventJobHandler (new Segment Event service)."
            description: "Grafana: https://go/segment-event-job-handler-general-errors-dash/{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/segment-event-job-handler-general-errors-splunk/{{ $labels.xgen_environment }}
    - name: ABTestSvc error alerts
      rules:
        - alert: "[NON-PROD] ABTestSvc allocation error"
          expr: sum(increase(atlasgrowth_abTestSvc_test_allocation_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[60m])) > 2
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of test allocation failures"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3Bzbh0k"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3yD9MfE
        - alert: "[NON-PROD] ABTestSvc duplicate allocation error"
          expr: sum(increase(atlasgrowth_abTestSvc_duplicate_assignment_key_exception_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 2
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of test duplicate allocation failures"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3BM8PE7"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-duplicate-allocation-errors
        - alert: "[NON-PROD] ABTestSvc get assignments error"
          expr: sum(increase(atlasgrowth_abTestSvc_get_assignments_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) > 2
          for: 3m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of failures when retrieving test assignment data"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3jBOURQ"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3zDXkxA
        - alert: "[NON-PROD] ABTestSvc get tests errors"
          expr: sum(increase(atlasgrowth_abTestSvc_get_tests_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) > 5
          for: 3m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of failures when retrieving test data"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3DC495k"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3DxfJyF
        - alert: "[NON-PROD] ABTestSvc delete tests errors"
          expr: sum(increase(atlasgrowth_abTestSvc_delete_test_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 5
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of failures when deleting test data"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3jxnX1M"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/2WDErgg
        - alert: "[NON-PROD] ABTestSvc upsert tests errors"
          expr: sum(increase(atlasgrowth_abTestSvc_upsert_test_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 5
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of failures when upserting test data"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3DFk7M7"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3jCJs1g
        - alert: "[NON-PROD] ABTestSvc choose new assignment errors"
          expr: sum(increase(atlasgrowth_abTestSvc_choose_new_assignment_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 2
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of failures when choosing new assignment for users"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3Bzbh0k"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3ypdtqQ
        - alert: "[NON-PROD] ABTestSvc socket exceptions"
          expr: sum(increase(atlasgrowth_abTestSvc_socket_exception_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) > 50
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of socket exceptions, often associated with deploys or SRE maintenance"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://grafana.corp.mongodb.com/d/XOjtOcM7k/experiment-allocations?orgId=20&viewPanel=15"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3D%22mms%22%20ABTestSvc%20%22com.mongodb.MongoSocketOpenException%22&display.page.search.mode=smart&dispatch.sample_ratio=1&earliest=-7d%40h&latest=now&sid=1648148472.848679
        - alert: "[NON-PROD] ABTestSvc get linked holdout test for entity errors"
          expr: sum(increase(atlasgrowth_abTestSvc_get_linked_holdout_test_for_entity_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 5
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of failures when getting the linked holdout test for an entity"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/45TzP44"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/46cGcjt
        - alert: "[NON-PROD] ABTestSvc get current and launched assignments error"
          expr: sum(increase(atlasgrowth_abTestSvc_get_current_and_launched_assignments_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of failures when retrieving current and launched assignments"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3MZ3wYW"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/45tx4p6
        - alert: "[NON-PROD] ABTestSvc get ghost assignments error"
          expr: sum(increase(atlasgrowth_abTestSvc_get_ghost_assignments_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 5
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of failures when retrieving ghost assignments"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3MZ3wYW"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3que0rU
    - name: ABTestAssignmentResource /v2 endpoint alerts
      rules:
        - alert: "[NON-PROD] ABTestAssignmentResource 5xx errors GET /v2/assignment(s)"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path=~"/v2/assignment[s]*",xgen_app="mms-ui",method="GET",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /v2/assignment(s) endpoint"
            description: |
              High number of 5xx errors for the /v2/assignment(s) endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-assignment-v2-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ABTestAssignmentResource High Latency GET /v2/assignment(s)"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path=~"/v2/assignment[s]*",xgen_app="mms-ui",method="GET"}[5m])))) > 800
          for: 16m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High Latency detected at p99 for the /v2/assignment(s) endpoint"
            description: |
              Latency at the 99th percentile for the /v2/assignment(s) endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-assignment-v2-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ABTestAssignmentResource 5xx errors POST /v2/assign"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/v2/assign",xgen_app="mms-ui",method="POST",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /v2/assign endpoint"
            description: |
              High number of 5xx errors for the v2/assign endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-assign-v2-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ABTestAssignmentResource High Latency POST /v2/assign"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/v2/assign",xgen_app="mms-ui",method="POST"}[5m])))) > 800
          for: 16m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High Latency detected at p99 for the /v2/assign endpoint"
            description: |
              Latency at the 99th percentile for the assign endpoint is elevated
              For more details (UPDATE THE QUERY FOR NON-PROD ENV):
              - Grafana: http://go/grafana-growth-assign-latency
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ABTestAssignmentResource 5xx errors POST /v2/assignByPoint"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/v2/assignByPoint",xgen_app="mms-ui",method="POST",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /v2/assignByPoint endpoint"
            description: |
              High number of 5xx errors for the v2/assignByPoint endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-assignbypoint-v2-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ABTestAssignmentResource High Latency POST /v2/assignByPoint"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/v2/assignByPoint",xgen_app="mms-ui",method="POST"}[5m])))) > 800
          for: 16m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High Latency detected at p99 for the /v2/assignByPoint endpoint"
            description: |
              Latency at the 99th percentile for the /v2/assignByPoint endpoint is elevated
              For more details (UPDATE THE QUERY FOR NON-PROD ENV):
              - Grafana: http://go/grafana-growth-assignbypoint-latency
            runbook_url: http://go/growth-on-call-playbook
    - name: ABTestAssignmentResource /v3 endpoint alerts
      rules:
        - alert: "[NON-PROD] ABTestAssignmentResource 5xx errors GET /v3/assignment/{testName}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/v3/assignment/{testName}",xgen_app="mms-ui",method="GET",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /v3/assignment/{testName} endpoint"
            description: |
              High number of 5xx errors for the /v3/assignment/{testName} endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-assignment-v3-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ABTestAssignmentResource High Latency GET /v3/assignment/{testName}"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/v3/assignment/{testName}",xgen_app="mms-ui",method="GET"}[5m])))) > 800
          for: 11m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High Latency detected at p99 for the /v3/assignment/{testName} endpoint"
            description: |
              Latency at the 99th percentile for the /v3/assignment/{testName} endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-assignment-v3-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ABTestAssignmentResource 5xx errors POST /v3/assignment"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/v3/assignment",xgen_app="mms-ui",method="POST",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /v3/assignment endpoint"
            description: |
              High number of 5xx errors for the /v3/assignment endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-assign-v3-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ABTestAssignmentResource High Latency POST /v3/assignment"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/v3/assignment",xgen_app="mms-ui",method="POST"}[5m])))) > 800
          for: 11m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High Latency detected at p99 for the /v3/assignment endpoint"
            description: |
              Latency at the 99th percentile for the /v3/assignment endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-assign-v3-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ABTestAssignmentResource 5xx errors POST /v3/assignmentsByAllocationPoints"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/v3/assignmentsByAllocationPoints",xgen_app="mms-ui",method="POST",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] Hi gh number of 5xx errors for the /v3/assignmentsByAllocationPoints endpoint"
            description: |
              High number of 5xx errors for the /v3/assignmentsByAllocationPoints endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-assignbypoint-v3-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ABTestAssignmentResource High Latency POST /v3/assignmentsByAllocationPoints"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/v3/assignmentsByAllocationPoints",xgen_app="mms-ui",method="POST"}[5m])))) > 800
          for: 11m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD]   High Latency detected at p99 for the /v3/assignmentsByAllocationPoints endpoint"
            description: |
              Latency at the 99th percentile for the /v3/assignmentsByAllocationPoints endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-assignbypoint-v3-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
    - name: CMABSvc alerts
      rules:
        - alert: "[NON-PROD] CMABSvc get features error"
          expr: sum(increase(atlasgrowth_CMABSvc_getFeatures_failure_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[60m])) > 2
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of get feature failures"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3CJYLxX"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3ELivSr
        - alert: "[NON-PROD] CMABSvc get allocation error"
          expr: sum(increase(atlasgrowth_CMABSvc_getCmabAllocation_failure_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[60m])) > 5
          for: 1m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of allocation failures against Okta or DSplatform model endpoint"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3CJYLxX"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3EsYhN2
        - alert: "[NON-PROD] CMAB model not deployed"
          expr: sum(increase(atlasgrowth_CMABSvc_model_endpoint_not_deployed_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) > 10
          for: 70m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] Getting CMAB model not deployed warnings for over an hour. We expect these to go away within the hour because the cron job which deploys models runs once an hour."
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3CJYLxX | Splunk: https://bit.ly/46y1S8W | http://go/growth-on-call-playbook"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/46y1S8W
    - name: Cluster Active (cron job) alerts
      rules:
        - alert: "[NON-PROD] Cluster Active cron job failure"
          expr: sum(increase(mms_nds_atlasgrowth_cluster_active_failure_cron_svc_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] Cluster Active cron job failed"
            description: "Grafana: https://go/cluster-active-cron-job-error-dash/{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/cluster-active-cron-job-error-splunk/{{ $labels.xgen_environment }}
        - alert: "[NON-PROD] Cluster Active group process errors"
          expr: sum(increase(mms_nds_atlasgrowth_cluster_active_batch_job_handler_failure_run_check_for_group_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1h])) > 5
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of Cluster Active group processing failures"
            description: "Grafana: https://go/cluster-active-group-process-failure-dash/{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/cluster-active-group-process-failure-splunk/{{ $labels.xgen_environment }}
        - alert: "[NON-PROD] Cluster Active event broadcast errors"
          expr: sum(increase(mms_nds_atlasgrowth_cluster_active_batch_job_handler_failure_broadcast_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[10m])) > 5
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of Cluster Active Segment event broadcast errors"
            description: "Grafana:  https://go/cluster-active-broadcast-failures-dash/{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/cluster-active-broadcast-failures-splunk/{{ $labels.xgen_environment }}
        - alert: "[NON-PROD] Cluster Active update group sample times errors"
          expr: sum(increase(mms_nds_atlasgrowth_cluster_active_batch_job_handler_failure_update_group_sample_times_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[10m])) > 5
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of Cluster Active Segment event update sample times errors"
            description: "Grafana: https://go/cluster-active-group-sample-times-errors-dash/{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/cluster-active-group-sample-times-errors-splunk/{{ $labels.xgen_environment }}
        - alert: "[NON-PROD] Cluster Active event generation errors"
          expr: sum(increase(mms_nds_atlasgrowth_cluster_active_batch_job_handler_failure_add_events_for_cluster_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[10m])) > 5
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of Cluster Active Segment event generation errors"
            description: "Grafana: https://go/cluster-active-event-generation-errors-dash/{{ $labels.xgen_environment }}"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/cluster-active-event-generation-errors-splunk/{{ $labels.xgen_environment }}
    - name: ExperimentsSvc (cron job) alerts
      rules:
        - alert: "[NON-PROD] ExperimentsSvc getExperiments errors"
          expr: sum(increase(atlasgrowth_experimentsSvc_getExperiments_failure_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", xgen_app="mms-cron-jobs"}[6m])) > 1.5
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of failures when retrieving experiment configs"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3Bzdfhe"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3MCUpgC
        - alert: "[NON-PROD] ExperimentsSvc processExperimentUpdates errors"
          expr: sum(increase(atlasgrowth_experimentsSvc_processExperimentUpdates_failure_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", xgen_app="mms-cron-jobs"}[30m])) > 5
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of failures when processing retrieved experiments and updating"
            description: "Grafana: http://go/process-experiments-updates-health-non-prod"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/splunk-atlas-growth-experimentssvc-errors
        - alert: "[NON-PROD] ExperimentView invalid ExperimentView entering MMS from Pholiota"
          expr: sum(increase(atlasgrowth_invalid_experiment_view_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of toABTest failures in ExperimentView"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: http://go/growth-experimentview-invalid-grafana"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3efWWQJ
    - name: SegmentGroupSvc (cron job) alerts
      rules:
        - alert: "[NON-PROD] SegmentGroupSvc Group broadcast errors"
          expr: sum(increase(atlasgrowth_segmentGroupSvc_segment_call_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 1
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of Group broadcast errors"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3jD4Rr7"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3Bs5edS
        - alert: "[NON-PROD] SegmentGroupSvc group queuing errors"
          expr: sum(increase(atlasgrowth_segmentGroupSvc_orgs_queued_error_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 1
          for: 2m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of group/project queuing errors"
            description: "UPDATE THE QUERY FOR NON-PROD ENV: Grafana: https://bit.ly/3mPV4jm"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/2WPeWbA
    - name: SmartLinkSvc alerts
      rules:
        - alert: "[NON-PROD] SmartLinkResource 5xx errors GET /go"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/go",method="GET",xgen_app="mms-ui",status="5xx"}[1m]))) > 2
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /go endpoint"
            description: |
              High number of 5xx errors for the /go endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-smartlinks-endpoints-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
    - name: Cluster Template alerts
      rules:
        - alert: "[NON-PROD] Template Cluster Creation IP Access Entry Adding error"
          expr: sum(increase(mms_nds_template_cluster_failure_add_ip_list_entry_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1h])) > 2
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of errors related to adding a new IP Access List Entry during the Create Template Cluster flow"
            description: >
              Errors adding a new IP Access List entry during the Create Template Cluster flow UPDATE BELOW QUERIES FOR NON-PROD:

              Grafana: http://go/atlas-growth-cluster-templates-grafana
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3sV4hwm

        - alert: "[NON-PROD] Create Template Cluster Creation Failure error"
          expr: sum(increase(mms_nds_create_template_cluster_failure_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1h])) > 2
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of errors related to creating a new cluster during the Create Template Cluster flow"
            description: >
              Errors creating a new cluster during the Create Template Cluster flow

              UPDATE BELOW QUERIES FOR NON-PROD:

              Grafana: http://go/atlas-growth-cluster-templates-create-grafana-dev
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/atlas-growth-cluster-templates-create-splunk-dev

        - alert: "[NON-PROD] Create Template Cluster Creation recaptcha errors"
          expr: sum(increase(mms_nds_create_template_cluster_null_recaptcha_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1h])) > 5
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of recaptcha failures when creating a new cluster during the Create Template Cluster flow"
            description: "UPDATE QUERIES FOR NON-PROD: Grafana: https://bit.ly/3vBCBhl"
            runbook_url: http://go/growth-on-call-playbook
            splunk: https://bit.ly/3Uf3uBj
        - alert: "[NON-PROD] ClusterDescriptionResource 5xx errors GET|POST /nds/clusters/{groupId}/template/{templateKey}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusters/{groupId}/template/{templateKey}",xgen_app="mms-ui",method=~"(GET|POST)",status="5xx"}[1m]))) > 2
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /nds/clusters/{groupId}/template/{templateKey} endpoint"
            description: |
              High number of 5xx errors for the /nds/clusters/{groupId}/template/{templateKey} endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-clustertemplates-endpoints-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ClusterDescriptionResource High Latency GET /nds/clusters/{groupId}/template/{templateKey}"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusters/{groupId}/template/{templateKey}",xgen_app="mms-ui",method="GET"}[5m])))) > 800
          for: 11m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High Latency detected at p99 for the /nds/clusters/{groupId}/template/{templateKey} GET endpoint"
            description: |
              Latency at the 99th percentile for the /nds/clusters/{groupId}/template/{templateKey} GET endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-clustertemplates-endpoints-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ClusterDescriptionResource High Latency POST /nds/clusters/{groupId}/template/{templateKey}"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusters/{groupId}/template/{templateKey}",xgen_app="mms-ui",method="POST"}[5m])))) > 4000
          for: 11m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High Latency detected at p99 for the /nds/clusters/{groupId}/template/{templateKey} POST endpoint"
            description: |
              Latency at the 99th percentile for the /nds/clusters/{groupId}/template/{templateKey} POST endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-clustertemplates-endpoints-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
    - name: Cluster Drafts alerts
      rules:
        - alert: "[NON-PROD] Cluster Draft Resource exceptions"
          expr: sum(increase(mms_nds_atlasgrowth_cluster_draft_resource_exception_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] Some number (>0 in last 5 min) of Cluster Draft Resource errors"
            description: "UPDATE QUERY FOR NON-PROD ENVS: Grafana: http://go/atlas-growth-cluster-drafts-grafana"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/atlas-growth-cluster-drafts-splunk
        - alert: "[NON-PROD] Cluster Draft Cron Svc Error"
          expr: sum(increase(mms_nds_atlasgrowth_cluster_draft_cron_svc_failures_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] Some number (>0 in last 5 min) of Cluster Draft Cron Svc errors"
            description: "UPDATE QUERY FOR NON-PROD ENV: Grafana: http://go/atlas-growth-cluster-drafts-grafana"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/atlas-growth-cluster-drafts-splunk
        - alert: "[NON-PROD] Cluster Draft Dao Error"
          expr: sum(increase(mms_nds_atlasgrowth_cluster_draft_dao_exception_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] Some number (>0 in last 5 min) of Cluster Draft Dao errors"
            description: "UPDATE QUERY FOR NON-PROD ENV: Grafana: http://go/atlas-growth-cluster-drafts-grafana"
            runbook_url: http://go/growth-on-call-playbook
            splunk: http://go/atlas-growth-cluster-drafts-splunk
        - alert: "[NON-PROD] ClusterDraftsResource 5xx errors GET|PUT|DELETE /nds/clusterdrafts/{groupId}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusterdrafts/{groupId}",method=~"(GET|PUT|DELETE)",xgen_app="mms-ui",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /nds/clusterdrafts/{groupId} endpoint"
            description: |
              High number of 5xx errors for the /nds/clusterdrafts/{groupId} endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-clusterdrafts-endpoint-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] ClusterDraftsResource High Latency GET|PUT|DELETE /nds/clusterdrafts/{groupId}"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusterdrafts/{groupId}",xgen_app="mms-ui",method=~"(GET|PUT|DELETE)"}[5m])))) > 800
          for: 11m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High Latency detected at p99 for the /nds/clusterdrafts/{groupId} endpoint"
            description: |
              Latency at the 99th percentile for the /nds/clusterdrafts/{groupId} endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-clusterdrafts-endpoint-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
    # Production Tips Wrap-up Epic: https://jira.mongodb.org/browse/CLOUDP-278324
    - name: Production Tips alerts
      rules:
        - alert: "[NON-PROD] Production Tips Dismissed Toolbar Tip POST Endpoint errors (>0)"
          expr: sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/dismissedToolbarTip",method="POST",xgen_app="mms-ui",http_status_code=~"(5..)"}[5m])) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] Some number (>0 in last 5 min) of errors for POST calls to dismissedToolbarTip endpoint"
            description: "UPDATE QUERIES FOR NON-PROD: Grafana: http://go/grafana-dismissedtoolbartip | Epic: https://jira.mongodb.org/browse/CLOUDP-278324"
            runbook_url: http://go/growth-on-call-playbook
    - name: Personalization Wizard alerts
      rules:
        - alert: "[NON-PROD] PersonalizationWizardResource 5xx errors PUT /{groupId}/setup/personalization"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/{groupId}/setup/personalization",method="PUT",xgen_app="mms-ui",status="5xx"}[1m]))) > 2
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /{groupId}/setup/personalization endpoint"
            description: |
              High number of 5xx errors for the /{groupId}/setup/personalization endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-pw-endpoint-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] PersonalizationWizardResource High Latency PUT /{groupId}/setup/personalization"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/{groupId}/setup/personalization",method="PUT",xgen_app="mms-ui"}[5m])))) > 800
          for: 11m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High Latency detected at p99 for the /{groupId}/setup/personalization endpoint"
            description: |
              Latency at the 99th percentile for the /{groupId}/setup/personalization endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-pw-endpoint-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
    - name: Ecosystem alerts
      rules:
        - alert: "[NON-PROD] MarketplaceHomeResource 5xx errors GET /ecosystem(/integration)"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path=~"/ecosystem[/{integration:(.+)?}]*",method="GET",xgen_app="mms-ui",status="5xx"}[1m]))) > 2
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /ecosystem(/integration) endpoint"
            description: |
              High number of 5xx errors for the /ecosystem(/integration) endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-ecosystem-endpoints-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
        - alert: "[NON-PROD] MarketplaceHomeResource High Latency GET /ecosystem(/integration)"
          expr: (1000 * histogram_quantile(.99, sum by (le) (rate(java_jersey_http_duration_seconds_bucket{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path=~"/ecosystem[/{integration:(.+)?}]*",method="GET",xgen_app="mms-ui"}[5m])))) > 2000
          for: 11m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High Latency detected at p99 for the /ecosystem(/integration) endpoint"
            description: |
              Latency at the 99th percentile for the /ecosystem(/integration) endpoint is elevated
              For more details:
              - Grafana: http://go/grafana-growth-ecosystem-endpoints-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
    - name: GoTo alerts
      rules:
        - alert: "[NON-PROD] GoToResource 5xx errors GET /goto"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/goto",method="GET",xgen_app="mms-ui",status="5xx"}[1m]))) > 5
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /goto endpoint"
            description: |
              High number of 5xx errors for the /goto endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-goto-endpoints-dash-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook
    # @experiment Cluster Builder - Address Pain Points | Jira Epic: CLOUDP-261633
    - name: ClusterDescriptionResource endpoint alerts
      rules:
        - alert: "[NON-PROD] ClusterDescriptionResource 5xx errors POST /nds/clusters/{groupId}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusters/{groupId}",method="POST",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /nds/clusters/{groupId} endpoint"
            description: |
              High number of 5xx errors for the/nds/clusters/{groupId} endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk-dev
        - alert: "[NON-PROD] ClusterDescriptionResource 400 errors POST /nds/clusters/{groupId}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusters/{groupId}",method="POST",status="400"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 400 errors for the /nds/clusters/{groupId} endpoint"
            description: |
              High number of 400 errors for the/nds/clusters/{groupId} endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk-dev-400
        - alert: "[NON-PROD] ClusterDescriptionResource 5xx errors PATCH /nds/clusters/{groupId}/{clusterName}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusters/{groupId}/{clusterName}",method="PATCH",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /nds/clusters/{groupId}/{clusterName} endpoint"
            description: |
              High number of 5xx errors for the/nds/clusters/{groupId}/{clusterName} endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk-dev
        - alert: "[NON-PROD] ClusterDescriptionResource 400 errors PATCH /nds/clusters/{groupId}/{clusterName}"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusters/{groupId}/{clusterName}",method="PATCH",status="400"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 400 errors for the /nds/clusters/{groupId}/{clusterName} endpoint"
            description: |
              High number of 400 errors for the/nds/clusters/{groupId}/{clusterName} endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk-dev-400
        - alert: "[NON-PROD] ClusterDescriptionResource 5xx errors POST /nds/clusters/{groupId}/{clusterName}/processArgs"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusters/{groupId}/{clusterName}/processArgs",method="POST",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /nds/clusters/{groupId}/{clusterName}/processArgs endpoint"
            description: |
              High number of 5xx errors for the/nds/clusters/{groupId}/{clusterName}/processArgs endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk-dev
        - alert: "[NON-PROD] ClusterDescriptionResource 400 errors POST /nds/clusters/{groupId}/{clusterName}/processArgs"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusters/{groupId}/{clusterName}/processArgs",method="POST",status="400"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 400 errors for the /nds/clusters/{groupId}/{clusterName}/processArgs endpoint"
            description: |
              High number of 400 errors for the/nds/clusters/{groupId}/{clusterName}/processArgs endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk-dev-400
        - alert: "[NON-PROD] ClusterDescriptionResource 5xx errors PATCH /nds/clusters/{groupId}/{clusterName}/processArgs"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusters/{groupId}/{clusterName}/processArgs",method="PATCH",status="5xx"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 5xx errors for the /nds/clusters/{groupId}/{clusterName}/processArgs endpoint"
            description: |
              High number of 5xx errors for the/nds/clusters/{groupId}/{clusterName}/processArgs endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk-dev
        - alert: "[NON-PROD] ClusterDescriptionResource 400 errors PATCH /nds/clusters/{groupId}/{clusterName}/processArgs"
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path="/nds/clusters/{groupId}/{clusterName}/processArgs",method="PATCH",status="400"}[1m]))) > 5
          for: 5m
          labels:
            severity: warning
          annotations:
            alert_title_override: ({{ .Values.additionalLabels.xgen_environment }}) {{ $labels.alertname }}
            summary: "[NON-PROD] High number of 400 errors for the /nds/clusters/{groupId}/{clusterName}/processArgs endpoint"
            description: |
              High number of 400 errors for the/nds/clusters/{groupId}/{clusterName}/processArgs endpoint in nonprod
              For more details:
              - Grafana: http://go/grafana-growth-cluster-creation-dev (update for relevant nonprod env)
            runbook_url: http://go/growth-on-call-playbook-cluster-description-error
            splunk: http://go/cluster-description-resource-splunk-dev-400
