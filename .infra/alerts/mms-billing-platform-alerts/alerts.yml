---
# yamllint disable rule:quoted-strings
pagerdutyService:
  enabled: true
  name: mms-billing-platform
  fullnameOverride: mms-billing-platform
  escalationPolicyName: Billing Platform Escalation
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
prometheusRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: billing_client_facing_performance
      rules:
        - alert: Line Item Query Latency High
          expr: (1000 * histogram_quantile(0.90, sum(rate(billing_usagedetails_query_duration_seconds_bucket{xgen_environment="$environment",operationName=~"queryLineItemsByInvoiceAndCriteria"}[5m])) by (le))) > 10000
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: Line Item Query Latency Outside Threshold
            description: "The query against the line item collection that servers the UI and the API has exceeded threshold for the last 15 minutes in prod. Current value: {{ $value }} milliseconds %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/YpasWAqSz?orgId=20'
    - name: salesforce_integrations
      rules:
        - alert: Cron job updateSalesforceCacheId was not triggered in PROD
          expr: sum by (cron_job_name) (increase (mms_core_cron_jobs_successful_execution_time_seconds_count{app="mms-cron-jobs",xgen_environment="prod",cron_job_name=~"updateSalesforceIdCacheTrigger", job_lock_acquired_result="JOB_ACQUIRED_LOCK"}[24h])) == 0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: "Salesforce Cache has not been updated in past 24 hours in PROD"
            description: "The cron job to trigger the update of the SalesforceIdCache has not been triggered in the past 24 hours"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/xEZOy1VNz?orgId=20'
        - alert: Cron job keepIdsInSync job was not triggered in PROD
          expr: sum by (cron_job_name) (increase (mms_core_cron_jobs_successful_execution_time_seconds_count{app="mms-cron-jobs",xgen_environment="prod",cron_job_name=~"keepIdsInSyncTrigger", job_lock_acquired_result="JOB_ACQUIRED_LOCK"}[24h])) == 0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: "KeepIdsInSync has not been run in past 24 hours in PROD"
            description: "The cron job to trigger the keepIdsInSync job has not been triggered in the past 24 hours"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/yJiUs1VHk?orgId=20'
        - alert: keepIdsInSync User Sync Error High in NON PROD
          expr: (1 - (sum by (xgen_environment)(increase(billing_salesforce_user_sync_error_count{xgen_environment=~"dev|qa|staging"}[24h])) / sum by (xgen_environment) (increase(billing_salesforce_user_sync_processed_count{xgen_environment=~"dev|qa|staging"}[24h])))) * 100 < 99
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: keepIdsInSync user sync error rate above 1%
            description: "keepIdsInSync user sync error rate has exceeded threshold over last 24 hours in {{ $labels.xgen_environment }}. Current success rate: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/Rrp1HTlSz?orgId=20'
        - alert: keepIdsInSync User Sync Error High in PROD
          expr: (1 - (sum by (xgen_environment)(increase(billing_salesforce_user_sync_error_count{xgen_environment=~"prod"}[24h])) / sum by (xgen_environment) (increase(billing_salesforce_user_sync_processed_count{xgen_environment=~"prod"}[24h])))) * 100 < 99
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: keepIdsInSync user sync error rate above 1%
            description: "keepIdsInSync user sync error rate has exceeded threshold over last 24 hours in {{ $labels.xgen_environment }}. Current success rate: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/Rrp1HTlSz?orgId=20'
        - alert: keepIdsInSync User Organizations Sync Error High in NON PROD
          expr: (1 - (sum by (xgen_environment)(increase(billing_salesforce_user_org_sync_error_count{xgen_environment=~"dev|qa|staging"}[24h])) / sum by (xgen_environment) (increase(billing_salesforce_user_org_sync_processed_count{xgen_environment=~"dev|qa|staging"}[24h])))) * 100 < 99
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: keepIdsInSync user organizations sync error rate above 1%
            description: "keepIdsInSync user organizations sync error rate has exceeded threshold over last 24 hours in {{ $labels.xgen_environment }}. Current success rate: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/Rrp1HTlSz?orgId=20'
        - alert: keepIdsInSync User Organizations Sync Error High in PROD
          expr: (1 - (sum by (xgen_environment)(increase(billing_salesforce_user_org_sync_error_count{xgen_environment=~"prod"}[24h])) / sum by (xgen_environment) (increase(billing_salesforce_user_org_sync_processed_count{xgen_environment=~"prod"}[24h])))) * 100 < 99
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: keepIdsInSync user organizations sync error rate above 1%
            description: "keepIdsInSync user organizations sync error rate has exceeded threshold over last 24 hours in {{ $labels.xgen_environment }}. Current success rate: {{ $value }} %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/Rrp1HTlSz?orgId=20'
        - alert: Update Salesforce Id Cache Job Not Running in PROD for 30 hours
          expr: time() - max(billing_salesforce_id_cache_update_last_successful_timestamp{xgen_environment="prod"} > 0) > 30*3600
          labels:
            severity: warning
          annotations:
            summary: Update Salesforce Id Cache Job is not running in PROD
            description: "Update Salesforce Id Cache Job is not running in PROD"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/d/w_yMXlInk/billing-salesforce-integration?orgId=20'
    - name: cost_explorer
      rules:
        - alert: Cron job costExplorerAggregateCreationJob was not triggered in PROD
          expr: sum by (cron_job_name) (increase (mms_core_cron_jobs_successful_execution_time_seconds_count{app="mms-cron-jobs",xgen_environment="prod",cron_job_name=~"costExplorerAggregateCreationJobJob", job_lock_acquired_result="JOB_ACQUIRED_LOCK"}[24h])) == 0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: "costExplorerAggregateCreationJob has not run in production in the last 24 hours"
            description: "The Cost Explorer Aggregate Creation Job has not run in production in the last 24 hours"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/DCF2O3VHz?orgId=20'
        - alert: Cron job costExplorerTokenDeletionJob job was not triggered in PROD
          expr: sum by (cron_job_name) (increase (mms_core_cron_jobs_successful_execution_time_seconds_count{app="mms-cron-jobs",xgen_environment="prod",cron_job_name=~"costExplorerTokenDeletionJobJob", job_lock_acquired_result="JOB_ACQUIRED_LOCK"}[24h])) == 0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: "costExplorerTokenDeletionJob has not run in production in the last 24 hours"
            description: "The Cost Explorer Token Deletion Job has not run in production in the last 24 hours"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/DCF2O3VHz?orgId=20'
        - alert: Cron job costExplorerAggregateDeletionJob job was not triggered in PROD
          expr: |
            (day_of_month() >= 15 and day_of_month() <= 17)
            and sum by (cron_job_name) (
              increase(
                mms_core_cron_jobs_successful_execution_time_seconds_count{
                  app="mms-cron-jobs",
                  xgen_environment="prod",
                  cron_job_name=~"costExplorerAggregateDeletionJobJob",
                  job_lock_acquired_result="JOB_ACQUIRED_LOCK"
                }[24h]
              )
            ) == 0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: "costExplorerAggregateDeletionJob has not run in production during its specified time window"
            description: "The Cost Explorer Aggregate deletion should run between the 15th and 17th of the month, but has not run as expected"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/DCF2O3VHz?orgId=20'
    - name: core_billing_performance
      rules:
        - alert: Billing Failure Rate Outside Threshold for past 24 hours
          expr: sum(rate(billing_revenue_generating_process_org_technical_failure_total{xgen_environment="prod"}[1d])) / (sum(rate(billing_revenue_generating_process_org_success_total{xgen_environment="prod"}[1d])) + sum(rate(billing_revenue_generating_process_org_technical_failure_total{xgen_environment="prod"}[1d]))) > 0.02
          for: 1h
          labels:
            severity: critical
          annotations:
            summary: Billing Failure Rate Outside Threshold for past 24 hours
            description: "The failure rate for billing organizations is higher has gone above threshold. Current value: {{ $value }}  %"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/heU7Z8gNk?orgId=20'
        - alert: Daily Billing Runtime Exceeded PROD
          expr: max(billing_daily_billing_realtime_runtime_seconds{xgen_environment="prod"} and rate(billing_daily_billing_realtime_runtime_seconds{xgen_environment="prod"}[12h]) > 0) > 9000
          for: 1h
          labels:
            severity: critical
          annotations:
            summary: Daily Billing Runtime Exceeded SLA in PROD
            description: "Daily billing did not complete within the given SLA window {{ $labels.xgen_environment }}"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/-VFgwJ4Hk?orgId=20'
        - alert: Daily Billing Runtime Exceeded in NON PROD
          expr: max(billing_daily_billing_realtime_runtime_seconds{xgen_environment=~"dev|qa"} and rate(billing_daily_billing_realtime_runtime_seconds{xgen_environment=~"dev|qa"}[12h]) > 0) > 9000
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: Daily Billing Runtime Exceeded SLA in NON PROD
            description: "Daily billing did not complete within the given SLA window {{ $labels.xgen_environment }}"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/-VFgwJ4Hk?orgId=20'
        - alert: Daily Billing Runtime Exceeded PROD GOV
          expr: max(billing_daily_billing_realtime_runtime_seconds{xgen_environment="prod-gov"} and rate(billing_daily_billing_realtime_runtime_seconds{xgen_environment="prod-gov"}[12h]) > 0) > 9000
          for: 1h
          labels:
            severity: critical
          annotations:
            summary: Daily Billing Runtime Exceeded SLA in PROD GOV
            description: "Daily billing did not complete within the given SLA window {{ $labels.xgen_environment }}"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/-VFgwJ4Hk?orgId=20'
        - alert: Billing Data Export Delayed PROD
          expr: max(billing_data_warehouse_start_delay_seconds{xgen_environment="prod"} and rate(billing_data_warehouse_start_delay_seconds{xgen_environment="prod"}[12h]) > 0) > 36000
          for: 1h
          labels:
            severity: critical
          annotations:
            summary: Billing Data Export Delay Exceeded SLA in PROD
            description: "Billing data export did not start in time {{ $labels.xgen_environment }}"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.helix.corp.mongodb.com/goto/JdozQMXNR?orgId=20'
        - alert: Revenue Alerting Framework Delayed PROD
          expr: max(billing_revenue_alert_complete_seconds{xgen_environment="prod"} and rate(billing_revenue_alert_complete_seconds{xgen_environment="prod"}[12h]) > 0) > 14400
          for: 1h
          labels:
            severity: critical
          annotations:
            summary: Billing Revenue Alerting Auditors Delay Exceeded SLA in PROD
            description: "Billing revenue alerting auditors did not complete in time {{ $labels.xgen_environment }}"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.helix.corp.mongodb.com/goto/JdozQMXNR?orgId=20'
    - name: LineItemMaterializedView
      rules:
        - alert: LineItemMaterializedView did not run or failed
          expr: (max(up{xgen_environment="prod",xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}) by (xgen_environment) == bool 1) and absent(rate(billing_line_item_summary_materialized_view_job_duration_seconds_count{xgen_environment=~"(prod)", xgen_region=~"(us-east-1)", jobType="SCHEDULED", jobStatus="SUCCEEDED", xgen_kube_cluster="kube-1-us-east-1-aws-cloud"}[26h]))
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: LineItemMaterializedView did not run or failed
            description: "LineItemMaterializedView did not run or failed today"
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Pager+Duty+Alert+Runbook'
            grafana_url: 'https://grafana.corp.mongodb.com/goto/OBX_65OHk?orgId=20'
    - name: Salesforce OAuth Authentication Failure Non Prod
      rules:
        - alert: Salesforce OAuth Authentication Failure Non Prod
          expr: sum(increase(mms_salesforce_oauth_conn_creation_total{xgen_environment=~"(dev|qa|staging)", status="failure"}[5m])) by (xgen_environment) > 3
          keep_firing_for: 30m
          labels:
            severity: warning
          annotations:
            summary: Failed to create OAuth connection to access Salesforce Non Prod
            description: "Failed to create OAuth connection to access Salesforce in {{ $labels.xgen_environment }}"
            runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/285097379/Billing+Alerts+Runbook#BillingAlertsRunbook-UnabletoLogintoSalesforce'
    - name: Salesforce OAuth Authentication Failure Prod
      rules:
        - alert: Salesforce OAuth Authentication Failure Prod
          expr: sum(increase(mms_salesforce_oauth_conn_creation_total{xgen_environment=~"(prod|prod-gov)", status="failure"}[5m])) by (xgen_environment) > 3
          keep_firing_for: 30m
          labels:
            severity: critical
          annotations:
            summary: Failed to create OAuth connection to access Salesforce in Prod
            description: "Failed to create OAuth connection to access Salesforce in {{ $labels.xgen_environment }}"
            runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/285097379/Billing+Alerts+Runbook#BillingAlertsRunbook-UnabletoLogintoSalesforce'
    - name: Salesforce SOAP Authentication Failure Non Prod
      rules:
        - alert: Salesforce SOAP Authentication Failure Non Prod
          expr: sum(increase(mms_salesforce_soap_conn_creation_total{xgen_environment=~"(dev|qa|staging)", status="failure"}[5m])) by (xgen_environment) > 3
          keep_firing_for: 30m
          labels:
            severity: warning
          annotations:
            summary: Failed to create SOAP connection to access Salesforce Non Prod
            description: "Failed to create SOAP connection to access Salesforce in {{ $labels.xgen_environment }}"
            runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/285097379/Billing+Alerts+Runbook#BillingAlertsRunbook-UnabletoLogintoSalesforce'
    - name: Salesforce SOAP Authentication Failure Prod
      rules:
        - alert: Salesforce SOAP Authentication Failure Prod
          expr: sum(increase(mms_salesforce_soap_conn_creation_total{xgen_environment=~"(prod|prod-gov)", status="failure"}[5m])) by (xgen_environment) > 3
          keep_firing_for: 30m
          labels:
            severity: critical
          annotations:
            summary: Failed to create SOAP connection to access Salesforce in Prod
            description: "Failed to create SOAP connection to access Salesforce in {{ $labels.xgen_environment }}"
            runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/285097379/Billing+Alerts+Runbook#BillingAlertsRunbook-UnabletoLogintoSalesforce'
