---
# yamllint disable rule:quoted-strings
pagerdutyService:
  enabled: true
  name: mms-automation-non-prod
  fullnameOverride: mms-automation-non-prod
  escalationPolicyName: mms-automation-non-prod
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: mms-agent_automation_median_mtmproxy_memory_usage_non_prod
      rules:
        - alert: Median memory used across the atlas proxies is large
          expr: quantile(0.5, go_memstats_sys_bytes{type="atlas_proxy",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[30s]) by (xgen_environment) > 2e+8
          annotations:
            summary: median memory used across the atlas proxies is larger than 200MB
            description: median memory used across the atlas proxies is larger than 200MB
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
          for: 30m
          labels:
            severity: warning
    - name: mms-agent_automation_95_pct_mtmproxy_memory_usage_non_prod
      rules:
        - alert: 95% percentile of memory used across the atlas proxies is large
          expr: quantile(0.95, go_memstats_sys_bytes{type="atlas_proxy",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[30s]) by (xgen_environment) > 1e+9
          annotations:
            summary: 95% percentile of memory used across the atlas proxies is larger than 1GB
            description: 95% percentile of memory used across the atlas proxies is larger than 1GB
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
          for: 30m
          labels:
            severity: warning
    - name: mms-agent_automation_max_mtmproxy_memory_usage_non_prod
      rules:
        - alert: Max memory used across the atlas proxies is large
          expr: max(go_memstats_sys_bytes{type="atlas_proxy",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[30s]) by (xgen_environment) > 1.5e+9
          annotations:
            summary: max memory used across the atlas proxies is larger than 1.5GB
            description: max memory used across the atlas proxies is larger than 1.5GB
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
          for: 30m
          labels:
            severity: warning
    - name: mms-agent_automation_mtmproxy_throttling_events_non_prod
      rules:
        - alert: large percentage of throttled operations across mtms
          expr: (sum(rate(mtmproxy_throttling_events_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",subtype!="network_bandwidth"}[5m])) by (xgen_environment)) / (sum(rate(mtmproxy_processing_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) by (xgen_environment)) > 0.07
          annotations:
            summary: Over 7% of request to mtms have been throttled in the past 5 minutes
            description: Over 7% of request to mtms have been throttled in the past 5 minutes
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
          for: 30m
          labels:
            severity: warning
    - name: mms-agent_automation_mtmproxy_health_events
      rules:
        - alert: Proxy restarts more than 100 over 1h
          expr: sum(increase(mms_nds_proxy_audit_dao_proxy_restarted_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1h])) > 100
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Proxy restarts more than 100 over 15m
            description: Observe NDS Proxy Tenant Errors in grafana
            grafana_url: 'https://grafana.corp.mongodb.com/d/ha9CmmK7z/qa-release-check-board?orgId=20&var-datasource=thanos&var-env=dev'
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/NDS+Proxy+Tenant+Failure+Debugging+Guide'
        - alert: Proxy panicked errors more than 15 over 15m
          expr: sum(increase(mms_nds_proxy_svc_proxy_panicked_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[15m])) > 15
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Proxy panicked errors more than 15 over 15m
            description: Observe NDS Proxy Tenant Errors in grafana
            grafana_url: 'https://grafana.corp.mongodb.com/d/ha9CmmK7z/qa-release-check-board?orgId=20&var-datasource=thanos&var-env=dev'
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/NDS+Proxy+Tenant+Failure+Debugging+Guide'
    - name: mms-agent_automation_free_process_proxy_config_per_cluster_move_long_running_non_prod
      rules:
        - alert: FreeProcessProxyConfigPerClusterMove Long Running Duration Alert
          expr: histogram_quantile(0.9, sum(rate(mms_planner_move_overall_duration_seconds_bucket{move_name=~"FreeProcessProxyConfigPerClusterMove", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) by (le, xgen_environment)) > 3600
          annotations:
            summary: FreeProcessProxyConfigPerClusterMove Duration 90% percentile is over an hour
            description: FreeProcessProxyConfigPerClusterMove Duration 90% percentile is over an hour
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
          for: 10m
          labels:
            severity: warning
    - name: mms-agent_automation_log_error_increase_sustained_non_prod
      rules:
        - alert: abnormal z-score of agent log errors
          expr: ((mms_agent_logs_inserted_total_error_non_prod::increase1m - mms_agent_logs_inserted_total_error_non_prod::increase1m::avg_over_time_5d) / mms_agent_logs_inserted_total_error_non_prod::increase1m:stddev_over_time_5d) > 10
          for: 3m
          labels:
            severity: warning
          annotations:
            summary: abnormal z-score of agent log errors (non-prod)
            description: abnormal z-score of agent log errors (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: sustained increase of agent log errors
          expr: sum(increase(mms_agent_logs_inserted_total{xgen_app=~"mms-agent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",level=~"(error|ERROR)",module=~"automation"}[10m])) by (xgen_environment,module) - sum(increase(mms_agent_logs_inserted_total{xgen_app=~"mms-agent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",level=~"(error|ERROR)",module=~"automation"}[10m] offset 10m)) by (xgen_environment,module) > 5000.0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: sustained increase of agent log errors in the automation module (non-prod)
            description: sustained increase of agent log errors in the automation module (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-agent_automation_log_warn_increase_sustained_non_prod
      rules:
        - alert: abnormal z-score of agent log warnings
          expr: ((mms_agent_logs_inserted_total_warn_non_prod::increase1m - mms_agent_logs_inserted_total_warn_non_prod::increase1m::avg_over_time_5d) / mms_agent_logs_inserted_total_warn_non_prod::increase1m:stddev_over_time_5d) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: abnormal z-score of agent log warnings (non-prod)
            description: abnormal z-score of agent log warnings (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: sustained increase of agent log warnings
          expr: sum(increase(mms_agent_logs_inserted_total{xgen_app=~"mms-agent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",level=~"(warn|WARN)",module=~"automation"}[10m])) by (xgen_environment,module) - sum(increase(mms_agent_logs_inserted_total{xgen_app=~"mms-agent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",level=~"(warn|WARN)",module=~"automation"}[10m] offset 10m)) by (xgen_environment,module) > 50000.0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: sustained increase of agent log warnings in the automation module (non-prod)
            description: sustained increase of agent log warnings in the automation module (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-agent_high_endpoint_latency_non_prod
      rules:
        - alert: mms-agent /agents/api/automation/conf/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="GET", path=~"/agents/api/automation/conf/v1/.*"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="GET", path=~"/agents/api/automation/conf/v1/.*"}[1m])) by (xgen_environment) - mms_automation_non_prod::automation_conf_v1_non_prod_avg_over_time::no_subquery) / mms_automation_non_prod::automation_conf_v1_non_prod_stddev::no_subquery > 1.0
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for /agents/api/automation/conf/v1/{groupId} endpoint (non-prod)
            description: max z-score breached for /agents/api/automation/conf/v1/{groupId} endpoint (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-agent /agents/api/automation/jobs/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="GET", path=~"/agents/api/automation/jobs/v1/.*"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="GET", path=~"/agents/api/automation/jobs/v1/.*"}[1m])) by (xgen_environment) - mms_automation_non_prod::agents_api_automation_jobs_v1_non_prod_avg_over_time::no_subquery) / mms_automation_non_prod::agents_api_automation_jobs_v1_non_prod_avg_stddev::no_subquery > 4.5
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for GET /agents/api/automation/jobs/v1/{groupId} endpoint (non-prod)
            description: max z-score breached for GET /agents/api/automation/jobs/v1/{groupId} endpoint (non-prod)
        - alert: mms-agent /agents/api/automation/settings/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="GET", path=~"/agents/api/automation/settings/v1/.*"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="GET", path=~"/agents/api/automation/settings/v1/.*"}[1m])) by (xgen_environment) - mms_automation_non_prod::agents_api_automation_settings_v1_non_prod_avg_over_time::no_subquery) / mms_automation_non_prod::agents_api_automation_settings_v1_non_prod_avg_stddev::no_subquery > 3.0
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for GET /agents/api/automation/settings/v1/{groupId} endpoint (non-prod)
            description: max z-score breached for GET /agents/api/automation/settings/v1/{groupId} endpoint (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-agent /agentlog/v3/catch/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="POST", path=~"/agentlog/v3/catch/.*"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="POST", path=~"/agentlog/v3/catch/.*"}[1m])) by (xgen_environment) - mms_automation_non_prod::agentlog_v3_catch_non_prod_avg_over_time::no_subquery) / mms_automation_non_prod::agentlog_v3_catch_non_prod_avg_stddev::no_subquery > 3.0
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for POST /agentlog/v3/catch/{groupId} endpoint (non-prod)
            description: max z-score breached for POST /agentlog/v3/catch/{groupId} endpoint (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-agent /agents/api/automation/fullstatus/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="POST", path=~"/agents/api/automation/fullstatus/v1/.*"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="POST", path=~"/agents/api/automation/fullstatus/v1/.*"}[1m])) by (xgen_environment) - mms_automation_non_prod::agents_api_automation_fullstatus_v1_non_prod_avg_over_time::no_subquery) / mms_automation_non_prod::agents_api_automation_fullstatus_v1_non_prod_avg_stddev::no_subquery > 4
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for POST /agents/api/automation/fullstatus/v1/{groupId} endpoint (non-prod)
            description: max z-score breached for POST /agents/api/automation/fullstatus/v1/{groupId} endpoint (non-prod)
        - alert: mms-agent /agents/api/automation/log/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="POST", path=~"/agents/api/automation/log/v1/.*"}[1m])) by (xgen_environment) / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", method="POST", path=~"/agents/api/automation/log/v1/.*"}[1m])) by (xgen_environment) - mms_automation_non_prod::agents_api_automation_log_v1_non_prod_avg_over_time::no_subquery) / mms_automation_non_prod::agents_api_automation_log_v1_non_prod_avg_stddev::no_subquery > 2.5
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for POST /agents/api/automation/log/v1/{groupId} endpoint (non-prod)
            description: max z-score breached for POST /agents/api/automation/log/v1/{groupId} endpoint (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-ui-or-agent_5xx_response_code_increase_non_prod
      rules:
        - alert: 'MMS Automation Endpoints: (mms-agent) High proportion of HTTP 5XX statuses'
          expr: sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",xgen_app=~"(mms-agent|mms-ui)",path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*|.*/nds/proxy/serverless.*|.*/nds/proxy/conf.*",status="5xx"}[60s])) by (xgen_app) /  sum(rate(java_jersey_http_duration_seconds_count{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",xgen_app=~"(mms-agent|mms-ui)",path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*|.*/nds/proxy/serverless.*|.*/nds/proxy/conf.*"}[60s])) by (xgen_app) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Too many 5xx responses returned by Automation Endpoints in {{ $labels.xgen_environment }}
            description: A large proportion of HTTP requests (>10%) are resulting in 5xx response codes over last 5 mins
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-agent abnormal z-score of 5xx responses for automation/agent endpoints
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment) - avg_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m])) / stddev_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m]) > 2
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-agent abnormal z-score of 5xx responses for automation/agent endpoints in {{ $labels.xgen_environment }}
            description: mms-agent abnormal z-score of 5xx responses for automation/agent endpoints
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-agent abnormal z-score of 5xx responses for NDS/proxy endpoints
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment) - avg_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m])) / stddev_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m]) > 2
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-agent abnormal z-score of 5xx responses for automation/agent endpoints in {{ $labels.xgen_environment }}
            description: mms-agent abnormal z-score of 5xx responses for automation/agent endpoints
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-ui abnormal z-score of 5xx responses for automation/agent endpoints
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment) - avg_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m])) / stddev_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m]) > 2
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-ui abnormal z-score of 5xx responses for automation/agent endpoints in {{ $labels.xgen_environment }}
            description: mms-ui abnormal z-score of 5xx responses for automation/agent endpoints
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-ui abnormal z-score of 5xx responses for NDS/proxy endpoints
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment) - avg_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m])) / stddev_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m]) > 2
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-ui abnormal z-score of 5xx responses for automation/agent endpoints in {{ $labels.xgen_environment }}
            description: mms-ui abnormal z-score of 5xx responses for automation/agent endpoints
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-agent_conf_calls_3xx_down_and_2xx_up_non_prod
      rules:
        - alert: conf calls sustained increase of 2xx and decrease of 3xx requests non-prod
          expr: (sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="2xx"}[20s])) - avg_over_time(sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="2xx"}[20s]))[5d:20s])) /stddev_over_time(sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="2xx"}[20s]))[5d:20s]) > 2.0 and (sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="3xx"}[20s])) - avg_over_time(sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="3xx"}[20s]))[5d:20s])) /stddev_over_time(sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="3xx"}[20s]))[5d:20s]) < -2.0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: automation conf calls sustained increase of 2xx coincided with decrease of 3xx response status codes (non-prod)
            description: automation conf calls sustained increase of 2xx coincided with decrease of 3xx response status codes (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-rtagent_jetty_thread_pool_queues_non_prod
      rules:
        - alert: mms-rtagent jetty request thread pool queue is running at high capacity (non-prod)
          expr: (avg(jetty_queued_thread_pool_jobs{xgen_app=~"(mms-rtagent)",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}) - avg_over_time(avg(jetty_queued_thread_pool_jobs{xgen_app=~"(mms-rtagent)",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"})[7d:1m]))/ stddev_over_time(avg(jetty_queued_thread_pool_jobs{xgen_app=~"(mms-rtagent)",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"})[7d:1m]) > 2.0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent jetty thread pool queues are at at high capacity (non-prod)
            description: z score for jetty request thread pool capacity utilization is above normal for last 20m (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent jetty async waiting requests breached z-score (non-prod)
          expr: (avg(jetty_async_requests_waiting{xgen_app=~"(mms-rtagent)",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"} ) by (xgen_environment) - avg_over_time(avg(jetty_async_requests_waiting{xgen_app=~"(mms-rtagent)",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"} ) by (xgen_environment) [7d:1m]))/ stddev_over_time(avg(jetty_async_requests_waiting{xgen_app=~"(mms-rtagent)",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}) by (xgen_environment) [7d:1m]) > 5.0
          for: 25m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent jetty async waiting requests breached z-score (non-prod)
            description: z score for jetty async waiting requests is above normal for last 20m (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms_automation_changes_watcher_invalidated_total_sustained_increased_non_prod
      rules:
        - alert: sustained increase in real-time agent errors (non-prod)
          expr: sum(increase(mms_automation_changes_watcher_invalidated_total{xgen_app=~"mms-rtagent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) by (xgen_environment) - sum(increase(mms_automation_changes_watcher_invalidated_total{xgen_app=~"mms-rtagent",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m] offset 5m)) by (xgen_environment) > 25
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: sustained increase in real-time agent errors (non-prod)
            description: sustained increase in real-time agent errors via mms_automation_changes_watcher_invalidated_total for last 10m (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-agent_automation_logdb_insert_latency_non_prod
      rules:
        - alert: latency in inserting in automation log db crosses threshold (non-prod)
          expr: max(mms_automation_agent_logs_insert_duration_seconds{app=~"(mms-agent|mms-ui)",xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}) by (xgen_environment,app)  > 5.0
          for: 3m
          labels:
            severity: warning
          annotations:
            summary: latency in inserting in automation log db in non-prod crosses threshold (non-prod)
            description: latency in inserting in automation log db in non-prod crosses threshold (non-prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
