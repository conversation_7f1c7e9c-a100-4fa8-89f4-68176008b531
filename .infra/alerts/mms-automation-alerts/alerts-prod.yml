---
# yamllint disable rule:quoted-strings
pagerdutyService:
  enabled: true
  name: mms-automation-prod
  fullnameOverride: mms-automation-prod
  # Since warning alerts only email by default, we can use the High Urgency policy for all alerts.
  escalationPolicyName: Cloud Automation Alerts Triaging Prod and Internal - High Urgency
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
vmRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: mms-agent_automation_median_mtmproxy_memory_usage_prod
      rules:
        - alert: (Prod) Median memory used across the atlas proxies is large
          expr: quantile(0.5, go_memstats_sys_bytes{type="atlas_proxy",xgen_environment=~"(prod)"}[30s]) by (xgen_environment) > 5e+8
          annotations:
            summary: median memory used across the atlas proxies is larger than 500MB
            description: median memory used across the atlas proxies is larger than 500MB
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
          for: 30m
          labels:
            severity: warning

    - name: mms-agent_automation_95_pct_mtmproxy_memory_usage_prod
      rules:
        - alert: (Prod) 95% percentile of memory used across the atlas proxies is large
          expr: quantile(0.95, go_memstats_sys_bytes{type="atlas_proxy",xgen_environment=~"(prod)"}[30s]) by (xgen_environment) > 2.5e+9
          annotations:
            summary: 95% percentile of memory used across the atlas proxies is larger than 2.5GB
            description: 95% percentile of memory used across the atlas proxies is larger than 2.5GB
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
          for: 30m
          labels:
            severity: warning

    - name: mms-agent_automation_max_mtmproxy_memory_usage_prod
      rules:
        - alert: (Prod) Max memory used across the atlas proxies is large
          expr: max(go_memstats_sys_bytes{type="atlas_proxy",xgen_environment=~"(prod)"}[30s]) by (xgen_environment) > 1.5e+10
          annotations:
            summary: max memory used across the atlas proxies is larger than 15GB
            description: max memory used across the atlas proxies is larger than 15GB
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
          for: 30m
          labels:
            severity: warning
    - name: endpoint utilization
      rules:
        - alert: max endpoint utilization breached - low severity - mms-agent
          expr: ((max(mms_circuitbreaker_semaphore_active_permits{xgen_app=~"mms-agent", xgen_environment="prod", semaphore_group="endpointCircuitBreaker"}) by (name))/200.0)*100.0 > 25.0
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: max endpoint utilization breached - low severity - mms-agent
            description: max endpoint utilization breached - low severity - mms-agent
        - alert: max endpoint utilization breached - high severity - mms-agent
          expr: ((max(mms_circuitbreaker_semaphore_active_permits{xgen_app=~"mms-agent", xgen_environment="prod", semaphore_group="endpointCircuitBreaker"}) by (name))/200.0)*100.0 > 35.0
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: max endpoint utilization breached - high severity - mms-agent
            description: max endpoint utilization breached - high severity - mms-agent
        - alert: discovered new throttled endpoints - low severity - mms-agent
          expr: ((max(mms_circuitbreaker_semaphore_active_permits{xgen_app=~"mms-agent", xgen_environment="prod", semaphore_group="endpointCircuitBreaker", name!~"endPointCircuitBreaker-/agents/api/automation/conf/v1/{groupId}-GET", name!~"endPointCircuitBreaker-/agents/api/automation/log/v1/{groupId}-POST", name!~"endPointCircuitBreaker-/conf/v5/{groupId}-GET"}) by (name))/200.0)*100.0 > 35
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: discovered new throttled endpoints - low severity - mms-agent
            description: discovered new throttled endpoints - low severity - mms-agent

    - name: mms-agent_automation_mtmproxy_health_events_prod
      rules:
        - alert: (Prod) Proxy restarts more than 100 over 1m
          expr: sum(increase(mms_nds_proxy_audit_dao_proxy_restarted_total{xgen_environment="prod"}[1m])) > 100
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Proxy restarts more than 100 over 1m
            description: Observe NDS Proxy Tenant Errors in grafana.
            grafana_url: 'https://grafana.corp.mongodb.com/d/ha9CmmK7z/qa-release-check-board?orgId=20&var-datasource=default&var-env=prod'
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/NDS+Proxy+Tenant+Failure+Debugging+Guide'
        - alert: (Prod) Proxy panicked errors more than 10 over 15m
          expr: sum(increase(mms_nds_proxy_svc_proxy_panicked_total{xgen_environment="prod"}[15m])) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Proxy panicked errors more than 10 over 15m
            description: Observe NDS Proxy Tenant Errors in grafana.
            grafana_url: 'https://grafana.corp.mongodb.com/d/ha9CmmK7z/qa-release-check-board?orgId=20&var-datasource=default&var-env=prod'
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/NDS+Proxy+Tenant+Failure+Debugging+Guide'
    - name: mms-agent_automation_free_process_proxy_config_per_cluster_move_long_running_prod
      rules:
        - alert: FreeProcessProxyConfigPerClusterMove Long Running Duration Alert
          expr: histogram_quantile(0.9, sum(rate(mms_planner_move_overall_duration_seconds_bucket{move_name=~"FreeProcessProxyConfigPerClusterMove", xgen_environment=~"prod"}[1m])) by (le, xgen_environment)) > 3600
          annotations:
            summary: FreeProcessProxyConfigPerClusterMove Duration 90% percentile is over an hour
            description: FreeProcessProxyConfigPerClusterMove Duration 90% percentile is over an hour
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
          for: 10m
          labels:
            severity: warning
    - name: mms-agent_automation_log_error_increase_sustained_prod
      rules:
        - alert: abnormal z-score of agent log errors
          expr: ((mms_agent_logs_inserted_total_error_prod::increase1m - mms_agent_logs_inserted_total_error_prod::increase1m::avg_over_time_5d) / mms_agent_logs_inserted_total_error_prod::increase1m:stddev_over_time_5d) > 10
          for: 3m
          labels:
            severity: warning
          annotations:
            summary: abnormal z-score of agent log errors (prod)
            description: abnormal z-score of agent log errors (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: sustained increase of agent log errors
          expr: sum(increase(mms_agent_logs_inserted_total{xgen_app=~"mms-agent",xgen_environment=~"(prod)",level=~"(error|ERROR)",module=~"automation"}[10m])) by (xgen_environment,module) - sum(increase(mms_agent_logs_inserted_total{xgen_app=~"mms-agent",xgen_environment=~"(prod)",level=~"(error|ERROR)",module=~"automation"}[10m] offset 10m)) by (xgen_environment,module) > 5000.0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: sustained increase of agent log errors in the automation module (prod)
            description: sustained increase of agent log errors in the automation module (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-agent_automation_log_warn_increase_sustained_prod
      rules:
        - alert: abnormal z-score of agent log warnings
          expr: ((mms_agent_logs_inserted_total_warn_prod::increase1m - mms_agent_logs_inserted_total_warn_prod::increase1m::avg_over_time_5d) / mms_agent_logs_inserted_total_warn_prod::increase1m:stddev_over_time_5d) > 10
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: abnormal z-score of agent log warnings (prod)
            description: abnormal z-score of agent log warnings (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: sustained increase of agent log warnings
          expr: sum(increase(mms_agent_logs_inserted_total{xgen_app=~"mms-agent",xgen_environment=~"(prod)",level=~"(warn|WARN)",module=~"automation"}[10m])) by (xgen_environment,module) - sum(increase(mms_agent_logs_inserted_total{xgen_app=~"mms-agent",xgen_environment=~"(prod)",level=~"(warn|WARN)",module=~"automation"}[10m] offset 10m)) by (xgen_environment,module) > 50000.0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: sustained increase of agent log warnings in the automation module (prod)
            description: sustained increase of agent log warnings in the automation module (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-agent_high_endpoint_latency_prod
      rules:
        - alert: mms-agent /agents/api/automation/conf/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="GET", path=~"/agents/api/automation/conf/v1/.*"}[1m]))  / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="GET", path=~"/agents/api/automation/conf/v1/.*"}[1m])) - mms_automation_prod::automation_conf_v1_prod_avg_over_time::no_subquery) / mms_automation_prod::automation_conf_v1_prod_stddev::no_subquery > 1.0
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for /agents/api/automation/conf/v1/{groupId} endpoint (prod)
            description: max z-score breached for /agents/api/automation/conf/v1/{groupId} endpoint (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-agent /agents/api/automation/jobs/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="GET", path=~"/agents/api/automation/jobs/v1/.*"}[1m]))  / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="GET", path=~"/agents/api/automation/jobs/v1/.*"}[1m])) - mms_automation_prod::agents_api_automation_jobs_v1_prod_avg_over_time::no_subquery) / mms_automation_prod::agents_api_automation_jobs_v1_prod_avg_stddev::no_subquery > 4.5
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for GET /agents/api/automation/jobs/v1/{groupId} endpoint (prod)
            description: max z-score breached for GET /agents/api/automation/jobs/v1/{groupId} endpoint (prod)
        - alert: mms-agent /agents/api/automation/settings/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="GET", path=~"/agents/api/automation/settings/v1/.*"}[1m]))  / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="GET", path=~"/agents/api/automation/settings/v1/.*"}[1m])) - mms_automation_prod::agents_api_automation_settings_v1_prod_avg_over_time::no_subquery) / mms_automation_prod::agents_api_automation_settings_v1_prod_avg_stddev::no_subquery > 3.0
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for GET /agents/api/automation/settings/v1/{groupId} endpoint (prod)
            description: max z-score breached for GET /agents/api/automation/settings/v1/{groupId} endpoint (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-agent /agentlog/v3/catch/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="POST", path=~"/agentlog/v3/catch/.*"}[1m]))  / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="POST", path=~"/agentlog/v3/catch/.*"}[1m])) - mms_automation_prod::agentlog_v3_catch_prod_avg_over_time::no_subquery) / mms_automation_prod::agentlog_v3_catch_prod_avg_stddev::no_subquery > 3.0
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for POST /agentlog/v3/catch/{groupId} endpoint (prod)
            description: max z-score breached for POST /agentlog/v3/catch/{groupId} endpoint (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-agent /agents/api/automation/fullstatus/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="POST", path=~"/agents/api/automation/fullstatus/v1/.*"}[1m]))  / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="POST", path=~"/agents/api/automation/fullstatus/v1/.*"}[1m])) - mms_automation_prod::agents_api_automation_fullstatus_v1_prod_avg_over_time::no_subquery) / mms_automation_prod::agents_api_automation_fullstatus_v1_prod_avg_stddev::no_subquery > 4
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for POST /agents/api/automation/fullstatus/v1/{groupId} endpoint (prod)
            description: max z-score breached for POST /agents/api/automation/fullstatus/v1/{groupId} endpoint (prod)
        - alert: mms-agent /agents/api/automation/log/v1/{groupId} high latency
          expr: (sum(increase(java_jersey_http_duration_seconds_sum{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="POST", path=~"/agents/api/automation/log/v1/.*"}[1m]))  / sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", method="POST", path=~"/agents/api/automation/log/v1/.*"}[1m])) - mms_automation_prod::agents_api_automation_log_v1_prod_avg_over_time::no_subquery) / mms_automation_prod::agents_api_automation_log_v1_prod_avg_stddev::no_subquery > 4.5
          for: 15m
          labels:
            severity: warning
          annotations:
            summary: max z-score breached for POST /agents/api/automation/log/v1/{groupId} endpoint (prod)
            description: max z-score breached for POST /agents/api/automation/log/v1/{groupId} endpoint (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-agent_ERROR_or_WARN_sustained
      rules:
        - alert: mms-agent pool ERROR logs sustained increase over 30 mins in prod
          expr: sum(increase(logback_appender_total{xgen_app=~"mms-agent", xgen_environment=~"prod", level=~"error"}[10m]))  by (xgen_environment,xgen_app,level) - sum(increase(logback_appender_total{xgen_app=~"mms-agent", xgen_environment=~"prod", level=~"error"}[10m] offset 10m))  by (xgen_environment,xgen_app,level) > 50.0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: mms-agent pool ERROR logs sustained increase over 30 mins in prod
            description: mms-agent pool ERROR logs sustained increase over 30 mins in prod
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-agent pool WARN logs sustained increase over 30 mins in prod
          expr: sum(increase(logback_appender_total{xgen_app=~"mms-agent", xgen_environment=~"prod", level=~"warn"}[10m]))  by (xgen_environment,xgen_app,level) - sum(increase(logback_appender_total{xgen_app=~"mms-agent", xgen_environment=~"prod", level=~"warn"}[10m] offset 10m))  by (xgen_environment,xgen_app,level) > 5000.0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: mms-agent pool WARN logs sustained increase over 30 mins in prod
            description: mms-agent pool WARN logs sustained increase over 30 mins in prod
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-ui-or-agent_5xx_response_code_increase_prod
      rules:
        - alert: 'MMS Automation Endpoints: (mms-agent) High proportion of HTTP 5XX statuses'
          expr: sum(rate(java_jersey_http_duration_seconds_count{xgen_environment=~"(prod)",xgen_app=~"(mms-agent|mms-ui)",path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*|.*/nds/proxy/serverless.*|.*/nds/proxy/conf.*",status="5xx"}[60s])) by (xgen_app) /  sum(rate(java_jersey_http_duration_seconds_count{xgen_environment=~"(prod)",xgen_app=~"(mms-agent|mms-ui)",path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*|.*/nds/proxy/serverless.*|.*/nds/proxy/conf.*"}[60s])) by (xgen_app) > 0.1
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: Too many 5xx responses returned by Automation Endpoints in {{ $labels.xgen_environment }}
            description: A large proportion of HTTP requests (>10%) are resulting in 5xx response codes over last 5 mins
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-agent abnormal z-score of 5xx responses for automation/agent endpoints
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment) - avg_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m])) / stddev_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m]) > 2
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-agent abnormal z-score of 5xx responses for automation/agent endpoints
            description: mms-agent abnormal z-score of 5xx responses for automation/agent endpoints
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-agent abnormal z-score of 5xx responses for NDS/proxy endpoints
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment) - avg_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m])) / stddev_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent", xgen_environment=~"(prod)", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m]) > 2
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-agent abnormal z-score of 5xx responses for NDS/proxy endpoints
            description: mms-agent abnormal z-score of 5xx responses for NDS/proxy endpoints
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-ui abnormal z-score of 5xx responses for automation/agent endpoints
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment=~"(prod)", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment) - avg_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment=~"(prod)", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m])) / stddev_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment=~"(prod)", path=~"/agents/api/automation/conf/v1/.*|/agents/api/automation/jobs/v1/.*|/agents/api/automation/settings/v1/.*|/agentlog/v3/catch/.*|/agents/api/automation/fullstatus/v1/.*|/agents/api/automation/log/v1/.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m]) > 2
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-ui abnormal z-score of 5xx responses for automation/agent endpoints
            description: mms-ui abnormal z-score of 5xx responses for automation/agent endpoints
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-ui abnormal z-score of 5xx responses for NDS/proxy endpoints
          expr: (sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment=~"(prod)", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment) - avg_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment=~"(prod)", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m])) / stddev_over_time(sum(increase(java_jersey_http_duration_seconds_count{xgen_app=~"mms-ui", xgen_environment=~"(prod)", path=~".*/nds/proxy/serverless.*|.*/nds/proxy/conf.*", status="5xx"}[1m])) by (xgen_environment)[5d:1m]) > 2
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-ui abnormal z-score of 5xx responses for NDS/proxy endpoints
            description: mms-ui abnormal z-score of 5xx responses for NDS/proxy endpoints
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-agent_conf_calls_3xx_down_and_2xx_up_prod
      rules:
        - alert: conf calls sustained increase of 2xx and decrease of 3xx requests prod
          expr: (sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"(prod)",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="2xx"}[20s])) - avg_over_time(sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"(prod)",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="2xx"}[20s]))[5d:20s])) /stddev_over_time(sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"(prod)",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="2xx"}[20s]))[5d:20s]) > 2.0 and (sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"(prod)",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="3xx"}[20s])) - avg_over_time(sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"(prod)",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="3xx"}[20s]))[5d:20s])) /stddev_over_time(sum(rate(java_jersey_http_duration_seconds_count{xgen_app=~"mms-agent",xgen_environment=~"(prod)",path=~"/agents/api/automation/conf/v1/.*",method="GET", status="3xx"}[20s]))[5d:20s]) < -2.0
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: automation conf calls sustained increase of 2xx coincided with decrease of 3xx response status codes (prod)
            description: automation conf calls sustained increase of 2xx coincided with decrease of 3xx response status codes (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-rtagent_jetty_thread_pool_queues_prod
      rules:
        - alert: mms-rtagent jetty request thread pool queue is running at high capacity (prod)
          expr: (avg(jetty_queued_thread_pool_jobs{xgen_app=~"(mms-rtagent)",xgen_environment=~"(prod)"}) - avg_over_time(avg(jetty_queued_thread_pool_jobs{xgen_app=~"(mms-rtagent)",xgen_environment=~"(prod)"})[7d:1m]))/ stddev_over_time(avg(jetty_queued_thread_pool_jobs{xgen_app=~"(mms-rtagent)",xgen_environment=~"(prod)"})[7d:1m]) > 2.0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent jetty thread pool queues are at at high capacity (prod)
            description: z score for jetty request thread pool capacity utilization is above normal for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent jetty async waiting requests breached z-score (prod)
          expr: (avg(jetty_async_requests_waiting{xgen_app=~"(mms-rtagent)",xgen_environment=~"(prod)"}) - avg_over_time(avg(jetty_async_requests_waiting{xgen_app=~"(mms-rtagent)",xgen_environment=~"(prod)"})[7d:1m]))/ stddev_over_time(avg(jetty_async_requests_waiting{xgen_app=~"(mms-rtagent)",xgen_environment=~"(prod)"})[7d:1m]) > 2.0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent jetty async waiting requests breached z-score (prod)
            description: z score for jetty async waiting requests is above normal for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms_automation_changes_watcher_invalidated_total_sustained_increased_prod
      rules:
        - alert: sustained increase in real-time agent errors (prod)
          expr: sum(increase(mms_automation_changes_watcher_invalidated_total{xgen_app=~"mms-rtagent",xgen_environment=~"(prod)"}[5m])) by (xgen_environment) - sum(increase(mms_automation_changes_watcher_invalidated_total{xgen_app=~"mms-rtagent",xgen_environment=~"(prod)"}[5m] offset 5m)) by (xgen_environment) > 25
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: sustained increase in real-time agent errors (prod)
            description: sustained increase in real-time agent errors via mms_automation_changes_watcher_invalidated_total for last 10m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-agent_prod_automation_logdb_insert_latency_prod
      rules:
        - alert: latency in inserting in automation log db crosses threshold (prod)
          expr: max(mms_automation_agent_logs_insert_duration_seconds{app=~"(mms-agent|mms-ui)",xgen_environment=~"(prod)"}) by (xgen_environment,app)  > 5.0
          for: 3m
          labels:
            severity: warning
          annotations:
            summary: latency in inserting in automation log db in prod crosses threshold (prod)
            description: latency in inserting in automation log db in prod crosses threshold (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-rtagent_envoy_upstream_cluster_prod
      rules:
        - alert: mms-rtagent envoy connection failure rate is high (prod)
          expr: rate(envoy_cluster_upstream_cx_connect_fail{envoy_cluster_name=~"kube_mms-rtagent.*_prod_.*"}[1m]) > 0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent upstream_cx_connect_fail nonzero rate (prod)
            description: envoy connection failure rate for mms-rtagent above zero for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent envoy connection timeout rate is high (prod)
          expr: rate(envoy_cluster_upstream_cx_connect_timeout{envoy_cluster_name=~"kube_mms-rtagent.*_prod_.*"}[1m]) > 0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent upstream_cx_connect_timeout nonzero rate (prod)
            description: envoy connection timeout rate for mms-rtagent above zero for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent envoy upstream consecutive connection failure rate is high (prod)
          expr: rate(envoy_cluster_upstream_cx_connect_attempts_exceeded{envoy_cluster_name=~"kube_mms-rtagent.*_prod_.*"}[1m]) > 0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent upstream_cx_connect_attempts_exceeded has nonzero rate (prod)
            description: envoy consecutive connection failures exceeding configured connection attempts rate for mms-rtagent above zero for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent envoy upstream cluster circuit breaker overflow rate is high (prod)
          expr: rate(envoy_cluster_upstream_cx_overflow{envoy_cluster_name=~"kube_mms-rtagent.*_prod_.*"}[1m]) > 0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent upstream_cx_overflow has nonzero rate (prod)
            description: envoy times that the cluster's connection circuit breaker overflowed for mms-rtagent above zero for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent envoy upstream pool overflow rate is high (prod)
          expr: rate(envoy_cluster_upstream_cx_pool_overflow{envoy_cluster_name=~"kube_mms-rtagent.*_prod_.*"}[1m]) > 0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent upstream_cx_pool_overflow has nonzero rate (prod)
            description: envoy times that the cluster's connection pool circuit breaker overflowed for mms-rtagent above zero for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent envoy upstream connection protocol error rate is high (prod)
          expr: rate(envoy_cluster_upstream_cx_protocol_error{envoy_cluster_name=~"kube_mms-rtagent.*_prod_.*"}[1m]) > 0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent upstream_cx_protocol_error has nonzero rate (prod)
            description: envoy connection protocol error rate for mms-rtagent above zero for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent envoy upstream max_requests rate is high (prod)
          expr: rate(envoy_cluster_upstream_cx_max_requests{envoy_cluster_name=~"kube_mms-rtagent.*_prod_.*"}[1m]) > 0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent upstream_cx_max_requests has nonzero rate (prod)
            description: envoy connections closed due to maximum requests rate for mms-rtagent above zero for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent envoy no healthy upstreams (prod)
          expr: rate(envoy_cluster_upstream_cx_none_healthy{envoy_cluster_name=~"kube_mms-rtagent.*_prod_.*"}[1m]) > 0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent upstream_cx_none_healthy has nonzero rate (prod)
            description: envoy rate of connection not established due to no healthy hosts for mms-rtagent above zero for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent envoy request pending overflow has nonzero rate (prod)
          expr: rate(envoy_cluster_upstream_rq_pending_overflow{envoy_cluster_name=~"kube_mms-rtagent.*_prod_.*"}[1m]) > 0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent upstream_rq_pending_overflow has nonzero rate (prod)
            description: envoy rate of requests that overflowed connection pool or requests circuit breaking and failed for mms-rtagent above zero for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent envoy request pending overflow is over rate of 100 (prod)
          expr: rate(envoy_cluster_upstream_rq_pending_overflow{envoy_cluster_name=~"kube_mms-rtagent.*_prod_.*"}[1m]) > 100
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: mms-rtagent upstream_rq_pending_overflow has rate of {{ $value }} (prod)
            description: envoy rate of requests that overflowed connection pool or requests circuit breaking and failed for mms-rtagent above 100 for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
        - alert: mms-rtagent envoy request pending failure ejection has nonzero rate (prod)
          expr: rate(envoy_cluster_upstream_rq_pending_failure_eject{envoy_cluster_name=~"kube_mms-rtagent.*_prod_.*"}[1m]) > 0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent upstream_rq_pending_failure_eject has nonzero rate (prod)
            description: envoy rate of requests that were failed due to a connection pool connection failure or remote connection termination for mms-rtagent above zero for last 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
    - name: mms-rtagent_envoy_downstream_prod
      rules:
        - alert: mms-rtagent envoy 5xx rate is high (prod)
          expr: sum by(envoy_http_conn_manager_prefix) (increase(envoy_http_downstream_rq_xx{envoy_http_conn_manager_prefix=~"mms-rtagent(-regional)?",envoy_response_code_class="5"}[1m])) / sum  by(envoy_http_conn_manager_prefix) (increase(envoy_http_downstream_rq_xx{envoy_http_conn_manager_prefix=~"mms-rtagent(-regional)?"}[1m])) > 0.01
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: mms-rtagent envoy downstream has >1% 5xx rate (prod)
            description: envoy mms-rtagent listener has had >1% 5xx response codes for 20m (prod)
            runbook_url: 'https://wiki.corp.mongodb.com/display/MMS/Automation+Team+Alerts+Runbook'
