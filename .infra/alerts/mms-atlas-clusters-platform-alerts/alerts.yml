---
# yamllint disable rule:quoted-strings

pagerdutyService:
  enabled: true
  escalationPolicyName: Atlas Clusters Lead
autoConfigAlertRouting: true
alertmanagerConfig:
  enabled: true
  legacyEmailConfig:
    enabled: true
    externalSecretsComponent: core-systems-alerts
prometheusRule:
  additionalRuleLabels:
    namespace: "{{ .Release.Namespace }}"
  groups:
    - name: mms_nds_clusters_unable_to_plan_alerting_rules
      rules:
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) The number of clusters unable to plan is high - critical
          expr: |
            (
              sum(increase(mms_core_alert_statuses_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", event_type="CLUSTERS_UNABLE_TO_PLAN", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment)
              - avg_over_time(sum(increase(mms_core_alert_statuses_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", event_type="CLUSTERS_UNABLE_TO_PLAN", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment)[7d:1h])
            )
            / stddev_over_time(sum(increase(mms_core_alert_statuses_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", event_type="CLUSTERS_UNABLE_TO_PLAN", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment) [7d:1h]) > {{ .Values.rulesTplVals.clusters_unable_to_plan_high_critical.threshold1 }}
            and stddev_over_time(sum(increase(mms_core_alert_statuses_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", event_type="CLUSTERS_UNABLE_TO_PLAN", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment)[7d:1h]) > {{ .Values.rulesTplVals.clusters_unable_to_plan_high_critical.threshold2 }}
          for: 5m
          labels:
            severity: "{{ .Values.rulesTplVals.clusters_unable_to_plan_high_critical.severity }}"
          annotations:
            summary: A high number of clusters are currently unable to plan.
            description: The z score for clusters unable to plan has reached at least +25 within the last hour.
            grafana_url: 'https://grafana.corp.mongodb.com/d/Fj7aq3USk/atlas-cluster-customer-experience-cx?orgId=20&from=now-24h&to=now&viewPanel=34'
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) The number of clusters unable to plan is high.
          expr: |
            (
              sum(increase(mms_core_alert_statuses_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", event_type="CLUSTERS_UNABLE_TO_PLAN", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment)
              - avg_over_time(sum(increase(mms_core_alert_statuses_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", event_type="CLUSTERS_UNABLE_TO_PLAN", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment)[7d:1h])
            )
            / stddev_over_time(sum(increase(mms_core_alert_statuses_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", event_type="CLUSTERS_UNABLE_TO_PLAN", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment) [7d:1h]) > 10
            and stddev_over_time(sum(increase(mms_core_alert_statuses_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", event_type="CLUSTERS_UNABLE_TO_PLAN", status=~"OPEN|TRACKING"}[2h])) by (xgen_environment)[7d:1h]) > 0
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: A high number of clusters are currently unable to plan.
            description: The z score for clusters unable to plan has reached at least +25 within the last hour.
            grafana_url: 'https://grafana.corp.mongodb.com/d/Fj7aq3USk/atlas-cluster-customer-experience-cx?orgId=20&from=now-24h&to=now&viewPanel=34'
    - name: mms_nds_mtm_compaction_alerting_rules
      rules:
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Tenant compaction failed
          expr: sum(increase(mms_nds_mtm_compaction_tenant_failed_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[5m])) > 0
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: MTM Compaction failed to compact tenant
            description: MTM Compaction failed to compact tenant
            splunk_url: 'https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3Dmms%20%22Fail%20to%20pause%20compaction%22&earliest=-4h%40m&latest=now'
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Too many paused compactions
          expr: max(mms_nds_mtm_compaction_gauge{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", status="PAUSED"}) > 2
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Too many paused compactions
            description: Implies tenant compaction failures
            splunk_url: 'https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3Dmms%20%22Fail%20to%20pause%20compaction%22&earliest=-4h%40m&latest=now'
    - name: mms_nds_planner_alerting_rules
      rules:
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Planner jobId mismatch
          expr: sum(rate(mms_nds_planner_job_id_mismatch_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[1m])) > 0
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Too many jobId mismatch during planning
            description: Indicates that PlanGenerationJobId for a group is overridden by other planning job
            splunk_url: 'https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3Dmms%20%22in%20group%20does%20not%20match%20passed%20in%20jobId%22&earliest=-4h%40m&latest=now'
    - name: lets_encrypt_alerting_rules
      rules:
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Certificate issued by non-R10 or R11 Lets Encrypt intermediate or Google Trust Services
          expr: sum(rate(mms_nds_acme_certificates_downloaded_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", issuer!~"CN=R10,O=Let's Encrypt,C=US|CN=R11,O=Let's Encrypt,C=US|CN=R12,O=Let's Encrypt,C=US|CN=R13,O=Let's Encrypt,C=US|CN=WR1,O=Google Trust Services,C=US"}[1m])) by (issuer) > 0
          for: 1m
          labels:
            severity: warning
          annotations:
            summary: Let's Encrypt or GTS Certificate issued by unexpected cert
            description: Indicates that at least one Certificate was issued by an unexpected cert
    - name: kube_services_failing_to_start_or_are_not_ready
      rules:
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Number of ready pods in mms namespace is less than 50% for 10 minutes
          expr: (kube_deployment_status_replicas_ready{job="kube-state-metrics", namespace=~"mms.*", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", deployment!~"istio.*"} / kube_deployment_status_replicas) < {{ .Values.rulesTplVals.ready_pod_low_critical.threshold }}
          for: 10m
          labels:
            severity: "{{ .Values.rulesTplVals.ready_pod_low_critical.severity }}"
          annotations:
            summary: 'Number of { $labels.deployment }} ready pods is less than 50% for 10 minutes ({{ $labels.xgen_environment }},{{ $labels.xgen_region }})'
            description: 'Number of { $labels.deployment }} ready pods is less than 50% for 10 minutes ({{ $labels.xgen_environment }},{{ $labels.xgen_region }})'
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/MMS+in+Helix+Runbook
            affected_hosts_com: https://victoria-metrics.corp.mongodb.com/select/0/vmui/#/?g0.expr=(kube_deployment_status_replicas_ready%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22(staging%7Cprod%7Cinternal)%22,%20deployment!~%22istio.*%22%7D%20/%20kube_deployment_status_replicas)%20%3C%200.50&g0.tab=0&g0.stacked=0&g0.range_input=1h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            affected_hosts_gov: https://thanos.corp.mongodbgov.com/graph?g0.expr=(kube_deployment_status_replicas_ready%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22prod-gov%22,%20deployment!~%22istio.*%22%7D%20/%20kube_deployment_status_replicas)%20%3C%200.50&g0.tab=0&g0.stacked=0&g0.range_input=1h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            impact: Mms kube service is available with limited capacity.
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Kube pod is not ready in mms namespace for 30 minutes
          expr: kube_pod_status_ready{job="kube-state-metrics", condition!="true", pod!~"istio.*", namespace=~"mms.*", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"} > 0
          for: 30m
          labels:
            severity: warning
          annotations:
            summary: The pod {{ $labels.pod }} has been in a non-ready state for more than than 30 minutes ({{ $labels.xgen_environment }}, {{ $labels.xgen_region }})
            description:
              The pod {{ $labels.pod }} in namespace {{ $labels.namespace }}
              has been in a non-ready state for longer than 30 minutes.
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/MMS+in+Helix+Runbook
            affected_hosts_com: https://victoria-metrics.corp.mongodb.com/select/0/vmui/#/?g0.expr=kube_pod_status_ready%7Bcondition!%3D%22true%22,pod!~%22istio.*%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22(staging%7Cprod%7Cinternal)%22%7D%20%3E%200&g0.tab=0&g0.stacked=0&g0.range_input=6h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            affected_hosts_gov: https://thanos.corp.mongodbgov.com/graph?g0.expr=kube_pod_status_ready%7Bcondition!%3D%22true%22,pod!~%22istio.*%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22prod-gov%22%7D%20%3E%200&g0.tab=0&g0.stacked=0&g0.range_input=6h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            impact: Some mms pods are down. The service is running with limited capacity.
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Critical pod unavailability in mms namespace
          expr: (kube_deployment_status_replicas{job="kube-state-metrics", namespace=~"mms.*", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", deployment!~"istio.*"} - kube_deployment_status_replicas_ready{job="kube-state-metrics", namespace=~"mms.*", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", deployment!~"istio.*"}) > 2 and (kube_deployment_status_replicas_ready{job="kube-state-metrics", namespace=~"mms.*", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", deployment!~"istio.*"} / kube_deployment_status_replicas{job="kube-state-metrics", namespace=~"mms.*", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", deployment!~"istio.*"}) < {{ .Values.rulesTplVals.pod_unavailability_critical.threshold }}
          for: 10m
          labels:
            severity: "{{ .Values.rulesTplVals.pod_unavailability_critical.severity }}"
          annotations:
            summary: 'Critical pod unavailability detected for {{ $labels.deployment }} - more than 2 pods unavailable and less than 85% pods ready ({{ $labels.xgen_environment }}, {{ $labels.xgen_region }})'
            description: 'Deployment {{ $labels.deployment }} in namespace {{ $labels.namespace }} has more than 2 pods unavailable and less than 85% pods ready for 10 minutes ({{ $labels.xgen_environment }}, {{ $labels.xgen_region }})'
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/MMS+in+Helix+Runbook
            affected_hosts_com: https://victoria-metrics.corp.mongodb.com/select/0/vmui/#/?g0.expr=(kube_deployment_status_replicas%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22(staging%7Cprod%7Cinternal)%22,%20deployment!~%22istio.*%22%7D%20-%20kube_deployment_status_replicas_ready%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22(staging%7Cprod%7Cinternal)%22,%20deployment!~%22istio.*%22%7D)%20%3E%202%20and%20(kube_deployment_status_replicas_ready%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22(staging%7Cprod%7Cinternal)%22,%20deployment!~%22istio.*%22%7D%20/%20kube_deployment_status_replicas%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22(staging%7Cprod%7Cinternal)%22,%20deployment!~%22istio.*%22%7D)%20%3C%200.85&g0.tab=0&g0.stacked=0&g0.range_input=1h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            affected_hosts_gov: https://thanos.corp.mongodbgov.com/graph?g0.expr=(kube_deployment_status_replicas%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22prod-gov%22,%20deployment!~%22istio.*%22%7D%20-%20kube_deployment_status_replicas_ready%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22prod-gov%22,%20deployment!~%22istio.*%22%7D)%20%3E%202%20and%20(kube_deployment_status_replicas_ready%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22prod-gov%22,%20deployment!~%22istio.*%22%7D%20/%20kube_deployment_status_replicas%7Bjob%3D%22kube-state-metrics%22,%20namespace%3D~%22mms.*%22,%20xgen_environment%3D~%22prod-gov%22,%20deployment!~%22istio.*%22%7D)%20%3C%200.85&g0.tab=0&g0.stacked=0&g0.range_input=1h&g0.max_source_resolution=0s&g0.deduplicate=1&g0.partial_response=0&g0.store_matches=%5B%5D&g0.engine=prometheus&g0.explain=0
            impact: Critical pod unavailability detected. The service is running with severely limited capacity.
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) CrashLoopBackOff
          expr: kube_pod_container_status_waiting_reason{reason="CrashLoopBackOff", namespace=~"mms-.*", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"} > bool 0
          for: 1m
          labels:
            severity: "{{ .Values.rulesTplVals.crash_loop_backoff_critical.severity }}"
          annotations:
            summary: "{{ $value }} MMS kube pods in {{ $labels.xgen_environment }} are failing to start during the deployment."
            description: |
              The condition indicates that kube pods in {{ $labels.xgen_environment }} environment are unable to start
              during the deployment. It usually means that some secrets aren't there or app startup is broken. MMS logs
              might be the best source for information about the failure.
            runbook: https://wiki.corp.mongodb.com/display/MMS/MMS+in+Helix+Runbook
            affected_hosts_com: https://victoria-metrics.corp.mongodb.com/select/0/vmui/#/?g0.expr=kube_pod_container_status_waiting_reason%7Breason%3D"CrashLoopBackOff"%2C%20namespace%3D~"mms-.*"%2C%20xgen_environment%3D~"(staging%7Cprod)"%7D%20>%20bool%200&g0.tab=1&g0.stacked=0&g0.show_exemplars=0&g0.range_input=1h
            affected_hosts_gov: https://thanos.corp.mongodbgov.com/graph?g0.expr=kube_pod_container_status_waiting_reason%7Breason%3D"CrashLoopBackOff"%2C%20namespace%3D~"mms-.*"%2C%20xgen_environment%3D~"prod-gov"%7D%20>%20bool%200&g0.tab=1&g0.stacked=0&g0.show_exemplars=0&g0.range_input=1h
            splunk_search_com_staging: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3D%22mms-staging%22%20kube.namespace_name%20=%20%22mms-*%22&display.page.search.mode=smart&dispatch.sample_ratio=1&workload_pool=&earliest=-60m%40m&latest=now&sid=**********.10438130
            splunk_search_com_prod: https://splunk.corp.mongodb.com/en-US/app/search/search?q=search%20index%3D%22mms-prod%22%20kube.namespace_name%20=%20%22mms-*%22&display.page.search.mode=smart&dispatch.sample_ratio=1&workload_pool=&earliest=-60m%40m&latest=now&sid=**********.10438132
            impact: Some services are down. Multiple versions of the code may be running at the same time.
    - name: elevated_health_monitoring_alerting_rules
      rules:
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Unhealthy clusters from discrete elevated health monitoring exceeds warning threshold
          expr: sum(increase(mms_nds_group_healthcheck_monitoring_unhealthy_actions_warning_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[2h])) by (monitoring_name) > 0
          labels:
            severity: warning
          annotations:
            summary: An elevated health monitoring has exceeded its warning threshold of unhealthy clusters
            description: Number of times unhealthy clusters exceeded warning threshold by increment of itself for a discrete EHM
            runbook_url: https://wiki.corp.mongodb.com/x/Vqi9Eg
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Unhealthy clusters from discrete elevated health monitoring exceeds critical threshold
          expr: sum(increase(mms_nds_group_healthcheck_monitoring_unhealthy_actions_critical_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[2h])) by (monitoring_name) > 0
          labels:
            severity: warning
          annotations:
            summary: An elevated health monitoring has exceeded its critical threshold of unhealthy clusters
            description: Number of times unhealthy clusters exceeded critical threshold by increment of warning threshold for a discrete EHM
            runbook_url: https://wiki.corp.mongodb.com/x/Vqi9Eg
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Unhealthy clusters from continuous elevated health monitoring exceeds cluster threshold
          expr: sum(increase(mms_nds_group_healthcheck_monitoring_unhealthy_clusters_continuous_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[2h])) by (monitoring_name) > {{ .Values.rulesTplVals.ehm_unhealthy_clusters_continuous.threshold }}
          labels:
            severity: warning
            threshold: "{{ .Values.rulesTplVals.ehm_unhealthy_clusters_continuous.threshold }}"
          annotations:
            summary: A continuous elevated health monitoring has exceeded its {{ $labels.threshold }} unhealthy cluster threshold over a 2 hour period.
            description: Number of unhealthy clusters for a continuous EHM.
            runbook_url: https://wiki.corp.mongodb.com/x/Vqi9Eg
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Failed plans from discrete elevated health monitoring exceeds warning threshold
          expr: sum(increase(mms_nds_ehm_plans_failed_warning_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[2h])) by (monitoring_name) > 0
          labels:
            severity: warning
          annotations:
            summary: An elevated health monitoring has exceeded its warning threshold of failed plans
            description: Number of failed plans after warning threshold was breached for discrete EHM
            runbook_url: https://wiki.corp.mongodb.com/x/Vqi9Eg
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Failed plans from discrete elevated health monitoring exceeds critical threshold
          expr: sum(increase(mms_nds_ehm_plans_failed_critical_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[2h])) by (monitoring_name) > 0
          labels:
            severity: warning
          annotations:
            summary: An elevated health monitoring has exceeded its critical threshold of failed plans
            description: Number of failed plans after critical threshold was breached for discrete EHM
            runbook_url: https://wiki.corp.mongodb.com/x/Vqi9Eg
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Failed plans from continuous elevated health monitoring exceeds plan threshold
          expr: sum(increase(mms_nds_ehm_plans_failed_continuous_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[2h])) by (monitoring_name) > {{ .Values.rulesTplVals.ehm_failed_plans_continuous.threshold }}
          labels:
            severity: warning
            threshold: "{{ .Values.rulesTplVals.ehm_unhealthy_clusters_continuous.threshold }}"
          annotations:
            summary: A continuous elevated health monitoring has exceeded its {{ $labels.threshold }} failed plan threshold over 2 hours.
            description: Number of failed plans for a continuous EHM.
            runbook_url: https://wiki.corp.mongodb.com/x/Vqi9Eg
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Stuck plans from discrete elevated health monitoring exceeds warning threshold
          expr: sum(increase(mms_nds_group_healthcheck_monitoring_stuck_plans_warning_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[2h])) by (monitoring_name) > 0
          labels:
            severity: warning
          annotations:
            summary: A discrete elevated health monitoring has exceeded its warning threshold of stuck plans
            description: Number of stuck plans after warning threshold was breached for discrete EHM
            runbook_url: https://wiki.corp.mongodb.com/x/Vqi9Eg
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Stuck plans from discrete elevated health monitoring exceeds critical threshold
          expr: sum(increase(mms_nds_group_healthcheck_monitoring_stuck_plans_critical_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[2h])) by (monitoring_name) > 0
          labels:
            severity: warning
          annotations:
            summary: A discrete elevated health monitoring has exceeded its critical threshold of stuck plans
            description: Number of stuck plans after critical threshold was breached for discrete EHM
            runbook_url: https://wiki.corp.mongodb.com/x/Vqi9Eg
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Stuck plans from continuous elevated health monitoring exceeds plan threshold
          expr: sum(increase(mms_nds_group_healthcheck_monitoring_stuck_plans_continuous_total{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}"}[2h])) by (monitoring_name) > {{ .Values.rulesTplVals.ehm_stuck_plans_continuous.threshold }}
          labels:
            severity: warning
            threshold: "{{ .Values.rulesTplVals.ehm_stuck_plans_continuous.threshold }}"
          annotations:
            summary: A continuous elevated health monitoring has exceeded its {{ $labels.threshold }} stuck plan threshold over 2 hours.
            description: Number of stuck plans for a continuous EHM.
            runbook_url: https://wiki.corp.mongodb.com/x/Vqi9Eg
    - name: mms_nds_cloud_provider_capacity_alerting_rules
      rules:
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) AWS Capacity is below {{ .Values.rulesTplVals.mms_nds_aws_remaining_capacity.threshold }}, please ping on-call lead
          expr: max(max_over_time(mms_nds_aws_remaining_capacity{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}",xgen_app="mms-cron-jobs",xgen_region="us-east-1"}[1h])) < {{ .Values.rulesTplVals.mms_nds_aws_remaining_capacity.threshold }}
          for: 5m
          labels:
            severity: warning
            threshold: "{{ .Values.rulesTplVals.mms_nds_aws_remaining_capacity.threshold }}"
          annotations:
            summary: AWS capacity is below {{ $labels.threshold }}
            description: AWS capacity is below {{ $labels.threshold }} - current value {{ $value }}
            grafana_url: https://grafana.corp.mongodb.com/d/6l_mkbV4k/leaked-items?orgId=20&var-datasource=default&var-env=prod&var-metrics_int=1m
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/Atlas+Admin+Playbook#AtlasAdminPlaybook-AddingaNewCloudProviderAccountinProduction
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) Azure Capacity is below {{ .Values.rulesTplVals.mms_nds_azure_remaining_capacity.threshold }}, please ping on-call lead
          expr: max(max_over_time(mms_nds_azure_remaining_capacity{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", xgen_app="mms-cron-jobs", xgen_region="us-east-1"}[1h])) < {{ .Values.rulesTplVals.mms_nds_azure_remaining_capacity.threshold }}
          for: 5m
          labels:
            severity: warning
            threshold: "{{ .Values.rulesTplVals.mms_nds_azure_remaining_capacity.threshold }}"
          annotations:
            summary: Azure capacity is below {{ $labels.threshold }}
            description: Azure capacity is below {{ $labels.threshold }} - current value {{ $value }}
            grafana_url: https://grafana.corp.mongodb.com/d/6l_mkbV4k/leaked-items?orgId=20&var-datasource=default&var-env=prod&var-metrics_int=1m
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/Atlas+Admin+Playbook#AtlasAdminPlaybook-AddingaNewCloudProviderAccountinProduction
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) GCP Capacity is below {{ .Values.rulesTplVals.mms_nds_gcp_remaining_capacity.threshold }}, please ping on-call lead
          expr: max(max_over_time(mms_nds_gcp_remaining_capacity{xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", xgen_app="mms-cron-jobs", xgen_region="us-east-1"}[1h])) < {{ .Values.rulesTplVals.mms_nds_gcp_remaining_capacity.threshold }}
          for: 5m
          labels:
            severity: warning
            threshold: "{{ .Values.rulesTplVals.mms_nds_gcp_remaining_capacity.threshold }}"
          annotations:
            summary: GCP capacity is below {{ $labels.threshold }}
            description: GCP capacity is below {{ $labels.threshold }} - current value {{ $value }}
            grafana_url: https://grafana.corp.mongodb.com/d/6l_mkbV4k/leaked-items?orgId=20&var-datasource=default&var-env=prod&var-metrics_int=1m
            runbook_url: https://wiki.corp.mongodb.com/display/MMS/Atlas+Admin+Playbook#AtlasAdminPlaybook-AddingaNewCloudProviderAccountinProduction
    - name: argocd_alerting_rules
      rules:
        - alert: ({{ .Values.additionalLabels.xgen_environment }}) ArgoCD mms-jobs-atlas-prioritized app is out of sync
          expr: sum(argocd_app_info{name=~"{{ .Values.rulesTplVals.argocd_alerting_rules.app_name }}", xgen_environment="{{ .Values.additionalLabels.xgen_environment }}", sync_status="OutOfSync"})  by (xgen_environment, xgen_region) > 0
          for: 20m
          labels:
            severity: warning
          annotations:
            summary: ArgoCD mms-jobs-atlas-prioritized app is out of sync in env {{ $labels.xgen_environment }} region {{ $labels.xgen_region }}
            description: ArgoCD mms-jobs-atlas-prioritized app is out of sync. This can lead to apps not being appropriate updated. Availability of the app could suffer.
            runbook_url: 'https://wiki.corp.mongodb.com/spaces/MMS/pages/********/Atlas+Admin+Playbook#AtlasAdminPlaybook-ArgoCDoutofSyncalert'
