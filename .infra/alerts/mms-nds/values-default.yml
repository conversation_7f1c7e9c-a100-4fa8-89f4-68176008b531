---
rulesTplVals:
  acme_orders_high_usage:
    threshold: 10000
  planner_lag_15min:
    threshold: 900
    for_duration: "5m"
  job_ownership_delay:
    for_duration: "2m"
    severity: warning
  mms_core_job_queue_alerting_rules:
    time_window: "1h"
    threshold: 19
  redis_errors:
    threshold: 20
  tenant_snapshot_errors:
    threshold: 15
  tenant_restore_errors:
    threshold: 15
  encryption_at_rest_errors:
    threshold: 30
  gcp_capacity_errors:
    threshold: 0
  move_rollback_zscore:
    threshold: 10
  step_rollback_zscore:
    threshold: 15
  slow_provisioned_M0_Clusters:
    threshold: 15
  fast_M0_record_low_capacity:
    threshold: 8
  mms_nds_data_validation_svc_capacity:
    threshold: 0
  acme_orders_high_usage_critical:
    severity: warning
    threshold: 99999999
  mms_nds_acme_certificates_downloaded_critical:
    severity: warning
    threshold: 99999999
  mms_nds_acme_ca_api_errors_critical:
    severity: warning
    threshold: 99999999
  planner_lag_30min_critical:
    severity: warning
  planner_down_dry_run_mode_admin_critical:
    severity: warning
  planner_down_paused_by_admin_critical:
    severity: warning
  planner_down_no_plans_created_critical:
    severity: warning
  aws_cloud_provider_capacity_automated_test_region:
    threshold: 99999999
  average_job_ownership_delay_critical:
    severity: warning
    threshold: 99999999
  redis_operation_latency_high:
    severity: warning
  planner_down_missing_plan_creation_metrics_critical:
    severity: warning
    threshold: 99999999
  gcp_az_instances_in_healing_critical:
    severity: warning
    threshold: 99999999
  azure_az_instances_in_healing_critical:
    severity: warning
    threshold: 99999999
  aws_capacity_below_threshold_with_region_critical:
    severity: warning
    threshold: 99999999
  aws_capacity_below_threshold_no_region_critical:
    severity: warning
    threshold: 99999999
  azure_capacity_below_threshold_critical:
    severity: warning
  gcp_capacity_below_threshold_critical:
    severity: warning
  aws_az_instances_in_healing_critical:
    severity: warning
    percentThreshold: 99999999
    totalThreshold: 99999999
  jira_critical_invalidated_rate_critical:
    severity: warning
  sfdc_critical_invalidated_rate_critical:
    severity: warning
  intercom_critical_invalidated_rate_critical:
    severity: warning
  ready_pods_less_than_50_percent_critical:
    severity: warning
  kube_pod_not_ready_30_minutes_critical:
    severity: warning
  crash_loop_back_off_critical:
    severity: warning
  excessive_dns_records_in_queue_critical:
    severity: warning
    threshold: 99999999
  clusters_unable_to_plan_high_critical:
    severity: warning
    threshold1: 99999999
    threshold2: 99999999
  clusters_unable_to_plan_high_warning:
    severity: warning
    threshold1: 10
    threshold2: 0
  subscription_usage_cron_job_critical:
    severity: warning
    threshold: 99999999
  aws_capacity_errors:
    threshold: 25
  load_sample_dataset_high_cpu_usage_critical:
    severity: warning
    threshold: 99999999
  jobs_atlas_prioritized_high_cpu_usage_critical:
    severity: warning
    threshold: 99999999
  jobs_atlas_prioritized_low_memory_critical:
    severity: warning
    threshold: 99999999
  planning_failure_alerting_rules_critical:
    severity: warning
    threshold: 99999999
  plan_rollback_alerting_rules_critical:
    severity: warning
    threshold: 99999999
  aws_cloud_provider_capacity_without_region:
    threshold: 99999999
  aws_cloud_provider_capacity_lt:
    threshold: 50
  route53_resource_records_usage:
    threshold: 99999999
  gcp_az_instances_in_healing_warning:
    threshold: .50
  azure_az_instances_in_healing_warning:
    threshold: .50
  aws_az_instances_in_healing_warning:
    percentThreshold: .50
    totalThreshold: 5
  planner_lag_1hr_warning:
    threshold: 3600
  missing_plan_creation_metrics:
    xgen_kube_cluster: "non-existent-disables-the-alert"
    for_duration: "70m"
    severity: warning
  missing_plan_creation_metrics2:
    xgen_kube_cluster: "non-existent-disables-the-alert"
    for_duration: "70m"
    severity: warning
  missing_plan_creation_metrics3:
    for_duration: "70m"
    severity: warning
    app_name: "non-existent-disables-the-alert"
  reaper_not_running:
    threshold: -99999999 # impossible threshold, alert won't fire
  mms_atlas_billing_alerting_error_NDSPremiumCloudManagerMeterUsageSubmission:
    for_duration: "1520m"
    xgen_kube_cluster: "non-existent-disables-the-alert"
  kms_shutdown_threshold_exceeded:
    severity: warning
  azure_quota_usage_80p:
    threshold: 99999999 # impossible threshold, alert won't fire
  azure_quota_usage_95p:
    threshold: 99999999 # impossible threshold, alert won't fire
