---

pagerdutyService:
  name: mms-nds-non-prod-gov
  fullnameOverride: mms-nds-non-prod-gov
rulesTplVals:
  planner_lag_15min:
    for_duration: "15m"
  job_ownership_delay:
    for_duration: "1m"
    severity: warning
  acme_orders_high_usage:
    threshold: 2500
  capacity_alerting_rules_aws:
    account: "cloud-qa-(com|gov).*"
  route_53_records:
    account: "cloud-qa-(com|gov)-root"
  blackbox_monitoring:
    agent_pool_url: "https://cloud-qa.mongodbgov.com/monitor/pool/agent"
    ui_pool_url: "https://cloud-qa.mongodbgov.com/monitor/pool/ui"
  missing_plan_creation_metrics:
    xgen_kube_cluster: "kube-1-us-gov-west-1-aws-cloud-gov-dev"
    for_duration: "70m"
    severity: warning
  subscription_usage_cron_job_critical:
    for_duration: "140m"
    severity: warning
    xgen_kube_cluster: "kube-1-us-gov-west-1-aws-cloud-gov-dev"
  mms_atlas_billing_alerting_error_meterUsageSubmission:
    for_duration: "440m"
    xgen_kube_cluster: "kube-1-us-gov-west-1-aws-cloud-gov-dev"
  mms_atlas_billing_alerting_error_NDSPremiumCloudManagerMeterUsageSubmission:
    for_duration: "1520m"
    xgen_kube_cluster: "kube-1-us-gov-west-1-aws-cloud-gov-dev"
  mms_nds_az_capacity_checkers_alerting_rules:
    xgen_kube_cluster: "kube-1-us-gov-west-1-aws-cloud-gov-dev"
