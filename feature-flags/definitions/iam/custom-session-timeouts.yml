name: mms.featureFlag.customSessionTimeouts
namespace: global
scope: organization
description: Guards frontend components and endpoints used for the Atlas UI Custom Session Timeout
  feature
phases:
  local: controlled
  local-gov: controlled
  test: controlled
  test-gov: controlled
  dev: enabled
  dev-gov: enabled
  qa: controlled
  qa-gov: controlled
  stage: controlled
  prod: disabled
  prod-gov: disabled
  internal: disabled
