import {
  ChatbotDemoBuilder,
  ChatbotMessageContent,
  Chatbox,
  CopyPasteModal,
  DataSettingsModal,
  DocumentsPreviewModal,
  Header,
  ImportPdfModal,
  LandingPage,
  ShareConfirmationModal,
  SideNavPanel,
  StartOverConfirmationModal,
} from '../pages/chatbot-demo-builder.page';

const CHATBOT_DEMO_BUILDER_URL = `/tools/chatbot-demo-builder/snapshots/new`;

describe('Chatbot Demo Builder', () => {
  describe('#redirects', () => {
    it('should redirect to chatbot-demo-builder snapshot page', () => {
      cy.visit('/tools/chatbot-demo-builder');
      cy.location('pathname').should('eq', CHATBOT_DEMO_BUILDER_URL);
    });
  });

  describe('landing page', () => {
    it('should render the chatbot demo builder landing page', () => {
      visitChatbotDemoBuilderPage();
      cy.matchSnapshot({
        // This is a text-heavy component. We want to allow for some diffs between
        // Mac and Linux as well as various browsers rendering fonts
        maxDiffThreshold: 0.1,
      });
    });

    it('should import and parse pdf document content', () => {
      visitChatbotDemoBuilderPage();
      uploadDummyPdfFile();
      DataSettingsModal.chunksPreview().should('contain', 'Dummy PDF file');
    });

    it('should import via copy-paste', () => {
      visitChatbotDemoBuilderPage();
      LandingPage.copyPasteButton().click();
      CopyPasteModal.textarea().type('copy & pasted text');
      CopyPasteModal.nextButton().click();
      DataSettingsModal.chunksPreview().should('contain', 'copy & pasted text');
    });
  });

  describe('main page', () => {
    beforeEach(() => {
      // mock API responses
      cy.intercept(
        {
          method: 'POST',
          url: '/api/tools/chatbot-demo-builder/embeddings',
        },
        {
          embeddings: [[0]],
        }
      );
      cy.intercept(
        {
          method: 'POST',
          url: '/api/tools/chatbot-demo-builder/chat',
        },
        {
          response: 'im fine, thank you',
          searchPipeline: [
            {
              $vectorSearch: {
                index: 'default',
                path: 'vector_embeddings',
                exact: false,
                numCandidates: 40,
                limit: 2,
                queryVector: [0],
              },
            },
            {
              $project: {
                vector_embeddings: 1,
                text: 1,
                page_number: 1,
                score: {
                  $meta: 'vectorSearchScore',
                },
              },
            },
          ],
          contextDocuments: [
            {
              _id: 9,
              text: 'document 1',
              page_number: 9,
              vector_embeddings: [0],
              score: 0.7211458683013916,
            },
            {
              _id: 17,
              text: 'document 2',
              page_number: 17,
              vector_embeddings: [0],
              score: 0.7163580656051636,
            },
          ],
          llmName: 'Anthropic Claude 3 Haiku',
        }
      );
    });

    it('should generate embeddings and provide a response when asked a question', () => {
      visitChatbotDemoBuilderPage();
      loadSourceDataAndGenerateEmbeddings();

      // ask a question
      Chatbox.textarea().type('hello how are you?');
      Chatbox.sendButton().click();

      // wait for a response to be generated
      ChatbotMessageContent.get().scrollIntoView();
      ChatbotMessageContent.get().should('be.visible').should('contain', 'im fine, thank you');

      // compare snapshots
      cy.matchSnapshot({
        // This is a text-heavy component. We want to allow for some diffs between
        // Mac and Linux as well as various browsers rendering fonts
        maxDiffThreshold: 0.1,
      });
    });

    it('should show the documents used as context', () => {
      visitChatbotDemoBuilderPage();
      loadSourceDataAndGenerateEmbeddings();

      // ask a question
      Chatbox.textarea().type('hello how are you?');
      Chatbox.sendButton().click();

      // wait for a response to be generated
      ChatbotMessageContent.get().scrollIntoView();
      ChatbotMessageContent.get().should('be.visible').should('contain', 'im fine, thank you');

      // click on documents link
      ChatbotMessageContent.documentsLink().click();
      ChatbotDemoBuilder.get().should('contain', 'Documents retrieved');
    });

    it('should render the search config panel', () => {
      visitChatbotDemoBuilderPage();
      loadSourceDataAndGenerateEmbeddings();

      // ask a question
      Chatbox.textarea().type('hello how are you?');
      Chatbox.sendButton().click();

      // wait for a response to be generated
      ChatbotMessageContent.get().scrollIntoView();
      ChatbotMessageContent.get().should('be.visible').should('contain', 'im fine, thank you');

      // open bottom panel
      ChatbotDemoBuilder.searchConfigPanelToggleButton().click();
      ChatbotDemoBuilder.searchConfigPanel().contains('Dummy PDF file');

      // switch to Index definition tab
      ChatbotDemoBuilder.searchConfigPanelTabList().get('.playground-tab').contains('Index definition').click();
      ChatbotDemoBuilder.searchConfigPanel().contains('vectorSearch');

      // switch to Search query tab
      ChatbotDemoBuilder.searchConfigPanelTabList().get('.playground-tab').contains('Search query').click();
      ChatbotDemoBuilder.searchConfigPanel().contains('$vectorSearch');

      // switch to LLM & Prompt tab
      ChatbotDemoBuilder.searchConfigPanelTabList().get('.playground-tab').contains('LLM & Prompt').click();
      ChatbotDemoBuilder.searchConfigPanel().contains(
        'A text is split into several chunks and you are provided a subset of these chunks as context to answer the question at the end.'
      );
    });

    it('should open the data settings modal', () => {
      visitChatbotDemoBuilderPage();
      loadSourceDataAndGenerateEmbeddings();

      // click change data settings
      ChatbotDemoBuilder.changeDataSettingsLink().click();

      // wait for data settings modal to be visible
      DataSettingsModal.chunksPreview().should('contain', 'Dummy PDF file');
    });

    it('should share a chatbot snapshot', () => {
      visitChatbotDemoBuilderPage();
      loadSourceDataAndGenerateEmbeddings();

      // click share
      Header.shareButton().click();
      ShareConfirmationModal.confirmButton().click();
      cy.location('pathname').should('match', /\/tools\/chatbot-demo-builder\/snapshots\/[a-zA-Z0-9]+/);
    });

    it('should return to the landing page if the user clicks on start over', () => {
      visitChatbotDemoBuilderPage();
      loadSourceDataAndGenerateEmbeddings();

      // click start over
      Header.startOverButton().click();
      StartOverConfirmationModal.confirmButton().click();
      LandingPage.get().should('be.visible');
    });
  });
});

export const visitChatbotDemoBuilderPage = () => {
  cy.visit(CHATBOT_DEMO_BUILDER_URL);
  // wait for page to finish loading
  SideNavPanel.versionsNavPanel().should('contain', 'Version');
};

export const uploadDummyPdfFile = () => {
  LandingPage.uploadPdfButton().click();
  cy.get('input[type=file]').selectFile('cypress/fixture/dummy_pdf_file.pdf', { force: true });
  ImportPdfModal.importButton().should('have.attr', 'aria-disabled', 'false').click();
};

export const loadSourceDataAndGenerateEmbeddings = () => {
  uploadDummyPdfFile();
  DataSettingsModal.chunksPreview().should('contain', 'Dummy PDF file');
  DataSettingsModal.createEmbeddingsButton().click();

  // wait for embeddings to generate
  DocumentsPreviewModal.startChattingButton().click();
};
