export class Header {
  static shareButton() {
    return cy.get(`[data-testid="ChatbotDemoBuilderHeader_shareBtn"]`);
  }

  static startOverButton() {
    return cy.get(`[data-testid="ChatbotDemoBuilderHeader_startOverBtn"]`);
  }
}

export class SideNavPanel {
  static versionsNavPanel() {
    return cy.get(`[data-testid="SideNavPanel_versionsNavGroup"]`, { timeout: 30000 });
  }
}

export class ShareConfirmationModal {
  static confirmButton() {
    return cy.get('[data-testid="lg-confirmation_modal-footer-confirm_button"]');
  }
}

export class StartOverConfirmationModal {
  static get() {
    return cy.get(`[data-testid="ChatbotDemoBuilderHeader_startOverConfirmationModal"]`);
  }

  static confirmButton() {
    return StartOverConfirmationModal.get().get('[data-testid="lg-confirmation_modal-footer-confirm_button"]');
  }
}

export class LandingPage {
  static get() {
    return cy.get('[data-testid="ChatbotDemoBuilderLandingPage_root"]');
  }

  static uploadPdfButton() {
    return cy.get('[data-testid="ChatbotDemoBuilderLandingPage_uploadPdfButton"]');
  }

  static copyPasteButton() {
    return cy.get('[data-testid="ChatbotDemoBuilderLandingPage_copyPasteButton"]');
  }

  static sampleDataButton() {
    return cy.get('[data-testid="ChatbotDemoBuilderLandingPage_sampleDataButton"]');
  }
}

export class ImportPdfModal {
  static importButton() {
    return cy.get('[data-testid="ImportPdfModal_importBtn"]');
  }
}

export class CopyPasteModal {
  static textarea() {
    return cy.get('[data-testid="CopyPasteModal_textArea"]');
  }

  static nextButton() {
    return cy.get('[data-testid="CopyPasteModal_next"]');
  }
}

export class DataSettingsModal {
  static chunksPreview() {
    return cy.get('[data-testid="ChunksPreview_root"]');
  }

  static createEmbeddingsButton() {
    return cy.get(`[data-testid="DataSettingsModal_createEmbeddingsBtn"]`);
  }
}

export class DocumentsPreviewModal {
  static startChattingButton() {
    // wait for embeddings request loading status modal to complete
    return cy.get(`[data-testid="DocumentsPreviewModal_startChattingBtn"]`, { timeout: 60000 });
  }
}

export class ChatbotDemoBuilder {
  static get() {
    return cy.get(`[data-testid="ChatbotDemoBuilder_root"]`);
  }

  static searchConfigPanel() {
    return cy.get(`[data-testid="ChatbotDemoBuilder_searchConfigPanelContainer"]`);
  }

  static searchConfigPanelToggleButton() {
    return cy.get(`[data-testid="ChatbotDemoBuilder_searchConfigPanelToggleButton"]`);
  }

  static searchConfigPanelTabList() {
    return ChatbotDemoBuilder.searchConfigPanel().get(`div[role="tablist"]`);
  }

  static changeDataSettingsLink() {
    return cy.get(`[data-testid="DataSettingsPanel_changeDataSettingsLink"]`);
  }
}

export class Chatbox {
  static get() {
    return cy.get(`[data-testid="Chatbox_root"]`);
  }

  static textarea() {
    return Chatbox.get().find('textarea');
  }

  static sendButton() {
    return Chatbox.get().get(`button[type=submit]`);
  }
}

export class ChatbotMessageContent {
  static get() {
    return cy.get(`[data-testid="ChatbotMessageContent_root"]`, { timeout: 30000 });
  }

  static documentsLink() {
    return cy.get(`[data-testid="ChatbotMessageContent_documentsLink"]`);
  }
}
