import { lazy, Suspense } from 'react';

import { Navigate, Route, Routes } from 'react-router-dom-v5-compat';

import CloudTeams from '@packages/cloud-teams/CloudTeams';
import componentLoader from '@packages/component-loader';
import { ErrorBoundary } from '@packages/components/ErrorBoundary';
import { RouteResources } from '@packages/layout/resources/wrappers/RouteResources';
import { profiler } from '@packages/observability/profiling';
import { ReactRouterRouteProps } from '@packages/project/common/types/router';

const ModelAPIKeysPage = lazy(() =>
  componentLoader(
    () =>
      import(
        /* webpackPrefetch: true */ /* webpackChunkName: 'model-api-keys-page' */ '@packages/ai-models/components/ModelAPIKeysPage'
      ),
    { componentName: 'model-api-keys-page' }
  )
);

const CreateModelAPIKeyPage = lazy(() =>
  componentLoader(
    () =>
      import(
        /* webpackPrefetch: true */ /* webpackChunkName: 'create-model-api-key-page' */ '@packages/ai-models/components/CreateModelAPIKeyPage'
      ),
    { componentName: 'create-model-api-key-page' }
  )
);

const UsagePage = lazy(() =>
  componentLoader(
    () =>
      import(
        /* webpackPrefetch: true */ /* webpackChunkName: 'usage-page' */ '@packages/ai-models/components/UsagePage'
      ),
    { componentName: 'usage-page' }
  )
);

const RateLimitsPage = lazy(() =>
  componentLoader(
    () =>
      import(
        /* webpackPrefetch: true */ /* webpackChunkName: 'rate-limits-page' */ '@packages/ai-models/components/RateLimitsPage'
      ),
    { componentName: 'rate-limits-page' }
  )
);

export default function Router({ dependencies, renderNotFound }: ReactRouterRouteProps) {
  const { settingsModel } = dependencies;
  const isVoyageApiServiceEnabled = settingsModel.isVoyageApiServiceEnabled();

  profiler.useMeasureRender('AiModelsProjectRouter');

  return (
    <Suspense fallback={null}>
      <ErrorBoundary metadata={{ name: 'AI Models Router: React ErrorBoundary Hit', team: CloudTeams.Search }}>
        <Routes>
          {isVoyageApiServiceEnabled && (
            <Route
              path="/modelAPIKeys"
              element={
                <RouteResources.Project>
                  <ModelAPIKeysPage />
                </RouteResources.Project>
              }
            />
          )}
          {isVoyageApiServiceEnabled && (
            <Route
              path="/modelAPIKeys/create"
              element={
                <RouteResources.Project>
                  <CreateModelAPIKeyPage />
                </RouteResources.Project>
              }
            />
          )}
          {isVoyageApiServiceEnabled && (
            <Route
              path="/usage"
              element={
                <RouteResources.Project>
                  <UsagePage />
                </RouteResources.Project>
              }
            />
          )}
          {isVoyageApiServiceEnabled && (
            <Route
              path="/rateLimits"
              element={
                <RouteResources.Project>
                  <RateLimitsPage />
                </RouteResources.Project>
              }
            />
          )}
          {isVoyageApiServiceEnabled && <Route path="/" element={<Navigate to="./modelAPIKeys" replace />} />}
          <Route
            path="*"
            element={<RouteResources.DefinedElsewhere>{renderNotFound()}</RouteResources.DefinedElsewhere>}
          />
        </Routes>
      </ErrorBoundary>
    </Suspense>
  );
}
