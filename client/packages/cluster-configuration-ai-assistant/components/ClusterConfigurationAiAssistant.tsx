import React, { useEffect, useMemo, useState } from 'react';

import styled from '@emotion/styled';
import { DisplayMode, DrawerLayout, DrawerLayoutProps, useDrawerToolbarContext } from '@leafygreen-ui/drawer';
import { GuideCue } from '@leafygreen-ui/guide-cue';
import InlineDefinition from '@leafygreen-ui/inline-definition';
import { palette } from '@leafygreen-ui/palette';
import { spacing } from '@leafygreen-ui/tokens';
import { DisclaimerText } from '@lg-chat/chat-disclaimer';
import { InputBar } from '@lg-chat/input-bar';
import { LeafyGreenChatProvider } from '@lg-chat/leafygreen-chat-provider';
import { Message, MessageSourceType } from '@lg-chat/message';
import { MessageFeed } from '@lg-chat/message-feed';
import { MessagePrompt, MessagePrompts } from '@lg-chat/message-prompts';
import { State, SuggestedActions, SuggestedActionsProps } from '@lg-chat/suggestions';

import { ClusterConfigurationForClusterBuilder } from '@packages/types/clusterConfiguration';
import { BillingEstimate } from '@packages/types/nds/billingEstimate';
import { ClusterDescription } from '@packages/types/nds/clusterDescription';
import { CloudTeams } from '@packages/types/observability';

import { ClusterConfigToolResult } from '@packages/ai-assistant/tools/clusterConfigTool';
import { ChatMessage, useAiChat } from '@packages/cluster-configuration-ai-assistant/hooks/useAiChat';
import {
  transformToSuggestedActionsParameters,
  updateSuggestedActionsWithState,
} from '@packages/cluster-configuration-ai-assistant/utils/clusterConfigParamUtils';
import {
  extractClusterConfigSummary,
  transformSummaryToSuggestedActionsParameters,
} from '@packages/cluster-configuration-ai-assistant/utils/clusterConfigSummaryUtils';
import { ClusterConfigurationParameters } from '@packages/cluster-configuration/parameterizedTransformationUtility';
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import { sendError } from '@packages/observability';

const StyledGuideCue = styled(GuideCue)`
  width: 372px;
  max-width: 372px;
  padding: ${spacing[400]}px;
`;

const StyledMessagePrompt = styled(MessagePrompt)`
  width: 100%;

  &:not(:last-child) {
    margin-bottom: ${spacing[200]}px !important;
  }

  &:last-child {
    margin-bottom: 0 !important;
  }
`;

const ChatContainer = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;

  > div {
    height: 100%;
  }
`;

const getAnalyticsContext = (isEdit: boolean) =>
  isEdit ? 'Cluster AI Assistant - Edit' : 'Cluster AI Assistant - Create';

export interface ClusterConfigurationAiAssistantProps {
  children: React.ReactNode;
  userId: string;
  clusterConfiguration?: ClusterConfigurationForClusterBuilder;
  originalCluster: ClusterDescription | null;
  billingEstimate?: BillingEstimate;
  isEdit: boolean;
  createClusterButtonRef?: React.RefObject<HTMLDivElement>;
  onApplySuggestedParameters: (parameters: ClusterConfigurationParameters) => void;
  clusterDescriptions?: Array<ClusterDescription>;
}

export function ClusterConfigurationAiAssistant({
  children,
  userId,
  clusterConfiguration,
  originalCluster,
  billingEstimate,
  isEdit,
  createClusterButtonRef,
  onApplySuggestedParameters,
  clusterDescriptions,
}: ClusterConfigurationAiAssistantProps) {
  const [showCreateClusterGuideCue, setShowCreateClusterGuideCue] = useState(false);
  const [hasBanner, setHasBanner] = useState(false);

  // Check for banner presence
  useEffect(() => {
    const banner = document.getElementById('group-status-banners');
    const hasContent = banner && banner.children.length > 0;
    setHasBanner(!!hasContent);
  }, []);

  const DrawerToolbarContext = () => {
    const { openDrawer } = useDrawerToolbarContext();

    useEffect(() => {
      openDrawer('Cluster Assistant');
    }, [openDrawer]);

    return null;
  };

  // Memoize the cluster config summary to prevent unnecessary recalculations
  const originalClusterConfigSummary = useMemo(() => {
    // Only extract summary if originalCluster exists
    if (!originalCluster) {
      return null;
    }
    return extractClusterConfigSummary(originalCluster, undefined, undefined, undefined);
  }, [originalCluster]);

  // Memoize the current cluster config summary from the current configuration
  const currentClusterConfigSummary = useMemo(() => {
    // Only extract summary if clusterConfiguration exists
    if (!clusterConfiguration) {
      return null;
    }
    return extractClusterConfigSummary(
      clusterConfiguration.clusterDescription,
      clusterConfiguration.searchDeploymentSpec,
      clusterConfiguration.tags,
      billingEstimate
    );
  }, [clusterConfiguration, billingEstimate]);

  const DrawerContent = useMemo(() => {
    const InternalDrawerContent = () => {
      const { messages, isLoading, error, sendMessage } = useAiChat({
        userId,
        originalClusterConfigSummary: originalClusterConfigSummary || undefined,
        currentClusterConfigSummary: currentClusterConfigSummary || undefined,
        isEdit,
        billingEstimate,
        clusterDescriptions,
      });
      const [showPromptExamples, setShowPromptExamples] = useState(true);
      const [promptSelected, setPromptSelected] = useState<number | null>();
      const [suggestedActionsProps, setSuggestedActionsProps] = useState<SuggestedActionsProps | null>(null);

      // Create the initial starting message
      const messageBody = isEdit
        ? 'Welcome! This is your current configuration, do you want to change anything?'
        : 'Welcome! Provide some information about what you need to build and we can suggest a cluster for you.';

      const initialStartingMessage = (
        <Message isSender={false} messageBody={messageBody}>
          {isEdit && originalClusterConfigSummary && (
            <SuggestedActions
              state={State.Unset}
              configurationParameters={transformSummaryToSuggestedActionsParameters(originalClusterConfigSummary)}
              onClickApply={() => {}} // This is a read-only display, so onClickApply is a no-op
            />
          )}
        </Message>
      );

      // Memoize the messages list to prevent re-rendering when parent state changes
      const memoizedMessages = useMemo(() => {
        return messages.map((message: ChatMessage) => (
          <Message
            key={message.id}
            messageBody={message.messageBody}
            isSender={message.role === 'user'}
            sourceType={MessageSourceType.Markdown}
          >
            {message.children}
          </Message>
        ));
      }, [messages]);

      // Extract actual cluster configuration from assistant messages
      const latestAssistantMessage = messages.filter((msg) => msg.role === 'assistant').pop();
      const toolResults = latestAssistantMessage?.toolResults;
      const clusterConfigFromTool: ClusterConfigToolResult | undefined = toolResults?.clusterConfigTool;

      // Only create suggestions if we have a successful tool result
      const suggestedActionsParameters = useMemo(() => {
        return clusterConfigFromTool?.success
          ? transformToSuggestedActionsParameters(clusterConfigFromTool.clusterConfigurationParameters)
          : null;
      }, [clusterConfigFromTool?.success, clusterConfigFromTool?.clusterConfigurationParameters]);

      // Update suggestions panel when we get actual cluster configuration
      useEffect(() => {
        if (clusterConfigFromTool?.success && suggestedActionsParameters && !isLoading) {
          // TypeScript now knows clusterConfigurationParameters is not null when success is true
          const validClusterConfig = clusterConfigFromTool.clusterConfigurationParameters;
          setSuggestedActionsProps({
            state: State.Apply,
            configurationParameters: suggestedActionsParameters,
            onClickApply: () => {
              try {
                analytics.track(SEGMENT_EVENTS.BUTTON_CLICKED, {
                  action: 'Applied AI Cluster Configuration Suggestion',
                  configuration: JSON.stringify(suggestedActionsParameters),
                  context: getAnalyticsContext(isEdit),
                });

                onApplySuggestedParameters(validClusterConfig);

                setSuggestedActionsProps((prev) =>
                  updateSuggestedActionsWithState(State.Success, suggestedActionsParameters)(prev)
                );

                if (createClusterButtonRef?.current) {
                  setShowCreateClusterGuideCue(true);
                }

                analytics.track(SEGMENT_EVENTS.CONFIGURATION_APPLICATION_SUCCESS, {
                  configuration: JSON.stringify(suggestedActionsParameters),
                  context: getAnalyticsContext(isEdit),
                });
              } catch (applyError) {
                setSuggestedActionsProps((prev) =>
                  updateSuggestedActionsWithState(State.Error, suggestedActionsParameters)(prev)
                );

                sendError({
                  error: applyError,
                  team: CloudTeams.AtlasGrowth,
                  extras: {
                    configurationParameters: validClusterConfig,
                  },
                });

                analytics.track(SEGMENT_EVENTS.CONFIGURATION_APPLICATION_FAILED, {
                  configuration: JSON.stringify(suggestedActionsParameters),
                  error: applyError instanceof Error ? applyError.message : 'Unknown error',
                  context: getAnalyticsContext(isEdit),
                });
              }
            },
          });
        } else if (!clusterConfigFromTool?.success || !suggestedActionsParameters) {
          // Clear suggestions when there are no valid tool results
          setSuggestedActionsProps(null);
        }
      }, [clusterConfigFromTool, isLoading, suggestedActionsParameters]);

      const promptExamples = isEdit
        ? [
            "Suggest a cluster setup as I'm expanding to India and expect 20,000 new users and the associated data.",
            'My cluster costs more than expected. Is there a setup that meets our data needs but stays under $500/mo?',
            "I've had outages and need better availability. Can you suggest a cluster setup that reduces downtime?",
          ]
        : [
            "I'm setting up a development cluster for a restaurant rating app with 100 users in NYC. What's the best cluster configuration?",
            "I'm looking to migrate an existing project to Atlas. We currently have 100GB of data that we need to move over. The project is currently deployed in Europe and we want to maintain that. How should I configure my cluster?",
            "I'm setting up a production cluster for my current project. I have a budget of $1,000/month. What do you recommend?",
          ];

      const handleMessageSend = async (messageText: string) => {
        try {
          showPromptExamples && setShowPromptExamples(false);
          setSuggestedActionsProps(null);
          await sendMessage(messageText);
        } catch (err) {
          console.error('Failed to send message:', err);
        }
      };

      const handlePromptClick = async (promptText: string, promptIndex: number) => {
        analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          action: 'Suggested Prompt Clicked',
          context: getAnalyticsContext(isEdit),
          suggested_prompt: promptText,
        });

        setShowPromptExamples(false);
        setPromptSelected(promptIndex);
        await handleMessageSend(promptText);
      };

      return (
        <ChatContainer>
          <LeafyGreenChatProvider assistantName="Cluster Assistant">
            <MessageFeed style={{ height: '100%' }}>
              <div style={{ flex: '1' }} />
              <DisclaimerText title="Terms of Use" style={{ textAlign: 'center' }}>
                This feature is powered by generative AI. See our{' '}
                <a
                  target="_blank"
                  href="https://www.mongodb.com/docs/generative-ai-faq/"
                  style={{ textDecoration: 'underline' }}
                >
                  FAQ
                </a>{' '}
                for more info.{' '}
                <InlineDefinition
                  style={{ textAlign: 'left' }}
                  align="bottom"
                  definition={
                    'The Cluster Assistant is powered by generative AI and offers suggestions tailored to your inputs. Outputs are dynamic and may change based on the current state of your cluster and each specific input.'
                  }
                >
                  Please review outputs carefully.
                </InlineDefinition>
              </DisclaimerText>
              <div style={{ flex: '1' }} />

              {initialStartingMessage}

              {showPromptExamples && (
                <MessagePrompts label="Suggested Prompts" style={{ width: '100%', margin: '0px' }}>
                  {promptExamples.map((promptText, index) => (
                    <StyledMessagePrompt
                      key={index}
                      selected={promptSelected === index}
                      onClick={() => handlePromptClick(promptText, index)}
                    >
                      {promptText}
                    </StyledMessagePrompt>
                  ))}
                </MessagePrompts>
              )}

              {/* Render chat messages */}
              {memoizedMessages}

              {/* Only show suggested actions when we have tool results */}
              {suggestedActionsProps && <SuggestedActions {...suggestedActionsProps} />}
              {isLoading && clusterConfigFromTool && !suggestedActionsProps && (
                <div
                  style={{
                    padding: '16px',
                    textAlign: 'center',
                    fontStyle: 'italic',
                    color: palette.gray.dark2,
                  }}
                >
                  Setting up apply action...
                </div>
              )}

              {/* Display error message if there's an error*/}
              {error && <div style={{ color: 'red', padding: '8px', margin: '8px 0' }}>Error: {error}</div>}

              <InputBar
                onMessageSend={handleMessageSend}
                shouldRenderGradient={false}
                disabled={isLoading}
                data-testid="cluster-ai-assistant-input-bar"
              />
            </MessageFeed>
          </LeafyGreenChatProvider>
        </ChatContainer>
      );
    };

    return <InternalDrawerContent />;
  }, [
    userId,
    isEdit,
    onApplySuggestedParameters,
    createClusterButtonRef,
    originalClusterConfigSummary,
    currentClusterConfigSummary,
    billingEstimate,
    clusterDescriptions,
  ]);

  const DRAWER_TOOLBAR_DATA: DrawerLayoutProps['toolbarData'] = useMemo(
    () => [
      {
        id: 'Cluster Assistant',
        label: 'Cluster Assistant',
        content: DrawerContent,
        title: 'Cluster Assistant',
        glyph: 'Sparkle',
        scrollable: false,
        onClick: () => {
          analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
            action: 'AI Assistant Opened',
            context: getAnalyticsContext(isEdit),
          });
        },
      },
    ],
    [DrawerContent, isEdit]
  );

  return (
    <div style={{ height: hasBanner ? 'calc(100vh - 174px)' : 'calc(100vh - 130px)' }}>
      <DrawerLayout
        toolbarData={DRAWER_TOOLBAR_DATA}
        displayMode={DisplayMode.Embedded}
        onClose={() => {
          analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
            action: 'AI Assistant Closed',
            context: getAnalyticsContext(isEdit),
          });
        }}
      >
        <DrawerToolbarContext />
        {children}
        {createClusterButtonRef?.current && (
          <StyledGuideCue
            open={showCreateClusterGuideCue}
            setOpen={setShowCreateClusterGuideCue}
            refEl={createClusterButtonRef}
            title="Happy with your cluster configuration?"
            numberOfSteps={1}
            currentStep={1}
            buttonText="Done"
            tooltipAlign="top"
            tooltipJustify="end"
            onPrimaryButtonClick={() => {
              setShowCreateClusterGuideCue(false);
            }}
          >
            Click here to create a cluster!
          </StyledGuideCue>
        )}
      </DrawerLayout>
    </div>
  );
}

// Memoize the component to prevent re-renders when parent state changes
const MemoizedClusterConfigurationAiAssistant = React.memo(ClusterConfigurationAiAssistant, (prevProps, nextProps) => {
  // Only re-render if userId, onApplySuggestedParameters, createClusterButtonRef, billingEstimate, or clusterDescriptions change
  return (
    prevProps.userId === nextProps.userId &&
    prevProps.onApplySuggestedParameters === nextProps.onApplySuggestedParameters &&
    prevProps.createClusterButtonRef === nextProps.createClusterButtonRef &&
    prevProps.billingEstimate === nextProps.billingEstimate &&
    prevProps.clusterDescriptions === nextProps.clusterDescriptions
  );
});

export default MemoizedClusterConfigurationAiAssistant;
