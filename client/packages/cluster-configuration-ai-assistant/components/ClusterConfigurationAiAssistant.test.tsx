import { ClusterConfigurationForClusterBuilder } from '@packages/types/clusterConfiguration';
import { BillingEstimate } from '@packages/types/nds/billingEstimate';
import { ClusterDescription } from '@packages/types/nds/clusterDescription';
import { BackingCloudProvider, DedicatedInstanceSize } from '@packages/types/nds/provider';
import { AWSRegionName, RegionName } from '@packages/types/nds/region';
import { CloudTeams } from '@packages/types/observability';
import { Tag, TagVisibility } from '@packages/types/resourceTagging';
import { SearchDeploymentSpec } from '@packages/types/search/decoupled/deployment';

import {
  ClusterConfigurationAiAssistant,
  ClusterConfigurationAiAssistantProps,
} from '@packages/cluster-configuration-ai-assistant/components/ClusterConfigurationAiAssistant';
import { ChatMessage, useAiChat } from '@packages/cluster-configuration-ai-assistant/hooks/useAiChat';
import { transformToSuggestedActionsParameters } from '@packages/cluster-configuration-ai-assistant/utils/clusterConfigParamUtils';
import clusterFixtures from '@packages/common/fixtures/clusterFixtures';
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import { sendError } from '@packages/observability';
import { fireEvent, render, screen, waitFor } from '@packages/react-testing-library';
import getTypedMockOf from '@packages/test-utils/getTypedMockOf';

// Get the mocked functions
const mockedTransformToSuggestedActionsParameters = transformToSuggestedActionsParameters as jest.MockedFunction<
  typeof transformToSuggestedActionsParameters
>;

const mockSendMessage = jest.fn();

jest.mock('@packages/cluster-configuration-ai-assistant/hooks/useAiChat', () => ({
  useAiChat: jest.fn<any, any>(() => ({
    messages: [] as Array<ChatMessage>,
    isLoading: false,
    error: null,
    sendMessage: mockSendMessage,
  })),
}));

// Get the mocked function after the mock is set up
let mockUseAiChat: jest.MockedFunction<any>;

jest.mock('@packages/cluster-configuration-ai-assistant/utils/clusterConfigParamUtils', () => ({
  transformToSuggestedActionsParameters: jest.fn(),
  transformToSuccessSuggestedActionsParameters: jest.fn(),
  transformToErrorSuggestedActionsParameters: jest.fn(),
  updateSuggestedActionsWithState: jest.fn(
    (state, suggestedActionsParameters) => (suggestedActionsProps: any) =>
      suggestedActionsProps
        ? {
            ...suggestedActionsProps,
            state,
            configurationParameters: suggestedActionsParameters.map((param: any) => ({ ...param, state })),
          }
        : null
  ),
}));

jest.mock('@packages/cluster-configuration-ai-assistant/utils/clusterConfigSummaryUtils', () => ({
  extractClusterConfigSummary: jest.fn((clusterDescription, searchDeploymentSpec, tags, billingEstimate) => ({
    costEstimate: billingEstimate?.hourlyEstimateDollars ? `${billingEstimate.hourlyEstimateDollars}/hour` : undefined,
    instanceSize: 'M30',
    electableNodes: [],
    readOnlyNodes: [],
    analyticsNodes: [],
    region: 'US_EAST_1',
    provider: 'AWS',
    diskSizeGB: 30,
    numShards: 1,
    name: clusterDescription?.name || 'test-cluster',
    tags: tags || [{ key: 'env', value: 'prod' }],
    searchDeploymentSpec: searchDeploymentSpec
      ? {
          nodeCount: searchDeploymentSpec.nodeCount || 0,
          instanceSize: searchDeploymentSpec.instanceSize || '',
        }
      : undefined,
  })),
  transformSummaryToSuggestedActionsParameters: jest.fn(() => [
    { key: 'Cluster Tier', value: 'M30' },
    { key: 'Provider', value: 'AWS' },
    { key: 'Region', value: 'US_EAST_1' },
    { key: 'Cluster Name', value: 'test-cluster' },
    { key: 'Tags', value: JSON.stringify([{ key: 'env', value: 'prod' }]) },
  ]),
}));

jest.mock('@packages/common/utils/segmentAnalytics', () => ({
  __esModule: true,
  default: {
    track: jest.fn(),
  },
  SEGMENT_EVENTS: {
    BUTTON_CLICKED: 'Button Clicked',
    UX_ACTION_PERFORMED: 'UX Action Performed',
    CONFIGURATION_APPLICATION_SUCCESS: 'Configuration Applied Successfully',
    CONFIGURATION_APPLICATION_FAILED: 'Configuration Application Failed',
  },
}));

jest.mock('@packages/observability', () => ({
  sendError: jest.fn(),
}));

jest.mock('@lg-chat/suggestions', () => ({
  ...jest.requireActual('@lg-chat/suggestions'),
  SuggestedActions: ({ onClickApply, state, configurationParameters }: any) => (
    <div data-testid="suggested-actions" data-state={state}>
      <div data-testid="config-params">{JSON.stringify(configurationParameters)}</div>
      <button data-testid="apply-button" onClick={onClickApply}>
        Apply Configuration
      </button>
    </div>
  ),
  State: {
    Unset: 'unset',
    Apply: 'apply',
    Success: 'success',
    Error: 'error',
  },
}));

jest.mock('@leafygreen-ui/drawer', () => ({
  ...jest.requireActual('@leafygreen-ui/drawer'),
  useDrawerToolbarContext: () => ({
    openDrawer: jest.fn(),
  }),
  DrawerLayout: ({ children, toolbarData, onClose }: any) => (
    <div data-testid="drawer-layout" data-toolbar-count={toolbarData?.length}>
      {children}
      {/* Render the first toolbar item's content for testing */}
      {toolbarData?.[0] && (
        <div data-testid="drawer-content">
          {toolbarData[0].content}
          <button data-testid="toolbar-open-button" onClick={toolbarData[0].onClick}>
            Open
          </button>
        </div>
      )}
      <button data-testid="drawer-close-button" onClick={onClose}>
        Close
      </button>
    </div>
  ),
}));

jest.mock('@lg-chat/chat-disclaimer', () => ({
  DisclaimerText: ({ children, title }: { children: React.ReactNode; title: string }) => (
    <div data-testid="disclaimer-text" data-title={title}>
      {children}
    </div>
  ),
}));

jest.mock('@lg-chat/input-bar', () => ({
  InputBar: ({ onMessageSend, style }: any) => (
    <div data-testid="input-bar" style={style}>
      <input data-testid="message-input" placeholder="Type a message..." />
      <button data-testid="send-button" onClick={() => onMessageSend('test message')}>
        Send
      </button>
    </div>
  ),
}));

jest.mock('@lg-chat/leafygreen-chat-provider', () => ({
  LeafyGreenChatProvider: ({ children, variant }: any) => (
    <div data-testid="chat-provider" data-variant={variant}>
      {children}
    </div>
  ),
  Variant: {
    Compact: 'compact',
  },
}));

jest.mock('@lg-chat/message', () => ({
  Message: ({ messageBody, ...props }: any) => (
    <div data-testid="message" {...props}>
      {messageBody}
    </div>
  ),
  MessageSourceType: {
    Markdown: 'markdown',
  },
}));

jest.mock('@lg-chat/message-feed', () => ({
  MessageFeed: ({ children, style }: any) => (
    <div data-testid="message-feed" style={style}>
      {children}
    </div>
  ),
}));

jest.mock('@lg-chat/message-prompts', () => ({
  MessagePrompts: ({ children, label, style }: any) => (
    <div data-testid="message-prompts" data-label={label} style={style}>
      {children}
    </div>
  ),
  MessagePrompt: ({ children, selected, onClick, style }: any) => (
    <button data-testid="message-prompt" data-selected={selected} onClick={onClick} style={style}>
      {children}
    </button>
  ),
}));

const mockOriginalCluster: ClusterDescription = clusterFixtures.getAwsDefaultClusterDescription_asJSON(
  'original-cluster',
  'M10'
);

const mockClusterDescription: ClusterDescription = clusterFixtures.getAwsDefaultClusterDescription_asJSON(
  'test-cluster',
  'M30'
);

const mockTags: Array<Tag> = [
  { key: 'env', value: 'prod', visibility: TagVisibility.PUBLIC },
  { key: 'team', value: 'backend', visibility: TagVisibility.PUBLIC },
];

const mockSearchDeploymentSpec: SearchDeploymentSpec = {
  nodeCount: 2,
  instanceSize: 'M30',
};

const mockClusterConfiguration: ClusterConfigurationForClusterBuilder = {
  clusterDescription: mockClusterDescription,
  processArgs: clusterFixtures.getDefaultProcessArgs(),
  tags: mockTags,
  searchDeploymentSpec: mockSearchDeploymentSpec,
};

const mockBillingEstimate: BillingEstimate = {
  hourlyEstimateDollars: '0.08',
  monthlyEstimateDollars: '58.40',
  snapshotBackupEstimateDollars: '0.00',
};

const mockSuggestedConfigurationParameters = {
  instanceSize: DedicatedInstanceSize.M30,
  cloudProvider: BackingCloudProvider.AWS,
  region: AWSRegionName.US_EAST_1,
  clusterName: 'suggested-cluster',
  tags: [{ key: 'env', value: 'dev', visibility: TagVisibility.PUBLIC }],
};

const mockSuggestedActionsParameters = [
  { key: 'Cluster Tier', value: 'M30' },
  { key: 'Provider', value: 'AWS' },
  { key: 'Region', value: 'US_EAST_1' },
  { key: 'Cluster Name', value: 'suggested-cluster' },
  { key: 'Tags', value: JSON.stringify([{ key: 'env', value: 'dev' }]) },
];

const mockOnApplySuggestedParameters = jest.fn();

const mockUserId = 'cluster-config-user';

const mockClusterDescriptions: Array<ClusterDescription> = [
  clusterFixtures.getAwsDefaultClusterDescription_asJSON('other-cluster-1', 'M10'),
  clusterFixtures.getAwsDefaultClusterDescription_asJSON('other-cluster-2', 'M20'),
];

describe('ClusterConfigurationAiAssistant', () => {
  const defaultProps: ClusterConfigurationAiAssistantProps = {
    originalCluster: mockOriginalCluster,
    clusterConfiguration: mockClusterConfiguration,
    billingEstimate: mockBillingEstimate,
    onApplySuggestedParameters: mockOnApplySuggestedParameters,
    children: <div data-testid="page-content">Page Content</div>,
    isEdit: false,
    userId: mockUserId,
    clusterDescriptions: mockClusterDescriptions,
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Get the mocked function
    mockUseAiChat = jest.mocked(useAiChat);

    mockedTransformToSuggestedActionsParameters.mockReturnValue(mockSuggestedActionsParameters);
    mockSendMessage.mockResolvedValue(undefined);

    // Reset the useAiChat mock to default state
    mockUseAiChat.mockReturnValue({
      messages: [],
      isLoading: false,
      error: null,
      sendMessage: mockSendMessage,
    });

    // Ensure the mock returns the expected value
    mockedTransformToSuggestedActionsParameters.mockImplementation(() => {
      return mockSuggestedActionsParameters;
    });
  });

  describe('Component Structure', () => {
    it('accepts and renders children', () => {
      const customChildren = <div data-testid="custom-children">Custom Content</div>;

      render(<ClusterConfigurationAiAssistant {...defaultProps}>{customChildren}</ClusterConfigurationAiAssistant>);

      expect(screen.getByTestId('custom-children')).toBeInTheDocument();
      expect(screen.getByText('Custom Content')).toBeInTheDocument();
    });

    it('passes clusterDescriptions to useAiChat', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      expect(mockUseAiChat).toHaveBeenCalledWith(
        expect.objectContaining({
          clusterDescriptions: mockClusterDescriptions,
          billingEstimate: mockBillingEstimate,
        })
      );
    });

    it('creates toolbar data with drawer content', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      const drawerLayout = screen.getByTestId('drawer-layout');
      expect(drawerLayout).toHaveAttribute('data-toolbar-count', '1');

      // The drawer content should be rendered
      expect(screen.getByTestId('drawer-content')).toBeInTheDocument();
    });
  });

  describe('Drawer Interactions Analytics', () => {
    it('tracks analytics event when AI Assistant is opened', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      const openButton = screen.getByTestId('toolbar-open-button');
      fireEvent.click(openButton);

      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        action: 'AI Assistant Opened',
        context: 'Cluster AI Assistant - Create',
      });
    });

    it('tracks analytics event with edit context when AI Assistant is opened in edit mode', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} isEdit={true} />);

      const openButton = screen.getByTestId('toolbar-open-button');
      fireEvent.click(openButton);

      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        action: 'AI Assistant Opened',
        context: 'Cluster AI Assistant - Edit',
      });
    });

    it('tracks analytics event when AI Assistant is closed', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      const closeButton = screen.getByTestId('drawer-close-button');
      fireEvent.click(closeButton);

      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        action: 'AI Assistant Closed',
        context: 'Cluster AI Assistant - Create',
      });
    });

    it('tracks analytics event with edit context when AI Assistant is closed in edit mode', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} isEdit={true} />);

      const closeButton = screen.getByTestId('drawer-close-button');
      fireEvent.click(closeButton);

      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        action: 'AI Assistant Closed',
        context: 'Cluster AI Assistant - Edit',
      });
    });

    it('tracks analytics event when suggested prompt is clicked in create mode', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      const promptButtons = screen.getAllByTestId('message-prompt');
      fireEvent.click(promptButtons[0]);

      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        action: 'Suggested Prompt Clicked',
        context: 'Cluster AI Assistant - Create',
        suggested_prompt:
          "I'm setting up a development cluster for a restaurant rating app with 100 users in NYC. What's the best cluster configuration?",
      });
    });

    it('tracks analytics event when suggested prompt is clicked in edit mode', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} isEdit={true} />);

      const promptButtons = screen.getAllByTestId('message-prompt');
      fireEvent.click(promptButtons[0]);

      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        action: 'Suggested Prompt Clicked',
        context: 'Cluster AI Assistant - Edit',
        suggested_prompt:
          "Suggest a cluster setup as I'm expanding to India and expect 20,000 new users and the associated data.",
      });
    });
  });

  describe('Drawer Content', () => {
    it('renders the chat interface components', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      // Test that the drawer content contains the expected text content
      expect(screen.getByText(/Welcome!/)).toBeInTheDocument();
      expect(screen.getByText(/Provide some information about what you need to build/)).toBeInTheDocument();
      expect(screen.getByTestId('disclaimer-text')).toHaveAttribute('data-title', 'Terms of Use');
      expect(screen.getByText(/This feature is powered by generative AI/)).toBeInTheDocument();
    });

    it('renders prompt examples', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      expect(screen.getByTestId('message-prompts')).toHaveAttribute('data-label', 'Suggested Prompts');
      expect(screen.getByText(/development cluster for a restaurant rating app/)).toBeInTheDocument();
      expect(screen.getByText(/migrate an existing project to Atlas/)).toBeInTheDocument();
      expect(screen.getByText(/I have a budget of \$1,000\/month/)).toBeInTheDocument();
    });

    it('renders mocked chat components', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      // Verify that our mocked components are rendering
      expect(screen.getByTestId('chat-provider')).toBeInTheDocument();
      expect(screen.getByTestId('message-feed')).toBeInTheDocument();
      expect(screen.getByTestId('disclaimer-text')).toBeInTheDocument();
      expect(screen.getByTestId('input-bar')).toBeInTheDocument();
      expect(screen.getByTestId('message-prompts')).toBeInTheDocument();
    });

    it('if editing, show different components', () => {
      // Mock with tool results directly
      mockUseAiChat.mockReturnValue({
        messages: [
          {
            id: 'test-message',
            role: 'assistant' as const,
            messageBody: 'Here is your cluster configuration',
            toolResults: {
              clusterConfigTool: {
                success: true,
                message: 'Cluster configuration is valid',
                clusterConfigurationParameters: mockSuggestedConfigurationParameters,
                summary: 'Validated cluster configuration',
              },
            },
          },
        ] as Array<ChatMessage>,
        isLoading: false,
        error: null,
        sendMessage: mockSendMessage,
      });

      render(<ClusterConfigurationAiAssistant {...defaultProps} isEdit={true} />);

      expect(
        screen.getByText(/This is your current configuration, do you want to change anything?/)
      ).toBeInTheDocument();

      // Different suggested prompts for edit mode should be visible
      expect(screen.getByText(/expanding to India and expect 20,000 new users/)).toBeInTheDocument();
      expect(screen.getByText(/cluster costs more than expected/)).toBeInTheDocument();
      expect(screen.getByText(/had outages and need better availability/)).toBeInTheDocument();

      // Only the latest SuggestedActions should appear - the new suggestion takes precedence over current config
      const suggestedActions = screen.getByTestId('suggested-actions');
      expect(suggestedActions).toHaveAttribute('data-state', 'apply'); // New suggestion
    });

    it('should not show suggested actions when there are no tool results in edit mode', () => {
      // Mock AI chat with no tool results
      mockUseAiChat.mockReturnValue({
        messages: [],
        isLoading: false,
        error: null,
        sendMessage: mockSendMessage,
      });

      render(<ClusterConfigurationAiAssistant {...defaultProps} isEdit={true} />);

      expect(
        screen.getByText(/This is your current configuration, do you want to change anything?/)
      ).toBeInTheDocument();
      // No SuggestedActions should be shown when there are no tool results, even in edit mode
      expect(screen.queryByTestId('suggested-actions')).not.toBeInTheDocument();
    });

    it('should not show suggested actions when not in edit mode and there are no tool results', () => {
      // Mock AI chat with no tool results
      mockUseAiChat.mockReturnValue({
        messages: [],
        isLoading: false,
        error: null,
        sendMessage: mockSendMessage,
      });

      render(<ClusterConfigurationAiAssistant {...defaultProps} isEdit={false} />);

      // No SuggestedActions should be shown without tool results when not in edit mode
      expect(screen.queryByTestId('suggested-actions')).not.toBeInTheDocument();
    });

    it('should handle null originalCluster', () => {
      render(<ClusterConfigurationAiAssistant {...defaultProps} originalCluster={null} />);

      expect(screen.getByText(/Welcome!/)).toBeInTheDocument();
      expect(screen.getByText(/Provide some information about what you need to build/)).toBeInTheDocument();
    });
  });

  describe('Apply Configuration', () => {
    const sendErrorMock = getTypedMockOf(sendError);

    it('should successfully apply configuration and show success state', async () => {
      // Mock with tool results directly
      mockUseAiChat.mockReturnValue({
        messages: [
          {
            id: 'test-message',
            role: 'assistant' as const,
            messageBody: 'Here is your cluster configuration',
            toolResults: {
              clusterConfigTool: {
                success: true,
                message: 'Cluster configuration is valid',
                clusterConfigurationParameters: mockSuggestedConfigurationParameters,
                summary: 'Validated cluster configuration',
              },
            },
          },
        ] as Array<ChatMessage>,
        isLoading: false,
        error: null,
        sendMessage: mockSendMessage,
      });

      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      // SuggestedActions should be rendered with apply button
      const suggestedActionsElement = screen.getByTestId('suggested-actions');
      expect(suggestedActionsElement).toBeInTheDocument();
      expect(suggestedActionsElement).toHaveAttribute('data-state', 'apply');
      expect(screen.getByTestId('apply-button')).toBeInTheDocument();

      const applyButton = screen.getByTestId('apply-button');
      fireEvent.click(applyButton);

      expect(mockOnApplySuggestedParameters).toHaveBeenCalledWith(mockSuggestedConfigurationParameters);

      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
        action: 'Applied AI Cluster Configuration Suggestion',
        configuration: JSON.stringify(mockSuggestedActionsParameters),
        context: 'Cluster AI Assistant - Create',
      });

      // Wait for the state to update to success
      await waitFor(() => {
        expect(screen.getByTestId('suggested-actions')).toHaveAttribute('data-state', 'success');
      });

      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.CONFIGURATION_APPLICATION_SUCCESS, {
        configuration: JSON.stringify(mockSuggestedActionsParameters),
        context: 'Cluster AI Assistant - Create',
      });
      expect(sendErrorMock).not.toHaveBeenCalled();
    });

    it('should handle errors when applying invalid parameters and show error state', async () => {
      const invalidConfigParameters = {
        ...mockSuggestedConfigurationParameters,
        region: 'FAKE_PLACE' as RegionName,
      };
      const invalidSuggestedActionsParameters = transformToSuggestedActionsParameters(invalidConfigParameters);

      // Mock error to be thrown when applying invalid parameters
      mockOnApplySuggestedParameters.mockImplementation(() => {
        throw new Error('Cannot find region view for FAKE_PLACE');
      });

      // Mock with invalid tool results directly
      mockUseAiChat.mockReturnValue({
        messages: [
          {
            id: 'test-message',
            role: 'assistant' as const,
            messageBody: 'Here is your cluster configuration',
            toolResults: {
              clusterConfigTool: {
                success: true,
                message: 'Cluster configuration is valid',
                clusterConfigurationParameters: invalidConfigParameters,
                summary: 'Validated cluster configuration',
              },
            },
          },
        ] as Array<ChatMessage>,
        isLoading: false,
        error: null,
        sendMessage: mockSendMessage,
      });

      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      // SuggestedActions should be rendered with apply button
      const suggestedActionsElement = screen.getByTestId('suggested-actions');
      expect(suggestedActionsElement).toBeInTheDocument();
      expect(suggestedActionsElement).toHaveAttribute('data-state', 'apply');
      expect(screen.getByTestId('apply-button')).toBeInTheDocument();

      const applyButton = screen.getByTestId('apply-button');
      fireEvent.click(applyButton);

      expect(mockOnApplySuggestedParameters).toHaveBeenCalledWith(invalidConfigParameters);

      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
        action: 'Applied AI Cluster Configuration Suggestion',
        configuration: JSON.stringify(invalidSuggestedActionsParameters),
        context: 'Cluster AI Assistant - Create',
      });

      // Wait for the state to update to error
      await waitFor(() => {
        expect(screen.getByTestId('suggested-actions')).toHaveAttribute('data-state', 'error');
      });

      expect(analytics.track).toHaveBeenCalledWith(SEGMENT_EVENTS.CONFIGURATION_APPLICATION_FAILED, {
        configuration: JSON.stringify(invalidSuggestedActionsParameters),
        error: 'Cannot find region view for FAKE_PLACE',
        context: 'Cluster AI Assistant - Create',
      });
      expect(sendErrorMock).toHaveBeenCalledWith({
        error: new Error('Cannot find region view for FAKE_PLACE'),
        team: CloudTeams.AtlasGrowth,
        extras: {
          configurationParameters: invalidConfigParameters,
        },
      });
    });
  });

  describe('Conversation Flow Tests', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should handle multiple conversation turns correctly', async () => {
      // Mock the hook to return messages that simulate a conversation
      const conversationMessages: Array<ChatMessage> = [
        {
          id: 'user-1',
          messageBody: 'I need a cluster for development',
          role: 'user',
        },
        {
          id: 'assistant-1',
          messageBody: 'Response to: I need a cluster for development',
          role: 'assistant',
        },
        {
          id: 'user-2',
          messageBody: 'Can you make it smaller?',
          role: 'user',
        },
        {
          id: 'assistant-2',
          messageBody: 'Response to: Can you make it smaller?',
          role: 'assistant',
        },
      ];

      mockUseAiChat.mockReturnValue({
        messages: conversationMessages,
        isLoading: false,
        error: null,
        sendMessage: mockSendMessage,
      });

      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      // Verify both messages are displayed
      expect(screen.getByText('I need a cluster for development')).toBeInTheDocument();
      expect(screen.getByText('Response to: I need a cluster for development')).toBeInTheDocument();
      expect(screen.getByText('Can you make it smaller?')).toBeInTheDocument();
      expect(screen.getByText('Response to: Can you make it smaller?')).toBeInTheDocument();
    });

    it('should maintain conversation context across multiple turns', async () => {
      // Mock the hook to return messages that simulate a conversation
      const conversationMessages: Array<ChatMessage> = [
        {
          id: 'user-1',
          messageBody: 'I need a cluster',
          role: 'user',
        },
        {
          id: 'assistant-1',
          messageBody: 'Response to: I need a cluster',
          role: 'assistant',
        },
        {
          id: 'user-2',
          messageBody: 'Make it production ready',
          role: 'user',
        },
        {
          id: 'assistant-2',
          messageBody: 'Response to: Make it production ready',
          role: 'assistant',
        },
      ];

      mockUseAiChat.mockReturnValue({
        messages: conversationMessages,
        isLoading: false,
        error: null,
        sendMessage: mockSendMessage,
      });

      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      // Verify conversation flow
      expect(screen.getByText('I need a cluster')).toBeInTheDocument();
      expect(screen.getByText('Response to: I need a cluster')).toBeInTheDocument();
      expect(screen.getByText('Make it production ready')).toBeInTheDocument();
      expect(screen.getByText('Response to: Make it production ready')).toBeInTheDocument();
    });

    it('should handle conversation with tool results correctly', async () => {
      const messagesWithToolResults: Array<ChatMessage> = [
        {
          id: 'user-1',
          messageBody: 'Create a cluster configuration',
          role: 'user',
        },
        {
          id: 'assistant-1',
          messageBody: 'Here is your cluster configuration',
          role: 'assistant',
          toolResults: {
            clusterConfigTool: {
              success: true,
              clusterConfigurationParameters: mockSuggestedConfigurationParameters,
            },
          },
        },
        {
          id: 'user-2',
          messageBody: 'Can you modify the region?',
          role: 'user',
        },
        {
          id: 'assistant-2',
          messageBody: 'Response to: Can you modify the region?',
          role: 'assistant',
          toolResults: {
            clusterConfigTool: {
              success: true,
              clusterConfigurationParameters: mockSuggestedConfigurationParameters,
            },
          },
        },
      ];

      mockUseAiChat.mockReturnValue({
        messages: messagesWithToolResults,
        isLoading: false,
        error: null,
        sendMessage: mockSendMessage,
      });

      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      // Verify all messages are displayed
      expect(screen.getByText('Create a cluster configuration')).toBeInTheDocument();
      expect(screen.getByText('Here is your cluster configuration')).toBeInTheDocument();
      expect(screen.getByText('Can you modify the region?')).toBeInTheDocument();
      expect(screen.getByText('Response to: Can you modify the region?')).toBeInTheDocument();

      // Verify tool results are still accessible
      expect(screen.getByTestId('suggested-actions')).toBeInTheDocument();
    });
  });

  describe('Loading State for Suggested Actions', () => {
    it('should show loading message when AI has generated config but SuggestedActions is not ready', () => {
      mockUseAiChat.mockReturnValue({
        messages: [
          {
            id: 'assistant-1',
            messageBody: 'Here is your cluster configuration',
            role: 'assistant',
            toolResults: {
              clusterConfigTool: {
                success: true,
                message: 'Cluster configuration is valid',
                clusterConfigurationParameters: mockSuggestedConfigurationParameters,
                summary: 'Validated cluster configuration',
              },
            },
          },
        ] as Array<ChatMessage>,
        isLoading: true,
        error: null,
        sendMessage: mockSendMessage,
      });

      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      // Loading message shown and SuggestedActions not shown
      expect(screen.getByText('Setting up apply action...')).toBeInTheDocument();
      expect(screen.queryByTestId('suggested-actions')).not.toBeInTheDocument();
    });

    it('should not show loading message when no tool results exist', () => {
      mockUseAiChat.mockReturnValue({
        messages: [],
        isLoading: true,
        error: null,
        sendMessage: mockSendMessage,
      });

      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      // Should not show loading message when no tool results
      expect(screen.queryByText('Setting up apply action...')).not.toBeInTheDocument();
    });

    it('should not show loading message when SuggestedActions is ready', () => {
      mockUseAiChat.mockReturnValue({
        messages: [
          {
            id: 'assistant-1',
            messageBody: 'Here is your cluster configuration',
            role: 'assistant',
            toolResults: {
              clusterConfigTool: {
                success: true,
                message: 'Cluster configuration is valid',
                clusterConfigurationParameters: mockSuggestedConfigurationParameters,
                summary: 'Validated cluster configuration',
              },
            },
          },
        ] as Array<ChatMessage>,
        isLoading: false, // Not loading anymore
        error: null,
        sendMessage: mockSendMessage,
      });

      render(<ClusterConfigurationAiAssistant {...defaultProps} />);

      // Loading message not shown and SuggestedActions shown
      expect(screen.queryByText('Setting up apply action...')).not.toBeInTheDocument();
      expect(screen.getByTestId('suggested-actions')).toBeInTheDocument();
    });
  });
});
