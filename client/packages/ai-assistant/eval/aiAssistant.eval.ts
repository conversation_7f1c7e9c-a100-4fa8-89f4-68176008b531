import { ModelMessage } from 'ai';
import { <PERSON><PERSON>, Eval<PERSON>ase, EvalScorer } from 'braintrust';
import { z } from 'zod/v4';

import {
  CLUSTER_CONFIG_TOOL_NAME,
  ClusterConfigSchema,
  createClusterConfigTool,
} from '@packages/ai-assistant/tools/clusterConfigTool';
import { validateClusterConfig } from '@packages/ai-assistant/utils/clusterConfigValidation';

import { createResponse } from '../api/knowledgeApi';
import { generateClusterConfigInstructions } from '../instructions/clusterConfigAIAssistantInstructions';
import { clusterConfigAssistantEvalCases } from './aiAssistantEvalCases';

const MAX_RESPONSE_WORD_COUNT = 250;
const MAX_EXPLANATION_WORD_COUNT = 100;

interface ClusterConfigAssistantEvalInput {
  messages: Array<ModelMessage>;
  clusterConfigSummary?: ClusterConfig | null;
  originalClusterConfigSummary?: ClusterConfig | null;
  isEdit?: boolean;
  billingEstimate?: any;
  clusterDescriptions?: Array<any>;
}

interface ClusterConfigAssistantTaskOuput {
  assistantMessageContent: string;
  explanationContent: string;
  clusterConfigParams?: ClusterConfig;
  metadata?: ClusterConfig;
}

interface ClusterConfigAssistantEvalExpected {
  expectedClusterConfigParams?: ClusterConfig | null;
  referenceAnswer?: string | null;
}

interface AssistantEvalCase
  extends EvalCase<ClusterConfigAssistantEvalInput, ClusterConfigAssistantEvalExpected, unknown> {
  name: string;
}

type ClusterConfigAssistantEvalScorer = EvalScorer<
  ClusterConfigAssistantEvalInput,
  ClusterConfigAssistantTaskOuput,
  ClusterConfigAssistantEvalExpected
>;

type ClusterConfig = z.infer<typeof ClusterConfigSchema>;

// -- Custom Scorers --

/**
 * Evaluates if the main response is a reasonable length
 */
const ResponseLength: ClusterConfigAssistantEvalScorer = async (args) => {
  const name = 'ResponseLength';

  const response = args.output.assistantMessageContent;

  // Check if response is within reasonable length
  const wordCount = response.split(/\s+/).length;
  const appropriateLength = wordCount <= MAX_RESPONSE_WORD_COUNT;

  return {
    name,
    score: appropriateLength ? 1 : 0,
    metadata: { actualWordCount: wordCount, maxWordCount: MAX_RESPONSE_WORD_COUNT },
  };
};

/**
 * Evaluates if the explanation response is concise and appropriate
 */
const ExplanationLength: ClusterConfigAssistantEvalScorer = async (args) => {
  const name = 'ExplanationLength';

  const explanation = args.output.explanationContent;

  // Check if explanation is concise (should be shorter than main response)
  const wordCount = explanation.split(/\s+/).length;
  const appropriateLength = wordCount <= MAX_EXPLANATION_WORD_COUNT;

  return {
    name,
    score: appropriateLength ? 1 : 0,
    metadata: { actualWordCount: wordCount, maxWordCount: MAX_EXPLANATION_WORD_COUNT },
  };
};

/**
 * Normalizes field values for comparison
 */
function normalizeFieldValue(value: any): any {
  if (value === null || value === undefined) {
    return value;
  }
  if (typeof value === 'string') {
    return value.trim().toLowerCase();
  }
  if (typeof value === 'number') {
    return value;
  }
  if (typeof value === 'boolean') {
    return value;
  }
  return value;
}

/**
 * Compares two arrays for equality
 */
function arraysEqual(a: Array<any>, b: Array<any>): boolean {
  if (!a && !b) return true;
  if (!a || !b) return false;
  if (a.length !== b.length) return false;

  // Sort arrays for comparison if they contain objects
  const sortedA = a.map((item) => (typeof item === 'object' ? JSON.stringify(item) : item)).sort();
  const sortedB = b.map((item) => (typeof item === 'object' ? JSON.stringify(item) : item)).sort();

  return sortedA.every((val, index) => val === sortedB[index]);
}

/**
 * Evaluates if the generated cluster configuration matches the expected configuration
 */
const ClusterConfigAccuracy: ClusterConfigAssistantEvalScorer = async (args) => {
  const name = 'ClusterConfigAccuracy';

  const expectedClusterConfig = args.expected.expectedClusterConfigParams;

  if (!expectedClusterConfig) {
    return {
      name,
      score: null,
    };
  }
  if (expectedClusterConfig && !args.output.clusterConfigParams) {
    return { name, score: 0, metadata: { message: 'no output cluster config when expected' } };
  }

  const expected = expectedClusterConfig;
  const generated = args.output.clusterConfigParams!;

  let matches = 0;
  let totalFields = 0;
  const fieldMismatches: Array<{ field: string; expected: any; generated: any }> = [];

  // Check required fields
  const requiredFields = ['name', 'cloudProvider', 'regionName', 'instanceSize'];
  for (const field of requiredFields) {
    totalFields++;
    const expectedValue = expected[field as keyof ClusterConfig];
    const generatedValue = generated[field as keyof ClusterConfig];

    // Normalize values for comparison
    const normalizedExpected = normalizeFieldValue(expectedValue);
    const normalizedGenerated = normalizeFieldValue(generatedValue);

    if (normalizedExpected === normalizedGenerated) {
      matches++;
    } else {
      fieldMismatches.push({ field, expected: expectedValue, generated: generatedValue });
    }
  }

  // Check optional fields if they exist in the expected cluster config
  const optionalFields = ['diskSizeGB', 'diskIOPS', 'nvme', 'lowCpu', 'tags'];
  for (const field of optionalFields) {
    const expectedValue = expected[field as keyof ClusterConfig];
    if (expectedValue !== undefined && expectedValue !== null) {
      totalFields++;
      const generatedValue = generated[field as keyof ClusterConfig];

      // Special handling for tags array comparison
      if (field === 'tags') {
        if (arraysEqual(expectedValue as Array<any>, generatedValue as Array<any>)) {
          matches++;
        } else {
          fieldMismatches.push({ field, expected: expectedValue, generated: generatedValue });
        }
      } else {
        // Normalize values for comparison
        const normalizedExpected = normalizeFieldValue(expectedValue);
        const normalizedGenerated = normalizeFieldValue(generatedValue);

        if (normalizedExpected === normalizedGenerated) {
          matches++;
        } else {
          fieldMismatches.push({ field, expected: expectedValue, generated: generatedValue });
        }
      }
    }
  }

  return {
    name,
    score: totalFields > 0 ? matches / totalFields : 0,
    metadata: {
      totalFields,
      matches,
      fieldMismatches: fieldMismatches.slice(0, 5), // Limit to first 5 mismatches for debugging
      expected: expected,
      generated: generated,
    },
  };
};

/**
 * Evaluates if the explanation is relevant and helpful
 */
const ExplanationQuality: ClusterConfigAssistantEvalScorer = async (args) => {
  const name = 'ExplanationQuality';

  const explanation = args.output.explanationContent;
  const clusterConfig = args.output.clusterConfigParams;

  if (!explanation || explanation.trim().length === 0) {
    return { name, score: 0, metadata: { message: 'no explanation provided' } };
  }

  // Check if explanation mentions key configuration details
  const configMentioned = clusterConfig
    ? explanation.toLowerCase().includes(clusterConfig.instanceSize.toLowerCase()) ||
      explanation.toLowerCase().includes(clusterConfig.cloudProvider.toLowerCase())
    : false;

  // Check if explanation is concise (not too verbose)
  const wordCount = explanation.split(/\s+/).length;
  const isConcise = wordCount <= MAX_EXPLANATION_WORD_COUNT;

  // Check if explanation provides reasoning (contains words like "because", "since", "for", etc.)
  const hasReasoning = /\b(because|since|for|as|due to|given that|considering|this|that|which|why)\b/i.test(
    explanation
  );

  // Calculate score based on multiple factors
  let score = 0;
  if (configMentioned) score += 0.4;
  if (isConcise) score += 0.3;
  if (hasReasoning) score += 0.3;

  return {
    name,
    score,
    metadata: {
      configMentioned,
      isConcise,
      hasReasoning,
      wordCount,
      explanation: explanation.substring(0, 200), // First 200 chars for debugging
    },
  };
};

/**
 * Evaluates if the generated cluster configuration is technically valid
 */
const ClusterConfigValidity: ClusterConfigAssistantEvalScorer = async (args) => {
  const name = 'ClusterConfigValidity';

  const clusterConfig = args.output.clusterConfigParams;

  if (!clusterConfig) {
    return { name, score: 0, metadata: { message: 'no cluster config generated' } };
  }

  // Use shared validation logic with evaluation mode enabled
  const validationResult = validateClusterConfig(clusterConfig, { forEvaluation: true });

  return {
    name,
    score:
      validationResult.score !== undefined && validationResult.totalChecks !== undefined
        ? validationResult.totalChecks > 0
          ? validationResult.score / validationResult.totalChecks
          : 0
        : validationResult.isValid
          ? 1
          : 0,
    metadata: {
      totalChecks: validationResult.totalChecks,
      score: validationResult.score,
      validationErrors: validationResult.errors.slice(0, 10), // Limit to first 10 errors
      clusterConfig,
    },
  };
};

// -- Main Evaluation Function --

export function createClusterConfigAssistantEval() {
  return Eval('Cluster Configuration Assistant', {
    data: clusterConfigAssistantEvalCases.map(
      (evalCase) =>
        ({
          name: evalCase.name,
          tags: evalCase.tags,
          input: {
            messages: evalCase.messages,
            clusterConfigSummary: evalCase.clusterConfigSummary,
            originalClusterConfigSummary: evalCase.clusterConfigSummary, // For edit scenarios, use the same as current
            isEdit: evalCase.clusterConfigSummary !== undefined, // If we have a cluster config, it's an edit scenario
            billingEstimate: undefined, // Add default billing estimate
            clusterDescriptions: [], // Add default cluster descriptions
          },
          expected: {
            expectedClusterConfigParams: evalCase.expectedClusterConfigParams,
            referenceAnswer: evalCase.referenceAnswer,
          },
          metadata: null,
        }) satisfies AssistantEvalCase
    ),
    task: async (input: ClusterConfigAssistantEvalInput): Promise<ClusterConfigAssistantTaskOuput> => {
      try {
        // Filter out empty messages and ensure we have valid content (matching useAiChat)
        const conversationMessages = input.messages
          .filter((msg) => {
            if (typeof msg.content === 'string') {
              return msg.content.trim().length > 0;
            }
            return true; // Keep non-string content as-is
          })
          .map((msg) => ({
            role: msg.role,
            content: msg.content,
          }));

        // Create full context for instruction generation (matching useAiChat)
        const context = {
          originalClusterConfigSummary: input.originalClusterConfigSummary ?? undefined,
          currentClusterConfigSummary: input.clusterConfigSummary ?? undefined,
          isEdit: input.isEdit || false,
          billingEstimate: input.billingEstimate,
          clusterDescriptions: input.clusterDescriptions || [],
        };

        // Generate dynamic instructions based on cluster configuration context (matching useAiChat)
        const instructions = generateClusterConfigInstructions(context);

        const result = createResponse({
          isCommandLine: true,
          messages: conversationMessages as Array<ModelMessage>,
          instructions,
          tools: {
            [CLUSTER_CONFIG_TOOL_NAME]: createClusterConfigTool(context),
          },
        });

        // Check if textStream exists before trying to iterate (matching useAiChat)
        if (!result?.textStream) {
          throw new Error(`textStream is undefined. Result structure: ${JSON.stringify(result, null, 2)}`);
        }

        let fullResponse = '';

        // Handle the streaming response (matching useAiChat)
        for await (const delta of result.textStream) {
          fullResponse += delta;
        }

        // Extract cluster config params from tool results
        const clusterConfigParams = await result.metadata.getToolResult(CLUSTER_CONFIG_TOOL_NAME);

        // Debug: Log tool usage
        console.log(`Tool called: ${!!clusterConfigParams}, Success: ${clusterConfigParams?.success}`);

        // Enhanced error handling and validation
        if (!fullResponse || fullResponse.trim().length === 0) {
          fullResponse = 'No response generated';
        }

        return {
          assistantMessageContent: fullResponse,
          explanationContent: fullResponse, // Use the same response for explanation in evaluation
          clusterConfigParams: clusterConfigParams?.success
            ? clusterConfigParams.clusterConfigurationParameters
            : undefined,
          metadata: clusterConfigParams?.success ? clusterConfigParams.clusterConfigurationParameters : undefined,
        };
      } catch (error) {
        return {
          assistantMessageContent: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          explanationContent: '',
          clusterConfigParams: undefined,
          metadata: undefined,
        };
      }
    },
    scores: [ClusterConfigAccuracy, ResponseLength, ExplanationQuality, ExplanationLength, ClusterConfigValidity],
  });
}

createClusterConfigAssistantEval();
