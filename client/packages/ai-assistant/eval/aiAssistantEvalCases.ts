import { z } from 'zod/v4';

import { BackingCloudProvider, InstanceSizes } from '../../types/nds/provider';
import { RegionNames } from '../../types/nds/region';
import { ClusterConfigAssistantEvalCaseSchema } from './aiAssistantEvalCase';

export type ClusterConfigAssistantEvalCase = z.infer<typeof ClusterConfigAssistantEvalCaseSchema>;

// TODO CLOUDP-331810: Update these eval cases once we have a better idea of actual chatbot output
export const clusterConfigAssistantEvalCases: Array<ClusterConfigAssistantEvalCase> = [
  // Moving cluster region - Editing flow
  {
    name: 'should migrate cluster from AWS to Azure with region change',
    messages: [
      {
        role: 'user',
        content: 'We want to migrate this workload to Azure deployed in California.',
      },
    ],
    clusterConfigSummary: {
      name: 'multiCloud',
      tags: [{ key: 'api', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 50,
      diskIOPS: 3000,
    },
    expectedClusterConfigParams: {
      name: 'multiCloud',
      tags: [{ key: 'api', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AZURE,
      regionName: RegionNames.US_WEST_2,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 50,
      diskIOPS: 3000,
    },
    referenceAnswer: "Here's the same configuration moved to Azure West US.",
    tags: ['migration', 'azure', 'region-change', 'editing'],
  },

  // Starting production cluster - Creation flow
  {
    name: 'should recommend production e-commerce cluster configuration',
    messages: [
      {
        role: 'user',
        content: "I'm launching a production e-commerce app in North America. What's a good starting cluster?",
      },
    ],
    expectedClusterConfigParams: {
      name: 'ecom-prod',
      tags: [{ key: 'ecommerce', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 60,
      diskIOPS: 3000,
    },
    referenceAnswer:
      'For a high-traffic e-commerce app in production, I recommend: Cloud Provider: AWS, Region: US_EAST_1. Instance Size: M30, Disk: 60 GB, 3000 IOPS.',
    tags: ['production', 'ecommerce', 'creation', 'M30'],
  },

  // Backup feature details
  {
    name: 'should explain backup options for existing cluster',
    messages: [
      {
        role: 'user',
        content: 'What backup options are available for my Atlas cluster?',
      },
    ],
    clusterConfigSummary: {
      name: 'multi-cloud',
      tags: [{ key: 'api', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 50,
    },
    referenceAnswer:
      'Atlas offers the following options for your current cluster: Continuous backups with point-in-time restore, Snapshot backups on a fixed schedule. Would you like to enable backups or set a policy?',
    tags: ['backup', 'feature-details', 'existing-cluster'],
  },

  // Reduce slow queries - Editing flow
  {
    name: 'should upgrade cluster to resolve slow query performance',
    messages: [
      {
        role: 'user',
        content: "We're seeing slow queries—can you bump us up to the next size?",
      },
    ],
    clusterConfigSummary: {
      name: 'user-data-cluster',
      tags: [{ key: 'prod', value: 'analytics' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M20,
      diskSizeGB: 40,
      diskIOPS: 2000,
    },
    expectedClusterConfigParams: {
      name: 'user-data-cluster',
      tags: [{ key: 'prod', value: 'analytics' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 40,
      diskIOPS: 3000,
    },
    referenceAnswer: 'Upgraded from M20 to M30 to improve performance.',
    tags: ['performance', 'upgrade', 'slow-queries', 'M30'],
  },

  // Prototyping dev cluster better than free - Creation flow
  {
    name: 'should recommend Flex tier for prototyping beyond M0',
    messages: [
      {
        role: 'user',
        content: "We're prototyping a CLI tool and want a dev cluster that's better than M0. What should we use?",
      },
    ],
    expectedClusterConfigParams: {
      name: 'cli-prototype',
      tags: [{ key: 'dev', value: 'flex' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.FLEX,
      diskSizeGB: 5,
      diskIOPS: 50,
    },
    referenceAnswer:
      'For lightweight development without going full M10, I recommend a Flex Tier cluster. It offers better performance than M0 but at a lower cost than M10. Great for prototyping.',
    tags: ['development', 'flex', 'prototyping', 'cost-effective'],
  },

  // Budget constraint - Creation flow
  {
    name: 'should recommend best production cluster within budget',
    messages: [
      {
        role: 'user',
        content:
          "I have a $500 monthly budget for this project. What's the best production cluster I can run in that range?",
      },
    ],
    expectedClusterConfigParams: {
      name: 'prod-budget',
      tags: [{ key: 'budget', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 60,
      diskIOPS: 3000,
    },
    referenceAnswer:
      'A M30 cluster on AWS in US_EAST_1 with ~60 GB disk and backups fits comfortably within a $500/month budget, even with light autoscaling.',
    tags: ['budget', 'production', 'cost-optimization', 'M30'],
  },

  // Health tech use case - Creation flow
  {
    name: 'should recommend HIPAA-compliant cluster for health records',
    messages: [
      {
        role: 'user',
        content: 'This is a US-based health records platform. We need HIPAA compliance and audit logging.',
      },
    ],
    expectedClusterConfigParams: {
      name: 'health-records',
      tags: [{ key: 'health', value: 'compliance' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 60,
      diskIOPS: 3000,
    },
    referenceAnswer:
      'For HIPAA-compliant workloads, I recommend: Dedicated cluster (M30+), Backups and encryption (enabled by default), Enable auditing in the UI, Deploy in secure region (e.g., US_EAST_1 on AWS)',
    tags: ['healthcare', 'hipaa', 'compliance', 'security'],
  },

  // Data size - Creation flow
  {
    name: 'should recommend high-throughput cluster for telemetry data ingestion',
    messages: [
      {
        role: 'user',
        content:
          'We expect to ingest 500GB of telemetry data per day with 3K writes/sec. What kind of cluster do we need?',
      },
    ],
    expectedClusterConfigParams: {
      name: 'telemetry-ingest',
      tags: [{ key: 'telemetry', value: 'ingest' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M60,
      diskSizeGB: 500,
      diskIOPS: 1000,
    },
    referenceAnswer:
      "To handle 500GB/day ingest and sustained 3K writes/sec, you'll need: A sharded cluster, At least M60 tier or higher, Fast disk with high IOPS, Consider enabling write concern: 1 to reduce latency. I cannot automatically enable sharding for you right now but you can do so in the UI.",
    tags: ['high-throughput', 'telemetry', 'ingestion', 'M60', 'sharding'],
  },

  // Downgrading based on environment - Editing flow
  {
    name: 'should downgrade cluster for QA environment usage',
    messages: [
      {
        role: 'user',
        content: "We're only using this for internal QA now. Can we downgrade?",
      },
    ],
    clusterConfigSummary: {
      name: 'test-logs',
      tags: [{ key: 'prod', value: '' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 50,
      diskIOPS: 3000,
    },
    expectedClusterConfigParams: {
      name: 'test-logs',
      tags: [{ key: 'dev', value: '' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 50,
      diskIOPS: 1000,
    },
    referenceAnswer:
      "Here's an M10 configuration with a disk size of 50GB to handle the data in the existing cluster. Can you provide any more detail about the number of reads and writes you expect per day?",
    tags: ['downgrade', 'qa', 'cost-reduction', 'M10'],
  },

  // Upgrading for better latency - Editing flow
  {
    name: 'should upgrade from Flex to M10 for better latency',
    messages: [
      {
        role: 'user',
        content: 'We need better latency. Can we move to a higher performance tier?',
      },
    ],
    clusterConfigSummary: {
      name: 'api-cluster',
      tags: [{ key: 'api', value: 'edge' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.AP_SOUTHEAST_1,
      instanceSize: InstanceSizes.FLEX,
      diskSizeGB: 5,
    },
    expectedClusterConfigParams: {
      name: 'api-cluster',
      tags: [{ key: 'api', value: 'edge' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.AP_SOUTHEAST_1,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 10,
    },
    referenceAnswer: 'I have increased your cluster tier size to an M10 to improve latency.',
    tags: ['upgrade', 'latency', 'performance', 'flex-to-m10'],
  },

  // Staging environment - Creation flow
  {
    name: 'should recommend staging cluster based on production requirements',
    messages: [
      {
        role: 'user',
        content:
          'Can you recommend a good cluster for our staging environment? Our production app is in M40 and we expect to replicate 100% of data but can handle a bit more latency.',
      },
    ],
    expectedClusterConfigParams: {
      name: 'staging-mirror',
      tags: [{ key: 'env', value: 'staging' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M20,
      diskSizeGB: 20,
      diskIOPS: 2000,
    },
    referenceAnswer:
      "For a staging environment, it's common to use a slightly smaller or matching instance size to save costs while ensuring realistic testing. Since your production app uses an M40, you could go with an M20 or M30 to reduce spend while still supporting a full dataset copy and test scalability.",
    tags: ['staging', 'cost-optimization', 'production-mirror', 'M20'],
  },

  // Batch queries - Editing flow
  {
    name: 'should recommend cluster upgrade for batch analytics workload',
    messages: [
      {
        role: 'user',
        content: 'We need to run batch queries without affecting app performance. Suggestions?',
      },
    ],
    clusterConfigSummary: {
      name: 'reporting',
      tags: [{ key: 'analytics', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_WEST_2,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 60,
      diskIOPS: 3000,
    },
    expectedClusterConfigParams: {
      name: 'reporting-analytics',
      tags: [{ key: 'analytics', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_WEST_2,
      instanceSize: InstanceSizes.M40,
      diskSizeGB: 80,
      diskIOPS: 3000,
    },
    referenceAnswer:
      'To support batch analytics queries without affecting application performance, the usual best practice is to isolate workloads. This can be done by scaling up your main cluster or, ideally, creating a dedicated analytics node or a separate cluster for reporting which you can do in the Atlas UI.',
    tags: ['batch-queries', 'analytics', 'performance-isolation', 'M40'],
  },

  // Time to scale - Feature Details
  {
    name: 'should explain scaling timeline from M10 to M30',
    messages: [
      {
        role: 'user',
        content: 'How long would it take me to scale this M10 to an M30?',
      },
    ],
    referenceAnswer:
      'Scaling from an M10 to an M30 in MongoDB Atlas is usually a quick operation and requires just a few clicks in the UI (or via the API). For most clusters, the upgrade process takes about 7–10 minutes, but can vary depending on Cluster size (amount of data stored), Current workload, Cloud provider region. Would you like help configuring the upgrade to M30 or need an example cluster setup?',
    tags: ['scaling', 'timeline', 'feature-details', 'M10-to-M30'],
  },

  // Global cluster configurations - Feature Details
  {
    name: 'should explain global cluster configuration requirements',
    messages: [
      {
        role: 'user',
        content: 'I have global users - do I need to enable global cluster configuration?',
      },
    ],
    referenceAnswer:
      'If your application serves users from multiple geographic regions and requires low latency for read/write operations from different parts of the world or data locality for compliance or performance reasons then enabling a Global Cluster (MongoDB Atlas Global Writes) is recommended. This feature allows you to distribute your data across multiple geographic regions, reducing latency and improving user experience for global audiences.',
    tags: ['global-cluster', 'multi-region', 'feature-details', 'latency'],
  },

  // Scaling up for production - Editing flow
  {
    name: 'should upgrade development cluster for production deployment',
    messages: [
      {
        role: 'user',
        content: "We're moving this from development to production. What changes should I make?",
      },
    ],
    clusterConfigSummary: {
      name: 'dev-blog',
      tags: [{ key: 'env', value: 'development' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 10,
      diskIOPS: 1000,
    },
    expectedClusterConfigParams: {
      name: 'blog-prod',
      tags: [{ key: 'env', value: 'production' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 40,
      diskIOPS: 3000,
    },
    referenceAnswer:
      "Since you're transitioning to production, I recommend upgrading your instance to an M30 or higher, depending on traffic and data needs. A common choice is M30 for non-latency-critical production workloads.",
    tags: ['dev-to-prod', 'upgrade', 'production', 'M30'],
  },

  // Budget based downgrade - Editing flow
  {
    name: 'should downgrade expensive cluster for cost savings',
    messages: [
      {
        role: 'user',
        content: "This cluster is too expensive. It's not business-critical — can we make it cheaper?",
      },
    ],
    clusterConfigSummary: {
      name: 'marketing-data',
      tags: [
        { key: 'team', value: 'marketing' },
        { key: 'env', value: 'staging' },
      ],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M40,
      diskSizeGB: 80,
      diskIOPS: 3000,
    },
    expectedClusterConfigParams: {
      name: 'marketing-data-staging',
      tags: [
        { key: 'team', value: 'marketing' },
        { key: 'env', value: 'staging' },
      ],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M20,
      diskSizeGB: 20,
      diskIOPS: 2000,
    },
    referenceAnswer:
      "You can scale down to an M20 or even a FLEX cluster if usage is light. Here's a more cost-effective option on GCP:",
    tags: ['cost-reduction', 'downgrade', 'budget', 'M20'],
  },

  // Increasing storage - Editing flow
  {
    name: 'should increase storage without changing instance tier',
    messages: [
      {
        role: 'user',
        content: "We're running out of space. Can I expand storage without changing tier?",
      },
    ],
    clusterConfigSummary: {
      name: 'product-images',
      tags: [{ key: 'env', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 10,
      diskIOPS: 1000,
    },
    expectedClusterConfigParams: {
      name: 'product-images',
      tags: [{ key: 'env', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 20,
      diskIOPS: 1000,
    },
    referenceAnswer: "Yes, you can increase the disk size while staying on M10. Here's the revised config:",
    tags: ['storage-expansion', 'disk-size', 'same-tier', 'M10'],
  },

  // Free to flex - Editing flow
  {
    name: 'should upgrade from free tier to Flex for increased limits',
    messages: [
      {
        role: 'user',
        content: 'I need to increase the limits of the free tier but keep costs low.',
      },
    ],
    clusterConfigSummary: {
      name: 'user-auth',
      tags: [{ key: 'env', value: 'staging' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M0,
    },
    expectedClusterConfigParams: {
      name: 'user-auth-staging',
      tags: [{ key: 'env', value: 'staging' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.FLEX,
      diskSizeGB: 5,
    },
    referenceAnswer:
      'You can upgrade to a FLEX cluster which starts as low as $0.011/hour, is capped at $30/month, and scales automatically.',
    tags: ['free-to-flex', 'cost-effective', 'limits', 'FLEX'],
  },

  // Scale to Handle Nightly Ingest - Editing flow
  {
    name: 'should upgrade cluster for heavy nightly data ingestion',
    messages: [
      {
        role: 'user',
        content: 'We ingest about 50GB of new IoT data each night. This tier is struggling. What should I upgrade to?',
      },
    ],
    clusterConfigSummary: {
      name: 'iot-ingest',
      tags: [{ key: 'env', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M20,
      diskSizeGB: 20,
      diskIOPS: 2000,
    },
    expectedClusterConfigParams: {
      name: 'iot-ingest-prod',
      tags: [{ key: 'env', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M60,
      diskSizeGB: 320,
      diskIOPS: 3000,
    },
    referenceAnswer:
      "For heavy ingest workloads, especially at this scale, I'd recommend moving to an M50 or M60 with larger disk and higher throughput.",
    tags: ['iot-ingestion', 'heavy-workload', 'upgrade', 'M60'],
  },

  // Cheaper cloud provider - Editing flow
  {
    name: 'should migrate to cheaper cloud provider',
    messages: [
      {
        role: 'user',
        content: 'This is getting expensive on AWS. Can I move to a cheaper provider?',
      },
    ],
    clusterConfigSummary: {
      name: 'log-analysis',
      tags: [{ key: 'team', value: 'infra' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 40,
      diskIOPS: 3000,
    },
    expectedClusterConfigParams: {
      name: 'log-analysis',
      tags: [{ key: 'team', value: 'infra' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 40,
      diskIOPS: 3000,
    },
    referenceAnswer: "Yes, GCP and Azure typically offer lower pricing. Here's the equivalent config on GCP:",
    tags: ['cost-optimization', 'provider-migration', 'gcp', 'M30'],
  },

  // Prep for surge - Editing flow
  {
    name: 'should upgrade cluster for expected traffic surge',
    messages: [
      {
        role: 'user',
        content: 'Expecting 5x traffic next month. What should I upgrade to for safety?',
      },
    ],
    clusterConfigSummary: {
      name: 'checkout-db',
      tags: [
        { key: 'env', value: 'production' },
        { key: 'event', value: 'surge' },
      ],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M40,
      diskSizeGB: 80,
      diskIOPS: 3000,
    },
    expectedClusterConfigParams: {
      name: 'checkout-db-surge',
      tags: [
        { key: 'env', value: 'production' },
        { key: 'event', value: 'surge' },
      ],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M80,
      diskSizeGB: 750,
      diskIOPS: 3000,
    },
    referenceAnswer:
      'For major traffic surges, I recommend moving to M80 or higher and enabling autoscaling and backups via the UI.',
    tags: ['traffic-surge', 'scaling', 'preparation', 'M80'],
  },

  // Testing highest IOPS - Editing flow
  {
    name: 'should maximize IOPS for performance testing',
    messages: [
      {
        role: 'user',
        content: 'I want to test this API with maximum IOPS possible for this tier.',
      },
    ],
    clusterConfigSummary: {
      name: 'search-api',
      tags: [{ key: 'env', value: 'test' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 40,
      diskIOPS: 2000,
    },
    expectedClusterConfigParams: {
      name: 'search-api-test',
      tags: [{ key: 'env', value: 'test' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 40,
      diskIOPS: 3000,
    },
    referenceAnswer: 'You can increase disk IOPS to the max (3000) supported for M30 without changing tier.',
    tags: ['performance-testing', 'max-iops', 'optimization', 'M30'],
  },

  // NVME storage - Editing flow
  {
    name: 'should configure NVME storage for real-time workload',
    messages: [
      {
        role: 'user',
        content: 'We want to test NVME for our real-time feed — what config supports that?',
      },
    ],
    clusterConfigSummary: {
      name: 'real-time-feed',
      tags: [{ key: 'env', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M40,
      diskSizeGB: 80,
      diskIOPS: 3000,
    },
    expectedClusterConfigParams: {
      name: 'real-time-feed-prod',
      tags: [{ key: 'env', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M40_NVME,
      diskSizeGB: 80,
      diskIOPS: 3000,
    },
    referenceAnswer:
      "NVME is supported starting at M40_NVME on AWS. Here's an updated version of your current cluster using NVME:",
    tags: ['nvme', 'real-time', 'performance', 'M40'],
  },

  // Switching providers + tier for NVME - Editing flow
  {
    name: 'should migrate to AWS for NVME storage testing',
    messages: [
      {
        role: 'user',
        content:
          "We're doing a lot of AI model training and want to try out NVME for performance. How can we test this?",
      },
    ],
    clusterConfigSummary: {
      name: 'ml-training-data',
      tags: [{ key: 'team', value: 'ml' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 40,
      diskIOPS: 3000,
    },
    expectedClusterConfigParams: {
      name: 'ml-training-data-nvme',
      tags: [
        { key: 'team', value: 'ml' },
        { key: 'usecase', value: 'ai' },
      ],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M40,
      diskSizeGB: 380,
    },
    referenceAnswer:
      "NVME storage is currently only available on AWS starting at M40_NVME. To test NVME, you'll need to migrate to AWS. Here's a similar config using NVME:",
    tags: ['nvme', 'provider-migration', 'ai-training', 'M40'],
  },

  // Background processing with lower CPU needs - Editing flow
  {
    name: 'should recommend low-CPU tier for batch processing',
    messages: [
      {
        role: 'user',
        content: "This is a scheduled batch report system. It doesn't need much CPU but should stay reliable.",
      },
    ],
    clusterConfigSummary: {
      name: 'report-generator',
      tags: [{ key: 'env', value: 'prod' }],
      cloudProvider: BackingCloudProvider.AZURE,
      regionName: RegionNames.US_WEST_2,
      instanceSize: InstanceSizes.M40,
      diskSizeGB: 80,
      diskIOPS: 3000,
    },
    expectedClusterConfigParams: {
      name: 'report-generator',
      tags: [
        { key: 'env', value: 'prod' },
        { key: 'usecase', value: 'reporting' },
      ],
      cloudProvider: BackingCloudProvider.AZURE,
      regionName: RegionNames.US_WEST_2,
      instanceSize: InstanceSizes.R40,
      diskSizeGB: 80,
      diskIOPS: 3000,
    },
    referenceAnswer:
      "For background jobs that don't require high CPU, switching to a low-CPU tier like R40 can reduce costs while keeping reliability.",
    tags: ['low-cpu', 'batch-processing', 'cost-optimization', 'R40'],
  },

  // Heavy search usage - Editing flow
  {
    name: 'should upgrade cluster for search-heavy workload',
    messages: [
      {
        role: 'user',
        content:
          'This powers a product browse page with lots of keyword filtering and sorting. Response times are getting slow — any suggestions?',
      },
    ],
    clusterConfigSummary: {
      name: 'product-browse',
      tags: [{ key: 'env', value: 'production' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M20,
      diskSizeGB: 20,
      diskIOPS: 2000,
    },
    expectedClusterConfigParams: {
      name: 'product-browse',
      tags: [
        { key: 'env', value: 'production' },
        { key: 'usecase', value: 'search' },
      ],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M40,
      diskSizeGB: 80,
      diskIOPS: 3000,
    },
    referenceAnswer:
      'If the workload is mostly reads with text filtering and sorting, consider scaling up to an M30 or M40 tier for improved read throughput. You can also enable dedicated search indexes or workload isolation in the UI.',
    tags: ['search-workload', 'performance', 'upgrade', 'M40'],
  },

  // CPU issues on an M10 - Editing flow
  {
    name: 'should upgrade M10 to resolve CPU bottlenecks',
    messages: [
      {
        role: 'user',
        content: 'We keep hitting CPU spikes on this backend cluster and requests slow down. What should I do?',
      },
    ],
    clusterConfigSummary: {
      name: 'app-backend',
      tags: [{ key: 'env', value: 'prod' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 10,
      diskIOPS: 1000,
    },
    expectedClusterConfigParams: {
      name: 'app-backend-prod',
      tags: [{ key: 'env', value: 'prod' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 40,
      diskIOPS: 3000,
    },
    referenceAnswer:
      'M10s can be limiting under bursty or compute-intensive workloads. I recommend upgrading to an M30 for more RAM and vCPUs. You can also consider enabling auto-scaling in the UI to handle future spikes.',
    tags: ['cpu-bottleneck', 'performance', 'upgrade', 'M30'],
  },

  // Bad prompt - Creation flow
  {
    name: 'should handle vague cluster request with default recommendation',
    messages: [
      {
        role: 'user',
        content: 'I need a cluster',
      },
    ],
    expectedClusterConfigParams: {
      name: 'default-cluster',
      tags: [],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 10,
      diskIOPS: 1000,
    },
    referenceAnswer:
      "Could you share a bit more about your use case? If you're just exploring or prototyping, here's a solid default recommendation to get you started. If you want to prototype without costs, try the Free tier.",
    tags: ['vague-request', 'default-recommendation', 'M10'],
  },

  // Use case based - Creation flow
  {
    name: 'should recommend Flex for development restaurant app',
    messages: [
      {
        role: 'user',
        content:
          "I'm setting up a development cluster for a restaurant rating app with 100 users in NYC. What's the best cluster configuration?",
      },
    ],
    expectedClusterConfigParams: {
      name: 'restaurant-ratings-dev',
      tags: [
        { key: 'env', value: 'development' },
        { key: 'app', value: 'restaurant-ratings' },
      ],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.FLEX,
      diskSizeGB: 5,
    },
    referenceAnswer:
      "Since this is for development and usage is light, a FLEX cluster is a great fit. It offers scalability and keeps costs low. Here's a config optimized for your NYC location:",
    tags: ['development', 'restaurant-app', 'flex', 'nyc'],
  },

  // Migration data size + location - Creation flow
  {
    name: 'should recommend European cluster for data migration',
    messages: [
      {
        role: 'user',
        content:
          "I'm looking to migrate an existing project to Atlas. We currently have 100GB of data that we need to move over. The project is currently deployed in Europe and we want to maintain that. How should I configure my cluster?",
      },
    ],
    expectedClusterConfigParams: {
      name: 'project-migration-eu',
      tags: [{ key: 'env', value: 'production' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.EUROPE_WEST,
      instanceSize: InstanceSizes.M40,
      diskSizeGB: 120,
      diskIOPS: 3000,
    },
    referenceAnswer:
      'For 100GB of data in production, I recommend an M40 tier to ensure performance and reliability. You can select a European region like EU_WEST_1 (AWS). Use Live Migrate or mongosync for the data transfer',
    tags: ['migration', 'europe', 'large-data', 'M40'],
  },

  // Prod cluster with budget and user estimate - Creation flow
  {
    name: 'should recommend production cluster within budget for 30K users',
    messages: [
      {
        role: 'user',
        content:
          "I'm setting up a production cluster for my current project and anticipate roughly 30K users. I have a budget of $1,000/month. What do you recommend?",
      },
    ],
    expectedClusterConfigParams: {
      name: 'project-prod-cluster',
      tags: [{ key: 'env', value: 'production' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M40,
      diskSizeGB: 80,
      diskIOPS: 3000,
    },
    referenceAnswer:
      "For your production cluster, an M40 is a good fit. The M40 costs about $1.04/hr ($750/month), giving you budget flexibility for backups, extra storage, or additional features. I recommend enabling backups and autoscaling (especially disk if you anticipate rapid growth), but you'll need to enable those in the Atlas UI.",
    tags: ['production', 'budget', '30k-users', 'M40'],
  },

  // Startup prototyping - Creation flow
  {
    name: 'should recommend Flex for startup prototype',
    messages: [
      {
        role: 'user',
        content:
          "We're a two-person startup building an early prototype. We want to launch quickly and stay flexible. What cluster should we start with?",
      },
    ],
    expectedClusterConfigParams: {
      name: 'startup-prototype',
      tags: [
        { key: 'env', value: 'development' },
        { key: 'startup', value: 'true' },
      ],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.FLEX,
    },
    referenceAnswer: 'FLEX is ideal for fast, iterative prototyping with low overhead and automatic scaling.',
    tags: ['startup', 'prototype', 'flex', 'agile'],
  },

  // US based SAAS MVP - Creation flow
  {
    name: 'should recommend M10 for SaaS MVP',
    messages: [
      {
        role: 'user',
        content:
          "I'm launching a SaaS MVP for time tracking in the US and need a cluster that's not overkill but reliable. What do you recommend?",
      },
    ],
    expectedClusterConfigParams: {
      name: 'timetracker-prod',
      tags: [
        { key: 'env', value: 'production' },
        { key: 'app', value: 'saas' },
      ],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 10,
      diskIOPS: 1000,
    },
    referenceAnswer:
      'An M10 is a strong choice for a small production app with minimal traffic. Consider enabling autoscaling in the UI to handle traffic as your usage grows.',
    tags: ['saas', 'mvp', 'time-tracking', 'M10'],
  },

  // App in APAC - Creation flow
  {
    name: 'should recommend APAC region for Southeast Asia users',
    messages: [
      {
        role: 'user',
        content:
          'Our app is mostly used in Southeast Asia. We need low latency for users in that region. What config do you suggest?',
      },
    ],
    expectedClusterConfigParams: {
      name: 'apac-app-prod',
      tags: [
        { key: 'env', value: 'production' },
        { key: 'region', value: 'apac' },
      ],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.AP_SOUTHEAST_1,
      instanceSize: InstanceSizes.M20,
      diskSizeGB: 20,
      diskIOPS: 2000,
    },
    referenceAnswer: 'Choose a region like ASIA_SOUTHEAST1 on GCP to reduce latency for local users.',
    tags: ['apac', 'latency', 'regional', 'M20'],
  },

  // AI Workload - Creation flow
  {
    name: 'should recommend high-memory cluster for AI model backend',
    messages: [
      {
        role: 'user',
        content:
          "I'm deploying a backend that serves AI model predictions. Need fast reads and decent memory. What do you recommend?",
      },
    ],
    expectedClusterConfigParams: {
      name: 'ai-model-backend',
      tags: [
        { key: 'env', value: 'production' },
        { key: 'app', value: 'ai' },
      ],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_WEST_2,
      instanceSize: InstanceSizes.M40,
      diskSizeGB: 80,
      diskIOPS: 3000,
    },
    referenceAnswer: 'AI workloads benefit from higher RAM and fast storage — M40 or above is a solid starting point.',
    tags: ['ai', 'model-serving', 'high-memory', 'M40'],
  },

  // Low Traffic Production - Creation flow
  {
    name: 'should recommend M10 for low-traffic marketing site',
    messages: [
      {
        role: 'user',
        content:
          'This is for our marketing site that includes a CMS and some dynamic content. Low traffic, but public-facing.',
      },
    ],
    expectedClusterConfigParams: {
      name: 'marketing-site',
      tags: [
        { key: 'env', value: 'production' },
        { key: 'site', value: 'public' },
      ],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 10,
      diskIOPS: 1000,
    },
    referenceAnswer: 'M10 is a starting minimum for public-facing prod apps. Is this app business critical?',
    tags: ['marketing', 'low-traffic', 'public-facing', 'M10'],
  },

  // Staging with location - Creation flow
  {
    name: 'should recommend EU staging cluster',
    messages: [
      {
        role: 'user',
        content: 'Need a staging cluster in the EU region to match prod setup.',
      },
    ],
    expectedClusterConfigParams: {
      name: 'eu-staging',
      tags: [{ key: 'env', value: 'staging' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.EUROPE_WEST,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 10,
      diskIOPS: 1000,
    },
    referenceAnswer: 'M10 is appropriate for most staging use cases.',
    tags: ['staging', 'europe', 'regional', 'M10'],
  },

  // Fastest / cheapest - Creation flow
  {
    name: 'should recommend Flex for hackathon project',
    messages: [
      {
        role: 'user',
        content: "Spinning up a quick app for a hackathon. What's the fastest and cheapest way to do that?",
      },
    ],
    expectedClusterConfigParams: {
      name: 'hackathon-app',
      tags: [
        { key: 'env', value: 'development' },
        { key: 'hackathon', value: 'true' },
      ],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.FLEX,
      diskSizeGB: 10,
      diskIOPS: 1000,
    },
    referenceAnswer:
      'FLEX is perfect for short-term, cost-sensitive apps. Free tier is also available if you want a truly no cost option.',
    tags: ['hackathon', 'fast-setup', 'cheap', 'FLEX'],
  },

  // Archival Data - Creation flow
  {
    name: 'should recommend M20 for archival data storage',
    messages: [
      {
        role: 'user',
        content: "Need a cluster to store historical event logs that won't be queried often but must be retained.",
      },
    ],
    expectedClusterConfigParams: {
      name: 'event-logs-archive',
      tags: [{ key: 'env', value: 'archive' }],
      cloudProvider: BackingCloudProvider.AZURE,
      regionName: RegionNames.US_WEST_2,
      instanceSize: InstanceSizes.M20,
      diskSizeGB: 100,
      diskIOPS: 1000,
    },
    referenceAnswer: 'An M20 with modest IOPS and disk is a good low-cost, high-capacity option.',
    tags: ['archival', 'logs', 'storage', 'M20'],
  },

  // Ingest heavy backend - Creation flow
  {
    name: 'should recommend M50 for heavy IoT data ingestion',
    messages: [
      {
        role: 'user',
        content:
          'This cluster will be ingesting a few hundred thousand records per day from various IoT devices. What tier should I use?',
      },
    ],
    expectedClusterConfigParams: {
      name: 'iot-backend',
      tags: [
        { key: 'env', value: 'production' },
        { key: 'usecase', value: 'iot' },
      ],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M50,
      diskSizeGB: 750,
      diskIOPS: 3000,
    },
    referenceAnswer: 'Start with M50 for high-ingest pipelines to ensure capacity and throughput.',
    tags: ['iot', 'heavy-ingestion', 'high-throughput', 'M50'],
  },

  // Autoscaling - Feature Details
  {
    name: 'should explain autoscaling capabilities for existing cluster',
    messages: [
      {
        role: 'user',
        content: 'Can this cluster automatically scale during peak hours?',
      },
    ],
    clusterConfigSummary: {
      name: 'ecommerce-db',
      tags: [{ key: 'env', value: 'production' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M30,
      diskSizeGB: 40,
      diskIOPS: 3000,
    },
    referenceAnswer:
      "Yes — you can enable compute and storage auto-scaling from the Cluster Builder UI. It's available for all M10+ clusters.",
    tags: ['autoscaling', 'feature-details', 'peak-hours'],
  },

  // Encryption - Feature Details
  {
    name: 'should explain encryption features for financial data',
    messages: [
      {
        role: 'user',
        content: 'Is data encrypted at rest by default?',
      },
    ],
    clusterConfigSummary: {
      name: 'financial-data',
      tags: [
        { key: 'env', value: 'prod' },
        { key: 'compliance', value: 'yes' },
      ],
      cloudProvider: BackingCloudProvider.AZURE,
      regionName: RegionNames.US_WEST_2,
      instanceSize: InstanceSizes.M60,
      diskSizeGB: 320,
      diskIOPS: 3000,
    },
    referenceAnswer:
      'Yes — Atlas encrypts all data at rest and in transit by default, using cloud provider-managed keys. For custom KMS support, you can enable customer key management in the Security tab.',
    tags: ['encryption', 'security', 'compliance', 'feature-details'],
  },

  // Pausing - Feature Details
  {
    name: 'should explain cluster pausing for cost savings',
    messages: [
      {
        role: 'user',
        content: "Can I pause this cluster when it's not in use to save money?",
      },
    ],
    clusterConfigSummary: {
      name: 'temp-staging',
      tags: [{ key: 'env', value: 'staging' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 10,
      diskIOPS: 1000,
    },
    referenceAnswer:
      'M10–M40 clusters can be paused via the Atlas UI. Pausing halts compute charges but retains your data.',
    tags: ['pausing', 'cost-savings', 'staging', 'feature-details'],
  },

  // Serverless - Feature Details (deprecated)
  {
    name: 'should explain serverless is no longer supported',
    messages: [
      {
        role: 'user',
        content: 'Can I use Serverless for this project?',
      },
    ],
    referenceAnswer:
      'Serverless clusters are no longer supported in Atlas. We recommend using FLEX for similar low-cost, scalable workloads.',
    tags: ['serverless', 'deprecated', 'flex-alternative', 'feature-details'],
  },

  // Low CPU - Feature Details
  {
    name: 'should explain low-CPU instance options',
    messages: [
      {
        role: 'user',
        content: "We don't need a lot of compute but want more memory. Anything cost-effective?",
      },
    ],
    referenceAnswer:
      'Yes — Atlas offers low-CPU instance types like R40, R60, etc., which provide more RAM per dollar. These are available starting at M40+.',
    tags: ['low-cpu', 'memory-optimized', 'cost-effective', 'feature-details'],
  },

  // NVME - Feature Details
  {
    name: 'should explain NVME storage availability',
    messages: [
      {
        role: 'user',
        content: 'Do you offer NVME disks for better throughput?',
      },
    ],
    referenceAnswer:
      'Yes — NVME storage is available for AWS clusters starting at M40_NVME and above. You can select it in the form to the right.',
    tags: ['nvme', 'storage', 'throughput', 'feature-details'],
  },

  // Concurrent connections - Feature Details
  {
    name: 'should explain connection limits for M10 cluster',
    messages: [
      {
        role: 'user',
        content: 'How many concurrent users can this M10 cluster support?',
      },
    ],
    clusterConfigSummary: {
      name: 'realtime-chat',
      tags: [{ key: 'env', value: 'production' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_WEST_2,
      instanceSize: InstanceSizes.M10,
      diskSizeGB: 10,
      diskIOPS: 1000,
    },
    referenceAnswer:
      'An M10 supports up to 100 concurrent connections by default. You can view and monitor usage in the Metrics tab. For more users, consider upgrading to M20 or higher.',
    tags: ['connections', 'limits', 'M10', 'feature-details'],
  },

  // Disk scaling - Feature Details
  {
    name: 'should explain disk auto-expansion for free tier',
    messages: [
      {
        role: 'user',
        content: 'What happens if we run out of disk space?',
      },
    ],
    clusterConfigSummary: {
      name: 'cms-dev',
      tags: [{ key: 'env', value: 'dev' }],
      cloudProvider: BackingCloudProvider.GCP,
      regionName: RegionNames.US_CENTRAL,
      instanceSize: InstanceSizes.M0,
    },
    referenceAnswer:
      'Atlas offers disk auto-expansion by default for M10+ clusters. When enabled, your disk size grows automatically to avoid capacity issues. Would you like me to create an M10 cluster for you?',
    tags: ['disk-scaling', 'auto-expansion', 'free-tier', 'feature-details'],
  },

  // Security / access management - Feature Details
  {
    name: 'should explain team access management',
    messages: [
      {
        role: 'user',
        content: 'How can I safely share this cluster with other dev teams?',
      },
    ],
    referenceAnswer:
      'Use Project-level access controls and IP whitelisting to manage access. You can assign roles like read-only or read/write to specific users via the Atlas UI.',
    tags: ['security', 'access-control', 'team-sharing', 'feature-details'],
  },

  // Backups on an M0 - Feature Details
  {
    name: 'should explain backup limitations on free tier',
    messages: [
      {
        role: 'user',
        content: 'Can I back up my data just in case I break something?',
      },
    ],
    clusterConfigSummary: {
      name: 'test-db',
      tags: [{ key: 'env', value: 'dev' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.M0,
      diskSizeGB: 0,
      diskIOPS: 0,
    },
    referenceAnswer:
      "Backups aren't supported on free-tier (M0) clusters. To enable daily snapshots, upgrade to an M10 or higher. Flex also have backup options.",
    tags: ['backups', 'free-tier', 'limitations', 'feature-details'],
  },

  // Storage limits on a Flex - Feature Details
  {
    name: 'should explain Flex storage limitations and upgrade path',
    messages: [
      {
        role: 'user',
        content: 'Our dev cluster on Flex is running out of space — can we increase storage?',
      },
    ],
    clusterConfigSummary: {
      name: 'internal-tools',
      tags: [{ key: 'env', value: 'dev' }],
      cloudProvider: BackingCloudProvider.AWS,
      regionName: RegionNames.US_EAST_1,
      instanceSize: InstanceSizes.FLEX,
      diskSizeGB: 10,
      diskIOPS: 1000,
    },
    referenceAnswer:
      "Flex clusters scale automatically up to 5GB based on usage. If you're hitting limits, consider upgrading to an M10 to get dedicated resources and configurable storage.",
    tags: ['flex', 'storage-limits', 'upgrade-path', 'feature-details'],
  },
];
