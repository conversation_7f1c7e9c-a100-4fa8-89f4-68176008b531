import { Tool, tool } from 'ai';
import { z } from 'zod';

import { BillingEstimate } from '@packages/types/nds/billingEstimate';
import { ClusterDescription } from '@packages/types/nds/clusterDescription';
import {
  BackingCloudProvider,
  InstanceSize,
  StandardInstanceSize,
  StandardInstanceSizes,
} from '@packages/types/nds/provider';
import { RegionName } from '@packages/types/nds/region';
import { CloudTeams } from '@packages/types/observability';
import { Tag, TagVisibility } from '@packages/types/resourceTagging';

import { generateClusterConfigToolInstructions } from '@packages/ai-assistant/instructions/clusterConfigAIAssistantInstructions';
import { validateClusterConfig } from '@packages/ai-assistant/utils/clusterConfigValidation';
import { ClusterConfigurationParameters } from '@packages/cluster-configuration/parameterizedTransformationUtility';
import { sendError } from '@packages/observability';

export const CLUSTER_CONFIG_TOOL_NAME = 'clusterConfigTool';

/**
 * Zod Schema for MongoDB Atlas Cluster Configuration Params that the AI Assistant will generate.
 */
export const ClusterConfigSchema = z.object({
  // Required fields - minimal schema for AI SDK compatibility
  name: z.string().describe('A descriptive name for the cluster'),
  cloudProvider: z.nativeEnum(BackingCloudProvider).describe('The backing cloud provider that will host the database'),
  regionName: z.string().describe('The cloud data center location'),
  instanceSize: z
    .enum(Object.keys(StandardInstanceSizes) as [InstanceSize, ...Array<InstanceSize>])
    .describe('The hardware resources allocated to the cluster'),

  // Optional fields
  tags: z
    .array(
      z.object({
        key: z.string().describe('Tag key'),
        value: z.string().describe('Tag value'),
      })
    )
    .optional()
    .describe('Metadata tags for organizing and categorizing the cluster'),
  nvme: z.boolean().nullish().describe('Whether to use NVME SSD storage'),
  lowCpu: z.boolean().nullish().describe('Whether to use a low CPU version'),
  diskSizeGB: z.number().nullish().describe('The total storage capacity in GB'),
  diskIOPS: z.number().nullish().describe('Input/Output Operations Per Second'),
});

/**
 * Type inferred from the Zod schema for type safety
 */
export type ClusterConfigSchemaType = z.infer<typeof ClusterConfigSchema>;

/**
 * Transforms the Zod schema output to ClusterConfigurationParameters interface
 * This ensures type safety between the AI tool output and the expected application interface
 */
export const transformToClusterConfigurationParameters = (
  schemaOutput: ClusterConfigSchemaType
): ClusterConfigurationParameters => {
  const transformedTags: Array<Tag> = (schemaOutput.tags || []).map((tag) => ({
    key: tag.key,
    value: tag.value,
    visibility: TagVisibility.PUBLIC,
  }));

  return {
    clusterName: schemaOutput.name,
    cloudProvider: schemaOutput.cloudProvider,
    region: schemaOutput.regionName as RegionName,
    instanceSize: schemaOutput.instanceSize as StandardInstanceSize,
    tags: transformedTags,
  };
};

/**
 * Returns a function tool for recommending MongoDB Atlas cluster configuration params.
 */
/**
 * Interface for the tool's return type using discriminated unions for better type safety
 */
export type ClusterConfigToolResult =
  | {
      success: true;
      message: string;
      clusterConfigurationParameters: ClusterConfigurationParameters;
      summary: string;
    }
  | {
      success: false;
      message: string;
      clusterConfigurationParameters: null;
      errors: Array<string>;
    };

export function createClusterConfigTool({
  originalClusterConfigSummary,
  currentClusterConfigSummary,
  isEdit = false,
  billingEstimate,
  clusterDescriptions,
}: {
  originalClusterConfigSummary?: Record<string, any>;
  currentClusterConfigSummary?: Record<string, any>;
  isEdit?: boolean;
  billingEstimate?: BillingEstimate;
  clusterDescriptions?: Array<ClusterDescription>;
} = {}): Tool {
  return tool({
    inputSchema: ClusterConfigSchema,
    description: generateClusterConfigToolInstructions({
      originalClusterConfigSummary,
      currentClusterConfigSummary,
      isEdit,
      billingEstimate,
      clusterDescriptions,
    }),
    execute: async (params: ClusterConfigSchemaType): Promise<ClusterConfigToolResult> => {
      try {
        // Use shared validation logic
        const validationResult = validateClusterConfig(params);

        // Check if validation passed
        if (!validationResult.isValid) {
          return {
            success: false,
            message: 'Cluster configuration validation failed',
            clusterConfigurationParameters: null,
            errors: [...validationResult.errors],
          };
        }

        // Transform to the expected ClusterConfigurationParameters type
        const clusterConfigurationParameters = transformToClusterConfigurationParameters(params);

        // Return successful result with structured data
        return {
          success: true,
          message: 'Cluster configuration is valid',
          clusterConfigurationParameters,
          summary: `Validated cluster configuration: ${params.name} on ${params.cloudProvider} in ${params.regionName} with ${params.instanceSize} instance`,
        };
      } catch (error) {
        sendError({
          error: error,
          team: CloudTeams.AtlasGrowth,
          extras: {
            params: JSON.stringify(params),
          },
        });

        // Return failure result for unexpected errors
        return {
          success: false,
          message: 'An unexpected error occurred. Please try again.',
          clusterConfigurationParameters: null,
          errors: [
            `An unexpected error occurred while validating cluster configuration with parameters: ${JSON.stringify(params)}`,
          ],
        };
      }
    },
  });
}
