import { BackingCloudProvider, SupportedInstanceSizes } from '@packages/types/nds/provider';
import { RegionNames } from '@packages/types/nds/region';
import { CloudTeams } from '@packages/types/observability';

import { sendError } from '@packages/observability';

import { ClusterConfigSchema, createClusterConfigTool } from './clusterConfigTool';

jest.mock('@packages/observability', () => ({
  sendError: jest.fn(),
}));

jest.mock('@packages/common/utils/clusterDescription', () => ({
  CLUSTER_NAME_CHAR_LIMIT: 64,
  isNameValid: jest.fn(),
}));

describe('clusterConfigTool', () => {
  describe('createClusterConfigTool', () => {
    const mockSendError = sendError as jest.MockedFunction<typeof sendError>;
    const mockIsNameValid = require('@packages/common/utils/clusterDescription')
      .isNameValid as jest.MockedFunction<any>;

    beforeEach(() => {
      jest.clearAllMocks();
      mockIsNameValid.mockReturnValue(true);
    });

    it('should return a tool with correct properties', () => {
      const tool = createClusterConfigTool();

      expect(tool).toBeDefined();
      expect(tool.inputSchema).toBeDefined();
      expect(tool.inputSchema).toBe(ClusterConfigSchema);
    });

    it('should have the ClusterConfigSchema as inputSchema', () => {
      const tool = createClusterConfigTool();

      expect(tool.inputSchema).toBe(ClusterConfigSchema);
    });

    it('should send error to Sentry when unexpected error occurs', async () => {
      // Mock isNameValid to throw an unexpected error
      mockIsNameValid.mockImplementation(() => {
        throw new Error('Invalid cluster name');
      });

      const tool = createClusterConfigTool();
      const testParams = {
        name: 'test-cluster',
        cloudProvider: BackingCloudProvider.AWS,
        regionName: RegionNames.US_EAST_1,
        instanceSize: SupportedInstanceSizes.M10,
      };

      const result = await tool.execute!(testParams, {
        toolCallId: 'test-call-id',
        messages: [],
      });

      expect(mockSendError).toHaveBeenCalledWith({
        error: expect.any(Error),
        team: CloudTeams.AtlasGrowth,
        extras: {
          params: JSON.stringify(testParams),
        },
      });

      expect(result).toEqual({
        success: false,
        message: 'An unexpected error occurred. Please try again.',
        clusterConfigurationParameters: null,
        errors: [
          `An unexpected error occurred while validating cluster configuration with parameters: ${JSON.stringify(testParams)}`,
        ],
      });
    });
  });

  describe('ClusterConfigSchema', () => {
    describe('valid configurations', () => {
      it('should validate minimal required configuration', () => {
        const validConfig = {
          name: 'test-cluster',
          cloudProvider: BackingCloudProvider.AWS,
          regionName: RegionNames.US_EAST_1,
          instanceSize: SupportedInstanceSizes.M10,
        };

        const result = ClusterConfigSchema.safeParse(validConfig);
        expect(result.success).toBe(true);
      });

      it('should validate complete configuration with all optional fields', () => {
        const validConfig = {
          name: 'production-cluster',
          cloudProvider: BackingCloudProvider.GCP,
          regionName: RegionNames.CENTRAL_US,
          instanceSize: SupportedInstanceSizes.M40,
          tags: [
            { key: 'env', value: 'production' },
            { key: 'team', value: 'backend' },
          ],
          nvme: true,
          lowCpu: false,
          diskSizeGB: 100,
          diskIOPS: 3000,
        };

        const result = ClusterConfigSchema.safeParse(validConfig);
        expect(result.success).toBe(true);
      });

      it('should validate region-provider combinations across all providers', () => {
        // Test multiple valid combinations for each provider
        const validConfigs = [
          // AWS regions
          {
            name: 'aws-east-cluster',
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.US_EAST_1,
            instanceSize: SupportedInstanceSizes.M10,
          },
          {
            name: 'aws-west-cluster',
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.US_WEST_1,
            instanceSize: SupportedInstanceSizes.M10,
          },
          {
            name: 'aws-eu-cluster',
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.EU_WEST_1,
            instanceSize: SupportedInstanceSizes.M10,
          },
          // GCP regions
          {
            name: 'gcp-central-cluster',
            cloudProvider: BackingCloudProvider.GCP,
            regionName: RegionNames.CENTRAL_US,
            instanceSize: SupportedInstanceSizes.M10,
          },
          {
            name: 'gcp-eastern-cluster',
            cloudProvider: BackingCloudProvider.GCP,
            regionName: RegionNames.EASTERN_US,
            instanceSize: SupportedInstanceSizes.M10,
          },
          {
            name: 'gcp-western-cluster',
            cloudProvider: BackingCloudProvider.GCP,
            regionName: RegionNames.WESTERN_US,
            instanceSize: SupportedInstanceSizes.M10,
          },
          // Azure regions
          {
            name: 'azure-east-cluster',
            cloudProvider: BackingCloudProvider.AZURE,
            regionName: RegionNames.US_EAST,
            instanceSize: SupportedInstanceSizes.M10,
          },
          {
            name: 'azure-central-cluster',
            cloudProvider: BackingCloudProvider.AZURE,
            regionName: RegionNames.US_CENTRAL,
            instanceSize: SupportedInstanceSizes.M10,
          },
          {
            name: 'azure-west-cluster',
            cloudProvider: BackingCloudProvider.AZURE,
            regionName: RegionNames.US_WEST,
            instanceSize: SupportedInstanceSizes.M10,
          },
        ];

        validConfigs.forEach((config) => {
          const result = ClusterConfigSchema.safeParse(config);
          expect(result.success).toBe(true);
        });
      });

      it('should validate different instance sizes', () => {
        const freeConfig = {
          name: 'free-cluster',
          cloudProvider: BackingCloudProvider.AWS,
          regionName: RegionNames.US_EAST_1,
          instanceSize: SupportedInstanceSizes.M0,
        };

        const flexConfig = {
          name: 'flex-cluster',
          cloudProvider: BackingCloudProvider.GCP,
          regionName: RegionNames.CENTRAL_US,
          instanceSize: SupportedInstanceSizes.FLEX,
        };

        const dedicatedConfig = {
          name: 'dedicated-cluster',
          cloudProvider: BackingCloudProvider.AZURE,
          regionName: RegionNames.US_EAST_2,
          instanceSize: SupportedInstanceSizes.M700,
        };

        expect(ClusterConfigSchema.safeParse(freeConfig).success).toBe(true);
        expect(ClusterConfigSchema.safeParse(flexConfig).success).toBe(true);
        expect(ClusterConfigSchema.safeParse(dedicatedConfig).success).toBe(true);
      });

      it('should handle empty tags array', () => {
        const validConfig = {
          name: 'test-cluster',
          cloudProvider: BackingCloudProvider.AWS,
          regionName: RegionNames.US_EAST_1,
          instanceSize: SupportedInstanceSizes.M10,
          tags: [],
        };

        const result = ClusterConfigSchema.safeParse(validConfig);
        expect(result.success).toBe(true);
      });

      it('should handle minimum valid disk size and IOPS', () => {
        const validConfig = {
          name: 'test-cluster',
          cloudProvider: BackingCloudProvider.AWS,
          regionName: RegionNames.US_EAST_1,
          instanceSize: SupportedInstanceSizes.M10,
          diskSizeGB: 10,
          diskIOPS: 100,
        };

        const result = ClusterConfigSchema.safeParse(validConfig);
        expect(result.success).toBe(true);
      });

      it('should handle large disk size and IOPS values', () => {
        const validConfig = {
          name: 'test-cluster',
          cloudProvider: BackingCloudProvider.AWS,
          regionName: RegionNames.US_EAST_1,
          instanceSize: SupportedInstanceSizes.M700,
          diskSizeGB: 16000,
          diskIOPS: 40000,
        };

        const result = ClusterConfigSchema.safeParse(validConfig);
        expect(result.success).toBe(true);
      });

      it('should handle multiple tags with various key-value pairs', () => {
        const validConfig = {
          name: 'test-cluster',
          cloudProvider: BackingCloudProvider.AWS,
          regionName: RegionNames.US_EAST_1,
          instanceSize: SupportedInstanceSizes.M10,
          tags: [
            { key: 'environment', value: 'production' },
            { key: 'team', value: 'data-platform' },
            { key: 'cost-center', value: '12345' },
            { key: 'project', value: 'analytics-v2' },
          ],
        };

        const result = ClusterConfigSchema.safeParse(validConfig);
        expect(result.success).toBe(true);
      });

      it('should validate various valid cluster names', () => {
        const validNames = [
          'cluster1',
          'my-cluster',
          'production-db',
          'test123',
          'a',
          '1',
          'cluster-with-many-hyphens-and-numbers-123',
          'MyCluster',
          'CLUSTER',
          'cluster1234567890',
          // Names ending with hyphens are valid according to the regex
          'cluster-',
          'test-cluster-',
          // 64 character name (at the limit)
          'a123456789012345678901234567890123456789012345678901234567890123',
        ];

        validNames.forEach((name) => {
          const validConfig = {
            name,
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.US_EAST_1,
            instanceSize: SupportedInstanceSizes.M10,
          };

          const result = ClusterConfigSchema.safeParse(validConfig);
          expect(result.success).toBe(true);
        });
      });
    });

    describe('invalid configurations', () => {
      it('should reject configuration missing required fields', () => {
        const invalidConfigs = [
          // Missing name
          {
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.US_EAST_1,
            instanceSize: SupportedInstanceSizes.M10,
          },
          // Missing cloudProvider
          {
            name: 'test-cluster',
            regionName: RegionNames.US_EAST_1,
            instanceSize: SupportedInstanceSizes.M10,
          },
          // Missing regionName
          {
            name: 'test-cluster',
            cloudProvider: BackingCloudProvider.AWS,
            instanceSize: SupportedInstanceSizes.M10,
          },
          // Missing instanceSize
          {
            name: 'test-cluster',
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.US_EAST_1,
          },
        ];

        invalidConfigs.forEach((config) => {
          const result = ClusterConfigSchema.safeParse(config);
          expect(result.success).toBe(false);
        });
      });

      it('should reject invalid cloud provider values', () => {
        const invalidConfig = {
          name: 'test-cluster',
          cloudProvider: 'INVALID_PROVIDER',
          regionName: RegionNames.US_EAST_1,
          instanceSize: SupportedInstanceSizes.M10,
        };

        const result = ClusterConfigSchema.safeParse(invalidConfig);
        expect(result.success).toBe(false);
      });

      // Note: The schema is permissive for regionName and instanceSize (accepts any string)
      // The actual validation happens in the execute function
      it('should accept any string for region name (validation happens in execute)', () => {
        const configWithInvalidRegion = {
          name: 'test-cluster',
          cloudProvider: BackingCloudProvider.AWS,
          regionName: 'INVALID_REGION',
          instanceSize: SupportedInstanceSizes.M10,
        };

        const result = ClusterConfigSchema.safeParse(configWithInvalidRegion);
        expect(result.success).toBe(true);
      });

      it('should accept any string for instance size (validation happens in execute)', () => {
        const configWithInvalidInstanceSize = {
          name: 'test-cluster',
          cloudProvider: BackingCloudProvider.AWS,
          regionName: RegionNames.US_EAST_1,
          instanceSize: 'M2',
        };

        const result = ClusterConfigSchema.safeParse(configWithInvalidInstanceSize);
        expect(result.success).toBe(true);
      });

      it('should reject invalid tag structure', () => {
        const invalidConfigs = [
          // Tags with missing key
          {
            name: 'test-cluster',
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.US_EAST_1,
            instanceSize: SupportedInstanceSizes.M10,
            tags: [{ value: 'production' }],
          },
          // Tags with missing value
          {
            name: 'test-cluster',
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.US_EAST_1,
            instanceSize: SupportedInstanceSizes.M10,
            tags: [{ key: 'env' }],
          },
          // Tags with non-string values
          {
            name: 'test-cluster',
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.US_EAST_1,
            instanceSize: SupportedInstanceSizes.M10,
            tags: [{ key: 123, value: 'production' }],
          },
        ];

        invalidConfigs.forEach((config) => {
          const result = ClusterConfigSchema.safeParse(config);
          expect(result.success).toBe(false);
        });
      });

      // Note: The schema is permissive for disk size and IOPS (accepts any number)
      // The actual validation happens in the execute function
      it('should accept any number for disk size (validation happens in execute)', () => {
        const configWithInvalidDiskSize = {
          name: 'test-cluster',
          cloudProvider: BackingCloudProvider.AWS,
          regionName: RegionNames.US_EAST_1,
          instanceSize: SupportedInstanceSizes.M10,
          diskSizeGB: 5,
        };

        const result = ClusterConfigSchema.safeParse(configWithInvalidDiskSize);
        expect(result.success).toBe(true);
      });

      it('should accept any number for disk IOPS (validation happens in execute)', () => {
        const configWithInvalidIOPS = {
          name: 'test-cluster',
          cloudProvider: BackingCloudProvider.AWS,
          regionName: RegionNames.US_EAST_1,
          instanceSize: SupportedInstanceSizes.M10,
          diskIOPS: 50,
        };

        const result = ClusterConfigSchema.safeParse(configWithInvalidIOPS);
        expect(result.success).toBe(true);
      });

      it('should reject non-boolean values for nvme and lowCpu', () => {
        const invalidConfigs = [
          // Invalid nvme value
          {
            name: 'test-cluster',
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.US_EAST_1,
            instanceSize: SupportedInstanceSizes.M10,
            nvme: 'true',
          },
          // Invalid lowCpu value
          {
            name: 'test-cluster',
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.US_EAST_1,
            instanceSize: SupportedInstanceSizes.M10,
            lowCpu: 1,
          },
        ];

        invalidConfigs.forEach((config) => {
          const result = ClusterConfigSchema.safeParse(config);
          expect(result.success).toBe(false);
        });
      });

      // Note: The schema is permissive for region-provider combinations
      // The actual validation happens in the execute function
      it('should accept mismatched region-provider combinations (validation happens in execute)', () => {
        const invalidConfigs = [
          // AWS region with GCP provider (only test if regions exist)
          ...(RegionNames.US_EAST_1
            ? [
                {
                  name: 'test-cluster',
                  cloudProvider: BackingCloudProvider.GCP,
                  regionName: RegionNames.US_EAST_1, // AWS region
                  instanceSize: SupportedInstanceSizes.M10,
                },
              ]
            : []),
          // GCP region with AWS provider (only test if regions exist)
          ...(RegionNames.CENTRAL_US
            ? [
                {
                  name: 'test-cluster',
                  cloudProvider: BackingCloudProvider.AWS,
                  regionName: RegionNames.CENTRAL_US, // GCP region
                  instanceSize: SupportedInstanceSizes.M10,
                },
              ]
            : []),
          // Azure region with AWS provider (only test if regions exist)
          ...(RegionNames.US_CENTRAL
            ? [
                {
                  name: 'test-cluster',
                  cloudProvider: BackingCloudProvider.AWS,
                  regionName: RegionNames.US_CENTRAL, // Azure region
                  instanceSize: SupportedInstanceSizes.M10,
                },
              ]
            : []),
        ];

        invalidConfigs.forEach((config) => {
          const result = ClusterConfigSchema.safeParse(config);
          expect(result.success).toBe(true);
        });
      });

      // Note: The schema is permissive for cluster names (accepts any string)
      // The actual validation happens in the execute function
      it('should accept any string for cluster name (validation happens in execute)', () => {
        const invalidNames = [
          '',
          'a12345678901234567890123456789012345678901234567890012345678901234',
          'very-long-cluster-name-that-exceeds-the-maximum-allowed-length-of-64-characters',
          '-cluster', // starts with hyphen
          'cluster name', // contains space
          'cluster.name', // contains period
        ];

        invalidNames.forEach((name) => {
          const config = {
            name,
            cloudProvider: BackingCloudProvider.AWS,
            regionName: RegionNames.US_EAST_1,
            instanceSize: SupportedInstanceSizes.M10,
          };

          const result = ClusterConfigSchema.safeParse(config);
          expect(result.success).toBe(true);
        });
      });
    });
  });
});
