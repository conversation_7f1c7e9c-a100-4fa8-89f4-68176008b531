import { Provider } from 'react-redux';

import { FederationSettings, IdpType, PemFileInfo, Protocol } from '@packages/types/federation';

import * as settings from '@packages/redux/common/settings';
import { createMainReduxStore } from '@packages/redux/main/test-utils/reduxHelpers';

import Deferred from '@packages/common/utils/Deferred';
import {
  IdentityProviderWizard,
  IdentityProviderWizardProps,
} from '@packages/federation/components/identityProviders/IdentityProviderWizard';
import { PopulatedFederationSettingsProvider } from '@packages/federation/context/FederationSettingsContext';
import { fireEvent, render, screen, within } from '@packages/react-testing-library';

describe('@packages/federation/components/identityProviders/IdentityProviderWizard', () => {
  type IdpCertificateSubmission = 'uploaded' | 'pasted';

  let historyStub: jest.Mock;
  let uploadCertificateDeferred: Deferred<PemFileInfo>;
  let onSubmitPemDataStub: jest.Mock;
  let submitDeferred: Deferred<{
    oktaIdpId: string;
    acsUrl: string;
    audienceUri: string;
  }>;
  let submitStub: jest.Mock;
  let setDisableOidcRadioBtnStub: jest.Mock;

  const federationSettingsId = 'fedSettings1';
  let federationSettings = {
    _id: federationSettingsId,
    identityProviders: [],
    domains: [],
    connectedOrgConfigs: [],
    restrictOrgMembershipEnabled: false,
  } as FederationSettings;

  const reduxStore = createMainReduxStore({
    viewer: {
      dateFormatCode: 'us1',
    },
  });

  const renderComponent = (
    props = {} as Omit<
      IdentityProviderWizardProps,
      'history' | 'onSubmitSamlIdentityProvider' | 'onSubmitOidcIdentityProvider' | 'setDisableOidcRadioBtn'
    >
  ) => {
    return render(
      <Provider store={reduxStore}>
        <PopulatedFederationSettingsProvider federationSettings={federationSettings}>
          <IdentityProviderWizard
            history={historyStub}
            onSubmitPemData={onSubmitPemDataStub}
            onSubmitSamlIdentityProvider={submitStub}
            onSubmitOidcIdentityProvider={submitStub}
            setDisableOidcRadioBtn={setDisableOidcRadioBtnStub}
            {...props}
          />
        </PopulatedFederationSettingsProvider>
      </Provider>
    );
  };

  let renderResult: ReturnType<typeof renderComponent>;

  beforeEach(() => {
    jest.spyOn(settings, 'isDatabaseIdpDiscoveryEnabled').mockReturnValue(true);
    historyStub = jest.fn();

    uploadCertificateDeferred = new Deferred<PemFileInfo>();
    onSubmitPemDataStub = jest.fn().mockReturnValue(uploadCertificateDeferred.promise());
    submitDeferred = new Deferred<{
      oktaIdpId: string;
      acsUrl: string;
      audienceUri: string;
    }>();
    submitStub = jest.fn().mockReturnValue(submitDeferred.promise());
    setDisableOidcRadioBtnStub = jest.fn();

    // eslint-disable-next-line testing-library/no-render-in-lifecycle
    renderResult = renderComponent();
  });

  afterEach(() => {
    renderResult.unmount();
    jest.restoreAllMocks();
  });

  const identityProviderFormTests = (idpCertificateSubmission: IdpCertificateSubmission) => {
    describe('and the certificate parse fails with a parse error', () => {
      beforeEach(() => {
        uploadCertificateDeferred.reject({ errorCode: 'CERTIFICATE_PARSE_ERROR' });
      });

      it('renders the SamlIdentityProviderForm with certificate error', () => {
        expect(screen.getByTestId('federation-error-message'));
      });

      it('renders with a disabled "Continue" button', () => {
        expect(screen.getByTestId('submit-button')).toBeAriaDisabled();
      });
    });

    describe('and the certificate is parsed successfully', () => {
      beforeEach(() => {
        uploadCertificateDeferred.resolve({
          fileName: 'cert.pem',
          certificates: [
            {
              content: 'cert',
              notAfter: new Date().toISOString(),
              notBefore: '',
            },
          ],
        });
      });

      it('does not render an Alert', () => {
        expect(screen.queryByTestId('federation-error-message')).not.toBeInTheDocument();
      });

      if (idpCertificateSubmission === 'uploaded') {
        it('does not render the plain text IdP certificate text box', () => {
          expect(screen.queryByTestId('plainTextIdpCertificate')).not.toBeInTheDocument();
        });
      } else {
        it('renders the enabled plain text IdP certificate text box', () => {
          expect(screen.getByTestId('plainTextIdpCertificate')).toBeInTheDocument();
          expect(screen.getByTestId('plainTextIdpCertificate')).toBeEnabled();
        });
      }

      describe('and the "Continue" button is clicked', () => {
        beforeEach(() => {
          fireEvent.click(screen.getByTestId('submit-button'));
        });

        it('calls setDisableOidcRadioBtn to disable OIDC radio button', () => {
          expect(setDisableOidcRadioBtnStub).toHaveBeenCalledWith(true);
        });

        it('renders with the form inputs disabled', () => {
          [
            screen.getByTestId('configName'),
            screen.getByTestId('configDesc'),
            screen.getByTestId('issuerUri'),
            screen.getByTestId('ssoUrl'),
            screen.getByTestId('slug'),
          ].forEach((element) => expect(element).toBeDisabled());

          [screen.getByTestId('requestBinding'), screen.getByTestId('responseSignatureAlgorithm')].forEach((element) =>
            expect(element).toBeAriaDisabled()
          );
        });

        it('renders with the "Continue" button in the loading state', () => {
          const button = screen.getByTestId('submit-button');
          expect(button).toBeAriaDisabled();
          expect(within(button).getByTestId('button-loader-icon')).toBeInTheDocument();
        });

        describe('and the submission succeeds', () => {
          const createdIdentityProvider = {
            oktaIdpId: 'okta id',
            acsUrl: 'acs url',
            audienceUri: 'audience uri',
          };

          beforeEach(() => {
            submitDeferred.resolve(createdIdentityProvider);
          });

          it('renders the "Download metadata" step', () => {
            expect(screen.getByTestId('multi-step-wizard')).toHaveTextContent('Download metadata');
          });

          it('renders the "Download metadata" button with the correct URL', () => {
            expect(screen.getByRole('link', { name: /download metadata/i })).toHaveAttribute(
              'href',
              `/federationSettings/${federationSettingsId}/identityProviders/${createdIdentityProvider.oktaIdpId}/metadata.xml`
            );
          });

          it('renders the ACS URL copyable command', () => {
            expect(screen.getAllByTestId('copy-command')[0]).toHaveTextContent(createdIdentityProvider.acsUrl);
          });

          it('renders the Audience URI copyable command', () => {
            expect(screen.getAllByTestId('copy-command')[1]).toHaveTextContent(createdIdentityProvider.audienceUri);
          });
        });

        describe('and the submission fails with validation errors', () => {
          beforeEach(() => {
            submitDeferred.reject({
              errorCode: 'VALIDATION_ERROR',
              errors: {
                slug: 'invalid',
                issuerUri: 'invalid',
                ssoUrl: 'invalid',
                displayName: 'invalid',
              },
            });
          });

          it('renders with a disabled "Continue" button', () => {
            expect(screen.getByTestId('submit-button')).toBeAriaDisabled();
          });

          it('renders with the offending inputs in an error state', () => {
            expect(screen.getByTestId('issuerUri')).toHaveAttribute('aria-invalid', 'true');
            expect(screen.getByTestId('ssoUrl')).toHaveAttribute('aria-invalid', 'true');
            expect(screen.getByTestId('configName')).toHaveAttribute('aria-invalid', 'true');
            expect(screen.getByTestId('slug')).toHaveAttribute('aria-invalid', 'true');
          });

          it('renders the correct error messages', () => {
            expect(screen.getByText('The Issuer URI is invalid')).toBeInTheDocument();
            expect(screen.getByText('The URL is invalid')).toBeInTheDocument();
            expect(screen.getByText('The display name is invalid')).toBeInTheDocument();
            expect(
              screen.getByText(
                'Login URL may only contain digits (0-9), lowercase letters (a-z), and hypens (-), but cannot begin or end with a hyphen'
              )
            ).toBeInTheDocument();
          });

          describe('and the invalid fields are updated', () => {
            beforeEach(() => {
              fireEvent.change(screen.getByTestId('issuerUri'), { target: { value: 'new value' } });
              fireEvent.change(screen.getByTestId('ssoUrl'), { target: { value: 'new value' } });
              fireEvent.change(screen.getByTestId('configName'), { target: { value: 'new value' } });
              fireEvent.change(screen.getByTestId('slug'), { target: { value: 'new value' } });
            });

            it('renders with an enabled "Continue" button', () => {
              expect(screen.getByTestId('submit-button')).toBeEnabled();
            });
          });
        });

        describe('and the submission fails with an unexpected error', () => {
          beforeEach(() => {
            submitDeferred.reject({ errorCode: 'CERTIFICATE_ERROR' });
          });

          it('renders an alert', () => {
            expect(screen.getByTestId('alert')).toBeInTheDocument();
          });

          it('renders with an enabled "Continue" button', () => {
            expect(screen.getByTestId('submit-button')).toBeEnabled();
          });
        });

        describe('and the submission fails with an unexpected error', () => {
          beforeEach(() => {
            submitDeferred.reject({ errorCode: 'SOME_UNEXPECTED_VALUE' });
          });

          it('renders an alert', () => {
            expect(screen.getByTestId('alert')).toBeInTheDocument();
          });

          it('renders with an enabled "Continue" button', () => {
            expect(screen.getByTestId('submit-button')).toBeEnabled();
          });
        });
      });
    });
  };

  describe('when identity provider protocol is SAML', () => {
    describe('when rendered with default props', () => {
      beforeEach(() => {
        renderResult.unmount();

        // eslint-disable-next-line testing-library/no-render-in-lifecycle
        renderResult = renderComponent({
          centralUrl: 'http://example.com',
          protocol: Protocol.SAML,
          idpType: IdpType.WORKFORCE,
        });
      });

      afterEach(() => {
        renderResult.unmount();
      });

      it('renders the first step', () => {
        expect(screen.getByTestId('multi-step-wizard')).toHaveTextContent('Setup Config');
      });

      it('renders the SamlIdentityProviderForm component', () => {
        expect(screen.getByTestId('saml-identity-provider-form')).toBeInTheDocument();
      });

      it('renders with the "Continue" button disabled', () => {
        expect(screen.getByTestId('submit-button')).toHaveTextContent('Continue');
        expect(screen.getByTestId('submit-button')).toBeAriaDisabled();
      });

      it('renders with a "Cancel" button that leads to the Identity Providers page', () => {
        expect(screen.getByTestId('cancel-button')).toBeInTheDocument();
      });

      it('renders with a "Back" button', () => {
        expect(screen.getByTestId('back-button')).toBeInTheDocument();
      });

      describe('and the optional paste certificate link is clicked', () => {
        beforeEach(() => {
          fireEvent.click(screen.getByTestId('optionalPasteCertLink'));
        });

        it('renders the paste IdP certificate text modal', () => {
          expect(screen.getByTestId('plainTextIdpCertificate')).toBeInTheDocument();
        });

        describe('and the Identity Provider form has content in the plain text certificate box and has a file uploaded', () => {
          beforeEach(() => {
            fireEvent.change(screen.getByTestId('plainTextIdpCertificate'), {
              target: { value: 'pasted cert content' },
            });
            fireEvent.change(screen.getByTestId('certificate'), {
              target: { files: [new File([''], 'cert.pem', { type: 'application/x-pem-file' })] },
            });
          });

          it('clears and disables the plain text certificate text box', () => {
            expect(screen.getByTestId('plainTextIdpCertificate')).toHaveValue('');
            expect(screen.getByTestId('plainTextIdpCertificate')).toBeAriaDisabled();
          });
        });
      });

      describe('and the Identity Provider form is completed', () => {
        describe('by uploading a certificate', () => {
          beforeEach(() => {
            fireEvent.change(screen.getByTestId('configName'), { target: { value: 'config' } });
            fireEvent.change(screen.getByTestId('certificate'), {
              target: { files: [new File([''], 'cert.pem', { type: 'application/x-pem-file' })] },
            });
            fireEvent.change(screen.getByTestId('slug'), { target: { value: 'slug' } });
          });

          identityProviderFormTests('uploaded');
        });

        describe('by clicking the optional paste certificate link', () => {
          beforeEach(() => {
            fireEvent.click(screen.getByTestId('optionalPasteCertLink'));
            fireEvent.change(screen.getByTestId('configName'), { target: { value: 'config' } });
          });

          describe('and a certificate is pasted', () => {
            beforeEach(() => {
              fireEvent.change(screen.getByTestId('plainTextIdpCertificate'), {
                target: { value: 'pasted cert content' },
              });
            });

            identityProviderFormTests('pasted');
          });
        });
      });
    });
  });

  describe('when identity provider protocol is OIDC', () => {
    beforeEach(() => {
      renderResult.unmount();

      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderResult = renderComponent({
        centralUrl: 'http://example.com',
        protocol: Protocol.OIDC,
        idpType: IdpType.WORKFORCE,
      });
    });

    afterEach(() => {
      renderResult.unmount();
    });

    describe('when rendered with default props', () => {
      it('renders the OidcIdentityProviderForm component', () => {
        expect(screen.getByTestId('oidc-identity-provider-form')).toBeInTheDocument();
      });

      it('renders with the "Save and Finish" button disabled', () => {
        expect(screen.getByTestId('submit-button')).toHaveTextContent('Save and Finish');
        expect(screen.getByTestId('submit-button')).toBeAriaDisabled();
      });

      it('renders with a "Cancel" button that leads to the Identity Providers page', () => {
        expect(screen.getByTestId('cancel-button')).toBeInTheDocument();
      });

      it('renders with a "Back" button', () => {
        expect(screen.getByTestId('back-button')).toBeInTheDocument();
      });

      describe('and the "Save and Finish" button is clicked', () => {
        beforeEach(() => {
          fireEvent.change(screen.getByTestId('configName'), { target: { value: 'new value' } });
          expect(screen.getByTestId('submit-button')).toBeEnabled();
          fireEvent.click(screen.getByTestId('submit-button'));
        });

        it('renders with the form inputs disabled', () => {
          screen.debug();
          [
            screen.getByTestId('issuerUri'),
            screen.getByTestId('configDesc'),
            screen.getByTestId('clientId'),
            screen.getByTestId('userClaim'),
            screen.getByTestId('groupsClaim'),
          ].forEach((element) => expect(element).toBeDisabled());
        });

        it('renders with the "Save and Finish" button in the loading state', () => {
          const button = screen.getByTestId('submit-button');
          expect(button).toBeAriaDisabled();
          expect(within(button).getByTestId('button-loader-icon')).toBeInTheDocument();
        });

        describe('and the submission fails with validation errors', () => {
          beforeEach(() => {
            submitDeferred.reject({
              errorCode: 'VALIDATION_ERROR',
              errors: {
                issuerUri: 'invalid',
                description: 'invalid',
                clientId: 'invalid',
                userClaim: 'invalid',
                groupsClaim: 'invalid',
              },
            });
          });

          it('renders with a disabled "Save and Finish" button', () => {
            expect(screen.getByTestId('submit-button')).toBeAriaDisabled();
          });

          it('renders with the offending inputs in an error state', () => {
            expect(screen.getByTestId('issuerUri')).toHaveAttribute('aria-invalid', 'true');
            expect(screen.getByTestId('configDesc')).toHaveAttribute('aria-invalid', 'true');
            expect(screen.getByTestId('clientId')).toHaveAttribute('aria-invalid', 'true');
            expect(screen.getByTestId('userClaim')).toHaveAttribute('aria-invalid', 'true');
            expect(screen.getByTestId('groupsClaim')).toHaveAttribute('aria-invalid', 'true');
          });

          it('renders the correct error messages', () => {
            expect(screen.getByText('The Issuer URI is invalid')).toBeInTheDocument();
            expect(screen.getByText('The description is invalid')).toBeInTheDocument();
            expect(screen.getByText('The Client ID is invalid')).toBeInTheDocument();
            expect(screen.getByText('The user claim is invalid')).toBeInTheDocument();
            expect(screen.getByText('The groups claim is invalid')).toBeInTheDocument();
          });

          describe('and the invalid fields are updated', () => {
            beforeEach(() => {
              fireEvent.change(screen.getByTestId('issuerUri'), { target: { value: 'new value' } });
              fireEvent.change(screen.getByTestId('configName'), { target: { value: 'new value' } });
              fireEvent.change(screen.getByTestId('configDesc'), { target: { value: 'new value' } });
              fireEvent.change(screen.getByTestId('clientId'), { target: { value: 'new value' } });
              fireEvent.change(screen.getByTestId('userClaim'), { target: { value: 'new value' } });
              fireEvent.change(screen.getByTestId('groupsClaim'), { target: { value: 'new value' } });
            });

            it('renders with an enabled "Save and Finish" button', () => {
              expect(screen.getByTestId('submit-button')).toBeEnabled();
            });
          });
        });
      });
    });
  });
});
