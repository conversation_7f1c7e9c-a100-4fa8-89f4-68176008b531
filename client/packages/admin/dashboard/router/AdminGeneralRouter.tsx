import { useEffect, useMemo } from 'react';

import { css } from '@emotion/react';
import { useSelector } from 'react-redux';
import { Navigate, Outlet, Route, Routes, useParams, useSearchParams } from 'react-router-dom-v5-compat';

import { EventScope } from '@packages/types/activity/EventScope';
import { FilterType } from '@packages/types/dashboard/appUsers';

import * as settings from '@packages/redux/common/settings';
import * as viewer from '@packages/redux/common/viewer';

import ManageActivityFeed from '@packages/activity/components/ManageActivityFeed';
import { useAdminNav, WithAdminNav } from '@packages/admin/common/components/AdminApplicationLayout';
import AppSettings from '@packages/admin/common/models/setup/AppSettings';
import type { AdminRouterDependencies } from '@packages/admin/common/types/router';
import SmsBlockingPage from '@packages/admin/dashboard/SmsBlockingPage';
import { parsePagination } from '@packages/admin/dashboard/util/URLSearchParamsUtil';
import AdminAuthnPage from '@packages/authn/components/AdminAuthnPage';
import { InternalClientPage } from '@packages/authn/components/InternalClientPage';
import { RegisterInternalClientPage } from '@packages/authn/components/RegisterInternalClientPage';
import AdminCommentsPage from '@packages/comment/components/AdminCommentsPage';
import backupAdminApi from '@packages/common/services/api/backupAdminApi';
import fetchWrapper from '@packages/common/services/api/fetchWrapper';
import BackboneViewWrapper from '@packages/components/BackboneViewWrapper';
import useChainedPromise from '@packages/hooks/useChainedPromise';
import usePromise from '@packages/hooks/usePromise';
import { RequestInfo, RequestState, usePromiseAll } from '@packages/hooks/useRequestInfo';
import useRequestInfoUi from '@packages/hooks/useRequestInfoUi';
import LogList from '@packages/legacy/admin/models/LogList';
import LogSummaryList from '@packages/legacy/admin/models/LogSummaryList';
import LogListView from '@packages/legacy/admin/views/LogListView';
import LogSummaryView from '@packages/legacy/admin/views/LogSummaryView';

import AccountDetailsPage from '../components/AccountDetailsPage';
import { AccountsPage } from '../components/AccountsPage';
import ModifyApiKeyPage from '../components/ApiKeys/ModifyApiKeyPage';
import ApiKeysPage from '../components/ApiKeysPage';
import ConfigLimitOverridesPage from '../components/ConfigLimitOverridesPage';
import EncryptionKeyUsagePage from '../components/EncryptionKeyUsagePage';
import FederationSettingsPage, {
  FederationSettingsPageProps,
} from '../components/FederationSettings/FederationSettingsPage';
import GlobalAccessListPage from '../components/GlobalAccessListPage';
import CreateGlobalServiceAccountPage from '../components/GlobalServiceAccounts/CreateGlobalServiceAccountPage';
import EditGlobalServiceAccountPage from '../components/GlobalServiceAccounts/EditGlobalServiceAccountPage';
import GlobalServiceAccountDetailsPage from '../components/GlobalServiceAccounts/GlobalServiceAccountDetailsPage';
import GlobalServiceAccountsPage from '../components/GlobalServiceAccounts/GlobalServiceAccountsPage';
import { GroupsPage } from '../components/Groups/GroupsPage';
import AddOrgPage from '../components/OrgAndProject/AddOrgPage';
import AddProjectPage from '../components/OrgAndProject/AddProjectPage';
import OrganizationsPage from '../components/OrganizationsPage';
import RateLimitsPage from '../components/RateLimits/RateLimitsPage';
import SecurityActionsPage from '../components/SecurityActionsPage';
import { StackTraceJobPage } from '../components/StackTraceJobPage';
import { StackTraceJobRawViewPage } from '../components/StackTraceJobRawViewPage';
import { StackTraceJobsPage } from '../components/StackTraceJobsPage';
import SupportPage from '../components/SupportPage';
import { UiMessagesPage } from '../components/UiMessagesPage';
import VersionManifestPage from '../components/VersionManifestPage';
import overviewService from '../services/overviewService';
import AuditView from '../views/AuditView';
import OverviewLayout from '../views/OverviewLayout';

function OverviewServersRenderer({
  overviewLayoutRequestInfo,
}: {
  overviewLayoutRequestInfo: RequestInfo<InstanceType<typeof OverviewLayout>>;
}) {
  const siteServersRequestInfo = usePromise(() => overviewService.getSiteServers(), []);

  useEffect(() => {
    if (
      overviewLayoutRequestInfo.state !== RequestState.Success ||
      siteServersRequestInfo.state !== RequestState.Success
    ) {
      return;
    }

    // Once the request is finished and the layout is ready, show the servers content in the layout
    overviewLayoutRequestInfo.value.showServersContent(siteServersRequestInfo.value);
  }, [overviewLayoutRequestInfo, siteServersRequestInfo]);

  // The actual UI gets rendered inside the layout
  return null;
}

function OverviewProcessesRenderer({
  overviewLayoutRequestInfo,
}: {
  overviewLayoutRequestInfo: RequestInfo<InstanceType<typeof OverviewLayout>>;
}) {
  const deploymentRequestInfo = usePromise(() => overviewService.getSiteDeployment(), []);

  useEffect(() => {
    if (
      overviewLayoutRequestInfo.state !== RequestState.Success ||
      deploymentRequestInfo.state !== RequestState.Success
    ) {
      return;
    }

    // Once the request is finished and the layout is ready, show the processes content in the layout
    overviewLayoutRequestInfo.value.showProcessTreeView(deploymentRequestInfo.value);
  }, [overviewLayoutRequestInfo, deploymentRequestInfo]);

  // The actual UI gets rendered inside the layout
  return null;
}

function OverviewWarningsRenderer({
  overviewLayoutRequestInfo,
}: {
  overviewLayoutRequestInfo: RequestInfo<InstanceType<typeof OverviewLayout>>;
}) {
  useEffect(() => {
    if (overviewLayoutRequestInfo.state !== RequestState.Success) {
      return;
    }

    // Once the layout is ready, tell it to render warnings
    overviewLayoutRequestInfo.value.showWarningsContent();
  }, [overviewLayoutRequestInfo]);

  // The actual UI gets rendered inside the layout
  return null;
}

const overviewBackupMetricsToLookup = [
  { name: 'process_cpu_seconds_total', labels: [{}] },
  { name: 'jvm_memory_bytes_used', labels: [{ area: 'heap' }] },
];

function OverviewRoutes({
  adminSettings,
  settingsModel,
}: Pick<AdminRouterDependencies, 'adminSettings' | 'settingsModel'>) {
  const recentLogsRequestInfo = usePromise(() => overviewService.getRecentLogs(), []);
  const overviewStatisticsRequestInfo = usePromise(() => overviewService.getOverviewStatistics(), []);
  const backupMetricsRequestInfo = usePromise(
    () => backupAdminApi.getBackupMetrics(overviewBackupMetricsToLookup, '', ''),
    []
  );

  const requiredCallsRequestInfo = usePromiseAll([backupMetricsRequestInfo, recentLogsRequestInfo]);

  const overviewLayoutRequestInfo = useChainedPromise(
    requiredCallsRequestInfo,
    ([adminPromMetrics, logs]) =>
      new OverviewLayout({
        adminPromMetrics,
        adminSettings,
        settingsModel,
        logs,
      }),
    [adminSettings, settingsModel]
  );

  const ui = useRequestInfoUi(overviewLayoutRequestInfo, (overviewLayout) => (
    <BackboneViewWrapper view={overviewLayout} />
  ));

  useEffect(() => {
    if (
      overviewLayoutRequestInfo.state !== RequestState.Success ||
      overviewStatisticsRequestInfo.state !== RequestState.Success
    ) {
      return;
    }
    overviewLayoutRequestInfo.value.showAdminStatisticsContent(overviewStatisticsRequestInfo.value);
  }, [overviewLayoutRequestInfo, overviewStatisticsRequestInfo]);

  return (
    <>
      {ui}
      <Routes>
        <Route index element={<Navigate to="servers" replace />} />
        {/*
         * Important note: these `Renderer` components aren't actually passing the view instance
         * to `<BackboneViewWrapper>` (which would cause it to destroy the view on unmount and break things when
         * we tried to re-use it). Instead they're just accessing the view instance to call a method on it once it's
         * loaded. Also I have it passing in the `RequestInfo` object instead of waiting for the view instance itself
         * so that these routes can start their own fetch calls in parallel,
         * before the layout's dependencies have finished loading.
         */}
        <Route
          path="servers"
          element={<OverviewServersRenderer overviewLayoutRequestInfo={overviewLayoutRequestInfo} />}
        />
        <Route
          path="processes"
          element={<OverviewProcessesRenderer overviewLayoutRequestInfo={overviewLayoutRequestInfo} />}
        />
        <Route
          path="warnings"
          element={<OverviewWarningsRenderer overviewLayoutRequestInfo={overviewLayoutRequestInfo} />}
        />
      </Routes>
    </>
  );
}

function GroupsPageWrapper() {
  const [searchParams] = useSearchParams();

  const paginationProps = useMemo(() => parsePagination(searchParams), [searchParams]);

  return (
    <GroupsPage
      defaultNameFilter={searchParams.get('filter') ?? ''}
      defaultTagsFilter={searchParams.get('tags') ?? ''}
      {...paginationProps}
    />
  );
}

function AccountsWrapper() {
  const [searchParams] = useSearchParams();

  const paginationProps = useMemo(() => parsePagination(searchParams), [searchParams]);
  const defaultFilterType = useMemo(() => {
    const queryFilterType = searchParams.get('filterType');

    if (queryFilterType && queryFilterType in FilterType) {
      return queryFilterType as FilterType;
    }

    return FilterType.EMAIL;
  }, [searchParams]);

  return (
    <AccountsPage
      defaultFilter={searchParams.get('filter') ?? ''}
      defaultIncludeDeleted={searchParams.get('includeDeleted') === 'true'}
      defaultShowOnlyCloud={searchParams.get('showOnlyCloud') === 'true'}
      defaultFilterType={defaultFilterType}
      {...paginationProps}
    />
  );
}

function AccountDetailsWrapper() {
  const { cloudId = '' } = useParams();
  const hasGlobalUserAdmin = useSelector(viewer.isGlobalUserAdmin);
  const hasGlobalMfaAdmin = useSelector(viewer.isGlobalMfaAdmin);
  const hasGlobalEmployeeMfaAdmin = useSelector(viewer.isGlobalEmployeeMfaAdmin);
  const oktaBaseUrl = useSelector(settings.getOktaBaseUrl);

  return (
    <AccountDetailsPage
      cloudId={cloudId}
      oktaBaseUrl={oktaBaseUrl ?? ''}
      hasGlobalUserAdmin={hasGlobalUserAdmin}
      hasGlobalMfaAdmin={hasGlobalMfaAdmin}
      hasGlobalEmployeeMfaAdmin={hasGlobalEmployeeMfaAdmin}
      enableDeleteButton={false}
    />
  );
}

function LogsWrapper() {
  const [searchParams] = useSearchParams();

  const params = useMemo(
    () => ({
      category: searchParams.get('category') ?? '',
      level: searchParams.get('level') ?? '',
      hours: searchParams.get('hours') ?? 1,
      logger: searchParams.get('logger') ?? '',
    }),
    [searchParams]
  );

  const viewRequestInfo = usePromise(() => {
    const logList = new LogList(params);
    return logList.fetch().then(
      () =>
        new LogListView({
          collection: logList,
          context: `${params.category} logs`,
          parentLink: '/v2/admin#/backupv2/logs',
        })
    );
  }, [params]);

  const ui = useRequestInfoUi(viewRequestInfo, (view) => <BackboneViewWrapper view={view} />);

  return <>{ui}</>;
}

function LogsSummaryWrapper() {
  const viewRequestInfo = usePromise(() => {
    const logs = new LogSummaryList();
    return logs.fetch().then(() => new LogSummaryView({ collection: logs.models }));
  }, []);

  const ui = useRequestInfoUi(viewRequestInfo, (view) => <BackboneViewWrapper view={view} />);

  return <>{ui}</>;
}

function AddProjectWrapper() {
  const { orgId } = useParams();

  if (orgId == null) {
    throw new Error('Missing required param orgId');
  }

  return <AddProjectPage windowLocation={window.location} organizationId={orgId} />;
}

function VersionManifestWrapper() {
  const appSettingsRequestInfo = usePromise(() => {
    const appSettings = new AppSettings();
    return appSettings.fetch().then(() => appSettings);
  }, []);
  const manifestDataRequestInfo = usePromise(() => fetchWrapper('/admin/version_manifest').then((r) => r.json()), []);

  const ui = useRequestInfoUi(
    usePromiseAll([appSettingsRequestInfo, manifestDataRequestInfo]),
    ([appSettings, data]) => {
      const siteName = appSettings.attributes.defaultFileProperties['mms.siteName'];

      return <VersionManifestPage {...data} sitename={siteName} />;
    }
  );

  return <>{ui}</>;
}

function EditApiKeyWrapper() {
  const { apiKeyId } = useParams();
  return <ModifyApiKeyPage apiKeyId={apiKeyId} />;
}

function AuditWrapper() {
  const view = useMemo(() => new AuditView(), []);
  return (
    <BackboneViewWrapper
      css={css`
        display: flex;
        flex-direction: column;
        flex-grow: 1;
      `}
      view={view}
    />
  );
}

function FederationWrapper() {
  const [searchParams] = useSearchParams();

  const federationSettingsPageProps: FederationSettingsPageProps = useMemo(
    () => ({
      ...parsePagination(searchParams),
      defaultSearch: searchParams.get('search') || '',
    }),
    [searchParams]
  );

  return <FederationSettingsPage {...federationSettingsPageProps} />;
}

function InternalClientWrapper() {
  const { internalClientId } = useParams();

  if (internalClientId == null) {
    throw new Error('Missing required param internalClientId');
  }

  return <InternalClientPage internalClientId={internalClientId} />;
}

function StackTraceJobsWrapper() {
  const [searchParams] = useSearchParams();

  const mmsCentralUrl = useSelector(settings.getCentralUrl);

  const paginationProps = useMemo(() => parsePagination(searchParams), [searchParams]);

  return (
    <StackTraceJobsPage
      {...paginationProps}
      defaultHostname={searchParams.get('hostname') ?? ''}
      defaultHostIdentifier={searchParams.get('hostIdentifier') ?? ''}
      defaultRunType={searchParams.get('runType') ?? ''}
      defaultRunId={searchParams.get('runId') ?? ''}
      mmsCentralUrl={mmsCentralUrl}
    />
  );
}

function StackTraceJobAnalyticsWrapper() {
  const { jobId = '' } = useParams();
  const [searchParams] = useSearchParams();

  return (
    <StackTraceJobPage
      jobId={jobId}
      hostname={searchParams.get('hostname') ?? ''}
      hostIdentifier={searchParams.get('hostIdentifier') ?? ''}
      runType={searchParams.get('runType') ?? ''}
      runId={searchParams.get('runId') ?? ''}
      mmsRole={searchParams.get('mmsRole') ?? ''}
      instanceId={searchParams.get('instanceId') ?? ''}
      userName={searchParams.get('userName') ?? ''}
      defaultThreadNameRegex={searchParams.get('threadNameRegex') ?? ''}
      defaultClassFQNRegex={searchParams.get('classFQNRegex') ?? ''}
      defaultThreadStateRegex={searchParams.get('threadStateRegex') ?? ''}
      defaultMaxTopCodePaths={searchParams.get('maxTopCodePaths') ?? ''}
    />
  );
}

function StackTraceJobRawWrapper() {
  const { jobId = '' } = useParams();
  const [searchParams] = useSearchParams();

  return (
    <StackTraceJobRawViewPage
      jobId={jobId}
      hostname={searchParams.get('hostname') ?? ''}
      hostIdentifier={searchParams.get('hostIdentifier') ?? ''}
      runType={searchParams.get('runType') ?? ''}
      runId={searchParams.get('runId') ?? ''}
      mmsRole={searchParams.get('mmsRole') ?? ''}
      instanceId={searchParams.get('instanceId') ?? ''}
      userName={searchParams.get('userName') ?? ''}
      defaultSnapshotId={searchParams.get('snapshotId') ?? ''}
    />
  );
}

export default function AdminGeneralRouter({ dependencies }: { dependencies: AdminRouterDependencies }) {
  useAdminNav({ activeLink: '#/general', shownSubNav: 'general' });
  const hasGlobalSecurityAdmin = useSelector(viewer.isGlobalSecurityAdmin);
  const hasGlobalServiceAccountAdmin = useSelector(viewer.isGlobalServiceAccountAdmin);
  const hasGlobalOwner = useSelector(viewer.isGlobalOwner);
  const allowGSAManagement = hasGlobalServiceAccountAdmin || hasGlobalOwner;

  return (
    <Routes>
      <Route index element={<Navigate to="overview" replace />} />
      <Route
        path="overview/*"
        element={
          <WithAdminNav activeSubNavLink="#/general/overview">
            <OverviewRoutes adminSettings={dependencies.adminSettings} settingsModel={dependencies.settingsModel} />
          </WithAdminNav>
        }
      />
      <Route
        path="projects"
        element={
          <WithAdminNav activeSubNavLink="#/general/projects">
            <GroupsPageWrapper />
          </WithAdminNav>
        }
      />
      <Route
        path="userAccounts"
        element={
          <WithAdminNav activeSubNavLink="#/general/userAccounts">
            <Outlet />
          </WithAdminNav>
        }
      >
        <Route index element={<AccountsWrapper />} />
        <Route path=":cloudId" element={<AccountDetailsWrapper />} />
      </Route>
      <Route
        path="logs"
        element={
          <WithAdminNav activeSubNavLink="#/general/logs/summary">
            <Outlet />
          </WithAdminNav>
        }
      >
        <Route index element={<LogsWrapper />} />
        <Route path="summary" element={<LogsSummaryWrapper />} />
      </Route>
      <Route
        path="addOrganization"
        element={
          <WithAdminNav activeSubNavLink="#/general/organizations">
            <AddOrgPage windowLocation={window.location} />
          </WithAdminNav>
        }
      />
      <Route
        path="organizations"
        element={
          <WithAdminNav activeSubNavLink="#/general/organizations">
            <Outlet />
          </WithAdminNav>
        }
      >
        <Route index element={<OrganizationsPage />} />
        <Route path=":orgId/addProject" element={<AddProjectWrapper />} />
      </Route>
      <Route
        path="version_manifest"
        element={
          <WithAdminNav activeSubNavLink="#/general/version_manifest">
            <VersionManifestWrapper />
          </WithAdminNav>
        }
      />
      <Route
        path="apiKeys"
        element={
          <WithAdminNav activeSubNavLink="#/general/apiKeys">
            <Outlet />
          </WithAdminNav>
        }
      >
        <Route index element={<ApiKeysPage />} />
        <Route path="create" element={<ModifyApiKeyPage />} />
        <Route path="edit/:apiKeyId" element={<EditApiKeyWrapper />} />
      </Route>
      <Route
        path="accessList"
        element={
          <WithAdminNav activeSubNavLink="#/general/accessList">
            <GlobalAccessListPage />
          </WithAdminNav>
        }
      />
      <Route
        path="audit"
        element={
          <WithAdminNav activeSubNavLink="#/general/audit">
            <AuditWrapper />
          </WithAdminNav>
        }
      />
      <Route
        path="auditBeta"
        element={
          <WithAdminNav activeSubNavLink="#/general/audit">
            <ManageActivityFeed scope={EventScope.GLOBAL} />
          </WithAdminNav>
        }
      />
      <Route
        path="rateLimits"
        element={
          <WithAdminNav activeSubNavLink="#/general/rateLimits">
            <RateLimitsPage />
          </WithAdminNav>
        }
      />
      {hasGlobalSecurityAdmin && (
        <Route
          path="securityActions"
          element={
            <WithAdminNav activeSubNavLink="#/general/securityActions">
              <SecurityActionsPage />
            </WithAdminNav>
          }
        />
      )}
      <Route
        path="iamConfigLimitOverrides"
        element={
          <WithAdminNav activeSubNavLink="#/general/iamConfigLimitOverrides">
            <ConfigLimitOverridesPage />
          </WithAdminNav>
        }
      />
      <Route
        path="messages"
        element={
          <WithAdminNav activeSubNavLink="#/general/messages">
            <UiMessagesPage />
          </WithAdminNav>
        }
      />
      <Route
        path="encryptionkeyusage"
        element={
          <WithAdminNav activeSubNavLink="#/general/encryptionkeyusage">
            <EncryptionKeyUsagePage />
          </WithAdminNav>
        }
      />
      <Route
        path="smsblocking"
        element={
          <WithAdminNav activeSubNavLink="#/general/smsblocking">
            <SmsBlockingPage />
          </WithAdminNav>
        }
      />
      <Route path="alerts/test" element={<Navigate to="/alerts/test" replace />} />
      <Route
        path="federation"
        element={
          <WithAdminNav activeSubNavLink="#/general/federation">
            <FederationWrapper />
          </WithAdminNav>
        }
      />
      <Route
        path="authn"
        element={
          <WithAdminNav activeSubNavLink="#/general/authn">
            <Outlet />
          </WithAdminNav>
        }
      >
        <Route index element={<AdminAuthnPage />} />
        <Route path="internalClients/register" element={<RegisterInternalClientPage />} />
        <Route path="internalClients/:internalClientId" element={<InternalClientWrapper />} />
      </Route>
      <Route
        path="poc/comments"
        element={
          dependencies.adminSettings.isCommentServiceEnabled() ? (
            <WithAdminNav activeSubNavLink="#/general/poc/comments">
              <AdminCommentsPage />
            </WithAdminNav>
          ) : (
            <Navigate to="../overview" replace />
          )
        }
      />
      <Route
        path="support"
        element={
          <WithAdminNav activeSubNavLink="#/general/support">
            <SupportPage />
          </WithAdminNav>
        }
      />
      <Route
        path="stackTrace/jobs"
        element={
          <WithAdminNav activeSubNavLink="#/general/stackTrace/jobs">
            <Outlet />
          </WithAdminNav>
        }
      >
        <Route index element={<StackTraceJobsWrapper />} />
        <Route path=":jobId">
          <Route path="analytics" element={<StackTraceJobAnalyticsWrapper />} />
          <Route path="raw" element={<StackTraceJobRawWrapper />} />
        </Route>
      </Route>
      <Route
        path="globalServiceAccounts"
        element={
          <WithAdminNav activeSubNavLink="#/general/globalServiceAccounts">
            <Outlet />
          </WithAdminNav>
        }
      >
        <Route index element={<GlobalServiceAccountsPage />} />
        {allowGSAManagement && (
          <>
            <Route path="create" element={<CreateGlobalServiceAccountPage />} />
            <Route path="edit/:gsaId" element={<EditGlobalServiceAccountPage />} />
          </>
        )}
        <Route path=":gsaId" element={<GlobalServiceAccountDetailsPage />} />
      </Route>
    </Routes>
  );
}
