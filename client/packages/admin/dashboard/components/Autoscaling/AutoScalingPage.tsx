import { useState } from 'react';

import { css } from '@emotion/react';
import Card from '@leafygreen-ui/card';
import { Tab, Tabs } from '@leafygreen-ui/tabs';
import { H1 } from '@leafygreen-ui/typography';
import { Helmet } from 'react-helmet';

import PredictiveAutoScalingContextInfo from './PredictiveAutoScalingContextInfo';
import PredictiveTriggersTable from './PredictiveTriggersTable';
import ViewPredictionsTable from './ViewPredictionsTable';

interface AutoScalingPageProps {
  isGlobalReadOnly: boolean;
  predictionStatUsed: string;
  isAtlasOperator: boolean;
}

const styles = {
  tabCard: css`
    margin-top: 4rem;
  `,
};

function AutoScalingPage({ isGlobalReadOnly, isAtlasOperator, predictionStatUsed }: AutoScalingPageProps) {
  const [selectedHeaderTab, setSelectedHeaderTab] = useState(0);
  const [selectedPredictiveTab, setSelectedPredictiveTab] = useState(0);

  return (
    <div data-testid="autoscaling-page-container">
      <Helmet title="Autoscaling" />
      <H1 id="autoScalingHead">Autoscaling</H1>
      <p>
        This page contains information about Atlas' auto-scaling decisions. After customers opt in, Atlas attempts to
        scale clusters both reactively and predictively.
        <br />
        <a href="https://www.mongodb.com/docs/atlas/cluster-autoscaling/" target="_blank" rel="noreferrer">
          Learn more about Atlas auto-scaling.
        </a>
      </p>
      <Tabs aria-labelledby="autoScalingHead" setSelected={setSelectedHeaderTab} selected={selectedHeaderTab}>
        <Tab name="Reactive">
          <br />
          <p>
            We are in the process of migrating reactive autoscaling information to this page. For now, refer to the the
            cluster card on the Search page.
          </p>
        </Tab>
        <Tab name="Predictive">
          <Card css={styles.tabCard}>
            <Tabs
              aria-labelledby="predictiveAutoScalingHead"
              setSelected={setSelectedPredictiveTab}
              selected={selectedPredictiveTab}
            >
              <Tab name="View Predictions" disabled={!isGlobalReadOnly}>
                <ViewPredictionsTable predictionStatUsed={predictionStatUsed} />
              </Tab>
              <Tab name="View Triggers" disabled={!isGlobalReadOnly}>
                <PredictiveTriggersTable />
              </Tab>
              <Tab name="View Scaling Rules">
                <PredictiveAutoScalingContextInfo isAtlasOperator={isAtlasOperator} />
              </Tab>
            </Tabs>
          </Card>
        </Tab>
      </Tabs>
    </div>
  );
}

export default AutoScalingPage;
