import { AppEnv } from '@packages/types/RequestParams';

import PhasedVersionManager from '@packages/admin/dashboard/components/PhasedVersionManager/PhasedVersionManager';
import AdminSettings from '@packages/common/models/AdminSettings';
import versionManagerApi from '@packages/common/services/api/nds/versionManagerApi';
import { act, cleanup, fireEvent, render, screen } from '@packages/react-testing-library';

import * as phasedVersionFixtures from './phasedVersionFixtures';

// Helper to generate a valid monitoringThresholds doc
function getMonitoringThresholdsDoc() {
  return {
    maxMonitoringIterations: 10,
    cpuThreshold: {
      averageUsagePercentDecrease: 5,
      averageUsagePercentIncrease: 10,
      averageUsagePercentLowerLimit: 20,
      averageUsagePercentUpperLimit: 80,
    },
    memoryThreshold: {
      averageUsagePercentDecrease: 3,
      averageUsagePercentIncrease: 7,
      averageUsagePercentLowerLimit: 30,
      averageUsagePercentUpperLimit: 90,
    },
    connectionCountThreshold: {
      averageUsagePercentDecrease: 2,
      averageUsagePercentIncrease: 4,
      averageUsagePercentLowerLimit: 10,
      averageUsagePercentUpperLimit: 100,
    },
    readWriteOpsThreshold: {
      averageUsagePercentDecrease: 3,
      averageUsagePercentIncrease: 6,
      averageUsagePercentLowerLimit: 15,
      averageUsagePercentUpperLimit: 95,
    },
    iterationIntervalInMinutes: 15,
    lookbackDurationInMinutes: 60,
    unhealthyClustersThresholdWarning: 2,
    unhealthyClustersThresholdCritical: 5,
    planFailureThresholdWarning: 1,
    planFailureThresholdCritical: 3,
    maxFleetClustersToMonitorCount: 100,
    maxFleetClustersToMonitorPercent: 50,
    planRoundsBeforeTiedThreshold: 2,
    stuckPlansThresholdWarning: 1,
    stuckPlansThresholdCritical: 2,
    stalenessThresholdInMinutes: 30,
  };
}

describe('@packages/admin/dashboard/components/PhasedVersionManager', function () {
  let getVersionsStub: $TSFixMe;
  let patchVersionStub: $TSFixMe;
  const expectedPhasedVersions = 15;

  const getNthEditor = (index: number) => {
    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
    return screen.getAllByTestId('ace-editor')[index].getElementsByClassName('ace_editor')[0]['env'].editor;
  };

  beforeEach(() => {
    getVersionsStub = jest.fn().mockResolvedValue(phasedVersionFixtures.getPhasedVersions);
    patchVersionStub = jest.fn();
    versionManagerApi.getVersions = getVersionsStub;
    versionManagerApi.patchVersion = patchVersionStub;

    AdminSettings.getAppEnv = () => AppEnv.PROD;
    AdminSettings.isAlwaysUpdateVersionsEnabled = () => true;
  });

  afterEach(() => {
    cleanup();
  });

  describe('when the page loads', () => {
    beforeEach(async () => {
      await act(async () => {
        render(<PhasedVersionManager adminSettings={AdminSettings} />);
      });
    });

    it('calls the phased version api for current phased versions', () => {
      expect(getVersionsStub).toHaveBeenCalledTimes(1);
    });

    it('displays JSON editors for the allowed phased version types', () => {
      expect(screen.getAllByTestId('ace-editor').length).toBe(expectedPhasedVersions);
    });

    it('displays save buttons for each editor', () => {
      expect(screen.getAllByRole('button', { name: 'Save' }).length).toBe(expectedPhasedVersions);
    });

    it('displays the current version configuration in the editors', () => {
      // rapid release version
      const editor0 = getNthEditor(0);
      expect(JSON.parse(editor0.getValue())).toEqual({
        releaseMode: 'AUTOMATIC',
        targetVersion: '6.3.0-rc0',
        criticalReleaseDurationHours: 1,
        phasedReleaseCriteria: null,
        monitoringThresholds: null,
      });

      // fcv
      const editor1 = getNthEditor(1);
      expect(JSON.parse(editor1.getValue())).toEqual({
        releaseMode: 'CRITICAL',
        targetVersion: '6.3',
        criticalReleaseDurationHours: 0,
        phasedReleaseCriteria: null,
        monitoringThresholds: null,
      });

      // ifr
      const editor2 = getNthEditor(2);
      expect(JSON.parse(editor2.getValue())).toEqual({
        releaseMode: 'IFR',
        targetVersion: '8.0',
        criticalReleaseDurationHours: 0,
        phasedReleaseCriteria: null,
        monitoringThresholds: null,
        ifrStateLocked: false,
        ifrState: {
          experimentId: '000000000000000000000001',
          experimentStatus: 'LIVE',
          rolloutType: 'BINARY',
          wave: 1,
          waveStatus: 'FINALIZED',
        },
        previousIfrState: {
          experimentId: '000000000000000000000000',
          experimentStatus: 'LIVE',
          rolloutType: 'BINARY',
          wave: 1,
          waveStatus: 'FINALIZED',
        },
      });

      // os policy warning banner
      expect(screen.getByTestId('policy-warning-banner')).toBeInTheDocument();
    });

    it('each editor value has monitoringThresholds: null or an object', () => {
      const editors = screen.getAllByTestId('ace-editor');
      for (let i = 0; i < editors.length; i++) {
        const editorInstance = getNthEditor(i);
        const value = JSON.parse(editorInstance.getValue());
        expect('monitoringThresholds' in value).toBe(true);
        const mt = value.monitoringThresholds;
        expect(mt === null || typeof mt === 'object').toBe(true);
      }
    });

    it('displays alwaysUpdateVersions banner when te setting is true', () => {
      expect(screen.getByTestId('always-update-versions-banner')).toBeInTheDocument();
    });

    it('do not displays alwaysUpdateVersions banner when the setting is false', () => {
      AdminSettings.isAlwaysUpdateVersionsEnabled = () => false;
      cleanup();
      render(<PhasedVersionManager adminSettings={AdminSettings} />);
      expect(screen.queryByTestId('always-update-versions-banner')).not.toBeInTheDocument();
    });

    describe('and invalid JSON is entered in an editor', () => {
      beforeEach(async () => {
        const editorInstance = getNthEditor(0);
        const invalidJSON = '}}}}{@!!!';
        act(() => {
          editorInstance.setValue(invalidJSON);
        });
      });

      it('disables the Save button for that editor', () => {
        expect(screen.getAllByRole('button')[0]).toBeAriaDisabled();
      });
    });

    describe('and a valid new configuration is entered in an editor', () => {
      beforeEach(async () => {
        const monitoringThresholds = getMonitoringThresholdsDoc();
        const newConfiguration = {
          releaseMode: 'PHASED',
          targetVersion: '6.3.2',
          criticalReleaseDurationHours: 1,
          phasedReleaseCriteria: {
            versionPercent: 25,
          },
          monitoringThresholds,
        };
        const editorInstance = getNthEditor(0);
        act(() => {
          editorInstance.setValue(JSON.stringify(newConfiguration));
        });
      });

      it('has an enabled Save button for that editor', () => {
        expect(screen.getAllByRole('button')[0]).toBeEnabled();
      });

      describe('and I click the save button for that editor (request fails)', () => {
        const errorMessage = 'my little server error';

        beforeEach(async () => {
          patchVersionStub.mockRejectedValue({ message: errorMessage });
          act(() => {
            fireEvent.click(screen.getAllByRole('button')[0]);
          });
        });

        it('calls the API with the expected patch body', () => {
          const monitoringThresholds = getMonitoringThresholdsDoc();
          const expectedBody = {
            _id: 'CONTINUOUS_DELIVERY_MONGODB_VERSION',
            releaseMode: 'PHASED',
            targetVersion: '6.3.2',
            isPreviousVersionSafe: undefined,
            criticalReleaseDurationHours: 1,
            phasedReleaseCriteria: {
              versionPercent: 25,
            },
            monitoringThresholds,
          };
          expect(patchVersionStub).toHaveBeenCalledWith(expectedBody);
        });

        it('shows a banner indicating the error', () => {
          const errorBanner = screen.getByTestId('error-banner');
          expect(errorBanner).toBeInTheDocument();
          expect(errorBanner.textContent).toContain(errorMessage);
        });
      });

      describe('and I click the save button for that editor (request succeeds)', () => {
        beforeEach(async () => {
          patchVersionStub.mockResolvedValue({});
          act(() => {
            fireEvent.click(screen.getAllByRole('button')[0]);
          });
        });

        it('calls the API with the expected patch body', () => {
          const monitoringThresholds = getMonitoringThresholdsDoc();
          const expectedBody = {
            _id: 'CONTINUOUS_DELIVERY_MONGODB_VERSION',
            releaseMode: 'PHASED',
            targetVersion: '6.3.2',
            isPreviousVersionSafe: undefined,
            criticalReleaseDurationHours: 1,
            phasedReleaseCriteria: {
              versionPercent: 25,
            },
            monitoringThresholds,
          };
          expect(patchVersionStub).toHaveBeenCalledWith(expectedBody);
        });

        it('shows a banner indicating success', () => {
          const errorBanner = screen.getByTestId('success-banner');
          expect(errorBanner).toBeInTheDocument();
          expect(errorBanner.textContent).toContain('Release configuration updated.');
        });
      });
    });
  });
});
