import { useEffect, useState } from 'react';

import { css } from '@emotion/react';
import Banner from '@leafygreen-ui/banner';
import Button from '@leafygreen-ui/button';
import Card from '@leafygreen-ui/card';
import Checkbox from '@leafygreen-ui/checkbox';
import ConfirmationModal from '@leafygreen-ui/confirmation-modal';
import { palette } from '@leafygreen-ui/palette';
import TextInput from '@leafygreen-ui/text-input';
import AceEditor from 'react-ace';
import semverGt from 'semver/functions/gt';

import { AppEnv } from '@packages/types/RequestParams';

import { AdminSettingsModel } from '@packages/common/models/AdminSettings';
import versionManagerApi, { NDSVersionView } from '@packages/common/services/api/nds/versionManagerApi';
import { exceptionToMessage } from '@packages/common/services/errorHelper';
import SectionHeader from '@packages/components/SectionHeader/SectionHeader';
import mongoDate from '@packages/date';

const cardContainerStyle = css({
  display: 'grid',
  gridTemplateColumns: '1fr 1fr',
  gap: '16px',
  margin: '8px',
});

const cardStyle = css({
  margin: '12px',
  backgroundColor: palette.gray.light2,
});

const DEPRECATED_MONGODB_VERSIONS = [
  'MONGODB_3_2',
  'MONGODB_3_4',
  'MONGODB_3_6',
  'MONGODB_4_0',
  'MONGODB_4_2',
  'MONGODB_4_4',
];

const SOFTWARE_TYPE_NOT_NEEDING_CONFIRMATION = [
  'CONTINUOUS_DELIVERY_MONGODB_VERSION',
  'CONTINUOUS_DELIVERY_FCV',
  'MONGODB_EOL_VERSION',
];

interface VersionConfigurer {
  (inputPhasedVersion: PhasedVersionInput): PhasedVersionInput;
}

interface PhasedVersionInput {
  editedTargetVersion: string;
  isSuccess?: boolean;
  error?: string;
  editableJSON: string;
  isJSONValid: boolean;
  isPreviousVersionSafe: boolean;
  previousVersion?: string | null;
  original: NDSVersionView;
}

interface Props {
  adminSettings: AdminSettingsModel;
}

// The ace editor's z-index made it float above confirmation modal, changing the z-index to auto fixes it
const aceEditorStyle = css`
  &.ace_editor {
    .ace_gutter {
      z-index: auto;
    }
  }
  margin-top: 16px;
`;

const previousVersionStyle = css`
  display: inline-block;
  position: relative;
`;

const previousCheckBoxStyle = css`
  display: inline-block;
  height: 55px;
  position: relative;
  bottom: -35px;
  margin-left: 5px;
`;

function PhasedVersionCard({
  phasedVersion,
  adminSettings,
  aceEditorStyle,
  onChangeUpdatePhasedVersionJson,
  isConfirmationRequired,
  setIsPreviousVersionSafeCheckedInModal,
  setPreviousVersion,
  setIsPreviousVersionSafe,
  setSelectedPhasedVersion,
  setOpenSaveConfirmationModal,
  patchVersion,
  clearError,
  clearSuccess,
}: {
  phasedVersion: PhasedVersionInput;
  adminSettings: AdminSettingsModel;
  aceEditorStyle: any;
  onChangeUpdatePhasedVersionJson: (val: string, id: string) => void;
  isConfirmationRequired: (v: PhasedVersionInput) => boolean;
  setIsPreviousVersionSafeCheckedInModal: (checked: boolean) => void;
  setIsPreviousVersionSafe: (id: string, isSafe: boolean) => void;
  setPreviousVersion: (id: string, previousVersion?: string) => void;
  setSelectedPhasedVersion: (v: PhasedVersionInput) => void;
  setOpenSaveConfirmationModal: (open: boolean) => void;
  patchVersion: (id: string) => void;
  clearError: (id: string) => void;
  clearSuccess: (id: string) => void;
}) {
  const i = phasedVersion;
  return (
    <Card css={cardStyle}>
      <div key={i.original._id} style={{ paddingBottom: '4px' }}>
        <h4>{i.original.displayName}</h4>
        <div>
          <span>Last updated {mongoDate(i.original.lastUpdated).tz('UTC').format('MM/DD/YY - HH:mm')} UTC</span>
          {i.original.previousVersion && i.original.releaseMode === 'PHASED' && (
            <>
              <div>
                <div css={previousVersionStyle}>
                  <TextInput
                    label="Previous Version"
                    sizeVariant="small"
                    value={i.previousVersion || ''}
                    onChange={(e) => setPreviousVersion(i.original._id, e.target.value || undefined)}
                  />
                </div>
                <div css={previousCheckBoxStyle}>
                  <Checkbox
                    checked={i.isPreviousVersionSafe}
                    onChange={(e) => setIsPreviousVersionSafe(i.original._id, e.target.checked)}
                    aria-labelledby="previous-version-safe-checkbox"
                    label="Previous version is safe to rollout"
                  />
                </div>
              </div>
            </>
          )}
        </div>
        {i.error && (
          <Banner
            variant="danger"
            data-testid="error-banner"
            dismissible
            onClose={() => clearError(i.original._id)}
            css={{ marginTop: 8, marginBottom: 8 }}
          >
            {i.error}
          </Banner>
        )}
        {i.isSuccess && (
          <Banner
            variant="success"
            data-testid="success-banner"
            dismissible
            onClose={() => clearSuccess(i.original._id)}
            css={{ marginTop: 8, marginBottom: 8 }}
          >
            Release configuration updated.
          </Banner>
        )}
        {i.original._id.toLowerCase().includes('os_') && (
          <Banner variant="warning" data-testid="policy-warning-banner" css={{ marginTop: 8, marginBottom: 8 }}>
            Os Image version should never be less than Os Policy version.
            {adminSettings.getAppEnv() !== AppEnv.PROD_GOV &&
              adminSettings.getAppEnv() !== AppEnv.PROD &&
              ` Also, changing ${i.original.displayName} in lower environments may delay image rollout.`}
          </Banner>
        )}
        <div data-testid="ace-editor">
          <AceEditor
            mode="json"
            value={i.editableJSON}
            height="300px"
            width="100%"
            onChange={(val) => {
              onChangeUpdatePhasedVersionJson(val, i.original._id);
            }}
            editorProps={{ $blockScrolling: Infinity }}
            css={aceEditorStyle}
          />
        </div>
        <Button
          css={{ marginTop: 16 }}
          variant="primary"
          onClick={() => {
            if (isConfirmationRequired(i)) {
              setIsPreviousVersionSafeCheckedInModal(
                i.original.phasedReleaseCriteria?.versionPercent === 100 &&
                  !semverGt(i.editedTargetVersion, i.original.targetVersion)
              );
              setSelectedPhasedVersion(i);
              setOpenSaveConfirmationModal(true);
            } else {
              patchVersion(i.original._id);
            }
          }}
          disabled={!i.isJSONValid}
        >
          Save
        </Button>
      </div>
    </Card>
  );
}

export default function PhasedVersionManager({ adminSettings }: Props) {
  const [phasedVersionInputs, setPhasedVersionInputs] = useState<Array<PhasedVersionInput>>([]);
  const [selectedPhasedVersion, setSelectedPhasedVersion] = useState<PhasedVersionInput | null>(null);
  const [isPreviousVersionSafeCheckedInModal, setIsPreviousVersionSafeCheckedInModal] = useState<boolean | undefined>(
    undefined
  );
  const [openSaveConfirmationModal, setOpenSaveConfirmationModal] = useState(false);

  const fetchPhasedVersions = async (): Promise<Array<PhasedVersionInput>> => {
    return versionManagerApi.getVersions().then((resp) => {
      return resp
        .filter((v) => !!v.isAllowedPhasedRelease)
        .map((v) => {
          let editableFields: any = {
            releaseMode: v.releaseMode,
            targetVersion: v.targetVersion,
            criticalReleaseDurationHours: v.criticalReleaseDurationHours,
            phasedReleaseCriteria: v.phasedReleaseCriteria,
            monitoringThresholds: v.monitoringThresholds ?? null,
            isPaused: v.isPaused,
            ifrStateLocked: v.ifrStateLocked,
            ifrState: v.ifrState,
            previousIfrState: v.previousIfrState,
          };

          return {
            editedTargetVersion: v.targetVersion,
            editableJSON: JSON.stringify(editableFields, null, 2),
            isPreviousVersionSafe: v.isPreviousVersionSafe,
            previousVersion: v.previousVersion,
            isJSONValid: true,
            isSuccess: false,
            error: '',
            original: v,
          };
        });
    });
  };

  const updatePhasedVersionById = (
    phasedVersions: Array<PhasedVersionInput>,
    versionId: string,
    versionConfigurer: VersionConfigurer
  ) => {
    const updatedPhasedVersions = phasedVersions.map((v) => (v.original._id === versionId ? versionConfigurer(v) : v));
    setPhasedVersionInputs(updatedPhasedVersions);
  };

  const setPreviousVersion = (versionId: string, previousVersion?: string) => {
    updatePhasedVersionById(phasedVersionInputs, versionId, (v) => {
      return { ...v, previousVersion: previousVersion === undefined ? null : previousVersion };
    });
  };

  const setIsPreviousVersionSafe = (versionId: string, isSafe: boolean) => {
    updatePhasedVersionById(phasedVersionInputs, versionId, (v) => {
      return { ...v, isPreviousVersionSafe: isSafe };
    });
  };

  const setError = (versionId: string, error: string) => {
    updatePhasedVersionById(phasedVersionInputs, versionId, (v) => {
      return { ...v, error: error };
    });
  };

  const clearError = (versionId: string) => {
    updatePhasedVersionById(phasedVersionInputs, versionId, (v) => {
      const newValue = { ...v };
      delete newValue['error'];
      return newValue;
    });
  };

  const setSuccess = (phasedVersions: Array<PhasedVersionInput>, versionId: string) => {
    updatePhasedVersionById(phasedVersions, versionId, (v) => {
      return { ...v, isSuccess: true };
    });
  };

  const clearSuccess = (versionId: string) => {
    updatePhasedVersionById(phasedVersionInputs, versionId, (v) => {
      const newValue = { ...v };
      delete newValue['isSuccess'];
      return newValue;
    });
  };

  const onChangeUpdatePhasedVersionJson = (jsonValue: string, versionId: string) => {
    updatePhasedVersionById(phasedVersionInputs, versionId, (v) => {
      let isJSONValid = true;
      let parsedJSON = null;

      try {
        parsedJSON = JSON.parse(jsonValue);
      } catch (e) {
        isJSONValid = false;
      }

      let editedTargetVersion = v.editedTargetVersion;
      if (isJSONValid && parsedJSON) {
        editedTargetVersion = parsedJSON.targetVersion;
      }

      return { ...v, isJSONValid, editableJSON: jsonValue, editedTargetVersion };
    });
  };

  const patchVersion = async (versionId: string) => {
    clearError(versionId);
    const input = phasedVersionInputs.find((v) => v.original._id === versionId);

    if (!input) {
      setError(versionId, 'Invalid input');
      return;
    }

    const patchBody = {
      _id: versionId,
      isPreviousVersionSafe:
        isPreviousVersionSafeCheckedInModal === undefined
          ? input.isPreviousVersionSafe
          : isPreviousVersionSafeCheckedInModal,
      previousVersion: input.previousVersion,
      ...JSON.parse(input.editableJSON),
    };

    try {
      await versionManagerApi.patchVersion(patchBody);
    } catch (err) {
      setError(versionId, exceptionToMessage(err));
      return;
    }

    setSuccess(await fetchPhasedVersions(), versionId);
  };

  const closeConfirmationModal = () => {
    setOpenSaveConfirmationModal(false);
    setIsPreviousVersionSafeCheckedInModal(undefined);
  };

  const isConfirmationRequired = (phasedVersion: PhasedVersionInput) => {
    return (
      SOFTWARE_TYPE_NOT_NEEDING_CONFIRMATION.indexOf(phasedVersion.original._id) === -1 &&
      phasedVersion.original.releaseMode === 'PHASED' &&
      phasedVersion.original.isPaused === false &&
      phasedVersion.original.targetVersion !== phasedVersion.editedTargetVersion
    );
  };

  useEffect(() => {
    const loadAndUpdatePhasedVersions = async () => {
      setPhasedVersionInputs(await fetchPhasedVersions());
    };

    loadAndUpdatePhasedVersions();
  }, []);

  return (
    <div>
      <ConfirmationModal
        open={openSaveConfirmationModal}
        title="Confirm Save Phased Version"
        cancelButtonProps={{
          onClick: () => {
            closeConfirmationModal();
          },
        }}
        confirmButtonProps={{
          onClick() {
            patchVersion(selectedPhasedVersion!!.original._id);
            closeConfirmationModal();
          },
        }}
      >
        <p>
          The new version is <strong>{selectedPhasedVersion?.editedTargetVersion}</strong>, and the previous version is{' '}
          <strong>{selectedPhasedVersion?.original.targetVersion}</strong>. To roll out version{' '}
          <strong>{selectedPhasedVersion?.original.targetVersion}</strong> to projects that haven’t received it yet,
          check the box below to confirm it is safe for deployment. For details, see the{' '}
          <a
            href="https://wiki.corp.mongodb.com/spaces/MMS/pages/216826136/Atlas+Phased+Version+Manager#AtlasPhasedVersionManager-PreviousSafeVersion"
            target="_blank"
          >
            Version Rollout Wiki. <i className="fa fa-external-link" />
          </a>
        </p>
        <div>
          <Checkbox
            onChange={(event) => setIsPreviousVersionSafeCheckedInModal(event.target.checked)}
            label={`Confirm that version ${selectedPhasedVersion?.original.targetVersion} is safe to rollout to remaining projects`}
            checked={isPreviousVersionSafeCheckedInModal}
            bold={true}
            disabled={false}
          ></Checkbox>
        </div>
      </ConfirmationModal>
      <SectionHeader headlineText="Phased Version Manager" />
      <Banner css={{ marginTop: 18 }}>
        This UI allows the configuration of phased releases of MongoDB versions to the Atlas Fleet. This functionality
        is also available via a private API. For further information on the available parameters, and usage of this
        feature,&nbsp;
        <a href="https://wiki.corp.mongodb.com/display/MMS/Atlas+Phased+Version+Manager" target="_blank">
          please see the wiki <i className="fa fa-external-link" />
        </a>
      </Banner>
      {adminSettings.isAlwaysUpdateVersionsEnabled() && (
        <Banner data-testid="always-update-versions-banner" css={{ marginTop: 18 }}>
          The "alwaysUpdateVersions" flag is enabled. Any changes you make to the target version will be overridden, and
          the latest version will be used automatically.
        </Banner>
      )}
      {/* Main (non-deprecated) versions */}
      <div css={cardContainerStyle}>
        {phasedVersionInputs
          .filter((i) => !DEPRECATED_MONGODB_VERSIONS.includes(i.original._id))
          .map((i) => (
            <PhasedVersionCard
              key={i.original._id}
              phasedVersion={i}
              adminSettings={adminSettings}
              aceEditorStyle={aceEditorStyle}
              onChangeUpdatePhasedVersionJson={onChangeUpdatePhasedVersionJson}
              isConfirmationRequired={isConfirmationRequired}
              setIsPreviousVersionSafeCheckedInModal={setIsPreviousVersionSafeCheckedInModal}
              setPreviousVersion={setPreviousVersion}
              setIsPreviousVersionSafe={setIsPreviousVersionSafe}
              setSelectedPhasedVersion={setSelectedPhasedVersion}
              setOpenSaveConfirmationModal={setOpenSaveConfirmationModal}
              patchVersion={patchVersion}
              clearError={clearError}
              clearSuccess={clearSuccess}
            />
          ))}
      </div>
      {/* Deprecated versions section */}
      {phasedVersionInputs.some((i) => DEPRECATED_MONGODB_VERSIONS.includes(i.original._id)) && (
        <>
          <div css={{ height: '50px' }}></div>
          <div css={cardContainerStyle}>
            {phasedVersionInputs
              .filter((i) => DEPRECATED_MONGODB_VERSIONS.includes(i.original._id))
              .map((i) => (
                <PhasedVersionCard
                  key={i.original._id}
                  phasedVersion={i}
                  adminSettings={adminSettings}
                  aceEditorStyle={aceEditorStyle}
                  onChangeUpdatePhasedVersionJson={onChangeUpdatePhasedVersionJson}
                  isConfirmationRequired={isConfirmationRequired}
                  setIsPreviousVersionSafeCheckedInModal={setIsPreviousVersionSafeCheckedInModal}
                  setPreviousVersion={setPreviousVersion}
                  setIsPreviousVersionSafe={setIsPreviousVersionSafe}
                  setSelectedPhasedVersion={setSelectedPhasedVersion}
                  setOpenSaveConfirmationModal={setOpenSaveConfirmationModal}
                  patchVersion={patchVersion}
                  clearError={clearError}
                  clearSuccess={clearSuccess}
                />
              ))}
          </div>
        </>
      )}
    </div>
  );
}
