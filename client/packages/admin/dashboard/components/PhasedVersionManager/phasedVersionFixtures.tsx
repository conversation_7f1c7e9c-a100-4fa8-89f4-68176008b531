const getPhasedVersions = [
  {
    _id: 'CONTINUOUS_DELIVERY_MONGODB_VERSION',
    criticalReleaseDurationHours: 1,
    displayName: 'Rapid Release MongoDB Version',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'AUTOMATIC',
    targetVersion: '6.3.0-rc0',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'CONTINUOUS_DELIVERY_FCV',
    criticalReleaseDurationHours: 0,
    displayName: 'Rapid Release FCV',
    lastUpdated: '2023-02-16T18:25:49Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-15T16:27:29Z',
    releaseMode: 'CRITICAL',
    targetVersion: '6.3',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'MONGODB_8_0',
    criticalReleaseDurationHours: 0,
    displayName: 'IFR Rollout',
    lastUpdated: '2023-02-16T18:25:49Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-15T16:27:29Z',
    releaseMode: 'IFR',
    targetVersion: '8.0',
    isAllowedPhasedRelease: true,
    ifrStateLocked: false,
    ifrState: {
      experimentId: '000000000000000000000001',
      experimentStatus: 'LIVE',
      rolloutType: 'BINARY',
      wave: 1,
      waveStatus: 'FINALIZED',
    },
    previousIfrState: {
      experimentId: '000000000000000000000000',
      experimentStatus: 'LIVE',
      rolloutType: 'BINARY',
      wave: 1,
      waveStatus: 'FINALIZED',
    },
  },
  {
    _id: 'MONGODB_3_2',
    criticalReleaseDurationHours: 1,
    displayName: 'MongoDB 3.2',
    lastUpdated: '2023-02-16T18:24:43Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-10T15:26:00Z',
    releaseMode: 'AUTOMATIC',
    targetVersion: '3.2.22',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'MONGODB_3_4',
    criticalReleaseDurationHours: 0,
    displayName: 'MongoDB 3.4',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'CRITICAL',
    targetVersion: '3.4.24',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'MONGODB_3_6',
    criticalReleaseDurationHours: 1,
    displayName: 'MongoDB 3.6',
    lastUpdated: '2023-02-16T18:24:50Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-10T15:26:00Z',
    releaseMode: 'AUTOMATIC',
    targetVersion: '3.6.23',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'MONGODB_4_0',
    criticalReleaseDurationHours: 0,
    displayName: 'MongoDB 4.0',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'CRITICAL',
    targetVersion: '4.0.28',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'MONGODB_4_2',
    criticalReleaseDurationHours: 1,
    displayName: 'MongoDB 4.2',
    lastUpdated: '2023-02-16T18:24:57Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-15T16:27:29Z',
    releaseMode: 'CRITICAL',
    targetVersion: '4.2.23',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'MONGODB_4_4',
    criticalReleaseDurationHours: 0,
    displayName: 'MongoDB 4.4',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'CRITICAL',
    targetVersion: '4.4.18',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'MONGODB_5_0',
    criticalReleaseDurationHours: 1,
    displayName: 'MongoDB 5.0',
    lastUpdated: '2023-02-16T18:25:03Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-10T15:26:00Z',
    releaseMode: 'AUTOMATIC',
    targetVersion: '5.0.14',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'MONGODB_6_0',
    criticalReleaseDurationHours: 44,
    displayName: 'MongoDB 6.0',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'CRITICAL',
    targetVersion: '6.0.4',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'AUTOMATION_AGENT',
    criticalReleaseDurationHours: 0,
    displayName: 'MongoDB Agent',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'CRITICAL',
    targetVersion: '12.13.0.8028-1',
    isAllowedPhasedRelease: false,
  },
  {
    _id: 'BI_CONNECTOR',
    criticalReleaseDurationHours: 0,
    displayName: 'BI Connector',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'CRITICAL',
    targetVersion: '2.14.5',
    isAllowedPhasedRelease: false,
  },
  {
    _id: 'CHARTS',
    criticalReleaseDurationHours: 0,
    displayName: 'Charts',
    lastUpdated: '2023-02-10T15:26:00Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-10T15:26:00Z',
    releaseMode: 'CRITICAL',
    targetVersion: '0.0.0',
    isAllowedPhasedRelease: false,
  },
  {
    _id: 'MONGOT',
    criticalReleaseDurationHours: 0,
    displayName: 'Mongot',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'CRITICAL',
    targetVersion: '1.27.2.1441',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'MONGODB_TOOLS',
    criticalReleaseDurationHours: 0,
    displayName: 'Mongodb Tools',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'CRITICAL',
    targetVersion: '100.7.1',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'MONGOSH',
    criticalReleaseDurationHours: 0,
    displayName: 'Mongosh',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'CRITICAL',
    targetVersion: '1.6.2',
    isAllowedPhasedRelease: false,
  },
  {
    _id: 'ATLAS_PROXY',
    criticalReleaseDurationHours: 22,
    displayName: 'Atlas Proxy',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'CRITICAL',
    targetVersion: '20230301.0.0.1676490101',
    isAllowedPhasedRelease: false,
  },
  {
    _id: 'SERVERLESS_PROXY',
    criticalReleaseDurationHours: 2,
    displayName: 'Serverless Proxy',
    lastUpdated: '2023-02-22T14:01:09Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-22T14:01:09Z',
    releaseMode: 'CRITICAL',
    targetVersion: '1.21.3',
    isAllowedPhasedRelease: false,
  },
  {
    _id: 'OS_POLICY',
    criticalReleaseDurationHours: 0,
    displayName: 'Os Policy',
    lastUpdated: '2024-02-16T18:25:49Z',
    phasedReleaseCriteria: null,
    releaseDate: '2024-02-15T16:27:29Z',
    releaseMode: 'AUTOMATIC',
    targetVersion: '150',
    isAllowedPhasedRelease: true,
  },
  {
    _id: 'MONGODB_EOL_VERSION',
    criticalReleaseDurationHours: 0,
    displayName: 'MongoDB EOL',
    lastUpdated: '2023-02-16T18:24:43Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-10T15:26:00Z',
    releaseMode: 'PHASED',
    targetVersion: '6.0.19',
    isAllowedPhasedRelease: true,
  },
];

const getEOLVersion = [
  {
    _id: 'MONGODB_EOL_VERSION',
    criticalReleaseDurationHours: 0,
    displayName: 'MongoDB EOL',
    lastUpdated: '2023-02-16T18:24:43Z',
    phasedReleaseCriteria: null,
    releaseDate: '2023-02-10T15:26:00Z',
    releaseMode: 'PHASED',
    targetVersion: '6.0.19',
    isAllowedPhasedRelease: true,
  },
];

export { getPhasedVersions, getEOLVersion };
