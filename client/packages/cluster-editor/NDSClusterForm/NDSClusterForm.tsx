import { Component, createRef, RefObject } from 'react';

import Badge from '@leafygreen-ui/badge';
import Banner from '@leafygreen-ui/banner';
import { GuideCue } from '@leafygreen-ui/guide-cue';
import { Tab, Tabs } from '@leafygreen-ui/tabs';
import { Variant } from '@leafygreen-ui/toast';
import Tooltip from '@leafygreen-ui/tooltip';
import { Body } from '@leafygreen-ui/typography';
import pluralize from 'pluralize';

import { BI_CONNECTOR_DEPRECATION_DATE } from '@packages/types/deployment/biConnector';
import { DataProtectionSettings } from '@packages/types/nds/backup';
import { BiConnectorCostEstimate } from '@packages/types/nds/biConnectorCostEstimate';
import { BillingEstimate } from '@packages/types/nds/billingEstimate';
import {
  ClusterDescription,
  ClusterType,
  ConfigServerTypeEstimate,
  DiskWarmingMode,
  EncryptionAtRestProvider,
  ReplicaSetScalingStrategy,
  VersionReleaseSystem,
} from '@packages/types/nds/clusterDescription';
import {
  ACCORDION_NAMES,
  AccordionName,
  ClusterBuilderFilterInterface,
  ClusterFormError,
  ClusterStateSetter,
  DeploymentType,
} from '@packages/types/nds/clusterEditor';
import { ClusterOutageSimulation } from '@packages/types/nds/clusterOutageSimulation';
import { ClusterUsageStats } from '@packages/types/nds/ClusterUsageStats';
import { DefaultTemplates, TemplateKey } from '@packages/types/nds/defaultTemplate';
import Limits from '@packages/types/nds/Limits';
import { ProcessArgs } from '@packages/types/nds/ProcessArgs';
import {
  AzureDiskType,
  BackingCloudProvider,
  CloudProvider,
  CrossCloudProviderOptionsView,
  InstanceClass,
  InstanceSize,
  RegionProviderFeatures,
} from '@packages/types/nds/provider';
import { RegionUsageRestrictions } from '@packages/types/nds/regionUsageRestrictions';
import {
  AutoScaling,
  ProviderOptions,
  ReplicationSpec,
  ReplicationSpecList,
} from '@packages/types/nds/replicationSpec';
import { EncryptionAtRest } from '@packages/types/nds/security/enterpriseSecurity';
import { Tag, TagWithErrorMessage } from '@packages/types/resourceTagging';
import { HighlightInPage } from '@packages/types/routes';
import { SearchDeploymentSpec } from '@packages/types/search/decoupled/deployment';

import * as clusterDescriptionUtils from '@packages/common/utils/clusterDescription';
import * as clusterEditorUtils from '@packages/common/utils/clusterEditorUtils';
import * as endOfLifeUtils from '@packages/components/EndOfLife/utils';
import {
  getMinRecommendedStorageGBForServerlessUpgradeToDedicated,
  getRecommendedInstanceSizeForServerlessUpgradeToDedicated,
} from '@packages/cluster-configuration/serverlessUpgradeToDedicatedUtils';
import { ErrorToast } from '@packages/cluster-editor/NDSClusterForm/common/styles';
import { NDSClusterFormContext } from '@packages/cluster-editor/NDSClusterForm/context/NDSClusterFormContext';
import NDSClusterFormAdvancedOptions from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormAdvancedOptions/NDSClusterFormAdvancedOptions';
import NDSClusterFormBackup from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormBackup';
import NDSClusterFormBIConnector from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormBIConnector';
import NDSClusterFormBIConnectorAdvanced from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormBIConnectorAdvanced';
import NDSClusterFormClusterCapacity from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormClusterCapacity';
import NDSClusterFormEncryptionAtRestPrivateNetworking from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormEncryptionAtRestPrivateNetworking';
import NDSClusterFormEncryptionAtRestProvider from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormEncryptionAtRestProvider';
import NDSClusterFormGeoOverview from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormGeoOverview';
import NDSClusterFormGeoZones from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormGeoZones';
import NDSClusterFormInstanceSize from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormInstanceSize';
import NDSClusterFormName from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormName';
import NDSClusterFormProviderButtons from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormProviderButtons';
import NDSClusterFormRegionSection from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormRegionSection';
import NDSClusterFormSharding from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormSharding';
import NDSClusterFormSQLSchemaInterface from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormSQLSchemaInterface';
import NDSClusterFormTags from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormTags';
import NDSClusterFormTerminationProtection from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormTerminationProtection';
import NDSClusterFormVersion from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormVersion';
import { NDSSearchDeploymentFormInstanceSize } from '@packages/cluster-editor/NDSClusterForm/NDSSearchDeploymentFormInstanceSize';
import Settings from '@packages/common/models/Settings';
import { PROVIDER_TEXT } from '@packages/common/schemas/ndsClusterForm';
import { ContainerShape } from '@packages/common/schemas/peeringSchema';
import backupApi from '@packages/common/services/api/nds/backupApi';
import localStorage from '@packages/common/services/localStorage';
import backupUtils from '@packages/common/utils/backupUtils';
import {
  getClusterCapacitySubtext,
  getClusterTierSecondarySubtext,
  getClusterTierSubtext,
  getCrossRegionSubtext,
  getDefaultTemplateKey,
  getEnabledBackingProviders,
  getProviderOptionsToUse,
  getProviderRegions,
} from '@packages/common/utils/clusterBuilderHelpers';
import { NodeTypeFamily } from '@packages/common/utils/clusterDescription';
import { getEncryptionAtRestPrivateEndpoints, requirePrivateNetworkingEnabled } from '@packages/common/utils/kms';
import { isAtLeast5_0 } from '@packages/common/utils/mongoDbVersionHelpers';
import replicationSpecListUtils, { replicationSpecListRegions } from '@packages/common/utils/replicationSpecList';
// analytics
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import { RecursivePartial } from '@packages/common/utils/utilTypes';
import Accordion from '@packages/components/Accordion';
import DocsLink from '@packages/components/DocsLink';
import ImagePreloader from '@packages/components/ImagePreloader';
import { ATLAS_RESOURCE_POLICIES_NOT_AUTHORIZED_ERROR_CODE } from '@packages/components/ResourcePolicy/utils';

export enum ClusterBuilderGuideCue {
  ENABLE_BACKUPS = 'ENABLE_BACKUPS',
  ENABLE_AUTOSCALING = 'ENABLE_AUTOSCALING',
  CONFIGURE_AUTOSCALING = 'CONFIGURE_AUTOSCALING',
  UPGRADE_CONNECTIONS = 'UPGRADE_CONNECTIONS',
  UPGRADE_STORAGE = 'UPGRADE_STORAGE',
  EDIT_AUTOSCALE = 'EDIT_AUTOSCALE',
}

const DEFAULT_NODE_TYPE_SET = new Set<NodeTypeFamily>().add(NodeTypeFamily.BASE).add(NodeTypeFamily.ANALYTICS);

export const FREE_CLUSTER_EXISTS_ERROR = 'FREE_CLUSTER_EXISTS';

const EXCLUDED_ERROR_CODES = [
  FREE_CLUSTER_EXISTS_ERROR,
  // This error is handled in NDSClusterFormFooter
  ATLAS_RESOURCE_POLICIES_NOT_AUTHORIZED_ERROR_CODE,
];

interface Props {
  setCloudProvider: (provider: CloudProvider) => void;
  setAutoScaling: (newAutoScaling: RecursivePartial<AutoScaling>, nodeTypeFamilySet: Set<NodeTypeFamily>) => void;
  setClusterFormValue: ClusterStateSetter;
  setBackupValues: (values: { backupEnabled?: boolean; diskBackupEnabled?: boolean; pitEnabled?: boolean }) => void;
  setDiskWarmingMode: (newMode: DiskWarmingMode) => void;
  setReplicaSetScalingStrategy: (newReplicaSetScalingStrategy: ReplicaSetScalingStrategy) => void;
  setInstanceSize: (newBaseInstanceSize: InstanceSize, nodeTypeFamilySet: Set<NodeTypeFamily>) => void;
  setRedactClientLogData: (redactClientLogData: boolean) => void;
  formValues: ClusterDescription;
  defaultTemplates: DefaultTemplates;
  providerOptions: ProviderOptions;
  crossCloudProviderOptions: CrossCloudProviderOptionsView;
  error: ClusterFormError | null;
  setError: (error: $TSFixMe) => void;
  instanceClass: InstanceClass;
  analyticsInstanceClass: InstanceClass;
  processArgs: ProcessArgs;
  originalProcessArgs: ProcessArgs;
  isEdit: boolean;
  m0LimitReached: boolean;
  isTenantUpgrade: boolean;
  encryptionAtRest: EncryptionAtRest;
  isLDAPEnabled: boolean;
  isPrivateIPModeEnabled: boolean;
  useCNRegionsOnly: boolean;
  originalCluster: ClusterDescription;
  settingsModel: typeof Settings;
  billingEstimate: BillingEstimate;
  biConnectorCostEstimate: BiConnectorCostEstimate;
  clusterUsageStats: ClusterUsageStats;
  cloudContainers: Array<ContainerShape>;
  clusterBuilderFilterInterface: ClusterBuilderFilterInterface;
  isNameInUseByClusterWithBackupSnapshots: boolean;
  isNameValid: boolean;
  isNameUnique: boolean;
  isNamePrefixUnique: boolean;
  isNameWithinCharLimit: boolean;
  isNameEndWithHyphen: boolean;
  isAutoIndexingEligible: boolean;
  // @ts-expect-error TS(7051): Parameter has a name but no type. Did you mean 'ar... Remove this comment to see the full error message
  isInstanceSizeVisible: (string) => boolean;
  deploymentType: DeploymentType;
  setDeploymentType: (deploymentType: DeploymentType) => void;
  openSection: AccordionName | null;
  groupLimits: Limits;
  hasUnsetOplogMinRetentionHours: boolean;
  isDiskSizeLargerThanStandardMax: boolean;
  clusterOutageSimulation?: ClusterOutageSimulation;
  updateCrossCloudProviderOptions: (replicationSpecList: ReplicationSpecList) => Promise<void>;
  hasDecoupledSearchFeatureEnabled: boolean;
  removeSearchConsiderationConfirmed: boolean;
  hasStorageOptimizedSearchNodesUIEnabled: boolean;
  searchNodesEnabled: boolean;
  hasExistingSearchDeployment: boolean;
  searchDeploymentSpec: SearchDeploymentSpec;
  fetchSearchDeploymentFailed: boolean;
  tags: Array<TagWithErrorMessage>;
  setTags: (tags: Array<Tag>) => void;
  onTagInputBlur: (event?: React.FocusEvent<HTMLInputElement, Element>, before?: Tag, after?: Tag) => void;
  regionProviderFeatures: RegionProviderFeatures;
  isAzureSsdV2FeatureEnabled: boolean;
  isDiskWarmingFeatureEnabled: boolean;
  isDiskWarmingFeatureDisabledForAzure: boolean;
  highlight?: HighlightInPage;
  enableBackups?: boolean;
  enableAutoscaling?: boolean;
  isResourceTaggingEnabled: boolean;
  hideGuideCues?: boolean; // Do not render guide cue in tests environments until https://jira.mongodb.org/browse/LG-3284 bug fix
  regionUsageRestrictions: RegionUsageRestrictions;
  isAllowCrossRegionExtendedStorageFeatureFlagEnabled: boolean;
  isEncryptionAtRestAzureKeyVaultPrivateEndpointEnabled: boolean;
  isEncryptionAtRestPrivateNetworkingUIEnabled: boolean;
  isAtlasSqlClusterSchemaInterfaceEnabled: boolean;
  configServerTypeEstimate: ConfigServerTypeEstimate | null;
  hasGroupApiKeys: boolean;
  hasManuallySetMinAutoScaling: boolean;
  overriddenOpenSections?: Partial<Record<AccordionName, boolean>>;
}

interface State {
  newFormSelectedReplicationSpecId: string;
  openAccordions: Partial<Record<AccordionName, boolean>>;
  highlightAccordions: Partial<Record<string, boolean>>;
  selectedClusterTierTab: number;
  showSqlInterfaceBanner: boolean;
  dataProtectionSettings: DataProtectionSettings | null;
  scrollContainer: HTMLDivElement | null;
  guideCues: Partial<Record<ClusterBuilderGuideCue, { isOpen: boolean }>>;
}

class NDSClusterForm extends Component<Props, State> {
  static contextType = NDSClusterFormContext;

  static getDerivedStateFromProps(nextProps: $TSFixMe, prevState: $TSFixMe) {
    const { formValues } = nextProps;

    const newState: Partial<State> = {};

    if (
      formValues.replicationSpecList &&
      replicationSpecListUtils.getReplicationSpecIndexFromId(
        formValues.replicationSpecList,
        prevState.newFormSelectedReplicationSpecId
      ) === -1
    ) {
      newState.newFormSelectedReplicationSpecId =
        replicationSpecListUtils.getReplicationSpecWithLargestBaseInstanceSize(formValues.replicationSpecList).id;
    }

    return newState;
  }

  state: State = {
    // if global configuration filter is chosen, the GeoOverview accordion should default to open
    openAccordions: {
      [ACCORDION_NAMES.GLOBAL_CONFIGURATION.name]:
        this.props.clusterBuilderFilterInterface.providerTemplateKey === 'geoSharded3Zone',
      [ACCORDION_NAMES.INSTANCE_TIER.name]:
        this.props.isTenantUpgrade || this.props.openSection === ACCORDION_NAMES.INSTANCE_TIER.name,
      [ACCORDION_NAMES.ADDITIONAL_SETTINGS.name]: this.props.openSection === ACCORDION_NAMES.ADDITIONAL_SETTINGS.name,
      [ACCORDION_NAMES.NAME_CLUSTER.name]: this.props.openSection === ACCORDION_NAMES.NAME_CLUSTER.name,
      // Add any overridden sections from props
      ...this.props.overriddenOpenSections,
    },
    highlightAccordions: {},
    newFormSelectedReplicationSpecId: replicationSpecListUtils.getReplicationSpecWithLargestBaseInstanceSize(
      this.props.formValues.replicationSpecList
    ).id,
    selectedClusterTierTab: 0,
    showSqlInterfaceBanner: localStorage.getItem(this.getDismissKey()) !== 'dismissed',
    dataProtectionSettings: null,
    scrollContainer: null,
    guideCues: {},
  };

  backupPitSelectorRef: RefObject<HTMLDivElement> = createRef();
  autoscalingSelectorRef: RefObject<HTMLDivElement> = createRef();

  isGuideCueOpen = (guideCue: ClusterBuilderGuideCue): boolean => !!this.state.guideCues[guideCue]?.isOpen;

  setIsGuideCueOpen = (guideCue: ClusterBuilderGuideCue, isOpen: boolean) => {
    this.setState({ guideCues: { ...this.state.guideCues, [guideCue]: { isOpen } } });
  };

  getIsGuideCueOpenSetter = (guideCue: ClusterBuilderGuideCue): ((isOpen: boolean) => void) => {
    return (isOpen: boolean) => {
      this.setIsGuideCueOpen(guideCue, isOpen);
    };
  };

  closeGuideCues = () => {
    this.setState({ guideCues: {} });
  };

  getGuideCueFromHighlight = (highlight: HighlightInPage): ClusterBuilderGuideCue => {
    // For now, this is a simple 1:1 mapping, but highlights/guide cues could diverge in the future.
    return ClusterBuilderGuideCue[HighlightInPage[highlight] as keyof typeof ClusterBuilderGuideCue];
  };

  setScrollContainer = (ref: $TSFixMe) => {
    this.setState({
      scrollContainer: ref,
    });
  };

  getDismissKey() {
    const { settingsModel } = this.props;
    return `MMS.ClusterForm.AtlasSqlInterfaceBanner.${settingsModel.getCurrentGroupId()}`;
  }

  componentDidMount() {
    const {
      isEdit,
      formValues,
      settingsModel,
      setBackupValues,
      setAutoScaling,
      highlight,
      openSection,
      enableBackups,
      enableAutoscaling,
    } = this.props;
    const isGeoZoned = formValues.clusterType === ClusterType.GEOSHARDED;
    if (enableBackups) {
      setBackupValues({ backupEnabled: undefined, pitEnabled: true, diskBackupEnabled: true });
    }
    if (enableAutoscaling) {
      setAutoScaling({ compute: { enabled: true }, diskGB: { enabled: true } }, DEFAULT_NODE_TYPE_SET);
    }

    if (openSection) {
      // Only scroll to the specified section if it exists.
      const sectionElement = this[openSection as keyof this] as HTMLElement;
      if (sectionElement) {
        sectionElement.scrollIntoView();
      }
    }

    if (isEdit && !openSection && isGeoZoned) {
      this.setAccordionOpened(ACCORDION_NAMES.GLOBAL_CONFIGURATION.name, true);
    }

    if (!isEdit && !openSection) {
      // When creating a new cluster, and there is not a specified open section, trigger the animation of the first accordion opening
      // so that a new user can understand the accordion behavior.
      this.animateOpenAccordion(ACCORDION_NAMES.CLOUD_PROVIDER.name);
    }

    if (highlight) {
      this.setIsGuideCueOpen(this.getGuideCueFromHighlight(highlight), true);
    }

    // Get the largest instance size among replicationSpecLists
    const currentInstanceSizeName = replicationSpecListUtils.getLargestBaseInstanceSize(formValues.replicationSpecList);

    // Set context for default cluster size
    this.context.setDesiredInstanceSize(currentInstanceSizeName);
    const currentAnalyticsInstanceSizeName = replicationSpecListUtils.getLargestAnalyticsInstanceSize(
      formValues.replicationSpecList
    );

    this.context.setDesiredAnalyticsInstanceSize(currentAnalyticsInstanceSizeName);

    backupApi.getDataProtectionSettings(settingsModel.getCurrentGroupId()).then((dataProtectionSettings) => {
      if (dataProtectionSettings) {
        this.setState({
          dataProtectionSettings: dataProtectionSettings as DataProtectionSettings,
        });
      }
    });
  }

  componentDidUpdate(prevProps: Props) {
    if (!prevProps.error && this.props.error) {
      window.scrollTo(0, 0);
    }
    if (prevProps.openSection !== this.props.openSection && this.props.openSection != null) {
      // To draw attention to the change, trigger the animation of the accordion opening
      this.animateOpenAccordion(this.props.openSection);
    }

    // Handle changes to overriddenOpenSections
    if (prevProps.overriddenOpenSections !== this.props.overriddenOpenSections) {
      const openAccordions = {
        ...this.state.openAccordions,
        ...this.props.overriddenOpenSections,
      };
      this.setState({ openAccordions });
    }

    this._maybeResetClusterTierSelectedTab(prevProps);
  }

  /**
   * Reset selected Cluster tier tab when Analytics/Search nodes were added or removed.
   */
  _maybeResetClusterTierSelectedTab = (prevProps: $TSFixMe) => {
    const hasAnalyticsNodes =
      clusterDescriptionUtils.getTotalAnalyticsNodes(this.props.formValues.replicationSpecList) > 0;
    const hasAnalyticsNodesPrev =
      clusterDescriptionUtils.getTotalAnalyticsNodes(prevProps.formValues.replicationSpecList) > 0;
    if (this.props.searchNodesEnabled !== prevProps.searchNodesEnabled || hasAnalyticsNodes !== hasAnalyticsNodesPrev) {
      this.setSelectedClusterTierTab(0);
    }
  };

  getBackupSubtext = (diskBackupAllowed: $TSFixMe) => {
    if (diskBackupAllowed) {
      if (this.props.formValues.diskBackupEnabled) {
        return 'Cloud Backup';
      }
      if (this.props.formValues.backupEnabled) {
        return 'Legacy Backup';
      }
    }

    return '';
  };

  getAdditionalSettingsSecondaryText() {
    const { formValues, originalCluster, crossCloudProviderOptions, settingsModel } = this.props;
    const hasAnyBackupEnabled =
      formValues.backupEnabled || formValues.diskBackupEnabled || !!formValues.tenantBackupEnabled;
    const backupText = hasAnyBackupEnabled ? 'Backup' : 'No Backup';
    const totalShards = replicationSpecListUtils.getTotalShards(formValues.replicationSpecList);
    const shardingText = formValues.clusterType === 'SHARDED' ? `, ${totalShards} Shards` : '';
    const biConnectorText = formValues.biConnector.enabled ? ', BI Connector' : '';
    const encryptionAtRestProvider =
      formValues.encryptionAtRestProvider !== EncryptionAtRestProvider.NONE ? ', Encryption at Rest' : '';
    const selectedMongoDBMajorVersion =
      formValues.mongoDBMajorVersion === '4.3' ? '4.4' : formValues.mongoDBMajorVersion;

    const hasCustomerPinnedFcvEnabled = settingsModel.hasProjectFeature('CUSTOMER_PINNED_FCV_AND_MDB_DOWNGRADE');
    const mongoDBVersionToShow = clusterDescriptionUtils.getCurrentClusterVersionToShow(
      selectedMongoDBMajorVersion,
      formValues.versionReleaseSystem === VersionReleaseSystem.CONTINUOUS,
      crossCloudProviderOptions.defaultCDMongoDBFCV,
      originalCluster.fixedMongoDBFCV,
      hasCustomerPinnedFcvEnabled
    );

    return `
      ${
        originalCluster.versionReleaseSystem === 'CONTINUOUS' ? 'Latest Release' : `MongoDB ${mongoDBVersionToShow}`
      }, ${backupText}${shardingText}${biConnectorText}${encryptionAtRestProvider}`;
  }

  onDismissSqlInterfaceBanner = () => {
    this.setState({ showSqlInterfaceBanner: false });
    localStorage.setItem(this.getDismissKey(), 'dismissed');
  };

  onClickSqlInterfaceDocs = () => {
    analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
      context: 'Cluster Builder',
      action: 'Clicked Atlas SQL Interface Docs',
    });
  };

  setAccordionOpened = (name: $TSFixMe, isOpen: $TSFixMe, callback = () => {}) => {
    const open = this.state.openAccordions;
    const highlight = this.state.highlightAccordions;
    highlight[name] = isOpen;
    // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
    open[name] = isOpen;

    this.setState(
      {
        openAccordions: open,
        highlightAccordions: highlight,
      },
      callback
    );
  };

  animateOpenAccordion = (name: AccordionName) => {
    setTimeout(() => {
      this.setAccordionOpened(name, true);
    }, 0);
  };

  setInstanceSize = (instanceSize: InstanceSize, nodeTypeFamilySet: Set<NodeTypeFamily>) => {
    const { setInstanceSize } = this.props;
    setInstanceSize(instanceSize, nodeTypeFamilySet);
  };

  setNewFormSelectedReplicationSpecId = (id: $TSFixMe) => {
    if (id !== this.state.newFormSelectedReplicationSpecId) {
      this.setState({ newFormSelectedReplicationSpecId: id });
    }
  };

  toggleActive =
    (toggle: $TSFixMe, deploymentType: DeploymentType, callback = () => {}) =>
    () => {
      const open = this.state.openAccordions;

      // Leaving these analytics events referencing "databases" as "cluster" for backwards compatibility
      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
      if (open[toggle.name]) {
        if (toggle.headlineText) {
          analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
            context: 'Cluster Builder',
            action: 'Accordion Closed',
            value: toggle.headlineText,
            deployment_type: DeploymentType[deploymentType],
            pathfinder_filter: `${toggle.headlineText} Accordion Closed`,
          });
        }
        this.setAccordionOpened(toggle.name, false, callback);
      } else {
        if (toggle.headlineText) {
          analytics.track(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
            context: 'Cluster Builder',
            action: 'Accordion Opened',
            value: toggle.headlineText,
            deployment_type: DeploymentType[deploymentType],
            pathfinder_filter: `${toggle.headlineText} Accordion Opened`,
          });
        }
        this.setAccordionOpened(toggle.name, true, callback);
      }
    };

  updateReplicationSpecList = (newSpec: $TSFixMe, callback?: $TSFixMe) => {
    const { formValues, setClusterFormValue } = this.props;

    let newReplicationSpecList = replicationSpecListUtils.updateReplicationSpecInList(
      formValues.replicationSpecList,
      newSpec
    );

    if (replicationSpecListUtils.getTotalAnalyticsNodes(newSpec) === 0) {
      const baseInstanceSizeName = replicationSpecListUtils.getFirstInstanceSize(newReplicationSpecList);
      newReplicationSpecList = replicationSpecListUtils.updateAnalyticsInstanceSize(
        baseInstanceSizeName,
        newReplicationSpecList
      );
    }

    setClusterFormValue('replicationSpecList', newReplicationSpecList, callback);
  };

  updateReplicationSpecListPerZone = (newSpec: $TSFixMe, callback?: $TSFixMe) => {
    const { formValues, setClusterFormValue } = this.props;

    let newReplicationSpecList = replicationSpecListUtils.updateReplicationSpecInZone(
      formValues.replicationSpecList,
      newSpec
    );

    if (replicationSpecListUtils.getTotalAnalyticsNodes(newSpec) === 0) {
      const baseInstanceSizeName = replicationSpecListUtils.getFirstInstanceSize(newReplicationSpecList);
      newReplicationSpecList = replicationSpecListUtils.updateAnalyticsInstanceSize(
        baseInstanceSizeName,
        newReplicationSpecList
      );
    }

    setClusterFormValue('replicationSpecList', newReplicationSpecList, callback);
  };

  // This method is used when shard count is modified for asymmetric clusters, or split replication specs
  updateShardsCountForReplicationSpecList = (newSpec: $TSFixMe, newShardCount: number, callback?: $TSFixMe) => {
    const { formValues, setClusterFormValue } = this.props;

    // Updating all existing replication spec in the list to make the cluster symmetric again
    let newReplicationSpecList = replicationSpecListUtils.updateReplicationSpecInZone(
      formValues.replicationSpecList,
      newSpec
    );

    // if no analytics node exsits, update the analytics instance size to be the same as base instance size
    if (replicationSpecListUtils.getTotalAnalyticsNodes(newSpec) === 0) {
      const baseInstanceSizeName = replicationSpecListUtils.getFirstInstanceSize(newReplicationSpecList);
      newReplicationSpecList = replicationSpecListUtils.updateAnalyticsInstanceSize(
        baseInstanceSizeName,
        newReplicationSpecList
      );
    }

    const currentShardsCount = replicationSpecListUtils.getShardCountByZoneName(
      formValues.replicationSpecList,
      newSpec.zoneName
    );

    let iteration = currentShardsCount;

    // In the new schema, when adding new shard, we copy the existing replication spec with the largest instance size
    // and insert the copy with a new RsId into replicationSpecList
    while (iteration !== newShardCount) {
      if (newShardCount > iteration) {
        const replicationSpecWithNewRsId = replicationSpecListUtils.copySpecWithNewRsId(newSpec);
        newReplicationSpecList.push(replicationSpecWithNewRsId);
      } else {
        // When removing a shard, we always remove the last replication spec in the list
        // Leave internalIds empty in the UI API request, they will be inferred in ClusterDescriptionUtil.fillReplicationSpecWithInferredIds
        // This is to ensure that the internalId of the replication spec where embedded config lives does not change
        replicationSpecListUtils.removeLastSpecInZone(newReplicationSpecList, newSpec.zoneName);
        // @ts-expect-error TS(2322): Type 'null' is not assignable to type 'string'.
        newReplicationSpecList.forEach((rs) => (rs.id = null));
      }
      iteration = replicationSpecListUtils.getShardCountByZoneName(newReplicationSpecList, newSpec.zoneName);
    }

    setClusterFormValue('replicationSpecList', newReplicationSpecList, callback);
  };

  ensureProvisionedIOPSVolumeType = () => {
    const { formValues, setClusterFormValue } = this.props;

    const currentVolumeType = replicationSpecListUtils.getFirstAWSVolumeType(formValues.replicationSpecList);

    const newReplicationSpecList = replicationSpecListUtils.ensureProvisionedIOPSVolumeType(
      formValues.replicationSpecList
    );

    const newVolumeType = replicationSpecListUtils.getFirstAWSVolumeType(newReplicationSpecList);

    if (newVolumeType !== currentVolumeType) {
      setClusterFormValue('ebsVolumeType', newVolumeType);
      setClusterFormValue('replicationSpecList', newReplicationSpecList);
    }
  };

  ensureAzureDiskSizeIOPS = () => {
    const {
      formValues,
      setClusterFormValue,
      providerOptions,
      originalCluster,
      regionProviderFeatures,
      isAzureSsdV2FeatureEnabled,
    } = this.props;
    const { replicationSpecList, diskSizeGB } = formValues;
    const cloudProviders = replicationSpecListUtils.getCloudProviders(replicationSpecList);

    if (cloudProviders.includes(CloudProvider.AZURE)) {
      const clusterRegionsSupportAzureSsdV2 = replicationSpecListUtils.clusterRegionsSupportAzureSsdV2(
        replicationSpecList,
        regionProviderFeatures
      );
      const azureDiskType = replicationSpecListUtils.getAzureDiskType(replicationSpecList, originalCluster);
      const instance =
        providerOptions[CloudProvider.AZURE].instanceSizes[
          replicationSpecListUtils.getFirstInstanceSize(formValues.replicationSpecList)
        ]!;
      if (clusterRegionsSupportAzureSsdV2 && (isAzureSsdV2FeatureEnabled || azureDiskType === AzureDiskType.V2)) {
        // ensure that the standard IOPS is set properly when switching to Azure PV2
        const newReplicationSpecList = replicationSpecListUtils.ensureAzureSsdV2IOPS(
          replicationSpecList,
          instance,
          diskSizeGB
        );
        setClusterFormValue('replicationSpecList', newReplicationSpecList);
      } else {
        // ensure that the disk size is set to the closest predefined value when switching to Azure PV1
        const newDiskSizeGB = replicationSpecListUtils.ensureAzureSsdV1DiskSize(
          replicationSpecList,
          providerOptions,
          diskSizeGB
        );
        setClusterFormValue('diskSizeGB', newDiskSizeGB);
      }
    }
  };

  getPreferredRegionReplicationSpecs = (replicationSpecList: $TSFixMe): Record<string, string> => {
    const mapping = {};
    replicationSpecList.forEach((replicationSpec: $TSFixMe) => {
      const replicationId = replicationSpec.id;
      // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
      mapping[replicationId] = replicationSpecListUtils.getPreferredRegion(replicationSpec).regionName;
    });
    return mapping;
  };

  hasPITHighPriorityRegionChanged = () => {
    const { originalCluster, formValues } = this.props;
    const preferredRegionOrig = this.getPreferredRegionReplicationSpecs(originalCluster.replicationSpecList);
    const preferredRegionEdit = this.getPreferredRegionReplicationSpecs(formValues.replicationSpecList);
    if (!originalCluster.pitEnabled) {
      return false;
    }
    // check if the two are equal
    if (preferredRegionOrig.size !== preferredRegionEdit.size) {
      return true;
    }
    for (const [id, highestPriorityRegionOrig] of Object.entries(preferredRegionOrig)) {
      const highestPriorityRegionEdit = preferredRegionEdit[id];
      if (highestPriorityRegionEdit !== highestPriorityRegionOrig) {
        return true;
      }
    }
    return false;
  };

  hasPITBeenToggledOff = () => {
    const { originalCluster, formValues } = this.props;
    return originalCluster.pitEnabled && !formValues.pitEnabled;
  };

  hasPITBeenToggledOn = () => {
    const { originalCluster, formValues } = this.props;
    return !originalCluster.pitEnabled && formValues.pitEnabled;
  };

  isShardingSupported = (isTenant: boolean, isInstanceSizeSupportSharding: boolean) => {
    return !isTenant && isInstanceSizeSupportSharding;
  };

  setSelectedClusterTierTab = (index: number): void => {
    this.setState({ selectedClusterTierTab: index });
  };

  renderGlobalConfigurationSection = (
    backingProviderDefaultOneZoneReplicationSpec: Record<BackingCloudProvider, ReplicationSpec>
  ) => {
    const {
      setCloudProvider,
      setClusterFormValue,
      formValues,
      defaultTemplates,
      providerOptions,
      crossCloudProviderOptions,
      isEdit,
      settingsModel,
      originalCluster,
      isPrivateIPModeEnabled,
      useCNRegionsOnly,
      cloudContainers,
      clusterBuilderFilterInterface,
      groupLimits,
      isDiskSizeLargerThanStandardMax,
      isAllowCrossRegionExtendedStorageFeatureFlagEnabled,
      clusterOutageSimulation,
      regionProviderFeatures,
      isAzureSsdV2FeatureEnabled,
      regionUsageRestrictions,
      searchNodesEnabled,
      deploymentType,
    } = this.props;
    const { openAccordions, highlightAccordions, newFormSelectedReplicationSpecId } = this.state;

    const replicationSpecIndex = replicationSpecListUtils.getReplicationSpecIndexFromId(
      formValues.replicationSpecList,
      newFormSelectedReplicationSpecId
    );

    const selectedReplicationSpec = formValues.replicationSpecList[replicationSpecIndex];

    const backingCloudProviders = replicationSpecListUtils.getBackingCloudProviders(formValues.replicationSpecList);

    const originalInstanceSizeName = replicationSpecListUtils.getFirstInstanceSize(originalCluster.replicationSpecList);
    const instanceSizeName = replicationSpecListUtils.getFirstInstanceSize(formValues.replicationSpecList);

    const geoText = formValues.clusterType === ClusterType.GEOSHARDED ? 'Global Writes Enabled' : '';

    // for this value, FREE provider is mapped to the backing provider
    const originalBackingProviders = replicationSpecListUtils.getBackingCloudProviders(
      originalCluster.replicationSpecList
    );
    const isOriginalClusterReplicaSet = originalCluster.clusterType === ClusterType.REPLICASET;
    const isGeoZoned = formValues.clusterType === ClusterType.GEOSHARDED;
    const disabledAdditionalShards = isOriginalClusterReplicaSet && isEdit;
    const minShardingInstanceSize = crossCloudProviderOptions.minShardingInstanceSize;

    const isValid = replicationSpecListUtils.isValid(formValues.replicationSpecList, formValues.clusterType);

    const numZones = replicationSpecListUtils.getUniqueZoneIds(formValues.replicationSpecList).size;

    const selectedVersion = this._getMongoDBSelectedVersion();
    const isShardedCluster = this._isShardedCluster();

    const azureDiskType = replicationSpecListUtils.getAzureDiskType(formValues.replicationSpecList, originalCluster);
    const isInAzureSsdV2Mode = replicationSpecListUtils.isInAzureSsdV2Mode(
      isEdit,
      isAzureSsdV2FeatureEnabled,
      azureDiskType,
      regionProviderFeatures,
      formValues.replicationSpecList
    );

    const isNDSGovEnabled = settingsModel.isNdsGovEnabled();
    const enabledBackingProviders = getEnabledBackingProviders({
      isCNRegionsOnly: settingsModel.useCNRegionsOnly(),
      isNDSGovEnabled,
      regionUsageRestrictions,
    });

    return (
      <>
        <Accordion
          ref={(ref) => {
            // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
            this[ACCORDION_NAMES.GLOBAL_CONFIGURATION.name] = ref;
          }}
          headlineText={ACCORDION_NAMES.GLOBAL_CONFIGURATION.headlineText}
          secondaryText={geoText}
          secondarySubText={
            isGeoZoned
              ? `${backingCloudProviders.map((p) => PROVIDER_TEXT[p]).join(', ')}, ${numZones} Zone${
                  formValues.replicationSpecList.length > 1 ? 's' : ''
                }`
              : ''
          }
          highlightSecondaryText={highlightAccordions[ACCORDION_NAMES.GLOBAL_CONFIGURATION.name]}
          errorSecondaryText={!isValid}
          active={!!openAccordions[ACCORDION_NAMES.GLOBAL_CONFIGURATION.name]}
          onHeadlineClick={this.toggleActive(ACCORDION_NAMES.GLOBAL_CONFIGURATION, deploymentType, this.closeGuideCues)}
          animate
          colorCode={isGeoZoned ? clusterEditorUtils.getAccordionColor(replicationSpecIndex) : undefined}
          hasSummary={
            openAccordions[ACCORDION_NAMES.GLOBAL_CONFIGURATION.name] &&
            formValues.clusterType === ClusterType.GEOSHARDED
          }
        >
          <NDSClusterFormGeoOverview
            setCloudProvider={setCloudProvider}
            setClusterFormValue={setClusterFormValue}
            defaultTemplates={defaultTemplates}
            isEdit={isEdit}
            clusterType={formValues.clusterType}
            originalClusterType={originalCluster.clusterType}
            originalInstanceSize={originalInstanceSizeName}
            currentInstanceSize={instanceSizeName}
            providers={backingCloudProviders}
            originalProviders={originalBackingProviders}
            groupId={settingsModel.getCurrentGroupId()}
            docsUrl={settingsModel.getDocsUrl()}
            replicationSpecList={formValues.replicationSpecList}
            geoSharding={formValues.geoSharding}
            // ensure region configuration accordion is open when editing geosharding
            onShardingEnabled={() => this.setAccordionOpened(ACCORDION_NAMES.REGION_CONFIGURATION.name, true)}
            isPrivateIPModeEnabled={isPrivateIPModeEnabled}
            minShardingInstanceSize={minShardingInstanceSize}
            cloudContainers={cloudContainers}
            showOverlayTemplates={clusterBuilderFilterInterface.providerTemplateKey !== 'geoSharded3Zone'}
            providerOptions={providerOptions}
            useCNRegionsOnly={useCNRegionsOnly}
            backingProviderDefaultOneZoneReplicationSpec={backingProviderDefaultOneZoneReplicationSpec}
            providerRegions={providerOptions[backingCloudProviders[0]].regions}
            diskSizeGB={formValues.diskSizeGB}
            isDiskSizeLargerThanStandardMax={isDiskSizeLargerThanStandardMax}
            isAllowCrossRegionExtendedStorageFeatureFlagEnabled={isAllowCrossRegionExtendedStorageFeatureFlagEnabled}
            searchNodesEnabled={searchNodesEnabled}
            enabledBackingProviders={enabledBackingProviders}
            isNDSGovEnabled={isNDSGovEnabled}
          />
        </Accordion>
        {isGeoZoned && (
          <NDSClusterFormGeoZones
            originalCluster={originalCluster}
            providerOptions={providerOptions}
            providers={backingCloudProviders}
            replicationSpec={selectedReplicationSpec}
            replicationSpecList={formValues.replicationSpecList}
            originalReplicationSpecList={originalCluster.replicationSpecList}
            backingProviderDefaultOneZoneReplicationSpec={backingProviderDefaultOneZoneReplicationSpec}
            highlightSecondaryText={!!highlightAccordions.regionConfiguration}
            onAccordionHeadlineClick={this.toggleActive(
              ACCORDION_NAMES.REGION_CONFIGURATION,
              deploymentType,
              this.closeGuideCues
            )}
            providerRegions={providerOptions[backingCloudProviders[0]].regions}
            isAccordionActive={!!openAccordions[ACCORDION_NAMES.REGION_CONFIGURATION.name]}
            setClusterFormValue={setClusterFormValue}
            updateReplicationSpecList={this.updateReplicationSpecListPerZone}
            updateShardsCountForReplicationSpecList={this.updateShardsCountForReplicationSpecList}
            setNewFormSelectedReplicationSpecId={this.setNewFormSelectedReplicationSpecId}
            isEdit={isEdit}
            clusterType={formValues.clusterType}
            isPrivateIPModeEnabled={isPrivateIPModeEnabled}
            hasPITHighestPriorityRegionChanged={this.hasPITHighPriorityRegionChanged()}
            maxZones={groupLimits.maxZonesPerGeoCluster}
            ensureProvisionedIOPSVolumeType={this.ensureProvisionedIOPSVolumeType}
            diskSizeGB={formValues.diskSizeGB}
            clusterOutageSimulation={clusterOutageSimulation}
            mongoDBVersion={selectedVersion}
            isShardedCluster={isShardedCluster}
            disabledAdditionalShards={disabledAdditionalShards}
            ensureAzureDiskSizeIOPS={this.ensureAzureDiskSizeIOPS}
            regionProviderFeatures={regionProviderFeatures}
            isInAzureSsdV2Mode={isInAzureSsdV2Mode}
            enabledBackingProviders={enabledBackingProviders}
            regionGroupingType={clusterEditorUtils.getRegionGroupingType(isNDSGovEnabled)}
          />
        )}
      </>
    );
  };

  backingProviderDefaultOneZoneReplicationSpecMemoState: {
    backingProviderDefaultOneZoneReplicationSpec?: Record<BackingCloudProvider, ReplicationSpec>;
    defaultTemplates?: DefaultTemplates;
    defaultTemplateKey?: string;
  } = {};

  getMemoizedBackingProviderDefaultOneZoneReplicationSpec(
    defaultTemplateKey: TemplateKey
  ): Record<BackingCloudProvider, ReplicationSpec> {
    const defaultTemplates = this.props.defaultTemplates;
    if (
      !this.backingProviderDefaultOneZoneReplicationSpecMemoState.backingProviderDefaultOneZoneReplicationSpec ||
      this.backingProviderDefaultOneZoneReplicationSpecMemoState.defaultTemplates !== defaultTemplates ||
      this.backingProviderDefaultOneZoneReplicationSpecMemoState.defaultTemplateKey !== defaultTemplateKey
    ) {
      const backingProviderDefaultOneZoneReplicationSpec = {
        AWS: defaultTemplates.AWS[defaultTemplateKey].replicationSpecList[0],
        GCP: defaultTemplates.GCP[defaultTemplateKey].replicationSpecList[0],
        AZURE: defaultTemplates.AZURE[defaultTemplateKey].replicationSpecList[0],
      };

      this.backingProviderDefaultOneZoneReplicationSpecMemoState = {
        backingProviderDefaultOneZoneReplicationSpec,
        defaultTemplates,
        defaultTemplateKey,
      };

      return backingProviderDefaultOneZoneReplicationSpec;
    }

    return this.backingProviderDefaultOneZoneReplicationSpecMemoState.backingProviderDefaultOneZoneReplicationSpec;
  }

  _getMongoDBSelectedVersion() {
    const { formValues } = this.props;
    return formValues.mongoDBMajorVersion === '4.3' ? '4.4' : formValues.mongoDBMajorVersion;
  }

  _isShardedCluster(): boolean {
    const { formValues } = this.props;
    return formValues.clusterType !== ClusterType.REPLICASET;
  }

  render() {
    const {
      setCloudProvider,
      setAutoScaling,
      setClusterFormValue,
      setBackupValues,
      setDiskWarmingMode,
      setReplicaSetScalingStrategy,
      setInstanceSize,
      setRedactClientLogData,
      formValues,
      defaultTemplates,
      providerOptions,
      crossCloudProviderOptions,
      error,
      isEdit,
      processArgs,
      originalProcessArgs,
      settingsModel,
      m0LimitReached,
      originalCluster,
      isTenantUpgrade,
      isLDAPEnabled,
      billingEstimate,
      biConnectorCostEstimate,
      encryptionAtRest,
      isPrivateIPModeEnabled,
      useCNRegionsOnly,
      clusterUsageStats,
      instanceClass,
      analyticsInstanceClass,
      cloudContainers,
      clusterBuilderFilterInterface,
      isNameInUseByClusterWithBackupSnapshots,
      isNameUnique,
      isNameValid,
      isNamePrefixUnique,
      isNameWithinCharLimit,
      isNameEndWithHyphen,
      isAutoIndexingEligible,
      isInstanceSizeVisible,
      deploymentType,
      setDeploymentType,
      hasUnsetOplogMinRetentionHours,
      isDiskSizeLargerThanStandardMax,
      clusterOutageSimulation,
      hasDecoupledSearchFeatureEnabled,
      removeSearchConsiderationConfirmed,
      hasStorageOptimizedSearchNodesUIEnabled,
      searchNodesEnabled,
      hasExistingSearchDeployment,
      searchDeploymentSpec,
      fetchSearchDeploymentFailed,
      regionProviderFeatures,
      isAzureSsdV2FeatureEnabled,
      isDiskWarmingFeatureEnabled,
      isDiskWarmingFeatureDisabledForAzure,
      isResourceTaggingEnabled,
      hideGuideCues,
      regionUsageRestrictions,
      isAllowCrossRegionExtendedStorageFeatureFlagEnabled,
      isEncryptionAtRestAzureKeyVaultPrivateEndpointEnabled,
      isEncryptionAtRestPrivateNetworkingUIEnabled,
      isAtlasSqlClusterSchemaInterfaceEnabled,
      configServerTypeEstimate,
      hasGroupApiKeys,
    } = this.props;

    const {
      openAccordions,
      highlightAccordions,
      newFormSelectedReplicationSpecId,
      selectedClusterTierTab,
      showSqlInterfaceBanner,
      scrollContainer,
    } = this.state;

    const currentProviders = replicationSpecListUtils.getCloudProviders(formValues.replicationSpecList);

    const currentBackingProviders = replicationSpecListUtils.getBackingCloudProviders(formValues.replicationSpecList);

    const providerOptionsToUse = getProviderOptionsToUse(
      formValues.replicationSpecList,
      providerOptions,
      crossCloudProviderOptions
    );
    const originalProviderOptionsToUse = getProviderOptionsToUse(
      originalCluster.replicationSpecList,
      providerOptions,
      crossCloudProviderOptions
    );

    const currentInstanceSizeName = replicationSpecListUtils.getLargestBaseInstanceSize(formValues.replicationSpecList);

    const currentInstanceSize = providerOptionsToUse.instanceSizes[currentInstanceSizeName]!;
    const currentAnalyticsInstanceSizeName = replicationSpecListUtils.getLargestAnalyticsInstanceSize(
      formValues.replicationSpecList
    );

    const shouldShowReplicationLagWarning = replicationSpecListUtils.isAnalyticsInstanceTwoOrMoreSizesBelowBaseInstance(
      formValues.replicationSpecList,
      crossCloudProviderOptions
    );

    const originalInstanceSizeName = replicationSpecListUtils.getLargestBaseInstanceSize(
      originalCluster.replicationSpecList
    );
    const originalAnalyticsInstanceSizeName = replicationSpecListUtils.getLargestAnalyticsInstanceSize(
      originalCluster.replicationSpecList
    );

    const instanceSizeChanged = originalInstanceSizeName !== currentInstanceSizeName;

    const originalRegion = replicationSpecListUtils.getPreferredRegion(originalCluster.replicationSpecList[0]);

    const replicationSpecIndex = replicationSpecListUtils.getReplicationSpecIndexFromId(
      formValues.replicationSpecList,
      newFormSelectedReplicationSpecId
    );

    const originalVersion = originalCluster.mongoDBMajorVersion;
    const selectedVersion = this._getMongoDBSelectedVersion();
    const selectedReplicationSpec = formValues.replicationSpecList[replicationSpecIndex];
    const originalClusterPreferredCpuArch = replicationSpecListUtils.getFirstProviderBaseHardwareSpec(
      originalCluster.replicationSpecList,
      CloudProvider.AWS
    )?.preferredCpuArchitecture;

    // If a customer pins their FCV, be sure to pass in the pinned FCV version to advanced options
    // This prevents a customer from configuring options for a version above their pinned FCV
    const mongoDBVersionToUseInAdvancedOptions = clusterDescriptionUtils.getCurrentClusterVersionToShow(
      selectedVersion,
      formValues.versionReleaseSystem === VersionReleaseSystem.CONTINUOUS,
      crossCloudProviderOptions.defaultCDMongoDBFCV,
      originalCluster.fixedMongoDBFCV,
      false
    );

    const docsUrl = settingsModel.getDocsUrl();
    const diskBackupAllowed = clusterDescriptionUtils.isDiskBackupAllowed(currentProviders);
    const isOriginallyServerless = clusterDescriptionUtils.isServerlessCluster(originalInstanceSizeName);
    const isServerlessUpgradeToDedicated =
      isOriginallyServerless && clusterDescriptionUtils.isDedicatedCluster(currentInstanceSizeName);
    const isTenant = clusterDescriptionUtils.isFreeOrSharedTierCluster(currentInstanceSizeName);
    const isFree = clusterDescriptionUtils.isFreeCluster(currentInstanceSizeName);
    const isShared = clusterDescriptionUtils.isSharedTierCluster(currentInstanceSizeName);
    const isCrossRegionEnabled =
      replicationSpecListUtils.isCrossRegionEnabled(selectedReplicationSpec) || searchNodesEnabled;
    const hasAtlasSearchByokEnabled = settingsModel.hasProjectFeature('ATLAS_SEARCH_BYOK');
    const isClusterEncryptionAtRestEnabled = formValues.encryptionAtRestProvider !== EncryptionAtRestProvider.NONE;
    const hasMultipleRegions = replicationSpecListUtils.isCrossRegionEnabled(selectedReplicationSpec);
    const preferredRegion = replicationSpecListUtils.getPreferredRegion(selectedReplicationSpec);
    const isAwsGravitonEnabled = settingsModel.hasProjectFeature('AWS_GRAVITON');
    const instanceSizes = providerOptionsToUse.instanceSizes;
    const originalInstanceSizes = originalProviderOptionsToUse.instanceSizes;
    const shardingEnabled = this.isShardingSupported(isTenant, currentInstanceSize.supportsSharding);
    const autoScaling = replicationSpecListUtils.getFirstAutoScaling(formValues.replicationSpecList);
    const analyticsAutoScaling =
      replicationSpecListUtils.getFirstAnalyticsAutoScalingOrBaseFallbackOrDisabledComputeAutoScaling(
        formValues.replicationSpecList
      );
    const { compute } = autoScaling;
    const minComputeInstanceSizeSupportsSharding =
      !compute.scaleDownEnabled ||
      (compute.minInstanceSize &&
        // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
        instanceSizes[compute.minInstanceSize] &&
        // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
        instanceSizes[compute.minInstanceSize].supportsSharding);

    // for this value, FREE provider is mapped to the backing provider
    const originalBackingProviders = replicationSpecListUtils.getBackingCloudProviders(
      originalCluster.replicationSpecList
    );
    // for this value, FREE is not mapped to the backing provider
    const isGeoZoned = formValues.clusterType === ClusterType.GEOSHARDED;
    const isOriginalClusterSharded = originalCluster.clusterType === ClusterType.SHARDED;
    const isOriginalClusterSyncedWithNonOptimizedPrivateEndpoint = originalCluster.privateLinkSrvAddresses
      ? Object.keys(originalCluster.privateLinkSrvAddresses).length > 0
      : false;
    const originalInstanceSize = originalInstanceSizes[originalInstanceSizeName]!;
    const originalEncryptionAtRestProvider = originalCluster.encryptionAtRestProvider;
    const minShardingInstanceSize = providerOptions[currentBackingProviders[0]].minShardingInstanceSize;

    const azureDiskType = replicationSpecListUtils.getAzureDiskType(formValues.replicationSpecList, originalCluster);
    const originalAzureDiskType = isEdit
      ? replicationSpecListUtils.getAzureDiskType(originalCluster.replicationSpecList)
      : null;

    const isInAzureSsdV2Mode = replicationSpecListUtils.isInAzureSsdV2Mode(
      isEdit,
      isAzureSsdV2FeatureEnabled,
      azureDiskType,
      regionProviderFeatures,
      formValues.replicationSpecList
    );

    const isNVMe = currentInstanceSize.isNVMe;

    const useInstanceSizeMaxStorage =
      isEdit &&
      originalCluster.diskSizeGB >
        (originalAzureDiskType === AzureDiskType.V2
          ? originalInstanceSize.maxAllowedStorageGBForV2 || originalInstanceSize.maxAllowedStorageGB
          : originalInstanceSize.maxAllowedStorageGB);
    const onlyShowDiskBackup =
      currentProviders.length > 1 ||
      currentProviders[0] === 'AWS' ||
      ((currentProviders[0] === 'GCP' || currentProviders[0] === 'AZURE') &&
        settingsModel.hasProjectFeature('CPS_GCP_AND_AZURE_NEW_CLUSTERS_ONLY_CPS'));

    const hasAnalyticsNodes = clusterDescriptionUtils.getTotalAnalyticsNodes(formValues.replicationSpecList) > 0;
    const hasConfigurableNodes = hasAnalyticsNodes || searchNodesEnabled;
    const defaultTemplateKey = getDefaultTemplateKey(currentInstanceSizeName);
    const clusterTierHeader = (
      <div>
        <Tooltip
          align="top"
          justify="start"
          trigger={<span className="accordion-headline-text underline-dotted">Cluster</span>}
          triggerEvent="hover"
          usePortal={false}
          className="tooltip-content-is-cluster-form"
        >
          An Atlas-managed MongoDB deployment. Clusters can be either a replica set or a sharded cluster.
        </Tooltip>{' '}
        <span className="accordion-headline-text"> Tier</span>
      </div>
    );

    const analyticsTierHeader = (
      <span>
        Analytics Tier
        <span css={{ paddingLeft: 5 }}>
          <Badge variant="blue">New!</Badge>
        </span>
      </span>
    );

    const searchTierHeader = (
      <span>
        Search Tier
        <span css={{ paddingLeft: 5 }}>
          <Badge variant="blue">New</Badge>
        </span>
      </span>
    );

    const possibleMongoDBMajorVersions = clusterEditorUtils.getPossibleMongoDBMajorVersionsForCluster(
      providerOptionsToUse,
      currentProviders,
      currentInstanceSizeName,
      formValues.replicationSpecList
    );

    const isValid = replicationSpecListUtils.isValid(formValues.replicationSpecList, formValues.clusterType);

    const isNotSharedOrUSSOrVersionSelectionAvailable =
      (deploymentType !== DeploymentType.SHARED && deploymentType !== DeploymentType.FLEX) ||
      possibleMongoDBMajorVersions.filter((v: $TSFixMe) => !v.deprecated).length > 1;

    const backingProviderDefaultOneZoneReplicationSpec =
      this.getMemoizedBackingProviderDefaultOneZoneReplicationSpec(defaultTemplateKey);

    const isClusterAWSOnly =
      currentBackingProviders.length === 1 && currentBackingProviders.includes(BackingCloudProvider.AWS);

    const updatedClusterWithPrivateEndpointSupportsOptimizedConnectionString =
      isEdit &&
      isOriginalClusterSyncedWithNonOptimizedPrivateEndpoint &&
      isClusterAWSOnly &&
      isAtLeast5_0(formValues.mongoDBMajorVersion) &&
      formValues.clusterType !== ClusterType.REPLICASET;
    const areInstanceClassesAsymmetric = hasAnalyticsNodes && instanceClass !== analyticsInstanceClass;

    const { dataProtectionSettings } = this.state;

    const isShardedCluster = this._isShardedCluster();
    const isNDSGovEnabled = settingsModel.isNdsGovEnabled();
    const enabledBackingProviders = getEnabledBackingProviders({
      isFree,
      isEdit,
      originalProvider: originalBackingProviders[0], // since this is only used for M0s, we can assume 1 backing provider
      isCNRegionsOnly: settingsModel.useCNRegionsOnly(),
      isNDSGovEnabled,
      regionUsageRestrictions,
      deploymentType,
    });

    const isBICDeprecationEnabled = settingsModel.hasProjectFeature('BIC_DEPRECATION');
    const isBICDeprecationShowWarningEnabled = settingsModel.hasProjectFeature('BIC_DEPRECATION_SHOW_WARNING');
    const isAtlasBICOrg = settingsModel.isAtlasBICOrg();

    const showBIConnectorDeprecationWarning = !isNDSGovEnabled && isBICDeprecationShowWarningEnabled;
    const showBIConnectorSection =
      formValues.biConnector.enabled || isNDSGovEnabled || !isBICDeprecationEnabled || isAtlasBICOrg;

    const displayFlex = !settingsModel.isServerlessSharedUIOptionEnabled() && settingsModel.isNdsFlexEnabled();

    const showFreeTab = displayFlex && deploymentType === DeploymentType.SHARED && isFree;
    const isFlex = deploymentType === DeploymentType.FLEX;
    const showClusterCapacity = showFreeTab || isFlex;

    const hasAsymmetricShards = replicationSpecListUtils.isAsymmetricShards(formValues.replicationSpecList);
    const numShards = replicationSpecListUtils.getTotalShards(formValues.replicationSpecList);

    let minRecommendedInstanceSizeForServerlessUpgradeToDedicated;
    let minRecommendedDiskSizeGBForServerlessUpgradeToDedicated;
    let defaultDiskSizeGBForRecommendedInstanceSizeForServerlessUpgradeToDedicated;

    if (isServerlessUpgradeToDedicated) {
      const recommendedInstanceSizeForServerlessUpgradeToDedicatedKey =
        getRecommendedInstanceSizeForServerlessUpgradeToDedicated(instanceSizes, clusterUsageStats);
      if (recommendedInstanceSizeForServerlessUpgradeToDedicatedKey != null) {
        minRecommendedInstanceSizeForServerlessUpgradeToDedicated =
          recommendedInstanceSizeForServerlessUpgradeToDedicatedKey;

        const recommendedInstanceSizeForServerlessUpgradeToDedicated =
          instanceSizes[recommendedInstanceSizeForServerlessUpgradeToDedicatedKey as InstanceSize];
        if (recommendedInstanceSizeForServerlessUpgradeToDedicated !== undefined) {
          defaultDiskSizeGBForRecommendedInstanceSizeForServerlessUpgradeToDedicated =
            recommendedInstanceSizeForServerlessUpgradeToDedicated.defaultStorageGB;

          const minRecommendedStorageForServerlessUpgradeToDedicatedGB =
            getMinRecommendedStorageGBForServerlessUpgradeToDedicated(clusterUsageStats.clusterMaxDiskUsageMB);

          minRecommendedDiskSizeGBForServerlessUpgradeToDedicated = Math.max(
            defaultDiskSizeGBForRecommendedInstanceSizeForServerlessUpgradeToDedicated,
            minRecommendedStorageForServerlessUpgradeToDedicatedGB
          );
        }
      }
    }

    return (
      <div className="nds-cluster-form" ref={this.setScrollContainer}>
        <ImagePreloader sources={['/static/images/threezones.png', '/static/images/sixzones.png']} />
        {!settingsModel.isNdsGovEnabled() &&
          !useCNRegionsOnly &&
          clusterBuilderFilterInterface.isGlobalConfigurationVisible &&
          deploymentType === DeploymentType.DEDICATED &&
          !isServerlessUpgradeToDedicated &&
          this.renderGlobalConfigurationSection(backingProviderDefaultOneZoneReplicationSpec)}
        {!isGeoZoned && (
          <Accordion
            ref={(ref) => {
              // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
              this[ACCORDION_NAMES.CLOUD_PROVIDER.name] = ref;
            }}
            headlineText={ACCORDION_NAMES.CLOUD_PROVIDER.headlineText}
            secondaryText={
              hasMultipleRegions
                ? `${currentBackingProviders.map((provider) => PROVIDER_TEXT[provider]).join(', ')}`
                : `${PROVIDER_TEXT[currentBackingProviders[0]]},
                            ${preferredRegion.regionView.location} (${preferredRegion.regionView.name})`
            }
            secondarySubText={
              isCrossRegionEnabled
                ? getCrossRegionSubtext(
                    this.props.formValues.replicationSpecList,
                    this.state.newFormSelectedReplicationSpecId,
                    {
                      removeSearchConsiderationConfirmed,
                      searchNodesEnabled,
                      searchDeploymentSpec,
                    }
                  )
                : ''
            }
            onHeadlineClick={this.toggleActive(ACCORDION_NAMES.CLOUD_PROVIDER, deploymentType, this.closeGuideCues)}
            active={!!openAccordions[ACCORDION_NAMES.CLOUD_PROVIDER.name]}
            disabled={
              isServerlessUpgradeToDedicated || (isShared && !settingsModel.isServerlessSharedUIOptionEnabled())
            }
            highlightSecondaryText={highlightAccordions[ACCORDION_NAMES.CLOUD_PROVIDER.name]}
            errorSecondaryText={!isValid}
            animate
          >
            <NDSClusterFormProviderButtons
              setCloudProvider={setCloudProvider}
              originalProviders={originalBackingProviders}
              isTenantUpgrade={isTenantUpgrade}
              providers={currentBackingProviders}
              isPrivateIPModeEnabled={isPrivateIPModeEnabled}
              instanceSize={currentInstanceSizeName}
              cloudContainers={cloudContainers}
              isNDSGovEnabled={isNDSGovEnabled}
              enabledBackingProviders={enabledBackingProviders}
              searchNodesEnabled={searchNodesEnabled}
              searchDeploymentSpec={searchDeploymentSpec}
            />
            <NDSClusterFormRegionSection
              originalCluster={originalCluster}
              providerOptions={providerOptions}
              updateReplicationSpecList={this.updateReplicationSpecListPerZone}
              provider={currentBackingProviders[0]}
              providerRegions={getProviderRegions({
                providerOptions,
                deploymentType,
                providerOptionsToUse,
                currentBackingProviders,
                isFree,
                isServerlessUpgradeToDedicated,
                isEdit,
                originalRegion,
                instanceSizeChanged,
              })}
              replicationSpecList={formValues.replicationSpecList}
              replicationSpec={selectedReplicationSpec}
              docsUrl={docsUrl}
              isTenant={isTenant}
              isFree={isFree}
              isEdit={isEdit}
              cloudContainers={cloudContainers}
              isPrivateIPModeEnabled={isPrivateIPModeEnabled}
              isMultiRegionConfigurationVisible={clusterBuilderFilterInterface.isMultiRegionConfigurationVisible}
              clusterType={formValues.clusterType}
              hasPITHighestPriorityRegionChanged={this.hasPITHighPriorityRegionChanged()}
              useCNRegionsOnly={useCNRegionsOnly}
              backingProviderDefaultOneZoneReplicationSpec={backingProviderDefaultOneZoneReplicationSpec}
              deploymentType={deploymentType}
              setDeploymentType={setDeploymentType}
              ensureProvisionedIOPSVolumeType={this.ensureProvisionedIOPSVolumeType}
              diskSizeGB={formValues.diskSizeGB}
              isDiskSizeLargerThanStandardMax={isDiskSizeLargerThanStandardMax}
              clusterOutageSimulation={clusterOutageSimulation}
              hasAtlasSearchByokEnabled={hasAtlasSearchByokEnabled}
              hasDecoupledSearchFeatureEnabled={hasDecoupledSearchFeatureEnabled}
              searchNodesEnabled={searchNodesEnabled}
              hasExistingSearchDeployment={hasExistingSearchDeployment}
              searchDeploymentSpec={searchDeploymentSpec}
              fetchSearchDeploymentFailed={fetchSearchDeploymentFailed}
              setClusterFormValue={setClusterFormValue}
              mongoDBVersion={selectedVersion}
              isShardedCluster={isShardedCluster}
              ensureAzureDiskSizeIOPS={this.ensureAzureDiskSizeIOPS}
              isDowngradingFromAzurePv2ToPv1={replicationSpecListUtils.isDowngradingFromAzurePv2ToPv1(
                originalAzureDiskType,
                formValues.replicationSpecList
              )}
              regionProviderFeatures={regionProviderFeatures}
              isInAzureSsdV2Mode={isInAzureSsdV2Mode}
              currentInstanceSize={currentInstanceSize}
              isNDSGovEnabled={isNDSGovEnabled}
              enabledBackingProviders={enabledBackingProviders}
              instanceSizeChanged={instanceSizeChanged}
              isAllowCrossRegionExtendedStorageFeatureFlagEnabled={isAllowCrossRegionExtendedStorageFeatureFlagEnabled}
            />
          </Accordion>
        )}
        {isGeoZoned && (
          <div className="nds-cluster-form-divider">
            <span className="nds-cluster-form-divider-text">Options Below Apply to All Zones</span>
            <hr className="nds-cluster-form-divider-line" />
          </div>
        )}
        {!showClusterCapacity && (
          <Accordion
            ref={(ref) => {
              // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
              this[ACCORDION_NAMES.INSTANCE_TIER.name] = ref;
            }}
            data-testid={ACCORDION_NAMES.INSTANCE_TIER.name}
            headline={clusterTierHeader}
            secondary={getClusterTierSubtext(
              providerOptionsToUse,
              this.props.formValues,
              hasAsymmetricShards,
              providerOptions,
              {
                removeSearchConsiderationConfirmed,
                searchNodesEnabled,
                searchDeploymentSpec,
              }
            )}
            secondarySubText={getClusterTierSecondarySubtext(
              useInstanceSizeMaxStorage,
              isNVMe,
              originalCluster,
              formValues,
              providerOptions,
              crossCloudProviderOptions
            )}
            onHeadlineClick={this.toggleActive(ACCORDION_NAMES.INSTANCE_TIER, deploymentType, this.closeGuideCues)}
            active={!!openAccordions[ACCORDION_NAMES.INSTANCE_TIER.name]}
            highlightSecondaryText={highlightAccordions[ACCORDION_NAMES.INSTANCE_TIER.name]}
            animate
            inheritOverflow
            disabled={isShared && !settingsModel.isServerlessSharedUIOptionEnabled()}
          >
            {isServerlessUpgradeToDedicated &&
              minRecommendedInstanceSizeForServerlessUpgradeToDedicated !== undefined &&
              minRecommendedDiskSizeGBForServerlessUpgradeToDedicated !== undefined &&
              defaultDiskSizeGBForRecommendedInstanceSizeForServerlessUpgradeToDedicated !== undefined && (
                <Banner variant="warning">
                  <strong>
                    For large data sizes (100 GB+), we recommend using a higher-tier cluster (e.g., M80) with larger
                    disk sizes (e.g., 4 TB).
                  </strong>
                  <ul>
                    <li>
                      Index builds are memory-intensive, and under provisioning may significantly slow down the
                      migration. You can easily scale down to a lower-tier cluster after the process completes.
                    </li>
                    <li>
                      For smaller migrations, at least an {minRecommendedInstanceSizeForServerlessUpgradeToDedicated}{' '}
                      cluster with{' '}
                      {minRecommendedDiskSizeGBForServerlessUpgradeToDedicated >
                      defaultDiskSizeGBForRecommendedInstanceSizeForServerlessUpgradeToDedicated ? (
                        <span>{minRecommendedDiskSizeGBForServerlessUpgradeToDedicated}</span>
                      ) : (
                        <span>{minRecommendedDiskSizeGBForServerlessUpgradeToDedicated}</span>
                      )}{' '}
                      GB of storage is recommended to ensure a smooth migration.
                    </li>
                  </ul>
                </Banner>
              )}
            {!hasConfigurableNodes && (
              <NDSClusterFormInstanceSize
                providerOptions={providerOptions}
                crossCloudProviderOptions={crossCloudProviderOptions}
                providers={currentBackingProviders}
                clusterType={formValues.clusterType}
                instanceSize={currentInstanceSizeName}
                replicationSpecList={formValues.replicationSpecList}
                setAutoScaling={(autoScaling) => setAutoScaling(autoScaling, DEFAULT_NODE_TYPE_SET)}
                setBackupValues={setBackupValues}
                setClusterFormValue={setClusterFormValue}
                setInstanceSize={(instanceSize) => setInstanceSize(instanceSize, DEFAULT_NODE_TYPE_SET)}
                originalInstanceSize={originalInstanceSizeName}
                isEdit={isEdit}
                m0LimitReached={m0LimitReached}
                isTenantUpgrade={isTenantUpgrade}
                isCrossRegionEnabled={isCrossRegionEnabled}
                isPrivateIPModeEnabled={isPrivateIPModeEnabled}
                autoScaling={autoScaling}
                diskSizeGB={formValues.diskSizeGB}
                diskIOPS={replicationSpecListUtils.getIOPSToDisplay(
                  providerOptions,
                  formValues.diskSizeGB,
                  formValues.replicationSpecList
                )}
                originalProviders={originalBackingProviders}
                useInstanceSizeMaxStorage={useInstanceSizeMaxStorage}
                originalEncryptEBSVolume={
                  replicationSpecListUtils.getFirstProviderBaseHardwareSpec(
                    originalCluster.replicationSpecList,
                    CloudProvider.AWS
                  )?.encryptEBSVolume
                }
                encryptEBSVolume={
                  replicationSpecListUtils.getFirstProviderBaseHardwareSpec(
                    formValues.replicationSpecList,
                    CloudProvider.AWS
                  )?.encryptEBSVolume
                }
                ebsVolumeType={
                  replicationSpecListUtils.getFirstProviderBaseHardwareSpec(
                    formValues.replicationSpecList,
                    CloudProvider.AWS
                  )?.volumeType || null
                }
                diskBackupAllowed={diskBackupAllowed}
                instanceClass={instanceClass}
                cloudContainers={cloudContainers}
                backupEnabled={formValues.backupEnabled}
                isInstanceSizeVisible={isInstanceSizeVisible}
                isNDSGovEnabled={settingsModel.isNdsGovEnabled()}
                isPredictiveFeatureEnabled={settingsModel.isPredictiveAutoScalingEnabled()}
                isPredictiveSupportedM10M20={settingsModel.isPredictiveSupportedM10M20()}
                useCNRegionsOnly={useCNRegionsOnly}
                selectedVersion={selectedVersion}
                isAutoIndexingEnabled={settingsModel.isAutoIndexingEnabled()}
                isAutoIndexingEligible={isAutoIndexingEligible}
                isAwsGravitonEnabled={isAwsGravitonEnabled}
                awsGravitonMinimumMongoDBVersion={settingsModel.getAwsGravitonMinimumMongoDBVersion()}
                originalPreferredCpuArchitecture={originalClusterPreferredCpuArch}
                isAnalyticsTier={false}
                hasAnalyticsNodes={hasAnalyticsNodes}
                shouldShowReplicationLagWarning={false}
                areInstanceClassesAsymmetric={areInstanceClassesAsymmetric}
                hasUnsetOplogMinRetentionHours={hasUnsetOplogMinRetentionHours}
                isOriginalClusterSharded={isOriginalClusterSharded}
                isInAzureSsdV2Mode={isInAzureSsdV2Mode}
                autoscalingRef={this.autoscalingSelectorRef}
                isAllowCrossRegionExtendedStorageFeatureFlagEnabled={
                  isAllowCrossRegionExtendedStorageFeatureFlagEnabled
                }
                hasGroupApiKeys={hasGroupApiKeys}
                hasAsymmetricShards={hasAsymmetricShards}
                clusterName={formValues.name}
                clusterUsageStats={clusterUsageStats}
                groupId={settingsModel.getCurrentGroupId()}
              />
            )}
            {hasConfigurableNodes && (
              <Tabs aria-label="test" selected={selectedClusterTierTab} setSelected={this.setSelectedClusterTierTab}>
                <Tab name="Base Tier">
                  <NDSClusterFormInstanceSize
                    providerOptions={providerOptions}
                    crossCloudProviderOptions={crossCloudProviderOptions}
                    providers={currentBackingProviders}
                    clusterType={formValues.clusterType}
                    instanceSize={currentInstanceSizeName}
                    replicationSpecList={formValues.replicationSpecList}
                    setAutoScaling={(a) => setAutoScaling(a, new Set<NodeTypeFamily>().add(NodeTypeFamily.BASE))}
                    setBackupValues={setBackupValues}
                    setClusterFormValue={setClusterFormValue}
                    setInstanceSize={(instanceSize, nodeTypeFamilySet) =>
                      this.setInstanceSize(instanceSize, nodeTypeFamilySet)
                    }
                    originalInstanceSize={originalInstanceSizeName}
                    isEdit={isEdit}
                    m0LimitReached={m0LimitReached}
                    isTenantUpgrade={isTenantUpgrade}
                    isCrossRegionEnabled={isCrossRegionEnabled}
                    isPrivateIPModeEnabled={isPrivateIPModeEnabled}
                    autoScaling={autoScaling}
                    diskSizeGB={formValues.diskSizeGB}
                    diskIOPS={replicationSpecListUtils.getIOPSToDisplay(
                      providerOptions,
                      formValues.diskSizeGB,
                      formValues.replicationSpecList
                    )}
                    originalProviders={originalBackingProviders}
                    useInstanceSizeMaxStorage={useInstanceSizeMaxStorage}
                    originalEncryptEBSVolume={
                      replicationSpecListUtils.getFirstProviderBaseHardwareSpec(
                        originalCluster.replicationSpecList,
                        CloudProvider.AWS
                      )?.encryptEBSVolume
                    }
                    encryptEBSVolume={
                      replicationSpecListUtils.getFirstProviderBaseHardwareSpec(
                        formValues.replicationSpecList,
                        CloudProvider.AWS
                      )?.encryptEBSVolume
                    }
                    ebsVolumeType={
                      replicationSpecListUtils.getFirstProviderBaseHardwareSpec(
                        formValues.replicationSpecList,
                        CloudProvider.AWS
                      )?.volumeType || null
                    }
                    diskBackupAllowed={diskBackupAllowed}
                    instanceClass={instanceClass}
                    cloudContainers={cloudContainers}
                    backupEnabled={formValues.backupEnabled}
                    isInstanceSizeVisible={isInstanceSizeVisible}
                    isNDSGovEnabled={settingsModel.isNdsGovEnabled()}
                    isPredictiveFeatureEnabled={settingsModel.isPredictiveAutoScalingEnabled()}
                    isPredictiveSupportedM10M20={settingsModel.isPredictiveSupportedM10M20()}
                    useCNRegionsOnly={useCNRegionsOnly}
                    selectedVersion={selectedVersion}
                    isAutoIndexingEnabled={settingsModel.isAutoIndexingEnabled()}
                    isAutoIndexingEligible={isAutoIndexingEligible}
                    isAwsGravitonEnabled={isAwsGravitonEnabled}
                    awsGravitonMinimumMongoDBVersion={settingsModel.getAwsGravitonMinimumMongoDBVersion()}
                    originalPreferredCpuArchitecture={originalClusterPreferredCpuArch}
                    isAnalyticsTier={false}
                    hasAnalyticsNodes={hasAnalyticsNodes}
                    shouldShowReplicationLagWarning={false}
                    areInstanceClassesAsymmetric={areInstanceClassesAsymmetric}
                    hasUnsetOplogMinRetentionHours={hasUnsetOplogMinRetentionHours}
                    isOriginalClusterSharded={isOriginalClusterSharded}
                    isInAzureSsdV2Mode={isInAzureSsdV2Mode}
                    autoscalingRef={this.autoscalingSelectorRef}
                    isAllowCrossRegionExtendedStorageFeatureFlagEnabled={
                      isAllowCrossRegionExtendedStorageFeatureFlagEnabled
                    }
                    hasGroupApiKeys={hasGroupApiKeys}
                    hasAsymmetricShards={hasAsymmetricShards}
                    clusterName={formValues.name}
                    clusterUsageStats={clusterUsageStats}
                    groupId={settingsModel.getCurrentGroupId()}
                  />
                </Tab>
                {/* Note(vm-mishchenko): LG-3214 Analytics/Search tabs might be rendered not in the specified order. */}
                {hasAnalyticsNodes && (
                  <Tab name={analyticsTierHeader}>
                    <NDSClusterFormInstanceSize
                      providerOptions={providerOptions}
                      crossCloudProviderOptions={crossCloudProviderOptions}
                      providers={currentBackingProviders}
                      clusterType={formValues.clusterType}
                      instanceSize={currentAnalyticsInstanceSizeName}
                      replicationSpecList={formValues.replicationSpecList}
                      setAutoScaling={(a) => setAutoScaling(a, new Set<NodeTypeFamily>().add(NodeTypeFamily.ANALYTICS))}
                      setBackupValues={setBackupValues}
                      setClusterFormValue={setClusterFormValue}
                      setInstanceSize={(instanceSize, nodeTypeFamilySet) =>
                        this.setInstanceSize(instanceSize, nodeTypeFamilySet)
                      }
                      originalInstanceSize={originalAnalyticsInstanceSizeName}
                      isEdit={isEdit}
                      m0LimitReached={m0LimitReached}
                      isTenantUpgrade={isTenantUpgrade}
                      isCrossRegionEnabled={isCrossRegionEnabled}
                      isPrivateIPModeEnabled={isPrivateIPModeEnabled}
                      autoScaling={analyticsAutoScaling}
                      diskSizeGB={formValues.diskSizeGB}
                      diskIOPS={replicationSpecListUtils.getIOPSToDisplay(
                        providerOptions,
                        formValues.diskSizeGB,
                        formValues.replicationSpecList
                      )}
                      originalProviders={originalBackingProviders}
                      useInstanceSizeMaxStorage={useInstanceSizeMaxStorage}
                      originalEncryptEBSVolume={
                        replicationSpecListUtils.getFirstProviderAnalyticsHardwareSpec(
                          originalCluster.replicationSpecList,
                          CloudProvider.AWS
                        )?.encryptEBSVolume
                      }
                      encryptEBSVolume={
                        replicationSpecListUtils.getFirstProviderAnalyticsHardwareSpec(
                          formValues.replicationSpecList,
                          CloudProvider.AWS
                        )?.encryptEBSVolume
                      }
                      ebsVolumeType={
                        replicationSpecListUtils.getFirstProviderAnalyticsHardwareSpec(
                          formValues.replicationSpecList,
                          CloudProvider.AWS
                        )?.volumeType || null
                      }
                      diskBackupAllowed={diskBackupAllowed}
                      instanceClass={analyticsInstanceClass}
                      cloudContainers={cloudContainers}
                      backupEnabled={formValues.backupEnabled}
                      isInstanceSizeVisible={isInstanceSizeVisible}
                      isNDSGovEnabled={settingsModel.isNdsGovEnabled()}
                      isPredictiveFeatureEnabled={settingsModel.isPredictiveAutoScalingEnabled()}
                      isPredictiveSupportedM10M20={settingsModel.isPredictiveSupportedM10M20()}
                      useCNRegionsOnly={useCNRegionsOnly}
                      selectedVersion={selectedVersion}
                      isAutoIndexingEnabled={settingsModel.isAutoIndexingEnabled()}
                      isAutoIndexingEligible={isAutoIndexingEligible}
                      isAwsGravitonEnabled={isAwsGravitonEnabled}
                      awsGravitonMinimumMongoDBVersion={settingsModel.getAwsGravitonMinimumMongoDBVersion()}
                      originalPreferredCpuArchitecture={originalClusterPreferredCpuArch}
                      isAnalyticsTier
                      hasAnalyticsNodes={hasAnalyticsNodes}
                      shouldShowReplicationLagWarning={shouldShowReplicationLagWarning}
                      areInstanceClassesAsymmetric={areInstanceClassesAsymmetric}
                      hasUnsetOplogMinRetentionHours={hasUnsetOplogMinRetentionHours}
                      isOriginalClusterSharded={isOriginalClusterSharded}
                      isInAzureSsdV2Mode={isInAzureSsdV2Mode}
                      isAllowCrossRegionExtendedStorageFeatureFlagEnabled={
                        isAllowCrossRegionExtendedStorageFeatureFlagEnabled
                      }
                      hasGroupApiKeys={hasGroupApiKeys}
                      hasAsymmetricShards={hasAsymmetricShards}
                      clusterName={formValues.name}
                      clusterUsageStats={clusterUsageStats}
                      groupId={settingsModel.getCurrentGroupId()}
                    />
                  </Tab>
                )}
                {searchNodesEnabled && (
                  <Tab name={searchTierHeader}>
                    <NDSSearchDeploymentFormInstanceSize
                      providerOptions={providerOptions}
                      searchDeploymentSpec={searchDeploymentSpec}
                      replicationSpecList={formValues.replicationSpecList}
                      setClusterFormValue={setClusterFormValue}
                      hasStorageOptimizedSearchNodesUIEnabled={hasStorageOptimizedSearchNodesUIEnabled}
                    />
                  </Tab>
                )}
              </Tabs>
            )}
          </Accordion>
        )}
        {showClusterCapacity && (
          <Accordion
            ref={(ref) => {
              // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
              this[ACCORDION_NAMES.CLUSTER_CAPACITY.name] = ref;
            }}
            headlineText={ACCORDION_NAMES.CLUSTER_CAPACITY.headlineText}
            secondaryText={getClusterCapacitySubtext(providerOptionsToUse, this.props.formValues)}
            secondarySubText={''}
            onHeadlineClick={this.toggleActive(ACCORDION_NAMES.CLUSTER_CAPACITY, deploymentType, this.closeGuideCues)}
            active={!!openAccordions[ACCORDION_NAMES.CLUSTER_CAPACITY.name]}
            highlightSecondaryText={highlightAccordions[ACCORDION_NAMES.CLUSTER_CAPACITY.name]}
            errorSecondaryText={!isValid}
            animate
          >
            <NDSClusterFormClusterCapacity
              tenantProviderOptions={providerOptionsToUse}
              instanceSize={currentInstanceSizeName}
            />
          </Accordion>
        )}
        {scrollContainer && this.autoscalingSelectorRef.current && !hideGuideCues && (
          <>
            <GuideCue
              title="Auto-Scaling Enabled"
              refEl={this.autoscalingSelectorRef}
              open={this.isGuideCueOpen(ClusterBuilderGuideCue.ENABLE_AUTOSCALING)}
              setOpen={this.getIsGuideCueOpenSetter(ClusterBuilderGuideCue.ENABLE_AUTOSCALING)}
              numberOfSteps={1}
              tooltipAlign="right"
              tooltipJustify="middle"
              buttonText="Got it"
              spacing={60}
            >
              <p>
                You can configure the cluster tier ranges that Atlas uses to automatically scale your cluster tier,
                storage capacity, or both in response to cluster usage.
              </p>
              <p>Auto-scaling works on a rolling basis, meaning the process doesn't incur any downtime.</p>
            </GuideCue>
            {/* @experiment Dedicated User Onboarding | Jira Epic: CLOUDP-217593 */}
            <GuideCue
              title="Auto-scale"
              refEl={this.autoscalingSelectorRef}
              open={this.isGuideCueOpen(ClusterBuilderGuideCue.EDIT_AUTOSCALE)}
              setOpen={this.getIsGuideCueOpenSetter(ClusterBuilderGuideCue.EDIT_AUTOSCALE)}
              numberOfSteps={1}
              tooltipAlign="left"
              tooltipJustify="middle"
              buttonText="Got it"
              spacing={150}
            >
              <p>You can edit the auto-scaling configurations for your Dedicated cluster.</p>
            </GuideCue>
            <GuideCue
              title="Revise your Cluster Tier Scaling configuration"
              refEl={this.autoscalingSelectorRef}
              open={this.isGuideCueOpen(ClusterBuilderGuideCue.CONFIGURE_AUTOSCALING)}
              setOpen={this.getIsGuideCueOpenSetter(ClusterBuilderGuideCue.CONFIGURE_AUTOSCALING)}
              numberOfSteps={1}
              tooltipAlign="right"
              tooltipJustify="middle"
              buttonText="Got it"
              spacing={60}
            >
              Your cluster tier is at the limit of the auto-scaling range. Increase your maximum cluster size to better
              respond to usage.
            </GuideCue>
            <GuideCue
              title="M10 tier has higher storage"
              refEl={this.autoscalingSelectorRef}
              open={this.isGuideCueOpen(ClusterBuilderGuideCue.UPGRADE_STORAGE)}
              setOpen={this.getIsGuideCueOpenSetter(ClusterBuilderGuideCue.UPGRADE_STORAGE)}
              numberOfSteps={1}
              tooltipAlign="right"
              tooltipJustify="middle"
              buttonText="Got it"
              spacing={60}
            >
              <p>By upgrading to the M10 tier, you will have a higher storage limit and auto-scaling enabled.</p>
              <p>The M10 tier also includes backups, real time metrics, and many other features.</p>
            </GuideCue>
            <GuideCue
              title="M10 tier has a higher connection limit"
              refEl={this.autoscalingSelectorRef}
              open={this.isGuideCueOpen(ClusterBuilderGuideCue.UPGRADE_CONNECTIONS)}
              setOpen={this.getIsGuideCueOpenSetter(ClusterBuilderGuideCue.UPGRADE_CONNECTIONS)}
              numberOfSteps={1}
              tooltipAlign="right"
              tooltipJustify="middle"
              buttonText="Got it"
              spacing={60}
            >
              <p>By upgrading to the M10 tier, you will have a higher connection limit of 1,500. </p>
              <p>The M10 tier also includes backups, real time metrics, and many other features. </p>
            </GuideCue>
          </>
        )}
        <Accordion
          ref={(ref) => {
            // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
            this[ACCORDION_NAMES.ADDITIONAL_SETTINGS.name] = ref;
          }}
          headlineText={ACCORDION_NAMES.ADDITIONAL_SETTINGS.headlineText}
          secondaryText={this.getAdditionalSettingsSecondaryText()}
          secondarySubText={this.getBackupSubtext(diskBackupAllowed)}
          onHeadlineClick={this.toggleActive(ACCORDION_NAMES.ADDITIONAL_SETTINGS, deploymentType, this.closeGuideCues)}
          active={!!openAccordions[ACCORDION_NAMES.ADDITIONAL_SETTINGS.name]}
          highlightSecondaryText={highlightAccordions[ACCORDION_NAMES.ADDITIONAL_SETTINGS.name]}
          animate
          hasSummary
        >
          <>
            <NDSClusterFormVersion
              // NDSClusterFormVersion isn't conditionally rendered because it detects and handles
              // case where selectedVersion wouldn't be in "Select a Version" dropdown, which can
              // occur when NDSClusterFormVersion isn't visible.
              selectedVersion={selectedVersion}
              originalVersion={originalCluster.mongoDBMajorVersion}
              providerVersions={possibleMongoDBMajorVersions}
              defaultCDMongoDBVersion={providerOptionsToUse.defaultCDMongoDBVersion}
              defaultCDMongoDBFCV={providerOptionsToUse.defaultCDMongoDBFCV}
              fixedMongoDBFCV={originalCluster.fixedMongoDBFCV}
              instanceSize={currentInstanceSizeName}
              isEdit={isEdit}
              isLDAPEnabled={isLDAPEnabled}
              setClusterFormValue={setClusterFormValue}
              mongoDBMajorVersionsSubtext={crossCloudProviderOptions.mongoDBMajorVersionsSubtext || {}}
              replicationSpecList={formValues.replicationSpecList}
              isShardedCluster={formValues.clusterType !== ClusterType.REPLICASET}
              versionToDeprecate={settingsModel.getVersionToDeprecate()}
              versionDeprecatedByDate={endOfLifeUtils.getDeprecateByDateOrExtensionDate(settingsModel)}
              isAllowDeprecatedClusterVersionFeatureFlagEnabled={settingsModel.hasAtlasAllowDeprecatedVersions()}
              hasPendingExtensionRequest={endOfLifeUtils.hasPendingExtension(settingsModel)}
              shouldShowOptimizedPrivateEndpointConnectionStringWarning={
                updatedClusterWithPrivateEndpointSupportsOptimizedConnectionString &&
                !!originalCluster.mongoDBMajorVersion &&
                !isAtLeast5_0(originalCluster.mongoDBMajorVersion)
              }
              visible={isNotSharedOrUSSOrVersionSelectionAvailable}
              isCDFeatureFlagEnabled={settingsModel.hasProjectFeature('ATLAS_CONTINUOUS_DELIVERY')}
              currentClusterVersionReleaseSystem={originalCluster.versionReleaseSystem}
              selectedVersionReleaseSystem={formValues.versionReleaseSystem}
              searchNodesEnabled={searchNodesEnabled}
              isFCVPinningUiFeatureFlagEnabled={settingsModel.hasProjectFeature('ATLAS_FCV_PINNING_IN_UI')}
            />
            <hr className="nds-cluster-form-hr-thin" />
          </>
          <NDSClusterFormBackup
            providers={currentProviders}
            name={formValues.name}
            groupId={settingsModel.getCurrentGroupId()}
            docsUrl={docsUrl}
            backupEnabled={formValues.backupEnabled}
            diskBackupEnabled={formValues.diskBackupEnabled}
            diskBackupAllowed={diskBackupAllowed}
            tenantBackupEnabled={!!formValues.tenantBackupEnabled}
            isEdit={isEdit}
            isTenant={isTenant}
            isFree={isFree}
            hasEncryptionAtRestProvider={isClusterEncryptionAtRestEnabled}
            setClusterFormValue={setClusterFormValue}
            setBackupValues={setBackupValues}
            billingEstimate={billingEstimate}
            isNVMe={isNVMe}
            pitEnabled={formValues.pitEnabled}
            hasPITBeenToggledOff={this.hasPITBeenToggledOff()}
            hasPITBeenToggledOn={this.hasPITBeenToggledOn()}
            mongoDBVersion={selectedVersion}
            isMongoDBVersionUpgrade={isEdit && originalVersion !== selectedVersion}
            onlyShowDiskBackup={onlyShowDiskBackup}
            hasSnapshotDistribution={settingsModel.hasProjectFeature('CPS_SNAPSHOT_DISTRIBUTION_UI')}
            originalCluster={originalCluster}
            isNdsGovEnabled={settingsModel.isNdsGovEnabled()}
            isBackupLockEnabled={backupUtils.isBackupLockEnabledForGaOrMVP(settingsModel, dataProtectionSettings)}
            isBackupLockPitEnabled={backupUtils.isBackupLockPitEnabledForGaOrMVP(settingsModel, dataProtectionSettings)}
            backupPitRef={this.backupPitSelectorRef}
            settingsModel={settingsModel}
            displayFlex={displayFlex}
            isFlex={isFlex}
          />
          {scrollContainer && this.backupPitSelectorRef.current && !hideGuideCues && (
            <GuideCue
              title="Cloud Backup & Continuous Cloud Backup Selected"
              refEl={this.backupPitSelectorRef}
              open={this.isGuideCueOpen(ClusterBuilderGuideCue.ENABLE_BACKUPS)}
              setOpen={this.getIsGuideCueOpenSetter(ClusterBuilderGuideCue.ENABLE_BACKUPS)}
              numberOfSteps={1}
              tooltipAlign="right"
              tooltipJustify="middle"
              buttonText="Got it"
              spacing={105}
            >
              Click Review Changes below to continue.
            </GuideCue>
          )}

          <hr className="nds-cluster-form-hr-thin" />
          <NDSClusterFormTerminationProtection
            terminationProtectionEnabled={formValues.terminationProtectionEnabled ?? false}
            onTerminationProtectionChange={(enabled) => setClusterFormValue('terminationProtectionEnabled', enabled)}
            isGroupOwner={settingsModel.isGroupOwner()}
            isServerlessInstance={false}
          />
          {(clusterBuilderFilterInterface.advancedOptions.shardingVisible ||
            clusterBuilderFilterInterface.advancedOptions.otherOptionsVisible) && (
            <>
              <hr className="nds-cluster-form-hr-thick" />
              <div className="nds-cluster-form-setting nds-cluster-form-padding-top nds-cluster-form-subsection-header">
                Advanced Settings
              </div>
              {!isGeoZoned && clusterBuilderFilterInterface.advancedOptions.shardingVisible && (
                <>
                  <NDSClusterFormSharding
                    docsUrl={docsUrl}
                    clusterType={formValues.clusterType}
                    replicationSpec={selectedReplicationSpec}
                    replicationSpecList={formValues.replicationSpecList}
                    isEdit={isEdit}
                    setClusterFormValue={setClusterFormValue}
                    updateReplicationSpecList={this.updateReplicationSpecList}
                    updateShardsCountForReplicationSpecList={this.updateShardsCountForReplicationSpecList}
                    instanceSupportsSharding={shardingEnabled}
                    minComputeInstanceSizeSupportsSharding={minComputeInstanceSizeSupportsSharding}
                    isOriginalClusterSharded={isOriginalClusterSharded}
                    originalInstanceSize={originalInstanceSizeName}
                    shouldShowOptimizedPrivateEndpointConnectionStringWarning={
                      updatedClusterWithPrivateEndpointSupportsOptimizedConnectionString &&
                      originalCluster.clusterType === ClusterType.REPLICASET
                    }
                    minShardingInstanceSize={minShardingInstanceSize}
                    isNdsGovEnabled={settingsModel.isNdsGovEnabled()}
                    searchNodesEnabled={searchNodesEnabled}
                    mongoDBVersion={mongoDBVersionToUseInAdvancedOptions}
                    settingsModel={settingsModel}
                    configServerTypeEstimate={configServerTypeEstimate}
                    totalShards={numShards}
                  />
                  <hr className="nds-cluster-form-hr-thin" />
                </>
              )}
              {clusterBuilderFilterInterface.advancedOptions.otherOptionsVisible && (
                <>
                  {showBIConnectorSection && (
                    <NDSClusterFormBIConnector
                      docsUrl={docsUrl}
                      biConnector={formValues.biConnector}
                      instanceSize={currentInstanceSize.highCPUEquivalent || currentInstanceSize.name}
                      biConnectorCostEstimate={biConnectorCostEstimate}
                      isTenant={isTenant}
                      setClusterFormValue={setClusterFormValue}
                      hasAnalyticsNodes={hasAnalyticsNodes}
                      originalClusterBiConnector={originalCluster.biConnector}
                      isEdit={isEdit}
                      isNdsGovEnabled={settingsModel.isNdsGovEnabled()}
                      isBICDeprecationShowWarningEnabled={isBICDeprecationShowWarningEnabled}
                    />
                  )}
                  {formValues.biConnector.enabled && (
                    <>
                      {showBIConnectorDeprecationWarning && (
                        <Banner variant={Variant.Warning} className="nds-cluster-form-bi-connector-warning-banner">
                          <a
                            href="https://www.mongodb.com/docs/atlas/tutorial/transition-bic-to-atlas-sql/"
                            target="_blank"
                            rel="noreferrer"
                          >
                            Transition to Atlas SQL&nbsp;
                            <i className="fa fa-external-link" />
                          </a>
                          {'. '}
                          <strong>The BI Connector will be deprecated by {BI_CONNECTOR_DEPRECATION_DATE}.</strong>
                          <Body>
                            The BI Connector will no longer be supported after deprecation. Transition to the{' '}
                            <a
                              href="https://www.mongodb.com/docs/atlas/tutorial/transition-bic-to-atlas-sql/"
                              target="_blank"
                              rel="noreferrer"
                            >
                              Atlas SQL Interface&nbsp;
                              <i className="fa fa-external-link" />
                            </a>{' '}
                            now to gain a first-class SQL querying experience with natively built SQL-92 compatible
                            dialect, SQL schema management, and easy setup and installation.
                          </Body>
                        </Banner>
                      )}
                      {!showBIConnectorDeprecationWarning && showSqlInterfaceBanner && !isNDSGovEnabled && (
                        <Banner
                          dismissible
                          className="nds-cluster-form-bi-connector-warning-banner"
                          onClose={this.onDismissSqlInterfaceBanner}
                        >
                          <div>
                            <strong>Try the New Atlas SQL Interface Instead</strong>{' '}
                          </div>
                          The new{' '}
                          <DocsLink docsPath="query-with-sql" onClick={this.onClickSqlInterfaceDocs}>
                            Atlas SQL Interface
                          </DocsLink>{' '}
                          makes it easy to connect sql-based tools to query you Atlas data. This interface is compatible
                          and can be accessed using a SQL ODBC/JDBC driver or an Atlas SQL custom connector, such as
                          Atlas Power BI connector or the Atlas Tableau connector. Atlas SQL is pay-per-use since it
                          leverages Atlas Data Federation for data processing.
                        </Banner>
                      )}
                      <NDSClusterFormBIConnectorAdvanced
                        setClusterFormValue={setClusterFormValue}
                        processArgs={processArgs}
                      />
                    </>
                  )}
                  {showBIConnectorSection && <hr className="nds-cluster-form-hr-thin" />}
                  {isAtlasSqlClusterSchemaInterfaceEnabled && (
                    <NDSClusterFormSQLSchemaInterface
                      instanceSize={currentInstanceSizeName}
                      isTenant={isTenant}
                      isNdsGovEnabled={isNDSGovEnabled}
                      clusterName={formValues.name}
                    />
                  )}
                  {isAtlasSqlClusterSchemaInterfaceEnabled && <hr className="nds-cluster-form-hr-thin" />}
                  <NDSClusterFormEncryptionAtRestProvider
                    docsUrl={docsUrl}
                    enabledBackingProviders={enabledBackingProviders}
                    encryptionAtRestProvider={formValues.encryptionAtRestProvider}
                    setClusterFormValue={setClusterFormValue}
                    isEdit={isEdit}
                    isTenant={isTenant}
                    encryptionAtRest={encryptionAtRest}
                    isHeadBackupEnabled={formValues.backupEnabled}
                    defaultEncryptionAtRestProvider={
                      defaultTemplates[currentBackingProviders[0]][defaultTemplateKey].encryptionAtRestProvider
                    }
                    originalEncryptionAtRestProvider={originalEncryptionAtRestProvider}
                    minClusterTier={settingsModel.isNdsGovEnabled() ? undefined : 'M10'}
                    isBackupLockEAREnabled={backupUtils.isBackupLockEnabledForGAWithEAR(
                      settingsModel,
                      dataProtectionSettings
                    )}
                    onlineArchiveEncryption={settingsModel.hasProjectFeature('ONLINE_ARCHIVE_ENCRYPTION')}
                    isEncryptionAtRestAzureKeyVaultPrivateEndpointEnabled={
                      isEncryptionAtRestAzureKeyVaultPrivateEndpointEnabled
                    }
                    isEncryptionAtRestPrivateNetworkingUIEnabled={isEncryptionAtRestPrivateNetworkingUIEnabled}
                    hasAtlasSearchByokEnabled={hasAtlasSearchByokEnabled}
                    searchNodesEnabled={searchNodesEnabled}
                  />
                  {isEncryptionAtRestPrivateNetworkingUIEnabled &&
                    requirePrivateNetworkingEnabled(encryptionAtRest, formValues.encryptionAtRestProvider) && (
                      <NDSClusterFormEncryptionAtRestPrivateNetworking
                        encryptionAtRestProvider={formValues.encryptionAtRestProvider}
                        clusterRegions={replicationSpecListRegions(formValues.replicationSpecList).filter(
                          (c) => !!c.regionName
                        )}
                        earPrivateEndpoints={getEncryptionAtRestPrivateEndpoints(
                          encryptionAtRest,
                          formValues.encryptionAtRestProvider
                        )}
                      />
                    )}

                  {!isTenant && !isServerlessUpgradeToDedicated && (
                    <NDSClusterFormAdvancedOptions
                      originalCluster={originalCluster}
                      processArgs={processArgs}
                      originalProcessArgs={originalProcessArgs}
                      diskSizeGB={formValues.diskSizeGB}
                      setClusterFormValue={setClusterFormValue}
                      diskWarmingMode={formValues.diskWarmingMode}
                      setDiskWarmingMode={setDiskWarmingMode}
                      replicaSetScalingStrategy={formValues.replicaSetScalingStrategy}
                      setReplicaSetScalingStrategy={setReplicaSetScalingStrategy}
                      isDiskWarmingFeatureEnabled={isDiskWarmingFeatureEnabled}
                      isDiskWarmingFeatureDisabledForAzure={isDiskWarmingFeatureDisabledForAzure}
                      redactClientLogData={formValues.redactClientLogData}
                      setRedactClientLogData={setRedactClientLogData}
                      configServerManagementMode={formValues.configServerManagementMode}
                      currentProviders={currentProviders}
                      mongoDBVersion={mongoDBVersionToUseInAdvancedOptions}
                      clusterUsageStats={clusterUsageStats}
                      autoScaling={autoScaling}
                      isNVMe={isNVMe}
                      vCPU={currentInstanceSize.vCPU}
                      clusterType={formValues.clusterType}
                      isEdit={isEdit}
                      settingsModel={settingsModel}
                      configServerTypeEstimate={configServerTypeEstimate}
                    />
                  )}
                </>
              )}
            </>
          )}
        </Accordion>
        {(!isEdit || isResourceTaggingEnabled) && (
          <Accordion
            ref={(ref) => {
              // @ts-expect-error TS(7053): Element implicitly has an 'any' type because expre... Remove this comment to see the full error message
              this[ACCORDION_NAMES.NAME_CLUSTER.name] = ref;
            }}
            headlineText={isResourceTaggingEnabled ? ACCORDION_NAMES.NAME_CLUSTER.headlineText : 'Cluster Name'}
            secondaryText={`${formValues.name || 'Please Enter a Name'}`}
            secondarySubText={isResourceTaggingEnabled ? pluralize('Tag', this.props.tags.length, true) : ''}
            onHeadlineClick={this.toggleActive(ACCORDION_NAMES.NAME_CLUSTER, deploymentType, this.closeGuideCues)}
            active={!!openAccordions[ACCORDION_NAMES.NAME_CLUSTER.name]}
            highlightSecondaryText={highlightAccordions[ACCORDION_NAMES.NAME_CLUSTER.name]}
            errorSecondaryText={
              !isNameValid ||
              !isNameUnique ||
              !isNamePrefixUnique ||
              !isNameWithinCharLimit ||
              isNameEndWithHyphen ||
              isNameInUseByClusterWithBackupSnapshots
            }
            animate
          >
            {!isEdit && (
              <NDSClusterFormName
                setClusterFormValue={setClusterFormValue}
                name={formValues.name}
                isNameValid={isNameValid}
                isNameUnique={isNameUnique}
                isNamePrefixUnique={isNamePrefixUnique}
                isNameEndWithHyphen={isNameEndWithHyphen}
                isNameWithinCharLimit={isNameWithinCharLimit}
                isNameInUseByClusterWithBackupSnapshots={isNameInUseByClusterWithBackupSnapshots}
                isResourceTaggingEnabled={isResourceTaggingEnabled}
                computeUnit="cluster"
              />
            )}
            {isResourceTaggingEnabled && (
              <NDSClusterFormTags
                tags={this.props.tags}
                onChange={this.props.setTags}
                onTagInputBlur={this.props.onTagInputBlur}
                clusterId={originalCluster.uniqueId}
                clusterName={formValues.name}
                groupId={settingsModel.getCurrentGroupId()}
                computeUnit="cluster"
              />
            )}
          </Accordion>
        )}
        {!isEdit && (
          <div>
            <ErrorToast
              open={!!error && EXCLUDED_ERROR_CODES.indexOf(error.errorCode) === -1}
              title="Error"
              description={error?.message}
              variant={Variant.Warning}
              onClose={() => this.props.setError(null)}
            />
            <br />
          </div>
        )}
      </div>
    );
  }
}

export default NDSClusterForm;
