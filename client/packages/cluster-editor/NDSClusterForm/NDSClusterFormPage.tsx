import { useMemo, useRef, useState } from 'react';

import { css } from '@emotion/react';
import styled from '@emotion/styled';
import { usePoller } from '@leafygreen-ui/hooks';
import { BackLink } from '@leafygreen-ui/typography';
import { connect } from 'react-redux';

import { ExperimentFeatureFlag, TestName } from '@packages/types/abTest';
import { PartnerSubscriptionStatus, PartnerType } from '@packages/types/billing';
import { ClusterConfigurationForClusterBuilder } from '@packages/types/clusterConfiguration';
import { ClusterDraftResponse, ClusterTransactionPurchase } from '@packages/types/clusterDrafts';
import { BillingEstimate } from '@packages/types/nds/billingEstimate';
import { ClusterDescription } from '@packages/types/nds/clusterDescription';
import { AccordionName, ClusterBuilderFilterInterface, SharedTierTerm } from '@packages/types/nds/clusterEditor';
import { ClusterOutageSimulation } from '@packages/types/nds/clusterOutageSimulation';
import { ClusterUsageStats } from '@packages/types/nds/ClusterUsageStats';
import { DefaultTemplates, TemplateKey } from '@packages/types/nds/defaultTemplate';
import { InstanceHardwareReplicationView } from '@packages/types/nds/instanceHardwareReplicationView';
import { ProcessArgs } from '@packages/types/nds/ProcessArgs';
import {
  CloudProvider,
  NonServerlessCloudProvider,
  ProviderFeature,
  ProviderToOptionsMapping,
  RegionProviderFeatures,
  TenantProviderOptionsView,
} from '@packages/types/nds/provider';
import { RegionName } from '@packages/types/nds/region';
import { RegionUsageRestrictions } from '@packages/types/nds/regionUsageRestrictions';
import { CustomRole } from '@packages/types/nds/role';
import { AuditLog } from '@packages/types/nds/security/auditLog';
import { CloudTeams, ErrorSeverity } from '@packages/types/observability';
import { FromLocation, HighlightInPage } from '@packages/types/routes';
import { SearchDeployment } from '@packages/types/search/decoupled/deployment';
import { SettingsModel } from '@packages/types/settingsModel';
import { UserSecurity } from '@packages/types/UserSecurity';
import { WithDefaultProps } from '@packages/types/withDefaultProps';

import * as app from '@packages/redux/common/app';
import * as auditLogReducer from '@packages/redux/common/nds/auditLog';
import * as clusterDescriptionsReducer from '@packages/redux/common/nds/clusterDescriptions';
import * as clusterForm from '@packages/redux/common/nds/clusterForm';
import * as customRolesReducer from '@packages/redux/common/nds/customRoles';
import * as deletedClusterNamesWithActiveSnapshotsReducer from '@packages/redux/common/nds/deletedClusterNamesWithActiveSnapshots';
import * as encryptionAtRestReducer from '@packages/redux/common/nds/encryptionAtRest';
import * as searchDeployment from '@packages/redux/common/nds/searchDeployment';
import * as serverlessInstanceForm from '@packages/redux/common/nds/serverlessInstanceForm';
import * as serverlessInstances from '@packages/redux/common/nds/serverlessInstances';
import * as userSecurityReducer from '@packages/redux/common/nds/userSecurity';
import * as purchase from '@packages/redux/common/purchase';
import * as settings from '@packages/redux/common/settings';
import * as uiPreferences from '@packages/redux/common/uiPreferences';
import { getActiveGroupUseCNRegionsOnly } from '@packages/redux/common/app';
import { loadAWSPrivateLinks, loadAzurePrivateLinks, loadGCPPSCs } from '@packages/redux/common/nds/privateEndpoint';
import { getRegionUsageRestrictions, loadRegionUsageRestrictions } from '@packages/redux/common/nds/projectSettings';

import * as abTestUtil from '@packages/common/utils/abTestUtil';
import * as clusterDescriptionUtils from '@packages/common/utils/clusterDescription';
import * as experimentUtils from '@packages/common/utils/experimentUtils';
import ClusterConfigurationAiAssistant from '@packages/cluster-configuration-ai-assistant/components/ClusterConfigurationAiAssistant';
import { ClusterConfigurationParameters } from '@packages/cluster-configuration/parameterizedTransformationUtility';
import EncryptionAtRest from '@packages/cluster-editor/EncryptionAtRest/EncryptionAtRest';
import { CLUSTER_DRAFTS_TEAM } from '@packages/common/constants/errorTracker';
import { Routes } from '@packages/common/constants/project';
import { PrivateIpMode } from '@packages/common/schemas/ndsClusterForm';
import { ContainerShape } from '@packages/common/schemas/peeringSchema';
import {
  getMarketplaceAccountLinkQueryString,
  getMarketplacePartnerTypeFromPaymentMethod,
} from '@packages/common/utils/billingHelpers';
import { getDefaultProviderTemplate } from '@packages/common/utils/clusterBuilderHelpers';
import { navigateTo } from '@packages/common/utils/navigateTo';
import replicationSpecListUtils from '@packages/common/utils/replicationSpecList';
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import PartnerLinkOrgStatusToast from '@packages/components/billing/PartnerLinkOrgStatusToast';
import { BreadcrumbsRedux } from '@packages/components/Breadcrumbs';
import EditorLayout, { EditorLayoutBody, EditorLayoutHeader } from '@packages/components/EditorLayout';
import { ErrorBoundary } from '@packages/components/ErrorBoundary';
import Loader from '@packages/components/Loader';
import OrganizationResourcePolicyBanner from '@packages/components/ResourcePolicy/OrganizationResourcePolicyBanner';
import { useMarketplaceLinkStatusPoller } from '@packages/hooks/useMarketplaceLinkStatusPoller';
import { RequestState } from '@packages/hooks/useRequestInfo';
import { sendError } from '@packages/observability';

import NDSClusterFormController from './NDSClusterFormController';

const OrganizationBannerWrapper = styled.div`
  margin-bottom: 12px;
`;
const navigateToStarterTemplatesPage = () => {
  analytics.track(SEGMENT_EVENTS.BUTTON_CLICKED, {
    context: 'Cluster Builder',
    action: 'Back to CST Button Clicked',
  });
  navigateTo(`#/${Routes.StarterTemplates({ query: `from=${FromLocation.CLUSTER_BUILDER}` })}`);
};

// TODO<CLOUDP-289651>: Remove serverless front-end code.
const getProcessArgsForClusterForm = (state: any, existingClusterDescription: ClusterDescription | null) => {
  // Note: Serverless instances will not have process args so use the default process args.
  if (existingClusterDescription != null) {
    if (replicationSpecListUtils.isServerlessCluster(existingClusterDescription.replicationSpecList)) {
      return clusterForm.getDefaultProcessArgs(state);
    } else {
      return clusterForm.getProcessArgs(state);
    }
  } else {
    return clusterForm.getDefaultProcessArgs(state);
  }
};

interface OwnNDSClusterFormPageProps {
  clusterId?: string;
  settingsModel: SettingsModel;
  existingCluster?: ClusterDescription;
  loadDefaultTemplates: $TSFixMeFunction;
  loadDeletedClusterNamesWithActiveSnapshots: $TSFixMeFunction;
  loadProviderOptions: (groupId: string) => any;
  loadServerlessProviderOptions: (groupId: string) => any;
  loadProcessArgs: $TSFixMeFunction;
  loadDefaultProcessArgs: $TSFixMeFunction;
  loadClusterDescriptions: $TSFixMeFunction;
  loadGroupLimits: $TSFixMeFunction;
  loadUserSecurity: $TSFixMeFunction;
  loadAuditLog: $TSFixMeFunction;
  loadEncryptionAtRest: $TSFixMeFunction;
  loadPrivateIpMode: $TSFixMeFunction;
  loadCloudContainers: $TSFixMeFunction;
  loadClusterUsageStats: (groupId: string, clusterName: string) => any;
  loadServerlessInstanceUsageStats: (groupId: string, clusterName: string) => any;
  loadCustomRoles: $TSFixMeFunction;
  loadAWSPrivateLinks: $TSFixMeFunction;
  loadActiveOrgPaymentMethods: $TSFixMeFunction;
  loadAzurePrivateLinks: $TSFixMeFunction;
  loadGCPPSCs: $TSFixMeFunction;
  loadRegionUsageRestrictions: $TSFixMeFunction;
  loadInstanceHardwareReplicationViews: $TSFixMeFunction;
  loadClusterOutageSimulation: $TSFixMeFunction;
  loadSearchDeployment: $TSFixMeFunction;
  existingSearchDeployment: SearchDeployment | null;
  fetchSearchDeploymentFailed: boolean;
  toggleGroupEditableFeatureFlag: $TSFixMeFunction;
  activePaymentMethod?: $TSFixMe;
  paymentMethods?: Array<$TSFixMe>;
  storeClusterTransaction: (transaction: ClusterTransactionPurchase) => string | undefined;
  transactionId?: string;
  loadedClusterTransaction?: ClusterTransactionPurchase | null;
  deletedClusterNamesWithActiveSnapshots: Array<string>;
  clusterDescriptions?: Array<ClusterDescription>;
  groupLimits?: {
    [key: string]: number;
  };
  defaultTemplates: DefaultTemplates;
  providerOptions: ProviderToOptionsMapping;
  serverlessProviderOptions: TenantProviderOptionsView;
  crossCloudProviderOptions: $TSFixMe | {};
  processArgs: ProcessArgs;
  userSecurity: UserSecurity;
  auditLog: AuditLog;
  encryptionAtRest: typeof EncryptionAtRest;
  privateIpMode: PrivateIpMode;
  useCNRegionsOnly: boolean;
  cloudContainers: ContainerShape;
  clusterUsageStats: ClusterUsageStats;
  providerFromQuery?: string;
  customRoles: Array<CustomRole>;
  serverlessInstances?: Array<ClusterDescription>;
  clusterBuilderFilterInterface?: ClusterBuilderFilterInterface;
  fromLocation?: string;
  highlight?: HighlightInPage;
  enableBackups?: boolean;
  enableAutoscaling?: boolean;
  fromFpp?: boolean;
  clusterDraft: ClusterDraftResponse | null;
  clusterDraftRequestState: RequestState;
  tier?: string;
  review?: boolean;
  deploymentType: number;
  setDeploymentType: $TSFixMeFunction;
  projectHasSharedCluster?: boolean;
  hasServerlessProjectFeatureEnabled?: boolean;
  instanceHardwareReplicationViews?: Array<InstanceHardwareReplicationView>;
  clusterOutageSimulation?: ClusterOutageSimulation;
  openSection: null | AccordionName;
  expectMarketplaceAccountLink?: PartnerType;
  hasQuickAccess?: boolean;
  hasAutoRetrieve?: boolean;
  hasGenAIFeaturesEnabled?: boolean;
  hasDecoupledSearchFeatureEnabled: boolean;
  hasStorageOptimizedSearchNodesUIEnabled: boolean;
  defaultRegionKey?: string;
  defaultCloudProvider?: NonServerlessCloudProvider;
  loadCrossCloudClusterProviderOptions: $TSFixMeFunction;
  loadServerlessInstances: $TSFixMeFunction;
  hasChurnDowngradeSuggestion: boolean;
  azureSsdV2RegionNames: Array<RegionName>;
  regionUsageRestrictions: RegionUsageRestrictions;
  isAllowCrossRegionExtendedStorageFeatureFlagEnabled: boolean;
  skipClusterDraftLoad: boolean;
  loadDraftToast?: boolean;
}

const nDSClusterFormPageDefaultProps = {
  clusterId: null,
  existingCluster: null,
  clusterDescriptions: [],
  deletedClusterNamesWithActiveSnapshots: [],
  groupLimits: {},
  clusterUsageStats: { clusterMaxDiskUsageMB: 0, currentOplogUsageSizeMB: 0 },
  providerFromQuery: null,
  activePaymentMethod: null,
  paymentMethods: null,
  serverlessInstances: [],
  clusterBuilderFilterInterface: {
    isGlobalConfigurationVisible: true,
    isMultiRegionConfigurationVisible: true,
    advancedOptions: {
      shardingVisible: true,
      otherOptionsVisible: true,
    },
    providerTemplateKey: TemplateKey.REPLICA_SET,
  },
  fromLocation: null,
  fromFpp: false,
  clusterDraft: null,
  clusterDraftRequestState: RequestState.Loading,
  tier: null,
  review: false,
  instanceHardwareReplicationViews: [],
  clusterOutageSimulation: undefined,
  transactionId: null,
  loadedClusterTransaction: null,
  openSection: null,
  expectMarketplaceAccountLink: null,
  existingSearchDeployment: null,
  fetchSearchDeploymentFailed: false,
  isAllowCrossRegionExtendedStorageFeatureFlagEnabled: false,
  loadDraftToast: undefined,
};

type NDSClusterFormPageProps = WithDefaultProps<OwnNDSClusterFormPageProps, typeof nDSClusterFormPageDefaultProps>;

function NDSClusterFormPage(props: NDSClusterFormPageProps) {
  const createClusterButtonRef = useRef<HTMLDivElement>(null);
  const [aiAssistantError, setAiAssistantError] = useState(false);
  const {
    clusterId,
    settingsModel,
    existingCluster,
    loadDefaultTemplates,
    loadDeletedClusterNamesWithActiveSnapshots,
    loadClusterDescriptions,
    loadGroupLimits,
    loadProviderOptions,
    loadServerlessProviderOptions,
    loadCrossCloudClusterProviderOptions,
    loadProcessArgs,
    loadDefaultProcessArgs,
    loadUserSecurity,
    loadAuditLog,
    loadEncryptionAtRest,
    loadPrivateIpMode,
    loadCloudContainers,
    loadClusterUsageStats,
    loadServerlessInstanceUsageStats,
    loadCustomRoles,
    loadServerlessInstances,
    loadAWSPrivateLinks,
    loadAzurePrivateLinks,
    loadGCPPSCs,
    loadRegionUsageRestrictions,
    loadInstanceHardwareReplicationViews,
    loadClusterOutageSimulation,
    toggleGroupEditableFeatureFlag,
    loadActiveOrgPaymentMethods,
    loadSearchDeployment,
    existingSearchDeployment,
    fetchSearchDeploymentFailed,
    defaultTemplates,
    providerOptions,
    serverlessProviderOptions,
    crossCloudProviderOptions,
    processArgs,
    deletedClusterNamesWithActiveSnapshots,
    clusterDescriptions,
    groupLimits,
    userSecurity,
    auditLog,
    encryptionAtRest,
    privateIpMode,
    useCNRegionsOnly,
    cloudContainers,
    clusterUsageStats,
    providerFromQuery,
    customRoles,
    serverlessInstances,
    clusterBuilderFilterInterface,
    fromLocation,
    highlight,
    enableBackups,
    enableAutoscaling,
    fromFpp,
    clusterDraft,
    clusterDraftRequestState,
    tier,
    review,
    deploymentType,
    setDeploymentType,
    projectHasSharedCluster,
    hasServerlessProjectFeatureEnabled,
    instanceHardwareReplicationViews,
    clusterOutageSimulation,
    hasQuickAccess,
    hasAutoRetrieve,
    hasGenAIFeaturesEnabled,
    hasDecoupledSearchFeatureEnabled,
    hasStorageOptimizedSearchNodesUIEnabled,
    defaultRegionKey,
    defaultCloudProvider,
    storeClusterTransaction,
    loadedClusterTransaction,
    activePaymentMethod,
    paymentMethods,
    openSection,
    expectMarketplaceAccountLink,
    hasChurnDowngradeSuggestion,
    azureSsdV2RegionNames,
    regionUsageRestrictions,
    isAllowCrossRegionExtendedStorageFeatureFlagEnabled,
    skipClusterDraftLoad,
    loadDraftToast,
  } = props;

  const { marketplaceAccountLinkStatus } = useMarketplaceLinkStatusPoller();

  const marketplacePaymentMethod = paymentMethods?.find(
    (paymentMethod) => getMarketplacePartnerTypeFromPaymentMethod(paymentMethod) != null
  );
  const marketplacePartnerType =
    getMarketplaceAccountLinkQueryString() ?? getMarketplacePartnerTypeFromPaymentMethod(marketplacePaymentMethod);

  usePoller(loadActiveOrgPaymentMethods, {
    interval: 5000,
    immediate: true,
    enabled: expectMarketplaceAccountLink != null && expectMarketplaceAccountLink !== marketplacePartnerType,
  });

  const showMarketplaceToast =
    marketplaceAccountLinkStatus &&
    marketplaceAccountLinkStatus !== PartnerSubscriptionStatus.NOT_LINKING &&
    marketplacePartnerType != null;

  const groupId = settingsModel.getCurrentGroupId();
  const orgId = settingsModel.getCurrentOrgId();
  const isAtlasResourcePoliciesEnabled = settingsModel.isAtlasResourcePoliciesEnabled();

  const sharedTierTerm = abTestUtil.getExperimentFeatureFlag(
    ExperimentFeatureFlag.sharedTierTerm,
    SharedTierTerm.SHARED
  );

  const displayFlex = !settingsModel.isServerlessSharedUIOptionEnabled() && settingsModel.isNdsFlexEnabled();

  const isEdit = !!clusterId;
  const defaultProvider =
    providerFromQuery || clusterDescriptionUtils.getDefaultProvider(clusterDescriptions, privateIpMode.enabled);

  const providerTemplate = getDefaultProviderTemplate(
    defaultTemplates,
    // @ts-expect-error Argument of type 'string | number' is not assignable to parameter of type 'BackingCloudProvider | NonServerlessCloudProvider'
    defaultProvider,
    clusterBuilderFilterInterface.providerTemplateKey,
    deploymentType
  );

  const isAzureSsdV2FeatureEnabled =
    settingsModel.hasProjectFeature('ATLAS_AZURE_SSD_PV2') &&
    !settingsModel.hasProjectFeature('ATLAS_AZURE_SSD_FORCE_PV1');

  const isDiskWarmingFeatureEnabled = settingsModel.hasProjectFeature('ATLAS_DISK_WARMING_SUPPORT');
  const isDiskWarmingFeatureDisabledForAzure = settingsModel.hasProjectFeature('ATLAS_TURN_DISK_WARMING_OFF_FOR_AZURE');

  const regionProviderFeatures: RegionProviderFeatures = {
    // a mapping between azure region names and Azure ssd v2 provider features
    [CloudProvider.AZURE]: azureSsdV2RegionNames.reduce(
      (obj, key) => ({
        ...obj,
        [key]: [ProviderFeature.DISK_SIZES_PRECISE, ProviderFeature.IOPS_PROVISIONED],
      }),
      {}
    ),
  };

  // Update default IOPS for Azure SSD v2
  if (defaultProvider === CloudProvider.AZURE && providerOptions.AZURE && providerTemplate) {
    const instanceSize = replicationSpecListUtils.getFirstInstanceSize(providerTemplate.replicationSpecList);
    const azureInstance = providerOptions.AZURE.instanceSizes[instanceSize]!;
    const clusterRegionsSupportAzureSsdV2 = replicationSpecListUtils.clusterRegionsSupportAzureSsdV2(
      providerTemplate.replicationSpecList,
      regionProviderFeatures
    );
    if (clusterRegionsSupportAzureSsdV2 && isAzureSsdV2FeatureEnabled) {
      providerTemplate.replicationSpecList = replicationSpecListUtils.ensureAzureSsdV2IOPS(
        providerTemplate.replicationSpecList,
        azureInstance,
        providerTemplate.diskSizeGB
      );
    }
  }

  const getExistingClusterOrRedirect = () => {
    if (existingCluster == null) {
      sendError({
        error: Error(
          `User attempted to load cluster draft for cluster that does not exist. groupId: [${groupId}], clusterId: [${clusterId}]`
        ),
        team: CLUSTER_DRAFTS_TEAM,
        severity: ErrorSeverity.WARNING,
      });
      navigateTo(Routes.ClusterCard({ clusterNotFound: true }));
    }
    return existingCluster;
  };
  const hasFreeCluster = clusterDescriptionUtils.hasFreeCluster(clusterDescriptions);
  const hasAnyCluster = !!clusterDescriptions.length;
  const originalClusterPreBackupsChange = isEdit ? getExistingClusterOrRedirect() : providerTemplate;
  const siteName = settingsModel.get('SITE_NAME');
  const showWelcomeMessage = !isEdit && !hasAnyCluster;
  const isOriginallyServerless =
    originalClusterPreBackupsChange &&
    replicationSpecListUtils.isServerlessCluster(originalClusterPreBackupsChange.replicationSpecList);
  const originalCluster = useMemo(() => {
    return originalClusterPreBackupsChange != null && (isOriginallyServerless || enableBackups)
      ? { ...originalClusterPreBackupsChange, diskBackupEnabled: true, pitEnabled: true }
      : originalClusterPreBackupsChange;
  }, [originalClusterPreBackupsChange, isOriginallyServerless, enableBackups]);
  const loadUsageStats = isEdit
    ? () =>
        isOriginallyServerless
          ? loadServerlessInstanceUsageStats(groupId, clusterId)
          : loadClusterUsageStats(groupId, clusterId)
    : () => Promise.resolve();
  const loadProcessArgsPromise = isEdit
    ? isOriginallyServerless
      ? () => loadDefaultProcessArgs(groupId)
      : () => loadProcessArgs(groupId, clusterId)
    : () => loadDefaultProcessArgs(groupId);
  const loadInstances = settingsModel.isNdsServerlessEnabled()
    ? () => loadServerlessInstances(groupId)
    : () => Promise.resolve();
  const loadInstanceHardwareReplicationViewsPromise = isEdit
    ? () => loadInstanceHardwareReplicationViews(groupId, clusterId)
    : () => Promise.resolve();

  let formTitle = `Create New Cluster`;
  if (clusterBuilderFilterInterface.providerTemplateKey === 'm0') {
    formTitle = `Create a ${displayFlex ? 'Free' : sharedTierTerm} Cluster`;
  } else if (clusterBuilderFilterInterface.providerTemplateKey === 'replicaSetM10') {
    formTitle = `Create a Dedicated Cluster`;
  } else if (
    clusterBuilderFilterInterface.providerTemplateKey === 'crossRegionReplicaSet' ||
    clusterBuilderFilterInterface.providerTemplateKey === 'geoSharded3Zone'
  ) {
    formTitle = `Create a Dedicated Multi-Cloud & Multi-Region Cluster`;
  }

  const clusterNames = clusterDescriptions
    .map((cluster) => cluster.name)
    .concat(serverlessInstances.map((instance) => instance.name));

  const doLoadCrossCloudClusterProviderOptions = () => {
    let providers;
    if (existingCluster == null) {
      providers = [defaultProvider];
    } else {
      providers = replicationSpecListUtils.getCloudProviders(existingCluster.replicationSpecList);
    }
    return loadCrossCloudClusterProviderOptions(groupId, providers);
  };

  const getPageTitle = () => {
    if (isEdit) {
      return `Editing ${clusterId}`;
    }
    return formTitle;
  };

  const getBreadcrumbText = () => {
    if (isEdit) {
      return `Edit ${clusterId}`;
    }
    return formTitle;
  };

  const loadSearchDeployment_ =
    hasDecoupledSearchFeatureEnabled && isEdit
      ? () => loadSearchDeployment(groupId, clusterId)
      : () => Promise.resolve();

  const backButton =
    fromLocation === FromLocation.STARTER_TEMPLATES ? (
      <div>
        <BackLink onClick={navigateToStarterTemplatesPage}>Back</BackLink>
      </div>
    ) : null;

  // AI Cluster Assistant | Jira Epic: CLOUDP-313767
  experimentUtils.useTrackIsInSample({
    experimentName: TestName.aiClusterAssistant,
    team: CloudTeams.AtlasGrowth,
    shouldFireEvent: hasGenAIFeaturesEnabled,
  });

  const showAiAssistantOnClusterBuilder =
    hasGenAIFeaturesEnabled &&
    experimentUtils.getExperimentAttribute(ExperimentFeatureFlag.showAiAssistantOnClusterBuilder, false);

  const controllerRef = useRef<NDSClusterFormController>(null);
  const onApplySuggestedParameters = (parameters: ClusterConfigurationParameters) => {
    if (!controllerRef.current) {
      throw new Error('controllerRef is not set for applying AI Assistant cluster configuration parameters.');
    }
    controllerRef.current?.applyAiAssistantClusterConfigurationParameters(parameters);
  };

  // Use refs to maintain stable references for cluster configuration and billing estimate
  const currentClusterConfigurationRef = useRef<ClusterConfigurationForClusterBuilder | undefined>(undefined);
  const currentBillingEstimateRef = useRef<BillingEstimate | undefined>(undefined);

  // Callback to handle cluster configuration changes
  const handleClusterConfigurationChange = (
    clusterConfiguration: ClusterConfigurationForClusterBuilder,
    billingEstimate: BillingEstimate
  ) => {
    currentClusterConfigurationRef.current = clusterConfiguration;
    currentBillingEstimateRef.current = billingEstimate;
  };

  const pageContent = (
    <EditorLayout>
      <EditorLayoutHeader
        hideBackLink
        headlineText={getPageTitle()}
        newNavCss={css`
          margin-left: auto;
          margin-right: auto;
          width: 870px;

          ${showAiAssistantOnClusterBuilder &&
          `
            + div {
              border-bottom: none;
            }
            margin-bottom: 0;
          `}
        `}
      >
        <BreadcrumbsRedux
          items={[
            { href: '#/clusters', text: `clusters` },
            { href: `#/${Routes.ClusterBuilder()}`, text: getBreadcrumbText() },
          ]}
          includeDefaultPrefixes={false}
          showLastAsCurrent
        />
        <h1>
          <strong>{getPageTitle()}</strong>
        </h1>
        {backButton}
      </EditorLayoutHeader>
      <EditorLayoutBody>
        <Loader
          className="nds-cluster-form-loader"
          load={() =>
            Promise.all([
              loadDefaultTemplates(groupId),
              loadDeletedClusterNamesWithActiveSnapshots(groupId),
              loadClusterDescriptions(groupId),
              loadGroupLimits(groupId),
              loadProviderOptions(groupId),
              loadServerlessProviderOptions(groupId),
              loadUserSecurity(groupId),
              loadAuditLog(groupId),
              loadEncryptionAtRest(groupId),
              loadPrivateIpMode(groupId),
              loadCloudContainers(groupId),
              loadUsageStats(),
              loadProcessArgsPromise(),
              loadCustomRoles(groupId),
              loadInstances(),
              loadAWSPrivateLinks(groupId),
              loadAzurePrivateLinks(groupId),
              loadGCPPSCs(groupId),
              loadRegionUsageRestrictions(groupId),
              loadInstanceHardwareReplicationViewsPromise(),
              loadClusterOutageSimulation(groupId, clusterId),
              loadActiveOrgPaymentMethods(),
              loadSearchDeployment_(),
            ])
          }
          renderWhenLoaded
        >
          <Loader load={doLoadCrossCloudClusterProviderOptions} renderWhenLoaded>
            {isAtlasResourcePoliciesEnabled && (
              <OrganizationBannerWrapper>
                <OrganizationResourcePolicyBanner orgId={orgId}></OrganizationResourcePolicyBanner>
              </OrganizationBannerWrapper>
            )}
            {originalCluster && [
              <NDSClusterFormController
                ref={controllerRef}
                originalCluster={originalCluster}
                originalProcessArgs={processArgs}
                defaultTemplates={defaultTemplates}
                // Remove this comment to see the full error message
                // @ts-expect-error TS(2304): Cannot find name 'groupLimits'.
                groupLimits={groupLimits}
                providerOptions={providerOptions}
                serverlessProviderOptions={serverlessProviderOptions}
                crossCloudProviderOptions={crossCloudProviderOptions}
                clusterNames={clusterNames}
                deletedClusterNamesWithActiveSnapshots={deletedClusterNamesWithActiveSnapshots}
                isEdit={isEdit}
                settingsModel={settingsModel}
                userSecurity={userSecurity}
                auditLog={auditLog}
                // Remove this comment to see the full error message
                // @ts-expect-error TS(2304): Cannot find name 'encryptionAtRest'.
                encryptionAtRest={encryptionAtRest}
                privateIpMode={privateIpMode}
                useCNRegionsOnly={useCNRegionsOnly}
                clusterUsageStats={clusterUsageStats}
                customRoles={customRoles}
                key="cluster-form-controller"
                // Remove this comment to see the full error message
                // @ts-expect-error TS(2304): Cannot find name 'cloudContainers'.
                cloudContainers={cloudContainers}
                clusterDescriptions={clusterDescriptions}
                clusterBuilderFilterInterface={clusterBuilderFilterInterface}
                // Remove this comment to see the full error message
                // @ts-expect-error TS(2304): Cannot find name 'fromLocation'.
                fromLocation={fromLocation}
                highlight={highlight}
                enableBackups={enableBackups}
                enableAutoscaling={enableAutoscaling}
                fromFpp={fromFpp}
                clusterDraft={clusterDraft}
                clusterDraftRequestState={clusterDraftRequestState}
                // Remove this comment to see the full error message
                // @ts-expect-error TS(2304): Cannot find name 'tier'.
                preSelectedTier={tier}
                goToReview={isEdit && review}
                deploymentType={deploymentType}
                setDeploymentType={setDeploymentType}
                // Remove this comment to see the full error message
                // @ts-expect-error TS(2304): Cannot find name 'hasServerlessProjectFeatureEnabl... Remove this comment to see the full error message
                hasServerlessProjectFeatureEnabled={hasServerlessProjectFeatureEnabled}
                sharedTierTerm={sharedTierTerm}
                hasFreeCluster={hasFreeCluster}
                // Remove this comment to see the full error message
                // @ts-expect-error TS(2304): Cannot find name 'projectHasSharedCluster'.
                projectHasSharedCluster={projectHasSharedCluster}
                instanceHardwareReplicationViews={instanceHardwareReplicationViews}
                clusterOutageSimulation={clusterOutageSimulation}
                // Remove this comment to see the full error message
                // @ts-expect-error TS(2304): Cannot find name 'hasQuickAccess'.
                hasQuickAccess={hasQuickAccess}
                // Remove this comment to see the full error message
                // @ts-expect-error TS(2304): Cannot find name 'hasAutoRetrieve'.
                hasAutoRetrieve={hasAutoRetrieve}
                hasDecoupledSearchFeatureEnabled={hasDecoupledSearchFeatureEnabled}
                hasStorageOptimizedSearchNodesUIEnabled={hasStorageOptimizedSearchNodesUIEnabled}
                toggleGroupEditableFeatureFlag={toggleGroupEditableFeatureFlag}
                marketplaceAccountLinkStatus={marketplaceAccountLinkStatus}
                defaultRegionKey={defaultRegionKey}
                defaultCloudProvider={defaultCloudProvider}
                storeClusterTransaction={storeClusterTransaction}
                loadedClusterTransaction={loadedClusterTransaction}
                openSection={openSection}
                activePaymentMethod={activePaymentMethod}
                paymentMethods={paymentMethods}
                loadActiveOrgPaymentMethods={loadActiveOrgPaymentMethods}
                expectMarketplaceAccountLink={expectMarketplaceAccountLink}
                showWelcomeMessage={showWelcomeMessage}
                siteName={siteName}
                existingSearchDeployment={existingSearchDeployment}
                fetchSearchDeploymentFailed={fetchSearchDeploymentFailed}
                hasChurnDowngradeSuggestion={hasChurnDowngradeSuggestion}
                regionProviderFeatures={regionProviderFeatures}
                isAzureSsdV2FeatureEnabled={isAzureSsdV2FeatureEnabled}
                isDiskWarmingFeatureEnabled={isDiskWarmingFeatureEnabled}
                isDiskWarmingFeatureDisabledForAzure={isDiskWarmingFeatureDisabledForAzure}
                regionUsageRestrictions={regionUsageRestrictions}
                isAllowCrossRegionExtendedStorageFeatureFlagEnabled={
                  isAllowCrossRegionExtendedStorageFeatureFlagEnabled
                }
                skipClusterDraftLoad={skipClusterDraftLoad}
                displayFlex={displayFlex}
                loadDraftToast={loadDraftToast}
                createClusterButtonRef={createClusterButtonRef}
                onClusterConfigurationChange={handleClusterConfigurationChange}
              />,
            ]}
          </Loader>
        </Loader>

        {showMarketplaceToast && (
          <PartnerLinkOrgStatusToast status={marketplaceAccountLinkStatus} partnerType={marketplacePartnerType} />
        )}
      </EditorLayoutBody>
    </EditorLayout>
  );

  // AI Cluster Assistant | Jira Epic: CLOUDP-313767
  const aiAssistantWrappedPageContent = (
    <ErrorBoundary
      onError={(error, componentStack, metadata) => {
        setAiAssistantError(true);
        sendError({
          error,
          team: metadata.team,
          extras: {
            name: metadata.name,
            ...metadata.extra,
            componentStack,
          },
          severity: error.severity ?? ErrorSeverity.ERROR,
        });
      }}
      metadata={{
        name: 'AI Cluster Assistant: React ErrorBoundary Hit',
        team: CloudTeams.AtlasGrowth,
      }}
    >
      <ClusterConfigurationAiAssistant
        userId={settingsModel.getCurrentUserId()}
        clusterConfiguration={currentClusterConfigurationRef.current}
        originalCluster={originalCluster}
        billingEstimate={currentBillingEstimateRef.current}
        isEdit={isEdit}
        createClusterButtonRef={createClusterButtonRef}
        onApplySuggestedParameters={onApplySuggestedParameters}
        clusterDescriptions={clusterDescriptions}
      >
        {pageContent}
      </ClusterConfigurationAiAssistant>
    </ErrorBoundary>
  );

  return showAiAssistantOnClusterBuilder && !aiAssistantError ? aiAssistantWrappedPageContent : pageContent;
}

NDSClusterFormPage.defaultProps = nDSClusterFormPageDefaultProps;

export default connect(
  (state, props: $TSFixMe) => {
    const existingClusterDescription: ClusterDescription | null = props.clusterId
      ? clusterDescriptionsReducer.getClusterDescription(state, { clusterName: props.clusterId })
      : null;

    return {
      defaultTemplates: clusterForm.getDefaultTemplates(state),
      providerOptions: clusterForm.getProviderOptions(state),
      serverlessProviderOptions: serverlessInstanceForm.getServerlessProviderOptions(state),
      crossCloudProviderOptions: clusterForm.getCrossCloudProviderOptions(state),
      processArgs: getProcessArgsForClusterForm(state, existingClusterDescription),
      existingCluster: existingClusterDescription,
      deletedClusterNamesWithActiveSnapshots:
        deletedClusterNamesWithActiveSnapshotsReducer.getDeletedClusterNamesWithActiveSnapshots(state),
      clusterDescriptions: clusterDescriptionsReducer.getClusterDescriptionsAsList(state),
      groupLimits: clusterForm.getGroupLimits(state),
      userSecurity: userSecurityReducer.getUserSecurity(state),
      auditLog: auditLogReducer.getAuditLog(state),
      encryptionAtRest: encryptionAtRestReducer.getEncryptionAtRest(state),
      privateIpMode: clusterForm.getPrivateIpMode(state),
      useCNRegionsOnly: getActiveGroupUseCNRegionsOnly(state),
      cloudContainers: clusterForm.getCloudContainers(state),
      clusterUsageStats: props.clusterId
        ? clusterForm.getClusterUsageStats(state)
        : NDSClusterFormPage.defaultProps.clusterUsageStats,
      providerFromQuery: (uiPreferences.getRegistrationData(state) as $TSFixMe).cp
        ? (uiPreferences.getRegistrationData(state) as $TSFixMe).cp.trim().toUpperCase()
        : null,
      customRoles: customRolesReducer.getCustomRoles(state),
      serverlessInstances: serverlessInstances.getServerlessInstancesAsList(state),
      instanceHardwareReplicationViews: clusterForm.getInstanceHardwareReplicationViews(state),
      clusterOutageSimulation: clusterForm.getClusterOutageSimulation(state),
      hasQuickAccess: !settings.hasProjectFeature(state, 'ATLAS_HIDE_QUICK_ACCESS_PAGE'),
      hasAutoRetrieve: settings.hasOrgFeature(state, 'ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'),
      // AI Cluster Assistant | Jira Epic: CLOUDP-313767
      hasGenAIFeaturesEnabled: settings.hasOrgFeature(state, 'ORG_LEVEL_GEN_AI_CONTROL_SWITCH'),
      hasDecoupledSearchFeatureEnabled: settings.hasDecoupledSearchFeatureEnabled(state),
      hasStorageOptimizedSearchNodesUIEnabled: settings.hasStorageOptimizedSearchNodesUIEnabled(state),
      // @ts-expect-error Argument of type 'DefaultRootState' is not assignable to parameter of type 'State'
      loadedClusterTransaction: purchase.getTransactionConfigurationObjectForActiveGroupAndOrg(state, {
        transactionId: props.transactionId,
      }),
      activePaymentMethod: app.getActiveOrgActivePaymentMethod(state),
      paymentMethods: app.getActiveOrgPaymentMethods(state),
      existingSearchDeployment: searchDeployment.getSearchDeployment(state),
      fetchSearchDeploymentFailed: searchDeployment.getFetchSearchDeploymentFailed(state),
      azureSsdV2RegionNames: settings.getAzureSsdV2RegionNames(state),
      regionUsageRestrictions: getRegionUsageRestrictions(state),
    };
  },
  {
    loadDefaultTemplates: clusterForm.loadDefaultTemplates,
    loadDeletedClusterNamesWithActiveSnapshots:
      deletedClusterNamesWithActiveSnapshotsReducer.loadDeletedClusterNamesWithActiveSnapshots,
    loadClusterDescriptions: clusterDescriptionsReducer.loadClusterDescriptions,
    loadGroupLimits: clusterForm.loadGroupLimits,
    loadProviderOptions: clusterForm.loadProviderOptions,
    loadServerlessProviderOptions: serverlessInstanceForm.loadServerlessProviderOptions,
    loadCrossCloudClusterProviderOptions: clusterForm.loadCrossCloudProviderOptions,
    loadProcessArgs: clusterForm.loadProcessArgs,
    loadDefaultProcessArgs: clusterForm.loadDefaultProcessArgs,
    loadUserSecurity: userSecurityReducer.loadUserSecurity,
    loadAuditLog: auditLogReducer.loadAuditLog,
    loadEncryptionAtRest: encryptionAtRestReducer.loadEncryptionAtRest,
    loadPrivateIpMode: clusterForm.loadPrivateIpMode,
    loadCloudContainers: clusterForm.loadCloudContainers,
    loadClusterUsageStats: clusterForm.loadClusterUsageStats,
    loadServerlessInstanceUsageStats: clusterForm.loadServerlessInstanceUsageStats,
    loadCustomRoles: customRolesReducer.loadCustomRoles,
    loadServerlessInstances: serverlessInstances.loadServerlessInstances,
    loadAWSPrivateLinks,
    loadAzurePrivateLinks,
    loadGCPPSCs,
    loadRegionUsageRestrictions,
    loadInstanceHardwareReplicationViews: clusterForm.loadInstanceHardwareReplicationViews,
    loadClusterOutageSimulation: clusterForm.loadClusterOutageSimulation,
    storeClusterTransaction: purchase.storeClusterTransactionForCurrentGroup,
    toggleGroupEditableFeatureFlag: settings.toggleGroupEditableFeatureFlag,
    loadActiveOrgPaymentMethods: app.loadActiveOrgPaymentMethods,
    loadSearchDeployment: searchDeployment.loadSearchDeployment,
  }
)(NDSClusterFormPage);

export { NDSClusterFormPage };
