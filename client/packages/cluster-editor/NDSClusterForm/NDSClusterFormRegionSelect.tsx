import * as React from 'react';

import classNames from 'classnames';

import { BackingCloudProviderName, RegionProviderFeatures } from '@packages/types/nds/provider';
import { RegionName } from '@packages/types/nds/region';
import { RegionView, ReplicationSpec, ReplicationSpecList } from '@packages/types/nds/replicationSpec';

import { RegionGroupingType } from '@packages/common/utils/clusterEditorUtils';
import replicationSpecListUtils from '@packages/common/utils/replicationSpecList';
import RegionSelect from '@packages/components/RegionSelect';

interface DisabledChecks {
  checkPreferred?: boolean;
  checkElectable?: boolean;
  checkReadOnly?: boolean;
  checkAnalytics?: boolean;
  checkDiskSizeLargerThanStandardMax?: boolean;
  checkSearchSupported?: boolean;
  checkAzureSsdV2?: boolean;
}

interface Props {
  value: string;
  cloudProvider: BackingCloudProviderName;
  replicationSpecList: ReplicationSpecList;
  replicationSpec: ReplicationSpec;
  regions: Array<RegionView>;
  availableRegions: Array<RegionName>;
  searchSupportedRegionNames: Array<RegionName>;
  // @ts-expect-error TS(7051): Parameter has a name but no type. Did you mean 'ar... Remove this comment to see the full error message
  onChange: (string) => void;
  className?: string;
  disabledChecks?: DisabledChecks;
  regionGroupingType?: RegionGroupingType;
  disabled?: boolean;
  disabledReason?: string;
  regionProviderFeatures?: RegionProviderFeatures;
}

export default function NDSClusterFormRegionSelect(props: Props) {
  const {
    value,
    cloudProvider,
    onChange,
    replicationSpec,
    replicationSpecList,
    regions,
    availableRegions,
    searchSupportedRegionNames,
    className = null,
    disabledChecks = {},
    regionGroupingType = RegionGroupingType.CONTINENT,
    disabled = false,
    disabledReason = '',
    regionProviderFeatures,
  } = props;

  const preferredRegion = replicationSpecListUtils.getPreferredRegion(replicationSpec);

  function isRegionElectable(regionName: RegionName): boolean {
    return !replicationSpecListUtils
      .getUnusedElectableRegions(replicationSpec, regions)
      .some((region: RegionView) => region.key === regionName);
  }

  function isRegionReadOnly(regionName: RegionName): boolean {
    return !replicationSpecListUtils
      .getUnusedReadOnlyRegions(replicationSpec, regions)
      .some((region: RegionView) => region.key === regionName);
  }

  function isRegionAnalytics(regionName: RegionName): boolean {
    return !replicationSpecListUtils
      .getUnusedAnalyticsRegions(replicationSpec, regions)
      .some((region: RegionView) => region.key === regionName);
  }

  function isRegionPreferred(regionName: RegionName): boolean {
    return !replicationSpecListUtils
      .getUnusedPreferredRegions(replicationSpecList, regions)
      .some((region: RegionView) => region.key === regionName);
  }

  function isRegionSupportedBySearchNodes(pRegionName: RegionName): boolean {
    return searchSupportedRegionNames.includes(pRegionName);
  }

  function isRegionAvailableForAzureSsdV2(regionName: RegionName): boolean {
    return (
      !!regionProviderFeatures && replicationSpecListUtils.regionSupportsAzureSsdV2(regionName, regionProviderFeatures)
    );
  }

  function isRegionDisabled(regionName: RegionName): boolean {
    const {
      checkPreferred = false,
      checkElectable = false,
      checkReadOnly = false,
      checkAnalytics = false,
      checkDiskSizeLargerThanStandardMax = false,
      checkSearchSupported = false,
      checkAzureSsdV2 = false,
    } = disabledChecks;

    return (
      (checkAnalytics && isRegionAnalytics(regionName)) ||
      (checkReadOnly && isRegionReadOnly(regionName)) ||
      (checkPreferred && isRegionPreferred(regionName)) ||
      (checkElectable && isRegionElectable(regionName)) ||
      (availableRegions && !availableRegions.includes(regionName)) ||
      (checkDiskSizeLargerThanStandardMax && regionName !== preferredRegion.regionName) ||
      (checkSearchSupported && !isRegionSupportedBySearchNodes(regionName)) ||
      (checkAzureSsdV2 && !isRegionAvailableForAzureSsdV2(regionName))
    );
  }

  function getPreferredTooltipContent(key: RegionName): React.ReactElement | undefined {
    const otherPreferredZone: string | undefined = replicationSpecListUtils.getZoneWithPreferredRegion(
      replicationSpecList,
      key
    );
    if (otherPreferredZone) {
      return (
        <div className="nds-cluster-form-tooltip">
          This region has already been selected as the preferred region for the <strong>{otherPreferredZone}</strong>{' '}
          zone
        </div>
      );
    }

    return undefined;
  }

  function getElectableTooltipContent(key: RegionName): React.ReactElement | undefined {
    if (isRegionElectable(key)) {
      const selection =
        preferredRegion.regionView.key === key && preferredRegion?.cloudProvider === cloudProvider
          ? 'the preferred region'
          : 'an electable region';

      return <div className="nds-cluster-form-tooltip">This region has already been selected as {selection}</div>;
    }

    return undefined;
  }

  function getReadOnlyTooltipContent(key: RegionName): React.ReactElement | undefined {
    if (isRegionReadOnly(key)) {
      return (
        <div className="nds-cluster-form-tooltip">This region has already been selected as a read-only replica</div>
      );
    }

    return undefined;
  }

  function getAnalyticsTooltipContent(key: RegionName): React.ReactElement | undefined {
    if (isRegionAnalytics(key)) {
      return (
        <div className="nds-cluster-form-tooltip">This region has already been selected as an analytics replica</div>
      );
    }

    return undefined;
  }

  function getNoAvailableInstanceSizeTooltipContent(key: RegionName): React.ReactElement | undefined {
    if (!availableRegions.includes(key)) {
      return (
        <div className="nds-cluster-form-tooltip">
          This region is not supported with your existing configuration. Try changing to a different cluster tier or
          switching to a different region.
        </div>
      );
    }

    return undefined;
  }

  function getExtendedDiskSizeTooltipContent(): React.ReactElement {
    return (
      <div className="nds-cluster-form-tooltip">
        Multi-cloud is not available for clusters with extended storage sizes.
      </div>
    );
  }

  function getSearchNodesUnsupportedRegionTooltipContent(regionName: RegionName): React.ReactElement | undefined {
    if (!isRegionSupportedBySearchNodes(regionName)) {
      return (
        <div className="nds-cluster-form-tooltip">This region is not supported when Search Nodes are present.</div>
      );
    }

    return undefined;
  }

  function getAzureSsdV2RegionUnavailableTooltipContent(regionName: RegionName): React.ReactElement | undefined {
    if (!isRegionAvailableForAzureSsdV2(regionName)) {
      return <div className="nds-cluster-form-tooltip">This region does not support Extended Storage.</div>;
    }
    return undefined;
  }

  function getTooltipContent(key: RegionName): React.ReactElement | undefined {
    const {
      checkPreferred,
      checkElectable,
      checkReadOnly,
      checkAnalytics,
      checkDiskSizeLargerThanStandardMax,
      checkSearchSupported,
      checkAzureSsdV2,
    } = disabledChecks;

    if (checkDiskSizeLargerThanStandardMax) {
      return getExtendedDiskSizeTooltipContent();
    }

    const tooltip =
      (checkAnalytics && getAnalyticsTooltipContent(key)) ||
      (checkReadOnly && getReadOnlyTooltipContent(key)) ||
      (checkPreferred && getPreferredTooltipContent(key)) ||
      (checkElectable && getElectableTooltipContent(key)) ||
      (availableRegions && getNoAvailableInstanceSizeTooltipContent(key)) ||
      (checkSearchSupported && getSearchNodesUnsupportedRegionTooltipContent(key)) ||
      (checkAzureSsdV2 && getAzureSsdV2RegionUnavailableTooltipContent(key));

    return tooltip || undefined;
  }

  return (
    <RegionSelect
      value={value}
      onChange={onChange}
      tooltipRenderer={getTooltipContent}
      regions={regions}
      className={classNames('dropdown-select', {
        [className as any]: !!className,
      })}
      isRegionDisabled={isRegionDisabled}
      regionGroupingType={regionGroupingType}
      searchable
      placeholder="Choose a region"
      disabled={disabled}
      disabledReason={disabledReason}
    />
  );
}
