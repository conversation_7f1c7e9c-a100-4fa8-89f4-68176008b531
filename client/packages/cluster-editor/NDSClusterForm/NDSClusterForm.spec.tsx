import Banner from '@leafygreen-ui/banner';
import { Tab } from '@leafygreen-ui/tabs';
// test
import chai, { expect } from 'chai';
import chaiEnzyme from 'chai-enzyme';
import enzyme, { ReactWrapper } from 'enzyme';
import { Provider } from 'react-redux';
import sinon from 'sinon';
import sinon<PERSON><PERSON> from 'sinon-chai';

import { ClusterType, VersionReleaseSystem } from '@packages/types/nds/clusterDescription';
import { ACCORDION_NAMES, DeploymentType } from '@packages/types/nds/clusterEditor';
import { TemplateKey } from '@packages/types/nds/defaultTemplate';
import Limits from '@packages/types/nds/Limits';
import { BackingCloudProvider, CloudProvider } from '@packages/types/nds/provider';
import { RegionUsageRestrictions } from '@packages/types/nds/regionUsageRestrictions';

import * as api from '@packages/common/services/api';
import * as experimentUtils from '@packages/common/utils/experimentUtils';
import NDSClusterForm from '@packages/cluster-editor/NDSClusterForm/NDSClusterForm';
import NDSClusterFormAdvancedOptions from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormAdvancedOptions/NDSClusterFormAdvancedOptions';
import NDSClusterFormBIConnector from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormBIConnector';
import NDSClusterFormEncryptionAtRestProvider from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormEncryptionAtRestProvider';
import NDSClusterFormGeoOverView from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormGeoOverview';
import NDSClusterFormGeoZones from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormGeoZones';
import NDSClusterFormInstanceSize from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormInstanceSize';
import NDSClusterFormName from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormName';
import NDSClusterFormSharding from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormSharding';
import NDSClusterFormSQLSchemaInterface from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormSQLSchemaInterface';
import NDSClusterFormStorageSection from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormStorageSection';
import NDSClusterFormTags from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormTags';
import NDSClusterFormTerminationProtection from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormTerminationProtection';
import NDSClusterFormVersion from '@packages/cluster-editor/NDSClusterForm/NDSClusterFormVersion';
import { NDSSearchDeploymentFormInstanceSize } from '@packages/cluster-editor/NDSClusterForm/NDSSearchDeploymentFormInstanceSize';
import clusterFixtures from '@packages/common/fixtures/clusterFixtures';
import Settings from '@packages/common/models/Settings';
import { INSTANCE_CLASS } from '@packages/common/schemas/ndsClusterForm';
import { isAtLeast5_0 } from '@packages/common/utils/mongoDbVersionHelpers';
// analytics
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import Accordion from '@packages/components/Accordion';
import { createMockStore } from '@packages/test-utils/createMockStore';

import NDSClusterFormProviderButtons from './NDSClusterFormProviderButtons';

chai.use(chaiEnzyme());
chai.use(sinonChai);

const BI_CONNECTOR_COST_ESTIMATE = {
  biConnectorMaxDailyCents: 100,
  biConnectorMaxMonthlyCents: 1500,
  biConnectorSustainedDailyCents: 150,
};

const DEFAULT_LIMITS: Limits = {
  azurePrivateLinkInboundNATRuleMaximumPort: 2524,
  azurePrivateLinkInboundNATRuleMinimumPort: 1024,
  azurePrivateLinkMaxNodesPerPrivateLinkRegion: 150,
  clusterNameUniquePrefixLength: 23,
  exportIops: 300,
  gcpPSCNATSubnetMask: 27,
  maxActiveOnlineArchivesPerCluster: 20,
  maxConcurrentPlans: 51,
  maxCrossRegionNetworkPermissionEntries: 40,
  maxCustomRolesPerUser: 20,
  maxDataLakeTenants: 25,
  maxManualDownloads: 30,
  maxNetworkPermissionEntries: 200,
  maxNodesPerPrivateLinkRegion: 50,
  maxOnlineArchivesPerCluster: 50,
  maxValidAtlasGeneratedCerts: 50,
  maxZonesPerGeoCluster: 13,
  mongodbUsers: 100,
  numBackgroundCustomRoles: 100,
  numClusters: 25,
  numServerlessMTMs: 100,
  numPrivateServiceConnectionsPerRegionGroup: 50,
  numUserCustomRoles: 100,
  pushBasedLogExportTempCredentialSessionHours: 12,
  streamingRestoreIops: 300,
};

describe('@packages/cluster-editor/NDSClusterForm/NDSClusterForm', function () {
  beforeEach(function (this: $TSFixMe) {
    this.sandbox = sinon.createSandbox();
    this.clock = sinon.useFakeTimers();
    this.sandbox.stub(api.billing, 'getPaymentMethods').returns(Promise.resolve({}));
    this.sandbox.stub(api.billing, 'getCredits').returns([]);
    this.sandbox.stub(api.nds.clusterDescriptions, 'getNewFormLocationMapping').returns(new Promise(() => {}));
    this.sandbox.stub(api.nds.backup, 'getDiskBackupMetadata').returns(Promise.resolve({}));
    this.getExperimentAttributeStub = this.sandbox.stub(experimentUtils, 'getExperimentAttribute');

    this.isNameValid = true;
    this.isNameUnique = true;
    this.isNamePrefixUnique = true;
    this.isNameEndWithHyphen = false;
    this.isNameWithinCharLimit = true;

    this.settingsModel = new Settings({
      GROUP_ID: '123',
      ORG_ID: '408',
      DOCS_URL: '',
      RECAPTCHA_PUBLIC: '',
      HAS_ORG_BILLING_ADMIN: true,
      IS_NDS_GOV_ENABLED: false,
      NDS_FLEX_FEATURE_ENABLED: true,
      SERVERLESS_SHARED_UI_OPTION_ENABLED: false,
    });

    this.sandbox.stub(analytics, 'track');

    this.state = {
      settings: {
        publishableKeyInc: 'a',
        publishableKeyLtd: 'a',
      },
      app: {},
      organization: {},
      nds: {
        projectSettings: {
          regionUsageRestrictions: 'NONE',
        },
      },
      viewer: {},
      uiPreferences: {
        localCurrency: '$ USD',
      },
    };
    this.WrapperComponent = (props = {}) => (
      <Provider store={createMockStore(this.state)}>
        {/* @ts-expect-error TS(2769): No overload matches this call. */}
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues()}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          {...props}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      </Provider>
    );
  });

  afterEach(function (this: $TSFixMe) {
    this.sandbox.restore();
    this.clock.restore();
  });

  describe('initial animation behavior', function () {
    describe('if creating a new cluster', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper = enzyme.mount(<this.WrapperComponent />);
        this.clock.tick(0);
        this.wrapper.update();
      });

      afterEach(function (this: $TSFixMe) {
        this.wrapper.unmount();
      });

      it('animates the first accordion to the open position when loaded', function (this: $TSFixMe) {
        expect(this.wrapper.find(Accordion).at(1).prop('active')).to.equal(true);

        expect(this.wrapper.find(NDSClusterForm).state().openAccordions).to.eql({
          additionalSettings: false,
          globalConfiguration: false,
          cloudProvider: true,
          instanceTier: false,
          nameCluster: false,
        });
      });

      it('hides the next button at the bottom of the accordion', function (this: $TSFixMe) {
        expect(this.wrapper.find(Accordion).at(1).find('.accordion-footer').length).to.equal(0);
        expect(this.wrapper.find(Accordion).at(1).find('.editor-layout-footer button[type="button"]').length).to.equal(
          0
        );
      });

      it('defaults to the first replication spec as the default selected spec', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterForm).state().newFormSelectedReplicationSpecId).to.equal(
          '000000000000000000000001'
        );
      });
    });

    describe('if editing an existing tenant cluster', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper = enzyme.mount(<this.WrapperComponent isEdit isTenantUpgrade />);
        this.clock.tick(0);
      });

      afterEach(function (this: $TSFixMe) {
        this.wrapper.unmount();
      });

      it('does not animate the first accordion to the open position when loaded', function (this: $TSFixMe) {
        expect(this.wrapper.find(Accordion).at(0).prop('active')).to.equal(false);
      });

      it('does animate the third accordion to the open position when loaded', function (this: $TSFixMe) {
        expect(this.wrapper.find(Accordion).at(2).prop('active')).to.equal(true);
      });

      describe('and the first accordion is opened', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.find(Accordion).at(0).simulate('click');
        });

        it('does not show a footer at the bottom of the accordion', function (this: $TSFixMe) {
          expect(this.wrapper.find(Accordion).at(0).find('.accordion-footer').length).to.equal(0);
        });
      });
    });
  });

  describe('when rendered without an error', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    it('shows Accordions for the form subsections', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).length).to.equal(5);
    });

    it('shows Accordions for Cluster Tier to have overflow inherited', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).at(2).prop('inheritOverflow')).to.be.true;
    });

    it('does not show an ErrorToast', function (this: $TSFixMe) {
      expect(this.wrapper.find('ErrorToast').props().open).to.equal(false);
    });
  });

  describe('when rendered without an error with replicationSpecList', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues_WithReplicationSpecList()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_withReplicationSpecList_asJSON()}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    it('shows Accordions for the form subsections', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).length).to.equal(5);
    });
    it('shows Accordions for Cluster Tier to have overflow inherited', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).at(2).prop('inheritOverflow')).to.be.true;
    });

    it('does not show an ErrorToast', function (this: $TSFixMe) {
      expect(this.wrapper.find('ErrorToast').props().open).to.equal(false);
    });
  });

  describe('when rendered with an error', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues()}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          error={{ message: 'foo', errorCode: 'foo' }}
          isEdit={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    it('shows Accordions for the form subsections', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).length).to.equal(5);
    });

    it('shows Accordions for Cluster Tier to have overflow inherited', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).at(2).prop('inheritOverflow')).to.be.true;
    });

    it('shows an ErrorToast', function (this: $TSFixMe) {
      expect(this.wrapper.find('ErrorToast').props().open).to.equal(true);
    });

    describe('Accordion Name Validation', function () {
      beforeEach(function (this: $TSFixMe) {
        this.nameAccordionIndex = this.wrapper.find(Accordion).length - 1;
      });
      describe('name is valid', function () {
        it('accordion should not show name error', function (this: $TSFixMe) {
          expect(this.wrapper.find(Accordion).at(this.nameAccordionIndex).prop('errorSecondaryText')).to.equal(false);
        });
      });
      describe('name is not a valid format', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.setProps({ isNameValid: false });
        });

        it('accordion should show name error', function (this: $TSFixMe) {
          expect(this.wrapper.find(Accordion).at(this.nameAccordionIndex).prop('errorSecondaryText')).to.equal(true);
        });
      });
      describe('name is not unique', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.setProps({
            isNameUnique: false,
            isNameValid: true,
            isNamePrefixUnique: true,
            isNameEndWithHyphen: false,
          });
        });

        it('accordion should show duplication error', function (this: $TSFixMe) {
          expect(this.wrapper.find(Accordion).at(this.nameAccordionIndex).prop('errorSecondaryText')).to.equal(true);
        });
      });
      describe('name is not valid or unique', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.setProps({
            isNameValid: false,
            isNameUnique: false,
            isNamePrefixUnique: true,
            isNameEndWithHyphen: false,
          });
        });

        it('accordion should show duplication error', function (this: $TSFixMe) {
          expect(this.wrapper.find(Accordion).at(this.nameAccordionIndex).prop('errorSecondaryText')).to.equal(true);
        });
      });
    });
  });

  describe('when rendered with an error with replicationSpecList', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          formValues={clusterFixtures.getDefaultFormValues_WithReplicationSpecList()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_withReplicationSpecList_asJSON()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          error={{ message: 'foo', errorCode: 'foo' }}
          isEdit={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    it('shows Accordions for the form subsections', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).length).to.equal(5);
    });

    it('shows Accordions for Cluster Tier to have overflow inherited', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).at(2).prop('inheritOverflow')).to.be.true;
    });

    it('shows an ErrorToast', function (this: $TSFixMe) {
      expect(this.wrapper.find('ErrorToast').props().open).to.equal(true);
    });

    describe('Accordion Name Validation', function () {
      beforeEach(function (this: $TSFixMe) {
        this.nameAccordionIndex = this.wrapper.find(Accordion).length - 1;
      });
      describe('name is valid', function () {
        it('accordion should not show name error', function (this: $TSFixMe) {
          expect(this.wrapper.find(Accordion).at(this.nameAccordionIndex).prop('errorSecondaryText')).to.equal(false);
        });
      });
      describe('name is not a valid format', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.setProps({ isNameValid: false });
        });

        it('accordion should show name error', function (this: $TSFixMe) {
          expect(this.wrapper.find(Accordion).at(this.nameAccordionIndex).prop('errorSecondaryText')).to.equal(true);
        });
      });
      describe('name is not unique', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.setProps({
            isNameUnique: false,
            isNameValid: true,
            isNamePrefixUnique: true,
            isNameEndWithHyphen: false,
          });
        });

        it('accordion should show duplication error', function (this: $TSFixMe) {
          expect(this.wrapper.find(Accordion).at(this.nameAccordionIndex).prop('errorSecondaryText')).to.equal(true);
        });
      });
      describe('name is not valid or unique', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.setProps({
            isNameValid: false,
            isNameUnique: false,
            isNamePrefixUnique: true,
            isNameEndWithHyphen: false,
          });
        });

        it('accordion should show duplication error', function (this: $TSFixMe) {
          expect(this.wrapper.find(Accordion).at(this.nameAccordionIndex).prop('errorSecondaryText')).to.equal(true);
        });
      });
    });
  });

  describe('Accordion behaviors and styles', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.mount(<this.WrapperComponent />);
      // open three accordions consecutively
      this.wrapper.find('.accordion-headline').at(1).simulate('click');
      this.wrapper.find('.accordion-headline').at(2).simulate('click');
      this.wrapper.find('.accordion-headline').at(3).simulate('click');
    });

    afterEach(function (this: $TSFixMe) {
      this.wrapper.unmount();
    });

    it('broadcasts the applicable tracking events', function () {
      expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        context: 'Cluster Builder',
        action: 'Accordion Opened',
        value: 'Cloud Provider & Region',
        deployment_type: 'DEDICATED',
        pathfinder_filter: 'Cloud Provider & Region Accordion Opened',
      });
      expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        context: 'Cluster Builder',
        action: 'Accordion Opened',
        value: 'Cluster Tier',
        deployment_type: 'DEDICATED',
        pathfinder_filter: 'Cluster Tier Accordion Opened',
      });
      expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        context: 'Cluster Builder',
        action: 'Accordion Opened',
        value: 'Additional Settings',
        deployment_type: 'DEDICATED',
        pathfinder_filter: 'Additional Settings Accordion Opened',
      });
    });

    describe('default flex view', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper = enzyme.mount(<this.WrapperComponent />);
        // open three accordions consecutively
        this.wrapper.find('.accordion-headline').at(1).simulate('click');
        this.wrapper.find('.accordion-headline').at(2).simulate('click');
        this.wrapper.find('.accordion-headline').at(3).simulate('click');
      });

      afterEach(function (this: $TSFixMe) {
        this.wrapper.unmount();
      });

      it('broadcasts the applicable tracking events with Cluster Tier', function () {
        expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          context: 'Cluster Builder',
          action: 'Accordion Opened',
          value: 'Cloud Provider & Region',
          deployment_type: 'DEDICATED',
          pathfinder_filter: 'Cloud Provider & Region Accordion Opened',
        });
        expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          context: 'Cluster Builder',
          action: 'Accordion Opened',
          value: 'Cluster Tier',
          deployment_type: 'DEDICATED',
          pathfinder_filter: 'Cluster Tier Accordion Opened',
        });
        expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          context: 'Cluster Builder',
          action: 'Accordion Opened',
          value: 'Additional Settings',
          deployment_type: 'DEDICATED',
          pathfinder_filter: 'Additional Settings Accordion Opened',
        });
      });

      describe('Free tab is selected', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper = enzyme.mount(<this.WrapperComponent />);
          this.wrapper.setProps({
            deploymentType: DeploymentType.SHARED,
          });
          const formValues = clusterFixtures.getDefaultFreeFormValues();
          formValues.replicationSpecList =
            clusterFixtures.getFreeAwsDefaultClusterDescription_asJSON().replicationSpecList;
          this.wrapper.setProps({ formValues });
          // open three accordions consecutively
          this.wrapper.find('.accordion-headline').at(0).simulate('click');
          this.wrapper.find('.accordion-headline').at(1).simulate('click');
          this.wrapper.find('.accordion-headline').at(2).simulate('click');
        });

        afterEach(function (this: $TSFixMe) {
          this.wrapper.unmount();
        });

        it('broadcasts the applicable tracking events with Cluster Capacity', function () {
          expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
            context: 'Cluster Builder',
            action: 'Accordion Opened',
            value: 'Cloud Provider & Region',
            deployment_type: 'SHARED',
            pathfinder_filter: 'Cloud Provider & Region Accordion Opened',
          });
          expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
            context: 'Cluster Builder',
            action: 'Accordion Opened',
            value: 'Cluster Capacity',
            deployment_type: 'SHARED',
            pathfinder_filter: 'Cluster Capacity Accordion Opened',
          });
          expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
            context: 'Cluster Builder',
            action: 'Accordion Opened',
            value: 'Additional Settings',
            deployment_type: 'SHARED',
            pathfinder_filter: 'Additional Settings Accordion Opened',
          });
        });
      });

      describe('Flex tab is selected', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper = enzyme.mount(<this.WrapperComponent />);
          this.wrapper.setProps({
            deploymentType: DeploymentType.FLEX,
          });
          const formValues = clusterFixtures.getDefaultFlexFormValues();
          formValues.replicationSpecList =
            clusterFixtures.getFlexDefaultClusterDescriptionForAws_asJSON().replicationSpecList;
          this.wrapper.setProps({ formValues });
          // open three accordions consecutively
          this.wrapper.find('.accordion-headline').at(0).simulate('click');
          this.wrapper.find('.accordion-headline').at(1).simulate('click');
          this.wrapper.find('.accordion-headline').at(2).simulate('click');
          this.wrapper.find('.accordion-headline').at(3).simulate('click');
        });

        afterEach(function (this: $TSFixMe) {
          this.wrapper.unmount();
        });

        it('broadcasts the applicable tracking events with Cluster Capacity', function () {
          expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
            context: 'Cluster Builder',
            action: 'Accordion Opened',
            value: 'Cloud Provider & Region',
            deployment_type: 'FLEX',
            pathfinder_filter: 'Cloud Provider & Region Accordion Opened',
          });
          expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
            context: 'Cluster Builder',
            action: 'Accordion Opened',
            value: 'Cluster Capacity',
            deployment_type: 'FLEX',
            pathfinder_filter: 'Cluster Capacity Accordion Opened',
          });
          expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
            context: 'Cluster Builder - Cluster Capacity',
            action: 'Accordion Opened',
            value: 'Flex Pricing Details',
          });
          expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
            context: 'Cluster Builder',
            action: 'Accordion Opened',
            value: 'Additional Settings',
            deployment_type: 'FLEX',
            pathfinder_filter: 'Additional Settings Accordion Opened',
          });
        });
      });
    });

    it('once an accordion has been opened, secondary text is highlighted', function (this: $TSFixMe) {
      expect(this.wrapper.find('.accordion-headline-secondary-text-highlighted').length).to.equal(3);
    });

    it('BI connector advanced options are not initially visible', function (this: $TSFixMe) {
      expect(this.wrapper.find('NDSClusterFormBIConnectorAdvanced').length).to.equal(0);
    });

    describe('termination protection enabled', function () {
      const terminationProtectionEnabled = true;
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          formValues: {
            ...clusterFixtures.getDefaultFormValues(),
            terminationProtectionEnabled,
          },
        });
      });

      it('should show the termination protection section', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormTerminationProtection)).to.have.lengthOf(1);
      });

      it('should assign the right form value', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormTerminationProtection).props().terminationProtectionEnabled).to.equal(
          terminationProtectionEnabled
        );
      });
    });

    describe('BI Connector enabled in gov environment', function () {
      beforeEach(function (this: $TSFixMe) {
        const newFormValues = clusterFixtures.getDefaultFormValues();
        newFormValues.biConnector.enabled = true;
        this.wrapper.setProps({
          formValues: newFormValues,
          settingsModel: new Settings({
            GROUP_ID: '123',
            ORG_ID: '408',
            DOCS_URL: '',
            RECAPTCHA_PUBLIC: '',
            HAS_ORG_BILLING_ADMIN: true,
            GROUP_ENABLED_FEATURE_FLAGS: [],
            IS_NDS_GOV_ENABLED: true,
          }),
        });
      });

      it('BI connector advanced options become visible when BI connector is enabled', function (this: $TSFixMe) {
        expect(this.wrapper.find('NDSClusterFormBIConnectorAdvanced').length).to.equal(1);
      });

      it('Atlas SQL Interface banner is not visible', function (this: $TSFixMe) {
        const banners = this.wrapper.find(Banner);
        const atlasSQLInterfaceBanner = banners.filterWhere((node: ReactWrapper) =>
          node.text().includes('Atlas SQL Interface')
        );
        expect(atlasSQLInterfaceBanner.exists()).to.be.false;
      });
    });

    describe('BI Connector in non-gov environment', function () {
      const newFormValues = clusterFixtures.getDefaultFormValues();
      newFormValues.biConnector.enabled = false;

      it('is enabled when BIC Deprecation feature is disabled', function (this: $TSFixMe) {
        this.wrapper.setProps({
          formValues: newFormValues,
          settingsModel: new Settings({
            GROUP_ID: '123',
            ORG_ID: '408',
            DOCS_URL: '',
            RECAPTCHA_PUBLIC: '',
            HAS_ORG_BILLING_ADMIN: true,
            GROUP_ENABLED_FEATURE_FLAGS: [],
            IS_NDS_GOV_ENABLED: false,
          }),
        });
        expect(this.wrapper.find('NDSClusterFormBIConnector').length).to.equal(1);
      });

      it('is enabled when org is an Atlas BIC Org', function (this: $TSFixMe) {
        this.wrapper.setProps({
          formValues: newFormValues,
          settingsModel: new Settings({
            GROUP_ID: '123',
            ORG_ID: '408',
            DOCS_URL: '',
            RECAPTCHA_PUBLIC: '',
            HAS_ORG_BILLING_ADMIN: true,
            GROUP_ENABLED_FEATURE_FLAGS: ['BIC_DEPRECATION_SHOW_WARNING', 'BIC_DEPRECATION'],
            IS_NDS_GOV_ENABLED: false,
            IS_ATLAS_BIC_ORG: true,
          }),
        });
        expect(this.wrapper.find('NDSClusterFormBIConnector').length).to.equal(1);
      });

      it('is disabled when org is not an Atlas BIC Org', function (this: $TSFixMe) {
        this.wrapper.setProps({
          formValues: newFormValues,
          settingsModel: new Settings({
            GROUP_ID: '123',
            ORG_ID: '408',
            DOCS_URL: '',
            RECAPTCHA_PUBLIC: '',
            HAS_ORG_BILLING_ADMIN: true,
            GROUP_ENABLED_FEATURE_FLAGS: ['BIC_DEPRECATION_SHOW_WARNING', 'BIC_DEPRECATION'],
            IS_NDS_GOV_ENABLED: false,
            IS_ATLAS_BIC_ORG: false,
          }),
        });
        expect(this.wrapper.find('NDSClusterFormBIConnector').length).to.equal(0);
      });
    });

    describe('BI Connector deprecation warning', function () {
      const newFormValues = clusterFixtures.getDefaultFormValues();
      newFormValues.biConnector.enabled = true;

      it('is not shown in gov environment', function (this: $TSFixMe) {
        this.wrapper.setProps({
          formValues: newFormValues,
          settingsModel: new Settings({
            GROUP_ID: '123',
            ORG_ID: '408',
            DOCS_URL: '',
            RECAPTCHA_PUBLIC: '',
            HAS_ORG_BILLING_ADMIN: true,
            GROUP_ENABLED_FEATURE_FLAGS: ['BIC_DEPRECATION_SHOW_WARNING'],
            IS_NDS_GOV_ENABLED: true,
          }),
        });
        const banners = this.wrapper.find(Banner);
        const bicDeprecationBanner = banners.filterWhere((node: ReactWrapper) =>
          node.text().includes('The BI Connector will be deprecated')
        );
        const atlasSQLInterfaceBanner = banners.filterWhere((node: ReactWrapper) =>
          node.text().includes('Try the New Atlas SQL Interface Instead')
        );
        expect(bicDeprecationBanner.exists()).to.be.false;
        expect(atlasSQLInterfaceBanner.exists()).to.be.false;
      });

      it('is not shown in when BIC deprecation warnings are disabled', function (this: $TSFixMe) {
        this.wrapper.setProps({
          formValues: newFormValues,
          settingsModel: new Settings({
            GROUP_ID: '123',
            ORG_ID: '408',
            DOCS_URL: '',
            RECAPTCHA_PUBLIC: '',
            HAS_ORG_BILLING_ADMIN: true,
            GROUP_ENABLED_FEATURE_FLAGS: [],
            IS_NDS_GOV_ENABLED: false,
          }),
        });
        expect(this.wrapper.find(Banner).at(0).text()).to.not.include('The BI Connector will be deprecated');
      });

      it('is shown in when BIC deprecation warnings are enabled', function (this: $TSFixMe) {
        this.wrapper.setProps({
          formValues: newFormValues,
          settingsModel: new Settings({
            GROUP_ID: '123',
            ORG_ID: '408',
            DOCS_URL: '',
            RECAPTCHA_PUBLIC: '',
            HAS_ORG_BILLING_ADMIN: true,
            GROUP_ENABLED_FEATURE_FLAGS: ['BIC_DEPRECATION_SHOW_WARNING'],
            IS_NDS_GOV_ENABLED: false,
          }),
        });
        const banners = this.wrapper.find(Banner);
        const bicDeprecationBanner = banners.filterWhere((node: ReactWrapper) =>
          node.text().includes('The BI Connector will be deprecated')
        );
        const atlasSQLInterfaceBanner = banners.filterWhere((node: ReactWrapper) =>
          node.text().includes('Try the New Atlas SQL Interface Instead')
        );
        expect(bicDeprecationBanner.exists()).to.be.true;
        expect(atlasSQLInterfaceBanner.exists()).to.be.false;
      });
    });

    describe('When an accordion is closed', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find('.accordion-headline').at(3).simulate('click');
      });

      it('broadcasts a tracking event', function () {
        expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          context: 'Cluster Builder',
          action: 'Accordion Closed',
          value: 'Additional Settings',
          deployment_type: 'DEDICATED',
          pathfinder_filter: 'Additional Settings Accordion Closed',
        });
      });
    });
  });

  describe('Instance size accordion behavior', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.mount(<this.WrapperComponent />);
      this.wrapper.find('.accordion-headline').at(2).simulate('click');
    });

    afterEach(function (this: $TSFixMe) {
      this.wrapper.unmount();
    });

    it('broadcasts a tracking event', function () {
      expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        context: 'Cluster Builder',
        action: 'Accordion Opened',
        value: 'Cluster Tier',
        deployment_type: 'DEDICATED',
        pathfinder_filter: 'Cluster Tier Accordion Opened',
      });
    });

    it('shows the storage options section', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormStorageSection).length).to.equal(1);
    });

    it('shows the process args section', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormAdvancedOptions).length).to.equal(1);
    });

    describe('For AWS NVMe clusters', function () {
      beforeEach(function (this: $TSFixMe) {
        const formValues = clusterFixtures.getDefaultFormValues();
        formValues.replicationSpecList[0].regionConfigs[0].electableSpecs.instanceSize = 'M40_NVME';
        formValues.replicationSpecList[0].regionConfigs[0].analyticsSpecs.instanceSize = 'M40_NVME';
        formValues.replicationSpecList[0].regionConfigs[0].cloudProvider = CloudProvider.AWS;
        this.wrapper.setProps({
          formValues,
        });
      });

      it('indicates disk storage and IOPS correctly in the accordion text', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(2).text()).to.include('10 GB Storage');
        expect(this.wrapper.find('.accordion-headline-secondary-subtext').at(2).text()).to.include('135,125 IOPS');
      });

      it('describes the instance class as NVMe in the accordion subtext', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-subtext').at(2).text()).to.include('Local NVMe SSD');
      });
    });

    describe('For tenant clusters', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper = enzyme.mount(<this.WrapperComponent />);
        this.tenantFormValues = clusterFixtures.getDefaultFormValues();
        this.tenantFormValues.replicationSpecList[0].regionConfigs[0].cloudProvider = 'FREE';
        this.tenantFormValues.replicationSpecList[0].regionConfigs[0].electableSpecs.instanceSize = 'M2';
        this.tenantFormValues.replicationSpecList[0].regionConfigs[0].analyticsSpecs.instanceSize = 'M2';
        this.tenantFormValues.replicationSpecList[0].regionConfigs[0].electableSpecs.backingProvider = 'AWS';
        this.wrapper.setProps({
          formValues: this.tenantFormValues,
        });
      });

      afterEach(function (this: $TSFixMe) {
        this.wrapper.unmount();
      });

      it('shows the cloud provider and instance size section as greyed out', function (this: $TSFixMe) {
        expect(this.wrapper.find(Accordion).at(1).prop('disabled')).to.equal(true);
        expect(this.wrapper.find(Accordion).at(2).prop('disabled')).to.equal(true);
      });

      it('shows the additional settings section', function (this: $TSFixMe) {
        expect(this.wrapper.find(Accordion).at(3).prop('disabled')).to.equal(false);
      });

      it('does not show the storage options section', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormStorageSection).length).to.equal(0);
      });

      it('does not show the process options section', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormAdvancedOptions).length).to.equal(0);
      });
      describe('Enabling Shared Serverless Option, function', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper = enzyme.mount(<this.WrapperComponent />);
          this.wrapper.setProps({
            settingsModel: new Settings({
              SERVERLESS_SHARED_UI_OPTION_ENABLED: true,
            }),
          });
        });
        it('shows the cloud provider and instance size sections when Shared Serverless UI is enabled', function (this: $TSFixMe) {
          expect(this.wrapper.find(Accordion).at(1).prop('disabled')).to.equal(false);
          expect(this.wrapper.find(Accordion).at(2).prop('disabled')).to.equal(false);
        });
      });
    });

    describe('For asymmetric hardware clusters', function () {
      beforeEach(function (this: $TSFixMe) {
        this.asymmetricFormValues = clusterFixtures.getDefaultFormValues_WithReplicationSpecListAndAnalyticsNodes();
        this.asymmetricFormValues.replicationSpecList[0].regionConfigs[0].analyticsSpecs.instanceSize = 'M40';
        this.wrapper.setProps({
          formValues: this.asymmetricFormValues,
          analyticsInstanceClass: INSTANCE_CLASS.LOW_CPU,
        });
        this.wrapper.find('.accordion-headline').at(2).simulate('click');
      });

      it('if analytics and base instance sizes are different move diskGB to secondary subtext', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(2).text()).to.not.include('10 GB Storage');
        expect(this.wrapper.find('.accordion-headline-secondary-subtext').at(2).text()).to.include('10 GB Storage');
      });

      it('if analytics and base instance sizes are different cluster tier summary shows both instance sizes', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(2).text()).to.include('M10 Base');
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(2).text()).to.include('M40 Analytics');
      });

      it('if analytics and base instance sizes are different cluster tier summary shows RAM for instance sizes', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(2).text()).to.include('2 GB RAM');
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(2).text()).to.include('16 GB RAM');
      });

      it('if analytics and base instance classes are different show warning banner', function (this: $TSFixMe) {
        expect(
          this.wrapper
            .find(Banner)
            .findWhere((node: $TSFixMe) => node.props().variant === 'warning' && node.name() === 'Banner')
            .at(0)
            .text()
        ).include('Disk auto-scaling is not yet available for clusters with mixed instance classes.');
      });
    });

    describe('When sharding is enabled', function () {
      beforeEach(function (this: $TSFixMe) {
        const formValues = clusterFixtures.getDefaultFormValues();
        const replicationSpecs = formValues.replicationSpecList;
        replicationSpecs[0].numShards = 2;
        formValues.clusterType = ClusterType.SHARDED;
        this.wrapper.setProps({
          formValues,
        });
      });

      it('indicates disk storage and IOPS per shard', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(2).text()).to.include(
          '10 GB Storage per Shard'
        );
        expect(this.wrapper.find('.accordion-headline-secondary-subtext').at(2).text()).to.include(
          '1,000 IOPS per Shard'
        );
      });
    });
  });

  describe('advance configuration accordion behavior', function () {
    beforeEach(function (this: $TSFixMe) {
      const formValues = clusterFixtures.getDefaultFormValues();
      formValues.replicationSpecList[0].regionConfigs[0].electableSpecs.instanceSize = 'R40';
      formValues.replicationSpecList[0].regionConfigs[0].analyticsSpecs.instanceSize = 'R40';
      formValues.replicationSpecList[0].regionConfigs[0].cloudProvider = CloudProvider.AWS;
      this.wrapper = enzyme.mount(<this.WrapperComponent formValues={formValues} />);
    });

    afterEach(function (this: $TSFixMe) {
      this.wrapper.unmount();
    });

    describe('when rendered with a low CPU instance size', function () {
      it('renders the BI Connector view with the high CPU instance size name', function (this: $TSFixMe) {
        const component = this.wrapper.find(NDSClusterFormBIConnector);
        expect(component.props().instanceSize).to.equal('M40');
      });

      it('describes the instance class as Low CPU in the accordion subtext', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-subtext').at(2).text()).to.include('Low-CPU');
      });
    });

    describe('when editing a pre-5.0 replicaset synced with a private endpoint', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          isEdit: true,
          originalCluster: {
            ...clusterFixtures.getAwsDefaultClusterDescription_asJSON(),
            privateLinkSrvAddresses: { 'vpce-1234': 'abc-mmscloudtest.com' },
          },
        });
        expect(isAtLeast5_0(this.wrapper.prop('originalCluster').mongoDBMajorVersion)).to.equal(false);
        expect(this.wrapper.prop('originalCluster').clusterType).to.equal(ClusterType.REPLICASET);
      });

      it('renders children without setting the optimized private endpoint connection string warning', function (this: $TSFixMe) {
        expect(
          this.wrapper.find(NDSClusterFormVersion).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
        ).to.equal(false);
        expect(
          this.wrapper.find(NDSClusterFormSharding).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
        ).to.equal(false);
      });

      describe('and the cluster is sharded and upgraded to 5.0 via the form', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.setProps({
            formValues: {
              ...this.wrapper.prop('formValues'),
              mongoDBMajorVersion: '5.0',
              clusterType: ClusterType.SHARDED,
            },
          });

          expect(isAtLeast5_0(this.wrapper.prop('formValues').mongoDBMajorVersion)).to.equal(true);
          expect(this.wrapper.prop('formValues').clusterType).to.equal(ClusterType.SHARDED);
        });

        it('renders form version and sharding sections with the private endpoint connection string warning', function (this: $TSFixMe) {
          expect(
            this.wrapper.find(NDSClusterFormVersion).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
          ).to.equal(true);
          expect(
            this.wrapper.find(NDSClusterFormSharding).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
          ).to.equal(true);
        });
      });
    });

    describe('when editing a pre-5.0 sharded cluster synced with a private endpoint', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          isEdit: true,
          originalCluster: {
            ...clusterFixtures.getAwsDefaultClusterDescription_asJSON(),
            privateLinkSrvAddresses: { 'vpce-1234': 'abc-mmscloudtest.com' },
            clusterType: ClusterType.SHARDED,
          },
          formValues: {
            ...clusterFixtures.getDefaultFormValues(),
            clusterType: ClusterType.SHARDED, // clusterType on form will be equal to original cluster.
          },
        });

        expect(isAtLeast5_0(this.wrapper.prop('originalCluster').mongoDBMajorVersion)).to.equal(false);
        expect(this.wrapper.prop('originalCluster').clusterType).to.equal(ClusterType.SHARDED);
      });

      it('renders children without setting the optimized private endpoint connection string warning', function (this: $TSFixMe) {
        expect(
          this.wrapper.find(NDSClusterFormVersion).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
        ).to.equal(false);
        expect(
          this.wrapper.find(NDSClusterFormSharding).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
        ).to.equal(false);
      });

      describe('and the MongoDB version is upgraded to 5.0 via the form', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.setProps({
            formValues: {
              ...this.wrapper.prop('formValues'),
              mongoDBMajorVersion: '5.0',
            },
          });

          expect(isAtLeast5_0(this.wrapper.prop('formValues').mongoDBMajorVersion)).to.equal(true);
        });

        it('renders form version section with the optimized private endpoint connection string warning', function (this: $TSFixMe) {
          expect(
            this.wrapper.find(NDSClusterFormVersion).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
          ).to.equal(true);
          expect(
            this.wrapper.find(NDSClusterFormSharding).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
          ).to.equal(false);
        });
      });
    });

    describe('when editing a 5.0 replicaset synced with a private endpoint', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          isEdit: true,
          originalCluster: {
            ...clusterFixtures.getAwsDefaultClusterDescription_asJSON(),
            privateLinkSrvAddresses: { 'vpce-1234': 'abc-mmscloudtest.com' },
            mongoDBMajorVersion: '5.0',
          },
          formValues: {
            ...clusterFixtures.getDefaultFormValues(),
            mongoDBMajorVersion: '5.0', // majorVersion on form will be equal to original cluster.
          },
        });

        expect(isAtLeast5_0(this.wrapper.prop('originalCluster').mongoDBMajorVersion)).to.equal(true);
        expect(this.wrapper.prop('originalCluster').clusterType).to.equal(ClusterType.REPLICASET);
      });

      it('renders children without setting the optimized private endpoint connection string warning', function (this: $TSFixMe) {
        expect(
          this.wrapper.find(NDSClusterFormVersion).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
        ).to.equal(false);
        expect(
          this.wrapper.find(NDSClusterFormSharding).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
        ).to.equal(false);
      });

      describe('and the cluster is sharded via the form', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.setProps({
            formValues: {
              ...this.wrapper.prop('formValues'),
              clusterType: ClusterType.SHARDED,
            },
          });

          expect(this.wrapper.prop('formValues').clusterType).to.equal(ClusterType.SHARDED);
        });

        it('renders form sharding section with the optimized private endpoint connection string warning', function (this: $TSFixMe) {
          expect(
            this.wrapper.find(NDSClusterFormVersion).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
          ).to.equal(false);
          expect(
            this.wrapper.find(NDSClusterFormSharding).props().shouldShowOptimizedPrivateEndpointConnectionStringWarning
          ).to.equal(true);
        });
      });
    });
  });

  describe('MongoDB version section behavior', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.mount(<this.WrapperComponent />);
    });

    afterEach(function (this: $TSFixMe) {
      this.wrapper.unmount();
    });

    describe('when the original cluster MongoDB version is 5.0 and selected version is 5.0', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          originalCluster: {
            ...clusterFixtures.getAwsDefaultClusterDescription_asJSON(),
            versionReleaseSystem: VersionReleaseSystem.LTS,
            mongoDBMajorVersion: '5.0',
          },
          formValues: {
            ...clusterFixtures.getDefaultFormValues(),
            versionReleaseSystem: VersionReleaseSystem.LTS,
            mongoDBMajorVersion: '5.0',
          },
        });
      });

      it('shows selected MongoDB version in the accordion text', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include('MongoDB 5.0');
      });
    });

    describe('when the original cluster MongoDB version is 5.0 and selected version is 6.0', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          originalCluster: {
            ...clusterFixtures.getAwsDefaultClusterDescription_asJSON(),
            versionReleaseSystem: VersionReleaseSystem.LTS,
            mongoDBMajorVersion: '5.0',
          },
          formValues: {
            ...clusterFixtures.getDefaultFormValues(),
            versionReleaseSystem: VersionReleaseSystem.LTS,
            mongoDBMajorVersion: '6.0',
          },
        });
      });

      it('shows selected MongoDB version in the accordion text', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include('MongoDB 6.0');
      });
    });

    describe('when the original cluster MongoDB version is 8.0 and is pinned to fcv 7.0', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          originalCluster: {
            ...clusterFixtures.getAwsDefaultClusterDescription_asJSON(),
            versionReleaseSystem: VersionReleaseSystem.LTS,
            mongoDBMajorVersion: '8.0',
            mongoDBVersion: '8.0.12',
            fixedMongoDBFCV: '7.0',
          },
          formValues: {
            ...clusterFixtures.getDefaultFormValues(),
            mongoDBMajorVersion: '8.0',
          },
        });
      });

      describe('and the customer pinned FCV feature flag is disabled', () => {
        it('shows selected MongoDB fcv pinned version in the accordion text', function (this: $TSFixMe) {
          expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include('MongoDB 7.0');
        });
      });

      describe('and the customer pinned FCV feature flag is enabled', () => {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper.setProps({
            settingsModel: new Settings({
              GROUP_ENABLED_FEATURE_FLAGS: ['CUSTOMER_PINNED_FCV_AND_MDB_DOWNGRADE'],
            }),
          });
        });

        it('shows selected MongoDB version in the accordion text', function (this: $TSFixMe) {
          expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include('MongoDB 8.0');
        });
      });
    });

    describe('when the original cluster MongoDB version is 6.0', function () {
      describe('and Rapid Release MongoDB Version is 6.1.0', function () {
        describe('and Rapid Release FCV is 6.0', function () {
          describe;
          beforeEach(function (this: $TSFixMe) {
            this.wrapper.setProps({
              crossCloudProviderOptions: {
                ...clusterFixtures.getCrossCloudProviderOptions(),
                defaultCDMongoDBVersion: '6.1.0',
                defaultCDMongoDBFCV: '6.0',
              },
              originalCluster: {
                ...clusterFixtures.getAwsDefaultClusterDescription_asJSON(),
                versionReleaseSystem: VersionReleaseSystem.LTS,
                mongoDBMajorVersion: '6.0',
              },
              formValues: {
                ...clusterFixtures.getDefaultFormValues(),
                versionReleaseSystem: VersionReleaseSystem.CONTINUOUS,
                mongoDBMajorVersion: '6.1.0',
              },
            });
          });

          it('shows Rapid Release FCV in the accordion text', function (this: $TSFixMe) {
            expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include('MongoDB 6.0');
          });
        });
      });
    });

    describe('when the original cluster MongoDB version is 6.0', function () {
      describe('and Rapid Release MongoDB Version is 6.1.0', function () {
        describe('and Rapid Release FCV is 6.1', function () {
          describe;
          beforeEach(function (this: $TSFixMe) {
            this.wrapper.setProps({
              crossCloudProviderOptions: {
                ...clusterFixtures.getCrossCloudProviderOptions(),
                defaultCDMongoDBVersion: '6.1.0',
                defaultCDMongoDBFCV: '6.1',
              },
              originalCluster: {
                ...clusterFixtures.getAwsDefaultClusterDescription_asJSON(),
                versionReleaseSystem: VersionReleaseSystem.LTS,
                mongoDBMajorVersion: '6.0',
              },
              formValues: {
                ...clusterFixtures.getDefaultFormValues(),
                versionReleaseSystem: VersionReleaseSystem.CONTINUOUS,
                mongoDBMajorVersion: '6.1.0',
              },
            });
          });

          it('shows Rapid Release FCV in the accordion text', function (this: $TSFixMe) {
            expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include('MongoDB 6.1');
          });
        });
      });
    });

    describe('when the original cluster MongoDB version is 6.0', function () {
      describe('and Rapid Release MongoDB Version is 6.2.0', function () {
        describe('and Rapid Release FCV is 6.1', function () {
          describe;
          beforeEach(function (this: $TSFixMe) {
            this.wrapper.setProps({
              crossCloudProviderOptions: {
                ...clusterFixtures.getCrossCloudProviderOptions(),
                defaultCDMongoDBVersion: '6.2.0',
                defaultCDMongoDBFCV: '6.1',
              },
              originalCluster: {
                ...clusterFixtures.getAwsDefaultClusterDescription_asJSON(),
                versionReleaseSystem: VersionReleaseSystem.LTS,
                mongoDBMajorVersion: '6.0',
              },
              formValues: {
                ...clusterFixtures.getDefaultFormValues(),
                versionReleaseSystem: VersionReleaseSystem.CONTINUOUS,
                mongoDBMajorVersion: '6.2.0',
              },
            });
          });

          it('shows Rapid Release FCV in the accordion text', function (this: $TSFixMe) {
            expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include('MongoDB 6.1');
          });
        });
      });
    });

    describe('when the original cluster is on Rapid Release', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          originalCluster: {
            ...clusterFixtures.getAwsDefaultClusterDescription_asJSON(),
            versionReleaseSystem: VersionReleaseSystem.CONTINUOUS,
          },
        });
      });

      it('shows Latest Release in the accordion text', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include('Latest Release');
      });
    });
  });

  describe('Backup section behavior', function () {
    describe('when the original cluster has backup disabled, or when creating new cluster', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper = enzyme.mount(
          <this.WrapperComponent formValues={{ ...clusterFixtures.getDefaultFormValues(), instanceSize: 'R40' }} />
        );
      });

      afterEach(function (this: $TSFixMe) {
        this.wrapper.unmount();
      });

      it('show the correct backup info in the accordion text', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include(', No Backup');
        expect(this.wrapper.find('.accordion-headline-secondary-subtext').at(3).text()).to.be.empty;
      });
    });

    describe('when the original cluster has legacy backup enabled', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper = enzyme.mount(
          <this.WrapperComponent
            formValues={{
              ...clusterFixtures.getDefaultFormValues(),
              instanceSize: 'R40',
              backupEnabled: true,
            }}
          />
        );
      });

      afterEach(function (this: $TSFixMe) {
        this.wrapper.unmount();
      });

      it('show the correct backup info in the accordion text', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include(', Backup');
        expect(this.wrapper.find('.accordion-headline-secondary-subtext').at(3).text()).to.equal('Legacy Backup');
      });
    });

    describe('when the original cluster has Cloud backup enabled', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper = enzyme.mount(
          <this.WrapperComponent
            formValues={{
              ...clusterFixtures.getDefaultFormValues(),
              instanceSize: 'R40',
              diskBackupEnabled: true,
            }}
          />
        );
      });

      afterEach(function (this: $TSFixMe) {
        this.wrapper.unmount();
      });

      it('show the correct backup info in the accordion text', function (this: $TSFixMe) {
        expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include(', Backup');
        expect(this.wrapper.find('.accordion-headline-secondary-subtext').at(3).text()).to.equal('Cloud Backup');
      });
    });
  });

  describe('Name accordion behavior', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.mount(<this.WrapperComponent />);
    });

    afterEach(function (this: $TSFixMe) {
      this.wrapper.unmount();
    });

    describe('For cluster creation', function () {
      beforeEach(function (this: $TSFixMe) {
        // 4 represents the index of the name accordion in the current iteration of the cluster editor UI
        this.wrapper.find('.accordion-headline').at(4).simulate('click');
      });

      it('broadcasts a tracking event', function () {
        expect(analytics.track).to.have.been.calledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          context: 'Cluster Builder',
          action: 'Accordion Opened',
          value: 'Cluster Details',
          deployment_type: 'DEDICATED',
          pathfinder_filter: 'Cluster Details Accordion Opened',
        });
      });

      it('shows the name accordion', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormName).length).to.equal(1);
        expect(this.wrapper.find(Accordion).at(4).prop('active')).to.equal(true);
      });

      it('shows the appropriate number of accordions', function (this: $TSFixMe) {
        expect(this.wrapper.find(Accordion).length).to.equal(5);
      });
    });

    describe('For cluster editing', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          isEdit: true,
        });
      });

      it('does not show the name accordion', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormName).length).to.equal(0);
      });

      it('shows the appropriate number of accordions', function (this: $TSFixMe) {
        expect(this.wrapper.find(Accordion).length).to.equal(4);
      });
    });
  });

  describe('#clusterBuilderFilterInterface visibility control behavior', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues()}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={{
            ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
            providerTemplateKey: TemplateKey.REPLICA_SET,
          }}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={true}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    describe('all advanced options not visible', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          clusterBuilderFilterInterface: {
            ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
            advancedOptions: {
              shardingVisible: false,
              otherOptionsVisible: false,
            },
          },
        });
      });

      it('does not show any advanced options', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormSharding)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormBIConnector)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormSQLSchemaInterface)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormEncryptionAtRestProvider)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormAdvancedOptions)).to.not.exist;
      });
    });

    describe('sharding option visible', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          clusterBuilderFilterInterface: {
            ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
            advancedOptions: {
              shardingVisible: true,
              otherOptionsVisible: false,
            },
          },
        });
      });

      it('shows the sharding option only', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormSharding)).to.exist;
        expect(this.wrapper.find(NDSClusterFormBIConnector)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormSQLSchemaInterface)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormEncryptionAtRestProvider)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormAdvancedOptions)).to.not.exist;
      });
    });

    describe('all options exception sharding visible', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          clusterBuilderFilterInterface: {
            ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
            advancedOptions: {
              shardingVisible: false,
              otherOptionsVisible: true,
            },
          },
        });
      });

      it('shows the other advanced options only', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormSharding)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormBIConnector)).to.exist;
        expect(this.wrapper.find(NDSClusterFormSQLSchemaInterface)).to.exist;
        expect(this.wrapper.find(NDSClusterFormEncryptionAtRestProvider)).to.exist;
        expect(this.wrapper.find(NDSClusterFormAdvancedOptions)).to.exist;
      });
    });

    describe('If the global configuration option is not supposed to be displayed', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          clusterBuilderFilterInterface: {
            ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
            isGlobalConfigurationVisible: false,
          },
        });
      });

      it('does not show the geozone configuration section', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormGeoOverView)).to.not.exist;
      });
    });

    describe('If the global configuration accordion is supposed to be open by default', function () {
      beforeEach(function (this: $TSFixMe) {
        // the openAccordion state is determined on mount, so the wrapper needs to be reset instead of using setProps
        this.wrapper = enzyme.shallow(
          // @ts-expect-error TS(2769): No overload matches this call.
          <NDSClusterForm
            isInstanceSizeVisible={() => true}
            setClusterFormValue={() => {}}
            formValues={clusterFixtures.getDefaultFormValues()}
            providerOptions={clusterFixtures.getProviderOptions()}
            crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
            originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
            defaultTemplates={clusterFixtures.getDefaultTemplates()}
            processArgs={clusterFixtures.getDefaultProcessArgs()}
            originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
            clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
            isEdit={false}
            encryptionAtRest={{
              awsKms: { enabled: false },
              azureKeyVault: { enabled: false },
              googleCloudKms: { enabled: false },
            }}
            isPrivateIPModeEnabled={false}
            useCNRegionsOnly={false}
            m0LimitReached={false}
            isTenantUpgrade={false}
            isLDAPEnabled={false}
            settingsModel={this.settingsModel}
            billingEstimate={{
              hourlyEstimateDollars: '0.00',
              monthlyEstimateDollars: '0.00',
              snapshotBackupEstimateDollars: '0.00',
            }}
            biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
            instanceClass={INSTANCE_CLASS.HIGH_CPU}
            analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
            cloudContainers={[]}
            customRoles={[]}
            clusterBuilderFilterInterface={{
              ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
              providerTemplateKey: TemplateKey.GEO_SHARDED_3_ZONE,
            }}
            isNameValid={this.isNameValid}
            isNameUnique={this.isNameUnique}
            isNamePrefixUnique={this.isNamePrefixUnique}
            isNameEndWithHyphen={this.isNameEndWithHyphen}
            isNameWithinCharLimit={this.isNameWithinCharLimit}
            error={null}
            isAutoIndexingEligible
            deploymentType={DeploymentType.DEDICATED}
            setDeploymentType={() => {}}
            openSection={null}
            groupLimits={DEFAULT_LIMITS}
            hasUnsetOplogMinRetentionHours={false}
            updateCrossCloudProviderOptions={() => {}}
            regionProviderFeatures={{}}
            isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
            isAtlasSqlClusterSchemaInterfaceEnabled={false}
            searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
          />
        );
      });

      it('the geozone configuration section is visible and open', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormGeoOverView)).to.exist;
        expect(this.wrapper.state().openAccordions.globalConfiguration).to.equal(true);
      });
    });
  });

  describe('#clusterBuilderFilterInterface visibility control behavior with replicationSpecList', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues_WithReplicationSpecList()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_withReplicationSpecList_asJSON()}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={{
            ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
            providerTemplateKey: TemplateKey.REPLICA_SET,
          }}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={true}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    describe('all advanced options not visible', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          clusterBuilderFilterInterface: {
            ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
            advancedOptions: {
              shardingVisible: false,
              otherOptionsVisible: false,
            },
          },
        });
      });

      it('does not show any advanced options', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormSharding)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormBIConnector)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormSQLSchemaInterface)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormEncryptionAtRestProvider)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormAdvancedOptions)).to.not.exist;
      });
    });

    describe('sharding option visible', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          clusterBuilderFilterInterface: {
            ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
            advancedOptions: {
              shardingVisible: true,
              otherOptionsVisible: false,
            },
          },
        });
      });

      it('shows the sharding option only', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormSharding)).to.exist;
        expect(this.wrapper.find(NDSClusterFormBIConnector)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormSQLSchemaInterface)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormEncryptionAtRestProvider)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormAdvancedOptions)).to.not.exist;
      });
    });

    describe('all options exception sharding visible', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          clusterBuilderFilterInterface: {
            ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
            advancedOptions: {
              shardingVisible: false,
              otherOptionsVisible: true,
            },
          },
        });
      });

      it('shows the other advanced options only', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormSharding)).to.not.exist;
        expect(this.wrapper.find(NDSClusterFormBIConnector)).to.exist;
        expect(this.wrapper.find(NDSClusterFormSQLSchemaInterface)).to.exist;
        expect(this.wrapper.find(NDSClusterFormEncryptionAtRestProvider)).to.exist;
        expect(this.wrapper.find(NDSClusterFormAdvancedOptions)).to.exist;
      });
    });

    describe('If the global configuration option is not supposed to be displayed', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          clusterBuilderFilterInterface: {
            ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
            isGlobalConfigurationVisible: false,
          },
        });
      });

      it('does not show the geozone configuration section', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormGeoOverView)).to.not.exist;
      });
    });

    describe('If the global configuration accordion is supposed to be open by default with replicationSpecList', function () {
      beforeEach(function (this: $TSFixMe) {
        // the openAccordion state is determined on mount, so the wrapper needs to be reset instead of using setProps
        this.wrapper = enzyme.shallow(
          // @ts-expect-error TS(2769): No overload matches this call.
          <NDSClusterForm
            isInstanceSizeVisible={() => true}
            setClusterFormValue={() => {}}
            formValues={clusterFixtures.getDefaultFormValues_WithReplicationSpecList()}
            originalCluster={clusterFixtures.getAwsDefaultClusterDescription_withReplicationSpecList_asJSON()}
            providerOptions={clusterFixtures.getProviderOptions()}
            crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
            defaultTemplates={clusterFixtures.getDefaultTemplates()}
            processArgs={clusterFixtures.getDefaultProcessArgs()}
            originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
            clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
            isEdit={false}
            encryptionAtRest={{
              awsKms: { enabled: false },
              azureKeyVault: { enabled: false },
              googleCloudKms: { enabled: false },
            }}
            isPrivateIPModeEnabled={false}
            useCNRegionsOnly={false}
            m0LimitReached={false}
            isTenantUpgrade={false}
            isLDAPEnabled={false}
            settingsModel={this.settingsModel}
            billingEstimate={{
              hourlyEstimateDollars: '0.00',
              monthlyEstimateDollars: '0.00',
              snapshotBackupEstimateDollars: '0.00',
            }}
            biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
            instanceClass={INSTANCE_CLASS.HIGH_CPU}
            analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
            cloudContainers={[]}
            customRoles={[]}
            clusterBuilderFilterInterface={{
              ...clusterFixtures.getDefaultClusterBuilderFilterInterface(),
              providerTemplateKey: TemplateKey.GEO_SHARDED_3_ZONE,
            }}
            isNameValid={this.isNameValid}
            isNameUnique={this.isNameUnique}
            isNamePrefixUnique={this.isNamePrefixUnique}
            isNameEndWithHyphen={this.isNameEndWithHyphen}
            isNameWithinCharLimit={this.isNameWithinCharLimit}
            error={null}
            isAutoIndexingEligible
            deploymentType={DeploymentType.DEDICATED}
            setDeploymentType={() => {}}
            openSection={null}
            groupLimits={DEFAULT_LIMITS}
            hasUnsetOplogMinRetentionHours={false}
            updateCrossCloudProviderOptions={() => {}}
            regionProviderFeatures={{}}
            isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
            isAtlasSqlClusterSchemaInterfaceEnabled={false}
            searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
          />
        );
      });

      it('the geozone configuration section is visible and open', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormGeoOverView)).to.exist;
        expect(this.wrapper.state().openAccordions.globalConfiguration).to.equal(true);
      });
    });
  });

  describe('If the user has geo-zoned sharding enabled', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues()}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_withReplicationSpecList_asJSON()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    it('shows an additional accordion for the Map View', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).length).to.equal(5);
    });

    it('does not yet show the UI for zone configuration', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormGeoZones).length).to.equal(0);
    });

    it('does not yet show a divider between sets of accordions', function (this: $TSFixMe) {
      expect(this.wrapper.find('.nds-cluster-form-divider').length).to.equal(0);
    });

    it('still shows the sharding configuration UI', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormSharding).length).to.equal(1);
    });

    describe('and the cluster is geo-sharded', function () {
      beforeEach(function (this: $TSFixMe) {
        const formValues = clusterFixtures.getDefaultFormValues();
        formValues.clusterType = ClusterType.GEOSHARDED;

        this.wrapper.setProps({
          formValues,
        });
      });

      it('shows the UI for zone configuration', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormGeoZones).length).to.equal(1);
      });

      it('shows a divider between sets of accordions', function (this: $TSFixMe) {
        expect(this.wrapper.find('.nds-cluster-form-divider').length).to.equal(1);
      });

      it('no longer shows the sharding configuration UI', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormSharding).length).to.equal(0);
      });

      it('defaults to the first replication spec as the default selected spec', function (this: $TSFixMe) {
        expect(this.wrapper.instance().state.newFormSelectedReplicationSpecId).to.equal('000000000000000000000001');
      });

      describe('when the size of the list of replication specs increases to cover the selected index', function () {
        beforeEach(function (this: $TSFixMe) {
          const formValues = clusterFixtures.getDefaultFormValues();
          formValues.replicationSpecList =
            clusterFixtures.getAwsDefaultClusterDescription_geo6Zone_asJSON().replicationSpecList;
          this.wrapper.setProps({ formValues });
        });

        it('keeps the selected replication spec as the first in the list', function (this: $TSFixMe) {
          expect(this.wrapper.instance().state.newFormSelectedReplicationSpecId).to.equal('000000000000000000000004');
        });
      });

      describe('when the size of the list of replication specs decreases to not cover the selected index', function () {
        beforeEach(function (this: $TSFixMe) {
          const formValues = clusterFixtures.getDefaultFormValues();
          formValues.replicationSpecList = clusterFixtures.getAwsDefaultClusterDescription_asJSON().replicationSpecList;
          this.wrapper.setProps({ formValues });
        });

        it('defaults to the first replication spec as the default selected spec', function (this: $TSFixMe) {
          expect(this.wrapper.instance().state.newFormSelectedReplicationSpecId).to.equal('000000000000000000000001');
        });
      });
    });
  });

  describe('If the user has geo-zoned sharding enabled with replicationSpecList', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues_WithReplicationSpecList()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_withReplicationSpecList_asJSON()}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    it('shows an additional accordion for the Map View', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).length).to.equal(5);
    });

    it('does not yet show the UI for zone configuration', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormGeoZones).length).to.equal(0);
    });

    it('does not yet show a divider between sets of accordions', function (this: $TSFixMe) {
      expect(this.wrapper.find('.nds-cluster-form-divider').length).to.equal(0);
    });

    it('still shows the sharding configuration UI', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormSharding).length).to.equal(1);
    });

    describe('and the cluster is geo-sharded', function () {
      beforeEach(function (this: $TSFixMe) {
        const formValues = clusterFixtures.getDefaultFormValues();
        formValues.clusterType = ClusterType.GEOSHARDED;

        this.wrapper.setProps({
          formValues,
        });
      });

      it('shows the UI for zone configuration', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormGeoZones).length).to.equal(1);
      });

      it('shows a divider between sets of accordions', function (this: $TSFixMe) {
        expect(this.wrapper.find('.nds-cluster-form-divider').length).to.equal(1);
      });

      it('no longer shows the sharding configuration UI', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormSharding).length).to.equal(0);
      });

      it('defaults to the first replication spec as the default selected spec', function (this: $TSFixMe) {
        expect(this.wrapper.instance().state.newFormSelectedReplicationSpecId).to.equal('000000000000000000000001');
      });

      describe('when the size of the list of replication specs increases to cover the selected index', function () {
        beforeEach(function (this: $TSFixMe) {
          const formValues = clusterFixtures.getDefaultFormValues();
          formValues.replicationSpecList =
            clusterFixtures.getAwsDefaultClusterDescription_geo6Zone_asJSON().replicationSpecList;
          this.wrapper.setProps({ formValues });
        });

        it('keeps the selected replication spec as the first in the list', function (this: $TSFixMe) {
          expect(this.wrapper.instance().state.newFormSelectedReplicationSpecId).to.equal('000000000000000000000004');
        });
      });

      describe('when the size of the list of replication specs decreases to not cover the selected index', function () {
        beforeEach(function (this: $TSFixMe) {
          const formValues = clusterFixtures.getDefaultFormValues();
          formValues.replicationSpecList = clusterFixtures.getAwsDefaultClusterDescription_asJSON().replicationSpecList;
          this.wrapper.setProps({ formValues });
        });

        it('defaults to the first replication spec as the default selected spec', function (this: $TSFixMe) {
          expect(this.wrapper.instance().state.newFormSelectedReplicationSpecId).to.equal('000000000000000000000001');
        });
      });
    });
  });

  describe('If editing an existing geo-zoned cluster', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.mount(
        <this.WrapperComponent isEdit formValues={clusterFixtures.getAwsDefaultClusterDescription_geo3Zone_asJSON()} />
      );
    });

    afterEach(function (this: $TSFixMe) {
      this.wrapper.unmount();
    });

    it('the additional map view accordion is opened', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).at(0).prop('active')).to.equal(true);
      expect(this.wrapper.find(NDSClusterForm).state().openAccordions.globalConfiguration).to.equal(true);
    });

    describe('when the zone configuration accordion is opened', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.find('.accordion-headline').at(1).simulate('click');
      });

      it('does not broadcast a tracking event', function () {
        expect(analytics.track).to.not.have.been.called;
      });
    });
  });

  describe('When rendered with Atlas gov environment enabled', function () {
    beforeEach(function (this: $TSFixMe) {
      this.settingsModel = new Settings({
        GROUP_ID: '123',
        ORG_ID: '408',
        DOCS_URL: '',
        RECAPTCHA_PUBLIC: '',
        HAS_ORG_BILLING_ADMIN: true,
        GROUP_ENABLED_FEATURE_FLAGS: [],
        IS_NDS_GOV_ENABLED: true,
      });
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues()}
          providerOptions={clusterFixtures.getProviderOptions()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    it('does not render the global cluster option', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormGeoOverView)).to.not.exist;
    });

    it('shows the correct number of Accordions for the form subsections', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).length).to.equal(4);
    });

    it('has aws the only enabled gov providers in prop', function (this: $TSFixMe) {
      const { enabledBackingProviders } = this.wrapper.find(NDSClusterFormProviderButtons).props();
      expect(enabledBackingProviders).to.eql([BackingCloudProvider.AWS]);
    });
  });

  describe('When rendered with Atlas gov environment and GCP Assured workloads enabled with commerical regions allowed', function () {
    beforeEach(function (this: $TSFixMe) {
      this.settingsModel = new Settings({
        GROUP_ID: '123',
        ORG_ID: '408',
        DOCS_URL: '',
        RECAPTCHA_PUBLIC: '',
        HAS_ORG_BILLING_ADMIN: true,
        IS_NDS_GOV_ENABLED: true,
      });
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues()}
          providerOptions={clusterFixtures.getProviderOptions()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          regionUsageRestrictions={RegionUsageRestrictions.COMMERCIAL_FEDRAMP_REGIONS_ONLY}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });
    it('has aws the only enabled gov providers in prop', function (this: $TSFixMe) {
      const { enabledBackingProviders } = this.wrapper.find(NDSClusterFormProviderButtons).props();
      expect(enabledBackingProviders).to.eql([BackingCloudProvider.AWS]);
    });
  });

  describe('When rendered with Atlas gov environment and GCP Assured workloads enabled with gov regions only', function () {
    beforeEach(function (this: $TSFixMe) {
      this.settingsModel = new Settings({
        GROUP_ID: '123',
        ORG_ID: '408',
        DOCS_URL: '',
        RECAPTCHA_PUBLIC: '',
        HAS_ORG_BILLING_ADMIN: true,
        IS_NDS_GOV_ENABLED: true,
      });
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues()}
          providerOptions={clusterFixtures.getProviderOptions()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          regionUsageRestrictions={RegionUsageRestrictions.GOV_REGIONS_ONLY}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });
    it('has aws and gcp as the enabled gov providers in prop', function (this: $TSFixMe) {
      const { enabledBackingProviders } = this.wrapper.find(NDSClusterFormProviderButtons).props();
      expect(enabledBackingProviders).to.eql([BackingCloudProvider.AWS, BackingCloudProvider.GCP]);
    });
  });
  describe('When rendered with Atlas gov environment enabled with replicationSpecList', function () {
    beforeEach(function (this: $TSFixMe) {
      this.settingsModel = new Settings({
        GROUP_ID: '123',
        ORG_ID: '408',
        DOCS_URL: '',
        RECAPTCHA_PUBLIC: '',
        HAS_ORG_BILLING_ADMIN: true,
        GROUP_ENABLED_FEATURE_FLAGS: [],
        IS_NDS_GOV_ENABLED: true,
      });
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          formValues={clusterFixtures.getDefaultFormValues_WithReplicationSpecList()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_withReplicationSpecList_asJSON()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    it('does not render the global cluster option', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormGeoOverView)).to.not.exist;
    });

    it('shows the correct number of Accordions for the form subsections', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).length).to.equal(4);
    });
  });

  describe('When rendered for a Atlas CN regions only project', function () {
    beforeEach(function (this: $TSFixMe) {
      this.settingsModel = new Settings({
        GROUP_ID: '123',
        ORG_ID: '408',
        DOCS_URL: '',
        RECAPTCHA_PUBLIC: '',
        HAS_ORG_BILLING_ADMIN: true,
        GROUP_ENABLED_FEATURE_FLAGS: [],
      });
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues()}
          providerOptions={clusterFixtures.getProviderOptions()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    it('does not render the global cluster option', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormGeoOverView)).to.not.exist;
    });

    it('shows the correct number of Accordions for the form subsections', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).length).to.equal(4);
    });
  });

  describe('When cluster form is rendered with SHARED deployment type', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          formValues={clusterFixtures.getDefaultFormValues()}
          providerOptions={clusterFixtures.getProviderOptions()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.SHARED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    it('does not render the global cluster option', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormGeoOverView)).to.not.exist;
    });

    describe('When only one mongodbversion is available', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper.setProps({
          providerOptions: {
            ...clusterFixtures.getProviderOptions(),
            AWS: {
              ...clusterFixtures.getProviderOptions().AWS,
              mongodbMajorVersions: [
                {
                  name: '4.0',
                  default: true,
                  deprecated: false,
                },
              ],
            },
          },
        });
      });

      it('version selection has visible prop set to false', function (this: $TSFixMe) {
        expect(this.wrapper.find(NDSClusterFormVersion).prop('visible')).to.be.false;
      });
    });
  });

  describe('context', function () {
    let contextStub: $TSFixMe;
    let component: $TSFixMe;

    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.mount(<this.WrapperComponent />);
      component = this.wrapper.find('NDSClusterForm').instance();
      contextStub = this.sandbox.stub(component.context, 'setDesiredInstanceSize');
    });

    afterEach(function (this: $TSFixMe) {
      this.wrapper.unmount();
    });

    it('should set desired instance size on mounting component', function () {
      component.componentDidMount();
      expect(contextStub).to.have.been.calledWith('M10');
    });
  });

  describe('check open accordions when cluster form is rendered', function () {
    const checkAccordionStatus = (shouldBeActive: boolean, dataTestId: string) => {
      it('confirms Instance Tier is active or not based on param', function (this: $TSFixMe) {
        const accordions = this.wrapper.find(Accordion);
        const endIdx = accordions.length;
        const startIdx = 0;
        for (let i = startIdx; i < endIdx; i++) {
          const currAccordion = accordions.at(i);
          if (currAccordion.prop('data-testid') === dataTestId) {
            expect(currAccordion.prop('active')).to.equal(shouldBeActive);
            break;
          }
        }
      });
    };

    describe('on a SHARED cluster tier', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper = enzyme.shallow(
          // @ts-expect-error TS(2769): No overload matches this call.
          <NDSClusterForm
            isInstanceSizeVisible={() => true}
            setClusterFormValue={() => {}}
            formValues={clusterFixtures.getDefaultFormValues()}
            providerOptions={clusterFixtures.getProviderOptions()}
            originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
            crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
            defaultTemplates={clusterFixtures.getDefaultTemplates()}
            processArgs={clusterFixtures.getDefaultProcessArgs()}
            originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
            clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
            isEdit={false}
            m0LimitReached={false}
            isTenantUpgrade={false}
            isLDAPEnabled={false}
            encryptionAtRest={{
              awsKms: { enabled: false },
              azureKeyVault: { enabled: false },
              googleCloudKms: { enabled: false },
            }}
            isPrivateIPModeEnabled={false}
            useCNRegionsOnly={false}
            settingsModel={this.settingsModel}
            billingEstimate={{
              hourlyEstimateDollars: '0.00',
              monthlyEstimateDollars: '0.00',
              snapshotBackupEstimateDollars: '0.00',
            }}
            biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
            instanceClass={INSTANCE_CLASS.HIGH_CPU}
            analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
            cloudContainers={[]}
            customRoles={[]}
            clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
            isNameValid={this.isNameValid}
            isNameUnique={this.isNameUnique}
            isNamePrefixUnique={this.isNamePrefixUnique}
            isNameEndWithHyphen={this.isNameEndWithHyphen}
            isNameWithinCharLimit={this.isNameWithinCharLimit}
            error={null}
            isAutoIndexingEligible
            deploymentType={DeploymentType.SHARED}
            setDeploymentType={() => {}}
            groupLimits={DEFAULT_LIMITS}
            hasUnsetOplogMinRetentionHours={false}
            updateCrossCloudProviderOptions={() => {}}
            regionProviderFeatures={{}}
            isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
            isAtlasSqlClusterSchemaInterfaceEnabled={false}
            searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
          />
        );
      });
      it('instance size accordion should be active', function () {
        checkAccordionStatus(true, ACCORDION_NAMES.INSTANCE_TIER.name);
      });
    });

    describe('on a SHARED cluster tier with openSection prop set', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper = enzyme.shallow(
          // @ts-expect-error TS(2769): No overload matches this call.
          <NDSClusterForm
            isInstanceSizeVisible={() => true}
            setClusterFormValue={() => {}}
            formValues={clusterFixtures.getDefaultFormValues()}
            providerOptions={clusterFixtures.getProviderOptions()}
            originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
            crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
            defaultTemplates={clusterFixtures.getDefaultTemplates()}
            processArgs={clusterFixtures.getDefaultProcessArgs()}
            originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
            clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
            isEdit={false}
            m0LimitReached={false}
            isTenantUpgrade={false}
            isLDAPEnabled={false}
            encryptionAtRest={{
              awsKms: { enabled: false },
              azureKeyVault: { enabled: false },
              googleCloudKms: { enabled: false },
            }}
            isPrivateIPModeEnabled={false}
            useCNRegionsOnly={false}
            settingsModel={this.settingsModel}
            billingEstimate={{
              hourlyEstimateDollars: '0.00',
              monthlyEstimateDollars: '0.00',
              snapshotBackupEstimateDollars: '0.00',
            }}
            biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
            instanceClass={INSTANCE_CLASS.HIGH_CPU}
            analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
            cloudContainers={[]}
            customRoles={[]}
            clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
            isNameValid={this.isNameValid}
            isNameUnique={this.isNameUnique}
            isNamePrefixUnique={this.isNamePrefixUnique}
            isNameEndWithHyphen={this.isNameEndWithHyphen}
            isNameWithinCharLimit={this.isNameWithinCharLimit}
            error={null}
            isAutoIndexingEligible
            deploymentType={DeploymentType.SHARED}
            setDeploymentType={() => {}}
            groupLimits={DEFAULT_LIMITS}
            hasUnsetOplogMinRetentionHours={false}
            openSection={ACCORDION_NAMES.ADDITIONAL_SETTINGS.name}
            updateCrossCloudProviderOptions={() => {}}
            regionProviderFeatures={{}}
            isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
            isAtlasSqlClusterSchemaInterfaceEnabled={false}
            searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
          />
        );
      });
      it('addition settings accordion should be active', function () {
        checkAccordionStatus(true, ACCORDION_NAMES.ADDITIONAL_SETTINGS.name);
      });
    });

    describe('on a DEDICATED cluster tier', function () {
      beforeEach(function (this: $TSFixMe) {
        this.wrapper = enzyme.shallow(
          // @ts-expect-error TS(2769): No overload matches this call.
          <NDSClusterForm
            isInstanceSizeVisible={() => true}
            setClusterFormValue={() => {}}
            formValues={clusterFixtures.getDefaultFormValues()}
            providerOptions={clusterFixtures.getProviderOptions()}
            originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
            crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
            defaultTemplates={clusterFixtures.getDefaultTemplates()}
            processArgs={clusterFixtures.getDefaultProcessArgs()}
            originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
            clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
            isEdit={false}
            m0LimitReached={false}
            isTenantUpgrade={false}
            isLDAPEnabled={false}
            encryptionAtRest={{
              awsKms: { enabled: false },
              azureKeyVault: { enabled: false },
              googleCloudKms: { enabled: false },
            }}
            isPrivateIPModeEnabled={false}
            useCNRegionsOnly={false}
            settingsModel={this.settingsModel}
            billingEstimate={{
              hourlyEstimateDollars: '0.00',
              monthlyEstimateDollars: '0.00',
              snapshotBackupEstimateDollars: '0.00',
            }}
            biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
            instanceClass={INSTANCE_CLASS.HIGH_CPU}
            analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
            cloudContainers={[]}
            customRoles={[]}
            clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
            isNameValid={this.isNameValid}
            isNameUnique={this.isNameUnique}
            isNamePrefixUnique={this.isNamePrefixUnique}
            isNameEndWithHyphen={this.isNameEndWithHyphen}
            isNameWithinCharLimit={this.isNameWithinCharLimit}
            error={null}
            isAutoIndexingEligible
            deploymentType={DeploymentType.DEDICATED}
            setDeploymentType={() => {}}
            groupLimits={DEFAULT_LIMITS}
            hasUnsetOplogMinRetentionHours={false}
            updateCrossCloudProviderOptions={() => {}}
            regionProviderFeatures={{}}
            isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
            isAtlasSqlClusterSchemaInterfaceEnabled={false}
            searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
          />
        );
      });
      it('instance size accordion should be inactive by default', function () {
        checkAccordionStatus(false, ACCORDION_NAMES.INSTANCE_TIER.name);
      });
    });

    [
      ACCORDION_NAMES.INSTANCE_TIER.name,
      ACCORDION_NAMES.ADDITIONAL_SETTINGS.name,
      ACCORDION_NAMES.NAME_CLUSTER.name,
    ].forEach((accordionName) => {
      describe('on a DEDICATED cluster tier with openSection prop set', function () {
        beforeEach(function (this: $TSFixMe) {
          this.wrapper = enzyme.shallow(
            // @ts-expect-error TS(2769): No overload matches this call.
            <NDSClusterForm
              isInstanceSizeVisible={() => true}
              setClusterFormValue={() => {}}
              formValues={clusterFixtures.getDefaultFormValues()}
              providerOptions={clusterFixtures.getProviderOptions()}
              originalCluster={clusterFixtures.getAwsDefaultClusterDescription_asJSON()}
              crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
              defaultTemplates={clusterFixtures.getDefaultTemplates()}
              processArgs={clusterFixtures.getDefaultProcessArgs()}
              originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
              clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
              isEdit={false}
              m0LimitReached={false}
              isTenantUpgrade={false}
              isLDAPEnabled={false}
              encryptionAtRest={{
                awsKms: { enabled: false },
                azureKeyVault: { enabled: false },
                googleCloudKms: { enabled: false },
              }}
              isPrivateIPModeEnabled={false}
              useCNRegionsOnly={false}
              settingsModel={this.settingsModel}
              billingEstimate={{
                hourlyEstimateDollars: '0.00',
                monthlyEstimateDollars: '0.00',
                snapshotBackupEstimateDollars: '0.00',
              }}
              biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
              instanceClass={INSTANCE_CLASS.HIGH_CPU}
              analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
              cloudContainers={[]}
              customRoles={[]}
              clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
              isNameValid={this.isNameValid}
              isNameUnique={this.isNameUnique}
              isNamePrefixUnique={this.isNamePrefixUnique}
              isNameEndWithHyphen={this.isNameEndWithHyphen}
              isNameWithinCharLimit={this.isNameWithinCharLimit}
              error={null}
              isAutoIndexingEligible
              deploymentType={DeploymentType.DEDICATED}
              setDeploymentType={() => {}}
              openSection={accordionName}
              groupLimits={DEFAULT_LIMITS}
              hasUnsetOplogMinRetentionHours={false}
              updateCrossCloudProviderOptions={() => {}}
              regionProviderFeatures={{}}
              isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
              isAtlasSqlClusterSchemaInterfaceEnabled={false}
              searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
            />
          );
        });
        it(`${accordionName} accordion should be active`, function () {
          checkAccordionStatus(true, accordionName);
        });
      });
    });
  });

  describe('When rendered with replicationSpecList containing analytics nodes', function () {
    beforeEach(function (this: $TSFixMe) {
      this.settingsModel = new Settings({
        GROUP_ID: '123',
        ORG_ID: '408',
        DOCS_URL: '',
        RECAPTCHA_PUBLIC: '',
        HAS_ORG_BILLING_ADMIN: true,
        IS_NDS_GOV_ENABLED: false,
      });
      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          formValues={clusterFixtures.getDefaultFormValues_WithReplicationSpecListAndAnalyticsNodes()}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_withReplicationSpecList_asJSON()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
          searchDeploymentSpec={{ nodeCount: 2, instanceSize: 'S20_HIGHCPU_NVME' }}
        />
      );
    });

    it('renders two Tabs that each contain one NDSClusterFormInstanceSize each', function (this: $TSFixMe) {
      expect(this.wrapper.find(Tab).length).to.equal(2);
      const baseTierTab = this.wrapper.find(Tab).at(0);
      const analyticsTierTab = this.wrapper.find(Tab).at(1);
      expect(baseTierTab.find(NDSClusterFormInstanceSize).length).to.equal(1);
      expect(analyticsTierTab.find(NDSClusterFormInstanceSize).length).to.equal(1);
    });
  });

  describe('When rendered with configured Search nodes', function () {
    beforeEach(function (this: $TSFixMe) {
      this.settingsModel = new Settings({
        GROUP_ID: '123',
        ORG_ID: '408',
        DOCS_URL: '',
        RECAPTCHA_PUBLIC: '',
        HAS_ORG_BILLING_ADMIN: true,
        IS_NDS_GOV_ENABLED: false,
      });

      const enabledSearchNodes = {
        removeSearchConsiderationConfirmed: false,
        searchNodesEnabled: true,
        searchDeploymentSpec: {
          instanceSize: 'S20_HIGHCPU_NVME',
          nodeCount: 4,
        },
      };

      this.formValues = clusterFixtures.getDefaultFormValues_WithReplicationSpecListAndAnalyticsNodes();

      this.wrapper = enzyme.shallow(
        // @ts-expect-error TS(2769): No overload matches this call.
        <NDSClusterForm
          isInstanceSizeVisible={() => true}
          setClusterFormValue={() => {}}
          providerOptions={clusterFixtures.getProviderOptions()}
          crossCloudProviderOptions={clusterFixtures.getCrossCloudProviderOptions()}
          formValues={this.formValues}
          originalCluster={clusterFixtures.getAwsDefaultClusterDescription_withReplicationSpecList_asJSON()}
          defaultTemplates={clusterFixtures.getDefaultTemplates()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={clusterFixtures.getDefaultClusterUsageStats()}
          isEdit={false}
          m0LimitReached={false}
          isTenantUpgrade={false}
          isLDAPEnabled={false}
          encryptionAtRest={{
            awsKms: { enabled: false },
            azureKeyVault: { enabled: false },
            googleCloudKms: { enabled: false },
          }}
          isPrivateIPModeEnabled={false}
          useCNRegionsOnly={false}
          settingsModel={this.settingsModel}
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          biConnectorCostEstimate={BI_CONNECTOR_COST_ESTIMATE}
          instanceClass={INSTANCE_CLASS.HIGH_CPU}
          analyticsInstanceClass={INSTANCE_CLASS.HIGH_CPU}
          cloudContainers={[]}
          customRoles={[]}
          clusterBuilderFilterInterface={clusterFixtures.getDefaultClusterBuilderFilterInterface()}
          isNameValid={this.isNameValid}
          isNameUnique={this.isNameUnique}
          isNamePrefixUnique={this.isNamePrefixUnique}
          isNameEndWithHyphen={this.isNameEndWithHyphen}
          isNameWithinCharLimit={this.isNameWithinCharLimit}
          error={null}
          isAutoIndexingEligible
          deploymentType={DeploymentType.DEDICATED}
          setDeploymentType={() => {}}
          openSection={null}
          groupLimits={DEFAULT_LIMITS}
          hasUnsetOplogMinRetentionHours={false}
          updateCrossCloudProviderOptions={() => {}}
          regionProviderFeatures={{}}
          {...enabledSearchNodes}
          isAllowCrossRegionExtendedStorageFeatureFlagEnabled={false}
          isAtlasSqlClusterSchemaInterfaceEnabled={false}
        />
      );
    });

    it('should show "Search tier" tab after Base and Analytics tabs', function (this: $TSFixMe) {
      expect(this.wrapper.find(Tab).length).to.equal(3);
      const searchTierTab = this.wrapper.find(Tab).at(2);
      expect(searchTierTab.find(NDSSearchDeploymentFormInstanceSize).length).to.equal(1);
    });

    it('should show selected Search tier info in Accordion tab', function (this: $TSFixMe) {
      this.wrapper.find(Accordion);
      const clusterTierAccordion = this.wrapper.find(Accordion).at(2);
      const view = enzyme.render(clusterTierAccordion.prop('secondary'));
      const clusterSubText = view.find('li:nth-child(1)').text();
      const searchSubText = view.find('li:nth-child(2)').text();
      expect(clusterSubText).to.equal('M10 (2 GB RAM, 10 GB Storage)');
      expect(searchSubText).to.equal('Search S20 (2 GB RAM, 5 GB Storage)');
    });

    it('should reset selected Cluster tier tab when Analytics/Search nodes were added or removed', function (this: $TSFixMe) {
      this.wrapper.setState({
        selectedClusterTierTab: 2,
      });

      this.wrapper.instance().componentDidUpdate({
        searchNodesEnabled: false,
        formValues: this.formValues,
      });

      expect(this.wrapper.state('selectedClusterTierTab')).to.equal(0);
    });
  });

  describe('When rendered in edit with resource tagging is disabled', function () {
    beforeEach(function (this: $TSFixMe) {
      this.sandbox.stub(this.settingsModel, 'hasProjectFeature').withArgs('RESOURCE_TAG_COMPONENT').returns(false);
      const formValues = clusterFixtures.getDefaultFormValues_WithReplicationSpecList();
      this.wrapper = enzyme.mount(<this.WrapperComponent formValues={formValues} settingsModel={this.settingsModel} />);
    });

    it('should NOT show the tags table', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormTags)).to.not.exist;
    });
  });

  describe('When rendered in edit with resource tagging enabled', function () {
    beforeEach(function (this: $TSFixMe) {
      const formValues = clusterFixtures.getDefaultFormValues_WithReplicationSpecList();
      this.wrapper = enzyme.mount(
        <this.WrapperComponent
          formValues={formValues}
          settingsModel={this.settingsModel}
          tags={[{ key: 'env', value: 'dev' }]}
          isResourceTaggingEnabled={true}
        />
      );
    });

    it('should show the tags table', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormTags)).to.exist;
    });
  });

  describe('When rendered in edit with resource tagging disabled', function () {
    beforeEach(function (this: $TSFixMe) {
      const formValues = clusterFixtures.getDefaultFormValues_WithReplicationSpecList();
      this.wrapper = enzyme.mount(
        <this.WrapperComponent
          formValues={formValues}
          settingsModel={this.settingsModel}
          tags={[{ key: 'env', value: 'dev' }]}
          isResourceTaggingEnabled={false}
        />
      );
    });

    it('should not show the tags table', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormTags)).to.not.exist;
    });
  });

  describe('When rendered with enableBackups', () => {
    beforeEach(function (this: $TSFixMe) {
      const formValues = clusterFixtures.getDefaultFormValues_WithReplicationSpecList();
      this.setBackupValues = this.sandbox.stub();
      this.wrapper = enzyme.mount(
        <this.WrapperComponent
          isEdit={true}
          formValues={formValues}
          settingsModel={this.settingsModel}
          enableBackups={true}
          setBackupValues={this.setBackupValues}
          hideGuideCues={true}
        />
      );
    });

    it('should have the setting for backups enabled', function (this: $TSFixMe) {
      expect(this.setBackupValues).to.have.been.calledOnce;
      expect(this.setBackupValues).to.have.been.calledWith({
        backupEnabled: undefined,
        diskBackupEnabled: true,
        pitEnabled: true,
      });
    });
  });

  describe('When rendered with enableAutoscaling', () => {
    let wasSetAutoScalingCalled = false;

    beforeEach(function (this: $TSFixMe) {
      const formValues = clusterFixtures.getDefaultFormValues_WithReplicationSpecList();
      this.wrapper = enzyme.mount(
        <this.WrapperComponent
          isEdit={true}
          formValues={formValues}
          settingsModel={this.settingsModel}
          enableAutoscaling={true}
          setAutoScaling={() => {
            wasSetAutoScalingCalled = true;
          }}
          hideGuideCues={true}
        />
      );
    });

    it('should execute setAutoScaling callback', function () {
      expect(wasSetAutoScalingCalled).to.equal(true);
    });
  });

  // when asymmetric sharded cluster is being edited
  describe('when rendered with split replicationSpecList', function () {
    beforeEach(function (this: $TSFixMe) {
      const formValues =
        clusterFixtures.getDefaultFormValues_WithReplicationSpecListAndAnalyticsNodes_AsymmetricSharded();
      this.wrapper = enzyme.mount(
        <this.WrapperComponent
          isEdit={true}
          formValues={formValues}
          settingsModel={this.settingsModel}
          originalCluster={clusterFixtures.getAwsAsymmetricShardedClusterDescription_asJSON()}
          hideGuideCues={true}
        />
      );
    });

    afterEach(function (this: $TSFixMe) {
      this.wrapper.unmount();
    });

    it('shows Accordions for the form subsections', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).length).to.equal(4);
    });
    it('shows Accordions for Cluster Tier to have overflow inherited', function (this: $TSFixMe) {
      expect(this.wrapper.find(Accordion).at(2).prop('inheritOverflow')).to.be.true;
    });

    it('shows cloud provider secondary text', function (this: $TSFixMe) {
      expect(this.wrapper.find('.accordion-headline-secondary-text').at(1).text()).to.include('AWS');
      expect(this.wrapper.find('.accordion-headline-secondary-subtext').at(1).text()).to.include(
        '3 Electable, 1 Analytics Node'
      );
    });

    // shows the largest instance
    it('shows cluster tier secondary text', function (this: $TSFixMe) {
      expect(this.wrapper.find('.accordion-headline-secondary-text').at(2).text()).to.equal(
        'M30, and more Base (8 GB RAM), M40 Analytics (16 GB RAM)'
      );
      expect(this.wrapper.find('.accordion-headline-secondary-subtext').at(2).text()).to.equal(
        '10 GB Storage per Shard, 1,000 IOPS per Shard, Encrypted, Auto-expand Storage'
      );
    });

    it('shows correct number of shards', function (this: $TSFixMe) {
      expect(this.wrapper.find('.accordion-headline-secondary-text').at(3).text()).to.include('2 Shards');
    });
  });

  describe('when for serverless upgrade to dedicated', function () {
    beforeEach(function (this: $TSFixMe) {
      this.wrapper = enzyme.mount(
        <this.WrapperComponent
          formValues={clusterFixtures.getDefaultFormValues()}
          // Note: Serverless provider option provided
          providerOptions={{
            ...clusterFixtures.getProviderOptions(),
            [CloudProvider.SERVERLESS]: clusterFixtures.getServerlessClusterOptions_asJSON(),
          }}
          originalCluster={clusterFixtures.getServerlessDefaultClusterDescriptionForAws_asJSON()}
          processArgs={clusterFixtures.getDefaultProcessArgs()}
          originalProcessArgs={clusterFixtures.getDefaultProcessArgs()}
          clusterUsageStats={{
            ...clusterFixtures.getDefaultClusterUsageStats(),
            clusterMaxDiskUsageMB: 1 * 1024, // 1 GB
          }}
          isEdit={true}
          isTenantUpgrade={true}
          settingsModel={this.settingsModel}
          // Note: Billing estimate for dedicated cluster
          billingEstimate={{
            hourlyEstimateDollars: '0.00',
            monthlyEstimateDollars: '0.00',
            snapshotBackupEstimateDollars: '0.00',
          }}
          deploymentType={DeploymentType.DEDICATED}
        />
      );
    });

    it('show the recommended instance size and storage size banner', function (this: $TSFixMe) {
      expect(this.wrapper.find(Banner).at(0).text()).to.include(
        'For large data sizes (100 GB+), we recommend using a higher-tier cluster (e.g., M80) with larger disk sizes (e.g., 4 TB).'
      );
      expect(this.wrapper.find(Banner).at(0).text()).to.include(
        'Index builds are memory-intensive, and under provisioning may significantly slow down the migration. You can easily scale down to a lower-tier cluster after the process completes.'
      );
      expect(this.wrapper.find(Banner).at(0).text()).to.include(
        'For smaller migrations, at least an M40 cluster with 80 GB of storage is recommended to ensure a smooth migration.'
      );
    });

    it('does not show the geozone configuration section', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormGeoOverView)).to.not.exist;
    });

    it('shows the cloud provider & region accordion as disabled with the correct region', function (this: $TSFixMe) {
      const accordion = this.wrapper.find(Accordion).at(0);
      expect(accordion).to.exist;
      expect(accordion.props().secondaryText).to.contain('AWS');
      expect(accordion.props().secondaryText).to.contain('N. Virginia (us-east-1)');
      expect(accordion.props().disabled).to.be.true;
    });

    it('does not show the process options section', function (this: $TSFixMe) {
      expect(this.wrapper.find(NDSClusterFormAdvancedOptions).length).to.equal(0);
    });
  });

  describe('when query parameter is invalid', function () {
    it('should not break when openSection is invalid', function (this: $TSFixMe) {
      expect(() => {
        this.WrapperComponent({ openSection: 'invalidSection' });
      }).to.not.throw();
    });
  });
});
