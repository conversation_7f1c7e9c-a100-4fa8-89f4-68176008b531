import userEvent from '@testing-library/user-event';

import { GoogleCloudKMS } from '@packages/types/nds/security/enterpriseSecurity';

import * as api from '@packages/common/services/api';
import {
  MOCK_GCP_SERVICE_ACCOUNTS,
  SERVICE_ACCOUNT_KEY_GOOGLE_KMS,
} from '@packages/cluster-editor/EncryptionAtRest/fixtures/googleCloudKMSFormFixtures';
import GoogleCloudKMSFormNew, { Props, TestId } from '@packages/cluster-editor/EncryptionAtRest/GoogleCloudKMSFormNew';
import { getDefaultGoogleCloudKMS } from '@packages/common/fixtures/encryptionAtRestFixtures';
import EncryptionAtRest from '@packages/common/models/EncryptionAtRest';
import mongoDate from '@packages/date';
import { fireEvent, render, screen, waitFor, within } from '@packages/react-testing-library';

describe('@packages/cluster-editor/EncryptionAtRest/GoogleCloudKMSFormNew', () => {
  const user = userEvent.setup();

  let setInputValueMock: jest.Func;
  let disableEncryptionAtRestMock: jest.Func;
  let patchEncryptionAtRestSpy: jest.SpyInstance;

  beforeEach(() => {
    jest.spyOn(mongoDate.prototype, 'diff').mockReturnValue(10);
    setInputValueMock = jest.fn();
    disableEncryptionAtRestMock = jest.fn();
    patchEncryptionAtRestSpy = jest.spyOn(api.nds.encryptionAtRest, 'patchEncryptionAtRest');
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  // default value for kms that has never been configured
  const INITIAL_GOOGLE_CLOUD_KMS: GoogleCloudKMS = {
    enabled: false,
    keyVersionResourceID: null,
    lastUpdatedKeyID: null,
    networkAccessDeniedDate: null,
    roleId: null,
    valid: false,
  };

  const renderGoogleCloudKMSFormNew = (overrides: Partial<Props> = {}) => {
    const defaultProps = {
      isGroupOwner: true,
      groupId: '408',
      googleCloudKms: getDefaultGoogleCloudKMS(),
      enabled: false,
      setParentComponentState: setInputValueMock,
      cancel: jest.fn(),
      serviceAccountKeyPlaceholder: '*',
      disableEncryptionAtRest: disableEncryptionAtRestMock,
      encryptedClusters: [],
      enabledForSearchNodes: false,
      encryptionAtRest: new EncryptionAtRest(),
      gcpServiceAccounts: [],
      ...overrides,
    };

    return render(<GoogleCloudKMSFormNew {...defaultProps} />, { leafygreenToastContext: false });
  };

  const openAuthenticationMethodAccordion = async () => {
    await user.click(screen.getByRole('button', { name: 'Authentication Method' }));
  };

  describe('when rendered and Google Cloud KMS is not enabled', () => {
    it('should render correctly with all expected elements and states', () => {
      renderGoogleCloudKMSFormNew({
        googleCloudKms: getDefaultGoogleCloudKMS(),
        enabled: false,
      });

      // Should not be rendered in edit mode
      const accordions = screen.getAllByTestId('inline-accordion');
      expect(accordions).toHaveLength(2);
      expect(accordions[0]).toHaveClass('inline-accordion-is-open');
      expect(accordions[1]).toHaveClass('inline-accordion-is-open');
      expect(screen.queryByText('Generate a new version of your key', { exact: false })).not.toBeInTheDocument();

      // Should not have a try again button
      expect(screen.queryByRole('button', { name: 'Try Again' })).not.toBeInTheDocument();

      // Should not have a selected badge
      expect(screen.queryByTestId('selectedBadge')).not.toBeInTheDocument();

      // Should show the authentication toggle switch
      expect(screen.getByTestId(TestId.gcpKMSAuthToggle)).toBeInTheDocument();
    });
  });

  it('should render edit mode correctly with all expected elements and warnings', () => {
    renderGoogleCloudKMSFormNew({
      googleCloudKms: SERVICE_ACCOUNT_KEY_GOOGLE_KMS,
      enabled: true,
    });

    // Shows the disable button as clickable
    expect(screen.getByRole('button', { name: 'Disable Google Cloud KMS' })).toBeEnabled();

    // Form is rendered in edit mode
    screen.getByText('Generate a new version of your key', { exact: false });

    // Shows both accordions as expanded when legacy credentials exist
    const accordions = screen.getAllByTestId('inline-accordion');
    expect(accordions).toHaveLength(2);
    expect(accordions[0]).toHaveClass('inline-accordion-is-open');
    expect(accordions[1]).toHaveClass('inline-accordion-is-open'); // Now expanded due to legacy credentials

    // Shows a warning and alert for key configuration date
    screen.getByText('Configured 10 days ago');
    expect(screen.getByTestId('alert')).toHaveTextContent('configured 10 days ago');

    // Shows a warning about no clusters using Google Cloud KMS
    expect(screen.getByTestId('alert')).toHaveTextContent('No clusters are encrypted with your Google Cloud KMS.');
  });

  describe('when Google Cloud KMS key has been revoked', () => {
    beforeEach(() => {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderGoogleCloudKMSFormNew({
        googleCloudKms: { ...SERVICE_ACCOUNT_KEY_GOOGLE_KMS, valid: false },
        enabled: true,
        gcpServiceAccounts: MOCK_GCP_SERVICE_ACCOUNTS,
      });
    });

    it('should show an error explaining mongo processes have been shut down', () => {
      const alerts = screen.getAllByTestId('alert');
      expect(alerts).toHaveLength(2);
      expect(alerts[1]).toHaveTextContent(
        'Google Cloud KMS Encryption At Rest settings are no longer valid. All MongoDB processes that have not already been stopped will be stopped imminently.'
      );
    });

    describe('when all of the inputs are displayed', () => {
      it('should have "Key Version Resource ID" disabled', () => {
        expect(screen.getByTestId('key-version-resource-id-input')).toBeDisabled();
      });

      it('should have "Service Account Key" enabled', async () => {
        await openAuthenticationMethodAccordion();

        // Find the Select component by its test ID - it IS the button
        const dropdownButton = screen.getByTestId(TestId.serviceAccountDropdown);
        expect(dropdownButton).toBeEnabled();

        // Should be able to open the dropdown
        await user.click(dropdownButton);
      });
    });

    describe('and there is a try again button', () => {
      it('should have a button visible', () => {
        screen.getByRole('button', { name: 'tryAgain Try Again' });
      });

      it('should send a patch request to the API', async () => {
        await user.click(screen.getByRole('button', { name: 'tryAgain Try Again' }));
        await waitFor(() => expect(patchEncryptionAtRestSpy).toHaveBeenCalledTimes(1));
      });

      it('should hide the button to try again when change the access key id and secret', async () => {
        await openAuthenticationMethodAccordion();

        // Find the Select component by its test ID - it IS the button
        const dropdownButton = screen.getByTestId(TestId.serviceAccountDropdown);

        // Click on the dropdown button to open it
        await user.click(dropdownButton);

        // After clicking, the dropdown should be accessible and functional
        // We can verify this by checking that the dropdown button is now labeled "close menu"

        // Select a new service account from the dropdown
        const newServiceAccount = MOCK_GCP_SERVICE_ACCOUNTS[0]; // Select the first service account
        const serviceAccountOption = screen.getByText(newServiceAccount.gcpServiceAccountForAtlas);
        await user.click(serviceAccountOption);

        // After selecting a new service account, the Try Again button should disappear
        // because revalidateEnabled() will return false (roleId has changed from null to the new roleId)
        await waitFor(() => {
          expect(screen.queryByRole('button', { name: 'tryAgain Try Again' })).not.toBeInTheDocument();
        });
      });
    });
  });

  describe('when Google Cloud KMS network access is restricted', () => {
    it('should show an error explaining Atlas control plane access to cloud provider has been restricted', () => {
      renderGoogleCloudKMSFormNew({
        googleCloudKms: { ...SERVICE_ACCOUNT_KEY_GOOGLE_KMS, networkAccessDeniedDate: '2023-11-07T19:22:59Z' },
        enabled: true,
      });

      const alerts = screen.getAllByTestId('alert');
      expect(alerts).toHaveLength(2);
      expect(alerts[1]).toHaveTextContent('Google Cloud KMS');
      expect(alerts[1]).toHaveTextContent('IP addresses');
    });
  });

  it('when there are clusters using Google Cloud KMS should render correctly with cluster information and disabled state', () => {
    renderGoogleCloudKMSFormNew({
      googleCloudKms: SERVICE_ACCOUNT_KEY_GOOGLE_KMS,
      enabled: true,
      encryptedClusters: ['a'],
    });

    // Does not show a warning about no clusters using Google Cloud KMS
    expect(screen.getAllByTestId('alert')).toHaveLength(1);

    // Shows an accordion for the clusters using Google Cloud KMS
    const accordions = screen.getAllByTestId('inline-accordion');
    expect(accordions).toHaveLength(3);
    expect(accordions[0]).toHaveTextContent('Configured on 1 cluster');

    // Shows the disable button as not clickable
    const disableKMSButton = screen.getByRole('button', { name: 'Disable Google Cloud KMS' });
    expect(disableKMSButton).toBeDisabled();
  });

  describe('when the user clicks the disable button', () => {
    beforeEach(async () => {
      // eslint-disable-next-line testing-library/no-render-in-lifecycle
      renderGoogleCloudKMSFormNew({
        googleCloudKms: SERVICE_ACCOUNT_KEY_GOOGLE_KMS,
        enabled: true,
      });
      await user.click(screen.getByRole('button', { name: 'Disable Google Cloud KMS' }));
    });

    it('shows the disable modal', () => {
      screen.getByRole('heading', { name: 'Disable Google Cloud KMS' });
    });

    it('submits a disable request for Google Cloud KMS when the user confirms the disable action', async () => {
      await user.click(screen.getByRole('button', { name: 'Yes, Disable Google Cloud KMS' }));
      expect(disableEncryptionAtRestMock).toHaveBeenCalledWith({ googleCloudKms: { enabled: false } });
    });
  });

  describe('when service accounts are available', () => {
    it('should show service account options when service accounts are available', async () => {
      const targetServiceAccount = MOCK_GCP_SERVICE_ACCOUNTS[1];
      // Render with mock service accounts and a valid configuration
      renderGoogleCloudKMSFormNew({
        googleCloudKms: getDefaultGoogleCloudKMS({
          enabled: true,
          valid: true, // Set to true so hasLegacyCredentials will be false (since roleId is set)
          serviceAccountKey: targetServiceAccount.gcpServiceAccountForAtlas,
          roleId: targetServiceAccount.roleId,
          keyVersionResourceID:
            'projects/my-project/locations/us-east4/keyRings/my-ring/cryptoKeys/my-key/cryptoKeyVersions/1', // Provide a valid key version resource ID
        }),
        enabled: true, // Enable the form
        gcpServiceAccounts: MOCK_GCP_SERVICE_ACCOUNTS,
      });

      // First, open the "Authentication Method" accordion to reveal the dropdown
      await openAuthenticationMethodAccordion();

      // Click the dropdown button to open the menu - the Select component IS the button
      const dropdownButton = screen.getByTestId(TestId.serviceAccountDropdown);
      await user.click(dropdownButton);

      // Should show the service account options in the dropdown menu
      const dropdownMenu = screen.getByRole('listbox');
      expect(
        within(dropdownMenu).getByText('<EMAIL>')
      ).toBeInTheDocument();
      expect(
        within(dropdownMenu).getByText('<EMAIL>')
      ).toBeInTheDocument();
      expect(
        within(dropdownMenu).getByText('<EMAIL>')
      ).toBeInTheDocument();
    });
  });

  describe('when creating new configuration', () => {
    it('should disable submit button when required fields are missing', () => {
      renderGoogleCloudKMSFormNew({
        googleCloudKms: INITIAL_GOOGLE_CLOUD_KMS,
        enabled: false,
      });

      const submitButton = screen.getByTestId('encryptionAtRestSave');
      expect(submitButton).toHaveAttribute('aria-disabled', 'true');
    });

    it('should disable submit button when using static credentials without consent', async () => {
      renderGoogleCloudKMSFormNew({
        googleCloudKms: getDefaultGoogleCloudKMS(),
        enabled: false,
      });

      // Fill in required fields but don't check consent
      const keyVersionInput = screen.getByTestId(TestId.keyVersionResourceId);
      await user.type(
        keyVersionInput,
        'projects/test/locations/us-east1/keyRings/test/cryptoKeys/test/cryptoKeyVersions/1'
      );

      // Switch to static credentials mode
      const authToggle = screen.getByTestId(TestId.gcpKMSAuthToggle);
      await user.click(authToggle);

      // Fill in service account key
      const serviceAccountKeyInput = screen.getByTestId(TestId.serviceAccountKeyTextInput);
      fireEvent.change(serviceAccountKeyInput, { target: { value: '{"type": "service_account"}' } });

      // Submit button should be disabled because consent is not checked
      const submitButton = screen.getByTestId('encryptionAtRestSave');
      expect(submitButton).toHaveAttribute('aria-disabled', 'true');
    });

    it('should enable submit button when using static credentials with consent', async () => {
      renderGoogleCloudKMSFormNew({
        googleCloudKms: getDefaultGoogleCloudKMS(),
        enabled: false,
      });

      // Fill in required fields
      const keyVersionInput = screen.getByTestId(TestId.keyVersionResourceId);
      await user.type(
        keyVersionInput,
        'projects/test/locations/us-east1/keyRings/test/cryptoKeys/test/cryptoKeyVersions/1'
      );

      // Switch to static credentials mode
      const authToggle = screen.getByTestId(TestId.gcpKMSAuthToggle);
      await user.click(authToggle);

      // Fill in service account key
      const serviceAccountKeyInput = screen.getByTestId(TestId.serviceAccountKeyTextInput);
      fireEvent.change(serviceAccountKeyInput, { target: { value: '{"type": "service_account"}' } });

      // Wait for consent checkbox to be enabled and then check it
      const consentCheckbox = screen.getByTestId(TestId.staticCredentialsConsentCheckbox);
      fireEvent.click(consentCheckbox);

      // Assert that checkbox is checked
      expect(consentCheckbox).toBeChecked();

      // Submit button should now be enabled
      const submitButton = screen.getByTestId('encryptionAtRestSave');
      expect(submitButton).toBeEnabled();
    });

    it('should enable submit button when using role-based authentication without consent requirement', async () => {
      renderGoogleCloudKMSFormNew({
        googleCloudKms: getDefaultGoogleCloudKMS(),
        enabled: false,
        gcpServiceAccounts: MOCK_GCP_SERVICE_ACCOUNTS,
      });

      // Fill in required fields
      const keyVersionInput = screen.getByTestId(TestId.keyVersionResourceId);
      await user.type(
        keyVersionInput,
        'projects/test/locations/us-east1/keyRings/test/cryptoKeys/test/cryptoKeyVersions/1'
      );

      // Authentication method accordion should already be open for new configurations
      const dropdownButton = screen.getByTestId(TestId.serviceAccountDropdown);
      await user.click(dropdownButton);

      const serviceAccountOption = screen.getByText('<EMAIL>');
      await user.click(serviceAccountOption);

      // Submit button should be enabled (no consent required for role-based auth)
      const submitButton = screen.getByTestId('encryptionAtRestSave');
      expect(submitButton).toBeEnabled();
    });
  });

  describe('when editing LEGACY existing configuration', () => {
    it('should enable submit button when service account key is modified', async () => {
      renderGoogleCloudKMSFormNew({
        googleCloudKms: SERVICE_ACCOUNT_KEY_GOOGLE_KMS,
        enabled: true,
      });

      // In edit mode with legacy credentials, the service account key field should already be visible
      // Let's check if it's available by test ID instead of label
      const serviceAccountKeyInput = screen.getByTestId(TestId.serviceAccountKeyTextInput);
      await user.clear(serviceAccountKeyInput);
      await user.type(serviceAccountKeyInput, 'modified-service-account-key');

      const submitButton = screen.getByTestId('encryptionAtRestSave');
      expect(submitButton).toBeEnabled();
    });

    it('should enable submit button when key version resource ID is modified', async () => {
      renderGoogleCloudKMSFormNew({
        googleCloudKms: SERVICE_ACCOUNT_KEY_GOOGLE_KMS,
        enabled: true,
      });

      // Modify the key version resource ID
      const keyVersionInput = screen.getByTestId(TestId.keyVersionResourceId);
      await user.clear(keyVersionInput);
      await user.type(
        keyVersionInput,
        'projects/test/locations/us-east1/keyRings/test/cryptoKeys/test/cryptoKeyVersions/2'
      );

      // Check consent checkbox
      const consentCheckbox = screen.getByTestId(TestId.staticCredentialsConsentCheckbox);
      fireEvent.click(consentCheckbox);

      const submitButton = screen.getByTestId('encryptionAtRestSave');
      expect(submitButton).toBeEnabled();
    });

    it('should enable submit button when consent is given for static credentials in edit mode', async () => {
      renderGoogleCloudKMSFormNew({
        googleCloudKms: SERVICE_ACCOUNT_KEY_GOOGLE_KMS,
        enabled: true,
      });

      // In edit mode with legacy credentials, we're already in static credentials mode
      // Modify the service account key to make a change
      const serviceAccountKeyInput = screen.getByTestId(TestId.serviceAccountKeyTextInput);
      await user.clear(serviceAccountKeyInput);
      await user.type(serviceAccountKeyInput, 'modified-service-account-key');

      // confirm submit button disabled
      const submitButtonNoConsent = screen.getByTestId('encryptionAtRestSave');
      expect(submitButtonNoConsent).toHaveAttribute('aria-disabled', 'true');

      // Check consent checkbox
      const consentCheckbox = screen.getByTestId(TestId.staticCredentialsConsentCheckbox);
      fireEvent.click(consentCheckbox);

      // confirm submit button enabled
      const submitButtonWithConsent = screen.getByTestId('encryptionAtRestSave');
      expect(submitButtonWithConsent).toBeEnabled();
    });
  });

  describe('when roleId has a value', () => {
    const googleCloudKmsWithRoleId: GoogleCloudKMS = {
      ...INITIAL_GOOGLE_CLOUD_KMS,
      roleId: 'test-role-id-123',
      enabled: true,
    };

    it('should hide the Banner component when roleId is present', () => {
      renderGoogleCloudKMSFormNew({
        googleCloudKms: googleCloudKmsWithRoleId,
        enabled: true,
      });

      // Banner should not be present
      expect(screen.queryByTestId('gcp-kms-banner')).not.toBeInTheDocument();
      expect(screen.queryByText('Improved Authentication Method')).not.toBeInTheDocument();
    });

    it('should hide the Toggle section when roleId is present', async () => {
      renderGoogleCloudKMSFormNew({
        googleCloudKms: googleCloudKmsWithRoleId,
        enabled: true,
      });

      await openAuthenticationMethodAccordion();

      // Toggle section should not be present
      expect(screen.queryByTestId('gcp-kms-toggle-section')).not.toBeInTheDocument();
      expect(screen.queryByTestId(TestId.gcpKMSAuthToggle)).not.toBeInTheDocument();
      expect(screen.queryByText('Authorize with static credentials')).not.toBeInTheDocument();
    });
  });
});
