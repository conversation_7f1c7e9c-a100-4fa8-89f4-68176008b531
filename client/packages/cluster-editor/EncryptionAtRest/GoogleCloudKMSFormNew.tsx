import { useState } from 'react';

import { css } from '@emotion/react';
import Badge, { Variant as BadgeVariant } from '@leafygreen-ui/badge';
import Banner, { Variant as BannerVariant } from '@leafygreen-ui/banner';
import Checkbox from '@leafygreen-ui/checkbox';
import { palette } from '@leafygreen-ui/palette';
import { Option, Select } from '@leafygreen-ui/select';
import TextArea from '@leafygreen-ui/text-area';
import Toggle, { Size as ToggleSize } from '@leafygreen-ui/toggle';
import { Link } from '@leafygreen-ui/typography';

import { CloudProviderAccessGCPServiceAccount } from '@packages/types/nds/cloudProviderAccess';
import { EncryptionAtRestProvider } from '@packages/types/nds/clusterDescription';
import { GoogleCloudKMS } from '@packages/types/nds/security/enterpriseSecurity';

import * as api from '@packages/common/services/api';
import { ATLAS_EAR_WITH_CMK_LINK } from '@packages/cluster-editor/EncryptionAtRest/constantsDocsLinks';
import EncryptionKeyInfo from '@packages/cluster-editor/EncryptionAtRest/EncryptionKeyInfo';
import EncryptionProviderFormSubmit from '@packages/cluster-editor/EncryptionAtRest/EncryptionProviderFormSubmit';
import TryAgainButton from '@packages/cluster-editor/EncryptionAtRest/TryAgainButton';
import EnterpriseSecurityInput from '@packages/cluster-editor/EnterpriseSecurity/EnterpriseSecurityInput';
import EncryptionAtRestType from '@packages/common/models/EncryptionAtRest';
import { exceptionToMessage } from '@packages/common/services/errorHelper';
import groupStatusService from '@packages/common/services/groupStatusService';
import Alert from '@packages/components/Alert';
import ButtonWithLoaderAndLink from '@packages/components/ButtonWithLoaderAndLink';
import InlineAccordion from '@packages/components/InlineAccordion';
import mongoDate from '@packages/date';

import GoogleCloudKMSConfigurationModal from './GoogleCloudKMSConfigurationModal';
import { isValidGcpKeyVersionResourceID } from './utils';

// docs links
const SERVICE_ACCOUNT_LINK = 'https://cloud.google.com/docs/authentication/getting-started';
const CREATE_KEY_LINK = 'https://cloud.google.com/kms/docs/creating-keys';

export enum TestId {
  serviceAccountDropdown = 'service-account-dropdown',
  serviceAccountKeyTextInput = 'service-account-key-input',
  keyVersionResourceId = 'key-version-resource-id-input',
  gcpKMSAuthToggle = 'gcp-kms-auth-toggle',
  staticCredentialsConsentCheckbox = 'static-credentials-consent-checkbox',
}

const styles = {
  authorizeButton: css({
    marginTop: 5,
  }),
  deprecationDescriptionText: css({
    color: palette.gray.dark1,
    fontSize: '1.4rem',
    marginLeft: '4rem',
  }),
  staticCredsToggleLabel: css({
    fontSize: '1.4rem',
  }),
  staticCredsToggleContainer: css`
    display: flex;
    align-items: center;
    gap: 1rem;
  `,
  disabledLink: css({
    color: palette.gray.base,
    pointerEvents: 'none',
  }),
  disabledContainer: css({
    opacity: 0.5,
  }),
  serviceAccountSelectContainer: css`
    margin-top: 2rem;
    margin-bottom: 2rem;
  `,
  authorizationMethodDetails: css({
    fontSize: '1.4rem',
    marginBottom: '2rem',
  }),
  staticCredsToggleSection: css`
    margin-bottom: 2rem;
    margin-top: 2rem;
  `,
  serviceAccountTextArea: css({
    '& > div > label': {
      marginBottom: 0,
    },
  }),
  staticConsentCheckboxContainer: css({
    marginTop: '1rem',
  }),
  marginLeft: css`
    margin-left: 26px;
  `,
};

// Helper component for service account selection
interface ServiceAccountSelectorProps {
  roleId: string;
  serviceAccounts: Array<CloudProviderAccessGCPServiceAccount>;
  disabled: boolean;
  onRoleIdChange: (value: string) => void;
  onAuthorizeNewClick: () => void;
}

function ServiceAccountSelector({
  roleId,
  serviceAccounts,
  disabled,
  onRoleIdChange,
  onAuthorizeNewClick,
}: ServiceAccountSelectorProps) {
  return (
    <div css={disabled && styles.disabledContainer}>
      <Select
        value={roleId}
        label="Connect Atlas Service Account"
        description="Allow Atlas to encrypt and decrypt your data"
        placeholder="Choose an existing service account"
        disabled={disabled}
        onChange={onRoleIdChange}
        data-testid={TestId.serviceAccountDropdown}
      >
        {serviceAccounts.map(({ roleId: rId, gcpServiceAccountForAtlas = '' }) => (
          <Option value={rId} key={rId}>
            {gcpServiceAccountForAtlas}
          </Option>
        ))}
      </Select>
      <div css={styles.authorizeButton}>
        <ButtonWithLoaderAndLink
          isLink
          name="authorizeNewServiceAccountButton"
          onClick={onAuthorizeNewClick}
          css={disabled && styles.disabledLink}
          disabled={disabled}
        >
          Authorize a new service account
        </ButtonWithLoaderAndLink>
      </div>
    </div>
  );
}

export interface Props {
  setParentComponentState: (inputs: $TSFixMe) => void;
  cancel: () => void;
  googleCloudKms: GoogleCloudKMS;
  disabled?: boolean;
  isGroupOwner: boolean;
  enabled: boolean;
  groupId: string;
  serviceAccountKeyPlaceholder: string;
  disableEncryptionAtRest: $TSFixMeFunction;
  requestInProgress?: boolean;
  encryptedClusters: Array<string>;
  enabledForSearchNodes: boolean;
  encryptionAtRest: typeof EncryptionAtRestType;
  gcpServiceAccounts: Array<CloudProviderAccessGCPServiceAccount>;
}

enum AuthenticationType {
  ServiceAccountKey,
  RoleBased,
}

// Helper functions for submission logic
function areFieldsValid({
  tempServiceAccountKey,
  tempKeyVersionResourceID,
  tempRoleId,
  authenticationType,
}: {
  tempServiceAccountKey: string;
  tempKeyVersionResourceID: string;
  tempRoleId: string;
  authenticationType: AuthenticationType;
}): boolean {
  // Key version resource ID is always required
  if (!tempKeyVersionResourceID.trim()) {
    return false;
  }

  // Check authentication-specific requirements
  if (authenticationType === AuthenticationType.ServiceAccountKey) {
    // Static credentials require service account key
    return tempServiceAccountKey.trim() !== '';
  } else {
    // Role-based authentication requires role ID
    return tempRoleId.trim() !== '';
  }
}

function hasNoChangesFromDefaults({
  tempServiceAccountKey,
  serviceAccountKey,
  tempKeyVersionResourceID,
  keyVersionResourceID,
  tempRoleId,
  roleId,
}: {
  tempServiceAccountKey: string;
  serviceAccountKey: string;
  tempKeyVersionResourceID: string;
  keyVersionResourceID: string;
  tempRoleId: string;
  roleId: string;
}): boolean {
  return (
    tempServiceAccountKey === serviceAccountKey &&
    tempKeyVersionResourceID === keyVersionResourceID &&
    tempRoleId === roleId
  );
}

function isUsingStaticCredentialsWithoutConsent({
  authenticationType,
  consentToStaticCreds,
}: {
  authenticationType: AuthenticationType;
  consentToStaticCreds: boolean;
}): boolean {
  return authenticationType === AuthenticationType.ServiceAccountKey && !consentToStaticCreds;
}

// Main helper function to determine if form submission should be disabled
function shouldDisableSubmission({
  tempServiceAccountKey,
  serviceAccountKey,
  tempKeyVersionResourceID,
  keyVersionResourceID,
  tempRoleId,
  roleId,
  authenticationType,
  consentToStaticCreds,
}: {
  tempServiceAccountKey: string;
  serviceAccountKey: string;
  tempKeyVersionResourceID: string;
  keyVersionResourceID: string;
  tempRoleId: string;
  roleId: string;
  serviceAccountKeyPlaceholder: string;
  authenticationType: AuthenticationType;
  consentToStaticCreds: boolean;
}): boolean {
  // Always check consent for static credentials (both edit and new modes)
  const staticCredsWithoutConsent = isUsingStaticCredentialsWithoutConsent({
    authenticationType,
    consentToStaticCreds,
  });

  if (staticCredsWithoutConsent) {
    return true; // Disable submission if using static creds without consent
  }

  // Check if all required fields are valid
  const fieldsValid = areFieldsValid({
    tempServiceAccountKey,
    tempKeyVersionResourceID,
    tempRoleId,
    authenticationType,
  });

  if (!fieldsValid) {
    return true; // Disable submission if fields are invalid
  }

  // For new configuration mode: disable if no changes from defaults
  return hasNoChangesFromDefaults({
    tempServiceAccountKey,
    serviceAccountKey,
    tempKeyVersionResourceID,
    keyVersionResourceID,
    tempRoleId,
    roleId,
  });
}
enum Modal {
  NONE,
  SERVICE_ACCOUNT,
}
interface ModalState {
  activeModal: Modal;
  roleId: string;
}
const INITIAL_MODAL_STATE: ModalState = {
  activeModal: Modal.NONE,
  roleId: '',
};

function GoogleCloudKMSFormNew(props: Props) {
  const {
    setParentComponentState,
    cancel,
    googleCloudKms,
    disabled = false,
    isGroupOwner,
    enabled: parentEnabled,
    groupId,
    serviceAccountKeyPlaceholder,
    disableEncryptionAtRest,
    requestInProgress = false,
    encryptedClusters,
    enabledForSearchNodes,
    encryptionAtRest,
    gcpServiceAccounts = [],
  } = props;

  const {
    lastUpdatedKeyID = '',
    enabled: isEdit,
    serviceAccountKey,
    roleId,
    keyVersionResourceID,
    valid,
  } = googleCloudKms;
  const [modalState, setModalState] = useState<ModalState>(INITIAL_MODAL_STATE);
  const { roleId: modalRoleId } = modalState;

  // GOOGLE KMS
  const [tempServiceAccountKey, setTempServiceAccountKey] = useState(
    googleCloudKms.enabled ? serviceAccountKeyPlaceholder : serviceAccountKey || ''
  );
  const [tempKeyVersionResourceID, setTempKeyVersionResourceID] = useState(keyVersionResourceID || '');
  const [tempRoleId, setTempRoleId] = useState(roleId || '');

  // FORM
  const [consentToStaticCreds, setConsentToStaticCreds] = useState(false);
  const [missingFields, setMissingFields] = useState<any>({});
  const hasLegacyCredentials = !roleId && googleCloudKms.enabled && valid;
  const [authenticationType, setAuthenticationType] = useState<AuthenticationType>(
    hasLegacyCredentials ? AuthenticationType.ServiceAccountKey : AuthenticationType.RoleBased
  );

  const saveEncryptionAtRest = async () => {
    setMissingFields({});
    setParentComponentState({
      requestInProgress: true,
      errorMessage: undefined,
    });

    // check if settings is legacy credentials (serviceAccountKey) or new flow (roleId)
    const isLegacyConfiguration = !tempRoleId;
    const serviceAccountKeyChanged = tempServiceAccountKey !== serviceAccountKeyPlaceholder;
    const includeServiceAccountKey = serviceAccountKeyChanged || !isEdit;
    const serviceAccountKeySanitized = includeServiceAccountKey ? tempServiceAccountKey : '';

    const legacyPatch =
      (isLegacyConfiguration && {
        ...(includeServiceAccountKey && { serviceAccountKey: serviceAccountKeySanitized }),
      }) ||
      {};
    const rolePatch = (!isLegacyConfiguration && tempRoleId && { roleId: tempRoleId }) || {};
    const googleCloudKmsLocal = {
      enabled: parentEnabled,
      keyVersionResourceID: tempKeyVersionResourceID,
      ...rolePatch,
      ...legacyPatch,
    };

    const missingFieldsLocal = isLegacyConfiguration
      ? {
          keyVersionResourceID: !tempKeyVersionResourceID,
          serviceAccountKey: !tempServiceAccountKey,
        }
      : {
          keyVersionResourceID: !tempKeyVersionResourceID,
          roleId: !tempRoleId,
        };

    if (parentEnabled && Object.values(missingFieldsLocal).some((val) => val)) {
      setMissingFields(missingFieldsLocal);
      setParentComponentState({
        errorMessage: 'Please fill out the missing required fields.',
        requestInProgress: false,
      });
      return;
    }

    try {
      const updatedEncryptionAtRest = await api.nds.encryptionAtRest.patchEncryptionAtRest(groupId, {
        googleCloudKms: googleCloudKmsLocal,
        ...(enabledForSearchNodes !== undefined && { enabledForSearchNodes }),
      });
      const updatedGoogleCloudKMS = updatedEncryptionAtRest.googleCloudKms;
      if (updatedGoogleCloudKMS) {
        updatedGoogleCloudKMS.serviceAccountKey = serviceAccountKeyPlaceholder;

        setParentComponentState({
          alertBannerEncryptionProvider: !isEdit && updatedGoogleCloudKMS.enabled && 'GCP',
          lastSavedGoogleCloudKms: { ...updatedGoogleCloudKMS },
          displayEditButton: parentEnabled,
          errorMessage: undefined,
          requestInProgress: false,
        });

        encryptionAtRest.setGoogleCloudKms({ ...updatedGoogleCloudKMS });
        encryptionAtRest.setEnabledForSearchNodes(enabledForSearchNodes);
        groupStatusService.enableFastPolling();
      }
    } catch (error) {
      setParentComponentState({
        errorMessage: exceptionToMessage(error, {
          INTERNAL:
            'We were unable to connect to your Google Cloud KMS account. Please check your credentials and try again.',
        }),
        requestInProgress: false,
      });
    }
  };

  const revalidateEnabled = () => {
    const serviceAccountKeyMatch = (tempServiceAccountKey || '') === (googleCloudKms.serviceAccountKey || '');
    const roleIdMatch = (tempRoleId || '') === (googleCloudKms.roleId || '');
    return serviceAccountKeyMatch || roleIdMatch;
  };

  const daysSinceLastRotation = lastUpdatedKeyID ? mongoDate().diff(lastUpdatedKeyID, 'days') : null;
  const activeWarning = isEdit && daysSinceLastRotation !== null ? `Configured ${daysSinceLastRotation} days ago` : '';

  const disableSubmitButton = shouldDisableSubmission({
    tempServiceAccountKey,
    serviceAccountKey: serviceAccountKey || '',
    tempKeyVersionResourceID,
    keyVersionResourceID: keyVersionResourceID || '',
    tempRoleId,
    roleId: roleId || '',
    serviceAccountKeyPlaceholder,
    authenticationType,
    consentToStaticCreds,
  });

  const googleCloudKmsInvalid = googleCloudKms.enabled && !googleCloudKms.valid;
  const googleCloudKmsNetworkAccessDenied = googleCloudKms.enabled && !!googleCloudKms.networkAccessDeniedDate;
  const selectedServiceAccountEmail =
    gcpServiceAccounts.find((sa) => sa.roleId === tempRoleId)?.gcpServiceAccountForAtlas || '';

  // Disable service account section if keyVersionResourceID is invalid
  const disableServiceAccountControls = !isValidGcpKeyVersionResourceID(tempKeyVersionResourceID);

  return (
    <div className="nds-enterprise-security-form">
      {isEdit && (
        <EncryptionKeyInfo
          encryptionProvider={EncryptionAtRestProvider.GCP}
          encryptedClusters={encryptedClusters}
          daysSinceLastRotation={daysSinceLastRotation as number}
        />
      )}
      <InlineAccordion
        headlineText="Encryption Key Credentials"
        accordionClasses="nds-enterprise-security-form-accordion"
        initialActive
        isCompact
        hideSelectedBadgeWhenZero
      >
        <div css={styles.marginLeft}>
          {googleCloudKmsNetworkAccessDenied && (
            <Alert level="danger" className="nds-enterprise-security-form-ear-form-child">
              The Atlas control plane cannot access Google Cloud KMS. Please update your Google Cloud KMS key access
              list to include Atlas control plane IP addresses. <Link href={ATLAS_EAR_WITH_CMK_LINK}>Learn more</Link>
            </Alert>
          )}
          {googleCloudKmsInvalid && (
            <Alert level="danger" className="nds-enterprise-security-form-ear-form-child">
              Google Cloud KMS Encryption At Rest settings are no longer valid. All MongoDB processes that have not
              already been stopped will be stopped imminently.
            </Alert>
          )}
          <p className="nds-enterprise-security-form-input-description nds-enterprise-security-form-ear-form-child">
            {isEdit ? (
              <span>
                Generate a new version of your key and enter its Key Version Resource ID to rotate each cluster&apos;s
                Wired Tiger&trade; Storage Engine encryption key.{' '}
                <strong>
                  Please keep your old key version active until all keys have been successfully rotated; paused clusters
                  will be encrypted with your old key version until they are resumed.
                </strong>
              </span>
            ) : (
              <span>
                Create a key, and provide its Key Version Resource ID. Read how to{' '}
                <Link href={CREATE_KEY_LINK} target="_blank" rel="noopener noreferrer">
                  create a key
                </Link>
                .
              </span>
            )}
          </p>
          <div className="nds-enterprise-security-form-ear-form-child">
            <EnterpriseSecurityInput
              title="Key Version Resource ID"
              warning={activeWarning}
              placeholder="e.g. projects/my-207221/locations/us-east4/keyRings/cryptoKeys/key/cryptoKeyVersions/1"
              value={tempKeyVersionResourceID}
              onChange={setTempKeyVersionResourceID}
              disabled={googleCloudKmsInvalid || disabled}
              validationFail={missingFields.keyVersionResourceID}
              retryButton={
                googleCloudKmsInvalid && revalidateEnabled() && <TryAgainButton onClick={saveEncryptionAtRest} />
              }
              data-testid={TestId.keyVersionResourceId}
            />
          </div>
        </div>
      </InlineAccordion>
      <hr className="nds-enterprise-security-form-divider" />
      <InlineAccordion
        headlineText="Authentication Method"
        accordionClasses="nds-enterprise-security-form-accordion"
        initialActive={!isEdit || hasLegacyCredentials}
        isCompact
        hideSelectedBadgeWhenZero
      >
        <div css={styles.marginLeft}>
          <div>
            <>
              {!roleId && (
                <Banner variant={BannerVariant.Info} data-testid="gcp-kms-banner">
                  <b>Improved Authentication Method.</b> Use an Atlas service account for stronger security － once a
                  service account is in use, static credentials can no longer be used.
                </Banner>
              )}
              <div css={styles.serviceAccountSelectContainer}>
                <ServiceAccountSelector
                  roleId={tempRoleId}
                  serviceAccounts={gcpServiceAccounts}
                  disabled={authenticationType !== AuthenticationType.RoleBased || disableServiceAccountControls}
                  onRoleIdChange={setTempRoleId}
                  onAuthorizeNewClick={() => {
                    setModalState({
                      roleId: '',
                      activeModal: Modal.SERVICE_ACCOUNT,
                    });
                  }}
                />
              </div>
            </>
          </div>

          {/* AUTHENTICATION METHOD SECRETLESS OR ROLE BASED */}
          {!roleId && (
            <div css={styles.staticCredsToggleSection} data-testid="gcp-kms-toggle-section">
              <div css={styles.staticCredsToggleContainer}>
                <Toggle
                  checked={authenticationType === AuthenticationType.ServiceAccountKey}
                  onChange={(checked) => {
                    setAuthenticationType(
                      checked ? AuthenticationType.ServiceAccountKey : AuthenticationType.RoleBased
                    );
                    setConsentToStaticCreds(false);
                  }}
                  disabled={false}
                  data-testid={TestId.gcpKMSAuthToggle}
                  size={ToggleSize.XSmall}
                  aria-label="Toggle between service account and legacy key authentication"
                />
                <span css={styles.staticCredsToggleLabel}>Authorize with static credentials</span>
                <Badge variant={BadgeVariant.Yellow}>DEPRECATING</Badge>
              </div>
              <div css={styles.deprecationDescriptionText}>
                This method presents weaker security and will be deprecated.
              </div>
            </div>
          )}

          {/* STATIC CREDENTIALS */}
          {authenticationType === AuthenticationType.ServiceAccountKey && (
            <>
              <TextArea
                css={styles.serviceAccountTextArea}
                rows={8}
                label="Service Account Key"
                description={
                  <>
                    Provide credentials for your Google Cloud service account key. Read how to create a service account
                    and create a{' '}
                    <Link href={SERVICE_ACCOUNT_LINK} target="_blank" rel="noopener noreferrer">
                      service account key
                    </Link>
                    .
                  </>
                }
                placeholder="Paste your Service Account Key here"
                value={tempServiceAccountKey}
                onChange={({ target: { value } }) => setTempServiceAccountKey(value)}
                disabled={disabled}
                data-testid={TestId.serviceAccountKeyTextInput}
              />
              <div css={styles.staticConsentCheckboxContainer}>
                <Checkbox
                  checked={consentToStaticCreds}
                  onChange={({ target: { checked } }) => setConsentToStaticCreds(checked)}
                  label="I acknowledge that I am choosing a weak security method."
                  data-testid={TestId.staticCredentialsConsentCheckbox}
                />
              </div>
            </>
          )}
        </div>
      </InlineAccordion>
      <hr className="nds-enterprise-security-form-divider" />
      <div css={styles.marginLeft}>
        <EncryptionProviderFormSubmit
          showSubmitButtons={isGroupOwner}
          encryptionProviderName="Google Cloud KMS"
          encryptedClusters={encryptedClusters}
          isEdit={isEdit}
          disabled={disabled}
          encryptionProviderInvalid={googleCloudKmsInvalid}
          disableSubmitButton={disableSubmitButton}
          requestInProgress={requestInProgress}
          saveEncryptionAtRest={saveEncryptionAtRest}
          disableEncryptionAtRest={() => disableEncryptionAtRest({ googleCloudKms: { enabled: false } })}
          cancel={cancel}
        />
      </div>

      <GoogleCloudKMSConfigurationModal
        open={modalState.activeModal === Modal.SERVICE_ACCOUNT}
        onClose={() => setModalState({ ...modalState, activeModal: Modal.NONE })}
        keyVersionResourceID={tempKeyVersionResourceID}
        serviceAccountEmail={selectedServiceAccountEmail}
        isProjectSetupRequired={false}
        selectedRoleId={modalRoleId}
        serviceAccounts={gcpServiceAccounts}
        onServiceAccountChange={(r) => setModalState({ ...modalState, roleId: r })}
        groupId={groupId}
        enabledForSearchNodes={enabledForSearchNodes}
        googleCloudKms={googleCloudKms}
        // close both modal and parent form on finish
        onFinish={() => {
          setModalState(INITIAL_MODAL_STATE);
          if (modalRoleId) setTempRoleId(modalRoleId);
          cancel(); // close the parent form as well
        }}
      />
    </div>
  );
}

export default GoogleCloudKMSFormNew;
