/** AUTO GENERATED FILE. DO NOT EDIT! */
/* eslint-disable */
// @ts-nocheck

const obj__0 = {"<field_name>": [{"type": "field_type",
},
],
};
const obj__1 = {"^.*$": {"required": ["type",
],
"oneOf": [null,
{"type": "array",
"items": null,
},
],
},
};
const obj__2 = {"enum": ["lucene.standard",
"lucene.simple",
"lucene.whitespace",
"lucene.keyword",
"lucene.arabic",
"lucene.armenian",
"lucene.basque",
"lucene.bengali",
"lucene.brazilian",
"lucene.bulgarian",
"lucene.catalan",
"lucene.chinese",
"lucene.czech",
"lucene.danish",
"lucene.dutch",
"lucene.english",
"lucene.finnish",
"lucene.french",
"lucene.galician",
"lucene.german",
"lucene.greek",
"lucene.hindi",
"lucene.hungarian",
"lucene.indonesian",
"lucene.irish",
"lucene.italian",
"lucene.japanese",
"lucene.korean",
"lucene.latvian",
"lucene.lithuanian",
"lucene.morfologik",
"lucene.nori",
"lucene.norwegian",
"lucene.persian",
"lucene.polish",
"lucene.portuguese",
"lucene.romanian",
"lucene.russian",
"lucene.smartcn",
"lucene.sorani",
"lucene.spanish",
"lucene.swedish",
"lucene.thai",
"lucene.turkish",
"lucene.ukrainian",
],
};
const obj__3 = {"type": "string",
"minLength": 1,
};
const obj__4 = {"anyOf": [null,
{"enum": ["lucene.kuromoji",
"lucene.cjk",
],
},
],
};
const obj__5 = {"type": "bm25",
};
const obj__6 = {"type": "boolean",
};
const obj__7 = {"type": "stableTfl",
};
const obj__8 = {"type": {"oneOf": [{"const": "bm25",
},
{"const": "boolean",
},
{"const": "stableTfl",
},
],
"default": "bm25",
"type": "string",
},
};
const obj__9 = {"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/stringComponents/baseString.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields with string values. To learn more about this data type, see [string Type](https://dochub.mongodb.org/core/string-type-fts).",
"description": "Data type to use to index fields with string values.",
"const": "string",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"analyzer": {"suggestSortText": "1",
"markdownDescription": "Label that identifies the built-in or custom [analyzer](https://dochub.mongodb.org/core/analyzers-fts) to use for indexing the field. If you omit this parameter, it defaults to an analyzer in the following order:\n1. The `analyzer` option for the [index](https://dochub.mongodb.org/core/index-definition-fts) if specified.\n2. The `lucene.standard` analyzer.",
"description": "Label that identifies the analyzer to use for indexing this field.",
"anyOf": [null,
null,
],
"default": "lucene.standard",
},
"searchAnalyzer": {"suggestSortText": "2",
"markdownDescription": "Label that identifies the analyzer to use when querying the field. If you omit this parameter, it defaults to an analyzer in the following order:\n1. The `analyzer` option for this field if specified.\n2. The `searchAnalyzer` option for the [index](https://www.mongodb.com/docs/atlas/atlas-search/index-definitions) if specified.\n3. The `analyzer` option for the [index](https://dochub.mongodb.org/core/index-definition-fts) if specified.\n4. The `lucene.standard` analyzer.",
"description": "Label that identifies the analyzer to use when querying this field.",
"anyOf": [null,
null,
],
"default": "lucene.standard",
},
"indexOptions": {"suggestSortText": "3",
"type": "string",
"markdownDescription": "Amount of information to store for the indexed field. Values include:\n- `docs` - Only indexes documents. Only a single occurrence of the term is reflected in the [score](https://dochub.mongodb.org/core/scoring-fts).\n- `freqs` - Indexes documents and term frequency.\n- `positions` - Indexes documents, term frequency, and term positions.\n- `offsets` - (_Default_) Indexes documents, term frequency, term positions, and term offsets.\n\nThis option is required for [highlighting results](https://dochub.mongodb.org/core/highlighting-fts).",
"description": "Amount of information to store for the indexed field.",
"oneOf": [{"const": "docs",
},
{"const": "freqs",
},
{"const": "positions",
},
{"const": "offsets",
},
],
"default": "offsets",
},
"store": {"suggestSortText": "4",
"type": "boolean",
"markdownDescription": "Boolean flag that indicates whether to store the exact document text as well as the analyzed values in the index. To reduce the index size and performance footprint, we recommend that you set store to `false`. To learn more, see [Atlas Search Index Performance](https://dochub.mongodb.org/core/performance-fts). You must set this option to `true` if you want to [highlight your results](https://dochub.mongodb.org/core/highlighting-fts).",
"description": "Boolean flag that indicates whether to store the exact document text as well as the analyzed values in the index",
"default": true,
},
"ignoreAbove": {"suggestSortText": "5",
"type": "integer",
"markdownDescription": "The maximum number of characters in the value of the field to index. Atlas Search doesn't index the field if its length exceeds the specified number of characters.",
"description": "Maximum number of characters in the value of the field to index.",
"minimum": 1,
},
"norms": {"suggestSortText": "7",
"type": "string",
"markdownDescription": "String that specifies whether to `include` or `omit` the field length in the [scoring results](https://dochub.mongodb.org/core/scoring-fts). The length of the field is determined by the number of tokens produced by the analyzer for the field.",
"description": "String that specifies whether to include or omit the field length in result when scoring",
"oneOf": [{"const": "include",
},
{"const": "omit",
},
],
"default": "include",
},
"similarity": {"suggestSortText": "8",
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/stringComponents/similarity.json",
"markdownDescription": "Similarity function to use to score search results.",
"description": "Similarity function to use to score search results.",
"type": "object",
"examples": [null,
null,
null,
],
"properties": null,
"required": ["type",
],
"additionalProperties": false,
},
},
"required": ["type",
],
};
const obj__10 = {"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/field.json",
"type": "object",
"properties": {"type": {"type": "string",
"markdownDescription": "[Data type](https://www.mongodb.com/docs/atlas/atlas-search/define-field-mappings/#data-types) to use for static mapping of the field during indexing.",
"enum": ["autocomplete",
"boolean",
"date",
"dateFacet",
"document",
"embeddedDocuments",
"geo",
"knnVector",
"number",
"numberFacet",
"objectId",
"string",
"stringFacet",
"token",
"uuid",
],
"markdownEnumDescriptions": ["Data type to use to index string type fields for autocompletion, including any string fields inside arrays and embedded documents. You can use this field type only for [static mappings](https://dochub.mongodb.org/core/static-and-dynamic-mappings-fts). To learn more about this data type, see [autocomplete Type](https://dochub.mongodb.org/core/autocomplete-type-fts).",
"Data type to use to index fields with `true` and `false` values. To learn more about this data type, see [boolean Type](https://dochub.mongodb.org/core/boolean-type-fts).",
"Data type to use to index fields with date values, including date fields inside arrays and embedded documents. To learn more about this data type, see [date Type](https://dochub.mongodb.org/core/date-type-fts).",
"Data type is outdated. Use `Date` instead. To learn more about this data type, see [date Type](https://dochub.mongodb.org/core/date-type-fts).",
"Data type to use to index fields that contain a document or an object. To learn more about this data type, see [document Type](https://dochub.mongodb.org/core/document-type-fts).",
"Data type to use to index fields in documents and objects that are elements of an array. To learn more about this data type, see [embeddedDocuments Type](https://dochub.mongodb.org/core/embedded-documents-type-fts).",
"Data type to use to index fields with [GeoJSON](https://dochub.mongodb.org/core/geojson-type-fts) objects, which includes geographic points and shape coordinates. To learn more about this data type, see [geo Type](https://dochub.mongodb.org/core/geo-type-fts).",
"Data type to use to index fields of type number array for vector search. To learn more, see [knnVector Type](https://www.mongodb.com/docs/atlas/atlas-search/field-types/knn-vector/).",
"Data type to use to index fields with numeric values of type `int32`, `int64`, and `double`. To learn more about this data type, see [number Type](https://dochub.mongodb.org/core/number-type-fts).",
"Data type is outdated. Use `Number` instead. To learn more about this data type, see [number Type](https://dochub.mongodb.org/core/number-type-fts).",
"Data type to use to index fields with [ObjectId](https://dochub.mongodb.org/core/objectid-bson-fts) values, including ObjectId fields inside arrays and embedded documents. To learn more about this data type, see [objectId Type](https://dochub.mongodb.org/core/objectid-type-fts).",
"Data type to use to index fields with string values. To learn more about this data type, see [string Type](https://dochub.mongodb.org/core/string-type-fts).",
"Data type is outdated. Use `Token` instead. To learn more about this data type, see [token Type](https://dochub.mongodb.org/core/token-type-fts).",
"Data type to use to index fields of type `string` for sorting or filtering search results with equals, in or range. To learn more about this data type, see [token Type](https://dochub.mongodb.org/core/token-type-fts).",
"Data type to use to index fields with `uuid` values, including UUID fields inside arrays and embedded documents. To learn more about this data type, see [uuid Type](https://dochub.mongodb.org/core/uuid-type-fts).",
],
},
},
"required": ["type",
],
"oneOf": [{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/autocomplete.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index string type fields for autocompletion, including any string fields inside arrays and embedded documents. You can use this field type only for [static mappings](https://dochub.mongodb.org/core/static-and-dynamic-mappings-fts). To learn more about this data type, see [autocomplete Type](https://dochub.mongodb.org/core/autocomplete-type-fts).",
"description": "Data type to use to index fields with string values for autocompletion.",
"const": "autocomplete",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"analyzer": {"suggestSortText": "1",
"markdownDescription": "Label that identifies the [analyzer](https://dochub.mongodb.org/core/analyzers-fts) to use with the autocomplete mapping. You can use any Atlas Search analyzer except the `lucene.kuromoji` [language analyzer](https://dochub.mongodb.org/core/language-fts) and the following [custom analyzer](https://dochub.mongodb.org/core/custom-fts) tokenizers and [token filters](https://dochub.mongodb.org/core/token-filters-fts):\n\n- ``ngram`` Tokenizer\n- ``edgeGram`` Tokenizer\n- `daitchMokotoffSoundex` Token Filter\n- `nGram` Token Filter\n- `edgeGram` Token Filter\n- `shingle` Token Filter",
"description": "Label that identifies the analyzer to use with the autocomplete mapping.",
"anyOf": [null,
null,
],
"not": {"enum": ["lucene.kuromoji",
],
},
"default": "lucene.standard",
},
"minGrams": {"suggestSortText": "2",
"type": "integer",
"markdownDescription": "The minimum number of characters per indexed sequence. We recommend a value of `4`. Any value less than `4` can impact performance as the size of the index can become very large. We recommend the default value of `2` for `edgeGram` only.",
"description": "Minimum number of characters to index per sequence.",
"default": 2,
},
"maxGrams": {"suggestSortText": "3",
"type": "integer",
"markdownDescription": "Maximum number of characters per indexed sequence. Atlas Search limits the character length of indexed tokens based on this value.",
"description": "Maximum number of characters to index per sequence.",
"default": 15,
},
"tokenization": {"suggestSortText": "4",
"type": "string",
"markdownDescription": "[Tokenization strategy](https://dochub.mongodb.org/core/tokenizers-fts) to use when indexing the field for autocompletion. \nValues include:\n- `edgeGram` - create indexable tokens, referred to as grams, from variable-length character sequences starting at the left side of the words as delimited by the analyzer used with this autocomplete mapping.\n- `rightEdgeGram` - create indexable tokens, referred to as grams, from variable-length character sequences starting at the right side of the words as delimited by the analyzer used with this autocomplete mapping.\n- `nGram` - create indexable tokens, referred to as grams, by sliding a variable-length character window over a word. Atlas Search creates more tokens for `nGram` than `edgeGram` or `rightEdgeGram`. Therefore, `nGram` takes more space and time to index the field. `nGram` is better suited for querying languages with long, compound words or languages that don't use spaces.\n\nAtlas Search applies `edgeGram`, `rightEdgeGram`, and `nGram` at the letter-level.",
"description": "Tokenization strategy to use when indexing the field for autocompletion",
"oneOf": [{"const": "edgeGram",
},
{"const": "rightEdgeGram",
},
{"const": "nGram",
},
],
"default": "edgeGram",
},
"foldDiacritics": {"suggestSortText": "5",
"type": "boolean",
"markdownDescription": "Boolean flag that indicates whether to include or remove diacritics from the indexed text. \nValues include:\n\n- `true` - ignore diacritic marks in the index and query text. Returns results with and without diacritic marks. For example, a search for `cafè` returns results with the characters `cafè` and `cafe`.\n- `false` - include diacritic marks in the index and query text. Returns only results that match the strings with or without diacritics in the query. For example, a search for `cafè` returns results only with the characters `cafè`. A search for `cafe` returns results only with the characters `cafe`.",
"description": "Boolean flag that indicates whether to include or remove diacritics from the indexed text.",
"default": true,
},
"similarity": {"suggestSortText": "6",
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/stringComponents/similarity.json",
"markdownDescription": "Similarity function to use to score search results.",
"description": "Similarity function to use to score search results.",
"type": "object",
"examples": [null,
null,
null,
],
"properties": null,
"required": ["type",
],
"additionalProperties": false,
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/boolean.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields with `true` and `false` values. To learn more about this data type, see [boolean Type](https://dochub.mongodb.org/core/boolean-type-fts).",
"description": "Data type to use to index fields with true and false values.",
"const": "boolean",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/date.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields with date values, including date fields inside arrays and embedded documents. To learn more about this data type, see [date Type](https://dochub.mongodb.org/core/date-type-fts).",
"description": "Data type to use to index fields with date values.",
"const": "date",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/dateFacet.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index date values for [faceting](https://dochub.mongodb.org/core/faceting-fts). You can run only facet queries against fields indexed as the `dateFacet` type. To perform a normal text search on the same field, you must also index the field as type `date`. To learn more about this data type, see [dateFacet Type](https://dochub.mongodb.org/core/date-facet-type-fts).",
"description": "Data type to use to index fields with date values for faceting.",
"const": "dateFacet",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/document.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields that contain a document or an object. To learn more about this data type, see [document Type](https://dochub.mongodb.org/core/document-type-fts).",
"description": "Data type to use to index fields that contain either a document or an object.",
"const": "document",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"dynamic": {"suggestSortText": "1",
"markdownDescription": "Boolean flag that indicates whether to index every [dynamically indexable](https://dochub.mongodb.org/core/data-types-fts) field in the document. If you omit or set this parameter to `false`, Atlas Search doesn't index any field and you can define the fields to index in the field definition.",
"description": "Boolean flag that indicates whether to index every dynamically indexable field in the document.",
"default": false,
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/dynamic.json",
"type": "boolean",
},
"fields": {"suggestSortText": "2",
"markdownDescription": "Document that maps field names to field definitions. To learn more, see [an example](https://dochub.mongodb.org/core/mapping-example-fts).",
"description": "Document that maps field names to field definitions.",
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields.json",
"type": "object",
"examples": [null,
],
"patternProperties": null,
"additionalProperties": false,
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/embeddedDocuments.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields in documents and objects that are elements of an array. To learn more about this data type, see [embeddedDocuments Type](https://dochub.mongodb.org/core/embedded-documents-type-fts).",
"description": "Data type to use to index fields in document or objects that are elements of an array.",
"const": "embeddedDocuments",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"dynamic": {"suggestSortText": "1",
"markdownDescription": "Boolean flag that indicates whether to index every [dynamically indexable](https://dochub.mongodb.org/core/data-types-fts) field in the document. If you omit or set this parameter to `false`, Atlas Search doesn't index any field and you can define the fields to index in the field definition.",
"description": "Boolean flag that indicates whether to index every dynamically indexable field in the document.",
"default": false,
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/dynamic.json",
"type": "boolean",
},
"fields": {"suggestSortText": "2",
"markdownDescription": "Document that maps field names to field definitions. To learn more, see [an example](https://dochub.mongodb.org/core/mapping-example-fts).",
"description": "Document that maps field names to field definitions.",
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields.json",
"type": "object",
"examples": [null,
],
"patternProperties": null,
"additionalProperties": false,
},
"storedSource": {"suggestSortText": "3",
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/storedSource.json",
"markdownDescription": "Boolean flag that indicates whether to store all the fields in your collection on Atlas Search or a Stored Source Object that specifies the fields to `include` or `exclude` from storage. If you omit this parameter, it defaults to `false`. To learn more, see [Define Stored Source Fields in Your Atlas Search Index](https://dochub.mongodb.org/core/stored-source-definition-fts).",
"description": "Boolean flag that indicates whether to store the fields in your collection on Atlas Search or a Stored Source Object that specifies the fields to include or exclude from storage.",
"examples": [null,
null,
false,
true,
],
"oneOf": [null,
null,
],
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/geo.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields with [GeoJSON](https://dochub.mongodb.org/core/geojson-type-fts) objects, which includes geographic points and shape coordinates. To learn more about this data type, see [geo Type](https://dochub.mongodb.org/core/geo-type-fts).",
"description": "Data type to use to index fields with geographic points and shape coordinates.",
"const": "geo",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"indexShapes": {"suggestSortText": "1",
"type": "boolean",
"markdownDescription": "Boolean flag that indicates whether Atlas Search indexes both points and geographic shape coordinates, such as lines and polygons. If you set this parameter to `false`, Atlas Search indexes only points.",
"description": "Boolean flag that indicates whether Atlas Search indexes both geographic shape coordinates and points.",
"default": false,
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/knnVector.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields of type number array for vector search. To learn more, see [knnVector Type](https://www.mongodb.com/docs/atlas/atlas-search/field-types/knn-vector/).",
"description": "Data type to use to index fields of type number array for vector search.",
"const": "knnVector",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"dimensions": {"suggestSortText": "1",
"type": "integer",
"markdownDescription": "Number of vector dimensions. Values may range from 1 to 2048.",
"description": "Number of vector dimensions. Values may range from 1 to 2048.",
"minimum": 1,
"maximum": 2048,
},
"similarity": {"suggestSortText": "2",
"type": "string",
"markdownDescription": "Vector similarity function to use to search for top K-nearest neighbors. \nValues include:\n- `euclidean` - measures the distance between ends of vectors allowing similarity measurements based on varying dimensions. See [Euclidean](https://en.wikipedia.org/wiki/Euclidean_distance) to learn more.\n- `cosine` - measures similarity based on the angle between vectors allowing for similarity measurements not scaled by magnitude. See [Cosine](https://en.wikipedia.org/wiki/Cosine_similarity) to learn more.\n- `dotProduct` - measures similarity based on both the angle between, and magnitude of vectors. See [Dot Product](https://en.wikipedia.org/wiki/Dot_product) to learn more.",
"description": "Vector similarity function to use to search for top K-nearest neighbors",
"enum": ["euclidean",
"cosine",
"dotProduct",
],
},
},
"required": ["type",
"dimensions",
"similarity",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/number.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields with numeric values of type `int32`, `int64`, and `double`. To learn more about this data type, see [number Type](https://dochub.mongodb.org/core/number-type-fts).",
"description": "Data type to use to index fields of type int32, int64, or double.",
"const": "number",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"representation": {"suggestSortText": "1",
"type": "string",
"markdownDescription": "Data type of the field to index. Values include:\n- `int64` - for indexing large integers without loss of precision and for rounding double values to integers. You can't use this type to index large double values.\n- `double` - for indexing large double values without rounding.\n\nTo learn more, see [an example](https://dochub.mongodb.org/core/number-example-fts).",
"description": "Data type of the field to index.",
"oneOf": [{"const": "int64",
},
{"const": "double",
},
],
"default": "double",
},
"indexIntegers": {"suggestSortText": "2",
"type": "boolean",
"markdownDescription": "Boolean flag that indicates whether to index or omit indexing `int32` and `int64` type fields. By default, this flag is set to `true`. Either the `indexIntegers` or `indexDoubles` flag, or both, must be `true`. To learn more, see [an example](https://dochub.mongodb.org/core/number-example-fts).",
"description": "Boolean flag that indicates whether to index or omit indexing int32 and int64 type fields.",
"default": true,
},
"indexDoubles": {"suggestSortText": "3",
"type": "boolean",
"markdownDescription": "Boolean flag that indicates whether to index or omit indexing `double` type values. By default, this flag is set to `true`. Either the `indexIntegers` or `indexDoubles` flag, or both, must be `true`. To learn more, see [an example](https://dochub.mongodb.org/core/number-example-fts).",
"description": "Boolean flag that indicates whether to index or omit indexing double type fields.",
"default": true,
},
},
"anyOf": [{"properties": {"indexIntegers": {"enum": [true,
],
},
},
},
{"properties": {"indexDoubles": {"enum": [true,
],
},
},
},
],
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/numberFacet.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index numeric values of type `int32`, `int64`, or `double` for [faceting](https://dochub.mongodb.org/core/faceting-fts). You can run only facet queries against fields indexed as the `numberFacet` type. To perform a normal text search on the same field, you must also index the field as type `number`. To learn more about this data type, see [numberFacet Type](https://dochub.mongodb.org/core/number-facet-type-fts).",
"description": "Data type to use to index fields of type int32, int64, or double for faceting.",
"const": "numberFacet",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"representation": {"suggestSortText": "1",
"type": "string",
"markdownDescription": "Data type of the field to index. Values include:\n- `int64` - for indexing large integers without loss of precision and for rounding double values to integers. You can't use this type to index large double values.\n- `double` - for indexing large double values without rounding.\n\nTo learn more, see [an example](https://dochub.mongodb.org/core/number-facet-example-fts).",
"description": "Data type of the field to index.",
"enum": ["int64",
"double",
],
},
"indexIntegers": {"suggestSortText": "2",
"type": "boolean",
"markdownDescription": "Boolean flag that indicates whether to index or omit indexing `int32` and `int64` type fields. By default, this flag is set to `true`. Either the `indexIntegers` or `indexDoubles` flag, or both, must be `true`. To learn more, see [an example](https://dochub.mongodb.org/core/number-facet-example-fts).",
"description": "Boolean flag that indicates whether to index or omit indexing int32 and int64 type fields.",
"default": true,
},
"indexDoubles": {"suggestSortText": "3",
"type": "boolean",
"markdownDescription": "Boolean flag that indicates whether to index or omit indexing `double` type values. By default, this flag is set to `true`. Either the `indexIntegers` or `indexDoubles` flag, or both, must be `true`. To learn more, see [an example](https://dochub.mongodb.org/core/number-facet-example-fts).",
"description": "Boolean flag that indicates whether to index or omit indexing double type fields.",
"default": true,
},
},
"anyOf": [{"properties": {"indexIntegers": {"enum": [true,
],
},
},
},
{"properties": {"indexDoubles": {"enum": [true,
],
},
},
},
],
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/objectId.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields with [ObjectId](https://dochub.mongodb.org/core/objectid-bson-fts) values, including ObjectId fields inside arrays and embedded documents. To learn more about this data type, see [objectId Type](https://dochub.mongodb.org/core/objectid-type-fts).",
"description": "Data type to use to index fields with values of objectId type.",
"const": "objectId",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/string.json",
"type": "object",
"allOf": [null,
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/stringComponents/extendedStringProperties.json",
"type": "object",
"properties": {"multi": {"suggestSortText": "6",
"type": "object",
"markdownDescription": "String field to index with the name of the alternate analyzer specified in the `multi` object. To learn more about configuring a `multi` object, see [Multi Analyzer](https://dochub.mongodb.org/core/multi-fts) and follow [an example](https://dochub.mongodb.org/core/string-example-fts).",
"description": "String field to index with the name of an alternate analyzer specified in the multi object.",
"examples": [{"<multi_analyzer_name>": {"type": "string",
},
},
],
"patternProperties": {"^.*$": null,
},
},
},
},
{"properties": {"type": true,
"analyzer": true,
"searchAnalyzer": true,
"indexOptions": true,
"store": true,
"ignoreAbove": true,
"multi": true,
"norms": true,
"similarity": true,
},
"additionalProperties": false,
},
],
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/stringFacet.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields with string values for [faceting](https://dochub.mongodb.org/core/faceting-fts). You can run only facet queries against fields indexed as the `stringFacet` type. To perform a normal text search on the same field, you must also index the field as type `string`. To learn more about this data type, see [stringFacet Type](https://dochub.mongodb.org/core/string-facet-type-fts).",
"description": "Data type to use to index fields with string values for faceting.",
"const": "stringFacet",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/token.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields of type `string` for sorting or filtering search results with equals, in or range. To learn more about this data type, see [token Type](https://dochub.mongodb.org/core/token-type-fts).",
"description": "Data type to use to index string fields for sorting or filtering search results with equals, in or range.",
"const": "token",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"normalizer": {"suggestSortText": "1",
"type": "string",
"markdownDescription": "Type of transformation to perform on the field value.",
"description": "Type of transformation to perform on the field value.",
"oneOf": [{"const": "lowercase",
},
{"const": "none",
},
],
"default": "none",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields/uuid.json",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Data type to use to index fields with `uuid` values, including UUID fields inside arrays and embedded documents. To learn more about this data type, see [uuid Type](https://dochub.mongodb.org/core/uuid-type-fts).",
"description": "Data type to use to index fields with true and false values.",
"const": "uuid",
"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
],
"definitions": {"typeErrorMessage": {"errorMessage": "Unsupported field mapping type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
};
const obj__11 = {"include": ["<field_name_1>",
"<field_name_2>",
],
};
const obj__12 = {"exclude": ["<field_name_1>",
"<field_name_2>",
],
};
const obj__13 = {"type": "object",
"properties": {"include": {"type": "array",
"markdownDescription": "List that contains the field names or dot-separated paths to the fields to store. By default, Atlas Search also stores the `_id` field.",
"description": "List that contains the field names or paths to the fields to store.",
"items": {"type": "string",
"pattern": "^.*$",
"errorMessage": "Field name does not meet MongoDb field naming restrictions. Please see https://www.mongodb.com/docs/manual/core/document/#field-names .",
},
},
"exclude": {"type": "array",
"markdownDescription": "List that contains the field names or dot-separated paths to the fields to exclude from storage.",
"description": "List that contains the field names or paths to the fields to exclude from storage.",
"items": {"type": "string",
"pattern": "^.*$",
"errorMessage": "Field name does not meet MongoDb field naming restrictions. Please see https://www.mongodb.com/docs/manual/core/document/#field-names .",
},
},
},
"additionalProperties": false,
};
const obj__14 = {"type": "boolean",
};
const root = {"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "fullIndex.json",
"type": "object",
"allOf": [{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/baseIndex.json",
"type": "object",
"description": "Define your Atlas Search Index",
"properties": {"mappings": {"suggestSortText": "0",
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings.json",
"markdownDescription": "Object that defines how Atlas Search indexes fields for this index. To learn more, see [Review Atlas Search Index Syntax](https://dochub.mongodb.org/core/index-definition-fts).",
"description": "Object that defines how Atlas Search indexes fields for this index.",
"type": "object",
"properties": {"dynamic": {"markdownDescription": "Boolean flag that indicates whether to recursively index all [dynamically indexable](https://dochub.mongodb.org/core/data-types-fts) fields. If you set this parameter to `false`, you must specify individual fields to index. If you omit this parameter, it defaults to `false`. To learn more, see [Static and Dynamic Mappings](https://dochub.mongodb.org/core/static-and-dynamic-mappings-fts).",
"description": "Boolean flag that indicates whether to recursively index all dynamically indexable fields.",
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/dynamic.json",
"type": "boolean",
},
"fields": {"markdownDescription": "Field specifications for your Atlas Search index. If you omit or set the `dynamic` flag to `false`, you must specify this parameter. To learn more, see [Define Field Mappings](https://dochub.mongodb.org/core/field-mapping-definition-fts).",
"description": "Field specifications for your Atlas Search index.",
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/mappings/fields.json",
"type": "object",
"examples": [null,
],
"patternProperties": null,
"additionalProperties": false,
},
},
"anyOf": [{"required": ["dynamic",
],
},
{"required": ["fields",
],
},
],
"if": {"properties": {"dynamic": {"const": false,
},
},
},
"then": {"required": ["fields",
],
"properties": {"fields": {"minProperties": 1,
},
},
},
"additionalProperties": false,
},
"synonyms": {"suggestSortText": "4",
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/synonyms.json",
"markdownDescription": "Synonym mappings to use in your index. To learn more, see [Define Synonym Mappings in Your Atlas Search Index](https://dochub.mongodb.org/core/synonym-mapping-fts).",
"description": "Synonym mappings to use in your index.",
"type": "array",
"examples": [[{"name": "<synonym_mapping_name>",
"source": {"collection": "<source_collection_name>",
},
"analyzer": "<synonym_mapping_analyzer>",
},
],
],
"items": [{"type": "object",
"properties": {"name": {"type": "string",
"minLength": 1,
"suggestSortText": "0",
"markdownDescription": "Label that identifies the synonym mapping. You must specify a unique value in the index definition and not an empty string.",
"description": "Label that identifies the synonym mapping.",
},
"source": {"suggestSortText": "1",
"type": "object",
"markdownDescription": "[Source collection](https://dochub.mongodb.org/core/source-collection-fts) for synonym mappings to use in the index. This option takes the `collection` field.",
"description": "Source collection for synonym mappings to use in the index.",
"properties": {"collection": {"type": "string",
"markdownDescription": "Label that identifies the collection for synonym mappings to use in the index. You must specify a collection in the same database as your index. Documents in this collection must be in the format described in the [Synonyms Source Collection Documents](https://dochub.mongodb.org/core/source-collection-fts).",
"description": "Label that identifies the collection for synonym mappings to use in the index.",
},
},
"required": ["collection",
],
},
"analyzer": {"suggestSortText": "2",
"markdownDescription": "Label that identifies the [analyzer](https://dochub.mongodb.org/core/analyzers-fts) to use with this synonym mapping. You can use a synonym mapping to query only fields analyzed with the same analyzer. By default, Atlas Search uses the [Standard Analyzer](https://dochub.mongodb.org/core/standard-fts). You can use any Atlas Search analyzer except the `lucene.kuromoji` and `lucene.cjk` [language analyzers](https://dochub.mongodb.org/core/language-fts) and the following [custom analyzer](https://dochub.mongodb.org/core/custom-fts) tokenizers and [token filters](https://dochub.mongodb.org/core/token-filters-fts):\n\n- `nGram` Tokenizer\n- `edgeGram Tokenizer`\n- `daitchMokotoffSoundex` Token Filter\n- `nGram` Token Filter\n- `edgeGram` Token Filter\n- `shingle` Token Filter\n- `wordDelimiterGraph` Token Filter\n\nTo use synonyms with stop words, you must either index the field using the standard analyzer or add the synonym entry without the stop word. To learn more, see [synonyms options](https://dochub.mongodb.org/core/synonym-options-fts).",
"description": "Label that identifies the analyzer to use with this synonym mapping.",
"anyOf": [null,
null,
],
"not": {"enum": ["lucene.kuromoji",
"lucene.cjk",
],
},
"default": "lucene.standard",
},
},
"required": ["name",
"source",
"analyzer",
],
},
],
},
"analyzer": {"suggestSortText": "2",
"markdownDescription": "[Analyzer](https://dochub.mongodb.org/core/analyzers-fts) to apply on fields when indexing. If you omit this parameter, it defaults to the [Standard Analyzer](https://dochub.mongodb.org/core/standard-fts). Atlas Search applies this analyzer to all fields unless you specify a different analyzer in the field mapping definition.",
"description": "Analyzer to apply to string fields when indexing.",
"anyOf": [null,
null,
],
"default": "lucene.standard",
},
"searchAnalyzer": {"suggestSortText": "3",
"markdownDescription": "[Analyzer](https://dochub.mongodb.org/core/analyzers-fts) to apply to query text before searching with it. If you omit this parameter, it defaults to the analyzer that you specify for the `analyzer` option. If you omit both the `searchAnalyzer` and the `analyzer` options, it defaults to the [Standard Analyzer](https://dochub.mongodb.org/core/standard-fts).",
"description": "Analyzer to apply to query text before searching with it.",
"anyOf": [null,
null,
],
"default": "lucene.standard",
},
"analyzers": {"suggestSortText": "1",
"type": "array",
"items": {"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer.json",
"type": "object",
"properties": {"name": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the custom analyzer. You must specify a unique value within the index and it can't start with the following strings:\n- `lucene.` \n- `builtin.` \n- `mongodb.`",
"description": "Label that identifies the custom analyzer.",
"minLength": 1,
"errorMessage": "Custom analyzer names may not start with 'lucene.', 'mongodb.' or 'builtin.'",
"pattern": "^(?!lucene\\.|mongodb\\.|builtin\\.)",
},
"charFilters": {"suggestSortText": "3",
"type": "array",
"markdownDescription": "List that contains the [character filters](https://dochub.mongodb.org/core/character-filters-fts) to perform filtering operations on text, one character at a time.",
"description": "List that contains the character filters to perform filtering operations on text.",
"items": {"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/charFilter.json",
"type": "object",
"properties": {"type": {"type": "string",
"markdownDescription": "Label that identifies the [character filter](https://www.mongodb.com/docs/atlas/atlas-search/analyzers/character-filters/) type.",
"enum": ["htmlStrip",
"icuNormalize",
"mapping",
"persian",
],
"markdownEnumDescriptions": ["Character filter that strips out HTML elements.",
"Character filter that normalizes text with the [ICU](http://site.icu-project.org/) Normalizer based on Lucene's [ICUNormalizer2CharFilter](https://lucene.apache.org/core/8_3_0/analyzers-icu/org/apache/lucene/analysis/icu/ICUNormalizer2CharFilter.html).",
"Character filter that applies the normalization mappings that you specify to characters based on Lucene's [MappingCharFilter](https://lucene.apache.org/core/8_0_0/analyzers-common/org/apache/lucene/analysis/charfilter/MappingCharFilter.html).",
"Character filter that replaces instances of [zero-width non-joiner](https://en.wikipedia.org/wiki/Zero-width_non-joiner) with the space character based on Lucene's [PersianCharFilter](https://lucene.apache.org/core/8_0_0/analyzers-common/org/apache/lucene/analysis/fa/PersianCharFilter.html)",
],
},
},
"required": ["type",
],
"oneOf": [{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/charFilter/icuNormalize.json",
"type": "object",
"_description": "Character filter that normalizes text with the ICU Normalizer.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "icuNormalize",
"markdownDescription": "Label that identifies the `icuNormalize` character filter type, which normalizes text with the [ICU](http://site.icu-project.org/) Normalizer.",
"description": "Label that identifies the icuNormalize character filter type, which normalizes text with the ICU Normalizer.",
"errorMessage": "Unsupported character filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"additionalProperties": false,
"required": ["type",
],
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/charFilter/htmlStrip.json",
"type": "object",
"_description": "Character filter that strips out HTML elements.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "htmlStrip",
"markdownDescription": "Label that identifies the `htmlStrip` character filter type, which strips out HTML elements.",
"description": "Label that identifies the htmlStrip character filter type, which strips out HTML elements.",
"errorMessage": "Unsupported character filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"ignoredTags": {"suggestSortText": "1",
"type": "array",
"markdownDescription": "List that contains the HTML tags to exclude from filtering.",
"description": "List that contains the HTML tags to exclude from filtering.",
"items": {"type": "string",
},
},
},
"additionalProperties": false,
"required": ["type",
"ignoredTags",
],
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/charFilter/mapping.json",
"type": "object",
"_description": "Character filter that applies the normalization mappings that you specify to characters.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "mapping",
"markdownDescription": "Label that identifies the `mapping` character filter type, which applies the normalization mappings that you specify to characters.",
"description": "Label that identifies the mapping character filter type, which applies the normalization mappings that you specify to characters.",
"errorMessage": "Unsupported character filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"mappings": {"suggestSortText": "1",
"type": "object",
"minProperties": 1,
"propertyNames": null,
"additionalProperties": {"type": "string",
},
"description": "Object that contains a comma-separated list of mappings in the <original> : <replacement> format. Each mapping indicates one character or group of characters to substitute for another.",
"markdownDescription": "Object that contains a comma-separated list of mappings in the `<original> : <replacement>` format. Each mapping indicates one character or group of characters to substitute for another.",
"examples": [{"<original>": "<replacement>",
},
],
},
},
"additionalProperties": false,
"required": ["type",
"mappings",
],
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/charFilter/persian.json",
"type": "object",
"_description": "Character filter that replaces instances of zero-width non-joiner with the space character.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "persian",
"markdownDescription": "Label that identifies the `persian` character filter type, which replaces instances of [zero-width non-joiner](https://en.wikipedia.org/wiki/Zero-width_non-joiner) with the space character.",
"description": "Label that identifies the persian character filter type, which replaces instances of zero-width non-joiner with the space character.",
"errorMessage": "Unsupported character filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"additionalProperties": false,
"required": ["type",
],
},
],
"definitions": {"typePropertyOverrides": {"errorMessage": "Unsupported character filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
},
},
"tokenizer": {"suggestSortText": "1",
"markdownDescription": "[Tokenizer](https://dochub.mongodb.org/core/tokenizers-fts) to use to create tokens for indexing.",
"description": "Tokenizer to use to create tokens for indexing.",
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenizer.json",
"type": "object",
"properties": {"type": {"type": "string",
"markdownDescription": "Label that identifies this [tokenizer](https://www.mongodb.com/docs/atlas/atlas-search/analyzers/tokenizers/) type.",
"enum": ["edgeGram",
"keyword",
"nGram",
"regexCaptureGroup",
"regexSplit",
"standard",
"uaxUrlEmail",
"whitespace",
],
"markdownEnumDescriptions": ["Tokenizer that tokenizes input from the left side, or edge, of a text input into n-grams of given sizes.",
"Tokenizer that tokenizes the entire input as a single token.",
"Tokenizer that tokenizes input into text chunks, or n-grams, of given sizes.",
"Tokenizer that matches a regular expression pattern to extract tokens.",
"Tokenizer that splits tokens with a regular expression-based delimiter.",
"Tokenizer that tokenizes input based on word break rules from the [Unicode Text Segmentation algorithm](https://www.unicode.org/L2/L2019/19034-uax29-34-draft.pdf).",
"Tokenizer that tokenizes URLs and email addresses.",
"Tokenizer that tokenizes input based on occurrences of whitespace between words.",
],
},
},
"required": ["type",
],
"oneOf": [{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenizer/standard.json",
"type": "object",
"_description": "Tokenizer that tokenizes input based on word break rules from the Unicode Text Segmentation algorithm.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "standard",
"markdownDescription": "Label that identifies the `standard` tokenizer type, which tokenizes input based on word break rules from the [Unicode Text Segmentation algorithm](https://www.unicode.org/L2/L2019/19034-uax29-34-draft.pdf).",
"description": "Label that identifies the standard tokenizer type, which tokenizes input based on word break rules from the Unicode Text Segmentation algorithm.",
"errorMessage": "Unsupported tokenizer type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"maxTokenLength": {"suggestSortText": "1",
"type": "integer",
"markdownDescription": "Maximum length for a single token. Tokens greater than this length are split at `maxTokenLength` into multiple tokens.",
"description": "Maximum length for a single token. Tokens greater than this length are split at the specified length into multiple tokens.",
"default": 255,
"minimum": 1,
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenizer/edgeGram.json",
"type": "object",
"_description": "Tokenizer that tokenizes input from the left side, or edge, of a text input into n-grams of given sizes.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "edgeGram",
"markdownDescription": "Label that identifies the `edgeGram` tokenizer type, which tokenizes input from the left side, or edge, of a text input into n-grams of given sizes.",
"description": "Label that identifies the edgeGram tokenizer type, which tokenizes input from the left side, or edge, of a text input into n-grams of given sizes.",
"errorMessage": "Unsupported tokenizer type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"minGram": {"suggestSortText": "1",
"type": "integer",
"markdownDescription": "Number of characters to include in the shortest token.",
"description": "Number of characters to include in the shortest token.",
"minimum": 0,
},
"maxGram": {"suggestSortText": "2",
"type": "integer",
"markdownDescription": "Number of characters to include in the longest token.",
"description": "Number of characters to include in the longest token.",
"minimum": 0,
},
},
"required": ["type",
"maxGram",
"minGram",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenizer/keyword.json",
"type": "object",
"_description": "Tokenizer that tokenizes the entire input as a single token.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "keyword",
"markdownDescription": "Label that identifies the `keyword` tokenizer type, which tokenizes the entire input as a single token.",
"description": "Label that identifies the keyword tokenizer type, which tokenizes the entire input as a single token.",
"errorMessage": "Unsupported tokenizer type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenizer/nGram.json",
"_description": "Tokenizer that tokenizes input into text chunks, or n-grams, of given sizes.",
"type": "object",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "nGram",
"markdownDescription": "Label that identifies the `nGram` tokenizer type, which tokenizes input into text chunks, or n-grams, of given sizes.",
"description": "Label that identifies the nGram tokenizer type, which tokenizes input into text chunks, or n-grams, of given sizes.",
"errorMessage": "Unsupported tokenizer type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"minGram": {"suggestSortText": "1",
"type": "integer",
"markdownDescription": "Number of characters to include in the shortest token.",
"description": "Number of characters to include in the shortest token.",
"minimum": 0,
},
"maxGram": {"suggestSortText": "2",
"type": "integer",
"markdownDescription": "Number of characters to include in the longest token.",
"description": "Number of characters to include in the longest token.",
"minimum": 0,
},
},
"required": ["type",
"maxGram",
"minGram",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenizer/regexCaptureGroup.json",
"type": "object",
"_description": "Tokenizer that matches a regular expression pattern to extract tokens.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "regexCaptureGroup",
"markdownDescription": "Label that identifies the `regexCaptureGroup` tokenizer type, which matches a regular expression pattern to extract tokens.",
"description": "Label that identifies the regexCaptureGroup tokenizer type, which matches a regular expression pattern to extract tokens.",
"errorMessage": "Unsupported tokenizer type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"pattern": {"suggestSortText": "1",
"type": "string",
"markdownDescription": "String that indicates the regular expression to match against.",
"description": "String that indicates the regular expression to match against.",
},
"group": {"suggestSortText": "2",
"type": "integer",
"markdownDescription": "Index of the character group within the matching expression to extract into tokens. Use `0` to extract all character groups.",
"description": "Index of the character group within the matching expression to extract into tokens. Use 0 to extract all character groups.",
"minimum": 0,
},
},
"required": ["type",
"pattern",
"group",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenizer/regexSplit.json",
"type": "object",
"_description": "Tokenizer that splits tokens with a regular expression-based delimiter.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "regexSplit",
"markdownDescription": "Label that identifies the `regexSplit` tokenizer type, which splits tokens with a regular expression-based delimiter.",
"description": "Label that identifies the regexSplit tokenizer type, which splits tokens with a regular expression-based delimiter.",
"errorMessage": "Unsupported tokenizer type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"pattern": {"suggestSortText": "1",
"type": "string",
"markdownDescription": "String that indicates the regular expression to match against.",
"description": "String that indicates the regular expression to match against.",
},
},
"required": ["type",
"pattern",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenizer/uaxUrlEmail.json",
"type": "object",
"_description": "Tokenizer that tokenizes URLs and email addresses.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "uaxUrlEmail",
"markdownDescription": "Label that identifies the `uaxURLEmail` tokenizer type, which tokenizes URLs and email addresses.",
"description": "Label that identifies the uaxURLEmail tokenizer type, which tokenizes URLs and email addresses.",
"errorMessage": "Unsupported tokenizer type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"maxTokenLength": {"suggestSortText": "1",
"type": "integer",
"markdownDescription": "Maximum length for a single token. Tokens greater than this length are split at `maxTokenLength` into multiple tokens.",
"description": "Maximum length for a single token. Tokens greater than this length are split at the specified length into multiple tokens.",
"default": 255,
"minimum": 1,
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenizer/whitespace.json",
"type": "object",
"_description": "Tokenizer that tokenizes input based on occurrences of whitespace between words.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"const": "whitespace",
"markdownDescription": "Label that identifies the `whitespace` tokenizer type, which tokenizes input based on occurrences of whitespace between words.",
"description": "Label that identifies the whitespace tokenizer type, which tokenizes input based on occurrences of whitespace between words.",
"errorMessage": "Unsupported tokenizer type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"maxTokenLength": {"suggestSortText": "1",
"type": "integer",
"markdownDescription": "Maximum length for a single token. Tokens greater than this length are split at `maxTokenLength` into multiple tokens.",
"description": "Maximum length for a single token. Tokens greater than this length are split at the specified length into multiple tokens.",
"default": 255,
"minimum": 1,
},
},
"required": ["type",
],
"additionalProperties": false,
},
],
"definitions": {"typePropertyOverrides": {"errorMessage": "Unsupported tokenizer type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
},
"tokenFilters": {"suggestSortText": "2",
"type": "array",
"markdownDescription": "List that contains the [token filters](https://dochub.mongodb.org/core/token-filters-fts) to perform operations such as stemming and redaction.",
"description": "List that contains the token filters to perform operations such as stemming and redaction.",
"items": {"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter.json",
"type": "object",
"properties": {"type": {"type": "string",
"markdownDescription": "Label that identifies the [token filter](https://www.mongodb.com/docs/atlas/atlas-search/analyzers/token-filters/) type.",
"enum": ["asciiFolding",
"daitchMokotoffSoundex",
"edgeGram",
"englishPossessive",
"flattenGraph",
"icuFolding",
"icuNormalizer",
"keywordRepeat",
"kStemming",
"length",
"lowercase",
"nGram",
"porterStemming",
"regex",
"removeDuplicates",
"reverse",
"shingle",
"snowballStemming",
"spanishPluralStemming",
"stempel",
"stopword",
"trim",
"wordDelimiterGraph",
],
"markdownEnumDescriptions": ["Token filter that converts alphabetic, numeric, and symbolic unicode characters that are not in the [Basic Latin Unicode block](https://en.wikipedia.org/wiki/Basic_Latin_(Unicode_block)) to their ASCII equivalents, if available.",
"Token filter that creates tokens for words that sound the same based on the [Daitch-Mokotoff Soundex](https://en.wikipedia.org/wiki/Daitch%E2%80%93Mokotoff_Soundex) phonetic algorithm.",
"Token filter that tokenizes input from the left side, or edge, of a text input into n-grams of configured sizes.",
"Token filter that removes possessives (trailing 's) from words.",
"Token filter that transforms a token filter graph, such as the token filter graph that the `wordDelimiterGraph` token filter produces, into a flat form suitable for indexing.",
"Token filter that applies character folding from [Unicode Technical Report #30](http://www.unicode.org/reports/tr30/tr30-4.html) such as accent removal, case folding, canonical duplicates folding, and many others detailed in the report.",
"Token filter that normalizes tokens using a standard [Unicode Normalization Mode](https://unicode.org/reports/tr15/).",
"Token filter that emits each incoming token twice, once as keyword and once non-keyword.",
"Token filter that combines algorithmic stemming with a built-in dictionary for the English language to stem words.",
"Token filter that removes tokens that don't match the minimum and maximum length that you specify.",
"Token filter that normalizes token text to lowercase.",
"Token filter that tokenizes input into n-grams of configured sizes.",
"Token filter that uses the porter stemming algorithm to remove the common morphological and inflectional suffixes from words in English. It expects lowercase text and doesn't work as expected for uppercase text.",
"Token filter that applies a regular expression to each token and replaces matches with a specified string.",
"Token filter that filters out tokens at the same position and term text as the previous token in the stream.",
"Token filter that reverses each string token.",
"Token filter that constructs shingles (token n-grams) of configurable sizes from a series of tokens.",
"Token filter that stems tokens using a [Snowball-generated stemmer](https://snowballstem.org/).",
"Token filter that stems Spanish plural words. It expects lowercase text.",
"Token filter that uses Lucene's [default Polish stemmer table](https://lucene.apache.org/core/9_2_0/analysis/stempel/org/apache/lucene/analysis/pl/PolishAnalyzer.html#DEFAULT_STEMMER_FILE) to stem words in the Polish language. It expects lowercase text.",
"Token filter that removes tokens for the stop words that you specify.",
"Token filter that trims leading and trailing whitespace from tokens.",
"Token filter that splits tokens into sub-tokens based on configured rules.",
],
},
},
"required": ["type",
],
"oneOf": [{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/asciiFolding.json",
"type": "object",
"_description": "Token filter that converts alphabetic, numeric, and symbolic unicode characters that are not in the Basic Latin Unicode block to their ASCII equivalents, if available.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `asciiFolding` token filter type, which converts alphabetic, numeric, and symbolic unicode characters that are not in the [Basic Latin Unicode block](https://en.wikipedia.org/wiki/Basic_Latin_(Unicode_block)) to their ASCII equivalents, if available.",
"description": "Label that identifies the asciiFolding token filter type, which converts alphabetic, numeric, and symbolic unicode characters that are not in the Basic Latin Unicode block to their ASCII equivalents, if available.",
"const": "asciiFolding",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"originalTokens": {"suggestSortText": "1",
"type": "string",
"markdownDescription": "String that specifies whether to `include` or `omit` the original tokens in the output of the token filter.",
"description": "String that specifies whether to include or omit the original tokens in the output of the token filter.",
"oneOf": [{"const": "include",
},
{"const": "omit",
},
],
"default": "omit",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/daitchMokotoffSoundex.json",
"type": "object",
"_description": "The daitchMokotoffSoundex token filter creates tokens for words that sound the same based on the Daitch-Mokotoff Soundex phonetic algorithm.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `daitchMokotoffSoundex` token filter type, which creates tokens for words that sound the same based on the [Daitch-Mokotoff Soundex](https://en.wikipedia.org/wiki/Daitch%E2%80%93Mokotoff_Soundex) phonetic algorithm.",
"description": "Label that identifies the daitchMokotoffSoundex token filter type, which creates tokens for words that sound the same based on the Daitch-Mokotoff Soundex phonetic algorithm.",
"const": "daitchMokotoffSoundex",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"originalTokens": {"suggestSortText": "1",
"type": "string",
"markdownDescription": "String that specifies whether to `include` or `omit` the original tokens in the output of the token filter.",
"description": "Specify whether to include or omit the original tokens in the output of the token filter",
"oneOf": [{"const": "include",
},
{"const": "omit",
},
],
"default": "include",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/edgeGram.json",
"type": "object",
"_description": "Token filter that tokenizes input from the left side, or edge, of a text input into n-grams of configured sizes.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `edgegram` token filter type, which tokenizes input from the left side, or edge, of a text input into n-grams of configured sizes.",
"description": "Label that identifies the edgegram token filter type, which tokenizes input from the left side, or edge, of a text input into n-grams of configured sizes.",
"const": "edgeGram",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"minGram": {"suggestSortText": "1",
"type": "integer",
"markdownDescription": "Number that specifies the minimum length of generated n-grams. Value must be less than or equal to `maxGram`.",
"description": "Number that specifies the minimum length of generated n-grams.",
"minimum": 0,
},
"maxGram": {"suggestSortText": "2",
"type": "integer",
"markdownDescription": "Number that specifies the maximum length of generated n-grams. Value must be greater than or equal to `minGram`.",
"description": "Number that specifies the maximum length of generated n-grams.",
"minimum": 0,
},
"termNotInBounds": {"suggestSortText": "3",
"type": "string",
"markdownDescription": "String that specifies whether to `include` or `omit` tokens shorter than `minGram` or longer than `maxGram`.",
"description": "String that specifies whether to include or omit tokens shorter than minGram or longer than maxGram.",
"oneOf": [{"const": "include",
},
{"const": "omit",
},
],
"default": "omit",
},
},
"required": ["type",
"minGram",
"maxGram",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/englishPossessive.json",
"type": "object",
"_description": "Token filter that removes possessives (trailing 's) from words.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `englishPossessive` token filter type, which removes possessives (trailing 's) from words.",
"description": "Label that identifies the englishPossessive token filter type, which removes possessives (trailing 's) from words.",
"const": "englishPossessive",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/flattenGraph.json",
"type": "object",
"_description": "Token filter that transforms a token filter graph, such as the token filter graph that the wordDelimiterGraph token filter produces, into a flat form suitable for indexing.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `flattenGraph` token filter type, which transforms a token filter graph, such as the token filter graph that the `wordDelimiterGraph` token filter produces, into a flat form suitable for indexing.",
"description": "Label that identifies the flattenGraph token filter type, which transforms a token filter graph, such as the token filter graph that the wordDelimiterGraph token filter produces, into a flat form suitable for indexing.",
"const": "flattenGraph",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/icuFolding.json",
"type": "object",
"_description": "Token filter that applies character folding from Unicode Technical Report #30 such as accent removal, case folding, canonical duplicates folding, and many others detailed in the report.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `icuFolding` token filter type, which applies character folding from [Unicode Technical Report #30](http://www.unicode.org/reports/tr30/tr30-4.html) such as accent removal, case folding, canonical duplicates folding, and many others detailed in the report.",
"description": "Label that identifies the icuFolding token filter type, which applies character folding from Unicode Technical Report #30 such as accent removal, case folding, canonical duplicates folding, and many others detailed in the report.",
"const": "icuFolding",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/icuNormalizer.json",
"type": "object",
"_description": "Token filter that normalizes tokens using a standard Unicode Normalization Mode.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `icuNormalizer` token filter type, which normalizes tokens using a standard [Unicode Normalization Mode](https://unicode.org/reports/tr15/).",
"const": "icuNormalizer",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"normalizationForm": {"suggestSortText": "1",
"type": "string",
"markdownDescription": "String that indicates the [normalization form](https://unicode.org/reports/tr15/#Norm_Forms) to apply. Values include: \n\n- `nfd` (Canonical Decomposition)\n\n- `nfc` (Canonical Decomposition, followed by Canonical Composition)\n\n- `nfkd` (Compatibility Decomposition)\n\n- `nfkc` (Compatibility Decomposition, followed by Canonical Composition)\n\n",
"description": "String that indicates the normalization form to apply.",
"oneOf": [{"const": "nfd",
},
{"const": "nfc",
},
{"const": "nfkd",
},
{"const": "nfkc",
},
],
"default": "nfc",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/keywordRepeat.json",
"type": "object",
"_description": "Token filter that emits each incoming token twice, once as keyword and once non-keyword.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Token filter that emits each incoming token twice, once as keyword and once non-keyword.",
"description": "Token filter that emits each incoming token twice, once as keyword and once non-keyword.",
"const": "keywordRepeat",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/kStemming.json",
"type": "object",
"_description": "Token filter that combines algorithmic stemming with a built-in dictionary for the English language to stem words.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `kStemming` token filter type, which combines algorithmic stemming with a built-in dictionary for the English language to stem words.",
"description": "Label that identifies the kStemming token filter type, which combines algorithmic stemming with a built-in dictionary for the English language to stem words.",
"const": "kStemming",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/length.json",
"type": "object",
"_description": "Token filter that removes tokens that don't match the minimum and maximum length that you specify.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `length` token filter type, which removes tokens that don't match the minimum and maximum length that you specify.",
"description": "Label that identifies the length token filter type, which removes tokens that don't match the minimum and maximum length that you specify.",
"const": "length",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"min": {"suggestSortText": "1",
"type": "integer",
"markdownDescription": "Number that specifies the minimum length of a token. Value must be less than or equal to `max`.",
"description": "Number that specifies the minimum length of a token.",
"minimum": 0,
"default": 0,
},
"max": {"suggestSortText": "2",
"type": "integer",
"markdownDescription": "Number that specifies the maximum length of a token. Value must be greater than or equal to `min`.",
"description": "Number that specifies the maximum length of a token.",
"minimum": 0,
"default": 255,
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/lowercase.json",
"type": "object",
"_description": "Token filter that normalizes token text to lowercase.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `lowercase` token filter type, which normalizes token text to lowercase.",
"description": "Label that identifies the lowercase token filter type, which normalizes token text to lowercase.",
"const": "lowercase",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/nGram.json",
"type": "object",
"_description": "Token filter that tokenizes input into n-grams of configured sizes.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `nGram` token filter type, which tokenizes input into n-grams of configured sizes.",
"description": "Label that identifies the nGram token filter type, which tokenizes input into n-grams of configured sizes.",
"const": "nGram",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"minGram": {"suggestSortText": "1",
"type": "integer",
"markdownDescription": "Number that specifies the minimum length of generated n-grams. Value must be less than or equal to `maxGram`.",
"description": "Number that specifies the minimum length of generated n-grams.",
"minimum": 0,
},
"maxGram": {"suggestSortText": "2",
"type": "integer",
"markdownDescription": "Number that specifies the maximum length of generated n-grams. Value must be greater than or equal to `minGram`.",
"description": "Number that specifies the maximum length of generated n-grams.",
"minimum": 0,
},
"termNotInBounds": {"suggestSortText": "3",
"type": "string",
"markdownDescription": "String that specifies whether to `include` or `omit` tokens shorter than `minGram` or longer than `maxGram`.",
"description": "String that specifies whether to include or omit tokens shorter than minGram or longer than maxGram.",
"oneOf": [{"const": "include",
},
{"const": "omit",
},
],
"default": "omit",
},
},
"required": ["type",
"minGram",
"maxGram",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/porterStemming.json",
"type": "object",
"_description": "Token filter that uses the porter stemming algorithm to remove the common morphological and inflectional suffixes from words in English. It expects lowercase text and doesn't work as expected for uppercase text.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `porterStemming` token filter type, which uses the porter stemming algorithm to remove the common morphological and inflectional suffixes from words in English.",
"description": "Label that identifies the porterStemming token filter type, which uses the porter stemming algorithm to remove the common morphological and inflectional suffixes from words in English.",
"const": "porterStemming",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/regex.json",
"type": "object",
"_description": "Token filter that applies a regular expression to each token and replaces matches with a specified string.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `regex` token filter type, which applies a regular expression to each token and replaces matches with a specified string.",
"description": "Label that identifies the regex token filter type, which applies a regular expression to each token and replaces matches with a specified string.",
"const": "regex",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"pattern": {"suggestSortText": "1",
"type": "string",
"markdownDescription": "Regular expression pattern to apply to each token.",
"description": "Regular expression pattern to apply to each token.",
},
"replacement": {"suggestSortText": "2",
"type": "string",
"markdownDescription": "Replacement string to substitute wherever a matching pattern occurs.",
"description": "Replacement string to substitute wherever a matching pattern occurs.",
},
"matches": {"suggestSortText": "3",
"type": "string",
"markdownDescription": "String that indicates whether to replace `all` matching patterns, or only the `first` matching pattern.",
"description": "String that indicates whether to replace `all` matching patterns, or only the `first` matching pattern.",
"enum": ["all",
"first",
],
},
},
"required": ["type",
"pattern",
"replacement",
"matches",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/removeDuplicates.json",
"type": "object",
"_description": "Token filter that filters out tokens at the same position and term text as the previous token in the stream.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Token filter that filters out tokens at the same position and term text as the previous token in the stream.",
"description": "Token filter that filters out tokens at the same position and term text as the previous token in the stream.",
"const": "removeDuplicates",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/reverse.json",
"type": "object",
"_description": "Token filter that reverses each string token.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `reverse` token filter type, which reverses each string token.",
"description": "Label that identifies the reverse token filter type, which reverses each string token.",
"const": "reverse",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/shingle.json",
"type": "object",
"_description": "Token filter that constructs shingles (token n-grams) configurable sizes from a series of tokens.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `shingle` token filter type, which constructs shingles (token n-grams) configurable sizes from a series of tokens.",
"description": "Label that identifies the shingle token filter type, which constructs shingles (token n-grams) configurable sizes from a series of tokens.",
"const": "shingle",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"minShingleSize": {"suggestSortText": "1",
"type": "integer",
"markdownDescription": "Minimum number of tokens per shingle. Must be less than or equal to `maxShingleSize`.",
"description": "Minimum number of tokens per shingle.",
"minimum": 2,
},
"maxShingleSize": {"suggestSortText": "2",
"type": "integer",
"markdownDescription": "Maximum number of tokens per shingle. Must be greater than or equal to `minShingleSize`.",
"description": " Maximum number of tokens per shingle.",
"minimum": 0,
},
},
"required": ["type",
"maxShingleSize",
"minShingleSize",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/snowballStemming.json",
"type": "object",
"_description": "Token filter that stems tokens using a Snowball-generated stemmer.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `snowballStemming` token filter type, which stems tokens using a [Snowball-generated stemmer](https://snowballstem.org/).",
"description": "Label that identifies the snowballStemming token filter type, which stems tokens using a Snowball-generated stemmer.",
"const": "snowballStemming",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"stemmerName": {"suggestSortText": "1",
"type": "string",
"markdownDescription": "Label that identifies the stemmer to use. To learn more about the available values, see [snowballStemming](https://dochub.mongodb.org/core/snowballStemming-fts)",
"description": "Label that identifies the stemmer to use.",
"enum": ["arabic",
"armenian",
"basque",
"catalan",
"danish",
"dutch",
"english",
"estonian",
"finnish",
"french",
"german",
"german2",
"hungarian",
"irish",
"italian",
"kp",
"lithuanian",
"lovins",
"norwegian",
"porter",
"portuguese",
"romanian",
"russian",
"spanish",
"swedish",
"turkish",
],
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/spanishPluralStemming.json",
"type": "object",
"_description": "Token filter that stems Spanish plural words. It expects lowercase text.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `spanishPluralStemming` token filter type, which stems Spanish plural words.",
"description": "Label that identifies the spanishPluralStemming token filter type, which stems Spanish plural words.",
"const": "spanishPluralStemming",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/stempel.json",
"type": "object",
"_description": "Token filter that uses Lucene's default Polish stemmer table to stem words in the Polish language. It expects lowercase text.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `stempel` token filter type, which uses Lucene's [default Polish stemmer table](https://lucene.apache.org/core/9_2_0/analysis/stempel/org/apache/lucene/analysis/pl/PolishAnalyzer.html#DEFAULT_STEMMER_FILE) to stem words in the Polish language.",
"description": "Label that identifies the stempel token filter type, which uses Lucene's default Polish stemmer table to stem words in the Polish language.",
"const": "stempel",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/stopword.json",
"type": "object",
"_description": "Token filter that removes tokens for the stop words that you specify.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `stopword` token filter type, which removes tokens for the stop words that you specify.",
"description": "Label that identifies the stopword token filter type, which removes tokens for the stop words that you specify.",
"const": "stopword",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"tokens": {"suggestSortText": "1",
"type": "array",
"items": {"type": "string",
},
"markdownDescription": "List that contains the stop words that correspond to the tokens to remove. Value must be one or more stop words.",
"description": "List that contains the stop words that correspond to the tokens to remove.",
},
"ignoreCase": {"suggestSortText": "2",
"type": "boolean",
"markdownDescription": "Boolean flag that indicates whether to ignore the letter case and remove all tokens that match the specified stop words. If you omit this attribute, it defaults to `true`.",
"description": "Boolean flag that indicates whether to ignore the letter case and remove all tokens that match the specified stop words.",
"default": true,
},
},
"required": ["type",
"tokens",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/trim.json",
"type": "object",
"_description": "Token filter that trims leading and trailing whitespace from tokens.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `trim` token filter type, which trims leading and trailing whitespace from tokens.",
"description": "Label that identifies the trim token filter type, which trims leading and trailing whitespace from tokens.",
"const": "trim",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
"required": ["type",
],
"additionalProperties": false,
},
{"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/custom_analyzer/tokenFilter/wordDelimiterGraph.json",
"type": "object",
"_description": "Token filter that splits tokens into sub-tokens based on configured rules.",
"properties": {"type": {"suggestSortText": "0",
"type": "string",
"markdownDescription": "Label that identifies the `wordDelimiterGraph` token filter type, which splits tokens into sub-tokens based on configured rules.",
"description": "Label that identifies the wordDelimiterGraph token filter type, which splits tokens into sub-tokens based on configured rules.",
"const": "wordDelimiterGraph",
"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
"delimiterOptions": {"suggestSortText": "1",
"type": "object",
"markdownDescription": "Object that contains the rules that determine how to split words into sub-words.",
"description": "Object that contains the rules that determine how to split words into sub-words.",
"properties": {"generateWordParts": {"suggestSortText": "0",
"type": "boolean",
"markdownDescription": "Flag that indicates whether to split tokens based on sub-words. For example, if `true`, this option splits `PowerShot` into `Power` and `Shot`.",
"description": "Flag that indicates whether to split tokens based on sub-words.",
"default": true,
},
"generateNumberParts": {"suggestSortText": "1",
"type": "boolean",
"markdownDescription": "Flag that indicates whether to split tokens based on sub-numbers. For example, if `true`, this option splits `100-2` into `100` and `2`.",
"description": "Flag that indicates whether to split tokens based on sub-numbers.",
"default": true,
},
"concatenateWords": {"suggestSortText": "2",
"type": "boolean",
"markdownDescription": "Flag that indicates whether to concatenate runs of sub-words. For example, if `true`, this option concatenates `wi-fi` into `wifi`. If `true`, apply the `flattenGraph` token filter after this option to make the token stream suitable for indexing.",
"description": "Flag that indicates whether to concatenate runs of sub-words.",
"default": false,
},
"concatenateNumbers": {"suggestSortText": "3",
"type": "boolean",
"markdownDescription": "Flag that indicates whether to concatenate runs of sub-numbers. For example, if `true`, this option concatenates `100-2` into `1002`. If `true`, apply the `flattenGraph` token filter after this option to make the token stream suitable for indexing.",
"description": "Flag that indicates whether to concatenate runs of sub-numbers.",
"default": false,
},
"concatenateAll": {"suggestSortText": "4",
"type": "boolean",
"markdownDescription": "Flag that indicates whether to concatenate runs. For example, if `true`, this option concatenates `wi-fi-100-2` into `wifi1002`. If `true`, apply the `flattenGraph` token filter after this option to make the token stream suitable for indexing.",
"description": "Flag that indicates whether to concatenate runs.",
"default": false,
},
"preserveOriginal": {"suggestSortText": "5",
"type": "boolean",
"markdownDescription": "Flag that indicates whether to generate tokens of the original words. If `true`, apply the `flattenGraph` token filter after this option to make the token stream suitable for indexing.",
"description": "Flag that indicates whether to generate tokens of the original words.",
"default": true,
},
"splitOnCaseChange": {"suggestSortText": "6",
"type": "boolean",
"markdownDescription": "Flag that indicates whether to split tokens based on letter-case transitions. For example, if `true`, this option splits `camelCase` into `camel` and `Case`.",
"description": "Flag that indicates whether to split tokens based on letter-case transitions.",
"default": true,
},
"splitOnNumerics": {"suggestSortText": "7",
"type": "boolean",
"markdownDescription": "Flag that indicates whether to split tokens based on letter-number transitions. For example, if `true`, this option splits `g2g` into `g`, `2`, and `g`.",
"description": "Flag that indicates whether to split tokens based on letter-number transitions.",
"default": true,
},
"stemEnglishPossessive": {"suggestSortText": "8",
"type": "boolean",
"markdownDescription": "Flag that indicates whether to remove trailing possessives from each sub-word. For example, if `true`, this option changes `who's` into `who'`.",
"description": "Flag that indicates whether to remove trailing possessives from each sub-word.",
"default": true,
},
"ignoreKeywords": {"suggestSortText": "9",
"type": "boolean",
"markdownDescription": "Flag that indicates whether to skip tokens with the `keyword` attribute set to `true`",
"description": "Flag that indicates whether to skip tokens with the `keyword` attribute set to `true`",
"default": false,
},
},
"additionalProperties": false,
},
"protectedWords": {"suggestSortText": "2",
"type": "object",
"description": "Object that contains options for protected words.",
"properties": {"words": {"suggestSortText": "0",
"type": "array",
"markdownDescription": "List that contains the tokens to protect from delimination. If you specify `protectedWords`, you must specify this option.",
"description": "List that contains the tokens to protect from delimination.",
"items": {"type": "string",
},
},
"ignoreCase": {"suggestSortText": "1",
"type": "boolean",
"markdownDescription": "Flag that indicates whether to ignore letter case sensitivity for protected words.",
"description": "Flag that indicates whether to ignore letter case sensitivity for protected words.",
"default": true,
},
},
"required": ["words",
],
"additionalProperties": false,
},
},
"required": ["type",
],
"additionalProperties": false,
},
],
"definitions": {"typePropertyOverrides": {"errorMessage": "Unsupported token filter type.",
"$comment": "The custom error message defined above is a global override for all consuming schema.",
},
},
},
},
},
"required": ["name",
"tokenizer",
],
},
"markdownDescription": "[Custom analyzers](https://dochub.mongodb.org/core/custom-fts) to define for your index. You can define as many custom analyzers as you need for your specific indexing purposes.",
"description": "Custom analyzers to define for your index.",
"minItems": 1,
},
"storedSource": {"suggestSortText": "5",
"$schema": "http://json-schema.org/draft-07/schema#",
"$id": "components/storedSource.json",
"markdownDescription": "Boolean flag that indicates whether to store all the fields in your collection on Atlas Search or a Stored Source Object that specifies the fields to `include` or `exclude` from storage. If you omit this parameter, it defaults to `false`. To learn more, see [Define Stored Source Fields in Your Atlas Search Index](https://dochub.mongodb.org/core/stored-source-definition-fts).",
"description": "Boolean flag that indicates whether to store the fields in your collection on Atlas Search or a Stored Source Object that specifies the fields to include or exclude from storage.",
"examples": [null,
null,
false,
true,
],
"oneOf": [null,
null,
],
},
"numPartitions": {"description": "Number of index partitions. Allowed values are [1, 2, 4].",
"markdownDescription": "Number of [index partitions](https://dochub.mongodb.org/core/atlas-search-index-partition). Allowed values are [1, 2, 4].",
"default": 1,
"oneOf": [{"const": 1,
},
{"const": 2,
},
{"const": 4,
},
],
"suggestSortText": "6",
"type": "integer",
},
},
},
],
"description": "Define your Atlas Search Index",
"properties": {"mappings": true,
"synonyms": true,
"analyzer": true,
"searchAnalyzer": true,
"analyzers": true,
"storedSource": true,
"numPartitions": true,
"name": {"type": "string",
"minLength": 1,
"markdownDescription": "Label that identifies the index. If you omit this parameter, it defaults to `default`. You must specify unique index names in each [collection](https://dochub.mongodb.org/core/collection-fts).",
"description": "Label that identifies the index.",
"defaultSnippets": [{"label": "index name",
"body": "default",
},
],
},
"database": {"type": "string",
"minLength": 1,
"markdownDescription": "Label that identifies the database to contain the index.",
"description": "Label that identifies the database to contain the index.",
},
"collectionName": {"type": "string",
"minLength": 1,
"markdownDescription": "Label that identifies the collection to index.",
"description": "Label that identifies the collection to index.",
},
},
"required": ["mappings",
"collectionName",
"database",
"name",
],
"additionalProperties": false,
};


obj__1["^.*$"]["oneOf"][0] = obj__10;
obj__1["^.*$"]["oneOf"][1]["items"] = obj__10;
obj__4["anyOf"][0] = obj__2;
obj__9["properties"]["analyzer"]["anyOf"][0] = obj__4;
obj__9["properties"]["analyzer"]["anyOf"][1] = obj__3;
obj__9["properties"]["searchAnalyzer"]["anyOf"][0] = obj__4;
obj__9["properties"]["searchAnalyzer"]["anyOf"][1] = obj__3;
obj__9["properties"]["similarity"]["examples"][0] = obj__5;
obj__9["properties"]["similarity"]["examples"][1] = obj__6;
obj__9["properties"]["similarity"]["examples"][2] = obj__7;
obj__9["properties"]["similarity"]["properties"] = obj__8;
obj__10["oneOf"][0]["properties"]["analyzer"]["anyOf"][0] = obj__2;
obj__10["oneOf"][0]["properties"]["analyzer"]["anyOf"][1] = obj__3;
obj__10["oneOf"][0]["properties"]["similarity"]["examples"][0] = obj__5;
obj__10["oneOf"][0]["properties"]["similarity"]["examples"][1] = obj__6;
obj__10["oneOf"][0]["properties"]["similarity"]["examples"][2] = obj__7;
obj__10["oneOf"][0]["properties"]["similarity"]["properties"] = obj__8;
obj__10["oneOf"][4]["properties"]["fields"]["examples"][0] = obj__0;
obj__10["oneOf"][4]["properties"]["fields"]["patternProperties"] = obj__1;
obj__10["oneOf"][5]["properties"]["fields"]["examples"][0] = obj__0;
obj__10["oneOf"][5]["properties"]["fields"]["patternProperties"] = obj__1;
obj__10["oneOf"][5]["properties"]["storedSource"]["examples"][0] = obj__11;
obj__10["oneOf"][5]["properties"]["storedSource"]["examples"][1] = obj__12;
obj__10["oneOf"][5]["properties"]["storedSource"]["oneOf"][0] = obj__13;
obj__10["oneOf"][5]["properties"]["storedSource"]["oneOf"][1] = obj__14;
obj__10["oneOf"][11]["allOf"][0] = obj__9;
obj__10["oneOf"][11]["allOf"][1]["properties"]["multi"]["patternProperties"]["^.*$"] = obj__9;
root["allOf"][0]["properties"]["mappings"]["properties"]["fields"]["examples"][0] = obj__0;
root["allOf"][0]["properties"]["mappings"]["properties"]["fields"]["patternProperties"] = obj__1;
root["allOf"][0]["properties"]["synonyms"]["items"][0]["properties"]["analyzer"]["anyOf"][0] = obj__2;
root["allOf"][0]["properties"]["synonyms"]["items"][0]["properties"]["analyzer"]["anyOf"][1] = obj__3;
root["allOf"][0]["properties"]["analyzer"]["anyOf"][0] = obj__4;
root["allOf"][0]["properties"]["analyzer"]["anyOf"][1] = obj__3;
root["allOf"][0]["properties"]["searchAnalyzer"]["anyOf"][0] = obj__4;
root["allOf"][0]["properties"]["searchAnalyzer"]["anyOf"][1] = obj__3;
root["allOf"][0]["properties"]["analyzers"]["items"]["properties"]["charFilters"]["items"]["oneOf"][2]["properties"]["mappings"]["propertyNames"] = obj__3;
root["allOf"][0]["properties"]["storedSource"]["examples"][0] = obj__11;
root["allOf"][0]["properties"]["storedSource"]["examples"][1] = obj__12;
root["allOf"][0]["properties"]["storedSource"]["oneOf"][0] = obj__13;
root["allOf"][0]["properties"]["storedSource"]["oneOf"][1] = obj__14;


export default root;
