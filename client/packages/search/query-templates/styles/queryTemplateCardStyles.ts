import { css } from '@emotion/react';
import { palette } from '@leafygreen-ui/palette';
import { fontFamilies, spacing } from '@leafygreen-ui/tokens';

import { styles as aceEditorStyles } from '@packages/search/common/styles/aceEditorStyles';

export const styles = {
  card: css`
    padding: 0;
    overflow: hidden;
    margin-top: ${spacing[4]}px;
  `,

  banner: css`
    &:before {
      background: none;
    }
    border-radius: ${spacing[4]}px ${spacing[4]}px 0 0;
  `,

  body: css`
    p {
      margin-bottom: ${spacing[1]}px;
    }

    padding: ${spacing[4]}px ${spacing[4]}px 0 ${spacing[4]}px;
    display: flex;
  `,

  image: css`
    img {
      width: 72px;
      height: 72px;
    }
    margin-right: ${spacing[4]}px;
  `,

  panel: css`
    flex-grow: 1;
  `,

  contents: css`
    display: flex;
    border-radius: ${spacing[4]}px;
  `,

  header: css`
    display: flex;
    width: 100%;
    margin: ${spacing[2]}px 0 ${spacing[3]}px 0;
  `,

  title: css`
    flex-grow: 1;
    font-weight: bold;
    font-size: 16px;
  `,

  keywords: css`
    margin: 0 ${spacing[2]}px ${spacing[2]}px 0;
  `,

  actionBar: css`
    display: flex;
    justify-content: space-between;
    margin-top: ${spacing[3]}px;
  `,

  templateSelect: css`
    display: flex;
    align-items: center;
    font-weight: bold;
  `,

  templateDropdown: css`
    width: 300px;
    margin-left: ${spacing[2]}px;
  `,

  templateCodeContainer: css`
    margin-top: ${spacing[3]}px;
    border-top: 1px solid ${palette.gray.light2};
    background-color: ${palette.gray.light3};
    display: flex;
    flex-direction: column;
  `,

  showMore: (isDarkMode?: boolean) => css`
    &:hover {
      box-shadow: none;
      border: none;
      background: none;
    }

    border: none;
    background: none;
    color: ${!!isDarkMode ? palette.blue.light1 : palette.blue.base};
    margin: ${spacing[1]}px auto;
  `,

  icon: (isDarkMode?: boolean) => css`
    color: ${!!isDarkMode ? palette.blue.light1 : palette.blue.base};
  `,

  expandContainer: css`
    display: flex;
    margin: 0 ${spacing[4]}px ${spacing[4]}px ${spacing[4]}px;
  `,

  editorContainer: css`
    display: flex;
    flex-grow: 1;
    min-width: 600px;
    border-radius: ${spacing[3]}px;
    overflow: hidden;
    background-color: ${palette.gray.dark3};
  `,

  editorRoot: css`
    padding: ${spacing[2]}px;
    flex-grow: 1;
  `,

  editor: css`
    ${aceEditorStyles.aceEditor};
    .ace_marker-layer {
      display: none;
    }
  `,

  actions: aceEditorStyles.actions,

  relevantFields: css`
    li {
      margin-top: ${spacing[1]}px;
      width: 100px;
    }

    div {
      margin-left: ${spacing[3]}px;
    }
  `,

  code: css`
    font-family: ${fontFamilies.code};
  `,
};
