// TODO: Both the .js and the relative path shouldn't be necessary, but for some reason are
import CloudTeams from '../../cloud-teams/CloudTeams.js';

export const todoSlack = Symbol();
const teamToSlackChannel = {
  [CloudTeams.AlertsPlatform]: { slack: '#cloud-cap-monitoring', sentryTeamId: '4505235387973632' },
  [CloudTeams.Apix]: { slack: '#cloud-apix-alerts', sentryTeamId: '4505517150044160' },
  [CloudTeams.AppServices]: { slack: todoSlack, sentryTeamId: '4505210986561536' },
  [CloudTeams.AtlasI]: {
    // Atlas I became Atlas Dedicated I, which is now Atlas Clusters Performance
    slack: '#atlas-clusters-performance-prod-alerts',
    sentryTeamId: '4508739813834752',
  },
  [CloudTeams.AtlasIi]: {
    // Atlas II became Atlas Dedicated II, which is now Atlas Clusters Security 1
    slack: todoSlack,
    sentryTeamId: '4508739812392960',
  },
  [CloudTeams.AtlasIii]: {
    // Atlas III became Atlas Serverless I, which is now Atlas Foundational Services
    // AFS doesn't have a team in Sentry, so this is the team ID for the "serverless" team
    slack: todoSlack,
    sentryTeamId: '4505518103068672',
  },
  [CloudTeams.AtlasGrowth]: {
    slack: { channel: '#growth-on-duty', channelId: 'C031LERLBT9' },
    sentryTeamId: '4505190195462144',
  },
  // [CloudTeams.AtlasInternTeam]: {},
  // [CloudTeams.AtlasMigration]: {},
  [CloudTeams.AtlasStreams]: { slack: '#mongohouse-alerts-streams', sentryTeamId: '4507012523819008' },
  [CloudTeams.AtlasTriage]: { slack: todoSlack, sentryTeamId: '4505511627849728' },
  // [CloudTeams.AtlasExtendedI]: {},
  [CloudTeams.AtlasServerless]: {
    // Same as AtlasIii, this is now Atlas Foundational Services
    // AFS doesn't have a team in Sentry, so this is the team ID for the "serverless" team
    slack: todoSlack,
    sentryTeamId: '4505518103068672',
  },
  [CloudTeams.AtlasOnlineArchiveII]: {
    slack: todoSlack,
    sentryTeamId: '4508808185446400', // This team is just "online-archive" in Sentry
  },
  // [CloudTeams.AtlasXV]: {},
  [CloudTeams.Automation]: {
    slack: todoSlack,
    sentryTeamId: '4506258539216896',
  },
  [CloudTeams.BaaS]: {
    // BaaS is the same thing as App Services
    slack: todoSlack,
    sentryTeamId: '4505210986561536',
  },
  [CloudTeams.BackupAtlas]: {
    slack: todoSlack,
    sentryTeamId: '4505512079589376',
  },
  [CloudTeams.BackupPrivateCloud]: {
    slack: todoSlack,
    sentryTeamId: '4506622409506816',
  },
  [CloudTeams.CoreBillingAndIntegrations]: {
    slack: '#billing-platform-sentry-alerts', // this is just "billing" in the Sentry UI, but I'm assuming Billing and Integrations is right
    sentryTeamId: '4505235099615232',
  },
  [CloudTeams.CoreIam]: { slack: '#cloud-iam-ops', sentryTeamId: '4505235388825600' },
  // [CloudTeams.Design]: {},
  [CloudTeams.DeveloperProductivity]: {
    slack: todoSlack, // maybe #devprod-performance-team-alerts? That's assigned to DevProd Performance in Sentry
    sentryTeamId: '4505913016451072',
  },
  [CloudTeams.DevTools]: {
    slack: todoSlack,
    sentryTeamId: '4509289489235968',
  },
  // [CloudTeams.EngineeringManagement]: {},
  [CloudTeams.FrontEndPlatform]: {
    slack: { channel: '#atlas-fe-alerts', channelId: 'C05RZL6CN4A' },
    sentryTeamId: '4505240662900736',
  },
  [CloudTeams.InsightsAndTelemetry]: { slack: '#cloud-intel-alerts', sentryTeamId: '4505511457652736' },
  // [CloudTeams.OpsManager]: {},
  // [CloudTeams.Kubernetes]: {},
  [CloudTeams.Payments]: { slack: '#cloud-payments-alerts', sentryTeamId: '4505195636326400' },
  // [CloudTeams.ProgramManagement]: {},
  [CloudTeams.Search]: {
    slack: todoSlack,
    sentryTeamId: '4507501516095488', // This is the team ID for the "atlas-search" team in Sentry
  },
  [CloudTeams.WebSearch]: { slack: '#atlas-search-web-sentry-alerts', sentryTeamId: '4506400271958016' },
  // [CloudTeams.Security]: {},
  // [CloudTeams.Sre]: {},
  // [CloudTeams.Triage]: {},
} as const satisfies Partial<
  Record<
    CloudTeams,
    {
      slack: `#${string}` | { channel: `#${string}`; channelId?: string } | typeof todoSlack;
      sentryTeamId: string;
    }
  >
>;
export default teamToSlackChannel;

// Teams in Sentry config that seems to be missing from the CloudTeams enum:
// Atlas Clusters Availability and Durability: { slack: '#acad-sentry-prod-alerts', sentryTeamId: '4508739813244928' }
// Atlas Live Migrate (is this the same team as AtlasMigration?):
//   { slack: '#atlas-live-migrate-alerts', sentryTeamId: '4508993548255232' }
// Core XV: #ace-core-xv-alerts
// DevProd Performance: #devprod-performance-team-alerts
// Evergreen: #evergreen-ops-alerts
// Metering: #metering-alerts
// Migrator: #migrator-bugsnag
