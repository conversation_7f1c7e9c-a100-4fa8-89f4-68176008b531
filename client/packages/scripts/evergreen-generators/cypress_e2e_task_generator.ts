import fs from 'fs';
import path from 'path';

import { findCypressTestsWithTag } from './find_cypress_tests_with_tag';
import { getTeamTagForTask } from './task_generator_utils';

const taskNamePrefix = 'E2E_CYPRESS_';

const LOG_LABEL = 'cypress_e2e_task';
let LOG_COUNT = 0;
const logDebugInfo = () => console.log(LOG_LABEL, LOG_COUNT++, new Date());

process.on('uncaughtException', (err: Error) => {
  console.log('uncaughtException: shutting down...');
  console.log(err.name, err.message);
  process.exit(1);
});

process.on('unhandledRejection', (err: Error) => {
  console.log(`unhandledRejection: shutting down...`);
  console.log(err.name, err.message);
  process.exit(1);
});

logDebugInfo();

enum TaskName {
  ACCESS_MANAGER = `${taskNamePrefix}ACCESSMANAGER`,
  ACCOUNT = `${taskNamePrefix}ACCOUNT`,
  ACTIVITY = `${taskNamePrefix}ACTIVITY`,
  AICLUSTERASSISTANT = `${taskNamePrefix}AICLUSTERASSISTANT`,
  ALERTCONFIGS = `${taskNamePrefix}ALERTCONFIGS`,
  ALERTS = `${taskNamePrefix}ALERTS`,
  AUTH = `${taskNamePrefix}AUTH`,
  CLOUD_PROVIDER_RESOURCES_AICLUSTERASSISTANT = `${taskNamePrefix}CLOUD_PROVIDER_RESOURCES_AICLUSTERASSISTANT`,
  CLUSTER_DRAFTS = `${taskNamePrefix}CLUSTER_DRAFTS`,
  COMMUNICATION = `${taskNamePrefix}COMMUNICATION`,
  CONFIG_SERVICE = `${taskNamePrefix}CONFIGSERVICE`,
  CRITICAL_ANALYTICS_AUTH = `${taskNamePrefix}CRITICAL_ANALYTICS_AUTH`,
  CRITICAL_ANALYTICS_HOLDOUTS = `${taskNamePrefix}CRITICAL_ANALYTICS_HOLDOUTS`,
  CRITICAL_ANALYTICS_CLUSTERS_FLEX = `${taskNamePrefix}CRITICAL_ANALYTICS_CLUSTERS_FLEX`,
  CRITICAL_ANALYTICS_CLUSTERS_DEDICATED = `${taskNamePrefix}CRITICAL_ANALYTICS_CLUSTERS_DEDICATED`,
  ECOSYSTEM = `${taskNamePrefix}ECOSYSTEM`,
  ORGANIZATION = `${taskNamePrefix}ORGANIZATION`,
  SERVICE_ACCOUNTS = `${taskNamePrefix}SERVICEACCOUNTS`,
  UI = `${taskNamePrefix}UI`,
  VERCEL = `${taskNamePrefix}VERCEL`,
  FEDERATION = `${taskNamePrefix}FEDERATION`,
  ATLAS_TEST_PLAN = `${taskNamePrefix}CLOUD_PROVIDER_RESOURCES_ATLAS_TEST_PLAN`,
  FPP = `${taskNamePrefix}FPP`,
  ATLASHOMEPAGE = `${taskNamePrefix}ATLASHOMEPAGE`,
  ATLASSECURITYQUICKSTART = `${taskNamePrefix}ATLASSECURITYQUICKSTART`,
  BILLING = `${taskNamePrefix}BILLING`,
}

// MANUALLY MAINTAINED CONFIG OBJECTS:

// MANUALLY UPDATED mapping of task names and their desired MMS properties file IF
// they do NOT want to use the default properties file for their variant (as defined in variantProps below).
//
// For example, if the default properties file for a variant has Okta disabled, this can be used to specify
// certain Cypress test tasks should run with Okta enabled by specifying a different MMS properties file.
//
// Ideally the default properties file is less resource-intensive and permissive and the overrides use the more
// resource-intensive/permissive properties only for test tasks that require it.
//
// MMS properties files are located at: mms/server/conf
//
// IMPORTANT: e2e_cypress variant tasks should ONLY use either conf-local-e2e-analytics.properties or conf-local-e2e-analytics-okta.properties!
// For more context, please see https://wiki.corp.mongodb.com/display/MMS/Cloud+Cypress+E2E+Tests
const TASK_PROPERTIES_OVERRIDES: Partial<Record<TaskName, TaskOverrideConfig>> = {
  [TaskName.ACCOUNT]: { propertiesFile: 'conf-local-e2e-analytics-okta.properties' },
  [TaskName.AICLUSTERASSISTANT]: { propertiesFile: 'conf-local-e2e-ai-cluster-assistant-overrides.properties' },
  [TaskName.AUTH]: { propertiesFile: 'conf-local-e2e-analytics-okta.properties' },
  [TaskName.CLOUD_PROVIDER_RESOURCES_AICLUSTERASSISTANT]: {
    propertiesFile: 'conf-local-e2e-ai-cluster-assistant-overrides-cloud-provider-resources.properties',
  },
  [TaskName.CLUSTER_DRAFTS]: { propertiesFile: 'conf-local-e2e-analytics.properties' },
  [TaskName.COMMUNICATION]: { propertiesFile: 'conf-local-e2e-communication-overrides.properties' },
  [TaskName.CRITICAL_ANALYTICS_AUTH]: { propertiesFile: 'conf-local-e2e-analytics-auth-overrides.properties' },
  [TaskName.CRITICAL_ANALYTICS_HOLDOUTS]: { propertiesFile: 'conf-local-e2e-analytics-holdout-overrides.properties' },
  [TaskName.ECOSYSTEM]: { propertiesFile: 'conf-local-e2e-analytics.properties' },
  [TaskName.FEDERATION]: { propertiesFile: 'conf-local-e2e-analytics.properties' },
  [TaskName.ORGANIZATION]: { propertiesFile: 'conf-local-e2e-organization.properties' },
  [TaskName.UI]: { propertiesFile: 'conf-local-e2e-analytics.properties' },
  [TaskName.VERCEL]: { propertiesFile: 'conf-local-e2e-analytics-okta.properties' },
  [TaskName.ATLAS_TEST_PLAN]: { batchtime: 1440 },
};

// MMS-required standalone services are started as part of the "start mms" function
const TASKS_WITH_ADDITIONAL_STANDALONE_SERVICES: Set<TaskName> = new Set([
  TaskName.ORGANIZATION,
  TaskName.SERVICE_ACCOUNTS,
  TaskName.ACCESS_MANAGER,
  TaskName.ACTIVITY,
  TaskName.FPP,
  TaskName.ATLASHOMEPAGE,
  TaskName.ATLASSECURITYQUICKSTART,
  TaskName.BILLING,
  TaskName.CRITICAL_ANALYTICS_CLUSTERS_FLEX,
  TaskName.CRITICAL_ANALYTICS_CLUSTERS_DEDICATED,
]);

const TASKS_WITH_AUTHZ_SERVICES: Set<TaskName> = new Set([
  TaskName.ORGANIZATION,
  TaskName.SERVICE_ACCOUNTS,
  TaskName.ACCESS_MANAGER,
]);

const TASKS_WITH_EVENT_SERVICES: Set<TaskName> = new Set([TaskName.ACTIVITY]);

const TASKS_WITH_PAYMENT_SERVICES: Set<TaskName> = new Set([
  TaskName.FPP,
  TaskName.ATLASHOMEPAGE,
  TaskName.ATLASSECURITYQUICKSTART,
  TaskName.BILLING,
  TaskName.ORGANIZATION,
  TaskName.CRITICAL_ANALYTICS_CLUSTERS_FLEX,
  TaskName.CRITICAL_ANALYTICS_CLUSTERS_DEDICATED,
]);

logDebugInfo();

// MANUALLY UPDATED config object. If a new 'mms/client/cypress' root test folder is added
// (examples: mms/client/cypress/e2e, mms/client/cypress/e2e-dev) or variant type is needed,
// add to this object with the appropriate props.
const variantProps: Partial<Record<CypressVariant, CypressVariantConfig>> = {
  local: {
    dir: `cypress/e2e/`,
    standardServerVariantInfo: {
      name: 'e2e_cypress',
      displayName: '06b-1. E2E Cypress Local',
      // TEMPORARILY SKIP UNTIL TIMEOUT ISSUES FIXED. SEE:
      // https://jira.mongodb.org/browse/DEVPROD-15519
      // https://jira.mongodb.org/browse/CLOUDP-246424
      tags: ['foliage_skip'],
    },
    latestServerVariantInfo: {
      name: 'e2e_cypress_mdb_latest',
      displayName: 'h7b-1. E2E Cypress Local (MDB latest)',
      // only do this at most once a day
      batchtime: 1440,
      expansions: {
        mongodb_version: 'latest',
      },
    },
    newNavVariantInfo: {
      name: 'e2e_cypress_new_nav',
      displayName: '06b-5. E2E Cypress Local (with CloudNav)',
      // only do this at most once a day
      batchtime: 1440,
      expansions: {
        should_use_cloud_nav: 'true',
      },
      tags: ['foliage_skip'],
    },
    defaultMMSPropertiesFile: 'conf-local-e2e-overrides.properties',
  },
  dev: {
    // Used by the BAAS team because a running BAAS server is needed and cannot be started natively through evergreen
    // at this time
    dir: `cypress/e2e-dev/`,
    standardServerVariantInfo: {
      name: 'e2e_cypress_dev',
      displayName: '06b-2. E2E Cypress Dev BAAS',
      // run at most once every 8 hours
      batchtime: 480,
      variantTeamOwnerTag: 'assigned_to_jira_team_baas',
    },
    defaultMMSPropertiesFile: 'conf-dev.properties',
    // Leaving out "latestServerVariantInfo" intentionally, as the whole point
    // of tasks in this variant is that they run against Cloud-Dev server
  },
  criticalAnalytics: {
    // directory is intentionally the same as the "local" variant, as the test suites are co-located
    // the tests for this variant are identifiable by having the @critical-analytics tag
    dir: `cypress/e2e/`,
    standardServerVariantInfo: {
      name: 'e2e_cypress_critical_analytics',
      displayName: '06b-3. E2E Cypress Critical Analytics',
      // run at most once every 8 hours
      batchtime: 480,
      variantTeamOwnerTag: 'assigned_to_jira_team_cloudp_atlas_growth_triage',
    },
    taggedTestsLookahead: findCypressTestsWithTag('@critical-analytics'),
    defaultMMSPropertiesFile: 'conf-local-e2e-analytics-cloud-provider-resources.properties',
  },
  cloudProviderResources: {
    dir: `cypress/e2e-cloud-provider-resources/`,
    standardServerVariantInfo: {
      name: 'e2e_cypress_cloud_provider_resources',
      displayName: '06b-4. E2E Cypress Cloud Provider Resources',
      // run at most once every 8 hours
      batchtime: 480,
      variantTeamOwnerTag: 'assigned_to_jira_team_cloudp_atlas_growth_triage',
    },
    newNavVariantInfo: {
      name: 'e2e_cypress_cloud_provider_resources_new_nav',
      displayName: '06b-4a. E2E Cypress Cloud Provider Resources (with CloudNav)',
      // only do this at most once a day
      batchtime: 1440,
      expansions: {
        should_use_cloud_nav: 'true',
      },
      tags: ['foliage_skip'],
    },
    defaultMMSPropertiesFile: 'conf-local-e2e-analytics-cloud-provider-resources.properties',
  },
  security: {
    dir: `cypress/e2e-security/`,
    standardServerVariantInfo: {
      name: 'e2e_cypress_security',
      displayName: '06b-4. E2E Cypress Security Tests',
      batchtime: 1440,
    },
    // Not used, add it here to avoid script failures
    defaultMMSPropertiesFile: 'conf-dev.properties',
    // To only run in commit builds not patches
    patchable: false,
  },
  localGov: {
    dir: `cypress/e2e-local-gov/`,
    standardServerVariantInfo: {
      name: 'e2e_cypress_local_gov',
      displayName: '06b-6. E2E Cypress Local Gov',
      // run at most once every 8 hours
      batchtime: 480,
      variantTeamOwnerTag: 'assigned_to_jira_team_cloudp_atlas_growth_triage',
    },
    defaultMMSPropertiesFile: 'conf-local-e2e-overrides.properties',
  },
};

logDebugInfo();

// ---- end of manually maintained config objects

// CONSTANTS, ENV VARS, ETC

const CLIENT_DIR_PATH = path.resolve(__dirname, '../../../');

// Env vars and args passed in by Evergreen
const shouldRunLatestOverride = process.env.SHOULD_RUN_LATEST_OVERRIDE === 'true';
const shouldRunNewNavOverride = process.env.SHOULD_RUN_CLOUD_NAV === 'true';
const isVerifyOnly = process.env.VERIFY_ONLY === 'true'; // Verify only mode for not generating more tasks
const isPr = process.env.TASK_REQUESTER === 'github_pr';
const findPrTaggedTasksOnly = isPr && !isVerifyOnly; // For verifying purposes, we want all tasks
const isPatch = ['patch', 'github_pr'].includes(process.env.TASK_REQUESTER || '');
const specifiedTasks: Array<string> = process.env.E2E_CYPRESS_TASK_GENERATOR_TARGETS
  ? process.env.E2E_CYPRESS_TASK_GENERATOR_TARGETS.split(' ')
  : [];

// Tasks are filtered later on in the flow, but we'd like to error if specified
// task names are invalid, as this could lead to false positives for test runs.
const validateSpecifiedTasks = (tasks: Array<string>) => {
  tasks.forEach((taskName) => {
    getTeamTagForTask(taskName);
  });
};

logDebugInfo();

validateSpecifiedTasks(specifiedTasks);

logDebugInfo();

// leaving these in for debugging purposes
console.info('requester property: ', process.env.TASK_REQUESTER);
console.info('isVerifyOnly: ', isVerifyOnly);
console.info('isPr: ', isPr);
console.info('isPatch: ', isPatch);

// standard S3 upload function for these Cypress tasks
const s3UploadSnapshotsCommandDef = {
  command: 's3.put',
  params: {
    aws_key: '${mciuploads_aws_key}',
    aws_secret: '${mciuploads_aws_secret}',
    local_files_include_filter: [
      'mms/client/cypress/**/__image_snapshots__/*.actual.png',
      'mms/client/cypress/**/__image_snapshots__/*.diff.png',
    ],
    remote_file: '${mciuploads_dir|mms}/${build_id}/${task_name}/${execution}/',
    bucket: 'mciuploads',
    permissions: 'public-read',
    content_type: '${content_type|image/png}',
    display_name: '(Screenshot)',
  },
};

const s3UploadMockServerLogsCommandDef = {
  command: 's3.put',
  params: {
    aws_key: '${mciuploads_aws_key}',
    aws_secret: '${mciuploads_aws_secret}',
    local_files_include_filter: ['mms/client/cypress/logs/mock-server-requests-logs-*.json'],
    remote_file: '${mciuploads_dir|mms}/${build_id}/${task_name}/${execution}/',
    bucket: 'mciuploads',
    permissions: 'public-read',
    content_type: '${content_type|application/json}',
    display_name: '(Mock Server Requests)',
  },
};

// If cypress run exits with a 1, it might not have run any tests at all
// We should ensure that we tell Evergreen to fail this task. If it is > 1
// then Cy ran and that number of tests failed. We can also expect the XUnit
// xml files to exist, so attaching those will fail the test. Regardless, to be
// safe, we should simply fail the test if Cypress gives back something non-zero.
const postFailureIfNonZeroCode = `cypress_exit_code=$?; \
          if [ $cypress_exit_code -ne 0 ]; then
            set +v
            source "../scripts/evergreen/task.bash"
            set -v
            post_evergreen_status failed test "Cypress failed" true
          fi`;

// ---- end of constants, env vars, etc

// HELPER FUNCTIONS

function getLocalE2ECukesSecureSetupForTaskName(taskName: string): Commands {
  if (TASKS_WITH_ADDITIONAL_STANDALONE_SERVICES.has(taskName as TaskName)) {
    return [
      {
        func: 'setup conf-cukes-secure for e2e',
        vars: {
          conf_local: 'true',
          conf_cukes: 'true',
        },
      },
    ];
  }

  return [];
}

function getLocalE2EStandaloneServiceSetupForTaskName(taskName: string): Commands {
  let commands: Commands = [];
  if (TASKS_WITH_AUTHZ_SERVICES.has(taskName as TaskName)) {
    commands.push(
      {
        func: 'setup service-test-secure for standalone services',
        vars: {
          environment: 'local',
        },
      },
      {
        func: 'start standalone service',
        vars: {
          serviceName: 'authz-service',
        },
      },
      {
        func: 'wait for standalone service',
        vars: {
          servicePort: 50059,
        },
      }
    );
  }
  if (TASKS_WITH_EVENT_SERVICES.has(taskName as TaskName)) {
    commands.push(
      {
        func: 'setup service-test-secure for standalone services',
        vars: {
          environment: 'local',
        },
      },
      {
        func: 'start systems component',
        vars: {
          systemName: 'events',
          componentName: 'api',
        },
      },
      {
        func: 'wait for standalone service',
        vars: {
          servicePort: 50505,
        },
      }
    );
  }
  if (TASKS_WITH_PAYMENT_SERVICES.has(taskName as TaskName)) {
    commands.push(
      {
        func: 'setup service-test-secure for standalone services',
        vars: {
          environment: 'local',
        },
      },
      {
        func: 'start standalone service',
        vars: {
          serviceName: 'payments-service',
          extraPropFile: 'payments-service-local-e2e-overrides.yaml',
        },
      },
      {
        func: 'wait for standalone service',
        vars: {
          servicePort: 50055,
        },
      }
    );
  }
  return commands;
}

const getLocalEnvE2ESteps = (
  variant: CypressVariant,
  taskName: string,
  cypressFlagsStr: string,
  isLocalGov = false
): Commands => {
  return [
    { func: 'set caching expansions' },
    { func: 'fetch nodejs' },
    { func: 'run pnpm install' },
    { func: 'update conf-local if we want to test cloudnav' },
    ...getLocalE2ECukesSecureSetupForTaskName(taskName),
    { func: 'install mongodb and tools' },
    {
      func: 'start mongod single node replicaset',
    },
    {
      func: 'wait for single node mongod replicaset',
    },
    { func: 'set local centralUrl' },
    {
      // not all scenarios need serverless functionality, but doesn't hurt just to have it
      // this function also starts authn service and config service
      func: isLocalGov ? 'start atlas mms' : 'start atlas mms serverless',
      vars: {
        extraPropFile: getMMSPropertiesFile(variant, taskName),
        serverEnv: isLocalGov ? 'local-gov' : 'local',
      },
    },
    ...getLocalE2EStandaloneServiceSetupForTaskName(taskName),
    { func: 'register cloud provider observers' },
    {
      command: 'shell.exec',
      timeout_secs: 1800, // in seconds, so 30 min. "maximum amount of time that can elapse without any output on stdout"
      params: {
        working_dir: 'mms/client',
        include_expansions_in_env: ['CYPRESS_CACHE_FOLDER'],
        // the mix of template literals and concatenation is intentional unfortunately
        script:
          `set -uvx; \
          export TERM=xterm-256color; \
          export NODEJS_HOME=\$\{workdir\}/mms/nodejs; \
          export PATH=$NODEJS_HOME/bin:$PATH; ` +
          'CYPRESS_screenshotsFolder=../server/build/screenshots ' +
          'CYPRESS_videosFolder=../server/build/videos ' +
          'CYPRESS_baseUrl=http://localhost:9080 ' +
          'CYPRESS_paypalEmail=${payments_paypal_e2e_dev_email} ' +
          "CYPRESS_paypalPassword='${payments_paypal_e2e_dev_password}' " +
          "CYPRESS_isCloudNavEnabled='${should_use_cloud_nav}' " +
          'DEBUG=cypress:server:*,-cypress:server:util:process_profiler,cypress:data-context:sources:*,cypress:launcher:*,cypress:network:*,cypress:net-stubbing:* ' +
          `pnpm cypress:run ${cypressFlagsStr};` +
          postFailureIfNonZeroCode,
        // continue_on_err so all artifacts get uploaded and cleanup is done
        continue_on_err: true,
      },
    },
    { func: 'clean from cloud provider observers' },
    { func: 'generate automation troubleshooting report' },
    { func: 'upload automation troubleshooting report' },
    s3UploadMockServerLogsCommandDef,
    s3UploadSnapshotsCommandDef,
  ];
};

const generateVariantConfig = (variantConfig: VariantProperties): BuildVariantEvergreenConfig => {
  const template: BuildVariantEvergreenConfig = {
    name: variantConfig.name,
    display_name: `${variantConfig.displayName} [${variantConfig.name}]`,
    // this is hard to run on anything __other__ than AL2 right now.
    // We run MongoDB v4.x, which is not available on AL2023
    // and when run on rhel80, mms_init_local fails with: "lsof: command not found".
    // However, this runs the cypress app and Chrome directly anyway, so nothing is
    // really run through NodeJS anyway
    run_on: ['amazon2-cloud-large'],
    expansions: {
      platform: 'linux',
      arch: 'x86_64',
      mms_build_arch: 'amd64',
      mongodBasePort: 27017,
      mongodb_arch: 'x86_64-amazon2',
    },
    tags: variantConfig.tags || [],
    tasks: [],
  };

  if (variantConfig.batchtime) {
    template.batchtime = variantConfig.batchtime;
    template.stepback = false;
  }

  if (variantConfig.expansions) {
    template.expansions = {
      ...template.expansions,
      ...variantConfig.expansions,
    };
  }

  if (variantConfig.variantTeamOwnerTag) {
    template.tags.push(variantConfig.variantTeamOwnerTag);
  }

  return template;
};

const getTaskBatchtimeOverride = (taskName: string): number | undefined => {
  return TASK_PROPERTIES_OVERRIDES[taskName as TaskName]?.batchtime;
};

const getMMSPropertiesFile = (variant: CypressVariant, taskName: string): string => {
  if (!variantProps[variant] || !variantProps[variant]?.defaultMMSPropertiesFile) {
    throw new Error(
      `Can't call getMMSPropertiesFile without a default properties file defined for the variant: ${variant}`
    );
  }

  return (
    TASK_PROPERTIES_OVERRIDES[taskName as TaskName]?.propertiesFile || variantProps[variant]!.defaultMMSPropertiesFile!
  );
};

const getTaskName = ({ name, specGlob }: { name: string; specGlob: string }) => {
  // matches the last folder name preceeding the "**/*"" in the glob
  const taskNameMatcher = /\/(\w*\d*)\/\*\*\/\*$/;
  const cypressTestFolderName = specGlob.match(taskNameMatcher)![1];
  return `${name.toUpperCase()}_${cypressTestFolderName.toUpperCase()}`;
};

const generateCypressTaskConfig = (variant: CypressVariant, name: string, specGlob: string): TaskEvergreenConfig => {
  const taskName = getTaskName({ name, specGlob });

  const templateBase = {
    // will generate a name like "E2E_CYPRESS_AUTH" or "E2E_CYPRESS_DEV_DATAAPI"
    name: taskName,
  };
  const batchtimeOverride = getTaskBatchtimeOverride(taskName);

  const commandsMap: Record<CypressVariant, Array<Record<string, Json>>> = {
    local: getLocalEnvE2ESteps(
      variant,
      taskName,
      `--env grepTags="--@critical-analytics ${findPrTaggedTasksOnly ? '@pr' : ''}" --spec "${specGlob}"`
    ),
    dev: [
      { func: 'early exit if not mms project' },
      { func: 'merge in base branch' },
      { func: 'set caching expansions' },
      { func: 'fetch nodejs' },
      { func: 'run pnpm install' },
      {
        command: 'shell.exec',
        timeout_secs: 1800, // in seconds, so 30 min. "maximum amount of time that can elapse without any output on stdout"
        params: {
          working_dir: 'mms/client',
          include_expansions_in_env: ['CYPRESS_CACHE_FOLDER'],
          // the mix of template literals and concatenation is intentional unfortunately
          script:
            `set -euvx; \
            export TERM=xterm-256color; \
            export NODEJS_HOME=\$\{workdir\}/mms/nodejs; \
            export PATH=$NODEJS_HOME/bin:$PATH; ` +
            'CYPRESS_screenshotsFolder=../server/build/screenshots ' +
            'CYPRESS_videosFolder=../server/build/videos ' +
            'CYPRESS_baseUrl=${e2e_url} ' +
            'CYPRESS_baasUrl=${baas_e2e_dev_url} ' +
            'CYPRESS_baasGroupId=${baas_e2e_dev_group_id} ' +
            'CYPRESS_baasUsername=${baas_e2e_dev_username} ' +
            'CYPRESS_baasPw=${baas_e2e_dev_pw} ' +
            'CYPRESS_baasPublicKey=${baas_e2e_dev_public_key} ' +
            'CYPRESS_baasPrivateKey=${baas_e2e_dev_private_key} ' +
            `pnpm cypress:run-dev --env grepTags="${findPrTaggedTasksOnly ? '@pr' : ''}" --spec "client/${specGlob}";` +
            postFailureIfNonZeroCode,
          // continue_on_err so all artifacts get uploaded
          continue_on_err: true,
        },
      },
      s3UploadSnapshotsCommandDef,
    ],
    criticalAnalytics: getLocalEnvE2ESteps(
      variant,
      taskName,
      `--env grepTags="@critical-analytics ${findPrTaggedTasksOnly ? '@pr' : ''}" --spec "${specGlob}"`
    ),
    cloudProviderResources: getLocalEnvE2ESteps(variant, taskName, `--spec "${specGlob}"`),
    security: [
      { func: 'merge in base branch' },
      { func: 'set caching expansions' },
      { func: 'fetch nodejs' },
      { func: 'run pnpm install' },
      {
        command: 'shell.exec',
        params: {
          working_dir: 'mms/client',
          include_expansions_in_env: ['CYPRESS_CACHE_FOLDER'],
          silent: true,
          // the mix of template literals and concatenation is intentional unfortunately
          script:
            `set -euvx; \
            export TERM=xterm-256color; \
            export NODEJS_HOME=\$\{workdir\}/mms/nodejs; \
            export PATH=$NODEJS_HOME/bin:$PATH; ` +
            'CYPRESS_screenshotsFolder=../server/build/screenshots ' +
            'CYPRESS_videosFolder=../server/build/videos ' +
            'CYPRESS_cloudsecClusterUsername=${cloudsec_e2e_prod_username} ' +
            'CYPRESS_cloudsecClusterPassword=${cloudsec_e2e_prod_pw} ' +
            'CYPRESS_cloudsecPublicKey=${cloudsec_e2e_prod_public_key} ' +
            'CYPRESS_cloudsecPrivateKey=${cloudsec_e2e_prod_private_key} ' +
            'CYPRESS_cloudsecOrgId=${cloudsec_e2e_prod_org_id} ' +
            'CYPRESS_cloudsecProjectId=${cloudsec_e2e_prod_proj_id} ' +
            'CYPRESS_baasUrl=${cloudsec_baas_prod_url} ' +
            'CYPRESS_baasGroupId=${cloudsec_e2e_prod_proj_id} ' +
            `pnpm cypress:run-security --env grepTags="@security" --spec "client/${specGlob}";` +
            postFailureIfNonZeroCode,
          // continue_on_err so all artifacts get uploaded
          continue_on_err: true,
        },
      },
      s3UploadSnapshotsCommandDef,
    ],
    localGov: getLocalEnvE2ESteps(variant, taskName, `--spec "${specGlob}"`, true),
  };

  return {
    ...templateBase,
    tags: [getTeamTagForTask(taskName)],
    commands: commandsMap[variant],
    // add patchable: false if task should only run in commit builds
    patchable: variantProps[variant]?.patchable !== false,
    ...(batchtimeOverride && { batchtime: batchtimeOverride }),
  };
};

// given a dir (eg "cypress/e2e/FPP") and a JSON object with a list of test files with the desired tag,
// returns true if that dir is found in the JSON object (aka it contains relevant tests)
const doesDirContainTaggedTests = (dir: string, taggedTestsLookahead: Record<string, Json>): boolean => {
  return Object.keys(taggedTestsLookahead).some((fileName) => fileName.startsWith(dir));
};

// given a variant, returns array of relevant subdir paths relative to the mms/client folder
// "relevant": for variants requiring specific tags, filters out non-applicable dirs
const getVariantDirsRelativeToClientFolder = (variantConfig: CypressVariantConfig): Array<string> => {
  const pathFromClientDir = variantConfig.dir;
  const dirAbsolutePath = path.join(CLIENT_DIR_PATH, pathFromClientDir);

  const subDirs: Array<string> = [];

  fs.readdirSync(dirAbsolutePath).forEach((item: string) => {
    const itemAbsolutePath: string = path.join(dirAbsolutePath, item);
    const itemPathFromClientDir: string = path.join(pathFromClientDir, item);

    if (!fs.statSync(itemAbsolutePath).isDirectory()) {
      throw new Error(
        `Top-level non-directories in Cypress test folders are not allowed. File found: ${itemAbsolutePath}`
      );
    }

    // For variants that have this value defined (like critical-analytics), assume we don't want to add the dir by default
    // For variants that don't have this value defined (like local), assume we do want to add the dir
    // Situations that use tagged tests (like PRs and variants that define taggedTestsLookahead) will adjust this value
    // in the next block
    let shouldAddDir = !variantConfig.taggedTestsLookahead;

    // Currently these blocks are MUTUALLY EXCLUSIVE.
    // AKA if a PR, no critical-analytics variant (for example) will ever be generated.
    // If taggedTestsLookahead (aka find-cypress-specs) is updated to support "AND" tag filters
    // (https://github.com/bahmutov/find-cypress-specs/issues/51), this can be updated.
    if (isVerifyOnly) {
      // If we are in the isVerifyOnly case, we are looking to add all directories
      if (variantConfig.taggedTestsLookahead) {
        shouldAddDir = doesDirContainTaggedTests(itemPathFromClientDir, variantConfig.taggedTestsLookahead);
      }
    } else if (isPr && !variantConfig.taggedTestsLookahead) {
      shouldAddDir = doesDirContainTaggedTests(itemPathFromClientDir, findCypressTestsWithTag('@pr'));
    } else if (!isPr && variantConfig.taggedTestsLookahead) {
      shouldAddDir = doesDirContainTaggedTests(itemPathFromClientDir, variantConfig.taggedTestsLookahead);
    }

    if (shouldAddDir) {
      subDirs.push(itemPathFromClientDir);
    }
  });

  console.info('generated list of relevant directories for the variant:');
  console.info(variantConfig.standardServerVariantInfo.name);
  console.info(subDirs);
  console.info('-----');

  return subDirs;
};

// ---- end of helper functions definitions

// MAIN

// building out the JSON config
const config: EvergreenConfig = {
  buildvariants: [],
  tasks: [],
};

// get map of all folders with relevant tests for each variant:
// { variant: [...folders using variant...] }
// example:
// targetDirs =
// { local: [ 'cypress/e2e/auth', 'cypress/e2e/account', ... ],
//   dev: [ 'cypress/e2e-dev/dataApi', ...] }
const targetDirs: Partial<Record<CypressVariant, Array<string>>> = {};
Object.entries(variantProps).forEach(([weakVariantType, variantConfig]) => {
  const variant = weakVariantType as CypressVariant;
  targetDirs[variant] = getVariantDirsRelativeToClientFolder(variantConfig);
});

// generate json object by iterating over all identified folders per variant
Object.entries(targetDirs).forEach(([weakVariantType, dirs]) => {
  const variant = weakVariantType as CypressVariant;

  const { standardServerVariantInfo, latestServerVariantInfo, newNavVariantInfo } = variantProps[variant]!;

  const buildVariantConfig = generateVariantConfig(standardServerVariantInfo);
  config.buildvariants.push(buildVariantConfig);

  // add the "latest server" variant config as well if variantProps define one and is not a patch
  // (or if overridden to run on patch)
  let buildVariantLatestServerConfig: BuildVariantEvergreenConfig | undefined;
  let buildVariantNewNavServerConfig: BuildVariantEvergreenConfig | undefined;

  const shouldRunLatestVariant = latestServerVariantInfo && (!isPatch || shouldRunLatestOverride);
  if (shouldRunLatestVariant) {
    buildVariantLatestServerConfig = generateVariantConfig(latestServerVariantInfo);
    config.buildvariants.push(buildVariantLatestServerConfig);
  }

  const shouldRunNewNavVariant = newNavVariantInfo && shouldRunNewNavOverride;
  if (shouldRunNewNavVariant) {
    buildVariantNewNavServerConfig = generateVariantConfig(newNavVariantInfo);
    config.buildvariants.push(buildVariantNewNavServerConfig);
  }

  dirs.forEach((dir) => {
    // append the task block for that dir
    // example specGlob value: "cypress/e2e/auth/**/*"
    const cypressSpecGlob = `${dir}/**/*`;
    if (isVerifyOnly) {
      const taskName = getTaskName({
        name: standardServerVariantInfo.name,
        specGlob: cypressSpecGlob,
      });
      getTeamTagForTask(taskName);
      return;
    }

    const task = generateCypressTaskConfig(variant, standardServerVariantInfo.name, cypressSpecGlob);

    const isSpecifiedTask = !specifiedTasks.length || specifiedTasks.includes(task.name);
    if (isSpecifiedTask) {
      config.tasks.push(task);

      // add the task name to the variant tasks array
      buildVariantConfig.tasks.push(task.name);

      if (buildVariantLatestServerConfig) {
        buildVariantLatestServerConfig.tasks.push(task.name);
      }

      if (buildVariantNewNavServerConfig) {
        buildVariantNewNavServerConfig.tasks.push(task.name);
      }
    }
  });
});

// filter out variants with no tasks (either due to no relevant dirs or no specified tasks)
// (Evergreen task generator command throws an error if it gets a build variant with no tasks)
config.buildvariants = config.buildvariants.filter((variant) => variant.tasks.length);

const jsonResultStr = JSON.stringify(config, null, 2);
// While what's really important is outputting to file, it's nice to see the result in console too
// for ease of reference
console.info(jsonResultStr);
// bazel run will allow us to put this in mms
const outdir = process.env.BUILD_WORKSPACE_DIRECTORY ? process.env.BUILD_WORKSPACE_DIRECTORY : __dirname;
fs.writeFileSync(path.join(outdir, 'cypress-e2e-tasks.json'), jsonResultStr, 'utf-8');

/**
 * due to some unknown reason, when using require(<file_name>) to load a file,
 * if that file imports realm sdk lib then this issue happens and causes the script
 * to hang. find-cypress-specs cli uses require(<file_name>) to load and scan cypress files.
 *
 * This is captured in CLOUDP-219781.
 */
process.exit(0);
