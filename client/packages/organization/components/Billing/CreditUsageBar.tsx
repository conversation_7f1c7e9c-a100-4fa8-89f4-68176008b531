import * as React from 'react';

import { palette } from '@leafygreen-ui/palette';

export interface CreditUsageBarProps {
  usedCents: string | number;
  maxAmountCents?: number;
  customLabel?: string;
  style?: React.CSSProperties;
  isActive?: boolean;
  height?: string;
  width?: string;
  background?: string;
}

const CreditUsageBar = ({
  maxAmountCents,
  usedCents,
  style,
  isActive = false,
  height = '7px',
  width = '77px',
  background = undefined,
}: CreditUsageBarProps) => {
  const getFillerWidth = (
    maxAmountCentsValue: CreditUsageBarProps['maxAmountCents'],
    usedCentsValue: CreditUsageBarProps['usedCents']
  ) => {
    if (maxAmountCentsValue) {
      const ratio = Number(usedCentsValue) / maxAmountCentsValue;
      return ratio > 1 ? '100%' : `${ratio * 100}%`;
    }
    return 0;
  };

  const fillerWidth = getFillerWidth(maxAmountCents, usedCents);

  const containerStyles: React.CSSProperties = {
    height: height,
    background:
      background ??
      (isActive
        ? 'repeating-linear-gradient(' +
          '60deg,' +
          'rgba(19, 170, 82, 0.15),' +
          'rgba(19, 170, 82, 0.15) 2px,' +
          'rgba(61, 79, 88, 0.25) 2px,' +
          'rgba(61, 79, 88, 0.25) 4px)'
        : 'rgba(19, 170, 82, 0.15)'),
    borderRadius: '4px',
    width: width,
  };

  const fillerStyles: React.CSSProperties = {
    height: height,
    width: fillerWidth,
    backgroundColor: palette.green.dark1,
    borderRadius: '4px',
  };

  return (
    <div role="progressbar" style={style}>
      <div style={containerStyles}>{isActive && <div style={fillerStyles} />}</div>
    </div>
  );
};

export default CreditUsageBar;
