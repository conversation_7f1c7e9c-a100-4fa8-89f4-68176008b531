import { useEffect, useState } from 'react';

import styled from '@emotion/styled';
import Badge, { Variant as BadgeVariant } from '@leafygreen-ui/badge';
import Banner from '@leafygreen-ui/banner';
import But<PERSON>, { Variant as ButtonVariant } from '@leafygreen-ui/button';
import Card from '@leafygreen-ui/card';
import Icon from '@leafygreen-ui/icon';
import InlineDefinition from '@leafygreen-ui/inline-definition';
import { useToast, Variant } from '@leafygreen-ui/toast';
import { Body, Link } from '@leafygreen-ui/typography';
import { useSelector } from 'react-redux';

import { StyledBody, StyledCard } from '@packages/types/leafygreen-emotion';
import { AppEnv } from '@packages/types/RequestParams';

import * as settings from '@packages/redux/common/settings';

import * as api from '@packages/common/services/api';
import { isAppEnvGov } from '@packages/common/utils/envUtils';
import {
  convertSecondsToDurationString,
  convertSecondsToTimeoutDuration,
  convertTimeoutDurationToString,
} from '@packages/common/utils/timeoutDuration';
import { CardTitleDiv, DescriptionContainer, LargeSettingsTitleCard } from '@packages/components/styles/settingsPage';

import {
  absoluteSessionTimeoutOptions,
  IDLE_SESSION_TIMEOUT_UNSET,
  idleSessionTimeoutOptionsCommercial,
  idleSessionTimeoutOptionsGov,
} from './constants';
import SessionTimeoutInputGroup from './SessionTimeoutInputGroup';

export const TestSelector = {
  CUSTOM_SESSION_TIMEOUT_CARD: 'custom-session-timeout-card',
  EditButton: 'edit-button',
  DocsLink: 'docs-link',
  AbsoluteSessionTimeoutDisplayContainer: 'absolute-session-timeout-display-container',
  IdleSessionTimeoutDisplayContainer: 'idle-session-timeout-display-container',
  CancelButton: 'cancel-button',
  SaveButton: 'save-button',
  EditCard: 'edit-card',
  AbsoluteSessionTimeoutInputGroup: 'absolute-session-timeout-input-group',
  IdleSessionTimeoutInputGroup: 'idle-session-timeout-input-group',
  AbsoluteSessionTimeoutErrorMessage: 'absolute-session-timeout-error-message',
  IdleSessionTimeoutErrorMessage: 'idle-session-timeout-error-message',
  ErrorBanner: 'error-banner',
};

const StyledTitle = styled.div({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
});

const ErrorBanner = styled(Banner)({
  marginTop: '12px',
});

const TimeoutInfoContainer = styled.div({
  marginTop: '12px',
});

const TimeoutDefinitionText = styled<StyledBody>(Body)({ cursor: 'pointer' });

const EditCard = styled<StyledCard>(Card)({
  boxShadow: 'none',
  margin: '16px 0',
});

const StyledButtonFooter = styled.div({
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'end',
  gap: '8px',
});

const MIN_IDLE_SESSION_TIMEOUT = 300; // 5 minutes
const MAX_IDLE_SESSION_TIMEOUT_GOV = 600; // 10 minutes
const MIN_ABSOLUTE_SESSION_TIMEOUT = 3600; // 1 hour

export const IDLE_SESSION_BELOW_MIN_ERROR = `The idle session timeout must be at least ${convertSecondsToDurationString(MIN_IDLE_SESSION_TIMEOUT)} long.`;
export const IDLE_SESSION_ABOVE_MAX_ERROR =
  'The idle session timeout must be shorter or equal to the absolute session timeout.';
export const IDLE_SESSION_ABOVE_MAX_GOV_ERROR = `The idle session timeout cannot exceed ${convertSecondsToDurationString(
  MAX_IDLE_SESSION_TIMEOUT_GOV
)}.`;
export const ABSOLUTE_SESSION_BELOW_MIN_ERROR = `The absolute session timeout must be at least ${convertSecondsToDurationString(
  MIN_ABSOLUTE_SESSION_TIMEOUT
)} long.`;
export const ABSOLUTE_SESSION_ABOVE_MAX_ERROR = (max: string) => `Absolute session timeout cannot exceed ${max}.`;

interface Props {
  orgId: string;
  editEnabled: boolean;
}

const CustomSessionTimeoutCard = ({ orgId, editEnabled }: Props) => {
  const [editing, setEditing] = useState(false);
  const [isModified, setIsModified] = useState(false);

  const mmsMaxSessionSeconds = useSelector(settings.getMmsMaxSessionSeconds);
  const mmsIdleSessionSeconds = useSelector(settings.getMmsIdleSessionTimeoutSeconds);

  const [savedAbsoluteSessionTimeoutInSeconds, setSavedAbsoluteSessionTimeoutInSeconds] = useState<number | undefined>(
    mmsMaxSessionSeconds
  );
  const [editingAbsoluteSessionTimeoutInSeconds, setEditingAbsoluteSessionTimeoutInSeconds] = useState<
    number | undefined
  >(mmsMaxSessionSeconds);

  const isGovEnv = isAppEnvGov(useSelector(settings.appEnv) as AppEnv);

  const [savedIdleSessionTimeoutInSeconds, setSavedIdleSessionTimeoutInSeconds] = useState<number | undefined>(
    isGovEnv ? mmsIdleSessionSeconds : IDLE_SESSION_TIMEOUT_UNSET
  );
  const [editingIdleSessionTimeoutInSeconds, setEditingIdleSessionTimeoutInSeconds] = useState<number | undefined>(
    isGovEnv ? mmsIdleSessionSeconds : IDLE_SESSION_TIMEOUT_UNSET
  );

  const [absoluteTimeoutInputError, setAbsoluteTimeoutInputError] = useState<string | undefined>(undefined);
  const [idleTimeoutInputError, setIdleTimeoutInputError] = useState<string | undefined>(undefined);
  const [responseError, setResponseError] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (
      savedAbsoluteSessionTimeoutInSeconds !== editingAbsoluteSessionTimeoutInSeconds ||
      savedIdleSessionTimeoutInSeconds !== editingIdleSessionTimeoutInSeconds
    ) {
      setIsModified(true);
    } else {
      setIsModified(false);
    }
  }, [
    savedAbsoluteSessionTimeoutInSeconds,
    savedIdleSessionTimeoutInSeconds,
    editingAbsoluteSessionTimeoutInSeconds,
    editingIdleSessionTimeoutInSeconds,
  ]);

  const validateIdleTimeoutInput = (
    absoluteTimeoutInput?: number,
    idleTimeoutInput?: number,
    customInputError?: string
  ) => {
    setIdleTimeoutInputError(undefined);

    if (customInputError) {
      setIdleTimeoutInputError(customInputError);
      return;
    }

    if (idleTimeoutInput === undefined) {
      return;
    }

    if (idleTimeoutInput === IDLE_SESSION_TIMEOUT_UNSET) {
      return;
    }

    if (idleTimeoutInput < MIN_IDLE_SESSION_TIMEOUT) {
      setIdleTimeoutInputError(IDLE_SESSION_BELOW_MIN_ERROR);
      return;
    }

    if (!isGovEnv && absoluteTimeoutInput && idleTimeoutInput > absoluteTimeoutInput) {
      setIdleTimeoutInputError(IDLE_SESSION_ABOVE_MAX_ERROR);
      return;
    }

    if (isGovEnv && idleTimeoutInput > MAX_IDLE_SESSION_TIMEOUT_GOV) {
      setIdleTimeoutInputError(IDLE_SESSION_ABOVE_MAX_GOV_ERROR);
      return;
    }
  };

  const validateAbsoluteTimeoutInput = (absoluteTimeoutInput?: number, customInputError?: string) => {
    setAbsoluteTimeoutInputError(undefined);

    if (customInputError) {
      setAbsoluteTimeoutInputError(customInputError);
      return;
    }

    if (absoluteTimeoutInput === undefined) {
      return;
    }

    if (absoluteTimeoutInput < MIN_ABSOLUTE_SESSION_TIMEOUT) {
      setAbsoluteTimeoutInputError(ABSOLUTE_SESSION_BELOW_MIN_ERROR);
      return;
    }

    if (absoluteTimeoutInput > mmsMaxSessionSeconds) {
      setAbsoluteTimeoutInputError(
        ABSOLUTE_SESSION_ABOVE_MAX_ERROR(convertSecondsToDurationString(mmsMaxSessionSeconds))
      );
      return;
    }
  };

  useEffect(() => {
    api.organization.organization({ orgId }).then((response) => {
      if (response.absoluteSessionTimeoutInSeconds) {
        setSavedAbsoluteSessionTimeoutInSeconds(response.absoluteSessionTimeoutInSeconds);
        setEditingAbsoluteSessionTimeoutInSeconds(response.absoluteSessionTimeoutInSeconds);
      }
      if (response.idleSessionTimeoutInSeconds) {
        setSavedIdleSessionTimeoutInSeconds(response.idleSessionTimeoutInSeconds);
        setEditingIdleSessionTimeoutInSeconds(response.idleSessionTimeoutInSeconds);
      }
    });
  }, [orgId]);

  const { pushToast } = useToast();

  const onSave = () => {
    setResponseError(undefined);
    api.organization
      .updateSessionTimeouts({
        orgId,
        absoluteSessionTimeoutInSeconds: editingAbsoluteSessionTimeoutInSeconds,
        idleSessionTimeoutInSeconds: editingIdleSessionTimeoutInSeconds,
      })
      .then(() => {
        setSavedAbsoluteSessionTimeoutInSeconds(editingAbsoluteSessionTimeoutInSeconds);
        setSavedIdleSessionTimeoutInSeconds(editingIdleSessionTimeoutInSeconds);
        setEditing(false);
        pushToast({
          title: 'You have successfully configured session timeout',
          description: 'The changes will take effect the next time you log in.',
          variant: Variant.Success,
        });
      })
      .catch((error) => {
        setResponseError(error?.message ?? error?.errorCode ?? 'Unknown error');
      });
  };

  const onCancel = () => {
    setEditingAbsoluteSessionTimeoutInSeconds(savedAbsoluteSessionTimeoutInSeconds);
    setEditingIdleSessionTimeoutInSeconds(savedIdleSessionTimeoutInSeconds);
    setEditing(false);
    setIsModified(false);
    setAbsoluteTimeoutInputError(undefined);
    setIdleTimeoutInputError(undefined);
    setResponseError(undefined);
  };

  const absoluteSessionTimeoutDuration = convertSecondsToTimeoutDuration(savedAbsoluteSessionTimeoutInSeconds);
  const idleSessionTimeoutDuration = convertSecondsToTimeoutDuration(savedIdleSessionTimeoutInSeconds);
  const containsEmptyCustomInputs =
    editingIdleSessionTimeoutInSeconds === undefined || editingAbsoluteSessionTimeoutInSeconds === undefined;
  const containsTimeoutInputErrors = !!absoluteTimeoutInputError || !!idleTimeoutInputError;

  return (
    <LargeSettingsTitleCard data-testid={TestSelector.CUSTOM_SESSION_TIMEOUT_CARD}>
      <CardTitleDiv>
        <StyledTitle>
          Configure Session Timeout <Badge variant={BadgeVariant.Blue}>Preview</Badge>
        </StyledTitle>
        {editEnabled && (
          <Button
            data-testid={TestSelector.EditButton}
            size="xsmall"
            name="editSessionTimeouts"
            leftGlyph={<Icon glyph="Edit" />}
            onClick={() => {
              setEditing(true);
            }}
          />
        )}
      </CardTitleDiv>
      <DescriptionContainer>
        <Body>
          Configure the absolute duration and the maximum idle (inactive) time allowed before requiring
          re-authentication within your organization. Updated timeouts will be reflected on a user's next login.{' '}
          <strong>
            If a user belongs to more than one organization, the session timeout policy from the organization with the
            strictest settings will be applied for each duration.
          </strong>{' '}
          <Link
            data-testid={TestSelector.DocsLink}
            // TODO: GA CLOUDP-329895 Add link to docs
            target="_blank"
          >
            Learn More
          </Link>
        </Body>
      </DescriptionContainer>
      {responseError && (
        <ErrorBanner variant="danger" data-testid={TestSelector.ErrorBanner}>
          Encountered error attempting to save: {responseError}
        </ErrorBanner>
      )}
      {!editing && (
        <TimeoutInfoContainer>
          <div data-testid={TestSelector.AbsoluteSessionTimeoutDisplayContainer}>
            <InlineDefinition definition="The maximum amount of time a session can be active in your organization.">
              <TimeoutDefinitionText weight="medium" as="span">
                Absolute Session Timeout:
              </TimeoutDefinitionText>
            </InlineDefinition>{' '}
            {convertTimeoutDurationToString(absoluteSessionTimeoutDuration)}
          </div>
          <div data-testid={TestSelector.IdleSessionTimeoutDisplayContainer}>
            <InlineDefinition definition="The maximum amount of time a session can be active with no user activity.">
              <TimeoutDefinitionText weight="medium" as="span">
                Idle Session Timeout:
              </TimeoutDefinitionText>
            </InlineDefinition>{' '}
            {convertTimeoutDurationToString(idleSessionTimeoutDuration)}
          </div>
        </TimeoutInfoContainer>
      )}
      {editing && (
        <>
          <EditCard data-testid={TestSelector.EditCard}>
            <SessionTimeoutInputGroup
              timeoutType="absolute"
              savedTimeoutDurationInSeconds={savedAbsoluteSessionTimeoutInSeconds}
              onTimeoutChange={(absoluteTimeoutInput?: number, customInputError?: string) => {
                setEditingAbsoluteSessionTimeoutInSeconds(absoluteTimeoutInput);
                validateAbsoluteTimeoutInput(absoluteTimeoutInput, customInputError);
                validateIdleTimeoutInput(absoluteTimeoutInput, editingIdleSessionTimeoutInSeconds);
              }}
              maxAbsoluteSessionTimeout={mmsMaxSessionSeconds}
              primarySelectOptions={absoluteSessionTimeoutOptions}
              data-testid={TestSelector.AbsoluteSessionTimeoutInputGroup}
              inputError={absoluteTimeoutInputError}
            />
            <SessionTimeoutInputGroup
              timeoutType="idle"
              savedTimeoutDurationInSeconds={savedIdleSessionTimeoutInSeconds}
              onTimeoutChange={(idleTimeoutInput?: number, customInputError?: string) => {
                setEditingIdleSessionTimeoutInSeconds(idleTimeoutInput);
                validateIdleTimeoutInput(editingAbsoluteSessionTimeoutInSeconds, idleTimeoutInput, customInputError);
              }}
              maxAbsoluteSessionTimeout={mmsMaxSessionSeconds}
              primarySelectOptions={isGovEnv ? idleSessionTimeoutOptionsGov : idleSessionTimeoutOptionsCommercial}
              data-testid={TestSelector.IdleSessionTimeoutInputGroup}
              inputError={idleTimeoutInputError}
            />
          </EditCard>
          <StyledButtonFooter>
            <Button data-testid={TestSelector.CancelButton} onClick={onCancel}>
              Cancel
            </Button>
            <Button
              data-testid={TestSelector.SaveButton}
              onClick={onSave}
              disabled={!isModified || containsTimeoutInputErrors || containsEmptyCustomInputs}
              variant={ButtonVariant.Primary}
            >
              Save
            </Button>
          </StyledButtonFooter>
        </>
      )}
    </LargeSettingsTitleCard>
  );
};

export default CustomSessionTimeoutCard;
