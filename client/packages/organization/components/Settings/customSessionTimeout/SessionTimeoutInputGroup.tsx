import { useEffect, useState } from 'react';

import styled from '@emotion/styled';
import { NumberInput } from '@leafygreen-ui/number-input';
import { Option, Select, Size } from '@leafygreen-ui/select';
import { SizeVariant } from '@leafygreen-ui/text-input';

import {
  SessionTimeoutType,
  TimeoutOption,
  TimeoutUnitDisplayName,
  TimeoutUnitOption,
} from '@packages/types/customSessionTimeouts';

import {
  convertCustomInputToSeconds,
  convertSecondsToCustomInputValue,
  convertSecondsToDurationString,
  EMPTY_INPUT,
  INVALID_INPUT,
} from '@packages/common/utils/timeoutDuration';

import { customOptionValue, sessionTimeoutUnitOptions } from './constants';

const TimeoutInputContainer = styled.div({
  display: 'flex',
  flexDirection: 'row',
  marginBottom: '16px',
});

const InputContainer = styled.div({
  width: '300px',
  marginRight: '24px',
});

export const INVALID_CUSTOM_INPUT_ERROR = 'Only integer values are allowed as an input.';
export const EMPTY_CUSTOM_INPUT_ERROR = 'Custom input cannot be empty.';

export interface Props {
  timeoutType: SessionTimeoutType;
  maxAbsoluteSessionTimeout: number;
  savedTimeoutDurationInSeconds?: number;
  inputError?: string;
  primarySelectOptions: Array<TimeoutOption>;
  onTimeoutChange: (value?: number, customInputError?: string) => void;
}

export const TestSelector = {
  PrimarySelect: 'primary-select',
  CustomInput: 'custom-input',
  CustomInputNumberInput: 'custom-input-number-input',
};

const mapToSelectedTimeoutOption = (
  timeoutType: SessionTimeoutType,
  options: Array<TimeoutOption>,
  timeoutDurationInSeconds: number | undefined
): string | undefined => {
  if (timeoutType === 'idle' && timeoutDurationInSeconds === undefined) {
    return '0';
  }

  const displayedOption = options.find((opt) => parseInt(opt.value ?? '') === timeoutDurationInSeconds);
  return displayedOption !== undefined ? displayedOption.value : customOptionValue;
};

const SessionTimeoutInputGroup = ({
  timeoutType,
  maxAbsoluteSessionTimeout,
  savedTimeoutDurationInSeconds,
  onTimeoutChange,
  primarySelectOptions,
  inputError,
  ...rest
}: Props) => {
  const [customInputUnitDisplayName, setCustomInputUnitDisplayName] = useState<TimeoutUnitDisplayName>('Minute(s)');
  const [selectedTimeoutOption, setSelectedTimeoutOption] = useState<string | undefined>();
  const [customInputValue, setCustomInputValue] = useState<string>('');

  const primarySelectTitle = timeoutType === 'absolute' ? 'Absolute Session Timeout' : 'Idle Session Timeout';
  const primarySelectDescription =
    timeoutType === 'absolute'
      ? 'Set the maximum amount of time a session can be active in your organization.'
      : 'Set the maximum amount of time a session can be active with no user activity.';

  const customInputTitle =
    timeoutType === 'absolute' ? 'Custom Absolute Session Timeout' : 'Custom Idle Session Timeout';
  const customInputDescription =
    timeoutType === 'absolute'
      ? `Absolute session timeout must be between 1 hour and ${convertSecondsToDurationString(maxAbsoluteSessionTimeout)}.`
      : 'Idle session timeout must be between 5 minutes and the absolute session timeout.';

  useEffect(() => {
    const { inputValue, unitDisplayName } = convertSecondsToCustomInputValue(savedTimeoutDurationInSeconds);
    setCustomInputValue(inputValue);
    setCustomInputUnitDisplayName(unitDisplayName);

    setSelectedTimeoutOption(
      mapToSelectedTimeoutOption(timeoutType, primarySelectOptions, savedTimeoutDurationInSeconds)
    );
  }, [savedTimeoutDurationInSeconds, timeoutType, primarySelectOptions]);

  const handleSelectChange = (valueInSecondsString: string) => {
    setSelectedTimeoutOption(valueInSecondsString);
    if (valueInSecondsString === customOptionValue) {
      // reset custom input value to empty when switching to custom option
      onTimeoutChange(undefined);
      setCustomInputValue('');
      return;
    }

    const valueInSeconds = parseInt(valueInSecondsString);
    onTimeoutChange(valueInSeconds);
  };

  const handleCustomInputChange = (value: string) => {
    setCustomInputValue(value);

    try {
      const unit =
        sessionTimeoutUnitOptions.find((opt) => opt.displayName === customInputUnitDisplayName)?.value ?? 'minutes';
      const valueInSeconds = convertCustomInputToSeconds(value, unit);
      onTimeoutChange(valueInSeconds);
    } catch (e) {
      if (e instanceof Error) {
        if (e.message === INVALID_INPUT) onTimeoutChange(undefined, INVALID_CUSTOM_INPUT_ERROR);
        if (e.message === EMPTY_INPUT) onTimeoutChange(undefined, EMPTY_CUSTOM_INPUT_ERROR);
      }
    }
  };

  const handleCustomInputUnitChange = (unit: TimeoutUnitOption) => {
    setCustomInputUnitDisplayName(unit.displayName);

    try {
      const valueInSeconds = convertCustomInputToSeconds(customInputValue, unit.value);
      onTimeoutChange(valueInSeconds);
    } catch (e) {
      if (e instanceof Error) {
        if (e.message === INVALID_INPUT) onTimeoutChange(undefined, INVALID_CUSTOM_INPUT_ERROR);
        if (e.message === EMPTY_INPUT) onTimeoutChange(undefined, EMPTY_CUSTOM_INPUT_ERROR);
      }
    }
  };

  return (
    <TimeoutInputContainer {...rest}>
      <InputContainer>
        <Select
          size={Size.Small}
          label={primarySelectTitle}
          description={primarySelectDescription}
          onChange={handleSelectChange}
          value={selectedTimeoutOption ?? ''}
          data-testid={TestSelector.PrimarySelect}
          allowDeselect={false}
        >
          {primarySelectOptions.map((opt) => (
            <Option value={opt.value?.toString()} key={opt.label}>
              {opt.label}
            </Option>
          ))}
        </Select>
      </InputContainer>
      {selectedTimeoutOption === customOptionValue && (
        <InputContainer data-testid={TestSelector.CustomInput}>
          <NumberInput
            size={SizeVariant.Small}
            label={customInputTitle}
            description={customInputDescription}
            unitOptions={sessionTimeoutUnitOptions}
            unit={customInputUnitDisplayName}
            value={customInputValue}
            onSelectChange={handleCustomInputUnitChange}
            onChange={(e) => handleCustomInputChange(e.target.value)}
            state={inputError ? 'error' : 'none'}
            errorMessage={inputError}
            data-testid={TestSelector.CustomInputNumberInput}
            min={0}
          />
        </InputContainer>
      )}
    </TimeoutInputContainer>
  );
};

export default SessionTimeoutInputGroup;
