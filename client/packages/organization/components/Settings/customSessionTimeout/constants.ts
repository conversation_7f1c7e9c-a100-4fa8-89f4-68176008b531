import { TimeoutOption, TimeoutUnitOption } from '@packages/types/customSessionTimeouts';

export const customOptionValue = 'custom';

export const absoluteSessionTimeoutOptions: Array<TimeoutOption> = [
  { label: '6 hours', value: '21600' },
  { label: '8 hours', value: '28800' },
  { label: '12 hours (recommended)', value: '43200' },
  { label: 'Custom', value: customOptionValue },
];

export const IDLE_SESSION_TIMEOUT_UNSET = 0;
export const idleSessionTimeoutOptionsCommercial: Array<TimeoutOption> = [
  { label: 'None', value: `${IDLE_SESSION_TIMEOUT_UNSET}` },
  { label: '10 minutes (recommended)', value: '600' },
  { label: '30 minutes', value: '1800' },
  { label: '1 hour', value: '3600' },
  { label: 'Custom', value: customOptionValue },
];

export const idleSessionTimeoutOptionsGov: Array<TimeoutOption> = [
  { label: '5 minutes', value: '300' },
  { label: '10 minutes (recommended)', value: '600' },
  { label: 'Custom', value: customOptionValue },
];

export const sessionTimeoutUnitOptions: Array<TimeoutUnitOption> = [
  { displayName: 'Hour(s)', value: 'hours' },
  { displayName: 'Minute(s)', value: 'minutes' },
  { displayName: 'Second(s)', value: 'seconds' },
];
