import userEvent from '@testing-library/user-event';

import { SessionTimeoutType } from '@packages/types/customSessionTimeouts';

import { convertSecondsToDurationString } from '@packages/common/utils/timeoutDuration';
import { render, screen, within } from '@packages/react-testing-library';
import { selectLeafygreenOption } from '@packages/test-utils/selectLeafygreenOption';

import {
  absoluteSessionTimeoutOptions,
  idleSessionTimeoutOptionsCommercial,
  idleSessionTimeoutOptionsGov,
} from './constants';
import SessionTimeoutInputGroup, {
  EMPTY_CUSTOM_INPUT_ERROR,
  INVALID_CUSTOM_INPUT_ERROR,
  Props,
  TestSelector,
} from './SessionTimeoutInputGroup';

describe('SessionTimeoutInputGroup', () => {
  const absoluteSessionTimeoutDefault = 43200;
  const absoluteSessionTimeoutInputTitle = 'Absolute Session Timeout';
  const idleSessionTimeoutInputTitle = 'Idle Session Timeout';

  const user = userEvent.setup();
  const onTimeoutChangeSpy = jest.fn();

  const renderComponent = ({
    timeoutType = 'absolute',
    savedTimeoutDurationInSeconds,
    onTimeoutChange = onTimeoutChangeSpy,
    primarySelectOptions = absoluteSessionTimeoutOptions,
  }: Partial<Props>) => {
    return render(
      <SessionTimeoutInputGroup
        timeoutType={timeoutType}
        savedTimeoutDurationInSeconds={savedTimeoutDurationInSeconds}
        onTimeoutChange={onTimeoutChange}
        maxAbsoluteSessionTimeout={absoluteSessionTimeoutDefault}
        primarySelectOptions={primarySelectOptions}
      />
    );
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe.each([
    {
      testDescription: 'when timeoutType is absolute',
      timeoutType: 'absolute' as SessionTimeoutType,
      primarySelectTitle: absoluteSessionTimeoutInputTitle,
      customInputTitle: 'Custom Absolute Session Timeout',
      primarySelectDescription: 'Set the maximum amount of time a session can be active in your organization.',
      customInputDescription: `Absolute session timeout must be between 1 hour and ${convertSecondsToDurationString(absoluteSessionTimeoutDefault)}.`,
      presetDurations: absoluteSessionTimeoutOptions.filter((opt) => opt.label !== 'Custom'),
      primarySelectOptions: absoluteSessionTimeoutOptions,
    },
    {
      testDescription: 'when timeoutType is idle and env is commercial',
      timeoutType: 'idle' as SessionTimeoutType,
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTitle: 'Custom Idle Session Timeout',
      primarySelectDescription: 'Set the maximum amount of time a session can be active with no user activity.',
      customInputDescription: 'Idle session timeout must be between 5 minutes and the absolute session timeout.',
      presetDurations: idleSessionTimeoutOptionsCommercial.filter((opt) => opt.label !== 'Custom'),
      primarySelectOptions: idleSessionTimeoutOptionsCommercial,
    },
    {
      testDescription: 'when timeoutType is idle and env is gov',
      timeoutType: 'idle' as SessionTimeoutType,
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTitle: 'Custom Idle Session Timeout',
      primarySelectDescription: 'Set the maximum amount of time a session can be active with no user activity.',
      customInputDescription: 'Idle session timeout must be between 5 minutes and the absolute session timeout.',
      presetDurations: idleSessionTimeoutOptionsGov.filter((opt) => opt.label !== 'Custom'),
      primarySelectOptions: idleSessionTimeoutOptionsGov,
    },
  ])(
    '$description',
    ({
      timeoutType,
      primarySelectTitle,
      primarySelectDescription,
      customInputTitle,
      customInputDescription,
      presetDurations,
      primarySelectOptions,
    }) => {
      it('renders the primary select with correct title and description', () => {
        renderComponent({ timeoutType, primarySelectOptions });
        expect(screen.getByLabelText(primarySelectTitle)).toBeInTheDocument();
        expect(screen.getByText(primarySelectDescription)).toBeInTheDocument();
      });

      it('displays custom input when "Custom" option is selected', async () => {
        renderComponent({ timeoutType, primarySelectOptions });
        selectLeafygreenOption(screen, primarySelectTitle, 'Custom');
        expect(screen.getByLabelText(customInputTitle)).toBeInTheDocument();
        expect(screen.getByText(customInputDescription)).toBeInTheDocument();
      });

      describe.each(presetDurations)('for the $label preset duration', ({ label, value }) => {
        it('renders the option in the primary select', async () => {
          renderComponent({ timeoutType, primarySelectOptions });
          await user.click(screen.getByTestId(TestSelector.PrimarySelect));
          expect(screen.getByRole('option', { name: label })).toBeInTheDocument();
        });

        it('calls onTimeoutChange with correct value when option is selected', async () => {
          renderComponent({ timeoutType, primarySelectOptions });
          selectLeafygreenOption(screen, primarySelectTitle, label);
          expect(onTimeoutChangeSpy).toHaveBeenCalledWith(parseInt(value));
        });
      });

      it('calls onTimeoutChange with empty value when Custom option is selected without input value', () => {
        renderComponent({ timeoutType, primarySelectOptions });
        selectLeafygreenOption(screen, primarySelectTitle, 'Custom');
        expect(screen.getByTestId(TestSelector.CustomInputNumberInput)).toHaveDisplayValue('');
        expect(onTimeoutChangeSpy).toHaveBeenCalledWith(undefined);
      });
    }
  );

  it('displays preset option in primary select if preset option was previously saved', () => {
    const savedTimeoutDuration = 21600;
    renderComponent({ savedTimeoutDurationInSeconds: savedTimeoutDuration });

    expect(within(screen.getByTestId(TestSelector.PrimarySelect)).getByText('6 hours')).toBeInTheDocument();
  });

  it('displays Custom option in primary select and custom timeout in custom input if custom option was previously saved', () => {
    const savedTimeoutDuration = 7200;
    renderComponent({ savedTimeoutDurationInSeconds: savedTimeoutDuration });

    expect(screen.getByTestId(TestSelector.PrimarySelect)).toHaveTextContent('Custom');

    expect(within(screen.getByTestId(TestSelector.CustomInput)).getByRole('spinbutton')).toHaveDisplayValue('2');
    expect(
      within(screen.getByTestId(TestSelector.CustomInput)).getByRole('button', { name: 'Hour(s)' })
    ).toBeInTheDocument();
  });

  it('calls onTimeoutChange when custom input value changes', async () => {
    renderComponent({});
    selectLeafygreenOption(screen, absoluteSessionTimeoutInputTitle, 'Custom');
    await user.type(within(screen.getByTestId(TestSelector.CustomInput)).getByRole('spinbutton'), '2');

    expect(onTimeoutChangeSpy).toHaveBeenCalledWith(120);
  });

  it('calls onTimeoutChange when custom input unit changes', async () => {
    renderComponent({ savedTimeoutDurationInSeconds: 7200 });
    selectLeafygreenOption(screen, 'Hour(s)', 'Minute(s)');
    expect(onTimeoutChangeSpy).toHaveBeenCalledWith(120);
  });

  it('calls onTimeoutChange with input error when custom input value is invalid', async () => {
    renderComponent({});
    selectLeafygreenOption(screen, absoluteSessionTimeoutInputTitle, 'Custom');
    await user.type(screen.getByTestId(TestSelector.CustomInputNumberInput), '2.5');

    expect(onTimeoutChangeSpy).toHaveBeenCalledWith(undefined, INVALID_CUSTOM_INPUT_ERROR);
  });

  it('calls onTimeoutChange with input error when custom input is cleared', async () => {
    renderComponent({});
    selectLeafygreenOption(screen, absoluteSessionTimeoutInputTitle, 'Custom');
    await user.type(screen.getByTestId(TestSelector.CustomInputNumberInput), '2');
    await user.clear(screen.getByTestId(TestSelector.CustomInputNumberInput));

    expect(onTimeoutChangeSpy).toHaveBeenCalledWith(undefined, EMPTY_CUSTOM_INPUT_ERROR);
  });
});
