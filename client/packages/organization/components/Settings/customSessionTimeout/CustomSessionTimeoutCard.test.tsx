import { ToastContextProps, useToast, Variant } from '@leafygreen-ui/toast';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';

import { AppEnv } from '@packages/types/RequestParams';

import { createMainReduxStore } from '@packages/redux/main/test-utils/reduxHelpers';

import * as api from '@packages/common/services/api';
import { convertSecondsToDurationString } from '@packages/common/utils/timeoutDuration';
import { render, screen, waitFor, within } from '@packages/react-testing-library';
import getTypedMockOf from '@packages/test-utils/getTypedMockOf';
import { selectLeafygreenOption } from '@packages/test-utils/selectLeafygreenOption';

import {
  IDLE_SESSION_TIMEOUT_UNSET,
  idleSessionTimeoutOptionsCommercial,
  idleSessionTimeoutOptionsGov,
} from './constants';
import CustomSessionTimeoutCard, {
  ABSOLUTE_SESSION_ABOVE_MAX_ERROR,
  ABSOLUTE_SESSION_BELOW_MIN_ERROR,
  IDLE_SESSION_ABOVE_MAX_ERROR,
  IDLE_SESSION_ABOVE_MAX_GOV_ERROR,
  IDLE_SESSION_BELOW_MIN_ERROR,
  TestSelector,
} from './CustomSessionTimeoutCard';
import {
  EMPTY_CUSTOM_INPUT_ERROR,
  INVALID_CUSTOM_INPUT_ERROR,
  TestSelector as SessionTimeoutInputGroupTestSelector,
} from './SessionTimeoutInputGroup';

jest.mock('@leafygreen-ui/toast', () => ({
  __esModule: true,
  ...jest.requireActual('@leafygreen-ui/toast'),
  useToast: jest.fn(),
}));

describe('CustomSessionTimeoutCard', () => {
  const orgId = 'organo';
  const maxAbsoluteSessionTimeoutDefault = 43200; // 12 hours
  const maxIdleSessionTimeoutDefault = 600; // 10 minutes
  const absoluteSessionTimeoutInputTitle = 'Absolute Session Timeout';
  const idleSessionTimeoutInputTitle = 'Idle Session Timeout';

  const user = userEvent.setup();

  const pushToastMock = jest.fn();
  getTypedMockOf(useToast).mockReturnValue({ pushToast: pushToastMock } as unknown as ToastContextProps);

  let getOrganizationSpy: jest.SpyInstance;

  const renderComponent = ({ editEnabled = true, isGovEnv = false }: { editEnabled?: boolean; isGovEnv?: boolean }) =>
    render(
      <Provider
        store={createMainReduxStore({
          settings: {
            mmsMaxSessionSeconds: maxAbsoluteSessionTimeoutDefault,
            mmsIdleSessionTimeoutSeconds: maxIdleSessionTimeoutDefault,
            appEnv: isGovEnv ? AppEnv.PROD_GOV : AppEnv.PROD,
          },
        })}
      >
        <CustomSessionTimeoutCard orgId={orgId} editEnabled={editEnabled} />
      </Provider>
    );

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the edit button when enabled', () => {
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });

    renderComponent({});

    expect(screen.getByTestId(TestSelector.EditButton)).toBeInTheDocument();
  });

  it('does not render the edit button when disabled', () => {
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });

    renderComponent({ editEnabled: false });

    expect(screen.queryByTestId(TestSelector.EditButton)).not.toBeInTheDocument();
  });

  it('renders the docs link', () => {
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });

    renderComponent({});

    expect(screen.getByTestId(TestSelector.DocsLink)).toBeInTheDocument();
    // TODO: GA CLOUDP-329895 Add test for docs link href attribute
  });

  describe('when rendered in gov', () => {
    it('displays default idle session timeout app setting value for unset idle session timeout', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
      renderComponent({ isGovEnv: true });
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

      within(screen.getByTestId(TestSelector.IdleSessionTimeoutDisplayContainer)).getByText('10 minutes');
    });

    it.each(idleSessionTimeoutOptionsGov)(
      'renders the $label option in the idle session timeout select',
      async ({ label }) => {
        getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
        renderComponent({ isGovEnv: true });
        await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
        await user.click(screen.getByTestId(TestSelector.EditButton));

        const idleSessionTimeoutGroup = screen.getByTestId(TestSelector.IdleSessionTimeoutInputGroup);

        await user.click(
          within(idleSessionTimeoutGroup).getByTestId(SessionTimeoutInputGroupTestSelector.PrimarySelect)
        );
        expect(screen.getByRole('option', { name: label })).toBeInTheDocument();
      }
    );
  });

  describe('when rendered in commercial', () => {
    it('displays "None" for unset idle session timeout in commercial', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });

      renderComponent({});

      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

      within(screen.getByTestId(TestSelector.IdleSessionTimeoutDisplayContainer)).getByText('None');
    });

    it.each(idleSessionTimeoutOptionsCommercial)(
      'renders the $label option in the idle session timeout select',
      async ({ label }) => {
        getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
        renderComponent({});
        await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
        await user.click(screen.getByTestId(TestSelector.EditButton));

        const idleSessionTimeoutGroup = screen.getByTestId(TestSelector.IdleSessionTimeoutInputGroup);

        await user.click(
          within(idleSessionTimeoutGroup).getByTestId(SessionTimeoutInputGroupTestSelector.PrimarySelect)
        );
        expect(screen.getByRole('option', { name: label })).toBeInTheDocument();
      }
    );
  });

  it('displays default absolute session timeout app setting value for unset absolute session timeout', async () => {
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });

    renderComponent({});

    await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

    within(screen.getByTestId(TestSelector.AbsoluteSessionTimeoutDisplayContainer)).getByText(
      convertSecondsToDurationString(maxAbsoluteSessionTimeoutDefault)
    );
  });

  it('displays absolute and idle session timeouts when set', async () => {
    const absoluteSessionTimeoutInSeconds = 36666;
    const idleSessionTimeoutInSeconds = 1800;
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({
      orgId,
      absoluteSessionTimeoutInSeconds,
      idleSessionTimeoutInSeconds,
    });

    renderComponent({});

    await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

    within(screen.getByTestId(TestSelector.AbsoluteSessionTimeoutDisplayContainer)).getByText(
      convertSecondsToDurationString(absoluteSessionTimeoutInSeconds)
    );
    within(screen.getByTestId(TestSelector.IdleSessionTimeoutDisplayContainer)).getByText(
      convertSecondsToDurationString(idleSessionTimeoutInSeconds)
    );
  });

  it('displays the edit card when the edit button is clicked', async () => {
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
    renderComponent({});
    await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
    await user.click(screen.getByTestId(TestSelector.EditButton));

    expect(screen.getByTestId(TestSelector.EditCard)).toBeInTheDocument();
  });

  it('renders the save button as disabled', async () => {
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
    renderComponent({});
    await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
    await user.click(screen.getByTestId(TestSelector.EditButton));

    expect(screen.getByTestId(TestSelector.SaveButton)).toHaveAttribute('aria-disabled', 'true');
  });

  it('renders the cancel button', async () => {
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
    renderComponent({});
    await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
    await user.click(screen.getByTestId(TestSelector.EditButton));

    expect(screen.getByTestId(TestSelector.CancelButton)).toBeInTheDocument();
  });

  it('closes the edit card when cancel button is clicked', async () => {
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
    renderComponent({});
    await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
    await user.click(screen.getByTestId(TestSelector.EditButton));
    await user.click(screen.getByTestId(TestSelector.CancelButton));

    expect(screen.queryByTestId(TestSelector.EditCard)).not.toBeInTheDocument();
  });

  it('displays defaults for unset absolute and idle session timeouts', async () => {
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
    renderComponent({});
    await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

    expect(
      within(screen.getByTestId(TestSelector.AbsoluteSessionTimeoutDisplayContainer)).getByText('12 hours')
    ).toBeInTheDocument();
    expect(
      within(screen.getByTestId(TestSelector.IdleSessionTimeoutDisplayContainer)).getByText('None')
    ).toBeInTheDocument();
  });

  it('displays absolute and idle session timeouts when set', async () => {
    const absoluteSessionTimeoutInSeconds = 36666;
    const idleSessionTimeoutInSeconds = 1800;
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({
      orgId,
      absoluteSessionTimeoutInSeconds,
      idleSessionTimeoutInSeconds,
    });

    renderComponent({});
    await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

    expect(
      within(screen.getByTestId(TestSelector.AbsoluteSessionTimeoutDisplayContainer)).getByText(
        convertSecondsToDurationString(absoluteSessionTimeoutInSeconds)
      )
    ).toBeInTheDocument();
    expect(
      within(screen.getByTestId(TestSelector.IdleSessionTimeoutDisplayContainer)).getByText(
        convertSecondsToDurationString(idleSessionTimeoutInSeconds)
      )
    ).toBeInTheDocument();
  });

  describe.each([
    {
      timeoutType: 'absolute',
      primarySelectTitle: absoluteSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.AbsoluteSessionTimeoutInputGroup,
      presetValue: '6 hours',
    },
    {
      timeoutType: 'idle',
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.IdleSessionTimeoutInputGroup,
      presetValue: '30 minutes',
    },
  ])('when editing $timeoutType session timeout', ({ primarySelectTitle, presetValue }) => {
    it('enables the save button when preset value is selected', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });

      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
      await user.click(screen.getByTestId(TestSelector.EditButton));
      selectLeafygreenOption(screen, primarySelectTitle, presetValue);

      expect(screen.getByTestId(TestSelector.SaveButton)).toHaveAttribute('aria-disabled', 'false');
    });

    it('enables the save button when custom value is inputted', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });

      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

      await user.click(screen.getByTestId(TestSelector.EditButton));
      selectLeafygreenOption(screen, primarySelectTitle, 'Custom');

      await user.type(screen.getByTestId(SessionTimeoutInputGroupTestSelector.CustomInputNumberInput), '5');

      expect(screen.getByTestId(TestSelector.SaveButton)).toHaveAttribute('aria-disabled', 'false');
    });
  });

  describe.each([
    {
      timeoutType: 'absolute',
      description: 'longer than max timeout',
      primarySelectTitle: absoluteSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.AbsoluteSessionTimeoutInputGroup,
      input: '100', // hours
      expectedError: ABSOLUTE_SESSION_ABOVE_MAX_ERROR('12 hours'),
    },
    {
      timeoutType: 'absolute',
      description: 'shorter than min timeout',
      primarySelectTitle: absoluteSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.AbsoluteSessionTimeoutInputGroup,
      input: '0', // hours
      expectedError: ABSOLUTE_SESSION_BELOW_MIN_ERROR,
    },
    {
      timeoutType: 'absolute',
      description: 'invalid timeout',
      primarySelectTitle: absoluteSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.AbsoluteSessionTimeoutInputGroup,
      input: '10.5', // hours
      expectedError: INVALID_CUSTOM_INPUT_ERROR,
    },
    {
      timeoutType: 'absolute',
      description: 'invalid timeout',
      primarySelectTitle: absoluteSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.AbsoluteSessionTimeoutInputGroup,
      input: '-10', // hours
      expectedError: INVALID_CUSTOM_INPUT_ERROR,
    },
    {
      timeoutType: 'idle',
      description: 'longer than max timeout',
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.IdleSessionTimeoutInputGroup,
      input: '800', // minutes
      expectedError: IDLE_SESSION_ABOVE_MAX_ERROR,
    },
    {
      timeoutType: 'idle',
      description: 'shorter than min timeout',
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.IdleSessionTimeoutInputGroup,
      input: '4', // minutes
      expectedError: IDLE_SESSION_BELOW_MIN_ERROR,
    },
    {
      timeoutType: 'idle',
      description: 'invalid timeout',
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.IdleSessionTimeoutInputGroup,
      input: '10.5', // minutes
      expectedError: INVALID_CUSTOM_INPUT_ERROR,
    },
    {
      timeoutType: 'idle',
      description: 'invalid timeout',
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.IdleSessionTimeoutInputGroup,
      input: '-10', // minutes
      expectedError: INVALID_CUSTOM_INPUT_ERROR,
    },
    {
      timeoutType: 'idle',
      description: 'longer than max timeout in gov env',
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.IdleSessionTimeoutInputGroup,
      input: '100', // minutes
      isGovEnv: true,
      expectedError: IDLE_SESSION_ABOVE_MAX_GOV_ERROR,
    },
  ])(
    'when editing custom $timeoutType session timeout with input $description',
    ({ primarySelectTitle, customInputTestSelector, input, isGovEnv = false, expectedError }) => {
      it('prevents save with expected error message', async () => {
        getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({
          orgId,
          absoluteSessionTimeoutInSeconds: maxAbsoluteSessionTimeoutDefault, // 12 hours
          idleSessionTimeoutInSeconds: maxIdleSessionTimeoutDefault, // 10 minutes
        });

        renderComponent({ isGovEnv });
        await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

        await user.click(screen.getByTestId(TestSelector.EditButton));

        selectLeafygreenOption(screen, primarySelectTitle, 'Custom');
        // absolute session timeout custom input renders with 12 hours
        // idle session timeout custom input renders with 10 minutes
        await user.type(screen.getByTestId(SessionTimeoutInputGroupTestSelector.CustomInputNumberInput), input);

        expect(screen.getByTestId(TestSelector.SaveButton)).toHaveAttribute('aria-disabled', 'true');
        expect(screen.getByTestId(customInputTestSelector)).toHaveTextContent(expectedError);
      });
    }
  );

  describe.each([
    {
      timeoutType: 'idle',
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.IdleSessionTimeoutInputGroup,
    },
    {
      timeoutType: 'absolute',
      primarySelectTitle: absoluteSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.AbsoluteSessionTimeoutInputGroup,
    },
  ])('when custom $timeoutType session input is empty', ({ primarySelectTitle, customInputTestSelector }) => {
    it('disables save button', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({
        orgId,
        absoluteSessionTimeoutInSeconds: maxAbsoluteSessionTimeoutDefault, // 12 hours
        idleSessionTimeoutInSeconds: maxIdleSessionTimeoutDefault, // 10 minutes
      });

      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

      await user.click(screen.getByTestId(TestSelector.EditButton));

      selectLeafygreenOption(screen, primarySelectTitle, 'Custom');
      await user.type(screen.getByTestId(SessionTimeoutInputGroupTestSelector.CustomInputNumberInput), ' ');

      expect(screen.getByTestId(TestSelector.SaveButton)).toHaveAttribute('aria-disabled', 'true');
    });

    it('displays error if custom input is deleted', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({
        orgId,
        absoluteSessionTimeoutInSeconds: maxAbsoluteSessionTimeoutDefault, // 12 hours
        idleSessionTimeoutInSeconds: maxIdleSessionTimeoutDefault, // 10 minutes
      });

      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

      await user.click(screen.getByTestId(TestSelector.EditButton));

      selectLeafygreenOption(screen, primarySelectTitle, 'Custom');
      await user.type(screen.getByTestId(SessionTimeoutInputGroupTestSelector.CustomInputNumberInput), '5');
      await userEvent.clear(screen.getByTestId(SessionTimeoutInputGroupTestSelector.CustomInputNumberInput));

      expect(screen.getByTestId(TestSelector.SaveButton)).toHaveAttribute('aria-disabled', 'true');
      expect(screen.getByTestId(customInputTestSelector)).toHaveTextContent(EMPTY_CUSTOM_INPUT_ERROR);
    });
  });

  describe.each([
    {
      timeoutType: 'absolute',
      description: 'longer than max timeout',
      primarySelectTitle: absoluteSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.AbsoluteSessionTimeoutInputGroup,
      input: '100', // hours
      expectedError: ABSOLUTE_SESSION_ABOVE_MAX_ERROR('12 hours'),
    },
    {
      timeoutType: 'absolute',
      description: 'shorter than min timeout',
      primarySelectTitle: absoluteSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.AbsoluteSessionTimeoutInputGroup,
      input: '0', // hours
      expectedError: ABSOLUTE_SESSION_BELOW_MIN_ERROR,
    },
    {
      timeoutType: 'absolute',
      description: 'invalid timeout',
      primarySelectTitle: absoluteSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.AbsoluteSessionTimeoutInputGroup,
      input: '10.5', // hours
      expectedError: INVALID_CUSTOM_INPUT_ERROR,
    },
    {
      timeoutType: 'idle',
      description: 'longer than max timeout',
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.IdleSessionTimeoutInputGroup,
      input: '800', // minutes
      expectedError: IDLE_SESSION_ABOVE_MAX_ERROR,
    },
    {
      timeoutType: 'idle',
      description: 'shorter than min timeout',
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.IdleSessionTimeoutInputGroup,
      input: '4', // minutes
      expectedError: IDLE_SESSION_BELOW_MIN_ERROR,
    },
    {
      timeoutType: 'idle',
      description: 'invalid timeout',
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.IdleSessionTimeoutInputGroup,
      input: '10.5', // minutes
      expectedError: INVALID_CUSTOM_INPUT_ERROR,
    },
  ])(
    'when editing custom $timeoutType session timeout with input $description',
    ({ primarySelectTitle, customInputTestSelector, input, expectedError }) => {
      it('prevents save with expected error message', async () => {
        getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({
          orgId,
          absoluteSessionTimeoutInSeconds: maxAbsoluteSessionTimeoutDefault, // 12 hours
          idleSessionTimeoutInSeconds: maxIdleSessionTimeoutDefault, // 10 minutes
        });

        renderComponent({});
        await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

        await user.click(screen.getByTestId(TestSelector.EditButton));

        selectLeafygreenOption(screen, primarySelectTitle, 'Custom');
        // absolute session timeout custom input renders with 12 hours
        // idle session timeout custom input renders with 10 minutes
        await user.type(screen.getByTestId(SessionTimeoutInputGroupTestSelector.CustomInputNumberInput), input);

        expect(screen.getByTestId(TestSelector.SaveButton)).toHaveAttribute('aria-disabled', 'true');
        expect(screen.getByTestId(customInputTestSelector)).toHaveTextContent(expectedError);
      });
    }
  );

  it('when editing custom absolute session timeout to be shorter than idle timeout prevents save with expected error message', async () => {
    getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({
      orgId,
      absoluteSessionTimeoutInSeconds: maxAbsoluteSessionTimeoutDefault, // 12 hours
      idleSessionTimeoutInSeconds: 7200, // 2 hours
    });

    renderComponent({});
    await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

    await user.click(screen.getByTestId(TestSelector.EditButton));

    selectLeafygreenOption(screen, absoluteSessionTimeoutInputTitle, 'Custom');
    await user.type(
      within(screen.getByTestId(TestSelector.AbsoluteSessionTimeoutInputGroup)).getByTestId(
        SessionTimeoutInputGroupTestSelector.CustomInputNumberInput
      ),
      '1' // 1 hour
    );

    expect(screen.getByTestId(TestSelector.SaveButton)).toHaveAttribute('aria-disabled', 'true');
    expect(screen.getByTestId(TestSelector.IdleSessionTimeoutInputGroup)).toHaveTextContent(
      IDLE_SESSION_ABOVE_MAX_ERROR
    );
  });

  describe.each([
    {
      timeoutType: 'idle',
      primarySelectTitle: idleSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.IdleSessionTimeoutInputGroup,
    },
    {
      timeoutType: 'absolute',
      primarySelectTitle: absoluteSessionTimeoutInputTitle,
      customInputTestSelector: TestSelector.AbsoluteSessionTimeoutInputGroup,
    },
  ])('when custom $timeoutType session input is empty', ({ primarySelectTitle, customInputTestSelector }) => {
    it('disables save button', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({
        orgId,
        absoluteSessionTimeoutInSeconds: maxAbsoluteSessionTimeoutDefault, // 12 hours
        idleSessionTimeoutInSeconds: maxIdleSessionTimeoutDefault, // 10 minutes
      });

      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

      await user.click(screen.getByTestId(TestSelector.EditButton));

      selectLeafygreenOption(screen, primarySelectTitle, 'Custom');
      await user.type(screen.getByTestId(SessionTimeoutInputGroupTestSelector.CustomInputNumberInput), ' ');

      expect(screen.getByTestId(TestSelector.SaveButton)).toHaveAttribute('aria-disabled', 'true');
    });

    it('displays error if custom input is deleted', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({
        orgId,
        absoluteSessionTimeoutInSeconds: maxAbsoluteSessionTimeoutDefault, // 12 hours
        idleSessionTimeoutInSeconds: maxIdleSessionTimeoutDefault, // 10 minutes
      });

      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());

      await user.click(screen.getByTestId(TestSelector.EditButton));

      selectLeafygreenOption(screen, primarySelectTitle, 'Custom');
      await user.type(screen.getByTestId(SessionTimeoutInputGroupTestSelector.CustomInputNumberInput), '5');
      await userEvent.clear(screen.getByTestId(SessionTimeoutInputGroupTestSelector.CustomInputNumberInput));

      expect(screen.getByTestId(TestSelector.SaveButton)).toHaveAttribute('aria-disabled', 'true');
      expect(screen.getByTestId(customInputTestSelector)).toHaveTextContent(EMPTY_CUSTOM_INPUT_ERROR);
    });
  });

  describe('on save', () => {
    it('calls updateSessionTimeouts api', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
      const updateSessionTimeoutsSpy = jest.spyOn(api.organization, 'updateSessionTimeouts').mockResolvedValue({});
      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
      await user.click(screen.getByTestId(TestSelector.EditButton));
      selectLeafygreenOption(screen, absoluteSessionTimeoutInputTitle, '6 hours');
      selectLeafygreenOption(screen, idleSessionTimeoutInputTitle, '30 minutes');

      await user.click(screen.getByTestId(TestSelector.SaveButton));

      expect(updateSessionTimeoutsSpy).toHaveBeenCalledWith({
        orgId,
        absoluteSessionTimeoutInSeconds: 21600, // 6 hours in seconds
        idleSessionTimeoutInSeconds: 1800, // 30 minutes in seconds
      });
    });

    it('calls updateSessionTimeouts api with default idleSessionTimeoutInSeconds when only absolute timeout is set for commercial', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
      const updateSessionTimeoutsSpy = jest.spyOn(api.organization, 'updateSessionTimeouts').mockResolvedValue({});

      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
      await user.click(screen.getByTestId(TestSelector.EditButton));
      selectLeafygreenOption(screen, absoluteSessionTimeoutInputTitle, '6 hours');

      await user.click(screen.getByTestId(TestSelector.SaveButton));

      expect(updateSessionTimeoutsSpy).toHaveBeenCalledWith({
        orgId,
        absoluteSessionTimeoutInSeconds: 21600, // 6 hours in seconds
        idleSessionTimeoutInSeconds: IDLE_SESSION_TIMEOUT_UNSET,
      });
    });

    it('calls updateSessionTimeouts api with default idleSessionTimeoutInSeconds when only absolute timeout is set for gov', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
      const updateSessionTimeoutsSpy = jest.spyOn(api.organization, 'updateSessionTimeouts').mockResolvedValue({});

      renderComponent({ isGovEnv: true });
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
      await user.click(screen.getByTestId(TestSelector.EditButton));
      selectLeafygreenOption(screen, absoluteSessionTimeoutInputTitle, '6 hours');

      await user.click(screen.getByTestId(TestSelector.SaveButton));

      expect(updateSessionTimeoutsSpy).toHaveBeenCalledWith({
        orgId,
        absoluteSessionTimeoutInSeconds: 21600, // 6 hours in seconds
        idleSessionTimeoutInSeconds: maxIdleSessionTimeoutDefault,
      });
    });

    it('calls updateSessionTimeouts api with 0 to unset idleSessionTimeoutInSeconds', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({
        orgId,
        absoluteSessionTimeoutInSeconds: maxAbsoluteSessionTimeoutDefault, // 12 hours
        idleSessionTimeoutInSeconds: maxIdleSessionTimeoutDefault, // 10 minutes
      });
      const updateSessionTimeoutsSpy = jest.spyOn(api.organization, 'updateSessionTimeouts').mockResolvedValue({});

      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
      await user.click(screen.getByTestId(TestSelector.EditButton));
      selectLeafygreenOption(screen, idleSessionTimeoutInputTitle, 'None');

      await user.click(screen.getByTestId(TestSelector.SaveButton));

      expect(updateSessionTimeoutsSpy).toHaveBeenCalledWith({
        orgId,
        absoluteSessionTimeoutInSeconds: maxAbsoluteSessionTimeoutDefault, // 6 hours in seconds
        idleSessionTimeoutInSeconds: IDLE_SESSION_TIMEOUT_UNSET,
      });
    });

    it('calls updateSessionTimeouts api with absoluteSessionTimeoutInSeconds set to default when only idle timeout is changed', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
      const updateSessionTimeoutsSpy = jest.spyOn(api.organization, 'updateSessionTimeouts').mockResolvedValue({});

      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
      await user.click(screen.getByTestId(TestSelector.EditButton));
      selectLeafygreenOption(screen, idleSessionTimeoutInputTitle, '30 minutes');

      await user.click(screen.getByTestId(TestSelector.SaveButton));

      expect(updateSessionTimeoutsSpy).toHaveBeenCalledWith({
        orgId,
        absoluteSessionTimeoutInSeconds: maxAbsoluteSessionTimeoutDefault,
        idleSessionTimeoutInSeconds: 1800, // 30 minutes in seconds
      });
    });

    it('displays success toast on success', async () => {
      const successToastProps = {
        title: 'You have successfully configured session timeout',
        description: 'The changes will take effect the next time you log in.',
        variant: Variant.Success,
      };

      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
      const updateSessionTimeoutsSpy = jest.spyOn(api.organization, 'updateSessionTimeouts').mockResolvedValue({});

      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
      await user.click(screen.getByTestId(TestSelector.EditButton));
      selectLeafygreenOption(screen, absoluteSessionTimeoutInputTitle, '6 hours');
      selectLeafygreenOption(screen, idleSessionTimeoutInputTitle, '30 minutes');

      await user.click(screen.getByTestId(TestSelector.SaveButton));

      expect(updateSessionTimeoutsSpy).toHaveBeenCalled();
      await waitFor(() => expect(pushToastMock).toHaveBeenCalledWith(successToastProps));
    });

    it('displays error banner and does not display toast on failure', async () => {
      getOrganizationSpy = jest.spyOn(api.organization, 'organization').mockResolvedValue({ orgId });
      const errorString = 'test custom session save failure';
      const updateSessionTimeoutsSpy = jest
        .spyOn(api.organization, 'updateSessionTimeouts')
        .mockRejectedValue(new Error(errorString));

      renderComponent({});
      await waitFor(() => expect(getOrganizationSpy).toHaveBeenCalled());
      await user.click(screen.getByTestId(TestSelector.EditButton));
      selectLeafygreenOption(screen, absoluteSessionTimeoutInputTitle, '6 hours');
      selectLeafygreenOption(screen, idleSessionTimeoutInputTitle, '30 minutes');

      await user.click(screen.getByTestId(TestSelector.SaveButton));

      expect(updateSessionTimeoutsSpy).toHaveBeenCalled();
      expect(pushToastMock).toHaveBeenCalledTimes(0);
      expect(screen.getByTestId(TestSelector.ErrorBanner)).toHaveTextContent(
        `Encountered error attempting to save: ${errorString}`
      );
    });
  });
});
