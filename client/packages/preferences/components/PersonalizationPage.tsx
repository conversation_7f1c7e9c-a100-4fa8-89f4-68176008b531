import { Component, FormEvent, useCallback } from 'react';

import styled from '@emotion/styled';
import Badge, { Variant } from '@leafygreen-ui/badge';
import { Option, Select } from '@leafygreen-ui/select';
import Toggle from '@leafygreen-ui/toggle';
import { spacing } from '@leafygreen-ui/tokens';
import { Link } from '@leafygreen-ui/typography';
import { Helmet } from 'react-helmet';
import { connect, useDispatch } from 'react-redux';

import { ProjectLandingPage } from '@packages/types/projectLandingPage';
import { ThemePreference } from '@packages/types/theme';
import { WithDefaultProps } from '@packages/types/withDefaultProps';

import * as app from '@packages/redux/common/app';
import * as settings from '@packages/redux/common/settings';
import * as viewer from '@packages/redux/common/viewer';

import * as api from '@packages/common/services/api';
import { ActiveNavElement, ModifyLayoutContext } from '@packages/common/context/LayoutContext';
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import ItemsList from '@packages/components/ItemsList/ItemsList';
import ItemsListRow, { Subtitle } from '@packages/components/ItemsList/ItemsListRow';
import SectionHeader from '@packages/components/SectionHeader/SectionHeader';
import ThemeSelection from '@packages/components/ThemeSelection';
import usePromise from '@packages/hooks/usePromise';
import { getValueOrPrevValue } from '@packages/hooks/useRequestInfo';
import { useIsInNewNav } from '@packages/layout/useIsInNewNav';
import SessionTimeoutsPersonalizationInfo from '@packages/preferences/components/SessionTimeoutsPersonalizationInfo';
import { PreferencesCard } from '@packages/preferences/styles';

const GOTO_CARD_TEXT =
  'Atlas Goto is a navigation tool that provides an efficient way to navigate the Atlas UI with keyboard shortcuts.' +
  ' If enabled, it is accessible by keyboard shortcuts (Ctrl + Space or Cmd + /).';
const NO_PREFERENCE_TEXT = 'No preference';

const TitleIconBadge = styled(Badge)`
  margin-left: ${spacing[200]}px;
`;

function CloudNavAllowedCard() {
  const requestInfo = usePromise(() => api.settings.canOptIntoCloudNav(), []);
  const isAllowed = getValueOrPrevValue(requestInfo) ?? false;

  const isEnabled = useIsInNewNav();
  const dispatch = useDispatch();
  const setIsEnabled = useCallback(
    (newEnabled: boolean) => {
      analytics.track(SEGMENT_EVENTS.BUTTON_CLICKED, {
        action: `Cloud nav ${newEnabled ? 'enabled' : 'disabled'}`,
      });
      dispatch(settings.setShowCloudNav(newEnabled));
    },
    [dispatch]
  );

  if (!isAllowed) {
    return null;
  }

  return (
    <PreferencesCard>
      <ItemsList.Row
        title="Enable New Navigation"
        titleIcon={<TitleIconBadge variant={Variant.Blue}>Preview</TitleIconBadge>}
        subtitle="Opt into the beta design for navigation in Atlas"
        hasNoMargins
        rightContent={
          <Toggle
            aria-label={`${isEnabled ? 'Enable' : 'Disable'} New Navigation`}
            onChange={setIsEnabled}
            checked={isEnabled}
          />
        }
      />
    </PreferencesCard>
  );
}

interface OwnProps {
  timeFormatCode?: string;
  timeZoneId?: string;
  timeZoneDisplay?: string;
  dateFormats?: Array<$TSFixMe>;
  timeFormats?: Array<$TSFixMe>;
  dateFormatCode?: string;
  saveDateFormat: $TSFixMeFunction;
  saveTimeFormat: $TSFixMeFunction;
  updateTimeZone: $TSFixMeFunction;
  isGoToDisabled?: boolean;
  updateIsGoToDisabled: $TSFixMeFunction;
  themePreference?: ThemePreference;
  updateThemePreference: (setting: ThemePreference) => Promise<void>;
  darkModePreviewEnabled?: boolean;
  isGovAtlas?: boolean;
  isAtlasOrg?: boolean;
  isGlobalReadOnly?: boolean;
  updateProjectLandingPage: (projectLandingPage: ProjectLandingPage) => Promise<void>;
  projectLandingPage?: ProjectLandingPage;
  projectLandingPages?: ReadonlyArray<ProjectLandingPage>;
  isCustomSessionTimeoutsFeatureEnabled?: boolean;
}

type State = $TSFixMe;

const personalizationPageDefaultProps = {
  timeFormatCode: '',
  timeZoneId: 'Etc/UTC',
  timeZoneDisplay: '',
  dateFormats: [],
  timeFormats: [],
  dateFormatCode: '',
  isGoToDisabled: false,
  darkModePreviewEnabled: false,
  themePreference: ThemePreference.LIGHT,
  projectLandingPage: ProjectLandingPage.Overview,
  projectLandingPages: [],
  isCustomSessionTimeoutsFeatureEnabled: false,
};

const SecondLineSubtitle = styled(Subtitle)`
  margin-top: 0;
`;

const ThemeSelectionSubtitle = () => (
  <>
    <Subtitle>
      Change the appearance of Atlas. These settings control experimental behavior of Atlas and should be used with
      discretion.
    </Subtitle>
    <SecondLineSubtitle>Note: Dark mode is currently unavailable for Charts.</SecondLineSubtitle>
  </>
);

const ThemeSelectionContainer = styled('div')`
  display: flex;
  justify-content: flex-end;
`;

type Props = WithDefaultProps<OwnProps, typeof personalizationPageDefaultProps>;

class PersonalizationPage extends Component<Props, State> {
  static defaultProps = personalizationPageDefaultProps;

  timeZoneRow: ItemsListRow | null;
  dateFormatRow: ItemsListRow | null;
  timeFormatRow: ItemsListRow | null;
  sessionTimeoutRow: ItemsListRow | null;
  themePreferenceRow: ItemsListRow | null;
  goToToggleRow: ItemsListRow | null;
  projectLandingPageRow: ItemsListRow | null;

  state = {
    timeZones: [],
  };

  async componentDidMount() {
    api.v2.timeZones().then((_timeZones) => {
      const timeZones = [
        // No preference option
        {
          canonicalName: null,
          displayName: null,
          displayWithOffset: '',
        },
        ..._timeZones,
      ];
      this.setState({ timeZones });
    });
  }

  onTimeZoneChange = (value: string) => {
    const { updateTimeZone } = this.props;
    const { timeZones } = this.state;

    // eslint-disable-next-line eqeqeq
    const targetTimeZone = timeZones.find((it) => (it as $TSFixMe).displayWithOffset == value);

    if (this.timeZoneRow) {
      this.timeZoneRow.hideSuccessIcon();
      updateTimeZone(targetTimeZone).then(this.timeZoneRow.showSuccessIcon);
    }
  };

  onChangeDateFormat = (value: string) => {
    const { saveDateFormat } = this.props;
    if (this.dateFormatRow) {
      this.dateFormatRow.hideSuccessIcon();
      saveDateFormat(value).then(this.dateFormatRow.showSuccessIcon);
    }
  };

  onChangeTimeFormat = (value: string) => {
    const { saveTimeFormat } = this.props;
    if (this.timeFormatRow) {
      this.timeFormatRow.hideSuccessIcon();
      saveTimeFormat(value).then(this.timeFormatRow.showSuccessIcon);
    }
  };

  onGoToToggleChange = (checked: $TSFixMe) => {
    const { updateIsGoToDisabled } = this.props;
    analytics.track(SEGMENT_EVENTS.USER_SETTINGS_TOGGLE_CHECKED, {
      context: 'User Preferences Page',
      settings_name: 'goto',
      checked,
    });
    updateIsGoToDisabled({ isDisabled: !checked }).then(() => {
      if (this.goToToggleRow) {
        return this.goToToggleRow.showSuccessIcon;
      }
    });
  };

  onThemePreferenceChange = (event: FormEvent) => {
    const value = (event.target as HTMLInputElement).value as ThemePreference;
    // The user's theme becomes their "prior" one after we updateThemePreference
    const { updateThemePreference, themePreference: priorThemePreference } = this.props;

    this.themePreferenceRow?.hideSuccessIcon();
    updateThemePreference(value).then(() => {
      this.themePreferenceRow?.showSuccessIcon();

      analytics.track(SEGMENT_EVENTS.THEME_SELECTED, {
        action: value,
        context: 'Preferences Page',
        prior_theme: priorThemePreference,
      });
    });
  };

  onProjectLandingPageChange = (value: ProjectLandingPage) => {
    const { updateProjectLandingPage } = this.props;
    updateProjectLandingPage(value).then(() => {
      this.projectLandingPageRow?.showSuccessIcon();
    });
  };

  mapTimeZoneDisplayToWithOffset = (currentTimeZone: $TSFixMe) => {
    if (!currentTimeZone) {
      return '';
    }
    if (currentTimeZone.startsWith('(')) {
      return currentTimeZone;
    }
    const { timeZones } = this.state;
    // eslint-disable-next-line eqeqeq
    const filteredTimeZone = timeZones.find((tz) => (tz as $TSFixMe).displayName == currentTimeZone);
    return filteredTimeZone ? (filteredTimeZone as $TSFixMe).displayWithOffset : '(-00:00) UTC';
  };

  render() {
    const {
      timeFormatCode,
      timeZoneDisplay,
      dateFormats,
      timeFormats,
      dateFormatCode,
      isGoToDisabled,
      isGovAtlas,
      isAtlasOrg,
      isGlobalReadOnly,
      themePreference,
      darkModePreviewEnabled,
      projectLandingPage,
      projectLandingPages,
      isCustomSessionTimeoutsFeatureEnabled,
    } = this.props;
    const { timeZones } = this.state;
    const timeZoneWithOffset = this.mapTimeZoneDisplayToWithOffset(timeZoneDisplay);

    return (
      <div>
        <Helmet title="Personalization" />
        <ModifyLayoutContext
          state={{
            activeNav: ActiveNavElement.UserMenuCloudUserPreferences,
          }}
        />
        <SectionHeader headlineText="Personalization" hasBottomSpacing isBorderless />
        <ItemsList>
          <PreferencesCard>
            <ItemsList.Row
              ref={(r) => {
                this.timeZoneRow = r;
              }}
              title="My Time Zone"
              subtitle="When dates are displayed, show them in this timezone."
              hasNoMargins
              rightContent={
                <Select
                  onChange={this.onTimeZoneChange}
                  value={timeZoneWithOffset}
                  aria-label="Timezone"
                  style={{ width: 284 }}
                  dropdownWidthBasis="option"
                  allowDeselect={false}
                >
                  {timeZones.map((item) => (
                    <Option key={(item as $TSFixMe).displayWithOffset} value={(item as $TSFixMe).displayWithOffset}>
                      {(item as $TSFixMe).displayWithOffset || NO_PREFERENCE_TEXT}
                    </Option>
                  ))}
                </Select>
              }
            />
          </PreferencesCard>
          <PreferencesCard>
            <ItemsList.Row
              ref={(r) => {
                this.dateFormatRow = r;
              }}
              title="My Date Format"
              subtitle="When dates are displayed, show them in this format."
              hasNoMargins
              rightContent={
                <Select
                  onChange={this.onChangeDateFormat}
                  value={dateFormatCode}
                  aria-label="Date format"
                  style={{ width: 131 }}
                  dropdownWidthBasis="option"
                  allowDeselect={false}
                >
                  {dateFormats.map((item) => (
                    <Option key={item.code} value={item.code}>
                      {item.displayName.split(' - ')[0]}
                    </Option>
                  ))}
                </Select>
              }
            />
          </PreferencesCard>
          <PreferencesCard>
            <ItemsList.Row
              ref={(r) => {
                this.timeFormatRow = r;
              }}
              title="My Time Format"
              subtitle="When times are displayed, show them in this format."
              hasNoMargins
              rightContent={
                <Select
                  onChange={this.onChangeTimeFormat}
                  value={timeFormatCode}
                  aria-label="Time format"
                  style={{ width: 98 }}
                  dropdownWidthBasis="option"
                  allowDeselect={false}
                >
                  {timeFormats.map((item) => (
                    <Option key={item.code} value={item.code}>
                      {item.displayName}
                    </Option>
                  ))}
                </Select>
              }
            />
          </PreferencesCard>
          {isCustomSessionTimeoutsFeatureEnabled && (
            <PreferencesCard>
              <ItemsList.Row
                ref={(r) => {
                  this.sessionTimeoutRow = r;
                }}
                title="My Session Timeout"
                titleIcon={<TitleIconBadge variant={Variant.Blue}>Preview</TitleIconBadge>}
                renderSubtitle={() => (
                  <>
                    <Subtitle>
                      Your session timeout settings are determined by the policies of the organizations you are part of.
                      If you belong to more than one organization, the session timeout policy from the organization with
                      the strictest settings is applied for each duration.{' '}
                      <Link // TODO: NB CLOUDP-329895 Add link to docs
                        target="_blank"
                      >
                        Learn more
                      </Link>
                    </Subtitle>
                  </>
                )}
                hasNoMargins
              />
              <br />
              <SessionTimeoutsPersonalizationInfo />
            </PreferencesCard>
          )}

          {darkModePreviewEnabled && (
            <PreferencesCard>
              <ItemsList.Row
                ref={(r) => {
                  this.themePreferenceRow = r;
                }}
                title="Appearance"
                titleIcon={<TitleIconBadge variant={Variant.Blue}>Preview</TitleIconBadge>}
                renderSubtitle={ThemeSelectionSubtitle}
                hasNoMargins
              />
              <br />
              <ThemeSelectionContainer>
                <ThemeSelection
                  onThemePreferenceChange={this.onThemePreferenceChange}
                  themePreference={themePreference}
                />
              </ThemeSelectionContainer>
            </PreferencesCard>
          )}

          {isAtlasOrg && !isGovAtlas && (
            <PreferencesCard>
              <ItemsList.Row
                ref={(r) => {
                  this.goToToggleRow = r;
                }}
                title="Atlas Goto"
                subtitle={GOTO_CARD_TEXT}
                hasNoMargins
                rightContent={
                  <Toggle
                    aria-label={`${isGoToDisabled ? 'Enable' : 'Disable'} Atlas Goto`}
                    onChange={this.onGoToToggleChange}
                    checked={!isGoToDisabled}
                  />
                }
              />
            </PreferencesCard>
          )}
          {isGlobalReadOnly && (
            <PreferencesCard data-testid="project-landing-page-row">
              <ItemsList.Row
                ref={(ref) => {
                  this.projectLandingPageRow = ref;
                }}
                title="Landing page"
                subtitle="For internal MongoDB users only. Set your default landing page in Atlas."
                hasNoMargins
                rightContent={
                  <Select
                    onChange={this.onProjectLandingPageChange}
                    value={projectLandingPage}
                    aria-label="Project landing page"
                    style={{ width: 117 }}
                    dropdownWidthBasis="option"
                    allowDeselect={false}
                  >
                    {projectLandingPages.map((item) => (
                      <Option key={item} value={item}>
                        {item}
                      </Option>
                    ))}
                  </Select>
                }
              />
            </PreferencesCard>
          )}

          <CloudNavAllowedCard />
        </ItemsList>
      </div>
    );
  }
}

export default connect(
  (state) => ({
    isAtlasOrg: app.isActiveOrgAtlas(state),
    isGovAtlas: settings.isNdsGovEnabled(state),
    isGlobalReadOnly: viewer.isGlobalReadOnly(state),
    timeZoneId: viewer.getTimeZoneId(state),
    timeZoneDisplay: viewer.getTimeZoneDisplay(state),
    dateFormats: settings.getDateFormats(state),
    timeFormats: settings.getTimeFormats(state),
    dateFormatCode: viewer.getDateFormatCode(state),
    timeFormatCode: viewer.getTimeFormatCode(state),
    isGoToDisabled: viewer.isGoToDisabled(state),
    themePreference: viewer.getThemePreference(state),
    projectLandingPage: viewer.getProjectLandingPage(state),
    projectLandingPages: settings.getProjectLandingPages(state),
    darkModePreviewEnabled: settings.isDarkModePreviewEnabled(state),
    isCustomSessionTimeoutsFeatureEnabled: settings.isCustomSessionTimeoutsFeatureEnabled(state),
  }),
  {
    saveDateFormat: viewer.updateDateFormat,
    saveTimeFormat: viewer.updateTimeFormat,
    updateTimeZone: viewer.updateTimeZone,
    updateIsGoToDisabled: viewer.updateIsGoToDisabled,
    updateThemePreference: viewer.updateThemePreference,
    updateProjectLandingPage: viewer.updateProjectLandingPage,
  }
)(PersonalizationPage);

export { PersonalizationPage };
