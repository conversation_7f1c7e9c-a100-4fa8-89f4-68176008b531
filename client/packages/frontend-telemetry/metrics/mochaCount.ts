//@ts-ignore, file exists but outside of client
// eslint-disable-next-line no-restricted-imports
import mochaFiles from '../../../../scripts/python/resources/evergreen/known_mocha_files.json';
import globBySubdir from '../utils/globBySubdir';
import groupBySubdir from '../utils/groupBySubdir';
import zipTotalCounts from '../utils/zipTotalCounts';

const mochaCount = async () => {
  const deprecatedCount = groupBySubdir(mochaFiles);
  const jestCount = await globBySubdir('**/*.test.*');
  return zipTotalCounts(deprecatedCount, jestCount);
};

export default mochaCount;
