import CollectionWithLookupTables from '@packages/legacy/core/models/CollectionWithLookupTables';

import Mongosqld from './Mongosqld';

export default CollectionWithLookupTables.extend({
  model: Mongosqld,

  initialize() {
    this.initializeLookupTable('mongosqldsByDeploymentItem', 'multi', (model: $TSFixMe) => {
      return model.getDeploymentItem();
    });
  },

  filterByDeploymentItem(deploymentItem: $TSFixMe) {
    return new this.constructor(this.getFromLookupTable('mongosqldsByDeploymentItem', deploymentItem));
  },

  removeByDeploymentItem(deploymentItem: $TSFixMe) {
    this.remove(this.getFromLookupTable('mongosqldsByDeploymentItem', deploymentItem));
  },

  getByName(name: string) {
    return this.getFromLookupTable('mongosqldsByDeploymentItem', name);
  },
});
