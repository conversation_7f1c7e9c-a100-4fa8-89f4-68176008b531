import { ClusterDetailModel } from '@packages/types/nds/clusterDescription';

import { render, screen } from '@packages/react-testing-library';

import SampleDatasetLoaded from './SampleDatasetLoaded';

jest.mock('@packages/project/common/util/getDataExplorerRoute', () => jest.fn(() => 'data-explorer-route'));

describe('SampleDatasetLoaded component', () => {
  const mockClusterDetail = {} as ClusterDetailModel;

  const renderComponent = (props: $TSFixMe) => {
    render(<SampleDatasetLoaded clusterDetail={mockClusterDetail} {...props} />);
  };

  it('renders correct text for isCreateSampleSearchIndexEnabled without serach index', () => {
    renderComponent({
      dataExplorerExposed: true,
      isCreateSampleSearchIndexEnabled: true,
      hasSampleSearchIndex: false,
    });

    expect(screen.getByText('Sample data loaded successfully.')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Browse this collection.' })).toHaveAttribute(
      'href',
      'data-explorer-route'
    );
  });

  it('renders correct text for isCreateSampleSearchIndexEnabled with search index', () => {
    renderComponent({
      dataExplorerExposed: true,
      isCreateSampleSearchIndexEnabled: true,
      hasSampleSearchIndex: true,
    });

    const banner = screen.getByRole('alert');
    expect(banner).toHaveTextContent('Sample data loaded successfully.');
    expect(banner).toHaveTextContent('Get started with a sample query in');
    expect(screen.getByRole('link', { name: 'Data Explorer' })).toHaveAttribute('href', 'data-explorer-route');
    expect(banner).toHaveTextContent('or by connecting with MongoDB Shell');
  });

  it('renders correct control message for multiple datasets', () => {
    renderComponent({
      dataExplorerExposed: true,
      shouldUsePlural: true,
      isCreateSampleSearchIndexEnabled: false,
    });

    expect(screen.getByText('Sample Datasets loaded successfully!')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Browse this collection.' })).toHaveAttribute(
      'href',
      'data-explorer-route'
    );
  });

  it('renders correct control message for single dataset', () => {
    renderComponent({
      dataExplorerExposed: true,
      shouldUsePlural: false,
      isCreateSampleSearchIndexEnabled: false,
    });

    expect(screen.getByText('Sample Dataset loaded successfully!')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: 'Browse this collection.' })).toHaveAttribute(
      'href',
      'data-explorer-route'
    );
  });
});
