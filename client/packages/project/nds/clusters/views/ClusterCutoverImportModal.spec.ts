import chai from 'chai';
import sinon, { SinonStub } from 'sinon';
import sinonChai from 'sinon-chai';

import clusterFixtures from '@packages/common/fixtures/clusterFixtures';
import ClusterDescription from '@packages/common/models/ClusterDescription';
import Settings from '@packages/common/models/Settings';
import Deferred from '@packages/common/utils/Deferred';
import LiveImport from '@packages/project/common/models/LiveImport';
import liveImportService from '@packages/project/common/services/liveImportService';
import ClusterCutoverImportModal from '@packages/project/nds/clusters/views/ClusterCutoverImportModal';

const expect = chai.expect;

chai.use(sinonChai);

describe('@packages/project/nds/clusters/views/ClusterCutoverImportModal', function () {
  beforeEach(function (this: $TSFixMe) {
    this.sandbox = sinon.createSandbox();
    this.responses = {
      updateLiveImport: new Deferred(),
    };

    this.sandbox.stub(liveImportService, 'updateLiveImport').returns(this.responses.updateLiveImport);
    this.sandbox.stub(LiveImport.prototype, 'fetch');

    this.liveImport = new LiveImport({
      _id: '1234567890',
      clusterName: 'myCluster',
      mongoMirrorStatuses: [
        {
          errorMessage: '',
          stage: 'OPLOG_SYNC',
          phase: 'catching up with oplog',
          initialSyncBytesCopied: 131929920,
          initialSyncBytesTotal: 131929920,
          oplogSourceTimeEpoch: 1487806832,
          oplogTargetTimeEpoch: 1487806826,
          oplogSourceTimeInc: 3,
          oplogTargetTimeInc: 2,
        },
      ],
    });

    this.shardedLiveImport = new LiveImport({
      _id: '1234567891',
      clusterName: 'myShardedCluster',
      mongoMirrorStatuses: [
        {
          errorMessage: '',
          stage: 'OPLOG_SYNC',
          phase: 'catching up with oplog',
          initialSyncBytesCopied: 131929920,
          initialSyncBytesTotal: 131929920,
          oplogSourceTimeEpoch: 1487806832,
          oplogTargetTimeEpoch: 1487806832,
          oplogSourceTimeInc: 3,
          oplogTargetTimeInc: 2,
        },
        {
          errorMessage: '',
          stage: 'OPLOG_SYNC',
          phase: 'catching up with oplog',
          initialSyncBytesCopied: 131929920,
          initialSyncBytesTotal: 131929920,
          oplogSourceTimeEpoch: 1487806832,
          oplogTargetTimeEpoch: 1487806832,
          oplogSourceTimeInc: 3,
          oplogTargetTimeInc: 2,
        },
      ],
    });

    const settingsModel = new Settings({ SITE_NAME: 'Atlas' });

    this.clusterDescription = new ClusterDescription(
      {
        name: 'fakeName',
        mongoDBUriHosts: [
          'bobmango042516b-shard-00-00-3m247.mmscloudtest.com:27017',
          'bobmango042516b-shard-00-01-3m247.mmscloudtest.com:27017',
        ],
        replicationSpecList: [clusterFixtures.getDefaultNewAWSReplicationSpec()],
        clusterType: 'REPLICASET',
        deploymentItemName: 'fakeName-shard-0',
      },
      { parse: true }
    );

    const replicationSpec = clusterFixtures.getDefaultNewAWSReplicationSpec();
    replicationSpec.numShards = 2;

    this.shardedClusterDescription = new ClusterDescription(
      {
        name: 'fakeName',
        mongoDBUriHosts: [
          'bobmango042516b-shard-00-00-3m247.mmscloudtest.com:27016',
          'bobmango042516b-shard-00-01-3m247.mmscloudtest.com:27016',
        ],
        replicationSpecList: [replicationSpec],
        clusterType: 'SHARDED',
        deploymentItemName: 'fakeName',
      },
      { parse: true }
    );

    this.shardedView = new ClusterCutoverImportModal({
      liveImport: this.shardedLiveImport,
      clusterDescription: this.shardedClusterDescription,
      settingsModel,
    });
    this.sandbox.spy(this.shardedView, 'remove');

    this.view = new ClusterCutoverImportModal({
      liveImport: this.liveImport,
      clusterDescription: this.clusterDescription,
      settingsModel,
    });
    this.sandbox.spy(this.view, 'remove');
  });

  afterEach(function (this: $TSFixMe) {
    this.sandbox.restore();
    this.view.destroy();
    this.shardedView.destroy();
  });

  describe('when rendered with a live import with less than a minute of oplog lag', function () {
    beforeEach(function (this: $TSFixMe) {
      this.view.render();
    });

    it('shows a disabled done button', function (this: $TSFixMe) {
      expect(this.view.$('button[name="done"]').length).to.equal(1);
      expect(this.view.$('button[name="done"]').attr('disabled')).to.equal('disabled');
    });

    it('shows a cancel button', function (this: $TSFixMe) {
      expect(this.view.$('button[name="cancel"]').length).to.equal(1);
    });

    it('shows the current oplog lag', function (this: $TSFixMe) {
      expect(this.view.$('.live-import-oplog-lag').text()).to.include('0m 06s');
      expect(this.view.$('.live-import-check').attr('hidden')).to.equal('hidden');
    });

    it('shows the atlas mongo URI', function (this: $TSFixMe) {
      const uris =
        'bobmango042516b-shard-00-00-3m247.mmscloudtest.com:27017,bobmango042516b-shard-00-01-3m247.mmscloudtest.com:27017';
      const name = this.view.clusterDescription.getDeploymentItemName();
      const expectedString = `mongodb://<db_username>:<db_password>@${uris}/<DATABASE>?ssl=true&replicaSet=${name}&authSource=admin`;
      const displayedString = this.view.$('[class*=ConnectCode]').text().trim();
      expect(displayedString).to.include(expectedString);
    });

    describe('and the oplog lag drops to less than one second', function () {
      beforeEach(function (this: $TSFixMe) {
        this.liveImport.set({
          mongoMirrorStatuses: [
            {
              errorMessage: '',
              stage: 'OPLOG_SYNC',
              phase: 'catching up with oplog',
              initialSyncBytesCopied: 131929920,
              initialSyncBytesTotal: 131929920,
              oplogSourceTimeEpoch: 1487806832,
              oplogTargetTimeEpoch: 1487806832,
              oplogSourceTimeInc: 3,
              oplogTargetTimeInc: 2,
            },
          ],
        });
        this.liveImport.trigger('sync');
      });

      it('shows that there is less than one second of oplog lag', function (this: $TSFixMe) {
        expect(this.view.$('.live-import-oplog-lag').text()).to.include('< 0:01');
        expect(this.view.$('.live-import-check').attr('hidden')).to.equal('hidden');
      });

      it('has a disabled done button', function (this: $TSFixMe) {
        expect(this.view.$('button[name="done"]').attr('disabled')).to.equal('disabled');
      });

      it('shows correct instructions for non sharded clusters', function (this: $TSFixMe) {
        expect(this.view.$('.step-indicator-step:nth-last-child(2)').text()).to.include('Restart your application');
        expect(this.view.$('.step-indicator-step:last-child').text()).to.include('Confirm the steps');
      });
    });

    describe('and the oplog is completely caught up', function () {
      beforeEach(function (this: $TSFixMe) {
        this.liveImport.set({
          mongoMirrorStatuses: [
            {
              errorMessage: '',
              stage: 'OPLOG_SYNC',
              phase: 'catching up with oplog',
              initialSyncBytesCopied: 131929920,
              initialSyncBytesTotal: 131929920,
              oplogSourceTimeEpoch: 1487806832,
              oplogTargetTimeEpoch: 1487806832,
              oplogSourceTimeInc: 3,
              oplogTargetTimeInc: 3,
            },
          ],
        });
        this.liveImport.trigger('sync');
      });

      it('shows that the oplog is caught up', function (this: $TSFixMe) {
        expect(this.view.$('.live-import-oplog-lag').text()).to.include('0m 00s');
        expect(this.view.$('.live-import-check').attr('hidden')).to.equal(undefined);
      });

      it('enables the done button', function (this: $TSFixMe) {
        expect(this.view.$('button[name="done"]').attr('disabled')).to.equal(undefined);
      });

      describe('and the user clicks the done button', function () {
        beforeEach(function (this: $TSFixMe) {
          this.view.$('button[name="done"]').click();
        });

        it('clicks the cutover button and a secondary confirmation modal shows; then makes a request for cutover, a copy of the Live Import with cutover complete flag', function (this: $TSFixMe) {
          expect(this.view.$el.text()).to.contain('Confirm Cut Over');
          this.view.$('button[name="confirm"]').click();

          expect(liveImportService.updateLiveImport).to.have.been.calledOnce;
          const liveImport = (liveImportService.updateLiveImport as SinonStub).lastCall.args[0];
          expect(liveImport.get('_id')).to.equal('1234567890');
          expect(liveImport.get('isCutoverComplete')).to.equal(true);
        });

        describe('and the call to request cutover rejects', function () {
          beforeEach(function (this: $TSFixMe) {
            this.view.$('button[name="confirm"]').click();

            this.responses.updateLiveImport.reject('My error');
            return this.responses.updateLiveImport.catch(() => {});
          });

          it('shows an error message', function (this: $TSFixMe) {
            expect(this.view.$('.alert-danger').text()).to.include('My error');
          });

          it('does not close the modal', function (this: $TSFixMe) {
            expect(this.view.remove).not.to.have.been.called;
          });
        });

        describe('and the call to request cutover resolves', function () {
          beforeEach(function (this: $TSFixMe) {
            this.view.$('button[name="confirm"]').click();

            return this.responses.updateLiveImport.resolve();
          });

          it('fetches the live import', function (this: $TSFixMe) {
            expect(this.liveImport.fetch).to.have.been.calledOnce;
          });

          it('closes the modal', function (this: $TSFixMe) {
            expect(this.view.remove).to.have.been.calledOnce;
          });
        });
      });
    });
  });

  describe('when the cluster has an SRV connection string', function () {
    beforeEach(function (this: $TSFixMe) {
      this.clusterDescription.set('srvAddress', 'cluster0-lkece.mongodb.net');
      this.view.render();
    });

    it('links to DNS seedlist documentation', function (this: $TSFixMe) {
      const href = 'https://docs.mongodb.com/manual/reference/connection-string/#dns-seedlist-connection-format';
      expect(this.view.$(`a[href="${href}"]`).length).to.equal(1);
    });
  });

  describe('when the cluster does not have an SRV connection string', function () {
    beforeEach(function (this: $TSFixMe) {
      this.clusterDescription.set('srvAddress', null);
      this.view.render();
    });

    it('does link to driver compatibility reference', function (this: $TSFixMe) {
      const href = 'https://docs.mongodb.com/ecosystem/drivers/driver-compatibility-reference/';
      expect(this.view.$(`a[href="${href}"]`).length).to.equal(1);
    });

    it('does link to DNS seedlist documentation', function (this: $TSFixMe) {
      const href = 'https://docs.mongodb.com/manual/reference/connection-string/#dns-seedlist-connection-format';
      expect(this.view.$(`a[href="${href}"]`).length).to.equal(1);
    });
  });

  describe('when clusters are sharded', function () {
    beforeEach(function (this: $TSFixMe) {
      this.shardedView.render();
    });

    it('shows correct step 3 instructions for sharded clusters', function (this: $TSFixMe) {
      expect(this.shardedView.$('.js-mongo-uri').text()).to.not.be.empty;
      expect(this.shardedView.$('.step-indicator-step:nth-last-child(4)').text()).to.include('Prepare to point');
    });

    it('shows correct step 4 instructions for sharded clusters', function (this: $TSFixMe) {
      expect(this.shardedView.$('.js-mongo-uri').text()).to.not.be.empty;
      expect(this.shardedView.$('.step-indicator-step:last-child').text()).to.include('Wait approximately 3-5 minutes');
    });

    describe('when clusters are sharded and cluster has privateEndpoints', function () {
      beforeEach(function (this: $TSFixMe) {
        this.shardedClusterDescription.set('endpointToLoadBalancedSRVConnectionURI', {
          vpcId: 'mongodb.pl.com',
        });
        this.shardedView.render();
      });

      it('shows correct step 1 instructions', function (this: $TSFixMe) {
        expect(this.shardedView.$('.step-indicator-step:nth-last-child(4)').text()).to.include('Prepare to point');
        expect(this.shardedView.$('.step-indicator-step:nth-last-child(4)').text()).to.include(
          'Use the connection details that you noted earlier. Otherwise, you will be able to retrieve them once the migration is complete.'
        );

        // Should not include
        expect(this.shardedView.$('.js-mongo-uri').text()).to.be.empty;
        const href = 'https://docs.mongodb.com/manual/reference/connection-string/#dns-seedlist-connection-format';
        expect(this.view.$(`a[href="${href}"]`).length).to.equal(0);
        expect(this.shardedView.$('.step-indicator-step:nth-last-child(4)').text()).to.not.include(
          'Get ready to update your connection string so that it points to the destination'
        );
      });
    });
  });
});
