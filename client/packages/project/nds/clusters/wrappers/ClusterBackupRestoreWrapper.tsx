import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom-v5-compat';

import { SettingsModel } from '@packages/types/settingsModel';

import { getActiveGroupId } from '@packages/redux/common/app';

import RestoreSnapshotPage from '@packages/project/nds/backup/components/RestoreSnapshotPage';

function ClusterBackupRestoreWrapper({
  settingsModel,
  isRetained,
}: {
  settingsModel: SettingsModel;
  isRetained: boolean;
}) {
  const { clusterId, clusterName, snapshotId } = useParams<{
    clusterId: string;
    clusterName: string;
    snapshotId: string;
  }>();
  const groupId = useSelector(getActiveGroupId);

  if (!clusterId || !clusterName || !snapshotId) {
    return null;
  }

  return (
    <RestoreSnapshotPage
      clusterUniqueId={clusterId}
      clusterName={clusterName}
      groupId={groupId}
      isPit={false}
      snapshotId={snapshotId}
      settingsModel={settingsModel}
      isRetained={isRetained}
    />
  );
}

export default ClusterBackupRestoreWrapper;
