import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom-v5-compat';

import { SettingsModel } from '@packages/types/settingsModel';

import { getActiveGroupId } from '@packages/redux/common/app';

import RestoreSnapshotPage from '@packages/project/nds/backup/components/RestoreSnapshotPage';

function ClusterBackupPitRestoreWrapper({
  settingsModel,
  isRetained,
}: {
  settingsModel: SettingsModel;
  isRetained: boolean;
}) {
  const { clusterId, clusterName } = useParams<{ clusterId: string; clusterName: string }>();
  const groupId = useSelector(getActiveGroupId);

  if (!clusterId || !clusterName) {
    return null;
  }

  return (
    <RestoreSnapshotPage
      clusterUniqueId={clusterId}
      clusterName={clusterName}
      groupId={groupId}
      isPit={true}
      snapshotId={''}
      settingsModel={settingsModel}
      isRetained={isRetained}
    />
  );
}

export default ClusterBackupPitRestoreWrapper;
