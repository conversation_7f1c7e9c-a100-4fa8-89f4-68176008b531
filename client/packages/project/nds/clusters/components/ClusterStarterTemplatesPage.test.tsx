import { Provider } from 'react-redux';

import { ExperimentFeatureFlag, TestName } from '@packages/types/abTest';
import {
  CostEstimate,
  PartnerSubscriptionStatus,
  PartnerType,
  PaymentMethod,
  PaymentMethodStatus,
} from '@packages/types/billing';
import { ClusterConfigurationSource } from '@packages/types/clusterConfiguration';
import { BillingEstimate, ClusterDraftResponse } from '@packages/types/clusterDrafts';
import { ClusterDescription } from '@packages/types/nds/clusterDescription';
import { ClusterStarterTemplateCardOption } from '@packages/types/nds/clusterStarterTemplates';
import { DefaultTemplateRegions, DefaultTemplates, TemplateKey } from '@packages/types/nds/defaultTemplate';
import {
  BackingCloudProvider,
  CloudProvider,
  InstanceSizes,
  ProviderToOptionsMapping,
} from '@packages/types/nds/provider';
import { Region, RegionNames } from '@packages/types/nds/region';
import { CloudTeams } from '@packages/types/observability';
import { TagVisibility } from '@packages/types/resourceTagging';
import { FromLocation } from '@packages/types/routes';

import * as app from '@packages/redux/common/app';
import * as clusterDescriptions from '@packages/redux/common/nds/clusterDescriptions';
import * as settings from '@packages/redux/common/settings';
import { createMainReduxStore } from '@packages/redux/project/test-utils/reduxHelpers';

import * as api from '@packages/common/services/api';
import * as authzApi from '@packages/common/services/api/authzApi';
import * as abTestUtil from '@packages/common/utils/abTestUtil';
import * as experimentUtils from '@packages/common/utils/experimentUtils';
import * as resourceManagementApi from '@packages/resource-tagging/resourceManagementApi';
import { Permission, validateAuthzMessaging } from '@packages/authz';
import {
  debouncedSaveClusterDraft,
  deleteClusterDraftOrSendError,
  getRelevantClusterDraft,
  sendClusterDraftLoadedEvent,
} from '@packages/cluster-drafts/utils';
import { saveTemplateCluster } from '@packages/cluster-editor/saveNDSCluster';
import NDSUserCollection from '@packages/common/collections/NDSUserCollection';
import WhitelistedIpCollection from '@packages/common/collections/WhitelistedIpCollection';
import { Routes } from '@packages/common/constants/project';
import { ATLAS_HIDE_QUICK_ACCESS_PAGE } from '@packages/common/constants/settingNames';
//fixtures
import clusterFixtures from '@packages/common/fixtures/clusterFixtures';
import SettingsModel from '@packages/common/models/Settings';
import { getPaymentMethods, hasEffectivePaymentMethod } from '@packages/common/services/api/billingApi';
import clusterDescriptionsApi from '@packages/common/services/api/nds/clusterDescriptionsApi';
import networkSettingsApi from '@packages/common/services/api/nds/networkSettingsApi';
import { getOnlineArchives } from '@packages/common/services/api/nds/onlineArchiveApi';
import {
  fetchAWSPrivateLinks,
  fetchAzurePrivateLinks,
  fetchGCPPSCs,
} from '@packages/common/services/api/nds/privateEndpointApi';
import { getPrivateEndpointsForServerlessProject } from '@packages/common/services/api/nds/serverlessPrivateEndpointApi';
import backboneHistoryShim from '@packages/common/utils/backboneHistoryShim';
import { getMarketplaceAccountLinkQueryString } from '@packages/common/utils/billingHelpers';
import getCurrentRouteQueryString from '@packages/common/utils/getCurrentRouteQueryString';
import { navigateTo } from '@packages/common/utils/navigateTo';
import { PurchaseType } from '@packages/common/utils/paymentPageHelpers';
import analytics, { PAGE_CALLS, SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import { useMarketplaceLinkStatusPoller } from '@packages/hooks/useMarketplaceLinkStatusPoller';
import { sendError } from '@packages/observability';
import ipWhitelistService from '@packages/project/common/services/ipWhitelistService';
import usersService from '@packages/project/common/services/usersService';
import ClusterStarterTemplatesPage from '@packages/project/nds/clusters/components/ClusterStarterTemplatesPage';
import { clearQueryParams } from '@packages/project/pricingPageLanding/utils/LandingPageUtil';
import { act, fireEvent, render, screen, waitFor, within } from '@packages/react-testing-library';
import getTypedMockOf from '@packages/test-utils/getTypedMockOf';
import fakeLocation from '@packages/test-utils/locationMock';

// mocks
jest.mock('@packages/common/utils/navigateTo');
jest.mock('@packages/common/services/api/nds/clusterDescriptionsApi');
jest.mock('@packages/cluster-editor/saveNDSCluster');
jest.mock('@packages/common/services/api/billingApi');
jest.mock('@packages/resource-tagging/resourceManagementApi');
jest.mock('@packages/cluster-drafts/utils', () => {
  const originalModule = jest.requireActual('@packages/cluster-drafts/utils');

  return {
    __esModule: true,
    ...originalModule,
    debouncedSaveClusterDraft: jest.fn(),
    getRelevantClusterDraft: jest.fn(),
    sendClusterDraftLoadedEvent: jest.fn(),
    deleteClusterDraftOrSendError: jest.fn(),
  };
});
jest.mock('@packages/cluster-drafts/clusterDraftsApi');
jest.mock('@packages/observability');
jest.mock('@packages/common/utils/segmentAnalytics');
jest.mock('@packages/hooks/abTestHooks');
jest.mock('@packages/hooks/useMarketplaceLinkStatusPoller');
jest.mock('@packages/common/utils/billingHelpers');
jest.mock('@packages/common/utils/experimentUtils');
jest.mock('@packages/common/utils/abTestUtil');
jest.mock('@packages/project/common/services/usersService');
jest.mock('@packages/common/services/api/nds/privateEndpointApi');
jest.mock('@packages/project/common/services/ipWhitelistService');
jest.mock('@packages/redux/common/app', () => ({
  ...jest.requireActual('@packages/redux/common/app'),
  getActiveOrgName: jest.fn(),
  getActiveOrgId: jest.fn().mockReturnValue('test-active-org-id'),
  getActiveGroupId: jest.fn().mockReturnValue('test-active-group-id'),
  __esModule: true,
}));
jest.mock('@packages/common/utils/getCurrentRouteQueryString');
jest.mock('@packages/common/services/api/authzApi');
jest.mock('@packages/common/services/api/nds/serverlessPrivateEndpointApi');
jest.mock('@packages/common/services/api/nds/networkSettingsApi');
jest.mock('@packages/common/services/api/nds/onlineArchiveApi');
jest.mock('@packages/project/pricingPageLanding/utils/LandingPageUtil');

const defaultTemplates: DefaultTemplates = {
  AWS: clusterFixtures.getAwsDefaultTemplates(),
  GCP: {
    ...clusterFixtures.getGcpDefaultTemplates(),
    serverless: clusterFixtures.getServerlessDefaultClusterDescriptionForGcp_asJSON(),
  },
  AZURE: {
    ...clusterFixtures.getAzureDefaultTemplates(),
    serverless: clusterFixtures.getServerlessDefaultClusterDescriptionForAzure_asJSON(),
  },
  FREE: clusterFixtures.getFreeAwsDefaultClusterDescription(),
  SERVERLESS: clusterFixtures.getServerlessDefaultClusterDescription(),
  FLEX: clusterFixtures.getFlexDefaultClusterDescription(),
} as unknown as DefaultTemplates;

const providerOptions: ProviderToOptionsMapping = {
  AWS: clusterFixtures.getAwsClusterOptions_asJSON(),
  GCP: clusterFixtures.getGcpClusterOptions_asJSON(),
  AZURE: clusterFixtures.getAzureClusterOptions_asJSON(),
  FREE: clusterFixtures.getFreeClusterOptions_asJSON(),
  SERVERLESS: clusterFixtures.getServerlessClusterOptions_asJSON(),
  FLEX: clusterFixtures.getFlexClusterOptions_asJSON(),
} as unknown as ProviderToOptionsMapping;

const defaultRegions: DefaultTemplateRegions = {
  AWS: {
    ...clusterFixtures.getAwsDefaultRegions(),
    m0: providerOptions.AWS.regions[1].key,
  },
  GCP: {
    ...clusterFixtures.getGcpDefaultRegions(),
    m0: providerOptions.FREE.instanceSizes.M0!.availableRegions.filter(({ providerName }) => providerName === 'GCP')[0]
      .regionName,
    replicaSetM10: providerOptions.GCP.instanceSizes.M10!.availableRegions[1].regionName,
  },
  AZURE: clusterFixtures.getAzureDefaultRegions(),
};

const SERVERLESS_REGION_AWS: Region = {
  continent: 'North America',
  provider: 'AWS',
  key: 'US_EAST_2',
  location: 'Ohio (us-east-2)',
  latitude: 39.0437567,
  longitude: -77.4874416,
  isRecommended: true,
  name: 'us-east-2',
} as unknown as Region;

const SERVERLESS_REGION_GCP: Region = {
  continent: 'North America',
  provider: 'GCP',
  key: 'US_EAST_1',
  location: 'Georgia',
  latitude: 39.0437567,
  longitude: -77.4874416,
  isRecommended: true,
  name: 'us-east-3',
} as unknown as Region;

const mockGroupId = 'groupId';
const mockOrgId = 'orgId';

const defaultSettingsModel = new SettingsModel({
  ORG_ID: mockOrgId,
  RECAPTCHA_PUBLIC: 'captcha',
  isRecaptChaEnabled: false,
  SELF_SERVE_PAYMENTS_ENABLED: false,
  HAS_ORG_BILLING_ADMIN: true,
  IS_GROUP_ATLAS_ADMIN: true,
  GROUP_ENABLED_FEATURE_FLAGS: ['SERVERLESS_SHARED_UI_OPTION_ENABLED'],
  NDS_FLEX_FEATURE_ENABLED: true,
});

const paymentMethod: PaymentMethod = {
  id: 'abc',
  created: '2022-03-11T20:40:28Z',
  status: PaymentMethodStatus.ACTIVE,
  type: 'CREDIT_CARD',
};

describe('@packages/project/nds/clusters/components/ClusterStarterTemplatesPage', () => {
  const navigateToMock = getTypedMockOf(navigateTo);
  const templateCostEstimateMock = getTypedMockOf(clusterDescriptionsApi.getTemplateClusterCostEstimate);
  const saveTemplateClusterMock = getTypedMockOf(saveTemplateCluster);
  const getDefaultProcessArgsMock = getTypedMockOf(clusterDescriptionsApi.getDefaultProcessArgs);
  const getPermissionsMock = getTypedMockOf(resourceManagementApi.getPermissions).mockReturnValue(
    Promise.resolve({
      suggestedTags: true,
      previouslyUsedTags: true,
    })
  );
  const getSuggestedTagsMock = getTypedMockOf(resourceManagementApi.getSuggestedTags);
  const getPreviousTagsMock = getTypedMockOf(resourceManagementApi.getPreviousTags);
  const debouncedSaveClusterDraftMock = getTypedMockOf(debouncedSaveClusterDraft);
  const getRelevantClusterDraftMock = getTypedMockOf(getRelevantClusterDraft);
  const sendClusterDraftLoadedEventMock = getTypedMockOf(sendClusterDraftLoadedEvent);
  const hasPaymentMock = getTypedMockOf(hasEffectivePaymentMethod);
  const getPaymentMethodsMock = getTypedMockOf(getPaymentMethods);
  const sendErrorMock = getTypedMockOf(sendError);
  const trackMock = getTypedMockOf(analytics.track);
  const pageMock = getTypedMockOf(analytics.page);
  const useMarketplaceLinkStatusPollerMock = getTypedMockOf(useMarketplaceLinkStatusPoller);
  const getMarketplaceAccountLinkQueryStringMock = getTypedMockOf(getMarketplaceAccountLinkQueryString);
  const getActiveOrgNameMock = app.getActiveOrgName as jest.Mock;
  const toggleGroupEditableFeatureFlagSpy = jest.spyOn(settings, 'toggleGroupEditableFeatureFlag');
  const getCurrentRouteQueryStringMock = getTypedMockOf(getCurrentRouteQueryString);
  const getFeatureFlagMock = getTypedMockOf(abTestUtil.getExperimentFeatureFlag);
  const getExperimentAttributeMock = getTypedMockOf(experimentUtils.getExperimentAttribute);
  const useExperimentAttributeMock = getTypedMockOf(experimentUtils.useExperimentAttribute);
  const trackIsInSampleMock = getTypedMockOf(experimentUtils.trackIsInSample);
  const fetchUsersMock = getTypedMockOf(usersService.fetchUsers);
  const fetchAWSPrivateLinksMock = getTypedMockOf(fetchAWSPrivateLinks);
  const fetchAzurePrivateLinksMock = getTypedMockOf(fetchAzurePrivateLinks);
  const fetchGCPPSCsMock = getTypedMockOf(fetchGCPPSCs);
  const loadWhitelistMock = getTypedMockOf(ipWhitelistService.loadWhitelist);
  const getAssignmentMock = getTypedMockOf(experimentUtils.getAssignment);
  const getPrivateEndpointsForServerlessProjectMock = getTypedMockOf(getPrivateEndpointsForServerlessProject);
  const getPeerConnectionsForGroupMock = getTypedMockOf(networkSettingsApi.getPeerConnectionsForGroup);
  const getOnlineArchivesMock = getTypedMockOf(getOnlineArchives);
  const deleteClusterDraftOrSendErrorMock = getTypedMockOf(deleteClusterDraftOrSendError);
  const clearQueryParamsMock = getTypedMockOf(clearQueryParams);
  const getClusterDescriptionsAsListMock = jest.spyOn(clusterDescriptions, 'getClusterDescriptionsAsList');

  const now = new Date();

  let renderResult: $TSFixMe;
  let assignMock: jest.Mock;

  const mockMarketplaceLinkFlow = (status: PartnerSubscriptionStatus) => {
    useMarketplaceLinkStatusPollerMock.mockImplementation(() => ({
      marketplaceAccountLinkStatus: status,
      beginPolling: () => Promise.resolve(),
      stopPolling: () => jest.fn(),
      changeInterval: () => jest.fn(),
    }));

    if (status !== PartnerSubscriptionStatus.NOT_LINKING) {
      getMarketplaceAccountLinkQueryStringMock.mockReturnValue(PartnerType.AWS);
    } else {
      getMarketplaceAccountLinkQueryStringMock.mockReturnValue(null);
    }

    getActiveOrgNameMock.mockReturnValue('fakeOrgName');
    saveTemplateClusterMock.mockReturnValue(Promise.resolve());
  };

  beforeEach(() => {
    templateCostEstimateMock.mockImplementation((_groupId, templateKey, templateParams) => {
      let monthlyBaseEstimateCents = templateParams.cloudProvider === BackingCloudProvider.AWS ? 7200 : 14400;
      if (templateKey === TemplateKey.SERVERLESS) {
        return Promise.resolve({ tier1ReadCents: 10 } as BillingEstimate);
      }
      if (templateKey === TemplateKey.FLEX) {
        return Promise.resolve({
          monthlyBaseEstimateCents: 720,
          flexMonthlyMinEstimateCents: 720,
          flexMonthlyMaxEstimateCents: 3000,
        } as CostEstimate);
      }
      if (templateKey === TemplateKey.M2) {
        monthlyBaseEstimateCents = 899;
      } else if (templateKey === TemplateKey.REPLICA_SET_M10 && templateParams.regionKey === 'EU_CENTRAL_1') {
        monthlyBaseEstimateCents = 10000;
      }
      return Promise.resolve({
        monthlyBaseEstimateCents,
      } as CostEstimate);
    });
    mockMarketplaceLinkFlow(PartnerSubscriptionStatus.NOT_LINKING);
    hasPaymentMock.mockResolvedValue(false);
    getPaymentMethodsMock.mockResolvedValue([]);
    getCurrentRouteQueryStringMock.mockReturnValue('transactionId=68e66bd3-7dd9-44bf-b8cb-6525d0e41eb9');
    getSuggestedTagsMock.mockResolvedValue({
      tagKeyValues: [
        {
          key: 'Suggested',
          values: ['environment'],
        },
      ],
    });
    getPreviousTagsMock.mockResolvedValue({
      tagKeyValues: [
        {
          key: 'Previous',
          values: ['application'],
        },
      ],
    });
    getExperimentAttributeMock.mockReturnValue(false);
    useExperimentAttributeMock.mockReturnValue(false);
    jest.spyOn(api.allClusters, 'getGroups').mockReturnValue(Promise.resolve([]));
    fetchUsersMock.mockResolvedValue(new NDSUserCollection());
    fetchAWSPrivateLinksMock.mockResolvedValue([]);
    fetchAzurePrivateLinksMock.mockResolvedValue([]);
    fetchGCPPSCsMock.mockResolvedValue([]);
    loadWhitelistMock.mockResolvedValue(new WhitelistedIpCollection());
    getAssignmentMock.mockResolvedValue(null);
    getPrivateEndpointsForServerlessProjectMock.mockResolvedValue([]);
    getPeerConnectionsForGroupMock.mockResolvedValue([]);
    getOnlineArchivesMock.mockResolvedValue([]);
    getClusterDescriptionsAsListMock.mockReturnValue([]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('when cluster starter is loaded', () => {
    beforeEach(async () => {
      getExperimentAttributeMock.mockImplementation((expAttr, fallback) =>
        expAttr === ExperimentFeatureFlag.showOnCST ? false : fallback
      );
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT', 'SERVERLESS_SHARED_UI_OPTION_ENABLED'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                USER_COUNTRY_CODE: 'US',
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('sends a segment page event', () => {
      expect(pageMock).toHaveBeenCalledWith(PAGE_CALLS.CLUSTER_STARTER_TEMPLATES, {
        clusterCount: 0,
      });
    });

    it('renders the title for Cluster Starter Templates Page', () => {
      expect(screen.getByText('Deploy your cluster')).toBeInTheDocument();
    });

    it('calls the getSuggestedTags and getPreviousTags endpoints', () => {
      expect(getPermissionsMock).toHaveBeenCalled();
      expect(getSuggestedTagsMock).toHaveBeenCalled();
      expect(getPreviousTagsMock).toHaveBeenCalled();
    });

    it('renders the description', () => {
      expect(screen.getByText(/Use a template below or set up/i)).toBeInTheDocument();
    });

    it('AWS is selected as the default provider', () => {
      const selector = screen.getByTestId('cloud-provider-selector');
      const options = selector.querySelectorAll('input');
      const checkedOption = Array.from(options).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(CloudProvider.AWS);
    });

    it('shows the m10 is selected', () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      const checkedOption = Array.from(options).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M10);
    });

    it('displays the correct price estimate for m10', async () => {
      const m10TemplateCard = screen.getByTestId('template-cards-m10');
      expect(within(m10TemplateCard).getByText('$0.10/hour'));
    });

    it('checks all cards are enabled', () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const disabledCards = clusterSelector.querySelectorAll('input[disabled]');
      expect(disabledCards).toHaveLength(0);
    });

    it('shows the details in the card', () => {
      const m10TemplateCard = screen.getByTestId('template-cards-m10');
      const m10TemplateCardContent = within(m10TemplateCard);
      expect(m10TemplateCardContent.getByText(/1 GB/i)).toBeInTheDocument();
    });

    it('shows the price in the card', () => {
      const m10TemplateCard = screen.getByTestId('template-cards-m10');
      const m10TemplateCardContent = within(m10TemplateCard);
      expect(m10TemplateCardContent.getByText(`$0.10/hour`)).toBeInTheDocument();
    });

    it('renders the specified default region as the default region', () => {
      const expectedRegionObj = providerOptions.AWS.regions.find(
        ({ key }) => key === defaultRegions.AWS.replicaSetM10
      )!;
      const regionSelector = document.getElementsByClassName('Select-control')[0];
      const region = regionSelector.querySelector('strong');
      expect(region?.innerHTML).toBe(expectedRegionObj.location);
    });

    it('renders the cluster name selector', () => {
      const selector = screen.getByTestId('cluster-starter-template-name-selector');
      expect(selector.querySelector('input')?.value).toBe('Cluster0');
    });

    it('shows the correct cluster details', () => {
      expect(screen.getByText(/Atlas enables auto-scaling for cluster storage and cluster tier./i)).toBeInTheDocument();
      expect(screen.getByText('Storage Scaling')).toBeInTheDocument();
      expect(screen.getByText('Cluster Tier Scaling')).toBeInTheDocument();
      expect(
        screen.getByText(/Snapshots are taken automatically and stored according to your backup and retention policy./i)
      ).toBeInTheDocument();
      expect(screen.getByText('Continuous Cloud Backup')).toBeInTheDocument();
    });

    it('calls serverless cost estimation once', () => {
      const serverlessCalls = templateCostEstimateMock.mock.calls.filter(
        ([_groupId, templateKey]) => templateKey === TemplateKey.SERVERLESS
      );
      expect(serverlessCalls.length).toBe(1);
    });

    it('renders the Price Description banner', () => {
      expect(screen.getByTestId('price-description-banner')).toBeInTheDocument();
    });

    it('renders the footer', () => {
      expect(screen.getByTestId('cluster-starter-templates-footer')).toBeInTheDocument();
    });

    it('sends the Cluster Starter Templates Cards Viewed Segment analytics event with the correct properties', async () => {
      expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.CLUSTER_STARTER_TEMPLATES_CARDS_VIEWED, {
        context: 'Cluster Starter Templates',
        cards: ['M10', 'SERVERLESS', 'M0'],
      });
    });

    it('tracks the event with a segment call when the deploy later button is clicked', () => {
      const deployLaterCta = screen.getByText("I'll do this later");
      fireEvent.click(deployLaterCta);
      expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
        action: 'Deploy Later Clicked',
        context: 'Cluster Starter Templates',
        clusterCount: 0,
      });
    });

    it('when M10 is selected, renders the add sample data checkbox, but not the automate security checkbox', () => {
      expect(screen.queryByTestId('automate-security-setup-checkbox')).not.toBeInTheDocument();
      const loadSampleDatasetCheckbox = screen.getByTestId('preload-sample-dataset-checkbox');
      expect(loadSampleDatasetCheckbox).toBeInTheDocument();
      expect(loadSampleDatasetCheckbox).not.toBeChecked();

      expect(screen.queryByText(/you will be billed/)).not.toBeInTheDocument();

      fireEvent.click(loadSampleDatasetCheckbox);

      expect(screen.queryByText(/you will be billed/)).not.toBeInTheDocument();
    });

    it('when serverless is selected, renders the add sample data checkbox and the automate security checkbox ', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[1]);
      });
      const automateSecurityCheckbox = screen.getByTestId('automate-security-setup-checkbox');
      expect(automateSecurityCheckbox).toBeInTheDocument();
      expect(automateSecurityCheckbox).toBeChecked();

      const loadSampleDatasetCheckbox = screen.getByTestId('preload-sample-dataset-checkbox');
      expect(loadSampleDatasetCheckbox).toBeInTheDocument();
      expect(loadSampleDatasetCheckbox).not.toBeChecked();

      expect(screen.queryByText(/you will be billed/)).not.toBeInTheDocument();
      await act(async () => {
        fireEvent.click(loadSampleDatasetCheckbox);
      });

      expect(screen.getByTestId('boc-warning-callout')).toBeInTheDocument();
    });

    it('when M0 is selected, renders the load sample data checkbox and the automate security checkbox ', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[2]);
      });
      const automateSecurityCheckbox = screen.getByTestId('automate-security-setup-checkbox');
      expect(automateSecurityCheckbox).toBeInTheDocument();
      expect(automateSecurityCheckbox).toBeChecked();

      const loadSampleDatasetCheckbox = screen.getByTestId('preload-sample-dataset-checkbox');
      expect(loadSampleDatasetCheckbox).toBeInTheDocument();
      expect(loadSampleDatasetCheckbox).toBeChecked();
      expect(screen.queryByText(/you will be billed/)).not.toBeInTheDocument();
    });

    it('does display configuration category labels', () => {
      expect(screen.getByTestId('boc-configurations')).toBeInTheDocument();
      expect(screen.getByTestId('boc-included-features')).toBeInTheDocument();
      expect(screen.getByTestId('boc-quick-setup')).toBeInTheDocument();
    });

    it('sends an analytics track event when the preload sample dataset checkbox is clicked', async () => {
      const loadSampleDatasetCheckbox = screen.getByTestId('preload-sample-dataset-checkbox');
      await act(async () => {
        fireEvent.click(loadSampleDatasetCheckbox);
      });

      expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        action: 'Preload sample dataset checkbox clicked',
        checked: true,
        context: 'Cluster Starter Templates',
        selected_tier: 'M10',
        is_first_cluster: true,
      });
    });

    it('sends an analytics track event when the automate security setup checkbox is clicked', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[1]);
      });

      const automateSecuritySetupCheckbox = screen.getByTestId('automate-security-setup-checkbox');
      await act(async () => {
        fireEvent.click(automateSecuritySetupCheckbox);
      });

      expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
        action: 'Automate security setup checkbox clicked',
        checked: false,
        context: 'Cluster Starter Templates',
        selected_tier: 'SERVERLESS',
        is_first_cluster: true,
      });
    });

    it('sends an analytics track event when the preload sample dataset tooltip is hovered', async () => {
      const loadSampleDatasetTooltip = screen.getByTestId('preload-sample-dataset-tooltip');
      await act(async () => {
        fireEvent.mouseEnter(loadSampleDatasetTooltip);
      });

      await waitFor(() => {
        expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          action: 'Preload sample dataset tooltip hovered',
          context: 'Cluster Starter Templates',
          selected_tier: 'M10',
        });
      });
    });

    it('sends an analytics track event when the automate security setup tooltip is hovered', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[1]);
      });

      const automateSecuritySetupTooltip = screen.getByTestId('automate-security-setup-tooltip');
      await act(async () => {
        fireEvent.mouseEnter(automateSecuritySetupTooltip);
      });

      await waitFor(() => {
        expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          action: 'Automate security setup tooltip hovered',
          context: 'Cluster Starter Templates',
          selected_tier: 'SERVERLESS',
        });
      });
    });

    describe('when create button is clicked', () => {
      beforeEach(async () => {
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        jest.spyOn(backboneHistoryShim, 'getFragment').mockReturnValue('?');
        await act(async () => {
          fireEvent.click(createButton);
        });
      });

      it('redirects to payment', () => {
        expect(assignMock).toHaveBeenCalledWith(expect.stringContaining('/v2#/org/orgId/checkout?'));

        const url = assignMock.mock.calls.pop()[0];
        const params = new URLSearchParams(url);

        expect(params.get('isEdit')).toEqual('false');
        expect(params.get('groupId')).toEqual('groupId');
        expect(params.get('clusterName')).toEqual('Cluster0');
        expect(params.get('fromStarterTemplates')).toEqual('true');
      });
    });

    describe('when GCP is selected as the cloud provider', () => {
      let providerSelector: HTMLElement;

      beforeEach(async () => {
        providerSelector = screen.getByTestId('cloud-provider-selector');
        const options = providerSelector.querySelectorAll('input');
        await act(async () => {
          fireEvent.click(options[1]);
        });
      });

      it('shows the gcp is selected', () => {
        const updatedOptions = providerSelector.querySelectorAll('input');
        const checkedOption = Array.from(updatedOptions).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(CloudProvider.GCP);
      });

      it('selects the GCP default region', () => {
        const expectedRegionObj = providerOptions.GCP.regions.find(
          ({ key }) => key === defaultRegions.GCP.replicaSetM10
        )!;
        const regionSelector = document.getElementsByClassName('Select-control')[0];
        const region = regionSelector.querySelector('strong');
        expect(region?.innerHTML).toBe(expectedRegionObj.location);
      });

      it('shows the correct updated price for the other cluster templates', () => {
        const m0TemplateCard = screen.getByTestId('template-cards-m0');
        expect(within(m0TemplateCard).getByText('Free')).toBeInTheDocument();
        const serverlessTemplateCard = screen.getByTestId('template-cards-serverless');
        expect(within(serverlessTemplateCard).getByText('$0.10/1M reads')).toBeInTheDocument();
      });

      it('sends an analytics track event', () => {
        expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          context: 'Cluster Starter Templates',
          action: 'Cloud Provider Selected',
          value: 'GCP',
          clusterCount: 0,
        });
      });
    });

    describe('when a new region is selected from the dropdown', () => {
      beforeEach(() => {
        const regionDropdown = renderResult.container.querySelector('.Select-control');
        regionDropdown && fireEvent.keyDown(regionDropdown, { key: 'Down', keyCode: 40 });
        fireEvent.mouseDown(
          screen.getByRole('option', {
            name: /Frankfurt/i,
          })
        );
      });

      it('updates the selected dropdown value', () => {
        const regionSelector = document.getElementsByClassName('Select-value')[0];
        const region = regionSelector.querySelector('strong');
        expect(region?.innerHTML).toBe('Frankfurt');
      });

      it('sends an analytics track event', () => {
        expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          context: 'Cluster Starter Templates',
          action: 'Cloud Provider Region Selected',
          low_carbon_region: true,
          region_key: 'EU_CENTRAL_1',
          region_location: 'Frankfurt',
          clusterCount: 0,
        });
      });

      it('displays updated price estimates for m10', async () => {
        const m10TemplateCard = screen.getByTestId('template-cards-m10');
        await waitFor(() => expect(within(m10TemplateCard).getByText('$0.14/hour')).toBeInTheDocument());
      });
    });

    describe('when a region not supported by serverless is selected from the dropdown', () => {
      beforeEach(() => {
        const serverlessCalls = templateCostEstimateMock.mock.calls.filter(
          ([_groupId, templateKey]) => templateKey === TemplateKey.SERVERLESS
        );
        expect(serverlessCalls.length).toBe(1);

        const regionDropdown = renderResult.container.querySelector('.Select-control');
        regionDropdown && fireEvent.keyDown(regionDropdown, { key: 'Down', keyCode: 40 });
        fireEvent.mouseDown(
          screen.getByRole('option', {
            name: /Frankfurt/i,
          })
        );
      });
      // We call our mock once when the page loads, but we will not call it again
      it('does not call our serverless cost estimate again', () => {
        const serverlessCalls = templateCostEstimateMock.mock.calls.filter(
          ([_groupId, templateKey]) => templateKey === TemplateKey.SERVERLESS
        );
        expect(serverlessCalls.length).toBe(1);
      });
    });

    describe('when user enters an invalid cluster name', () => {
      let nameSelector: HTMLElement;
      const invalidName = '%myCluster@';

      beforeEach(async () => {
        nameSelector = screen.getByTestId('cluster-starter-template-name-selector');
        await act(async () => {
          fireEvent.change(nameSelector.querySelectorAll('input')[0], { target: { value: invalidName } });
        });
      });

      it('shows error message', () => {
        expect(
          screen.getByText(/Cluster names can only contain ASCII letters, numbers, and hyphens./)
        ).toBeInTheDocument();
      });

      it('disables the create button', () => {
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        expect(createButton).toBeAriaDisabled();
      });
    });

    describe('when user enters an valid cluster name', () => {
      let nameSelector: HTMLElement;
      const validName = 'MyCluster';

      beforeEach(async () => {
        nameSelector = screen.getByTestId('cluster-starter-template-name-selector');
        await act(async () => {
          fireEvent.change(nameSelector.querySelectorAll('input')[0], { target: { value: validName } });
        });
      });

      it('does NOT show error message', () => {
        expect(
          screen.queryByText(/Cluster names can only contain ASCII letters, numbers, and hyphens./)
        ).not.toBeInTheDocument();
      });
    });

    describe('when advanced configuration CTA is clicked', () => {
      beforeEach(() => {
        const advancedConfigCta = screen.getByText('Go to Advanced Configuration');
        fireEvent.click(advancedConfigCta);
      });

      it('redirects to cluster builder with the proper query params', () => {
        expect(navigateToMock).toHaveBeenCalledWith(
          Routes.ClusterBuilder({ query: 'filter=advanced&from=starterTemplates' })
        );
      });

      it('sends an analytics track event', () => {
        expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          action: 'Advanced Configuration Clicked',
          context: 'Cluster Starter Templates',
          clusterCount: 0,
        });
      });
    });

    describe('when M0 is selected as the cluster instance', () => {
      let clusterSelector: HTMLElement;

      beforeEach(async () => {
        const getActiveOrgActivePaymentMethodMock = jest.spyOn(app, 'getActiveOrgActivePaymentMethod');
        getActiveOrgActivePaymentMethodMock.mockReturnValue(paymentMethod);

        clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        await act(async () => {
          fireEvent.click(options[2]);
        });
      });

      it('shows the M0 is selected', () => {
        const updatedOptions = clusterSelector.querySelectorAll('input');
        const checkedOption = Array.from(updatedOptions).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M0);
      });

      describe('when create button is clicked', () => {
        beforeEach(() => {
          const createButton = screen.getByTestId('lg-form_footer-primary_button');
          act(() => {
            fireEvent.click(createButton);
          });
        });

        it('calls the create template cluster api', () => {
          expect(saveTemplateClusterMock).toHaveBeenCalledWith({
            groupId: mockGroupId,
            templateKey: TemplateKey.M0,
            templateParams: {
              clusterName: 'Cluster0',
              automateSecurity: true,
              loadSampleDataset: true,
              tags: [],
              cloudProvider: CloudProvider.AWS,
              regionKey: 'US_EAST_1',
              isAutoScalingEnabled: true,
              isInCreateSampleSearchIndexVariant: false,
            },
            recaptchaToken: undefined,
            analyticsTrackProperties: {
              context: 'Cluster Starter Templates',
              current_active_payment_method_type: 'CREDIT_CARD',
              hourly_cost: 'Free',
              user_country_code: 'US',
            },
            isFirstCluster: true,
            toggleGroupEditableFeatureFlag: expect.anything(),
            projectHasDedicatedClusters: false,
          });
        });
      });

      describe('and a region that is not available in M0 is selected', () => {
        beforeEach(async () => {
          await act(async () => {
            // Unmount and remount because rerender does not work here.
            renderResult.unmount();
            renderResult = render(
              <ClusterStarterTemplatesPage
                groupId={mockGroupId}
                defaultTemplates={defaultTemplates}
                defaultRegions={defaultRegions}
                providers={providerOptions}
                settingsModel={defaultSettingsModel}
                windowLocation={fakeLocation}
              />
            );
          });
          // Open the region dropdown
          const regionDropdown = screen.getByLabelText('Caret Down Icon');
          fireEvent.mouseDown(regionDropdown);
          // Select the Stockholm region, available for M10 but not for M0
          await act(async () => {
            const regionNotAvailableInM0 = screen.getByLabelText(/Stockholm/i); // EU_NORTH_1
            fireEvent.mouseDown(regionNotAvailableInM0);
          });
        });

        it('still shows "Free" as the price for M0', () => {
          const m0TemplateCard = screen.getByTestId('template-cards-m0');
          expect(within(m0TemplateCard).getByText('Free')).toBeInTheDocument();
        });
      });
    });

    describe('when project already has an M0 cluster (and AHP is enabled)', () => {
      beforeEach(async () => {
        // Mock existing clusters
        getClusterDescriptionsAsListMock.mockReturnValue([
          clusterFixtures.getTypedFreeAwsDefaultClusterDescription_asJSON(),
        ]);
        saveTemplateClusterMock.mockReturnValue(Promise.resolve());

        // eslint-disable-next-line testing-library/no-unnecessary-act
        await act(async () => {
          // Unmount and remount because rerender does not work here.
          renderResult.unmount();
          renderResult = render(
            <Provider
              store={createMainReduxStore({
                group: {
                  [mockGroupId]: {
                    data: {
                      isProjectOverviewEnabled: true,
                    },
                  },
                },
                organization: {
                  [mockOrgId]: {
                    billing: {
                      paymentMethods: [],
                    },
                  },
                },
              })}
            >
              <ClusterStarterTemplatesPage
                groupId={mockGroupId}
                defaultTemplates={defaultTemplates}
                defaultRegions={defaultRegions}
                providers={providerOptions}
                settingsModel={
                  new SettingsModel({
                    ORG_ID: mockOrgId,
                    RECAPTCHA_PUBLIC: 'captcha',
                    isRecaptChaEnabled: false,
                    SELF_SERVE_PAYMENTS_ENABLED: false,
                    GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT', 'SERVERLESS_SHARED_UI_OPTION_ENABLED'],
                    HAS_ORG_BILLING_ADMIN: true,
                    IS_GROUP_ATLAS_ADMIN: true,
                    USER_COUNTRY_CODE: 'US',
                  })
                }
                windowLocation={fakeLocation}
              />
            </Provider>
          );
        });
      });
      it('should display a card for M2 instead of M0', async () => {
        expect(screen.queryByTestId('template-cards-m0')).not.toBeInTheDocument();
        expect(screen.getByTestId('template-cards-m2')).toBeInTheDocument();
      });

      describe('and when M2 is selected as the cluster instance', () => {
        let clusterSelector: HTMLElement;

        beforeEach(async () => {
          hasPaymentMock.mockResolvedValue(true);
          const getActiveOrgActivePaymentMethodMock = jest.spyOn(app, 'getActiveOrgActivePaymentMethod');
          getActiveOrgActivePaymentMethodMock.mockReturnValue(paymentMethod);

          clusterSelector = screen.getByTestId('template-cards');
          const options = clusterSelector.querySelectorAll('input');
          await act(async () => {
            fireEvent.click(options[2]);
          });
        });

        it('shows the M2 is selected', () => {
          const updatedOptions = clusterSelector.querySelectorAll('input');
          const checkedOption = Array.from(updatedOptions).find((cp) => !!cp.checked);
          expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M2);
        });

        it('calls the create template cluster api when the create button is clicked', async () => {
          saveTemplateClusterMock.mockResolvedValue();

          const createButton = screen.getByTestId('lg-form_footer-primary_button');
          act(() => {
            fireEvent.click(createButton);
          });
          expect(saveTemplateClusterMock).toHaveBeenCalledWith({
            groupId: mockGroupId,
            templateKey: TemplateKey.M2,
            templateParams: {
              clusterName: 'Cluster1',
              automateSecurity: false,
              loadSampleDataset: false,
              tags: [],
              cloudProvider: CloudProvider.AWS,
              regionKey: 'US_EAST_1',
              isAutoScalingEnabled: true,
              isInCreateSampleSearchIndexVariant: false,
            },
            recaptchaToken: undefined,
            analyticsTrackProperties: {
              context: 'Cluster Starter Templates',
              current_active_payment_method_type: 'CREDIT_CARD',
              hourly_cost: '$0.012/hour',
              user_country_code: 'US',
            },
            isFirstCluster: false,
            toggleGroupEditableFeatureFlag: expect.anything(),
            projectHasDedicatedClusters: false,
          });
        });

        it('navigates to AHP with the right params when the create button is clicked', async () => {
          saveTemplateClusterMock.mockResolvedValue();

          const createButton = screen.getByTestId('lg-form_footer-primary_button');
          act(() => {
            fireEvent.click(createButton);
          });

          await waitFor(() => {
            // Should not automate security since the automate security checkbox is not visible when there are clusters in the project
            expect(navigateToMock).toHaveBeenCalledWith(Routes.HomeCenter({ automateSecurity: false }));
          });
        });
      });

      describe('when the user changes the selected region', () => {
        beforeEach(async () => {
          // Mock cost estimate with the default regions
          templateCostEstimateMock.mockImplementation((_groupId, templateKey, templateParams) => {
            let monthlyBaseEstimateCents = 1000;
            if (
              templateKey === TemplateKey.M2 &&
              templateParams.regionKey === RegionNames.US_EAST_1 &&
              templateParams.cloudProvider === BackingCloudProvider.AWS
            ) {
              monthlyBaseEstimateCents = 2000;
            }
            return Promise.resolve({
              monthlyBaseEstimateCents,
            } as CostEstimate);
          });

          await act(async () => {
            // Unmount and remount because rerender does not work here.
            renderResult.unmount();
            renderResult = render(
              <ClusterStarterTemplatesPage
                groupId={mockGroupId}
                defaultTemplates={defaultTemplates}
                defaultRegions={defaultRegions}
                providers={providerOptions}
                settingsModel={defaultSettingsModel}
                windowLocation={fakeLocation}
              />
            );
          });

          // Open the region dropdown
          const regionDropdown = screen.getByLabelText('Caret Down Icon');
          fireEvent.mouseDown(regionDropdown);
          // Select the Stockholm region
          await act(async () => {
            fireEvent.mouseDown(screen.getByLabelText(/Stockholm/i));
          });
        });

        it('shows the default price for m2 when the user selects a region that is not available for m2', async () => {
          const m2TemplateCard = screen.getByTestId('template-cards-m2');
          expect(within(m2TemplateCard).getByText('$0.028/hour')).toBeInTheDocument();
        });

        it('fetches cost estimate for m2 using only the first available region', () => {
          expect(templateCostEstimateMock).toHaveBeenCalledWith(
            mockGroupId,
            TemplateKey.M2,
            expect.objectContaining({
              cloudProvider: CloudProvider.AWS,
              regionKey: 'US_EAST_1',
            })
          );
          expect(templateCostEstimateMock).not.toHaveBeenCalledWith(
            mockGroupId,
            TemplateKey.M2,
            expect.objectContaining({
              cloudProvider: CloudProvider.AWS,
              regionKey: 'EU_NORTH_1',
            })
          );
        });
      });

      describe('when the user changes the selected provider', () => {
        beforeEach(async () => {
          // Mock cost estimate with the default regions
          templateCostEstimateMock.mockImplementation((_groupId, templateKey, templateParams) => {
            let monthlyBaseEstimateCents = 1000;
            if (
              templateKey === TemplateKey.M2 &&
              templateParams.regionKey === RegionNames.CENTRAL_US &&
              templateParams.cloudProvider === BackingCloudProvider.GCP
            ) {
              monthlyBaseEstimateCents = 3000;
            }
            return Promise.resolve({
              monthlyBaseEstimateCents,
            } as CostEstimate);
          });

          await act(async () => {
            // Unmount and remount because rerender does not work here.
            renderResult.unmount();
            renderResult = render(
              <ClusterStarterTemplatesPage
                groupId={mockGroupId}
                defaultTemplates={defaultTemplates}
                defaultRegions={defaultRegions}
                providers={providerOptions}
                settingsModel={defaultSettingsModel}
                windowLocation={fakeLocation}
              />
            );
          });

          const providerSelector = screen.getByTestId('cloud-provider-selector');
          const options = providerSelector.querySelectorAll('input');
          await act(async () => {
            fireEvent.click(options[1]);
          });
        });

        it('shows the correct updated price m2 when a different cloud provider is selected', async () => {
          const m2TemplateCard = screen.getByTestId('template-cards-m2');
          expect(within(m2TemplateCard).getByText('$0.042/hour')).toBeInTheDocument();
        });

        it('fetches cost estimate for m2 using only the first available region', () => {
          expect(templateCostEstimateMock).toHaveBeenCalledWith(
            mockGroupId,
            TemplateKey.M2,
            expect.objectContaining({
              cloudProvider: CloudProvider.GCP,
              regionKey: 'CENTRAL_US',
            })
          );
        });
      });
    });
  });

  describe('when cluster starter is loaded with tags from FPP transaction', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      // mock transaction with tags
      await act(async () => {
        renderResult = render(
          <Provider
            store={createMainReduxStore({
              purchase: {
                'test-active-org-id': {
                  purchaseDetails: {
                    cluster: {
                      'test-active-group-id': {
                        '68e66bd3-7dd9-44bf-b8cb-6525d0e41eb9': {
                          billingEstimate: {},
                          clusterDescription:
                            clusterFixtures.getCrossCloudCluster_DuplicateRegionName_asJSON('my cluster'),
                          processArgs: clusterFixtures.getDefaultProcessArgs(),
                          isEdit: false,
                          tags: [
                            {
                              key: 'environment',
                              value: 'development',
                              visibility: TagVisibility.PUBLIC,
                            },
                          ],
                        },
                      },
                    },
                  },
                },
              },
            })}
          >
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                })
              }
              windowLocation={fakeLocation}
            />
          </Provider>
        );
      });
    });

    it('populates the tag row input with the existing tags from the transaction', () => {
      expect(screen.getByDisplayValue('environment')).toBeInTheDocument();
      expect(screen.getByDisplayValue('development')).toBeInTheDocument();
    });
  });

  describe('when cluster starter is loaded with payment mode saved', () => {
    beforeEach(async () => {
      hasPaymentMock.mockResolvedValue(true);
      getPaymentMethodsMock.mockResolvedValue([paymentMethod]);

      providerOptions.SERVERLESS.regions.push(SERVERLESS_REGION_AWS, SERVERLESS_REGION_GCP);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT', 'SERVERLESS_SHARED_UI_OPTION_ENABLED'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                USER_COUNTRY_CODE: 'US',
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    describe('and tags are added', () => {
      let tagKeyInput: HTMLElement;
      let tagValueInput: HTMLElement;
      let createButton: HTMLElement;
      const validKey = 'environment';
      const validValue = 'development';

      beforeEach(async () => {
        getFeatureFlagMock.mockReturnValue(false);
        tagKeyInput = screen.getByPlaceholderText('Select or enter key');
        tagValueInput = screen.getByPlaceholderText('Select or enter value');
        createButton = screen.getByTestId('lg-form_footer-primary_button');
        const getActiveOrgActivePaymentMethodMock = jest.spyOn(app, 'getActiveOrgActivePaymentMethod');
        getActiveOrgActivePaymentMethodMock.mockReturnValue(paymentMethod);

        // Fill out tag key and value
        act(() => {
          fireEvent.change(tagKeyInput, { target: { value: validKey } });
        });

        act(() => {
          fireEvent.change(tagValueInput, { target: { value: validValue } });
        });
      });

      describe('when tag is deleted', () => {
        beforeEach(() => {
          // Delete tag key
          act(() => {
            fireEvent.change(tagKeyInput, { target: { value: '' } });
          });
        });

        it('doesnt disable the create button when tag partially filled', () => {
          expect(createButton.getAttribute('aria-disabled')).toBe('false');
        });

        it('doesnt disable the create button when tag emptied', () => {
          // delete tag value
          act(() => {
            fireEvent.change(tagValueInput, { target: { value: '' } });
          });
          expect(createButton.getAttribute('aria-disabled')).toBe('false');
        });
      });

      describe('when create button is clicked', () => {
        beforeEach(() => {
          act(() => {
            fireEvent.click(createButton);
          });
        });

        it('calls the create template cluster api', () => {
          expect(saveTemplateClusterMock).toHaveBeenCalledWith({
            groupId: mockGroupId,
            templateKey: TemplateKey.REPLICA_SET_M10,
            templateParams: {
              clusterName: 'Cluster0',
              automateSecurity: false,
              loadSampleDataset: false,
              tags: [{ key: 'environment', value: 'development', visibility: TagVisibility.PUBLIC }],
              cloudProvider: CloudProvider.AWS,
              regionKey: 'US_EAST_1',
              isAutoScalingEnabled: true,
              isInCreateSampleSearchIndexVariant: false,
            },
            recaptchaToken: undefined,
            analyticsTrackProperties: {
              context: 'Cluster Starter Templates',
              current_active_payment_method_type: 'CREDIT_CARD',
              hourly_cost: '$0.10/hour',
              user_country_code: 'US',
            },
            isFirstCluster: true,
            toggleGroupEditableFeatureFlag: expect.anything(),
            projectHasDedicatedClusters: false,
          });
        });

        it('disables the hide atlas quick start group feature flag', () => {
          expect(toggleGroupEditableFeatureFlagSpy).toHaveBeenCalledWith({
            groupId: mockGroupId,
            featureFlag: ATLAS_HIDE_QUICK_ACCESS_PAGE,
            enabled: false,
          });
        });

        it('redirects users to the security quickstart page', () => {
          expect(navigateToMock).toHaveBeenCalledWith(Routes.QuickAccessSetup({ includeToast: true }));
        });
      });
    });

    describe('when SERVERLESS is selected as the cluster instance', () => {
      let clusterSelector: HTMLElement;

      beforeEach(async () => {
        const getActiveOrgActivePaymentMethodMock = jest.spyOn(app, 'getActiveOrgActivePaymentMethod');
        getActiveOrgActivePaymentMethodMock.mockReturnValue(paymentMethod);

        clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        await act(async () => {
          fireEvent.click(options[1]);
        });
      });

      it('selects the serverless card', () => {
        const updatedOptions = clusterSelector.querySelectorAll('input');
        const checkedOption = Array.from(updatedOptions).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.SERVERLESS);
      });

      it('renders the default serverless region', () => {
        const expectedRegionObj = providerOptions.SERVERLESS.regions.find(
          ({ key }) => key === defaultRegions.AWS.serverless
        )!;
        const regionSelector = document.getElementsByClassName('Select-control')[0];
        const region = regionSelector.querySelector('strong');
        expect(region?.innerHTML).toBe(expectedRegionObj.location);
      });

      describe('when a region drop down is clicked', () => {
        beforeEach(() => {
          const regionDropdown = renderResult.container.querySelector('.Select-control');
          regionDropdown && fireEvent.keyDown(regionDropdown, { key: 'Down', keyCode: 40 });
        });

        it('does not show the gcp region while in AWS', () => {
          expect(screen.queryAllByRole('option', { name: /Georgia/i })).toHaveLength(0);
        });
      });

      it('shows the correct cluster details', () => {
        expect(screen.getByText('Cloud Backup')).toBeInTheDocument();
        expect(
          screen.getByText(/Snapshots are taken every 6 hours. You can switch backup plans at any time/i)
        ).toBeInTheDocument();
        expect(screen.getByText('Continuous Cloud Backup')).toBeInTheDocument();
      });

      it('shows the details in the card', () => {
        const serverlessTemplateCard = screen.getByTestId('template-cards-serverless');
        const serverlessTemplateCardContent = within(serverlessTemplateCard);
        expect(serverlessTemplateCardContent.getByText(/Up to 512 MB/i)).toBeInTheDocument();
      });

      it('sends a segment analytics track event', () => {
        expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.UX_ACTION_PERFORMED, {
          context: 'Cluster Starter Templates',
          action: 'Clicked Template Door',
          template_name: 'Serverless',
          instance_size: 'SERVERLESS',
          clusterCount: 0,
        });
      });

      describe('when create button is clicked', () => {
        beforeEach(() => {
          const createButton = screen.getByTestId('lg-form_footer-primary_button');
          act(() => {
            fireEvent.click(createButton);
          });
        });

        it('calls the create template cluster api', () => {
          expect(saveTemplateClusterMock).toHaveBeenCalledWith({
            groupId: mockGroupId,
            templateKey: TemplateKey.SERVERLESS,
            templateParams: {
              clusterName: 'ServerlessInstance0',
              automateSecurity: true,
              loadSampleDataset: false,
              tags: [],
              cloudProvider: CloudProvider.AWS,
              regionKey: 'US_EAST_1',
              isAutoScalingEnabled: true,
              isInCreateSampleSearchIndexVariant: false,
            },
            recaptchaToken: undefined,
            analyticsTrackProperties: {
              context: 'Cluster Starter Templates',
              current_active_payment_method_type: 'CREDIT_CARD',
              hourly_cost: '$0.10/1M reads',
              user_country_code: 'US',
            },
            isFirstCluster: true,
            toggleGroupEditableFeatureFlag: expect.anything(),
            projectHasDedicatedClusters: false,
          });
        });
      });

      describe('when create template cluster API is down and create is clicked', () => {
        beforeEach(async () => {
          saveTemplateClusterMock.mockRejectedValue(new Error());
          const createButton = screen.getByTestId('lg-form_footer-primary_button');
          await act(async () => {
            fireEvent.click(createButton);
          });
        });

        it('logs the error', () => {
          expect(sendErrorMock).toHaveBeenCalled();
        });
      });

      describe('when recaptcha is null and create is clicked', () => {
        beforeEach(async () => {
          const recaptchaError = { errorCode: 'INVALID_RECAPTCHA' };
          saveTemplateClusterMock.mockRejectedValue(recaptchaError);
          const createButton = screen.getByTestId('lg-form_footer-primary_button');
          await act(async () => {
            fireEvent.click(createButton);
          });
        });

        it('logs the warning', () => {
          expect(sendErrorMock).toHaveBeenCalledWith({
            error: {
              errorCode: 'INVALID_RECAPTCHA',
            },
            severity: 'warning',
            team: 'Atlas Growth',
          });
        });
      });
    });
  });

  describe('when cluster starter returned to from payment page with payment mode saved', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      getCurrentRouteQueryStringMock.mockReturnValue('fromFpp=true');

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    describe('when create button is clicked', () => {
      beforeEach(async () => {
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        jest.spyOn(backboneHistoryShim, 'getFragment').mockReturnValue('?');
        await act(async () => {
          fireEvent.click(createButton);
        });
      });

      it('redirects to payment', () => {
        expect(assignMock).toHaveBeenCalledWith(expect.stringContaining('/v2#/org/orgId/checkout?'));

        const url = assignMock.mock.calls.pop()[0];
        const params = new URLSearchParams(url);

        expect(params.get('isEdit')).toEqual('false');
        expect(params.get('groupId')).toEqual('groupId');
        expect(params.get('clusterName')).toEqual('Cluster0');
        expect(params.get('fromStarterTemplates')).toEqual('true');
      });
    });
  });

  describe('when user is in Marketplace link flow PENDING state', () => {
    beforeEach(async () => {
      mockMarketplaceLinkFlow(PartnerSubscriptionStatus.PENDING);
      hasPaymentMock.mockResolvedValue(true);

      providerOptions.SERVERLESS.regions.push(SERVERLESS_REGION_AWS, SERVERLESS_REGION_GCP);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
          />
        );
      });
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('disables the Create button', () => {
      const createButton = screen.getByTestId('lg-form_footer-primary_button');
      expect(createButton).toBeAriaDisabled();
    });

    it('renders an informational toast', () => {
      const informationalToast = screen.getByTestId('marketplace-link-status-toast');
      expect(informationalToast).toBeInTheDocument();
    });

    describe('and the user clicks the advanced configuration CTA', () => {
      it('navigates to the cluster builder with the proper query params', () => {
        const advancedConfigCta = screen.getByText('Go to Advanced Configuration');
        fireEvent.click(advancedConfigCta);

        expect(navigateToMock).toHaveBeenCalledWith(
          Routes.ClusterBuilder({
            query: `filter=advanced&from=starterTemplates&marketplaceAccountLink=${PartnerType.AWS}`,
          })
        );
      });
    });
  });

  describe('when user is in Marketplace link flow SUCCESS state', () => {
    beforeEach(async () => {
      mockMarketplaceLinkFlow(PartnerSubscriptionStatus.SUCCESS);

      hasPaymentMock.mockResolvedValue(true);
      getPaymentMethodsMock.mockResolvedValue([paymentMethod]);

      providerOptions.SERVERLESS.regions.push(SERVERLESS_REGION_AWS, SERVERLESS_REGION_GCP);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
          />
        );
      });
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('enables the Create button', () => {
      const createButton = screen.getByTestId('lg-form_footer-primary_button');
      expect(createButton).toBeEnabled();
    });

    it('renders an informational toast', () => {
      const informationalToast = screen.getByTestId('marketplace-link-status-toast');
      expect(informationalToast).toBeInTheDocument();
    });
  });

  describe('when deploy later CTA is clicked and project has isProjectOverviewEnabled setting', () => {
    beforeEach(async () => {
      await act(() => {
        renderResult = render(
          <Provider
            store={createMainReduxStore({
              group: {
                [mockGroupId]: {
                  data: {
                    isProjectOverviewEnabled: true,
                  },
                },
              },
              organization: {
                [mockOrgId]: {
                  billing: {
                    paymentMethods: [paymentMethod],
                  },
                },
              },
            })}
          >
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                })
              }
            />
          </Provider>
        );
      });
      const deployLaterCta = screen.getByText("I'll do this later");
      fireEvent.click(deployLaterCta);
    });

    it('redirects to Atlas Home Center', () => {
      expect(navigateToMock).toHaveBeenCalledWith(Routes.HomeCenter());
    });

    it('tracks the event with a segment call', () => {
      expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
        action: 'Deploy Later Clicked',
        context: 'Cluster Starter Templates',
        clusterCount: 0,
      });
    });
  });

  describe('when deploy later CTA is clicked and project does not have isProjectOverviewEnabled setting', () => {
    beforeEach(async () => {
      await act(() => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
          />
        );
      });
      const deployLaterCta = screen.getByText("I'll do this later");
      fireEvent.click(deployLaterCta);
    });

    it('redirects to clusters page', () => {
      expect(navigateToMock).toHaveBeenCalledWith(Routes.ClusterCard());
    });

    it('tracks the event with a segment call', () => {
      expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
        action: 'Deploy Later Clicked',
        context: 'Cluster Starter Templates',
        clusterCount: 0,
      });
    });
  });

  describe('when user is not a billing admin and org has no payment method', () => {
    beforeEach(async () => {
      providerOptions.SERVERLESS.regions.push(SERVERLESS_REGION_AWS, SERVERLESS_REGION_GCP);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                HAS_ORG_BILLING_ADMIN: false,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
          />
        );
      });

      let clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[0]);
      });
    });

    it('disables the Create button', () => {
      const createButton = screen.getByTestId('lg-form_footer-primary_button');
      expect(createButton).toBeAriaDisabled();
    });

    it('renders a warning banner', () => {
      const alertBanner = screen.getByTestId('warning-banner');
      expect(alertBanner).toBeInTheDocument();
      expect(alertBanner).toContainHTML(
        'There is no payment on file and you must have billing admin permissions to add a new payment method. If you have any questions, contact your organization owner or administrator.'
      );
    });
  });

  describe('when user is not a billing admin but is group atlas admin and org has payment method', () => {
    beforeEach(async () => {
      hasPaymentMock.mockResolvedValue(true);
      getPaymentMethodsMock.mockResolvedValue([paymentMethod]);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                HAS_ORG_BILLING_ADMIN: false,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
          />
        );
      });

      let clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[0]);
      });
    });

    it('does not disable the Create button', () => {
      const createButton = screen.getByTestId('lg-form_footer-primary_button');
      expect(createButton).not.toBeAriaDisabled();
    });

    it('does not render a warning banner', () => {
      expect(screen.queryByTestId('warning-banner')).not.toBeInTheDocument();
    });
  });

  describe('when user is not a billing admin or group atlas admin', () => {
    beforeEach(async () => {
      hasPaymentMock.mockResolvedValue(true);
      getPaymentMethodsMock.mockResolvedValue([paymentMethod]);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                HAS_ORG_BILLING_ADMIN: false,
                IS_GROUP_ATLAS_ADMIN: false,
              })
            }
          />
        );
      });

      let clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[0]);
      });
    });

    it('disables the Create button', () => {
      const createButton = screen.getByTestId('lg-form_footer-primary_button');
      expect(createButton).toBeAriaDisabled();
    });

    it('renders a warning banner', () => {
      const alertBanner = screen.getByTestId('warning-banner');
      expect(alertBanner).toBeInTheDocument();
      expect(alertBanner).toContainHTML(
        'You do not have permission to create a deployment. Please contact your organization admin.'
      );
    });
  });

  describe('when user is a billing admin but not a group atlas admin', () => {
    beforeEach(async () => {
      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: false,
              })
            }
          />
        );
      });
    });

    it('disables the Create button', () => {
      const createButton = screen.getByTestId('lg-form_footer-primary_button');
      expect(createButton).toBeAriaDisabled();
    });

    it('renders a warning banner', () => {
      const alertBanner = screen.getByTestId('warning-banner');
      expect(alertBanner).toContainHTML(
        'You do not have permission to create a deployment. Please contact your organization admin.'
      );
      expect(alertBanner).toBeInTheDocument();
    });
  });

  describe('when user is in Low Carbon Regions on CST Experiment', () => {
    beforeEach(async () => {
      jest.useFakeTimers();

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
          />
        );
      });
    });
    afterEach(() => {
      jest.useRealTimers();
    });

    it('renders the active low carbon legend for AWS', () => {
      expect(screen.getByText(/Low carbon emissions/i)).toBeInTheDocument();
    });

    it('renders the active low carbon legend for GCP', async () => {
      // Select GCP
      const gcpRadioBox = screen.getByTestId('GCP-radio-box');
      expect(gcpRadioBox).toBeInTheDocument();
      await act(async () => {
        fireEvent.click(gcpRadioBox);
      });
      expect(screen.getByText(/Low carbon emissions/i)).toBeInTheDocument();
    });

    it('renders the disabled low carbon legend for Azure', async () => {
      // Select Azure
      const azureRadioBox = screen.getByTestId('AZURE-radio-box');
      expect(azureRadioBox).toBeInTheDocument();
      await act(async () => {
        fireEvent.click(azureRadioBox);
      });
      expect(screen.getByText(/Carbon data unavailable/i)).toBeInTheDocument();
    });

    it('renders the low carbon icons correctly on the region dropdown for AWS', async () => {
      await act(async () => {
        fireEvent.mouseDown(renderResult.container.querySelector('.Select-arrow-zone'));
      });

      // N. Virginia should have the low carbon icon
      expect(screen.getByLabelText('N. Virginia (us-east-1)').querySelector('.low-carbon-region-icon')).toBeTruthy();

      // Ohio should have the low carbon icon
      expect(screen.getByLabelText('Ohio (us-east-2)').querySelector('.low-carbon-region-icon')).toBeTruthy();

      // Hong Kong shoud NOT have the low carbon icon
      expect(screen.getByLabelText('Hong Kong (ap-east-1)').querySelector('.low-carbon-region-icon')).toBeFalsy();
    });

    it('renders the low carbon icons correctly on the region dropdown for GCP', async () => {
      // Select GCP
      const gcpRadioBox = screen.getByTestId('GCP-radio-box');
      expect(gcpRadioBox).toBeInTheDocument();
      await act(async () => {
        fireEvent.click(gcpRadioBox);
      });

      await act(async () => {
        fireEvent.mouseDown(renderResult.container.querySelector('.Select-arrow-zone'));
      });

      // South Carolina should NOT have the low carbon icon
      expect(screen.getByLabelText('South Carolina (us-east1)').querySelector('.low-carbon-region-icon')).toBeFalsy();

      // Iowa should have the low carbon icon
      expect(screen.getByLabelText('Iowa (us-central1)').querySelector('.low-carbon-region-icon')).toBeTruthy();
    });

    it('does not render low carbon icons for Azure', async () => {
      // Select Azure
      const azureRadioBox = screen.getByTestId('AZURE-radio-box');
      expect(azureRadioBox).toBeInTheDocument();
      await act(async () => {
        fireEvent.click(azureRadioBox);
      });

      await act(async () => {
        fireEvent.mouseDown(renderResult.container.querySelector('.Select-arrow-zone'));
      });
      expect(renderResult.container.querySelector('.Select-menu .low-carbon-region-icon')).toBeNull();
    });
  });
  describe('when cluster starter is loaded with an FPP transaction and a cluster draft', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;

      const clusterDescription: ClusterDescription = clusterFixtures.getAwsDefaultClusterDescription_asJSON(
        'my cluster',
        'M10'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M10',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: [{ key: 'cluster', value: 'draft', visibility: TagVisibility.PUBLIC }],
        },
        draftId: '',
        isFromCst: true,
        lastUpdatedTime: now.toISOString(),
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      // mock purchase transaction with different tags
      await act(async () => {
        renderResult = render(
          <Provider
            store={createMainReduxStore({
              purchase: {
                'test-active-org-id': {
                  purchaseDetails: {
                    cluster: {
                      'test-active-group-id': {
                        '68e66bd3-7dd9-44bf-b8cb-6525d0e41eb9': {
                          billingEstimate: {},
                          clusterDescription:
                            clusterFixtures.getCrossCloudCluster_DuplicateRegionName_asJSON('my cluster'),
                          processArgs: clusterFixtures.getDefaultProcessArgs(),
                          isEdit: false,
                          tags: [
                            {
                              key: 'environment',
                              value: 'development',
                              visibility: TagVisibility.PUBLIC,
                            },
                          ],
                        },
                      },
                    },
                  },
                },
              },
            })}
          >
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                  ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                  IS_GROUP_CLUSTER_MANAGER: true,
                })
              }
              windowLocation={fakeLocation}
            />
          </Provider>
        );
      });
    });

    it('populates the tag row input with the existing tags from the draft', () => {
      expect(screen.getByDisplayValue('environment')).toBeInTheDocument();
      expect(screen.getByDisplayValue('development')).toBeInTheDocument();
      expect(screen.queryByDisplayValue('cluster')).not.toBeInTheDocument();
      expect(screen.queryByDisplayValue('draft')).not.toBeInTheDocument();
    });
    it('does not show the cluster drafts notification toast', () => {
      expect(screen.queryByTestId('draft-new-notification-toast')).not.toBeInTheDocument();
    });
    it('sends a cluster draft loaded event', () => {
      expect(sendClusterDraftLoadedEventMock).toHaveBeenCalledWith({
        context: 'Cluster Starter Templates',
        transactionLoadedFrom: ClusterConfigurationSource.FPP,
        lastUpdatedTime: undefined,
        clusterName: 'my cluster',
        instanceSize: 'M10',
      });
    });
  });

  describe('when cluster starter is loaded with a cluster draft', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      const clusterDescription: ClusterDescription = clusterFixtures.getGcpDefaultClusterDescription_asJSON(
        'draft cluster',
        'M10'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M10',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: [{ key: 'cluster tag', value: 'draft tag', visibility: TagVisibility.PUBLIC }],
          loadSampleDataset: true,
        },
        draftId: '',
        isFromCst: true,
        lastUpdatedTime: now.toISOString(),
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });

      expect(screen.getByTestId('draft-new-notification-toast')).toBeInTheDocument();
      const link = screen.getByTestId('toast-link');
      await act(async () => {
        fireEvent.click(link);
      });
    });

    describe('clicks on the continue button on the cluster draft modal', () => {
      beforeEach(async () => {
        expect(screen.getByTestId('cluster-draft-modal')).toBeInTheDocument();
        // click continue button
        const continueWithDraftButton = screen.getByTestId('continue-button');
        await act(async () => {
          fireEvent.click(continueWithDraftButton);
        });
      });

      it('render South Carolina as the region from the cluster draft', () => {
        const regionSelector = document.getElementsByClassName('Select-control')[0];
        const region = regionSelector.querySelector('strong');
        expect(region?.innerHTML).toBe('South Carolina');
      });

      it('renders the cluster name selector', () => {
        const selector = screen.getByTestId('cluster-starter-template-name-selector');
        expect(selector.querySelector('input')?.value).toBe('draft cluster');
      });

      it('GCP is selected as the provider from the cluster draft', () => {
        const selector = screen.getByTestId('cloud-provider-selector');
        const options = selector.querySelectorAll('input');
        const checkedOption = Array.from(options).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(CloudProvider.GCP);
      });

      it('shows the m10 is selected', () => {
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        const checkedOption = Array.from(options).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M10);
      });
      it('populates the tag row input with the existing tags from the cluster draft, not the FPP one', () => {
        expect(screen.getByDisplayValue('cluster tag')).toBeInTheDocument();
        expect(screen.getByDisplayValue('draft tag')).toBeInTheDocument();
        expect(screen.queryByDisplayValue('environment')).not.toBeInTheDocument();
        expect(screen.queryByDisplayValue('development')).not.toBeInTheDocument();
      });

      it('checks the load sample dataset checkbox', () => {
        expect(screen.getByTestId('preload-sample-dataset-checkbox')).toBeChecked();
      });
      it('sends a cluster draft loaded event', () => {
        expect(sendClusterDraftLoadedEventMock).toHaveBeenCalledWith({
          context: 'Cluster Starter Templates',
          transactionLoadedFrom: ClusterConfigurationSource.CST,
          lastUpdatedTime: now.toISOString(),
          clusterName: 'draft cluster',
          instanceSize: 'M10',
        });
      });
    });
  });

  describe('when cluster starter is loaded with a cluster draft with an invalid cluster name', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      const clusterDescription: ClusterDescription = clusterFixtures.getGcpDefaultClusterDescription_asJSON(
        'times_square_hot_dog_cluster',
        'M10'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M10',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: [{ key: 'cluster tag', value: 'draft tag', visibility: TagVisibility.PUBLIC }],
          loadSampleDataset: true,
        },
        draftId: '',
        lastUpdatedTime: '',
        isFromCst: true,
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
      expect(screen.getByTestId('draft-new-notification-toast')).toBeInTheDocument();
      const link = screen.getByTestId('toast-link');
      await act(async () => {
        fireEvent.click(link);
      });
    });
    describe('clicks on the continue button on the cluster draft modal', () => {
      beforeEach(async () => {
        expect(screen.getByTestId('cluster-draft-modal')).toBeInTheDocument();
        // click continue button
        const continueWithDraftButton = screen.getByTestId('continue-button');
        await act(async () => {
          fireEvent.click(continueWithDraftButton);
        });
      });
      it('shows an invalid cluster name error', () => {
        expect(
          screen.getByText(/Cluster names can only contain ASCII letters, numbers, and hyphens./)
        ).toBeInTheDocument();
      });

      it('disables the create button', () => {
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        expect(createButton).toBeAriaDisabled();
      });
    });
  });

  describe('when cluster starter is loaded with an M0 cluster draft', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      const clusterDescription: ClusterDescription = clusterFixtures.getTypedFreeAwsDefaultClusterDescription_asJSON(
        'free cluster draft',
        'M0'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M0',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: [
            { key: 'cluster tag 1', value: 'draft tag 1', visibility: TagVisibility.PUBLIC },
            { key: 'cluster tag 2', value: 'draft tag 2', visibility: TagVisibility.PUBLIC },
          ],
          automateSecurity: false,
          loadSampleDataset: false,
        },
        draftId: '',
        lastUpdatedTime: '',
        isFromCst: true,
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });

      expect(screen.getByTestId('draft-new-notification-toast')).toBeInTheDocument();
      const link = screen.getByTestId('toast-link');
      await act(async () => {
        fireEvent.click(link);
      });
    });
    describe('clicks on the continue button on the cluster draft modal', () => {
      beforeEach(async () => {
        expect(screen.getByTestId('cluster-draft-modal')).toBeInTheDocument();
        // click continue button
        const continueWithDraftButton = screen.getByTestId('continue-button');
        await act(async () => {
          fireEvent.click(continueWithDraftButton);
        });
      });

      it('renders N.Virginia as the region from the draft', () => {
        const regionSelector = document.getElementsByClassName('Select-control')[0];
        const region = regionSelector.querySelector('strong');
        expect(region?.innerHTML).toBe('N. Virginia');
      });

      it('renders the cluster name selector', () => {
        const selector = screen.getByTestId('cluster-starter-template-name-selector');
        expect(selector.querySelector('input')?.value).toBe('free cluster draft');
      });

      it('AWS is selected as the provider from the draft', () => {
        const selector = screen.getByTestId('cloud-provider-selector');
        const options = selector.querySelectorAll('input');
        const checkedOption = Array.from(options).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(CloudProvider.AWS);
      });

      it('shows the m0 is selected', () => {
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        const checkedOption = Array.from(options).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M0);
      });

      it('populates the tag row input with the existing tags from the cluster draft', () => {
        // only the first tag gets populated
        expect(screen.getByDisplayValue('cluster tag 1')).toBeInTheDocument();
        expect(screen.getByDisplayValue('draft tag 1')).toBeInTheDocument();
      });

      it('unchecks the automate security checkbox', () => {
        expect(screen.getByTestId('automate-security-setup-checkbox')).not.toBeChecked();
      });

      it('unchecks the load sample dataset checkbox', () => {
        expect(screen.getByTestId('preload-sample-dataset-checkbox')).not.toBeChecked();
      });
    });
  });

  describe('when cluster starter is loaded with a M30 cluster draft from the cluster builder', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      const clusterDescription: ClusterDescription = clusterFixtures.getAwsDefaultClusterDescription_asJSON(
        'M30_cluster_draft',
        'M30'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M30',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: undefined,
        },
        draftId: '',
        lastUpdatedTime: '',
        isFromCst: false,
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                IS_GROUP_CLUSTER_MANAGER: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
      // display the cluster draft notification toast
      expect(screen.getByTestId('draft-new-notification-toast')).toBeInTheDocument();
      const link = screen.getByTestId('toast-link');
      await act(async () => {
        fireEvent.click(link);
      });
    });
    describe('clicks on the continue button on the cluster draft modal', () => {
      beforeEach(async () => {
        expect(screen.getByTestId('cluster-draft-modal')).toBeInTheDocument();
        // click continue button
        const continueWithDraftButton = screen.getByTestId('continue-button');
        await act(async () => {
          fireEvent.click(continueWithDraftButton);
        });
      });

      it('renders N.Virginia as the region from the draft', () => {
        const regionSelector = document.getElementsByClassName('Select-control')[0];
        const region = regionSelector.querySelector('strong');
        expect(region?.innerHTML).toBe('N. Virginia');
      });

      it('renders the cluster name selector', () => {
        const selector = screen.getByTestId('cluster-starter-template-name-selector');
        expect(selector.querySelector('input')?.value).toBe('M30_cluster_draft');
      });

      it('AWS is selected as the provider from the draft', () => {
        const selector = screen.getByTestId('cloud-provider-selector');
        const options = selector.querySelectorAll('input');
        const checkedOption = Array.from(options).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(CloudProvider.AWS);
      });

      it('shows the m10 is selected', () => {
        // M30 gets downgraded to M10
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        const checkedOption = Array.from(options).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M10);
      });

      it('no tags are shown since the draft does not have any', () => {
        // no tags shown
        expect(screen.queryByDisplayValue('cluster tag 1')).not.toBeInTheDocument();
      });

      it('does not show the failed to load toast', () => {
        expect(screen.queryByTestId('draft-failed-to-load-toast')).not.toBeInTheDocument();
      });
    });
  });

  describe('when cluster starter is loaded with a M30 cluster draft from the CST', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      const clusterDescription: ClusterDescription = clusterFixtures.getAwsDefaultClusterDescription_asJSON(
        'M30_cluster_draft',
        'M30'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M30',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: undefined,
        },
        draftId: '',
        lastUpdatedTime: '',
        isFromCst: true,
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                IS_GROUP_CLUSTER_MANAGER: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });
    it('does not show the cluster drafts notification toast', () => {
      expect(screen.queryByTestId('draft-new-notification-toast')).not.toBeInTheDocument();
    });

    it('does show the failed to load toast', () => {
      expect(screen.getByTestId('draft-failed-to-load-toast')).toBeInTheDocument();
    });
  });

  describe('when cluster starter is loaded with a M0 cluster draft', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      const clusterDescription: ClusterDescription = clusterFixtures.getTypedFreeAwsDefaultClusterDescription_asJSON(
        'free_cluster_draft',
        'M0'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M0',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: [
            { key: 'cluster tag 1', value: 'draft tag 1', visibility: TagVisibility.PUBLIC },
            { key: 'cluster tag 2', value: 'draft tag 2', visibility: TagVisibility.PUBLIC },
          ],
          automateSecurity: false,
          loadSampleDataset: false,
        },
        draftId: '',
        lastUpdatedTime: '',
        isFromCst: true,
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                IS_GROUP_CLUSTER_MANAGER: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });

      // show the cluster draft notification toast
      expect(screen.getByTestId('draft-new-notification-toast')).toBeInTheDocument();
      const link = screen.getByTestId('toast-link');
      await act(async () => {
        fireEvent.click(link);
      });
    });

    describe('clicks on the continue button on the cluster draft modal', () => {
      beforeEach(async () => {
        expect(screen.getByTestId('cluster-draft-modal')).toBeInTheDocument();
        // click continue button
        const continueWithDraftButton = screen.getByTestId('continue-button');
        await act(async () => {
          fireEvent.click(continueWithDraftButton);
        });
      });

      it('renders N.Virginia as the region from the draft', () => {
        const regionSelector = document.getElementsByClassName('Select-control')[0];
        const region = regionSelector.querySelector('strong');
        expect(region?.innerHTML).toBe('N. Virginia');
      });

      it('renders the cluster name selector', () => {
        const selector = screen.getByTestId('cluster-starter-template-name-selector');
        expect(selector.querySelector('input')?.value).toBe('free_cluster_draft');
      });

      it('AWS is selected as the provider from the draft', () => {
        const selector = screen.getByTestId('cloud-provider-selector');
        const options = selector.querySelectorAll('input');
        const checkedOption = Array.from(options).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(CloudProvider.AWS);
      });

      it('shows the m0 is selected', () => {
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        const checkedOption = Array.from(options).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M0);
      });

      it('populates the tag row input with the existing tags from the cluster draft', () => {
        // only the first tag gets populated
        expect(screen.getByDisplayValue('cluster tag 1')).toBeInTheDocument();
        expect(screen.getByDisplayValue('draft tag 1')).toBeInTheDocument();
      });

      it('unchecks the automate security checkbox', () => {
        expect(screen.getByTestId('automate-security-setup-checkbox')).not.toBeChecked();
      });

      it('unchecks the load sample dataset checkbox', () => {
        expect(screen.getByTestId('preload-sample-dataset-checkbox')).not.toBeChecked();
      });
    });
  });

  describe('when cluster starter fails to load a cluster draft', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      getRelevantClusterDraftMock.mockRejectedValue(null);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                IS_GROUP_CLUSTER_MANAGER: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });
    it('shows the m10 is selected', () => {
      // default
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      const checkedOption = Array.from(options).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M10);
    });
    it('shows the failed to load toast', () => {
      expect(screen.getByTestId('draft-failed-to-load-toast')).toBeInTheDocument();
    });
  });

  describe('when the user clicked "Back" from the cluster builder and has a cluster draft', () => {
    const clusterName = 'Cluster0';
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      fakeLocation.search = `from=${FromLocation.CLUSTER_BUILDER}`;
      getCurrentRouteQueryStringMock.mockReturnValue(`from=${FromLocation.CLUSTER_BUILDER}`);
      const clusterDescription: ClusterDescription = clusterFixtures.getAwsDefaultClusterDescription_asJSON(
        clusterName,
        'M10'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M10',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: [{ key: 'cluster tag', value: 'draft tag', visibility: TagVisibility.PUBLIC }],
          loadSampleDataset: true,
        },
        draftId: '',
        isFromCst: true,
        lastUpdatedTime: now.toISOString(),
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('deletes the existing cluster draft', () => {
      expect(deleteClusterDraftOrSendErrorMock).toHaveBeenCalledWith({
        groupId: mockGroupId,
        clusterName,
        isEdit: false,
      });
    });

    it('does not show the cluster drafts notification toast', () => {
      expect(screen.queryByTestId('draft-new-notification-toast')).toBeNull();
    });
  });

  describe('when cluster starter templates page does not load a cluster draft when from=cartEmail', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      fakeLocation.search = `?from=${FromLocation.CART_EMAIL}`;
      getCurrentRouteQueryStringMock.mockReturnValue(`from=${FromLocation.CART_EMAIL}`);
      getRelevantClusterDraftMock.mockResolvedValue(null);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });
    it('shows the failed to load toast and sends a not found event', () => {
      expect(screen.getByTestId('draft-failed-to-load-toast')).toBeInTheDocument();
      expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.CLUSTER_DRAFT_NOT_FOUND, {
        context: 'Cluster Starter Templates',
      });
    });
  });

  describe('Auto Save is working', () => {
    const mockClusterConfiguration = {
      clusterDescription: {
        acceptDataRisksAndForceReplicaSetReconfig: null,
        backupEnabled: false,
        biConnector: { enabled: false, hostname: null, readPreference: 'secondary' },
        clusterTags: [],
        clusterType: 'REPLICASET',
        collectionLimit: 500,
        createDate: null,
        databaseLimit: 100,
        deleteAfterDate: null,
        deploymentItemName: 'Cluster0-shard-0',
        diskBackupEnabled: false,
        diskSizeGB: 0.5,
        encryptionAtRestProvider: 'NONE',
        geoSharding: { customZoneMapping: {}, managedNamespaces: [], selfManagedSharding: false },
        groupId: '000000000000000000000001',
        isFastProvisioned: false,
        isPaused: false,
        maxIncomingConns: 100,
        mongoDBMajorVersion: '3.4',
        mongoDBUriHosts: [
          'foo-shard-00-00-q8x1v.mmscloudtest.com:27000',
          'foo-shard-00-01-q8x1v.mmscloudtest.com:27000',
          'foo-shard-00-02-q8x1v.mmscloudtest.com:27000',
        ],
        mongoDBUriHostsLastUpdateDate: '2017-05-08T19:23:59Z',
        mongoDBVersion: '3.4.4',
        name: 'Cluster0',
        operationsPerSecondLimit: 100,
        pitEnabled: false,
        privateLinkSrvAddresses: {},
        replicationSpecList: [
          {
            id: '000000000000000000000001',
            numShards: 1,
            regionConfigs: [
              {
                analyticsAutoScaling: null,
                analyticsSpecs: { backingProvider: 'AWS', instanceSize: 'M0', nodeCount: 0 },
                autoScaling: {
                  autoIndex: { enabled: false },
                  compute: {
                    enabled: false,
                    maxInstanceSize: null,
                    minInstanceSize: null,
                    scaleDownEnabled: false,
                    predictiveEnabled: false,
                  },
                  diskGB: { enabled: false },
                },
                cloudProvider: 'FREE',
                electableSpecs: { backingProvider: 'AWS', instanceSize: 'M0', nodeCount: 3 },
                priority: 7,
                readOnlySpecs: { backingProvider: 'AWS', instanceSize: 'M0', nodeCount: 0 },
                regionName: 'US_EAST_1',
                regionView: {
                  continent: 'North America',
                  isRecommended: true,
                  key: 'US_EAST_1',
                  location: 'N. Virginia',
                  provider: 'AWS',
                },
              },
            ],
            zoneName: 'Zone 1',
          },
        ],
        rootCertType: 'DST',
        srvAddress: 'connectionString.com',
        state: 'IDLE',
        terminationProtectionEnabled: false,
        uniqueId: '000000000000000000000002',
      },
      processArgs: {},
      tags: [],
    };

    beforeEach(async () => {
      jest.useFakeTimers();
      getDefaultProcessArgsMock.mockResolvedValue({});
      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT', 'SERVERLESS_SHARED_UI_OPTION_ENABLED'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                IS_GROUP_CLUSTER_MANAGER: true,
                USE_NEW_CLUSTER_DRAFTS_API: true,
              })
            }
          />
        );
      });
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('no draft is saved when no changes are made', async () => {
      expect(debouncedSaveClusterDraftMock).not.toBeCalled();
    });

    it('and changes the provider, the draft is saved', async () => {
      const providerSelector = screen.getByTestId('cloud-provider-selector');
      const options = providerSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[1]);
      });
      const updatedOptions = providerSelector.querySelectorAll('input');
      const checkedOption = Array.from(updatedOptions).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(CloudProvider.GCP);

      expect(debouncedSaveClusterDraftMock).toHaveBeenCalledTimes(2);

      expect(debouncedSaveClusterDraftMock).toHaveBeenLastCalledWith({
        clusterConfiguration: {
          clusterDescription: {
            acceptDataRisksAndForceReplicaSetReconfig: null,
            backupEnabled: true,
            biConnector: { enabled: false, hostname: null, readPreference: 'secondary' },
            clusterTags: [],
            clusterType: 'REPLICASET',
            createDate: '2019-05-20T17:43:03Z',
            deleteAfterDate: null,
            deploymentItemName: 'Cluster0-shard-0',
            diskBackupEnabled: false,
            diskSizeGB: 40,
            encryptionAtRestProvider: 'NONE',
            geoSharding: { customZoneMapping: {}, managedNamespaces: [], selfManagedSharding: false },
            groupId: '000000000000000000000001',
            hostnameSubdomainLevel: 'MONGODB',
            isCrossCloudCluster: false,
            isFastProvisioned: false,
            isPaused: false,
            maxIncomingConns: null,
            mongoDBMajorVersion: '3.4',
            mongoDBUriHosts: [
              'foo-shard-00-00-q8x1v.mmscloudtest.com:27000',
              'foo-shard-00-01-q8x1v.mmscloudtest.com:27000',
              'foo-shard-00-02-q8x1v.mmscloudtest.com:27000',
            ],
            mongoDBUriHostsLastUpdateDate: '2017-05-08T19:23:59Z',
            mongoDBVersion: '3.4.4',
            name: 'Cluster0',
            pitEnabled: false,
            privateLinkSrvAddresses: {},
            replicationSpecList: [
              {
                id: '000000000000000000000001',
                numShards: 1,
                regionConfigs: [
                  {
                    analyticsAutoScaling: null,
                    analyticsSpecs: { instanceSize: 'M10', nodeCount: 0 },
                    autoScaling: {
                      autoIndex: { enabled: false },
                      compute: {
                        enabled: false,
                        maxInstanceSize: null,
                        minInstanceSize: null,
                        scaleDownEnabled: false,
                        predictiveEnabled: false,
                      },
                      diskGB: { enabled: true },
                    },
                    cloudProvider: 'GCP',
                    electableSpecs: { instanceSize: 'M10', nodeCount: 3 },
                    priority: 7,
                    readOnlySpecs: { instanceSize: 'M10', nodeCount: 0 },
                    regionName: 'CENTRAL_US',
                    regionView: {
                      complianceLevel: 'COMMERCIAL',
                      continent: 'Americas',
                      isRecommended: true,
                      key: 'CENTRAL_US',
                      location: 'Iowa',
                      name: 'us-central1',
                      provider: 'GCP',
                    },
                  },
                ],
                zoneName: 'Zone 1',
              },
            ],
            rootCertType: 'DST',
            srvAddress: 'connectionString.com',
            state: 'IDLE',
            terminationProtectionEnabled: false,
            uniqueId: '000000000000000000000002',
          },
          processArgs: {},
          tags: [],
        },
        desiredInstanceSize: 'M10',
        groupId: 'groupId',
        hourlyEstimate: '$0.20/hour',
        isAutoSave: true,
        isEdit: false,
        isFromCst: true,
      });
    });

    it('and changes the region, the draft is saved', async () => {
      const regionDropdown = renderResult.container.querySelector('.Select-control');
      regionDropdown && fireEvent.keyDown(regionDropdown, { key: 'Down', keyCode: 40 });
      await act(async () => {
        fireEvent.mouseDown(
          screen.getByRole('option', {
            name: /Frankfurt/i,
          })
        );
      });

      expect(debouncedSaveClusterDraftMock).toHaveBeenCalledTimes(2);
    });

    it('saves the draft if the desired instance size is M0', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[2]);
      });
      const checkedOption = Array.from(options).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M0);

      expect(debouncedSaveClusterDraftMock).toHaveBeenLastCalledWith({
        clusterConfiguration: mockClusterConfiguration,
        desiredInstanceSize: 'M0',
        groupId: 'groupId',
        hourlyEstimate: 'Free',
        isAutoSave: true,
        isEdit: false,
        isFromCst: true,
      });
    });

    it('does not save the draft if the desired instance size is Serverless', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[1]);
      });
      const checkedOption = Array.from(options).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.SERVERLESS);

      expect(debouncedSaveClusterDraftMock).not.toBeCalled();
    });

    it('saves the draft with checkbox states if they were changed', async () => {
      // select M0 so that both checkboxes are visible
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      fireEvent.click(options[2]);

      await act(() => Promise.resolve());

      // uncheck both boxes
      fireEvent.click(screen.getByText('Automate security setup'));
      fireEvent.click(screen.getByText('Preload sample dataset'));

      await act(() => Promise.resolve());

      expect(debouncedSaveClusterDraftMock).toHaveBeenLastCalledWith<Parameters<typeof debouncedSaveClusterDraftMock>>({
        clusterConfiguration: expect.objectContaining({
          automateSecurity: false,
          loadSampleDataset: false,
        }),
        desiredInstanceSize: 'M0',
        groupId: 'groupId',
        hourlyEstimate: 'Free',
        isAutoSave: true,
        isEdit: false,
        isFromCst: true,
      });

      // Re-check one
      fireEvent.click(screen.getByText('Preload sample dataset'));

      await act(() => Promise.resolve());

      expect(debouncedSaveClusterDraftMock).toHaveBeenLastCalledWith<Parameters<typeof debouncedSaveClusterDraftMock>>({
        clusterConfiguration: expect.objectContaining({
          automateSecurity: false,
          loadSampleDataset: true,
        }),
        desiredInstanceSize: 'M0',
        groupId: 'groupId',
        hourlyEstimate: 'Free',
        isAutoSave: true,
        isEdit: false,
        isFromCst: true,
      });
    });

    it('saves the draft if the desired instance size is Flex', async () => {
      // Mock existing clusters
      getClusterDescriptionsAsListMock.mockReturnValue([
        clusterFixtures.getTypedFreeAwsDefaultClusterDescription_asJSON(),
      ]);
      // eslint-disable-next-line testing-library/no-unnecessary-act
      await act(async () => {
        // Unmount and remount because rerender does not work here.
        renderResult.unmount();
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                IS_GROUP_CLUSTER_MANAGER: true,
                NDS_FLEX_FEATURE_ENABLED: true,
              })
            }
          />
        );
      });
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      fireEvent.click(options[2]);

      const checkedOption = Array.from(options).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.FLEX);

      expect(debouncedSaveClusterDraftMock).toHaveBeenCalled();
    });
  });

  describe('Auto Save is not enabled when user doesnt have permission', () => {
    beforeEach(async () => {
      jest.useFakeTimers();
      getDefaultProcessArgsMock.mockResolvedValue({});
      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                IS_GROUP_CLUSTER_MANAGER: false,
              })
            }
          />
        );
      });
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('and changes the provider, no draft is saved', async () => {
      const providerSelector = screen.getByTestId('cloud-provider-selector');
      const options = providerSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[1]);
      });
      const updatedOptions = providerSelector.querySelectorAll('input');
      const checkedOption = Array.from(updatedOptions).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(CloudProvider.GCP);

      expect(debouncedSaveClusterDraftMock).not.toBeCalled();
    });
  });

  describe('when user has existing clusters', () => {
    beforeEach(async () => {
      jest.spyOn(api.allClusters, 'getGroups').mockReturnValue(
        Promise.resolve([
          {
            clusters: [
              {
                alertCount: 0,
                authEnabled: true,
                availability: 'unavailable',
                backupEnabled: true,
                clusterId: '000000000000000000000000',
                dataSizeBytes: 0,
                name: 'Cluster0',
                nodeCount: 3,
                sslEnabled: true,
                type: 'replica set',
                versions: ['7.0.9'],
              },
            ],
            groupId: '000000000000000000000000',
            groupName: 'mockGroup',
            orgId: '000000000000000000000000',
            orgName: 'mockOrg',
            planType: 'Atlas',
            tags: [],
          },
        ])
      );

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                IS_GROUP_CLUSTER_MANAGER: true,
              })
            }
          />
        );
      });
    });

    it('when M0 is selected, does not check the load sample data checkbox', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[2]);
      });
      const loadSampleDatasetCheckbox = screen.getByTestId('preload-sample-dataset-checkbox');
      expect(loadSampleDatasetCheckbox).toBeInTheDocument();
      expect(loadSampleDatasetCheckbox).not.toBeChecked();
    });
  });

  describe('when user is creating a cluster with a payment method', () => {
    beforeEach(async () => {
      hasPaymentMock.mockResolvedValue(true);
      getPaymentMethodsMock.mockResolvedValue([paymentMethod]);

      await act(async () => {
        renderResult = render(
          <Provider
            store={createMainReduxStore({
              group: {
                [mockGroupId]: {
                  data: {
                    isProjectOverviewEnabled: true,
                  },
                },
              },
              organization: {
                [mockOrgId]: {
                  billing: {
                    paymentMethods: [paymentMethod],
                  },
                },
              },
            })}
          >
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT', 'SERVERLESS_SHARED_UI_OPTION_ENABLED'],
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                  USER_COUNTRY_CODE: 'US',
                })
              }
            />
          </Provider>
        );
      });
    });

    describe('creating an M10', () => {
      beforeEach(async () => {
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        await act(async () => {
          fireEvent.click(createButton);
        });
      });

      it('calls the create template cluster', () => {
        expect(saveTemplateClusterMock).toHaveBeenCalledWith({
          groupId: mockGroupId,
          templateKey: TemplateKey.REPLICA_SET_M10,
          templateParams: {
            clusterName: 'Cluster0',
            automateSecurity: false,
            loadSampleDataset: false,
            tags: [],
            cloudProvider: CloudProvider.AWS,
            regionKey: 'US_EAST_1',
            isAutoScalingEnabled: true,
            isInCreateSampleSearchIndexVariant: false,
          },
          recaptchaToken: undefined,
          analyticsTrackProperties: {
            context: 'Cluster Starter Templates',
            current_active_payment_method_type: 'CREDIT_CARD',
            hourly_cost: '$0.10/hour',
            user_country_code: 'US',
          },
          isFirstCluster: true,
          toggleGroupEditableFeatureFlag: expect.anything(),
          projectHasDedicatedClusters: false,
        });
      });
      it('redirects users to the security quickstart with proper query params', () => {
        expect(navigateToMock).toHaveBeenCalledWith(
          Routes.QuickAccessSetup({
            includeToast: true,
          })
        );
      });
    });
    describe('creating a serverless instance', () => {
      beforeEach(async () => {
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        // select serverless
        await act(async () => {
          fireEvent.click(options[1]);
        });
        // click create
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        await act(async () => {
          fireEvent.click(createButton);
        });
      });

      it('calls the create template cluster', () => {
        expect(saveTemplateClusterMock).toHaveBeenCalledWith({
          groupId: mockGroupId,
          templateKey: TemplateKey.SERVERLESS,
          templateParams: {
            clusterName: 'ServerlessInstance0',
            automateSecurity: true,
            loadSampleDataset: false,
            tags: [],
            cloudProvider: CloudProvider.AWS,
            regionKey: 'US_EAST_1',
            isAutoScalingEnabled: true,
            isInCreateSampleSearchIndexVariant: false,
          },
          recaptchaToken: undefined,
          analyticsTrackProperties: {
            context: 'Cluster Starter Templates',
            current_active_payment_method_type: 'CREDIT_CARD',
            hourly_cost: '$0.10/1M reads',
            user_country_code: 'US',
          },
          isFirstCluster: true,
          toggleGroupEditableFeatureFlag: expect.anything(),
          projectHasDedicatedClusters: false,
        });
      });
      it('redirects users to the home center with proper query params', () => {
        expect(navigateToMock).toHaveBeenCalledWith(
          Routes.HomeCenter({
            automateSecurity: true,
            connectCluster: 'ServerlessInstance0',
          })
        );
      });
    });
    describe('creating an M0', () => {
      beforeEach(async () => {
        const getActiveOrgActivePaymentMethodMock = jest.spyOn(app, 'getActiveOrgActivePaymentMethod');
        getActiveOrgActivePaymentMethodMock.mockReturnValue(paymentMethod);
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        // select M0
        await act(async () => {
          fireEvent.click(options[2]);
        });
        // click create
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        await act(async () => {
          fireEvent.click(createButton);
        });
      });

      it('calls the create template cluster', () => {
        expect(saveTemplateClusterMock).toHaveBeenCalledWith({
          groupId: mockGroupId,
          templateKey: TemplateKey.M0,
          templateParams: {
            clusterName: 'Cluster0',
            automateSecurity: true,
            loadSampleDataset: true,
            tags: [],
            cloudProvider: CloudProvider.AWS,
            regionKey: 'US_EAST_1',
            isAutoScalingEnabled: true,
            isInCreateSampleSearchIndexVariant: false,
          },
          recaptchaToken: undefined,
          analyticsTrackProperties: {
            context: 'Cluster Starter Templates',
            current_active_payment_method_type: 'CREDIT_CARD',
            hourly_cost: 'Free',
            user_country_code: 'US',
          },
          isFirstCluster: true,
          toggleGroupEditableFeatureFlag: expect.anything(),
          projectHasDedicatedClusters: false,
        });
      });
      it('redirects users to the home center with proper query params', () => {
        expect(navigateToMock).toHaveBeenCalledWith(
          Routes.HomeCenter({
            automateSecurity: true,
            connectCluster: 'Cluster0',
          })
        );
      });
    });

    describe('making changes and creating an M10', () => {
      beforeEach(async () => {
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        const tagKeyInput = screen.getByPlaceholderText('Select or enter key');
        const tagValueInput = screen.getByPlaceholderText('Select or enter value');
        const loadSampleDatasetCheckbox = screen.getByTestId('preload-sample-dataset-checkbox');

        // Fill out tag key and value
        act(() => {
          fireEvent.change(tagKeyInput, { target: { value: 'key' } });
        });

        act(() => {
          fireEvent.change(tagValueInput, { target: { value: 'value' } });
        });

        act(() => {
          fireEvent.click(loadSampleDatasetCheckbox);
        });
        await act(async () => {
          fireEvent.click(createButton);
        });
      });

      it('calls the create template cluster with tags', () => {
        expect(saveTemplateClusterMock).toHaveBeenCalledWith({
          groupId: mockGroupId,
          templateKey: TemplateKey.REPLICA_SET_M10,
          templateParams: {
            clusterName: 'Cluster0',
            automateSecurity: false,
            loadSampleDataset: true,
            tags: [{ key: 'key', value: 'value', visibility: TagVisibility.PUBLIC }],
            cloudProvider: CloudProvider.AWS,
            regionKey: 'US_EAST_1',
            isAutoScalingEnabled: true,
            isInCreateSampleSearchIndexVariant: false,
          },
          recaptchaToken: undefined,
          analyticsTrackProperties: {
            context: 'Cluster Starter Templates',
            current_active_payment_method_type: 'CREDIT_CARD',
            hourly_cost: '$0.10/hour',
            user_country_code: 'US',
          },
          isFirstCluster: true,
          toggleGroupEditableFeatureFlag: expect.anything(),
          projectHasDedicatedClusters: false,
        });
      });
      it('redirects users to the security quickstart with proper query params', () => {
        expect(navigateToMock).toHaveBeenCalledWith(
          Routes.QuickAccessSetup({
            includeToast: true,
          })
        );
      });
    });
  });

  describe('when user is creating a cluster without a payment method', () => {
    beforeEach(async () => {
      hasPaymentMock.mockResolvedValue(false);
      getPaymentMethodsMock.mockResolvedValue([]);
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;

      await act(async () => {
        renderResult = render(
          <Provider
            store={createMainReduxStore({
              group: {
                [mockGroupId]: {
                  data: {
                    isProjectOverviewEnabled: true,
                  },
                },
              },
              organization: {
                [mockOrgId]: {
                  billing: {
                    paymentMethods: [],
                  },
                },
              },
            })}
          >
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT', 'SERVERLESS_SHARED_UI_OPTION_ENABLED'],
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                  USER_COUNTRY_CODE: 'US',
                })
              }
              windowLocation={fakeLocation}
            />
          </Provider>
        );
      });
    });

    describe('creating an M10', () => {
      beforeEach(async () => {
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        await act(async () => {
          fireEvent.click(createButton);
        });
      });

      it('redirects to the payment page for a template M10', () => {
        expect(saveTemplateClusterMock).not.toHaveBeenCalled();
        expect(assignMock).toHaveBeenCalledTimes(1);
        const navigatedRoute = assignMock.mock.calls[0][0];
        const queryStr = navigatedRoute.slice(navigatedRoute.indexOf('?'));
        const query = new URLSearchParams(queryStr);

        expect(Object.fromEntries(query.entries())).toEqual({
          type: PurchaseType.Cluster,
          groupId: mockGroupId,
          isEdit: 'false',
          clusterName: 'Cluster0',
          templateKey: TemplateKey.REPLICA_SET_M10,
          cloudProvider: BackingCloudProvider.AWS,
          regionKey: RegionNames.US_EAST_1,
          loadSampleDataset: 'false',
          automateSecurity: 'false',
          tags: JSON.stringify([]),
          fromStarterTemplates: 'true',
        });
      });
    });
    describe('creating a serverless instance', () => {
      beforeEach(async () => {
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        // select serverless
        await act(async () => {
          fireEvent.click(options[1]);
        });
        // click create
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        await act(async () => {
          fireEvent.click(createButton);
        });
      });

      it('redirects to the payment page for a template serverless instance', () => {
        expect(saveTemplateClusterMock).not.toHaveBeenCalled();
        expect(assignMock).toHaveBeenCalledTimes(1);
        const navigatedRoute = assignMock.mock.calls[0][0];
        const queryStr = navigatedRoute.slice(navigatedRoute.indexOf('?'));
        const query = new URLSearchParams(queryStr);

        expect(Object.fromEntries(query.entries())).toEqual({
          type: PurchaseType.Cluster,
          groupId: mockGroupId,
          isEdit: 'false',
          clusterName: 'ServerlessInstance0',
          templateKey: TemplateKey.SERVERLESS,
          cloudProvider: BackingCloudProvider.AWS,
          regionKey: RegionNames.US_EAST_1,
          loadSampleDataset: 'false',
          automateSecurity: 'true',
          tags: JSON.stringify([]),
          fromStarterTemplates: 'true',
        });
      });
    });
    describe('creating an M0', () => {
      beforeEach(async () => {
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        // select M0
        await act(async () => {
          fireEvent.click(options[2]);
        });
        // click create
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        await act(async () => {
          fireEvent.click(createButton);
        });
      });

      it('calls the create template cluster', () => {
        expect(saveTemplateClusterMock).toHaveBeenCalledWith({
          groupId: mockGroupId,
          templateKey: TemplateKey.M0,
          templateParams: {
            clusterName: 'Cluster0',
            automateSecurity: true,
            loadSampleDataset: true,
            tags: [],
            cloudProvider: CloudProvider.AWS,
            regionKey: 'US_EAST_1',
            isAutoScalingEnabled: true,
            isInCreateSampleSearchIndexVariant: false,
          },
          recaptchaToken: undefined,
          analyticsTrackProperties: {
            context: 'Cluster Starter Templates',
            current_active_payment_method_type: 'CREDIT_CARD',
            hourly_cost: 'Free',
            user_country_code: 'US',
          },
          isFirstCluster: true,
          toggleGroupEditableFeatureFlag: expect.anything(),

          projectHasDedicatedClusters: false,
        });
      });
      it('redirects users to the home center with proper query params', () => {
        expect(navigateToMock).toHaveBeenCalledWith(
          Routes.HomeCenter({
            automateSecurity: true,
            connectCluster: 'Cluster0',
          })
        );
      });
    });

    describe('making changes and creating an M10', () => {
      beforeEach(async () => {
        const createButton = screen.getByTestId('lg-form_footer-primary_button');
        const tagKeyInput = screen.getByPlaceholderText('Select or enter key');
        const tagValueInput = screen.getByPlaceholderText('Select or enter value');
        const loadSampleDatasetCheckbox = screen.getByTestId('preload-sample-dataset-checkbox');

        // Fill out tag key and value
        act(() => {
          fireEvent.change(tagKeyInput, { target: { value: 'key' } });
        });

        act(() => {
          fireEvent.change(tagValueInput, { target: { value: 'value' } });
        });

        act(() => {
          fireEvent.click(loadSampleDatasetCheckbox);
        });
        await act(async () => {
          fireEvent.click(createButton);
        });
      });

      it('redirects to the payment page for a template M10 with tags', () => {
        expect(saveTemplateClusterMock).not.toHaveBeenCalled();
        expect(assignMock).toHaveBeenCalledTimes(1);
        const navigatedRoute = assignMock.mock.calls[0][0];
        const queryStr = navigatedRoute.slice(navigatedRoute.indexOf('?'));
        const query = new URLSearchParams(queryStr);

        expect(Object.fromEntries(query.entries())).toEqual({
          type: PurchaseType.Cluster,
          groupId: mockGroupId,
          isEdit: 'false',
          clusterName: 'Cluster0',
          templateKey: TemplateKey.REPLICA_SET_M10,
          cloudProvider: BackingCloudProvider.AWS,
          regionKey: RegionNames.US_EAST_1,
          loadSampleDataset: 'true',
          automateSecurity: 'false',
          tags: expect.anything(),
          fromStarterTemplates: 'true',
        });

        // Check tags separately so that the ordering of the object doesn't matter for the test, just the contents
        expect(JSON.parse(query.get('tags')!)).toEqual([
          { key: 'key', value: 'value', visibility: TagVisibility.PUBLIC },
        ]);
      });
    });
  });

  describe('when FGA is enabled', () => {
    const permission = Permission.CLOUD_PROJECT_CLUSTERS_CREATE(mockGroupId);

    it(`should have proper FGA messaging for ${permission.action}`, async function (this: $TSFixMe) {
      getTypedMockOf(authzApi.fetchIsAuthorized).mockResolvedValue({ authorizationResults: [] });

      await validateAuthzMessaging({
        permission: permission,
        component: (
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                USER_COUNTRY_CODE: 'US',
              })
            }
            windowLocation={fakeLocation}
          />
        ),
      });
    });
  });

  describe('Display Flex on CST', () => {
    beforeEach(async () => {
      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: false,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                USER_COUNTRY_CODE: 'US',
                IS_GROUP_CLUSTER_MANAGER: true,
                NDS_FLEX_FEATURE_ENABLED: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('renders the m10 template option', async () => {
      expect(screen.getByTestId('template-cards-m10')).toBeInTheDocument();
    });

    it('renders the flex template option', async () => {
      expect(screen.getByTestId('template-cards-flex')).toBeInTheDocument();
    });

    it('renders the m0 template option', async () => {
      expect(screen.getByTestId('template-cards-m0')).toBeInTheDocument();
    });

    it('shows Free instead of M0', async () => {
      expect(screen.getByTestId('template-cards-m0').textContent).toContain('Free');
    });
  });

  describe('Display Flex on CST Experiment and the project has an M0', function () {
    beforeEach(async () => {
      const clusterDescription = clusterFixtures.getAwsDefaultClusterDescription_asJSON('Cluster0', InstanceSizes.M0);

      // Existing cluster
      getClusterDescriptionsAsListMock.mockReturnValue([clusterDescription]);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: false,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                USER_COUNTRY_CODE: 'US',
                IS_GROUP_CLUSTER_MANAGER: true,
                NDS_FLEX_FEATURE_ENABLED: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('renders the m30 template option when the user is in the experiment', async () => {
      expect(screen.getByTestId('template-cards-m30')).toBeInTheDocument();
    });

    it('renders the m10 template option when the user is in the experiment', async () => {
      expect(screen.getByTestId('template-cards-m10')).toBeInTheDocument();
    });

    it('renders the flex template option when the user is in the experiment', async () => {
      expect(screen.getByTestId('template-cards-flex')).toBeInTheDocument();
    });
  });

  describe('When CST page is rendering with preload sample dataset checkbox', () => {
    beforeEach(async () => {
      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: false,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT', 'SERVERLESS_SHARED_UI_OPTION_ENABLED'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                USER_COUNTRY_CODE: 'US',
                IS_GROUP_CLUSTER_MANAGER: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('displays all 3 configuration category labels when M10 template is selected', () => {
      expect(screen.getByTestId('boc-configurations')).toBeInTheDocument();
      expect(screen.getByTestId('boc-included-features')).toBeInTheDocument();
      expect(screen.getByTestId('boc-quick-setup')).toBeInTheDocument();
    });

    it('displays all 3 configuration category labels when Serverless template is selected', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[1]);
      });

      expect(screen.getByTestId('boc-configurations')).toBeInTheDocument();
      expect(screen.getByTestId('boc-included-features')).toBeInTheDocument();
      expect(screen.getByTestId('boc-quick-setup')).toBeInTheDocument();
    });

    it('displays only 2 configuration category labels when M0 template is selected', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[2]);
      });

      expect(screen.getByTestId('boc-configurations')).toBeInTheDocument();
      expect(screen.getByTestId('boc-quick-setup')).toBeInTheDocument();
    });

    it('has a new warning callout that appears for loading sample data when Serverless template is selected', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[1]);
      });

      const loadSampleDatasetCheckbox = screen.getByTestId('preload-sample-dataset-checkbox');
      await act(async () => {
        fireEvent.click(loadSampleDatasetCheckbox);
      });

      expect(screen.getByTestId('boc-warning-callout')).toBeInTheDocument();
    });

    it('has the preload sample dataset checkbox checked by default on the M0 template', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[2]);
      });

      const loadSampleDatasetCheckbox = screen.getByTestId('preload-sample-dataset-checkbox');
      expect(loadSampleDatasetCheckbox).toBeInTheDocument();
      expect(loadSampleDatasetCheckbox).toBeChecked();
    });
  });

  describe('when CST is loaded while the project has a M0 cluster and a draft for a M0 cluster', () => {
    beforeEach(async () => {
      const clusterDescription = clusterFixtures.getAwsDefaultClusterDescription_asJSON('Cluster0', InstanceSizes.M0);

      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M0',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          automateSecurity: false,
          loadSampleDataset: false,
        },
        draftId: '',
        lastUpdatedTime: '',
        isFromCst: true,
      };
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      // Existing cluster
      getClusterDescriptionsAsListMock.mockReturnValue([clusterDescription]);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                NDS_FLEX_FEATURE_ENABLED: true,
              })
            }
          />
        );
      });
    });

    afterEach(() => {
      jest.clearAllMocks();
    });

    it('does not load the draft data', () => {
      // should show that M10 is selected
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      const checkedOption = Array.from(options).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M30);
    });
    it('shows the failed to load toast', () => {
      expect(screen.getByTestId('draft-failed-to-load-toast')).toBeInTheDocument();
    });
    it('sends a draft not valid event', () => {
      expect(trackMock).toHaveBeenCalledTimes(2);
      expect(trackMock).toHaveBeenNthCalledWith(2, SEGMENT_EVENTS.CLUSTER_DRAFT_NOT_VALID, {
        context: 'Cluster Starter Templates',
        reason: 'Invalid cluster draft with instance size M0',
      });
    });
    it('deletes the draft', () => {
      expect(deleteClusterDraftOrSendErrorMock).toHaveBeenCalledWith({
        groupId: mockGroupId,
        clusterName: 'Cluster0',
        isEdit: false,
      });
    });
  });

  describe('when CST is rendered and the project has a cluster', () => {
    beforeEach(async () => {
      // Existing cluster
      getClusterDescriptionsAsListMock.mockReturnValue([
        clusterFixtures.getAwsDefaultClusterDescription_asJSON('Cluster0', InstanceSizes.M10),
      ]);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={defaultSettingsModel}
          />
        );
      });
    });

    it('does not show the automate security checkbox when any of the template options are selected', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      for (let i = 0, len = options.length; i < len; i++) {
        await act(async () => {
          fireEvent.click(options[i]);
        });
        expect(screen.queryByTestId('automate-security-setup-checkbox')).not.toBeInTheDocument();
      }
    });
  });

  describe('When user is coming from WWW Pricing Page', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      getCurrentRouteQueryStringMock.mockReturnValue(
        'isFromPricingPage=true&clusterTier=M10&cloudProvider=GCP&cloudRegion=CENTRAL_US&clusterName=cluster1&isNewUser=true'
      );

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('should call clearQueryParams', () => {
      expect(clearQueryParamsMock).toHaveBeenCalled();
    });

    it('renders cloud provider from query param', () => {
      const selector = screen.getByTestId('cloud-provider-selector');
      const options = selector.querySelectorAll('input');
      const checkedOption = Array.from(options).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(CloudProvider.GCP);
    });
    it('renders cluster name from query param', () => {
      const selector = screen.getByTestId('cluster-starter-template-name-selector');
      expect(selector.querySelector('input')?.value).toBe('cluster1');
    });
    it('renders cluster tier from query param', () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      const checkedOption = Array.from(options).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M10);
    });
  });
  describe('When user is coming from WWW Pricing Page and set openFreeLimitModal query param to be true', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      getCurrentRouteQueryStringMock.mockReturnValue('isFromPricingPage=true&openFreeLimitModal=true');

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('renders freeClusterLimitReachedModal modal', () => {
      const freeClusterLimitReachedModal = screen.getByTestId('free-cluster-limit-reached-modal');
      expect(freeClusterLimitReachedModal).toBeInTheDocument();
    });
  });

  describe('When user is coming from WWW Pricing Page and invalidQueryParam is set to true', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      getCurrentRouteQueryStringMock.mockReturnValue('isFromPricingPage=true&invalidQueryParam=true');

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('renders invalid query param banner', () => {
      const invalidQueryParamBanner = screen.getByTestId('invalid-query-param-banner');
      expect(invalidQueryParamBanner).toBeInTheDocument();
    });

    it('should hide the banner, when user change the cluster configuration', async () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      await act(async () => {
        fireEvent.click(options[2]);
      });

      expect(screen.queryByTestId('invalid-query-param-banner')).not.toBeInTheDocument();
    });
  });

  describe('When user is coming from WWW Pricing Page and cluster tier is not available in CST page cluster cards', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      getCurrentRouteQueryStringMock.mockReturnValue(
        'isFromPricingPage=true&clusterTier=M2&cloudProvider=GCP&cloudRegion=CENTRAL_US&clusterName=cluster1&isNewUser=true'
      );

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
                NDS_FLEX_FEATURE_ENABLED: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('logs the error', () => {
      expect(sendErrorMock).toHaveBeenCalled();
      expect(sendErrorMock).toHaveBeenCalledWith({
        error: new Error('M2 is not available on CST cluster cards'),
        team: 'Atlas Growth',
        severity: 'error',
        extras: {
          clusterCards: [
            {
              description: 'Dedicated cluster for development environments and low-traffic applications.',
              title: 'M10',
              value: 'M10',
            },
            {
              description: 'For development and testing, with on-demand burst capacity for unpredictable traffic.',
              title: 'Flex',
              value: 'FLEX',
            },
            {
              description: 'For learning and exploring MongoDB in a cloud environment.',
              title: 'Free',
              value: 'M0',
            },
          ],
        },
      });
    });
  });

  describe('When user is coming from WWW Pricing Page and has a saved cluster draft', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;

      getCurrentRouteQueryStringMock.mockReturnValue(
        'isFromPricingPage=true&clusterTier=M10&cloudProvider=GCP&cloudRegion=CENTRAL_US&clusterName=cluster1&isNewUser=true'
      );

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('should not call getRelevantClusterDraftMock', () => {
      expect(getRelevantClusterDraftMock).not.toHaveBeenCalled();
    });
    it('should not show the cluster drafts notification toast', () => {
      expect(screen.queryByTestId('draft-new-notification-toast')).not.toBeInTheDocument();
    });
  });

  describe('When user has a saved Cluster Draft', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      const clusterDescription: ClusterDescription = clusterFixtures.getGcpDefaultClusterDescription_asJSON(
        'draft_cluster',
        'M10'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M10',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: [{ key: 'cluster tag', value: 'draft tag', visibility: TagVisibility.PUBLIC }],
          loadSampleDataset: true,
        },
        draftId: '',
        isFromCst: true,
        lastUpdatedTime: now.toISOString(),
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('should show cluster draft notification toast', () => {
      expect(screen.getByTestId('toast-title')).toBeInTheDocument();
      expect(screen.getByTestId('toast-title')).toHaveTextContent('You have a saved configuration.');
    });

    it('does not show the cluster drafts modal', () => {
      expect(screen.queryByTestId('cluster-draft-modal')).not.toBeInTheDocument();
    });

    it('should show the toast hyperlink to open the modal', () => {
      expect(screen.getByTestId('toast-link')).toBeInTheDocument();
    });
    it('should open the cluster draft modal when clicking on the hyperlink', async () => {
      const link = screen.getByTestId('toast-link');
      await act(async () => {
        fireEvent.click(link);
      });
      expect(screen.getByTestId('cluster-draft-modal')).toBeInTheDocument();
    });
  });

  describe('When user is in Copyable Cluster Configuration experiment', () => {
    beforeEach(async () => {
      getRelevantClusterDraftMock.mockReset();
      useExperimentAttributeMock.mockImplementation((expAttr, fallback) =>
        expAttr === ExperimentFeatureFlag.showCopyableClusterConfigurationModal ? true : fallback
      );
    });

    describe('and the project has no existing clusters', () => {
      beforeEach(async () => {
        await act(async () => {
          renderResult = render(
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                  ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                })
              }
              windowLocation={fakeLocation}
            />
          );
        });
      });

      it('shows the original Button and not the experimental SplitButton', () => {
        const goToAdvancedConfiguratinButton = screen.getByTestId('lg-form_footer-cancel_button');
        expect(goToAdvancedConfiguratinButton).toBeInTheDocument();
        const experimentalSplitButton = screen.queryByTestId('advanced-configuration-split-button');
        expect(experimentalSplitButton).not.toBeInTheDocument();
      });
    });

    describe('and the project has existing clusters', () => {
      beforeEach(async () => {
        await act(async () => {
          renderResult = render(
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                  ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                })
              }
              windowLocation={fakeLocation}
              projectHasClusters={true}
            />
          );
        });
      });

      it('shows the experimental SplitButton and not the original Button', () => {
        const experimentalSplitButton = screen.getByTestId('advanced-configuration-split-button');
        expect(experimentalSplitButton).toBeInTheDocument();
        const goToAdvancedConfiguratinButton = screen.queryByTestId('lg-form_footer-cancel_button');
        expect(goToAdvancedConfiguratinButton).not.toBeInTheDocument();
      });

      it("and the user opens up the Split Button dropdown menu and clicks the 'Copy Configuration Code' option", () => {
        const splitButtonDropdownMenuButton = screen.getByLabelText(
          'advanced-configuration-split-button-dropdown-menu'
        );
        fireEvent.click(splitButtonDropdownMenuButton);
        const copyCodeConfigurationOption = screen.getByTestId('split-button-show-copyable-cluster-modal');
        fireEvent.click(copyCodeConfigurationOption);
        const copyClusterConfigurationModal = screen.getByTestId('copy-cluster-configuration-modal');
        expect(copyClusterConfigurationModal).toBeInTheDocument();
        expect(trackMock).toHaveBeenCalledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
          action: 'Copy Configuration Code Button Clicked',
          context: 'Cluster Starter Templates',
        });
      });
    });

    describe('and the project has existing clusters and the query param to auto-show the CCC modal is present', () => {
      beforeEach(async () => {
        getCurrentRouteQueryStringMock.mockReturnValue('showCopyableClusterConfigurationModal=true');
        await act(async () => {
          renderResult = render(
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                  ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                })
              }
              windowLocation={fakeLocation}
              projectHasClusters={true}
            />
          );
        });
      });

      it('the modal auto-shows', () => {
        const copyClusterConfigurationModal = screen.getByTestId('copy-cluster-configuration-modal');
        expect(copyClusterConfigurationModal).toBeInTheDocument();
      });
    });

    describe("and there is a cluster draft, but CCC modal shouldn't auto-show", () => {
      beforeEach(async () => {
        const clusterDescription: ClusterDescription = clusterFixtures.getGcpDefaultClusterDescription_asJSON(
          'draft_cluster',
          'M10'
        );
        const clusterDraft: ClusterDraftResponse = {
          clusterConfiguration: {
            instanceSize: 'M10',
            clusterDescription,
            searchDeploymentSpec: undefined,
            processArgs: clusterFixtures.getDefaultProcessArgs(),
            tags: [{ key: 'cluster tag', value: 'draft tag', visibility: TagVisibility.PUBLIC }],
            loadSampleDataset: true,
          },
          draftId: '',
          isFromCst: true,
          lastUpdatedTime: now.toISOString(),
        };
        // mock cluster draft with tags
        getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

        await act(async () => {
          renderResult = render(
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                  ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                })
              }
              windowLocation={fakeLocation}
            />
          );
        });
      });

      it('shows the cluster drafts notification toast', () => {
        expect(screen.getByTestId('draft-new-notification-toast')).toBeInTheDocument();
      });

      it('does not delete the existing cluster draft', () => {
        expect(deleteClusterDraftOrSendErrorMock).not.toHaveBeenCalled();
      });
    });

    describe('and there is a cluster draft and the CCC modal should auto-show', () => {
      beforeEach(async () => {
        getCurrentRouteQueryStringMock.mockReturnValue('showCopyableClusterConfigurationModal=true');
        const clusterDescription: ClusterDescription = clusterFixtures.getGcpDefaultClusterDescription_asJSON(
          'draft_cluster',
          'M10'
        );
        const clusterDraft: ClusterDraftResponse = {
          clusterConfiguration: {
            instanceSize: 'M10',
            clusterDescription,
            searchDeploymentSpec: undefined,
            processArgs: clusterFixtures.getDefaultProcessArgs(),
            tags: [{ key: 'cluster tag', value: 'draft tag', visibility: TagVisibility.PUBLIC }],
            loadSampleDataset: true,
          },
          draftId: '',
          isFromCst: true,
          lastUpdatedTime: now.toISOString(),
        };
        // mock cluster draft with tags
        getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

        await act(async () => {
          renderResult = render(
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                  ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                })
              }
              windowLocation={fakeLocation}
              projectHasClusters={true}
            />
          );
        });
      });

      it('does not show the cluster drafts notification toast', () => {
        expect(screen.queryByTestId('draft-new-notification-toast')).not.toBeInTheDocument();
      });

      it('deletes the existing cluster draft', () => {
        expect(deleteClusterDraftOrSendErrorMock).toHaveBeenCalledWith({
          groupId: mockGroupId,
          clusterName: 'draft_cluster',
          isEdit: false,
        });
      });
    });
  });

  describe('When user is in CUT On CST experiment', () => {
    describe('and is in the variant group', () => {
      beforeEach(async () => {
        getRelevantClusterDraftMock.mockReset();
        getExperimentAttributeMock.mockImplementation((expAttr, fallback) =>
          expAttr === ExperimentFeatureFlag.showOnCST ? true : fallback
        );

        assignMock = jest.fn();
        fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
        await act(async () => {
          renderResult = render(
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT', 'SERVERLESS_SHARED_UI_OPTION_ENABLED'],
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                  USER_COUNTRY_CODE: 'US',
                })
              }
              windowLocation={fakeLocation}
            />
          );
        });
      });

      it('fires experiment viewed event', () => {
        expect(trackIsInSampleMock).toHaveBeenCalledWith(TestName.cutOnCST, undefined, CloudTeams.AtlasGrowth);
      });

      it('shows the details in the card', () => {
        const m10TemplateCard = screen.getByTestId('template-cards-m10');
        const m10TemplateCardContent = within(m10TemplateCard);
        expect(m10TemplateCardContent.getByText(/1 GB/i)).toBeInTheDocument();
      });

      it('shows the price in the card', () => {
        const m10TemplateCard = screen.getByTestId('template-cards-m10');
        const m10TemplateCardContent = within(m10TemplateCard);
        expect(m10TemplateCardContent.getByText(`$0.10/hour`)).toBeInTheDocument();
      });

      it('shows the features in the card', () => {
        const m10TemplateCard = screen.getByTestId('template-cards-m10');
        const m10TemplateCardContent = within(m10TemplateCard);
        expect(m10TemplateCardContent.getByText(`Flexible backups`)).toBeInTheDocument();
      });

      it('does not show included features on the page', () => {
        expect(screen.queryByTestId('boc-included-features')).not.toBeInTheDocument();
      });

      it('still shows price description banner when M10 selected', async () => {
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        await act(async () => {
          fireEvent.click(options[0]);
        });

        expect(screen.getByTestId('price-description-banner')).toBeInTheDocument();
      });

      it('still shows price description banner when Serverless selected', async () => {
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        await act(async () => {
          fireEvent.click(options[1]);
        });

        expect(screen.getByTestId('price-description-banner')).toBeInTheDocument();
      });

      it('does not show price description banner when M0 selected', async () => {
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        await act(async () => {
          fireEvent.click(options[2]);
        });

        expect(screen.queryByTestId('price-description-banner')).not.toBeInTheDocument();
      });
    });

    describe('and is in the control group', () => {
      beforeEach(async () => {
        getRelevantClusterDraftMock.mockReset();
        getExperimentAttributeMock.mockImplementation((expAttr, fallback) =>
          expAttr === ExperimentFeatureFlag.showOnCST ? false : fallback
        );

        assignMock = jest.fn();
        fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
        await act(async () => {
          renderResult = render(
            <ClusterStarterTemplatesPage
              groupId={mockGroupId}
              defaultTemplates={defaultTemplates}
              defaultRegions={defaultRegions}
              providers={providerOptions}
              settingsModel={
                new SettingsModel({
                  ORG_ID: mockOrgId,
                  RECAPTCHA_PUBLIC: 'captcha',
                  isRecaptChaEnabled: false,
                  SELF_SERVE_PAYMENTS_ENABLED: true,
                  GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT', 'SERVERLESS_SHARED_UI_OPTION_ENABLED'],
                  HAS_ORG_BILLING_ADMIN: true,
                  IS_GROUP_ATLAS_ADMIN: true,
                  USER_COUNTRY_CODE: 'US',
                })
              }
              windowLocation={fakeLocation}
            />
          );
        });
      });

      it('fires experiment viewed event', () => {
        expect(trackIsInSampleMock).toHaveBeenCalledWith(TestName.cutOnCST, undefined, CloudTeams.AtlasGrowth);
      });

      it('shows the details in the card', () => {
        const m10TemplateCard = screen.getByTestId('template-cards-m10');
        const m10TemplateCardContent = within(m10TemplateCard);
        expect(m10TemplateCardContent.getByText(/1 GB/i)).toBeInTheDocument();
      });

      it('shows the price in the card', () => {
        const m10TemplateCard = screen.getByTestId('template-cards-m10');
        const m10TemplateCardContent = within(m10TemplateCard);
        expect(m10TemplateCardContent.getByText(`$0.10/hour`)).toBeInTheDocument();
      });

      it('does not show the features in the card', () => {
        const m10TemplateCard = screen.getByTestId('template-cards-m10');
        const m10TemplateCardContent = within(m10TemplateCard);
        expect(m10TemplateCardContent.queryByText(`Flexible backups`)).not.toBeInTheDocument();
      });

      it('shows the includedFeatures section', () => {
        expect(screen.getByTestId('boc-included-features')).toBeInTheDocument();
      });

      it('shows the price description banner', async () => {
        expect(screen.queryByTestId('price-description-banner')).toBeInTheDocument();
      });
    });
  });

  describe('When user has a saved cluster draft and load the cluster draft', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      const clusterDescription: ClusterDescription = clusterFixtures.getGcpDefaultClusterDescription_asJSON(
        'draft_cluster',
        'M10'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M10',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: [{ key: 'cluster tag', value: 'draft tag', visibility: TagVisibility.PUBLIC }],
          loadSampleDataset: true,
        },
        draftId: '',
        isFromCst: true,
        lastUpdatedTime: now.toISOString(),
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });
    // load default state of the CST page
    it('renders N.Virginia as the region', () => {
      const regionSelector = document.getElementsByClassName('Select-control')[0];
      const region = regionSelector.querySelector('strong');
      expect(region?.innerHTML).toBe('N. Virginia');
    });

    it('renders the cluster name selector', () => {
      const selector = screen.getByTestId('cluster-starter-template-name-selector');
      expect(selector.querySelector('input')?.value).toBe('Cluster0');
    });

    it('selects AWS as the provider', () => {
      const selector = screen.getByTestId('cloud-provider-selector');
      const options = selector.querySelectorAll('input');
      const checkedOption = Array.from(options).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(CloudProvider.AWS);
    });

    it('shows the m10 is selected', () => {
      const clusterSelector = screen.getByTestId('template-cards');
      const options = clusterSelector.querySelectorAll('input');
      const checkedOption = Array.from(options).find((cp) => !!cp.checked);
      expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M10);
    });

    it('shows no tags', () => {
      // no tags shown
      expect(screen.queryByDisplayValue('cluster tag 1')).not.toBeInTheDocument();
    });

    it('does not show the failed to load toast', () => {
      expect(screen.queryByTestId('draft-failed-to-load-toast')).not.toBeInTheDocument();
    });

    // when user click on the continue button on the draft modal, load the cluster draft
    describe('when user click on the continue button on the draft modal', () => {
      beforeEach(async () => {
        const link = screen.getByTestId('toast-link');
        await act(async () => {
          fireEvent.click(link);
        });
        expect(screen.getByTestId('cluster-draft-modal')).toBeInTheDocument();
        // click continue button
        const continueWithDraftButton = screen.getByTestId('continue-button');
        await act(async () => {
          fireEvent.click(continueWithDraftButton);
        });
      });
      it('renders South Carolina as the region', () => {
        const regionSelector = document.getElementsByClassName('Select-control')[0];
        const region = regionSelector.querySelector('strong');
        expect(region?.innerHTML).toBe('South Carolina');
      });

      it('renders the cluster name selector', () => {
        const selector = screen.getByTestId('cluster-starter-template-name-selector');
        expect(selector.querySelector('input')?.value).toBe('draft_cluster');
      });

      it('selects the GCP as provider', () => {
        const selector = screen.getByTestId('cloud-provider-selector');
        const options = selector.querySelectorAll('input');
        const checkedOption = Array.from(options).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(CloudProvider.GCP);
      });

      it('shows the m10 is selected', () => {
        const clusterSelector = screen.getByTestId('template-cards');
        const options = clusterSelector.querySelectorAll('input');
        const checkedOption = Array.from(options).find((cp) => !!cp.checked);
        expect(checkedOption?.value).toBe(ClusterStarterTemplateCardOption.M10);
      });

      it('shows the cluster draft tags', () => {
        expect(screen.getByDisplayValue('cluster tag')).toBeInTheDocument();
        expect(screen.getByDisplayValue('draft tag')).toBeInTheDocument();
      });
    });
  });

  describe('When user has a saved cluster draft and coming from cartEmail', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      fakeLocation.search = `?from=${FromLocation.CART_EMAIL}`;
      getCurrentRouteQueryStringMock.mockReturnValue(`from=${FromLocation.CART_EMAIL}`);

      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      const clusterDescription: ClusterDescription = clusterFixtures.getGcpDefaultClusterDescription_asJSON(
        'draft_cluster',
        'M10'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M10',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: [{ key: 'cluster tag', value: 'draft tag', visibility: TagVisibility.PUBLIC }],
          loadSampleDataset: true,
        },
        draftId: '',
        isFromCst: true,
        lastUpdatedTime: now.toISOString(),
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('should not show cluster draft notification toast', () => {
      expect(screen.queryByTestId('toast-title')).not.toBeInTheDocument();
    });

    it('does  show the cluster drafts modal', () => {
      expect(screen.getByTestId('cluster-draft-modal')).toBeInTheDocument();
    });
  });

  describe('When user has a saved cluster draft and performs actions on toast for advanced configuration', () => {
    beforeEach(async () => {
      assignMock = jest.fn();
      fakeLocation.assign = assignMock as jest.MockedFunction<typeof fakeLocation.assign>;
      const clusterDescription: ClusterDescription = clusterFixtures.getAwsDefaultClusterDescription_asJSON(
        'draft_cluster',
        'M30'
      );
      const clusterDraft: ClusterDraftResponse = {
        clusterConfiguration: {
          instanceSize: 'M30',
          clusterDescription,
          searchDeploymentSpec: undefined,
          processArgs: clusterFixtures.getDefaultProcessArgs(),
          tags: [{ key: 'cluster tag', value: 'draft tag', visibility: TagVisibility.PUBLIC }],
          loadSampleDataset: true,
        },
        draftId: '',
        isFromCst: false,
        lastUpdatedTime: now.toISOString(),
      };
      // mock cluster draft with tags
      getRelevantClusterDraftMock.mockResolvedValue(clusterDraft);

      await act(async () => {
        renderResult = render(
          <ClusterStarterTemplatesPage
            groupId={mockGroupId}
            defaultTemplates={defaultTemplates}
            defaultRegions={defaultRegions}
            providers={providerOptions}
            settingsModel={
              new SettingsModel({
                ORG_ID: mockOrgId,
                RECAPTCHA_PUBLIC: 'captcha',
                isRecaptChaEnabled: false,
                SELF_SERVE_PAYMENTS_ENABLED: true,
                GROUP_ENABLED_FEATURE_FLAGS: ['RESOURCE_TAG_COMPONENT'],
                ENABLED_FEATURE_FLAGS: { orgEnabledFeatureFlags: ['ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG'] },
                HAS_ORG_BILLING_ADMIN: true,
                IS_GROUP_ATLAS_ADMIN: true,
              })
            }
            windowLocation={fakeLocation}
          />
        );
      });
    });

    it('should show cluster draft notification toast', () => {
      expect(screen.getByTestId('toast-title')).toBeInTheDocument();
      expect(screen.getByTestId('toast-title')).toHaveTextContent('You have a saved advanced configuration.');
    });

    it('should fire events when clicking on the hyperlink', async () => {
      const link = screen.getByTestId('toast-link');
      await act(async () => {
        fireEvent.click(link);
      });
      expect(trackMock).toHaveBeenNthCalledWith(2, SEGMENT_EVENTS.LOAD_CLUSTER_DRAFT_TOAST_CLICKED, {
        is_advanced_config: true,
        context: 'Cluster Starter Templates',
        cluster_name: 'draft_cluster',
        draft_instance_size: 'M30',
      });
    });

    it('should fire events when clicking on the close toast button', async () => {
      const closeButton = screen.getByTestId('lg-toast-dismiss-button');
      await act(async () => {
        fireEvent.click(closeButton);
      });
      expect(trackMock).toHaveBeenNthCalledWith(2, SEGMENT_EVENTS.LOAD_CLUSTER_DRAFT_TOAST_DISMISSED, {
        is_advanced_config: true,
        context: 'Cluster Starter Templates',
        cluster_name: 'draft_cluster',
        draft_instance_size: 'M30',
      });
    });
  });
});
