import { useEffect, useState } from 'react';

import Card from '@leafygreen-ui/card';
import Icon from '@leafygreen-ui/icon';
import { palette } from '@leafygreen-ui/palette';
import TextInput from '@leafygreen-ui/text-input';
import { H3 } from '@leafygreen-ui/typography';

import { RestoreName } from '@packages/types/nds/backup/systemClusterJob';

import BrandIcon from '@packages/brand-icons';
import backupUtils from '@packages/common/utils/backupUtils';
import { formatDateTimeWithTimezoneWithDate } from '@packages/common/utils/dateFormatters';
import { getPageScrollContainer } from '@packages/layout/pageScrollContainer';

export interface SnapshotRestoreConfirmStepProps {
  clusterName: string;
  groupName: string;
  isPit: boolean;
  useFullSnapshot: boolean;
  pitUTCSeconds: number | null;
  pitOplogTs: string;
  snapshotDate: string;
  destinationClusterName: string;
  destinationGroupName: string;
  onUserAgreementInput: (value: string, phrase: string) => void;
  // Collection restore specific props
  dbsToRestore?: Array<RestoreName>;
  collectionsToRestore?: Array<RestoreName>;
  indexOption?: string;
  failureProcedure?: string;
  writeStrategy?: string;
}

const computeUserPreferenceRestoreTime = (pitUTCSeconds: number | null, pitOplogTs: string) => {
  const timeZoneId = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const chosenPitUTCSeconds = pitUTCSeconds !== null ? pitUTCSeconds : Number(pitOplogTs);
  const date = new Date(chosenPitUTCSeconds * 1000);
  return formatDateTimeWithTimezoneWithDate(date, timeZoneId);
};

const computeUserPreferenceSnapshotDate = (snapshotDate: string) => {
  const timeZoneId = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const date = new Date(snapshotDate);
  return formatDateTimeWithTimezoneWithDate(date, timeZoneId);
};

const formatRestoreNames = (
  dbNames: Array<RestoreName> | undefined,
  collNames: Array<RestoreName> | undefined
): string => {
  const restoreNames = (dbNames || []).concat(collNames || []);
  return restoreNames.map((item) => item.sourceName).join(', ');
};

const getRestoreDataDisplayText = (
  dbsToRestore: Array<RestoreName>,
  collectionsToRestore: Array<RestoreName>
): string => {
  const dbCount = dbsToRestore?.length || 0;
  const collCount = collectionsToRestore?.length || 0;

  if (dbCount > 0 && collCount > 0) {
    return `${dbCount} Database${dbCount === 1 ? '' : 's'} & ${collCount} Collection${collCount === 1 ? '' : 's'}`;
  } else if (dbCount > 0) {
    return `${dbCount} Database${dbCount === 1 ? '' : 's'}`;
  } else if (collCount > 0) {
    return `${collCount} Collection${collCount === 1 ? '' : 's'}`;
  }
  return 'Not specified';
};

function SnapshotRestoreConfirmStep(props: SnapshotRestoreConfirmStepProps) {
  const {
    clusterName,
    groupName,
    isPit,
    snapshotDate,
    useFullSnapshot,
    pitUTCSeconds,
    pitOplogTs,
    onUserAgreementInput,
    destinationGroupName,
    destinationClusterName,
    dbsToRestore,
    collectionsToRestore,
    indexOption,
    failureProcedure,
    writeStrategy,
  } = props;

  // Scroll to top when component mounts
  useEffect(() => {
    const body = getPageScrollContainer();
    if (body) {
      body.scrollTop = 0;
    }
  }, []);

  const [inputValue, setInputValue] = useState('');
  const [validationError, setValidationError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // Validate input matches destination cluster name
    if (value && value !== destinationClusterName) {
      setValidationError(`Incorrect destination cluster name.`);
    } else {
      setValidationError(null);
    }

    // Call the original callback
    onUserAgreementInput(value, destinationClusterName);
  };
  return (
    <div data-testid="confirm-step">
      <Card data-testid="restore-overview-card">
        <H3 style={{ marginBottom: '16px' }}>Restore Overview</H3>
        <div style={{ display: 'flex', alignItems: 'flex-start', gap: '32px', marginTop: '16px' }}>
          <BrandIcon iconName="Technical_SOFTWARE_BackupSnapshots_Spot" width="348" height="234" />
          <div>
            <div style={{ marginTop: '32px' }}>
              <label>Restore Type</label>
              <H3 data-testid="restore-type">
                {isPit
                  ? 'Point in Time (' + computeUserPreferenceRestoreTime(pitUTCSeconds, pitOplogTs) + ')'
                  : 'Snapshot (' + computeUserPreferenceSnapshotDate(snapshotDate) + ')'}
              </H3>
            </div>
            <div style={{ display: 'flex', gap: '160px', marginTop: '48px' }}>
              <div>
                <label>Restore Data</label>
                <div data-testid="restore-data">
                  {useFullSnapshot
                    ? 'Entire Snapshot'
                    : getRestoreDataDisplayText(dbsToRestore || [], collectionsToRestore || [])}
                </div>
              </div>
              {!useFullSnapshot && (
                <div>
                  <label>Write strategy</label>
                  <div>{backupUtils.getWriteStrategyLabel(writeStrategy)}</div>
                </div>
              )}
            </div>
          </div>
        </div>
      </Card>
      <br></br>
      <br></br>
      <div style={{ display: 'flex', gap: '16px' }}>
        <Card style={{ width: '50%' }} data-testid="source-card">
          <H3 style={{ marginBottom: '16px' }}>Source</H3>
          <div>
            <label>Source project</label>
            <div data-testid="source-project">{groupName}</div>
          </div>
          <div style={{ marginTop: '16px' }}>
            <label>Source cluster</label>
            <div data-testid="source-cluster">{clusterName}</div>
          </div>

          {!useFullSnapshot && (
            <>
              <div style={{ marginTop: '16px' }}>
                <label>Source database/collection name(s)</label>
                <div>{formatRestoreNames(dbsToRestore, collectionsToRestore)}</div>
              </div>
            </>
          )}
        </Card>
        <Card style={{ width: '50%' }} data-testid="destination-card">
          <H3 style={{ marginBottom: '16px' }}>Destination</H3>
          <div>
            <label>Destination project</label>
            <div data-testid="destination-project">{destinationGroupName}</div>
          </div>
          <div style={{ marginTop: '16px' }}>
            <label>Destination cluster</label>
            <div data-testid="destination-cluster">{destinationClusterName}</div>
          </div>
          {!useFullSnapshot && (
            <>
              <div style={{ marginTop: '16px' }}>
                <label>Index restore option</label>
                <div>{backupUtils.getIndexOptionLabel(indexOption)}</div>
              </div>
              <div style={{ marginTop: '16px' }}>
                <label>Failure procedure</label>
                <div>{backupUtils.getFailureProcedureLabel(failureProcedure)}</div>
              </div>
            </>
          )}
        </Card>
      </div>
      <br></br>
      {destinationClusterName !== '' &&
        (useFullSnapshot || (!useFullSnapshot && writeStrategy === 'OVERWRITE_IF_EXISTS')) && (
          <Card data-testid="confirmation-card">
            <div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Icon glyph="Warning" fill={palette.red.base} size="default" />{' '}
                <H3 data-testid="warning-title">Are you sure you want to overwrite existing data?</H3>
              </div>
              <div>
                {useFullSnapshot ? (
                  <p data-testid="warning-message">
                    All existing data on the target cluster {destinationClusterName} will be deleted prior to the
                    restore. The destination cluster will also be unavailable for the duration of the restore. Please
                    enter the destination cluster name to confirm this action.
                  </p>
                ) : (
                  <p>
                    Destination database/collections with matching names in the destination cluster will be overwritten.
                    Please enter the destination cluster name {destinationClusterName} to confirm this action.
                  </p>
                )}
                <TextInput
                  data-testid="confirmation-input"
                  onChange={handleInputChange}
                  label="Type the destination cluster name to confirm"
                  value={inputValue}
                  state={validationError ? 'error' : 'none'}
                  errorMessage={validationError || undefined}
                />
              </div>
            </div>
          </Card>
        )}
    </div>
  );
}

export default SnapshotRestoreConfirmStep;
