import React, { useEffect, useState } from 'react';

import Banner from '@leafygreen-ui/banner';
import { Option, Select } from '@leafygreen-ui/select';
import classNames from 'classnames';

import { EncryptionAtRestProvider } from '@packages/types/nds/backupSnapshot';
import { WithDefaultProps } from '@packages/types/withDefaultProps';

import WhitelistedIpCollection from '@packages/common/collections/WhitelistedIpCollection';
import { OptionType } from '@packages/components/DropdownSelect/types';
import { AutomatedRestoreProjectPicker } from '@packages/project/backup/components/AutomatedRestoreProjectPicker';
import { WhitelistTableConnected } from '@packages/project/nds/backup/components/WhitelistTable';

interface OwnProps {
  destinationClusterName: string;
  destinationGroupId: string;
  destinationGroupName: string;
  destinationGroupType: string;
  destinationItems?: Array<{
    name?: string;
    encryptionEnabled?: boolean;
    encryptionType?: string;
    errorMessage?: string;
    isTenantCluster?: boolean;
    isServerless?: boolean;
  }>;
  destinationOrgName: string;
  docsUrl: string;
  errorMessage: string;
  groupId: string;
  isPit: boolean;
  isResilient: boolean;
  isServerless?: boolean;
  isTenant?: boolean;
  loadRestoreDestinations: () => void;
  members?: Array<{ snapshotId?: string; rsId?: string; geoSharded?: boolean; region?: string }>;
  onChangeTargetCluster: (_: OptionType) => void;
  onSelectProject: (_: OptionType) => void;
  restoreDisabled: boolean;
  snapshotEncrypted: boolean;
  snapshotEncryptionType: EncryptionAtRestProvider;
  snapshotHasSearchIndex: boolean;
  whitelistedIpCollection?: typeof WhitelistedIpCollection;
  useFullSnapshot: boolean;
}

const snapshotRestoreChooseDestinationDefaultProps = {
  members: [],
  whitelistedIpCollection: new WhitelistedIpCollection(),
};

type Props = WithDefaultProps<OwnProps, typeof snapshotRestoreChooseDestinationDefaultProps>;

const SnapshotRestoreChooseDestination: React.FC<Props> = (props) => {
  const [directAttachLearnMore, setDirectAttachLearnMore] = useState(false);

  useEffect(() => {
    props.loadRestoreDestinations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const {
    destinationClusterName,
    destinationGroupId,
    destinationGroupName,
    destinationGroupType,
    destinationItems,
    destinationOrgName,
    docsUrl,
    errorMessage,
    isPit,
    isServerless,
    isTenant,
    members,
    onChangeTargetCluster,
    onSelectProject,
    snapshotEncrypted,
    snapshotEncryptionType,
    snapshotHasSearchIndex,
    whitelistedIpCollection,
    useFullSnapshot,
  } = props;

  const ipsJson = whitelistedIpCollection.getIPs().toJSON();
  const securityGroupsJson = whitelistedIpCollection.getSecurityGroups().toJSON();
  const hasPending = whitelistedIpCollection.hasPending();
  const isTargetCM = destinationGroupType === 'CLOUD';
  const backupDocsUrl = `${docsUrl}/backup/cloud-provider-snapshots/#global-cluster-backups`;
  const showEncryptedSnapshotNotes = snapshotEncrypted && !isPit;
  const showSearchIndexNotes = snapshotHasSearchIndex && !isPit;
  const ndsTargetsOnly = snapshotEncrypted || isPit;
  const isResilientVal = props.isResilient;

  let destinationItem = null;
  let canNotDirectAttachReasons: Array<string> | null = null;

  if (destinationItems) {
    destinationItem = destinationItems.find((di) => di.name === destinationClusterName);
  }

  if (destinationItem) {
    canNotDirectAttachReasons = (destinationItem as any).canNotDirectAttachReasons || [];
  }

  const generateDropdownOptions = (destinationItems: Array<any>) => {
    return destinationItems.map((d: any) => {
      if (
        (isServerless || (!isServerless && !d.isServerless)) &&
        (isPit || d.encryptionType === snapshotEncryptionType) &&
        !d.isTenantCluster
      ) {
        return { label: d.name, value: d.name };
      }

      if (!isServerless && d.isServerless) {
        return {
          label: `${d.name} (Cluster type not supported)`,
          value: d.name,
          disabled: true,
          reason: 'This snapshot cannot be restored to a serverless cluster',
        };
      }

      if (!isTenant && d.isTenantCluster) {
        return {
          label: `${d.name} (Cluster type not supported)`,
          value: d.name,
          disabled: true,
          reason: 'This snapshot cannot be restored to a shared tier cluster',
        };
      }

      if (!isPit && d.encryptionType !== snapshotEncryptionType) {
        return {
          label: `${d.name} (Cluster encryption type not supported)`,
          value: d.name,
          disabled: true,
          reason: 'This snapshot has incompatible encryption settings',
        };
      }

      return { label: d.name, value: d.name };
    });
  };

  return (
    <div className="automated-restore-choose-cluster">
      {isResilientVal && (
        <div className="automated-restore-choose-cluster-resilient">
          <Banner variant="danger">
            <div>
              <b>Warning!</b> You are about to perform a restore of a snapshot that was taken using the fallback method!
              Click <a href="https://dochub.mongodb.org/core/atlas-resilient-backups">here</a> for more information
            </div>
          </Banner>
        </div>
      )}
      {showEncryptedSnapshotNotes && (
        <p>Note: Snapshots cannot be restored to clusters with different encryption settings or topologies.</p>
      )}
      {useFullSnapshot && showSearchIndexNotes && (
        <p>
          Note: Any search indexes present in this snapshot will be rebuilt on the cluster after the restore is
          completed.
        </p>
      )}
      {members && members.some((member) => member.geoSharded) && (
        <div className="bem-alert bem-alert-is-warning automated-restore-choose-cluster-warning-box">
          <div className="automated-restore-choose-cluster-warning-header">
            <i className="fa fa-exclamation-triangle automated-restore-choose-cluster-warning-icon" />
            Global Cluster Snapshot Restore
          </div>
          <div className="automated-restore-choose-cluster-warning-text">
            You are restoring a snapshot taken on a global cluster that is distributed among different zones. You can
            read the global cluster{' '}
            {
              <a
                className={classNames({
                  'automated-restore-choose-cluster-has-dimmed-link': true,
                })}
                href={backupDocsUrl}
                target="_blank"
              >
                documentation
              </a>
            }{' '}
            for more info. The snapshot has these zones:
          </div>
          <ul>
            {members
              .filter((member) => member.geoSharded)
              .sort((member1, member2) => ((member1.rsId || '') > (member2.rsId || '') ? 1 : -1))
              .map((member) => (
                <li key={member.rsId}>
                  <b>{member.rsId}</b>: {member.region}
                </li>
              ))}
          </ul>
        </div>
      )}
      <form className="backup-form" id="backupForm" onSubmit={(event) => event.preventDefault()}>
        <div className="backup-form-input-groups">
          <div className="backup-form-input-group form-group">
            <label className="control-label" htmlFor="selectProject">
              Destination Project
            </label>
            <AutomatedRestoreProjectPicker
              automatedDiskRestore
              buttonWidth="max"
              clientSideFilter={false}
              initialGroupId={destinationGroupId}
              initialGroupName={destinationGroupName}
              initialOrgName={destinationOrgName}
              ndsOnly={ndsTargetsOnly}
              onSelectProject={onSelectProject}
            />
          </div>
        </div>
        <div className="backup-form-input-groups">
          <div
            className={classNames('backup-form-input-group form-group', {
              'has-error': false,
            })}
          >
            <Select
              label="Destination Cluster"
              value={destinationClusterName}
              onChange={(value) =>
                onChangeTargetCluster({
                  value,
                  label: '',
                })
              }
            >
              {generateDropdownOptions(destinationItems ?? []).map((option) => (
                <Option key={option.value} value={option.value} disabled={option.disabled} description={option.reason}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </div>
        </div>
        {canNotDirectAttachReasons !== null && canNotDirectAttachReasons.length > 0 && (
          <div className="direct-attach-learn-more">
            <Banner variant="info">
              <p>
                <i className="fa fa-info-circle fa-fw" />
                &nbsp;The duration of restoring a backup depends on project and region. This restore is not optimized
                for speed.
              </p>
              <p>
                <a onClick={() => setDirectAttachLearnMore((prev) => !prev)} role="button" tabIndex={0}>
                  Learn more &nbsp;
                  <i
                    className="fa fa-angle-down fa-2x direct-attach-learn-more-arrow"
                    style={{ transform: directAttachLearnMore ? 'rotate(0deg)' : 'rotate(180deg)' }}
                  />
                </a>
              </p>
              {directAttachLearnMore && (
                <p>
                  <ul className="direct-attach-learn-more-list">
                    {canNotDirectAttachReasons.includes('TARGET_DIFFERENT_PROJECT') && (
                      <li>Target cluster needs to be in the same project as the snapshot.</li>
                    )}
                    {canNotDirectAttachReasons.includes('TARGET_DIFFERENT_REGION') && (
                      <li>Target cluster needs to be in the same region as the snapshot.</li>
                    )}
                    {canNotDirectAttachReasons.includes('TARGET_DISK_INCOMPATIBLE') && (
                      <li>
                        Target cluster&apos;s instance storage settings need to support the snapshot disk capacity.
                      </li>
                    )}
                    {(canNotDirectAttachReasons.includes('CROSS_CLOUD') ||
                      canNotDirectAttachReasons.includes('TARGET_NVME')) && (
                      <li>
                        Target cluster can&apos;t be any of the following: global, multi-region, multi-cloud, or NVME
                        cluster.
                      </li>
                    )}
                  </ul>
                  <a
                    href="https://www.mongodb.com/docs/atlas/backup/cloud-backup/restore-from-snapshot/#recommendations-to-optimize-restore-times"
                    rel="noopener noreferrer"
                    target="_blank"
                  >
                    View Docs&nbsp;
                    <i aria-hidden="true" className="fa fa-external-link" />
                  </a>
                </p>
              )}
            </Banner>
          </div>
        )}
      </form>

      {isTargetCM && (
        <div className="automated-restore-choose-cluster-whitelist-section">
          <span>
            IP address(es) of the Cloud Manager project need to be configured in the IP Access List for the snapshot
            restore to work.
          </span>
          <h5 className="automated-restore-choose-cluster-title">Existing IP Access List</h5>
          <WhitelistTableConnected
            animatePending={false}
            header=""
            ipsJson={ipsJson}
            securityGroupsJson={securityGroupsJson}
          />
          {hasPending && (
            <p className="automated-restore-choose-cluster-pending-notes">
              A pending IP Access List entry may take a few minutes to become active. In the meantime, you can continue
              to initiate the restore.{' '}
            </p>
          )}
          <div>
            <a href="#/security/network/accessList" target="_blank">
              Add or Modify your IP Addresses
            </a>
          </div>
        </div>
      )}

      {errorMessage && (
        <div className="automated-restore-choose-cluster-alert">
          <div className="banner banner-is-alert banner-is-alert-unfixed-height">
            <div className="banner-main">Error: {errorMessage}</div>
            <br />
          </div>
        </div>
      )}
    </div>
  );
};

SnapshotRestoreChooseDestination.defaultProps = snapshotRestoreChooseDestinationDefaultProps;

export default SnapshotRestoreChooseDestination;
