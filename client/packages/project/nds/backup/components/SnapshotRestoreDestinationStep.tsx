import { useEffect, useState } from 'react';

import { css } from '@emotion/css';
import Card from '@leafygreen-ui/card';
import { Option, Select } from '@leafygreen-ui/select';

import { EncryptionAtRestProvider, FailureProcedureOptions, IndexOptions } from '@packages/types/nds/backupSnapshot';

import * as api from '@packages/common/services/api';
import WhitelistedIpCollection from '@packages/common/collections/WhitelistedIpCollection';
import { exceptionToMessage } from '@packages/common/services/errorHelper';
import { getPageScrollContainer } from '@packages/layout/pageScrollContainer';
import ipWhitelistService from '@packages/project/common/services/ipWhitelistService';
import serverlessInstanceService from '@packages/project/common/services/serverlessInstanceService';
import SnapshotRestoreChooseDestination from '@packages/project/nds/backup/components/SnapshotRestoreChooseDestination';

const styles = {
  container: css`
    display: flex;
    flex-direction: column;
  `,
  content: css`
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  `,
};

export interface SnapshotRestoreDestinationStepProps {
  clusterName: string;
  docsUrl: string;
  groupId: string;
  orgId: string;
  isPit: boolean;
  isResilient: boolean;
  isSharded: boolean;
  handleIndexOption: (value: string) => void;
  handleFailureOption: (value: string) => void;
  indexOption: string;
  failureProcedure: string;
  members?: Array<{
    snapshotId?: string;
    rsId?: string;
    geoSharded?: boolean;
    regions?: Array<string>;
  }>;
  snapshotEncrypted: boolean;
  snapshotHasSearchIndex: boolean;
  snapshotEncryptionType: EncryptionAtRestProvider;
  snapshotId: string;
  useFullSnapshot: boolean;
  chosenPitUTCSeconds: number | null;
  pitOplogTs: string;
  pitOplogInc: string;
  destinationClusterName: string;
  destinationGroupId: string;
  destinationGroupName: string;
  setDestinationClusterName: (name: string) => void;
  setDestinationGroupId: (id: string) => void;
  setDestinationGroupName: (id: string) => void;
  setUserAgreedToRestore: (value: boolean) => void;
  noOverwrite: boolean;
}

function SnapshotRestoreDestinationStep(props: SnapshotRestoreDestinationStepProps) {
  const {
    clusterName,
    docsUrl,
    groupId,
    orgId,
    isPit,
    isResilient,
    isSharded,
    handleIndexOption,
    handleFailureOption,
    indexOption,
    failureProcedure,
    members = [],
    snapshotEncrypted,
    snapshotHasSearchIndex,
    snapshotEncryptionType,
    snapshotId,
    useFullSnapshot,
    chosenPitUTCSeconds,
    pitOplogTs,
    pitOplogInc,
    destinationGroupId,
    destinationClusterName,
    destinationGroupName,
    setDestinationClusterName,
    setDestinationGroupId,
    setDestinationGroupName,
    setUserAgreedToRestore,
    noOverwrite,
  } = props;

  // Scroll to top when component mounts
  useEffect(() => {
    const body = getPageScrollContainer();
    if (body) {
      body.scrollTop = 0;
    }
  }, []);

  const [restoreDisabled] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [destinationGroupType, setDestinationGroupType] = useState('');
  const [destinationOrgName, setDestinationOrgName] = useState(orgId);
  const [destinationItems, setDestinationItems] = useState<Array<any>>([]);
  const [whitelistedIpCollection, setWhitelistedIpCollection] = useState(new WhitelistedIpCollection());

  useEffect(() => {
    ipWhitelistService.loadWhitelist().then((whitelisted) => {
      setWhitelistedIpCollection(whitelisted);
    });
  }, []);

  const loadRestoreDestinations = () => {
    loadDestinations();
  };

  function loadDestinations() {
    const whenDestinationClusterDescriptions = api.nds.clusterDescriptions.getClusterDescriptions(destinationGroupId);
    const whenRestoreDestinationItems = api.nds.backup.getRestoreDestinationItems(
      groupId,
      clusterName,
      destinationGroupId,
      isSharded,
      snapshotId,
      isPit,
      chosenPitUTCSeconds,
      pitOplogTs,
      pitOplogInc
    );
    const whenServerlessDestinationClusterDescriptions =
      serverlessInstanceService.reloadServerlessInstancesWithPromise(destinationGroupId);

    Promise.all([
      whenDestinationClusterDescriptions,
      whenRestoreDestinationItems,
      whenServerlessDestinationClusterDescriptions,
    ])
      .then(([destinationClusterDescriptions, restoreDestinationItems, serverlessDestinationClusterDescriptions]) => {
        const deploymentClusterNameToClusterDescriptionName: Record<string, string> = {};
        const deploymentClusterNameToMongoDBVersion: Record<string, string> = {};
        destinationClusterDescriptions.forEach((cd: any) => {
          deploymentClusterNameToClusterDescriptionName[cd.deploymentClusterName] = cd.name;
          deploymentClusterNameToMongoDBVersion[cd.deploymentClusterName] = cd.mongoDBVersion;
        });
        if (serverlessDestinationClusterDescriptions) {
          serverlessDestinationClusterDescriptions.models.forEach((cd: any) => {
            deploymentClusterNameToClusterDescriptionName[cd.attributes.deploymentClusterName] = cd.attributes.name;
            deploymentClusterNameToMongoDBVersion[cd.attributes.deploymentClusterName] = cd.attributes.mongoDBVersion;
            if (cd.attributes.state !== 'CREATING') {
              restoreDestinationItems.destinationItems.push({
                name: cd.attributes.deploymentClusterName,
                encryptionEnabled: false,
                encryptionType: 'NONE',
                errorMessage: null,
                isServerless: true,
                isTenantCluster: false,
              });
            }
          });
        }
        const items = restoreDestinationItems.destinationItems.map((rdi: any) => ({
          ...rdi,
          name: deploymentClusterNameToClusterDescriptionName[rdi.name],
          version: deploymentClusterNameToMongoDBVersion[rdi.name],
        }));
        setDestinationGroupType(restoreDestinationItems.groupType);
        setDestinationItems(items);
      })
      .catch((e) => {
        setErrorMessage(exceptionToMessage(e));
      });
  }

  function onSelectProject(selectedOption: any) {
    setDestinationGroupId(selectedOption.id);
    setDestinationGroupName(selectedOption.name);
    setDestinationOrgName(selectedOption.org.name);
    setDestinationClusterName('');
  }

  function onChangeTargetCluster(cluster: any) {
    setDestinationClusterName(cluster.value);
    setUserAgreedToRestore(!useFullSnapshot && noOverwrite);
  }

  useEffect(() => {
    if (destinationGroupId) {
      loadDestinations();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [destinationGroupId]);

  return (
    <div className={styles.container} data-testid="destination-step">
      <Card data-testid="destination-card">
        <h3>Destination</h3>
        <SnapshotRestoreChooseDestination
          data-testid="choose-destination"
          destinationClusterName={destinationClusterName}
          destinationGroupId={destinationGroupId}
          destinationGroupName={destinationGroupName}
          destinationGroupType={destinationGroupType}
          destinationItems={destinationItems}
          destinationOrgName={destinationOrgName}
          docsUrl={docsUrl}
          errorMessage={errorMessage}
          groupId={groupId}
          isPit={isPit}
          isResilient={isResilient}
          isServerless={false}
          isTenant={false}
          loadRestoreDestinations={loadRestoreDestinations}
          members={members}
          onChangeTargetCluster={onChangeTargetCluster}
          onSelectProject={onSelectProject}
          restoreDisabled={restoreDisabled}
          snapshotEncrypted={snapshotEncrypted}
          snapshotEncryptionType={snapshotEncryptionType}
          snapshotHasSearchIndex={snapshotHasSearchIndex}
          whitelistedIpCollection={whitelistedIpCollection}
          useFullSnapshot={useFullSnapshot}
        />
      </Card>
      {!useFullSnapshot && (
        <div className={styles.content}>
          <Card>
            <h3>Restore Details</h3>
            <Select
              data-testid="index-select"
              label="Indexes"
              description={
                'Select indexes to restore. Notes that Atlas will not restore search indexes. Please redefine any search indexes on your target cluster after the restore. ' +
                'It is recommended to restore without TTL indexes that would otherwise delete data in the restore.'
              }
              value={indexOption}
              onChange={handleIndexOption}
            >
              {IndexOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
            <br></br>
            <Select
              data-testid="failure-select"
              label="Failure Procedure"
              value={failureProcedure}
              onChange={handleFailureOption}
            >
              {FailureProcedureOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Card>
        </div>
      )}
    </div>
  );
}

export default SnapshotRestoreDestinationStep;
