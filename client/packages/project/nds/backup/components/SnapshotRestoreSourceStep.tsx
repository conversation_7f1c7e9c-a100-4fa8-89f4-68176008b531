import { useEffect, useMemo, useState } from 'react';

import { css } from '@emotion/css';
import Banner from '@leafygreen-ui/banner';
import Card from '@leafygreen-ui/card';
import Checkbox from '@leafygreen-ui/checkbox';
import ExpandableCard from '@leafygreen-ui/expandable-card';
import Icon from '@leafygreen-ui/icon';
import { palette } from '@leafygreen-ui/palette';
import { RadioBox, RadioBoxGroup } from '@leafygreen-ui/radio-box-group';
import { SearchInput } from '@leafygreen-ui/search-input';
import { SegmentedControl, SegmentedControlOption } from '@leafygreen-ui/segmented-control';
import { Cell, HeaderCell, HeaderRow, Row, Table, TableBody, TableHead } from '@leafygreen-ui/table';
import TextArea from '@leafygreen-ui/text-area';
import Tooltip from '@leafygreen-ui/tooltip';
import { Body } from '@leafygreen-ui/typography';

import { BSONTimestampRange } from '@packages/types/nds/backup';
import { RestoreName } from '@packages/types/nds/backup/systemClusterJob';
import { BackupSnapshot, BackupSnapshotMemberView, EncryptionAtRestProvider } from '@packages/types/nds/backupSnapshot';
import { CustomerCollectionMetadata } from '@packages/types/nds/customerCollectionMetadata';

import mongoDate from '@packages/date';
import { getPageScrollContainer } from '@packages/layout/pageScrollContainer';
import snapshotsTableUtils from '@packages/project/nds/common/utils/snapshotsTableUtils';

import SnapshotRestoreSourcePitStep from './SnapshotRestoreSourcePitStep';

const styles = {
  content: css`
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  `,
  expandableCard: css`
    background-color: ${palette.gray.light3};
  `,
};

const formatDateTime = (date: string | Date) => {
  const timeZoneId = Intl.DateTimeFormat().resolvedOptions().timeZone;
  return mongoDate(date).tz(timeZoneId).format('MM/DD/YY - hh:mm A');
};

const getClusterType = (shardCount: number, members: Array<BackupSnapshotMemberView> | undefined) => {
  if (!members) {
    return 'Replica Set';
  }
  if (members && shardCount === 1) {
    return 'Sharded - ' + shardCount + ' shard';
  }
  return 'Sharded - ' + shardCount + ' shards';
};

const getUsedSized = (usedSize: number | undefined) => {
  return snapshotsTableUtils.getFormattedBytes(usedSize);
};

const getVersion = (mongoDbVersion: string | undefined, mongoDbFcv: string | undefined) => {
  return snapshotsTableUtils.getMongoDbVersionsForDisplay(mongoDbVersion, mongoDbFcv);
};

const getEncryptionId = (encryptionProvider: EncryptionAtRestProvider, encryptionId: string | undefined) => {
  if (encryptionProvider === 'NONE') {
    return 'Not enabled';
  }

  if (['AWS', 'AZURE', 'GCP'].includes(encryptionProvider)) {
    return <span>return {encryptionId}</span>;
  }
};

const getProviderAndRegionsContent = (snapshot: BackupSnapshot) => {
  let members: Array<any>;

  if (snapshot.members) {
    // for sharded cluster snapshots
    members = snapshot.members;
  } else {
    if (snapshot.providers && snapshot.providers.length > 0 && snapshot.regions && snapshot.regions.length > 0) {
      // for replica set snapshots
      members = [
        {
          provider: snapshot.providers[0],
          region: snapshot.regions[0],
          copyRegions: snapshot.copyRegions,
          zoneName: '',
        },
      ];
    } else {
      // no info at all?
      members = [];
    }
  }

  if (members.length === 0) {
    return <span>N/A</span>;
  }

  const allCopyRegions = _.uniq(members.flatMap((member: $TSFixMe) => member.copyRegions));
  const hasCopyRegions = allCopyRegions.length > 0;

  const isGeoSharded = members.some((m: $TSFixMe) => m.geoSharded);

  const byCloudProviders = _.groupBy(members, 'provider');
  const cloudProviders = Object.keys(byCloudProviders).sort();

  const byZoneName = _.groupBy(members, 'zoneName');
  const zoneNames = Object.keys(byZoneName).sort();
  const elems: Array<$TSFixMe> = [];

  for (let zoneName of zoneNames) {
    const membersInZone = byZoneName[zoneName];
    const byCloudProvidersInZone = _.groupBy(membersInZone, 'provider');
    const cloudProvidersInZone = Object.keys(byCloudProvidersInZone).sort();

    if (cloudProvidersInZone.length === 0) {
      return <span>N/A</span>;
    }

    if (isGeoSharded) {
      elems.push(
        <h5 key={zoneName}>
          <strong>{zoneName}</strong>
        </h5>
      );
    }

    const modes = ['Original', 'Copy'];

    for (let mode of modes) {
      for (let cloudProvider of cloudProvidersInZone) {
        const membersInProvider = byCloudProvidersInZone[cloudProvider];
        var regions;
        if (mode === 'Original') {
          regions = _.uniq(membersInProvider.map((member) => member.region)).sort();
        } else {
          regions = _.uniq(membersInProvider.flatMap((member) => member.copyRegions)).sort();
        }

        const modeText = hasCopyRegions ? ' (' + mode + ')' : '';

        for (let region of regions) {
          elems.push(
            <h5 key={`${zoneName}-${mode}-${cloudProvider}-${region}-h5`}>
              <strong>
                {cloudProvider} {region}
                {modeText}
              </strong>
            </h5>
          );
          elems.push(
            <p key={`${zoneName}-${mode}-${cloudProvider}-${region}-p`}>
              {membersInProvider
                .filter((m) => (mode === 'Original' ? m.region === region : m.copyRegions.includes(region)))
                .map((m) => m.rsId)
                .join(', ')}
            </p>
          );
        }
      }
    }
    if (zoneNames.length > 1) {
      elems.push(<br />);
    }
  }

  const tooltip = (
    <div data-testid="multi-cloud-tooltip" className="snapshots-table-multi-cloud-tooltip">
      {elems}
    </div>
  );

  if (cloudProviders.length === 1) {
    // only cloud provider
    const cloudProvider = cloudProviders[0];
    const membersInProvider = byCloudProviders[cloudProvider];
    const regions = _.uniq(membersInProvider.map((member) => member.region));
    const copyRegions = _.uniq(membersInProvider.flatMap((member) => member.copyRegions));
    if (regions.length === 1) {
      // one cloud provider, one region, but has copies
      if (copyRegions !== null && copyRegions.length > 0) {
        return (
          <span>
            {cloudProvider},{' '}
            <Tooltip
              popoverZIndex={100}
              trigger={<span className="underline-dotted">{regions.length + copyRegions.length} regions</span>}
            >
              {tooltip}
            </Tooltip>
          </span>
        );
      }
      // one cloud provider, one region, no copies
      return (
        <span>
          {cloudProvider}, {regions[0]}
        </span>
      );
    } else {
      // one cloud provider, many regions
      return (
        <span>
          {cloudProvider},{' '}
          <Tooltip popoverZIndex={100} trigger={<span className="underline-dotted">Multi-region</span>}>
            {tooltip}
          </Tooltip>
        </span>
      );
    }
  } else {
    // multi cloud provider
    return (
      <span>
        <Tooltip popoverZIndex={100} trigger={<span className="underline-dotted">Multi-cloud</span>}>
          {tooltip}
        </Tooltip>
      </span>
    );
  }
};

//snapshot details fields
const getSnapshotDetails = (snapshot: BackupSnapshot | null) => {
  if (!snapshot) {
    return <span>No snapshot details available</span>;
  }
  return (
    <Table shouldTruncate={false}>
      <TableHead className={styles.expandableCard}>
        <HeaderRow>
          <HeaderCell>Time Created</HeaderCell>
          <HeaderCell>Provider & Region</HeaderCell>
          <HeaderCell>Cluster Type</HeaderCell>
          <HeaderCell>Used Size</HeaderCell>
          <HeaderCell>Version</HeaderCell>
          <HeaderCell>Encryption Key ID</HeaderCell>
        </HeaderRow>
      </TableHead>
      <TableBody>
        <Row>
          <Cell>{formatDateTime(snapshot.creationDate)}</Cell>
          <Cell>{getProviderAndRegionsContent(snapshot)}</Cell>
          <Cell>{getClusterType(snapshot.shardCount || 1, snapshot.members)}</Cell>
          <Cell>{getUsedSized(snapshot.usedDiskSpaceInBytes)}</Cell>
          <Cell>{getVersion(snapshot.mongoDbVersion, snapshot.mongoDbFcv)}</Cell>
          <Cell>
            {getEncryptionId(
              snapshot.encryptionAtRestProvider || EncryptionAtRestProvider.NONE,
              snapshot.encryptionAtRestId
            )}
          </Cell>
        </Row>
      </TableBody>
    </Table>
  );
};

export interface SnapshotRestoreSourceStepProps {
  clusterName: string;
  groupName: string;
  isPit: boolean;
  snapshotDate: string;
  snapshot: BackupSnapshot | null;
  onFullSnapshotRestore: (isFullSnapshot: boolean) => void;
  useFullSnapshot: boolean;

  onNoOverwriteStrategy: (noOverwrite: boolean) => void;
  noOverwrite: boolean;
  isCheckbox: boolean;
  setIsCheckbox: (isCheckbox: boolean) => void;
  metadata: CustomerCollectionMetadata | null;
  collectionsToRestore?: Array<RestoreName>;
  dbsToRestore?: Array<RestoreName>;
  setCollectionsToRestore?: (collections: Array<RestoreName>) => void;
  setDbsToRestore?: (dbs: Array<RestoreName>) => void;
  textAreaValidationError: string | null;
  setTextAreaValidationError: (error: string | null) => void;

  // PIT props (all optional, only present if isPit)
  maxPitRangeI: number;
  maxPitRangeTs: number;
  minPitRangeTs: number;
  onChangeOplogInc: (_: string) => void;
  onChangeOplogTs: (_: string) => void;
  onChangePitDate: (_: string) => void;
  onChangePitOption: (_: string) => void;
  onChangePitTime: (_: string) => void;
  onClearSnapshot: () => void;
  pitDate: string;
  pitOplogInc: string;
  pitOplogTs: string;
  pitOptionValue: string;
  pitRanges: Array<BSONTimestampRange>;
  pitTimeUTC: string;
  pitRangeError: string;
  pitSnapshotError: string;
  pitValidationError: string;
  setPitValidationError: (error: string) => void;
}

function SnapshotRestoreSourceStep({
  clusterName,
  groupName,
  isPit,
  snapshotDate,
  snapshot,
  onFullSnapshotRestore,
  useFullSnapshot,
  onNoOverwriteStrategy,
  noOverwrite,
  isCheckbox,
  setIsCheckbox,
  metadata,
  collectionsToRestore,
  dbsToRestore,
  setCollectionsToRestore,
  setDbsToRestore,
  textAreaValidationError,
  setTextAreaValidationError,
  maxPitRangeI,
  maxPitRangeTs,
  minPitRangeTs,
  onChangeOplogInc,
  onChangeOplogTs,
  onChangePitDate,
  onChangePitOption,
  onChangePitTime,
  onClearSnapshot,
  pitDate,
  pitOplogInc,
  pitOplogTs,
  pitOptionValue,
  pitRanges,
  pitTimeUTC,
  pitRangeError,
  pitSnapshotError,
  pitValidationError,
  setPitValidationError,
}: SnapshotRestoreSourceStepProps) {
  // Scroll to top when component mounts
  useEffect(() => {
    const body = getPageScrollContainer();
    if (body) {
      body.scrollTop = 0;
    }
  }, []);

  const makeRestoreName = (name: string): RestoreName => ({
    sourceName: name,
    targetName: name,
  });

  const [searchValue, setSearchValue] = useState('');
  const [expandedDatabases, setExpandedDatabases] = useState<Set<string>>(new Set());

  const dbStructure = useMemo(() => {
    const structure: { [database: string]: Array<string> } = {};
    const namespaceStats = metadata?.namespaceStats || {};

    Object.keys(namespaceStats).forEach((namespace) => {
      const separatorIndex = namespace.indexOf('.');
      const database = namespace.slice(0, separatorIndex);
      const collection = namespace.slice(separatorIndex + 1);
      if (!structure[database]) structure[database] = [];
      structure[database].push(collection);
    });

    return structure;
  }, [metadata?.namespaceStats]);

  const isSelected = (item: string) => {
    if (item.includes('.')) {
      // Collection: check if individually selected OR if its database is selected
      const database = item.split('.')[0];
      return (
        collectionsToRestore?.some((c) => c.sourceName === item) ||
        dbsToRestore?.some((db) => db.sourceName === database)
      );
    } else {
      // Database: check if database is selected OR if all its collections are selected
      if (dbsToRestore?.some((db) => db.sourceName === item)) {
        return true;
      }
      // Check if all collections in this database are individually selected
      const collections = dbStructure[item] || [];
      return (
        collections.length > 0 &&
        collections.every((col) => collectionsToRestore?.some((c) => c.sourceName === `${item}.${col}`))
      );
    }
  };

  const toggleSelection = (item: string, checked: boolean) => {
    if (item.includes('.')) {
      // Collection toggle
      const database = item.split('.')[0];

      if (checked) {
        // Add collection, remove database if selected
        setDbsToRestore?.((dbsToRestore || []).filter((db) => db.sourceName !== database));
        const newCollectionsToRestore = [...(collectionsToRestore || []), makeRestoreName(item)];
        setCollectionsToRestore?.(newCollectionsToRestore);

        // Check if all collections in this database are now selected
        const allCollectionsInDb = dbStructure[database] || [];
        const selectedCollectionsInDb = newCollectionsToRestore.filter((c) => c.sourceName.startsWith(database + '.'));

        if (selectedCollectionsInDb.length === allCollectionsInDb.length) {
          // All collections selected, convert to database selection
          setDbsToRestore?.([
            ...(dbsToRestore || []).filter((db) => db.sourceName !== database),
            makeRestoreName(database),
          ]);
          setCollectionsToRestore?.(newCollectionsToRestore.filter((c) => !c.sourceName.startsWith(database + '.')));
        }
      } else {
        // Remove collection
        if (dbsToRestore?.some((db) => db.sourceName === database)) {
          // Convert database selection to individual collections, excluding this one
          const otherCollections = (dbStructure[database] || [])
            .filter((col) => col !== item.split('.')[1])
            .map((col) => makeRestoreName(`${database}.${col}`));

          setDbsToRestore?.((dbsToRestore || []).filter((db) => db.sourceName !== database));
          setCollectionsToRestore?.([
            ...(collectionsToRestore || []).filter((c) => !c.sourceName.startsWith(database + '.')),
            ...otherCollections,
          ]);
        } else {
          // Just remove the collection
          setCollectionsToRestore?.((collectionsToRestore || []).filter((c) => c.sourceName !== item));
        }
      }
    } else {
      // Database toggle
      if (checked) {
        // Add database, remove individual collections from this database
        setDbsToRestore?.([...(dbsToRestore || []), makeRestoreName(item)]);
        setCollectionsToRestore?.((collectionsToRestore || []).filter((c) => !c.sourceName.startsWith(item + '.')));
      } else {
        // Remove database - handle both explicit database selection and all-collections-selected case
        setDbsToRestore?.((dbsToRestore || []).filter((db) => db.sourceName !== item));

        // Also remove all individual collections from this database
        setCollectionsToRestore?.((collectionsToRestore || []).filter((c) => !c.sourceName.startsWith(item + '.')));
      }
    }
  };

  // Filtered data for search
  const filteredDbStructure = useMemo(() => {
    if (!searchValue) return dbStructure;
    const filtered: { [database: string]: Array<string> } = {};
    const searchLower = searchValue.toLowerCase();

    Object.entries(dbStructure).forEach(([database, collections]) => {
      const dbMatches = database.toLowerCase().includes(searchLower);
      const matchingCollections = collections.filter(
        (col) => col.toLowerCase().includes(searchLower) || `${database}.${col}`.toLowerCase().includes(searchLower)
      );
      if (dbMatches || matchingCollections.length > 0) {
        filtered[database] = dbMatches ? collections : matchingCollections;
      }
    });
    return filtered;
  }, [dbStructure, searchValue]);

  // Auto-expand on search, collapse when search is cleared
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    if (value) {
      // Auto-expand databases with matching collections
      setExpandedDatabases((prev) => {
        const newExpanded = new Set(prev);
        Object.entries(dbStructure).forEach(([database, collections]) => {
          if (
            collections.some(
              (col) =>
                col.toLowerCase().includes(value.toLowerCase()) ||
                `${database}.${col}`.toLowerCase().includes(value.toLowerCase())
            )
          ) {
            newExpanded.add(database);
          }
        });
        return newExpanded;
      });
    } else {
      // Clear search - return to default collapsed state
      setExpandedDatabases(new Set());
    }
  };

  // Highlight search matches
  const highlightText = (text: string) => {
    if (!searchValue) return text;
    const regex = new RegExp(`(${searchValue})`, 'gi');
    const parts = text.split(regex);
    return <span>{parts.map((part, i) => (regex.test(part) ? <strong key={i}>{part}</strong> : part))}</span>;
  };

  // Database row component
  const DatabaseRow = ({ database, collections }: { database: string; collections: Array<string> }) => {
    const isExpanded = expandedDatabases.has(database);
    const isDatabaseSelected = dbsToRestore?.some((db) => db.sourceName === database);

    const checked = isSelected(database);

    // Count selected collections (only individual ones, not from database selection)
    const selectedCollectionCount = collections.filter((col) =>
      collectionsToRestore?.some((c) => c.sourceName === `${database}.${col}`)
    ).length;

    const indeterminate =
      !isDatabaseSelected && selectedCollectionCount > 0 && selectedCollectionCount < collections.length;

    const toggleExpanded = () =>
      setExpandedDatabases((prev) => {
        const next = new Set(prev);
        if (isExpanded) {
          next.delete(database);
        } else {
          next.add(database);
        }
        return next;
      });

    return (
      <div>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
          {collections.length > 0 && (
            <button
              style={{ marginRight: '8px', border: 'none', background: 'none', cursor: 'pointer' }}
              onClick={toggleExpanded}
            >
              <Icon glyph={isExpanded ? 'ChevronDown' : 'ChevronRight'} size="small" />
            </button>
          )}
          <Checkbox
            checked={checked}
            indeterminate={indeterminate}
            onChange={(e) => toggleSelection(database, e.target.checked)}
            label={<>{highlightText(database)}</>}
            value={database}
          />
        </div>

        {isExpanded && (
          <div style={{ marginLeft: '58px', marginBottom: '8px' }}>
            {collections.map((collection) => (
              <Checkbox
                key={collection}
                checked={isSelected(`${database}.${collection}`)}
                onChange={(e) => toggleSelection(`${database}.${collection}`, e.target.checked)}
                label={highlightText(collection)}
                value={`${database}.${collection}`}
                style={{ marginBottom: '4px' }}
              />
            ))}
          </div>
        )}
      </div>
    );
  };

  const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (!metadata || !metadata.namespaceStats) return;
    const value = e.target.value;
    const namespaceKeys = Object.keys(metadata.namespaceStats);

    const entries = value
      .split('\n')
      .map((item) => item.trim())
      .filter((item) => item);

    const validCollections: Array<RestoreName> = [];
    const validDbs: Array<RestoreName> = [];
    const invalidEntries: Array<string> = [];

    for (const entry of entries) {
      if (entry.includes('.')) {
        // for non pit validate namespace exists in metadata
        if (isPit || namespaceKeys.includes(entry)) {
          validCollections.push(makeRestoreName(entry));
        } else {
          invalidEntries.push(entry);
        }
      } else {
        // for non pit validate if any db exists in metadata
        if (isPit || namespaceKeys.some((ns) => ns.startsWith(entry + '.'))) {
          validDbs.push(makeRestoreName(entry));
        } else {
          invalidEntries.push(entry);
        }
      }
    }

    // Set validation error if there are invalid entries
    if (invalidEntries.length > 0) {
      setTextAreaValidationError(`Invalid database(s)/collection(s): ${invalidEntries.join(', ')}`);
    } else {
      setTextAreaValidationError(null);
    }

    setCollectionsToRestore?.(validCollections);
    setDbsToRestore?.(validDbs);
  };

  const getPitTable = () => {
    return (
      <SnapshotRestoreSourcePitStep
        maxPitRangeI={maxPitRangeI}
        maxPitRangeTs={maxPitRangeTs}
        minPitRangeTs={minPitRangeTs}
        onChangeOplogInc={onChangeOplogInc}
        pitDate={pitDate}
        pitRanges={pitRanges}
        pitOplogInc={pitOplogInc}
        pitOplogTs={pitOplogTs}
        pitOptionValue={pitOptionValue}
        pitTimeUTC={pitTimeUTC}
        pitRangeError={pitRangeError}
        pitSnapshotError={pitSnapshotError}
        pitValidationError={pitValidationError}
        setPitValidationError={setPitValidationError}
        onChangeOplogTs={onChangeOplogTs}
        onChangePitDate={onChangePitDate}
        onChangePitOption={onChangePitOption}
        onChangePitTime={onChangePitTime}
        onClearSnapshot={onClearSnapshot}
      ></SnapshotRestoreSourcePitStep>
    );
  };

  return (
    <div data-testid="source-step">
      <Card data-testid="source-card">
        <h3>Source</h3>
        <Table>
          <TableHead>
            <HeaderRow>
              <HeaderCell>Source Project</HeaderCell>
              <HeaderCell>Source Cluster</HeaderCell>
              {!isPit && <HeaderCell>Snapshot</HeaderCell>}
            </HeaderRow>
          </TableHead>
          <TableBody>
            <Row>
              <Cell>{groupName}</Cell>
              <Cell>{clusterName}</Cell>
              {!isPit && <Cell>{formatDateTime(snapshotDate)}</Cell>}
            </Row>
          </TableBody>
        </Table>
        {isPit && getPitTable()}
        {snapshot != null && (
          <ExpandableCard className={styles.expandableCard} title="SNAPSHOT DETAILS" defaultOpen={true}>
            {getSnapshotDetails(snapshot)}
          </ExpandableCard>
        )}
      </Card>
      <div className={styles.content}>
        <Card>
          <h3>Source Details</h3>
          <label>Restore Data</label>
          <RadioBoxGroup
            data-testid="restore-options"
            name="restoreOptions"
            size="full"
            value={useFullSnapshot ? 'entireSnapshot' : 'collectionLevel'}
            onChange={({ target: { value } }) => onFullSnapshotRestore(value === 'entireSnapshot')}
          >
            <RadioBox data-testid="entire-snapshot-option" value="entireSnapshot" checked={useFullSnapshot}>
              Restore entire snapshot
            </RadioBox>
            {!metadata || !metadata.namespaceStats || metadata.namespaceStats.size === 0 ? (
              <Tooltip
                align="top"
                justify="middle"
                trigger={
                  <RadioBox
                    data-testid="collection-level-disabled-option"
                    value="collectionLevel"
                    checked={!useFullSnapshot}
                    disabled={true}
                  >
                    Restore database(s)/collection(s)
                  </RadioBox>
                }
              >
                Snapshot selected lacks database and collection level metadata. Please select a different snapshot to
                perform this type of restore.
              </Tooltip>
            ) : (
              <RadioBox
                data-testid="collection-level-option"
                value="collectionLevel"
                checked={!useFullSnapshot}
                disabled={false}
              >
                Restore database(s)/collection(s)
              </RadioBox>
            )}
          </RadioBoxGroup>
          {!useFullSnapshot && (
            <>
              <label>Write Strategy</label>
              <RadioBoxGroup
                name="writeStrategy"
                size="full"
                value={noOverwrite ? 'noOverwrite' : 'overwriteIfExists'}
                onChange={({ target: { value } }) => onNoOverwriteStrategy(value === 'noOverwrite')}
              >
                <RadioBox value="noOverwrite" checked={noOverwrite}>
                  Add as new
                </RadioBox>
                <RadioBox value="overwriteIfExists" checked={!noOverwrite}>
                  Overwrite destination
                </RadioBox>
              </RadioBoxGroup>
              <br></br>
              <label>Select Restore Data</label>
              <p>
                {' '}
                Select the data you would like to restore.
                {isPit
                  ? 'The checkboxes display the data from the snapshot closest to your chosen point in time. '
                  : 'The checkboxes display the data from your snapshot. '}
                For managing thousands of collections, use the 'List Entry' editor or API.
              </p>
              <Banner variant="info">
                Views and time-series collections are not supported for database/collection level restores.
              </Banner>
              <br></br>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <SegmentedControl
                  defaultValue={isCheckbox ? 'checkbox' : 'list'}
                  onChange={(value: string) => {
                    setIsCheckbox(value === 'checkbox');
                    setCollectionsToRestore?.([]);
                    setDbsToRestore?.([]);
                    setTextAreaValidationError(null);
                  }}
                >
                  <SegmentedControlOption value="checkbox">CHECKBOX ENTRY</SegmentedControlOption>
                  <SegmentedControlOption value="list">LIST ENTRY</SegmentedControlOption>
                </SegmentedControl>
                <Tooltip
                  justify="middle"
                  triggerEvent="hover"
                  trigger={
                    <span>
                      <Icon glyph="InfoWithCircle" size="small" />
                    </span>
                  }
                >
                  <strong>Important note:</strong> Selected source data will be cleared by switching between entry
                  modes.
                </Tooltip>
              </div>
              <br></br>
              <label>Source Database(s)/Collection(s)</label>
              {!isCheckbox && (
                <Body>
                  <div style={{ marginBottom: '8px' }}>
                    List each database or database.collection on a separate line.
                  </div>
                </Body>
              )}

              {isCheckbox ? (
                <>
                  <SearchInput
                    aria-label="Search databases or collections"
                    placeholder="Search databases or collections"
                    value={searchValue}
                    onChange={(event) => handleSearchChange(event.target.value)}
                  />
                  {Object.keys(dbStructure).length > 0 && (
                    <div style={{ marginTop: '16px' }}>
                      <Checkbox
                        checked={Object.keys(dbStructure).every((db) => isSelected(db))}
                        indeterminate={(() => {
                          const hasSelection =
                            (dbsToRestore?.length || 0) > 0 || (collectionsToRestore?.length || 0) > 0;
                          const allSelected = Object.keys(dbStructure).every((db) => isSelected(db));
                          return hasSelection && !allSelected;
                        })()}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setDbsToRestore?.(Object.keys(dbStructure).map(makeRestoreName));
                            setCollectionsToRestore?.([]);
                          } else {
                            setDbsToRestore?.([]);
                            setCollectionsToRestore?.([]);
                          }
                        }}
                        label="Select All"
                        value="select-all"
                      />
                      <div style={{ maxHeight: '250px', overflowY: 'auto' }}>
                        {Object.entries(filteredDbStructure).map(([database, collections]) => (
                          <DatabaseRow key={database} database={database} collections={collections} />
                        ))}
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <>
                  <TextArea
                    aria-labelledby="list"
                    placeholder="//database or database.collection"
                    rows={20}
                    defaultValue={[
                      ...(dbsToRestore?.map((db) => db.sourceName) || []),
                      ...(collectionsToRestore?.map((col) => col.sourceName) || []),
                    ].join('\n')}
                    onChange={handleTextAreaChange}
                    state={textAreaValidationError ? 'error' : 'none'}
                    errorMessage={textAreaValidationError || undefined}
                  ></TextArea>
                </>
              )}
            </>
          )}
        </Card>
      </div>
    </div>
  );
}

export default SnapshotRestoreSourceStep;
