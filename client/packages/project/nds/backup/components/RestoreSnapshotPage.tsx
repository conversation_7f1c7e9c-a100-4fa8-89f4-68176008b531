import { useEffect, useState } from 'react';

import { css } from '@emotion/css';
import Banner from '@leafygreen-ui/banner';
import Button from '@leafygreen-ui/button';
import { palette } from '@leafygreen-ui/palette';
import Stepper, { Step } from '@leafygreen-ui/stepper';
import { H2 } from '@leafygreen-ui/typography';

import { RestoreName } from '@packages/types/nds/backup/systemClusterJob';
import {
  BackupSnapshot,
  BackupType,
  CollectionMetadataConfigStatus,
  EncryptionAtRestProvider,
} from '@packages/types/nds/backupSnapshot';
import { CustomerCollectionMetadata } from '@packages/types/nds/customerCollectionMetadata';
import { SettingsModel } from '@packages/types/settingsModel';

import * as api from '@packages/common/services/api';
import { exceptionToMessage } from '@packages/common/services/errorHelper';
import { headerZIndex } from '@packages/common/styles/layoutStyles';
import { navigateTo } from '@packages/common/utils/navigateTo';
import mongoDate from '@packages/date';
import { getPageScrollContainer } from '@packages/layout/pageScrollContainer';

import SnapshotRestoreConfirmStep from './SnapshotRestoreConfirmStep';
import SnapshotRestoreDestinationStep from './SnapshotRestoreDestinationStep';
import SnapshotRestoreSourceStep from './SnapshotRestoreSourceStep';

enum RestoreStep {
  SOURCE,
  DESTINATION,
  REVIEW_CONFIRM = 2,
}

const STEP_INFO: Record<RestoreStep, { title: string; nextButtonText: string }> = {
  [RestoreStep.SOURCE]: {
    title: 'Source',
    nextButtonText: 'Next',
  },
  [RestoreStep.DESTINATION]: {
    title: 'Destination',
    nextButtonText: 'Next',
  },
  [RestoreStep.REVIEW_CONFIRM]: {
    title: 'Review & Confirm',
    nextButtonText: 'Start Restore',
  },
};

const styles = {
  modal: css`
    z-index: ${headerZIndex + 1};
    max-width: 960px;
  `,
  header: css`
    margin-bottom: 24px;
    color: ${palette.black};
  `,
  stepperContainer: css`
    margin-bottom: 24px;
  `,
  footer: css`
    margin-top: 36px;
    display: flex;
    justify-content: space-between;
  `,
  buttonGroup: css`
    margin-left: auto;
    display: flex;
    gap: 8px;
  `,
  banner: css`
    margin-top: 12px;
  `,
  restoreListContainer: css`
    margin-bottom: 24px;
  `,
};

export interface RestoreSnapshotPageProps {
  clusterUniqueId: string;
  clusterName: string;
  groupId: string;
  isPit: boolean;
  snapshotId: string;
  settingsModel: SettingsModel;
  isRetained: boolean;
}

export default function RestoreSnapshotPage(props: RestoreSnapshotPageProps) {
  const { clusterUniqueId, clusterName, groupId, isPit, snapshotId, settingsModel, isRetained } = props;

  const orgId = settingsModel.getCurrentOrgId();
  const groupName = settingsModel.getCurrentGroupName();

  // Scroll to top when component mounts
  useEffect(() => {
    const body = getPageScrollContainer();
    if (body) {
      body.scrollTop = 0;
    }
  }, []);

  const [snapshot, setSnapshot] = useState<BackupSnapshot | null>(null);
  const [currentSnapshotId, setCurrentSnapshotId] = useState<string>(snapshotId);

  const [snapshotDate, setSnapshotDate] = useState<string>('');
  const [useFullSnapshot, setUseFullSnapshot] = useState<boolean>(true);
  const [noOverwrite, setNoOverwrite] = useState<boolean>(true);
  const [isCheckbox, setIsCheckbox] = useState<boolean>(true);
  const [metadata, setMetadata] = useState<CustomerCollectionMetadata | null>(null);
  const [collectionsToRestore, setCollectionsToRestore] = useState<Array<RestoreName>>([]);
  const [dbsToRestore, setDbsToRestore] = useState<Array<RestoreName>>([]);
  const [indexOption, setIndexOption] = useState('');
  const [failureProcedure, setFailureProcedure] = useState('');
  const [textAreaValidationError, setTextAreaValidationError] = useState<string | null>(null);

  const [snapshotHasSearchIndex, setSnapshotHasSearchIndex] = useState(false);
  const [isResilient, setIsResilient] = useState(false);
  const [isSnapshotToRestoreSharded, setIsSnapshotToRestoreSharded] = useState(false);
  const [snapshotEncrypted, setSnapshotEncrypted] = useState(false);
  const [snapshotEncryptionType, setSnapshotEncryptionType] = useState(EncryptionAtRestProvider.NONE);

  const [destinationGroupId, setDestinationGroupId] = useState(groupId);
  const [destinationClusterName, setDestinationClusterName] = useState('');
  const [destinationGroupName, setDestinationGroupName] = useState(groupName);

  const [userAgreedToRestore, setUserAgreedToRestore] = useState(false);
  const [restoreDisabled, setRestoreDisabled] = useState(false);

  // Add PIT state if needed
  const [pitOptionValue, setPitOptionValue] = useState('datetime');
  const [pitDate, setPitDate] = useState('');
  const [pitTimeUTC, setPitTimeUTC] = useState('');
  const [pitOplogTs, setPitOplogTs] = useState('');
  const [pitOplogInc, setPitOplogInc] = useState('');
  const [maxPitRangeTs, setMaxPitRangeTs] = useState(0);
  const [minPitRangeTs, setMinPitRangeTs] = useState(0);
  const [maxPitRangeI, setMaxPitRangeI] = useState(0);
  const [pitRanges, setPitRanges] = useState([]);
  const [pitRangeError, setPitRangeError] = useState('');
  const [pitSnapshotError, setPitSnapshotError] = useState('');
  const [pitValidationError, setPitValidationError] = useState('');
  const [pitUTCSeconds, setPitUTCSeconds] = useState<number | null>(null);

  // Fetch PIT ranges if isPit
  useEffect(() => {
    if (isPit) {
      api.nds.backup
        .getPitRestoreRange(groupId, clusterUniqueId)
        .then((range) => {
          if (range.start == null || range.end == null) {
            setMaxPitRangeTs(0);
            setMinPitRangeTs(0);
            setMaxPitRangeI(0);
            setPitRangeError(
              'Could not find a base snapshot to perform a point in time restore. Please try again later.'
            );
          } else {
            setMaxPitRangeTs(range.end.T);
            setMinPitRangeTs(range.start.T);
            setMaxPitRangeI(range.end.I);
            // @ts-ignore
            setPitRanges(range.pitRanges ? range.pitRanges : []);
          }
        })
        .catch((e) => {
          setPitRangeError(exceptionToMessage(e));
        });
    }
  }, [isPit, groupId, clusterUniqueId]);

  const getChosenPitUTCSeconds = (pitOptionValue: string, pitDate: string, pitTimeUTC: string) => {
    if (pitOptionValue !== 'datetime') {
      return null;
    }

    const pitUTCDate = mongoDate(`${pitDate} ${pitTimeUTC} +0000`, 'MM/DD/YYYY HH:mm ZZ');
    if (!pitUTCDate.isValid()) {
      return -1;
    }

    return pitUTCDate.unix();
  };

  const getChosenPitOplogTs = (pitOptionValue: string, pitOplogTs: string) => {
    return pitOptionValue === 'optime' ? pitOplogTs : null;
  };

  const getChosenPitOplogInc = (pitOptionValue: string, pitOplogInc: string) => {
    return pitOptionValue === 'optime' ? pitOplogInc : null;
  };

  useEffect(() => {
    if (!isPit) {
      return;
    }

    const hasDateTimeFields = pitOptionValue === 'datetime' && pitDate && pitTimeUTC;
    const hasOplogFields = pitOptionValue === 'optime' && pitOplogTs && pitOplogInc;

    if (!hasDateTimeFields && !hasOplogFields) {
      return;
    }

    // Don't fetch if there's a validation error
    if (pitValidationError) {
      return;
    }

    const pitUTCSecondsValue = getChosenPitUTCSeconds(pitOptionValue, pitDate, pitTimeUTC);
    const pitOplogTsValue = getChosenPitOplogTs(pitOptionValue, pitOplogTs);
    const pitOplogIncValue = getChosenPitOplogInc(pitOptionValue, pitOplogInc);
    api.nds.backup
      .getBaseSnapshotForPit(groupId, clusterUniqueId, pitUTCSecondsValue, pitOplogTsValue, pitOplogIncValue)
      .then((snap) => {
        setSnapshot(snap);
        setCurrentSnapshotId(snap.id);
        setPitUTCSeconds(pitUTCSecondsValue);
        setPitSnapshotError(''); // Clear any previous snapshot errors on success
      })
      .catch((error) => {
        setPitSnapshotError(exceptionToMessage(error));
        setSnapshot(null);
        setCurrentSnapshotId('');
        setPitUTCSeconds(null);
      });
  }, [
    isPit,
    groupId,
    clusterUniqueId,
    pitDate,
    pitTimeUTC,
    pitOplogTs,
    pitOplogInc,
    pitOptionValue,
    pitValidationError,
  ]);

  const onFullSnapshotRestore = (useFull: boolean) => {
    setUseFullSnapshot(useFull);
    setUserAgreedToRestore(!useFull && noOverwrite);
  };

  const onNoOverwriteStrategy = (noOverwrite: boolean) => {
    setNoOverwrite(noOverwrite);
    //if no overwrite, set setUserAgreedToRestore to true as they don't need to confirm overwriting data
    setUserAgreedToRestore(!useFullSnapshot && noOverwrite);
  };

  const handleIndexOption = (value: string) => {
    setIndexOption(value);
  };

  const handleFailureOption = (value: string) => {
    setFailureProcedure(value);
  };

  const [currentStep, setCurrentStep] = useState(RestoreStep.SOURCE);
  const [error, setError] = useState('');

  useEffect(() => {
    api.nds.backup.getSnapshot(groupId, currentSnapshotId).then((backupSnapshot) => {
      setSnapshot(backupSnapshot);
      setSnapshotDate(backupSnapshot.creationDate.toString());
      setSnapshotHasSearchIndex(
        !!backupSnapshot.searchIndexDefId && settingsModel.hasProjectFeature('CPS_RESTORE_SEARCH_INDEX')
      );
      setIsResilient(backupSnapshot.type === BackupType.FALLBACK);
      setIsSnapshotToRestoreSharded(!!backupSnapshot.members && backupSnapshot.members.length > 0);
      setSnapshotEncrypted(
        !!backupSnapshot.encryptionAtRestProvider && backupSnapshot.encryptionAtRestProvider !== 'NONE'
      );
      setSnapshotEncryptionType(backupSnapshot.encryptionAtRestProvider || EncryptionAtRestProvider.NONE);
    });
  }, [groupId, currentSnapshotId, settingsModel]);

  useEffect(() => {
    if (
      snapshot &&
      snapshot.collectionMetadataConfig &&
      snapshot.collectionMetadataConfig.status === CollectionMetadataConfigStatus.COMPLETED
    ) {
      api.nds.backup.getCollectionMetadata(groupId, snapshot.id).then((metadata) => {
        setMetadata(metadata);
      });
    } else {
      setMetadata(null);
    }
  }, [snapshot, groupId]);

  const validateCollectionRestoreJob = async (): Promise<boolean> => {
    try {
      const cluster = await api.nds.clusterDescriptions.getClusterDescription(
        destinationGroupId,
        destinationClusterName
      );
      await api.nds.backup.validateCollectionRestoreJob(
        groupId,
        clusterUniqueId,
        currentSnapshotId,
        destinationGroupId,
        destinationClusterName,
        pitUTCSeconds,
        pitOplogTs,
        pitOplogInc,
        cluster.uniqueId,
        cluster.mongoDBVersion,
        cluster.fixedMongoDBFCV,
        indexOption,
        failureProcedure,
        noOverwrite ? 'NO_OVERWRITE' : 'OVERWRITE_IF_EXISTS',
        dbsToRestore,
        collectionsToRestore
      );
      setError('');
      return true;
    } catch (err) {
      setError(exceptionToMessage(err));
      return false;
    }
  };

  const onCancel = async () => {
    if (isRetained) {
      navigateTo(`#/security/backup/${clusterName}`);
    } else {
      navigateTo(`#/clusters/backup/${clusterName}`);
    }
  };

  const onNext = async () => {
    switch (currentStep) {
      case RestoreStep.SOURCE: {
        setCurrentStep(RestoreStep.DESTINATION);
        break;
      }
      case RestoreStep.DESTINATION: {
        if (useFullSnapshot) {
          setError('');
          setCurrentStep(RestoreStep.REVIEW_CONFIRM);
          break;
        } else {
          const isValid = await validateCollectionRestoreJob();
          if (isValid) {
            setCurrentStep(RestoreStep.REVIEW_CONFIRM);
          }
          break;
        }
      }
      case RestoreStep.REVIEW_CONFIRM: {
        break;
      }
      default:
        throw new Error(`unknown RestoreStep ${currentStep}`);
    }
  };

  const onBack = () => {
    setCurrentStep(currentStep - 1);
  };

  const onConfirm = async () => {
    try {
      if (useFullSnapshot) {
        await api.nds.backup.performAutomatedRestore(
          groupId,
          currentSnapshotId,
          clusterName,
          destinationGroupId,
          destinationClusterName,
          isPit,
          pitUTCSeconds,
          pitOplogTs,
          pitOplogInc
        );
      } else {
        const cluster = await api.nds.clusterDescriptions.getClusterDescription(
          destinationGroupId,
          destinationClusterName
        );
        await api.nds.backup.performCollectionRestore(
          groupId,
          clusterUniqueId,
          currentSnapshotId,
          destinationGroupId,
          destinationClusterName,
          pitUTCSeconds,
          pitOplogTs,
          pitOplogInc,
          cluster.uniqueId,
          cluster.mongoDBVersion,
          cluster.fixedMongoDBFCV,
          indexOption,
          failureProcedure,
          noOverwrite ? 'NO_OVERWRITE' : 'OVERWRITE_IF_EXISTS',
          dbsToRestore,
          collectionsToRestore
        );
      }
      setRestoreDisabled(true);
      navigateTo(`#/clusters/backup/${clusterName}/restoreHistory`);
    } catch (err) {
      setError(exceptionToMessage(err));
    }
  };

  const onUserAgreementInput = (value: string, phrase: string) => {
    setUserAgreedToRestore(value === phrase);
  };

  const onClearSnapshot = () => {
    setSnapshot(null);
    setCurrentSnapshotId('');
  };

  // Handler for PIT option changes - clears PIT fields and snapshot
  const handlePitOptionChange = (newPitOption: string) => {
    setPitOptionValue(newPitOption);
    setPitDate('');
    setPitTimeUTC('');
    setPitOplogTs('');
    setPitOplogInc('');
    setPitSnapshotError('');
    setPitValidationError('');
    onClearSnapshot();
  };

  // Handler for PIT date/time changes - clears snapshot for refetch and validation
  const handlePitDateChange = (newDate: string) => {
    setPitDate(newDate);
    onClearSnapshot();
  };

  const handlePitTimeChange = (newTime: string) => {
    setPitTimeUTC(newTime);
    onClearSnapshot();
  };

  // Handler for oplog changes - clears snapshot for refetch and validation
  const handleOplogTsChange = (newOplogTs: string) => {
    setPitOplogTs(newOplogTs);
    onClearSnapshot();
  };

  const handleOplogIncChange = (newOplogInc: string) => {
    setPitOplogInc(newOplogInc);
    onClearSnapshot();
  };

  const continueDisabled = (() => {
    if (!snapshot) {
      return true;
    }

    if (currentStep === RestoreStep.SOURCE && !useFullSnapshot) {
      return (!dbsToRestore.length && !collectionsToRestore.length) || textAreaValidationError !== null;
    }
    if (currentStep === RestoreStep.DESTINATION) {
      // Always require destination cluster name
      if (!destinationClusterName) {
        return true;
      }

      // For collection restore, also require index option and failure procedure
      if (!useFullSnapshot) {
        return !indexOption || !failureProcedure;
      }

      return false;
    }

    return false;
  })();

  return (
    <div className={styles.modal} data-testid="restore-snapshot-page">
      <H2 className={styles.header} data-testid="page-title">
        {' '}
        {isPit ? 'Point in Time Restore' : 'Snapshot Restore'}
      </H2>
      <div className={styles.stepperContainer} data-testid="stepper-container">
        <Stepper currentStep={currentStep} data-testid="stepper">
          <Step data-testid="source-step-label">{STEP_INFO[RestoreStep.SOURCE].title}</Step>
          <Step data-testid="destination-step-label">{STEP_INFO[RestoreStep.DESTINATION].title}</Step>
          <Step data-testid="confirm-step-label">{STEP_INFO[RestoreStep.REVIEW_CONFIRM].title}</Step>
        </Stepper>
      </div>

      {currentStep === RestoreStep.SOURCE && (
        <SnapshotRestoreSourceStep
          clusterName={clusterName}
          groupName={groupName}
          isPit={isPit}
          snapshotDate={snapshotDate}
          snapshot={snapshot}
          onFullSnapshotRestore={onFullSnapshotRestore}
          useFullSnapshot={useFullSnapshot}
          maxPitRangeI={maxPitRangeI}
          maxPitRangeTs={maxPitRangeTs}
          minPitRangeTs={minPitRangeTs}
          onChangeOplogInc={handleOplogIncChange}
          onChangeOplogTs={handleOplogTsChange}
          onChangePitOption={handlePitOptionChange}
          onChangePitTime={handlePitTimeChange}
          onChangePitDate={handlePitDateChange}
          onClearSnapshot={onClearSnapshot}
          pitOptionValue={pitOptionValue}
          pitDate={pitDate}
          pitRanges={pitRanges}
          pitOplogTs={pitOplogTs}
          pitTimeUTC={pitTimeUTC}
          pitOplogInc={pitOplogInc}
          pitRangeError={pitRangeError}
          pitSnapshotError={pitSnapshotError}
          pitValidationError={pitValidationError}
          setPitValidationError={setPitValidationError}
          onNoOverwriteStrategy={onNoOverwriteStrategy}
          noOverwrite={noOverwrite}
          isCheckbox={isCheckbox}
          setIsCheckbox={setIsCheckbox}
          metadata={metadata}
          collectionsToRestore={collectionsToRestore}
          dbsToRestore={dbsToRestore}
          setCollectionsToRestore={setCollectionsToRestore}
          setDbsToRestore={setDbsToRestore}
          textAreaValidationError={textAreaValidationError}
          setTextAreaValidationError={setTextAreaValidationError}
        />
      )}
      {currentStep === RestoreStep.DESTINATION && (
        <SnapshotRestoreDestinationStep
          clusterName={clusterName}
          groupId={groupId}
          orgId={orgId}
          isPit={isPit}
          snapshotId={currentSnapshotId}
          useFullSnapshot={useFullSnapshot}
          isResilient={isResilient}
          isSharded={isSnapshotToRestoreSharded}
          snapshotEncrypted={snapshotEncrypted}
          snapshotEncryptionType={snapshotEncryptionType}
          snapshotHasSearchIndex={snapshotHasSearchIndex}
          docsUrl={settingsModel.getDocsUrl()}
          handleIndexOption={handleIndexOption}
          handleFailureOption={handleFailureOption}
          indexOption={indexOption}
          failureProcedure={failureProcedure}
          chosenPitUTCSeconds={pitUTCSeconds}
          pitOplogTs={pitOplogTs}
          pitOplogInc={pitOplogInc}
          destinationClusterName={destinationClusterName}
          destinationGroupId={destinationGroupId}
          destinationGroupName={destinationGroupName}
          setDestinationClusterName={setDestinationClusterName}
          setDestinationGroupId={setDestinationGroupId}
          setDestinationGroupName={setDestinationGroupName}
          setUserAgreedToRestore={setUserAgreedToRestore}
          noOverwrite={noOverwrite}
        />
      )}
      {currentStep === RestoreStep.REVIEW_CONFIRM && (
        <SnapshotRestoreConfirmStep
          clusterName={clusterName}
          groupName={groupName}
          isPit={isPit}
          snapshotDate={snapshotDate}
          useFullSnapshot={useFullSnapshot}
          pitUTCSeconds={pitUTCSeconds}
          pitOplogTs={pitOplogTs}
          destinationClusterName={destinationClusterName}
          destinationGroupName={destinationGroupName}
          onUserAgreementInput={onUserAgreementInput}
          dbsToRestore={dbsToRestore}
          collectionsToRestore={collectionsToRestore}
          indexOption={indexOption}
          failureProcedure={failureProcedure}
          writeStrategy={noOverwrite ? 'NO_OVERWRITE' : 'OVERWRITE_IF_EXISTS'}
        />
      )}

      {error && (
        <Banner
          variant="danger"
          className={styles.banner}
          dismissible
          onClose={() => setError('')}
          data-testid="error-banner"
        >
          {error}
        </Banner>
      )}

      <div className={styles.footer} data-testid="footer">
        {currentStep !== RestoreStep.SOURCE && (
          <Button onClick={onBack} data-testid="back-button">
            Back
          </Button>
        )}
        <div className={styles.buttonGroup} data-testid="button-group">
          <Button onClick={onCancel} data-testid="cancel-button">
            {' '}
            Cancel{' '}
          </Button>

          {currentStep !== RestoreStep.REVIEW_CONFIRM && (
            <Button onClick={onNext} variant="primary" disabled={continueDisabled} data-testid="continue-button">
              Continue
            </Button>
          )}
          {currentStep === RestoreStep.REVIEW_CONFIRM && (
            <Button
              onClick={onConfirm}
              variant="primary"
              disabled={!userAgreedToRestore || restoreDisabled}
              data-testid="start-restore-button"
            >
              Start Restore
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
