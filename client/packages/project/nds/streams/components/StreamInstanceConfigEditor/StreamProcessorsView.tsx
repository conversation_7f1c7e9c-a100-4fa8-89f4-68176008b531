import { use<PERSON><PERSON>back, useEffect, useState } from 'react';

import Banner, { Variant as BannerVariant } from '@leafygreen-ui/banner';
import Button, { Variant } from '@leafygreen-ui/button';
import Card from '@leafygreen-ui/card';
import Code from '@leafygreen-ui/code';
import { Variant as ConfirmationModalVariant } from '@leafygreen-ui/confirmation-modal';
import { PageLoader } from '@leafygreen-ui/loading-indicator';
import { OrderedList, OrderedListItem } from '@leafygreen-ui/ordered-list';
// eslint-disable-next-line no-restricted-imports
import { Table, TableHeader } from '@leafygreen-ui/table-legacy';
import { Body, H3, Link, Subtitle } from '@leafygreen-ui/typography';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom-v5-compat';

import { WindowLocation } from '@packages/types/browser';
import {
  ConnectionRegistrySample,
  ConnectionRegistryType,
  SAMPLE_CONNECTION_NAMES,
  SPIStage,
  StreamProcessor,
  StreamProcessorDetails,
  StreamsConnectionReadOnly,
} from '@packages/types/nds/streams';
import { SettingsModel } from '@packages/types/settingsModel';

import { getStreamProcessors, loadStreamInstances, showAddConnectionToast } from '@packages/redux/common/nds/streams';

import * as api from '@packages/common/services/api';
import BrandIcon from '@packages/brand-icons/base-brand-icons';
import { exceptionToMessage } from '@packages/common/services/errorHelper';
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import Image from '@packages/components/Image';

import { pageRouteByView } from './StreamInstanceConfigEditor';
import StreamProcessorEmptyState from './StreamProcessorEmptyState';
import { StreamProcessorRow } from './StreamProcessorRow';
import {
  EmptyStateContainer,
  EmptyStateCTA,
  EmptyStateGraphic,
  EmptyStateText,
  EmptyStateWithGraphic,
  FlexColumn,
  FlexRow,
  FlexTab,
  StreamProcessorDeleteConfirmModal,
  StreamProcessorDeleteSucessToast,
  StreamsProcessorPipelineDetailsModal,
} from './styledComponents';
import { styles } from './styles';
import { MODAL_VIEWS, PageView, StreamProcessorAction, TestSelector } from './types';

interface StreamProcessorsViewProps {
  tenantName: string;
  isStreamsOwner: boolean;
  settingsModel: SettingsModel;
  connections: Array<StreamsConnectionReadOnly>;
  setFetchError: React.Dispatch<React.SetStateAction<string>>;
  setOperationError: React.Dispatch<React.SetStateAction<string>>;
  setSelectedSPForMonitoring: React.Dispatch<React.SetStateAction<StreamProcessor | undefined>>;
  addConnectionRegistryItem: () => void;
  activeModal: MODAL_VIEWS;
  setActiveModal: React.Dispatch<React.SetStateAction<MODAL_VIEWS>>;
  windowLocation?: WindowLocation;
}

export function StreamProcessorsView({
  tenantName,
  isStreamsOwner,
  settingsModel,
  connections,
  setFetchError,
  setOperationError,
  setSelectedSPForMonitoring,
  addConnectionRegistryItem,
  activeModal,
  setActiveModal,
}: StreamProcessorsViewProps) {
  const groupId = settingsModel.getCurrentGroupId();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const canViewStreamsInstance = isStreamsOwner || settingsModel.isDataAccessAny();
  const [processorToViewDetails, setProcessorToViewDetails] = useState<StreamProcessorDetails | null>(null);
  const [isLoadingProcessor, setLoadingProcessor] = useState(false);
  const [processorLoadError, setProcessorLoadError] = useState('');

  const [disableStreamProcessors, setDisableStreamProcessors] = useState(false);
  const [toastDeleteStreamProcessor, setToastDeleteStreamProcessor] = useState(false);
  const [processorToDelete, setProcessorToDelete] = useState('');
  const [toastProcessorName, setToastProcessorName] = useState('');
  const isStreamsCreateProcessorUIEnabled = settingsModel.hasProjectFeature('STREAMS_CREATE_PROCESSOR_UI');

  const unsortedStreamProcessors = useSelector(getStreamProcessors(tenantName));
  const streamProcessors =
    unsortedStreamProcessors?.sort((a: StreamProcessor, b: StreamProcessor) => (a.startedAt > b.startedAt ? -1 : 1)) ??
    [];
  const [loadingByProcessor, setLoadingByProcessor] = useState<Record<string, StreamProcessorAction | null>>({});

  const fetchStreamInstance = useCallback(async () => {
    setFetchError('');
    try {
      await dispatch(loadStreamInstances(groupId, tenantName, canViewStreamsInstance));
    } catch (e) {
      setFetchError(exceptionToMessage(e));
    }
  }, [dispatch, setFetchError, groupId, tenantName, canViewStreamsInstance]);

  const startStreamProcessor = useCallback(
    async (processorName: string) => {
      setLoadingByProcessor((prev) => ({ ...prev, [processorName]: StreamProcessorAction.Starting }));
      try {
        await api.nds.streams.startStreamProcessor(groupId, tenantName, processorName);
        // re-fetch the stream processors to update the UI immediately
        await fetchStreamInstance();
      } catch {
        setOperationError(`Failed to start stream processor ${processorName}`);
      } finally {
        setLoadingByProcessor((prev) => ({ ...prev, [processorName]: null }));
      }
    },
    [fetchStreamInstance, setOperationError, groupId, tenantName]
  );

  const stopStreamProcessor = useCallback(
    async (processorName: string) => {
      setLoadingByProcessor((prev) => ({ ...prev, [processorName]: StreamProcessorAction.Stopping }));
      try {
        await api.nds.streams.stopStreamProcessor(groupId, tenantName, processorName);
        // re-fetch the stream processors to update the UI immediately
        await fetchStreamInstance();
      } catch {
        setOperationError(`Failed to stop stream processor ${processorName}`);
      } finally {
        setLoadingByProcessor((prev) => ({ ...prev, [processorName]: null }));
      }
    },
    [fetchStreamInstance, setOperationError, groupId, tenantName]
  );

  const deleteStreamProcessor = useCallback(
    async (processorName: string) => {
      setLoadingByProcessor((prev) => ({ ...prev, [processorName]: StreamProcessorAction.Deleting }));
      try {
        setToastDeleteStreamProcessor(false);
        await api.nds.streams.deleteStreamProcessor(groupId, tenantName, processorName);
        // re-fetch the stream processors to update the UI immediately
        await fetchStreamInstance();
        setActiveModal(MODAL_VIEWS.NONE);
        setToastDeleteStreamProcessor(true);
        setToastProcessorName(processorName);
      } catch {
        setOperationError(`Failed to delete stream processor ${processorName}`);
      } finally {
        setLoadingByProcessor((prev) => ({ ...prev, [processorName]: null }));
      }
    },
    [fetchStreamInstance, setOperationError, setActiveModal, groupId, tenantName]
  );

  const addSampleConnection = useCallback(async () => {
    const connection: ConnectionRegistrySample = {
      name: SAMPLE_CONNECTION_NAMES.SOLAR,
      type: ConnectionRegistryType.SAMPLE,
    };

    analytics.track(SEGMENT_EVENTS.BUTTON_CLICKED, {
      context: 'Atlas Streams',
      action: 'Added new Streams Connection',
      connection_type: connection.type,
    });

    await api.nds.streams.createStreamConnectionRegistryItem(groupId, tenantName, connection);

    fetchStreamInstance();

    dispatch(showAddConnectionToast(connection.name, false));

    navigate(pageRouteByView(tenantName)[PageView.Connections]);
  }, [dispatch, navigate, fetchStreamInstance, groupId, tenantName]);

  useEffect(() => {
    setDisableStreamProcessors(connections.length <= 0);
  }, [connections]);

  const orderConnectionNamesByStage = ({
    pipelineAsJson = [],
    stagesAndConnectionNames = [],
  }: {
    pipelineAsJson?: Array<any>;
    stagesAndConnectionNames?: Array<[string, string]>;
  }): Array<[string, string]> => {
    // Although we already return a list of stages and connection names from the backend, we parse the pipeline in
    // the UI to get the proper ordering of the stages. We use the values returned from the backend as the source of truth
    // for what stages we should be parsing for connection names.
    const stages = stagesAndConnectionNames
      .map((stageAndConnectionName) => {
        const [stageName] = stageAndConnectionName;
        return stageName;
      })
      .reduce((acc, stageName) => [...acc, stageName], []);

    return pipelineAsJson
      .filter((p) => stages.includes(Object.keys(p)[0]))
      .map((p) => {
        const pipelineStage = Object.keys(p)[0];
        let connectionName = p[pipelineStage]['connectionName'];
        if (pipelineStage === SPIStage.Merge) {
          connectionName = p[pipelineStage]['into']['connectionName'];
        }
        if (pipelineStage === SPIStage.Lookup) {
          connectionName = p[pipelineStage]['from']['connectionName'];
        }

        return [pipelineStage, connectionName];
      });
  };

  const handleViewProcessorDetails = async (selectedProcessorName: string) => {
    setProcessorToViewDetails(null);
    setProcessorLoadError('');
    setLoadingProcessor(true);
    setActiveModal(MODAL_VIEWS.VIEW_DETAILS);

    try {
      const { pipeline: pipelineAsJson } = await api.nds.streams.getStreamProcessor(
        groupId,
        tenantName,
        selectedProcessorName
      );
      const stagesAndConnectionNames =
        streamProcessors.find(({ name }) => selectedProcessorName === name)?.stagesAndConnectionNames ?? [];

      const pipelineAsFormattedString = JSON.stringify(pipelineAsJson, null, 2);

      const orderedConnectionNamesByStage = orderConnectionNamesByStage({
        pipelineAsJson,
        stagesAndConnectionNames,
      });
      setProcessorToViewDetails({
        name: selectedProcessorName,
        pipeline: pipelineAsFormattedString,
        orderedConnectionNamesByStage,
      });
    } catch (e) {
      setProcessorLoadError(e.message);
    } finally {
      setLoadingProcessor(false);
    }
  };

  return (
    <FlexTab data-testid={TestSelector.ProcessorsView}>
      {!isStreamsCreateProcessorUIEnabled && (
        <FlexRow css={styles.createAndManageRow}>
          <BrandIcon iconName="Technical_ACTION_Pipeline_Thumbnail" Component={Image} height="76px" />
          <FlexColumn>
            <Subtitle>Create and Manage Stream Processors</Subtitle>
            <Body>
              Process continuous data streams with a stream processor.{' '}
              <Link href="https://www.mongodb.com/docs/atlas/atlas-sp/manage-stream-processor/">
                Learn how to create and manage stream processors.
              </Link>
            </Body>
          </FlexColumn>
        </FlexRow>
      )}
      {isStreamsCreateProcessorUIEnabled && streamProcessors.length > 0 && (
        <Banner data-testid={TestSelector.EditingProcessorsInfoBanner}>
          Stream processors cannot be edited in the UI at this time. To do so,{' '}
          <Link as="button" onClick={() => setActiveModal(MODAL_VIEWS.CONNECT)}>
            connect
          </Link>{' '}
          to MongoDB Shell or VSCode.{' '}
          <Link
            href="https://www.mongodb.com/docs/atlas/atlas-stream-processing/manage-stream-processor/"
            target="_blank"
          >
            Learn how to create and manage stream processors
          </Link>
        </Banner>
      )}
      <Card css={styles.cardContainer}>
        {streamProcessors.length > 0 && (
          <Table
            data-testid={TestSelector.StreamProcessorsTable}
            data={streamProcessors}
            columns={[
              <TableHeader label="Stream Processor Name" />,
              <TableHeader label="Status" />,
              <TableHeader label="Actions" />,
            ]}
          >
            {({ datum }) => {
              // Render an error if we have one, and if we are in the failed or stopped state.
              // Errors is oddly enough always an array of length 1, but check anyway
              return (
                <StreamProcessorRow
                  datum={datum}
                  onStart={startStreamProcessor}
                  onStop={stopStreamProcessor}
                  onDelete={(name) => {
                    setProcessorToDelete(name);
                    setActiveModal(MODAL_VIEWS.DELETE_PROCESSOR);
                  }}
                  onDetailsClick={() => handleViewProcessorDetails(datum.name)}
                  setSelectedSPForMonitoring={setSelectedSPForMonitoring}
                  tenantName={tenantName}
                  loadingAction={loadingByProcessor[datum.name]}
                  settingsModel={settingsModel}
                />
              );
            }}
          </Table>
        )}
        <StreamProcessorDeleteConfirmModal
          data-testid={TestSelector.DeleteStreamProcessorModal}
          title={`Are you sure you want to delete the processor, ${processorToDelete}`}
          open={activeModal === MODAL_VIEWS.DELETE_PROCESSOR}
          confirmButtonProps={{
            onClick: () => deleteStreamProcessor(processorToDelete),
            children: 'Delete',
          }}
          cancelButtonProps={{
            onClick: () => {
              setActiveModal(MODAL_VIEWS.NONE);
              setProcessorToDelete('');
            },
          }}
          requiredInputText={processorToDelete}
          variant={ConfirmationModalVariant.Danger}
        >
          You cannot use this processor after deleting it!
        </StreamProcessorDeleteConfirmModal>
        <StreamsProcessorPipelineDetailsModal
          open={activeModal === MODAL_VIEWS.VIEW_DETAILS}
          setOpen={(open) => {
            if (!open) {
              setActiveModal(MODAL_VIEWS.NONE);
            }
          }}
          title={`Stream Processor Details: ${processorToViewDetails?.name}`}
          data-testid={TestSelector.PipelineDetailsModal}
        >
          {isLoadingProcessor ? (
            <PageLoader description="Loading processor details..." />
          ) : !!processorLoadError ? (
            <Banner variant={BannerVariant.Danger}>{`Error loading processor details: ${processorLoadError}`}</Banner>
          ) : (
            <>
              <H3 css={styles.detailsModalHeader}> {processorToViewDetails?.name} </H3>
              <Subtitle css={styles.detailsModalHeader}>Connection Names By Stage</Subtitle>
              <div css={styles.connectionNamesByStageSections}>
                <OrderedList>
                  {processorToViewDetails?.orderedConnectionNamesByStage.map((stageAndConnectionName, index) => {
                    const [stage, connectionName] = stageAndConnectionName;
                    return (
                      <OrderedListItem
                        key={`stage-${index}-${stage}-${connectionName}`}
                        data-testid={`stage-${index}-${stage}-${connectionName}`}
                        title={stage}
                        description={connectionName}
                      />
                    );
                  })}
                </OrderedList>
              </div>
              <Subtitle css={styles.detailsModalHeader}>Pipeline</Subtitle>
              <Code language="json" data-testid={TestSelector.PipelineDetailsCode} css={styles.pipelineCode}>
                {processorToViewDetails?.pipeline ?? ''}
              </Code>
            </>
          )}
        </StreamsProcessorPipelineDetailsModal>
        <StreamProcessorDeleteSucessToast
          variant="success"
          title={`${toastProcessorName} was successfully deleted!`}
          open={toastDeleteStreamProcessor}
          onClose={() => {
            setToastDeleteStreamProcessor(false);
          }}
        />
        {streamProcessors.length === 0 &&
          (disableStreamProcessors ? (
            <EmptyStateWithGraphic>
              <BrandIcon iconName="Technical_ACTION_ConnectSettings_Spot" Component={EmptyStateGraphic} width="300" />
              <EmptyStateText>
                <H3>You need at least one connection to create a stream processor.</H3>
                <Body>Use our sample connection or add your own Kafka or Atlas Database connection.</Body>
                <EmptyStateCTA>
                  <Button onClick={addSampleConnection} variant={Variant.Primary} disabled={!isStreamsOwner}>
                    Add sample connection
                  </Button>
                  <Button onClick={addConnectionRegistryItem} disabled={!isStreamsOwner}>
                    Add your own connection
                  </Button>
                </EmptyStateCTA>
                <Link
                  href="https://www.mongodb.com/docs/atlas/atlas-sp/manage-processing-instance/#add-a-connection-to-the-connection-registry"
                  css={styles.docLink}
                >
                  Learn more about sample connections
                </Link>
              </EmptyStateText>
            </EmptyStateWithGraphic>
          ) : isStreamsCreateProcessorUIEnabled ? (
            <EmptyStateContainer>
              <StreamProcessorEmptyState tenantName={tenantName} />
            </EmptyStateContainer>
          ) : (
            <EmptyStateText data-testid={TestSelector.LegacyProcessorTableEmptyState}>
              <H3>No stream processors created.</H3>
              <Body>
                Add a stream processor to get started. A stream processor represents the logic to be performed on a
                stream of data.
              </Body>
            </EmptyStateText>
          ))}
      </Card>
    </FlexTab>
  );
}
