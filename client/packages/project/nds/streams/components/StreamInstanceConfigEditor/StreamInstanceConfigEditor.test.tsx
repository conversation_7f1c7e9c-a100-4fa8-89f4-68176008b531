import { CloudNavElementIds } from '@lg-private/cloud-nav';
import userEvent from '@testing-library/user-event';
import fetchMock from 'fetch-mock';
import { Provider } from 'react-redux';
import { MemoryRouter, useNavigate } from 'react-router-dom-v5-compat';

import { BackingCloudProvider, CloudProvider } from '@packages/types/nds/provider';
import {
  ConnectionRegistryNetworkingType,
  ConnectionRegistryNetworkingTypeReadable,
  ConnectionRegistryType,
  HAS_VIEWED_VPC_GUIDE_CUE,
  NetworkTypeAtlasManaged,
  ProviderToConnectionRegistryType,
  STREAM_INSTANCE_TIERS,
  STREAM_PROCESSOR_STATE,
  StreamInstance,
  StreamMetrics,
  StreamsConnectionReadOnly,
} from '@packages/types/nds/streams';
import { SettingsModel } from '@packages/types/settingsModel';

import { createMainReduxStore } from '@packages/redux/project/test-utils/reduxHelpers';

import * as api from '@packages/common/services/api';
import Settings from '@packages/common/models/Settings';
import { AwsPeerShape } from '@packages/common/schemas/peeringSchema';
import localStorageWrapper from '@packages/common/services/localStorage';
import { retry } from '@packages/common/utils/retry';
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import { CloudLayout } from '@packages/layout/CloudLayout';
import { mockConnections, mockStreamProcessors } from '@packages/project/nds/streams/fixtures/streamsFixtures';
import { Route, toHashRoute } from '@packages/project/nds/streams/routes';
import { act, render, screen, waitFor, waitForElementToBeRemoved, within } from '@packages/react-testing-library';
import getTypedMockOf from '@packages/test-utils/getTypedMockOf';
import fakeWindowLocation from '@packages/test-utils/locationMock';

import { PageView, StreamInstanceConfigEditor } from './index';
import { pageRouteByView, StreamInstanceConfigEditorProps } from './StreamInstanceConfigEditor';
import { TestSelector as EmptyStateTestSelector } from './StreamProcessorEmptyState';
import { TestSelector } from './types';

const TENANT_NAME = 'tenant';
const GROUP_ID = 'group';

const defaultProps = {
  tenantName: TENANT_NAME,
  streamProcessors: [],
  metrics: {},
  groupId: GROUP_ID,
};

const defaultInstance: StreamInstance = {
  name: defaultProps.tenantName,
  connections: [],
  groupId: GROUP_ID,
  _id: 'aa',
  hostnames: ['aah'],
  streamConfig: { tier: STREAM_INSTANCE_TIERS.SP30 },
  dataProcessRegion: { region: 'us-east-1', cloudProvider: CloudProvider.AWS },
};

const ERRORED_VPC_ID = 'vpc-error';

const mockSettingsModel = new Settings();
jest.mock('@packages/common/utils/retry');
jest.mock('react-router-dom-v5-compat', () => ({
  __esModule: true,
  ...jest.requireActual('react-router-dom-v5-compat'),
  useNavigate: jest.fn().mockReturnValue(() => {}),
}));

jest.mock('@packages/project/common/settings', () => ({
  __esModule: true,
  default: { getModel: jest.fn().mockReturnValue({ hasProjectFeature: jest.fn() }) },
}));

jest.spyOn(localStorageWrapper, 'setItem');
jest.spyOn(api.nds.streams, 'createStreamConnectionRegistryItem');
jest.spyOn(api.nds.streams, 'getStreamInstance');
jest.spyOn(api.nds.streams, 'getStreamInstances');
jest.spyOn(api.nds.networkSettings, 'getPeerConnectionsForGroup');
jest.spyOn(api.nds.streams, 'deleteStreamConnection');
jest.spyOn(api.nds.streams, 'sendViewStreamsConnectionEvent');
jest.spyOn(api.nds.streams, 'getStreamInstanceProcessors');
// for mounting with CloudLayout, mock from the footer
jest.spyOn(api.settings, 'systemHealth').mockResolvedValue({ alertsEnabled: true });
jest.mock('@packages/layout/ResourceContext', () => ({
  useResourceContext: jest.fn().mockReturnValue({ activeProjectId: GROUP_ID }),
}));

const retryMock = getTypedMockOf(retry);

const waitForLayoutMount = async () =>
  // Wait until the Streams SSN appears to ensure we're fully loaded
  await screen.findByTestId(CloudNavElementIds.sideNav.primary.clusters, undefined, {
    timeout: 2000, // LG's config fetch stub waits one second before returning
  });

describe('@packages/project/nds/streams/components/StreamInstanceConfigEditor', function () {
  jest.setTimeout(20000);

  const redirect = jest.fn();

  let analyticsTrackStub: jest.SpyInstance;

  // test
  /* @ts-ignore unused, re-declared with default never overwritten */
  let createStreamConnectionRegistryItemStub: jest.SpyInstance;
  /* @ts-ignore unused, re-declared with default never overwritten */
  let getStreamProcessorsStub: jest.SpyInstance;
  let getStreamInstanceStub: jest.SpyInstance;
  let getStreamInstancesStub: jest.SpyInstance;
  let getPeerConnectionsForGroupStub: jest.SpyInstance;
  let deleteStreamConnectionStub: jest.SpyInstance;
  let localStorageGetItemStub: jest.SpyInstance;
  let localStorageSetItemStub: jest.SpyInstance;
  let sendNewConnectionEventStub: jest.SpyInstance;
  let startStreamProcessorStub: jest.SpyInstance;
  let stopStreamProcessorStub: jest.SpyInstance;
  let deleteStreamProcessorStub: jest.SpyInstance;
  let useNavigateStub: jest.Mock;
  let user: ReturnType<typeof userEvent.setup>;

  fakeWindowLocation.assign = redirect;

  function mountComponent({
    instance = defaultInstance,
    streamProcessors = [],
    connections = [],
    vpcPeers = [],
    toast = { name: '', showAddConnectionToast: false },
    metrics = {},
    isGroupOwner = true,
    settingsModel = mockSettingsModel,
    pageView,
    showCloudNav = false,
  }: {
    instance?: Partial<StreamInstance>;
    streamProcessors?: Array<any>;
    connections?: Array<StreamsConnectionReadOnly>;
    vpcPeers?: Array<AwsPeerShape>;
    toast?: { name: string; showAddConnectionToast: boolean };
    metrics?: StreamMetrics;
    isGroupOwner?: boolean;
    settingsModel?: SettingsModel;
    showConnectionRegistryTab?: boolean;
    pageView?: PageView;
    showCloudNav?: boolean;
  }) {
    analyticsTrackStub = jest.spyOn(analytics, 'track');

    useNavigateStub = jest.fn();
    const useNavigateMock = getTypedMockOf(useNavigate);
    useNavigateMock.mockReturnValue(useNavigateStub);

    const state = {
      settings: {
        showCloudNav,
        isNdsGovEnabled: false,
        groupType: 'NDS',
        appEnv: 'TEST',
      },
      nds: {
        streams: {
          instances: [
            {
              ...instance,
              connections: [...(instance.connections || []), ...connections],
            },
          ],
          streamMetrics: metrics,
          streamProcessors: { [TENANT_NAME]: streamProcessors },
          fromAddConnection: {
            ...toast,
          },
        },
        vpcPeering: {
          vpcPeeringConnections: vpcPeers,
        },
      },
    };

    getStreamProcessorsStub = jest
      .spyOn(api.nds.streams, 'getStreamInstanceProcessors')
      .mockResolvedValue(streamProcessors);

    const props: StreamInstanceConfigEditorProps = {
      ...defaultProps,
      windowLocation: fakeWindowLocation,
      isStreamsOwner: isGroupOwner,
      settingsModel,
      pageView,
    };

    const streamInstanceConfigEditorWithCloudNav = (
      <CloudLayout>
        <StreamInstanceConfigEditor {...props} />
      </CloudLayout>
    );

    return {
      renderResult: render(
        <MemoryRouter initialEntries={['/streamProcessing']}>
          <Provider store={createMainReduxStore(state)}>
            {showCloudNav ? streamInstanceConfigEditorWithCloudNav : <StreamInstanceConfigEditor {...props} />}
          </Provider>
        </MemoryRouter>
      ),
      props,
      state,
    };
  }

  beforeEach(() => {
    // Disable retry for test environment
    retryMock.mockImplementation((fn) => fn());

    createStreamConnectionRegistryItemStub = jest
      .spyOn(api.nds.streams, 'createStreamConnectionRegistryItem')
      .mockResolvedValue(defaultInstance);
    getStreamInstanceStub = jest.spyOn(api.nds.streams, 'getStreamInstance').mockResolvedValue(defaultInstance);
    getStreamInstancesStub = jest.spyOn(api.nds.streams, 'getStreamInstances').mockResolvedValue([]);
    getPeerConnectionsForGroupStub = jest
      .spyOn(api.nds.networkSettings, 'getPeerConnectionsForGroup')
      .mockResolvedValue([]);
    getStreamProcessorsStub = jest.fn().mockResolvedValue([]);
    deleteStreamConnectionStub = jest.spyOn(api.nds.streams, 'deleteStreamConnection');

    localStorageGetItemStub = jest.spyOn(localStorageWrapper, 'getItem').mockReturnValue(null);
    localStorageSetItemStub = jest.spyOn(localStorageWrapper, 'setItem');

    sendNewConnectionEventStub = jest.spyOn(api.nds.streams, 'sendViewStreamsConnectionEvent').mockResolvedValue();

    startStreamProcessorStub = jest.spyOn(api.nds.streams, 'startStreamProcessor');
    stopStreamProcessorStub = jest.spyOn(api.nds.streams, 'stopStreamProcessor');
    deleteStreamProcessorStub = jest.spyOn(api.nds.streams, 'deleteStreamProcessor');

    jest.useFakeTimers();
    // Override advanceTimers so that useFakeTimers + advanceTimersByTime works with user actions
    // https://github.com/testing-library/user-event/issues/833#issuecomment-**********
    user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

    fetchMock.get('/system/health', {});
  });

  afterEach(() => {
    fetchMock.restore();
    jest.useRealTimers();
    jest.restoreAllMocks();
  });

  describe('Kafka VPC Deployment', function () {
    const vpcId = '123';
    const kafkaVPC: StreamsConnectionReadOnly = {
      name: 'KafkaDev',
      bootstrapServers: '',
      type: ConnectionRegistryType.KAFKA,
      networking: {
        access: {
          type: ConnectionRegistryNetworkingType.VPC,
          name: vpcId,
        },
      },
      inUse: false,
      isDeployingVpcProxy: true,
    };

    const vpcPeer = {
      '@provider': BackingCloudProvider.AWS,
      id: `1`,
      vpcId: vpcId,
      awsAccountId: 'ABCDEFG12345',
      status: 'AVAILABLE',
      connectionId: 'test',
      containerId: 'test',
    } as AwsPeerShape;

    const streamInstanceWithDeployedKafkaConnection = {
      ...defaultInstance,
      connections: [
        {
          ...kafkaVPC,
          isDeployingVpcProxy: false,
        },
      ],
    };

    it('Renders a deployment banner, toast, enables/disabled buttons', async function () {
      localStorageGetItemStub.mockResolvedValue('true');
      // Initial state idle, flip to working when mocking the fetch.
      getStreamInstanceStub.mockResolvedValue(streamInstanceWithDeployedKafkaConnection);
      getStreamInstancesStub.mockResolvedValue([streamInstanceWithDeployedKafkaConnection]);

      getPeerConnectionsForGroupStub.mockResolvedValue([vpcPeer]);

      mountComponent({
        connections: [kafkaVPC],
        vpcPeers: [vpcPeer],
        pageView: PageView.Connections,
      });

      const deploymentBanner = screen.getByRole('alert', { name: /vpc deployment/i });

      expect(within(deploymentBanner).getByText(`"${kafkaVPC.name}"`, { exact: false })).toBeInTheDocument();
      expect(within(deploymentBanner).getByText(`"${vpcPeer.vpcId}"`, { exact: false })).toBeInTheDocument();
      expect(
        within(deploymentBanner).getByText(`Edit and view the VPC Peer for this connection in`, { exact: false })
      ).toBeInTheDocument();

      // Buttons should be disabled while deploying
      expect(screen.getByRole('button', { name: /edit/i })).toBeAriaDisabled();
      expect(screen.getByRole('button', { name: /delete/i })).toBeAriaDisabled();

      // We have fetched latest VPC Proxies with an updated state.
      act(() => jest.advanceTimersByTime(40000));
      expect(getStreamInstanceStub).toHaveBeenCalledTimes(1);

      // Render the toast.
      expect(await screen.findByText(`Connection "${kafkaVPC.name}" is ready for use`)).toBeInTheDocument();
      expect(
        screen.getByText('You can now reference this connection and review its details in the connection registry.')
      ).toBeInTheDocument();

      // Re-enable edit/delete buttons
      expect(screen.getByRole('button', { name: /edit/i })).not.toBeAriaDisabled();
      expect(screen.getByRole('button', { name: /delete/i })).not.toBeAriaDisabled();
    });
  });

  describe('header', function () {
    it('Renders the tenant name as a title', function () {
      mountComponent({});

      expect(screen.getByText(`Configure ${defaultProps.tenantName}`)).toBeInTheDocument();
    });

    it('Clicking streams breadcrumb redirects back to streams list', async function () {
      mountComponent({});

      expect(screen.getByRole('link', { name: 'Stream Processing' })).toHaveAttribute(
        'href',
        toHashRoute(Route.STREAMS_LIST)
      );
    });

    it('should render a "Create stream processor" button on the processors page when the STREAMS_CREATE_PROCESSOR_UI flag is enabled', () => {
      mountComponent({
        settingsModel: new Settings({ GROUP_ENABLED_FEATURE_FLAGS: ['STREAMS_CREATE_PROCESSOR_UI'] }),
        pageView: PageView.Processors,
      });

      expect(screen.getByText('Create stream processor')).toBeInTheDocument();
    });

    it('should not render a "Create stream processor" button on the monitoring page when the STREAMS_CREATE_PROCESSOR_UI flag is enabled', () => {
      mountComponent({
        settingsModel: new Settings({ GROUP_ENABLED_FEATURE_FLAGS: ['STREAMS_CREATE_PROCESSOR_UI'] }),
        pageView: PageView.Monitoring,
      });

      expect(screen.queryByText('Create stream processor')).not.toBeInTheDocument();
    });

    it('should not render a "Create stream processor" button on the connection registry page when the STREAMS_CREATE_PROCESSOR_UI flag is enabled', () => {
      mountComponent({
        settingsModel: new Settings({ GROUP_ENABLED_FEATURE_FLAGS: ['STREAMS_CREATE_PROCESSOR_UI'] }),
        pageView: PageView.Connections,
      });

      expect(screen.queryByText('Create stream processor')).not.toBeInTheDocument();
    });

    it('should not render a "Create stream processor" button on the processors page when the STREAMS_CREATE_PROCESSOR_UI flag is disabled', () => {
      mountComponent({
        settingsModel: new Settings({ GROUP_ENABLED_FEATURE_FLAGS: [] }),
        pageView: PageView.Processors,
      });

      expect(screen.queryByText('Create stream processor')).not.toBeInTheDocument();
    });
  });

  describe('Stream processors', function () {
    describe('without connections', function () {
      it('displays a custom empty state', async function () {
        mountComponent({});

        expect(screen.getByText('You need at least one connection to create a stream processor.')).toBeInTheDocument();
        expect(
          screen.getByText('Use our sample connection or add your own Kafka or Atlas Database connection.')
        ).toBeInTheDocument();

        expect(screen.getByRole('button', { name: /add sample connection/i })).toBeInTheDocument();
        expect(screen.getByRole('link', { name: /Learn more about sample connections/ })).toBeInTheDocument();

        await user.click(screen.getByRole('button', { name: /add your own connection/i }));

        expect(redirect).toHaveBeenCalledWith(
          toHashRoute(Route.STREAMS_ADD_CONNECTION, { tenantName: defaultProps.tenantName })
        );
      });

      it('can create sample connection via empty state button, redirects to connections', async function () {
        mountComponent({});

        await user.click(screen.getByRole('button', { name: /add sample connection/i }));

        expect(screen.getByText('Connection "sample_stream_solar" added')).toBeInTheDocument();

        expect(useNavigateStub).toHaveBeenCalledWith(`${pageRouteByView(TENANT_NAME)[PageView.Connections]}`);
      });

      it('displays a custom empty state, cannot click if not group owner', async function () {
        mountComponent({ isGroupOwner: false });

        expect(screen.getByText('You need at least one connection to create a stream processor.')).toBeInTheDocument();
        expect(
          screen.getByText('Use our sample connection or add your own Kafka or Atlas Database connection.')
        ).toBeInTheDocument();

        expect(screen.getByRole('button', { name: /add sample connection/i })).toBeAriaDisabled();
        expect(screen.getByRole('button', { name: /add your own connection/i })).toBeAriaDisabled();
      });
    });

    it('renders tutorial link', function () {
      mountComponent({});

      expect(screen.getByRole('link', { name: /learn how to create and manage stream processors\./i })).toHaveAttribute(
        'href',
        'https://www.mongodb.com/docs/atlas/atlas-sp/manage-stream-processor/'
      );
    });

    it('renders the legacy empty state when there are no processors and the STREAMS_CREATE_PROCESSOR_UI flag is disabled', () => {
      mountComponent({
        connections: mockConnections,
        settingsModel: new Settings({ GROUP_ENABLED_FEATURE_FLAGS: [] }),
      });

      expect(screen.getByTestId(TestSelector.LegacyProcessorTableEmptyState)).toBeInTheDocument();
    });

    it('renders the empty state with CTAs to create a processor when there are no processors and the STREAMS_CREATE_PROCESSOR_UI flag is enabled', () => {
      mountComponent({
        connections: mockConnections,
        settingsModel: new Settings({ GROUP_ENABLED_FEATURE_FLAGS: ['STREAMS_CREATE_PROCESSOR_UI'] }),
      });

      expect(screen.getByTestId(EmptyStateTestSelector.VisualBuilderButton)).toBeInTheDocument();
      expect(screen.getByTestId(EmptyStateTestSelector.JSONEditorButton)).toBeInTheDocument();
      expect(screen.queryByTestId(TestSelector.LegacyProcessorTableEmptyState)).not.toBeInTheDocument();
    });

    it('renders an icon to view processor details when the STREAMS_CREATE_PROCESSOR_UI flag is enabled', async () => {
      const getStreamProcessorSpy = jest.spyOn(api.nds.streams, 'getStreamProcessor').mockResolvedValue({
        _id: mockStreamProcessors[0]._id,
        name: mockStreamProcessors[0].name,
        pipeline: mockStreamProcessors[0].pipeline,
      });

      mountComponent({
        streamProcessors: mockStreamProcessors,
        settingsModel: new Settings({
          GROUP_ENABLED_FEATURE_FLAGS: ['STREAMS_CREATE_PROCESSOR_UI'],
          GROUP_ID,
        }),
      });

      const processorName = mockStreamProcessors[0].name;
      await user.click(screen.getByTestId(`${TestSelector.ViewDetailsIcon}-${processorName}`));
      expect(await screen.findByTestId(TestSelector.PipelineDetailsModal)).toBeInTheDocument();
      expect(getStreamProcessorSpy).toHaveBeenCalledWith(GROUP_ID, TENANT_NAME, processorName);

      mockStreamProcessors[0].stagesAndConnectionNames.forEach((stageAndConnectionName, index) => {
        const [stageName, connName] = stageAndConnectionName;
        expect(screen.getByTestId(`stage-${index}-${stageName}-${connName}`)).toHaveTextContent(`${index + 1}`);
        expect(screen.getByTestId(`stage-${index}-${stageName}-${connName}`)).toHaveTextContent(stageName);
        expect(screen.getByTestId(`stage-${index}-${stageName}-${connName}`)).toHaveTextContent(connName);
      });
      expect(JSON.parse(screen.getByTestId(TestSelector.PipelineDetailsCode).textContent)).toEqual(
        mockStreamProcessors[0].pipeline
      );
    });

    it('renders an error in the processor details modal when loading a processor fails', async () => {
      const error = new Error("you're not perfect");
      const getStreamProcessorSpy = jest.spyOn(api.nds.streams, 'getStreamProcessor').mockRejectedValue(error);

      mountComponent({
        streamProcessors: mockStreamProcessors,
        settingsModel: new Settings({
          GROUP_ENABLED_FEATURE_FLAGS: ['STREAMS_CREATE_PROCESSOR_UI'],
          GROUP_ID,
        }),
      });

      const processorName = mockStreamProcessors[0].name;
      await user.click(screen.getByTestId(`${TestSelector.ViewDetailsIcon}-${processorName}`));
      expect(await screen.findByTestId(TestSelector.PipelineDetailsModal)).toBeInTheDocument();
      expect(getStreamProcessorSpy).toHaveBeenCalledWith(GROUP_ID, TENANT_NAME, processorName);

      expect(
        within(screen.getByTestId(TestSelector.PipelineDetailsModal)).getByText(
          `Error loading processor details: ${error.message}`
        )
      ).toBeInTheDocument();
    });

    it('does not render an icon to view processor details when the STREAMS_CREATE_PROCESSOR_UI flag is disabled', () => {
      mountComponent({
        streamProcessors: mockStreamProcessors,
        settingsModel: new Settings({
          GROUP_ENABLED_FEATURE_FLAGS: [],
          GROUP_ID,
        }),
      });

      expect(
        screen.queryByTestId(`${TestSelector.ViewDetailsIcon}-${mockStreamProcessors[0]}`)
      ).not.toBeInTheDocument();
    });

    it('Renders stream processors by default', function () {
      mountComponent({});

      expect(screen.getByTestId(TestSelector.ProcessorsView)).toBeInTheDocument();
    });

    it('renders an info banner about editing processors when the STREAMS_CREATE_PROCESSOR_UI flag is enabled', () => {
      mountComponent({
        streamProcessors: mockStreamProcessors,
        settingsModel: new Settings({ GROUP_ENABLED_FEATURE_FLAGS: ['STREAMS_CREATE_PROCESSOR_UI'] }),
      });

      expect(screen.getByTestId(TestSelector.EditingProcessorsInfoBanner)).toBeInTheDocument();
    });

    it('does not render an info banner about editing processors when the STREAMS_CREATE_PROCESSOR_UI flag is disabled', () => {
      mountComponent({
        streamProcessors: mockStreamProcessors,
        settingsModel: new Settings({ GROUP_ENABLED_FEATURE_FLAGS: [] }),
      });

      expect(screen.queryByTestId(TestSelector.EditingProcessorsInfoBanner)).not.toBeInTheDocument();
    });

    it('Renders stream processor information', function () {
      mountComponent({ streamProcessors: mockStreamProcessors });

      expect(screen.getByText('FraudDetection')).toBeInTheDocument();
      expect(screen.getAllByText(STREAM_PROCESSOR_STATE.STARTED)[0]).toBeInTheDocument();
      expect(screen.getByText('TestProcessor')).toBeInTheDocument();
      expect(screen.getByText(STREAM_PROCESSOR_STATE.FAILED)).toBeInTheDocument();
      expect(screen.getByText('PausedProcessor')).toBeInTheDocument();
      expect(screen.getByText(STREAM_PROCESSOR_STATE.CREATED)).toBeInTheDocument();
      expect(screen.getByText('StoppedProcessor')).toBeInTheDocument();
      expect(screen.getByText(STREAM_PROCESSOR_STATE.STOPPED)).toBeInTheDocument();
      expect(screen.getByText('SampleSolarProcessor')).toBeInTheDocument();
      expect(screen.getAllByText(STREAM_PROCESSOR_STATE.STARTED)[1]).toBeInTheDocument();
    });

    it('Handles start stream processor button actions based on state', async () => {
      mockSettingsModel.getCurrentGroupId = () => GROUP_ID;
      startStreamProcessorStub.mockImplementation(() => new Promise((resolve) => setTimeout(() => resolve(true), 10)));
      mountComponent({ streamProcessors: mockStreamProcessors });

      const stoppedStreamProcessor = mockStreamProcessors[3];
      const startButtons = screen.getAllByRole('button', { name: /start/i });

      expect(stoppedStreamProcessor.state).toBe(STREAM_PROCESSOR_STATE.STOPPED);
      // the started processor should have the start button disabled
      expect(startButtons[0]).toHaveAttribute('aria-disabled', 'true');
      expect(startButtons).toHaveLength(6);

      // start the stopped processor
      await user.click(startButtons[3]);
      await waitFor(() => {
        expect(screen.getByTestId(TestSelector.StreamProcessorLoadingState)).toBeInTheDocument();
      });

      const table = screen.getByTestId(TestSelector.StreamProcessorsTable);
      const rows = within(table).getAllByRole('row');
      const loadingRow = rows.find((row) => within(row).queryByTestId(TestSelector.StreamProcessorLoadingState));

      const stopButton = within(loadingRow!).getByRole('button', { name: 'Stop' });
      const deleteButton = within(loadingRow!).getByRole('button', { name: 'Delete' });

      expect(stopButton).toHaveAttribute('aria-disabled', 'true');
      expect(deleteButton).toHaveAttribute('aria-disabled', 'true');

      expect(startStreamProcessorStub).toHaveBeenCalledWith(GROUP_ID, TENANT_NAME, stoppedStreamProcessor.name);
      await waitFor(() => {
        expect(getStreamInstancesStub).toHaveBeenCalled();
      });
    });

    it('Renders start stream processor errors', async () => {
      startStreamProcessorStub.mockRejectedValue(new Error('error starting stream processor'));
      mountComponent({ streamProcessors: mockStreamProcessors });

      const startButtons = screen.getAllByRole('button', { name: /start/i });
      // start the stopped processor
      await user.click(startButtons[3]);

      const ErrorBanner = screen.getByRole('alert');
      expect(ErrorBanner).toBeInTheDocument();

      expect(
        within(ErrorBanner).getByText(`Failed to start stream processor ${mockStreamProcessors[3].name}`, {
          exact: false,
        })
      ).toBeInTheDocument();
    });

    it('Renders stop stream processor errors', async () => {
      stopStreamProcessorStub.mockRejectedValue(new Error('error starting stream processor'));
      mountComponent({ streamProcessors: mockStreamProcessors });

      const stopButtons = screen.getAllByRole('button', { name: /stop/i });
      // start the stopped processor
      await user.click(stopButtons[0]);

      const ErrorBanner = screen.getByRole('alert');
      expect(ErrorBanner).toBeInTheDocument();

      expect(
        within(ErrorBanner).getByText(`Failed to stop stream processor ${mockStreamProcessors[0].name}`, {
          exact: false,
        })
      ).toBeInTheDocument();
    });

    it('Renders delete stream processor errors', async () => {
      deleteStreamProcessorStub.mockRejectedValue(new Error('error delete stream processor'));
      mountComponent({ streamProcessors: mockStreamProcessors });

      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      // start the stopped processor
      await user.click(deleteButtons[1]);

      // confirmation modal should render when attempting to delete failed processor
      expect(
        await screen.findByText(`Are you sure you want to delete the processor, ${mockStreamProcessors[1].name}`)
      ).toBeInTheDocument();
      const deleteConfirmationModal = screen.getByTestId(TestSelector.DeleteStreamProcessorModal);
      const confirmationTextbox = within(deleteConfirmationModal).getByRole('textbox');
      const confirmDeleteButton = within(deleteConfirmationModal).getByRole('button', { name: 'Delete' });
      expect(confirmDeleteButton).toBeAriaDisabled();
      await user.type(confirmationTextbox, mockStreamProcessors[1].name);
      expect(confirmDeleteButton).not.toBeAriaDisabled();
      await user.click(confirmDeleteButton);

      const ErrorBanner = screen.getByRole('alert');
      expect(ErrorBanner).toBeInTheDocument();

      expect(
        within(ErrorBanner).getByText(`Failed to delete stream processor ${mockStreamProcessors[1].name}`, {
          exact: false,
        })
      ).toBeInTheDocument();
    });

    it('Handles stop stream processor button actions based on state', async () => {
      mockSettingsModel.getCurrentGroupId = () => GROUP_ID;
      stopStreamProcessorStub.mockImplementation(() => new Promise((resolve) => setTimeout(() => resolve(true), 10)));
      mountComponent({ streamProcessors: mockStreamProcessors });
      const startedIndex = 0;
      const stoppedIndex = 3;
      const provisioningIndex = 4;

      const startedStreamProcessor = mockStreamProcessors[startedIndex];
      const provisioningStreamProcessor = mockStreamProcessors[provisioningIndex];
      const stopButtons = screen.getAllByRole('button', { name: /stop/i });

      expect(startedStreamProcessor.state).toBe(STREAM_PROCESSOR_STATE.STARTED);
      expect(provisioningStreamProcessor.state).toBe(STREAM_PROCESSOR_STATE.PROVISIONING);

      // the stopped processor should have the stop button disabled
      expect(stopButtons[stoppedIndex]).toHaveAttribute('aria-disabled', 'true');
      // the started processor and provisioning processor should not have the stop button disabled
      expect(stopButtons[startedIndex]).not.toHaveAttribute('aria-disabled', 'true');
      expect(stopButtons[provisioningIndex]).not.toHaveAttribute('aria-disabled', 'true');

      expect(stopButtons).toHaveLength(6);

      // stop the started processor
      await user.click(stopButtons[startedIndex]);
      await waitFor(() => {
        expect(screen.getByTestId(TestSelector.StreamProcessorLoadingState)).toBeInTheDocument();
      });

      const table = screen.getByTestId(TestSelector.StreamProcessorsTable);
      const rows = within(table).getAllByRole('row');
      const loadingRow = rows.find((row) => within(row).queryByTestId(TestSelector.StreamProcessorLoadingState));

      const startButton = within(loadingRow!).getByRole('button', { name: 'Start' });
      const deleteButton = within(loadingRow!).getByRole('button', { name: 'Delete' });

      expect(startButton).toHaveAttribute('aria-disabled', 'true');
      expect(deleteButton).toHaveAttribute('aria-disabled', 'true');

      expect(stopStreamProcessorStub).toHaveBeenCalledWith(GROUP_ID, TENANT_NAME, startedStreamProcessor.name);
      await waitFor(() => {
        expect(getStreamInstancesStub).toHaveBeenCalled();
      });
    });

    it('Handles delete stream processor button actions based on state', async () => {
      mockSettingsModel.getCurrentGroupId = () => GROUP_ID;
      deleteStreamProcessorStub.mockImplementation(() => new Promise((resolve) => setTimeout(() => resolve(true), 10)));
      mountComponent({ streamProcessors: mockStreamProcessors });

      const failedStreamProcessor = mockStreamProcessors[1];
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });

      expect(failedStreamProcessor.state).toBe(STREAM_PROCESSOR_STATE.FAILED);
      // the failed processor should have the stop button disabled
      expect(deleteButtons[0]).toHaveAttribute('aria-disabled', 'true');
      expect(deleteButtons).toHaveLength(6);

      // delete the failed processor
      await user.click(deleteButtons[1]);

      // confirmation modal should render when deleting failed processor
      expect(
        await screen.findByText(`Are you sure you want to delete the processor, ${mockStreamProcessors[1].name}`)
      ).toBeInTheDocument();
      const deleteConfirmationModal = screen.getByTestId(TestSelector.DeleteStreamProcessorModal);
      const confirmationTextbox = within(deleteConfirmationModal).getByRole('textbox');
      const confirmDeleteButton = within(deleteConfirmationModal).getByRole('button', { name: 'Delete' });
      expect(confirmDeleteButton).toBeAriaDisabled();
      await user.type(confirmationTextbox, mockStreamProcessors[1].name);
      expect(confirmDeleteButton).not.toBeAriaDisabled();
      await user.click(confirmDeleteButton);

      await waitFor(() => {
        expect(screen.getByTestId(TestSelector.StreamProcessorLoadingState)).toBeInTheDocument();
      });

      expect(deleteStreamProcessorStub).toHaveBeenCalledWith(GROUP_ID, TENANT_NAME, failedStreamProcessor.name);
    });

    it('Renders stream processor errors', async function () {
      mountComponent({ streamProcessors: mockStreamProcessors });

      const errorIcons = screen.getAllByRole('img', { name: /warning icon/i });
      expect(errorIcons).toHaveLength(2);

      await user.click(errorIcons[0]);
      expect(screen.getByText('Something happened')).toBeInTheDocument();

      // dismiss previous tooltip
      await user.click(errorIcons[0]);

      await user.click(errorIcons[1]);
      expect(screen.getByText('Error time')).toBeInTheDocument();
    });

    it('redirects to monitoring page when view monitoring link is clicked', async function () {
      mountComponent({ streamProcessors: mockStreamProcessors });

      const row1 = screen.getAllByTestId(TestSelector.ViewMonitoringLink)[0];
      await user.click(row1);
      expect(useNavigateStub).toHaveBeenCalledWith(`${pageRouteByView(TENANT_NAME)[PageView.Monitoring]}`);
    });
  });

  describe('Connection Registry', function () {
    it('renders the Connection Registry page when the pageView prop equals PageView.Connection', () => {
      mountComponent({ pageView: PageView.Connections });
      expect(screen.getByTestId(TestSelector.ConnectionsView)).toBeInTheDocument();
    });

    it('renders an empty state when there are no connections', async function () {
      mountComponent({ pageView: PageView.Connections, connections: [] });

      expect(screen.getByText('No connections added.')).toBeInTheDocument();
    });

    it('Renders connection registry items', async function () {
      mountComponent({ pageView: PageView.Connections, connections: mockConnections });

      expect(screen.getByText('KafkaDev')).toBeInTheDocument();
      expect(screen.getByText(ProviderToConnectionRegistryType.kafka)).toBeInTheDocument();

      // Atlas
      expect(screen.getByText('AtlasDev')).toBeInTheDocument();
      expect(screen.getAllByText(ProviderToConnectionRegistryType.atlas)[0]).toBeInTheDocument();

      expect(screen.getAllByRole('img', { name: /edit icon/i })).toHaveLength(3);
      expect(screen.getAllByRole('button', { name: /delete/i })).toHaveLength(3);
    });

    it('Links to add connection', async function () {
      mountComponent({ pageView: PageView.Connections, connections: mockConnections });

      await user.click(screen.getByRole('button', { name: /add connection/i }));

      expect(redirect).toHaveBeenCalledWith(
        toHashRoute(Route.STREAMS_ADD_CONNECTION, { tenantName: defaultProps.tenantName })
      );

      expect(analyticsTrackStub).toHaveBeenCalledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
        context: 'Atlas Streams',
        action: 'Clicked "Add connection"',
      });
    });

    it('Links to add connection; cannot click if not group owner', async function () {
      mountComponent({ pageView: PageView.Connections, connections: mockConnections, isGroupOwner: false });

      expect(screen.getByRole('button', { name: /add connection/i })).toBeAriaDisabled();
    });

    it('Links to add connection; cannot click edit or delete if kafka network type is transit gateway', async function () {
      const transitGWConnection: Array<StreamsConnectionReadOnly> = [
        {
          name: 'TransitGatewayDev',
          bootstrapServers: '',
          type: ConnectionRegistryType.KAFKA,
          networking: {
            access: {
              type: ConnectionRegistryNetworkingType.TRANSIT_GATEWAY,
            },
          },
          inUse: false,
        },
      ];
      mountComponent({ pageView: PageView.Connections, connections: transitGWConnection, isGroupOwner: false });

      expect(screen.getByText('TransitGatewayDev')).toBeInTheDocument();

      expect(screen.getByRole('button', { name: /edit/i })).toBeAriaDisabled();
      expect(screen.getByRole('button', { name: /delete/i })).toBeAriaDisabled();
    });

    it('Can delete connection', async function () {
      deleteStreamConnectionStub.mockResolvedValue(true);
      mountComponent({ pageView: PageView.Connections, connections: mockConnections });

      await user.click(screen.getAllByRole('button', { name: /delete/i })[1]);

      expect(
        screen.getByText(`Are you sure you want to delete the connection, ${mockConnections[1].name}?`)
      ).toBeInTheDocument();
      await user.click(screen.getByRole('button', { name: 'Delete' }));

      expect(deleteStreamConnectionStub).toHaveBeenCalledWith(GROUP_ID, TENANT_NAME, mockConnections[1].name);

      expect(analyticsTrackStub).toHaveBeenCalledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
        context: 'Atlas Streams',
        action: 'Deleted Streams Connection',
      });
    });

    it('Cant delete connection when not group owner', async function () {
      mountComponent({ pageView: PageView.Connections, connections: mockConnections, isGroupOwner: false });

      expect(screen.getAllByRole('button', { name: /delete/i })[1]).toBeAriaDisabled();
    });

    it('Delete connection modal closes even if the fetch for connections fails', async function () {
      deleteStreamConnectionStub.mockResolvedValue(true);
      getStreamInstancesStub.mockResolvedValue([defaultInstance]);

      // Use fake timers to stop the poll from fetching the mock rejected value.
      getStreamInstanceStub.mockRejectedValue(new Error('blah'));

      mountComponent({ pageView: PageView.Connections, connections: mockConnections });

      await user.click(screen.getAllByRole('button', { name: /delete/i })[1]);

      expect(
        screen.getByText(`Are you sure you want to delete the connection, ${mockConnections[1].name}?`)
      ).toBeInTheDocument();
      await user.click(screen.getByRole('button', { name: 'Delete' }));

      expect(deleteStreamConnectionStub).toHaveBeenCalledWith(GROUP_ID, TENANT_NAME, mockConnections[1].name);

      expect(analyticsTrackStub).toHaveBeenCalledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
        context: 'Atlas Streams',
        action: 'Deleted Streams Connection',
      });

      await waitForElementToBeRemoved(
        screen.queryByText(`Are you sure you want to delete the connection, ${mockConnections[1].name}?`)
      );

      expect(screen.getByText('Failed to fetch connections.')).toBeInTheDocument();
    });

    describe('Kafka VPC Errors', function () {
      const erroredKafkaVPC: StreamsConnectionReadOnly = {
        name: 'KafkaDev',
        bootstrapServers: '',
        type: ConnectionRegistryType.KAFKA,
        networking: {
          access: {
            type: ConnectionRegistryNetworkingType.VPC,
            name: ERRORED_VPC_ID,
          },
        },
        inUse: false,
      };

      const secondErroredKafka = { ...erroredKafkaVPC, name: 'SecondError' };

      const vpcPeer = {
        '@provider': BackingCloudProvider.AWS,
        id: `1`,
        vpcId: ERRORED_VPC_ID,
        awsAccountId: 'ABCDEFG12345',
        status: 'TERMINATING',
        connectionId: 'test',
        containerId: 'test',
      } as AwsPeerShape;

      it('Does not render an error banner with no failed VPC Peers', async function () {
        mountComponent({ connections: mockConnections, pageView: PageView.Connections });

        expect(screen.queryByRole('alert', { name: /vpc errors/i })).not.toBeInTheDocument();
      });

      it('Does not render an error banner with used available VPC Peer', async function () {
        mountComponent({
          connections: [erroredKafkaVPC],
          vpcPeers: [{ ...vpcPeer, status: 'AVAILABLE' }],
          pageView: PageView.Connections,
        });

        expect(screen.queryByRole('alert', { name: /vpc errors/i })).not.toBeInTheDocument();
      });

      it('renders errors when a used VPC peer is not available', async function () {
        mountComponent({ connections: [erroredKafkaVPC], vpcPeers: [vpcPeer], pageView: PageView.Connections });

        // Test banner text with broken up text content. Focus on data & plurals
        const VPCErrorBanner = screen.getByRole('alert', { name: /vpc errors/i });
        expect(
          within(VPCErrorBanner).getByText('An issue has occurred with the VPC Peer used by', { exact: false })
        ).toBeInTheDocument();
        // Test expected
        expect(within(VPCErrorBanner).getByText('connection', { exact: false })).toBeInTheDocument();
        expect(within(VPCErrorBanner).getByText(erroredKafkaVPC.name, { exact: false })).toBeInTheDocument();
        // Test pluralise
        expect(within(VPCErrorBanner).queryByText('connections', { exact: false })).not.toBeInTheDocument();
        expect(
          within(VPCErrorBanner).queryByText(`${erroredKafkaVPC.name},`, { exact: false })
        ).not.toBeInTheDocument();
      });

      it('renders multiple connection errors', async function () {
        mountComponent({
          connections: [erroredKafkaVPC, secondErroredKafka],
          vpcPeers: [vpcPeer],
          pageView: PageView.Connections,
        });

        // Test banner text with broken up text content. Focus on data & plurals
        const VPCErrorBanner = screen.getByRole('alert', { name: /vpc errors/i });
        expect(
          within(VPCErrorBanner).getByText('An issue has occurred with the VPC Peer used by', { exact: false })
        ).toBeInTheDocument();
        // Test expected
        expect(within(VPCErrorBanner).getByText('connections', { exact: false })).toBeInTheDocument();
        expect(
          within(VPCErrorBanner).getByText(`${erroredKafkaVPC.name}, ${secondErroredKafka.name}`, { exact: false })
        ).toBeInTheDocument();
      });
    });

    const editConnectionTestCases: Array<StreamsConnectionReadOnly> = [
      {
        name: 'KafkaDev',
        bootstrapServers: '',
        type: ConnectionRegistryType.KAFKA,
        networking: {
          access: {
            type: ConnectionRegistryNetworkingType.PUBLIC,
          },
        },
        inUse: false,
      },
      {
        name: 'AtlasDev',
        clusterName: 'Cluster0',
        type: ConnectionRegistryType.ATLAS,
        inUse: false,
      },
      {
        name: 'Sample',
        clusterName: 'Cluster0',
        type: ConnectionRegistryType.SAMPLE,
        inUse: false,
      },
    ];

    it.each(editConnectionTestCases)('Can edit connection', async function (connection: StreamsConnectionReadOnly) {
      sendNewConnectionEventStub.mockResolvedValue(true);
      mountComponent({ pageView: PageView.Connections, connections: [connection] });

      await user.click(screen.getByRole('img', { name: /edit icon/i }));

      expect(redirect).toHaveBeenCalledWith(
        `${toHashRoute(Route.STREAMS_ADD_CONNECTION, { tenantName: defaultProps.tenantName })}?${connection.name}`
      );

      expect(analyticsTrackStub).toHaveBeenCalledWith(SEGMENT_EVENTS.BUTTON_CLICKED, {
        context: 'Atlas Streams',
        action: 'Clicked "Edit connection"',
      });

      if (connection.type === ConnectionRegistryType.KAFKA) {
        expect(localStorageSetItemStub).toHaveBeenCalledWith(HAS_VIEWED_VPC_GUIDE_CUE, true);
      } else {
        expect(localStorageSetItemStub).not.toHaveBeenCalled();
      }
    });

    it('Can edit the Https connection', async function () {
      sendNewConnectionEventStub.mockResolvedValue(true);
      const connection: StreamsConnectionReadOnly = {
        name: 'httpsConn',
        type: ConnectionRegistryType.HTTPS,
        url: 'https://example.com',
        inUse: false,
      };

      const settingsModel = new Settings();
      mountComponent({
        connections: [connection],
        settingsModel,
        pageView: PageView.Connections,
      });

      expect(screen.getByText('httpsConn')).toBeInTheDocument();
      expect(screen.getByText('HTTPS')).toBeInTheDocument();

      await user.click(screen.getByRole('button', { name: /edit/i }));
      expect(redirect).toHaveBeenCalledWith(
        `${toHashRoute(Route.STREAMS_ADD_CONNECTION, { tenantName: defaultProps.tenantName })}?httpsConn`
      );
    });

    it('Cant edit connection when not group owner', async function () {
      mountComponent({ connections: mockConnections, isGroupOwner: false, pageView: PageView.Connections });

      expect(screen.getAllByRole('button', { name: /edit/i })[1]).toBeAriaDisabled();
    });

    it('Does not render network type column when vpc peering not enabled', async function () {
      const settingsModel = new Settings({ GROUP_ENABLED_FEATURE_FLAGS: [] });
      mountComponent({
        connections: mockConnections,
        isGroupOwner: true,
        settingsModel,
        pageView: PageView.Connections,
      });

      expect(screen.queryByRole('columnheader', { name: 'Network Type' })).not.toBeInTheDocument();
    });

    const connectionTypeTestCases: Array<[StreamsConnectionReadOnly, string]> = [
      [
        {
          name: 'kafkaConn0',
          bootstrapServers: '',
          type: ConnectionRegistryType.KAFKA,
          networking: {
            access: {
              type: ConnectionRegistryNetworkingType.PUBLIC,
            },
          },
          inUse: true,
        },
        ConnectionRegistryNetworkingTypeReadable[ConnectionRegistryNetworkingType.PUBLIC],
      ],
      [
        {
          name: 'kafkaConn1',
          bootstrapServers: '',
          type: ConnectionRegistryType.KAFKA,
          networking: {
            access: {
              type: ConnectionRegistryNetworkingType.PUBLIC,
            },
          },
          inUse: true,
        },
        ConnectionRegistryNetworkingTypeReadable[ConnectionRegistryNetworkingType.PUBLIC],
      ],
      [
        {
          name: 'kafkaConn2',
          bootstrapServers: '',
          type: ConnectionRegistryType.KAFKA,
          networking: {
            access: {
              type: ConnectionRegistryNetworkingType.VPC,
              name: 'blah',
            },
          },
          inUse: true,
        },
        ConnectionRegistryNetworkingTypeReadable[ConnectionRegistryNetworkingType.VPC],
      ],
      [
        {
          name: 'atlasConn',
          clusterName: 'cluster',
          type: ConnectionRegistryType.ATLAS,
          inUse: true,
        },
        NetworkTypeAtlasManaged,
      ],
      [
        {
          name: 'KafkaDev',
          clusterName: 'sample',
          type: ConnectionRegistryType.SAMPLE,
          inUse: true,
        },
        NetworkTypeAtlasManaged,
      ],
    ];

    it.each(connectionTypeTestCases)(
      'Renders the correct network type (STREAMS_VPC_PEERING)',
      async (connection: StreamsConnectionReadOnly, expectedNetworkType) => {
        const settingsModel = new Settings({ GROUP_ENABLED_FEATURE_FLAGS: ['STREAMS_VPC_PEERING'] });
        mountComponent({
          connections: [connection],
          isGroupOwner: true,
          settingsModel,
          pageView: PageView.Connections,
        });

        expect(screen.getByRole('columnheader', { name: 'Network Type' })).toBeInTheDocument();
        const cells = within(screen.getByRole('table')).getAllByRole('cell');
        expect(cells[2]).toHaveTextContent(expectedNetworkType);
      }
    );

    it.each(connectionTypeTestCases)(
      'Renders the correct network type (STREAMS_VPC_PEERING_PREVIEW)',
      async (connection: StreamsConnectionReadOnly, expectedNetworkType) => {
        const settingsModel = new Settings({ GROUP_ENABLED_FEATURE_FLAGS: ['STREAMS_VPC_PEERING_PREVIEW'] });
        mountComponent({
          connections: [connection],
          isGroupOwner: true,
          settingsModel,
          pageView: PageView.Connections,
        });

        expect(screen.getByRole('columnheader', { name: 'Network Type' })).toBeInTheDocument();
        const cells = within(screen.getByRole('table')).getAllByRole('cell');
        expect(cells[2]).toHaveTextContent(expectedNetworkType);
      }
    );

    it('displays test connection icon for supported connection types', async function () {
      const supportedConnections: Array<StreamsConnectionReadOnly> = [
        {
          name: 'KafkaConn',
          bootstrapServers: '',
          type: ConnectionRegistryType.KAFKA,
          networking: {
            access: {
              type: ConnectionRegistryNetworkingType.PUBLIC,
            },
          },
          inUse: false,
        },
        {
          name: 'AtlasConn',
          clusterName: 'Cluster0',
          type: ConnectionRegistryType.ATLAS,
          inUse: false,
        },
        {
          name: 'SampleConn',
          clusterName: 'Cluster0',
          type: ConnectionRegistryType.SAMPLE,
          inUse: false,
        },
      ];

      mountComponent({ pageView: PageView.Connections, connections: supportedConnections });

      // Should have test connection icons for all supported types
      expect(screen.getAllByRole('button', { name: /test/i })).toHaveLength(3);
    });

    it('does not display test connection icon for unsupported connection types', async function () {
      const unsupportedConnections: Array<StreamsConnectionReadOnly> = [
        {
          name: 'HttpsConn',
          type: ConnectionRegistryType.HTTPS,
          url: 'https://example.com',
          inUse: false,
        },
      ];

      mountComponent({ pageView: PageView.Connections, connections: unsupportedConnections });

      // Should not have any test connection icons
      expect(screen.queryByRole('button', { name: /test/i })).not.toBeInTheDocument();
    });

    it('opens test connection modal when test button is clicked for Sample connection', async function () {
      const sampleConnection: Array<StreamsConnectionReadOnly> = [
        {
          name: 'SampleConn',
          clusterName: 'Cluster0',
          type: ConnectionRegistryType.SAMPLE,
          inUse: false,
        },
      ];

      mountComponent({ pageView: PageView.Connections, connections: sampleConnection });

      await user.click(screen.getByRole('button', { name: /test/i }));

      // Modal should open with title
      expect(screen.getByText('Test connection: SampleConn')).toBeInTheDocument();

      // Should have Cancel and Test Connection buttons
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /test connection/i })).toBeInTheDocument();
    });

    it('opens test connection modal for Atlas connection with required fields and proper validation', async function () {
      const atlasConnection: Array<StreamsConnectionReadOnly> = [
        {
          name: 'AtlasConn',
          clusterName: 'Cluster0',
          type: ConnectionRegistryType.ATLAS,
          inUse: false,
        },
      ];

      mountComponent({ pageView: PageView.Connections, connections: atlasConnection });

      await user.click(screen.getByRole('button', { name: /test/i }));

      // Modal should open with title
      expect(screen.getByText('Test connection: AtlasConn')).toBeInTheDocument();

      // Should have input fields for database and collection
      expect(screen.getByLabelText('Database Name')).toBeInTheDocument();
      expect(screen.getByLabelText('Collection Name')).toBeInTheDocument();

      // Should have Cancel and Test Connection buttons
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
      const testConnectionButton = screen.getByRole('button', { name: /test connection/i });
      expect(testConnectionButton).toBeInTheDocument();

      // Test connection button should be disabled initially
      expect(testConnectionButton).toBeAriaDisabled();

      // Fill in only database name - button should still be disabled
      await user.type(screen.getByLabelText('Database Name'), 'testdb');
      expect(testConnectionButton).toBeAriaDisabled();

      // Fill in collection name - button should now be enabled
      await user.type(screen.getByLabelText('Collection Name'), 'testcoll');
      expect(testConnectionButton).not.toBeAriaDisabled();
    });

    it('opens test connection modal for Kafka connection with required field and proper validation', async function () {
      const kafkaConnection: Array<StreamsConnectionReadOnly> = [
        {
          name: 'KafkaConn',
          bootstrapServers: '',
          type: ConnectionRegistryType.KAFKA,
          networking: {
            access: {
              type: ConnectionRegistryNetworkingType.PUBLIC,
            },
          },
          inUse: false,
        },
      ];

      mountComponent({ pageView: PageView.Connections, connections: kafkaConnection });

      await user.click(screen.getByRole('button', { name: /test/i }));

      // Modal should open with title
      expect(screen.getByText('Test connection: KafkaConn')).toBeInTheDocument();

      // Should have input field for topic name
      expect(screen.getByLabelText('Topic Name')).toBeInTheDocument();

      // Should have Cancel and Test Connection buttons
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
      const testConnectionButton = screen.getByRole('button', { name: /test connection/i });
      expect(testConnectionButton).toBeInTheDocument();

      // Test connection button should be disabled initially
      expect(testConnectionButton).toBeAriaDisabled();

      // Fill in topic name - button should now be enabled
      await user.type(screen.getByLabelText('Topic Name'), 'test-topic');
      expect(testConnectionButton).not.toBeAriaDisabled();
    });

    it('does not display error banner when isTesting is true', async function () {
      const sampleConnection: Array<StreamsConnectionReadOnly> = [
        {
          name: 'SampleConn',
          clusterName: 'Cluster0',
          type: ConnectionRegistryType.SAMPLE,
          inUse: false,
        },
      ];

      // Mock the test connection API to simulate testing state
      const testConnectionStub = jest.spyOn(api.nds.streams, 'testStreamConnection');
      testConnectionStub.mockImplementation(() => new Promise(() => {})); // Never resolves to keep isTesting true

      mountComponent({ pageView: PageView.Connections, connections: sampleConnection });

      await user.click(screen.getByRole('button', { name: /test/i }));

      // Click the test connection button to start testing
      const testConnectionButton = screen.getByRole('button', { name: /test connection/i });
      await user.click(testConnectionButton);

      // Even if there's an error, the banner should not display while isTesting is true
      const alerts = screen.getAllByRole('alert');
      expect(alerts).toHaveLength(1);
      expect(alerts[0]).toHaveTextContent('Testing connection...');
    });

    it('does not display success banner when isTesting is true', async function () {
      const sampleConnection: Array<StreamsConnectionReadOnly> = [
        {
          name: 'SampleConn',
          clusterName: 'Cluster0',
          type: ConnectionRegistryType.SAMPLE,
          inUse: false,
        },
      ];

      // Mock the test connection API to simulate testing state
      const testConnectionStub = jest.spyOn(api.nds.streams, 'testStreamConnection');
      testConnectionStub.mockImplementation(() => new Promise(() => {})); // Never resolves to keep isTesting true

      mountComponent({ pageView: PageView.Connections, connections: sampleConnection });

      await user.click(screen.getByRole('button', { name: /test/i }));

      // Click the test connection button to start testing
      const testConnectionButton = screen.getByRole('button', { name: /test connection/i });
      await user.click(testConnectionButton);

      // Success banner should not display while isTesting is true, even if testResult exists
      expect(screen.queryByText('Connection test successful')).not.toBeInTheDocument();
    });

    it('does not display success banner when testError has content', async function () {
      const sampleConnection: Array<StreamsConnectionReadOnly> = [
        {
          name: 'SampleConn',
          clusterName: 'Cluster0',
          type: ConnectionRegistryType.SAMPLE,
          inUse: false,
        },
      ];

      // Mock the test connection API to reject with an error
      const testConnectionStub = jest.spyOn(api.nds.streams, 'testStreamConnection');
      testConnectionStub.mockRejectedValue(new Error('Connection failed'));

      mountComponent({ pageView: PageView.Connections, connections: sampleConnection });

      await user.click(screen.getByRole('button', { name: /test/i }));

      // Click the test connection button to trigger the error
      const testConnectionButton = screen.getByRole('button', { name: /test connection/i });
      await user.click(testConnectionButton);

      // Wait for the error to be processed
      await waitFor(() => {
        // Success banner should not display when there's an error (testError?.length > 0)
        expect(screen.queryByText('Connection test successful')).not.toBeInTheDocument();
      });
    });
  });

  describe('Kafka connection guide cue', function () {
    const connectionListWithMultipleKafka: Array<StreamsConnectionReadOnly> = [
      {
        name: 'KafkaDev',
        bootstrapServers: '',
        type: ConnectionRegistryType.KAFKA,
        networking: {
          access: {
            type: ConnectionRegistryNetworkingType.PUBLIC,
          },
        },
        inUse: true,
      },
      {
        name: 'AtlasDev',
        clusterName: 'Cluster0',
        type: ConnectionRegistryType.ATLAS,
        inUse: false,
      },
      {
        name: 'KafkaDev2',
        bootstrapServers: '',
        type: ConnectionRegistryType.KAFKA,
        networking: {
          access: {
            type: ConnectionRegistryNetworkingType.PUBLIC,
          },
        },
        inUse: false,
      },
    ];
    const settingsModel = new Settings({ GROUP_ENABLED_FEATURE_FLAGS: ['STREAMS_VPC_PEERING'] });

    it('renders once when there are multiple', async function () {
      mountComponent({
        connections: connectionListWithMultipleKafka,
        isGroupOwner: true,
        settingsModel,
        pageView: PageView.Connections,
      });

      const guideCues = screen.getAllByRole('dialog');
      expect(guideCues.length).toBe(1);
      expect(within(guideCues[0]).getByRole('heading', { name: 'Introducing VPC Peering' }));
    });

    it('can be dismissed', async function () {
      mountComponent({
        connections: connectionListWithMultipleKafka,
        isGroupOwner: true,
        settingsModel,
        pageView: PageView.Connections,
      });

      const guideCue = screen.getByRole('dialog');

      await user.click(within(guideCue).getByRole('button'));
      expect(localStorageWrapper.setItem).toHaveBeenCalledWith(HAS_VIEWED_VPC_GUIDE_CUE, true);
      await waitFor(() => expect(screen.queryByRole('dialog')).not.toBeInTheDocument());
    });

    it('cannot be seen when tab is switched', async function () {
      mountComponent({
        connections: connectionListWithMultipleKafka,
        isGroupOwner: true,
        settingsModel: settingsModel,
        pageView: PageView.Processors,
      });

      expect(localStorageWrapper.setItem).not.toHaveBeenCalledWith(HAS_VIEWED_VPC_GUIDE_CUE, true);
      await waitFor(() => expect(screen.queryByRole('dialog')).not.toBeInTheDocument());
    });

    it('does not render when feature flag is off', async function () {
      mountComponent({
        connections: connectionListWithMultipleKafka,
        isGroupOwner: true,
        settingsModel: new Settings(),
        pageView: PageView.Connections,
      });

      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    it('does not render when local storage key is present', async function () {
      jest.spyOn(localStorageWrapper, 'getItem').mockReturnValue('true');
      mountComponent({
        connections: connectionListWithMultipleKafka,
        isGroupOwner: true,
        settingsModel,
        pageView: PageView.Connections,
      });

      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    it('does not render when landing on the default Stream Processors tab', async function () {
      mountComponent({
        connections: connectionListWithMultipleKafka,
        isGroupOwner: true,
        settingsModel,
        pageView: PageView.Processors,
      });

      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });

    it('does not render when no existing kafka connections', async function () {
      mountComponent({
        connections: [],
        isGroupOwner: true,
        settingsModel,
        pageView: PageView.Connections,
      });

      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      await waitFor(() => expect(localStorageWrapper.setItem).toHaveBeenCalledWith(HAS_VIEWED_VPC_GUIDE_CUE, true));
    });

    it('does not render when there is an existing VPC kafka connection', async function () {
      const kafkaConnections: Array<StreamsConnectionReadOnly> = [
        {
          name: 'conn1',
          bootstrapServers: '',
          type: ConnectionRegistryType.KAFKA,
          networking: {
            access: {
              type: ConnectionRegistryNetworkingType.VPC,
              name: '123',
            },
          },
          inUse: false,
        },
        {
          name: 'conn2',
          bootstrapServers: '',
          type: ConnectionRegistryType.KAFKA,
          networking: {
            access: {
              type: ConnectionRegistryNetworkingType.PUBLIC,
            },
          },
          inUse: false,
        },
      ];

      mountComponent({
        connections: kafkaConnections,
        isGroupOwner: true,
        settingsModel: settingsModel,
        pageView: PageView.Connections,
      });

      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      await waitFor(() => expect(localStorageWrapper.setItem).toHaveBeenCalledWith(HAS_VIEWED_VPC_GUIDE_CUE, true));
    });
  });

  describe('Monitoring', function () {
    it('shows the monitoring tab when in the old nav', function () {
      mountComponent({
        showCloudNav: false,
      });
      expect(screen.getByRole('tab', { name: /monitoring/i })).toBeVisible();
    });

    it('renders the Monitoring page when the pageView prop equals PageView.Monitoring', function () {
      mountComponent({ pageView: PageView.Monitoring });
      expect(screen.getByTestId(TestSelector.MonitoringView)).toBeInTheDocument();
    });

    it('renders an empty state when there are no stream processors', function () {
      mountComponent({ pageView: PageView.Monitoring, streamProcessors: [] });
      expect(screen.getByTestId(TestSelector.MonitoringEmptyState)).toBeInTheDocument();
    });
  });

  describe('With new nav', () => {
    const realWindowLocation = window.location;

    beforeEach(() => {
      Object.defineProperty(window, 'location', {
        writable: true,
        value: {
          ...window.location,
        },
      });
      window.location.href = `https://cloud.example.com/v2/${GROUP_ID}#${pageRouteByView(TENANT_NAME)[PageView.Processors]}`;
    });

    afterEach(() => {
      //@ts-ignore
      window.location = realWindowLocation;
    });

    it('renders the Stream Processors and Connection Registry side nav items', async () => {
      mountComponent({ showCloudNav: true });
      act(() => jest.advanceTimersByTime(2000));
      await waitForLayoutMount();

      expect(screen.getByTestId(`${CloudNavElementIds.sideNav.secondary.item}-processors`)).toBeInTheDocument();
      expect(screen.getByTestId(`${CloudNavElementIds.sideNav.secondary.item}-connections`)).toBeInTheDocument();
    });

    it('renders the Monitoring side nav item', async () => {
      mountComponent({
        showCloudNav: true,
      });
      act(() => jest.advanceTimersByTime(2000));
      await waitForLayoutMount();

      expect(screen.getByTestId(`${CloudNavElementIds.sideNav.secondary.item}-monitoring`)).toBeInTheDocument();
    });
  });
});
