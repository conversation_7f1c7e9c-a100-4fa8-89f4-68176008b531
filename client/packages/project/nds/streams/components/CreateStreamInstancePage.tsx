import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { css } from '@emotion/react';
import styled from '@emotion/styled';
import Badge, { Variant } from '@leafygreen-ui/badge';
import Banner from '@leafygreen-ui/banner';
import But<PERSON>, { Size as ButtonSize, Variant as <PERSON><PERSON>Varian<PERSON> } from '@leafygreen-ui/button';
import { palette } from '@leafygreen-ui/palette';
import { RadioBox, RadioBoxGroup, Size } from '@leafygreen-ui/radio-box-group';
import TextInput from '@leafygreen-ui/text-input';
import { Toast, Variant as ToastVariant } from '@leafygreen-ui/toast';
import { BaseFontSize } from '@leafygreen-ui/tokens';
import { Body, bodyTypeScaleStyles, Disclaimer, Link, Overline, Subtitle } from '@leafygreen-ui/typography';
import { useDispatch, useSelector } from 'react-redux';
import { omit } from 'underscore';

import { WindowLocation } from '@packages/types/browser';
import { BackingCloudProvider, CloudProvider } from '@packages/types/nds/provider';
import { Region } from '@packages/types/nds/region';
import {
  DISABLED_TIERS_COMINGSOON_DESCRIPTION,
  STREAM_INSTANCE_DESCRIPTIONS,
  STREAM_INSTANCE_MEMORY,
  STREAM_INSTANCE_TIERS,
  STREAM_INSTANCE_VCPU,
  STREAM_INSTANCE_WORKERS,
  StreamsPurchaseTransactionBillingEstimate,
  StreamsTiersStrikethroughPrice,
} from '@packages/types/nds/streams';

import * as purchase from '@packages/redux/common/purchase';
import { getStreamAvailableRegions, getStreamInstances } from '@packages/redux/common/nds/streams';
import { isWorkspacesEnabled } from '@packages/redux/common/settings';
import { AppDispatch } from '@packages/redux/project/appRootReducer';

import * as api from '@packages/common/services/api';
import * as clusterDescriptionUtils from '@packages/common/utils/clusterDescription';
import { Permission, PermissionBanner, useAuthz } from '@packages/authz';
import TemplatesPage from '@packages/cluster-editor/TemplatesPage';
import { PAYMENT_PAGE_TEAM } from '@packages/common/constants/errorTracker';
import { exceptionToMessage } from '@packages/common/services/errorHelper';
import { isNameUnused } from '@packages/common/utils/isNameUnused';
import {
  getCheckoutUrl,
  PurchaseType,
  shouldStreamInstanceCreationGoThroughPaymentPage,
} from '@packages/common/utils/paymentPageHelpers';
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import { sendError } from '@packages/observability';
import ProviderSelector from '@packages/project/nds/clusters/components/templates/ProviderSelector';
import RegionDropdown from '@packages/project/nds/clusters/components/templates/RegionDropdown';
import { Route, toHashRoute } from '@packages/project/nds/streams/routes';

import StreamsFeedbackModal from './StreamsFeedbackModel';

export const enum TestSelector {
  InstanceTierRadioBox = 'instance-tier-radio-box',
  WorkspaceName = 'workspace-name',
  CreateButton = 'create-button',
}

const STARTER_FOOTER_HEIGHT = '176px';

const CreateInstanceWrapper = styled.div`
  height: 100%;
`;

const Row = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
`;

const Column = styled.div`
  display: flex;
  flex-direction: column;
  text-align: left;
`;

const RadioBoxColumn = styled.div`
  display: flex;
  flex-direction: column;
  padding: 8px 0px;
  gap: 16px;
`;

const Divider = styled.div`
  width: 100%;
  margin: 32px 0px;
  border-bottom: 1px ${palette.gray.light1} solid;
`;

const styles = {
  radioBoxGroupTopMargin: css`
    margin-top: 32px;
    gap: 42px;
  `,
  radioBoxOverrides: css`
    width: 100%;
  `,
  tierDetailsColumn: css`
    border-top: 1px ${palette.gray.light1} solid;
    padding-top: 16px;
    gap: 6px;
  `,
  titleColumnGap: css`
    gap: 8px;
  `,
  detailsColumnGap: css`
    gap: 6px;
  `,
  formColumnGap: css`
    gap: 40px;
  `,
  footerCreateButton: css`
    width: 260px;
  `,
  cancelButton: css`
    color: ${palette.gray.dark1};
    text-decoration: underline;
  `,
  bannerOverrides: css`
    width: 700px;
  `,
  spiDescriptionBody: css`
    width: calc(100% + 10px);
  `,
  priceDisclaimerGap: css`
    gap: 4px;
  `,
  shareFeedbackButtonStyle: css`
    position: absolute;
    top: 0px;
    right: 0px;
    transform: translate(0, -50%);
    border-radius: 38px;
    background-color: ${palette.black};
    color: white;
    font-weight: 600;
    padding: 16px;
    height: 52px;
    box-shadow: 0px 4px 10px -4px ${palette.black};
  `,
};

const StyledRadioBox = styled(RadioBox)`
  width: 100%;
  max-width: 290px;

  > div {
    width: 100%;
    max-width: 300px;
  }
`;

const TemplateFooterWrapper = styled.footer`
  display: block;
  width: 100%;
  position: sticky;
  bottom: 0px;
  background-color: ${palette.white};
`;

const TemplateFooterInnerWrapper = styled.div`
  max-width: 1068px;
  margin: 0 auto;
  padding: 24px 0;
  display: flex;
  flex-direction: column;
  align-items: baseline;
  gap: 16px;
  position: relative;
`;

const ButtonRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;

interface PriceColumnProps {
  color?: string;
  oldPrice?: number;
}

const PriceColumn = styled.div<PriceColumnProps>`
  text-align: right;
  font-size: 16px;
  color: ${(props) => props.color || palette.gray.dark2};
  margin-left: auto;

  /* Additional styles for the strikethrough price */
  &::before {
    content: '${(props) => (props.oldPrice ? ` $${props.oldPrice.toFixed(2)}` : '')}';
    color: ${palette.gray.base};
    text-decoration: line-through;
    margin-right: 4px;
  }
`;

interface Props {
  groupId: string;
  orgId: string;
  hasAccessToCreateStreamsInstance: boolean;
  windowLocation?: WindowLocation;
  selfServePaymentsEnabled: boolean;
  hasEffectivePayment: boolean;
  isSP10Enabled: boolean;
  transactionId?: string;
}

export const CreateStreamInstancePage = ({
  groupId,
  orgId,
  hasAccessToCreateStreamsInstance,
  selfServePaymentsEnabled,
  hasEffectivePayment,
  isSP10Enabled,
  transactionId = '',
  windowLocation = window.location,
}: Props) => {
  const dispatch: AppDispatch = useDispatch();

  // Form UI
  const [createInstanceEnabled, setCreateInstanceEnabled] = useState(false);
  const [isNameValid, setIsNameValid] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [createInstanceErrorMessage, setCreateInstanceErrorMessage] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Instance details
  const [instanceName, setInstanceName] = useState('');
  const [provider, setProvider] = useState<BackingCloudProvider>(BackingCloudProvider.AWS);
  const [providerRegions, setProviderRegions] = useState<Array<Region>>([]);
  const [region, setRegion] = useState<Region>();
  const [tier, setTier] = useState<STREAM_INSTANCE_TIERS>(STREAM_INSTANCE_TIERS.SP30);
  const [instance, setInstance] = useState({
    name: '',
    dataProcessRegion: { cloudProvider: CloudProvider.AWS, region: '' },
    groupId,
    streamConfig: {
      tier,
    },
  });
  const streamWorkspace = useSelector(isWorkspacesEnabled) ? 'Workspace' : 'Instance';
  const streamWorkspaceString = useSelector(isWorkspacesEnabled)
    ? "This name can't be changed after a workspace has been created."
    : "This name can't be changed after an instance has been created.";
  const streamWorkspaceTitle = useSelector(isWorkspacesEnabled)
    ? 'Create a stream processing workspace'
    : 'Create a stream processing instance';
  // FGA
  const requiredPermissions = [Permission.CLOUD_PROJECT_STREAM_PROCESSING_INSTANCES_CREATE(groupId)];
  const { isAuthorized } = useAuthz(requiredPermissions, true);

  // Template init
  const STREAM_INSTANCE_BADGES = useMemo(() => {
    return {
      [STREAM_INSTANCE_TIERS.SP10]: isSP10Enabled ? undefined : 'COMING SOON',
      [STREAM_INSTANCE_TIERS.SP30]: isSP10Enabled ? undefined : 'PREVIEW',
      [STREAM_INSTANCE_TIERS.SP50]: 'COMING SOON',
    };
  }, [isSP10Enabled]);

  const ENABLED_TIERS = useMemo(() => {
    return [STREAM_INSTANCE_TIERS.SP30, isSP10Enabled ? STREAM_INSTANCE_TIERS.SP10 : undefined];
  }, [isSP10Enabled]);

  const INSTANCE_TEMPLATES = useMemo(() => {
    return [
      {
        value: STREAM_INSTANCE_TIERS.SP10,
        title: STREAM_INSTANCE_TIERS.SP10,
        description: STREAM_INSTANCE_DESCRIPTIONS[STREAM_INSTANCE_TIERS.SP10],
        streamInstanceTemplateDetails: {
          workers: STREAM_INSTANCE_WORKERS[STREAM_INSTANCE_TIERS.SP10],
          ram: STREAM_INSTANCE_MEMORY[STREAM_INSTANCE_TIERS.SP10],
          cpu: STREAM_INSTANCE_VCPU[STREAM_INSTANCE_TIERS.SP10],
        },
        badge: STREAM_INSTANCE_BADGES[STREAM_INSTANCE_TIERS.SP10],
      },
      {
        value: STREAM_INSTANCE_TIERS.SP30,
        title: STREAM_INSTANCE_TIERS.SP30,
        description: STREAM_INSTANCE_DESCRIPTIONS[STREAM_INSTANCE_TIERS.SP30],
        streamInstanceTemplateDetails: {
          workers: STREAM_INSTANCE_WORKERS[STREAM_INSTANCE_TIERS.SP30],
          ram: STREAM_INSTANCE_MEMORY[STREAM_INSTANCE_TIERS.SP30],
          cpu: STREAM_INSTANCE_VCPU[STREAM_INSTANCE_TIERS.SP30],
        },
        badge: STREAM_INSTANCE_BADGES[STREAM_INSTANCE_TIERS.SP30],
      },
      {
        value: STREAM_INSTANCE_TIERS.SP50,
        title: STREAM_INSTANCE_TIERS.SP50,
        description: STREAM_INSTANCE_DESCRIPTIONS[STREAM_INSTANCE_TIERS.SP50],
        streamInstanceTemplateDetails: {
          workers: STREAM_INSTANCE_WORKERS[STREAM_INSTANCE_TIERS.SP50],
          ram: STREAM_INSTANCE_MEMORY[STREAM_INSTANCE_TIERS.SP50],
          cpu: STREAM_INSTANCE_VCPU[STREAM_INSTANCE_TIERS.SP50],
        },
        badge: STREAM_INSTANCE_BADGES[STREAM_INSTANCE_TIERS.SP50],
      },
    ];
  }, [STREAM_INSTANCE_BADGES]);

  // Hard-coded at the moment
  const STREAMS_TIERS_STRIKETHROUGH_PRICE: StreamsTiersStrikethroughPrice = {
    ...(isSP10Enabled ? {} : { [STREAM_INSTANCE_TIERS.SP30]: 0.39 }),
  };

  // Costs
  const [billingEstimateForTransaction, setBillingEstimateForTransaction] =
    useState<StreamsPurchaseTransactionBillingEstimate>({ dailyEstimateCents: 0, monthlyEstimateCents: 0 });
  const [cost, setCost] = useState('');

  const [billingEstimateForAllTiers, setBillingEstimateForAllTiers] = useState();
  const tierMapping = useRef({
    SP10: { daily: 'dailyEstimateCentsSP10', monthly: 'monthlyEstimateCentsSP10' },
    SP30: { daily: 'dailyEstimateCentsSP30', monthly: 'monthlyEstimateCentsSP30' },
    SP50: { daily: 'dailyEstimateCentsSP50', monthly: 'monthlyEstimateCentsSP50' },
  });

  const regions = useSelector(getStreamAvailableRegions);
  const streamInstanceNames = useSelector(getStreamInstances).map((si) => si.name);
  const allowedProviders = regions.reduce((acc, r) => {
    acc.add(r.provider as BackingCloudProvider);
    return acc;
  }, new Set<BackingCloudProvider>());

  // load transaction from Full Page Payment
  // Streams does not yet store interim configurations in the DB so there's no need to attempt
  // a recovery deeper then a redux fetch.
  const purchaseTransaction = useSelector((state: any) =>
    purchase.getStreamInstanceTransactionConfigurationObjectForActiveOrg(state, { groupId, transactionId })
  );

  // sets the default state of the page, taking stream instance transactions into account
  useEffect(() => {
    const setDefaultState = () => {
      // Load data from cluster transaction when one is available
      const transactionStreamInstance = purchaseTransaction?.streamInstance;

      const _regionToFind = transactionStreamInstance?.dataProcessRegion?.region;
      const _region = regions.find((r) => r.key === _regionToFind) || region;
      const _instance = transactionStreamInstance ?? instance;
      const _provider = transactionStreamInstance?.dataProcessRegion?.cloudProvider ?? provider;
      const _tier = transactionStreamInstance?.streamConfig?.tier ?? STREAM_INSTANCE_TIERS.SP30;
      const _name = transactionStreamInstance?.name ?? '';

      setRegion(_region);
      // @ts-ignore BackingCloudProvider vs CloudProvider (We only return AWS, in future Azure)
      setProvider(_provider);
      setInstance(_instance);
      setTier(_tier);
      setInstanceName(_name);
    };
    setDefaultState();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Filter regions by cloud provider
  useEffect(() => {
    const regionOptions = regions.filter((r) => r.provider === provider);
    setProviderRegions(regionOptions);
  }, [provider, regions]);

  // set Default region
  useEffect(() => {
    const _defaultRegion =
      providerRegions.find((r) => r.key === 'US_EAST_1' && r.provider === CloudProvider.AWS) ?? providerRegions[0];
    setRegion(_defaultRegion);
  }, [providerRegions]);

  const setNameFromEvent: React.ChangeEventHandler<HTMLInputElement> = useCallback((e) => {
    const updatedValue = e.target.value;
    setInstanceName(updatedValue);
  }, []);

  const setInstanceTier: React.ChangeEventHandler<HTMLInputElement> = useCallback((e) => {
    const updatedValue = e.target.value as STREAM_INSTANCE_TIERS;
    setTier(updatedValue);
  }, []);

  const setProviderWithAnalytics = useCallback(
    (_provider: BackingCloudProvider) => {
      analytics.track(SEGMENT_EVENTS.BUTTON_CLICKED, {
        context: 'Atlas Streams',
        action: 'Set a Cloud Provider while creating an Instance',
        cloud_provider: _provider,
      });
      setProvider(_provider);
    },
    [setProvider]
  );

  const setRegionWithAnalytics = useCallback(
    (_region: Region) => {
      analytics.track(SEGMENT_EVENTS.BUTTON_CLICKED, {
        context: 'Atlas Streams',
        action: 'Set a Cloud Region while creating an Instance',
        region: _region.key,
      });
      setRegion(_region);
    },
    [setRegion]
  );

  const returnToLayout = useCallback(() => {
    windowLocation.assign(toHashRoute(Route.STREAMS_LIST));
  }, [windowLocation]);

  const createInstance = useCallback(async () => {
    setIsSaving(true);
    try {
      const createdStreamInstance = await api.nds.streams.saveStreamInstance(groupId, instance);

      analytics.track(SEGMENT_EVENTS.BUTTON_CLICKED, {
        context: 'Atlas Streams',
        action: 'Created a Stream Instance',
        region: instance.dataProcessRegion.region,
        cloud_provider: instance.dataProcessRegion.cloudProvider,
        tier: instance.streamConfig.tier,
        tenant_id: createdStreamInstance._id,
      });

      returnToLayout();
    } catch (e) {
      setCreateInstanceErrorMessage(exceptionToMessage(e));
    }
    setIsSaving(false);
  }, [setCreateInstanceErrorMessage, instance, groupId, returnToLayout]);

  // Check if instance name is valid
  useEffect(() => {
    const isNameAlphanumericHyphen = clusterDescriptionUtils.isNameValid(instanceName);
    const isNameUnique = isNameUnused(instanceName, streamInstanceNames);
    const hasNameError = (instanceName.length > 0 && !isNameAlphanumericHyphen) || !isNameUnique;

    setIsNameValid(!hasNameError);

    if (!isNameUnique) {
      setErrorMessage(`${streamWorkspace} names must be unique`);
    }

    if (!isNameAlphanumericHyphen) {
      setErrorMessage(`Stream ${streamWorkspace} names can only contain ASCII letters, numbers, and hyphens.`);
    }
  }, [streamWorkspace, instanceName, streamInstanceNames]);

  // Prepare instance object to send
  useEffect(() => {
    const _derivedInstance = {
      name: instanceName,
      dataProcessRegion: { cloudProvider: CloudProvider[provider], region: region?.key || '' },
      groupId,
      streamConfig: {
        tier,
      },
    };

    setInstance(_derivedInstance);
  }, [instanceName, region, provider, tier, groupId]);

  useEffect(() => {
    async function fetchPrices() {
      // omit streamConfig since we do not need to pass in tier
      const instanceWithoutStreamConfig = omit(instance, 'streamConfig');

      const billingEstimate = await api.nds.streams.costEstimate(instanceWithoutStreamConfig);
      setBillingEstimateForAllTiers(billingEstimate);
    }

    if (instance.dataProcessRegion.region !== '') {
      fetchPrices();
    }
  }, [instance]);

  useEffect(() => {
    if (billingEstimateForAllTiers) {
      const selectedTier = instance.streamConfig.tier;
      const { daily, monthly } = tierMapping.current[selectedTier];

      const billingEstimate = {
        dailyEstimateCents: billingEstimateForAllTiers[daily],
        monthlyEstimateCents: billingEstimateForAllTiers[monthly],
      };

      setBillingEstimateForTransaction(billingEstimate);
    }
  }, [billingEstimateForAllTiers, instance]);

  // Enable/Disable submit button
  useEffect(() => {
    setCreateInstanceEnabled(
      provider.length > 0 &&
        region !== undefined &&
        region.key?.length > 0 &&
        instanceName.length > 0 &&
        isNameValid &&
        ENABLED_TIERS.includes(tier)
    );
  }, [provider, region, tier, instanceName, isNameValid, ENABLED_TIERS]);

  // Set price
  useEffect(() => {
    if (billingEstimateForTransaction !== undefined && billingEstimateForTransaction?.dailyEstimateCents !== null) {
      setCost(`$${(billingEstimateForTransaction?.dailyEstimateCents / 24 / 100).toFixed(2)}/hr (per worker)`);
    } else {
      setCost('');
    }
  }, [billingEstimateForTransaction]);

  const redirectToFPP = useCallback(async () => {
    const newTransactionId = dispatch(
      purchase.storeStreamInstanceTransactionForCurrentGroup({
        billingEstimate: billingEstimateForTransaction,
        streamInstance: instance,
        desiredInstanceSize: tier,
        isEdit: false,
      })
    );

    if (newTransactionId == null) {
      sendError({
        error: new Error('Failed to store to Redux/sessionStorage (Atlas Stream Processing)'),
        team: PAYMENT_PAGE_TEAM,
      });
      return;
    }

    windowLocation.assign(
      getCheckoutUrl({
        type: PurchaseType.StreamInstance,
        orgId,
        groupId,
        transactionId: newTransactionId,
      })
    );
  }, [billingEstimateForTransaction, instance, tier, dispatch, groupId, orgId, windowLocation]);

  const onCreateStreamInstanceClicked = useCallback(async () => {
    setIsSaving(true);
    if (
      shouldStreamInstanceCreationGoThroughPaymentPage({
        selfServePaymentsEnabled,
        hasEffectivePaymentMethod: hasEffectivePayment,
      })
    ) {
      await redirectToFPP();
    } else {
      await createInstance();
    }
  }, [selfServePaymentsEnabled, hasEffectivePayment, redirectToFPP, createInstance]);

  const getHourlyEstimate = (selectedTier: STREAM_INSTANCE_TIERS): string => {
    if (!billingEstimateForAllTiers) return '';
    const tierValue = tierMapping.current[selectedTier];
    const amountInHourlyDollars = billingEstimateForAllTiers[tierValue.daily] / (24 * 100);

    return `$${amountInHourlyDollars.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  };
  return (
    <CreateInstanceWrapper>
      <TemplatesPage
        title={streamWorkspaceTitle}
        footerHeight={STARTER_FOOTER_HEIGHT}
        description={
          <Body className={bodyTypeScaleStyles[BaseFontSize.Body2]} css={styles.spiDescriptionBody}>
            <strong>Pay-as-you go!</strong> You’ll be billed hourly when you have at least 1 running processor. You can
            edit or terminate at any time. Excludes{' '}
            <Link
              hideExternalIcon
              target="_blank"
              href="https://www.mongodb.com/docs/atlas/billing/stream-processing-costs/#data-transfer"
              baseFontSize={BaseFontSize.Body2}
            >
              variable costs
            </Link>
            .
          </Body>
        }
      >
        <RadioBoxGroup css={styles.radioBoxGroupTopMargin} onChange={setInstanceTier}>
          {INSTANCE_TEMPLATES.map((template) => {
            return (
              <StyledRadioBox
                value={template.value}
                checked={tier === template.value}
                size={Size.Full}
                key={template.value}
              >
                <RadioBoxColumn data-testid={TestSelector.InstanceTierRadioBox + template.value}>
                  <Column css={styles.titleColumnGap}>
                    <Row>
                      <Subtitle>{template.title}</Subtitle>
                      {template.badge && (
                        <Badge variant={template.badge === 'PREVIEW' ? Variant.Green : Variant.LightGray}>
                          {template.badge}
                        </Badge>
                      )}
                      <PriceColumn
                        color={
                          Object.keys(STREAMS_TIERS_STRIKETHROUGH_PRICE).includes(template.value)
                            ? palette.green.dark1
                            : palette.gray.dark2
                        }
                        oldPrice={STREAMS_TIERS_STRIKETHROUGH_PRICE[template.value]}
                      >
                        {getHourlyEstimate(template.value)}/hr
                      </PriceColumn>
                    </Row>
                    <Body>{template.description}</Body>
                  </Column>
                  <Column css={styles.tierDetailsColumn}>
                    <Overline>Resources Per Worker</Overline>
                    <Body weight="medium">
                      {template.streamInstanceTemplateDetails.ram} RAM, {template.streamInstanceTemplateDetails.cpu}
                    </Body>
                  </Column>
                </RadioBoxColumn>
              </StyledRadioBox>
            );
          })}
        </RadioBoxGroup>
        <Divider />
        {ENABLED_TIERS.includes(tier) ? (
          <Column css={styles.formColumnGap}>
            <ProviderSelector
              selectedProvider={provider}
              onSelect={setProviderWithAnalytics}
              availableProviders={Array.from(allowedProviders)}
            />
            <RegionDropdown
              availableRegions={providerRegions}
              selectedRegion={region}
              onRegionSelected={setRegionWithAnalytics}
              provider={provider}
              disabled={!regions.length}
            />
            <TextInput
              value={instanceName}
              onChange={setNameFromEvent}
              errorMessage={errorMessage}
              state={isNameValid ? 'none' : 'error'}
              label={`${streamWorkspace} Name`}
              description={streamWorkspaceString}
              data-testid={TestSelector.WorkspaceName}
            />
          </Column>
        ) : (
          <Banner css={styles.bannerOverrides}>
            <strong>{tier} is coming soon.</strong>
            <br />
            {DISABLED_TIERS_COMINGSOON_DESCRIPTION[tier]}{' '}
            <a href="https://dochub.mongodb.org/core/streams-billing" target="_blank">
              Learn more
            </a>
          </Banner>
        )}
      </TemplatesPage>
      <Toast
        title="Unsuccessful deployment"
        description={createInstanceErrorMessage}
        variant={ToastVariant.Warning}
        open={!!createInstanceErrorMessage}
        onClose={() => setCreateInstanceErrorMessage('')}
      />
      <TemplateFooterWrapper>
        <PermissionBanner requiredPermissions={requiredPermissions} proactive css={{ textAlign: 'center' }} />

        <TemplateFooterInnerWrapper>
          <StreamsFeedbackModal groupId={groupId} buttonStyle={styles.shareFeedbackButtonStyle} />
          <Column css={styles.priceDisclaimerGap}>
            <Body className={bodyTypeScaleStyles[BaseFontSize.Body2]} weight="medium">
              {cost}
            </Body>
            <Disclaimer>*Billing takes place when you have at least 1 running processor.</Disclaimer>
          </Column>
          <ButtonRow>
            <Button
              disabled={!createInstanceEnabled || isSaving || !hasAccessToCreateStreamsInstance || !isAuthorized}
              variant={ButtonVariant.Primary}
              size={ButtonSize.Large}
              onClick={onCreateStreamInstanceClicked}
              css={styles.footerCreateButton}
              data-testid={TestSelector.CreateButton}
            >
              Create
            </Button>
            <Link href={`#/${Route.STREAMS_LIST}`} css={styles.cancelButton}>
              Cancel
            </Link>
          </ButtonRow>
        </TemplateFooterInnerWrapper>
      </TemplateFooterWrapper>
    </CreateInstanceWrapper>
  );
};

export default CreateStreamInstancePage;
