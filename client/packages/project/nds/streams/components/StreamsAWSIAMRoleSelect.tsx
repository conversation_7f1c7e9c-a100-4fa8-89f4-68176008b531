import { useCallback, useEffect, useState } from 'react';

import styled from '@emotion/styled';
import Banner, { Variant as BannerVariant } from '@leafygreen-ui/banner';
import Button from '@leafygreen-ui/button';
import { Spinner } from '@leafygreen-ui/loading-indicator';
import { Option, Select } from '@leafygreen-ui/select';
import { useToast, Variant } from '@leafygreen-ui/toast';
import { spacing } from '@leafygreen-ui/tokens';
import Tooltip from '@leafygreen-ui/tooltip';
import { Link } from '@leafygreen-ui/typography';
import { useSelector } from 'react-redux';

import { CloudProviderAccessAWSIAMRole } from '@packages/types/nds/cloudProviderAccess';

import * as app from '@packages/redux/common/app';

import * as api from '@packages/common/services/api/nds/cloudProviderAccessApi';

export const TestSelector = {
  NoRoleWarningBanner: 'no-roles-warning',
  RoleSelect: 'role-select',
  RoleOption: 'role-option',
  RefetchButton: 'refetch-button',
  RefetchText: 'refetch-text',
};

export const StyledInputWithButton = styled.div({
  display: 'grid',
  gridTemplateColumns: '1fr auto',
  gap: `${spacing[200]}px`,
});
export const InputButton = styled(Button)`
  margin-top: ${spacing[1200]}px;
`;

const TextButton = styled.button`
  background: none;
  border: none;
  margin: 0;
  padding: 0;
  font-weight: bold;
  text-decoration: underline;
`;

const UnifiedAWSAccessLink: React.FC<{ groupId: string }> = ({ groupId }: { groupId: string }) => (
  <Link href={`/v2/${groupId}#/integrations/cloudProviderAccess/aws`} target="_blank" rel="noopener noreferrer">
    AWS unified integration.
  </Link>
);

export interface Props {
  value: string;
  onChange(newValue: string): void;
  setError(error: string): void;
}

export const StreamsAWSIAMRoleSelect = ({ value, onChange, setError }: Props) => {
  const { pushToast } = useToast();
  const groupId = useSelector(app.getActiveGroupId);
  const [loadingArns, setLoadingArns] = useState(false);
  const [validAwsIamRoles, setValidAwsIamRoles] = useState<Array<CloudProviderAccessAWSIAMRole>>([]);

  // fetch the existing arn roles from the group's aws integration
  const fetchIamRoles = useCallback(
    async (showToast = false) => {
      try {
        setLoadingArns(true);
        const { awsIamRoles } = await api.fetchCloudProviderAccess(groupId);
        // only include validated completed iam roles
        setValidAwsIamRoles(awsIamRoles.filter((role) => !!role.iamAssumedRoleArn));
        setLoadingArns(false);
        if (showToast) {
          pushToast({
            variant: Variant.Success,
            title: 'IAM roles re-fetched successfully',
          });
        }
      } catch (e) {
        setError('Error loading AWS IAM roles. Please refresh this page to try again');
        setValidAwsIamRoles([]);
      } finally {
        setLoadingArns(false);
      }
    },
    [groupId, pushToast, setError]
  );

  useEffect(() => {
    fetchIamRoles();
  }, [fetchIamRoles]);

  return (
    <>
      <StyledInputWithButton>
        <Select
          css={{ width: '100%' }}
          label="AWS IAM Role ARN"
          description={
            <>
              To authorize new AWS IAM roles, do so with the <UnifiedAWSAccessLink groupId={groupId} />
            </>
          }
          value={value}
          placeholder="Select role"
          disabled={validAwsIamRoles.length === 0}
          onChange={(arn: string) => {
            onChange(arn);
            setError('');
          }}
          data-testid={TestSelector.RoleSelect}
        >
          {validAwsIamRoles.map(({ iamAssumedRoleArn }) => (
            <Option value={iamAssumedRoleArn || ''} key={iamAssumedRoleArn} data-testid={TestSelector.RoleOption}>
              {iamAssumedRoleArn}
            </Option>
          ))}
        </Select>
        <Tooltip
          justify="middle"
          trigger={
            <InputButton
              onClick={() => fetchIamRoles(true)}
              isLoading={loadingArns}
              loadingIndicator={<Spinner />}
              data-testid={TestSelector.RefetchButton}
            >
              Re-fetch
            </InputButton>
          }
        >
          Populate any new roles added since trying to create this connection
        </Tooltip>
      </StyledInputWithButton>
      {validAwsIamRoles.length === 0 && (
        <Banner variant={BannerVariant.Warning} data-testid={TestSelector.NoRoleWarningBanner}>
          No AWS IAM roles have been added. Authorize a role with the <UnifiedAWSAccessLink groupId={groupId} />
          <br />
          Once roles have been added,{' '}
          <TextButton onClick={() => fetchIamRoles(true)} data-testid={TestSelector.RefetchText}>
            re-fetch
          </TextButton>{' '}
          to populate them here.
        </Banner>
      )}
    </>
  );
};

export default StreamsAWSIAMRoleSelect;
