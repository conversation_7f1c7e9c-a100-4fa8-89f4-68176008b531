import { ConnectionType, PrivateEndpointConnectionSubtype } from '@packages/types/nds/connectionTypes';

import clusterFixtures from '@packages/common/fixtures/clusterFixtures';
import NDSUser from '@packages/common/models/NDSUser';
import {
  getConnectionCommandForShell,
  getConnectionStringForDriver,
  getConnectionStringForNativeApp,
} from '@packages/project/common/util/getConnectionStringUri';

const DEDICATED_PRIVATE_LINK_HORIZON = 'vpce-1234';
const DEDICATED_PRIVATE_LINK_LOAD_BALANCED_HORIZON = 'vpce-1234-load-balanced';

const PRIVATE_LINK_SRV_ADDRESS = 'abc-mmscloudtest.com';
const PRIVATE_LINK_SRV_LOAD_BALANCED_ADDRESS = 'abc-mmscloudtest-load-balanced.com';

const PRIVATE_SRV_ADDRESS = 'abc-mmscloudtest-pri.com';

const DEFAULT_SRV_ADDRESS = clusterFixtures.getAwsDefaultClusterDescription().get('srvAddress');

const getClusterDescriptionWithPrivateConnections = () => {
  const defaultClusterDescription = clusterFixtures.getAwsDefaultClusterDescription();

  // Needed to render api version
  defaultClusterDescription.set('mongoDBMajorVersion', '7.0.0');

  // Needed to render private endpoint hostname
  defaultClusterDescription.set('privateLinkSrvAddresses', {
    [DEDICATED_PRIVATE_LINK_HORIZON]: PRIVATE_LINK_SRV_ADDRESS,
  });

  // Needed to render private (peering) hostname
  defaultClusterDescription.set('privateSrvAddress', PRIVATE_SRV_ADDRESS);

  // Needed to render load balanced private endpoint hostname
  defaultClusterDescription.set('endpointToLoadBalancedSRVConnectionURI', {
    [DEDICATED_PRIVATE_LINK_LOAD_BALANCED_HORIZON]: PRIVATE_LINK_SRV_LOAD_BALANCED_ADDRESS,
  });

  return defaultClusterDescription;
};

const MockUsers: Record<string, typeof NDSUser> = {
  SCRAM: new NDSUser({
    awsIAMType: 'NONE',
    db: 'admin',
    deleteAfterDate: null,
    hasScramSha256Auth: true,
    hasUserToDNMapping: false,
    isEditable: true,
    labels: [],
    ldapAuthType: 'NONE',
    oidcAuthType: 'NONE',
    roles: [
      {
        collection: null,
        db: 'admin',
        role: 'atlasAdmin',
      },
    ],
    scopes: [],
    user: 'scramUser',
    x509Type: 'NONE',
  }),
  X509: new NDSUser({
    awsIAMType: 'NONE',
    db: '$external',
    deleteAfterDate: null,
    hasScramSha256Auth: false,
    hasUserToDNMapping: false,
    isEditable: true,
    labels: [],
    ldapAuthType: 'NONE',
    oidcAuthType: 'NONE',
    roles: [
      {
        collection: null,
        db: 'admin',
        role: 'atlasAdmin',
      },
    ],
    scopes: [],
    user: 'certUser',
    x509Type: 'MANAGED',
  }),
  AWS_IAM: new NDSUser({
    awsIAMType: 'USER',
    db: '$external',
    deleteAfterDate: null,
    hasScramSha256Auth: false,
    hasUserToDNMapping: false,
    isEditable: true,
    labels: [],
    ldapAuthType: 'NONE',
    oidcAuthType: 'NONE',
    roles: [
      {
        collection: null,
        db: 'admin',
        role: 'atlasAdmin',
      },
    ],
    scopes: [],
    user: 'arn:aws:iam::358363220050:user/aws_user',
    x509Type: 'NONE',
  }),
  OIDC: new NDSUser({
    awsIAMType: 'NONE',
    db: 'admin',
    deleteAfterDate: null,
    hasScramSha256Auth: false,
    hasUserToDNMapping: false,
    isEditable: true,
    labels: [],
    ldapAuthType: 'NONE',
    oidcAuthType: 'IDP_GROUP',
    roles: [
      {
        collection: null,
        db: 'admin',
        role: 'atlasAdmin',
      },
    ],
    scopes: [],
    user: '5dd7496c7a3e5a648454341c/10-gen',
    x509Type: 'NONE',
  }),
  LDAP: new NDSUser({
    awsIAMType: 'NONE',
    db: '$external',
    deleteAfterDate: null,
    hasScramSha256Auth: false,
    hasUserToDNMapping: false,
    isEditable: true,
    labels: [],
    ldapAuthType: 'USER',
    oidcAuthType: 'NONE',
    roles: [
      {
        collection: null,
        db: 'admin',
        role: 'atlasAdmin',
      },
    ],
    scopes: [],
    user: 'CN=testuser,CN=Users,DC=aws-atlas-ads-test-01,DC=mmscloudteam,DC=com',
    x509Type: 'NONE',
  }),
};

describe('packages/project/connectPage/utils/getConnectionStringUri', () => {
  const defaultClusterDescription = getClusterDescriptionWithPrivateConnections();
  describe('when getConnectionCommandForShell is called with a standard connection and supports SRV', () => {
    test('it returns the proper connection string for SCRAM User', () => {
      const connectionString = getConnectionCommandForShell({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        shellSupportsSRV: true,
        isMongosh: true,
        shellVersion: 'mongosh.2.0',
      });

      expect(connectionString).toEqual(
        `mongosh "mongodb+srv://${DEFAULT_SRV_ADDRESS}/" --apiVersion 1 --username scramUser`
      );
    });

    test('returns a connection string with a placeholder for the database user if no database users exist', () => {
      const connectionString = getConnectionCommandForShell({
        user: new NDSUser(),
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        shellSupportsSRV: true,
        isMongosh: true,
        shellVersion: 'mongosh.2.0',
      });

      expect(connectionString).toEqual(
        `mongosh "mongodb+srv://${DEFAULT_SRV_ADDRESS}/" --apiVersion 1 --username <db_username>`
      );
    });

    test('it returns the proper connection string for X509 User', () => {
      const connectionString = getConnectionCommandForShell({
        user: MockUsers.X509,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        shellSupportsSRV: true,
        isMongosh: true,
        shellVersion: 'mongosh.2.0',
      });

      expect(connectionString).toEqual(
        `mongosh "mongodb+srv://${DEFAULT_SRV_ADDRESS}/?authSource=%24external&authMechanism=MONGODB-X509" --apiVersion 1 --tls --tlsCertificateKeyFile <path to PEM file>`
      );
    });

    test('it returns the proper connection string for AWS IAM User', () => {
      const connectionString = getConnectionCommandForShell({
        user: MockUsers.AWS_IAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        shellSupportsSRV: true,
        isMongosh: true,
        shellVersion: 'mongosh.2.0',
      });

      expect(connectionString).toEqual(
        `mongosh "mongodb+srv://${DEFAULT_SRV_ADDRESS}/?authSource=%24external&authMechanism=MONGODB-AWS" --apiVersion 1 --username <AWS access key> --password <AWS secret key> --awsIamSessionToken <session token (for AWS IAM Roles)>`
      );
    });

    test('it returns the proper connection string for OIDC User', () => {
      const connectionString = getConnectionCommandForShell({
        user: MockUsers.OIDC,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        shellSupportsSRV: true,
        isMongosh: true,
        shellVersion: 'mongosh.2.0',
      });

      expect(connectionString).toEqual(
        `mongosh "mongodb+srv://${DEFAULT_SRV_ADDRESS}/?authSource=%24external&authMechanism=MONGODB-OIDC" --apiVersion 1`
      );
    });

    test('it returns the proper connection string for LDAP User', () => {
      const connectionString = getConnectionCommandForShell({
        user: MockUsers.LDAP,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        shellSupportsSRV: true,
        isMongosh: true,
        shellVersion: 'mongosh.2.0',
      });

      expect(connectionString).toEqual(
        `mongosh "mongodb+srv://${DEFAULT_SRV_ADDRESS}/?authSource=%24external&authMechanism=PLAIN" --apiVersion 1 --username CN=testuser,CN=Users,DC=aws-atlas-ads-test-01,DC=mmscloudteam,DC=com`
      );
    });
  });

  describe('when getConnectionCommandForShell is called with a standard connection and does not support SRV', () => {
    test('it returns the proper connection string', () => {
      const connectionString = getConnectionCommandForShell({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        shellSupportsSRV: false,
        isMongosh: false,
        shellVersion: '3.4',
      });

      expect(connectionString).toEqual(
        'mongo "mongodb://foo-shard-00-00-q8x1v.mmscloudtest.com:27000,foo-shard-00-01-q8x1v.mmscloudtest.com:27000,foo-shard-00-02-q8x1v.mmscloudtest.com:27000/?replicaSet=Cluster0-shard-0" --ssl --authenticationDatabase admin --username scramUser --password <password>'
      );
    });
  });

  describe('when getConnectionCommandForShell is called with a standard connection and connectable does not support SRV', () => {
    test('it returns the proper connection string', () => {
      const clusterDescription = getClusterDescriptionWithPrivateConnections();
      clusterDescription.set('srvAddress', []);
      const connectionString = getConnectionCommandForShell({
        user: MockUsers.SCRAM,
        clusterDescription: clusterDescription,
        connectionType: ConnectionType.PUBLIC,
        shellSupportsSRV: true,
        isMongosh: false,
        shellVersion: '3.4',
      });

      expect(connectionString).toEqual(
        'mongo "mongodb://foo-shard-00-00-q8x1v.mmscloudtest.com:27000,foo-shard-00-01-q8x1v.mmscloudtest.com:27000,foo-shard-00-02-q8x1v.mmscloudtest.com:27000/?replicaSet=Cluster0-shard-0" --ssl --authenticationDatabase admin --username scramUser --password <password>'
      );
    });
  });

  describe('when getConnectionCommandForShell is called with a standard connection and does support SRV with a password in memory', () => {
    test('it returns the proper connection string with the password displayed', () => {
      const connectionString = getConnectionCommandForShell({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        shellSupportsSRV: true,
        isMongosh: true,
        shellVersion: '2.0',
        showPlainTextPassword: true,
        plainTextPassword: 'myPassword',
      });

      expect(connectionString).toEqual(
        `mongosh "mongodb+srv://${DEFAULT_SRV_ADDRESS}/" --apiVersion 1 --username scramUser --password myPassword`
      );
    });
  });

  describe('when getConnectionCommandForShell is called with a private endpoint selected', () => {
    test('it returns the proper connection string', () => {
      const connectionString = getConnectionCommandForShell({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PRIVATE_ENDPOINT,
        privateLinkConnectionSubtype: PrivateEndpointConnectionSubtype.NOT_LOAD_BALANCED,
        selectedHorizon: DEDICATED_PRIVATE_LINK_HORIZON,
        shellSupportsSRV: true,
        isMongosh: true,
        shellVersion: 'mongosh.2.0',
      });

      expect(connectionString).toEqual(
        `mongosh "mongodb+srv://${PRIVATE_LINK_SRV_ADDRESS}/" --apiVersion 1 --username scramUser`
      );
    });
  });

  describe('when getConnectionCommandForShell is called with a private connection (peering) selected', () => {
    test('it returns the proper connection string', () => {
      const connectionString = getConnectionCommandForShell({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PRIVATE,
        selectedHorizon: DEDICATED_PRIVATE_LINK_HORIZON,
        shellSupportsSRV: true,
        isMongosh: true,
        shellVersion: 'mongosh.2.0',
      });

      expect(connectionString).toEqual(
        `mongosh "mongodb+srv://${PRIVATE_SRV_ADDRESS}/" --apiVersion 1 --username scramUser`
      );
    });
  });

  describe('when getConnectionCommandForShell is called with an optimized private endpoint selected', () => {
    test('it returns the proper connection string', () => {
      const connectionString = getConnectionCommandForShell({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PRIVATE_ENDPOINT,
        selectedHorizon: DEDICATED_PRIVATE_LINK_LOAD_BALANCED_HORIZON,
        privateLinkConnectionSubtype: PrivateEndpointConnectionSubtype.LOAD_BALANCED,
        shellSupportsSRV: true,
        isMongosh: true,
        shellVersion: 'mongosh.2.0',
      });

      expect(connectionString).toEqual(
        `mongosh "mongodb+srv://${PRIVATE_LINK_SRV_LOAD_BALANCED_ADDRESS}/" --apiVersion 1 --username scramUser`
      );
    });
  });

  describe('when getConnectionStringForNativeApp is called with a standard connection and supports SRV', () => {
    test('it returns the proper connection string for SCRAM User', () => {
      const connectionString = getConnectionStringForNativeApp({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        supportsSRV: true,
      });

      expect(connectionString).toEqual(`mongodb+srv://scramUser:<db_password>@${DEFAULT_SRV_ADDRESS}/`);
    });

    test('it returns the proper connection string for LDAP User', () => {
      const connectionString = getConnectionStringForNativeApp({
        user: MockUsers.LDAP,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        supportsSRV: true,
      });

      expect(connectionString).toEqual(
        `mongodb+srv://CN=testuser,CN=Users,DC=aws-atlas-ads-test-01,DC=mmscloudteam,DC=com:<db_password>@${DEFAULT_SRV_ADDRESS}/?authSource=$external&authMechanism=PLAIN`
      );
    });
  });

  describe('when getConnectionStringForNativeApp is called with a standard connection and does not support SRV', () => {
    test('it returns the proper connection string for SCRAM User', () => {
      const connectionString = getConnectionStringForNativeApp({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        supportsSRV: false,
      });

      expect(connectionString).toEqual(
        'mongodb://scramUser:<db_password>@foo-shard-00-00-q8x1v.mmscloudtest.com:27000,foo-shard-00-01-q8x1v.mmscloudtest.com:27000,foo-shard-00-02-q8x1v.mmscloudtest.com:27000/?replicaSet=Cluster0-shard-0&ssl=true&authSource=admin'
      );
    });

    test('it returns the proper connection string for LDAP User', () => {
      const connectionString = getConnectionStringForNativeApp({
        user: MockUsers.LDAP,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        supportsSRV: false,
      });

      expect(connectionString).toEqual(
        'mongodb://CN=testuser,CN=Users,DC=aws-atlas-ads-test-01,DC=mmscloudteam,DC=com:<db_password>@foo-shard-00-00-q8x1v.mmscloudtest.com:27000,foo-shard-00-01-q8x1v.mmscloudtest.com:27000,foo-shard-00-02-q8x1v.mmscloudtest.com:27000/?authSource=$external&authMechanism=PLAIN&replicaSet=Cluster0-shard-0&ssl=true'
      );
    });
  });

  describe('when getConnectionStringForNativeApp is called with a standard connection and password in memory', () => {
    test('it returns the proper connection string for SCRAM User', () => {
      const connectionString = getConnectionStringForNativeApp({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        supportsSRV: true,
        showPlainTextPassword: true,
        plainTextPassword: 'myPassword',
      });

      expect(connectionString).toEqual(`mongodb+srv://scramUser:myPassword@${DEFAULT_SRV_ADDRESS}/`);
    });
  });

  describe('when getConnectionStringForNativeApp is called with a private endpoint connection', () => {
    test('it returns the proper connection string for SCRAM User', () => {
      const connectionString = getConnectionStringForNativeApp({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PRIVATE_ENDPOINT,
        privateLinkConnectionSubtype: PrivateEndpointConnectionSubtype.NOT_LOAD_BALANCED,
        selectedHorizon: DEDICATED_PRIVATE_LINK_HORIZON,
        supportsSRV: true,
      });

      expect(connectionString).toEqual(`mongodb+srv://scramUser:<db_password>@${PRIVATE_LINK_SRV_ADDRESS}/`);
    });
  });

  describe('when getConnectionStringForNativeApp is called with a private peering connection', () => {
    test('it returns the proper connection string for SCRAM User', () => {
      const connectionString = getConnectionStringForNativeApp({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PRIVATE,
        selectedHorizon: DEDICATED_PRIVATE_LINK_HORIZON,
        supportsSRV: true,
      });

      expect(connectionString).toEqual(`mongodb+srv://scramUser:<db_password>@${PRIVATE_SRV_ADDRESS}/`);
    });
  });

  describe('when getConnectionStringForDriver is called with a standard connection and supports SRV', () => {
    test('it returns the proper connection string for SCRAM User', () => {
      const connectionString = getConnectionStringForDriver({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        supportsSRV: true,
        isRetryWritesSupported: true,
        writeConcern: 'majority',
      });

      expect(connectionString).toEqual(
        `mongodb+srv://scramUser:<db_password>@${DEFAULT_SRV_ADDRESS}/?retryWrites=true&w=majority&appName=Cluster0`
      );
    });

    test('it returns the proper connection string for X509 User', () => {
      const connectionString = getConnectionStringForDriver({
        user: MockUsers.X509,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        supportsSRV: true,
        isRetryWritesSupported: true,
        writeConcern: 'majority',
      });

      expect(connectionString).toEqual(
        `mongodb+srv://${DEFAULT_SRV_ADDRESS}/?authSource=%24external&authMechanism=MONGODB-X509&retryWrites=true&w=majority&appName=Cluster0`
      );
    });

    test('it returns the proper connection string for AWS IAM User', () => {
      const connectionString = getConnectionStringForDriver({
        user: MockUsers.AWS_IAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        supportsSRV: true,
        isRetryWritesSupported: true,
        writeConcern: 'majority',
      });

      expect(connectionString).toEqual(
        `mongodb+srv://<AWS access key>:<AWS secret key>@${DEFAULT_SRV_ADDRESS}/?authSource=%24external&authMechanism=MONGODB-AWS&retryWrites=true&w=majority&appName=Cluster0&authMechanismProperties=AWS_SESSION_TOKEN:<session token (for AWS IAM Roles)>`
      );
    });

    test('it returns the proper connection string for OIDC User', () => {
      const connectionString = getConnectionStringForDriver({
        user: MockUsers.OIDC,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        supportsSRV: true,
        isRetryWritesSupported: true,
        writeConcern: 'majority',
      });

      expect(connectionString).toEqual(
        `mongodb+srv://${DEFAULT_SRV_ADDRESS}/?authSource=%24external&authMechanism=MONGODB-OIDC&retryWrites=true&w=majority&appName=Cluster0`
      );
    });

    test('it returns the proper connection string for LDAP User', () => {
      const connectionString = getConnectionStringForDriver({
        user: MockUsers.LDAP,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        supportsSRV: true,
        isRetryWritesSupported: true,
        writeConcern: 'majority',
      });

      expect(connectionString).toEqual(
        'mongodb+srv://CN=testuser,CN=Users,DC=aws-atlas-ads-test-01,DC=mmscloudteam,DC=com:<db_password>@connectionString.com/?authSource=%24external&authMechanism=PLAIN&retryWrites=true&w=majority&appName=Cluster0'
      );
    });
  });

  describe('when getConnectionStringForDriver is called with a standard connection and supports SRV', () => {
    const connectionString = getConnectionStringForDriver({
      user: MockUsers.SCRAM,
      clusterDescription: defaultClusterDescription,
      connectionType: ConnectionType.PUBLIC,
      supportsSRV: false,
      isRetryWritesSupported: true,
      writeConcern: 'majority',
    });

    expect(connectionString).toEqual(
      'mongodb://scramUser:<db_password>@foo-shard-00-00-q8x1v.mmscloudtest.com:27000,foo-shard-00-01-q8x1v.mmscloudtest.com:27000,foo-shard-00-02-q8x1v.mmscloudtest.com:27000/?replicaSet=Cluster0-shard-0&ssl=true&authSource=admin&retryWrites=true&w=majority&appName=Cluster0'
    );
  });

  describe('when getConnectionStringForDriver is called with a private endpoint connection and supports SRV', () => {
    const connectionString = getConnectionStringForDriver({
      user: MockUsers.SCRAM,
      clusterDescription: defaultClusterDescription,
      connectionType: ConnectionType.PRIVATE_ENDPOINT,
      privateLinkConnectionSubtype: PrivateEndpointConnectionSubtype.NOT_LOAD_BALANCED,
      selectedHorizon: DEDICATED_PRIVATE_LINK_HORIZON,
      supportsSRV: true,
      isRetryWritesSupported: true,
      writeConcern: 'majority',
    });

    expect(connectionString).toEqual(
      `mongodb+srv://scramUser:<db_password>@${PRIVATE_LINK_SRV_ADDRESS}/?retryWrites=true&w=majority&appName=Cluster0`
    );
  });

  describe('when getConnectionStringForDriver is called with a private endpoint connection and supports SRV', () => {
    const connectionString = getConnectionStringForDriver({
      user: MockUsers.SCRAM,
      clusterDescription: defaultClusterDescription,
      connectionType: ConnectionType.PRIVATE,
      supportsSRV: true,
      isRetryWritesSupported: true,
      writeConcern: 'majority',
    });

    expect(connectionString).toEqual(
      `mongodb+srv://scramUser:<db_password>@${PRIVATE_SRV_ADDRESS}/?retryWrites=true&w=majority&appName=Cluster0`
    );
  });

  describe('when getConnectionStringForDriver is called with a standard connection and password in memory', () => {
    test('it returns the proper connection string for SCRAM User', () => {
      const connectionString = getConnectionStringForDriver({
        user: MockUsers.SCRAM,
        clusterDescription: defaultClusterDescription,
        connectionType: ConnectionType.PUBLIC,
        supportsSRV: true,
        isRetryWritesSupported: true,
        writeConcern: 'majority',
        showPlainTextPassword: true,
        plainTextPassword: 'myPassword',
      });

      expect(connectionString).toEqual(
        `mongodb+srv://scramUser:myPassword@${DEFAULT_SRV_ADDRESS}/?retryWrites=true&w=majority&appName=Cluster0`
      );
    });
  });
});
