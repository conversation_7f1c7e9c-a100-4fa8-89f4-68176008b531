import { RefObject, useContext, useEffect, useRef, useState } from 'react';

import { css } from '@emotion/react';
import Banner from '@leafygreen-ui/banner';
import { Combobox, ComboboxOption } from '@leafygreen-ui/combobox';
import { GuideCue, TooltipAlign, TooltipJustify } from '@leafygreen-ui/guide-cue';
import { SegmentedControl, SegmentedControlOption } from '@leafygreen-ui/segmented-control';

import { AlertConfig, AlertConfigSource } from '@packages/types/alerts/alertConfigs/AlertConfig';
import { AlertConfigFamily } from '@packages/types/alerts/alertConfigs/AlertConfig/AlertConfigFamily';

import { ProjectAlertSettingsContext } from '@packages/alertConfig/context/ProjectAlertSettingsContext';
import localStorageWrapper from '@packages/common/services/localStorage';
import { popoverZIndex } from '@packages/common/styles/layoutStyles';
import {
  alertConfigFamilyToDisplayName,
  alertConfigTypeFamilyMap,
  sortedAlertConfigFamilies,
} from '@packages/common/utils/activityStringMappings/alertConfigTypeFamily';
import analytics, { SEGMENT_EVENTS } from '@packages/common/utils/segmentAnalytics';
import { CARD_WIDTH } from '@packages/project/alerts/components/AlertConfigurationCategoryCard';
import { ALERTS_SETUP_EXPERIENCE_GUIDE_CUE_LS_KEY } from '@packages/project/common/constants';

import MaintenanceWindowBanner from './MaintenanceWindowBanner';
import ManageActiveCardView from './ManageActiveCardView';
import ManageActiveListView from './ManageActiveListView';

/**
 * Extracts every unique {@link AlertConfigFamily} present in a given array of {@link AlertConfig} objects.
 */
const getUniqueAlertConfigFamilies = (alertConfigs: Array<AlertConfig>): Set<AlertConfigFamily> => {
  const uniqueFamilies = new Set<AlertConfigFamily>();

  alertConfigs.forEach((alertConfig) => {
    const family = alertConfigTypeFamilyMap[alertConfig._t];
    // Sets only store unique values - we can call "add" as many times as we want without risk of duplication
    uniqueFamilies.add(family);
  });

  return uniqueFamilies;
};

/**
 * Filters an array of {@link AlertConfig} objects to include only those belonging to
 * the {@link AlertConfigFamily} values present in the `uniqueAlertFamilies` Set.
 *
 * If the provided Set is empty the function returns an unfiltered array of AlertConfigs.
 */
const getFilteredAlertConfigs = (alertConfigs: Array<AlertConfig>, uniqueAlertFamilies: Set<AlertConfigFamily>) => {
  const hasNoActiveFilters = uniqueAlertFamilies.size === 0;

  if (hasNoActiveFilters) return alertConfigs;

  return alertConfigs.filter((alertConfig) => uniqueAlertFamilies.has(alertConfigTypeFamilyMap[alertConfig._t]));
};

enum AlertsSettingsView {
  CARD = 'CARD',
  LIST = 'LIST',
}

interface StepBasedGuideCueProps {
  /**
   * Title to appear inside of the Tooltip.
   */
  title: string;

  /**
   * Body content to appear inside the tooltip, under the title.
   */
  children: React.ReactNode;

  /**
   * Text of the CTA button.
   */
  buttonText: string;

  /**
   * Reference to an element that the tooltip should be positioned against.
   */
  refEl: RefObject<HTMLDivElement>;

  /**
   * Determines the alignment of the tooltip relative to the ref element.
   */
  tooltipAlign: TooltipAlign;

  /**
   * Determines the justification of the tooltip relative to its arrow.
   */
  tooltipJustify: TooltipJustify;

  /**
   * Determines the space between the ref and the guide cue.
   */
  spacing: number;
}

function ManageActiveWrapper() {
  const state = useContext(ProjectAlertSettingsContext);
  const [activeAlertConfigFamilies, setActiveAlertConfigFamilies] = useState<Set<AlertConfigFamily>>(new Set());
  const uniqueAlertConfigFamilies = getUniqueAlertConfigFamilies(state.alertConfigs);
  const filteredAlertConfigs = getFilteredAlertConfigs(state.alertConfigs, activeAlertConfigFamilies);

  const hasNotificationWarning = state.alertConfigs.some((alertConfig) =>
    alertConfig.notify?.some?.((notifier) => notifier?.warning)
  );

  const [view, setView] = useState<AlertsSettingsView>(AlertsSettingsView.CARD);

  const projectHasNonDefaultAlertConfig = state.alertConfigs.some(
    (alertConfig) => alertConfig.source === AlertConfigSource.API || alertConfig.source === AlertConfigSource.UI
  );
  const hasGuideCueFlag = !!localStorageWrapper.getItem(ALERTS_SETUP_EXPERIENCE_GUIDE_CUE_LS_KEY);

  const shouldShowGuideCues = !projectHasNonDefaultAlertConfig && !hasGuideCueFlag;

  // Check if modal is open: either editing (alertConfigId=<id>) or creating (operation=Clone)
  const isAlertConfigModalOpen =
    window.location.href.includes('alertConfigId=') || window.location.href.includes('operation=Clone');

  const [isGuideCueOpen, setIsGuideCueOpen] = useState(shouldShowGuideCues);
  const [guideCueStep, setGuideCueStep] = useState(0);

  const segmentedControlRef = useRef<HTMLDivElement>(null);
  const comboBoxRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  const stepToGuideCueProps: Array<StepBasedGuideCueProps> = [
    {
      title: 'Toggle between views',
      children: (
        <>
          <p>Use the Card View to group alerts by category and perform actions for each category.</p>
          <p>Use the List View for a more compact overview of the alerts.</p>
        </>
      ),
      buttonText: 'Next',
      refEl: segmentedControlRef,
      tooltipAlign: 'bottom',
      tooltipJustify: 'end',
      spacing: 25,
    },
    {
      title: 'Apply filters',
      children: <>Use the filter to view alerts in specific categories.</>,
      buttonText: 'Next',
      refEl: comboBoxRef,
      tooltipAlign: 'bottom',
      tooltipJustify: 'start',
      spacing: 35,
    },
    {
      title: 'Manage and customize alerts',
      children: (
        <>
          <p>
            Add new alerts and manage the default notification method, including how you get notified and how often.
          </p>
          <p>You can also edit the notification method for each specific alert.</p>
        </>
      ),
      buttonText: 'Got it',
      refEl: cardRef,
      tooltipAlign: 'right',
      tooltipJustify: 'start',
      spacing: CARD_WIDTH / 2 + 35,
    },
  ];

  const onGuideCuePrimaryButtonClick = () => {
    if (guideCueStep >= 2) {
      setIsGuideCueOpen(false);
    } else {
      if (guideCueStep === 1) {
        setView(AlertsSettingsView.CARD);
      }

      setGuideCueStep(guideCueStep + 1);
    }

    const title = stepToGuideCueProps[guideCueStep].title;
    analytics.track(SEGMENT_EVENTS.GUIDE_CUE_CTA_CLICKED, {
      context: 'Alert Settings Page - Card View',
      title,
    });
  };

  const onDismissGuideCue = () => {
    const title = stepToGuideCueProps[guideCueStep].title;
    analytics.track(SEGMENT_EVENTS.GUIDE_CUE_DISMISSED, {
      context: 'Alert Settings Page - Card View',
      title,
    });
    setIsGuideCueOpen(false);
  };
  useEffect(() => {
    // prevent showing guide cues again
    if (shouldShowGuideCues) {
      localStorageWrapper.setItem(ALERTS_SETUP_EXPERIENCE_GUIDE_CUE_LS_KEY, true);
    }
  }, [shouldShowGuideCues]);

  const viewMap = {
    [AlertsSettingsView.CARD]: <ManageActiveCardView filteredAlertConfigs={filteredAlertConfigs} refEl={cardRef} />,
    [AlertsSettingsView.LIST]: <ManageActiveListView filteredAlertConfigs={filteredAlertConfigs} />,
  };

  return (
    <div
      css={css`
        display: flex;
        flex-direction: column;
        gap: 24px;
      `}
    >
      <div
        css={css`
          align-items: center;
          display: flex;
        `}
      >
        <div ref={comboBoxRef}>
          <Combobox
            css={css`
              width: 220px;
            `}
            chipCharacterLimit={50}
            popoverZIndex={popoverZIndex}
            multiselect
            size="small"
            label="Filter by category"
            placeholder="Select category"
            value={Array.from(activeAlertConfigFamilies)}
            onChange={(alertConfigFamilies: Array<AlertConfigFamily>) =>
              setActiveAlertConfigFamilies(new Set(alertConfigFamilies))
            }
          >
            {sortedAlertConfigFamilies.map((family) => {
              if (!uniqueAlertConfigFamilies.has(family)) return null;
              return (
                <ComboboxOption key={family} value={family} displayName={alertConfigFamilyToDisplayName[family]} />
              );
            })}
          </Combobox>
        </div>

        <div
          css={css`
            display: flex;
            margin-bottom: auto;
            margin-left: auto;
            padding: 20px 0;
          `}
        >
          <SegmentedControl
            value={view}
            onChange={(newView: AlertsSettingsView) => {
              const currentViewTitleCased = view.charAt(0).toUpperCase() + view.slice(1).toLowerCase();
              const newViewTitleCased = newView.charAt(0).toUpperCase() + newView.slice(1).toLowerCase();
              analytics.track(SEGMENT_EVENTS.TOGGLE_ALERT_SETTING_VIEW, {
                context: `Alert Settings Page - ${currentViewTitleCased} View`,
                newView: newViewTitleCased,
              });
              setView(newView);
            }}
            size="xsmall"
            ref={segmentedControlRef}
          >
            <SegmentedControlOption data-testid="card-view-tab" value={AlertsSettingsView.CARD}>
              Card View
            </SegmentedControlOption>
            <SegmentedControlOption data-testid="list-view-tab" value={AlertsSettingsView.LIST}>
              List View
            </SegmentedControlOption>
          </SegmentedControl>
          <GuideCue
            open={isGuideCueOpen && !isAlertConfigModalOpen}
            setOpen={() => {}} // Required prop. For multi-step guide cues, pass empty function
            currentStep={guideCueStep + 1} // Display with 1-based indexing
            numberOfSteps={3}
            onDismiss={onDismissGuideCue}
            onPrimaryButtonClick={onGuideCuePrimaryButtonClick}
            {...stepToGuideCueProps[guideCueStep]}
          />
        </div>
      </div>
      <MaintenanceWindowBanner />
      {hasNotificationWarning && <Banner variant="warning">Some notifications may be misconfigured</Banner>}
      {viewMap[view]}
    </div>
  );
}

export default ManageActiveWrapper;
