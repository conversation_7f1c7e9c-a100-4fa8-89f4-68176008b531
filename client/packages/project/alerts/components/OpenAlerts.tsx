import { useContext } from 'react';

import { css } from '@emotion/css';
import { TableSkeleton } from '@leafygreen-ui/skeleton-loader';
import { useSelector } from 'react-redux';

import { getActiveGroupId } from '@packages/redux/common/app';

import { Permission, PermissionCard } from '@packages/authz';
import { OPEN_ALERT_COLLECTION_COLUMNS } from '@packages/project/alerts/utils/alertCollections';

import { ContextTypes, OpenAlertsContext } from '../context/openAlertsContext';
import AcknowledgeAlertModal from './AckUnackAlertModal';
import BulkAckButton from './BulkAckButton';
import EmptyState from './EmptyState';
import OpenAlertsCollection from './OpenAlertsCollection';

function OpenAlerts() {
  const state: ContextTypes = useContext(OpenAlertsContext);
  const alertCollectionColumns = OPEN_ALERT_COLLECTION_COLUMNS;

  const projectId: string = useSelector(getActiveGroupId);

  return (
    <PermissionCard requiredPermissions={[Permission.CLOUD_PROJECT_ALERTING_ALERTS_LIST(projectId)]}>
      {!state.loaded ? (
        <div
          className={css`
            padding-top: 5px;
          `}
          data-testid="open-alerts-skeleton"
        >
          <TableSkeleton numCols={alertCollectionColumns.length} numRows={8} />
        </div>
      ) : state.allAlerts && state.allAlerts.length ? (
        <>
          <AcknowledgeAlertModal />
          <BulkAckButton />
          <OpenAlertsCollection columns={alertCollectionColumns} />
        </>
      ) : (
        <div
          className={css`
            padding-top: 72px;
          `}
        >
          <EmptyState isOpen={true} />
        </div>
      )}
    </PermissionCard>
  );
}

export default OpenAlerts;
