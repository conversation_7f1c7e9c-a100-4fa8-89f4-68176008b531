import { css } from '@emotion/react';
import styled from '@emotion/styled';
import Banner from '@leafygreen-ui/banner';
import Checkbox from '@leafygreen-ui/checkbox';
import { palette } from '@leafygreen-ui/palette';
import TextInput from '@leafygreen-ui/text-input';

import type { LogType } from '@packages/types/monitoring/logs/externalLogSinksInterfaces';

import useRandomId from '@packages/hooks/useRandomId';

const AWS_S3_DOCS_URL = 'https://docs.aws.amazon.com/s3/';

const TitleContainer = styled.div({
  marginTop: 10,
  marginBottom: 15,
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
});

const Title = styled.div({
  fontSize: 19,
  fontWeight: 'bold',
  color: `${palette.gray.dark2}`,
});

const ViewAwsDocsLink = styled.a({
  marginTop: 5,
});

const IntroText = styled.div({
  fontSize: 15,
  marginBottom: 24,
});

const InputLabel = styled.label({
  fontSize: 15,
  fontWeight: 'bold',
});

const InputContainer = styled.div({
  marginBottom: 20,
});

const InputDescription = styled.div({
  fontSize: 14,
  marginBottom: 8,
  color: `${palette.gray.dark1}`,
});

const inputCSS = css({
  maxWidth: 'none',
  width: '100%',
});

const getDisplayPrefixPath = (bucketName: string, prefixPath: string): string => {
  if (!bucketName) return '';
  const path = prefixPath ? prefixPath : 'logs';
  return `${bucketName}/${path}/{cluster-name}/{hostname}/{log-name}`;
};

interface S3ExternalLogSinkBucketStepProps {
  bucketName: string;
  setBucketName: (bucketName: string) => void;
  prefixPath: string;
  setPrefixPath: (prefixPath: string) => void;
  selectedLogTypes: Array<LogType>;
  setSelectedLogTypes: (logTypes: Array<LogType>) => void;
  kmsKey: string;
  setKmsKey: (kmsKey: string) => void;
  error: JSX.Element | null;
  message: JSX.Element | null;
}

const LOG_TYPE_OPTIONS: Array<{ value: LogType; label: string }> = [
  { value: 'MONGOD', label: 'MongoDB Logs' },
  { value: 'MONGOD_AUDIT', label: 'MongoDB Audit Logs' },
  { value: 'MONGOS', label: 'MongoDB Router Logs' },
  { value: 'MONGOS_AUDIT', label: 'MongoDB Router Audit Logs' },
];

const LogTypeContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  margin-top: 0.5rem;
`;

export default function S3ExternalLogSinkBucketStep({
  bucketName,
  setBucketName,
  prefixPath,
  setPrefixPath,
  selectedLogTypes,
  setSelectedLogTypes,
  kmsKey,
  setKmsKey,
  error,
  message,
}: S3ExternalLogSinkBucketStepProps) {
  const bucketNameLabelId = useRandomId('bucket-name');
  const prefixPathLabelId = useRandomId('prefix-path');
  const logTypeLabelId = useRandomId('log-type');
  const kmsKeyLabelId = useRandomId('kms-key');

  const handleLogTypeChange = (logType: LogType, checked: boolean) => {
    if (checked) {
      setSelectedLogTypes([...selectedLogTypes, logType]);
    } else {
      setSelectedLogTypes(selectedLogTypes.filter((type) => type !== logType));
    }
  };

  return (
    <div>
      {error}
      {message}
      <TitleContainer>
        <Title>S3 Bucket Settings</Title>
        <ViewAwsDocsLink href={AWS_S3_DOCS_URL} target="_blank" rel="noopener noreferrer">
          View AWS S3 Docs <i className="fa fa-external-link" />
        </ViewAwsDocsLink>
      </TitleContainer>
      <IntroText>
        Enter the name of the S3 bucket that you want Atlas to access. In the next step, you will be asked to create an
        access policy to grant Atlas the required AWS IAM role access to your bucket.
      </IntroText>

      <InputLabel id={bucketNameLabelId}>Bucket Name</InputLabel>
      <Banner variant="warning" style={{ marginBottom: '8px' }}>
        Cross-region data transfer may result in higher costs.
      </Banner>
      <InputContainer>
        <InputDescription>Enter the S3 Bucket you want Atlas to write to</InputDescription>
        <TextInput
          placeholder="my-first-bucket"
          name="bucketNameInput"
          value={bucketName}
          onChange={({ target: { value } }) => setBucketName(value)}
          css={inputCSS}
          aria-labelledby={bucketNameLabelId}
          data-testid={'bucket name input'}
          label=""
        />
      </InputContainer>

      <InputLabel id={prefixPathLabelId}>Prefix</InputLabel>
      <InputContainer>
        <InputDescription>
          This string will be prepended to all paths you define for this bucket. Add sentence that describes folder
          structure that Atlas will upload logs in.
          <div style={{ marginTop: 12, marginBottom: 12 }}>
            <TextInput
              name="displayPath"
              aria-labelledby="test"
              disabled={true}
              placeholder={getDisplayPrefixPath(bucketName, prefixPath)}
            />
          </div>
        </InputDescription>
        <TextInput
          placeholder="logs/"
          name="prefixPathInput"
          value={prefixPath}
          onChange={({ target: { value } }) => setPrefixPath(value)}
          css={inputCSS}
          aria-labelledby={prefixPathLabelId}
          data-testid={'prefix path input'}
          label=""
        />
      </InputContainer>

      <InputLabel id={logTypeLabelId}>Log Type</InputLabel>
      <InputContainer>
        <InputDescription>Select the type of logs you want to be exported to your S3 bucket</InputDescription>
        <LogTypeContainer>
          {LOG_TYPE_OPTIONS.map(({ value, label }) => (
            <Checkbox
              key={value}
              checked={selectedLogTypes.includes(value)}
              onChange={(e) => handleLogTypeChange(value, e.target.checked)}
              label={label}
              data-testid={`log-type-${value}`}
            />
          ))}
        </LogTypeContainer>
      </InputContainer>

      <InputLabel id={kmsKeyLabelId}>KMS Key</InputLabel>
      <InputContainer>
        <InputDescription>Enter an optional KMS Key ARN to encrypt logs in your S3 bucket</InputDescription>
        <TextInput
          placeholder="arn:aws:kms:us-east-1:123456789012:key/12345678-1234-1234-1234-123456789012"
          name="kmsKeyInput"
          value={kmsKey}
          onChange={({ target: { value } }) => setKmsKey(value)}
          css={inputCSS}
          aria-labelledby={kmsKeyLabelId}
          data-testid={'kms key input'}
          label=""
          optional={true}
        />
      </InputContainer>
    </div>
  );
}
