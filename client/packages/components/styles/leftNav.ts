import { css } from '@emotion/react';
import styled from '@emotion/styled';
import { MenuItem } from '@leafygreen-ui/menu';
import { palette } from '@leafygreen-ui/palette';

import { StyledMenuItem } from '@packages/types/leafygreen-emotion';

// NOTE: This is explicitly typed as "any" because @leafygreen-ui/menu is missing an "export"
// needed for type declaration. (They need to export the "BaseMenuItemProps" interface.)
// When Visual Brand epic is complete, this "export" should be added
// into the LG module and this "any" can be removed
// TODO: https://jira.mongodb.org/browse/CLOUDP-116541
export const MenuGroupHeader: any = styled<StyledMenuItem>(MenuItem)`
  text-transform: uppercase;
  letter-spacing: 1.3px;
  line-height: 16px;

  a {
    height: 100%;
    width: 100%;
    text-decoration: none;

    div {
      font-size: 10px;
      color: ${palette.gray.base};
      font-weight: bold;
    }
  }
  :hover {
    background-color: inherit;
  }
`;

export const navItem = css`
  font-size: 13px;
  letter-spacing: 0.4px;
  line-height: 16px;
  min-height: 35px;

  a,
  span {
    height: 100%;
    width: 100%;
    text-decoration: none;
    color: ${palette.gray.dark2};
  }
`;

export const activeNavItem = css`
  ${navItem};
  background-color: ${palette.green.light3};
  font-weight: bold;

  a,
  div,
  span {
    color: ${palette.green.dark3};
    font-weight: bold;
  }
`;

export const LeftNavContainer = styled.ul`
  color: ${palette.gray.dark3};
  width: 235px;
  height: 100%;
  background-color: ${palette.white};
  left: 0;
  padding: 0px 0px 40px 0px;
  display: flex;
  flex-direction: column;
  list-style: none;
  position: fixed;
`;
