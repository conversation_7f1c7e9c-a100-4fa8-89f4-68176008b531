import { SchedulingMetadataView } from './SchedulingMetadataView';

export interface NDSGroupMaintenanceAdminView {
  dayOfWeek: number;
  hourOfDay: number;
  numberOfDeferrals: number;
  isUserDefined: boolean;
  isAutoDeferEnabled: boolean;
  deferralGrantJiraTicket: string;
  deferralRequestDate: string;
  advanceNotificationSendDate: string;
  previousAdvancedNotificationSendDate: string;
  maintenanceStartedNotificationSentDate: string;
  lastMaintenanceCompletionDate: string;
  lastMaintenanceDate: string;
  lastMaintenanceStartedDate: string;
  lastMaintenanceDurationMinutes: number;
  groupTimeZoneId: string;
  groupId: string;
  groupName: string;
  schedulingMetadata: Array<SchedulingMetadataView>;
}
