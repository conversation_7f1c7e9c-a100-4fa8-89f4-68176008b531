import type { ReactNode } from 'react';

import { css, cx } from '@emotion/css';
import styled from '@emotion/styled';
import { Code, Language } from '@leafygreen-ui/code';
import Copyable from '@leafygreen-ui/copyable';
import Icon from '@leafygreen-ui/icon';
import { palette } from '@leafygreen-ui/palette';
import Tooltip from '@leafygreen-ui/tooltip';

import type { QueryStatsDetails } from '@packages/types/QueryShapeInsightsInterfaces';

import {
  formatBytes,
  formatDate,
  formatNumber,
  formatNumberWithSingleDigitZero,
  formatTime,
} from '@packages/handlebars-helpers/formatNumber';
import wdyr from '@packages/intel/wdyr';

import BlockQueryShapeButton from './BlockQueryShapeButton';

const tableMargin = css`
  margin-top: 25px;
`;

export const tooltipIconStyle = css`
  top: 2px;
`;

const queryShapeHashCopyComponent = css`
  width: 100% !important;
  max-width: unset !important;
`;

const QueryShapeTitle = styled.span`
  color: ${palette.gray.dark4};
  font-weight: 600;
  margin-right: 8px;
`;

const widenLineNumbers = css`
  & td:first-child {
    width: 40px !important;
  }
`;

const appNameToolTipText = (
  <>
    The application name can be set as a parameter to the MongoDB connection string. For more information on how to set
    it, click{' '}
    <a
      href="https://www.mongodb.com/docs/manual/reference/connection-string-options/#mongodb-urioption-urioption.appName"
      target="_blank"
      rel="noopener noreferrer"
    >
      here
    </a>
    . This property can not be set for internal queries.
  </>
);

const driverNameVersionToolTipText = (
  <>
    The driver name and version are part of the MongoDB driver connection. These properties can not be set. For more
    details on these values, click{' '}
    <a
      href="https://specifications.readthedocs.io/en/latest/mongodb-handshake/handshake/#clientdrivername"
      target="_blank"
      rel="noopener noreferrer"
    >
      here
    </a>
    . These properties are sometimes not set for certain internal queries.
  </>
);

const Table = styled.table`
  width: 100%;

  & > tbody {
    & > tr {
      display: grid;
      grid-template-columns: 1fr 1fr;
      padding-top: 5px;
      padding-bottom: 5px;

      &:nth-of-type(even) {
        padding-bottom: 20px;
      }

      & > th {
        font-size: 13px;
        font-weight: normal;
        color: ${palette.gray.dark1};
      }

      & > td {
        font-size: 13px;
        font-weight: normal;
      }
    }
  }
`;

interface Props {
  queryShapeDetails: QueryStatsDetails;
  timeZoneId: string;
  groupId: string;
  clusterName: string;
  onQueryShapeBlocked?: () => void;
  onQueryShapeUnblocked?: () => void;
}

const QueryShapeDetailsFlyout = (props: Props) => {
  const { queryShapeDetails, timeZoneId, groupId, clusterName, onQueryShapeBlocked, onQueryShapeUnblocked } = props;
  const { queryShape, queryShapeHash } = queryShapeDetails.queryStats;
  return (
    <>
      {queryShape && (
        <>
          <QueryShapeTitle>Full Query Shape</QueryShapeTitle>
          <Code
            data-testid="queryShapeDetailsFlyoutParsedQueryShape"
            copyable
            showLineNumbers
            language={Language.Json}
            className={widenLineNumbers}
          >
            {JSON.stringify(JSON.parse(queryShape), null, 2)}
          </Code>
        </>
      )}
      <BlockQueryShapeButton
        groupId={groupId}
        clusterName={clusterName}
        queryShapeHash={queryShapeHash}
        queryShape={queryShape}
        onQueryShapeBlocked={onQueryShapeBlocked}
        onQueryShapeUnblocked={onQueryShapeUnblocked}
      />
      <QueryShapeDetailsDataTable entry={queryShapeDetails} timeZoneId={timeZoneId} />
    </>
  );
};

interface QueryShapeDetailsTableProps {
  entry: QueryStatsDetails;
  timeZoneId: string;
}

const QueryShapeDetailsDataTable = (props: QueryShapeDetailsTableProps) => {
  const { entry, timeZoneId } = props;
  const queryStats = entry.queryStats;
  return (
    <Table className={cx({ [tableMargin]: queryStats.queryShape != null })}>
      <tbody>
        <>
          <tr>
            <th>Query Shape Hash</th>
          </tr>
          <tr
            className={css`
              grid-template-columns: 1fr !important;
            `}
          >
            <td data-testid="queryShapeDetailsData" colSpan={2}>
              <Copyable className={queryShapeHashCopyComponent}>{queryStats.queryShapeHash}</Copyable>
            </td>
          </tr>
        </>
        <tr>
          <th>Namespace</th>
          <th>Command</th>
        </tr>
        <tr>
          <td data-testid="queryShapeDetailsData">{queryStats.namespace}</td>
          <td data-testid="queryShapeDetailsData">{queryStats.command}</td>
        </tr>
        <tr>
          <th>Total Execution Time</th>
          <th>Average Execution Time</th>
        </tr>
        <tr>
          <td data-testid="queryShapeDetailsData">{formatTime(queryStats.totalWorkingMillis)}</td>
          <td data-testid="queryShapeDetailsData">{formatTime(queryStats.avgWorkingMillis)}</td>
        </tr>
        <tr>
          <th>Last Execution Time</th>
          <th>Execution Count</th>
        </tr>
        <tr>
          <td data-testid="queryShapeDetailsData">{formatTime(queryStats.lastExecMicros, true)}</td>
          <td data-testid="queryShapeDetailsData">{formatNumber(queryStats.execCount, 0)}</td>
        </tr>
        <tr>
          <th>Total Time to Response</th>
          <th>P50 Execution Time</th>
        </tr>
        <tr>
          <td data-testid="queryShapeDetailsData">{formatTime(queryStats.totalTimeToResponseMicros, true)}</td>
          <td data-testid="queryShapeDetailsData">{formatTime(queryStats.p50ExecMicros, true)}</td>
        </tr>
        <tr>
          <th>P90 Execution Time</th>
          <th>P99 Execution Time</th>
        </tr>
        <tr>
          <td data-testid="queryShapeDetailsData">{formatTime(queryStats.p90ExecMicros, true)}</td>
          <td data-testid="queryShapeDetailsData">{formatTime(queryStats.p99ExecMicros, true)}</td>
        </tr>
        <tr>
          <th>Total Docs Examined</th>
          <th>Total Docs Returned</th>
        </tr>
        <tr>
          <td data-testid="queryShapeDetailsData">{formatNumber(queryStats.docsExamined, 0)}</td>
          <td data-testid="queryShapeDetailsData">{formatNumber(queryStats.docsReturned, 0)}</td>
        </tr>
        <tr>
          <th>Docs Examined:Returned Ratio</th>
          <th>Keys Examined:Returned Ratio</th>
        </tr>
        <tr>
          <td data-testid="queryShapeDetailsData">
            {formatNumberWithSingleDigitZero(queryStats.docsExaminedRatio, 2)}
          </td>
          <td data-testid="queryShapeDetailsData">
            {formatNumberWithSingleDigitZero(queryStats.keysExaminedRatio, 2)}
          </td>
        </tr>
        <tr>
          <th>Total Keys Examined</th>
          <th>Bytes Read</th>
        </tr>
        <tr>
          <td data-testid="queryShapeDetailsData">{formatNumber(queryStats.keysExamined, 0)}</td>
          <td data-testid="queryShapeDetailsData">{formatBytes(queryStats.bytesRead)}</td>
        </tr>
        <tr>
          <th>Last Seen Timestamp</th>
          <th>First Seen Timestamp</th>
        </tr>
        <tr>
          <td data-testid="queryShapeDetailsData">
            {formatDate(entry.lastSeen.timestamp, timeZoneId, true, true, false)}
          </td>
          <td data-testid="queryShapeDetailsData">
            {formatDate(entry.firstSeen.timestamp, timeZoneId, true, true, false)}
          </td>
        </tr>
        <tr>
          <th>
            Last Seen Application <DetailsToolTip text={appNameToolTipText} />
          </th>
        </tr>
        <tr
          className={css`
            grid-template-columns: 1fr !important;
          `}
        >
          <td data-testid="queryShapeDetailsData">
            {entry.lastSeen.applicationName == null ? 'Unknown' : entry.lastSeen.applicationName}
          </td>
        </tr>
        <tr>
          <th>
            Last Seen Driver Name and Version <DetailsToolTip text={driverNameVersionToolTipText} />
          </th>
        </tr>
        <tr
          className={css`
            grid-template-columns: 1fr !important;
          `}
        >
          <td data-testid="queryShapeDetailsData">
            {entry.lastSeen.driverName == null
              ? 'Unknown'
              : entry.lastSeen.driverName + ' ' + entry.lastSeen.driverVersion}
          </td>
        </tr>
        <tr>
          <th>
            First Seen Application <DetailsToolTip text={appNameToolTipText} />
          </th>
        </tr>
        <tr
          className={css`
            grid-template-columns: 1fr !important;
          `}
        >
          <td data-testid="queryShapeDetailsData">
            {entry.firstSeen.applicationName == null ? 'Unknown' : entry.firstSeen.applicationName}
          </td>
        </tr>
        <tr>
          <th>
            First Seen Driver Name and Version <DetailsToolTip text={driverNameVersionToolTipText} />
          </th>
        </tr>
        <tr
          className={css`
            grid-template-columns: 1fr !important;
          `}
        >
          <td data-testid="queryShapeDetailsData">
            {entry.firstSeen.driverName == null
              ? 'Unknown'
              : entry.firstSeen.driverName + ' ' + entry.firstSeen.driverVersion}
          </td>
        </tr>
      </tbody>
    </Table>
  );
};

interface DetailsToolTipProps {
  text: ReactNode;
}

const DetailsToolTip = (props: DetailsToolTipProps) => {
  return (
    <Tooltip
      align="top"
      justify="middle"
      triggerEvent="click"
      trigger={
        <span
          css={[
            tooltipIconStyle,
            {
              cursor: 'pointer',
            },
          ]}
        >
          <Icon glyph="InfoWithCircle" />
        </span>
      }
      popoverZIndex={1000}
    >
      {props.text}
    </Tooltip>
  );
};

export default wdyr(QueryShapeDetailsFlyout);
export type { Props as QueryShapeDetailsFlyoutProps };
