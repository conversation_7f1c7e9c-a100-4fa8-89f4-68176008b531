import { useEffect, useState } from 'react';

import styled from '@emotion/styled';
import { Spinner } from '@leafygreen-ui/loading-indicator';
import { palette } from '@leafygreen-ui/palette';
import { useSelector } from 'react-redux';
import { isEqual } from 'underscore';

import { CloudTeams } from '@packages/types/observability';
import type { DeploymentId, Shard, QueryDetails as SlowQueryDetails } from '@packages/types/QueryProfilerInterfaces';
import type { QueryStatsDetails } from '@packages/types/QueryShapeInsightsInterfaces';

import * as viewer from '@packages/redux/common/viewer';
import { QueryProfilerRedux } from '@packages/redux/project/intel/queryProfiler';

import {
  getQueryStatsDetails,
  getSampleSlowQueriesByQueryShapeHash,
} from '@packages/common/services/api/queryShapeInsightsApi';
import { exceptionToMessage } from '@packages/common/services/errorHelper';
import { extractFromTopology } from '@packages/intel/components/vqp/utils';
import { isSomeTopologySelected } from '@packages/intel/utils/hostTopologyUtils';
import wdyr from '@packages/intel/wdyr';
import { sendError } from '@packages/observability';

import QueryShapeDetailsFlyout from './QueryShapeDetailsFlyout';
import SampleSlowQueriesFlyout from './SampleSlowQueriesFlyout';

const ErrorText = styled.p`
  color: ${palette.gray.base};
  margin: 20px auto;
  text-align: center;
  font-size: 16px;
`;

export const UNEXPECTED_ERROR_MESSAGE = 'An unexpected error has occurred. Please try again later.';

interface Props {
  groupId: string;
  clusterName: string;
  selectedHostTopology: Array<Shard>;
  activeTab: 'Sample Queries' | 'Summary';
  queryShapeHash: string;
  changePage: (page: string) => void;
  onNumSlowQueriesChange: (count: number) => void;
  deploymentId: DeploymentId;
  onQueryShapeBlocked?: () => void;
  onQueryShapeUnblocked?: () => void;
}

function DetailsFlyoutController(props: Props) {
  const {
    activeTab,
    groupId,
    clusterName,
    selectedHostTopology,
    queryShapeHash,
    onNumSlowQueriesChange,
    changePage,
    deploymentId,
    onQueryShapeBlocked,
    onQueryShapeUnblocked,
  } = props;
  const [loadingQueryShapeDetails, setLoadingQueryShapeDetails] = useState(true);
  const [queryShapeDetails, setQueryShapeDetails] = useState<QueryStatsDetails>();
  const [detailsError, setDetailsError] = useState<string>();
  const [loadingSampleSlowQueries, setLoadingSampleSlowQueries] = useState(true);
  const [sampleSlowQueries, setSampleSlowQueries] = useState<Array<SlowQueryDetails>>([]);
  const [sampleSlowQueriesError, setSampleSlowQueriesError] = useState<string>();
  const { since, until } = useSelector(QueryProfilerRedux.getTimeWindow, isEqual);
  const hasPiiViewAccess = useSelector(viewer.hasPiiViewAccess);
  const timeZoneId = useSelector(viewer.getTimeZoneId) ?? Intl.DateTimeFormat().resolvedOptions().timeZone;

  useEffect(() => {
    let shouldDiscardData = false;
    setLoadingQueryShapeDetails(true);

    const isSomeSelected = isSomeTopologySelected(selectedHostTopology);
    const hostnamePorts = isSomeSelected
      ? extractFromTopology(selectedHostTopology, (host) => (host.selected ? host.hostname : null)).filter(
          (hostname): hostname is string => hostname != null
        )
      : undefined;

    getQueryStatsDetails(groupId, clusterName, { queryShapeHash, since, until, hostnamePorts })
      .then((res) => {
        if (shouldDiscardData) return;
        setQueryShapeDetails(res);
      })
      .catch((err) => {
        if (shouldDiscardData) return;
        setDetailsError(exceptionToMessage(err));
      })
      .finally(() => {
        if (!shouldDiscardData) {
          setLoadingQueryShapeDetails(false);
        }
      });

    if (hasPiiViewAccess && deploymentId.type === 'replicaSet') {
      const hostIds = isSomeSelected
        ? extractFromTopology(selectedHostTopology, (host) => (host.selected ? host.hostId : null)).filter(
            (hostId): hostId is string => hostId != null
          )
        : extractFromTopology(selectedHostTopology, (host) => host.hostId);

      if (hostIds.length === 0) {
        // 1. Log for debugging and send to Sentry
        const errorMessage = 'QueryShapeInsights: No hostIds available for Sample Queries';
        const contextData = {
          selectedHostTopology: selectedHostTopology.length,
          deploymentId: deploymentId.id,
        };

        console.error(errorMessage, contextData);

        sendError({
          error: new Error(errorMessage),
          team: CloudTeams.InsightsAndTelemetry,
          extras: contextData,
          tags: {
            component: 'QueryShapeInsights',
            issue: 'missing_host_ids',
          },
        });

        // 2. Set error state for user feedback
        if (!shouldDiscardData) {
          setSampleSlowQueriesError('Sample queries temporarily unavailable');
          setLoadingSampleSlowQueries(false);
        }
        return;
      }

      if (!shouldDiscardData) {
        setLoadingSampleSlowQueries(true);
      }
      getSampleSlowQueriesByQueryShapeHash(groupId, queryShapeHash, { since, until, hostIds })
        .then((res) => {
          if (shouldDiscardData) return;
          setSampleSlowQueries(res.toSorted((a, b) => b.timestamp - a.timestamp));
          onNumSlowQueriesChange(res.length);
        })
        .catch((err) => {
          if (shouldDiscardData) return;
          setSampleSlowQueriesError(exceptionToMessage(err));
        })
        .finally(() => {
          if (!shouldDiscardData) {
            setLoadingSampleSlowQueries(false);
          }
        });
    }

    return () => {
      shouldDiscardData = true;
    };
  }, [
    clusterName,
    groupId,
    hasPiiViewAccess,
    selectedHostTopology,
    onNumSlowQueriesChange,
    queryShapeHash,
    since,
    until,
    deploymentId,
  ]);

  if (!queryShapeHash) {
    return <ErrorText>{UNEXPECTED_ERROR_MESSAGE}</ErrorText>;
  }

  if (activeTab === 'Sample Queries') {
    if (!hasPiiViewAccess || sampleSlowQueriesError)
      return (
        <ErrorText>
          {!hasPiiViewAccess
            ? 'Additional permissions are required to view sample slow queries.'
            : (sampleSlowQueriesError ?? UNEXPECTED_ERROR_MESSAGE)}
        </ErrorText>
      );

    if (loadingSampleSlowQueries)
      return <Spinner displayOption="xlarge-vertical" data-testid="sample-queries-spinner" />;

    return <SampleSlowQueriesFlyout slowQueries={sampleSlowQueries} timeZoneId={timeZoneId} changePage={changePage} />;
  }

  if (loadingQueryShapeDetails)
    return <Spinner displayOption="xlarge-vertical" data-testid="query-shape-details-spinner" />;

  if (detailsError || !queryShapeDetails) {
    return <ErrorText>{detailsError ?? UNEXPECTED_ERROR_MESSAGE}</ErrorText>;
  }

  return (
    <QueryShapeDetailsFlyout
      queryShapeDetails={queryShapeDetails}
      timeZoneId={timeZoneId}
      groupId={groupId}
      clusterName={clusterName}
      onQueryShapeBlocked={onQueryShapeBlocked}
      onQueryShapeUnblocked={onQueryShapeUnblocked}
    />
  );
}

export default wdyr(DetailsFlyoutController);
export type { Props as DetailsFlyoutProps };
