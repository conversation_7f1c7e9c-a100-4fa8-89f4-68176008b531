# JIRA Ticket Creation (CLOUDP Project)

## Required Fields - ALL MUST BE PROVIDED

Every ticket creation MUST include these exact fields or it will fail:

```python
mcp__docker - mcp__jira_create_issue(
    project_key="CLOUDP",  # Always CLOUDP
    summary="<title>",  # Clear, specific, <255 chars
    issue_type="<type>",  # Bug|Story|Investigation
    description="<details>",  # Include context, steps, impact
    components="<component>",  # See valid list below
    additional_fields={
      "priority": {"name": "<priority>"},  # Critical - P2 | Major - P3 | Minor - P4
      "customfield_12751": [{"value": "<team>"}],  # Assigned team (see list below)
      "customfield_10857": "<epic_key>"  # Epic link (optional, e.g. "CLOUDP-275436")
    }
)
```

## Field Selection Rules

**Issue Type:**

- Bug: Errors, failures, broken functionality
- Story: Features, enhancements, general work (default)
- Investigation: Research tasks (rare)

**Components:**

- Atlas: Cluster/migration/deployment issues (default)

**Priority:**

- Critical - P2: Production down, data loss risk, security
- Major - P3: Everything else (default)
- Minor - P4: Low impact, nice-to-have

**Teams (customfield_12751):**
**ASK USER FOR THIS**

**Epic Link (customfield_10857):**

- Optional field to link ticket to an epic
- Provide epic key as string (e.g., "CLOUDP-275436")
- Use when ticket is part of a larger feature or initiative

## Example

```python
mcp__docker - mcp__jira_create_issue(
    project_key="CLOUDP",
    summary="Atlas cluster fails to restart after maintenance",
    issue_type="Bug",
    description="Cluster abc123 failed restart. Error: timeout. Impact: 8min downtime.",
    components="Atlas",
    additional_fields={
      "priority": {"name": "Critical - P2"},
      "customfield_12751": [{"value": "Automation"}],
      "customfield_10857": "CLOUDP-275436"
    }
)
```

## Critical Rules

- NEVER omit priority or customfield_12751 - ticket will fail
- Field values are case-sensitive - use exactly as shown
- When uncertain: Story + Major - P3 + Atlas
