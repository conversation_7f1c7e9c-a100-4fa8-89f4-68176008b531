# Built-in Starlark repository rules
load("@bazel_tools//tools/build_defs/repo:git.bzl", "git_repository", "new_git_repository")
load("@bazel_tools//tools/build_defs/repo:http.bzl", "http_archive", "http_file")

#####
# External Starlark rules
#####

load(
    "//server:bazel_versions.bzl",
    "GO_VERSION",
    "IO_GRPC_VERSION",
    "PROTOBUF_VERSION",
    "PYTHON_VERSION",
    "RULES_PKG_SHA",
    "RULES_PKG_VER",
    "RULES_PYTHON_GAZELLE_VERSION",
    "RULES_PYTHON_SHA",
    "RULES_PYTHON_VERSION",
)

PROTOBUF_PATCHES = [
    "//server/scripts/bazel/protobuf:redirect_protoc_protobuf.diff",
]

http_archive(
    name = "com_google_protobuf",
    patches = PROTOBUF_PATCHES,
    sha256 = "3ad017543e502ffaa9cd1f4bd4fe96cf117ce7175970f191705fa0518aff80cd",
    strip_prefix = "protobuf-%s" % PROTOBUF_VERSION,
    urls = [
        "https://github.com/protocolbuffers/protobuf/archive/refs/tags/v%s.tar.gz" % PROTOBUF_VERSION,
    ],
)

http_archive(
    name = "com_google_protobuf_javalite",
    patches = PROTOBUF_PATCHES,
    sha256 = "3ad017543e502ffaa9cd1f4bd4fe96cf117ce7175970f191705fa0518aff80cd",
    strip_prefix = "protobuf-%s" % PROTOBUF_VERSION,
    urls = [
        "https://github.com/protocolbuffers/protobuf/archive/refs/tags/v%s.tar.gz" % PROTOBUF_VERSION,
    ],
)

################################################################################
# Python rules
################################################################################

http_archive(
    name = "rules_python",
    sha256 = RULES_PYTHON_SHA,
    strip_prefix = "rules_python-{version}".format(version = RULES_PYTHON_VERSION),
    url = "https://github.com/bazelbuild/rules_python/releases/download/{version}/rules_python-{version}.tar.gz".format(version = RULES_PYTHON_VERSION),
)

# https://github.com/bazelbuild/rules_python/tree/main/gazelle
# NOTE: This is separated from rules_python due to gazelle macros that use absolute paths which
#   require the WORKSPACE in the gazelle to be at the root e.g.
#   https://github.com/bazelbuild/rules_python/blob/7d82f06e7ba1cf217c6d855cd5ffbb3b6598587c/gazelle/manifest/defs.bzl#L56
http_archive(
    name = "rules_python_gazelle_plugin",
    sha256 = RULES_PYTHON_SHA,
    strip_prefix = "rules_python-{version}/gazelle".format(version = RULES_PYTHON_GAZELLE_VERSION),
    url = "https://github.com/bazelbuild/rules_python/releases/download/{version}/rules_python-{version}.tar.gz".format(version = RULES_PYTHON_GAZELLE_VERSION),
)

load("@rules_python//python:repositories.bzl", "py_repositories", "python_register_toolchains")

py_repositories()

load("@rules_python//python:pip.bzl", "pip_parse")
load(
    "@rules_python//python:versions.bzl",
    PYTHON_TOOL_VERSIONS = "TOOL_VERSIONS",
    get_python_release_info = "get_release_info",
)

(_, python_urls, _, _, _) = get_python_release_info(
    platform = "x86_64-unknown-linux-gnu",
    python_version = PYTHON_VERSION,
)

# Reference: https://github.com/bazelbuild/rules_python/blob/main/python/versions.bzl
http_file(
    name = "python3_linux_x86_64_interpreter_archive",
    downloaded_file_path = "python.tar.gz",
    sha256 = PYTHON_TOOL_VERSIONS[PYTHON_VERSION]["sha256"]["x86_64-unknown-linux-gnu"],
    url = python_urls[0],
)

# Packaging (depends on skylib and rules_python)
http_archive(
    name = "rules_pkg",
    sha256 = RULES_PKG_SHA,
    urls = [
        "https://mirror.bazel.build/github.com/bazelbuild/rules_pkg/releases/download/{v}/rules_pkg-{v}.tar.gz".format(v = RULES_PKG_VER),
        "https://github.com/bazelbuild/rules_pkg/releases/download/{v}/rules_pkg-{v}.tar.gz".format(v = RULES_PKG_VER),
    ],
)

load("@rules_pkg//:deps.bzl", "rules_pkg_dependencies")

rules_pkg_dependencies()

# Override rules_go https://github.com/bazelbuild/rules_docker/issues/2036
http_archive(
    name = "io_bazel_rules_go",
    sha256 = "80a98277ad1311dacd837f9b16db62887702e9f1d1c4c9f796d0121a46c8e184",
    urls = [
        "https://mirror.bazel.build/github.com/bazelbuild/rules_go/releases/download/v0.46.0/rules_go-v0.46.0.zip",
        "https://github.com/bazelbuild/rules_go/releases/download/v0.46.0/rules_go-v0.46.0.zip",
    ],
)

# Override gazelle https://github.com/bazelbuild/rules_docker/issues/2157
http_archive(
    name = "bazel_gazelle",
    patches = [
        "//server/scripts/bazel:allow_testonly_in_gazelle.diff",
    ],
    sha256 = "75df288c4b31c81eb50f51e2e14f4763cb7548daae126817247064637fd9ea62",
    urls = [
        "https://mirror.bazel.build/github.com/bazelbuild/bazel-gazelle/releases/download/v0.36.0/bazel-gazelle-v0.36.0.tar.gz",
        "https://github.com/bazelbuild/bazel-gazelle/releases/download/v0.36.0/bazel-gazelle-v0.36.0.tar.gz",
    ],
)

load("@io_bazel_rules_go//go:deps.bzl", "go_register_toolchains", "go_rules_dependencies")
load("//third_party/go:repositories.bzl", "go_deps")

# gazelle:repository_macro third_party/go/repositories.bzl%go_deps
go_deps()

go_rules_dependencies()

go_register_toolchains(
    nogo = "@//tools/go:nogo",
    version = GO_VERSION,
)

load("@bazel_gazelle//:deps.bzl", "gazelle_dependencies")

gazelle_dependencies()

################################################################################
# Aspect
################################################################################

http_archive(
    name = "aspect_bazel_lib",
    sha256 = "cea19e6d8322fb212f155acb58d1590f632e53abde7f1be5f0a086a93cf4c9f4",
    strip_prefix = "bazel-lib-2.8.0",
    url = "https://github.com/aspect-build/bazel-lib/releases/download/v2.8.0/bazel-lib-v2.8.0.tar.gz",
)

# Must be loaded before aspect_rules_js / aspect_rules_ts / oci_register_toolchains
load(
    "@aspect_bazel_lib//lib:repositories.bzl",
    "aspect_bazel_lib_dependencies",
    "register_copy_directory_toolchains",
    "register_copy_to_directory_toolchains",
    "register_coreutils_toolchains",
    "register_expand_template_toolchains",
    "register_tar_toolchains",
)

aspect_bazel_lib_dependencies()

register_copy_directory_toolchains()

register_copy_to_directory_toolchains()

# See: https://github.com/aspect-build/rules_js/releases/tag/v1.35.0
# To use rules_js / rules_ts with bazel-lib 2.x, you must additionally register the coreutils toolchain.
register_coreutils_toolchains()

register_tar_toolchains()

register_expand_template_toolchains()

################################################################################
# Container rules
################################################################################

# rules_oci https://github.com/bazel-contrib/rules_oci/releases (Replaces rules_docker)
http_archive(
    name = "rules_oci",
    sha256 = "d41d0ba7855f029ad0e5ee35025f882cbe45b0d5d570842c52704f7a47ba8668",
    strip_prefix = "rules_oci-1.4.3",
    url = "https://github.com/bazel-contrib/rules_oci/releases/download/v1.4.3/rules_oci-v1.4.3.tar.gz",
)

load("@rules_oci//oci:dependencies.bzl", "rules_oci_dependencies")

rules_oci_dependencies()

load("@rules_oci//oci:repositories.bzl", "LATEST_CRANE_VERSION", "oci_register_toolchains")

oci_register_toolchains(
    name = "oci",
    crane_version = LATEST_CRANE_VERSION,
)

load("@rules_oci//oci:pull.bzl", "oci_pull")

# Pull Containers
#===============================================================================

# Only one of 'digest' or 'tag' may be set
# Update digest with:
# login_ecr_registry() { aws ecr get-login-password | docker login -u AWS --password-stdin ${1} }
# get_digest() {
#     docker manifest inspect ${1}/${2}:${3:-"latest"} -v | jq -r \
#         'if type == "array" then .[] else . end |
#         .Descriptor | select(.platform.architecture == "amd64") |
#         select(.platform.os == "linux") | .digest'
# }
# get_digest "<registry>" "<repository>"
oci_pull(
    name = "rhel8_ubi_oci",
    digest = "sha256:70fe679f2a24b76d5e90fee30c1616afc4f96eed13d77a2ad04af0261c928fb1",
    registry = "registry.access.redhat.com",
    repository = "ubi8/ubi-minimal",
)

oci_pull(
    name = "amazonlinux2023-python-base",
    digest = "sha256:2ffae3f5225c55eb4f18074ddbacc6c4524e5bb0dc9f1878cc5bb94303d3ff5e",
    platforms = ["linux/amd64"],
    registry = "************.dkr.ecr.us-east-1.amazonaws.com",
    repository = "base/amazonlinux2023-python",
)
# FUTURE: Update to use in drone-bazelisk-ecr
# oci_pull(
#     name = "cloud_bot_base_oci",
#     digest = "sha256:08f2925658d00f5d8deed2ba128b20914f024a83c532b2f210c757e974190ec9",
#     registry = "************.dkr.ecr.us-east-1.amazonaws.com",
#     repository = "cloud-build/cloud-bot-base",
#     # reproducible = False,  # Required to pull from a tag
#     # tag = "latest",
# )

# FUTURE: remove io_bazel_rules_docker when fully replaced by rules_oci
# Docker (depends on rules_python, rules_go, skylib, bazel_gazelle and rules_pkg)
http_archive(
    name = "io_bazel_rules_docker",
    patches = [
        "//server/scripts/bazel:rules_docker_simple_disabled_transitions.diff",
        "//server/scripts/bazel:rules_docker-exclude_xattrs_from_tarballs.patch",
    ],
    sha256 = "b1e80761a8a8243d03ebca8845e9cc1ba6c82ce7c5179ce2b295cd36f7e394bf",
    urls = ["https://github.com/bazelbuild/rules_docker/releases/download/v0.25.0/rules_docker-v0.25.0.tar.gz"],
)

load(
    "@io_bazel_rules_docker//toolchains/docker:toolchain.bzl",
    docker_toolchain_configure = "toolchain_configure",
)

docker_toolchain_configure(
    name = "docker_config",
    client_config = "@//:.docker/mms/config.json",
    docker_flags = [
        "--log-level=info",
    ],
)

load(
    "@io_bazel_rules_docker//repositories:repositories.bzl",
    container_repositories = "repositories",
)

container_repositories()

load("@io_bazel_rules_docker//repositories:deps.bzl", container_deps = "deps")

container_deps()

# FUTURE: remove this toolchain after oci container migration if no longer needed
register_toolchains("//scripts/bazel_python:container_py_toolchain")

# depends on bazel_python:container_py_toolchain
python_register_toolchains(
    name = "python3",
    ignore_root_user_error = False,
    python_version = PYTHON_VERSION,
)

# FUTURE: remove after oci container migration if no longer needed
load("@io_bazel_rules_docker//java:image.bzl", _java_image_repos = "repositories")

_java_image_repos()

# We're not using py3_image directly but we call into one of its callees to build the app layer
# So we call this macro to set up the toolchains
load(
    "@io_bazel_rules_docker//python3:image.bzl",
    _py3_image_repos = "repositories",
)

_py3_image_repos()

# rules_js (https://github.com/aspect-build/rules_js)
http_archive(
    name = "aspect_rules_js",
    sha256 = "4cab6898f0ff8048e32640cce06a47aa4b92b2fb330d055940f95f24c8ebb868",
    strip_prefix = "rules_js-2.0.1",
    url = "https://github.com/aspect-build/rules_js/releases/download/v2.0.1/rules_js-v2.0.1.tar.gz",
)

load("@aspect_rules_js//js:repositories.bzl", "rules_js_dependencies")

rules_js_dependencies()

# rules_ts (https://github.com/aspect-build/rules_ts)
http_archive(
    name = "aspect_rules_ts",
    sha256 = "ee7dcc35faef98f3050df9cf26f2a72ef356cab8ad927efb1c4dc119ac082a19",
    strip_prefix = "rules_ts-3.0.0",
    url = "https://github.com/aspect-build/rules_ts/releases/download/v3.0.0/rules_ts-v3.0.0.tar.gz",
)

load("@aspect_rules_ts//ts:repositories.bzl", "rules_ts_dependencies")

rules_ts_dependencies(
    ts_version_from = "//:package.json",
)

# load("//toolchains/nodejs:defs.bzl", "NODE_VERSION", "NODE_VERSIONS")
load("//toolchains/nodejs:defs.bzl", "define_platform_specific_nodejs_toolchains")

# rules_js_register_toolchains(
#     name = "nodejs",
#     node_repositories = NODE_VERSIONS,
#     node_urls = [
#         "https://nodejs.org/dist/v{version}/{filename}",
#     ],
#     node_version = NODE_VERSION,
# )

# we can't use rules_js_register_toolchains for multiple NodeJS toolchains because it
# creates the toolchains AND registers them. So we cannot control any of the settings
# that control compatibility. Instead, we will manually register the toolchains to have
# more granular control to ensure only ONE NodeJS toolchain is loaded.
define_platform_specific_nodejs_toolchains()

register_toolchains(
    "//toolchains/nodejs:nodejs_linux_amd64_glibc_gte_2_28",
    "//toolchains/nodejs:nodejs_linux_amd64_glibc_lt_2_28",
    "//toolchains/nodejs:nodejs_linux_arm64_glibc_gte_2_28",
    "//toolchains/nodejs:nodejs_linux_arm64_glibc_lt_2_28",
    "//toolchains/nodejs:nodejs_darwin_amd64",
    "//toolchains/nodejs:nodejs_darwin_arm64",
)

# rules_cypress
http_archive(
    name = "aspect_rules_cypress",
    sha256 = "598b3cbab64c8d44a603dde055687c19cc4de76fbbb631871f0c97e5f472876a",
    strip_prefix = "rules_cypress-0.6.0",
    url = "https://github.com/aspect-build/rules_cypress/releases/download/v0.6.0/rules_cypress-v0.6.0.tar.gz",
)

load("@aspect_rules_cypress//cypress:dependencies.bzl", "rules_cypress_dependencies")
load("@aspect_rules_cypress//cypress:repositories.bzl", "cypress_register_toolchains")

rules_cypress_dependencies()

cypress_register_toolchains(
    name = "cypress",

    # Regenerate with `bazel run @aspect_rules_cypress//scripts:mirror_releases <VERSION_NUMBER>`
    cypress_integrity = {
        "13.17.0": {
            "darwin-x64": "1cef9ccca2d31e0273fdd8fa49b90f100a5b2fd553f422c30079892b231d1342",
            "darwin-arm64": "45cb0824d223c491634b23acc7eb0082755dbe7475858112f5130b5bf8baf0bf",
            "linux-x64": "5d3f50a8e491067288d27567a01fabf39938375870cdf1107ba6620f8f81a2f0",
            "linux-arm64": "d574c41661e05cad44ea66f56b323bf89e8a5d8d6a21acb3fc0f36d7ae29a5ec",
            "win32-x64": "93532f4df3ac8c1b4019d67b9a247cc7ab1fe5b450af8f8bdd61d05049197509",
        },
    },
    cypress_version = "13.17.0",
)

git_repository(
    name = "io_grpc_grpc_proto",
    commit = "5ba669a3292d2b2808be40f08e15185567e223d8",
    remote = "https://github.com/grpc/grpc-proto.git",
)

load("@com_google_protobuf//:protobuf_deps.bzl", "protobuf_deps")

protobuf_deps()

http_archive(
    name = "rules_proto_grpc",
    patches = [
        "//server/scripts/bazel:update_plugin_rules_proto_grpc.diff",
    ],
    sha256 = "c0d718f4d892c524025504e67a5bfe83360b3a982e654bc71fed7514eb8ac8ad",
    strip_prefix = "rules_proto_grpc-4.6.0",
    urls = ["https://github.com/rules-proto-grpc/rules_proto_grpc/archive/4.6.0.tar.gz"],
)

load("@rules_proto_grpc//:repositories.bzl", "rules_proto_grpc_repos", "rules_proto_grpc_toolchains")

rules_proto_grpc_repos()

rules_proto_grpc_toolchains()

http_archive(
    name = "rules_buf",
    sha256 = "433bc0ff54883998b07a4c7f6ffd2a2d6074d1bf5c401fff14c89b2047965d5a",
    strip_prefix = "rules_buf-0.4.0",
    urls = [
        "https://github.com/bufbuild/rules_buf/archive/refs/tags/v0.4.0.zip",
    ],
)

load("@rules_buf//buf:repositories.bzl", "rules_buf_dependencies", "rules_buf_toolchains")

rules_buf_dependencies()

rules_buf_toolchains(version = "v1.54.0")

load("@rules_buf//gazelle/buf:repositories.bzl", "gazelle_buf_dependencies")

gazelle_buf_dependencies()

http_archive(
    name = "io_grpc_grpc_java",
    patches = [
        "//server/scripts/bazel:update_plugin_grpc_java.diff",
    ],
    repo_mapping = {
        "@com_github_cncf_udpa": "@com_github_cncf_xds",
    },
    sha256 = "eca44a9f3eb341daf7a01482b96016dfa7d91baee495a697746c4724868a06db",
    strip_prefix = "grpc-java-%s" % IO_GRPC_VERSION,
    url = "https://github.com/grpc/grpc-java/archive/refs/tags/v%s.tar.gz" % IO_GRPC_VERSION,
)

load("@io_grpc_grpc_java//:repositories.bzl", "grpc_java_repositories")

grpc_java_repositories()

load("@rules_proto_grpc//java:repositories.bzl", "java_repos")

java_repos()

load("@com_google_googleapis//:repository_rules.bzl", "switched_rules_by_language")

switched_rules_by_language(
    name = "com_google_googleapis_imports",
    java = True,
)

new_git_repository(
    name = "open_telemetry_opentelemetry_proto",
    commit = "c4dfbc51f3cd4089778555a2ac5d9bc093ed2956",
    patches = ["//server/scripts/bazel:open_telemetry_opentelemetry_proto_build_files.patch"],
    remote = "https://github.com/open-telemetry/opentelemetry-proto.git",
)

new_git_repository(
    name = "com_xgen_mdb_idl",
    build_file = "//server/scripts/bazel:BUILD.mdb_idl",
    commit = "2d8f4440ebe0ced8830b7d2b6589b1cc5fe0b225",
    patches = [
        "//server/scripts/bazel:canine_proto.diff",
        "//server/scripts/bazel:pops_protos.diff",
        "//server/scripts/bazel:officeips_proto.diff",
    ],
    remote = "**************:10gen/mdb_idl",
)

#Multirun tools
git_repository(
    name = "multirun_bazel_tools",
    commit = "4daedde3ec61a03db841c8a9ca68288972e25a82",
    remote = "https://github.com/ash2k/bazel-tools.git",
    repo_mapping = {"@com_github_ash2k_bazel_tools": "@multirun_bazel_tools"},
    shallow_since = "1636776104 +1100",
)

load("@multirun_bazel_tools//multirun:deps.bzl", "multirun_dependencies")

multirun_dependencies()

git_repository(
    name = "aspect_rules_format",
    commit = "e218fb0982f0598ed6ddac8218ecda7dc19ce31f",
    patches = [
        "//client/scripts/git:custom-formatters.patch",  # Needed to access the import sorting plugin, rules_js v2 and our black version
    ],
    remote = "https://github.com/aspect-build/bazel-super-formatter.git",
)

load("@aspect_rules_format//format:repositories.bzl", "rules_format_dependencies")

rules_format_dependencies()

#####
# End of Starlark dependencies
#####

load("//server:bazel_rje_deps.bzl", "get_rje_artifacts", "grpc_override_targets")

# Java dependencies
MAVEN_REPOSITORIES = [
    "https://artifactory.corp.mongodb.com/artifactory/maven",
    "https://repo1.maven.org/maven2",
]

# Load dependencies created by rules_jvm_external
load("@rules_jvm_external//:defs.bzl", "maven_install")

http_archive(
    name = "io_cucumber_junit_xml_formatter",
    build_file = "//third_party/cucumber:BUILD.junit-xml-formatter",
    patches = ["//third_party/cucumber:junitxmlformatter_pickle_name.patch"],
    sha256 = "26df13282552c2aeba0086f4737f8e678320c522d8aed9f09ed5a986ce0ac2fc",
    strip_prefix = "junit-xml-formatter-0.1.0",
    urls = ["https://github.com/cucumber/junit-xml-formatter/archive/refs/tags/v0.1.0.tar.gz"],
)

MMS_OVERRIDE_TARGETS = {
    "io.cucumber:junit-xml-formatter": "@io_cucumber_junit_xml_formatter//:junitxmlformatter",
    "org.hamcrest:hamcrest-all": "org.hamcrest:hamcrest",
    "com.jcraft:jsch": "@maven//:com_github_mwiede_jsch",
}

maven_install(
    artifacts = get_rje_artifacts(),
    excluded_artifacts = [
        # Things migrated from Oracle/javax/J2EE to the Eclipse Foundation
        "javax.activation:activation-api",
        "javax.activation:activation",
        "javax.annotation:javax.annotation-api",
        "javax.inject:inject-api",
        "javax.inject:javax.inject",
        "javax.mail:mail",
        "com.sun.mail:javax.mail",
        "com.sun.activation:jakarta.activation",
        "javax.servlet:servlet-api",
        "javax.xml:jaxrpc-api",
        "javax.xml.soap:saaj-api",
        # Things we have direct deps on
        "org.glassfish.hk2.external:aopalliance-repackaged",
        "org.glassfish.hk2.external:jakarta.inject",

        # Using SLF4J legacy bridging APIs instead
        "log4j:log4j",

        # Migrated from Bouncy Castle to Bouncy Castle BCFIPS
        "org.bouncycastle:bcprov-jdk15on",
        "org.bouncycastle:bcpkix-jdk15on",
        "org.bouncycastle:bcpg-jdk15on",
        "org.bouncycastle:bcprov-jdk18on",
        "org.bouncycastle:bcpkix-jdk18on",
        "org.bouncycastle:bcpg-jdk18on",

        # Prefer the other implementation of RequestExecutorFactory
        "com.okta.commons:okta-http-okhttp",

        # Exclude deprecated org.hamcrest:hamcrest-all (favor of the new org.hamcrest:hamcrest)
        "org.hamcrest:hamcrest-all",

        # Use drop in replacement for JSCH
        "com.jcraft:jsch",

        # This does not compile, so avoid resolving.
        # Please keep this in line with server/bazel_rje_deps.bzl.
        "io.grpc:grpc-okhttp",
    ],
    fetch_sources = True,
    generate_compat_repositories = True,
    maven_install_json = "@//:maven_install.json",
    override_targets = grpc_override_targets() | MMS_OVERRIDE_TARGETS,
    repositories = MAVEN_REPOSITORIES,
    strict_visibility = True,
    # switching the version conflict policy back to default would cause alphas / betas of our direct dependencies
    # to be brought in if one of our other direct dependencies used it as well without pinning a version of it
    version_conflict_policy = "pinned",
)

load("@maven//:defs.bzl", "pinned_maven_install")

pinned_maven_install()

load("@maven//:compat.bzl", "compat_repositories")

compat_repositories()

##################################################################################################
### Systems Related functionality
##################################################################################################

###
# Common Maven dependencies. These are utilized by anything in the systems directory, but will
# only contain artifacts necessary for the systems/common directory.
###
load("//systems/common/java:maven_deps.bzl", install_maven_common = "install_maven")

install_maven_common(MAVEN_REPOSITORIES)

load("@maven_common//:defs.bzl", pinned_maven_common_install = "pinned_maven_install")

pinned_maven_common_install()

###
# Events Maven dependencies. These are utilized by anything in the systems/events directory.
###
load("//systems/events:maven_deps.bzl", install_maven_events = "install_maven")

install_maven_events(MAVEN_REPOSITORIES)

load("@maven_events//:defs.bzl", pinned_maven_events_install = "pinned_maven_install")

pinned_maven_events_install()

###
# Metering Maven dependencies. These are utilized by anything in the systems/metering directory.
###
load("//systems/metering:maven_deps.bzl", install_maven_metering = "install_maven")

install_maven_metering(MAVEN_REPOSITORIES)

load("@maven_metering//:defs.bzl", pinned_maven_metering_install = "pinned_maven_install")

pinned_maven_metering_install()

###
# Pricing Maven dependencies. These are utilized by anything in the systems/pricing directory.
###
load("//systems/pricing:maven_deps.bzl", install_maven_pricing = "install_maven")

install_maven_pricing(MAVEN_REPOSITORIES)

load("@maven_pricing//:defs.bzl", pinned_maven_pricing_install = "pinned_maven_install")

pinned_maven_pricing_install()

###
# Prototypes Maven dependencies. These are utilized by anything in the systems/prototypes directory.
###
load("//systems/prototypes:maven_deps.bzl", install_maven_prototypes = "install_maven")

install_maven_prototypes(MAVEN_REPOSITORIES)

load("@maven_prototypes//:defs.bzl", pinned_maven_prototypes_install = "pinned_maven_install")

pinned_maven_prototypes_install()

###
# RT Agent Maven dependencies. These are utilized by anything in the systems/rtagent directory.
###
load("//systems/rtagent:maven_deps.bzl", install_maven_rtagent = "install_maven")

install_maven_rtagent(MAVEN_REPOSITORIES)

load("@maven_rtagent//:defs.bzl", pinned_maven_rtagent_install = "pinned_maven_install")

pinned_maven_rtagent_install()
##################################################################################################

install_maven_metering(MAVEN_REPOSITORIES)

pinned_maven_metering_install()
##################################################################################################

install_maven_pricing(MAVEN_REPOSITORIES)

pinned_maven_pricing_install()
##################################################################################################

# bazel_common
# This is below our Java dependencies so it will use our versions of Maven artifacts
git_repository(
    name = "bazel_common",
    commit = "5a98ecc1193057db05a19c19a5853f95703749c4",
    patches = ["//server/scripts/bazel:classifier_dep_block.diff"],
    remote = "https://github.com/google/bazel-common.git",
)

load("@bazel_common//:workspace_defs.bzl", "google_common_workspace_rules")

google_common_workspace_rules()

# Fork of rules_jvm
git_repository(
    name = "contrib_rules_jvm",
    commit = "4fcdea8a0b5d04cade2ca1fe4951bcb28d5d539e",
    remote = "**************:10gen/rules_jvm.git",
)

load("@contrib_rules_jvm//:repositories.bzl", "contrib_rules_jvm_deps")

contrib_rules_jvm_deps()

load("@contrib_rules_jvm//:setup.bzl", "contrib_rules_jvm_setup")

contrib_rules_jvm_setup()

load("@contrib_rules_jvm//:gazelle_setup.bzl", "contrib_rules_jvm_gazelle_setup")

contrib_rules_jvm_gazelle_setup()

# JDKs for local development
# Update and re-run the repackager script: https://wiki.corp.mongodb.com/pages/viewpage.action?pageId=88082467
# Also update .tool-versions

# Javascript dependencies
load("@aspect_rules_js//npm:repositories.bzl", "npm_translate_lock")

# https://github.com/aspect-build/rules_js/blob/main/docs/npm_translate_lock.md
npm_translate_lock(
    # Creates a new repository named "@npm". Name can be arbitrary.
    name = "npm",
    # Move binary files in node_modules/.bin for packages in the lock file.
    bins = {
        # Derived from "bin" attribute in node_modules/next/package.json.
        "next": {
            "next": "./dist/bin/next",
        },
    },
    data = [],
    lifecycle_hooks_envs = {
        "cypress": ["CYPRESS_INSTALL_BINARY=0"],  # rules_cypress will provide the binary when needed
    },
    # prevent some packages from running npm lifecycle hooks that aren't needed in mms
    lifecycle_hooks_exclude = [
        "macos-export-certificate-and-key",
        "os-dns-native",
        "win-export-certificate-and-key",
    ],
    lifecycle_hooks_execution_requirements = {
        # This package installed improperly (missing some postinstall files) and got cached
        "electron": [
            "no-sandbox",
            "no-remote-cache",
        ],
    },
    npmrc = "//:.npmrc",
    patch_args = {"*": ["-p1"]},
    pnpm_lock = "//:pnpm-lock.yaml",
    pnpm_version = "9.7.1",  # sync with .tool-versions
    public_hoist_packages = {
        # focus-trap-react is a module from the LeafyGreen modal which we stub for testing, we want to expose the right version
        "focus-trap-react@9.0.2": [""],
        # tabbable is a dependency of focus-trap which we need to overwrite for testing the LeafyGreen GuideCue in jest
        "tabbable@5.3.3": [""],
        "events@3.3.0": [""],
        "string_decoder@0.10.31": [""],
    },
    verify_node_modules_ignored = "//:.bazelignore",
)

load("@npm//:repositories.bzl", "npm_repositories")

# Create `npm_import` rule for each package in package.json (https://github.com/aspect-build/rules_js/blob/main/docs/npm_import.md)
npm_repositories()

load("@aspect_rules_format//format:dependencies.bzl", "parse_dependencies")

parse_dependencies()

# Docker base images
load(
    "@docker_config//:pull.bzl",
    "container_pull",
)

container_pull(
    name = "rhel8_ubi",
    architecture = "amd64",
    os = "linux",
    registry = "registry.access.redhat.com",
    repository = "ubi8/ubi-minimal",
    tag = "latest",
)

container_pull(
    name = "cloud_bot_base",
    architecture = "amd64",
    os = "linux",
    registry = "************.dkr.ecr.us-east-1.amazonaws.com",
    repository = "cloud-build/cloud-bot-base",
    tag = "latest",
)

container_pull(
    name = "centos7-base",
    architecture = "amd64",
    os = "linux",
    registry = "************.dkr.ecr.us-east-1.amazonaws.com",
    repository = "base/centos7",
    tag = "20210425-0a4afb7",
)

container_pull(
    name = "amazonlinux2-x86_64",
    architecture = "amd64",
    digest = "sha256:2303151ffd136d5d26e11013943c29d7995b8decf57299e2f53033d621711db9",
    os = "linux",
    registry = "public.ecr.aws",
    repository = "amazonlinux/amazonlinux",
    tag = "2.0.20240916.0-amd64",
)

container_pull(
    name = "amazonlinux2-arm64",
    architecture = "arm64",
    digest = "sha256:fcef42fd2ad2027b04dd978a902e802b31b033f0a8424db96a08a3c08e5fb7e0",
    os = "linux",
    registry = "public.ecr.aws",
    repository = "amazonlinux/amazonlinux",
    tag = "2.0.20240916.0-arm64v8",
)

container_pull(
    name = "amazonlinux2-base",
    architecture = "amd64",
    digest = "sha256:1c34be30fd76c8795f09d5873dd0831704e70f922fc8a9c7a759525446e8f8d3",
    os = "linux",
    registry = "************.dkr.ecr.us-east-1.amazonaws.com",
    repository = "base/amazonlinux2-jdk",
    # changing this alone won't do anything, you need to change the digest as well
    tag = "v17.0.15.0.0.6-0-202509042021-7d95c2b",
)

container_pull(
    name = "amazonlinux2-python-base",
    architecture = "amd64",
    digest = "sha256:9d9750ba1e5023d0bb949a0ab055f0d7b708638966772adfe4288c54d074d5a4",
    os = "linux",
    registry = "************.dkr.ecr.us-east-1.amazonaws.com",
    repository = "base/amazonlinux2-python",
    # changing this alone won't do anything, you need to change the digest as well
    tag = "v3.11-************-511398f",
)

# We use ECR pull through cache to pull the mongodb/mongodb-enterprise-server image from dockerhub and store it in ECR.
# https://docs.aws.amazon.com/AmazonECR/latest/userguide/pull-through-cache.html
# please use crane to get the digests, and _do not_ target a tag that gets overwritten. timestamped tags only.
container_pull(
    name = "mongodb-enterprise-server-amd64",
    architecture = "amd64",
    digest = "sha256:8b48bbf6594989ca012efd65fc4126e9b743064cca27ac9076735bd25d0dfb32",
    os = "linux",
    registry = "************.dkr.ecr.us-east-1.amazonaws.com",
    repository = "thirdparty/docker.io/mongodb/mongodb-enterprise-server",
    tag = "8.0.8-ubuntu2204-20250807T075231Z",
)

container_pull(
    name = "mongodb-enterprise-server-arm64",
    architecture = "arm64",
    digest = "sha256:b79c26cc9e91a1f34fbbe086dec71f26613ce81b01b0959699f5b23e255b9846",
    os = "linux",
    registry = "************.dkr.ecr.us-east-1.amazonaws.com",
    repository = "thirdparty/docker.io/mongodb/mongodb-enterprise-server",
    tag = "8.0.8-ubuntu2204-20250807T075231Z",
)

# mongodb/mongodb-atlas-search is an SRE-created ECR. The contents of it are automatically built
# and placed standalone mongot images. This base image is originally built in the mongot repo at
# https://github.com/10gen/mongot/blob/42e550cc9ee004f63d5b3cbeca0be60b97c71318/docker/BUILD#L207-L224
container_pull(
    name = "mongodb-atlas-search-amd64",
    architecture = "amd64",
    os = "linux",
    registry = "************.dkr.ecr.us-east-1.amazonaws.com",
    repository = "mongodb/mongodb-atlas-search",
    tag = "1.53.0-amd64",
)

# mongodb/mongodb-atlas-search for arm64 architecture (for development on M1 macs)
container_pull(
    name = "mongodb-atlas-search-arm64",
    architecture = "arm64",
    os = "linux",
    registry = "************.dkr.ecr.us-east-1.amazonaws.com",
    repository = "mongodb/mongodb-atlas-search",
    tag = "1.53.0-arm64",
)

# Base image used to build NodeJS images for arm64 architecture (for development on M1 macs).
container_pull(
    name = "amazonlinux2023-arm64",
    architecture = "arm64",
    os = "linux",
    registry = "************.dkr.ecr.us-east-1.amazonaws.com",
    repository = "base/amazonlinux2023",
    tag = "202508242213-511398f",
)

# Base image used to build NodeJS images for amd64 architecture.
# Check the latest available images at: https://github.com/xgen-cloud/docker-base-images/releases
container_pull(
    name = "amazonlinux2023-amd64",
    architecture = "amd64",
    os = "linux",
    registry = "************.dkr.ecr.us-east-1.amazonaws.com",
    repository = "base/amazonlinux2023",
    tag = "202508242213-511398f",
)

# Depends on bazel_gazelle and io_bazel_rules_go
load("@rules_python_gazelle_plugin//:deps.bzl", py_gazelle_deps = "gazelle_deps")

py_gazelle_deps()

# To add/update/remove a library
# please follow https://wiki.corp.mongodb.com/display/MMS/How+to+build+a+Python+application+using+Bazel#HowtobuildaPythonapplicationusingBazel-UpdatingDependencies
pip_parse(
    name = "pypi",
    enable_implicit_namespace_pkgs = True,
    python_interpreter_target = "@python3_host//:python",
    requirements_darwin = "//scripts/bazel_python:requirements_darwin.txt",
    requirements_linux = "//scripts/bazel_python:requirements_linux.txt",
    requirements_lock = "//scripts/bazel_python:requirements.txt",
)

load("@pypi//:requirements.bzl", install_python_deps = "install_deps")

install_python_deps()

# 10gen/mothra
new_git_repository(
    name = "10gen_mothra",
    build_file_content = """
filegroup(
    name = "teams",
    srcs = glob(["mothra/teams/*.yaml"]),
    visibility = ["//visibility:public"],
)
""",
    commit = "0da3ba6e8b5b2c72bf26860676fe22580e73df9c",
    remote = "**************:10gen/mothra.git",
)

# jira-oauth
new_git_repository(
    name = "iteng_jira_oauth",
    build_file_content = """
load("@rules_python//python:defs.bzl", "py_binary")
load("@jira_oauth_deps//:requirements.bzl", "requirement")

filegroup(
    name = "keys",
    srcs = ["key.pem", "key.pub"]
)

py_binary(
    name = "jira_token_gen",
    main = "jira-token-gen.py",
    srcs = ["jira-token-gen.py"],
    deps = [
        requirement("pyjwt"),
        requirement("requests-oauthlib"),
        requirement("cryptography"),
    ],
    data = [":keys"]
)
""",
    commit = "f2d366112ae5c34e3634db949cca815c64f43649",
    patches = ["//scripts:iteng_jira_oauth_bazel.diff"],
    remote = "**************:10gen/iteng-jira-oauth.git",
)

# How to add a new python repository: https://wiki.corp.mongodb.com/display/MMS/Adding+remote+Python+repository+to+MMS
pip_parse(
    name = "jira_oauth_deps",
    python_interpreter_target = "@python3_host//:python",
    requirements_darwin = "//scripts/bazel_python:iteng_requirements_darwin.txt",
    requirements_linux = "//scripts/bazel_python:iteng_requirements_linux.txt",
)

load("@jira_oauth_deps//:requirements.bzl", iteng_deps = "install_deps")

iteng_deps()

load("//server/mms_packaging:repos.bzl", "mms_packaging_repos")

mms_packaging_repos()

load("@aspect_rules_format//format:toolchains.bzl", "format_register_toolchains")

format_register_toolchains()

http_file(
    name = "evergreen_openapi_spec",
    downloaded_file_path = "evergreen_openapi_spec.json",
    urls = ["https://mciuploads.s3.amazonaws.com/evergreen/latest/swagger.json"],
)

# Poetry rules for managing Python dependencies
http_archive(
    name = "rules_poetry",
    sha256 = "533a0178767be4d79a67ae43890970485217f031adf090ef28c5c18e8fd337d8",
    strip_prefix = "rules_poetry-092d43107d13e711ac4ac92050d8b570bcc8ef43",
    urls = [
        # Implements retry by relisting each url multiple times to be used as a failover.
        "https://github.com/mongodb-forks/rules_poetry/archive/092d43107d13e711ac4ac92050d8b570bcc8ef43.tar.gz",
        "https://github.com/mongodb-forks/rules_poetry/archive/092d43107d13e711ac4ac92050d8b570bcc8ef43.tar.gz",
        "https://github.com/mongodb-forks/rules_poetry/archive/092d43107d13e711ac4ac92050d8b570bcc8ef43.tar.gz",
        "https://github.com/mongodb-forks/rules_poetry/archive/092d43107d13e711ac4ac92050d8b570bcc8ef43.tar.gz",
        "https://github.com/mongodb-forks/rules_poetry/archive/092d43107d13e711ac4ac92050d8b570bcc8ef43.tar.gz",
    ],
)

load("@rules_poetry//rules_poetry:poetry.bzl", "poetry")

http_archive(
    name = "bazel_rules_mongo",
    repo_mapping = {"@poetry": "@poetry_bazel_rules_mongo"},
    sha256 = "929f8f1a944f6cb159840995350cee3b96490b13ca4cddc9074265f95b61cebc",
    strip_prefix = "bazel_rules_mongo",
    urls = [
        # Implements retry by relisting each url multiple times to be used as a failover.
        "https://mdb-build-public.s3.amazonaws.com/bazel_rules_mongo/0.1.12/bazel_rules_mongo.tar.gz",
        "https://mdb-build-public.s3.amazonaws.com/bazel_rules_mongo/0.1.12/bazel_rules_mongo.tar.gz",
        "https://mdb-build-public.s3.amazonaws.com/bazel_rules_mongo/0.1.12/bazel_rules_mongo.tar.gz",
    ],
)

load("@bazel_rules_mongo//codeowners:codeowners_validator.bzl", "codeowners_validator")

codeowners_validator()

load("@bazel_rules_mongo//codeowners:codeowners_binary.bzl", "codeowners_binary")

codeowners_binary()

poetry(
    name = "poetry_bazel_rules_mongo",
    lockfile = "@bazel_rules_mongo//:poetry.lock",
    pyproject = "@bazel_rules_mongo//:pyproject.toml",
    python_interpreter_target_default = "@python3_host//:python",
    python_interpreter_target_mac = "@python3_host//:python",
)
