# Evergreen configuration for MongoDB Cloud Manager and Atlas
# The software_versions variables must appear in the first 100 lines of the file to be accessible from ant
stepback: true
oom_tracker: true

include:
  - filename: evergreen/apicontract_e2e.yml
  - filename: evergreen/atlas_e2e.yml
  - filename: evergreen/atlas_search_e2e.yml
  - filename: evergreen/atlas_search_schema_validation.yml
  - filename: evergreen/bazel_e2e.yml
  - filename: evergreen/bazel_health.yml
  - filename: evergreen/bazel_junit.yml
  - filename: evergreen/bazel_python.yml
  - filename: evergreen/bazel.yml
  - filename: evergreen/brs_e2e.yml
  - filename: evergreen/brs_third_party_e2e.yml
  - filename: evergreen/brs_config_shard_e2e.yml
  - filename: evergreen/brs_nightly_e2e.yml
  - filename: evergreen/brs_snapshot_validation_e2e.yml
  - filename: evergreen/cloud_prod_deploy_gate_common.yml
  - filename: evergreen/cloud_prod_deploy_gate_tests.yml
  - filename: evergreen/config_service.yml
  - filename: evergreen/e2e_setup.yml
  - filename: evergreen/evergreen_api.yml
  - filename: evergreen/git_repo.yml
  - filename: evergreen/iam_e2e.yml
  - filename: evergreen/mongodb.yml
  - filename: evergreen/onboarding.yml
  - filename: evergreen/openapi_e2e.yaml
  - filename: evergreen/openapi_evergreen_environment.yaml
  - filename: evergreen/openapi_standalone_services.yaml
  - filename: evergreen/packaging.yml
  - filename: evergreen/post_functions.yml
  - filename: evergreen/pre_functions.yml
  - filename: evergreen/sbom.yml
  - filename: evergreen/search_playground.yml
  - filename: evergreen/sign_artifacts.yml
  - filename: evergreen/standalone-service.yml
  - filename: evergreen/pine_markdown.yml
  - filename: evergreen/java_grpc_proto_plugin.yml

# Some parameters, tasks, functions, and variants have moved to the separate files named above
parameters:
  - key: unit_js_task_generator_targets
    value: ""
    description: Space-delimited list of UNIT_JS tasks we want to generate. If empty string, will generate all tasks.
  - key: int_js_task_generator_targets
    value: ""
    description: Space-delimited list of INT_JS tasks we want to generate. If empty string, will generate all tasks.
  - key: e2e_cypress_task_generator_targets
    value: ""
    description: Space-delimited list of E2E_CYPRESS* tasks we want to generate. If empty string, will generate all tasks.
  - key: should_run_e2e_cypress_mdb_latest
    value: "false"
    description: Can be true or false. If true, runs the e2e_cypress_mdb_latest variant on a patch. False by default.
  - key: should_run_e2e_cypress_new_nav
    value: "false"
    description: Can be true or false. If true, runs the e2e_cypress_new_nav variant on a patch (if running the cypress generator) or otherwise updates conf-local to enable cloudnav. False by default.
  - key: should_use_cloud_nav_e2e
    value: "false"
    description: If true, updates conf-local to enable cloudnav (for selenium e2e tests only). False by default.

  # These are for the Clusters E2E tests
  - key: e2e_clusters_variant_targets
    value: ""
    description: Space-delimited list of e2e_nds_* variants we want to generate. If empty string, will generate all variants.
  - key: e2e_clusters_task_targets
    value: ""
    description: Space-delimited list of E2E_NDS_* tasks we want to generate. If empty string, will generate all tasks.
  - key: e2e_clusters_mongodb_major_version
    value: ""
    description: Space-delimited list of MongoDB major versions we want to generate. If empty string, will use default version.
  - key: e2e_clusters_cloud_providers
    value: ""
    description: Space-delimited list of cloud providers to run tests for. If empty string, will target all providers.
  - key: e2e_clusters_remote_env
    value: "local"
    description: Either "local", "dev", or "qa" are allowed options. Default is "local".
  - key: e2e_clusters_os_release_tasks_only
    value: "false"
    description: If true, only tasks labeled as os_release will be generated / run. False by default.
  - key: e2e_clusters_version_release_tasks_only
    value: "false"
    description: If true, only tasks labeled as version_release will be generated / run. False by default.
  - key: e2e_clusters_critical_version_release_tasks_only
    value: "false"
    description:  If true, only tasks labeled as critical will be generated / run. False by default.
  - key: e2e_clusters_generate_disabled_tasks
    value: "false"
    description: If true, all tasks are generated but disabled. False by default.
  - key: chromedriver_version
    value: "111.0.5563.64"

  # These are just for TESTING automated_code_changes
  - key: mms_build_account_user
    value: "help"  # this would never be allowed as a GitHub username because it redirects to the help site
  - key: mms_build_account_email
    value: "<EMAIL>"
  - key: mms_build_account_gpg_key_id
    value: "DCAEEE7815FFF4C7"  # fake key stored in github_gpg_key_2 of the mms Evergreen project settings
  - key: authn_e2e_tests
    value: ""
    description: comma-delimited list of AuthN e2e test names to run, defaults to none
variables:
  # bazel installation anchors - used to configure which bazel version to install
  - &install_bazel_linux_x86_64_amd64
    platform: linux
    arch: x86_64
    mms_build_arch: amd64
  # e2e remote anchors - used to configure which remote hosts to launch
  - &amazon_linux2
    e2e_remote_user: ec2-user
    e2e_remote_distro: amazon2-large
    artifact: x86_64-rpm
  - &amazon_linux2_arm
    e2e_remote_user: ec2-user
    e2e_remote_distro: amazon2-arm64-large
    artifact: x86_64-rpm
  - &amazon_2023
    e2e_remote_user: ec2-user
    e2e_remote_distro: amazon2023.0-large
    artifact: x86_64-rpm
  - &amazon_2023_arm
    e2e_remote_user: ec2-user
    e2e_remote_distro: amazon2023.2-arm64-large
    artifact: x86_64-rpm
  - &debian_10
    e2e_remote_user: admin
    e2e_remote_distro: debian10-cloud-large
    artifact: x86_64-deb
  - &debian_11
    e2e_remote_user: admin
    e2e_remote_distro: debian11-large
    artifact: x86_64-deb
  - &debian_12
    e2e_remote_user: admin
    e2e_remote_distro: debian12-large
    artifact: x86_64-deb
  - &rhel_76
    e2e_remote_user: root
    e2e_remote_distro: rhel76-cloud-large
    artifact: x86_64-rpm
  - &rhel_88
    e2e_remote_user: ec2-user
    e2e_remote_distro: rhel8.8-large
    artifact: x86_64-rpm
  - &rhel_93
    e2e_remote_user: ec2-user
    e2e_remote_distro: rhel93-large
    artifact: x86_64-rpm
  - &rhel_88_arm
    e2e_remote_user: ec2-user
    e2e_remote_distro: rhel8.8-arm64-large
    artifact: x86_64-rpm
  - &rhel_93_arm
    e2e_remote_user: ec2-user
    e2e_remote_distro: rhel93-arm64-large
    artifact: x86_64-rpm
  - &suse_12
    e2e_remote_user: ec2-user
    e2e_remote_distro: suse12-sp5-large
    artifact: x86_64-rpm
  - &suse_15
    e2e_remote_user: ec2-user
    e2e_remote_distro: suse15-large
    artifact: x86_64-rpm
  - &suse_15_sp15
    e2e_remote_user: ec2-user
    e2e_remote_distro: suse15sp5-large
    artifact: x86_64-rpm
  - &ubuntu_1804
    e2e_remote_user: ubuntu
    e2e_remote_distro: ubuntu1804-large
    artifact: x86_64-deb
  - &ubuntu_2004
    e2e_remote_user: ubuntu
    e2e_remote_distro: ubuntu2004-large
    artifact: x86_64-deb
  - &ubuntu_2204
    e2e_remote_user: ubuntu
    e2e_remote_distro: ubuntu2204-large
    artifact: x86_64-deb
  - &ubuntu_2404
    e2e_remote_user: ubuntu
    e2e_remote_distro: ubuntu2404-large
    artifact: x86_64-deb
  - &ubuntu_2004_arm
    e2e_remote_user: ubuntu
    e2e_remote_distro: ubuntu2004-arm64-large
    artifact: aarch64-deb
  - &ubuntu_2204_arm
    e2e_remote_user: ubuntu
    e2e_remote_distro: ubuntu2204-arm64-large
    artifact: aarch64-deb
  - &ubuntu_2404_arm
    e2e_remote_user: ubuntu
    e2e_remote_distro: ubuntu2404-arm64-large
    artifact: aarch64-deb
  - &windows_2016_dc
    e2e_remote_user: Administrator
    e2e_remote_distro: windows-2016-dc
    artifact: x86_64-msi
  - &windows_large
    e2e_remote_user: Administrator
    e2e_remote_distro: windows-vsCurrent-large
    artifact: x86_64-msi
  - &windows_small
    e2e_remote_user: Administrator
    e2e_remote_distro: windows-vsCurrent-small
    artifact: x86_64-msi
  - &openapi
    v1_0: "v1.0"
    v1_5: "v1.5"
  - &compile_bazel_env_expansions
    - use_engflow_cache
    - engflow_rollout_percent
    - workdir
    - platform
    - arch
    - DEVELOPER_DIR
    - BAZELISK_HOME

exec_timeout_secs: 21600 # tasks will time out after 6 hours
pre_error_fails_task: true

functions:

  "add metrics properties":
      command: shell.exec
      params:
        working_dir: mms/server
        shell: bash
        script: |
          set -ex
          echo "mongo.mmsdbrrd4ia.mongoUri=mongodb://127.0.0.1:28500/?maxPoolSize=100&retryWrites=false&retryReads=false&uuidRepresentation=standard" >> "${workdir}/mms/server/conf/conf-test.properties"
          echo "mongo.mmsdbrrd4dbia.mongoUri=mongodb://127.0.0.1:28500/?maxPoolSize=100&retryWrites=false&retryReads=false&uuidRepresentation=standard" >> "${workdir}/mms/server/conf/conf-test.properties"

  "fetch nodejs": &fetch_nodejs
      command: subprocess.exec
      type: setup
      failure_metadata_tags: ["assigned_to_jira_team_cloudp_frontend_platform"]
      params:
        env:
          DISTRO: ${distro_id}
        working_dir: mms
        shell: bash
        binary: ./scripts/evergreen/fetch_nodejs.sh

  "install mongodb on custom directory": &install_mongodb_custom
    command: subprocess.exec
    type: setup
    failure_metadata_tags: ["assigned_to_jira_team_devprod_correctness"]
    params:
      working_dir: mms
      binary: ./scripts/evergreen/install_mongodb_custom.sh
      include_expansions_in_env: [ mongodb_version_custom, mongodb_arch_custom, mongodb_custom_dir ]

  "wait for successful e2e resource lock db connection":
      command: subprocess.exec
      params:
        include_expansions_in_env: [ e2e_resource_locks_db_conn_string, workdir ]
        working_dir: mms
        binary: bash
        args: ["./scripts/cluster_connection_test/e2e_resource_lock_db_connection_test.sh"]

  "fetch sls ecr tag": &fetch_sls_ecr_tag
    command: shell.exec
    type: setup
    params:
      shell: bash
      working_dir: mms
      background: false
      env:
        AWS_ACCESS_KEY_ID: ${e2e_aws_key}
        AWS_SECRET_ACCESS_KEY: ${e2e_aws_secret}
      script: |
        set -ex

        if [[ "${e2e_local_sls_ecr_tag}" == "main" ]]; then
          aws sts get-caller-identity
          aws s3 cp s3://sls-versions/slsctl/main - > "${workdir}/.sls-latest-ecr-tag"
        else
          echo "${e2e_local_sls_ecr_tag}" > "${workdir}/.sls-latest-ecr-tag"
        fi

  "start local sls with slsctl": &start_local_sls_with_slsctl
    command: shell.exec
    type: setup
    params:
      shell: bash
      working_dir: mms
      env:
        AWS_ACCESS_KEY_ID: ${e2e_aws_key}
        AWS_SECRET_ACCESS_KEY: ${e2e_aws_secret}
      background: false
      script: |
        set -ex

        export SLSCTL_AWS_DIR="${workdir}/.slsctl-aws"
        mkdir -p "$SLSCTL_AWS_DIR"
        echo "[profile mms-scratch]
        region = us-east-1" > "$SLSCTL_AWS_DIR/config"
        echo "[mms-scratch]
        aws_access_key_id = $AWS_ACCESS_KEY_ID
        aws_secret_access_key = $AWS_SECRET_ACCESS_KEY" > "$SLSCTL_AWS_DIR/credentials"

        export SLSCTL_DIR="${workdir}/.slsctl"
        mkdir -p "$SLSCTL_DIR"
        set +x
        TOKEN=$(aws ecr get-authorization-token --region us-east-1 --output text --query 'authorizationData[0].authorizationToken')
        echo "$TOKEN" | base64 --decode | cut -d: -f2 > "$SLSCTL_DIR/docker"
        set -x

        export PATH="${workdir}/bin:${workdir}/mms:$PATH"
        sudo amazon-linux-extras enable docker
        sudo yum install -y amazon-ecr-credential-helper jq

        export DOCKER_CONFIG="${workdir}/.docker/slsctl"
        mkdir -p "$DOCKER_CONFIG"
        rm -f "$DOCKER_CONFIG/config.json"
        cat "$SLSCTL_DIR/docker" | docker login --username "AWS" --password-stdin 664315256653.dkr.ecr.us-east-1.amazonaws.com
        docker info

        export SLS_ECR_TAG=$(cat "${workdir}/.sls-latest-ecr-tag")
        export LOG_FILE_PREFIX=$(readlink -f "server/sls-services-")

        ./server/scripts/sls/slsctl-start

  "install pint": &install_pint
    command: subprocess.exec
    params:
      binary: bash
      args: [ "${workdir}/mms/scripts/evergreen/install_pint.sh",
              "-w", "${workdir}", "-a", "${mms_build_arch}", "-p", "${platform}", "-v", "0.70.0" ]

  "install prometheus": &install_prometheus
      command: subprocess.exec
      params:
        binary: bash
        args: [ "${workdir}/mms/scripts/evergreen/install_prometheus.sh",
                "-w", "${workdir}", "-a", "${mms_build_arch}", "-p", "${platform}", "-v", "2.53.2" ]

  "configure and run prometheus": &configure_and_run_prometheus
    - *install_prometheus
    - command: subprocess.exec
      params:
        background: true
        working_dir: mms/scripts/evergreen
        binary: bash
        args: ["${workdir}/mms/scripts/evergreen/configure_and_run_prometheus.sh", "${workdir}/mms/scripts/evergreen", "10"]
#      TODO: CLOUDP-97511 replace '10' with an evergreen expansion

  "save prometheus data files": &save_prometheus_data_files
    - command: archive.targz_pack
      params:
        target: "prometheus_data.tgz"
        source_dir: "${workdir}/mms/scripts/evergreen"
        include:
          - "data/**"
          - "prometheus.yml"
    - command: s3.put
      params:
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
        local_file: "prometheus_data.tgz"
        remote_file: "${mciuploads_dir|mms}/${task_id}/${bazel_test_target}/prometheus_data.tgz"
        bucket: mciuploads
        permissions: private
        visibility: signed
        content_type: application/x-tar
        display_name: "nds_load_test_prometheus_data.tar"

  "setup global-test-secure for standalone services": &setup_global_secure_for_standalone_services
    command: subprocess.exec
    type: setup
    failure_metadata_tags: ["assigned_to_jira_team_devprod_correctness"]
    params:
      binary: "${workdir}/mms/scripts/evergreen/standalone_services/setup_systems_test_config.sh"
      env:
        WORK_DIR: ${workdir}
        AWS_ACCESS_KEY: ${e2e_aws_key}
        AWS_SECRET_KEY: ${e2e_aws_secret}
        SERVICE_ENV: ${environment}

  "stop backup daemon":
      command: shell.exec
      type: setup
      failure_metadata_tags: ["assigned_to_jira_team_cloudp_backup_private_cloud"]
      params:
        working_dir: mms/server
        shell: bash
        script: |
          set -evx
          echo "stopping backup daemon..."
          if [ "${arch}" = "" ]; then
            pkill -f "daemon.hosted"
          else
            pkill -f "daemon.runfiles"
          fi

  "build custom docker image for required services": &build_custom_docker_image_for_required_services
    command: shell.exec
    type: setup
    timeout_secs: 300
    failure_metadata_tags: ["assigned_to_jira_team_cloudp_backup_private_cloud"]
    params:
      working_dir: mms
      shell: bash
      include_expansions_in_env: [AWS_SECRET_ACCESS_KEY, AWS_ACCESS_KEY_ID, AWS_SESSION_TOKEN]
      env:
        DOCKER_CONFIG: ${workdir}/mms/.docker/mms
      script: |
        set -ex
        # add aws "docker-credential-ecr-login" to PATH so Docker can use it to resolve creds while pulling image from ECR
        export PATH="${workdir}/bin:$PATH"
        PUBLIC_HOSTNAME=$(curl --silent --show-error --fail http://***************/latest/meta-data/public-hostname)
        echo "Replacing mongo uri and loopback address in authn & config service yaml files"
        sed -i "s/127\.0\.0\.1:27017/$PUBLIC_HOSTNAME:9443/" "./server/src/main/com/xgen/cloud/services/authn/resource/config/authn-service-local.yaml"
        sed -i "s/127\.0\.0\.1:27017/$PUBLIC_HOSTNAME:9443/" "./server/src/main/com/xgen/cloud/services/config/resource/config/config-service-local.yaml"
        sed -i "s/127\.0\.0\.1:27017/$PUBLIC_HOSTNAME:9443/" "./server/src/main/com/xgen/cloud/services/resource/config/service-local.yaml"
        # Docker container servers cannot listen on localhost for requests coming from outside the container.
        sed -i "s/address: 'localhost:30053'/address: '0.0.0.0:30053'/g" "./server/src/main/com/xgen/cloud/services/authn/resource/config/authn-service-local.yaml"
        sed -i "s/address: 'localhost:50071'/address: '0.0.0.0:50071'/g" "./server/src/main/com/xgen/cloud/services/config/resource/config/config-service.yaml"
        echo "Build docker images"
        bazel build //server/src/main/com/xgen/cloud/services/authn/binary:docker-image.tar  --@io_bazel_rules_docker//transitions:enable=false
        bazel build //server/src/main/com/xgen/cloud/services/config/binary:docker-image.tar  --@io_bazel_rules_docker//transitions:enable=false

  "upload versioned openapi yaml files to s3": &upload_versioned_openapi_yaml_files_to_s3
    command: s3.put
    params:
      aws_key: ${mciuploads_aws_key}
      aws_secret: ${mciuploads_aws_secret}
      local_files_include_filter:
        - mms/server/openapi/atlas/*.yaml
      remote_file: ${mciuploads_dir|mms}/${revision}/${created_at}/
      bucket: mciuploads
      permissions: public-read
      content_type: application/yaml

  "upload versioned openapi json files to s3": &upload_versioned_openapi_json_files_to_s3
    command: s3.put
    params:
      aws_key: ${mciuploads_aws_key}
      aws_secret: ${mciuploads_aws_secret}
      local_files_include_filter:
        - mms/server/openapi/atlas/*.json
      remote_file: ${mciuploads_dir|mms}/${revision}/${created_at}/
      bucket: mciuploads
      permissions: public-read
      content_type: application/json

  "lint versioned openapi files": &lint_versioned_openapi_files
    command: subprocess.exec
    params:
      working_dir: mms
      command: "bazel ${bazel_command|run} ${bazel_args} //scripts/evergreen/openapi:spectral_lint"
      add_expansions_to_env: true

  "generate openapi branded preview": &generate_openapi_branded_preview
    command: subprocess.exec
    timeout_secs: 300
    params:
      working_dir: mms
      include_expansions_in_env: [revision, created_at]
      binary: ./scripts/evergreen/openapi/branded_preview.sh

  "override timeout to 4 hours":
    - command: shell.exec
      params:
        working_dir: mms
        script: |
          set -eux
          {
            echo 'test --test_timeout=14400'
          } >> "${workdir}/.bazelrc"

  "early exit if not mms project": &early_exit_if_not_mms_project
    command: shell.exec
    type: setup
    failure_metadata_tags: ["assigned_to_jira_team_devprod_release_infrastructure"]
    params:
      shell: bash
      script: |
        set -eu
        source "mms/scripts/evergreen/task.bash"
        # check current project, fail if not mms
        echo "checking project"
        echo "${project}"
        if [ "${project}" != "mms" ]; then
          echo "exiting due to this not being mms (master) (project: ${project})"
          post_evergreen_status success test "exiting due to this not being mms (master)" false
        fi

  "report success and exit": &report_success_and_exit
    command: shell.exec
    type: setup
    failure_metadata_tags: [ "assigned_to_jira_team_devprod_release_infrastructure" ]
    params:
      shell: bash
      script: |
        set -eu
        source "mms/scripts/evergreen/task.bash"
        post_evergreen_status success test "reporting success and exiting" false

  "create remote e2e hosts":
      - command: shell.exec
        type: setup
        failure_metadata_tags: ["assigned_to_jira_team_devprod_correctness"]
        params:
          shell: bash
          silent: true
          script: |
            set -e
            keyfile="${workdir}/${e2e_remote_ssh_identity}"
            echo '${__project_aws_ssh_key_value}' > "$keyfile"
            chmod 600 "$keyfile"
      - command: subprocess.exec
        type: setup
        failure_metadata_tags: ["assigned_to_jira_team_devprod_correctness"]
        params:
          binary: ${workdir}/mms/scripts/evergreen/verify_ssh_keys.sh
          args: ['${workdir}/${e2e_remote_ssh_identity}']
      - command: host.create
        type: system
        retry_on_failure: true
        failure_metadata_tags: ["assigned_to_jira_team_devprod_correctness"]
        params:
          provider: ec2
          distro: ${e2e_remote_distro}
          num_hosts: ${e2e_remote_num_hosts}
          security_group_ids: [sg-097bff6dd0d1d31d0] # Allows hosts to talk to each other for MongoDB replication
      - command: host.list
        type: system
        retry_on_failure: true
        failure_metadata_tags: ["assigned_to_jira_team_devprod_correctness"]
        params:
          wait: true
          timeout_seconds: 1800
          num_hosts: ${e2e_remote_num_hosts}
          path: ${e2e_remote_hosts}
          silent: true
      - command: subprocess.exec
        type: system
        retry_on_failure: true
        failure_metadata_tags: ["assigned_to_jira_team_devprod_correctness"]
        params:
          binary: ${workdir}/mms/scripts/evergreen/test_multiserver_comms.sh
          args: ['-u', '${e2e_remote_user}', '-i', '${workdir}/${e2e_remote_ssh_identity}', '-h', '${workdir}/${e2e_remote_hosts}']
      - command: subprocess.exec
        type: setup
        failure_metadata_tags: ["assigned_to_jira_team_cloudp_backup_private_cloud"]
        params:
          binary: ${workdir}/mms/scripts/evergreen/ensure_data_dir.sh
          args: ['-u', '${e2e_remote_user}', '-i', '${workdir}/${e2e_remote_ssh_identity}', '-h', '${workdir}/${e2e_remote_hosts}']
      - command: subprocess.exec
        type: setup
        failure_metadata_tags: ["assigned_to_jira_team_cloudp_backup_private_cloud"]
        params:
          binary: ${workdir}/mms/scripts/evergreen/mount_large_data_partition.sh
          args: [ '-u', '${e2e_remote_user}', '-i', '${workdir}/${e2e_remote_ssh_identity}', '-h', '${workdir}/${e2e_remote_hosts}', '-d', '${e2e_remote_distro}' ]
      - command: subprocess.exec
        type: setup
        failure_metadata_tags: ["assigned_to_jira_team_cloudp_backup_private_cloud"]
        params:
          binary: ${workdir}/mms/scripts/evergreen/enable_outdated_rsa_for_testing.sh
          args: [ '-u', '${e2e_remote_user}', '-i', '${workdir}/${e2e_remote_ssh_identity}', '-h', '${workdir}/${e2e_remote_hosts}', '-d', '${e2e_remote_distro}' ]

  "save spawned hosts": &save_spawned_hosts
    command: shell.exec
    type: setup
    failure_metadata_tags: ["assigned_to_jira_team_devprod_correctness"]
    params:
      shell: bash
      script: |
        set -ex
        # Store spawned host URL(s) for later consumption
        echo "Working dir: ${workdir}"
        cat << EOF | python - "${workdir}/${e2e_remote_hosts}" > ${workdir}/spawned_hosts_env
        import sys
        import json
        with open(sys.argv[1]) as hostsfile:
            hosts = json.load(hostsfile)
            for host in hosts:
                print(host["dns_name"])
        EOF

  "run python script":
    - command: shell.exec
      params:
        working_dir: mms/scripts
        include_expansions_in_env: [BAZELISK_HOME]
        shell: bash
        script: |
          set -eux
          ./init_virtualenv.sh '${workdir}'
          VIRTUAL_ENV_DISABLE_PROMPT=true source ./venv-activate
          export workdir="${workdir}"
          ${script}

  "setup conf-test-secure for int":
    command: shell.exec
    type: setup
    failure_metadata_tags: ["assigned_to_jira_team_devprod_correctness"]
    params:
      shell: bash
      working_dir: mms/server
      silent: true
      script: |
        set -eu

        declare CONF_FILE="conf/conf-test-secure.properties"

        if [[ "${build_variant}" =~ ^int_external ]]
        then
          declare AWS_SECRET="${int_external_aws_secret_key}" AWS_KEY="${int_external_aws_access_key}"

          declare CENTRAL_URL="http://$(curl --silent --show-error --fail http://***************/latest/meta-data/public-hostname):9080"
          echo "mms.centralUrl=$CENTRAL_URL" >> "$CONF_FILE"
          echo "mms.agentCentralUrl=$CENTRAL_URL" >> "$CONF_FILE"
          echo "mms.backupCentralUrl=$CENTRAL_URL" >> "$CONF_FILE"
        else
          declare AWS_SECRET="${e2e_aws_secret}" AWS_KEY="${e2e_aws_key}"
        fi

        echo "local.aws.accessKey=$AWS_KEY" >> "$CONF_FILE"
        echo "local.aws.secretKey=$AWS_SECRET" >> "$CONF_FILE"

  "start atlas gateway": &start_atlas_gateway
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        script: |
          set -ex
          echo "Copying Atlas Gateway config file to working directory..."
          cp server/conf/atlasgateway/atlas-gateway-e2e.yml ./atlas-gateway-e2e.yml
          echo "Config file copied. Contents:"
          cat ./atlas-gateway-e2e.yml
    - command: shell.exec
      type: setup
      failure_metadata_tags: ["assigned_to_jira_team_devprod_correctness"]
      params:
        shell: bash
        script: |
          set -exu

          cred_helper="${workdir}/bin/docker-credential-ecr-login"

          if [ "${platform}" = "macos" ]; then
            effective_platform='darwin'
          else
            effective_platform="${platform}"
          fi

          curl --silent --show-error --fail --location --retry 10 \
            --output "$cred_helper" \
            "https://amazon-ecr-credential-helper-releases.s3.us-east-2.amazonaws.com/${amazon_ecr_credential_helper_version}/$effective_platform-${mms_build_arch}/docker-credential-ecr-login"

          chmod +x "$cred_helper"
          echo "build --@io_bazel_rules_docker//transitions:enable=false" >> "${workdir}/.bazelrc"
    - command: ec2.assume_role
      params:
        role_arn: ${ecr_readonly_iam_role_arn}
    - command: shell.exec
      type: setup
      failure_metadata_tags: ["assigned_to_jira_team_cloudp_atlas_cluster_security_i"]
      params:
        working_dir: mms
        shell: bash
        include_expansions_in_env: [AWS_SECRET_ACCESS_KEY, AWS_ACCESS_KEY_ID, AWS_SESSION_TOKEN]
        env:
          DOCKER_CONFIG: ${workdir}/mms/.docker/mms
        script: |
          set -euo pipefail

          chmod +x scripts/evergreen/atlasgateway/start_atlas_gateway.sh
          ./scripts/evergreen/atlasgateway/start_atlas_gateway.sh

  "upload atlas gateway log": &upload_atlas_gateway_log
    command: s3.put
    params:
      aws_key: ${mciuploads_aws_key}
      aws_secret: ${mciuploads_aws_secret}
      local_file: "mms/atlas-gateway.log"
      remote_file: "${mciuploads_dir|mms}/${task_id}/${execution}/atlas-gateway.log"
      bucket: mciuploads
      permissions: private
      visibility: signed
      content_type: "text/plain"
      optional: true

  "upload data exfil prev haproxy log": &upload_data_exfil_prev_haproxy_log
    command: s3.put
    params:
      aws_key: ${mciuploads_aws_key}
      aws_secret: ${mciuploads_aws_secret}
      local_file: "mms/data-exfil-prev-haproxy.log"
      remote_file: "${mciuploads_dir|mms}/${task_id}/${execution}/data-exfil-prev-haproxy.log"
      bucket: mciuploads
      permissions: private
      visibility: signed
      content_type: "text/plain"
      optional: true

  "capture atlas gateway logs": &capture_atlas_gateway_logs
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        script: |
          # Capture Atlas Gateway container logs for debugging
          echo "Capturing Atlas Gateway container logs..."
          if docker ps -a --format '{{.Names}}' | grep -q '^atlas-gateway-e2e$'; then
            docker logs atlas-gateway-e2e > atlas-gateway.log 2>&1 || true
          else
            echo "No Atlas Gateway container present" > atlas-gateway.log
          fi

          # Capture data exfiltration prevention HAProxy container logs for debugging
          echo "Capturing data exfiltration prevention HAProxy container logs..."
          if docker ps -a --format '{{.Names}}' | grep -q '^data-exfil-prev-haproxy-e2e$'; then
            docker logs data-exfil-prev-haproxy-e2e > data-exfil-prev-haproxy.log 2>&1 || true
          else
            echo "No data exfiltration prevention HAProxy container present" > data-exfil-prev-haproxy.log
          fi
    - *upload_atlas_gateway_log
    - *upload_data_exfil_prev_haproxy_log

  "start localstack": &start_localstack
    command: shell.exec
    type: setup
    failure_metadata_tags: [ "assigned_to_jira_team_cloudp_alerts_platform" ]
    params:
      working_dir: mms/server
      shell: bash
      timeout_secs: 900
      include_expansions_in_env: [BAZELISK_HOME]
      script: |
        set -eux
        bazel run //scripts:start_localstack >> mms_localstack.log 2>&1 &

  "start local sls":
    - *fetch_sls_ecr_tag
    - *start_local_sls_with_slsctl

  "tail local sls":
    command: shell.exec
    type: setup
    params:
      working_dir: mms
      shell: bash
      background: true
      script: |
        set -ex

        export SLS_ECR_TAG=$(cat "${workdir}/.sls-latest-ecr-tag")
        export SLSCTL_AWS_DIR="${workdir}/.slsctl-aws"
        export SLSCTL_DIR="${workdir}/.slsctl"
        export DOCKER_CONFIG="${workdir}/.docker/slsctl"
        export LOG_FILE_PREFIX=$(readlink -f "server/sls")

        ./server/scripts/sls/slsctl-tail

  "run pnpm install": &run_pnpm_install
    command: subprocess.exec
    type: setup
    failure_metadata_tags: ["assigned_to_jira_team_cloudp_frontend_platform"]
    params:
      working_dir: mms
      add_to_path:
        - ${workdir}/mms/nodejs/bin
      include_expansions_in_env: [CYPRESS_CACHE_FOLDER]
      command: "pnpm install --frozen-lockfile"

  "generate versioned openapi spec":
    - *upload_versioned_openapi_yaml_files_to_s3
    - *upload_versioned_openapi_json_files_to_s3
    - *generate_openapi_branded_preview
    - *lint_versioned_openapi_files

  "fetch upstream agent build variables":
    - command: shell.exec
      params:
        script: |
          set -evx
          if [ "${upstream_agent_patch}" != "" ]  && [ "${upstream_agent_version}" != "" ]; then
            cd mms/server/conf
            sed -i -e "s/^automation\.agent\.version\=.*\$/automation.agent.version=${upstream_agent_version}/" conf-local.properties
            sed -i -e "s|^automation\.agent\.location\=.*\$|automation.agent.location=https://s3.amazonaws.com/mciuploads/mms-automation/mongodb-mms-build-agent/builds/patches/${upstream_agent_patch}/automation-agent/\$\{env\}/|" conf-local.properties
            grep automation.agent.version conf-local.properties
            grep automation.agent.location conf-local.properties
          fi

  # A very similar function also exists in .cloud_perf.yml. If making any changes here,
  # make sure to keep the other function in sync
  "register cloud provider observers":
    command: subprocess.exec
    type: setup
    failure_metadata_tags: ["assigned_to_jira_team_cloudp_atlas_clusters_platform_i"]
    params:
      working_dir: mms
      include_expansions_in_env: [ BAZELISK_HOME ]
      command: bazel run //server/src/features/com/xgen/svc/cukes/misc:TestCloudProviderResourceCleanupTool -- -c register -u http://localhost:9080

  # A very similar function also exists in .cloud_perf.yml. If making any changes here,
  # make sure to keep the other function in sync
  "clean from cloud provider observers":
    command: subprocess.exec
    type: setup
    failure_metadata_tags: ["assigned_to_jira_team_cloudp_atlas_clusters_platform_i"]
    params:
      working_dir: mms
      include_expansions_in_env: [ BAZELISK_HOME ]
      command: bazel run //server/src/features/com/xgen/svc/cukes/misc:TestCloudProviderResourceCleanupTool -- -c clean -u http://localhost:9080

  "setup cypress task generator":
    - command: shell.exec
      params:
        working_dir: mms
        env:
          VERIFY_ONLY: ${verify_only}
          # values can be patch, github_pr, github_tag, commit, trigger, commit_queue, or ad_hoc
          TASK_REQUESTER: ${requester}
          SHOULD_RUN_LATEST_OVERRIDE: ${should_run_e2e_cypress_mdb_latest}
          SHOULD_RUN_CLOUD_NAV: ${should_run_e2e_cypress_new_nav}
          E2E_CYPRESS_TASK_GENERATOR_TARGETS:  ${e2e_cypress_task_generator_targets}
          DEBUG: "find-cypress-specs,find-test-names"
          CYPRESS_CONFIG_FILE: "client/cypress.config.ts" # find-cypress-specs uses this to find cy config since its in client
        script: |
          bazel run //client/packages/scripts/evergreen-generators:cypress_e2e

  "checkout to specific branch":
    command: subprocess.exec
    params:
      working_dir: mms
      add_expansions_to_env: true
      binary: ./scripts/evergreen/checkout_to_specific_branch.sh

  "nds model verification test execution":
    command: subprocess.exec
    params:
      working_dir: mms
      add_expansions_to_env: true
      binary: ./scripts/evergreen/nds_model_verification_test_execution.sh

  "install target-determinator": &install_target_determinator
    command: subprocess.exec
    type: setup
    params:
      working_dir: mms
      binary: bash
      args: [ "${workdir}/mms/scripts/evergreen/install_target_determinator.sh",
              "-w", "${workdir}", "-a", "${mms_build_arch}", "-p", "${platform}", "-v", "0.30.3" ]

  "install redis tools": &install_redis_tools
    command: subprocess.exec
    type: setup
    params:
      binary: ${workdir}/mms/scripts/evergreen/install_redis_tools.sh
      args: [ "-w", "${workdir}", "-v", "7.2.7", "-i", ">> install-redis.log 2>&1 &" ]

  "start local vm cluster and redis": &start_local_vm_cluster_and_redis
    command: shell.exec
    params:
      working_dir: mms/server
      shell: bash
      timeout_secs: 900
      include_expansions_in_env: [ BAZELISK_HOME ]
      add_to_path:
        - ${workdir}/mms/bin
      script: |
        set -eux
        bazel run //scripts:compose_vm_and_redis_start >> vm-redis.log 2>&1 &

  "prepare for metrics service":
    - *install_redis_tools
    - *start_local_vm_cluster_and_redis

  "update placeholder": &update_placeholder
    command: shell.exec
    params:
      working_dir: mms
      include_expansions_in_env: [ BAZELISK_HOME ]
      env:
        MMS_HOME: ${workdir}/mms
      shell: bash
      script: |
          set -eux
          {
            echo "Change this file to invalidate cache"
            echo "Change this file to invalidate cache"
            echo "Change this file to invalidate cache"
            echo "Change this file to invalidate cache"
            echo "Change this file to invalidate cache"
          } >> scripts/build_perf/placeholder.txt

          cat scripts/build_perf/placeholder.txt

  "revert_placeholder": &revert_placeholder
    command: shell.exec
    params:
      working_dir: mms
      shell: bash
      script: |
        set -eux
        {
          echo "This is a placeholder"
          echo ""
        } > scripts/build_perf/placeholder.txt

tasks:
- name: WEBPACK_BUILD_STATS
  tags: ["run_webpack_stats", "assigned_to_jira_team_cloudp_frontend_platform"]
  patch_only: true
  commands:
    - command: shell.exec
      params:
        working_dir: mms
        shell: bash
        script: |
            set -ex
            # Generate webpack stats for the current branch
            ./scripts/evergreen/build_and_copy_webpack_stats.sh current-stats

            # Check out the base branch
            base_commit=$(git merge-base HEAD ${branch_name})
            git reset --soft $base_commit
            git stash

            # Generate webpack stats for the base branch
            ./scripts/evergreen/build_and_copy_webpack_stats.sh base-stats

            git stash pop


            # Compare the stats and output the result
            bazel run //client/packages/frontend-telemetry:compare_stats ${workdir}/mms/current-stats ${workdir}/mms/base-stats ${workdir}/mms/check_outputs.json
    - command: s3.put
      params:
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
        local_file: ${workdir}/mms/check_outputs.json
        display_name: GitHub webpack stats check run results
        remote_file: ${mciuploads_dir|mms}/${build_id}/${task_name}/${execution}/check_outputs.json
        bucket: mciuploads
        permissions: private
        visibility: signed
        content_type: application/json

- name: E2E_Local_Core_Orgs
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_iam_authorization"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-okta-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@e2eLocalCoreOrgs"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Core_Projects
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_iam_authorization"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-okta-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@e2eLocalCoreProjects"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Core_Teams
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_iam_authorization"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-okta-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@e2eLocalCoreTeams"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Core_AccessManager
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_iam_authorization"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-okta-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@e2eLocalCoreAccessManager"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Core_ApiKeys
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_iam_workload_identity"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-okta-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@e2eLocalCoreApiKeys"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Core_User
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_iam_identity_security"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-okta-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@e2eLocalCoreUser"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Core_Auth
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_iam_identity_security"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-okta-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@e2eLocalCoreAuth"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Core_Alerts
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_alerts_platform"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        # The reason this tests needs the okta overrides is due to
        # informationalAlerts.feature, which requires form registration
        # to process an invitation
        extraPropFile: "conf-local-e2e-okta-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@e2eLocalCoreAlerts"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Test_Util_On
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_backup_private_cloud"]
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@testUtil"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Test_Util_Off
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_backup_private_cloud"]
  commands:
    - func: "run e2e local tests without testUtil"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@testUtilOff"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_UI_Sanity
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_backup_private_cloud"]
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@uiSanity"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_SCRAM_SHA_256_Auth
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @scramSha256"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_OIDC_Auth
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @oidc"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_X509_Auth_Part1
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @x509 and @part1"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_X509_Auth_Part2
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @x509 and @part2"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_X509_Auth_Part3
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @x509 and @part3"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_X509_Auth_Part4
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @x509 and @part4"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_X509_Auth_MultiServer
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        e2e_remote_distro: amazon2-large
        e2e_remote_user: ec2-user
        e2e_remote_num_hosts: 6
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: ec2-user
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: ec2-user
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @x509multiserver"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_ChangeListener_MultiServer
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        e2e_remote_distro: amazon2-large
        e2e_remote_user: ec2-user
        e2e_remote_num_hosts: 3
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for automation e2e local tests replica set with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-rs-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: ec2-user
        daemonEnvironment: "local"
        # graceful shutdown testing (ps/kill commands) requires local access
        automationTestRunner: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-rs-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: ec2-user
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @changeListenerMultiserver"
        # DEBUG log level necessary for log processing
        extra_args: "--test_timeout=14400 --test_arg=--jvm_flag=-Dlocal.automation.agent.logLevel=DEBUG"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_PublishLatency_MultiServer
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        e2e_remote_distro: amazon2-large
        e2e_remote_user: ec2-user
        e2e_remote_num_hosts: 3
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: ec2-user
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-rs-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: ec2-user
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @publishLatencyMultiserver"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Sanity_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and (@sanity or @creation)"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Deployment_Item_Disabled_State_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @disabledstate and @multiserver"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Upgrades_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @mongodbupgrades and @multiserver"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Downgrades_Remote_Windows_Part1
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @mongodbversiondowngrade and @multiserver and @part1"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Downgrades_Remote_Windows_Part2
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @mongodbversiondowngrade and @multiserver and @part2"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Auth_Part1_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @auth and @part1"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Auth_Part2_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @auth and @part2"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Auth_Part3_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @auth and @part3"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Keyfile_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @keyfile"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Deployment_Conversion_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @deploymentItemConversion"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_ShardDraining_Part1_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @sharddraining and @part1"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_ShardDraining_Part2_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @sharddraining and @part2"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_ShardDraining_Part3_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @sharddraining and @part3"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_BI_Connector_Part1_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @biconnector and @part1 and not @publicapi"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_BI_Connector_Part2_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @biconnector and @part2 and not @publicapi"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_BI_Connector_Part3_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @biconnector and @part3 and not @publicapi"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_BI_Connector_PublicApi_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @biconnector and @publicapi"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Simple_Create_Convert_Part1_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @ssl-simple-create-convert and @part1 and not @linuxRemoteServer"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Simple_Create_Convert_Part2_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @ssl-simple-create-convert and @part2 and not @linuxRemoteServer"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Simple_Create_Convert_Part3_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @ssl-simple-create-convert and @part3 and not @linuxRemoteServer"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Adv_Convert_Part1_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @ssl-adv-convert and @part1 and not @linuxRemoteServer"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Adv_Convert_Part2_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @ssl-adv-convert and @part2 and not @linuxRemoteServer"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Adv_Convert_Part3_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @ssl-adv-convert and @part3 and not @linuxRemoteServer"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_KerberosAuth_Remote_Windows_50_and_Below
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [ *windows_2016_dc ]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-automation-windows-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @kerberos and @windows and @le50"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_KerberosAuth_Remote_Windows_60_and_Above
  tags: [ "e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage" ]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      retry_on_failure: true
      vars:
        <<: [ *windows_2016_dc ]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      retry_on_failure: true
      vars:
        extraPropFile: "conf-local-e2e-automation-windows-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      retry_on_failure: true
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @kerberos and @windows and @ge60"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Deployment_Item_Removal_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_large]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @multiserver and @removal"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_NoPasswordsOnDisk_MultiServer
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        e2e_remote_distro: amazon2-large
        e2e_remote_user: ec2-user
        e2e_remote_num_hosts: 6
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: ec2-user
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: ec2-user
        daemonEnvironment: "local"
        cucumber_tags: "@deployment and @noPasswordsOnDisk"
        extra_args: "--test_timeout=14400 --test_arg=--jvm_flag=-Denable.local.configuration.server=true"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_GSSAPI_Auth_Part1
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @gssapi and @part1"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_GSSAPI_Auth_Part2
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @gssapi and @part2"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_GSSAPI_Auth_Part3
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @gssapi and @part3"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_LDAP_Native_Auth_Part1
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ldap and @native and @part1"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_LDAP_Native_Auth_Part2
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ldap and @native and @part2"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_LDAP_Native_Auth_Part3
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ldap and @native and @part3"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_LDAP_Native_Auth_Smooth_Password_Rotation
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ldap and @native and @smoothrotation"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_LDAP_SASL_Auth
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        # saslauthd usage requires local access
        automationTestRunner: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ldap and @saslauthd"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_KMIP
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @kmip"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_KMIP_PROXY_Part1
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @kmipproxy and @part1"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_KMIP_PROXY_Part2
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @kmipproxy and @part2"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_KMIP_PROXY_Part3
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @kmipproxy and @part3"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Auth_Backup_WT
  tags: ["e2e_local_with_daemon", "e2e_local_with_daemon_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests with daemon
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
        # saslauthd usage requires local access
        automationTestRunner: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @enterpriseauth and @backup and @wt"
        extra_args: "--test_timeout=10800"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Import_Automation
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @importforautomation"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Import_Automation_Sanity
  tags: [ "e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage" ]
  must_have_test_results: true
  commands:
    - func: "fetch upstream agent build variables"
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @importforautomation and @sanity"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Agent_Alerts
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @agentalerts"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Agent_Upgrade
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @agentupgrade"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Upgrades_Part1
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @mongodbupgrades and not @multiserver and @part1"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Upgrades_Part2
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @mongodbupgrades and not @multiserver and @part2"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Upgrades_Part3
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @mongodbupgrades and not @multiserver and @part3"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Patch_Version_Downgrades_Part1
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @mongodbpatchversiondowngrade and @part1"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Patch_Version_Downgrades_Part2
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @mongodbpatchversiondowngrade and @part2"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Patch_Version_Downgrades_Unsafe
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @mongodbpatchversiondowngradeunsafe"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Simple_Create_Convert_Part1
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ssl-simple-create-convert and @part1 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Simple_Create_Convert_Part2
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ssl-simple-create-convert and @part2 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Simple_Create_Convert_Part3
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ssl-simple-create-convert and @part3 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Simple_Create_Convert_Part4
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ssl-simple-create-convert and @part4 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Simple_Create_Convert_Seperate_CA
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ssl-simple-create-convert-seperate-ca and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Adv_Convert_Part1
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ssl-adv-convert and @part1 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Adv_Convert_Part2
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ssl-adv-convert and @part2 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Adv_Convert_Part3
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ssl-adv-convert and @part3 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_SSL_Adv_Convert_Part4
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @ssl-adv-convert and @part4 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Auth_Part1
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @auth and @part1 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Auth_Part2
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @auth and @part2 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Auth_Part3
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @auth and @part3 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Auth_Part4
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @auth and @part4 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Auth_Part5
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @auth and @part5 and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_AuthUsers
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @authUsers and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Keyfile
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @keyfile and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_BI_Connector_Part1
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @biconnector and not @multiserver and @part1"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_BI_Connector_Part2
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @biconnector and not @multiserver and @part2"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_BI_Connector_Part3
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @biconnector and not @multiserver and @part3"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Mongot
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @mongot"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_Mongotune
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @mongotune"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_ShardDraining_Part1
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @sharddraining and not @multiserver and @part1"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_ShardDraining_Part2
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @sharddraining and not @multiserver and @part2"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_MongoDB_ShardDraining_Part3
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @sharddraining and not @multiserver and @part3"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Deployment_Item_Creation
  tags: ["e2e_local", "e2e_local_old_agent", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @creation and not (@multiserver or @sanity)"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Config_Shard
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @config-shard and not (@multiserver or @sanity)"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Single_Shard_Cluster
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @single-shard-cluster and not (@multiserver or @sanity)"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Deployment_Cluster_Wide_Configuration
  tags: [ "e2e_local", "assigned_to_jira_team_cloudp_automation_triage" ]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @cluster-wide-config"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Deployment_Item_Creation_Sanity
  tags: ["e2e_local", "e2e_local_old_agent", "e2e_local_required", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "fetch upstream agent build variables"
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @creation and @sanity and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Deployment_Item_Disabled_State
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @disabledstate and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Deployment_Clusters_Page
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @deploymentClusters"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_PublicApiSmokeTest
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @public-api-smoke"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_AmazonLinux2_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*amazon_linux2]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*amazon_linux2]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*amazon_linux2]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc"
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_Amazon2023_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*amazon_2023]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*amazon_2023]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*amazon_2023]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc"
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"


- name: E2E_CloudManager_SanityTests_RemoteAutomation_Amazon2023_ARM_WTC
  tags: [ "e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage" ]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [ *amazon_2023_arm ]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [ *amazon_2023_arm ]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [ *amazon_2023_arm ]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc"
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_Debian10_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*debian_10]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*debian_10]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*debian_10]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-50-60"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_Debian11_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*debian_11]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*debian_11]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*debian_11]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-50-70"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_Debian12_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*debian_12]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*debian_12]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*debian_12]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-70-and-above"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_RHEL70_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*rhel_76]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*rhel_76]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*rhel_76]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-50-70"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_RHEL88_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*rhel_88]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*rhel_88]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*rhel_88]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc"
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_RHEL93_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*rhel_93]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*rhel_93]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*rhel_93]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-70-and-above"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_RHEL93_ARM_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*rhel_93_arm]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*rhel_93_arm]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*rhel_93_arm]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-70-and-above"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_RHEL88_ARM_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*rhel_88_arm]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*rhel_88_arm]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*rhel_88_arm]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc"
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_AmazonLinux2_ARM_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*amazon_linux2_arm]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*amazon_linux2_arm]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*amazon_linux2_arm]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc"
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_SUSE12_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*suse_12]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*suse_12]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*suse_12]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-50-70"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_SUSE15_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*suse_15]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*suse_15]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*suse_15]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-50-70"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_SUSE15SP15_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*suse_15_sp15]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*suse_15_sp15]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*suse_15_sp15]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-70-and-above"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_Ubuntu1804_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*ubuntu_1804]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*ubuntu_1804]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*ubuntu_1804]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-50-60"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CM_Backup_Restore_PrivateLink_AWS
  tags: ["e2e_backup_continuous", "assigned_to_jira_team_cloudp_backup_private_cloud"]
  disable: true # CLOUDP-317730: Fix and re-enable
  must_have_test_results: true
  commands:
    - func: setup conf-cukes-secure for e2e
    - func: run e2e tests with Bazel
      vars:
        e2e_test: "cm"
        cucumber_tags: '@backupRestoreReplicaSetPrivateLink'
        extra_args: "--test_arg=--jvm_flag=-Dmms.baseUrl=https://cloud-dev.mongodb.com"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"


- name: E2E_CloudManager_SanityTests_RemoteAutomation_Ubuntu2004_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_backup_private_cloud"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*ubuntu_2004]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*ubuntu_2004]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*ubuntu_2004]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc"
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_Ubuntu2204_WTC
  tags: ["e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*ubuntu_2204]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [*ubuntu_2204]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [*ubuntu_2204]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-60-and-above"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_Ubuntu2404_WTC
  tags: [ "e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_backup_private_cloud" ]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [ *ubuntu_2404 ]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [ *ubuntu_2404 ]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [ *ubuntu_2404 ]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-80-and-above"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_Ubuntu2004_ARM64_WTC
  tags: [ "e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_backup_private_cloud" ]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [ *ubuntu_2004_arm ]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [ *ubuntu_2004_arm ]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [ *ubuntu_2004_arm ]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc"
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_Ubuntu2204_ARM64_WTC
  tags: [ "e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_backup_private_cloud" ]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [ *ubuntu_2204_arm ]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [ *ubuntu_2204_arm ]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [ *ubuntu_2204_arm ]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-60-and-above"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_CloudManager_SanityTests_RemoteAutomation_Ubuntu2404_ARM64_WTC
  tags: [ "e2e_cm_os_sanity", "assigned_to_jira_team_cloudp_backup_private_cloud" ]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [ *ubuntu_2404_arm ]
        e2e_remote_num_hosts: 1
    - func: "prepare for e2e local tests with remote agents"
      vars:
        <<: [ *ubuntu_2404_arm ]
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        <<: [ *ubuntu_2404_arm ]
        cucumber_tags: "@cm-sanity-tests-remote-automation-agent-wtc-80-and-above"
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Deployment_Conversion
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @deploymentItemConversion and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Deployment_Item_Removal
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @removal and not @multiserver"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Deployment_Performance
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @performance"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Custom_Builds
  tags: ["e2e_local_with_daemon", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests with daemon
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @custombuilds"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_ATM_Servers_Page
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_automation_triage"]
  must_have_test_results: true
  commands:
    - func: prepare for automation local e2e tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        automationTestRunner: "sandboxed"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@deployment and @servers"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_AllClusters
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@all-clusters"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_AgentAPIKeys_API
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@agent-api-keys-api"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_AgentAPIKeys_UI
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@agent-api-keys-ui"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Monitoring_Standalone
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@metrics-charts-monitoring-standalone"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Automated_Standalone
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@metrics-charts-automated-standalone"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Monitoring_Replica_Set
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@metrics-charts-monitoring-replica-set"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Automated_Replica_Set
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@metrics-charts-automated-replica-set"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Monitoring_Sharded_Cluster
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@metrics-charts-monitoring-sharded-cluster"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Automated_Sharded_Cluster
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@metrics-charts-automated-sharded-cluster"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Import_Standalone
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@import-for-monitoring-standalone"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Import_Replica_Set
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@import-for-monitoring-replica-set"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Import_Cluster
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@import-for-monitoring-cluster"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Import_Replica_Set_Auth
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@import-for-monitoring-replica-set-auth"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Import_Cluster_Auth
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@import-for-monitoring-cluster-auth"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_DataExplorer_Read_Full
  tags: ["e2e_intel_nightly", "assigned_to_jira_team_cloudp_developer_tools", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests replicaset"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@data-explorer-read"
        extra_args: "--test_timeout=10800"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_DataExplorer_Read_Write_Full
  tags: ["e2e_intel_nightly", "assigned_to_jira_team_cloudp_developer_tools", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests replicaset"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@data-explorer-read-write"
        extra_args: "--test_timeout=10800"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_DataExplorer_Admin_Full
  tags: ["e2e_intel_nightly", "assigned_to_jira_team_cloudp_developer_tools", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests replicaset"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@data-explorer-admin"
        extra_args: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_DataExplorer_Read
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_developer_tools", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests replicaset"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@data-explorer-read-latest"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_DataExplorer_Read_Write
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_developer_tools", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests replicaset"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@data-explorer-read-write-latest"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_DataExplorer_Admin
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_developer_tools", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests replicaset"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@data-explorer-admin-latest"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Profiler
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@profiler and @group1"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Profiler_2
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@profiler and @group2"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_ApiMeasurements
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@public-api-measurements"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_EmbeddedConfigMetricsUi
  tags: [ "e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only" ]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@embeddedConfigMetricsUi"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_CollStatsLatencyMeasurements_Version5.0
  tags: [ "e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only" ]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@coll-stats-latency-measurements and @mongodb50"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_CollStatsLatencyMeasurements_Version6.0
  tags: [ "e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only" ]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@coll-stats-latency-measurements and @mongodb60"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_CollStatsLatencyMeasurements_Version7.0
  tags: [ "e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only" ]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@coll-stats-latency-measurements and @mongodb70"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_CollStatsLatencyMeasurements_VersionLatest
  tags: [ "e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only" ]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@coll-stats-latency-measurements and @dynamicMongoDBVersion"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_S3_LogCollection
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_platforms_xv" ]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        #testGroup: "cloud"
        extraPropFile: "conf-local-e2e-overrides.properties"
        #tags: "@cm-logcollection"
        appServerOptions: ">> mms-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@cm-logcollection"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_FeatureToggles
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_developer_tools", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@project-level-toggles"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_PerformanceAdvisor
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@performance-advisor"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_PerformanceAdvisor_Landing
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@performance-advisor-landing"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_PerformanceAdvisor_CCPA
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@performance-advisor-ccpa"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_PerformanceAdvisor_DropIndexes
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        mms_data_region: "region"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@performance-advisor-drop-indexes"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_CCPA_DropIndexes
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        mms_data_region: "region"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@performance-advisor-cc-drop-indexes-replicaset"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_CCPA_DropIndexesSharded
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        mms_data_region: "region"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@performance-advisor-cc-drop-indexes-sharded"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_CCPA_DropIndexesStandalone
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        mms_data_region: "region"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@performance-advisor-cc-drop-indexes-standalone"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_CCPA_SchemaAdvisorStandalone
  tags: [ "e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only" ]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        mms_data_region: "region"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@performance-advisor-cc-schema-advisor-standalone"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_CCPA_SchemaAdvisorReplicaSet
  tags: [ "e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only" ]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        mms_data_region: "region"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@performance-advisor-cc-schema-advisor-replica-set"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_CCPA_SchemaAdvisorSharded
  tags: [ "e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only" ]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        mms_data_region: "region"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@performance-advisor-cc-schema-advisor-sharded"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_PerformanceAdvisor_DataServices
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
        daemonOptions: ">> daemon-local.log"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@performance-advisor-data-services"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_ClientMetadata
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @clientmetadata"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_HotColl
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-hot-coll"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_Long_Running_Slow_Ops
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-long-running-slow-ops"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_SlowOp
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-slow-op"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_SlowOp_Details
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-slow-op-details"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_SlowOp_KillOp
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-slow-op-kill-op"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_Charts_View_Primary
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-charts-view-primary"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_Charts_View_Pause
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-charts-view-pause"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_Charts_View_Unpause
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-charts-view-unpause"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_Table_View_Primary
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-table-view-primary"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_Table_View_Pause
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-table-view-pause"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_Table_View_Unpause
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-table-view-unpause"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_Charts_View_Secondary
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-charts-view-secondary"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RTPP_Table_View_Secondary
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=10800"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @real-time-table-view-secondary"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_RollingIndexCleanup
  tags: ["e2e_local", "assigned_to_jira_team_cloudp_developer_tools", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "run e2e local tests"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        extra_args: "--test_timeout=7200"
        e2e_test: "cm"
        cucumber_tags: "@monitoring and @rolling-index-cleanup"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

# Begin Monitoring Remote Windows tasks

- name: E2E_Local_Monitoring_AllClusters_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@all-clusters"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_AgentAPIKeys_API_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@agent-api-keys-api"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_AgentAPIKeys_UI_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@agent-api-keys-ui"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Monitoring_Standalone_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@metrics-charts-monitoring-standalone"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Automated_Standalone_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@metrics-charts-automated-standalone"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Monitoring_Replica_Set_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@metrics-charts-monitoring-replica-set"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Automated_Replica_Set_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@metrics-charts-automated-replica-set"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Monitoring_Sharded_Cluster_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@metrics-charts-monitoring-sharded-cluster"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_MetricsCharts_Automated_Sharded_Cluster_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@metrics-charts-automated-sharded-cluster"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Import_Standalone_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@import-for-monitoring-standalone"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Import_Replica_Set_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@import-for-monitoring-replica-set"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Import_Cluster_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@import-for-monitoring-cluster"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Import_Replica_Set_Auth_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@import-for-monitoring-replica-set-auth"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_Import_Cluster_Auth_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_insights_and_telemetry", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@import-for-monitoring-cluster-auth"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_ApiMeasurements_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_intel_i", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@public-api-measurements"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_PerformanceAdvisor_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@performance-advisor"
        extra_args: "--test_timeout=7200"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

- name: E2E_Local_Monitoring_ClientMetadata_Remote_Windows
  tags: ["e2e_local_remote_win", "assigned_to_jira_team_cloudp_intel_ii", "foliage_check_task_only"]
  must_have_test_results: true
  commands:
    - func: "create remote e2e hosts"
      vars:
        <<: [*windows_small]
        e2e_remote_num_hosts: 1
        e2e_remote_agents_dir: /data/mci
    - func: "prepare for e2e local tests with remote agents and daemon"
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "cm"
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        e2e_remote_user: Administrator
        daemonEnvironment: "local"
        cucumber_tags: "@monitoring and @clientmetadata"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/cm"

# End Monitoring Remote Windows tasks

- name: ATM_SMOKE_TEST
  tags: ["atm_smoke_test", "assigned_to_jira_team_cloudp_automation_triage", "foliage_check_task_only"]
  commands:
    - command: shell.exec
      params:
        working_dir: mms
        shell: bash
        script: |
          set -euvx
          cd scripts/automation_smoke_test

          export PATH=/opt/mongodbtoolchain/v4/bin:$PATH
          virtualenv -p python3 automation_smoke_test
          source automation_smoke_test/bin/activate
          which python3
          python3 --version

          pip3 install -r requirements.txt

          # Use Automation Smoke Test Org in cloud-dev 652d5ef495212851d40d1f58
          python3 smoke_test.py 652d5ef495212851d40d1f58 `hostname -f` ${mongoDbFcv} ${smokeTestApiPublicKey} ${smokeTestApiPrivateKey} ${agentVersion} ${agentArch}

- name: E2E_CYPRESS_TASK_GENERATOR
  tags: ["assigned_to_jira_team_cloudp_frontend_platform"]
  commands:
    - func: "setup cypress task generator"
    - command: s3.put
      params:
        bucket: mciuploads
        local_files_include_filter:
          - "mms/cypress-e2e-tasks.json"
        remote_file: ${mciuploads_dir|mms}/${build_id}/${task_name}/${execution}/cypress-e2e-tasks.json
        permissions: private
        visibility: signed
        content_type: ${content_type|application/json}
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
    - command: generate.tasks
      params:
        files:
          - mms/cypress-e2e-tasks.json

- name: VERIFY_CYPRESS_TASK_OWNERS
  tags: ["unit_js", "assigned_to_jira_team_cloudp_frontend_platform"]
  commands:
    - func: "setup cypress task generator"
      vars:
        verify_only: "true"

- name: INT_JAVA_EXTERNAL_AWS_PROVISION_MACHINE_MOVE
  tags: [ "assigned_to_jira_team_cloudp_atlas_clusters_platform_ii", "foliage_health", "foliage_check_task_only" ]
  must_have_test_results: true
  commands:
    - func: "setup conf-test-secure for int"
    - func: "setup service-test-secure for standalone services"
    - func: "install mongosh"
    - func: "start mongod single node replicaset"
      vars:
        replicaSetSingleNodeMongodPort: "26000"
    - func: "wait for single node mongod replicaset"
      vars:
        replicaSetSingleNodeMongodPort: "26000"
    - command: subprocess.exec
      params:
        command: "cp \"${workdir}/mms/scripts/evergreen/logback-test-external-int.xml\" \"${workdir}/mms/server/conf/logback/logback-test-custom.xml\""
    - func: "run bazel test init"
      vars:
        test_grouping: "//server/src/test:nds"
    - func: "run bazel test"
      vars:
        bazel_target: "//server/src/test/com/xgen/svc/nds/aws/planner:provisionMachineTestLibraryAllTests"
        bazel_options: "--test_filter=AWSProvisionMachineMoveIntTests.testAWSProvisionMachineMove --test_output=streamed --test_arg=--jvm_flags='-DSTORAGE.ENGINE.FOR.BRS.TESTS=wiredTiger -DMONGODB.VERSION.FOR.BRS.TESTS=4.0.0 -Dskip.ppc.builds=${skip_ppc_builds} -Dskip.s390x.builds=${skip_s390x_builds} -Dmms.buildIndexesAndShardCollections=true -Dhttp-test-port=9080 -Dbase-port=9080' --test_env=PATH=/bin:/usr/bin:${workdir}/mms/mongodb/bin"
        test_timeout: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/test/com/xgen/svc/nds/aws/planner/provisionMachineTestLibraryAllTests"

- name: INT_JAVA_EXTERNAL_AZURE_PROVISION_MACHINE_MOVE
  tags: [ "assigned_to_jira_team_cloudp_atlas_clusters_platform_ii", "foliage_health", "foliage_check_task_only" ]
  must_have_test_results: true
  commands:
    - func: "setup conf-test-secure for int"
    - func: "setup service-test-secure for standalone services"
    - func: "install mongosh"
    - func: "start mongod single node replicaset"
      vars:
        replicaSetSingleNodeMongodPort: "26000"
    - func: "wait for single node mongod replicaset"
      vars:
        replicaSetSingleNodeMongodPort: "26000"
    - command: subprocess.exec
      params:
        command: "cp \"${workdir}/mms/scripts/evergreen/logback-test-external-int.xml\" \"${workdir}/mms/server/conf/logback/logback-test-custom.xml\""
    - func: "run bazel test init"
      vars:
        test_grouping: "//server/src/test:nds"
    - func: "run bazel test"
      vars:
        bazel_target: "//server/src/test/com/xgen/svc/nds/azure/planner:provisionMachineTestLibraryAllTests"
        bazel_options: "--test_filter=AzureProvisionMachineMoveIntTests.testAzureProvisionMachineMove_CapacityUnaware --test_output=streamed --test_arg=--jvm_flags='-DSTORAGE.ENGINE.FOR.BRS.TESTS=wiredTiger -DMONGODB.VERSION.FOR.BRS.TESTS=4.0.0 -Dskip.ppc.builds=${skip_ppc_builds} -Dskip.s390x.builds=${skip_s390x_builds} -Dmms.buildIndexesAndShardCollections=true -Dhttp-test-port=9080 -Dbase-port=9080' --test_env=PATH=/bin:/usr/bin:${workdir}/mms/mongodb/bin"
        test_timeout: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/test/com/xgen/svc/nds/azure/planner/provisionMachineTestLibraryAllTests"

- name: INT_JAVA_EXTERNAL_GCP_PROVISION_MACHINE_MOVE
  tags: [ "assigned_to_jira_team_cloudp_atlas_clusters_platform_ii", "foliage_health", "foliage_check_task_only" ]
  must_have_test_results: true
  commands:
    - func: "setup conf-test-secure for int"
    - func: "setup service-test-secure for standalone services"
    - func: "install mongosh"
    - func: "start mongod single node replicaset"
      vars:
        replicaSetSingleNodeMongodPort: "26000"
    - func: "wait for single node mongod replicaset"
      vars:
        replicaSetSingleNodeMongodPort: "26000"
    - command: subprocess.exec
      params:
        command: "cp \"${workdir}/mms/scripts/evergreen/logback-test-external-int.xml\" \"${workdir}/mms/server/conf/logback/logback-test-custom.xml\""
    - func: "run bazel test init"
      vars:
        test_grouping: "//server/src/test:nds"
    - func: "run bazel test"
      vars:
        bazel_target: "//server/src/test/com/xgen/svc/nds/gcp/planner:provisionMachineTestLibraryAllTests"
        bazel_options: "--test_filter=GCPProvisionMachineMoveIntTests.testGCPProvisionMachineMove_CapacityUnaware --test_output=streamed --test_arg=--jvm_flags='-DSTORAGE.ENGINE.FOR.BRS.TESTS=wiredTiger -DMONGODB.VERSION.FOR.BRS.TESTS=4.0.0 -Dskip.ppc.builds=${skip_ppc_builds} -Dskip.s390x.builds=${skip_s390x_builds} -Dmms.buildIndexesAndShardCollections=true -Dhttp-test-port=9080 -Dbase-port=9080' --test_env=PATH=/bin:/usr/bin:${workdir}/mms/mongodb/bin"
        test_timeout: "--test_timeout=14400"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/test/com/xgen/svc/nds/gcp/planner/provisionMachineTestLibraryAllTests"

- name: INT_JS_TASK_GENERATOR
  tags: ["int_js", "assigned_to_jira_team_cloudp_frontend_platform"]
  commands:
    - command: shell.exec
      params:
        working_dir: mms
        shell: bash
        env:
          NODEJS_HOME: ${workdir}/mms/nodejs
          IS_PATCH: ${is_patch}
        include_expansions_in_env: [BAZELISK_HOME, CYPRESS_CACHE_FOLDER]
        script: |
          set -ex
          if [ "${int_js_task_generator_targets}" != "" ]; then
            bazel run //client/packages/scripts/evergreen-generators:int_js -- --tasks "${int_js_task_generator_targets}"
          else
            packages_to_test=$(bazel query 'filter(":js_int_test$", tests(//client/... + //systems/...))' --output package)
            bazel run //client/packages/scripts/evergreen-generators:int_js -- $packages_to_test
          fi

    - command: s3.put
      params:
        bucket: mciuploads
        local_file: "mms/js_int_test-tasks.json"
        remote_file: ${mciuploads_dir|mms}/${build_id}/${task_name}/${execution}/js_int_test-tasks.json
        permissions: private
        visibility: signed
        content_type: ${content_type|application/json}
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
    - command: generate.tasks
      params:
        files:
          - mms/js_int_test-tasks.json

- name: VALIDATE_PNPM_LOCK_YAML
  tags: ["code_health", "assigned_to_jira_team_cloudp_backup_private_cloud"]
  priority: 5 # small priority bump since PACKAGE_CLOUD_DEPLOY_GATE depends on this
  commands:
    - func: "fetch nodejs"
    - command: subprocess.exec
      params:
        working_dir: mms
        add_to_path:
          - ${workdir}/mms/nodejs/bin
        include_expansions_in_env: [CYPRESS_CACHE_FOLDER]
        command: ${workdir}/mms/scripts/evergreen/validate_pnpm_lock_yaml.sh

- name: COMPASS_GENERATIVE_AI_ACCURACY_TESTS
  tags: ["compass_generative_ai_accuracy_tests_nightly", "assigned_to_jira_team_cloudp_developer_tools"]
  commands:
    - func: "install mongodb and tools"
    - func: "install mongosh"
    - func: "start mongod single node replicaset"
    - func: "wait for single node mongod replicaset"
    - func: "setup conf-test-secure for int"
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        include_expansions_in_env: [use_engflow_cache, engflow_rollout_percent, workdir, distro_id, BAZELISK_HOME]
        env:
          NLQ_ACCURACYTESTS_REPORT_METRICS_CONNECTION_STRING: ${compass_gen_ai_accuracy_test_results_mdb_connection_string}
        script: |
          set -xu


          bazel test --test_timeout=3600 --execution_log_binary_file="${workdir}/execution.log" --build_tests_only \
            --test_env=NLQ_ACCURACYTESTS_REPORT_METRICS_CONNECTION_STRING=$NLQ_ACCURACYTESTS_REPORT_METRICS_CONNECTION_STRING \
            --test_env=NLQ_ACCURACYTESTS_ATTEMPTS_PER_TEST_CASE=10 //server/src/test/com/xgen/cloud/naturallanguagequery/accuracytests:GenerativeAIQueryAccuracyIntTests
          echo "Ran accuracy tests, checking status now..."

          # Set the task status as success when we find the test success.
          # We do this since the bazel will fail when one test fails, although the overall accuracy is successful.
          if grep -q "Overall accuracy success: true" "${workdir}/mms/bazel-testlogs/server/src/test/com/xgen/cloud/naturallanguagequery/accuracytests/GenerativeAIQueryAccuracyIntTests/test.log"; then
            echo "Accuracy tests succeeded."
            curl -d '{"status":"success", "desc":"Accuracy tests successful."}' \
              -H "Content-Type: application/json" -X POST localhost:2285/task_status
            exit 0
          fi
        continue_on_err: true
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/test/com/xgen/cloud/naturallanguagequery/accuracytests/GenerativeAIQueryAccuracyIntTests"

- name: COMPASS_GENERATIVE_AI_API_TESTS
  tags: ["compass_generative_ai_tests_nightly", "assigned_to_jira_team_cloudp_developer_tools"]
  commands:
    - func: "install mongodb and tools"
    - func: "install mongosh"
    - func: "start mongod single node replicaset"
    - func: "wait for single node mongod replicaset"
    - func: "setup conf-test-secure for int"
    - func: "run bazel test"
      vars:
        bazel_target: //server/src/test/com/xgen/svc/mms/api/res/generativeai/...

- name: COMPASS_DE_GENERATIVE_AI_TESTS
  tags: ["compass_generative_ai_tests_nightly", "assigned_to_jira_team_cloudp_developer_tools"]
  commands:
    - func: "install mongodb and tools"
    - func: "install mongosh"
    - func: "start mongod single node replicaset"
    - func: "wait for single node mongod replicaset"
    - func: "setup conf-test-secure for int"
    - func: "run bazel test"
      vars:
        bazel_target: //server/src/test/com/xgen/svc/mms/res/generativeai/...

- name: IDEMPOTENT_MIGRATIONS
  tags: ["code_health", "assigned_to_jira_team_cloudp_backup_private_cloud"]
  priority: 5 # small priority bump since PACKAGE_CLOUD_DEPLOY_GATE depends on this
  commands:
    - func: "install mongodb and tools"
    - func: "install mongosh"
    - func: "start mongod single node replicaset"
    - func: "wait for single node mongod replicaset"
    - func: "setup conf-test-secure for int"
    - func: "run bazel test"
      vars:
        bazel_target: //server/src/test/com/xgen/cloud/migration/_public/svc:EnsureMigrationsAreTestedIntTests
        bazel_options: "--sandbox_default_allow_network --noincompatible_exclusive_test_sandboxed"  # un-network sandbox this int test
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/test/com/xgen/cloud/migration/_public/svc/EnsureMigrationsAreTestedIntTests"

- name: Intel_Metrics_Streaming_Test_Datadog
  tags: ["e2e_intel_nightly", "assigned_to_jira_team_cloudp_intel_i"]
  commands:
    - func: "install mongodb and tools"
    - func: "install mongosh"
    - func: "start mongod single node replicaset"
      vars:
        mongodBasePort: 26000
    - func: "wait for single node mongod replicaset"
      vars:
        mongodBasePort: 26000
    - func: "start mongod single node replicaset"
      vars:
        mongodBasePort: 27017
    - func: "wait for single node mongod replicaset"
      vars:
        mongodBasePort: 27017
    - func: "setup conf-test-secure for int"
    - func: "start standalone service"
      vars:
        serviceName: "telemetry-consumers"
    - *start_localstack
    - func: "run bazel test"
      vars:
        bazel_target: "//server/src/test/com/xgen/cloud/services/telemetryconsumers/_private/consumers/kcl/..."
        bazel_options: "--sandbox_default_allow_network --noincompatible_exclusive_test_sandboxed"  # un-network sandbox this int test

- name: E2E_Standalone_Migration
  tags: ["code_health", "assigned_to_jira_team_cloudp_developer_tools"] # Move this to Core XV later
  commands:
    - func: run e2e migration tests
      vars:
        extraPropFile: "conf-local-e2e-overrides.properties"
        appServerOptions: ">> mms-local.log"
        migrationPropFile: "migration/base/test-with-config-service.properties"
        mongodBasePort: 27017

- name: E2E_NDS_DRY_RUN
  tags: ["code_health", "assigned_to_jira_team_cloudp_atlas_clusters_platform_triage"]
  must_have_test_results: true
  commands:
    - func: "run e2e tests"
      vars:
        cloudProvider: "AWS"
    - func: "run e2e tests with Bazel"
      vars:
        e2e_test: "nds"
        cucumber_tags: ""
        extra_args: "--test_arg=--jvm_flag=-Dcucumber.execution.dry-run=true"
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/features/nds"

- name: VALIDATE_VERSION_MANIFEST
  tags: ["code_health", "assigned_to_jira_team_cloudp_backup_private_cloud"]
  commands:
    - func: "install mongodb and tools"
    - func: "install mongosh"
    - func: "start mongod single node replicaset"
    - func: "wait for single node mongod replicaset"
    - func: "run bazel test"
      vars:
        bazel_target: "//server/src/test/com/xgen/svc/mms/util/manifest/..."
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "server/src/test/com/xgen/svc/mms/util/manifest/TestLibraryAllTests"

- name: VALIDATE_NEW_MIGRATIONS
  tags: ["code_health", "assigned_to_jira_team_cloudp_backup_private_cloud"]
  commands:
    - command: github.generate_token
      type: system
      params:
        expansion_name: gh_pr_oauth_token
        permissions:
          contents: read
    - command: shell.exec
      params:
        working_dir: mms
        shell: bash
        include_expansions_in_env: [BAZELISK_HOME]
        env:
          GITHUB_OAUTH_TOKEN: ${gh_pr_oauth_token}
        script: |
          set -eux

          if [[ ! "${branch_name}" =~ ^v ]]; then
            if [ "${github_pr_number}" != "" ]; then
                bazel run //scripts/python/src/ops_manager:check_migrations -- --pull-request
            else
                bazel run //scripts/python/src/ops_manager:check_migrations
            fi
          fi

- name: VALIDATE_CODEOWNERS
  tags: ["code_health", "assigned_to_jira_team_devprod_correctness"]
  commands:
    - command: expansions.write
      params:
        file: ./expansions.yml
        redacted: true
    - func: "run bazel command"
      vars:
        label: "codeowners"
        args: "--check --expansions-file ../expansions.yml"

- name: VALIDATE_GENERATED_ATLAS_PRIVATE_API_DEFINITION
  tags: ["code_health", "assigned_to_jira_team_cloudp_atlas_clusters_platform_triage"]
  commands:
    - func: "run bazel test"
      vars:
        bazel_target: "//server/openapi/nds-private-api/... //server/openapi/nds-private-api-strict/... //server/openapi/billing-private-api/..."

- name: VALIDATE_CLIENT_ALERT_CONFIGS
  tags: ["code_health", "assigned_to_jira_team_cloudp_frontend_platform"]
  commands:
    - func: "run bazel command"
      vars:
        label: "//scripts/sentry/src:validate_client_alert_configs"

- name: CHECK_XGEN_CLOUD_PACKAGES_NOT_IN_MONOLITH
  tags: ["code_health", "assigned_to_jira_team_devprod_build"]
  commands:
    - func: "run bazel command"
      vars:
        label: "//scripts/bazel_lint_checks:check_xgen_cloud_build_file_coverage"

- name: CHECK_XGEN_CLOUD_PACKAGES_NOT_IN_MONOLITH_P2
  tags: ["code_health", "assigned_to_jira_team_devprod_build"]
  commands:
    - func: "run bazel command"
      vars:
        label: "//scripts/bazel_lint_checks:package_structure_validator"

- name: CHECK_UNIT_TESTS_ARE_ALL_MAPPED_TO_EVERGREEN_TASKS
  tags: ["assigned_to_jira_team_devprod_build"]
  commands:
    - func: "run bazel command"
      vars:
        label: "//scripts/bazel_lint_checks:check_tests_are_all_mapped_to_evergreen_tasks"
        args: "unit"

- name: CHECK_INT_TESTS_ARE_ALL_MAPPED_TO_EVERGREEN_TASKS
  tags: ["assigned_to_jira_team_devprod_build"]
  commands:
    - func: "run bazel command"
      vars:
        label: "//scripts/bazel_lint_checks:check_tests_are_all_mapped_to_evergreen_tasks"
        args: "int"

- name: UNIT_PYTHON_MMS
  tags: ["unit_python", "assigned_to_jira_team_devprod_services_integrations"]
  commands:
    - func: "initialize evergreen api"
    - func: "run bazel command"
      vars:
        bazel_command: build
        label: "@rules_python//python/runfiles"
    - func: "run python script"
      vars:
        script: "PYTEST_ADDOPTS='--junitxml=${workdir}/results.xml' ./python/test || [ $? -le 1 ]"
    - command: attach.xunit_results
      params:
        files:
          - results.xml
    - command: subprocess.exec
      params:
        working_dir: mms
        command: "touch server/conf/conf-local-secure.properties"  # let's call this a mock for telemetry

- name: UNIT_PYTHON_LINT
  tags: ["unit_python_with_gh_annotations", "assigned_to_jira_team_devprod_services_integrations", "foliage_creates_breaking_commit"]
  commands:
    - func: "run bazel command"
      vars:
        label: "//scripts/python:lint"
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        include_expansions_in_env: [ task_name, branch_name ]
        script: |
          set -exu

          source scripts/evergreen/changed_files.sh

          [ -r output.json ] && jq --arg title "${task_name}" --rawfile changedFilesRaw <(changed_files) \
            'include "./scripts/ivy_to_bazel/to_github_annotations"; include "./scripts/evergreen/github_annotations/github_checkrun";
            [.[] | pylint_to_github_annotations] | fromannotations($title; "Pylint checkers"; "")' \
            output.json \
            > "${workdir}/github_annotations.json"
    - command: s3.put
      params:
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
        local_file: github_annotations.json
        display_name: GitHub check run
        remote_file: ${mciuploads_dir|mms}/${build_id}/${task_name}/${execution}/github_annotations.json
        bucket: mciuploads
        permissions: private
        visibility: signed
        content_type: application/json

- name: UNIT_GO_GO
  tags: ["unit_go"]
  commands:
    - func: "run bazel command"
      vars:
        bazel_command: build
        label: "//go/..."
    - func: "run bazel test"
      vars:
        bazel_target: "-- //go/... -//go/e2e/..."
    - command: subprocess.exec
      params:
        working_dir: mms
        include_expansions_in_env: [workdir, TMP, BAZELISK_HOME]
        command: ./scripts/evergreen/copy_xunit_results.sh //go/...
        continue_on_err: true
    - command: attach.xunit_results
      params:
        files:
          - "tmp/test_results/*.xml"

- name: UNIT_SH
  tags: ["unit_sh", "assigned_to_jira_team_devprod_build"]
  commands:
    - command: shell.exec
      params:
        working_dir: mms
        shell: bash
        script: |
          set -ex
          packages_to_test=$(bazel query 'kind("sh_test", //... except //server/src/features/...)')
          echo "$packages_to_test" | xargs bazel test --test_output=streamed --

# Run the buf CLI tests over the standalone service proto files
- name: BUF_PROTO_TEST
  tags: ["unit_proto", "assigned_to_jira_team_devprod_correctness"]
  commands:
    - func: "merge in base branch"  # prevents the backwards compatibility check from failing if new changes on HEAD since the base revision
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        script: |
          set -exu
          echo "buf_against: https://github.com/10gen/mms.git#branch=${branch_name}" > "${workdir}/buf_expansions.yml"
          echo "build --test_env=BUF_INPUT_HTTPS_USERNAME=x-access-token --test_env=BUF_INPUT_HTTPS_PASSWORD=${GH_TOKEN}" >> "${workdir}/.bazelrc"
    - command: expansions.update
      params:
        file: buf_expansions.yml
    - func: "run bazel test"
      vars:
        bazel_target: //server/src/main/com/xgen/cloud/services/...
        bazel_options: "--test_tag_filters=buf --test_arg=--against=${buf_against}"

- name: UNIT_JAVA_CHECKSTYLE
  tags: ["code_health", "assigned_to_jira_team_devprod_build"]
  commands:
    - func: "run bazel command"
      vars:
        bazel_command: test
        label: "//server/src/main/com/xgen/devtools/bugchecker:checkstyle"

- name: FRONTEND_TYPECHECK
  tags: ["code_health_with_gh_annotations", "assigned_to_jira_team_cloudp_frontend_platform", "foliage_creates_breaking_commit"]
  run_on: [amazon2023-cloud-large] # for jq >= 1.6, needed for --rawFile
  commands:
    - func: "run bazel frontend test"
      vars:
        bazel_target: //client/... //systems/...
        args: "--test_output=errors --test_tag_filters=typecheck"
        append_command: "| tee logs.txt"
        post_command: "echo $? > ts_exit_code"
    - command: shell.exec
      params:
        shell: bash
        script: |
          set -exu

          mkdir -p test_results
          find mms/bazel-testlogs/client mms/bazel-testlogs/systems -path "*/test.xml" -print \
            | while read -r results_xml; do
              cp "$results_xml" "$(mktemp test_results/results.XXXX)"
          done

          exit_code=$(<mms/ts_exit_code)
          if [ $exit_code -ne 0 ]; then
            # If TS failed, then create an additional xml to upload in test_results
            # Also make less noisy when creating the XML
            set +x
            source "mms/scripts/evergreen/attach_xunit_from_logs/create_xunit_xml.bash"
            create_xunit_xml mms/logs.txt "$(mktemp test_results/results.XXXX)" "Frontend Typecheck Errors" $exit_code
            set -x
          fi
    - command: attach.xunit_results
      params:
        file: "test_results/results.*"
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        include_expansions_in_env: [ task_name, branch_name ]
        script: |
          set -exu

          # branch_name is needed in this script
          source scripts/evergreen/changed_files.sh

          # logs.txt is outputted in the first step (typecheck) in this task
          [ -r logs.txt ] && jq --raw-input \
            'include "./scripts/ivy_to_bazel/to_github_annotations"; typescript_to_github_annotations' logs.txt | \
            jq --slurp --arg title "${task_name}" --rawfile changedFilesRaw <(changed_files) \
            'include "./scripts/evergreen/github_annotations/github_checkrun"; fromannotations($title; "TypeScript checkers"; "")' \
            > "${workdir}/github_annotations.json"

- name: FRONTEND_CODE_HEALTH
  tags: ["code_health_with_gh_annotations", "assigned_to_jira_team_cloudp_frontend_platform", "foliage_creates_breaking_commit"]
  run_on: [amazon2023-cloud-large] # for jq >= 1.6, needed for --rawFile
  commands:
    - func: "run bazel frontend test"
      vars:
        bazel_target: //client/... //toolchains/nodejs/... //systems/... //:unused_dependencies //:dead_code_check
        args: "--test_output=errors --test_tag_filters=code_health --test_arg=--format=json"
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        include_expansions_in_env: [ task_name, branch_name ]
        script: |
          set -exu

          # branch_name is needed in this script
          source scripts/evergreen/changed_files.sh

          find bazel-testlogs/client bazel-testlogs/systems \( \
            -path "*/lint/test.log" \
            \) -print \
            | while read -r path; do
              # bazel test logs include extra lines at the top, which makes our json log file, invalid json, so search for
              # the line that starts with valid json output from eslint.
              grep '^\[{' "$path"
            done \
            | jq --slurp --arg title "$task_name" \
              --rawfile changedFilesRaw <(changed_files) \
              'include "./scripts/ivy_to_bazel/to_github_annotations"; include "./scripts/evergreen/github_annotations/github_checkrun";
                [.[][] | eslint_to_github_annotations] | fromannotations($title; "Eslint checkers"; "")' \
              > "${workdir}/github_annotations.json"
    - command: shell.exec
      params:
        shell: bash
        script: |
          set -exu

          mkdir -p test_results
          find mms/bazel-testlogs/client mms/bazel-testlogs/systems -path "*/test.xml" -print \
            | while read -r results_xml; do
              cp "$results_xml" "$(mktemp test_results/results.XXXX)"
          done
    - command: attach.xunit_results
      params:
        file: "test_results/results.*"

- name: JS_LIBRARY_CHECKS
  tags: ["assigned_to_jira_team_cloudp_frontend_platform"]
  commands:
    - func: "run bazel frontend test"
      vars:
        bazel_target: //client/packages/libraries:check
    - func: "attach bazel test logs"
      vars:
        bazel_test_target: "client/packages/libraries/check"

- name: FRONTEND_CODE_ANALYSIS_NIGHTLY
  tags: ["frontend_code_analysis_nightly", "assigned_to_jira_team_cloudp_frontend_platform"]
  commands:
    - *early_exit_if_not_mms_project
    - *fetch_nodejs
    - *run_pnpm_install
    - command: shell.exec
      params:
        working_dir: mms
        include_expansions_in_env:
          - TRINO_CLIENT_ID_FEP
          - TRINO_CLIENT_SECRET_FEP
          - TRINO_MESH_HOST_JWT
          - TRINO_OAUTH_URL
          - TRINO_POLICY_USER_FEP
          - evergreen-api-key
          - evergreen-api-user
          - FRONTEND_DASHBOARD_DB_URL
          - FRONTEND_DASHBOARD_DB_PASSWORD
        env:
          NODEJS_HOME: ${workdir}/mms/nodejs
          COVERAGE_RAW_PATH: ${workdir}/mms/client/packages/frontend-telemetry/mms-coverage-raw
          COVERAGE_INPUT_PATH: ${workdir}/mms/client/packages/frontend-telemetry/mms-coverage-input
          COVERAGE_REPORT_PATH: ${workdir}/mms/client/packages/frontend-telemetry/mms-coverage-report
        script: |
          set -euvx
          export PATH=$NODEJS_HOME/bin:$PATH

          ${envVars|CI=true} NODE_OPTIONS="--max_old_space_size=8192" npx npm-run-all --npm-path npm frontend-telemetry
    - command: s3.put
      params:
        bucket: mciuploads
        # there are a lot of other files generated, but let's just keep it simple with the upload
        local_file: "mms/client/packages/frontend-telemetry/mms-coverage-report/index.html"
        remote_file: ${mciuploads_dir|mms}/${build_id}/${task_name}/${execution}/unit-js-test-coverage.html
        permissions: private
        visibility: signed
        content_type: text/html
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
        optional: true

- name: ENSURE_SENTRY_ALERTS
  tags: ["ensure_sentry_alerts", "assigned_to_jira_team_cloudp_atlas_clusters_platform_triage"]
  commands:
    - func: "run bazel command"
      vars:
        label: //scripts/sentry/src:generate_planner_alerts
    - func: "run bazel command"
      vars:
        label: //scripts/sentry/src:create_sentry_alerts

- name: CHECK_PROM_ALERT
  tags: [ "code_health", "assigned_to_jira_team_cloudp_atlas_clusters_platform_triage" ]
  priority: 5 # small priority bump since PACKAGE_CLOUD_DEPLOY_GATE depends on this
  commands:
    - func: "install prometheus"
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        script: |
          set -e
          ${workdir}/prometheus/bin/promtool check rules \
            $(find .alerts -type f -not \( -name "*_test.yml" -o -path ".alerts/tests/*" \) -not \( -name "*.md" \) -not \( -name "OWNERS.yml" \) -print)

- name: PROM_ADVANCED_LINT
  tags: [ "code_health", "assigned_to_jira_team_cloudp_atlas_triage" ]
  commands:
    - func: "install pint"
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        script: |
          set -e
          # Extract only the `groups` section into a temp file
          python3 - <<'EOF'
          import yaml, sys
          input_file = ".infra/alerts/mms-atlas-clusters-security-alerts/alerts-prod.yml"
          output_file = ".infra/alerts/mms-atlas-clusters-security-alerts/alerts-prod-only-groups.yml"
          with open(input_file, "r") as f:
              data = yaml.safe_load(f)
          groups_only = {}
          if "prometheusRule" in data and "groups" in data["prometheusRule"]:
              groups_only["groups"] = data["prometheusRule"]["groups"]
          with open(output_file, "w") as f:
              yaml.dump(groups_only, f)
          print(f"Wrote extracted groups to {output_file}")
          EOF

          # Now lint only the groups subsection
          ${workdir}/pint/bin/pint --offline lint test rules .infra/alerts/mms-atlas-clusters-security-alerts/alerts-prod-only-groups.yml

- name: PROM_ALERT_UNIT_TESTS
  tags: ["prom_alerts", "assigned_to_jira_team_cloudp_atlas_clusters_platform_triage"]
  commands:
    - func: "install prometheus"
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        script: |
          set -e
          ${workdir}/prometheus/bin/promtool test rules \
            $(find .alerts -type f \( -name "*_test.yml" -o -path ".alerts/tests/*" \) -print)

- name: LINT_PULL_REQUEST_TITLE
  patch_only: true
  tags: ["code_health", "assigned_to_jira_team_devprod_services_integrations"]
  commands:
    - command: github.generate_token
      type: system
      params:
        expansion_name: gh_pr_oauth_token
        permissions:
          pull_requests: read
    - command: subprocess.exec
      params:
        command: "scripts/evergreen/lint_pull_request_title"
        working_dir: mms
        include_expansions_in_env: [github_org, github_repo, github_pr_number, gh_pr_oauth_token, requester]

- name: DETECT_DISALLOWED_LIBRARY_USAGE
  tags: ["code_health", "assigned_to_jira_team_devprod_services_integrations"]
  commands:
    - func: "run bazel command"
      vars:
        label: "//scripts/utils/disallowed_libraries:detect_disallowed_library_usage"
        args: "--enforce=pull-request"

- name: VERIFY_GAZELLE_GAZELLE_DOES_NOT_MODIFY_FILES
  tags: ["gazelle", "assigned_to_jira_team_devprod_build"]
  commands:
    - command: subprocess.exec
      params:
        working_dir: mms
        binary: "scripts/evergreen/check_gazelle_file_modification.sh"
        include_expansions_in_env: [workdir, BAZELISK_HOME]
        args: ["-g", "//scripts/gazelle:gazelle"]

- name: VERIFY_GAZELLE_UPDATE_GO_REPOS_DOES_NOT_MODIFY_FILES
  tags: ["gazelle", "assigned_to_jira_team_devprod_build"]
  commands:
    - command: subprocess.exec
      params:
        working_dir: mms
        binary: "scripts/evergreen/check_gazelle_file_modification.sh"
        include_expansions_in_env: [workdir, BAZELISK_HOME]
        args: ["-g", "//scripts/gazelle:update-go-repos"]

- name: UNIT_JS_TASK_GENERATOR
  tags: ["unit_js", "unit_js_uncached", "assigned_to_jira_team_cloudp_frontend_platform"]
  commands:
    - command: shell.exec
      params:
        working_dir: mms
        shell: bash
        env:
          NODEJS_HOME: ${workdir}/mms/nodejs
        include_expansions_in_env: [BAZELISK_HOME]
        script: |
          set -ex
          additional_args=()
          if [ "${run_uncached}" == "true" ]; then
            additional_args+=("--run_uncached")
          fi
          if [ "${unit_js_task_generator_targets}" != "" ]; then
            bazel run //client/packages/scripts/evergreen-generators:unit_js -- --tasks "${unit_js_task_generator_targets}" $additional_args
          else
            packages_to_test=$(bazel query 'filter(":js_unit_test$", tests(//client/... + //systems/...))' --output package)
            bazel run //client/packages/scripts/evergreen-generators:unit_js -- $packages_to_test $additional_args
          fi
    - command: s3.put
      params:
        bucket: mciuploads
        local_file: "mms/js_unit_test-tasks.json"
        remote_file: ${mciuploads_dir|mms}/${build_id}/${task_name}/${execution}/js_unit_test-tasks.json
        permissions: private
        visibility: signed
        content_type: ${content_type|application/json}
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
    - command: generate.tasks
      params:
        files:
          - mms/js_unit_test-tasks.json

- name: PACKAGE_CLOUD_CHANGE_MANAGEMENT
  run_on: [amazon2-cloud-small] # Comes with Docker preinstalled
  tags: ["assigned_to_jira_team_cloudp_core_xv"]
  priority: 100
  commands:
    - func: "prep docker ecr"
    - func: "assume packaging role"
    - func: "build docker image"
      vars:
        bazel_args: "--stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//python/src/change_management:tarball"
    - func: "early success if patch build"
    - func: "check if docker image already exists in ECR"
      vars:
        repository: "mms-operations" # Matches the container repository in //python/src/change_management/BUILD.bazel
    - func: "publish docker image"
      vars:
        bazel_target: "//python/src/change_management:push_image"
        bazel_args: "--stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"

- name: PACKAGE_CLOUD_CONFIG_RELEASE
  tags: ["assigned_to_jira_team_devprod_build", "required_for_cloud_deploy_dry_run"]
  commands:
    - func: "prep docker ecr"
    - command: shell.exec  # Remove as part of CLOUDP-54068
      params:
        working_dir: mms
        shell: bash
        script: |
          set -exu
          sed -e "s/^mms\.assets\.minified=.*/mms\.assets\.minified=false/" -i server/conf/conf-{dev,qa,stage,prod}*.properties
    - command: shell.exec
      params:
        working_dir: mms
        silent: true
        shell: bash
        include_expansions_in_env: [BAZELISK_HOME, SENTRY_AUTH_TOKEN, SENTRY_ORG, SENTRY_PROJECT]
        script: |
          # check current deploy step. if LAMEDUCK, PROD, QA, skip including source maps.
          echo checking deploy step
          if [ "${deploy_step}" == "PROD" ] || [ "${deploy_step}" == "QA" ] || [ "${deploy_step}" == "LAMEDUCK" ]; then
            echo "build --//client/scripts/webpack:append_source_maps=false" >> "${workdir}/.bazelrc"
            set +x
            echo "{\"SENTRY_ORG\": \"${SENTRY_ORG}\", \"SENTRY_PROJECT\": \"${SENTRY_PROJECT}\", \"SENTRY_AUTH_TOKEN\": \"${SENTRY_AUTH_TOKEN}\"}" > "./client/scripts/webpack/customEnv.json"
            set -x
          fi
    - command: subprocess.exec
      params:
        working_dir: mms
        include_expansions_in_env: [BAZELISK_HOME]
        command: bazel build --config=release --build_env=tarball --execution_log_binary_file=${workdir}/tar_execution.log //server:package

- name: PACKAGE_CLOUD
  tags: ["assigned_to_jira_team_devprod_release_infrastructure", "required_for_cloud_deploy_gate"]
  priority: 100
  commands:
    - func: "prep docker ecr"
    - command: shell.exec  # Remove as part of CLOUDP-54068
      params:
        working_dir: mms
        shell: bash
        script: |
          set -exu
          sed -e "s/^mms\.assets\.minified=.*/mms\.assets\.minified=false/" -i server/conf/conf-{dev,qa,stage,prod}*.properties
    - command: shell.exec
      params:
        working_dir: mms
        silent: true
        shell: bash
        include_expansions_in_env: [BAZELISK_HOME, SENTRY_AUTH_TOKEN, SENTRY_ORG, SENTRY_PROJECT]
        script: |
          # check current deploy step. if LAMEDUCK, PROD, QA, skip including source maps.
          echo checking deploy step
          if [ "${deploy_step}" == "PROD" ] || [ "${deploy_step}" == "QA" ] || [ "${deploy_step}" == "LAMEDUCK" ]; then
            echo "build --//client/scripts/webpack:append_source_maps=false" >> "${workdir}/.bazelrc"
            set +x
            echo "{\"SENTRY_ORG\": \"${SENTRY_ORG}\", \"SENTRY_PROJECT\": \"${SENTRY_PROJECT}\", \"SENTRY_AUTH_TOKEN\": \"${SENTRY_AUTH_TOKEN}\"}" > "./client/scripts/webpack/customEnv.json"
            set -x
          fi
    - command: shell.exec
      params:
        working_dir: mms
        silent: true
        shell: bash
        include_expansions_in_env: [BAZELISK_HOME, branch_name]
        script: |
          regexp='v(19|20)([0-9]{2})(([0][0-9])|([1][0-2]))(([0-2][0-9])|([3][0-1]))'
          if [[ "${branch_name}" =~  $regexp ]]; then
            bazel build --config=release --build_env=tarball --execution_log_binary_file=${workdir}/tar_execution.log //server:package
          else
            bazel build --build_env=tarball --execution_log_binary_file=${workdir}/tar_execution.log //server:package
          fi
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        script: |
          mkdir ${workdir}/mms/server/build/
          cp bazel-bin/server/package.tar.gz ${workdir}/mms/server/build/${github_commit}.tgz
    - func: sign_artifact
      vars:
        unsigned_artifact: ${workdir}/mms/server/build/${github_commit}.tgz
        detached_signature: ${workdir}/mms/server/build/${github_commit}.tgz.sig
        garasign_username: ${EVG_grs_config_cloudbuild_username}
        garasign_password: ${EVG_grs_config_cloudbuild_password}
#    - command: s3.put
#      params:
#        aws_key: ${mciuploads_aws_key}
#        aws_secret: ${mciuploads_aws_secret}
#        local_file: tar_execution.log
#        remote_file: ${mciuploads_dir|mms}/${task_id}/${execution}/tar_execution.log
#        bucket: mciuploads
#        permissions: private
#        visibility: signed
#        display_name: tar_execution
#        content_type: binary/octet-stream
    - func: "assume packaging role"
    - func: "run bazel command"
      vars:
        label: //scripts/evergreen:upload_frontend_javascript_assets
    - func: "run bazel command"
      vars:
        label: //scripts/evergreen:upload_frontend_css_assets
    - func: "run bazel command"
      vars:
        label: //scripts/evergreen:upload_frontend_thirdparty_css_assets
    - func: "run bazel command"
      vars:
        label: //scripts/evergreen:upload_frontend_image_assets
    - func: "run bazel command"
      vars:
        label: //scripts/evergreen:upload_frontend_font_assets
    - func: "run bazel command"
      vars:
        label: //scripts/evergreen:upload_frontend_html_assets
    - func: "run bazel command"
      vars:
        label: //scripts/evergreen:upload_additional_cdn_assets
    - command: s3.put
      params:
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
        local_file: mms/server/build/${github_commit}.tgz
        remote_file: ${mciuploads_dir|mms}/bazel/${github_commit}.tgz
        bucket: mciuploads
        permissions: private
        visibility: signed
        content_type: application/x-gzip
        display_name: package.tgz
    - func: "run bazel command"
      vars:
        label: "//scripts/evergreen:upload_cloud_package"
        should_upload_marker: "false"
        target_path: "bazel/"
    - func: "build docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-docker-ecr"
    - func: "build docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-mongodb-tools-docker-ecr"
    - func: "build docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-mongosync-v1.9.0-ecr"
    - func: "build docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-mongosync-experimental-ecr"
    - func: "build docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-mongosync-ecr"
    - func: "build docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-mongomirror-ecr"
    - func: "early success if patch build"
    - func: "check if docker image already exists in ECR"
      vars:
        repository: "mms"
    - func: "publish docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-docker-ecr"
    - func: "publish docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-mongodb-tools-docker-ecr"
    - func: "publish docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-mongosync-ecr"
    - func: "publish docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-mongosync-v1.9.0-ecr"
    - func: "publish docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-mongosync-experimental-ecr"
    - func: "publish docker image"
      vars:
        bazel_args: "--build_env=tarball --stamp --explain=${workdir}/rebuild_reasons.txt --verbose_explanations --execution_log_binary_file=${workdir}/docker_execution.log"
        bazel_target: "//server/mms_packaging:mms-mongomirror-ecr"
    - func: "scan docker image"
      vars:
        repository: "mms"
        bazel_args: "--stamp"
    - command: s3.put
      params:
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
        local_file: rebuild_reasons.txt
        remote_file: ${mciuploads_dir|mms}/${task_id}/${execution}/rebuild_reasons.txt
        bucket: mciuploads
        permissions: private
        visibility: signed
        display_name: rebuild_reasons
        content_type: text/plain
#    - command: s3.put
#      params:
#        aws_key: ${mciuploads_aws_key}
#        aws_secret: ${mciuploads_aws_secret}
#        local_file: docker_execution.log
#        remote_file: ${mciuploads_dir|mms}/${task_id}/${execution}/docker_execution.log
#        bucket: mciuploads
#        permissions: private
#        visibility: signed
#        display_name: docker_execution
#        content_type: binary/octet-stream
    # attach links to the EVG UI
    - command: attach.artifacts
      params:
        working_dir: mms/server
        optional: true
        files:
          - attach_artifacts.json

- name: PACKAGE_CLOUD_DEPLOY_GATE
  tags: ["assigned_to_jira_team_devprod_release_infrastructure"]
  priority: 100
  depends_on:
    - name: CHECK_PROM_ALERT
      variant: code_health
    - name: IDEMPOTENT_MIGRATIONS
      variant: code_health
    - name: VALIDATE_PNPM_LOCK_YAML
      variant: code_health
    - name: CHECK_BAZEL_DEPS
      variant: bazel_linux_x86_64
    - name: ".required_for_cloud_deploy_gate"
      variant: package_rpm
  commands:
    - func: "fetch source full clone"
    - command: ec2.assume_role
      params:
        role_arn: ${deploy_gate_role_arn}
    - command: subprocess.exec  # doesn't use "run bazel command" because expansions don't nest and marker_file includes ${branch_name}
      params:
        working_dir: mms
        command: "bazel run //scripts/evergreen:upload_cloud_package -- origin/${branch_name}${marker_file_suffix}"
        include_expansions_in_env: [is_patch, project, task_name, branch_name, github_commit,
            deploy_step, should_upload_marker, target_path, jdk_version,
            AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_SESSION_TOKEN]

- name: COMPILE_BAZEL
  tags: ["code_health", "assigned_to_jira_team_devprod_build", "foliage_creates_breaking_commit"]
  exec_timeout_secs: 900
  commands:
    - command: subprocess.exec
      retry_on_failure: true
      params:
        working_dir: mms
        include_expansions_in_env: *compile_bazel_env_expansions
        binary: scripts/evergreen/compile_bazel
        args:
          - --
          - //server:mms_skip_assets
          - //server/src/unit/...
          - //server/src/test/...
          - //server/src/features/...
          - //server/src/main/com/xgen/cloud/...
    # commented out because it's slow for large sets of tests and is mostly only useful for troubleshooting,
#    - command: s3.put
#      params:
#        bucket: mciuploads
#        local_file: "execution.log"
#        remote_file: "${mciuploads_dir|mms}/${task_id}/${execution}/execution.log"
#        permissions: private
#        visibility: signed
#        content_type: text/plain
#        aws_key: ${mciuploads_aws_key}
#        aws_secret: ${mciuploads_aws_secret}
    - command: s3.put
      params:
        local_file: "jvm.out"
        bucket: "mciuploads"
        remote_file: "${mciuploads_dir|mms}/${task_id}/${execution}/jvm.out"
        display_name: "jvm.out"
        permissions: private
        visibility: signed
        content_type: text/plain
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
        optional: true
    - command: s3.put
      params:
        bucket: mciuploads
        local_file: "memory.log"
        remote_file: "${mciuploads_dir|mms}/${task_id}/${execution}/memory.log"
        permissions: private
        visibility: signed
        content_type: text/plain
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
        optional: true
    - func: "attach bazel profiling data"

- name: COMPILE_CLIENT_BAZEL
  tags: ["code_health", "assigned_to_jira_team_cloudp_frontend_platform", "foliage_creates_breaking_commit"]
  exec_timeout_secs: 900
  commands:
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        script: |
          echo "For help with debugging this task, see the following wiki:"
          echo "https://wiki.corp.mongodb.com/display/MMS/Bazel+HOWTO#BazelHOWTO-ClientBuild"
    - command: subprocess.exec
      retry_on_failure: true
      params:
        working_dir: mms
        include_expansions_in_env: *compile_bazel_env_expansions
        binary: scripts/evergreen/compile_bazel
        args:
          # since this is run in code_health, ensure we get no chunks larger than CloudFront's max size
          # for serving compressed files in production
          - --define=CLIENT_BUILD_ENVIRONMENT=production
          - --//client/scripts/webpack:append_source_maps=false
          - --
          - //server/src/webapp-mms:resources
#    - command: s3.put
#      params:
#        bucket: mciuploads
#        local_file: "execution.log"
#        remote_file: "${mciuploads_dir|mms}/${task_id}/${execution}/execution.log"
#        permissions: private
#        visibility: signed
#        content_type: text/plain
#        aws_key: ${mciuploads_aws_key}
#        aws_secret: ${mciuploads_aws_secret}
    - func: "attach bazel profiling data"

- name: BLOCK_COMMIT_TASK
  patch_only: true
  tags: ["code_health", "assigned_to_jira_team_devprod_services_integrations"]
  commands:
    - command: github.generate_token
      type: system
      params:
        expansion_name: gh_pr_oauth_token
        permissions:
          pull_requests: read
    - command: subprocess.exec
      params:
        command: "scripts/evergreen/block_commit_script"
        working_dir: mms
        include_expansions_in_env: [workdir, BAZELISK_HOME,
          github_org, github_repo, github_pr_number, github_head_branch, gh_pr_oauth_token,
          requester, block_commit_pr_number,
          foliage_should_block_merges, foliage_should_block_merges_for_teams
        ]
        env:
          JIRA_TOKEN: ${jira_token}

- name: PRE_COMMIT_HOOK
  tags: ["code_health_with_gh_annotations", "assigned_to_jira_team_devprod_services_integrations_cloudp"]
  run_on: [amazon2023-cloud-large]  # for system python >= 3.8 for pre-commit package >= 3.0.0
  commands:
    - func: "initialize evergreen api" # Needed for evergreen verify auth
    - command: subprocess.exec
      params:
        working_dir: mms
        include_expansions_in_env: [CI, BAZELISK_HOME, workdir, task_name, branch_name, project]
        env:
          MMS_HOME: ${workdir}/mms
          PRE_COMMIT_HOME: ${workdir}/pre-commit
          EVERGREEN_CONFIG: ${workdir}/.evergreen.yml  # used by our wrapper around evergreen validate in .pre-commit-config.yaml
          # DESCRIPTION OF HOOKS SKIPPED BY THIS TASK
          # legacy-python-lint: has own task plus is legacy
          # bazel-super-formatter: some issue with max command line length, plus it's slow for the whole repo and has its own task
          # maven-pin: requires a newer JDK, can get checked by its own task (rules_jvm_external should use toolchain JDK like rules_js does for node)
          # systems-common-maven-pin: requires a newer JDK, can get checked by its own task (rules_jvm_external should use toolchain JDK like rules_js does for node)
          # systems-prototypes-maven-pin: requires a newer JDK, can get checked by its own task (rules_jvm_external should use toolchain JDK like rules_js does for node)
          # systems-events-maven-pin: requires a newer JDK, can get checked by its own task (rules_jvm_external should use toolchain JDK like rules_js does for node)
          # systems-gazelle: this is run as part of a separate check inside systems and does not need to be run in evergreen.
          # check-drone-deployment: notices only
          # generate-sentry-planner-alerts: CLOUDP-205722 plus it's handled by its own task
          # check-disallowed-libraries: has own task that operates in a different mode
          # openapi-validations: requires git-diff, presumably covered by openapi variants
          SKIP: legacy-python-lint,bazel-super-formatter,maven-pin,systems-common-maven-pin,systems-prototypes-maven-pin,systems-events-maven-pin,systems-gazelle,check-drone-deployment,generate-sentry-planner-alerts,check-disallowed-libraries,openapi-validations,generate-internal-ui-grpc
        command: scripts/evergreen/pre_commit

- name: PRE_COMMIT_HOOK_WITH_NPM
  tags: ["code_health", "assigned_to_jira_team_cloudp_frontend_platform"]
  run_on: [amazon2023-cloud-large]  # for system python >= 3.8 for pre-commit package >= 3.0.0
  commands:
    - *fetch_nodejs
    - *run_pnpm_install  # We could probably limit this to just a few deps, but there isn't a function already written for that
    - command: subprocess.exec
      params:
        working_dir: mms
        include_expansions_in_env: [CI, BAZELISK_HOME, workdir, task_name, branch_name, project]
        add_to_path:
          - ${workdir}/mms/nodejs/bin
        env:
          MMS_HOME: ${workdir}/mms
          PRE_COMMIT_HOME: ${workdir}/pre-commit
          EVERGREEN_CONFIG: ${workdir}/.evergreen.yml  # used by our wrapper around evergreen validate in .pre-commit-config.yaml
        command: scripts/evergreen/pre_commit_only_one generate-internal-ui-grpc

- name: PUBLIC_JAVADOC_ENFORCER
  tags: [ "code_health", "assigned_to_jira_team_cloudp_atlas_clusters_platform_ii" ]
  run_on: [ amazon2023-cloud-large ]  # for system python >= 3.8 for pre-commit package >= 3.0.0
  commands:
    - func: "initialize evergreen api" # Needed for evergreen verify auth
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        include_expansions_in_env: [ CI, BAZELISK_HOME, workdir, task_name, branch_name, project ]
        env:
          MMS_HOME: ${workdir}/mms
          PRE_COMMIT_HOME: ${workdir}/pre-commit
          EVERGREEN_CONFIG: ${workdir}/.evergreen.yml  # used by our wrapper around evergreen validate in .pre-commit-config.yaml
        script: |
          set -exu
          bazel run //scripts/cloud_atlas_clusters_platform/coding_standards:bin -- --base-branch ${branch_name}

- name: MARKDOWN_CODEGEN_PR_CHECKS
  tags: [ "code_health", "assigned_to_jira_team_cloudp_atlas_availability_durability_i" ]
  run_on: [ amazon2023-cloud-large ]  # for system python >= 3.8 for pre-commit package >= 3.0.0
  commands:
    - func: "initialize evergreen api" # Needed for evergreen verify auth
    - command: shell.exec
      params:
        working_dir: mms
        shell: bash
        env:
          NODEJS_HOME: ${workdir}/mms/nodejs
        include_expansions_in_env: [ BAZELISK_HOME ]
        script: |
          set -eux
          bazel test $(bazel query 'tests(attr(tags, "markdown_codegen", //server/...))')

- name: INT_TEST_NDS_MODEL_VERIFICATION_BASE_BRANCH
  tags: [ "int_nds_model_verification", "assigned_to_jira_team_cloudp_atlas_clusters_platform_i" ]
  run_on: [ amazon2-cloud-large ]
  patch_only: true
  commands:
    - func: "install mongodb and tools"
    - func: "install mongosh"
    - func: "start mongod single node replicaset"
    - func: "wait for single node mongod replicaset"
    - func: "setup conf-test-secure for int"
    - func: "checkout to specific branch"
      vars:
        env: "local"
    - func: "nds model verification test execution"
      vars:
        bazel_target: "//server/src/test/com/xgen/cloud/nds/modelverification:NDSModelVerificationIntTests"
        bazel_test_filter: "com.xgen.cloud.nds.modelverification.NDSModelVerificationIntTests#test_serializeNDSModels_parameterized"
    - func: "checkout to specific branch"
      vars:
        env: "branch"
    - func: "nds model verification test execution"
      vars:
        bazel_target: "//server/src/test/com/xgen/cloud/nds/modelverification:NDSModelVerificationIntTests"
        bazel_test_filter: "com.xgen.cloud.nds.modelverification.NDSModelVerificationIntTests#test_deserializeNDSModels_parameterized"
        strict_check: "true"

- name: UNIT_MMS_CODEGEN_TESTS
  tags: [ "int_nds_model_verification", "assigned_to_jira_team_cloudp_atlas_availability_durability_i" ]
  run_on: [ amazon2-cloud-large ]
  commands:
    - command: shell.exec
      params:
        working_dir: mms
        shell: bash
        include_expansions_in_env: [ BAZELISK_HOME ]
        script: |
          set -eux
          # This test covers the diff_tests created within write_source_file rules
          bazel test $(bazel query 'kind("diff_test", //server/src/unit/...)')


# fixme: pull this into a separate file when limits allow
- name: authn-generate
  tags: ["assigned_to_jira_team_cloudp_iam_workload_identity"]
  commands:
    - command: subprocess.exec
      params:
        env:
          GOROOT: '/opt/golang/go1.25'
        working_dir: mms/scripts/cloud_iam/go
        include_expansions_in_env: [BAZELISK_HOME, branch_name, authn_e2e_tests]
        binary: '/opt/golang/go1.25/bin/go'
        args: ['run', 'github.com/10gen/mms/iam/cmd/authn/evergreen/generate']
    - command: generate.tasks
      params:
        files:
          - mms/scripts/cloud_iam/go/authn-tasks.json
    - command: s3.put
      params:
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
        local_file: "mms/scripts/cloud_iam/go/authn-tasks.json"
        remote_file: ${mciuploads_dir|mms}/${build_id}/${task_name}/${execution}/authn-tasks.json
        bucket: mciuploads
        permissions: private
        visibility: signed
        content_type: ${content_type|application/json}
        display_name: authn-tasks.json
        optional: true

- name: authz-generate
  tags: [ "assigned_to_jira_team_cloudp_iam_authorization" ]
  commands:
    - command: subprocess.exec
      params:
        env:
          GOROOT: '/opt/golang/go1.25'
        working_dir: mms/scripts/cloud_iam/go
        include_expansions_in_env: [ BAZELISK_HOME, branch_name ]
        binary: '/opt/golang/go1.25/bin/go'
        args: [ 'run', 'github.com/10gen/mms/iam/cmd/authz/evergreen/generate' ]
    - command: generate.tasks
      params:
        files:
          - mms/scripts/cloud_iam/go/authz-tasks.json
    - command: s3.put
      params:
        aws_key: ${mciuploads_aws_key}
        aws_secret: ${mciuploads_aws_secret}
        local_file: "mms/scripts/cloud_iam/go/authz-tasks.json"
        remote_file: ${mciuploads_dir|mms}/${build_id}/${task_name}/${execution}/authz-tasks.json
        bucket: mciuploads
        permissions: private
        visibility: signed
        content_type: ${content_type|application/json}
        display_name: authz-tasks.json
        optional: true

- name: PUBLISH_ALERT_DEPLOYMENT_VERSION_POINTERS
  tags: ["assigned_to_jira_team_devprod_release_infrastructure"]
  allowed_requesters: ["commit", "patch", "trigger"]
  commands:
    - *install_target_determinator
    - command: subprocess.exec
      params:
        binary: bash
        working_dir: mms
        args: [ "${workdir}/mms/scripts/evergreen/diff_bazel_targets.sh",
                "-w", "${workdir}", "-q", "kind(filegroup, //.infra/alerts/...)" ]
    - command: expansions.update
      params:
        file: ${workdir}/target_determinator_expansions.yml
    - command: shell.exec
      params:
        shell: bash
        working_dir: mms
        script: |
          set -exu
          mkdir -p "${workdir}/version-pointers"
          if [ "${continue}" = "true" ]; then
            for deployment in ${targets}; do
              echo "${github_commit}" > "${workdir}/version-pointers/$deployment"
            done
          fi
    - command: ec2.assume_role
      params:
        role_arn: ${deploy_gate_role_arn}
    - command: s3.put
      params:
        aws_key: ${AWS_ACCESS_KEY_ID}
        aws_secret: ${AWS_SECRET_ACCESS_KEY}
        aws_session_token: ${AWS_SESSION_TOKEN}
        local_files_include_filter:
          - version-pointers/*
        remote_file: versions/helm-values-deployments/${branch_name}/
        bucket: mongodb-mms-build-server
        permissions: private
        visibility: private
        content_type: text/plain
        display_name: version-pointer-

#
# this task is currently used as a placeholder for buildvariants that dynamically
#   receive tasks via 'generate.tasks' but the buildvariant must exist in evergreen
#   config to allow for path-filtering (every buildvariant must have at least one
#   defined task, otherwise it will fail validation).
#
- name: PHONY_TASK_REPORT_SUCCESS
  tags: ["assigned_to_jira_team_devprod_build"]
  commands:
    - *report_success_and_exit

################################################################################
# Run build performance tasks
################################################################################
- name: FETCH_NO_OP
  tags: ["build_perf", "assigned_to_jira_team_devprod_build"]
  commands:
  - func: "run bazel command"
    vars:
      bazel_command: "fetch"
      label: "//scripts/build_perf:shell_no_op"

- name: RUN_NO_OP
  tags: ["build_perf", "assigned_to_jira_team_devprod_build"]
  commands:
  - func: "run bazel command"
    vars:
      bazel_command: "run"
      label: "//scripts/build_perf:shell_no_op"

- name: RUN_NO_OP_TWICE
  tags: ["build_perf", "assigned_to_jira_team_devprod_build"]
  commands:
  - func: "run bazel command"
    vars:
      bazel_command: "run"
      label: "//scripts/build_perf:shell_no_op"
  - func: "run bazel command"
    vars:
      bazel_command: "run"
      label: "//scripts/build_perf:shell_no_op"

- name: RUN_NO_OP_TWICE_WITH_CHANGED_FILE
  tags: ["build_perf", "assigned_to_jira_team_devprod_build"]
  commands:
  - func: "run bazel command"
    vars:
      bazel_command: "run"
      label: "//scripts/build_perf:shell_no_op"
  - *update_placeholder
  - func: "run bazel command"
    vars:
      bazel_command: "run"
      label: "//scripts/build_perf:shell_no_op"
  - *revert_placeholder

################################################################################

pre:
  - func: "install AL2 packages and Chrome"
  - func: "set caching expansions"
  - func: "fetch source"
  - func: "install bazel"
  - func: "set artifactory creds"

post:
  - func: "post task"
  - func: "post machine"

timeout:
  - command: shell.exec
    params:
      working_dir: mms
      script: |
        set -ex

        # If running Javascript unit tests, collect information about why Node process is hung
        if [ '${build_variant}' = 'js' ] && [ -f client/js-test-runner.pid ]; then
          export NODEJS_HOME='${workdir}/mms/nodejs'
          export PATH="$NODEJS_HOME/bin:$PATH"

          cd client
          node scripts/wait-for-why-is-node-running.js
        fi
  - command: s3.put
    params:
      aws_key: ${mciuploads_aws_key}
      aws_secret: ${mciuploads_aws_secret}
      optional: true
      local_file: mms/client/why-is-node-running.log
      remote_file: ${mciuploads_dir|mms}/${build_id}/${task_name}/${execution}/why-is-node-running.log
      bucket: mciuploads
      permissions: private
      visibility: signed
      content_type: text/plain

task_groups:
  - name: int_external_provision_machine_move-group
    max_hosts: 2
    tasks:
      - INT_JAVA_EXTERNAL_AWS_PROVISION_MACHINE_MOVE
      - INT_JAVA_EXTERNAL_AZURE_PROVISION_MACHINE_MOVE
      - INT_JAVA_EXTERNAL_GCP_PROVISION_MACHINE_MOVE
    setup_group:
      - func: "install AL2 packages and Chrome"
      - func: "fetch source"
      - func: "install bazel"
      - func: "install mongodb and tools"
        vars:
          mongodb_version: "4.4.24"
          mongodb_arch: "x86_64-amazon2"
    setup_group_can_fail_task: true
    setup_task:
      - func: "generate cloning token"
      - func: "reset git credentials"
    setup_task_can_fail_task: true
    teardown_task:
      - func: "post task"
    teardown_group:
      - func: "post machine"

buildvariants:
- name: code_health
  tags: ["foliage_flaky_detection_enabled", "foliage_health"]
  display_name: 01. Code Health [code_health]
  run_on:
    - rhel80-cloud-large
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodBasePort: 26000
    mongodb_arch: "x86_64-rhel80"
  tasks:
    - name: ".code_health"
    - name: ".code_health_with_gh_annotations"
      create_check_run:
        path_to_outputs: "github_annotations.json"

- name: sentry
  tags: ["ensure_sentry_alerts"]
  display_name: z. Sentry Configurations
  cron: "@daily"
  run_on:
    - amazon2-cloud-small
  expansions:
    <<: [ *install_bazel_linux_x86_64_amd64 ]
  tasks:
    - ".ensure_sentry_alerts"

- name: unit_python
  tags: []
  display_name: 11. Unit Tests Python [unit_python]
  paths:
    - "operations/**/*.py"
    - "python/**/*.py"
    - "scripts/**/*.py"
    - "server/**/*.py"
  run_on:
    - amazon2023.0-cloud-medium
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - name: ".unit_python"
    - name: ".unit_python_with_gh_annotations"
      create_check_run:
        path_to_outputs: "github_annotations.json"

- name: unit_sh
  tags: []
  display_name: 12. Unit Tests Shell [unit_sh]
  paths:
    - "!systems/**"
  run_on:
    - amazon2023.0-cloud-medium
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - name: ".unit_sh"

- name: unit_go
  tags: ["assigned_to_jira_team_devprod_build"]
  display_name: 18. Unit Tests Go [unit_go]
  paths:
    - "go/**/*.go"
    - "server/**/*.go"
    - "scripts/**/*.go"
    - "go.mod"
    - "go.sum"
  run_on:
    - amazon2-cloud-small
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - ".unit_go"

- name: unit_proto
  display_name: 19. Unit Tests Proto [unit_proto]
  paths:
    - "server/**/*.proto"
  run_on:
    - amazon2-cloud-medium
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - ".unit_proto"

- name: unit_prometheus_alerts
  tags: []
  display_name: 14. Prometheus Alerting Unit Tests
  paths:
    - ".alerts/**/*.yml"
    - ".alerts/**/*.yaml"
  run_on:
    - amazon2-cloud-small
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - ".prom_alerts"

- name: atlas_search
  tags: ["assigned_to_jira_team_cloudp_search_web_platform" ]
  display_name: 17. Atlas Search Schema Health [atlas_search]
  paths:
    - "!systems/**"
  run_on:
    - amazon2023-cloud-small
  expansions:
    <<: [ *install_bazel_linux_x86_64_amd64 ]
  tasks:
    - ".atlas_search"

- name: e2e_local
  tags: []
  display_name: 06a. E2E Local [e2e_local]
  # 'paths' for monolith_e2e_test_path_filter should be kept in sync across these files:
  #  - .evergreen.yml
  #  - evergreen/apicontract_e2e.yml
  #  - evergreen/atlas_e2e.yml
  #  - evergreen/atlas_search_e2e.yml
  #  - evergreen/standalone-service.yml
  paths: &monolith_e2e_test_path_filter
   - "WORKSPACE"
   - "maven_install.json"
   - "**/.bazelrc"
   - "**/BUILD"
   - "**/BUILD.bazel"
   - "**/MODULE.bazel"
   - "bazelisk.cmd"
   - "bazelisk.py"
   - "bazelisk"
   - "client/**"
   - "go/**"
   - "server/**"
   - "scripts/**"
   - "third_party/**"
   - "tools/**"
   - "!server/conf/conf-internal*.properties"
   - "!server/conf/conf-qa*.properties"
   - "!server/conf/conf-stage*.properties"
   - "!server/conf/conf-prod*.properties"
   - "!server/src/main/com/xgen/cloud/services/**/resource/config/*-internal*.yaml"
   - "!server/src/main/com/xgen/cloud/services/**/resource/config/*-qa*.yaml"
   - "!server/src/main/com/xgen/cloud/services/**/resource/config/*-stage*.yaml"
   - "!server/src/main/com/xgen/cloud/services/**/resource/config/*-prod*.yaml"
   - "!systems/**"  # this path needs to be last (last eval wins)
  run_on:
    - amazon2-cloud-medium
  batchtime: 120 # 120 minutes
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodBasePort: 27017
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local"
    - Generate_E2E_Local_Tasks_By_Jira_Issue_Components

- name: e2e_local_required
  tags: []
  display_name: 06a-r. E2E Local Required [e2e_local_required]
  paths: *monolith_e2e_test_path_filter
  run_on:
    - amazon2-cloud-medium
  batchtime: 120
  expansions:
    <<: [ *install_bazel_linux_x86_64_amd64 ]
    mongodBasePort: 27017
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local_required"

- name: local_openapi_required # Tasks that run in PR and are blockers
  tags: [ "assigned_to_jira_team_cloudp_apix_platform" ]
  display_name: 05a. OpenAPI Required Checks [local_openapi_required]
  paths: &local_openapi_path_filter
    - "server/**"
    - "scripts/**"
  run_on:
    - amazon2-cloud-medium
  expansions:
    <<: [ *install_bazel_linux_x86_64_amd64, *openapi ]
    mongodBasePort: 27017
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_openapi_required"
    - name: ".e2e_openapi_required_with_gh_annotations"
      create_check_run:
        path_to_outputs: "openapi_github_annotations.json"

- name: local_openapi_pr_checks # Tasks that run in PRs, but aren't blockers
  tags: [ "assigned_to_jira_team_cloudp_apix_platform" ]
  display_name: 05b. OpenAPI PR Checks [local_openapi_pr_checks]
  paths: *local_openapi_path_filter
  run_on:
    - amazon2023-cloud-medium
  expansions:
    <<: [ *install_bazel_linux_x86_64_amd64, *openapi ]
  tasks:
    - ".e2e_openapi_pr_checks"
    - name: ".e2e_openapi_pr_checks_with_gh_annotations"
      create_check_run:
        path_to_outputs: "github_annotations.json"

- name: local_openapi #Tasks that run only in waterfall
  tags: ["assigned_to_jira_team_cloudp_apix_platform"]
  display_name: 05c. OpenAPI [local_openapi]
  paths: *local_openapi_path_filter
  run_on:
    - amazon2-cloud-medium
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64, *openapi]
    mongodBasePort: 27017
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - name: ".e2e_openapi"

- name: e2e_cypress_generator
  display_name: 06b-0. E2E Cypress Task Generator [e2e_cypress_generator]
  paths: *monolith_e2e_test_path_filter
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  run_on:
    - amazon2023-cloud-medium
  tasks:
    - E2E_CYPRESS_TASK_GENERATOR

- name: e2e_cypress_search_playground
  display_name: 06f. E2E Cypress Search Playground [e2e_cypress_search_playground]
  paths: *monolith_e2e_test_path_filter
  run_on:
    - amazon2-cloud-large
  tasks:
    - E2E_CYPRESS_SEARCH_PLAYGROUND
  expansions:
    <<: [ *install_bazel_linux_x86_64_amd64 ]

- name: e2e_local_remote_win
  display_name: g6a. E2E Local (Remote Windows) [e2e_local_remote_win]
  paths: *monolith_e2e_test_path_filter
  run_on:
    - amazon2-cloud-medium
  batchtime: 1440
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodBasePort: 27017
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local_remote_win"

- name: smoke_ppc_no_preinstall_bazel
  display_name: "j7a. ATM Smoke (Power/RHEL/8.1) [smoke_ppc_no_preinstall_bazel]"
  run_on:
    - rhel8-power-large
  batchtime: 10080 # 7 days
  expansions:
    agentVersion: "13.33.0.9423-1"
    agentArch: "rhel8_ppc64le"
    mongoDbFcv: "7.0"
  tasks: ".atm_smoke_test"

- name: smoke_rhel8_zseries_no_preinstall_bazel
  display_name: "j7b. ATM Smoke (zSeries/RHEL/8) [smoke_zseries_no_preinstall_bazel]"
  run_on:
    - rhel8-zseries-large
  batchtime: 10080 # 7 days
  expansions:
    agentVersion: "13.28.1.9307-1"
    agentArch: "rhel7_s390x"
    mongoDbFcv: "7.0"
  tasks: ".atm_smoke_test"

- name: smoke_rhel9_zseries_no_preinstall_bazel
  display_name: "j7c. ATM Smoke (zSeries/RHEL/9) [smoke_zseries_no_preinstall_bazel]"
  run_on:
    - rhel9-zseries-large
  batchtime: 10080 # 7 days
  expansions:
    agentVersion: "13.38.0.9609-1"
    agentArch: "rhel7_s390x"
    mongoDbFcv: "8.0"
  tasks: ".atm_smoke_test"

- name: e2e_local_old_atm_agent
  display_name: i7c. E2E Local (Old Automation Agent) [e2e_local_old_atm_agent]
  run_on:
    - amazon2-cloud-medium
  batchtime: 1440 # 1 day
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodBasePort: 27017
    automation_agent_version: "oldest"
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local_old_agent"

- name: e2e_local_mdb_latest
  display_name: h7a. E2E Local (MDB latest) [e2e_local_mdb_latest]
  paths: *monolith_e2e_test_path_filter
  run_on:
    - amazon2-cloud-medium
  batchtime: 1440 # 1 day
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodBasePort: 27017
    mongodb_version: "latest"
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local"

- name: e2e_cm_os_sanity
  display_name: "07a. E2E CM: Remote AA, MDB, backup, restore [e2e_cm_os_sanity]"
  paths: *monolith_e2e_test_path_filter
  run_on: amazon2-cloud-medium
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodBasePort: 27017
    mongodb_arch: "x86_64-amazon2"
  tasks : ".e2e_cm_os_sanity"

- name: e2e_local_with_daemon
  tags: []
  display_name: 06c-0. E2E Local w Daemon [e2e_local_with_daemon]
  paths: *monolith_e2e_test_path_filter
  run_on:
    - amazon2-cloud-medium-high-memory
  batchtime: 120 # 2 hours
  expansions:
    mongodBasePort: 27017
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local_with_daemon"

- name: e2e_local_with_daemon_nightly
  tags: []
  display_name: 06c-1. E2E Local w Daemon (Nightly) [e2e_local_with_daemon_nightly]
  run_on:
    - amazon2-cloud-large
  cron: "@daily"
  expansions:
    mongodBasePort: 27017
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local_with_daemon_nightly"

- name: e2e_local_with_daemon_third_party
  tags: []
  display_name: 06c-2. E2E Local w Daemon (Third-party) [e2e_local_with_daemon_third_party]
  run_on:
    - amazon2-cloud-large
  batchtime: 120 # 2 hours
  patch_only: true
  expansions:
    mongodBasePort: 27017
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local_with_daemon_third_party"

- name: e2e_local_with_daemon_config_shard
  tags: []
  display_name: 06c-3. E2E Local w Daemon with Config Shard [e2e_local_with_daemon_config_shard]
  run_on:
    - amazon2-cloud-large
  cron: "@daily"
  expansions:
    mongodBasePort: 27017
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local_with_daemon_config_shard"

- name: e2e_local_with_daemon_manual
  tags: []
  display_name: 06c-4. E2E Local w Daemon (Manual) [e2e_local_with_daemon_manual]
  run_on:
    - amazon2-cloud-large
  batchtime: 120 # 2 hours
  patch_only: true
  expansions:
    mongodBasePort: 27017
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local_with_daemon_manual"

- name: e2e_local_with_daemon_old_atm_agent
  display_name: i7c. E2E Local w Daemon (Old Automation Agent) [e2e_local_with_daemon_old_atm_agent]
  run_on:
    - amazon2-cloud-medium
  batchtime: 1440 # 1 day
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodBasePort: 27017
    automation_agent_version: "oldest"
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local_with_daemon_old_agent"

- name: e2e_local_with_daemon_mdb_latest
  display_name: h7c. E2E Local w Daemon (MDB latest) [e2e_local_with_daemon_mdb_latest]
  paths: *monolith_e2e_test_path_filter
  run_on:
    - amazon2-cloud-large
  batchtime: 1440 # 1 day
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodBasePort: 27017
    mongodb_version: "latest"
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_local_with_daemon"

- name: js
  tags: ["foliage_flaky_detection_enabled"]
  display_name: 03b. JavaScript Tests [js]
  paths:
    - "package.json"
    - "client/**"
  run_on:
    - amazon2023-cloud-medium
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - ".unit_js"
    - ".int_js"

- name: js_library_checks
  tags: []
  display_name: 03c. JavaScript Library Tests [js_library_checks]
  paths:
    - "!systems/**"
  run_on:
    - amazon2023-cloud-small
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - JS_LIBRARY_CHECKS

- name: js_uncached
  tags: ["foliage_flaky_detection_enabled"]
  batchtime: 240 # 4 hours
  deactivate_previous: false
  display_name: 03d. JavaScript Tests (Uncached) [js_uncached]
  run_on:
    - amazon2023-cloud-medium
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    run_uncached: true
  tasks:
    - ".unit_js_uncached"

- name: package_rpm
  display_name: 02. Packaging (RPM - RHEL8) [package_rpm]
  run_on: rhel80-cloud-xlarge
  tasks:
    - PACKAGE_CLOUD
    - PACKAGE_JAVA_SERVICES
    - PACKAGE_JS_SERVICES
    - PACKAGE_CONTAINERIZED_DEPENDENCY_SERVICES
    - PACKAGE_CLOUD_CHANGE_MANAGEMENT
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    jdk_version: 17
    ecr_repository: "mms/mms"
    bazel_target: "package_rpm"

- name: ssdlc
  display_name: zz. Compliance [ssdlc]
  run_on:
    - amazon2-cloud-small
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - PACKAGE_SILK_SBOM
    - UPLOAD_SBOM_TO_SILK
    - VERIFY_LOCAL_SBOM_UPDATED

- name: deploy_gate
  display_name: z. Cloud Manager Deployment Gating (prod) [deploy_gate]
  run_on:
    - rhel80-cloud-small
  cron: "*/15 13-23 * * mon-thu"
  tasks:
    - "PACKAGE_CLOUD_DEPLOY_GATE"
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    marker_file_suffix: "-green"
    jdk_version: 17

- name: deploy_gate_nonprod
  display_name: z. Cloud Manager Deployment Gating (dev/qa/stage) [deploy_gate_nonprod]
  run_on:
    - rhel80-cloud-small
  tasks:
    - "PACKAGE_CLOUD_DEPLOY_GATE"
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    marker_file_suffix: ""
    jdk_version: 17

- name: e2e_intel_nightly
  display_name: 15. E2E InTel Nightly [e2e_intel_nightly]
  run_on:
    - amazon2-cloud-medium
  cron: "@daily"
  stepback: false
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodBasePort: 27017
    mongodb_arch: "x86_64-amazon2"
  tasks:
    - ".e2e_intel_nightly"

- name: compass_generative_ai_accuracy_tests_nightly
  display_name: Nightly Generative AI Tests for Compass
  run_on:
    - amazon2-cloud-medium
  cron: "@daily"
  stepback: false
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
    mongodBasePort: 26000
    mongodb_arch: "x86_64-amazon2"
  tasks:
    # TODO: reenable in CLOUDP-289443
    # - ".compass_generative_ai_accuracy_tests_nightly"
    - ".compass_generative_ai_tests_nightly"

- name: frontend_code_analysis_nightly
  display_name: 16. Frontend Status Dashboard Update [frontend_code_analysis_nightly]
  run_on:
    - amazon2023-cloud-medium
  cron: "@daily"
  stepback: false
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - ".frontend_code_analysis_nightly"

- name: block_commit
  display_name: 99. Block Commit Preview
  run_on:
    - amazon2-cloud-small
  tasks:
    - "BLOCK_COMMIT_TASK"

- name: run_webpack_stats
  display_name: 20. Build Stats [run_webpack_stats]
  paths:
    - "!systems/**"
  run_on:
    - amazon2023-cloud-large
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - name: ".run_webpack_stats"
      create_check_run:
        path_to_outputs: "${workdir}/mms/check_outputs.json"

- name: int_nds_model_verification
  display_name: 22. Int NDS Model Verification Tests [int_nds_model_verification]
  paths:
    - "server/src/main/com/xgen/cloud/nds/**/*.java"
  run_on:
    - amazon2-cloud-large
  expansions:
    <<: [ *install_bazel_linux_x86_64_amd64 ]
    mongodb_arch: "x86_64-amazon2"
    mongodb_version: "latest"
    mongodBasePort: 26000
  tasks:
    - ".int_nds_model_verification"

# fixme: pull into external exclude when limits allow for it
- name: authn
  display_name: 'AuthN'
  paths:
    - "!systems/**"
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  run_on:
    - amazon2023-cloud-small
  tasks:
    - name: 'authn-generate'

- name: authz
  display_name: 'AuthZ'
  paths:
    - "!systems/**"
  expansions:
    <<: [ *install_bazel_linux_x86_64_amd64 ]
  run_on:
    - amazon2023-cloud-small
  tasks:
    - name: 'authz-generate'

- name: publish_alert_deployment_version_pointers
  display_name: Publish Alert Deployment Version Pointers
  run_on:
    - amazon2-cloud-medium
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - "PUBLISH_ALERT_DEPLOYMENT_VERSION_POINTERS"

- name: int_external_provision_machine_move
  display_name: "09. External Int Tests Provision Machine Moves AtlasD ACPlat2"
  run_on: amazon2-cloud-large
  stepback: false
  expansions:
    jdk_version: 17
    platform: "linux"
    arch: "x86_64"
    aojdk_arch: "x64"
    bazel_buildtools_version: "v7.1.2"
    mongodb_arch: "x86_64-amazon2"
    use_engflow_cache: "true"
    mms_build_arch: "amd64"
    mongodb_version: "4.4.24"
    mongodBasePort: 26000
  tasks:
    - name: "int_external_provision_machine_move-group"

################################################################################
# variants for build performance
################################################################################

- name: build_performance_variant
  display_name: "000. Tasks to capture build times"
  cron: "*/15 * * * *" # Every 15 minutes
  run_on: amazon2-cloud-small
  expansions:
    <<: [*install_bazel_linux_x86_64_amd64]
  tasks:
    - name: ".build_perf"
