package com.xgen.module.iam.config;

import static com.xgen.module.iam.config.IamAppSettings.IAM_DB_MAINTENANCE_ERROR;
import static com.xgen.module.iam.config.IamAppSettings.IAM_GCP_RECAPTCHA_API_KEY;
import static com.xgen.module.iam.config.IamAppSettings.IAM_GCP_RECAPTCHA_PROJECT_ID;
import static com.xgen.module.iam.config.IamAppSettings.IAM_GCP_RECAPTCHA_SITE_KEY;
import static com.xgen.module.iam.config.IamAppSettings.IAM_MFA_REMEMBER_DEVICE_ENABLED;
import static com.xgen.module.iam.config.IamAppSettings.IAM_MFA_REQUIRED_AFTER_REGISTRATION;
import static com.xgen.module.iam.config.IamAppSettings.IAM_MFA_REQUIRED_FOR_ALL_USERS;
import static com.xgen.module.iam.config.IamAppSettings.IAM_RECAPTCHA_ENTERPRISE_LOGIN_ENABLED;
import static com.xgen.module.iam.config.IamAppSettings.IAM_RECAPTCHA_EXTERNAL_REGISTRATION_ENABLED;
import static com.xgen.module.iam.config.IamAppSettings.IAM_RECAPTCHA_EXTERNAL_REGISTRATION_MIN_SCORE;
import static com.xgen.module.iam.config.IamAppSettings.IAM_RECAPTCHA_LINK_EXTERNAL_ACCOUNT_ENABLED;
import static com.xgen.module.iam.config.IamAppSettings.IAM_RECAPTCHA_LINK_EXTERNAL_ACCOUNT_MIN_SCORE;
import static com.xgen.module.iam.config.IamAppSettings.IAM_RECAPTCHA_LOGIN_MIN_SCORE;
import static com.xgen.module.iam.config.IamAppSettings.IAM_RECAPTCHA_REGISTRATION_ENABLED;
import static com.xgen.module.iam.config.IamAppSettings.IAM_RECAPTCHA_REGISTRATION_MIN_SCORE;
import static com.xgen.module.iam.config.IamAppSettings.PARTNER_VERCEL_NATIVE_ENABLED;
import static com.xgen.module.iam.config.IamAppSettings.PARTNER_VERCEL_NATIVE_OIP_CLIENT_ID;
import static com.xgen.module.iam.config.IamAppSettings.PARTNER_VERCEL_NATIVE_OIP_CLIENT_SECRET;
import static com.xgen.module.iam.config.IamAppSettings.PARTNER_VERCEL_NATIVE_OKTA_IDP;
import static com.xgen.module.iam.config.IamAppSettings.PARTNER_VERCEL_NATIVE_REDIRECT_DOMAIN;
import static com.xgen.module.iam.config.IamAppSettings.UI_AUTH_COOKIE_EXP;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class IamAppSettingsUnitTests {
  private AppSettings appSettings;

  @BeforeEach
  public void setup() throws Exception {
    appSettings = mock(AppSettings.class);
  }

  @Test
  public void testGetMmsAuthCookieExpirationSeconds() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    when(appSettings.getDoubleProp(Fields.SESSION_MAX_HOURS.value, UI_AUTH_COOKIE_EXP))
        .thenReturn(1.5);
    int oneAndHalfHourInSeconds = settings.getMmsAuthCookieExpirationSeconds();
    assertEquals((int) (1.5 * 60 * 60), oneAndHalfHourInSeconds);
  }

  @Test
  public void getIamGcpRecaptchaProjectId() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getStrProp(IAM_GCP_RECAPTCHA_PROJECT_ID),
        settings.getIamGcpRecaptchaProjectId());
  }

  @Test
  public void getIamGcpRecaptchaApiKey() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getStrProp(IAM_GCP_RECAPTCHA_API_KEY), settings.getIamGcpRecaptchaApiKey());
  }

  @Test
  public void testGetRecaptchaSiteKey() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getStrProp(IAM_GCP_RECAPTCHA_SITE_KEY), settings.getRecaptchaSiteKey());
  }

  @Test
  public void testGetRecaptchaLoginEnabled() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getBoolProp(IAM_RECAPTCHA_ENTERPRISE_LOGIN_ENABLED),
        settings.getRecaptchaEnterpriseLoginEnabled());
  }

  @Test
  public void testGetRecaptchaLoginMinScore() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getDoubleProp(IAM_RECAPTCHA_LOGIN_MIN_SCORE),
        settings.getRecaptchaLoginMinScore());
  }

  @Test
  public void testGetMFARequiredAfterRegistration() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getBoolProp(IAM_MFA_REQUIRED_AFTER_REGISTRATION),
        settings.getMFARequiredAfterRegistration());
  }

  @Test
  public void testGetMfaRequiredForAllUsers() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getBoolProp(IAM_MFA_REQUIRED_FOR_ALL_USERS),
        settings.getMfaRequiredForAllUsers());
  }

  @Test
  public void testGetMfaRememberDeviceEnabled() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getBoolProp(IAM_MFA_REMEMBER_DEVICE_ENABLED),
        settings.getMfaRememberDeviceEnabled());
  }

  @Test
  public void testGetRecaptchaRegistrationEnabled() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getBoolProp(IAM_RECAPTCHA_REGISTRATION_ENABLED),
        settings.getRecaptchaRegistrationEnabled());
  }

  @Test
  public void testGetRecaptchaRegistrationMinScore() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getDoubleProp(IAM_RECAPTCHA_REGISTRATION_MIN_SCORE),
        settings.getRecaptchaRegistrationMinScore());
  }

  @Test
  public void testGetRecaptchaExternalRegistrationEnabled() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getBoolProp(IAM_RECAPTCHA_EXTERNAL_REGISTRATION_ENABLED),
        settings.getRecaptchaExternalRegistrationEnabled());
  }

  @Test
  public void testGetRecaptchaExternalRegistrationMinScore() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getDoubleProp(IAM_RECAPTCHA_EXTERNAL_REGISTRATION_MIN_SCORE),
        settings.getRecaptchaExternalRegistrationMinScore());
  }

  @Test
  public void testGetRecaptchaLinkExternalAccountEnabled() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getBoolProp(IAM_RECAPTCHA_LINK_EXTERNAL_ACCOUNT_ENABLED),
        settings.getRecaptchaLinkExternalAccountEnabled());
  }

  @Test
  public void testGetRecaptchaLinkExternalAccountMinScore() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getDoubleProp(IAM_RECAPTCHA_LINK_EXTERNAL_ACCOUNT_MIN_SCORE),
        settings.getRecaptchaLinkExternalAccountMinScore());
  }

  @Test
  public void testGetAccountMultiFactorAuthSmsBackoffLevelsOffsetsAsMap_returnsCorrectValues() {
    // Given
    IamAppSettings settings = spy(new IamAppSettings(appSettings));

    // When
    Map<Integer, Set<String>> backoffLevelOffsetsMap =
        settings.getAccountMultiFactorAuthSmsBackoffLevelsOffsetsAsMap();

    // Then
    assertEquals(5, backoffLevelOffsetsMap.size());
    assertEquals(
        settings.getAccountMultiFactorAuthSmsBackoffLevelsOffsetByOneCountryCodes(),
        backoffLevelOffsetsMap.get(1));
    assertEquals(
        settings.getAccountMultiFactorAuthSmsBackoffLevelsOffsetByTwoCountryCodes(),
        backoffLevelOffsetsMap.get(2));
    assertEquals(
        settings.getAccountMultiFactorAuthSmsBackoffLevelsOffsetByThreeCountryCodes(),
        backoffLevelOffsetsMap.get(3));
    assertEquals(
        settings.getAccountMultiFactorAuthSmsBackoffLevelsOffsetByFourCountryCodes(),
        backoffLevelOffsetsMap.get(4));
    assertEquals(
        settings.getAccountMultiFactorAuthSmsBackoffLevelsOffsetByFiveCountryCodes(),
        backoffLevelOffsetsMap.get(5));
  }

  @Test
  public void testIsDbMaintenanceErrorEnabled() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getBoolProp(IAM_DB_MAINTENANCE_ERROR), settings.isDbMaintenanceErrorEnabled());
  }

  @Test
  public void testIsVercelNativeEnabled() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getBoolProp(PARTNER_VERCEL_NATIVE_ENABLED, false),
        settings.isVercelNativeEnabled());
  }

  @Test
  public void testGetVercelNativeOktaIdp() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getStrProp(PARTNER_VERCEL_NATIVE_OKTA_IDP, null),
        settings.getVercelNativeOktaIdp());
  }

  @Test
  public void testGetVercelNativeRedirectDomain() {
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    assertEquals(
        appSettings.getStrProp(PARTNER_VERCEL_NATIVE_REDIRECT_DOMAIN, "https://auth.mongodb.com"),
        settings.getVercelNativeRedirectDomain());
  }

  @Test
  public void
      testGetPartnerVercelNativeClientId_whenPropertyExists_returnsOpenInProviderClientId() {
    // Given
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    String expectedClientId = "test-client-id";
    when(appSettings.getStrProp(PARTNER_VERCEL_NATIVE_OIP_CLIENT_ID, ""))
        .thenReturn(expectedClientId);

    // When
    String result = settings.getPartnerVercelNativeOpenInProviderClientId();

    // Then
    assertEquals(expectedClientId, result);
  }

  @Test
  public void
      testGetPartnerVercelNativeOpenInProviderClientId_whenPropertyDoesNotExist_returnsEmptyString() {
    // Given
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    when(appSettings.getStrProp(PARTNER_VERCEL_NATIVE_OIP_CLIENT_ID, "")).thenReturn("");

    // When
    String result = settings.getPartnerVercelNativeOpenInProviderClientId();

    // Then
    assertEquals("", result);
  }

  @Test
  public void
      testGetPartnerVercelNativeClientSecret_whenPropertyExists_returnsOpenInProviderClientSecret() {
    // Given
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    String expectedClientSecret = "test-client-secret";
    when(appSettings.getStrProp(PARTNER_VERCEL_NATIVE_OIP_CLIENT_SECRET, ""))
        .thenReturn(expectedClientSecret);

    // When
    String result = settings.getPartnerVercelNativeOpenInProviderClientSecret();

    // Then
    assertEquals(expectedClientSecret, result);
  }

  @Test
  public void
      testGetPartnerVercelNativeOpenInProviderClientSecret_whenPropertyDoesNotExist_returnsEmptyString() {
    // Given
    IamAppSettings settings = spy(new IamAppSettings(appSettings));
    when(appSettings.getStrProp(PARTNER_VERCEL_NATIVE_OIP_CLIENT_SECRET, "")).thenReturn("");

    // When
    String result = settings.getPartnerVercelNativeOpenInProviderClientSecret();

    // Then
    assertEquals("", result);
  }
}
