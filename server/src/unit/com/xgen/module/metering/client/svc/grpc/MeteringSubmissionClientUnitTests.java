package com.xgen.module.metering.client.svc.grpc;

import static org.assertj.core.api.Assertions.assertThatException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import com.google.protobuf.Any;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.authn._public.svc.AuthnOAuthClient;
import com.xgen.metering.meterusages.v1.MeterSubmissionError;
import com.xgen.metering.meterusages.v1.SubmissionError;
import com.xgen.metering.meterusages.v1.SubmitMeterUsage;
import com.xgen.metering.meterusages.v1.SubmitMeterUsageRequest;
import com.xgen.metering.meterusages.v1.SubmitMeterUsageResponse;
import com.xgen.module.metering.client.svc.grpc.mapper.MeterUsageMapper;
import com.xgen.module.metering.common.exception.MeterErrorCode;
import com.xgen.module.metering.common.exception.MeterSvcException;
import com.xgen.module.metering.common.model.MeterUsage;
import io.grpc.Status.Code;
import io.grpc.protobuf.StatusProto;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class MeteringSubmissionClientUnitTests {

  public static final String INGESTION_JOB_NAME = "test";

  @Mock private AppSettings appSettings;

  @Mock private AuthnOAuthClient authnOAuthClient;
  @Mock private MeterUsageMapper meterUsageMapper;

  @Spy @InjectMocks private MeteringSubmissionClient meteringSubmissionClient;

  @ParameterizedTest
  @CsvSource(value = {"V1", "V2"})
  public void submitMeterUsage_V1(String version) throws MeterSvcException {

    var requestArgumentCaptor = ArgumentCaptor.forClass(SubmitMeterUsageRequest.class);
    var response = SubmitMeterUsageResponse.newBuilder().build();
    doReturn(response)
        .when(meteringSubmissionClient)
        .callSubmitMeterUsageGrpc(requestArgumentCaptor.capture());

    int batchSize = 10;
    when(appSettings.getIntProp(anyString(), anyInt())).thenReturn(batchSize);
    var meterUsagesMapping = mockMeterUsageMapping(batchSize * 2);

    boolean sessionBulkSave;
    if (version.equals("V1")) {
      sessionBulkSave = false;
      meteringSubmissionClient.submitMeterUsageV1(meterUsagesMapping.getLeft(), INGESTION_JOB_NAME);
    } else {
      sessionBulkSave = true;
      meteringSubmissionClient.submitMeterUsageV2(meterUsagesMapping.getLeft(), INGESTION_JOB_NAME);
    }

    var requests = requestArgumentCaptor.getAllValues();

    assertEquals(2, requests.size());

    var expectedSubmitMeterUsages = Lists.partition(meterUsagesMapping.getRight(), batchSize);

    assertEquals(expectedSubmitMeterUsages.get(0), requests.get(0).getMeterUsagesList());
    assertEquals(sessionBulkSave, requests.get(0).getSessionBulkSave());

    assertEquals(expectedSubmitMeterUsages.get(1), requests.get(1).getMeterUsagesList());
    assertEquals(sessionBulkSave, requests.get(1).getSessionBulkSave());
  }

  @Test
  public void submitMeterUsage_exception_handling() throws MeterSvcException {

    MeterSubmissionError meterSubmissionError =
        MeterSubmissionError.newBuilder()
            .setErrorCode(SubmissionError.SUBMISSION_ERROR_CONFLICTS_WITH_ROLLBACK)
            .setDescription("Conflicts found")
            .addDetails("Index 0 conflicting")
            .build();
    var status =
        com.google.rpc.Status.newBuilder()
            .setCode(Code.NOT_FOUND.value())
            .addDetails(Any.pack(meterSubmissionError))
            .build();

    when(appSettings.getIntProp(anyString(), anyInt())).thenReturn(10);

    doThrow(StatusProto.toStatusRuntimeException(status))
        .when(meteringSubmissionClient)
        .callSubmitMeterUsageGrpc(any());

    when(meterUsageMapper.mapToSubmitMeterUsage(any())).thenReturn(mock(SubmitMeterUsage.class));

    assertThatException()
        .isThrownBy(
            () ->
                meteringSubmissionClient.submitMeterUsageV2(
                    List.of(mock(MeterUsage.class)), INGESTION_JOB_NAME))
        .isInstanceOf(MeterSvcException.class)
        .matches(
            e -> {
              var meterSvcException = (MeterSvcException) e;
              return meterSvcException.getErrorCode() == MeterErrorCode.CONFLICTS_WITH_ROLLBACK
                  && !meterSvcException.getMessage().isEmpty()
                  && meterSvcException.getCause() != null
                  && meterSubmissionError
                      .getDetailsList()
                      .equals(meterSvcException.getMessageParams());
            });
  }

  private Pair<List<MeterUsage>, List<SubmitMeterUsage>> mockMeterUsageMapping(int size) {
    var meterUsages = new ArrayList<MeterUsage>();
    var submitMeterUsages = new ArrayList<SubmitMeterUsage>();
    IntStream.range(0, size)
        .forEach(
            i -> {
              MeterUsage meterUsage = mock(MeterUsage.class);
              SubmitMeterUsage submitMeterUsage = mock(SubmitMeterUsage.class);
              try {
                when(meterUsageMapper.mapToSubmitMeterUsage(meterUsage))
                    .thenReturn(submitMeterUsage);
              } catch (MeterSvcException e) {
                throw new RuntimeException(e);
              }
              meterUsages.add(meterUsage);
              submitMeterUsages.add(submitMeterUsage);
            });
    return Pair.of(meterUsages, submitMeterUsages);
  }
}
