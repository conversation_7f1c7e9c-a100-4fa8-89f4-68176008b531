load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*UnitTests.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/billingplatform/model/units/_public/model",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/authn",
        "//server/src/main/com/xgen/cloud/nds/aws/_public/model",
        "//server/src/main/com/xgen/cloud/nds/azure/_public/model",
        "//server/src/main/com/xgen/cloud/nds/gcp/_public/model",
        "//server/src/main/com/xgen/cloud/services/core/client/_public/impl",
        "//server/src/main/com/xgen/module/metering/client/svc",
        "//server/src/main/com/xgen/module/metering/client/svc/grpc",
        "//server/src/main/com/xgen/module/metering/client/svc/grpc/mapper",
        "//server/src/main/com/xgen/module/metering/common/exception",
        "//server/src/main/com/xgen/module/metering/common/model",
        "//server/src/main/com/xgen/module/metering/common/utils",
        "//systems/common/proto/com/xgen/common/bson/v1:com_xgen_common_bson_v1_java_library",
        "//systems/metering:meter_usages_grpc_v1",
        "//systems/metering:meter_usages_java_v1",
        "@com_google_protobuf//java/core",
        "@com_google_protobuf//java/util",
        "@io_grpc_grpc_java//api",
        "@io_grpc_grpc_java//protobuf",
        "@maven//:com_google_api_grpc_proto_google_common_protos",
        "@maven//:org_assertj_assertj_core",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_junit_jupiter_junit_jupiter_params",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)
