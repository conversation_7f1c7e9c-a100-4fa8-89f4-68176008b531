package com.xgen.cloud.authz_tools._public.tools;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

public class EndpointActionCoverageUnitTests {
  @Disabled("The test will be enabled during the EPAs rollout")
  @Test
  void allAuthorizedEndpointsInMmsHaveValidEndpointActions() {
    String[] args = {"--check", "--level=ERROR", ""};
    assertDoesNotThrow(
        () -> GenerateEndpointActionsTool.main(args),
        """
❌ Endpoint Action (EPA) validation failed: One or more MMS endpoints annotated with @UiCall or @RolesAllowed are missing valid @Auth annotations with Endpoint Actions.

RUN TO FIX AUTOMATICALLY: bazel run //server/src/main/com/xgen/cloud/authz_tools:generate_epas_and_cedar_schema

For more details, see: <TODO: CLOUDP-339308 Add link>
Reach out to #ask-atlas-iam in case of unexpected errors!

""");
  }
}
