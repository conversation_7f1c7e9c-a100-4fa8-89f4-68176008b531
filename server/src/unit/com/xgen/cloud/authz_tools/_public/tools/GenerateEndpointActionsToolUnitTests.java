package com.xgen.cloud.authz_tools._public.tools;

import static com.xgen.cloud.authz_tools._private.model.EndpointInfo.getMethodsWithEpaQualifyingUiCallOrRolesAllowedAnnotation;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.mockStatic;

import com.xgen.cloud.authz_tools._private.model.EndpointInfo;
import com.xgen.cloud.authz_tools._public.tools.fixtures.DuplicateEpaResource;
import com.xgen.cloud.authz_tools._public.tools.fixtures.MissingEpaValidRolesAllowedOnClassResource;
import com.xgen.cloud.authz_tools._public.tools.fixtures.MissingEpaValidRolesAllowedOnMethodResource;
import com.xgen.cloud.authz_tools._public.tools.fixtures.MissingEpaValidUiCallResource;
import com.xgen.cloud.authz_tools._public.tools.fixtures.UnexpectedEpaResource;
import com.xgen.cloud.authz_tools._public.tools.fixtures.ValidEpaResource;
import com.xgen.cloud.openrewrite._public.runner.RewriteRunner;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import nl.altindag.log.LogCaptor;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentMatcher;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

public class GenerateEndpointActionsToolUnitTests {
  private static final List<Method> qualifyingMethodsWithEpaErrors =
      List.of(
          MissingEpaValidRolesAllowedOnMethodResource.class.getDeclaredMethods()[0],
          MissingEpaValidUiCallResource.class.getDeclaredMethods()[0],
          MissingEpaValidRolesAllowedOnClassResource.class.getDeclaredMethods()[0],
          DuplicateEpaResource.class.getDeclaredMethods()[0],
          DuplicateEpaResource.class.getDeclaredMethods()[1],
          UnexpectedEpaResource.class.getDeclaredMethods()[0]);
  static final LogCaptor logCaptor = LogCaptor.forClass(GenerateEndpointActionsTool.class);

  @Test
  @SuppressWarnings("unchecked")
  public void test_fixMode_callsRewriteWithFilterOnFilesWithErrors() {
    // All methods with EPA errors need to be fixed
    Set<String> expectedFilesWithErrors =
        qualifyingMethodsWithEpaErrors.stream()
            .map(Method::getDeclaringClass)
            .map(Class::getSimpleName)
            .map(name -> name + ".java")
            .collect(Collectors.toSet());

    try (MockedStatic<EndpointInfo> endpointInfoMock =
        mockStatic(EndpointInfo.class, Mockito.CALLS_REAL_METHODS)) {
      // Mock this method to skip actual reflection scanning
      endpointInfoMock
          .when(
              () ->
                  EndpointInfo.getMethodsWithEpaQualifyingUiCallOrRolesAllowedAnnotation(
                      anyBoolean(), any(), any()))
          .thenReturn(qualifyingMethodsWithEpaErrors);

      try (MockedStatic<RewriteRunner> rewriteRunnerMock = mockStatic(RewriteRunner.class)) {
        // Run using --fix mode
        GenerateEndpointActionsTool.main(new String[] {"--fix"});

        ArgumentMatcher<String[]> hasRewriteAndExpectedFilteredFiles =
            args -> {
              List<String> argsList = Arrays.asList(args);
              boolean hasRewrite = argsList.contains("--rewrite");
              String filterArg =
                  argsList.stream()
                      .filter(arg -> arg.startsWith("--filter="))
                      .findFirst()
                      .orElse(null);
              boolean hasExpectedFilesInFilter = false;
              if (filterArg != null) {
                String filesPart = filterArg.substring("--filter=".length());
                List<String> actualFiles = Arrays.asList(filesPart.split(","));
                hasExpectedFilesInFilter =
                    new HashSet<>(actualFiles).equals(new HashSet<>(expectedFilesWithErrors));
              }
              return hasRewrite && hasExpectedFilesInFilter;
            };

        rewriteRunnerMock.verify(
            () ->
                RewriteRunner.runWithCLI(
                    argThat(hasRewriteAndExpectedFilteredFiles),
                    any(String[].class),
                    any(String[].class),
                    any(Supplier.class)),
            Mockito.times(1));
      }
    }
  }

  @Test
  public void test_checkMode_qualifyingMethodsDoNotHaveValidEpas() {
    try (MockedStatic<EndpointInfo> endpointInfoMock =
        mockStatic(EndpointInfo.class, Mockito.CALLS_REAL_METHODS)) {
      // Mock this method to skip actual reflection scanning
      // Actual reflection scanning is done in the method level test
      endpointInfoMock
          .when(
              () ->
                  getMethodsWithEpaQualifyingUiCallOrRolesAllowedAnnotation(
                      anyBoolean(), any(), any()))
          .thenReturn(qualifyingMethodsWithEpaErrors);

      List<String> filesWithErrors = GenerateEndpointActionsTool.check(false);
      Set<String> expectedFilesWithErrors =
          qualifyingMethodsWithEpaErrors.stream()
              .map(Method::getDeclaringClass)
              .map(Class::getSimpleName)
              .map(name -> name + ".java")
              .collect(Collectors.toSet());

      assertEquals(expectedFilesWithErrors, new HashSet<>(filesWithErrors));
      // Verify counts by checking the log output
      assertTrue(
          logCaptor.getInfoLogs().stream()
              .anyMatch(l -> l.contains("Total methods requiring EPAs: 6")));
      assertTrue(logCaptor.getInfoLogs().stream().anyMatch(l -> l.contains("Duplicate EPAs: 1")));
      assertTrue(logCaptor.getInfoLogs().stream().anyMatch(l -> l.contains("Unexpected EPAs: 2")));
      assertTrue(logCaptor.getInfoLogs().stream().anyMatch(l -> l.contains("Missing EPAs: 3")));
    }
  }

  @Test
  public void test_checkMode_qualifyingMethodsHaveValidEpas() {
    List<Method> qualifyingMethods = List.of(ValidEpaResource.class.getDeclaredMethods()[0]);

    try (MockedStatic<EndpointInfo> endpointInfoMock =
        mockStatic(EndpointInfo.class, Mockito.CALLS_REAL_METHODS)) {
      // Mock this method to skip actual reflection scanning
      // Actual reflection scanning is done in the method level test
      endpointInfoMock
          .when(
              () ->
                  getMethodsWithEpaQualifyingUiCallOrRolesAllowedAnnotation(
                      anyBoolean(), any(), any()))
          .thenReturn(qualifyingMethods);

      List<String> filesWithErrors = GenerateEndpointActionsTool.check(false);

      assertTrue(filesWithErrors.isEmpty());
      // Verify log output
      assertTrue(
          logCaptor.getInfoLogs().stream()
              .anyMatch(l -> l.contains("Total methods requiring EPAs: 1")));
      assertTrue(
          logCaptor.getInfoLogs().stream().anyMatch(l -> l.contains("✅ No duplicate EPAs found")));
      assertTrue(
          logCaptor.getInfoLogs().stream().anyMatch(l -> l.contains("✅ No unexpected EPAs found")));
      assertTrue(
          logCaptor.getInfoLogs().stream().anyMatch(l -> l.contains("✅ No missing EPAs found")));
    }
  }

  @Test
  public void test_checkMode_filesWithExcludedEndpointsAreSkipped() {
    // Test that files containing any excluded endpoints are completely skipped from javaFilesWithErrors
    // This prevents --fix from trying to process files with excluded endpoints
    List<Method> methodsWithErrors = List.of(
        MissingEpaValidRolesAllowedOnMethodResource.class.getDeclaredMethods()[0],
        MissingEpaValidUiCallResource.class.getDeclaredMethods()[0]);

    try (MockedStatic<EndpointInfo> endpointInfoMock =
        mockStatic(EndpointInfo.class, Mockito.CALLS_REAL_METHODS)) {
      endpointInfoMock
          .when(
              () ->
                  getMethodsWithEpaQualifyingUiCallOrRolesAllowedAnnotation(
                      anyBoolean(), any(), any()))
          .thenReturn(methodsWithErrors);

      List<String> filesWithErrors = GenerateEndpointActionsTool.check(false);

      // Since EXCLUDED_ENDPOINTS is currently empty, both files should have errors
      assertEquals(2, filesWithErrors.size());
      assertTrue(filesWithErrors.contains("MissingEpaValidUiCallResource.java"));
      assertTrue(filesWithErrors.contains("MissingEpaValidRolesAllowedOnMethodResource.java"));

      // Verify log output shows all missing EPAs as non-excluded
      assertTrue(
          logCaptor.getInfoLogs().stream()
              .anyMatch(l -> l.contains("Non-excluded methods missing @Auth (2):")));
      assertTrue(
          logCaptor.getInfoLogs().stream()
              .anyMatch(l -> l.contains("Missing EPAs: 2 (excluded: 0, non-excluded: 2)")));

      // When endpoints are added to EXCLUDED_ENDPOINTS, the corresponding files should be
      // completely excluded from javaFilesWithErrors to prevent --fix from processing them
    }
  }

  @Test
  public void test_addsAnnotationWithExpectedEndpointActionName() {
    verifyRewrite(
        """
        package com.xgen.cloud.example.runtime.res.api.api_2023_01_01;

        @Path("/class")
        public class ApiExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = RoleSet.GROUP_READ_ONLY)
          public void exampleMethod() {}
        }
        """,
        """
        package com.xgen.cloud.example.runtime.res.api.api_2023_01_01;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ApiExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = RoleSet.GROUP_READ_ONLY)
          @Auth(endpointAction = "epa.project.ApiExampleResource_2023_01_01.exampleMethod.GET")
          public void exampleMethod() {}
        }
        """);
  }

  @Test
  public void test_movesAnnotationToJustBeforeMethodIfExistsElsewhere() {
    verifyRewrite(
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @Auth(endpointAction = "epa.project.ExampleResource.exampleMethod.GET")
          @UiCall(roles = RoleSet.GROUP_READ_ONLY)
          public void exampleMethod() {}
        }
        """,
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = RoleSet.GROUP_READ_ONLY)
          @Auth(endpointAction = "epa.project.ExampleResource.exampleMethod.GET")
          public void exampleMethod() {}
        }
        """);
  }

  @Test
  public void test_handlesRolesAllowed() {
    verifyRewrite(
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ApiExampleResource {
          @GET
          @Path("/method")
          @RolesAllowed({RoleSet.GROUP_READ_ONLY})
          public void exampleMethod() {}
        }
        """,
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ApiExampleResource {
          @GET
          @Path("/method")
          @RolesAllowed({RoleSet.GROUP_READ_ONLY})
          @Auth(endpointAction = "epa.project.ApiExampleResource.exampleMethod.GET")
          public void exampleMethod() {}
        }
        """);
  }

  @Test
  public void test_handlesRolesAllowedOnClass() {
    verifyRewrite(
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        @RolesAllowed({RoleSet.ORG_READ_ONLY})
        public class ApiExampleResource {
          @GET
          @Path("/method")
          public void exampleMethod() {}
        }
        """,
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        @RolesAllowed({RoleSet.ORG_READ_ONLY})
        public class ApiExampleResource {
          @GET
          @Path("/method")
          @Auth(endpointAction = "epa.organization.ApiExampleResource.exampleMethod.GET")
          public void exampleMethod() {}
        }
        """);
  }

  @Test
  public void test_updatesAnnotationIfItHasSomeOtherValue() {
    verifyRewrite(
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = RoleSet.GROUP_READ_ONLY)
          @Auth(endpointAction = "epa.epa.global.something.else.old.format")
          public void exampleMethod() {}
        }
        """,
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = RoleSet.GROUP_READ_ONLY)
          @Auth(endpointAction = "epa.project.ExampleResource.exampleMethod.GET")
          public void exampleMethod() {}
        }
        """);
  }

  @ParameterizedTest
  @ValueSource(strings = {"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"})
  public void test_handlesAllHttpMethods(String method) {
    verifyRewrite(
        String.format(
            """
            package com.example;

            import com.xgen.cloud.common.access._public.annotation.Auth;

            @Path("/class")
            public class ExampleResource {
              @%s
              @Path("/method")
              @UiCall(roles = RoleSet.GROUP_READ_ONLY)
              public void exampleMethod() {}
            }
            """,
            method),
        String.format(
            """
            package com.example;

            import com.xgen.cloud.common.access._public.annotation.Auth;

            @Path("/class")
            public class ExampleResource {
              @%s
              @Path("/method")
              @UiCall(roles = RoleSet.GROUP_READ_ONLY)
              @Auth(endpointAction = "epa.project.ExampleResource.exampleMethod.%s")
              public void exampleMethod() {}
            }
            """,
            method, method));
  }

  @Test
  public void test_doesNotAddAnnotationOrImportIfAlreadyCorrect() {
    verifyRewriteUnchanged(
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = RoleSet.GROUP_READ_ONLY)
          @Auth(endpointAction = "epa.project.ExampleResource.exampleMethod.GET")
          public void exampleMethod() {}
        }
        """);
  }

  @Test
  public void test_ignoresIfNoHttpMethodPresent() {
    verifyRewriteUnchanged(
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @Path("/method")
          @UiCall(roles = RoleSet.GROUP_READ_ONLY)
          public void exampleMethod() {}
        }
        """);
  }

  @Test
  public void test_ignoresIfNoUiCallOrRolesAllowed() {
    verifyRewriteUnchanged(
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          public void exampleMethod() {}
        }
        """);
  }

  @Test
  public void test_usesGlobalResourceTypeForGlobalOnlyRoleSet() {
    verifyRewrite(
        """
        package com.example;
        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = RoleSet.GLOBAL_READ_ONLY)
          public void exampleMethod() {}
        }
        """,
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = RoleSet.GLOBAL_READ_ONLY)
          @Auth(endpointAction = "epa.global.ExampleResource.exampleMethod.GET")
          public void exampleMethod() {}
        }
        """);
  }

  @Test
  public void test_usesOrganizationResourceTypeForGlobalAndOrgRoleSet() {
    verifyRewrite(
        """
        package com.example;
        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = {RoleSet.GLOBAL_READ_ONLY, RoleSet.ORG_READ_ONLY})
          public void exampleMethod() {}
        }
        """,
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = {RoleSet.GLOBAL_READ_ONLY, RoleSet.ORG_READ_ONLY})
          @Auth(endpointAction = "epa.organization.ExampleResource.exampleMethod.GET")
          public void exampleMethod() {}
        }
        """);
  }

  @Test
  public void test_usesProjectResourceTypeForGlobalAndGroupRoleSet() {
    verifyRewrite(
        """
        package com.example;
        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = {RoleSet.GLOBAL_READ_ONLY, RoleSet.GROUP_READ_ONLY})
          public void exampleMethod() {}
        }
        """,
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = {RoleSet.GLOBAL_READ_ONLY, RoleSet.GROUP_READ_ONLY})
          @Auth(endpointAction = "epa.project.ExampleResource.exampleMethod.GET")
          public void exampleMethod() {}
        }
        """);
  }

  @Test
  public void test_usesGroupResourceForOrgAndGroupRoleSet() {
    verifyRewrite(
        """
        package com.example;
        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = {RoleSet.ORG_READ_ONLY, RoleSet.GROUP_READ_ONLY})
          public void exampleMethod() {}
        }
        """,
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @GET
          @Path("/method")
          @UiCall(roles = {RoleSet.ORG_READ_ONLY, RoleSet.GROUP_READ_ONLY})
          @Auth(endpointAction = "epa.project.ExampleResource.exampleMethod.GET")
          public void exampleMethod() {}
        }
        """);
  }

  @Test
  public void test_doesNotOverrideCustomerFacingActionIfPresent() {
    verifyRewriteUnchanged(
        """
package com.example;

import com.xgen.cloud.common.access._public.annotation.Auth;

@Path("/class")
public class ExampleResource {
  @GET
  @Path("/method")
  @UiCall(roles = RoleSet.GROUP_READ_ONLY)
  @Auth(endpointAction = value = "epa.project.ExampleResource.exampleMethod.GET", customerFacingAction = "example.cfa")
  public void exampleMethod() {}
}
""");
  }

  @Test
  public void test_doesNotAddEndpointActionIfEndpointIsUnauthenticated() {
    verifyRewriteUnchanged(
        """
package com.example;

@Path("/class")
public class ExampleResource {
  @GET
  @UiCall(auth = false)
  public void exampleMethod() {}
}
""");
  }

  @Test
  public void test_doesNotAddEndpointActionIfHasAnyAuthenticatedUserRoleSet() {
    verifyRewriteUnchanged(
        """
package com.example;

@Path("/class")
public class ApiExampleResource {
  @GET
  @RolesAllowed(RoleSet.NAME.ANY_AUTHENTICATED_USER)
  public void exampleMethod() {}
}
""");
    verifyRewriteUnchanged(
        """
package com.example;

@Path("/class")
public class ExampleResource {
  @GET
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER)
  public void exampleMethod() {}
}
""");
    verifyRewriteUnchanged(
        """
package com.example;

@Path("/class")
public class ExampleResource {
  @GET
  @UiCall(roles = {RoleSet.ANY_AUTHENTICATED_USER})
  public void exampleMethod() {}
}
""");
    verifyRewriteUnchanged(
        """
package com.example;

@Path("/class")
public class ApiExampleResource {
  @GET
  @RolesAllowed({RoleSet.NAME.ANY_AUTHENTICATED_USER})
  public void exampleMethod() {}
}
""");
  }

  @Test
  public void test_addsEndpointActionWithProjectResourceTypeIfNoAttributeUiCall() {
    verifyRewrite(
        """
        package com.example;

        @Path("/class")
        public class ExampleResource {
          @GET
          @UiCall
          public void exampleMethod() {}
        }
        """,
        """
        package com.example;

        import com.xgen.cloud.common.access._public.annotation.Auth;

        @Path("/class")
        public class ExampleResource {
          @GET
          @UiCall
          @Auth(endpointAction = "epa.project.ExampleResource.exampleMethod.GET")
          public void exampleMethod() {}
        }
        """);
  }

  private static void verifyRewrite(String input, String expectedOutput) {
    assertEquals(
        expectedOutput,
        RewriteRunner.runWithJavaString(input, GenerateEndpointActionsTool.getSupplier()));
  }

  private static void verifyRewriteUnchanged(String inputAndOutput) {
    verifyRewrite(inputAndOutput, inputAndOutput);
  }
}
