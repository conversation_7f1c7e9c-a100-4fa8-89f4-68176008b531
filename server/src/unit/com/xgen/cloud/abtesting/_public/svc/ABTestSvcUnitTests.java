package com.xgen.cloud.abtesting._public.svc;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.DUPLICATE_KEY_ERROR_CODE;
import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.LoggingEvent;
import ch.qos.logback.core.Appender;
import com.mongodb.MongoException;
import com.mongodb.MongoSocketOpenException;
import com.mongodb.MongoWriteException;
import com.mongodb.ServerAddress;
import com.mongodb.WriteError;
import com.xgen.cloud.abtesting._private.dao.ABTestDao;
import com.xgen.cloud.abtesting._private.dao.AssignmentDao;
import com.xgen.cloud.abtesting._private.dao.GhostAssignmentDao;
import com.xgen.cloud.abtesting._private.dao.OverrideAssignmentDao;
import com.xgen.cloud.abtesting._private.view.ApiBatchTestAssignmentEntityView;
import com.xgen.cloud.abtesting._public.model.ABTest;
import com.xgen.cloud.abtesting._public.model.ABTest.ABTestBuilder;
import com.xgen.cloud.abtesting._public.model.ABTest.ExperimentType;
import com.xgen.cloud.abtesting._public.model.Assignment;
import com.xgen.cloud.abtesting._public.model.AssignmentMetaData;
import com.xgen.cloud.abtesting._public.model.EntityForExperimentation;
import com.xgen.cloud.abtesting._public.model.GhostAssignment;
import com.xgen.cloud.abtesting._public.model.ModelNotReadyException;
import com.xgen.cloud.abtesting._public.model.OverrideAssignment;
import com.xgen.cloud.abtesting._public.model.OverrideAssignmentView;
import com.xgen.cloud.abtesting._public.model.TestGroup;
import com.xgen.cloud.abtesting._public.model.TestGroupFeatureFlag;
import com.xgen.cloud.abtesting._public.svc.ABTestSvc.AssignmentDataResponse;
import com.xgen.cloud.abtesting._public.svc.ABTestSvc.GenerateBatchAssignmentsResponse;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.entity._public.model.Entity;
import com.xgen.cloud.common.entity._public.model.EntityType;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.AdminABTestErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.externalanalytics._public.model.SegmentEvent;
import com.xgen.cloud.externalanalytics._public.model.SegmentEventAssignmentSource;
import com.xgen.cloud.externalanalytics._public.model.SegmentGroupEvent;
import com.xgen.cloud.externalanalytics._public.model.TestAssignmentAllocatedEvent;
import com.xgen.cloud.externalanalytics._public.model.TestAssignmentAllocatedEvent.EventName;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventJobSubmissionSvc;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.externalanalytics._public.util.SegmentEventUtil;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.ifrabtest._public.model.ExperimentStatus;
import com.xgen.cloud.ifrabtest._public.model.IFRRolloutConfig;
import com.xgen.cloud.ifrabtest._public.model.IFRRolloutConfig.FleetType;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.ListUtils;
import org.bson.BsonDocument;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.slf4j.LoggerFactory;

// remove this suppression after CLOUDP-176486 is resolved to deprecate the
// ABTestSvc#getCurrentAssignmentsForEntity method.
@SuppressWarnings("deprecation")
@MockitoSettings(strictness = Strictness.LENIENT)
public class ABTestSvcUnitTests {

  private static final ObjectId GROUP_ID_1 = ObjectId.get();
  private static final ObjectId GROUP_ID_2 = ObjectId.get();
  private static final ObjectId GROUP_ID_3 = ObjectId.get();
  public static final String ORGANIZATION_AB_TEST_ASSIGNED = "Organization AB Test Assigned";

  private MockedStatic<FeatureFlagSvc> mockedStaticFeatureFlagSvc;

  @Mock private UserSvc _userSvc;
  @Mock private GroupSvc _groupSvc;
  @Mock private OrganizationSvc _orgSvc;
  @Spy @InjectMocks private ABTestSvc _abTestSvc;
  @Mock private OverrideAssignmentDao _overrideAssignmentDao;
  @Mock private AssignmentDao _assignmentDao;
  @Mock private GhostAssignmentDao _ghostAssignmentDao;
  @Mock private ABTestDao _abTestDao;
  @Mock private AppSettings _appSettings;
  // new segment pipeline
  @Mock private SegmentEventSvc _segmentEventSvc;
  @Mock private SegmentEventUtil _segmentEventUtil;
  // old segment pipeline
  @Mock private SegmentEventJobSubmissionSvc _segmentEventJobSubmissionSvc;
  @Mock private CMABSvc _cmabSvc;
  @Mock private AuthzSvc _authzSvc;

  private AppUser _user;
  private Group _group;
  private Organization _org;

  final String _testName1 = "testName1";
  final String _testName2 = "testName2";
  final String _testName3 = "orgTest";
  final String _testName4 = "testName4";
  final String _testName5 = "testName5";
  final String _testName6 = "testName6";
  final String _testName7 = "testName7";
  final String _holdoutTestName1 = "holdout_test1";
  final String _holdoutTestName2 = "holdout_test2";
  final String _holdoutTestName3 = "holdout_complete";
  final String _tag1 = "_tagName1";
  final String _tag2 = "_tagName2";
  final String _tag3 = "_tagName3";
  final String _tag4 = "_tagName4";
  final String _holdoutTag = "GLOBAL_HOLDOUT";
  final String _testGroupName1 = "experimental-1";
  final String _testGroupName2 = "experimental-2";
  final String _testGroupName3 = "experimental-3";
  final String _testGroupName4 = "experimental-4";
  final String _testGroupName5 = "experimental-5";
  final String _testGroupName6 = "experimental-6";
  final String _testGroupName7 = "experimental-7";
  final String _holdoutTestControlName = "HOLDOUT_TEST_HOLDOUT";
  final String _holdoutTestVariantName = "HOLDOUT_TEST_EXPERIMENT_ELIGIBLE";
  final String fakeEmailAddress = "fakeEmailAddress";
  final String _allocationPoint = "FIRST_TIME_CLOUD_LOGIN";
  private ObjectId _testid1;
  private ObjectId _testid2;
  private ObjectId _testid3;
  private ObjectId _testid4;
  private ObjectId _testid5;
  private ObjectId _testid6;
  private ObjectId _testid7;
  private ObjectId _holdoutTestId1;
  private ObjectId _holdoutTestId2;
  private ObjectId _holdoutTestId3;
  private ObjectId _testGroupDatabaseId1;
  private ObjectId _testGroupDatabaseId2;
  private ObjectId _testGroupDatabaseId3;
  private ObjectId _testGroupDatabaseId4;
  private ObjectId _testGroupDatabaseId5;
  private ObjectId _testGroupDatabaseId6;
  private ObjectId _testGroupDatabaseId7;
  private ObjectId _holdoutTestGroupDatabaseId1;
  private ObjectId _holdoutTestGroupDatabaseId2;
  private ABTest test1;
  private ABTest test2;
  private ABTest test3;
  private ABTest test4;
  private ABTest test5;
  private ABTest test6;
  private ABTest launchedTest;
  private ABTest holdoutTestLive;
  private ABTest holdoutTestCooldown;
  private ABTest holdoutTestComplete;
  private Assignment assignment1;
  private Assignment assignment2;
  private Assignment assignment3;
  private Assignment assignment4;
  private GhostAssignment ghostAssignment1;
  private GhostAssignment ghostAssignment2;
  private GhostAssignment ghostAssignment3;
  private GhostAssignment ghostAssignmentOld;
  private GhostAssignment nullGhostAssignment;
  private Assignment holdoutLiveAssignment;
  private Assignment holdoutLiveAssignment2;
  private Assignment holdoutCompleteAssignment1;
  private Assignment holdoutCompleteAssignment2;
  private OverrideAssignment overrideAssignment1;
  private OverrideAssignment overrideAssignment2;
  private OverrideAssignment overrideAssignment3;
  private OverrideAssignment holdoutControlOverrideAssignment;
  private OverrideAssignment holdoutCompleteOverrideAssignment;
  private List<Assignment> _testAssignments;
  private List<ABTest> activeTests;
  private List<TestGroupFeatureFlag> featureFlags1;
  private List<TestGroupFeatureFlag> featureFlags2;
  private List<TestGroupFeatureFlag> featureFlags3;
  private List<TestGroup> defaultIfrTestGroups;

  @Mock private Appender<ILoggingEvent> mockAppender;
  @Captor private ArgumentCaptor<LoggingEvent> captorLoggingEvent;

  @BeforeEach
  public void setUp() {
    _testid1 = new ObjectId();
    _testid2 = new ObjectId();
    _testid3 = new ObjectId();
    _testid4 = new ObjectId();
    _testid5 = new ObjectId();
    _testid6 = new ObjectId();
    _testid7 = new ObjectId();
    _holdoutTestId1 = new ObjectId();
    _holdoutTestId2 = new ObjectId();
    _holdoutTestId3 = new ObjectId();

    _testGroupDatabaseId1 = new ObjectId();
    _testGroupDatabaseId2 = new ObjectId();
    _testGroupDatabaseId3 = new ObjectId();
    _testGroupDatabaseId4 = new ObjectId();
    _testGroupDatabaseId5 = new ObjectId();
    _testGroupDatabaseId6 = new ObjectId();
    _testGroupDatabaseId7 = new ObjectId();
    _holdoutTestGroupDatabaseId1 = new ObjectId();
    _holdoutTestGroupDatabaseId2 = new ObjectId();

    _user = mock(AppUser.class);
    doReturn(oid(1)).when(_user).getId();
    doReturn(EntityType.USER).when(_user).getEntityType();

    _group = mock(Group.class);
    doReturn(oid(2)).when(_group).getId();
    doReturn(EntityType.GROUP).when(_group).getEntityType();

    _org = mock(Organization.class);
    doReturn(oid(3)).when(_org).getId();
    doReturn(EntityType.ORG).when(_org).getEntityType();

    // prepopulate test assignment data
    assignment1 =
        Assignment.builder()
            .entityId(oid(1))
            .entityType(EntityType.USER)
            .testId(_testid1)
            .testGroupName(_testGroupName1)
            .testGroupDatabaseId(_testGroupDatabaseId1)
            .testName(_testName1)
            .tag(_tag1)
            .build();
    assignment2 =
        Assignment.builder()
            .entityId(oid(2))
            .entityType(EntityType.USER)
            .testId(_testid2)
            .testGroupName(_testGroupName2)
            .testGroupDatabaseId(_testGroupDatabaseId2)
            .testName(_testName2)
            .tag(_tag2)
            .build();
    assignment3 =
        Assignment.builder()
            .entityId(oid(3))
            .entityType(EntityType.ORG)
            .testId(_testid3)
            .testGroupName(_testGroupName3)
            .testGroupDatabaseId(_testGroupDatabaseId3)
            .testName(_testName3)
            .tag(_tag2)
            .build();
    assignment4 =
        Assignment.builder()
            .entityId(oid(3))
            .entityType(EntityType.ORG)
            .testId(_testid4)
            .testGroupName(null)
            .testGroupDatabaseId(null)
            .testName(_testName4)
            .tag(_tag2)
            .build();
    holdoutLiveAssignment =
        Assignment.builder()
            .entityId(oid(3))
            .entityType(EntityType.ORG)
            .testId(_holdoutTestId1)
            .testGroupName(_holdoutTestControlName)
            .testGroupDatabaseId(_holdoutTestGroupDatabaseId1)
            .testName(_holdoutTestName1)
            .tag(_holdoutTag)
            .build();
    holdoutLiveAssignment2 =
        Assignment.builder()
            .entityId(oid(4))
            .entityType(EntityType.ORG)
            .testId(_holdoutTestId1)
            .testGroupName(_holdoutTestVariantName)
            .testGroupDatabaseId(_holdoutTestGroupDatabaseId2)
            .testName(_holdoutTestName1)
            .tag(_holdoutTag)
            .build();
    holdoutCompleteAssignment1 =
        Assignment.builder()
            .entityId(oid(3))
            .entityType(EntityType.ORG)
            .testId(_holdoutTestId3)
            .testGroupName(_holdoutTestControlName)
            .testGroupDatabaseId(_holdoutTestGroupDatabaseId1)
            .testName(_holdoutTestName1)
            .tag(_holdoutTag)
            .build();
    holdoutCompleteAssignment2 =
        Assignment.builder()
            .entityId(oid(4))
            .entityType(EntityType.ORG)
            .testId(_holdoutTestId3)
            .testGroupName(_holdoutTestVariantName)
            .testGroupDatabaseId(_holdoutTestGroupDatabaseId2)
            .testName(_holdoutTestName1)
            .tag(_holdoutTag)
            .build();
    _testAssignments = List.of(assignment1, assignment2);

    ghostAssignment1 =
        GhostAssignment.builder()
            .entityId(oid(3))
            .entityType(EntityType.ORG)
            .testId(_testid3)
            .testGroupName(_testGroupName2)
            .testGroupDatabaseId(_testGroupDatabaseId2)
            .testName(_testName3)
            .tag(_tag2)
            .linkedHoldoutTestId(_holdoutTestId1)
            .linkedHoldoutTestName(_holdoutTestName1)
            .build();

    ghostAssignment2 =
        GhostAssignment.builder()
            .entityId(oid(3))
            .entityType(EntityType.ORG)
            .testId(_testid4)
            .testGroupName(_testGroupName3)
            .testGroupDatabaseId(_testGroupDatabaseId3)
            .testName(_testName4)
            .tag(_tag2)
            .linkedHoldoutTestId(_holdoutTestId2)
            .linkedHoldoutTestName(_holdoutTestName2)
            .build();

    ghostAssignment3 =
        GhostAssignment.builder()
            .entityId(oid(3))
            .entityType(EntityType.GROUP)
            .testId(_testid5)
            .testGroupName(_testGroupName3)
            .testGroupDatabaseId(_testGroupDatabaseId3)
            .testName(_testName5)
            .tag(_tag2)
            .linkedHoldoutTestId(_holdoutTestId1)
            .linkedHoldoutTestName(_holdoutTestName1)
            .build();

    ghostAssignmentOld =
        GhostAssignment.builder()
            .entityId(oid(4))
            .entityType(EntityType.ORG)
            .testId(_testid5)
            .testGroupName(_testGroupName3)
            .testGroupDatabaseId(_testGroupDatabaseId3)
            .testName(_testName5)
            .tag(_tag2)
            .linkedHoldoutTestId(_holdoutTestId3)
            .linkedHoldoutTestName(_holdoutTestName3)
            .build();

    nullGhostAssignment =
        GhostAssignment.builder()
            .entityId(oid(3))
            .entityType(EntityType.ORG)
            .testId(_testid3)
            .testGroupName(null)
            .testGroupDatabaseId(null)
            .testName(_testName3)
            .tag(_tag2)
            .linkedHoldoutTestId(_holdoutTestId1)
            .linkedHoldoutTestName(_holdoutTestName1)
            .build();

    overrideAssignment1 =
        new OverrideAssignment.Builder()
            .entityType(EntityType.USER)
            .testId(_testid1)
            .testGroupId(_testGroupName2)
            .testGroupDatabaseId(_testGroupDatabaseId1)
            .testName(_testName1)
            .tag(_tag1)
            .emailAddress("fakeEmail1")
            .build();

    overrideAssignment2 =
        new OverrideAssignment.Builder()
            .entityType(EntityType.USER)
            .testId(_testid4)
            .testGroupId(_testGroupName3)
            .testGroupDatabaseId(_testGroupDatabaseId3)
            .testName(_testName4)
            .tag(_tag3)
            .emailAddress("fakeEmail2")
            .build();

    overrideAssignment3 =
        new OverrideAssignment.Builder()
            .entityType(EntityType.ORG)
            .testId(_testid3)
            .testGroupId(null)
            .testGroupDatabaseId(null)
            .testName(_testName3)
            .tag(_tag2)
            .emailAddress("fakeEmail3")
            .build();

    holdoutControlOverrideAssignment =
        new OverrideAssignment.Builder()
            .entityType(EntityType.ORG)
            .testId(_holdoutTestId1)
            .testGroupId(_holdoutTestControlName)
            .testGroupDatabaseId(_holdoutTestGroupDatabaseId1)
            .testName(_holdoutTestName1)
            .tag(_holdoutTag)
            .emailAddress("bestFakeEmail")
            .build();

    holdoutCompleteOverrideAssignment =
        new OverrideAssignment.Builder()
            .entityType(EntityType.ORG)
            .testId(_holdoutTestId3)
            .testGroupId(_holdoutTestControlName)
            .testGroupDatabaseId(_holdoutTestGroupDatabaseId1)
            .testName(_holdoutTestName3)
            .tag(_holdoutTag)
            .emailAddress("bestFakeEmail")
            .build();

    //    create some test objects
    featureFlags1 =
        List.of(
            new TestGroupFeatureFlag("flag-1", "value-1"),
            new TestGroupFeatureFlag("flag-2", "true"),
            new TestGroupFeatureFlag("flag-3", "false"));

    featureFlags2 =
        List.of(
            new TestGroupFeatureFlag("flag-4", "value-1"),
            new TestGroupFeatureFlag("flag-5", "true"));

    featureFlags3 =
        List.of(
            new TestGroupFeatureFlag("flag-4", "value-2"),
            new TestGroupFeatureFlag("flag-5", "false"));

    test1 =
        ABTest.builder()
            .id(_testid1)
            .testName(_testName1)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 50, true, null),
                    new TestGroup(
                        _testGroupName1, _testGroupDatabaseId1, 50, featureFlags1, false, null)))
            .description("desc1")
            .scope(EntityType.USER)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag1)
            .allocationPoints(List.of(_allocationPoint))
            .experimentStatus(ExperimentStatus.COOLDOWN)
            .build();

    test2 =
        ABTest.builder()
            .id(_testid2)
            .testName(_testName2)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 50, true, null),
                    new TestGroup(
                        _testGroupName2, _testGroupDatabaseId2, 50, featureFlags2, false, null)))
            .description("desc2")
            .scope(EntityType.USER)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag2)
            .experimentStatus(ExperimentStatus.LIVE)
            .build();

    test3 =
        ABTest.builder()
            .id(_testid3)
            .testName(_testName3)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 50, true, false),
                    new TestGroup(
                        _testGroupName3, _testGroupDatabaseId3, 50, featureFlags3, false, true)))
            .description("desc3")
            .scope(EntityType.ORG)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag2)
            .experimentStatus(ExperimentStatus.LIVE)
            .build();
    test4 =
        ABTest.builder()
            .id(_testid4)
            .testName(_testName4)
            .experimentType(ExperimentType.AB)
            .experimentStatus(ExperimentStatus.COMPLETE)
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 50, true, null),
                    new TestGroup(
                        _testGroupName4, _testGroupDatabaseId4, 50, featureFlags3, false, null)))
            .description("desc4")
            .scope(EntityType.ORG)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag2)
            .build();

    test5 =
        ABTest.builder()
            .id(_testid5)
            .testName(_testName5)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 50, true, null),
                    new TestGroup(_testGroupName5, _testGroupDatabaseId5, 50, false, true)))
            .description("desc4")
            .scope(EntityType.ORG)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag2)
            .experimentStatus(ExperimentStatus.LIVE)
            .build();

    test6 =
        ABTest.builder()
            .id(_testid6)
            .testName(_testName6)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 50, true, null),
                    new TestGroup(_testGroupName6, _testGroupDatabaseId6, 50, false, null)))
            .description("desc4")
            .scope(EntityType.ORG)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag2)
            .experimentStatus(ExperimentStatus.COMPLETE)
            .build();

    launchedTest =
        ABTest.builder()
            .id(_testid7)
            .testName(_testName7)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 0, true, false),
                    new TestGroup(_testGroupName7, _testGroupDatabaseId7, 100, false, true)))
            .description("desc4")
            .scope(EntityType.ORG)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag2)
            .experimentStatus(ExperimentStatus.LAUNCHED)
            .build();

    holdoutTestLive =
        ABTest.builder()
            .id(_holdoutTestId1)
            .experimentType(ExperimentType.HOLDOUT)
            .experimentStatus(ExperimentStatus.LIVE)
            .testName(_holdoutTestName1)
            .testGroups(
                List.of(
                    new TestGroup(
                        _holdoutTestControlName, _holdoutTestGroupDatabaseId1, 5, true, null),
                    new TestGroup(
                        _holdoutTestVariantName, _holdoutTestGroupDatabaseId2, 95, false, null)))
            .description("holdout test 1")
            .scope(EntityType.ORG)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_holdoutTag)
            .build();

    holdoutTestCooldown =
        ABTest.builder()
            .id(_holdoutTestId2)
            .experimentType(ExperimentType.HOLDOUT)
            .experimentStatus(ExperimentStatus.COOLDOWN)
            .testName(_holdoutTestName2)
            .testGroups(
                List.of(
                    new TestGroup(
                        _holdoutTestControlName, _holdoutTestGroupDatabaseId1, 5, true, null),
                    new TestGroup(
                        _holdoutTestVariantName, _holdoutTestGroupDatabaseId2, 95, false, null)))
            .description("holdout test 2")
            .scope(EntityType.ORG)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_holdoutTag)
            .build();

    holdoutTestComplete =
        ABTest.builder()
            .id(_holdoutTestId3)
            .experimentType(ExperimentType.HOLDOUT)
            .experimentStatus(ExperimentStatus.COMPLETE)
            .testName(_holdoutTestName3)
            .testGroups(
                List.of(
                    new TestGroup(
                        _holdoutTestControlName, _holdoutTestGroupDatabaseId1, 5, true, null),
                    new TestGroup(
                        _holdoutTestVariantName, _holdoutTestGroupDatabaseId2, 95, false, null)))
            .description("holdout test complete")
            .scope(EntityType.ORG)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_holdoutTag)
            .build();

    activeTests = new ArrayList<>(List.of(test1, test2, test3));
    final Logger logger = (Logger) LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME);
    logger.addAppender(mockAppender);

    // this is to stub out the check for the Holdouts FF
    mockedStaticFeatureFlagSvc = Mockito.mockStatic(FeatureFlagSvc.class);
    mockedStaticFeatureFlagSvc
        .when(
            () ->
                isFeatureFlagEnabled(
                    eq(FeatureFlag.ENABLE_EXPERIMENT_HOLDOUTS), any(), any(), any()))
        .thenReturn(true);

    doReturn(true).when(_appSettings).isGradualRolloutEnabledForAbTesting();

    defaultIfrTestGroups =
        List.of(
            new TestGroup("Control", new ObjectId("677431c6eb22f3000cedcff6"), 50, true, null),
            new TestGroup("Treatment", new ObjectId("677431c60790b0000ce0f778"), 50, false, null));
  }

  @AfterEach
  public void teardown() {
    final Logger logger = (Logger) LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME);
    logger.detachAppender(mockAppender);

    mockedStaticFeatureFlagSvc.close();
  }

  private Assignment getAssignmentControl() {
    return Assignment.builder()
        .testName(_testName1)
        .entityId(oid(1))
        .entityType(EntityType.USER)
        .testId(_testid1)
        .testGroupName("control")
        .tag(_tag1)
        .build();
  }

  private Assignment getAssignmentTest() {
    return Assignment.builder()
        .testName(_testName2)
        .entityId(oid(2))
        .entityType(EntityType.USER)
        .testId(_testid2)
        .testGroupName("experimental")
        .tag(_tag2)
        .build();
  }

  private Assignment getHoldoutAssignmentControl() {
    return Assignment.builder()
        .testName(_holdoutTestName1)
        .entityId(oid(1))
        .entityType(EntityType.ORG)
        .testId(_holdoutTestId1)
        .testGroupName(_holdoutTestControlName)
        .testGroupDatabaseId(_holdoutTestGroupDatabaseId1)
        .tag(_holdoutTag)
        .assignmentDate(new Date())
        .build();
  }

  private Assignment getHoldoutAssignmentVariant() {
    return Assignment.builder()
        .testName(_holdoutTestName2)
        .entityId(oid(1))
        .entityType(EntityType.ORG)
        .testId(_holdoutTestId2)
        .testGroupName(_holdoutTestVariantName)
        .testGroupDatabaseId(_holdoutTestGroupDatabaseId2)
        .tag(_holdoutTag)
        .assignmentDate(new Date())
        .build();
  }

  @Test
  public void test_getAllAssignments_ignoringOverrides() {
    doReturn(List.of(assignment1))
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntities(any(), eq(null), eq(null));

    final List<Assignment> result = _abTestSvc.getAllAssignments(_user, _group, _org, false, null);
    assertEquals(1, result.size());
    assertEquals(_testGroupName1, result.get(0).getTestGroupName());
    verify(_abTestSvc, never())
        .checkAndMergeOverridesForAssignments(any(), any(), any(), any(), any(), any());
  }

  @Test
  public void test_getAllAssignments_includingOverrides() {
    doReturn(List.of(assignment1))
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntities(any(), eq(_user), eq(null));
    doReturn(List.of(overrideAssignment1)).when(_overrideAssignmentDao).findByUserId(any());

    final List<Assignment> result = _abTestSvc.getAllAssignments(_user, _group, _org, true, null);
    assertEquals(1, result.size());
    assertEquals(_testGroupName2, result.get(0).getTestGroupName());
    // is called once over all entity types
    verify(_abTestSvc, times(1))
        .checkAndMergeOverridesForAssignments(any(), any(), any(), any(), any(), any());
  }

  @Test
  public void test_getSpecifiedAssignments_ignoringRelevantOverrides() {
    final List<String> specifiedTestNames = List.of(_testName1);

    // this will call "getAllOrSpecificAssignments" and "getCurrentAndLaunchedAssignmentsForEntity"
    doReturn(List.of(assignment1, assignment2))
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntities(any(), eq(null), eq(null));

    final List<Assignment> result =
        _abTestSvc.getAssignments(_user, _group, _org, false, specifiedTestNames, null, null);

    assertEquals(1, result.size());
    assertEquals(_testGroupName1, result.get(0).getTestGroupName());
    verify(_abTestSvc, never())
        .checkAndMergeOverridesForAssignments(any(), any(), any(), any(), any(), any());
  }

  @Test
  public void test_getSpecifiedAssignments_includingRelevantOverrides() {
    final List<String> specifiedTestNames = List.of(_testName1);
    final ObjectId mockedUserId = new ObjectId();
    doReturn(mockedUserId).when(_user).getId();
    final ObjectId mockedGroupId = new ObjectId();
    doReturn(mockedGroupId).when(_group).getId();
    final ObjectId mockedOrgId = new ObjectId();
    doReturn(mockedOrgId).when(_org).getId();

    // this will call "getAllOrSpecificAssignments", "getCurrentAndLaunchedAssignmentsForEntity",
    // "checkAndMergeOverridesForAssignments", and "combineAssignmentsAndOverrides"
    doReturn(List.of(assignment1, assignment2))
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntities(any(), eq(_user), eq(null));
    doReturn(List.of(overrideAssignment1)).when(_overrideAssignmentDao).findByUserId(mockedUserId);

    final List<Assignment> result =
        _abTestSvc.getAssignments(_user, _group, _org, true, specifiedTestNames, null, null);

    assertEquals(1, result.size());
    assertEquals(_testGroupName2, result.get(0).getTestGroupName());
    // is called once over all entity types
    verify(_abTestSvc, times(1))
        .checkAndMergeOverridesForAssignments(any(), any(), any(), any(), any(), any());
  }

  @Test
  public void test_getSpecifiedAssignments_ignoringIrrelevantOverrides() {
    final List<String> specifiedTestNames = List.of(_testName1);
    final ObjectId mockedUserId = new ObjectId();
    doReturn(mockedUserId).when(_user).getId();
    final ObjectId mockedGroupId = new ObjectId();
    doReturn(mockedGroupId).when(_group).getId();
    final ObjectId mockedOrgId = new ObjectId();
    doReturn(mockedOrgId).when(_org).getId();

    // this will call "getAllOrSpecificAssignments", "getCurrentAndLaunchedAssignmentsForEntity",
    // "checkAndMergeOverridesForAssignments", and "combineAssignmentsAndOverrides"
    doReturn(List.of(assignment1, assignment2))
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntities(any(), eq(_user), eq(null));
    // this is an override for another test, so should not matter
    doReturn(List.of(overrideAssignment2)).when(_overrideAssignmentDao).findByUserId(mockedUserId);

    final List<Assignment> result =
        _abTestSvc.getAssignments(_user, _group, _org, true, specifiedTestNames, null, null);

    assertEquals(1, result.size());
    assertEquals(_testGroupName1, result.get(0).getTestGroupName());
    // is called once over all entity types
    verify(_abTestSvc, times(1))
        .checkAndMergeOverridesForAssignments(any(), any(), any(), any(), any(), any());
  }

  @Test
  public void test_getSpecificAssignmentsForEntityWithOverrides() {
    final EntityForExperimentation entity = EntityForExperimentation.fromAnotherEntity(_org);

    doReturn(List.of(assignment3))
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(entity, _user, null);

    doReturn(List.of(overrideAssignment3)).when(_abTestSvc).getOverrideAssignmentsForUserId(any());

    final List<Assignment> result =
        _abTestSvc.getSpecificAssignmentsForEntityWithOverrides(
            entity, List.of(_testName3), _user, null, null);

    assertEquals(1, result.size());

    // the override assignment should set the testGroupName to null since the original assignment
    // sets it to _testGroupName3
    assertNull(result.get(0).getTestGroupName());
  }

  @Test
  public void test_isInTestGroup() {
    doReturn(_testAssignments)
        .when(_abTestSvc)
        .getAssignments(_user, _group, _org, true, List.of(_testName2), null, null);

    // Failure case - invalid test name
    assertFalse(_abTestSvc.isInTestGroup(_user, _group, _org, "fakeTestName", "fakeTestGroupName"));

    // Failure case - mismatched test name and test group name
    assertFalse(_abTestSvc.isInTestGroup(_user, _group, _org, _testName2, _testGroupName1));

    // Success case
    assertTrue(_abTestSvc.isInTestGroup(_user, _group, _org, _testName2, _testGroupName2));
  }

  @Test
  public void test_checkAndMergeOverridesForAssignments() {
    doReturn(List.of(overrideAssignment1, overrideAssignment2))
        .when(_abTestSvc)
        .getOverrideAssignmentsForUserId(any());

    final List<Assignment> result =
        _abTestSvc.checkAndMergeOverridesForAssignments(
            _user, _testAssignments, null, _group.getId(), _org.getId(), null);

    assertEquals(3, result.size());
    assertEquals(overrideAssignment1.getTestName(), result.get(0).getTestName());
    assertEquals(overrideAssignment1.getTestGroupId(), result.get(0).getTestGroupName());
    assertEquals(assignment2.getTestName(), result.get(1).getTestName());
    assertEquals(assignment2.getTestGroupName(), result.get(1).getTestGroupName());
    assertEquals(overrideAssignment2.getTestName(), result.get(2).getTestName());
    assertEquals(overrideAssignment2.getTestGroupId(), result.get(2).getTestGroupName());
  }

  @Test
  public void test_combineAssignmentWithOverride() {
    final Assignment result =
        _abTestSvc.combineAssignmentWithOverride(assignment1, overrideAssignment1);
    assertEquals(overrideAssignment1.getTestGroupId(), result.getTestGroupName());
    assertEquals(overrideAssignment1.getTestName(), result.getTestName());
  }

  @Test
  public void test_combineAssignmentWithOverride_whenOverrideIsNull() {
    final Assignment result = _abTestSvc.combineAssignmentWithOverride(assignment1, null);
    assertEquals(assignment1.getTestGroupName(), result.getTestGroupName());
    assertEquals(assignment1.getTestName(), result.getTestName());
  }

  @Test
  public void test_createAssignmentFromOverrideForUserScopedTest() {
    final ObjectId mockedUserId = new ObjectId();
    doReturn(mockedUserId).when(_user).getId();

    final Assignment result =
        _abTestSvc.createAssignmentFromOverride(
            overrideAssignment1, _user.getId(), _group.getId(), _org.getId());
    assertEquals(mockedUserId, result.getEntityId());
    assertEquals(overrideAssignment1.getTestName(), result.getTestName());
    assertEquals(overrideAssignment1.getTestGroupId(), result.getTestGroupName());
  }

  @Test
  public void test_createAssignmentFromOverrideForGroupScopedTest() {
    final ObjectId mockedGroupId = new ObjectId();
    doReturn(mockedGroupId).when(_group).getId();

    final OverrideAssignment overrideAssignmentForTest2 =
        new OverrideAssignment.Builder()
            .userId(_user.getId())
            .testGroupId(_testGroupName2)
            .testGroupDatabaseId(_testGroupDatabaseId2)
            .testName(_testName2)
            .testId(_testid2)
            .entityType(EntityType.GROUP)
            .build();

    final Assignment result =
        _abTestSvc.createAssignmentFromOverride(
            overrideAssignmentForTest2, _user.getId(), _group.getId(), _org.getId());
    assertEquals(mockedGroupId, result.getEntityId());
    assertEquals(overrideAssignmentForTest2.getTestName(), result.getTestName());
    assertEquals(overrideAssignmentForTest2.getTestGroupId(), result.getTestGroupName());
  }

  @Test
  public void test_createAssignmentFromOverrideForOrgScopedTest() {
    final ObjectId mockedOrgId = new ObjectId();
    doReturn(mockedOrgId).when(_org).getId();

    final OverrideAssignment overrideAssignmentForTest2 =
        new OverrideAssignment.Builder()
            .userId(_user.getId())
            .testGroupId(_testGroupName2)
            .testGroupDatabaseId(_testGroupDatabaseId2)
            .testName(_testName2)
            .testId(_testid2)
            .entityType(EntityType.ORG)
            .build();

    final Assignment result =
        _abTestSvc.createAssignmentFromOverride(
            overrideAssignmentForTest2, _user.getId(), _group.getId(), _org.getId());
    assertEquals(mockedOrgId, result.getEntityId());
    assertEquals(overrideAssignmentForTest2.getTestName(), result.getTestName());
    assertEquals(overrideAssignmentForTest2.getTestGroupId(), result.getTestGroupName());
  }

  @Test
  public void test_ensureAllAssignments_withNoOverrides() {
    final List<String> testNames = List.of(_testName3, _testName4);
    final List<Assignment> expectedAssignments = List.of(assignment3, assignment4);
    doReturn(List.of()).when(_abTestSvc).ensureAssignmentsForEntity(_user, testNames, _user, null);
    doReturn(List.of()).when(_abTestSvc).ensureAssignmentsForEntity(_group, testNames, _user, null);
    doReturn(expectedAssignments)
        .when(_abTestSvc)
        .ensureAssignmentsForEntity(_org, testNames, _user, null);
    doReturn(List.of()).when(_overrideAssignmentDao).findByUserId(any());

    final List<Assignment> result =
        _abTestSvc.ensureAllAssignments(_user, _group, _org, testNames, true, null);
    assertEquals(expectedAssignments, result);
  }

  @Test
  public void test_ensureAssignmentsForEntityWithOverrides() {
    final List<String> testNames = List.of(_testName3, _testName4);
    final List<Assignment> realAssignments = List.of(assignment3, assignment4);
    final OverrideAssignment overrideAssignmentForTest3 =
        new OverrideAssignment.Builder()
            .userId(_user.getId())
            .testGroupId(_testGroupName2)
            .testGroupDatabaseId(_testGroupDatabaseId2)
            .testName(_testName3)
            .testId(_testid3)
            .entityType(EntityType.ORG)
            .tag(_tag2)
            .build();
    final List<Assignment> expectedAssignments =
        _abTestSvc.combineAssignmentsAndOverrides(
            realAssignments,
            List.of(overrideAssignmentForTest3),
            _user.getId(),
            _group.getId(),
            _org.getId());
    final EntityForExperimentation entityForExperimentation =
        new EntityForExperimentation(_org.getId(), EntityType.ORG);

    doReturn(realAssignments)
        .when(_abTestSvc)
        .ensureAssignmentsForEntity(_org, testNames, _user, null);
    doReturn(realAssignments)
        .when(_abTestSvc)
        .ensureAssignmentsForEntity(entityForExperimentation, testNames, _user, null);
    doReturn(List.of(overrideAssignmentForTest3)).when(_overrideAssignmentDao).findByUserId(any());

    final List<Assignment> result =
        _abTestSvc.ensureAssignmentsForEntityWithOverrides(
            entityForExperimentation, testNames, _user, null);

    assertEquals(expectedAssignments, result);

    final List<Assignment> resultFromNonEntityGenericMethod =
        _abTestSvc.ensureAllAssignments(_user, null, _org, testNames, false, null);
    assertEquals(expectedAssignments, resultFromNonEntityGenericMethod);
  }

  @Test
  public void ensureAssignmentsForEntity() {
    final List<ABTest> unassignedTests = new ArrayList<>();

    unassignedTests.add(test2);

    final Assignment existing = getAssignmentControl();
    final Assignment newOne = getAssignmentTest();

    final List<Assignment> existingAssignments = Collections.singletonList(existing);
    final List<String> testNames =
        activeTests.stream().map(ABTest::getTestName).collect(Collectors.toList());

    doReturn(activeTests).when(_abTestSvc).getActiveTests(any(), any());
    doReturn(existingAssignments)
        .when(_abTestSvc)
        .getCurrentAssignmentsForEntityForTags(any(), any());
    doReturn(ListUtils.union(existingAssignments, List.of(newOne)))
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());
    doReturn(unassignedTests).when(_abTestSvc).getUnassignedTests(activeTests, existingAssignments);
    doReturn(Collections.singletonList(newOne))
        .when(_abTestSvc)
        .assignEntityToTests(_user, unassignedTests, existingAssignments, null, null);

    final List<Assignment> result =
        _abTestSvc.ensureAssignmentsForEntity(_user, testNames, _user, null);
    assertEquals(Arrays.asList(existing, newOne), result);

    // Check feature flags
    final List<TestGroupFeatureFlag> returnedFeatureFlags =
        _abTestSvc
            .getActiveTests(_user.getEntityType(), testNames)
            .get(0)
            .getTestGroups()
            .get(1)
            .getFeatureFlags();
    assertEquals(returnedFeatureFlags.get(0), featureFlags1.get(0));
    assertEquals(returnedFeatureFlags.get(1), featureFlags1.get(1));
    assertEquals(returnedFeatureFlags.get(2), featureFlags1.get(2));
  }

  @Test
  public void test_ensureAssignmentsForEntity_noActiveTests() {
    doReturn(List.of()).when(_abTestSvc).getActiveTests(any());

    final List<String> testNames =
        activeTests.stream().map(ABTest::getTestName).collect(Collectors.toList());

    final List<Assignment> result =
        _abTestSvc.ensureAssignmentsForEntity(_user, testNames, _user, null);

    assertEquals(List.of(), result);
    verify(_abTestSvc, never()).assignEntityToTests(any(), any(), any(), any(), any());
  }

  @Test
  public void test_ensureAssignmentsForEntity_noUnassignedTests() {
    final Assignment existing = getAssignmentControl();
    final List<Assignment> existingAssignments = Collections.singletonList(existing);

    doReturn(null).when(_abTestSvc).ensureAndGetEntityHoldoutTestId(any(), any(), any());
    doReturn(List.of(test1)).when(_abTestSvc).getActiveTests(any(), any());
    doReturn(existingAssignments)
        .when(_abTestSvc)
        .getCurrentAssignmentsForEntityForTags(any(), any());
    doReturn(existingAssignments)
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());

    final List<Assignment> result =
        _abTestSvc.ensureAssignmentsForEntity(_user, List.of(test1.getTestName()), _user, null);

    assertEquals(existingAssignments, result);
    verify(_abTestSvc, never()).assignEntityToTests(any(), any(), any(), any(), any());
  }

  @Test
  public void test_ensureAssignmentsForEntity_gradualRollout() {
    final List<String> testNames = List.of(_testName1, _testName2, _testName3);

    final ABTest gradualRolloutTest1 =
        ABTest.builder()
            .id(_testid1)
            .testName(_testName1)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup(_testGroupName1, _testGroupDatabaseId1, 50, true, null),
                    new TestGroup(_testGroupName2, _testGroupDatabaseId2, 50, false, null)))
            .description("gradual rollout test 1")
            .scope(EntityType.USER)
            .scopePercentage(100)
            .gradualRolloutPercentage(0)
            .jiraLink("asdf")
            .tag(_tag1)
            .experimentStatus(ExperimentStatus.LIVE)
            .build();

    final ABTest gradualRolloutTest2 =
        ABTest.builder()
            .id(_testid2)
            .testName(_testName2)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup(_testGroupName1, _testGroupDatabaseId1, 50, true, null),
                    new TestGroup(_testGroupName2, _testGroupDatabaseId2, 50, false, null)))
            .description("gradual rollout test 2")
            .scope(EntityType.USER)
            .scopePercentage(100)
            .gradualRolloutPercentage(100)
            .jiraLink("asdf")
            .tag(_tag2)
            .experimentStatus(ExperimentStatus.LIVE)
            .build();

    final ABTest gradualRolloutTest3 =
        ABTest.builder()
            .id(_testid3)
            .testName(_testName3)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup(_testGroupName1, _testGroupDatabaseId1, 50, true, null),
                    new TestGroup(_testGroupName2, _testGroupDatabaseId2, 50, false, null)))
            .description("gradual rollout test 3")
            .scope(EntityType.USER)
            .scopePercentage(100)
            .gradualRolloutPercentage(100)
            .jiraLink("asdf")
            .tag(_tag3)
            .experimentStatus(ExperimentStatus.LIVE)
            .build();

    final Date staticDate = Date.from(Instant.parse("2025-04-01T00:00:00Z"));

    final Assignment.Builder<?> baseAssignmentBuilderForTest3 =
        Assignment.builder()
            .id(oid(3))
            .testId(gradualRolloutTest3.getId())
            .entityId(_user.getId())
            .entityType(EntityType.USER)
            .testName(gradualRolloutTest3.getTestName())
            .tag(gradualRolloutTest3.getTag())
            .assignmentDate(staticDate)
            .gradualRolloutAssignmentValue(44)
            .meta(new AssignmentMetaData(false));

    final Assignment existingNullAssignmentForTest3 =
        baseAssignmentBuilderForTest3.testGroupName(null).testGroupDatabaseId(null).build();

    final List<Assignment> existingAssignments = List.of(existingNullAssignmentForTest3);

    final Assignment expectedAssignment1 =
        Assignment.builder()
            .id(oid(1))
            .testId(gradualRolloutTest1.getId())
            .entityId(_user.getId())
            .entityType(EntityType.USER)
            .testName(_testName1)
            .testGroupName(null)
            .testGroupDatabaseId(null)
            .tag(_tag1)
            .assignmentDate(staticDate)
            .gradualRolloutAssignmentValue(50)
            .meta(new AssignmentMetaData(false))
            .build();

    final Assignment expectedAssignment2 =
        Assignment.builder()
            .id(oid(2))
            .testId(gradualRolloutTest2.getId())
            .entityId(_user.getId())
            .entityType(EntityType.USER)
            .testName(_testName2)
            .testGroupName(_testGroupName1)
            .testGroupDatabaseId(_testGroupDatabaseId1)
            .tag(_tag2)
            .assignmentDate(staticDate)
            .meta(new AssignmentMetaData(false))
            .build();

    final Assignment expectedConvertedAssignmentForTest3 =
        baseAssignmentBuilderForTest3
            .testGroupName(_testGroupName2)
            .testGroupDatabaseId(_testGroupDatabaseId2)
            .build();

    doReturn(List.of(gradualRolloutTest1, gradualRolloutTest2, gradualRolloutTest3))
        .when(_abTestSvc)
        .getActiveTests(any(), any());
    doReturn(existingAssignments)
        .when(_abTestSvc)
        .getCurrentAssignmentsForEntityForTags(any(), any());
    doReturn(List.of(expectedAssignment1, expectedAssignment2, expectedConvertedAssignmentForTest3))
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());
    doReturn(null).when(_abTestSvc).ensureAndGetEntityHoldoutTestId(any(), any(), any());
    doReturn(List.of(expectedAssignment1, expectedAssignment2))
        .when(_abTestSvc)
        .assignEntityToTests(
            _user,
            List.of(gradualRolloutTest1, gradualRolloutTest2),
            existingAssignments,
            null,
            null);
    doNothing().when(_abTestSvc).updateGradualRolloutAssignments(any());
    doReturn(50).when(_abTestSvc).getRandomInteger();
    doReturn(75.0).when(_abTestSvc).getRandomPercentage();
    doReturn(staticDate).when(_abTestSvc).getNow();

    final List<Assignment> result =
        _abTestSvc.ensureAssignmentsForEntity(_user, testNames, _user, null);

    assertEquals(
        List.of(expectedAssignment1, expectedAssignment2, expectedConvertedAssignmentForTest3),
        result);

    // Verify the spies were called with the expected arguments
    verify(_abTestSvc, times(1)).getActiveTests(_user.getEntityType(), testNames);
    verify(_abTestSvc, times(1))
        .getCurrentAssignmentsForEntityForTags(_user, List.of(_tag2, _tag3, _tag1));
    verify(_abTestSvc, times(1))
        .assignEntityToTests(
            _user,
            List.of(gradualRolloutTest1, gradualRolloutTest2),
            existingAssignments,
            null,
            null);
    verify(_abTestSvc, times(1))
        .updateGradualRolloutAssignments(List.of(expectedConvertedAssignmentForTest3));
  }

  @Test
  public void ensureAssignmentsForEntity_withActiveHoldout_entityNotInHoldout() {
    // Make sure the feature flag to enable holdouts is on
    assertTrue(
        isFeatureFlagEnabled(FeatureFlag.ENABLE_EXPERIMENT_HOLDOUTS, _appSettings, _org, null));
    final List<ABTest> unassignedTests = new ArrayList<>();

    unassignedTests.add(test2);

    final Assignment existing = getAssignmentControl();
    final Assignment newOne = getAssignmentTest();

    final List<Assignment> existingAssignments = Collections.singletonList(existing);

    // Make our entity NOT in the holdout group
    doReturn(null).when(_abTestSvc).ensureAndGetEntityHoldoutTestId(any(), any(), any());

    doReturn(activeTests).when(_abTestSvc).getActiveTests(any(), any());
    doReturn(existingAssignments)
        .when(_abTestSvc)
        .getCurrentAssignmentsForEntityForTags(any(), any());
    doReturn(ListUtils.union(existingAssignments, List.of(newOne)))
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());
    doReturn(unassignedTests).when(_abTestSvc).getUnassignedTests(activeTests, existingAssignments);
    doReturn(Collections.singletonList(newOne))
        .when(_abTestSvc)
        .assignEntityToTests(_user, unassignedTests, existingAssignments, null, null);

    final List<String> testNames =
        activeTests.stream().map(ABTest::getTestName).collect(Collectors.toList());

    final List<Assignment> result =
        _abTestSvc.ensureAssignmentsForEntity(_user, testNames, _user, null);
    assertEquals(Arrays.asList(existing, newOne), result);
    verify(_abTestSvc, times(1)).ensureAndGetEntityHoldoutTestId(any(), any(), any());
    verify(_abTestSvc, never()).getCurrentGhostAssignmentsForEntityForTags(any(), any());

    // Check feature flags -- not exactly related to "ensureAssignmentsForEntity, but good to check
    final List<TestGroupFeatureFlag> returnedFeatureFlags =
        _abTestSvc
            .getActiveTests(_user.getEntityType(), testNames)
            .get(0)
            .getTestGroups()
            .get(1)
            .getFeatureFlags();
    assertEquals(returnedFeatureFlags.get(0), featureFlags1.get(0));
    assertEquals(returnedFeatureFlags.get(1), featureFlags1.get(1));
    assertEquals(returnedFeatureFlags.get(2), featureFlags1.get(2));
  }

  @Test
  public void ensureAssignmentsForEntity_withActiveHoldout_entityInHoldout() {
    // Make sure the feature flag to enable holdouts is on
    assertTrue(
        isFeatureFlagEnabled(FeatureFlag.ENABLE_EXPERIMENT_HOLDOUTS, _appSettings, _org, null));

    final Assignment existingTest1ControlAssignment = getAssignmentControl();

    final List<Assignment> existingAssignments =
        List.of(existingTest1ControlAssignment, holdoutLiveAssignment);
    final GhostAssignment ghostAssignmentForTest2 =
        GhostAssignment.builder()
            .testId(_testid2)
            .testGroupName("control")
            .testName(_testName2)
            .linkedHoldoutTestName(holdoutTestLive.getTestName())
            .linkedHoldoutTestId(holdoutTestLive.getId())
            .entityType(EntityType.USER)
            .entityId(_user.getId())
            .assignmentDate(new Date())
            .build();

    activeTests.add(holdoutTestLive);

    // Make our entity in the holdout group
    doReturn(_holdoutTestId1).when(_abTestSvc).ensureAndGetEntityHoldoutTestId(any(), any(), any());

    doReturn(activeTests).when(_abTestSvc).getActiveTests(any(), any());
    doReturn(List.of(existingTest1ControlAssignment, holdoutLiveAssignment))
        .when(_abTestSvc)
        .getCurrentAssignmentsForEntityForTags(any(), any());
    doReturn(List.of()).when(_abTestSvc).getCurrentGhostAssignmentsForEntityForTags(any(), any());
    doReturn(List.of(test2)).when(_abTestSvc).getUnassignedTests(activeTests, existingAssignments);
    doReturn(Collections.singletonList(ghostAssignmentForTest2))
        .when(_abTestSvc)
        .assignEntityToTests(any(), any(), any(), any(), any());
    doReturn(List.of(existingTest1ControlAssignment))
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());

    final List<String> activeTestNames =
        activeTests.stream().map(ABTest::getTestName).collect(Collectors.toList());

    // Check that the existing Assignments is returned
    final List<Assignment> result =
        _abTestSvc.ensureAssignmentsForEntity(_user, activeTestNames, _user, null);
    assertEquals(List.of(existingTest1ControlAssignment), result);
    verify(_abTestSvc, times(1)).ensureAndGetEntityHoldoutTestId(any(), any(), any());
    verify(_abTestSvc, times(1)).getCurrentGhostAssignmentsForEntityForTags(any(), any());
    verify(_abTestSvc, times(1)).getCurrentGhostAssignmentsForEntityForTags(any(), any());

    // Check feature flags for test1 control group are returned and not any for test2
    // -- not exactly related to "ensureAssignmentsForEntity, but good to check
    final List<TestGroupFeatureFlag> returnedFeatureFlags =
        _abTestSvc
            .getActiveTests(_user.getEntityType(), activeTestNames)
            .get(0)
            .getTestGroups()
            .get(1)
            .getFeatureFlags();
    assertEquals(3, returnedFeatureFlags.size());
    assertEquals(returnedFeatureFlags.get(0), featureFlags1.get(0));
    assertEquals(returnedFeatureFlags.get(1), featureFlags1.get(1));
    assertEquals(returnedFeatureFlags.get(2), featureFlags1.get(2));
  }

  @Test
  public void ensureAssignmentsForEntity_holdoutFlagDisabled() {
    // Make sure the feature flag to enable holdouts is off
    mockedStaticFeatureFlagSvc
        .when(
            () ->
                isFeatureFlagEnabled(
                    eq(FeatureFlag.ENABLE_EXPERIMENT_HOLDOUTS), any(), any(), any()))
        .thenReturn(false);
    assertFalse(
        isFeatureFlagEnabled(FeatureFlag.ENABLE_EXPERIMENT_HOLDOUTS, _appSettings, _org, null));

    final Assignment existingTest1ControlAssignment = getAssignmentControl();
    final Assignment newTest2VariantAssignment = getAssignmentTest();

    final List<Assignment> existingAssignments =
        List.of(existingTest1ControlAssignment, holdoutLiveAssignment);

    doReturn(activeTests).when(_abTestSvc).getActiveTests(any(), any());
    doReturn(List.of(existingTest1ControlAssignment, holdoutLiveAssignment))
        .when(_abTestSvc)
        .getCurrentAssignmentsForEntityForTags(any(), any());
    doReturn(List.of(test2)).when(_abTestSvc).getUnassignedTests(activeTests, existingAssignments);
    doReturn(Collections.singletonList(newTest2VariantAssignment))
        .when(_abTestSvc)
        .assignEntityToTests(any(), any(), any(), any(), any());
    activeTests.add(holdoutTestLive);

    final List<String> activeTestNames =
        activeTests.stream().map(ABTest::getTestName).collect(Collectors.toList());
    // Check that the existing Assignment and new holdoutAssignment are returned
    final List<Assignment> result =
        _abTestSvc.ensureAssignmentsForEntity(_user, activeTestNames, _user, null);
    assertEquals(
        result,
        List.of(existingTest1ControlAssignment, holdoutLiveAssignment, newTest2VariantAssignment));
    verify(_abTestSvc, never()).ensureAndGetEntityHoldoutTestId(any(), any(), any());
    verify(_abTestSvc, never()).getCurrentGhostAssignmentsForEntityForTags(any(), any());
    verify(_abTestSvc, never()).getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());

    // Check feature flags for test1 control group are returned and not any for test2
    // -- not exactly related to "ensureAssignmentsForEntity" but good to check
    final List<TestGroupFeatureFlag> returnedFeatureFlags =
        _abTestSvc
            .getActiveTests(_user.getEntityType(), activeTestNames)
            .get(0)
            .getTestGroups()
            .get(1)
            .getFeatureFlags();
    assertEquals(3, returnedFeatureFlags.size());
    assertEquals(returnedFeatureFlags.get(0), featureFlags1.get(0));
    assertEquals(returnedFeatureFlags.get(1), featureFlags1.get(1));
    assertEquals(returnedFeatureFlags.get(2), featureFlags1.get(2));
  }

  @Test
  public void test_ensureAndGetEntityHoldoutTestId_noUnassignedTests() {
    final Assignment holdoutAssignment1 = getHoldoutAssignmentControl();
    final List<Assignment> existingAssignments = List.of(holdoutAssignment1);
    final List<ABTest> holdoutTests = List.of(holdoutTestLive, holdoutTestCooldown);

    doReturn(List.of(test1, test2, holdoutTestLive, holdoutTestCooldown))
        .when(_abTestSvc)
        .getActiveTests(any());

    doReturn(existingAssignments)
        .when(_abTestSvc)
        .getCurrentAssignmentsForEntityForTags(any(), any());
    doReturn(List.of()).when(_abTestSvc).getUnassignedTests(holdoutTests, existingAssignments);

    final ObjectId result = _abTestSvc.ensureAndGetEntityHoldoutTestId(_org, null, null);

    assertEquals(_holdoutTestId1, result);
    verify(_abTestSvc, times(1)).getActiveTests(any());
    verify(_abTestSvc, times(1)).getCurrentAssignmentsForEntityForTags(any(), any());
    verify(_abTestSvc, never()).assignEntityToTests(any(), any(), any(), any(), any());
  }

  @Test
  public void test_ensureAndGetEntityHoldoutTestId_newAssignments() {
    final Assignment holdoutAssignment1 = getHoldoutAssignmentControl();
    final List<ABTest> holdoutTests = List.of(holdoutTestLive, holdoutTestCooldown);
    final List<Assignment> existingAssignments = List.of();

    doReturn(List.of(test1, test2, holdoutTestLive, holdoutTestCooldown))
        .when(_abTestSvc)
        .getActiveTests(any());
    doReturn(existingAssignments)
        .when(_abTestSvc)
        .getCurrentAssignmentsForEntityForTags(any(), any());
    doReturn(holdoutTests).when(_abTestSvc).getUnassignedTests(holdoutTests, existingAssignments);
    // in this situation, if both holdout tests were unassigned, assignEntityToTests
    // would assign the entity to ONE of the tests (it randomly picks 1) and then do a null
    // assignment for the other one. (Because all Holdout experiments share the same tag.) So only 1
    // assignment comes back.
    doReturn(List.of(holdoutAssignment1))
        .when(_abTestSvc)
        .assignEntityToTests(_org, holdoutTests, List.of(), null, null);

    final ObjectId result = _abTestSvc.ensureAndGetEntityHoldoutTestId(_org, null, null);

    assertEquals(_holdoutTestId1, result);
    verify(_abTestSvc, times(1)).getCurrentAssignmentsForEntityForTags(any(), any());
    verify(_abTestSvc, times(1)).assignEntityToTests(any(), any(), any(), any(), any());
  }

  @Test
  public void test_ensureAndGetEntityHoldoutTestId_noActiveHoldouts() {
    doReturn(List.of(test1, test2)).when(_abTestSvc).getActiveTests(any());

    final ObjectId result = _abTestSvc.ensureAndGetEntityHoldoutTestId(_org, null, null);

    assertNull(result);
    verify(_abTestSvc, never()).getCurrentAssignmentsForEntityForTags(any(), any());
    verify(_abTestSvc, never()).assignEntityToTests(any(), any(), any(), any(), any());
  }

  @Test
  public void test_ensureAndGetEntityHoldoutTestId_notInHoldout() {
    final Assignment holdoutAssignment1 = getHoldoutAssignmentVariant();
    final List<Assignment> existingAssignments = List.of(holdoutAssignment1);
    final List<ABTest> holdoutTests = List.of(holdoutTestCooldown);

    doReturn(List.of(test1, test2, holdoutTestCooldown)).when(_abTestSvc).getActiveTests(any());

    doReturn(existingAssignments)
        .when(_abTestSvc)
        .getCurrentAssignmentsForEntityForTags(any(), any());
    doReturn(List.of()).when(_abTestSvc).getUnassignedTests(holdoutTests, existingAssignments);

    final ObjectId result = _abTestSvc.ensureAndGetEntityHoldoutTestId(_org, null, null);

    assertNull(result);
    verify(_abTestSvc, times(1)).getCurrentAssignmentsForEntityForTags(any(), any());
    verify(_abTestSvc, never()).assignEntityToTests(any(), any(), any(), any(), any());
  }

  @Test
  public void test_ensureAndGetEntityHoldoutTestId_notInHoldout_withControlOverride() {
    final Assignment holdoutAssignment1 = getHoldoutAssignmentVariant();
    final List<Assignment> existingAssignments = List.of(holdoutAssignment1);
    final List<ABTest> holdoutTests = List.of(holdoutTestLive);

    doReturn(List.of(test1, test2, holdoutTestLive)).when(_abTestSvc).getActiveTests(any());

    doReturn(existingAssignments)
        .when(_abTestSvc)
        .getCurrentAssignmentsForEntityForTags(any(), any());
    doReturn(List.of()).when(_abTestSvc).getUnassignedTests(holdoutTests, existingAssignments);
    doReturn(List.of(test1, test2, test4, test6, holdoutTestLive)).when(_abTestSvc).getAllTests();

    doReturn(List.of(holdoutControlOverrideAssignment))
        .when(_abTestSvc)
        .getOverrideAssignmentsForUserId(any());

    final ObjectId result = _abTestSvc.ensureAndGetEntityHoldoutTestId(_org, _user, null);

    assertEquals(_holdoutTestId1, result);
    verify(_abTestSvc, times(1)).getCurrentAssignmentsForEntityForTags(any(), any());
    verify(_abTestSvc, times(1))
        .checkAndMergeOverridesForAssignments(any(), any(), any(), any(), any(), any());
    verify(_abTestSvc, never()).assignEntityToTests(any(), any(), any(), any(), any());
  }

  @Test
  public void test_ensureAndGetEntityHoldoutTestId_invalidConfigWithNoControl() {
    // no experiment config should ever not have a control variant (this is enforced in Pholiota),
    // but let's be safe and test the fallback logic in ensureAndGetEntityHoldoutTestId to confirm
    // it
    // returns false in this case
    final ABTest invalidTestConfig =
        ABTest.builder()
            .id(_holdoutTestId2)
            .experimentType(ExperimentType.HOLDOUT)
            .experimentStatus(ExperimentStatus.LIVE)
            .testName(_holdoutTestName2)
            .testGroups(
                List.of(
                    // note that "isControl" is null for both variants
                    new TestGroup(
                        _holdoutTestControlName, _holdoutTestGroupDatabaseId1, 5, null, null),
                    new TestGroup(
                        _holdoutTestVariantName, _holdoutTestGroupDatabaseId2, 95, null, null)))
            .description("invalid test config with no control variant")
            .scope(EntityType.ORG)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_holdoutTag)
            .build();

    final Assignment holdoutAssignment1 = getHoldoutAssignmentControl();
    final List<Assignment> existingAssignments = List.of(holdoutAssignment1);
    final List<ABTest> holdoutTests = List.of(invalidTestConfig);

    doReturn(List.of(test1, test2, invalidTestConfig)).when(_abTestSvc).getActiveTests(any());

    doReturn(existingAssignments)
        .when(_abTestSvc)
        .getCurrentAssignmentsForEntityForTags(any(), any());
    doReturn(List.of()).when(_abTestSvc).getUnassignedTests(holdoutTests, existingAssignments);

    final ObjectId result = _abTestSvc.ensureAndGetEntityHoldoutTestId(_org, null, null);

    assertNull(result);
    verify(_abTestSvc, times(1)).getCurrentAssignmentsForEntityForTags(any(), any());
    verify(_abTestSvc, never()).assignEntityToTests(any(), any(), any(), any(), any());
  }

  @Test
  public void getCurrentAssignmentsForEntityForTags() {
    final Assignment assignment1 = getAssignmentTest();
    final Assignment assignment2 = getAssignmentControl();
    final List<String> tagList = List.of(assignment1.getTag(), assignment2.getTag());

    doReturn(List.of(assignment1, assignment2)).when(_assignmentDao).findByTag(any(), any());
    doReturn(List.of(assignment1.getTestId())).when(_abTestSvc).getActiveTestIds(any());
    _abTestSvc.getCurrentAssignmentsForEntityForTags(_user, tagList);
  }

  @Test
  public void getCurrentAndLaunchedAssignmentsForEntity() {
    doReturn(List.of(assignment2)).when(_abTestSvc).getCurrentAssignmentsForEntities(any());
    doReturn(List.of()).when(_abTestSvc).getLaunchedTestsForEntity(_org, null, null);

    // No LAUNCHED tests, just returns current assignment
    assertEquals(
        List.of(assignment2),
        _abTestSvc.getCurrentAndLaunchedAssignmentsForEntity(_org, null, null));

    doReturn(List.of(launchedTest)).when(_abTestSvc).getLaunchedTestsForEntity(_org, null, null);
    List<Assignment> assignments =
        _abTestSvc.getCurrentAndLaunchedAssignmentsForEntity(_org, null, null);
    // Now with LAUNCHED test, returns current assignment and launched assignment
    assertEquals(_testGroupName2, assignments.get(0).getTestGroupName());
    assertEquals(_testGroupName7, assignments.get(1).getTestGroupName());
    verify(_abTestSvc, times(0))
        .incrementGetAssignmentsErrorCounter("getCurrentAndLaunchedAssignmentsForEntity");

    // meta isLaunched should be true for launched test
    AssignmentMetaData metaData = assignments.get(1).getMeta();
    assertTrue(metaData.getIsLaunchedExperiment());
    verify(_abTestSvc, times(0))
        .incrementGetAssignmentsErrorCounter("getCurrentAndLaunchedAssignmentsForEntity");
  }

  @Test
  public void getCurrentAndLaunchedAssignmentsForEntity_badLaunchedTestConfig() {
    List<Assignment> assignments;
    // when a LAUNCHED test with no winner is passed (an invalid config), fall back to null
    // assignment, increment error counter, and continue processing experiments.
    final ABTest badLaunchedTest =
        ABTest.builder()
            .id(oid(8))
            .experimentType(ExperimentType.AB)
            .testName("badTest")
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 0, true, null),
                    new TestGroup("variant", new ObjectId(), 100, false, false)))
            .description("badDesc")
            .scope(EntityType.ORG)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag2)
            .experimentStatus(ExperimentStatus.LAUNCHED)
            .build();
    // launchedTest is a LAUNCHED test with a winner, so it should be processed without issue.
    doReturn(List.of(launchedTest, badLaunchedTest))
        .when(_abTestSvc)
        .getLaunchedTestsForEntity(_org, null, null);
    clearInvocations(_abTestSvc);
    assignments = _abTestSvc.getCurrentAndLaunchedAssignmentsForEntity(_org, null, null);
    assertEquals(_testGroupName7, assignments.get(0).getTestGroupName());
    assertNull(assignments.get(1).getTestGroupName());
    verify(_abTestSvc, times(1)).incrementGetCurrentAndLaunchedAssignmentsErrorCounter();
  }

  @Test
  public void getCurrentAndLaunchedAssignmentsForEntity_catchExceptions() {
    List<Assignment> assignments;

    // Verify that when getCurrentAssignmentsForEntity throws an error, it is caught and handled
    // here.
    clearInvocations(_abTestSvc);
    doThrow(new RuntimeException()).when(_abTestSvc).getCurrentAssignmentsForEntities(any());
    assignments = _abTestSvc.getCurrentAndLaunchedAssignmentsForEntity(_org, null, null);
    assertEquals(List.of(), assignments);
    verify(_abTestSvc, times(1)).incrementGetCurrentAndLaunchedAssignmentsErrorCounter();

    // And when getLaunchedTestsForEntity throws an error, it is caught and handled here.
    clearInvocations(_abTestSvc);
    doThrow(new RuntimeException()).when(_abTestSvc).getLaunchedTestsForEntity(_org, null, null);
    assignments = _abTestSvc.getCurrentAndLaunchedAssignmentsForEntity(_org, null, null);
    assertEquals(List.of(), assignments);
    verify(_abTestSvc, times(1)).incrementGetCurrentAndLaunchedAssignmentsErrorCounter();

    // And when ensureAndGetEntityHoldoutTestId throws an error, it is caught and handled here.
    clearInvocations(_abTestSvc);
    assignments = _abTestSvc.getCurrentAndLaunchedAssignmentsForEntity(_org, null, null);
    assertEquals(List.of(), assignments);
    verify(_abTestSvc, times(1)).incrementGetCurrentAndLaunchedAssignmentsErrorCounter();

    // And when getLaunchedTestsForHoldoutEntity throws an error, it is caught and handled here.
    clearInvocations(_abTestSvc);
    assignments = _abTestSvc.getCurrentAndLaunchedAssignmentsForEntity(_org, null, null);
    assertEquals(List.of(), assignments);
    verify(_abTestSvc, times(1)).incrementGetCurrentAndLaunchedAssignmentsErrorCounter();

    // And when getLaunchedTests throws an error, it is caught and handled here.
    clearInvocations(_abTestSvc);
    assignments = _abTestSvc.getCurrentAndLaunchedAssignmentsForEntity(_org, null, null);
    assertEquals(List.of(), assignments);
    verify(_abTestSvc, times(1)).incrementGetCurrentAndLaunchedAssignmentsErrorCounter();
  }

  @Test
  public void getActiveTestIds() {
    when((_abTestDao.getAllTests())).thenReturn(activeTests);
    List<ObjectId> expectedTestIds = Arrays.asList(_testid1, _testid2, _testid3);
    List<ObjectId> actualTestIds = _abTestSvc.getActiveTestIds(_abTestDao.getAllTests());
    assertTrue(expectedTestIds.containsAll(actualTestIds));
    assertTrue(actualTestIds.containsAll(expectedTestIds));
  }

  @Test
  public void getActiveTests() {
    doReturn(List.of(test1, test2, test3, test4, test5, test6, launchedTest))
        .when(_abTestDao)
        .getAllTests();

    List<ABTest> orgActiveTests = _abTestSvc.getActiveTests(EntityType.ORG);
    assertEquals(2, orgActiveTests.size());
    List<ABTest> userActiveTests = _abTestSvc.getActiveTests(EntityType.USER);
    assertEquals(2, userActiveTests.size());
  }

  @Test
  public void getActiveTestsForAllEntityTypes() {
    doReturn(List.of(test1, test2, test3, test4, test5, test6, launchedTest))
        .when(_abTestDao)
        .getAllTests();

    List<ABTest> orgActiveTests = _abTestSvc.getActiveTestsForAllEntityTypes();
    assertEquals(4, orgActiveTests.size());
    assertEquals(_testid1, orgActiveTests.get(0).getId());
    assertEquals(_testid2, orgActiveTests.get(1).getId());
    assertEquals(_testid3, orgActiveTests.get(2).getId());
    assertEquals(_testid5, orgActiveTests.get(3).getId());
  }

  @Test
  public void getLaunchedTests() {
    doReturn(List.of(test1, test2, test3, test4, test5, test6, launchedTest))
        .when(_abTestDao)
        .getAllTests();

    List<ABTest> orgLaunchedTests = _abTestSvc.getLaunchedTests(EntityType.ORG);
    assertEquals(1, orgLaunchedTests.size());
    List<ABTest> userLaunchedTests = _abTestSvc.getLaunchedTests(EntityType.USER);
    assertEquals(0, userLaunchedTests.size());
  }

  @Test
  public void getLaunchedTestsForHoldoutEntity() {
    final Assignment existingAssignmentForLaunchedTest =
        Assignment.builder()
            .testId(launchedTest.getId())
            .testGroupName("control")
            .testName(launchedTest.getTestName())
            .entityType(EntityType.ORG)
            .entityId(_org.getId())
            .assignmentDate(new Date())
            .build();
    doReturn(Optional.of(launchedTest)).when(_abTestDao).getTest(launchedTest.getId());

    final List<ObjectId> launchedTestsIds = List.of(launchedTest.getId());

    doReturn(List.of(existingAssignmentForLaunchedTest))
        .when(_abTestSvc)
        .getAssignmentsForEntity(_org, launchedTestsIds);
    when(_org.getEntityType()).thenReturn(EntityType.ORG);

    // org had a control assignment for launchedTest which is LAUNCHED
    doReturn(List.of(launchedTest)).when(_abTestSvc).getLaunchedTests(EntityType.ORG);
    assertEquals(List.of(launchedTest), _abTestSvc.getLaunchedTestsForHoldoutEntity(_org));

    // ABTest.getTest returns an empty optional, no launchedTests found. This should not really
    // happen, just testing an edge case to make sure the function does not error.
    doReturn(Optional.empty()).when(_abTestDao).getTest(launchedTest.getId());
    doReturn(List.of(launchedTest)).when(_abTestSvc).getLaunchedTests(EntityType.ORG);
    assertEquals(List.of(), _abTestSvc.getLaunchedTestsForHoldoutEntity(_org));

    // there are no LAUNCHED tests
    doReturn(Optional.of(launchedTest)).when(_abTestDao).getTest(launchedTest.getId());
    doReturn(List.of()).when(_abTestSvc).getLaunchedTests(EntityType.ORG);
    assertEquals(List.of(), _abTestSvc.getLaunchedTestsForHoldoutEntity(_org));

    // org had no assignments at all
    doReturn(List.of()).when(_abTestSvc).getAssignmentsForEntity(_org, launchedTestsIds);
    doReturn(List.of(launchedTest)).when(_abTestSvc).getLaunchedTests(EntityType.ORG);
    assertEquals(List.of(), _abTestSvc.getLaunchedTestsForHoldoutEntity(_org));

    // org had a null assignment for launchedTest which is LAUNCHED
    final Assignment nullAssignment =
        Assignment.builder()
            .testId(launchedTest.getId())
            .testGroupName(null)
            .testName(launchedTest.getTestName())
            .entityType(EntityType.ORG)
            .entityId(_org.getId())
            .assignmentDate(new Date())
            .build();
    doReturn(List.of(nullAssignment))
        .when(_abTestSvc)
        .getAssignmentsForEntity(_org, launchedTestsIds);
    doReturn(List.of(launchedTest)).when(_abTestSvc).getLaunchedTests(EntityType.ORG);
    assertEquals(List.of(launchedTest), _abTestSvc.getLaunchedTestsForHoldoutEntity(_org));
  }

  @Test
  public void getLaunchedTestsForEntity() {
    final Organization holdoutEntity = mock(Organization.class);
    final Organization nonHoldoutEntity = mock(Organization.class);

    doReturn(EntityType.ORG).when(nonHoldoutEntity).getEntityType();

    doReturn(_holdoutTestId1)
        .when(_abTestSvc)
        .ensureAndGetEntityHoldoutTestId(holdoutEntity, null, null);
    doReturn(null).when(_abTestSvc).ensureAndGetEntityHoldoutTestId(nonHoldoutEntity, null, null);

    doReturn(List.of()).when(_abTestSvc).getLaunchedTestsForHoldoutEntity(holdoutEntity);
    doReturn(List.of(launchedTest)).when(_abTestSvc).getLaunchedTests(EntityType.ORG);

    assertEquals(List.of(), _abTestSvc.getLaunchedTestsForEntity(holdoutEntity, null, null));
    assertEquals(
        List.of(launchedTest), _abTestSvc.getLaunchedTestsForEntity(nonHoldoutEntity, null, null));
  }

  @Test
  public void getUnassignedTests() {
    final Assignment existingAssignment =
        Assignment.builder()
            .testId(test1.getId())
            .testGroupName("control")
            .testName(test1.getTestName())
            .entityType(EntityType.USER)
            .entityId(oid(1))
            .assignmentDate(new Date())
            .build();
    final Collection<ABTest> unassignedTests =
        _abTestSvc.getUnassignedTests(activeTests, Collections.singleton(existingAssignment));
    assertEquals(List.of(test2, test3), unassignedTests);
  }

  @Test
  public void getAllTests() {
    _abTestSvc.getAllTests();
    verify(_abTestSvc, never()).incrementGetTestsErrorCounter();
    clearInvocations(_abTestSvc);

    when((_abTestDao.getAllTests())).thenThrow(new RuntimeException());
    assertThrows(RuntimeException.class, _abTestSvc::getAllTests);
    verify(_abTestSvc, times(1)).incrementGetTestsErrorCounter();
  }

  @Test
  public void getActiveFeatureFlagsForEntity() {
    doReturn(activeTests).when(_abTestDao).getAllTests();
    doReturn(_testAssignments)
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());

    Map<String, String> expectedFeatureFlags =
        featureFlags1.stream()
            .collect(
                Collectors.toMap(TestGroupFeatureFlag::getKey, TestGroupFeatureFlag::getValue));
    expectedFeatureFlags.putAll(
        featureFlags2.stream()
            .collect(
                Collectors.toMap(TestGroupFeatureFlag::getKey, TestGroupFeatureFlag::getValue)));

    Map<String, String> returnedFeatureFlags =
        _abTestSvc.getActiveFeatureFlagsForEntity(_user, _user, null);

    assertEquals(expectedFeatureFlags, returnedFeatureFlags);
    assertNonEntityGenericGetActiveFeatureFlags(expectedFeatureFlags, EntityType.USER);
  }

  @Test
  public void getActiveFeatureFlagsForEntity_skipDisabled() {
    final List<TestGroupFeatureFlag> disabledTestFeatureFlags = new ArrayList<>();
    disabledTestFeatureFlags.add(new TestGroupFeatureFlag("flag-4", "Should not show"));
    disabledTestFeatureFlags.add(new TestGroupFeatureFlag("flag-5", "Should not show"));

    final ObjectId disabledTestId = new ObjectId();
    final ABTest disabledTest =
        ABTest.builder()
            .id(disabledTestId)
            .experimentType(ExperimentType.AB)
            .experimentStatus(ExperimentStatus.COMPLETE)
            .testName(_testName2)
            .testGroups(
                List.of(
                    new TestGroup("control-2", new ObjectId(), 50, true, null),
                    new TestGroup(
                        _testGroupName2,
                        new ObjectId(),
                        50,
                        disabledTestFeatureFlags,
                        false,
                        null)))
            .description("desc2")
            .scope(EntityType.USER)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag2)
            .build();

    List<ABTest> allTests = List.of(test1, disabledTest);

    doReturn(allTests).when(_abTestDao).getAllTests();
    doReturn(_testAssignments)
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());

    Map<String, String> expectedFeatureFlags =
        featureFlags1.stream()
            .collect(
                Collectors.toMap(TestGroupFeatureFlag::getKey, TestGroupFeatureFlag::getValue));

    Map<String, String> returnedFeatureFlags =
        _abTestSvc.getActiveFeatureFlagsForEntity(_user, _user, null);
    assertEquals(expectedFeatureFlags, returnedFeatureFlags);
    assertNonEntityGenericGetActiveFeatureFlags(expectedFeatureFlags, EntityType.USER);
  }

  @Test
  public void getActiveFeatureFlagsForEntity_includeOverriddenInDisabled() {
    final List<TestGroupFeatureFlag> disabledTestFeatureFlags = new ArrayList<>();
    disabledTestFeatureFlags.add(new TestGroupFeatureFlag("flag-4", "Should show"));
    disabledTestFeatureFlags.add(new TestGroupFeatureFlag("flag-5", "Should also show"));

    final ObjectId disabledTestId = new ObjectId();
    final ObjectId disabledTestGroupId = new ObjectId();
    final ABTest disabledTest =
        ABTest.builder()
            .id(disabledTestId)
            .experimentType(ExperimentType.AB)
            .experimentStatus(ExperimentStatus.COMPLETE)
            .testName(_testName2)
            .testGroups(
                List.of(
                    new TestGroup("control-2", new ObjectId(), 50, true, null),
                    new TestGroup(
                        _testGroupName2,
                        disabledTestGroupId,
                        50,
                        disabledTestFeatureFlags,
                        false,
                        null)))
            .description("desc2")
            .scope(EntityType.USER)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag2)
            .build();

    final OverrideAssignment overrideAssignment =
        new OverrideAssignment.Builder()
            .userId(_user.getId())
            .testGroupId(_testGroupName2)
            .testGroupDatabaseId(disabledTestGroupId)
            .testName(_testName2)
            .testId(disabledTestId)
            .entityType(EntityType.USER)
            .build();

    final Assignment assignmentFromOverride =
        _abTestSvc.createAssignmentFromOverride(overrideAssignment, _user.getId(), null, null);
    final List<Assignment> assignments = List.of(assignment1, assignmentFromOverride);

    List<ABTest> allTests = List.of(test1, disabledTest);

    doReturn(allTests).when(_abTestDao).getAllTests();
    doReturn(assignments)
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());
    doReturn(Collections.singletonList(overrideAssignment))
        .when(_overrideAssignmentDao)
        .findByUserId(any());

    Map<String, String> expectedFeatureFlags =
        Stream.concat(featureFlags1.stream(), disabledTestFeatureFlags.stream())
            .collect(
                Collectors.toMap(TestGroupFeatureFlag::getKey, TestGroupFeatureFlag::getValue));

    Map<String, String> returnedFeatureFlags =
        _abTestSvc.getActiveFeatureFlagsForEntity(_user, _user, null);
    assertEquals(expectedFeatureFlags, returnedFeatureFlags);
    assertNonEntityGenericGetActiveFeatureFlags(expectedFeatureFlags, EntityType.USER);
  }

  @Test
  public void getActiveFeatureFlagsForEntity_duplicateKeysConsolidate() {
    final List<Assignment> assignmentsWithDupFlags = List.of(assignment2, assignment3);

    doReturn(activeTests).when(_abTestDao).getAllTests();
    doReturn(assignmentsWithDupFlags)
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());

    Map<String, String> returnedFeatureFlags =
        _abTestSvc.getActiveFeatureFlagsForEntity(_user, _user, null);
    assertEquals(2, returnedFeatureFlags.size());
    assert (returnedFeatureFlags.containsKey("flag-4"));
    assert (returnedFeatureFlags.containsKey("flag-5"));
    verify(_abTestSvc, times(2)).incrementDuplicateExperimentFeatureFlagCounter();
    assertNonEntityGenericGetActiveFeatureFlags(returnedFeatureFlags, EntityType.USER);
  }

  @Test
  public void getActiveFeatureFlagsForEntity_identicalTestGroupNames() {
    // Define new tests with feature flags for their control group
    final List<TestGroupFeatureFlag> assignedFeatureFlags = new ArrayList<>();
    assignedFeatureFlags.add(new TestGroupFeatureFlag("assigned-feature-flag", "should-appear"));

    final List<TestGroupFeatureFlag> unassignedFeatureFlags = new ArrayList<>();
    unassignedFeatureFlags.add(
        new TestGroupFeatureFlag("unassigned-feature-flag", "should-not-appear"));

    final List<ABTest> abTests =
        Arrays.asList(
            ABTest.builder()
                .id(_testid1)
                .testName("new-test-1")
                .experimentType(ExperimentType.AB)
                .testGroups(
                    List.of(
                        new TestGroup(
                            "control", _testGroupDatabaseId1, 50, assignedFeatureFlags, true, null),
                        new TestGroup("variantA", new ObjectId(), 50, false, null)))
                .scope(EntityType.USER)
                .scopePercentage(100)
                .experimentStatus(ExperimentStatus.LIVE)
                .tag(_tag1)
                .build(),
            ABTest.builder()
                .id(_testid2)
                .testName("new-test-2")
                .experimentType(ExperimentType.AB)
                .testGroups(
                    List.of(
                        new TestGroup(
                            "control", new ObjectId(), 50, unassignedFeatureFlags, true, null),
                        new TestGroup("variantB", _testGroupDatabaseId2, 50, false, null)))
                .scope(EntityType.USER)
                .experimentStatus(ExperimentStatus.LIVE)
                .scopePercentage(100)
                .tag(_tag2)
                .build());

    // Assign the user to the control group of new-test-1, and the non-control group of new-test-2
    final List<Assignment> assignments =
        Arrays.asList(
            Assignment.builder()
                .entityId(oid(1))
                .entityType(EntityType.USER)
                .testId(_testid1)
                .testGroupName("control")
                .testGroupDatabaseId(_testGroupDatabaseId1)
                .testName("new-test-1")
                .tag(_tag1)
                .build(),
            Assignment.builder()
                .entityId(oid(2))
                .entityType(EntityType.USER)
                .testId(_testid2)
                .testGroupName("variantB")
                .testGroupDatabaseId(_testGroupDatabaseId2)
                .testName("new-test-2")
                .tag(_tag2)
                .build());

    doReturn(abTests).when(_abTestDao).getAllTests(); // active tests
    doReturn(assignments)
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());

    // Expect feature flags from new-test-2's control group not to be in the results
    Map<String, String> returnedFeatureFlags =
        _abTestSvc.getActiveFeatureFlagsForEntity(_user, _user, null);
    assertEquals(1, returnedFeatureFlags.size());
    assert (returnedFeatureFlags.containsKey("assigned-feature-flag"));
    assertFalse(returnedFeatureFlags.containsKey("unassigned-feature-flag"));
    assertNonEntityGenericGetActiveFeatureFlags(returnedFeatureFlags, EntityType.USER);
  }

  @Test
  public void getActiveFeatureFlagsForEntity_includeTestGroupOverrides() {
    final ObjectId enabledTestId = new ObjectId();
    final ObjectId enabledTestGroupId = new ObjectId();

    final List<TestGroupFeatureFlag> variantFeatureFlags = new ArrayList<>();
    variantFeatureFlags.add(new TestGroupFeatureFlag("flag-6", "Should show"));
    variantFeatureFlags.add(new TestGroupFeatureFlag("flag-7", "Should show"));

    final List<TestGroupFeatureFlag> controlFeatureFlags = new ArrayList<>();
    controlFeatureFlags.add(new TestGroupFeatureFlag("flag-6", "Should not show"));
    controlFeatureFlags.add(new TestGroupFeatureFlag("flag-7", "Should not show"));

    final OverrideAssignment overrideAssignment =
        new OverrideAssignment.Builder()
            .userId(_user.getId())
            .testGroupId(_testGroupName2)
            .testGroupDatabaseId(enabledTestGroupId)
            .testName(_testName2)
            .testId(enabledTestId)
            .entityType(EntityType.USER)
            .build();

    // override the variant
    final Assignment assignmentFromOverride =
        _abTestSvc.createAssignmentFromOverride(overrideAssignment, _user.getId(), null, null);

    final ABTest enabledTest =
        ABTest.builder()
            .id(enabledTestId)
            .testName(_testName2)
            .experimentType(ExperimentType.AB)
            .experimentStatus(ExperimentStatus.LIVE)
            .testGroups(
                List.of(
                    new TestGroup("Control", new ObjectId(), 50, controlFeatureFlags, true, null),
                    new TestGroup(
                        _testGroupName2, enabledTestGroupId, 50, variantFeatureFlags, false, null)))
            .description("desc3")
            .scope(EntityType.USER)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag2)
            .build();

    final Assignment realAssignment =
        Assignment.builder()
            .testId(enabledTestId)
            .testGroupName("control")
            .testName(_testName2)
            .entityType(EntityType.USER)
            .entityId(oid(1))
            .assignmentDate(new Date())
            .build();

    final List<Assignment> assignments = List.of(realAssignment, assignmentFromOverride);

    List<ABTest> allTests = List.of(test1, enabledTest);

    doReturn(allTests).when(_abTestDao).getAllTests();
    doReturn(assignments)
        .when(_abTestSvc)
        .getCurrentAndLaunchedAssignmentsForEntity(any(), any(), any());
    doReturn(Collections.singletonList(overrideAssignment))
        .when(_overrideAssignmentDao)
        .findByUserId(any());

    Map<String, String> expectedFeatureFlags =
        variantFeatureFlags.stream()
            .collect(
                Collectors.toMap(TestGroupFeatureFlag::getKey, TestGroupFeatureFlag::getValue));

    Map<String, String> returnedFeatureFlags =
        _abTestSvc.getActiveFeatureFlagsForEntity(_user, _user, null);
    assertEquals(expectedFeatureFlags, returnedFeatureFlags);
    assertNonEntityGenericGetActiveFeatureFlags(expectedFeatureFlags, EntityType.USER);
  }

  @Test
  public void getActiveFeatureFlagsForEntity_includeWinningVariantTestGroups() {
    final List<TestGroupFeatureFlag> variantFeatureFlags = new ArrayList<>();
    variantFeatureFlags.add(new TestGroupFeatureFlag("flag-6", "Should show"));
    variantFeatureFlags.add(new TestGroupFeatureFlag("flag-7", "Should show"));

    final List<TestGroupFeatureFlag> controlFeatureFlags = new ArrayList<>();
    controlFeatureFlags.add(new TestGroupFeatureFlag("flag-6", "Should not show"));
    controlFeatureFlags.add(new TestGroupFeatureFlag("flag-7", "Should not show"));

    Map<String, String> expectedFeatureFlags =
        variantFeatureFlags.stream()
            .collect(
                Collectors.toMap(TestGroupFeatureFlag::getKey, TestGroupFeatureFlag::getValue));

    ABTest winningTest =
        ABTest.builder()
            .id(_holdoutTestId1)
            .experimentType(ExperimentType.CMAB)
            .experimentStatus(ExperimentStatus.LAUNCHED)
            .testName(_holdoutTestName1)
            .testGroups(
                List.of(
                    new TestGroup(
                        _holdoutTestControlName,
                        _holdoutTestGroupDatabaseId1,
                        5,
                        controlFeatureFlags,
                        true,
                        false),
                    new TestGroup(
                        _holdoutTestVariantName,
                        _holdoutTestGroupDatabaseId2,
                        95,
                        variantFeatureFlags,
                        false,
                        true)))
            .description("holdout test 1")
            .scope(EntityType.ORG)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_holdoutTag)
            .build();

    List<ABTest> allTests = List.of(test1, winningTest);
    doReturn(allTests).when(_abTestDao).getAllTests();
    doReturn(List.of(winningTest)).when(_abTestSvc).getLaunchedTestsForEntity(_org, null, null);

    Map<String, String> returnedFeatureFlags =
        _abTestSvc.getActiveFeatureFlagsForEntity(_org, null, null);
    assertEquals(expectedFeatureFlags, returnedFeatureFlags);
    assertNonEntityGenericGetActiveFeatureFlags(expectedFeatureFlags, EntityType.ORG);
  }

  @Test
  public void assignEntityToTests() {
    final String testName1 = "all control test";
    final String testName2 = "all experimental test";
    final String testName3 = "zero scope percentage test";
    final String testName4 = "zero gradual rollout percentage test";
    final String testName5 = "100 gradual rollout percentage test";
    final ExperimentType experimentTypeAB = ExperimentType.AB;

    when(_user.getEntityType()).thenReturn(EntityType.USER);
    when(_user.getId()).thenReturn(oid(1));
    doReturn(50).when(_abTestSvc).getRandomInteger();
    doReturn(true).when(_appSettings).isGradualRolloutEnabledForAbTesting();

    final Date staticDate = Date.from(Instant.parse("2025-04-01T00:00:00Z"));
    doReturn(staticDate).when(_abTestSvc).getNow();

    final ObjectId testGroupId1 = oid(2);
    final ObjectId testGroupId2 = oid(3);
    final List<Assignment> expectedAssignments =
        Arrays.asList(
            Assignment.builder()
                .id(null)
                .testId(GROUP_ID_1)
                .entityId(oid(1))
                .entityType(EntityType.USER)
                .testGroupName("control")
                .testGroupDatabaseId(testGroupId1)
                .testName(testName1)
                .assignmentDate(staticDate)
                .tag(_tag1)
                .meta(new AssignmentMetaData(false))
                .build(),
            Assignment.builder()
                .id(null)
                .testId(GROUP_ID_2)
                .entityId(oid(1))
                .entityType(EntityType.USER)
                .testGroupName("experimental")
                .testGroupDatabaseId(testGroupId2)
                .testName(testName2)
                .assignmentDate(staticDate)
                .tag(_tag2)
                .meta(new AssignmentMetaData(true))
                .build(),
            Assignment.builder()
                .id(null)
                .testId(oid(4))
                .entityId(oid(1))
                .entityType(EntityType.USER)
                .testGroupName(null)
                .testGroupDatabaseId(null)
                .testName(testName4)
                .assignmentDate(staticDate)
                .tag(_tag3)
                .meta(new AssignmentMetaData(false))
                .gradualRolloutAssignmentValue(50)
                .build(),
            Assignment.builder()
                .id(null)
                .testId(oid(5))
                .entityId(oid(1))
                .entityType(EntityType.USER)
                .testGroupName("experimental")
                .testGroupDatabaseId(testGroupId2)
                .testName(testName5)
                .assignmentDate(staticDate)
                .tag(_tag4)
                .meta(new AssignmentMetaData(false))
                .build());
    //    not using top-level mocks as these have specific group/scope allocations
    final List<ABTest> abTests =
        Arrays.asList(
            new ABTest(
                GROUP_ID_1,
                testName1,
                "all users are assigned to control",
                List.of(
                    new TestGroup("control", testGroupId1, 100, true, null),
                    new TestGroup("experimental", testGroupId2, 0, false, null)),
                EntityType.USER,
                100,
                "jiraLink",
                _tag1,
                experimentTypeAB,
                List.of(new ObjectId().toString()),
                null),
            new ABTest(
                GROUP_ID_2,
                testName2,
                "all users are assigned to experimental",
                List.of(
                    new TestGroup("control", testGroupId1, 0, true, null),
                    new TestGroup("experimental", testGroupId2, 100, false, null)),
                EntityType.USER,
                100,
                "jiraLink",
                _tag2,
                experimentTypeAB,
                List.of(new ObjectId().toString()),
                ExperimentStatus.LAUNCHED),
            new ABTest(
                GROUP_ID_3,
                testName3,
                "no assignments generated for zero scope percentage",
                List.of(
                    new TestGroup("control", new ObjectId(), 50, true, null),
                    new TestGroup("experimental", new ObjectId(), 50, false, null)),
                EntityType.USER,
                0,
                "jiraLink",
                _tag2,
                experimentTypeAB,
                List.of(new ObjectId().toString()),
                null),
            new ABTest(
                oid(4),
                testName4,
                "null assignments w/ \"gradualRolloutAssignmentValue\" generated for zero gradual"
                    + " rollout percentage",
                List.of(
                    new TestGroup("control", testGroupId1, 50, true, null),
                    new TestGroup("experimental", testGroupId2, 50, false, null)),
                EntityType.USER,
                100,
                0,
                "jiraLink",
                _tag3,
                experimentTypeAB,
                List.of(),
                ExperimentStatus.LIVE),
            new ABTest(
                oid(5),
                testName5,
                "all users are assigned to experimental",
                List.of(
                    new TestGroup("control", testGroupId1, 0, true, null),
                    new TestGroup("experimental", testGroupId2, 100, false, null)),
                EntityType.USER,
                100,
                100,
                "jiraLink",
                _tag4,
                experimentTypeAB,
                List.of(),
                ExperimentStatus.LIVE));
    final List<Assignment> assignments =
        _abTestSvc.assignEntityToTests(_user, abTests, new ArrayList<>(), null, null);
    // the order of the assignments is not guaranteed since we are creating a map with tags as keys
    assertEquals(new HashSet<>(expectedAssignments), new HashSet<>(assignments));
    verify(_abTestSvc, times(1)).incrementTestAllocationCounter(abTests.size());
    verify(_abTestSvc, times(2)).incrementTestAllocationSuccessCounter(false, false);
    verify(_abTestSvc, times(2)).incrementTestAllocationSuccessCounter(false, true);
  }

  // this tests assignEntityToValidTestGroup, assignEntityToTestGroup, createAssignment,
  // saveAssignment
  @ParameterizedTest
  @MethodSource("provideArgumentsForAssignEntityToTestGroup")
  public void assignEntityToTestGroup(
      Entity testEntity, AppUser expectedUser, Organization expectedOrg, Group expectedGroup) {
    final ABTest mockAbTest =
        ABTest.builder()
            .id(_testid1)
            .testName(_testName1)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 50, true, null),
                    new TestGroup(
                        _testGroupName1, _testGroupDatabaseId1, 50, featureFlags1, false, null)))
            .description("desc1")
            .scope(testEntity.getEntityType())
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag1)
            .allocationPoints(List.of(_allocationPoint))
            .experimentStatus(ExperimentStatus.COOLDOWN)
            .build();
    doReturn(Optional.of(TestGroup.builder().testGroupName("control").percent(100).build()))
        .when(_abTestSvc)
        .chooseTestGroup(mockAbTest);
    when(_userSvc.findById(any())).thenReturn(expectedUser);
    when(_orgSvc.findById(any())).thenReturn(expectedOrg);
    when(_groupSvc.findById(any())).thenReturn(expectedGroup);

    final Instant now = initNewDateFixture(_abTestSvc);

    _abTestSvc.assignEntityToValidTestGroup(testEntity, mockAbTest, null, null);

    final ArgumentCaptor<Assignment> newAssignmentCaptor =
        ArgumentCaptor.forClass(Assignment.class);
    verify(_assignmentDao, atLeastOnce()).save(newAssignmentCaptor.capture());
    final Assignment newAssignment = newAssignmentCaptor.getValue();

    assertEquals("control", newAssignment.getTestGroupName());
    assertEquals(testEntity.getEntityType(), newAssignment.getEntityType());
    assertEquals(testEntity.getId(), newAssignment.getEntityId());
    assertTrue(newAssignment.getAssignmentDate().before(Date.from(now)));
    verify(_abTestSvc, atLeastOnce()).incrementTestAllocationSuccessCounter(false, false);
    verify(_abTestSvc, times(0)).incrementTestAllocationErrorCounter(false, false);

    // meta isLaunched should be false for real assignment
    AssignmentMetaData metaData = newAssignment.getMeta();
    assertFalse(metaData.getIsLaunchedExperiment());

    final ArgumentCaptor<TestAssignmentAllocatedEvent> taaEventCaptor =
        ArgumentCaptor.forClass(TestAssignmentAllocatedEvent.class);
    verify(_segmentEventSvc, atLeastOnce()).submitEvent(taaEventCaptor.capture());
    final TestAssignmentAllocatedEvent taaEvent = taaEventCaptor.getValue();

    assertEquals(EventName.REAL_ASSIGNMENT.toString(), taaEvent.getEvent());
    assertEquals(testEntity.getId(), taaEvent.getProperties().getEntityId());
    assertEquals(testEntity.getEntityType().toString(), taaEvent.getProperties().getEntityType());
    assertEquals(_testName1, taaEvent.getProperties().getTestName());
    assertEquals("control", taaEvent.getProperties().getTestGroupName());
    assertEquals("test_testName1_variant_control", taaEvent.getProperties().getTestAssignment());
    assertNull(taaEvent.getProperties().getSource());

    // Validate userId, organizationId, groupId, and orgCreatorEmail on segment event
    assertEquals(
        expectedUser != null ? expectedUser.getId() : null, taaEvent.getProperties().getUserId());
    assertEquals(
        expectedOrg != null ? expectedOrg.getId() : null,
        taaEvent.getProperties().getOrganizationId());
    assertEquals(
        expectedGroup != null ? expectedGroup.getId() : null,
        taaEvent.getProperties().getGroupId());
    if (testEntity.getEntityType() == EntityType.ORG) {
      assertEquals(expectedUser.getPrimaryEmail(), taaEvent.getProperties().getOrgCreatorEmail());
    }
  }

  @Test
  public void assignEntityToTestGroupFailure() {
    doReturn(Optional.empty()).when(_abTestSvc).chooseTestGroup(any());
    doNothing().when(_abTestSvc).incrementChooseNewAssignmentErrorCounter(false);

    _abTestSvc.assignEntityToValidTestGroup(_user, test1, null, null);
    verify(_abTestSvc, times(1)).incrementChooseNewAssignmentErrorCounter(false);
  }

  @Test
  public void assignEntityToTestGroup_duplicateKeyException_notHoldoutTest() {
    final ObjectId userId = oid(1);

    doReturn(Optional.of(TestGroup.builder().testGroupName("control").percent(100).build()))
        .when(_abTestSvc)
        .chooseTestGroup(test1);
    when(_user.getId()).thenReturn(userId);
    when(_user.getEntityType()).thenReturn(EntityType.USER);

    // Duplicate key
    final WriteError mongoWriteError =
        new WriteError(DUPLICATE_KEY_ERROR_CODE, "Mock duplicate error", new BsonDocument());
    final MongoWriteException mongoWriteException =
        new MongoWriteException(mongoWriteError, null, Collections.emptySet());

    doThrow(mongoWriteException).when(_assignmentDao).save(any());

    // existing assignment
    doReturn(List.of(assignment1)).when(_assignmentDao).find(List.of(test1.getId()), userId);

    // test1 is an AB type test
    _abTestSvc.assignEntityToValidTestGroup(_user, test1, null, null);

    // confirm we log a warning and increment our counter
    verify(mockAppender, atLeastOnce()).doAppend(captorLoggingEvent.capture());
    final LoggingEvent loggingEvent = captorLoggingEvent.getValue();
    assertThat(loggingEvent.getLevel(), is(Level.WARN));

    verify(_abTestSvc, times(1)).incrementDuplicateKeyCounter();

    // this doesn't count as a success or an error
    verify(_abTestSvc, times(0)).incrementTestAllocationSuccessCounter(false, false);
    verify(_abTestSvc, times(0)).incrementTestAllocationErrorCounter(false, false);

    verify(_segmentEventSvc, times(0)).submitEvent(any());
  }

  @Test
  public void assignEntityToTestGroup_duplicateKeyException_isHoldoutTest() {
    doReturn(
            Optional.of(
                TestGroup.builder().testGroupName(_holdoutTestVariantName).percent(100).build()))
        .when(_abTestSvc)
        .chooseTestGroup(holdoutTestLive);

    _abTestSvc.assignEntityToValidTestGroup(_user, holdoutTestLive, null, holdoutTestLive.getId());

    // confirm we DO NOT log a warning and increment our counter
    verify(mockAppender, never()).doAppend(captorLoggingEvent.capture());
    verify(_abTestSvc, times(0)).incrementDuplicateKeyCounter();

    // this doesn't count as a success or an error
    verify(_abTestSvc, times(0)).incrementTestAllocationSuccessCounter(false, false);
    verify(_abTestSvc, times(0)).incrementTestAllocationErrorCounter(false, false);

    verify(_segmentEventSvc, times(0)).submitEvent(any());
  }

  @Test
  public void assignEntityToValidTestGroup_withOrganization() {
    final Organization organization = mock(Organization.class);
    final String email = "<EMAIL>";
    final ObjectId testGroupId = new ObjectId();
    final TestGroup testGroup =
        TestGroup.builder().testGroupName("control").testGroupId(testGroupId).percent(0).build();

    final Date now = new Date();

    doReturn(now).when(_abTestSvc).getNow();

    doReturn(Optional.of(testGroup)).when(_abTestSvc).chooseTestGroup(test3);
    when(organization.getId()).thenReturn(oid(1));
    when(organization.getEntityType()).thenReturn(EntityType.ORG);
    when(organization.getCreator()).thenReturn(oid(2));
    when(organization.getCreated()).thenReturn(now);
    when(_segmentEventUtil.buildSegmentGroupEvent(any(), any(), any(), any())).thenCallRealMethod();
    when(_segmentEventUtil.getBasicGroupEventOrgTraits(organization)).thenCallRealMethod();
    when(_userSvc.findById(any())).thenReturn(_user);
    when(_user.getPrimaryEmail()).thenReturn(email);

    _abTestSvc.assignEntityToValidTestGroup(organization, test3, null, null);
    verify(_segmentEventUtil)
        .buildSegmentGroupEvent(
            eq(organization), eq(_user), eq(null), eq(ORGANIZATION_AB_TEST_ASSIGNED));

    final ArgumentCaptor<SegmentEvent> parameters = ArgumentCaptor.forClass(SegmentEvent.class);
    verify(_segmentEventSvc, times(2)).submitEvent(parameters.capture());
    List<SegmentEvent> values = parameters.getAllValues();

    // Segment group event
    final SegmentGroupEvent groupEvent = (SegmentGroupEvent) values.get(0);
    assertEquals("group", groupEvent.getType());
    assertEquals(organization.getId(), groupEvent.getTraits().getOrganizationId());
    assertEquals(organization.getCreator(), groupEvent.getTraits().getOrgCreatorId());
    assertThat("Amplitude integration exists", groupEvent.getIntegrations().getAmplitude() != null);
    assertThat(
        "Google Analytics integration exists",
        groupEvent.getIntegrations().getGoogleAnalytics() != null);

    // Segment track event
    final TestAssignmentAllocatedEvent taaEvent = (TestAssignmentAllocatedEvent) values.get(1);
    assertEquals(EventName.REAL_ASSIGNMENT.toString(), taaEvent.getEvent());
    assertEquals(oid(1), taaEvent.getProperties().getEntityId());
    assertEquals(EntityType.ORG.toString(), taaEvent.getProperties().getEntityType());
    assertEquals(_testName3, taaEvent.getProperties().getTestName());
    assertEquals(testGroup.getTestGroupName(), taaEvent.getProperties().getTestGroupName());
    assertEquals("test_orgTest_variant_control", taaEvent.getProperties().getTestAssignment());
    assertNull(taaEvent.getProperties().getSource());
    assertEquals(oid(2), taaEvent.getProperties().getUserId());
    assertEquals(oid(1), taaEvent.getProperties().getOrganizationId());
    assertEquals(email, taaEvent.getProperties().getOrgCreatorEmail());
  }

  @Test
  public void assignEntityToNullTestGroup() {
    final ABTest test = mock(ABTest.class);

    when(test.getId()).thenReturn(oid(1));
    when(_user.getId()).thenReturn(oid(1));
    when(_user.getEntityType()).thenReturn(EntityType.USER);

    _abTestSvc.assignEntityToNullTestGroup(_user, test, null);
    final ArgumentCaptor<Assignment> newAssignmentCaptor =
        ArgumentCaptor.forClass(Assignment.class);
    verify(_assignmentDao).save(newAssignmentCaptor.capture());
    final Assignment newAssignment = newAssignmentCaptor.getValue();
    assertNull(newAssignment.getTestGroupName());
    assertEquals(EntityType.USER, newAssignment.getEntityType());
    assertEquals(oid(1), newAssignment.getEntityId());
  }

  @ParameterizedTest
  @MethodSource("provideArgumentsForAssignEntityToTestGroup")
  public void assignEntityToTestGroup_ghost(
      Entity testEntity, AppUser expectedUser, Organization expectedOrg, Group expectedGroup) {
    final ABTest mockHoldoutTest =
        ABTest.builder()
            .id(_holdoutTestId1)
            .experimentType(ExperimentType.HOLDOUT)
            .experimentStatus(ExperimentStatus.LIVE)
            .testName(_holdoutTestName1)
            .testGroups(
                List.of(
                    new TestGroup(
                        _holdoutTestControlName, _holdoutTestGroupDatabaseId1, 5, true, null),
                    new TestGroup(
                        _holdoutTestVariantName, _holdoutTestGroupDatabaseId2, 95, false, null)))
            .description("holdout test 1")
            .scope(testEntity.getEntityType())
            .scopePercentage(100)
            .jiraLink("")
            .tag(_holdoutTag)
            .build();
    final ABTest mockAbTest =
        ABTest.builder()
            .id(_testid1)
            .testName(_testName1)
            .experimentType(ExperimentType.AB)
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 50, true, null),
                    new TestGroup(
                        _testGroupName1, _testGroupDatabaseId1, 50, featureFlags1, false, null)))
            .description("desc1")
            .scope(testEntity.getEntityType())
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag1)
            .allocationPoints(List.of(_allocationPoint))
            .experimentStatus(ExperimentStatus.LIVE)
            .build();
    doReturn(Optional.of(TestGroup.builder().testGroupName("control").percent(100).build()))
        .when(_abTestSvc)
        .chooseTestGroup(mockAbTest);
    doReturn(Optional.of(mockHoldoutTest)).when(_abTestDao).getTest(_holdoutTestId1);
    when(_userSvc.findById(any())).thenReturn(expectedUser);
    when(_orgSvc.findById(any())).thenReturn(expectedOrg);
    when(_groupSvc.findById(any())).thenReturn(expectedGroup);

    final Instant now = initNewDateFixture(_abTestSvc);

    _abTestSvc.assignEntityToValidTestGroup(testEntity, mockAbTest, null, _holdoutTestId1);

    final ArgumentCaptor<GhostAssignment> newAssignmentCaptor =
        ArgumentCaptor.forClass(GhostAssignment.class);
    verify(_ghostAssignmentDao).save(newAssignmentCaptor.capture());
    final GhostAssignment newAssignment = newAssignmentCaptor.getValue();

    assertEquals("control", newAssignment.getTestGroupName());
    assertEquals(testEntity.getEntityType(), newAssignment.getEntityType());
    assertEquals(testEntity.getId(), newAssignment.getEntityId());
    assertTrue(newAssignment.getAssignmentDate().before(Date.from(now)));
    verify(_abTestSvc, times(1)).incrementTestAllocationSuccessCounter(true, false);
    verify(_abTestSvc, times(0)).incrementTestAllocationErrorCounter(true, false);

    final ArgumentCaptor<TestAssignmentAllocatedEvent> taaEventCaptor =
        ArgumentCaptor.forClass(TestAssignmentAllocatedEvent.class);
    verify(_segmentEventSvc, atLeastOnce()).submitEvent(taaEventCaptor.capture());
    final TestAssignmentAllocatedEvent taaEvent = taaEventCaptor.getValue();

    assertEquals(EventName.GHOST_ASSIGNMENT.toString(), taaEvent.getEvent());
    assertEquals(testEntity.getId(), taaEvent.getProperties().getEntityId());
    assertEquals(testEntity.getEntityType().toString(), taaEvent.getProperties().getEntityType());
    assertEquals(_testName1, taaEvent.getProperties().getTestName());
    assertEquals("control", taaEvent.getProperties().getTestGroupName());
    assertEquals("test_testName1_variant_control", taaEvent.getProperties().getTestAssignment());
    assertNull(taaEvent.getProperties().getSource());
    assertEquals(
        expectedUser != null ? expectedUser.getId() : null, taaEvent.getProperties().getUserId());
    assertEquals(
        expectedOrg != null ? expectedOrg.getId() : null,
        taaEvent.getProperties().getOrganizationId());
    assertEquals(
        expectedGroup != null ? expectedGroup.getId() : null,
        taaEvent.getProperties().getGroupId());
    if (testEntity.getEntityType() == EntityType.ORG) {
      assertEquals(expectedUser.getPrimaryEmail(), taaEvent.getProperties().getOrgCreatorEmail());
    }
    assertEquals(mockHoldoutTest.getId(), taaEvent.getProperties().getLinkedHoldoutTestId());
    assertEquals(
        mockHoldoutTest.getTestName(), taaEvent.getProperties().getLinkedHoldoutTestName());
  }

  @Test
  public void assignEntityToTestGroup_ghost_failure() {
    doReturn(Optional.empty()).when(_abTestSvc).chooseTestGroup(any());
    doNothing().when(_abTestSvc).incrementChooseNewAssignmentErrorCounter(true);

    _abTestSvc.assignEntityToValidTestGroup(_user, test1, null, _holdoutTestId1);
    verify(_abTestSvc, times(1)).incrementChooseNewAssignmentErrorCounter(true);
  }

  @Test
  public void chooseTestGroup() {
    final ABTest test = mock(ABTest.class);
    final TestGroup allControlGroup = TestGroup.builder().percent(100).build();
    final TestGroup noControlGroup = TestGroup.builder().percent(0).build();
    final TestGroup experimentalGroup = TestGroup.builder().build();

    // Loop so we have less false-positive.
    for (int i = 0; i < 10; i++) {
      when(test.getTestGroups()).thenReturn(List.of(allControlGroup, experimentalGroup));
      assertEquals(Optional.of(allControlGroup), _abTestSvc.chooseTestGroup(test));
      when(test.getTestGroups()).thenReturn(List.of(noControlGroup, experimentalGroup));
      assertEquals(Optional.empty(), _abTestSvc.chooseTestGroup(test));
    }
  }

  @Test
  public void getAllNonNullAssignments() {
    final Assignment nullAssignment =
        Assignment.builder()
            .testId(test2.getId())
            .testGroupName(null)
            .testName(test2.getTestName())
            .entityType(EntityType.USER)
            .entityId(oid(1))
            .assignmentDate(new Date())
            .build();
    List<Assignment> assignments = List.of(assignment1, nullAssignment);
    doReturn(assignments).when(_abTestSvc).getAllAssignments(null, null, null, null);

    final List<Assignment> nonNullAssignments =
        _abTestSvc.getAllNonNullAssignments(null, null, null);
    assertEquals(Collections.singletonList(assignment1), nonNullAssignments);
  }

  @Test
  public void upsertTest() {
    doNothing().when(_abTestDao).upsertTest(test1);
    _abTestSvc.upsertTest(test1);
    verify(_abTestSvc, never()).incrementUpsertTestErrorCounter();
    clearInvocations(_abTestSvc);

    doThrow(new RuntimeException()).when(_abTestDao).upsertTest(test1);
    assertThrows(RuntimeException.class, () -> _abTestSvc.upsertTest(test1));
    verify(_abTestSvc, times(1)).incrementUpsertTestErrorCounter();
  }

  @Test
  public void deleteTest() {
    final String testIdString = _testid1.toString();
    doReturn(true).when(_abTestDao).deleteTest(testIdString);
    _abTestSvc.deleteTest(testIdString);
    verify(_abTestSvc, never()).incrementDeleteTestErrorCounter();
    verify(_overrideAssignmentDao, times(1)).deleteOverrideAssignmentsByTestId(testIdString);
    clearInvocations(_abTestSvc);
    clearInvocations(_overrideAssignmentDao);
    clearInvocations(_abTestDao);

    doThrow(new RuntimeException())
        .when(_overrideAssignmentDao)
        .deleteOverrideAssignmentsByTestId(testIdString);
    _abTestSvc.deleteTest(testIdString);
    verify(_abTestDao, times(1)).deleteTest(testIdString);
    verify(_abTestSvc, never()).incrementDeleteTestErrorCounter();
    clearInvocations(_abTestSvc);
    clearInvocations(_overrideAssignmentDao);

    doThrow(new RuntimeException()).when(_abTestDao).deleteTest(testIdString);
    assertThrows(RuntimeException.class, () -> _abTestSvc.deleteTest(testIdString));
    verify(_abTestSvc, times(1)).incrementDeleteTestErrorCounter();
    clearInvocations(_abTestSvc);
    clearInvocations(_overrideAssignmentDao);

    doReturn(false).when(_abTestDao).deleteTest(testIdString);
    _abTestSvc.deleteTest(testIdString);
    verify(_abTestSvc, times(1)).incrementDeleteTestErrorCounter();
  }

  @Test
  public void test_ensureAndVerifyNamedTestAssignment() {
    final Organization organization = mock(Organization.class);
    final String testGroupName = "testGroupName";
    final String testName = "testName";
    final List<String> testNames = List.of(testName);

    doReturn(Collections.emptyList())
        .when(_abTestSvc)
        .ensureAllAssignments(_user, null, organization, testNames);

    assertFalse(
        _abTestSvc.ensureAndVerifyNamedTestAssignment(
            _user, null, organization, testName, testGroupName));

    final Assignment testAssignment = mock(Assignment.class);
    final List<Assignment> testAssignments = List.of(testAssignment);

    doReturn(testAssignments)
        .when(_abTestSvc)
        .ensureAllAssignments(_user, null, organization, testNames);
    doReturn(null).when(testAssignments.get(0)).getTestGroupName();

    assertFalse(
        _abTestSvc.ensureAndVerifyNamedTestAssignment(
            _user, null, organization, testName, testGroupName));

    doReturn(testGroupName).when(testAssignments.get(0)).getTestGroupName();
    assertFalse(
        _abTestSvc.ensureAndVerifyNamedTestAssignment(
            _user, null, organization, testName, "notMyTest"));

    assertTrue(
        _abTestSvc.ensureAndVerifyNamedTestAssignment(
            _user, null, organization, testName, testGroupName));
  }

  @Test
  public void test_ensureAndVerifyEntityInNamedTestAssignment() {
    final Organization organization = mock(Organization.class);
    final String testName = "testName";
    final String testGroupName = "testGroupName";

    final List<String> activeTests = List.of(testName);
    doReturn(Collections.emptyList())
        .when(_abTestSvc)
        .ensureAssignmentsForEntity(organization, activeTests, null, null);

    assertFalse(
        _abTestSvc.ensureAndVerifyEntityInNamedTestAssignment(
            organization, testName, testGroupName, null));

    final Assignment testAssignment = mock(Assignment.class);
    final List<Assignment> testAssignments = List.of(testAssignment);

    doReturn(testAssignments)
        .when(_abTestSvc)
        .ensureAssignmentsForEntity(organization, activeTests, null, null);
    doReturn(null).when(testAssignments.get(0)).getTestGroupName();

    assertFalse(
        _abTestSvc.ensureAndVerifyEntityInNamedTestAssignment(
            organization, testName, testGroupName, null));

    doReturn(testGroupName).when(testAssignments.get(0)).getTestGroupName();
    assertFalse(
        _abTestSvc.ensureAndVerifyEntityInNamedTestAssignment(
            organization, testName, "notMyTest", null));

    assertTrue(
        _abTestSvc.ensureAndVerifyEntityInNamedTestAssignment(
            organization, testName, testGroupName, null));
  }

  @Test
  public void test_getNonNullAssignments() {
    final List<Assignment> returnedAssignments =
        _abTestSvc.getNonNullAssignments(List.of(assignment3, assignment4));
    assertEquals(1, returnedAssignments.size());
    assertEquals(assignment3, returnedAssignments.get(0));
  }

  @Test
  public void test_filterHoldoutAssignments() {
    doReturn(List.of(test1, holdoutTestLive, holdoutTestCooldown, holdoutTestComplete))
        .when(_abTestSvc)
        .getAllTests();

    final List<Assignment> input =
        List.of(
            assignment1,
            holdoutLiveAssignment,
            holdoutLiveAssignment2,
            assignment4,
            holdoutCompleteAssignment1,
            assignment3,
            holdoutCompleteAssignment2);
    final List<Assignment> result = _abTestSvc.filterHoldoutAssignments(input);
    assertEquals(result.size(), 4);
  }

  @Test
  public void test_getAllOverrideAssignments() {
    _abTestSvc.getAllOverrideAssignments();
    verify(_overrideAssignmentDao, times(1)).fetchAll();
  }

  @Test
  public void test_deleteOverrideAssignment() {
    final String testId = "testId";
    _abTestSvc.deleteOverrideAssignment(testId);
    verify(_overrideAssignmentDao, times(1)).deleteOverrideAssignment(testId);
  }

  @Test
  public void test_createOverrideAssignment_withNoErrors() throws SvcException {
    final OverrideAssignmentView testView = mock(OverrideAssignmentView.class);
    final OverrideAssignment convertedOA = mock(OverrideAssignment.class);
    final ObjectId mockUserId = new ObjectId();
    doReturn(Optional.of(fakeEmailAddress)).when(testView).getEmailAddress();
    when(testView.toOverrideAssignment()).thenReturn(convertedOA);
    when(_user.getId()).thenReturn(mockUserId);
    when(_userSvc.findByPrimaryEmail(fakeEmailAddress)).thenReturn(List.of(_user));
    doReturn(true).when(_authzSvc).isGlobalReadOnly(_user);

    _abTestSvc.createOverrideAssignment(testView);
    verify(_overrideAssignmentDao, times(1)).createOverrideAssignment(convertedOA);
  }

  @Test
  public void test_createOverrideAssignment_whenNoEmailIsProvided() {
    final OverrideAssignmentView testView = mock(OverrideAssignmentView.class);

    final SvcException thrown =
        assertThrows(
            SvcException.class,
            () -> {
              _abTestSvc.createOverrideAssignment(testView);
            });
    assertEquals(AdminABTestErrorCode.USER_NOT_FOUND, thrown.getErrorCode());
    assertEquals("No Email Address Provided", thrown.getMessage());
    verify(_overrideAssignmentDao, times(0)).createOverrideAssignment(any());
  }

  @Test
  public void test_assignEntityToValidTestGroup_getCmabAllocation_model_not_ready()
      throws ModelNotReadyException {
    doReturn(EntityType.ORG).when(_org).getEntityType();

    ABTest cmabTest =
        ABTest.builder()
            .id(_testid1)
            .testName(_testName1)
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 50, true, null),
                    new TestGroup(
                        _testGroupName1, _testGroupDatabaseId1, 50, featureFlags1, false, null)))
            .description("desc1")
            .scope(EntityType.USER)
            .scopePercentage(100)
            .jiraLink("")
            .tag(_tag1)
            .experimentType(ExperimentType.CMAB)
            .experimentStatus(ExperimentStatus.LIVE)
            .allocationPoints(List.of(_allocationPoint))
            .build();

    doThrow(new ModelNotReadyException("Model Not Ready"))
        .when(_cmabSvc)
        .getCmabAllocation(any(), any(), any());
    doReturn(Optional.of(TestGroup.builder().testGroupName("control").percent(100).build()))
        .when(_abTestSvc)
        .chooseTestGroup(cmabTest);

    // ensure a real random assignment is returned, despite the model being not ready
    final Optional<Assignment> assignment =
        _abTestSvc.assignEntityToValidTestGroup(_org, cmabTest, null, null);
    assertTrue(assignment.isPresent());
    assertEquals(assignment.get().getTestGroupName(), "control");

    // Now verify our logging interactions
    verify(mockAppender, atLeastOnce()).doAppend(captorLoggingEvent.capture());
    final LoggingEvent loggingEvent = captorLoggingEvent.getValue();
    assertThat(loggingEvent.getLevel(), is(Level.WARN));
  }

  @Test
  public void test_createOverrideAssignment_whenNoUserIsFound() {
    final OverrideAssignmentView testView = mock(OverrideAssignmentView.class);
    doReturn(Optional.of(fakeEmailAddress)).when(testView).getEmailAddress();
    when(_userSvc.findByPrimaryEmail(fakeEmailAddress)).thenReturn(List.of());

    final SvcException thrown =
        assertThrows(
            SvcException.class,
            () -> {
              _abTestSvc.createOverrideAssignment(testView);
            });
    assertEquals(AdminABTestErrorCode.USER_NOT_FOUND, thrown.getErrorCode());
    assertEquals(
        String.format("Unable to find user with email: %s", fakeEmailAddress), thrown.getMessage());
    verify(_overrideAssignmentDao, times(0)).createOverrideAssignment(any());
  }

  @Test
  public void test_createOverrideAssignment_whenMoreThanOneUserIsFound() {
    final OverrideAssignmentView testView = mock(OverrideAssignmentView.class);
    final AppUser secondUser = mock(AppUser.class);
    doReturn(Optional.of(fakeEmailAddress)).when(testView).getEmailAddress();
    when(_userSvc.findByPrimaryEmail(fakeEmailAddress)).thenReturn(List.of(_user, secondUser));

    final SvcException thrown =
        assertThrows(
            SvcException.class,
            () -> {
              _abTestSvc.createOverrideAssignment(testView);
            });
    assertEquals(AdminABTestErrorCode.USER_NOT_FOUND, thrown.getErrorCode());
    assertEquals(
        String.format("Multiple users found with email: %s", fakeEmailAddress),
        thrown.getMessage());
    verify(_overrideAssignmentDao, times(0)).createOverrideAssignment(any());
  }

  @Test
  public void test_createOverrideAssignment_whenUserDoesNotHavePermission() {
    final OverrideAssignmentView testView = mock(OverrideAssignmentView.class);
    final OverrideAssignment convertedOA = mock(OverrideAssignment.class);
    doReturn(Optional.of(fakeEmailAddress)).when(testView).getEmailAddress();
    when(_userSvc.findByPrimaryEmail(fakeEmailAddress)).thenReturn(List.of(_user));
    doReturn(false).when(_authzSvc).isGlobalReadOnly(_user);

    final SvcException thrown =
        assertThrows(
            SvcException.class,
            () -> {
              _abTestSvc.createOverrideAssignment(testView);
            });
    assertEquals(AdminABTestErrorCode.PERMISSION_ERROR, thrown.getErrorCode());
    assertEquals("User does not have role \"Global Read Only\"", thrown.getMessage());
    verify(_overrideAssignmentDao, times(0)).createOverrideAssignment(convertedOA);
  }

  @Test
  public void test_createOverrideAssignment_whenUserAndTestAlreadyAssigned() {
    final OverrideAssignmentView testView = mock(OverrideAssignmentView.class);
    final OverrideAssignment convertedOA = mock(OverrideAssignment.class);
    final ObjectId mockUserId = new ObjectId();
    doReturn(Optional.of(fakeEmailAddress)).when(testView).getEmailAddress();
    when(testView.toOverrideAssignment()).thenReturn(convertedOA);
    when(_user.getId()).thenReturn(mockUserId);
    when(_userSvc.findByPrimaryEmail(fakeEmailAddress)).thenReturn(List.of(_user));
    doReturn(true).when(_authzSvc).isGlobalReadOnly(_user);
    doThrow(MongoWriteException.class)
        .when(_overrideAssignmentDao)
        .createOverrideAssignment(convertedOA);

    final SvcException thrown =
        assertThrows(
            SvcException.class,
            () -> {
              _abTestSvc.createOverrideAssignment(testView);
            });
    assertEquals(AdminABTestErrorCode.TEST_AND_USER_ALREADY_ASSIGNED, thrown.getErrorCode());
    assertEquals(
        "User already has an Override Assignment with the chosen Test", thrown.getMessage());
    verify(_overrideAssignmentDao, times(1)).createOverrideAssignment(convertedOA);
  }

  @Test
  public void test_updateOverrideAssignmentTestGroup() {
    final String testId = "testId";
    final OverrideAssignmentView testView = mock(OverrideAssignmentView.class);
    final Optional<String> mockTestGroupName = Optional.of("mockTestGroupName");
    final Optional<String> mockTestGroupDatabaseId = Optional.of((new ObjectId()).toString());

    doReturn(mockTestGroupName).when(testView).getTestGroupName();
    doReturn(mockTestGroupDatabaseId).when(testView).getTestGroupDatabaseId();

    _abTestSvc.updateOverrideAssignmentTestGroup(testId, testView);
    verify(_overrideAssignmentDao, times(1))
        .updateTestGroup(testId, mockTestGroupName.get(), mockTestGroupDatabaseId.get());
  }

  @Test
  public void testSocketExceptionGetAllTests() {
    doNothing().when(_abTestSvc).incrementSocketExceptionCounter();

    // Dao layer throws socket exception
    doThrow(new MongoSocketOpenException("Timed out", new ServerAddress()))
        .when(_abTestDao)
        .getAllTests();
    _abTestSvc.getAllTests();
    // Should increment counter
    verify(_abTestSvc, times(1)).incrementSocketExceptionCounter();
  }

  @Test
  public void testSocketExceptionGetAllAssignments() {
    doNothing().when(_abTestSvc).incrementSocketExceptionCounter();

    // Dao layer throws socket exception
    doThrow(new MongoSocketOpenException("Timed out", new ServerAddress()))
        .when(_assignmentDao)
        .find(any(), ArgumentMatchers.<List<ObjectId>>any());

    _abTestSvc.getAllAssignments(_user, null, null, false, null);
    // Should increment counter
    verify(_abTestSvc, times(1)).incrementSocketExceptionCounter();
  }

  @Test
  public void testSocketExceptionOverride() {
    doNothing().when(_abTestSvc).incrementSocketExceptionCounter();

    // Dao layer throws socket exception
    doThrow(new MongoSocketOpenException("Timed out", new ServerAddress()))
        .when(_overrideAssignmentDao)
        .findByUserId(any());

    _abTestSvc.getAllAssignments(_user, null, null, true, null);
    // Should increment counter twice--once in "ensureAndGetEntityHoldoutTestId", and once in
    // getAllAssignments
    verify(_abTestSvc, times(2)).incrementSocketExceptionCounter();
  }

  @Test
  public void test_assignUserByAllocationPoint() {

    doReturn(activeTests).when(_abTestSvc).getAllTests();

    doReturn(_testAssignments)
        .when(_abTestSvc)
        .ensureAllAssignments(_user, _group, _org, List.of(_testName1));

    final List<Assignment> assignments =
        _abTestSvc.assignEntityByAllocationPoint(
            _user, _group, _org, ABTestSvc.AllocationPoint.FIRST_TIME_CLOUD_LOGIN);

    verify(_abTestSvc, times(1)).ensureAllAssignments(_user, _group, _org, List.of(_testName1));
    assertEquals(_testAssignments, assignments);
  }

  @Test
  public void test_retrieveTestNamesFromTestIds() {
    doReturn(activeTests).when(_abTestSvc).getAllTests();

    final List<String> result =
        _abTestSvc.retrieveTestNamesFromTestIds(List.of(test1.getId(), test2.getId()));

    assertEquals(List.of(test1.getTestName(), test2.getTestName()), result);
  }

  @Test
  public void test_getAllAssignmentsAndGenerateResponse() {
    // calls getAllAssignments and getActiveFeatureFlags. since they have their own tests, stub them
    // out
    doReturn(_testAssignments)
        .when(_abTestSvc)
        .getAllAssignments(any(), any(), any(), any(), any());

    doReturn(new HashMap<String, String>())
        .when(_abTestSvc)
        .getActiveFeatureFlags(any(), any(), any(), any(), any());

    final AssignmentDataResponse response =
        _abTestSvc.getAllAssignmentsAndGenerateResponse(_user, _group, _org, null);

    assertEquals(List.of(assignment1, assignment2), response.data);
    assertEquals(new HashMap<String, String>(), response.experimentFeatureFlags);
  }

  @Test
  public void test_getAssignmentsAndGenerateResponse() {
    // calls getAssignments and getActiveFeatureFlags. since they have their own tests, stub them
    // out
    doReturn(List.of(assignment1))
        .when(_abTestSvc)
        .getAssignments(
            eq(_user), eq(_group), eq(_org), eq(true), eq(List.of(_testName1)), eq(null), any());

    doReturn(new HashMap<String, String>())
        .when(_abTestSvc)
        .getActiveFeatureFlags(any(), any(), any(), any(), any());

    final AssignmentDataResponse response =
        _abTestSvc.getAssignmentsAndGenerateResponse(
            _user, _group, _org, List.of(_testName1), null);

    assertEquals(List.of(assignment1), response.data);
    assertEquals(new HashMap<String, String>(), response.experimentFeatureFlags);
  }

  @Test
  public void test_getAssignmentsAndGenerateResponseForEntity() {
    final EntityForExperimentation entity =
        new EntityForExperimentation(_org.getId(), EntityType.ORG);
    final List<String> testNames = List.of(_testName3);

    final List<Assignment> expectedAssignments = List.of(assignment3);
    doReturn(expectedAssignments)
        .when(_abTestSvc)
        .getSpecificAssignmentsForEntityWithOverrides(
            eq(entity), eq(testNames), eq(_user), eq(null), any());

    final HashMap<String, String> expectedExperimentFeatures = new HashMap<String, String>();
    doReturn(expectedExperimentFeatures)
        .when(_abTestSvc)
        .getActiveFeatureFlagsForEntity(eq(entity), eq(_user), eq(null), any());

    final List<GhostAssignment> expectedGhostAssignments = List.of(ghostAssignment1);
    doReturn(expectedGhostAssignments)
        .when(_abTestSvc)
        .getAllNonNullGhostAssignmentsForEntityWithinActiveHoldout(
            eq(entity), eq(_user), eq(true), any());

    final AssignmentDataResponse expectedResponse =
        new AssignmentDataResponse(
            expectedAssignments, expectedExperimentFeatures, expectedGhostAssignments);

    final AssignmentDataResponse response =
        _abTestSvc.getAssignmentsAndGenerateResponseForEntity(entity, testNames, _user, null);

    assertEquals(expectedResponse.data, response.data);
    assertEquals(expectedResponse.experimentFeatureFlags, response.experimentFeatureFlags);
    assertEquals(expectedResponse.ghostAssignments, response.ghostAssignments);
  }

  @Test
  public void test_assignAndGenerateBatchResponse() {
    final ApiBatchTestAssignmentEntityView userEntity =
        new ApiBatchTestAssignmentEntityView(_user.getId(), _user.getEntityType());
    final ApiBatchTestAssignmentEntityView orgEntity =
        new ApiBatchTestAssignmentEntityView(_org.getId(), _org.getEntityType());
    final List<ApiBatchTestAssignmentEntityView> batchEntities =
        new ArrayList<>() {
          {
            add(userEntity);
            add(orgEntity);
          }
        };

    final List<ObjectId> testIds = List.of(test1.getId(), test2.getId(), test3.getId());
    final List<String> testNames =
        List.of(test1.getTestName(), test2.getTestName(), test3.getTestName());
    final SegmentEventAssignmentSource source = SegmentEventAssignmentSource.BATCH_API;

    doReturn(testNames).when(_abTestSvc).retrieveTestNamesFromTestIds(testIds);

    doReturn(List.of(assignment1, assignment2))
        .when(_abTestSvc)
        .ensureAssignmentsForEntityWithOverrides(userEntity, testNames, _user, source);
    doReturn(List.of(assignment3))
        .when(_abTestSvc)
        .ensureAssignmentsForEntityWithOverrides(orgEntity, testNames, _user, source);
    doReturn(new HashMap<String, String>())
        .when(_abTestSvc)
        .getActiveFeatureFlagsForEntity(any(), any(), any());

    final GenerateBatchAssignmentsResponse response =
        _abTestSvc.generateBatchAssignments(batchEntities, testIds, source, _user);

    final List<ABTestSvc.GenerateBatchAssignmentsResult> result = response.result;

    // ensure test assignments for user
    assertEquals(List.of(assignment1, assignment2), result.get(0).data);
    // ensure test assignments for org
    assertEquals(List.of(assignment3), result.get(1).data);
  }

  @Test
  public void test_assignAndGenerateBatchResponseWithException() {
    final ApiBatchTestAssignmentEntityView userEntity =
        new ApiBatchTestAssignmentEntityView(_user.getId(), _user.getEntityType());
    final ApiBatchTestAssignmentEntityView orgEntity =
        new ApiBatchTestAssignmentEntityView(_org.getId(), _org.getEntityType());
    final List<ApiBatchTestAssignmentEntityView> batchEntities =
        new ArrayList<>() {
          {
            add(userEntity);
            add(orgEntity);
          }
        };

    final List<ObjectId> testIds = List.of(test1.getId(), test2.getId(), test3.getId());
    final List<String> testNames =
        List.of(test1.getTestName(), test2.getTestName(), test3.getTestName());
    final SegmentEventAssignmentSource source = SegmentEventAssignmentSource.BATCH_API;

    final String exceptionMessage = "Error";
    final RuntimeException exception = new RuntimeException(exceptionMessage);

    doReturn(testNames).when(_abTestSvc).retrieveTestNamesFromTestIds(testIds);

    doThrow(exception)
        .when(_abTestSvc)
        .ensureAssignmentsForEntityWithOverrides(userEntity, testNames, _user, source);
    doReturn(List.of(assignment3))
        .when(_abTestSvc)
        .ensureAssignmentsForEntityWithOverrides(orgEntity, testNames, _user, source);
    doReturn(new HashMap<String, String>())
        .when(_abTestSvc)
        .getActiveFeatureFlagsForEntity(any(), any(), any());

    final GenerateBatchAssignmentsResponse response =
        _abTestSvc.generateBatchAssignments(batchEntities, testIds, source, _user);

    final List<ABTestSvc.GenerateBatchAssignmentsResult> result = response.result;

    // ensure first batch entity (user) had an exception
    assertEquals(exception.getMessage(), result.get(0).exception);
    // ensure test assignments for org despite exception
    assertEquals(List.of(assignment3), result.get(1).data);
  }

  @Test
  public void test_getAllNonNullGhostAssignmentsForEntityWithinActiveHoldout_withoutOverrides() {
    // includes a ghost assignment for the LIVE holdout test, 1 for the COOLDOWN holdout test, and 1
    // for a COMPLETE holdout test
    doReturn(List.of(ghostAssignment1, ghostAssignment2, ghostAssignmentOld))
        .when(_abTestSvc)
        .getAllNonNullGhostAssignmentsForEntity(_org);
    doReturn(List.of(holdoutTestLive)).when(_abTestSvc).getActiveTests(any());
    final List<GhostAssignment> result =
        _abTestSvc.getAllNonNullGhostAssignmentsForEntityWithinActiveHoldout(
            _org, _user, false, null);
    // the COOLDOWN holdout ghost assignment should be filtered out since it was not returned as an
    // active test in our mock
    assertEquals(1, result.size());
    assertEquals(_testid3, result.get(0).getTestId());
    assertEquals(_holdoutTestId1, result.get(0).getLinkedHoldoutTestId());
    assertEquals(_holdoutTestName1, result.get(0).getLinkedHoldoutTestName());
  }

  @Test
  public void test_getAllNonNullGhostAssignmentsForEntityWithinActiveHoldout_withOverrides() {
    // includes a ghost assignment for the LIVE holdout test, 1 for the COOLDOWN holdout test, and 1
    // for a COMPLETE holdout test
    doReturn(List.of(ghostAssignment1, ghostAssignment2, ghostAssignmentOld))
        .when(_abTestSvc)
        .getAllNonNullGhostAssignmentsForEntity(_org);
    doReturn(List.of(holdoutTestLive)).when(_abTestSvc).getActiveTests(any());
    doReturn(List.of(holdoutCompleteOverrideAssignment))
        .when(_abTestSvc)
        .getOverrideAssignmentsForUserId(any());
    doReturn(Optional.of(holdoutTestComplete))
        .when(_abTestDao)
        .getTest(holdoutCompleteOverrideAssignment.getTestId());

    final List<GhostAssignment> result =
        _abTestSvc.getAllNonNullGhostAssignmentsForEntityWithinActiveHoldout(
            _org, _user, true, null);

    // the COOLDOWN holdout ghost assignment should be filtered out since it was not returned as an
    // active test in our mock
    assertEquals(2, result.size());
    final GhostAssignment orgResult = result.get(0);
    assertEquals(_testid3, orgResult.getTestId());
    assertEquals(_holdoutTestId1, orgResult.getLinkedHoldoutTestId());
    assertEquals(_holdoutTestName1, orgResult.getLinkedHoldoutTestName());

    final GhostAssignment orgResultOldHoldoutGhostAssignment = result.get(1);
    assertEquals(_testid5, orgResultOldHoldoutGhostAssignment.getTestId());
    assertEquals(_holdoutTestId3, orgResultOldHoldoutGhostAssignment.getLinkedHoldoutTestId());
    assertEquals(_holdoutTestName3, orgResultOldHoldoutGhostAssignment.getLinkedHoldoutTestName());
  }

  @Test
  public void test_getAllNonNullGhostAssignmentsWithinActiveHoldout_withoutOverrides() {
    // includes a group ghost assignment, and also an org ghost assignment for the LIVE holdout
    // test, one for the COOLDOWN holdout test, and one for a COMPLETE holdout test
    doReturn(List.of(ghostAssignment3, ghostAssignment1, ghostAssignment2, ghostAssignmentOld))
        .when(_abTestSvc)
        .getAllNonNullGhostAssignmentsForEntities(any());
    doReturn(List.of(holdoutTestLive)).when(_abTestSvc).getActiveTestsForAllEntityTypes();
    final List<GhostAssignment> result =
        _abTestSvc.getAllNonNullGhostAssignmentsWithinActiveHoldout(_user, _group, _org, false);

    assertEquals(2, result.size());
    final GhostAssignment groupResult = result.get(0);
    assertEquals(_testid5, groupResult.getTestId());
    assertEquals(_holdoutTestId1, groupResult.getLinkedHoldoutTestId());
    assertEquals(_holdoutTestName1, groupResult.getLinkedHoldoutTestName());
    final GhostAssignment orgResult = result.get(1);
    assertEquals(_testid3, orgResult.getTestId());
    assertEquals(_holdoutTestId1, orgResult.getLinkedHoldoutTestId());
    assertEquals(_holdoutTestName1, orgResult.getLinkedHoldoutTestName());
  }

  @Test
  public void test_getAllNonNullGhostAssignmentsWithinActiveHoldout_withOverrides() {
    // includes a group ghost assignment, and also an org ghost assignment for the LIVE holdout
    // test, one for the COOLDOWN holdout test, and one for a COMPLETE holdout test
    doReturn(List.of(ghostAssignment3, ghostAssignment1, ghostAssignment2, ghostAssignmentOld))
        .when(_abTestSvc)
        .getAllNonNullGhostAssignmentsForEntities(any());
    doReturn(List.of(holdoutTestLive)).when(_abTestSvc).getActiveTestsForAllEntityTypes();
    doReturn(List.of(holdoutCompleteOverrideAssignment))
        .when(_abTestSvc)
        .getOverrideAssignmentsForUserId(any());
    doReturn(Optional.of(holdoutTestComplete))
        .when(_abTestDao)
        .getTest(holdoutCompleteOverrideAssignment.getTestId());

    final List<GhostAssignment> result =
        _abTestSvc.getAllNonNullGhostAssignmentsWithinActiveHoldout(_user, _group, _org, true);

    assertEquals(3, result.size());
    final GhostAssignment groupResult = result.get(0);
    assertEquals(_testid5, groupResult.getTestId());
    assertEquals(_holdoutTestId1, groupResult.getLinkedHoldoutTestId());
    assertEquals(_holdoutTestName1, groupResult.getLinkedHoldoutTestName());

    final GhostAssignment orgResult = result.get(1);
    assertEquals(_testid3, orgResult.getTestId());
    assertEquals(_holdoutTestId1, orgResult.getLinkedHoldoutTestId());
    assertEquals(_holdoutTestName1, orgResult.getLinkedHoldoutTestName());

    final GhostAssignment orgResultOldHoldoutGhostAssignment = result.get(2);
    assertEquals(_testid5, orgResultOldHoldoutGhostAssignment.getTestId());
    assertEquals(_holdoutTestId3, orgResultOldHoldoutGhostAssignment.getLinkedHoldoutTestId());
    assertEquals(_holdoutTestName3, orgResultOldHoldoutGhostAssignment.getLinkedHoldoutTestName());
  }

  @Test
  public void
      test_getAllNonNullLinkedGhostAssignmentsForHoldoutAssignments_entitiesHaveNoGhostAssignments() {
    final List<Assignment> holdoutAssignments =
        List.of(
            Assignment.builder()
                .testName(_holdoutTestName2)
                .entityId(oid(4)) // does not match id of any entity
                .entityType(EntityType.ORG)
                .testId(_holdoutTestId2) // matches linkedHoldoutTestId of ghostAssignment2
                .testGroupName(_holdoutTestVariantName)
                .testGroupDatabaseId(_holdoutTestGroupDatabaseId2)
                .tag(_holdoutTag)
                .assignmentDate(new Date())
                .build());
    final List<GhostAssignment> result =
        _abTestSvc.getAllNonNullLinkedGhostAssignmentsForHoldoutAssignments(
            _user, _group, _org, holdoutAssignments);

    verify(_abTestSvc, never()).getAllNonNullGhostAssignmentsForEntities(any());
    assertEquals(0, result.size());
  }

  @Test
  public void
      test_getAllNonNullLinkedGhostAssignmentsForHoldoutAssignments_entitiesHaveOnlyUnlinkedGhostAssignments() {
    doReturn(List.of(ghostAssignment1, ghostAssignment2))
        .when(_abTestSvc)
        .getAllNonNullGhostAssignmentsForEntities(any());

    final List<Assignment> holdoutAssignments =
        List.of(
            Assignment.builder()
                .testName(_holdoutTestName2)
                .entityId(oid(3)) // matches id of _org
                .entityType(EntityType.ORG)
                .testId(
                    _holdoutTestId3) // does not match linkedHoldoutTestId of any ghost assignment
                .testGroupName(_holdoutTestVariantName)
                .testGroupDatabaseId(_holdoutTestGroupDatabaseId2)
                .tag(_holdoutTag)
                .assignmentDate(new Date())
                .build());
    final List<GhostAssignment> result =
        _abTestSvc.getAllNonNullLinkedGhostAssignmentsForHoldoutAssignments(
            _user, _group, _org, holdoutAssignments);

    verify(_abTestSvc, times(1)).getAllNonNullGhostAssignmentsForEntities(any());
    assertEquals(0, result.size());
  }

  @Test
  public void
      test_getAllNonNullLinkedGhostAssignmentsForHoldoutAssignments_entitiesHaveLinkedGhostAssignments() {
    doReturn(List.of(ghostAssignment1, ghostAssignment2))
        .when(_abTestSvc)
        .getAllNonNullGhostAssignmentsForEntities(any());

    final List<Assignment> holdoutAssignments =
        List.of(
            Assignment.builder()
                .testName(_holdoutTestName2)
                .entityId(oid(3)) // matches id of _org
                .entityType(EntityType.ORG)
                .testId(_holdoutTestId2) // matches linkedHoldoutTestId of ghostAssignment2
                .testGroupName(_holdoutTestVariantName)
                .testGroupDatabaseId(_holdoutTestGroupDatabaseId2)
                .tag(_holdoutTag)
                .assignmentDate(new Date())
                .build());
    final List<GhostAssignment> result =
        _abTestSvc.getAllNonNullLinkedGhostAssignmentsForHoldoutAssignments(
            _user, _group, _org, holdoutAssignments);

    verify(_abTestSvc, times(1)).getAllNonNullGhostAssignmentsForEntities(any());
    assertEquals(1, result.size());
    assertEquals(result.get(0).getId(), ghostAssignment2.getId());
  }

  @Test
  public void test_getActiveExperimentGhostAssignmentsForEntity() {
    doReturn(EntityType.ORG).when(_org).getEntityType();
    doReturn(List.of(test3)).when(_abTestSvc).getActiveTestsForAllEntityTypes();
    doReturn(List.of(ghostAssignment1))
        .when(_abTestSvc)
        .getGhostAssignmentsForEntities(any(), any());
    final List<GhostAssignment> result =
        _abTestSvc.getActiveExperimentGhostAssignmentsForEntity(_org);
    verify(_abTestSvc, times(1)).getActiveTestsForAllEntityTypes();
    verify(_abTestSvc, times(1)).getActiveTestIds(any());
    assertEquals(1, result.size());
    assertEquals(ghostAssignment1, result.get(0));
  }

  @Test
  public void test_getAllNonNullGhostAssignmentsForEntity() {
    doReturn(List.of(ghostAssignment1, nullGhostAssignment))
        .when(_abTestSvc)
        .getActiveExperimentGhostAssignmentsForEntities(any());
    final List<GhostAssignment> result = _abTestSvc.getAllNonNullGhostAssignmentsForEntity(_org);
    assertEquals(1, result.size());
    assertEquals(_testGroupName2, result.get(0).getTestGroupName());
  }

  @Test
  public void test_getCurrentGhostAssignmentsForEntityForTags() {
    doReturn(EntityType.ORG).when(_org).getEntityType();
    doReturn(List.of(test3)).when(_abTestSvc).getActiveTests(EntityType.ORG);
    final List<String> tagList = List.of(test3.getTag());

    when(_ghostAssignmentDao.findByTag(tagList, _org.getId()))
        .thenReturn(List.of(ghostAssignment1));
    final List<Assignment> result =
        _abTestSvc.getCurrentGhostAssignmentsForEntityForTags(_org, tagList);
    verify(_org, times(1)).getEntityType();
    verify(_abTestSvc, times(1)).getActiveTests(EntityType.ORG);
    verify(_abTestSvc, times(1)).getActiveTestIds(any());
    assertEquals(1, result.size());
    assertEquals(ghostAssignment1, result.get(0));
    verify(_abTestSvc, times(0))
        .incrementGetAssignmentsErrorCounter("getCurrentGhostAssignmentsForEntityForTags");
  }

  @Test
  public void test_assignAndGenerateResponseForEntity() {
    final EntityForExperimentation entity =
        new EntityForExperimentation(_org.getId(), EntityType.ORG);
    final List<String> testNames = List.of(_testName1, _testName2, _testName3, _testName4);
    final SegmentEventAssignmentSource source = SegmentEventAssignmentSource.BATCH_API;

    final List<Assignment> expectedAssignments = List.of(assignment3, assignment4);
    doReturn(expectedAssignments)
        .when(_abTestSvc)
        .ensureAssignmentsForEntityWithOverrides(entity, testNames, _user, source);

    final Map<String, String> expectedFeatureFlags =
        featureFlags1.stream()
            .collect(
                Collectors.toMap(TestGroupFeatureFlag::getKey, TestGroupFeatureFlag::getValue));
    doReturn(expectedFeatureFlags)
        .when(_abTestSvc)
        .getActiveFeatureFlagsForEntity(entity, _user, source);

    final List<GhostAssignment> expectedGhostAssignments =
        List.of(ghostAssignment1, ghostAssignment2);
    doReturn(expectedGhostAssignments)
        .when(_abTestSvc)
        .getAllNonNullGhostAssignmentsForEntityWithinActiveHoldout(entity, _user, true, null);

    final AssignmentDataResponse response =
        _abTestSvc.assignAndGenerateResponseForEntity(entity, testNames, _user, source);
    final AssignmentDataResponse expectedResponse =
        new AssignmentDataResponse(
            expectedAssignments, expectedFeatureFlags, expectedGhostAssignments);

    assertEquals(expectedResponse.data, response.data);
    assertEquals(expectedResponse.experimentFeatureFlags, response.experimentFeatureFlags);
    assertEquals(expectedResponse.ghostAssignments, response.ghostAssignments);
  }

  // @todo clean up this test case in CLOUDP-309288 after cutting over to Gradual Rollout
  @Test
  public void test_shouldUseGradualRollout() {
    final ABTestBuilder baseBuilder =
        new ABTestBuilder()
            .testName("someTest")
            .tag("someTag")
            .scopePercentage(100)
            .scope(EntityType.ORG)
            .testGroups(
                List.of(
                    new TestGroup("control", oid(1), 50, true, null),
                    new TestGroup("experimental", oid(2), 50, false, null)));
    final List<HashMap<String, Object>> testScenarios =
        List.of(
            new HashMap<>() {
              {
                put(
                    "input",
                    baseBuilder
                        .experimentType(ExperimentType.AB)
                        .gradualRolloutPercentage(30)
                        .build());
                put("expected", true);
              }
            },
            new HashMap<>() {
              {
                put(
                    "input",
                    baseBuilder
                        .experimentType(ExperimentType.CMAB)
                        .gradualRolloutPercentage(50)
                        .build());
                put("expected", true);
              }
            },
            new HashMap<>() {
              {
                put(
                    "input",
                    baseBuilder
                        .experimentType(ExperimentType.HOLDOUT)
                        .gradualRolloutPercentage(100)
                        .build());
                put("expected", false);
              }
            },
            new HashMap<>() {
              {
                put(
                    "input",
                    baseBuilder
                        .experimentType(ExperimentType.AB)
                        .gradualRolloutPercentage(null)
                        .build());
                put("expected", false);
              }
            });

    for (final HashMap<String, Object> testScenario : testScenarios) {
      final ABTest test = (ABTest) testScenario.get("input");
      final boolean expected = (boolean) testScenario.get("expected");
      assertEquals(expected, _abTestSvc.shouldUseGradualRollout(test));
    }
  }

  @Test
  @SuppressWarnings("unchecked") // for the cast from Object to List<ABTest> and List<Assignment>
  public void test_getEligibleNullAssignmentsForRolloutConversion() {
    final Assignment assignmentWithTestGroup =
        Assignment.builder()
            .entityId(oid(1))
            .entityType(EntityType.CLUSTER)
            .testId(_testid1)
            .testGroupName(_testGroupName1)
            .testGroupDatabaseId(_testGroupDatabaseId1)
            .testName(_testName1)
            .tag(_tag1)
            .build();
    final Assignment nullAssignmentWithoutGradualRolloutAssignmentValue =
        Assignment.builder()
            .entityId(oid(1))
            .entityType(EntityType.CLUSTER)
            .testId(_testid2)
            .testGroupName(null)
            .testGroupDatabaseId(null)
            .testName(_testName2)
            .tag(_tag2)
            .build();
    final Assignment nullGhostAssignmentWithoutGradualRolloutAssignmentValue =
        GhostAssignment.builder()
            .entityId(oid(1))
            .entityType(EntityType.CLUSTER)
            .testId(_testid2)
            .testGroupName(null)
            .testGroupDatabaseId(null)
            .testName(_testName2)
            .tag(_tag2)
            .build();
    final Assignment nullAssignmentWithGradualRolloutAssignmentValue1 =
        Assignment.builder()
            .entityId(oid(1))
            .entityType(EntityType.CLUSTER)
            .testId(_testid3)
            .testGroupName(null)
            .testGroupDatabaseId(null)
            .testName(_testName3)
            .tag(_tag3)
            .gradualRolloutAssignmentValue(86)
            .build();
    final Assignment nullAssignmentWithGradualRolloutAssignmentValue2 =
        Assignment.builder()
            .entityId(oid(1))
            .entityType(EntityType.CLUSTER)
            .testId(_testid4)
            .testGroupName(null)
            .testGroupDatabaseId(null)
            .testName(_testName4)
            .tag(_tag4)
            .gradualRolloutAssignmentValue(33)
            .build();
    final GhostAssignment nullGhostAssignmentWithGradualRolloutAssignmentValue1 =
        GhostAssignment.builder()
            .entityId(oid(1))
            .entityType(EntityType.CLUSTER)
            .testId(_testid3)
            .testGroupName(null)
            .testGroupDatabaseId(null)
            .testName(_testName3)
            .tag(_tag3)
            .gradualRolloutAssignmentValue(40)
            .build();
    final GhostAssignment nullGhostAssignmentWithGradualRolloutAssignmentValue2 =
        GhostAssignment.builder()
            .entityId(oid(1))
            .entityType(EntityType.CLUSTER)
            .testId(_testid4)
            .testGroupName(null)
            .testGroupDatabaseId(null)
            .testName(_testName4)
            .tag(_tag4)
            .gradualRolloutAssignmentValue(77)
            .build();
    final List<Assignment> assignmentsInputForTestScenarios =
        List.of(
            assignmentWithTestGroup,
            nullAssignmentWithoutGradualRolloutAssignmentValue,
            nullGhostAssignmentWithoutGradualRolloutAssignmentValue,
            nullAssignmentWithGradualRolloutAssignmentValue1,
            nullAssignmentWithGradualRolloutAssignmentValue2,
            nullGhostAssignmentWithGradualRolloutAssignmentValue1,
            nullGhostAssignmentWithGradualRolloutAssignmentValue2);

    final ABTestBuilder baseAbTestBuilder1 =
        new ABTestBuilder()
            .id(_testid3)
            .testName(_testName3)
            .tag(_tag3)
            .scopePercentage(100)
            .experimentStatus(ExperimentStatus.LIVE)
            .scope(EntityType.CLUSTER)
            .testGroups(
                List.of(
                    new TestGroup("control", oid(1), 50, true, null),
                    new TestGroup("experimental", oid(2), 50, false, null)));

    final ABTestBuilder baseAbTestBuilder2 =
        new ABTestBuilder()
            .id(_testid4)
            .testName(_testName4)
            .tag(_tag4)
            .scopePercentage(100)
            .experimentStatus(ExperimentStatus.LIVE)
            .scope(EntityType.CLUSTER)
            .testGroups(
                List.of(
                    new TestGroup("control", oid(3), 50, true, null),
                    new TestGroup("experimental", oid(4), 50, false, null)));

    final List<HashMap<String, Object>> testScenarios =
        List.of(
            new HashMap<>() {
              {
                put("testTitle", "Empty tests list");
                put("testsInput", List.of());
                put("assignmentsInput", assignmentsInputForTestScenarios);
                put("expectedResult", List.of());
              }
            },
            new HashMap<>() {
              {
                put("testTitle", "No tests using gradual rollout");
                put("testsInput", List.of(baseAbTestBuilder1.build(), baseAbTestBuilder2.build()));
                put("assignmentsInput", assignmentsInputForTestScenarios);
                put("expectedResult", List.of());
              }
            },
            new HashMap<>() {
              {
                put("testTitle", "Some assignments eligible");
                put(
                    "testsInput",
                    List.of(
                        baseAbTestBuilder1.gradualRolloutPercentage(50).build(),
                        baseAbTestBuilder2.gradualRolloutPercentage(100).build()));
                put("assignmentsInput", assignmentsInputForTestScenarios);
                put(
                    "expectedResult",
                    List.of(
                        nullAssignmentWithGradualRolloutAssignmentValue2,
                        nullGhostAssignmentWithGradualRolloutAssignmentValue1,
                        nullGhostAssignmentWithGradualRolloutAssignmentValue2));
              }
            },
            new HashMap<>() {
              {
                put("testTitle", "All assignments eligible");
                put(
                    "testsInput",
                    List.of(
                        baseAbTestBuilder1.gradualRolloutPercentage(100).build(),
                        baseAbTestBuilder2.gradualRolloutPercentage(100).build()));
                put("assignmentsInput", assignmentsInputForTestScenarios);
                put(
                    "expectedResult",
                    List.of(
                        nullAssignmentWithGradualRolloutAssignmentValue1,
                        nullAssignmentWithGradualRolloutAssignmentValue2,
                        nullGhostAssignmentWithGradualRolloutAssignmentValue1,
                        nullGhostAssignmentWithGradualRolloutAssignmentValue2));
              }
            });

    for (final HashMap<String, Object> testScenario : testScenarios) {
      final List<ABTest> testsInput = (List<ABTest>) testScenario.get("testsInput");
      final List<Assignment> assignmentsInput =
          (List<Assignment>) testScenario.get("assignmentsInput");
      final List<Assignment> expectedResult = (List<Assignment>) testScenario.get("expectedResult");
      final Map<ObjectId, ABTest> testsById = _abTestSvc.convertTestsToMapById(testsInput);

      final List<Assignment> result =
          _abTestSvc.getEligibleNullAssignmentsForRolloutConversion(testsById, assignmentsInput);
      assertEquals(expectedResult, result);
    }
  }

  @Test
  public void test_setTestGroupOnAssignmentForEntity() {
    final EntityForExperimentation entity =
        new EntityForExperimentation(_org.getId(), EntityType.ORG);
    final EntityForExperimentation nonOrgEntity =
        new EntityForExperimentation(_user.getId(), EntityType.USER);

    final TestGroup testGroupControl =
        new TestGroup(_testGroupName1, _testGroupDatabaseId1, 50, true, null);
    final TestGroup testGroupVariant =
        new TestGroup(_testGroupName2, _testGroupDatabaseId2, 50, false, null);

    final ABTestBuilder baseBuilder =
        new ABTestBuilder()
            .id(_testid1)
            .testName(_testName1)
            .tag(_tag1)
            .scopePercentage(100)
            .gradualRolloutPercentage(100)
            .experimentStatus(ExperimentStatus.LIVE)
            .scope(EntityType.ORG)
            .testGroups(List.of(testGroupControl, testGroupVariant));

    final ABTest abTest = baseBuilder.experimentType(ExperimentType.AB).build();
    final ABTest cmabTest = baseBuilder.experimentType(ExperimentType.CMAB).build();

    final Assignment.Builder<?> assignmentBaseBuilder =
        Assignment.builder()
            .entityId(entity.getId())
            .entityType(entity.getEntityType())
            .testId(_testid1)
            .testName(_testName1)
            .tag(_tag1);

    final GhostAssignment.Builder ghostAssignmentBaseBuilder =
        GhostAssignment.builder()
            .entityId(entity.getId())
            .entityType(entity.getEntityType())
            .testId(_testid1)
            .testName(_testName1)
            .tag(_tag1);

    final Date staticDate = Date.from(Instant.parse("2025-04-01T00:00:00Z"));

    final List<Map<String, Object>> testScenarios =
        List.of(
            new HashMap<>() {
              {
                put("testTitle", "Process null assignment from AB Test");
                put("entityInput", entity);
                put(
                    "assignmentInput",
                    assignmentBaseBuilder.testGroupName(null).testGroupDatabaseId(null).build());
                put("testInput", abTest);
                put(
                    "expectedResult",
                    assignmentBaseBuilder
                        .testGroupName(testGroupVariant.getTestGroupName())
                        .testGroupDatabaseId(testGroupVariant.getTestGroupId())
                        .assignmentDate(staticDate)
                        .build());
              }
            },
            new HashMap<>() {
              {
                put("testTitle", "Process null ghost assignment from AB Test");
                put("entityInput", entity);
                put(
                    "assignmentInput",
                    ghostAssignmentBaseBuilder
                        .testGroupName(null)
                        .testGroupDatabaseId(null)
                        .build());
                put("testInput", abTest);
                put(
                    "expectedResult",
                    ghostAssignmentBaseBuilder
                        .testGroupName(testGroupVariant.getTestGroupName())
                        .testGroupDatabaseId(testGroupVariant.getTestGroupId())
                        .assignmentDate(staticDate)
                        .build());
              }
            },
            new HashMap<>() {
              {
                put("testTitle", "Process null assignment from CMAB Test");
                put("entityInput", entity);
                put(
                    "assignmentInput",
                    assignmentBaseBuilder.testGroupName(null).testGroupDatabaseId(null).build());
                put("testInput", cmabTest);
                put(
                    "expectedResult",
                    assignmentBaseBuilder
                        .testGroupName(testGroupControl.getTestGroupName())
                        .testGroupDatabaseId(testGroupControl.getTestGroupId())
                        .assignmentDate(staticDate)
                        .build());
              }
            },
            new HashMap<>() {
              {
                put("testTitle", "Process null ghost assignment from CMAB Test");
                put("entityInput", entity);
                put(
                    "assignmentInput",
                    ghostAssignmentBaseBuilder
                        .testGroupName(null)
                        .testGroupDatabaseId(null)
                        .build());
                put("testInput", cmabTest);
                put(
                    "expectedResult",
                    ghostAssignmentBaseBuilder
                        .testGroupName(testGroupControl.getTestGroupName())
                        .testGroupDatabaseId(testGroupControl.getTestGroupId())
                        .assignmentDate(staticDate)
                        .build());
              }
            },
            new HashMap<>() {
              {
                put("testTitle", "Unsupported entity type for CMAB Test");
                put("entityInput", nonOrgEntity);
                put(
                    "assignmentInput",
                    assignmentBaseBuilder
                        .entityId(nonOrgEntity.getId())
                        .entityType(nonOrgEntity.getEntityType())
                        .build());
                put("testInput", cmabTest);
                put(
                    "expectedResult",
                    assignmentBaseBuilder
                        .entityId(nonOrgEntity.getId())
                        .entityType(nonOrgEntity.getEntityType())
                        .build());
              }
            },
            new HashMap<>() {
              {
                put("testTitle", "CMAB model not ready");
                put("entityInput", entity);
                put(
                    "assignmentInput",
                    assignmentBaseBuilder.testGroupName(null).testGroupDatabaseId(null).build());
                put("testInput", cmabTest);
                put(
                    "expectedResult",
                    // When the model is not ready the test group will be chosen from the
                    // chooseTestGroup() method which is configured to return the variant test group
                    assignmentBaseBuilder
                        .testGroupName(testGroupVariant.getTestGroupName())
                        .testGroupDatabaseId(testGroupVariant.getTestGroupId())
                        .build());
              }
            },
            new HashMap<>() {
              {
                put("testTitle", "Test group could not be selected");
                put("entityInput", entity);
                put("assignmentInput", assignmentBaseBuilder.build());
                put("testInput", abTest);
                put("expectedResult", assignmentBaseBuilder.build());
              }
            });

    for (final Map<String, Object> testScenario : testScenarios) {
      final String testTitle = (String) testScenario.get("testTitle");
      final Entity entityInput = (Entity) testScenario.get("entityInput");
      final Assignment assignmentInput = (Assignment) testScenario.get("assignmentInput");
      final ABTest testInput = (ABTest) testScenario.get("testInput");
      final Assignment expectedResult = (Assignment) testScenario.get("expectedResult");

      doReturn(staticDate).when(_abTestSvc).getNow();
      doReturn(Optional.of(testGroupControl))
          .when(_cmabSvc)
          .getCmabAllocation(testInput.getId(), testInput.getTestGroups(), entityInput);
      doReturn(51.0).when(_abTestSvc).getRandomPercentage();

      if (testTitle.equals("CMAB model not ready")) {
        doThrow(new ModelNotReadyException())
            .when(_cmabSvc)
            .getCmabAllocation(testInput.getId(), testInput.getTestGroups(), entityInput);
      } else if (testTitle.equals("Test group could not be selected")) {
        doReturn(Optional.empty()).when(_abTestSvc).chooseTestGroup(testInput);
      }

      _abTestSvc.setTestGroupOnAssignmentForEntity(entityInput, assignmentInput, testInput);

      assertEquals(expectedResult, assignmentInput);
    }
  }

  @Test
  public void test_updateGradualRolloutAssignments() {
    final Date staticDate = Date.from(Instant.parse("2025-04-01T00:00:00Z"));

    final Assignment assignment1 =
        Assignment.builder()
            .id(oid(1))
            .entityId(_user.getId())
            .entityType(EntityType.USER)
            .testId(_testid1)
            .testName(_testName1)
            .tag(_tag1)
            .testGroupName(_testGroupName1)
            .testGroupDatabaseId(_testGroupDatabaseId1)
            .assignmentDate(staticDate)
            .build();
    final Assignment assignment2 =
        Assignment.builder()
            .id(oid(2))
            .entityId(_user.getId())
            .entityType(EntityType.USER)
            .testId(_testid2)
            .testName(_testName2)
            .tag(_tag2)
            .testGroupName(_testGroupName2)
            .testGroupDatabaseId(_testGroupDatabaseId2)
            .assignmentDate(staticDate)
            .build();
    final GhostAssignment ghostAssignment1 =
        GhostAssignment.builder()
            .id(oid(1))
            .entityId(_user.getId())
            .entityType(EntityType.USER)
            .testId(_testid2)
            .testName(_testName2)
            .tag(_tag2)
            .testGroupName(_testGroupName2)
            .testGroupDatabaseId(_testGroupDatabaseId2)
            .assignmentDate(staticDate)
            .linkedHoldoutTestId(_holdoutTestId1)
            .linkedHoldoutTestName(_holdoutTestName1)
            .build();
    final GhostAssignment ghostAssignment2 =
        GhostAssignment.builder()
            .id(oid(1))
            .entityId(_user.getId())
            .entityType(EntityType.USER)
            .testId(_testid2)
            .testName(_testName2)
            .tag(_tag2)
            .testGroupName(_testGroupName2)
            .testGroupDatabaseId(_testGroupDatabaseId2)
            .assignmentDate(staticDate)
            .linkedHoldoutTestId(_holdoutTestId1)
            .linkedHoldoutTestName(_holdoutTestName1)
            .build();

    final List<Assignment> assignmentsToUpdate =
        List.of(assignment1, ghostAssignment1, assignment2, ghostAssignment2);

    _abTestSvc.updateGradualRolloutAssignments(assignmentsToUpdate);

    verify(_assignmentDao, times(1))
        .updateTestGroupAssignmentsInBulk(List.of(assignment1, assignment2));
    verify(_abTestSvc, times(1)).incrementUpdateConvertedAssignmentsCounter("success", false);

    verify(_ghostAssignmentDao, times(1))
        .updateTestGroupAssignmentsInBulk(List.of(ghostAssignment1, ghostAssignment2));
    verify(_abTestSvc, times(1)).incrementUpdateConvertedAssignmentsCounter("success", true);
  }

  @Test
  public void test_updateGradualRolloutAssignments_mongoException() {
    doThrow(new MongoException("db operation failed"))
        .when(_assignmentDao)
        .updateTestGroupAssignmentsInBulk(any());

    doThrow(new MongoException("db operation failed"))
        .when(_ghostAssignmentDao)
        .updateTestGroupAssignmentsInBulk(any());

    _abTestSvc.updateGradualRolloutAssignments(List.of(assignment1, ghostAssignment1));

    verify(_assignmentDao, times(1)).updateTestGroupAssignmentsInBulk(List.of(assignment1));
    verify(_abTestSvc, times(1)).incrementUpdateConvertedAssignmentsCounter("error", false);

    verify(_ghostAssignmentDao, times(1))
        .updateTestGroupAssignmentsInBulk(List.of(ghostAssignment1));
    verify(_abTestSvc, times(1)).incrementUpdateConvertedAssignmentsCounter("error", true);

    verify(mockAppender, times(2)).doAppend(captorLoggingEvent.capture());

    final List<LoggingEvent> loggingEvents = captorLoggingEvent.getAllValues();

    final LoggingEvent loggingEvent1 = loggingEvents.get(0);
    assertThat(loggingEvent1.getLevel(), is(Level.ERROR));
    assertEquals(
        "mongodb error updating assignments in bulk, assignments={}", loggingEvent1.getMessage());

    final LoggingEvent loggingEvent2 = loggingEvents.get(1);
    assertThat(loggingEvent2.getLevel(), is(Level.ERROR));
    assertEquals(
        "mongodb error updating ghost assignments in bulk, ghostAssignments={}",
        loggingEvent2.getMessage());
  }

  @Test
  @SuppressWarnings("unchecked") // for the cast from Object to List<ABTest>
  public void test_getIFRExperiments() {
    final ABTest ifrTest1 =
        ABTest.builder()
            .id(oid(1))
            .testName("ifr_test_1")
            .scope(EntityType.CLUSTER)
            .scopePercentage(100)
            .tag("ifr_test_1")
            .experimentType(ExperimentType.IFR)
            .experimentStatus(ExperimentStatus.LIVE)
            .testGroups(defaultIfrTestGroups)
            .ifrRolloutConfig(
                IFRRolloutConfig.builder()
                    .rolloutType(IFRRolloutConfig.RolloutType.BINARY)
                    .mongoDBVersion("8.2.0")
                    .waveAllocationsPercentages(25, 50, 75, 100)
                    .liveWaveNumber(2)
                    .isRolloutPaused(false)
                    .fleetType(IFRRolloutConfig.FleetType.LTS)
                    .build())
            .build();

    final ABTest ifrTest2 =
        ABTest.builder()
            .id(oid(2))
            .testName("ifr_test_2")
            .scope(EntityType.CLUSTER)
            .scopePercentage(100)
            .tag("ifr_test_2")
            .experimentType(ExperimentType.IFR)
            .experimentStatus(ExperimentStatus.COMPLETE)
            .testGroups(defaultIfrTestGroups)
            .ifrRolloutConfig(
                IFRRolloutConfig.builder()
                    .rolloutType(IFRRolloutConfig.RolloutType.FEATURES)
                    .mongoDBVersion("8.0.0")
                    .waveAllocationsPercentages(50, 100)
                    .liveWaveNumber(1)
                    .isRolloutPaused(false)
                    .fleetType(IFRRolloutConfig.FleetType.RAPID_RELEASE)
                    .build())
            .build();

    final ABTest ifrTest3 =
        ABTest.builder()
            .id(oid(3))
            .testName("ifr_test_3")
            .scope(EntityType.CLUSTER)
            .scopePercentage(100)
            .tag("ifr_test_3")
            .experimentType(ExperimentType.IFR)
            .experimentStatus(ExperimentStatus.LIVE)
            .testGroups(defaultIfrTestGroups)
            .ifrRolloutConfig(
                IFRRolloutConfig.builder()
                    .rolloutType(IFRRolloutConfig.RolloutType.FEATURES)
                    .mongoDBVersion("7.0.0")
                    .waveAllocationsPercentages(5, 10, 100)
                    .liveWaveNumber(1)
                    .isRolloutPaused(false)
                    .fleetType(IFRRolloutConfig.FleetType.RAPID_RELEASE)
                    .build())
            .build();

    final ABTest draftIfrTest =
        ABTest.builder()
            .id(oid(3))
            .testName("draft_ifr_test")
            .scope(EntityType.CLUSTER)
            .scopePercentage(100)
            .tag("draft_ifr_test")
            .experimentType(ExperimentType.IFR)
            .experimentStatus(ExperimentStatus.DRAFT)
            .testGroups(defaultIfrTestGroups)
            .ifrRolloutConfig(
                IFRRolloutConfig.builder()
                    .rolloutType(IFRRolloutConfig.RolloutType.BINARY)
                    .mongoDBVersion("8.2.0")
                    .waveAllocationsPercentages(100)
                    .isRolloutPaused(false)
                    .fleetType(IFRRolloutConfig.FleetType.LTS)
                    .build())
            .build();

    final ABTest abTest =
        ABTest.builder()
            .id(oid(4))
            .testName("ab_test")
            .scope(EntityType.CLUSTER)
            .scopePercentage(100)
            .tag("ab_test")
            .experimentType(ExperimentType.AB)
            .experimentStatus(ExperimentStatus.LIVE)
            .testGroups(
                List.of(
                    new TestGroup("control", new ObjectId(), 50, true, null),
                    new TestGroup(
                        _testGroupName1, _testGroupDatabaseId1, 50, featureFlags1, false, null)))
            .build();

    final List<ABTest> allTests = List.of(ifrTest1, ifrTest2, ifrTest3, draftIfrTest, abTest);
    doReturn(allTests).when(_abTestDao).getAllTests();

    // Test different software types
    final List<Map<String, Object>> testScenarios =
        List.of(
            Map.of("softwareType", SoftwareType.MONGODB_8_2, "expectedTests", List.of(ifrTest1)),
            Map.of("softwareType", SoftwareType.MONGODB_8_0, "expectedTests", List.of(ifrTest2)),
            Map.of("softwareType", SoftwareType.MONGODB_7_0, "expectedTests", List.of(ifrTest3)),
            Map.of("softwareType", SoftwareType.MONGODB_6_0, "expectedTests", List.of()));

    for (final Map<String, Object> scenario : testScenarios) {
      final SoftwareType softwareType = (SoftwareType) scenario.get("softwareType");
      final List<ABTest> expectedTests = (List<ABTest>) scenario.get("expectedTests");

      final List<ABTest> result = _abTestSvc.getIFRExperiments(softwareType);

      assertEquals(expectedTests, result);
    }
  }

  @Test
  public void test_getIFRExperiments_exception() {
    // Test exception scenarios for different software types
    final List<SoftwareType> softwareTypesToTest =
        List.of(
            SoftwareType.MONGODB_8_2,
            SoftwareType.MONGODB_8_0,
            SoftwareType.MONGODB_7_0,
            SoftwareType.MONGODB_6_0);

    for (final SoftwareType softwareType : softwareTypesToTest) {
      // Mock dao to throw exception
      doThrow(new RuntimeException("Database connection failed")).when(_abTestDao).getAllTests();

      final List<ABTest> result = _abTestSvc.getIFRExperiments(softwareType);

      // Should return empty list on exception
      assertEquals(List.of(), result);

      // Verify error counter was incremented
      verify(_abTestSvc, times(1)).incrementGetIFRExperimentsErrorCounter(softwareType.name());

      // Reset the mock for next iteration
      reset(_abTestDao, _abTestSvc);
    }
  }

  @Test
  public void test_getIFRExperimentsRapidRelease() {
    final ABTest ltsTest =
        ABTest.builder()
            .id(oid(1))
            .testName("ifr_test_lts")
            .scope(EntityType.CLUSTER)
            .scopePercentage(100)
            .tag("ifr_test_lts")
            .experimentType(ExperimentType.IFR)
            .experimentStatus(ExperimentStatus.LIVE)
            .testGroups(defaultIfrTestGroups)
            .ifrRolloutConfig(
                IFRRolloutConfig.builder()
                    .rolloutType(IFRRolloutConfig.RolloutType.BINARY)
                    .mongoDBVersion("8.0.0")
                    .waveAllocationsPercentages(25, 50, 75, 100)
                    .liveWaveNumber(2)
                    .isRolloutPaused(false)
                    .fleetType(IFRRolloutConfig.FleetType.LTS)
                    .build())
            .build();

    final ABTest cdTest =
        ABTest.builder()
            .id(oid(2))
            .testName("ifr_test_cd")
            .scope(EntityType.CLUSTER)
            .scopePercentage(100)
            .tag("ifr_test_cd")
            .experimentType(ExperimentType.IFR)
            .experimentStatus(ExperimentStatus.COMPLETE)
            .testGroups(defaultIfrTestGroups)
            .ifrRolloutConfig(
                IFRRolloutConfig.builder()
                    .rolloutType(IFRRolloutConfig.RolloutType.FEATURES)
                    .mongoDBVersion("8.0.1")
                    .waveAllocationsPercentages(50, 100)
                    .liveWaveNumber(1)
                    .isRolloutPaused(false)
                    .fleetType(IFRRolloutConfig.FleetType.RAPID_RELEASE)
                    .build())
            .build();

    final List<ABTest> allTests = List.of(ltsTest, cdTest);
    doReturn(allTests).when(_abTestDao).getAllTests();

    final List<ABTest> cdTests =
        _abTestSvc.getIFRExperiments(SoftwareType.CONTINUOUS_DELIVERY_MONGODB_VERSION);
    assertEquals(1, cdTests.size());
    assertEquals(
        cdTest.getIFRRolloutConfig().getMongoDBVersion(),
        cdTests.get(0).getIFRRolloutConfig().getMongoDBVersion());
    assertEquals(FleetType.RAPID_RELEASE, cdTests.get(0).getIFRRolloutConfig().getFleetType());
  }

  /**
   * Ensure {@link ABTestSvc} returns at least two (guaranteed) increasing values before reverting
   * to the <code>new Date()</code> default
   */
  private static Instant initNewDateFixture(final ABTestSvc _abTestSvc) {
    final Instant now = new Date().toInstant();
    final Date firstInstant = Date.from(now.minusSeconds(2));
    final Date secondInstant = Date.from(now.minusSeconds(1));

    doReturn(firstInstant)
        .doReturn(secondInstant)
        .doReturn(Date.from(now))
        .doAnswer((mock) -> new Date())
        .when(_abTestSvc)
        .getNow();

    return now;
  }

  private void assertNonEntityGenericGetActiveFeatureFlags(
      final Map<String, String> expectedResult, final EntityType entityType) {
    Map<String, String> featureFlags;
    if (entityType.equals(EntityType.ORG)) {
      featureFlags = _abTestSvc.getActiveFeatureFlags(null, null, _org, null, null);
    } else {
      featureFlags = _abTestSvc.getActiveFeatureFlags(_user, null, null, null, null);
    }
    assertEquals(expectedResult, featureFlags);
  }

  // Parametrized input entity and expected outputs for assignEntityToTestGroup* tests
  // Arguments:
  //  - testEntity - the entity to be assigned to a test group
  //  - expectedUser - the expected user entity for segment event properties
  //  - expectedOrg - the expected org entity for segment event properties
  //  - expectedGroup - the expected group entity for segment event properties
  private static Stream<Arguments> provideArgumentsForAssignEntityToTestGroup() {
    final ObjectId userId = oid(1);
    final ObjectId orgId = oid(2);
    final ObjectId groupId = oid(3);

    final AppUser mockUser = mock(AppUser.class);
    doReturn(userId).when(mockUser).getId();
    doReturn(EntityType.USER).when(mockUser).getEntityType();
    doReturn(orgId).when(mockUser).getCurrentOrgId();
    doReturn("<EMAIL>").when(mockUser).getPrimaryEmail();

    final Organization mockOrg = mock(Organization.class);
    doReturn(orgId).when(mockOrg).getId();
    doReturn(EntityType.ORG).when(mockOrg).getEntityType();
    doReturn(userId).when(mockOrg).getCreator();

    final Group mockGroup = mock(Group.class);
    doReturn(groupId).when(mockGroup).getId();
    doReturn(EntityType.GROUP).when(mockGroup).getEntityType();
    doReturn(orgId).when(mockGroup).getOrgId();

    final EntityForExperimentation mockUserForExperimentation =
        new EntityForExperimentation(userId, EntityType.USER);
    final EntityForExperimentation mockOrgForExperimentation =
        new EntityForExperimentation(orgId, EntityType.ORG);
    final EntityForExperimentation mockGroupForExperimentation =
        new EntityForExperimentation(groupId, EntityType.GROUP);

    return Stream.of(
        Arguments.of(mockUser, mockUser, mockOrg, null),
        Arguments.of(mockOrg, mockUser, mockOrg, null),
        Arguments.of(mockGroup, null, mockOrg, mockGroup),
        Arguments.of(mockUserForExperimentation, mockUser, mockOrg, null),
        Arguments.of(mockOrgForExperimentation, mockUser, mockOrg, null),
        Arguments.of(mockGroupForExperimentation, null, mockOrg, mockGroup));
  }
}
