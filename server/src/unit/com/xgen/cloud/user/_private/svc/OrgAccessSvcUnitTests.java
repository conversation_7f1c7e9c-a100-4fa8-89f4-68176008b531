package com.xgen.cloud.user._private.svc;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.access.rolecheck._public.svc.GlobalUserTrackingSvc;
import com.xgen.cloud.access.usergroups._public.svc.MmsUserGroupSvc;
import com.xgen.cloud.activity._public.svc.alert.InformationalAlertSvc;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.alerts.alert._public.svc.AlertConfigSvc;
import com.xgen.cloud.authz.core._public.client.ActorClient;
import com.xgen.cloud.authz.core._public.utils.ConversionUtils;
import com.xgen.cloud.authz.core._public.view.ui.ActorView;
import com.xgen.cloud.authz.resource._public.client.ResourceClient;
import com.xgen.cloud.authz.resource._public.wrapper.ResourceClientProvider;
import com.xgen.cloud.authz.shared._public.exceptions.AuthzServiceClientErrorCode;
import com.xgen.cloud.authz.shared._public.exceptions.AuthzServiceClientException;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.authz._public.view.PolicyAssignmentView;
import com.xgen.cloud.common.authz._public.view.ResourceIdView;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.invitation._public.model.PolicyAssignment;
import com.xgen.cloud.invitation._public.model.ResourceId;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.services.authz.proto.GetResourcesResponse;
import com.xgen.cloud.services.authz.proto.ResourceMessage;
import com.xgen.cloud.services.common.proto.ResourceIdMessage;
import com.xgen.cloud.team._private.dao.TeamDao;
import com.xgen.cloud.team._public.model.Team;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class OrgAccessSvcUnitTests {
  private static final ObjectId ORG_ID = ObjectId.get();
  private static final ObjectId GROUP_ID = ObjectId.get();
  private static final ObjectId TEAM_ID = ObjectId.get();
  private static final ObjectId USER_ID = ObjectId.get();
  private static final String USERNAME = "<EMAIL>";
  private static final ObjectId OTHER_ORG_ID = ObjectId.get();
  private static final ObjectId OTHER_ORG_GROUP_ID = ObjectId.get();

  @Mock private UserDao mockUserDao;
  @Mock private AuditSvc mockAuditSvc;
  @Mock private InformationalAlertSvc mockInformationalAlertSvc;
  @Mock private AlertConfigSvc mockAlertConfigSvc;
  @Mock private GroupSvc mockGroupSvc;
  @Mock private TeamAccessSvc mockTeamAccessSvc;
  @Mock private TeamDao mockTeamDao;
  @Mock private AuthzSvc mockAuthzSvc;
  @Mock private OrganizationSvc mockOrgSvc;
  @Mock private GlobalUserTrackingSvc mockGlobalUserTrackingSvc;
  @Mock private UserRoleSvc mockUserRoleSvc;
  @Mock private ActorClient mockActorClient;
  @Mock private ResourceClient mockResourceClient;
  @Mock private ResourceClientProvider mockResourceClientProvider;

  private AppUser userDouble;
  @Mock private Organization mockOrg;
  @Mock private Group mockGroup;
  @Mock private Team mockTeam;
  @Mock private Organization mockOtherOrg;
  @Mock private Group mockOtherOrgGroup;
  @Mock private MmsUserGroupSvc mockMmsUserGroupSvc;

  private OrgAccessSvc orgAccessSvc;

  @BeforeEach
  public void setup() throws SvcException {
    when(mockOrg.getId()).thenReturn(ORG_ID);
    when(mockOrgSvc.validateOrg(ORG_ID)).thenReturn(mockOrg);

    when(mockGroup.getId()).thenReturn(GROUP_ID);
    when(mockGroup.getOrgId()).thenReturn(ORG_ID);
    when(mockGroupSvc.findById(mockGroup.getId())).thenReturn(mockGroup);
    when(mockGroupSvc.findByOrgId(ORG_ID, false)).thenReturn(List.of(mockGroup));

    when(mockTeam.getId()).thenReturn(TEAM_ID);
    when(mockTeam.getOrgId()).thenReturn(ORG_ID);
    when(mockTeamDao.findById(TEAM_ID)).thenReturn(mockTeam);
    when(mockTeamDao.findByOrgId(ORG_ID)).thenReturn(List.of(mockTeam));

    when(mockOtherOrg.getId()).thenReturn(OTHER_ORG_ID);
    when(mockOrgSvc.validateOrg(OTHER_ORG_ID)).thenReturn(mockOtherOrg);

    when(mockOtherOrgGroup.getId()).thenReturn(OTHER_ORG_GROUP_ID);
    when(mockOtherOrgGroup.getOrgId()).thenReturn(OTHER_ORG_ID);
    when(mockGroupSvc.findById(mockOtherOrgGroup.getId())).thenReturn(mockOtherOrgGroup);
    when(mockGroupSvc.findByOrgId(OTHER_ORG_ID, false)).thenReturn(List.of(mockOtherOrgGroup));

    userDouble = new AppUser();
    userDouble.setId(USER_ID);
    userDouble.setUsername(USERNAME);
    when(mockUserDao.findByUsername(USERNAME)).thenReturn(userDouble);

    when(mockResourceClientProvider.get()).thenReturn(mockResourceClient);

    orgAccessSvc =
        new OrgAccessSvc(
            mockOrgSvc,
            mockUserDao,
            mockAuditSvc,
            mockInformationalAlertSvc,
            mockAlertConfigSvc,
            mockGroupSvc,
            mockTeamAccessSvc,
            mockTeamDao,
            mockAuthzSvc,
            mockGlobalUserTrackingSvc,
            mockUserRoleSvc,
            mockActorClient,
            mockResourceClientProvider,
            mockMmsUserGroupSvc);
  }

  @Test
  void testAddUserToOrganization() throws SvcException {
    // GIVEN
    Set<Role> rolesToAdd = Set.of(Role.ORG_BILLING_ADMIN);
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();

    // WHEN
    orgAccessSvc.addUserToOrganization(userDouble, mockOrg.getId(), rolesToAdd, auditInfo);

    // THEN
    verify(mockUserDao).addOrgIdAndAssignRoles(userDouble.getId(), mockOrg.getId(), rolesToAdd);
    verify(mockUserDao).setCurrentOrgId(userDouble.getId(), mockOrg.getId());
    verify(mockUserRoleSvc)
        .processAccessChange(
            eq(userDouble),
            eq(Set.of(RoleAssignment.forOrg(Role.ORG_BILLING_ADMIN, ORG_ID))),
            eq(Collections.emptySet()));
  }

  @Test
  void addUserToOrganization_throwsException_whenGivenUserIsNull() {
    // GIVEN
    AppUser user = null;

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.addUserToOrganization(
                    user, ObjectId.get(), Set.of(Role.ORG_OWNER), AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.USERNAME_NOT_FOUND));
  }

  @Test
  void addUserToOrganization_throwsException_whenGivenUserIsDeleted() {
    // GIVEN
    userDouble.setDeleted(true);

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.addUserToOrganization(
                    userDouble,
                    ObjectId.get(),
                    Set.of(Role.ORG_OWNER),
                    AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.USER_DELETED));
  }

  @Test
  void addUserToOrganization_throwsException_whenUserAlreadyInOrg() {
    // GIVEN
    givenUserHasRoles(userDouble, Set.of(RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId())));

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.addUserToOrganization(
                    userDouble,
                    mockOrg.getId(),
                    Set.of(Role.ORG_OWNER),
                    AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.USER_ALREADY_IN_ORG));
  }

  @Test
  void addUserToOrganization_throwsException_whenGivenInvalidOrgRole() {
    // GIVEN
    var rolesToAdd = Set.of(Role.GROUP_READ_ONLY);

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.addUserToOrganization(
                    userDouble, mockOrg.getId(), rolesToAdd, AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.INVALID_ROLE_IN_ORG));
  }

  @Test
  void addUserToOrganization_throwsException_whenGivenChartsUser() {
    // GIVEN
    AppUser chartsUser = new AppUser();
    chartsUser.setUsername("<EMAIL>");
    when(mockUserDao.findByUsername(chartsUser.getUsername())).thenReturn(chartsUser);
    givenUserHasRoles(
        chartsUser, Set.of(RoleAssignment.forGroup(Role.GROUP_READ_ONLY, mockGroup.getId())));
    when(mockGroupSvc.findByIds(eq(List.of(mockGroup.getId())))).thenReturn(List.of(mockGroup));

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.addUserToOrganization(
                    chartsUser,
                    mockOrg.getId(),
                    Set.of(Role.ORG_OWNER),
                    AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.CANNOT_MODIFY_CHARTS_USER));
  }

  @Test
  void addUserRoleInOrganization() throws SvcException {
    // GIVEN
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();
    RoleAssignment roleAssignment = RoleAssignment.forOrg(Role.ORG_OWNER, mockOrg.getId());
    AppUser updatedUser = mock(AppUser.class);
    when(mockUserDao.addRoleAssignment(userDouble.getId(), roleAssignment)).thenReturn(updatedUser);
    // WHEN
    orgAccessSvc.addUserRoleInOrganization(userDouble, mockOrg.getId(), Role.ORG_OWNER, auditInfo);

    // THEN
    verify(mockUserDao).addRoleAssignment(userDouble.getId(), roleAssignment);
    verify(mockUserRoleSvc)
        .processAccessChange(
            eq(userDouble), eq(List.of(roleAssignment)), eq(Collections.emptyList()));
  }

  @Test
  void addUserRoleInOrganization_throwsException_whenUserAlreadyHasRole() {
    // GIVEN
    AppUser user = new AppUser();
    user.setUsername("<EMAIL>");
    user.setRoles(Set.of(RoleAssignment.forOrg(Role.ORG_OWNER, mockOrg.getId())));

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.addUserRoleInOrganization(
                    user, mockOrg.getId(), Role.ORG_OWNER, AuditInfoHelpers.fromInternal()));

    // THEN
    assertEquals(AppUserErrorCode.USER_ALREADY_HAS_ROLE, exception.getErrorCode());
  }

  @Test
  void addUserRoleInOrganization_throwsException_whenChartsUser() {
    // GIVEN
    AppUser chartsUser = new AppUser();
    chartsUser.setUsername("<EMAIL>");

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.addUserRoleInOrganization(
                    chartsUser, mockOrg.getId(), Role.ORG_OWNER, AuditInfoHelpers.fromInternal()));

    // THEN
    assertEquals(AppUserErrorCode.CANNOT_MODIFY_CHARTS_USER, exception.getErrorCode());
  }

  @Test
  void removeUserRoleInOrganization() throws SvcException {
    // GIVEN
    AppUser user = new AppUser();
    user.setUsername("<EMAIL>");
    user.setRoles(Set.of(RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId())));
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();
    RoleAssignment roleAssignment = RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId());
    when(mockUserDao.removeRoleAssignment(user.getId(), roleAssignment)).thenReturn(user);
    // WHEN
    orgAccessSvc.removeUserRoleInOrganization(user, mockOrg.getId(), Role.ORG_MEMBER, auditInfo);
    // THEN
    verify(mockUserDao).removeRoleAssignment(user.getId(), roleAssignment);
    verify(mockUserRoleSvc)
        .processAccessChange(eq(user), eq(Collections.emptyList()), eq(List.of(roleAssignment)));
  }

  @Test
  void removeUserRoleInOrganization_throwsException_whenChartsUser() {
    // GIVEN
    AppUser chartsUser = new AppUser();
    chartsUser.setUsername("<EMAIL>");
    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.removeUserRoleInOrganization(
                    chartsUser, mockOrg.getId(), Role.ORG_OWNER, AuditInfoHelpers.fromInternal()));

    // THEN
    assertEquals(AppUserErrorCode.CANNOT_MODIFY_CHARTS_USER, exception.getErrorCode());
  }

  @Test
  void removeUserRoleInOrganization_throwsException_whenUserDoesNotHaveRole() {
    // GIVEN
    AppUser userWithoutRole = new AppUser();
    userWithoutRole.setUsername("<EMAIL>");
    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.removeUserRoleInOrganization(
                    userWithoutRole,
                    mockOrg.getId(),
                    Role.ORG_OWNER,
                    AuditInfoHelpers.fromInternal()));
    // THEN
    assertEquals(AppUserErrorCode.USER_DOES_NOT_HAVE_ROLE, exception.getErrorCode());
  }

  @Test
  void testRemoveUserFromOrganization_noFgaAndUserGroupFeatureFlag() throws SvcException {
    // GIVEN
    var orgRoleToRemove = RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId());
    var roleInDifferentOrg = RoleAssignment.forOrg(Role.ORG_OWNER, ObjectId.get());

    var groupRoleToRemove = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, mockGroup.getId());
    var roleInDifferentGroup = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, ObjectId.get());

    var auditInfo = AuditInfoHelpers.fromInternal();

    givenUserHasRoles(
        userDouble,
        Set.of(orgRoleToRemove, roleInDifferentOrg, groupRoleToRemove, roleInDifferentGroup));

    Team teamNotInOrg = Mockito.mock(Team.class);
    when(teamNotInOrg.getId()).thenReturn(ObjectId.get());
    when(teamNotInOrg.getOrgId()).thenReturn(ObjectId.get());

    givenUserIsOnTeams(userDouble, Set.of(mockTeam, teamNotInOrg));
    givenTeamHasMemberCountOf(mockTeam.getId(), 2);

    when(mockActorClient.getActor(any())).thenReturn(new ActorView("", List.of(), Set.of()));
    when(mockOrgSvc.findById(any())).thenReturn(mockOrg);
    when(mockMmsUserGroupSvc.areFgaAndUserGroupsFeaturesEnabled(any())).thenReturn(false);

    // WHEN
    orgAccessSvc.removeUserFromOrganization(userDouble, mockOrg.getId(), auditInfo);

    // THEN

    // Assert user record updated
    verify(mockUserDao).removeOrgId(userDouble.getId(), mockOrg.getId());

    // Assert user access change post-processing
    verify(mockUserRoleSvc)
        .processAccessChange(
            eq(userDouble), eq(Collections.emptySet()), eq(Set.of(orgRoleToRemove)));

    // Assert alert configs cleaned
    verify(mockAlertConfigSvc)
        .removeUserAlertNotificationsForOrganization(mockOrg.getId(), USERNAME);

    // Assert user removed from org's groups
    verify(mockGroupSvc).doRemoveUserFromGroup(userDouble.getUsername(), mockGroup, auditInfo);
    verify(mockUserRoleSvc)
        .processAccessChange(
            eq(userDouble), eq(Collections.emptySet()), eq(Set.of(groupRoleToRemove)));

    // Assert user removed from org's teams
    verify(mockTeamAccessSvc)
        .removeUserFromTeamWithValidationBypass(userDouble, mockTeam, auditInfo);

    // Assert call is made to remove user from user groups
    verify(mockMmsUserGroupSvc)
        .removeExistingUserFromUserGroupsInOrg(userDouble.getId(), mockOrg.getId());
  }

  @Test
  void testRemoveUserFromOrganization_hasUserGroupFeatureFlag() throws SvcException {
    // GIVEN
    var orgRoleToRemove = RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId());
    var roleInDifferentOrg = RoleAssignment.forOrg(Role.ORG_OWNER, ObjectId.get());

    var groupRoleToRemove = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, mockGroup.getId());
    var roleInDifferentGroup = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, ObjectId.get());

    var auditInfo = AuditInfoHelpers.fromInternal();

    givenUserHasRoles(
        userDouble,
        Set.of(orgRoleToRemove, roleInDifferentOrg, groupRoleToRemove, roleInDifferentGroup));

    Team teamNotInOrg = Mockito.mock(Team.class);
    when(teamNotInOrg.getId()).thenReturn(ObjectId.get());
    when(teamNotInOrg.getOrgId()).thenReturn(ObjectId.get());

    givenUserIsOnTeams(userDouble, Set.of(mockTeam, teamNotInOrg));
    givenTeamHasMemberCountOf(mockTeam.getId(), 2);

    when(mockActorClient.getActor(any())).thenReturn(new ActorView("", List.of(), Set.of()));
    when(mockOrgSvc.findById(any())).thenReturn(mockOrg);
    when(mockMmsUserGroupSvc.areFgaAndUserGroupsFeaturesEnabled(any())).thenReturn(true);

    // WHEN
    orgAccessSvc.removeUserFromOrganization(userDouble, mockOrg.getId(), auditInfo);

    // THEN
    // Assert call is made to remove user from user groups
    verify(mockMmsUserGroupSvc)
        .removeExistingUserFromUserGroupsInOrg(userDouble.getId(), mockOrg.getId());
    verify(mockTeamAccessSvc).removeUserFromTeamWithValidationBypass(any(), any(), any());
  }

  @Test
  void testRemoveUserFromOrganization_hasUserGroupFeatureFlag_exceptionThrownForUserGroups()
      throws SvcException {
    // GIVEN
    var orgRoleToRemove = RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId());
    var roleInDifferentOrg = RoleAssignment.forOrg(Role.ORG_OWNER, ObjectId.get());

    var groupRoleToRemove = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, mockGroup.getId());
    var roleInDifferentGroup = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, ObjectId.get());

    var auditInfo = AuditInfoHelpers.fromInternal();

    givenUserHasRoles(
        userDouble,
        Set.of(orgRoleToRemove, roleInDifferentOrg, groupRoleToRemove, roleInDifferentGroup));

    Team teamNotInOrg = Mockito.mock(Team.class);
    when(teamNotInOrg.getId()).thenReturn(ObjectId.get());
    when(teamNotInOrg.getOrgId()).thenReturn(ObjectId.get());

    givenUserIsOnTeams(userDouble, Set.of(mockTeam, teamNotInOrg));
    givenTeamHasMemberCountOf(mockTeam.getId(), 2);

    when(mockActorClient.getActor(any())).thenReturn(new ActorView("", List.of(), Set.of()));
    when(mockOrgSvc.findById(any())).thenReturn(mockOrg);
    when(mockMmsUserGroupSvc.areFgaAndUserGroupsFeaturesEnabled(any())).thenReturn(true);
    doThrow(new AuthzServiceClientException(AuthzServiceClientErrorCode.UNEXPECTED_ERROR))
        .when(mockMmsUserGroupSvc)
        .removeExistingUserFromUserGroupsInOrg(any(), any());

    // WHEN
    assertThrows(
        AuthzServiceClientException.class,
        () -> orgAccessSvc.removeUserFromOrganization(userDouble, mockOrg.getId(), auditInfo));

    // THEN
    verify(mockTeamAccessSvc).removeUserFromTeamWithValidationBypass(any(), any(), any());
  }

  @Test
  void
      testRemoveUserFromOrganization_doesNotHaveUserGroupFeatureFlag_exceptionNotThrownForUserGroups()
          throws SvcException {
    // GIVEN
    var orgRoleToRemove = RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId());
    var roleInDifferentOrg = RoleAssignment.forOrg(Role.ORG_OWNER, ObjectId.get());

    var groupRoleToRemove = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, mockGroup.getId());
    var roleInDifferentGroup = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, ObjectId.get());

    var auditInfo = AuditInfoHelpers.fromInternal();

    givenUserHasRoles(
        userDouble,
        Set.of(orgRoleToRemove, roleInDifferentOrg, groupRoleToRemove, roleInDifferentGroup));

    Team teamNotInOrg = Mockito.mock(Team.class);
    when(teamNotInOrg.getId()).thenReturn(ObjectId.get());
    when(teamNotInOrg.getOrgId()).thenReturn(ObjectId.get());

    givenUserIsOnTeams(userDouble, Set.of(mockTeam, teamNotInOrg));
    givenTeamHasMemberCountOf(mockTeam.getId(), 2);

    when(mockActorClient.getActor(any())).thenReturn(new ActorView("", List.of(), Set.of()));
    when(mockOrgSvc.findById(any())).thenReturn(mockOrg);
    when(mockMmsUserGroupSvc.areFgaAndUserGroupsFeaturesEnabled(any())).thenReturn(false);
    doThrow(new AuthzServiceClientException(AuthzServiceClientErrorCode.UNEXPECTED_ERROR))
        .when(mockMmsUserGroupSvc)
        .removeExistingUserFromUserGroupsInOrg(any(), any());

    // WHEN
    assertDoesNotThrow(
        () -> orgAccessSvc.removeUserFromOrganization(userDouble, mockOrg.getId(), auditInfo));

    // THEN
    verify(mockTeamAccessSvc).removeUserFromTeamWithValidationBypass(any(), any(), any());
  }

  @Test
  void testRemoveUserFromOrganization_doesNotHaveUserGroupFeatureFlag_exceptionThrownForTeams()
      throws SvcException {
    // GIVEN
    var orgRoleToRemove = RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId());
    var roleInDifferentOrg = RoleAssignment.forOrg(Role.ORG_OWNER, ObjectId.get());

    var groupRoleToRemove = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, mockGroup.getId());
    var roleInDifferentGroup = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, ObjectId.get());

    var auditInfo = AuditInfoHelpers.fromInternal();

    givenUserHasRoles(
        userDouble,
        Set.of(orgRoleToRemove, roleInDifferentOrg, groupRoleToRemove, roleInDifferentGroup));

    Team teamNotInOrg = Mockito.mock(Team.class);
    when(teamNotInOrg.getId()).thenReturn(ObjectId.get());
    when(teamNotInOrg.getOrgId()).thenReturn(ObjectId.get());

    givenUserIsOnTeams(userDouble, Set.of(mockTeam, teamNotInOrg));
    givenTeamHasMemberCountOf(mockTeam.getId(), 2);

    when(mockActorClient.getActor(any())).thenReturn(new ActorView("", List.of(), Set.of()));
    when(mockOrgSvc.findById(any())).thenReturn(mockOrg);
    when(mockMmsUserGroupSvc.areFgaAndUserGroupsFeaturesEnabled(any())).thenReturn(false);
    doThrow(new RuntimeException())
        .when(mockTeamAccessSvc)
        .removeUserFromTeamWithValidationBypass(any(), any(), any());

    // WHEN
    assertThrows(
        RuntimeException.class,
        () -> orgAccessSvc.removeUserFromOrganization(userDouble, mockOrg.getId(), auditInfo));

    // THEN
    verify(mockTeamAccessSvc).removeUserFromTeamWithValidationBypass(any(), any(), any());
    verify(mockMmsUserGroupSvc, never()).removeExistingUserFromUserGroupsInOrg(any(), any());
  }

  @Test
  void testRemoveUserFromOrganization_doesHaveUserGroupFeatureFlag_exceptionNotThrownForTeams()
      throws SvcException {
    // GIVEN
    var orgRoleToRemove = RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId());
    var roleInDifferentOrg = RoleAssignment.forOrg(Role.ORG_OWNER, ObjectId.get());

    var groupRoleToRemove = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, mockGroup.getId());
    var roleInDifferentGroup = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, ObjectId.get());

    var auditInfo = AuditInfoHelpers.fromInternal();

    givenUserHasRoles(
        userDouble,
        Set.of(orgRoleToRemove, roleInDifferentOrg, groupRoleToRemove, roleInDifferentGroup));

    Team teamNotInOrg = Mockito.mock(Team.class);
    when(teamNotInOrg.getId()).thenReturn(ObjectId.get());
    when(teamNotInOrg.getOrgId()).thenReturn(ObjectId.get());

    givenUserIsOnTeams(userDouble, Set.of(mockTeam, teamNotInOrg));
    givenTeamHasMemberCountOf(mockTeam.getId(), 2);

    when(mockActorClient.getActor(any())).thenReturn(new ActorView("", List.of(), Set.of()));
    when(mockOrgSvc.findById(any())).thenReturn(mockOrg);
    when(mockMmsUserGroupSvc.areFgaAndUserGroupsFeaturesEnabled(any())).thenReturn(true);
    doThrow(new RuntimeException())
        .when(mockTeamAccessSvc)
        .removeUserFromTeamWithValidationBypass(any(), any(), any());

    // WHEN
    assertDoesNotThrow(
        () -> orgAccessSvc.removeUserFromOrganization(userDouble, mockOrg.getId(), auditInfo));

    // THEN
    verify(mockTeamAccessSvc).removeUserFromTeamWithValidationBypass(any(), any(), any());
    verify(mockMmsUserGroupSvc).removeExistingUserFromUserGroupsInOrg(any(), any());
  }

  @Test
  void testRemoveUserFromOrganization_hasUserGroupFeatureFlag_otherError() throws SvcException {
    // GIVEN
    var orgRoleToRemove = RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId());
    var roleInDifferentOrg = RoleAssignment.forOrg(Role.ORG_OWNER, ObjectId.get());

    var groupRoleToRemove = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, mockGroup.getId());
    var roleInDifferentGroup = RoleAssignment.forGroup(Role.GROUP_READ_ONLY, ObjectId.get());

    var auditInfo = AuditInfoHelpers.fromInternal();

    givenUserHasRoles(
        userDouble,
        Set.of(orgRoleToRemove, roleInDifferentOrg, groupRoleToRemove, roleInDifferentGroup));

    Team teamNotInOrg = Mockito.mock(Team.class);
    when(teamNotInOrg.getId()).thenReturn(ObjectId.get());
    when(teamNotInOrg.getOrgId()).thenReturn(ObjectId.get());

    givenUserIsOnTeams(userDouble, Set.of(mockTeam, teamNotInOrg));
    givenTeamHasMemberCountOf(mockTeam.getId(), 2);

    when(mockActorClient.getActor(any())).thenReturn(new ActorView("", List.of(), Set.of()));
    when(mockOrgSvc.findById(any())).thenReturn(mockOrg);
    when(mockMmsUserGroupSvc.areFgaAndUserGroupsFeaturesEnabled(any())).thenReturn(true);
    doThrow(new AuthzServiceClientException(AuthzServiceClientErrorCode.UNEXPECTED_ERROR))
        .when(mockMmsUserGroupSvc)
        .removeExistingUserFromUserGroupsInOrg(any(), any());

    // WHEN
    assertThrows(
        AuthzServiceClientException.class,
        () -> orgAccessSvc.removeUserFromOrganization(userDouble, mockOrg.getId(), auditInfo));
  }

  @Test
  void removeUserFromOrganization_throwsException_whenUserIsLastOrgOwner() {
    // GIVEN
    givenUserHasRoles(userDouble, Set.of(RoleAssignment.forOrg(Role.ORG_OWNER, mockOrg.getId())));
    givenOrgHasOrgOwnerCountOf(mockOrg.getId(), 1);

    when(mockAuthzSvc.isExplicitOrgOwner(userDouble, mockOrg.getId())).thenReturn(true);

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.removeUserFromOrganization(
                    userDouble, mockOrg.getId(), AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.CANNOT_DELETE_LAST_ORG_OWNER));
  }

  @Test
  void removeUserFromOrganization_throwsException_whenUserIsChartsUser() {
    // GIVEN
    AppUser chartsUser = new AppUser();
    chartsUser.setUsername("<EMAIL>");
    when(mockUserDao.findByUsername(chartsUser.getUsername())).thenReturn(chartsUser);
    givenUserHasRoles(
        chartsUser,
        Set.of(
            RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId()),
            RoleAssignment.forGroup(Role.GROUP_OWNER, mockGroup.getId())));

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.removeUserFromOrganization(
                    chartsUser, mockOrg.getId(), AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.CANNOT_MODIFY_CHARTS_USER));
  }

  @Test
  void removeUserFromOrganization_succeeds_whenUserIsLastOrgTeamMember() throws SvcException {
    // GIVEN
    givenUserHasRoles(userDouble, Set.of(RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId())));
    givenUserIsOnTeams(userDouble, Set.of(mockTeam));
    givenTeamHasMemberCountOf(mockTeam.getId(), 1);

    // WHEN
    orgAccessSvc.removeUserFromOrganization(
        userDouble, mockOrg.getId(), AuditInfoHelpers.fromInternal());

    // THEN
    verify(mockUserDao, times(1)).removeOrgId(userDouble.getId(), mockOrg.getId());
  }

  @Test
  void testUpdateUserRoleInOrganization() throws SvcException {
    // GIVEN
    givenUserHasRoles(userDouble, Set.of(RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId())));
    Set<Role> rolesToAdd = Set.of(Role.ORG_OWNER);
    Set<Role> rolesToRemove = Set.of(Role.ORG_MEMBER);

    // WHEN
    orgAccessSvc.updateUserRoleInOrganization(
        userDouble,
        mockOrg.getId(),
        rolesToAdd,
        rolesToRemove,
        true,
        AuditInfoHelpers.fromInternal());

    // THEN
    verify(mockUserDao)
        .addOrgRoleAssignments(userDouble.getUsername(), mockOrg.getId(), rolesToAdd);
    verify(mockUserDao)
        .removeOrgRoleAssignments(userDouble.getUsername(), mockOrg.getId(), rolesToRemove);

    verify(mockUserRoleSvc)
        .processAccessChange(
            eq(userDouble),
            eq(Set.of(RoleAssignment.forOrg(Role.ORG_OWNER, ORG_ID))),
            eq(Set.of(RoleAssignment.forOrg(Role.ORG_MEMBER, ORG_ID))));
  }

  @Test
  void
      updateUserRoleInOrganization_throwsException_whenGivenChartsUserAndUserModNotAllowedInCurrentOrg() {
    // GIVEN
    AppUser chartsUser = new AppUser();
    chartsUser.setUsername("<EMAIL>");
    when(mockUserDao.findByUsername(chartsUser.getUsername())).thenReturn(chartsUser);
    givenUserHasRoles(
        chartsUser,
        Set.of(
            RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId()),
            RoleAssignment.forGroup(Role.GROUP_OWNER, mockGroup.getId()),
            RoleAssignment.forOrg(Role.ORG_MEMBER, mockOtherOrg.getId()),
            RoleAssignment.forGroup(Role.GROUP_OWNER, mockOtherOrgGroup.getId())));
    when(mockGroupSvc.findByIdsAndOrgIds(any(), any())).thenReturn(List.of(mockGroup));

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.updateUserRoleInOrganization(
                    chartsUser,
                    mockOrg.getId(),
                    Set.of(Role.ORG_OWNER),
                    Collections.emptySet(),
                    false,
                    AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.CANNOT_MODIFY_CHARTS_USER));
  }

  @Test
  void updateUserRoleInOrganization_allowsChartsUserRoleChange_givenChartsUserModIsAllowed()
      throws SvcException {
    // GIVEN
    AppUser chartsUser = new AppUser();
    chartsUser.setUsername("<EMAIL>");
    when(mockUserDao.findByUsername(chartsUser.getUsername())).thenReturn(chartsUser);
    givenUserHasRoles(
        chartsUser,
        Set.of(
            RoleAssignment.forOrg(Role.ORG_MEMBER, mockOrg.getId()),
            RoleAssignment.forGroup(Role.GROUP_OWNER, mockGroup.getId())));
    when(mockGroupSvc.findByIds(eq(List.of(mockGroup.getId())))).thenReturn(List.of(mockGroup));

    Set<Role> rolesToAdd = Set.of(Role.ORG_OWNER);
    Set<Role> rolesToRemove = Set.of(Role.ORG_MEMBER);

    // WHEN

    orgAccessSvc.updateUserRoleInOrganization(
        chartsUser,
        mockOrg.getId(),
        rolesToAdd,
        rolesToRemove,
        true,
        AuditInfoHelpers.fromInternal());

    // THEN
    verify(mockUserDao)
        .addOrgRoleAssignments(chartsUser.getUsername(), mockOrg.getId(), rolesToAdd);
    verify(mockUserDao)
        .removeOrgRoleAssignments(chartsUser.getUsername(), mockOrg.getId(), rolesToRemove);

    verify(mockUserRoleSvc)
        .processAccessChange(
            eq(chartsUser),
            eq(Set.of(RoleAssignment.forOrg(Role.ORG_OWNER, ORG_ID))),
            eq(Set.of(RoleAssignment.forOrg(Role.ORG_MEMBER, ORG_ID))));
  }

  @Test
  void updateUserRoleInOrganization_throwsException_whenGivenNullUser() {
    // GIVEN
    AppUser user = null;

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.updateUserRoleInOrganization(
                    user,
                    ObjectId.get(),
                    Set.of(Role.ORG_MEMBER),
                    Collections.emptySet(),
                    true,
                    AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.INVALID_USERNAME));
  }

  @Test
  void updateUserRoleInOrganization_throwsException_whenGivenNonOrgRoles() {
    // GIVEN
    Set<Role> newRoles = Set.of(Role.GROUP_OWNER);

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.updateUserRoleInOrganization(
                    userDouble,
                    ObjectId.get(),
                    newRoles,
                    Collections.emptySet(),
                    true,
                    AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.INVALID_ROLE_IN_ORG));
  }

  @Test
  void updateUserRoleInOrganization_throwsException_ifUserWouldBeLeftWithNoOrgRoles() {
    // GIVEN
    Set<Role> newRoles = Collections.emptySet();
    Set<Role> rolesToRemove = Set.of(Role.ORG_MEMBER);

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.updateUserRoleInOrganization(
                    userDouble,
                    ObjectId.get(),
                    newRoles,
                    rolesToRemove,
                    true,
                    AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.CANNOT_REMOVE_ALL_USER_ROLES));
  }

  @Test
  void updateUserRoleInOrganization_throwsException_whenRemovingLastOrgOwner() {
    // GIVEN
    givenUserHasRoles(userDouble, Set.of(RoleAssignment.forOrg(Role.ORG_OWNER, mockOrg.getId())));
    givenOrgHasOrgOwnerCountOf(mockOrg.getId(), 1);

    Set<Role> rolesToRemove = Set.of(Role.ORG_OWNER);
    Set<Role> rolesToAdd = Set.of(Role.ORG_MEMBER);

    when(mockAuthzSvc.isExplicitOrgOwner(userDouble, mockOrg.getId())).thenReturn(true);

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.updateUserRoleInOrganization(
                    userDouble,
                    mockOrg.getId(),
                    rolesToAdd,
                    rolesToRemove,
                    true,
                    AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.CANNOT_DELETE_LAST_ADMIN));
  }

  @Test
  void testReplaceUserRolesInOrganization() throws SvcException {
    // GIVEN
    Set<RoleAssignment> existingRoles =
        Set.of(RoleAssignment.forOrg(Role.ORG_BILLING_ADMIN, ORG_ID));
    givenUserHasRoles(userDouble, existingRoles);

    Set<Role> newRoles = Set.of(Role.ORG_OWNER);
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();

    // WHEN
    orgAccessSvc.replaceUserRolesInOrganization(userDouble, ORG_ID, newRoles, auditInfo);

    // THEN
    verify(mockUserDao)
        .removeOrgRoleAssignments(
            eq(userDouble.getUsername()), eq(ORG_ID), eq(Set.of(Role.ORG_BILLING_ADMIN)));
    verify(mockUserDao)
        .addOrgRoleAssignments(eq(userDouble.getUsername()), eq(ORG_ID), eq(newRoles));
    verify(mockUserRoleSvc)
        .processAccessChange(
            eq(userDouble),
            eq(Set.of(RoleAssignment.forOrg(Role.ORG_OWNER, ORG_ID))),
            eq(existingRoles));
  }

  @Test
  void testReplaceUserRolesInOrganizationWithOverlappingRoles() throws SvcException {
    // GIVEN
    Set<RoleAssignment> existingRoles =
        Set.of(RoleAssignment.forOrg(Role.ORG_BILLING_ADMIN, ORG_ID));
    givenUserHasRoles(userDouble, existingRoles);

    Set<Role> newRoles = Set.of(Role.ORG_OWNER, Role.ORG_BILLING_ADMIN);
    AuditInfo auditInfo = AuditInfoHelpers.fromInternal();

    // WHEN
    orgAccessSvc.replaceUserRolesInOrganization(userDouble, ORG_ID, newRoles, auditInfo);

    // THEN
    verify(mockUserDao)
        .removeOrgRoleAssignments(
            eq(userDouble.getUsername()), eq(ORG_ID), eq(Set.of(Role.ORG_BILLING_ADMIN)));
    verify(mockUserDao)
        .addOrgRoleAssignments(eq(userDouble.getUsername()), eq(ORG_ID), eq(newRoles));
    verify(mockUserRoleSvc)
        .processAccessChange(
            eq(userDouble),
            eq(Set.of(RoleAssignment.forOrg(Role.ORG_OWNER, ORG_ID))),
            eq(Collections.emptySet()));
  }

  @Test
  void replaceUserRolesInOrganization_throwsException_givenNullUser() {
    // GIVEN
    AppUser user = null;

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.replaceUserRolesInOrganization(
                    user,
                    ObjectId.get(),
                    Set.of(Role.ORG_MEMBER),
                    AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.INVALID_USERNAME));
  }

  @Test
  void replaceUserRolesInOrganization_throwException_givenInvalidOrgRole() {
    // GIVEN
    Set<Role> newRoles = Set.of(Role.GROUP_READ_ONLY);

    // WHEN
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                orgAccessSvc.replaceUserRolesInOrganization(
                    userDouble, mockOrg.getId(), newRoles, AuditInfoHelpers.fromInternal()));

    // THEN
    assertThat(exception.getErrorCode(), is(AppUserErrorCode.INVALID_ROLE_IN_ORG));
  }

  @Test
  void userExistsInOrganization_returnsFalse_whenUserDoesNotExist() {
    // GIVEN
    String username = "<EMAIL>";

    // WHEN
    boolean exists = orgAccessSvc.userExistsInOrganization(username, ObjectId.get());

    // THEN
    assertFalse(exists);
  }

  @Test
  void userExistsInOrganization_returnsFalse_whenUserDoesNotHaveOrgRole() {
    // GIVEN
    givenUserHasRoles(userDouble, Set.of(RoleAssignment.forOrg(Role.ORG_OWNER, ObjectId.get())));

    // WHEN
    boolean exists =
        orgAccessSvc.userExistsInOrganization(userDouble.getUsername(), ObjectId.get());

    // THEN
    assertFalse(exists);
  }

  @Test
  void userExistsInOrganization_returnsTrue_whenUserHasOrgRole() {
    // GIVEN
    ObjectId orgId = ObjectId.get();
    givenUserHasRoles(userDouble, Set.of(RoleAssignment.forOrg(Role.ORG_OWNER, orgId)));

    // WHEN
    boolean exists = orgAccessSvc.userExistsInOrganization(userDouble.getUsername(), orgId);

    // THEN
    assertTrue(exists);
  }

  @Test
  void updateUserOrgSettings_addsOrg_setsCurrentOrg_addsPolicyAssignmentsToUser()
      throws AuthzServiceClientException {
    // GIVEN
    ResourceId resourceId = new ResourceId("cloud", "organization", ObjectId.get(), null);
    List<PolicyAssignment> policyAssignments = List.of(new PolicyAssignment("1", resourceId));

    // WHEN
    orgAccessSvc.updateUserOrgSettings(userDouble, mockOrg, policyAssignments);

    // THEN
    verify(mockUserDao).addOrgIdAndSetCurrentOrgId(userDouble, mockOrg.getId());
    verify(mockActorClient)
        .editPolicyAssignments(
            userDouble.getId().toString(),
            policyAssignments.stream().map(PolicyAssignment::serializeToMessage).toList(),
            List.of());
  }

  @Test
  void removePolicyAssignments() throws AuthzServiceClientException {
    // GIVEN
    ObjectId orgId = ObjectId.get();
    AppUser user = createAppUser(List.of(orgId));
    List<Group> groups = List.of(mockGroup);

    AppUser appUserWithOneOrgAssignment = createAppUser(List.of(orgId));

    List<PolicyAssignmentView> policyAssignmentViews =
        List.of(
            createPolicyAssignmentView("assignment1", "cloud", "organization", orgId.toString()),
            createPolicyAssignmentView(
                "assignment2", "cloud", "organization", ObjectId.get().toString()),
            createPolicyAssignmentView(
                "assignment3", "cloud", "project", mockGroup.getId().toString()),
            createPolicyAssignmentView(
                "assignment4", "cloud", "project", ObjectId.get().toString()),
            createPolicyAssignmentView("assignment5", "cloud", "cluster", "cluster1"),
            createPolicyAssignmentView(
                "assignment6", "cloud", "cluster", ObjectId.get().toString()));

    ActorView actor =
        new ActorView(
            appUserWithOneOrgAssignment.getId().toString(),
            policyAssignmentViews,
            Collections.emptySet());
    when(mockActorClient.getActor(any())).thenReturn(actor);

    ResourceIdMessage org1 =
        ResourceIdMessage.newBuilder()
            .setService("cloud")
            .setType("organization")
            .setId(orgId.toString())
            .build();
    ResourceIdMessage org2 =
        ResourceIdMessage.newBuilder()
            .setService("cloud")
            .setType("organization")
            .setId("org2")
            .build();
    ResourceIdMessage cluster1 =
        ResourceIdMessage.newBuilder()
            .setService("cloud")
            .setType("cluster")
            .setId("cluster1")
            .build();
    ResourceIdMessage cluster2 =
        ResourceIdMessage.newBuilder()
            .setService("cloud")
            .setType("cluster")
            .setId("cluster2")
            .build();
    GetResourcesResponse getResourcesResponse =
        GetResourcesResponse.newBuilder()
            .addAllResources(
                List.of(
                    ResourceMessage.newBuilder()
                        .setResourceId(cluster1)
                        .addAllAncestorResourceIds(List.of(org1))
                        .build(),
                    ResourceMessage.newBuilder()
                        .setResourceId(cluster2)
                        .addAllAncestorResourceIds(List.of(org2))
                        .build()))
            .build();

    when(mockResourceClient.getResources(Collections.singletonList(any())))
        .thenReturn(getResourcesResponse);

    // WHEN
    orgAccessSvc.removePolicyAssignments(user, orgId, groups);

    // THEN
    verify(mockActorClient)
        .editPolicyAssignments(
            user.getId().toString(),
            List.of(),
            List.of(
                ConversionUtils.mapPolicyAssignmentViewToMessage(policyAssignmentViews.get(0)),
                ConversionUtils.mapPolicyAssignmentViewToMessage(policyAssignmentViews.get(2)),
                ConversionUtils.mapPolicyAssignmentViewToMessage(policyAssignmentViews.get(4))));
  }

  @Test
  void getClusterResourceIdViews_returnsOnlyClusters() {
    // GIVEN
    ObjectId orgId = ObjectId.get();
    ObjectId clusterId1 = ObjectId.get();
    ObjectId clusterId2 = ObjectId.get();
    AppUser appUserWithOneOrgAssignment = createAppUser(List.of(orgId));

    ActorView actor =
        new ActorView(
            appUserWithOneOrgAssignment.getId().toString(),
            List.of(
                createPolicyAssignmentView(
                    "assignment1", "cloud", "organization", orgId.toString()),
                createPolicyAssignmentView(
                    "assignment2", "cloud", "project", ObjectId.get().toString()),
                createPolicyAssignmentView(
                    "assignment3", "cloud", "project", ObjectId.get().toString()),
                createPolicyAssignmentView(
                    "assignment4", "cloud", "cluster", clusterId1.toString()),
                createPolicyAssignmentView(
                    "assignment5", "cloud", "cluster", clusterId2.toString())),
            Collections.emptySet());

    // WHEN
    List<ResourceIdView> clusters = orgAccessSvc.getClusterResourceIdViews(actor);

    // THEN
    assertEquals(clusters.size(), 2);
    assertEquals(clusters.get(0).getService(), "cloud");
    assertEquals(clusters.get(0).getType(), "cluster");
    assertEquals(clusters.get(0).getId(), clusterId1.toString());
    assertEquals(clusters.get(1).getService(), "cloud");
    assertEquals(clusters.get(1).getType(), "cluster");
    assertEquals(clusters.get(1).getId(), clusterId2.toString());
  }

  @Test
  void getClusterResourceIdViews_returnsUniqueClusters() {
    // GIVEN
    ObjectId orgId = ObjectId.get();
    ObjectId clusterId = ObjectId.get();
    AppUser appUserWithOneOrgAssignment = createAppUser(List.of(orgId));

    ActorView actor =
        new ActorView(
            appUserWithOneOrgAssignment.getId().toString(),
            List.of(
                createPolicyAssignmentView("assignment1", "cloud", "cluster", clusterId.toString()),
                createPolicyAssignmentView(
                    "assignment2", "cloud", "cluster", clusterId.toString())),
            Collections.emptySet());

    // WHEN
    List<ResourceIdView> clusters = orgAccessSvc.getClusterResourceIdViews(actor);

    // THEN
    assertEquals(clusters.size(), 1);
    assertEquals(clusters.get(0).getService(), "cloud");
    assertEquals(clusters.get(0).getType(), "cluster");
    assertEquals(clusters.get(0).getId(), clusterId.toString());
  }

  @Test
  void getClusterResourceIdViews_returnsEmptyList_whenUserHasNoClusters() {
    // GIVEN
    ObjectId orgId = ObjectId.get();
    AppUser appUserWithOneOrgAssignment = createAppUser(List.of(orgId));

    ActorView actor =
        new ActorView(
            appUserWithOneOrgAssignment.getId().toString(),
            List.of(
                createPolicyAssignmentView(
                    "assignment1", "cloud", "organization", orgId.toString()),
                createPolicyAssignmentView(
                    "assignment2", "cloud", "project", ObjectId.get().toString()),
                createPolicyAssignmentView(
                    "assignment3", "cloud", "project", ObjectId.get().toString())),
            Collections.emptySet());

    // WHEN
    List<ResourceIdView> clusters = orgAccessSvc.getClusterResourceIdViews(actor);

    // THEN
    assertTrue(clusters.isEmpty());
  }

  @Test
  void getClusterIdsToRemove_returnsEmptySetAndDoesNotMakeRequest_whenNoClustersProvided()
      throws AuthzServiceClientException {
    // GIVEN
    ObjectId orgId = ObjectId.get();
    List<ResourceIdView> clusterResourceIdViews = List.of();

    // WHEN
    Set<String> clusterIds = orgAccessSvc.getClusterIdsToRemove(clusterResourceIdViews, orgId);

    // THEN
    assertTrue(clusterIds.isEmpty());
    verify(mockResourceClient, never()).getResources(Collections.singletonList(any()));
  }

  @Test
  void getClusterIdsToRemove_returnsClustersWithOrgIdAsAncestorResource()
      throws AuthzServiceClientException {
    // GIVEN
    ObjectId orgId = ObjectId.get();
    ResourceIdMessage org1 =
        ResourceIdMessage.newBuilder()
            .setService("cloud")
            .setType("organization")
            .setId(orgId.toString())
            .build();
    ResourceIdMessage org2 =
        ResourceIdMessage.newBuilder()
            .setService("cloud")
            .setType("organization")
            .setId("org2")
            .build();
    ResourceIdMessage cluster1 =
        ResourceIdMessage.newBuilder()
            .setService("cloud")
            .setType("cluster")
            .setId("cluster1")
            .build();
    ResourceIdMessage cluster2 =
        ResourceIdMessage.newBuilder()
            .setService("cloud")
            .setType("cluster")
            .setId("cluster2")
            .build();
    GetResourcesResponse getResourcesResponse =
        GetResourcesResponse.newBuilder()
            .addAllResources(
                List.of(
                    ResourceMessage.newBuilder()
                        .setResourceId(cluster1)
                        .addAllAncestorResourceIds(List.of(org1))
                        .build(),
                    ResourceMessage.newBuilder()
                        .setResourceId(cluster2)
                        .addAllAncestorResourceIds(List.of(org2))
                        .build()))
            .build();

    List<ResourceIdView> clusterResourceIdViews = List.of(new ResourceIdView());
    when(mockResourceClient.getResources(clusterResourceIdViews)).thenReturn(getResourcesResponse);

    // WHEN
    Set<String> clusterIds = orgAccessSvc.getClusterIdsToRemove(clusterResourceIdViews, orgId);

    // THEN
    assertEquals(clusterIds.size(), 1);
    assertTrue(clusterIds.contains("cluster1"));
    verify(mockResourceClient).getResources(clusterResourceIdViews);
  }

  @Test
  void getPolicyAssignmentViewsToRemove() {
    // GIVEN
    ObjectId orgId = ObjectId.get();
    ObjectId projectId = ObjectId.get();
    ObjectId clusterId = ObjectId.get();

    List<PolicyAssignmentView> policyAssignmentViews =
        List.of(
            createPolicyAssignmentView("assignment1", "cloud", "organization", orgId.toString()),
            createPolicyAssignmentView(
                "assignment2", "cloud", "organization", ObjectId.get().toString()),
            createPolicyAssignmentView("assignment3", "cloud", "project", projectId.toString()),
            createPolicyAssignmentView(
                "assignment4", "cloud", "project", ObjectId.get().toString()),
            createPolicyAssignmentView("assignment5", "cloud", "cluster", clusterId.toString()),
            createPolicyAssignmentView(
                "assignment6", "cloud", "cluster", ObjectId.get().toString()));
    Set<String> groupIdsToRemoveSet = Set.of(projectId.toString());
    Set<String> clusterIdsToRemoveSet = Set.of(clusterId.toString());

    // WHEN
    List<PolicyAssignmentView> viewsToRemove =
        orgAccessSvc.getPolicyAssignmentViewsToRemove(
            policyAssignmentViews, orgId, groupIdsToRemoveSet, clusterIdsToRemoveSet);

    // THEN
    assertEquals(viewsToRemove.size(), 3);
    assertEquals(viewsToRemove.get(0).getPolicyId(), "assignment1");
    assertEquals(viewsToRemove.get(1).getPolicyId(), "assignment3");
    assertEquals(viewsToRemove.get(2).getPolicyId(), "assignment5");
  }

  private void givenUserHasRoles(AppUser appUser, Set<RoleAssignment> roleAssignments) {
    appUser.setRoles(roleAssignments);

    List<ObjectId> groupIds = new ArrayList<>();
    List<ObjectId> orgIds = new ArrayList<>();

    for (RoleAssignment roleAssignment : roleAssignments) {
      if (roleAssignment.getGroupId() != null) {
        groupIds.add(roleAssignment.getGroupId());
      } else if (roleAssignment.getOrgId() != null) {
        orgIds.add(roleAssignment.getOrgId());
      }
    }

    appUser.setOrgIds(orgIds);
    appUser.setGroupIds(groupIds);
  }

  private void givenOrgHasOrgOwnerCountOf(ObjectId orgId, long count) {
    when(mockUserDao.countLocalUsersByRoleAndOrgId(Role.ORG_OWNER, orgId)).thenReturn(count);
  }

  private void givenUserIsOnTeams(AppUser appUser, Set<Team> teams) {
    Set<ObjectId> teamIds = new HashSet<>();

    for (Team team : teams) {
      teamIds.add(team.getId());
    }
    appUser.setTeamIds(teamIds);
  }

  private void givenTeamHasMemberCountOf(ObjectId teamId, long count) {
    when(mockUserDao.countLocalUsersByTeamId(teamId)).thenReturn(count);
  }

  private PolicyAssignmentView createPolicyAssignmentView(
      String policyId, String scopeService, String scopeType, String scopeId) {
    ResourceIdView resourceIdView = new ResourceIdView(scopeService, scopeType, scopeId);
    return new PolicyAssignmentView(policyId, resourceIdView);
  }

  private AppUser createAppUser(List<ObjectId> orgIds) {
    AppUser appUser = new AppUser();
    appUser.setId(new ObjectId());
    appUser.setOrgIds(orgIds);
    return appUser;
  }
}
