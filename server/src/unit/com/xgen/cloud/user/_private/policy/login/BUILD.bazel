load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*UnitTests.java"]),
    deny_warnings = True,
    deps = [
        "//server/src/main/com/xgen/cloud/access/authz",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/auditinfosvc/_public/svc",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/user",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/cloud/user/_private/policy",
        "@maven//:junit_junit",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
    ],
)
