package com.xgen.cloud.user._private.policy.login;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import java.util.Calendar;
import java.util.Date;
import org.bson.types.ObjectId;
import org.junit.Test;

public class PasswordExpiredLoginPolicyUnitTests {
  @Test
  public void testNoSettingShouldNotAffect() {
    AppSettings settings = mock(AppSettings.class);
    when(settings.hasProp(PasswordExpiredLoginPolicy.NS_EXPIRE_AFTER_DAYS)).thenReturn(false);
    PasswordExpiredLoginPolicy policy = new PasswordExpiredLoginPolicy(null, settings);
    for (AppUserErrorCode code : AppUserErrorCode.values()) {
      assertEquals(code, policy.enforce(new AppUser(), code, null).code);
    }
  }

  @Test
  public void testPasswordExpired() {
    AppSettings settings = mockSettings(3);
    AppUser user = new AppUser();
    user.setId(ObjectId.get());
    for (int i = 10; i >= 3; i--) {
      Date date = InactiveUserLoginPolicyUnitTests.getOldDate(i);
      user.setLastPasswordUpdated(date);
      for (AppUserErrorCode code : AppUserErrorCode.values()) {
        PasswordExpiredLoginPolicy policy =
            new PasswordExpiredLoginPolicy(mock(UserSvc.class), settings);
        if (policy.isAcceptedCode(code)) {
          assertEquals(
              AppUserErrorCode.INVALID_PASSWORD_EXPIRED, policy.enforce(user, code, null).code);
        }
      }
    }
  }

  @Test
  public void testExpirationRounding() {
    AppSettings settings = mockSettings(3);
    AppUser user = new AppUser();
    user.setId(ObjectId.get());

    Calendar justUnder3DaysAgo = TimeUtils.nowTime();
    TimeUtils.subtractHours(justUnder3DaysAgo, 71);
    Calendar justAbove3DaysAgo = TimeUtils.nowTime();
    TimeUtils.subtractHours(justAbove3DaysAgo, 72);
    user.setLastPasswordUpdated(justUnder3DaysAgo.getTime());
    PasswordExpiredLoginPolicy policy =
        new PasswordExpiredLoginPolicy(mock(UserSvc.class), settings);
    assertEquals(
        "policy rounded 71 hours up to 3 days",
        CommonErrorCode.NONE,
        policy.enforce(user, CommonErrorCode.NONE, null).code);
    user.setLastPasswordUpdated(justAbove3DaysAgo.getTime());
    assertEquals(
        "policy should failed on and after 72 hours",
        AppUserErrorCode.INVALID_PASSWORD_EXPIRED,
        policy.enforce(user, CommonErrorCode.NONE, null).code);
  }

  private AppSettings mockSettings(int value) {
    AppSettings settings = mock(AppSettings.class);
    when(settings.hasProp(PasswordExpiredLoginPolicy.NS_EXPIRE_AFTER_DAYS)).thenReturn(true);
    when(settings.getIntProp(PasswordExpiredLoginPolicy.NS_EXPIRE_AFTER_DAYS)).thenReturn(value);
    return settings;
  }
}
