package com.xgen.cloud.user._private.svc;

import static com.xgen.cloud.common.dao.base._public.util.BsonUtils.Static.oid;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.authz.shared._public.exceptions.AuthzServiceClientException;
import com.xgen.cloud.authz.sync._public.wrapper.AuthzMmsSyncClientWrapper;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class UserRoleSvcUnitTests {
  @Mock private UserDao userDao;
  @Mock private AuthzMmsSyncClientWrapper authzMmsSyncClientWrapper;
  @Mock private AuthzSvc authzSvc;

  @InjectMocks private UserRoleSvc userRoleSvc;

  @Test
  public void testResetRoleAssignments_rightRoleAssignments() throws AuthzServiceClientException {
    ObjectId appUserId = oid(1);
    AppUser appUser = mock(AppUser.class);
    when(appUser.getActorId()).thenReturn(appUserId.toHexString());
    when(appUser.getRoles())
        .thenReturn(
            Set.of(
                RoleAssignment.forGlobal(Role.GLOBAL_OWNER),
                RoleAssignment.forGroup(Role.GROUP_OWNER, oid(1))));
    when(userDao.findById(eq(appUserId))).thenReturn(appUser);
    Set<RoleAssignment> rolesToReset =
        Set.of(
            RoleAssignment.forGlobal(Role.GLOBAL_OWNER),
            RoleAssignment.forGroup(Role.GROUP_USER_ADMIN, oid(1)));
    userRoleSvc.resetRoleAssignments(appUserId, rolesToReset);

    verify(userDao).resetRoleAssignments(appUserId, rolesToReset);
    verify(authzMmsSyncClientWrapper)
        .editPolicyAssignments(
            eq(appUserId.toString()),
            eq(Set.of(RoleAssignment.forGroup(Role.GROUP_USER_ADMIN, oid(1)))),
            eq(Set.of(RoleAssignment.forGroup(Role.GROUP_OWNER, oid(1)))));
  }

  @Test
  public void testResetRoleAssignments_rightRoleAssignments_withAuditInfo()
      throws AuthzServiceClientException {
    ObjectId appUserId = oid(1);
    AppUser appUser = mock(AppUser.class);
    AppUser updatedAppUser = mock(AppUser.class);
    when(appUser.getId()).thenReturn(appUserId);
    when(appUser.getActorId()).thenReturn(appUserId.toHexString());
    when(appUser.getRoles())
        .thenReturn(
            Set.of(
                RoleAssignment.forGlobal(Role.GLOBAL_OWNER),
                RoleAssignment.forGroup(Role.GROUP_OWNER, oid(1))));
    when(userDao.findById(eq(appUserId))).thenReturn(updatedAppUser);
    when(updatedAppUser.getCurrentGroupId()).thenReturn(oid(1));
    when(updatedAppUser.getCurrentOrgId()).thenReturn(null);
    when(authzSvc.isGlobalReadOnly(any(AppUser.class))).thenReturn(false);
    Set<RoleAssignment> rolesToReset =
        Set.of(
            RoleAssignment.forGlobal(Role.GLOBAL_OWNER),
            RoleAssignment.forGroup(Role.GROUP_USER_ADMIN, oid(1)));
    AuditInfo auditInfo = AuditInfoHelpers.fromSystem();
    userRoleSvc.resetRoleAssignments(appUser, rolesToReset, auditInfo);

    verify(userDao).resetRoleAssignments(appUserId, rolesToReset);
    verify(authzMmsSyncClientWrapper)
        .editPolicyAssignments(
            appUserId.toString(),
            Set.of(RoleAssignment.forGroup(Role.GROUP_USER_ADMIN, oid(1))),
            Set.of(RoleAssignment.forGroup(Role.GROUP_OWNER, oid(1))));
  }

  @Test
  void updateRoleAssignments_throwsErrorForChartsUser() {
    AppUser chartsUser = new AppUser();
    chartsUser.setUsername("<EMAIL>");

    SvcException svcException =
        assertThrows(
            SvcException.class,
            () ->
                userRoleSvc.updateRoleAssignments(
                    chartsUser, Set.of(), AuditInfoHelpers.fromSystem()));
    assertEquals(AppUserErrorCode.CANNOT_MODIFY_CHARTS_USER, svcException.getErrorCode());
  }

  @Test
  void updateRoleAssignments_success() {
    ObjectId appUserId = oid(1);
    AppUser appUser = mock(AppUser.class);
    AppUser updatedAppUser = mock(AppUser.class);
    when(appUser.getId()).thenReturn(appUserId);
    when(appUser.getActorId()).thenReturn(appUserId.toHexString());
    when(appUser.getRoles())
        .thenReturn(
            Set.of(
                RoleAssignment.forGlobal(Role.GLOBAL_OWNER),
                RoleAssignment.forGroup(Role.GROUP_OWNER, oid(1))));
    when(userDao.findById(eq(appUserId))).thenReturn(updatedAppUser);
    when(updatedAppUser.getCurrentGroupId()).thenReturn(oid(1));
    when(updatedAppUser.getCurrentOrgId()).thenReturn(null);
    when(authzSvc.isGlobalReadOnly(any(AppUser.class))).thenReturn(false);
    Set<RoleAssignment> rolesToReset =
        Set.of(
            RoleAssignment.forGlobal(Role.GLOBAL_OWNER),
            RoleAssignment.forGroup(Role.GROUP_USER_ADMIN, oid(1)));
    AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    assertDoesNotThrow(() -> userRoleSvc.updateRoleAssignments(appUser, rolesToReset, auditInfo));

    verify(userDao).resetRoleAssignments(appUserId, rolesToReset);
  }
}
