package com.xgen.cloud.user._private.policy.login;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.common.appsettings._public.model.MultiFactorAuthLevel;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.user._private.policy.login.UserLoginPolicy.AuthContext;
import com.xgen.cloud.user._public.error.legacy2fa.MultiFactorAuthErrorCode;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.legacy2fa.MultiFactorAuthSettings;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.cloud.user._public.svc.legacy2fa.MultiFactorAuthSvc;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class MultiFactorUserLoginPolicyUnitTests {
  private MultiFactorUserLoginPolicy instance;
  private MultiFactorAuthSettings googleAuthenticator;
  private MultiFactorAuthSettings smsAuthenticator;
  private MultiFactorAuthSvc multiFactorAuthSvc;
  private UserSvc userSvc;
  private AppSettings settings;

  @BeforeEach
  public void setUp() throws Exception {
    multiFactorAuthSvc = mock(MultiFactorAuthSvc.class);
    userSvc = mock(UserSvc.class);
    settings = mock(AppSettings.class);
    when(settings.getMultiFactorAuthLevel()).thenReturn(MultiFactorAuthLevel.REQUIRED);
    instance = new MultiFactorUserLoginPolicy(userSvc, settings, multiFactorAuthSvc);
    googleAuthenticator = new MultiFactorAuthSettings();
    googleAuthenticator.setGoogleAuthenticator(true);
    smsAuthenticator = new MultiFactorAuthSettings();
    smsAuthenticator.setPhone("123");
  }

  private void checkUserError(
      AppUser user, ErrorCode expectedErrorWithoutAuthcode, ErrorCode expectedErrorWithAuthcode)
      throws SvcException {
    AuthContext contextWithoutAuthcode = new AuthContext("username", "password", "useraddr", null);
    AuthContext contextWithAuthcode = new AuthContext("username", "password", "useraddr", "123");
    Mockito.doThrow(new SvcException(MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_CHECK_FAILED))
        .when(multiFactorAuthSvc)
        .checkAuthCode(user, null, null);
    Mockito.doThrow(new SvcException(MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_CHECK_FAILED))
        .when(multiFactorAuthSvc)
        .checkAuthCode(user, "123", null);
    for (ErrorCode code : UserLoginPolicy.SUCCESSFUL_LOGIN_CODES) {
      assertEquals(
          expectedErrorWithoutAuthcode,
          instance.enforce(user, CommonErrorCode.NONE, contextWithoutAuthcode).code);
      assertEquals(
          expectedErrorWithAuthcode,
          instance.enforce(user, CommonErrorCode.NONE, contextWithAuthcode).code);
    }
  }

  @Test
  public void basicTest() throws Exception {
    assertEquals(CommonErrorCode.NONE, instance.enforce(null, CommonErrorCode.NONE, null).code);
  }

  @Test
  public void testUnlockedUser() throws Exception {
    AppUser unlockedUser = new AppUser();
    checkUserError(unlockedUser, CommonErrorCode.NONE, CommonErrorCode.NONE);
    unlockedUser.setMultiFactorAuth(googleAuthenticator);
    checkUserError(
        unlockedUser,
        AppUserErrorCode.MULTI_FACTOR_AUTH_REQUIRED,
        MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_CHECK_FAILED);

    unlockedUser = new AppUser();
    checkUserError(unlockedUser, CommonErrorCode.NONE, CommonErrorCode.NONE);
    unlockedUser.setMultiFactorAuth(smsAuthenticator);
    checkUserError(
        unlockedUser,
        AppUserErrorCode.MULTI_FACTOR_AUTH_REQUIRED,
        MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_CHECK_FAILED);
  }

  @Test
  public void testNonXgenUser() throws Exception {
    AppUser nonXgenUser = new AppUser();
    checkUserError(nonXgenUser, CommonErrorCode.NONE, CommonErrorCode.NONE);
    nonXgenUser.setMultiFactorAuth(smsAuthenticator);
    checkUserError(
        nonXgenUser,
        AppUserErrorCode.MULTI_FACTOR_AUTH_REQUIRED,
        MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_CHECK_FAILED);
  }

  @Test
  public void testBackupAdminUser() throws Exception {
    AppUser backupAdminUser = new AppUser();
    backupAdminUser.setRoleAssignment(RoleAssignment.forGlobal(Role.GLOBAL_BACKUP_ADMIN), true);
    checkUserError(backupAdminUser, CommonErrorCode.NONE, CommonErrorCode.NONE);
    backupAdminUser.setMultiFactorAuth(googleAuthenticator);
    checkUserError(
        backupAdminUser,
        AppUserErrorCode.MULTI_FACTOR_AUTH_REQUIRED,
        MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_CHECK_FAILED);
    backupAdminUser.setMultiFactorAuth(smsAuthenticator);
    checkUserError(
        backupAdminUser,
        AppUserErrorCode.MULTI_FACTOR_AUTH_REQUIRED,
        MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_CHECK_FAILED);
  }

  @Test
  public void testMmsAdminUser() throws Exception {
    AppUser mmsAdminUser = new AppUser();
    mmsAdminUser.setRoleAssignment(RoleAssignment.forGlobal(Role.GLOBAL_MONITORING_ADMIN), true);
    checkUserError(mmsAdminUser, CommonErrorCode.NONE, CommonErrorCode.NONE);
    mmsAdminUser.setMultiFactorAuth(googleAuthenticator);
    checkUserError(
        mmsAdminUser,
        AppUserErrorCode.MULTI_FACTOR_AUTH_REQUIRED,
        MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_CHECK_FAILED);
    mmsAdminUser.setMultiFactorAuth(smsAuthenticator);
    checkUserError(
        mmsAdminUser,
        AppUserErrorCode.MULTI_FACTOR_AUTH_REQUIRED,
        MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_CHECK_FAILED);
  }

  @Test
  public void testGlobalReadOnlyUser() throws Exception {
    AppUser xgenUser = new AppUser();
    xgenUser.setRoleAssignment(RoleAssignment.forGlobal(Role.GLOBAL_READ_ONLY), true);
    checkUserError(xgenUser, CommonErrorCode.NONE, CommonErrorCode.NONE);
    xgenUser.setMultiFactorAuth(googleAuthenticator);
    checkUserError(
        xgenUser,
        AppUserErrorCode.MULTI_FACTOR_AUTH_REQUIRED,
        MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_CHECK_FAILED);
    xgenUser.setMultiFactorAuth(smsAuthenticator);
    checkUserError(
        xgenUser,
        AppUserErrorCode.MULTI_FACTOR_AUTH_REQUIRED,
        MultiFactorAuthErrorCode.MULTI_FACTOR_AUTH_CHECK_FAILED);
  }

  @Test
  public void testWhenMfaIsOff() {
    AppUser user = Mockito.mock(AppUser.class);
    MultiFactorAuthSettings mfaSettings = Mockito.mock(MultiFactorAuthSettings.class);
    Mockito.when(mfaSettings.isEnabled()).thenReturn(true);
    user.setMultiFactorAuth(mfaSettings);
    Mockito.when(settings.getMultiFactorAuthLevel()).thenReturn(MultiFactorAuthLevel.OFF);
    assertEquals(CommonErrorCode.NONE, instance.enforce(user, CommonErrorCode.NONE, null).code);
  }
}
