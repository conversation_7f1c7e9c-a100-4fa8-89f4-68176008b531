package com.xgen.cloud.user._private.policy.login;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.user._public.model.AppUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class LockedUserLoginPolicyUnitTests {
  private LockedUserLoginPolicy instance;
  private AppUser lockedUser;
  private AppUser unlockedUser;

  @BeforeEach
  public void setUp() throws Exception {
    instance = new LockedUserLoginPolicy(null, null);
    lockedUser = new AppUser();
    lockedUser.setIsLocked(true);
    unlockedUser = new AppUser();
    unlockedUser.setIsLocked(false);
  }

  @Test
  public void testLockedUser() throws Exception {
    assertEquals(CommonErrorCode.NONE, instance.enforce(null, CommonErrorCode.NONE, null).code);
    for (ErrorCode code : UserLoginPolicy.SUCCESSFUL_LOGIN_CODES) {
      assertEquals(AppUserErrorCode.ACCOUNT_LOCKED, instance.enforce(lockedUser, code, null).code);
    }
  }

  @Test
  public void testUnlockedUser() throws Exception {
    assertEquals(
        CommonErrorCode.NONE, instance.enforce(unlockedUser, CommonErrorCode.NONE, null).code);
    assertEquals(
        CommonErrorCode.SERVER_ERROR,
        instance.enforce(unlockedUser, CommonErrorCode.SERVER_ERROR, null).code);
  }
}
