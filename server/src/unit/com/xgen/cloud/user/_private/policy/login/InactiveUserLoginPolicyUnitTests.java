package com.xgen.cloud.user._private.policy.login;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.isA;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.auditinfosvc._public.svc.AuditInfoSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.constants._public.model.user.LockAccountReason;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.user._private.policy.login.UserLoginPolicy.AuthContext;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import java.util.Calendar;
import java.util.Date;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class InactiveUserLoginPolicyUnitTests {
  @Test
  public void testNoSettingShouldNotAffect() {
    AppSettings settings = mock(AppSettings.class);
    UserSvc userSvc = mock(UserSvc.class);
    AuthzSvc authzSvc = mock(AuthzSvc.class);
    AuditInfoSvc auditInfoSvc = new AuditInfoSvc(authzSvc);
    when(settings.hasProp(InactiveUserLoginPolicy.NS_LOCK_ACCOUNT_AFTER_INACTIVE_DAYS))
        .thenReturn(false);
    InactiveUserLoginPolicy policy = new InactiveUserLoginPolicy(userSvc, settings, auditInfoSvc);
    for (AppUserErrorCode code : AppUserErrorCode.values()) {
      assertEquals(code, policy.enforce(new AppUser(), code, null).code);
    }
  }

  @Test
  public void testInactiveUserShouldBeLocked() throws Exception {
    AppSettings settings = mockSettings(3);
    AppUser user = new AppUser();
    user.setId(ObjectId.get());
    for (int i = 10; i >= 3; i--) {
      Date date = getOldDate(i);
      user.setLastPageView(date);
      for (AppUserErrorCode code : AppUserErrorCode.values()) {
        UserSvc userSvc = mock(UserSvc.class);
        AuthzSvc authzSvc = mock(AuthzSvc.class);
        AuditInfoSvc auditInfoSvc = new AuditInfoSvc(authzSvc);
        InactiveUserLoginPolicy policy =
            new InactiveUserLoginPolicy(userSvc, settings, auditInfoSvc);
        if (policy.isAcceptedCode(code)) {
          assertEquals(
              AppUserErrorCode.LOCKED_INACTIVE_ACCOUNT,
              policy.enforce(user, code, mock(AuthContext.class)).code);
          verify(userSvc)
              .lockAccount(eq(user), eq(LockAccountReason.INACTIVE_ACCOUNT), isA(AuditInfo.class));
        }
      }
    }
  }

  private AppSettings mockSettings(int value) {
    AppSettings settings = mock(AppSettings.class);
    when(settings.hasProp(InactiveUserLoginPolicy.NS_LOCK_ACCOUNT_AFTER_INACTIVE_DAYS))
        .thenReturn(true);
    when(settings.getIntProp(InactiveUserLoginPolicy.NS_LOCK_ACCOUNT_AFTER_INACTIVE_DAYS))
        .thenReturn(value);
    return settings;
  }

  public static Date getOldDate(int howMany) {
    Calendar cal = TimeUtils.nowTime();
    TimeUtils.addDays(cal, -10);
    assertTrue(TimeUtils.diffInDays(TimeUtils.nowTime(), cal) > 3);
    return cal.getTime();
  }
}
