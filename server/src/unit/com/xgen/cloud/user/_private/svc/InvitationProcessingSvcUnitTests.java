package com.xgen.cloud.user._private.svc;

import static com.xgen.cloud.common.model._public.error.CommonErrorCode.SERVER_ERROR;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.usergroups._public.svc.MmsUserGroupSvc;
import com.xgen.cloud.activity._public.svc.alert.InformationalAlertSvc;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.authz.shared._public.exceptions.AuthzServiceClientErrorCode;
import com.xgen.cloud.authz.shared._public.exceptions.AuthzServiceClientException;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.email._public.svc.UserEmailSvc;
import com.xgen.cloud.externalanalytics._public.model.InvitationAcceptedEvent;
import com.xgen.cloud.externalanalytics._public.model.UserAddedToGroupEvent;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.invitation._public.model.Invitation;
import com.xgen.cloud.invitation._public.model.PolicyAssignment;
import com.xgen.cloud.invitation._public.svc.InvitationSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.team._private.dao.TeamDao;
import com.xgen.cloud.team._public.model.Team;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class InvitationProcessingSvcUnitTests {
  private static final ObjectId ORG_ID = ObjectId.get();
  private static final ObjectId GROUP_ID = ObjectId.get();

  @Mock private InvitationSvc mockInvitationSvc;
  @Mock private UserDao mockUserDao;
  @Mock private AuditSvc mockAuditSvc;
  @Mock private InformationalAlertSvc mockInformationalAlertSvc;
  @Mock private OrgAccessSvc mockOrgAccessSvc;
  @Mock private GroupSvc mockGroupSvc;
  @Mock private GroupAccessSvc mockGroupAccessSvc;
  @Mock private TeamAccessSvc mockTeamAccessSvc;
  @Mock private TeamDao mockTeamDao;
  @Mock private AuthzSvc mockAuthzSvc;
  @Mock private OrganizationSvc mockOrgSvc;
  @Mock private UserEmailSvc mockUserEmailSvc;
  @Mock private MmsUserGroupSvc mockMmsUserGroupSvc;

  @Mock private SegmentEventSvc mockSegmentEventSvc;

  private AppUser userDouble;
  @Mock private Organization mockOrg;

  @Mock private Group mockGroup;
  @Mock private Invitation mockInvitation;
  @Mock private AuditInfo mockAuditInfo;
  @Mock private Team team;

  private InvitationProcessingSvc invitationProcessingSvc;

  @BeforeEach
  public void setup() throws SvcException {
    when(mockOrgSvc.findById(ORG_ID)).thenReturn(mockOrg);
    when(mockGroupSvc.findById(GROUP_ID)).thenReturn(mockGroup);
    when(mockInvitation.getOrgId()).thenReturn(ORG_ID);
    mockAuditInfo = AuditInfoHelpers.fromInternal();
    userDouble = new AppUser();

    invitationProcessingSvc =
        new InvitationProcessingSvc(
            mockInvitationSvc,
            mockUserDao,
            mockOrgSvc,
            mockOrgAccessSvc,
            mockGroupSvc,
            mockGroupAccessSvc,
            mockAuditSvc,
            mockInformationalAlertSvc,
            mockTeamDao,
            mockTeamAccessSvc,
            mockAuthzSvc,
            mockUserEmailSvc,
            mockMmsUserGroupSvc,
            mockSegmentEventSvc);
  }

  @Test
  public void testProcessInvitation_whenFineGrainedAuthIsNotEnabled() throws SvcException {
    // GIVEN
    when(mockOrg.hasEnabledFeatureFlag(FeatureFlag.FINE_GRAINED_AUTH)).thenReturn(false);
    when(mockUserDao.findById(any())).thenReturn(userDouble);

    Set<Role> roles = Set.of();
    List<PolicyAssignment> policyAssignments = List.of();
    Set<Role> groupRoles = new HashSet<>();
    groupRoles.add(Role.GROUP_OWNER);
    HashMap<ObjectId, Set<Role>> rolesMap = new HashMap<>();
    rolesMap.put(GROUP_ID, groupRoles);
    when(mockInvitation.getRoles()).thenReturn(roles);
    when(mockInvitation.getInviterUsername()).thenReturn("<EMAIL>");
    when(mockInvitation.getPolicyAssignments()).thenReturn(policyAssignments);
    when(mockInvitation.getGroupRoleAssignmentsAsMap()).thenReturn(rolesMap);

    // WHEN
    invitationProcessingSvc.processInvitation(userDouble, mockInvitation, mockAuditInfo);

    // THEN
    verify(mockOrgAccessSvc).updateUserOrgSettings(userDouble, mockOrg, roles, mockAuditInfo);
    verify(mockOrgAccessSvc, never()).updateUserOrgSettings(userDouble, mockOrg, policyAssignments);
    ArgumentCaptor<UserAddedToGroupEvent> userAddedToGroupEventCaptor =
        ArgumentCaptor.forClass(UserAddedToGroupEvent.class);
    verify(mockSegmentEventSvc).submitEvent(userAddedToGroupEventCaptor.capture());
    UserAddedToGroupEvent userAddedToGroupEvent = userAddedToGroupEventCaptor.getValue();
    assertEquals(userAddedToGroupEvent.getEvent(), "User Added to Group");
    assertEquals(userAddedToGroupEvent.getProperties().getInvitationType(), "ORG");
    assertEquals(
        userAddedToGroupEvent.getProperties().getRoles(),
        List.of(Role.GROUP_OWNER.getDisplayName()));
    assertEquals(userAddedToGroupEvent.getProperties().getInviterUsername(), "<EMAIL>");
  }

  @Test
  public void testProcessInvitation_whenFineGrainedAuthIsEnabled() throws SvcException {
    // GIVEN
    givenFgaEnabled();
    when(mockUserDao.findById(any())).thenReturn(userDouble);

    Set<Role> roles = Set.of();
    List<PolicyAssignment> policyAssignments = List.of();
    when(mockInvitation.getRoles()).thenReturn(roles);
    when(mockInvitation.getPolicyAssignments()).thenReturn(policyAssignments);
    Set<Role> groupRoles = new HashSet<>();
    groupRoles.add(Role.GROUP_OWNER);
    HashMap<ObjectId, Set<Role>> rolesMap = new HashMap<>();
    rolesMap.put(GROUP_ID, groupRoles);
    when(mockInvitation.getGroupRoleAssignmentsAsMap()).thenReturn(rolesMap);
    when(mockInvitation.getInviterUsername()).thenReturn("<EMAIL>");

    // WHEN
    invitationProcessingSvc.processInvitation(userDouble, mockInvitation, mockAuditInfo);

    // THEN
    verify(mockOrgAccessSvc, never())
        .updateUserOrgSettings(userDouble, mockOrg, roles, mockAuditInfo);
    verify(mockOrgAccessSvc).updateUserOrgSettings(userDouble, mockOrg, policyAssignments);
    ArgumentCaptor<UserAddedToGroupEvent> userAddedToGroupEventCaptor =
        ArgumentCaptor.forClass(UserAddedToGroupEvent.class);
    verify(mockSegmentEventSvc).submitEvent(userAddedToGroupEventCaptor.capture());
    UserAddedToGroupEvent userAddedToGroupEvent = userAddedToGroupEventCaptor.getValue();
    assertEquals(userAddedToGroupEvent.getEvent(), "User Added to Group");
    assertEquals(userAddedToGroupEvent.getProperties().getInvitationType(), "ORG");
    assertEquals(
        userAddedToGroupEvent.getProperties().getRoles(),
        List.of(Role.GROUP_OWNER.getDisplayName()));
    assertEquals(userAddedToGroupEvent.getProperties().getInviterUsername(), "<EMAIL>");
  }

  @Test
  public void testProcessGroupInvitation_whenFineGrainedAuthIsNotEnabled() throws SvcException {
    // GIVEN
    when(mockOrg.hasEnabledFeatureFlag(FeatureFlag.FINE_GRAINED_AUTH)).thenReturn(false);
    when(mockUserDao.findById(any())).thenReturn(userDouble);

    Set<Role> roles = Set.of(Role.GROUP_OWNER);
    List<PolicyAssignment> policyAssignments = List.of();
    Set<Role> groupRoles = new HashSet<>();
    groupRoles.add(Role.GROUP_OWNER);
    HashMap<ObjectId, Set<Role>> rolesMap = new HashMap<>();
    rolesMap.put(GROUP_ID, groupRoles);
    when(mockInvitation.getRoles()).thenReturn(roles);
    when(mockInvitation.getGroupId()).thenReturn(GROUP_ID);
    when(mockInvitation.getPolicyAssignments()).thenReturn(policyAssignments);
    when(mockInvitation.getGroupRoleAssignmentsAsMap()).thenReturn(rolesMap);

    // WHEN
    invitationProcessingSvc.processInvitation(userDouble, mockInvitation, mockAuditInfo);

    // THEN
    verify(mockGroupAccessSvc)
        .updateUserGroupSettings(userDouble, mockGroup, roles, Set.of(), null);

    ArgumentCaptor<UserAddedToGroupEvent> userAddedToGroupEventCaptor =
        ArgumentCaptor.forClass(UserAddedToGroupEvent.class);
    verify(mockSegmentEventSvc).submitEvent(userAddedToGroupEventCaptor.capture());
    UserAddedToGroupEvent userAddedToGroupEvent = userAddedToGroupEventCaptor.getValue();
    assertEquals(userAddedToGroupEvent.getEvent(), "User Added to Group");
    assertEquals(userAddedToGroupEvent.getProperties().getInvitationType(), "GROUP");
    assertEquals(
        userAddedToGroupEvent.getProperties().getRoles(),
        List.of(Role.GROUP_OWNER.getDisplayName()));
  }

  @Test
  public void processInvitation_userGroupNotFound_doesNotThrowException() throws SvcException {
    givenFgaAndUserGroupsEnabled();
    when(mockInvitation.getUserGroupIds()).thenReturn(Set.of(new ObjectId()));
    doThrow(new AuthzServiceClientException(AuthzServiceClientErrorCode.NOT_FOUND))
        .when(mockMmsUserGroupSvc)
        .addExistingUserToUserGroup(any(), any(), any());

    assertDoesNotThrow(
        () -> invitationProcessingSvc.processInvitation(userDouble, mockInvitation, mockAuditInfo));
  }

  @Test
  public void processInvitation_userGroupError_throwsException() throws SvcException {
    givenFgaAndUserGroupsEnabled();
    when(mockInvitation.getUserGroupIds()).thenReturn(Set.of(new ObjectId()));
    doThrow(new AuthzServiceClientException(AuthzServiceClientErrorCode.INVALID_PARAMETERS))
        .when(mockMmsUserGroupSvc)
        .addExistingUserToUserGroup(any(), any(), any());

    SvcException e =
        assertThrows(
            SvcException.class,
            () ->
                invitationProcessingSvc.processInvitation(
                    userDouble, mockInvitation, mockAuditInfo));

    assertEquals(SERVER_ERROR, e.getErrorCode());
  }

  @Test
  @DisplayName(
      "When fga and user groups are not enabled, we should only process only teams on invites, no"
          + " user groups should be processed.")
  public void processInvitation_bothFeatureFlagsOff_processTeamsOnly() throws SvcException {
    ObjectId teamId = new ObjectId();
    when(mockInvitation.getTeamIds()).thenReturn(Set.of(teamId));
    when(mockTeamDao.findById(teamId)).thenReturn(team);

    invitationProcessingSvc.processInvitation(userDouble, mockInvitation, mockAuditInfo);

    verify(mockInvitation, times(0)).getUserGroupIds();
    verify(mockTeamAccessSvc, times(1)).addUserToTeam(userDouble, team, mockAuditInfo);
    verify(mockMmsUserGroupSvc, times(0)).addExistingUserToUserGroup(any(), any(), any());
  }

  @Test
  @DisplayName(
      "When fga or user groups is not enabled, we should process only teams"
          + " on invites, no user groups should be processed.")
  public void processInvitation_fgaAndUserGroupsFeatureFlagOff_processTeamsOnly()
      throws SvcException {
    ObjectId teamId = new ObjectId();
    when(mockInvitation.getTeamIds()).thenReturn(Set.of(teamId));
    when(mockTeamDao.findById(teamId)).thenReturn(team);

    invitationProcessingSvc.processInvitation(userDouble, mockInvitation, mockAuditInfo);

    verify(mockInvitation, times(0)).getUserGroupIds();
    verify(mockTeamAccessSvc, times(1)).addUserToTeam(userDouble, team, mockAuditInfo);
    verify(mockMmsUserGroupSvc, times(0)).addExistingUserToUserGroup(any(), any(), any());
  }

  @Test
  @DisplayName(
      "When fga and user groups are enabled, we should process only user groups on invites, no"
          + " teams should be processed.")
  public void processInvitation_bothFeatureFlagsOn_processUserGroupsOnly() throws SvcException {
    givenFgaAndUserGroupsEnabled();
    ObjectId userGroupId = new ObjectId();
    when(mockInvitation.getUserGroupIds()).thenReturn(Set.of(userGroupId));

    invitationProcessingSvc.processInvitation(userDouble, mockInvitation, mockAuditInfo);

    verify(mockMmsUserGroupSvc)
        .addExistingUserToUserGroup(eq(userGroupId), eq(userDouble.getId()), any());
    verify(mockTeamAccessSvc, times(0)).addUserToTeam(any(), any(), any());
  }

  @Test
  public void processInvitation_sendsInvitationAcceptedEventForNewUser() throws SvcException {
    // GIVEN
    userDouble.setLoginCount(0L);
    userDouble.setUsername("<EMAIL>");
    userDouble.setFirstName("Mark");
    userDouble.setLastName("Scout");
    when(mockOrg.hasEnabledFeatureFlag(FeatureFlag.FINE_GRAINED_AUTH)).thenReturn(false);
    when(mockUserDao.findById(any())).thenReturn(userDouble);

    Set<Role> roles = Set.of();
    List<PolicyAssignment> policyAssignments = List.of();
    Set<Role> groupRoles = new HashSet<>();
    groupRoles.add(Role.GROUP_OWNER);
    HashMap<ObjectId, Set<Role>> rolesMap = new HashMap<>();
    rolesMap.put(GROUP_ID, groupRoles);
    ObjectId orgId = new ObjectId();
    when(mockInvitation.getOrgId()).thenReturn(orgId);
    when(mockInvitation.getRoles()).thenReturn(roles);
    when(mockInvitation.getInviterUsername()).thenReturn("<EMAIL>");
    when(mockInvitation.getPolicyAssignments()).thenReturn(policyAssignments);
    when(mockInvitation.getGroupRoleAssignmentsAsMap()).thenReturn(rolesMap);

    // WHEN
    invitationProcessingSvc.processInvitation(userDouble, mockInvitation, mockAuditInfo);

    // THEN
    ArgumentCaptor<InvitationAcceptedEvent> invitationAcceptedEventArgumentCaptor =
        ArgumentCaptor.forClass(InvitationAcceptedEvent.class);
    verify(mockSegmentEventSvc).submitEvent(invitationAcceptedEventArgumentCaptor.capture());
    InvitationAcceptedEvent invitationAcceptedEvent =
        invitationAcceptedEventArgumentCaptor.getValue();
    assertEquals(invitationAcceptedEvent.getEvent(), "Invitation Accepted");
    assertTrue(invitationAcceptedEvent.getProperties().getIsNewUser());
    assertEquals(
        invitationAcceptedEvent.getProperties().getInviteeUsername(), "<EMAIL>");
    assertEquals(invitationAcceptedEvent.getProperties().getUserFirstName(), "Mark");
    assertEquals(invitationAcceptedEvent.getProperties().getUserLastName(), "Scout");
    assertEquals(invitationAcceptedEvent.getOrgId(), orgId);
  }

  @Test
  public void processInvitation_sendsInvitationAcceptedEventForExistingUser() throws SvcException {
    // GIVEN
    userDouble.setLoginCount(1L);
    userDouble.setUsername("<EMAIL>");
    userDouble.setFirstName("Mark");
    userDouble.setLastName("Scout");
    when(mockOrg.hasEnabledFeatureFlag(FeatureFlag.FINE_GRAINED_AUTH)).thenReturn(false);
    when(mockUserDao.findById(any())).thenReturn(userDouble);

    Set<Role> roles = Set.of();
    List<PolicyAssignment> policyAssignments = List.of();
    Set<Role> groupRoles = new HashSet<>();
    groupRoles.add(Role.GROUP_OWNER);
    HashMap<ObjectId, Set<Role>> rolesMap = new HashMap<>();
    rolesMap.put(GROUP_ID, groupRoles);
    ObjectId orgId = new ObjectId();
    when(mockInvitation.getOrgId()).thenReturn(orgId);
    when(mockInvitation.getRoles()).thenReturn(roles);
    when(mockInvitation.getInviterUsername()).thenReturn("<EMAIL>");
    when(mockInvitation.getPolicyAssignments()).thenReturn(policyAssignments);
    when(mockInvitation.getGroupRoleAssignmentsAsMap()).thenReturn(rolesMap);

    // WHEN
    invitationProcessingSvc.processInvitation(userDouble, mockInvitation, mockAuditInfo);

    // THEN
    ArgumentCaptor<InvitationAcceptedEvent> invitationAcceptedEventArgumentCaptor =
        ArgumentCaptor.forClass(InvitationAcceptedEvent.class);
    verify(mockSegmentEventSvc).submitEvent(invitationAcceptedEventArgumentCaptor.capture());
    InvitationAcceptedEvent invitationAcceptedEvent =
        invitationAcceptedEventArgumentCaptor.getValue();
    assertEquals(invitationAcceptedEvent.getEvent(), "Invitation Accepted");
    assertFalse(invitationAcceptedEvent.getProperties().getIsNewUser());
    assertEquals(
        invitationAcceptedEvent.getProperties().getInviteeUsername(), "<EMAIL>");
    assertEquals(invitationAcceptedEvent.getProperties().getUserFirstName(), "Mark");
    assertEquals(invitationAcceptedEvent.getProperties().getUserLastName(), "Scout");
    assertEquals(invitationAcceptedEvent.getOrgId(), orgId);
  }

  private void givenFgaEnabled() {
    when(mockOrg.hasEnabledFeatureFlag(FeatureFlag.FINE_GRAINED_AUTH)).thenReturn(true);
  }

  private void givenFgaAndUserGroupsEnabled() {
    when(mockMmsUserGroupSvc.areFgaAndUserGroupsFeaturesEnabled(any())).thenReturn(true);
  }
}
