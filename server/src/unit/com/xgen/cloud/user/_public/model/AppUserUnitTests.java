package com.xgen.cloud.user._public.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.access.role._public.model.TeamRoleAssignment;
import com.xgen.cloud.common.appsettings._public.model.ThemePreference;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.constants._public.model.user.UserType;
import com.xgen.cloud.common.security._public.util.CodecUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.user._public.model.legacy2fa.MultiFactorAuthSettings;
import com.xgen.svc.mms.BaseUTest;
import com.xgen.svc.mms.model.auth.UiAuthCode;
import com.xgen.svc.mms.model.auth.UiAuthMethod;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Test;

public class AppUserUnitTests extends BaseUTest {

  @Test
  public void forcePrimaryEmailToLowercase() {
    AppUser user = new AppUser();
    user.setPrimaryEmail("<EMAIL>");
    assertEquals("<EMAIL>", user.getPrimaryEmail());
  }

  @Test
  public void forceUsernamesToLowercase() {
    AppUser user = new AppUser();
    user.setUsername("USER");
    assertEquals("user", user.getUsername());
  }

  @Test
  public void getMobileNumber() {
    AppUser user1 = new AppUser();
    assertNull(user1.getPhoneNumber());

    AppUser user2 = new AppUser();
    MultiFactorAuthSettings auth2 = new MultiFactorAuthSettings();
    user2.setMultiFactorAuth(auth2);
    assertNull(user2.getMultiFactorAuth().getPhone());

    AppUser user3 = new AppUser();
    MultiFactorAuthSettings auth3 = new MultiFactorAuthSettings();
    auth3.setPhone("123");
    user3.setMultiFactorAuth(auth3);
    assertEquals("123", user3.getMultiFactorAuth().getPhone());
  }

  @Test
  public void testGetTeamRoles() {
    Group group = new Group();
    group.setTeams(
        Arrays.asList(
            new TeamRoleAssignment(
                oid(1), Set.of(Role.GROUP_MONITORING_ADMIN, Role.GROUP_BACKUP_ADMIN)),
            new TeamRoleAssignment(oid(2), Set.of(Role.GROUP_AUTOMATION_ADMIN)),
            new TeamRoleAssignment(oid(3), Set.of(Role.GROUP_OWNER))));
    AppUser user = new AppUser();
    user.setTeamIds(oids(1, 2, 4));
    Set<Role> teamRoles = user.getTeamRoles(group);
    assertEquals(
        Set.of(Role.GROUP_MONITORING_ADMIN, Role.GROUP_BACKUP_ADMIN, Role.GROUP_AUTOMATION_ADMIN),
        teamRoles);
  }

  @Test
  public void testGetRoleInOrganization() {
    AppUser user = new AppUser();
    user.assignRole(RoleAssignment.forOrg(Role.ORG_MEMBER, oid(100)));
    user.assignRole(RoleAssignment.forOrg(Role.ORG_BILLING_ADMIN, oid(101)));
    assertEquals(Role.ORG_MEMBER, user.getRoleInOrganization(oid(100)));
    assertEquals(Role.ORG_BILLING_ADMIN, user.getRoleInOrganization(oid(101)));
    assertNull(user.getRoleInOrganization(oid(102)));
  }

  @Test
  public void testHasType() {
    AppUser userHasLocalType = new AppUser();
    userHasLocalType.setType(UserType.LOCAL);
    assertEquals(userHasLocalType.getType(), UserType.LOCAL);

    AppUser userHasAPIType = new AppUser();
    userHasAPIType.setType(UserType.API);
    assertEquals(userHasAPIType.getType(), UserType.API);
  }

  @Test
  public void testHasThemePreferenceFromAppSettings() {
    AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isDarkModePreviewEnabled()).thenReturn(true);
    when(appSettings.getDefaultTheme()).thenReturn(ThemePreference.OS);

    AppUser user = new AppUser();
    assertEquals(user.getThemePreference(appSettings), ThemePreference.OS); // Default to OS

    when(appSettings.isDarkModePreviewEnabled()).thenReturn(false);
    assertEquals(
        user.getThemePreference(appSettings),
        ThemePreference.LIGHT); // Default to Light if disabled
  }

  @Test
  public void testChartsPersonalApiKeyUser() {
    AppUser user = new AppUser();

    user.setUsername("<EMAIL>");
    assertFalse(user.isChartsApiKeyUser());

    user.setUsername("<EMAIL>");
    assertFalse(user.isChartsApiKeyUser());

    user.setUsername("<EMAIL>");
    assertTrue(user.isChartsApiKeyUser());
  }

  @Test
  public void testGetAuid() {
    AppUser user = new AppUser();
    String oktaId = "oktaId";
    user.setOktaUserId(oktaId);
    assertEquals(CodecUtils.sha256Hex(oktaId), user.getAuid());
    user.setOktaUserId(null);
    assertEquals(null, user.getAuid());
  }

  @Test
  public void testGetLastNonRegionalTokenAuthMethod() {
    UiAuthCode lessRecentAuthCode = new UiAuthCode("lessRecent", UiAuthMethod.GOOGLE);
    UiAuthCode mostRecentAuthCode = new UiAuthCode("mostRecent", UiAuthMethod.REGIONAL_TOKEN);
    AppUser user = spy(new AppUser());
    when(user.getUiAuthCodes()).thenReturn(List.of(mostRecentAuthCode, lessRecentAuthCode));
    assertEquals(user.getLastNonRegionalTokenAuthMethod(), UiAuthMethod.GOOGLE);
  }

  @Test
  public void testMfaSetupRequiredAtLogin() {
    AppUser user = new AppUser();
    assertFalse(user.isMfaSetupRequiredAtLogin());

    user.setMfaSetupRequiredAtLogin(true);
    assertTrue(user.isMfaSetupRequiredAtLogin());

    user.setMfaSetupRequiredAtLogin(false);
    assertFalse(user.isMfaSetupRequiredAtLogin());
  }

  @Test
  public void testIsProgrammaticUser_whenUserIsInternalClientType_returnsTrue() {
    AppUser user = new AppUser();

    user.setType(UserType.INTERNAL_CLIENT);

    assertTrue(user.isProgrammaticUser());
  }
}
