package com.xgen.cloud.user._public.model.activity.view;

import static com.xgen.cloud.user._public.model.activity.ApiUserEvent.TYPE_NAME;
import static com.xgen.cloud.user._public.model.activity.ApiUserEvent.Type.API_KEY_CREATED;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;

import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.user._public.model.activity.ApiUserAudit;
import com.xgen.cloud.user._public.model.activity.ApiUserEvent;
import java.util.Date;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class ApiUserEventViewUnitTests {

  private static final String PUBLIC_KEY = "<EMAIL>";
  private static final String IP_ADDRESS = "0.0.0.0";

  @Test
  public void shouldPopulateBasicApiUserAuditFields() {
    ApiUserAudit model = givenApiUserAudit();

    ApiUserEventView view = new ApiUserEventView(model, null);

    assertThat(view.getWhitelistEntry(), is(IP_ADDRESS));
    assertThat(view.getTargetPublicKey(), is(PUBLIC_KEY));
  }

  @Test
  public void shouldNotPopulateAuditFieldsWhenEventIsNotAudit() {
    ApiUserEvent model = givenApiUserEvent();

    ApiUserEventView view = new ApiUserEventView(model, null);

    assertThat(view.getWhitelistEntry(), nullValue());
    assertThat(view.getTargetPublicKey(), nullValue());
  }

  private ApiUserAudit givenApiUserAudit() {
    AuditInfo auditInfo = new AuditInfo(IP_ADDRESS, "myClient", null, null);
    ApiUserAudit.Builder auditBuilder = new ApiUserAudit.Builder(API_KEY_CREATED, auditInfo);
    auditBuilder.groupId(ObjectId.get());
    auditBuilder.orgId(ObjectId.get());
    auditBuilder.createdAt(new Date());
    auditBuilder.ipAddress(IP_ADDRESS);
    auditBuilder.publicKey(PUBLIC_KEY);
    return auditBuilder.build();
  }

  private ApiUserEvent givenApiUserEvent() {
    ApiUserEvent.Builder auditBuilder =
        new ApiUserEvent.Builder(TYPE_NAME, API_KEY_CREATED, ObjectId.get());
    auditBuilder.groupId(ObjectId.get());
    auditBuilder.orgId(ObjectId.get());
    auditBuilder.createdAt(new Date());
    return auditBuilder.build();
  }
}
