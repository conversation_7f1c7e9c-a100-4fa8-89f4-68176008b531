package com.xgen.cloud.user._public.svc;

import static com.xgen.cloud.user._public.svc.LegacyUsernameSvc.isLegacyOrInvalidUsername;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.user._private.dao.LegacyUsernameDao;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class LegacyUsernameSvcUnitTests {
  private AppSettings appSettings;
  private LegacyUsernameSvc legacyUsernameSvc;
  private LegacyUsernameDao legacyUsernameDao;

  @BeforeEach
  public void setup() {
    appSettings = mock(AppSettings.class);
    legacyUsernameDao = mock(LegacyUsernameDao.class);
    legacyUsernameSvc = new LegacyUsernameSvc(appSettings, legacyUsernameDao);
  }

  @Test
  public void getOktaUsernameFromLegacyUsername_returnsOktaUsername_whenPresent() {
    String oktaUsername = "oktaUsername";
    String legacyUsername = "legacyUsername";

    doReturn(Optional.of(oktaUsername)).when(legacyUsernameDao).getOktaUsername(legacyUsername);

    Optional<String> oktaUsernameOpt =
        legacyUsernameSvc.getOktaUsernameFromLegacyUsername(legacyUsername);

    assertTrue(oktaUsernameOpt.isPresent());
    assertThat(oktaUsernameOpt.get(), equalTo(oktaUsername));
  }

  @Test
  public void getOktaUsernameFromLegacyUsername_returnsEmptyOptional_whenNotPresent() {
    String legacyUsername = "legacyUsername";

    doReturn(Optional.empty()).when(legacyUsernameDao).getOktaUsername(legacyUsername);

    Optional<String> oktaUsernameOpt =
        legacyUsernameSvc.getOktaUsernameFromLegacyUsername(legacyUsername);

    assertFalse(oktaUsernameOpt.isPresent());
  }

  @Test
  public void getLegacyUsernameFromOktaUsername_returnsOktaUsername_whenPresent() {
    String oktaUsername = "oktaUsername";
    String legacyUsername = "legacyUsername";

    doReturn(Optional.of(legacyUsername)).when(legacyUsernameDao).getLegacyUsername(oktaUsername);

    Optional<String> legacyUsernameOpt =
        legacyUsernameSvc.getLegacyUsernameFromOktaUsername(oktaUsername);

    assertTrue(legacyUsernameOpt.isPresent());
    assertThat(legacyUsernameOpt.get(), equalTo(legacyUsername));
  }

  @Test
  public void getLegacyUsernameFromOktaUsername_returnsEmptyOptional_whenNotPresent() {
    String username = "legacyUsername";

    doReturn(Optional.empty()).when(legacyUsernameDao).getLegacyUsername(username);

    Optional<String> legacyUsernameOpt =
        legacyUsernameSvc.getLegacyUsernameFromOktaUsername(username);

    assertFalse(legacyUsernameOpt.isPresent());
  }

  @Test
  public void isLegacyOrInvalidUsername_returnsTrue_whenLegacyUsername() {
    assertTrue(isLegacyOrInvalidUsername("<EMAIL>"));
  }

  @Test
  public void isLegacyOrInvalidUsername_returnsTrue_whenInvalidUsername() {
    assertTrue(isLegacyOrInvalidUsername("<EMAIL>"));
  }

  @Test
  public void isLegacyOrInvalidUsername_returnsTrue_whenInvalidUsernameWithAllLowercase() {
    assertTrue(isLegacyOrInvalidUsername("<EMAIL>"));
  }

  @Test
  public void isLegacyOrInvalidUsername_returnsFalse_whenNormalUsername() {
    assertFalse(isLegacyOrInvalidUsername("<EMAIL>"));
  }

  @Test
  public void isLegacyUser_whenUserHasNoOktaUsername_returnsFalse() {
    String username = "regularUsername";
    doReturn(Optional.empty()).when(legacyUsernameDao).getOktaUsername(username);
    assertFalse(legacyUsernameSvc.isLegacyUser(username));
  }

  @Test
  public void isLegacyUser_whenUserHasOktaUsername_returnsTrue() {
    String username = "legacyUsername";
    doReturn(Optional.of("oktaUsername")).when(legacyUsernameDao).getOktaUsername(username);
    assertTrue(legacyUsernameSvc.isLegacyUser(username));
  }
}
