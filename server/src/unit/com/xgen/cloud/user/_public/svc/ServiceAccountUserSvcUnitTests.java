package com.xgen.cloud.user._public.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.UserAllowList;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ServiceAccountUserSvcUnitTests {
  private static final String CLIENT_ID = "asa_test";

  @Mock private UserDao mockUserDao;
  @Mock private UserSvc mockUserSvc;
  @Mock private UserAllowListSvc mockAllowListSvc;

  @InjectMocks private ServiceAccountUserSvc subject;

  @Captor private ArgumentCaptor<AppUser> appUserCaptor;

  @Test
  public void testCreateClient_withDesiredRoles() throws Exception {
    var orgId = ObjectId.get();
    var groupId = ObjectId.get();
    var testRole = RoleAssignment.forOrg(Role.ORG_MEMBER, orgId);
    var projectRole = RoleAssignment.forGroup(Role.GROUP_CHARTS_ADMIN, groupId);
    subject.createServiceAccountUser(CLIENT_ID, List.of(testRole, projectRole));

    verify(mockUserDao, times(1)).save(appUserCaptor.capture());
    verify(mockUserSvc, times(1)).createActorProxy(CLIENT_ID, List.of(testRole, projectRole));

    AppUser savedAppUser = appUserCaptor.getValue();
    assertEquals(CLIENT_ID, savedAppUser.getUsername());

    assertTrue(savedAppUser.getRoles().contains(RoleAssignment.forOrg(Role.ORG_MEMBER, orgId)));
    assertTrue(
        savedAppUser
            .getRoles()
            .contains(RoleAssignment.forGroup(Role.GROUP_CHARTS_ADMIN, groupId)));
  }

  @Test
  public void testDeleteClient() throws Exception {
    RoleAssignment expectedRoleAssignment1 = RoleAssignment.forGlobal(Role.GLOBAL_READ_ONLY);
    RoleAssignment expectedRoleAssignment2 = RoleAssignment.forGlobal(Role.GLOBAL_METERING_USER);

    AppUser mockAppUser = new AppUser();
    mockAppUser.setUsername(CLIENT_ID);
    mockAppUser.setRoles(Set.of(expectedRoleAssignment1, expectedRoleAssignment2));

    when(mockUserDao.findByUsername(CLIENT_ID)).thenReturn(mockAppUser);
    subject.deleteServiceAccountUser(CLIENT_ID);

    verify(mockUserDao, times(1)).save(appUserCaptor.capture());
    verify(mockUserSvc, times(1)).deleteActorProxy(CLIENT_ID);

    AppUser savedAppUser = appUserCaptor.getValue();
    assertTrue(savedAppUser.getRoles().isEmpty());
    assertTrue(savedAppUser.isDeleted());
    assertNotNull(savedAppUser.getDeletedDate());
  }

  @Test
  public void testDeleteClient_withNullClientId() {
    var ex = assertThrows(SvcException.class, () -> subject.deleteServiceAccountUser(null));
    verifyNoInteractions(mockUserDao);
    verifyNoInteractions(mockUserSvc);
    assertEquals(CommonErrorCode.BAD_REQUEST, ex.getErrorCode());
  }

  @Test
  public void testDeleteClient_withEmptyClientId() {
    var svcException =
        assertThrows(SvcException.class, () -> subject.deleteServiceAccountUser(" "));
    verifyNoInteractions(mockUserDao);
    verifyNoInteractions(mockUserSvc);
    assertEquals(CommonErrorCode.BAD_REQUEST, svcException.getErrorCode());
  }

  @Test
  public void testDeleteAccessListEntries() {
    AppUser mockAppUser = new AppUser();
    UserAllowList allowList = new UserAllowList("1.1.1.1.1");

    when(mockAllowListSvc.findByUserId(mockAppUser.getId())).thenReturn(List.of(allowList));
    subject.deleteAccessListEntries(mockAppUser);
    verify(mockAllowListSvc, times(1)).remove(allowList.getId());
  }

  @Test
  public void testSetShouldApplyOrgUiAccessListForApi() throws SvcException {
    String clientId = "ID";
    AppUser mockAppUser = new AppUser();
    mockAppUser.setUsername(clientId);
    subject.setShouldApplyOrgUiAccessListForApi(mockAppUser, true);
    verify(mockUserSvc, times(1)).setShouldApplyOrgUiAccessListForApi(mockAppUser.getId(), true);
  }

  @Test
  public void testGetGlobalServiceAccountUser() throws SvcException {
    AppUser mockAppUser = new AppUser();
    mockAppUser.setUsername(CLIENT_ID);

    when(mockUserDao.getGlobalServiceAccountUser(CLIENT_ID)).thenReturn(mockAppUser);

    AppUser fetchedUser = subject.getGlobalServiceAccountUser(CLIENT_ID);
    assertEquals(mockAppUser, fetchedUser);
  }

  @Test
  public void testGetGlobalServiceAccountUser_throwsExceptionWhenUserNotFound() {
    when(mockUserDao.getGlobalServiceAccountUser(CLIENT_ID)).thenReturn(null);

    final SvcException ex =
        assertThrows(SvcException.class, () -> subject.getGlobalServiceAccountUser(CLIENT_ID));

    assertEquals(CommonErrorCode.NOT_FOUND, ex.getErrorCode());
  }

  @Test
  public void testGetGlobalServiceAccountUser_throwsExceptionWhenUserIsDeleted() {
    AppUser mockAppUser = new AppUser();
    mockAppUser.setUsername(CLIENT_ID);
    mockAppUser.setDeleted(true);

    when(mockUserDao.getGlobalServiceAccountUser(CLIENT_ID)).thenReturn(mockAppUser);

    final SvcException ex =
        assertThrows(SvcException.class, () -> subject.getGlobalServiceAccountUser(CLIENT_ID));

    assertEquals(CommonErrorCode.NOT_FOUND, ex.getErrorCode());
  }
}
