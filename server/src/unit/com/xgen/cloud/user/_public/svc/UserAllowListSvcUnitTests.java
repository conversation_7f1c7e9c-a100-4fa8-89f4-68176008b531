package com.xgen.cloud.user._public.svc;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.constants._public.model.user.UserType;
import com.xgen.cloud.user._public.model.UserAllowList;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class UserAllowListSvcUnitTests {

  @Test
  public void testIsValidAddress() {
    UserAllowList list = Mockito.mock(UserAllowList.class);

    assertFalse(UserAllowListSvc.isValidAddress(null, "127.0.0.1", UserType.LOCAL));

    Mockito.when(list.getIpAddress()).thenReturn("127.0.0.1");
    assertTrue(UserAllowListSvc.isValidAddress(list, "127.0.0.1", UserType.LOCAL));

    Mockito.when(list.getIpAddress()).thenReturn("::1");
    assertTrue(UserAllowListSvc.isValidAddress(list, "[0:0:0:0:0:0:0:1]", UserType.LOCAL));

    Mockito.when(list.getIpAddress()).thenReturn("********/24");
    assertTrue(UserAllowListSvc.isValidAddress(list, "********", UserType.LOCAL));
    assertTrue(UserAllowListSvc.isValidAddress(list, "**********", UserType.LOCAL));

    Mockito.when(list.getIpAddress()).thenReturn("");
    assertFalse(UserAllowListSvc.isValidAddress(list, "127.0.0.1", UserType.LOCAL));

    Mockito.when(list.getIpAddress()).thenReturn(null);
    assertFalse(UserAllowListSvc.isValidAddress(list, "127.0.0.1", UserType.LOCAL));

    Mockito.when(list.getIpAddress()).thenReturn("*********");
    assertFalse(UserAllowListSvc.isValidAddress(list, "127.0.0.1", UserType.LOCAL));

    Mockito.when(list.getIpAddress()).thenReturn("********/24");
    assertFalse(UserAllowListSvc.isValidAddress(list, "********", UserType.LOCAL));

    Mockito.when(list.getIpAddress()).thenReturn("********");
    assertTrue(UserAllowListSvc.isValidAddress(list, "********", UserType.SERVICE_ACCOUNT));
    assertFalse(UserAllowListSvc.isValidAddress(list, "********", UserType.SERVICE_ACCOUNT));

    Mockito.when(list.getIpAddress()).thenReturn("********/24");
    assertTrue(UserAllowListSvc.isValidAddress(list, "********", UserType.SERVICE_ACCOUNT));
    assertFalse(UserAllowListSvc.isValidAddress(list, "********", UserType.SERVICE_ACCOUNT));

    Mockito.when(list.getIpAddress()).thenReturn("********/24");
    assertTrue(UserAllowListSvc.isValidAddress(list, "********/24", UserType.SERVICE_ACCOUNT));
    assertFalse(UserAllowListSvc.isValidAddress(list, "********/24", UserType.SERVICE_ACCOUNT));
    assertFalse(UserAllowListSvc.isValidAddress(list, "********/8", UserType.SERVICE_ACCOUNT));
  }
}
