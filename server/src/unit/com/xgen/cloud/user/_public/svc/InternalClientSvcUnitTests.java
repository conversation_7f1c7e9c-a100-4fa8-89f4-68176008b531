package com.xgen.cloud.user._public.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.common.constants._public.model.user.UserType;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.user._private.dao.UserDao;
import com.xgen.cloud.user._public.model.AppUser;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class InternalClientSvcUnitTests {

  static final String MOCK_CLIENT_ID = "mdb_ic_id_example";

  @Mock private UserSvc mockUserSvc;
  @Mock private UserDao mockUserDao;

  @InjectMocks private InternalClientSvc internalClientSvc;

  @Captor private ArgumentCaptor<AppUser> appUserCaptor;

  @Test
  public void testFindAllClients_queriesByInternalClientUserType() {
    internalClientSvc.findAllClients();
    verify(mockUserSvc, times(1)).findAllUsersByType(Set.of(UserType.INTERNAL_CLIENT));
  }

  @Test
  public void testGetGlobalRolesForClientId_queriesByUsername() throws SvcException {
    AppUser mockAppUser = new AppUser();
    mockAppUser.setUsername(MOCK_CLIENT_ID);
    mockAppUser.setRoles(null);

    when(mockUserSvc.findByUsername(MOCK_CLIENT_ID)).thenReturn(mockAppUser);

    assertEquals(new HashSet<>(), internalClientSvc.getGlobalRolesForClientId(MOCK_CLIENT_ID));

    verify(mockUserSvc, times(1)).findByUsername(MOCK_CLIENT_ID);
  }

  @Test
  public void testGetGlobalRolesForClientId_returnsExpectedRoles() throws SvcException {
    RoleAssignment expectedRoleAssignment1 = RoleAssignment.forGlobal(Role.GLOBAL_READ_ONLY);
    RoleAssignment expectedRoleAssignment2 = RoleAssignment.forGlobal(Role.GLOBAL_METERING_USER);

    AppUser mockAppUser = new AppUser();
    mockAppUser.setUsername(MOCK_CLIENT_ID);
    mockAppUser.setRoles(Set.of(expectedRoleAssignment1, expectedRoleAssignment2));

    when(mockUserSvc.findByUsername(MOCK_CLIENT_ID)).thenReturn(mockAppUser);

    assertEquals(
        Set.of(Role.GLOBAL_READ_ONLY, Role.GLOBAL_METERING_USER),
        internalClientSvc.getGlobalRolesForClientId(MOCK_CLIENT_ID));

    verify(mockUserSvc, times(1)).findByUsername(MOCK_CLIENT_ID);
  }

  @Test
  public void testGetGlobalRolesForClientId_throwsSvcExceptionWhenNotFound() {
    SvcException svcException =
        assertThrows(
            SvcException.class, () -> internalClientSvc.getGlobalRolesForClientId(MOCK_CLIENT_ID));
    verify(mockUserSvc, times(1)).findByUsername(MOCK_CLIENT_ID);
    assertEquals(CommonErrorCode.NOT_FOUND, svcException.getErrorCode());
  }

  @Test
  public void testCreateClient_withEmptyDesiredRoles() throws Exception {
    internalClientSvc.createClient(MOCK_CLIENT_ID, new HashSet<>());

    verify(mockUserDao, times(1)).save(appUserCaptor.capture());
    verify(mockUserSvc, times(1)).createActorProxy(MOCK_CLIENT_ID, new HashSet<>());

    AppUser savedAppUser = appUserCaptor.getValue();
    assertEquals(MOCK_CLIENT_ID, savedAppUser.getUsername());
    assertEquals(new ArrayList<>(), savedAppUser.getGlobalRoles());
  }

  @Test
  public void testCreateClient_withDesiredRoles() throws Exception {
    Set<Role> desiredGlobalRoles = Set.of(Role.GLOBAL_READ_ONLY, Role.GLOBAL_METERING_USER);
    Set<RoleAssignment> roleAssignments =
        desiredGlobalRoles.stream().map(RoleAssignment::forGlobal).collect(Collectors.toSet());
    internalClientSvc.createClient(MOCK_CLIENT_ID, desiredGlobalRoles);

    verify(mockUserDao, times(1)).save(appUserCaptor.capture());
    verify(mockUserSvc, times(1)).createActorProxy(MOCK_CLIENT_ID, roleAssignments);

    AppUser savedAppUser = appUserCaptor.getValue();
    assertEquals(MOCK_CLIENT_ID, savedAppUser.getUsername());
    assertEquals(desiredGlobalRoles, new HashSet<>(savedAppUser.getGlobalRoles()));
  }

  @Test
  public void testCreateClient_withNullDesiredRoles() {
    SvcException svcException =
        assertThrows(
            SvcException.class, () -> internalClientSvc.createClient(MOCK_CLIENT_ID, null));
    verifyNoInteractions(mockUserSvc);
    verifyNoInteractions(mockUserDao);
    assertEquals(CommonErrorCode.INVALID_PARAMETER, svcException.getErrorCode());
  }

  @Test
  public void testCreateClient_withNullClientId() {
    SvcException svcException =
        assertThrows(
            SvcException.class, () -> internalClientSvc.createClient(null, new HashSet<>()));
    verifyNoInteractions(mockUserSvc);
    verifyNoInteractions(mockUserDao);
    assertEquals(CommonErrorCode.INVALID_PARAMETER, svcException.getErrorCode());
  }

  @Test
  public void testCreateClient_withEmptyClientId() {
    SvcException svcException =
        assertThrows(
            SvcException.class, () -> internalClientSvc.createClient("  ", new HashSet<>()));
    verifyNoInteractions(mockUserSvc);
    verifyNoInteractions(mockUserDao);
    assertEquals(CommonErrorCode.INVALID_PARAMETER, svcException.getErrorCode());
  }

  @Test
  public void testDeleteClient() throws Exception {
    RoleAssignment expectedRoleAssignment1 = RoleAssignment.forGlobal(Role.GLOBAL_READ_ONLY);
    RoleAssignment expectedRoleAssignment2 = RoleAssignment.forGlobal(Role.GLOBAL_METERING_USER);

    AppUser mockAppUser = new AppUser();
    mockAppUser.setUsername(MOCK_CLIENT_ID);
    mockAppUser.setRoles(Set.of(expectedRoleAssignment1, expectedRoleAssignment2));

    when(mockUserSvc.findByUsername(MOCK_CLIENT_ID)).thenReturn(mockAppUser);
    internalClientSvc.deleteClient(MOCK_CLIENT_ID);

    verify(mockUserDao, times(1)).save(appUserCaptor.capture());
    verify(mockUserSvc, times(1)).deleteActorProxy(MOCK_CLIENT_ID);

    AppUser savedAppUser = appUserCaptor.getValue();
    assertEquals(new HashSet<>(), savedAppUser.getRoles());
    assertTrue(savedAppUser.isDeleted());
    assertNotNull(savedAppUser.getDeletedDate());
  }

  @Test
  public void testDeleteClient_withNullClientId() {
    SvcException svcException =
        assertThrows(SvcException.class, () -> internalClientSvc.deleteClient(null));
    verifyNoInteractions(mockUserSvc);
    verifyNoInteractions(mockUserDao);
    assertEquals(CommonErrorCode.INVALID_PARAMETER, svcException.getErrorCode());
  }

  @Test
  public void testDeleteClient_withEmptyClientId() {
    SvcException svcException =
        assertThrows(SvcException.class, () -> internalClientSvc.deleteClient(" "));
    verifyNoInteractions(mockUserSvc);
    verifyNoInteractions(mockUserDao);
    assertEquals(CommonErrorCode.INVALID_PARAMETER, svcException.getErrorCode());
  }

  @Test
  public void testUpdateClientGlobalRoles() throws Exception {
    RoleAssignment existingRole1 = RoleAssignment.forGlobal(Role.GLOBAL_READ_ONLY);
    RoleAssignment existingRole2 = RoleAssignment.forGlobal(Role.GLOBAL_METERING_USER);

    AppUser mockAppUser = new AppUser();
    mockAppUser.setUsername(MOCK_CLIENT_ID);
    mockAppUser.setRoles(Set.of(existingRole1, existingRole2));

    when(mockUserSvc.findByUsername(MOCK_CLIENT_ID)).thenReturn(mockAppUser);

    Set<Role> desiredRoles = Set.of(Role.GLOBAL_BAAS_ADMIN);
    Set<RoleAssignment> roleAssignments =
        Set.of(Role.GLOBAL_BAAS_ADMIN).stream()
            .map(RoleAssignment::forGlobal)
            .collect(Collectors.toSet());
    internalClientSvc.updateClientGlobalRoles(MOCK_CLIENT_ID, desiredRoles);

    verify(mockUserDao, times(1)).save(appUserCaptor.capture());
    verify(mockUserSvc, times(1))
        .processUserAccessChangeProxyWithUsername(
            mockAppUser, MOCK_CLIENT_ID, roleAssignments, Set.of(existingRole1, existingRole2));

    AppUser savedAppUser = appUserCaptor.getValue();
    assertEquals(List.of(Role.GLOBAL_BAAS_ADMIN), savedAppUser.getGlobalRoles());
  }

  @Test
  public void testUpdateClientGlobalRoles_noopUpdate() throws Exception {
    RoleAssignment existingRole1 = RoleAssignment.forGlobal(Role.GLOBAL_READ_ONLY);
    RoleAssignment existingRole2 = RoleAssignment.forGlobal(Role.GLOBAL_METERING_USER);

    AppUser mockAppUser = new AppUser();
    mockAppUser.setUsername(MOCK_CLIENT_ID);
    mockAppUser.setRoles(Set.of(existingRole1, existingRole2));

    when(mockUserSvc.findByUsername(MOCK_CLIENT_ID)).thenReturn(mockAppUser);

    internalClientSvc.updateClientGlobalRoles(
        MOCK_CLIENT_ID, Set.of(Role.GLOBAL_READ_ONLY, Role.GLOBAL_METERING_USER));

    verify(mockUserDao, times(0)).save(appUserCaptor.capture());
    verify(mockUserSvc, times(0))
        .processUserAccessChangeProxyWithUsername(any(), any(), any(), any());
  }

  @Test
  public void testUpdateClientGlobalRoles_removeAllRoles() throws Exception {
    RoleAssignment existingRole1 = RoleAssignment.forGlobal(Role.GLOBAL_READ_ONLY);
    RoleAssignment existingRole2 = RoleAssignment.forGlobal(Role.GLOBAL_METERING_USER);

    AppUser mockAppUser = new AppUser();
    mockAppUser.setUsername(MOCK_CLIENT_ID);
    mockAppUser.setRoles(Set.of(existingRole1, existingRole2));

    when(mockUserSvc.findByUsername(MOCK_CLIENT_ID)).thenReturn(mockAppUser);

    internalClientSvc.updateClientGlobalRoles(MOCK_CLIENT_ID, Set.of());

    verify(mockUserDao).save(appUserCaptor.capture());
    verify(mockUserSvc)
        .processUserAccessChangeProxyWithUsername(
            mockAppUser, MOCK_CLIENT_ID, new HashSet<>(), Set.of(existingRole1, existingRole2));

    AppUser savedAppUser = appUserCaptor.getValue();
    assertEquals(List.of(), savedAppUser.getGlobalRoles());
  }

  @Test
  public void testUpdateClientGlobalRoles_withNonexistentClientId() {
    SvcException svcException =
        assertThrows(
            SvcException.class, () -> internalClientSvc.getGlobalRolesForClientId(MOCK_CLIENT_ID));
    verify(mockUserSvc, times(1)).findByUsername(MOCK_CLIENT_ID);
    assertEquals(CommonErrorCode.NOT_FOUND, svcException.getErrorCode());
  }
}
