package com.xgen.cloud.user._public.model.activity.view;

import static com.xgen.cloud.user._public.model.activity.UserEvent.Type.JOINED_GROUP;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

import com.xgen.cloud.user._public.model.activity.UserEvent;
import java.util.Date;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class UserEventViewUnitTests {

  private static final String USER_NAME = "testUser";

  @Test
  public void shouldPopulateBasicUserEventFields() {
    UserEvent model = givenUserEvent();

    UserEventView view = new UserEventView(model, null);

    assertThat(view.getTargetUsername(), is(USER_NAME));
  }

  private UserEvent givenUserEvent() {
    UserEvent.Builder eventBuilder = new UserEvent.Builder(JOINED_GROUP, ObjectId.get());
    eventBuilder.groupId(ObjectId.get());
    eventBuilder.orgId(ObjectId.get());
    eventBuilder.createdAt(new Date());
    eventBuilder.targetUsername(USER_NAME);
    return eventBuilder.build();
  }
}
