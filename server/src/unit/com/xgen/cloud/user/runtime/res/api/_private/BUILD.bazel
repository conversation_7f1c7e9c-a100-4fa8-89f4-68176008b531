load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*UnitTests.java"]),
    deny_warnings = True,
    deps = [
        "//server/src/main/com/xgen/cloud/abtesting",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/authn",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/groupcreation",
        "//server/src/main/com/xgen/cloud/common/user",
        "//server/src/main/com/xgen/cloud/externalanalytics",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/invitation",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/partnerintegrations/vercelnative",
        "//server/src/main/com/xgen/cloud/personalizationwizard",
        "//server/src/main/com/xgen/cloud/sfdc",
        "//server/src/main/com/xgen/cloud/sfsc",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/cloud/user/_private/svc",
        "//server/src/main/com/xgen/cloud/user/_private/svc/provider",
        "//server/src/main/com/xgen/cloud/user/runtime/res",
        "//server/src/main/com/xgen/svc/mms/model/grouptype",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:jakarta_ws_rs_jakarta_ws_rs_api",
        "@maven//:org_hamcrest_hamcrest",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)
