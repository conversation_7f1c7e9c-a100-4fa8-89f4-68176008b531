package com.xgen.cloud.user.runtime.res.api._private;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.authn._public.constants.ClientPrefixes;
import com.xgen.cloud.common.constants._public.model.user.UserType;
import com.xgen.cloud.common.user._public.view.AppUserView;
import com.xgen.cloud.common.user._public.view.RoleAssignmentsView;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeAccountSvc;
import com.xgen.cloud.user._private.svc.UserSvcOkta;
import com.xgen.cloud.user._private.svc.provider.UserSvcProvider;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.model.grouptype.GroupType;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@MockitoSettings(strictness = Strictness.LENIENT)
public class ApiPrivateUsersResourceUnitTests {
  private static final ObjectId USER_ID = ObjectId.get();
  private static final String USERNAME = "test-username";
  private static final String FIRST_NAME = "test-firstName";
  private static final String LAST_NAME = "test-lastName";
  private static final String PRIMARY_EMAIL = "test-email";
  private static final String USER_OKTA_ID = "user-okta-id";
  private static final String INTERNAL_CLIENT_ID = ClientPrefixes.CLIENT_ID_PREFIX + "test_client";
  private static final Set<RoleAssignment> EXPECTED_ROLES =
      Set.of(RoleAssignment.forGroup(Role.GROUP_OWNER, ObjectId.get()));

  @Mock private UserSvcOkta mockUserSvcOkta;
  @Mock private AppUser mockAppUser;
  @Mock private OrganizationSvc mockOrganizationSvc;
  @Mock private GroupSvc mockGroupSvc;
  @Mock private VercelNativeAccountSvc vercelNativeAccountSvc;

  private ApiPrivateUsersResource resource;

  @BeforeEach
  public void setup() {
    UserSvcProvider mockProvider = mock(UserSvcProvider.class);
    when(mockProvider.getUserSvcOkta()).thenReturn(mockUserSvcOkta);

    when(mockUserSvcOkta.findBySubject(USER_ID.toString())).thenReturn(Optional.of(mockAppUser));
    when(mockUserSvcOkta.findBySubject(USER_OKTA_ID)).thenReturn(Optional.of(mockAppUser));
    when(mockUserSvcOkta.findBySubject(INTERNAL_CLIENT_ID)).thenReturn(Optional.of(mockAppUser));

    when(mockUserSvcOkta.getDirectAndTeamRoleAssignments(any())).thenReturn(EXPECTED_ROLES);
    when(mockAppUser.getId()).thenReturn(USER_ID);
    when(mockAppUser.getUsername()).thenReturn(USERNAME);
    when(mockAppUser.getFirstName()).thenReturn(FIRST_NAME);
    when(mockAppUser.getLastName()).thenReturn(LAST_NAME);
    when(mockAppUser.getPrimaryEmail()).thenReturn(PRIMARY_EMAIL);

    resource =
        new ApiPrivateUsersResource(
            mock(AppSettings.class),
            mockProvider,
            mockGroupSvc,
            mockOrganizationSvc,
            vercelNativeAccountSvc);
  }

  @Test
  public void getDerivedRolesForUser_throwsNotFoundWebApplicationException_whenUserIdIsBlank() {
    // GIVEN
    String userId = "  ";

    // WHEN
    WebApplicationException exception =
        assertThrows(WebApplicationException.class, () -> resource.getDerivedRolesForUser(userId));

    // THEN
    assertThat(exception.getResponse().getStatus(), is(HttpServletResponse.SC_NOT_FOUND));
  }

  @Test
  public void
      getDerivedRolesForUser_throwsNotFoundWebApplicationException_whenUserIsNotFoundById() {
    // GIVEN
    ObjectId userId = ObjectId.get();

    // WHEN
    WebApplicationException exception =
        assertThrows(
            WebApplicationException.class,
            () -> resource.getDerivedRolesForUser(userId.toString()));

    // THEN
    assertThat(exception.getResponse().getStatus(), is(HttpServletResponse.SC_NOT_FOUND));
  }

  @Test
  public void
      getDerivedRolesForUser_throwsNotFoundWebApplicationException_whenUserIsNotFoundByInternalClientId() {
    // GIVEN
    String internalClientId = ClientPrefixes.CLIENT_ID_PREFIX + "no_match_client_id";

    // WHEN
    WebApplicationException exception =
        assertThrows(
            WebApplicationException.class, () -> resource.getDerivedRolesForUser(internalClientId));

    // THEN
    assertThat(exception.getResponse().getStatus(), is(HttpServletResponse.SC_NOT_FOUND));
  }

  @Test
  public void
      getDerivedRolesForUser_throwsNotFoundWebApplicationException_whenUserIsNotFoundByOktaId() {
    // GIVEN
    when(mockUserSvcOkta.findBySubject(any())).thenReturn(Optional.empty());
    String userId = "user-id";

    // WHEN
    WebApplicationException exception =
        assertThrows(WebApplicationException.class, () -> resource.getDerivedRolesForUser(userId));

    // THEN
    assertThat(exception.getResponse().getStatus(), is(HttpServletResponse.SC_NOT_FOUND));
  }

  @Test
  public void getDerivedRolesForUser_findsInternalClient_givenInternalClientId() {
    // GIVEN
    when(mockAppUser.getType()).thenReturn(UserType.INTERNAL_CLIENT);
    when(mockAppUser.getActorId()).thenReturn(USERNAME);

    // WHEN
    Response response = resource.getDerivedRolesForUser(INTERNAL_CLIENT_ID);

    // THEN
    verify(mockUserSvcOkta, times(1)).findBySubject(INTERNAL_CLIENT_ID);
    verifyRolesResponse(response, USERNAME);
  }

  @Test
  public void getDerivedRolesForUser_findsUserById_givenObjectId() {
    // GIVEN
    when(mockAppUser.getType()).thenReturn(UserType.LOCAL);
    when(mockAppUser.getActorId()).thenReturn(USER_ID.toString());

    // WHEN
    Response response = resource.getDerivedRolesForUser(USER_ID.toString());
    // THEN
    verify(mockUserSvcOkta).findBySubject(USER_ID.toString());

    verifyRolesResponse(response, USER_ID.toString());
  }

  @Test
  public void getAppUser_returnsAppUser() {
    // WHEN
    Response response = resource.getAppUser(mock(AppUser.class), USER_ID.toString(), false, null);

    // THEN
    verify(mockUserSvcOkta).findBySubject(USER_ID.toString());
    verifyAppUserResponse(response, EXPECTED_ROLES);
  }

  @Test
  public void getAppUser_throwsNotFoundWebApplicationException_whenUserIsNotFoundById() {
    // GIVEN
    ObjectId userId = ObjectId.get();

    // WHEN
    WebApplicationException exception =
        assertThrows(
            WebApplicationException.class,
            () -> resource.getAppUser(mock(AppUser.class), userId.toString(), false, null));

    // THEN
    assertThat(exception.getResponse().getStatus(), is(HttpServletResponse.SC_NOT_FOUND));
  }

  @Test
  public void getAppUser_throwsNotFoundWebApplicationException_whenUserIsNotFoundByOktaId() {
    // GIVEN
    when(mockUserSvcOkta.findBySubject(any())).thenReturn(Optional.empty());
    String userId = "user-id";

    // WHEN
    WebApplicationException exception =
        assertThrows(
            WebApplicationException.class,
            () -> resource.getAppUser(mock(AppUser.class), userId, false, null));

    // THEN
    assertThat(exception.getResponse().getStatus(), is(HttpServletResponse.SC_NOT_FOUND));
  }

  @Test
  public void
      getAppUser_throwsNotFoundWebApplicationException_whenUserIsNotFoundByInternalClientId() {
    // GIVEN
    String internalClientId = ClientPrefixes.CLIENT_ID_PREFIX + "no_match_client_id";

    // WHEN
    WebApplicationException exception =
        assertThrows(
            WebApplicationException.class,
            () -> resource.getAppUser(mock(AppUser.class), internalClientId, false, null));

    // THEN
    assertThat(exception.getResponse().getStatus(), is(HttpServletResponse.SC_NOT_FOUND));
  }

  @Test
  public void getAppUser_returnsAppUserWithFlattenedRoles_givenFlattenedRolesIsTrue() {
    // GIVEN
    ObjectId orgId = ObjectId.get();
    ObjectId groupId = ObjectId.get();
    Map<ObjectId, Set<Role>> groupRoles = Map.of(groupId, Set.of(Role.GROUP_CLUSTER_MANAGER));
    Map<ObjectId, Set<Role>> orgRoles = Map.of(orgId, Set.of(Role.ORG_MEMBER));
    when(mockGroupSvc.flattenGroupRoles(mockAppUser, GroupType.NDS)).thenReturn(groupRoles);
    when(mockOrganizationSvc.flattenOrgRoles(mockAppUser)).thenReturn(orgRoles);

    // WHEN
    Response response =
        resource.getAppUser(mock(AppUser.class), USER_ID.toString(), true, GroupType.NDS);

    // THEN
    verify(mockUserSvcOkta).findBySubject(USER_ID.toString());
    verifyAppUserResponse(
        response,
        Set.of(
            RoleAssignment.forGroup(Role.GROUP_CLUSTER_MANAGER, groupId),
            RoleAssignment.forOrg(Role.ORG_MEMBER, orgId)));
  }

  @Test
  public void getDerivedRolesForUser_findsUserByOkta_givenNonInternalClientString() {
    // GIVEN
    when(mockAppUser.getType()).thenReturn(UserType.LOCAL);
    when(mockAppUser.getActorId()).thenReturn(USER_ID.toHexString());

    // WHEN
    Response response = resource.getDerivedRolesForUser(USER_OKTA_ID);

    // THEN
    verify(mockUserSvcOkta).findBySubject(USER_OKTA_ID);
    verifyRolesResponse(response, USER_ID.toString());
  }

  private void verifyRolesResponse(Response response, String actorId) {
    assertThat(response.getStatus(), is(equalTo(HttpServletResponse.SC_OK)));

    RoleAssignmentsView view = (RoleAssignmentsView) response.getEntity();
    verifyRoleAssignmentsView(view, actorId, EXPECTED_ROLES);
  }

  private void verifyAppUserResponse(
      Response response, Set<RoleAssignment> expectedRoleAssignments) {
    assertThat(response.getStatus(), is(equalTo(HttpServletResponse.SC_OK)));
    AppUserView appUserView = (AppUserView) response.getEntity();

    assertThat(appUserView.username(), is(USERNAME));
    assertThat(appUserView.primaryEmail(), is(PRIMARY_EMAIL));
    assertThat(appUserView.firstName(), is(FIRST_NAME));
    assertThat(appUserView.lastName(), is(LAST_NAME));

    verifyRoleAssignments(appUserView.roleAssignments(), expectedRoleAssignments);
  }

  private void verifyRoleAssignmentsView(
      RoleAssignmentsView roleAssignmentsView,
      String actorId,
      Set<RoleAssignment> expectedRoleAssignments) {
    verifyRoleAssignments(roleAssignmentsView.roleAssignments(), expectedRoleAssignments);
    assertThat(roleAssignmentsView.actorId(), is(actorId));
  }

  private void verifyRoleAssignments(
      Set<RoleAssignment> roleAssignments, Set<RoleAssignment> expectedRoleAssignments) {
    expectedRoleAssignments.forEach(
        expectedRole -> assertTrue(roleAssignments.contains(expectedRole)));
  }
}
