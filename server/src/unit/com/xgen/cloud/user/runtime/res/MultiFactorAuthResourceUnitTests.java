package com.xgen.cloud.user.runtime.res;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.common.appsettings._public.model.AppEnv;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.email._public.svc.template.HandlebarsTemplateSvc;
import com.xgen.cloud.user._private.svc.UserSvcOkta;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.legacy2fa.MultiFactorAuthSvc;
import com.xgen.module.account.svc.AccountUserSvc;
import com.xgen.svc.mms.BaseUTest;
import com.xgen.svc.mms.form.UserLoginForm;
import com.xgen.svc.mms.svc.user.UserLoginSvc;
import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class MultiFactorAuthResourceUnitTests extends BaseUTest {
  private MultiFactorAuthSvc multiFactorAuthSvc;
  private HandlebarsTemplateSvc templateSvc;
  private UserSvcOkta userSvcOkta;
  private AppSettings appSettings;
  private AccountUserSvc accountUserSvc;
  private MultiFactorAuthResource multiFactorAuthResource;
  private UserLoginForm userLoginForm;
  private AuditInfo auditInfo;
  private AuthzSvc authzSvc;
  private UserLoginSvc userLoginSvc;

  @BeforeEach
  public void setup() throws Exception {
    multiFactorAuthSvc = mock(MultiFactorAuthSvc.class);
    templateSvc = mock(HandlebarsTemplateSvc.class);
    userSvcOkta = mock(UserSvcOkta.class);
    appSettings = mock(AppSettings.class);
    accountUserSvc = mock(AccountUserSvc.class);
    userLoginForm = mock(UserLoginForm.class);
    auditInfo = mock(AuditInfo.class);
    authzSvc = mock(AuthzSvc.class);
    userLoginSvc = mock(UserLoginSvc.class);

    doReturn(UserSvcOkta.class.getSimpleName()).when(appSettings).getStrProp("mms.userSvcClass");
    doReturn(AppEnv.TEST).when(appSettings).getAppEnv();

    multiFactorAuthResource =
        spy(
            new MultiFactorAuthResource(
                multiFactorAuthSvc,
                templateSvc,
                userSvcOkta,
                appSettings,
                accountUserSvc,
                authzSvc,
                userLoginSvc));
  }

  @Test
  public void auth_whenNeedsMFAVerificationIsTrue_callsConvertToSocialUser() throws Exception {
    HttpServletRequest servletRequest = mock(HttpServletRequest.class);
    AppUser appUser = new AppUser();
    String username = "username";
    appUser.setUsername(username);
    appUser.setOktaUserId("oktaUserId");
    Map<String, Object> clientState = new HashMap<>();
    clientState.put("needsMFAVerification", "true");

    doReturn(clientState).when(userLoginForm).getClientState();
    doNothing().when(userSvcOkta).verifyMultiFactorAuth(appUser, "999999", "", "", "", auditInfo);

    multiFactorAuthResource.auth(appUser, servletRequest, auditInfo, userLoginForm);

    verify(accountUserSvc).convertUserToSocial(appUser.getOktaUserId());
    verify(multiFactorAuthSvc).clearMultiFactorAuth(appUser);
  }

  @Test
  public void auth_whenNeedsMFAVerificationIsFalse_doesNotCallConvertToSocialUser()
      throws Exception {
    HttpServletRequest servletRequest = mock(HttpServletRequest.class);
    AppUser appUser = new AppUser();
    String username = "username";
    appUser.setUsername(username);
    Map<String, Object> clientState = new HashMap<>();
    clientState.put("needsMFAVerification", "false");

    doReturn(clientState).when(userLoginForm).getClientState();
    doNothing().when(userSvcOkta).verifyMultiFactorAuth(appUser, "999999", "", "", "", auditInfo);

    multiFactorAuthResource.auth(appUser, servletRequest, auditInfo, userLoginForm);

    verify(accountUserSvc, never()).convertUserToSocial(username);
    verify(multiFactorAuthSvc, never()).clearMultiFactorAuth(appUser);
  }
}
