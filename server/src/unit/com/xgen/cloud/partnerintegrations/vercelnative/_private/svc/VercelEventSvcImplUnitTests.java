package com.xgen.cloud.partnerintegrations.vercelnative._private.svc;

import static com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelEventJobHandler.ACCESS_TOKEN_FIELD;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelEventJobHandler.INSTALLATION_ID_FIELD;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelEventJobHandler.VERCEL_RESOURCE_FIELD;
import static com.xgen.cloud.partnerintegrations.vercelnative.utils.TestUtils.PROJECT_ID;
import static com.xgen.cloud.partnerintegrations.vercelnative.utils.TestUtils.generateAtlasResources;
import static com.xgen.cloud.partnerintegrations.vercelnative.utils.TestUtils.generateNewInstallation;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.svc.JobsProcessorSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.module._public.util.CloudProviderRegistryUtil;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.partnerintegrations.vercelnative._public.config.VercelNativeConfig;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.AtlasResources;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.InstalledProduct;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeInstallation;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeInstallationsDaoSvc;
import com.xgen.cloud.partnerintegrations.vercelnative.utils.TestUtils;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.UserMetadata;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceStatus;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class VercelEventSvcImplUnitTests {

  @InjectMocks @Spy private VercelEventSvcImpl vercelEventSvc;

  @Mock private VercelNativeInstallationsDaoSvc vercelNativeInstallationsDaoSvc;
  @Mock private VercelNativeConfig vercelNativeConfig;
  @Mock private JobsProcessorSvc jobsProcessorSvc;

  private ClusterDescription clusterDescription;
  private VercelNativeInstallation installation;
  private InstalledProduct installedProduct;
  private String clusterName;
  private NDSAudit.Type auditType;

  @BeforeEach
  public void setUp() {
    CloudProviderRegistryUtil.registerAllProvider();

    clusterName = "Cluster0";

    // Create test M10 cluster description using NDSModelTestFactory
    clusterDescription =
        NDSModelTestFactory.getClusterDescription(PROJECT_ID, clusterName, CloudProvider.AWS)
            .copy()
            .setState(ClusterDescription.State.IDLE)
            .setIsPaused(false)
            .build();

    // Create test installation using TestUtils
    final AtlasResources mockResources =
        generateAtlasResources(UserMetadata.ClusterTier.M10, 1).build();
    final VercelNativeInstallation.Builder installationBuilder = generateNewInstallation();
    installation = installationBuilder.atlasResources(mockResources).build();
    installedProduct = mockResources.installedProducts().get(0);
    auditType = NDSAudit.Type.CLUSTER_READY;
  }

  @Test
  void testSendProviderUpdate_happyPath() {
    // Arrange
    setupSuccessfulFlow();

    // Act
    vercelEventSvc.sendProviderUpdate(clusterDescription, auditType);

    // Assert
    verifySuccessfulFlow();
    verifyUpdatedInstalledProduct(VercelResourceStatus.READY); // IDLE maps to READY
    verifyJobSubmission();
  }

  @Test
  void testSendProviderUpdate_clusterTierChange() {
    // Arrange
    final AtlasResources mockFreeResources =
        generateAtlasResources(UserMetadata.ClusterTier.FREE, 1).build();
    final VercelNativeInstallation.Builder installationBuilder = generateNewInstallation();
    VercelNativeInstallation freeInstallation =
        installationBuilder.atlasResources(mockFreeResources).build();
    InstalledProduct freeResource = mockFreeResources.installedProducts().get(0);

    when(vercelNativeInstallationsDaoSvc.findInstallation(PROJECT_ID))
        .thenReturn(Optional.of(freeInstallation));
    when(vercelNativeInstallationsDaoSvc.findExistingProjectById(freeInstallation, PROJECT_ID))
        .thenReturn(Optional.of(freeResource));

    // Act
    vercelEventSvc.sendProviderUpdate(clusterDescription, auditType);

    // Assert
    verifyUpdatedInstalledProduct(
        VercelResourceStatus.READY, UserMetadata.ClusterTier.M10, freeResource);
    verifyJobSubmission(freeInstallation);
    verify(vercelEventSvc, times(1)).incrementSuccessCounter(any());
  }

  @Test
  void testSendProviderUpdate_withIncompleteInstalledProduct() {
    // Arrange
    final AtlasResources mockIncompleteResources =
        AtlasResources.builder()
            .installedProducts(
                List.of(
                    InstalledProduct.builder()
                        .userMetadata(
                            new UserMetadata(
                                UserMetadata.ClusterTier.M10, UserMetadata.VercelRegion.IAD1))
                        .projectId(PROJECT_ID)
                        .initialProjectName("test-project")
                        .build()))
            .build();
    final VercelNativeInstallation.Builder installationBuilder = generateNewInstallation();
    final VercelNativeInstallation installation =
        installationBuilder.atlasResources(mockIncompleteResources).build();
    final InstalledProduct incompleteResource = mockIncompleteResources.installedProducts().get(0);
    final InstalledProduct expectedCompleteResource =
        incompleteResource.toBuilder()
            .clusterName(clusterName)
            // these 2 values aren't going to match the actual cluster because these values are
            // generated on-the-spot, but just including it here for realism
            .createdAt(Instant.now())
            .vercelResourceId(new ObjectId())
            .build();

    when(vercelNativeInstallationsDaoSvc.findInstallation(PROJECT_ID))
        .thenReturn(Optional.of(installation));
    when(vercelNativeInstallationsDaoSvc.findExistingProjectById(installation, PROJECT_ID))
        .thenReturn(Optional.of(incompleteResource));

    // Act
    vercelEventSvc.sendProviderUpdate(clusterDescription, auditType);

    // Assert
    verifyUpdatedInstalledProduct(
        VercelResourceStatus.READY, UserMetadata.ClusterTier.M10, expectedCompleteResource);
    verifyJobSubmission(installation);
    verify(vercelEventSvc, times(1)).incrementSuccessCounter(any());
  }

  @Test
  void testSendProviderUpdate_isLoadTesting() {
    // Arrange
    when(vercelNativeInstallationsDaoSvc.findInstallation(PROJECT_ID))
        .thenReturn(Optional.of(installation));
    when(vercelNativeInstallationsDaoSvc.findExistingProjectById(installation, PROJECT_ID))
        .thenReturn(Optional.of(installedProduct));
    when(vercelNativeConfig.isLoadTestingScenario()).thenReturn(true);

    // Act
    vercelEventSvc.sendProviderUpdate(clusterDescription, auditType);

    // Assert
    verifyUpdatedInstalledProduct(VercelResourceStatus.READY);
    verify(jobsProcessorSvc, never()).submitJob(any(Job.class)); // confirm we do NOT submit a job
    verify(vercelEventSvc, times(1)).incrementSuccessCounter(any());
  }

  @Test
  void testSendProviderUpdate_usesE2ETestingProductId() {
    // Arrange
    final String e2eTestingUrl =
        "https://vercel.com/mongodb-team/~/integrations/products/mongodb-native-e2e-dev/icfg_something";
    final String e2eProductId = "iap_e2e_test_product_id";

    // Create installation with E2E testing URL
    final VercelNativeInstallation e2eInstallation =
        installation.toBuilder()
            .vercelData(
                installation.vercelData().toBuilder().installationUrl(e2eTestingUrl).build())
            .build();

    when(vercelNativeInstallationsDaoSvc.findInstallation(PROJECT_ID))
        .thenReturn(Optional.of(e2eInstallation));
    when(vercelNativeInstallationsDaoSvc.findExistingProjectById(e2eInstallation, PROJECT_ID))
        .thenReturn(Optional.of(installedProduct));
    when(vercelNativeConfig.getVercelProductIdForE2ETesting()).thenReturn(e2eProductId);
    when(jobsProcessorSvc.submitJob(any(Job.class))).thenReturn(new ObjectId());

    // Act
    vercelEventSvc.sendProviderUpdate(clusterDescription, auditType);

    // Assert
    verify(vercelNativeConfig, times(2))
        .getVercelProductIdForE2ETesting(); // Called twice: once for logging, once for return
    verify(vercelNativeConfig, never()).getVercelProductId();

    // Verify the job was submitted with the E2E product ID
    ArgumentCaptor<Job> jobCaptor = ArgumentCaptor.forClass(Job.class);
    verify(jobsProcessorSvc).submitJob(jobCaptor.capture());

    Job submittedJob = jobCaptor.getValue();
    BasicDBObject jobParams = submittedJob.getParameters();
    BasicDBObject vercelResourceData = (BasicDBObject) jobParams.get(VERCEL_RESOURCE_FIELD);
    assertEquals(e2eProductId, vercelResourceData.getString("productId"));

    verify(vercelEventSvc, times(1)).incrementSuccessCounter(any());
  }

  @Test
  void testSendProviderUpdate_installationNotFound() {
    // Arrange
    when(vercelNativeInstallationsDaoSvc.findInstallation(PROJECT_ID)).thenReturn(Optional.empty());

    // Act
    vercelEventSvc.sendProviderUpdate(clusterDescription, auditType);

    // Assert
    verify(vercelNativeInstallationsDaoSvc).findInstallation(PROJECT_ID);
    verify(vercelNativeInstallationsDaoSvc, never())
        .findExistingProjectById(any(VercelNativeInstallation.class), any(ObjectId.class));
    verifyEarlyReturn("installation_not_found");
  }

  @Test
  void testSendProviderUpdate_noAtlasResources() {
    // Arrange
    VercelNativeInstallation installationWithNullResources =
        installation.toBuilder().atlasResources(null).build();

    when(vercelNativeInstallationsDaoSvc.findInstallation(PROJECT_ID))
        .thenReturn(Optional.of(installationWithNullResources));

    // Act
    vercelEventSvc.sendProviderUpdate(clusterDescription, auditType);

    // Assert
    verify(vercelNativeInstallationsDaoSvc).findInstallation(PROJECT_ID);
    verify(vercelNativeInstallationsDaoSvc, never())
        .findExistingProjectById(any(VercelNativeInstallation.class), any(ObjectId.class));
    verifyEarlyReturn("no_atlas_resources");
  }

  @Test
  void testSendProviderUpdate_clusterNotFoundInInstallation() {
    // Arrange
    when(vercelNativeInstallationsDaoSvc.findInstallation(PROJECT_ID))
        .thenReturn(Optional.of(installation));
    when(vercelNativeInstallationsDaoSvc.findExistingProjectById(installation, PROJECT_ID))
        .thenReturn(Optional.empty());

    // Act
    vercelEventSvc.sendProviderUpdate(clusterDescription, auditType);

    // Assert
    verify(vercelNativeInstallationsDaoSvc).findInstallation(PROJECT_ID);
    verify(vercelNativeInstallationsDaoSvc).findExistingProjectById(installation, PROJECT_ID);
    verifyEarlyReturn("cluster_not_found");
  }

  @Test
  void testSendProviderUpdate_serviceThrowsException() {
    // Arrange
    when(vercelNativeInstallationsDaoSvc.findInstallation(PROJECT_ID))
        .thenThrow(new RuntimeException("Database error"));

    // Act & Assert
    RuntimeException exception =
        assertThrows(
            RuntimeException.class,
            () -> vercelEventSvc.sendProviderUpdate(clusterDescription, auditType));

    assertEquals("Failed to send provider update", exception.getMessage());
    verify(vercelNativeInstallationsDaoSvc).findInstallation(PROJECT_ID);
    verify(jobsProcessorSvc, never()).submitJob(any(Job.class));
  }

  @ParameterizedTest(name = "testSendProviderUpdate_clusterStateMapping_{0}_mapsTo_{4}")
  @MethodSource("clusterStateToVercelStatusProvider")
  void testSendProviderUpdate_clusterStateMapping(
      ClusterDescription.State state,
      boolean isPaused,
      boolean isDeleteRequested,
      String[] mongodbUriHosts,
      VercelResourceStatus expectedStatus) {
    // Arrange
    ClusterDescription testCluster =
        createClusterWithSpecifiedParams(state, isPaused, isDeleteRequested, mongodbUriHosts);
    setupSuccessfulFlow();

    // Act
    vercelEventSvc.sendProviderUpdate(testCluster, auditType);

    // Assert
    verifySuccessfulFlow();
    verifyUpdatedInstalledProduct(expectedStatus);
    verifyJobSubmission();
  }

  @Test
  void testMultipleClusters_findsCorrectCluster() {
    // Arrange - create installation with multiple clusters using TestUtils
    AtlasResources multipleClusterResources =
        TestUtils.generateAtlasResources(UserMetadata.ClusterTier.FREE, 2).build();

    VercelNativeInstallation installationWithMultipleClusters =
        installation.toBuilder().atlasResources(multipleClusterResources).build();

    // Store the original clusters for comparison
    List<InstalledProduct> originalClusters = multipleClusterResources.installedProducts();
    InstalledProduct targetCluster = originalClusters.get(0); // First cluster is our target
    InstalledProduct otherCluster =
        originalClusters.get(1); // Second cluster should remain unchanged

    when(vercelNativeInstallationsDaoSvc.findInstallation(PROJECT_ID))
        .thenReturn(Optional.of(installationWithMultipleClusters));
    when(vercelNativeInstallationsDaoSvc.findExistingProjectById(
            installationWithMultipleClusters, PROJECT_ID))
        .thenReturn(Optional.of(targetCluster));
    when(jobsProcessorSvc.submitJob(any(Job.class))).thenReturn(new ObjectId());

    // Act
    vercelEventSvc.sendProviderUpdate(clusterDescription, auditType);

    // Assert - verify the cluster was updated
    InstalledProduct updatedTargetCluster = captureUpdatedInstalledProduct();

    assertEquals(VercelResourceStatus.READY, updatedTargetCluster.status());
    assertNotNull(updatedTargetCluster.updatedAt());

    verifyJobSubmission();
  }

  @Test
  void testSendProviderUpdate_invalidUninstalledState_skipsUpdate() {
    // Arrange - create a cluster with uninstalledAt set but trying to update to non-UNINSTALLED
    // status
    Instant uninstalledTime = Instant.now().minusSeconds(3600); // 1 hour ago
    InstalledProduct clusterWithUninstalledAt =
        installedProduct.toBuilder().uninstalledAt(uninstalledTime).build();

    VercelNativeInstallation installationWithUninstalledCluster =
        installation.toBuilder()
            .atlasResources(
                installation.atlasResources().toBuilder()
                    .installedProducts(List.of(clusterWithUninstalledAt))
                    .build())
            .build();

    when(vercelNativeInstallationsDaoSvc.findInstallation(PROJECT_ID))
        .thenReturn(Optional.of(installationWithUninstalledCluster));
    when(vercelNativeInstallationsDaoSvc.findExistingProjectById(
            installationWithUninstalledCluster, PROJECT_ID))
        .thenReturn(Optional.of(clusterWithUninstalledAt));

    // Act - try to update to READY (not UNINSTALLED) when uninstalledAt is set
    vercelEventSvc.sendProviderUpdate(clusterDescription, auditType); // IDLE state maps to READY

    // Assert - update should be skipped, no job should be submitted
    verify(vercelNativeInstallationsDaoSvc).findInstallation(PROJECT_ID);
    verify(vercelNativeInstallationsDaoSvc)
        .findExistingProjectById(installationWithUninstalledCluster, PROJECT_ID);
    verifyEarlyReturn("invalid_uninstalled_state");
  }

  // Helper methods for common test setup and assertions
  private void setupSuccessfulFlow() {
    when(vercelNativeInstallationsDaoSvc.findInstallation(PROJECT_ID))
        .thenReturn(Optional.of(installation));
    when(vercelNativeInstallationsDaoSvc.findExistingProjectById(installation, PROJECT_ID))
        .thenReturn(Optional.of(installedProduct));
    when(vercelNativeConfig.getVercelProductId()).thenReturn("vercel_product_id");
    when(jobsProcessorSvc.submitJob(any(Job.class))).thenReturn(new ObjectId());
  }

  private void verifySuccessfulFlow() {
    verify(vercelNativeInstallationsDaoSvc).findInstallation(PROJECT_ID);
    verify(vercelNativeInstallationsDaoSvc).findExistingProjectById(installation, PROJECT_ID);
    verify(vercelNativeInstallationsDaoSvc)
        .updateInstalledProductByProjectId(eq(PROJECT_ID), any(InstalledProduct.class));
    verify(vercelNativeConfig).getVercelProductId();
    verify(vercelNativeConfig, never()).getVercelProductIdForE2ETesting();
    verify(jobsProcessorSvc).submitJob(any(Job.class));
    verify(vercelEventSvc, times(1)).incrementSuccessCounter(any());
  }

  private void verifyUpdatedInstalledProduct(VercelResourceStatus expectedStatus) {
    verifyUpdatedInstalledProduct(expectedStatus, null, null);
  }

  private void verifyUpdatedInstalledProduct(
      VercelResourceStatus expectedStatus,
      UserMetadata.ClusterTier expectedClusterTier,
      InstalledProduct expectedInstalledProduct) {
    InstalledProduct updatedCluster = captureUpdatedInstalledProduct();
    assertNotNull(updatedCluster.updatedAt());
    assertEquals(expectedStatus, updatedCluster.status());

    if (!List.of(VercelResourceStatus.READY, VercelResourceStatus.RESUMED)
        .contains(expectedStatus)) {
      assertNotNull(updatedCluster.notification());
    } else {
      assertNull(updatedCluster.notification());
    }

    assertNotNull(updatedCluster.updatedAt());
    assertNotNull(updatedCluster.vercelResourceId());
    assertNotNull(updatedCluster.createdAt());
    assertEquals(clusterName, updatedCluster.clusterName());

    InstalledProduct installedProductToValidate =
        expectedInstalledProduct != null ? expectedInstalledProduct : installedProduct;
    assertEquals(installedProductToValidate.projectId(), updatedCluster.projectId());

    if (expectedClusterTier != null) {
      assertEquals(expectedClusterTier, updatedCluster.userMetadata().clusterTier());
      assertEquals(
          installedProduct.userMetadata().vercelRegion(),
          updatedCluster.userMetadata().vercelRegion());
    } else {
      assertEquals(installedProduct.userMetadata(), updatedCluster.userMetadata());
    }

    if (updatedCluster.status() == VercelResourceStatus.UNINSTALLED) {
      assertNotNull(updatedCluster.uninstalledAt());
    } else {
      assertNull(updatedCluster.uninstalledAt());
    }
  }

  private InstalledProduct captureUpdatedInstalledProduct() {
    ArgumentCaptor<InstalledProduct> installedProductCaptor =
        ArgumentCaptor.forClass(InstalledProduct.class);
    verify(vercelNativeInstallationsDaoSvc)
        .updateInstalledProductByProjectId(eq(PROJECT_ID), installedProductCaptor.capture());
    return installedProductCaptor.getValue();
  }

  private InstalledProduct findClusterById(
      List<InstalledProduct> clusters, ObjectId vercelResourceId) {
    return clusters.stream()
        .filter(cluster -> cluster.vercelResourceId().equals(vercelResourceId))
        .findFirst()
        .orElseThrow(() -> new AssertionError("Cluster not found with ID: " + vercelResourceId));
  }

  private void verifyClusterUnchanged(InstalledProduct expected, InstalledProduct actual) {
    assertEquals(expected.status(), actual.status());
    assertEquals(expected.updatedAt(), actual.updatedAt());
    assertEquals(expected.createdAt(), actual.createdAt());
    assertEquals(expected.clusterName(), actual.clusterName());
    assertEquals(expected.projectId(), actual.projectId());
    assertEquals(expected.userMetadata(), actual.userMetadata());
    assertEquals(expected.uninstalledAt(), actual.uninstalledAt());
  }

  private void verifyJobSubmission() {
    verifyJobSubmission(null);
  }

  private void verifyJobSubmission(VercelNativeInstallation expectedInstallation) {
    ArgumentCaptor<Job> jobCaptor = ArgumentCaptor.forClass(Job.class);
    verify(jobsProcessorSvc).submitJob(jobCaptor.capture());

    Job submittedJob = jobCaptor.getValue();
    assertEquals(10, submittedJob.getRetriesRemaining()); // DEFAULT_RETRIES
    assertEquals(300000, submittedJob.getIntervalUntilRerun().toMillis()); // 5 minutes

    BasicDBObject jobParams = submittedJob.getParameters();
    assertNotNull(jobParams.get(VERCEL_RESOURCE_FIELD));
    assertNotNull(jobParams.get(ACCESS_TOKEN_FIELD));
    assertNotNull(jobParams.get(INSTALLATION_ID_FIELD));

    if (expectedInstallation != null) {
      // Verify that the access token in the job matches the encrypted token from the installation
      // The implementation passes the encrypted token directly from
      // installation.vercelData().accessToken()
      assertEquals(
          expectedInstallation.vercelData().accessToken(), jobParams.getString(ACCESS_TOKEN_FIELD));

      // Verify that the installationId in the job matches the installation ID
      assertEquals(
          expectedInstallation.vercelData().installationId(),
          jobParams.getString(INSTALLATION_ID_FIELD));
    } else {
      assertEquals(
          installation.vercelData().accessToken(), jobParams.getString(ACCESS_TOKEN_FIELD));
      assertEquals(
          installation.vercelData().installationId(), jobParams.getString(INSTALLATION_ID_FIELD));
    }
  }

  private void verifyEarlyReturn(String expectedErrorType) {
    verify(vercelNativeInstallationsDaoSvc, never())
        .updateInstalledProductByProjectId(any(ObjectId.class), any(InstalledProduct.class));
    verify(vercelNativeConfig, never()).getVercelProductId();
    verify(jobsProcessorSvc, never()).submitJob(any(Job.class));
    verify(vercelEventSvc, times(1)).incrementFailureCounter(eq(expectedErrorType), any());
  }

  private ClusterDescription createClusterWithSpecifiedParams(
      ClusterDescription.State state,
      boolean isPaused,
      boolean isDeleteRequested,
      String[] mongodbUriHosts) {
    return clusterDescription
        .copy()
        .setState(state)
        .setIsPaused(isPaused)
        .setDeleteRequested(isDeleteRequested)
        .setMongoUriHosts(mongodbUriHosts)
        .build();
  }

  // Test data providers for parameterized tests
  private static Stream<Arguments> clusterStateToVercelStatusProvider() {
    final String[] mockHosts = {"host1", "host2", "host3"};
    final String[] noHosts = {};

    return Stream.of(
        Arguments.of(
            ClusterDescription.State.IDLE, false, false, noHosts, VercelResourceStatus.READY),
        Arguments.of(
            ClusterDescription.State.WORKING, false, false, noHosts, VercelResourceStatus.READY),
        Arguments.of(
            ClusterDescription.State.WORKING,
            false,
            false,
            mockHosts,
            VercelResourceStatus.PENDING),
        Arguments.of(
            ClusterDescription.State.REPAIRING,
            false,
            false,
            mockHosts,
            VercelResourceStatus.PENDING),
        Arguments.of(
            ClusterDescription.State.DELETED,
            false,
            true,
            noHosts,
            VercelResourceStatus.UNINSTALLED),
        Arguments.of(
            ClusterDescription.State.IDLE, true, true, noHosts, VercelResourceStatus.UNINSTALLED),
        Arguments.of(
            ClusterDescription.State.IDLE, true, false, noHosts, VercelResourceStatus.SUSPENDED),
        Arguments.of(
            ClusterDescription.State.WORKING,
            true,
            false,
            noHosts,
            VercelResourceStatus.SUSPENDED));
  }
}
