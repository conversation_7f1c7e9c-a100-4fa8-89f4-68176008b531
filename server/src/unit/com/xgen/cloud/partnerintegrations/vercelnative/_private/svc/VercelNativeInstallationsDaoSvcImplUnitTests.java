package com.xgen.cloud.partnerintegrations.vercelnative._private.svc;

import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.FREE_CLUSTER_LIMIT;
import static com.xgen.cloud.partnerintegrations.vercelnative.utils.TestUtils.createTestVercelData;
import static com.xgen.cloud.partnerintegrations.vercelnative.utils.TestUtils.generateAtlasResources;
import static com.xgen.cloud.partnerintegrations.vercelnative.utils.TestUtils.generateAtlasResourcesWithProducts;
import static com.xgen.cloud.partnerintegrations.vercelnative.utils.TestUtils.generateInstalledProduct;
import static com.xgen.cloud.partnerintegrations.vercelnative.utils.TestUtils.generateNewInstallation;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.partnerintegrations.vercelnative._private.dao.VercelNativeInstallationDao;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.AtlasResources;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.InstalledProduct;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelData;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeInstallation;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.UserMetadata;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceNotification;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceNotificationLevel;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceStatus;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class VercelNativeInstallationsDaoSvcImplUnitTests {
  @InjectMocks VercelNativeInstallationsDaoSvcImpl vercelNativeInstallationsDaoSvcImpl;
  @Mock VercelNativeInstallationDao vercelNativeInstallationDao;

  final String mockInstallationId = "icfg_1111";
  final ObjectId projectId = new ObjectId();

  final String initialProjectName = "test-project";
  final String idempotencyKey = "test-key-123";
  final UserMetadata metadata =
      new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1);
  final InstalledProduct.RequestIdempotencyKeyTypes operationType =
      InstalledProduct.RequestIdempotencyKeyTypes.CREATE;
  final String mockIdempotencyKey = "some-key";

  final InstalledProduct partiallyInstalledProduct =
      InstalledProduct.builder()
          .requestIdempotencyKeys(Map.of(operationType.name(), mockIdempotencyKey))
          .userMetadata(metadata)
          .build();

  @Test
  void testFindInstallation() {
    final VercelNativeInstallation.Builder installationBuilder =
        generateNewInstallation(mockInstallationId, true);
    final VercelNativeInstallation installation = installationBuilder.build();

    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(installation));

    final Optional<VercelNativeInstallation> result =
        vercelNativeInstallationsDaoSvcImpl.findInstallation(mockInstallationId);
    final VercelNativeInstallation foundInstallation = result.orElse(null);
    assertEquals(installation, foundInstallation);
  }

  @Test
  void testFindAllActiveInstallations_Success() {
    // Given
    final VercelNativeInstallation activeInstallation1 =
        generateNewInstallation("icfg_1111", true).build();
    final VercelNativeInstallation activeInstallation2 =
        generateNewInstallation("icfg_2222", true).build();
    final VercelNativeInstallation activeInstallation3 =
        generateNewInstallation("icfg_3333", true).build();

    final Stream<VercelNativeInstallation> expectedStream =
        Stream.of(activeInstallation1, activeInstallation2, activeInstallation3);

    when(vercelNativeInstallationDao.findAllActiveInstallations()).thenReturn(expectedStream);

    // When
    final Stream<VercelNativeInstallation> result =
        vercelNativeInstallationsDaoSvcImpl.findAllActiveInstallations();

    // Then
    final List<VercelNativeInstallation> resultList = result.toList();
    assertEquals(3, resultList.size());
    assertTrue(resultList.contains(activeInstallation1));
    assertTrue(resultList.contains(activeInstallation2));
    assertTrue(resultList.contains(activeInstallation3));

    verify(vercelNativeInstallationDao, times(1)).findAllActiveInstallations();
  }

  @Test
  void testFindAllActiveInstallations_EmptyResult() {
    // Given
    final Stream<VercelNativeInstallation> emptyStream = Stream.empty();

    when(vercelNativeInstallationDao.findAllActiveInstallations()).thenReturn(emptyStream);

    // When
    final Stream<VercelNativeInstallation> result =
        vercelNativeInstallationsDaoSvcImpl.findAllActiveInstallations();

    // Then
    final List<VercelNativeInstallation> resultList = result.toList();
    assertEquals(0, resultList.size());
    assertTrue(resultList.isEmpty());

    verify(vercelNativeInstallationDao, times(1)).findAllActiveInstallations();
  }

  @Test
  void testFindAllActiveInstallations_WithRetry() {
    // Given
    final VercelNativeInstallation installation =
        generateNewInstallation("icfg_retry", true).build();
    final Stream<VercelNativeInstallation> successStream = Stream.of(installation);

    // Mock the DAO to fail once, then succeed
    when(vercelNativeInstallationDao.findAllActiveInstallations())
        .thenThrow(new RuntimeException("Database connection failed"))
        .thenReturn(successStream);

    // When
    final Stream<VercelNativeInstallation> result =
        vercelNativeInstallationsDaoSvcImpl.findAllActiveInstallations();

    // Then
    final List<VercelNativeInstallation> resultList = result.toList();
    assertEquals(1, resultList.size());
    assertEquals(installation, resultList.get(0));

    // Verify that the DAO method was called twice (initial failure + retry)
    verify(vercelNativeInstallationDao, times(2)).findAllActiveInstallations();
  }

  @Test
  void testFindAllActiveInstallations_OnlyActiveInstallations() {
    // Given - This test verifies the behavior conceptually, since the DAO filtering
    // is tested at the DAO layer. Here we just verify the service calls the DAO correctly.
    final VercelNativeInstallation activeInstallation1 =
        generateNewInstallation("icfg_active1", true).build();
    final VercelNativeInstallation activeInstallation2 =
        generateNewInstallation("icfg_active2", true).build();

    // The DAO should only return active installations (uninstalledAt == null)
    final Stream<VercelNativeInstallation> activeStream =
        Stream.of(activeInstallation1, activeInstallation2);

    when(vercelNativeInstallationDao.findAllActiveInstallations()).thenReturn(activeStream);

    // When
    final Stream<VercelNativeInstallation> result =
        vercelNativeInstallationsDaoSvcImpl.findAllActiveInstallations();

    // Then
    final List<VercelNativeInstallation> resultList = result.toList();
    assertEquals(2, resultList.size());

    // Verify all returned installations are active (uninstalledAt is null)
    resultList.forEach(
        installation -> {
          assertNull(installation.uninstalledAt());
        });

    verify(vercelNativeInstallationDao, times(1)).findAllActiveInstallations();
  }

  @Test
  void testFindExistingInstallation_success() {
    // Given
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).build();
    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(installation));

    // When
    final VercelNativeInstallation result =
        vercelNativeInstallationsDaoSvcImpl.findExistingInstallation(mockInstallationId);

    // Then
    assertEquals(installation, result);
  }

  @Test
  void testFindExistingInstallation_notFound() {
    // Given
    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.empty());

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () -> vercelNativeInstallationsDaoSvcImpl.findExistingInstallation(mockInstallationId));

    assertEquals("No installation found with id " + mockInstallationId, exception.getMessage());
  }

  @Test
  void testFindExistingInstallation_uninstalled() {
    // Given
    final VercelNativeInstallation uninstalledInstallation =
        generateNewInstallation(mockInstallationId, true).uninstalledAt(Instant.now()).build();
    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(uninstalledInstallation));

    // When & Then
    IllegalStateException exception =
        assertThrows(
            IllegalStateException.class,
            () -> vercelNativeInstallationsDaoSvcImpl.findExistingInstallation(mockInstallationId));

    assertEquals("No active installation with id " + mockInstallationId, exception.getMessage());
  }

  @Test
  void testFindExistingProjectById_success() {
    // Given
    final InstalledProduct expectedProduct =
        InstalledProduct.builder()
            .vercelResourceId(new ObjectId())
            .projectId(projectId)
            .clusterName("test-cluster")
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(expectedProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingProjectById(installation, projectId);

    // Then
    assertTrue(result.isPresent());
    assertEquals(expectedProduct, result.get());
  }

  @Test
  void testFindExistingProjectById_notFound() {
    // Given
    final ObjectId searchProjectId = new ObjectId();

    // existing installation with other projects
    final AtlasResources mockResources =
        generateAtlasResources(UserMetadata.ClusterTier.FREE, 1).build();
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(mockResources).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingProjectById(installation, searchProjectId);

    // Then
    assertFalse(result.isPresent());
  }

  @Test
  void testFindExistingProjectById_nullAtlasResourcesOrNullInstalledProducts() {
    // case 1: null AtlasResources
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(null).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingProjectById(installation, projectId);

    // Then
    assertFalse(result.isPresent());

    // case 2: null InstalledProducts
    final ObjectId projectId2 = new ObjectId();
    final AtlasResources atlasResources =
        AtlasResources.builder().organizationId(new ObjectId()).installedProducts(null).build();
    final VercelNativeInstallation installation2 =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final Optional<InstalledProduct> result2 =
        vercelNativeInstallationsDaoSvcImpl.findExistingProjectById(installation2, projectId2);

    // Then
    assertFalse(result2.isPresent());
  }

  @Test
  void testFindExistingProjectByName_success() {
    // Given
    final InstalledProduct expectedProduct =
        InstalledProduct.builder()
            .vercelResourceId(new ObjectId())
            .projectId(projectId)
            .clusterName("test-cluster")
            .initialProjectName(initialProjectName)
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(expectedProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingProjectByName(
            installation, initialProjectName);

    // Then
    assertTrue(result.isPresent());
    assertEquals(expectedProduct, result.get());
  }

  @Test
  void testFindExistingProjectByName_notFound() {
    // Given
    final String searchProjectName = "search-project";

    // existing installation with other projects
    final AtlasResources mockResources =
        generateAtlasResources(UserMetadata.ClusterTier.FREE, 1).build();
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(mockResources).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingProjectByName(
            installation, searchProjectName);

    // Then
    assertFalse(result.isPresent());
  }

  @Test
  void testFindExistingProjectByName_nullAtlasResourcesOrNullInstalledProducts() {
    // case 1: null atlasResources
    final String projectName = "test-project";
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(null).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingProjectByName(installation, projectName);

    // Then
    assertFalse(result.isPresent());

    // case 2: null installedProducts
    final String projectName2 = "test-project";
    final AtlasResources atlasResources =
        AtlasResources.builder().organizationId(new ObjectId()).installedProducts(null).build();
    final VercelNativeInstallation installation2 =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final Optional<InstalledProduct> result2 =
        vercelNativeInstallationsDaoSvcImpl.findExistingProjectByName(installation2, projectName2);

    // Then
    assertFalse(result2.isPresent());
  }

  @Test
  void testFindExistingProjectByName_nullInitialProjectName() {
    // Given
    final String searchProjectName = "looking-for-project";
    final InstalledProduct productWithNullName =
        InstalledProduct.builder()
            .vercelResourceId(new ObjectId())
            .projectId(projectId)
            .clusterName("test-cluster")
            .initialProjectName(null)
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(productWithNullName))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingProjectByName(
            installation, searchProjectName);

    // Then
    assertFalse(result.isPresent());
  }

  @Test
  void testFindExistingResource_success() {
    // Given
    final ObjectId resourceId = new ObjectId();
    final String resourceIdString = resourceId.toString();

    // Create a cluster with the specific resource ID
    final InstalledProduct activeCluster =
        InstalledProduct.builder()
            .vercelResourceId(resourceId)
            .projectId(projectId)
            .clusterName("test-cluster")
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(activeCluster))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final InstalledProduct result =
        vercelNativeInstallationsDaoSvcImpl.findExistingResource(installation, resourceIdString);

    // Then
    assertEquals(activeCluster, result);
  }

  @Test
  void testFindExistingResource_notFound() {
    // Given
    final String nonExistentResourceId = new ObjectId().toString();

    // Create installation with different resource ID
    final InstalledProduct differentCluster =
        InstalledProduct.builder()
            .vercelResourceId(new ObjectId()) // Different resource ID
            .projectId(projectId)
            .clusterName("test-cluster")
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(differentCluster))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.findExistingResource(
                    installation, nonExistentResourceId));

    assertEquals("No resource found with id " + nonExistentResourceId, exception.getMessage());
  }

  @Test
  void testFindExistingResource_uninstalled() {
    // Given
    final ObjectId resourceId = new ObjectId();
    final String resourceIdString = resourceId.toString();

    // Create an uninstalled cluster
    final InstalledProduct uninstalledCluster =
        InstalledProduct.builder()
            .vercelResourceId(resourceId)
            .projectId(projectId)
            .clusterName("test-cluster")
            .status(VercelResourceStatus.UNINSTALLED)
            .createdAt(Instant.now())
            .uninstalledAt(Instant.now()) // This makes it uninstalled
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(uninstalledCluster))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // case 1: call version of fn that throws on uninstalled resource
    IllegalStateException exception =
        assertThrows(
            IllegalStateException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.findExistingResource(
                    installation, resourceIdString));

    assertEquals("No active resource with id " + resourceIdString, exception.getMessage());

    // case 2: call version of fn that returns uninstalled resource
    final InstalledProduct result =
        vercelNativeInstallationsDaoSvcImpl.findExistingResource(
            installation, resourceIdString, true);

    // Then
    assertEquals(uninstalledCluster, result);
  }

  @Test
  void testFindExistingResource_nullAtlasResources() {
    // Given
    final String resourceId = new ObjectId().toString();
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true)
            .atlasResources(null) // No atlas resources
            .build();

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.findExistingResource(installation, resourceId));

    assertEquals("No resource found with id " + resourceId, exception.getMessage());
  }

  @Test
  void testFindExistingResource_nullClusters() {
    // Given
    final String resourceId = new ObjectId().toString();
    final AtlasResources atlasResourcesWithNullClusters =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(null) // null clusters
            .build();
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true)
            .atlasResources(atlasResourcesWithNullClusters)
            .build();

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.findExistingResource(installation, resourceId));

    assertEquals("No resource found with id " + resourceId, exception.getMessage());
  }

  @Test
  void testFindExistingResource_emptyClusters() {
    // Given
    final String resourceId = new ObjectId().toString();
    final AtlasResources atlasResourcesWithEmptyClusters =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of()) // empty clusters list
            .build();
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true)
            .atlasResources(atlasResourcesWithEmptyClusters)
            .build();

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.findExistingResource(installation, resourceId));

    assertEquals("No resource found with id " + resourceId, exception.getMessage());
  }

  @Test
  void testFindExistingResourceByIdempotencyKey_success() {
    // Given
    final InstalledProduct expectedProduct =
        InstalledProduct.builder()
            .vercelResourceId(new ObjectId())
            .projectId(projectId)
            .clusterName("test-cluster")
            .initialProjectName("test-project")
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .requestIdempotencyKeys(Map.of(operationType.name(), idempotencyKey))
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(expectedProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingResourceByIdempotencyKey(
            installation, idempotencyKey, operationType);

    // Then
    assertTrue(result.isPresent());
    assertEquals(expectedProduct, result.get());
  }

  @Test
  void testFindExistingResourceByIdempotencyKey_notFound_noMatchingKey() {
    // Given
    final String searchIdempotencyKey = "search-key";
    final String existingIdempotencyKey = "existing-key";

    final InstalledProduct existingProduct =
        InstalledProduct.builder()
            .vercelResourceId(new ObjectId())
            .projectId(projectId)
            .clusterName("test-cluster")
            .requestIdempotencyKeys(Map.of(operationType.name(), existingIdempotencyKey))
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(existingProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingResourceByIdempotencyKey(
            installation, searchIdempotencyKey, operationType);

    // Then
    assertFalse(result.isPresent());
  }

  @Test
  void testFindExistingResourceByIdempotencyKey_nullAtlasResources() {
    // Given
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(null).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingResourceByIdempotencyKey(
            installation, idempotencyKey, operationType);

    // Then
    assertFalse(result.isPresent());
  }

  @Test
  void testFindExistingResourceByIdempotencyKey_nullOrEmptyInstalledProducts() {
    // case 1 : null installed products
    final AtlasResources atlasResources =
        AtlasResources.builder().organizationId(new ObjectId()).installedProducts(null).build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingResourceByIdempotencyKey(
            installation, idempotencyKey, operationType);

    // Then
    assertFalse(result.isPresent());

    // case 2: empty installed products
    final AtlasResources atlasResources2 =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of())
            .build();

    final VercelNativeInstallation installation2 =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources2).build();

    // When
    final Optional<InstalledProduct> result2 =
        vercelNativeInstallationsDaoSvcImpl.findExistingResourceByIdempotencyKey(
            installation2, idempotencyKey, operationType);

    // Then
    assertFalse(result2.isPresent());
  }

  @Test
  void testFindExistingResourceByIdempotencyKey_notFound_noIdempotencyKeys() {
    // Given
    final InstalledProduct existingProduct =
        InstalledProduct.builder()
            .vercelResourceId(new ObjectId())
            .projectId(projectId)
            .clusterName("test-cluster")
            .requestIdempotencyKeys(Map.of())
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(existingProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final Optional<InstalledProduct> result =
        vercelNativeInstallationsDaoSvcImpl.findExistingResourceByIdempotencyKey(
            installation, idempotencyKey, operationType);

    // Then
    assertFalse(result.isPresent());
  }

  @Test
  void testFindActiveInstalledProductsInInstallation_mixedActiveAndInactive() {
    // Given
    final ObjectId activeProjectId1 = new ObjectId();
    final ObjectId activeProjectId2 = new ObjectId();
    final ObjectId inactiveProjectId = new ObjectId();
    final ObjectId activeResourceId1 = new ObjectId();
    final ObjectId activeResourceId2 = new ObjectId();
    final ObjectId inactiveResourceId = new ObjectId();

    // Create active products (uninstalledAt is null)
    final InstalledProduct activeProduct1 =
        InstalledProduct.builder()
            .projectId(activeProjectId1)
            .vercelResourceId(activeResourceId1)
            .clusterName("active-cluster-1")
            .userMetadata(metadata)
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .build();

    final InstalledProduct activeProduct2 =
        InstalledProduct.builder()
            .projectId(activeProjectId2)
            .vercelResourceId(activeResourceId2)
            .clusterName("active-cluster-2")
            .userMetadata(metadata)
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .build();

    // Create inactive product (uninstalledAt is set)
    final InstalledProduct inactiveProduct =
        InstalledProduct.builder()
            .projectId(inactiveProjectId)
            .vercelResourceId(inactiveResourceId)
            .clusterName("inactive-cluster")
            .userMetadata(metadata)
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .uninstalledAt(Instant.now()) // This makes it inactive
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(activeProduct1, inactiveProduct, activeProduct2))
            .build();

    final VercelNativeInstallation testInstallation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final List<InstalledProduct> activeProducts =
        vercelNativeInstallationsDaoSvcImpl.findActiveInstalledProductsInInstallation(
            testInstallation);

    // Then
    assertEquals(2, activeProducts.size());
    assertTrue(activeProducts.contains(activeProduct1));
    assertTrue(activeProducts.contains(activeProduct2));
    assertFalse(activeProducts.contains(inactiveProduct));

    // Verify all returned products have null uninstalledAt
    activeProducts.forEach(product -> assertNull(product.uninstalledAt()));
  }

  @Test
  void testFindActiveInstalledProductsInInstallation_noProducts() {
    // Given
    final AtlasResources emptyAtlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of())
            .build();

    final VercelNativeInstallation emptyInstallation =
        generateNewInstallation(mockInstallationId, true)
            .atlasResources(emptyAtlasResources)
            .build();

    // When
    final List<InstalledProduct> activeProducts =
        vercelNativeInstallationsDaoSvcImpl.findActiveInstalledProductsInInstallation(
            emptyInstallation);

    // Then
    assertEquals(0, activeProducts.size());
    assertTrue(activeProducts.isEmpty());
  }

  @Test
  void testFindActiveInstalledProductsInInstallation_nullAtlasResources() {
    // Given
    final VercelNativeInstallation nullResourcesInstallation =
        generateNewInstallation(mockInstallationId, true).atlasResources(null).build();

    // When
    final List<InstalledProduct> activeProducts =
        vercelNativeInstallationsDaoSvcImpl.findActiveInstalledProductsInInstallation(
            nullResourcesInstallation);

    // Then
    assertEquals(0, activeProducts.size());
    assertTrue(activeProducts.isEmpty());
  }

  @Test
  void testFindActiveInstalledProductsInInstallation_allInactive() {
    // Given
    final ObjectId inactiveProjectId1 = new ObjectId();
    final ObjectId inactiveProjectId2 = new ObjectId();
    final ObjectId inactiveResourceId1 = new ObjectId();
    final ObjectId inactiveResourceId2 = new ObjectId();

    final InstalledProduct inactiveProduct1 =
        InstalledProduct.builder()
            .projectId(inactiveProjectId1)
            .vercelResourceId(inactiveResourceId1)
            .clusterName("inactive-cluster-1")
            .userMetadata(metadata)
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .uninstalledAt(Instant.now())
            .build();

    final InstalledProduct inactiveProduct2 =
        InstalledProduct.builder()
            .projectId(inactiveProjectId2)
            .vercelResourceId(inactiveResourceId2)
            .clusterName("inactive-cluster-2")
            .userMetadata(metadata)
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .uninstalledAt(Instant.now())
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(inactiveProduct1, inactiveProduct2))
            .build();

    final VercelNativeInstallation testInstallation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final List<InstalledProduct> activeProducts =
        vercelNativeInstallationsDaoSvcImpl.findActiveInstalledProductsInInstallation(
            testInstallation);

    // Then
    assertEquals(0, activeProducts.size());
    assertTrue(activeProducts.isEmpty());
  }

  @Test
  void testIsInstallationAtOrAboveResourceLimits() {
    final AtlasResources mockResources =
        generateAtlasResources(UserMetadata.ClusterTier.FREE, 2).build();
    final AtlasResources mockTooManyResources =
        generateAtlasResources(UserMetadata.ClusterTier.FREE, FREE_CLUSTER_LIMIT).build();

    final VercelNativeInstallation.Builder installationBuilder =
        generateNewInstallation(mockInstallationId, true);

    // unable to find installation
    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.empty());
    assertTrue(
        vercelNativeInstallationsDaoSvcImpl.isInstallationAtOrAboveResourceLimits(
            mockInstallationId));

    // testing with null atlasResources
    final VercelNativeInstallation installation1 = installationBuilder.build();
    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(installation1));
    assertFalse(
        vercelNativeInstallationsDaoSvcImpl.isInstallationAtOrAboveResourceLimits(
            mockInstallationId));
    assertFalse(
        vercelNativeInstallationsDaoSvcImpl.isInstallationAtOrAboveResourceLimits(installation1));

    // testing with null atlasResources.clusters()
    final VercelNativeInstallation installation2 =
        installationBuilder.atlasResources(AtlasResources.builder().build()).build();
    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(installation2));
    assertFalse(
        vercelNativeInstallationsDaoSvcImpl.isInstallationAtOrAboveResourceLimits(
            mockInstallationId));
    assertFalse(
        vercelNativeInstallationsDaoSvcImpl.isInstallationAtOrAboveResourceLimits(installation2));

    // testing with 2 free resources
    final VercelNativeInstallation installation3 =
        installationBuilder.atlasResources(mockResources).build();
    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(installation3));
    assertFalse(
        vercelNativeInstallationsDaoSvcImpl.isInstallationAtOrAboveResourceLimits(
            mockInstallationId));
    assertFalse(
        vercelNativeInstallationsDaoSvcImpl.isInstallationAtOrAboveResourceLimits(installation3));

    // testing with 10 free resources
    final VercelNativeInstallation installation4 =
        installationBuilder.atlasResources(mockTooManyResources).build();
    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(installation4));
    assertTrue(
        vercelNativeInstallationsDaoSvcImpl.isInstallationAtOrAboveResourceLimits(
            mockInstallationId));
    assertTrue(
        vercelNativeInstallationsDaoSvcImpl.isInstallationAtOrAboveResourceLimits(installation4));

    // testing with 10 free resources, but 1 of them is uninstalled
    final AtlasResources mockMixOfInstalledAndUninstalledResources =
        generateAtlasResources(UserMetadata.ClusterTier.FREE, FREE_CLUSTER_LIMIT).build();
    final InstalledProduct uninstalledCluster =
        mockMixOfInstalledAndUninstalledResources.installedProducts().get(0).toBuilder()
            .uninstalledAt(Instant.now())
            .build();
    mockMixOfInstalledAndUninstalledResources.installedProducts().set(0, uninstalledCluster);

    final VercelNativeInstallation installation5 =
        installationBuilder.atlasResources(mockMixOfInstalledAndUninstalledResources).build();
    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(installation5));
    assertFalse(
        vercelNativeInstallationsDaoSvcImpl.isInstallationAtOrAboveResourceLimits(
            mockInstallationId));
    assertFalse(
        vercelNativeInstallationsDaoSvcImpl.isInstallationAtOrAboveResourceLimits(installation5));
  }

  @Test
  void testUpsertInstallationDb() {
    // Given
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).build();

    // When
    vercelNativeInstallationsDaoSvcImpl.upsertInstallationDb(mockInstallationId, installation);

    // Then
    verify(vercelNativeInstallationDao).upsertInstallation(mockInstallationId, installation);
  }

  @Test
  void testSetOrganizationIdForInstallation() {
    // Given
    final ObjectId organizationId = new ObjectId();
    final VercelNativeInstallation updatedInstallation =
        generateNewInstallation(mockInstallationId, true)
            .atlasResources(
                AtlasResources.builder()
                    .organizationId(organizationId)
                    .installedProducts(List.of())
                    .build())
            .build();

    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(updatedInstallation));

    // When
    final VercelNativeInstallation result =
        vercelNativeInstallationsDaoSvcImpl.setOrganizationIdForInstallation(
            mockInstallationId, organizationId);

    // Then
    assertEquals(updatedInstallation, result);
    assertEquals(organizationId, result.atlasResources().organizationId());
    verify(vercelNativeInstallationDao)
        .updateInstallationOrganizationId(mockInstallationId, organizationId);
    verify(vercelNativeInstallationDao).findByInstallationId(mockInstallationId);
  }

  @Test
  void testSetOrganizationIdForInstallation_installationNotFoundAfterUpdate() {
    // Given
    final ObjectId organizationId = new ObjectId();

    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.empty());

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.setOrganizationIdForInstallation(
                    mockInstallationId, organizationId));

    assertEquals("No installation found with id " + mockInstallationId, exception.getMessage());
    verify(vercelNativeInstallationDao)
        .updateInstallationOrganizationId(mockInstallationId, organizationId);
    verify(vercelNativeInstallationDao).findByInstallationId(mockInstallationId);
  }

  @Test
  void testSetRequestIdempotencyKeyAndRequestDetails_initializeAtlasResources() {
    // Given
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(null).build();

    final VercelNativeInstallation updatedInstallation =
        generateNewInstallation(mockInstallationId, true)
            .atlasResources(
                AtlasResources.builder()
                    .organizationId(new ObjectId())
                    .installedProducts(
                        List.of(
                            InstalledProduct.builder()
                                .userMetadata(metadata)
                                .requestIdempotencyKeys(
                                    Map.of(operationType.name(), idempotencyKey))
                                .build()))
                    .build())
            .build();

    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(updatedInstallation));

    // When
    final VercelNativeInstallation result =
        vercelNativeInstallationsDaoSvcImpl.setRequestIdempotencyKeyAndRequestDetails(
            installation, initialProjectName, metadata, idempotencyKey, operationType);

    // Then
    assertEquals(updatedInstallation, result);
    verify(vercelNativeInstallationDao)
        .updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
            mockInstallationId, initialProjectName, idempotencyKey, metadata, true, true);
    verify(vercelNativeInstallationDao).findByInstallationId(mockInstallationId);
  }

  @Test
  void testSetRequestIdempotencyKeyAndRequestDetails_initializesInstalledProduct() {
    // Given
    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of())
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    final VercelNativeInstallation updatedInstallation =
        generateNewInstallation(mockInstallationId, true)
            .atlasResources(
                AtlasResources.builder()
                    .organizationId(new ObjectId())
                    .installedProducts(
                        List.of(
                            InstalledProduct.builder()
                                .userMetadata(metadata)
                                .requestIdempotencyKeys(
                                    Map.of(operationType.name(), idempotencyKey))
                                .build()))
                    .build())
            .build();

    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(updatedInstallation));

    // When
    final VercelNativeInstallation result =
        vercelNativeInstallationsDaoSvcImpl.setRequestIdempotencyKeyAndRequestDetails(
            installation, initialProjectName, metadata, idempotencyKey, operationType);

    // Then
    assertEquals(updatedInstallation, result);
    verify(vercelNativeInstallationDao)
        .updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
            mockInstallationId, initialProjectName, idempotencyKey, metadata, false, true);
    verify(vercelNativeInstallationDao).findByInstallationId(mockInstallationId);
  }

  @Test
  void testSetRequestIdempotencyKeyAndRequestDetails_useExistingInstalledProduct() {
    // Given
    final InstalledProduct existingProduct =
        InstalledProduct.builder()
            .projectId(projectId)
            .initialProjectName(initialProjectName)
            .requestIdempotencyKeys(Map.of(operationType.name(), "old-key"))
            .userMetadata(metadata)
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(existingProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    final VercelNativeInstallation updatedInstallation =
        generateNewInstallation(mockInstallationId, true)
            .atlasResources(
                AtlasResources.builder()
                    .organizationId(new ObjectId())
                    .installedProducts(
                        List.of(
                            existingProduct.toBuilder()
                                .requestIdempotencyKeys(
                                    Map.of(operationType.name(), idempotencyKey))
                                .userMetadata(metadata)
                                .build()))
                    .build())
            .build();

    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.of(updatedInstallation));

    // When
    final VercelNativeInstallation result =
        vercelNativeInstallationsDaoSvcImpl.setRequestIdempotencyKeyAndRequestDetails(
            installation, initialProjectName, metadata, idempotencyKey, operationType);

    // Then
    assertEquals(updatedInstallation, result);
    verify(vercelNativeInstallationDao)
        .updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
            mockInstallationId, initialProjectName, idempotencyKey, metadata, false, false);
    verify(vercelNativeInstallationDao).findByInstallationId(mockInstallationId);
  }

  @Test
  void testSetRequestIdempotencyKeyAndRequestDetails_existingResourceWithMatchingIdempotencyKey() {
    // Given
    final InstalledProduct existingProduct =
        InstalledProduct.builder()
            .projectId(projectId)
            .initialProjectName(initialProjectName)
            .requestIdempotencyKeys(Map.of(operationType.name(), idempotencyKey))
            .userMetadata(metadata)
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(existingProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final VercelNativeInstallation result =
        vercelNativeInstallationsDaoSvcImpl.setRequestIdempotencyKeyAndRequestDetails(
            installation, initialProjectName, metadata, idempotencyKey, operationType);

    // Then
    assertEquals(installation, result);
    // since a matching key already is set, no update should be performed
    verify(vercelNativeInstallationDao, never())
        .updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
            any(String.class),
            any(String.class),
            any(String.class),
            any(UserMetadata.class),
            any(Boolean.class),
            any(Boolean.class));
  }

  @Test
  void testSetRequestIdempotencyKeyAndRequestDetails_existingProductAlreadyCreated() {
    // Given
    final InstalledProduct existingProduct =
        InstalledProduct.builder()
            .projectId(projectId)
            .initialProjectName(initialProjectName)
            .userMetadata(metadata)
            .requestIdempotencyKeys(Map.of(operationType.name(), "other-key"))
            .clusterName("existing-cluster")
            .status(VercelResourceStatus.READY)
            .vercelResourceId(new ObjectId())
            .createdAt(Instant.now()) // Already created from a prior request
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(existingProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When & Then
    IllegalStateException exception =
        assertThrows(
            IllegalStateException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.setRequestIdempotencyKeyAndRequestDetails(
                    installation, initialProjectName, metadata, idempotencyKey, operationType));

    assertTrue(exception.getMessage().contains("already was created at"));
    assertTrue(exception.getMessage().contains("no need to update idempotency key"));
    verify(vercelNativeInstallationDao, never())
        .updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
            any(String.class),
            any(String.class),
            any(String.class),
            any(UserMetadata.class),
            any(Boolean.class),
            any(Boolean.class));
  }

  @Test
  void testSetRequestIdempotencyKeyAndRequestDetails_installationNotFoundAfterUpdate() {
    // Given
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(null).build();

    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenReturn(Optional.empty());

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.setRequestIdempotencyKeyAndRequestDetails(
                    installation, initialProjectName, metadata, idempotencyKey, operationType));

    assertEquals("No installation found with id " + installation.id(), exception.getMessage());
    verify(vercelNativeInstallationDao)
        .updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
            mockInstallationId, initialProjectName, idempotencyKey, metadata, true, true);
    verify(vercelNativeInstallationDao).findByInstallationId(mockInstallationId);
  }

  @Test
  void testUpsertVercelDataForInstallation() {
    // Given
    final String testInstallationId = "icfg_test123";
    final VercelData vercelData = createTestVercelData(testInstallationId);

    // When
    vercelNativeInstallationsDaoSvcImpl.upsertVercelDataForInstallation(
        testInstallationId, vercelData);

    // Then
    verify(vercelNativeInstallationDao)
        .upsertVercelDataForInstallation(testInstallationId, vercelData);
  }

  @Test
  void testUpsertVercelDataForInstallation_withRetry() {
    // Given
    final String testInstallationId = "icfg_retry123";
    final VercelData vercelData = createTestVercelData(testInstallationId);

    // Mock the DAO to fail once, then succeed
    doThrow(new RuntimeException("Database connection failed"))
        .doNothing()
        .when(vercelNativeInstallationDao)
        .upsertVercelDataForInstallation(testInstallationId, vercelData);

    // When
    vercelNativeInstallationsDaoSvcImpl.upsertVercelDataForInstallation(
        testInstallationId, vercelData);

    // Then
    verify(vercelNativeInstallationDao, times(2))
        .upsertVercelDataForInstallation(testInstallationId, vercelData);
  }

  @Test
  void testSetProjectIdAndNameForInstallationOnCreate() {
    // Given
    // existing installation with partially installed product
    final AtlasResources mockResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(partiallyInstalledProduct))
            .build();
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(mockResources).build();

    final VercelNativeInstallation updatedInstallation =
        generateNewInstallation(mockInstallationId, true)
            .atlasResources(
                AtlasResources.builder()
                    .organizationId(new ObjectId())
                    .installedProducts(
                        List.of(
                            partiallyInstalledProduct.toBuilder()
                                .projectId(projectId)
                                .initialProjectName(initialProjectName)
                                .build()))
                    .build())
            .build();

    when(vercelNativeInstallationDao.findByProjectId(projectId))
        .thenReturn(Optional.of(updatedInstallation));

    // When
    final VercelNativeInstallation result =
        vercelNativeInstallationsDaoSvcImpl.setProjectIdAndNameForInstallationOnCreate(
            installation, projectId, initialProjectName, mockIdempotencyKey);

    // Then
    assertEquals(updatedInstallation, result);
    verify(vercelNativeInstallationDao)
        .updateInstalledProductProjectIdAndNameByIdempotencyKey(
            mockIdempotencyKey,
            InstalledProduct.RequestIdempotencyKeyTypes.CREATE,
            projectId,
            initialProjectName);
    verify(vercelNativeInstallationDao).findByProjectId(projectId);
  }

  @Test
  void testSetProjectIdAndNameForInstallationOnCreate_projectAlreadyExists() {
    // Given
    final InstalledProduct existingProduct =
        InstalledProduct.builder()
            .vercelResourceId(new ObjectId())
            .projectId(projectId)
            .initialProjectName(initialProjectName)
            .clusterName("existing-cluster")
            .status(VercelResourceStatus.READY)
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .requestIdempotencyKeys(Map.of(operationType.name(), idempotencyKey))
            .createdAt(Instant.now())
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(existingProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When
    final VercelNativeInstallation result =
        vercelNativeInstallationsDaoSvcImpl.setProjectIdAndNameForInstallationOnCreate(
            installation, projectId, initialProjectName, mockIdempotencyKey);

    // Then - should return the same installation since project already exists
    assertEquals(installation, result);
    verify(vercelNativeInstallationDao, never())
        .updateInstalledProductProjectIdAndNameByIdempotencyKey(
            any(String.class), any(InstalledProduct.RequestIdempotencyKeyTypes.class),
            any(ObjectId.class), any(String.class));
    verify(vercelNativeInstallationDao, never()).findByProjectId(any(ObjectId.class));
  }

  @Test
  void testSetProjectIdAndNameForInstallationOnCreate_nullAtlasResourcesOrNullOrganization() {
    // case 1: null atlas resources
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(null).build();

    // When & Then
    IllegalStateException exception =
        assertThrows(
            IllegalStateException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.setProjectIdAndNameForInstallationOnCreate(
                    installation, projectId, initialProjectName, mockIdempotencyKey));

    assertEquals(
        String.format(
            "Installation %s does not have an org yet, cannot set project %s",
            installation.id(), projectId),
        exception.getMessage());

    // case 2: null organization
    final ObjectId newProjectId2 = new ObjectId();
    final AtlasResources atlasResources = AtlasResources.builder().build();
    final VercelNativeInstallation installation2 =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    // When & Then
    IllegalStateException exception2 =
        assertThrows(
            IllegalStateException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.setProjectIdAndNameForInstallationOnCreate(
                    installation2, newProjectId2, initialProjectName, mockIdempotencyKey));

    assertEquals(
        String.format(
            "Installation %s does not have an org yet, cannot set project %s",
            installation2.id(), newProjectId2),
        exception2.getMessage());
  }

  @Test
  void testSetProjectIdAndNameForInstallationOnCreate_installationNotFoundAfterUpdate() {
    // Given
    final AtlasResources mockResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(partiallyInstalledProduct))
            .build();
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(mockResources).build();

    when(vercelNativeInstallationDao.findByProjectId(projectId)).thenReturn(Optional.empty());

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.setProjectIdAndNameForInstallationOnCreate(
                    installation, projectId, initialProjectName, mockIdempotencyKey));

    assertEquals("No installation found with project id " + projectId, exception.getMessage());
    verify(vercelNativeInstallationDao)
        .updateInstalledProductProjectIdAndNameByIdempotencyKey(
            mockIdempotencyKey,
            InstalledProduct.RequestIdempotencyKeyTypes.CREATE,
            projectId,
            initialProjectName);
    verify(vercelNativeInstallationDao).findByProjectId(projectId);
  }

  // NOTE: the unhappy paths for findExistingResource are tested in that method's tests, so not
  // repeating
  // for any of the below "update" methods that use it

  @Test
  void testUpdateInstalledProductByIdempotencyKey() {
    // Given
    final String requestIdempotencyKey = "test-idempotency-key";
    final ObjectId vercelResourceId = new ObjectId();
    final InstalledProduct.RequestIdempotencyKeyTypes operationType =
        InstalledProduct.RequestIdempotencyKeyTypes.CREATE;

    final InstalledProduct installedProduct =
        InstalledProduct.builder()
            .vercelResourceId(vercelResourceId)
            .projectId(projectId)
            .initialProjectName("updated-project")
            .clusterName("updated-cluster")
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .requestIdempotencyKeys(Map.of(operationType.name(), requestIdempotencyKey))
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(installedProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    when(vercelNativeInstallationDao.findByVercelResourceId(vercelResourceId))
        .thenReturn(Optional.of(installation));

    // When
    final InstalledProduct result =
        vercelNativeInstallationsDaoSvcImpl.updateInstalledProductByIdempotencyKey(
            requestIdempotencyKey, operationType, installedProduct);

    // Then
    assertEquals(installedProduct, result);
    verify(vercelNativeInstallationDao)
        .updateInstalledProductByIdempotencyKey(
            requestIdempotencyKey, operationType, installedProduct);
    verify(vercelNativeInstallationDao).findByVercelResourceId(vercelResourceId);
  }

  @Test
  void testUpdateInstalledProductByIdempotencyKey_installationNotFoundAfterUpdate() {
    // Given
    final String requestIdempotencyKey = "test-idempotency-key";
    final ObjectId vercelResourceId = new ObjectId();
    final InstalledProduct.RequestIdempotencyKeyTypes operationType =
        InstalledProduct.RequestIdempotencyKeyTypes.CREATE;

    final InstalledProduct installedProduct =
        InstalledProduct.builder()
            .vercelResourceId(vercelResourceId)
            .projectId(projectId)
            .initialProjectName("updated-project")
            .clusterName("updated-cluster")
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .requestIdempotencyKeys(Map.of(operationType.name(), requestIdempotencyKey))
            .build();

    when(vercelNativeInstallationDao.findByVercelResourceId(vercelResourceId))
        .thenReturn(Optional.empty());

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.updateInstalledProductByIdempotencyKey(
                    requestIdempotencyKey, operationType, installedProduct));

    assertEquals("No installation found after trying to update resource", exception.getMessage());
    verify(vercelNativeInstallationDao)
        .updateInstalledProductByIdempotencyKey(
            requestIdempotencyKey, operationType, installedProduct);
    verify(vercelNativeInstallationDao).findByVercelResourceId(vercelResourceId);
  }

  @Test
  void testUpdateInstalledProductByIdempotencyKey_resourceNotFoundInInstallation() {
    // this is a strange error scenario and honestly not sure how this could actually occur, but for
    // the sake of test completion
    // Given
    final String requestIdempotencyKey = "test-idempotency-key";
    final ObjectId vercelResourceId = new ObjectId();
    final ObjectId differentResourceId = new ObjectId();
    final InstalledProduct.RequestIdempotencyKeyTypes operationType =
        InstalledProduct.RequestIdempotencyKeyTypes.CREATE;

    final InstalledProduct installedProduct =
        InstalledProduct.builder()
            .vercelResourceId(vercelResourceId)
            .projectId(projectId)
            .initialProjectName("updated-resource")
            .clusterName("updated-resource")
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .requestIdempotencyKeys(Map.of(operationType.name(), requestIdempotencyKey))
            .build();

    // Create installation with different resource ID
    final InstalledProduct differentProduct =
        InstalledProduct.builder()
            .vercelResourceId(differentResourceId)
            .projectId(projectId)
            .initialProjectName("different-resource")
            .clusterName("different-resource")
            .status(VercelResourceStatus.READY)
            .createdAt(Instant.now())
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(differentProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    when(vercelNativeInstallationDao.findByVercelResourceId(vercelResourceId))
        .thenReturn(Optional.of(installation));

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.updateInstalledProductByIdempotencyKey(
                    requestIdempotencyKey, operationType, installedProduct));

    assertEquals("No resource found with id " + vercelResourceId, exception.getMessage());
    verify(vercelNativeInstallationDao)
        .updateInstalledProductByIdempotencyKey(
            requestIdempotencyKey, operationType, installedProduct);
    verify(vercelNativeInstallationDao).findByVercelResourceId(vercelResourceId);
  }

  @Test
  void testUpdateInstalledProductByProjectId() {
    // Given
    final ObjectId vercelResourceId = new ObjectId();
    final InstalledProduct updatedProduct = generateInstalledProduct(projectId, vercelResourceId);

    final VercelNativeInstallation installation =
        generateNewInstallation()
            .atlasResources(generateAtlasResourcesWithProducts(List.of(updatedProduct)))
            .build();

    when(vercelNativeInstallationDao.findByProjectId(projectId))
        .thenReturn(Optional.of(installation));

    // When
    final InstalledProduct result =
        vercelNativeInstallationsDaoSvcImpl.updateInstalledProductByProjectId(
            projectId, updatedProduct);

    // Then
    assertEquals(updatedProduct, result);
    verify(vercelNativeInstallationDao)
        .updateInstalledProductByProjectId(projectId, updatedProduct);
    verify(vercelNativeInstallationDao).findByProjectId(projectId);
  }

  @Test
  void testUpdateInstalledProductByProjectId_installationNotFoundAfterUpdate() {
    // Given
    final ObjectId vercelResourceId = new ObjectId();
    final InstalledProduct updatedProduct = generateInstalledProduct(projectId, vercelResourceId);

    when(vercelNativeInstallationDao.findByProjectId(projectId)).thenReturn(Optional.empty());

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.updateInstalledProductByProjectId(
                    projectId, updatedProduct));

    assertEquals("No installation found after trying to update resource", exception.getMessage());
    verify(vercelNativeInstallationDao)
        .updateInstalledProductByProjectId(projectId, updatedProduct);
    verify(vercelNativeInstallationDao).findByProjectId(projectId);
  }

  @Test
  void testUpdateInstalledProductByProjectId_resourceNotFoundAfterUpdate() {
    // Given
    final ObjectId vercelResourceId = new ObjectId();
    final InstalledProduct updatedProduct = generateInstalledProduct(projectId, vercelResourceId);

    // Create installation with different project ID
    final ObjectId differentProjectId = new ObjectId();
    final InstalledProduct differentProduct =
        generateInstalledProduct(differentProjectId, new ObjectId());
    final VercelNativeInstallation installation =
        generateNewInstallation()
            .atlasResources(generateAtlasResourcesWithProducts(List.of(differentProduct)))
            .build();

    when(vercelNativeInstallationDao.findByProjectId(projectId))
        .thenReturn(Optional.of(installation));

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.updateInstalledProductByProjectId(
                    projectId, updatedProduct));

    assertEquals("No resource found after trying to update resource", exception.getMessage());
    verify(vercelNativeInstallationDao)
        .updateInstalledProductByProjectId(projectId, updatedProduct);
    verify(vercelNativeInstallationDao).findByProjectId(projectId);
  }

  @Test
  void testUpdateInstalledProductUserMetadata() {
    // Given
    final ObjectId resourceId = new ObjectId();
    final UserMetadata newUserMetadata =
        new UserMetadata(UserMetadata.ClusterTier.M10, UserMetadata.VercelRegion.SFO1);
    final VercelResourceStatus newStatus = VercelResourceStatus.READY;
    final VercelResourceNotification newNotification =
        VercelResourceNotification.builder()
            .level(VercelResourceNotificationLevel.INFO)
            .title("Test Title")
            .message("Test Message")
            .build();

    final InstalledProduct expectedProduct =
        InstalledProduct.builder()
            .vercelResourceId(resourceId)
            .projectId(projectId)
            .clusterName("test-cluster")
            .status(newStatus)
            .userMetadata(newUserMetadata)
            .notification(newNotification)
            .createdAt(Instant.now())
            .build();

    final AtlasResources atlasResources =
        AtlasResources.builder()
            .organizationId(new ObjectId())
            .installedProducts(List.of(expectedProduct))
            .build();

    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).atlasResources(atlasResources).build();

    when(vercelNativeInstallationDao.findByVercelResourceId(resourceId))
        .thenReturn(Optional.of(installation));

    // When
    final InstalledProduct result =
        vercelNativeInstallationsDaoSvcImpl.updateInstalledProductUserMetadata(
            resourceId, newUserMetadata, newStatus, newNotification);

    // Then
    assertEquals(expectedProduct, result);
    verify(vercelNativeInstallationDao)
        .updateInstalledProductUserMetadata(
            resourceId, newUserMetadata, newStatus, newNotification);
    verify(vercelNativeInstallationDao).findByVercelResourceId(resourceId);
  }

  @Test
  void testUpdateInstalledProductUserMetadata_noInstallationFoundPostUpdate() {
    // Given
    final ObjectId resourceId = new ObjectId();
    final UserMetadata newUserMetadata =
        new UserMetadata(UserMetadata.ClusterTier.M10, UserMetadata.VercelRegion.SFO1);
    final VercelResourceStatus newStatus = VercelResourceStatus.READY;
    final VercelResourceNotification newNotification =
        VercelResourceNotification.builder()
            .level(VercelResourceNotificationLevel.INFO)
            .title("Test Title")
            .message("Test Message")
            .build();

    when(vercelNativeInstallationDao.findByVercelResourceId(resourceId))
        .thenReturn(Optional.empty());

    // When & Then
    NoSuchElementException exception =
        assertThrows(
            NoSuchElementException.class,
            () ->
                vercelNativeInstallationsDaoSvcImpl.updateInstalledProductUserMetadata(
                    resourceId, newUserMetadata, newStatus, newNotification));

    assertEquals("No installation found after trying to update resource", exception.getMessage());
    verify(vercelNativeInstallationDao)
        .updateInstalledProductUserMetadata(
            resourceId, newUserMetadata, newStatus, newNotification);
    verify(vercelNativeInstallationDao).findByVercelResourceId(resourceId);
  }

  @Test
  void testSoftDeleteInstallation() {
    // When
    vercelNativeInstallationsDaoSvcImpl.softDeleteInstallation(mockInstallationId);

    // Then
    verify(vercelNativeInstallationDao).softDeleteInstallation(eq(mockInstallationId));
  }

  @Test
  void testSoftDeleteResource() {
    final ObjectId resourceId = new ObjectId();
    final String resourceIdString = resourceId.toString();

    vercelNativeInstallationsDaoSvcImpl.softDeleteResource(resourceIdString);

    verify(vercelNativeInstallationDao).softDeleteInstalledProduct(eq(resourceId));
  }

  @Test
  void testDbQuery_retriesOnInitialFailure() {
    // Given
    final VercelNativeInstallation installation =
        generateNewInstallation(mockInstallationId, true).build();

    // Mock DAO to fail on first call, succeed on second call
    when(vercelNativeInstallationDao.findByInstallationId(mockInstallationId))
        .thenThrow(new RuntimeException("Database connection failed"))
        .thenReturn(Optional.of(installation));

    // When
    final Optional<VercelNativeInstallation> result =
        vercelNativeInstallationsDaoSvcImpl.findInstallation(mockInstallationId);

    // Then
    assertEquals(installation, result.orElse(null));
    // Verify that the DAO method was called twice (initial failure + retry)
    verify(vercelNativeInstallationDao, times(2)).findByInstallationId(mockInstallationId);
  }
}
