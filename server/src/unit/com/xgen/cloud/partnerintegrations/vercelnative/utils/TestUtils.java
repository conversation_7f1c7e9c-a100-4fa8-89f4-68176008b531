package com.xgen.cloud.partnerintegrations.vercelnative.utils;

import com.xgen.cloud.common.util._public.json.JsonOptional;
import com.xgen.cloud.nds.project._public.view.NDSDBUserView;
import com.xgen.cloud.partnerintegrations.common._public.model.IntegrationType;
import com.xgen.cloud.partnerintegrations.common._public.model.PartnerIntegrationsData;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.AtlasResources;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.InstalledProduct;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelData;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeInstallation;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelContact;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.upsert.UpsertInstallationRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.upsert.UpsertInstallationRequestAccount;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.upsert.UpsertInstallationRequestAccountContact;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.upsert.UpsertInstallationRequestCredentials;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.plan.VercelBillingPlan;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.plan.VercelBillingPlanDetail;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.plan.VercelBillingPlanId;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.plan.VercelBillingPlanType;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.UserMetadata;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResource;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceStatus;
import jakarta.annotation.Nullable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;

public class TestUtils {
  // Test data constants
  public static final String TEST_INSTALLATION_ID = "icfg_9bceb8ccT32d3U417ezb5c8p";
  public static final ObjectId PROJECT_ID = new ObjectId();
  public static final String TEST_ACCESS_TOKEN = "mockAccessToken";
  static final ObjectId TEST_ID = new ObjectId();
  static final Instant TEST_INSTANT = new Date().toInstant();
  static final String TEST_TOKEN_TYPE = "Bearer";
  static final String TEST_USER_NAME = "Niku";
  static final String TEST_USER_EMAIL = "<EMAIL>";
  static final String TEST_TEAM_NAME = "Cats";
  static final String TEST_URL = "https://test.vercel.app";
  static final String TEST_TOC_KEY = "toc";
  static final String TEST_TOC_DATE = "2023-10-01T00:00Z";
  static final String FAKE_ENCRYPTION_KEY =
      "32d0d9d5a364607645559305723a0d25f03092bfdbde8a5b199914183bb828d8";

  // Billing plan test constants
  static final String TEST_BILLING_PLAN_NAME = "Test Plan";
  static final String TEST_BILLING_PLAN_DESCRIPTION = "Test billing plan description";
  static final String TEST_BILLING_PLAN_COST = "$10.00/month";
  static final String TEST_STORAGE_LABEL = "Storage";
  static final String TEST_STORAGE_VALUE = "10GB";
  static final String TEST_BANDWIDTH_LABEL = "Bandwidth";
  static final String TEST_BANDWIDTH_VALUE = "100GB";
  static final String TEST_SUPPORT_LABEL = "Premium Support";
  static final String TEST_SUPPORT_VALUE = "24/7";

  // Resource test constants
  static final String TEST_RESOURCE_WITH_BILLING_ID = "test-resource-with-billing";
  static final String TEST_RESOURCE_WITH_BILLING_NAME = "test-cluster-with-billing";
  static final String TEST_RESOURCE_NULL_BILLING_ID = "test-resource-null-billing";
  static final String TEST_RESOURCE_NULL_BILLING_NAME = "test-cluster-null-billing";

  // Database user test constants
  static final String TEST_VERCEL_DB_USERNAME = "vercel-admin-test";
  static final String TEST_VERCEL_DB_DATABASE = "admin";
  static final String TEST_VERCEL_DB_PASSWORD = "testPassword123";

  public static VercelNativeInstallation.Builder generateNewInstallation() {
    return generateNewInstallation(null, true);
  }

  public static VercelNativeInstallation.Builder generateNewInstallation(
      @Nullable String installationId, boolean includeContact) {
    final VercelContact mockVercelContact =
        VercelContact.builder().name(TEST_USER_NAME).email(TEST_USER_EMAIL).build();

    final VercelData.Builder vercelDataBuilder =
        VercelData.builder()
            .installationId(installationId != null ? installationId : TEST_INSTALLATION_ID)
            .accessToken(TEST_ACCESS_TOKEN, FAKE_ENCRYPTION_KEY)
            .teamName(TEST_TEAM_NAME)
            .installationUrl(TEST_URL)
            .acceptedPolicies(Map.of(TEST_TOC_KEY, TEST_TOC_DATE));

    if (includeContact) {
      vercelDataBuilder.contact(mockVercelContact);
    }

    final VercelNativeInstallation.Builder mockInstallation =
        VercelNativeInstallation.builder()
            .id(TEST_ID)
            .vercelData(vercelDataBuilder.build())
            .createdAt(TEST_INSTANT);

    return mockInstallation;
  }

  public static AtlasResources.Builder generateAtlasResources(
      UserMetadata.ClusterTier clusterTier, int numberOfResources) {
    final AtlasResources.Builder resourceBuilder =
        AtlasResources.builder().organizationId(new ObjectId());
    final List<InstalledProduct> clusters = new ArrayList<>();

    final UserMetadata userMetadata = new UserMetadata(clusterTier, UserMetadata.VercelRegion.IAD1);

    for (int i = 0; i < numberOfResources; i++) {
      final InstalledProduct.Builder clusterBuilder =
          InstalledProduct.builder()
              .vercelResourceId(new ObjectId())
              .clusterName("Cluster" + i)
              .initialProjectName("Cluster" + i)
              .userMetadata(userMetadata)
              .status(VercelResourceStatus.READY)
              .createdAt(new Date().toInstant());

      if (i == 0) {
        clusterBuilder.projectId(PROJECT_ID);
      } else {
        // should all be distinct projects
        clusterBuilder.projectId(new ObjectId());
      }

      clusters.add(clusterBuilder.build());
    }

    resourceBuilder.installedProducts(clusters);
    return resourceBuilder;
  }

  public static AtlasResources generateAtlasResourcesWithProducts(List<InstalledProduct> products) {
    return AtlasResources.builder()
        .organizationId(new ObjectId())
        .installedProducts(products)
        .build();
  }

  public static InstalledProduct generatePartialInstalledProduct(boolean withProjectData) {
    final InstalledProduct.Builder builder =
        InstalledProduct.builder()
            .userMetadata(
                new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
            .requestIdempotencyKeys(
                Map.of(
                    String.valueOf(InstalledProduct.RequestIdempotencyKeyTypes.CREATE),
                    "test-key"));

    if (withProjectData) {
      builder.projectId(PROJECT_ID).initialProjectName("test-project");
    }

    return builder.build();
  }

  public static InstalledProduct generateInstalledProduct(
      ObjectId projectId, ObjectId vercelResourceId) {
    return InstalledProduct.builder()
        .vercelResourceId(vercelResourceId)
        .projectId(projectId)
        .initialProjectName("test-project")
        .clusterName("test-cluster")
        .status(VercelResourceStatus.READY)
        .createdAt(Instant.now())
        .userMetadata(
            new UserMetadata(UserMetadata.ClusterTier.FREE, UserMetadata.VercelRegion.IAD1))
        .build();
  }

  public static UpsertInstallationRequest createTestRequest(boolean includeContact) {
    return createTestRequestWithCredentials(includeContact, true);
  }

  public static UpsertInstallationRequest createTestRequestWithCredentials(
      boolean includeContact, boolean includeCredentials) {
    final Map<String, String> acceptedPolicies = new HashMap<>();
    acceptedPolicies.put(TEST_TOC_KEY, TEST_TOC_DATE);

    final UpsertInstallationRequestCredentials credentials =
        includeCredentials
            ? UpsertInstallationRequestCredentials.builder()
                .accessToken(TEST_ACCESS_TOKEN)
                .tokenType(TEST_TOKEN_TYPE)
                .build()
            : null;

    final UpsertInstallationRequestAccountContact contact =
        includeContact
            ? UpsertInstallationRequestAccountContact.builder()
                .name(TEST_USER_NAME)
                .email(TEST_USER_EMAIL)
                .build()
            : null;

    final UpsertInstallationRequestAccount account =
        UpsertInstallationRequestAccount.builder()
            .name(TEST_TEAM_NAME)
            .url(TEST_URL)
            .contact(contact)
            .build();

    return UpsertInstallationRequest.builder()
        .acceptedPolicies(acceptedPolicies)
        .credentials(credentials)
        .account(account)
        .build();
  }

  public static VercelData createTestVercelData(String installationId) {
    return VercelData.builder()
        .installationId(installationId != null ? installationId : TEST_INSTALLATION_ID)
        .accessToken(TEST_ACCESS_TOKEN, FAKE_ENCRYPTION_KEY)
        .teamName(TEST_TEAM_NAME)
        .installationUrl(TEST_URL)
        .acceptedPolicies(Map.of(TEST_TOC_KEY, TEST_TOC_DATE))
        .contact(VercelContact.builder().name(TEST_USER_NAME).email(TEST_USER_EMAIL).build())
        .build();
  }

  public static VercelResource createTestVercelResource() {
    return createTestVercelResource("test-resource-id", "test-cluster", VercelResourceStatus.READY);
  }

  public static VercelResource createTestVercelResource(
      String resourceId, String name, VercelResourceStatus status) {
    return createTestVercelResource(resourceId, name, status, UserMetadata.ClusterTier.FREE);
  }

  public static VercelResource createTestVercelResource(
      String resourceId,
      String name,
      VercelResourceStatus status,
      UserMetadata.ClusterTier clusterTier) {
    final UserMetadata metadata = new UserMetadata(clusterTier, UserMetadata.VercelRegion.IAD1);

    return VercelResource.builder()
        .id(resourceId)
        .productId("atlas")
        .name(name)
        .status(status)
        .metadata(metadata)
        .build();
  }

  public static VercelResource createTestVercelResourceWithoutMetadata(
      String resourceId, String name, VercelResourceStatus status) {
    return VercelResource.builder()
        .id(resourceId)
        .productId("atlas")
        .name(name)
        .status(status)
        .metadata(null)
        .build();
  }

  public static VercelBillingPlan createTestVercelBillingPlan() {
    return VercelBillingPlan.builder()
        .id(VercelBillingPlanId.PAID)
        .type(VercelBillingPlanType.SUBSCRIPTION)
        .name(TEST_BILLING_PLAN_NAME)
        .description(TEST_BILLING_PLAN_DESCRIPTION)
        .paymentMethodRequired(true)
        .cost(TEST_BILLING_PLAN_COST)
        .details(
            List.of(
                VercelBillingPlanDetail.builder()
                    .label(TEST_STORAGE_LABEL)
                    .value(TEST_STORAGE_VALUE)
                    .build(),
                VercelBillingPlanDetail.builder()
                    .label(TEST_BANDWIDTH_LABEL)
                    .value(TEST_BANDWIDTH_VALUE)
                    .build()))
        .highlightedDetails(
            List.of(
                VercelBillingPlanDetail.builder()
                    .label(TEST_SUPPORT_LABEL)
                    .value(TEST_SUPPORT_VALUE)
                    .build()))
        .effectiveDate(java.time.OffsetDateTime.now())
        .build();
  }

  public static VercelResource createTestVercelResourceWithBillingPlan(
      VercelBillingPlan billingPlan) {
    final UserMetadata metadata =
        new UserMetadata(UserMetadata.ClusterTier.M10, UserMetadata.VercelRegion.IAD1);

    return VercelResource.builder()
        .id(TEST_RESOURCE_WITH_BILLING_ID)
        .productId("atlas")
        .name(TEST_RESOURCE_WITH_BILLING_NAME)
        .status(VercelResourceStatus.READY)
        .billingPlan(billingPlan)
        .metadata(metadata)
        .build();
  }

  public static VercelResource createTestVercelResourceWithNullBillingPlan() {
    final UserMetadata metadata =
        new UserMetadata(UserMetadata.ClusterTier.M10, UserMetadata.VercelRegion.IAD1);

    return VercelResource.builder()
        .id(TEST_RESOURCE_NULL_BILLING_ID)
        .productId("atlas")
        .name(TEST_RESOURCE_NULL_BILLING_NAME)
        .status(VercelResourceStatus.READY)
        .billingPlan(null)
        .metadata(metadata)
        .build();
  }

  /**
   * Creates a test Vercel Native database user.
   *
   * @return NDSDBUserView configured for Vercel Native integration testing
   */
  public static NDSDBUserView createTestVercelDatabaseUser() {
    final PartnerIntegrationsData vercelPartnerIntegrationsData =
        new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false);

    return NDSDBUserView.builder()
        .username(TEST_VERCEL_DB_USERNAME)
        .database(TEST_VERCEL_DB_DATABASE)
        .password(TEST_VERCEL_DB_PASSWORD)
        .description(JsonOptional.of("Vercel Native integration database user"))
        .roles(List.of()) // Empty roles list for basic user
        .scopes(List.of()) // Empty scopes list for basic user
        .isEditable(true)
        .hasUserToDNMapping(false)
        .ldapAuthType(null)
        .deleteAfterDate(JsonOptional.unset())
        .x509Type(null)
        .labels(List.of()) // Empty labels list
        .awsIAMType(null)
        .oidcAuthType(null)
        .lastCertExpiration(JsonOptional.unset())
        .deleteAfterHours(JsonOptional.unset())
        .partnerIntegrationsData(vercelPartnerIntegrationsData)
        .build();
  }

  public static VercelNativeInstallation MOCK_INSTALLATION_WITHOUT_RESOURCES =
      VercelNativeInstallation.builder()
          .vercelData(
              VercelData.builder()
                  .installationId("test-installation")
                  .accessToken("test-token", "0123456789abcdef0123456789abcdef")
                  .teamName("test-team")
                  .installationUrl("https://test.vercel.app")
                  .contact(null)
                  .acceptedPolicies(Map.of())
                  .build())
          .atlasResources(
              AtlasResources.builder()
                  .organizationId(new ObjectId())
                  .installedProducts(List.of()) // Empty list - no products installed
                  .build())
          .build();

  public static VercelNativeInstallation createInstallationWithActiveProduct(ObjectId projectId) {
    InstalledProduct product =
        InstalledProduct.builder()
            .projectId(projectId)
            .clusterName("test-cluster")
            .vercelResourceId(new ObjectId())
            .build();

    return VercelNativeInstallation.builder()
        .vercelData(
            VercelData.builder()
                .installationId("test-installation")
                .accessToken("test-token", "0123456789abcdef0123456789abcdef")
                .teamName("test-team")
                .installationUrl("https://test.vercel.app")
                .contact(null)
                .acceptedPolicies(Map.of())
                .build())
        .atlasResources(
            AtlasResources.builder()
                .organizationId(new ObjectId())
                .installedProducts(List.of(product))
                .build())
        .build();
  }

  public static NDSDBUserView createVercelNativeDbUser() {
    PartnerIntegrationsData partnerData =
        new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false);

    return NDSDBUserView.builder()
        .username("vercel-user")
        .database("test-db")
        .password("old-password")
        .partnerIntegrationsData(partnerData)
        .build();
  }
}
