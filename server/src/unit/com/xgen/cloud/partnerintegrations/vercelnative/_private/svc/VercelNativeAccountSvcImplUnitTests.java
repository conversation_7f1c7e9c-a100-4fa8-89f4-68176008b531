package com.xgen.cloud.partnerintegrations.vercelnative._private.svc;

import static com.xgen.cloud.partnerintegrations.common._public.model.VercelUserToken.USER_EMAIL_FIELD;
import static com.xgen.cloud.partnerintegrations.common._public.model.VercelUserToken.USER_ID_FIELD;
import static com.xgen.cloud.partnerintegrations.common._public.model.VercelUserToken.USER_NAME_FIELD;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.JWTParser;
import com.nimbusds.jwt.SignedJWT;
import com.nimbusds.oauth2.sdk.token.AccessToken;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.authn._public.client.PartnerIdentityClient;
import com.xgen.cloud.authn._public.client.PartnerIdentityClientProvider;
import com.xgen.cloud.authn._public.exceptions.AuthnServiceException;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.authn._public.model.PartnerIdentityType;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.partnerintegrations.common._public.model.IntegrationType;
import com.xgen.cloud.partnerintegrations.common._public.model.PartnerIntegrationsData;
import com.xgen.cloud.partnerintegrations.common._public.model.TentativePartnerAccountLink;
import com.xgen.cloud.partnerintegrations.common._public.model.VercelRole;
import com.xgen.cloud.partnerintegrations.common._public.svc.TentativePartnerAccountLinkSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.config.VercelNativeConfig;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.AtlasResources;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.InstalledProduct;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelData;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeInstallation;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.enums.VercelNativeIntegrationErrorCode;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelAuthSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeInstallationsDaoSvc;
import com.xgen.cloud.partners.vercel.sdk.client._public.exception.VercelApiRequestException;
import com.xgen.cloud.partners.vercel.sdk.client._public.svc.VercelMarketplaceApiClient;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.GetMemberInformation200Response;
import com.xgen.cloud.services.authn.proto.v1.PartnerIdentity;
import com.xgen.cloud.services.authn.proto.v1.PartnerIdentityMapping;
import com.xgen.cloud.user._private.svc.OrgAccessSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.PartnerIntegrationUserSource;
import com.xgen.cloud.user._public.model.UserRegistrationForm;
import com.xgen.cloud.user._public.svc.UserSvc;
import jakarta.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
public class VercelNativeAccountSvcImplUnitTests {
  private VercelNativeAccountSvcImpl vercelNativeAccountSvcImpl;

  @Mock private UserSvc userSvc;
  @Mock private VercelAuthSvc vercelAuth;
  @Mock private PartnerIdentityClient partnerIdentityClient;
  @Mock private PartnerIdentityClientProvider partnerIdentityClientProvider;
  @Mock private VercelNativeInstallationsDaoSvc installationsSvc;
  @Mock private VercelMarketplaceApiClient vercelMarketplaceApiClient;
  @Mock private VercelNativeConfig vercelNativeConfig;
  @Mock private OrgAccessSvc orgAccessSvc;
  @Mock private TentativePartnerAccountLinkSvc tentativePartnerAccountLinkSvc;

  @Captor private ArgumentCaptor<UserRegistrationForm> registrationFormCaptor;
  @Captor private ArgumentCaptor<List<PartnerIdentity>> partnerIdentityCaptor;
  @Captor private ArgumentCaptor<PartnerIdentityMapping> partnerIdentityMappingCaptor;
  @Captor private ArgumentCaptor<String> usernameCaptor;
  @Captor private ArgumentCaptor<Set<RoleAssignment>> roleAssignmentsCaptor;

  private static final String USER_ID = "1234";
  private static final String USER_ID_2 = "5678";
  private static final String USER_EMAIL = "<EMAIL>";
  private static final String USER_EMAIL_2 = "<EMAIL>";
  private static final String FIRST_NAME = "FirstName";
  private static final String LAST_NAME = "LastName";
  private static final String INSTALLATION_ID = "0987";
  private static final String INSTALLATION_ID_2 = "6543";
  private static final ObjectId ORG_ID = new ObjectId();
  private static final String ACCOUNT_ID = "vercel-account-123";
  private static final AppUser APP_USER = mock(AppUser.class);
  private static final AppUser APP_USER_2 = mock(AppUser.class);
  private static final ObjectId APP_USER_ID = new ObjectId();
  private static final ObjectId APP_USER_ID_2 = new ObjectId();

  @BeforeEach
  public void setUp() {
    when(partnerIdentityClientProvider.get()).thenReturn(partnerIdentityClient);
    vercelNativeAccountSvcImpl =
        Mockito.spy(
            new VercelNativeAccountSvcImpl(
                userSvc,
                vercelAuth,
                partnerIdentityClientProvider,
                installationsSvc,
                vercelMarketplaceApiClient,
                vercelNativeConfig,
                orgAccessSvc,
                tentativePartnerAccountLinkSvc));
  }

  @Test
  public void test_isUserLinkedToVercelNativeIntegration_whenUserNotFound_returnsFalse() {
    // Given
    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(null);

    // When
    boolean isUserLinkedToVercelNativeIntegration =
        vercelNativeAccountSvcImpl.isUserLinkedToVercelNativeIntegration(USER_EMAIL);

    // Then
    assertFalse(isUserLinkedToVercelNativeIntegration);
  }

  @Test
  public void test_isUserLinkedToVercelNativeIntegration_whenIntegrationIsNotSet_returnsFalse() {
    // Given
    AppUser mockUser = mock(AppUser.class);
    when(mockUser.getPartnerIntegrationsData()).thenReturn(null);

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(mockUser);

    // When
    boolean isUserLinkedToVercelNativeIntegration =
        vercelNativeAccountSvcImpl.isUserLinkedToVercelNativeIntegration(USER_EMAIL);

    // Then
    assertFalse(isUserLinkedToVercelNativeIntegration);
  }

  @Test
  public void
      test_isUserLinkedToVercelNativeIntegration_whenIntegrationIsNotVercelNative_returnsFalse() {
    // Given
    PartnerIntegrationsData mockPartnerIntegrationsData = mock(PartnerIntegrationsData.class);
    when(mockPartnerIntegrationsData.getIntegrationType()).thenReturn(IntegrationType.ANIS);

    AppUser mockUser = mock(AppUser.class);
    when(mockUser.getPartnerIntegrationsData()).thenReturn(mockPartnerIntegrationsData);

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(mockUser);

    // When
    boolean isUserLinkedToVercelNativeIntegration =
        vercelNativeAccountSvcImpl.isUserLinkedToVercelNativeIntegration(USER_EMAIL);

    // Then
    assertFalse(isUserLinkedToVercelNativeIntegration);
  }

  @Test
  public void test_isUserLinkedToVercelNativeIntegration_whenValid_returnsTrue() {
    // Given
    PartnerIntegrationsData mockPartnerIntegrationsData = mock(PartnerIntegrationsData.class);
    when(mockPartnerIntegrationsData.getIntegrationType())
        .thenReturn(IntegrationType.VERCEL_NATIVE);

    AppUser mockUser = mock(AppUser.class);
    when(mockUser.getPartnerIntegrationsData()).thenReturn(mockPartnerIntegrationsData);

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(mockUser);

    // When
    boolean isUserLinkedToVercelNativeIntegration =
        vercelNativeAccountSvcImpl.isUserLinkedToVercelNativeIntegration(USER_EMAIL);

    // Then
    assertTrue(isUserLinkedToVercelNativeIntegration);
  }

  @Test
  public void test_isUserLinkedToOtherIntegrationType_whenUserNotFound_returnsFalse() {
    // Given
    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(null);

    // When
    boolean isUserLinkedToVercelNativeIntegration =
        vercelNativeAccountSvcImpl.isUserLinkedToOtherIntegrationType(USER_EMAIL);

    // Then
    assertFalse(isUserLinkedToVercelNativeIntegration);
  }

  @Test
  public void test_isUserLinkedToOtherIntegrationType_whenIntegrationIsNotSet_returnsFalse() {
    // Given
    AppUser mockUser = mock(AppUser.class);
    when(mockUser.getPartnerIntegrationsData()).thenReturn(null);

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(mockUser);

    // When
    boolean isUserLinkedToVercelNativeIntegration =
        vercelNativeAccountSvcImpl.isUserLinkedToOtherIntegrationType(USER_EMAIL);

    // Then
    assertFalse(isUserLinkedToVercelNativeIntegration);
  }

  @Test
  public void test_isUserLinkedToOtherIntegrationType_whenIntegrationIsVercelNative_returnsFalse() {
    // Given
    PartnerIntegrationsData mockPartnerIntegrationsData = mock(PartnerIntegrationsData.class);
    when(mockPartnerIntegrationsData.getIntegrationType())
        .thenReturn(IntegrationType.VERCEL_NATIVE);

    AppUser mockUser = mock(AppUser.class);
    when(mockUser.getPartnerIntegrationsData()).thenReturn(mockPartnerIntegrationsData);

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(mockUser);

    // When
    boolean isUserLinkedToVercelNativeIntegration =
        vercelNativeAccountSvcImpl.isUserLinkedToOtherIntegrationType(USER_EMAIL);

    // Then
    assertFalse(isUserLinkedToVercelNativeIntegration);
  }

  @Test
  public void test_isUserLinkedToOtherIntegrationType_whenValid_returnsTrue() {
    // Given
    PartnerIntegrationsData mockPartnerIntegrationsData = mock(PartnerIntegrationsData.class);
    when(mockPartnerIntegrationsData.getIntegrationType()).thenReturn(IntegrationType.ANIS);

    AppUser mockUser = mock(AppUser.class);
    when(mockUser.getPartnerIntegrationsData()).thenReturn(mockPartnerIntegrationsData);

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(mockUser);

    // When
    boolean isUserLinkedToVercelNativeIntegration =
        vercelNativeAccountSvcImpl.isUserLinkedToOtherIntegrationType(USER_EMAIL);

    // Then
    assertTrue(isUserLinkedToVercelNativeIntegration);
  }

  @Test
  public void test_installVercelIntegration_whenUserDoesNotExist_callsUserSvcRegisterFromPartner()
      throws SvcException {
    // Given
    HttpServletRequest mockRequestHeader = createMockRequestHeader();

    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.empty());
    when(userSvc.registerFromPartner(
            any(), any(), eq(PartnerIntegrationUserSource.VERCEL_NATIVE_PROGRAMMATIC_USER)))
        .thenReturn(mock(AppUser.class));

    // When
    vercelNativeAccountSvcImpl.installVercelIntegration(
        mockRequestHeader, mock(AuditInfo.class), INSTALLATION_ID);

    // Then
    verify(userSvc)
        .registerFromPartner(
            registrationFormCaptor.capture(),
            any(),
            eq(PartnerIntegrationUserSource.VERCEL_NATIVE_PROGRAMMATIC_USER));

    UserRegistrationForm capturedForm = registrationFormCaptor.getValue();
    assertEquals(USER_EMAIL, capturedForm.getUsername());
    assertEquals(FIRST_NAME, capturedForm.getFirstName());
    assertEquals(LAST_NAME, capturedForm.getLastName());
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationCounter();
    verify(vercelNativeAccountSvcImpl, never()).incrementVercelNativeInstallationErrorCounter();
  }

  @Test
  public void
      test_installVercelIntegration_whenUserHasExistingOktaAccountButNoAssociatedAppUser_throwsVercelNativeUserProvisioningFailedDueToExistingOktaAccountException()
          throws SvcException {
    // Given
    HttpServletRequest mockRequestHeader = createMockRequestHeader();

    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.empty());
    when(userSvc.registerFromPartner(
            any(), any(), eq(PartnerIntegrationUserSource.VERCEL_NATIVE_PROGRAMMATIC_USER)))
        .thenThrow(new SvcException(AppUserErrorCode.DUPLICATE_USERNAME));

    // When
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                vercelNativeAccountSvcImpl.installVercelIntegration(
                    mockRequestHeader, mock(AuditInfo.class), INSTALLATION_ID));

    // Then
    assertEquals(
        VercelNativeIntegrationErrorCode
            .VERCEL_NATIVE_USER_PROVISIONING_FAILED_DUE_TO_EXISTING_OKTA_ACCOUNT,
        exception.getErrorCode());
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationCounter();
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationErrorCounter();
  }

  @Test
  public void
      test_installVercelIntegration_whenUserIsDeleted_throwsVercelNativeUserProvisioningFailedDueToDeletionPendingException() {
    // Given
    HttpServletRequest mockRequestHeader = createMockRequestHeader();

    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.of(APP_USER));
    when(APP_USER.isDeleted()).thenReturn(true);

    // When
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                vercelNativeAccountSvcImpl.installVercelIntegration(
                    mockRequestHeader, mock(AuditInfo.class), INSTALLATION_ID));

    // Then
    assertEquals(
        VercelNativeIntegrationErrorCode
            .VERCEL_NATIVE_USER_PROVISIONING_FAILED_DUE_TO_DELETION_PENDING,
        exception.getErrorCode());
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationCounter();
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationErrorCounter();
  }

  @Test
  public void
      test_installVercelIntegration_whenUserExistsAndPartnerIntegrationsDataIsNull_doesNotCallUserSvcRegisterAndSetsPartnerIntegrationsData()
          throws SvcException {
    // Given
    HttpServletRequest mockRequestHeader = createMockRequestHeader();

    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.of(APP_USER));
    when(APP_USER.getPartnerIntegrationsData()).thenReturn(null);

    // When
    vercelNativeAccountSvcImpl.installVercelIntegration(
        mockRequestHeader, mock(AuditInfo.class), INSTALLATION_ID);

    // Then
    verify(userSvc, never()).registerFromPartner(any(), any(), any());
    verify(userSvc)
        .setPartnerIntegrationsData(
            APP_USER.getId(), new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false));
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationCounter();
    verify(vercelNativeAccountSvcImpl, never()).incrementVercelNativeInstallationErrorCounter();
  }

  @Test
  public void
      test_installVercelIntegration_whenUserIsAlreadyLinkedToOtherPartnerIntegrationType_throwsServerErrorException() {
    // Given
    HttpServletRequest mockRequestHeader = createMockRequestHeader();
    PartnerIntegrationsData mockPartnerIntegrationsData = mock(PartnerIntegrationsData.class);

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(APP_USER);
    when(APP_USER.getPartnerIntegrationsData()).thenReturn(mockPartnerIntegrationsData);
    when(mockPartnerIntegrationsData.getIntegrationType()).thenReturn(IntegrationType.ANIS);

    // When
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                vercelNativeAccountSvcImpl.installVercelIntegration(
                    mockRequestHeader, mock(AuditInfo.class), INSTALLATION_ID));

    // Then
    assertEquals(CommonErrorCode.SERVER_ERROR, exception.getErrorCode());
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationCounter();
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationErrorCounter();
  }

  @Test
  public void
      test_installVercelIntegration_whenUserExistsAndPartnerIntegrationsDataIsAlreadySet_doesNotCallUserSvcRegisterAndDoesNotSetPartnerIntegrationsData()
          throws SvcException {
    // Given
    HttpServletRequest mockRequestHeader = createMockRequestHeader();

    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.of(APP_USER));
    when(APP_USER.isDeleted()).thenReturn(false);
    when(APP_USER.getPartnerIntegrationsData())
        .thenReturn(new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false));

    // When
    vercelNativeAccountSvcImpl.installVercelIntegration(
        mockRequestHeader, mock(AuditInfo.class), INSTALLATION_ID);

    // Then
    verify(userSvc, never()).registerFromPartner(any(), any(), any());
    verify(userSvc, never())
        .setPartnerIntegrationsData(
            APP_USER.getId(), new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false));
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationCounter();
    verify(vercelNativeAccountSvcImpl, never()).incrementVercelNativeInstallationErrorCounter();
  }

  @Test
  public void
      test_installVercelIntegration_whenUserDoesNotExist_callsPartnerIdentityClientCreateMapping()
          throws SvcException {
    // Given
    HttpServletRequest mockRequestHeader = createMockRequestHeader();

    AppUser newUser = mock(AppUser.class);
    when(newUser.getUsername()).thenReturn(USER_EMAIL);
    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.empty());
    when(userSvc.registerFromPartner(
            any(), any(), eq(PartnerIntegrationUserSource.VERCEL_NATIVE_PROGRAMMATIC_USER)))
        .thenReturn(newUser);

    // When
    vercelNativeAccountSvcImpl.installVercelIntegration(
        mockRequestHeader, mock(AuditInfo.class), INSTALLATION_ID);

    // Then
    verify(userSvc)
        .registerFromPartner(
            any(), any(), eq(PartnerIntegrationUserSource.VERCEL_NATIVE_PROGRAMMATIC_USER));
    verify(partnerIdentityClient).getMappingByEmail(USER_EMAIL);
    verify(partnerIdentityClient).createMapping(eq(USER_EMAIL), partnerIdentityCaptor.capture());

    List<PartnerIdentity> capturedIdentities = partnerIdentityCaptor.getValue();
    assertEquals(1, capturedIdentities.size());
    assertEquals(INSTALLATION_ID, capturedIdentities.get(0).getIntegrationId());
    assertEquals(USER_ID, capturedIdentities.get(0).getExternalId());
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationCounter();
    verify(vercelNativeAccountSvcImpl, never()).incrementVercelNativeInstallationErrorCounter();
  }

  @Test
  public void test_installVercelIntegration_whenRegistrationFails_throwsServerError()
      throws SvcException {
    // Given
    HttpServletRequest mockRequestHeader = createMockRequestHeader();

    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.empty());
    when(userSvc.registerFromPartner(
            any(), any(), eq(PartnerIntegrationUserSource.VERCEL_NATIVE_PROGRAMMATIC_USER)))
        .thenThrow(new SvcException(AppUserErrorCode.UNABLE_TO_REGISTER_USER));

    // When
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                vercelNativeAccountSvcImpl.installVercelIntegration(
                    mockRequestHeader, mock(AuditInfo.class), INSTALLATION_ID));

    // Then
    assertEquals(CommonErrorCode.SERVER_ERROR, exception.getErrorCode());
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationCounter();
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationErrorCounter();
  }

  @Test
  public void
      test_installVercelIntegration_whenUserAlreadyExists_callsPartnerIdentityClientUpdateMapping()
          throws SvcException {
    // Given
    HttpServletRequest mockRequestHeader = createMockRequestHeader();

    PartnerIdentity existingIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID_2)
            .setExternalId(USER_ID_2)
            .build();
    PartnerIdentityMapping existingMapping =
        PartnerIdentityMapping.newBuilder().addIdentities(existingIdentity).build();

    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL))
        .thenReturn(Optional.of(existingMapping));
    AppUser existingUser = mock(AppUser.class);
    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.of(existingUser));
    when(existingUser.getUsername()).thenReturn(USER_EMAIL);

    // When
    vercelNativeAccountSvcImpl.installVercelIntegration(
        mockRequestHeader, mock(AuditInfo.class), INSTALLATION_ID);

    // Then
    verify(userSvc, never()).registerFromPartner(any(), any(), any());
    verify(partnerIdentityClient).getMappingByEmail(USER_EMAIL);
    verify(partnerIdentityClient).updateMapping(partnerIdentityMappingCaptor.capture());

    List<PartnerIdentity> updatedIdentities =
        partnerIdentityMappingCaptor.getValue().getIdentitiesList();
    assertEquals(USER_EMAIL, partnerIdentityMappingCaptor.getValue().getEmail());
    assertEquals(2, updatedIdentities.size());
    assertEquals(INSTALLATION_ID_2, updatedIdentities.get(0).getIntegrationId());
    assertEquals(USER_ID_2, updatedIdentities.get(0).getExternalId());
    assertEquals(INSTALLATION_ID, updatedIdentities.get(1).getIntegrationId());
    assertEquals(USER_ID, updatedIdentities.get(1).getExternalId());
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationCounter();
    verify(vercelNativeAccountSvcImpl, never()).incrementVercelNativeInstallationErrorCounter();
  }

  @Test
  public void
      test_installVercelIntegration_whenDuplicatePartnerIdentityMappingAlreadyExists_skipsMethod()
          throws SvcException {
    // Given
    HttpServletRequest mockRequestHeader = createMockRequestHeader();

    PartnerIdentity existingIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID)
            .build();
    PartnerIdentityMapping existingMapping =
        PartnerIdentityMapping.newBuilder().addIdentities(existingIdentity).build();

    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL))
        .thenReturn(Optional.of(existingMapping));
    AppUser existingUser = mock(AppUser.class);
    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.of(existingUser));
    when(existingUser.getUsername()).thenReturn(USER_EMAIL);

    // When
    vercelNativeAccountSvcImpl.installVercelIntegration(
        mockRequestHeader, mock(AuditInfo.class), INSTALLATION_ID);

    // Then
    verify(partnerIdentityClient).getMappingByEmail(USER_EMAIL);
    verify(partnerIdentityClient, never()).updateMapping(any());
    verify(partnerIdentityClient, never()).createMapping(any(), any());
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeInstallationCounter();
    verify(vercelNativeAccountSvcImpl, never()).incrementVercelNativeInstallationErrorCounter();
  }

  // ===== Tests for getOrCreateVercelApiUser method =====

  @Test
  public void test_upsertVercelApiUser_userToken_existingUser_returnsUser() throws SvcException {
    // Given
    String token = createVercelUserToken();
    AppUser existingUser = mock(AppUser.class);
    VercelNativeInstallation installation = createMockInstallation();

    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(userSvc.upsertPartnerApiUser(anyString(), anySet(), any(AuditInfo.class)))
        .thenReturn(existingUser);

    // When
    AppUser result =
        vercelNativeAccountSvcImpl.upsertVercelApiUser(
            token, PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER, mock(AuditInfo.class));

    // Then
    assertEquals(existingUser, result);
    verify(installationsSvc).findInstallation(INSTALLATION_ID);
    verify(userSvc)
        .upsertPartnerApiUser(
            usernameCaptor.capture(), roleAssignmentsCaptor.capture(), any(AuditInfo.class));

    String expectedUsername = "email=" + USER_EMAIL + ",user=" + USER_ID + ",account=" + ACCOUNT_ID;
    assertEquals(expectedUsername, usernameCaptor.getValue());
    assertEquals(1, roleAssignmentsCaptor.getValue().size());
    RoleAssignment roleAssignment = roleAssignmentsCaptor.getValue().iterator().next();
    assertEquals(Role.ORG_READ_ONLY, roleAssignment.getRole()); // USER role maps to ORG_READ_ONLY
    assertEquals(ORG_ID, roleAssignment.getOrgId());
  }

  @Test
  public void test_upsertVercelApiUser_userToken_newUser_createsUser() throws SvcException {
    // Given
    String token = createVercelUserToken();
    AppUser newUser = mock(AppUser.class);
    VercelNativeInstallation installation = createMockInstallation();

    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(userSvc.upsertPartnerApiUser(anyString(), anySet(), any(AuditInfo.class)))
        .thenReturn(newUser);

    // When
    AppUser result =
        vercelNativeAccountSvcImpl.upsertVercelApiUser(
            token, PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER, mock(AuditInfo.class));

    // Then
    assertEquals(newUser, result);
    verify(installationsSvc).findInstallation(INSTALLATION_ID);
    verify(userSvc)
        .upsertPartnerApiUser(
            usernameCaptor.capture(), roleAssignmentsCaptor.capture(), any(AuditInfo.class));

    String expectedUsername = "email=" + USER_EMAIL + ",user=" + USER_ID + ",account=" + ACCOUNT_ID;
    assertEquals(expectedUsername, usernameCaptor.getValue());
    assertEquals(1, roleAssignmentsCaptor.getValue().size());
    RoleAssignment roleAssignment = roleAssignmentsCaptor.getValue().iterator().next();
    assertEquals(Role.ORG_READ_ONLY, roleAssignment.getRole()); // USER role maps to ORG_READ_ONLY
    assertEquals(ORG_ID, roleAssignment.getOrgId());
  }

  @Test
  public void test_upsertVercelApiUser_systemToken_existingUser_returnsUser() throws SvcException {
    // Given
    String token = createVercelSystemToken();
    AppUser existingUser = mock(AppUser.class);
    VercelNativeInstallation installation = createMockInstallation();

    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(userSvc.upsertPartnerApiUser(anyString(), anySet(), any(AuditInfo.class)))
        .thenReturn(existingUser);

    // When
    AppUser result =
        vercelNativeAccountSvcImpl.upsertVercelApiUser(
            token, PartnerIdentityType.VERCEL_NATIVE_SYSTEM_USER, mock(AuditInfo.class));

    // Then
    assertEquals(existingUser, result);
    verify(installationsSvc).findInstallation(INSTALLATION_ID);
    verify(userSvc)
        .upsertPartnerApiUser(
            usernameCaptor.capture(), roleAssignmentsCaptor.capture(), any(AuditInfo.class));

    String expectedUsername = "account=" + ACCOUNT_ID + ",installation=" + INSTALLATION_ID;
    assertEquals(expectedUsername, usernameCaptor.getValue());
    assertEquals(1, roleAssignmentsCaptor.getValue().size());
    RoleAssignment roleAssignment = roleAssignmentsCaptor.getValue().iterator().next();
    assertEquals(Role.ORG_OWNER, roleAssignment.getRole()); // System token gets ORG_OWNER
    assertEquals(ORG_ID, roleAssignment.getOrgId());
  }

  @Test
  public void test_upsertVercelApiUser_systemToken_newUser_createsUser() throws SvcException {
    // Given
    String token = createVercelSystemToken();
    AppUser newUser = mock(AppUser.class);
    VercelNativeInstallation installation = createMockInstallation();

    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(userSvc.upsertPartnerApiUser(anyString(), anySet(), any(AuditInfo.class)))
        .thenReturn(newUser);

    // When
    AppUser result =
        vercelNativeAccountSvcImpl.upsertVercelApiUser(
            token, PartnerIdentityType.VERCEL_NATIVE_SYSTEM_USER, mock(AuditInfo.class));

    // Then
    assertEquals(newUser, result);
    verify(installationsSvc).findInstallation(INSTALLATION_ID);
    verify(userSvc)
        .upsertPartnerApiUser(
            usernameCaptor.capture(), roleAssignmentsCaptor.capture(), any(AuditInfo.class));

    String expectedUsername = "account=" + ACCOUNT_ID + ",installation=" + INSTALLATION_ID;
    assertEquals(expectedUsername, usernameCaptor.getValue());
    assertEquals(1, roleAssignmentsCaptor.getValue().size());
    RoleAssignment roleAssignment = roleAssignmentsCaptor.getValue().iterator().next();
    assertEquals(Role.ORG_OWNER, roleAssignment.getRole()); // System token gets ORG_OWNER
    assertEquals(ORG_ID, roleAssignment.getOrgId());
  }

  @Test
  public void test_upsertVercelApiUser_userToken_adminRole_createsUserWithOwnerRole()
      throws SvcException {
    // Given
    String token = createVercelUserTokenWithRole(VercelRole.ADMIN);
    AppUser newUser = mock(AppUser.class);
    VercelNativeInstallation installation = createMockInstallation();

    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(userSvc.upsertPartnerApiUser(anyString(), anySet(), any(AuditInfo.class)))
        .thenReturn(newUser);

    // When
    AppUser result =
        vercelNativeAccountSvcImpl.upsertVercelApiUser(
            token, PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER, mock(AuditInfo.class));

    // Then
    assertEquals(newUser, result);
    verify(installationsSvc).findInstallation(INSTALLATION_ID);
    verify(userSvc)
        .upsertPartnerApiUser(
            usernameCaptor.capture(), roleAssignmentsCaptor.capture(), any(AuditInfo.class));

    String expectedUsername = "email=" + USER_EMAIL + ",user=" + USER_ID + ",account=" + ACCOUNT_ID;
    assertEquals(expectedUsername, usernameCaptor.getValue());
    assertEquals(1, roleAssignmentsCaptor.getValue().size());
    RoleAssignment roleAssignment = roleAssignmentsCaptor.getValue().iterator().next();
    assertEquals(Role.ORG_OWNER, roleAssignment.getRole()); // ADMIN role maps to ORG_OWNER
    assertEquals(ORG_ID, roleAssignment.getOrgId());
  }

  @Test
  public void test_upsertVercelApiUser_installationNotFound_createsUserWithEmptyRoles()
      throws SvcException {
    // Given
    String token = createVercelUserToken();
    AppUser expectedUser = mock(AppUser.class);
    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.empty());
    when(userSvc.upsertPartnerApiUser(anyString(), eq(Set.of()), any(AuditInfo.class)))
        .thenReturn(expectedUser);

    // When
    AppUser result =
        vercelNativeAccountSvcImpl.upsertVercelApiUser(
            token, PartnerIdentityType.VERCEL_NATIVE_HUMAN_USER, mock(AuditInfo.class));

    // Then
    assertNotNull(result);
    assertEquals(expectedUser, result);
    verify(installationsSvc).findInstallation(INSTALLATION_ID);
    verify(userSvc).upsertPartnerApiUser(anyString(), eq(Set.of()), any(AuditInfo.class));
  }

  @Test
  public void
      test_syncVercelRolesWithAtlasRoles_whenInstallationIsValid_callsReplaceUserRolesInOrganization()
          throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    VercelNativeInstallation installation = createMockInstallationWithVercelData();

    PartnerIdentityMapping partnerIdentityMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(
                PartnerIdentity.newBuilder()
                    .setIntegrationId(INSTALLATION_ID)
                    .setExternalId(USER_ID)
                    .build())
            .build();
    PartnerIdentityMapping partnerIdentityMapping2 =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL_2)
            .addIdentities(
                PartnerIdentity.newBuilder()
                    .setIntegrationId(INSTALLATION_ID)
                    .setExternalId(USER_ID_2)
                    .build())
            .build();

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(APP_USER);
    when(userSvc.findByUsername(USER_EMAIL_2)).thenReturn(APP_USER_2);
    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of(partnerIdentityMapping, partnerIdentityMapping2));
    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class)))
        .thenReturn(Mono.just(new GetMemberInformation200Response(USER_ID, VercelRole.ADMIN)));
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID_2), any(AccessToken.class)))
        .thenReturn(Mono.just(new GetMemberInformation200Response(USER_ID_2, VercelRole.USER)));

    // When
    vercelNativeAccountSvcImpl.syncVercelRolesWithAtlasRoles(
        INSTALLATION_ID, auditInfo, Optional.empty());

    // Then
    verify(vercelMarketplaceApiClient)
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class));
    verify(vercelMarketplaceApiClient)
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID_2), any(AccessToken.class));
    verify(orgAccessSvc)
        .replaceUserRolesInOrganization(
            eq(APP_USER), eq(ORG_ID), eq(Set.of(Role.ORG_OWNER)), eq(auditInfo));
    verify(orgAccessSvc)
        .replaceUserRolesInOrganization(
            eq(APP_USER_2), eq(ORG_ID), eq(Set.of(Role.ORG_READ_ONLY)), eq(auditInfo));
  }

  @Test
  public void
      test_syncVercelRolesWithAtlasRoles_whenUsernameIsProvided_onlyUpdatesRolesForSpecifiedUser()
          throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    VercelNativeInstallation installation = createMockInstallationWithVercelData();

    PartnerIdentityMapping partnerIdentityMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(
                PartnerIdentity.newBuilder()
                    .setIntegrationId(INSTALLATION_ID)
                    .setExternalId(USER_ID)
                    .build())
            .build();
    PartnerIdentityMapping partnerIdentityMapping2 =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL_2)
            .addIdentities(
                PartnerIdentity.newBuilder()
                    .setIntegrationId(INSTALLATION_ID)
                    .setExternalId(USER_ID_2)
                    .build())
            .build();

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(APP_USER);
    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of(partnerIdentityMapping, partnerIdentityMapping2));
    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class)))
        .thenReturn(Mono.just(new GetMemberInformation200Response(USER_ID, VercelRole.ADMIN)));

    // When
    vercelNativeAccountSvcImpl.syncVercelRolesWithAtlasRoles(
        INSTALLATION_ID, auditInfo, Optional.of(USER_EMAIL));

    // Then
    verify(vercelMarketplaceApiClient)
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class));
    verify(vercelMarketplaceApiClient, never())
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID_2), any(AccessToken.class));
    verify(orgAccessSvc)
        .replaceUserRolesInOrganization(
            eq(APP_USER), eq(ORG_ID), eq(Set.of(Role.ORG_OWNER)), eq(auditInfo));
    verify(orgAccessSvc, never())
        .replaceUserRolesInOrganization(
            eq(APP_USER_2), eq(ORG_ID), eq(Set.of(Role.ORG_READ_ONLY)), eq(auditInfo));
  }

  @Test
  public void
      test_syncVercelRolesWithAtlasRoles_whenReplaceUserRolesInOrganizationFails_throwsServerErrorException()
          throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    VercelNativeInstallation installation = createMockInstallationWithVercelData();

    PartnerIdentityMapping partnerIdentityMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(
                PartnerIdentity.newBuilder()
                    .setIntegrationId(INSTALLATION_ID)
                    .setExternalId(USER_ID)
                    .build())
            .build();

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(APP_USER);
    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of(partnerIdentityMapping));
    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class)))
        .thenReturn(Mono.just(new GetMemberInformation200Response(USER_ID, VercelRole.ADMIN)));
    when(orgAccessSvc.replaceUserRolesInOrganization(
            APP_USER, ORG_ID, Set.of(Role.ORG_OWNER), auditInfo))
        .thenThrow(new RuntimeException("Generic error"));

    // When
    UncheckedSvcException exception =
        assertThrows(
            UncheckedSvcException.class,
            () ->
                vercelNativeAccountSvcImpl.syncVercelRolesWithAtlasRoles(
                    INSTALLATION_ID, auditInfo, Optional.empty()));

    // Then
    assertEquals(CommonErrorCode.SERVER_ERROR, exception.getErrorCode());
    verify(orgAccessSvc)
        .replaceUserRolesInOrganization(
            eq(APP_USER), eq(ORG_ID), eq(Set.of(Role.ORG_OWNER)), eq(auditInfo));
  }

  @Test
  public void
      test_syncVercelRolesWithAtlasRoles_whenVercelApiReturnsInternalServerError_throwsServerErrorExceptionAndSuspendsOtherThreads()
          throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    VercelNativeInstallation installation = createMockInstallationWithVercelData();

    PartnerIdentityMapping partnerIdentityMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(
                PartnerIdentity.newBuilder()
                    .setIntegrationId(INSTALLATION_ID)
                    .setExternalId(USER_ID)
                    .build())
            .build();
    PartnerIdentityMapping partnerIdentityMapping2 =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL_2)
            .addIdentities(
                PartnerIdentity.newBuilder()
                    .setIntegrationId(INSTALLATION_ID)
                    .setExternalId(USER_ID_2)
                    .build())
            .build();

    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of(partnerIdentityMapping, partnerIdentityMapping2));
    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    // Simulate API execution delay so error from other API call is triggered first
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class)))
        .thenReturn(
            Mono.just(new GetMemberInformation200Response(USER_ID, VercelRole.ADMIN))
                .delayElement(Duration.ofMillis(500)));
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID_2), any(AccessToken.class)))
        .thenReturn(Mono.error(new VercelApiRequestException("Internal server error", 500)));

    // When
    UncheckedSvcException exception =
        assertThrows(
            UncheckedSvcException.class,
            () ->
                vercelNativeAccountSvcImpl.syncVercelRolesWithAtlasRoles(
                    INSTALLATION_ID, auditInfo, Optional.empty()));

    // Then
    assertEquals(CommonErrorCode.SERVER_ERROR, exception.getErrorCode());
    verify(vercelMarketplaceApiClient)
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class));
    verify(vercelMarketplaceApiClient)
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID_2), any(AccessToken.class));
    verify(orgAccessSvc, never())
        .replaceUserRolesInOrganization(
            eq(APP_USER), eq(ORG_ID), eq(Set.of(Role.ORG_OWNER)), eq(auditInfo));
    verify(orgAccessSvc, never())
        .replaceUserRolesInOrganization(
            eq(APP_USER_2), eq(ORG_ID), eq(Set.of(Role.ORG_READ_ONLY)), eq(auditInfo));
  }

  @Test
  public void
      test_syncVercelRolesWithAtlasRoles_whenVercelApiReturnsUserNotFoundError_deletesAssociatedDataAndContinuesWithOtherThreads()
          throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    VercelNativeInstallation installation = createMockInstallationWithVercelData();

    PartnerIdentity identity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID_2)
            .build();
    PartnerIdentityMapping partnerIdentityMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(
                PartnerIdentity.newBuilder()
                    .setIntegrationId(INSTALLATION_ID)
                    .setExternalId(USER_ID)
                    .build())
            .build();
    PartnerIdentityMapping partnerIdentityMapping2 =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL_2).addIdentities(identity).build();
    PartnerIdentityMapping partnerIdentityMapping2AfterIdentityDeletion =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL_2).build();

    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of(partnerIdentityMapping, partnerIdentityMapping2));
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL_2))
        .thenReturn(Optional.of(partnerIdentityMapping2AfterIdentityDeletion));
    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(APP_USER);
    when(userSvc.findByUsername(USER_EMAIL_2)).thenReturn(APP_USER_2);
    when(APP_USER_2.getId()).thenReturn(APP_USER_ID_2);
    // Simulate API execution delay so error from other API call is triggered first
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class)))
        .thenReturn(
            Mono.just(new GetMemberInformation200Response(USER_ID, VercelRole.ADMIN))
                .delayElement(Duration.ofMillis(500)));
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID_2), any(AccessToken.class)))
        .thenReturn(Mono.error(new VercelApiRequestException("User not found", 404)));
    doNothing()
        .when(orgAccessSvc)
        .removeUserFromOrganization(eq(APP_USER_2), eq(ORG_ID), eq(auditInfo));
    doNothing().when(userSvc).clearPartnerIntegrationsData(APP_USER_ID_2);

    // When
    vercelNativeAccountSvcImpl.syncVercelRolesWithAtlasRoles(
        INSTALLATION_ID, auditInfo, Optional.empty());

    // Successfully updates roles for APP_USER
    verify(vercelMarketplaceApiClient)
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class));
    verify(orgAccessSvc)
        .replaceUserRolesInOrganization(
            eq(APP_USER), eq(ORG_ID), eq(Set.of(Role.ORG_OWNER)), eq(auditInfo));

    // Does not delete associated data for APP_USER
    verify(partnerIdentityClient, never())
        .deleteMappingByEmail(eq(USER_EMAIL), eq(List.of(identity)));
    verify(userSvc, never()).clearPartnerIntegrationsData(APP_USER_ID);
    verify(orgAccessSvc, never())
        .removeUserFromOrganization(eq(APP_USER), eq(ORG_ID), eq(auditInfo));

    // Does not update roles for APP_USER_2
    verify(vercelMarketplaceApiClient)
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID_2), any(AccessToken.class));
    verify(orgAccessSvc, never())
        .replaceUserRolesInOrganization(
            eq(APP_USER_2), eq(ORG_ID), eq(Set.of(Role.ORG_READ_ONLY)), eq(auditInfo));

    // Deletes associated data for APP_USER_2
    verify(partnerIdentityClient).deleteMappingByEmail(eq(USER_EMAIL_2), eq(List.of(identity)));
    verify(userSvc).clearPartnerIntegrationsData(APP_USER_ID_2);
    verify(orgAccessSvc).removeUserFromOrganization(eq(APP_USER_2), eq(ORG_ID), eq(auditInfo));
  }

  @Test
  public void
      test_syncVercelRolesWithAtlasRoles_whenVercelApiReturnsUserNotFoundErrorAndUserIsLastOrgOwner_deletesAssociatedDataAndContinuesWithOtherThreads()
          throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    VercelNativeInstallation installation = createMockInstallationWithVercelData();

    PartnerIdentity identity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID)
            .build();
    PartnerIdentityMapping partnerIdentityMapping =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL).addIdentities(identity).build();
    PartnerIdentityMapping partnerIdentityMappingAfterIdentityDeletion =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL).build();

    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of(partnerIdentityMapping));
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL))
        .thenReturn(Optional.of(partnerIdentityMappingAfterIdentityDeletion));
    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(APP_USER);
    when(APP_USER.getId()).thenReturn(APP_USER_ID);
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class)))
        .thenReturn(Mono.error(new VercelApiRequestException("User not found", 404)));
    doThrow(new SvcException(AppUserErrorCode.CANNOT_DELETE_LAST_ORG_OWNER))
        .when(orgAccessSvc)
        .removeUserFromOrganization(eq(APP_USER), eq(ORG_ID), eq(auditInfo));
    doNothing().when(userSvc).clearPartnerIntegrationsData(APP_USER_ID);

    // When
    vercelNativeAccountSvcImpl.syncVercelRolesWithAtlasRoles(
        INSTALLATION_ID, auditInfo, Optional.empty());

    // Does not update roles for APP_USER
    verify(vercelMarketplaceApiClient)
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class));
    verify(orgAccessSvc, never())
        .replaceUserRolesInOrganization(
            eq(APP_USER), eq(ORG_ID), eq(Set.of(Role.ORG_READ_ONLY)), eq(auditInfo));

    // Deletes associated data for APP_USER
    verify(partnerIdentityClient).deleteMappingByEmail(eq(USER_EMAIL), eq(List.of(identity)));
    verify(userSvc).clearPartnerIntegrationsData(APP_USER_ID);
    verify(orgAccessSvc).removeUserFromOrganization(eq(APP_USER), eq(ORG_ID), eq(auditInfo));
  }

  @Test
  public void
      test_syncVercelRolesWithAtlasRoles_whenVercelApiReturnsUserNotFoundErrorAndDeletionFails_throwsServerErrorExceptionAndSuspendsOtherThreads()
          throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    VercelNativeInstallation installation = createMockInstallationWithVercelData();

    PartnerIdentity identity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID_2)
            .build();
    PartnerIdentityMapping partnerIdentityMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(
                PartnerIdentity.newBuilder()
                    .setIntegrationId(INSTALLATION_ID)
                    .setExternalId(USER_ID)
                    .build())
            .build();
    PartnerIdentityMapping partnerIdentityMapping2 =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL_2).addIdentities(identity).build();
    PartnerIdentityMapping partnerIdentityMapping2AfterIdentityDeletion =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL_2).build();

    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of(partnerIdentityMapping, partnerIdentityMapping2));
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL_2))
        .thenReturn(Optional.of(partnerIdentityMapping2AfterIdentityDeletion));
    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(APP_USER);
    when(userSvc.findByUsername(USER_EMAIL_2)).thenReturn(APP_USER_2);
    when(APP_USER_2.getId()).thenReturn(APP_USER_ID_2);
    // Simulate API execution delay so error from other API call is triggered first
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class)))
        .thenReturn(
            Mono.just(new GetMemberInformation200Response(USER_ID, VercelRole.ADMIN))
                .delayElement(Duration.ofMillis(500)));
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID_2), any(AccessToken.class)))
        .thenReturn(Mono.error(new VercelApiRequestException("User not found", 404)));
    doThrow(new RuntimeException("Generic error"))
        .when(orgAccessSvc)
        .removeUserFromOrganization(eq(APP_USER), eq(ORG_ID), eq(auditInfo));
    doNothing().when(userSvc).clearPartnerIntegrationsData(APP_USER_ID_2);

    // When
    UncheckedSvcException exception =
        assertThrows(
            UncheckedSvcException.class,
            () ->
                vercelNativeAccountSvcImpl.syncVercelRolesWithAtlasRoles(
                    INSTALLATION_ID, auditInfo, Optional.empty()));

    // Then
    assertEquals(CommonErrorCode.SERVER_ERROR, exception.getErrorCode());

    // Does not update roles for APP_USER as the remaining threads were suspended
    verify(vercelMarketplaceApiClient)
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class));
    verify(orgAccessSvc, never())
        .replaceUserRolesInOrganization(
            eq(APP_USER), eq(ORG_ID), eq(Set.of(Role.ORG_OWNER)), eq(auditInfo));

    // Does not delete associated data for APP_USER
    verify(partnerIdentityClient, never())
        .deleteMappingByEmail(eq(USER_EMAIL), eq(List.of(identity)));
    verify(userSvc, never()).clearPartnerIntegrationsData(APP_USER_ID);
    verify(orgAccessSvc, never())
        .removeUserFromOrganization(eq(APP_USER), eq(ORG_ID), eq(auditInfo));

    // Does not update roles for APP_USER_2
    verify(vercelMarketplaceApiClient)
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID_2), any(AccessToken.class));
    verify(orgAccessSvc, never())
        .replaceUserRolesInOrganization(
            eq(APP_USER_2), eq(ORG_ID), eq(Set.of(Role.ORG_READ_ONLY)), eq(auditInfo));

    // Deletes associated data for APP_USER_2
    verify(partnerIdentityClient).deleteMappingByEmail(eq(USER_EMAIL_2), eq(List.of(identity)));
    verify(userSvc).clearPartnerIntegrationsData(APP_USER_ID_2);
    verify(orgAccessSvc).removeUserFromOrganization(eq(APP_USER_2), eq(ORG_ID), eq(auditInfo));
  }

  @Test
  public void
      test_syncVercelRolesWithAtlasRoles_whenInstallationNotFound_throwsBadRequestException() {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);

    // When
    UncheckedSvcException exception =
        assertThrows(
            UncheckedSvcException.class,
            () ->
                vercelNativeAccountSvcImpl.syncVercelRolesWithAtlasRoles(
                    INSTALLATION_ID, auditInfo, Optional.empty()));

    // Then
    assertEquals(CommonErrorCode.BAD_REQUEST, exception.getErrorCode());
  }

  @Test
  public void test_syncVercelRolesWithAtlasRoles_whenOrgNotFound_skipsMethod() {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    VercelNativeInstallation installation = mock(VercelNativeInstallation.class);
    VercelData vercelData = mock(VercelData.class);

    when(installation.vercelData()).thenReturn(vercelData);
    when(vercelData.accessToken()).thenReturn("1234");
    when(installationsSvc.findExistingInstallation(INSTALLATION_ID)).thenReturn(installation);
    doReturn(mock(AccessToken.class))
        .when(vercelNativeAccountSvcImpl)
        .createBearerAccessToken(anyString());

    // When
    vercelNativeAccountSvcImpl.syncVercelRolesWithAtlasRoles(
        INSTALLATION_ID, auditInfo, Optional.empty());

    // Then
    verify(vercelMarketplaceApiClient, never())
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class));
  }

  @Test
  public void test_uninstallVercelIntegration_success() throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    VercelNativeInstallation installation = createMockInstallationWithVercelData();

    PartnerIdentity partnerIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID)
            .build();
    PartnerIdentity partnerIdentity2 =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID_2)
            .build();
    PartnerIdentityMapping partnerIdentityMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(partnerIdentity)
            .build();
    PartnerIdentityMapping partnerIdentityMapping2 =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL_2)
            .addIdentities(partnerIdentity2)
            .build();
    PartnerIdentityMapping partnerIdentityMappingWithNoIdentities =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL).build();
    PartnerIdentityMapping partnerIdentityMappingWithNoIdentities2 =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL_2).build();

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(APP_USER);
    when(userSvc.findByUsername(USER_EMAIL_2)).thenReturn(APP_USER_2);
    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of(partnerIdentityMapping, partnerIdentityMapping2));
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL))
        .thenReturn(Optional.of(partnerIdentityMapping))
        .thenReturn(
            Optional.of(
                partnerIdentityMappingWithNoIdentities)); // 2nd invocation is when partner identity
    // is deleted
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL_2))
        .thenReturn(Optional.of(partnerIdentityMapping2))
        .thenReturn(
            Optional.of(partnerIdentityMappingWithNoIdentities2)); // 2nd invocation is when partner
    // identity is deleted
    when(installationsSvc.findInstallation(INSTALLATION_ID)).thenReturn(Optional.of(installation));
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class)))
        .thenReturn(Mono.just(new GetMemberInformation200Response(USER_ID, VercelRole.ADMIN)));
    when(vercelMarketplaceApiClient.getMemberInformation(
            eq(INSTALLATION_ID), eq(USER_ID_2), any(AccessToken.class)))
        .thenReturn(Mono.just(new GetMemberInformation200Response(USER_ID_2, VercelRole.USER)));
    when(APP_USER.getId()).thenReturn(APP_USER_ID);
    when(APP_USER_2.getId()).thenReturn(APP_USER_ID_2);

    // When
    vercelNativeAccountSvcImpl.uninstallVercelIntegration(INSTALLATION_ID, auditInfo);

    // Then
    verify(orgAccessSvc)
        .replaceUserRolesInOrganization(
            eq(APP_USER), eq(ORG_ID), eq(Set.of(Role.ORG_OWNER)), eq(auditInfo));
    verify(orgAccessSvc)
        .replaceUserRolesInOrganization(
            eq(APP_USER_2), eq(ORG_ID), eq(Set.of(Role.ORG_READ_ONLY)), eq(auditInfo));
    verify(partnerIdentityClient).deleteMappingByEmail(USER_EMAIL, List.of(partnerIdentity));
    verify(partnerIdentityClient).deleteMappingByEmail(USER_EMAIL_2, List.of(partnerIdentity2));
    verify(userSvc).clearPartnerIntegrationsData(APP_USER_ID);
    verify(userSvc).clearPartnerIntegrationsData(APP_USER_ID_2);
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeUninstallationCounter();
    verify(vercelNativeAccountSvcImpl, never()).incrementVercelNativeUninstallationErrorCounter();

    // Ensure operation is idempotent by running it again and ensuring there are no failures
    reset(orgAccessSvc, partnerIdentityClient, userSvc);

    // Mock the state after first uninstall - no partner identities should exist
    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of()); // Empty list - no mappings exist after first uninstall

    // Run uninstall again - should be idempotent
    vercelNativeAccountSvcImpl.uninstallVercelIntegration(INSTALLATION_ID, auditInfo);

    // Verify no operations were performed on second call
    verify(orgAccessSvc, never()).replaceUserRolesInOrganization(any(), any(), any(), any());
    verify(partnerIdentityClient, never()).deleteMappingByEmail(any(), any());
    verify(userSvc, never()).clearPartnerIntegrationsData(any());
  }

  @Test
  public void test_uninstallVercelIntegration_isLoadTesting() throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    when(vercelNativeConfig.isLoadTestingScenario()).thenReturn(true);

    PartnerIdentity partnerIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID)
            .build();
    PartnerIdentity partnerIdentity2 =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID_2)
            .build();
    PartnerIdentityMapping partnerIdentityMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(partnerIdentity)
            .build();
    PartnerIdentityMapping partnerIdentityMapping2 =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL_2)
            .addIdentities(partnerIdentity2)
            .build();
    PartnerIdentityMapping partnerIdentityMappingWithNoIdentities =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL).build();
    PartnerIdentityMapping partnerIdentityMappingWithNoIdentities2 =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL_2).build();

    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(APP_USER);
    when(userSvc.findByUsername(USER_EMAIL_2)).thenReturn(APP_USER_2);
    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of(partnerIdentityMapping, partnerIdentityMapping2));
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL))
        .thenReturn(Optional.of(partnerIdentityMapping))
        .thenReturn(
            Optional.of(
                partnerIdentityMappingWithNoIdentities)); // 2nd invocation is when partner identity
    // is deleted
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL_2))
        .thenReturn(Optional.of(partnerIdentityMapping2))
        .thenReturn(
            Optional.of(partnerIdentityMappingWithNoIdentities2)); // 2nd invocation is when partner
    // identity is deleted

    when(APP_USER.getId()).thenReturn(APP_USER_ID);
    when(APP_USER_2.getId()).thenReturn(APP_USER_ID_2);

    // When
    vercelNativeAccountSvcImpl.uninstallVercelIntegration(INSTALLATION_ID, auditInfo);

    // Then
    // confirm we do NOT call Vercel API
    verify(vercelMarketplaceApiClient, never())
        .getMemberInformation(eq(INSTALLATION_ID), eq(USER_ID), any(AccessToken.class));
    verify(orgAccessSvc, never()).replaceUserRolesInOrganization(any(), any(), any(), any());

    verify(partnerIdentityClient).deleteMappingByEmail(USER_EMAIL, List.of(partnerIdentity));
    verify(partnerIdentityClient).deleteMappingByEmail(USER_EMAIL_2, List.of(partnerIdentity2));
    verify(userSvc).clearPartnerIntegrationsData(APP_USER_ID);
    verify(userSvc).clearPartnerIntegrationsData(APP_USER_ID_2);
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeUninstallationCounter();
    verify(vercelNativeAccountSvcImpl, never()).incrementVercelNativeUninstallationErrorCounter();
  }

  @Test
  public void test_uninstallVercelIntegration_whenIdentitySyncFails_throwsServerError() {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    when(vercelNativeConfig.isLoadTestingScenario()).thenReturn(false);
    doThrow(new RuntimeException("Generic error"))
        .when(vercelNativeAccountSvcImpl)
        .syncVercelRolesWithAtlasRoles(any(), any(), any());

    // When
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                vercelNativeAccountSvcImpl.uninstallVercelIntegration(INSTALLATION_ID, auditInfo));

    // Then
    assertEquals(CommonErrorCode.SERVER_ERROR, exception.getErrorCode());
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeUninstallationCounter();
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeUninstallationErrorCounter();
  }

  @Test
  public void
      test_uninstallVercelIntegration_whenPartnerIdentityFailsToBeDeleted_triggersRollbackForAffectedUsers()
          throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);

    PartnerIdentity partnerIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID)
            .build();
    PartnerIdentity partnerIdentity2 =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID_2)
            .build();
    PartnerIdentityMapping partnerIdentityMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(partnerIdentity)
            .build();
    PartnerIdentityMapping partnerIdentityMapping2 =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL_2)
            .addIdentities(partnerIdentity2)
            .build();
    PartnerIdentityMapping partnerIdentityMappingWithNoIdentities =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL).build();

    doNothing()
        .when(vercelNativeAccountSvcImpl)
        .syncVercelRolesWithAtlasRoles(INSTALLATION_ID, auditInfo, Optional.empty());
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL))
        .thenReturn(Optional.of(partnerIdentityMapping))
        .thenReturn(Optional.of(partnerIdentityMappingWithNoIdentities));
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL_2))
        .thenReturn(Optional.of(partnerIdentityMapping2));
    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of(partnerIdentityMapping, partnerIdentityMapping2));
    when(APP_USER.getId()).thenReturn(APP_USER_ID);
    when(APP_USER_2.getId()).thenReturn(APP_USER_ID_2);
    when(partnerIdentityClient.deleteMappingByEmail(USER_EMAIL, List.of(partnerIdentity)))
        .thenReturn(true);
    when(partnerIdentityClient.deleteMappingByEmail(USER_EMAIL_2, List.of(partnerIdentity)))
        .thenThrow(new RuntimeException("Some error"));

    doReturn(1).when(vercelNativeAccountSvcImpl).getMaxDbConcurrency();

    // When
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                vercelNativeAccountSvcImpl.uninstallVercelIntegration(INSTALLATION_ID, auditInfo));

    // Then
    assertEquals(CommonErrorCode.SERVER_ERROR, exception.getErrorCode());
    verify(partnerIdentityClient).deleteMappingByEmail(USER_EMAIL, List.of(partnerIdentity));
    verify(partnerIdentityClient).deleteMappingByEmail(USER_EMAIL_2, List.of(partnerIdentity2));
    verify(partnerIdentityClient).updateMapping(eq(partnerIdentityMapping));
    verify(partnerIdentityClient, never()).updateMapping(eq(partnerIdentityMapping2));
    verify(userSvc, never()).clearPartnerIntegrationsData(APP_USER_ID);
    verify(userSvc, never()).clearPartnerIntegrationsData(APP_USER_ID_2);
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeUninstallationCounter();
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeUninstallationErrorCounter();
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeRollbackCounter();
  }

  @Test
  public void
      test_uninstallVercelIntegration_whenPartnerIntegrationsDataFailsToBeCleared_triggersRollbackForAffectedUsers()
          throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);

    PartnerIdentity partnerIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID)
            .build();
    PartnerIdentity partnerIdentity2 =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID_2)
            .build();
    PartnerIdentityMapping partnerIdentityMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(partnerIdentity)
            .build();
    PartnerIdentityMapping partnerIdentityMapping2 =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL_2)
            .addIdentities(partnerIdentity2)
            .build();
    PartnerIdentityMapping partnerIdentityMappingWithNoIdentities =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL).build();
    PartnerIdentityMapping partnerIdentityMapping2WithNoIdentities =
        PartnerIdentityMapping.newBuilder().setEmail(USER_EMAIL_2).build();

    doNothing()
        .when(vercelNativeAccountSvcImpl)
        .syncVercelRolesWithAtlasRoles(INSTALLATION_ID, auditInfo, Optional.empty());
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL))
        .thenReturn(Optional.of(partnerIdentityMapping))
        .thenReturn(Optional.of(partnerIdentityMappingWithNoIdentities));
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL_2))
        .thenReturn(Optional.of(partnerIdentityMapping2))
        .thenReturn(Optional.of(partnerIdentityMapping2WithNoIdentities));
    when(partnerIdentityClient.getMappingsByIntegration(INSTALLATION_ID))
        .thenReturn(List.of(partnerIdentityMapping, partnerIdentityMapping2));
    when(APP_USER.getId()).thenReturn(APP_USER_ID);
    when(APP_USER_2.getId()).thenReturn(APP_USER_ID_2);
    when(partnerIdentityClient.deleteMappingByEmail(USER_EMAIL, List.of(partnerIdentity)))
        .thenReturn(true);
    when(partnerIdentityClient.deleteMappingByEmail(USER_EMAIL_2, List.of(partnerIdentity2)))
        .thenReturn(true);
    when(userSvc.findByUsername(USER_EMAIL)).thenReturn(APP_USER);
    when(userSvc.findByUsername(USER_EMAIL_2)).thenReturn(APP_USER_2);
    doNothing().when(userSvc).clearPartnerIntegrationsData(APP_USER_ID);
    doThrow(new RuntimeException("Generic error"))
        .when(userSvc)
        .clearPartnerIntegrationsData(APP_USER_ID_2);

    doReturn(1).when(vercelNativeAccountSvcImpl).getMaxDbConcurrency();

    // When
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                vercelNativeAccountSvcImpl.uninstallVercelIntegration(INSTALLATION_ID, auditInfo));

    // Then
    assertEquals(CommonErrorCode.SERVER_ERROR, exception.getErrorCode());
    verify(partnerIdentityClient).deleteMappingByEmail(USER_EMAIL, List.of(partnerIdentity));
    verify(partnerIdentityClient).deleteMappingByEmail(USER_EMAIL_2, List.of(partnerIdentity2));
    verify(userSvc).clearPartnerIntegrationsData(APP_USER_ID);
    verify(userSvc).clearPartnerIntegrationsData(APP_USER_ID_2);
    verify(partnerIdentityClient).updateMapping(eq(partnerIdentityMapping));
    verify(partnerIdentityClient).updateMapping(eq(partnerIdentityMapping2));
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeUninstallationCounter();
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeUninstallationErrorCounter();
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeRollbackCounter();
  }

  @Test
  public void test_handleOidcCallback_success() throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    PartnerIdentityMapping mapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(PartnerIdentity.newBuilder().setIntegrationId(INSTALLATION_ID).build())
            .build();

    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL)).thenReturn(Optional.of(mapping));
    doNothing()
        .when(vercelNativeAccountSvcImpl)
        .syncVercelRolesWithAtlasRoles(INSTALLATION_ID, auditInfo, Optional.of(USER_EMAIL));

    // When
    vercelNativeAccountSvcImpl.handleOidcCallback(USER_EMAIL, auditInfo);

    // Then
    verify(vercelNativeAccountSvcImpl)
        .syncVercelRolesWithAtlasRoles(INSTALLATION_ID, auditInfo, Optional.of(USER_EMAIL));
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeOidcCallbackCounter();
    verify(vercelNativeAccountSvcImpl, never()).incrementVercelNativeOidcCallbackErrorCounter();
  }

  @Test
  public void test_handleOidcCallback_whenMappingsNotFound_throwsBadRequestException()
      throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL)).thenReturn(Optional.empty());

    // When
    UncheckedSvcException exception =
        assertThrows(
            UncheckedSvcException.class,
            () -> vercelNativeAccountSvcImpl.handleOidcCallback(USER_EMAIL, auditInfo));

    // Then
    assertEquals(CommonErrorCode.BAD_REQUEST, exception.getErrorCode());
    verify(partnerIdentityClient).getMappingByEmail(USER_EMAIL);
    verify(vercelNativeAccountSvcImpl, never())
        .syncVercelRolesWithAtlasRoles(anyString(), any(), any());
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeOidcCallbackCounter();
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeOidcCallbackErrorCounter();
  }

  @Test
  public void test_handleOidcCallback_whenErrorDuringPermissionSync_throwsServerErrorException()
      throws SvcException {
    // Given
    AuditInfo auditInfo = mock(AuditInfo.class);
    PartnerIdentityMapping mapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(PartnerIdentity.newBuilder().setIntegrationId(INSTALLATION_ID).build())
            .build();

    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL)).thenReturn(Optional.of(mapping));
    doThrow(new UncheckedSvcException(CommonErrorCode.SERVER_ERROR, "Generic error"))
        .when(vercelNativeAccountSvcImpl)
        .syncVercelRolesWithAtlasRoles(INSTALLATION_ID, auditInfo, Optional.of(USER_EMAIL));

    // When
    UncheckedSvcException exception =
        assertThrows(
            UncheckedSvcException.class,
            () -> vercelNativeAccountSvcImpl.handleOidcCallback(USER_EMAIL, auditInfo));

    // Then
    assertEquals(CommonErrorCode.SERVER_ERROR, exception.getErrorCode());
    verify(vercelNativeAccountSvcImpl)
        .syncVercelRolesWithAtlasRoles(INSTALLATION_ID, auditInfo, Optional.of(USER_EMAIL));
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeOidcCallbackCounter();
    verify(vercelNativeAccountSvcImpl).incrementVercelNativeOidcCallbackErrorCounter();
  }

  // ===== Helper methods =====
  /**
   * Creates a valid JWT and builds a mock HTTP request with the Authorization header set to include
   * it.
   */
  private HttpServletRequest createMockRequestHeader() {
    var token = createVercelUserTokenWithRole(VercelRole.USER);
    HttpServletRequest mockRequestHeader = mock(HttpServletRequest.class);

    // Mock the vercelAuth.decodeVercelJwtFromRequest to return the parsed claims
    try {
      JWTClaimsSet claimsSet = JWTParser.parse(token).getJWTClaimsSet();
      when(vercelAuth.parseVercelJwtFromRequest(mockRequestHeader)).thenReturn(claimsSet);
    } catch (Exception e) {
      throw new RuntimeException("Failed to parse JWT for mock", e);
    }

    return mockRequestHeader;
  }

  /** Creates a Vercel user token with default USER role. */
  private String createVercelUserToken() {
    return createVercelUserTokenWithRole(VercelRole.USER);
  }

  /** Creates a Vercel user token with specified role. */
  private String createVercelUserTokenWithRole(VercelRole role) {
    try {
      JWTClaimsSet claimsSet =
          new JWTClaimsSet.Builder()
              .claim("account_id", ACCOUNT_ID)
              .claim("installation_id", INSTALLATION_ID)
              .claim(USER_ID_FIELD, USER_ID)
              .claim(USER_EMAIL_FIELD, USER_EMAIL)
              .claim("user_role", role.name())
              .claim(USER_NAME_FIELD, FIRST_NAME + " " + LAST_NAME)
              .subject(USER_EMAIL)
              .expirationTime(new Date())
              .build();

      SignedJWT signedJWT = new SignedJWT(new JWSHeader(JWSAlgorithm.HS256), claimsSet);
      JWSSigner signer =
          new MACSigner("test-secret-that-is-at-least-256-bits-long-for-hmac-sha256-algorithm");
      signedJWT.sign(signer);
      return signedJWT.serialize();
    } catch (Exception e) {
      throw new RuntimeException("Failed to create JWT", e);
    }
  }

  /** Creates a Vercel system token. */
  private String createVercelSystemToken() {
    try {
      JWTClaimsSet claimsSet =
          new JWTClaimsSet.Builder()
              .claim("account_id", ACCOUNT_ID)
              .claim("installation_id", INSTALLATION_ID)
              .subject("system")
              .expirationTime(new Date())
              .build();

      SignedJWT signedJWT = new SignedJWT(new JWSHeader(JWSAlgorithm.HS256), claimsSet);
      JWSSigner signer =
          new MACSigner("test-secret-that-is-at-least-256-bits-long-for-hmac-sha256-algorithm");
      signedJWT.sign(signer);
      return signedJWT.serialize();
    } catch (Exception e) {
      throw new RuntimeException("Failed to create JWT", e);
    }
  }

  /** Creates a simple JWT for testing. */
  private String createSimpleJWT() {
    try {
      JWTClaimsSet claimsSet = new JWTClaimsSet.Builder().expirationTime(new Date()).build();

      SignedJWT signedJWT = new SignedJWT(new JWSHeader(JWSAlgorithm.HS256), claimsSet);
      JWSSigner signer =
          new MACSigner("test-secret-that-is-at-least-256-bits-long-for-hmac-sha256-algorithm");
      signedJWT.sign(signer);
      return signedJWT.serialize();
    } catch (Exception e) {
      throw new RuntimeException("Failed to create JWT", e);
    }
  }

  /** Creates a mock VercelNativeInstallation with AtlasResources. */
  private VercelNativeInstallation createMockInstallation() {
    VercelNativeInstallation installation = mock(VercelNativeInstallation.class);
    AtlasResources atlasResources = mock(AtlasResources.class);
    when(atlasResources.organizationId()).thenReturn(ORG_ID);
    when(installation.atlasResources()).thenReturn(atlasResources);
    return installation;
  }

  /**
   * Creates a mock VercelNativeInstallation with VercelData for tests where we need an AccessToken.
   */
  private VercelNativeInstallation createMockInstallationWithVercelData() {
    VercelNativeInstallation installation = mock(VercelNativeInstallation.class);
    AtlasResources atlasResources = mock(AtlasResources.class);
    VercelData vercelData = mock(VercelData.class);

    when(atlasResources.organizationId()).thenReturn(ORG_ID);
    when(installation.atlasResources()).thenReturn(atlasResources);
    when(installation.vercelData()).thenReturn(vercelData);
    when(vercelData.accessToken()).thenReturn("1234");

    when(installationsSvc.findExistingInstallation(INSTALLATION_ID)).thenReturn(installation);

    doReturn(mock(AccessToken.class))
        .when(vercelNativeAccountSvcImpl)
        .createBearerAccessToken(anyString());

    return installation;
  }

  /** Creates a test TentativePartnerAccountLink for testing. */
  private TentativePartnerAccountLink createTestTentativeLink() {
    return new TentativePartnerAccountLink.Builder()
        .id(new ObjectId())
        .sessionId("test-session-id")
        .email(USER_EMAIL)
        .externalId(USER_ID)
        .integrationId(INSTALLATION_ID)
        .consented(true)
        .createdAt(Instant.now())
        .build();
  }

  @Test
  public void hasExistingPartnerIdentityMapping_userHasMatchingInstallationId_returnsTrue()
      throws AuthnServiceException {
    // Given
    String username = USER_EMAIL;
    String installationId = INSTALLATION_ID;

    PartnerIdentity matchingIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(installationId)
            .setExternalId(USER_ID)
            .build();

    PartnerIdentityMapping existingMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(username)
            .addIdentities(matchingIdentity)
            .build();

    when(partnerIdentityClient.getMappingByEmail(username))
        .thenReturn(Optional.of(existingMapping));

    // When
    boolean result =
        vercelNativeAccountSvcImpl.hasExistingPartnerIdentityMapping(username, installationId);

    // Then
    assertTrue(result);
    verify(partnerIdentityClient).getMappingByEmail(username);
  }

  @Test
  public void hasExistingPartnerIdentityMapping_userHasNonMatchingInstallationId_returnsFalse()
      throws AuthnServiceException {
    // Given
    String username = USER_EMAIL;
    String installationId = INSTALLATION_ID;
    String differentInstallationId = INSTALLATION_ID_2;

    PartnerIdentity nonMatchingIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(differentInstallationId)
            .setExternalId(USER_ID)
            .build();

    PartnerIdentityMapping existingMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(username)
            .addIdentities(nonMatchingIdentity)
            .build();

    when(partnerIdentityClient.getMappingByEmail(username))
        .thenReturn(Optional.of(existingMapping));

    // When
    boolean result =
        vercelNativeAccountSvcImpl.hasExistingPartnerIdentityMapping(username, installationId);

    // Then
    assertFalse(result);
    verify(partnerIdentityClient).getMappingByEmail(username);
  }

  @Test
  public void hasExistingPartnerIdentityMapping_userHasMultipleIdentitiesWithMatch_returnsTrue()
      throws AuthnServiceException {
    // Given
    String username = USER_EMAIL;
    String installationId = INSTALLATION_ID;

    PartnerIdentity nonMatchingIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID_2)
            .setExternalId(USER_ID_2)
            .build();

    PartnerIdentity matchingIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(installationId)
            .setExternalId(USER_ID)
            .build();

    PartnerIdentityMapping existingMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(username)
            .addIdentities(nonMatchingIdentity)
            .addIdentities(matchingIdentity)
            .build();

    when(partnerIdentityClient.getMappingByEmail(username))
        .thenReturn(Optional.of(existingMapping));

    // When
    boolean result =
        vercelNativeAccountSvcImpl.hasExistingPartnerIdentityMapping(username, installationId);

    // Then
    assertTrue(result);
    verify(partnerIdentityClient).getMappingByEmail(username);
  }

  @Test
  public void hasExistingPartnerIdentityMapping_noMappingExists_returnsFalse()
      throws AuthnServiceException {
    // Given
    String username = USER_EMAIL;
    String installationId = INSTALLATION_ID;

    when(partnerIdentityClient.getMappingByEmail(username)).thenReturn(Optional.empty());

    // When
    boolean result =
        vercelNativeAccountSvcImpl.hasExistingPartnerIdentityMapping(username, installationId);

    // Then
    assertFalse(result);
    verify(partnerIdentityClient).getMappingByEmail(username);
  }

  @Test
  public void hasExistingPartnerIdentityMapping_partnerIdentityClientThrowsException_returnsFalse()
      throws AuthnServiceException {
    // Given
    String username = USER_EMAIL;
    String installationId = INSTALLATION_ID;

    when(partnerIdentityClient.getMappingByEmail(username))
        .thenThrow(new RuntimeException("Database connection error"));

    // When
    boolean result =
        vercelNativeAccountSvcImpl.hasExistingPartnerIdentityMapping(username, installationId);

    // Then
    assertFalse(result);
    verify(partnerIdentityClient).getMappingByEmail(username);
  }

  @Test
  public void hasExistingPartnerIdentityMapping_mappingExistsButNoIdentities_returnsFalse()
      throws AuthnServiceException {
    // Given
    String username = USER_EMAIL;
    String installationId = INSTALLATION_ID;

    PartnerIdentityMapping existingMapping =
        PartnerIdentityMapping.newBuilder().setEmail(username).build(); // No identities added

    when(partnerIdentityClient.getMappingByEmail(username))
        .thenReturn(Optional.of(existingMapping));

    // When
    boolean result =
        vercelNativeAccountSvcImpl.hasExistingPartnerIdentityMapping(username, installationId);

    // Then
    assertFalse(result);
    verify(partnerIdentityClient).getMappingByEmail(username);
  }

  // ===== Tests for findLoginTargetResource method =====
  @Test
  public void findLoginTargetResource_installationNotFound_returnsEmpty() {
    // Given
    String installationId = "non-existent-installation";
    String resourceId = "some-resource-id";

    when(installationsSvc.findInstallation(installationId)).thenReturn(Optional.empty());

    // When
    Optional<AtlasResources> result =
        vercelNativeAccountSvcImpl.findLoginTargetResource(installationId, resourceId);

    // Then
    assertTrue(result.isEmpty());
  }

  @Test
  public void findLoginTargetResource_installationFoundButNoAtlasResources_returnsEmpty() {
    // Given
    String installationId = "test-installation";
    String resourceId = "some-resource-id";
    VercelNativeInstallation installation = mock(VercelNativeInstallation.class);

    when(installationsSvc.findInstallation(installationId)).thenReturn(Optional.of(installation));
    when(installation.atlasResources()).thenReturn(null);

    // When
    Optional<AtlasResources> result =
        vercelNativeAccountSvcImpl.findLoginTargetResource(installationId, resourceId);

    // Then
    assertTrue(result.isEmpty());
  }

  @Test
  public void findLoginTargetResource_nullResourceId_returnsResourcesWithEmptyProducts() {
    // Given
    String installationId = "test-installation";
    String resourceId = null;
    ObjectId orgId = new ObjectId();
    ObjectId vercelResourceId1 = new ObjectId();
    ObjectId vercelResourceId2 = new ObjectId();

    InstalledProduct product1 =
        InstalledProduct.builder()
            .vercelResourceId(vercelResourceId1)
            .projectId(new ObjectId())
            .clusterName("cluster1")
            .build();

    InstalledProduct product2 =
        InstalledProduct.builder()
            .vercelResourceId(vercelResourceId2)
            .projectId(new ObjectId())
            .clusterName("cluster2")
            .build();

    AtlasResources originalResources =
        AtlasResources.builder()
            .organizationId(orgId)
            .installedProducts(List.of(product1, product2))
            .build();

    VercelNativeInstallation installation = mock(VercelNativeInstallation.class);

    when(installationsSvc.findInstallation(installationId)).thenReturn(Optional.of(installation));
    when(installation.atlasResources()).thenReturn(originalResources);

    // When
    Optional<AtlasResources> result =
        vercelNativeAccountSvcImpl.findLoginTargetResource(installationId, resourceId);

    // Then
    assertTrue(result.isPresent());
    AtlasResources filteredResources = result.get();
    assertEquals(orgId, filteredResources.organizationId());
    assertTrue(filteredResources.installedProducts().isEmpty());
  }

  @Test
  public void findLoginTargetResource_validResourceId_returnsMatchingProduct() {
    // Given
    String installationId = "test-installation";
    ObjectId targetResourceId = new ObjectId();
    String resourceId = targetResourceId.toHexString();
    ObjectId orgId = new ObjectId();
    ObjectId otherResourceId = new ObjectId();

    InstalledProduct targetProduct =
        InstalledProduct.builder()
            .vercelResourceId(targetResourceId)
            .projectId(new ObjectId())
            .clusterName("target-cluster")
            .build();

    InstalledProduct otherProduct =
        InstalledProduct.builder()
            .vercelResourceId(otherResourceId)
            .projectId(new ObjectId())
            .clusterName("other-cluster")
            .build();

    AtlasResources originalResources =
        AtlasResources.builder()
            .organizationId(orgId)
            .installedProducts(List.of(targetProduct, otherProduct))
            .build();

    VercelNativeInstallation installation = mock(VercelNativeInstallation.class);

    when(installationsSvc.findInstallation(installationId)).thenReturn(Optional.of(installation));
    when(installation.atlasResources()).thenReturn(originalResources);

    // When
    Optional<AtlasResources> result =
        vercelNativeAccountSvcImpl.findLoginTargetResource(installationId, resourceId);

    // Then
    assertTrue(result.isPresent());
    AtlasResources filteredResources = result.get();
    assertEquals(orgId, filteredResources.organizationId());
    assertEquals(1, filteredResources.installedProducts().size());
    assertEquals(targetProduct, filteredResources.installedProducts().get(0));
  }

  @Test
  public void findLoginTargetResource_noMatchingResourceId_returnsEmptyProducts() {
    // Given
    String installationId = "test-installation";
    ObjectId nonMatchingResourceId = new ObjectId();
    String resourceId = nonMatchingResourceId.toHexString();
    ObjectId orgId = new ObjectId();
    ObjectId existingResourceId = new ObjectId();

    InstalledProduct existingProduct =
        InstalledProduct.builder()
            .vercelResourceId(existingResourceId)
            .projectId(new ObjectId())
            .clusterName("existing-cluster")
            .build();

    AtlasResources originalResources =
        AtlasResources.builder()
            .organizationId(orgId)
            .installedProducts(List.of(existingProduct))
            .build();

    VercelNativeInstallation installation = mock(VercelNativeInstallation.class);

    when(installationsSvc.findInstallation(installationId)).thenReturn(Optional.of(installation));
    when(installation.atlasResources()).thenReturn(originalResources);

    // When
    Optional<AtlasResources> result =
        vercelNativeAccountSvcImpl.findLoginTargetResource(installationId, resourceId);

    // Then
    assertTrue(result.isPresent());
    AtlasResources filteredResources = result.get();
    assertEquals(orgId, filteredResources.organizationId());
    assertTrue(filteredResources.installedProducts().isEmpty());
  }

  @Test
  public void findLoginTargetResource_multipleMatchingProducts_returnsAllMatching() {
    // Given
    String installationId = "test-installation";
    ObjectId sharedResourceId = new ObjectId();
    String resourceId = sharedResourceId.toHexString();
    ObjectId orgId = new ObjectId();

    InstalledProduct matchingProduct1 =
        InstalledProduct.builder()
            .vercelResourceId(sharedResourceId)
            .projectId(new ObjectId())
            .clusterName("cluster1")
            .build();

    InstalledProduct matchingProduct2 =
        InstalledProduct.builder()
            .vercelResourceId(sharedResourceId)
            .projectId(new ObjectId())
            .clusterName("cluster2")
            .build();

    InstalledProduct nonMatchingProduct =
        InstalledProduct.builder()
            .vercelResourceId(new ObjectId())
            .projectId(new ObjectId())
            .clusterName("other-cluster")
            .build();

    AtlasResources originalResources =
        AtlasResources.builder()
            .organizationId(orgId)
            .installedProducts(List.of(matchingProduct1, matchingProduct2, nonMatchingProduct))
            .build();

    VercelNativeInstallation installation = mock(VercelNativeInstallation.class);

    when(installationsSvc.findInstallation(installationId)).thenReturn(Optional.of(installation));
    when(installation.atlasResources()).thenReturn(originalResources);

    // When
    Optional<AtlasResources> result =
        vercelNativeAccountSvcImpl.findLoginTargetResource(installationId, resourceId);

    // Then
    assertTrue(result.isPresent());
    AtlasResources filteredResources = result.get();
    assertEquals(orgId, filteredResources.organizationId());
    assertEquals(2, filteredResources.installedProducts().size());
    assertTrue(filteredResources.installedProducts().contains(matchingProduct1));
    assertTrue(filteredResources.installedProducts().contains(matchingProduct2));
    assertFalse(filteredResources.installedProducts().contains(nonMatchingProduct));
  }

  @Test
  public void findLoginTargetResource_emptyInstalledProducts_returnsEmptyProducts() {
    // Given
    String installationId = "test-installation";
    String resourceId = new ObjectId().toHexString();
    ObjectId orgId = new ObjectId();

    AtlasResources originalResources =
        AtlasResources.builder().organizationId(orgId).installedProducts(List.of()).build();

    VercelNativeInstallation installation = mock(VercelNativeInstallation.class);

    when(installationsSvc.findInstallation(installationId)).thenReturn(Optional.of(installation));
    when(installation.atlasResources()).thenReturn(originalResources);

    // When
    Optional<AtlasResources> result =
        vercelNativeAccountSvcImpl.findLoginTargetResource(installationId, resourceId);

    // Then
    assertTrue(result.isPresent());
    AtlasResources filteredResources = result.get();
    assertEquals(orgId, filteredResources.organizationId());
    assertTrue(filteredResources.installedProducts().isEmpty());
  }

  // ===== Tests for createPartnerIdentityMappingFromTentativeLink method =====

  @Test
  public void
      createPartnerIdentityMappingFromTentativeLink_withValidTentativeLink_successfullyCreatesMapping()
          throws SvcException {
    // Given
    TentativePartnerAccountLink tentativeLink = createTestTentativeLink();
    AppUser mockUser = mock(AppUser.class);

    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.of(mockUser));
    when(mockUser.getPartnerIntegrationsData()).thenReturn(null);
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL)).thenReturn(Optional.empty());

    // When
    vercelNativeAccountSvcImpl.createPartnerIdentityMappingFromTentativeLink(tentativeLink);

    // Then
    verify(userSvc).findByUsernameOpt(USER_EMAIL);
    verify(userSvc)
        .setPartnerIntegrationsData(
            mockUser.getId(), new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false));
    verify(partnerIdentityClient).getMappingByEmail(USER_EMAIL);
    verify(partnerIdentityClient).createMapping(eq(USER_EMAIL), partnerIdentityCaptor.capture());

    List<PartnerIdentity> capturedIdentities = partnerIdentityCaptor.getValue();
    assertEquals(1, capturedIdentities.size());
    assertEquals(INSTALLATION_ID, capturedIdentities.get(0).getIntegrationId());
    assertEquals(USER_ID, capturedIdentities.get(0).getExternalId());
  }

  @Test
  public void createPartnerIdentityMappingFromTentativeLink_withExistingMapping_updatesMapping()
      throws SvcException {
    // Given
    TentativePartnerAccountLink tentativeLink = createTestTentativeLink();
    AppUser mockUser = mock(AppUser.class);

    PartnerIdentity existingIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID_2)
            .setExternalId(USER_ID_2)
            .build();
    PartnerIdentityMapping existingMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(existingIdentity)
            .build();

    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.of(mockUser));
    when(mockUser.getPartnerIntegrationsData())
        .thenReturn(new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false));
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL))
        .thenReturn(Optional.of(existingMapping));

    // When
    vercelNativeAccountSvcImpl.createPartnerIdentityMappingFromTentativeLink(tentativeLink);

    // Then
    verify(userSvc).findByUsernameOpt(USER_EMAIL);
    verify(userSvc, never()).setPartnerIntegrationsData(any(), any());
    verify(partnerIdentityClient).getMappingByEmail(USER_EMAIL);
    verify(partnerIdentityClient).updateMapping(partnerIdentityMappingCaptor.capture());

    PartnerIdentityMapping capturedMapping = partnerIdentityMappingCaptor.getValue();
    assertEquals(USER_EMAIL, capturedMapping.getEmail());
    assertEquals(2, capturedMapping.getIdentitiesList().size());

    // Verify both identities are present
    List<PartnerIdentity> identities = capturedMapping.getIdentitiesList();
    assertTrue(identities.contains(existingIdentity));
    assertTrue(
        identities.stream()
            .anyMatch(
                identity ->
                    INSTALLATION_ID.equals(identity.getIntegrationId())
                        && USER_ID.equals(identity.getExternalId())));
  }

  @Test
  public void createPartnerIdentityMappingFromTentativeLink_withDuplicateIdentity_skipsUpdate()
      throws SvcException {
    // Given
    TentativePartnerAccountLink tentativeLink = createTestTentativeLink();
    AppUser mockUser = mock(AppUser.class);

    PartnerIdentity duplicateIdentity =
        PartnerIdentity.newBuilder()
            .setIntegrationId(INSTALLATION_ID)
            .setExternalId(USER_ID)
            .build();
    PartnerIdentityMapping existingMapping =
        PartnerIdentityMapping.newBuilder()
            .setEmail(USER_EMAIL)
            .addIdentities(duplicateIdentity)
            .build();

    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.of(mockUser));
    when(mockUser.getPartnerIntegrationsData())
        .thenReturn(new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false));
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL))
        .thenReturn(Optional.of(existingMapping));

    // When
    vercelNativeAccountSvcImpl.createPartnerIdentityMappingFromTentativeLink(tentativeLink);

    // Then
    verify(userSvc).findByUsernameOpt(USER_EMAIL);
    verify(userSvc, never()).setPartnerIntegrationsData(any(), any());
    verify(partnerIdentityClient).getMappingByEmail(USER_EMAIL);
    verify(partnerIdentityClient, never()).updateMapping(any());
    verify(partnerIdentityClient, never()).createMapping(anyString(), any());
  }

  @Test
  public void createPartnerIdentityMappingFromTentativeLink_whenUserNotFound_throwsServerError()
      throws SvcException {
    // Given
    TentativePartnerAccountLink tentativeLink = createTestTentativeLink();

    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.empty());

    // When & Then
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                vercelNativeAccountSvcImpl.createPartnerIdentityMappingFromTentativeLink(
                    tentativeLink));

    assertEquals(CommonErrorCode.SERVER_ERROR, exception.getErrorCode());
    assertEquals("We're sorry, an unexpected error has occurred", exception.getMessage());
    verify(userSvc).findByUsernameOpt(USER_EMAIL);
    verify(partnerIdentityClient, never()).getMappingByEmail(anyString());
    verify(partnerIdentityClient, never()).createMapping(anyString(), any());
    verify(partnerIdentityClient, never()).updateMapping(any());
  }

  @Test
  public void
      createPartnerIdentityMappingFromTentativeLink_whenPartnerIdentityClientThrowsException_throwsServerError()
          throws SvcException {
    // Given
    TentativePartnerAccountLink tentativeLink = createTestTentativeLink();
    AppUser mockUser = mock(AppUser.class);
    AuthnServiceException authnException =
        new AuthnServiceException(
            com.xgen.cloud.common.authn._public.exception.AuthnServiceErrorCode.UNEXPECTED_ERROR);

    when(userSvc.findByUsernameOpt(USER_EMAIL)).thenReturn(Optional.of(mockUser));
    when(mockUser.getPartnerIntegrationsData())
        .thenReturn(new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false));
    when(partnerIdentityClient.getMappingByEmail(USER_EMAIL)).thenThrow(authnException);

    // When & Then
    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                vercelNativeAccountSvcImpl.createPartnerIdentityMappingFromTentativeLink(
                    tentativeLink));

    assertEquals(CommonErrorCode.SERVER_ERROR, exception.getErrorCode());
    assertEquals(authnException, exception.getCause());
    verify(userSvc).findByUsernameOpt(USER_EMAIL);
    verify(partnerIdentityClient).getMappingByEmail(USER_EMAIL);
  }

  // ===== Tests for confirmVercelLink method =====

  @Test
  public void test_confirmVercelLink_withValidEmailAndSessionId_confirmsLink() throws SvcException {
    // Given
    String email = "<EMAIL>";
    String sessionId = "valid-session-id";
    TentativePartnerAccountLink mockLink = mock(TentativePartnerAccountLink.class);
    when(mockLink.email()).thenReturn(email);
    when(mockLink.sessionId()).thenReturn(sessionId);

    when(tentativePartnerAccountLinkSvc.getTentativeLinkBySessionId(sessionId))
        .thenReturn(Optional.of(mockLink));

    // When
    vercelNativeAccountSvcImpl.confirmVercelLink(email, sessionId);

    // Then
    verify(tentativePartnerAccountLinkSvc).getTentativeLinkBySessionId(sessionId);
    verify(tentativePartnerAccountLinkSvc).confirmTentativeLink(sessionId);
  }

  @Test
  public void test_confirmVercelLink_withNonExistentSessionId_throwsBadRequestException()
      throws SvcException {
    // Given
    String email = "<EMAIL>";
    String sessionId = "non-existent-session-id";

    when(tentativePartnerAccountLinkSvc.getTentativeLinkBySessionId(sessionId))
        .thenReturn(Optional.empty());

    // When & Then
    SvcException exception =
        assertThrows(
            SvcException.class,
            () -> vercelNativeAccountSvcImpl.confirmVercelLink(email, sessionId));

    assertEquals(CommonErrorCode.BAD_REQUEST, exception.getErrorCode());
    verify(tentativePartnerAccountLinkSvc).getTentativeLinkBySessionId(sessionId);
  }

  @Test
  public void test_confirmVercelLink_withMismatchedEmail_throwsBadRequestException()
      throws SvcException {
    // Given
    String requestEmail = "<EMAIL>";
    String linkEmail = "<EMAIL>";
    String sessionId = "valid-session-id";
    TentativePartnerAccountLink mockLink = mock(TentativePartnerAccountLink.class);
    when(mockLink.email()).thenReturn(linkEmail);

    when(tentativePartnerAccountLinkSvc.getTentativeLinkBySessionId(sessionId))
        .thenReturn(Optional.of(mockLink));

    // When & Then
    SvcException exception =
        assertThrows(
            SvcException.class,
            () -> vercelNativeAccountSvcImpl.confirmVercelLink(requestEmail, sessionId));

    assertEquals(CommonErrorCode.BAD_REQUEST, exception.getErrorCode());
    verify(tentativePartnerAccountLinkSvc).getTentativeLinkBySessionId(sessionId);
  }

  @Test
  public void test_confirmVercelLink_withEmptyEmail_throwsBadRequestException()
      throws SvcException {
    // Given
    String email = "";
    String sessionId = "valid-session-id";
    TentativePartnerAccountLink mockLink = mock(TentativePartnerAccountLink.class);
    when(mockLink.email()).thenReturn("<EMAIL>");

    when(tentativePartnerAccountLinkSvc.getTentativeLinkBySessionId(sessionId))
        .thenReturn(Optional.of(mockLink));

    // When & Then
    SvcException exception =
        assertThrows(
            SvcException.class,
            () -> vercelNativeAccountSvcImpl.confirmVercelLink(email, sessionId));

    assertEquals(CommonErrorCode.BAD_REQUEST, exception.getErrorCode());
    verify(tentativePartnerAccountLinkSvc).getTentativeLinkBySessionId(sessionId);
  }

  @Test
  public void test_confirmVercelLink_withNullSessionId_throwsBadRequestException()
      throws SvcException {
    // Given
    String email = "<EMAIL>";
    String sessionId = null;

    when(tentativePartnerAccountLinkSvc.getTentativeLinkBySessionId(sessionId))
        .thenReturn(Optional.empty());

    // When & Then
    SvcException exception =
        assertThrows(
            SvcException.class,
            () -> vercelNativeAccountSvcImpl.confirmVercelLink(email, sessionId));

    assertEquals(CommonErrorCode.BAD_REQUEST, exception.getErrorCode());
    verify(tentativePartnerAccountLinkSvc).getTentativeLinkBySessionId(sessionId);
  }

  @Test
  public void test_confirmVercelLink_whenConfirmTentativeLinkThrowsException_propagatesException()
      throws SvcException {
    // Given
    String email = "<EMAIL>";
    String sessionId = "valid-session-id";
    TentativePartnerAccountLink mockLink = mock(TentativePartnerAccountLink.class);
    when(mockLink.email()).thenReturn(email);
    when(mockLink.sessionId()).thenReturn(sessionId);
    SvcException expectedException =
        new SvcException(CommonErrorCode.SERVER_ERROR, "Database error");

    when(tentativePartnerAccountLinkSvc.getTentativeLinkBySessionId(sessionId))
        .thenReturn(Optional.of(mockLink));
    when(tentativePartnerAccountLinkSvc.confirmTentativeLink(sessionId))
        .thenThrow(expectedException);

    // When & Then
    SvcException exception =
        assertThrows(
            SvcException.class,
            () -> vercelNativeAccountSvcImpl.confirmVercelLink(email, sessionId));

    assertEquals(expectedException, exception);
    verify(tentativePartnerAccountLinkSvc).getTentativeLinkBySessionId(sessionId);
    verify(tentativePartnerAccountLinkSvc).confirmTentativeLink(sessionId);
  }
}
