load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*UnitTests.java"]),
    deny_warnings = False,
    deps = [
        "//server/src/main/com/xgen/cloud/billingplatform/model/plan",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/metrics",
        "//server/src/main/com/xgen/cloud/configsdk/_public/wrapper",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/services/config/_public/proto:java_grpc",
        "//server/src/main/com/xgen/cloud/services/config/_public/util",
        "//server/src/main/com/xgen/cloud/util/_public/featureflag",
        "@maven//:com_fasterxml_jackson_dataformat_jackson_dataformat_yaml",
        "@maven//:com_xgen_devtools_configservicesdk",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)
