package com.xgen.cloud.billingplatform.model.sku._public.model;

import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.FLEX_AWS_LEGACY_100_USAGE_HOURS;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.FLEX_AZURE_LEGACY_100_USAGE_HOURS;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.FLEX_AZURE_LEGACY_300_USAGE_HOURS;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.FLEX_GCP_LEGACY_500_USAGE_HOURS;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_EXTENDED_IOPS;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_CMK_PRIVATE_NETWORKING;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_COMPRESSED_OBJECT_STORAGE;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTER_CONTINENT;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTRA_CONTINENT;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_EXTENDED_STANDARD_IOPS;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_OBJECT_STORAGE;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_OBJECT_STORAGE_ACCESS;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_EXTENDED_IOPS;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_AZURE_STANDARD_STORAGE;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_GCP_OBJECT_STORAGE;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.NDS_GCP_OBJECT_STORAGE_ACCESS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.AWS_SECRET_MANAGER_DISABLED_PROPERTY;
import static com.xgen.cloud.nds.aws._public.model.AWSRegionName.AP_SOUTHEAST_1;
import static com.xgen.cloud.nds.aws._public.model.AWSRegionName.CA_CENTRAL_1;
import static com.xgen.cloud.nds.aws._public.model.AWSRegionName.EU_WEST_1;
import static com.xgen.cloud.nds.aws._public.model.AWSRegionName.US_EAST_1;
import static com.xgen.cloud.nds.aws._public.model.AWSRegionName.US_EAST_2;
import static com.xgen.cloud.nds.aws._public.model.AWSRegionName.US_WEST_2;
import static java.lang.Boolean.TRUE;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.xgen.cloud.billingplatform.common._public.model.ProductCode;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.util._public.util.classpath.ClasspathUtils;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureNDSDefaults;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.module.metering.common.model.MeterId;
import com.xgen.svc.mms.model.billing.PlanTypeSvc;
import com.xgen.svc.mms.svc.billing.SKUSvc;
import com.xgen.svc.mms.svc.marketing.SalesSoldDealActivationSvc;
import com.xgen.testlib.util.appsettings.AppSettingsUtil;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.bson.types.ObjectId;
import org.hamcrest.MatcherAssert;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.JSONTokener;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

@SuppressWarnings("deprecation")
public class SKUUnitTests {

  private static final Date TODAY = new Date();
  private static final double EPSILON = 0.0000001d;
  private static final Set<AWSRegionName> NDS_AWS_OBJECT_STORAGE_UNAVAILABLE_REGIONS =
      Set.of(
          AWSRegionName.US_GOV_EAST_1,
          AWSRegionName.US_GOV_WEST_1,
          AWSRegionName.CN_NORTH_1,
          AWSRegionName.CN_NORTHWEST_1,
          AWSRegionName.AP_SOUTHEAST_3,
          AWSRegionName.AP_SOUTHEAST_4,
          AWSRegionName.EU_CENTRAL_2,
          AWSRegionName.EU_SOUTH_2,
          AWSRegionName.ME_CENTRAL_1,
          AWSRegionName.AP_SOUTH_2,
          AWSRegionName.IL_CENTRAL_1,
          AWSRegionName.CA_WEST_1,
          AWSRegionName.AP_SOUTHEAST_5,
          AWSRegionName.AP_SOUTHEAST_7,
          AWSRegionName.MX_CENTRAL_1);
  private static final Set<AWSRegionName> NDS_AWS_SERVERLESS_AVAILABLE_REGIONS =
      Set.of(
          AWSRegionName.US_EAST_1,
          AWSRegionName.AF_SOUTH_1,
          AWSRegionName.AP_EAST_1,
          AWSRegionName.AP_NORTHEAST_1,
          AWSRegionName.AP_NORTHEAST_2,
          AWSRegionName.AP_NORTHEAST_3,
          AWSRegionName.AP_SOUTH_1,
          AWSRegionName.AP_SOUTHEAST_1,
          AWSRegionName.AP_SOUTHEAST_2,
          AWSRegionName.CA_CENTRAL_1,
          AWSRegionName.EU_CENTRAL_1,
          AWSRegionName.EU_NORTH_1,
          AWSRegionName.EU_SOUTH_1,
          AWSRegionName.EU_WEST_1,
          AWSRegionName.EU_WEST_2,
          AWSRegionName.EU_WEST_3,
          AWSRegionName.ME_SOUTH_1,
          AWSRegionName.SA_EAST_1,
          AWSRegionName.US_EAST_2,
          AWSRegionName.US_GOV_EAST_1,
          AWSRegionName.US_GOV_WEST_1,
          AWSRegionName.US_WEST_1,
          AWSRegionName.US_WEST_2);
  private static final Set<AWSRegionName> NDS_AWS_SEARCH_UNAVAILABLE_REGIONS =
      Set.of(
          AWSRegionName.AP_SOUTHEAST_5, AWSRegionName.AP_SOUTHEAST_7, AWSRegionName.MX_CENTRAL_1);

  private static final Set<GCPRegionName> NDS_GCP_SERVERLESS_AVAILABLE_REGIONS =
      Set.of(
          GCPRegionName.EASTERN_ASIA_PACIFIC,
          GCPRegionName.ASIA_EAST_2,
          GCPRegionName.NORTHEASTERN_ASIA_PACIFIC,
          GCPRegionName.ASIA_NORTHEAST_2,
          GCPRegionName.ASIA_NORTHEAST_3,
          GCPRegionName.ASIA_SOUTH_1,
          GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC,
          GCPRegionName.ASIA_SOUTHEAST_2,
          GCPRegionName.AUSTRALIA_SOUTHEAST_1,
          GCPRegionName.EUROPE_NORTH_1,
          GCPRegionName.WESTERN_EUROPE,
          GCPRegionName.EUROPE_WEST_2,
          GCPRegionName.EUROPE_WEST_3,
          GCPRegionName.EUROPE_WEST_4,
          GCPRegionName.EUROPE_WEST_6,
          GCPRegionName.NORTH_AMERICA_NORTHEAST_1,
          GCPRegionName.SOUTH_AMERICA_EAST_1,
          GCPRegionName.CENTRAL_US,
          GCPRegionName.EASTERN_US,
          GCPRegionName.US_EAST_4,
          GCPRegionName.WESTERN_US,
          GCPRegionName.US_WEST_2,
          GCPRegionName.US_WEST_3,
          GCPRegionName.US_WEST_4);
  private static final Set<AzureRegionName> NDS_AZURE_SERVERLESS_AVAILABLE_REGIONS =
      Set.of(
          AzureRegionName.AUSTRALIA_CENTRAL,
          AzureRegionName.AUSTRALIA_CENTRAL_2,
          AzureRegionName.AUSTRALIA_EAST,
          AzureRegionName.AUSTRALIA_SOUTH_EAST,
          AzureRegionName.BRAZIL_SOUTH,
          AzureRegionName.BRAZIL_SOUTHEAST,
          AzureRegionName.CANADA_CENTRAL,
          AzureRegionName.CANADA_EAST,
          AzureRegionName.INDIA_CENTRAL,
          AzureRegionName.US_CENTRAL,
          AzureRegionName.ASIA_EAST,
          AzureRegionName.US_EAST,
          AzureRegionName.US_EAST_2,
          AzureRegionName.FRANCE_CENTRAL,
          AzureRegionName.FRANCE_SOUTH,
          AzureRegionName.GERMANY_NORTH,
          AzureRegionName.GERMANY_WEST_CENTRAL,
          AzureRegionName.JAPAN_EAST,
          AzureRegionName.JAPAN_WEST,
          AzureRegionName.KOREA_CENTRAL,
          AzureRegionName.KOREA_SOUTH,
          AzureRegionName.US_NORTH_CENTRAL,
          AzureRegionName.EUROPE_NORTH,
          AzureRegionName.NORWAY_EAST,
          AzureRegionName.NORWAY_WEST,
          AzureRegionName.SOUTH_AFRICA_NORTH,
          AzureRegionName.SOUTH_AFRICA_WEST,
          AzureRegionName.US_SOUTH_CENTRAL,
          AzureRegionName.ASIA_SOUTH_EAST,
          AzureRegionName.INDIA_SOUTH,
          AzureRegionName.SWITZERLAND_NORTH,
          AzureRegionName.SWITZERLAND_WEST,
          AzureRegionName.UAE_CENTRAL,
          AzureRegionName.UAE_NORTH,
          AzureRegionName.UK_SOUTH,
          AzureRegionName.UK_WEST,
          AzureRegionName.US_WEST_CENTRAL,
          AzureRegionName.EUROPE_WEST,
          AzureRegionName.INDIA_WEST,
          AzureRegionName.US_WEST,
          AzureRegionName.US_WEST_2,
          AzureRegionName.US_WEST_3);
  private static final Set<AzureRegionName> DATA_LAKE_AZURE_AVAILABLE_REGIONS =
      Set.of(
          AzureRegionName.US_EAST,
          AzureRegionName.US_EAST_2,
          AzureRegionName.EUROPE_NORTH,
          AzureRegionName.EUROPE_WEST,
          AzureRegionName.AUSTRALIA_EAST,
          AzureRegionName.BRAZIL_SOUTH);

  private static final Set<AzureRegionName> AZURE_CMK_PRIVATE_NETWORKING_UNAVAILABLE_REGIONS =
      Set.of(AzureRegionName.US_EAST_2_EUAP);

  private static final Set<AWSRegionName> AWS_CMK_PRIVATE_NETWORKING_UNAVAILABLE_REGIONS =
      Set.of(
          AWSRegionName.US_GOV_WEST_1,
          AWSRegionName.US_GOV_EAST_1,
          AWSRegionName.CN_NORTH_1,
          AWSRegionName.CN_NORTHWEST_1);

  private static final Set<GCPRegionName> SEARCH_GCP_UNAVAILABLE_REGIONS =
      Set.of(GCPRegionName.EUROPE_WEST_10, GCPRegionName.MIDDLE_EAST_CENTRAL_2);

  private static final Set<AzureRegionName> SEARCH_AZURE_UNAVAILABLE_REGIONS =
      Set.of(
          AzureRegionName.ITALY_NORTH,
          AzureRegionName.SPAIN_CENTRAL,
          AzureRegionName.MEXICO_CENTRAL,
          AzureRegionName.NEW_ZEALAND_NORTH);
  private static final Set<AzureRegionName> SEARCH_AZURE_UNAVAILABLE_REGIONS_MEMORY_LOCALSSD =
      Set.of(
          AzureRegionName.SPAIN_CENTRAL,
          AzureRegionName.MEXICO_CENTRAL,
          AzureRegionName.NEW_ZEALAND_NORTH);
  private static final String TEST_PRICE_CHANGE_SUFFIX = "TEST_PRICE_CHANGE";

  @BeforeEach
  public void setUp() throws Exception {
    System.setProperty(AWS_SECRET_MANAGER_DISABLED_PROPERTY, TRUE.toString());
  }

  @AfterEach
  public void tearDown() throws Exception {
    System.clearProperty(AWS_SECRET_MANAGER_DISABLED_PROPERTY);
  }

  private static final Map<SKU, Set<GCPRegionName>> GCP_SET_PRICES_FOR_STORAGE_SKUS =
      Map.of(
          SKU.NDS_GCP_DATA_LAKE_STORAGE_ACCESS,
          Set.of(GCPRegionName.CENTRAL_US, GCPRegionName.WESTERN_EUROPE),
          SKU.NDS_GCP_OBJECT_STORAGE_ACCESS,
          Set.of(GCPRegionName.CENTRAL_US, GCPRegionName.WESTERN_EUROPE),
          SKU.NDS_GCP_DATA_LAKE_STORAGE,
          Set.of(GCPRegionName.CENTRAL_US, GCPRegionName.WESTERN_EUROPE),
          SKU.NDS_GCP_OBJECT_STORAGE,
          Set.of(GCPRegionName.CENTRAL_US, GCPRegionName.WESTERN_EUROPE));

  @SuppressWarnings("ALL")
  @Test
  public void cloudProviderImportedSkuSet() {
    Set<SKU> expectedSkus =
        Set.of(
            SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION,
            SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION,
            SKU.NDS_AWS_DATA_TRANSFER_INTERNET,
            SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE,
            SKU.NDS_AWS_PIT_RESTORE_STORAGE_FREE_TIER,
            SKU.NDS_AWS_PIT_RESTORE_STORAGE,
            SKU.NDS_AWS_PRIVATE_ENDPOINT,
            SKU.NDS_AWS_PRIVATE_ENDPOINT_CAPACITY_UNITS,
            SKU.NDS_GCP_DATA_TRANSFER_INTERNET,
            SKU.NDS_GCP_DATA_TRANSFER_INTER_CONNECT,
            SKU.NDS_GCP_DATA_TRANSFER_INTER_ZONE,
            SKU.NDS_GCP_DATA_TRANSFER_INTER_REGION,
            SKU.NDS_GCP_DATA_TRANSFER_GOOGLE,
            SKU.GCP_SNAPSHOT_COPY_DATA_TRANSFER,
            SKU.NDS_GCP_PRIVATE_ENDPOINT,
            SKU.NDS_GCP_PRIVATE_ENDPOINT_CAPACITY_UNITS,
            SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE,
            SKU.NDS_GCP_PIT_RESTORE_STORAGE,
            SKU.NDS_GCP_PIT_RESTORE_STORAGE_FREE_TIER,
            SKU.NDS_AZURE_DATA_TRANSFER,
            SKU.NDS_AZURE_DATA_TRANSFER_REGIONAL_VNET_IN,
            SKU.NDS_AZURE_DATA_TRANSFER_REGIONAL_VNET_OUT,
            SKU.NDS_AZURE_DATA_TRANSFER_GLOBAL_VNET_IN,
            SKU.NDS_AZURE_DATA_TRANSFER_GLOBAL_VNET_OUT,
            SKU.NDS_AZURE_DATA_TRANSFER_AVAILABILITY_ZONE_IN,
            SKU.NDS_AZURE_DATA_TRANSFER_AVAILABILITY_ZONE_OUT,
            NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTER_CONTINENT,
            NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTRA_CONTINENT,
            SKU.NDS_AZURE_BACKUP_SNAPSHOT_STORAGE,
            SKU.NDS_AZURE_PIT_RESTORE_STORAGE,
            SKU.NDS_AZURE_PIT_RESTORE_STORAGE_FREE_TIER,
            SKU.NDS_AZURE_PRIVATE_ENDPOINT_CAPACITY_UNITS);
    assertEquals(expectedSkus, SKU.CLOUD_PROVIDER_IMPORT_SKUS);
  }

  @Test
  public void testTaxCodesAreAssignedCorrectly() {
    // Each SKU needs to be assigned the tax codes for both MongoDB, Inc. and MongoDB, Ltd if
    // they are assigned.  If a SKU is not assigned tax codes, the tax codes must be both null.
    for (SKU sku : SKU.values()) {
      if (sku.getInfo().getTaxCodeInc() == null) {
        assertNull(
            sku.getInfo().getTaxCodeLtd(),
            "TaxCode for MongoDB, Ltd. must not be assigned for " + sku);
      } else {
        assertNotNull(
            sku.getInfo().getTaxCodeLtd(), "TaxCode for MongoDB, Ltd. must be assigned for " + sku);
      }
    }
  }

  @Test
  public void testBackupSKUHasNoPlanType() {
    for (SKU sku : SKU.ALL_BACKUP_SKUS) {
      assertFalse(sku.getInfo().hasSummaryGroup());
    }
  }

  @Test
  public void testValidPricing() {
    AppSettings settings = AppSettingsUtil.createAppSettingsWithMockedConfigServiceSdk();

    for (SKU sku : SKU.values()) {
      SKUInfo info = sku.getInfo();
      int priceTypes = 0;

      boolean isAwsStorageAccess =
          List.of(
                  SKU.NDS_AWS_OBJECT_STORAGE_ACCESS,
                  SKU.NDS_AWS_OBJECT_STORAGE_SEEK,
                  SKU.NDS_AWS_DATA_LAKE_STORAGE_ACCESS)
              .contains(sku);

      if (info.getPricingStrategyType() == PricingStrategyType.AWS_REGION) {
        for (AWSRegionName region : AWSNDSDefaults.ALL_AVAILABLE_REGIONS) {

          SkuPricing pricing = info.getPricingForDate(new Date());
          if (!pricing.hasRegionUnitPriceDollars(region)) {
            continue;
          }

          try {
            info.getRegionUnitPriceDollars(region, new Date());
            // The underlying instance (m4.10xlarge) for M100 is not available in GovCloud East.
            // So we can't actually price it nor can we deliver it in that region.
            assertFalse(sku == SKU.NDS_AWS_INSTANCE_M100 && region == AWSRegionName.US_GOV_EAST_1);
            // These are not available in Atlas Government or Atlas China yet because Data Lake is
            // not available.
            assertFalse(
                sku == SKU.NDS_AWS_OBJECT_STORAGE
                    && NDS_AWS_OBJECT_STORAGE_UNAVAILABLE_REGIONS.contains(region));
            assertFalse(
                isAwsStorageAccess && NDS_AWS_OBJECT_STORAGE_UNAVAILABLE_REGIONS.contains(region));
            assertTrue(
                sku.getInfo().getSkuService() != SkuService.SERVERLESS_INSTANCES
                    || NDS_AWS_SERVERLESS_AVAILABLE_REGIONS.contains(region),
                sku + " SKU must have prices set for " + region + " region");
          } catch (IllegalStateException ise) {
            if ((sku != SKU.NDS_AWS_INSTANCE_M100 || region != AWSRegionName.US_GOV_EAST_1)
                    && ((sku != SKU.NDS_AWS_OBJECT_STORAGE
                            && sku != SKU.NDS_AWS_OBJECT_STORAGE_V3
                            && sku != SKU.NDS_AWS_COMPRESSED_OBJECT_STORAGE)
                        || !NDS_AWS_OBJECT_STORAGE_UNAVAILABLE_REGIONS.contains(region))
                    && (sku != SKU.NDS_AWS_DATA_LAKE_STORAGE
                        || !NDS_AWS_OBJECT_STORAGE_UNAVAILABLE_REGIONS.contains(region))
                    && (!isAwsStorageAccess
                        || !NDS_AWS_OBJECT_STORAGE_UNAVAILABLE_REGIONS.contains(region))
                // Serverless don't have prices set for unavailable regions and it's okay.
                || (sku.getInfo().getSkuService() == SkuService.SERVERLESS_INSTANCES
                        && !NDS_AWS_SERVERLESS_AVAILABLE_REGIONS.contains(region))
                    // Search instances doesn't have prices for some regions and it's okay
                    && (sku.getInfo().getSummaryGroup() != SummaryGroup.SEARCH
                        || !sku.name().contains("NDS_AWS_SEARCH_INSTANCE")
                        || !NDS_AWS_SEARCH_UNAVAILABLE_REGIONS.contains(region))
                    // streaming skus are only in preview so they only have a few pr
                    && (sku != SKU.NDS_AWS_CMK_PRIVATE_NETWORKING
                        || !AWS_CMK_PRIVATE_NETWORKING_UNAVAILABLE_REGIONS.contains(region))) {
              fail(ise.getMessage());
            }
          }
        }
        priceTypes++;
      }
      if (info.getPricingStrategyType() == PricingStrategyType.GCP_REGION) {
        for (GCPRegionName region : GCPNDSDefaults.AVAILABLE_REGIONS) {
          try {

            if ((sku.getInfo().getSummaryGroup() == SummaryGroup.SEARCH
                    && sku.name().contains("NDS_GCP_SEARCH_INSTANCE"))
                && SEARCH_GCP_UNAVAILABLE_REGIONS.contains(region)) {
              continue;
            }
            Set<GCPRegionName> setRegionsForSku =
                GCP_SET_PRICES_FOR_STORAGE_SKUS.getOrDefault(sku, Set.of());
            if (!setRegionsForSku.contains(region)) {
              continue;
            }
            info.getRegionUnitPriceDollars(region, new Date());
            assertTrue(
                sku.getInfo().getSkuService() != SkuService.SERVERLESS_INSTANCES
                    || NDS_GCP_SERVERLESS_AVAILABLE_REGIONS.contains(region),
                sku + " SKU must have prices set for " + region + " region");
          } catch (IllegalStateException ise) {
            if ((sku.getInfo().getSkuService() != SkuService.SERVERLESS_INSTANCES
                || NDS_GCP_SERVERLESS_AVAILABLE_REGIONS.contains(region))) {
              fail(ise.getMessage());
            }
          }
        }
        priceTypes++;
      }
      if (info.getPricingStrategyType() == PricingStrategyType.AZURE_REGION) {
        for (AzureRegionName region : AzureNDSDefaults.AVAILABLE_REGIONS) {
          if (sku.getInfo().getSkuService() == SkuService.SERVERLESS_INSTANCES
              && !NDS_AZURE_SERVERLESS_AVAILABLE_REGIONS.contains(region)) {
            continue;
          }

          if ((sku.getInfo().getSummaryGroup() == SummaryGroup.SEARCH
                  && sku.name().contains("NDS_AZURE_SEARCH_INSTANCE")
                  && sku.name().contains("COMPUTE_LOCALSSD"))
              && SEARCH_AZURE_UNAVAILABLE_REGIONS.contains(region)) {
            continue;
          }

          if ((sku.getInfo().getSummaryGroup() == SummaryGroup.SEARCH
                  && sku.name().contains("NDS_AZURE_SEARCH_INSTANCE")
                  && sku.name().contains("MEMORY_LOCALSSD"))
              && SEARCH_AZURE_UNAVAILABLE_REGIONS_MEMORY_LOCALSSD.contains(region)) {
            continue;
          }

          if (((sku.getInfo().getSkuService() == SkuService.DATA_FEDERATION
                      && sku.name().contains("AZURE"))
                  || NDS_AZURE_OBJECT_STORAGE_ACCESS.equals(sku)
                  || NDS_AZURE_OBJECT_STORAGE.equals(sku)
                  || NDS_AZURE_COMPRESSED_OBJECT_STORAGE.equals(sku))
              && !DATA_LAKE_AZURE_AVAILABLE_REGIONS.contains(region)) {
            continue;
          }
          // streaming skus are only in preview so they only have a few pr
          if (sku.getInfo().getSkuService() == SkuService.STREAMS) {
            continue;
          }
          // check if region supports pv2 for azure ssd v2 skus
          boolean ssdV2region =
              settings.getAzureSsdV2Regions().contains(region.getValue())
                  || settings.getAzureSsdV2PreviewRegions().contains(region.getValue());
          if ((NDS_AZURE_EXTENDED_STANDARD_IOPS.equals(sku)
                  || NDS_AZURE_STANDARD_STORAGE.equals(sku)
                  || NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE.equals(sku)
                  || NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE.equals(sku)
                  || NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_EXTENDED_IOPS.equals(sku)
                  || NDS_AZURE_SNAPSHOT_EXPORT_VM_STORAGE_EXTENDED_IOPS.equals(sku))
              && !ssdV2region) {
            continue;
          }
          if (NDS_AZURE_CMK_PRIVATE_NETWORKING.equals(sku)
              && AZURE_CMK_PRIVATE_NETWORKING_UNAVAILABLE_REGIONS.contains(region)) {
            continue;
          }

          info.getRegionUnitPriceDollars(region, new Date());
        }
        priceTypes++;
      }
      if (info.getSKU() == SKU.MMS_BACKUP_STORAGE) {
        assertNotNull(info.getPricingForDate(new Date()).getFloatPriceDollars());
        priceTypes++;
      }
      assertTrue(priceTypes <= 1);
    }
  }

  @Test
  public void testAtlasAWSPricing() {

    // Check instance pricing
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.US_EAST_1, 0.026);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.US_EAST_2, 0.026);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.US_WEST_1, 0.031);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.US_WEST_2, 0.026);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.CA_CENTRAL_1, 0.026);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.EU_NORTH_1, 0.027);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.EU_WEST_1, 0.028);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.EU_WEST_2, 0.028);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.EU_WEST_3, 0.028);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.EU_CENTRAL_1, 0.028);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.EU_CENTRAL_2, 0.035);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AP_EAST_1, 0.034);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AP_NORTHEAST_1, 0.032);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AP_NORTHEAST_2, 0.032);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AP_SOUTHEAST_1, 0.030);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AP_SOUTHEAST_2, 0.040);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AP_SOUTHEAST_3, 0.030);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AP_SOUTHEAST_4, 0.032);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AP_SOUTH_1, 0.027);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AP_SOUTH_2, 0.027);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.SA_EAST_1, 0.041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.ME_SOUTH_1, 0.029);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.ME_CENTRAL_1, 0.032);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.EU_SOUTH_1, 0.031);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.EU_SOUTH_2, 0.029);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AF_SOUTH_1, 0.035);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.IL_CENTRAL_1, 0.032);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.CA_WEST_1, 0.027);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AP_SOUTHEAST_5, 0.029);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.AP_SOUTHEAST_7, 0.029);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.MX_CENTRAL_1, 0.027);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.US_EAST_1, 0.067);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.US_EAST_2, 0.067);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.US_WEST_1, 0.079);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.US_WEST_2, 0.067);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.CA_CENTRAL_1, 0.067);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.EU_NORTH_1, 0.071);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.EU_WEST_1, 0.072);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.EU_WEST_2, 0.072);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.EU_WEST_3, 0.072);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.EU_CENTRAL_1, 0.072);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.EU_CENTRAL_2, 0.088);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AP_EAST_1, 0.087);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AP_NORTHEAST_1, 0.083);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AP_NORTHEAST_2, 0.083);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AP_SOUTHEAST_1, 0.077);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AP_SOUTHEAST_2, 0.103);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AP_SOUTHEAST_3, 0.077);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AP_SOUTHEAST_4, 0.082);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AP_SOUTH_1, 0.067);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AP_SOUTH_2, 0.067);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.SA_EAST_1, 0.105);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.ME_SOUTH_1, 0.074);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.ME_CENTRAL_1, 0.082);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.EU_SOUTH_1, 0.079);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.EU_SOUTH_2, 0.074);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AF_SOUTH_1, 0.090);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.IL_CENTRAL_1, 0.079);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.CA_WEST_1, 0.067);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AP_SOUTHEAST_5, 0.075);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.AP_SOUTHEAST_7, 0.075);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.MX_CENTRAL_1, 0.07);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.US_EAST_1, 0.177);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.US_EAST_2, 0.177);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.US_WEST_1, 0.186);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.US_WEST_2, 0.177);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.CA_CENTRAL_1, 0.177);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.EU_NORTH_1, 0.190);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.EU_WEST_1, 0.194);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.EU_WEST_2, 0.194);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.EU_WEST_3, 0.194);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.EU_CENTRAL_1, 0.194);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.EU_CENTRAL_2, 0.240);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AP_EAST_1, 0.218);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AP_NORTHEAST_1, 0.205);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AP_NORTHEAST_2, 0.194);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AP_SOUTHEAST_1, 0.197);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AP_SOUTHEAST_2, 0.247);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AP_SOUTHEAST_3, 0.198);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AP_SOUTHEAST_4, 0.202);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AP_SOUTH_1, 0.168);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AP_SOUTH_2, 0.168);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.SA_EAST_1, 0.252);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.ME_SOUTH_1, 0.196);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.ME_CENTRAL_1, 0.199);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.EU_SOUTH_1, 0.202);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.EU_SOUTH_2, 0.181);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AF_SOUTH_1, 0.231);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.IL_CENTRAL_1, 0.193);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.CA_WEST_1, 0.176);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AP_SOUTHEAST_5, 0.184);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.AP_SOUTHEAST_7, 0.184);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.MX_CENTRAL_1, 0.186);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.US_EAST_1, 0.341);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.US_EAST_2, 0.341);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.US_WEST_1, 0.358);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.US_WEST_2, 0.341);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.CA_CENTRAL_1, 0.341);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.EU_NORTH_1, 0.369);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.EU_WEST_1, 0.377);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.EU_WEST_2, 0.377);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.EU_WEST_3, 0.377);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.EU_CENTRAL_1, 0.377);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.EU_CENTRAL_2, 0.458);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AP_EAST_1, 0.421);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AP_NORTHEAST_1, 0.397);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AP_NORTHEAST_2, 0.378);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AP_SOUTHEAST_1, 0.380);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AP_SOUTHEAST_2, 0.480);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AP_SOUTHEAST_3, 0.382);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AP_SOUTHEAST_4, 0.386);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AP_SOUTH_1, 0.323);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AP_SOUTH_2, 0.323);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.SA_EAST_1, 0.490);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.ME_SOUTH_1, 0.379);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.ME_CENTRAL_1, 0.381);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.EU_SOUTH_1, 0.391);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.EU_SOUTH_2, 0.346);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AF_SOUTH_1, 0.443);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.IL_CENTRAL_1, 0.368);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.CA_WEST_1, 0.336);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AP_SOUTHEAST_5, 0.355);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.AP_SOUTHEAST_7, 0.355);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.MX_CENTRAL_1, 0.358);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.US_EAST_1, 0.656);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.US_EAST_2, 0.656);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.US_WEST_1, 0.689);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.US_WEST_2, 0.656);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.CA_CENTRAL_1, 0.656);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.EU_NORTH_1, 0.709);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.EU_WEST_1, 0.723);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.EU_WEST_2, 0.723);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.EU_WEST_3, 0.723);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.EU_CENTRAL_1, 0.723);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.EU_CENTRAL_2, 0.881);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AP_EAST_1, 0.809);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AP_NORTHEAST_1, 0.762);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AP_NORTHEAST_2, 0.723);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AP_SOUTHEAST_1, 0.730);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AP_SOUTHEAST_2, 0.922);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AP_SOUTHEAST_3, 0.734);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AP_SOUTHEAST_4, 0.743);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AP_SOUTH_1, 0.622);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AP_SOUTH_2, 0.622);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.SA_EAST_1, 0.938);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.ME_SOUTH_1, 0.728);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.ME_CENTRAL_1, 0.733);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.EU_SOUTH_1, 0.756);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.EU_SOUTH_2, 0.666);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AF_SOUTH_1, 0.857);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.IL_CENTRAL_1, 0.707);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.CA_WEST_1, 0.645);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AP_SOUTHEAST_5, 0.683);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.AP_SOUTHEAST_7, 0.683);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.MX_CENTRAL_1, 0.689);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.US_EAST_1, 1.300);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.US_EAST_2, 1.300);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.US_WEST_1, 1.364);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.US_WEST_2, 1.300);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.CA_CENTRAL_1, 1.300);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.EU_NORTH_1, 1.404);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.EU_WEST_1, 1.433);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.EU_WEST_2, 1.433);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.EU_WEST_3, 1.433);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.EU_CENTRAL_1, 1.433);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.EU_CENTRAL_2, 1.746);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AP_EAST_1, 1.602);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AP_NORTHEAST_1, 1.510);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AP_NORTHEAST_2, 1.434);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AP_SOUTHEAST_1, 1.447);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AP_SOUTHEAST_2, 1.825);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AP_SOUTHEAST_3, 1.455);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AP_SOUTHEAST_4, 1.472);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AP_SOUTH_1, 1.231);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AP_SOUTH_2, 1.231);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.SA_EAST_1, 1.859);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.ME_SOUTH_1, 1.444);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.ME_CENTRAL_1, 1.452);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.EU_SOUTH_1, 1.497);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.EU_SOUTH_2, 1.320);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AF_SOUTH_1, 1.696);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.IL_CENTRAL_1, 1.402);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.CA_WEST_1, 1.278);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AP_SOUTHEAST_5, 1.354);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.AP_SOUTHEAST_7, 1.334);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.MX_CENTRAL_1, 1.365);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.US_EAST_1, 2.401);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.US_EAST_2, 2.401);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.US_WEST_1, 2.521);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.US_WEST_2, 2.401);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.CA_CENTRAL_1, 2.401);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.EU_NORTH_1, 2.596);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.EU_WEST_1, 2.649);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.EU_WEST_2, 2.649);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.EU_WEST_3, 2.649);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.EU_CENTRAL_1, 2.649);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.EU_CENTRAL_2, 3.233);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AP_EAST_1, 3.089);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AP_NORTHEAST_1, 2.791);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AP_NORTHEAST_2, 2.650);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AP_SOUTHEAST_1, 2.673);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AP_SOUTHEAST_2, 3.374);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AP_SOUTHEAST_3, 2.694);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AP_SOUTHEAST_4, 2.725);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AP_SOUTH_1, 2.277);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AP_SOUTH_2, 2.277);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.SA_EAST_1, 3.433);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.ME_SOUTH_1, 2.666);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.ME_CENTRAL_1, 2.688);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.EU_SOUTH_1, 2.763);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.EU_SOUTH_2, 2.444);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AF_SOUTH_1, 3.135);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.IL_CENTRAL_1, 2.597);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.CA_WEST_1, 2.367);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AP_SOUTHEAST_5, 2.5);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.AP_SOUTHEAST_7, 2.477);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.MX_CENTRAL_1, 2.522);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.US_EAST_1, 3.012);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.US_EAST_2, 3.012);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.US_WEST_1, 3.163);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.US_WEST_2, 3.012);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.CA_CENTRAL_1, 3.012);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.EU_WEST_1, 3.323);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.EU_WEST_2, 3.323);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.EU_CENTRAL_1, 3.323);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.AP_NORTHEAST_1, 3.501);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.AP_NORTHEAST_2, 3.324);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.AP_SOUTHEAST_1, 3.353);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.AP_SOUTHEAST_2, 4.232);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.AP_SOUTHEAST_3, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.AP_SOUTH_1, 2.858);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.SA_EAST_1, 4.310);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.IL_CENTRAL_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.CA_WEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.AP_SOUTHEAST_5, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.AP_SOUTHEAST_7, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.MX_CENTRAL_1, 0.0);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.US_EAST_1, 3.614);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.US_EAST_2, 3.614);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.US_WEST_1, 3.795);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.US_WEST_2, 3.614);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.CA_CENTRAL_1, 3.614);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.EU_WEST_1, 3.987);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.EU_WEST_2, 3.987);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.EU_CENTRAL_1, 3.987);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.EU_CENTRAL_2, 4.824);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AP_EAST_1, 4.457);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AP_NORTHEAST_1, 4.200);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AP_NORTHEAST_2, 3.988);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AP_SOUTHEAST_1, 4.023);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AP_SOUTHEAST_2, 5.077);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AP_SOUTHEAST_3, 4.408);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AP_SOUTHEAST_4, 4.067);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AP_SOUTH_1, 3.429);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AP_SOUTH_2, 3.429);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.SA_EAST_1, 4.531);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.EU_NORTH_1, 3.907);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.EU_WEST_3, 3.987);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.ME_SOUTH_1, 4.014);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.ME_CENTRAL_1, 4.012);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.EU_SOUTH_1, 4.157);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.EU_SOUTH_2, 3.647);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AF_SOUTH_1, 4.715);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.IL_CENTRAL_1, 3.879);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.CA_WEST_1, 3.533);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AP_SOUTHEAST_5, 3.764);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.AP_SOUTHEAST_7, 3.735);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.MX_CENTRAL_1, 3.795);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.US_EAST_1, 4.798);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.US_EAST_2, 4.798);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.US_WEST_1, 5.038);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.US_WEST_2, 4.798);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.CA_CENTRAL_1, 4.798);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.EU_WEST_1, 5.293);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.EU_WEST_2, 5.293);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.EU_WEST_3, 5.293);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.EU_CENTRAL_1, 5.293);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.EU_CENTRAL_2, 6.476);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.EU_NORTH_1, 5.187);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AP_EAST_1, 5.918);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AP_NORTHEAST_1, 5.578);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AP_NORTHEAST_2, 5.296);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AP_SOUTHEAST_1, 5.342);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AP_SOUTHEAST_2, 6.742);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AP_SOUTHEAST_3, 5.389);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AP_SOUTHEAST_4, 5.460);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AP_SOUTH_1, 4.550);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AP_SOUTH_2, 5.279);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.SA_EAST_1, 6.860);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.ME_SOUTH_1, 5.328);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.ME_CENTRAL_1, 5.386);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.EU_SOUTH_1, 5.528);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.EU_SOUTH_2, 4.897);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AF_SOUTH_1, 6.267);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.IL_CENTRAL_1, 5.205);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.CA_WEST_1, 4.743);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AP_SOUTHEAST_5, 4.996);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.AP_SOUTHEAST_7, 4.963);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.MX_CENTRAL_1, 5.039);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.US_EAST_1, 7.185);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.US_EAST_2, 7.185);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.US_WEST_1, 7.544);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.US_WEST_2, 7.185);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.CA_CENTRAL_1, 7.185);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.EU_WEST_1, 7.927);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.EU_WEST_2, 7.927);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.EU_CENTRAL_1, 7.927);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.EU_CENTRAL_2, 9.776);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AP_EAST_1, 8.863);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AP_NORTHEAST_1, 8.355);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AP_NORTHEAST_2, 7.932);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AP_SOUTHEAST_1, 8.000);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AP_SOUTHEAST_2, 10.097);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AP_SOUTHEAST_3, 8.051);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AP_SOUTHEAST_4, 8.769);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AP_SOUTH_1, 6.814);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AP_SOUTH_2, 6.814);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.SA_EAST_1, 10.270);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.EU_NORTH_1, 7.769);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.EU_WEST_3, 7.927);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.ME_SOUTH_1, 7.981);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.ME_CENTRAL_1, 8.654);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.EU_SOUTH_1, 8.277);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.EU_SOUTH_2, 7.865);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AF_SOUTH_1, 9.386);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.IL_CENTRAL_1, 8.337);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.CA_WEST_1, 7.145);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AP_SOUTHEAST_5, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.AP_SOUTHEAST_7, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.MX_CENTRAL_1, 0.0);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.US_EAST_1, 0.254);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.US_EAST_2, 0.254);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.US_WEST_1, 0.254);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.US_WEST_2, 0.254);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.CA_CENTRAL_1, 0.250);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.EU_NORTH_1, 0.275);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.EU_WEST_1, 0.281);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.EU_WEST_2, 0.281);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.EU_WEST_3, 0.281);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.EU_CENTRAL_1, 0.278);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.EU_CENTRAL_2, 0.331);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AP_EAST_1, 0.301);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AP_NORTHEAST_1, 0.277);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AP_NORTHEAST_2, 0.273);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AP_SOUTHEAST_1, 0.271);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AP_SOUTHEAST_2, 0.354);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AP_SOUTHEAST_3, 0.294);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AP_SOUTHEAST_4, 0.295);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AP_SOUTH_1, 0.235);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AP_SOUTH_2, 0.261);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.SA_EAST_1, 0.444);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.ME_SOUTH_1, 0.280);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.ME_CENTRAL_1, 0.304);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.EU_SOUTH_1, 0.281);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.EU_SOUTH_2, 0.276);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AF_SOUTH_1, 0.320);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.IL_CENTRAL_1, 0.295);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.CA_WEST_1, 0.270);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AP_SOUTHEAST_5, 0.262);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.AP_SOUTHEAST_7, 0.262);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.MX_CENTRAL_1, 0.267);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.US_EAST_1, 0.486);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.US_EAST_2, 0.486);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.US_WEST_1, 0.484);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.US_WEST_2, 0.486);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.CA_CENTRAL_1, 0.477);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.EU_NORTH_1, 0.524);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.EU_WEST_1, 0.535);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.EU_WEST_2, 0.535);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.EU_WEST_3, 0.535);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.EU_CENTRAL_1, 0.529);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.EU_CENTRAL_2, 0.631);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AP_EAST_1, 0.573);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AP_NORTHEAST_1, 0.527);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AP_NORTHEAST_2, 0.517);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AP_SOUTHEAST_1, 0.516);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AP_SOUTHEAST_2, 0.674);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AP_SOUTHEAST_3, 0.562);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AP_SOUTHEAST_4, 0.562);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AP_SOUTH_1, 0.448);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AP_SOUTH_2, 0.498);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.SA_EAST_1, 0.848);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.ME_SOUTH_1, 0.533);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.ME_CENTRAL_1, 0.579);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.EU_SOUTH_1, 0.535);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.EU_SOUTH_2, 0.526);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AF_SOUTH_1, 0.607);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.IL_CENTRAL_1, 0.561);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.CA_WEST_1, 0.515);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AP_SOUTHEAST_5, 0.502);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.AP_SOUTHEAST_7, 0.502);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.MX_CENTRAL_1, 0.51);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.US_EAST_1, 0.960);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.US_EAST_2, 0.960);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.US_WEST_1, 0.957);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.US_WEST_2, 0.960);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.CA_CENTRAL_1, 0.942);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.EU_NORTH_1, 1.039);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.EU_WEST_1, 1.060);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.EU_WEST_2, 1.059);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.EU_WEST_3, 1.059);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.EU_CENTRAL_1, 1.048);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.EU_CENTRAL_2, 1.247);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AP_EAST_1, 1.133);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AP_NORTHEAST_1, 1.042);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AP_NORTHEAST_2, 1.024);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AP_SOUTHEAST_1, 1.020);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AP_SOUTHEAST_2, 1.331);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AP_SOUTHEAST_3, 1.108);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AP_SOUTHEAST_4, 1.111);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AP_SOUTH_1, 0.883);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AP_SOUTH_2, 0.982);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.SA_EAST_1, 1.679);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.ME_SOUTH_1, 1.054);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.SA_EAST_1, 1.679);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.ME_CENTRAL_1, 1.144);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.EU_SOUTH_1, 1.058);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.EU_SOUTH_2, 1.040);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AF_SOUTH_1, 1.202);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.IL_CENTRAL_1, 1.111);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.CA_WEST_1, 1.017);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AP_SOUTHEAST_5, 0.992);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.AP_SOUTHEAST_7, 0.971);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.MX_CENTRAL_1, 1.008);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.US_EAST_1, 1.844);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.US_EAST_2, 1.844);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.US_WEST_1, 1.812);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.US_WEST_2, 1.844);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.CA_CENTRAL_1, 1.784);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.EU_NORTH_1, 1.996);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.EU_WEST_1, 2.037);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.EU_WEST_2, 2.023);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.EU_WEST_3, 2.023);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.EU_CENTRAL_1, 1.992);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.EU_CENTRAL_2, 2.379);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AP_EAST_1, 2.147);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AP_NORTHEAST_1, 1.976);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AP_NORTHEAST_2, 1.941);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AP_SOUTHEAST_1, 1.930);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AP_SOUTHEAST_2, 2.597);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AP_SOUTHEAST_3, 2.111);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AP_SOUTHEAST_4, 2.118);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AP_SOUTH_1, 1.676);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AP_SOUTH_2, 1.860);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.SA_EAST_1, 3.179);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.ME_SOUTH_1, 2.001);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.ME_CENTRAL_1, 2.180);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.EU_SOUTH_1, 2.022);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.EU_SOUTH_2, 1.982);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AF_SOUTH_1, 2.293);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.IL_CENTRAL_1, 2.118);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.CA_WEST_1, 1.939);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AP_SOUTHEAST_5, 1.906);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.AP_SOUTHEAST_7, 1.88);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.MX_CENTRAL_1, 1.936);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.US_EAST_1, 3.684);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.US_EAST_2, 3.684);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.US_WEST_1, 3.618);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.US_WEST_2, 3.684);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.CA_CENTRAL_1, 3.564);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.EU_WEST_1, 4.068);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.EU_WEST_2, 4.041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.EU_WEST_3, 4.041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.EU_CENTRAL_1, 3.980);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.EU_CENTRAL_2, 4.752);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.EU_NORTH_1, 3.987);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AP_EAST_1, 4.288);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AP_NORTHEAST_1, 3.948);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AP_NORTHEAST_2, 3.878);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AP_SOUTHEAST_1, 3.855);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AP_SOUTHEAST_2, 5.188);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AP_SOUTHEAST_3, 4.221);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AP_SOUTHEAST_4, 4.231);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AP_SOUTH_1, 3.480);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AP_SOUTH_2, 3.716);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.SA_EAST_1, 6.351);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.ME_SOUTH_1, 3.996);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.ME_CENTRAL_1, 4.355);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.EU_SOUTH_1, 4.038);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.EU_SOUTH_2, 3.959);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AF_SOUTH_1, 4.583);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.IL_CENTRAL_1, 4.232);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.CA_WEST_1, 3.874);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AP_SOUTHEAST_5, 3.808);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.AP_SOUTHEAST_7, 3.771);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.MX_CENTRAL_1, 3.869);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.US_EAST_1, 5.469);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.US_EAST_2, 5.469);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.US_WEST_1, 5.369);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.US_WEST_2, 5.469);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.CA_CENTRAL_1, 5.291);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.EU_WEST_1, 6.041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.EU_WEST_2, 6.001);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.EU_WEST_3, 6.001);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.EU_CENTRAL_1, 5.911);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.EU_CENTRAL_2, 6.986);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AP_EAST_1, 6.367);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AP_NORTHEAST_1, 5.861);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AP_NORTHEAST_2, 5.760);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AP_SOUTHEAST_1, 5.725);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AP_SOUTHEAST_2, 7.702);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AP_SOUTHEAST_3, 6.140);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AP_SOUTHEAST_4, 6.227);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AP_SOUTH_1, 5.016);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AP_SOUTH_2, 5.016);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.SA_EAST_1, 9.307);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.EU_NORTH_1, 5.920);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.ME_SOUTH_1, 5.933);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.ME_CENTRAL_1, 6.410);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.EU_SOUTH_1, 5.753);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.EU_SOUTH_2, 5.827);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AF_SOUTH_1, 6.528);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.IL_CENTRAL_1, 6.219);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.CA_WEST_1, 5.700);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AP_SOUTHEAST_5, 5.65);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.AP_SOUTHEAST_7, 5.606);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.MX_CENTRAL_1, 5.743);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.US_EAST_1, 7.363);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.US_EAST_2, 7.363);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.US_WEST_1, 7.231);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.US_WEST_2, 7.363);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.CA_CENTRAL_1, 7.125);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.EU_WEST_1, 8.131);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.EU_WEST_2, 8.078);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.EU_WEST_3, 8.078);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.EU_CENTRAL_1, 7.956);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.EU_CENTRAL_2, 9.498);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.EU_NORTH_1, 7.968);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AP_EAST_1, 8.571);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AP_NORTHEAST_1, 7.890);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AP_NORTHEAST_2, 7.751);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AP_SOUTHEAST_1, 7.706);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AP_SOUTHEAST_2, 10.369);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AP_SOUTHEAST_3, 8.434);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AP_SOUTHEAST_4, 8.457);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AP_SOUTH_1, 6.955);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AP_SOUTH_2, 7.427);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.SA_EAST_1, 12.695);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.ME_SOUTH_1, 7.986);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.ME_CENTRAL_1, 8.704);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.EU_SOUTH_1, 8.073);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.EU_SOUTH_2, 7.911);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AF_SOUTH_1, 9.162);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.IL_CENTRAL_1, 8.461);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.CA_WEST_1, 7.743);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AP_SOUTHEAST_5, 7.613);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.AP_SOUTHEAST_7, 7.554);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.MX_CENTRAL_1, 7.732);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.US_EAST_1, 10.934);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.US_EAST_2, 10.934);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.US_WEST_1, 10.733);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.US_WEST_2, 10.934);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.CA_CENTRAL_1, 10.578);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.EU_WEST_1, 12.077);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.EU_WEST_2, 11.997);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.EU_WEST_3, 11.997);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.EU_CENTRAL_1, 11.817);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.EU_CENTRAL_2, 13.646);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AP_EAST_1, 12.730);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AP_NORTHEAST_1, 11.717);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AP_NORTHEAST_2, 11.516);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AP_SOUTHEAST_1, 11.445);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AP_SOUTHEAST_2, 15.397);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AP_SOUTHEAST_3, 12.276);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AP_SOUTHEAST_4, 12.239);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AP_SOUTH_1, 10.027);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AP_SOUTH_2, 10.027);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.SA_EAST_1, 0.000);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.EU_NORTH_1, 11.835);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.ME_SOUTH_1, 11.864);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.ME_CENTRAL_1, 12.551);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.EU_SOUTH_1, 11.501);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.EU_SOUTH_2, 11.407);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AF_SOUTH_1, 13.053);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.IL_CENTRAL_1, 12.143);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.CA_WEST_1, 11.188);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AP_SOUTHEAST_5, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.AP_SOUTHEAST_7, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.MX_CENTRAL_1, 0.0);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.US_EAST_1, 0.202);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.US_WEST_2, 0.202);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.EU_WEST_1, 0.225);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.AP_SOUTHEAST_2, 0.244);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.AP_SOUTHEAST_3, 0.240);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.AP_SOUTHEAST_4, 0.243);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.US_EAST_2, 0.202);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.SA_EAST_1, 0.375);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.US_WEST_1, 0.225);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.CA_CENTRAL_1, 0.225);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.AP_EAST_1, 0.269);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.AP_NORTHEAST_1, 0.238);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.AP_SOUTHEAST_1, 0.244);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.AP_NORTHEAST_2, 0.239);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.AP_SOUTH_1, 0.231);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.AP_SOUTH_2, 0.231);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.EU_WEST_2, 0.236);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.EU_CENTRAL_1, 0.242);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.EU_CENTRAL_2, 0.271);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.ME_SOUTH_1, 0.248);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.ME_CENTRAL_1, 0.250);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.EU_NORTH_1, 0.213);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.EU_WEST_3, 0.235);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.EU_SOUTH_1, 0.237);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.EU_SOUTH_2, 0.227);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.AF_SOUTH_1, 0.267);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.IL_CENTRAL_1, 0.230);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.CA_WEST_1, 0.0);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.US_EAST_1, 0.379);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.US_WEST_2, 0.379);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.EU_WEST_1, 0.420);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.AP_SOUTHEAST_2, 0.458);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.AP_SOUTHEAST_3, 0.451);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.US_EAST_2, 0.379);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.SA_EAST_1, 0.705);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.US_WEST_1, 0.421);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.CA_CENTRAL_1, 0.420);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.AP_EAST_1, 0.504);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.AP_NORTHEAST_1, 0.445);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.AP_SOUTHEAST_1, 0.458);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.AP_NORTHEAST_2, 0.449);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.AP_SOUTH_1, 0.435);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.EU_WEST_2, 0.442);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.EU_CENTRAL_1, 0.453);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.ME_SOUTH_1, 0.462);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.EU_NORTH_1, 0.399);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.EU_WEST_3, 0.438);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.EU_SOUTH_1, 0.442);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.AF_SOUTH_1, 0.501);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.ME_CENTRAL_1, 0.466);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.EU_CENTRAL_2, 0.505);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.EU_SOUTH_2, 0.424);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.AP_SOUTH_2, 0.435);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.AP_SOUTHEAST_4, 0.457);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.IL_CENTRAL_1, 0.424);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.CA_WEST_1, 0.0);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.US_EAST_1, 0.749);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.US_WEST_2, 0.749);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.EU_WEST_1, 0.831);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.AP_SOUTHEAST_2, 0.904);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.AP_SOUTHEAST_3, 0.890);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.US_EAST_2, 0.749);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.SA_EAST_1, 1.393);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.US_WEST_1, 0.832);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.CA_CENTRAL_1, 0.831);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.AP_EAST_1, 0.997);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.AP_NORTHEAST_1, 0.879);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.AP_SOUTHEAST_1, 0.904);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.AP_NORTHEAST_2, 0.887);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.AP_SOUTH_1, 0.859);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.EU_WEST_2, 0.873);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.EU_CENTRAL_1, 0.895);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.ME_SOUTH_1, 0.914);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.EU_NORTH_1, 0.787);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.EU_WEST_3, 0.867);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.EU_SOUTH_1, 0.874);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.AF_SOUTH_1, 0.990);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.ME_CENTRAL_1, 0.923);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.EU_CENTRAL_2, 0.996);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.EU_SOUTH_2, 0.839);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.AP_SOUTH_2, 0.859);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.AP_SOUTHEAST_4, 0.903);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.IL_CENTRAL_1, 0.838);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.CA_WEST_1, 0.0);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.US_EAST_1, 1.362);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.US_WEST_2, 1.362);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.EU_WEST_1, 1.511);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.AP_SOUTHEAST_2, 1.646);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.AP_SOUTHEAST_3, 1.626);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.US_EAST_2, 1.362);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.SA_EAST_1, 2.537);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.US_WEST_1, 1.511);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.CA_CENTRAL_1, 1.511);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.AP_EAST_1, 1.812);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.AP_NORTHEAST_1, 1.600);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.AP_SOUTHEAST_1, 1.646);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.AP_NORTHEAST_2, 1.615);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.AP_SOUTH_1, 1.564);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.EU_WEST_2, 1.589);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.EU_CENTRAL_1, 1.630);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.ME_SOUTH_1, 1.663);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.EU_NORTH_1, 1.431);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.EU_WEST_3, 1.575);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.EU_SOUTH_1, 1.590);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.AF_SOUTH_1, 1.801);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.ME_CENTRAL_1, 1.679);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.EU_CENTRAL_2, 1.808);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.EU_SOUTH_2, 1.526);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.AP_SOUTH_2, 1.564);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.AP_SOUTHEAST_4, 1.648);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.IL_CENTRAL_1, 1.543);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.CA_WEST_1, 0.0);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.US_EAST_1, 2.815);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.US_WEST_2, 2.815);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.EU_WEST_1, 3.125);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.AP_SOUTHEAST_2, 3.393);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.AP_SOUTHEAST_3, 3.364);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.US_EAST_2, 2.815);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.SA_EAST_1, 5.203);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.US_WEST_1, 3.125);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.CA_CENTRAL_1, 3.125);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.AP_EAST_1, 3.738);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.AP_NORTHEAST_1, 3.306);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.AP_SOUTHEAST_1, 3.393);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.AP_NORTHEAST_2, 3.326);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.AP_SOUTH_1, 3.225);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.EU_WEST_2, 3.285);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.EU_CENTRAL_1, 3.370);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.ME_SOUTH_1, 3.437);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.EU_NORTH_1, 2.961);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.EU_WEST_3, 3.260);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.EU_SOUTH_1, 3.288);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.AF_SOUTH_1, 3.724);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.ME_CENTRAL_1, 3.467);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.EU_CENTRAL_2, 3.737);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.EU_SOUTH_2, 3.152);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.AP_SOUTH_2, 3.225);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.AP_SOUTHEAST_4, 3.400);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.IL_CENTRAL_1, 3.236);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.CA_WEST_1, 0.0);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.US_EAST_1, 5.797);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.US_WEST_2, 5.797);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.EU_WEST_1, 6.435);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.AP_SOUTHEAST_2, 6.971);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.AP_SOUTHEAST_3, 6.986);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.US_EAST_2, 5.797);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.SA_EAST_1, 10.661);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.US_WEST_1, 6.435);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.CA_CENTRAL_1, 6.435);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.AP_EAST_1, 7.680);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.AP_NORTHEAST_1, 6.801);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.AP_SOUTHEAST_1, 6.971);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.AP_NORTHEAST_2, 6.821);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.AP_SOUTH_1, 6.624);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.EU_WEST_2, 6.767);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.EU_CENTRAL_1, 6.939);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.ME_SOUTH_1, 7.052);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.EU_NORTH_1, 6.098);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.EU_WEST_3, 6.715);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.EU_SOUTH_1, 6.769);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.AF_SOUTH_1, 7.668);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.ME_CENTRAL_1, 7.084);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.EU_CENTRAL_2, 7.635);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.EU_SOUTH_2, 6.439);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.AP_SOUTH_2, 6.588);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.AP_SOUTHEAST_4, 6.947);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.IL_CENTRAL_1, 6.688);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.CA_WEST_1, 0.0);

    // AWS Instance Price won't be added any longer - not so much value in it because they are
    // almost verbatim.

    // Check storage pricing
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.US_EAST_1, 0.133);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.US_EAST_2, 0.133);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.US_WEST_1, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.US_WEST_2, 0.133);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.CA_CENTRAL_1, 0.147);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.EU_NORTH_1, 0.139);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.EU_WEST_1, 0.147);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.EU_WEST_2, 0.155);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.EU_WEST_3, 0.155);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.EU_CENTRAL_1, 0.159);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AP_EAST_1, 0.176);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AP_NORTHEAST_1, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AP_NORTHEAST_2, 0.152);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AP_SOUTHEAST_1, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AP_SOUTHEAST_2, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AP_SOUTHEAST_3, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AP_SOUTH_1, 0.152);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.SA_EAST_1, 0.253);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.ME_SOUTH_1, 0.161);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.EU_SOUTH_1, 0.154);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AF_SOUTH_1, 0.175);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.CN_NORTH_1, 0.0);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.ME_CENTRAL_1, 0.161);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.EU_CENTRAL_2, 0.191);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.EU_SOUTH_2, 0.147);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AP_SOUTH_2, 0.152);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AP_SOUTHEAST_4, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.IL_CENTRAL_1, 0.176);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.CA_WEST_1, 0.147);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AP_SOUTHEAST_5, 0.144);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.AP_SOUTHEAST_7, 0.144);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.MX_CENTRAL_1, 0.140);

    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.US_EAST_1, 0.133);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.US_EAST_2, 0.133);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.US_WEST_1, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.US_WEST_2, 0.133);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.CA_CENTRAL_1, 0.147);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.EU_NORTH_1, 0.139);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.EU_WEST_1, 0.147);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.EU_WEST_2, 0.155);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.EU_WEST_3, 0.155);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.EU_CENTRAL_1, 0.159);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.AP_EAST_1, 0.176);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.AP_NORTHEAST_1, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.AP_NORTHEAST_2, 0.152);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.AP_SOUTHEAST_1, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.AP_SOUTHEAST_2, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.AP_SOUTHEAST_3, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.AP_SOUTH_1, 0.152);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.SA_EAST_1, 0.253);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.ME_SOUTH_1, 0.161);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.EU_SOUTH_1, 0.154);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.AF_SOUTH_1, 0.175);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.CN_NORTH_1, 0.0);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.ME_CENTRAL_1, 0.161);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.EU_CENTRAL_2, 0.191);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.EU_SOUTH_2, 0.147);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.AP_SOUTH_2, 0.152);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.AP_SOUTHEAST_4, 0.160);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.IL_CENTRAL_1, 0.176);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.CA_WEST_1, 0.147);

    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.US_EAST_1, 0.167);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.US_EAST_2, 0.167);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.US_WEST_1, 0.184);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.US_WEST_2, 0.167);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.CA_CENTRAL_1, 0.184);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.EU_NORTH_1, 0.175);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.EU_WEST_1, 0.184);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.EU_WEST_2, 0.193);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.EU_WEST_3, 0.193);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.EU_CENTRAL_1, 0.199);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AP_EAST_1, 0.202);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AP_NORTHEAST_1, 0.189);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AP_NORTHEAST_2, 0.171);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AP_SOUTHEAST_1, 0.184);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AP_SOUTHEAST_2, 0.184);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AP_SOUTHEAST_3, 0.184);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AP_SOUTH_1, 0.175);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.SA_EAST_1, 0.317);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.ME_SOUTH_1, 0.202);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.EU_SOUTH_1, 0.193);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AF_SOUTH_1, 0.219);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.CN_NORTH_1, 0.0);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.ME_CENTRAL_1, 0.202);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.EU_CENTRAL_2, 0.239);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.EU_SOUTH_2, 0.184);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AP_SOUTH_2, 0.175);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AP_SOUTHEAST_4, 0.184);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.IL_CENTRAL_1, 0.221);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.CA_WEST_1, 0.184);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AP_SOUTHEAST_5, 0.086);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.AP_SOUTHEAST_7, 0.086);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.MX_CENTRAL_1, 0.091);

    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.US_EAST_1, 0.087);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.US_EAST_2, 0.087);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.US_WEST_1, 0.096);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.US_WEST_2, 0.087);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.CA_CENTRAL_1, 0.096);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.EU_NORTH_1, 0.091);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.EU_WEST_1, 0.096);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.EU_WEST_2, 0.101);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.EU_WEST_3, 0.101);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.EU_CENTRAL_1, 0.104);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AP_EAST_1, 0.106);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AP_NORTHEAST_1, 0.099);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AP_NORTHEAST_2, 0.089);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AP_SOUTHEAST_1, 0.096);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AP_SOUTHEAST_2, 0.096);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AP_SOUTHEAST_3, 0.096);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AP_SOUTH_1, 0.091);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.SA_EAST_1, 0.121);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.ME_SOUTH_1, 0.106);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.EU_SOUTH_1, 0.101);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AF_SOUTH_1, 0.114);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.CN_NORTH_1, 0.0);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.ME_CENTRAL_1, 0.106);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.EU_CENTRAL_2, 0.125);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.EU_SOUTH_2, 0.096);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AP_SOUTH_2, 0.091);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AP_SOUTHEAST_4, 0.096);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.IL_CENTRAL_1, 0.115);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.CA_WEST_1, 0.096);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AP_SOUTHEAST_5, 0.166);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.AP_SOUTHEAST_7, 0.166);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.MX_CENTRAL_1, 0.175);

    // Check data transfer pricing
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.US_EAST_1, 0.02);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.US_EAST_2, 0.02);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.US_WEST_1, 0.02);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.US_WEST_2, 0.02);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.CA_CENTRAL_1, 0.02);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.EU_NORTH_1, 0.02);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.EU_WEST_1, 0.02);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.EU_WEST_2, 0.02);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.EU_WEST_3, 0.02);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.EU_CENTRAL_1, 0.02);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AP_EAST_1, 0.09);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AP_NORTHEAST_1, 0.09);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AP_NORTHEAST_2, 0.08);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AP_SOUTHEAST_1, 0.09);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AP_SOUTHEAST_2, 0.098);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AP_SOUTHEAST_3, 0.100);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AP_SOUTH_1, 0.086);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.SA_EAST_1, 0.138);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.ME_SOUTH_1, 0.111);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.EU_SOUTH_1, 0.02);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AF_SOUTH_1, 0.147);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.ME_CENTRAL_1, 0.085);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.EU_CENTRAL_2, 0.020);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.EU_SOUTH_2, 0.020);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AP_SOUTH_2, 0.086);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AP_SOUTHEAST_4, 0.100);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.IL_CENTRAL_1, 0.080);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.CA_WEST_1, 0.050);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AP_SOUTHEAST_5, 0.08);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.AP_SOUTHEAST_7, 0.08);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.MX_CENTRAL_1, 0.08);

    // same region data transfer pricing is the same for all regions
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION, AWSRegionName.US_EAST_1, 0.01);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION, AWSRegionName.US_EAST_2, 0.01);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION, AWSRegionName.US_WEST_1, 0.01);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION, AWSRegionName.AP_SOUTH_2, 0.01);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION, AWSRegionName.IL_CENTRAL_1, 0.01);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION, AWSRegionName.CA_WEST_1, 0.01);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION, AWSRegionName.AP_SOUTHEAST_5, 0.01);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION, AWSRegionName.AP_SOUTHEAST_7, 0.01);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION, AWSRegionName.MX_CENTRAL_1, 0.01);

    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.US_EAST_1, 0.09);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.US_EAST_2, 0.09);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.US_WEST_1, 0.09);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.US_WEST_2, 0.09);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.CA_CENTRAL_1, 0.09);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_NORTH_1, 0.09);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_WEST_1, 0.09);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_WEST_2, 0.09);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_WEST_3, 0.09);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_CENTRAL_1, 0.09);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_EAST_1, 0.12);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_NORTHEAST_1, 0.114);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_NORTHEAST_2, 0.126);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTHEAST_1, 0.12);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTHEAST_2, 0.114);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTHEAST_2, 0.114);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTHEAST_3, 0.132);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTH_1, 0.1093);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.SA_EAST_1, 0.15);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.ME_SOUTH_1, 0.117);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_SOUTH_1, 0.090);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AF_SOUTH_1, 0.154);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.ME_CENTRAL_1, 0.110);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_CENTRAL_2, 0.090);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_SOUTH_2, 0.090);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTH_2, 0.109);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTHEAST_4, 0.114);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.IL_CENTRAL_1, 0.110);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.CA_WEST_1, 0.090);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTHEAST_5, 0.108);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTHEAST_7, 0.108);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.MX_CENTRAL_1, 0.09);

    assertAWSBackupSnapshotStoragePrice(AWSRegionName.US_EAST_1, SKU.monthlyToDailyCost(0.14));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.US_EAST_2, SKU.monthlyToDailyCost(0.14));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.US_WEST_1, SKU.monthlyToDailyCost(0.16));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.US_WEST_2, SKU.monthlyToDailyCost(0.14));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.CA_CENTRAL_1, SKU.monthlyToDailyCost(0.16));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.EU_NORTH_1, SKU.monthlyToDailyCost(0.14));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.EU_WEST_1, SKU.monthlyToDailyCost(0.14));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.EU_WEST_2, SKU.monthlyToDailyCost(0.15));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.EU_WEST_3, SKU.monthlyToDailyCost(0.15));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.EU_CENTRAL_1, SKU.monthlyToDailyCost(0.15));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.AP_EAST_1, SKU.monthlyToDailyCost(0.16));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.AP_NORTHEAST_1, SKU.monthlyToDailyCost(0.14));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.AP_NORTHEAST_2, SKU.monthlyToDailyCost(0.14));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.AP_NORTHEAST_3, SKU.monthlyToDailyCost(0.14));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.AP_SOUTHEAST_1, SKU.monthlyToDailyCost(0.14));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.AP_SOUTHEAST_2, SKU.monthlyToDailyCost(0.16));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.AP_SOUTHEAST_3, SKU.monthlyToDailyCost(0.14));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.AP_SOUTH_1, SKU.monthlyToDailyCost(0.14));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.SA_EAST_1, SKU.monthlyToDailyCost(0.19));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.ME_SOUTH_1, SKU.monthlyToDailyCost(0.16));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.EU_SOUTH_1, SKU.monthlyToDailyCost(0.15));
    assertAWSBackupSnapshotStoragePrice(AWSRegionName.AF_SOUTH_1, SKU.monthlyToDailyCost(0.17));
    assertRegionPrice(SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE, AWSRegionName.CN_NORTH_1, 0.0);
    assertRegionPrice(SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE, AWSRegionName.CN_NORTHWEST_1, 0.0);
    assertRegionPrice(
        SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE,
        AWSRegionName.ME_CENTRAL_1,
        SKU.monthlyToDailyCost(0.16));
    assertRegionPrice(
        SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE,
        AWSRegionName.EU_SOUTH_2,
        SKU.monthlyToDailyCost(0.14));
    assertRegionPrice(
        SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE,
        AWSRegionName.AP_SOUTH_2,
        SKU.monthlyToDailyCost(0.14));
    assertRegionPrice(
        SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE,
        AWSRegionName.EU_CENTRAL_2,
        SKU.monthlyToDailyCost(0.17));
    assertRegionPrice(
        SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE,
        AWSRegionName.AP_SOUTHEAST_4,
        SKU.monthlyToDailyCost(0.16));
    assertRegionPrice(
        SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE,
        AWSRegionName.IL_CENTRAL_1,
        SKU.monthlyToDailyCost(0.17));
    assertRegionPrice(
        SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE, AWSRegionName.CA_WEST_1, SKU.monthlyToDailyCost(0.16));
    assertRegionPrice(
        SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE,
        AWSRegionName.AP_SOUTHEAST_5,
        SKU.monthlyToDailyCost(0.13));
    assertRegionPrice(
        SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE,
        AWSRegionName.AP_SOUTHEAST_7,
        SKU.monthlyToDailyCost(0.13));
    assertRegionPrice(
        SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE,
        AWSRegionName.MX_CENTRAL_1,
        SKU.monthlyToDailyCost(0.15));

    // Check PIT Restore pricing.
    assertAWSTierPricing(
        AWSRegionName.US_EAST_1,
        SKU.monthlyToDailyCost(1),
        SKU.monthlyToDailyCost(0.75),
        SKU.monthlyToDailyCost(0.5000),
        SKU.monthlyToDailyCost(0.2500));
    assertAWSTierPricing(
        AWSRegionName.ME_CENTRAL_1,
        SKU.monthlyToDailyCost(1.1000),
        SKU.monthlyToDailyCost(0.8500),
        SKU.monthlyToDailyCost(0.5500),
        SKU.monthlyToDailyCost(0.3000));
    assertAWSTierPricing(
        AWSRegionName.IL_CENTRAL_1,
        SKU.monthlyToDailyCost(1.20),
        SKU.monthlyToDailyCost(0.90),
        SKU.monthlyToDailyCost(0.60),
        SKU.monthlyToDailyCost(0.30));
    assertAWSTierPricing(
        AWSRegionName.CA_WEST_1,
        SKU.monthlyToDailyCost(1.10),
        SKU.monthlyToDailyCost(0.85),
        SKU.monthlyToDailyCost(0.55),
        SKU.monthlyToDailyCost(0.30));
    assertAWSTierPricing(
        AWSRegionName.AP_SOUTHEAST_5,
        SKU.monthlyToDailyCost(0.95),
        SKU.monthlyToDailyCost(0.70),
        SKU.monthlyToDailyCost(0.50),
        SKU.monthlyToDailyCost(0.25));
    assertAWSTierPricing(
        AWSRegionName.AP_SOUTHEAST_7,
        SKU.monthlyToDailyCost(0.95),
        SKU.monthlyToDailyCost(0.70),
        SKU.monthlyToDailyCost(0.50),
        SKU.monthlyToDailyCost(0.25));
    assertAWSTierPricing(
        AWSRegionName.MX_CENTRAL_1,
        SKU.monthlyToDailyCost(1.10),
        SKU.monthlyToDailyCost(0.80),
        SKU.monthlyToDailyCost(0.55),
        SKU.monthlyToDailyCost(0.30));
  }

  @Test
  public void testAtlasAWSPricing_forAtlasGov() {
    // Check instance pricing.
    // Pricing for Atlas Government. See the following page:
    // https://docs.google.com/spreadsheets/d/12SRNHdiTvKWBnyBNt9zxjIBtLXxSa-reFylyzewYJ64/edit?usp=sharing
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.US_GOV_EAST_1, 0.0300);
    assertRegionPrice(
        SKU.NDS_AWS_DATA_TRANSFER_DIFFERENT_REGION, AWSRegionName.US_GOV_WEST_1, 0.0300);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.US_GOV_EAST_1, 0.1550);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_INTERNET, AWSRegionName.US_GOV_WEST_1, 0.1550);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION, AWSRegionName.US_GOV_EAST_1, 0.0100);
    assertRegionPrice(SKU.NDS_AWS_DATA_TRANSFER_SAME_REGION, AWSRegionName.US_GOV_WEST_1, 0.0100);

    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.US_GOV_EAST_1, 0.0320);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10, AWSRegionName.US_GOV_WEST_1, 0.0320);
    assertThrows(
        IllegalStateException.class,
        () -> assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.US_GOV_EAST_1, 0.0000));
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100, AWSRegionName.US_GOV_WEST_1, 3.8700);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M100_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M10_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.US_GOV_EAST_1, 4.6470);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140, AWSRegionName.US_GOV_WEST_1, 4.6470);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M140_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.US_GOV_EAST_1, 0.0840);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20, AWSRegionName.US_GOV_WEST_1, 0.0840);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.US_GOV_EAST_1, 6.1690);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200, AWSRegionName.US_GOV_WEST_1, 6.1690);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.US_GOV_EAST_1, 3.3840);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_NVME, AWSRegionName.US_GOV_WEST_1, 3.3840);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_PAUSED, AWSRegionName.IL_CENTRAL_1, 0.00603);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M200_PAUSED, AWSRegionName.CA_WEST_1, 0.00502);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M20_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.US_GOV_EAST_1, 0.2250);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30, AWSRegionName.US_GOV_WEST_1, 0.2250);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.US_GOV_EAST_1, 9.2390);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300, AWSRegionName.US_GOV_WEST_1, 9.2390);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M300_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M30_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.US_GOV_EAST_1, 0.4400);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40, AWSRegionName.US_GOV_WEST_1, 0.4400);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.US_GOV_EAST_1, 0.2430);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_NVME, AWSRegionName.US_GOV_WEST_1, 0.2430);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.US_GOV_EAST_1, 6.9680);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M400_NVME, AWSRegionName.US_GOV_WEST_1, 6.9680);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M40_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.US_GOV_EAST_1, 0.8420);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50, AWSRegionName.US_GOV_WEST_1, 0.8420);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.US_GOV_EAST_1, 0.4560);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_NVME, AWSRegionName.US_GOV_WEST_1, 0.4560);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M50_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.US_GOV_EAST_1, 1.6760);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60, AWSRegionName.US_GOV_WEST_1, 1.6760);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.US_GOV_EAST_1, 0.8990);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_NVME, AWSRegionName.US_GOV_WEST_1, 0.8990);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M60_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.US_GOV_EAST_1, 3.0870);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80, AWSRegionName.US_GOV_WEST_1, 3.0870);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.US_GOV_EAST_1, 1.6370);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_NVME, AWSRegionName.US_GOV_WEST_1, 1.6370);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_M80_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.US_GOV_EAST_1, 4.4140);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200, AWSRegionName.US_GOV_WEST_1, 4.4140);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200_PAUSED, AWSRegionName.IL_CENTRAL_1, 0.00603);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R200_PAUSED, AWSRegionName.CA_WEST_1, 0.00502);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.US_GOV_EAST_1, 6.5560);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300, AWSRegionName.US_GOV_WEST_1, 6.5560);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300_PAUSED, AWSRegionName.IL_CENTRAL_1, 0.00603);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R300_PAUSED, AWSRegionName.CA_WEST_1, 0.00502);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.US_GOV_EAST_1, 0.3040);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40, AWSRegionName.US_GOV_WEST_1, 0.3040);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.US_GOV_EAST_1, 8.8280);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400, AWSRegionName.US_GOV_WEST_1, 8.8280);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400_PAUSED, AWSRegionName.IL_CENTRAL_1, 0.00603);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R400_PAUSED, AWSRegionName.CA_WEST_1, 0.00502);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40_PAUSED, AWSRegionName.IL_CENTRAL_1, 0.00603);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R40_PAUSED, AWSRegionName.CA_WEST_1, 0.00502);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.US_GOV_EAST_1, 0.5850);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50, AWSRegionName.US_GOV_WEST_1, 0.5850);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50_PAUSED, AWSRegionName.IL_CENTRAL_1, 0.00603);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R50_PAUSED, AWSRegionName.CA_WEST_1, 0.00502);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.US_GOV_EAST_1, 1.1500);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60, AWSRegionName.US_GOV_WEST_1, 1.1500);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60_PAUSED, AWSRegionName.IL_CENTRAL_1, 0.00603);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R60_PAUSED, AWSRegionName.CA_WEST_1, 0.00502);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.US_GOV_EAST_1, 13.1060);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700, AWSRegionName.US_GOV_WEST_1, 13.1060);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700_PAUSED, AWSRegionName.IL_CENTRAL_1, 0.00603);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R700_PAUSED, AWSRegionName.CA_WEST_1, 0.00502);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.US_GOV_EAST_1, 2.2090);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80, AWSRegionName.US_GOV_WEST_1, 2.2090);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80_PAUSED, AWSRegionName.US_GOV_EAST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80_PAUSED, AWSRegionName.US_GOV_WEST_1, 0.0041);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80_PAUSED, AWSRegionName.IL_CENTRAL_1, 0.00603);
    assertRegionPrice(SKU.NDS_AWS_INSTANCE_R80_PAUSED, AWSRegionName.CA_WEST_1, 0.00502);

    // Check PIT Restore pricing.
    assertAWSTierPricing(
        AWSRegionName.US_GOV_EAST_1,
        SKU.monthlyToDailyCost(1.4000),
        SKU.monthlyToDailyCost(1.0500),
        SKU.monthlyToDailyCost(0.7000),
        SKU.monthlyToDailyCost(0.3500));
    assertAWSTierPricing(
        AWSRegionName.US_GOV_WEST_1,
        SKU.monthlyToDailyCost(1.4000),
        SKU.monthlyToDailyCost(1.0500),
        SKU.monthlyToDailyCost(0.7000),
        SKU.monthlyToDailyCost(0.3500));

    assertRegionPrice(SKU.NDS_AWS_PRIVATE_ENDPOINT, AWSRegionName.US_GOV_EAST_1, 0.0640);
    assertRegionPrice(SKU.NDS_AWS_PRIVATE_ENDPOINT, AWSRegionName.US_GOV_WEST_1, 0.0640);
    assertRegionPrice(
        SKU.NDS_AWS_PRIVATE_ENDPOINT_CAPACITY_UNITS, AWSRegionName.US_GOV_EAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_PRIVATE_ENDPOINT_CAPACITY_UNITS, AWSRegionName.US_GOV_WEST_1, 0.0150);

    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.US_GOV_EAST_1, 0.1040);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_IOPS, AWSRegionName.US_GOV_WEST_1, 0.1040);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.US_GOV_EAST_1, 0.2000);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_PROVISIONED, AWSRegionName.US_GOV_WEST_1, 0.2000);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.US_GOV_EAST_1, 0.1600);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD, AWSRegionName.US_GOV_WEST_1, 0.1600);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.US_GOV_EAST_1, 0.1600);
    assertAWSStoragePrice(SKU.NDS_AWS_STORAGE_STANDARD_GP3, AWSRegionName.US_GOV_WEST_1, 0.1600);
    assertAWSBackupSnapshotStoragePrice(
        AWSRegionName.US_GOV_EAST_1, SKU.monthlyToDailyCost(0.1900));
    assertAWSBackupSnapshotStoragePrice(
        AWSRegionName.US_GOV_WEST_1, SKU.monthlyToDailyCost(0.1900));

    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.AF_SOUTH_1, 0.01309);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.AP_EAST_1, 0.01430);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.AP_NORTHEAST_1, 0.01400);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.AP_NORTHEAST_2, 0.01300);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.AP_NORTHEAST_3, 0.01400);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.AP_SOUTH_1, 0.01300);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.AP_SOUTHEAST_1, 0.01300);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.AP_SOUTHEAST_2, 0.01300);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.AP_SOUTHEAST_3, 0.01300);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.CA_CENTRAL_1, 0.01100);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.EU_CENTRAL_1, 0.01200);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.EU_NORTH_1, 0.01050);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.EU_SOUTH_1, 0.01155);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.EU_WEST_1, 0.01100);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.EU_WEST_2, 0.01100);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.EU_WEST_3, 0.01100);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.ME_SOUTH_1, 0.01210);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.SA_EAST_1, 0.02100);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.US_EAST_1, 0.01000);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.US_EAST_2, 0.01000);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.US_WEST_1, 0.01100);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.US_WEST_2, 0.01000);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.ME_CENTRAL_1, 0.01210);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.EU_CENTRAL_2, 0.01320);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.EU_SOUTH_2, 0.01100);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.AP_SOUTH_2, 0.01300);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.AP_SOUTHEAST_4, 0.01300);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.IL_CENTRAL_1, 0.01155);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.CA_WEST_1, 0.01100);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.US_GOV_EAST_1, 0.0125);
    assertRegionPrice(SKU.NDS_AWS_CMK_PRIVATE_NETWORKING, AWSRegionName.US_GOV_WEST_1, 0.0125);
  }

  @Test
  public void testGcpPrivateEndpointCapacityUnits_pricingByDate() {
    SKU sku = SKU.NDS_GCP_PRIVATE_ENDPOINT_CAPACITY_UNITS;

    // New Pricing
    assertGcpRegionPricing(sku, GCPRegionName.EASTERN_ASIA_PACIFIC, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.ASIA_EAST_2, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.ASIA_NORTHEAST_2, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.ASIA_NORTHEAST_3, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.ASIA_SOUTH_1, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.ASIA_SOUTH_2, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.ASIA_SOUTHEAST_2, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.EUROPE_CENTRAL_2, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.EUROPE_NORTH_1, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.WESTERN_EUROPE, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.EUROPE_WEST_2, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.EUROPE_WEST_3, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.EUROPE_WEST_4, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.EUROPE_WEST_6, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.EUROPE_WEST_8, 0.0387);
    assertGcpRegionPricing(sku, GCPRegionName.EUROPE_WEST_9, 0.0387);
    assertGcpRegionPricing(sku, GCPRegionName.EUROPE_WEST_10, 0.0446);
    assertGcpRegionPricing(sku, GCPRegionName.EUROPE_WEST_12, 0.04);
    assertGcpRegionPricing(sku, GCPRegionName.EUROPE_SOUTHWEST_1, 0.0392);
    assertGcpRegionPricing(sku, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 0.0397);
    assertGcpRegionPricing(sku, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 0.0456);
    assertGcpRegionPricing(sku, GCPRegionName.MIDDLE_EAST_WEST_1, 0.0376);
    assertGcpRegionPricing(sku, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.SOUTH_AMERICA_EAST_1, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.SOUTH_AMERICA_WEST_1, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.CENTRAL_US, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.EASTERN_US, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.US_EAST_4, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.US_EAST_5, 0.04);
    assertGcpRegionPricing(sku, GCPRegionName.WESTERN_US, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.US_WEST_2, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.US_WEST_3, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.US_WEST_4, 0.0200);
    assertGcpRegionPricing(sku, GCPRegionName.US_SOUTH_1, 0.0389);
    assertGcpRegionPricing(sku, GCPRegionName.AFRICA_SOUTH_1, 0.04092);
    assertGcpRegionPricing(sku, GCPRegionName.NORTH_AMERICA_SOUTH_1, 0.03744);
  }

  private void assertGcpRegionPricing(SKU sku, GCPRegionName regionName, double unitPrice) {
    assertEquals(
        unitPrice, sku.getInfo().getRegionUnitPriceDollars(regionName, new Date()), EPSILON);
  }

  @Test
  public void testAtlasGCPPricing() {
    // Instance pricing
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.WESTERN_US, 0.0274);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.CENTRAL_US, 0.0274);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EASTERN_US, 0.0274);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.WESTERN_EUROPE, 0.0301);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EASTERN_ASIA_PACIFIC, 0.0315);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 0.0345);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 0.0328);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.US_EAST_4, 0.0308);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.US_EAST_5, 0.027);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.US_WEST_2, 0.0329);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.US_WEST_3, 0.033);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.US_WEST_4, 0.031);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.US_SOUTH_1, 0.032);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 0.0302);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 0.067);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.SOUTH_AMERICA_EAST_1, 0.0425);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.SOUTH_AMERICA_WEST_1, 0.0392);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EUROPE_NORTH_1, 0.0302);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EUROPE_WEST_2, 0.0344);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EUROPE_WEST_3, 0.0344);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EUROPE_WEST_4, 0.0302);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EUROPE_WEST_6, 0.0375);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EUROPE_WEST_8, 0.0318);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EUROPE_WEST_9, 0.0318);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EUROPE_WEST_10, 0.0418);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EUROPE_WEST_12, 0.0351);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EUROPE_SOUTHWEST_1, 0.0324);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.EUROPE_CENTRAL_2, 0.034);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 0.0333);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 0.0434);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.MIDDLE_EAST_WEST_1, 0.0302);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.ASIA_EAST_2, 0.0375);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.ASIA_NORTHEAST_2, 0.0345);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.ASIA_NORTHEAST_3, 0.0345);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.ASIA_SOUTH_1, 0.0329);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.ASIA_SOUTH_2, 0.033);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.ASIA_SOUTHEAST_2, 0.036);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.0380);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 0.0390);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.AFRICA_SOUTH_1, 0.0360);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M10, GCPRegionName.NORTH_AMERICA_SOUTH_1, 0.0300);

    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.WESTERN_US, 0.0621);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.CENTRAL_US, 0.0621);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EASTERN_US, 0.0621);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.WESTERN_EUROPE, 0.0677);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EASTERN_ASIA_PACIFIC, 0.0708);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 0.0798);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 0.0757);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.US_EAST_4, 0.0697);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.US_EAST_5, 0.062);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.US_WEST_2, 0.0746);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.US_WEST_3, 0.075);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.US_WEST_4, 0.070);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.US_SOUTH_1, 0.073);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 0.0683);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 0.067);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.SOUTH_AMERICA_EAST_1, 0.0979);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.SOUTH_AMERICA_WEST_1, 0.0889);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EUROPE_NORTH_1, 0.0683);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EUROPE_WEST_2, 0.0794);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EUROPE_WEST_3, 0.0794);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EUROPE_WEST_4, 0.0683);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EUROPE_WEST_6, 0.0867);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EUROPE_WEST_8, 0.0721);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EUROPE_WEST_9, 0.0721);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EUROPE_WEST_10, 0.0956);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EUROPE_WEST_12, 0.0793);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EUROPE_SOUTHWEST_1, 0.0734);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.EUROPE_CENTRAL_2, 0.079);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 0.0756);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 0.0992);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.MIDDLE_EAST_WEST_1, 0.0686);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.ASIA_EAST_2, 0.0851);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.ASIA_NORTHEAST_2, 0.0798);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.ASIA_NORTHEAST_3, 0.0798);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.ASIA_SOUTH_1, 0.0745);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.ASIA_SOUTH_2, 0.075);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.ASIA_SOUTHEAST_2, 0.083);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.0876);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 0.088);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.AFRICA_SOUTH_1, 0.0810);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M20, GCPRegionName.NORTH_AMERICA_SOUTH_1, 0.0680);

    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.WESTERN_US, 0.1455);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.CENTRAL_US, 0.1455);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EASTERN_US, 0.1455);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.WESTERN_EUROPE, 0.1588);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EASTERN_ASIA_PACIFIC, 0.1662);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 0.1871);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 0.1776);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.US_EAST_4, 0.1635);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.US_EAST_5, 0.146);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.US_WEST_2, 0.1749);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.US_WEST_3, 0.175);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.US_WEST_4, 0.163);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.US_SOUTH_1, 0.172);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 0.1602);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 0.158);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.SOUTH_AMERICA_EAST_1, 0.2297);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.SOUTH_AMERICA_WEST_1, 0.2083);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EUROPE_NORTH_1, 0.1602);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EUROPE_WEST_2, 0.1862);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EUROPE_WEST_3, 0.1862);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EUROPE_WEST_4, 0.1602);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EUROPE_WEST_6, 0.2034);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EUROPE_WEST_8, 0.1689);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EUROPE_WEST_9, 0.1689);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EUROPE_WEST_10, 0.2243);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EUROPE_WEST_12, 0.186);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EUROPE_SOUTHWEST_1, 0.1719);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.EUROPE_CENTRAL_2, 0.186);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 0.1773);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 0.2330);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.MIDDLE_EAST_WEST_1, 0.1607);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.ASIA_EAST_2, 0.1995);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.ASIA_NORTHEAST_2, 0.1871);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.ASIA_NORTHEAST_3, 0.1871);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.ASIA_SOUTH_1, 0.1748);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.ASIA_SOUTH_2, 0.175);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.ASIA_SOUTHEAST_2, 0.195);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.2055);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 0.205);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.AFRICA_SOUTH_1, 0.1990);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M30, GCPRegionName.NORTH_AMERICA_SOUTH_1, 0.1590);

    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.WESTERN_US, 0.2830);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.CENTRAL_US, 0.2830);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EASTERN_US, 0.2830);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.WESTERN_EUROPE, 0.3089);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EASTERN_ASIA_PACIFIC, 0.3235);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 0.3639);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 0.3456);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.US_EAST_4, 0.3181);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.US_EAST_5, 0.283);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.US_WEST_2, 0.3402);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.US_WEST_3, 0.340);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.US_WEST_4, 0.318);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.US_SOUTH_1, 0.334);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 0.3116);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 0.307);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.SOUTH_AMERICA_EAST_1, 0.4471);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.SOUTH_AMERICA_WEST_1, 0.4051);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EUROPE_NORTH_1, 0.3116);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EUROPE_WEST_2, 0.3623);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EUROPE_WEST_3, 0.3623);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EUROPE_WEST_4, 0.3116);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EUROPE_WEST_6, 0.3957);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EUROPE_WEST_8, 0.3286);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EUROPE_WEST_9, 0.3286);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EUROPE_WEST_10, 0.4367);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EUROPE_WEST_12, 0.3618);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EUROPE_SOUTHWEST_1, 0.3343);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.EUROPE_CENTRAL_2, 0.362);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 0.3449);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 0.4536);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.MIDDLE_EAST_WEST_1, 0.3124);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.ASIA_EAST_2, 0.3882);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.ASIA_NORTHEAST_2, 0.3639);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.ASIA_NORTHEAST_3, 0.3639);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.ASIA_SOUTH_1, 0.3399);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.ASIA_SOUTH_2, 0.340);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.ASIA_SOUTHEAST_2, 0.379);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.4000);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 0.399);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.AFRICA_SOUTH_1, 0.3860);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M40, GCPRegionName.NORTH_AMERICA_SOUTH_1, 0.3100);

    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.WESTERN_US, 0.5449);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.CENTRAL_US, 0.5449);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EASTERN_US, 0.5449);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.WESTERN_EUROPE, 0.5949);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EASTERN_ASIA_PACIFIC, 0.6230);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 0.7006);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 0.6656);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.US_EAST_4, 0.6124);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.US_EAST_5, 0.545);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.US_WEST_2, 0.6549);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.US_WEST_3, 0.655);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.US_WEST_4, 0.612);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.US_SOUTH_1, 0.644);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 0.5999);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 0.592);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.SOUTH_AMERICA_EAST_1, 0.8608);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.SOUTH_AMERICA_WEST_1, 0.7798);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EUROPE_NORTH_1, 0.5999);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EUROPE_WEST_2, 0.6976);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EUROPE_WEST_3, 0.6976);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EUROPE_WEST_4, 0.5999);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EUROPE_WEST_6, 0.7623);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EUROPE_WEST_8, 0.6326);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EUROPE_WEST_9, 0.6326);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EUROPE_WEST_10, 0.8411);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EUROPE_WEST_12, 0.6966);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EUROPE_SOUTHWEST_1, 0.6435);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.EUROPE_CENTRAL_2, 0.697);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 0.6639);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 0.8738);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.MIDDLE_EAST_WEST_1, 0.6012);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.ASIA_EAST_2, 0.7477);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.ASIA_NORTHEAST_2, 0.7006);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.ASIA_NORTHEAST_3, 0.7006);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.ASIA_SOUTH_1, 0.6544);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.ASIA_SOUTH_2, 0.655);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.ASIA_SOUTHEAST_2, 0.730);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.7700);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 0.769);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.AFRICA_SOUTH_1, 0.7440);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M50, GCPRegionName.NORTH_AMERICA_SOUTH_1, 0.5960);

    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.WESTERN_US, 1.0814);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.CENTRAL_US, 1.0814);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EASTERN_US, 1.0814);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.WESTERN_EUROPE, 1.1807);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EASTERN_ASIA_PACIFIC, 1.2366);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 1.3902);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 1.3210);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.US_EAST_4, 1.2154);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.US_EAST_5, 1.082);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.US_WEST_2, 1.2997);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.US_WEST_3, 1.300);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.US_WEST_4, 1.215);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.US_SOUTH_1, 1.277);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 1.1905);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 1.175);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.SOUTH_AMERICA_EAST_1, 1.7085);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.SOUTH_AMERICA_WEST_1, 1.5476);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EUROPE_NORTH_1, 1.1905);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EUROPE_WEST_2, 1.3846);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EUROPE_WEST_3, 1.3846);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EUROPE_WEST_4, 1.1905);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EUROPE_WEST_6, 1.5129);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EUROPE_WEST_8, 1.2554);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EUROPE_WEST_9, 1.2554);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EUROPE_WEST_10, 1.6696);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EUROPE_WEST_12, 1.3825);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EUROPE_SOUTHWEST_1, 1.2771);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.EUROPE_CENTRAL_2, 1.382);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 1.3176);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 1.7346);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.MIDDLE_EAST_WEST_1, 1.193);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.ASIA_EAST_2, 1.4840);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.ASIA_NORTHEAST_2, 1.3902);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.ASIA_NORTHEAST_3, 1.3902);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.ASIA_SOUTH_1, 1.2985);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.ASIA_SOUTH_2, 1.299);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.ASIA_SOUTHEAST_2, 1.449);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 1.5283);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 1.527);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.AFRICA_SOUTH_1, 1.4770);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M60, GCPRegionName.NORTH_AMERICA_SOUTH_1, 1.1820);

    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.WESTERN_US, 2.0158);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.CENTRAL_US, 2.0158);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EASTERN_US, 2.0158);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.WESTERN_EUROPE, 2.1984);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EASTERN_ASIA_PACIFIC, 2.3012);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 2.5921);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 2.4590);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.US_EAST_4, 2.2650);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.US_EAST_5, 2.018);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.US_WEST_2, 2.4228);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.US_WEST_3, 2.423);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.US_WEST_4, 2.264);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.US_SOUTH_1, 2.381);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 2.2193);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 2.189);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.SOUTH_AMERICA_EAST_1, 3.1826);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.SOUTH_AMERICA_WEST_1, 2.8850);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EUROPE_NORTH_1, 2.2193);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EUROPE_WEST_2, 2.5788);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EUROPE_WEST_3, 2.5788);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EUROPE_WEST_4, 2.2193);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EUROPE_WEST_6, 2.8206);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EUROPE_WEST_8, 2.3403);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EUROPE_WEST_9, 2.3403);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EUROPE_WEST_10, 3.1138);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EUROPE_WEST_12, 2.5738);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EUROPE_SOUTHWEST_1, 2.3806);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.EUROPE_CENTRAL_2, 2.574);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 2.457);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 3.2350);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.MIDDLE_EAST_WEST_1, 2.2245);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.ASIA_EAST_2, 2.7583);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.ASIA_NORTHEAST_2, 2.5921);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.ASIA_NORTHEAST_3, 2.5921);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.ASIA_SOUTH_1, 2.4208);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.ASIA_SOUTH_2, 2.422);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.ASIA_SOUTHEAST_2, 2.700);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 2.8473);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 2.844);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.AFRICA_SOUTH_1, 2.7520);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M80, GCPRegionName.NORTH_AMERICA_SOUTH_1, 2.2040);

    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.WESTERN_US, 3.1362);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.CENTRAL_US, 3.1362);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EASTERN_US, 3.1362);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.WESTERN_EUROPE, 3.3994);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EASTERN_ASIA_PACIFIC, 3.5519);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 4.0316);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 3.8015);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.US_EAST_4, 3.4263);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.US_EAST_5, 3.136);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.US_WEST_2, 3.7663);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.US_WEST_3, 3.7663);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.US_WEST_4, 3.5308);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.US_SOUTH_1, 3.701);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 3.4517);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 3.4517);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.SOUTH_AMERICA_EAST_1, 4.9349);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.SOUTH_AMERICA_WEST_1, 4.4847);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EUROPE_NORTH_1, 3.4522);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EUROPE_WEST_2, 3.9937);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EUROPE_WEST_3, 3.9937);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EUROPE_WEST_4, 3.4521);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EUROPE_WEST_6, 4.3375);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EUROPE_WEST_8, 3.6380);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EUROPE_WEST_9, 3.6380);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EUROPE_WEST_10, 4.8273);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EUROPE_WEST_12, 3.9962);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EUROPE_SOUTHWEST_1, 3.7007);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.EUROPE_CENTRAL_2, 3.9937);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 3.8085);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 5.0153);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.MIDDLE_EAST_WEST_1, 3.4482);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.ASIA_EAST_2, 4.2377);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.ASIA_NORTHEAST_2, 4.0354);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.ASIA_NORTHEAST_3, 4.0354);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.ASIA_SOUTH_1, 3.7661);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.ASIA_SOUTH_2, 3.7661);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.ASIA_SOUTHEAST_2, 4.1944);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 4.4171);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 4.4171);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.AFRICA_SOUTH_1, 4.2700);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M140, GCPRegionName.NORTH_AMERICA_SOUTH_1, 3.4170);

    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.WESTERN_US, 4.2608);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.CENTRAL_US, 4.2608);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EASTERN_US, 4.2608);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.WESTERN_EUROPE, 4.6468);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EASTERN_ASIA_PACIFIC, 4.8640);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 5.4787);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 5.1976);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.US_EAST_4, 4.7874);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.US_EAST_5, 4.264);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.US_WEST_2, 5.1209);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.US_WEST_3, 5.121);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.US_WEST_4, 4.786);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.US_SOUTH_1, 5.032);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 4.6909);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 4.628);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.SOUTH_AMERICA_EAST_1, 6.7268);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.SOUTH_AMERICA_WEST_1, 6.0978);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EUROPE_NORTH_1, 4.6909);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EUROPE_WEST_2, 5.4507);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EUROPE_WEST_3, 5.4507);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EUROPE_WEST_4, 4.6909);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EUROPE_WEST_6, 5.9617);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EUROPE_WEST_8, 4.9465);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EUROPE_WEST_9, 4.9465);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EUROPE_WEST_10, 6.5817);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EUROPE_WEST_12, 5.4401);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EUROPE_SOUTHWEST_1, 5.0318);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.EUROPE_CENTRAL_2, 5.441);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 5.1931);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 6.8381);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.MIDDLE_EAST_WEST_1, 4.7017);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.ASIA_EAST_2, 5.8300);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.ASIA_NORTHEAST_2, 5.4787);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.ASIA_NORTHEAST_3, 5.4787);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.ASIA_SOUTH_1, 5.1167);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.ASIA_SOUTH_2, 5.120);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.ASIA_SOUTHEAST_2, 5.706);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 6.0181);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 6.01);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.AFRICA_SOUTH_1, 5.8170);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M200, GCPRegionName.NORTH_AMERICA_SOUTH_1, 4.6590);

    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.WESTERN_US, 5.3603);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.CENTRAL_US, 5.3603);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EASTERN_US, 5.3603);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.WESTERN_EUROPE, 5.8067);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EASTERN_ASIA_PACIFIC, 6.0655);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 6.8913);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 6.4928);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.US_EAST_4, 5.8566);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.US_EAST_5, 5.36);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.US_WEST_2, 6.4373);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.US_WEST_3, 6.4373);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.US_WEST_4, 6.0338);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.US_SOUTH_1, 6.325);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 5.8996);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 5.8996);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.SOUTH_AMERICA_EAST_1, 8.4317);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.SOUTH_AMERICA_WEST_1, 7.6652);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EUROPE_NORTH_1, 5.9003);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EUROPE_WEST_2, 6.8230);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EUROPE_WEST_3, 6.8230);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EUROPE_WEST_4, 5.9002);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EUROPE_WEST_6, 7.4102);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EUROPE_WEST_8, 6.2179);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EUROPE_WEST_9, 6.2179);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EUROPE_WEST_10, 8.2512);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EUROPE_WEST_12, 6.827);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EUROPE_SOUTHWEST_1, 6.3252);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.EUROPE_CENTRAL_2, 6.8230);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 6.5095);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 8.5726);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.MIDDLE_EAST_WEST_1, 5.8935);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.ASIA_EAST_2, 7.2324);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.ASIA_NORTHEAST_2, 6.8978);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.ASIA_NORTHEAST_3, 6.8978);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.ASIA_SOUTH_1, 6.4369);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.ASIA_SOUTH_2, 6.4369);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.ASIA_SOUTHEAST_2, 7.1675);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 7.5474);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 7.5474);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.AFRICA_SOUTH_1, 7.2960);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M250, GCPRegionName.NORTH_AMERICA_SOUTH_1, 5.8410);

    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.WESTERN_US, 6.3758);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.CENTRAL_US, 6.3758);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EASTERN_US, 6.3758);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.WESTERN_EUROPE, 6.6939);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EASTERN_ASIA_PACIFIC, 7.2806);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 8.1981);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 7.7795);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.US_EAST_4, 7.1642);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.US_EAST_5, 6.381);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.US_WEST_2, 7.6630);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.US_WEST_3, 7.663);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.US_WEST_4, 7.164);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.US_SOUTH_1, 7.53);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 7.0194);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 6.925);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.SOUTH_AMERICA_EAST_1, 10.0672);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.SOUTH_AMERICA_WEST_1, 9.1247);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EUROPE_NORTH_1, 7.0194);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EUROPE_WEST_2, 8.1577);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EUROPE_WEST_3, 8.1577);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EUROPE_WEST_4, 7.0194);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EUROPE_WEST_6, 8.9213);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EUROPE_WEST_8, 7.4019);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EUROPE_WEST_9, 7.4019);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EUROPE_WEST_10, 9.8453);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EUROPE_WEST_12, 8.1475);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EUROPE_SOUTHWEST_1, 7.5295);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.EUROPE_CENTRAL_2, 8.149);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 7.7681);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 10.2288);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.MIDDLE_EAST_WEST_1, 7.0329);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.ASIA_EAST_2, 8.7281);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.ASIA_NORTHEAST_2, 8.1981);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.ASIA_NORTHEAST_3, 8.1981);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.ASIA_SOUTH_1, 7.6566);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.ASIA_SOUTH_2, 7.661);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.ASIA_SOUTHEAST_2, 8.543);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 9.0064);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 9.0);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.AFRICA_SOUTH_1, 8.7080);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M300, GCPRegionName.NORTH_AMERICA_SOUTH_1, 6.9680);

    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.WESTERN_US, 8.6769);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.CENTRAL_US, 8.6769);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EASTERN_US, 8.6769);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.WESTERN_EUROPE, 9.3912);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EASTERN_ASIA_PACIFIC, 9.8052);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 11.1566);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 10.4990);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.US_EAST_4, 9.4810);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.US_EAST_5, 8.677);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.US_WEST_2, 10.4201);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.US_WEST_3, 10.4201);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.US_WEST_4, 9.7645);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.US_SOUTH_1, 10.239);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 9.5498);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 9.5498);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.SOUTH_AMERICA_EAST_1, 13.6414);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.SOUTH_AMERICA_WEST_1, 12.4080);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EUROPE_NORTH_1, 9.5510);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EUROPE_WEST_2, 11.0373);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EUROPE_WEST_3, 11.0373);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EUROPE_WEST_4, 9.5508);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EUROPE_WEST_6, 11.9869);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EUROPE_WEST_8, 10.0652);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EUROPE_WEST_9, 10.0652);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EUROPE_WEST_10, 13.3571);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EUROPE_WEST_12, 11.0434);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EUROPE_SOUTHWEST_1, 10.2387);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.EUROPE_CENTRAL_2, 11.0373);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.MIDDLE_EAST_CENTRAL_1, 10.5374);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.MIDDLE_EAST_CENTRAL_2, 13.8774);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.MIDDLE_EAST_WEST_1, 9.5402);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.ASIA_EAST_2, 11.6821);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.ASIA_NORTHEAST_2, 11.1671);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.ASIA_NORTHEAST_3, 11.1671);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.ASIA_SOUTH_1, 10.4195);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.ASIA_SOUTH_2, 10.4195);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.ASIA_SOUTHEAST_2, 11.5985);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 12.2117);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.AUSTRALIA_SOUTHEAST_2, 12.2117);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.AFRICA_SOUTH_1, 11.8050);
    assertRegionPrice(SKU.NDS_GCP_INSTANCE_M400, GCPRegionName.NORTH_AMERICA_SOUTH_1, 9.4540);

    // Storage pricing
    assertGCPStoragePrice(GCPRegionName.WESTERN_US, 0.2267);
    assertGCPStoragePrice(GCPRegionName.CENTRAL_US, 0.2267);
    assertGCPStoragePrice(GCPRegionName.EASTERN_US, 0.2267);
    assertGCPStoragePrice(GCPRegionName.WESTERN_EUROPE, 0.2267);
    assertGCPStoragePrice(GCPRegionName.EASTERN_ASIA_PACIFIC, 0.2267);
    assertGCPStoragePrice(GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 0.2947);
    assertGCPStoragePrice(GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 0.2493);
    assertGCPStoragePrice(GCPRegionName.US_EAST_4, 0.2493);
    assertGCPStoragePrice(GCPRegionName.US_EAST_5, 0.2267);
    assertGCPStoragePrice(GCPRegionName.US_WEST_2, 0.2720);
    assertGCPStoragePrice(GCPRegionName.US_WEST_3, 0.272);
    assertGCPStoragePrice(GCPRegionName.US_WEST_4, 0.249);
    assertGCPStoragePrice(GCPRegionName.US_SOUTH_1, 0.2675);
    assertGCPStoragePrice(GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 0.2493);
    assertGCPStoragePrice(GCPRegionName.NORTH_AMERICA_NORTHEAST_2, 0.249);
    assertGCPStoragePrice(GCPRegionName.SOUTH_AMERICA_EAST_1, 0.3400);
    assertGCPStoragePrice(GCPRegionName.SOUTH_AMERICA_WEST_1, 0.3241);
    assertGCPStoragePrice(GCPRegionName.EUROPE_NORTH_1, 0.2493);
    assertGCPStoragePrice(GCPRegionName.EUROPE_WEST_2, 0.2720);
    assertGCPStoragePrice(GCPRegionName.EUROPE_WEST_3, 0.2720);
    assertGCPStoragePrice(GCPRegionName.EUROPE_WEST_4, 0.2493);
    assertGCPStoragePrice(GCPRegionName.EUROPE_WEST_6, 0.2947);
    assertGCPStoragePrice(GCPRegionName.EUROPE_WEST_8, 0.2629);
    assertGCPStoragePrice(GCPRegionName.EUROPE_WEST_9, 0.2629);
    assertGCPStoragePrice(GCPRegionName.EUROPE_WEST_10, 0.3491);
    assertGCPStoragePrice(GCPRegionName.EUROPE_WEST_12, 0.2720);
    assertGCPStoragePrice(GCPRegionName.EUROPE_SOUTHWEST_1, 0.2675);
    assertGCPStoragePrice(GCPRegionName.EUROPE_CENTRAL_2, 0.272);
    assertGCPStoragePrice(GCPRegionName.MIDDLE_EAST_CENTRAL_1, 0.2754);
    assertGCPStoragePrice(GCPRegionName.MIDDLE_EAST_CENTRAL_2, 0.3627);
    assertGCPStoragePrice(GCPRegionName.MIDDLE_EAST_WEST_1, 0.2493);
    assertGCPStoragePrice(GCPRegionName.ASIA_EAST_2, 0.2493);
    assertGCPStoragePrice(GCPRegionName.ASIA_NORTHEAST_2, 0.2947);
    assertGCPStoragePrice(GCPRegionName.ASIA_NORTHEAST_3, 0.2947);
    assertGCPStoragePrice(GCPRegionName.ASIA_SOUTH_1, 0.2720);
    assertGCPStoragePrice(GCPRegionName.ASIA_SOUTH_2, 0.2720);
    assertGCPStoragePrice(GCPRegionName.ASIA_SOUTHEAST_2, 0.295);
    assertGCPStoragePrice(GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.3067);
    assertGCPStoragePrice(GCPRegionName.AUSTRALIA_SOUTHEAST_2, 0.307);
    assertGCPStoragePrice(GCPRegionName.AFRICA_SOUTH_1, 0.296);
    assertGCPStoragePrice(GCPRegionName.NORTH_AMERICA_SOUTH_1, 0.247);
  }

  @Test
  public void testAllFreeSkus() {
    assertFalse(SKU.ALL_FREE_SKUS.contains(SKU.MMS_STANDARD));
    assertFalse(SKU.ALL_FREE_SKUS.contains(SKU.MMS_BACKUP_STORAGE));
    assertFalse(SKU.ALL_FREE_SKUS.contains(SKU.NDS_AWS_INSTANCE_M10));
    assertFalse(SKU.ALL_FREE_SKUS.contains(SKU.NDS_AWS_INSTANCE_M20));
    assertFalse(SKU.ALL_FREE_SKUS.contains(SKU.NDS_AWS_INSTANCE_M50));
    assertFalse(SKU.ALL_FREE_SKUS.contains(SKU.NDS_AWS_INSTANCE_M100));
    assertFalse(SKU.ALL_FREE_SKUS.contains(SKU.NDS_GCP_INSTANCE_M10));
    assertFalse(SKU.ALL_FREE_SKUS.contains(SKU.NDS_GCP_INSTANCE_M60));

    assertTrue(SKU.ALL_FREE_SKUS.contains(SKU.MMS_FREE_TIER));
    assertTrue(SKU.ALL_FREE_SKUS.contains(SKU.MMS_FREE_TRIAL));
    assertTrue(SKU.ALL_FREE_SKUS.contains(SKU.NDS_FREE_INSTANCE_M0));
    assertTrue(SKU.ALL_FREE_SKUS.contains(SKU.NDS_FREE_SUPPORT));
  }

  @Test
  public void testAtlasAzurePricing() {

    // Check instance pricing
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.US_CENTRAL, 0.0319);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.US_EAST, 0.0264);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.US_EAST_2, 0.0251);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.US_NORTH_CENTRAL, 0.0264);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.US_WEST, 0.0300);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.US_SOUTH_CENTRAL, 0.0317);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.EUROPE_NORTH, 0.0311);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.EUROPE_WEST, 0.0293);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.US_WEST_CENTRAL, 0.0317);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.US_WEST_2, 0.0251);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.US_WEST_3, 0.0306);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.CANADA_EAST, 0.0286);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.CANADA_CENTRAL, 0.0298);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.BRAZIL_SOUTH, 0.0430);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.BRAZIL_SOUTHEAST, 0.0553);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.AUSTRALIA_EAST, 0.0323);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.0327);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.GERMANY_NORTH, 0.0383);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.FRANCE_SOUTH, 0.0445);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.GERMANY_WEST_CENTRAL, 0.0299);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.SWITZERLAND_NORTH, 0.0354);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.SWITZERLAND_WEST, 0.0452);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.SWEDEN_CENTRAL, 0.0277);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.SWEDEN_SOUTH, 0.0360);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.UK_SOUTH, 0.0292);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.UK_WEST, 0.0295);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.INDIA_CENTRAL, 0.0323);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.INDIA_WEST, 0.0347);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.INDIA_SOUTH, 0.0385);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.ASIA_EAST, 0.0383);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.JAPAN_EAST, 0.0337);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.JAPAN_WEST, 0.0368);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.ASIA_SOUTH_EAST, 0.0314);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.KOREA_CENTRAL, 0.0332);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.KOREA_SOUTH, 0.0299);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.FRANCE_CENTRAL, 0.0334);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.SOUTH_AFRICA_NORTH, 0.0344);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.SOUTH_AFRICA_WEST, 0.0420);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.UAE_NORTH, 0.0309);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.UAE_CENTRAL, 0.0399);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.NORWAY_EAST, 0.0330);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.NORWAY_WEST, 0.0427);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.AUSTRALIA_CENTRAL, 0.0330);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.AUSTRALIA_CENTRAL_2, 0.0330);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.QATAR_CENTRAL, 0.0315);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.POLAND_CENTRAL, 0.0305);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.ISRAEL_CENTRAL, 0.0317);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.ITALY_NORTH, 0.0305);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.SPAIN_CENTRAL, 0.0310);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.MEXICO_CENTRAL, 0.0279);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M10, AzureRegionName.NEW_ZEALAND_NORTH, 0.0373);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.US_CENTRAL, 0.0749);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.US_EAST, 0.0622);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.US_EAST_2, 0.0622);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.US_NORTH_CENTRAL, 0.0645);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.US_WEST, 0.0743);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.US_SOUTH_CENTRAL, 0.0745);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.EUROPE_NORTH, 0.0777);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.EUROPE_WEST, 0.0723);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.US_WEST_CENTRAL, 0.0745);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.US_WEST_2, 0.0622);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.US_WEST_3, 0.0753);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.CANADA_EAST, 0.0705);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.CANADA_CENTRAL, 0.0703);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.BRAZIL_SOUTH, 0.1049);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.BRAZIL_SOUTHEAST, 0.1354);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.AUSTRALIA_EAST, 0.0798);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.0804);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.GERMANY_NORTH, 0.0944);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.FRANCE_SOUTH, 0.1109);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.GERMANY_WEST_CENTRAL, 0.0734);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.SWEDEN_CENTRAL, 0.0675);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.SWEDEN_SOUTH, 0.0878);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.SWITZERLAND_NORTH, 0.0740);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.SWITZERLAND_WEST, 0.1081);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.UK_SOUTH, 0.0719);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.UK_WEST, 0.0724);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.INDIA_CENTRAL, 0.0793);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.INDIA_WEST, 0.0854);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.INDIA_SOUTH, 0.0951);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.ASIA_EAST, 0.0949);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.JAPAN_EAST, 0.0829);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.JAPAN_WEST, 0.0908);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.ASIA_SOUTH_EAST, 0.0782);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.KOREA_CENTRAL, 0.0820);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.KOREA_SOUTH, 0.0736);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.FRANCE_CENTRAL, 0.0831);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.SOUTH_AFRICA_NORTH, 0.0847);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.SOUTH_AFRICA_WEST, 0.1032);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.UAE_NORTH, 0.0759);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.UAE_CENTRAL, 0.0984);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.NORWAY_EAST, 0.0808);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.NORWAY_WEST, 0.1049);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.AUSTRALIA_CENTRAL, 0.0809);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.AUSTRALIA_CENTRAL_2, 0.0809);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.QATAR_CENTRAL, 0.0775);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.POLAND_CENTRAL, 0.0740);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.ISRAEL_CENTRAL, 0.0777);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.ITALY_NORTH, 0.0747);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.SPAIN_CENTRAL, 0.0772);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.MEXICO_CENTRAL, 0.0679);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M20, AzureRegionName.NEW_ZEALAND_NORTH, 0.0935);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.US_CENTRAL, 0.1986);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.US_EAST, 0.1685);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.US_EAST_2, 0.1671);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.US_NORTH_CENTRAL, 0.1740);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.US_WEST, 0.2165);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.US_SOUTH_CENTRAL, 0.1969);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.EUROPE_NORTH, 0.1988);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.EUROPE_WEST, 0.2244);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.US_WEST_CENTRAL, 0.1969);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.US_WEST_2, 0.1671);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.US_WEST_3, 0.1999);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.CANADA_EAST, 0.1896);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.CANADA_CENTRAL, 0.1896);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.BRAZIL_SOUTH, 0.2805);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.BRAZIL_SOUTHEAST, 0.3596);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.AUSTRALIA_EAST, 0.2266);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.2270);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.GERMANY_NORTH, 0.2577);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.FRANCE_SOUTH, 0.2421);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.GERMANY_WEST_CENTRAL, 0.1985);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.SWEDEN_CENTRAL, 0.1794);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.SWEDEN_SOUTH, 0.2334);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.SWITZERLAND_NORTH, 0.2467);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.SWITZERLAND_WEST, 0.3196);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.UK_SOUTH, 0.2172);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.UK_WEST, 0.2176);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.INDIA_CENTRAL, 0.1805);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.INDIA_WEST, 0.2070);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.INDIA_SOUTH, 0.2356);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.ASIA_EAST, 0.2257);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.JAPAN_EAST, 0.2390);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.JAPAN_WEST, 0.2380);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.ASIA_SOUTH_EAST, 0.2091);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.KOREA_CENTRAL, 0.2114);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.KOREA_SOUTH, 0.1798);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.FRANCE_CENTRAL, 0.2149);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.SOUTH_AFRICA_NORTH, 0.2277);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.SOUTH_AFRICA_WEST, 0.2715);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.UAE_NORTH, 0.2015);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.UAE_CENTRAL, 0.2617);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.NORWAY_EAST, 0.2476);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.NORWAY_WEST, 0.3225);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.AUSTRALIA_CENTRAL, 0.2273);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.AUSTRALIA_CENTRAL_2, 0.2273);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.QATAR_CENTRAL, 0.2041);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.POLAND_CENTRAL, 0.2198);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.ISRAEL_CENTRAL, 0.2057);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.ITALY_NORTH, 0.1957);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.SPAIN_CENTRAL, 0.2103);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.MEXICO_CENTRAL, 0.1833);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M30, AzureRegionName.NEW_ZEALAND_NORTH, 0.2447);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.US_CENTRAL, 0.3873);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.US_EAST, 0.3288);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.US_EAST_2, 0.3261);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.US_NORTH_CENTRAL, 0.3384);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.US_WEST, 0.4232);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.US_SOUTH_CENTRAL, 0.3844);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.EUROPE_NORTH, 0.3888);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.EUROPE_WEST, 0.4390);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.US_WEST_CENTRAL, 0.3844);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.US_WEST_2, 0.3261);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.US_WEST_3, 0.3895);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.CANADA_EAST, 0.3695);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.CANADA_CENTRAL, 0.3695);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.BRAZIL_SOUTH, 0.5449);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.BRAZIL_SOUTHEAST, 0.6986);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.AUSTRALIA_EAST, 0.4425);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.4429);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.GERMANY_NORTH, 0.5019);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.FRANCE_SOUTH, 0.4708);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.GERMANY_WEST_CENTRAL, 0.3865);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.SWEDEN_CENTRAL, 0.3482);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.SWEDEN_SOUTH, 0.4526);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.SWITZERLAND_NORTH, 0.4813);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.SWITZERLAND_WEST, 0.6245);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.UK_SOUTH, 0.4242);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.UK_WEST, 0.4246);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.INDIA_CENTRAL, 0.3500);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.INDIA_WEST, 0.4029);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.INDIA_SOUTH, 0.4598);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.ASIA_EAST, 0.4391);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.JAPAN_EAST, 0.4664);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.JAPAN_WEST, 0.4639);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.ASIA_SOUTH_EAST, 0.4086);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.KOREA_CENTRAL, 0.4121);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.KOREA_SOUTH, 0.3505);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.FRANCE_CENTRAL, 0.4197);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.SOUTH_AFRICA_NORTH, 0.4438);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.SOUTH_AFRICA_WEST, 0.5288);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.UAE_NORTH, 0.3924);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.UAE_CENTRAL, 0.5098);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.NORWAY_EAST, 0.4839);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.NORWAY_WEST, 0.6285);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.AUSTRALIA_CENTRAL, 0.4433);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.AUSTRALIA_CENTRAL_2, 0.4433);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.QATAR_CENTRAL, 0.3971);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.POLAND_CENTRAL, 0.4281);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.ISRAEL_CENTRAL, 0.4008);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.ITALY_NORTH, 0.3797);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.SPAIN_CENTRAL, 0.4111);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.MEXICO_CENTRAL, 0.3547);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M40, AzureRegionName.NEW_ZEALAND_NORTH, 0.4779);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.US_CENTRAL, 0.7639);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.US_EAST, 0.6483);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.US_EAST_2, 0.6423);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.US_NORTH_CENTRAL, 0.6574);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.US_WEST, 0.8357);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.US_SOUTH_CENTRAL, 0.7583);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.EUROPE_NORTH, 0.7676);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.EUROPE_WEST, 0.8485);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.US_WEST_CENTRAL, 0.7583);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.US_WEST_2, 0.6423);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.US_WEST_3, 0.7675);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.CANADA_EAST, 0.7142);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.CANADA_CENTRAL, 0.7219);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.BRAZIL_SOUTH, 0.9806);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.BRAZIL_SOUTHEAST, 1.3752);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.AUSTRALIA_EAST, 0.8598);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.8598);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.GERMANY_NORTH, 0.9725);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.FRANCE_SOUTH, 0.9266);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.GERMANY_WEST_CENTRAL, 0.7478);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.SWEDEN_CENTRAL, 0.6848);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.SWEDEN_SOUTH, 0.8901);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.SWITZERLAND_NORTH, 0.9316);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.SWITZERLAND_WEST, 1.2113);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.UK_SOUTH, 0.8282);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.UK_WEST, 0.8282);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.INDIA_CENTRAL, 0.6879);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.INDIA_WEST, 0.7932);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.INDIA_SOUTH, 0.9063);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.ASIA_EAST, 0.8648);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.JAPAN_EAST, 0.9022);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.JAPAN_WEST, 0.8934);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.ASIA_SOUTH_EAST, 0.8031);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.KOREA_CENTRAL, 0.7635);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.KOREA_SOUTH, 0.6277);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.FRANCE_CENTRAL, 0.8278);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.SOUTH_AFRICA_NORTH, 0.8611);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.SOUTH_AFRICA_WEST, 1.0413);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.UAE_NORTH, 0.7486);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.UAE_CENTRAL, 0.9730);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.NORWAY_EAST, 0.9327);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.NORWAY_WEST, 1.2402);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.AUSTRALIA_CENTRAL, 0.8741);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.AUSTRALIA_CENTRAL_2, 0.8741);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.QATAR_CENTRAL, 0.7724);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.POLAND_CENTRAL, 0.8319);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.ISRAEL_CENTRAL, 0.7791);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.ITALY_NORTH, 0.7376);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.SPAIN_CENTRAL, 0.8103);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.MEXICO_CENTRAL, 0.6970);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M50, AzureRegionName.NEW_ZEALAND_NORTH, 0.9416);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.US_CENTRAL, 1.4598);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.US_EAST, 1.2304);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.US_EAST_2, 1.2250);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.US_NORTH_CENTRAL, 1.2647);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.US_WEST, 1.6036);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.US_SOUTH_CENTRAL, 1.4542);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.EUROPE_NORTH, 1.4682);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.EUROPE_WEST, 1.6572);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.US_WEST_CENTRAL, 1.4542);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.US_WEST_2, 1.2250);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.US_WEST_3, 1.4665);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.CANADA_EAST, 1.3883);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.CANADA_CENTRAL, 1.3889);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.BRAZIL_SOUTH, 1.8727);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.BRAZIL_SOUTHEAST, 2.6000);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.AUSTRALIA_EAST, 1.6778);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.AUSTRALIA_SOUTH_EAST, 1.6782);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.GERMANY_NORTH, 1.8825);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.FRANCE_SOUTH, 1.7489);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.GERMANY_WEST_CENTRAL, 1.4485);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.SWEDEN_CENTRAL, 1.2952);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.SWEDEN_SOUTH, 1.6839);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.SWITZERLAND_NORTH, 1.8095);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.SWITZERLAND_WEST, 2.3515);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.UK_SOUTH, 1.5930);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.UK_WEST, 1.5935);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.INDIA_CENTRAL, 1.3068);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.INDIA_WEST, 1.5187);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.INDIA_SOUTH, 1.7487);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.ASIA_EAST, 1.6535);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.JAPAN_EAST, 1.7611);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.JAPAN_WEST, 1.7519);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.ASIA_SOUTH_EAST, 1.5452);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.KOREA_CENTRAL, 1.4877);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.KOREA_SOUTH, 1.2097);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.FRANCE_CENTRAL, 1.5752);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.SOUTH_AFRICA_NORTH, 1.6663);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.SOUTH_AFRICA_WEST, 1.9795);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.UAE_NORTH, 1.4508);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.UAE_CENTRAL, 1.8862);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.NORWAY_EAST, 1.8219);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.NORWAY_WEST, 2.3757);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.AUSTRALIA_CENTRAL, 1.6785);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.AUSTRALIA_CENTRAL_2, 1.6785);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.QATAR_CENTRAL, 1.4831);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.POLAND_CENTRAL, 1.5947);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.ISRAEL_CENTRAL, 1.4963);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.ITALY_NORTH, 1.4121);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.SPAIN_CENTRAL, 1.5493);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.MEXICO_CENTRAL, 1.3280);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M60, AzureRegionName.NEW_ZEALAND_NORTH, 1.8118);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.US_CENTRAL, 2.9066);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.US_EAST, 2.4494);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.US_EAST_2, 2.4391);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.US_NORTH_CENTRAL, 2.5165);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.US_WEST, 3.1943);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.US_SOUTH_CENTRAL, 2.8960);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.EUROPE_NORTH, 2.9242);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.EUROPE_WEST, 3.3076);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.US_WEST_CENTRAL, 2.8960);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.US_WEST_2, 2.4391);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.US_WEST_3, 2.9196);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.CANADA_EAST, 2.7649);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.CANADA_CENTRAL, 2.7649);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.BRAZIL_SOUTH, 3.8414);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.BRAZIL_SOUTHEAST, 5.1723);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.AUSTRALIA_EAST, 3.3417);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.AUSTRALIA_SOUTH_EAST, 3.3421);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.GERMANY_NORTH, 3.7474);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.FRANCE_SOUTH, 3.4795);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.GERMANY_WEST_CENTRAL, 2.8831);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.SWEDEN_CENTRAL, 2.5765);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.SWEDEN_SOUTH, 3.3497);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.SWITZERLAND_NORTH, 3.6027);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.SWITZERLAND_WEST, 4.6824);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.UK_SOUTH, 3.1720);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.UK_WEST, 3.1724);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.INDIA_CENTRAL, 2.5992);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.INDIA_WEST, 3.0404);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.INDIA_SOUTH, 3.4828);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.ASIA_EAST, 3.2913);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.JAPAN_EAST, 3.5079);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.JAPAN_WEST, 3.4968);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.ASIA_SOUTH_EAST, 3.0776);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.KOREA_CENTRAL, 3.0629);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.KOREA_SOUTH, 2.4833);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.FRANCE_CENTRAL, 3.1364);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.SOUTH_AFRICA_NORTH, 3.3170);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.SOUTH_AFRICA_WEST, 3.9397);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.UAE_NORTH, 2.9298);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.UAE_CENTRAL, 3.8084);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.NORWAY_EAST, 3.6392);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.NORWAY_WEST, 4.7314);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.AUSTRALIA_CENTRAL, 3.3424);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.AUSTRALIA_CENTRAL_2, 3.3424);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.QATAR_CENTRAL, 2.9576);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.POLAND_CENTRAL, 3.1805);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.ISRAEL_CENTRAL, 2.9845);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.ITALY_NORTH, 2.8160);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.SPAIN_CENTRAL, 3.0854);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.MEXICO_CENTRAL, 2.6426);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M80, AzureRegionName.NEW_ZEALAND_NORTH, 3.6080);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.US_CENTRAL, 2.6494);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.US_EAST, 2.6494);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.US_EAST_2, 2.1448);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.US_NORTH_CENTRAL, 2.6494);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.US_WEST, 2.6494);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.US_SOUTH_CENTRAL, 2.3819);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.EUROPE_NORTH, 2.6494);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.EUROPE_WEST, 2.7151);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.US_WEST_CENTRAL, 2.3785);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.US_WEST_2, 2.1448);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.US_WEST_3, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.CANADA_EAST, 2.7257);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.CANADA_CENTRAL, 2.9744);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.BRAZIL_SOUTH, 3.3794);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.BRAZIL_SOUTHEAST, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.AUSTRALIA_EAST, 2.8510);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.AUSTRALIA_SOUTH_EAST, 2.8510);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.GERMANY_NORTH, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.FRANCE_SOUTH, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.GERMANY_WEST_CENTRAL, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.SWEDEN_CENTRAL, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.SWEDEN_SOUTH, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.SWITZERLAND_NORTH, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.SWITZERLAND_WEST, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.UK_SOUTH, 3.3474);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.UK_WEST, 3.3474);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.INDIA_CENTRAL, 2.7088);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.INDIA_WEST, 2.7088);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.INDIA_SOUTH, 2.7047);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.ASIA_EAST, 3.2739);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.JAPAN_EAST, 3.2758);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.JAPAN_WEST, 2.8510);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.ASIA_SOUTH_EAST, 2.7324);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.KOREA_CENTRAL, 2.8492);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.KOREA_SOUTH, 2.5691);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.FRANCE_CENTRAL, 3.3474);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.SOUTH_AFRICA_NORTH, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.SOUTH_AFRICA_WEST, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.UAE_NORTH, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.UAE_CENTRAL, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.NORWAY_EAST, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.NORWAY_WEST, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.AUSTRALIA_CENTRAL, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.AUSTRALIA_CENTRAL_2, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.QATAR_CENTRAL, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.POLAND_CENTRAL, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.ISRAEL_CENTRAL, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.ITALY_NORTH, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.SPAIN_CENTRAL, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.MEXICO_CENTRAL, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M90, AzureRegionName.NEW_ZEALAND_NORTH, 0.00);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.US_CENTRAL, 5.3556);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.US_EAST, 4.4967);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.US_EAST_2, 4.4870);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.US_NORTH_CENTRAL, 4.6217);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.US_WEST, 5.8975);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.US_SOUTH_CENTRAL, 5.3457);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.EUROPE_NORTH, 5.3897);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.EUROPE_WEST, 6.0999);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.US_WEST_CENTRAL, 5.3457);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.US_WEST_2, 4.4870);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.US_WEST_3, 5.3796);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.CANADA_EAST, 5.0892);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.CANADA_CENTRAL, 5.0892);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.BRAZIL_SOUTH, 7.0753);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.BRAZIL_SOUTHEAST, 9.4785);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.AUSTRALIA_EAST, 6.1739);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.AUSTRALIA_SOUTH_EAST, 6.1743);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.GERMANY_NORTH, 6.8899);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.FRANCE_SOUTH, 6.3699);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.GERMANY_WEST_CENTRAL, 5.3003);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.SWEDEN_CENTRAL, 4.7232);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.SWEDEN_SOUTH, 6.1400);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.SWITZERLAND_NORTH, 6.6300);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.SWITZERLAND_WEST, 8.6177);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.UK_SOUTH, 5.8326);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.UK_WEST, 5.8330);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.INDIA_CENTRAL, 4.7759);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.INDIA_WEST, 5.5905);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.INDIA_SOUTH, 6.4492);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.ASIA_EAST, 6.0669);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.JAPAN_EAST, 6.4701);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.JAPAN_WEST, 6.4596);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.ASIA_SOUTH_EAST, 5.6777);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.KOREA_CENTRAL, 5.7000);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.KOREA_SOUTH, 4.5905);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.FRANCE_CENTRAL, 5.7656);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.SOUTH_AFRICA_NORTH, 6.1032);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.SOUTH_AFRICA_WEST, 7.2400);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.UAE_NORTH, 5.3889);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.UAE_CENTRAL, 7.0054);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.NORWAY_EAST, 6.7108);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.NORWAY_WEST, 8.7235);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.AUSTRALIA_CENTRAL, 6.1746);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.AUSTRALIA_CENTRAL_2, 6.1746);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.QATAR_CENTRAL, 5.4497);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.POLAND_CENTRAL, 5.8556);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.ISRAEL_CENTRAL, 5.5001);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.ITALY_NORTH, 5.1808);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.SPAIN_CENTRAL, 5.6827);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.MEXICO_CENTRAL, 4.8561);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_M200, AzureRegionName.NEW_ZEALAND_NORTH, 6.6681);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.US_CENTRAL, 0.3143);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.US_EAST, 0.2717);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.US_EAST_2, 0.2767);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.US_NORTH_CENTRAL, 0.2731);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.US_WEST, 0.3083);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.US_SOUTH_CENTRAL, 0.3113);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.EUROPE_NORTH, 0.2998);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.EUROPE_WEST, 0.3331);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.US_WEST_CENTRAL, 0.3113);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.US_WEST_2, 0.2689);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.US_WEST_3, 0.3142);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.CANADA_EAST, 0.3046);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.CANADA_CENTRAL, 0.3046);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.BRAZIL_SOUTH, 0.5261);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.BRAZIL_SOUTHEAST, 0.6503);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.AUSTRALIA_EAST, 0.3308);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.3311);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.GERMANY_NORTH, 0.4218);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.FRANCE_SOUTH, 0.3954);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.GERMANY_WEST_CENTRAL, 0.3246);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.SWEDEN_CENTRAL, 0.2865);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.SWEDEN_SOUTH, 0.3723);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.SWITZERLAND_NORTH, 0.3928);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.SWITZERLAND_WEST, 0.5099);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.UK_SOUTH, 0.3295);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.UK_WEST, 0.3299);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.INDIA_CENTRAL, 0.2899);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.INDIA_WEST, 0.3168);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.INDIA_SOUTH, 0.3527);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.ASIA_EAST, 0.3542);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.JAPAN_EAST, 0.3364);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.JAPAN_WEST, 0.3339);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.ASIA_SOUTH_EAST, 0.3300);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.KOREA_CENTRAL, 0.3311);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.KOREA_SOUTH, 0.2899);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.FRANCE_CENTRAL, 0.3295);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.SOUTH_AFRICA_NORTH, 0.3614);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.SOUTH_AFRICA_WEST, 0.4493);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.UAE_NORTH, 0.3228);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.UAE_CENTRAL, 0.4201);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.NORWAY_EAST, 0.3673);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.NORWAY_WEST, 0.4777);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.AUSTRALIA_CENTRAL, 0.3315);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.AUSTRALIA_CENTRAL_2, 0.3315);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.QATAR_CENTRAL, 0.3264);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.POLAND_CENTRAL, 0.3436);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.ISRAEL_CENTRAL, 0.3320);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.ITALY_NORTH, 0.3193);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.SPAIN_CENTRAL, 0.3334);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.MEXICO_CENTRAL, 0.3072);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R40, AzureRegionName.NEW_ZEALAND_NORTH, 0.3704);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.US_CENTRAL, 0.6177);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.US_EAST, 0.5341);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.US_EAST_2, 0.5443);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.US_NORTH_CENTRAL, 0.5356);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.US_WEST, 0.6060);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.US_SOUTH_CENTRAL, 0.6121);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.EUROPE_NORTH, 0.5897);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.EUROPE_WEST, 0.6553);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.US_WEST_CENTRAL, 0.6121);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.US_WEST_2, 0.5287);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.US_WEST_3, 0.6168);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.CANADA_EAST, 0.5984);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.CANADA_CENTRAL, 0.5984);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.BRAZIL_SOUTH, 1.0341);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.BRAZIL_SOUTHEAST, 1.2769);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.AUSTRALIA_EAST, 0.6487);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.6491);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.GERMANY_NORTH, 0.8280);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.FRANCE_SOUTH, 0.7758);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.GERMANY_WEST_CENTRAL, 0.6376);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.SWEDEN_CENTRAL, 0.5614);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.SWEDEN_SOUTH, 0.7293);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.SWITZERLAND_NORTH, 0.7719);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.SWITZERLAND_WEST, 1.0020);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.UK_SOUTH, 0.6475);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.UK_WEST, 0.6479);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.INDIA_CENTRAL, 0.5677);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.INDIA_WEST, 0.6215);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.INDIA_SOUTH, 0.6927);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.ASIA_EAST, 0.6949);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.JAPAN_EAST, 0.6600);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.JAPAN_WEST, 0.6545);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.ASIA_SOUTH_EAST, 0.6494);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.KOREA_CENTRAL, 0.6504);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.KOREA_SOUTH, 0.5689);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.FRANCE_CENTRAL, 0.6475);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.SOUTH_AFRICA_NORTH, 0.7098);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.SOUTH_AFRICA_WEST, 0.8832);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.UAE_NORTH, 0.6338);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.UAE_CENTRAL, 0.8241);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.NORWAY_EAST, 0.7216);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.NORWAY_WEST, 0.9388);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.AUSTRALIA_CENTRAL, 0.6494);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.AUSTRALIA_CENTRAL_2, 0.6494);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.QATAR_CENTRAL, 0.6409);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.POLAND_CENTRAL, 0.6744);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.ISRAEL_CENTRAL, 0.6522);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.ITALY_NORTH, 0.6268);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.SPAIN_CENTRAL, 0.6559);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.MEXICO_CENTRAL, 0.6005);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R50, AzureRegionName.NEW_ZEALAND_NORTH, 0.7254);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.US_CENTRAL, 1.1666);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.US_EAST, 1.0020);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.US_EAST_2, 1.0278);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.US_NORTH_CENTRAL, 1.0035);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.US_WEST, 1.1087);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.US_SOUTH_CENTRAL, 1.1610);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.EUROPE_NORTH, 1.1124);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.EUROPE_WEST, 1.2370);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.US_WEST_CENTRAL, 1.1610);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.US_WEST_2, 0.9966);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.US_WEST_3, 1.1059);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.CANADA_EAST, 1.1291);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.CANADA_CENTRAL, 1.1291);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.BRAZIL_SOUTH, 1.9517);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.BRAZIL_SOUTHEAST, 2.4035);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.AUSTRALIA_EAST, 1.2286);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.AUSTRALIA_SOUTH_EAST, 1.2290);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.GERMANY_NORTH, 1.5600);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.FRANCE_SOUTH, 1.4457);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.GERMANY_WEST_CENTRAL, 1.2009);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.SWEDEN_CENTRAL, 1.0485);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.SWEDEN_SOUTH, 1.3632);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.SWITZERLAND_NORTH, 1.4550);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.SWITZERLAND_WEST, 1.8898);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.UK_SOUTH, 1.2146);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.UK_WEST, 1.2150);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.INDIA_CENTRAL, 1.0664);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.INDIA_WEST, 1.1741);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.INDIA_SOUTH, 1.3204);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.ASIA_EAST, 1.3138);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.JAPAN_EAST, 1.2416);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.JAPAN_WEST, 1.2361);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.ASIA_SOUTH_EAST, 1.2310);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.KOREA_CENTRAL, 1.2320);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.KOREA_SOUTH, 1.0698);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.FRANCE_CENTRAL, 1.2146);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.SOUTH_AFRICA_NORTH, 1.3360);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.SOUTH_AFRICA_WEST, 1.6626);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.UAE_NORTH, 1.1947);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.UAE_CENTRAL, 1.5523);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.NORWAY_EAST, 1.3615);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.NORWAY_WEST, 1.7695);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.AUSTRALIA_CENTRAL, 1.2293);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.AUSTRALIA_CENTRAL_2, 1.2293);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.QATAR_CENTRAL, 1.2063);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.POLAND_CENTRAL, 1.2648);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.ISRAEL_CENTRAL, 1.2289);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.ITALY_NORTH, 1.1774);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.SPAIN_CENTRAL, 1.2398);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.MEXICO_CENTRAL, 1.1316);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R60, AzureRegionName.NEW_ZEALAND_NORTH, 1.3818);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.US_CENTRAL, 2.3212);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.US_EAST, 1.9926);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.US_EAST_2, 2.0446);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.US_NORTH_CENTRAL, 1.9941);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.US_WEST, 2.2044);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.US_SOUTH_CENTRAL, 2.3095);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.EUROPE_NORTH, 2.2126);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.EUROPE_WEST, 2.4607);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.US_WEST_CENTRAL, 2.3095);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.US_WEST_2, 1.9823);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.US_WEST_3, 2.1983);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.CANADA_EAST, 2.2454);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.CANADA_CENTRAL, 2.2454);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.BRAZIL_SOUTH, 3.8807);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.BRAZIL_SOUTHEAST, 4.7797);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.AUSTRALIA_EAST, 2.4444);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.AUSTRALIA_SOUTH_EAST, 2.4448);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.GERMANY_NORTH, 3.1035);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.FRANCE_SOUTH, 2.8741);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.GERMANY_WEST_CENTRAL, 2.3877);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.SWEDEN_CENTRAL, 2.0830);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.SWEDEN_SOUTH, 2.7084);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.SWITZERLAND_NORTH, 2.8937);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.SWITZERLAND_WEST, 3.7604);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.UK_SOUTH, 2.4150);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.UK_WEST, 2.4155);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.INDIA_CENTRAL, 2.1185);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.INDIA_WEST, 2.3510);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.INDIA_SOUTH, 2.6262);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.ASIA_EAST, 2.6117);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.JAPAN_EAST, 2.4678);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.JAPAN_WEST, 2.4566);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.ASIA_SOUTH_EAST, 2.4490);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.KOREA_CENTRAL, 2.4500);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.KOREA_SOUTH, 2.1264);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.FRANCE_CENTRAL, 2.4150);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.SOUTH_AFRICA_NORTH, 2.6575);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.SOUTH_AFRICA_WEST, 3.3065);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.UAE_NORTH, 2.3753);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.UAE_CENTRAL, 3.0865);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.NORWAY_EAST, 2.7075);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.NORWAY_WEST, 3.5189);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.AUSTRALIA_CENTRAL, 2.4451);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.AUSTRALIA_CENTRAL_2, 2.4451);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.QATAR_CENTRAL, 2.3997);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.POLAND_CENTRAL, 2.5141);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.ISRAEL_CENTRAL, 2.4435);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.ITALY_NORTH, 2.3407);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.SPAIN_CENTRAL, 2.4663);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.MEXICO_CENTRAL, 2.2512);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R80, AzureRegionName.NEW_ZEALAND_NORTH, 2.7477);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.US_CENTRAL, 4.2537);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.US_EAST, 3.6368);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.US_EAST_2, 3.7443);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.US_NORTH_CENTRAL, 3.6382);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.US_WEST, 4.0341);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.US_SOUTH_CENTRAL, 4.2427);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.EUROPE_NORTH, 4.0502);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.EUROPE_WEST, 4.5057);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.US_WEST_CENTRAL, 4.2428);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.US_WEST_2, 3.6271);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.US_WEST_3, 4.0221);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.CANADA_EAST, 4.1113);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.CANADA_CENTRAL, 4.1113);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.BRAZIL_SOUTH, 7.1062);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.BRAZIL_SOUTHEAST, 8.7396);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.AUSTRALIA_EAST, 4.4849);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.AUSTRALIA_SOUTH_EAST, 4.4853);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.GERMANY_NORTH, 5.6778);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.FRANCE_SOUTH, 5.2333);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.GERMANY_WEST_CENTRAL, 4.3678);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.SWEDEN_CENTRAL, 3.7942);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.SWEDEN_SOUTH, 4.9323);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.SWITZERLAND_NORTH, 5.2953);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.SWITZERLAND_WEST, 6.8824);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.UK_SOUTH, 4.4078);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.UK_WEST, 4.4082);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.INDIA_CENTRAL, 3.8378);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.INDIA_WEST, 4.2928);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.INDIA_SOUTH, 4.8356);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.ASIA_EAST, 4.7878);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.JAPAN_EAST, 4.5122);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.JAPAN_WEST, 4.5017);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.ASIA_SOUTH_EAST, 4.4945);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.KOREA_CENTRAL, 4.4955);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.KOREA_SOUTH, 3.8869);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.FRANCE_CENTRAL, 4.4078);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.SOUTH_AFRICA_NORTH, 4.8606);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.SOUTH_AFRICA_WEST, 6.0472);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.UAE_NORTH, 4.3442);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.UAE_CENTRAL, 5.6475);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.NORWAY_EAST, 4.9571);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.NORWAY_WEST, 6.4445);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.AUSTRALIA_CENTRAL, 4.4856);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.AUSTRALIA_CENTRAL_2, 4.4856);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.QATAR_CENTRAL, 4.3906);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.POLAND_CENTRAL, 4.5914);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.ISRAEL_CENTRAL, 4.4731);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.ITALY_NORTH, 4.2782);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.SPAIN_CENTRAL, 4.5191);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.MEXICO_CENTRAL, 4.1198);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R200, AzureRegionName.NEW_ZEALAND_NORTH, 5.0587);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.US_CENTRAL, 6.3193);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.US_EAST, 5.4864);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.US_EAST_2, 5.4676);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.US_NORTH_CENTRAL, 5.4878);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.US_WEST, 6.0735);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.US_SOUTH_CENTRAL, 6.2971);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.EUROPE_NORTH, 6.1148);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.EUROPE_WEST, 6.5948);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.US_WEST_CENTRAL, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.US_WEST_2, 5.4676);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.US_WEST_3, 6.0743);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.CANADA_EAST, 5.9884);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.CANADA_CENTRAL, 5.9884);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.BRAZIL_SOUTH, 10.1996);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.BRAZIL_SOUTHEAST, 13.2035);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.AUSTRALIA_EAST, 6.5331);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.AUSTRALIA_SOUTH_EAST, 6.5334);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.GERMANY_NORTH, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.FRANCE_SOUTH, 8.8155);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.GERMANY_WEST_CENTRAL, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.SWEDEN_CENTRAL, 5.7372);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.SWEDEN_SOUTH, 7.4585);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.SWITZERLAND_NORTH, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.SWITZERLAND_WEST, 0.00);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.UK_SOUTH, 6.4541);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.UK_WEST, 6.4545);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.INDIA_CENTRAL, 5.9373);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.INDIA_WEST, 6.5631);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.INDIA_SOUTH, 7.1727);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.ASIA_EAST, 7.2266);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.JAPAN_EAST, 6.6067);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.JAPAN_WEST, 6.9227);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.ASIA_SOUTH_EAST, 6.9110);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.KOREA_CENTRAL, 6.5751);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.KOREA_SOUTH, 5.8544);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.FRANCE_CENTRAL, 6.7801);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.SOUTH_AFRICA_NORTH, 7.3273);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.SOUTH_AFRICA_WEST, 9.1163);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.UAE_NORTH, 6.5622);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.UAE_CENTRAL, 8.5298);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.NORWAY_EAST, 7.2555);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.NORWAY_WEST, 9.4310);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.AUSTRALIA_CENTRAL, 6.5338);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.AUSTRALIA_CENTRAL_2, 6.5338);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.QATAR_CENTRAL, 6.6147);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.POLAND_CENTRAL, 6.9146);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.ISRAEL_CENTRAL, 6.7393);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.ITALY_NORTH, 6.4484);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.SPAIN_CENTRAL, 6.5978);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.MEXICO_CENTRAL, 6.0139);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R300, AzureRegionName.NEW_ZEALAND_NORTH, 7.3719);

    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.US_CENTRAL, 7.8064);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.US_EAST, 6.6746);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.US_EAST_2, 6.8182);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.US_NORTH_CENTRAL, 6.6759);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.US_WEST, 7.4022);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.US_SOUTH_CENTRAL, 7.7864);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.EUROPE_NORTH, 7.4318);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.EUROPE_WEST, 8.0202);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.US_WEST_CENTRAL, 7.7864);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.US_WEST_2, 6.6558);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.US_WEST_3, 7.2461);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.CANADA_EAST, 7.2973);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.CANADA_CENTRAL, 7.2973);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.BRAZIL_SOUTH, 13.0558);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.BRAZIL_SOUTHEAST, 15.7478);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.AUSTRALIA_EAST, 7.9611);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.AUSTRALIA_SOUTH_EAST, 7.9615);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.GERMANY_NORTH, 10.2301);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.FRANCE_SOUTH, 9.5673);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.GERMANY_WEST_CENTRAL, 7.8703);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.SWEDEN_CENTRAL, 7.5710);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.SWEDEN_SOUTH, 9.8418);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.SWITZERLAND_NORTH, 9.5417);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.SWITZERLAND_WEST, 12.4006);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.UK_SOUTH, 7.8448);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.UK_WEST, 7.8452);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.INDIA_CENTRAL, 7.0889);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.INDIA_WEST, 7.8312);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.INDIA_SOUTH, 8.8732);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.ASIA_EAST, 8.6249);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.JAPAN_EAST, 8.0311);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.JAPAN_WEST, 8.2541);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.ASIA_SOUTH_EAST, 8.2424);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.KOREA_CENTRAL, 7.9994);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.KOREA_SOUTH, 6.8401);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.FRANCE_CENTRAL, 8.0793);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.SOUTH_AFRICA_NORTH, 8.9197);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.SOUTH_AFRICA_WEST, 11.1818);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.UAE_NORTH, 8.1651);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.UAE_CENTRAL, 10.6138);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.NORWAY_EAST, 8.8237);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.NORWAY_WEST, 11.4703);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.AUSTRALIA_CENTRAL, 7.9618);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.AUSTRALIA_CENTRAL_2, 7.9618);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.QATAR_CENTRAL, 8.7644);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.POLAND_CENTRAL, 8.4093);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.ISRAEL_CENTRAL, 8.0631);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.ITALY_NORTH, 7.7120);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.SPAIN_CENTRAL, 8.2292);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.MEXICO_CENTRAL, 7.4986);
    assertRegionPrice(SKU.NDS_AZURE_INSTANCE_R400, AzureRegionName.NEW_ZEALAND_NORTH, 9.2100);

    // Check storage pricing
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.US_CENTRAL, 1.968);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.US_EAST, 1.600);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.US_EAST_2, 1.600);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.US_NORTH_CENTRAL, 1.920);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.US_WEST, 2.080);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.US_SOUTH_CENTRAL, 1.920);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.EUROPE_NORTH, 1.920);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.EUROPE_WEST, 2.080);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.US_WEST_CENTRAL, 1.920);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.US_WEST_2, 1.600);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.US_WEST_3, 1.56);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.CANADA_EAST, 1.920);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.CANADA_CENTRAL, 1.920);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.BRAZIL_SOUTH, 3.200);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.BRAZIL_SOUTHEAST, 2.96);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.AUSTRALIA_EAST, 2.320);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.AUSTRALIA_SOUTH_EAST, 2.240);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.GERMANY_NORTH, 2.560);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.FRANCE_SOUTH, 1.96);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.GERMANY_WEST_CENTRAL, 2.080);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.SWEDEN_CENTRAL, 1.56);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.SWEDEN_SOUTH, 2.03);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.SWITZERLAND_NORTH, 2.400);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.SWITZERLAND_WEST, 2.880);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.UK_SOUTH, 2.000);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.UK_WEST, 2.096);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.INDIA_CENTRAL, 2.240);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.INDIA_WEST, 2.208);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.INDIA_SOUTH, 2.416);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.ASIA_EAST, 2.480);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.JAPAN_EAST, 2.320);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.JAPAN_WEST, 2.480);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.ASIA_SOUTH_EAST, 2.080);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.KOREA_CENTRAL, 2.160);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.KOREA_SOUTH, 2.000);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.FRANCE_CENTRAL, 2.000);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.SOUTH_AFRICA_NORTH, 2.336);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.SOUTH_AFRICA_WEST, 2.16);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.UAE_NORTH, 2.112);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.UAE_CENTRAL, 2.688);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.NORWAY_EAST, 2.293);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.NORWAY_WEST, 2.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.AUSTRALIA_CENTRAL, 1.74);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.AUSTRALIA_CENTRAL_2, 1.74);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.QATAR_CENTRAL, 2.112);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.POLAND_CENTRAL, 2.293);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.ISRAEL_CENTRAL, 2.1200);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.ITALY_NORTH, 2.0800);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.SPAIN_CENTRAL, 1.936);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.MEXICO_CENTRAL, 1.936);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P2, AzureRegionName.NEW_ZEALAND_NORTH, 2.436);

    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.US_CENTRAL, 3.936);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.US_EAST, 3.200);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.US_EAST_2, 3.200);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.US_NORTH_CENTRAL, 3.840);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.US_WEST, 4.160);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.US_SOUTH_CENTRAL, 3.840);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.EUROPE_NORTH, 3.840);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.EUROPE_WEST, 4.160);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.US_WEST_CENTRAL, 3.840);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.US_WEST_2, 3.200);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.US_WEST_3, 3.12);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.CANADA_EAST, 3.840);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.CANADA_CENTRAL, 3.840);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.BRAZIL_SOUTH, 6.400);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.BRAZIL_SOUTHEAST, 5.93);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.AUSTRALIA_EAST, 4.640);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.AUSTRALIA_SOUTH_EAST, 4.480);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.GERMANY_NORTH, 5.120);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.FRANCE_SOUTH, 3.91);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.GERMANY_WEST_CENTRAL, 4.160);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.SWEDEN_CENTRAL, 3.12);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.SWEDEN_SOUTH, 4.06);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.SWITZERLAND_NORTH, 4.800);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.SWITZERLAND_WEST, 5.760);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.UK_SOUTH, 4.000);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.UK_WEST, 4.192);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.INDIA_CENTRAL, 4.480);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.INDIA_WEST, 4.416);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.INDIA_SOUTH, 4.832);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.ASIA_EAST, 4.960);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.JAPAN_EAST, 4.640);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.JAPAN_WEST, 4.960);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.ASIA_SOUTH_EAST, 4.160);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.KOREA_CENTRAL, 4.320);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.KOREA_SOUTH, 4.000);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.FRANCE_CENTRAL, 4.000);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.SOUTH_AFRICA_NORTH, 4.672);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.SOUTH_AFRICA_WEST, 4.32);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.UAE_NORTH, 4.224);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.UAE_CENTRAL, 5.376);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.NORWAY_EAST, 4.587);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.NORWAY_WEST, 4.46);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.AUSTRALIA_CENTRAL, 3.48);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.AUSTRALIA_CENTRAL_2, 3.48);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.QATAR_CENTRAL, 4.224);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.POLAND_CENTRAL, 4.5867);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.ISRAEL_CENTRAL, 4.2267);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.ITALY_NORTH, 4.16);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.SPAIN_CENTRAL, 3.871);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.MEXICO_CENTRAL, 3.872);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P3, AzureRegionName.NEW_ZEALAND_NORTH, 4.872);

    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.US_CENTRAL, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.US_EAST, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.US_EAST_2, 6.41);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.US_NORTH_CENTRAL, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.US_WEST, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.US_SOUTH_CENTRAL, 6.41);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.EUROPE_NORTH, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.EUROPE_WEST, 7.75);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.US_WEST_CENTRAL, 6.41);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.US_WEST_2, 6.41);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.US_WEST_3, 5.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.CANADA_EAST, 7.05);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.CANADA_CENTRAL, 7.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.BRAZIL_SOUTH, 12.17);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.BRAZIL_SOUTHEAST, 11.86);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.AUSTRALIA_EAST, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.AUSTRALIA_SOUTH_EAST, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.GERMANY_NORTH, 10.07);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.FRANCE_SOUTH, 8.30);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.GERMANY_WEST_CENTRAL, 7.75);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.SWEDEN_CENTRAL, 5.81);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.SWEDEN_SOUTH, 7.55);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.SWITZERLAND_NORTH, 9.29);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.SWITZERLAND_WEST, 12.08);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.UK_SOUTH, 8.52);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.UK_WEST, 8.52);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.INDIA_CENTRAL, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.INDIA_WEST, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.INDIA_SOUTH, 6.35);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.ASIA_EAST, 7.75);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.JAPAN_EAST, 8.11);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.JAPAN_WEST, 7.37);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.ASIA_SOUTH_EAST, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.KOREA_CENTRAL, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.KOREA_SOUTH, 7.04);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.FRANCE_CENTRAL, 8.52);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.SOUTH_AFRICA_NORTH, 8.59);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.SOUTH_AFRICA_WEST, 8.06);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.UAE_NORTH, 7.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.UAE_CENTRAL, 9.99);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.NORWAY_EAST, 8.52);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.NORWAY_WEST, 8.30);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.AUSTRALIA_CENTRAL, 5.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.AUSTRALIA_CENTRAL_2, 5.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.QATAR_CENTRAL, 7.68);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.POLAND_CENTRAL, 8.52);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.ISRAEL_CENTRAL, 7.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.ITALY_NORTH, 7.75);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.SPAIN_CENTRAL, 7.740);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.MEXICO_CENTRAL, 7.740);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P4, AzureRegionName.NEW_ZEALAND_NORTH, 7.040);

    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.US_CENTRAL, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.US_EAST, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.US_EAST_2, 12.39);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.US_NORTH_CENTRAL, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.US_WEST, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.US_SOUTH_CENTRAL, 12.39);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.EUROPE_NORTH, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.EUROPE_WEST, 14.97);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.US_WEST_CENTRAL, 12.39);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.US_WEST_2, 12.39);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.US_WEST_3, 10.21);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.CANADA_EAST, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.CANADA_CENTRAL, 14.85);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.BRAZIL_SOUTH, 23.52);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.BRAZIL_SOUTHEAST, 22.92);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.AUSTRALIA_EAST, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.AUSTRALIA_SOUTH_EAST, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.GERMANY_NORTH, 19.47);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.FRANCE_SOUTH, 16.05);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.GERMANY_WEST_CENTRAL, 14.97);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.SWEDEN_CENTRAL, 11.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.SWEDEN_SOUTH, 14.60);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.SWITZERLAND_NORTH, 17.97);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.SWITZERLAND_WEST, 23.36);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.UK_SOUTH, 16.47);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.UK_WEST, 16.47);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.INDIA_CENTRAL, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.INDIA_WEST, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.INDIA_SOUTH, 12.25);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.ASIA_EAST, 14.97);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.JAPAN_EAST, 15.65);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.JAPAN_WEST, 14.24);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.ASIA_SOUTH_EAST, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.KOREA_CENTRAL, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.KOREA_SOUTH, 13.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.FRANCE_CENTRAL, 16.47);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.SOUTH_AFRICA_NORTH, 16.59);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.SOUTH_AFRICA_WEST, 15.59);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.UAE_NORTH, 14.85);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.UAE_CENTRAL, 19.31);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.NORWAY_EAST, 16.47);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.NORWAY_WEST, 16.06);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.AUSTRALIA_CENTRAL, 10.21);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.AUSTRALIA_CENTRAL_2, 10.21);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.QATAR_CENTRAL, 14.85);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.POLAND_CENTRAL, 16.47);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.ISRAEL_CENTRAL, 14.85);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.ITALY_NORTH, 14.97);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.SPAIN_CENTRAL, 14.970);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.MEXICO_CENTRAL, 14.970);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P6, AzureRegionName.NEW_ZEALAND_NORTH, 13.610);

    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.US_CENTRAL, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.US_EAST, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.US_EAST_2, 23.89);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.US_NORTH_CENTRAL, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.US_WEST, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.US_SOUTH_CENTRAL, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.EUROPE_NORTH, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.EUROPE_WEST, 28.91);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.US_WEST_CENTRAL, 23.89);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.US_WEST_2, 23.89);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.US_WEST_3, 19.71);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.CANADA_EAST, 26.29);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.CANADA_CENTRAL, 26.68);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.BRAZIL_SOUTH, 45.40);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.BRAZIL_SOUTHEAST, 44.27);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.AUSTRALIA_EAST, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.AUSTRALIA_SOUTH_EAST, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.GERMANY_NORTH, 37.59);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.FRANCE_SOUTH, 31.00);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.GERMANY_WEST_CENTRAL, 28.91);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.SWEDEN_CENTRAL, 21.68);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.SWEDEN_SOUTH, 28.18);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.SWITZERLAND_NORTH, 34.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.SWITZERLAND_WEST, 45.11);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.UK_SOUTH, 31.80);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.UK_WEST, 31.80);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.INDIA_CENTRAL, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.INDIA_WEST, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.INDIA_SOUTH, 23.65);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.ASIA_EAST, 28.91);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.JAPAN_EAST, 30.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.JAPAN_WEST, 27.48);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.ASIA_SOUTH_EAST, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.KOREA_CENTRAL, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.KOREA_SOUTH, 26.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.FRANCE_CENTRAL, 31.80);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.SOUTH_AFRICA_NORTH, 32.03);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.SOUTH_AFRICA_WEST, 30.11);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.UAE_NORTH, 28.68);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.UAE_CENTRAL, 37.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.NORWAY_EAST, 31.80);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.NORWAY_WEST, 31.00);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.AUSTRALIA_CENTRAL, 19.71);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.AUSTRALIA_CENTRAL_2, 19.71);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.QATAR_CENTRAL, 28.67);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.POLAND_CENTRAL, 31.80);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.ISRAEL_CENTRAL, 28.68);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.ITALY_NORTH, 28.91);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.SPAIN_CENTRAL, 28.910);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.MEXICO_CENTRAL, 28.910);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P10, AzureRegionName.NEW_ZEALAND_NORTH, 26.280);

    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.US_CENTRAL, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.US_EAST, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.US_EAST_2, 46.08);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.US_NORTH_CENTRAL, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.US_WEST, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.US_SOUTH_CENTRAL, 46.08);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.EUROPE_NORTH, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.EUROPE_WEST, 55.76);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.US_WEST_CENTRAL, 46.08);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.US_WEST_2, 46.08);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.US_WEST_3, 38.01);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.CANADA_EAST, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.CANADA_CENTRAL, 55.31);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.BRAZIL_SOUTH, 87.56);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.BRAZIL_SOUTHEAST, 85.37);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.AUSTRALIA_EAST, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.AUSTRALIA_SOUTH_EAST, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.GERMANY_NORTH, 72.48);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.FRANCE_SOUTH, 59.79);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.GERMANY_WEST_CENTRAL, 55.76);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.SWEDEN_CENTRAL, 41.81);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.SWEDEN_SOUTH, 54.35);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.SWITZERLAND_NORTH, 66.91);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.SWITZERLAND_WEST, 86.97);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.UK_SOUTH, 61.33);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.UK_WEST, 61.33);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.INDIA_CENTRAL, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.INDIA_WEST, 58.29);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.INDIA_SOUTH, 45.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.ASIA_EAST, 55.76);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.JAPAN_EAST, 58.31);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.JAPAN_WEST, 53.00);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.ASIA_SOUTH_EAST, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.KOREA_CENTRAL, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.KOREA_SOUTH, 50.69);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.FRANCE_CENTRAL, 61.33);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.SOUTH_AFRICA_NORTH, 61.75);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.SOUTH_AFRICA_WEST, 58.06);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.UAE_NORTH, 55.31);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.UAE_CENTRAL, 71.89);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.NORWAY_EAST, 61.33);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.NORWAY_WEST, 59.79);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.AUSTRALIA_CENTRAL, 38.01);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.AUSTRALIA_CENTRAL_2, 38.01);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.QATAR_CENTRAL, 55.30);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.POLAND_CENTRAL, 61.33);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.ISRAEL_CENTRAL, 55.31);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.ITALY_NORTH, 55.76);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.SPAIN_CENTRAL, 55.750);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.MEXICO_CENTRAL, 55.750);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P15, AzureRegionName.NEW_ZEALAND_NORTH, 50.680);

    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.US_CENTRAL, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.US_EAST, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.US_EAST_2, 88.75);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.US_NORTH_CENTRAL, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.US_WEST, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.US_SOUTH_CENTRAL, 88.75);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.EUROPE_NORTH, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.EUROPE_WEST, 107.39);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.US_WEST_CENTRAL, 88.75);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.US_WEST_2, 88.75);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.US_WEST_3, 73.22);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.CANADA_EAST, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.CANADA_CENTRAL, 106.51);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.BRAZIL_SOUTH, 168.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.BRAZIL_SOUTHEAST, 164.40);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.AUSTRALIA_EAST, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.AUSTRALIA_SOUTH_EAST, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.GERMANY_NORTH, 139.60);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.FRANCE_SOUTH, 115.17);
    assertAzureStoragePrice(
        SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.GERMANY_WEST_CENTRAL, 107.39);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.SWEDEN_CENTRAL, 80.54);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.SWEDEN_SOUTH, 104.70);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.SWITZERLAND_NORTH, 128.87);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.SWITZERLAND_WEST, 167.52);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.UK_SOUTH, 118.13);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.UK_WEST, 118.13);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.INDIA_CENTRAL, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.INDIA_WEST, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.INDIA_SOUTH, 87.87);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.ASIA_EAST, 107.39);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.JAPAN_EAST, 112.27);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.JAPAN_WEST, 102.05);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.ASIA_SOUTH_EAST, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.KOREA_CENTRAL, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.KOREA_SOUTH, 97.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.FRANCE_CENTRAL, 118.13);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.SOUTH_AFRICA_NORTH, 118.92);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.SOUTH_AFRICA_WEST, 111.82);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.UAE_NORTH, 106.51);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.UAE_CENTRAL, 138.45);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.NORWAY_EAST, 118.13);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.NORWAY_WEST, 115.17);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.AUSTRALIA_CENTRAL, 73.22);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.AUSTRALIA_CENTRAL_2, 73.22);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.QATAR_CENTRAL, 106.50);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.POLAND_CENTRAL, 118.13);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.ISRAEL_CENTRAL, 106.51);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.ITALY_NORTH, 107.39);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.SPAIN_CENTRAL, 107.370);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.MEXICO_CENTRAL, 107.370);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P20, AzureRegionName.NEW_ZEALAND_NORTH, 97.630);

    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.US_CENTRAL, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.US_EAST, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.US_EAST_2, 163.84);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.US_NORTH_CENTRAL, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.US_WEST, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.US_SOUTH_CENTRAL, 163.84);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.EUROPE_NORTH, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.EUROPE_WEST, 198.24);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.US_WEST_CENTRAL, 163.84);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.US_WEST_2, 163.84);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.US_WEST_3, 135.17);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.CANADA_EAST, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.CANADA_CENTRAL, 196.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.BRAZIL_SOUTH, 311.29);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.BRAZIL_SOUTHEAST, 303.51);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.AUSTRALIA_EAST, 180.23);
    assertAzureStoragePrice(
        SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.AUSTRALIA_SOUTH_EAST, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.GERMANY_NORTH, 257.71);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.FRANCE_SOUTH, 212.61);
    assertAzureStoragePrice(
        SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.GERMANY_WEST_CENTRAL, 198.24);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.SWEDEN_CENTRAL, 148.68);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.SWEDEN_SOUTH, 193.28);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.SWITZERLAND_NORTH, 237.89);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.SWITZERLAND_WEST, 309.25);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.UK_SOUTH, 218.07);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.UK_WEST, 218.07);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.INDIA_CENTRAL, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.INDIA_WEST, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.INDIA_SOUTH, 162.21);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.ASIA_EAST, 198.24);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.JAPAN_EAST, 207.25);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.JAPAN_WEST, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.ASIA_SOUTH_EAST, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.KOREA_CENTRAL, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.KOREA_SOUTH, 180.23);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.FRANCE_CENTRAL, 218.07);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.SOUTH_AFRICA_NORTH, 219.55);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.SOUTH_AFRICA_WEST, 206.44);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.UAE_NORTH, 196.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.UAE_CENTRAL, 255.60);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.NORWAY_EAST, 218.07);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.NORWAY_WEST, 212.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.AUSTRALIA_CENTRAL, 135.17);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.AUSTRALIA_CENTRAL_2, 135.17);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.QATAR_CENTRAL, 196.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.POLAND_CENTRAL, 218.07);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.ISRAEL_CENTRAL, 196.61);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.ITALY_NORTH, 198.24);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.SPAIN_CENTRAL, 198.220);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.MEXICO_CENTRAL, 198.230);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P30, AzureRegionName.NEW_ZEALAND_NORTH, 180.230);

    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.GERMANY_NORTH, 493.89);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.FRANCE_SOUTH, 407.46);
    assertAzureStoragePrice(
        SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.GERMANY_WEST_CENTRAL, 379.92);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.SWEDEN_CENTRAL, 284.94);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.SWEDEN_SOUTH, 370.42);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.SWITZERLAND_WEST, 592.67);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.SOUTH_AFRICA_WEST, 395.67);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.UAE_CENTRAL, 489.89);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.NORWAY_EAST, 417.91);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.NORWAY_WEST, 407.46);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.AUSTRALIA_CENTRAL, 259.05);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.AUSTRALIA_CENTRAL_2, 259.05);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.BRAZIL_SOUTHEAST, 581.77);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.US_WEST_3, 259.05);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.QATAR_CENTRAL, 376.83);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.POLAND_CENTRAL, 417.92);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.ISRAEL_CENTRAL, 376.84);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.ITALY_NORTH, 379.92);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.SPAIN_CENTRAL, 379.920);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.MEXICO_CENTRAL, 379.930);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P40, AzureRegionName.NEW_ZEALAND_NORTH, 345.390);

    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.GERMANY_NORTH, 944.84);
    assertAzureStoragePrice(
        SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.GERMANY_WEST_CENTRAL, 726.80);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.SWEDEN_CENTRAL, 545.10);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.SWEDEN_SOUTH, 708.63);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.SWITZERLAND_WEST, 1133.80);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.SOUTH_AFRICA_WEST, 756.94);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.UAE_CENTRAL, 937.17);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.FRANCE_SOUTH, 779.49);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.NORWAY_EAST, 799.48);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.NORWAY_WEST, 779.49);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.AUSTRALIA_CENTRAL, 495.57);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.AUSTRALIA_CENTRAL_2, 495.57);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.BRAZIL_SOUTHEAST, 1112.95);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.US_WEST_3, 495.57);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.QATAR_CENTRAL, 720.90);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.POLAND_CENTRAL, 799.48);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.ISRAEL_CENTRAL, 720.91);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.ITALY_NORTH, 726.80);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.SPAIN_CENTRAL, 726.800);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.MEXICO_CENTRAL, 726.830);
    assertAzureStoragePrice(SKU.NDS_AZURE_STORAGE_P50, AzureRegionName.NEW_ZEALAND_NORTH, 660.750);

    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.US_CENTRAL, 0.1986);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.US_EAST, 0.1685);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.US_EAST_2, 0.1671);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.US_NORTH_CENTRAL, 0.1740);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.US_WEST, 0.2165);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.US_SOUTH_CENTRAL, 0.1969);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.EUROPE_NORTH, 0.1988);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.EUROPE_WEST, 0.2244);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.US_WEST_CENTRAL, 0.1969);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.US_WEST_2, 0.1671);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.US_WEST_3, 0.1999);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.CANADA_EAST, 0.1896);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.CANADA_CENTRAL, 0.1896);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.BRAZIL_SOUTH, 0.2805);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.BRAZIL_SOUTHEAST, 0.3596);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.AUSTRALIA_CENTRAL, 0.2273);
    assertRegionPrice(
        SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.AUSTRALIA_CENTRAL_2, 0.2273);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.AUSTRALIA_EAST, 0.2266);
    assertRegionPrice(
        SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.2270);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.GERMANY_NORTH, 0.2577);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.FRANCE_SOUTH, 0.2421);
    assertRegionPrice(
        SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.GERMANY_WEST_CENTRAL, 0.1985);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.SWEDEN_CENTRAL, 0.1794);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.SWEDEN_SOUTH, 0.2334);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.SWITZERLAND_NORTH, 0.2467);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.SWITZERLAND_WEST, 0.3196);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.UK_SOUTH, 0.2172);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.UK_WEST, 0.2176);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.INDIA_CENTRAL, 0.1805);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.INDIA_WEST, 0.2070);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.INDIA_SOUTH, 0.2356);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.ASIA_EAST, 0.2257);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.JAPAN_EAST, 0.2390);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.JAPAN_WEST, 0.2380);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.ASIA_SOUTH_EAST, 0.2091);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.KOREA_CENTRAL, 0.2114);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.KOREA_SOUTH, 0.1798);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.FRANCE_CENTRAL, 0.2149);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.SOUTH_AFRICA_NORTH, 0.2277);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.SOUTH_AFRICA_WEST, 0.2715);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.UAE_NORTH, 0.2015);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.UAE_CENTRAL, 0.2617);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.NORWAY_EAST, 0.2476);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.NORWAY_WEST, 0.3225);
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM, AzureRegionName.QATAR_CENTRAL, 0.2041);

    // check storage prices for backup snapshot
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.US_CENTRAL, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.US_EAST, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.US_EAST_2, SKU.monthlyToDailyCost(0.34));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.US_NORTH_CENTRAL, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.US_WEST, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.US_SOUTH_CENTRAL, SKU.monthlyToDailyCost(0.34));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.EUROPE_NORTH, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.EUROPE_WEST, SKU.monthlyToDailyCost(0.41));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.US_WEST_2, SKU.monthlyToDailyCost(0.34));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.US_WEST_3, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.CANADA_EAST, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.CANADA_CENTRAL, SKU.monthlyToDailyCost(0.41));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.BRAZIL_SOUTH, SKU.monthlyToDailyCost(0.65));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.BRAZIL_SOUTHEAST, SKU.monthlyToDailyCost(0.85));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.AUSTRALIA_EAST, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.AUSTRALIA_SOUTH_EAST, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.GERMANY_NORTH, SKU.monthlyToDailyCost(0.54));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.GERMANY_WEST_CENTRAL, SKU.monthlyToDailyCost(0.42));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.SWITZERLAND_NORTH, SKU.monthlyToDailyCost(0.50));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.SWITZERLAND_WEST, SKU.monthlyToDailyCost(0.65));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.SWEDEN_CENTRAL, SKU.monthlyToDailyCost(0.42));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.SWEDEN_SOUTH, SKU.monthlyToDailyCost(0.54));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.UK_SOUTH, SKU.monthlyToDailyCost(0.46));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.UK_WEST, SKU.monthlyToDailyCost(0.46));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.INDIA_CENTRAL, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.INDIA_WEST, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.INDIA_SOUTH, SKU.monthlyToDailyCost(0.34));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.ASIA_EAST, SKU.monthlyToDailyCost(0.41));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.JAPAN_EAST, SKU.monthlyToDailyCost(0.43));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.JAPAN_WEST, SKU.monthlyToDailyCost(0.39));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.ASIA_SOUTH_EAST, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.KOREA_CENTRAL, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.KOREA_SOUTH, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.FRANCE_CENTRAL, SKU.monthlyToDailyCost(0.46));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.FRANCE_SOUTH, SKU.monthlyToDailyCost(0.59));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.SOUTH_AFRICA_NORTH, SKU.monthlyToDailyCost(0.46));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.SOUTH_AFRICA_WEST, SKU.monthlyToDailyCost(0.58));
    assertAzureBackupSnapshotStoragePrice(AzureRegionName.UAE_NORTH, SKU.monthlyToDailyCost(0.42));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.UAE_CENTRAL, SKU.monthlyToDailyCost(0.55));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.NORWAY_EAST, SKU.monthlyToDailyCost(0.46));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.NORWAY_WEST, SKU.monthlyToDailyCost(0.59));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.AUSTRALIA_CENTRAL, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.AUSTRALIA_CENTRAL_2, SKU.monthlyToDailyCost(0.38));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.QATAR_CENTRAL, SKU.monthlyToDailyCost(0.42));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.POLAND_CENTRAL, SKU.monthlyToDailyCost(0.46));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.ISRAEL_CENTRAL, SKU.monthlyToDailyCost(0.42));
    assertAzureBackupSnapshotStoragePrice(
        AzureRegionName.ITALY_NORTH, SKU.monthlyToDailyCost(0.42));
  }

  @Test
  public void testAtlasServerlessPricing() {
    // Assert prices in a different order from the SKU definitions to verify the correct prices are
    // set. Keep the prices in the same order as the pricing doc:
    // https://docs.google.com/spreadsheets/d/1xdHFU0MMuxKHDGuqlodRYJR9vLjdq4md3RxRZAayTio/edit?usp=sharing
    // This might seem redundant but adds another layer of verification that might not be easy to
    // catch at the time the prices were entered for each SKU.

    // AWS US_EAST_1
    assertServerlessAWSTierPricing(AWSRegionName.US_EAST_1, new double[] {0.100, 0.050, 0.010});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.US_EAST_1, 1.000);

    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.US_EAST_1, 0.250);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.US_EAST_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.US_EAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.US_EAST_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.US_EAST_1, 0.0200);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.US_EAST_1, 0.0900);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.US_EAST_1, 2.50);

    // AWS AF_SOUTH_1
    assertServerlessAWSTierPricing(AWSRegionName.AF_SOUTH_1, new double[] {0.132, 0.066, 0.013});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.AF_SOUTH_1, 1.323);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.AF_SOUTH_1, 0.327);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.AF_SOUTH_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.AF_SOUTH_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.AF_SOUTH_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.AF_SOUTH_1, 0.1470);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.AF_SOUTH_1, 0.1540);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.AF_SOUTH_1, 3.300);

    // AWS AP_EAST_1
    assertServerlessAWSTierPricing(AWSRegionName.AP_EAST_1, new double[] {0.137, 0.069, 0.014});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.AP_EAST_1, 1.375);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.AP_EAST_1, 0.330);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.AP_EAST_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.AP_EAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.AP_EAST_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.AP_EAST_1, 0.0900);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_EAST_1, 0.1200);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.AP_EAST_1, 3.425);

    // AWS AP_NORTHEAST_1
    assertServerlessAWSTierPricing(
        AWSRegionName.AP_NORTHEAST_1, new double[] {0.129, 0.065, 0.013});
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.AP_NORTHEAST_1, 1.291);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.AP_NORTHEAST_1, 0.300);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.AP_NORTHEAST_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.AP_NORTHEAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.AP_NORTHEAST_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.AP_NORTHEAST_1, 0.0900);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_NORTHEAST_1, 0.1140);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.AP_NORTHEAST_1, 3.225);

    // AWS AP_NORTHEAST_2
    assertServerlessAWSTierPricing(
        AWSRegionName.AP_NORTHEAST_2, new double[] {0.123, 0.061, 0.012});
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.AP_NORTHEAST_2, 1.229);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.AP_NORTHEAST_2, 0.285);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.AP_NORTHEAST_2, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.AP_NORTHEAST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.AP_NORTHEAST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.AP_NORTHEAST_2, 0.0800);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_NORTHEAST_2, 0.1260);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.AP_NORTHEAST_2, 3.075);

    // AWS AP_NORTHEAST_3
    assertServerlessAWSTierPricing(
        AWSRegionName.AP_NORTHEAST_3, new double[] {0.129, 0.065, 0.013});
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.AP_NORTHEAST_3, 1.291);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.AP_NORTHEAST_3, 0.300);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.AP_NORTHEAST_3, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.AP_NORTHEAST_3, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.AP_NORTHEAST_3, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.AP_NORTHEAST_3, 0.0900);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_NORTHEAST_3, 0.1140);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.AP_NORTHEAST_3, 3.225);

    // AWS AP_SOUTH_1
    assertServerlessAWSTierPricing(AWSRegionName.AP_SOUTH_1, new double[] {0.105, 0.053, 0.011});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.AP_SOUTH_1, 1.052);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.AP_SOUTH_1, 0.285);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.AP_SOUTH_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.AP_SOUTH_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.AP_SOUTH_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.AP_SOUTH_1, 0.0860);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTH_1, 0.1093);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.AP_SOUTH_1, 2.625);

    // AWS AP_SOUTHEAST_1
    assertServerlessAWSTierPricing(
        AWSRegionName.AP_SOUTHEAST_1, new double[] {0.125, 0.062, 0.012});
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.AP_SOUTHEAST_1, 1.250);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.AP_SOUTHEAST_1, 0.300);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.AP_SOUTHEAST_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.AP_SOUTHEAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.AP_SOUTHEAST_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.AP_SOUTHEAST_1, 0.0900);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTHEAST_1, 0.1200);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.AP_SOUTHEAST_1, 3.125);

    // AWS AP_SOUTHEAST_2
    assertServerlessAWSTierPricing(
        AWSRegionName.AP_SOUTHEAST_2, new double[] {0.125, 0.062, 0.012});
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.AP_SOUTHEAST_2, 1.250);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.AP_SOUTHEAST_2, 0.300);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.AP_SOUTHEAST_2, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.AP_SOUTHEAST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.AP_SOUTHEAST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.AP_SOUTHEAST_2, 0.0980);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.AP_SOUTHEAST_2, 0.1140);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.AP_SOUTHEAST_2, 3.125);

    // AWS CA_CENTRAL_1
    assertServerlessAWSTierPricing(AWSRegionName.CA_CENTRAL_1, new double[] {0.111, 0.056, 0.011});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.CA_CENTRAL_1, 1.115);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.CA_CENTRAL_1, 0.275);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.CA_CENTRAL_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.CA_CENTRAL_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.CA_CENTRAL_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.CA_CENTRAL_1, 0.0200);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.CA_CENTRAL_1, 0.0900);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.CA_CENTRAL_1, 2.775);

    // AWS EU_CENTRAL_1
    assertServerlessAWSTierPricing(AWSRegionName.EU_CENTRAL_1, new double[] {0.120, 0.060, 0.012});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.EU_CENTRAL_1, 1.198);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.EU_CENTRAL_1, 0.298);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.EU_CENTRAL_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.EU_CENTRAL_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.EU_CENTRAL_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.EU_CENTRAL_1, 0.0200);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_CENTRAL_1, 0.0900);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.EU_CENTRAL_1, 3.000);

    // AWS EU_NORTH_1
    assertServerlessAWSTierPricing(AWSRegionName.EU_NORTH_1, new double[] {0.106, 0.053, 0.011});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.EU_NORTH_1, 1.062);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.EU_NORTH_1, 0.261);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.EU_NORTH_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.EU_NORTH_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.EU_NORTH_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.EU_NORTH_1, 0.0200);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_NORTH_1, 0.0900);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.EU_NORTH_1, 2.650);

    // AWS EU_SOUTH_1
    assertServerlessAWSTierPricing(AWSRegionName.EU_SOUTH_1, new double[] {0.117, 0.058, 0.012});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.EU_SOUTH_1, 1.167);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.EU_SOUTH_1, 0.289);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.EU_SOUTH_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.EU_SOUTH_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.EU_SOUTH_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.EU_SOUTH_1, 0.0200);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_SOUTH_1, 0.0900);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.EU_SOUTH_1, 2.925);

    // AWS EU_WEST_1
    assertServerlessAWSTierPricing(AWSRegionName.EU_WEST_1, new double[] {0.111, 0.056, 0.011});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.EU_WEST_1, 1.115);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.EU_WEST_1, 0.275);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.EU_WEST_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.EU_WEST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.EU_WEST_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.EU_WEST_1, 0.0200);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_WEST_1, 0.0900);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.EU_WEST_1, 2.775);

    // AWS EU_WEST_2
    assertServerlessAWSTierPricing(AWSRegionName.EU_WEST_2, new double[] {0.116, 0.058, 0.012});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.EU_WEST_2, 1.156);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.EU_WEST_2, 0.290);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.EU_WEST_2, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.EU_WEST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.EU_WEST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.EU_WEST_2, 0.0200);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_WEST_2, 0.0900);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.EU_WEST_2, 2.900);

    // AWS EU_WEST_3
    assertServerlessAWSTierPricing(AWSRegionName.EU_WEST_3, new double[] {0.117, 0.058, 0.012});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.EU_WEST_3, 1.167);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.EU_WEST_3, 0.290);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.EU_WEST_3, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.EU_WEST_3, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.EU_WEST_3, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.EU_WEST_3, 0.0200);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.EU_WEST_3, 0.0900);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.EU_WEST_3, 2.925);

    // AWS ME_SOUTH_1
    assertServerlessAWSTierPricing(AWSRegionName.ME_SOUTH_1, new double[] {0.123, 0.061, 0.012});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.ME_SOUTH_1, 1.226);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.ME_SOUTH_1, 0.303);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.ME_SOUTH_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.ME_SOUTH_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.ME_SOUTH_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.ME_SOUTH_1, 0.1105);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.ME_SOUTH_1, 0.1170);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.ME_SOUTH_1, 3.075);

    // AWS SA_EAST_1
    assertServerlessAWSTierPricing(AWSRegionName.SA_EAST_1, new double[] {0.159, 0.080, 0.016});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.SA_EAST_1, 1.594);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.SA_EAST_1, 0.428);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.SA_EAST_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.SA_EAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.SA_EAST_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.SA_EAST_1, 0.1380);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.SA_EAST_1, 0.1500);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.SA_EAST_1, 3.975);

    // AWS US_EAST_2
    assertServerlessAWSTierPricing(AWSRegionName.US_EAST_2, new double[] {0.100, 0.050, 0.010});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.US_EAST_2, 1.000);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.US_EAST_2, 0.250);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.US_EAST_2, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.US_EAST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.US_EAST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.US_EAST_2, 0.0200);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.US_EAST_2, 0.0900);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.US_EAST_2, 2.50);

    // AWS US_GOV_EAST_1
    assertServerlessAWSTierPricing(AWSRegionName.US_GOV_EAST_1, new double[] {0.126, 0.063, 0.013});
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.US_GOV_EAST_1, 1.260);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.US_GOV_EAST_1, 0.300);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.US_GOV_EAST_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.US_GOV_EAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.US_GOV_EAST_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.US_GOV_EAST_1, 0.0300);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.US_GOV_EAST_1, 0.1550);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.US_GOV_EAST_1, 3.150);

    // AWS US_GOV_WEST_1
    assertServerlessAWSTierPricing(AWSRegionName.US_GOV_WEST_1, new double[] {0.126, 0.063, 0.013});
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.US_GOV_WEST_1, 1.260);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.US_GOV_WEST_1, 0.300);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.US_GOV_WEST_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.US_GOV_WEST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.US_GOV_WEST_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.US_GOV_WEST_1, 0.0300);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.US_GOV_WEST_1, 0.1550);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.US_GOV_WEST_1, 3.150);

    // AWS US_WEST_1
    assertServerlessAWSTierPricing(AWSRegionName.US_WEST_1, new double[] {0.117, 0.058, 0.012});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.US_WEST_1, 1.167);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.US_WEST_1, 0.300);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.US_WEST_1, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.US_WEST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.US_WEST_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.US_WEST_1, 0.0200);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.US_WEST_1, 0.0900);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.US_WEST_1, 2.925);

    // AWS US_WEST_2
    assertServerlessAWSTierPricing(AWSRegionName.US_WEST_2, new double[] {0.100, 0.050, 0.010});
    assertServerlessPriceByAwsRegion(SKU.NDS_AWS_SERVERLESS_WPU, AWSRegionName.US_WEST_2, 1.000);
    assertServerlessStorageAndBackupPriceByAwsRegion(AWSRegionName.US_WEST_2, 0.250);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_PREVIEW, AWSRegionName.US_WEST_2, 0.0150);
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER, AWSRegionName.US_WEST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_REGIONAL, AWSRegionName.US_WEST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AWSRegionName.US_WEST_2, 0.0200);
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_DATA_TRANSFER_INTERNET, AWSRegionName.US_WEST_2, 0.0900);
    assertServerlessPriceByAwsRegion(
        SKU.NDS_AWS_SERVERLESS_BACKUP_RESTORE_VM, AWSRegionName.US_WEST_2, 2.500);

    // AZURE AUSTRALIA_CENTRAL
    assertServerlessAzureTierPricing(
        AzureRegionName.AUSTRALIA_CENTRAL, new double[] {0.130, 0.065, 0.013});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.AUSTRALIA_CENTRAL, 1.302);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.AUSTRALIA_CENTRAL, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.AUSTRALIA_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.AUSTRALIA_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.AUSTRALIA_CENTRAL, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.AUSTRALIA_CENTRAL,
        0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.AUSTRALIA_CENTRAL, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.AUSTRALIA_CENTRAL, 3.250);

    // AZURE AUSTRALIA_CENTRAL_2
    assertServerlessAzureTierPricing(
        AzureRegionName.AUSTRALIA_CENTRAL_2, new double[] {0.130, 0.065, 0.013});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.AUSTRALIA_CENTRAL_2, 1.302);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.AUSTRALIA_CENTRAL_2, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW,
        AzureRegionName.AUSTRALIA_CENTRAL_2,
        0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.AUSTRALIA_CENTRAL_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL,
        AzureRegionName.AUSTRALIA_CENTRAL_2,
        0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.AUSTRALIA_CENTRAL_2,
        0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET,
        AzureRegionName.AUSTRALIA_CENTRAL_2,
        0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.AUSTRALIA_CENTRAL_2, 3.250);

    // AZURE AUSTRALIA_EAST
    assertServerlessAzureTierPricing(
        AzureRegionName.AUSTRALIA_EAST, new double[] {0.130, 0.065, 0.013});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.AUSTRALIA_EAST, 1.302);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.AUSTRALIA_EAST, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.AUSTRALIA_EAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.AUSTRALIA_EAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.AUSTRALIA_EAST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.AUSTRALIA_EAST,
        0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.AUSTRALIA_EAST, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.AUSTRALIA_EAST, 3.250);

    // AZURE AUSTRALIA_SOUTH_EAST
    assertServerlessAzureTierPricing(
        AzureRegionName.AUSTRALIA_SOUTH_EAST, new double[] {0.130, 0.065, 0.013});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.AUSTRALIA_SOUTH_EAST, 1.302);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW,
        AzureRegionName.AUSTRALIA_SOUTH_EAST,
        0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL,
        AzureRegionName.AUSTRALIA_SOUTH_EAST,
        0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.AUSTRALIA_SOUTH_EAST,
        0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET,
        AzureRegionName.AUSTRALIA_SOUTH_EAST,
        0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.AUSTRALIA_SOUTH_EAST, 3.250);

    // AZURE BRAZIL_SOUTH
    assertServerlessAzureTierPricing(
        AzureRegionName.BRAZIL_SOUTH, new double[] {0.166, 0.083, 0.017});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.BRAZIL_SOUTH, 1.657);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.BRAZIL_SOUTH, 0.473);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.BRAZIL_SOUTH, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.BRAZIL_SOUTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.BRAZIL_SOUTH, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.BRAZIL_SOUTH, 0.1600);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.BRAZIL_SOUTH, 0.1810);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.BRAZIL_SOUTH, 4.150);

    // AZURE BRAZIL_SOUTH_EAST
    assertServerlessAzureTierPricing(
        AzureRegionName.BRAZIL_SOUTHEAST, new double[] {0.194, 0.097, 0.020});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.BRAZIL_SOUTHEAST, 2.154);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.BRAZIL_SOUTHEAST, 0.683);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.BRAZIL_SOUTHEAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.BRAZIL_SOUTHEAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.BRAZIL_SOUTHEAST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.BRAZIL_SOUTHEAST,
        0.1600);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.BRAZIL_SOUTHEAST, 0.1810);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.BRAZIL_SOUTHEAST, 4.850);

    // AZURE CANADA_CENTRAL
    assertServerlessAzureTierPricing(
        AzureRegionName.CANADA_CENTRAL, new double[] {0.116, 0.058, 0.012});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.CANADA_CENTRAL, 1.156);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.CANADA_CENTRAL, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.CANADA_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.CANADA_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.CANADA_CENTRAL, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.CANADA_CENTRAL,
        0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.CANADA_CENTRAL, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.CANADA_CENTRAL, 2.900);

    // AZURE CANADA_EAST
    assertServerlessAzureTierPricing(
        AzureRegionName.CANADA_EAST, new double[] {0.116, 0.058, 0.012});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.CANADA_EAST, 1.156);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.CANADA_EAST, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.CANADA_EAST, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.CANADA_EAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.CANADA_EAST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.CANADA_EAST, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.CANADA_EAST, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.CANADA_EAST, 2.900);

    // AZURE INDIA_CENTRAL
    assertServerlessAzureTierPricing(
        AzureRegionName.INDIA_CENTRAL, new double[] {0.109, 0.055, 0.011});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.INDIA_CENTRAL, 1.094);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.INDIA_CENTRAL, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.INDIA_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.INDIA_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.INDIA_CENTRAL, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.INDIA_CENTRAL, 0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.INDIA_CENTRAL, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.INDIA_CENTRAL, 2.725);

    // AZURE US_CENTRAL
    assertServerlessAzureTierPricing(
        AzureRegionName.US_CENTRAL, new double[] {0.115, 0.057, 0.011});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.US_CENTRAL, 1.146);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.US_CENTRAL, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.US_CENTRAL, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.US_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.US_CENTRAL, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.US_CENTRAL, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.US_CENTRAL, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.US_CENTRAL, 2.875);

    // AZURE ASIA_EAST
    assertServerlessAzureTierPricing(AzureRegionName.ASIA_EAST, new double[] {0.138, 0.069, 0.014});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.ASIA_EAST, 1.375);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.ASIA_EAST, 0.334);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.ASIA_EAST, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.ASIA_EAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.ASIA_EAST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.ASIA_EAST, 0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.ASIA_EAST, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.ASIA_EAST, 3.450);

    // AZURE US_EAST
    assertServerlessAzureTierPricing(AzureRegionName.US_EAST, new double[] {0.100, 0.050, 0.010});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.US_EAST, 1.000);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.US_EAST, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.US_EAST, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.US_EAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.US_EAST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.US_EAST, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.US_EAST, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.US_EAST, 2.500);

    // AZURE US_EAST_2
    assertServerlessAzureTierPricing(AzureRegionName.US_EAST_2, new double[] {0.100, 0.050, 0.010});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.US_EAST_2, 1.000);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.US_EAST_2, 0.276);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.US_EAST_2, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.US_EAST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.US_EAST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.US_EAST_2, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.US_EAST_2, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.US_EAST_2, 2.500);

    // AZURE FRANCE_CENTRAL
    assertServerlessAzureTierPricing(
        AzureRegionName.FRANCE_CENTRAL, new double[] {0.117, 0.058, 0.012});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.FRANCE_CENTRAL, 1.167);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.FRANCE_CENTRAL, 0.368);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.FRANCE_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.FRANCE_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.FRANCE_CENTRAL, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.FRANCE_CENTRAL,
        0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.FRANCE_CENTRAL, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.FRANCE_CENTRAL, 2.925);

    // AZURE FRANCE_SOUTH
    assertServerlessAzureTierPricing(
        AzureRegionName.FRANCE_SOUTH, new double[] {0.152, 0.076, 0.015});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.FRANCE_SOUTH, 1.517);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.FRANCE_SOUTH, 0.478);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.FRANCE_SOUTH, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.FRANCE_SOUTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.FRANCE_SOUTH, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.FRANCE_SOUTH, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.FRANCE_SOUTH, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.FRANCE_SOUTH, 3.800);

    // AZURE GERMANY_NORTH
    assertServerlessAzureTierPricing(
        AzureRegionName.GERMANY_NORTH, new double[] {0.156, 0.078, 0.016});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.GERMANY_NORTH, 1.557);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.GERMANY_NORTH, 0.435);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.GERMANY_NORTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.GERMANY_NORTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.GERMANY_NORTH, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.GERMANY_NORTH, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.GERMANY_NORTH, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.GERMANY_NORTH, 3.900);

    // AZURE GERMANY_WEST_CENTRAL
    assertServerlessAzureTierPricing(
        AzureRegionName.GERMANY_WEST_CENTRAL, new double[] {0.120, 0.060, 0.012});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.GERMANY_WEST_CENTRAL, 1.198);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.GERMANY_WEST_CENTRAL, 0.334);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW,
        AzureRegionName.GERMANY_WEST_CENTRAL,
        0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.GERMANY_WEST_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL,
        AzureRegionName.GERMANY_WEST_CENTRAL,
        0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.GERMANY_WEST_CENTRAL,
        0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET,
        AzureRegionName.GERMANY_WEST_CENTRAL,
        0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.GERMANY_WEST_CENTRAL, 3.000);

    // AZURE JAPAN_EAST
    assertServerlessAzureTierPricing(
        AzureRegionName.JAPAN_EAST, new double[] {0.134, 0.067, 0.013});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.JAPAN_EAST, 1.344);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.GERMANY_WEST_CENTRAL, 0.334);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.JAPAN_EAST, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.JAPAN_EAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.JAPAN_EAST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.JAPAN_EAST, 0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.JAPAN_EAST, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.JAPAN_EAST, 3.350);

    // AZURE JAPAN_WEST
    assertServerlessAzureTierPricing(
        AzureRegionName.JAPAN_WEST, new double[] {0.134, 0.067, 0.013});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.JAPAN_WEST, 1.344);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.JAPAN_WEST, 0.318);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.JAPAN_WEST, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.JAPAN_WEST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.JAPAN_WEST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.JAPAN_WEST, 0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.JAPAN_WEST, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.JAPAN_WEST, 3.350);

    // AZURE KOREA_CENTRAL
    assertServerlessAzureTierPricing(
        AzureRegionName.KOREA_CENTRAL, new double[] {0.128, 0.064, 0.013});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.KOREA_CENTRAL, 1.281);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.KOREA_CENTRAL, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.KOREA_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.KOREA_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.KOREA_CENTRAL, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.KOREA_CENTRAL, 0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.KOREA_CENTRAL, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.KOREA_CENTRAL, 3.200);

    // AZURE KOREA_SOUTH
    assertServerlessAzureTierPricing(
        AzureRegionName.KOREA_SOUTH, new double[] {0.115, 0.058, 0.012});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.KOREA_SOUTH, 1.154);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.KOREA_SOUTH, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.KOREA_SOUTH, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.KOREA_SOUTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.KOREA_SOUTH, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.KOREA_SOUTH, 0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.KOREA_SOUTH, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.KOREA_SOUTH, 2.875);

    // AZURE US_NORTH_CENTRAL
    assertServerlessAzureTierPricing(
        AzureRegionName.US_NORTH_CENTRAL, new double[] {0.104, 0.052, 0.010});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.US_NORTH_CENTRAL, 1.042);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.US_NORTH_CENTRAL, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.US_NORTH_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.US_NORTH_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.US_NORTH_CENTRAL, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.US_NORTH_CENTRAL,
        0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.US_NORTH_CENTRAL, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.US_NORTH_CENTRAL, 2.600);

    // AZURE EUROPE_NORTH
    assertServerlessAzureTierPricing(
        AzureRegionName.EUROPE_NORTH, new double[] {0.111, 0.056, 0.011});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.EUROPE_NORTH, 1.115);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.EUROPE_NORTH, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.EUROPE_NORTH, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.EUROPE_NORTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.EUROPE_NORTH, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.EUROPE_NORTH, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.EUROPE_NORTH, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.EUROPE_NORTH, 2.775);

    // AZURE NORWAY_EAST
    assertServerlessAzureTierPricing(
        AzureRegionName.NORWAY_EAST, new double[] {0.137, 0.069, 0.014});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.NORWAY_EAST, 1.375);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.NORWAY_EAST, 0.368);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.NORWAY_EAST, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.NORWAY_EAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.NORWAY_EAST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.NORWAY_EAST, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.NORWAY_EAST, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.NORWAY_EAST, 3.425);

    // AZURE NORWAY_WEST
    assertServerlessAzureTierPricing(
        AzureRegionName.NORWAY_WEST, new double[] {0.179, 0.089, 0.018});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.NORWAY_WEST, 1.788);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.NORWAY_WEST, 0.478);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.NORWAY_WEST, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.NORWAY_WEST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.NORWAY_WEST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.NORWAY_WEST, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.NORWAY_WEST, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.NORWAY_WEST, 4.475);

    // AZURE SOUTH_AFRICA_NORTH
    assertServerlessAzureTierPricing(
        AzureRegionName.SOUTH_AFRICA_NORTH, new double[] {0.132, 0.066, 0.013});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.SOUTH_AFRICA_NORTH, 1.323);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.SOUTH_AFRICA_NORTH, 0.370);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.SOUTH_AFRICA_NORTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.SOUTH_AFRICA_NORTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL,
        AzureRegionName.SOUTH_AFRICA_NORTH,
        0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.SOUTH_AFRICA_NORTH,
        0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET,
        AzureRegionName.SOUTH_AFRICA_NORTH,
        0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.SOUTH_AFRICA_NORTH, 3.300);

    // AZURE SOUTH_AFRICA_WEST
    assertServerlessAzureTierPricing(
        AzureRegionName.SOUTH_AFRICA_WEST, new double[] {0.165, 0.083, 0.017});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.SOUTH_AFRICA_WEST, 1.654);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.SOUTH_AFRICA_WEST, 0.464);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.SOUTH_AFRICA_WEST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.SOUTH_AFRICA_WEST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.SOUTH_AFRICA_WEST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.SOUTH_AFRICA_WEST,
        0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.SOUTH_AFRICA_WEST, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.SOUTH_AFRICA_WEST, 4.125);

    // AZURE US_SOUTH_CENTRAL
    assertServerlessAzureTierPricing(
        AzureRegionName.US_SOUTH_CENTRAL, new double[] {0.115, 0.057, 0.011});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.US_SOUTH_CENTRAL, 1.146);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.US_SOUTH_CENTRAL, 0.276);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.US_SOUTH_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.US_SOUTH_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.US_SOUTH_CENTRAL, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.US_SOUTH_CENTRAL,
        0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.US_SOUTH_CENTRAL, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.US_SOUTH_CENTRAL, 2.875);

    // AZURE ASIA_SOUTH_EAST
    assertServerlessAzureTierPricing(
        AzureRegionName.ASIA_SOUTH_EAST, new double[] {0.130, 0.065, 0.013});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.ASIA_SOUTH_EAST, 1.302);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.ASIA_SOUTH_EAST, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.ASIA_SOUTH_EAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.ASIA_SOUTH_EAST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.ASIA_SOUTH_EAST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.ASIA_SOUTH_EAST,
        0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.ASIA_SOUTH_EAST, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.ASIA_SOUTH_EAST, 3.250);

    // AZURE INDIA_SOUTH
    assertServerlessAzureTierPricing(
        AzureRegionName.INDIA_SOUTH, new double[] {0.141, 0.070, 0.014});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.INDIA_SOUTH, 1.409);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.INDIA_SOUTH, 0.274);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.INDIA_SOUTH, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.INDIA_SOUTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.INDIA_SOUTH, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.INDIA_SOUTH, 0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.INDIA_SOUTH, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.INDIA_SOUTH, 3.525);

    // AZURE SWITZERLAND_NORTH
    assertServerlessAzureTierPricing(
        AzureRegionName.SWITZERLAND_NORTH, new double[] {0.150, 0.075, 0.015});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.SWITZERLAND_NORTH, 1.500);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.SWITZERLAND_NORTH, 0.401);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.SWITZERLAND_NORTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.SWITZERLAND_NORTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.SWITZERLAND_NORTH, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.SWITZERLAND_NORTH,
        0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.SWITZERLAND_NORTH, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.SWITZERLAND_NORTH, 3.750);

    // AZURE SWITZERLAND_WEST
    assertServerlessAzureTierPricing(
        AzureRegionName.SWITZERLAND_WEST, new double[] {0.195, 0.097, 0.019});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.SWITZERLAND_WEST, 1.949);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.SWITZERLAND_WEST, 0.522);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.SWITZERLAND_WEST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.SWITZERLAND_WEST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.SWITZERLAND_WEST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.SWITZERLAND_WEST,
        0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.SWITZERLAND_WEST, 0.0875);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.SWITZERLAND_WEST, 4.875, 4.875);

    // AZURE UAE_CENTRAL
    assertServerlessAzureTierPricing(
        AzureRegionName.UAE_CENTRAL, new double[] {0.162, 0.081, 0.016});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.UAE_CENTRAL, 1.625);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.UAE_CENTRAL, 0.431);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.UAE_CENTRAL, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.UAE_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.UAE_CENTRAL, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.UAE_CENTRAL, 0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.UAE_CENTRAL, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.UAE_CENTRAL, 4.050);

    // AZURE UAE_NORTH
    assertServerlessAzureTierPricing(AzureRegionName.UAE_NORTH, new double[] {0.125, 0.063, 0.013});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.UAE_NORTH, 1.250);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.UAE_NORTH, 0.332);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.UAE_NORTH, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.UAE_NORTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.UAE_NORTH, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.UAE_NORTH, 0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.UAE_NORTH, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.UAE_NORTH, 3.125);

    // AZURE UK_SOUTH
    assertServerlessAzureTierPricing(AzureRegionName.UK_SOUTH, new double[] {0.121, 0.060, 0.012});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.UK_SOUTH, 1.208);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.UK_SOUTH, 0.368);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.UK_SOUTH, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.UK_SOUTH, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.UK_SOUTH, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.UK_SOUTH, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.UK_SOUTH, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.UK_SOUTH, 3.025);

    // AZURE UK_WEST
    assertServerlessAzureTierPricing(AzureRegionName.UK_WEST, new double[] {0.121, 0.060, 0.012});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.UK_WEST, 1.208);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.UK_WEST, 0.368);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.UK_WEST, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.UK_WEST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.UK_WEST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.UK_WEST, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.UK_WEST, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.UK_WEST, 3.025);

    // AZURE US_WEST_CENTRAL
    assertServerlessAzureTierPricing(
        AzureRegionName.US_WEST_CENTRAL, new double[] {0.115, 0.057, 0.011});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.US_WEST_CENTRAL, 1.146);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.US_WEST_CENTRAL, 0.276);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.US_WEST_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.US_WEST_CENTRAL, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.US_WEST_CENTRAL, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        AzureRegionName.US_WEST_CENTRAL,
        0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.US_WEST_CENTRAL, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.US_WEST_CENTRAL, 2.875);

    // AZURE US_WEST_3
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.US_WEST_3, 1.219);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.US_WEST_3, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.US_WEST_3, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.US_WEST_3, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.US_WEST_3, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.US_WEST_3, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.US_WEST_3, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.US_WEST_3, 2.750);

    // AZURE EUROPE_WEST
    assertServerlessAzureTierPricing(
        AzureRegionName.EUROPE_WEST, new double[] {0.125, 0.062, 0.012});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.EUROPE_WEST, 1.250);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.EUROPE_WEST, 0.334);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.EUROPE_WEST, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.EUROPE_WEST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.EUROPE_WEST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.EUROPE_WEST, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.EUROPE_WEST, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.EUROPE_WEST, 3.125);

    // AZURE INDIA_WEST
    assertServerlessAzureTierPricing(
        AzureRegionName.INDIA_WEST, new double[] {0.128, 0.064, 0.013});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.INDIA_WEST, 1.281);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.INDIA_WEST, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.INDIA_WEST, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.INDIA_WEST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.INDIA_WEST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.INDIA_WEST, 0.0800);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.INDIA_WEST, 0.1200);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.INDIA_WEST, 3.200);

    // AZURE US_WEST
    assertServerlessAzureTierPricing(AzureRegionName.US_WEST, new double[] {0.122, 0.061, 0.012});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.US_WEST, 1.219);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.US_WEST, 0.304);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.US_WEST, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.US_WEST, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.US_WEST, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.US_WEST, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.US_WEST, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.US_WEST, 3.050);

    // AZURE US_WEST_2
    assertServerlessAzureTierPricing(AzureRegionName.US_WEST_2, new double[] {0.100, 0.050, 0.010});
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_WPU, AzureRegionName.US_WEST_2, 1.000);
    assertServerlessStorageAndBackupPriceByAzureRegion(AzureRegionName.US_WEST_2, 0.276);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_PREVIEW, AzureRegionName.US_WEST_2, 0.0150);
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER, AzureRegionName.US_WEST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_REGIONAL, AzureRegionName.US_WEST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_CROSS_REGION, AzureRegionName.US_WEST_2, 0.0250);
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_DATA_TRANSFER_INTERNET, AzureRegionName.US_WEST_2, 0.0875);
    assertServerlessPriceByAzureRegion(
        SKU.NDS_AZURE_SERVERLESS_BACKUP_RESTORE_VM, AzureRegionName.US_WEST_2, 2.500);

    // GCP EASTERN_ASIA_PACIFIC
    assertServerlessGCPTierPricing(
        GCPRegionName.EASTERN_ASIA_PACIFIC, new double[] {0.110, 0.055, 0.011});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.EASTERN_ASIA_PACIFIC, 1.098);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.EASTERN_ASIA_PACIFIC, 0.302);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.EASTERN_ASIA_PACIFIC, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.EASTERN_ASIA_PACIFIC, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.EASTERN_ASIA_PACIFIC, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        GCPRegionName.EASTERN_ASIA_PACIFIC,
        0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.EASTERN_ASIA_PACIFIC, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.EASTERN_ASIA_PACIFIC, 2.750);

    // GCP ASIA_EAST_2
    assertServerlessGCPTierPricing(GCPRegionName.ASIA_EAST_2, new double[] {0.133, 0.066, 0.013});
    assertServerlessPriceByGcpRegion(SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.ASIA_EAST_2, 1.326);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.ASIA_EAST_2, 0.332);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.ASIA_EAST_2, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.ASIA_EAST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.ASIA_EAST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.ASIA_EAST_2, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.ASIA_EAST_2, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.ASIA_EAST_2, 3.325);

    // GCP NORTHEASTERN_ASIA_PACIFIC
    assertServerlessGCPTierPricing(
        GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, new double[] {0.122, 0.061, 0.012});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 1.216);
    assertServerlessStorageAndBackupPriceByGcpRegion(
        GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 0.392);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW,
        GCPRegionName.NORTHEASTERN_ASIA_PACIFIC,
        0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL,
        GCPRegionName.NORTHEASTERN_ASIA_PACIFIC,
        0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        GCPRegionName.NORTHEASTERN_ASIA_PACIFIC,
        0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET,
        GCPRegionName.NORTHEASTERN_ASIA_PACIFIC,
        0.1400);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.NORTHEASTERN_ASIA_PACIFIC, 3.050);

    // GCP ASIA_NORTHEAST_2
    assertServerlessGCPTierPricing(
        GCPRegionName.ASIA_NORTHEAST_2, new double[] {0.122, 0.061, 0.012});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.ASIA_NORTHEAST_2, 1.216);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.ASIA_NORTHEAST_2, 0.392);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.ASIA_NORTHEAST_2, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.ASIA_NORTHEAST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.ASIA_NORTHEAST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.ASIA_NORTHEAST_2, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.ASIA_NORTHEAST_2, 0.1400);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.ASIA_NORTHEAST_2, 3.050);

    // GCP ASIA_NORTHEAST_3
    assertServerlessGCPTierPricing(
        GCPRegionName.ASIA_NORTHEAST_3, new double[] {0.122, 0.061, 0.012});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.ASIA_NORTHEAST_3, 1.216);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.ASIA_NORTHEAST_3, 0.392);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.ASIA_NORTHEAST_3, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.ASIA_NORTHEAST_3, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.ASIA_NORTHEAST_3, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.ASIA_NORTHEAST_3, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.ASIA_NORTHEAST_3, 0.1470);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.ASIA_NORTHEAST_3, 3.050);

    // GCP ASIA_SOUTH_1
    assertServerlessGCPTierPricing(GCPRegionName.ASIA_SOUTH_1, new double[] {0.114, 0.057, 0.011});
    assertServerlessPriceByGcpRegion(SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.ASIA_SOUTH_1, 1.139);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.ASIA_SOUTH_1, 0.362);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.ASIA_SOUTH_1, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.ASIA_SOUTH_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.ASIA_SOUTH_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.ASIA_SOUTH_1, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.ASIA_SOUTH_1, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.ASIA_SOUTH_1, 2.850);

    // GCP SOUTHEASTERN_ASIA_PACIFIC
    assertServerlessGCPTierPricing(
        GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, new double[] {0.117, 0.058, 0.012});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 1.169);
    assertServerlessStorageAndBackupPriceByGcpRegion(
        GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 0.332);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW,
        GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC,
        0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL,
        GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC,
        0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC,
        0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET,
        GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC,
        0.1400);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC, 2.925);

    // GCP ASIA_SOUTHEAST_2
    assertServerlessGCPTierPricing(
        GCPRegionName.ASIA_SOUTHEAST_2, new double[] {0.127, 0.064, 0.013});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.ASIA_SOUTHEAST_2, 1.275);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.ASIA_SOUTHEAST_2, 0.392);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.ASIA_SOUTHEAST_2, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.ASIA_SOUTHEAST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.ASIA_SOUTHEAST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.ASIA_SOUTHEAST_2, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.ASIA_SOUTHEAST_2, 0.1900);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.ASIA_SOUTHEAST_2, 3.175);

    // GCP AUSTRALIA_SOUTHEAST_1
    assertServerlessGCPTierPricing(
        GCPRegionName.AUSTRALIA_SOUTHEAST_1, new double[] {0.134, 0.067, 0.013});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 1.345);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.408);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        GCPRegionName.AUSTRALIA_SOUTHEAST_1,
        0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 0.1900);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.AUSTRALIA_SOUTHEAST_1, 3.350);

    // GCP EUROPE_NORTH_1
    assertServerlessGCPTierPricing(
        GCPRegionName.EUROPE_NORTH_1, new double[] {0.104, 0.052, 0.010});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.EUROPE_NORTH_1, 1.044);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.EUROPE_NORTH_1, 0.332);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.EUROPE_NORTH_1, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.EUROPE_NORTH_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.EUROPE_NORTH_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.EUROPE_NORTH_1, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.EUROPE_NORTH_1, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.EUROPE_NORTH_1, 2.600);

    // GCP WESTERN_EUROPE
    assertServerlessGCPTierPricing(
        GCPRegionName.WESTERN_EUROPE, new double[] {0.104, 0.052, 0.010});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.WESTERN_EUROPE, 1.043);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.WESTERN_EUROPE, 0.302);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.WESTERN_EUROPE, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.WESTERN_EUROPE, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.WESTERN_EUROPE, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.WESTERN_EUROPE, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.WESTERN_EUROPE, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.WESTERN_EUROPE, 2.600);

    // GCP EUROPE_WEST_2
    assertServerlessGCPTierPricing(GCPRegionName.EUROPE_WEST_2, new double[] {0.122, 0.061, 0.012});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.EUROPE_WEST_2, 1.221);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.EUROPE_WEST_2, 0.362);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.EUROPE_WEST_2, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.EUROPE_WEST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.EUROPE_WEST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.EUROPE_WEST_2, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.EUROPE_WEST_2, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.EUROPE_WEST_2, 3.050);

    // GCP EUROPE_WEST_3
    assertServerlessGCPTierPricing(GCPRegionName.EUROPE_WEST_3, new double[] {0.122, 0.061, 0.012});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.EUROPE_WEST_3, 1.221);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.EUROPE_WEST_3, 0.362);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.EUROPE_WEST_3, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.EUROPE_WEST_3, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.EUROPE_WEST_3, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.EUROPE_WEST_3, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.EUROPE_WEST_3, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.EUROPE_WEST_3, 3.050);

    // GCP EUROPE_WEST_4
    assertServerlessGCPTierPricing(GCPRegionName.EUROPE_WEST_4, new double[] {0.104, 0.052, 0.010});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.EUROPE_WEST_4, 1.044);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.EUROPE_WEST_4, 0.332);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.EUROPE_WEST_4, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.EUROPE_WEST_4, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.EUROPE_WEST_4, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.EUROPE_WEST_4, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.EUROPE_WEST_4, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.EUROPE_WEST_4, 2.600);

    // GCP EUROPE_WEST_6
    assertServerlessGCPTierPricing(GCPRegionName.EUROPE_WEST_6, new double[] {0.122, 0.061, 0.012});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.EUROPE_WEST_6, 1.221);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.EUROPE_WEST_6, 0.392);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.EUROPE_WEST_6, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.EUROPE_WEST_6, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.EUROPE_WEST_6, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.EUROPE_WEST_6, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.EUROPE_WEST_6, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.EUROPE_WEST_6, 3.050);

    // GCP NORTH_AMERICA_NORTHEAST_1
    assertServerlessGCPTierPricing(
        GCPRegionName.NORTH_AMERICA_NORTHEAST_1, new double[] {0.104, 0.052, 0.010});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 1.044);
    assertServerlessStorageAndBackupPriceByGcpRegion(
        GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 0.332);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW,
        GCPRegionName.NORTH_AMERICA_NORTHEAST_1,
        0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL,
        GCPRegionName.NORTH_AMERICA_NORTHEAST_1,
        0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        GCPRegionName.NORTH_AMERICA_NORTHEAST_1,
        0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET,
        GCPRegionName.NORTH_AMERICA_NORTHEAST_1,
        0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, 2.600);

    // GCP SOUTH_AMERICA_EAST_1
    assertServerlessGCPTierPricing(
        GCPRegionName.SOUTH_AMERICA_EAST_1, new double[] {0.150, 0.075, 0.015});
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.SOUTH_AMERICA_EAST_1, 1.504);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.SOUTH_AMERICA_EAST_1, 0.453);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.SOUTH_AMERICA_EAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.SOUTH_AMERICA_EAST_1, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.SOUTH_AMERICA_EAST_1, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION,
        GCPRegionName.SOUTH_AMERICA_EAST_1,
        0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.SOUTH_AMERICA_EAST_1, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.SOUTH_AMERICA_EAST_1, 3.750);

    // GCP CENTRAL_US
    assertServerlessGCPTierPricing(GCPRegionName.CENTRAL_US, new double[] {0.095, 0.047, 0.009});
    assertServerlessPriceByGcpRegion(SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.CENTRAL_US, 0.949);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.CENTRAL_US, 0.302);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.CENTRAL_US, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.CENTRAL_US, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.CENTRAL_US, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.CENTRAL_US, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.CENTRAL_US, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.CENTRAL_US, 2.375);

    // GCP EASTERN_US
    assertServerlessGCPTierPricing(GCPRegionName.EASTERN_US, new double[] {0.095, 0.047, 0.009});
    assertServerlessPriceByGcpRegion(SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.EASTERN_US, 0.949);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.EASTERN_US, 0.302);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.EASTERN_US, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.EASTERN_US, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.EASTERN_US, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.EASTERN_US, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.EASTERN_US, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.EASTERN_US, 2.375);

    // GCP US_EAST_4
    assertServerlessGCPTierPricing(GCPRegionName.US_EAST_4, new double[] {0.107, 0.053, 0.011});
    assertServerlessPriceByGcpRegion(SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.US_EAST_4, 1.068);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.US_EAST_4, 0.332);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.US_EAST_4, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.US_EAST_4, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.US_EAST_4, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.US_EAST_4, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.US_EAST_4, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.US_EAST_4, 2.675);

    // GCP WESTERN_US
    assertServerlessGCPTierPricing(GCPRegionName.WESTERN_US, new double[] {0.095, 0.047, 0.009});
    assertServerlessPriceByGcpRegion(SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.WESTERN_US, 0.949);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.WESTERN_US, 0.302);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.WESTERN_US, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.WESTERN_US, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.WESTERN_US, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.WESTERN_US, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.WESTERN_US, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.WESTERN_US, 2.375);

    // GCP US_WEST_2
    assertServerlessGCPTierPricing(GCPRegionName.US_WEST_2, new double[] {0.114, 0.057, 0.011});
    assertServerlessPriceByGcpRegion(SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.US_WEST_2, 1.139);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.US_WEST_2, 0.362);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.US_WEST_2, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.US_WEST_2, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.US_WEST_2, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.US_WEST_2, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.US_WEST_2, 0.1200);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.US_WEST_2, 2.850, 2.850);

    // GCP US_WEST_3
    assertServerlessGCPTierPricing(GCPRegionName.US_WEST_3, new double[] {0.114, 0.057, 0.011});
    assertServerlessPriceByGcpRegion(SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.US_WEST_3, 1.139);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.US_WEST_3, 0.362);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.US_WEST_3, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.US_WEST_3, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.US_WEST_3, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.US_WEST_3, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.US_WEST_3, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.US_WEST_3, 2.850);

    // GCP US_WEST_4
    assertServerlessGCPTierPricing(GCPRegionName.US_WEST_4, new double[] {0.117, 0.059, 0.012});
    assertServerlessPriceByGcpRegion(SKU.NDS_GCP_SERVERLESS_WPU, GCPRegionName.US_WEST_4, 1.175);
    assertServerlessStorageAndBackupPriceByGcpRegion(GCPRegionName.US_WEST_4, 0.332);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_PREVIEW, GCPRegionName.US_WEST_4, 0.0150);
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER, GCPRegionName.US_WEST_4, 0.0150);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_REGIONAL, GCPRegionName.US_WEST_4, 0.0100);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_CROSS_REGION, GCPRegionName.US_WEST_4, 0.0250);
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_DATA_TRANSFER_INTERNET, GCPRegionName.US_WEST_4, 0.1200);
    assertServerlessPriceByGcpRegion(
        SKU.NDS_GCP_SERVERLESS_BACKUP_RESTORE_VM, GCPRegionName.US_WEST_4, 2.925);
  }

  private void assertServerlessPriceByAwsRegion(SKU sku, AWSRegionName region, double price) {
    assertEquals(
        sku.getInfo().getRegionUnitPriceDollars(region, TODAY),
        price,
        EPSILON,
        sku + " SKU has wrong price for " + region);
    assertEquals(
        sku.getInfo().getRegionUnitPriceDollars(region, new Date()),
        price,
        EPSILON,
        sku + " SKU has wrong price for " + region + " region for current date");
  }

  private void assertServerlessAWSTierPricing(
      AWSRegionName awsRegionName, double[] unitPriceDollars) {
    assertServerlessTierPricing(SKU.NDS_AWS_SERVERLESS_RPU, awsRegionName, unitPriceDollars);
  }

  private void assertServerlessTierPricing(
      SKU sku, RegionName regionName, double[] unitPriceDollars) {
    SKUInfo skuInfo = sku.getInfo();
    for (int i = 1; i <= unitPriceDollars.length; i++) {
      assertEquals(
          unitPriceDollars[i - 1],
          skuInfo.getPricingForDate(TODAY).getTieredRegionUnitPriceDollars(regionName, i),
          EPSILON);

      assertEquals(
          unitPriceDollars[i - 1],
          skuInfo.getPricingForDate(new Date()).getTieredRegionUnitPriceDollars(regionName, i),
          EPSILON);
    }
  }

  private void assertServerlessPriceByGcpRegion(SKU sku, GCPRegionName region, double price) {
    SkuPricing pricing = sku.getInfo().getPricingForDate(TODAY);
    assertEquals(
        pricing.getRegionUnitPriceDollars(region),
        price,
        EPSILON,
        sku + " SKU has wrong price for " + region);
    assertEquals(
        sku.getInfo().getPricingForDate(new Date()).getRegionUnitPriceDollars(region),
        price,
        EPSILON,
        sku + " SKU has wrong price for " + region + " region for current date");
  }

  private void assertServerlessGCPTierPricing(
      GCPRegionName gcpRegionName, double[] postMDBWorldUnitPriceDollars) {
    assertServerlessTierPricing(
        SKU.NDS_GCP_SERVERLESS_RPU, gcpRegionName, postMDBWorldUnitPriceDollars);
  }

  private void assertServerlessPriceByAzureRegion(SKU sku, AzureRegionName region, double price) {
    assertEquals(
        sku.getInfo().getRegionUnitPriceDollars(region, TODAY),
        price,
        EPSILON,
        sku + " SKU has wrong price for " + region);
    assertEquals(
        sku.getInfo().getRegionUnitPriceDollars(region, new Date()),
        price,
        EPSILON,
        sku + " SKU has wrong price for " + region + " region for current date");
  }

  private void assertServerlessAzureTierPricing(
      AzureRegionName azureRegionName, double[] postMDBWorldUnitPriceDollars) {
    assertServerlessTierPricing(
        SKU.NDS_AZURE_SERVERLESS_RPU, azureRegionName, postMDBWorldUnitPriceDollars);
  }

  private void assertServerlessStorageAndBackupPriceByAwsRegion(
      AWSRegionName region, double baseCost) {
    assertRegionPrice(SKU.NDS_AWS_SERVERLESS_STORAGE, region, SKU.monthlyToDailyCost(baseCost));
    assertRegionPrice(
        SKU.NDS_AWS_SERVERLESS_CONTINUOUS_BACKUP,
        region,
        // Currently, serverless premium backup is marked down 20% relative to
        // serverless storage.  This could change over time.
        SKU.monthlyToDailyCost(0.8 * baseCost),
        // Put more narrow epsilon for this given that double math is not exact.
        0.001);
  }

  private void assertServerlessStorageAndBackupPriceByAzureRegion(
      AzureRegionName region, double baseCost) {
    assertRegionPrice(SKU.NDS_AZURE_SERVERLESS_STORAGE, region, SKU.monthlyToDailyCost(baseCost));
    assertRegionPrice(
        SKU.NDS_AZURE_SERVERLESS_CONTINUOUS_BACKUP,
        region,
        // Currently, premium backup is marked down 20% relative to serverless storage.
        // This could change over time.
        SKU.monthlyToDailyCost(0.8 * baseCost),
        // Put more narrow epsilon for this given that double math is not exact.
        0.001);
  }

  private void assertServerlessStorageAndBackupPriceByGcpRegion(
      GCPRegionName region, double baseCost) {
    assertRegionPrice(SKU.NDS_GCP_SERVERLESS_STORAGE, region, SKU.monthlyToDailyCost(baseCost));
    assertRegionPrice(
        SKU.NDS_GCP_SERVERLESS_CONTINUOUS_BACKUP,
        region,
        // Currently, premium backup is marked down 20% relative to serverless storage.
        // This could change over time.
        SKU.monthlyToDailyCost(0.8 * baseCost),
        // Put more narrow epsilon for this given that double math is not exact.
        0.001);
  }

  /**
   * @see <a
   *     href=https://docs.google.com/spreadsheets/d/1t9x4WuLEL5HP_gzTjnf7jx4aXIOxDR1H3xYD8zi4Ez0/edit#gid=40584641>Proposed
   *     Archive Billing Changes</a>
   */
  @Test
  public void testArchiveAccessPartitionLocate_awsRegionPrice() {
    BiConsumer<AWSRegionName, Double> t =
        (pRegion, pPrice) -> assertRegionPrice(SKU.NDS_AWS_OBJECT_STORAGE_SEEK, pRegion, pPrice);

    t.accept(AWSRegionName.US_EAST_1, 0.0125000);
    t.accept(AWSRegionName.US_EAST_2, 0.0125000);
    t.accept(AWSRegionName.US_WEST_1, 0.01375000);
    t.accept(AWSRegionName.US_WEST_2, 0.0125000);
    t.accept(AWSRegionName.AF_SOUTH_1, 0.0150000);
    t.accept(AWSRegionName.AP_EAST_1, 0.0125000);
    t.accept(AWSRegionName.AP_SOUTH_1, 0.0125000);
    t.accept(AWSRegionName.AP_NORTHEAST_3, 0.0117500);
    t.accept(AWSRegionName.AP_NORTHEAST_2, 0.0112500);
    t.accept(AWSRegionName.AP_SOUTHEAST_1, 0.0125000);
    t.accept(AWSRegionName.AP_SOUTHEAST_2, 0.0137500);
    t.accept(AWSRegionName.AP_NORTHEAST_1, 0.0117500);
    t.accept(AWSRegionName.CA_CENTRAL_1, 0.0137500);
    t.accept(AWSRegionName.EU_CENTRAL_1, 0.0135000);
    t.accept(AWSRegionName.EU_WEST_1, 0.0125000);
    t.accept(AWSRegionName.EU_WEST_2, 0.0132500);
    t.accept(AWSRegionName.EU_WEST_3, 0.0132500);
    t.accept(AWSRegionName.EU_NORTH_1, 0.0125000);
    t.accept(AWSRegionName.EU_SOUTH_1, 0.0132500);
    t.accept(AWSRegionName.ME_SOUTH_1, 0.0137500);
    t.accept(AWSRegionName.SA_EAST_1, 0.0175000);
  }

  /**
   * @see <a
   *     href=https://docs.google.com/spreadsheets/d/1t9x4WuLEL5HP_gzTjnf7jx4aXIOxDR1H3xYD8zi4Ez0/edit#gid=40584641>Proposed
   *     Archive Billing Changes</a>
   */
  @Test
  public void testArchiveAccessPartitionRetrieve_awsRegionPrice() {
    BiConsumer<AWSRegionName, Double> t =
        (pRegion, pPrice) -> assertRegionPrice(SKU.NDS_AWS_OBJECT_STORAGE_ACCESS, pRegion, pPrice);
    t.accept(AWSRegionName.US_EAST_1, 0.0010000);
    t.accept(AWSRegionName.US_EAST_2, 0.0010000);
    t.accept(AWSRegionName.US_WEST_1, 0.0011000);
    t.accept(AWSRegionName.US_WEST_2, 0.0010000);
    t.accept(AWSRegionName.AF_SOUTH_1, 0.0010000);
    t.accept(AWSRegionName.AP_EAST_1, 0.0010000);
    t.accept(AWSRegionName.AP_SOUTH_1, 0.0010000);
    t.accept(AWSRegionName.AP_NORTHEAST_3, 0.0009250);
    t.accept(AWSRegionName.AP_NORTHEAST_2, 0.0008750);
    t.accept(AWSRegionName.AP_SOUTHEAST_1, 0.0010000);
    t.accept(AWSRegionName.AP_SOUTHEAST_2, 0.0011000);
    t.accept(AWSRegionName.AP_NORTHEAST_1, 0.0009250);
    t.accept(AWSRegionName.CA_CENTRAL_1, 0.0011000);
    t.accept(AWSRegionName.EU_CENTRAL_1, 0.0010750);
    t.accept(AWSRegionName.EU_WEST_1, 0.0010000);
    t.accept(AWSRegionName.EU_WEST_2, 0.0010500);
    t.accept(AWSRegionName.EU_WEST_3, 0.0010500);
    t.accept(AWSRegionName.EU_NORTH_1, 0.0010000);
    t.accept(AWSRegionName.EU_SOUTH_1, 0.0010000);
    t.accept(AWSRegionName.ME_SOUTH_1, 0.0011000);
    t.accept(AWSRegionName.SA_EAST_1, 0.0014000);
  }

  /**
   * @see <a
   *     href=https://docs.google.com/spreadsheets/d/1QSapT1jXNoRk-ozKTxJCHTQVoFcl9jEgdZ9tQ1pQfAQ/edit#gid=0>Proposed
   *     Archive Billing Changes</a>
   */
  @Test
  public void testArchiveAccessPartitionRetrieve_azureRegionPrice() {
    BiConsumer<AzureRegionName, Double> t =
        (pRegion, pPrice) -> assertRegionPrice(NDS_AZURE_OBJECT_STORAGE_ACCESS, pRegion, pPrice);

    t.accept(AzureRegionName.US_EAST, 0.0009);
    t.accept(AzureRegionName.US_EAST_2, 0.0009);
    t.accept(AzureRegionName.EUROPE_NORTH, 0.0009);
    t.accept(AzureRegionName.EUROPE_WEST, 0.0010);
    t.accept(AzureRegionName.AUSTRALIA_EAST, 0.0010);
    t.accept(AzureRegionName.ASIA_SOUTH_EAST, 0.0009);
    t.accept(AzureRegionName.US_WEST, 0.0009);
    t.accept(AzureRegionName.BRAZIL_SOUTH, 0.0014);
  }

  /**
   * @see <a
   *     href=https://docs.google.com/document/d/1SEhqcqaFiITRm7bq1Tt-wsLfM7ejmSqTTMPT-SckYPU/edit>
   *     [Billing Change Request] GCP for Atlas Data Federation, Data Lake, Online Archives</a>
   */
  @Test
  public void testNdsGcpObjectStorageAccess_gcpRegionPrice() {
    BiConsumer<GCPRegionName, Double> t =
        (pRegion, pPrice) -> assertRegionPrice(NDS_GCP_OBJECT_STORAGE_ACCESS, pRegion, pPrice);

    t.accept(GCPRegionName.CENTRAL_US, 0.0008);
    t.accept(GCPRegionName.WESTERN_EUROPE, 0.0008);
  }

  /**
   * @see <a
   *     href=https://docs.google.com/spreadsheets/d/1t9x4WuLEL5HP_gzTjnf7jx4aXIOxDR1H3xYD8zi4Ez0/edit#gid=40584641>Proposed
   *     Archive Billing Changes</a>
   */
  @Test
  public void testNdsAwsObjectStorage_awsRegionPrice() {
    BiConsumer<AWSRegionName, Double> t =
        (pRegion, pPrice) -> assertRegionPrice(SKU.NDS_AWS_OBJECT_STORAGE, pRegion, pPrice);
    t.accept(AWSRegionName.US_EAST_1, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.US_EAST_2, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.US_WEST_1, SKU.monthlyToDailyCost(0.0543));
    t.accept(AWSRegionName.US_WEST_2, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.AF_SOUTH_1, SKU.monthlyToDailyCost(0.0572));
    t.accept(AWSRegionName.AP_EAST_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_SOUTH_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_NORTHEAST_3, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_NORTHEAST_2, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_SOUTHEAST_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_SOUTHEAST_2, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_NORTHEAST_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.CA_CENTRAL_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.EU_CENTRAL_1, SKU.monthlyToDailyCost(0.0511));
    t.accept(AWSRegionName.EU_WEST_1, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.EU_WEST_2, SKU.monthlyToDailyCost(0.0501));
    t.accept(AWSRegionName.EU_WEST_3, SKU.monthlyToDailyCost(0.0501));
    t.accept(AWSRegionName.EU_NORTH_1, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.EU_SOUTH_1, SKU.monthlyToDailyCost(0.0501));
    t.accept(AWSRegionName.ME_SOUTH_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.SA_EAST_1, SKU.monthlyToDailyCost(0.0845));
  }

  /**
   * @see <a
   *     href=https://docs.google.com/spreadsheets/d/1t9x4WuLEL5HP_gzTjnf7jx4aXIOxDR1H3xYD8zi4Ez0/edit#gid=40584641>Proposed
   *     Archive Billing Changes</a>
   */
  @Test
  public void testNdsAwsObjectStorageV3_awsRegionPrice() {
    BiConsumer<AWSRegionName, Double> t =
        (pRegion, pPrice) -> assertRegionPrice(SKU.NDS_AWS_OBJECT_STORAGE_V3, pRegion, pPrice);
    t.accept(AWSRegionName.US_EAST_1, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.US_EAST_2, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.US_WEST_1, SKU.monthlyToDailyCost(0.0543));
    t.accept(AWSRegionName.US_WEST_2, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.AF_SOUTH_1, SKU.monthlyToDailyCost(0.0572));
    t.accept(AWSRegionName.AP_EAST_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_SOUTH_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_NORTHEAST_3, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_NORTHEAST_2, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_SOUTHEAST_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_SOUTHEAST_2, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_NORTHEAST_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.CA_CENTRAL_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.EU_CENTRAL_1, SKU.monthlyToDailyCost(0.0511));
    t.accept(AWSRegionName.EU_WEST_1, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.EU_WEST_2, SKU.monthlyToDailyCost(0.0501));
    t.accept(AWSRegionName.EU_WEST_3, SKU.monthlyToDailyCost(0.0501));
    t.accept(AWSRegionName.EU_NORTH_1, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.EU_SOUTH_1, SKU.monthlyToDailyCost(0.0501));
    t.accept(AWSRegionName.ME_SOUTH_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.SA_EAST_1, SKU.monthlyToDailyCost(0.0845));
  }

  /**
   * @see <a
   *     href=https://docs.google.com/document/d/1SEhqcqaFiITRm7bq1Tt-wsLfM7ejmSqTTMPT-SckYPU/edit>
   *     [Billing Change Request] GCP for Atlas Data Federation, Data Lake, Online Archives</a>
   */
  @Test
  public void testNdsGcpObjectStorage_gcpRegionPrice() {
    BiConsumer<GCPRegionName, Double> t =
        (pRegion, pPrice) -> assertRegionPrice(NDS_GCP_OBJECT_STORAGE, pRegion, pPrice);

    t.accept(GCPRegionName.CENTRAL_US, 0.001347945205479452);
    t.accept(GCPRegionName.WESTERN_EUROPE, 0.001347945205479452);
  }

  /**
   * @see <a
   *     href=https://docs.google.com/spreadsheets/d/1QSapT1jXNoRk-ozKTxJCHTQVoFcl9jEgdZ9tQ1pQfAQ/edit#gid=0>Proposed
   *     Archive Billing Changes</a>
   */
  @Test
  public void testNdsAzureObjectStorage_azureRegionPrice() {
    BiConsumer<AzureRegionName, Double> t =
        (pRegion, pPrice) -> assertRegionPrice(NDS_AZURE_OBJECT_STORAGE, pRegion, pPrice);

    t.accept(AzureRegionName.US_EAST, SKU.monthlyToDailyCost(0.054));
    t.accept(AzureRegionName.US_EAST_2, SKU.monthlyToDailyCost(0.048));
    t.accept(AzureRegionName.EUROPE_NORTH, SKU.monthlyToDailyCost(0.048));
    t.accept(AzureRegionName.EUROPE_WEST, SKU.monthlyToDailyCost(0.050));
    t.accept(AzureRegionName.AUSTRALIA_EAST, SKU.monthlyToDailyCost(0.052));
    t.accept(AzureRegionName.ASIA_SOUTH_EAST, SKU.monthlyToDailyCost(0.052));
    t.accept(AzureRegionName.US_WEST, SKU.monthlyToDailyCost(0.054));
    t.accept(AzureRegionName.BRAZIL_SOUTH, SKU.monthlyToDailyCost(0.0851));
  }

  /**
   * @see <a
   *     href=https://docs.google.com/spreadsheets/d/1t9x4WuLEL5HP_gzTjnf7jx4aXIOxDR1H3xYD8zi4Ez0/edit#gid=40584641>Proposed
   *     Archive Billing Changes</a>
   */
  @Test
  public void testDataLakeArchiveAccessPartitionRetrieve_awsRegionPrice() {
    BiConsumer<AWSRegionName, Double> t =
        (pRegion, pPrice) ->
            assertRegionPrice(SKU.NDS_AWS_DATA_LAKE_STORAGE_ACCESS, pRegion, pPrice);
    t.accept(AWSRegionName.US_EAST_1, 0.0010000);
    t.accept(AWSRegionName.US_EAST_2, 0.0010000);
    t.accept(AWSRegionName.US_WEST_1, 0.0011000);
    t.accept(AWSRegionName.US_WEST_2, 0.0010000);
    t.accept(AWSRegionName.AF_SOUTH_1, 0.0010000);
    t.accept(AWSRegionName.AP_EAST_1, 0.0010000);
    t.accept(AWSRegionName.AP_SOUTH_1, 0.0010000);
    t.accept(AWSRegionName.AP_NORTHEAST_3, 0.0009250);
    t.accept(AWSRegionName.AP_NORTHEAST_2, 0.0008750);
    t.accept(AWSRegionName.AP_SOUTHEAST_1, 0.0010000);
    t.accept(AWSRegionName.AP_SOUTHEAST_2, 0.0011000);
    t.accept(AWSRegionName.AP_NORTHEAST_1, 0.0009250);
    t.accept(AWSRegionName.CA_CENTRAL_1, 0.0011000);
    t.accept(AWSRegionName.EU_CENTRAL_1, 0.0010750);
    t.accept(AWSRegionName.EU_WEST_1, 0.0010000);
    t.accept(AWSRegionName.EU_WEST_2, 0.0010500);
    t.accept(AWSRegionName.EU_WEST_3, 0.0010500);
    t.accept(AWSRegionName.EU_NORTH_1, 0.0010000);
    t.accept(AWSRegionName.EU_SOUTH_1, 0.0010000);
    t.accept(AWSRegionName.ME_SOUTH_1, 0.0011000);
    t.accept(AWSRegionName.SA_EAST_1, 0.0014000);
  }

  /**
   * @see <a
   *     href=https://docs.google.com/spreadsheets/d/1QSapT1jXNoRk-ozKTxJCHTQVoFcl9jEgdZ9tQ1pQfAQ/edit#gid=0>Proposed
   *     Archive Billing Changes</a>
   */
  @Test
  public void testDataLakeArchiveAccessPartitionRetrieve_azureRegionPrice() {
    BiConsumer<AzureRegionName, Double> t =
        (pRegion, pPrice) ->
            assertRegionPrice(SKU.NDS_AZURE_DATA_LAKE_STORAGE_ACCESS, pRegion, pPrice);

    t.accept(AzureRegionName.US_EAST, 0.0009);
    t.accept(AzureRegionName.US_EAST_2, 0.0009);
    t.accept(AzureRegionName.EUROPE_NORTH, 0.0009);
    t.accept(AzureRegionName.EUROPE_WEST, 0.0010);
    t.accept(AzureRegionName.AUSTRALIA_EAST, 0.0010);
    t.accept(AzureRegionName.ASIA_SOUTH_EAST, 0.0009);
    t.accept(AzureRegionName.US_WEST, 0.0009);
    t.accept(AzureRegionName.BRAZIL_SOUTH, 0.0014);
  }

  /**
   * @see <a
   *     href=https://docs.google.com/spreadsheets/d/1t9x4WuLEL5HP_gzTjnf7jx4aXIOxDR1H3xYD8zi4Ez0/edit#gid=40584641>Proposed
   *     Archive Billing Changes</a>
   */
  @Test
  public void testNdsAwsDataLakeObjectStorage_awsRegionPrice() {
    BiConsumer<AWSRegionName, Double> t =
        (pRegion, pPrice) -> assertRegionPrice(SKU.NDS_AWS_DATA_LAKE_STORAGE, pRegion, pPrice);
    t.accept(AWSRegionName.US_EAST_1, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.US_EAST_2, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.US_WEST_1, SKU.monthlyToDailyCost(0.0543));
    t.accept(AWSRegionName.US_WEST_2, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.AF_SOUTH_1, SKU.monthlyToDailyCost(0.0572));
    t.accept(AWSRegionName.AP_EAST_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_SOUTH_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_NORTHEAST_3, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_NORTHEAST_2, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_SOUTHEAST_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_SOUTHEAST_2, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.AP_NORTHEAST_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.CA_CENTRAL_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.EU_CENTRAL_1, SKU.monthlyToDailyCost(0.0511));
    t.accept(AWSRegionName.EU_WEST_1, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.EU_WEST_2, SKU.monthlyToDailyCost(0.0501));
    t.accept(AWSRegionName.EU_WEST_3, SKU.monthlyToDailyCost(0.0501));
    t.accept(AWSRegionName.EU_NORTH_1, SKU.monthlyToDailyCost(0.0480));
    t.accept(AWSRegionName.EU_SOUTH_1, SKU.monthlyToDailyCost(0.0501));
    t.accept(AWSRegionName.ME_SOUTH_1, SKU.monthlyToDailyCost(0.0522));
    t.accept(AWSRegionName.SA_EAST_1, SKU.monthlyToDailyCost(0.0845));
  }

  /**
   * @see <a
   *     href=https://docs.google.com/spreadsheets/d/1QSapT1jXNoRk-ozKTxJCHTQVoFcl9jEgdZ9tQ1pQfAQ/edit#gid=0>Proposed
   *     Archive Billing Changes</a>
   */
  @Test
  public void testNdsAzureDataLakeObjectStorage_azureRegionPrice() {
    BiConsumer<AzureRegionName, Double> t =
        (pRegion, pPrice) -> assertRegionPrice(SKU.NDS_AZURE_DATA_LAKE_STORAGE, pRegion, pPrice);

    t.accept(AzureRegionName.US_EAST, SKU.monthlyToDailyCost(0.054));
    t.accept(AzureRegionName.US_EAST_2, SKU.monthlyToDailyCost(0.048));
    t.accept(AzureRegionName.EUROPE_NORTH, SKU.monthlyToDailyCost(0.048));
    t.accept(AzureRegionName.EUROPE_WEST, SKU.monthlyToDailyCost(0.050));
    t.accept(AzureRegionName.AUSTRALIA_EAST, SKU.monthlyToDailyCost(0.052));
    t.accept(AzureRegionName.ASIA_SOUTH_EAST, SKU.monthlyToDailyCost(0.052));
    t.accept(AzureRegionName.US_WEST, SKU.monthlyToDailyCost(0.054));
    t.accept(AzureRegionName.BRAZIL_SOUTH, SKU.monthlyToDailyCost(0.0851));
  }

  @Test
  public void testStreamDataTransferPricing() {
    BiConsumer<AWSRegionName, Double> awsCheck =
        (region, price) ->
            assertRegionPrice(SKU.NDS_AWS_STREAM_PROCESSING_DATA_TRANSFER, region, price);
    BiConsumer<AzureRegionName, Double> azureCheck =
        (region, price) ->
            assertRegionPrice(SKU.NDS_AZURE_STREAM_PROCESSING_DATA_TRANSFER, region, price);
    awsCheck.accept(AWSRegionName.US_EAST_1, 0.090);
    awsCheck.accept(AWSRegionName.US_EAST_2, 0.090);
    awsCheck.accept(AWSRegionName.AP_NORTHEAST_1, 0.114);
    awsCheck.accept(AWSRegionName.CA_CENTRAL_1, 0.090);

    azureCheck.accept(AzureRegionName.US_WEST, 0.088);
  }

  @Test
  public void testStreamInstancePricing() {
    BiConsumer<AzureRegionName, Double> azureSP10Check =
        (region, price) ->
            assertRegionPrice(SKU.NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP10, region, price);
    BiConsumer<AzureRegionName, Double> azureSP30Check =
        (region, price) ->
            assertRegionPrice(SKU.NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP30, region, price);
    BiConsumer<AzureRegionName, Double> azureSP50Check =
        (region, price) ->
            assertRegionPrice(SKU.NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP50, region, price);
    BiConsumer<AWSRegionName, Double> awsSP10Check =
        (region, price) ->
            assertRegionPrice(SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP10, region, price);
    BiConsumer<AWSRegionName, Double> awsSP30Check =
        (region, price) ->
            assertRegionPrice(SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP30, region, price);
    BiConsumer<AWSRegionName, Double> awsSP50Check =
        (region, price) ->
            assertRegionPrice(SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP50, region, price);

    azureSP10Check.accept(AzureRegionName.AUSTRALIA_EAST, 0.262);
    azureSP10Check.accept(AzureRegionName.US_EAST_2, 0.190);

    azureSP30Check.accept(AzureRegionName.ASIA_SOUTH_EAST, 0.488);
    azureSP30Check.accept(AzureRegionName.US_WEST, 0.515);

    azureSP50Check.accept(AzureRegionName.EUROPE_NORTH, 1.927);
    azureSP50Check.accept(AzureRegionName.ASIA_SOUTH_EAST, 1.950);

    awsSP10Check.accept(AWSRegionName.US_EAST_1, 0.190);
    awsSP10Check.accept(AWSRegionName.US_EAST_2, 0.190);
    awsSP10Check.accept(AWSRegionName.AP_NORTHEAST_1, 0.245);
    awsSP10Check.accept(AWSRegionName.CA_CENTRAL_1, 0.206);

    awsSP30Check.accept(AWSRegionName.US_EAST_1, 0.390);
    awsSP30Check.accept(AWSRegionName.US_EAST_2, 0.390);
    awsSP30Check.accept(AWSRegionName.AP_NORTHEAST_1, 0.503);
    awsSP30Check.accept(AWSRegionName.CA_CENTRAL_1, 0.422);

    awsSP50Check.accept(AWSRegionName.US_EAST_1, 1.560);
    awsSP50Check.accept(AWSRegionName.US_EAST_2, 1.560);
    awsSP50Check.accept(AWSRegionName.AP_NORTHEAST_1, 2.011);
    awsSP50Check.accept(AWSRegionName.CA_CENTRAL_1, 1.689);
  }

  @Test
  public void testStreamVPCPeeringPricing() {
    BiConsumer<AWSRegionName, Double> awsCheck =
        (region, price) ->
            assertRegionPrice(SKU.NDS_AWS_STREAM_PROCESSING_VPC_PEERING, region, price);
    awsCheck.accept(AWSRegionName.US_EAST_1, 0.050);
    awsCheck.accept(AWSRegionName.US_EAST_2, 0.050);
    awsCheck.accept(AWSRegionName.AP_NORTHEAST_1, 0.072);
    awsCheck.accept(AWSRegionName.CA_CENTRAL_1, 0.055);
  }

  @Test
  public void testStreamProcessingPrivateLinkPricing() {
    BiConsumer<AWSRegionName, Double> awsCheck =
        (region, price) ->
            assertRegionPrice(SKU.NDS_AWS_STREAM_PROCESSING_PRIVATELINK, region, price);

    BiConsumer<AzureRegionName, Double> azureCheck =
        (region, price) ->
            assertRegionPrice(SKU.NDS_AZURE_STREAM_PROCESSING_PRIVATELINK, region, price);
    awsCheck.accept(AWSRegionName.AP_NORTHEAST_1, 0.101);
    awsCheck.accept(AWSRegionName.AP_SOUTH_1, 0.074);
    awsCheck.accept(AWSRegionName.AP_SOUTHEAST_1, 0.087);
    awsCheck.accept(AWSRegionName.AP_SOUTHEAST_2, 0.098);
    awsCheck.accept(AWSRegionName.CA_CENTRAL_1, 0.077);
    awsCheck.accept(AWSRegionName.EU_CENTRAL_1, 0.093);
    awsCheck.accept(AWSRegionName.EU_WEST_1, 0.088);
    awsCheck.accept(AWSRegionName.EU_WEST_2, 0.090);
    awsCheck.accept(AWSRegionName.SA_EAST_1, 0.112);
    awsCheck.accept(AWSRegionName.US_EAST_1, 0.070);
    awsCheck.accept(AWSRegionName.US_EAST_2, 0.070);
    awsCheck.accept(AWSRegionName.US_WEST_2, 0.070);

    azureCheck.accept(AzureRegionName.AUSTRALIA_EAST, 0.098);
    azureCheck.accept(AzureRegionName.US_EAST, 0.070);
    azureCheck.accept(AzureRegionName.US_EAST_2, 0.070);
    azureCheck.accept(AzureRegionName.EUROPE_NORTH, 0.090);
    azureCheck.accept(AzureRegionName.ASIA_SOUTH_EAST, 0.087);
    azureCheck.accept(AzureRegionName.US_WEST, 0.087);
    azureCheck.accept(AzureRegionName.EUROPE_WEST, 0.096);
  }

  private static List<Triple<SKU, AWSRegionName, Double>> skuRegionPriceTriplet() {
    return List.of(
        Triple.of(SKU.NDS_AWS_SEARCH_INSTANCE_S40_STORAGE_NVME, AP_SOUTHEAST_1, 0.3),
        Triple.of(SKU.NDS_AWS_SEARCH_INSTANCE_S40_STORAGE_NVME, CA_CENTRAL_1, 0.27),
        Triple.of(SKU.NDS_AWS_SEARCH_INSTANCE_S40_STORAGE_NVME, EU_WEST_1, 0.27),
        Triple.of(SKU.NDS_AWS_SEARCH_INSTANCE_S50_STORAGE_NVME, US_EAST_1, 0.51),
        Triple.of(SKU.NDS_AWS_SEARCH_INSTANCE_S60_STORAGE_NVME, US_EAST_1, 0.93),
        Triple.of(SKU.NDS_AWS_SEARCH_INSTANCE_S80_STORAGE_NVME, US_EAST_2, 1.86),
        Triple.of(SKU.NDS_AWS_SEARCH_INSTANCE_S90_STORAGE_NVME, US_WEST_2, 3.71));
  }

  /**
   * @see <a
   *     href=https://docs.google.com/spreadsheets/d/1EKz3Cf47nDRPQVLUWwOnZSHKHvYKyOmrPGdMm2xdKEg/edit?gid=0#gid=0>Proposed
   *     Storage Optimized Search Nodes</a>
   */
  @ParameterizedTest
  @MethodSource("skuRegionPriceTriplet")
  public void testStorageNVME_awsRegionPrice(Triple<SKU, AWSRegionName, Double> parameters) {
    assertRegionPrice(parameters.getLeft(), parameters.getMiddle(), parameters.getRight());
  }

  private static List<Pair<SKU, String>> flexLegacySkus() {
    return List.of(
        Pair.of(
            FLEX_AWS_LEGACY_100_USAGE_HOURS,
            "Atlas Flex Instance (AWS) 0-100 ops/sec (Lower Base Fee)"),
        Pair.of(
            FLEX_AZURE_LEGACY_100_USAGE_HOURS,
            "Atlas Flex Instance (Azure) 0-100 ops/sec (Lower Base Fee)"),
        Pair.of(
            FLEX_AZURE_LEGACY_300_USAGE_HOURS,
            "Atlas Flex Instance (Azure) 200-300 ops/sec (Lower Base Fee)"),
        Pair.of(
            FLEX_GCP_LEGACY_500_USAGE_HOURS,
            "Atlas Flex Instance (GCP) 400-500 ops/sec (Lower Base Fee)"));
  }

  /**
   * @see <a
   *     href=https://docs.google.com/document/d/1NlPzyFiTE6MOiYevNQmyaviNDpY7tN6x9SUHdpHjqk0/edit?tab=t.0#heading=h.my8l8kwcujpo>Flex
   *     Base SKUs for Low-Usage Serverless Customers</a>
   */
  @ParameterizedTest
  @MethodSource("flexLegacySkus")
  public void testFlexLegacySkus(Pair<SKU, String> parameters) {
    assertEquals(parameters.getLeft().getInfo().getDescription(), parameters.getRight());
  }

  private void assertAWSStoragePrice(SKU pSKU, AWSRegionName pRegionName, double pPricePerGbMonth) {
    double pricePerGbHour = pPricePerGbMonth * 12 / 365 / 24;
    assertRegionPrice(pSKU, pRegionName, pricePerGbHour);
  }

  private void assertAWSBackupSnapshotStoragePrice(AWSRegionName pRegionName, double pPrice) {
    assertRegionPrice(SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE, pRegionName, pPrice);
  }

  private void assertAWSTierPricing(AWSRegionName pAWSRegionName, double... pUnitPriceDollars) {
    SKU sku = SKU.NDS_AWS_PIT_RESTORE_STORAGE;
    SkuPricing pricing = sku.getInfo().getPricingForDate(new Date());
    for (int i = 1; i <= pUnitPriceDollars.length; i++) {
      assertEquals(
          pricing.getTieredRegionUnitPriceDollars(pAWSRegionName, i),
          pUnitPriceDollars[i - 1],
          EPSILON);
    }
  }

  private void assertGCPStoragePrice(GCPRegionName pRegionName, double pPricePerGbMonth) {
    double pricePerGbHour = pPricePerGbMonth * 12 / 365 / 24;
    assertRegionPrice(SKU.NDS_GCP_STORAGE_SSD, pRegionName, pricePerGbHour);
  }

  private void assertAzureStoragePrice(
      SKU pSKU, AzureRegionName pRegionName, double pPricePerMonth) {
    double pricePerGbHour =
        pPricePerMonth * 12 / 365 / 24 / pSKU.getInfo().getAzureDiskType().getSizeGB();
    assertRegionPrice(pSKU, pRegionName, pricePerGbHour);
  }

  private void assertAzureBackupSnapshotStoragePrice(AzureRegionName pRegionName, double pPrice) {
    assertRegionPrice(SKU.NDS_AZURE_BACKUP_SNAPSHOT_STORAGE, pRegionName, pPrice);
  }

  private void assertRegionPrice(SKU pSKU, RegionName pRegionName, double pPrice) {
    assertRegionPrice(pSKU, pRegionName, pPrice, EPSILON);
  }

  private void assertRegionPrice(SKU pSKU, RegionName pRegionName, double pPrice, double epsilon) {
    assertEquals(
        pSKU.getInfo().getRegionUnitPriceDollars(pRegionName, new Date()),
        pPrice,
        epsilon,
        pSKU + " SKU has wrong price for " + pRegionName + " region");
  }

  @Test
  public void testSKUAtlasExtras() {
    Set<SKU> ussSKUs = Stream.of(SKU.values()).filter(this::isFlexSku).collect(Collectors.toSet());

    for (SKU sku : SKU.values()) {
      if (sku.equals(SKU.NDS_AZURE_BACKUP_DOWNLOAD_VM)
          || sku.equals(SKU.NDS_AWS_BACKUP_DOWNLOAD_VM)
          || sku.equals(SKU.NDS_GCP_BACKUP_DOWNLOAD_VM)
          || sku.equals(SKU.NDS_AZURE_SNAPSHOT_EXPORT_VM)
          || sku.equals(SKU.NDS_AZURE_SNAPSHOT_EXPORT_VM_M40)
          || sku.equals(SKU.NDS_AZURE_SNAPSHOT_EXPORT_VM_M50)
          || sku.equals(SKU.NDS_AZURE_SNAPSHOT_EXPORT_VM_M60)
          || sku.equals(SKU.NDS_AWS_SNAPSHOT_EXPORT_VM)
          || sku.equals(SKU.NDS_AWS_SNAPSHOT_EXPORT_VM_M40)
          || sku.equals(SKU.NDS_AWS_SNAPSHOT_EXPORT_VM_M50)
          || sku.equals(SKU.NDS_AWS_SNAPSHOT_EXPORT_VM_M60)
          || sku.equals(SKU.NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE)
          || sku.equals(SKU.NDS_AWS_SNAPSHOT_EXPORT_VM_STORAGE_IOPS)
          || sku.equals(SKU.NDS_GCP_SNAPSHOT_EXPORT_VM)
          || sku.equals(SKU.NDS_GCP_SNAPSHOT_EXPORT_VM_M40)
          || sku.equals(SKU.NDS_GCP_SNAPSHOT_EXPORT_VM_M50)
          || sku.equals(SKU.NDS_GCP_SNAPSHOT_EXPORT_VM_M60)
          || sku.equals(SKU.METERING_E2E_BILLING)) {
        continue;
      }

      SKUInfo info = sku.getInfo();
      SkuPricing pricing = info.getPricingForDate(new Date());
      if (info.getSummaryGroup() == SummaryGroup.NDS_INSTANCES
          && !info.isPaused()
          && !info.hasFreeInstanceSize()
          && !ussSKUs.contains(sku)) {
        assertNotNull(
            pricing.getBiConnectorHigh(), sku.name() + " is missing BI Connector high factor");
        assertNotNull(
            pricing.getBiConnectorLow(), sku.name() + " is missing BI Connector low factor");
        assertTrue(
            pricing.getBiConnectorLow() < pricing.getBiConnectorHigh(),
            "Low factor is not less than high factor");
      } else {
        assertNull(
            pricing.getBiConnectorHigh(), sku.name() + " should not have BI Connector high factor");
        assertNull(
            pricing.getBiConnectorLow(), sku.name() + " should not have BI Connector low factor");
      }
    }
  }

  private boolean isFlexSku(SKU sku) {
    return sku.getInfo().getFlexInstanceSize() != null;
  }

  @Test
  public void testSKUBiller() {
    for (SKU sku : SKU.values()) {
      SKUInfo info = sku.getInfo();
      if (info.getLegacy()
          || info.getFreeTier()
          || info.getCredit()
          // We don't bill professional service product in Cloud billing
          || info.getProductCode() == ProductCode.CONSULTING
          || info.getMinimumCharge()
          || info.isNotBilled()
          || info.getDescription().equals("30 Days Onboarding Support")) {
        continue;
      }
      assertTrue(SKUSvc.hasBillerOrIsMetering(sku), sku.name() + " is missing biller class");
    }
  }

  @Test
  public void testFindByInstanceSize() {
    assertEquals(SKU.NDS_AWS_INSTANCE_M30, SKU.findByInstanceSize(AWSNDSInstanceSize.M30));
    assertEquals(SKU.NDS_GCP_INSTANCE_M30, SKU.findByInstanceSize(GCPNDSInstanceSize.M30));
    assertEquals(SKU.NDS_AZURE_INSTANCE_M30, SKU.findByInstanceSize(AzureNDSInstanceSize.M30));
    assertEquals(SKU.NDS_FREE_INSTANCE_M0, SKU.findByInstanceSize(FreeInstanceSize.M0));

    // Since flex SKUs are the same and do not have a field to filter by cloud provider,
    // we will always return the first flex SKU on the list which is the AWS 100 ops/sec
    // Note: this only works for now because Flex pricing is the same across cloud providers
    assertEquals(SKU.FLEX_AWS_100_USAGE_HOURS, SKU.findByInstanceSize(FlexInstanceSize.FLEX));
  }

  @Test
  public void ensureSizesHavePremiumUpchargeSKUs() {
    List<NDSInstanceSize> dedicatedSizes = new ArrayList<>();
    dedicatedSizes.addAll(Arrays.asList(AWSNDSInstanceSize.values()));
    dedicatedSizes.addAll(Arrays.asList(AzureNDSInstanceSize.values()));
    dedicatedSizes.addAll(Arrays.asList(GCPNDSInstanceSize.values()));
    for (NDSInstanceSize size : dedicatedSizes) {
      SKU sizeSku = SKU.findByInstanceSize(size);
      assertNotNull(sizeSku);

      SkuPricing pricing = sizeSku.getInfo().getPricingForDate(new Date());
      assertEquals(
          SKUInfo.ADVANCED_SECURITY_UPCHARGE,
          pricing.getAdvancedSecurityUpcharge(),
          0,
          String.format(
              "%s %s should have an advanced security upcharge",
              size.getCloudProvider(), size.name()));
      assertEquals(
          SKUInfo.ENTERPRISE_AUDITING_UPCHARGE,
          pricing.getEnterpriseAuditingUpcharge(),
          0,
          String.format(
              "%s %s should have an enterprise auditing upcharge",
              size.getCloudProvider(), size.name()));
    }

    List<NDSInstanceSize> tenantSizesWithSizeSku = new ArrayList<>();
    tenantSizesWithSizeSku.addAll(Arrays.asList(FreeInstanceSize.values()));
    tenantSizesWithSizeSku.addAll(Arrays.asList(FlexInstanceSize.values()));
    for (NDSInstanceSize size : tenantSizesWithSizeSku) {
      SKU sizeSku = SKU.findByInstanceSize(size);
      assertNotNull(sizeSku);

      SkuPricing pricing = sizeSku.getInfo().getPricingForDate(new Date());
      assertNull(
          pricing.getAdvancedSecurityUpcharge(),
          String.format(
              "%s %s should not have an advanced security upcharge",
              size.getCloudProvider(), size.name()));
      assertNull(
          pricing.getEnterpriseAuditingUpcharge(),
          String.format(
              "%s %s should not have an enterprise auditing upcharge",
              size.getCloudProvider(), size.name()));
    }

    for (NDSInstanceSize size : ServerlessInstanceSize.values()) {
      SKU sizeSku = SKU.findByInstanceSize(size);
      assertNull(
          sizeSku,
          String.format(
              "%s %s should not have an instance size sku", size.getCloudProvider(), size.name()));
    }
  }

  @Test
  public void testAzureDiskTypesHaveCorrespondingSKUs() {
    for (AzureDiskType dt : AzureDiskType.values()) {
      // This is not a customer facing option
      if (Set.of(AzureDiskType.P60, AzureDiskType.P70).contains(dt)) {
        continue;
      }
      if (dt.equals(AzureDiskType.V2)) {
        continue;
      }

      try {
        SKU.valueOf("NDS_AZURE_STORAGE_" + dt.name());
      } catch (IllegalArgumentException e) {
        fail("NDS_AZURE_STORAGE_" + dt.name() + " does not exist");
      }
      try {
        SKU.valueOf("NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_" + dt.name());
      } catch (IllegalArgumentException e) {
        fail("NDS_AZURE_BACKUP_DOWNLOAD_VM_STORAGE_" + dt.name() + " does not exist");
      }
    }
  }

  @Test
  public void testInstanceSkuSets() {
    // makes sure that there's no duplicate instance entries with same instance size and paused
    // status
    assertFalse(
        SKU.ALL_NDS_AWS_INSTANCE_SKUS.stream()
            // Exclude special SKUs used for testing price changes in non-prod environments.
            .filter(sku -> !sku.name().contains(TEST_PRICE_CHANGE_SUFFIX))
            .collect(
                Collectors.groupingBy(
                    sku -> Pair.of(sku.getInfo().getAWSInstanceSize(), sku.getInfo().isPaused())))
            .values()
            .stream()
            .anyMatch(group -> group.size() > 1));

    assertFalse(
        SKU.ALL_NDS_AZURE_INSTANCE_SKUS.stream()
            .filter(sku -> !sku.name().contains(TEST_PRICE_CHANGE_SUFFIX))
            .collect(
                Collectors.groupingBy(
                    sku -> Pair.of(sku.getInfo().getAzureInstanceSize(), sku.getInfo().isPaused())))
            .values()
            .stream()
            .anyMatch(group -> group.size() > 1));

    assertFalse(
        SKU.ALL_NDS_GCP_INSTANCE_SKUS.stream()
            .filter(sku -> !sku.name().contains(TEST_PRICE_CHANGE_SUFFIX))
            .collect(
                Collectors.groupingBy(
                    sku -> Pair.of(sku.getInfo().getGCPInstanceSize(), sku.getInfo().isPaused())))
            .values()
            .stream()
            .anyMatch(group -> group.size() > 1));
  }

  @Test
  public void testGCPSnapshotStoragePrice() {
    for (GCPRegionName regionName : GCPRegionName.values()) {
      assertTrue(
          SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE
                  .getInfo()
                  .getRegionUnitPriceDollars(regionName, new Date())
              >= 0);
    }
  }

  @Test
  public void testImportedSKUsAreForNDSPlan() {
    // Some code, including AccountantSvc.determineFirstBillDate, depends on the fact that all
    // imported SKUs are for Atlas plans.
    MatcherAssert.assertThat(
        PlanTypeSvc.BILLABLE_NDS_SKUS.containsAll(
            SKU.BILLABLE_SKUS_USING_HAS_EXTERNAL_DATA_DEPENDENCY),
        is(true));

    assertThat(
        PlanTypeSvc.BILLABLE_NDS_SKUS_INCLUDE_NON_PROD.containsAll(
            SKU.BILLABLE_SKUS_USING_HAS_EXTERNAL_DATA_DEPENDENCY_PROD_ONLY),
        is(true));
  }

  @Test
  public void testStitchSkusAreNotBilled() {
    Arrays.stream(SKU.values())
        .filter(
            sku ->
                sku.getInfo() != null
                    && sku.getInfo().getProductCode() != null
                    && sku.getInfo().getProductCode().equals(ProductCode.STITCH)
                    && !sku.getInfo().getFreeTier())
        .forEach(sku -> assertFalse(PlanTypeSvc.BILLABLE_NDS_SKUS.contains(sku)));

    Arrays.stream(SKU.values())
        .filter(
            sku ->
                sku.getInfo() != null
                    && sku.getInfo().getProductCode() != null
                    && sku.getInfo().getProductCode().equals(ProductCode.STITCH)
                    && sku.getInfo().getFreeTier())
        .forEach(sku -> assertFalse(PlanTypeSvc.BILLABLE_NDS_SKUS_FOR_FREE_CLUSTERS.contains(sku)));
  }

  @Test
  public void testAllFreeSKUsAreIncludedInTheSKUsForFreeClusters() {
    for (SKU sku : PlanTypeSvc.BILLABLE_NDS_SKUS) {
      if (SalesSoldDealActivationSvc.UNCHARGED_SKUS.contains(sku) || sku == SKU.NDS_ENTITLEMENTS) {
        continue;
      }
      if (sku.getInfo().isEffectivelyFree()) {
        assertTrue(PlanTypeSvc.BILLABLE_NDS_SKUS_FOR_FREE_CLUSTERS.contains(sku), sku.name());
      }
    }
  }

  @Test
  public void testAllSkusHaveMeterIdSkuUnit() {
    // Test to ensure that each SKU has and it's corresponding MeterId
    // has unit compatible with the unit on SKU
    Arrays.stream(SKU.values())
        .filter(sku -> sku.getInfo().getMeterId() != null)
        .forEach(
            sku ->
                assertEquals(
                    sku.getInfo().getUnits(),
                    sku.getInfo().getMeterId().getSkuUnits(),
                    "SKU and MeterId should have the same SKU unit but was different for "
                        + sku.name()));
  }

  @Test
  public void testAllSkusHaveSummaryGroup() {
    // these SKUs do not have summary groups for historical reasons so we dont check them
    Set<SKU> ignoredSkus =
        Set.of(
            SKU.CLASSIC_BACKUP_OPLOG,
            SKU.CLASSIC_BACKUP_STORAGE,
            SKU.CLASSIC_BACKUP_SNAPSHOT_CREATE,
            SKU.CLASSIC_BACKUP_DAILY_MINIMUM,
            SKU.CLASSIC_BACKUP_FREE_TIER,
            SKU.NDS_FREE_SUPPORT,
            SKU.NDS_ENTITLEMENTS,
            SKU.MMS_BACKUP_STORAGE_FREE_TIER,
            SKU.MMS_BACKUP_STORAGE,
            SKU.CLASSIC_COUPON,
            SKU.CREDIT,
            SKU.MINIMUM_CHARGE,
            SKU.NDS_BACKFILL_SUPPORT);
    for (SKU sku : SKU.values()) {
      if (ignoredSkus.contains(sku)) {
        continue;
      }
      assertTrue(sku.getInfo().hasSummaryGroup(), "No summary group for SKU:" + sku);
      assertNotNull(sku.getInfo().getSummaryGroup());
    }
  }

  @Test
  public void testSkuToMeterIdMap() {
    assertEquals(
        Set.of(SKU.DATA_LAKE_AWS_DATA_RETURNED_INTERNET),
        SKU.METER_ID_TO_SKUS_MAP.get(MeterId.DATA_LAKE_AWS_DATA_RETURNED_INTERNET));

    assertEquals(
        Set.of(SKU.DATA_FEDERATION_AZURE_DATA_RETURNED_INTERNET),
        SKU.METER_ID_TO_SKUS_MAP.get(MeterId.DATA_FEDERATION_AZURE_DATA_RETURNED_INTERNET));

    assertEquals(
        Set.of(SKU.DATA_FEDERATION_GCP_DATA_RETURNED_INTERNET),
        SKU.METER_ID_TO_SKUS_MAP.get(MeterId.DATA_FEDERATION_GCP_DATA_RETURNED_INTERNET));

    assertEquals(
        Set.of(SKU.DATA_FEDERATION_AZURE_DATA_RETURNED_SAME_CONTINENT),
        SKU.METER_ID_TO_SKUS_MAP.get(MeterId.DATA_FEDERATION_AZURE_DATA_RETURNED_SAME_CONTINENT));

    assertEquals(
        Set.of(SKU.DATA_FEDERATION_GCP_DATA_RETURNED_SAME_REGION),
        SKU.METER_ID_TO_SKUS_MAP.get(MeterId.DATA_FEDERATION_GCP_DATA_RETURNED_SAME_REGION));

    assertEquals(
        Set.of(SKU.DATA_FEDERATION_AZURE_DATA_RETURNED_DIFFERENT_CONTINENT),
        SKU.METER_ID_TO_SKUS_MAP.get(
            MeterId.DATA_FEDERATION_AZURE_DATA_RETURNED_DIFFERENT_CONTINENT));

    assertEquals(
        Set.of(SKU.DATA_FEDERATION_GCP_DATA_RETURNED_DIFFERENT_REGION),
        SKU.METER_ID_TO_SKUS_MAP.get(MeterId.DATA_FEDERATION_GCP_DATA_RETURNED_DIFFERENT_REGION));

    assertEquals(
        Set.of(SKU.NDS_AWS_DATA_TRANSFER_INTERNET),
        SKU.METER_ID_TO_SKUS_MAP.get(MeterId.NDS_AWS_DATA_TRANSFER_INTERNET));

    assertEquals(
        MeterId.DATA_LAKE_AWS_DATA_RETURNED_INTERNET,
        SKU.SKU_TO_METER_ID_MAP.get(SKU.DATA_LAKE_AWS_DATA_RETURNED_INTERNET));

    assertEquals(
        MeterId.NDS_AWS_DATA_TRANSFER_INTERNET,
        SKU.SKU_TO_METER_ID_MAP.get(SKU.NDS_AWS_DATA_TRANSFER_INTERNET));

    assertEquals(MeterId.BAAS_APP_COMPUTE, SKU.SKU_TO_METER_ID_MAP.get(SKU.REALM_APP_COMPUTE));

    assertEquals(MeterId.BAAS_APP_SYNC, SKU.SKU_TO_METER_ID_MAP.get(SKU.REALM_APP_SYNC));

    assertEquals(
        MeterId.BAAS_APP_DATA_TRANSFER, SKU.SKU_TO_METER_ID_MAP.get(SKU.REALM_APP_DATA_TRANSFER));

    assertEquals(MeterId.BAAS_APP_REQUESTS, SKU.SKU_TO_METER_ID_MAP.get(SKU.REALM_APP_REQUESTS));

    assertEquals(
        MeterId.CHARTS_DATA_DOWNLOADED, SKU.SKU_TO_METER_ID_MAP.get(SKU.CHARTS_DATA_DOWNLOADED));
  }

  @Test
  public void testAllSkusHaveSkuService() {
    for (SKU sku : SKU.values()) {
      assertNotNull(sku.getInfo().getSkuService());
    }
  }

  @Test
  public void testInstanceMigration() {
    // TODO: remove this test post-Atlas instance migration
    // This test was written specifically for Atlas Instance&Storage migration and
    // these skus were not a part of it
    List<SKU> skipMigrating =
        List.of(
            SKU.NDS_AZURE_STANDARD_STORAGE,
            SKU.NDS_AZURE_EXTENDED_STANDARD_IOPS,
            SKU.NDS_AWS_STORAGE_STANDARD_GP3);

    for (SKU sku : Arrays.stream(SKU.values()).filter(s -> !skipMigrating.contains(s)).toList()) {
      if (sku.getInfo().getSkuService() != SkuService.CLUSTERS) {
        continue;
      }

      if (sku.name().contains("TEST_PRICE_CHANGE")) {
        continue;
      }
      assertNotNull(
          sku.getInfo().getMeterId(),
          "MeterId was not set correctly for Atlas instance migration for sku " + sku.name());
      assertEquals(
          sku.name(),
          sku.getInfo().getMeterId().name(),
          "MeterId was not set correctly for Atlas instance migration for sku " + sku.name());
    }
  }

  @Test
  public void testPricing() throws IOException {
    double eps = 1e-6;
    Date date = Date.from(Instant.parse("2025-08-13T00:00:00.000Z"));

    JSONArray pricingData;
    try (InputStream stream = ClasspathUtils.openResourceUrl("json/pricing.json")) {
      try (InputStreamReader reader = new InputStreamReader(stream)) {
        JSONTokener tokener = new JSONTokener(reader);
        pricingData = new JSONArray(tokener);
      }
    }

    for (int i = 0; i < pricingData.length(); i++) {
      JSONObject pricing = pricingData.getJSONObject(i);
      SKU sku = SKU.valueOf(pricing.getString("sku"));
      SKUInfo skuInfo = sku.getInfo();
      SkuPricing skuPricing = skuInfo.getPricingForDate(date);

      CloudProvider provider = CloudProvider.valueOf(pricing.getString("provider"));
      RegionName regionName =
          switch (provider) {
            case AWS -> AWSRegionName.valueOf(pricing.getString("region"));
            case GCP -> GCPRegionName.valueOf(pricing.getString("region"));
            case AZURE -> AzureRegionName.valueOf(pricing.getString("region"));
            default ->
                throw new IllegalArgumentException("Unsupported cloud provider: " + provider);
          };

      if (pricing.has("unitPrice")) {
        double expectedPrice = pricing.getDouble("unitPrice");
        if (sku.getInfo().getSkuService() == SkuService.STREAMS
            && regionName == AzureRegionName.US_EAST) {
          continue;
        }
        assertEquals(expectedPrice, skuPricing.getRegionUnitPriceDollars(regionName), eps);
      } else if (pricing.has("tieredPrices")) {
        int numTiers = skuPricing.getPricingTiers();
        JSONArray tieredPrices = pricing.getJSONArray("tieredPrices");

        for (int tier = 1; tier <= numTiers; tier++) {
          double expectedPrice = tieredPrices.getDouble(tier - 1);
          assertEquals(
              expectedPrice, skuPricing.getTieredRegionUnitPriceDollars(regionName, tier), eps);
        }
      }
    }
  }

  @Test
  public void testSKUEnumOrder() {
    SKU[] skus = SKU.values();

    // ensure that people cannot add a SKU to the middle of the list of enums
    for (int i = 0; i < skus.length; i++) {
      assertEquals(i, skus[i].ordinal());
    }
  }

  @Test
  public void testGetSKUByObjectId_ValidId() {
    for (SKU sku : SKU.values()) {
      ObjectId objectId = new ObjectId(sku.ordinal(), 0);
      SKU retrievedSku = SKU.getSKUByObjectId(objectId);

      assertEquals(
          sku,
          retrievedSku,
          "The retrieved SKU does not match the expected SKU for ObjectId: " + objectId);
    }
  }

  @Test
  public void testGetSKUByObjectId_InvalidId() {
    // Create an ObjectId with an ordinal that is out of range
    ObjectId invalidObjectId = new ObjectId(SKU.values().length, 0);

    assertThrows(ArrayIndexOutOfBoundsException.class, () -> SKU.getSKUByObjectId(invalidObjectId));
  }

  @Test
  public void testSKUEnumOrderIntegrity() {
    SKU[] skus = SKU.values();

    // Ensure that specific SKUs remain at their expected positions to maintain enum order
    // integrity.
    // This test will fail if any SKUs are reordered or if new SKUs are inserted before these
    // positions.
    assertEquals("CLASSIC_BACKUP_OPLOG", skus[0].name());
    assertEquals("NDS_AZURE_STREAM_PROCESSING_PRIVATELINK", skus[405].name());
  }

  @Test
  public void testAzureDataTransferInterRegionInterContinentPricingIsZeroForPeriod() {
    Date dateInsidePeriod = Date.from(Instant.parse("2025-08-18T00:00:00.000Z"));

    Date dateOutsidePeriod = Date.from(Instant.parse("2025-12-18T00:00:00.000Z"));
    assertEquals(
        0.05,
        NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTER_CONTINENT
            .getInfo()
            .getRegionUnitPriceDollars(AzureRegionName.US_CENTRAL, dateOutsidePeriod),
        0.0);
    assertEquals(
        0.02,
        NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTRA_CONTINENT
            .getInfo()
            .getRegionUnitPriceDollars(AzureRegionName.US_CENTRAL, dateOutsidePeriod),
        0.0);

    assertEquals(
        0.0,
        NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTER_CONTINENT
            .getInfo()
            .getRegionUnitPriceDollars(AzureRegionName.US_CENTRAL, dateInsidePeriod),
        0.0);
    assertEquals(
        0.0,
        NDS_AZURE_DATA_TRANSFER_INTER_REGION_INTRA_CONTINENT
            .getInfo()
            .getRegionUnitPriceDollars(AzureRegionName.US_CENTRAL, dateInsidePeriod),
        0.0);
  }

  @Test
  public void comparePricingStrategyWithRegionPriceDollars() {
    Map.of(
            CloudProvider.AWS,
            PricingStrategyType.AWS_REGION,
            CloudProvider.AZURE,
            PricingStrategyType.AZURE_REGION)
        .forEach(
            (cloudProvider, pricingStrategyType) -> {
              List<SKU> skus =
                  Stream.of(SKU.values())
                      .filter(
                          sku -> {
                            SKUInfo info = sku.getInfo();
                            return !info.getCurrentPricingSet()
                                    .getRegionUnitPriceDollars(cloudProvider)
                                    .isEmpty()
                                && info.getSkuService() == SkuService.STREAMS
                                && info.hasStreamsInstanceSize();
                          })
                      .toList();
              List<SKU> skusByPricingStrategy =
                  Stream.of(SKU.values())
                      .filter(
                          sku -> {
                            SKUInfo info = sku.getInfo();
                            return info.getPricingStrategyType() == pricingStrategyType
                                && info.getSkuService() == SkuService.STREAMS
                                && info.hasStreamsInstanceSize();
                          })
                      .toList();
              assertEquals(skusByPricingStrategy, skus);
            });
  }
}
