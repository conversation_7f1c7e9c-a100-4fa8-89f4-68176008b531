package com.xgen.cloud.cps.restore._public.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.cps.core._public.svc.CpsFeatureFlagSvc;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import java.util.List;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class CpsPreRestoreUpgradeSvcUnitTests {

  private CpsPreRestoreUpgradeSvc cpsPreRestoreUpgradeSvc;
  private CpsFeatureFlagSvc mockCpsFeatureFlagSvc;

  @BeforeEach
  public void setUp() {
    mockCpsFeatureFlagSvc = mock(CpsFeatureFlagSvc.class);
    cpsPreRestoreUpgradeSvc =
        new CpsPreRestoreUpgradeSvc(mockCpsFeatureFlagSvc, mock(BackupRestoreJobDao.class));

    // Default: feature flag enabled for all tests
    when(mockCpsFeatureFlagSvc.isCpsPreRestoreUpgradeEnabled(any(ObjectId.class))).thenReturn(true);
  }

  @ParameterizedTest(name = "{0}")
  @MethodSource("needsPreRestoreUpgradeTestCases")
  public void needsPreRestoreUpgrade_variousScenarios_returnsExpectedResult(
      String description,
      String version,
      String fcv,
      ClusterDescription.VersionReleaseSystem versionReleaseSystem,
      BackupSnapshot snapshot,
      boolean featureFlagEnabled,
      boolean expectedResult) {
    when(mockCpsFeatureFlagSvc.isCpsPreRestoreUpgradeEnabled(any(ObjectId.class)))
        .thenReturn(featureFlagEnabled);

    final ClusterDescription targetCluster = mock(ClusterDescription.class);
    when(targetCluster.getName()).thenReturn("test-cluster");
    when(targetCluster.getGroupId()).thenReturn(new ObjectId());
    when(targetCluster.isDedicatedCluster()).thenReturn(true);
    when(targetCluster.getVersionReleaseSystem()).thenReturn(versionReleaseSystem);

    final List<Process> processes = createMockProcesses(version, fcv);
    boolean actualResult =
        cpsPreRestoreUpgradeSvc.needsPreRestoreUpgrade(targetCluster, snapshot, processes);

    // Verify
    assertEquals(expectedResult, actualResult, description);
  }

  private static Stream<Arguments> needsPreRestoreUpgradeTestCases() {
    return Stream.of(
        Arguments.of(
            "Snapshot FCV null should return false",
            "8.0",
            "8.0",
            ClusterDescription.VersionReleaseSystem.CONTINUOUS,
            createMockBackupSnapshot(null, "8.0"),
            true,
            false),
        Arguments.of(
            "Target FCV null should return false",
            "8.0",
            null,
            ClusterDescription.VersionReleaseSystem.CONTINUOUS,
            createMockBackupSnapshot("8.0", "8.0"),
            true,
            false),
        Arguments.of(
            "Non-continuous version release system should return false",
            "8.0",
            "8.0",
            ClusterDescription.VersionReleaseSystem.LTS,
            createMockBackupSnapshot("8.0", "8.0"),
            true,
            false),
        Arguments.of(
            "Same binary version, target FCV 1 minor lower should return true",
            "8.1",
            "8.0",
            ClusterDescription.VersionReleaseSystem.CONTINUOUS,
            createMockBackupSnapshot("8.1", "8.1"),
            true,
            true),
        Arguments.of(
            "Same binary version, target FCV 2 minor lower should return false",
            "8.2",
            "8.0",
            ClusterDescription.VersionReleaseSystem.CONTINUOUS,
            createMockBackupSnapshot("8.2", "8.2"),
            true,
            false),
        Arguments.of(
            "Target FCV higher than snapshot should return false",
            "8.0",
            "8.2",
            ClusterDescription.VersionReleaseSystem.CONTINUOUS,
            createMockBackupSnapshot("8.0", "8.1"),
            true,
            false),
        Arguments.of(
            "Invalid FCV format should return false",
            "8.0",
            "invalid-fcv",
            ClusterDescription.VersionReleaseSystem.CONTINUOUS,
            createMockBackupSnapshot("8.0", "8.0"),
            true,
            false),
        Arguments.of(
            "Invalid snapshot FCV format should return false",
            "8.0",
            "8.0",
            ClusterDescription.VersionReleaseSystem.CONTINUOUS,
            createMockBackupSnapshot("invalid-fcv", "8.0"),
            true,
            false),
        Arguments.of(
            "Same FCV, target binary 1 minor lower should return false",
            "8.0",
            "8.1",
            ClusterDescription.VersionReleaseSystem.CONTINUOUS,
            createMockBackupSnapshot("8.1", "8.1"),
            true,
            false),
        Arguments.of(
            "Same FCV, target binary higher should return false",
            "8.2",
            "8.1",
            ClusterDescription.VersionReleaseSystem.CONTINUOUS,
            createMockBackupSnapshot("8.1", "8.1"),
            true,
            false),
        Arguments.of(
            "Same FCV, same binary version should return false",
            "8.1",
            "8.1",
            ClusterDescription.VersionReleaseSystem.CONTINUOUS,
            createMockBackupSnapshot("8.1", "8.1"),
            true,
            false),
        Arguments.of(
            "Same FCV, target binary 1 minor lower, FF disabled should return false",
            "8.0",
            "8.1",
            ClusterDescription.VersionReleaseSystem.CONTINUOUS,
            createMockBackupSnapshot("8.1", "8.1"),
            false,
            false));
  }

  /** Creates a mock BackupSnapshot with specified FCV and binary version. */
  private static BackupSnapshot createMockBackupSnapshot(String fcv, String binaryVersion) {
    final BackupSnapshot snapshot = mock(BackupSnapshot.class);
    when(snapshot.getId()).thenReturn(new ObjectId());
    when(snapshot.getFcv()).thenReturn(fcv);
    if (binaryVersion != null) {
      when(snapshot.getMongoDbVersion())
          .thenReturn(VersionUtils.Version.fromString(binaryVersion + ".0"));
    }
    return snapshot;
  }

  /** Creates a mock list of processes based on the cluster's binary version. */
  private static List<Process> createMockProcesses(String version, String fcv) {
    final Process process = mock(Process.class);
    when(process.getVersion()).thenReturn(version);
    when(process.getFeatureCompatibilityVersion()).thenReturn(fcv);
    return List.of(process);
  }
}
