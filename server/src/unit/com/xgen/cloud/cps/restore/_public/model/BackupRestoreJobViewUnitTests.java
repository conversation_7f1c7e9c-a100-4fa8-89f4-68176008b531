package com.xgen.cloud.cps.restore._public.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.cps.restore._public.model.DirectAttachReplicaSetBackupRestoreJob.AttachProgressStatus;
import com.xgen.cloud.cps.restore._public.model.DirectAttachReplicaSetBackupRestoreJob.AttachStatus;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;

public class BackupRestoreJobViewUnitTests {

  private static final ObjectMapper MAPPER = CustomJacksonJsonProvider.createObjectMapper();

  @Test
  public void testDirectAttachRestoreJobViewWithoutAttachStatuses() {
    // Create a DirectAttach restore job without attach statuses
    final DirectAttachReplicaSetBackupRestoreJob job =
        new DirectAttachReplicaSetBackupRestoreJob(
            NDSModelTestFactory.getDirectAttachRestoreJobBuilder(new ObjectId()));

    final BackupRestoreJobView view = new BackupRestoreJobView(job, true);

    // Verify that attachStatuses is null when there are no attach statuses
    assertNull(view.getAttachStatuses());
  }

  @Test
  public void testDirectAttachRestoreJobViewWithAttachStatuses() {
    final ObjectId instanceId1 = new ObjectId();
    final ObjectId instanceId2 = new ObjectId();
    final Date timeAttached = new Date();

    // Create attach statuses with some test data
    final AttachStatus attachStatus1 =
        new AttachStatus(
            new BasicDBObject()
                .append(AttachStatus.FieldDefs.INSTANCE_ID, instanceId1)
                .append(AttachStatus.FieldDefs.STATUS, AttachProgressStatus.IN_PROGRESS.name())
                .append(AttachStatus.FieldDefs.TIME_ATTACHED, timeAttached)
                .append(AttachStatus.FieldDefs.HOSTNAME, "host1")
                .append(AttachStatus.FieldDefs.DETACHED_DISK_ID, "disk-123")
                .append(AttachStatus.FieldDefs.MACHINE_SHUTDOWN, false)
                .append(AttachStatus.FieldDefs.DISK_SWAP_COMPLETED, false)
                .append(AttachStatus.FieldDefs.AGENT_REACHED_GOAL_STATE_FOR_RESTORE, true)
                .append(AttachStatus.FieldDefs.OPTIMIZED_DISK_TYPE_ATTACHED, "gp3")
                .append(AttachStatus.FieldDefs.ATTACH_STATS, new BasicDBObject()));

    final AttachStatus attachStatus2 =
        new AttachStatus(
            new BasicDBObject()
                .append(AttachStatus.FieldDefs.INSTANCE_ID, instanceId2)
                .append(AttachStatus.FieldDefs.STATUS, AttachProgressStatus.COMPLETED.name())
                .append(AttachStatus.FieldDefs.TIME_ATTACHED, timeAttached)
                .append(AttachStatus.FieldDefs.HOSTNAME, "host2")
                .append(AttachStatus.FieldDefs.DETACHED_DISK_ID, "disk-456")
                .append(AttachStatus.FieldDefs.MACHINE_SHUTDOWN, true)
                .append(AttachStatus.FieldDefs.DISK_SWAP_COMPLETED, true)
                .append(AttachStatus.FieldDefs.AGENT_REACHED_GOAL_STATE_FOR_RESTORE, false)
                .append(AttachStatus.FieldDefs.OPTIMIZED_DISK_TYPE_ATTACHED, "io2")
                .append(
                    AttachStatus.FieldDefs.ATTACH_STATS,
                    new BasicDBObject()
                        .append(
                            "mongoShutdownStats",
                            new BasicDBObject()
                                .append(
                                    "applyOpsToDesiredTime_shutdown",
                                    new BasicDBObject()
                                        .append("rollBackToStableDurationMilliseconds", 2000L)))
                        .append("createVolumeFromSnapshotDurationSeconds", 120L)
                        .append("mountVolumeWithSnapshotDataDurationMilliseconds", 8000L)
                        .append("diskType", "io2")));

    // Create a DirectAttach restore job with attach statuses
    final DirectAttachReplicaSetBackupRestoreJob job =
        new DirectAttachReplicaSetBackupRestoreJob(
            NDSModelTestFactory.getDirectAttachRestoreJobBuilder(new ObjectId())
                .withAttachStatuses(List.of(attachStatus1, attachStatus2))
                .withCloudProvider(CloudProvider.AWS));

    final BackupRestoreJobView view = new BackupRestoreJobView(job, true);

    // Verify that attachStatuses is populated
    assertNotNull(view.getAttachStatuses());
    assertEquals(2, view.getAttachStatuses().size());

    // Verify first attach status
    final BackupRestoreJobView.AttachStatusView attachStatusView1 = view.getAttachStatuses().get(0);
    assertEquals(instanceId1, attachStatusView1.getInstanceId());
    assertEquals("inProgress", attachStatusView1.getStatus());
    assertEquals(timeAttached, attachStatusView1.getTimeAttached());
    assertEquals("host1", attachStatusView1.getHostname());
    assertEquals("disk-123", attachStatusView1.getDetachedDiskId());
    assertFalse(attachStatusView1.getMachineShutdown());
    assertFalse(attachStatusView1.getDiskSwapCompleted());
    assertTrue(attachStatusView1.getAgentReachedGoalStateForRestore());
    assertEquals("gp3", attachStatusView1.getOptimizedDiskTypeAttached());
    assertNotNull(attachStatusView1.getAttachStats());

    // Verify second attach status
    final BackupRestoreJobView.AttachStatusView attachStatusView2 = view.getAttachStatuses().get(1);
    assertEquals(instanceId2, attachStatusView2.getInstanceId());
    assertEquals("completed", attachStatusView2.getStatus());
    assertEquals(timeAttached, attachStatusView2.getTimeAttached());
    assertEquals("host2", attachStatusView2.getHostname());
    assertEquals("disk-456", attachStatusView2.getDetachedDiskId());
    assertTrue(attachStatusView2.getMachineShutdown());
    assertTrue(attachStatusView2.getDiskSwapCompleted());
    assertFalse(attachStatusView2.getAgentReachedGoalStateForRestore());
    assertEquals("io2", attachStatusView2.getOptimizedDiskTypeAttached());
    assertNotNull(attachStatusView2.getAttachStats());
  }

  @Test
  public void testDirectAttachRestoreJobViewJsonSerialization() throws Exception {
    final ObjectId instanceId = new ObjectId();
    final Date timeAttached = new Date();

    // Create attach status with test data
    final AttachStatus attachStatus =
        new AttachStatus(
            new BasicDBObject()
                .append(AttachStatus.FieldDefs.INSTANCE_ID, instanceId)
                .append(AttachStatus.FieldDefs.STATUS, AttachProgressStatus.IN_PROGRESS.name())
                .append(AttachStatus.FieldDefs.TIME_ATTACHED, timeAttached)
                .append(AttachStatus.FieldDefs.HOSTNAME, "test-host")
                .append(AttachStatus.FieldDefs.DETACHED_DISK_ID, "disk-test")
                .append(AttachStatus.FieldDefs.MACHINE_SHUTDOWN, false)
                .append(AttachStatus.FieldDefs.DISK_SWAP_COMPLETED, false)
                .append(AttachStatus.FieldDefs.AGENT_REACHED_GOAL_STATE_FOR_RESTORE, true)
                .append(AttachStatus.FieldDefs.OPTIMIZED_DISK_TYPE_ATTACHED, "gp3")
                .append(
                    AttachStatus.FieldDefs.ATTACH_STATS,
                    new BasicDBObject()
                        .append(
                            "mongoStartupStats",
                            new BasicDBObject()
                                .append(
                                    "seedRestore_restartWithOplogReplayParams",
                                    new BasicDBObject().append("journalReplayDurationMs", 1500L)))
                        .append(
                            "mongoShutdownStats",
                            new BasicDBObject()
                                .append(
                                    "applyOpsToDesiredTime_shutdown",
                                    new BasicDBObject()
                                        .append("rollBackToStableDurationMilliseconds", 500L)))
                        .append("detachVolumeDurationMilliseconds", 4000L)
                        .append("attachVolumeDurationMilliseconds", 2500L)
                        .append("createVolumeFromSnapshotDurationSeconds", 90L)
                        .append("mountVolumeWithSnapshotDataDurationMilliseconds", 7000L)
                        .append("diskType", "gp3")
                        .append("bounceStopIfUpWithForceKillDurationSeconds", 30L)
                        .append("getDirectAttachFileListDurationSeconds", 15L)
                        .append("diskPreWarmDurationSeconds", 60L)
                        .append("diskPreWarmThroughputMbPerSecond", 125.5)));

    // Create a DirectAttach restore job with attach status
    final DirectAttachReplicaSetBackupRestoreJob job =
        new DirectAttachReplicaSetBackupRestoreJob(
            NDSModelTestFactory.getDirectAttachRestoreJobBuilder(new ObjectId())
                .withAttachStatuses(List.of(attachStatus))
                .withCloudProvider(CloudProvider.AWS));

    final BackupRestoreJobView view = new BackupRestoreJobView(job, true);

    // Serialize to JSON
    final String jsonString = MAPPER.writeValueAsString(view);
    final JsonNode jsonNode = MAPPER.readTree(jsonString);

    // Verify that attachStatuses field is present in JSON
    assertNotNull(jsonNode.get("attachStatuses"));
    assertEquals(1, jsonNode.get("attachStatuses").size());

    // Verify attach status fields in JSON
    final JsonNode attachStatusJson = jsonNode.get("attachStatuses").get(0);
    assertEquals(instanceId.toString(), attachStatusJson.get("instanceId").asText());
    assertEquals("inProgress", attachStatusJson.get("status").asText());
    assertEquals("test-host", attachStatusJson.get("hostname").asText());
    assertEquals("disk-test", attachStatusJson.get("detachedDiskId").asText());
    assertFalse(attachStatusJson.get("machineShutdown").asBoolean());
    assertTrue(attachStatusJson.get("agentReachedGoalStateForRestore").asBoolean());
    assertEquals("gp3", attachStatusJson.get("optimizedDiskTypeAttached").asText());

    // Verify attachStats is present and has the correct structure
    final JsonNode attachStatsJson = attachStatusJson.get("attachStats");
    assertNotNull(attachStatsJson);

    // Verify that AttachStats fields are properly serialized with correct field names
    assertNotNull(attachStatsJson.get("mongoStartupStats"));
    assertNotNull(attachStatsJson.get("mongoShutdownStats"));
    assertEquals(4000, attachStatsJson.get("detachVolumeDurationMilliseconds").asLong());
    assertEquals(2500, attachStatsJson.get("attachVolumeDurationMilliseconds").asLong());
    assertEquals(90, attachStatsJson.get("createVolumeFromSnapshotDurationSeconds").asLong());
    assertEquals(
        7000, attachStatsJson.get("mountVolumeWithSnapshotDataDurationMilliseconds").asLong());
    assertEquals("gp3", attachStatsJson.get("diskType").asText());
    assertEquals(30, attachStatsJson.get("bounceStopIfUpWithForceKillDurationSeconds").asLong());
    assertEquals(15, attachStatsJson.get("getDirectAttachFileListDurationSeconds").asLong());
    assertEquals(60, attachStatsJson.get("diskPreWarmDurationSeconds").asLong());
    assertEquals(125.5, attachStatsJson.get("diskPreWarmThroughputMbPerSecond").asDouble(), 0.01);

    // Verify nested mongoStartupStats structure
    final JsonNode startupStats = attachStatsJson.get("mongoStartupStats");
    assertNotNull(startupStats.get("seedRestore_restartWithOplogReplayParams"));
    assertEquals(
        1500,
        startupStats
            .get("seedRestore_restartWithOplogReplayParams")
            .get("journalReplayDurationMs")
            .asLong());

    // Verify nested mongoShutdownStats structure
    final JsonNode shutdownStats = attachStatsJson.get("mongoShutdownStats");
    assertNotNull(shutdownStats.get("applyOpsToDesiredTime_shutdown"));
    assertEquals(
        500,
        shutdownStats
            .get("applyOpsToDesiredTime_shutdown")
            .get("rollBackToStableDurationMilliseconds")
            .asLong());
  }
}
