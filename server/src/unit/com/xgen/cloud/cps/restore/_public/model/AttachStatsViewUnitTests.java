package com.xgen.cloud.cps.restore._public.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import java.util.Map;
import org.junit.jupiter.api.Test;

public class AttachStatsViewUnitTests {

  private static final ObjectMapper MAPPER = CustomJacksonJsonProvider.createObjectMapper();

  @Test
  public void testAttachStatsViewWithNullAttachStats() {
    final AttachStatsView view = AttachStatsView.fromAttachStats(null);
    assertNull(view);
  }

  @Test
  public void testAttachStatsViewWithValidAttachStats() {
    final Map<String, Map<String, Long>> startupStats =
        Map.of(
            "seedRestore_restartWithOplogReplayParams",
            Map.of(
                "journalReplayDurationMs", 1000L, "rollBackToStableDurationMilliseconds", 2000L));

    final Map<String, Map<String, Long>> shutdownStats =
        Map.of("shutdown", Map.of("shutdownDurationMs", 500L));

    final AttachStats attachStats =
        new AttachStats(
            startupStats,
            shutdownStats,
            4000L, // detachVolumeDurationMilliseconds
            120L, // createVolumeFromSnapshotDurationSeconds
            2500L, // attachVolumeDurationMilliseconds
            7000L, // mountVolumeWithSnapshotDataDurationMilliseconds
            "gp3", // diskType
            30L, // bounceStopIfUpWithForceKillDurationSeconds
            45L, // getDirectAttachFileListDurationSeconds
            600L, // diskPreWarmDurationSeconds
            15.5, // diskPreWarmThroughputMbPerSecond
            180L, // pitMetaReaderDurationSeconds
            240L, // pitPullerDurationSeconds
            90L, // pitOpProviderDurationSeconds
            360L // pitInsertOpsDurationSeconds
            );

    final AttachStatsView view = AttachStatsView.fromAttachStats(attachStats);

    assertEquals(startupStats, view.mongoStartupStats());
    assertEquals(shutdownStats, view.mongoShutdownStats());
    assertEquals(4000L, view.detachVolumeDurationMilliseconds());
    assertEquals(120L, view.createVolumeFromSnapshotDurationSeconds());
    assertEquals(2500L, view.attachVolumeDurationMilliseconds());
    assertEquals(7000L, view.mountVolumeWithSnapshotDataDurationMilliseconds());
    assertEquals("gp3", view.diskType());
    assertEquals(30L, view.bounceStopIfUpWithForceKillDurationSeconds());
    assertEquals(45L, view.getDirectAttachFileListDurationSeconds());
    assertEquals(600L, view.diskPreWarmDurationSeconds());
    assertEquals(15.5, view.diskPreWarmThroughputMbPerSecond());
    assertEquals(180L, view.pitMetaReaderDurationSeconds());
    assertEquals(240L, view.pitPullerDurationSeconds());
    assertEquals(90L, view.pitOpProviderDurationSeconds());
    assertEquals(360L, view.pitInsertOpsDurationSeconds());
  }

  @Test
  public void testAttachStatsViewJsonSerialization() throws Exception {
    final AttachStats attachStats = getAttachStats();

    final AttachStatsView view = AttachStatsView.fromAttachStats(attachStats);

    // Serialize to JSON
    final String jsonString = MAPPER.writeValueAsString(view);
    final JsonNode jsonNode = MAPPER.readTree(jsonString);

    // Verify that all non-null fields are properly serialized with correct field names
    assertNotNull(jsonNode.get("mongoStartupStats"));
    assertNull(
        jsonNode.get("mongoShutdownStats")); // Should be omitted due to @JsonInclude(NON_NULL)
    assertEquals(4000, jsonNode.get("detachVolumeDurationMilliseconds").asLong());
    assertEquals(120, jsonNode.get("createVolumeFromSnapshotDurationSeconds").asLong());
    assertEquals(2500, jsonNode.get("attachVolumeDurationMilliseconds").asLong());
    assertEquals(7000, jsonNode.get("mountVolumeWithSnapshotDataDurationMilliseconds").asLong());
    assertEquals("gp3", jsonNode.get("diskType").asText());
    assertEquals(30, jsonNode.get("bounceStopIfUpWithForceKillDurationSeconds").asLong());
    assertEquals(45, jsonNode.get("getDirectAttachFileListDurationSeconds").asLong());
    assertEquals(600, jsonNode.get("diskPreWarmDurationSeconds").asLong());
    assertEquals(15.5, jsonNode.get("diskPreWarmThroughputMbPerSecond").asDouble());
    assertEquals(180, jsonNode.get("pitMetaReaderDurationSeconds").asLong());
    assertEquals(240, jsonNode.get("pitPullerDurationSeconds").asLong());
    assertEquals(90, jsonNode.get("pitOpProviderDurationSeconds").asLong());
    assertEquals(360, jsonNode.get("pitInsertOpsDurationSeconds").asLong());

    // Verify nested structure
    final JsonNode startupStatsNode = jsonNode.get("mongoStartupStats");
    assertNotNull(startupStatsNode.get("seedRestore_restartWithOplogReplayParams"));
    assertEquals(
        1000,
        startupStatsNode
            .get("seedRestore_restartWithOplogReplayParams")
            .get("journalReplayDurationMs")
            .asLong());
    assertEquals(
        2000,
        startupStatsNode
            .get("seedRestore_restartWithOplogReplayParams")
            .get("rollBackToStableDurationMilliseconds")
            .asLong());
  }

  private static AttachStats getAttachStats() {
    final Map<String, Map<String, Long>> startupStats =
        Map.of(
            "seedRestore_restartWithOplogReplayParams",
            Map.of(
                "journalReplayDurationMs", 1000L, "rollBackToStableDurationMilliseconds", 2000L));

    final AttachStats attachStats =
        new AttachStats(
            startupStats,
            null, // mongoShutdownStats
            4000L, // detachVolumeDurationMilliseconds
            120L, // createVolumeFromSnapshotDurationSeconds
            2500L, // attachVolumeDurationMilliseconds
            7000L, // mountVolumeWithSnapshotDataDurationMilliseconds
            "gp3", // diskType
            30L, // bounceStopIfUpWithForceKillDurationSeconds
            45L, // getDirectAttachFileListDurationSeconds
            600L, // diskPreWarmDurationSeconds
            15.5, // diskPreWarmThroughputMbPerSecond
            180L, // pitMetaReaderDurationSeconds
            240L, // pitPullerDurationSeconds
            90L, // pitOpProviderDurationSeconds
            360L // pitInsertOpsDurationSeconds
            );
    return attachStats;
  }

  @Test
  public void testAttachStatsViewWithPartialData() {
    final AttachStats attachStats =
        new AttachStats(
            null, // mongoStartupStats
            null, // mongoShutdownStats
            4000L, // detachVolumeDurationMilliseconds
            null, // createVolumeFromSnapshotDurationSeconds
            null, // attachVolumeDurationMilliseconds
            null, // mountVolumeWithSnapshotDataDurationMilliseconds
            "gp3", // diskType
            null, // bounceStopIfUpWithForceKillDurationSeconds
            null, // getDirectAttachFileListDurationSeconds
            null, // diskPreWarmDurationSeconds
            null, // diskPreWarmThroughputMbPerSecond
            null, // pitMetaReaderDurationSeconds
            null, // pitPullerDurationSeconds
            null, // pitOpProviderDurationSeconds
            null // pitInsertOpsDurationSeconds
            );

    final AttachStatsView view = AttachStatsView.fromAttachStats(attachStats);

    assertNull(view.mongoStartupStats());
    assertNull(view.mongoShutdownStats());
    assertEquals(4000L, view.detachVolumeDurationMilliseconds());
    assertNull(view.createVolumeFromSnapshotDurationSeconds());
    assertNull(view.attachVolumeDurationMilliseconds());
    assertNull(view.mountVolumeWithSnapshotDataDurationMilliseconds());
    assertEquals("gp3", view.diskType());
    assertNull(view.bounceStopIfUpWithForceKillDurationSeconds());
    assertNull(view.getDirectAttachFileListDurationSeconds());
    assertNull(view.diskPreWarmDurationSeconds());
    assertNull(view.diskPreWarmThroughputMbPerSecond());
    assertNull(view.pitMetaReaderDurationSeconds());
    assertNull(view.pitPullerDurationSeconds());
    assertNull(view.pitOpProviderDurationSeconds());
    assertNull(view.pitInsertOpsDurationSeconds());
  }
}
