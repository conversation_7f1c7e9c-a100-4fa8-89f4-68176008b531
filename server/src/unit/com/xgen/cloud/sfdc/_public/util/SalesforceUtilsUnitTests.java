package com.xgen.cloud.sfdc._public.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.appsettings._public.model.AppEnv;
import java.time.Instant;
import java.util.Date;
import org.junit.jupiter.api.Test;

public class SalesforceUtilsUnitTests {

  @Test
  public void testIsIdValid() {
    assertTrue(SalesforceUtils.isIdValid("001A000001FTyJr"));
    assertTrue(SalesforceUtils.isIdValid("001A000001FTyJrIAL"));
    assertTrue(SalesforceUtils.isIdValid("00QA000001JhyG7MAJ"));
    assertFalse(SalesforceUtils.isIdValid(";;QA000001JhyG7MAJ"));
    assertFalse(SalesforceUtils.isIdValid("00QA000001JhyG7MJ"));
  }

  @Test
  public void testIsIdEqual() {
    assertTrue(SalesforceUtils.isIdEqual("001A000001FTyJr", "001A000001FTyJr"));
    assertTrue(SalesforceUtils.isIdEqual("001A000001FTyJr", "001A000001FTyJrIAL"));
    assertTrue(SalesforceUtils.isIdEqual("001A000001FTyJrIAL", "001A000001FTyJrIAL"));
    assertFalse(SalesforceUtils.isIdEqual("001A000001FTyJr", "001A000001FTyJR"));
    assertFalse(SalesforceUtils.isIdEqual("001A000001FTyjk", "001A000001FTyjf"));
  }

  @Test
  public void testIsValidationActivationCode() {
    String commercialCode = "3586042218233987";
    String cloudCode = "CLOUDGOV3586042218233987";
    String atlasCode = "ATLASGOV3586042218233987";

    for (AppEnv env : AppEnv.values()) {
      assertFalse(SalesforceUtils.isValidActivationCode(env, ""));
      assertFalse(SalesforceUtils.isValidActivationCode(env, null));

      if (env.isGovCloud()) {
        assertFalse(SalesforceUtils.isValidActivationCode(env, commercialCode));
        assertTrue(SalesforceUtils.isValidActivationCode(env, cloudCode));
        assertTrue(SalesforceUtils.isValidActivationCode(env, atlasCode));
      } else {
        assertTrue(SalesforceUtils.isValidActivationCode(env, commercialCode));
        assertFalse(SalesforceUtils.isValidActivationCode(env, cloudCode));
        assertFalse(SalesforceUtils.isValidActivationCode(env, atlasCode));
      }
    }
  }

  @Test
  public void testGenerateCodeFromCurrentTime() {
    Date now = Date.from(Instant.ofEpochSecond(1624641123));

    assertEquals(
        "ATLASGOV1111624641123000", SalesforceUtils.generateCodeFromCurrentTime(now, true));
    assertEquals("1111624641123000", SalesforceUtils.generateCodeFromCurrentTime(now, false));
  }
}
