package com.xgen.cloud.sfdc._private.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.svc.mms.BaseUTest;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import org.junit.jupiter.api.Test;

public class SkuSalesforceConfigUnitTests extends BaseUTest {
  // check that it's not missing any definition. All SKUs should be defined in SKUSalesforceConfig
  @Test
  public void validateEnums() {
    SKU[] skuList = SKU.values();
    SKUSalesforceConfig[] SKUSalesforceConfigList = SKUSalesforceConfig.values();
    List<SKU> missingDefs = new LinkedList<>();
    for (SKU sku : skuList) {
      if (!isInConfig(sku, SKUSalesforceConfigList)) {
        missingDefs.add(sku);
      }
    }
    StringBuilder errorMsg = new StringBuilder("Missing SKU definitions in SKUSalesforceConfig: ");
    missingDefs.forEach(sku -> errorMsg.append(sku.name()).append(", "));
    assertEquals(0, missingDefs.size(), errorMsg.toString());
  }

  private boolean isInConfig(SKU pSku, SKUSalesforceConfig[] pSKUSalesforceConfigArr) {
    List<SKUSalesforceConfig> pSKUSalesforceConfigList = Arrays.asList(pSKUSalesforceConfigArr);
    boolean hasNdsEntry = pSKUSalesforceConfigList.contains(SKUSalesforceConfig.NDS_ALL);
    if (pSku.getInfo().isNDS()) {
      return hasNdsEntry;
    }
    return pSKUSalesforceConfigList.stream()
        .anyMatch(config -> config.getInternalSKU().equals(pSku.name()));
  }
}
