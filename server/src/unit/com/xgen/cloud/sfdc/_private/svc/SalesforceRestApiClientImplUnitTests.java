package com.xgen.cloud.sfdc._private.svc;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeast;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.sfdc._private.svc.SalesforceRestApiClientImpl.OAuthConnection;
import com.xgen.cloud.sfdc._public.util.SalesforceConfig;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.mms.util.http.UnexpectedResponseException;
import java.io.IOException;
import java.net.URI;
import java.util.List;
import java.util.Map;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpStatus;
import org.apache.http.HttpVersion;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicHttpResponse;
import org.apache.http.message.BasicStatusLine;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class SalesforceRestApiClientImplUnitTests {
  private static final String USERNAME = "usernameValue";
  private static final String PASSWORD = "passwordValue";
  private static final String CLIENT_ID = "clientIdValue";
  private static final String SECRET = "secretValue";
  private static final String OAUTH_BASE_ENDPOINT = "https://oauth-endpoint.test/";
  private static final String OAUTH_ENDPOINT = OAUTH_BASE_ENDPOINT + "token";
  private static final String INSTANCE_URL = "https://instance-endpoint.test";
  private static final String ACCESS_TOKEN = "accessTokenValue";
  @Mock private HttpUtils httpUtils;
  @Mock private CloseableHttpClient httpClient;
  @Captor private ArgumentCaptor<HttpPost> postCaptor;
  @Captor private ArgumentCaptor<HttpGet> getCaptor;
  private SalesforceRestApiClientImpl salesforceRestApiClient;

  @BeforeEach
  public void setup() {
    salesforceRestApiClient =
        new SalesforceRestApiClientImpl(new StaticSalesforceConfig(), httpUtils);
    when(httpUtils.getClient()).thenReturn(httpClient);
  }

  @Test
  public void testGetForJsonObject_succeeded() throws Exception {
    when(httpUtils.doPostForm(eq(OAUTH_ENDPOINT), any()))
        .thenReturn(
            new JSONObject(
                Map.of(
                    "instance_url", INSTANCE_URL,
                    "access_token", ACCESS_TOKEN)));
    JSONObject responseBody = new JSONObject(Map.of("a", 1));
    when(httpClient.execute(getCaptor.capture()))
        .thenReturn(response(HttpStatus.SC_OK, responseBody));

    JSONObject result =
        salesforceRestApiClient.getForJsonObject("/endpoint", Map.of(), HttpStatus.SC_OK);

    assertThat(result.toString()).isEqualTo(responseBody.toString());

    HttpGet httpGet = getCaptor.getValue();
    assertThat(httpGet.getURI()).isEqualTo(new URI(INSTANCE_URL + "/endpoint"));
    assertThat(httpGet.getFirstHeader(HttpHeaders.AUTHORIZATION).getValue())
        .isEqualTo("OAuth " + ACCESS_TOKEN);

    OAuthConnection connection = salesforceRestApiClient.getOAuthConnection();
    assertThat(connection).isNotNull();
    assertThat(connection.accessToken()).isEqualTo(ACCESS_TOKEN);
    assertThat(connection.instanceUrl()).isEqualTo(INSTANCE_URL);
  }

  @Test
  public void testGetForJsonObject_authTwoAttempts() throws Exception {
    when(httpUtils.doPostForm(eq(OAUTH_ENDPOINT), any()))
        .thenThrow(new RuntimeException("something failed"))
        .thenReturn(
            new JSONObject(
                Map.of(
                    "instance_url", INSTANCE_URL,
                    "access_token", ACCESS_TOKEN)));
    JSONObject responseBody = new JSONObject(Map.of("a", 1));
    when(httpClient.execute(getCaptor.capture()))
        .thenReturn(response(HttpStatus.SC_OK, responseBody));

    JSONObject result =
        salesforceRestApiClient.getForJsonObject("/endpoint", Map.of(), HttpStatus.SC_OK);

    assertThat(result.toString()).isEqualTo(responseBody.toString());

    List<HttpGet> httpGets = getCaptor.getAllValues();
    assertThat(httpGets).hasSize(1);
    HttpGet httpGet = httpGets.get(0);
    assertThat(httpGet.getURI()).isEqualTo(new URI(INSTANCE_URL + "/endpoint"));
    assertThat(httpGet.getFirstHeader(HttpHeaders.AUTHORIZATION).getValue())
        .isEqualTo("OAuth " + ACCESS_TOKEN);

    OAuthConnection connection = salesforceRestApiClient.getOAuthConnection();
    assertThat(connection).isNotNull();
    assertThat(connection.accessToken()).isEqualTo(ACCESS_TOKEN);
    assertThat(connection.instanceUrl()).isEqualTo(INSTANCE_URL);
  }

  @Test
  public void testGetForJsonObject_tokenExpired() throws Exception {
    when(httpUtils.doPostForm(eq(OAUTH_ENDPOINT), any()))
        .thenReturn(
            new JSONObject(
                Map.of(
                    "instance_url", "https://another-instance.test",
                    "access_token", "expiredToken")))
        .thenReturn(
            new JSONObject(
                Map.of(
                    "instance_url", INSTANCE_URL,
                    "access_token", ACCESS_TOKEN)));
    JSONObject responseBody = new JSONObject(Map.of("a", 1));
    when(httpClient.execute(getCaptor.capture()))
        .thenReturn(response(HttpStatus.SC_UNAUTHORIZED, new JSONObject(Map.of("b", 2))))
        .thenReturn(response(HttpStatus.SC_OK, responseBody));

    JSONObject result =
        salesforceRestApiClient.getForJsonObject("/endpoint", Map.of(), HttpStatus.SC_OK);

    assertThat(result.toString()).isEqualTo(responseBody.toString());

    List<HttpGet> httpGets = getCaptor.getAllValues();
    assertThat(httpGets).hasSize(2);
    assertThat(httpGets.get(0).getURI())
        .isEqualTo(new URI("https://another-instance.test/endpoint"));
    assertThat(httpGets.get(0).getFirstHeader(HttpHeaders.AUTHORIZATION).getValue())
        .isEqualTo("OAuth expiredToken");
    assertThat(httpGets.get(1).getURI()).isEqualTo(new URI(INSTANCE_URL + "/endpoint"));
    assertThat(httpGets.get(1).getFirstHeader(HttpHeaders.AUTHORIZATION).getValue())
        .isEqualTo("OAuth " + ACCESS_TOKEN);

    OAuthConnection connection = salesforceRestApiClient.getOAuthConnection();
    assertThat(connection).isNotNull();
    assertThat(connection.accessToken()).isEqualTo(ACCESS_TOKEN);
    assertThat(connection.instanceUrl()).isEqualTo(INSTANCE_URL);
  }

  @Test
  public void testPostForJsonObject_endpoint_succeeded() throws Exception {
    when(httpUtils.doPostForm(eq(OAUTH_ENDPOINT), any()))
        .thenReturn(
            new JSONObject(
                Map.of(
                    "instance_url", INSTANCE_URL,
                    "access_token", ACCESS_TOKEN)));
    JSONObject requestBody = new JSONObject(Map.of("a", 1));
    JSONObject responseBody = new JSONObject(Map.of("b", 2));
    when(httpClient.execute(postCaptor.capture()))
        .thenReturn(response(HttpStatus.SC_OK, responseBody));

    JSONObject result =
        salesforceRestApiClient.postForJsonObject("/endpoint", requestBody, HttpStatus.SC_OK);

    assertThat(result.toString()).isEqualTo(responseBody.toString());

    HttpPost httpPost = postCaptor.getValue();
    assertThat(httpPost.getURI()).isEqualTo(new URI(INSTANCE_URL + "/endpoint"));
    assertThat(httpPost.getFirstHeader(HttpHeaders.AUTHORIZATION).getValue())
        .isEqualTo("OAuth " + ACCESS_TOKEN);

    OAuthConnection connection = salesforceRestApiClient.getOAuthConnection();
    assertThat(connection).isNotNull();
    assertThat(connection.accessToken()).isEqualTo(ACCESS_TOKEN);
    assertThat(connection.instanceUrl()).isEqualTo(INSTANCE_URL);
  }

  @Test
  public void testPostForJsonObject_endpoint_failedExpectedStatus() throws Exception {
    when(httpUtils.doPostForm(eq(OAUTH_ENDPOINT), any()))
        .thenReturn(
            new JSONObject(
                Map.of(
                    "instance_url", INSTANCE_URL,
                    "access_token", ACCESS_TOKEN)));
    JSONObject requestBody = new JSONObject(Map.of("a", 1));
    JSONObject responseBody = new JSONObject(Map.of("b", 2));
    when(httpClient.execute(postCaptor.capture()))
        .thenReturn(response(HttpStatus.SC_BAD_REQUEST, responseBody));

    salesforceRestApiClient.postForJsonObject(
        "/endpoint", requestBody, List.of(HttpStatus.SC_OK, HttpStatus.SC_BAD_REQUEST), null);

    verify(httpClient).execute(any());
  }

  @Test
  public void testPostForJsonObject_endpoint_failedUnexpectedStatus() throws Exception {
    when(httpUtils.doPostForm(eq(OAUTH_ENDPOINT), any()))
        .thenReturn(
            new JSONObject(
                Map.of(
                    "instance_url", INSTANCE_URL,
                    "access_token", ACCESS_TOKEN)));
    JSONObject requestBody = new JSONObject(Map.of("a", 1));
    JSONObject responseBody = new JSONObject(Map.of("b", 2));
    when(httpClient.execute(postCaptor.capture()))
        .thenReturn(response(HttpStatus.SC_UNAUTHORIZED, responseBody));

    assertThrows(
        UnexpectedResponseException.class,
        () ->
            salesforceRestApiClient.postForJsonObject(
                "/endpoint",
                requestBody,
                List.of(HttpStatus.SC_OK, HttpStatus.SC_BAD_REQUEST),
                null));

    verify(httpClient, atLeast(1)).execute(any());
  }

  @Test
  public void testPostForJsonObject_uri_succeeded() throws Exception {
    URI customUri = new URI("https://custom-endpoint.test/smth");
    when(httpUtils.doPostForm(eq(OAUTH_ENDPOINT), any()))
        .thenReturn(
            new JSONObject(
                Map.of(
                    "instance_url", INSTANCE_URL,
                    "access_token", ACCESS_TOKEN)));
    JSONObject requestBody = new JSONObject(Map.of("a", 1));
    JSONObject responseBody = new JSONObject(Map.of("b", 2));
    when(httpClient.execute(postCaptor.capture()))
        .thenReturn(response(HttpStatus.SC_OK, responseBody));

    JSONObject result =
        salesforceRestApiClient.postForJsonObject(customUri, requestBody, HttpStatus.SC_OK);

    assertThat(result.toString()).isEqualTo(responseBody.toString());

    HttpPost httpPost = postCaptor.getValue();
    assertThat(httpPost.getURI()).isEqualTo(customUri);
    assertThat(httpPost.getFirstHeader(HttpHeaders.AUTHORIZATION).getValue())
        .isEqualTo("OAuth " + ACCESS_TOKEN);

    OAuthConnection connection = salesforceRestApiClient.getOAuthConnection();
    assertThat(connection).isNotNull();
    assertThat(connection.accessToken()).isEqualTo(ACCESS_TOKEN);
    assertThat(connection.instanceUrl()).isEqualTo(INSTANCE_URL);
  }

  @Test
  public void testPostForJsonObject_endpoint_tokenExpired() throws Exception {
    when(httpUtils.doPostForm(eq(OAUTH_ENDPOINT), any()))
        .thenReturn(
            new JSONObject(
                Map.of(
                    "instance_url", "https://another-instance.test",
                    "access_token", "expiredToken")))
        .thenReturn(
            new JSONObject(
                Map.of(
                    "instance_url", INSTANCE_URL,
                    "access_token", ACCESS_TOKEN)));
    JSONObject requestBody = new JSONObject(Map.of("a", 1));
    JSONObject responseBody = new JSONObject(Map.of("b", 2));
    when(httpClient.execute(postCaptor.capture()))
        .thenReturn(response(HttpStatus.SC_UNAUTHORIZED, responseBody))
        .thenReturn(response(HttpStatus.SC_OK, responseBody));

    JSONObject result =
        salesforceRestApiClient.postForJsonObject("/endpoint", requestBody, HttpStatus.SC_OK);

    assertThat(result.toString()).isEqualTo(responseBody.toString());

    List<HttpPost> httpPosts = postCaptor.getAllValues();
    assertThat(httpPosts).hasSize(2);
    assertThat(httpPosts.get(0).getURI())
        .isEqualTo(new URI("https://another-instance.test/endpoint"));
    assertThat(httpPosts.get(0).getFirstHeader(HttpHeaders.AUTHORIZATION).getValue())
        .isEqualTo("OAuth expiredToken");
    assertThat(httpPosts.get(1).getURI()).isEqualTo(new URI(INSTANCE_URL + "/endpoint"));
    assertThat(httpPosts.get(1).getFirstHeader(HttpHeaders.AUTHORIZATION).getValue())
        .isEqualTo("OAuth " + ACCESS_TOKEN);

    OAuthConnection connection = salesforceRestApiClient.getOAuthConnection();
    assertThat(connection).isNotNull();
    assertThat(connection.accessToken()).isEqualTo(ACCESS_TOKEN);
    assertThat(connection.instanceUrl()).isEqualTo(INSTANCE_URL);
  }

  @Test
  public void testGetForJsonObject_IOException() throws Exception {
    when(httpUtils.doPostForm(eq(OAUTH_ENDPOINT), any()))
        .thenReturn(
            new JSONObject(
                Map.of(
                    "instance_url", INSTANCE_URL,
                    "access_token", ACCESS_TOKEN)));
    when(httpClient.execute(any())).thenThrow(new IOException("Connection timeout"));

    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                salesforceRestApiClient.getForJsonObject("/endpoint", Map.of(), HttpStatus.SC_OK));

    verify(httpClient, times(6)).execute(any());
    assertThat(exception.getErrorCode()).isEqualTo(CommonErrorCode.INTERRUPTED);
    assertThat(exception.getCause()).isInstanceOf(IOException.class);
    assertThat(exception.getCause().getMessage()).isEqualTo("Connection timeout");
  }

  private CloseableHttpResponse response(int status, JSONObject content) {
    CloseableHttpResponse response =
        new CloseableHttpResponseImpl(new BasicStatusLine(HttpVersion.HTTP_1_1, status, ""));
    HttpEntity entity = new StringEntity(content.toString(), ContentType.APPLICATION_JSON);
    response.setEntity(entity);
    return response;
  }

  private static class CloseableHttpResponseImpl extends BasicHttpResponse
      implements CloseableHttpResponse {
    public CloseableHttpResponseImpl(StatusLine statusline) {
      super(statusline);
    }

    @Override
    public void close() throws IOException {
      // No-op
    }
  }

  private static class StaticSalesforceConfig implements SalesforceConfig {
    @Override
    public String getSalesforceApiUsername() {
      return USERNAME;
    }

    @Override
    public String getSalesforceApiPassword() {
      return PASSWORD;
    }

    @Override
    public String getSalesforceServiceCloudClientId() {
      return CLIENT_ID;
    }

    @Override
    public String getSalesforceServiceCloudSecret() {
      return SECRET;
    }

    @Override
    public String getSoapAPIAuthEndPoint() {
      return null;
    }

    @Override
    public String getOAuthBaseEndpoint() {
      return OAUTH_BASE_ENDPOINT;
    }
  }
}
