package com.xgen.cloud.services.config._private.svc;

import static com.xgen.cloud.services.config._public.constants.Constants.ApplicationProperties.LOCAL_FILE_NAMESPACE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mongodb.client.ClientSession;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.InsertOneResult;
import com.xgen.cloud.services.config._private.constants.Constants;
import com.xgen.cloud.services.config._private.constants.Constants.ConfigModuleType;
import com.xgen.cloud.services.config._private.dao.ConfigNamespaceDao;
import com.xgen.cloud.services.config._private.dao.CoreConfigDao;
import com.xgen.cloud.services.config._private.model.ConfigNamespace;
import com.xgen.cloud.services.config._private.model.CoreConfig;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.BsonDocument;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CoreConfigSvcUnitTests {

  @Spy @InjectMocks CoreConfigSvc coreConfigSvc;

  @Mock ConfigNamespaceDao configNamespaceDao;
  @Mock CoreConfigDao coreConfigDao;

  private final String mockNamespaceName = "mockNamespace";
  private final String mockGlobalName = "global";
  private final ConfigModuleType mockModule = Constants.ConfigModuleType.DEMO_MODULE;
  private final ObjectId objectId = new ObjectId();
  private final CoreConfig mockConfig1 =
      new CoreConfig(objectId, "mockConfig1", mockNamespaceName, mockModule);
  private final CoreConfig mockConfig2 =
      new CoreConfig(new ObjectId(), "mockConfig2", mockNamespaceName, mockModule);
  private final CoreConfig mockConfig3 =
      new CoreConfig(new ObjectId(), "mockConfig3", mockGlobalName, mockModule);
  private final ConfigNamespace mockNamespace =
      new ConfigNamespace(new ObjectId(), mockNamespaceName, List.of("role1"));
  private final List<CoreConfig> mockConfigList1 = List.of(mockConfig1, mockConfig2, mockConfig3);

  @Test
  public void testListConfigNamespaces() {
    List<ConfigNamespace> expectedNamespaces =
        List.of(
            new ConfigNamespace(new ObjectId(), "name 1", List.of("authRole1")),
            new ConfigNamespace(new ObjectId(), "name 2", List.of("authRole1", "authRole2")),
            new ConfigNamespace(new ObjectId(), "name 3", List.of("authRole3")));

    doReturn(expectedNamespaces).when(configNamespaceDao).findAll();

    List<ConfigNamespace> resultNamespaces = coreConfigSvc.listConfigNamespaces();

    assertEquals(resultNamespaces, expectedNamespaces);
  }

  @Test
  public void testCreateConfigNamespace() {
    InsertOneResult mockResult = InsertOneResult.acknowledged(new BsonDocument());
    when(configNamespaceDao.create(any(ConfigNamespace.class))).thenReturn(mockResult);

    InsertOneResult result = coreConfigSvc.createConfigNamespace(mockNamespace);

    assertEquals(mockResult, result);
  }

  @Test
  public void testCreateConfigNamespace_containsForbiddenCharacter() {
    ConfigNamespace badNamespace =
        new ConfigNamespace(new ObjectId(), "new:Namespace", List.of("authRole1", "authRole2"));

    final IllegalArgumentException thrown =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              coreConfigSvc.createConfigNamespace(badNamespace);
            });
    assertEquals("Namespace 'new:Namespace' cannot contain character :", thrown.getMessage());
  }

  @Test
  public void testCreateConfigNamespace_sameNameAsLocalFileNamespace() {
    ConfigNamespace badNamespace =
        new ConfigNamespace(new ObjectId(), LOCAL_FILE_NAMESPACE, List.of("authRole1"));

    final IllegalArgumentException thrown =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              coreConfigSvc.createConfigNamespace(badNamespace);
            });
    assertEquals(
        String.format(
            "Namespace '%s' cannot have the same name as the local file namespace",
            LOCAL_FILE_NAMESPACE),
        thrown.getMessage());
  }

  @Test
  public void testFindConfigNamespaceByName() {
    final String expectedName = "testNamespace";
    final ConfigNamespace expectedNamespace =
        new ConfigNamespace(new ObjectId(), expectedName, List.of("authRole1"));

    doReturn(Optional.of(expectedNamespace)).when(configNamespaceDao).findByName(expectedName);

    final Optional<ConfigNamespace> resultNamespace =
        coreConfigSvc.findConfigNamespaceByName(expectedName);

    assertEquals(expectedNamespace, resultNamespace.get());
  }

  @Test
  public void testFindConfigNamespace() {
    final ObjectId expectedId = new ObjectId();
    final ConfigNamespace expectedNamespace =
        new ConfigNamespace(expectedId, "test.namespace", List.of("authRole1"));

    doReturn(Optional.of(expectedNamespace)).when(configNamespaceDao).findById(expectedId);

    final Optional<ConfigNamespace> resultNamespace = coreConfigSvc.findConfigNamespace(expectedId);

    assertEquals(expectedNamespace, resultNamespace.get());
  }

  @Test
  public void testDeleteConfigNamespace() {
    String name = "deleting";

    doReturn(Optional.empty()).when(coreConfigDao).findFirstInNamespace(name);
    doReturn(DeleteResult.acknowledged(1)).when(configNamespaceDao).delete(name);

    assertEquals(coreConfigSvc.deleteConfigNamespace(name), DeleteResult.acknowledged(1));
  }

  @Test
  public void testDeleteConfigNamespace_namespace_in_use() {
    String name = "deleting";

    doReturn(Optional.of(new CoreConfig())).when(coreConfigDao).findFirstInNamespace(name);

    final IllegalStateException thrown =
        assertThrows(
            IllegalStateException.class,
            () -> {
              coreConfigSvc.deleteConfigNamespace(name);
            });
    assertEquals(
        "Namespace 'deleting' is used in a coreConfig and cannot be deleted", thrown.getMessage());
  }

  @Test
  public void testFindConfigsByNamespaces() {
    final Set<String> requestedNamespaces =
        new HashSet<>(List.of(mockNamespaceName, mockGlobalName));
    final List<CoreConfig> expectedConfigs = List.of(mockConfig1, mockConfig2, mockConfig3);

    doReturn(expectedConfigs).when(coreConfigDao).findByNamespaces(requestedNamespaces);

    final List<CoreConfig> configs = coreConfigSvc.findConfigsByNamespaces(requestedNamespaces);

    assertEquals(expectedConfigs, configs);
  }

  @Test
  public void testUpdateConfigNamespace() {
    final List<String> authRoles = List.of("role1", "role2");
    final Optional<ConfigNamespace> expectedResult =
        Optional.of(new ConfigNamespace(new ObjectId(), mockNamespaceName, authRoles));

    when(configNamespaceDao.updateOne(any(), any())).thenReturn(expectedResult);

    Optional<ConfigNamespace> resultNamespace =
        coreConfigSvc.updateConfigNamespace(mockNamespaceName, authRoles);

    assertEquals(expectedResult, resultNamespace);
  }

  @Test
  public void testCreateCoreConfig_withoutClientSession() {
    InsertOneResult mockResult = InsertOneResult.acknowledged(new BsonDocument());
    when(coreConfigDao.create(any(CoreConfig.class))).thenReturn(mockResult);
    when(configNamespaceDao.findByName(mockConfig1.getNamespace()))
        .thenReturn(Optional.of(mockNamespace));
    InsertOneResult result = coreConfigSvc.createCoreConfig(mockConfig1);
    assertEquals(mockResult, result);
  }

  @Test
  public void testCreateCoreConfig_withClientSession() {
    InsertOneResult mockResult = InsertOneResult.acknowledged(new BsonDocument());
    when(coreConfigDao.create(any(ClientSession.class), any(CoreConfig.class)))
        .thenReturn(mockResult);
    when(configNamespaceDao.findByName(mockConfig1.getNamespace()))
        .thenReturn(Optional.of(mockNamespace));
    InsertOneResult result = coreConfigSvc.createCoreConfig(mock(ClientSession.class), mockConfig1);
    assertEquals(mockResult, result);
  }

  @Test
  public void testCreateCoreConfig_withNonExistentNamespace() {
    Exception e =
        assertThrows(
            IllegalArgumentException.class,
            () -> {
              coreConfigSvc.createCoreConfig(mockConfig1);
            });
    assertEquals(
        e.getMessage(), String.format("Namespace '%s' does not exist", mockConfig1.getNamespace()));
  }

  @Test
  public void testDeleteCoreConfig() {
    DeleteResult mockResult = DeleteResult.acknowledged(1);
    when(coreConfigDao.delete(any(ClientSession.class), any(ObjectId.class)))
        .thenReturn(mockResult);
    ObjectId coreConfigId = new ObjectId();
    DeleteResult result = coreConfigSvc.deleteCoreConfig(mock(ClientSession.class), coreConfigId);
    assertEquals(mockResult, result);
  }

  @Test
  public void testInsertCoreConfigs() {
    ClientSession session = mock(ClientSession.class);

    coreConfigSvc.insertCoreConfigs(session, mockConfigList1);
    verify(coreConfigDao).bulkInsert(session, mockConfigList1);
  }

  @Test
  public void testFindByIds() {
    final List<CoreConfig> expectedResult = List.of(mockConfig1);
    when(coreConfigDao.findByIds(List.of(objectId))).thenReturn(expectedResult);

    List<CoreConfig> resultCoreConfig = coreConfigSvc.findByIds(List.of(objectId));

    assertEquals(expectedResult, resultCoreConfig);
  }

  @Test
  public void testFindById() {
    when(coreConfigDao.findById(objectId)).thenReturn(Optional.of(mockConfig1));

    Optional<CoreConfig> resultCoreConfigOptional = coreConfigSvc.findById(objectId);

    assertTrue(resultCoreConfigOptional.isPresent());
    assertEquals(mockConfig1, resultCoreConfigOptional.get());
  }

  @Test
  public void testDeleteByIds() {
    doReturn(DeleteResult.acknowledged(1))
        .when(coreConfigDao)
        .deleteByIds(any(ClientSession.class), anyList());
    assertEquals(
        coreConfigSvc.deleteByIds(mock(ClientSession.class), List.of(objectId)),
        DeleteResult.acknowledged(1));
  }
}
