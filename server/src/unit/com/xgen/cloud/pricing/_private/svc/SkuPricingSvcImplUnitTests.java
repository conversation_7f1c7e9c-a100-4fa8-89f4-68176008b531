package com.xgen.cloud.pricing._private.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.xgen.cloud.billingplatform.model.sku._public.model.PricingConsumers;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKUInfo;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuPricing;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.pricing._private.client.SkuPriceServiceClient;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class SkuPricingSvcImplUnitTests {

  @Mock private SkuPricing mockSkuPricing1;
  @Mock private SkuPricing mockSkuPricing2;
  @Mock private Organization organization;
  @Mock private FeatureFlagSvc featureFlagSvc;
  @Mock private OrganizationDao organizationDao;
  @Mock private SkuPriceServiceClient skuPriceServiceClient;
  @InjectMocks private SkuPricingSvcImpl skuPricingSvc;

  private final Date testDate = new Date();
  private final ObjectId testOrgId = new ObjectId();

  @Test
  void getPricing_withAllParameters_delegatesToSkuInfo() {
    // When
    SKU sku = mockSku(mockSkuPricing1);
    when(organizationDao.findById(testOrgId)).thenReturn(organization);
    when(featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.BILLING_USE_PRICING_API, organization, null))
        .thenReturn(false);
    SkuPricing result =
        skuPricingSvc.getPricing(sku, testDate, testOrgId, PricingConsumers.BILLING);

    // Then
    assertEquals(mockSkuPricing1, result);
  }

  @Test
  void getPricing_withoutRemoteCall_delegatesToSkuInfo() {
    // When
    SKU sku = mockSku(mockSkuPricing1);
    SkuPricing result =
        skuPricingSvc.getPricing(sku, testDate, testOrgId, PricingConsumers.BILLING);

    // Then
    assertEquals(mockSkuPricing1, result);
  }

  @Test
  void getPricing_withoutOrgId_delegatesToSkuInfo() {
    // When
    SKU sku = mockSku(mockSkuPricing1);
    SkuPricing result = skuPricingSvc.getPricing(sku, testDate, null, PricingConsumers.BILLING);

    // Then
    assertEquals(mockSkuPricing1, result);
  }

  // Batch SKU getPricing Tests

  @Test
  void getPricing_batchWithAllParameters_delegatesToSkuInfo() {
    // Given
    SKU sku = mockSku(mockSkuPricing1);
    SKU sku2 = mockSku(mockSkuPricing2);
    List<SKU> skus = Arrays.asList(sku, sku2);
    when(organizationDao.findById(testOrgId)).thenReturn(organization);
    when(featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.BILLING_USE_PRICING_API, organization, null))
        .thenReturn(false);

    // When
    Map<SKU, SkuPricing> result =
        skuPricingSvc.getPricing(skus, testDate, testOrgId, PricingConsumers.BILLING);

    // Then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals(mockSkuPricing1, result.get(sku));
    assertEquals(mockSkuPricing2, result.get(sku2));
  }

  @Test
  void getPricing_batchWithoutRemoteCall_delegatesToSkuInfo() {
    // Given
    SKU sku = mockSku(mockSkuPricing1);
    SKU sku2 = mockSku(mockSkuPricing2);
    List<SKU> skus = Arrays.asList(sku, sku2);

    // When
    Map<SKU, SkuPricing> result =
        skuPricingSvc.getPricing(skus, testDate, testOrgId, PricingConsumers.BILLING);

    // Then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals(mockSkuPricing1, result.get(sku));
    assertEquals(mockSkuPricing2, result.get(sku2));
  }

  @Test
  void getPricing_batchWithoutOrgId_delegatesToSkuInfo() {
    // Given
    SKU sku = mockSku(mockSkuPricing1);
    SKU sku2 = mockSku(mockSkuPricing2);
    List<SKU> skus = Arrays.asList(sku, sku2);

    // When
    Map<SKU, SkuPricing> result =
        skuPricingSvc.getPricing(skus, testDate, null, PricingConsumers.BILLING);

    // Then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals(mockSkuPricing1, result.get(sku));
    assertEquals(mockSkuPricing2, result.get(sku2));
  }

  // Edge Cases and Error Handling Tests

  @Test
  void getPricing_withEmptySkuList_returnsEmptyMap() {
    // Given
    List<SKU> emptySkus = Collections.emptyList();

    // When
    Map<SKU, SkuPricing> result =
        skuPricingSvc.getPricing(emptySkus, testDate, testOrgId, PricingConsumers.BILLING);

    // Then
    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  void getPricing_withNullSku_throwsException() {
    // When/Then
    assertThrows(
        NullPointerException.class,
        () -> skuPricingSvc.getPricing((SKU) null, testDate, testOrgId, PricingConsumers.BILLING));
  }

  @Test
  void getPricing_batchWithNullSkuList_throwsException() {
    // When/Then
    assertThrows(
        NullPointerException.class,
        () ->
            skuPricingSvc.getPricing(
                (List<SKU>) null, testDate, testOrgId, PricingConsumers.BILLING));
  }

  // Default Method Behavior Tests

  @Test
  void getPricing_defaultMethodWithoutRemoteCall_callsMainMethod() {
    // When
    SKU sku = mockSku(mockSkuPricing1);
    SkuPricing result =
        skuPricingSvc.getPricing(sku, testDate, testOrgId, PricingConsumers.BILLING);

    // Then
    assertEquals(mockSkuPricing1, result);
  }

  @Test
  void getPricing_defaultMethodWithoutOrgId_callsMainMethod() {
    // When
    SKU sku = mockSku(mockSkuPricing1);
    SkuPricing result = skuPricingSvc.getPricing(sku, testDate, null, PricingConsumers.BILLING);

    // Then
    assertEquals(mockSkuPricing1, result);
  }

  @Test
  void getPricing_batchDefaultMethodWithoutRemoteCall_callsMainMethod() {
    // Given
    SKU sku = mockSku(mockSkuPricing1);
    SKU sku2 = mockSku(mockSkuPricing2);
    List<SKU> skus = Arrays.asList(sku, sku2);

    // When
    Map<SKU, SkuPricing> result =
        skuPricingSvc.getPricing(skus, testDate, testOrgId, PricingConsumers.BILLING);

    // Then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals(mockSkuPricing1, result.get(sku));
    assertEquals(mockSkuPricing2, result.get(sku2));
  }

  @Test
  void getPricing_batchDefaultMethodWithoutOrgId_callsMainMethod() {
    // Given
    SKU sku = mockSku(mockSkuPricing1);
    SKU sku2 = mockSku(mockSkuPricing2);
    List<SKU> skus = Arrays.asList(sku, sku2);

    // When
    Map<SKU, SkuPricing> result =
        skuPricingSvc.getPricing(skus, testDate, null, PricingConsumers.BILLING);

    // Then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals(mockSkuPricing1, result.get(sku));
    assertEquals(mockSkuPricing2, result.get(sku2));
  }

  @Test
  void getPricing_withFeatureFlagEnabled_callsGrpcClient() {
    // Given
    SKU sku = mock(SKU.class);
    when(organizationDao.findById(testOrgId)).thenReturn(organization);
    when(featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.BILLING_USE_PRICING_API, organization, null))
        .thenReturn(true);
    when(skuPriceServiceClient.fetchSkuPricing(List.of(sku), testDate))
        .thenReturn(Map.of(sku, mockSkuPricing1));

    // When
    SkuPricing result =
        skuPricingSvc.getPricing(sku, testDate, testOrgId, PricingConsumers.BILLING);

    // Then
    assertEquals(mockSkuPricing1, result);
  }

  @Test
  void getPricing_withFeatureFlagEnabledButClientReturnsEmpty_fallsBackToLocal() {
    // Given
    SKU sku = mockSku(mockSkuPricing1);
    when(organizationDao.findById(testOrgId)).thenReturn(organization);
    when(featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.BILLING_USE_PRICING_API, organization, null))
        .thenReturn(true);
    when(skuPriceServiceClient.fetchSkuPricing(List.of(sku), testDate))
        .thenReturn(Map.of()); // Empty response

    // When
    SkuPricing result =
        skuPricingSvc.getPricing(sku, testDate, testOrgId, PricingConsumers.BILLING);

    // Then
    assertEquals(mockSkuPricing1, result);
  }

  @Test
  void getPricing_withFeatureFlagEnabledButClientThrows_fallsBackToLocal() {
    // Given
    SKU sku = mock(SKU.class);
    when(organizationDao.findById(testOrgId)).thenReturn(organization);
    when(featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.BILLING_USE_PRICING_API, organization, null))
        .thenReturn(true);
    when(skuPriceServiceClient.fetchSkuPricing(List.of(sku), testDate))
        .thenThrow(new RuntimeException("gRPC call failed"));

    // When
    assertThrows(
        RuntimeException.class,
        () -> skuPricingSvc.getPricing(sku, testDate, testOrgId, PricingConsumers.BILLING));
  }

  @Test
  void getPricing_batchWithFeatureFlagEnabled_callsGrpcClient() {
    // Given
    SKU sku1 = mock(SKU.class);
    SKU sku2 = mock(SKU.class);
    List<SKU> skus = Arrays.asList(sku1, sku2);
    when(organizationDao.findById(testOrgId)).thenReturn(organization);
    when(featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.BILLING_USE_PRICING_API, organization, null))
        .thenReturn(true);
    when(skuPriceServiceClient.fetchSkuPricing(skus, testDate))
        .thenReturn(Map.of(sku1, mockSkuPricing1, sku2, mockSkuPricing2));

    // When
    Map<SKU, SkuPricing> result =
        skuPricingSvc.getPricing(skus, testDate, testOrgId, PricingConsumers.BILLING);

    // Then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals(mockSkuPricing1, result.get(sku1));
    assertEquals(mockSkuPricing2, result.get(sku2));
  }

  @Test
  void getPricing_batchWithFeatureFlagEnabledPartialResponse_mixesGrpcAndLocal() {
    // Given
    SKU sku1 = mock(SKU.class);
    SKU sku2 = mockSku(mockSkuPricing2); // Needs local pricing fallback
    List<SKU> skus = Arrays.asList(sku1, sku2);
    when(organizationDao.findById(testOrgId)).thenReturn(organization);
    when(featureFlagSvc.isFeatureFlagEnabled(
            FeatureFlag.BILLING_USE_PRICING_API, organization, null))
        .thenReturn(true);
    when(skuPriceServiceClient.fetchSkuPricing(skus, testDate))
        .thenReturn(Map.of(sku1, mockSkuPricing1)); // Only sku1 has gRPC pricing

    // When
    Map<SKU, SkuPricing> result =
        skuPricingSvc.getPricing(skus, testDate, testOrgId, PricingConsumers.BILLING);

    // Then
    assertNotNull(result);
    assertEquals(2, result.size());
    assertEquals(mockSkuPricing1, result.get(sku1));
    assertEquals(mockSkuPricing2, result.get(sku2));
  }

  private SKU mockSku(SkuPricing skuPricing) {
    SKU sku = mock(SKU.class);
    SKUInfo skuInfo = mock(SKUInfo.class);
    when(sku.getInfo()).thenReturn(skuInfo);
    when(skuInfo.getPricingForDate(testDate)).thenReturn(skuPricing);
    return sku;
  }
}
