package com.xgen.cloud.partners.vercel.billing._private.svc;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;

import com.xgen.cloud.partners.usage.report._public.model.PartnerUsageReport.Status;
import com.xgen.cloud.partners.usage.report._public.model.VercelUsageReport;
import com.xgen.cloud.partners.usage.report._public.svc.VercelUsageReportSvc;
import com.xgen.cloud.partners.usage.reporting._public.config.VercelUsageReportingConfig;
import com.xgen.cloud.partners.vercel.billing._public.config.VercelBillingConfig;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInstallationSvc;
import com.xgen.cloud.partners.vercel.sdk.client._public.svc.VercelMarketplaceApiClient;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.billingdata.SubmitBillingDataRequest;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.billingdata.SubmitBillingDataRequestBilling;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.billingdata.SubmitBillingDataRequestUsage;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.billingdata.SubmitBillingDataRequestUsageType;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelInstallation;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.VercelBillingPeriod;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class VercelUsageReportSubmissionSvcImplUnitTests {

  @Mock private Clock clock;
  @Mock private VercelBillingConfig vercelBillingConfig;
  @Mock private VercelInstallationSvc vercelInstallationSvc;
  @Mock private VercelMarketplaceApiClient vercelMarketplaceApiClient;
  @Mock private VercelUsageReportingConfig vercelUsageReportingConfig;
  @Mock private VercelUsageReportSvc vercelUsageReportSvc;

  @InjectMocks private VercelUsageReportSubmissionSvcImpl vercelUsageReportSubmissionSvcImpl;

  private static final Duration TWENTY_FOUR_HOURS = Duration.ofHours(24);
  private static final ZoneId UTC_ZONE = ZoneOffset.UTC;

  // Test time constants
  private final Instant baseInstant = Instant.parse("2024-01-15T12:00:00Z");
  private final LocalDateTime baseDateTime = LocalDateTime.ofInstant(baseInstant, UTC_ZONE);
  private final Date baseDate = Date.from(baseInstant);

  private void setupTimeBasedMocks() {
    doReturn(UTC_ZONE).when(clock).getZone();
    doReturn(baseInstant).when(clock).instant();
    doReturn(TWENTY_FOUR_HOURS).when(vercelUsageReportingConfig).getUsageReportingCutoffDuration();
  }

  @Test
  void isValidToProcess_alreadySuccessfulReport_returnsFalse() {
    // Test that reports with SUCCESSFUL status are skipped

    OffsetDateTime currentEod = baseInstant.atOffset(ZoneOffset.UTC);
    VercelUsageReport successfulReport =
        createTestUsageReportWithEod(Status.SUCCESSFUL, currentEod);
    VercelInstallation installation = createTestInstallation(null);

    boolean result =
        vercelUsageReportSubmissionSvcImpl.isValidToProcess(successfulReport, installation);

    assertFalse(result);
  }

  @Test
  void isValidToProcess_eodBeforeTwentyFourHourWindow_returnsFalse() {
    // Test that reports with EOD more than 24 hours ago are rejected
    setupTimeBasedMocks();

    OffsetDateTime twentyFiveHoursAgo =
        baseInstant.minus(Duration.ofHours(25)).atOffset(ZoneOffset.UTC);
    VercelUsageReport oldReport = createTestUsageReportWithEod(Status.NEW, twentyFiveHoursAgo);
    VercelInstallation installation = createTestInstallation(null);

    boolean result = vercelUsageReportSubmissionSvcImpl.isValidToProcess(oldReport, installation);

    assertFalse(result);
  }

  @Test
  void isValidToProcess_eodExactlyTwentyFourHoursAgo_returnsTrue() {
    // Test boundary condition: EOD exactly 24 hours ago should still be valid
    // The logic uses isBefore(), so exactly 24 hours ago is NOT considered expired
    setupTimeBasedMocks();

    OffsetDateTime exactlyTwentyFourHoursAgo =
        baseInstant.minus(TWENTY_FOUR_HOURS).atOffset(ZoneOffset.UTC);
    VercelUsageReport boundaryReport =
        createTestUsageReportWithEod(Status.NEW, exactlyTwentyFourHoursAgo);
    VercelInstallation installation = createTestInstallation(null);

    boolean result =
        vercelUsageReportSubmissionSvcImpl.isValidToProcess(boundaryReport, installation);

    assertTrue(result);
  }

  @Test
  void isValidToProcess_eodMoreThanTwentyFourHoursAgo_returnsFalse() {
    // Test that reports with EOD more than 24 hours ago are rejected
    // This should actually trigger the expired condition
    setupTimeBasedMocks();

    OffsetDateTime twentyFourHoursAndOneMinuteAgo =
        baseInstant.minus(TWENTY_FOUR_HOURS).minus(Duration.ofMinutes(1)).atOffset(ZoneOffset.UTC);
    VercelUsageReport expiredReport =
        createTestUsageReportWithEod(Status.NEW, twentyFourHoursAndOneMinuteAgo);
    VercelInstallation installation = createTestInstallation(null);

    boolean result =
        vercelUsageReportSubmissionSvcImpl.isValidToProcess(expiredReport, installation);

    assertFalse(result);
  }

  @Test
  void isValidToProcess_eodWithinTwentyFourHourWindow_returnsTrue() {
    // Test that reports with EOD within the 24-hour window are accepted
    setupTimeBasedMocks();

    OffsetDateTime twentyThreeHoursAgo =
        baseInstant.minus(Duration.ofHours(23)).atOffset(ZoneOffset.UTC);
    VercelUsageReport validReport = createTestUsageReportWithEod(Status.NEW, twentyThreeHoursAgo);
    VercelInstallation installation = createTestInstallation(null);

    boolean result = vercelUsageReportSubmissionSvcImpl.isValidToProcess(validReport, installation);

    assertTrue(result);
  }

  @Test
  void isValidToProcess_installationDeleted_returnsFalse() {
    // Test that any deleted installation is rejected (no 24-hour delay)
    setupTimeBasedMocks();

    LocalDateTime oneHourAgo = baseDateTime.minus(Duration.ofHours(1));
    OffsetDateTime currentEod = baseInstant.atOffset(ZoneOffset.UTC);
    VercelUsageReport validReport = createTestUsageReportWithEod(Status.NEW, currentEod);
    VercelInstallation deletedInstallation = createTestInstallation(oneHourAgo);

    boolean result =
        vercelUsageReportSubmissionSvcImpl.isValidToProcess(validReport, deletedInstallation);

    assertFalse(result);
  }

  @Test
  void isValidToProcess_installationDeletedRecentlyAlso_returnsFalse() {
    // Test that installations deleted recently are also rejected (no grace period)
    setupTimeBasedMocks();

    LocalDateTime oneMinuteAgo = baseDateTime.minus(Duration.ofMinutes(1));
    OffsetDateTime currentEod = baseInstant.atOffset(ZoneOffset.UTC);
    VercelUsageReport validReport = createTestUsageReportWithEod(Status.NEW, currentEod);
    VercelInstallation recentlyDeletedInstallation = createTestInstallation(oneMinuteAgo);

    boolean result =
        vercelUsageReportSubmissionSvcImpl.isValidToProcess(
            validReport, recentlyDeletedInstallation);

    assertFalse(result);
  }

  @Test
  void isValidToProcess_installationNotDeleted_returnsTrue() {
    // Test that non-deleted installations are valid for processing
    setupTimeBasedMocks();

    OffsetDateTime currentEod = baseInstant.atOffset(ZoneOffset.UTC);
    VercelUsageReport validReport = createTestUsageReportWithEod(Status.NEW, currentEod);
    VercelInstallation activeInstallation = createTestInstallation(null); // null deletedAt

    boolean result =
        vercelUsageReportSubmissionSvcImpl.isValidToProcess(validReport, activeInstallation);

    assertTrue(result);
  }

  @Test
  void isValidToProcess_failedStatusWithinWindow_returnsTrue() {
    // Test that FAILED status reports within the time window are valid for retry
    setupTimeBasedMocks();

    OffsetDateTime oneHourAgo = baseInstant.minus(Duration.ofHours(1)).atOffset(ZoneOffset.UTC);
    VercelUsageReport failedReport = createTestUsageReportWithEod(Status.FAILED, oneHourAgo);
    VercelInstallation installation = createTestInstallation(null);

    boolean result =
        vercelUsageReportSubmissionSvcImpl.isValidToProcess(failedReport, installation);

    assertTrue(result);
  }

  @Test
  void isValidToProcess_processingStatusWithinWindow_returnsTrue() {
    // Test that PROCESSING status reports within the time window are valid
    setupTimeBasedMocks();

    OffsetDateTime twoHoursAgo = baseInstant.minus(Duration.ofHours(2)).atOffset(ZoneOffset.UTC);
    VercelUsageReport processingReport =
        createTestUsageReportWithEod(Status.PROCESSING, twoHoursAgo);
    VercelInstallation installation = createTestInstallation(null);

    boolean result =
        vercelUsageReportSubmissionSvcImpl.isValidToProcess(processingReport, installation);

    assertTrue(result);
  }

  /** Helper method to create a test VercelUsageReport with specified status and EOD timestamp. */
  private VercelUsageReport createTestUsageReportWithEod(Status status, OffsetDateTime eod) {
    return createTestUsageReportWithEodAndCreated(status, eod, baseDate);
  }

  /**
   * Helper method to create a test VercelUsageReport with specified status, EOD timestamp, and
   * created date.
   */
  private VercelUsageReport createTestUsageReportWithEodAndCreated(
      Status status, OffsetDateTime eod, Date created) {
    SubmitBillingDataRequest request = createTestSubmitBillingDataRequest(eod);

    return VercelUsageReport.builder()
        .id(new ObjectId())
        .orgId(new ObjectId())
        .creditId(new ObjectId())
        .vercelInstallationId("test-installation-id")
        .status(status)
        .created(created)
        .startDate(baseDate)
        .endDate(baseDate)
        .billedAmountCents(1000L)
        .reportedAmountCents(1000L)
        .request(request)
        .build();
  }

  /** Helper method to create a test SubmitBillingDataRequest with specified EOD. */
  private SubmitBillingDataRequest createTestSubmitBillingDataRequest(OffsetDateTime eod) {
    VercelBillingPeriod period =
        VercelBillingPeriod.builder().start(eod.minusDays(15)).end(eod.plusDays(15)).build();

    SubmitBillingDataRequestBilling billing =
        SubmitBillingDataRequestBilling.builder().items(List.of()).discounts(List.of()).build();

    SubmitBillingDataRequestUsage usage =
        SubmitBillingDataRequestUsage.builder()
            .name("Atlas Usage")
            .type(SubmitBillingDataRequestUsageType.INTERVAL)
            .units("requests")
            .dayValue(BigDecimal.valueOf(10.00))
            .periodValue(BigDecimal.valueOf(100.00))
            .build();

    return SubmitBillingDataRequest.builder()
        .timestamp(eod)
        .eod(eod)
        .period(period)
        .billing(billing)
        .usage(List.of(usage))
        .build();
  }

  /** Helper method to create a test VercelInstallation with specified deletion date. */
  private VercelInstallation createTestInstallation(LocalDateTime deletedAt) {
    return VercelInstallation.builder()
        .id(new ObjectId())
        .installationId("test-installation-id")
        .orgId(new ObjectId())
        .accessToken("encrypted-token")
        .tokenType("Bearer")
        .installationUrl("https://test.vercel.app")
        .createdAt(baseDateTime)
        .updatedAt(baseDateTime)
        .deletedAt(deletedAt)
        .build();
  }
}
