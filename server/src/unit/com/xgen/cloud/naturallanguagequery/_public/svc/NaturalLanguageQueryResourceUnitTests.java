package com.xgen.cloud.naturallanguagequery._public.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.ratelimit._public.svc.RateLimitSvc;
import com.xgen.cloud.naturallanguagequery._public.model.ApiMockDataSchemaRequestView;
import com.xgen.cloud.naturallanguagequery._public.model.ApiMockDataSchemaResponseView;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.res.NaturalLanguageQueryResource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Note: There are tests for the other endpoints in {@link
 * com.xgen.svc.mms.api.res.ApiNaturalLanguageQueryResourceUnitTests} but they need to be moved from
 * the older monolithic folder structure
 */
@ExtendWith(MockitoExtension.class)
public class NaturalLanguageQueryResourceUnitTests {
  @Mock private NaturalLanguageQuerySvc naturalLanguageQuerySvc;
  @Mock private AppSettings appSettings;
  @Mock private RateLimitSvc rateLimitSvc;
  @Mock private SegmentEventSvc segmentEventSvc;

  @InjectMocks private NaturalLanguageQueryResource naturalLanguageQueryResource;

  @BeforeEach
  public void setUp() throws Exception {
    MockitoAnnotations.openMocks(this);

    naturalLanguageQueryResource =
        new NaturalLanguageQueryResource(
            appSettings, naturalLanguageQuerySvc, rateLimitSvc, segmentEventSvc);
  }

  @Test
  public void testGenerateMockDataSchema_enabledDataExplorerGenAi_ok() {
    final var a = newMockArgs();
    try (var ignored = withGenAiFeatureFlag(true)) {
      when(a.auditInfo.getAppUserId()).thenReturn(new ObjectId());
      when(rateLimitSvc.isPermitted(
              any(String.class), any(Integer.class), any(Long.class), any(Long.class)))
          .thenReturn(true);
      when(naturalLanguageQuerySvc.generateMockDataSchema(
              any(), any(), any(), any(), any(), any(), any()))
          .thenReturn(mock(ApiMockDataSchemaResponseView.class));

      final Response response =
          naturalLanguageQueryResource.mockDataSchema(
              a.httpRequest,
              a.auditInfo,
              a.groupId,
              a.requestId,
              a.envelope,
              a.compassVersion,
              a.requestView);
      assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());
    }
  }

  @Test
  public void testGenerateMockDataSchema_disabledDataExplorerGenAi_notFound() {
    final var a = newMockArgs();
    try (var ignored = withGenAiFeatureFlag(false)) {
      final Response response =
          naturalLanguageQueryResource.mockDataSchema(
              a.httpRequest,
              a.auditInfo,
              a.groupId,
              a.requestId,
              a.envelope,
              a.compassVersion,
              a.requestView);
      assertEquals(Response.Status.NOT_FOUND.getStatusCode(), response.getStatus());
    }
  }

  @Test
  public void testGenerateMockDataSchema_naturalLanguageQuerySvcThrows_internalServerError() {
    final var a = newMockArgs();
    try (var ignored = withGenAiFeatureFlag(true)) {
      when(a.auditInfo.getAppUserId()).thenReturn(new ObjectId());
      when(rateLimitSvc.isPermitted(
              any(String.class), any(Integer.class), any(Long.class), any(Long.class)))
          .thenReturn(true);
      when(naturalLanguageQuerySvc.generateMockDataSchema(
              any(), any(), any(), any(), any(), any(), any()))
          .thenThrow(ApiErrorCode.UNEXPECTED_ERROR.exception(a.envelope));

      final WebApplicationException ex =
          assertThrows(
              WebApplicationException.class,
              () ->
                  naturalLanguageQueryResource.mockDataSchema(
                      a.httpRequest,
                      a.auditInfo,
                      a.groupId,
                      a.requestId,
                      a.envelope,
                      a.compassVersion,
                      a.requestView));

      assertEquals(
          Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), ex.getResponse().getStatus());
    }
  }

  @Test
  public void testGenerateMockDataSchema_rateLimited_tooManyRequests() {
    final var a = newMockArgs();
    try (var ignored = withGenAiFeatureFlag(true)) {

      when(a.auditInfo.getAppUserId()).thenReturn(new ObjectId());
      when(a.auditInfo.getRemoteAddr()).thenReturn("127.0.0.1");
      when(a.auditInfo.getUsername()).thenReturn("test-user");
      when(rateLimitSvc.isPermitted(
              any(String.class), any(Integer.class), any(Long.class), any(Long.class)))
          .thenReturn(false);

      final WebApplicationException ex =
          assertThrows(
              WebApplicationException.class,
              () ->
                  naturalLanguageQueryResource.mockDataSchema(
                      a.httpRequest,
                      a.auditInfo,
                      a.groupId,
                      a.requestId,
                      a.envelope,
                      a.compassVersion,
                      a.requestView));

      assertEquals(Response.Status.TOO_MANY_REQUESTS.getStatusCode(), ex.getResponse().getStatus());
    }
  }

  private static final class ResourceMockArgs {
    final String groupId = "some-group-id";
    final String requestId = "some-request-id";
    final Boolean envelope = true;
    final String compassVersion = "some-compass-version";
    final HttpServletRequest httpRequest = mock(HttpServletRequest.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);
    final ApiMockDataSchemaRequestView requestView = mock(ApiMockDataSchemaRequestView.class);
    final RequestParams requestParams = mock(RequestParams.class);
    final Organization org = mock(Organization.class);
    final Group group = mock(Group.class);
  }

  private ResourceMockArgs newMockArgs() {
    final var a = new ResourceMockArgs();
    when(a.httpRequest.getAttribute("requestParams")).thenReturn(a.requestParams);
    when(a.requestParams.getCurrentOrganization()).thenReturn(a.org);
    when(a.requestParams.getCurrentGroup()).thenReturn(a.group);
    return a;
  }

  private MockedStatic<FeatureFlagSvc> withGenAiFeatureFlag(final boolean enabled) {
    final MockedStatic<FeatureFlagSvc> mocked = mockStatic(FeatureFlagSvc.class);
    mocked
        .when(
            () ->
                FeatureFlagSvc.isFeatureFlagEnabled(
                    eq(FeatureFlag.ENABLE_DATA_EXPLORER_GEN_AI_FEATURES), any(), any(), any()))
        .thenReturn(enabled);
    return mocked;
  }
}
