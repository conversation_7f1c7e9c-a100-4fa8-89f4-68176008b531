package com.xgen.cloud.atm.deploymentimport._public.svc;

import static com.xgen.cloud.monitoring.topology._public.model.HostType.SHARD_PRIMARY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.atm.core._private.dao.ImportDeploymentDao;
import com.xgen.cloud.atm.core._public.config.AutomationSettings;
import com.xgen.cloud.atm.core._public.model.ImportDeployment;
import com.xgen.cloud.atm.core._public.svc.DeploymentBuilderSvc;
import com.xgen.cloud.atm.core._public.svc.LastAgentStatusSvc;
import com.xgen.cloud.atm.core._public.svc.LegacyAutomationAgentAuditSvc;
import com.xgen.cloud.atm.core._public.view.ImportDeploymentRequestView;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.brs.daemon._public.grid.svc.BackupAgentAuditSvc;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.AuthMechanism;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.BaseAgentConfig;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6;
import com.xgen.cloud.deployment._public.model.ProcessType;
import com.xgen.cloud.deployment._public.model.monitoring.ProcessMonitoringState;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.PartialHost;
import com.xgen.cloud.monitoring.topology._public.model.errors.HostException;
import com.xgen.cloud.monitoring.topology._public.svc.CanonicalHostSvc;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.svc.core.model.api.FormResponse;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;
import org.bson.BsonDocument;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

public class ImportDeploymentSvcUnitTests {
  private GroupDao groupDao;
  private UserSvc userSvc;
  private OrganizationDao organizationDao;

  private AutomationSettings automationSettings;
  private HostSvc hostSvc;
  private CanonicalHostSvc canonicalHostSvc;
  private HostClusterLifecycleSvc hostClusterLifecycleSvc;
  private BackupAgentAuditSvc backupAgentAuditSvc;
  private AutomationConfigPublishingSvc automationConfigPublishingSvc;
  private ImportDeploymentDao importDeploymentDao;
  private ImportRequestSvc importRequestSvc;
  private LegacyAutomationAgentAuditSvc automationAgentAuditSvc;
  private ImportDeploymentSvc importDeploymentSvc;
  private DeploymentDiscoverySvc deploymentDiscoverySvc;
  private DeploymentBuilderSvc deploymentBuilderSvc;
  private LastAgentStatusSvc lastAgentStatusSvc;
  private PerGroupLockManager perGroupLockManager;

  private Group group;
  private AppUser user;

  @BeforeEach
  public void setUp() throws Exception {
    group = new Group();
    group.setId(new ObjectId());

    user = new AppUser();
    user.setId(new ObjectId());

    automationSettings = mock(AutomationSettings.class);
    canonicalHostSvc = mock(CanonicalHostSvc.class);
    hostSvc = mock(HostSvc.class);
    automationConfigPublishingSvc = mock(AutomationConfigPublishingSvc.class);
    importDeploymentDao = mock(ImportDeploymentDao.class);
    hostClusterLifecycleSvc = mock(HostClusterLifecycleSvc.class);
    groupDao = mock(GroupDao.class);
    userSvc = mock(UserSvc.class);
    organizationDao = mock(OrganizationDao.class);
    deploymentBuilderSvc = mock(DeploymentBuilderSvc.class);
    deploymentDiscoverySvc = mock(DeploymentDiscoverySvc.class);
    perGroupLockManager = new PerGroupLockManager();
    importRequestSvc = mock(ImportRequestSvc.class);

    // Automation should be up
    when(deploymentDiscoverySvc.getAutomationAgents(group))
        .thenReturn(List.of(new DeploymentDiscoverySvc.AgentView("", true, true)));
    when(deploymentDiscoverySvc.getMonitoringAgents(any(), eq(group)))
        .thenReturn(List.of(new DeploymentDiscoverySvc.AgentView("", true, true)));

    FormResponse fr = new FormResponse(CommonErrorCode.NONE);
    when(hostClusterLifecycleSvc.addHost(eq(group), any(), any())).thenReturn(fr);

    importDeploymentSvc =
        new ImportDeploymentSvc(
            automationSettings,
            automationConfigPublishingSvc,
            importDeploymentDao,
            hostClusterLifecycleSvc,
            groupDao,
            userSvc,
            organizationDao,
            hostSvc,
            canonicalHostSvc,
            importRequestSvc,
            deploymentDiscoverySvc,
            deploymentBuilderSvc,
            lastAgentStatusSvc,
            perGroupLockManager,
            null);

    when(groupDao.findById(group.getId())).thenReturn(group);
    when(userSvc.findById(user.getId())).thenReturn(user);
  }

  @Test
  public void testSubmitRequestForItem_WithAutomationRestricted_Throws() throws Exception {
    final AutomationConfig config = AutomationConfig.getEmptyConfig(group.getId(), null, null);
    when(automationConfigPublishingSvc.findCurrentOrEmpty(group.getId(), user.getId()))
        .thenReturn(config);

    final ImportDeploymentRequestView request = new ImportDeploymentRequestView();

    SvcException svcException =
        assertThrows(
            SvcException.class,
            () -> importDeploymentSvc.submitImportDeploymentRequest(group, user, request));
    assertServiceExceptionOf(
        svcException, CommonErrorCode.INVALID_PARAMETER, "Missing or malformed seedHostport");
  }

  @Test
  public void testSubmitRequestForItem_ValidateHostport() throws Exception {
    final AutomationConfig config = AutomationConfig.getEmptyConfig(group.getId(), null, null);
    when(automationConfigPublishingSvc.findCurrentOrEmpty(group.getId(), user.getId()))
        .thenReturn(config);

    Stream.of(null, "", "notavalidhostport", "8080:AlsoNotValid")
        .forEach(
            hostname -> {
              final ImportDeploymentRequestView request = createDefaultRequestView();
              request.setSeedHostport(hostname);

              SvcException svcException =
                  assertThrows(
                      SvcException.class,
                      () ->
                          importDeploymentSvc.submitImportDeploymentRequest(group, user, request));

              assertServiceExceptionOf(
                  svcException,
                  CommonErrorCode.INVALID_PARAMETER,
                  "Missing or malformed seedHostport");
            });
  }

  @Test
  public void testSubmitRequestForItem_ValidateRequiredProcesses() throws Exception {
    final AutomationConfig config = AutomationConfig.getEmptyConfig(group.getId(), null, null);
    when(automationConfigPublishingSvc.findCurrentOrEmpty(group.getId(), user.getId()))
        .thenReturn(config);

    Stream.of(List.of("notavalidhostport"), List.of("8080:AlsoNotValid"), List.of(""), List.of())
        .forEach(
            hostSvc -> {
              final ImportDeploymentRequestView request = createDefaultRequestView();
              request.setRequiredProcesses(List.of("1"));

              SvcException svcException =
                  assertThrows(
                      SvcException.class,
                      () ->
                          importDeploymentSvc.submitImportDeploymentRequest(group, user, request));

              assertServiceExceptionOf(
                  svcException,
                  CommonErrorCode.INVALID_PARAMETER,
                  "Each string in requiredProcesses collection should be in form of"
                      + " <hostname:port>");
            });
  }

  @Test
  public void testSubmitRequestForItem_TimeoutsAreOutOfRange() throws Exception {
    final AutomationConfig config = AutomationConfig.getEmptyConfig(group.getId(), null, null);
    config.getDeployment().getTLS().isEnabled();
    when(automationConfigPublishingSvc.findCurrentOrEmpty(group.getId(), user.getId()))
        .thenReturn(config);

    Stream.of(-1, 59, 24 * 60 * 60 + 1)
        .forEach(
            time -> {
              final ImportDeploymentRequestView request = createDefaultRequestView();
              request.getTimeouts().setTimeoutAutomationImported(-1);

              SvcException svcException =
                  assertThrows(
                      SvcException.class,
                      () ->
                          importDeploymentSvc.submitImportDeploymentRequest(group, user, request));

              assertServiceExceptionOf(
                  svcException,
                  CommonErrorCode.INVALID_PARAMETER,
                  "Timeout should be in range of [60..86400] seconds. 1min is minimum and 24hours"
                      + " is maximum");
            });

    {
      final ImportDeploymentRequestView request = createDefaultRequestView();
      request.getTimeouts().setTimeoutSeedHostConnectionSec(60);
      request.getTimeouts().setTimeoutHostsDiscoverySec(60);
      request.getTimeouts().setTimeoutAutomationImported(60);
      request.getTimeouts().setTimeoutGoalStateSec(60);

      importDeploymentSvc.submitImportDeploymentRequest(group, user, request);
    }
  }

  @Test
  public void testSubmitRequestForItem_InProgressRequestWithSameSeedNode() throws Exception {
    final AutomationConfig config = AutomationConfig.getEmptyConfig(group.getId(), null, null);
    when(automationConfigPublishingSvc.findCurrentOrEmpty(group.getId(), user.getId()))
        .thenReturn(config);

    ImportDeployment mock = mock(ImportDeployment.class);
    when(mock.seedHostport()).thenReturn("conflicting_seednode:1234");
    when(importDeploymentDao.findActiveImportDeploymentsByGroupId(group.getId()))
        .thenReturn(List.of(mock));

    final ImportDeploymentRequestView request = createDefaultRequestView();
    request.setSeedHostport("conflicting_seednode:1234");

    SvcException svcException =
        assertThrows(
            SvcException.class,
            () -> importDeploymentSvc.submitImportDeploymentRequest(group, user, request));

    assertServiceExceptionOf(
        svcException,
        CommonErrorCode.VALIDATION_ERROR,
        "There is another import request enqueued with same seed host. If you want to amend the"
            + " previous one please cancel it first");
  }

  @Test
  public void testSubmitRequestForItem_InProgressRequestWithRequiredProcesses() throws Exception {
    final AutomationConfig config = AutomationConfig.getEmptyConfig(group.getId(), null, null);
    when(automationConfigPublishingSvc.findCurrentOrEmpty(group.getId(), user.getId()))
        .thenReturn(config);

    ImportDeployment mock = mock(ImportDeployment.class);
    when(mock.seedHostport()).thenReturn("conflicting_hostname:1234");
    when(mock.requiredProcesses()).thenReturn(List.of("process:4004", "conflicting_process:1234"));
    when(importDeploymentDao.findActiveImportDeploymentsByGroupId(group.getId()))
        .thenReturn(List.of(mock));

    final ImportDeploymentRequestView request = createDefaultRequestView();
    request.setRequiredProcesses(List.of("conflicting_process:1234", "process:7001"));

    SvcException svcException =
        assertThrows(
            SvcException.class,
            () -> importDeploymentSvc.submitImportDeploymentRequest(group, user, request));

    assertServiceExceptionOf(
        svcException,
        CommonErrorCode.VALIDATION_ERROR,
        "There is another import request enqueued having required processes. If you want to amend"
            + " the previous one please cancel it first");
  }

  @Test
  public void testHandleWork_SelectFirstWhenManyRequestsPerSameGroup() {
    final AppUser user2 = new AppUser();
    user2.setId(new ObjectId());

    when(userSvc.findById(user2.getId())).thenReturn(user2);

    final AutomationConfig config = AutomationConfig.getEmptyConfig(group.getId(), null, null);
    config.setMonitoringAgentTemplate(new BaseAgentConfig());
    config.setBackupAgentTemplate(new BaseAgentConfig());
    when(automationConfigPublishingSvc.findCurrentOrEmpty(group.getId(), user.getId()))
        .thenReturn(config);

    when(importDeploymentDao.findAllActiveImportDeployments())
        .thenReturn(
            List.of(
                new BuilderForTests()
                    .withGroupId(group.getId())
                    .withUserId(user.getId())
                    .withSeedHostport("host:1234")
                    .build(),
                new BuilderForTests()
                    .withGroupId(group.getId())
                    .withUserId(user2.getId())
                    .withSeedHostport("host:1234")
                    .build()));

    importDeploymentSvc.handleWork();

    verify(automationConfigPublishingSvc).findCurrentOrEmpty(eq(group.getId()), eq(user.getId()));
    verify(automationConfigPublishingSvc, never())
        .findCurrentOrEmpty(eq(group.getId()), eq(user2.getId()));
  }

  @Test
  public void testHandleWork_CurrentlyPending_TransitionToAwaitSeedHost() {
    final AutomationConfig config = AutomationConfig.getEmptyConfig(group.getId(), null, null);
    config.setMonitoringAgentTemplate(new BaseAgentConfig());
    config.setBackupAgentTemplate(new BaseAgentConfig());
    when(automationConfigPublishingSvc.findCurrentOrEmpty(group.getId(), user.getId()))
        .thenReturn(config);

    when(importDeploymentDao.findAllActiveImportDeployments())
        .thenReturn(
            List.of(
                new BuilderForTests()
                    .withGroupId(group.getId())
                    .withUserId(user.getId())
                    .withSeedHostport("host:1234")
                    .withAdminUsername("adminUsername")
                    .withAdminPassword("adminPassword")
                    .build()));

    importDeploymentSvc.handleWork();

    verify(automationConfigPublishingSvc).findCurrentOrEmpty(eq(group.getId()), eq(user.getId()));

    ArgumentCaptor<PartialHost> argPartialHost = ArgumentCaptor.forClass(PartialHost.class);
    verify(hostClusterLifecycleSvc).addHost(eq(group), argPartialHost.capture(), any());

    assertEquals("adminUsername", argPartialHost.getValue().getUsername());
    assertEquals("adminPassword", argPartialHost.getValue().getPassword());
  }

  // TIMEOUTS

  @Test
  public void testHandleWork_TimeoutsOnAwaitSeedHost() {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(new Date());
    calendar.add(Calendar.SECOND, -30);
    Date before30s = calendar.getTime();

    final ImportDeployment importDeployment =
        new BuilderForTests()
            .withGroupId(group.getId())
            .withUserId(user.getId())
            .withCreatedAt(before30s)
            .withState(ImportDeployment.State.AWAIT_SEED_HOST_CONNECTED)
            .withTimeouts(0, 0, 0, 0)
            .build();

    when(automationSettings.getAutomationImportDeploymentTimeoutSeedHostConnectionSec())
        .thenReturn(18);
    when(importDeploymentDao.findAllActiveImportDeployments())
        .thenReturn(List.of(importDeployment));
    importDeploymentSvc.handleWork();

    ArgumentCaptor<Bson> arg1 = ArgumentCaptor.forClass(Bson.class);
    ArgumentCaptor<Bson> arg2 = ArgumentCaptor.forClass(Bson.class);
    verify(importDeploymentDao)
        .updateSelectively(eq(importDeployment.id()), arg1.capture(), arg2.capture());

    BsonDocument aaa1 = arg1.getValue().toBsonDocument();

    assertEquals("FAILED", aaa1.getDocument("$set").getString("state").getValue());

    BsonDocument bbb1 = arg2.getValue().toBsonDocument();

    assertEquals(
        "Operation timeouted on step: AWAIT_SEED_HOST_CONNECTED",
        bbb1.getDocument("$set").getString("errorMessage").getValue());
  }

  @Test
  public void testHandleWork_CurrentlyOnAwaitSeedHost_FailedHostIdShouldBePresent() {
    final ImportDeployment importDeployment =
        new BuilderForTests()
            .withGroupId(group.getId())
            .withUserId(user.getId())
            .withState(ImportDeployment.State.AWAIT_SEED_HOST_CONNECTED)
            .withHostId(null)
            .build();

    when(automationSettings.getAutomationImportDeploymentTimeoutSeedHostConnectionSec())
        .thenReturn(360);
    when(importDeploymentDao.findAllActiveImportDeployments())
        .thenReturn(List.of(importDeployment));

    Exception exception =
        assertThrows(IllegalStateException.class, () -> importDeploymentSvc.handleWork());

    assertEquals("importDeployment.hostId is null", exception.getMessage());

    ArgumentCaptor<Bson> arg1 = ArgumentCaptor.forClass(Bson.class);
    ArgumentCaptor<Bson> arg2 = ArgumentCaptor.forClass(Bson.class);
    verify(importDeploymentDao)
        .updateSelectively(eq(importDeployment.id()), arg1.capture(), arg2.capture());

    BsonDocument aaa1 = arg1.getValue().toBsonDocument();
    assertEquals("FAILED", aaa1.getDocument("$set").getString("state").getValue());

    BsonDocument bbb1 = arg2.getValue().toBsonDocument();
    assertEquals(
        "Import process failed transitioning to AWAIT_HOSTS_DISCOVERED state",
        bbb1.getDocument("$set").getString("errorMessage").getValue());
  }

  @Test
  public void testHandleWork_CurrentlyOnAwaitSeedHost_NoHostsDetected() {
    final ImportDeployment importDeployment =
        new BuilderForTests()
            .withGroupId(group.getId())
            .withUserId(user.getId())
            .withState(ImportDeployment.State.AWAIT_SEED_HOST_CONNECTED)
            .withHostId("testHostId")
            .build();

    when(automationSettings.getAutomationImportDeploymentTimeoutSeedHostConnectionSec())
        .thenReturn(360);
    when(importDeploymentDao.findAllActiveImportDeployments())
        .thenReturn(List.of(importDeployment));

    importDeploymentSvc.handleWork();

    verify(importDeploymentDao, never()).updateSelectively(eq(importDeployment.id()), any(), any());
  }

  @Test
  public void testHandleWork_CurrentlyOnAwaitSeedHost_WithHostEnabled_NoPings() {
    final ImportDeployment importDeployment =
        new BuilderForTests()
            .withGroupId(group.getId())
            .withUserId(user.getId())
            .withState(ImportDeployment.State.AWAIT_SEED_HOST_CONNECTED)
            .withHostId("testHostId")
            .build();

    when(automationSettings.getAutomationImportDeploymentTimeoutSeedHostConnectionSec())
        .thenReturn(360);
    when(importDeploymentDao.findAllActiveImportDeployments())
        .thenReturn(List.of(importDeployment));

    final Host host = new Host();
    host.setEnabled(true);
    host.setId("prefHostId");
    when(hostSvc.findHostById(eq("testHostId"), any())).thenReturn(host);

    Host preferredHost = new Host();
    preferredHost.setEnabled(true);
    preferredHost.setLastPing(null);

    when(hostSvc.findHostById(eq("prefHostId"), any())).thenReturn(preferredHost);

    importDeploymentSvc.handleWork();

    verify(hostSvc).findHostById(eq("prefHostId"), any()); // Mapping via preferred host occurred
    verify(importDeploymentDao, never()).updateSelectively(eq(importDeployment.id()), any(), any());
  }

  @Test
  public void testHandleWork_CurrentlyOnAwaitSeedHost_WithHostEnabled_NoHostType() {
    final ImportDeployment importDeployment =
        new BuilderForTests()
            .withGroupId(group.getId())
            .withUserId(user.getId())
            .withState(ImportDeployment.State.AWAIT_SEED_HOST_CONNECTED)
            .withHostId("testHostId")
            .build();

    when(automationSettings.getAutomationImportDeploymentTimeoutSeedHostConnectionSec())
        .thenReturn(360);
    when(importDeploymentDao.findAllActiveImportDeployments())
        .thenReturn(List.of(importDeployment));

    Host host = new Host();
    host.setEnabled(true);
    host.setId("prefHostId");
    when(hostSvc.findHostById(eq("testHostId"), any())).thenReturn(host);

    Host preferredHost = new Host();
    preferredHost.setEnabled(true);
    preferredHost.setLastPing(new Date());

    when(hostSvc.findHostById(eq("prefHostId"), any())).thenReturn(preferredHost);

    importDeploymentSvc.handleWork();

    verify(hostSvc).findHostById(eq("prefHostId"), any()); // Mapping via preferred host occurred
    verify(importDeploymentDao, never()).updateSelectively(eq(importDeployment.id()), any(), any());
  }

  @Test
  public void testHandleWork_CurrentlyOnAwaitSeedHost_WithHostEnabled_PingFound() {
    final ImportDeployment importDeployment =
        new BuilderForTests()
            .withGroupId(group.getId())
            .withUserId(user.getId())
            .withState(ImportDeployment.State.AWAIT_SEED_HOST_CONNECTED)
            .withHostId("testHostId")
            .build();

    when(automationSettings.getAutomationImportDeploymentTimeoutSeedHostConnectionSec())
        .thenReturn(360);
    when(importDeploymentDao.findAllActiveImportDeployments())
        .thenReturn(List.of(importDeployment));

    Host host = new Host();
    host.setEnabled(true);
    host.setId("prefHostId");
    when(hostSvc.findHostById(eq("testHostId"), any())).thenReturn(host);

    ObjectId clusterId = new ObjectId();

    Host preferredHost = new Host();
    preferredHost.setEnabled(true);
    preferredHost.setLastPing(new Date());
    preferredHost.setHostTypes(List.of(SHARD_PRIMARY.getCode()));
    preferredHost.setParentClusterId(clusterId);

    when(hostSvc.findHostById(eq("prefHostId"), any())).thenReturn(preferredHost);

    importDeploymentSvc.handleWork();

    ArgumentCaptor<Bson> arg1 = ArgumentCaptor.forClass(Bson.class);
    ArgumentCaptor<Bson> arg2 = ArgumentCaptor.forClass(Bson.class);
    verify(importDeploymentDao)
        .updateSelectively(eq(importDeployment.id()), arg1.capture(), arg2.capture());

    BsonDocument aaa1 = arg1.getValue().toBsonDocument();
    assertEquals("AWAIT_HOSTS_DISCOVERED", aaa1.getDocument("$set").getString("state").getValue());

    BsonDocument bbb1 = arg2.getValue().toBsonDocument();
    assertEquals(
        clusterId.toString(), bbb1.getDocument("$set").getString("deploymentItemId").getValue());
  }

  // END OF TIMEOUTS

  @Test
  public void testIsValidHostnamePort() {
    assertTrue(ImportDeploymentSvc.isValidHostnamePort("host:1234"));
    assertTrue(ImportDeploymentSvc.isValidHostnamePort("127.0.0.1:27017"));
    assertTrue(ImportDeploymentSvc.isValidHostnamePort("localhost:27017"));
    assertFalse(ImportDeploymentSvc.isValidHostnamePort("hostonly"));
    assertFalse(ImportDeploymentSvc.isValidHostnamePort("host:"));
    assertFalse(ImportDeploymentSvc.isValidHostnamePort(":1234"));
    assertFalse(ImportDeploymentSvc.isValidHostnamePort(null));
    assertFalse(ImportDeploymentSvc.isValidHostnamePort(""));
  }

  @Test
  public void testExtractHostnameAndPort() {
    var pair = ImportDeploymentSvc.extractHostnameAndPort("host:1234");
    assertEquals("host", pair.getLeft());
    assertEquals(1234, pair.getRight());
    assertEquals(null, ImportDeploymentSvc.extractHostnameAndPort("invalid"));
  }

  @Test
  public void testCancelImportRequest_wrongState() throws HostException {
    ObjectId importDeploymentId = new ObjectId();
    when(importDeploymentDao.find(importDeploymentId))
        .thenReturn(
            Optional.of(new BuilderForTests().withState(ImportDeployment.State.CANCELLED).build()));

    IllegalStateException illegalStateException =
        assertThrows(
            IllegalStateException.class,
            () -> importDeploymentSvc.cancelImportRequest(importDeploymentId.toString(), group));
    assertEquals(
        "ImportDeployment is in finished state. Can't cancel", illegalStateException.getMessage());
  }

  @Test
  public void testCancelImportRequest() throws HostException {
    ObjectId importDeploymentId = new ObjectId();
    ObjectId importRqId = new ObjectId();
    when(importDeploymentDao.find(importDeploymentId))
        .thenReturn(
            Optional.of(
                new BuilderForTests()
                    .withState(ImportDeployment.State.AWAIT_GOAL_STATE)
                    .withImportRequestId(importRqId)
                    .withHostId("hostId")
                    .build()));

    importDeploymentSvc.cancelImportRequest(importDeploymentId.toString(), group);

    verify(hostClusterLifecycleSvc).deleteHost(eq("hostId"), any(), any());
    verify(importRequestSvc).deleteRequest(any(), eq(importRqId));
  }

  @Test
  public void testCleanupImportRequest_wrongState() throws HostException {
    ObjectId importDeploymentId = new ObjectId();
    when(importDeploymentDao.find(importDeploymentId))
        .thenReturn(
            Optional.of(
                new BuilderForTests().withState(ImportDeployment.State.AWAIT_GOAL_STATE).build()));

    IllegalStateException illegalStateException =
        assertThrows(
            IllegalStateException.class,
            () -> importDeploymentSvc.cleanupImportRequest(importDeploymentId.toString(), group));
    assertEquals(
        "ImportDeployment is not in FAILED state. Can't cleanup",
        illegalStateException.getMessage());
  }

  @Test
  public void testCleanupImportRequest() throws HostException {
    ObjectId importDeploymentId = new ObjectId();
    ObjectId importRqId = new ObjectId();
    when(importDeploymentDao.find(importDeploymentId))
        .thenReturn(
            Optional.of(
                new BuilderForTests()
                    .withState(ImportDeployment.State.FAILED)
                    .withImportRequestId(importRqId)
                    .withHostId("hostId")
                    .build()));

    importDeploymentSvc.cleanupImportRequest(importDeploymentId.toString(), group);

    verify(hostClusterLifecycleSvc).deleteHost(eq("hostId"), any(), any());
    verify(importRequestSvc).deleteRequest(any(), eq(importRqId));
  }

  @Test
  public void testPruneImportDeploymentRequests() {
    // This is finalized but too fresh
    ImportDeployment importDeployment1 =
        new BuilderForTests()
            .withState(ImportDeployment.State.SUCCESS)
            .withCreatedAt(Date.from(Instant.now().minus(88, ChronoUnit.DAYS)))
            .build();
    // This is old but not finalized
    ImportDeployment importDeployment2 =
        new BuilderForTests()
            .withState(ImportDeployment.State.AWAIT_GOAL_STATE)
            .withCreatedAt(Date.from(Instant.now().minus(92, ChronoUnit.DAYS)))
            .build();
    // This is both old enough and finalized
    ImportDeployment importDeployment3 =
        new BuilderForTests()
            .withState(ImportDeployment.State.SUCCESS)
            .withCreatedAt(Date.from(Instant.now().minus(92, ChronoUnit.DAYS)))
            .build();

    when(automationSettings.getAutomationImportDeploymentRetentionDays()).thenReturn(90);

    when(importDeploymentDao.findAll())
        .thenReturn(List.of(importDeployment1, importDeployment2, importDeployment3));

    importDeploymentSvc.pruneImportDeploymentRequests();

    verify(importDeploymentDao).deleteBulk(List.of(importDeployment3.id()));
  }

  private void assertServiceExceptionOf(
      SvcException svcException, ErrorCode expectedErrorCode, String expectedMessageParam) {
    assertEquals(expectedErrorCode, svcException.getErrorCode());
    assertEquals(expectedMessageParam, svcException.getMessageParams().get(0));
  }

  private ImportDeploymentRequestView buildStandaloneRequest(
      final String deploymentId, final Group group) {
    final ImportDeploymentRequestView request = new ImportDeploymentRequestView();

    final Deployment monitoredDeployment = new Deployment();
    final Process myStandalone = new Process();
    myStandalone.setHostname("example.com");
    myStandalone.setProcessType(ProcessType.MONGOD);
    myStandalone.setArgs2_6(new ProcessArguments2_6());
    myStandalone.getArgs().setPort(27017);

    final ProcessMonitoringState myStandaloneState = new ProcessMonitoringState();
    myStandaloneState.setHostId(deploymentId);
    myStandalone.setMonitoringState(myStandaloneState);
    monitoredDeployment.getProcesses().add(myStandalone);

    //    when(_deploymentBuilderSvc.buildDeployment(eq(group),
    // any())).thenReturn(monitoredDeployment);

    return request;
  }

  private static ImportDeploymentRequestView createDefaultRequestView() {
    final ImportDeploymentRequestView request = new ImportDeploymentRequestView();
    request.setSeedHostport("hostname:1234");
    request.setRequiredProcesses(List.of("hostOne:12345"));
    request.setAuthMechanism(AuthMechanism.GSSAPI);

    final ImportDeploymentRequestView.TimeoutsView timeouts =
        new ImportDeploymentRequestView.TimeoutsView();
    timeouts.setTimeoutAutomationImported(600);
    request.setTimeouts(timeouts);
    return request;
  }

  public static class BuilderForTests {
    protected ImportDeployment.State state;
    protected ObjectId groupId;
    protected ObjectId userId;
    protected String seedHostport;
    protected List<String> requiredProcesses;
    protected AuthMechanism authMechanism = AuthMechanism.MONGODB_CR;
    protected String adminUsername;
    protected String adminPassword;
    protected String adminDb;
    protected String saslServiceName;
    protected String adminKerberosKeytab;
    protected String adminLdapGroupDn;
    protected String caPath;
    protected String clusterCaPath;
    protected String adminPemKeyPath;
    protected String adminPemKeyPwd;
    protected ImportDeployment.Timeouts timeouts;
    protected Date createdAt;
    protected String hostId;
    protected ObjectId importRequestId;
    protected String deploymentItemId;

    public BuilderForTests() {}

    public BuilderForTests withState(final ImportDeployment.State state) {
      this.state = state;
      return this;
    }

    public BuilderForTests withGroupId(final ObjectId groupId) {
      this.groupId = groupId;
      return this;
    }

    public BuilderForTests withUserId(final ObjectId userId) {
      this.userId = userId;
      return this;
    }

    public BuilderForTests withSeedHostport(final String seedHostport) {
      this.seedHostport = seedHostport;
      return this;
    }

    public BuilderForTests withRequiredProcesses(final List<String> requiredProcesses) {
      this.requiredProcesses = requiredProcesses;
      return this;
    }

    public BuilderForTests withAuthMechanism(final AuthMechanism authMechanism) {
      this.authMechanism = authMechanism;
      return this;
    }

    public BuilderForTests withAdminUsername(final String adminUsername) {
      this.adminUsername = adminUsername;
      return this;
    }

    public BuilderForTests withAdminPassword(final String adminPassword) {
      this.adminPassword = adminPassword;
      return this;
    }

    public BuilderForTests withAdminDb(final String adminDb) {
      this.adminDb = adminDb;
      return this;
    }

    public BuilderForTests withSaslServiceName(final String saslServiceName) {
      this.saslServiceName = saslServiceName;
      return this;
    }

    public BuilderForTests withAdminKerberosKeytab(final String adminKerberosKeytab) {
      this.adminKerberosKeytab = adminKerberosKeytab;
      return this;
    }

    public BuilderForTests withAdminLdapGroupDn(final String adminLdapGroupDn) {
      this.adminLdapGroupDn = adminLdapGroupDn;
      return this;
    }

    public BuilderForTests withCaPath(final String caPath) {
      this.caPath = caPath;
      return this;
    }

    public BuilderForTests withAdminPemKeyPath(final String adminPemKeyPath) {
      this.adminPemKeyPath = adminPemKeyPath;
      return this;
    }

    public BuilderForTests withAdminPemKeyPwd(final String adminPemKeyPwd) {
      this.adminPemKeyPwd = adminPemKeyPwd;
      return this;
    }

    public BuilderForTests withTimeouts(
        final int timeoutSeedHostConnectionSec,
        final int timeoutHostsDiscoverySec,
        final int timeoutAutomationImported,
        final int timeoutGoalStateSec) {
      this.timeouts =
          new ImportDeployment.Timeouts(
              timeoutSeedHostConnectionSec,
              timeoutHostsDiscoverySec,
              timeoutAutomationImported,
              timeoutGoalStateSec);
      return this;
    }

    public BuilderForTests withCreatedAt(final Date createdAt) {
      this.createdAt = createdAt;
      return this;
    }

    public BuilderForTests withHostId(final String hostId) {
      this.hostId = hostId;
      return this;
    }

    public BuilderForTests withImportRequestId(final ObjectId importRequestId) {
      this.importRequestId = importRequestId;
      return this;
    }

    public BuilderForTests withDeploymentItemId(final String deploymentItemId) {
      this.deploymentItemId = deploymentItemId;
      return this;
    }

    public ImportDeployment build() {
      return new ImportDeployment(
          new ObjectId(),
          groupId,
          userId,
          createdAt != null ? createdAt : new Date(),
          state != null ? state : ImportDeployment.State.PENDING,
          hostId,
          importRequestId,
          deploymentItemId,
          timeouts == null ? new ImportDeployment.Timeouts(0, 0, 0, 0) : timeouts,
          seedHostport,
          requiredProcesses,
          authMechanism,
          adminUsername,
          adminPassword,
          adminDb,
          saslServiceName,
          adminKerberosKeytab,
          adminLdapGroupDn,
          caPath,
          clusterCaPath,
          adminPemKeyPath,
          adminPemKeyPwd,
          Collections.emptyList(),
          null);
    }
  }
}
