load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*Tests.java"]),
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/authn",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/authn",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/federation",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/dataexfiltrationprevention",
        "//server/src/main/com/xgen/cloud/nds/dataexfiltrationprevention/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/dataexfiltrationprevention/_private/model",
        "//server/src/main/com/xgen/cloud/nds/dataexfiltrationprevention/_private/settings",
        "//server/src/main/com/xgen/cloud/nds/dataexfiltrationprevention/_private/svc",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/gcp/_public/model",
        "//server/src/main/com/xgen/cloud/nds/pointsofpresence/_public/exception",
        "//server/src/main/com/xgen/cloud/nds/pointsofpresence/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/services/authn/_public/proto:java_grpc",
        "//server/src/main/com/xgen/svc/mms/util/http",
        "//server/src/test/com/xgen/testlib/junit5/extensions",
        "//server/src/unit/com/xgen/cloud/nds/cloudprovider/_public/model:cloudProviderTestUtil",
        "//server/src/unit/com/xgen/cloud/nds/dataexfiltrationprevention/testutils:util",
        "//server/src/unit/com/xgen/cloud/nds/project/_public/model:commonTestUtil",
        "//server/src/unit/com/xgen/svc/nds/model",
        "@maven//:com_amazonaws_aws_java_sdk_ec2",
        "@maven//:io_prometheus_simpleclient",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_junit_jupiter_junit_jupiter_params",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
        "@maven//:software_amazon_awssdk_ec2",
    ],
)
