package com.xgen.cloud.nds.dataexfiltrationprevention._private.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.common.util._public.util.PojoTestUtils;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.bson.BsonArray;
import org.bson.BsonDateTime;
import org.bson.BsonDocument;
import org.bson.BsonInt32;
import org.bson.BsonInt64;
import org.bson.BsonObjectId;
import org.bson.BsonString;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import software.amazon.awssdk.services.ec2.model.PrefixListState;

public class AWSDataExfiltrationPrefixListUnitTests {
  static final PojoTestUtils<AWSDataExfiltrationPrefixList> bsonMapper =
      new PojoTestUtils<>(AWSDataExfiltrationPrefixList.class);

  @Test
  public void testAWSDataExfiltrationPrefixListSerialization() {
    // Given
    final ObjectId id = new ObjectId();
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    final String awsPrefixListId = "pl-0123456789abcdef0";
    final long version = 1L;
    final String name = "test-prefix-list";
    final String addressFamily = "IPv4";
    final String arn = "arn:aws:ec2:us-east-1:************:prefix-list/pl-0123456789abcdef0";
    final int maxEntries = 100;
    final PrefixListState state = PrefixListState.CREATE_COMPLETE;
    final Date lastModifiedDate = new Date();

    final Set<PrefixListEntry> entries =
        Set.of(
            new PrefixListEntry("10.0.0.0/16", "Test CIDR block 1"),
            new PrefixListEntry("***********/24", "Test CIDR block 2"));

    final List<AWSTag> tags = List.of(new AWSTag("Key1", "Value1"), new AWSTag("Key2", "Value2"));

    final AWSDataExfiltrationPrefixList prefixList =
        new AWSDataExfiltrationPrefixList(
            id,
            awsAccountId,
            region,
            awsPrefixListId,
            version,
            name,
            addressFamily,
            arn,
            maxEntries,
            state,
            entries,
            lastModifiedDate,
            tags);

    // When - Serialize to BSON
    final BsonDocument serialized = bsonMapper.encode(prefixList);

    // Then - Verify all fields are serialized correctly
    assertEquals(
        id, serialized.get(AWSDataExfiltrationPrefixList.FieldDefs.ID).asObjectId().getValue());
    assertEquals(
        awsAccountId,
        serialized
            .get(AWSDataExfiltrationPrefixList.FieldDefs.AWS_ACCOUNT_ID)
            .asObjectId()
            .getValue());
    assertEquals(
        region.name(),
        serialized.get(AWSDataExfiltrationPrefixList.FieldDefs.REGION).asString().getValue());
    assertEquals(
        awsPrefixListId,
        serialized
            .get(AWSDataExfiltrationPrefixList.FieldDefs.AWS_PREFIX_LIST_ID)
            .asString()
            .getValue());
    assertEquals(
        version,
        serialized.get(AWSDataExfiltrationPrefixList.FieldDefs.VERSION).asInt64().getValue());
    assertEquals(
        name, serialized.get(AWSDataExfiltrationPrefixList.FieldDefs.NAME).asString().getValue());
    assertEquals(
        addressFamily,
        serialized
            .get(AWSDataExfiltrationPrefixList.FieldDefs.ADDRESS_FAMILY)
            .asString()
            .getValue());
    assertEquals(
        arn, serialized.get(AWSDataExfiltrationPrefixList.FieldDefs.ARN).asString().getValue());
    assertEquals(
        maxEntries,
        serialized.get(AWSDataExfiltrationPrefixList.FieldDefs.MAX_ENTRIES).asInt32().getValue());
    assertEquals(
        state.name(),
        serialized.get(AWSDataExfiltrationPrefixList.FieldDefs.STATE).asString().getValue());
    assertEquals(
        lastModifiedDate.getTime(),
        serialized
            .get(AWSDataExfiltrationPrefixList.FieldDefs.LAST_MODIFIED_DATE)
            .asDateTime()
            .getValue());

    // Verify entries array
    final BsonArray entriesArray =
        serialized.get(AWSDataExfiltrationPrefixList.FieldDefs.ENTRIES).asArray();
    assertEquals(2, entriesArray.size());
    final var expectedEntries =
        List.of(
            new BsonDocument()
                .append(PrefixListEntry.FieldDefs.CIDR, new BsonString("10.0.0.0/16"))
                .append(PrefixListEntry.FieldDefs.DESCRIPTION, new BsonString("Test CIDR block 1")),
            new BsonDocument()
                .append(PrefixListEntry.FieldDefs.CIDR, new BsonString("***********/24"))
                .append(
                    PrefixListEntry.FieldDefs.DESCRIPTION, new BsonString("Test CIDR block 2")));
    expectedEntries.forEach(entry -> assertTrue(entriesArray.contains(entry)));

    // Verify tags array
    final BsonArray tagsArray =
        serialized.get(AWSDataExfiltrationPrefixList.FieldDefs.TAGS).asArray();
    assertEquals(2, tagsArray.size());

    final BsonDocument tag1 = tagsArray.get(0).asDocument();
    assertEquals("Key1", tag1.get(AWSTag.FieldDefs.KEY).asString().getValue());
    assertEquals("Value1", tag1.get(AWSTag.FieldDefs.VALUE).asString().getValue());

    final BsonDocument tag2 = tagsArray.get(1).asDocument();
    assertEquals("Key2", tag2.get(AWSTag.FieldDefs.KEY).asString().getValue());
    assertEquals("Value2", tag2.get(AWSTag.FieldDefs.VALUE).asString().getValue());

    assertEquals(
        AWSDataExfiltrationPrefixList.computeHash(entries, maxEntries),
        serialized.get(AWSDataExfiltrationPrefixList.FieldDefs.HASH).asInt32().getValue());
  }

  @Test
  public void testAWSDataExfiltrationPrefixListDeserialization() {
    // Given - Create BSON document manually
    final ObjectId id = new ObjectId();
    final ObjectId awsAccountId = new ObjectId();
    final Date lastModifiedDate = new Date();

    final BsonDocument document = new BsonDocument();
    document.put(AWSDataExfiltrationPrefixList.FieldDefs.ID, new BsonObjectId(id));
    document.put(
        AWSDataExfiltrationPrefixList.FieldDefs.AWS_ACCOUNT_ID, new BsonObjectId(awsAccountId));
    document.put(
        AWSDataExfiltrationPrefixList.FieldDefs.REGION,
        new BsonString(AWSRegionName.US_WEST_2.name()));
    document.put(
        AWSDataExfiltrationPrefixList.FieldDefs.AWS_PREFIX_LIST_ID,
        new BsonString("pl-9876543210fedcba0"));
    document.put(AWSDataExfiltrationPrefixList.FieldDefs.VERSION, new BsonInt64(2L));
    document.put(
        AWSDataExfiltrationPrefixList.FieldDefs.NAME,
        new BsonString("deserialization-test-prefix-list"));
    document.put(AWSDataExfiltrationPrefixList.FieldDefs.ADDRESS_FAMILY, new BsonString("IPv6"));
    document.put(
        AWSDataExfiltrationPrefixList.FieldDefs.ARN,
        new BsonString("arn:aws:ec2:us-west-2:************:prefix-list/pl-9876543210fedcba0"));
    document.put(AWSDataExfiltrationPrefixList.FieldDefs.MAX_ENTRIES, new BsonInt32(50));
    document.put(
        AWSDataExfiltrationPrefixList.FieldDefs.STATE,
        new BsonString(PrefixListState.MODIFY_COMPLETE.name()));
    document.put(
        AWSDataExfiltrationPrefixList.FieldDefs.LAST_MODIFIED_DATE,
        new BsonDateTime(lastModifiedDate.getTime()));

    // Create entries array
    final BsonArray entriesArray = new BsonArray();
    final BsonDocument entry1 = new BsonDocument();
    entry1.put(PrefixListEntry.FieldDefs.CIDR, new BsonString("2001:db8::/32"));
    entry1.put(PrefixListEntry.FieldDefs.DESCRIPTION, new BsonString("IPv6 test block"));
    entriesArray.add(entry1);
    document.put(AWSDataExfiltrationPrefixList.FieldDefs.ENTRIES, entriesArray);

    // Create tags array
    final BsonArray tagsArray = new BsonArray();
    final BsonDocument tag1 = new BsonDocument();
    tag1.put(AWSTag.FieldDefs.KEY, new BsonString("Key1"));
    tag1.put(AWSTag.FieldDefs.VALUE, new BsonString("Value1"));
    tagsArray.add(tag1);
    document.put(AWSDataExfiltrationPrefixList.FieldDefs.TAGS, tagsArray);

    // When - Deserialize from BSON
    final AWSDataExfiltrationPrefixList prefixList = bsonMapper.decode(document);

    // Then - Verify all fields are deserialized correctly
    assertEquals(id, prefixList.id());
    assertEquals(awsAccountId, prefixList.awsAccountId());
    assertEquals(AWSRegionName.US_WEST_2, prefixList.region());
    assertEquals("pl-9876543210fedcba0", prefixList.awsPrefixListId());
    assertEquals(2L, prefixList.version());
    assertEquals("deserialization-test-prefix-list", prefixList.name());
    assertEquals("IPv6", prefixList.addressFamily());
    assertEquals(
        "arn:aws:ec2:us-west-2:************:prefix-list/pl-9876543210fedcba0", prefixList.arn());
    assertEquals(50, prefixList.maxEntries());
    assertEquals(PrefixListState.MODIFY_COMPLETE, prefixList.state());
    assertEquals(lastModifiedDate, prefixList.lastModifiedDate());

    // Verify entries
    assertNotNull(prefixList.entries());
    assertEquals(1, prefixList.entries().size());

    final PrefixListEntry entry = prefixList.entries().stream().toList().get(0);
    assertEquals("2001:db8::/32", entry.cidr());
    assertEquals("IPv6 test block", entry.description());

    // Verify tags
    assertNotNull(prefixList.tags());
    assertEquals(1, prefixList.tags().size());

    final AWSTag tag = prefixList.tags().get(0);
    assertEquals("Key1", tag.key());
    assertEquals("Value1", tag.value());
  }

  @Test
  public void testRoundTripSerializationDeserialization() {
    // Given - Create a complete prefix list object
    final AWSDataExfiltrationPrefixList original = createTestPrefixList();

    // When - Serialize and then deserialize
    final BsonDocument serialized = bsonMapper.encode(original);
    final AWSDataExfiltrationPrefixList deserialized = bsonMapper.decode(serialized);

    // Then - Verify round-trip integrity
    assertEquals(original.id(), deserialized.id());
    assertEquals(original.awsAccountId(), deserialized.awsAccountId());
    assertEquals(original.region(), deserialized.region());
    assertEquals(original.awsPrefixListId(), deserialized.awsPrefixListId());
    assertEquals(original.version(), deserialized.version());
    assertEquals(original.name(), deserialized.name());
    assertEquals(original.addressFamily(), deserialized.addressFamily());
    assertEquals(original.arn(), deserialized.arn());
    assertEquals(original.maxEntries(), deserialized.maxEntries());
    assertEquals(original.state(), deserialized.state());
    assertEquals(original.lastModifiedDate(), deserialized.lastModifiedDate());
    assertEquals(original.entries(), deserialized.entries());

    // Verify tags
    assertEquals(original.tags().size(), deserialized.tags().size());
    for (int i = 0; i < original.tags().size(); i++) {
      final AWSTag originalTag = original.tags().get(i);
      final AWSTag deserializedTag = deserialized.tags().get(i);
      assertEquals(originalTag.key(), deserializedTag.key());
      assertEquals(originalTag.value(), deserializedTag.value());
    }

    assertEquals(original.hash(), deserialized.hash());
  }

  @Test
  public void testRoundTripSerializationDeserialization_NullHash() {
    final AWSDataExfiltrationPrefixList original =
        new AWSDataExfiltrationPrefixList(
            new ObjectId(),
            new ObjectId(),
            AWSRegionName.US_EAST_1,
            "pl-0123456789abcdef0",
            1L,
            "test-prefix-list",
            "IPv4",
            "arn:aws:ec2:us-east-1:************:prefix-list/pl-0123456789abcdef0",
            100,
            PrefixListState.CREATE_COMPLETE,
            Set.of(
                new PrefixListEntry("10.0.0.0/16", "Private network"),
                new PrefixListEntry("**********/12", "Private network 2"),
                new PrefixListEntry("***********/16", "Private network 3")),
            new Date(),
            List.of(new AWSTag("Key1", "Value1"), new AWSTag("Key2", "Value2")),
            null);

    // When - Serialize and then deserialize
    final BsonDocument serialized = bsonMapper.encode(original);
    final AWSDataExfiltrationPrefixList deserialized = bsonMapper.decode(serialized);

    assertEquals(original.hash(), deserialized.hash());
  }

  private AWSDataExfiltrationPrefixList createTestPrefixList() {
    return new AWSDataExfiltrationPrefixList(
        new ObjectId(),
        new ObjectId(),
        AWSRegionName.US_EAST_1,
        "pl-0123456789abcdef0",
        1L,
        "test-prefix-list",
        "IPv4",
        "arn:aws:ec2:us-east-1:************:prefix-list/pl-0123456789abcdef0",
        100,
        PrefixListState.CREATE_COMPLETE,
        Set.of(
            new PrefixListEntry("10.0.0.0/16", "Private network"),
            new PrefixListEntry("**********/12", "Private network 2"),
            new PrefixListEntry("***********/16", "Private network 3")),
        new Date(),
        List.of(new AWSTag("Key1", "Value1"), new AWSTag("Key2", "Value2")));
  }
}
