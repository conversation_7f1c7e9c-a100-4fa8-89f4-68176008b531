package com.xgen.cloud.nds.aws._public.svc;

import static com.xgen.cloud.nds.aws._public.svc.AWSApiPrefixListSvc.AWS_PREFIX_LIST_NAME_FILTER_KEY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.dataexfiltrationprevention._private.model.AWSDataExfiltrationPrefixList.Constants;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.services.ec2.Ec2Client;
import software.amazon.awssdk.services.ec2.model.AddPrefixListEntry;
import software.amazon.awssdk.services.ec2.model.CreateManagedPrefixListRequest;
import software.amazon.awssdk.services.ec2.model.CreateManagedPrefixListResponse;
import software.amazon.awssdk.services.ec2.model.DeleteManagedPrefixListRequest;
import software.amazon.awssdk.services.ec2.model.DeleteManagedPrefixListResponse;
import software.amazon.awssdk.services.ec2.model.DescribeManagedPrefixListsRequest;
import software.amazon.awssdk.services.ec2.model.DescribeManagedPrefixListsResponse;
import software.amazon.awssdk.services.ec2.model.Filter;
import software.amazon.awssdk.services.ec2.model.GetManagedPrefixListEntriesRequest;
import software.amazon.awssdk.services.ec2.model.GetManagedPrefixListEntriesResponse;
import software.amazon.awssdk.services.ec2.model.ManagedPrefixList;
import software.amazon.awssdk.services.ec2.model.ModifyManagedPrefixListRequest;
import software.amazon.awssdk.services.ec2.model.ModifyManagedPrefixListResponse;
import software.amazon.awssdk.services.ec2.model.PrefixListEntry;
import software.amazon.awssdk.services.ec2.model.PrefixListState;
import software.amazon.awssdk.services.ec2.model.RemovePrefixListEntry;
import software.amazon.awssdk.services.ec2.model.TagSpecification;

public class AWSApiPrefixListSvcUnitTests {
  private AWSApiSvcV2Framework awsApiSvcV2Framework;
  private AWSApiPrefixListSvc awsApiPrefixListSvc;

  @BeforeEach
  public void setUp() {
    awsApiSvcV2Framework = AWSApiSvcV2UnitTestHelper.getAWSApiSvcV2FrameworkSpy();
    awsApiPrefixListSvc = spy(new AWSApiPrefixListSvc(awsApiSvcV2Framework));
  }

  @Test
  public void testGetManagedPrefixListById_Success() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final Logger logger = mock(Logger.class);

    final ManagedPrefixList expectedPrefixList =
        ManagedPrefixList.builder()
            .prefixListId(prefixListId)
            .prefixListName(Constants.PREFIX_LIST_NAME)
            .state(PrefixListState.CREATE_COMPLETE)
            .addressFamily(Constants.ADDRESS_FAMILY)
            .maxEntries(150)
            .build();

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final DescribeManagedPrefixListsResponse response =
        DescribeManagedPrefixListsResponse.builder().prefixLists(expectedPrefixList).build();
    final ArgumentCaptor<DescribeManagedPrefixListsRequest> requestCaptor =
        ArgumentCaptor.forClass(DescribeManagedPrefixListsRequest.class);
    when(ec2Client.describeManagedPrefixLists(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final Optional<ManagedPrefixList> actualResult =
        awsApiPrefixListSvc.getManagedPrefixListById(
            awsAccountId, regionName, prefixListId, logger);

    // Then
    assertTrue(actualResult.isPresent());
    assertEquals(expectedPrefixList, actualResult.get());
    verify(ec2Client, times(1))
        .describeManagedPrefixLists(any(DescribeManagedPrefixListsRequest.class));

    final DescribeManagedPrefixListsRequest capturedRequest = requestCaptor.getValue();
    assertEquals(List.of(prefixListId), capturedRequest.prefixListIds());
  }

  @Test
  public void testGetManagedPrefixListById_EmptyResult() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final Logger logger = mock(Logger.class);

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final DescribeManagedPrefixListsResponse response =
        DescribeManagedPrefixListsResponse.builder().prefixLists(List.of()).build(); // Empty list
    final ArgumentCaptor<DescribeManagedPrefixListsRequest> requestCaptor =
        ArgumentCaptor.forClass(DescribeManagedPrefixListsRequest.class);
    when(ec2Client.describeManagedPrefixLists(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final Optional<ManagedPrefixList> actualResult =
        awsApiPrefixListSvc.getManagedPrefixListById(
            awsAccountId, regionName, prefixListId, logger);

    // Then
    assertFalse(actualResult.isPresent());
    verify(ec2Client, times(1))
        .describeManagedPrefixLists(any(DescribeManagedPrefixListsRequest.class));

    final DescribeManagedPrefixListsRequest capturedRequest = requestCaptor.getValue();
    assertEquals(List.of(prefixListId), capturedRequest.prefixListIds());
  }

  @Test
  public void testGetManagedPrefixListById_NullResult() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final Logger logger = mock(Logger.class);

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final DescribeManagedPrefixListsResponse response =
        DescribeManagedPrefixListsResponse.builder().prefixLists((ManagedPrefixList) null).build();
    final ArgumentCaptor<DescribeManagedPrefixListsRequest> requestCaptor =
        ArgumentCaptor.forClass(DescribeManagedPrefixListsRequest.class);
    when(ec2Client.describeManagedPrefixLists(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final Optional<ManagedPrefixList> actualResult =
        awsApiPrefixListSvc.getManagedPrefixListById(
            awsAccountId, regionName, prefixListId, logger);

    // Then
    assertFalse(actualResult.isPresent());
    verify(ec2Client, times(1))
        .describeManagedPrefixLists(any(DescribeManagedPrefixListsRequest.class));

    final DescribeManagedPrefixListsRequest capturedRequest = requestCaptor.getValue();
    assertEquals(List.of(prefixListId), capturedRequest.prefixListIds());
  }

  @Test
  public void testGetManagedPrefixListByName_Success() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListName = Constants.PREFIX_LIST_NAME;
    final String prefixListId = "pl-12345";
    final Logger logger = mock(Logger.class);

    final ManagedPrefixList expectedPrefixList =
        ManagedPrefixList.builder()
            .prefixListId(prefixListId)
            .prefixListName(prefixListName)
            .state(PrefixListState.CREATE_COMPLETE)
            .addressFamily(Constants.ADDRESS_FAMILY)
            .maxEntries(100)
            .build();

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final DescribeManagedPrefixListsResponse response =
        DescribeManagedPrefixListsResponse.builder().prefixLists(expectedPrefixList).build();
    final ArgumentCaptor<DescribeManagedPrefixListsRequest> requestCaptor =
        ArgumentCaptor.forClass(DescribeManagedPrefixListsRequest.class);
    when(ec2Client.describeManagedPrefixLists(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final Optional<ManagedPrefixList> actualResult =
        awsApiPrefixListSvc.getManagedPrefixListByName(
            awsAccountId, regionName, prefixListName, logger);

    // Then
    assertTrue(actualResult.isPresent());
    assertEquals(expectedPrefixList, actualResult.get());
    verify(ec2Client, times(1))
        .describeManagedPrefixLists(any(DescribeManagedPrefixListsRequest.class));

    final DescribeManagedPrefixListsRequest capturedRequest = requestCaptor.getValue();
    assertEquals(
        List.of(
            Filter.builder()
                .name(AWS_PREFIX_LIST_NAME_FILTER_KEY)
                .values(Constants.PREFIX_LIST_NAME)
                .build()),
        capturedRequest.filters());
  }

  @Test
  public void testCreateManagedPrefixList_Success() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListName = Constants.PREFIX_LIST_NAME;
    final String addressFamily = Constants.ADDRESS_FAMILY;
    final int maxEntries = 100;
    final Logger logger = mock(Logger.class);

    final ManagedPrefixList expectedPrefixList =
        ManagedPrefixList.builder()
            .prefixListId("pl-12345")
            .prefixListName(prefixListName)
            .state(PrefixListState.CREATE_COMPLETE)
            .addressFamily(addressFamily)
            .maxEntries(maxEntries)
            .build();

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final CreateManagedPrefixListResponse response =
        CreateManagedPrefixListResponse.builder().prefixList(expectedPrefixList).build();
    final ArgumentCaptor<CreateManagedPrefixListRequest> requestCaptor =
        ArgumentCaptor.forClass(CreateManagedPrefixListRequest.class);
    when(ec2Client.createManagedPrefixList(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final ManagedPrefixList actualResult =
        awsApiPrefixListSvc.createManagedPrefixList(
            awsAccountId,
            regionName,
            prefixListName,
            addressFamily,
            maxEntries,
            TagSpecification.builder().build(),
            logger);

    // Then
    assertEquals(expectedPrefixList, actualResult);
    verify(ec2Client, times(1)).createManagedPrefixList(any(CreateManagedPrefixListRequest.class));

    final CreateManagedPrefixListRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListName, capturedRequest.prefixListName());
    assertEquals(addressFamily, capturedRequest.addressFamily());
    assertEquals(maxEntries, capturedRequest.maxEntries());
  }

  @Test
  public void testCreateManagedPrefixList_ThrowsAWSApiException() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListName = "test-prefix-list";
    final String addressFamily = Constants.ADDRESS_FAMILY;
    final int maxEntries = 100;
    final Logger logger = mock(Logger.class);

    final AWSApiException expectedException =
        new AWSApiException("Test exception", NDSErrorCode.INTERNAL);

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final ArgumentCaptor<CreateManagedPrefixListRequest> requestCaptor =
        ArgumentCaptor.forClass(CreateManagedPrefixListRequest.class);
    when(ec2Client.createManagedPrefixList(requestCaptor.capture())).thenThrow(expectedException);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When & Then
    final AWSApiException actualException =
        assertThrows(
            AWSApiException.class,
            () ->
                awsApiPrefixListSvc.createManagedPrefixList(
                    awsAccountId,
                    regionName,
                    prefixListName,
                    addressFamily,
                    maxEntries,
                    TagSpecification.builder().build(),
                    logger));

    assertEquals(expectedException, actualException);
    verify(ec2Client, times(1)).createManagedPrefixList(any(CreateManagedPrefixListRequest.class));

    final CreateManagedPrefixListRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListName, capturedRequest.prefixListName());
    assertEquals(addressFamily, capturedRequest.addressFamily());
    assertEquals(maxEntries, capturedRequest.maxEntries());
  }

  @Test
  public void testDeleteManagedPrefixList_Success() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final Logger logger = mock(Logger.class);

    final ManagedPrefixList deletedPrefixList =
        ManagedPrefixList.builder().prefixListId(prefixListId).build();
    final DeleteManagedPrefixListResponse expectedResult =
        DeleteManagedPrefixListResponse.builder().prefixList(deletedPrefixList).build();

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final ArgumentCaptor<DeleteManagedPrefixListRequest> requestCaptor =
        ArgumentCaptor.forClass(DeleteManagedPrefixListRequest.class);
    when(ec2Client.deleteManagedPrefixList(requestCaptor.capture())).thenReturn(expectedResult);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final DeleteManagedPrefixListResponse actualResult =
        awsApiPrefixListSvc.deleteManagedPrefixListById(
            awsAccountId, regionName, prefixListId, logger);

    // Then
    assertEquals(expectedResult, actualResult);
    verify(ec2Client, times(1)).deleteManagedPrefixList(any(DeleteManagedPrefixListRequest.class));

    final DeleteManagedPrefixListRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
  }

  @Test
  public void testDeleteManagedPrefixListById_ThrowsAWSApiException() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final Logger logger = mock(Logger.class);

    final AWSApiException expectedException =
        new AWSApiException("Test exception", NDSErrorCode.INTERNAL);

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final ArgumentCaptor<DeleteManagedPrefixListRequest> requestCaptor =
        ArgumentCaptor.forClass(DeleteManagedPrefixListRequest.class);
    when(ec2Client.deleteManagedPrefixList(requestCaptor.capture())).thenThrow(expectedException);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When & Then
    final AWSApiException actualException =
        assertThrows(
            AWSApiException.class,
            () ->
                awsApiPrefixListSvc.deleteManagedPrefixListById(
                    awsAccountId, regionName, prefixListId, logger));

    assertEquals(expectedException, actualException);
    verify(ec2Client, times(1)).deleteManagedPrefixList(any(DeleteManagedPrefixListRequest.class));

    final DeleteManagedPrefixListRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
  }

  @Test
  public void testModifyManagedPrefixList_Success() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final long prefixListVersion = 2L;
    final Logger logger = mock(Logger.class);

    final List<AddPrefixListEntry> addEntries =
        List.of(
            AddPrefixListEntry.builder().cidr("10.0.0.0/16").description("Add entry 1").build(),
            AddPrefixListEntry.builder().cidr("***********/24").description("Add entry 2").build());

    final List<RemovePrefixListEntry> removeEntries =
        List.of(
            RemovePrefixListEntry.builder().cidr("**********/12").build(),
            RemovePrefixListEntry.builder().cidr("***********/24").build());

    final ManagedPrefixList modifiedPrefixList =
        ManagedPrefixList.builder()
            .prefixListId(prefixListId)
            .state(PrefixListState.MODIFY_COMPLETE)
            .build();

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final ModifyManagedPrefixListResponse response =
        ModifyManagedPrefixListResponse.builder().prefixList(modifiedPrefixList).build();
    final ArgumentCaptor<ModifyManagedPrefixListRequest> requestCaptor =
        ArgumentCaptor.forClass(ModifyManagedPrefixListRequest.class);
    when(ec2Client.modifyManagedPrefixList(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final ModifyManagedPrefixListResponse actualResult =
        awsApiPrefixListSvc.modifyManagedPrefixListEntries(
            awsAccountId,
            regionName,
            prefixListId,
            prefixListVersion,
            addEntries,
            removeEntries,
            logger);

    // Then
    assertEquals(response, actualResult);
    verify(ec2Client, times(1)).modifyManagedPrefixList(any(ModifyManagedPrefixListRequest.class));

    final ModifyManagedPrefixListRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
    assertEquals(prefixListVersion, capturedRequest.currentVersion());
    assertEquals(addEntries, capturedRequest.addEntries());
    assertEquals(removeEntries, capturedRequest.removeEntries());
  }

  @Test
  public void testModifyManagedPrefixList_EmptyLists() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final long prefixListVersion = 1L;
    final Logger logger = mock(Logger.class);

    final List<AddPrefixListEntry> addEntries = List.of(); // Empty
    final List<RemovePrefixListEntry> removeEntries = List.of(); // Empty

    final ManagedPrefixList modifiedPrefixList =
        ManagedPrefixList.builder()
            .prefixListId(prefixListId)
            .state(PrefixListState.MODIFY_COMPLETE)
            .build();

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final ModifyManagedPrefixListResponse response =
        ModifyManagedPrefixListResponse.builder().prefixList(modifiedPrefixList).build();
    final ArgumentCaptor<ModifyManagedPrefixListRequest> requestCaptor =
        ArgumentCaptor.forClass(ModifyManagedPrefixListRequest.class);
    when(ec2Client.modifyManagedPrefixList(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final ModifyManagedPrefixListResponse actualResult =
        awsApiPrefixListSvc.modifyManagedPrefixListEntries(
            awsAccountId,
            regionName,
            prefixListId,
            prefixListVersion,
            addEntries,
            removeEntries,
            logger);

    // Then
    assertEquals(response, actualResult);
    verify(ec2Client, times(1)).modifyManagedPrefixList(any(ModifyManagedPrefixListRequest.class));

    final ModifyManagedPrefixListRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
    assertEquals(prefixListVersion, capturedRequest.currentVersion());
    assertEquals(addEntries, capturedRequest.addEntries());
    assertEquals(removeEntries, capturedRequest.removeEntries());
  }

  @Test
  public void testModifyManagedPrefixList_ThrowsAWSApiException() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final long prefixListVersion = 3L;
    final Logger logger = mock(Logger.class);

    final List<AddPrefixListEntry> addEntries =
        List.of(
            AddPrefixListEntry.builder().cidr("10.0.0.0/16").description("Add entry 1").build());

    final List<RemovePrefixListEntry> removeEntries =
        List.of(RemovePrefixListEntry.builder().cidr("**********/12").build());

    final AWSApiException expectedException =
        new AWSApiException("Test exception", NDSErrorCode.INTERNAL);

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final ArgumentCaptor<ModifyManagedPrefixListRequest> requestCaptor =
        ArgumentCaptor.forClass(ModifyManagedPrefixListRequest.class);
    when(ec2Client.modifyManagedPrefixList(requestCaptor.capture())).thenThrow(expectedException);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When & Then
    final AWSApiException actualException =
        assertThrows(
            AWSApiException.class,
            () ->
                awsApiPrefixListSvc.modifyManagedPrefixListEntries(
                    awsAccountId,
                    regionName,
                    prefixListId,
                    prefixListVersion,
                    addEntries,
                    removeEntries,
                    logger));

    assertEquals(expectedException, actualException);
    verify(ec2Client, times(1)).modifyManagedPrefixList(any(ModifyManagedPrefixListRequest.class));

    final ModifyManagedPrefixListRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
    assertEquals(prefixListVersion, capturedRequest.currentVersion());
    assertEquals(addEntries, capturedRequest.addEntries());
    assertEquals(removeEntries, capturedRequest.removeEntries());
  }

  @Test
  public void testModifyManagedPrefixList_TruncatesEntriesOver100() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final Logger logger = LoggerFactory.getLogger(AwsApiSvcV2.class);

    // Create 150 add entries (more than the 100 limit)
    final List<AddPrefixListEntry> addEntries = new ArrayList<>();
    for (int i = 0; i < 150; i++) {
      addEntries.add(
          AddPrefixListEntry.builder()
              .cidr("10.0.0." + i + "/32")
              .description("Add entry " + i)
              .build());
    }

    // Create 120 remove entries (more than the 100 limit)
    final List<RemovePrefixListEntry> removeEntries = new ArrayList<>();
    for (int i = 0; i < 120; i++) {
      removeEntries.add(RemovePrefixListEntry.builder().cidr("192.168.0." + i + "/32").build());
    }

    final ManagedPrefixList modifiedPrefixList =
        ManagedPrefixList.builder()
            .prefixListId(prefixListId)
            .state(PrefixListState.MODIFY_COMPLETE)
            .build();

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final ModifyManagedPrefixListResponse response =
        ModifyManagedPrefixListResponse.builder().prefixList(modifiedPrefixList).build();
    final ArgumentCaptor<ModifyManagedPrefixListRequest> requestCaptor =
        ArgumentCaptor.forClass(ModifyManagedPrefixListRequest.class);
    when(ec2Client.modifyManagedPrefixList(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final long prefixListVersion = 5L;
    final ModifyManagedPrefixListResponse actualResult =
        awsApiPrefixListSvc.modifyManagedPrefixListEntries(
            awsAccountId,
            regionName,
            prefixListId,
            prefixListVersion,
            addEntries,
            removeEntries,
            logger);

    // Then
    assertEquals(response, actualResult);
    verify(ec2Client, times(1)).modifyManagedPrefixList(any(ModifyManagedPrefixListRequest.class));

    final ModifyManagedPrefixListRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
    assertEquals(prefixListVersion, capturedRequest.currentVersion());

    // Verify that entries were truncated to 100 each
    assertEquals(100, capturedRequest.addEntries().size());
    assertEquals(100, capturedRequest.removeEntries().size());

    // Verify that the first 100 entries are preserved
    assertEquals(addEntries.subList(0, 100), capturedRequest.addEntries());
    assertEquals(removeEntries.subList(0, 100), capturedRequest.removeEntries());
  }

  @Test
  public void testGetManagedPrefixListEntries_Success() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final Long targetVersion = 2L;
    final Logger logger = mock(Logger.class);

    final List<PrefixListEntry> expectedEntries =
        List.of(
            PrefixListEntry.builder().cidr("10.0.0.0/16").description("Entry 1").build(),
            PrefixListEntry.builder().cidr("***********/24").description("Entry 2").build(),
            PrefixListEntry.builder().cidr("**********/12").description("Entry 3").build());

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final GetManagedPrefixListEntriesResponse response =
        GetManagedPrefixListEntriesResponse.builder()
            .entries(expectedEntries)
            .nextToken(null) // No pagination
            .build();
    final ArgumentCaptor<GetManagedPrefixListEntriesRequest> requestCaptor =
        ArgumentCaptor.forClass(GetManagedPrefixListEntriesRequest.class);
    when(ec2Client.getManagedPrefixListEntries(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final List<PrefixListEntry> actualResult =
        awsApiPrefixListSvc.getManagedPrefixListEntries(
            awsAccountId, regionName, prefixListId, targetVersion, logger);

    // Then
    assertEquals(expectedEntries, actualResult);
    verify(ec2Client, times(1))
        .getManagedPrefixListEntries(any(GetManagedPrefixListEntriesRequest.class));

    final GetManagedPrefixListEntriesRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
    assertEquals(targetVersion, capturedRequest.targetVersion());
    assertEquals(100, capturedRequest.maxResults()); // MAX_PREFIX_LIST_ENTRIES_PER_REQUEST
  }

  @Test
  public void testGetManagedPrefixListEntries_EmptyResult() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final Logger logger = mock(Logger.class);

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final GetManagedPrefixListEntriesResponse response =
        GetManagedPrefixListEntriesResponse.builder()
            .entries(List.of()) // Empty list
            .nextToken(null)
            .build();
    final ArgumentCaptor<GetManagedPrefixListEntriesRequest> requestCaptor =
        ArgumentCaptor.forClass(GetManagedPrefixListEntriesRequest.class);
    when(ec2Client.getManagedPrefixListEntries(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final List<PrefixListEntry> actualResult =
        awsApiPrefixListSvc.getManagedPrefixListEntries(
            awsAccountId, regionName, prefixListId, null, logger);

    // Then
    assertTrue(actualResult.isEmpty());
    verify(ec2Client, times(1))
        .getManagedPrefixListEntries(any(GetManagedPrefixListEntriesRequest.class));

    final GetManagedPrefixListEntriesRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
    assertNull(capturedRequest.targetVersion());
    assertEquals(100, capturedRequest.maxResults());
  }

  @Test
  public void testGetManagedPrefixListEntries_WithPagination() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final Long targetVersion = 3L;
    final Logger logger = mock(Logger.class);

    // First page of entries
    final List<PrefixListEntry> firstPageEntries =
        List.of(
            PrefixListEntry.builder().cidr("10.0.0.0/16").description("Entry 1").build(),
            PrefixListEntry.builder().cidr("***********/24").description("Entry 2").build());

    // Second page of entries
    final List<PrefixListEntry> secondPageEntries =
        List.of(
            PrefixListEntry.builder().cidr("**********/12").description("Entry 3").build(),
            PrefixListEntry.builder().cidr("***********/24").description("Entry 4").build());

    final Ec2Client ec2Client = mock(Ec2Client.class);

    // First response with nextToken
    final GetManagedPrefixListEntriesResponse firstResponse =
        GetManagedPrefixListEntriesResponse.builder()
            .entries(firstPageEntries)
            .nextToken("next-token-123")
            .build();

    // Second response without nextToken (end of pagination)
    final GetManagedPrefixListEntriesResponse secondResponse =
        GetManagedPrefixListEntriesResponse.builder()
            .entries(secondPageEntries)
            .nextToken(null)
            .build();

    final ArgumentCaptor<GetManagedPrefixListEntriesRequest> requestCaptor =
        ArgumentCaptor.forClass(GetManagedPrefixListEntriesRequest.class);

    // Mock the two API calls
    when(ec2Client.getManagedPrefixListEntries(requestCaptor.capture()))
        .thenReturn(firstResponse)
        .thenReturn(secondResponse);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final List<PrefixListEntry> actualResult =
        awsApiPrefixListSvc.getManagedPrefixListEntries(
            awsAccountId, regionName, prefixListId, targetVersion, logger);

    // Then
    final List<PrefixListEntry> expectedAllEntries = new ArrayList<>();
    expectedAllEntries.addAll(firstPageEntries);
    expectedAllEntries.addAll(secondPageEntries);

    assertEquals(expectedAllEntries, actualResult);
    assertEquals(4, actualResult.size());

    // Verify two API calls were made (one for each page)
    verify(ec2Client, times(2))
        .getManagedPrefixListEntries(any(GetManagedPrefixListEntriesRequest.class));

    // Verify that both requests have the correct targetVersion
    final List<GetManagedPrefixListEntriesRequest> capturedRequests = requestCaptor.getAllValues();
    assertEquals(2, capturedRequests.size());
    assertEquals(targetVersion, capturedRequests.get(0).targetVersion());
    assertEquals(targetVersion, capturedRequests.get(1).targetVersion());
  }

  @Test
  public void testGetManagedPrefixListEntries_ThrowsAWSApiException() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final Long targetVersion = 1L;
    final Logger logger = mock(Logger.class);

    final AWSApiException expectedException =
        new AWSApiException("Test exception", NDSErrorCode.INTERNAL);

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final ArgumentCaptor<GetManagedPrefixListEntriesRequest> requestCaptor =
        ArgumentCaptor.forClass(GetManagedPrefixListEntriesRequest.class);
    when(ec2Client.getManagedPrefixListEntries(requestCaptor.capture()))
        .thenThrow(expectedException);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When & Then
    final AWSApiException actualException =
        assertThrows(
            AWSApiException.class,
            () ->
                awsApiPrefixListSvc.getManagedPrefixListEntries(
                    awsAccountId, regionName, prefixListId, targetVersion, logger));

    assertEquals(expectedException, actualException);
    verify(ec2Client, times(1))
        .getManagedPrefixListEntries(any(GetManagedPrefixListEntriesRequest.class));

    final GetManagedPrefixListEntriesRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
    assertEquals(targetVersion, capturedRequest.targetVersion());
    assertEquals(100, capturedRequest.maxResults());
  }

  @Test
  public void testGetManagedPrefixListEntries_WithNullTargetVersion() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_WEST_2;
    final String prefixListId = "pl-67890";
    final Logger logger = mock(Logger.class);

    final List<PrefixListEntry> expectedEntries =
        List.of(PrefixListEntry.builder().cidr("********/16").description("Test Entry").build());

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final GetManagedPrefixListEntriesResponse response =
        GetManagedPrefixListEntriesResponse.builder()
            .entries(expectedEntries)
            .nextToken(null)
            .build();
    final ArgumentCaptor<GetManagedPrefixListEntriesRequest> requestCaptor =
        ArgumentCaptor.forClass(GetManagedPrefixListEntriesRequest.class);
    when(ec2Client.getManagedPrefixListEntries(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final List<PrefixListEntry> actualResult =
        awsApiPrefixListSvc.getManagedPrefixListEntries(
            awsAccountId, regionName, prefixListId, null, logger);

    // Then
    assertEquals(expectedEntries, actualResult);
    verify(ec2Client, times(1))
        .getManagedPrefixListEntries(any(GetManagedPrefixListEntriesRequest.class));

    final GetManagedPrefixListEntriesRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
    assertNull(capturedRequest.targetVersion()); // Should be null when not specified
    assertEquals(100, capturedRequest.maxResults());
  }

  @Test
  public void testModifyManagedPrefixListMaxEntries_Success() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String prefixListId = "pl-12345";
    final int maxEntries = 200;
    final Logger logger = mock(Logger.class);

    final ManagedPrefixList modifiedPrefixList =
        ManagedPrefixList.builder()
            .prefixListId(prefixListId)
            .maxEntries(maxEntries)
            .state(PrefixListState.MODIFY_COMPLETE)
            .build();

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final ModifyManagedPrefixListResponse response =
        ModifyManagedPrefixListResponse.builder().prefixList(modifiedPrefixList).build();
    final ArgumentCaptor<ModifyManagedPrefixListRequest> requestCaptor =
        ArgumentCaptor.forClass(ModifyManagedPrefixListRequest.class);
    when(ec2Client.modifyManagedPrefixList(requestCaptor.capture())).thenReturn(response);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When
    final ModifyManagedPrefixListResponse actualResult =
        awsApiPrefixListSvc.modifyManagedPrefixListMaxEntries(
            awsAccountId, regionName, prefixListId, maxEntries, logger);

    // Then
    assertEquals(response, actualResult);
    verify(ec2Client, times(1)).modifyManagedPrefixList(any(ModifyManagedPrefixListRequest.class));

    final ModifyManagedPrefixListRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
    assertEquals(maxEntries, capturedRequest.maxEntries());
    assertNull(
        capturedRequest.currentVersion()); // Should be null for max entries only modification
    assertTrue(capturedRequest.addEntries().isEmpty()); // Should be empty for max entries only
    assertTrue(capturedRequest.removeEntries().isEmpty()); // Should be empty for max entries only
  }

  @Test
  public void testModifyManagedPrefixListMaxEntries_ThrowsAWSApiException() {
    // Given
    final ObjectId awsAccountId = new ObjectId();
    final AWSRegionName regionName = AWSRegionName.EU_WEST_1;
    final String prefixListId = "pl-error123";
    final int maxEntries = 300;
    final Logger logger = mock(Logger.class);

    final AWSApiException expectedException =
        new AWSApiException("Test exception", NDSErrorCode.INTERNAL);

    final Ec2Client ec2Client = mock(Ec2Client.class);
    final ArgumentCaptor<ModifyManagedPrefixListRequest> requestCaptor =
        ArgumentCaptor.forClass(ModifyManagedPrefixListRequest.class);
    when(ec2Client.modifyManagedPrefixList(requestCaptor.capture())).thenThrow(expectedException);
    doReturn(ec2Client).when(awsApiSvcV2Framework).getEc2Client(eq(awsAccountId), eq(regionName));

    // When & Then
    final AWSApiException actualException =
        assertThrows(
            AWSApiException.class,
            () ->
                awsApiPrefixListSvc.modifyManagedPrefixListMaxEntries(
                    awsAccountId, regionName, prefixListId, maxEntries, logger));

    assertEquals(expectedException, actualException);
    verify(ec2Client, times(1)).modifyManagedPrefixList(any(ModifyManagedPrefixListRequest.class));

    final ModifyManagedPrefixListRequest capturedRequest = requestCaptor.getValue();
    assertEquals(prefixListId, capturedRequest.prefixListId());
    assertEquals(maxEntries, capturedRequest.maxEntries());
    assertNull(capturedRequest.currentVersion());
    assertTrue(capturedRequest.addEntries().isEmpty());
    assertTrue(capturedRequest.removeEntries().isEmpty());
  }
}
