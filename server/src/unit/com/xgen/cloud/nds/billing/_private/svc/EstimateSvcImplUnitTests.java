package com.xgen.cloud.nds.billing._private.svc;

import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.monthlyToDailyCost;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.monthlyToHourlyCost;
import static com.xgen.cloud.billingplatform.model.sku._public.model.SKU.monthlyToHourlyCostPerGB;
import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static com.xgen.cloud.search.decoupled.config._public.svc.SearchDeploymentDescriptionValidationSvc.MIN_MONGODB_VERSION;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.amazonaws.services.ec2.model.VolumeType;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.billingplatform.model.sku._public.model.PricingConsumers;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuPricing;
import com.xgen.cloud.billingplatform.util.SkuUtils;
import com.xgen.cloud.brs.core._public.svc.BackupHostSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.brs._public.model.SnapshotSchedule;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSAutoScaling;
import com.xgen.cloud.nds.aws._public.model.autoscaling.ui.AWSAutoScalingView;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType.AzureStorageType;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType.PreferredStorageType;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.autoscaling.AzureAutoScaling;
import com.xgen.cloud.nds.azure._public.model.autoscaling.ui.AzureAutoScalingView;
import com.xgen.cloud.nds.billing._public.svc.EstimateSvc.ESTIMATED_FEE_FACTOR;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.HostnameScheme;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.free._public.model.FreeHardwareSpec;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.free._public.model.autoscaling.ui.FreeAutoScalingView;
import com.xgen.cloud.nds.gcp._public.model.GCPHardwareSpec;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceFamily;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.autoscaling.GCPAutoScaling;
import com.xgen.cloud.nds.gcp._public.model.autoscaling.ui.GCPAutoScalingView;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.InternalClusterRole;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.util.ClusterDescriptionValidationUtil;
import com.xgen.cloud.pricing._public.svc.SkuPricingSvc;
import com.xgen.cloud.search.decoupled.api._public.model.SearchInstanceSizeAPI;
import com.xgen.cloud.search.decoupled.config._public.model.SearchConfigModelTestFactory;
import com.xgen.cloud.search.decoupled.config._public.model.SearchDeploymentDescription;
import com.xgen.cloud.search.decoupled.config._public.model.aws.AWSSearchInstanceSize;
import com.xgen.cloud.search.decoupled.config._public.svc.SearchDeploymentDescriptionSvc;
import com.xgen.svc.mms.dao.billing.LineItemDao;
import com.xgen.svc.nds.model.ClusterDescriptionBuilderTestMixin;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.BiConnectorView;
import com.xgen.svc.nds.model.ui.GeoShardingView;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({CloudProviderExtension.class, MockitoExtension.class})
public class EstimateSvcImplUnitTests {

  @Mock private SkuPricingSvc skuPricingSvc;
  @Mock private LineItemDao lineItemDao;
  @Mock private HostClusterLifecycleSvc legacyHostSvc;
  @Mock private BackupHostSvc backupHostSvc;
  @Mock private SearchDeploymentDescriptionSvc searchDeploymentDescriptionSvc;
  @Mock private GroupDao groupDao;
  @Mock private ClusterDescriptionValidationUtil clusterDescriptionValidationUtil;

  @Mock(strictness = Mock.Strictness.LENIENT)
  private AppSettings appSettings;

  @InjectMocks private EstimateSvcImpl estimateSvc;

  @BeforeEach
  public void setup() {
    doReturn(0.0).when(appSettings).getGovUpliftRatio();
  }

  @Test
  public void testEstimateForReplicaSetReturnsNullIfNoPrimary() {
    final Group group = new Group();
    group.setId(oid(1));
    final List<SnapshotSchedule.Rule> rules = SnapshotSchedule.DEFAULT_RULES;

    String rsId = "rs0";
    when(backupHostSvc.getPrimaryForReplSet(group.getId(), rsId)).thenReturn(null);

    // If total bytes to be billed is null, return null
    assertNull(estimateSvc.estimateForReplicaSet(group, rsId, rules));
  }

  @Test
  public void testEstimateForReplicaSetReturnsCalculatedCost() {
    final Group group = new Group();
    group.setId(oid(1));
    group.setOrgId(oid(2));
    final List<SnapshotSchedule.Rule> rules = SnapshotSchedule.DEFAULT_RULES;
    final long twoGbInBytes = (long) Units.GIGABYTES.convertTo(2.0, Units.BYTES);

    final Host host = new Host();
    host.setLastDataSizeWithoutLocal(twoGbInBytes);
    String rsId = "rs1";
    when(backupHostSvc.getPrimaryForReplSet(group.getId(), rsId)).thenReturn(host);
    SkuPricing pricing =
        new SkuPricing.Builder()
            .unitPriceDollars(monthlyToDailyCost(1.25))
            .floatPriceDollars(monthlyToDailyCost(1.25))
            .freeTierQuantity(1.0)
            .build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.MMS_BACKUP_STORAGE),
            any(Date.class),
            eq(group.getOrgId()),
            eq(PricingConsumers.CLUSTERS));

    // Cost is based on number of bytes above free tier
    assertEquals(250L, estimateSvc.estimateForReplicaSet(group, rsId, rules));
  }

  @Test
  public void testEstimateForReplicaSetReturnsZeroIfInFreeTier() {
    final Group group = new Group();
    group.setId(oid(1));
    group.setOrgId(oid(2));
    final List<SnapshotSchedule.Rule> rules = SnapshotSchedule.DEFAULT_RULES;
    final long oneGbInBytes = (long) Units.GIGABYTES.convertTo(1.0, Units.BYTES);
    final Host host = new Host();
    host.setLastDataSizeWithoutLocal(oneGbInBytes);
    host.setLastIndexSizeWithoutLocal(null);
    String rsId = "rs1";
    when(backupHostSvc.getPrimaryForReplSet(group.getId(), rsId)).thenReturn(host);
    SkuPricing pricing =
        new SkuPricing.Builder()
            .unitPriceDollars(monthlyToDailyCost(1.25))
            .floatPriceDollars(monthlyToDailyCost(1.25))
            .freeTierQuantity(1.0)
            .build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.MMS_BACKUP_STORAGE),
            any(Date.class),
            eq(group.getOrgId()),
            eq(PricingConsumers.CLUSTERS));

    assertEquals(0L, estimateSvc.estimateForReplicaSet(group, rsId, rules));
  }

  @Test
  public void testEstimateForClusterReturnsSumOfEstimatesForReplicaSets() {
    // Estimate for an existing group is the sum of estimates of all its replica sets
    final Group group = new Group();
    group.setId(oid(1));
    group.setOrgId(oid(2));
    final HostCluster cluster = new HostCluster();
    Set<String> rsIds = Set.of("rs0", "rs1", "rs2");
    cluster.setReplicaSetIds(rsIds);
    when(legacyHostSvc.findHostClusterByClusterId(group.getId(), oid(1), false))
        .thenReturn(cluster);
    final long twoGbInBytes = (long) Units.GIGABYTES.convertTo(2.0, Units.BYTES);
    final Host host = new Host();
    host.setLastDataSizeWithoutLocal(twoGbInBytes);
    rsIds.forEach(
        rsId -> when(backupHostSvc.getPrimaryForReplSet(group.getId(), rsId)).thenReturn(host));
    SkuPricing pricing =
        new SkuPricing.Builder()
            .unitPriceDollars(monthlyToDailyCost(1.25))
            .floatPriceDollars(monthlyToDailyCost(1.25))
            .freeTierQuantity(1.0)
            .build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.MMS_BACKUP_STORAGE),
            any(Date.class),
            eq(group.getOrgId()),
            eq(PricingConsumers.CLUSTERS));

    assertEquals(
        750L, estimateSvc.estimateForCluster(group, oid(1), SnapshotSchedule.DEFAULT_RULES));
  }

  @Test
  public void testEstimateForClusterReturnsNullIfClusterDoesNotExist() {
    final Group group2 = new Group();
    group2.setId(oid(2));
    when(legacyHostSvc.findHostClusterByClusterId(group2.getId(), oid(2), false)).thenReturn(null);
    assertNull(estimateSvc.estimateForCluster(group2, oid(2), null));
  }

  @Test
  public void testCalcTotalBytesForBilling() {
    final Group group = new Group();
    group.setId(oid(1));
    group.setExemptFromBillingOnIndexSize(false);

    // Replica set with no primary returns null
    when(backupHostSvc.getPrimaryForReplSet(oid(1), "rs0")).thenReturn(null);
    assertNull(estimateSvc.calcTotalBytesForBilling(group, "rs0"));

    // Primary with null index size and data size returns 0
    final Host host1 = new Host();
    host1.setLastDataSizeWithoutLocal(null);
    host1.setLastIndexSizeWithoutLocal(null);
    when(backupHostSvc.getPrimaryForReplSet(oid(1), "rs1")).thenReturn(host1);
    assertEquals(0, estimateSvc.calcTotalBytesForBilling(group, "rs1").longValue());

    // Primary with data size and null index size returns data size
    final Host host2 = new Host();
    host2.setLastDataSizeWithoutLocal(12345L);
    host2.setLastIndexSizeWithoutLocal(null);
    when(backupHostSvc.getPrimaryForReplSet(oid(1), "rs2")).thenReturn(host2);
    assertEquals(12345L, estimateSvc.calcTotalBytesForBilling(group, "rs2").longValue());

    // Primary with index size and null data size returns index size
    final Host host3 = new Host();
    host3.setLastDataSizeWithoutLocal(null);
    host3.setLastIndexSizeWithoutLocal(54321L);
    when(backupHostSvc.getPrimaryForReplSet(oid(1), "rs3")).thenReturn(host3);
    assertEquals(54321L, estimateSvc.calcTotalBytesForBilling(group, "rs3").longValue());

    // Primary with index size and  data size returns sum of both for a group that's not exempt from
    // billing on index size
    final Host host4 = new Host();
    host4.setLastDataSizeWithoutLocal(12345L);
    host4.setLastIndexSizeWithoutLocal(54321L);
    when(backupHostSvc.getPrimaryForReplSet(oid(1), "rs4")).thenReturn(host4);
    assertEquals(54321L + 12345L, estimateSvc.calcTotalBytesForBilling(group, "rs4").longValue());

    // Primary with index size and data size returns just data size for a group that's exempt from
    // billing on index size
    final Host host5 = new Host();
    host5.setLastDataSizeWithoutLocal(12345L);
    host5.setLastIndexSizeWithoutLocal(54321L);
    group.setExemptFromBillingOnIndexSize(true);
    when(backupHostSvc.getPrimaryForReplSet(oid(1), "rs5")).thenReturn(host5);
    assertEquals(12345L, estimateSvc.calcTotalBytesForBilling(group, "rs5").longValue());
  }

  @Test
  public void testEstimateCentsForActiveAWSInstances() {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    SkuPricing pricing = new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.177).build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));

    assertEquals(
        0, estimateSvc.estimateCentsForInstances(0, AWSNDSInstanceSize.M30, false, regionName));
    assertEquals(
        38762, estimateSvc.estimateCentsForInstances(3, AWSNDSInstanceSize.M30, false, regionName));
    assertEquals(
        77525, estimateSvc.estimateCentsForInstances(6, AWSNDSInstanceSize.M30, false, regionName));
  }

  @Test
  public void testEstimateCentsForPausedAWSInstances() {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    SkuPricing pricing =
        new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.00364).build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_INSTANCE_M30_PAUSED), any(Date.class), eq(PricingConsumers.CLUSTERS));
    // Paused clusters
    assertEquals(
        0, estimateSvc.estimateCentsForInstances(0, AWSNDSInstanceSize.M30, true, regionName));
    assertEquals(
        797, estimateSvc.estimateCentsForInstances(3, AWSNDSInstanceSize.M30, true, regionName));
    assertEquals(
        1594, estimateSvc.estimateCentsForInstances(6, AWSNDSInstanceSize.M30, true, regionName));
  }

  @Test
  public void testEstimateCentsForLowCpuAWSInstances() {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    SkuPricing pricing = new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.254).build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_R40), any(Date.class), eq(PricingConsumers.CLUSTERS));
    assertEquals(
        0, estimateSvc.estimateCentsForInstances(0, AWSNDSInstanceSize.R40, false, regionName));
    assertEquals(
        55626, estimateSvc.estimateCentsForInstances(3, AWSNDSInstanceSize.R40, false, regionName));
    assertEquals(
        111252,
        estimateSvc.estimateCentsForInstances(6, AWSNDSInstanceSize.R40, false, regionName));
  }

  @Test
  public void testOneSKUPerInstanceType() {
    Arrays.stream(AWSNDSInstanceSize.values())
        .forEach(
            instanceSize -> {
              long numSKUs =
                  SKU.ALL_NDS_AWS_INSTANCE_SKUS.stream()
                      .filter(
                          sku ->
                              sku.getInfo().getAWSInstanceSize() == instanceSize
                                  && !sku.getInfo().isPaused())
                      .count();
              assertEquals(1, numSKUs);
            });
  }

  @Test
  public void testEstimateCentsForAWSStorage() {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    SkuPricing pricing =
        new SkuPricing.Builder()
            .regionUnitPriceDollars(regionName, monthlyToHourlyCost(0.133))
            .build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    assertEquals(
        0,
        estimateSvc.estimateCentsForAWSStorage(
            0, 50, AWSNDSInstanceSize.M30, VolumeType.Gp2, false, regionName));
    assertEquals(
        0,
        estimateSvc.estimateCentsForAWSStorage(
            1, 0, AWSNDSInstanceSize.M30, VolumeType.Gp2, false, regionName));
    assertEquals(
        0,
        estimateSvc.estimateCentsForAWSStorage(
            1, 40, AWSNDSInstanceSize.M30, VolumeType.Gp2, false, regionName));
    assertEquals(
        665,
        estimateSvc.estimateCentsForAWSStorage(
            1, 50, AWSNDSInstanceSize.M30, VolumeType.Gp2, false, regionName));
    assertEquals(
        798,
        estimateSvc.estimateCentsForAWSStorage(
            1, 60, AWSNDSInstanceSize.M30, VolumeType.Gp2, false, regionName));
    assertEquals(
        1330,
        estimateSvc.estimateCentsForAWSStorage(
            2, 50, AWSNDSInstanceSize.M30, VolumeType.Gp2, false, regionName));
  }

  @Test
  public void testEstimateCentsForPausedAWSStorage() {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    SkuPricing pricing =
        new SkuPricing.Builder()
            .regionUnitPriceDollars(regionName, monthlyToHourlyCost(0.133))
            .build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    assertEquals(
        0,
        estimateSvc.estimateCentsForAWSStorage(
            0, 50, AWSNDSInstanceSize.M30, VolumeType.Gp2, true, regionName));
    assertEquals(
        0,
        estimateSvc.estimateCentsForAWSStorage(
            1, 0, AWSNDSInstanceSize.M30, VolumeType.Gp2, true, regionName));
    assertEquals(
        532,
        estimateSvc.estimateCentsForAWSStorage(
            1, 40, AWSNDSInstanceSize.M30, VolumeType.Gp2, true, regionName));
    assertEquals(
        665,
        estimateSvc.estimateCentsForAWSStorage(
            1, 50, AWSNDSInstanceSize.M30, VolumeType.Gp2, true, regionName));
    assertEquals(
        798,
        estimateSvc.estimateCentsForAWSStorage(
            1, 60, AWSNDSInstanceSize.M30, VolumeType.Gp2, true, regionName));
    assertEquals(
        1330,
        estimateSvc.estimateCentsForAWSStorage(
            2, 50, AWSNDSInstanceSize.M30, VolumeType.Gp2, true, regionName));
  }

  @Test
  public void testEstimateCentsForAWSStorageIOPS() {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    SkuPricing pricing =
        new SkuPricing.Builder()
            .regionUnitPriceDollars(regionName, monthlyToHourlyCost(0.087))
            .build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_STORAGE_IOPS), any(Date.class), eq(PricingConsumers.CLUSTERS));
    assertEquals(0, estimateSvc.estimateCentsForAWSStorageIOPS(0, 10, VolumeType.Io1, regionName));
    assertEquals(0, estimateSvc.estimateCentsForAWSStorageIOPS(1, 0, VolumeType.Io1, regionName));
    assertEquals(87, estimateSvc.estimateCentsForAWSStorageIOPS(1, 10, VolumeType.Io1, regionName));
    assertEquals(
        174, estimateSvc.estimateCentsForAWSStorageIOPS(1, 20, VolumeType.Io1, regionName));
    assertEquals(
        174, estimateSvc.estimateCentsForAWSStorageIOPS(2, 10, VolumeType.Io1, regionName));

    // There is no IOPS charge for Gp2, so these should all come back as zero.
    assertEquals(0, estimateSvc.estimateCentsForAWSStorageIOPS(0, 10, VolumeType.Gp2, regionName));
    assertEquals(0, estimateSvc.estimateCentsForAWSStorageIOPS(1, 0, VolumeType.Gp2, regionName));
    assertEquals(0, estimateSvc.estimateCentsForAWSStorageIOPS(1, 10, VolumeType.Gp2, regionName));
    assertEquals(0, estimateSvc.estimateCentsForAWSStorageIOPS(1, 20, VolumeType.Gp2, regionName));
    assertEquals(0, estimateSvc.estimateCentsForAWSStorageIOPS(2, 10, VolumeType.Gp2, regionName));
  }

  @Test
  public void testEstimateCentsForM30AWSCluster() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    SkuPricing pricing =
        new SkuPricing.Builder().regionUnitPriceDollars(region, monthlyToHourlyCost(0.133)).build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing2 = new SkuPricing.Builder().regionUnitPriceDollars(region, 0.177).build();
    doReturn(pricing2)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));

    final ClusterDescription desc1 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setDiskIOPS(120)
                    .setInstanceSize(AWSNDSInstanceSize.M30))
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(38762, estimateSvc.estimateCentsForAWSCluster(desc1));
    final ClusterDescription desc2 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setDiskIOPS(150)
                    .setInstanceSize(AWSNDSInstanceSize.M30))
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(50)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(40757, estimateSvc.estimateCentsForAWSCluster(desc2));
  }

  @Test
  public void testEstimateCentsForAWSClusterEBSVolumeTypeIo1() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    SkuPricing pricing =
        new SkuPricing.Builder().regionUnitPriceDollars(region, monthlyToHourlyCost(0.167)).build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_PROVISIONED), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing2 = new SkuPricing.Builder().regionUnitPriceDollars(region, 0.177).build();
    doReturn(pricing2)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing3 =
        new SkuPricing.Builder().regionUnitPriceDollars(region, monthlyToHourlyCost(0.087)).build();
    doReturn(pricing3)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_STORAGE_IOPS), any(Date.class), eq(PricingConsumers.CLUSTERS));

    final ClusterDescription desc3 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setDiskIOPS(150)
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setEBSVolumeType(VolumeType.Io1))
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(44681, estimateSvc.estimateCentsForAWSCluster(desc3));
  }

  @Test
  public void testEstimateCentsForAWSClusterMultiRegion() {
    SkuPricing pricing =
        new SkuPricing.Builder()
            .regionUnitPriceDollars(AWSRegionName.US_EAST_1, monthlyToHourlyCost(0.167))
            .regionUnitPriceDollars(AWSRegionName.US_WEST_2, monthlyToHourlyCost(0.167))
            .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_2, monthlyToHourlyCost(0.184))
            .build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_PROVISIONED), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing2 =
        new SkuPricing.Builder()
            .regionUnitPriceDollars(AWSRegionName.US_EAST_1, 0.177)
            .regionUnitPriceDollars(AWSRegionName.US_WEST_2, 0.177)
            .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_2, 0.247)
            .build();
    doReturn(pricing2)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing3 =
        new SkuPricing.Builder()
            .regionUnitPriceDollars(AWSRegionName.US_EAST_1, monthlyToHourlyCost(0.087))
            .regionUnitPriceDollars(AWSRegionName.US_WEST_2, monthlyToHourlyCost(0.087))
            .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_2, monthlyToHourlyCost(0.096))
            .build();
    doReturn(pricing3)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_STORAGE_IOPS), any(Date.class), eq(PricingConsumers.CLUSTERS));
    final ClusterDescription desc4 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(
                getReplicationSpecs(
                    1,
                    1,
                    AWSRegionName.US_EAST_1,
                    AWSRegionName.AP_SOUTHEAST_2,
                    AWSRegionName.US_WEST_2))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setDiskIOPS(150)
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setEBSVolumeType(VolumeType.Io1))
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(49992, estimateSvc.estimateCentsForAWSCluster(desc4));
  }

  @Test
  public void testEstimateCentsForAWSClusterM40NVME() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    SkuPricing pricing =
        new SkuPricing.Builder().regionUnitPriceDollars(region, monthlyToHourlyCost(0.167)).build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_PROVISIONED), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing2 = new SkuPricing.Builder().regionUnitPriceDollars(region, 0.202).build();
    doReturn(pricing2)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_INSTANCE_M40_NVME), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing3 =
        new SkuPricing.Builder().regionUnitPriceDollars(region, monthlyToHourlyCost(0.087)).build();
    doReturn(pricing3)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_STORAGE_IOPS), any(Date.class), eq(PricingConsumers.CLUSTERS));

    final ClusterDescription desc5 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, 0, 1, CloudProvider.AWS, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setDiskIOPS(3000)
                    .setInstanceSize(AWSNDSInstanceSize.M40_NVME))
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(91764, estimateSvc.estimateCentsForAWSCluster(desc5));
  }

  @Test
  public void testEstimateCentsForAsymmetricAWSCluster() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    SkuPricing pricing =
        new SkuPricing.Builder().regionUnitPriceDollars(region, monthlyToHourlyCost(0.133)).build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing2 = new SkuPricing.Builder().regionUnitPriceDollars(region, 0.177).build();
    doReturn(pricing2)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.026).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M10), any(Date.class), eq(PricingConsumers.CLUSTERS));
    // asymmetric cluster - base: M30 * 3, analytics: M10 * 1
    final ClusterDescription desc6 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("6.0.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, 1, 0, CloudProvider.AWS, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M30),
                NodeTypeFamily.BASE)
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M10),
                NodeTypeFamily.ANALYTICS)
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.ANALYTICS)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(41192, estimateSvc.estimateCentsForAWSCluster(desc6));
    // symmetric M30 cluster (3 electable, 1 analytics)
    final ClusterDescription desc7 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("6.0.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, 1, 0, CloudProvider.AWS, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M30))
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertNotEquals(
        estimateSvc.estimateCentsForAWSCluster(desc6),
        estimateSvc.estimateCentsForAWSCluster(desc7));
    assertTrue(
        estimateSvc.estimateCentsForAWSCluster(desc7)
            > estimateSvc.estimateCentsForAWSCluster(desc6));
    assertEquals(51682, estimateSvc.estimateCentsForAWSCluster(desc7));
  }

  @Test
  public void testEstimateCentsForAWSCluster_nodeTypeSet() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    SkuPricing pricing =
        new SkuPricing.Builder().regionUnitPriceDollars(region, monthlyToHourlyCost(0.133)).build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing2 = new SkuPricing.Builder().regionUnitPriceDollars(region, 0.177).build();
    doReturn(pricing2)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.026).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M10), any(Date.class), eq(PricingConsumers.CLUSTERS));

    final ClusterDescription desc1 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, 1, 0, CloudProvider.AWS, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AWSHardwareSpec.Builder()
                    .setDiskIOPS(120)
                    .setInstanceSize(AWSNDSInstanceSize.M30),
                NodeTypeFamily.ANALYTICS)
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AWSHardwareSpec.Builder()
                    .setDiskIOPS(120)
                    .setInstanceSize(AWSNDSInstanceSize.M10),
                NodeTypeFamily.BASE)
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();

    final long analyticsNodesEstimate =
        estimateSvc.estimateCentsForAWSCluster(desc1, Set.of(NodeType.ANALYTICS));
    final long baseNodesEstimate =
        estimateSvc.estimateCentsForAWSCluster(desc1, Set.of(NodeType.ELECTABLE));
    final long totalNodesEstimate =
        estimateSvc.estimateCentsForAWSCluster(
            desc1, Set.of(NodeType.ANALYTICS, NodeType.ELECTABLE));
    assertEquals(totalNodesEstimate, analyticsNodesEstimate + baseNodesEstimate);
  }

  @Test
  public void testEstimateCentsForAWSShardedClusterM20And30() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    SkuPricing pricing =
        new SkuPricing.Builder().regionUnitPriceDollars(region, monthlyToHourlyCost(0.133)).build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing2 = new SkuPricing.Builder().regionUnitPriceDollars(region, 0.177).build();
    doReturn(pricing2)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.067).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M20), any(Date.class), eq(PricingConsumers.CLUSTERS));

    final ShardedClusterDescription desc1 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAWSConfigServerReplicationSpec(
                    region, AWSNDSInstanceSize.M20, 120, VolumeType.Gp2),
                false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setDiskIOPS(480))
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(106561, estimateSvc.estimateCentsForAWSCluster(desc1));
  }

  @Test
  public void testEstimateCentsForAWSShardedClusterM102030() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    SkuPricing pricing =
        new SkuPricing.Builder().regionUnitPriceDollars(region, monthlyToHourlyCost(0.133)).build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing2 = new SkuPricing.Builder().regionUnitPriceDollars(region, 0.177).build();
    doReturn(pricing2)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.026).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M10), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.067).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M20), any(Date.class), eq(PricingConsumers.CLUSTERS));

    // asymmetric sharded cluster - base: M30, analytics: M10
    final ShardedClusterDescription desc2 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAWSConfigServerReplicationSpec(
                    region, AWSNDSInstanceSize.M20, 120, VolumeType.Gp2),
                false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, 1, 0, CloudProvider.AWS, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setDiskIOPS(480),
                NodeTypeFamily.BASE)
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M10)
                    .setDiskIOPS(480),
                NodeTypeFamily.ANALYTICS)
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.ANALYTICS)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(114613, estimateSvc.estimateCentsForAWSCluster(desc2));
    final ShardedClusterDescription desc3 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAWSConfigServerReplicationSpec(
                    region, AWSNDSInstanceSize.M20, 120, VolumeType.Gp2),
                false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setDiskIOPS(600))
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(200)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(109753, estimateSvc.estimateCentsForAWSCluster(desc3));
  }

  @Test
  public void testEstimateCentsForAWSShardedCluster() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.133))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.167))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_PROVISIONED), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.087))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_STORAGE_IOPS), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing2 = new SkuPricing.Builder().regionUnitPriceDollars(region, 0.177).build();
    doReturn(pricing2)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.067).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M20), any(Date.class), eq(PricingConsumers.CLUSTERS));

    final ShardedClusterDescription desc4 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAWSConfigServerReplicationSpec(
                    region, AWSNDSInstanceSize.M20, 120, VolumeType.Gp2),
                false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setDiskIOPS(500)
                    .setEBSVolumeType(VolumeType.Io1))
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(135925, estimateSvc.estimateCentsForAWSCluster(desc4));
    final ShardedClusterDescription desc5 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAWSConfigServerReplicationSpec(
                    region, AWSNDSInstanceSize.M20, 120, VolumeType.Gp2),
                false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(3, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setDiskIOPS(480))
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(151709, estimateSvc.estimateCentsForAWSCluster(desc5));
    final ShardedClusterDescription desc6 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAWSConfigServerReplicationSpec(
                    region, AWSNDSInstanceSize.M20, 150, VolumeType.Gp2),
                false)
            .setDedicatedConfigServerDiskSizeGB(50)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setDiskIOPS(480))
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(106960, estimateSvc.estimateCentsForAWSCluster(desc6));
    final ShardedClusterDescription desc7 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAWSConfigServerReplicationSpec(
                    region, AWSNDSInstanceSize.M20, 150, VolumeType.Io1),
                false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setDiskIOPS(380))
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(110884, estimateSvc.estimateCentsForAWSCluster(desc7));
  }

  @Test
  public void testEstimateCentsForAWSShardedClusterMultiRegion() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.133))
                .regionUnitPriceDollars(AWSRegionName.US_WEST_2, monthlyToHourlyCost(0.133))
                .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_2, monthlyToHourlyCost(0.160))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.167))
                .regionUnitPriceDollars(AWSRegionName.US_WEST_2, monthlyToHourlyCost(0.167))
                .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_2, monthlyToHourlyCost(0.184))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_PROVISIONED), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.087))
                .regionUnitPriceDollars(AWSRegionName.US_WEST_2, monthlyToHourlyCost(0.087))
                .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_2, monthlyToHourlyCost(0.096))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_STORAGE_IOPS), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing2 =
        new SkuPricing.Builder()
            .regionUnitPriceDollars(region, 0.177)
            .regionUnitPriceDollars(AWSRegionName.US_WEST_2, 0.177)
            .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_2, 0.247)
            .build();
    doReturn(pricing2)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, 0.067)
                .regionUnitPriceDollars(AWSRegionName.US_WEST_2, 0.067)
                .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_2, 0.103)
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M20), any(Date.class), eq(PricingConsumers.CLUSTERS));
    final ShardedClusterDescription desc8 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getConfigServerReplicationSpec(
                    getAWSRegionConfig(
                        AWSRegionName.US_EAST_1,
                        RegionConfig.MAX_PRIORITY,
                        1,
                        AWSNDSInstanceSize.M20,
                        150,
                        VolumeType.Io1),
                    getAWSRegionConfig(
                        AWSRegionName.AP_SOUTHEAST_2,
                        RegionConfig.MAX_PRIORITY - 1,
                        1,
                        AWSNDSInstanceSize.M20,
                        150,
                        VolumeType.Io1),
                    getAWSRegionConfig(
                        AWSRegionName.US_WEST_2,
                        RegionConfig.MAX_PRIORITY - 2,
                        1,
                        AWSNDSInstanceSize.M20,
                        150,
                        VolumeType.Io1)),
                false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setReplicationSpecList(
                getReplicationSpecs(
                    2,
                    1,
                    AWSRegionName.US_EAST_1,
                    AWSRegionName.AP_SOUTHEAST_2,
                    AWSRegionName.US_WEST_2))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setDiskIOPS(380))
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(124798, estimateSvc.estimateCentsForAWSCluster(desc8));
  }

  @Test
  public void testEstimateCentsForAWSShardedClusterM40NVME() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.167))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_PROVISIONED), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.087))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_STORAGE_IOPS), any(Date.class), eq(PricingConsumers.CLUSTERS));
    SkuPricing pricing2 = new SkuPricing.Builder().regionUnitPriceDollars(region, 0.202).build();
    doReturn(pricing2)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_INSTANCE_M40_NVME), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.177).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    final ShardedClusterDescription desc9 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAWSConfigServerReplicationSpec(
                    region, AWSNDSInstanceSize.M30, 150, VolumeType.Io1),
                false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, 0, 1, CloudProvider.AWS, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M40_NVME)
                    .setDiskIOPS(3000))
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(136445, estimateSvc.estimateCentsForAWSCluster(desc9));
  }

  @Test
  public void testEstimateCentsForAWSShardedClusterMultiInstance() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.133))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.167))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_PROVISIONED), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.087))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_STORAGE_IOPS), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 1.300).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M60), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.656).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M50), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.341).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M40), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.177).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    // ISS = 3 M60 + 1 M50 + 3 M40 + 1 M30
    final ReplicationSpec issAsymmetricShard0 =
        getReplicationSpecs(1, 3, 1, 0, CloudProvider.AWS, region).get(0);
    final ReplicationSpec issAsymmetricShard1 =
        getReplicationSpecs(1, 3, 1, 0, CloudProvider.AWS, region).get(0);
    final ShardedClusterDescription desc10 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAWSConfigServerReplicationSpec(
                    region, AWSNDSInstanceSize.M30, 150, VolumeType.Io1),
                false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(List.of(issAsymmetricShard0, issAsymmetricShard1))
            .updateHardwareForNodeType(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M60)
                    .setDiskIOPS(3000),
                NodeTypeFamily.BASE,
                issAsymmetricShard0.getId())
            .updateHardwareForNodeType(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M50)
                    .setDiskIOPS(3000),
                NodeTypeFamily.ANALYTICS,
                issAsymmetricShard0.getId())
            .updateHardwareForNodeType(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M40)
                    .setDiskIOPS(3000),
                NodeTypeFamily.BASE,
                issAsymmetricShard1.getId())
            .updateHardwareForNodeType(
                new AWSHardwareSpec.Builder()
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setDiskIOPS(3000),
                NodeTypeFamily.ANALYTICS,
                issAsymmetricShard1.getId())
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(473379, estimateSvc.estimateCentsForAWSCluster(desc10));
  }

  @Test
  public void testCantEstimateUnavailableAWSRegion() throws SvcException {
    AzureRegionName regionName = AzureRegionName.CHINA_EAST;

    // Mock the validation util to throw REGION_UNAVAILABLE exception
    final SvcException regionException =
        new SvcException(NDSErrorCode.REGION_UNAVAILABLE, "Azure", "CHINA_EAST");
    doThrow(regionException)
        .when(clusterDescriptionValidationUtil)
        .validateReplicationSpecsWithShardData(any(ClusterDescription.class));
    final ClusterDescription desc =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setReplicationSpecList(getReplicationSpecs(1, 3, regionName))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M10)
                    .setDiskType(AzureDiskType.P10))
            .setDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AWS,
                AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .setHostnameSchemeForAgents(HostnameScheme.INTERNAL)
            .setGroupId(oid(42))
            .setClusterNamePrefix("test")
            .build();

    try {
      estimateSvc.estimateCentsForNDSCluster(
          desc, Optional.empty(), Optional.empty(), Optional.empty());
      fail("Should have thrown exception when attempting to estimate an unsupported region");
    } catch (final SvcException se) {
      assertEquals(NDSErrorCode.REGION_UNAVAILABLE, se.getErrorCode());
    }
  }

  @Test
  public void testEstimateMultiRegion() throws SvcException {
    // Mock AWS search instance pricing
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AWSRegionName.US_EAST_1, 0.117)
                .regionUnitPriceDollars(AWSRegionName.US_EAST_2, 0.120)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_SEARCH_INSTANCE_S20_COMPUTE_NVME),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AWSRegionName.US_EAST_1, 0.177)
                .regionUnitPriceDollars(AWSRegionName.US_EAST_2, 0.180)
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AWSRegionName.US_EAST_1, monthlyToHourlyCost(0.133))
                .regionUnitPriceDollars(AWSRegionName.US_EAST_2, monthlyToHourlyCost(0.140))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(new ObjectId(), "multiRegion", CloudProvider.AWS)
            .copy()
            .setMongoDBVersion("7.0")
            .setReplicationSpecList(
                List.of(
                    new ReplicationSpec(
                        new ObjectId(),
                        new ObjectId(),
                        new ObjectId(),
                        NDSDefaults.ZONE_NAME,
                        1,
                        NDSModelTestFactory.getRegionConfigs(
                            2, List.of(AWSRegionName.US_EAST_1, AWSRegionName.US_EAST_2)))))
            .build();
    // Create a search deployment description that matches the AWS multi-region cluster
    doReturn(
            SearchConfigModelTestFactory.getDeploymentDescriptionAWS(
                new ObjectId(),
                new ObjectId(),
                2,
                List.of(AWSRegionName.US_EAST_1, AWSRegionName.US_EAST_2),
                null))
        .when(searchDeploymentDescriptionSvc)
        .buildSearchDeploymentDescription(
            any(ClusterDescription.class), anyMap(), anyMap(), anyBoolean());

    {
      long costWithoutSearchNodes =
          estimateSvc.estimateCentsForNDSCluster(
              clusterDescription, Optional.empty(), Optional.empty(), Optional.empty());

      long costWithSearchNodes =
          estimateSvc.estimateCentsForNDSCluster(
              clusterDescription,
              Optional.of(SearchInstanceSizeAPI.API_INSTANCE_SIZE.S20_HIGHCPU_NVME),
              Optional.of(2),
              Optional.empty());
      assertTrue(costWithoutSearchNodes < costWithSearchNodes);
    }

    {
      // Cost Estimation should fail when search instance is not supported for all regions
      assertThrows(
          NullPointerException.class,
          () ->
              estimateSvc.estimateCentsForNDSCluster(
                  clusterDescription,
                  Optional.of(SearchInstanceSizeAPI.API_INSTANCE_SIZE.S135_LOWCPU_NVME),
                  Optional.of(2),
                  Optional.empty()));
    }
  }

  @Test
  public void testEstimateCentsForAzureInstances() {
    AzureRegionName regionName = AzureRegionName.US_EAST;
    SkuPricing pricing =
        new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.1685).build();
    doReturn(pricing)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));

    SkuPricing pausedPricing =
        new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.00438).build();
    doReturn(pausedPricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_INSTANCE_M30_PAUSED), any(Date.class), eq(PricingConsumers.CLUSTERS));

    assertEquals(
        0, estimateSvc.estimateCentsForInstances(0, AzureNDSInstanceSize.M30, false, regionName));
    assertEquals(
        36901,
        estimateSvc.estimateCentsForInstances(3, AzureNDSInstanceSize.M30, false, regionName));
    assertEquals(
        73803,
        estimateSvc.estimateCentsForInstances(6, AzureNDSInstanceSize.M30, false, regionName));

    // Paused clusters
    assertEquals(
        0, estimateSvc.estimateCentsForInstances(0, AzureNDSInstanceSize.M30, true, regionName));
    assertEquals(
        959, estimateSvc.estimateCentsForInstances(3, AzureNDSInstanceSize.M30, true, regionName));
    assertEquals(
        1918, estimateSvc.estimateCentsForInstances(6, AzureNDSInstanceSize.M30, true, regionName));
  }

  @Test
  public void testGetAWSServerlessCostEstimate() {
    Map<SKU, SkuPricing> pricingBySku =
        Map.of(
            SKU.NDS_AWS_SERVERLESS_RPU,
            new SkuPricing.Builder()
                .tieredPricingQuantities(50, 550)
                .tieredPricingQuantityHardCap(20550.0)
                .tieredRegionUnitPriceDollars(AWSRegionName.US_EAST_1, 0.100, 0.050, 0.010)
                .tieredRegionUnitPriceDollars(AWSRegionName.US_WEST_2, 0.100, 0.050, 0.010)
                .tieredRegionUnitPriceDollars(AWSRegionName.EU_WEST_1, 0.111, 0.056, 0.011)
                .tieredRegionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_1, 0.125, 0.062, 0.012)
                .build(),
            SKU.NDS_AWS_SERVERLESS_WPU,
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AWSRegionName.US_EAST_1, 1.000)
                .regionUnitPriceDollars(AWSRegionName.US_WEST_2, 1.000)
                .regionUnitPriceDollars(AWSRegionName.EU_WEST_1, 1.115)
                .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_1, 1.250)
                .build(),
            SKU.NDS_AWS_SERVERLESS_STORAGE,
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AWSRegionName.US_EAST_1, monthlyToDailyCost(0.250))
                .regionUnitPriceDollars(AWSRegionName.US_WEST_2, monthlyToDailyCost(0.250))
                .regionUnitPriceDollars(AWSRegionName.EU_WEST_1, monthlyToDailyCost(0.275))
                .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_1, monthlyToDailyCost(0.300))
                .build(),
            SKU.NDS_AWS_SERVERLESS_CONTINUOUS_BACKUP,
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AWSRegionName.US_EAST_1, monthlyToDailyCost(0.200))
                .regionUnitPriceDollars(AWSRegionName.US_WEST_2, monthlyToDailyCost(0.200))
                .regionUnitPriceDollars(AWSRegionName.EU_WEST_1, monthlyToDailyCost(0.220))
                .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_1, monthlyToDailyCost(0.240))
                .build());
    doReturn(pricingBySku)
        .when(skuPricingSvc)
        .getPricing(
            eq(
                List.of(
                    SKU.NDS_AWS_SERVERLESS_RPU,
                    SKU.NDS_AWS_SERVERLESS_WPU,
                    SKU.NDS_AWS_SERVERLESS_STORAGE,
                    SKU.NDS_AWS_SERVERLESS_CONTINUOUS_BACKUP)),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    for (final AWSRegionName awsRegionName :
        List.of(
            AWSRegionName.US_EAST_1,
            AWSRegionName.US_WEST_2,
            AWSRegionName.EU_WEST_1,
            AWSRegionName.AP_SOUTHEAST_1)) {
      int tier = 0;
      for (final String tieredReadString :
          List.of("tier1ReadCents", "tier2ReadCents", "tier3ReadCents")) {
        tier++;
        assertEquals(
            Long.valueOf(
                (long)
                    (pricingBySku
                            .get(SKU.NDS_AWS_SERVERLESS_RPU)
                            .getTieredRegionUnitPriceDollars(
                                awsRegionName, tier, SKU.NDS_AWS_SERVERLESS_RPU)
                        * 100)),
            estimateSvc.getAWSServerlessCostEstimate(awsRegionName, true).get(tieredReadString),
            awsRegionName.getName());
      }
      assertEquals(
          Long.valueOf(
              (long)
                  (pricingBySku
                          .get(SKU.NDS_AWS_SERVERLESS_WPU)
                          .getRegionUnitPriceDollars(awsRegionName, SKU.NDS_AWS_SERVERLESS_WPU)
                      * 100)),
          estimateSvc.getAWSServerlessCostEstimate(awsRegionName, true).get("serverlessWriteCents"),
          awsRegionName.getName());
      assertEquals(
          Long.valueOf(
              (long)
                  (pricingBySku
                          .get(SKU.NDS_AWS_SERVERLESS_STORAGE)
                          .getRegionUnitPriceDollars(awsRegionName, SKU.NDS_AWS_SERVERLESS_STORAGE)
                      * 100
                      * 365
                      / 12)),
          estimateSvc
              .getAWSServerlessCostEstimate(awsRegionName, true)
              .get("serverlessStorageCents"),
          awsRegionName.getName());
      assertEquals(
          Long.valueOf(
              (long)
                  (pricingBySku
                          .get(SKU.NDS_AWS_SERVERLESS_CONTINUOUS_BACKUP)
                          .getRegionUnitPriceDollars(
                              awsRegionName, SKU.NDS_AWS_SERVERLESS_CONTINUOUS_BACKUP)
                      * 100
                      * 365
                      / 12)),
          estimateSvc
              .getAWSServerlessCostEstimate(awsRegionName, true)
              .get("serverlessContinuousBackupCents"),
          awsRegionName.getName());
    }
  }

  @Test
  public void testGetAWSStreamInstanceCostEstimate() {
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    Map<SKU, SkuPricing> pricingBySku =
        Map.of(
            SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP10,
            new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.190).build(),
            SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP30,
            new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.390).build(),
            SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP50,
            new SkuPricing.Builder().regionUnitPriceDollars(regionName, 1.560).build());
    doReturn(pricingBySku)
        .when(skuPricingSvc)
        .getPricing(
            eq(
                List.of(
                    SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP10,
                    SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP30,
                    SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP50)),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    final Map<String, Long> result = estimateSvc.getAWSStreamInstanceCostEstimate(regionName);

    final double SP30Cost =
        pricingBySku
            .get(SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP30)
            .getRegionUnitPriceDollars(regionName, SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP30);

    // Monthly cost
    assertEquals(
        Long.valueOf((long) ((SP30Cost) * 24 * 365 / 12 * 100)),
        result.get("monthlyEstimateCentsSP30"));

    // Daily cost
    assertEquals(
        Long.valueOf((long) ((SP30Cost) * 24 * 100)), result.get("dailyEstimateCentsSP30"));

    final double SP10Cost =
        pricingBySku
            .get(SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP10)
            .getRegionUnitPriceDollars(regionName, SKU.NDS_AWS_STREAM_PROCESSING_INSTANCE_SP10);

    // Monthly cost
    assertEquals(
        Long.valueOf((long) ((SP10Cost) * 24 * 365 / 12 * 100)),
        result.get("monthlyEstimateCentsSP10"));

    // Daily cost
    assertEquals(
        Long.valueOf((long) ((SP10Cost) * 24 * 100)), result.get("dailyEstimateCentsSP10"));

    assertTrue(result.containsKey("dailyEstimateCentsSP50"));
    assertTrue(result.containsKey("monthlyEstimateCentsSP50"));
  }

  @Test
  public void testAWSStreamConnectionVPCPeeringCostEstimate() {
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    double unitPriceDollars = 0.1685;
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(regionName, unitPriceDollars).build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STREAM_PROCESSING_VPC_PEERING),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    final long result = estimateSvc.getAWSStreamConnectionVPCPeeringCostEstimate(regionName);

    assertEquals((long) (unitPriceDollars * 100), result);
  }

  @Test
  public void testGetAzureStreamInstanceCostEstimate() {
    final AzureRegionName regionName = AzureRegionName.US_WEST;
    double unitPriceDollars = 0.1685;
    Map<SKU, SkuPricing> pricingBySku =
        Map.of(
            SKU.NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP10,
            new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.251).build(),
            SKU.NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP30,
            new SkuPricing.Builder().regionUnitPriceDollars(regionName, unitPriceDollars).build(),
            SKU.NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP50,
            new SkuPricing.Builder().regionUnitPriceDollars(regionName, 2.061).build());
    doReturn(pricingBySku)
        .when(skuPricingSvc)
        .getPricing(
            eq(
                List.of(
                    SKU.NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP10,
                    SKU.NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP30,
                    SKU.NDS_AZURE_STREAM_PROCESSING_INSTANCE_SP50)),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    final Map<String, Long> result = estimateSvc.getAzureStreamInstanceCostEstimate(regionName);

    // Monthly cost
    assertEquals(
        Long.valueOf((long) ((unitPriceDollars) * 24 * 365 / 12 * 100)),
        result.get("monthlyEstimateCentsSP30"));

    // Daily cost
    assertEquals(
        Long.valueOf((long) ((unitPriceDollars) * 24 * 100)), result.get("dailyEstimateCentsSP30"));

    assertTrue(result.containsKey("dailyEstimateCentsSP10"));
    assertTrue(result.containsKey("dailyEstimateCentsSP50"));
    assertTrue(result.containsKey("monthlyEstimateCentsSP10"));
    assertTrue(result.containsKey("monthlyEstimateCentsSP50"));
  }

  @Test
  public void testEstimateCentsForAzureStorage() {
    AzureRegionName region = AzureRegionName.US_EAST;
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(7.04, 32))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P4), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(13.61, 64))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P6), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(26.28, 128))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P10), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(97.63, 512))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P20), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(180.23, 1024))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.161))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_STANDARD_STORAGE), any(Date.class), eq(PricingConsumers.CLUSTERS));

    // 1 instance, M30, P4 - included
    assertEquals(
        0,
        estimateSvc.estimateCentsForAzureStorage(
            1,
            AzureDiskType.P4.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P4,
            false,
            region));
    // 1 instance, M30, P6
    assertEquals(
        1361,
        estimateSvc.estimateCentsForAzureStorage(
            1,
            AzureDiskType.P6.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P6,
            false,
            region));
    // 0 instance, M30, P10
    assertEquals(
        0,
        estimateSvc.estimateCentsForAzureStorage(
            0,
            AzureDiskType.P10.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P10,
            false,
            region));
    // 1 M30, P10
    assertEquals(
        2627,
        estimateSvc.estimateCentsForAzureStorage(
            1,
            AzureDiskType.P10.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P10,
            false,
            region));
    // 1 M30, P20
    assertEquals(
        9763,
        estimateSvc.estimateCentsForAzureStorage(
            1,
            AzureDiskType.P20.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P20,
            false,
            region));
    // 1 M30, P30
    assertEquals(
        18023,
        estimateSvc.estimateCentsForAzureStorage(
            1,
            AzureDiskType.P30.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P30,
            false,
            region));
    // 2 M30, P10
    assertEquals(
        5255,
        estimateSvc.estimateCentsForAzureStorage(
            2,
            AzureDiskType.P10.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P10,
            false,
            region));

    // Paused clusters
    // 1 instance, M30, P4 - included
    assertEquals(
        704,
        estimateSvc.estimateCentsForAzureStorage(
            1,
            AzureDiskType.P4.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P4,
            true,
            region));
    // 1 instance, M30, P6
    assertEquals(
        1361,
        estimateSvc.estimateCentsForAzureStorage(
            1,
            AzureDiskType.P6.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P6,
            true,
            region));
    // 0 instance, M30, P10
    assertEquals(
        0,
        estimateSvc.estimateCentsForAzureStorage(
            0,
            AzureDiskType.P10.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P10,
            true,
            region));
    // 1 M30, P10
    assertEquals(
        2627,
        estimateSvc.estimateCentsForAzureStorage(
            1,
            AzureDiskType.P10.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P10,
            true,
            region));
    // 1 M30, P20
    assertEquals(
        9763,
        estimateSvc.estimateCentsForAzureStorage(
            1,
            AzureDiskType.P20.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P20,
            true,
            region));
    // 1 M30, P30
    assertEquals(
        18023,
        estimateSvc.estimateCentsForAzureStorage(
            1,
            AzureDiskType.P30.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P30,
            true,
            region));
    // 2 M30, P10
    assertEquals(
        5255,
        estimateSvc.estimateCentsForAzureStorage(
            2,
            AzureDiskType.P10.getSizeGB(),
            AzureNDSInstanceSize.M30,
            AzureDiskType.P10,
            true,
            region));

    // 1 M30, 1000 GB, V2
    assertEquals(
        16099,
        estimateSvc.estimateCentsForAzureStorage(
            1, 1000, AzureNDSInstanceSize.M30, AzureDiskType.V2, false, region));

    // 3 M30, 100 GB, V2
    assertEquals(
        4829,
        estimateSvc.estimateCentsForAzureStorage(
            3, 100, AzureNDSInstanceSize.M30, AzureDiskType.V2, false, region));
  }

  @Test
  public void testEstimateCentsForAzureCluster_Pv1() {
    final AzureRegionName region = AzureRegionName.US_EAST;
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(26.28, 128))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P10), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(97.63, 512))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P20), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(180.23, 1024))
                .regionUnitPriceDollars(
                    AzureRegionName.EUROPE_NORTH, monthlyToHourlyCostPerGB(180.23, 1024))
                .regionUnitPriceDollars(
                    AzureRegionName.AUSTRALIA_SOUTH_EAST, monthlyToHourlyCostPerGB(180.23, 1024))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, 0.1685)
                .regionUnitPriceDollars(AzureRegionName.EUROPE_NORTH, 0.1988)
                .regionUnitPriceDollars(AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.2270)
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.0264).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_INSTANCE_M10), any(Date.class), eq(PricingConsumers.CLUSTERS));
    final ClusterDescription desc1 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setReplicationSpecList(getReplicationSpecs(1, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M30)
                    .setDiskType(AzureDiskType.P10))
            .setDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(44784, estimateSvc.estimateCentsForAzureCluster(desc1));
    final ClusterDescription desc2 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setReplicationSpecList(getReplicationSpecs(1, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M30)
                    .setDiskType(AzureDiskType.P20))
            .setDiskSizeGB(AzureDiskType.P20.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(66189, estimateSvc.estimateCentsForAzureCluster(desc2));
    final ClusterDescription desc3 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setReplicationSpecList(getReplicationSpecs(1, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M30)
                    .setDiskType(AzureDiskType.P30))
            .setDiskSizeGB(AzureDiskType.P30.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(90969, estimateSvc.estimateCentsForAzureCluster(desc3));
    final ClusterDescription desc4 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setReplicationSpecList(
                getReplicationSpecs(
                    1,
                    1,
                    AzureRegionName.US_EAST,
                    AzureRegionName.EUROPE_NORTH,
                    AzureRegionName.AUSTRALIA_SOUTH_EAST))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M30)
                    .setDiskType(AzureDiskType.P30))
            .setDiskSizeGB(AzureDiskType.P30.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(97452, estimateSvc.estimateCentsForAzureCluster(desc4));
    // asymmetric cluster (base: M30, analytics: M10)
    final ClusterDescription desc5 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setReplicationSpecList(getReplicationSpecs(1, 3, 1, 0, CloudProvider.AZURE, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M30)
                    .setDiskType(AzureDiskType.P10),
                NodeTypeFamily.BASE)
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M10)
                    .setDiskType(AzureDiskType.P10),
                NodeTypeFamily.ANALYTICS)
            .setDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.ANALYTICS)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(49338, estimateSvc.estimateCentsForAzureCluster(desc5));
  }

  @Test
  public void testEstimateCentsForAzureCluster_NVMe_Pv1() {
    final AzureRegionName region = AzureRegionName.US_EAST;
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 1.2009).build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_INSTANCE_M60_NVME), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(345.40, 2048))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P40), any(Date.class), eq(PricingConsumers.CLUSTERS));
    final ClusterDescription desc1 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setReplicationSpecList(getReplicationSpecs(1, 3, 0, 1, CloudProvider.AZURE, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M60_NVME)
                    .setDiskType(AzureDiskType.P40))
            .setDiskSizeGB(AzureNDSInstanceSize.M60_NVME.getDefaultDiskSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(385201, estimateSvc.estimateCentsForAzureCluster(desc1));
  }

  @Test
  public void testEstimateCentsForAzureCluster_NVMe_Pv2() {
    final AzureRegionName region = AzureRegionName.US_EAST;
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 1.2009).build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_INSTANCE_M60_NVME), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.161))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_STANDARD_STORAGE), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.0036).build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_EXTENDED_STANDARD_IOPS),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    final ClusterDescription desc1 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setReplicationSpecList(getReplicationSpecs(1, 3, 0, 1, CloudProvider.AZURE, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M60_NVME)
                    .setDiskType(AzureDiskType.V2))
            .setDiskSizeGB(AzureNDSInstanceSize.M60_NVME.getDefaultDiskSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(376421, estimateSvc.estimateCentsForAzureCluster(desc1));
  }

  @Test
  public void testEstimateCentsForAzureStorageIOPS() {
    AzureRegionName region = AzureRegionName.US_EAST_2;
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.007))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_EXTENDED_STANDARD_IOPS),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    // Pv1
    for (final AzureDiskType diskType : AzureDiskType.getDiskTypes(AzureStorageType.PREMIUM)) {
      assertEquals(
          0,
          estimateSvc.estimateCentsForAzureStorageIOPS(
              0, 10, AzureNDSInstanceSize.M40, diskType, diskType.getIOPS(), region));
      assertEquals(
          0,
          estimateSvc.estimateCentsForAzureStorageIOPS(
              3, 64, AzureNDSInstanceSize.M40, diskType, diskType.getIOPS(), region));
    }

    // Pv2, instance count = 0
    assertEquals(
        0,
        estimateSvc.estimateCentsForAzureStorageIOPS(
            0,
            128,
            AzureNDSInstanceSize.M200,
            AzureDiskType.V2,
            AzureDiskType.MAX_SSD_V2_IOPS,
            region));

    // Pv2, disk iops below standard iops for instance size
    assertEquals(
        0,
        estimateSvc.estimateCentsForAzureStorageIOPS(
            1,
            128,
            AzureNDSInstanceSize.M200,
            AzureDiskType.V2,
            AzureDiskType.MIN_SSD_V2_IOPS,
            region));

    // Pv2, disk iops equal to standard iops for instance size
    assertEquals(
        0,
        estimateSvc.estimateCentsForAzureStorageIOPS(
            3, 64, AzureNDSInstanceSize.M30, AzureDiskType.V2, 3200, region));

    // Pv2, disk iops above standard iops for instance size
    assertEquals(
        182,
        estimateSvc.estimateCentsForAzureStorageIOPS(
            1, 64, AzureNDSInstanceSize.M40, AzureDiskType.V2, 4000, region));
    assertEquals(
        546,
        estimateSvc.estimateCentsForAzureStorageIOPS(
            3, 64, AzureNDSInstanceSize.M40, AzureDiskType.V2, 4000, region));
  }

  @Test
  public void testEstimateCentsForAzureCluster_nodeTypeSet() {
    final AzureRegionName region = AzureRegionName.EUROPE_WEST;

    final ClusterDescription desc1 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, 0, 1, CloudProvider.AZURE, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AzureHardwareSpec.Builder().setInstanceSize(AzureNDSInstanceSize.M30),
                NodeTypeFamily.ANALYTICS)
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AzureHardwareSpec.Builder().setInstanceSize(AzureNDSInstanceSize.M10),
                NodeTypeFamily.BASE)
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();

    final long analyticsNodesEstimate =
        estimateSvc.estimateCentsForAWSCluster(desc1, Set.of(NodeType.ANALYTICS));
    final long baseNodesEstimate =
        estimateSvc.estimateCentsForAWSCluster(desc1, Set.of(NodeType.ELECTABLE));
    final long totalNodesEstimate =
        estimateSvc.estimateCentsForAWSCluster(
            desc1, Set.of(NodeType.ANALYTICS, NodeType.ELECTABLE));
    assertEquals(totalNodesEstimate, analyticsNodesEstimate + baseNodesEstimate);
  }

  @Test
  public void testEstimateCentsForAzureShardedCluster_NVMe_Pv1() {
    final AzureRegionName region = AzureRegionName.US_EAST;
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.0622).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_INSTANCE_M20), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 1.2009).build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_INSTANCE_M60_NVME), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(345.40, 2048))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P40), any(Date.class), eq(PricingConsumers.CLUSTERS));

    // M30, P10, 2 shards, 3 replications
    final ShardedClusterDescription desc1 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAzureConfigServerReplicationSpec(region, AzureDiskType.P40), false)
            .setDedicatedConfigServerDiskSizeGB(AzureDiskType.P40.getSizeGB())
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, 0, 1, CloudProvider.AZURE, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M60_NVME)
                    .setDiskType(AzureDiskType.P40))
            .setDiskSizeGB(AzureNDSInstanceSize.M60_NVME.getDefaultDiskSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(887644, estimateSvc.estimateCentsForAzureCluster(desc1));
  }

  @Test
  public void testEstimateCentsForAzureShardedCluster_NVMe_Pv2() {
    final AzureRegionName region = AzureRegionName.US_EAST;
    final int configDiskSizeGB = 2048;
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.0622).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_INSTANCE_M20), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 1.2009).build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_INSTANCE_M60_NVME), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.007))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_EXTENDED_STANDARD_IOPS),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.161))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_STANDARD_STORAGE), any(Date.class), eq(PricingConsumers.CLUSTERS));

    // M30, P10, 2 shards, 3 replications
    final ShardedClusterDescription desc1 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAzureConfigServerReplicationSpec(region, AzureDiskType.V2), false)
            .setDedicatedConfigServerDiskSizeGB(configDiskSizeGB)
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, 0, 1, CloudProvider.AZURE, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M60_NVME)
                    .setDiskType(AzureDiskType.V2))
            .setDiskSizeGB(AzureNDSInstanceSize.M60_NVME.getDefaultDiskSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(865383, estimateSvc.estimateCentsForAzureCluster(desc1));
  }

  @Test
  public void testEstimateCentsForAzureShardedCluster() {
    final AzureRegionName region = AzureRegionName.US_EAST;
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.0264).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_INSTANCE_M10), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, 0.0622)
                .regionUnitPriceDollars(AzureRegionName.EUROPE_NORTH, 0.0777)
                .regionUnitPriceDollars(AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.0804)
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_INSTANCE_M20), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, 0.1685)
                .regionUnitPriceDollars(AzureRegionName.EUROPE_NORTH, 0.1988)
                .regionUnitPriceDollars(AzureRegionName.AUSTRALIA_SOUTH_EAST, 0.2270)
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.3288).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_INSTANCE_M40), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.6483).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_INSTANCE_M50), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 1.2304).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_INSTANCE_M60), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(7.04, 32))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P4), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(26.28, 128))
                .regionUnitPriceDollars(
                    AzureRegionName.EUROPE_NORTH, monthlyToHourlyCostPerGB(26.28, 128))
                .regionUnitPriceDollars(
                    AzureRegionName.AUSTRALIA_SOUTH_EAST, monthlyToHourlyCostPerGB(26.28, 128))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P10), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(97.63, 512))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P20), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCostPerGB(180.23, 1024))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AZURE_STORAGE_P30), any(Date.class), eq(PricingConsumers.CLUSTERS));

    // M30, P10, 2 shards, 3 replications
    final ShardedClusterDescription desc1 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAzureConfigServerReplicationSpec(region, AzureDiskType.P10), false)
            .setDedicatedConfigServerDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M30)
                    .setDiskType(AzureDiskType.P10))
            .setDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(111074, estimateSvc.estimateCentsForAzureCluster(desc1));
    // asymmetric sharded cluster - base: M30, analytics: M10, P10, 2 shards, 3 replications
    final ShardedClusterDescription desc2 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getAzureConfigServerReplicationSpec(region, AzureDiskType.P10), false)
            .setDedicatedConfigServerDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, 1, 0, CloudProvider.AZURE, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M30)
                    .setDiskType(AzureDiskType.P10),
                NodeTypeFamily.BASE)
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M10)
                    .setDiskType(AzureDiskType.P10),
                NodeTypeFamily.ANALYTICS)
            .setDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.ANALYTICS)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(120183, estimateSvc.estimateCentsForAzureCluster(desc2));
    // M30, P20, 2 shards, 3 replications
    final ShardedClusterDescription desc3 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerDiskSizeGB(AzureDiskType.P4.getSizeGB())
            .setDedicatedConfigServerReplicationSpec(
                getAzureConfigServerReplicationSpec(region, AzureDiskType.P4), false)
            .setReplicationSpecList(getReplicationSpecs(2, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setDiskType(AzureDiskType.P20)
                    .setInstanceSize(AzureNDSInstanceSize.M30))
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setDiskSizeGB(AzureDiskType.P20.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(148113, estimateSvc.estimateCentsForAzureCluster(desc3));
    // M30, P30, 2 shards, 3 replications
    final ShardedClusterDescription desc4 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerDiskSizeGB(AzureDiskType.P4.getSizeGB())
            .setDedicatedConfigServerReplicationSpec(
                getAzureConfigServerReplicationSpec(region, AzureDiskType.P4), false)
            .setReplicationSpecList(getReplicationSpecs(2, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setDiskType(AzureDiskType.P30)
                    .setInstanceSize(AzureNDSInstanceSize.M30))
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setDiskSizeGB(AzureDiskType.P30.getSizeGB())
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(197673, estimateSvc.estimateCentsForAzureCluster(desc4));
    // M30, P10, 3 shards, 3 replications
    final ShardedClusterDescription desc5 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setDedicatedConfigServerReplicationSpec(
                getAzureConfigServerReplicationSpec(region, AzureDiskType.P10), false)
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(3, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M30)
                    .setDiskType(AzureDiskType.P10))
            .setDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(155860, estimateSvc.estimateCentsForAzureCluster(desc5));
    // M30, P10, 3 shards, 3 replications across 3 regions
    final ShardedClusterDescription desc6 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setDedicatedConfigServerReplicationSpec(
                getConfigServerReplicationSpec(
                    getAzureRegionConfig(
                        AzureRegionName.US_EAST, RegionConfig.MAX_PRIORITY, 1, AzureDiskType.P10),
                    getAzureRegionConfig(
                        AzureRegionName.EUROPE_NORTH,
                        RegionConfig.MAX_PRIORITY - 1,
                        1,
                        AzureDiskType.P10),
                    getAzureRegionConfig(
                        AzureRegionName.AUSTRALIA_SOUTH_EAST,
                        RegionConfig.MAX_PRIORITY - 2,
                        1,
                        AzureDiskType.P10)),
                false)
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(
                getReplicationSpecs(
                    3,
                    1,
                    AzureRegionName.US_EAST,
                    AzureRegionName.EUROPE_NORTH,
                    AzureRegionName.AUSTRALIA_SOUTH_EAST))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M30)
                    .setDiskType(AzureDiskType.P10))
            .setDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(177762, estimateSvc.estimateCentsForAzureCluster(desc6));
    // ISS = 3 M60 + 1 M50 + 3 M40 + 1 M30
    final ReplicationSpec issAsymmetricShard0 =
        getReplicationSpecs(1, 3, 1, 0, CloudProvider.AZURE, AzureRegionName.US_EAST).get(0);
    final ReplicationSpec issAsymmetricShard1 =
        getReplicationSpecs(1, 3, 1, 0, CloudProvider.AZURE, AzureRegionName.US_EAST).get(0);
    final ShardedClusterDescription desc7 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setDedicatedConfigServerReplicationSpec(
                getConfigServerReplicationSpec(
                    getAzureRegionConfig(
                        AzureRegionName.US_EAST, RegionConfig.MAX_PRIORITY, 1, AzureDiskType.P10),
                    getAzureRegionConfig(
                        AzureRegionName.EUROPE_NORTH,
                        RegionConfig.MAX_PRIORITY - 1,
                        1,
                        AzureDiskType.P10),
                    getAzureRegionConfig(
                        AzureRegionName.AUSTRALIA_SOUTH_EAST,
                        RegionConfig.MAX_PRIORITY - 2,
                        1,
                        AzureDiskType.P10)),
                false)
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(List.of(issAsymmetricShard0, issAsymmetricShard1))
            .updateHardwareForNodeType(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M60)
                    .setDiskType(AzureDiskType.P10),
                NodeTypeFamily.BASE,
                issAsymmetricShard0.getId())
            .updateHardwareForNodeType(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M50)
                    .setDiskType(AzureDiskType.P10),
                NodeTypeFamily.ANALYTICS,
                issAsymmetricShard0.getId())
            .updateHardwareForNodeType(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M40)
                    .setDiskType(AzureDiskType.P10),
                NodeTypeFamily.BASE,
                issAsymmetricShard1.getId())
            .updateHardwareForNodeType(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M30)
                    .setDiskType(AzureDiskType.P10),
                NodeTypeFamily.ANALYTICS,
                issAsymmetricShard1.getId())
            .setDiskSizeGB(AzureDiskType.P10.getSizeGB())
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.AZURE,
                AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(435561, estimateSvc.estimateCentsForAzureCluster(desc7));
  }

  @Test
  public void testGetAzureServerlessCostEstimate() {
    AzureRegionName regionName = AzureRegionName.US_EAST_2;
    Map<SKU, SkuPricing> pricingBySku =
        Map.of(
            SKU.NDS_AZURE_SERVERLESS_RPU,
            new SkuPricing.Builder()
                .tieredPricingQuantityHardCap(20550.0)
                .tieredPricingQuantities(50, 550)
                .tieredRegionUnitPriceDollars(regionName, 0.100, 0.050, 0.010)
                .build(),
            SKU.NDS_AZURE_SERVERLESS_WPU,
            new SkuPricing.Builder().regionUnitPriceDollars(regionName, 1.000).build(),
            SKU.NDS_AZURE_SERVERLESS_STORAGE,
            new SkuPricing.Builder()
                .regionUnitPriceDollars(regionName, monthlyToDailyCost(0.276))
                .build(),
            SKU.NDS_AZURE_SERVERLESS_CONTINUOUS_BACKUP,
            new SkuPricing.Builder()
                .regionUnitPriceDollars(regionName, monthlyToDailyCost(0.221))
                .build());
    when(skuPricingSvc.getPricing(
            eq(
                List.of(
                    SKU.NDS_AZURE_SERVERLESS_RPU,
                    SKU.NDS_AZURE_SERVERLESS_WPU,
                    SKU.NDS_AZURE_SERVERLESS_STORAGE,
                    SKU.NDS_AZURE_SERVERLESS_CONTINUOUS_BACKUP)),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS)))
        .thenReturn(pricingBySku);
    int tier = 0;
    for (final String tieredReadString :
        List.of("tier1ReadCents", "tier2ReadCents", "tier3ReadCents")) {
      tier++;
      assertEquals(
          Long.valueOf(
              (long)
                  (pricingBySku
                          .get(SKU.NDS_AZURE_SERVERLESS_RPU)
                          .getTieredRegionUnitPriceDollars(
                              regionName, tier, SKU.NDS_AZURE_SERVERLESS_RPU)
                      * 100)),
          estimateSvc.getAzureServerlessCostEstimate(regionName, true).get(tieredReadString));
    }
    assertEquals(
        Long.valueOf(
            (long)
                (pricingBySku
                        .get(SKU.NDS_AZURE_SERVERLESS_WPU)
                        .getRegionUnitPriceDollars(regionName, SKU.NDS_AZURE_SERVERLESS_WPU)
                    * 100)),
        estimateSvc.getAzureServerlessCostEstimate(regionName, true).get("serverlessWriteCents"));
    assertEquals(
        Long.valueOf(
            (long)
                (pricingBySku
                        .get(SKU.NDS_AZURE_SERVERLESS_STORAGE)
                        .getRegionUnitPriceDollars(regionName, SKU.NDS_AZURE_SERVERLESS_STORAGE)
                    * 100
                    * 365
                    / 12)),
        estimateSvc.getAzureServerlessCostEstimate(regionName, true).get("serverlessStorageCents"));
    assertEquals(
        Long.valueOf(
            (long)
                (pricingBySku
                        .get(SKU.NDS_AZURE_SERVERLESS_CONTINUOUS_BACKUP)
                        .getRegionUnitPriceDollars(
                            regionName, SKU.NDS_AZURE_SERVERLESS_CONTINUOUS_BACKUP)
                    * 100
                    * 365
                    / 12)),
        estimateSvc
            .getAzureServerlessCostEstimate(regionName, true)
            .get("serverlessContinuousBackupCents"));
  }

  @Test
  public void testEstimateCentsForGCPInstances() {
    GCPRegionName regionName = GCPRegionName.EASTERN_US;
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.1455).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_GCP_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    assertEquals(
        0, estimateSvc.estimateCentsForInstances(0, GCPNDSInstanceSize.M30, false, regionName));
    assertEquals(
        31864, estimateSvc.estimateCentsForInstances(3, GCPNDSInstanceSize.M30, false, regionName));
    assertEquals(
        63729, estimateSvc.estimateCentsForInstances(6, GCPNDSInstanceSize.M30, false, regionName));

    // Paused clusters
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.00621).build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_GCP_INSTANCE_M30_PAUSED), any(Date.class), eq(PricingConsumers.CLUSTERS));
    assertEquals(
        0, estimateSvc.estimateCentsForInstances(0, GCPNDSInstanceSize.M30, true, regionName));
    assertEquals(
        1359, estimateSvc.estimateCentsForInstances(3, GCPNDSInstanceSize.M30, true, regionName));
    assertEquals(
        2719, estimateSvc.estimateCentsForInstances(6, GCPNDSInstanceSize.M30, true, regionName));
  }

  @Test
  public void testEstimateCentsForGCPStorage() {
    GCPRegionName regionName = GCPRegionName.EASTERN_US;
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(regionName, monthlyToHourlyCost(0.2267))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_GCP_STORAGE_SSD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    assertEquals(
        0,
        estimateSvc.estimateCentsForGCPStorage(0, 50, GCPNDSInstanceSize.M30, false, regionName));
    assertEquals(
        0, estimateSvc.estimateCentsForGCPStorage(1, 0, GCPNDSInstanceSize.M30, false, regionName));
    assertEquals(
        0,
        estimateSvc.estimateCentsForGCPStorage(1, 40, GCPNDSInstanceSize.M30, false, regionName));
    assertEquals(
        1133,
        estimateSvc.estimateCentsForGCPStorage(1, 50, GCPNDSInstanceSize.M30, false, regionName));
    assertEquals(
        1360,
        estimateSvc.estimateCentsForGCPStorage(1, 60, GCPNDSInstanceSize.M30, false, regionName));
    assertEquals(
        2267,
        estimateSvc.estimateCentsForGCPStorage(2, 50, GCPNDSInstanceSize.M30, false, regionName));

    // Paused clusters
    assertEquals(
        0, estimateSvc.estimateCentsForGCPStorage(0, 50, GCPNDSInstanceSize.M30, true, regionName));
    assertEquals(
        0, estimateSvc.estimateCentsForGCPStorage(1, 0, GCPNDSInstanceSize.M30, true, regionName));
    assertEquals(
        906,
        estimateSvc.estimateCentsForGCPStorage(1, 40, GCPNDSInstanceSize.M30, true, regionName));
    assertEquals(
        1133,
        estimateSvc.estimateCentsForGCPStorage(1, 50, GCPNDSInstanceSize.M30, true, regionName));
    assertEquals(
        1360,
        estimateSvc.estimateCentsForGCPStorage(1, 60, GCPNDSInstanceSize.M30, true, regionName));
    assertEquals(
        2267,
        estimateSvc.estimateCentsForGCPStorage(2, 50, GCPNDSInstanceSize.M30, true, regionName));
  }

  @Test
  public void testEstimateCentsForGCPCluster() {
    final GCPRegionName region = GCPRegionName.EASTERN_US;
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, 0.1455)
                .regionUnitPriceDollars(GCPRegionName.CENTRAL_US, 0.1455)
                .regionUnitPriceDollars(GCPRegionName.WESTERN_EUROPE, 0.1588)
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_GCP_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.0274).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_GCP_INSTANCE_M10), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.2267))
                .regionUnitPriceDollars(GCPRegionName.CENTRAL_US, monthlyToHourlyCost(0.2267))
                .regionUnitPriceDollars(GCPRegionName.WESTERN_EUROPE, monthlyToHourlyCost(0.2267))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_GCP_STORAGE_SSD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    final ClusterDescription desc1 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30))
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(31864, estimateSvc.estimateCentsForGCPCluster(desc1));
    final ClusterDescription desc2 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30))
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(50)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(35264, estimateSvc.estimateCentsForGCPCluster(desc2));
    final ClusterDescription desc3 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 5, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30))
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(53107, estimateSvc.estimateCentsForGCPCluster(desc3));
    final ClusterDescription desc4 =
        new ClusterDescription.Builder<>()
            .setReplicationSpecList(
                getReplicationSpecs(
                    1,
                    1,
                    GCPRegionName.EASTERN_US,
                    GCPRegionName.CENTRAL_US,
                    GCPRegionName.WESTERN_EUROPE))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30))
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(32834, estimateSvc.estimateCentsForGCPCluster(desc4));
    // asymmetric cluster (base: M30, analytics: M10)
    final ClusterDescription desc5 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, 1, 0, CloudProvider.GCP, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30),
                NodeTypeFamily.BASE)
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M10),
                NodeTypeFamily.ANALYTICS)
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.ANALYTICS)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(34770, estimateSvc.estimateCentsForGCPCluster(desc5));
  }

  @Test
  public void testEstimateCentsForGCPCluster_nodeTypeSet() {
    final GCPRegionName region = GCPRegionName.CENTRAL_US;

    final ClusterDescription desc1 =
        new ClusterDescription.Builder<>()
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(1, 3, 1, 0, CloudProvider.GCP, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30),
                NodeTypeFamily.ANALYTICS)
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M10),
                NodeTypeFamily.BASE)
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(40)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();

    final long analyticsNodesEstimate =
        estimateSvc.estimateCentsForAWSCluster(desc1, Set.of(NodeType.ANALYTICS));
    final long baseNodesEstimate =
        estimateSvc.estimateCentsForAWSCluster(desc1, Set.of(NodeType.ELECTABLE));
    final long totalNodesEstimate =
        estimateSvc.estimateCentsForAWSCluster(
            desc1, Set.of(NodeType.ANALYTICS, NodeType.ELECTABLE));
    assertEquals(totalNodesEstimate, analyticsNodesEstimate + baseNodesEstimate);
  }

  @Test
  public void testEstimateCentsForGCPShardedCluster() {
    final GCPRegionName region = GCPRegionName.EASTERN_US;
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.0274).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_GCP_INSTANCE_M10), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, 0.1455)
                .regionUnitPriceDollars(GCPRegionName.CENTRAL_US, 0.1455)
                .regionUnitPriceDollars(GCPRegionName.WESTERN_EUROPE, 0.1588)
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_GCP_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.2830).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_GCP_INSTANCE_M40), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 0.5449).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_GCP_INSTANCE_M50), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(region, 1.0814).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_GCP_INSTANCE_M60), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(region, monthlyToHourlyCost(0.2267))
                .regionUnitPriceDollars(GCPRegionName.CENTRAL_US, monthlyToHourlyCost(0.2267))
                .regionUnitPriceDollars(GCPRegionName.WESTERN_EUROPE, monthlyToHourlyCost(0.2267))
                .build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_GCP_STORAGE_SSD), any(Date.class), eq(PricingConsumers.CLUSTERS));
    // M30, 160GB, 2 shards, 3 replications / config: M30, 40GB
    final ShardedClusterDescription desc1 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getGCPConfigServerReplicationSpec(region), false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30))
            .setDiskSizeGB(160)
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(117356, estimateSvc.estimateCentsForGCPCluster(desc1));
    // asymmetric sharded cluster - base: M30, analytics: M10, 160GB, 2 shards, 3 replications /
    // config: M30, 40GB
    final ShardedClusterDescription desc2 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getGCPConfigServerReplicationSpec(region), false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, 1, 0, CloudProvider.GCP, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30),
                NodeTypeFamily.BASE)
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M10),
                NodeTypeFamily.ANALYTICS)
            .setDiskSizeGB(160)
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(128610, estimateSvc.estimateCentsForGCPCluster(desc2));
    // M30, 200GB, 2 shards, 3 replications / config: M30, 40GB
    final ShardedClusterDescription desc3 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getGCPConfigServerReplicationSpec(region), false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setReplicationSpecList(getReplicationSpecs(2, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30))
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(200)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(122797, estimateSvc.estimateCentsForGCPCluster(desc3));
    // M30, 160GB, 3 shards, 3 replications / config: M30, 40GB
    final ShardedClusterDescription desc4 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getGCPConfigServerReplicationSpec(region), false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setReplicationSpecList(getReplicationSpecs(3, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30))
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(160101, estimateSvc.estimateCentsForGCPCluster(desc4));
    // M30, 160GB, 2 shards, 5 replications / config: M30, 40GB
    final ShardedClusterDescription desc5 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getGCPConfigServerReplicationSpec(region), false)
            .setDedicatedConfigServerDiskSizeGB(40)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 5, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30))
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(174350, estimateSvc.estimateCentsForGCPCluster(desc5));
    // M30, 160GB, 2 shards, 3 replications / config: M30, 50GB
    final ShardedClusterDescription desc6 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getGCPConfigServerReplicationSpec(region), false)
            .setDedicatedConfigServerDiskSizeGB(50)
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setReplicationSpecList(getReplicationSpecs(2, 3, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30))
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(120756, estimateSvc.estimateCentsForGCPCluster(desc6));
    // M30, 160GB, 2 shards, 3 replications across 3 regions / config: M30, 50GB
    final ShardedClusterDescription desc7 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getConfigServerReplicationSpec(
                    getGCPRegionConfig(GCPRegionName.EASTERN_US, RegionConfig.MAX_PRIORITY, 1),
                    getGCPRegionConfig(GCPRegionName.CENTRAL_US, RegionConfig.MAX_PRIORITY - 1, 1),
                    getGCPRegionConfig(
                        GCPRegionName.WESTERN_EUROPE, RegionConfig.MAX_PRIORITY - 2, 1)),
                false)
            .setDedicatedConfigServerDiskSizeGB(50)
            .setReplicationSpecList(
                getReplicationSpecs(
                    2,
                    1,
                    GCPRegionName.EASTERN_US,
                    GCPRegionName.CENTRAL_US,
                    GCPRegionName.WESTERN_EUROPE))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30))
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(123663, estimateSvc.estimateCentsForGCPCluster(desc7));
    // ISS = 3 M60 + 1 M50 + 3 M40 + 1 M30
    final ReplicationSpec issAsymmetricShard0 =
        getReplicationSpecs(1, 3, 1, 0, CloudProvider.GCP, GCPRegionName.EASTERN_US).get(0);
    final ReplicationSpec issAsymmetricShard1 =
        getReplicationSpecs(1, 3, 1, 0, CloudProvider.GCP, GCPRegionName.EASTERN_US).get(0);
    final ShardedClusterDescription desc8 =
        new ShardedClusterDescription.Builder()
            .setDedicatedConfigServerReplicationSpec(
                getConfigServerReplicationSpec(
                    getGCPRegionConfig(GCPRegionName.EASTERN_US, RegionConfig.MAX_PRIORITY, 3)),
                false)
            .setDedicatedConfigServerDiskSizeGB(50)
            .setReplicationSpecList(List.of(issAsymmetricShard0, issAsymmetricShard1))
            .updateHardwareForNodeType(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M60),
                NodeTypeFamily.BASE,
                issAsymmetricShard0.getId())
            .updateHardwareForNodeType(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M50),
                NodeTypeFamily.ANALYTICS,
                issAsymmetricShard0.getId())
            .updateHardwareForNodeType(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M40),
                NodeTypeFamily.BASE,
                issAsymmetricShard1.getId())
            .updateHardwareForNodeType(
                new GCPHardwareSpec.Builder().setInstanceSize(GCPNDSInstanceSize.M30),
                NodeTypeFamily.ANALYTICS,
                issAsymmetricShard1.getId())
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(160)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setRestoreJobIds(Collections.emptyList())
            .setAutoScalingForProvider(
                CloudProvider.GCP,
                GCPAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(398972, estimateSvc.estimateCentsForGCPCluster(desc8));
  }

  @Test
  public void testGetGCPServerlessCostEstimate() {
    GCPRegionName regionName = GCPRegionName.CENTRAL_US;
    Map<SKU, SkuPricing> pricingBySku =
        Map.of(
            SKU.NDS_GCP_SERVERLESS_RPU,
            new SkuPricing.Builder()
                .tieredPricingQuantityHardCap(20550.0)
                .tieredPricingQuantities(50, 550)
                .tieredRegionUnitPriceDollars(regionName, 0.095, 0.047, 0.009)
                .build(),
            SKU.NDS_GCP_SERVERLESS_WPU,
            new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.949).build(),
            SKU.NDS_GCP_SERVERLESS_STORAGE,
            new SkuPricing.Builder()
                .regionUnitPriceDollars(regionName, monthlyToDailyCost(0.302))
                .build(),
            SKU.NDS_GCP_SERVERLESS_CONTINUOUS_BACKUP,
            new SkuPricing.Builder()
                .regionUnitPriceDollars(regionName, monthlyToDailyCost(0.242))
                .build());
    when(skuPricingSvc.getPricing(
            eq(
                List.of(
                    SKU.NDS_GCP_SERVERLESS_RPU,
                    SKU.NDS_GCP_SERVERLESS_WPU,
                    SKU.NDS_GCP_SERVERLESS_STORAGE,
                    SKU.NDS_GCP_SERVERLESS_CONTINUOUS_BACKUP)),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS)))
        .thenReturn(pricingBySku);

    int tier = 0;
    for (final String tieredReadString :
        List.of("tier1ReadCents", "tier2ReadCents", "tier3ReadCents")) {
      tier++;
      assertEquals(
          Long.valueOf(
              (long)
                  (pricingBySku
                          .get(SKU.NDS_GCP_SERVERLESS_RPU)
                          .getTieredRegionUnitPriceDollars(
                              regionName, tier, SKU.NDS_GCP_SERVERLESS_RPU)
                      * 100)),
          estimateSvc.getGCPServerlessCostEstimate(regionName, true).get(tieredReadString));
    }
    assertEquals(
        Long.valueOf(
            (long)
                (pricingBySku
                        .get(SKU.NDS_GCP_SERVERLESS_WPU)
                        .getRegionUnitPriceDollars(regionName, SKU.NDS_GCP_SERVERLESS_WPU)
                    * 100)),
        estimateSvc.getGCPServerlessCostEstimate(regionName, true).get("serverlessWriteCents"));
    assertEquals(
        Long.valueOf(
            (long)
                (pricingBySku
                        .get(SKU.NDS_GCP_SERVERLESS_STORAGE)
                        .getRegionUnitPriceDollars(regionName, SKU.NDS_GCP_SERVERLESS_STORAGE)
                    * 100
                    * 365
                    / 12)),
        estimateSvc.getGCPServerlessCostEstimate(regionName, true).get("serverlessStorageCents"));
    assertEquals(
        Long.valueOf(
            (long)
                (pricingBySku
                        .get(SKU.NDS_GCP_SERVERLESS_CONTINUOUS_BACKUP)
                        .getRegionUnitPriceDollars(
                            regionName, SKU.NDS_GCP_SERVERLESS_CONTINUOUS_BACKUP)
                    * 100
                    * 365
                    / 12)),
        estimateSvc
            .getGCPServerlessCostEstimate(regionName, true)
            .get("serverlessContinuousBackupCents"));
  }

  @Test
  public void testEstimateCentsForFreeCluster() {
    final AWSRegionName region = AWSRegionName.US_EAST_1;
    doReturn(new SkuPricing.Builder().unitPriceDollars(0.0).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_FREE_INSTANCE_M0), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().unitPriceDollars(0.004109).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_FREE_INSTANCE_M2), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(new SkuPricing.Builder().unitPriceDollars(monthlyToHourlyCost(25) / 3).build())
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_FREE_INSTANCE_M5), any(Date.class), eq(PricingConsumers.CLUSTERS));

    final ClusterDescription desc1 =
        new ClusterDescription.Builder<>()
            .setReplicationSpecList(getReplicationSpecs(1, 3, CloudProvider.FREE, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new FreeHardwareSpec.Builder()
                    .setBackingCloudProvider(CloudProvider.AWS)
                    .setInstanceSize(FreeInstanceSize.M0))
            .setFreeTenantProviderOptions(
                new FreeTenantProviderOptions(new BasicDBObject(), FreeInstanceSize.M0))
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setDiskSizeGB(0)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setAutoScalingForProvider(
                CloudProvider.FREE,
                FreeAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(0, estimateSvc.estimateCentsForFreeCluster(desc1));
    final ClusterDescription desc2 =
        new ClusterDescription.Builder<>()
            .setReplicationSpecList(getReplicationSpecs(1, 3, CloudProvider.FREE, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new FreeHardwareSpec.Builder()
                    .setBackingCloudProvider(CloudProvider.AWS)
                    .setInstanceSize(FreeInstanceSize.M2))
            .setFreeTenantProviderOptions(
                new FreeTenantProviderOptions(new BasicDBObject(), FreeInstanceSize.M2))
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(2)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setAutoScalingForProvider(
                CloudProvider.FREE,
                FreeAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(899, estimateSvc.estimateCentsForFreeCluster(desc2));
    final ClusterDescription desc3 =
        new ClusterDescription.Builder<>()
            .setReplicationSpecList(getReplicationSpecs(1, 3, CloudProvider.FREE, region))
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new FreeHardwareSpec.Builder()
                    .setBackingCloudProvider(CloudProvider.AWS)
                    .setInstanceSize(FreeInstanceSize.M5))
            .setFreeTenantProviderOptions(
                new FreeTenantProviderOptions(new BasicDBObject(), FreeInstanceSize.M5))
            .setName("test")
            .setMongoDBVersion("3.2.0")
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .setInternalClusterRole(InternalClusterRole.NONE)
            .setDiskSizeGB(5)
            .setMongoUriHosts(
                new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
            .setPrivateMongoUriHosts(
                new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
            .setState(ClusterDescription.State.IDLE)
            .setAutoScalingForProvider(
                CloudProvider.FREE,
                FreeAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
                NodeTypeFamily.BASE)
            .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
            .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
            .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
            .build();
    assertEquals(2500, estimateSvc.estimateCentsForFreeCluster(desc3));
  }

  @Test
  public void testEstimateCentsForAWSSnapshots() {
    // Mock AWS backup storage pricing
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AWSRegionName.US_EAST_1, 0.02)
                .regionUnitPriceDollars(AWSRegionName.US_EAST_2, 0.03)
                .regionUnitPriceDollars(AWSRegionName.AP_SOUTHEAST_1, 0.04)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    // Also mock other cloud provider backup storage in case they're called
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(GCPRegionName.EASTERN_US, 0.004602739726)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AzureRegionName.US_EAST, 0.004602739726)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    testEstimateCentsForBackupRegion(estimateSvc, CloudProvider.AWS, 0.02, AWSRegionName.US_EAST_1);
    testEstimateCentsForBackupRegion(estimateSvc, CloudProvider.AWS, 0.03, AWSRegionName.US_EAST_2);

    // Test multi region deployment (highest region price)
    // US_EAST_1 = 0.14 & AP_SOUTHEAST_2 = 0.16 = 0.16
    testEstimateCentsForBackupRegion(
        estimateSvc,
        CloudProvider.AWS,
        0.04,
        AWSRegionName.US_EAST_1,
        AWSRegionName.AP_SOUTHEAST_1);
  }

  @Test
  public void testEstimateCentsForAzureSnapshots() {
    // Mock Azure backup storage pricing
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AzureRegionName.US_CENTRAL, 0.02)
                .regionUnitPriceDollars(AzureRegionName.CANADA_EAST, 0.03)
                .regionUnitPriceDollars(AzureRegionName.EUROPE_WEST, 0.04)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    // Also mock other cloud provider backup storage in case they're called
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AWSRegionName.US_EAST_1, 0.004602739726)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(GCPRegionName.EASTERN_US, 0.004602739726)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    testEstimateCentsForBackupRegion(
        estimateSvc, CloudProvider.AZURE, 0.02, AzureRegionName.US_CENTRAL);
    testEstimateCentsForBackupRegion(
        estimateSvc, CloudProvider.AZURE, 0.03, AzureRegionName.CANADA_EAST);

    // Test multi region deployment (highest region price)
    // US_CENTRAL = 0.38 & EUROPE_WEST = 0.41 = 0.41
    testEstimateCentsForBackupRegion(
        estimateSvc,
        CloudProvider.AZURE,
        0.04,
        AzureRegionName.US_CENTRAL,
        AzureRegionName.EUROPE_WEST);
  }

  private void testEstimateCentsForBackupRegion(
      final EstimateSvcImpl estimateSvc,
      final CloudProvider cloudProvider,
      final double expectedBackupPrice,
      final RegionName... regionNames) {
    final ClusterDescription clusterDescription = getClusterDescription(cloudProvider, regionNames);
    assertEquals(
        (long) (expectedBackupPrice * 365 / 12 * 100),
        estimateSvc.estimateCentsForProviderSnapshots(clusterDescription),
        "Backup region cost failed for regions "
            + Arrays.stream(regionNames).map(RegionName::getName).collect(Collectors.joining()));
  }

  private ClusterDescription getClusterDescription(
      final CloudProvider cloudProvider, final RegionName... regionNames) {
    if (cloudProvider == CloudProvider.AWS) {
      return new ClusterDescription.Builder<>()
          .setName("test")
          .setMongoDBVersion("3.6.0")
          .setClusterType(ClusterDescription.ClusterType.REPLICASET)
          .setInternalClusterRole(InternalClusterRole.NONE)
          .setReplicationSpecList(getReplicationSpecs(1, 3, regionNames))
          .mixin(ClusterDescriptionBuilderTestMixin::new)
          .updateAllHardware(
              new AWSHardwareSpec.Builder()
                  .setInstanceSize(AWSNDSInstanceSize.M30)
                  .setDiskIOPS(120))
          .setDiskSizeGB(40)
          .setMongoUriHosts(new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
          .setPrivateMongoUriHosts(
              new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
          .setState(ClusterDescription.State.IDLE)
          .setRestoreJobIds(Collections.emptyList())
          .setAutoScalingForProvider(
              CloudProvider.AWS,
              AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
              NodeTypeFamily.BASE)
          .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
          .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
          .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
          .setHostnameSchemeForAgents(HostnameScheme.INTERNAL)
          .setGroupId(oid(42))
          .setClusterNamePrefix("TestClusterNamePrefix")
          .build();
    } else if (cloudProvider == CloudProvider.AZURE) {
      return new ClusterDescription.Builder<>()
          .setName("test")
          .setMongoDBVersion("3.6.0")
          .setClusterType(ClusterDescription.ClusterType.REPLICASET)
          .setInternalClusterRole(InternalClusterRole.NONE)
          .setReplicationSpecList(getReplicationSpecs(1, 3, regionNames))
          .mixin(ClusterDescriptionBuilderTestMixin::new)
          .updateAllHardware(
              new AzureHardwareSpec.Builder()
                  .setInstanceSize(AzureNDSInstanceSize.M30)
                  .setDiskType(AzureDiskType.P10))
          .setDiskSizeGB(AzureDiskType.P10.getSizeGB())
          .setMongoUriHosts(new String[] {"db1.example.com", "db2.example.com", "db3.example.com"})
          .setPrivateMongoUriHosts(
              new String[] {"db1-pri.example.com", "db2-pri.example.com", "db3-pri.example.com"})
          .setState(ClusterDescription.State.IDLE)
          .setRestoreJobIds(Collections.emptyList())
          .setAutoScalingForProvider(
              CloudProvider.AZURE,
              AzureAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
              NodeTypeFamily.BASE)
          .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
          .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
          .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
          .setHostnameSchemeForAgents(HostnameScheme.INTERNAL)
          .setGroupId(oid(42))
          .build();
    }

    throw new AssertionError("Fix this test that is looking for a different cloud provider.");
  }

  private ReplicationSpec getConfigServerReplicationSpec(final RegionConfig... pRegionConfigs) {
    return new ReplicationSpec(
        new ObjectId(),
        new ObjectId(),
        new ObjectId(),
        NDSDefaults.ZONE_NAME,
        1,
        Arrays.stream(pRegionConfigs).collect(Collectors.toList()));
  }

  private ReplicationSpec getAWSConfigServerReplicationSpec(
      final AWSRegionName region,
      final AWSNDSInstanceSize m20,
      final int pNodeCount,
      final VolumeType pVolumeType) {
    return getConfigServerReplicationSpec(
        getAWSRegionConfig(region, RegionConfig.MAX_PRIORITY, 3, m20, pNodeCount, pVolumeType));
  }

  private ReplicationSpec getAzureConfigServerReplicationSpec(
      final AzureRegionName pRegion, final AzureDiskType pDisk) {
    return getConfigServerReplicationSpec(
        getAzureRegionConfig(pRegion, RegionConfig.MAX_PRIORITY, 3, pDisk));
  }

  private ReplicationSpec getGCPConfigServerReplicationSpec(final GCPRegionName pRegion) {
    return getConfigServerReplicationSpec(
        getGCPRegionConfig(pRegion, RegionConfig.MAX_PRIORITY, 3));
  }

  private RegionConfig getAWSRegionConfig(
      final AWSRegionName pRegion,
      final int pPriority,
      final int pNodeCount,
      final AWSNDSInstanceSize pInstanceSize,
      final int pDiskIOPS,
      final VolumeType pVolumeType) {
    return new RegionConfig(
        pRegion,
        CloudProvider.AWS,
        AWSAutoScaling.getDefaultAutoScaling(),
        null,
        pPriority,
        new AWSHardwareSpec(
            pNodeCount,
            true,
            pInstanceSize,
            AWSInstanceFamily.T3,
            NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                CloudProvider.AWS, AWSInstanceFamily.T3),
            CpuArchitecture.X86_64,
            pDiskIOPS,
            0,
            pVolumeType,
            true),
        null);
  }

  private RegionConfig getAzureRegionConfig(
      final AzureRegionName pRegion,
      final int pPriority,
      final int pNodeCount,
      final AzureDiskType pDiskType) {
    return new RegionConfig(
        pRegion,
        CloudProvider.AZURE,
        AzureAutoScaling.getDefaultAutoScaling(),
        null,
        pPriority,
        new AzureHardwareSpec(
            pNodeCount,
            true,
            AzureNDSInstanceSize.M20,
            AzureInstanceFamily.STANDARD_B,
            NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                CloudProvider.AZURE, AzureInstanceFamily.STANDARD_B),
            CpuArchitecture.X86_64,
            pDiskType,
            0,
            0,
            PreferredStorageType.NONE),
        null);
  }

  private RegionConfig getGCPRegionConfig(
      final GCPRegionName pRegion, final int pPriority, final int pNodeCount) {
    return new RegionConfig(
        pRegion,
        CloudProvider.GCP,
        GCPAutoScaling.getDefaultAutoScaling(),
        null,
        pPriority,
        new GCPHardwareSpec(
            pNodeCount,
            true,
            GCPNDSInstanceSize.M30,
            GCPInstanceFamily.N2,
            NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                CloudProvider.GCP, GCPInstanceFamily.N2),
            CpuArchitecture.X86_64,
            null,
            null),
        null);
  }

  private List<ReplicationSpec> getReplicationSpecs(
      final int pNumShards, final int pNumElectableNodes, final RegionName... pRegionName) {
    return getReplicationSpecs(
        pNumShards, pNumElectableNodes, pRegionName[0].getProvider(), pRegionName);
  }

  private List<ReplicationSpec> getReplicationSpecs(
      final int pNumShards,
      final int pNumElectableNodes,
      final CloudProvider pProvider,
      final RegionName... pRegionName) {
    return getReplicationSpecs(pNumShards, pNumElectableNodes, 0, 0, pProvider, pRegionName);
  }

  private List<ReplicationSpec> getReplicationSpecs(
      final int pNumShards,
      final int pNumElectableNodes,
      final int pNumAnalyticsNodes,
      final int pNumHiddenSecondaryNodes,
      final CloudProvider pProvider,
      final RegionName... pRegionName) {
    final AtomicInteger priority = new AtomicInteger(RegionConfig.MAX_PRIORITY);
    return Collections.singletonList(
        new ReplicationSpec(
            new ObjectId(),
            new ObjectId(),
            new ObjectId(),
            NDSDefaults.ZONE_NAME,
            pNumShards,
            Arrays.stream(pRegionName)
                .map(
                    regionName ->
                        NDSModelTestFactory.getShardRegionConfigForRegion(
                            pProvider,
                            regionName,
                            priority.getAndDecrement(),
                            pNumElectableNodes,
                            pNumAnalyticsNodes,
                            0,
                            pNumHiddenSecondaryNodes))
                .collect(Collectors.toList())));
  }

  @Test
  public void testEstimateBasedOnPastUsage() {
    when(lineItemDao.getNonCreditLineItemsTotal(any(), any())).thenReturn(300d);
    when(groupDao.findGroupIdsByOrgId(any(), anyBoolean())).thenReturn(List.of());
    assertEquals(3000, estimateSvc.estimateCentsBasedOnPastUsage(null, 3));

    when(lineItemDao.getNonCreditLineItemsTotal(any(), any())).thenReturn(0d);
    assertEquals(0, estimateSvc.estimateCentsBasedOnPastUsage(null, 7));
  }

  @Test
  public void testEstimateCentsForAWSClusterSnapshots() {
    final ClusterDescription cluster = mock(ClusterDescription.class);
    final ReplicationSpec spec = mock(ReplicationSpec.class);
    final RegionConfig regionConfig = mock(RegionConfig.class);

    doReturn(1).when(spec).getNumShards();
    doReturn(Collections.singletonList(spec)).when(cluster).getReplicationSpecsWithShardData();
    doReturn(Collections.singletonList(regionConfig)).when(spec).getRegionConfigs();
    doReturn(CloudProvider.AWS).when(regionConfig).getCloudProvider();
    doReturn(AWSRegionName.US_EAST_1).when(regionConfig).getRegionName();
    when(skuPricingSvc.getPricing(
            eq(SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS)))
        .thenReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AWSRegionName.US_EAST_1, 0.004602739726)
                .build());
    when(skuPricingSvc.getPricing(
            eq(SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS)))
        .thenReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(GCPRegionName.EASTERN_US, 0.004602739726)
                .build());
    when(skuPricingSvc.getPricing(
            eq(SKU.NDS_AZURE_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS)))
        .thenReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AzureRegionName.US_EAST, 0.004602739726)
                .build());

    assertEquals(13, estimateSvc.estimateCentsForProviderSnapshots(cluster));

    doReturn(2).when(spec).getNumShards();
    assertEquals(26, estimateSvc.estimateCentsForProviderSnapshots(cluster));
  }

  @Test
  public void testEstimateCentsForAzureClusterSnapshots() {
    // Mock Azure backup storage pricing
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AzureRegionName.US_CENTRAL, 0.01249315068)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    // Also mock other cloud provider backup storage in case they're called
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AWSRegionName.US_EAST_1, 0.004602739726)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(GCPRegionName.EASTERN_US, 0.004602739726)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    final ClusterDescription cluster = mock(ClusterDescription.class);
    final ReplicationSpec spec = mock(ReplicationSpec.class);
    final RegionConfig regionConfig = mock(RegionConfig.class);
    doReturn(1).when(spec).getNumShards();
    doReturn(Collections.singletonList(spec)).when(cluster).getReplicationSpecsWithShardData();
    doReturn(Collections.singletonList(regionConfig)).when(spec).getRegionConfigs();
    doReturn(CloudProvider.AZURE).when(regionConfig).getCloudProvider();
    doReturn(AzureRegionName.US_CENTRAL).when(regionConfig).getRegionName();

    assertEquals(37, estimateSvc.estimateCentsForProviderSnapshots(cluster));

    doReturn(2).when(spec).getNumShards();
    assertEquals(74, estimateSvc.estimateCentsForProviderSnapshots(cluster));
  }

  @Test
  public void testEstimateCentsForGCPClusterSnapshots() {
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(GCPRegionName.EASTERN_US, monthlyToDailyCost(0.08))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    final ClusterDescription cluster = mock(ClusterDescription.class);
    final ReplicationSpec spec = mock(ReplicationSpec.class);
    final RegionConfig regionConfig = mock(RegionConfig.class);

    doReturn(1).when(spec).getNumShards();
    doReturn(Collections.singletonList(spec)).when(cluster).getReplicationSpecsWithShardData();
    doReturn(Collections.singletonList(regionConfig)).when(spec).getRegionConfigs();
    doReturn(CloudProvider.GCP).when(regionConfig).getCloudProvider();
    doReturn(GCPRegionName.EASTERN_US).when(regionConfig).getRegionName();

    assertEquals(
        8,
        estimateSvc.estimateCentsForClusterSnapshots(
            cluster, CloudProvider.GCP, SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE));

    doReturn(2).when(spec).getNumShards();
    assertEquals(
        16,
        estimateSvc.estimateCentsForClusterSnapshots(
            cluster, CloudProvider.GCP, SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE));
  }

  @Test
  public void testEstimateCentsForMonthlyFeesStandard() {
    final ClusterDescription desc =
        getClusterDescription(CloudProvider.AWS, AWSRegionName.SA_EAST_1);

    final long monthlyEstimateCents = 20000L;
    final long monthlyFeesEstimateCents =
        estimateSvc.estimateCentsForMonthlyFees(desc, monthlyEstimateCents);

    final long expectedEstimate = (long) (monthlyEstimateCents * ESTIMATED_FEE_FACTOR.AWS_M30);
    assertTrue(areLongsClose(expectedEstimate, monthlyFeesEstimateCents));
  }

  @Test
  public void testEstimateCentsForMonthlyFeesHighTier() {
    final ClusterDescription desc = mock(ClusterDescription.class);

    doReturn(Optional.of(AWSNDSInstanceSize.M100))
        .when(desc)
        .getMaxInstanceSizeForProvider(NodeType.ELECTABLE, CloudProvider.AWS);

    final long monthlyEstimateCents = 10000L;
    final long monthlyFeesEstimateCents =
        estimateSvc.estimateCentsForMonthlyFees(desc, monthlyEstimateCents);

    final long expectedEstimate = (long) (monthlyEstimateCents * ESTIMATED_FEE_FACTOR.AWS_M40_PLUS);
    assertTrue(areLongsClose(expectedEstimate, monthlyFeesEstimateCents));
  }

  @Test
  public void testEstimateCentsForMonthlyFeesCrossCloud() {
    final ClusterDescription desc = mock(ClusterDescription.class);
    doReturn(Optional.of(AWSNDSInstanceSize.M10))
        .when(desc)
        .getMaxInstanceSizeForProvider(NodeType.ELECTABLE, CloudProvider.AWS);
    doReturn(Optional.of(GCPNDSInstanceSize.M10))
        .when(desc)
        .getMaxInstanceSizeForProvider(NodeType.ELECTABLE, CloudProvider.GCP);
    doReturn(Optional.empty())
        .when(desc)
        .getMaxInstanceSizeForProvider(NodeType.ELECTABLE, CloudProvider.AZURE);

    final long monthlyEstimateCents = 30000L;
    final long monthlyFeesEstimateCents =
        estimateSvc.estimateCentsForMonthlyFees(desc, monthlyEstimateCents);

    final float pctBaseCostAvg = (ESTIMATED_FEE_FACTOR.AWS_M10 + ESTIMATED_FEE_FACTOR.GCP_M10) / 2;
    final long expectedEstimate = (long) (monthlyEstimateCents * pctBaseCostAvg);
    assertTrue(areLongsClose(expectedEstimate, monthlyFeesEstimateCents));
  }

  @Test
  public void testEstimateCentsForMonthlyFeesTenant() {
    final ClusterDescription desc = mock(ClusterDescription.class);
    doReturn(true).when(desc).isTenantCluster();

    final long monthlyEstimateCents = 30000L;
    final long monthlyFeesEstimateCents =
        estimateSvc.estimateCentsForMonthlyFees(desc, monthlyEstimateCents);

    assertEquals(0L, monthlyFeesEstimateCents);
  }

  @Test
  public void testEstimateCentsForMonthlyFeesAverageError() {
    final ClusterDescription desc =
        getClusterDescription(CloudProvider.AWS, AWSRegionName.SA_EAST_1);

    final long monthlyEstimateCents = 20000L;
    final long monthlyFeesEstimateCents =
        estimateSvc.estimateCentsForMonthlyFees(desc, monthlyEstimateCents);

    assertEquals(645L, monthlyFeesEstimateCents);
  }

  @Test
  public void testEstimateCentsForMonthlySearchNodes() throws SvcException {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    double unitPriceDollars = 0.117;
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.117).build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_SEARCH_INSTANCE_S20_COMPUTE_NVME),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    final SearchDeploymentDescription deploymentDescription =
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(2);
    final ClusterDescription clusterDescription =
        getClusterDescription(CloudProvider.AWS, regionName)
            .copy()
            .setMongoDBVersion(MIN_MONGODB_VERSION.getVersion())
            .build();
    {
      final long monthlySearchNodesEstimateCents =
          estimateSvc.estimateCentsForSearchNodes(clusterDescription, deploymentDescription);
      final long skuMonthlyCostInCents =
          getSearchInstanceMonthlyCostInCents(
              unitPriceDollars * 24 * 365 / 12 * 100, estimateSvc.getMultiplier(), 2);
      assertEquals(skuMonthlyCostInCents, monthlySearchNodesEstimateCents);
    }

    {
      // Paused search deployment
      final SearchDeploymentDescription pausedDeploymentDescription =
          deploymentDescription
              .copy()
              .setState(SearchDeploymentDescription.State.PAUSED)
              .setPausedDate(Instant.now())
              .build();
      final long monthlyPausedSearchNodesEstimateCents =
          estimateSvc.estimateCentsForSearchNodes(clusterDescription, pausedDeploymentDescription);
      assertEquals(0, monthlyPausedSearchNodesEstimateCents);
    }
  }

  @Test
  public void testEstimateCentsForMonthlySearchNodesPaused() throws SvcException {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.25).build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_INSTANCE_M30_PAUSED), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(regionName, monthlyToHourlyCost(0.133))
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));

    final SearchDeploymentDescription deploymentDescription =
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(2);
    final ClusterDescription clusterDescription =
        getClusterDescription(CloudProvider.AWS, regionName)
            .copy()
            .setMongoDBVersion(MIN_MONGODB_VERSION.getVersion())
            .setIsPaused(true)
            .build();
    final long monthlyPausedClusterSearchNodesEstimateCents =
        estimateSvc.estimateCentsForSearchNodes(clusterDescription, deploymentDescription);
    assertEquals(0, monthlyPausedClusterSearchNodesEstimateCents);

    long pausedClusterWithoutSearchNodesEstimateCents =
        estimateSvc.estimateCentsForNDSCluster(
            clusterDescription, Optional.empty(), Optional.empty(), Optional.empty());
    long pausedClusterWithSearchNodesEstimateCents =
        estimateSvc.estimateCentsForNDSCluster(
            clusterDescription,
            Optional.of(SearchInstanceSizeAPI.API_INSTANCE_SIZE.S20_HIGHCPU_NVME),
            Optional.of(2),
            Optional.empty());
    assertEquals(
        pausedClusterWithoutSearchNodesEstimateCents, pausedClusterWithSearchNodesEstimateCents);
  }

  @Test
  public void testEstimateCentsForMonthlySearchNodes_shardedCluster() throws SvcException {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    doReturn(new SkuPricing.Builder().regionUnitPriceDollars(regionName, 0.117).build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_SEARCH_INSTANCE_S20_COMPUTE_NVME),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    final SearchDeploymentDescription deploymentDescription =
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(2);
    final ClusterDescription shardedClusterDescription =
        getClusterDescription(CloudProvider.AWS, regionName)
            .copy()
            .setMongoDBVersion(MIN_MONGODB_VERSION.getVersion())
            .setReplicationSpecList(getReplicationSpecs(3, 3, regionName))
            .build();
    final long monthlySearchNodesEstimateCents =
        estimateSvc.estimateCentsForSearchNodes(shardedClusterDescription, deploymentDescription);

    final long skuMonthlyCostInCents =
        getSearchInstanceMonthlyCostInCents(
            SkuUtils.getSkuMonthlyCostInCents(
                SKU.NDS_AWS_SEARCH_INSTANCE_S20_COMPUTE_NVME, regionName),
            estimateSvc.getMultiplier(),
            2 * 3);

    assertEquals(skuMonthlyCostInCents, monthlySearchNodesEstimateCents);
  }

  @Test
  public void testEstimateCentsForMonthlySearchNodes_multiRegionCluster() throws SvcException {
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(AWSRegionName.US_EAST_1, 0.117)
                .regionUnitPriceDollars(AWSRegionName.US_EAST_2, 0.116)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_SEARCH_INSTANCE_S20_COMPUTE_NVME),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));

    final MockedStatic<FeatureFlagSvc> mockFeatureFlagSvc = mockStatic(FeatureFlagSvc.class);

    // create a single region cluster on AWS.US_EAST_1 and get cost estimates
    final SearchDeploymentDescription deploymentDescription1 =
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(
            new ObjectId(), new ObjectId(), 2, List.of(AWSRegionName.US_EAST_1), null);
    final ClusterDescription clusterDescription1 =
        getClusterDescription(CloudProvider.AWS, AWSRegionName.US_EAST_1)
            .copy()
            .setMongoDBVersion(MIN_MONGODB_VERSION.getVersion())
            .setReplicationSpecList(getReplicationSpecs(1, 3, AWSRegionName.US_EAST_1))
            .build();
    final long monthlySearchNodesEstimateCents1 =
        estimateSvc.estimateCentsForSearchNodes(clusterDescription1, deploymentDescription1);

    final long searchInstanceMonthlyCostInCents1 =
        getSearchInstanceMonthlyCostInCents(
            SkuUtils.getSkuMonthlyCostInCents(
                SKU.NDS_AWS_SEARCH_INSTANCE_S20_COMPUTE_NVME, AWSRegionName.US_EAST_1),
            estimateSvc.getMultiplier(),
            2);
    assertEquals(searchInstanceMonthlyCostInCents1, monthlySearchNodesEstimateCents1);

    // create a single region cluster on AWS.US_EAST_2 and get cost estimates
    final SearchDeploymentDescription deploymentDescription2 =
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(
            new ObjectId(), new ObjectId(), 2, List.of(AWSRegionName.US_EAST_2), null);
    final ClusterDescription clusterDescription2 =
        getClusterDescription(CloudProvider.AWS, AWSRegionName.US_EAST_2)
            .copy()
            .setMongoDBVersion(MIN_MONGODB_VERSION.getVersion())
            .setReplicationSpecList(getReplicationSpecs(1, 3, AWSRegionName.US_EAST_2))
            .build();
    final long monthlySearchNodesEstimateCents2 =
        estimateSvc.estimateCentsForSearchNodes(clusterDescription2, deploymentDescription2);

    final long searchInstanceMonthlyCostInCents2 =
        getSearchInstanceMonthlyCostInCents(
            SkuUtils.getSkuMonthlyCostInCents(
                SKU.NDS_AWS_SEARCH_INSTANCE_S20_COMPUTE_NVME, AWSRegionName.US_EAST_2),
            estimateSvc.getMultiplier(),
            2);
    assertEquals(searchInstanceMonthlyCostInCents2, monthlySearchNodesEstimateCents2);

    // create a multi-region cluster on both AWS.US_EAST_1 and AWS.US_EAST_2
    final SearchDeploymentDescription deploymentDescription =
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(
            new ObjectId(),
            new ObjectId(),
            2,
            List.of(AWSRegionName.US_EAST_1, AWSRegionName.US_EAST_2),
            null);
    final ClusterDescription shardedClusterDescription =
        getClusterDescription(CloudProvider.AWS, AWSRegionName.US_EAST_1, AWSRegionName.US_EAST_2)
            .copy()
            .setMongoDBVersion(MIN_MONGODB_VERSION.getVersion())
            .setReplicationSpecList(
                getReplicationSpecs(1, 3, AWSRegionName.US_EAST_1, AWSRegionName.US_EAST_2))
            .build();
    // cost estimation for the multi-region cluster should be sum of cost on each region
    final long monthlySearchNodesEstimateCents =
        estimateSvc.estimateCentsForSearchNodes(shardedClusterDescription, deploymentDescription);
    assertEquals(
        monthlySearchNodesEstimateCents1 + monthlySearchNodesEstimateCents2,
        monthlySearchNodesEstimateCents);
    mockFeatureFlagSvc.close();
  }

  @Test
  public void testEstimateCentsForMonthlySearchNodes_whenUnsupportedCluster_throwError() {
    final SearchDeploymentDescription deploymentDescription =
        SearchConfigModelTestFactory.getDeploymentDescriptionAWS(2);
    final VersionUtils.Version unsupportedMongodbVersion = VersionUtils.FOUR_FOUR_ZERO;
    final ClusterDescription unsupportedClusterDescription =
        getClusterDescription(CloudProvider.AWS, AWSRegionName.US_EAST_1)
            .copy()
            .setMongoDBVersion(unsupportedMongodbVersion.getVersion())
            .build();

    SvcException actual =
        assertThrows(
            SvcException.class,
            () ->
                estimateSvc.estimateCentsForSearchNodes(
                    unsupportedClusterDescription, deploymentDescription));
    assertEquals(NDSErrorCode.INVALID_ARGUMENT, actual.getErrorCode());
    assertEquals("Invalid search deployment", actual.getMessage());
  }

  @Test
  public void testEstimateCentsForAWSSearchInstances() {
    // Mock AWS search instance pricing
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    double unitPriceDollarsS20 = 0.117;
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(regionName, unitPriceDollarsS20)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_SEARCH_INSTANCE_S20_COMPUTE_NVME),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    double unitPriceDollarsS30 = 0.232;
    double unitPriceDollarsS30East2 = 0.229;
    doReturn(
            new SkuPricing.Builder()
                .regionUnitPriceDollars(regionName, unitPriceDollarsS30)
                .regionUnitPriceDollars(AWSRegionName.US_EAST_2, unitPriceDollarsS30East2)
                .build())
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_SEARCH_INSTANCE_S30_COMPUTE_NVME),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    SKU searchInstanceSKU =
        SKU.SEARCH_INSTANCE_SKUS_BY_SIZE.get(AWSSearchInstanceSize.S20_HIGHCPU_NVME);
    assertNotNull(searchInstanceSKU);
    assertEquals(
        getSearchInstanceMonthlyCostInCents(
            unitPriceDollarsS20 * 24 * 365 / 12 * 100, estimateSvc.getMultiplier(), 2),
        estimateSvc.estimateCentsForSearchInstances(2, searchInstanceSKU, regionName));

    // test different node count
    assertEquals(
        getSearchInstanceMonthlyCostInCents(
            unitPriceDollarsS20 * 24 * 365 / 12 * 100, estimateSvc.getMultiplier(), 3),
        estimateSvc.estimateCentsForSearchInstances(3, searchInstanceSKU, regionName));

    // test different Search Instance size
    SKU searchInstanceSKU2 =
        SKU.SEARCH_INSTANCE_SKUS_BY_SIZE.get(AWSSearchInstanceSize.S30_HIGHCPU_NVME);
    assertNotNull(searchInstanceSKU2);
    assertEquals(
        getSearchInstanceMonthlyCostInCents(
            unitPriceDollarsS30 * 24 * 365 / 12 * 100, estimateSvc.getMultiplier(), 3),
        estimateSvc.estimateCentsForSearchInstances(3, searchInstanceSKU2, regionName));

    // test different region
    assertEquals(
        getSearchInstanceMonthlyCostInCents(
            unitPriceDollarsS30East2 * 24 * 365 / 12 * 100, estimateSvc.getMultiplier(), 3),
        estimateSvc.estimateCentsForSearchInstances(
            3, searchInstanceSKU2, AWSRegionName.US_EAST_2));
  }

  @Test
  public void computeCostEstimate_standardCluster_returnsCorrectEstimates() throws Exception {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final ClusterDescription clusterDescription =
        getClusterDescription(CloudProvider.AWS, regionName);

    // Mock SKU pricing for specific SKUs used in M30 AWS cost calculation
    final SkuPricing instancePricing =
        new SkuPricing.Builder()
            .biConnectorLow(0.1)
            .biConnectorHigh(0.2)
            .advancedSecurityUpcharge(0.15)
            .enterpriseAuditingUpcharge(0.05)
            .unitPriceDollars(0.10)
            .regionUnitPriceDollars(regionName, 0.10)
            .build();
    final SkuPricing backupPricing =
        new SkuPricing.Builder()
            .unitPriceDollars(0.004602739726)
            .regionUnitPriceDollars(regionName, 0.004602739726)
            .build();

    doReturn(instancePricing)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M10), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(instancePricing)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(backupPricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(backupPricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(backupPricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(instancePricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));

    // Call the method
    final Map<String, Long> result =
        estimateSvc.computeCostEstimate(
            clusterDescription,
            AWSNDSInstanceSize.M10,
            Optional.empty(), // no search deployment
            false // not enterprise plan
            );

    // Verify that basic estimates have specific calculated values
    assertEquals(0L, result.get("monthlyAnalyticsEstimateCents"));
    assertEquals(0L, result.get("monthlySearchNodesEstimateCents"));
    assertEquals(21900L, result.get("monthlyBaseEstimateCents"));
    assertEquals(13L, result.get("snapshotBackupMonthlyCentsPerGB"));
    assertEquals(0L, result.get("monthlySearchNodesEstimateCents"));

    assertEquals(21900L, result.get("monthlyEstimateCents"));
    assertEquals(720L, result.get("dailyEstimateCents"));
    assertEquals(707L, result.get("monthlyFeesEstimateCents"));

    assertEquals(72L, result.get("biConnectorSustainedDailyCents"));
    assertEquals(144L, result.get("biConnectorMaxDailyCents"));
    assertEquals(2190L, result.get("biConnectorMaxMonthlyCents"));

    assertEquals(108L, result.get("advancedSecurityDailyCents"));
    assertEquals(3285L, result.get("advancedSecurityMonthlyCents"));
    assertEquals(36L, result.get("enterpriseAuditingDailyCents"));
    assertEquals(1095L, result.get("enterpriseAuditingMonthlyCents"));
    assertNull(result.get("flexMonthlyMinEstimateCents"));
    assertNull(result.get("flexMonthlyMaxEstimateCents"));
  }

  @Test
  public void computeCostEstimate_atlasEnterpriseOrg_returnsZeroForPremiumFeatures()
      throws Exception {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final ClusterDescription clusterDescription =
        getClusterDescription(CloudProvider.AWS, regionName);

    // Mock pricing for specific SKUs used in M30 AWS cost calculation
    final SkuPricing instancePricing =
        new SkuPricing.Builder()
            .unitPriceDollars(0.10)
            .regionUnitPriceDollars(regionName, 0.10)
            .build();
    final SkuPricing backupPricing =
        new SkuPricing.Builder()
            .unitPriceDollars(0.004602739726)
            .regionUnitPriceDollars(regionName, 0.004602739726)
            .build();

    doReturn(instancePricing)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(backupPricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(backupPricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(backupPricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(instancePricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));

    final Map<String, Long> result =
        estimateSvc.computeCostEstimate(
            clusterDescription,
            AWSNDSInstanceSize.M30,
            Optional.empty(), // no search deployment
            true // enterprise plan
            );

    assertEquals(21900L, result.get("monthlyEstimateCents"));
    assertEquals(0L, result.get("monthlyAnalyticsEstimateCents"));
    assertEquals(21900L, result.get("monthlyBaseEstimateCents"));

    assertEquals(0L, result.get("biConnectorSustainedDailyCents"));
    assertEquals(0L, result.get("biConnectorMaxDailyCents"));
    assertEquals(0L, result.get("biConnectorMaxMonthlyCents"));
    assertEquals(0L, result.get("advancedSecurityDailyCents"));
    assertEquals(0L, result.get("advancedSecurityMonthlyCents"));
    assertEquals(0L, result.get("enterpriseAuditingDailyCents"));
    assertEquals(0L, result.get("enterpriseAuditingMonthlyCents"));
  }

  @Test
  public void computeCostEstimate_withInvalidSearchDeployment_handlesGracefully() {
    AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final ClusterDescription clusterDescription =
        getClusterDescription(CloudProvider.AWS, regionName);

    // Create a search deployment mock that will fail validation
    final SearchDeploymentDescription searchDeployment = mock(SearchDeploymentDescription.class);

    // Mock pricing for specific SKUs used in M30 AWS cost calculation (needed before search
    // validation)
    final SkuPricing instancePricing =
        new SkuPricing.Builder()
            .unitPriceDollars(0.10)
            .regionUnitPriceDollars(regionName, 0.10)
            .build();
    final SkuPricing backupPricing =
        new SkuPricing.Builder()
            .unitPriceDollars(0.004602739726)
            .regionUnitPriceDollars(regionName, 0.004602739726)
            .build();

    doReturn(instancePricing)
        .when(skuPricingSvc)
        .getPricing(eq(SKU.NDS_AWS_INSTANCE_M30), any(Date.class), eq(PricingConsumers.CLUSTERS));
    doReturn(backupPricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(backupPricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AZURE_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(backupPricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_GCP_BACKUP_SNAPSHOT_STORAGE),
            any(Date.class),
            eq(PricingConsumers.CLUSTERS));
    doReturn(instancePricing)
        .when(skuPricingSvc)
        .getPricing(
            eq(SKU.NDS_AWS_STORAGE_STANDARD), any(Date.class), eq(PricingConsumers.CLUSTERS));

    // The method should throw SvcException for invalid search deployment
    assertThrows(
        SvcException.class,
        () -> {
          estimateSvc.computeCostEstimate(
              clusterDescription,
              AWSNDSInstanceSize.M30,
              Optional.of(searchDeployment),
              false // not enterprise plan
              );
        });
  }

  private boolean areLongsClose(final long longOne, final long longTwo) {
    // Ignore up to 2 cent differences from floating point numbers
    final long epsilon = 2L;
    return Math.abs(longOne - longTwo) <= epsilon;
  }

  // multiply SKU monthly cost in cents by pInstanceCount (numNodes * numShards) and multiplier
  private long getSearchInstanceMonthlyCostInCents(
      double skuMonthlyCostInCents, double multiplier, int pInstanceCount) {
    return (long) (skuMonthlyCostInCents * multiplier * pInstanceCount);
  }
}
