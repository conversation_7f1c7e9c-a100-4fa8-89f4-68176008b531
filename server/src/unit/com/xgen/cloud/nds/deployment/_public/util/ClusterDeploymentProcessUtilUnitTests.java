package com.xgen.cloud.nds.deployment._public.util;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.amazonaws.services.ec2.model.VolumeType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.security._public.util.TLSUtil;
import com.xgen.cloud.deployment._public.model.Auth;
import com.xgen.cloud.deployment._public.model.AuthUser;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.DefaultRWConcern;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.EncryptionProviderType;
import com.xgen.cloud.deployment._public.model.MaintainedMongotuneConfig;
import com.xgen.cloud.deployment._public.model.MaintainedRamiConfig;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6.CommonStartupSetparams;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6.FieldDefs;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6.MongoDStartupSetParams;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6.MongosStartupSetParams;
import com.xgen.cloud.deployment._public.model.ProcessArguments2_6.Security;
import com.xgen.cloud.deployment._public.model.ProcessType;
import com.xgen.cloud.deployment._public.model.ReplicaSet;
import com.xgen.cloud.deployment._public.model.ReplicaSetMember;
import com.xgen.cloud.deployment._public.model.publishable.PublishState;
import com.xgen.cloud.group._public.model.AuditLog;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.NDSAWSKMS;
import com.xgen.cloud.nds.aws._public.model.autoscaling.ui.AWSAutoScalingView;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSKMSEARPrivateEndpoint;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceHardware;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.NDSAzureKeyVault;
import com.xgen.cloud.nds.azure._public.model.SupportedAzureEnvironment;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.DedicatedCloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.DisaggregatedStorageConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.KeyManagementConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster.MTMClusterType;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RollableValue;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.CloudProviderPrivateEndpoint;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.common._public.model.INDSDefaults;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.HostnameScheme;
import com.xgen.cloud.nds.deployment._public.model.MaxIndexBuildMemoryRollout;
import com.xgen.cloud.nds.deployment._public.mongotune.MongotuneRawConfig;
import com.xgen.cloud.nds.deployment._public.mongotune.MongotuneRawConfigSettings;
import com.xgen.cloud.nds.deployment._public.util.ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails;
import com.xgen.cloud.nds.gcp._public.model.NDSGoogleCloudKMS;
import com.xgen.cloud.nds.ifr._public.planner.IFRProcessArgs;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterTag;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.EncryptionAtRestProvider;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.CustomDefaultRWConcern;
import com.xgen.cloud.nds.project._public.model.LogComponentVerbosity;
import com.xgen.cloud.nds.project._public.model.MongotuneProcessArgs;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSFTSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.RegionSpec;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware.Action;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSCustomerX509;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSLDAP;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSUserSecurity;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.cloud.nds.serverless._public.model.ServerlessNDSDefaults;
import com.xgen.cloud.search.envoy._public.config.EnvoyOnAtlasHost;
import com.xgen.cloud.search.util._public.mongoparams.SearchMongoArgs;
import com.xgen.cloud.search.util._public.mongoparams.SearchMongoArgs.MongotCallOptions;
import com.xgen.svc.nds.model.ClusterDescriptionBuilderTestMixin;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.BiConnectorView;
import com.xgen.svc.nds.model.ui.GeoShardingView;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import nl.altindag.log.LogCaptor;
import org.apache.commons.lang.time.DateUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ExtendWith(CloudProviderExtension.class)
public class ClusterDeploymentProcessUtilUnitTests {

  final LogCaptor logCaptor = LogCaptor.forClass(ClusterDeploymentProcessUtilUnitTests.class);

  private final String TEST_CLUSTER_NAME = "foo";
  final Logger LOG = LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class);

  private static ClusterDescription getMockedClusterDescription() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn(Set.of()).when(clusterDescription).getClusterTags();
    doReturn(ClusterType.REPLICASET).when(clusterDescription).getClusterType();
    return clusterDescription;
  }

  private NDSGroup getMockedNDSGroup() {
    final NDSGroup group = mock(NDSGroup.class);
    doReturn(false).when(group).getRotateAutoPwd();
    when(group.getGroupId()).thenReturn(new ObjectId());

    final NDSLDAP ldap = mock(NDSLDAP.class);
    doReturn(Optional.of("ldapUser")).when(ldap).getBindUsername();
    doReturn(Optional.of("LdapIzM1Friend")).when(ldap).getBindPassword();
    doReturn(Optional.of("someldapserver.ldapserverworld.com")).when(ldap).getHostname();
    doReturn(Optional.of(389)).when(ldap).getPort();
    doReturn(Optional.empty()).when(ldap).getCaCertificate();

    doReturn(false).when(ldap).isAuthenticationEnabled();
    final NDSUserSecurity userSecurity = mock(NDSUserSecurity.class);
    doReturn(ldap).when(userSecurity).getLDAP();
    doReturn(userSecurity).when(group).getUserSecurity();

    final NDSManagedX509 managedX509 = mock(NDSManagedX509.class);
    final NDSCustomerX509 customerX509 = mock(NDSCustomerX509.class);

    doReturn(true).when(managedX509).isEnabled();
    final TLSUtil.PEMKeyFile pem1 =
        TLSUtil.createNewSelfSignedCA(
            "CN=intergrationTest1,OU=Atlas,O=MongoDB Inc",
            NDSDefaults.X509_SIG_ALGORITHM,
            1024,
            Duration.ofHours(6).toMillis());
    final TLSUtil.PEMKeyFile pem2 =
        TLSUtil.createNewSelfSignedCA(
            "CN=intergrationTest2,OU=Atlas,O=MongoDB Inc",
            NDSDefaults.X509_SIG_ALGORITHM,
            1024,
            Duration.ofHours(6).toMillis());
    final List<NDSManagedX509.CA> managedCAs =
        Stream.of(pem1, pem2)
            .map(pem -> new NDSManagedX509.CA(pem.getKeyCertificate(), pem.getKey()))
            .collect(Collectors.toList());
    doReturn(managedCAs).when(managedX509).getCAs();
    doReturn(
            managedCAs.stream()
                .map(NDSManagedX509.CA::getCert)
                .map(cert -> new String(TLSUtil.getCABytes(cert)))
                .collect(Collectors.joining("\n")))
        .when(managedX509)
        .getCAsString();

    // This is temporary to make tests pass - we should add test coverage for CRLs in CLOUDP-52246
    doReturn("").when(managedX509).getCRLsString();

    doReturn(managedX509).when(userSecurity).getManagedX509();
    doReturn(customerX509).when(userSecurity).getCustomerX509();

    final AuditLog auditLog = mock(AuditLog.class);
    doReturn(false).when(auditLog).isEnabled();
    doReturn(auditLog).when(group).getAuditLog();

    final NDSAWSKMS awsKms = mock(NDSAWSKMS.class);
    final Date lastUpdated = new Date();
    doReturn(false).when(awsKms).isEnabled();
    doReturn(Optional.of("customer is always right")).when(awsKms).getCustomerMasterKeyID();
    doReturn(Optional.of("accessible access")).when(awsKms).getAccessKeyID();
    doReturn(Optional.of("deepest darkest secret")).when(awsKms).getSecretAccessKey();
    doReturn(Optional.of(AWSNDSDefaults.REGION_NAME)).when(awsKms).getRegion();
    doReturn(Optional.of(lastUpdated)).when(awsKms).getLastUpdatedKeyID();
    doReturn(Optional.of(lastUpdated)).when(awsKms).getLastKmipMasterKeyRotation();

    final NDSAzureKeyVault azureKeyVault = mock(NDSAzureKeyVault.class);
    doReturn(false).when(azureKeyVault).isEnabled();
    doReturn(Optional.of("client id")).when(azureKeyVault).getClientID();
    doReturn(Optional.of("tenant id")).when(azureKeyVault).getTenantID();
    doReturn(Optional.of("secret")).when(azureKeyVault).getSecret();
    doReturn(Optional.of(SupportedAzureEnvironment.AZURE)).when(azureKeyVault).getEnvironment();
    doReturn(Optional.of("subscription id")).when(azureKeyVault).getSubscriptionID();
    doReturn(Optional.of("resource group name")).when(azureKeyVault).getResourceGroupName();
    doReturn(Optional.of("key vault name")).when(azureKeyVault).getKeyVaultName();
    doReturn(Optional.of("key identifier")).when(azureKeyVault).getKeyIdentifier();
    doReturn(Optional.of(lastUpdated)).when(azureKeyVault).getLastUpdatedKeyID();
    doReturn(Optional.of(lastUpdated)).when(azureKeyVault).getLastKmipMasterKeyRotation();

    final NDSGoogleCloudKMS googleCloudKMS = mock(NDSGoogleCloudKMS.class);
    doReturn(false).when(googleCloudKMS).isEnabled();
    doReturn(Optional.of("service account key")).when(googleCloudKMS).getServiceAccountKey();
    doReturn(Optional.of("key version resource id")).when(googleCloudKMS).getKeyVersionResourceID();
    doReturn(Optional.of("key resource id")).when(googleCloudKMS).getKeyResourceID();
    doReturn(Optional.of(lastUpdated)).when(googleCloudKMS).getLastUpdatedKeyID();
    doReturn(Optional.of(lastUpdated)).when(googleCloudKMS).getLastKmipMasterKeyRotation();

    final NDSEncryptionAtRest encryptionAtRest = mock(NDSEncryptionAtRest.class);
    doReturn(awsKms).when(encryptionAtRest).getAWSKMS();
    doReturn(azureKeyVault).when(encryptionAtRest).getAzureKeyVault();
    doReturn(googleCloudKMS).when(encryptionAtRest).getGoogleCloudKMS();
    doReturn(encryptionAtRest).when(group).getEncryptionAtRest();

    return group;
  }

  private ClusterDescription getTestReplicaSet(
      final String pTestClusterName, final String mongoDBVersion) {
    final List<RegionConfig> regionsConfig =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                    AWSRegionName.US_EAST_1, RegionSpec.MAX_PRIORITY, 3, 0, 0, 0)
                .copy()
                .setElectableSpecs(
                    new AWSHardwareSpec(
                        3,
                        AWSNDSInstanceSize.M30,
                        AWSInstanceFamily.M5,
                        NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                            CloudProvider.AWS, AWSInstanceFamily.M5),
                        CpuArchitecture.X86_64,
                        100,
                        0,
                        VolumeType.Gp2,
                        true))
                .build());
    final ReplicationSpec replicationSpec =
        new ReplicationSpec(
            new ObjectId(),
            new ObjectId(),
            new ObjectId(),
            NDSDefaults.ZONE_NAME,
            1,
            regionsConfig);

    return NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
        .copy()
        .setClusterType(ClusterDescription.ClusterType.REPLICASET)
        .setReplicationSpecList(List.of(replicationSpec))
        .setName(pTestClusterName)
        .setCreateDate(new Date())
        .setDiskSizeGB(10)
        .setGroupId(oid(42))
        .setLastUpdateDate(new Date())
        .setMongoDBVersion(mongoDBVersion)
        .setMongoDBMajorVersion(
            VersionUtils.Version.fromString(mongoDBVersion).getMajorVersionString())
        .setMongoUriHosts(new String[0])
        .setPrivateMongoUriHosts(new String[0])
        .setMongoUriLastUpdateDate(new Date())
        .setState(ClusterDescription.State.WORKING)
        .setRestoreJobIds(Collections.emptyList())
        .setAutoScalingForProvider(
            CloudProvider.AWS,
            AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
            NodeTypeFamily.BASE)
        .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
        .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
        .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.AWS)
        .setHostnameSchemeForAgents(HostnameScheme.INTERNAL)
        .setDeploymentClusterName(
            NDSDefaults.generateInternalDeploymentClusterName(oid(42), pTestClusterName))
        .build();
  }

  private ShardedClusterDescription getTestShardedCluster(
      final String pTestClusterName,
      final String pVersion,
      final ClusterDescription.ClusterType pType,
      final int pNumShards) {
    return new ShardedClusterDescription.Builder(
            NDSModelTestFactory.changeSingleZoneClusterAnalyticsNodes(
                    NDSModelTestFactory.changeSingleZoneClusterElectableNodes(
                        NDSModelTestFactory.changeSingleZoneClusterNumShards(
                            NDSModelTestFactory.changeSingleZoneClusterElectableInstanceSize(
                                NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS),
                                AWSNDSInstanceSize.M30,
                                AWSInstanceFamily.M5),
                            pNumShards),
                        3),
                    1,
                    AWSNDSInstanceSize.M80,
                    AWSInstanceFamily.M6G)
                .toDBObject())
        .setClusterType(pType)
        .setName(pTestClusterName)
        .setCreateDate(new Date())
        .setDiskSizeGB(10)
        .setGroupId(oid(42))
        .setLastUpdateDate(new Date())
        .setMongoDBVersion(pVersion)
        .setMongoDBMajorVersion(VersionUtils.Version.fromString(pVersion).getMajorVersionString())
        .setMongoUriHosts(new String[0])
        .setPrivateMongoUriHosts(new String[0])
        .setMongoUriLastUpdateDate(new Date())
        .setState(ClusterDescription.State.WORKING)
        .setRestoreJobIds(List.of())
        .setRestoreJobType(null)
        .setAutoScalingForProvider(
            CloudProvider.AWS,
            AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
            NodeTypeFamily.BASE)
        .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
        .setGeoSharding(GeoShardingView.getDefaultGeoShardingView().toGeoSharding())
        .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
        .setHostnameSchemeForAgents(HostnameScheme.INTERNAL)
        .setDeploymentClusterName(
            NDSDefaults.generateInternalDeploymentClusterName(oid(42), pTestClusterName))
        .build();
  }

  private void assertX509ProcessArgs(
      final Process pProcess, final ClusterDescription pCluster, final NDSGroup pNDSGroup) {
    // Check parameters for X509 certificate features (used by Atlas X509 database users and Data
    // Lake Auth)

    final Map<String, Object> setParameterMap = pProcess.getArgs2_6().getSetParameterMap();
    final NDSManagedX509 managedX509 = pNDSGroup.getUserSecurity().getManagedX509();
    if (VersionUtils.supportsTLSCATrusts(pCluster.getMongoDBVersion()) && managedX509.isEnabled()) {
      assertTrue((Boolean) setParameterMap.get("allowRolesFromX509Certificates"));
      assertNotNull(setParameterMap.get("tlsCATrusts"));
      assertFalse(pNDSGroup.getUserSecurity().getManagedX509().getCAsString().isBlank());
      assertTrue(
          setParameterMap
              .get("tlsCATrusts")
              .toString()
              .contains(
                  TLSUtil.generateCertFingerprint(
                      pNDSGroup.getUserSecurity().getManagedX509().getCAs().get(0).getCert())));
    } else {
      assertNull(setParameterMap.get("tlsCATrusts"));
    }

    if (VersionUtils.supportsAtlasX509UserAuth(pCluster.getMongoDBVersion())) {
      if (pCluster.getMongoDBVersion().isGreaterThanOrEqualTo(VersionUtils.FOUR_ONE_ZERO)) {
        assertTrue((Boolean) setParameterMap.get("tlsWithholdClientCertificate"));
        assertNull(setParameterMap.get("sslWithholdClientCertificate"));
      } else {
        assertTrue((Boolean) setParameterMap.get("sslWithholdClientCertificate"));
        assertNull(setParameterMap.get("tlsWithholdClientCertificate"));
      }
      if (!VersionUtils.supportsAtlasX509UserAuth(pCluster.getMongoDBVersion())) {
        assertFalse((Boolean) setParameterMap.get("allowRolesFromX509Certificates"));
      }
    } else {
      assertNull(setParameterMap.get("tlsWithholdClientCertificate"));
      assertNull(setParameterMap.get("sslWithholdClientCertificate"));
    }

    if ((!VersionUtils.supportsTLSCATrusts(pCluster.getMongoDBVersion())
            || !managedX509.isEnabled())
        && !VersionUtils.supportsAtlasX509UserAuth(pCluster.getMongoDBVersion())) {
      assertNull(setParameterMap.get("allowRolesFromX509Certificates"));
    }
  }

  @Test
  public void updateInstanceMongoDProcess_ReplicaSet_43() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final AWSCloudProviderContainer container = mock(AWSCloudProviderContainer.class);
    doReturn(containerId).when(container).getId();
    doReturn(Optional.of(container)).when(ndsGroup).getCloudProviderContainer(eq(containerId));
    doReturn(AWSRegionName.US_EAST_1).when(container).getRegion();

    final ClusterDescription testCluster_43 = getTestReplicaSet(TEST_CLUSTER_NAME, "4.3.3");
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_43));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_43,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertX509ProcessArgs(process, testCluster_43, ndsGroup);

    final NDSManagedX509 disabledManagedX509 = new NDSManagedX509();
    final NDSUserSecurity userSecurity = ndsGroup.getUserSecurity();
    doReturn(disabledManagedX509).when(userSecurity).getManagedX509();

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_43,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertX509ProcessArgs(process, testCluster_43, ndsGroup);
  }

  @Test
  public void updateInstanceMongoDProcess_ReplicaSet_42() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_42 =
        getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.FOUR_TWO_ZERO.getVersion());
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_42));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_42,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals("snappy,zstd,zlib", process.getArgs().getNetworkMessageCompressors());
    assertX509ProcessArgs(process, testCluster_42, ndsGroup);
  }

  @Test
  public void testUpdateInstanceMongoDProcess_ReplicaSet_70_setInternalQueryStatsRateLimit() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_7016 = getTestReplicaSet(TEST_CLUSTER_NAME, "7.0.16");
    final ReplicaSetHardware hardware_7016 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_7016));

    final Process process = new Process();
    process.setName("process");

    final ProcessArguments2_6 args = new ProcessArguments2_6();
    args.setInternalQueryStatsRateLimit(50);

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_7016,
        Collections.emptyList(),
        null,
        hardware_7016,
        hardware_7016.getHardware().get(0),
        process,
        args,
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals(50, process.getArgs2_6().getInternalQueryStatsRateLimit().intValue());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_ReplicaSet_6017_queryStatsAndSBE() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    {
      final ObjectId containerId = new ObjectId();

      final ClusterDescription testCluster_6017 = getTestReplicaSet(TEST_CLUSTER_NAME, "6.0.17");
      final ReplicaSetHardware hardware_6017 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster_6017));

      final Process process = new Process();
      process.setName("process");

      final ProcessArguments2_6 args = new ProcessArguments2_6();

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_6017,
          Collections.emptyList(),
          null,
          hardware_6017,
          hardware_6017.getHardware().get(0),
          process,
          args,
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertTrue(process.getArgs2_6().isSBEDisabled(false));
      assertTrue(
          (Boolean)
              process
                  .getArgs2_6()
                  .getSetParameterMap()
                  .get(ProcessArguments2_6.SBESetParams.QUERY_FORCE_CLASSIC_ENGINE));
      assertNull(process.getArgs2_6().getInternalQueryStatsRateLimit());
    }

    // Confirm 6.0.17 must respect overrides that are already set
    {
      final ObjectId containerId = new ObjectId();

      final ClusterDescription testCluster_6017 = getTestReplicaSet(TEST_CLUSTER_NAME, "6.0.17");
      final ReplicaSetHardware hardware_6017 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster_6017));

      final Process process = new Process();
      process.setName("process");

      final ProcessArguments2_6 args = new ProcessArguments2_6();

      args.setInternalQueryForceClassicEngine(false);
      args.setInternalQueryStatsRateLimit(55);
      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_6017,
          Collections.emptyList(),
          null,
          hardware_6017,
          hardware_6017.getHardware().get(0),
          process,
          args,
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertFalse(process.getArgs2_6().isSBEDisabled(false));
      assertFalse(
          (Boolean)
              process
                  .getArgs2_6()
                  .getSetParameterMap()
                  .get(ProcessArguments2_6.SBESetParams.QUERY_FORCE_CLASSIC_ENGINE));
      assertEquals(55, process.getArgs2_6().getInternalQueryStatsRateLimit().intValue());
    }

    // Confirm 6.0.16 doesn't explicitly disable SBE
    {
      final ObjectId containerId = new ObjectId();

      final ClusterDescription testCluster_6016 = getTestReplicaSet(TEST_CLUSTER_NAME, "6.0.16");
      final ReplicaSetHardware hardware_6016 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster_6016));

      final Process process = new Process();
      process.setName("process");

      final ProcessArguments2_6 args = new ProcessArguments2_6();

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_6016,
          Collections.emptyList(),
          null,
          hardware_6016,
          hardware_6016.getHardware().get(0),
          process,
          args,
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertFalse(process.getArgs2_6().isSBEDisabled(false));
      assertFalse(
          process
              .getArgs2_6()
              .getSetParameterMap()
              .containsKey(ProcessArguments2_6.SBESetParams.QUERY_FORCE_CLASSIC_ENGINE));
      assertNull(process.getArgs2_6().getInternalQueryStatsRateLimit());
    }
  }

  @Test
  public void testUpdateInstanceMongoDProcess_ReplicaSet_disableSBE() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    // 6.1 with SBE disabled
    final Map<String, Object> incompatibleDisabled61 = new HashMap<>();
    incompatibleDisabled61.put("internalQueryForceClassicEngine", true);
    final Map<String, Object> disabled61 = new HashMap<>();
    disabled61.put("internalQueryFrameworkControl", "forceClassicEngine");
    final Map<String, Object> disabled61IgnoreConflict = new HashMap<>();
    disabled61IgnoreConflict.put("internalQueryFrameworkControl", "forceClassicEngine");
    disabled61IgnoreConflict.put("internalQueryForceClassicEngine", false);
    List.of(incompatibleDisabled61, disabled61, disabled61IgnoreConflict)
        .forEach(
            parameters -> {
              final ObjectId containerId = new ObjectId();

              final ClusterDescription testCluster_61 =
                  getTestReplicaSet(TEST_CLUSTER_NAME, "6.1.0");
              final ReplicaSetHardware hardware_61 =
                  new ReplicaSetHardware(
                      ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                          0, containerId, testCluster_61));

              final Process process = new Process();
              process.setName("process");

              final ProcessArguments2_6 args = new ProcessArguments2_6();
              args.setSetParameterMap(parameters);

              ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
                  new AutomationConfig(),
                  testCluster_61,
                  Collections.emptyList(),
                  null,
                  hardware_61,
                  hardware_61.getHardware().get(0),
                  process,
                  args,
                  ndsGroup,
                  Optional.empty(),
                  new SearchMongoArgs(),
                  ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
                  false,
                  false,
                  false,
                  LOG,
                  false,
                  false,
                  false,
                  false,
                  "",
                  false,
                  false,
                  new MaxIndexBuildMemoryRollout(false),
                  1L,
                  IFRProcessArgs.none());

              assertTrue(process.getArgs2_6().isSBEDisabled(true));
              assertFalse(process.getArgs2_6().isSBEDisabled(false));
              assertFalse(
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .containsKey(ProcessArguments2_6.SBESetParams.QUERY_FORCE_CLASSIC_ENGINE));
              assertEquals(
                  "forceClassicEngine",
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .get(ProcessArguments2_6.SBESetParams.QUERY_FRAMEWORK_CONTROL));
            });

    // 6.1 without SBE disabled
    final Map<String, Object> incompatibleNotDisabled61 = new HashMap<>();
    incompatibleNotDisabled61.put("internalQueryForceClassicEngine", false);
    final Map<String, Object> notDisabled61 = new HashMap<>();
    notDisabled61.put("internalQueryFrameworkControl", "trySbeEngine");
    final Map<String, Object> notPresent61 = new HashMap<>();
    final Map<String, Object> notDisabled61IgnoreConflict = new HashMap<>();
    notDisabled61IgnoreConflict.put("internalQueryFrameworkControl", "trySbeEngine");
    notDisabled61IgnoreConflict.put("internalQueryForceClassicEngine", true);
    List.of(incompatibleNotDisabled61, notDisabled61, notPresent61, notDisabled61IgnoreConflict)
        .forEach(
            parameters -> {
              final ObjectId containerId = new ObjectId();

              final ClusterDescription testCluster_61 =
                  getTestReplicaSet(TEST_CLUSTER_NAME, "6.1.0");
              final ReplicaSetHardware hardware_61 =
                  new ReplicaSetHardware(
                      ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                          0, containerId, testCluster_61));

              final Process process = new Process();
              process.setName("process");

              final ProcessArguments2_6 args = new ProcessArguments2_6();
              args.setSetParameterMap(parameters);

              ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
                  new AutomationConfig(),
                  testCluster_61,
                  Collections.emptyList(),
                  null,
                  hardware_61,
                  hardware_61.getHardware().get(0),
                  process,
                  args,
                  ndsGroup,
                  Optional.empty(),
                  new SearchMongoArgs(),
                  ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
                  false,
                  false,
                  false,
                  LOG,
                  false,
                  false,
                  false,
                  false,
                  "",
                  false,
                  false,
                  new MaxIndexBuildMemoryRollout(false),
                  1L,
                  IFRProcessArgs.none());

              assertFalse(process.getArgs2_6().isSBEDisabled(true));
              assertFalse(process.getArgs2_6().isSBEDisabled(false));
              assertFalse(
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .containsKey(ProcessArguments2_6.SBESetParams.QUERY_FORCE_CLASSIC_ENGINE));
              assertNotEquals(
                  "forceClassicEngine",
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .get(ProcessArguments2_6.SBESetParams.QUERY_FRAMEWORK_CONTROL));
            });

    // 6.0 with SBE disabled
    final Map<String, Object> incompatibleDisabled60 = new HashMap<>();
    incompatibleDisabled60.put("internalQueryFrameworkControl", "forceClassicEngine");
    final Map<String, Object> disabled60 = new HashMap<>();
    disabled60.put("internalQueryForceClassicEngine", true);
    final Map<String, Object> disabled60IgnoreConflict = new HashMap<>();
    disabled60IgnoreConflict.put("internalQueryForceClassicEngine", true);
    disabled60IgnoreConflict.put("internalQueryFrameworkControl", "trySbeEngine");
    List.of(incompatibleDisabled60, disabled60, disabled60IgnoreConflict)
        .forEach(
            parameters -> {
              final ObjectId containerId = new ObjectId();

              final ClusterDescription testCluster_60 =
                  getTestReplicaSet(TEST_CLUSTER_NAME, "6.0.16");
              final ReplicaSetHardware hardware_60 =
                  new ReplicaSetHardware(
                      ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                          0, containerId, testCluster_60));

              final Process process = new Process();
              process.setName("process");

              final ProcessArguments2_6 args = new ProcessArguments2_6();
              args.setSetParameterMap(parameters);

              ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
                  new AutomationConfig(),
                  testCluster_60,
                  Collections.emptyList(),
                  null,
                  hardware_60,
                  hardware_60.getHardware().get(0),
                  process,
                  args,
                  ndsGroup,
                  Optional.empty(),
                  new SearchMongoArgs(),
                  ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
                  false,
                  false,
                  false,
                  LOG,
                  false,
                  false,
                  false,
                  false,
                  "",
                  false,
                  false,
                  new MaxIndexBuildMemoryRollout(false),
                  1L,
                  IFRProcessArgs.none());

              assertTrue(process.getArgs2_6().isSBEDisabled(false));
              assertFalse(process.getArgs2_6().isSBEDisabled(true));
              assertFalse(
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .containsKey(ProcessArguments2_6.SBESetParams.QUERY_FRAMEWORK_CONTROL));
              assert ((Boolean)
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .get(ProcessArguments2_6.SBESetParams.QUERY_FORCE_CLASSIC_ENGINE));
            });

    // 6.0 without SBE disabled
    final Map<String, Object> notDisabled60 = new HashMap<>();
    notDisabled60.put("internalQueryForceClassicEngine", false);
    final Map<String, Object> incompatibleNotDisabled60 = new HashMap<>();
    incompatibleNotDisabled60.put("internalQueryFrameworkControl", "trySbeEngine");
    final Map<String, Object> notPresent60 = new HashMap<>();
    final Map<String, Object> notDisabled60IgnoreConflict = new HashMap<>();
    notDisabled60IgnoreConflict.put("internalQueryForceClassicEngine", false);
    notDisabled60IgnoreConflict.put("internalQueryFrameworkControl", "forceClassicEngine");
    List.of(notDisabled60, incompatibleNotDisabled60, notPresent60, notDisabled60IgnoreConflict)
        .forEach(
            parameters -> {
              final ObjectId containerId = new ObjectId();

              final ClusterDescription testCluster_60 =
                  getTestReplicaSet(TEST_CLUSTER_NAME, "6.0.16");
              final ReplicaSetHardware hardware_60 =
                  new ReplicaSetHardware(
                      ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                          0, containerId, testCluster_60));

              final Process process = new Process();
              process.setName("process");

              final ProcessArguments2_6 args = new ProcessArguments2_6();
              args.setSetParameterMap(parameters);

              ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
                  new AutomationConfig(),
                  testCluster_60,
                  Collections.emptyList(),
                  null,
                  hardware_60,
                  hardware_60.getHardware().get(0),
                  process,
                  args,
                  ndsGroup,
                  Optional.empty(),
                  new SearchMongoArgs(),
                  ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
                  false,
                  false,
                  false,
                  LOG,
                  false,
                  false,
                  false,
                  false,
                  "",
                  false,
                  false,
                  new MaxIndexBuildMemoryRollout(false),
                  1L,
                  IFRProcessArgs.none());

              assertFalse(process.getArgs2_6().isSBEDisabled(true));
              assertFalse(process.getArgs2_6().isSBEDisabled(false));
              assertFalse(
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .containsKey(ProcessArguments2_6.SBESetParams.QUERY_FRAMEWORK_CONTROL));
              assertNotEquals(
                  true,
                  process
                      .getArgs2_6()
                      .getSetParameterMap()
                      .get(ProcessArguments2_6.SBESetParams.QUERY_FORCE_CLASSIC_ENGINE));
            });
  }

  @Test
  public void testUpdateInstanceMongoDProcess_ReplicaSet_36() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_360 = getTestReplicaSet(TEST_CLUSTER_NAME, "3.6.0");
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_360));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_360,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals("snappy,zlib", process.getArgs().getNetworkMessageCompressors());
    assertX509ProcessArgs(process, testCluster_360, ndsGroup);
    assertNull(process.getArgs2_6().getInternalQueryStatsRateLimit());

    final AuditLog auditLog = ndsGroup.getAuditLog();

    doReturn(true).when(auditLog).isEnabled();
    doReturn(false).when(auditLog).isAuditAuthorizationSuccess();
    doReturn(Optional.of("{ atype: { $in: [ 'createCollection' ] } }"))
        .when(auditLog)
        .getAuditFilter();

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_360,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals("file", process.getArgs2_6().getAuditLogDestination());
    assertEquals("JSON", process.getArgs2_6().getAuditLogFormat());
    assertTrue(
        Pattern.matches(
            "/srv/mongodb/process/auditLog.json", process.getArgs2_6().getAuditLogPath()));
    assertEquals(
        "{ atype: { $in: [ 'createCollection' ] } }", process.getArgs2_6().getAuditLogFilter());
    assertFalse(process.getArgs2_6().getAuditAuthorizationSuccess());

    doReturn(true).when(auditLog).isAuditAuthorizationSuccess();

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_360,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertTrue(process.getArgs2_6().getAuditAuthorizationSuccess());
  }

  @Test
  public void
      updateInstanceMongoDProcess_auditLogRuntimeConfigurationEnabled_groupAuditSettingsNotApplied() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final AuditLog groupAuditLog = mock(AuditLog.class);
    when(groupAuditLog.isEnabled()).thenReturn(true);
    when(groupAuditLog.getAuditFilter()).thenReturn(Optional.of("{ action: 'insert' }"));
    when(groupAuditLog.isAuditAuthorizationSuccess()).thenReturn(true);
    when(ndsGroup.getAuditLog()).thenReturn(groupAuditLog);

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster = getTestReplicaSet(TEST_CLUSTER_NAME, "8.0.1");
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster));

    final Process process = new Process();
    process.setName("mongod-runtime-enabled");
    final ProcessArguments2_6 processArgs = new ProcessArguments2_6();
    final ProcessArguments2_6.AuditLog processAuditLog = new ProcessArguments2_6.AuditLog();
    processAuditLog.setEnableRuntimeDisableGroupConfig(true);
    processArgs.setAuditLog(processAuditLog);

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster,
        Collections.emptyList(),
        null,
        hardware,
        hardware.getHardware().get(0),
        process,
        processArgs,
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertNull(processArgs.getAuditLogFilter());
    assertNull(processArgs.getAuditAuthorizationSuccess());
    assertTrue(processArgs.getAuditLog().getEnableRuntimeDisableGroupConfig());
  }

  @Test
  public void
      updateInstanceMongoDProcess_auditLogRuntimeConfigurationDisabled_groupAuditSettingsApplied() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final AuditLog groupAuditLog = mock(AuditLog.class);
    when(groupAuditLog.isEnabled()).thenReturn(true);
    when(groupAuditLog.getAuditFilter()).thenReturn(Optional.of("{ action: 'insert' }"));
    when(groupAuditLog.isAuditAuthorizationSuccess()).thenReturn(true);
    when(ndsGroup.getAuditLog()).thenReturn(groupAuditLog);

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster = getTestReplicaSet(TEST_CLUSTER_NAME, "8.0.1");
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster));

    final Process process = new Process();
    process.setName("mongod-runtime-disabled");
    final ProcessArguments2_6 processArgs = new ProcessArguments2_6();
    final ProcessArguments2_6.AuditLog processAuditLog = new ProcessArguments2_6.AuditLog();
    processAuditLog.setEnableRuntimeDisableGroupConfig(false);
    processArgs.setAuditLog(processAuditLog);

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster,
        Collections.emptyList(),
        null,
        hardware,
        hardware.getHardware().get(0),
        process,
        processArgs,
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals("{ action: 'insert' }", processArgs.getAuditLogFilter());
    assertTrue(processArgs.getAuditAuthorizationSuccess());
    assertFalse(processArgs.getAuditLog().getEnableRuntimeDisableGroupConfig());
  }

  @Test
  public void
      updateInstanceMongoDProcess_auditLogRuntimeConfigurationNull_groupAuditSettingsApplied() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final AuditLog groupAuditLog = mock(AuditLog.class);
    when(groupAuditLog.isEnabled()).thenReturn(true);
    when(groupAuditLog.getAuditFilter()).thenReturn(Optional.of("{ action: 'insert' }"));
    when(groupAuditLog.isAuditAuthorizationSuccess()).thenReturn(true);
    when(ndsGroup.getAuditLog()).thenReturn(groupAuditLog);

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster = getTestReplicaSet(TEST_CLUSTER_NAME, "8.0.1");
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster));

    final Process process = new Process();
    process.setName("mongod-runtime-null");
    final ProcessArguments2_6 processArgs = new ProcessArguments2_6();
    final ProcessArguments2_6.AuditLog processAuditLog = new ProcessArguments2_6.AuditLog();
    processAuditLog.setEnableRuntimeDisableGroupConfig(null);
    processArgs.setAuditLog(processAuditLog);

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster,
        Collections.emptyList(),
        null,
        hardware,
        hardware.getHardware().get(0),
        process,
        processArgs,
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals("{ action: 'insert' }", processArgs.getAuditLogFilter());
    assertTrue(processArgs.getAuditAuthorizationSuccess());
    assertNull(processArgs.getAuditLog().getEnableRuntimeDisableGroupConfig());
  }

  @Test
  public void
      updateInstanceMongoSProcess_auditLogRuntimeConfigurationEnabled_groupAuditSettingsNotApplied() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final AuditLog groupAuditLog = mock(AuditLog.class);
    when(groupAuditLog.isEnabled()).thenReturn(true);
    when(groupAuditLog.getAuditFilter()).thenReturn(Optional.of("{ action: 'update' }"));
    when(groupAuditLog.isAuditAuthorizationSuccess()).thenReturn(false);
    when(ndsGroup.getAuditLog()).thenReturn(groupAuditLog);

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster = getTestReplicaSet(TEST_CLUSTER_NAME, "8.0.1");
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster));

    final Process process = new Process();
    process.setName("mongos-runtime-enabled");
    final ProcessArguments2_6 processArgs = new ProcessArguments2_6();
    final ProcessArguments2_6.AuditLog processAuditLog = new ProcessArguments2_6.AuditLog();
    processAuditLog.setEnableRuntimeDisableGroupConfig(true);
    processArgs.setAuditLog(processAuditLog);

    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        new AutomationConfig(),
        testCluster,
        hardware,
        hardware.getAllHardware().findFirst().orElseThrow(),
        process,
        processArgs,
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());

    assertNull(processArgs.getAuditLogFilter());
    assertNull(processArgs.getAuditAuthorizationSuccess());
    assertTrue(processArgs.getAuditLog().getEnableRuntimeDisableGroupConfig());
  }

  @Test
  public void
      updateInstanceMongoSProcess_auditLogRuntimeConfigurationDisabled_groupAuditSettingsApplied() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final AuditLog groupAuditLog = mock(AuditLog.class);
    when(groupAuditLog.isEnabled()).thenReturn(true);
    when(groupAuditLog.getAuditFilter()).thenReturn(Optional.of("{ action: 'update' }"));
    when(groupAuditLog.isAuditAuthorizationSuccess()).thenReturn(false);
    when(ndsGroup.getAuditLog()).thenReturn(groupAuditLog);

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster = getTestReplicaSet(TEST_CLUSTER_NAME, "8.0.1");
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster));

    final Process process = new Process();
    process.setName("mongos-runtime-disabled");
    final ProcessArguments2_6 processArgs = new ProcessArguments2_6();
    final ProcessArguments2_6.AuditLog processAuditLog = new ProcessArguments2_6.AuditLog();
    processAuditLog.setEnableRuntimeDisableGroupConfig(false);
    processArgs.setAuditLog(processAuditLog);

    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        new AutomationConfig(),
        testCluster,
        hardware,
        hardware.getAllHardware().findFirst().orElseThrow(),
        process,
        processArgs,
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());

    assertEquals("{ action: 'update' }", processArgs.getAuditLogFilter());
    assertFalse(processArgs.getAuditAuthorizationSuccess());
    assertFalse(processArgs.getAuditLog().getEnableRuntimeDisableGroupConfig());
  }

  @Test
  public void
      updateInstanceMongoSProcess_auditLogRuntimeConfigurationNull_groupAuditSettingsApplied() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final AuditLog groupAuditLog = mock(AuditLog.class);
    when(groupAuditLog.isEnabled()).thenReturn(true);
    when(groupAuditLog.getAuditFilter()).thenReturn(Optional.of("{ action: 'update' }"));
    when(groupAuditLog.isAuditAuthorizationSuccess()).thenReturn(false);
    when(ndsGroup.getAuditLog()).thenReturn(groupAuditLog);

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster = getTestReplicaSet(TEST_CLUSTER_NAME, "8.0.1");
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster));

    final Process process = new Process();
    process.setName("mongos-runtime-null");
    final ProcessArguments2_6 processArgs = new ProcessArguments2_6();
    final ProcessArguments2_6.AuditLog processAuditLog = new ProcessArguments2_6.AuditLog();
    processAuditLog.setEnableRuntimeDisableGroupConfig(null);
    processArgs.setAuditLog(processAuditLog);

    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        new AutomationConfig(),
        testCluster,
        hardware,
        hardware.getAllHardware().findFirst().orElseThrow(),
        process,
        processArgs,
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());

    assertEquals("{ action: 'update' }", processArgs.getAuditLogFilter());
    assertFalse(processArgs.getAuditAuthorizationSuccess());
    assertNull(processArgs.getAuditLog().getEnableRuntimeDisableGroupConfig());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_ReplicaSet_70() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_7016 = getTestReplicaSet(TEST_CLUSTER_NAME, "7.0.16");
    final ReplicaSetHardware hardware_7016 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_7016));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_7016,
        Collections.emptyList(),
        null,
        hardware_7016,
        hardware_7016.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals(100, process.getArgs2_6().getInternalQueryStatsRateLimit().intValue());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_ShardedCluster_70() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription shardedTestCluster_70 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "7.0.16", ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware_70 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedTestCluster_70));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        shardedTestCluster_70,
        Collections.emptyList(),
        null,
        hardware_70,
        hardware_70.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals(0, process.getArgs2_6().getInternalQueryStatsRateLimit().intValue());
  }

  private static Stream<Arguments> ingressConnectionEstablishmentRateLimiterParams() {
    return Stream.of(
        arguments(AWSNDSInstanceSize.M10, true),
        arguments(AWSNDSInstanceSize.M20, true),
        arguments(AWSNDSInstanceSize.M30, false));
  }

  @ParameterizedTest
  @MethodSource("ingressConnectionEstablishmentRateLimiterParams")
  public void testUpdateInstanceMongoDProcess_ingressConnectionEstablishmentRateLimiter(
      final AWSNDSInstanceSize pInstanceSize, final boolean pExpectedRateLimiterEnabled) {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription tempCluster = getTestReplicaSet(TEST_CLUSTER_NAME, "8.0.12");
    final ClusterDescription cluster =
        tempCluster
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setInstanceSize(pInstanceSize))
            .build();
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(0, containerId, cluster));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        cluster,
        Collections.emptyList(),
        null,
        hardware,
        hardware.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        true,
        true,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals(
        pExpectedRateLimiterEnabled,
        process.getArgs2_6().getIngressConnectionEstablishmentRateLimiterEnabled());
    assertEquals(20, process.getArgs2_6().getIngressConnectionEstablishmentRatePerSec());
    assertEquals(200, process.getArgs2_6().getIngressConnectionEstablishmentMaxQueueDepth());
    assertEquals(1.0, process.getArgs2_6().getIngressConnectionEstablishmentBurstCapacitySecs());
    assertEquals(
        "{\"ranges\": [\"127.0.0.1/32\"]}",
        process.getArgs2_6().getIngressConnectionEstablishmentRateLimiterBypass());
  }

  @Test
  public void
      testUpdateInstanceMongoDProcess_ingressConnectionEstablishmentRateLimiter_unsupportedVersion() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription tempCluster = getTestReplicaSet(TEST_CLUSTER_NAME, "6.0.5");
    final ClusterDescription cluster =
        tempCluster
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M10))
            .build();
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(0, containerId, cluster));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        cluster,
        Collections.emptyList(),
        null,
        hardware,
        hardware.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        true,
        true,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertNull(process.getArgs2_6().getIngressConnectionEstablishmentRateLimiterEnabled());
    assertNull(process.getArgs2_6().getIngressConnectionEstablishmentRatePerSec());
    assertNull(process.getArgs2_6().getIngressConnectionEstablishmentMaxQueueDepth());
    assertNull(process.getArgs2_6().getIngressConnectionEstablishmentBurstCapacitySecs());
    assertNull(process.getArgs2_6().getIngressConnectionEstablishmentRateLimiterBypass());
  }

  @Test
  public void testComputeIngressConnectionEstablishmentRateLimiterBypassIps() {
    final ObjectId containerId = new ObjectId();
    final AWSCloudProviderContainer container = mock(AWSCloudProviderContainer.class);
    doReturn(containerId).when(container).getId();
    doReturn("***********/16").when(container).getAtlasCidr();

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    doReturn(Optional.of(container))
        .when(ndsGroup)
        .getDedicatedCloudProviderContainer(eq(containerId));

    // instance hardware 0 and 1 are in the container
    final InstanceHardware instanceHardware0 = mock(InstanceHardware.class);
    doReturn(new ObjectId()).when(instanceHardware0).getInstanceId();
    doReturn(containerId).when(instanceHardware0).getCloudContainerId();
    doReturn(Optional.of("***************")).when(instanceHardware0).getPublicIP();

    final InstanceHardware instanceHardware1 = mock(InstanceHardware.class);
    doReturn(new ObjectId()).when(instanceHardware1).getInstanceId();
    doReturn(containerId).when(instanceHardware1).getCloudContainerId();
    doReturn(Optional.of("***************")).when(instanceHardware1).getPublicIP();

    final InstanceHardware instanceHardware2 = mock(InstanceHardware.class);
    doReturn(new ObjectId()).when(instanceHardware2).getInstanceId();
    doReturn(new ObjectId()).when(instanceHardware2).getCloudContainerId();
    doReturn(Optional.of("***************")).when(instanceHardware2).getPublicIP();

    final ReplicaSetHardware replicaSetHardware = mock(ReplicaSetHardware.class);

    doReturn(Stream.of(instanceHardware0, instanceHardware1, instanceHardware2))
        .when(replicaSetHardware)
        .getAllHardware();

    // We expect local host ip, atlas cidr and instance hardware 2's public IP since it is not in
    // the container
    assertEquals(
        List.of("127.0.0.1/32", "***********/16", "***************/32"),
        ClusterDeploymentProcessUtil.computeIngressConnectionEstablishmentRateLimiterBypassIps(
            ndsGroup, replicaSetHardware, instanceHardware0));
  }

  @ParameterizedTest
  @MethodSource("ingressConnectionEstablishmentRateLimiterParams")
  public void updateInstanceMongoSProcess_ingressConnectionEstablishmentRateLimiter(
      final AWSNDSInstanceSize pInstanceSize, final boolean pExpectedRateLimiterEnabled) {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ClusterDescription tempCluster =
        getTestShardedCluster(TEST_CLUSTER_NAME, "8.0.12", ClusterType.SHARDED, 2);
    final ClusterDescription cluster =
        tempCluster
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setInstanceSize(pInstanceSize))
            .build();
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(0, containerId, cluster));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        cluster,
        hardware,
        hardware.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        true,
        true,
        1L,
        IFRProcessArgs.none());

    assertEquals(
        pExpectedRateLimiterEnabled,
        process.getArgs2_6().getIngressConnectionEstablishmentRateLimiterEnabled());
    assertEquals(20, process.getArgs2_6().getIngressConnectionEstablishmentRatePerSec());
    assertEquals(200, process.getArgs2_6().getIngressConnectionEstablishmentMaxQueueDepth());
    assertEquals(1.0, process.getArgs2_6().getIngressConnectionEstablishmentBurstCapacitySecs());
    assertEquals(
        "{\"ranges\": [\"127.0.0.1/32\"]}",
        process.getArgs2_6().getIngressConnectionEstablishmentRateLimiterBypass());
  }

  private static Stream<Arguments> pessimisticConnectivityCheckForAcceptedConnectionsParams() {
    return Stream.of(
        arguments(AWSNDSInstanceSize.M10, true),
        arguments(AWSNDSInstanceSize.M20, true),
        arguments(AWSNDSInstanceSize.M30, false));
  }

  @ParameterizedTest
  @MethodSource("pessimisticConnectivityCheckForAcceptedConnectionsParams")
  public void testUpdateInstanceMongoDProcess_pessimisticConnectivityCheckForAcceptedConnections(
      final AWSNDSInstanceSize pInstanceSize,
      final boolean pExpectedPessimisticConnectivityCheckForAcceptedConnectionsEnabled) {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription tempCluster = getTestReplicaSet(TEST_CLUSTER_NAME, "8.0.12");
    final ClusterDescription cluster =
        tempCluster
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setInstanceSize(pInstanceSize))
            .build();
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(0, containerId, cluster));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        cluster,
        Collections.emptyList(),
        null,
        hardware,
        hardware.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals(
        pExpectedPessimisticConnectivityCheckForAcceptedConnectionsEnabled,
        process.getArgs2_6().getPessimisticConnectivityCheckForAcceptedConnections());
  }

  @ParameterizedTest
  @MethodSource("pessimisticConnectivityCheckForAcceptedConnectionsParams")
  public void testUpdateInstanceMongoSProcess_pessimisticConnectivityCheckForAcceptedConnections(
      final AWSNDSInstanceSize pInstanceSize,
      final boolean pExpectedPessimisticConnectivityCheckForAcceptedConnectionsEnabled) {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ClusterDescription tempCluster =
        getTestShardedCluster(TEST_CLUSTER_NAME, "8.0.12", ClusterType.SHARDED, 2);
    final ClusterDescription cluster =
        tempCluster
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(new AWSHardwareSpec.Builder().setInstanceSize(pInstanceSize))
            .build();
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(0, containerId, cluster));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        cluster,
        hardware,
        hardware.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        true,
        true,
        1L,
        IFRProcessArgs.none());

    assertEquals(
        pExpectedPessimisticConnectivityCheckForAcceptedConnectionsEnabled,
        process.getArgs2_6().getPessimisticConnectivityCheckForAcceptedConnections());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_MTMGroup() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    doReturn(true).when(ndsGroup).isMTMHolder();

    final List<MTMClusterType> mtmClusterTypes = List.of(MTMClusterType.SHARED);

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_700rc0 =
        getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.SEVEN_ZERO_ZERO_RC0.getVersion());
    final ReplicaSetHardware hardware_700rc0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_700rc0));

    final Process process_700rc0 = new Process();
    process_700rc0.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_700rc0,
        mtmClusterTypes,
        null,
        hardware_700rc0,
        hardware_700rc0.getHardware().get(0),
        process_700rc0,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    // Ensures enableAccessToUserRoles exists and is set to false
    assertTrue(
        process_700rc0.getArgs2_6().getSetParameterMap().containsKey("enableAccessToUserRoles"));
    // Value to enableAccessToUserRoles is given as a string rather than a boolean
    assertEquals(
        "false", process_700rc0.getArgs2_6().getSetParameterMap().get("enableAccessToUserRoles"));

    // Below 7.0.0rc-0 will not have it set
    final ObjectId containerId63 = new ObjectId();
    final ClusterDescription testCluster_63 =
        getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.SIX_THREE_ZERO.getVersion());
    final ReplicaSetHardware hardware_63 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId63, testCluster_63));

    final Process process_63 = new Process();
    process_63.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_63,
        mtmClusterTypes,
        null,
        hardware_63,
        hardware_63.getHardware().get(0),
        process_63,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    // Ensures enableAccessToUserRoles does not exist
    assertFalse(
        process_63.getArgs2_6().getSetParameterMap().containsKey("enableAccessToUserRoles"));
    // Ensures internalQueryStatsRateLimit does not exist
    assertFalse(
        process_63.getArgs2_6().getSetParameterMap().containsKey("internalQueryStatsRateLimit"));

    // Not MTM Holder
    doReturn(false).when(ndsGroup).isMTMHolder();

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_700rc0,
        Collections.emptyList(),
        null,
        hardware_700rc0,
        hardware_700rc0.getHardware().get(0),
        process_700rc0,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    // Ensures enableAccessToUserRoles exists and is set to false
    assertFalse(
        process_700rc0.getArgs2_6().getSetParameterMap().containsKey("enableAccessToUserRoles"));
  }

  @Test
  public void updateInstanceMongoDProcess_CrossCloud_EncryptionKeyRegionOverride() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final ObjectId containerId = new ObjectId();
    final ObjectId clusterId = new ObjectId();

    // case: cross cloud cluster description, the encryptionKeyRegionOverride should be null
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getCrossCloudClusterDescription(clusterId, TEST_CLUSTER_NAME);
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, clusterDescription));

    final Process process = new Process();
    process.setName("process");
    process.setVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());

    final NDSEncryptionAtRest encryptionAtRest = ndsGroup.getEncryptionAtRest();
    final KeyManagementConfig awsKms = encryptionAtRest.getAWSKMS();
    doReturn(true).when(awsKms).isEnabled();

    final AutomationConfig automationConfig = new AutomationConfig();
    final Deployment deployment = new Deployment();
    final Process process1 = new Process();
    process1.setName("process");
    process1.setCluster(clusterDescription.getDeploymentClusterName());
    process1.setProcessType(ProcessType.MONGOD);
    process1.setLastKmipMasterKeyRotation(
        DateUtils.addDays(encryptionAtRest.getAWSKMS().getLastUpdatedKeyID().orElseThrow(), -1));
    process1.setVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());
    deployment.setProcesses(List.of(process1));
    automationConfig.setDeployment(deployment);

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        automationConfig,
        clusterDescription,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertNull(process.getEncryptionKeyRegionOverride());
  }

  @Test
  public void updateInstanceMongoDProcess_AWS_KMS() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_360 =
        getTestReplicaSet(TEST_CLUSTER_NAME, NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_360));

    final Process process = new Process();
    process.setName("process");
    process.setVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());

    final NDSEncryptionAtRest encryptionAtRest = ndsGroup.getEncryptionAtRest();
    final KeyManagementConfig awsKms = encryptionAtRest.getAWSKMS();
    doReturn(true).when(awsKms).isEnabled();

    final AutomationConfig automationConfig = new AutomationConfig();
    final Deployment deployment = new Deployment();
    final Process process1 = new Process();
    process1.setName("process");
    process1.setCluster(testCluster_360.getDeploymentClusterName());
    process1.setProcessType(ProcessType.MONGOD);
    process1.setLastKmipMasterKeyRotation(
        DateUtils.addDays(encryptionAtRest.getAWSKMS().getLastUpdatedKeyID().get(), -1));
    process1.setVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());
    deployment.setProcesses(List.of(process1));
    automationConfig.setDeployment(deployment);

    // case 1: lastKmipMasterKeyRotation from config is before lastKmipMasterKeyRotation
    // from the group
    final InstanceHardware instanceHardware = spy(hardware_360.getHardware().get(0));
    final AWSCloudProviderContainer container = mock(AWSCloudProviderContainer.class);
    doReturn(containerId).when(container).getId();
    doReturn(containerId).when(instanceHardware).getCloudContainerId();
    doReturn(Optional.of(container)).when(ndsGroup).getCloudProviderContainer(containerId);
    doReturn(CloudProvider.AWS).when(container).getCloudProvider();
    doReturn(AWSRegionName.US_EAST_1).when(container).getRegion();

    final AWSKMSEARPrivateEndpoint awsPE =
        new AWSKMSEARPrivateEndpoint.Builder()
            .id(new ObjectId())
            .status(CloudProviderPrivateEndpoint.Status.ACTIVE)
            .regionName(AWSRegionName.US_EAST_1)
            .privateEndpointResourceId("endpoint resourceId")
            .needsUpdateAfter(new Date())
            .build();
    doReturn(Optional.of(awsPE)).when(awsKms).getPrivateEndpointForRegion(AWSRegionName.US_EAST_1);

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        automationConfig,
        testCluster_360,
        Collections.emptyList(),
        null,
        hardware_360,
        instanceHardware,
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertEquals(
        process.getLastKmipMasterKeyRotation(),
        encryptionAtRest.getAWSKMS().getLastUpdatedKeyID().get());
    assertEquals(process.getKmipProxyPort().intValue(), NDSDefaults.KMIP_PROXY_PORT);
    assertEquals(process.getEncryptionProviderType(), EncryptionProviderType.AWS);
    assertEquals(process.getEncryptionKeyRegionOverride(), AWSRegionName.US_EAST_1.getValue());

    // case 2: lastKmipMasterKeyRotation from config is after lastKmipMasterKeyRotation
    // from the group
    process1.setLastKmipMasterKeyRotation(
        DateUtils.addDays(encryptionAtRest.getAWSKMS().getLastUpdatedKeyID().get(), 1));
    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        automationConfig,
        testCluster_360,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertEquals(
        process.getLastKmipMasterKeyRotation(),
        DateUtils.addDays(encryptionAtRest.getAWSKMS().getLastUpdatedKeyID().get(), 1));
  }

  @Test
  public void updateInstanceMongoDProcess_Azure_KMS() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_360 =
        getTestReplicaSet(TEST_CLUSTER_NAME, NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion())
            .copy()
            .setEncryptionAtRestProvider(EncryptionAtRestProvider.AZURE)
            .build();
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_360));

    final Process process = new Process();
    process.setName("process");
    process.setVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());

    final NDSEncryptionAtRest encryptionAtRest = ndsGroup.getEncryptionAtRest();
    final KeyManagementConfig azureKeyVault = encryptionAtRest.getAzureKeyVault();
    doReturn(true).when(azureKeyVault).isEnabled();

    // case 1: lastKmipMasterKeyRotation from config is before lastKmipMasterKeyRotation
    // from the group
    final AutomationConfig automationConfig = new AutomationConfig();
    final Deployment deployment = new Deployment();
    final Process process1 = new Process();
    process1.setName("process");
    process1.setCluster(testCluster_360.getDeploymentClusterName());
    process1.setProcessType(ProcessType.MONGOD);
    process1.setLastKmipMasterKeyRotation(
        DateUtils.addDays(encryptionAtRest.getAzureKeyVault().getLastUpdatedKeyID().get(), -1));
    process1.setVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());
    deployment.setProcesses(List.of(process1));
    automationConfig.setDeployment(deployment);

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        automationConfig,
        testCluster_360,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertEquals(
        process.getLastKmipMasterKeyRotation(),
        encryptionAtRest.getAzureKeyVault().getLastUpdatedKeyID().get());
    assertEquals(process.getKmipProxyPort().intValue(), NDSDefaults.KMIP_PROXY_PORT);
    assertEquals(process.getEncryptionProviderType(), EncryptionProviderType.AZURE);

    // case 2: lastKmipMasterKeyRotation from config is after lastKmipMasterKeyRotation
    // from the group
    process1.setLastKmipMasterKeyRotation(
        DateUtils.addDays(encryptionAtRest.getAzureKeyVault().getLastUpdatedKeyID().get(), 1));
    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        automationConfig,
        testCluster_360,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertEquals(
        process.getLastKmipMasterKeyRotation(),
        DateUtils.addDays(encryptionAtRest.getAWSKMS().getLastUpdatedKeyID().get(), 1));
  }

  @Test
  public void updateInstanceMongoDProcess_GCP_KMS() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_360 =
        getTestReplicaSet(TEST_CLUSTER_NAME, NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion())
            .copy()
            .setEncryptionAtRestProvider(EncryptionAtRestProvider.GCP)
            .build();
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_360));

    final Process process = new Process();
    process.setName("process");
    process.setVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());

    final NDSEncryptionAtRest encryptionAtRest = ndsGroup.getEncryptionAtRest();
    final KeyManagementConfig googleCloudKMS = encryptionAtRest.getGoogleCloudKMS();
    doReturn(true).when(googleCloudKMS).isEnabled();

    // case 1: lastKmipMasterKeyRotation from config is before lastKmipMasterKeyRotation
    // from the group
    final AutomationConfig automationConfig = new AutomationConfig();
    final Deployment deployment = new Deployment();
    final Process process1 = new Process();
    process1.setName("process");
    process1.setCluster(testCluster_360.getDeploymentClusterName());
    process1.setProcessType(ProcessType.MONGOD);
    process1.setLastKmipMasterKeyRotation(
        DateUtils.addDays(encryptionAtRest.getGoogleCloudKMS().getLastUpdatedKeyID().get(), -1));
    process1.setVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());
    deployment.setProcesses(List.of(process1));
    automationConfig.setDeployment(deployment);

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        automationConfig,
        testCluster_360,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertEquals(
        process.getLastKmipMasterKeyRotation(),
        encryptionAtRest.getGoogleCloudKMS().getLastUpdatedKeyID().get());
    assertEquals(process.getKmipProxyPort().intValue(), NDSDefaults.KMIP_PROXY_PORT);
    assertEquals(process.getEncryptionProviderType(), EncryptionProviderType.GCP);

    // case 2: lastKmipMasterKeyRotation from config is after lastKmipMasterKeyRotation
    // from the group
    process1.setLastKmipMasterKeyRotation(
        DateUtils.addDays(encryptionAtRest.getAWSKMS().getLastUpdatedKeyID().get(), 1));
    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        automationConfig,
        testCluster_360,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertEquals(
        process.getLastKmipMasterKeyRotation(),
        DateUtils.addDays(encryptionAtRest.getGoogleCloudKMS().getLastUpdatedKeyID().get(), 1));
  }

  @Test
  public void updateInstanceMongoDProcess_systemProject() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    doReturn(true).when(ndsGroup).isSystemProject();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_360 =
        getTestReplicaSet(TEST_CLUSTER_NAME, NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion())
            .copy()
            .setEncryptionAtRestProvider(EncryptionAtRestProvider.GCP)
            .build();
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_360));

    final Process process = new Process();
    process.setName("process");
    process.setVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());

    final NDSEncryptionAtRest encryptionAtRest = ndsGroup.getEncryptionAtRest();
    final KeyManagementConfig googleCloudKMS = encryptionAtRest.getGoogleCloudKMS();
    doReturn(true).when(googleCloudKMS).isEnabled();

    final AutomationConfig automationConfig = new AutomationConfig();
    final Deployment deployment = new Deployment();
    final Process process1 = new Process();
    process1.setName("process");
    process1.setCluster(testCluster_360.getDeploymentClusterName());
    process1.setProcessType(ProcessType.MONGOD);
    process1.setLastKmipMasterKeyRotation(
        DateUtils.addDays(encryptionAtRest.getGoogleCloudKMS().getLastUpdatedKeyID().get(), -1));
    process1.setVersion(NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion());
    deployment.setProcesses(List.of(process1));
    automationConfig.setDeployment(deployment);
    final ProcessArguments2_6 args = new ProcessArguments2_6();

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        automationConfig,
        testCluster_360,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        args,
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertEquals("cache_size=1536MB", args.getWiredTigerEngineConfigString());
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess() {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    final ClusterDescription clusterDescription = getMockedClusterDescription();

    final ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails details =
        ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails.builder()
            .defaultStartupFCV(null)
            .clusterDescription(clusterDescription)
            .mtmClusterTypes(Collections.emptyList())
            .searchMongoArgs(new SearchMongoArgs())
            .build();

    // diagnosticDataCollectionDirectorySizeMB is supported on versions 3.2+
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(Version.fromString("3.2.0")).build());
    assertEquals(400, args.getSetParameterMap().get("diagnosticDataCollectionDirectorySizeMB"));

    // No tags and no existing params - set param to true
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(Version.fromString("3.4.0")).build());
    assertTrue((Boolean) args.getSetParameterMap().get("ttlMonitorEnabled"));
    assertEquals(60, args.getSetParameterMap().get("watchdogPeriodSeconds"));

    // TTL_MONITOR_OFF tag - set param to false
    final ClusterDescription cdTTLMontitorOff = getMockedClusterDescription();
    doReturn(Set.of(ClusterTag.TTL_MONITOR_OFF)).when(cdTTLMontitorOff).getClusterTags();
    args.getSetParameterMap().remove("ttlMonitorEnabled");
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .clusterDescription(cdTTLMontitorOff)
            .mongoDBVersion(Version.fromString("3.4.0"))
            .build());
    assertFalse((Boolean) args.getSetParameterMap().get("ttlMonitorEnabled"));

    // No tags and existing params - maintain existing param
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(Version.fromString("3.4.0")).build());
    assertFalse((Boolean) args.getSetParameterMap().get("ttlMonitorEnabled"));

    // No support for replOpWriteConcernCountersInServerStatus and
    // suppressNoTLSPeerCertificateWarning
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(Version.fromString("3.4.0")).build());
    assertFalse(
        args.getSetParameterMap().containsKey("reportOpWriteConcernCountersInServerStatus"));
    assertNull(args.getSetParameterMap().get("suppressNoTLSPeerCertificateWarning"));

    // 3.6 support for replOpWriteConcernCountersInServerStatus and
    // suppressNoTLSPeerCertificateWarning
    // first supported version
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(Version.fromString("3.6.11")).build());

    assertTrue(
        (Boolean) args.getSetParameterMap().get("reportOpWriteConcernCountersInServerStatus"));
    assertTrue((Boolean) args.getSetParameterMap().get("suppressNoTLSPeerCertificateWarning"));

    // later supported versions for replOpWriteConcernCountersInServerStatus
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(Version.fromString("3.6.12")).build());

    assertTrue(
        (Boolean) args.getSetParameterMap().get("reportOpWriteConcernCountersInServerStatus"));
    assertTrue((Boolean) args.getSetParameterMap().get("suppressNoTLSPeerCertificateWarning"));

    // condition: (4.0.x and ver >= 4.0.18 ) OR (4.2.x and ver >= 4.2.4) OR ver >= 4.4)
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.parse("4.0.18")).build());
    assertTrue((Boolean) args.getSetParameterMap().get("ldapForceMultiThreadMode"));
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.parse("4.0.11")).build());
    assertNull(args.getSetParameterMap().get("ldapForceMultiThreadMode"));
    args.getSetParameterMap().clear();

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.parse("4.2.4")).build());
    assertTrue((Boolean) args.getSetParameterMap().get("ldapForceMultiThreadMode"));
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.parse("4.2.2")).build());
    assertNull(args.getSetParameterMap().get("ldapForceMultiThreadMode"));
    args.getSetParameterMap().clear();

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.FOUR_FOUR_ZERO).build());
    assertTrue(args.getSetParameterMap().containsKey("ldapForceMultiThreadMode"));
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.FOUR_THREE_ZERO).build());
    assertNull(args.getSetParameterMap().get("ldapForceMultiThreadMode"));
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.THREE_SIX_ZERO).build());
    assertNull(args.getSetParameterMap().get("ldapForceMultiThreadMode"));

    // 4.0 support for replOpWriteConcernCountersInServerStatus and
    // suppressNoTLSPeerCertificateWarning
    // first supported version
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(Version.fromString("4.0.6")).build());

    assertTrue(
        (Boolean) args.getSetParameterMap().get("reportOpWriteConcernCountersInServerStatus"));
    assertTrue((Boolean) args.getSetParameterMap().get("suppressNoTLSPeerCertificateWarning"));

    // later supported versions
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(Version.fromString("4.0.7")).build());

    assertTrue(
        (Boolean) args.getSetParameterMap().get("reportOpWriteConcernCountersInServerStatus"));
    assertTrue((Boolean) args.getSetParameterMap().get("suppressNoTLSPeerCertificateWarning"));

    // suppressNoTLSPeerCertificateWarning and reportOpWriteConcernCountersInServerStatus are not
    // overwritten if present even when the version is supported
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(Version.fromString("4.0.7")).build());

    args.getSetParameterMap().put("suppressNoTLSPeerCertificateWarning", false);
    args.getSetParameterMap().put("reportOpWriteConcernCountersInServerStatus", false);
    assertFalse((Boolean) args.getSetParameterMap().get("suppressNoTLSPeerCertificateWarning"));
    assertFalse(
        (Boolean) args.getSetParameterMap().get("reportOpWriteConcernCountersInServerStatus"));

    // mongotHost added by default for any mongodb version that supports it
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .mongoDBVersion(Version.fromString("4.0.7"))
            .searchMongoArgs(new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.TCP))
            .build());
    assertNull(args.getSetParameterMap().get("mongotHost"));

    args.getSetParameterMap().clear();
    args.getSetParameterMap().put("skipAuthenticationToMongot", true);
    args.getSetParameterMap().put("pinTaskExecCursorConns", true);
    args.getSetParameterMap().put("searchTLSMode", "disabled");
    // set on supported development versions
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .mongoDBVersion(Version.fromString("6.0.0"))
            .searchMongoArgs(new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.TCP))
            .build());
    assertEquals(
        NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS, args.getSetParameterMap().get("mongotHost"));
    // Make sure that `skipAuthenticationToMongot` and `skipAuthenticationToMongot` are removed from
    // the parameter map.
    assertFalse(args.getSetParameterMap().containsKey("skipAuthenticationToMongot"));
    assertFalse(args.getSetParameterMap().containsKey("pinTaskExecCursorConns"));
    // Make sure searchTLSMode is not present
    assertFalse(args.getSetParameterMap().containsKey("searchTLSMode"));

    args.getSetParameterMap().clear();
    args.getSetParameterMap().put("skipAuthenticationToMongot", true);
    args.getSetParameterMap().put("pinTaskExecCursorConns", true);
    // will be set for the 4.2.0
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .mongoDBVersion(Version.fromString("6.0.0"))
            .searchMongoArgs(new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.TCP))
            .build());
    assertEquals(
        NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS, args.getSetParameterMap().get("mongotHost"));
    // Make sure that `skipAuthenticationToMongot` and `skipAuthenticationToMongot` are removed from
    // the parameter map.
    assertFalse(args.getSetParameterMap().containsKey("skipAuthenticationToMongot"));
    assertFalse(args.getSetParameterMap().containsKey("pinTaskExecCursorConns"));

    args.getSetParameterMap().clear();
    args.getSetParameterMap().put("skipAuthenticationToMongot", true);
    args.getSetParameterMap().put("pinTaskExecCursorConns", true);
    // will be set for future versions
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .mongoDBVersion(Version.fromString("6.0.0"))
            .searchMongoArgs(new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.TCP))
            .build());
    assertEquals(
        NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS, args.getSetParameterMap().get("mongotHost"));
    // Make sure that `skipAuthenticationToMongot` and `skipAuthenticationToMongot` are removed from
    // the parameter map.
    assertFalse(args.getSetParameterMap().containsKey("skipAuthenticationToMongot"));
    assertFalse(args.getSetParameterMap().containsKey("pinTaskExecCursorConns"));
    args.getSetParameterMap().clear();

    // won't call mongot through unix domain socket if Mongodb version is too old
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .mongoDBVersion(Version.fromString("5.0.0"))
            .searchMongoArgs(new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.UNIX_DOMAIN))
            .build());
    assertEquals(
        NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS, args.getSetParameterMap().get("mongotHost"));
    assertFalse(args.getSetParameterMap().containsKey("skipAuthenticationToMongot"));
    assertFalse(args.getSetParameterMap().containsKey("pinTaskExecCursorConns"));
    args.getSetParameterMap().clear();

    // will call mongot through unix domain socket if configured
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .mongoDBVersion(Version.fromString("6.0.0"))
            .searchMongoArgs(new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.UNIX_DOMAIN))
            .build());
    assertEquals(
        EnvoyOnAtlasHost.MONGOT_UNIX_DOMAIN_SOCKET, args.getSetParameterMap().get("mongotHost"));
    assertTrue((Boolean) args.getSetParameterMap().get("skipAuthenticationToMongot"));
    assertTrue((Boolean) args.getSetParameterMap().get("pinTaskExecCursorConns"));
    args.getSetParameterMap().clear();

    // call mongot through grpc socket if configured
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .mongoDBVersion(Version.fromString("6.0.0"))
            .searchMongoArgs(new SearchMongoArgs(0, MongotCallOptions.GRPC))
            .build());
    assertEquals("127.0.0.1:28000", args.getSetParameterMap().get("mongotHost"));
    assertTrue((Boolean) args.getSetParameterMap().get("useGrpcForSearch"));
    assertTrue((Boolean) args.getSetParameterMap().get("skipAuthenticationToMongot"));
    assertTrue((Boolean) args.getSetParameterMap().get("pinTaskExecCursorConns"));
    args.getSetParameterMap().clear();

    // ensure we don't set a parameter that doesn't exist yet on older versions of mongod
    // minSnapshotHistoryWindowInSeconds and lockCodeSegmentsInMemory should be removed at this step
    // if it's set before
    args.setMinSnapshotHistoryWindowInSeconds(300);
    args.getSetParameterMap().put("lockCodeSegmentsInMemory", true);
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(Version.fromString("4.4")).build());

    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.FEATURE_FLAG_TENANT_MIGRATIONS_SERVER_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TENANT_MIGRATION_DISABLE_X509_AUTH_SERVER_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TENANT_MIGRATION_GARBAGE_COLLECTION_DELAY_MS_SERVER_PARAM));
    assertNull(
        args.getSetParameterMap().get(ServerlessNDSDefaults.TTL_MONITOR_SLEEP_SECS_SERVER_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(
                ServerlessNDSDefaults
                    .AGGREGATE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(
                ServerlessNDSDefaults.PROFILE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    assertNull(
        args.getSetParameterMap().get(ServerlessNDSDefaults.DOCUMENT_UNIT_SIZE_BYTES_SERVER_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.INDEX_ENTRY_UNIT_SIZE_BYTES_SERVER_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TOTAL_UNIT_WRITE_SIZE_BYTES_SERVER_PARAM));
    assertNull(
        args.getSetParameterMap().get(ServerlessNDSDefaults.TRANSACTION_SIZE_LIMIT_BYTES_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_MINIMUM_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_IDLE_TIME_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_MAX_RETRIES_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_BACKOFF_BASE_MS_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TENANT_MIGRATION_BLOCKING_STATE_TIMEOUT_MS_PARAM));
    assertNull(args.getSetParameterMap().get(NDSDefaults.MTM_INTERNAL_PIPELINE_LENGTH_LIMIT_PARAM));
    assertNull(args.getSetParameterMap().get(NDSDefaults.MIN_SNAPSHOT_HISTORY_WINDOW_IN_SECONDS));
    assertNull(args.getSetParameterMap().get("lockCodeSegmentsInMemory"));

    // MTM cluster tests
    final ClusterDescription mtmClusterDescription = getMockedClusterDescription();
    doReturn(true).when(mtmClusterDescription).isMTM();

    final List<MTMClusterType> serverlessMtmClusterTypes = List.of(MTMClusterType.SERVERLESS);

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .clusterDescription(mtmClusterDescription)
            .mtmClusterTypes(serverlessMtmClusterTypes)
            .isServerlessMTMHolder(true)
            .mongoDBVersion(Version.fromString("4.9.0-alpha7"))
            .build());
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(ServerlessNDSDefaults.FEATURE_FLAG_TENANT_MIGRATIONS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(ServerlessNDSDefaults.TENANT_MIGRATION_DISABLE_X509_AUTH_SERVER_PARAM));
    assertEquals(
        3000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TENANT_MIGRATION_GARBAGE_COLLECTION_DELAY_MS_SERVER_PARAM));
    assertEquals(
        60,
        args.getSetParameterMap().get(ServerlessNDSDefaults.TTL_MONITOR_SLEEP_SECS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(
                    ServerlessNDSDefaults
                        .AGGREGATE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(
                    ServerlessNDSDefaults
                        .PROFILE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    assertEquals(
        4096,
        args.getSetParameterMap().get(ServerlessNDSDefaults.DOCUMENT_UNIT_SIZE_BYTES_SERVER_PARAM));
    assertEquals(
        256,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.INDEX_ENTRY_UNIT_SIZE_BYTES_SERVER_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TOTAL_UNIT_WRITE_SIZE_BYTES_SERVER_PARAM));
    assertEquals(
        734_003_200L,
        args.getSetParameterMap().get(ServerlessNDSDefaults.TRANSACTION_SIZE_LIMIT_BYTES_PARAM));
    assertEquals(
        10_000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_MINIMUM_PARAM));
    assertEquals(
        3_720,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_IDLE_TIME_PARAM));
    assertEquals(
        15_000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TENANT_MIGRATION_BLOCKING_STATE_TIMEOUT_MS_PARAM));
    assertEquals(
        50, args.getSetParameterMap().get(NDSDefaults.MTM_INTERNAL_PIPELINE_LENGTH_LIMIT_PARAM));

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .clusterDescription(mtmClusterDescription)
            .mongoDBVersion(Version.fromString("4.9.0-rc0"))
            .build());
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(ServerlessNDSDefaults.FEATURE_FLAG_TENANT_MIGRATIONS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(ServerlessNDSDefaults.TENANT_MIGRATION_DISABLE_X509_AUTH_SERVER_PARAM));
    assertEquals(
        3000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TENANT_MIGRATION_GARBAGE_COLLECTION_DELAY_MS_SERVER_PARAM));
    assertEquals(
        60,
        args.getSetParameterMap().get(ServerlessNDSDefaults.TTL_MONITOR_SLEEP_SECS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(
                    ServerlessNDSDefaults
                        .AGGREGATE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(
                    ServerlessNDSDefaults
                        .PROFILE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    assertEquals(
        4096,
        args.getSetParameterMap().get(ServerlessNDSDefaults.DOCUMENT_UNIT_SIZE_BYTES_SERVER_PARAM));
    assertEquals(
        256,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.INDEX_ENTRY_UNIT_SIZE_BYTES_SERVER_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TOTAL_UNIT_WRITE_SIZE_BYTES_SERVER_PARAM));
    assertEquals(
        734_003_200L,
        args.getSetParameterMap().get(ServerlessNDSDefaults.TRANSACTION_SIZE_LIMIT_BYTES_PARAM));
    assertEquals(
        10_000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_MINIMUM_PARAM));
    assertEquals(
        3_720,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_IDLE_TIME_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_MAX_RETRIES_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_BACKOFF_BASE_MS_PARAM));
    assertEquals(
        15_000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TENANT_MIGRATION_BLOCKING_STATE_TIMEOUT_MS_PARAM));
    assertEquals(
        50, args.getSetParameterMap().get(NDSDefaults.MTM_INTERNAL_PIPELINE_LENGTH_LIMIT_PARAM));

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .clusterDescription(mtmClusterDescription)
            .mtmClusterTypes(serverlessMtmClusterTypes)
            .isServerlessMTMHolder(true)
            .mongoDBVersion(Version.fromString("4.9"))
            .build());
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(ServerlessNDSDefaults.FEATURE_FLAG_TENANT_MIGRATIONS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(ServerlessNDSDefaults.TENANT_MIGRATION_DISABLE_X509_AUTH_SERVER_PARAM));
    assertEquals(
        3000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TENANT_MIGRATION_GARBAGE_COLLECTION_DELAY_MS_SERVER_PARAM));
    assertEquals(
        60,
        args.getSetParameterMap().get(ServerlessNDSDefaults.TTL_MONITOR_SLEEP_SECS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(
                    ServerlessNDSDefaults
                        .AGGREGATE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(
                    ServerlessNDSDefaults
                        .PROFILE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    assertEquals(
        4096,
        args.getSetParameterMap().get(ServerlessNDSDefaults.DOCUMENT_UNIT_SIZE_BYTES_SERVER_PARAM));
    assertEquals(
        256,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.INDEX_ENTRY_UNIT_SIZE_BYTES_SERVER_PARAM));
    assertEquals(
        1024,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TOTAL_UNIT_WRITE_SIZE_BYTES_SERVER_PARAM));
    assertEquals(
        734_003_200L,
        args.getSetParameterMap().get(ServerlessNDSDefaults.TRANSACTION_SIZE_LIMIT_BYTES_PARAM));
    assertEquals(
        10_000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_MINIMUM_PARAM));
    assertEquals(
        3_720,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_IDLE_TIME_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_MAX_RETRIES_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_BACKOFF_BASE_MS_PARAM));
    assertEquals(
        15_000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TENANT_MIGRATION_BLOCKING_STATE_TIMEOUT_MS_PARAM));
    assertEquals(
        50, args.getSetParameterMap().get(NDSDefaults.MTM_INTERNAL_PIPELINE_LENGTH_LIMIT_PARAM));

    // minSnapshotHistoryWindowInSeconds should be kept if it's set before
    args.setMinSnapshotHistoryWindowInSeconds(300);

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .clusterDescription(mtmClusterDescription)
            .mtmClusterTypes(serverlessMtmClusterTypes)
            .isServerlessMTMHolder(true)
            .mongoDBVersion(Version.fromString("5.0"))
            .build());
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(ServerlessNDSDefaults.FEATURE_FLAG_TENANT_MIGRATIONS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(ServerlessNDSDefaults.TENANT_MIGRATION_DISABLE_X509_AUTH_SERVER_PARAM));
    assertEquals(
        3000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TENANT_MIGRATION_GARBAGE_COLLECTION_DELAY_MS_SERVER_PARAM));
    assertEquals(
        60,
        args.getSetParameterMap().get(ServerlessNDSDefaults.TTL_MONITOR_SLEEP_SECS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(
                    ServerlessNDSDefaults
                        .AGGREGATE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    assertTrue(
        (Boolean)
            args.getSetParameterMap()
                .get(
                    ServerlessNDSDefaults
                        .PROFILE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    assertEquals(
        4096,
        args.getSetParameterMap().get(ServerlessNDSDefaults.DOCUMENT_UNIT_SIZE_BYTES_SERVER_PARAM));
    assertEquals(
        256,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.INDEX_ENTRY_UNIT_SIZE_BYTES_SERVER_PARAM));
    assertEquals(
        1024,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TOTAL_UNIT_WRITE_SIZE_BYTES_SERVER_PARAM));
    assertEquals(
        734_003_200L,
        args.getSetParameterMap().get(ServerlessNDSDefaults.TRANSACTION_SIZE_LIMIT_BYTES_PARAM));
    assertEquals(
        10_000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_MINIMUM_PARAM));
    assertEquals(
        3_720,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_IDLE_TIME_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_MAX_RETRIES_PARAM));
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_BACKOFF_BASE_MS_PARAM));
    assertEquals(
        15_000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TENANT_MIGRATION_BLOCKING_STATE_TIMEOUT_MS_PARAM));
    assertEquals(
        50, args.getSetParameterMap().get(NDSDefaults.MTM_INTERNAL_PIPELINE_LENGTH_LIMIT_PARAM));
    assertEquals(
        300, args.getSetParameterMap().get(NDSDefaults.MIN_SNAPSHOT_HISTORY_WINDOW_IN_SECONDS));

    // MongoDB 5.1+ for serverless MTMs does not support FEATURE_FLAG_TENANT_MIGRATIONS_SERVER_PARAM
    // anymore
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .clusterDescription(mtmClusterDescription)
            .mtmClusterTypes(serverlessMtmClusterTypes)
            .isServerlessMTMHolder(true)
            .mongoDBVersion(Version.fromString("5.1.0-rc0"))
            .build());
    assertNull(
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.FEATURE_FLAG_TENANT_MIGRATIONS_SERVER_PARAM));

    // MongoDB 6.0 must set temporarilyUnavailableMaxRetries and temporarilyUnavailableBackoffBaseMs
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .clusterDescription(mtmClusterDescription)
            .mtmClusterTypes(serverlessMtmClusterTypes)
            .isServerlessMTMHolder(true)
            .mongoDBVersion(VersionUtils.SIX_ZERO_ZERO)
            .build());
    assertEquals(
        1,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_MAX_RETRIES_PARAM));
    assertEquals(
        3000,
        args.getSetParameterMap()
            .get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_BACKOFF_BASE_MS_PARAM));
    // Validate allowDiskUseByDefault is not set in clusters that is not 6.0+ MTMs
    assertFalse(
        (Boolean) args.getSetParameterMap().get("allowDiskUseByDefault"),
        "Disallow disk usage in 6.0+ MTMs");

    // condition: (4.4.x and ver >= 4.4.20 ) OR (5.0.x and ver >= 5.0.16) OR (6.0.x and ver >=
    // 6.0.5)  OR ver >= 6.3)
    // 4.4.2 should not get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.FOUR_FOUR_TWO).build());

    assertNull(
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 4.4.20 clusters should get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.FOUR_FOUR_TWENTY).build());

    assertEquals(
        CommonStartupSetparams.InternalQueryGlobalProfilingFilter.VAlUE,
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 5.0.2 should not get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.FIVE_ZERO_TWO).build());

    assertNull(
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 5.0.16 clusters should get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.FIVE_ZERO_SIXTEEN).build());

    assertEquals(
        CommonStartupSetparams.InternalQueryGlobalProfilingFilter.VAlUE,
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 6.0.4 should not get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_ZERO_FOUR).build());

    assertNull(
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 6.0.5 clusters should get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_ZERO_FIVE).build());

    assertEquals(
        CommonStartupSetparams.InternalQueryGlobalProfilingFilter.VAlUE,
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 6.3.x clusters should get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_THREE_ZERO).build());

    assertEquals(
        CommonStartupSetparams.InternalQueryGlobalProfilingFilter.VAlUE,
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // Clusters with encryption at rest enabled do not get File Copy Based Initial Sync params
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_ZERO_ZERO).build());

    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.FIVE_ONE_ZERO).build());

    // Pre 6.0 NVMe clusters should not get File Copy Based Initial Sync params
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .clusterDescription(mtmClusterDescription)
            .mtmClusterTypes(serverlessMtmClusterTypes)
            .isServerlessMTMHolder(true)
            .mongoDBVersion(VersionUtils.FIVE_THREE_ZERO_RC0)
            .build());
    assertFalse(
        args.getSetParameterMap().containsKey("allowDiskUseByDefault"),
        "Do not set Disallow disk usage in 5.3 MTMs");
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.FIVE_THREE_ZERO_RC0).build());

    assertFalse(
        args.getSetParameterMap().containsKey("allowDiskUseByDefault"),
        "Do not set Disallow disk usage in dedicated clusters");

    // Test setting parameter map with one-off versions
    final List<Version> versionsLockingCodeSegmentsInMemory =
        List.of(
            VersionUtils.MIN_LOCK_CODE_SEGMENTS_IN_MEMORY_4_4,
            VersionUtils.MIN_LOCK_CODE_SEGMENTS_IN_MEMORY_5_0,
            VersionUtils.MIN_LOCK_CODE_SEGMENTS_IN_MEMORY_6_0,
            VersionUtils.SEVEN_ZERO_ZERO,
            VersionUtils.SEVEN_ONE_ZERO);
    final List<Version> versionsNotLockingCodeSegmentsInMemory =
        List.of(
            VersionUtils.FOUR_TWO_ZERO,
            VersionUtils.FOUR_FOUR_TWENTY_TWO,
            VersionUtils.FIVE_ZERO_EIGHTEEN,
            VersionUtils.SIX_ZERO_FIVE,
            VersionUtils.FOUR_FOUR_TWO,
            VersionUtils.FOUR_NINE_ZERO,
            VersionUtils.FIVE_THREE_ZERO,
            VersionUtils.SIX_THREE_ZERO);

    for (final Version version : versionsLockingCodeSegmentsInMemory) {
      args.getSetParameterMap().clear();

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          details.toBuilder().args(args).mongoDBVersion(version).build());
      assertTrue((boolean) args.getSetParameterMap().get("lockCodeSegmentsInMemory"));
    }

    for (final Version version : versionsNotLockingCodeSegmentsInMemory) {
      args.getSetParameterMap().clear();
      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          details.toBuilder().args(args).mongoDBVersion(version).build());
      assertNull(args.getSetParameterMap().get("lockCodeSegmentsInMemory"));
    }

    // Test multiple successive upgrades
    // 6.0.6+ Clusters should have lockCodeSegmentsInMemory set.
    // If upgraded to 6.3+ it should be removed again since it wasn't backported
    // If upgraded to 7.0.x+ it should be set again
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_ZERO_SIX).build());
    assertTrue((boolean) args.getSetParameterMap().get("lockCodeSegmentsInMemory"));
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_THREE_ZERO).build());
    assertNull(args.getSetParameterMap().get("lockCodeSegmentsInMemory"));
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.SEVEN_ZERO_ZERO).build());
    assertTrue((boolean) args.getSetParameterMap().get("lockCodeSegmentsInMemory"));

    // 6.0.8+ Clusters should have searchIndexManagementHostAndPort set.
    // If upgraded to 6.1.0+ it should be removed again since it wasn't backported
    // If upgraded to 7.0.x+ it should be set again
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_ZERO_SIX).build());
    assertNull(args.getSetParameterMap().get("searchIndexManagementHostAndPort"));

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_ZERO_EIGHT).build());
    assertEquals(
        EnvoyOnAtlasHost.MMS_UNIX_DOMAIN_SOCKET,
        args.getSetParameterMap().get("searchIndexManagementHostAndPort"));
    assertDisaggregatedStorgeConfig(args, null);

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_THREE_ZERO).build());
    assertNull(args.getSetParameterMap().get("searchIndexManagementHostAndPort"));
    assertDisaggregatedStorgeConfig(args, null);

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(VersionUtils.SEVEN_ZERO_ZERO).build());
    assertEquals(
        EnvoyOnAtlasHost.MMS_UNIX_DOMAIN_SOCKET,
        args.getSetParameterMap().get("searchIndexManagementHostAndPort"));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .args(args)
            .mongoDBVersion(VersionUtils.MIN_GLOBAL_SEARCH_TLS_MODE)
            .build());
    assertEquals("disabled", args.getSetParameterMap().get("searchTLSMode"));
  }

  private static List<Arguments> providerSetParameterMapForMongoDProcess_Tenant() {
    final Set<String> sharedAndFlexParams =
        Set.of("searchIndexManagementHostAndPort", "enableTimeoutOfInactiveSessionCursors");
    final Set<String> serverlessAndFlexParams =
        Set.of(
            ServerlessNDSDefaults.AGGREGATE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM,
            ServerlessNDSDefaults.PROFILE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM);
    final Set<String> serverlessParams =
        Set.of(
            ServerlessNDSDefaults.TENANT_MIGRATION_DISABLE_X509_AUTH_SERVER_PARAM,
            ServerlessNDSDefaults.TENANT_MIGRATION_GARBAGE_COLLECTION_DELAY_MS_SERVER_PARAM,
            ServerlessNDSDefaults.TTL_MONITOR_SLEEP_SECS_SERVER_PARAM,
            ServerlessNDSDefaults.AGGREGATE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM,
            ServerlessNDSDefaults.PROFILE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM,
            ServerlessNDSDefaults.DOCUMENT_UNIT_SIZE_BYTES_SERVER_PARAM,
            ServerlessNDSDefaults.INDEX_ENTRY_UNIT_SIZE_BYTES_SERVER_PARAM,
            ServerlessNDSDefaults.TRANSACTION_SIZE_LIMIT_BYTES_PARAM,
            ServerlessNDSDefaults.TENANT_MIGRATION_BLOCKING_STATE_TIMEOUT_MS_PARAM,
            ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_MINIMUM_PARAM,
            ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_IDLE_TIME_PARAM,
            ServerlessNDSDefaults.TOTAL_UNIT_WRITE_SIZE_BYTES_SERVER_PARAM,
            ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_MAX_RETRIES_PARAM,
            ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_BACKOFF_BASE_MS_PARAM);
    return List.of(
        Arguments.of("testServerless", true, List.of(MTMClusterType.SERVERLESS), serverlessParams),
        Arguments.of("testShared", false, List.of(MTMClusterType.SHARED), sharedAndFlexParams),
        Arguments.of(
            "testFlex",
            false,
            List.of(MTMClusterType.FLEX),
            Stream.of(sharedAndFlexParams, serverlessAndFlexParams)
                .flatMap(Set::stream)
                .collect(Collectors.toSet())),
        Arguments.of(
            "testServerlessMigratingToFlex",
            true,
            List.of(MTMClusterType.SERVERLESS, MTMClusterType.FLEX),
            serverlessParams),
        Arguments.of(
            "testSharedMigratingToFlex",
            false,
            List.of(MTMClusterType.SHARED, MTMClusterType.FLEX),
            sharedAndFlexParams),
        Arguments.of(
            "testServerlessMigratedToFlex",
            true,
            List.of(MTMClusterType.FLEX),
            Stream.of(sharedAndFlexParams, serverlessAndFlexParams)
                .flatMap(Set::stream)
                .collect(Collectors.toSet())),
        Arguments.of(
            "testSharedMigratedToFlex",
            false,
            List.of(MTMClusterType.FLEX),
            Stream.of(sharedAndFlexParams, serverlessAndFlexParams)
                .flatMap(Set::stream)
                .collect(Collectors.toSet())));
  }

  @ParameterizedTest(name = "testSetParameterMapForMongoDProcess_Tenant_{index}_{0}")
  @MethodSource("providerSetParameterMapForMongoDProcess_Tenant")
  public void testSetParameterMapForMongoDProcess_Tenant(
      @SuppressWarnings("unused") final String pTestName,
      final boolean pIsServerlessMtmHolder,
      final List<MTMClusterType> pMtmClusterTypes,
      final Set<String> pExpectParams) {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    final ClusterDescription mtmClusterDescription = getMockedClusterDescription();
    doReturn(true).when(mtmClusterDescription).isMTM();

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails.builder()
            .args(args)
            .clusterDescription(mtmClusterDescription)
            .mtmClusterTypes(pMtmClusterTypes)
            .isServerlessMTMHolder(pIsServerlessMtmHolder)
            .defaultStartupFCV(null)
            .searchMongoArgs(new SearchMongoArgs())
            .mongoDBVersion(VersionUtils.EIGHT_ZERO_ZERO)
            .build());

    final Map<String, Object> setParams = args.getSetParameterMap();

    // Search index management
    if (pExpectParams.contains("searchIndexManagementHostAndPort")) {
      assertEquals(
          EnvoyOnAtlasHost.MMS_UNIX_DOMAIN_SOCKET,
          setParams.get("searchIndexManagementHostAndPort"));
    } else {
      assertFalse(setParams.containsKey("searchIndexManagementHostAndPort"));
    }

    // Session cursor timeout
    if (pExpectParams.contains("enableTimeoutOfInactiveSessionCursors")) {
      assertTrue((Boolean) setParams.get("enableTimeoutOfInactiveSessionCursors"));
    } else {
      assertFalse(setParams.containsKey("enableTimeoutOfInactiveSessionCursors"));
    }

    // Parameters common to FLEX and SERVERLESS
    if (pExpectParams.contains(
        ServerlessNDSDefaults.AGGREGATE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.AGGREGATE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM_VALUE,
          setParams.get(
              ServerlessNDSDefaults.AGGREGATE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    } else {
      assertFalse(
          setParams.containsKey(
              ServerlessNDSDefaults.AGGREGATE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    }

    if (pExpectParams.contains(
        ServerlessNDSDefaults.PROFILE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.PROFILE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM_VALUE,
          setParams.get(
              ServerlessNDSDefaults.PROFILE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    } else {
      assertFalse(
          setParams.containsKey(
              ServerlessNDSDefaults.PROFILE_OPERATION_RESOURCE_CONSUMPTION_METRICS_SERVER_PARAM));
    }

    // Parameters specific to SERVERLESS
    if (pExpectParams.contains(
        ServerlessNDSDefaults.TENANT_MIGRATION_DISABLE_X509_AUTH_SERVER_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.TENANT_MIGRATION_DISABLE_X509_AUTH_SERVER_PARAM_VALUE,
          setParams.get(ServerlessNDSDefaults.TENANT_MIGRATION_DISABLE_X509_AUTH_SERVER_PARAM));
    } else {
      assertFalse(
          setParams.containsKey(
              ServerlessNDSDefaults.TENANT_MIGRATION_DISABLE_X509_AUTH_SERVER_PARAM));
    }

    if (pExpectParams.contains(
        ServerlessNDSDefaults.TENANT_MIGRATION_GARBAGE_COLLECTION_DELAY_MS_SERVER_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.TENANT_MIGRATION_GARBAGE_COLLECTION_DELAY_MS_SERVER_PARAM_VALUE,
          setParams.get(
              ServerlessNDSDefaults.TENANT_MIGRATION_GARBAGE_COLLECTION_DELAY_MS_SERVER_PARAM));
    } else {
      assertFalse(
          setParams.containsKey(
              ServerlessNDSDefaults.TENANT_MIGRATION_GARBAGE_COLLECTION_DELAY_MS_SERVER_PARAM));
    }

    if (pExpectParams.contains(ServerlessNDSDefaults.TTL_MONITOR_SLEEP_SECS_SERVER_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.TTL_MONITOR_SLEEP_SECS_SERVER_PARAM_VALUE,
          setParams.get(ServerlessNDSDefaults.TTL_MONITOR_SLEEP_SECS_SERVER_PARAM));
    } else {
      assertFalse(setParams.containsKey(ServerlessNDSDefaults.TTL_MONITOR_SLEEP_SECS_SERVER_PARAM));
    }

    if (pExpectParams.contains(ServerlessNDSDefaults.DOCUMENT_UNIT_SIZE_BYTES_SERVER_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.DOCUMENT_UNIT_SIZE_BYTES_SERVER_PARAM_VALUE,
          setParams.get(ServerlessNDSDefaults.DOCUMENT_UNIT_SIZE_BYTES_SERVER_PARAM));
    } else {
      assertFalse(
          setParams.containsKey(ServerlessNDSDefaults.DOCUMENT_UNIT_SIZE_BYTES_SERVER_PARAM));
    }

    if (pExpectParams.contains(ServerlessNDSDefaults.INDEX_ENTRY_UNIT_SIZE_BYTES_SERVER_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.INDEX_ENTRY_UNIT_SIZE_BYTES_SERVER_PARAM_VALUE,
          setParams.get(ServerlessNDSDefaults.INDEX_ENTRY_UNIT_SIZE_BYTES_SERVER_PARAM));
    } else {
      assertFalse(
          setParams.containsKey(ServerlessNDSDefaults.INDEX_ENTRY_UNIT_SIZE_BYTES_SERVER_PARAM));
    }

    if (pExpectParams.contains(ServerlessNDSDefaults.TRANSACTION_SIZE_LIMIT_BYTES_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.TRANSACTION_SIZE_LIMIT_BYTES_VALUE,
          setParams.get(ServerlessNDSDefaults.TRANSACTION_SIZE_LIMIT_BYTES_PARAM));
    } else {
      assertFalse(setParams.containsKey(ServerlessNDSDefaults.TRANSACTION_SIZE_LIMIT_BYTES_PARAM));
    }

    if (pExpectParams.contains(
        ServerlessNDSDefaults.TENANT_MIGRATION_BLOCKING_STATE_TIMEOUT_MS_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.TENANT_MIGRATION_BLOCKING_STATE_TIMEOUT_MS_VALUE,
          setParams.get(ServerlessNDSDefaults.TENANT_MIGRATION_BLOCKING_STATE_TIMEOUT_MS_PARAM));
    } else {
      assertFalse(
          setParams.containsKey(
              ServerlessNDSDefaults.TENANT_MIGRATION_BLOCKING_STATE_TIMEOUT_MS_PARAM));
    }

    if (pExpectParams.contains(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_MINIMUM_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_MINIMUM_VALUE,
          setParams.get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_MINIMUM_PARAM));
    } else {
      assertFalse(
          setParams.containsKey(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_MINIMUM_PARAM));
    }

    if (pExpectParams.contains(
        ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_IDLE_TIME_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_IDLE_TIME_VALUE,
          setParams.get(ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_IDLE_TIME_PARAM));
    } else {
      assertFalse(
          setParams.containsKey(
              ServerlessNDSDefaults.WIRED_TIGER_FILE_HANDLE_CLOSE_IDLE_TIME_PARAM));
    }

    if (pExpectParams.contains(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_MAX_RETRIES_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_MAX_RETRIES_VALUE,
          setParams.get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_MAX_RETRIES_PARAM));
    } else {
      assertFalse(
          setParams.containsKey(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_MAX_RETRIES_PARAM));
    }

    if (pExpectParams.contains(
        ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_BACKOFF_BASE_MS_PARAM)) {
      assertEquals(
          ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_BACKOFF_BASE_MS_VALUE,
          setParams.get(ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_BACKOFF_BASE_MS_PARAM));
    } else {
      assertFalse(
          setParams.containsKey(
              ServerlessNDSDefaults.TEMPORARILY_UNAVAILABLE_BACKOFF_BASE_MS_PARAM));
    }
  }

  @Test
  public void
      testSetSetParameterMapForMongoDProcess_InternalLookupStageIntermediateDocumentMaxSizeBytes() {
    Stream.of("3.4.0", "3.6.0", "4.0.0", "4.2.0")
        .map(Version::fromString)
        .forEach(
            v -> {
              testSetSetParameterMapForMongoDProcess_InternalLookupStageIntermediateDocumentMaxSizeBytes(
                  v, true);
              testSetSetParameterMapForMongoDProcess_InternalLookupStageIntermediateDocumentMaxSizeBytes(
                  v, false);
            });
  }

  // when serverless is removed, this test can be removed.
  @Test
  public void testSetSetParameterMapForMongoDProcess_searchIndexManagementPortServerlessHolder() {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    args.setSetParameterMap(new HashMap<>());

    // SERVERLESS MTM
    {
      final ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails detailsNonNVMe =
          ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .isServerlessMTMHolder(true)
              .mongoDBVersion(VersionUtils.EIGHT_ZERO_ZERO)
              .clusterDescription(getMockedClusterDescription())
              .mtmClusterTypes(List.of(MTMClusterType.SERVERLESS))
              .searchMongoArgs(new SearchMongoArgs())
              .build();

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(detailsNonNVMe);
      assertFalse(args.getSetParameterMap().containsKey("searchIndexManagementHostAndPort"));
    }

    args.getSetParameterMap().clear();

    // SERVERLESS MTM migrating to FLEX
    {
      final ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails detailsNonNVMe =
          ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .isServerlessMTMHolder(true)
              .mongoDBVersion(VersionUtils.EIGHT_ZERO_ZERO)
              .clusterDescription(getMockedClusterDescription())
              .mtmClusterTypes(List.of(MTMClusterType.SERVERLESS, MTMClusterType.FLEX))
              .searchMongoArgs(new SearchMongoArgs())
              .build();

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(detailsNonNVMe);
      assertFalse(args.getSetParameterMap().containsKey("searchIndexManagementHostAndPort"));
    }

    args.getSetParameterMap().clear();

    // SERVERLESS MTM migrated to FLEX
    {
      final ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails detailsNonNVMe =
          ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .isServerlessMTMHolder(true)
              .mongoDBVersion(VersionUtils.EIGHT_ZERO_ZERO)
              .clusterDescription(getMockedClusterDescription())
              .mtmClusterTypes(List.of(MTMClusterType.FLEX))
              .searchMongoArgs(new SearchMongoArgs())
              .build();

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(detailsNonNVMe);
      assertTrue(args.getSetParameterMap().containsKey("searchIndexManagementHostAndPort"));
    }
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess_fileCopyBasedInitialSyncParams() {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    args.setSetParameterMap(new HashMap<>());

    final ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails detailsNonNVMe =
        ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails.builder()
            .args(args)
            .mongoDBVersion(VersionUtils.SIX_ZERO_ZERO)
            .clusterDescription(getMockedClusterDescription())
            .mtmClusterTypes(Collections.emptyList())
            .searchMongoArgs(new SearchMongoArgs())
            .build();

    // 6.0 non NVMe clusters should not get File Copy Based Initial Sync params
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(detailsNonNVMe);

    // Pre 6.0.4 NVMe clusters should not get File Copy Based Initial Sync params
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    final ClusterDescription clusterDescription = getMockedClusterDescription();
    doReturn(true).when(clusterDescription).isMTM();
    doReturn(true).when(clusterDescription).isFullyNVMe();
    final ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails detailsNVMe =
        ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails.builder()
            .defaultStartupFCV(null)
            .clusterDescription(clusterDescription)
            .mtmClusterTypes(Collections.emptyList())
            .searchMongoArgs(new SearchMongoArgs())
            .build();

    // 6.0.4 NVMe clusters should get File Copy Based Initial Sync params
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        detailsNVMe.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_ZERO_FOUR).build());
    assertEquals(
        CommonStartupSetparams.InitialSyncSource.VALUE_SECONDARY_PREFERRED,
        args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertEquals(
        CommonStartupSetparams.InitialSyncMethod.VALUE_FILE_COPY_BASED,
        args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    // 6.1 NVMe clusters should not get FCBIS params
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        detailsNVMe.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_ONE_ZERO).build());

    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    // 6.2+ NVMe clusters should get File Copy Based Initial Sync params
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        detailsNVMe.toBuilder().args(args).mongoDBVersion(VersionUtils.SIX_TWO_ZERO).build());
    assertEquals(
        CommonStartupSetparams.InitialSyncSource.VALUE_SECONDARY_PREFERRED,
        args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertEquals(
        CommonStartupSetparams.InitialSyncMethod.VALUE_FILE_COPY_BASED,
        args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    // 7.0 NVMe clusters should get File Copy Based Initial Sync params
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        detailsNVMe.toBuilder()
            .args(args)
            .mongoDBVersion(VersionUtils.SEVEN_ZERO_ZERO_RC1)
            .build());
    assertEquals(
        CommonStartupSetparams.InitialSyncSource.VALUE_SECONDARY_PREFERRED,
        args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertEquals(
        CommonStartupSetparams.InitialSyncMethod.VALUE_FILE_COPY_BASED,
        args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    // Clusters with EAR enabled should not get File Copy Based Initial Sync params (7.0 NVMe
    // cluster)
    args.getSetParameterMap().clear();
    doReturn(true).when(clusterDescription).isEncryptionAtRestEnabled();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        detailsNVMe.toBuilder()
            .args(args)
            .mongoDBVersion(VersionUtils.SEVEN_ZERO_ZERO_RC1)
            .build());
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));
  }

  private void
      testSetSetParameterMapForMongoDProcess_InternalLookupStageIntermediateDocumentMaxSizeBytes(
          final Version pMongoDBVersion, final boolean pIsServerlessMTMCluster) {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    final ClusterDescription clusterDescription = getMockedClusterDescription();
    doReturn(pIsServerlessMTMCluster).when(clusterDescription).isMTM();
    final SetParameterMapForMongoDProcessDetails details =
        SetParameterMapForMongoDProcessDetails.builder()
            .args(args)
            .clusterDescription(clusterDescription)
            .mongoDBVersion(pMongoDBVersion)
            .isServerlessMTMHolder(pIsServerlessMTMCluster)
            .mtmClusterTypes(
                pIsServerlessMTMCluster
                    ? List.of(MTMClusterType.SERVERLESS)
                    : Collections.emptyList())
            .searchMongoArgs(new SearchMongoArgs())
            .build();

    // Check for the internalLookupStageIntermediateDocumentMaxSizeBytes parameter when it has not
    // been set by the admin / user
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).build());
    if (pIsServerlessMTMCluster
        && pMongoDBVersion.isGreaterThanOrEqualTo(VersionUtils.FOUR_TWO_ZERO)) {
      assertEquals(
          String.valueOf(
              NDSDefaults.MTM_INTERNAL_LOOKUP_STAGE_INTERMEDIATE_DOCUMENT_MAX_SIZE_BYTES),
          args.getSetParameterMap().get("internalLookupStageIntermediateDocumentMaxSizeBytes"));
    } else {
      assertFalse(
          args.getSetParameterMap()
              .containsKey("internalLookupStageIntermediateDocumentMaxSizeBytes"));
    }

    // Check for the internalLookupStageIntermediateDocumentMaxSizeBytes parameter when it has
    // been set by the admin / user
    args.getSetParameterMap().put("internalLookupStageIntermediateDocumentMaxSizeBytes", 25000);
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).build());
    assertEquals(
        25000,
        args.getSetParameterMap().get("internalLookupStageIntermediateDocumentMaxSizeBytes"));
  }

  @Test
  public void
      testSetSetParameterMapForMongoDProcess_internalQueryMaxBlockingSortMemoryUsageBytes() {
    testSetSetParameterMapForMongoDProcess_internalQueryMaxBlockingSortMemoryUsageBytes(
        VersionUtils.FOUR_TWO_ZERO, true);
    testSetSetParameterMapForMongoDProcess_internalQueryMaxBlockingSortMemoryUsageBytes(
        VersionUtils.FOUR_TWO_ZERO, false);
    testSetSetParameterMapForMongoDProcess_internalQueryMaxBlockingSortMemoryUsageBytes(
        VersionUtils.FOUR_FOUR_ZERO, true);
    testSetSetParameterMapForMongoDProcess_internalQueryMaxBlockingSortMemoryUsageBytes(
        VersionUtils.FOUR_FOUR_ZERO, false);
  }

  private void testSetSetParameterMapForMongoDProcess_internalQueryMaxBlockingSortMemoryUsageBytes(
      final Version pMongoDBVersion, final boolean pIsServerlessMTMCluster) {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    final ClusterDescription clusterDescription = getMockedClusterDescription();
    doReturn(pIsServerlessMTMCluster).when(clusterDescription).isMTM();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        SetParameterMapForMongoDProcessDetails.builder()
            .args(args)
            .clusterDescription(getMockedClusterDescription())
            .mongoDBVersion(pMongoDBVersion)
            .clusterDescription(clusterDescription)
            .isServerlessMTMHolder(pIsServerlessMTMCluster)
            .mtmClusterTypes(
                pIsServerlessMTMCluster
                    ? List.of(MTMClusterType.SERVERLESS)
                    : Collections.emptyList())
            .searchMongoArgs(new SearchMongoArgs())
            .build());
    if (pIsServerlessMTMCluster
        && pMongoDBVersion.isGreaterThanOrEqualTo(VersionUtils.FOUR_THREE_ZERO)) {
      assertEquals(
          String.valueOf(NDSDefaults.MTM_INTERNAL_QUERY_MAX_BLOCKING_SORT_MEMORY_USAGE_BYTES),
          args.getSetParameterMap().get("internalQueryMaxBlockingSortMemoryUsageBytes"));
    } else {
      assertFalse(
          args.getSetParameterMap().containsKey("internalQueryMaxBlockingSortMemoryUsageBytes"));
    }
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess_tlsX509ExpirationWarningThresholdDays() {
    testSetSetParameterMapForMongoDProcess_tlsX509ExpirationWarningThresholdDays(
        VersionUtils.FOUR_TWO_ZERO, true);
    testSetSetParameterMapForMongoDProcess_tlsX509ExpirationWarningThresholdDays(
        VersionUtils.FOUR_TWO_ZERO, false);
    testSetSetParameterMapForMongoDProcess_tlsX509ExpirationWarningThresholdDays(
        VersionUtils.parse("4.3.1"), true);
    testSetSetParameterMapForMongoDProcess_tlsX509ExpirationWarningThresholdDays(
        VersionUtils.parse("4.3.1"), false);
  }

  private void testSetSetParameterMapForMongoDProcess_tlsX509ExpirationWarningThresholdDays(
      final Version pMongoDBVersion, final boolean pIsServerlessMTMCluster) {
    final ProcessArguments2_6 args = new ProcessArguments2_6();

    final ClusterDescription clusterDescription = getMockedClusterDescription();
    doReturn(pIsServerlessMTMCluster).when(clusterDescription).isMTM();

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        SetParameterMapForMongoDProcessDetails.builder()
            .args(args)
            .clusterDescription(clusterDescription)
            .mtmClusterTypes(
                pIsServerlessMTMCluster
                    ? List.of(MTMClusterType.SERVERLESS)
                    : Collections.emptyList())
            .mongoDBVersion(pMongoDBVersion)
            .isServerlessMTMHolder(pIsServerlessMTMCluster)
            .searchMongoArgs(new SearchMongoArgs())
            .build());
    if (pIsServerlessMTMCluster
        && pMongoDBVersion.isGreaterThanOrEqualTo(VersionUtils.FOUR_THREE_ZERO)) {
      assertEquals(
          String.valueOf(NDSDefaults.MTM_TLS_X509_EXPIRATION_WARNING_THRESHOLD_DAYS),
          args.getSetParameterMap().get("tlsX509ExpirationWarningThresholdDays"));
    } else {
      assertFalse(args.getSetParameterMap().containsKey("tlsX509ExpirationWarningThresholdDays"));
    }
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit() {
    // 1. Cluster is not an MTM: 'internalPipelineLengthLimit' is not set
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        false, VersionUtils.FIVE_ONE_ZERO, false, false);

    // 2. Cluster is a 4.2 shared MTM: 'internalPipelineLengthLimit' is only set if version is
    // 4.2.16 or above
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        false, VersionUtils.FOUR_TWO_ZERO, true, false);
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        true, Version.fromString("4.2.16"), true, false);
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        true, Version.fromString("4.2.20"), true, false);

    // 3. Cluster is a 4.4 shared MTM: 'internalPipelineLengthLimit' is only set if version is 4.4.3
    // or above
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        false, VersionUtils.FOUR_FOUR_ZERO, true, false);
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        true, Version.fromString("4.4.3"), true, false);
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        true, Version.fromString("4.4.10"), true, false);

    // 4. Cluster is a 4.9+ shared MTM: 'internalPipelineLengthLimit' is set
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        true, VersionUtils.FOUR_NINE_ZERO, true, false);
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        true, VersionUtils.FIVE_ZERO_ZERO, true, false);
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        true, VersionUtils.FIVE_ONE_ZERO, true, false);

    // 5. Cluster is a 4.9+ serverless MTM: 'internalPipelineLengthLimit' is set
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        true, VersionUtils.FOUR_NINE_ZERO, true, true);
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        true, VersionUtils.FIVE_ZERO_ZERO, true, true);
    testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
        true, VersionUtils.FIVE_ONE_ZERO, true, true);
  }

  private void testSetSetParameterMapForMongoDProcess_internalPipelineLengthLimit(
      final boolean pShouldSetInternalPipelineLengthLimit,
      final Version pMongoDBVersion,
      final boolean pIsMTMProcess,
      final boolean pIsServerlessMTMCluster) {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    final ClusterDescription clusterDescription = getMockedClusterDescription();
    doReturn(pIsMTMProcess).when(clusterDescription).isMTM();

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        SetParameterMapForMongoDProcessDetails.builder()
            .args(args)
            .clusterDescription(clusterDescription)
            .mtmClusterTypes(
                pIsServerlessMTMCluster
                    ? List.of(MTMClusterType.SERVERLESS)
                    : Collections.emptyList())
            .mongoDBVersion(pMongoDBVersion)
            .isServerlessMTMHolder(pIsServerlessMTMCluster)
            .searchMongoArgs(new SearchMongoArgs())
            .build());
    if (pShouldSetInternalPipelineLengthLimit) {
      assertEquals(
          NDSDefaults.MTM_INTERNAL_PIPELINE_LENGTH_LIMIT,
          args.getSetParameterMap().get(NDSDefaults.MTM_INTERNAL_PIPELINE_LENGTH_LIMIT_PARAM));
    } else {
      assertNull(
          args.getSetParameterMap().get(NDSDefaults.MTM_INTERNAL_PIPELINE_LENGTH_LIMIT_PARAM));
    }
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors() {
    // 1. Cluster is not an MTM: 'enableTimeoutOfInactiveSessionCursors' is not set
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        false, VersionUtils.FIVE_ONE_ZERO, false, false);

    // 2. Cluster is a serverless MTM: 'enableTimeoutOfInactiveSessionCursors' is not set
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        false, Version.fromString("4.4.9"), true, true);
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        false, Version.fromString("5.0.3"), true, true);
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        false, VersionUtils.FIVE_ONE_ZERO, true, true);

    // 3. Cluster is a 4.4 shared MTM: 'enableTimeoutOfInactiveSessionCursors' is only set if
    // version is
    // 4.4.9 or above
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        false, VersionUtils.FOUR_FOUR_ZERO, true, false);
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        true, Version.fromString("4.4.9"), true, false);
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        true, Version.fromString("4.4.20"), true, false);

    // 4. Cluster is a 5.0 shared MTM: 'enableTimeoutOfInactiveSessionCursors' is only set if
    // version is 5.0.3
    // or above
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        false, VersionUtils.FIVE_ZERO_ZERO, true, false);
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        true, Version.fromString("5.0.3"), true, false);
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        true, Version.fromString("5.0.5"), true, false);

    // 4. Cluster is a 5.1+ shared MTM: 'enableTimeoutOfInactiveSessionCursors' is set
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        true, VersionUtils.FIVE_ONE_ZERO, true, false);
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        true, Version.fromString("5.1.3"), true, false);
    testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
        true, Version.fromString("5.1.5"), true, false);
  }

  private void testSetSetParameterMapForMongoDProcess_enableTimeoutOfInactiveSessionCursors(
      final boolean pShouldSetEnableTimeoutOfInactiveSessionCursors,
      final Version pMongoDBVersion,
      final boolean pIsMTMProcess,
      final boolean pIsServerlessMTMCluster) {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    final ClusterDescription clusterDescription = getMockedClusterDescription();
    doReturn(pIsMTMProcess).when(clusterDescription).isMTM();

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        SetParameterMapForMongoDProcessDetails.builder()
            .args(args)
            .clusterDescription(clusterDescription)
            .mtmClusterTypes(
                pIsServerlessMTMCluster
                    ? List.of(MTMClusterType.SERVERLESS)
                    : Collections.emptyList())
            .mongoDBVersion(pMongoDBVersion)
            .isServerlessMTMHolder(pIsServerlessMTMCluster)
            .searchMongoArgs(new SearchMongoArgs())
            .build());
    if (pShouldSetEnableTimeoutOfInactiveSessionCursors) {
      assertTrue((Boolean) args.getSetParameterMap().get("enableTimeoutOfInactiveSessionCursors"));
    } else {
      assertNull(args.getSetParameterMap().get("enableTimeoutOfInactiveSessionCursors"));
    }
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess_chunkMigrationConcurrency() {
    // INVALID - not supported for version
    {
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      final HardwareSpec hardwareSpec = mock(HardwareSpec.class);
      final NDSInstanceSize instanceSize = mock(NDSInstanceSize.class);
      doReturn(Optional.of(hardwareSpec))
          .when(clusterDescription)
          .getSmallestShardHardwareByInstanceSize(NodeType.ELECTABLE);
      doReturn(instanceSize).when(hardwareSpec).getInstanceSize();
      doReturn(Double.MAX_VALUE).when(instanceSize).getNumCPUs(any());
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setChunkMigrationConcurrency(4);
      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.MIN_CHUNK_MIGRATION_CONCURRENCY_FIVE_VERSION)
              .searchMongoArgs(new SearchMongoArgs())
              .build());
      assertFalse(args.getSetParameterMap().containsKey("chunkMigrationConcurrency"));
    }

    // INVALID - downgrade to instance size with insufficient 4 => max = 4/2 = 2
    {
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      final HardwareSpec hardwareSpec = mock(HardwareSpec.class);
      final NDSInstanceSize instanceSize = mock(NDSInstanceSize.class);
      doReturn(ClusterType.SHARDED).when(clusterDescription).getClusterType();
      doReturn(Optional.of(hardwareSpec))
          .when(clusterDescription)
          .getSmallestShardHardwareByInstanceSize(NodeType.ELECTABLE);
      doReturn(instanceSize).when(hardwareSpec).getInstanceSize();
      doReturn(4d).when(instanceSize).getNumCPUs(any());

      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setChunkMigrationConcurrency(4);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.SIX_ZERO_SIX)
              .searchMongoArgs(new SearchMongoArgs())
              .build());
      assertFalse(args.getSetParameterMap().containsKey("chunkMigrationConcurrency"));
    }

    // INVALID - incorrect cluster type
    {
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      final HardwareSpec hardwareSpec = mock(HardwareSpec.class);
      final NDSInstanceSize instanceSize = mock(NDSInstanceSize.class);
      doReturn(ClusterType.REPLICASET).when(clusterDescription).getClusterType();
      doReturn(Optional.of(hardwareSpec))
          .when(clusterDescription)
          .getSmallestShardHardwareByInstanceSize(NodeType.ELECTABLE);
      doReturn(instanceSize).when(hardwareSpec).getInstanceSize();
      doReturn(Double.MAX_VALUE).when(instanceSize).getNumCPUs(any());

      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setChunkMigrationConcurrency(4);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.SIX_ZERO_SIX)
              .searchMongoArgs(new SearchMongoArgs())
              .build());
      assertFalse(args.getSetParameterMap().containsKey("chunkMigrationConcurrency"));
    }

    // VALID -version and cluster description ok
    {
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      final HardwareSpec hardwareSpec = mock(HardwareSpec.class);
      final NDSInstanceSize instanceSize = mock(NDSInstanceSize.class);
      doReturn(ClusterType.SHARDED).when(clusterDescription).getClusterType();
      doReturn(Optional.of(hardwareSpec))
          .when(clusterDescription)
          .getSmallestShardHardwareByInstanceSize(NodeType.ELECTABLE);
      doReturn(instanceSize).when(hardwareSpec).getInstanceSize();
      doReturn(8D).when(instanceSize).getNumCPUs(any());

      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setChunkMigrationConcurrency(4);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.SIX_ZERO_SIX)
              .searchMongoArgs(new SearchMongoArgs())
              .build());
      assertEquals(4, args.getSetParameterMap().get("chunkMigrationConcurrency"));
    }
  }

  @Test
  public void testSetDisaggregatedStorageParameters() {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    args.setSetParameterMap(new HashMap<>());
    args.getSetParameterMap().put(FieldDefs.SEARCH_TLS_MODE, "disabled");
    args.setProxyProtocolPort(27200);
    final ObjectId replicationSpecId = new ObjectId();
    final ClusterDescription clusterDescription = getMockedClusterDescription();
    final ReplicaSetHardware replicaSetHardware = mock(ReplicaSetHardware.class);
    doReturn(replicationSpecId).when(replicaSetHardware).getReplicationSpecId();
    final InstanceHardware instanceHardware = mock(InstanceHardware.class);
    doReturn(0).when(instanceHardware).getMemberIndex();
    doReturn(Optional.of("hostname")).when(instanceHardware).getHostnameForAgents();
    final InstanceHardware instanceHardware2 = mock(InstanceHardware.class);
    doReturn(1).when(instanceHardware2).getMemberIndex();
    doReturn(Optional.of("hostname2")).when(instanceHardware2).getHostnameForAgents();
    final Logger logger = LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class);
    // disaggregated storage config should not exist for cluster use local storage system
    ClusterDeploymentProcessUtil.setDisaggregateStorageParameters(
        null, args, clusterDescription, replicaSetHardware, instanceHardware, logger, "testPem");
    assertDisaggregatedStorgeConfig(args, null);

    doReturn(true).when(clusterDescription).isDisaggregatedStorageSystem();
    // missing replica set
    // disaggregated storage config should not exist for cluster use local storage system
    ClusterDeploymentProcessUtil.setDisaggregateStorageParameters(
        null, args, clusterDescription, replicaSetHardware, instanceHardware, logger, "testPem");
    assertDisaggregatedStorgeConfig(args, null);

    final ReplicaSet replicaSet = new ReplicaSet();
    replicaSet.setId("rs0");
    replicaSet.setVersion(1l);
    replicaSet.setMembers(new ArrayList<>());
    for (var i = 0; i < 2; i++) {
      var member = new ReplicaSetMember();
      member.setHost("host" + i);
      member.setPriority(7.0f);
      member.setId(i);
      member.setHorizons(Map.of("PUBLIC", "hostname"));
      member.setTags(Map.of());
      replicaSet.addMember(member);
    }
    // missing availability zone
    ClusterDeploymentProcessUtil.setDisaggregateStorageParameters(
        replicaSet,
        args,
        clusterDescription,
        replicaSetHardware,
        instanceHardware,
        logger,
        "testPem");
    assertDisaggregatedStorgeConfig(args, null);

    final String zoneId = "zone1";
    doReturn(Optional.of(zoneId)).when(instanceHardware).getZoneTag();
    // missing replication spec
    ClusterDeploymentProcessUtil.setDisaggregateStorageParameters(
        replicaSet,
        args,
        clusterDescription,
        replicaSetHardware,
        instanceHardware,
        logger,
        "testPem");
    assertDisaggregatedStorgeConfig(args, null);

    final ReplicationSpec replicationSpec = mock(ReplicationSpec.class);
    doReturn(Optional.of(replicationSpec))
        .when(clusterDescription)
        .getReplicationSpecById(replicationSpecId);
    // missing storage config
    doReturn(Optional.empty()).when(replicationSpec).getDisaggregatedStorageConfig();
    ClusterDeploymentProcessUtil.setDisaggregateStorageParameters(
        replicaSet,
        args,
        clusterDescription,
        replicaSetHardware,
        instanceHardware,
        logger,
        "testPem");
    assertDisaggregatedStorgeConfig(args, null);

    final long logId = 123456789L;
    doReturn(Optional.of(new DisaggregatedStorageConfig(logId, List.of(), List.of(), "")))
        .when(replicationSpec)
        .getDisaggregatedStorageConfig();
    // missing sls cells
    ClusterDeploymentProcessUtil.setDisaggregateStorageParameters(
        replicaSet,
        args,
        clusterDescription,
        replicaSetHardware,
        instanceHardware,
        logger,
        "testPem");
    assertDisaggregatedStorgeConfig(args, null);

    // Mock ReplicationSpec with region configs for testing electionTimeoutMillis and
    // chainingAllowed
    final RegionConfig regionConfig = mock(RegionConfig.class);
    final HardwareSpec electableSpecs = mock(HardwareSpec.class);
    final NDSInstanceSize instanceSize = mock(NDSInstanceSize.class);

    // Test M10 instance size (should get 10000ms timeout)
    doReturn(Optional.of(10000)).when(instanceSize).getElectionTimeoutMillis();
    doReturn(instanceSize).when(electableSpecs).getInstanceSize();
    doReturn(electableSpecs).when(regionConfig).getElectableSpecs();
    doReturn(regionConfig).when(replicationSpec).getHighestPriorityRegionConfig();

    // Test single region, 3 node setup (should disable chaining)
    doReturn(List.of(regionConfig)).when(replicationSpec).getRegions();
    doReturn(3).when(replicationSpec).getTotalElectableNodes();

    var storageConfig =
        new DisaggregatedStorageConfig(
            logId,
            List.of(
                new DisaggregatedStorageConfig.SlsCell(
                    new ObjectId(), "cell1", zoneId, "metadata.server", "scheduler.server")),
            List.of("logServer"),
            "encryption.key");
    doReturn(Optional.of(storageConfig)).when(replicationSpec).getDisaggregatedStorageConfig();
    doReturn(Stream.of(instanceHardware, instanceHardware2))
        .when(replicaSetHardware)
        .getAllHardware();
    ClusterDeploymentProcessUtil.setDisaggregateStorageParameters(
        replicaSet,
        args,
        clusterDescription,
        replicaSetHardware,
        instanceHardware,
        logger,
        "testPem");

    assertDisaggregatedStorgeConfig(args, storageConfig.toSetParameters());

    // Verify the disaggregated storage config contains replica set settings
    var config =
        Document.parse(args.getSetParameterMap().get("disaggregatedStorageConfig").toString());
    Document replSetConfig = (Document) config.get("replSetConfig");
    assertTrue(replSetConfig.containsKey("settings"));

    Document settings = (Document) replSetConfig.get("settings");
    assertEquals(10000, settings.get("electionTimeoutMillis"));
    assertEquals(
        false, settings.get("chainingAllowed")); // Single region, 3 nodes = chaining disabled

    // Test case 2: Default electionTimeoutMillis (5000ms) and multi-region chaining allowed
    final NDSInstanceSize instanceSize2 = mock(NDSInstanceSize.class);
    doReturn(Optional.of(5000)).when(instanceSize2).getElectionTimeoutMillis();
    doReturn(instanceSize2).when(electableSpecs).getInstanceSize();

    // Test multi-region setup (should enable chaining)
    final RegionConfig regionConfig2 = mock(RegionConfig.class);
    doReturn(List.of(regionConfig, regionConfig2)).when(replicationSpec).getRegions();
    doReturn(6).when(replicationSpec).getTotalElectableNodes();

    // Reset args for second test
    args.setSetParameterMap(new HashMap<>());

    // Reset the stream for the second test call
    doReturn(Stream.of(instanceHardware, instanceHardware2))
        .when(replicaSetHardware)
        .getAllHardware();

    ClusterDeploymentProcessUtil.setDisaggregateStorageParameters(
        replicaSet,
        args,
        clusterDescription,
        replicaSetHardware,
        instanceHardware,
        logger,
        "testPem");

    // Verify second test case
    var config2 =
        Document.parse(args.getSetParameterMap().get("disaggregatedStorageConfig").toString());
    Document replSetConfig2 = (Document) config2.get("replSetConfig");
    Document settings2 = (Document) replSetConfig2.get("settings");
    assertEquals(5000, settings2.get("electionTimeoutMillis"));
    assertEquals(true, settings2.get("chainingAllowed")); // Multi-region = chaining enabled
  }

  void assertDisaggregatedStorgeConfig(
      final ProcessArguments2_6 pArguments26, final Document pStorageConfig) {
    final boolean exist = pStorageConfig != null;
    assertEquals(
        exist, pArguments26.getSetParameterMap().containsKey("disaggregatedStorageConfig"));
    assertEquals(
        exist, pArguments26.getSetParameterMap().containsKey("disaggregatedStorageEnabled"));
    assertEquals(
        exist, pArguments26.getSetParameterMap().containsKey("diagnosticDataCollectionEnabled"));
    assertEquals(
        exist, pArguments26.getSetParameterMap().containsKey("disableLogicalSessionCacheRefresh"));
    assertEquals(
        pArguments26
            .getSetParameterMap()
            .containsKey("queryAnalysisSamplerConfigurationRefreshSecs"),
        exist);
    if (exist) {
      var config =
          Document.parse(
              pArguments26.getSetParameterMap().get("disaggregatedStorageConfig").toString());
      assertEquals(123456789L, config.get("logID"));
      assertEquals("zone1", config.get("myZoneName"));
      assertTrue(config.containsKey("encryptionKeyFilePath"));
      assertTrue(config.containsKey("replSetConfig"));
      assertEquals(
          INDSDefaults.DISAGGREGATED_STORAGE_TRUSTED_CAS_PATH,
          ((Document) config.get("tlsSettings")).get("trustedCAsPath"));
      assertNull(pArguments26.getSetParameterMap().get(FieldDefs.SEARCH_TLS_MODE));
      assertNull(pArguments26.getProxyProtocolPort());
      assertNull(pArguments26.getIngressConnectionEstablishmentRateLimiterEnabled());
      assertNull(pArguments26.getIngressConnectionEstablishmentRatePerSec());
      assertNull(pArguments26.getIngressConnectionEstablishmentMaxQueueDepth());
      assertNull(pArguments26.getIngressConnectionEstablishmentBurstCapacitySecs());
      assertNull(pArguments26.getIngressConnectionEstablishmentRateLimiterBypass());
      assertNull(pArguments26.getPessimisticConnectivityCheckForAcceptedConnections());
    } else {
      assertNull(pArguments26.getSetParameterMap().get("disaggregatedStorageConfig"));
    }
  }

  @Test
  public void testGetDeploymentMongoDProcesses_MaxIncomingConnectionsOverride() {
    testGetDeploymentMongoDProcesses_MaxIncomingConnectionsOverride("3.4.0");
    testGetDeploymentMongoDProcesses_MaxIncomingConnectionsOverride("3.6.0");
    testGetDeploymentMongoDProcesses_MaxIncomingConnectionsOverride("4.0.0");
  }

  private void testGetDeploymentMongoDProcesses_MaxIncomingConnectionsOverride(
      final String pMongoDBVersion) {

    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final DedicatedCloudProviderContainer container = mock(DedicatedCloudProviderContainer.class);
    doReturn(containerId).when(container).getId();
    doReturn(Optional.of(container))
        .when(ndsGroup)
        .getDedicatedCloudProviderContainer(eq(containerId));
    final List<String> defaultOverrides = List.of("127.0.0.1/32");
    final String containerCIDR = "*******/21";
    final List<String> expectedOverrides =
        Stream.concat(defaultOverrides.stream(), Stream.of(containerCIDR))
            .collect(Collectors.toList());
    doReturn(containerCIDR).when(container).getAtlasCidr();

    final AutomationConfig config = new AutomationConfig();

    final ClusterDescription description = getTestReplicaSet(TEST_CLUSTER_NAME, pMongoDBVersion);
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, description));

    final ShardedClusterDescription shardedClusterDescription =
        getTestShardedCluster(TEST_CLUSTER_NAME, pMongoDBVersion, ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription));

    final Consumer<Process> assertMaxIncomingConnectionsOverride =
        (process) -> {
          Stream.of(process)
              .filter(
                  p ->
                      VersionUtils.parse(p.getVersion())
                          .isGreaterThanOrEqualTo(VersionUtils.THREE_SIX_ZERO))
              .map(Process::getMaxIncomingConnectionsOverride)
              .collect(Collectors.toList())
              .forEach(actual -> assertEquals(expectedOverrides, actual));

          Optional.of(process)
              .filter(
                  p -> VersionUtils.parse(p.getVersion()).isLessThan(VersionUtils.THREE_SIX_ZERO))
              .ifPresent(p -> assertNull(p.getMaxIncomingConnectionsOverride()));
        };

    final Process mongodProcess = new Process();
    mongodProcess.setVersion(
        NDSDefaults.toEnterpriseVersion(description.getMongoDBVersion().getVersion()));
    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        description,
        Collections.emptyList(),
        null,
        hardware,
        hardware.getHardware().get(0),
        mongodProcess,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertMaxIncomingConnectionsOverride.accept(mongodProcess);

    final Process mongosProcess = new Process();
    mongosProcess.setVersion(
        NDSDefaults.toEnterpriseVersion(
            shardedClusterDescription.getMongoDBVersion().getVersion()));
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        mongosProcess,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertMaxIncomingConnectionsOverride.accept(mongosProcess);
  }

  /** Tests if several processes arg parameters can be overridden */
  @Test
  public void testUpdateInstanceMongoDProcess_adminOverrides() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_44 =
        getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.FOUR_FOUR_ZERO.getVersion());
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_44));

    // no existing override
    {
      final Process process = new Process();
      process.setName("process");
      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_44,
          Collections.emptyList(),
          null,
          hardware_360,
          hardware_360.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals(
          true, process.getArgs2_6().getSetParameterMap().get("allowRolesFromX509Certificates"));
      assertTrue(process.getArgs2_6().getSetParameterMap().containsKey("tlsCATrusts"));
      assertEquals(
          true, process.getArgs2_6().getSetParameterMap().get("tlsWithholdClientCertificate"));
      assertEquals(true, process.getArgs2_6().getSetParameterMap().get("ttlMonitorEnabled"));
    }

    // existing overrides
    {
      final Process process = new Process();
      process.setName("process");
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      process.setArgs2_6(args);
      final Map<String, Object> setParameterMap = new HashMap<>();
      args.setSetParameterMap(setParameterMap);
      final double wiredTigerEngineCacheSizeGBOverride = 2.75;
      args.setWiredTigerEngineCacheSizeGB(wiredTigerEngineCacheSizeGBOverride);
      setParameterMap.put("allowRolesFromX509Certificates", false);
      setParameterMap.put("tlsCATrusts", "overriddenTLSCATrusts");
      setParameterMap.put("tlsWithholdClientCertificate", false);
      setParameterMap.put("ttlMonitorEnabled", false);
      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_44,
          Collections.emptyList(),
          null,
          hardware_360,
          hardware_360.getHardware().get(0),
          process,
          args,
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          true,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());
      assertEquals(
          wiredTigerEngineCacheSizeGBOverride,
          process.getArgs2_6().getWiredTigerEngineCacheSizeGB());
      assertEquals(
          false, process.getArgs2_6().getSetParameterMap().get("allowRolesFromX509Certificates"));
      assertEquals(
          "overriddenTLSCATrusts", process.getArgs2_6().getSetParameterMap().get("tlsCATrusts"));
      assertEquals(
          false, process.getArgs2_6().getSetParameterMap().get("tlsWithholdClientCertificate"));
      assertEquals(false, process.getArgs2_6().getSetParameterMap().get("ttlMonitorEnabled"));
    }
  }

  @Test
  public void testUpdateInstanceMongoDProcess_proxyProtocolPort() {
    testUpdateInstanceMongoDProcess_proxyProtocolPort(false, VersionUtils.EIGHT_ZERO_ZERO, null);
    testUpdateInstanceMongoDProcess_proxyProtocolPort(
        false, VersionUtils.EIGHT_ZERO_ZERO, VersionUtils.EIGHT_ZERO_ZERO.toString());
    testUpdateInstanceMongoDProcess_proxyProtocolPort(
        true, VersionUtils.EIGHT_ZERO_TEN, VersionUtils.EIGHT_ZERO_TEN.toString());
  }

  private void testUpdateInstanceMongoDProcess_proxyProtocolPort(
      final boolean pResultShouldHaveProxyPort, final Version pMongoDBVersion, final String pFCV) {

    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    ClusterDescription testReplicaSet =
        getTestReplicaSet(TEST_CLUSTER_NAME, pMongoDBVersion.getVersion());

    ClusterDescription testShardedCluster =
        getTestShardedCluster("sharded", pMongoDBVersion.getVersion(), ClusterType.SHARDED, 1);

    if (pFCV != null) {
      testReplicaSet =
          testReplicaSet
              .copy()
              .setFixedFeatureCompatibilityVersion(Optional.of(new FixedVersion(pFCV)))
              .build();

      testShardedCluster =
          testShardedCluster
              .copy()
              .setFixedFeatureCompatibilityVersion(Optional.of(new FixedVersion(pFCV)))
              .build();
    }
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testReplicaSet));

    final Process process = new Process();
    process.setName("process");

    final ProcessArguments2_6 args = new ProcessArguments2_6();
    if (!pResultShouldHaveProxyPort) { // ensure that result removes existing proxy port
      args.setProxyProtocolPort(27020);
    }

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testReplicaSet,
        Collections.emptyList(),
        null,
        hardware,
        hardware.getHardware().get(0),
        process,
        args,
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    if (pResultShouldHaveProxyPort) {
      assertEquals(
          NDSDefaults.MONGOD_PROXY_PROTOCOL_INGRESS_PORT,
          process.getArgs2_6().getProxyProtocolPort());
    } else {
      assertNull(process.getArgs2_6().getProxyProtocolPort());
    }

    final ReplicaSetHardware shardedHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testShardedCluster));

    final Process shardedClusterProcess = new Process();
    process.setName("process");

    final ProcessArguments2_6 shardedClusterArgs = new ProcessArguments2_6();

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testShardedCluster,
        Collections.emptyList(),
        null,
        shardedHardware,
        shardedHardware.getHardware().get(0),
        shardedClusterProcess,
        shardedClusterArgs,
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    // sharded cluster mongod processes should never be updated
    assertNull(shardedClusterProcess.getArgs2_6().getProxyProtocolPort());
  }

  @Test
  public void testSetSetParameterMapForMongoSProcess() {
    final ProcessArguments2_6 args = new ProcessArguments2_6();

    // Do nothing for 3.6
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("3.6.0"), false, false, new SearchMongoArgs());
    assertEquals(
        "/tmp/diagnostic.data",
        args.getSetParameterMap().get("diagnosticDataCollectionDirectoryPath"));
    assertFalse(args.getSetParameterMap().containsKey("ShardingTaskExecutorPoolMaxConnecting"));

    // Set the parameter for 3.2 if it doesn't exist
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("3.2.0"), false, false, new SearchMongoArgs());
    assertFalse(args.getSetParameterMap().containsKey("diagnosticDataCollectionDirectoryPath"));
    assertEquals(2, args.getSetParameterMap().get("ShardingTaskExecutorPoolMaxConnecting"));

    // Set the parameter for 3.4 if it doesn't exist
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("3.4.0"), false, false, new SearchMongoArgs());
    assertFalse(args.getSetParameterMap().containsKey("diagnosticDataCollectionDirectoryPath"));
    assertEquals(2, args.getSetParameterMap().get("ShardingTaskExecutorPoolMaxConnecting"));

    // Preserve the old setting if it's already set
    args.getSetParameterMap().clear();
    args.getSetParameterMap().put("ShardingTaskExecutorPoolMaxConnecting", 10);
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("3.4.0"), false, false, new SearchMongoArgs());
    assertEquals(10, args.getSetParameterMap().get("ShardingTaskExecutorPoolMaxConnecting"));

    // No support for suppressNoTLSPeerCertificateWarning
    assertNull(args.getSetParameterMap().get("suppressNoTLSPeerCertificateWarning"));

    // 4.0 support for suppressNoTLSPeerCertificateWarning
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("4.0.1"), false, false, new SearchMongoArgs());
    assertTrue((Boolean) args.getSetParameterMap().get("suppressNoTLSPeerCertificateWarning"));

    // Pre 6.0 NVMe clusters should not get File Copy Based Initial Sync params
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args,
        "/tmp",
        Version.fromString("5.2.0"),
        true,
        false,
        new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.TCP));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    // 6.0 non NVMe clusters should not get File Copy Based Initial Sync params
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("6.0.0"), true, false, new SearchMongoArgs());
    // Pre 6.0.4 NVMe clusters should not get File Copy Based Initial Sync params
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    // 6.0.4 NVMe clusters should get File Copy Based Initial Sync params
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("6.0.4"), true, false, new SearchMongoArgs());
    assertEquals(
        CommonStartupSetparams.InitialSyncSource.VALUE_SECONDARY_PREFERRED,
        args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertEquals(
        CommonStartupSetparams.InitialSyncMethod.VALUE_FILE_COPY_BASED,
        args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    // 6.1 NVMe clusters should not get FCBIS params
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("6.1.0"), true, false, new SearchMongoArgs());
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    // 6.2 NVMe clusters should get File Copy Based Initial Sync params
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("6.2.0"), true, false, new SearchMongoArgs());
    assertEquals(
        CommonStartupSetparams.InitialSyncSource.VALUE_SECONDARY_PREFERRED,
        args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertEquals(
        CommonStartupSetparams.InitialSyncMethod.VALUE_FILE_COPY_BASED,
        args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    // Clusters with encryption at rest enabled do not get File Copy Based Initial Sync params
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.SIX_ZERO_ZERO, true, true, new SearchMongoArgs());
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncSource.KEY));
    assertNull(args.getSetParameterMap().get(CommonStartupSetparams.InitialSyncMethod.KEY));

    // mongotHost added by default for any mongodb version that supports it (6.0+, 4.4.22+, 5.0.18+)
    List.of(
            "6.0.0-alpha0",
            "6.0.0-rc1",
            "6.0.0",
            "6.0.10",
            "6.1.0",
            "4.4.22",
            "4.4.23",
            "5.0.18",
            "5.0.19")
        .forEach(
            v -> {
              args.getSetParameterMap().clear();
              args.getSetParameterMap().put("skipAuthenticationToMongot", true);
              args.getSetParameterMap().put("pinTaskExecCursorConns", true);
              ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
                  args,
                  "/tmp",
                  Version.fromString(v),
                  false,
                  false,
                  new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.TCP));

              assertEquals(
                  NDSFTSDefaults.MONGOT_QUERY_SERVER_ADDRESS,
                  args.getSetParameterMap().get("mongotHost"));
              // Make sure that `skipAuthenticationToMongot` and `skipAuthenticationToMongot` are
              // removed from the parameter map.
              assertFalse(args.getSetParameterMap().containsKey("skipAuthenticationToMongot"));
              assertFalse(args.getSetParameterMap().containsKey("pinTaskExecCursorConns"));
            });

    // mongotHost should not be set for 4.2, 4.4 before 4.4.22, or 5.0 before 5.0.18
    List.of("4.2.24", "4.4.21", "5.0.17")
        .forEach(
            v -> {
              args.getSetParameterMap().clear();
              ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
                  args,
                  "/tmp",
                  Version.fromString(v),
                  false,
                  false,
                  new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.TCP));

              assertNull(args.getSetParameterMap().get("mongotHost"));
              // searchIndexManagementHostAndPort will not be set before version 6.0.8
              assertNull(args.getSetParameterMap().get("searchIndexManagementHostAndPort"));
            });

    // versions that support ShardingTaskExecutorPoolMaxSizeForConfigServers
    List.of("6.0.0", "5.0.10", "4.4.16", "4.2.22")
        .forEach(
            v -> {
              args.getSetParameterMap().clear();
              ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
                  args,
                  "/tmp",
                  Version.fromString(v),
                  false,
                  false,
                  new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.TCP));

              assertEquals(250, args.getSetParameterMap().get("ShardingTaskExecutorPoolMaxSize"));
              assertEquals(
                  Integer.MAX_VALUE,
                  args.getSetParameterMap().get("ShardingTaskExecutorPoolMaxSizeForConfigServers"));

              // If it's already set don't override it
              args.getSetParameterMap().put("ShardingTaskExecutorPoolMaxSize", 100);
              args.getSetParameterMap()
                  .put("ShardingTaskExecutorPoolMaxSizeForConfigServers", 1001);
              ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
                  args,
                  "/tmp",
                  Version.fromString(v),
                  false,
                  false,
                  new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.TCP));
              assertEquals(100, args.getSetParameterMap().get("ShardingTaskExecutorPoolMaxSize"));
              assertEquals(
                  1001,
                  args.getSetParameterMap().get("ShardingTaskExecutorPoolMaxSizeForConfigServers"));
            });

    // versions that don't support ShardingTaskExecutorPoolMaxSizeForConfigServers
    List.of("5.0.9", "4.4.15", "4.2.21", "3.6.0")
        .forEach(
            v -> {
              args.getSetParameterMap().clear();
              ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
                  args,
                  "/tmp",
                  Version.fromString(v),
                  false,
                  false,
                  new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.TCP));
              // searchIndexManagementHostAndPort will not be set before version 6.0.8
              assertNull(args.getSetParameterMap().get("searchIndexManagementHostAndPort"));
              assertNull(args.getSetParameterMap().get("ShardingTaskExecutorPoolMaxSize"));
              assertNull(
                  args.getSetParameterMap().get("ShardingTaskExecutorPoolMaxSizeForConfigServers"));
            });

    // searchIndexManagementHostAndPort will be set when version is 6.0.8+
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("6.0.8"), false, false, new SearchMongoArgs());
    assertEquals(
        EnvoyOnAtlasHost.MMS_UNIX_DOMAIN_SOCKET,
        args.getSetParameterMap().get("searchIndexManagementHostAndPort"));

    // will call mongot through unix domain socket if configured.
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args,
        "/tmp",
        Version.fromString("6.1.0"),
        false,
        false,
        new SearchMongoArgs(0, SearchMongoArgs.MongotCallOptions.UNIX_DOMAIN));
    assertEquals(
        EnvoyOnAtlasHost.MONGOT_UNIX_DOMAIN_SOCKET, args.getSetParameterMap().get("mongotHost"));
    // searchIndexManagementHostAndPort will not be set when version is 6.1.0+
    assertNull(args.getSetParameterMap().get("searchIndexManagementHostAndPort"));

    assertTrue((Boolean) args.getSetParameterMap().get("skipAuthenticationToMongot"));
    assertTrue((Boolean) args.getSetParameterMap().get("pinTaskExecCursorConns"));

    // will call mongot through unix domain socket if configured.
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("7.0.0"), false, false, new SearchMongoArgs());
    // searchIndexManagementHostAndPort will be set when version is 6.0.8+
    assertEquals(
        EnvoyOnAtlasHost.MMS_UNIX_DOMAIN_SOCKET,
        args.getSetParameterMap().get("searchIndexManagementHostAndPort"));

    // condition: (4.4.x and ver >= 4.4.20 ) OR (5.0.x and ver >= 5.0.16) OR (6.0.x and ver >=
    // 6.0.5)  OR ver >= 6.3)
    // 4.4.2 should not get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.FOUR_FOUR_TWO, false, false, new SearchMongoArgs());
    assertNull(
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 4.4.20 clusters should get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.FOUR_FOUR_TWENTY, false, false, new SearchMongoArgs());
    assertEquals(
        CommonStartupSetparams.InternalQueryGlobalProfilingFilter.VAlUE,
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 5.0.2 should not get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.FIVE_ZERO_TWO, false, false, new SearchMongoArgs());
    assertNull(
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 5.0.16 clusters should get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.FIVE_ZERO_SIXTEEN, false, false, new SearchMongoArgs());
    assertEquals(
        CommonStartupSetparams.InternalQueryGlobalProfilingFilter.VAlUE,
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 6.0.4 should not get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.SIX_ZERO_FOUR, false, false, new SearchMongoArgs());
    assertNull(
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 6.0.5 clusters should get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.SIX_ZERO_FIVE, false, false, new SearchMongoArgs());
    assertEquals(
        CommonStartupSetparams.InternalQueryGlobalProfilingFilter.VAlUE,
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));

    // 6.3.x clusters should get internalQueryGlobalProfilingFilter param
    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.SIX_THREE_ZERO, false, false, new SearchMongoArgs());
    assertEquals(
        CommonStartupSetparams.InternalQueryGlobalProfilingFilter.VAlUE,
        args.getSetParameterMap()
            .get(CommonStartupSetparams.InternalQueryGlobalProfilingFilter.KEY));
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess_DefaultStartupFCV() {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    final ClusterDescription clusterDescription = getMockedClusterDescription();
    final String defaultStartupFcv = "6.3.0";

    final SetParameterMapForMongoDProcessDetails details =
        SetParameterMapForMongoDProcessDetails.builder()
            .args(args)
            .clusterDescription(clusterDescription)
            .mtmClusterTypes(Collections.emptyList())
            .defaultStartupFCV(defaultStartupFcv)
            .searchMongoArgs(new SearchMongoArgs())
            .build();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().mongoDBVersion(VersionUtils.FIVE_ZERO_ZERO).build());
    assertFalse(
        args.getSetParameterMap().containsKey(MongoDStartupSetParams.MONGOD_DEFAULT_STARTUP_FCV));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().mongoDBVersion(VersionUtils.FIVE_TWO_ZERO).build());
    assertFalse(
        args.getSetParameterMap().containsKey(MongoDStartupSetParams.MONGOD_DEFAULT_STARTUP_FCV));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().mongoDBVersion(VersionUtils.SIX_ZERO_ZERO).build());
    assertFalse(
        args.getSetParameterMap().containsKey(MongoDStartupSetParams.MONGOD_DEFAULT_STARTUP_FCV));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().mongoDBVersion(VersionUtils.SEVEN_ZERO_ZERO_RC0).build());
    assertFalse(
        args.getSetParameterMap().containsKey(MongoDStartupSetParams.MONGOD_DEFAULT_STARTUP_FCV));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().mongoDBVersion(VersionUtils.SEVEN_ZERO_ZERO).build());
    assertEquals(
        defaultStartupFcv,
        args.getSetParameterMap().get(MongoDStartupSetParams.MONGOD_DEFAULT_STARTUP_FCV));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().mongoDBVersion(VersionUtils.SEVEN_ZERO_ZERO_RC1).build());
    assertEquals(
        defaultStartupFcv,
        args.getSetParameterMap().get(MongoDStartupSetParams.MONGOD_DEFAULT_STARTUP_FCV));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder()
            .mongoDBVersion(VersionUtils.SEVEN_ZERO_ZERO_RC1)
            .defaultStartupFCV(null)
            .build());
    assertFalse(
        args.getSetParameterMap().containsKey(MongoDStartupSetParams.MONGOD_DEFAULT_STARTUP_FCV));
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess_queryStats() {
    final LogComponentVerbosity logComponentVerbosity =
        new LogComponentVerbosity.Builder()
            .setComponent(LogComponentVerbosity.QUERY_STATS_COMPONENT_NAME, 3)
            .setComponent("test", 1)
            .build();

    { // supported mongodb version and non-sharded, should be unchanged
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      doReturn(ClusterType.REPLICASET).when(clusterDescription).getClusterType();

      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final String jsonString = logComponentVerbosity.toJsonString();
      args.setLogComponentVerbosity(jsonString);
      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.SEVEN_ONE_ZERO_RC0)
              .searchMongoArgs(new SearchMongoArgs())
              .build());
      assertEquals(jsonString, args.getSetParameterMap().get(FieldDefs.LOG_COMPONENT_VERBOSITY));
    }

    { // sharded cluster, queryStats should be removed
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setLogComponentVerbosity(logComponentVerbosity.toJsonString());

      final ClusterDescription clusterDescription = getMockedClusterDescription();
      doReturn(ClusterType.SHARDED).when(clusterDescription).getClusterType();

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.SEVEN_ONE_ZERO_RC0)
              .searchMongoArgs(new SearchMongoArgs())
              .build());
      final LogComponentVerbosity logComponentVerbosityAfterUpdate =
          LogComponentVerbosity.fromJsonString(
              (String) args.getSetParameterMap().get(FieldDefs.LOG_COMPONENT_VERBOSITY));
      assertEquals(1, logComponentVerbosityAfterUpdate.getLogComponents().size());
      assertFalse(
          logComponentVerbosityAfterUpdate.containsLogComponent(
              LogComponentVerbosity.QUERY_STATS_COMPONENT_NAME));
    }

    { // unsupported mongodb version, queryStats should be removed
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setLogComponentVerbosity(logComponentVerbosity.toJsonString());

      final ClusterDescription clusterDescription = getMockedClusterDescription();
      doReturn(ClusterType.REPLICASET).when(clusterDescription).getClusterType();

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.SIX_ZERO_ZERO)
              .searchMongoArgs(new SearchMongoArgs())
              .build());
      final LogComponentVerbosity logComponentVerbosityAfterUpdate =
          LogComponentVerbosity.fromJsonString(
              (String) args.getSetParameterMap().get(FieldDefs.LOG_COMPONENT_VERBOSITY));
      assertEquals(1, logComponentVerbosityAfterUpdate.getLogComponents().size());
      assertFalse(
          logComponentVerbosityAfterUpdate.containsLogComponent(
              LogComponentVerbosity.QUERY_STATS_COMPONENT_NAME));
    }
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess_indexPruning() {
    {
      // Should be explicitly disabled for 8.0.0
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.EIGHT_ZERO_ZERO)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertTrue(parameterMap.containsKey(FieldDefs.INTERNAL_QUERY_PLANNER_ENABLE_INDEX_PRUNING));
      assertFalse(
          (Boolean) parameterMap.get(FieldDefs.INTERNAL_QUERY_PLANNER_ENABLE_INDEX_PRUNING));
    }

    {
      // Should be explicitly disabled for 8.0.0-rc0
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.EIGHT_ZERO_ZERO_RC0)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertTrue(parameterMap.containsKey(FieldDefs.INTERNAL_QUERY_PLANNER_ENABLE_INDEX_PRUNING));
      assertFalse(
          (Boolean) parameterMap.get(FieldDefs.INTERNAL_QUERY_PLANNER_ENABLE_INDEX_PRUNING));
    }

    {
      {
        // Should be explicitly disabled for any 8.0.0 release candidate
        final ProcessArguments2_6 args = new ProcessArguments2_6();
        final ClusterDescription clusterDescription = getMockedClusterDescription();
        when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

        ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
            SetParameterMapForMongoDProcessDetails.builder()
                .args(args)
                .clusterDescription(clusterDescription)
                .mtmClusterTypes(Collections.emptyList())
                .mongoDBVersion(VersionUtils.parse("8.0.0-rc5"))
                .searchMongoArgs(new SearchMongoArgs())
                .build());

        final Map<String, Object> parameterMap = args.getSetParameterMap();
        assertTrue(parameterMap.containsKey(FieldDefs.INTERNAL_QUERY_PLANNER_ENABLE_INDEX_PRUNING));
        assertFalse(
            (Boolean) parameterMap.get(FieldDefs.INTERNAL_QUERY_PLANNER_ENABLE_INDEX_PRUNING));
      }
    }

    {
      // Should not override if argument is set explicitly
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setSetParameterMap(
          new HashMap<>(Map.of(FieldDefs.INTERNAL_QUERY_PLANNER_ENABLE_INDEX_PRUNING, true)));

      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.EIGHT_ZERO_ZERO)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertTrue(parameterMap.containsKey(FieldDefs.INTERNAL_QUERY_PLANNER_ENABLE_INDEX_PRUNING));
      assertTrue((Boolean) parameterMap.get(FieldDefs.INTERNAL_QUERY_PLANNER_ENABLE_INDEX_PRUNING));
    }

    {
      // Should not be modified if the version is not 8.0.0 or 8.0.0-rc0
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.SEVEN_ONE_ZERO)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertFalse(parameterMap.containsKey(FieldDefs.INTERNAL_QUERY_PLANNER_ENABLE_INDEX_PRUNING));
    }
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess_fassertOnLockTimeoutForStepUpDown() {
    {
      // Should be enabled for >= 8.0.0
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.EIGHT_ZERO_ZERO)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertTrue(parameterMap.containsKey(FieldDefs.FASSERT_ON_LOCK_TIMEOUT_FOR_STEP_UP_DOWN));
      assertEquals(
          60, (Integer) parameterMap.get(FieldDefs.FASSERT_ON_LOCK_TIMEOUT_FOR_STEP_UP_DOWN));
    }

    {
      // Should not override if argument is set explicitly
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setSetParameterMap(
          new HashMap<>(Map.of(FieldDefs.FASSERT_ON_LOCK_TIMEOUT_FOR_STEP_UP_DOWN, 120)));

      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.EIGHT_ZERO_ZERO)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertTrue(parameterMap.containsKey(FieldDefs.FASSERT_ON_LOCK_TIMEOUT_FOR_STEP_UP_DOWN));
      assertEquals(
          120, (Integer) parameterMap.get(FieldDefs.FASSERT_ON_LOCK_TIMEOUT_FOR_STEP_UP_DOWN));
    }

    {
      // Should not be modified if the version is < 8.0.0
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.SEVEN_ONE_ZERO)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertFalse(parameterMap.containsKey(FieldDefs.FASSERT_ON_LOCK_TIMEOUT_FOR_STEP_UP_DOWN));
    }
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess_spillWiredTigerSessionMax() {
    {
      // Should be set to 33000 for MongoDB 8.2.0
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.parse("8.2.0"))
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertTrue(parameterMap.containsKey("spillWiredTigerSessionMax"));
      assertEquals(33000, (Integer) parameterMap.get("spillWiredTigerSessionMax"));
    }

    {
      // Should be set to 33000 for MongoDB 8.2.1
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.parse("8.2.1"))
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertTrue(parameterMap.containsKey("spillWiredTigerSessionMax"));
      assertEquals(33000, (Integer) parameterMap.get("spillWiredTigerSessionMax"));
    }

    {
      // Should be set to 33000 for MongoDB 8.2.5
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.parse("8.2.5"))
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertTrue(parameterMap.containsKey("spillWiredTigerSessionMax"));
      assertEquals(33000, (Integer) parameterMap.get("spillWiredTigerSessionMax"));
    }

    {
      // Should NOT be set for MongoDB 8.1.9 (wrong minor version)
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.parse("8.1.9"))
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertFalse(parameterMap.containsKey("spillWiredTigerSessionMax"));
    }

    {
      // Should NOT be set for MongoDB 8.0.10 (wrong minor version)
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.EIGHT_ZERO_TEN)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertFalse(parameterMap.containsKey("spillWiredTigerSessionMax"));
    }

    {
      // Should NOT be set for MongoDB 7.0.18 (wrong major version)
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.SEVEN_ZERO_EIGHTEEN)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertFalse(parameterMap.containsKey("spillWiredTigerSessionMax"));
    }

    {
      // Should NOT be set for MongoDB 9.0.0 (wrong major version)
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.NINE_ZERO_ZERO)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertFalse(parameterMap.containsKey("spillWiredTigerSessionMax"));
    }

    {
      // Should NOT be set for MongoDB 8.1.99 (version < 8.2.0)
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.parse("8.1.99"))
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertFalse(parameterMap.containsKey("spillWiredTigerSessionMax"));
    }

    {
      // Should NOT override if argument is set explicitly
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setSetParameterMap(new HashMap<>(Map.of("spillWiredTigerSessionMax", 50000)));

      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.parse("8.2.0"))
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertTrue(parameterMap.containsKey("spillWiredTigerSessionMax"));
      assertEquals(50000, (Integer) parameterMap.get("spillWiredTigerSessionMax"));
    }

    {
      // Should REMOVE parameter when upgrading from 8.2.0 to 8.0.10 (downgrade scenario)
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setSetParameterMap(new HashMap<>(Map.of("spillWiredTigerSessionMax", 33000)));

      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.EIGHT_ZERO_TEN)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertFalse(parameterMap.containsKey("spillWiredTigerSessionMax"));
    }

    {
      // Should REMOVE parameter when upgrading from 8.2.0 to 9.0.0 (upgrade scenario)
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setSetParameterMap(new HashMap<>(Map.of("spillWiredTigerSessionMax", 33000)));

      final ClusterDescription clusterDescription = getMockedClusterDescription();
      when(clusterDescription.getClusterType()).thenReturn(ClusterType.REPLICASET);

      ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
          SetParameterMapForMongoDProcessDetails.builder()
              .args(args)
              .clusterDescription(clusterDescription)
              .mtmClusterTypes(Collections.emptyList())
              .mongoDBVersion(VersionUtils.NINE_ZERO_ZERO)
              .searchMongoArgs(new SearchMongoArgs())
              .build());

      final Map<String, Object> parameterMap = args.getSetParameterMap();
      assertFalse(parameterMap.containsKey("spillWiredTigerSessionMax"));
    }
  }

  @Test
  public void testSetSetParameterMapForMongoSProcess_DoesNotContainDefaultStartupFCV() {
    final ProcessArguments2_6 args = new ProcessArguments2_6();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.SEVEN_ZERO_ZERO_RC1, false, false, new SearchMongoArgs());
    assertFalse(
        args.getSetParameterMap().containsKey(MongoDStartupSetParams.MONGOD_DEFAULT_STARTUP_FCV));
  }

  @Test
  public void testSetSetParameterMapForMongoSProcess_LoadBalancedMode() {
    final ProcessArguments2_6 args = new ProcessArguments2_6();

    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.FOUR_ZERO_ZERO, false, false, new SearchMongoArgs());
    assertFalse(
        args.getSetParameterMap().containsKey(MongosStartupSetParams.MONGOS_LOAD_BALANCER_PORT));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.FIVE_ZERO_ZERO, false, false, new SearchMongoArgs());
    assertFalse(
        args.getSetParameterMap().containsKey(MongosStartupSetParams.MONGOS_LOAD_BALANCER_PORT));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.parse("5.0.9"), false, false, new SearchMongoArgs());
    assertEquals(
        INDSDefaults.MONGOS_LOAD_BALANCER_INGRESS_PORT,
        args.getSetParameterMap().get(MongosStartupSetParams.MONGOS_LOAD_BALANCER_PORT));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.FIVE_TWO_ZERO, false, false, new SearchMongoArgs());
    assertFalse(
        args.getSetParameterMap().containsKey(MongosStartupSetParams.MONGOS_LOAD_BALANCER_PORT));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.FIVE_THREE_ZERO_RC0, false, false, new SearchMongoArgs());
    assertFalse(
        args.getSetParameterMap().containsKey(MongosStartupSetParams.MONGOS_LOAD_BALANCER_PORT));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.parse("5.3.0-rc3"), false, false, new SearchMongoArgs());
    assertEquals(
        INDSDefaults.MONGOS_LOAD_BALANCER_INGRESS_PORT,
        args.getSetParameterMap().get(MongosStartupSetParams.MONGOS_LOAD_BALANCER_PORT));

    args.getSetParameterMap().clear();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", VersionUtils.SIX_ZERO_ZERO, false, false, new SearchMongoArgs());
    assertEquals(
        INDSDefaults.MONGOS_LOAD_BALANCER_INGRESS_PORT,
        args.getSetParameterMap().get(MongosStartupSetParams.MONGOS_LOAD_BALANCER_PORT));
  }

  @Test
  public void testSetSetParameterMapForMongoSProcess_queryStats() {
    { // supported mongodb version, should be unchanged
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      final LogComponentVerbosity logComponentVerbosity =
          new LogComponentVerbosity.Builder()
              .setComponent(LogComponentVerbosity.QUERY_STATS_COMPONENT_NAME, 3)
              .setComponent("test", 1)
              .build();
      final String jsonString = logComponentVerbosity.toJsonString();
      args.setLogComponentVerbosity(jsonString);
      ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
          args, "/tmp", VersionUtils.SEVEN_ONE_ZERO_RC0, false, false, new SearchMongoArgs());
      assertEquals(jsonString, args.getSetParameterMap().get(FieldDefs.LOG_COMPONENT_VERBOSITY));
    }

    { // valid version, queryStats component should be removed
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setSetParameterMap(new HashMap<>());
      final LogComponentVerbosity logComponentVerbosity =
          new LogComponentVerbosity.Builder()
              .setComponent(LogComponentVerbosity.QUERY_STATS_COMPONENT_NAME, 3)
              .setComponent("test", 1)
              .build();
      args.setLogComponentVerbosity(logComponentVerbosity.toJsonString());
      ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
          args, "/tmp", VersionUtils.SIX_ZERO_ZERO, false, false, new SearchMongoArgs());
      final LogComponentVerbosity logComponentVerbosityAfterUpdate =
          LogComponentVerbosity.fromJsonString(
              (String) args.getSetParameterMap().get(FieldDefs.LOG_COMPONENT_VERBOSITY));
      assertEquals(1, logComponentVerbosityAfterUpdate.getLogComponents().size());
      assertFalse(
          logComponentVerbosityAfterUpdate.containsLogComponent(
              LogComponentVerbosity.QUERY_STATS_COMPONENT_NAME));
    }
  }

  @Test
  public void testGetMaxIncomingConnectionsOverride() {
    final ObjectId containerId = new ObjectId();
    final NDSGroup ndsGroup = mock(NDSGroup.class);

    final DedicatedCloudProviderContainer container = mock(DedicatedCloudProviderContainer.class);
    final List<String> defaultOverrides = List.of("127.0.0.1/32");
    final String containerCIDR = "*******/21";
    final List<String> expectedOverrides =
        Stream.concat(defaultOverrides.stream(), Stream.of(containerCIDR))
            .collect(Collectors.toList());
    doReturn(containerCIDR).when(container).getAtlasCidr();
    doReturn(Optional.of(container))
        .when(ndsGroup)
        .getDedicatedCloudProviderContainer(eq(containerId));

    final InstanceHardware instanceHardware = mock(InstanceHardware.class);
    doReturn(containerId).when(instanceHardware).getCloudContainerId();

    assertEquals(
        expectedOverrides,
        ClusterDeploymentProcessUtil.getMaxIncomingConnectionsOverride(
            ndsGroup, instanceHardware, AWSRegionName.AP_NORTHEAST_1));

    // Container not found in group - return default overrides
    doReturn(Optional.empty()).when(ndsGroup).getDedicatedCloudProviderContainer(eq(containerId));
    assertEquals(
        defaultOverrides,
        ClusterDeploymentProcessUtil.getMaxIncomingConnectionsOverride(
            ndsGroup, instanceHardware, AWSRegionName.AP_NORTHEAST_1));
  }

  @Test
  public void testUpdateInstanceMongoSProcess_ShardedCluster_34() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();

    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription_34 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "3.4.0", ClusterType.SHARDED, 2);

    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription_34));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_34,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "atlas-5tulik-mongos-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());

    assertX509ProcessArgs(process, shardedClusterDescription_34, ndsGroup);
    // Deployment cluster name
    assertEquals("atlas-5tulik", process.getCluster());
    assertNotNull(process.getHostname());
    assertEquals(ProcessType.MONGOS, process.getProcessType());
    assertNotNull(process.getArgs2_6());
    assertNotNull(process.getArgs2_6().getLogPath());
    assertEquals(
        hardware0.getHardware().get(0).getMaxIncomingConnections().get(),
        process.getArgs().getMaxIncomingConnections());
    assertNull(process.getArgs().getNetworkMessageCompressors());
    assertNull(process.getArgs2_6().getAuditLogDestination());
    assertNull(process.getArgs2_6().getAuditLogFormat());
    assertNull(process.getArgs2_6().getAuditLogPath());
    assertNull(process.getArgs2_6().getAuditLogFilter());
    assertNull(process.getArgs2_6().getAuditAuthorizationSuccess());
  }

  @Test
  public void testUpdateInstanceMongoSProcess_ShardedCluster_36() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription_36 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "3.6.0", ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription_36));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_36,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "atlas-5tulik-mongos-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertX509ProcessArgs(process, shardedClusterDescription_36, ndsGroup);

    assertNotNull(process.getArgs2_6().getLogPath());
    assertEquals("snappy,zlib", process.getArgs().getNetworkMessageCompressors());

    final AuditLog auditLog = ndsGroup.getAuditLog();

    doReturn(true).when(auditLog).isEnabled();
    doReturn(Optional.of("{ atype: { $in: [ 'createCollection' ] } }"))
        .when(auditLog)
        .getAuditFilter();
    doReturn(false).when(auditLog).isAuditAuthorizationSuccess();

    final Process processAuditLog1 = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_36,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        processAuditLog1,
        new ProcessArguments2_6(),
        ndsGroup,
        "atlas-5tulik-mongos-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertEquals("file", processAuditLog1.getArgs2_6().getAuditLogDestination());
    assertEquals("JSON", processAuditLog1.getArgs2_6().getAuditLogFormat());
    assertTrue(
        Pattern.matches(
            "/srv/mongodb/atlas-5tulik-mongos-[0-9]+/auditLog.json",
            processAuditLog1.getArgs2_6().getAuditLogPath()));
    assertEquals(
        "{ atype: { $in: [ 'createCollection' ] } }",
        processAuditLog1.getArgs2_6().getAuditLogFilter());
    assertFalse(processAuditLog1.getArgs2_6().getAuditAuthorizationSuccess());

    doReturn(true).when(auditLog).isAuditAuthorizationSuccess();

    final Process processAuditLog2 = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_36,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        processAuditLog2,
        new ProcessArguments2_6(),
        ndsGroup,
        "atlas-5tulik-mongos-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertEquals("file", processAuditLog2.getArgs2_6().getAuditLogDestination());
    assertEquals("JSON", processAuditLog2.getArgs2_6().getAuditLogFormat());
    assertTrue(
        Pattern.matches(
            "/srv/mongodb/atlas-5tulik-mongos-[0-9]+/auditLog.json",
            processAuditLog2.getArgs2_6().getAuditLogPath()));
    assertEquals(
        "{ atype: { $in: [ 'createCollection' ] } }",
        processAuditLog2.getArgs2_6().getAuditLogFilter());
    assertTrue(processAuditLog2.getArgs2_6().getAuditAuthorizationSuccess());
  }

  @Test
  public void testUpdateInstanceMongoSProcess_ShardedCluster_42() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription_42 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "4.2.0", ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription_42));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_42,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertX509ProcessArgs(process, shardedClusterDescription_42, ndsGroup);
  }

  @Test
  public void testUpdateInstanceMongoSProcess_ShardedCluster_43() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription_433 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "4.3.3", ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription_433));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_433,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertX509ProcessArgs(process, shardedClusterDescription_433, ndsGroup);

    assertNull(process.getArgs2_6().getInternalQueryStatsRateLimit());
  }

  @Test
  public void testUpdateInstanceMongoSProcess_ShardedCluster_70() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription_7016 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "7.0.16", ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription_7016));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_7016,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());

    assertEquals(100, process.getArgs2_6().getInternalQueryStatsRateLimit().intValue());
  }

  // Test process args for cluster managed X.509 disabled (CLOUDP-60306)
  // Note: modifies group mock so keep as last test
  @Test
  public void testUpdateInstanceMongoSProcess_ShardedCluster_disabled_x509() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription_disabled_x509 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "4.3.3", ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription_disabled_x509));

    final NDSManagedX509 disabledManagedX509 = new NDSManagedX509();
    final NDSUserSecurity userSecurity = ndsGroup.getUserSecurity();
    doReturn(disabledManagedX509).when(userSecurity).getManagedX509();

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_disabled_x509,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertX509ProcessArgs(process, shardedClusterDescription_disabled_x509, ndsGroup);
  }

  @Test
  public void testUpdateInstanceMongoDProcess_NoRegionConfigForInstanceHardware() {
    final ObjectId containerId = new ObjectId();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ReplicationSpec replicationSpec =
        new ReplicationSpec(
            new ObjectId(),
            new ObjectId(),
            new ObjectId(),
            NDSDefaults.ZONE_NAME,
            1,
            NDSModelTestFactory.getRegionConfigs(
                3, List.of(AWSRegionName.US_WEST_1, AzureRegionName.US_WEST_2)));

    final ClusterDescription clusterDescription =
        getTestReplicaSet(TEST_CLUSTER_NAME, "4.2.0")
            .copy()
            .setReplicationSpecList(List.of(replicationSpec))
            .build();

    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, clusterDescription));

    // AWS instance
    final AWSInstanceHardware awsInstanceHardware =
        (AWSInstanceHardware) hardware0.getHardware().get(0);

    final Process awsProcess = new Process();
    awsProcess.setName("node-0");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        clusterDescription,
        Collections.emptyList(),
        null,
        hardware0,
        awsInstanceHardware,
        awsProcess,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    final RollableValue.RolledOutValue<?> awsRolledOutValue =
        assertInstanceOf(
            RollableValue.RolledOutValue.class,
            awsInstanceHardware.getMaxIndexBuildMemoryUsageMegabytes().get());
    assertEquals(
        awsInstanceHardware.getMaxIncomingConnections().get(),
        awsProcess.getArgs().getMaxIncomingConnections());
    assertEquals(
        awsInstanceHardware.getWiredTigerEngineConfigString().get(),
        awsProcess.getArgs().getWiredTigerEngineConfigString());
    assertEquals(
        awsRolledOutValue.value(),
        awsProcess.getArgs().getSetParameterMap().get("maxIndexBuildMemoryUsageMegabytes"));
    assertTrue(awsProcess.isMongod());

    // instance not provisioned either
    final AWSInstanceHardware awsInstanceHardware_noInstanceSize =
        (AWSInstanceHardware)
            awsInstanceHardware.copy().setInstanceSize(null).setInstanceId(new ObjectId()).build();

    final Process awsProcessEmpty = new Process();
    awsProcessEmpty.setName("node-0");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        clusterDescription,
        Collections.emptyList(),
        null,
        hardware0,
        awsInstanceHardware_noInstanceSize,
        awsProcessEmpty,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertNull(awsProcessEmpty.getArgs().getMaxIncomingConnections());
    assertNull(awsProcessEmpty.getArgs().getWiredTigerEngineConfigString());
    assertNull(
        awsProcessEmpty.getArgs().getSetParameterMap().get("maxIndexBuildMemoryUsageMegabytes"));
  }

  // test updateInstanceMongoDProcess for analytics node which has different size than electable
  // and pInstanceHardware.instanceSize is unset yet.
  // This could happen when replace a vm for analytics node.
  @Test
  public void testUpdateInstanceMongoDProcess_AsymmetricAnalytics() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription =
        getTestShardedCluster(TEST_CLUSTER_NAME, "4.2.0", ClusterType.SHARDED, 2);

    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription));

    final int analyticsNodeIdx = 3;

    // first call updateInstanceMongoDProcess with InstanceHardware WITHOUT instance size
    final AWSInstanceHardware analyticsNodeNoInstanceSizeHardware =
        (AWSInstanceHardware)
            hardware0.getHardware().get(analyticsNodeIdx).copy().setInstanceSize(null).build();

    final ProcessArguments2_6 processArgs = new ProcessArguments2_6();
    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        config,
        shardedClusterDescription,
        Collections.emptyList(),
        null,
        hardware0,
        analyticsNodeNoInstanceSizeHardware,
        process,
        processArgs,
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    // verify we are setting the correct value for wt engine cache size using analytics node
    // instance from cluster description
    assertEquals(
        AWSNDSInstanceSize.M80.getWiredTigerEngineConfigString().get(),
        processArgs.getWiredTigerEngineConfigString());

    // then call updateInstanceMongoDProcess with InstanceHardware HAVING instance size
    final AWSInstanceHardware analyticsNodeWithInstanceSizeHardware =
        (AWSInstanceHardware) hardware0.getHardware().get(3).copy().build();

    final ProcessArguments2_6 processArgs2 = new ProcessArguments2_6();
    final Process process2 = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        config,
        shardedClusterDescription,
        Collections.emptyList(),
        null,
        hardware0,
        analyticsNodeWithInstanceSizeHardware,
        process2,
        processArgs2,
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    // they should produce identical process args (not only the WT cache size configStr)
    assertEquals(processArgs2, processArgs);
  }

  @Test
  public void testUpdateInstanceMongoSProcess_InstanceHardwareBeingProvisioned() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ReplicationSpec replicationSpec =
        new ReplicationSpec(
                new ObjectId(),
                new ObjectId(),
                new ObjectId(),
                NDSDefaults.ZONE_NAME,
                1,
                NDSModelTestFactory.getRegionConfigs(
                    3, List.of(AWSRegionName.US_WEST_1, AzureRegionName.US_WEST_2)))
            .copy()
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M10))
            .updateAllHardware(
                new AzureHardwareSpec.Builder().setInstanceSize(AzureNDSInstanceSize.M10))
            .build();

    final ClusterDescription shardedClusterDescription_42 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "4.2.0", ClusterType.SHARDED, 2)
            .copy()
            .setReplicationSpecList(List.of(replicationSpec))
            .build();

    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription_42));

    final AWSInstanceHardware awsInstanceHardware =
        (AWSInstanceHardware) hardware0.getHardware().get(0).copy().setInstanceSize(null).build();

    final Process awsProcess = new Process();
    awsProcess.setName("node-0");

    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_42,
        hardware0,
        awsInstanceHardware,
        awsProcess,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());

    assertEquals(
        shardedClusterDescription_42
            .getOnlyHardwareSpecForProvider(CloudProvider.AWS, NodeType.ELECTABLE)
            .orElseThrow()
            .getInstanceSize()
            .getMaxIncomingConnections()
            .get(),
        awsProcess.getArgs().getMaxIncomingConnections());
    assertTrue(awsProcess.isMongos());

    final AzureInstanceHardware azureInstanceHardware =
        (AzureInstanceHardware) hardware0.getHardware().get(2).copy().setInstanceSize(null).build();

    final Process azureProcess = new Process();
    azureProcess.setName("node-0");

    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_42,
        hardware0,
        azureInstanceHardware,
        azureProcess,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());

    assertEquals(
        shardedClusterDescription_42
            .getOnlyHardwareSpecForProvider(CloudProvider.AZURE, NodeType.ELECTABLE)
            .orElseThrow()
            .getInstanceSize()
            .getMaxIncomingConnections()
            .get(),
        azureProcess.getArgs().getMaxIncomingConnections());
    assertTrue(azureProcess.isMongos());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_InstanceHardwareBeingProvisioned() {
    final ObjectId containerId = new ObjectId();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ReplicationSpec replicationSpec =
        new ReplicationSpec(
                new ObjectId(),
                new ObjectId(),
                new ObjectId(),
                NDSDefaults.ZONE_NAME,
                1,
                NDSModelTestFactory.getRegionConfigs(
                    3, List.of(AWSRegionName.US_WEST_1, AzureRegionName.US_WEST_2)))
            .copy()
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M10))
            .updateAllHardware(
                new AzureHardwareSpec.Builder().setInstanceSize(AzureNDSInstanceSize.M10))
            .build();

    final ClusterDescription clusterDescription =
        getTestReplicaSet(TEST_CLUSTER_NAME, "4.2.0")
            .copy()
            .setReplicationSpecList(List.of(replicationSpec))
            .build();

    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, clusterDescription));

    // AWS instance
    final AWSInstanceHardware awsInstanceHardware =
        (AWSInstanceHardware) hardware0.getHardware().get(0).copy().setInstanceSize(null).build();

    final Process awsProcess = new Process();
    awsProcess.setName("node-0");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        clusterDescription,
        Collections.emptyList(),
        null,
        hardware0,
        awsInstanceHardware,
        awsProcess,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    final HardwareSpec awsHardwareSpec =
        clusterDescription
            .getOnlyHardwareSpecForProvider(CloudProvider.AWS, NodeType.ELECTABLE)
            .orElseThrow();
    assertEquals(
        awsHardwareSpec.getInstanceSize().getMaxIncomingConnections().get(),
        awsProcess.getArgs().getMaxIncomingConnections());
    assertEquals(
        awsHardwareSpec.getInstanceSize().getWiredTigerEngineConfigString().get(),
        awsProcess.getArgs().getWiredTigerEngineConfigString());

    final RollableValue.RolledOutValue<?> awsRolledOutValue =
        assertInstanceOf(
            RollableValue.RolledOutValue.class,
            awsHardwareSpec.getInstanceSize().getMaxIndexBuildMemoryUsageMegabytes());
    assertEquals(
        awsRolledOutValue.value(),
        awsProcess.getArgs().getSetParameterMap().get("maxIndexBuildMemoryUsageMegabytes"));
    assertTrue(awsProcess.isMongod());

    // Azure instance
    final AzureInstanceHardware azureInstanceHardware =
        (AzureInstanceHardware) hardware0.getHardware().get(2).copy().setInstanceSize(null).build();

    final Process azureProcess = new Process();
    azureProcess.setName("node-0");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        clusterDescription,
        Collections.emptyList(),
        null,
        hardware0,
        azureInstanceHardware,
        azureProcess,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    final HardwareSpec azureHardwareSpec =
        clusterDescription
            .getOnlyHardwareSpecForProvider(CloudProvider.AZURE, NodeType.ELECTABLE)
            .orElseThrow();
    final RollableValue.RolledOutValue<?> azureRolledOutValue =
        assertInstanceOf(
            RollableValue.RolledOutValue.class,
            awsHardwareSpec.getInstanceSize().getMaxIndexBuildMemoryUsageMegabytes());
    assertEquals(
        azureHardwareSpec.getInstanceSize().getMaxIncomingConnections().get(),
        azureProcess.getArgs().getMaxIncomingConnections());
    assertEquals(
        azureHardwareSpec.getInstanceSize().getWiredTigerEngineConfigString().get(),
        azureProcess.getArgs().getWiredTigerEngineConfigString());
    assertEquals(
        azureRolledOutValue.value(),
        azureProcess.getArgs().getSetParameterMap().get("maxIndexBuildMemoryUsageMegabytes"));
    assertTrue(azureProcess.isMongod());
  }

  @Test
  public void updateInstanceMongoDProcess_DefaultRWConcern() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final CustomDefaultRWConcern customDefaultRWConcern = new CustomDefaultRWConcern();
    customDefaultRWConcern.setDefaultWriteConcern("majority");
    customDefaultRWConcern.setDefaultReadConcern("local");

    // 4.2
    {
      final ClusterDescription testCluster_42 =
          getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.FOUR_TWO_ZERO.getVersion());
      final ReplicaSetHardware hardware_360 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster_42));

      final Process process = new Process();
      process.setName("process");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_42,
          Collections.emptyList(),
          null,
          hardware_360,
          hardware_360.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.of(customDefaultRWConcern),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getDefaultRWConcern());
    }

    // 4.4
    {
      final ClusterDescription testCluster_44 =
          getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.FOUR_FOUR_ZERO.getVersion());
      final ReplicaSetHardware hardware_360 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster_44));

      final Process process = new Process();
      process.setName("process");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_44,
          Collections.emptyList(),
          null,
          hardware_360,
          hardware_360.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.of(customDefaultRWConcern),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals("majority", process.getDefaultRWConcern().getDefaultWriteConcern().getW());
      assertEquals("local", process.getDefaultRWConcern().getDefaultReadConcern().getLevel());
    }
  }

  @Test
  public void updateInstanceMongoDProcess_UseMongoDBVersionAndFCVFromProcess() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final ObjectId containerId = new ObjectId();
    final CustomDefaultRWConcern customDefaultRWConcern = new CustomDefaultRWConcern();
    customDefaultRWConcern.setDefaultWriteConcern("majority");
    customDefaultRWConcern.setDefaultReadConcern("local");

    // 44 with 42 mongodb version and FCV override
    {
      final ClusterDescription testCluster_44 =
          spy(getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.FOUR_FOUR_ZERO.getVersion()));
      final ReplicaSetHardware hardware_360 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster_44));

      final Process process = new Process();
      process.setName("process");
      process.setVersion("4.2.0");
      process.setFeatureCompatibilityVersion("4.2");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_44,
          Collections.emptyList(),
          null,
          hardware_360,
          hardware_360.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.of(customDefaultRWConcern),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.SINGLE_NODE,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getDefaultRWConcern());
      verify(testCluster_44, never()).getMongoDBVersion();
      verify(testCluster_44, never()).getFeatureCompatibilityVersion();
      assertEquals(testCluster_44.getMongoDBVersion(), Version.fromString("4.4.0"));
      assertEquals(process.getVersion(), "4.2.0");
      assertEquals(process.getFeatureCompatibilityVersion(), "4.2");
    }

    // 44 with 42 FCV override
    {
      final ClusterDescription testCluster_44 =
          spy(getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.FOUR_FOUR_ZERO.getVersion()));
      final ReplicaSetHardware hardware_360 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster_44));

      final Process process = new Process();
      process.setName("process");
      process.setVersion("4.4.0");
      process.setFeatureCompatibilityVersion("4.2");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_44,
          Collections.emptyList(),
          null,
          hardware_360,
          hardware_360.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.of(customDefaultRWConcern),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.SINGLE_NODE,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getDefaultRWConcern());
      verify(testCluster_44, never()).getMongoDBVersion();
      verify(testCluster_44, never()).getFeatureCompatibilityVersion();
      assertEquals(testCluster_44.getMongoDBVersion(), Version.fromString("4.4.0"));
      assertEquals(process.getVersion(), "4.4.0");
      assertEquals(process.getFeatureCompatibilityVersion(), "4.2");
    }

    {
      // 5.0 with 4.4 version override
      final ClusterDescription testCluster_50 =
          spy(getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.FIVE_ZERO_ZERO.getVersion()));
      final ReplicaSetHardware hardware =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, new ObjectId(), testCluster_50));

      final Process process = new Process();
      process.setName("process");
      process.setVersion("4.4.0");
      process.setFeatureCompatibilityVersion("4.4.0");

      final ProcessArguments2_6 processArg = new ProcessArguments2_6();
      processArg.setMinSnapshotHistoryWindowInSeconds(100);
      final DefaultRWConcern initialDefaultRWConcern = process.getDefaultRWConcern();

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_50,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          processArg,
          ndsGroup,
          Optional.of(customDefaultRWConcern),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.SINGLE_NODE,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals(initialDefaultRWConcern, process.getDefaultRWConcern());
      verify(testCluster_50, never()).getMongoDBVersion();
      assertEquals(testCluster_50.getMongoDBVersion(), Version.fromString("5.0.0"));
      assertEquals(process.getVersion(), "4.4.0");
      assertNull(process.getArgs2_6().getMinSnapshotHistoryWindowInSeconds());
    }
  }

  @Test
  public void testUpdateInstanceMongoSProcess_DefaultRWConcern() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final CustomDefaultRWConcern customDefaultRWConcern = new CustomDefaultRWConcern();
    customDefaultRWConcern.setDefaultWriteConcern("majority");
    customDefaultRWConcern.setDefaultReadConcern("local");

    // 4.2
    {
      final ShardedClusterDescription shardedClusterDescription_42 =
          getTestShardedCluster(TEST_CLUSTER_NAME, "4.2.0", ClusterType.SHARDED, 2);
      final ReplicaSetHardware hardware0 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, shardedClusterDescription_42));

      final Process process = new Process();
      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          config,
          shardedClusterDescription_42,
          hardware0,
          hardware0.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          Optional.of(customDefaultRWConcern),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertNull(process.getDefaultRWConcern());
    }

    // 4.4
    {
      final ShardedClusterDescription shardedClusterDescription_44 =
          getTestShardedCluster(TEST_CLUSTER_NAME, "4.4.0", ClusterType.SHARDED, 2);
      final ReplicaSetHardware hardware0 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, shardedClusterDescription_44));

      final Process process = new Process();
      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          config,
          shardedClusterDescription_44,
          hardware0,
          hardware0.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          Optional.of(customDefaultRWConcern),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertEquals("majority", process.getDefaultRWConcern().getDefaultWriteConcern().getW());
      assertEquals("local", process.getDefaultRWConcern().getDefaultReadConcern().getLevel());
    }
  }

  @Test
  public void testUpdateInstanceMongoSProcess_UseMongoDBVersionAndFCVFromProcess() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final CustomDefaultRWConcern customDefaultRWConcern = new CustomDefaultRWConcern();
    customDefaultRWConcern.setDefaultWriteConcern("majority");
    customDefaultRWConcern.setDefaultReadConcern("local");

    // 4.4 with 4.2 mongodb version and FCV override
    {
      final ShardedClusterDescription shardedClusterDescription_44 =
          spy(getTestShardedCluster(TEST_CLUSTER_NAME, "4.4.0", ClusterType.SHARDED, 2));
      final ReplicaSetHardware hardware0 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, shardedClusterDescription_44));
      final Process process = new Process();
      process.setVersion("4.2.0");
      process.setFeatureCompatibilityVersion("4.2");
      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          config,
          shardedClusterDescription_44,
          hardware0,
          hardware0.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          Optional.of(customDefaultRWConcern),
          new SearchMongoArgs(),
          true
              ? ClusterDeploymentProcessUtil.ProcessUpdateConsistency.SINGLE_NODE
              : ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());
      assertNull(process.getDefaultRWConcern());
      verify(shardedClusterDescription_44, never()).getMongoDBVersion();
      verify(shardedClusterDescription_44, never()).getFeatureCompatibilityVersion();
      assertEquals(shardedClusterDescription_44.getMongoDBVersion(), Version.fromString("4.4.0"));
      assertEquals(process.getVersion(), "4.2.0");
      assertEquals(process.getFeatureCompatibilityVersion(), "4.2");
    }

    // 4.4 with 4.2 FCV override
    {
      final ShardedClusterDescription shardedClusterDescription_44 =
          spy(getTestShardedCluster(TEST_CLUSTER_NAME, "4.4.0", ClusterType.SHARDED, 2));
      final ReplicaSetHardware hardware0 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, shardedClusterDescription_44));
      final Process process = new Process();
      process.setVersion("4.4.0");
      process.setFeatureCompatibilityVersion("4.2");
      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          config,
          shardedClusterDescription_44,
          hardware0,
          hardware0.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          Optional.of(customDefaultRWConcern),
          new SearchMongoArgs(),
          true
              ? ClusterDeploymentProcessUtil.ProcessUpdateConsistency.SINGLE_NODE
              : ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());
      assertNull(process.getDefaultRWConcern());
      verify(shardedClusterDescription_44, never()).getMongoDBVersion();
      verify(shardedClusterDescription_44, never()).getFeatureCompatibilityVersion();
      assertEquals(shardedClusterDescription_44.getMongoDBVersion(), Version.fromString("4.4.0"));
      assertEquals(process.getVersion(), "4.4.0");
      assertEquals(process.getFeatureCompatibilityVersion(), "4.2");
    }
  }

  @Test
  public void testUpdateWithCustomDefaultRWConcern() {
    { // updating with just read concern only updates read concern
      final DefaultRWConcern defaultRWConcern = new DefaultRWConcern();
      final CustomDefaultRWConcern customDefaultRWConcern = new CustomDefaultRWConcern();
      customDefaultRWConcern.setDefaultReadConcern("available");

      ClusterDeploymentProcessUtil.updateDefaultRWConcern(defaultRWConcern, customDefaultRWConcern);
      assertEquals("available", defaultRWConcern.getDefaultReadConcern().getLevel());
      assertNull(defaultRWConcern.getDefaultWriteConcern());
    }

    { // updating with just write concern only updates write concern
      final DefaultRWConcern defaultRWConcern = new DefaultRWConcern();
      final CustomDefaultRWConcern customDefaultRWConcern = new CustomDefaultRWConcern();
      customDefaultRWConcern.setDefaultWriteConcern("majority");

      ClusterDeploymentProcessUtil.updateDefaultRWConcern(defaultRWConcern, customDefaultRWConcern);
      assertNull(defaultRWConcern.getDefaultReadConcern());
      assertEquals("majority", defaultRWConcern.getDefaultWriteConcern().getW());
    }

    { // updating write concern with number
      final DefaultRWConcern defaultRWConcern = new DefaultRWConcern();
      final CustomDefaultRWConcern customDefaultRWConcern = new CustomDefaultRWConcern();
      customDefaultRWConcern.setDefaultWriteConcern("1");

      ClusterDeploymentProcessUtil.updateDefaultRWConcern(defaultRWConcern, customDefaultRWConcern);
      assertNull(defaultRWConcern.getDefaultReadConcern());
      assertEquals(1, defaultRWConcern.getDefaultWriteConcern().getW());
    }

    { // updating with null does nothing
      final DefaultRWConcern defaultRWConcern = new DefaultRWConcern();

      ClusterDeploymentProcessUtil.updateDefaultRWConcern(defaultRWConcern, null);
      assertNull(defaultRWConcern.getDefaultReadConcern());
      assertNull(defaultRWConcern.getDefaultWriteConcern());

      final CustomDefaultRWConcern customDefaultRWConcern = new CustomDefaultRWConcern();
      customDefaultRWConcern.setDefaultReadConcern("available");
      customDefaultRWConcern.setDefaultWriteConcern("majority");

      ClusterDeploymentProcessUtil.updateDefaultRWConcern(defaultRWConcern, customDefaultRWConcern);
      assertEquals("available", defaultRWConcern.getDefaultReadConcern().getLevel());
      assertEquals("majority", defaultRWConcern.getDefaultWriteConcern().getW());
    }
  }

  @Test
  public void testUpdateInstanceMongoDProcess_OplogMinRetentionHours() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    ClusterDescription clusterDescription =
        spy(
            getTestReplicaSet(
                TEST_CLUSTER_NAME, NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion()));

    final ReplicaSetHardware replicaSetHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, clusterDescription));

    final Deployment existingDeployment = new Deployment();
    final AutomationConfig existingConfig =
        new AutomationConfig(new ObjectId(), PublishState.PUBLISHED, existingDeployment);

    // null oplogMinRetentionHours but MongoDB version <= FOUR_FOUR_ZERO
    // should not change anything
    {
      final Process process = new Process();
      process.setName("testProcess");
      process.setVersion("4.2.0");

      ClusterDescription.Builder<?, ClusterDescription> clusterDescriptionBuilder =
          clusterDescription.copy();
      clusterDescriptionBuilder.setMongoDBVersion("4.4.3");
      final ClusterDescription updatedClusterDescription = spy(clusterDescriptionBuilder.build());

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          updatedClusterDescription,
          Collections.emptyList(),
          null,
          replicaSetHardware,
          replicaSetHardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.SINGLE_NODE,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getArgs().getOplogMinRetentionHours());
      verify(updatedClusterDescription, never()).getMongoDBVersion();
    }

    // 12 oplogMinRetentionHours but MongoDB version <= FOUR_FOUR_ZERO
    // should be set to null
    {
      final Process process = new Process();
      process.setName("testProcess");
      process.setVersion("4.2.0");

      ClusterDescription.Builder<?, ClusterDescription> clusterDescriptionBuilder =
          clusterDescription.copy();
      clusterDescriptionBuilder.setMongoDBVersion("4.4.3");
      final ClusterDescription updatedClusterDescription = spy(clusterDescriptionBuilder.build());

      final ProcessArguments2_6 processArgs = new ProcessArguments2_6();
      processArgs.setOplogMinRetentionHours(12.0);

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          updatedClusterDescription,
          Collections.emptyList(),
          null,
          replicaSetHardware,
          replicaSetHardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.SINGLE_NODE,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getArgs().getOplogMinRetentionHours());
      verify(updatedClusterDescription, never()).getMongoDBVersion();
    }

    // null oplogMinRetentionHours and new MongoDB version >= FOUR_FOUR_ZERO
    // but previous config had a version <= 4.4
    // should not change anything
    {
      final Process process = new Process();
      process.setName("testProcess");
      process.setVersion("4.4.3");

      final Process existingProcess = new Process();
      existingProcess.setName("testProcess");
      existingProcess.setVersion("4.2.1");
      existingDeployment.addProcess(existingProcess);

      ClusterDescription.Builder<?, ClusterDescription> clusterDescriptionBuilder =
          clusterDescription.copy();
      clusterDescriptionBuilder.setMongoDBVersion("4.4.3");
      final ClusterDescription updatedClusterDescription = spy(clusterDescriptionBuilder.build());

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          updatedClusterDescription,
          Collections.emptyList(),
          null,
          replicaSetHardware,
          replicaSetHardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getArgs().getOplogMinRetentionHours());

      existingProcess.setVersion("4.4.3");
    }

    // null oplogMinRetentionHours, MongoDB Version >= FOUR_FOUR_ZERO but not
    // isDiskGBEnabled - should not change anything
    {
      final Process process = new Process();
      process.setName("testProcess");

      ClusterDescription.Builder<?, ClusterDescription> clusterDescriptionBuilder =
          clusterDescription
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AWS,
                  NDSModelTestFactory.getDefaultAutoScalingForProvider(CloudProvider.AWS, false),
                  NodeTypeFamily.BASE);
      clusterDescriptionBuilder.setMongoDBVersion("4.4.0");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          clusterDescriptionBuilder.build(),
          Collections.emptyList(),
          null,
          replicaSetHardware,
          replicaSetHardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getArgs().getOplogMinRetentionHours());
    }

    // Null OplogMinRetentionHours, MongoDB Version Correct, & isDiskGBEnabled, but replica set is
    // config should not set Oplog
    {
      final Process process = new Process();
      process.setName("testProcess");

      final ShardedClusterDescription.Builder clusterDescriptionBuilder =
          getTestShardedCluster(
                  TEST_CLUSTER_NAME,
                  NDSModelTestFactory.TEST_MONGODB_VERSION.getVersion(),
                  ClusterType.SHARDED,
                  1)
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AWS,
                  NDSModelTestFactory.getDefaultAutoScalingForProvider(CloudProvider.AWS, true),
                  NodeTypeFamily.BASE);
      clusterDescriptionBuilder.setMongoDBVersion("4.4.0");

      final ShardedClusterDescription shardedClusterDescription = clusterDescriptionBuilder.build();

      final ReplicaSetHardware configReplicaSetHardware =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
                  shardedClusterDescription, List.of(containerId)));

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          shardedClusterDescription,
          Collections.emptyList(),
          null,
          configReplicaSetHardware,
          configReplicaSetHardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());
      assertNull(process.getArgs().getOplogMinRetentionHours());
    }

    // Null OplogMinRetentionHours, MongoDB Version Correct, & isDiskGBEnabled
    // should set Oplog
    {
      final Process process = new Process();
      process.setName("testProcess");

      ClusterDescription.Builder<?, ClusterDescription> clusterDescriptionBuilder =
          clusterDescription
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AWS,
                  NDSModelTestFactory.getDefaultAutoScalingForProvider(CloudProvider.AWS, true),
                  NodeTypeFamily.BASE);
      clusterDescriptionBuilder.setMongoDBVersion("4.4.0");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          clusterDescriptionBuilder.build(),
          Collections.emptyList(),
          null,
          replicaSetHardware,
          replicaSetHardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals(
          NDSDefaults.DEFAULT_OPLOG_MIN_RETENTION_HOURS,
          process.getArgs().getOplogMinRetentionHours(),
          0.001);
    }

    // Null OplogMinRetentionHours, MongoDB Version Correct, & isDiskGBEnabled but Serverless
    // MTM Holder so we are setting those args instead
    {
      final Process process = new Process();
      process.setName("testProcess");
      when(ndsGroup.isServerlessMTMHolder()).thenReturn(true);

      ClusterDescription.Builder<?, ClusterDescription> clusterDescriptionBuilder =
          clusterDescription
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AWS,
                  NDSModelTestFactory.getDefaultAutoScalingForProvider(CloudProvider.AWS, true),
                  NodeTypeFamily.BASE);
      clusterDescriptionBuilder.setMongoDBVersion("4.4.0");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          clusterDescriptionBuilder.build(),
          Collections.emptyList(),
          null,
          replicaSetHardware,
          replicaSetHardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals(
          ServerlessNDSDefaults.DEFAULT_OPLOG_MIN_RETENTION_HOURS,
          process.getArgs().getOplogMinRetentionHours(),
          0.0001);
      when(ndsGroup.isServerlessMTMHolder()).thenReturn(true);
    }

    // Group is a serverless MTM holder - should not change minimum oplog retention period when a
    // custom value has been provided
    {
      final Process process = new Process();
      process.setName("testProcess");

      final ProcessArguments2_6 processArgs = new ProcessArguments2_6();
      processArgs.setOplogMinRetentionHours(100D);

      ClusterDescription.Builder<?, ClusterDescription> clusterDescriptionBuilder =
          clusterDescription.copy();
      clusterDescriptionBuilder.setMongoDBVersion("4.4.3");
      final ClusterDescription updatedClusterDescription = spy(clusterDescriptionBuilder.build());

      doReturn(true).when(ndsGroup).isServerlessMTMHolder();
      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          updatedClusterDescription,
          Collections.emptyList(),
          null,
          replicaSetHardware,
          replicaSetHardware.getHardware().get(0),
          process,
          processArgs,
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNotEquals(
          ServerlessNDSDefaults.DEFAULT_OPLOG_MIN_RETENTION_HOURS,
          process.getArgs().getOplogMinRetentionHours(),
          0.001);
      assertEquals(100D, process.getArgs().getOplogMinRetentionHours(), 0.001);
    }

    // 12 OplogMinRetentionHours should get set unchanged
    {
      final Process process = new Process();
      final ProcessArguments2_6 processArgs = new ProcessArguments2_6();
      processArgs.setOplogMinRetentionHours(12.0);

      ClusterDescription.Builder<?, ClusterDescription> clusterDescriptionBuilder =
          clusterDescription.copy();
      clusterDescriptionBuilder.setMongoDBVersion("4.4.3");
      final ClusterDescription updatedClusterDescription = spy(clusterDescriptionBuilder.build());

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          updatedClusterDescription,
          Collections.emptyList(),
          null,
          replicaSetHardware,
          replicaSetHardware.getHardware().get(0),
          process,
          processArgs,
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals(process.getArgs().getOplogMinRetentionHours(), 12.0, 0);
    }
  }

  @Test
  public void testUpdateInstanceMongoDProcess_ConfigServer_decomposedShardedCluster() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ShardedClusterDescription clusterDescription =
        getTestShardedCluster("testCluster", "4.4", ClusterType.SHARDED, 1);
    final ReplicaSetHardware replicaSetHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
                clusterDescription, List.of(containerId)));

    // Normal cluster - no skipShardingConfigurationChecks
    {
      final Process process = new Process();
      process.setName("testProcess");

      doReturn(true).when(ndsGroup).isMTMHolder();
      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          clusterDescription,
          Collections.emptyList(),
          null,
          replicaSetHardware,
          replicaSetHardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertFalse(
          process.getArgs2_6().getSetParameterMap().containsKey("skipShardingConfigurationChecks"));
    }

    // Decomposed cluster - skipShardingConfigurationChecks = true
    {
      final Process process = new Process();
      process.setName("testProcess");

      doReturn(true).when(ndsGroup).isMTMHolder();
      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          clusterDescription
              .copy()
              .setClusterTags(Set.of(ClusterTag.DECOMPOSED_SHARDED_CLUSTER))
              .build(),
          Collections.emptyList(),
          null,
          replicaSetHardware,
          replicaSetHardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals(
          true, process.getArgs2_6().getSetParameterMap().get("skipShardingConfigurationChecks"));
    }
  }

  @Test
  public void testUpdateEncryptionAtRestSettings() {
    final AutomationConfig config = new AutomationConfig();
    final Deployment deployment = new Deployment();
    final Process process = new Process();
    config.setDeployment(deployment);
    deployment.setProcesses(List.of(process));
    process.setName("process-0");

    final ClusterDescription cd = mock(ClusterDescription.class);
    final NDSGroup group = mock(NDSGroup.class);
    final NDSEncryptionAtRest ndsGroup = mock(NDSEncryptionAtRest.class);
    final KeyManagementConfig awsKms = mock(KeyManagementConfig.class);
    final KeyManagementConfig azureKms = mock(KeyManagementConfig.class);
    final KeyManagementConfig gcpKms = mock(KeyManagementConfig.class);

    when(group.getEncryptionAtRest()).thenReturn(ndsGroup);
    when(ndsGroup.getAWSKMS()).thenReturn(awsKms);
    when(ndsGroup.getAzureKeyVault()).thenReturn(azureKms);
    when(ndsGroup.getGoogleCloudKMS()).thenReturn(gcpKms);

    final Date kmipDate = new Date();
    when(awsKms.getLastKmipMasterKeyRotation()).thenReturn(Optional.of(kmipDate));
    when(azureKms.getLastKmipMasterKeyRotation()).thenReturn(Optional.of(kmipDate));
    when(gcpKms.getLastKmipMasterKeyRotation()).thenReturn(Optional.of(kmipDate));

    // AWS Enabled
    when(awsKms.isEnabled()).thenReturn(true);
    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.AWS);

    final InstanceHardware mockInstanceHardware = mock(InstanceHardware.class);
    final ObjectId containerId = new ObjectId();
    final AWSCloudProviderContainer container = mock(AWSCloudProviderContainer.class);
    doReturn(containerId).when(container).getId();
    doReturn(containerId).when(mockInstanceHardware).getCloudContainerId();
    doReturn(Optional.of(container)).when(group).getCloudProviderContainer(containerId);
    doReturn(CloudProvider.AWS).when(container).getCloudProvider();
    doReturn(AWSRegionName.US_EAST_1).when(container).getRegion();

    final AWSKMSEARPrivateEndpoint awsPE =
        new AWSKMSEARPrivateEndpoint.Builder()
            .id(new ObjectId())
            .status(CloudProviderPrivateEndpoint.Status.ACTIVE)
            .regionName(AWSRegionName.US_EAST_1)
            .privateEndpointResourceId("endpoint resourceId")
            .needsUpdateAfter(new Date())
            .build();
    doReturn(Optional.of(awsPE)).when(awsKms).getPrivateEndpointForRegion(AWSRegionName.US_EAST_1);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    assertEquals(false, process.getKmipProxyDisabled());
    assertEquals((Integer) NDSDefaults.KMIP_PROXY_PORT, process.getKmipProxyPort());
    assertEquals(kmipDate, process.getLastKmipMasterKeyRotation());
    assertEquals(EncryptionProviderType.AWS, process.getEncryptionProviderType());
    assertEquals(AWSRegionName.US_EAST_1.getValue(), process.getEncryptionKeyRegionOverride());

    // Azure Enabled
    when(azureKms.isEnabled()).thenReturn(true);
    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.AZURE);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    assertEquals(false, process.getKmipProxyDisabled());
    assertEquals((Integer) NDSDefaults.KMIP_PROXY_PORT, process.getKmipProxyPort());
    assertEquals(kmipDate, process.getLastKmipMasterKeyRotation());
    assertEquals(EncryptionProviderType.AZURE, process.getEncryptionProviderType());

    // GCP Enabled
    when(gcpKms.isEnabled()).thenReturn(true);
    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.GCP);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    assertEquals(false, process.getKmipProxyDisabled());
    assertEquals((Integer) NDSDefaults.KMIP_PROXY_PORT, process.getKmipProxyPort());
    assertEquals(kmipDate, process.getLastKmipMasterKeyRotation());
    assertEquals(EncryptionProviderType.GCP, process.getEncryptionProviderType());

    // GCP-AWS switch
    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.AWS);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    assertEquals(false, process.getKmipProxyDisabled());
    assertEquals((Integer) NDSDefaults.KMIP_PROXY_PORT, process.getKmipProxyPort());
    assertEquals(kmipDate, process.getLastKmipMasterKeyRotation());
    assertEquals(EncryptionProviderType.AWS, process.getEncryptionProviderType());

    // AWS-Azure switch
    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.AZURE);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    assertEquals(false, process.getKmipProxyDisabled());
    assertEquals((Integer) NDSDefaults.KMIP_PROXY_PORT, process.getKmipProxyPort());
    assertEquals(kmipDate, process.getLastKmipMasterKeyRotation());
    assertEquals(EncryptionProviderType.AZURE, process.getEncryptionProviderType());

    // Azure-GCP switch
    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.GCP);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    assertEquals(false, process.getKmipProxyDisabled());
    assertEquals((Integer) NDSDefaults.KMIP_PROXY_PORT, process.getKmipProxyPort());
    assertEquals(kmipDate, process.getLastKmipMasterKeyRotation());
    assertEquals(EncryptionProviderType.GCP, process.getEncryptionProviderType());

    // AWS recently disabled
    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.AWS);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.NONE);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    assertEquals(true, process.getKmipProxyDisabled());
    assertEquals((Integer) NDSDefaults.KMIP_PROXY_PORT, process.getKmipProxyPort());
    assertNull(process.getLastKmipMasterKeyRotation());
    assertEquals(EncryptionProviderType.AWS, process.getEncryptionProviderType());

    // Azure recently disabled
    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.AZURE);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.NONE);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    assertEquals(true, process.getKmipProxyDisabled());
    assertEquals((Integer) NDSDefaults.KMIP_PROXY_PORT, process.getKmipProxyPort());
    assertNull(process.getLastKmipMasterKeyRotation());
    assertEquals(EncryptionProviderType.AZURE, process.getEncryptionProviderType());

    // GCP recently disabled
    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.GCP);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.NONE);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    assertEquals(true, process.getKmipProxyDisabled());
    assertEquals((Integer) NDSDefaults.KMIP_PROXY_PORT, process.getKmipProxyPort());
    assertNull(process.getLastKmipMasterKeyRotation());
    assertEquals(EncryptionProviderType.GCP, process.getEncryptionProviderType());

    // all providers longtime disabled
    // create a new Process instance here as this method will never explictly set the fields to
    // null, instead it ignores them when they don't need to be set
    when(cd.getEncryptionAtRestProvider()).thenReturn(EncryptionAtRestProvider.NONE);

    ClusterDeploymentProcessUtil.updateEncryptionAtRestSettings(
        config,
        cd,
        mockInstanceHardware,
        process,
        group,
        LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class));

    assertEquals(true, process.getKmipProxyDisabled());
    assertNull(process.getKmipProxyPort());
    assertNull(process.getLastKmipMasterKeyRotation());
    assertNull(process.getEncryptionProviderType());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_searchFilter_44() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_4421 = getTestReplicaSet(TEST_CLUSTER_NAME, "4.4.21");
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_4421));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_4421,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(12, SearchMongoArgs.MongotCallOptions.TCP),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertNull(process.getProfilingConfig());

    final Process process2 = new Process();
    process2.setName("process");
    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_4421,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process2,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertNull(process2.getProfilingConfig());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_searchFilter_50() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_5016 = getTestReplicaSet(TEST_CLUSTER_NAME, "5.0.16");
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_5016));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_5016,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(12, SearchMongoArgs.MongotCallOptions.TCP),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    final String expectedFilter = ClusterDeploymentProcessUtil.filterSearchQueries(12);
    assertEquals(expectedFilter, process.getProfilingConfig().getProfilingLevelFilter());
    assertEquals(0, process.getProfilingConfig().getProfilingLevel().intValue());

    final Process process2 = new Process();
    process2.setName("process");
    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_5016,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process2,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertNull(process2.getProfilingConfig());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_searchFilter_61() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_611 = getTestReplicaSet(TEST_CLUSTER_NAME, "6.1.1");
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_611));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_611,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(12, SearchMongoArgs.MongotCallOptions.TCP),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertNull(process.getProfilingConfig());

    final Process process2 = new Process();
    process2.setName("process");
    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_611,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process2,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertNull(process2.getProfilingConfig());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_searchFilter_63() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_630 = getTestReplicaSet(TEST_CLUSTER_NAME, "6.3.0");
    final ReplicaSetHardware hardware_360 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_630));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_630,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(12, SearchMongoArgs.MongotCallOptions.TCP),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    final String expectedFilter = ClusterDeploymentProcessUtil.filterSearchQueries(12);
    assertEquals(expectedFilter, process.getProfilingConfig().getProfilingLevelFilter());
    assertEquals(0, process.getProfilingConfig().getProfilingLevel().intValue());

    final Process process2 = new Process();
    process2.setName("process");
    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_630,
        Collections.emptyList(),
        null,
        hardware_360,
        hardware_360.getHardware().get(0),
        process2,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertNull(process2.getProfilingConfig());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_internal_44() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster = getTestReplicaSet(TEST_CLUSTER_NAME, "4.4.0");
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster,
        Collections.emptyList(),
        null,
        hardware,
        hardware.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(12, SearchMongoArgs.MongotCallOptions.TCP),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        true,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals(process.getArgs().getSetParameterMap().get("disableResumableRangeDeleter"), true);
    assertEquals(process.getArgs().getSetParameterMap().get("ttlMonitorEnabled"), false);
  }

  @Test
  public void testUpdateInstanceMongoDProcess_internal_50() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster = getTestReplicaSet(TEST_CLUSTER_NAME, "5.0.0");
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster,
        Collections.emptyList(),
        null,
        hardware,
        hardware.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(12, SearchMongoArgs.MongotCallOptions.TCP),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        true,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertEquals(process.getArgs().getSetParameterMap().get("disableResumableRangeDeleter"), true);
    assertEquals(process.getArgs().getSetParameterMap().get("ttlMonitorEnabled"), false);
  }

  @Test
  public void testUpdateInstanceMongoSProcess_searchFilter_44() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription_4421 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "4.4.21", ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription_4421));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_4421,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertNull(process.getProfilingConfig());

    final Process process2 = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_4421,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process2,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(12, SearchMongoArgs.MongotCallOptions.TCP),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertNull(process2.getProfilingConfig());
  }

  @Test
  public void testUpdateInstanceMongoSProcess_searchFilter_50() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription_5016 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "5.0.16", ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription_5016));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_5016,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(12, SearchMongoArgs.MongotCallOptions.TCP),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    final String expectedFilter = ClusterDeploymentProcessUtil.filterSearchQueries(12);
    assertEquals(expectedFilter, process.getProfilingConfig().getProfilingLevelFilter());
    assertNull(process.getProfilingConfig().getProfilingLevel());

    final Process process2 = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_5016,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process2,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertNull(process2.getProfilingConfig());
  }

  @Test
  public void testUpdateInstanceMongoSProcess_searchFilter_61() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription_611 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "6.1.1", ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription_611));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_611,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertNull(process.getProfilingConfig());

    final Process process2 = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_611,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process2,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(12, SearchMongoArgs.MongotCallOptions.TCP),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertNull(process2.getProfilingConfig());
  }

  @Test
  public void testUpdateInstanceMongoSProcess_searchFilter_63() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription_630 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "6.3.0", ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription_630));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_630,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(12, SearchMongoArgs.MongotCallOptions.TCP),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    final String expectedFilter = ClusterDeploymentProcessUtil.filterSearchQueries(12);
    assertEquals(expectedFilter, process.getProfilingConfig().getProfilingLevelFilter());
    assertNull(process.getProfilingConfig().getProfilingLevel());

    final Process process2 = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription_630,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process2,
        new ProcessArguments2_6(),
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertNull(process2.getProfilingConfig());
  }

  @Test
  public void testFilterSearchQueries() {
    final String filterExpr1 = ClusterDeploymentProcessUtil.filterSearchQueries(12);
    assertEquals(
        "{\"$or\":[{\"millis\":{\"$gte\":100}},{\"$and\":[{\"$sampleRate\":0.12},{\"command.pipeline.0.$search.tracking.searchTerms\":{\"$exists\":true}}]}]}",
        filterExpr1);
    final String filterExpr2 = ClusterDeploymentProcessUtil.filterSearchQueries(100);
    assertEquals(
        "{\"$or\":[{\"millis\":{\"$gte\":100}},{\"$and\":[{\"$sampleRate\":1},{\"command.pipeline.0.$search.tracking.searchTerms\":{\"$exists\":true}}]}]}",
        filterExpr2);
    final String filterExpr3 = ClusterDeploymentProcessUtil.filterSearchQueries(0);
    assertEquals(
        "{\"$or\":[{\"millis\":{\"$gte\":100}},{\"$and\":[{\"$sampleRate\":0},{\"command.pipeline.0.$search.tracking.searchTerms\":{\"$exists\":true}}]}]}",
        filterExpr3);
  }

  @Test
  public void testMergeShardWithPhasedArgs() {
    final ProcessArguments2_6 shardArgs = new ProcessArguments2_6();
    final ProcessArguments2_6 phasedArgs = new ProcessArguments2_6();

    // Shard args is empty, use phased args
    shardArgs.setSetParameterMap(new HashMap<>());
    phasedArgs.setSetParameterMap(Map.of("parameter1", "value1", "parameter2", "value2"));
    ClusterDeploymentProcessUtil.mergeShardWithPhasedArgs(shardArgs, phasedArgs, LOG);
    assertEquals(
        shardArgs.getSetParameterMap(), Map.of("parameter1", "value1", "parameter2", "value2"));

    // Phased args is empty, shard args should not be modified
    shardArgs.setSetParameterMap(Map.of("parameter1", "value1", "parameter2", "value2"));
    phasedArgs.setSetParameterMap(Map.of());
    ClusterDeploymentProcessUtil.mergeShardWithPhasedArgs(shardArgs, phasedArgs, LOG);
    assertEquals(
        shardArgs.getSetParameterMap(), Map.of("parameter1", "value1", "parameter2", "value2"));

    // Values in shardArgs should not be overwritten by values in phasedArgs
    shardArgs.setSetParameterMap(
        new HashMap<>(Map.of("parameter1", "value1", "parameter2", "value2")));
    phasedArgs.setSetParameterMap(Map.of("parameter2", "differentValue", "parameter3", "value3"));
    ClusterDeploymentProcessUtil.mergeShardWithPhasedArgs(shardArgs, phasedArgs, LOG);
    assertEquals(
        shardArgs.getSetParameterMap(),
        Map.of("parameter1", "value1", "parameter2", "value2", "parameter3", "value3"));
  }

  @Test
  public void testUpdateInstanceMongoDProcess_tcmalloc() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_80 = getTestReplicaSet(TEST_CLUSTER_NAME, "8.0.0");
    final ReplicaSetHardware hardware_80 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_80));

    final Process process = new Process();
    process.setName("process");

    ProcessArguments2_6 args = new ProcessArguments2_6();
    final ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails details =
        ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails.builder()
            .defaultStartupFCV(null)
            .clusterDescription(testCluster_80)
            .mtmClusterTypes(Collections.emptyList())
            .searchMongoArgs(new SearchMongoArgs())
            .build();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args).mongoDBVersion(Version.fromString("8.0.0")).build());
    args.getSetParameterMap().put(FieldDefs.TCMALLOC_AGGRESSIVE_MEMORY_DECOMMIT, 0);
    args.getSetParameterMap().put(FieldDefs.TCMALLOC_ENABLE_MARK_THREAD_TEMPORARILY_IDLE, false);

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_80,
        Collections.emptyList(),
        null,
        hardware_80,
        hardware_80.getHardware().get(0),
        process,
        args,
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());

    assertNull(process.getArgs2_6().getTCMallocEnableMarkThreadTemporarilyIdle());
    assertNull(process.getArgs2_6().getTCMallocAggressiveMemoryDecommit());

    final ClusterDescription testCluster_70 = getTestReplicaSet(TEST_CLUSTER_NAME, "7.0.0");
    final ReplicaSetHardware hardware_70 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_70));

    final Process process2 = new Process();
    process2.setName("process");
    ProcessArguments2_6 args2 = new ProcessArguments2_6();
    final ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails details2 =
        ClusterDeploymentProcessUtil.SetParameterMapForMongoDProcessDetails.builder()
            .defaultStartupFCV(null)
            .clusterDescription(testCluster_80)
            .mtmClusterTypes(Collections.emptyList())
            .searchMongoArgs(new SearchMongoArgs())
            .build();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
        details.toBuilder().args(args2).mongoDBVersion(Version.fromString("8.0.0")).build());
    args2.getSetParameterMap().put(FieldDefs.TCMALLOC_AGGRESSIVE_MEMORY_DECOMMIT, 0);
    args2.getSetParameterMap().put(FieldDefs.TCMALLOC_ENABLE_MARK_THREAD_TEMPORARILY_IDLE, false);

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_70,
        Collections.emptyList(),
        null,
        hardware_70,
        hardware_70.getHardware().get(0),
        process2,
        args2,
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        1L,
        IFRProcessArgs.none());
    assertFalse(process2.getArgs2_6().getTCMallocEnableMarkThreadTemporarilyIdle());
    assertTrue(process2.getArgs2_6().getTCMallocAggressiveMemoryDecommit().equals(0));
  }

  @Test
  public void testUpdateInstanceMongoSProcess_tcmalloc() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final AutomationConfig config = new AutomationConfig();

    final ObjectId containerId = new ObjectId();
    final ShardedClusterDescription testCluster_80 =
        getTestShardedCluster(TEST_CLUSTER_NAME, "8.0.0", ClusterType.SHARDED, 2);
    final ReplicaSetHardware hardware_80 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_80));

    final Process process = new Process();
    process.setName("process");

    ProcessArguments2_6 args = new ProcessArguments2_6();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args, "/tmp", Version.fromString("8.0.0"), false, false, new SearchMongoArgs());

    args.getSetParameterMap().put(FieldDefs.TCMALLOC_AGGRESSIVE_MEMORY_DECOMMIT, 0);
    args.getSetParameterMap().put(FieldDefs.TCMALLOC_ENABLE_MARK_THREAD_TEMPORARILY_IDLE, false);

    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        testCluster_80,
        hardware_80,
        hardware_80.getAllHardware().findFirst().orElseThrow(),
        process,
        args,
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());

    assertNull(process.getArgs2_6().getTCMallocEnableMarkThreadTemporarilyIdle());
    assertNull(process.getArgs2_6().getTCMallocAggressiveMemoryDecommit());

    final ClusterDescription testCluster_70 = getTestReplicaSet(TEST_CLUSTER_NAME, "7.0.0");
    final ReplicaSetHardware hardware_70 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_70));

    final Process process2 = new Process();
    process2.setName("process");
    ProcessArguments2_6 args2 = new ProcessArguments2_6();
    ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
        args2, "/tmp", Version.fromString("7.0.0"), false, false, new SearchMongoArgs());

    args2.getSetParameterMap().put(FieldDefs.TCMALLOC_AGGRESSIVE_MEMORY_DECOMMIT, 0);
    args2.getSetParameterMap().put(FieldDefs.TCMALLOC_ENABLE_MARK_THREAD_TEMPORARILY_IDLE, false);

    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        testCluster_70,
        hardware_70,
        hardware_70.getAllHardware().findFirst().orElseThrow(),
        process2,
        args2,
        ndsGroup,
        "node-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        1L,
        IFRProcessArgs.none());
    assertFalse(process2.getArgs2_6().getTCMallocEnableMarkThreadTemporarilyIdle());
    assertTrue(process2.getArgs2_6().getTCMallocAggressiveMemoryDecommit().equals(0));
  }

  @Test
  public void testGetDeploymentClusterWideConfigurations_ChangeStreamOptions()
      throws JsonProcessingException {
    final Map<String, Object> setClusterParameterMap =
        new HashMap<>(Map.of("changeStreamOptions", "someStringValue"));
    ObjectMapper objectMapper = new ObjectMapper();
    final AutomationConfig config = new AutomationConfig();

    // changeStreamOptions removed from map due to invalid version
    {
      final Cluster cluster =
          getShardedClusterForDeploymentTestsWithVersion(
              TEST_CLUSTER_NAME, 2, "5.0.0", null, false);
      final Map<String, String> result =
          ClusterDeploymentProcessUtil.getDeploymentClusterWideConfigurations(
              config, setClusterParameterMap, cluster, LOG);
      assertEquals(0, result.size());
    }

    // for sharded clusters, key in Cluster Wide Configurations is sharded cluster's deployment name
    // FCV pinned to version that does not support change stream options
    {
      final Cluster cluster =
          getShardedClusterForDeploymentTestsWithVersion("cluster0", 2, "6.0.0", "5.0", false);
      final Map<String, String> result =
          ClusterDeploymentProcessUtil.getDeploymentClusterWideConfigurations(
              config, setClusterParameterMap, cluster, LOG);
      assertEquals(0, result.size());
    }

    {
      setClusterParameterMap.put("changeStreamOptions", "someStringValue");
      final Cluster cluster =
          getShardedClusterForDeploymentTestsWithVersion("cluster0", 2, "6.0.0", null, false);
      final Map<String, String> result =
          ClusterDeploymentProcessUtil.getDeploymentClusterWideConfigurations(
              config, setClusterParameterMap, cluster, LOG);
      assertEquals(1, result.size());
      assertTrue(result.containsKey(cluster.getClusterDescription().getDeploymentClusterName()));
      assertEquals(
          objectMapper.writeValueAsString(setClusterParameterMap),
          result.get(cluster.getClusterDescription().getDeploymentClusterName()));
    }

    {
      final Cluster cluster =
          getShardedClusterForDeploymentTestsWithVersion("cluster0", 2, "6.0.0", "6.0", false);
      final Map<String, String> result =
          ClusterDeploymentProcessUtil.getDeploymentClusterWideConfigurations(
              config, setClusterParameterMap, cluster, LOG);
      assertEquals(1, result.size());
      assertTrue(result.containsKey(cluster.getClusterDescription().getDeploymentClusterName()));
      assertEquals(
          objectMapper.writeValueAsString(setClusterParameterMap),
          result.get(cluster.getClusterDescription().getDeploymentClusterName()));
    }

    // for replica sets, key in Cluster Wide Configurations is replica set id
    {
      final ClusterDescription clusterDescription =
          getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.EIGHT_ZERO_ZERO_RC0.getVersion());
      final ObjectId containerId = new ObjectId();
      final ReplicaSetHardware hardware =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, clusterDescription));

      final Cluster replicaSet =
          Cluster.getCluster(clusterDescription, Collections.singletonList(hardware));

      final Map<String, String> result =
          ClusterDeploymentProcessUtil.getDeploymentClusterWideConfigurations(
              config, setClusterParameterMap, replicaSet, LOG);
      assertEquals(1, result.size());
      assertTrue(result.containsKey(hardware.getRsId()));
      assertEquals(
          objectMapper.writeValueAsString(setClusterParameterMap), result.get(hardware.getRsId()));
    }
  }

  @Test
  public void testGetDeploymentClusterWideConfigurations_defaultMaxTimeMS()
      throws JsonProcessingException {
    final Map<String, Object> setClusterParameterMap =
        new HashMap<>(Map.of("defaultMaxTimeMS", 300));
    ObjectMapper objectMapper = new ObjectMapper();
    final AutomationConfig config = new AutomationConfig();

    // defaultMaxTimeMS removed from map due to invalid version
    {
      final ClusterDescription clusterDescription50 =
          getTestShardedCluster(
              TEST_CLUSTER_NAME, VersionUtils.FIVE_ONE_ZERO.getVersion(), ClusterType.SHARDED, 2);
      final Cluster cluster50 = mock(Cluster.class);
      doReturn(clusterDescription50).when(cluster50).getClusterDescription();

      final Map<String, String> result =
          ClusterDeploymentProcessUtil.getDeploymentClusterWideConfigurations(
              config, setClusterParameterMap, cluster50, LOG);
      assertEquals(0, result.size());
    }

    // for sharded clusters, key in Cluster Wide Configurations is sharded cluster's deployment name
    {
      // setClusterParameterMap was cleaned up
      setClusterParameterMap.put("defaultMaxTimeMS", 300);
      final Cluster cluster80 =
          getShardedClusterForDeploymentTestsWithVersion("cluster0", 2, "8.0.0", null, false);
      final Map<String, String> result =
          ClusterDeploymentProcessUtil.getDeploymentClusterWideConfigurations(
              config, setClusterParameterMap, cluster80, LOG);
      assertEquals(1, result.size());
      assertTrue(result.containsKey(cluster80.getClusterDescription().getDeploymentClusterName()));
      final Map<String, Object> expectedParameterMap =
          new HashMap<>(Map.of("defaultMaxTimeMS", Map.of("readOperations", 300)));

      assertEquals(
          objectMapper.writeValueAsString(expectedParameterMap),
          result.get(cluster80.getClusterDescription().getDeploymentClusterName()));
    }

    // for replica sets, key in Cluster Wide Configurations is replica set id
    {
      final Map<String, Object> setClusterParameterMap_Null = new HashMap<>();
      setClusterParameterMap_Null.put("defaultMaxTimeMS", null);

      final ClusterDescription clusterDescription =
          getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.EIGHT_ZERO_ZERO_RC0.getVersion());
      final ObjectId containerId = new ObjectId();
      final ReplicaSetHardware hardware =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, clusterDescription));

      final Cluster replicaSet =
          Cluster.getCluster(clusterDescription, Collections.singletonList(hardware));

      final Map<String, String> result =
          ClusterDeploymentProcessUtil.getDeploymentClusterWideConfigurations(
              config, setClusterParameterMap_Null, replicaSet, LOG);
      assertEquals(1, result.size());
      assertTrue(result.containsKey(hardware.getRsId()));
      setClusterParameterMap_Null.put("defaultMaxTimeMS", Map.of("readOperations", 0));
      assertEquals(
          objectMapper.writeValueAsString(setClusterParameterMap_Null),
          result.get(hardware.getRsId()));
    }

    // defaultMaxTimeMS removed from map due to invalid fcv version
    {
      final ClusterDescription clusterDescription73 =
          getTestShardedCluster(
                  TEST_CLUSTER_NAME,
                  VersionUtils.EIGHT_ZERO_ZERO.getVersion(),
                  ClusterType.SHARDED,
                  2)
              .copy()
              .setVersionReleaseSystem(VersionReleaseSystem.CONTINUOUS)
              .setContinuousDeliveryFCV("7.3")
              .build();
      final Cluster cluster73 = mock(Cluster.class);
      doReturn(clusterDescription73).when(cluster73).getClusterDescription();

      final Map<String, String> result =
          ClusterDeploymentProcessUtil.getDeploymentClusterWideConfigurations(
              config, setClusterParameterMap, cluster73, LOG);
      assertEquals(0, result.size());
    }

    // Return empty map when cluster is being deleted
    {
      final Cluster cluster80 =
          getShardedClusterForDeploymentTestsWithVersion("cluster0", 2, "8.0.0", null, true);
      final Map<String, String> result =
          ClusterDeploymentProcessUtil.getDeploymentClusterWideConfigurations(
              config, setClusterParameterMap, cluster80, LOG);
      assertEquals(0, result.size());
    }
  }

  /**
   * Note that redactClientLogData *must* not make into setParameters. At the time of writing this
   * option cannot be set with setParameters during initialization time, and would cause the server
   * to crash when starting.
   *
   * @see <a href="https://jira.mongodb.org/browse/HELP-64819">HELP-64819</a>
   */
  @Test
  public void updateInstanceMongoDProcess_redactClientLogData() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();

    // redactClientLogData set to true
    {
      final ClusterDescription testCluster =
          getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.SEVEN_ZERO_ZERO.getVersion())
              .copy()
              .setRedactClientLogData(true)
              .build();
      final ReplicaSetHardware hardware =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster));

      final Process process = new Process();
      process.setName("process");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getArgs2_6().getSetParameterMap().get(FieldDefs.REDACT_CLIENT_LOG_DATA));
      assertTrue(
          Optional.ofNullable(process.getArgs2_6().getSecurity())
              .map(Security::getRedactClientLogData)
              .orElse(false));
    }

    // redactClientLogData set to false
    {
      final ClusterDescription testCluster =
          getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.SEVEN_ZERO_ZERO.getVersion())
              .copy()
              .setRedactClientLogData(false)
              .build();
      final ReplicaSetHardware hardware =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster));

      final Process process = new Process();
      process.setName("process");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getArgs2_6().getSetParameterMap().get(FieldDefs.REDACT_CLIENT_LOG_DATA));
      assertFalse(process.getArgs2_6().getSecurity().getRedactClientLogData());
    }

    // omitted setting should default to false
    {
      final ClusterDescription testCluster =
          getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.SEVEN_ZERO_ZERO.getVersion())
              .copy()
              .setRedactClientLogData(null)
              .build();
      final ReplicaSetHardware hardware =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster));

      final Process process = new Process();
      process.setName("process");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getArgs2_6().getSetParameterMap().get(FieldDefs.REDACT_CLIENT_LOG_DATA));
      assertNull(
          Optional.ofNullable(process.getArgs2_6().getSecurity())
              .map(Security::getRedactClientLogData)
              .orElse(null));
    }
  }

  /**
   * Note that redactClientLogData *must* not make into setParameters. At the time of writing this
   * option cannot be set with setParameters during initialization time, and would cause the server
   * to crash when starting.
   *
   * @see <a href="https://jira.mongodb.org/browse/HELP-64819">HELP-64819</a>
   */
  @Test
  public void updateInstanceMongoSProcess_redactClientLogData() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();
    final NDSGroup ndsGroup = getMockedNDSGroup();

    // Propagate the setting through the automation config when set to true in the cluster
    // description.
    {
      final ShardedClusterDescription clusterDescription =
          getTestShardedCluster(
                  TEST_CLUSTER_NAME,
                  VersionUtils.SEVEN_ONE_ZERO.getVersion(),
                  ClusterType.SHARDED,
                  2)
              .copy()
              .setRedactClientLogData(true)
              .build();
      final ReplicaSetHardware hardware0 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, clusterDescription));

      final Process process = new Process();
      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          config,
          clusterDescription,
          hardware0,
          hardware0.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertNull(process.getArgs2_6().getSetParameterMap().get(FieldDefs.REDACT_CLIENT_LOG_DATA));
      assertTrue(
          Optional.ofNullable(process.getArgs2_6().getSecurity())
              .map(Security::getRedactClientLogData)
              .orElse(false));
    }

    // Propagate the setting through the automation config when set to false in the cluster
    // description.
    {
      final ShardedClusterDescription clusterDescription =
          getTestShardedCluster(
                  TEST_CLUSTER_NAME,
                  VersionUtils.SEVEN_ONE_ZERO.getVersion(),
                  ClusterType.SHARDED,
                  2)
              .copy()
              .setRedactClientLogData(false)
              .build();
      final ReplicaSetHardware hardware0 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, clusterDescription));

      final Process process = new Process();
      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          config,
          clusterDescription,
          hardware0,
          hardware0.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertNull(process.getArgs2_6().getSetParameterMap().get(FieldDefs.REDACT_CLIENT_LOG_DATA));
      assertFalse(
          Optional.ofNullable(process.getArgs2_6().getSecurity())
              .map(Security::getRedactClientLogData)
              .orElse(true));
    }

    // Ensure log redaction is absent from the config when the setting is omitted in the cluster
    // description.
    {
      final ShardedClusterDescription clusterDescription =
          getTestShardedCluster(
                  TEST_CLUSTER_NAME,
                  VersionUtils.SEVEN_ONE_ZERO.getVersion(),
                  ClusterType.SHARDED,
                  2)
              .copy()
              .setRedactClientLogData(null)
              .build();
      final ReplicaSetHardware hardware0 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, clusterDescription));

      final Process process = new Process();
      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          config,
          clusterDescription,
          hardware0,
          hardware0.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertNull(process.getArgs2_6().getSetParameterMap().get(FieldDefs.REDACT_CLIENT_LOG_DATA));
      assertNull(
          Optional.ofNullable(process.getArgs2_6().getSecurity())
              .map(Security::getRedactClientLogData)
              .orElse(null));
    }
  }

  @Test
  public void testUpdateInstanceMongoDProcess_cpuSocketBinding() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final ObjectId containerId = new ObjectId();

    // set cpu socket binding
    {
      final ClusterDescription testCluster =
          getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.SEVEN_ZERO_ZERO.getVersion())
              .copy()
              .setCpuSocketBinding(List.of(0, 1))
              .build();
      final ReplicaSetHardware hardware =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster));

      final Process process = new Process();
      process.setName("process");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals(List.of(0, 1), process.getCpuSocketBinding());
      assertNull(process.getNumCores());
    }

    // set cpu socket binding - existing config with different cpu socket binding
    {
      final ClusterDescription testCluster =
          getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.SEVEN_ZERO_ZERO.getVersion())
              .copy()
              .setCpuSocketBinding(List.of(3, 4))
              .build();
      final ReplicaSetHardware hardware =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster));

      final Process process = new Process();
      process.setName("process");

      final Process existingProcess = new Process();
      existingProcess.setName("process");
      existingProcess.setVersion(VersionUtils.SEVEN_ZERO_ZERO.getVersion());
      existingProcess.setCpuSocketBinding(List.of(0, 1));
      existingProcess.setNumCores(10);
      final Deployment existingDeployment = new Deployment();
      existingDeployment.setProcesses(List.of(existingProcess));
      final AutomationConfig existingConfig =
          new AutomationConfig(new ObjectId(), PublishState.PUBLISHED, existingDeployment);

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          testCluster,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals(List.of(3, 4), process.getCpuSocketBinding());
      assertNull(process.getNumCores());
    }

    // clear cpu socket binding
    {
      final ClusterDescription testCluster =
          getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.SEVEN_ZERO_ZERO.getVersion())
              .copy()
              .setCpuSocketBinding(List.of())
              .build();
      final ReplicaSetHardware hardware =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster));

      final Process process = new Process();
      process.setName("process");

      final Process existingProcess = new Process();
      existingProcess.setName("process");
      existingProcess.setVersion(VersionUtils.SEVEN_ZERO_ZERO.getVersion());
      existingProcess.setCpuSocketBinding(List.of(0, 1));
      existingProcess.setNumCores(10);
      final Deployment existingDeployment = new Deployment();
      existingDeployment.setProcesses(List.of(existingProcess));
      final AutomationConfig existingConfig =
          new AutomationConfig(new ObjectId(), PublishState.PUBLISHED, existingDeployment);

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          testCluster,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getCpuSocketBinding());
      assertEquals(
          -1,
          process.getNumCores().intValue(),
          "also need to set numCores to -1 to clear cpu socket binding");
    }

    // clear numCores if existing process's cpu socket binding is null
    {
      final ClusterDescription testCluster =
          getTestReplicaSet(TEST_CLUSTER_NAME, VersionUtils.SEVEN_ZERO_ZERO.getVersion())
              .copy()
              .build();
      final ReplicaSetHardware hardware =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, testCluster));

      final Process process = new Process();
      process.setName("process");

      final Process existingProcess = new Process();
      existingProcess.setName("process");
      existingProcess.setVersion(VersionUtils.SEVEN_ZERO_ZERO.getVersion());
      existingProcess.setNumCores(10);
      final Deployment existingDeployment = new Deployment();
      existingDeployment.setProcesses(List.of(existingProcess));

      final AutomationConfig existingConfig =
          new AutomationConfig(new ObjectId(), PublishState.PUBLISHED, existingDeployment);

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          existingConfig,
          testCluster,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getCpuSocketBinding());
      assertNull(process.getNumCores());
    }
  }

  @Test
  public void testUpdateInstanceMongoSProcess_cpuSocketBinding() {
    final NDSGroup ndsGroup = getMockedNDSGroup();
    final ObjectId containerId = new ObjectId();

    // set cpu socket binding
    {
      final ShardedClusterDescription clusterDescription =
          getTestShardedCluster(
                  TEST_CLUSTER_NAME,
                  VersionUtils.SEVEN_ONE_ZERO.getVersion(),
                  ClusterType.SHARDED,
                  2)
              .copy()
              .setCpuSocketBinding(List.of(0, 1))
              .build();
      final ReplicaSetHardware hardware0 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, clusterDescription));

      final Process process = new Process();
      process.setName("process");

      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          new AutomationConfig(),
          clusterDescription,
          hardware0,
          hardware0.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertEquals(List.of(0, 1), process.getCpuSocketBinding());
      assertNull(process.getNumCores());
    }

    // set cpu socket binding - existing config with different cpu socket binding
    {
      final ShardedClusterDescription clusterDescription =
          getTestShardedCluster(
                  TEST_CLUSTER_NAME,
                  VersionUtils.SEVEN_ONE_ZERO.getVersion(),
                  ClusterType.SHARDED,
                  2)
              .copy()
              .setCpuSocketBinding(List.of(3, 4))
              .build();
      final ReplicaSetHardware hardware0 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, clusterDescription));

      final Process process = new Process();
      process.setName("process");

      final Process existingProcess = new Process();
      existingProcess.setName("process");
      existingProcess.setVersion(VersionUtils.SEVEN_ZERO_ZERO.getVersion());
      existingProcess.setCpuSocketBinding(List.of(0, 1));
      existingProcess.setNumCores(10);
      final Deployment existingDeployment = new Deployment();
      existingDeployment.setProcesses(List.of(existingProcess));
      final AutomationConfig existingConfig =
          new AutomationConfig(new ObjectId(), PublishState.PUBLISHED, existingDeployment);

      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          existingConfig,
          clusterDescription,
          hardware0,
          hardware0.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertEquals(List.of(3, 4), process.getCpuSocketBinding());
      assertNull(process.getNumCores());
    }

    // clear cpu socket binding
    {
      final ShardedClusterDescription clusterDescription =
          getTestShardedCluster(
                  TEST_CLUSTER_NAME,
                  VersionUtils.SEVEN_ONE_ZERO.getVersion(),
                  ClusterType.SHARDED,
                  2)
              .copy()
              .setCpuSocketBinding(List.of())
              .build();
      final ReplicaSetHardware hardware0 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, clusterDescription));

      final Process process = new Process();
      process.setName("process");

      final Process existingProcess = new Process();
      existingProcess.setName("process");
      existingProcess.setVersion(VersionUtils.SEVEN_ZERO_ZERO.getVersion());
      existingProcess.setCpuSocketBinding(List.of(0, 1));
      existingProcess.setNumCores(10);
      final Deployment existingDeployment = new Deployment();
      existingDeployment.setProcesses(List.of(existingProcess));
      final AutomationConfig existingConfig =
          new AutomationConfig(new ObjectId(), PublishState.PUBLISHED, existingDeployment);

      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          existingConfig,
          clusterDescription,
          hardware0,
          hardware0.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertNull(process.getCpuSocketBinding());
      assertEquals(
          -1,
          process.getNumCores().intValue(),
          "also need to set numCores to -1 to clear cpu socket binding");
    }

    // clear numCores if existing process's cpu socket binding is null
    {
      final ShardedClusterDescription clusterDescription =
          getTestShardedCluster(
                  TEST_CLUSTER_NAME,
                  VersionUtils.SEVEN_ONE_ZERO.getVersion(),
                  ClusterType.SHARDED,
                  2)
              .copy()
              .build();
      final ReplicaSetHardware hardware0 =
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                  0, containerId, clusterDescription));

      final Process process = new Process();
      process.setName("process");

      final Process existingProcess = new Process();
      existingProcess.setName("process");
      existingProcess.setVersion(VersionUtils.SEVEN_ZERO_ZERO.getVersion());
      existingProcess.setNumCores(10);
      final Deployment existingDeployment = new Deployment();
      existingDeployment.setProcesses(List.of(existingProcess));
      final AutomationConfig existingConfig =
          new AutomationConfig(new ObjectId(), PublishState.PUBLISHED, existingDeployment);

      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          existingConfig,
          clusterDescription,
          hardware0,
          hardware0.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          Optional.empty(),
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertNull(process.getCpuSocketBinding());
      assertNull(process.getNumCores());
    }
  }

  @Test
  public void testInterpretParamResult() {
    final String clusterName = "clusterName0";

    // FCBIS field
    {
      final Logger logger = LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class);
      ClusterDeploymentProcessUtil.interpretParamResult(
          clusterName,
          new SixZeroSetParamSummary(
              Set.of("initialSyncMethod", "initialSyncSourceReadPreference")),
          logger);
      assertTrue(
          logCaptor.getWarnLogs().stream()
              .anyMatch(
                  l ->
                      l.contains(
                          "File Copy Based Initial Sync for cluster disabled due to enabled"
                              + " Encrypted At Rest")));
    }

    // NOT FCBIS field
    {
      logCaptor.clearLogs();
      final Logger logger = LoggerFactory.getLogger(ClusterDeploymentProcessUtilUnitTests.class);
      ClusterDeploymentProcessUtil.interpretParamResult(
          clusterName, new SixZeroSetParamSummary(Set.of("otherParamResult")), logger);
      assertTrue(logCaptor.getWarnLogs().isEmpty());
    }
  }

  @Test
  public void testSetSetParameterMapForMongoSProcess_sixZeroArgs() {
    // FCBIS values were removed
    {
      final Map<String, Object> parameterMap =
          new HashMap<>(
              Map.of(
                  CommonStartupSetparams.InitialSyncSource.KEY,
                  CommonStartupSetparams.InitialSyncSource.VALUE_SECONDARY_PREFERRED,
                  CommonStartupSetparams.InitialSyncMethod.KEY,
                  CommonStartupSetparams.InitialSyncMethod.VALUE_FILE_COPY_BASED));
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setSetParameterMap(parameterMap);
      final SixZeroSetParamSummary result =
          ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
              args, "/tmp", VersionUtils.SIX_TWO_ZERO, true, true, new SearchMongoArgs());
      assertEquals(
          SixZeroSetParamSummary.removed(
              Set.of(
                  CommonStartupSetparams.InitialSyncSource.KEY,
                  CommonStartupSetparams.InitialSyncMethod.KEY)),
          result);
    }

    // FCBIS values were NOT removed
    {
      final Map<String, Object> parameterMap =
          new HashMap<>(
              Map.of(
                  CommonStartupSetparams.InitialSyncSource.KEY,
                  CommonStartupSetparams.InitialSyncSource.VALUE_SECONDARY_PREFERRED,
                  CommonStartupSetparams.InitialSyncMethod.KEY,
                  CommonStartupSetparams.InitialSyncMethod.VALUE_FILE_COPY_BASED));
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setSetParameterMap(parameterMap);
      final SixZeroSetParamSummary result =
          ClusterDeploymentProcessUtil.setSetParameterMapForMongoSProcess(
              args, "/tmp", VersionUtils.SIX_TWO_ZERO, true, false, new SearchMongoArgs());
      assertEquals(SixZeroSetParamSummary.empty(), result);
    }
  }

  @Test
  public void testSetSetParameterMapForMongoDProcess_sixZeroArgs() {
    // FCBIS values were removed
    {
      final Map<String, Object> parameterMap =
          new HashMap<>(
              Map.of(
                  CommonStartupSetparams.InitialSyncSource.KEY,
                  CommonStartupSetparams.InitialSyncSource.VALUE_SECONDARY_PREFERRED,
                  CommonStartupSetparams.InitialSyncMethod.KEY,
                  CommonStartupSetparams.InitialSyncMethod.VALUE_FILE_COPY_BASED));
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setSetParameterMap(parameterMap);
      final Version version = VersionUtils.SIX_TWO_ZERO;
      final SixZeroSetParamSummary result =
          ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
              SetParameterMapForMongoDProcessDetails.builder()
                  .clusterDescription(getTestReplicaSet(TEST_CLUSTER_NAME, version.getVersion()))
                  .mtmClusterTypes(Collections.emptyList())
                  .mongoDBVersion(version)
                  .args(args)
                  .searchMongoArgs(new SearchMongoArgs())
                  .build());
      assertEquals(
          SixZeroSetParamSummary.removed(
              Set.of(
                  CommonStartupSetparams.InitialSyncSource.KEY,
                  CommonStartupSetparams.InitialSyncMethod.KEY)),
          result);
    }

    // FCBIS values were NOT removed
    {
      final Map<String, Object> parameterMap =
          new HashMap<>(
              Map.of(
                  CommonStartupSetparams.InitialSyncSource.KEY,
                  CommonStartupSetparams.InitialSyncSource.VALUE_SECONDARY_PREFERRED,
                  CommonStartupSetparams.InitialSyncMethod.KEY,
                  CommonStartupSetparams.InitialSyncMethod.VALUE_FILE_COPY_BASED));
      final ProcessArguments2_6 args = new ProcessArguments2_6();
      args.setSetParameterMap(parameterMap);
      final Version version = VersionUtils.SIX_TWO_ZERO;
      final SixZeroSetParamSummary result =
          ClusterDeploymentProcessUtil.setSetParameterMapForMongoDProcess(
              SetParameterMapForMongoDProcessDetails.builder()
                  .clusterDescription(
                      new ClusterDescription(
                          NDSModelTestFactory.getAWSNVMeClusterDescription(ObjectId.get())))
                  .mtmClusterTypes(Collections.emptyList())
                  .mongoDBVersion(version)
                  .args(args)
                  .searchMongoArgs(new SearchMongoArgs())
                  .build());
      assertEquals(SixZeroSetParamSummary.empty(), result);
    }
  }

  private Cluster getShardedClusterForDeploymentTestsWithVersion(
      final String pClusterName,
      final int pNumShards,
      final String pVersion,
      final String pFixedFCV,
      final boolean isDeleted) {
    final ObjectId containerId1 = new ObjectId();
    final ShardedClusterDescription shardedClusterDescription =
        getTestShardedCluster(
                pClusterName, pVersion, ClusterDescription.ClusterType.SHARDED, pNumShards)
            .copy()
            .setDeleteRequested(isDeleted)
            .setFixedFeatureCompatibilityVersion(
                Optional.ofNullable(pFixedFCV).map(FixedVersion::new))
            .build();

    final List<ReplicaSetHardware> replicaSets = new ArrayList<>();

    replicaSets.add(
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
                    shardedClusterDescription)
                .append(
                    "action", isDeleted ? ReplicaSetHardware.Action.DELETE.name() : Action.NONE)));

    for (int i = 0; i < pNumShards; i++) {
      replicaSets.add(
          new ReplicaSetHardware(
              ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                      i, containerId1, shardedClusterDescription, List.of())
                  .append(
                      "action",
                      isDeleted ? ReplicaSetHardware.Action.DELETE.name() : Action.NONE)));
    }

    return Cluster.getCluster(shardedClusterDescription, replicaSets);
  }

  @Test
  public void testCreateBaseMaintainedRamiConfig() {
    AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.getRamiDataCollectorPeriodInSecs()).thenReturn(30);
    when(appSettings.getRamiMitigatorPeriodInSecs()).thenReturn(35);
    ObjectId searchDeploymentId = new ObjectId("64c05471794d0257de62951d");

    AutomationConfig automationConfig = new AutomationConfig();
    assertEquals(0, automationConfig.incrementNextMaintainedRamiId());
    MaintainedRamiConfig maintainedRamiConfig =
        ClusterDeploymentProcessUtil.createBaseMaintainedRamiConfig(
            "hostname", 123, appSettings, automationConfig);
    assertEquals("hostname", maintainedRamiConfig.getHostname());
    assertEquals(1, maintainedRamiConfig.getId());
    assertEquals(30, maintainedRamiConfig.getRamiDataCollectorPeriodInSecs());
    assertEquals(35, maintainedRamiConfig.getRamiMitigatorPeriodInSecs());
    assertEquals(
        "mongodb://hostname:123/?tls=true&tlsInsecure=true", maintainedRamiConfig.getMongoUri());
  }

  @Test
  public void testCreateBaseMaintainedMongotuneConfig() {
    AppSettings appSettings = mock(AppSettings.class);
    Cluster cluster = mock(Cluster.class);
    ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(cluster.getClusterDescription()).thenReturn(clusterDescription);
    final String mongotunePwd = "some-string";
    AutomationConfig automationConfig = new AutomationConfig();
    final Auth newAuth = new Auth();
    final AuthUser mongotuneUser =
        new AuthUser.Builder(AuthUser.ADMIN_DB, AuthUser.MONGOTUNE_USER, mongotunePwd).build();
    newAuth.addAuthUser(mongotuneUser);
    final Deployment newDeployment = new Deployment();
    newDeployment.setAuth(newAuth);
    automationConfig.setDeployment(newDeployment);

    assertEquals(0, automationConfig.incrementNextMaintainedMongotuneId());
    MaintainedMongotuneConfig maintainedMongotuneConfig =
        ClusterDeploymentProcessUtil.createBaseMaintainedMongotuneConfig(
            "hostname", appSettings, new MongotuneProcessArgs(), false, automationConfig, cluster);
    assertEquals("hostname", maintainedMongotuneConfig.getHostname());
    assertEquals(1, maintainedMongotuneConfig.getId());
    assertFalse(maintainedMongotuneConfig.isDisabled());
    assertEquals("/srv/mongodb/mongotune/mongotune.log", maintainedMongotuneConfig.getLogPath());
    assertEquals(NDSDefaults.getLogRotateSettings(), maintainedMongotuneConfig.getLogRotate());
    assertEquals(
        "/var/lib/mongodb-mms-automation/mongotune.config",
        maintainedMongotuneConfig.getConfigPath());
    assertEquals(
        "/srv/mongodb/mongotune/diagnostic.data", maintainedMongotuneConfig.getFtdcDirPath());
    assertEquals(
        "/var/lib/mongodb-mms-automation/mongotune-healthcheck.json",
        maintainedMongotuneConfig.getHealthcheckFilePath());
    assertEquals(mongotunePwd, maintainedMongotuneConfig.getMongotuneUserPwd());

    String expectedRawConfig =
        new MongotuneRawConfig(
                new MongotuneRawConfigSettings(maintainedMongotuneConfig.getHostname()))
            .toYaml();

    assertEquals(expectedRawConfig, maintainedMongotuneConfig.getRawConfig());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_CustomDefaultRWConcern() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_noAlwaysManagedFlag =
        getTestReplicaSet(TEST_CLUSTER_NAME, "8.0.1");
    final ClusterDescription testCluster_withAlwaysManagedFlag =
        testCluster_noAlwaysManagedFlag
            .copy()
            .setAlwaysManagedDefaultRWConcernSince(new Date())
            .build();
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_noAlwaysManagedFlag));

    {
      final Process process = new Process();
      process.setName("process1");

      Optional<CustomDefaultRWConcern> customDefaultRWConcern = Optional.empty();

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_noAlwaysManagedFlag,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getDefaultRWConcern());

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_withAlwaysManagedFlag,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals(
          "majority", process.getDefaultRWConcern().getDefaultWriteConcern().getW().toString());
    }
    {
      final Process process = new Process();
      process.setName("process2");

      Optional<CustomDefaultRWConcern> customDefaultRWConcern =
          Optional.of(new CustomDefaultRWConcern());

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_noAlwaysManagedFlag,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertNull(process.getDefaultRWConcern().getDefaultWriteConcern());

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_withAlwaysManagedFlag,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals(
          "majority", process.getDefaultRWConcern().getDefaultWriteConcern().getW().toString());
    }
    {
      final Process process = new Process();
      process.setName("process3");

      Optional<CustomDefaultRWConcern> customDefaultRWConcern =
          Optional.of(new CustomDefaultRWConcern());
      customDefaultRWConcern.get().setDefaultWriteConcern("1");

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_noAlwaysManagedFlag,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals("1", process.getDefaultRWConcern().getDefaultWriteConcern().getW().toString());

      ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
          new AutomationConfig(),
          testCluster_withAlwaysManagedFlag,
          Collections.emptyList(),
          null,
          hardware,
          hardware.getHardware().get(0),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          LOG,
          false,
          false,
          false,
          false,
          "",
          false,
          false,
          new MaxIndexBuildMemoryRollout(false),
          1L,
          IFRProcessArgs.none());

      assertEquals("1", process.getDefaultRWConcern().getDefaultWriteConcern().getW().toString());
    }
  }

  @Test
  public void testUpdateInstanceMongoSProcess_CustomDefaultRWConcern() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_noAlwaysManagedFlag =
        getTestReplicaSet(TEST_CLUSTER_NAME, "8.0.1");
    final ClusterDescription testCluster_withAlwaysManagedFlag =
        testCluster_noAlwaysManagedFlag
            .copy()
            .setAlwaysManagedDefaultRWConcernSince(new Date())
            .build();
    final ReplicaSetHardware hardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_noAlwaysManagedFlag));

    {
      final Process process = new Process();
      process.setName("process1");

      Optional<CustomDefaultRWConcern> customDefaultRWConcern = Optional.empty();

      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          new AutomationConfig(),
          testCluster_noAlwaysManagedFlag,
          hardware,
          hardware.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertNull(process.getDefaultRWConcern());

      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          new AutomationConfig(),
          testCluster_withAlwaysManagedFlag,
          hardware,
          hardware.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertEquals(
          "majority", process.getDefaultRWConcern().getDefaultWriteConcern().getW().toString());
    }
    {
      final Process process = new Process();
      process.setName("process2");

      Optional<CustomDefaultRWConcern> customDefaultRWConcern =
          Optional.of(new CustomDefaultRWConcern());

      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          new AutomationConfig(),
          testCluster_noAlwaysManagedFlag,
          hardware,
          hardware.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertNull(process.getDefaultRWConcern().getDefaultWriteConcern());

      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          new AutomationConfig(),
          testCluster_withAlwaysManagedFlag,
          hardware,
          hardware.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertEquals(
          "majority", process.getDefaultRWConcern().getDefaultWriteConcern().getW().toString());
    }
    {
      final Process process = new Process();
      process.setName("process3");

      Optional<CustomDefaultRWConcern> customDefaultRWConcern =
          Optional.of(new CustomDefaultRWConcern());
      customDefaultRWConcern.get().setDefaultWriteConcern("1");

      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          new AutomationConfig(),
          testCluster_noAlwaysManagedFlag,
          hardware,
          hardware.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertEquals("1", process.getDefaultRWConcern().getDefaultWriteConcern().getW().toString());

      ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
          new AutomationConfig(),
          testCluster_withAlwaysManagedFlag,
          hardware,
          hardware.getAllHardware().findFirst().orElseThrow(),
          process,
          new ProcessArguments2_6(),
          ndsGroup,
          "node-0",
          customDefaultRWConcern,
          new SearchMongoArgs(),
          ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
          false,
          false,
          false,
          false,
          false,
          1L,
          IFRProcessArgs.none());

      assertEquals("1", process.getDefaultRWConcern().getDefaultWriteConcern().getW().toString());
    }
  }

  @Test
  public void testUpdateInstanceMongoSProcess_ShardedCluster_maxIncomingConnectionsMultiplier() {
    final ObjectId containerId = new ObjectId();
    final AutomationConfig config = new AutomationConfig();

    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ShardedClusterDescription shardedClusterDescription =
        getTestShardedCluster(TEST_CLUSTER_NAME, "8.0.0", ClusterType.SHARDED, 2);

    final ReplicaSetHardware hardware0 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, shardedClusterDescription));

    final Process process = new Process();
    ClusterDeploymentProcessUtil.updateInstanceMongoSProcess(
        config,
        shardedClusterDescription,
        hardware0,
        hardware0.getAllHardware().findFirst().orElseThrow(),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        "atlas-5tulik-mongos-0",
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        false,
        false,
        5L,
        IFRProcessArgs.none());

    assertEquals(
        hardware0.getHardware().get(0).getMaxIncomingConnections().get() * 5,
        process.getArgs().getMaxIncomingConnections());
  }

  @Test
  public void testUpdateInstanceMongoDProcess_ReplicaSet_maxIncomingConnectionsMultiplier() {
    final NDSGroup ndsGroup = getMockedNDSGroup();

    final ObjectId containerId = new ObjectId();
    final ClusterDescription testCluster_7016 = getTestReplicaSet(TEST_CLUSTER_NAME, "7.0.16");
    final ReplicaSetHardware hardware_7016 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, testCluster_7016));

    final Process process = new Process();
    process.setName("process");

    ClusterDeploymentProcessUtil.updateInstanceMongoDProcess(
        new AutomationConfig(),
        testCluster_7016,
        Collections.emptyList(),
        null,
        hardware_7016,
        hardware_7016.getHardware().get(0),
        process,
        new ProcessArguments2_6(),
        ndsGroup,
        Optional.empty(),
        new SearchMongoArgs(),
        ClusterDeploymentProcessUtil.ProcessUpdateConsistency.ENTIRE_CLUSTER,
        false,
        false,
        false,
        LOG,
        false,
        false,
        false,
        false,
        "",
        false,
        false,
        new MaxIndexBuildMemoryRollout(false),
        2L,
        IFRProcessArgs.none());

    assertEquals(
        hardware_7016.getHardware().get(0).getMaxIncomingConnections().get() * 2,
        process.getArgs().getMaxIncomingConnections());
  }
}
