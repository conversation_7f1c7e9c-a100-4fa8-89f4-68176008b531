package com.xgen.cloud.nds.ifr._public.svc;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.abtesting._public.model.ABTest;
import com.xgen.cloud.abtesting._public.svc.ABTestSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.deployment._public.model.SoftwareType;
import com.xgen.cloud.ifrabtest._public.model.ExperimentStatus;
import com.xgen.cloud.ifrabtest._public.model.IFRRolloutConfig;
import com.xgen.cloud.ifrabtest._public.model.IFRRolloutConfig.RolloutType;
import com.xgen.cloud.nds.ifr._public.model.IFREvent;
import com.xgen.cloud.nds.ifr._public.model.IFREvent.IFREventType;
import com.xgen.cloud.nds.project._public.model.versions.IFRState;
import com.xgen.cloud.nds.project._public.model.versions.IFRState.WaveStatus;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion.PhasedVersionFactory;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersionParameters;
import com.xgen.cloud.nds.project._public.model.versions.ReleaseMode;
import com.xgen.cloud.nds.project._public.svc.versions.PhasedVersionSvc;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.MockedStatic;

public class IFRStateSyncCronSvcUnitTests {

  private final PhasedVersionSvc _phasedVersionSvc = mock(PhasedVersionSvc.class);
  private final ABTestSvc _abTestSvc = mock(ABTestSvc.class);
  private final PhasedVersionFactory _phasedVersionFactory = new PhasedVersionFactory();
  private final IFRSvc _ifrSvc = mock(IFRSvc.class);

  private IFRStateSyncCronSvc _ifrStateSyncCronSvc;
  private MockedStatic<IFRState> _ifrStateMock;

  private final ObjectId testID = new ObjectId();

  @BeforeEach
  public void setUp() throws Exception {
    this._ifrStateSyncCronSvc =
        new IFRStateSyncCronSvc(_phasedVersionSvc, _abTestSvc, _phasedVersionFactory, _ifrSvc);

    // Mock the static method
    _ifrStateMock = mockStatic(IFRState.class);
    _ifrStateMock.when(() -> IFRState.isAllowedStateTransition(any(), any())).thenReturn(true);
  }

  @AfterEach
  public void tearDown() throws Exception {
    reset(_phasedVersionSvc, _abTestSvc, _ifrSvc);
    _ifrStateMock.close();
  }

  @Test
  public void syncIfrState() {
    var test = createABTest("syncIfrStateTest", testID, ExperimentStatus.DRAFT);

    setupDummyPhasedVersionMap();
    when(_abTestSvc.getIFRExperiments(SoftwareType.MONGODB_8_0)).thenReturn(List.of(test));

    _ifrStateSyncCronSvc.syncIfrState();
    // Assert that the function was not called, because the experiment is in Draft.
    assertDoesNotThrow(
        () -> {
          verify(_ifrSvc, never()).triggerWaveAllocationTransformation(any(), anyInt());
        });

    ABTest matchingABTest = createABTest("syncIfrStateTest", testID, ExperimentStatus.LIVE);
    // Simulate the experiment going to live
    when(_abTestSvc.getIFRExperiments(SoftwareType.MONGODB_8_0))
        .thenReturn(List.of(matchingABTest));

    _ifrStateSyncCronSvc.syncIfrState();
    // Assert the method was called to trigger wave allocation data transformation.
    assertDoesNotThrow(
        () -> {
          verify(_ifrSvc, times(1)).triggerWaveAllocationTransformation(testID, 1);
        });
  }

  @Test
  public void testIFRStateLocked() {
    // When ifrStateLocked is set to true by atlas admin
    setupIFRStateLockedPhasedVersionMap();
    var test = createABTest("syncIfrStateTest", testID, ExperimentStatus.LIVE, 2);
    when(_abTestSvc.getIFRExperiments(SoftwareType.MONGODB_8_0)).thenReturn(List.of(test));

    _ifrStateSyncCronSvc.syncIfrState();
    verify(_abTestSvc, times(1)).getIFRExperiments(SoftwareType.MONGODB_8_0);
    // No db call should be made.
    verify(_ifrSvc, never()).saveEvent(any());
    // No wave allocation transformation should be triggered.
    assertDoesNotThrow(
        () -> {
          verify(_ifrSvc, never()).triggerWaveAllocationTransformation(any(), anyInt());
        });
  }

  @Test
  public void testIfrRolloutPauseAndUnpause() {
    setupDummyPhasedVersionMap();

    var ifrConfig = mock(IFRRolloutConfig.class);
    when(ifrConfig.getIsRolloutPaused()).thenReturn(true);
    when(ifrConfig.getRolloutType()).thenReturn(RolloutType.BINARY);
    when(ifrConfig.getMongoDBVersion()).thenReturn(VersionUtils.EIGHT_ZERO_ZERO);
    when(ifrConfig.getLiveWaveNumber()).thenReturn(1);

    var test = createABTest("testIfrRolloutPause", testID, ExperimentStatus.LIVE, ifrConfig);
    when(_abTestSvc.getIFRExperiments(SoftwareType.MONGODB_8_0)).thenReturn(List.of(test));

    _ifrStateSyncCronSvc.syncIfrState();
    // Assert the method was called to trigger wave allocation data transformation.
    assertDoesNotThrow(
        () -> {
          verify(_ifrSvc, times(1)).triggerWaveAllocationTransformation(testID, 1);
        });

    // Create an argument captor to verify that isPaused is set to 'true'.
    ArgumentCaptor<PhasedVersion> phasedVersionCaptor =
        ArgumentCaptor.forClass(PhasedVersion.class);
    // We save twice because as part of the function, once before calling the S3 bucket and once
    // afterward.
    verify(_phasedVersionSvc, times(2)).save(phasedVersionCaptor.capture());
    var savedPhasedVersion = phasedVersionCaptor.getValue();
    assertTrue(savedPhasedVersion.isPaused());

    ArgumentCaptor<IFREvent> eventCaptor = ArgumentCaptor.forClass(IFREvent.class);
    verify(_ifrSvc, times(1)).saveEvent(eventCaptor.capture());
    assertNull(eventCaptor.getValue().clusterUniqueId());
    assertFalse(eventCaptor.getValue().hasOverride());
    assertEquals(testID, eventCaptor.getValue().experimentId());
    assertEquals(IFREventType.ROLLOUT_PAUSED, eventCaptor.getValue().type());
    assertEquals(1, eventCaptor.getValue().wave());

    // Unpause the experiment and sync again.
    when(_phasedVersionSvc.getCachedVersionForSoftwareTypes())
        .thenReturn(Map.of(SoftwareType.MONGODB_8_0, savedPhasedVersion));

    var newIfrConfig = mock(IFRRolloutConfig.class);
    when(newIfrConfig.getIsRolloutPaused()).thenReturn(false);
    when(newIfrConfig.getRolloutType()).thenReturn(RolloutType.BINARY);
    when(newIfrConfig.getMongoDBVersion()).thenReturn(VersionUtils.EIGHT_ZERO_ZERO);
    when(newIfrConfig.getLiveWaveNumber()).thenReturn(1);

    var test2 = createABTest("testIfrRolloutUnpause", testID, ExperimentStatus.LIVE, newIfrConfig);
    when(_abTestSvc.getIFRExperiments(SoftwareType.MONGODB_8_0)).thenReturn(List.of(test2));

    _ifrStateSyncCronSvc.syncIfrState();

    assertDoesNotThrow(
        () -> verify(_ifrSvc, times(1)).triggerWaveAllocationTransformation(testID, 1));

    verify(_phasedVersionSvc, times(3)).save(phasedVersionCaptor.capture());
    assertFalse(phasedVersionCaptor.getValue().isPaused());

    verify(_ifrSvc, times(2)).saveEvent(eventCaptor.capture());
    assertNull(eventCaptor.getValue().clusterUniqueId());
    assertFalse(eventCaptor.getValue().hasOverride());
    assertEquals(testID, eventCaptor.getValue().experimentId());
    assertEquals(IFREventType.ROLLOUT_UNPAUSED, eventCaptor.getValue().type());
    assertEquals(1, eventCaptor.getValue().wave());
  }

  @Test
  public void testChangePreviousIFRStateOnlyWhenExperimentIdMatch() {
    setupDummyPhasedVersionMap();

    var matchingIfrTest = createABTest("firstTest", testID, ExperimentStatus.LIVE);
    when(_abTestSvc.getIFRExperiments(SoftwareType.MONGODB_8_0))
        .thenReturn(List.of(matchingIfrTest));
    _ifrStateSyncCronSvc.syncIfrState();
    // We save twice because as part of the function, once before calling the S3 bucket and once
    // afterward.
    verify(_phasedVersionSvc, times(2)).save(any());

    // Assert that we don't trigger a save if the experimentId is different
    matchingIfrTest = createABTest("firstTestDiffId", new ObjectId(), ExperimentStatus.COMPLETE);
    when(_abTestSvc.getIFRExperiments(SoftwareType.MONGODB_8_0))
        .thenReturn(List.of(matchingIfrTest));
    assertThrows(IllegalArgumentException.class, () -> _ifrStateSyncCronSvc.syncIfrState());
    verify(_phasedVersionSvc, times(2)).save(any());
  }

  @Test
  public void testWaveAllocationDoesntTriggerWhenOnlyPausedChanged() throws SvcException {
    setupDummyPhasedVersionMap();

    var matchingIfrTest = createABTest("firstTest", testID, ExperimentStatus.LIVE, 1);
    when(_abTestSvc.getIFRExperiments(SoftwareType.MONGODB_8_0))
        .thenReturn(List.of(matchingIfrTest));
    _ifrStateSyncCronSvc.syncIfrState();
    verify(_ifrSvc, times(1)).triggerWaveAllocationTransformation(eq(testID), anyInt());
    // We save twice because as part of the function, once before calling the S3 bucket and once
    // afterward.
    verify(_phasedVersionSvc, times(2)).save(any());

    // Assert that we don't trigger a fetch when only the isPaused status changed.
    when(matchingIfrTest.getIFRRolloutConfig().getIsRolloutPaused()).thenReturn(true);
    setupDummyPhasedVersionMap(WaveStatus.FINALIZED, 1, ExperimentStatus.LIVE);
    _ifrStateSyncCronSvc.syncIfrState();
    verify(_ifrSvc, times(1)).triggerWaveAllocationTransformation(eq(testID), anyInt());
  }

  @Test
  public void testWaveAllocationTriggersWhenWaveChanges() throws SvcException {
    setupDummyPhasedVersionMap();

    var matchingIfrTest = createABTest("firstTest", testID, ExperimentStatus.LIVE);
    when(_abTestSvc.getIFRExperiments(SoftwareType.MONGODB_8_0))
        .thenReturn(List.of(matchingIfrTest));
    _ifrStateSyncCronSvc.syncIfrState();
    verify(_ifrSvc, times(1)).triggerWaveAllocationTransformation(eq(testID), anyInt());

    // Assert that we trigger a save if the wave number changed.
    setupDummyPhasedVersionMap(WaveStatus.FINALIZED, 1, ExperimentStatus.LIVE);
    var testNextWave = createABTest("firstTest", testID, ExperimentStatus.LIVE, 2);
    when(_abTestSvc.getIFRExperiments(SoftwareType.MONGODB_8_0)).thenReturn(List.of(testNextWave));
    _ifrStateSyncCronSvc.syncIfrState();
    verify(_ifrSvc, times(2)).triggerWaveAllocationTransformation(eq(testID), anyInt());
  }

  private void setupDummyPhasedVersionMap() {
    setupDummyPhasedVersionMap(WaveStatus.INITIALIZING, 1, ExperimentStatus.DRAFT);
  }

  private void setupIFRStateLockedPhasedVersionMap() {
    PhasedVersionParameters params =
        new PhasedVersionParameters(SoftwareType.MONGODB_8_0, "8.0")
            .setIfrState(
                new IFRState(
                    testID, ExperimentStatus.PAUSED, RolloutType.BINARY, 1, WaveStatus.FINALIZED))
            .setReleaseMode(ReleaseMode.IFR)
            .setIfrStateLocked(true);
    when(_phasedVersionSvc.getCachedVersionForSoftwareTypes())
        .thenReturn(
            Map.of(params.getSoftwareType(), _phasedVersionFactory.createPhasedVersion(params)));
  }

  private void setupDummyPhasedVersionMap(
      final WaveStatus pWaveStatus, final int wave, final ExperimentStatus pExperimentStatus) {
    PhasedVersionParameters params =
        new PhasedVersionParameters(SoftwareType.MONGODB_8_0, "8.0")
            .setIfrState(
                new IFRState(testID, pExperimentStatus, RolloutType.BINARY, wave, pWaveStatus))
            .setReleaseMode(ReleaseMode.IFR);
    when(_phasedVersionSvc.getCachedVersionForSoftwareTypes())
        .thenReturn(
            Map.of(params.getSoftwareType(), _phasedVersionFactory.createPhasedVersion(params)));
  }

  ABTest createABTest(String testName, ObjectId testID, ExperimentStatus experimentStatus) {
    IFRRolloutConfig ifrConfig = mock(IFRRolloutConfig.class);
    when(ifrConfig.getIsRolloutPaused()).thenReturn(false);
    when(ifrConfig.getMongoDBVersion()).thenReturn(VersionUtils.EIGHT_ZERO_ZERO);
    when(ifrConfig.getLiveWaveNumber()).thenReturn(1);
    return createABTest(testName, testID, experimentStatus, ifrConfig);
  }

  ABTest createABTest(
      String testName, ObjectId testID, ExperimentStatus experimentStatus, int wave) {
    IFRRolloutConfig ifrConfig = mock(IFRRolloutConfig.class);
    when(ifrConfig.getIsRolloutPaused()).thenReturn(false);
    when(ifrConfig.getMongoDBVersion()).thenReturn(VersionUtils.EIGHT_ZERO_ZERO);
    when(ifrConfig.getLiveWaveNumber()).thenReturn(wave);
    return createABTest(testName, testID, experimentStatus, ifrConfig);
  }

  // TODO: replace with proper object instantiation
  ABTest createABTest(
      String testName,
      ObjectId testID,
      ExperimentStatus experimentStatus,
      IFRRolloutConfig ifrConfig) {
    ABTest abtest = mock(ABTest.class);
    when(abtest.getTestName()).thenReturn(testName);
    when(abtest.getId()).thenReturn(testID);
    when(abtest.getExperimentStatus()).thenReturn(experimentStatus);
    when(abtest.getIFRRolloutConfig()).thenReturn(ifrConfig);
    when(ifrConfig.getRolloutType()).thenReturn(RolloutType.BINARY);
    return abtest;
  }
}
