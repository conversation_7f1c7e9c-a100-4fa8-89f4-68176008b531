package com.xgen.cloud.nds.mongotune.policies.writeblock._public.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.xgen.cloud.nds.common._public.model.INDSDefaults;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyArgVersion;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyArgs;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicySerializationContext;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyArgVersion;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockV1DefaultArgs;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockV1DefaultArgs.WriteBlockThreshold;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockV1DefaultArgs.WriteBlockThresholdType;
import java.util.Map;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DiskWriteBlockDefaultArgGeneratorSvcUnitTests {
  private static final double BYTES_IN_MB = 1024 * 1024;
  private static final double BYTES_IN_GB = 1024 * BYTES_IN_MB;

  private DiskWriteBlockDefaultArgGeneratorSvc diskWriteBlockDefaultArgGeneratorSvc;

  @BeforeEach
  public void setUp() {
    diskWriteBlockDefaultArgGeneratorSvc = Mockito.spy(new DiskWriteBlockDefaultArgGeneratorSvc());
  }

  @Test
  public void getDiskWriteBlockDefaultArgs_returnsNonEmptyMap() {
    final Map<PolicyArgVersion, PolicyArgs<? extends PolicySerializationContext>> defaultArgs =
        diskWriteBlockDefaultArgGeneratorSvc.getDiskWriteBlockDefaultArgs();

    assertNotNull(defaultArgs);
    assertNotNull(defaultArgs.get(DiskWriteBlockPolicyArgVersion.V1));
    assertEquals(DiskWriteBlockPolicyArgVersion.values().length, defaultArgs.size());
  }

  @Test
  public void verifyAllPolicyVersionsAreCovered() {
    DiskWriteBlockPolicyArgVersion[] allVersions = DiskWriteBlockPolicyArgVersion.values();

    final Map<PolicyArgVersion, PolicyArgs<? extends PolicySerializationContext>> defaultArgs =
        diskWriteBlockDefaultArgGeneratorSvc.getDiskWriteBlockDefaultArgs();

    for (DiskWriteBlockPolicyArgVersion version : allVersions) {
      assertTrue(
          defaultArgs.containsKey(version),
          "Default args map should contain an entry for version " + version);
      assertNotNull(
          defaultArgs.get(version), "Default args for version " + version + " should not be null");
    }

    assertEquals(
        allVersions.length,
        defaultArgs.size(),
        "Default args map should contain exactly one entry per policy version");
  }

  @ParameterizedTest
  @MethodSource("diskSizeScenarios")
  public void testV1BlockThresholdsForDifferentDiskSizes(
      final String testName,
      final double diskSizeGb,
      final WriteBlockThresholdType expectedBlockType,
      final WriteBlockThresholdType expectedUnblockType) {

    // Get the V1 default args
    final Map<PolicyArgVersion, PolicyArgs<? extends PolicySerializationContext>> defaultArgs =
        diskWriteBlockDefaultArgGeneratorSvc.getDiskWriteBlockDefaultArgs();
    assertTrue(defaultArgs.containsKey(DiskWriteBlockPolicyArgVersion.V1));
    final DiskWriteBlockV1DefaultArgs v1Args =
        (DiskWriteBlockV1DefaultArgs) defaultArgs.get(DiskWriteBlockPolicyArgVersion.V1);

    // Check block threshold type
    final WriteBlockThreshold blockThreshold = v1Args.getBlockThreshold(diskSizeGb);
    assertEquals(
        expectedBlockType,
        blockThreshold.type(),
        testName + " - Block threshold type should match for " + diskSizeGb + "GB disk");

    // Check unblock threshold type
    WriteBlockThreshold unblockThreshold = v1Args.getUnblockThreshold(diskSizeGb);
    assertEquals(
        expectedUnblockType,
        unblockThreshold.type(),
        testName + " - Unblock threshold type should match for " + diskSizeGb + "GB disk");
  }

  private static Stream<Arguments> diskSizeScenarios() {
    return Stream.of(
        Arguments.of(
            "Small disk", 15.0, WriteBlockThresholdType.ABSOLUTE, WriteBlockThresholdType.ABSOLUTE),
        Arguments.of(
            "Medium disk",
            500.0,
            WriteBlockThresholdType.RELATIVE,
            WriteBlockThresholdType.RELATIVE),
        Arguments.of(
            "Large disk",
            2000.0,
            WriteBlockThresholdType.ABSOLUTE,
            WriteBlockThresholdType.ABSOLUTE),
        Arguments.of(
            "At small disk boundary",
            20.0,
            WriteBlockThresholdType.RELATIVE,
            WriteBlockThresholdType.RELATIVE),
        Arguments.of(
            "At large disk boundary",
            1250.0,
            WriteBlockThresholdType.ABSOLUTE,
            WriteBlockThresholdType.ABSOLUTE));
  }

  @Test
  public void testV1DefaultArgsProperties() {
    final Map<PolicyArgVersion, PolicyArgs<? extends PolicySerializationContext>> defaultArgs =
        diskWriteBlockDefaultArgGeneratorSvc.getDiskWriteBlockDefaultArgs();
    assertTrue(defaultArgs.containsKey(DiskWriteBlockPolicyArgVersion.V1));
    final DiskWriteBlockV1DefaultArgs v1Args =
        (DiskWriteBlockV1DefaultArgs) defaultArgs.get(DiskWriteBlockPolicyArgVersion.V1);

    assertEquals(INDSDefaults.DATA_MOUNT, v1Args.fileSystemMountPoint());
    assertEquals(100, v1Args.tickIntervalMs());
    assertEquals(500, v1Args.slowTickThresholdMs());
    assertEquals(false, v1Args.dryRun());

    assertNotNull(v1Args.blockThresholdCalculator());
    assertNotNull(v1Args.unblockThresholdCalculator());
  }

  @Test
  public void testSmallDiskThresholds() {
    // Small disk scenario (under 20GB)
    final double smallDiskSize = 15.0;
    final Map<PolicyArgVersion, PolicyArgs<? extends PolicySerializationContext>> defaultArgs =
        diskWriteBlockDefaultArgGeneratorSvc.getDiskWriteBlockDefaultArgs();
    assertTrue(defaultArgs.containsKey(DiskWriteBlockPolicyArgVersion.V1));
    final DiskWriteBlockV1DefaultArgs v1Args =
        (DiskWriteBlockV1DefaultArgs) defaultArgs.get(DiskWriteBlockPolicyArgVersion.V1);

    // Block threshold
    final WriteBlockThreshold blockThreshold = v1Args.getBlockThreshold(smallDiskSize);
    assertEquals(600 * BYTES_IN_MB, blockThreshold.threshold());
    assertEquals(WriteBlockThresholdType.ABSOLUTE, blockThreshold.type());

    // Unblock threshold
    final WriteBlockThreshold unblockThreshold = v1Args.getUnblockThreshold(smallDiskSize);
    assertEquals(600 * BYTES_IN_MB * 1.5, unblockThreshold.threshold());
    assertEquals(WriteBlockThresholdType.ABSOLUTE, unblockThreshold.type());
  }

  @Test
  public void testMediumDiskThresholds() {
    // Medium disk scenario (between 20GB and 1250GB)
    final double mediumDiskSize = 500.0;
    final Map<PolicyArgVersion, PolicyArgs<? extends PolicySerializationContext>> defaultArgs =
        diskWriteBlockDefaultArgGeneratorSvc.getDiskWriteBlockDefaultArgs();
    assertTrue(defaultArgs.containsKey(DiskWriteBlockPolicyArgVersion.V1));
    final DiskWriteBlockV1DefaultArgs v1Args =
        (DiskWriteBlockV1DefaultArgs) defaultArgs.get(DiskWriteBlockPolicyArgVersion.V1);

    // Block threshold
    final WriteBlockThreshold blockThreshold = v1Args.getBlockThreshold(mediumDiskSize);
    assertEquals(1 - 0.04, blockThreshold.threshold(), 0.0001);
    assertEquals(WriteBlockThresholdType.RELATIVE, blockThreshold.type());

    // Unblock threshold
    WriteBlockThreshold unblockThreshold = v1Args.getUnblockThreshold(mediumDiskSize);
    assertEquals(1 - (0.04 * 1.5), unblockThreshold.threshold(), 0.0001);
    assertEquals(WriteBlockThresholdType.RELATIVE, unblockThreshold.type());
  }

  @Test
  public void testLargeDiskThresholds() {
    // Large disk scenario (over 1250GB)
    final double largeDiskSize = 2000.0;
    final Map<PolicyArgVersion, PolicyArgs<? extends PolicySerializationContext>> defaultArgs =
        diskWriteBlockDefaultArgGeneratorSvc.getDiskWriteBlockDefaultArgs();
    assertTrue(defaultArgs.containsKey(DiskWriteBlockPolicyArgVersion.V1));
    final DiskWriteBlockV1DefaultArgs v1Args =
        (DiskWriteBlockV1DefaultArgs) defaultArgs.get(DiskWriteBlockPolicyArgVersion.V1);

    // Block threshold
    final WriteBlockThreshold blockThreshold = v1Args.getBlockThreshold(largeDiskSize);
    assertEquals(50.0 * BYTES_IN_GB, blockThreshold.threshold());
    assertEquals(WriteBlockThresholdType.ABSOLUTE, blockThreshold.type());

    // Unblock threshold
    final WriteBlockThreshold unblockThreshold = v1Args.getUnblockThreshold(largeDiskSize);
    assertEquals(50.0 * BYTES_IN_GB * 1.5, unblockThreshold.threshold());
    assertEquals(WriteBlockThresholdType.ABSOLUTE, unblockThreshold.type());
  }
}
