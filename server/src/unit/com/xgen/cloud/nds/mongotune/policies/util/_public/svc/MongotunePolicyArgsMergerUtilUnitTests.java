package com.xgen.cloud.nds.mongotune.policies.util._public.svc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyStatus;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyType;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyArgVersion;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyStatus;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockV1DefaultArgs.FieldDefs;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.MongotuneProcessArgs;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.Test;

@SuppressWarnings("unchecked")
public class MongotunePolicyArgsMergerUtilUnitTests {

  private static final String TEST_AGENT_HOSTNAME = "test-hostname";
  private final double BYTES_IN_MB = 1024 * 1024;
  private final double BYTES_IN_GB = 1024 * BYTES_IN_MB;

  private static ClusterDescription createTestClusterDescription(double diskSizeGB) {
    ClusterDescription mockClusterDescription = mock(ClusterDescription.class);
    when(mockClusterDescription.getDiskSizeGB()).thenReturn(diskSizeGB);
    return mockClusterDescription;
  }

  private static InstanceHardware createTestInstanceHardware(
      Integer diskSizeGB, String hostnameForAgent) {
    InstanceHardware mockInstanceHardware = mock(InstanceHardware.class);
    when(mockInstanceHardware.getDiskSizeGB()).thenReturn(Optional.of(diskSizeGB));
    when(mockInstanceHardware.getHostnameForAgents()).thenReturn(Optional.of(hostnameForAgent));
    return mockInstanceHardware;
  }

  private static Cluster createTestCluster(
      String hostnameForAgent, double goalDiskSizeGB, Integer currentDiskSizeGB) {
    Cluster mockCluster = mock(Cluster.class);
    final ClusterDescription cd = createTestClusterDescription(goalDiskSizeGB);
    when(mockCluster.getClusterDescription()).thenReturn(cd);

    final InstanceHardware instanceHardware =
        createTestInstanceHardware(currentDiskSizeGB, hostnameForAgent);
    when(mockCluster.getAllProvisionedInstances()).thenReturn(List.of(instanceHardware));
    return mockCluster;
  }

  @Test
  public void getMergedPolicyArgs_withEnabledPolicy_returnsCorrectMap() {
    final MongotuneProcessArgs processArgs = MongotuneProcessArgs.builder().build();
    final Cluster cluster = createTestCluster(TEST_AGENT_HOSTNAME, 10.0, 10);

    final DiskWriteBlockPolicyStatus enabledPolicy =
        DiskWriteBlockPolicyStatus.builder()
            .setEnabled(true)
            .setEligible(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();

    final Map<PolicyType, PolicyStatus> policies = new HashMap<>();
    policies.put(PolicyType.DISK_WRITE_BLOCKING, enabledPolicy);

    final Map<String, Map<String, Object>> result =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            processArgs, policies, cluster, TEST_AGENT_HOSTNAME);

    assertNotNull(result);
    assertEquals(1, result.size());
    assertTrue(result.containsKey("disk-write-blocking"));

    final Map<String, Object> policyArgs = result.get("disk-write-blocking");
    assertNotNull(policyArgs);
    assertFalse(policyArgs.isEmpty());

    assertTrue(policyArgs.containsKey("file_system_mount_point"));
    assertTrue(policyArgs.containsKey("tick_interval_ms"));
    assertTrue(policyArgs.containsKey("slow_tick_threshold_ms"));
    assertTrue(policyArgs.containsKey("dry_run"));
    assertFalse((boolean) policyArgs.get("dry_run"));
    assertEquals(
        900 * BYTES_IN_MB,
        (long) policyArgs.get("bytes_disk_space_free_write_unblocking_threshold"),
        1);
    assertEquals(
        600 * BYTES_IN_MB,
        (long) policyArgs.get("bytes_disk_space_free_write_blocking_threshold"),
        1);
  }

  @Test
  public void getMergedPolicyArgs_withDisabledPolicy_returnsEmptyMap() {
    final MongotuneProcessArgs processArgs = MongotuneProcessArgs.builder().build();
    final Cluster cluster = createTestCluster(TEST_AGENT_HOSTNAME, 10.0, 10);

    final DiskWriteBlockPolicyStatus disabledPolicy =
        DiskWriteBlockPolicyStatus.builder()
            .setEnabled(false)
            .setEligible(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();

    final Map<PolicyType, PolicyStatus> policies = new HashMap<>();
    policies.put(PolicyType.DISK_WRITE_BLOCKING, disabledPolicy);

    final Map<String, Map<String, Object>> result =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            processArgs, policies, cluster, TEST_AGENT_HOSTNAME);

    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  public void getMergedPolicyArgs_withOverrides_mergesCorrectly() {
    final Map<String, Object> overrides = new HashMap<>();
    overrides.put("tick_interval_ms", 200);
    overrides.put("dry_run", true);
    overrides.put("file_system_mount_point", "/");

    overrides.put("foo", "bar"); // Random key to ensure unknown keys are ignored

    final MongotuneProcessArgs processArgs =
        MongotuneProcessArgs.builder().writeBlockOverrides(overrides).build();
    final Cluster cluster = createTestCluster(TEST_AGENT_HOSTNAME, 100.0, 100);

    final DiskWriteBlockPolicyStatus enabledPolicy =
        DiskWriteBlockPolicyStatus.builder()
            .setEnabled(true)
            .setEligible(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();

    final Map<PolicyType, PolicyStatus> policies = new HashMap<>();
    policies.put(PolicyType.DISK_WRITE_BLOCKING, enabledPolicy);

    final Map<String, Map<String, Object>> result =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            processArgs, policies, cluster, TEST_AGENT_HOSTNAME);

    assertNotNull(result);
    assertEquals(1, result.size());

    final Map<String, Object> policyArgs = result.get("disk-write-blocking");
    assertNotNull(policyArgs);

    // Verify overrides took precedence
    assertEquals(200, policyArgs.get("tick_interval_ms"));
    assertEquals(true, policyArgs.get("dry_run"));
    assertEquals("/", policyArgs.get("file_system_mount_point"));

    // Verify non-overridden values remain from defaults
    assertEquals(500, policyArgs.get("slow_tick_threshold_ms"));
    assertEquals(0.96, policyArgs.get("percent_disk_space_used_write_blocking_threshold"));

    // Verify unknown key was ignored
    assertFalse(policyArgs.containsKey("foo"));
  }

  @Test
  public void getMergedPolicyArgs_withNestedOverrides_mergesRecursively() {
    final Map<String, Object> nestedTarget = new HashMap<>();
    nestedTarget.put("existingField", "existingValue");
    nestedTarget.put("fieldToOverride", "originalValue");

    final Map<String, Object> target = new HashMap<>();
    target.put("tick_interval_ms", 100);
    target.put("nested", nestedTarget);

    final Map<String, Object> nestedOverrides = new HashMap<>();
    nestedOverrides.put("customField", "customValue");
    nestedOverrides.put("fieldToOverride", "overriddenValue");

    final Map<String, Object> overrides = new HashMap<>();
    overrides.put("tick_interval_ms", 300);
    overrides.put("nested", nestedOverrides);

    MongotunePolicyArgsMergerUtil.mergeMapRecursively(target, overrides);

    assertEquals(300, target.get("tick_interval_ms"));
    assertTrue(target.containsKey("nested"));
    @SuppressWarnings("unchecked")
    final Map<String, Object> nestedResult = (Map<String, Object>) target.get("nested");

    // Verify unknown nested override is ignored
    assertNull(nestedResult.get("customField"));

    // Verify existingField remains, and fieldToOverride is overridden
    assertEquals("existingValue", nestedResult.get("existingField"));
    assertEquals("overriddenValue", nestedResult.get("fieldToOverride"));
  }

  @Test
  public void getMergedPolicyArgs_withEmptyPoliciesMap_returnsEmptyMap() {
    final MongotuneProcessArgs processArgs = MongotuneProcessArgs.builder().build();
    final Cluster cluster = createTestCluster(TEST_AGENT_HOSTNAME, 100.0, 100);
    final Map<PolicyType, PolicyStatus> policies = new HashMap<>();

    final Map<String, Map<String, Object>> result =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            processArgs, policies, cluster, TEST_AGENT_HOSTNAME);

    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  public void getMergedPolicyArgs_withNullOverrides_usesDefaultsOnly() {
    final MongotuneProcessArgs processArgs =
        MongotuneProcessArgs.builder().writeBlockOverrides(null).build();
    final Cluster cluster = createTestCluster(TEST_AGENT_HOSTNAME, 100.0, 100);

    final DiskWriteBlockPolicyStatus enabledPolicy =
        DiskWriteBlockPolicyStatus.builder()
            .setEnabled(true)
            .setEligible(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();

    final Map<PolicyType, PolicyStatus> policies = new HashMap<>();
    policies.put(PolicyType.DISK_WRITE_BLOCKING, enabledPolicy);

    final Map<String, Map<String, Object>> result =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            processArgs, policies, cluster, TEST_AGENT_HOSTNAME);

    assertNotNull(result);
    assertEquals(1, result.size());

    final Map<String, Object> policyArgs = result.get("disk-write-blocking");
    assertNotNull(policyArgs);

    // Verify default values are present
    assertEquals(100, policyArgs.get("tick_interval_ms"));
    assertEquals(500, policyArgs.get("slow_tick_threshold_ms"));
    assertEquals(false, policyArgs.get("dry_run"));
  }

  @Test
  public void getMergedPolicyArgs_withNullProcessArgs_usesDefaultsOnly() {
    final MongotuneProcessArgs processArgs = null;
    final Cluster cluster = createTestCluster(TEST_AGENT_HOSTNAME, 100.0, 100);

    final DiskWriteBlockPolicyStatus enabledPolicy =
        DiskWriteBlockPolicyStatus.builder()
            .setEnabled(true)
            .setEligible(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();

    final Map<PolicyType, PolicyStatus> policies = new HashMap<>();
    policies.put(PolicyType.DISK_WRITE_BLOCKING, enabledPolicy);

    final Map<String, Map<String, Object>> result =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            processArgs, policies, cluster, TEST_AGENT_HOSTNAME);

    assertNotNull(result);
    assertEquals(1, result.size());

    final Map<String, Object> policyArgs = result.get("disk-write-blocking");
    assertNotNull(policyArgs);

    // Verify default values are present (no overrides applied)
    assertEquals(100, policyArgs.get("tick_interval_ms"));
    assertEquals(500, policyArgs.get("slow_tick_threshold_ms"));
    assertEquals(false, policyArgs.get("dry_run"));
    assertNotNull(policyArgs.get("file_system_mount_point"));
  }

  @Test
  public void getMergedPolicyArgs_withNullProcessArgsAndDisabledPolicy_returnsEmptyMap() {
    final MongotuneProcessArgs processArgs = null;
    final Cluster cluster = createTestCluster(TEST_AGENT_HOSTNAME, 100.0, 100);

    final DiskWriteBlockPolicyStatus disabledPolicy =
        DiskWriteBlockPolicyStatus.builder()
            .setEnabled(false)
            .setEligible(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();

    final Map<PolicyType, PolicyStatus> policies = new HashMap<>();
    policies.put(PolicyType.DISK_WRITE_BLOCKING, disabledPolicy);

    final Map<String, Map<String, Object>> result =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            processArgs, policies, cluster, TEST_AGENT_HOSTNAME);

    assertNotNull(result);
    assertTrue(result.isEmpty());
  }

  @Test
  public void getMergedPolicyArgs_withDifferentDiskSize_usesClusterDescriptionDiskSize() {
    final MongotuneProcessArgs processArgs = MongotuneProcessArgs.builder().build();
    final Cluster cluster = createTestCluster(TEST_AGENT_HOSTNAME, 250, 250);

    final DiskWriteBlockPolicyStatus enabledPolicy =
        DiskWriteBlockPolicyStatus.builder()
            .setEnabled(true)
            .setEligible(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();

    final Map<PolicyType, PolicyStatus> policies = new HashMap<>();
    policies.put(PolicyType.DISK_WRITE_BLOCKING, enabledPolicy);

    final Map<String, Map<String, Object>> result =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            processArgs, policies, cluster, TEST_AGENT_HOSTNAME);

    assertNotNull(result);
    assertEquals(1, result.size());

    final Map<String, Object> policyArgs = result.get("disk-write-blocking");
    assertNotNull(policyArgs);
    assertEquals(0.96, policyArgs.get("percent_disk_space_used_write_blocking_threshold"));
    assertEquals(0.94, policyArgs.get("percent_disk_space_used_write_unblocking_threshold"));
  }

  @Test
  public void mergeWithOverrides_doesNotModifyOriginalDefaultArgs() {
    final Map<String, Object> nestedLevel2 = new HashMap<>();
    nestedLevel2.put("deepValue", "original");
    nestedLevel2.put("deepNumber", 42);

    final Map<String, Object> nestedLevel1 = new HashMap<>();
    nestedLevel1.put("level2", nestedLevel2);
    nestedLevel1.put("simpleValue", "unchanged");

    final Map<String, Object> originalDefaultArgs = new HashMap<>();
    originalDefaultArgs.put("tick_interval_ms", 100);
    originalDefaultArgs.put("nested", nestedLevel1);
    originalDefaultArgs.put("topLevel", "original");

    // Create overrides that will modify nested values
    final Map<String, Object> overrideLevel2 = new HashMap<>();
    overrideLevel2.put("deepValue", "overridden");
    overrideLevel2.put(
        "newDeepValue", "added"); // This won't be merged since it doesn't exist in target

    final Map<String, Object> overrideLevel1 = new HashMap<>();
    overrideLevel1.put("level2", overrideLevel2);

    final Map<String, Object> overrides = new HashMap<>();
    overrides.put("tick_interval_ms", 200);
    overrides.put("nested", overrideLevel1);
    overrides.put("newTopLevel", "added"); // This won't be merged since it doesn't exist in target

    final MongotuneProcessArgs processArgs =
        MongotuneProcessArgs.builder().writeBlockOverrides(overrides).build();

    final Map<String, Object> result =
        MongotunePolicyArgsMergerUtil.mergeWithOverrides(
            originalDefaultArgs, processArgs, PolicyType.DISK_WRITE_BLOCKING);

    // Verify original top-level defaultArgs were not modified
    assertEquals(100, originalDefaultArgs.get("tick_interval_ms"));
    assertEquals("original", originalDefaultArgs.get("topLevel"));
    assertFalse(originalDefaultArgs.containsKey("newTopLevel"));

    // Verify nested objects in original were not modified
    final Map<String, Object> originalNestedAfter =
        (Map<String, Object>) originalDefaultArgs.get("nested");
    final Map<String, Object> originalLevel2After =
        (Map<String, Object>) originalNestedAfter.get("level2");

    assertEquals("original", originalLevel2After.get("deepValue"));
    assertEquals(42, originalLevel2After.get("deepNumber"));
    assertFalse(originalLevel2After.containsKey("newDeepValue"));

    // Verify result has the merged values
    assertEquals(200, result.get("tick_interval_ms"));
    assertEquals("original", result.get("topLevel")); // Should remain from original
    assertFalse(
        result.containsKey("newTopLevel")); // Should NOT be added (key doesn't exist in target)

    // Verify result has different object references (deep copy)
    final Map<String, Object> resultNested = (Map<String, Object>) result.get("nested");
    final Map<String, Object> resultLevel2 = (Map<String, Object>) resultNested.get("level2");

    assertEquals("overridden", resultLevel2.get("deepValue"));
    assertEquals(42, resultLevel2.get("deepNumber"));
    assertFalse(resultLevel2.containsKey("newDeepValue"));
    assertEquals("unchanged", resultNested.get("simpleValue"));
  }

  @Test
  public void getMergedPolicyArgs_postProcessingDeduplicatesThresholds() {
    final Cluster cluster = createTestCluster(TEST_AGENT_HOSTNAME, 100, 100);
    final DiskWriteBlockPolicyStatus enabledPolicy =
        DiskWriteBlockPolicyStatus.builder()
            .setEnabled(true)
            .setEligible(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();
    final Map<PolicyType, PolicyStatus> policies = new HashMap<>();
    policies.put(PolicyType.DISK_WRITE_BLOCKING, enabledPolicy);

    // Test 1: Relative overrides should remove absolute thresholds from result
    final Map<String, Object> relativeOverrides = new HashMap<>();
    relativeOverrides.put(FieldDefs.PERCENT_DISK_USED_BLOCKING_THRESHOLD, 0.95);
    relativeOverrides.put(FieldDefs.PERCENT_DISK_USED_UNBLOCKING_THRESHOLD, 0.85);

    final MongotuneProcessArgs relativeProcessArgs =
        MongotuneProcessArgs.builder().writeBlockOverrides(relativeOverrides).build();

    final Map<String, Map<String, Object>> relativeResult =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            relativeProcessArgs, policies, cluster, TEST_AGENT_HOSTNAME);

    final Map<String, Object> relativePolicyArgs = relativeResult.get("disk-write-blocking");
    assertTrue(relativePolicyArgs.containsKey(FieldDefs.PERCENT_DISK_USED_BLOCKING_THRESHOLD));
    assertTrue(relativePolicyArgs.containsKey(FieldDefs.PERCENT_DISK_USED_UNBLOCKING_THRESHOLD));
    assertFalse(relativePolicyArgs.containsKey(FieldDefs.FREE_BYTES_BLOCKING_THRESHOLD));
    assertFalse(relativePolicyArgs.containsKey(FieldDefs.FREE_BYTES_UNBLOCKING_THRESHOLD));

    // Test 2: Absolute overrides should remove relative thresholds from result
    final Map<String, Object> absoluteOverrides = new HashMap<>();
    absoluteOverrides.put(FieldDefs.FREE_BYTES_BLOCKING_THRESHOLD, 1000000000L);
    absoluteOverrides.put(FieldDefs.FREE_BYTES_UNBLOCKING_THRESHOLD, 2000000000L);

    final MongotuneProcessArgs absoluteProcessArgs =
        MongotuneProcessArgs.builder().writeBlockOverrides(absoluteOverrides).build();

    final Map<String, Map<String, Object>> absoluteResult =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            absoluteProcessArgs, policies, cluster, TEST_AGENT_HOSTNAME);

    final Map<String, Object> absolutePolicyArgs = absoluteResult.get("disk-write-blocking");
    assertTrue(absolutePolicyArgs.containsKey(FieldDefs.FREE_BYTES_BLOCKING_THRESHOLD));
    assertTrue(absolutePolicyArgs.containsKey(FieldDefs.FREE_BYTES_UNBLOCKING_THRESHOLD));
    assertFalse(absolutePolicyArgs.containsKey(FieldDefs.PERCENT_DISK_USED_BLOCKING_THRESHOLD));
    assertFalse(absolutePolicyArgs.containsKey(FieldDefs.PERCENT_DISK_USED_UNBLOCKING_THRESHOLD));
  }

  @Test
  public void getMergedPolicyArgs_useCurrentDiskSize_returnsCorrectMap() {
    final MongotuneProcessArgs processArgs = MongotuneProcessArgs.builder().build();
    // CD has 300GB but current instance hardware has 10GB
    // the arg should be generated based on the current disk size
    final Cluster cluster = createTestCluster(TEST_AGENT_HOSTNAME, 300.0, 10);

    final DiskWriteBlockPolicyStatus enabledPolicy =
        DiskWriteBlockPolicyStatus.builder()
            .setEnabled(true)
            .setEligible(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();

    final Map<PolicyType, PolicyStatus> policies = new HashMap<>();
    policies.put(PolicyType.DISK_WRITE_BLOCKING, enabledPolicy);

    final Map<String, Map<String, Object>> result =
        MongotunePolicyArgsMergerUtil.getMergedPolicyArgsForHost(
            processArgs, policies, cluster, TEST_AGENT_HOSTNAME);

    assertNotNull(result);
    assertEquals(1, result.size());
    assertTrue(result.containsKey("disk-write-blocking"));

    final Map<String, Object> policyArgs = result.get("disk-write-blocking");
    assertNotNull(policyArgs);
    assertFalse(policyArgs.isEmpty());

    assertTrue(policyArgs.containsKey("file_system_mount_point"));
    assertTrue(policyArgs.containsKey("tick_interval_ms"));
    assertTrue(policyArgs.containsKey("slow_tick_threshold_ms"));
    assertTrue(policyArgs.containsKey("dry_run"));
    assertFalse((boolean) policyArgs.get("dry_run"));
    assertEquals(
        900 * BYTES_IN_MB,
        (long) policyArgs.get("bytes_disk_space_free_write_unblocking_threshold"),
        1);
    assertEquals(
        600 * BYTES_IN_MB,
        (long) policyArgs.get("bytes_disk_space_free_write_blocking_threshold"),
        1);
  }
}
