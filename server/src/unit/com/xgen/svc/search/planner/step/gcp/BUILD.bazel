load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*UnitTests.java"]),
    deny_warnings = True,
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/capacity/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_public/model",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_public/model",
        "//server/src/main/com/xgen/cloud/nds/vmimage",
        "//server/src/main/com/xgen/cloud/search/decoupled/cloudprovider",
        "//server/src/main/com/xgen/cloud/search/decoupled/config",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/unit/com/xgen/cloud/search/decoupled/config/_public/model",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)
