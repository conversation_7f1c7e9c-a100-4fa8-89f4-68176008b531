package com.xgen.svc.mms.misc;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.xgen.cloud.billingplatform.model.sku._public.model.PricingConsumers;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuPricing;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.pricing._public.svc.SkuPricingSvc;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public final class ExportSKURegionPricingToolUnitTests {

  @Mock private SkuPricingSvc skuPricingSvc;
  @Mock private SkuPricing mockSkuPricing;
  @InjectMocks private ExportSKURegionPricingTool pricingTool;

  @BeforeEach
  void setUp() {
    Map<SKU, SkuPricing> mockPricingMap =
        Stream.of(SKU.values())
            .collect(Collectors.toMap(Function.identity(), (sku) -> mockSkuPricing));
    when(skuPricingSvc.getPricing(anyList(), any(Date.class), any(PricingConsumers.class)))
        .thenReturn(mockPricingMap);

    // Set up mock behavior for SkuPricing
    when(mockSkuPricing.hasRegionUnitPriceDollars(any(RegionName.class))).thenReturn(true);
    when(mockSkuPricing.getRegionUnitPriceDollars(any(RegionName.class), any(SKU.class)))
        .thenReturn(0.10);
    when(mockSkuPricing.hasTieredRegionUnitPriceDollars(any(RegionName.class))).thenReturn(true);
    when(mockSkuPricing.getPricingTiers()).thenReturn(3);
    when(mockSkuPricing.getTieredRegionUnitPriceDollars(
            any(RegionName.class), any(Integer.class), any(SKU.class)))
        .thenReturn(0.05);
  }

  @Test
  public void getRegionPrices_withEmptyOptional_returnsAllRegionPrices() {
    // When
    String result = pricingTool.getRegionPrices(Optional.empty());

    // Then
    assertNotNull(result);
    assertFalse(result.isEmpty());

    // Parse JSON and verify structure
    Gson gson = new Gson();
    JsonArray pricesArray = gson.fromJson(result, JsonArray.class);
    assertNotNull(pricesArray);
    assertFalse(pricesArray.isEmpty());

    // Verify we have prices from multiple cloud providers
    Set<String> providers = new HashSet<>();
    Set<String> regions = new HashSet<>();
    Set<String> skus = new HashSet<>();

    for (JsonElement element : pricesArray) {
      JsonObject priceObj = element.getAsJsonObject();
      validatePriceJsonStructure(priceObj);
      providers.add(priceObj.get("provider").getAsString());
      regions.add(priceObj.get("region").getAsString());
      skus.add(priceObj.get("sku").getAsString());
    }

    // Should have prices from all three cloud providers
    assertTrue(providers.contains("AWS"));
    assertTrue(providers.contains("GCP"));
    assertTrue(providers.contains("AZURE"));

    // Should have multiple regions
    assertTrue(regions.size() > 1);
    assertTrue(
        skus.stream()
            .allMatch(sku -> Arrays.stream(SKU.values()).anyMatch(v -> v.name().equals(sku))));
  }

  @Test
  public void getRegionPrices_withSpecificRegion_returnsOnlyThatRegion() {
    // Given
    RegionName testRegion = AWSRegionName.US_EAST_1;

    // When
    String result = pricingTool.getRegionPrices(Optional.of(testRegion));

    // Then
    assertNotNull(result);

    // Parse JSON and verify only the specified region is included
    Gson gson = new Gson();
    JsonArray pricesArray = gson.fromJson(result, JsonArray.class);
    assertNotNull(pricesArray);
    Set<String> regions = new HashSet<>();

    for (JsonElement element : pricesArray) {
      JsonObject priceObj = element.getAsJsonObject();
      validatePriceJsonStructure(priceObj);

      String region = priceObj.get("region").getAsString();
      String provider = priceObj.get("provider").getAsString();

      assertEquals(testRegion.getName(), region);
      assertEquals(testRegion.getProviderName(), provider);
      regions.add(region);
    }
    assertEquals(1, regions.size());
    assertTrue(regions.contains(testRegion.getName()));
  }

  @Test
  public void getRegionPrices_jsonStructure_isValid() {
    // When
    String result = pricingTool.getRegionPrices(Optional.empty());

    // Then
    assertNotNull(result);

    // Parse JSON to verify it's valid JSON
    Gson gson = new Gson();
    JsonArray pricesArray = gson.fromJson(result, JsonArray.class);
    assertNotNull(pricesArray);

    // Verify each price object has the expected structure
    boolean foundUnitPrice = false;
    boolean foundTieredPrice = false;

    for (JsonElement element : pricesArray) {
      JsonObject priceObj = element.getAsJsonObject();
      validatePriceJsonStructure(priceObj);

      if (priceObj.has("unitPrice")) {
        foundUnitPrice = true;
        assertTrue(priceObj.get("unitPrice").getAsDouble() >= 0);
      }

      if (priceObj.has("tieredPrices")) {
        foundTieredPrice = true;
        JsonArray tieredPrices = priceObj.getAsJsonArray("tieredPrices");
        assertNotNull(tieredPrices);
        assertFalse(tieredPrices.isEmpty());

        // Verify all tiered prices are non-negative numbers
        for (JsonElement priceElement : tieredPrices) {
          assertTrue(priceElement.getAsDouble() >= 0);
        }
      }
    }

    // Should find both types of pricing in the results
    assertTrue(foundUnitPrice, "Should find at least one SKU with unit pricing");
    assertTrue(foundTieredPrice, "Should find at least one SKU with tiered pricing");
  }

  /** Validates that a price JSON object has the expected structure. */
  private void validatePriceJsonStructure(JsonObject priceObj) {
    // Required fields
    assertTrue(priceObj.has("sku"), "Price object should have 'sku' field");
    assertTrue(priceObj.has("provider"), "Price object should have 'provider' field");
    assertTrue(priceObj.has("region"), "Price object should have 'region' field");

    // Should have either unitPrice or tieredPrices (or both)
    assertTrue(
        priceObj.has("unitPrice") || priceObj.has("tieredPrices"),
        "Price object should have either 'unitPrice' or 'tieredPrices' field");

    // Validate field types
    assertNotNull(priceObj.get("sku").getAsString());
    assertNotNull(priceObj.get("provider").getAsString());
    assertNotNull(priceObj.get("region").getAsString());

    // Validate provider is one of the expected values
    String provider = priceObj.get("provider").getAsString();
    assertNotNull(CloudProvider.findByNameIgnoreCase(provider), "Invalid provider: " + provider);
  }
}
