package com.xgen.svc.mms.svc.prometheus;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.atm.core._public.svc.AutomationAgentSupportSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfo._public.model.EventSource;
import com.xgen.cloud.common.constants._public.model.user.UserApiKeyType;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._private.dao.GroupDaoT;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.PrometheusConfig;
import com.xgen.svc.nds.svc.NDSPrivateLinkSvc;
import com.xgen.svc.nds.svc.NDSPrometheusEndpointSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class MetricsPrometheusIntegrationSvcUnitTests {

  @Mock private GroupDao _groupDao;
  @Mock private GroupDaoT _groupDaoT;
  @Mock private AuditSvc _auditSvc;
  @Mock private AppSettings _appSettings;
  @Mock private NDSGroupSvc _ndsGroupSvc;
  @Mock private AutomationAgentSupportSvc _automationAgentSupportSvc;
  @Mock private NDSPrivateLinkSvc _ndsPrivateLinkSvc;
  @Mock private MetricsDisablePrometheusIntegrationSvc _metricsDisablePrometheusIntegrationSvc;
  @Mock private NDSPrometheusEndpointSvc _ndsPrometheusEndpointSvc;

  private MetricsPrometheusIntegrationSvc _service;
  private ObjectId _groupId;
  private Group _group;
  private PrometheusConfig _prometheusConfig;
  private MockedStatic<FeatureFlagSvc> _featureFlagSvcMock;

  @BeforeEach
  public void setUp() {
    _service =
        new MetricsPrometheusIntegrationSvc(
            _groupDao,
            _groupDaoT,
            _auditSvc,
            _appSettings,
            _ndsGroupSvc,
            _automationAgentSupportSvc,
            _ndsPrivateLinkSvc,
            _metricsDisablePrometheusIntegrationSvc,
            _ndsPrometheusEndpointSvc);

    _groupId = new ObjectId();
    _group = new Group();
    _group.setId(_groupId);

    _prometheusConfig =
        new PrometheusConfig.Builder()
            .enabled(true)
            .username("testuser")
            .passwordHash("hash")
            .passwordSalt("salt")
            .scheme("http")
            .listenAddress("127.0.0.1:9090")
            .metricsPath("/metrics")
            .serviceDiscovery("http")
            .build();

    _group.setPromConfig(_prometheusConfig);
  }

  @AfterEach
  public void tearDown() {
    if (_featureFlagSvcMock != null) {
      _featureFlagSvcMock.close();
    }
  }

  private void setPromIntegrationAndPrivateLinkFeatureFlags(final boolean pEnabled) {
    _featureFlagSvcMock = Mockito.mockStatic(FeatureFlagSvc.class);
    _featureFlagSvcMock
        .when(
            () -> isFeatureFlagEnabled(eq(FeatureFlag.PROMETHEUS_INTEGRATION), any(), any(), any()))
        .thenReturn(pEnabled);
    _featureFlagSvcMock
        .when(
            () ->
                isFeatureFlagEnabled(
                    eq(FeatureFlag.PROMETHEUS_OVER_PRIVATE_LINK), any(), any(), any()))
        .thenReturn(pEnabled);
  }

  @Test
  public void testSetPrometheusNeedsSync() {
    // Test setting needsSync field
    {
      final Date syncDate = new Date();
      when(_groupDao.findById(_groupId)).thenReturn(_group);

      _service.setPrometheusNeedsSync(_groupId, syncDate);

      final ArgumentCaptor<PrometheusConfig> configCaptor =
          ArgumentCaptor.forClass(PrometheusConfig.class);
      verify(_groupDao).updatePromConfig(eq(_groupId), configCaptor.capture());

      final PrometheusConfig updatedConfig = configCaptor.getValue();
      assertEquals(updatedConfig.getNeedsSync(), syncDate);
      // Verify other fields are preserved
      assertTrue(updatedConfig.isEnabled());
      assertEquals("testuser", updatedConfig.getUsername());
    }

    // Test with null group
    {
      reset(_groupDao); // Reset mock to clear previous interactions
      when(_groupDao.findById(_groupId)).thenReturn(null);

      _service.setPrometheusNeedsSync(_groupId, new Date());

      verify(_groupDao, never()).updatePromConfig(any(), any());
    }

    // Test with group without Prometheus integration
    {
      reset(_groupDao); // Reset mock to clear previous interactions
      final Group groupWithoutProm = new Group();
      groupWithoutProm.setId(_groupId);
      when(_groupDao.findById(_groupId)).thenReturn(groupWithoutProm);

      _service.setPrometheusNeedsSync(_groupId, new Date());

      verify(_groupDao, never()).updatePromConfig(any(), any());
    }
  }

  @Test
  public void testClearPrometheusNeedsSync() {
    // Test clearing needsSync field
    {
      when(_groupDao.findById(_groupId)).thenReturn(_group);

      _service.clearPrometheusNeedsSync(_groupId);

      final ArgumentCaptor<PrometheusConfig> configCaptor =
          ArgumentCaptor.forClass(PrometheusConfig.class);
      verify(_groupDao).updatePromConfig(eq(_groupId), configCaptor.capture());

      final PrometheusConfig updatedConfig = configCaptor.getValue();
      assertNull(updatedConfig.getNeedsSync());
      // Verify other fields are preserved
      assertTrue(updatedConfig.isEnabled());
      assertEquals("testuser", updatedConfig.getUsername());
    }
  }

  @Test
  public void testConfigurationRequiresInfrastructureSync() {
    // Case: All conditions met - should return true
    {
      final Date syncDate = new Date();
      final PrometheusConfig configWithSync =
          new PrometheusConfig.Builder()
              .enabled(true)
              .username("testuser")
              .isViaPrivateEndpoint(true)
              .needsSync(syncDate)
              .build();

      final Group groupWithSync = new Group();
      groupWithSync.setId(_groupId);
      groupWithSync.setPromConfig(configWithSync);

      when(_groupDao.findById(_groupId)).thenReturn(groupWithSync);

      final boolean result = _service.configurationRequiresInfrastructureSync(_groupId);

      assertTrue(result);
    }

    // Case: No needsSync field - should return false
    {
      final PrometheusConfig configWithoutSync =
          new PrometheusConfig.Builder()
              .enabled(true)
              .username("testuser")
              .isViaPrivateEndpoint(true)
              .needsSync(null) // No sync needed
              .build();

      final Group groupWithoutSync = new Group();
      groupWithoutSync.setId(_groupId);
      groupWithoutSync.setPromConfig(configWithoutSync);

      when(_groupDao.findById(_groupId)).thenReturn(groupWithoutSync);

      final boolean result = _service.configurationRequiresInfrastructureSync(_groupId);

      assertFalse(result);
    }

    // Case: isViaPrivateEndpoint is false - should return false
    {
      final Date syncDate = new Date();
      final PrometheusConfig configNotPrivate =
          new PrometheusConfig.Builder()
              .enabled(true)
              .username("testuser")
              .isViaPrivateEndpoint(false) // Not using private networking
              .needsSync(syncDate)
              .build();

      final Group groupNotPrivate = new Group();
      groupNotPrivate.setId(_groupId);
      groupNotPrivate.setPromConfig(configNotPrivate);

      when(_groupDao.findById(_groupId)).thenReturn(groupNotPrivate);

      final boolean result = _service.configurationRequiresInfrastructureSync(_groupId);

      assertFalse(result);
    }

    // Case: isViaPrivateEndpoint is null - should return false
    {
      final Date syncDate = new Date();
      final PrometheusConfig configNullPrivate =
          new PrometheusConfig.Builder()
              .enabled(true)
              .username("testuser")
              // isViaPrivateEndpoint not set (null)
              .needsSync(syncDate)
              .build();

      final Group groupNullPrivate = new Group();
      groupNullPrivate.setId(_groupId);
      groupNullPrivate.setPromConfig(configNullPrivate);

      when(_groupDao.findById(_groupId)).thenReturn(groupNullPrivate);

      final boolean result = _service.configurationRequiresInfrastructureSync(_groupId);

      assertFalse(result);
    }

    // Case: No Prometheus integration - should return false
    {
      final Group groupWithoutPrometheus = new Group();
      groupWithoutPrometheus.setId(_groupId);
      // No Prometheus config set

      when(_groupDao.findById(_groupId)).thenReturn(groupWithoutPrometheus);

      final boolean result = _service.configurationRequiresInfrastructureSync(_groupId);

      assertFalse(result);
    }

    // Case: Group doesn't exist - should return false
    {
      when(_groupDao.findById(_groupId)).thenReturn(null);

      final boolean result = _service.configurationRequiresInfrastructureSync(_groupId);

      assertFalse(result);
    }
  }

  @Test
  public void testUnsetPrometheusNeedsSync() {
    final Date originalSyncDate = new Date();

    // Test successful atomic unset when date matches
    {
      when(_groupDaoT.unsetPromConfigNeedsSyncIfMatches(_groupId, originalSyncDate))
          .thenReturn(true);

      _service.unsetPrometheusNeedsSync(_groupId, originalSyncDate);

      verify(_groupDaoT).unsetPromConfigNeedsSyncIfMatches(_groupId, originalSyncDate);
    }

    // Test no-op when date doesn't match (race condition prevented)
    {
      reset(_groupDaoT); // Reset mock to clear previous interactions
      when(_groupDaoT.unsetPromConfigNeedsSyncIfMatches(_groupId, originalSyncDate))
          .thenReturn(false);

      _service.unsetPrometheusNeedsSync(_groupId, originalSyncDate);

      verify(_groupDaoT).unsetPromConfigNeedsSyncIfMatches(_groupId, originalSyncDate);
      // Should not call the old updatePromConfig method
      verify(_groupDao, never()).updatePromConfig(any(), any());
    }
  }

  @Test
  public void testDeletePromConfigAndPlan_AtlasGroupWithPrivateEndpoints() {
    final AuditInfo auditInfo =
        new AuditInfo("127.0.0.1", "clientId", UserApiKeyType.NONE, EventSource.USER);
    final PrometheusConfig configWithPrivateEndpoints =
        new PrometheusConfig.Builder()
            .enabled(true)
            .username("testuser")
            .selectedPrivateEndpoints(List.of("endpoint1", "endpoint2"))
            .build();

    final Group atlasGroup = spy(new Group());
    atlasGroup.setId(_groupId);
    atlasGroup.setPromConfig(configWithPrivateEndpoints);
    when(atlasGroup.isAtlas()).thenReturn(true);

    setPromIntegrationAndPrivateLinkFeatureFlags(true);
    _service.deletePromConfigAndPlan(atlasGroup, auditInfo, true);

    verify(_metricsDisablePrometheusIntegrationSvc).deletePromConfig(atlasGroup, auditInfo, true);
    verify(_ndsPrometheusEndpointSvc).transitionPrometheusEndpointStatusForDelete(atlasGroup);
    verify(_ndsGroupSvc).setPlanningNow(_groupId);
  }

  @Test
  public void testDeletePromConfigAndPlan_NonAtlasGroup() {
    final AuditInfo auditInfo =
        new AuditInfo("127.0.0.1", "clientId", UserApiKeyType.NONE, EventSource.USER);
    final PrometheusConfig configWithPrivateEndpoints =
        new PrometheusConfig.Builder()
            .enabled(true)
            .username("testuser")
            .selectedPrivateEndpoints(List.of("endpoint1", "endpoint2"))
            .build();

    final Group nonAtlasGroup = spy(new Group());
    nonAtlasGroup.setId(_groupId);
    nonAtlasGroup.setPromConfig(configWithPrivateEndpoints);
    when(nonAtlasGroup.isAtlas()).thenReturn(false);

    setPromIntegrationAndPrivateLinkFeatureFlags(true);
    _service.deletePromConfigAndPlan(nonAtlasGroup, auditInfo, false);

    verify(_metricsDisablePrometheusIntegrationSvc)
        .deletePromConfig(nonAtlasGroup, auditInfo, false);
    verify(_ndsPrometheusEndpointSvc, never()).transitionPrometheusEndpointStatusForDelete(any());
    verify(_ndsGroupSvc, never()).setPlanningNow(any());
  }

  @Test
  public void testDeletePromConfigAndPlan_AtlasGroupWithEmptyPrivateEndpoints() {
    final AuditInfo auditInfo =
        new AuditInfo("127.0.0.1", "clientId", UserApiKeyType.NONE, EventSource.USER);
    final PrometheusConfig configWithoutPrivateEndpoints =
        new PrometheusConfig.Builder()
            .enabled(true)
            .username("testuser")
            .selectedPrivateEndpoints(List.of())
            .build();

    final Group atlasGroup = spy(new Group());
    atlasGroup.setId(_groupId);
    atlasGroup.setPromConfig(configWithoutPrivateEndpoints);
    when(atlasGroup.isAtlas()).thenReturn(true);

    setPromIntegrationAndPrivateLinkFeatureFlags(true);
    _service.deletePromConfigAndPlan(atlasGroup, auditInfo, true);

    verify(_metricsDisablePrometheusIntegrationSvc).deletePromConfig(atlasGroup, auditInfo, true);
    verify(_ndsPrometheusEndpointSvc, never()).transitionPrometheusEndpointStatusForDelete(any());
    verify(_ndsGroupSvc, never()).setPlanningNow(any());
  }

  @Test
  public void testDeletePromConfigAndPlan_AtlasGroupWithNullPrivateEndpoints() {
    final AuditInfo auditInfo =
        new AuditInfo("127.0.0.1", "clientId", UserApiKeyType.NONE, EventSource.USER);
    final PrometheusConfig configWithNullEndpoints =
        new PrometheusConfig.Builder().enabled(true).username("testuser").build();

    final Group atlasGroup = spy(new Group());
    atlasGroup.setId(_groupId);
    atlasGroup.setPromConfig(configWithNullEndpoints);
    when(atlasGroup.isAtlas()).thenReturn(true);

    setPromIntegrationAndPrivateLinkFeatureFlags(true);
    _service.deletePromConfigAndPlan(atlasGroup, auditInfo, true);

    verify(_metricsDisablePrometheusIntegrationSvc).deletePromConfig(atlasGroup, auditInfo, true);
    verify(_ndsPrometheusEndpointSvc, never()).transitionPrometheusEndpointStatusForDelete(any());
    verify(_ndsGroupSvc, never()).setPlanningNow(any());
  }

  @Test
  public void testDeletePromConfigAndPlan_AtlasGroupWithNoPrometheusConfig() {
    final AuditInfo auditInfo =
        new AuditInfo("127.0.0.1", "clientId", UserApiKeyType.NONE, EventSource.USER);
    final Group atlasGroup = spy(new Group());
    atlasGroup.setId(_groupId);
    atlasGroup.setPromConfig(null);
    when(atlasGroup.isAtlas()).thenReturn(true);

    setPromIntegrationAndPrivateLinkFeatureFlags(true);
    _service.deletePromConfigAndPlan(atlasGroup, auditInfo, false);

    verify(_metricsDisablePrometheusIntegrationSvc).deletePromConfig(atlasGroup, auditInfo, false);
    verify(_ndsPrometheusEndpointSvc, never()).transitionPrometheusEndpointStatusForDelete(any());
    verify(_ndsGroupSvc, never()).setPlanningNow(any());
  }

  @Test
  public void testDeletePromConfigAndPlan_PrometheusOverPrivateLinkFeatureFlagDisabled() {
    final AuditInfo auditInfo =
        new AuditInfo("127.0.0.1", "clientId", UserApiKeyType.NONE, EventSource.USER);
    final PrometheusConfig configWithPrivateEndpoints =
        new PrometheusConfig.Builder()
            .enabled(true)
            .username("testuser")
            .selectedPrivateEndpoints(List.of("endpoint1", "endpoint2"))
            .build();

    final Group atlasGroup = spy(new Group());
    atlasGroup.setId(_groupId);
    atlasGroup.setPromConfig(configWithPrivateEndpoints);
    when(atlasGroup.isAtlas()).thenReturn(true);

    setPromIntegrationAndPrivateLinkFeatureFlags(false);
    _service.deletePromConfigAndPlan(atlasGroup, auditInfo, true);

    // deletePromConfig should be called normally
    verify(_metricsDisablePrometheusIntegrationSvc).deletePromConfig(atlasGroup, auditInfo, true);
    // transitionPrometheusEndpointStatusForDelete should NOT be called because private link feature
    // flag is disabled
    verify(_ndsPrometheusEndpointSvc, never()).transitionPrometheusEndpointStatusForDelete(any());
    // setPlanningNow should NOT be called because private link feature flag is disabled
    verify(_ndsGroupSvc, never()).setPlanningNow(any());
  }

  @Test
  public void testUpsertPromConfig_PlansWhenSelectedPrivateEndpointsChange_NonEmptyToEmpty()
      throws Exception {
    final Group atlasGroup = spy(new Group());
    atlasGroup.setId(_groupId);
    when(atlasGroup.isAtlas()).thenReturn(true);

    final PrometheusConfig existing =
        new PrometheusConfig.Builder()
            .enabled(true)
            .username("user")
            .scheme("http")
            .listenAddress("127.0.0.1:9090")
            .serviceDiscovery("http")
            .selectedPrivateEndpoints(List.of("endpoint-1"))
            .build();
    atlasGroup.setPromConfig(existing);

    when(_appSettings.getAppEnv())
        .thenReturn(com.xgen.cloud.common.appsettings._public.model.AppEnv.DEV);

    setPromIntegrationAndPrivateLinkFeatureFlags(true);

    // set isViaPrivateEndpoint to false so builder clears endpoints to []
    _service.upsertPromConfig(
        atlasGroup,
        new AuditInfo("ip", "clientId", UserApiKeyType.NONE, EventSource.USER),
        true, // enabled
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        false,
        List.of());

    // Assert
    verify(_ndsGroupSvc).setPlanningNow(_groupId);
    verify(_groupDao).updatePromConfig(eq(_groupId), any());
  }

  @Test
  public void testUpsertPromConfig_NoPlanWhenSelectedPrivateEndpointsNullToEmpty()
      throws Exception {
    // Arrange: existing atlas group with null selected endpoints
    final Group atlasGroup = spy(new Group());
    atlasGroup.setId(_groupId);
    when(atlasGroup.isAtlas()).thenReturn(true);

    final PrometheusConfig existing =
        new PrometheusConfig.Builder()
            .enabled(true)
            .username("user")
            .scheme("http")
            .listenAddress("127.0.0.1:9090")
            .serviceDiscovery("http")
            .build();
    atlasGroup.setPromConfig(existing);

    when(_appSettings.getAppEnv())
        .thenReturn(com.xgen.cloud.common.appsettings._public.model.AppEnv.DEV);

    setPromIntegrationAndPrivateLinkFeatureFlags(true);

    // Act: keep isViaPrivateEndpoint false; selected endpoints remain empty
    _service.upsertPromConfig(
        atlasGroup,
        new AuditInfo("ip", "clientId", UserApiKeyType.NONE, EventSource.USER),
        true,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        false,
        null);

    // Assert: no planning since both are null/empty
    verify(_ndsGroupSvc, never()).setPlanningNow(any());
    verify(_groupDao).updatePromConfig(eq(_groupId), any());
  }

  @Test
  public void testUpsertPromConfig_NoPlanWhenSelectedPrivateEndpointsUnchanged() throws Exception {
    // Arrange existing atlas group with non-empty selected endpoints
    final Group atlasGroup = spy(new Group());
    atlasGroup.setId(_groupId);
    when(atlasGroup.isAtlas()).thenReturn(true);

    final List<String> endpoints = List.of("endpoint-1", "endpoint-2");
    final PrometheusConfig existing =
        new PrometheusConfig.Builder()
            .enabled(true)
            .username("user")
            .scheme("http")
            .listenAddress("127.0.0.1:9090")
            .serviceDiscovery("http")
            .selectedPrivateEndpoints(endpoints)
            .build();
    atlasGroup.setPromConfig(existing);

    when(_appSettings.getAppEnv())
        .thenReturn(com.xgen.cloud.common.appsettings._public.model.AppEnv.DEV);

    // Enable both feature flags
    _featureFlagSvcMock = Mockito.mockStatic(FeatureFlagSvc.class);
    _featureFlagSvcMock
        .when(
            () -> isFeatureFlagEnabled(eq(FeatureFlag.PROMETHEUS_INTEGRATION), any(), any(), any()))
        .thenReturn(true);
    _featureFlagSvcMock
        .when(
            () ->
                isFeatureFlagEnabled(
                    eq(FeatureFlag.PROMETHEUS_OVER_PRIVATE_LINK), any(), any(), any()))
        .thenReturn(true);

    // Mock endpoint validation for pIsViaPrivateEndpoint = true
    final com.xgen.cloud.nds.project._public.model.NDSGroup ndsGroup =
        Mockito.mock(com.xgen.cloud.nds.project._public.model.NDSGroup.class);
    when(_ndsGroupSvc.find(_groupId)).thenReturn(java.util.Optional.of(ndsGroup));

    final com.xgen.svc.nds.model.ui.EndpointServiceView ev1 =
        Mockito.mock(com.xgen.svc.nds.model.ui.EndpointServiceView.class);
    final com.xgen.svc.nds.model.ui.RegionView region1 =
        Mockito.mock(com.xgen.svc.nds.model.ui.RegionView.class);
    when(region1.getKey()).thenReturn("us-east-1");
    when(ev1.getRegion()).thenReturn(region1);
    when(ev1.getEndpointId()).thenReturn("endpoint-1");

    final com.xgen.svc.nds.model.ui.EndpointServiceView ev2 =
        Mockito.mock(com.xgen.svc.nds.model.ui.EndpointServiceView.class);
    final com.xgen.svc.nds.model.ui.RegionView region2 =
        Mockito.mock(com.xgen.svc.nds.model.ui.RegionView.class);
    when(region2.getKey()).thenReturn("us-west-2");
    when(ev2.getRegion()).thenReturn(region2);
    when(ev2.getEndpointId()).thenReturn("endpoint-2");

    when(_ndsPrivateLinkSvc.getEndpointServiceViewsForGroup(
            eq(ndsGroup), eq(com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider.AWS)))
        .thenReturn(List.of(ev1, ev2));

    // Act: keep same endpoints with private endpoint mode enabled
    _service.upsertPromConfig(
        atlasGroup,
        new AuditInfo("ip", "clientId", UserApiKeyType.NONE, EventSource.USER),
        true,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        true,
        endpoints);

    // Assert: lists unchanged -> no planning
    verify(_ndsGroupSvc, never()).setPlanningNow(any());
    verify(_groupDao).updatePromConfig(eq(_groupId), any());
  }
}
