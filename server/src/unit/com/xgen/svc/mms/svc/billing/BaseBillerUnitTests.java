package com.xgen.svc.mms.svc.billing;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static com.xgen.svc.mms.svc.billing.BaseBiller.BILLER_DATA_ALWAYS_READY_IN_TEST;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingplatform.model.sku._public.model.PricingConsumers;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuPricing;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.pricing._public.svc.SkuPricingSvc;
import com.xgen.svc.mms.model.billing.Discount;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.model.billing.OrgBillContext;
import com.xgen.svc.mms.model.billing.OrgPlan;
import com.xgen.svc.mms.model.billing.OrgPrepaidPlan;
import com.xgen.svc.mms.model.billing.PercentOffCoupon;
import com.xgen.svc.mms.model.billing.PrepaidPlan;
import com.xgen.svc.mms.model.billing.PrepaidPlanType;
import com.xgen.svc.mms.svc.billing.calculator.PriceCalculator;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class BaseBillerUnitTests {

  @Mock private MTMHolderCacheSvc mtmHolderCacheSvc;
  @Mock private AppSettings appSettings;
  @Mock private SkuPricingSvc skuPricingSvc;

  private BaseBiller biller;
  private static final double EPSILON = 0.00000001;
  private static final Organization organization =
      new Organization.Builder().name("test org").id(new ObjectId()).build();

  @BeforeEach
  public void setUp() {
    biller = dummyBiller();
  }

  @Test
  public void testAddFreeTier() {
    Group group = new Group();
    group.setId(oid(100));
    ObjectId invoiceId = oid(1);
    Invoice invoice = new Invoice.Builder().id(invoiceId).build();
    Date today = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
    Date yesterday = DateUtils.addDays(today, -1);

    // Test entirely under the free tier
    SkuPricing pricing =
        new SkuPricing.Builder()
            .unitPriceDollars(monthlyToDailyCost(1.25))
            .floatPriceDollars(monthlyToDailyCost(1.25))
            .freeTierQuantity(1.0)
            .build();
    LineItem lineItem1 =
        biller.addFreeTierLineItem(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        mock(OrgPrepaidPlan.class)))
                .sku(SKU.MMS_BACKUP_STORAGE)
                .quantity(1)
                .priceCalculator(new PriceCalculator())
                .build(),
            pricing);
    assertNotNull(lineItem1);
    assertEquals(oid(100), lineItem1.getGroupId());
    assertEquals(invoiceId, lineItem1.getInvoiceId());
    assertEquals(yesterday, lineItem1.getStartDate());
    assertEquals(today, lineItem1.getEndDate());
    assertEquals(SKU.MMS_BACKUP_STORAGE_FREE_TIER, lineItem1.getSku());
    assertEquals(1, lineItem1.getQuantity(), EPSILON);
    assertEquals(0, lineItem1.getUnitPriceDollars(), EPSILON);
    assertEquals(0, lineItem1.getTotalPriceCents());

    // Test partially under the free tier
    LineItem lineItem2 =
        biller.addFreeTierLineItem(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        mock(OrgPrepaidPlan.class)))
                .sku(SKU.MMS_BACKUP_STORAGE)
                .quantity(10)
                .priceCalculator(new PriceCalculator())
                .build(),
            pricing);
    assertNotNull(lineItem2);
    assertEquals(oid(100), lineItem2.getGroupId());
    assertEquals(invoiceId, lineItem2.getInvoiceId());
    assertEquals(yesterday, lineItem2.getStartDate());
    assertEquals(today, lineItem2.getEndDate());
    assertEquals(SKU.MMS_BACKUP_STORAGE_FREE_TIER, lineItem2.getSku());
    assertEquals(1, lineItem2.getQuantity(), EPSILON);
    assertEquals(0, lineItem2.getUnitPriceDollars(), EPSILON);
    assertEquals(0, lineItem2.getTotalPriceCents());

    // Test a SKU that has no free tier
    assertNull(
        biller.addFreeTierLineItem(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        mock(OrgPrepaidPlan.class)))
                .sku(SKU.MMS_STANDARD)
                .quantity(10.0)
                .priceCalculator(new PriceCalculator())
                .build(),
            new SkuPricing.Builder().build()));
  }

  @Test
  public void addFreeTier_withPreviousUsage_billFreeAndNonFreeTiers() {
    Group group = new Group();
    group.setId(oid(100));
    ObjectId invoiceId = oid(1);
    Invoice invoice = new Invoice.Builder().id(invoiceId).build();
    Date today = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
    Date yesterday = DateUtils.addDays(today, -1);
    SKU sku = SKU.NDS_AWS_PIT_RESTORE_STORAGE;
    SkuPricing pricing =
        new SkuPricing.Builder()
            .freeTierQuantity(5.0)
            .tieredPricingQuantities(100, 250, 500)
            .tieredRegionUnitPriceDollars(
                AWSRegionName.US_EAST_1,
                monthlyToDailyCost(1),
                monthlyToDailyCost(0.75),
                monthlyToDailyCost(0.5),
                monthlyToDailyCost(0.25))
            .build();
    when(skuPricingSvc.getPricing(sku, yesterday, organization.getId(), PricingConsumers.BILLING))
        .thenReturn(pricing);

    // Test partially under the free tier, we have already billed 3 units in free tier
    Stream<LineItem> lineItemStream =
        biller.addLineItem(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        mock(OrgPrepaidPlan.class)))
                .sku(sku) // free tier quantity of 5
                .quantity(4)
                .priceCalculator(new RegionPriceCalculator(AWSRegionName.US_EAST_1))
                .totalQuantityForBillRange(7) // we have previously billed 3 units
                .build());
    assertNotNull(lineItemStream);
    List<LineItem> lineItemList = lineItemStream.toList();
    assertEquals(2, lineItemList.size());
    LineItem lineItem1 = lineItemList.get(0);
    assertEquals(oid(100), lineItem1.getGroupId());
    assertEquals(invoiceId, lineItem1.getInvoiceId());
    assertEquals(yesterday, lineItem1.getStartDate());
    assertEquals(today, lineItem1.getEndDate());
    assertEquals(SKU.NDS_AWS_PIT_RESTORE_STORAGE_FREE_TIER, lineItem1.getSku());
    assertEquals(2, lineItem1.getQuantity(), EPSILON);
    assertEquals(0, lineItem1.getUnitPriceDollars(), EPSILON);
    assertEquals(0, lineItem1.getTotalPriceCents());
    assertNull(lineItem1.getTier());
    LineItem lineItem2 = lineItemList.get(1);
    assertEquals(oid(100), lineItem2.getGroupId());
    assertEquals(invoiceId, lineItem2.getInvoiceId());
    assertEquals(yesterday, lineItem2.getStartDate());
    assertEquals(today, lineItem2.getEndDate());
    assertEquals(sku, lineItem2.getSku());
    assertEquals(2, lineItem2.getQuantity(), EPSILON);
    assertEquals(monthlyToDailyCost(1), lineItem2.getUnitPriceDollars(), EPSILON);
    assertEquals(1, lineItem2.getTier());
  }

  @Test
  public void addFreeTier_withPreviousUsage_billFreeTierOnly() {
    Group group = new Group();
    group.setId(oid(100));
    ObjectId invoiceId = oid(1);
    Invoice invoice = new Invoice.Builder().id(invoiceId).build();
    Date today = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
    Date yesterday = DateUtils.addDays(today, -1);
    SKU sku = SKU.NDS_AWS_PIT_RESTORE_STORAGE;
    SkuPricing pricing =
        new SkuPricing.Builder()
            .freeTierQuantity(5.0)
            .regionUnitPriceDollars(AWSRegionName.US_EAST_1, monthlyToDailyCost(1))
            .build();
    when(skuPricingSvc.getPricing(sku, yesterday, organization.getId(), PricingConsumers.BILLING))
        .thenReturn(pricing);

    // Test partially under the free tier, we have already billed 2 units in free tier
    Stream<LineItem> lineItemStream =
        biller.addLineItem(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        mock(OrgPrepaidPlan.class)))
                .sku(sku) // free tier quantity of 5
                .quantity(2)
                .priceCalculator(new RegionPriceCalculator(AWSRegionName.US_EAST_1))
                .totalQuantityForBillRange(4) // we have previously billed 2 units
                .build());
    assertNotNull(lineItemStream);
    List<LineItem> lineItemList = lineItemStream.toList();
    assertEquals(1, lineItemList.size());
    LineItem lineItem1 = lineItemList.get(0);
    assertEquals(oid(100), lineItem1.getGroupId());
    assertEquals(invoiceId, lineItem1.getInvoiceId());
    assertEquals(yesterday, lineItem1.getStartDate());
    assertEquals(today, lineItem1.getEndDate());
    assertEquals(SKU.NDS_AWS_PIT_RESTORE_STORAGE_FREE_TIER, lineItem1.getSku());
    assertEquals(2, lineItem1.getQuantity(), EPSILON);
    assertEquals(0, lineItem1.getUnitPriceDollars(), EPSILON);
    assertEquals(0, lineItem1.getTotalPriceCents(), EPSILON);
    assertNull(lineItem1.getTier());
  }

  @Test
  public void testAddLineItem() {
    ObjectId groupId = new ObjectId();
    Group group = new Group();
    group.setId(groupId);
    Invoice invoice = new Invoice.Builder().id(oid(1)).orgId(organization.getId()).build();
    Date today = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
    Date yesterday = DateUtils.addDays(today, -1);
    SKU sku = SKU.MMS_STANDARD;
    SkuPricing pricing =
        new SkuPricing.Builder()
            .unitPriceDollars(monthlyToDailyCost(1.25))
            .floatPriceDollars(monthlyToDailyCost(1.25))
            .build();
    when(skuPricingSvc.getPricing(sku, yesterday, organization.getId(), PricingConsumers.BILLING))
        .thenReturn(pricing);

    // Test simple case
    Stream<LineItem> stream1 =
        biller.addLineItem(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        new OrgPrepaidPlan.Builder().build()))
                .sku(sku)
                .quantity(10 * 24)
                .priceCalculator(new PriceCalculator())
                .build());
    List<LineItem> list1 = stream1.toList();
    assertEquals(1, list1.size());
    LineItem lineItem1 = list1.get(0);
    assertEquals(groupId, lineItem1.getGroupId());
    assertEquals(oid(1), lineItem1.getInvoiceId());
    assertEquals(yesterday, lineItem1.getStartDate());
    assertEquals(today, lineItem1.getEndDate());
    assertEquals(sku, lineItem1.getSku());
    assertEquals(10 * 24, lineItem1.getQuantity(), EPSILON);
    assertEquals(pricing.getUnitPriceDollars(), lineItem1.getUnitPriceDollars(), EPSILON);

    SKU sku2 = SKU.MMS_BACKUP_STORAGE;
    SkuPricing pricing2 =
        new SkuPricing.Builder()
            .unitPriceDollars(monthlyToDailyCost(1.25))
            .freeTierQuantity(1.0)
            .floatPriceDollars(monthlyToDailyCost(1.25))
            .build();
    when(skuPricingSvc.getPricing(sku2, yesterday, organization.getId(), PricingConsumers.BILLING))
        .thenReturn(pricing2);

    // Test with free tier
    Stream<LineItem> stream2 =
        biller.addLineItem(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        new OrgPrepaidPlan.Builder().build()))
                .sku(sku2)
                .quantity(10)
                .priceCalculator(new PriceCalculator())
                .build());
    List<LineItem> list2 = stream2.toList();
    assertEquals(2, list2.size());
    LineItem freeItem2 = list2.get(0);
    assertEquals(groupId, freeItem2.getGroupId());
    assertEquals(oid(1), freeItem2.getInvoiceId());
    assertEquals(yesterday, freeItem2.getStartDate());
    assertEquals(today, freeItem2.getEndDate());
    assertEquals(SKU.MMS_BACKUP_STORAGE_FREE_TIER, freeItem2.getSku());
    assertEquals(1, freeItem2.getQuantity(), EPSILON);
    assertEquals(0, freeItem2.getUnitPriceDollars(), EPSILON);
    assertEquals(0, freeItem2.getTotalPriceCents());
    LineItem lineItem2 = list2.get(1);
    assertEquals(groupId, lineItem2.getGroupId());
    assertEquals(oid(1), lineItem2.getInvoiceId());
    assertEquals(yesterday, lineItem2.getStartDate());
    assertEquals(today, lineItem2.getEndDate());
    assertEquals(sku2, lineItem2.getSku());
    assertEquals(9, lineItem2.getQuantity(), EPSILON);
    assertEquals(pricing.getUnitPriceDollars(), lineItem2.getUnitPriceDollars(), EPSILON);

    // Test with a coupon/discount
    PercentOffCoupon coupon =
        new PercentOffCoupon.Builder("HALF_OFF").percentDiscount(0.5f).build();
    Discount discount = new Discount(oid(100), oid(1), yesterday, today);
    Stream<LineItem> stream3 =
        biller.addLineItem(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        new OrgPrepaidPlan.Builder().build()))
                .sku(sku)
                .quantity(10 * 24)
                .priceCalculator(new PriceCalculator())
                .percentOffCoupon(coupon)
                .discount(discount)
                .build());
    List<LineItem> list3 = stream3.toList();
    assertEquals(1, list3.size());
    LineItem lineItem3 = list3.get(0);
    assertEquals(groupId, lineItem3.getGroupId());
    assertEquals(oid(1), lineItem3.getInvoiceId());
    assertEquals(yesterday, lineItem3.getStartDate());
    assertEquals(today, lineItem3.getEndDate());
    assertEquals(sku, lineItem3.getSku());
    assertEquals(10 * 24, lineItem3.getQuantity(), EPSILON);
    assertEquals(pricing2.getUnitPriceDollars(), lineItem3.getUnitPriceDollars(), EPSILON);
    assertEquals(0.5f, lineItem3.getPercentDiscount(), EPSILON);
    assertEquals(lineItem1.getTotalPriceCents() / 2, lineItem3.getTotalPriceCents());

    // Test with a prepaid plan
    Date thisYear = DateUtils.truncate(today, Calendar.YEAR);
    Date nextYear = DateUtils.addYears(thisYear, 1);
    PrepaidPlan ppp =
        new PrepaidPlan.Builder(PrepaidPlanType.PREPAID_UNBUNDLED, List.of(sku))
            .activationCode("ABCD1234")
            .startDate(thisYear)
            .endDate(nextYear)
            .quantity(100)
            .unitPriceDollars(1.0)
            .build();
    Stream<LineItem> stream4 =
        biller.addLineItem(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        new OrgPrepaidPlan.Builder().prepaidPlans(List.of(ppp)).build()))
                .sku(sku)
                .quantity(10 * 24)
                .priceCalculator(new PriceCalculator())
                .build());
    List<LineItem> list4 = stream4.toList();
    assertEquals(1, list4.size());
    LineItem lineItem4 = list4.get(0);
    assertEquals(groupId, lineItem4.getGroupId());
    assertEquals(oid(1), lineItem4.getInvoiceId());
    assertEquals(yesterday, lineItem4.getStartDate());
    assertEquals(today, lineItem4.getEndDate());
    assertEquals(sku, lineItem4.getSku());
    assertEquals(10 * 24, lineItem4.getQuantity(), EPSILON);
    assertEquals(0, lineItem4.getUnitPriceDollars(), EPSILON);
    assertEquals(0, lineItem4.getTotalPriceCents());

    // Test with a group as an MTM holder
    when(mtmHolderCacheSvc.isGroupMTMHolder(any())).thenReturn(true);
    Stream<LineItem> stream5 =
        biller.addLineItem(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        new OrgPrepaidPlan.Builder().build()))
                .sku(sku)
                .quantity(10 * 24)
                .priceCalculator(new PriceCalculator())
                .build());
    List<LineItem> list5 = stream5.toList();
    assertEquals(1, list5.size());
    LineItem lineItem5 = list5.get(0);
    assertEquals(groupId, lineItem5.getGroupId());
    assertEquals(oid(1), lineItem5.getInvoiceId());
    assertEquals(yesterday, lineItem5.getStartDate());
    assertEquals(today, lineItem5.getEndDate());
    assertEquals(sku, lineItem5.getSku());
    assertEquals(10 * 24, lineItem5.getQuantity(), EPSILON);
    assertEquals(0, lineItem5.getUnitPriceDollars(), EPSILON);
    assertEquals(0, lineItem5.getTotalPriceCents());
  }

  @Test
  public void testIsShard() {
    assertFalse(BaseBiller.isShard(null));
    assertFalse(BaseBiller.isShard(""));
    assertFalse(BaseBiller.isShard("test"));
    assertFalse(BaseBiller.isShard("cluster0-config-0"));
    assertTrue(BaseBiller.isShard("cluster0-shard-0"));
  }

  @Test
  public void testIsConfig() {
    assertFalse(BaseBiller.isConfig(null));
    assertFalse(BaseBiller.isConfig(""));
    assertFalse(BaseBiller.isConfig("test"));
    assertFalse(BaseBiller.isConfig("cluster0-shard-0"));
    assertTrue(BaseBiller.isConfig("cluster0-config-0"));
  }

  private BaseBiller dummyBiller() {
    return new BaseBiller(mtmHolderCacheSvc, appSettings, skuPricingSvc) {
      @Override
      public Stream<LineItem> billDaily(BillerContext billerContext) {
        return Stream.empty();
      }

      @Override
      public Date getLastDateReadyForBilling(SKU sku) {
        return new Date();
      }
    };
  }

  @Test
  public void testCreateTieredPricingLineItems() {
    ObjectId groupId = new ObjectId();
    Group group = new Group();
    group.setId(groupId);

    ObjectId invoiceId = oid(1);
    Invoice invoice = new Invoice.Builder().id(invoiceId).build();

    Date today = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
    Date yesterday = DateUtils.addDays(today, -1);

    ObjectId clusterId = oid(5);
    String clusterName = "cluster";
    SKU sku = SKU.NDS_AWS_PIT_RESTORE_STORAGE;

    SkuPricing pricing =
        new SkuPricing.Builder()
            .unitPriceDollars(monthlyToDailyCost(1.25))
            .floatPriceDollars(monthlyToDailyCost(1.25))
            .freeTierQuantity(5.0)
            .tieredPricingQuantities(100, 250, 500)
            .tieredRegionUnitPriceDollars(
                AWSRegionName.US_EAST_1,
                monthlyToDailyCost(1),
                monthlyToDailyCost(0.75),
                monthlyToDailyCost(0.5),
                monthlyToDailyCost(0.25))
            .tieredRegionUnitPriceDollars(
                AWSRegionName.US_WEST_1,
                monthlyToDailyCost(1.15),
                monthlyToDailyCost(0.85),
                monthlyToDailyCost(0.6),
                monthlyToDailyCost(0.3))
            .build();

    List<LineItem> lineItems =
        biller.addTieredPricingLineItems(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        mock(OrgPrepaidPlan.class)))
                .sku(sku)
                .quantity(280)
                .clusterUniqueId(clusterId)
                .clusterName(clusterName)
                .priceCalculator(new RegionPriceCalculator(AWSRegionName.US_EAST_1))
                .build(),
            pricing);

    assertEquals(3, lineItems.size());

    // quantities: 100, 250, 500
    assertEquals(95, lineItems.get(0).getQuantity(), EPSILON);
    assertEquals(150, lineItems.get(1).getQuantity(), EPSILON);
    assertEquals(30, lineItems.get(2).getQuantity(), EPSILON);

    assertEquals(monthlyToDailyCost(1), lineItems.get(0).getUnitPriceDollars(), EPSILON);
    assertEquals(monthlyToDailyCost(0.75), lineItems.get(1).getUnitPriceDollars(), EPSILON);
    assertEquals(monthlyToDailyCost(0.5), lineItems.get(2).getUnitPriceDollars(), EPSILON);

    assertEquals(1, lineItems.get(0).getTier().intValue());
    assertEquals(2, lineItems.get(1).getTier().intValue());
    assertEquals(3, lineItems.get(2).getTier().intValue());

    for (LineItem lineItem : lineItems) {
      assertEquals(groupId, lineItem.getGroupId());
      assertEquals(invoiceId, lineItem.getInvoiceId());
      assertEquals(clusterId, lineItem.getClusterUniqueId());
      assertEquals(clusterName, lineItem.getClusterName());
      assertEquals(yesterday, lineItem.getStartDate());
      assertEquals(today, lineItem.getEndDate());
      assertEquals(sku, lineItem.getSku());
    }

    // testing with quantity larger than the max tier quantity
    List<LineItem> lineItems2 =
        biller.addTieredPricingLineItems(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        mock(OrgPrepaidPlan.class)))
                .sku(sku)
                .quantity(580)
                .clusterUniqueId(clusterId)
                .clusterName(clusterName)
                .priceCalculator(new RegionPriceCalculator(AWSRegionName.US_WEST_1))
                .build(),
            pricing);

    assertEquals(4, lineItems2.size());

    assertEquals(95, lineItems2.get(0).getQuantity(), EPSILON);
    assertEquals(150, lineItems2.get(1).getQuantity(), EPSILON);
    assertEquals(250, lineItems2.get(2).getQuantity(), EPSILON);
    assertEquals(80, lineItems2.get(3).getQuantity(), EPSILON);

    assertEquals(monthlyToDailyCost(1.15), lineItems2.get(0).getUnitPriceDollars(), EPSILON);
    assertEquals(monthlyToDailyCost(0.85), lineItems2.get(1).getUnitPriceDollars(), EPSILON);
    assertEquals(monthlyToDailyCost(0.6), lineItems2.get(2).getUnitPriceDollars(), EPSILON);
    assertEquals(monthlyToDailyCost(0.3), lineItems2.get(3).getUnitPriceDollars(), EPSILON);

    assertEquals(1, lineItems2.get(0).getTier().intValue());
    assertEquals(2, lineItems2.get(1).getTier().intValue());
    assertEquals(3, lineItems2.get(2).getTier().intValue());
    assertEquals(4, lineItems2.get(3).getTier().intValue());
  }

  @Test
  public void testCreateTieredPricingLineItems_MtmHolder() {
    ObjectId groupId = new ObjectId();
    Group group = new Group();
    group.setId(groupId);
    ObjectId invoiceId = oid(1);
    Invoice invoice = new Invoice.Builder().id(invoiceId).build();
    Date today = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
    Date yesterday = DateUtils.addDays(today, -1);

    ObjectId clusterId = oid(5);
    String clusterName = "cluster";

    // Test with a group as an MTM holder
    when(mtmHolderCacheSvc.isGroupMTMHolder(any())).thenReturn(true);

    SkuPricing pricing =
        new SkuPricing.Builder()
            .unitPriceDollars(monthlyToDailyCost(1.25))
            .floatPriceDollars(monthlyToDailyCost(1.25))
            .freeTierQuantity(5.0)
            .tieredPricingQuantities(100, 250, 500)
            .tieredRegionUnitPriceDollars(
                AWSRegionName.US_EAST_1,
                monthlyToDailyCost(1),
                monthlyToDailyCost(0.75),
                monthlyToDailyCost(0.5),
                monthlyToDailyCost(0.25))
            .build();
    List<LineItem> lineItems =
        biller.addTieredPricingLineItems(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        mock(OrgPrepaidPlan.class)))
                .sku(SKU.NDS_AWS_PIT_RESTORE_STORAGE)
                .quantity(280)
                .clusterUniqueId(clusterId)
                .clusterName(clusterName)
                .priceCalculator(new RegionPriceCalculator(AWSRegionName.US_EAST_1))
                .build(),
            pricing);

    assertEquals(3, lineItems.size());

    // unit price should be 0.
    assertThat(lineItems.get(0).getUnitPriceDollars(), equalTo(0.0));
    assertThat(lineItems.get(1).getUnitPriceDollars(), equalTo(0.0));
    assertThat(lineItems.get(2).getUnitPriceDollars(), equalTo(0.0));

    // quantities: 100, 250, 500
    assertEquals(95, lineItems.get(0).getQuantity(), EPSILON);
    assertEquals(150, lineItems.get(1).getQuantity(), EPSILON);
    assertEquals(30, lineItems.get(2).getQuantity(), EPSILON);
  }

  @Test
  public void createTieredPricingLineItems_previouslyPartiallyBilled() {
    ObjectId groupId = new ObjectId();
    Group group = new Group();
    group.setId(groupId);

    ObjectId invoiceId = oid(1);
    Invoice invoice = new Invoice.Builder().id(invoiceId).build();

    Date today = DateUtils.truncate(new Date(), Calendar.DAY_OF_MONTH);
    Date yesterday = DateUtils.addDays(today, -1);

    ObjectId clusterId = oid(5);
    String clusterName = "cluster";
    SKU sku = SKU.NDS_AWS_PIT_RESTORE_STORAGE;

    SkuPricing pricing =
        new SkuPricing.Builder()
            .unitPriceDollars(monthlyToDailyCost(1.25))
            .floatPriceDollars(monthlyToDailyCost(1.25))
            .freeTierQuantity(5.0)
            .tieredPricingQuantities(100, 250, 500)
            .tieredRegionUnitPriceDollars(
                AWSRegionName.US_EAST_1,
                monthlyToDailyCost(1),
                monthlyToDailyCost(0.75),
                monthlyToDailyCost(0.5),
                monthlyToDailyCost(0.25))
            .build();
    List<LineItem> lineItems =
        biller.addTieredPricingLineItems(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        mock(OrgPrepaidPlan.class)))
                .sku(sku)
                .quantity(280)
                .clusterUniqueId(clusterId)
                .clusterName(clusterName)
                .priceCalculator(new RegionPriceCalculator(AWSRegionName.US_EAST_1))
                .totalQuantityForBillRange(400) // we already billed 120 units
                .build(),
            pricing);

    assertEquals(2, lineItems.size());

    // we have already billed 120, and we are billing  for another 280 on the same usage date. This
    // means that we have billed up to 120 in tier 2, so we have
    assertEquals(130, lineItems.get(0).getQuantity(), EPSILON);
    assertEquals(150, lineItems.get(1).getQuantity(), EPSILON);

    assertEquals(monthlyToDailyCost(0.75), lineItems.get(0).getUnitPriceDollars(), EPSILON);
    assertEquals(monthlyToDailyCost(0.5), lineItems.get(1).getUnitPriceDollars(), EPSILON);

    assertEquals(2, lineItems.get(0).getTier().intValue());
    assertEquals(3, lineItems.get(1).getTier().intValue());

    for (LineItem lineItem : lineItems) {
      assertEquals(groupId, lineItem.getGroupId());
      assertEquals(invoiceId, lineItem.getInvoiceId());
      assertEquals(clusterId, lineItem.getClusterUniqueId());
      assertEquals(clusterName, lineItem.getClusterName());
      assertEquals(yesterday, lineItem.getStartDate());
      assertEquals(today, lineItem.getEndDate());
      assertEquals(sku, lineItem.getSku());
    }

    // testing with quantity larger than the max tier quantity
    List<LineItem> lineItems2 =
        biller.addTieredPricingLineItems(
            new BillerContext.Builder(
                    new OrgBillContext(
                        organization,
                        group,
                        invoice,
                        yesterday,
                        today,
                        mock(OrgPlan.class),
                        mock(OrgPrepaidPlan.class)))
                .sku(sku)
                .quantity(20)
                .clusterUniqueId(clusterId)
                .clusterName(clusterName)
                .priceCalculator(new RegionPriceCalculator(AWSRegionName.US_EAST_1))
                .totalQuantityForBillRange(600) // we already billed 580
                .build(),
            pricing);

    assertEquals(1, lineItems2.size());

    for (LineItem lineItem : lineItems2) {
      assertEquals(groupId, lineItem.getGroupId());
      assertEquals(invoiceId, lineItem.getInvoiceId());
      assertEquals(clusterId, lineItem.getClusterUniqueId());
      assertEquals(clusterName, lineItem.getClusterName());
      assertEquals(yesterday, lineItem.getStartDate());
      assertEquals(today, lineItem.getEndDate());
      assertEquals(sku, lineItem.getSku());
    }

    assertEquals(20, lineItems2.get(0).getQuantity(), EPSILON);
    assertEquals(monthlyToDailyCost(0.25), lineItems2.get(0).getUnitPriceDollars(), EPSILON);
    assertEquals(4, lineItems2.get(0).getTier().intValue());
  }

  private static double monthlyToDailyCost(double pMonthlyCost) {
    return pMonthlyCost * 12 / 365;
  }

  @Test
  public void testGetUnitPriceDollars() {
    BaseBiller baseBiller = dummyBiller();

    // It is Atlas Gov but there are no uplift charges.
    when(appSettings.getGovUpliftRatio()).thenReturn(0.0);
    assertThat(baseBiller.getUnitPriceDollars(1.0), is(1.0));

    // There are 15% uplift charges.
    when(appSettings.getGovUpliftRatio()).thenReturn(0.15);
    assertThat(baseBiller.getUnitPriceDollars(1.0), is(1.15));
  }

  @Test
  public void testIsDataReady_forSKUHasExternalDataDependency_returnTrueByDefaultInTest() {
    BaseBiller baseBiller = Mockito.spy(dummyBiller());
    when(appSettings.isTest()).thenReturn(true);
    when(appSettings.getBoolProp(BILLER_DATA_ALWAYS_READY_IN_TEST, true)).thenReturn(true);

    assertTrue(baseBiller.isDataReady(SKU.STITCH_DATA_DOWNLOADED, new Date(), new ObjectId()));
    verify(baseBiller, never()).getLastDateReadyForBilling(any());
  }

  @Test
  public void testIsDataReady_forSKUHasExternalDataDependency_respectOverrideInTest() {
    BaseBiller baseBiller = Mockito.spy(dummyBiller());
    when(appSettings.isTest()).thenReturn(true);
    when(appSettings.getBoolProp(BILLER_DATA_ALWAYS_READY_IN_TEST, true)).thenReturn(false);

    baseBiller.isDataReady(SKU.STITCH_DATA_DOWNLOADED, new Date(), new ObjectId());
    verify(baseBiller, times(1)).getLastDateReadyForBilling(SKU.STITCH_DATA_DOWNLOADED);
  }

  @Test
  public void testIsDataReady_forSKUHasExternalDataDependency_callGetLastDateReadyForBilling() {
    BaseBiller baseBiller = Mockito.spy(dummyBiller());
    when(appSettings.isTest()).thenReturn(false);
    lenient()
        .when(appSettings.getBoolProp(BILLER_DATA_ALWAYS_READY_IN_TEST, true))
        .thenReturn(true);

    baseBiller.isDataReady(SKU.STITCH_DATA_DOWNLOADED, new Date(), new ObjectId());
    verify(baseBiller, times(1)).getLastDateReadyForBilling(SKU.STITCH_DATA_DOWNLOADED);
  }
}
