package com.xgen.svc.mms.svc.alert.check.system;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.xgen.cloud.alerts.checks.common._public.svc.Result.WithoutState;
import com.xgen.cloud.alerts.checks.system._public.svc.LogDebugOverrideActive;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.util._public.logging.LogLevel;
import com.xgen.cloud.common.util._public.logging.LogLevelOverride;
import com.xgen.svc.mms.BaseUTest;
import com.xgen.svc.mms.dao.logging.LogLevelOverrideDao;
import com.xgen.svc.mms.svc.alert.SystemAlertProcessingContext;
import com.xgen.svc.mms.util.UnitTestUtils;
import java.util.Collections;
import org.junit.jupiter.api.Test;

public class LogDebugOverrideActiveUnitTests extends BaseUTest {

  @Test
  public void testGetTargets() {
    final AppSettings appSettings = mock(AppSettings.class);
    final LogLevelOverrideDao logLevelOverrideDao = mock(LogLevelOverrideDao.class);
    final LogLevelOverride.Builder builder =
        new LogLevelOverride.Builder("com.xgen.svc", LogLevel.INFO, "test.user");
    final LogLevelOverride logLevelOverride = builder.build();
    when(logLevelOverrideDao.findAllLogLevelOverrides())
        .thenReturn(Collections.singletonList(logLevelOverride));
    final SystemAlertProcessingContext context =
        UnitTestUtils.create(SystemAlertProcessingContext.class).withArgs(logLevelOverrideDao);
    final LogDebugOverrideActive logDebugOverrideActive = new LogDebugOverrideActive(appSettings);
    assertEquals(
        Collections.singletonList(logLevelOverride),
        logDebugOverrideActive.doGetTargets(context, null));
  }

  @Test
  public void testRunCheck() throws Exception {
    final AppSettings appSettings = mock(AppSettings.class);
    final LogLevelOverride.Builder debugOverridebuilder =
        new LogLevelOverride.Builder("com.xgen.svc", LogLevel.DEBUG, "test.user");
    final LogLevelOverride debugOverride = debugOverridebuilder.build();
    final LogLevelOverride.Builder warnOverridebuilder =
        new LogLevelOverride.Builder("com.xgen.svc", LogLevel.WARN, "test.user");
    final LogLevelOverride warnOverride = warnOverridebuilder.build();
    final LogDebugOverrideActive logDebugOverrideActive = new LogDebugOverrideActive(appSettings);
    assertEquals(
        WithoutState.HAS_ALERT, logDebugOverrideActive.doRunCheck(null, null, debugOverride));
    assertEquals(
        WithoutState.NO_ALERT, logDebugOverrideActive.doRunCheck(null, null, warnOverride));
  }
}
