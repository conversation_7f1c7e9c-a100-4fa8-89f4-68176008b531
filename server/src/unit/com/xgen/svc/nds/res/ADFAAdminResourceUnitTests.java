package com.xgen.svc.nds.res;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.RETURNS_SMART_NULLS;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.svc.mms.api.view.atlas._private.dataLake.ApiPrivateAtlasDataLakeTenantConfigView;
import com.xgen.svc.mms.util.UnitTestUtils;
import com.xgen.svc.nds.model.ui.AndonCordContainer;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeAndonCordView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCurrentOpView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeDataSetView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeTenantSettingsView;
import com.xgen.svc.nds.svc.NDSDataLakePrivateSvc;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(CloudProviderExtension.class)
public class ADFAAdminResourceUnitTests {
  private ADFAAdminResource _resource;
  private NDSDataLakePrivateSvc _ndsDataLakePrivateSvc;

  @BeforeEach
  public void setUp() {
    _ndsDataLakePrivateSvc = mock(NDSDataLakePrivateSvc.class);
    AuditSvc _auditSvc = mock(AuditSvc.class, RETURNS_SMART_NULLS);
    _resource =
        UnitTestUtils.create(ADFAAdminResource.class).withArgs(_ndsDataLakePrivateSvc, _auditSvc);
  }

  @Test
  public void testListAndonCords() throws Exception {
    when(_ndsDataLakePrivateSvc.listAndonCords("", "", "", 101, 0))
        .thenReturn(
            List.of(
                NDSDataLakeAndonCordView.builder()
                    .name("featureA")
                    .region("regionA")
                    .state("enabled")
                    .build(),
                NDSDataLakeAndonCordView.builder()
                    .name("featureB")
                    .region("regionB")
                    .state("disabled")
                    .build()));

    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();
    Response response = _resource.listAndonCords(auditInfo, "", "", "", 0, 100);
    assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());

    Object entity = response.getEntity();
    assertInstanceOf(AndonCordContainer.class, entity);
    AndonCordContainer andonCordContainer = (AndonCordContainer) entity;
    List<NDSDataLakeAndonCordView> andonCords = new ArrayList<>(andonCordContainer.getData());
    andonCords.sort(Comparator.comparing(NDSDataLakeAndonCordView::getName));

    assertEquals(2, andonCords.size());
    assertEquals("featureA", andonCords.get(0).getName());
    assertEquals("regionA", andonCords.get(0).getRegion());
    assertEquals("enabled", andonCords.get(0).getState());

    assertEquals("featureB", andonCords.get(1).getName());
    assertEquals("regionB", andonCords.get(1).getRegion());
    assertEquals("disabled", andonCords.get(1).getState());
  }

  @Test
  public void testGetTenantSettings() throws Exception {
    final ObjectId tenantId1 = new ObjectId();
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    when(_ndsDataLakePrivateSvc.getTenantSettings(tenantId1))
        .thenThrow(new SvcException(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID, tenantId1));
    try {
      _resource.getTenantSettings(auditInfo, tenantId1.toHexString());
    } catch (SvcException pe) {
      assertEquals(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID, pe.getErrorCode());
    }

    final ObjectId tenantId2 = new ObjectId();
    when(_ndsDataLakePrivateSvc.getTenantSettings(tenantId2))
        .thenReturn(NDSDataLakeTenantSettingsView.builder().maxConnections(1000).build());
    Response response2 = _resource.getTenantSettings(auditInfo, tenantId2.toHexString());
    assertEquals(Response.Status.OK.getStatusCode(), response2.getStatus());
    assertEquals(
        NDSDataLakeTenantSettingsView.builder().maxConnections(1000).build(),
        response2.getEntity());

    try {
      _resource.getTenantSettings(auditInfo, "badTenantIdAbortAbort!");
    } catch (IllegalArgumentException e) {
      assertEquals("state should be: hexString has 24 characters", e.getMessage());
    }

    final ObjectId tenantId3 = new ObjectId();
    when(_ndsDataLakePrivateSvc.getTenantSettings(tenantId3))
        .thenReturn(
            NDSDataLakeTenantSettingsView.builder()
                .maxConnections(10)
                .queryExecutionBranchingDepth(2)
                .build());
    Response response3 = _resource.getTenantSettings(auditInfo, tenantId3.toHexString());
    assertEquals(Response.Status.OK.getStatusCode(), response3.getStatus());
    assertEquals(
        NDSDataLakeTenantSettingsView.builder()
            .maxConnections(10)
            .queryExecutionBranchingDepth(2)
            .build(),
        response3.getEntity());
  }

  @Test
  public void testGetTenantStorageConfig() throws Exception {
    final ObjectId tenantId1 = new ObjectId();
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    when(_ndsDataLakePrivateSvc.getTenantStorageConfig(tenantId1))
        .thenThrow(new SvcException(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID, tenantId1));
    try {
      _resource.getTenantStorageConfig(auditInfo, tenantId1.toHexString());
    } catch (SvcException pe) {
      assertEquals(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID, pe.getErrorCode());
    }

    final ObjectId tenantId2 = new ObjectId();
    when(_ndsDataLakePrivateSvc.getTenantStorageConfig(tenantId2))
        .thenReturn(NDSDataLakeStorageV1View.builder().build());
    Response response2 = _resource.getTenantStorageConfig(auditInfo, tenantId2.toHexString());
    assertEquals(Response.Status.OK.getStatusCode(), response2.getStatus());
    assertEquals(NDSDataLakeStorageV1View.builder().build(), response2.getEntity());

    try {
      _resource.getTenantStorageConfig(auditInfo, "badTenantIdAbortAbort!");
    } catch (IllegalArgumentException e) {
      assertEquals("state should be: hexString has 24 characters", e.getMessage());
    }

    final ObjectId tenantId3 = new ObjectId();
    when(_ndsDataLakePrivateSvc.getTenantStorageConfig(tenantId3))
        .thenReturn(NDSDataLakeStorageV1View.builder().build());
    Response response3 = _resource.getTenantStorageConfig(auditInfo, tenantId3.toHexString());
    assertEquals(Response.Status.OK.getStatusCode(), response3.getStatus());
    assertEquals(NDSDataLakeStorageV1View.builder().build(), response3.getEntity());
  }

  @Test
  public void testDeleteTenantStorageConfig() throws Exception {
    final ObjectId tenantId1 = new ObjectId();
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    doThrow(new SvcException(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID, tenantId1))
        .when(_ndsDataLakePrivateSvc)
        .deleteTenantStorageConfig((tenantId1));
    try {
      Response response = _resource.deleteTenantStorageConfig(auditInfo, tenantId1.toHexString());
      response.close();
    } catch (SvcException pe) {
      assertEquals(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID, pe.getErrorCode());
    }

    try {
      Response response = _resource.deleteTenantStorageConfig(auditInfo, "badTenantIdAbortAbort!");
      response.close();
    } catch (IllegalArgumentException e) {
      assertEquals("state should be: hexString has 24 characters", e.getMessage());
    }
  }

  @Test
  public void testGetTenantConfig() throws Exception {
    final ObjectId tenantId1 = new ObjectId();
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    when(_ndsDataLakePrivateSvc.getTenantConfig(tenantId1))
        .thenThrow(new SvcException(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID, tenantId1));
    try {
      Response response = _resource.getTenantConfig(auditInfo, tenantId1.toHexString());
      response.close();
    } catch (SvcException pe) {
      assertEquals(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID, pe.getErrorCode());
    }

    final ObjectId tenantId2 = new ObjectId();
    when(_ndsDataLakePrivateSvc.getTenantConfig(tenantId2))
        .thenReturn(ApiPrivateAtlasDataLakeTenantConfigView.builder().build());
    Response response2 = _resource.getTenantConfig(auditInfo, tenantId2.toHexString());
    assertEquals(Response.Status.OK.getStatusCode(), response2.getStatus());
    assertEquals(ApiPrivateAtlasDataLakeTenantConfigView.builder().build(), response2.getEntity());

    try {
      Response response = _resource.getTenantConfig(auditInfo, "badTenantIdAbortAbort!");
      response.close();
    } catch (IllegalArgumentException e) {
      assertEquals("state should be: hexString has 24 characters", e.getMessage());
    }

    final ObjectId tenantId3 = new ObjectId();
    when(_ndsDataLakePrivateSvc.getTenantConfig(tenantId3))
        .thenReturn(ApiPrivateAtlasDataLakeTenantConfigView.builder().build());
    Response response3 = _resource.getTenantConfig(auditInfo, tenantId3.toHexString());
    assertEquals(Response.Status.OK.getStatusCode(), response3.getStatus());
    assertEquals(ApiPrivateAtlasDataLakeTenantConfigView.builder().build(), response3.getEntity());
  }

  @Test
  public void testListDataSets() throws Exception {
    final Date now = new Date();
    final ObjectId projectId1 = new ObjectId();
    final ObjectId projectId2 = new ObjectId();

    when(_ndsDataLakePrivateSvc.listDataSets(""))
        .thenReturn(
            List.of(
                NDSDataLakeDataSetView.builder()
                    .dataSetName("alpha-dataset")
                    .dataSetLocationProvider("aws")
                    .dataSetLocationRegion("us-east-1")
                    .createdAt(now)
                    .deletedAt(null)
                    .projectId(projectId1)
                    .enabled(true)
                    .build(),
                NDSDataLakeDataSetView.builder()
                    .dataSetName("beta-dataset")
                    .dataSetLocationProvider("gcp")
                    .dataSetLocationRegion("us-central1")
                    .createdAt(now)
                    .deletedAt(now)
                    .projectId(projectId2)
                    .enabled(false)
                    .build()));

    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();
    final Response response = _resource.listDataSets(auditInfo, "");
    assertEquals(Response.Status.OK.getStatusCode(), response.getStatus());

    final Object entity = response.getEntity();
    assertInstanceOf(List.class, entity);
    final List<?> entityList = (List<?>) entity;
    assertTrue(entityList.stream().allMatch(element -> element instanceof NDSDataLakeDataSetView));
    final List<NDSDataLakeDataSetView> dataSetViews =
        entityList.stream()
            .map(element -> (NDSDataLakeDataSetView) element)
            .collect(Collectors.toList());
    dataSetViews.sort(Comparator.comparing(NDSDataLakeDataSetView::getDataSetName));

    assertEquals(2, dataSetViews.size());

    final NDSDataLakeDataSetView resultDs1 = dataSetViews.get(0);
    assertEquals("alpha-dataset", resultDs1.getDataSetName());
    assertEquals("aws", resultDs1.getDataSetLocationProvider());
    assertEquals("us-east-1", resultDs1.getDataSetLocationRegion());
    assertEquals(now, resultDs1.getCreatedAt());
    assertNull(resultDs1.getDeletedAt());
    assertEquals(projectId1, resultDs1.getProjectId());
    assertTrue(resultDs1.getEnabled());

    final NDSDataLakeDataSetView resultDs2 = dataSetViews.get(1);
    assertEquals("beta-dataset", resultDs2.getDataSetName());
    assertEquals("gcp", resultDs2.getDataSetLocationProvider());
    assertEquals("us-central1", resultDs2.getDataSetLocationRegion());
    assertEquals(now, resultDs2.getCreatedAt());
    assertEquals(now, resultDs2.getDeletedAt());
    assertEquals(projectId2, resultDs2.getProjectId());
    assertFalse(resultDs2.getEnabled());
  }

  @Test
  public void testListCurrentOps() throws Exception {
    final ObjectId tenantId1 = new ObjectId();
    final AuditInfo auditInfo = AuditInfoHelpers.fromSystem();

    when(_ndsDataLakePrivateSvc.listCurrentOps(auditInfo, tenantId1))
        .thenThrow(new SvcException(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID, tenantId1));
    try {
      Response response = _resource.listCurrentOps(auditInfo, tenantId1.toHexString());
      response.close();
    } catch (SvcException pe) {
      assertEquals(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID, pe.getErrorCode());
    }

    final ObjectId tenantId2 = new ObjectId();
    when(_ndsDataLakePrivateSvc.listCurrentOps(auditInfo, tenantId2))
        .thenReturn(
            List.of(
                NDSDataLakeCurrentOpView.builder().correlationId("id1").build(),
                NDSDataLakeCurrentOpView.builder().correlationId("id2").build()));
    Response response2 = _resource.listCurrentOps(auditInfo, tenantId2.toHexString());
    assertEquals(Response.Status.OK.getStatusCode(), response2.getStatus());

    final Object entity = response2.getEntity();
    assertInstanceOf(List.class, entity);
    final List<?> entityList = (List<?>) entity;
    assertTrue(
        entityList.stream().allMatch(element -> element instanceof NDSDataLakeCurrentOpView));

    final List<NDSDataLakeCurrentOpView> currentOps =
        entityList.stream()
            .map(element -> (NDSDataLakeCurrentOpView) element)
            .sorted(Comparator.comparing(NDSDataLakeCurrentOpView::getCorrelationId))
            .toList();

    assertEquals(2, entityList.size());
    NDSDataLakeCurrentOpView currentOp1 = currentOps.get(0);
    assertEquals("id1", currentOp1.getCorrelationId());
    NDSDataLakeCurrentOpView currentOp2 = currentOps.get(1);
    assertEquals("id2", currentOp2.getCorrelationId());

    try {
      Response response = _resource.listCurrentOps(auditInfo, "badTenantIdAbortAbort!");
      response.close();
    } catch (IllegalArgumentException e) {
      assertEquals("state should be: hexString has 24 characters", e.getMessage());
    }
  }
}
