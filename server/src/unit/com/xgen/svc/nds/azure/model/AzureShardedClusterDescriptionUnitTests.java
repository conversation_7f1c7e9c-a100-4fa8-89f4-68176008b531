package com.xgen.svc.nds.azure.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.deployment._public.model.diff.ItemDiff;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.autoscaling.AzureAutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleSingleCloudInstanceSize;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.svc.nds.model.ClusterDescriptionBuilderTestMixin;
import com.xgen.svc.nds.model.CrossCloudReplicationSpecModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import java.util.Optional;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(CloudProviderExtension.class)
public class AzureShardedClusterDescriptionUnitTests {
  @Test
  public void testConstructor() {
    final BasicDBObject doc = NDSModelTestFactory.getAzureShardedClusterDescription();

    final ShardedClusterDescription clusterDescription = new ShardedClusterDescription(doc);

    final AzureHardwareSpec hardwareSpec =
        (AzureHardwareSpec)
            clusterDescription
                .getOnlyHardwareSpecForProvider(CloudProvider.AZURE, NodeType.ELECTABLE)
                .orElseThrow();
    final AzureHardwareSpec configHardwareSpec =
        (AzureHardwareSpec)
            clusterDescription
                .getDedicatedConfigServerReplicationSpec()
                .get()
                .getElectableHardwareSpecByCloudProvider(CloudProvider.AZURE);

    final BasicDBObject regionConfig =
        ((BasicDBObject)
            ((BasicDBList)
                    ((BasicDBObject) ((BasicDBList) doc.get("replicationSpecList")).get(0))
                        .get("regionConfigs"))
                .get(0));

    final BasicDBObject csRegionConfig =
        ((BasicDBObject)
            ((BasicDBList)
                    ((BasicDBObject) doc.get("configServerReplicationSpec")).get("regionConfigs"))
                .get(0));

    assertFalse(
        ((BasicDBObject) regionConfig.get("electableSpecs")).getBoolean("isConfigServerHardware"));
    assertEquals(
        ((BasicDBObject) regionConfig.get("electableSpecs")).getString("instanceSize"),
        hardwareSpec.getInstanceSize().name());
    assertEquals(
        ((BasicDBObject) regionConfig.get("electableSpecs")).getString("instanceFamily"),
        hardwareSpec.getInstanceFamily().getName());
    assertEquals(
        ((BasicDBObject) regionConfig.get("electableSpecs")).getString("diskType"),
        hardwareSpec.getDiskType().name());

    assertFalse(
        ((BasicDBObject) regionConfig.get("readOnlySpecs")).getBoolean("isConfigServerHardware"));
    assertEquals(
        ((BasicDBObject) regionConfig.get("readOnlySpecs")).getString("instanceSize"),
        hardwareSpec.getInstanceSize().name());
    assertEquals(
        ((BasicDBObject) regionConfig.get("readOnlySpecs")).getString("instanceFamily"),
        hardwareSpec.getInstanceFamily().getName());
    assertEquals(
        ((BasicDBObject) regionConfig.get("readOnlySpecs")).getString("diskType"),
        hardwareSpec.getDiskType().name());

    assertFalse(
        ((BasicDBObject) regionConfig.get("analyticsSpecs")).getBoolean("isConfigServerHardware"));
    assertEquals(
        ((BasicDBObject) regionConfig.get("analyticsSpecs")).getString("instanceSize"),
        hardwareSpec.getInstanceSize().name());
    assertEquals(
        ((BasicDBObject) regionConfig.get("analyticsSpecs")).getString("instanceFamily"),
        hardwareSpec.getInstanceFamily().getName());
    assertEquals(
        ((BasicDBObject) regionConfig.get("analyticsSpecs")).getString("diskType"),
        hardwareSpec.getDiskType().name());

    assertTrue(
        ((BasicDBObject) csRegionConfig.get("electableSpecs"))
            .getBoolean("isConfigServerHardware"));
    assertEquals(
        ((BasicDBObject) csRegionConfig.get("electableSpecs"))
            .getString("configServerInstanceSize"),
        configHardwareSpec.getInstanceSize().name());
    assertEquals(
        ((BasicDBObject) csRegionConfig.get("electableSpecs"))
            .getString("configServerInstanceFamily"),
        configHardwareSpec.getInstanceFamily().getName());
    assertEquals(
        ((BasicDBObject) csRegionConfig.get("electableSpecs")).getString("configServerDiskType"),
        configHardwareSpec.getDiskType().name());
    assertEquals(
        AzureDiskType.valueOf(
                ((BasicDBObject) csRegionConfig.get("electableSpecs"))
                    .getString("configServerDiskType"))
            .getSizeGB(),
        (int) clusterDescription.getDedicatedConfigServerDiskSizeGB());

    assertEquals(
        new AzureAutoScaling((BasicDBObject) regionConfig.get("autoScaling")),
        clusterDescription.getAutoScaling(NodeTypeFamily.BASE));
  }

  @Test
  public void testGetAutoScaling() {
    final BasicDBObject descDoc = NDSModelTestFactory.getAzureShardedClusterDescription();
    final ShardedClusterDescription clusterDescription = new ShardedClusterDescription(descDoc);
    assertNotNull(clusterDescription.getAutoScaling(NodeTypeFamily.BASE));
  }

  @Test
  public void testDiffs() {
    final BasicDBObject descDoc = NDSModelTestFactory.getAzureShardedClusterDescription();
    final ShardedClusterDescription clusterDescription = new ShardedClusterDescription(descDoc);

    ItemDiff diff = clusterDescription.getNewClusterDiff();
    assertTrue(
        diff.getItems().stream()
            .anyMatch(
                i -> i.getParamName().equals(ShardedClusterDescription.FieldDefs.INSTANCE_SIZE)));

    diff = clusterDescription.getDeletedClusterDiff();
    assertTrue(
        diff.getItems().stream()
            .anyMatch(
                i -> i.getParamName().equals(ShardedClusterDescription.FieldDefs.INSTANCE_SIZE)));

    diff = clusterDescription.getUpdatedClusterDiff(clusterDescription);
    assertEquals(0, diff.getItems().size());

    final ShardedClusterDescription newClusterDescription =
        clusterDescription
            .copy()
            .setReplicationSpecList(
                clusterDescription.getReplicationSpecsWithShardData().stream()
                    .map(
                        rs ->
                            CrossCloudReplicationSpecModelTestFactory
                                .updateElectableAndAnalyticsSpecs(rs, AzureNDSInstanceSize.M50))
                    .collect(Collectors.toList()))
            .build();
    diff = clusterDescription.getUpdatedClusterDiff(newClusterDescription);
    assertTrue(
        diff.getItems().stream()
            .anyMatch(
                i -> i.getParamName().equals(ShardedClusterDescription.FieldDefs.INSTANCE_SIZE)));
  }

  @Test
  public void testPerformComputeAutoScaleForCluster() throws Exception {
    final ShardedClusterDescription clusterDescription =
        new ShardedClusterDescription(NDSModelTestFactory.getAzureShardedClusterDescription());

    // test fail -- wrong type
    try {
      final Optional<ClusterDescription> scaledClusterDescription =
          clusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M20), NodeTypeFamily.BASE);
      fail();
    } catch (final Exception pE) {
      assertTrue(pE instanceof IllegalArgumentException);
    }

    // test fail -- equal instance sizes
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          clusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  clusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow()),
              NodeTypeFamily.BASE);
      assertTrue(scaledClusterDescription.isEmpty());
    }

    // test success -- scale up
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          clusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  clusterDescription
                      .getOnlyInstanceSize(NodeType.ELECTABLE)
                      .orElseThrow()
                      .getNextInstanceSize()
                      .get()),
              NodeTypeFamily.BASE);
      assertEquals(
          clusterDescription
              .getOnlyInstanceSize(NodeType.ELECTABLE)
              .orElseThrow()
              .getNextInstanceSize()
              .get(),
          scaledClusterDescription.get().getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());
    }

    // test success -- scale down
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          clusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  clusterDescription
                      .getOnlyInstanceSize(NodeType.ELECTABLE)
                      .orElseThrow()
                      .getPreviousInstanceSize()
                      .get()),
              NodeTypeFamily.BASE);
      assertEquals(
          clusterDescription
              .getOnlyInstanceSize(NodeType.ELECTABLE)
              .orElseThrow()
              .getPreviousInstanceSize()
              .get(),
          scaledClusterDescription.get().getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());
    }

    final ShardedClusterDescription asymmetricClusterDescription =
        clusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AzureHardwareSpec.Builder()
                    .setNodeCount(1)
                    .setInstanceSize(AzureNDSInstanceSize.M50),
                NodeTypeFamily.ANALYTICS)
            .build();

    // test success -- scale up NodeTypeFamily=ANALYTICS
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          asymmetricClusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  asymmetricClusterDescription
                      .getOnlyInstanceSize(NodeType.ANALYTICS)
                      .orElseThrow()
                      .getNextInstanceSize()
                      .get()),
              NodeTypeFamily.ANALYTICS);
      assertEquals(
          asymmetricClusterDescription
              .getOnlyInstanceSize(NodeType.ANALYTICS)
              .orElseThrow()
              .getNextInstanceSize()
              .get(),
          scaledClusterDescription.get().getOnlyInstanceSize(NodeType.ANALYTICS).orElseThrow());
      assertNotEquals(
          asymmetricClusterDescription
              .getOnlyInstanceSize(NodeType.ANALYTICS)
              .orElseThrow()
              .getNextInstanceSize()
              .get(),
          scaledClusterDescription.get().getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());
    }

    // test success -- scale down NodeTypeFamily=ANALYTICS
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          asymmetricClusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  asymmetricClusterDescription
                      .getOnlyInstanceSize(NodeType.ANALYTICS)
                      .orElseThrow()
                      .getPreviousInstanceSize()
                      .get()),
              NodeTypeFamily.ANALYTICS);
      assertEquals(
          asymmetricClusterDescription
              .getOnlyInstanceSize(NodeType.ANALYTICS)
              .orElseThrow()
              .getPreviousInstanceSize()
              .get(),
          scaledClusterDescription.get().getOnlyInstanceSize(NodeType.ANALYTICS).orElseThrow());
      assertNotEquals(
          asymmetricClusterDescription
              .getOnlyInstanceSize(NodeType.ANALYTICS)
              .orElseThrow()
              .getPreviousInstanceSize()
              .get(),
          scaledClusterDescription.get().getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());
    }
  }
}
