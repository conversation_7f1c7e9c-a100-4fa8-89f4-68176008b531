package com.xgen.svc.nds.onlinearchive.restorationtool.jobs;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.ReadChannel;
import com.google.cloud.storage.Blob;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.jobqueue._public.svc.JobHandlerSvc;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.gcp._public.model.error.GCPApiException;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.gcp._public.util.GCPProviderFactory;
import com.xgen.cloud.nds.onlinearchive._private.dao.RestoreDLSJobDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.RestoreOADatasetRequestDao;
import com.xgen.cloud.nds.onlinearchive._private.settings.OnlineArchiveSettings;
import com.xgen.cloud.nds.onlinearchive._public.model.DataLandingZoneConfig.GCPDataLandingZoneConfig;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DataProcessRegion;
import com.xgen.cloud.nds.onlinearchive._public.model.RestoreDLSJob;
import com.xgen.cloud.nds.onlinearchive._public.model.RestoreOADatasetRequest;
import com.xgen.mhouse.backend.grpc.Models.UploadDataFilesResponse;
import com.xgen.svc.mms.util.UnitTestUtils;
import com.xgen.svc.nds.model.onlinearchiveagentview.GetJobProgressResponseView;
import com.xgen.svc.nds.onlinearchive.restorationtool.svc.OnlineArchiveRestorationSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiException;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OnlineArchiveRestoreGCPJobHandlerUnitTests {

  @Mock private OnlineArchiveSettings _onlineArchiveSettings;
  @Mock private RestoreDLSJobDao _restoreDLSJobDao;
  @Mock private JobHandlerSvc _jobHandlerSvc;
  @Mock private OnlineArchiveRestorationSvc _onlineArchiveRestorationSvc;
  @Mock private OnlineArchiveSvc _onlineArchiveSvc;
  @Mock private DataLakeAdminApiClient _dataLakeAdminApiClient;
  @Mock private RestoreOADatasetRequestDao _restoreOADatasetRequestDao;
  private MockedStatic<GCPApiSvc> _gcpApiSvc;

  private OnlineArchiveRestoreGCPJobHandler _gcpHandler;

  @BeforeEach
  public void setUp() {
    GCPProviderFactory.registerProvider();

    _gcpHandler =
        spy(
            UnitTestUtils.create(OnlineArchiveRestoreGCPJobHandler.class)
                .withArgs(
                    _onlineArchiveSettings,
                    _restoreDLSJobDao,
                    _jobHandlerSvc,
                    _onlineArchiveRestorationSvc,
                    _onlineArchiveSvc,
                    _dataLakeAdminApiClient,
                    _restoreOADatasetRequestDao));
    _gcpApiSvc = mockStatic(GCPApiSvc.class);
  }

  @AfterEach
  public void tearDown() {
    _gcpApiSvc.close();
  }

  @Test
  public void testHandleWork() throws Exception {
    final ObjectId mmsJobQueueId = ObjectId.get();
    doReturn(2).when(_onlineArchiveSettings).getOnlineArchiveUnfinishedRestoreDLSJobThresholdDays();

    // failure - unable to retrieve parameters
    try {
      _gcpHandler.handleWork(null, mmsJobQueueId);
      fail();
    } catch (IllegalArgumentException pE) {
      assertTrue(pE.getMessage().contains("Restoration job request ID"));
    }

    // failure - unable to find restoration job request
    final ObjectId restorationJobRequestId = ObjectId.get();
    final BasicDBObject params =
        new BasicDBObject(
            OnlineArchiveRestoreJobHandler.RESTORATION_JOB_REQUEST_ID_FIELD,
            restorationJobRequestId);

    _gcpHandler.handleWork(params, mmsJobQueueId);
    verify(_gcpHandler, times(1)).getRestorationJobRequest(eq(restorationJobRequestId));
    verify(_gcpHandler, times(1))
        .handleTerminalError(eq(restorationJobRequestId), eq(mmsJobQueueId), any());
    verify(_onlineArchiveRestorationSvc, times(1)).incrementErrorCounter(any(), any());
    verify(_restoreDLSJobDao, times(1))
        .setFailed(
            eq(restorationJobRequestId),
            any(),
            contains("Failed to find restoration dls job request"));
    verify(_onlineArchiveSvc, never()).getOnlineArchive(any());
    verify(_restoreDLSJobDao, never()).setSucceeded(any(), any());

    // failure - unable to find restoration request
    final RestoreDLSJob restoreDLSJob = mock(RestoreDLSJob.class);
    doReturn(Optional.of(restoreDLSJob)).when(_restoreDLSJobDao).find(eq(restorationJobRequestId));
    doReturn(new DataProcessRegion("GCP", "CENTRAL_US")).when(restoreDLSJob).getDataProcessRegion();
    final GetJobProgressResponseView jobProgress = mock(GetJobProgressResponseView.class);
    doReturn(jobProgress).when(_dataLakeAdminApiClient).getJobProgress(any(), any());
    doReturn(1).when(jobProgress).getErrorCount();
    final OnlineArchive archive = mock(OnlineArchive.class);
    doReturn(archive).when(_onlineArchiveSvc).getValidateOnlineArchive(any());

    _gcpHandler.handleWork(params, mmsJobQueueId);
    verify(_gcpHandler, times(2))
        .handleTerminalError(eq(restorationJobRequestId), eq(mmsJobQueueId), any());
    verify(_onlineArchiveRestorationSvc, times(2)).incrementErrorCounter(any(), any());
    verify(_restoreDLSJobDao, times(1))
        .setFailed(
            eq(restorationJobRequestId), any(), contains("Failed to find restoration request"));
    verify(_restoreDLSJobDao, never()).setSucceeded(any(), any());

    // failure - archive not paused when paused for restoration
    final ObjectId restoreOADatasetRequestId = ObjectId.get();
    doReturn(restoreOADatasetRequestId).when(restoreDLSJob).getRestoreOADatasetRequestId();
    final RestoreOADatasetRequest restoreOADatasetRequest = mock(RestoreOADatasetRequest.class);
    doReturn(Optional.of(restoreOADatasetRequest))
        .when(_restoreOADatasetRequestDao)
        .find(eq(restoreOADatasetRequestId));
    doReturn(true).when(restoreOADatasetRequest).getIsArchivePausedForRestoration();
    doReturn(OnlineArchive.State.ACTIVE).when(archive).getState();

    _gcpHandler.handleWork(params, mmsJobQueueId);
    verify(_gcpHandler, times(3))
        .handleTerminalError(eq(restorationJobRequestId), eq(mmsJobQueueId), any());
    verify(_onlineArchiveRestorationSvc, times(3)).incrementErrorCounter(any(), any());
    verify(_restoreDLSJobDao, times(1))
        .setFailed(
            eq(restorationJobRequestId),
            any(),
            contains("Archive is not in required PAUSED state for restoration"));
    verify(archive, never()).getCurrentArchiveRun();
    verify(_restoreDLSJobDao, never()).setSucceeded(any(), any());

    // failure - job being recovered is active archive run
    doReturn(false).when(restoreOADatasetRequest).getIsArchivePausedForRestoration();
    final String archiveRunJobId = ObjectId.get().toString();
    doReturn(archiveRunJobId).when(restoreDLSJob).getOriginalJobId();
    final OnlineArchive.ArchiveRun currentArchiveRun = mock(OnlineArchive.ArchiveRun.class);
    doReturn(archiveRunJobId).when(currentArchiveRun).getJobId();
    doReturn(Optional.of(currentArchiveRun)).when(archive).getCurrentArchiveRun();
    doReturn(OnlineArchive.ArchiveRunState.UPLOADING_TO_DLS).when(currentArchiveRun).getState();

    _gcpHandler.handleWork(params, mmsJobQueueId);
    verify(_gcpHandler, times(4))
        .handleTerminalError(eq(restorationJobRequestId), eq(mmsJobQueueId), any());
    verify(_onlineArchiveRestorationSvc, times(4)).incrementErrorCounter(any(), any());
    verify(_restoreDLSJobDao, times(1))
        .setFailed(
            eq(restorationJobRequestId),
            any(),
            contains("Original archive run is still in progress for job"));
    verify(_gcpHandler, never()).recoverJobDLZFiles(any());
    verify(_restoreDLSJobDao, never()).setSucceeded(any(), any());

    // not all files recovered - process job as waiting
    doReturn(ObjectId.get().toString()).when(currentArchiveRun).getJobId();
    doNothing().when(_gcpHandler).recoverJobDLZFiles(any());
    doReturn(List.of()).when(restoreDLSJob).getRecoveredFilePaths();
    doReturn("expectedFilePath").when(restoreDLSJob).getOriginalManifestPath();
    doReturn(15).when(restoreDLSJob).getWaitForDLZRecoveryIntervalMinutes();
    doReturn(new Date()).when(restoreDLSJob).getDlzRecoveryStartedOn();

    _gcpHandler.handleWork(params, mmsJobQueueId);
    verify(_jobHandlerSvc, times(1)).processJobAsWaitingForEvent(eq(mmsJobQueueId), eq(900000L));
    verify(_gcpHandler, never()).createAndUploadNewManifestFile(any());
    verify(_gcpHandler, never()).uploadToDLS(any(), any());
    verify(_restoreDLSJobDao, never()).setSucceeded(any(), any());

    // recovery taking longer than unfinished recovery threshold days
    doReturn(DateUtils.addDays(new Date(), -3)).when(restoreDLSJob).getDlzRecoveryStartedOn();
    _gcpHandler.handleWork(params, mmsJobQueueId);
    verify(_gcpHandler, times(1)).incrementCounter(any(), any(), any());

    // upload to dls not finished - process job as waiting
    doReturn(List.of("expectedFilePath")).when(restoreDLSJob).getRecoveredFilePaths();
    doReturn(new Date()).when(restoreDLSJob).getDlzRecoveryCompletedOn();
    doNothing().when(_gcpHandler).createAndUploadNewManifestFile(any());
    doNothing().when(_gcpHandler).uploadToDLS(any(), any());

    doReturn(false).when(_gcpHandler).hasUploadDLSJobCompleted(any());
    doReturn(10).when(restoreDLSJob).getWaitForDLSUploadIntervalMinutes();

    _gcpHandler.handleWork(params, mmsJobQueueId);
    verify(_jobHandlerSvc, times(1)).processJobAsWaitingForEvent(eq(mmsJobQueueId), eq(600000L));
    verify(_gcpHandler, times(1)).createAndUploadNewManifestFile(any());
    verify(_gcpHandler, times(1)).uploadToDLS(any(), any());
    verify(_restoreDLSJobDao, never()).setSucceeded(any(), any());

    // success
    doReturn(new Date()).when(restoreDLSJob).getDlzNewJobManifestUploadedOn();
    doReturn(new Date()).when(restoreDLSJob).getDlsUploadJobSubmittedOn();
    doReturn(true).when(_gcpHandler).hasUploadDLSJobCompleted(any());

    _gcpHandler.handleWork(params, mmsJobQueueId);
    verify(_gcpHandler, times(1)).createAndUploadNewManifestFile(any());
    verify(_gcpHandler, times(1)).uploadToDLS(any(), any());
    verify(_restoreDLSJobDao, times(1)).setSucceeded(any(), any());
    verify(_jobHandlerSvc, times(1)).processJobAsComplete(eq(mmsJobQueueId));
  }

  @Test
  public void testRecoverJobDLZFiles() {
    final String archivePrefixPath = "archivePath";
    final String manifestPath = "manifestPath";
    final String originalJobId = "jobId";

    final RestoreDLSJob restoreDLSJob = mock(RestoreDLSJob.class);
    doReturn(originalJobId).when(restoreDLSJob).getOriginalJobId();
    doReturn(manifestPath).when(restoreDLSJob).getOriginalManifestPath();

    final GCPDataLandingZoneConfig dlzConfig = mock(GCPDataLandingZoneConfig.class);
    doReturn(dlzConfig).when(restoreDLSJob).getDataLandingZoneConfig();
    doReturn(archivePrefixPath).when(dlzConfig).getPathPrefix();

    doReturn(List.of(manifestPath, "dataPath"))
        .when(_gcpHandler)
        .getAllPossibleDLZGCPPathsForArchiveJob(
            eq(archivePrefixPath), eq(originalJobId), eq(manifestPath));

    doNothing().when(_restoreDLSJobDao).updateRecoveredFilePaths(any(), any());
    doNothing().when(_restoreDLSJobDao).setDLZRecoveryCompleted(any(), any());

    // all paths marked recovered
    _gcpHandler.recoverJobDLZFiles(restoreDLSJob);
    verify(_restoreDLSJobDao, times(1)).startDLZRecovery(any(), any());
    verify(_restoreDLSJobDao, times(2)).updateRecoveredFilePaths(any(), any());
    verify(_restoreDLSJobDao, times(1)).setDLZRecoveryCompleted(any(), any());
  }

  @Test
  public void testCreateAndUploadNewManifestFile() throws Exception {
    final RestoreDLSJob restoreDLSJob = mock(RestoreDLSJob.class);
    final String existingManifestPath = "existing";
    final String newManifestPath = "new";
    final String bucket = "bucket";
    final DataProcessRegion dataProcessRegion = new DataProcessRegion("GCP", "CENTRAL_US");
    final GoogleCredentials googleCredentials = mock(GoogleCredentials.class);
    final GCPDataLandingZoneConfig dlzConfig = mock(GCPDataLandingZoneConfig.class);
    doReturn(bucket).when(dlzConfig).getDataLandingZoneBucketName();
    doReturn(dlzConfig).when(restoreDLSJob).getDataLandingZoneConfig();
    doReturn(existingManifestPath).when(restoreDLSJob).getOriginalManifestPath();
    doReturn(newManifestPath).when(restoreDLSJob).getNewManifestPath();
    doReturn(dataProcessRegion).when(restoreDLSJob).getDataProcessRegion();
    doReturn(googleCredentials).when(_onlineArchiveRestorationSvc).getGCPDLZCredentials(any());

    final Blob blob = mock(Blob.class);
    _gcpApiSvc
        .when(() -> GCPApiSvc.listBlobs(eq(googleCredentials), any(), any()))
        .thenReturn(List.of(blob));

    final ReadChannel readChannel = mock(ReadChannel.class);
    doReturn(readChannel).when(blob).reader();

    final String newManifest = "new-manifest";
    doReturn(newManifest)
        .when(_gcpHandler)
        .createNewGCPManifestFile(eq(restoreDLSJob), any(), eq(bucket), any(), eq(blob));

    doNothing()
        .when(_gcpHandler)
        .uploadNewManifestFile(
            eq(restoreDLSJob),
            eq(googleCredentials),
            eq(bucket),
            eq(newManifestPath),
            eq(newManifest));

    _gcpHandler.createAndUploadNewManifestFile(restoreDLSJob);
    verify(_restoreDLSJobDao, times(1)).setNewJobManifestUploadedOn(any(), any());
  }

  @Test
  public void testListExistingManifestFile() {
    final RestoreDLSJob restoreDLSJob = mock(RestoreDLSJob.class);
    final String existingManifestPath = "existing";
    final String bucket = "bucket";
    final GoogleCredentials googleCredentials = mock(GoogleCredentials.class);

    _gcpApiSvc
        .when(
            () -> GCPApiSvc.listBlobs(eq(googleCredentials), eq(bucket), eq(existingManifestPath)))
        .thenThrow(new GCPApiException(CommonErrorCode.SERVER_ERROR));

    try {
      _gcpHandler.listExistingManifestFile(
          restoreDLSJob, googleCredentials, bucket, existingManifestPath);
      fail();
    } catch (final IllegalStateException pE) {
      _gcpApiSvc.verify(
          () -> GCPApiSvc.listBlobs(eq(googleCredentials), eq(bucket), eq(existingManifestPath)),
          times(5));
      assertTrue(
          pE.getMessage()
              .contains("Exception thrown while listing existing manifest file for path"));
    }

    final ArrayList<Blob> blobList = new ArrayList<>();
    final Blob blob = mock(Blob.class);
    _gcpApiSvc
        .when(
            () -> GCPApiSvc.listBlobs(eq(googleCredentials), eq(bucket), eq(existingManifestPath)))
        .thenReturn(blobList);

    try {
      _gcpHandler.listExistingManifestFile(
          restoreDLSJob, googleCredentials, bucket, existingManifestPath);
      fail();
    } catch (final IllegalStateException pE) {
      _gcpApiSvc.verify(
          () -> GCPApiSvc.listBlobs(eq(googleCredentials), eq(bucket), eq(existingManifestPath)),
          times(6));
      assertTrue(pE.getMessage().contains("No manifest file found for path"));
    }

    blobList.add(blob);

    assertEquals(
        blob,
        _gcpHandler.listExistingManifestFile(
            restoreDLSJob, googleCredentials, bucket, existingManifestPath));
  }

  @Test
  public void testUploadNewManifestFile() {
    final RestoreDLSJob restoreDLSJob = mock(RestoreDLSJob.class);
    final String newManifestPath = "new";
    final String bucket = "bucket";
    final String manifest = "manifest";
    final GoogleCredentials googleCredentials = mock(GoogleCredentials.class);

    _gcpApiSvc
        .when(
            () ->
                GCPApiSvc.uploadBlob(eq(googleCredentials), eq(bucket), eq(newManifestPath), any()))
        .thenThrow(new GCPApiException(CommonErrorCode.SERVER_ERROR));

    try {
      _gcpHandler.uploadNewManifestFile(
          restoreDLSJob, googleCredentials, bucket, newManifestPath, manifest);
      fail();
    } catch (final IllegalStateException pE) {
      _gcpApiSvc.verify(
          () ->
              GCPApiSvc.uploadBlob(
                  eq(googleCredentials), eq(bucket), eq(newManifestPath), eq(manifest.getBytes())),
          times(5));
      assertTrue(
          pE.getMessage().contains("Exception thrown while uploading new manifest file for path"));
    }
  }

  @Test
  public void testUploadToDLS() throws Exception {
    final RestoreDLSJob restoreDLSJob = mock(RestoreDLSJob.class);
    final DataProcessRegion dataProcessRegion = new DataProcessRegion("GCP", "CENTRAL_US");
    final ObjectId groupId = ObjectId.get();
    final String datasetName = "dataset";
    final String bucketName = "bucket";
    final String newManifestPath = "new";
    final String newJobId = "jobId";
    final GCPDataLandingZoneConfig dlzConfig = mock(GCPDataLandingZoneConfig.class);
    doReturn(bucketName).when(dlzConfig).getDataLandingZoneBucketName();
    doReturn(dlzConfig).when(restoreDLSJob).getDataLandingZoneConfig();
    doReturn(dataProcessRegion).when(restoreDLSJob).getDataProcessRegion();
    doReturn(datasetName).when(restoreDLSJob).getTargetDatasetName();
    doReturn(newManifestPath).when(restoreDLSJob).getNewManifestPath();
    doReturn(newJobId).when(restoreDLSJob).getNewJobId();

    // terminal exception
    doThrow(
            new DataLakeAdminApiException(
                NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
                new StatusRuntimeException(Status.INVALID_ARGUMENT)))
        .when(_dataLakeAdminApiClient)
        .uploadDataFiles(
            eq(groupId),
            eq(datasetName),
            any(),
            any(),
            any(),
            any(),
            eq(bucketName),
            eq(newManifestPath),
            eq(newJobId),
            any());

    try {
      _gcpHandler.uploadToDLS(restoreDLSJob, groupId);
      fail();
    } catch (final Exception pE) {
      verify(_dataLakeAdminApiClient, times(1))
          .uploadDataFiles(
              eq(groupId),
              eq(datasetName),
              any(),
              any(),
              any(),
              any(),
              eq(bucketName),
              eq(newManifestPath),
              eq(newJobId),
              any());
      assertTrue(pE.getMessage().contains("Unexpected error"));
    }

    // retryable exception
    doThrow(
            new DataLakeAdminApiException(
                NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
                new StatusRuntimeException(Status.INTERNAL)))
        .when(_dataLakeAdminApiClient)
        .uploadDataFiles(
            eq(groupId),
            eq(datasetName),
            any(),
            any(),
            any(),
            any(),
            eq(bucketName),
            eq(newManifestPath),
            eq(newJobId),
            any());

    try {
      _gcpHandler.uploadToDLS(restoreDLSJob, groupId);
      fail();
    } catch (final Exception pE) {
      verify(_dataLakeAdminApiClient, times(6))
          .uploadDataFiles(
              eq(groupId),
              eq(datasetName),
              any(),
              any(),
              any(),
              any(),
              eq(bucketName),
              eq(newManifestPath),
              eq(newJobId),
              any());
      assertTrue(pE.getMessage().contains("Unexpected error"));
    }

    final UploadDataFilesResponse response = mock(UploadDataFilesResponse.class);
    doReturn(response)
        .when(_dataLakeAdminApiClient)
        .uploadDataFiles(
            eq(groupId),
            eq(datasetName),
            any(),
            any(),
            any(),
            any(),
            eq(bucketName),
            eq(newManifestPath),
            eq(newJobId),
            any());

    _gcpHandler.uploadToDLS(restoreDLSJob, groupId);
    verify(_restoreDLSJobDao, times(1)).setDlsUploadJobSubmittedOn(any(), any());
  }
}
