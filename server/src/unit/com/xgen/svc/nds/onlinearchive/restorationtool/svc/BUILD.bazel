load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*UnitTests.java"]),
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/main/com/xgen/cloud/nds/datalake/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/settings",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_public/model",
        "//server/src/main/com/xgen/module/common/planner",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)
