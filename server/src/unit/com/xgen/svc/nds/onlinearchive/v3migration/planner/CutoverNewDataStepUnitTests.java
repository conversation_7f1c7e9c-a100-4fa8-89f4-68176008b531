package com.xgen.svc.nds.onlinearchive.v3migration.planner;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.mongodb.MongoTimeoutException;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.nds.aws._public.util.AWSProviderFactory;
import com.xgen.cloud.nds.common._public.model.PartitionField;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataSetDao;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSet;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSetOA;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveRunDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.v3migration.CutoverRollbackJobDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.v3migration.OnlineArchiveV3MigrationDao;
import com.xgen.cloud.nds.onlinearchive._public.model.ArchiveRunStats;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.CollectionType;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DataProcessRegion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DateCriteria.DateFormat;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.State;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveRun;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchiveSchedule.DefaultSchedule;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.CutoverRollbackJob;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration.Phase;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration.Source;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration.Target;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.mhouse.backend.grpc.Models.InitializeDataSetResponse;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Result.NoData;
import com.xgen.module.common.planner.model.Result.Status;
import com.xgen.module.common.planner.model.Step;
import com.xgen.svc.core.MongoCursorUtils;
import com.xgen.svc.nds.onlinearchive.v3migration.svc.OnlineArchiveV3MigrationSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient.DataLakeAdminApiOperationOutcome;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiException;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveDataLakeConfigSvc;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import io.grpc.StatusRuntimeException;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CutoverNewDataStepUnitTests {

  private PlanContext _planContext;
  private Step.State _state;
  private DataLakeAdminApiClient _dataLakeAdminApiClient;
  private NDSDataSetDao _ndsDataSetDao;
  private OnlineArchiveV3MigrationSvc _onlineArchiveV3MigrationSvc;
  private OnlineArchiveV3MigrationDao _onlineArchiveV3MigrationDao;
  private OnlineArchiveSvc _onlineArchiveSvc;
  private OnlineArchiveDao _onlineArchiveDao;
  private OnlineArchiveV3Migration _onlineArchiveV3Migration;
  private CutoverRollbackJobDao _cutoverRollbackJobDao;
  private OnlineArchiveRunDao _onlineArchiveRunDao;
  private NDSClusterSvc _ndsClusterSvc;

  @BeforeEach
  public void setUp() {
    AWSProviderFactory.registerProvider();
    _planContext = mock(PlanContext.class);
    _dataLakeAdminApiClient = mock(DataLakeAdminApiClient.class);
    _ndsDataSetDao = mock(NDSDataSetDao.class);
    _onlineArchiveV3MigrationSvc = mock(OnlineArchiveV3MigrationSvc.class);
    _onlineArchiveV3MigrationDao = mock(OnlineArchiveV3MigrationDao.class);
    _onlineArchiveSvc = mock(OnlineArchiveSvc.class);
    _onlineArchiveDao = mock(OnlineArchiveDao.class);
    _onlineArchiveV3Migration = getMigration();
    _state = mock(Step.State.class);
    _cutoverRollbackJobDao = mock(CutoverRollbackJobDao.class);
    _onlineArchiveRunDao = mock(OnlineArchiveRunDao.class);
    _ndsClusterSvc = mock(NDSClusterSvc.class);

    final Logger logger = LoggerFactory.getLogger(CutoverNewDataStepUnitTests.class);
    doReturn(logger).when(_planContext).getLogger();
  }

  @Test
  public void testPerformInternal() {
    final CutoverNewDataStep step = getMockedStep();

    // skip validation
    doNothing().when(step).validateCutoverStep(_onlineArchiveV3Migration);

    // returns done if already performed
    doReturn(true).when(step).isPerformed();
    assertEquals(Result.done(), step.performInternal());

    when(step.isPerformed()).thenReturn(false);

    // returns in progress if create new V3 data set is in progress
    doReturn(Result.inProgress()).when(step).createNewV3DataSet(_onlineArchiveV3Migration);
    assertEquals(Result.inProgress(), step.performInternal());

    // returns failed if create new V3 data set has failed
    doReturn(Result.failed()).when(step).createNewV3DataSet(_onlineArchiveV3Migration);
    assertEquals(Result.failed(), step.performInternal());

    doReturn(Result.done()).when(step).createNewV3DataSet(_onlineArchiveV3Migration);
    doReturn(Optional.empty()).when(step).getPauseArchiveRequestedOn();

    // returns in progress if pause archive is in progress
    doReturn(Result.inProgress()).when(step).pauseArchive(_onlineArchiveV3Migration);
    assertEquals(Result.inProgress(), step.performInternal());

    // returns failed if pause archive has failed
    doReturn(Result.failed()).when(step).pauseArchive(_onlineArchiveV3Migration);
    assertEquals(Result.failed(), step.performInternal());

    doReturn(Result.done()).when(step).pauseArchive(_onlineArchiveV3Migration);
    doReturn(Optional.empty()).when(step).getResumeArchiveRequestedOn();

    // returns in progress if wait for pause archive is in progress
    doReturn(Result.inProgress()).when(step).waitForArchiveIsPaused(_onlineArchiveV3Migration);
    assertEquals(Result.inProgress(), step.performInternal());

    // returns failed if wait for pause archive has failed
    doReturn(Result.failed()).when(step).waitForArchiveIsPaused(_onlineArchiveV3Migration);
    assertEquals(Result.failed(), step.performInternal());

    doReturn(Result.done()).when(step).waitForArchiveIsPaused(_onlineArchiveV3Migration);

    // returns in progress if cutover archive is in progress
    doReturn(Result.inProgress()).when(step).cutoverArchive(_onlineArchiveV3Migration);
    assertEquals(Result.inProgress(), step.performInternal());

    // returns failed if cutover archive has failed
    doReturn(Result.failed()).when(step).cutoverArchive(_onlineArchiveV3Migration);
    assertEquals(Result.failed(), step.performInternal());

    doReturn(Result.done()).when(step).cutoverArchive(_onlineArchiveV3Migration);

    // returns done if wait for resume archive has succeeded
    assertEquals(Result.done(), step.performInternal());
  }

  @Test
  public void testCreateNewV3DataSet() throws SvcException {
    final CutoverNewDataStep step = getMockedStep();

    final ObjectId oaId = _onlineArchiveV3Migration.getArchiveId();
    final ObjectId migrationId = _onlineArchiveV3Migration.getId();

    final OnlineArchive oa = mock(OnlineArchive.class);
    final ObjectId groupId = ObjectId.get();
    final UUID uuid = UUID.randomUUID();
    doReturn("cluster0").when(oa).getClusterName();
    doReturn(groupId).when(oa).getGroupId();
    doReturn("db0").when(oa).getDbName();
    doReturn(Collections.emptyList()).when(oa).getPartitionFields();
    doReturn(Optional.of(uuid)).when(oa).getCollectionUUID();
    doReturn(oaId).when(oa).getId();
    doReturn(State.ACTIVE).when(oa).getState();
    doReturn(CollectionType.STANDARD).when(oa).getCollectionType();
    doReturn(Optional.of(oa)).when(_onlineArchiveSvc).getOnlineArchive(any());

    doReturn(Pair.of(mock(NDSGroup.class), mock(ClusterDescription.class)))
        .when(_onlineArchiveSvc)
        .verifyGroupAndCluster(any(), any());

    final String partitionSetName = ObjectId.get().toHexString();
    doReturn(Optional.of(partitionSetName)).when(step).getV3DataSetPartitionSet();

    final DataProcessRegion dpr = new DataProcessRegion("AWS", "US_EAST_1");
    doReturn(Optional.of(dpr)).when(oa).getDataProcessRegion();
    doReturn(oa).when(_onlineArchiveSvc).ensureV3DataProcessRegion(any(), any());
    doReturn(String.format("%s/%s", groupId, oaId))
        .when(_onlineArchiveSvc)
        .getV3ArchiveS3Path(any());

    // return failed if there is already active v3 dataset
    final NDSDataSetOA existingDataset = mock(NDSDataSetOA.class);
    doReturn(NDSDataSet.State.ACTIVE).when(existingDataset).getState();
    doReturn(Optional.of(existingDataset)).when(_ndsDataSetDao).findByGroupIdAndName(any(), any());
    final Result<NoData> resultWithActiveV3Dataset =
        step.createNewV3DataSet(_onlineArchiveV3Migration);
    assertEquals(Status.FAILED, resultWithActiveV3Dataset.getStatus());
    assertTrue(resultWithActiveV3Dataset.getMessage().contains("there is an existing V3 dataset"));

    doReturn(Optional.empty()).when(_ndsDataSetDao).findByGroupIdAndName(any(), any());
    // returns failed if initialize data set fails
    doThrow(new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR))
        .when(_dataLakeAdminApiClient)
        .initializeDataSet(any(), any(), any(), any(), any(), any(), anyBoolean());
    assertEquals(Result.failed(), step.createNewV3DataSet(_onlineArchiveV3Migration));
    verify(_ndsDataSetDao, times(0)).saveDataSet(any());

    doThrow(
            new DataLakeAdminApiException(
                NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR,
                new StatusRuntimeException(io.grpc.Status.DEADLINE_EXCEEDED)))
        .doReturn(mock(InitializeDataSetResponse.class))
        .when(_dataLakeAdminApiClient)
        .initializeDataSet(any(), any(), any(), any(), any(), any(), anyBoolean());

    // returns failed if enable data set fails
    doThrow(new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR))
        .when(_dataLakeAdminApiClient)
        .enableDataSets(any(), any());
    assertEquals(Result.failed(), step.createNewV3DataSet(_onlineArchiveV3Migration));
    verify(_ndsDataSetDao, times(1)).saveDataSet(any());
    verify(_onlineArchiveV3MigrationDao, times(1)).setTarget(eq(migrationId), any());
    verify(_onlineArchiveDao, times(1))
        .updateV3Fields(
            eq(oaId),
            eq(String.format("v1$atlas$archive$cluster0$db0$%s$%s", uuid, oaId)),
            eq(dpr),
            any(),
            eq(_onlineArchiveV3Migration.getTarget().getDataLandingZoneBucketName()),
            eq(String.format("%s/%s", groupId, oaId)));

    verify(_ndsDataSetDao, times(0)).markDataSetAsActive(any());

    // returns done if enable data set succeeds
    doReturn(mock(DataLakeAdminApiOperationOutcome.class))
        .when(_dataLakeAdminApiClient)
        .enableDataSets(any(), any());
    assertEquals(Result.done(), step.createNewV3DataSet(_onlineArchiveV3Migration));
    verify(_dataLakeAdminApiClient, times(4))
        .initializeDataSet(
            any(), any(), any(), any(), any(), eq(Collections.emptyList()), anyBoolean());
    verify(_ndsDataSetDao, times(2)).saveDataSet(any());
    verify(_ndsDataSetDao, times(1)).markDataSetAsActive(any());

    final List<String> overrideShardKeys = List.of("key1");

    // isTimeSeries flag should be false for Standard OA
    assertEquals(
        Result.done(),
        step.createNewV3DataSet(
            _onlineArchiveV3Migration.copy().setOverrideShardKeys(overrideShardKeys).build()));

    verify(_dataLakeAdminApiClient, times(1))
        .initializeDataSet(any(), any(), any(), any(), any(), eq(overrideShardKeys), eq(false));
    verify(_ndsDataSetDao, times(3)).saveDataSet(any());
    verify(_ndsDataSetDao, times(2)).markDataSetAsActive(any());

    // isTimeSeries flag should be true for Time-Series OA
    doReturn(CollectionType.TIMESERIES).when(oa).getCollectionType();
    assertEquals(
        Result.done(),
        step.createNewV3DataSet(
            _onlineArchiveV3Migration.copy().setOverrideShardKeys(overrideShardKeys).build()));
    verify(_dataLakeAdminApiClient, times(1))
        .initializeDataSet(any(), any(), any(), any(), any(), eq(overrideShardKeys), eq(true));
    verify(_ndsDataSetDao, times(4)).saveDataSet(any());
    verify(_ndsDataSetDao, times(3)).markDataSetAsActive(any());

    reset(_dataLakeAdminApiClient);
    reset(_ndsDataSetDao);
    reset(_onlineArchiveV3MigrationDao);
    // if createNewV3DataSet is already done
    doReturn(Optional.of(_onlineArchiveV3Migration))
        .when(_onlineArchiveV3MigrationDao)
        .find(_onlineArchiveV3Migration.getId());
    doReturn(Optional.of(new Date())).when(step).getV3DataSetInitializedOn();
    doReturn(Optional.of(existingDataset)).when(_ndsDataSetDao).findByGroupIdAndName(any(), any());
    doReturn(partitionSetName).when(oa).getPartitionSetName();
    assertEquals(Result.done(), step.createNewV3DataSet(_onlineArchiveV3Migration));
    verifyNoInteractions(_dataLakeAdminApiClient);
    verify(_ndsDataSetDao, times(0)).saveDataSet(any());
    verify(_ndsDataSetDao, times(0)).markDataSetAsActive(any());
    verify(_onlineArchiveV3MigrationDao, times(0)).setTarget(eq(migrationId), any());
  }

  @Test
  public void testCreateNewV3DataSet_simulateTransientMongoException() throws SvcException {
    final CutoverNewDataStep step = getMockedStep();

    final ObjectId oaId = _onlineArchiveV3Migration.getArchiveId();
    final ObjectId migrationId = _onlineArchiveV3Migration.getId();

    final OnlineArchive oa = mock(OnlineArchive.class);
    final ObjectId groupId = ObjectId.get();
    final UUID uuid = UUID.randomUUID();
    doReturn("cluster0").when(oa).getClusterName();
    doReturn(groupId).when(oa).getGroupId();
    doReturn("db0").when(oa).getDbName();
    doReturn(Optional.of(uuid)).when(oa).getCollectionUUID();
    doReturn(oaId).when(oa).getId();
    doReturn(State.ACTIVE).when(oa).getState();
    doReturn(CollectionType.STANDARD).when(oa).getCollectionType();
    doReturn(Optional.of(oa)).when(_onlineArchiveSvc).getOnlineArchive(any());

    doReturn(Pair.of(mock(NDSGroup.class), mock(ClusterDescription.class)))
        .when(_onlineArchiveSvc)
        .verifyGroupAndCluster(any(), any());

    final String partitionSetName = ObjectId.get().toHexString();
    doReturn(Optional.of(partitionSetName)).when(step).getV3DataSetPartitionSet();

    final DataProcessRegion dpr = new DataProcessRegion("AWS", "US_EAST_1");
    doReturn(Optional.of(dpr)).when(oa).getDataProcessRegion();
    doReturn(oa).when(_onlineArchiveSvc).ensureV3DataProcessRegion(any(), any());
    doReturn(String.format("%s/%s", groupId, oaId))
        .when(_onlineArchiveSvc)
        .getV3ArchiveS3Path(any());

    doReturn(Optional.empty()).when(_ndsDataSetDao).findByGroupIdAndName(any(), any());
    doReturn(mock(InitializeDataSetResponse.class))
        .when(_dataLakeAdminApiClient)
        .initializeDataSet(any(), any(), any(), any(), any(), any(), anyBoolean());
    try {
      doThrow(new MongoTimeoutException("timeout"))
          .when(_onlineArchiveV3MigrationDao)
          .setTarget(eq(migrationId), any());
      step.createNewV3DataSet(_onlineArchiveV3Migration);
      fail("simulate transient mongo error. The move returns in progress and will be tried again");
    } catch (final Exception ignored) {
    }
    verify(_dataLakeAdminApiClient, times(1))
        .initializeDataSet(any(), any(), any(), any(), any(), any(), anyBoolean());
    verify(_ndsDataSetDao, times(1)).saveDataSet(any());
    verify(_ndsDataSetDao, times(0)).markDataSetAsActive(any());

    reset(_dataLakeAdminApiClient);
    reset(_ndsDataSetDao);
    reset(_onlineArchiveV3MigrationDao);

    // simulate the retried run - dataSet already initialized
    final NDSDataSetOA initializedDataSet = mock(NDSDataSetOA.class);
    doReturn(NDSDataSet.State.CREATED).when(initializedDataSet).getState();
    doReturn(Optional.of(initializedDataSet))
        .when(_ndsDataSetDao)
        .findByGroupIdAndName(any(), any());
    doReturn(Optional.of(new Date())).when(step).getV3DataSetInitializedOn();
    assertEquals(Result.done(), step.createNewV3DataSet(_onlineArchiveV3Migration));
    verify(_ndsDataSetDao, times(0)).saveDataSet(any());
    verify(_dataLakeAdminApiClient, times(0))
        .initializeDataSet(any(), any(), any(), any(), any(), any(), anyBoolean());
    // update archive metadata
    verify(_onlineArchiveV3MigrationDao, times(1)).setTarget(eq(migrationId), any());
    verify(_onlineArchiveDao, times(1))
        .updateV3Fields(
            eq(oaId),
            eq(String.format("v1$atlas$archive$cluster0$db0$%s$%s", uuid, oaId)),
            eq(dpr),
            any(),
            eq(_onlineArchiveV3Migration.getTarget().getDataLandingZoneBucketName()),
            eq(String.format("%s/%s", groupId, oaId)));
    verify(_ndsDataSetDao, times(1)).markDataSetAsActive(any());
  }

  @Test
  public void testPauseArchive() throws SvcException {
    final CutoverNewDataStep step = getMockedStep();

    final ObjectId oaId = ObjectId.get();
    final OnlineArchive oa = mock(OnlineArchive.class);
    final ObjectId groupId = ObjectId.get();
    final UUID uuid = UUID.randomUUID();
    doReturn("cluster0").when(oa).getClusterName();
    doReturn(groupId).when(oa).getGroupId();
    doReturn("db0").when(oa).getDbName();
    doReturn(Optional.of(uuid)).when(oa).getCollectionUUID();
    doReturn(oaId).when(oa).getId();
    doReturn(State.ACTIVE).when(oa).getState();
    doReturn(Optional.of(oa)).when(_onlineArchiveSvc).getOnlineArchive(any());
    doNothing().when(_onlineArchiveV3MigrationDao).setIsArchivePausedForMigration(any(), any());
    doReturn(new DefaultSchedule()).when(oa).getSchedule();
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn(false).when(clusterDescription).isPaused();
    doReturn(Optional.of(clusterDescription))
        .when(_ndsClusterSvc)
        .getActiveClusterDescription(
            eq(_onlineArchiveV3Migration.getGroupId()),
            eq(_onlineArchiveV3Migration.getClusterName()));

    // returns failed if set pause fails
    doThrow(new SvcException(NDSErrorCode.ONLINE_ARCHIVE_DOES_NOT_EXIST))
        .when(_onlineArchiveSvc)
        .setPauseRequested(any(), any());
    assertEquals(Result.failed(), step.pauseArchive(_onlineArchiveV3Migration));
    verify(_onlineArchiveV3MigrationDao, times(0)).setIsArchivePausedForMigration(any(), any());

    // returns done if set pause succeeds
    doNothing().when(_onlineArchiveSvc).setPauseRequested(any(), any());
    assertEquals(Result.done(), step.pauseArchive(_onlineArchiveV3Migration));
    verify(_onlineArchiveV3MigrationDao, times(1))
        .setIsArchivePausedForMigration(eq(_onlineArchiveV3Migration.getId()), eq(true));
    verify(_onlineArchiveDao, times(2)).updateArchiveRunFrequency(eq(oaId), eq(300));
  }

  @Test
  public void testResumeArchive() throws SvcException {
    final CutoverNewDataStep step = getMockedStep();
    doNothing().when(_onlineArchiveV3MigrationDao).setIsArchivePausedForMigration(any(), any());

    // returns failed if set activate fails
    doThrow(new SvcException(NDSErrorCode.ONLINE_ARCHIVE_DOES_NOT_EXIST))
        .when(_onlineArchiveSvc)
        .activate(any(), any());
    assertEquals(Result.failed(), step.resumeArchive(_onlineArchiveV3Migration));
    verify(_onlineArchiveV3MigrationDao, times(0)).setIsArchivePausedForMigration(any(), any());
    reset(_onlineArchiveSvc);

    // returns done if archive paused or orphaned
    doReturn(true).when(step).isArchivePausedOrOrphaned();
    assertEquals(Result.done(), step.resumeArchive(_onlineArchiveV3Migration));
    verify(_onlineArchiveSvc, times(0)).activate(any(), any());
    verify(_onlineArchiveV3MigrationDao, times(1))
        .setIsArchivePausedForMigration(eq(_onlineArchiveV3Migration.getId()), eq(false));

    // returns done if set resume succeeds
    doReturn(false).when(step).isArchivePausedOrOrphaned();
    assertEquals(Result.done(), step.resumeArchive(_onlineArchiveV3Migration));
    verify(_onlineArchiveSvc, times(1)).activate(any(), any());
    verify(_onlineArchiveV3MigrationDao, times(2))
        .setIsArchivePausedForMigration(eq(_onlineArchiveV3Migration.getId()), eq(false));
  }

  @Test
  public void testWaitForArchiveIsPaused() {
    final CutoverNewDataStep step = getMockedStep();
    doReturn(90).when(step).getWaitForArchiveIsPausedTimeoutMinutes();

    final OnlineArchive oa = mock(OnlineArchive.class);
    doReturn(Optional.of(oa)).when(_onlineArchiveSvc).getOnlineArchive(any());

    // returns in progress if archive is not paused
    doReturn(State.PAUSING).when(oa).getState();
    assertEquals(
        Result.inProgress().getStatus(),
        step.waitForArchiveIsPaused(_onlineArchiveV3Migration).getStatus());

    // returns done if archive is paused
    doReturn(State.PAUSED).when(oa).getState();
    assertEquals(
        Result.done().getStatus(),
        step.waitForArchiveIsPaused(_onlineArchiveV3Migration).getStatus());
  }

  @Test
  public void testCutoverArchive() throws SvcException {
    final CutoverNewDataStep step = getMockedStep();

    // returns failed if generate storage config fails
    doThrow(new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR))
        .when(_onlineArchiveSvc)
        .updateClusterDataLakeStorageConfigs(any(), any());
    assertEquals(Result.failed(), step.cutoverArchive(_onlineArchiveV3Migration));

    // returns done if generate storage config succeeds
    doNothing().when(_onlineArchiveSvc).updateClusterDataLakeStorageConfigs(any(), any());
    assertEquals(Result.done(), step.cutoverArchive(_onlineArchiveV3Migration));
    verify(_onlineArchiveDao, times(2)).updateOnlineArchiveVersion(any(), any());
    verify(_onlineArchiveSvc, times(2)).updateClusterDataLakeStorageConfigs(any(), any());
    verify(_onlineArchiveSvc, times(1)).activate(any(), any());
    verify(step, times(1)).setResumeArchiveRequestedOn(any());
    verify(_onlineArchiveV3MigrationDao, times(1))
        .setIsArchivePausedForMigration(eq(_onlineArchiveV3Migration.getId()), eq(false));
  }

  @Test
  public void testValidateCutoverStep() throws SvcException {
    doThrow(new SvcException(NDSErrorCode.ONLINE_ARCHIVE_DOES_NOT_EXIST))
        .when(_onlineArchiveSvc)
        .getValidateOnlineArchive(any());

    final CutoverNewDataStep step = getMockedStep();
    try {
      step.validateCutoverStep(_onlineArchiveV3Migration);
      fail();
    } catch (IllegalStateException pE) {
      // expected
      assertTrue(pE.getCause() instanceof SvcException);
      assertEquals(
          NDSErrorCode.ONLINE_ARCHIVE_DOES_NOT_EXIST,
          ((SvcException) pE.getCause()).getErrorCode());
    }

    final OnlineArchive archive =
        new OnlineArchive.Builder()
            .setArchiveId(ObjectId.get())
            .setClusterId(new ObjectId(), "mycluster")
            .setDbName("db2")
            .setCollName("coll2")
            .setCriteria(new OnlineArchive.DateCriteria("dateField", 5, DateFormat.ISODATE))
            .setState(OnlineArchive.State.ACTIVE)
            .setPartitionFields(List.of(new PartitionField("dateField", 0)))
            .setOnlineArchiveVersion(OnlineArchiveVersion.V1)
            .build();

    doReturn(archive).when(_onlineArchiveSvc).getValidateOnlineArchive(any());
    doThrow(new SvcException(NDSErrorCode.ONLINE_ARCHIVE_NOT_AVAILABLE_TENANT_CLUSTER))
        .when(_onlineArchiveSvc)
        .verifyGroupAndCluster(any(), any());

    try {
      step.validateCutoverStep(_onlineArchiveV3Migration);
      fail();
    } catch (IllegalStateException pE) {
      // expected
      assertTrue(pE.getCause() instanceof SvcException);
      assertEquals(
          NDSErrorCode.ONLINE_ARCHIVE_NOT_AVAILABLE_TENANT_CLUSTER,
          ((SvcException) pE.getCause()).getErrorCode());
    }

    doReturn(null).when(_onlineArchiveSvc).verifyGroupAndCluster(any(), any());

    final OnlineArchive pendingArchive = archive.copy().setState(State.PENDING).build();
    doReturn(pendingArchive).when(_onlineArchiveSvc).getValidateOnlineArchive(any());

    try {
      step.validateCutoverStep(_onlineArchiveV3Migration);
      fail();
    } catch (final IllegalStateException pE) {
      assertTrue(pE.getMessage().contains("is not in allowed states"));
    }

    doReturn(archive).when(_onlineArchiveSvc).getValidateOnlineArchive(any());

    // should pass
    step.validateCutoverStep(_onlineArchiveV3Migration);

    // dataProcessRegion
    final OnlineArchiveV3Migration migrationMissingDpr =
        _onlineArchiveV3Migration
            .copy()
            .setTarget(
                _onlineArchiveV3Migration.getTarget().copy().setDataProcessRegion(null).build())
            .build();

    final CutoverNewDataStep stepMissingDpr = getMockedStep(migrationMissingDpr);
    try {
      stepMissingDpr.validateCutoverStep(migrationMissingDpr);
      fail();
    } catch (final IllegalStateException pE) {
      assertTrue(pE.getMessage().contains("target is missing dataProcessRegion"));
    }

    final OnlineArchiveV3Migration migrationMissingDlzBucket =
        _onlineArchiveV3Migration
            .copy()
            .setTarget(
                _onlineArchiveV3Migration
                    .getTarget()
                    .copy()
                    .setDataLandingZoneBucketName(null)
                    .build())
            .build();

    final CutoverNewDataStep stepMissingDlzBucket = getMockedStep(migrationMissingDlzBucket);
    try {
      stepMissingDlzBucket.validateCutoverStep(migrationMissingDlzBucket);
      fail();
    } catch (final IllegalStateException pE) {
      assertTrue(pE.getMessage().contains("target is missing dataLandingZoneBucketName"));
    }
  }

  @Test
  public void testRollbackInternal() {
    final CutoverNewDataStep step = getMockedStep();

    // returns done if already rolled back
    doReturn(true).when(step).isRolledBack();
    assertEquals(Result.done(), step.rollbackInternal());

    when(step.isRolledBack()).thenReturn(false);
    // returns done if initial validation failed
    doReturn(true).when(step).isInitialValidationFailed();
    assertEquals(Result.done(), step.rollbackInternal());

    doReturn(false).when(step).isInitialValidationFailed();
    doReturn(Optional.of(new Date())).when(step).getPauseArchiveRequestedOn();
    doReturn(Optional.empty()).when(step).getRollbackPauseArchiveRequestedOn();

    // returns in progress if pause archive is in progress
    doReturn(Result.inProgress()).when(step).pauseArchive(_onlineArchiveV3Migration);
    assertEquals(Result.inProgress(), step.rollbackInternal());

    // returns in progress if pause archive has failed
    doReturn(Result.failed()).when(step).pauseArchive(_onlineArchiveV3Migration);
    assertEquals(Result.failed(), step.rollbackInternal());

    doReturn(Result.done()).when(step).pauseArchive(_onlineArchiveV3Migration);

    // returns in progress if wait for pause archive is in progress
    doReturn(Result.inProgress()).when(step).waitForArchiveIsPaused(_onlineArchiveV3Migration);
    assertEquals(Result.inProgress(), step.rollbackInternal());

    // returns in progress if wait for pause archive has failed
    doReturn(Result.failed()).when(step).waitForArchiveIsPaused(_onlineArchiveV3Migration);
    assertEquals(Result.failed(), step.rollbackInternal());

    doReturn(Result.done()).when(step).waitForArchiveIsPaused(_onlineArchiveV3Migration);
    doReturn(Optional.of(new Date())).when(step).getResumeArchiveRequestedOn();
    doReturn(Optional.empty()).when(step).getRollbackCutoverJobsSubmittedOn();

    // returns in progress if revert v3 field is in progress
    doReturn(Result.inProgress())
        .when(step)
        .revertV3FieldsAndResumeArchive(_onlineArchiveV3Migration);
    assertEquals(Result.inProgress(), step.rollbackInternal());

    // returns failed if revert v3 field has failed
    doReturn(Result.failed()).when(step).revertV3FieldsAndResumeArchive(_onlineArchiveV3Migration);
    assertEquals(Result.failed(), step.rollbackInternal());

    doReturn(Result.done()).when(step).revertV3FieldsAndResumeArchive(_onlineArchiveV3Migration);

    // returns in progress if rollback cutover is in progress
    doReturn(Result.inProgress()).when(step).rollbackCutover(_onlineArchiveV3Migration);
    assertEquals(Result.inProgress(), step.rollbackInternal());

    // returns failed if wait for rollback cutover has failed
    doReturn(Result.failed()).when(step).rollbackCutover(_onlineArchiveV3Migration);
    assertEquals(Result.failed(), step.rollbackInternal());

    doReturn(Result.done()).when(step).rollbackCutover(_onlineArchiveV3Migration);

    // returns in progress if wait for rollback jobs is in progress
    doReturn(Result.inProgress()).when(step).waitForRollbackJobs(_onlineArchiveV3Migration);
    assertEquals(Result.inProgress(), step.rollbackInternal());

    // returns failed if wait for rollback jobs has failed
    doReturn(Result.failed()).when(step).waitForRollbackJobs(_onlineArchiveV3Migration);
    assertEquals(Result.failed(), step.rollbackInternal());

    doReturn(Result.done()).when(step).waitForRollbackJobs(_onlineArchiveV3Migration);

    // returns in progress if revert Storage Config is in progress
    doReturn(Result.inProgress())
        .when(step)
        .revertStorageConfigAndDestroyV3Dataset(_onlineArchiveV3Migration);
    assertEquals(Result.inProgress(), step.rollbackInternal());

    // returns failed if revert Storage Config has failed
    doReturn(Result.failed())
        .when(step)
        .revertStorageConfigAndDestroyV3Dataset(_onlineArchiveV3Migration);
    assertEquals(Result.failed(), step.rollbackInternal());

    // returns done if revert Storage Config has succeeded
    doReturn(Result.done())
        .when(step)
        .revertStorageConfigAndDestroyV3Dataset(_onlineArchiveV3Migration);
    assertEquals(Result.done(), step.rollbackInternal());
  }

  @Test
  public void testRollbackCutover() {
    final CutoverNewDataStep step = getMockedStep();

    final OnlineArchiveRun run1 = mock(OnlineArchiveRun.class);
    final OnlineArchiveRun run2 = mock(OnlineArchiveRun.class);
    final OnlineArchiveRun run3 = mock(OnlineArchiveRun.class);
    final OnlineArchiveRun run4 = mock(OnlineArchiveRun.class);
    final OnlineArchiveRun run5 = mock(OnlineArchiveRun.class);
    final ObjectId runId1 = ObjectId.get();
    final ObjectId runId2 = ObjectId.get();
    final ObjectId runId3 = ObjectId.get();
    final ObjectId runId4 = ObjectId.get();
    final ObjectId runId5 = ObjectId.get();
    final ArchiveRunStats stats = mock(ArchiveRunStats.class);
    doReturn(runId1).when(run1).getId();
    doReturn(runId2).when(run2).getId();
    doReturn(runId3).when(run3).getId();
    doReturn(runId4).when(run4).getId();
    doReturn(runId5).when(run5).getId();
    doReturn(stats).when(run1).getStats();
    doReturn(stats).when(run2).getStats();
    doReturn(stats).when(run3).getStats();
    doReturn(stats).when(run4).getStats();
    doReturn(stats).when(run5).getStats();
    doReturn(1).when(stats).getNumFiles();

    doReturn(MongoCursorUtils.fromCollection(List.of(run1, run2, run3, run4, run5)))
        .when(_onlineArchiveRunDao)
        .findNonEmptyArchiveRunsByVersion(eq(_onlineArchiveV3Migration.getArchiveId()), any());
    doReturn(mock(CutoverRollbackJob.class)).when(step).createCutoverRollbackJob(any(), any());
    doReturn(3).when(step).getMaxNumCutoverRollbackJobFiles();

    assertEquals(Result.done(), step.rollbackCutover(_onlineArchiveV3Migration));
    verify(step, times(2)).createCutoverRollbackJob(eq(_onlineArchiveV3Migration), any());
    verify(step, times(1))
        .createCutoverRollbackJob(
            eq(_onlineArchiveV3Migration), eq(List.of(runId1, runId2, runId3)));
    verify(step, times(1))
        .createCutoverRollbackJob(eq(_onlineArchiveV3Migration), eq(List.of(runId4, runId5)));
  }

  @Test
  public void testRevertV3Fields() throws SvcException {
    final CutoverNewDataStep step = getMockedStep();

    final OnlineArchive oa = mock(OnlineArchive.class);
    doReturn("cluster0").when(oa).getClusterName();
    doReturn("db0").when(oa).getDbName();
    doReturn(Optional.of(UUID.randomUUID())).when(oa).getCollectionUUID();
    doReturn(new ObjectId()).when(oa).getId();
    doReturn(oa).when(_onlineArchiveSvc).getValidateOnlineArchive(any());

    assertEquals(Result.done(), step.revertV3FieldsAndResumeArchive(_onlineArchiveV3Migration));
    verify(_onlineArchiveDao, times(1)).revertV3Fields(any(), any(), any());
    verify(_onlineArchiveSvc, times(1)).activate(any(), any());
    verify(step, times(1)).setRollbackResumeArchiveRequestedOn(any());
    verify(_onlineArchiveV3MigrationDao, times(1))
        .setIsArchivePausedForMigration(eq(_onlineArchiveV3Migration.getId()), eq(false));

    // skip reverting if archive is v1
    doReturn(OnlineArchiveVersion.V1).when(oa).getOnlineArchiveVersion();
    assertEquals(Result.done(), step.revertV3FieldsAndResumeArchive(_onlineArchiveV3Migration));
    verify(_onlineArchiveDao, times(1)).revertV3Fields(any(), any(), any());
    verify(_onlineArchiveSvc, times(2)).activate(any(), any());
    verify(step, times(2)).setRollbackResumeArchiveRequestedOn(any());
    verify(_onlineArchiveV3MigrationDao, times(2))
        .setIsArchivePausedForMigration(eq(_onlineArchiveV3Migration.getId()), eq(false));

    // skip resume if archive is paused before rollback
    doReturn(true).when(step).isArchivePausedOrOrphaned();
    assertEquals(Result.done(), step.revertV3FieldsAndResumeArchive(_onlineArchiveV3Migration));
    verify(_onlineArchiveDao, times(1)).revertV3Fields(any(), any(), any());
    verify(_onlineArchiveSvc, times(2)).activate(any(), any());
    verify(step, times(3)).setRollbackResumeArchiveRequestedOn(any());
    verify(_onlineArchiveV3MigrationDao, times(3))
        .setIsArchivePausedForMigration(eq(_onlineArchiveV3Migration.getId()), eq(false));
  }

  @Test
  public void testWaitForDLSUploadJobsToComplete() {
    final CutoverNewDataStep step = getMockedStep();

    final List<CutoverRollbackJob.State> notCompletedStates =
        List.of(CutoverRollbackJob.State.SUCCEEDED, CutoverRollbackJob.State.FAILED);
    doReturn(100)
        .when(_cutoverRollbackJobDao)
        .getJobsCountByMigrationAndStateNotIn(
            eq(_onlineArchiveV3Migration.getId()), eq(notCompletedStates));
    assertEquals(Result.inProgress(), step.waitForRollbackJobs(_onlineArchiveV3Migration));

    doReturn(0)
        .when(_cutoverRollbackJobDao)
        .getJobsCountByMigrationAndStateNotIn(
            eq(_onlineArchiveV3Migration.getId()), eq(notCompletedStates));
    doReturn(100)
        .when(_cutoverRollbackJobDao)
        .getJobsCountByMigrationAndStateNotIn(
            eq(_onlineArchiveV3Migration.getId()), eq(List.of(CutoverRollbackJob.State.SUCCEEDED)));
    assertEquals(
        Result.failed(),
        step.waitForRollbackJobs(_onlineArchiveV3Migration),
        "should fail if not all rollback jobs succeed");

    doReturn(0)
        .when(_cutoverRollbackJobDao)
        .getJobsCountByMigrationAndStateNotIn(
            eq(_onlineArchiveV3Migration.getId()), eq(List.of(CutoverRollbackJob.State.SUCCEEDED)));
    assertEquals(Result.done(), step.waitForRollbackJobs(_onlineArchiveV3Migration));
  }

  @Test
  public void testRevertStorageConfig() throws SvcException {
    final CutoverNewDataStep step = getMockedStep();

    final OnlineArchive oa = mock(OnlineArchive.class);
    final String clusterName = "cluster0";
    final String dbName = "db0";
    final UUID collectionUUID = UUID.randomUUID();
    final ObjectId archiveId = new ObjectId();
    final DataLakeAdminApiOperationOutcome outcome = mock(DataLakeAdminApiOperationOutcome.class);

    doReturn(clusterName).when(oa).getClusterName();
    doReturn(dbName).when(oa).getDbName();
    doReturn(Optional.of(collectionUUID)).when(oa).getCollectionUUID();
    doReturn(archiveId).when(oa).getId();
    doReturn(oa).when(_onlineArchiveSvc).getValidateOnlineArchive(any());
    doNothing().when(_onlineArchiveSvc).updateClusterDataLakeStorageConfigs(any(), any());

    // return failed if archive version is not v1
    assertEquals(
        Result.failed(), step.revertStorageConfigAndDestroyV3Dataset(_onlineArchiveV3Migration));

    doReturn(OnlineArchiveVersion.V1).when(oa).getOnlineArchiveVersion();
    // skip destroying v3 dataset if initializedV3DataSetOn is not set
    assertEquals(
        Result.done(), step.revertStorageConfigAndDestroyV3Dataset(_onlineArchiveV3Migration));
    verify(_onlineArchiveSvc, times(1)).updateClusterDataLakeStorageConfigs(any(), any());
    verify(_dataLakeAdminApiClient, times(0)).destroyDataSets(any(), any());

    doReturn(Optional.of(new Date())).when(step).getV3DataSetInitializedOn();

    final NDSDataSetOA backfillDataset =
        new NDSDataSetOA(
            new BasicDBObject().append(NDSDataSet.FieldDefs.STATE, NDSDataSet.State.ACTIVE));
    doReturn(Optional.of(backfillDataset))
        .when(_ndsDataSetDao)
        .findByGroupIdAndName(
            any(), eq(_onlineArchiveV3Migration.getTarget().getBackfillDatasetName()));

    // returns failed if disable backfill dataset fails
    doThrow(new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR))
        .when(_dataLakeAdminApiClient)
        .disableDataSets(any(), any());
    assertEquals(
        Result.failed(), step.revertStorageConfigAndDestroyV3Dataset(_onlineArchiveV3Migration));
    verify(_onlineArchiveSvc, times(2)).updateClusterDataLakeStorageConfigs(any(), any());
    verify(_dataLakeAdminApiClient, times(1)).disableDataSets(any(), any());

    doReturn(outcome).when(_dataLakeAdminApiClient).disableDataSets(any(), any());
    final NDSDataSetOA existingDataset =
        new NDSDataSetOA(
            new BasicDBObject().append(NDSDataSet.FieldDefs.STATE, NDSDataSet.State.ACTIVE));
    final String existingDatasetName =
        String.format(
            OnlineArchiveDataLakeConfigSvc.DATA_SET_NAME_FORMAT_V1,
            clusterName,
            dbName,
            collectionUUID,
            archiveId);
    doReturn(Optional.of(existingDataset))
        .when(_ndsDataSetDao)
        .findByGroupIdAndName(any(), eq(existingDatasetName));

    // returns failed if destroy dataset fails
    doThrow(new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR))
        .when(_dataLakeAdminApiClient)
        .destroyDataSets(any(), any());
    assertEquals(
        Result.failed(), step.revertStorageConfigAndDestroyV3Dataset(_onlineArchiveV3Migration));
    verify(_onlineArchiveSvc, times(3)).updateClusterDataLakeStorageConfigs(any(), any());
    verify(_dataLakeAdminApiClient, times(1)).destroyDataSets(any(), any());

    doReturn(List.of(outcome)).when(_dataLakeAdminApiClient).destroyDataSets(any(), any());
    assertEquals(
        Result.done(), step.revertStorageConfigAndDestroyV3Dataset(_onlineArchiveV3Migration));
    verify(_onlineArchiveSvc, times(4)).updateClusterDataLakeStorageConfigs(any(), any());
    verify(_dataLakeAdminApiClient, times(2)).destroyDataSets(any(), any());
    verify(_ndsDataSetDao, times(1)).markDataSetsAsDestroyed(any());
  }

  public CutoverNewDataStep getMockedStep() {
    return getMockedStep(_onlineArchiveV3Migration);
  }

  public CutoverNewDataStep getMockedStep(final OnlineArchiveV3Migration pMigration) {
    doReturn(Optional.of(pMigration)).when(_onlineArchiveV3MigrationDao).find(pMigration.getId());
    return spy(
        new CutoverNewDataStep(
            _planContext,
            _state,
            pMigration.getId(),
            _dataLakeAdminApiClient,
            _ndsDataSetDao,
            _onlineArchiveV3MigrationSvc,
            _onlineArchiveV3MigrationDao,
            _onlineArchiveSvc,
            _onlineArchiveDao,
            _cutoverRollbackJobDao,
            _onlineArchiveRunDao,
            _ndsClusterSvc));
  }

  private OnlineArchiveV3Migration getMigration() {
    final Source source = new Source(OnlineArchiveVersion.V1, "US_EAST_1", "bucket", "somePath");
    final DataProcessRegion dpr = new DataProcessRegion("AWS", "US_EAST_1");
    final Target target =
        new Target(dpr, "dlzBucket", null, null, null, "backfillDatasetName", null);
    return new OnlineArchiveV3Migration(
        new ObjectId(),
        new Date(),
        new ObjectId(),
        new ObjectId(),
        "cluster",
        new ObjectId(),
        OnlineArchiveV3Migration.State.IN_PROGRESS,
        new Date(),
        null,
        false,
        null,
        null,
        source,
        target,
        Phase.PHASE_1_CUTOVER_NEW_DATA,
        null,
        null,
        null,
        null,
        null,
        null,
        false,
        false,
        null,
        false);
  }
}
