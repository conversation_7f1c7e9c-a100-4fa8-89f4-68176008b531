load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*UnitTests.java"]),
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_public/model",
        "//server/src/main/com/xgen/cloud/nds/aws/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/azure/_public/model",
        "//server/src/main/com/xgen/cloud/nds/azure/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/azure/_public/util",
        "//server/src/main/com/xgen/cloud/nds/common/_public/model",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/settings",
        "//server/src/unit/com/xgen/svc/mms/util",
        "@com_xgen_mdb_idl//:mhouse_definitions",
        "@io_grpc_grpc_java//api",
        "@maven//:com_amazonaws_aws_java_sdk_core",
        "@maven//:com_amazonaws_aws_java_sdk_s3",
        "@maven//:com_azure_azure_storage_blob",
        "@maven//:com_google_auth_google_auth_library_oauth2_http",
        "@maven//:com_google_cloud_google_cloud_core",
        "@maven//:com_google_cloud_google_cloud_storage",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)
