load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*UnitTests.java"]),
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_public/model",
        "//server/src/main/com/xgen/cloud/nds/common/_public/model",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/main/com/xgen/cloud/nds/datalake/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/gcp/_public/model",
        "//server/src/main/com/xgen/cloud/nds/gcp/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/onlinearchive/_public/model",
        "//server/src/main/com/xgen/module/common/planner",
        "@com_xgen_mdb_idl//:mhouse_definitions",
        "@io_grpc_grpc_java//api",
        "@maven//:com_amazonaws_aws_java_sdk_s3",
        "@maven//:com_azure_azure_storage_blob",
        "@maven//:com_google_auth_google_auth_library_oauth2_http",
        "@maven//:com_google_cloud_google_cloud_storage",
        "@maven//:io_github_hakky54_logcaptor",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)
