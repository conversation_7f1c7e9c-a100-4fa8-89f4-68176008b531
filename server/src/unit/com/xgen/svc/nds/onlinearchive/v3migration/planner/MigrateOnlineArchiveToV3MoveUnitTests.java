package com.xgen.svc.nds.onlinearchive.v3migration.planner;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataSetDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.OnlineArchiveRunDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.v3migration.CutoverRollbackJobDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.v3migration.DLSUploadJobDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.v3migration.OnlineArchiveV3MigrationDao;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.DataProcessRegion;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive.OnlineArchiveVersion;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration.Phase;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration.Source;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration.State;
import com.xgen.cloud.nds.onlinearchive._public.model.v3migration.OnlineArchiveV3Migration.Target;
import com.xgen.module.common.planner.StepFailedException;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.onlinearchive.v3migration.svc.OnlineArchiveV3MigrationSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import java.util.Date;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@MockitoSettings(strictness = Strictness.LENIENT)
public class MigrateOnlineArchiveToV3MoveUnitTests {
  @Mock private OnlineArchiveV3MigrationDao _onlineArchiveV3MigrationDao;
  @Mock private AuditSvc _auditSvc;

  @Test
  public void testPerformInternal_all() {

    final ObjectId migrationId = new ObjectId();
    final MigrateOnlineArchiveToV3Move move = getMockedMove(migrationId);

    doReturn(Result.done())
        .when(move)
        .cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.done())
        .when(move)
        .backfillExistingData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.done())
        .when(move)
        .cutoverBackfill(any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.done())
        .when(move)
        .mergeDatasets(any(), any(), any(), any(), any(), any(), any());

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isDone());

    verify(move).cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move)
        .backfillExistingData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move).cutoverBackfill(any(), any(), any(), any(), any(), any(), any(), any());
    verify(move).mergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(_onlineArchiveV3MigrationDao)
        .setMigrationSucceeded(eq(migrationId), any(), any(), any());
  }

  @Test
  public void testPerformInternal_cutoverNewData() {

    final ObjectId migrationId = new ObjectId();
    final MigrateOnlineArchiveToV3Move move = getMockedMove(migrationId);

    doReturn(Result.inProgress())
        .when(move)
        .cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isInProgress());

    verify(move).cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0))
        .backfillExistingData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0)).cutoverBackfill(any(), any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0)).mergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(0))
        .setMigrationSucceeded(any(), any(), any(), any());

    // test failure
    doReturn(Result.failed())
        .when(move)
        .cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());
    try {
      move.performInternal();
      fail();
    } catch (final StepFailedException pE) {
      // expected
    }
  }

  @Test
  public void testPerformInternal_backfillExisting() {

    final ObjectId migrationId = new ObjectId();
    final MigrateOnlineArchiveToV3Move move = getMockedMove(migrationId);

    doReturn(Result.done())
        .when(move)
        .cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.inProgress())
        .when(move)
        .backfillExistingData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isInProgress());

    verify(move).cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move)
        .backfillExistingData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0)).cutoverBackfill(any(), any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0)).mergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(0))
        .setMigrationSucceeded(any(), any(), any(), any());

    // test failure
    doReturn(Result.failed())
        .when(move)
        .backfillExistingData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    try {
      move.performInternal();
      fail();
    } catch (final StepFailedException pE) {
      // expected
    }
  }

  @Test
  public void testPerformInternal_cutoverBackfill() {

    final ObjectId migrationId = new ObjectId();
    final MigrateOnlineArchiveToV3Move move = getMockedMove(migrationId);

    doReturn(Result.done())
        .when(move)
        .cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.done())
        .when(move)
        .backfillExistingData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.inProgress())
        .when(move)
        .cutoverBackfill(any(), any(), any(), any(), any(), any(), any(), any());

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isInProgress());

    verify(move).cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move)
        .backfillExistingData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move).cutoverBackfill(any(), any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0)).mergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(0))
        .setMigrationSucceeded(any(), any(), any(), any());

    // test failure
    doReturn(Result.failed())
        .when(move)
        .cutoverBackfill(any(), any(), any(), any(), any(), any(), any(), any());
    try {
      move.performInternal();
      fail();
    } catch (final StepFailedException pE) {
      // expected
    }
  }

  @Test
  public void testPerformInternal_mergeDatasets() {
    final ObjectId migrationId = new ObjectId();
    final MigrateOnlineArchiveToV3Move move = getMockedMove(migrationId);

    doReturn(Result.done())
        .when(move)
        .cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.done())
        .when(move)
        .backfillExistingData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.done())
        .when(move)
        .cutoverBackfill(any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.inProgress())
        .when(move)
        .mergeDatasets(any(), any(), any(), any(), any(), any(), any());

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isInProgress());

    verify(move).cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move)
        .backfillExistingData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move).cutoverBackfill(any(), any(), any(), any(), any(), any(), any(), any());
    verify(move).mergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(0))
        .setMigrationSucceeded(any(), any(), any(), any());

    // test failure
    doReturn(Result.failed())
        .when(move)
        .mergeDatasets(any(), any(), any(), any(), any(), any(), any());
    try {
      move.performInternal();
      fail();
    } catch (final StepFailedException pE) {
      // expected
    }
  }

  @Test
  public void testPerformInternal_auditEvents() {

    final ObjectId migrationId = new ObjectId();
    final MigrateOnlineArchiveToV3Move move = getMockedMove(migrationId);

    doReturn(Result.done())
        .when(move)
        .cutoverNewData(any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.done())
        .when(move)
        .backfillExistingData(any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.done())
        .when(move)
        .cutoverBackfill(any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.done())
        .when(move)
        .mergeDatasets(any(), any(), any(), any(), any(), any(), any());

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isDone());

    verify(_onlineArchiveV3MigrationDao)
        .setMigrationSucceeded(eq(migrationId), any(), any(), any());

    // verify audit
    verify(move, times(1))
        .saveAuditEvent(any(), eq(NDSAudit.Type.ONLINE_ARCHIVE_V3_MIGRATION_SUCCEEDED), any());
    verify(_auditSvc, times(1)).saveAuditEvent(any());
  }

  @Test
  public void testPerformInternal_auditEvents_FailedMigration() {

    final ObjectId migrationId = new ObjectId();
    final MigrateOnlineArchiveToV3Move move = getMockedMove(migrationId);

    doReturn(Result.failed()).when(move).runIfReady(any(), any(Result[].class));

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isFailed());

    // verify audit
    verify(move, times(1))
        .saveAuditEvent(any(), eq(NDSAudit.Type.ONLINE_ARCHIVE_V3_MIGRATION_FAILED), any());
    verify(_auditSvc, times(1)).saveAuditEvent(any());
  }

  @Test
  public void testRollbackInternal() {
    final ObjectId migrationId = new ObjectId();
    final MigrateOnlineArchiveToV3Move move = getMockedMove(migrationId);
    final OnlineArchiveV3Migration migration = spy(newMigration(migrationId));
    doReturn(Optional.of(migration)).when(_onlineArchiveV3MigrationDao).find(eq(migrationId));

    // rollback from PHASE_4_MERGE_DATASETS - succeed
    doReturn(Phase.PHASE_4_MERGE_DATASETS).when(migration).getCurrentPhase();
    doReturn(Result.done())
        .when(move)
        .rollbackMergeDatasets(any(), any(), any(), any(), any(), any(), any());
    assertTrue(move.rollbackInternal().getStatus().isDone());
    verify(move, times(1)).rollbackMergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0)) // do not rollback cutover data
        .rollbackBackfillExistingData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0)) // do not rollback cutover data
        .rollbackCutoverNewData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(1)).setMigrationRollbackStarted(any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(1))
        .setMigrationRollbackFinished(any(), any(), any());

    clearInvocations(move);
    clearInvocations(_onlineArchiveV3MigrationDao);
    // rollback from PHASE_4_MERGE_DATASETS - failed
    doReturn(Result.failed())
        .when(move)
        .rollbackMergeDatasets(any(), any(), any(), any(), any(), any(), any());
    assertTrue(move.rollbackInternal().getStatus().isFailed());
    verify(move, times(1)).rollbackMergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0))
        .rollbackBackfillExistingData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0)) // do not rollback cutover data
        .rollbackCutoverNewData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(1)).setMigrationRollbackStarted(any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(1))
        .setMigrationRollbackFinished(any(), any(), any());

    clearInvocations(move);
    clearInvocations(_onlineArchiveV3MigrationDao);
    // rollback from PHASE_4_MERGE_DATASETS - in progress
    doReturn(new Date()).when(migration).getRollbackStartDate();
    doReturn(Result.inProgress())
        .when(move)
        .rollbackMergeDatasets(any(), any(), any(), any(), any(), any(), any());
    assertTrue(move.rollbackInternal().getStatus().isInProgress());
    verify(move, times(1)).rollbackMergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0))
        .rollbackBackfillExistingData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move, times(0)) // do not rollback cutover data
        .rollbackCutoverNewData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(0)).setMigrationRollbackStarted(any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(0)) // rollback in progress
        .setMigrationRollbackFinished(any(), any(), any());

    clearInvocations(move);
    clearInvocations(_onlineArchiveV3MigrationDao);
    // rollback before PHASE_4_MERGE_DATASETS - done
    doReturn(Phase.PHASE_3_CUTOVER_BACKFILL).when(migration).getCurrentPhase();
    doReturn(null).when(migration).getRollbackStartDate();
    doReturn(Result.done())
        .when(move)
        .rollbackBackfillExistingData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.done())
        .when(move)
        .rollbackCutoverNewData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    assertTrue(move.rollbackInternal().getStatus().isDone());
    verify(move, times(0)).rollbackMergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(move, times(1))
        .rollbackCutoverNewData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(1)).setMigrationRollbackStarted(any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(1))
        .setMigrationRollbackFinished(any(), any(), any());

    clearInvocations(move);
    clearInvocations(_onlineArchiveV3MigrationDao);
    // rollback before PHASE_4_MERGE_DATASETS - failed
    doReturn(Result.failed())
        .when(move)
        .rollbackBackfillExistingData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.done())
        .when(move)
        .rollbackCutoverNewData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());

    // backfill rollback failures doesn't cause rollback to fail
    assertTrue(move.rollbackInternal().getStatus().isDone());

    verify(move, times(0)).rollbackMergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(move, times(1))
        .rollbackCutoverNewData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());

    doReturn(Result.done())
        .when(move)
        .rollbackBackfillExistingData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.failed())
        .when(move)
        .rollbackCutoverNewData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());

    assertTrue(move.rollbackInternal().getStatus().isFailed());

    verify(move, times(0)).rollbackMergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(move, times(2))
        .rollbackCutoverNewData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());

    verify(_onlineArchiveV3MigrationDao, times(2)).setMigrationRollbackStarted(any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(2))
        .setMigrationRollbackFinished(any(), any(), any());

    clearInvocations(move);
    clearInvocations(_onlineArchiveV3MigrationDao);
    // rollback before PHASE_4_MERGE_DATASETS - in progress
    doReturn(new Date()).when(migration).getRollbackStartDate();
    doReturn(Result.inProgress())
        .when(move)
        .rollbackBackfillExistingData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(Result.inProgress())
        .when(move)
        .rollbackCutoverNewData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    assertTrue(move.rollbackInternal().getStatus().isInProgress());
    verify(move, times(0)).rollbackMergeDatasets(any(), any(), any(), any(), any(), any(), any());
    verify(move, times(1))
        .rollbackBackfillExistingData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(move, times(1))
        .rollbackCutoverNewData(
            any(), any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(0)).setMigrationRollbackStarted(any(), any(), any());
    verify(_onlineArchiveV3MigrationDao, times(0)) // rollback in progress
        .setMigrationRollbackFinished(any(), any(), any());
  }

  public MigrateOnlineArchiveToV3Move getMockedMove(final ObjectId pMigrationId) {
    final PlanContext planContext = mock(PlanContext.class);
    final Logger logger = LoggerFactory.getLogger(MigrateOnlineArchiveToV3MoveUnitTests.class);
    doReturn(logger).when(planContext).getLogger();
    final DataLakeAdminApiClient dataLakeAdminApiClient = mock(DataLakeAdminApiClient.class);
    final NDSDataSetDao ndsDataSetDao = mock(NDSDataSetDao.class);
    final OnlineArchiveV3MigrationSvc onlineArchiveV3MigrationSvc =
        mock(OnlineArchiveV3MigrationSvc.class);
    final OnlineArchiveSvc onlineArchiveSvc = mock(OnlineArchiveSvc.class);
    final OnlineArchiveDao onlineArchiveDao = mock(OnlineArchiveDao.class);
    final DLSUploadJobDao dlsUploadJobDao = mock(DLSUploadJobDao.class);
    final AWSAccountDao awsAccountDao = mock(AWSAccountDao.class);
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);
    final OnlineArchiveV3Migration migration = newMigration(pMigrationId);
    doReturn(Optional.of(migration)).when(_onlineArchiveV3MigrationDao).find(eq(pMigrationId));
    final CutoverRollbackJobDao cutoverRollbackJobDao = mock(CutoverRollbackJobDao.class);
    final OnlineArchiveRunDao onlineArchiveRunDao = mock(OnlineArchiveRunDao.class);
    final NDSClusterSvc ndsClusterSvc = mock(NDSClusterSvc.class);

    return spy(
        new MigrateOnlineArchiveToV3Move(
            planContext,
            pMigrationId,
            dataLakeAdminApiClient,
            ndsDataSetDao,
            onlineArchiveV3MigrationSvc,
            _onlineArchiveV3MigrationDao,
            onlineArchiveSvc,
            onlineArchiveDao,
            dlsUploadJobDao,
            awsAccountDao,
            awsApiSvc,
            cutoverRollbackJobDao,
            onlineArchiveRunDao,
            ndsClusterSvc,
            _auditSvc));
  }

  private OnlineArchiveV3Migration newMigration(final ObjectId pMigrationId) {

    final Source source = new Source(OnlineArchiveVersion.V1, "US_EAST_1", "bucket", "somePath");
    final DataProcessRegion dpr = new DataProcessRegion("AWS", "US_EAST_1");
    final Target target =
        new Target(
            dpr, "dlzBucket", "cutoverDSName", "cutoverPartitionset", "cutoverPath", null, null);
    return new OnlineArchiveV3Migration(
        pMigrationId,
        new Date(),
        new ObjectId(),
        new ObjectId(),
        "cluster0",
        new ObjectId(),
        State.IN_PROGRESS,
        null,
        null,
        false,
        null,
        null,
        source,
        target,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        false,
        false,
        null,
        false);
  }
}
