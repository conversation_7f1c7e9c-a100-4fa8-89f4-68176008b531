package com.xgen.svc.nds.onlinearchive.restorationtool.svc;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.svc.JobsProcessorSvc;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.azure._private.dao.AzureStorageAccountDao;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._private.dao.NDSDataSetDao;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSet;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSetOA;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.gcp._public.svc.GCPOrganizationSvc;
import com.xgen.cloud.nds.onlinearchive._private.dao.RestoreDLSJobDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.RestoreOADatasetRequestDao;
import com.xgen.cloud.nds.onlinearchive._private.dao.v3migration.OnlineArchiveV3MigrationDao;
import com.xgen.cloud.nds.onlinearchive._private.settings.OnlineArchiveSettings;
import com.xgen.cloud.nds.onlinearchive._public.model.OnlineArchive;
import com.xgen.cloud.nds.onlinearchive._public.model.RestoreDLSJob;
import com.xgen.cloud.nds.onlinearchive._public.model.RestoreOADatasetRequest;
import com.xgen.module.common.planner.svc.PlanSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiException;
import com.xgen.svc.nds.svc.onlinearchive.OnlineArchiveSvc;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@MockitoSettings(strictness = Strictness.LENIENT)
public class OnlineArchiveRestorationSvcUnitTests {

  @Mock private OnlineArchiveSettings _onlineArchiveSettings;
  @Mock private RestoreOADatasetRequestDao _restoreOADatasetRequestDao;
  @Mock private PlanSvc _planSvc;
  @Mock private OnlineArchiveSvc _onlineArchiveSvc;
  @Mock private OnlineArchiveV3MigrationDao _onlineArchiveV3MigrationDao;
  @Mock private RestoreDLSJobDao _restoreDLSJobDao;
  @Mock private AWSAccountDao _awsAccountDao;
  @Mock private JobsProcessorSvc _jobsProcessorSvc;
  @Mock private AzureApiSvc _azureApiSvc;
  @Mock private AzureStorageAccountDao _azureStorageAccountDao;
  @Mock private GCPOrganizationSvc _gcpOrganizationSvc;
  @Mock private GCPApiSvc _gcpApiSvc;
  @Mock private NDSDataSetDao _ndsDataSetDao;
  @Mock private DataLakeAdminApiClient _dataLakeAdminApiClient;

  private OnlineArchiveRestorationSvc _restorationSvc;

  @BeforeEach
  public void setUp() throws Exception {
    _restorationSvc =
        spy(
            new OnlineArchiveRestorationSvc(
                _onlineArchiveSettings,
                _restoreOADatasetRequestDao,
                _planSvc,
                _onlineArchiveSvc,
                _onlineArchiveV3MigrationDao,
                _restoreDLSJobDao,
                _awsAccountDao,
                _jobsProcessorSvc,
                _azureApiSvc,
                _azureStorageAccountDao,
                _gcpOrganizationSvc,
                _gcpApiSvc,
                _ndsDataSetDao,
                _dataLakeAdminApiClient));
    doReturn(1).when(_onlineArchiveSettings).getOnlineArchiveRestorationRequestMaxRunningPlans();
  }

  @Test
  public void testHandleRestorationJobRequests() {
    doNothing().when(_restorationSvc).handleInterruptedRestoreDLSJobs();
    doNothing().when(_restorationSvc).submitRestorationJobRequest(any());

    // running jobs exceeds max allowed
    doReturn(1).when(_restoreDLSJobDao).getNumberOfQueuedJobs();
    doReturn(3).when(_restoreDLSJobDao).getNumberOfRunningJobs();
    doReturn(2).when(_onlineArchiveSettings).getOnlineArchiveMaxRunningRestoreDLSJobs();

    _restorationSvc.handleRestorationJobRequests();
    verify(_restorationSvc, never()).submitRestorationJobRequest(any());

    // no queued
    doReturn(0).when(_restoreDLSJobDao).getNumberOfQueuedJobs();
    doReturn(0).when(_restoreDLSJobDao).getNumberOfRunningJobs();
    doReturn(2).when(_onlineArchiveSettings).getOnlineArchiveMaxRunningRestoreDLSJobs();

    _restorationSvc.handleRestorationJobRequests();
    verify(_restorationSvc, never()).submitRestorationJobRequest(any());

    // no available slots
    doReturn(1).when(_restoreDLSJobDao).getNumberOfQueuedJobs();
    doReturn(3).when(_restoreDLSJobDao).getNumberOfRunningJobs();
    doReturn(2).when(_onlineArchiveSettings).getOnlineArchiveMaxRunningRestoreDLSJobs();

    _restorationSvc.handleRestorationJobRequests();
    verify(_restorationSvc, never()).submitRestorationJobRequest(any());

    // available slots
    doReturn(3).when(_restoreDLSJobDao).getNumberOfQueuedJobs();
    doReturn(1).when(_restoreDLSJobDao).getNumberOfRunningJobs();
    doReturn(3).when(_onlineArchiveSettings).getOnlineArchiveMaxRunningRestoreDLSJobs();

    final ObjectId restoreDLSJobRequestId1 = new ObjectId();
    final ObjectId restoreDLSJobRequestId2 = new ObjectId();
    final ObjectId restoreDLSJobRequestId3 = new ObjectId();
    doReturn(List.of(restoreDLSJobRequestId1, restoreDLSJobRequestId2, restoreDLSJobRequestId3))
        .when(_restoreDLSJobDao)
        .getDistinctRestorationRequestIdsByJobState(eq(RestoreDLSJob.State.QUEUED));

    // Test 1: test num queued > num slots
    // two restoration requests with 4 queued jobs, two slots,
    // should be distributed across both restoration requests
    final RestoreDLSJob job1 =
        new RestoreDLSJob.Builder().id(new ObjectId()).state(RestoreDLSJob.State.QUEUED).build();
    final RestoreDLSJob job2 =
        new RestoreDLSJob.Builder().id(new ObjectId()).state(RestoreDLSJob.State.QUEUED).build();
    final RestoreDLSJob job3 =
        new RestoreDLSJob.Builder().id(new ObjectId()).state(RestoreDLSJob.State.QUEUED).build();
    final RestoreDLSJob job4 =
        new RestoreDLSJob.Builder().id(new ObjectId()).state(RestoreDLSJob.State.QUEUED).build();
    final RestoreDLSJob job5 =
        new RestoreDLSJob.Builder().id(new ObjectId()).state(RestoreDLSJob.State.QUEUED).build();
    final RestoreDLSJob job6 =
        new RestoreDLSJob.Builder().id(new ObjectId()).state(RestoreDLSJob.State.QUEUED).build();

    doReturn(Optional.of(job1), Optional.of(job2), Optional.empty())
        .when(_restoreDLSJobDao)
        .findOneQueuedByRestorationRequest(eq(restoreDLSJobRequestId1));

    doReturn(Optional.of(job3), Optional.of(job4), Optional.empty())
        .when(_restoreDLSJobDao)
        .findOneQueuedByRestorationRequest(eq(restoreDLSJobRequestId2));

    doReturn(Optional.of(job5), Optional.of(job6), Optional.empty())
        .when(_restoreDLSJobDao)
        .findOneQueuedByRestorationRequest(eq(restoreDLSJobRequestId3));

    _restorationSvc.handleRestorationJobRequests();
    verify(_restorationSvc, times(2)).submitRestorationJobRequest(any());
    verify(_restorationSvc, times(1)).submitRestorationJobRequest(eq(job1));
    verify(_restorationSvc, times(0)).submitRestorationJobRequest(eq(job2));
    verify(_restorationSvc, times(1)).submitRestorationJobRequest(eq(job3));
    verify(_restorationSvc, times(0)).submitRestorationJobRequest(eq(job4));
    verify(_restorationSvc, times(0)).submitRestorationJobRequest(eq(job5));
    verify(_restorationSvc, times(0)).submitRestorationJobRequest(eq(job6));

    // Test 2: test num queued <= num slots
    // 4 restoration requests with 4 queued jobs, 4 slots,
    // should be distributed across all 4 restoration requests
    doReturn(4).when(_restoreDLSJobDao).getNumberOfQueuedJobs();
    doReturn(0).when(_restoreDLSJobDao).getNumberOfRunningJobs();
    doReturn(4).when(_onlineArchiveSettings).getOnlineArchiveMaxRunningRestoreDLSJobs();

    doReturn(
            List.of(restoreDLSJobRequestId1, restoreDLSJobRequestId2, restoreDLSJobRequestId3),
            List.of(restoreDLSJobRequestId3))
        .when(_restoreDLSJobDao)
        .getDistinctRestorationRequestIdsByJobState(eq(RestoreDLSJob.State.QUEUED));

    _restorationSvc.handleRestorationJobRequests();
    verify(_restorationSvc, times(6))
        .submitRestorationJobRequest(any()); //  total including previous test
    verify(_restorationSvc, times(1)).submitRestorationJobRequest(eq(job2));
    verify(_restorationSvc, times(1)).submitRestorationJobRequest(eq(job4));
    verify(_restorationSvc, times(1)).submitRestorationJobRequest(eq(job5));
    verify(_restorationSvc, times(1)).submitRestorationJobRequest(eq(job6));
  }

  @Test
  public void testHandleInterruptedUploadJobs() {
    final RestoreDLSJob restoreDLSJob1 =
        new RestoreDLSJob.Builder()
            .id(new ObjectId())
            .jobQueueJobId(new ObjectId())
            .state(RestoreDLSJob.State.IN_PROGRESS)
            .build();
    final RestoreDLSJob restoreDLSJob2 =
        new RestoreDLSJob.Builder()
            .id(new ObjectId())
            .jobQueueJobId(new ObjectId())
            .state(RestoreDLSJob.State.IN_PROGRESS)
            .build();

    final Job job1 = mock(Job.class);
    when(job1.getStatus()).thenReturn(Job.Status.OWNED);

    final Job job2 = mock(Job.class);
    when(job2.getStatus()).thenReturn(Job.Status.RETRY_EXHAUSTED_FAIL);
    when(_restoreDLSJobDao.findRunning()).thenReturn(Arrays.asList(restoreDLSJob1, restoreDLSJob2));

    doReturn(job1).when(_jobsProcessorSvc).findJobById(eq(restoreDLSJob1.getJobQueueJobId()));
    doReturn(job2).when(_jobsProcessorSvc).findJobById(eq(restoreDLSJob2.getJobQueueJobId()));

    _restorationSvc.handleInterruptedRestoreDLSJobs();

    verify(_restoreDLSJobDao, times(0))
        .setFailed(eq(restoreDLSJob1.getId()), any(), contains("job interrupted by job queue"));
    verify(_restoreDLSJobDao, times(1))
        .setFailed(eq(restoreDLSJob2.getId()), any(), contains("job interrupted by job queue"));
    verify(_restorationSvc, times(1)).incrementErrorCounter(any(), any());
  }

  @Test
  public void testHandleRestorationRequestPlans() {
    doNothing().when(_restorationSvc).submitPlanForRestorationRequest(any());

    // max running exceeds num running
    doReturn(2).when(_restoreOADatasetRequestDao).getRunningRestorationRequestsCount();
    _restorationSvc.handleRestorationRequestPlans();
    verify(_restoreOADatasetRequestDao, never()).findQueued(anyInt());
    verify(_restorationSvc, never()).submitPlanForRestorationRequest(any());

    // none queued
    doReturn(0).when(_restoreOADatasetRequestDao).getRunningRestorationRequestsCount();
    doReturn(0).when(_restoreOADatasetRequestDao).getQueuedRestorationRequestsCount();
    _restorationSvc.handleRestorationRequestPlans();
    verify(_restoreOADatasetRequestDao, never()).findQueued(anyInt());
    verify(_restorationSvc, never()).submitPlanForRestorationRequest(any());

    // no available slots
    doReturn(1).when(_restoreOADatasetRequestDao).getRunningRestorationRequestsCount();
    doReturn(1).when(_restoreOADatasetRequestDao).getQueuedRestorationRequestsCount();
    _restorationSvc.handleRestorationRequestPlans();
    verify(_restoreOADatasetRequestDao, never()).findQueued(anyInt());
    verify(_restorationSvc, never()).submitPlanForRestorationRequest(any());

    // success
    doReturn(0).when(_restoreOADatasetRequestDao).getRunningRestorationRequestsCount();
    doReturn(List.of(mock(RestoreOADatasetRequest.class)))
        .when(_restoreOADatasetRequestDao)
        .findQueued(eq(1));
    _restorationSvc.handleRestorationRequestPlans();
    verify(_restorationSvc, times(1)).submitPlanForRestorationRequest(any());
  }

  @Test
  public void testVerifyArchiveIsPausedForRestoration() {
    final OnlineArchive archive = mock(OnlineArchive.class);
    doReturn(OnlineArchive.State.PAUSED).when(archive).getState();

    _restorationSvc.verifyArchiveIsPausedForRestoration(archive);

    doReturn(OnlineArchive.State.PAUSING).when(archive).getState();
    try {
      _restorationSvc.verifyArchiveIsPausedForRestoration(archive);
      fail();
    } catch (Exception pE) {
      assertTrue(pE.getMessage().contains("Archive is not in required PAUSED state"));
    }
  }

  @Test
  public void testCleanUpRestorationRequestsUnusedDataSets() throws DataLakeAdminApiException {
    final RestoreOADatasetRequest request = mock(RestoreOADatasetRequest.class);
    String existingDatasetName = "existing";
    String targetDatasetName = "target";
    doReturn(existingDatasetName).when(request).getExistingDatasetName();
    doReturn(targetDatasetName).when(request).getTargetDatasetName();
    final NDSDataSetOA dataSetOA =
        new NDSDataSetOA(
            new BasicDBObject().append(NDSDataSet.FieldDefs.STATE, NDSDataSet.State.CREATED));
    doReturn(List.of(request))
        .when(_restoreOADatasetRequestDao)
        .findRestorationRequestsToCleanupUnusedDataSets();
    doReturn(Optional.of(dataSetOA)).when(_ndsDataSetDao).findByGroupIdAndName(any(), any());

    // skip destroying existing dataset if dataset being used for archive - success
    final OnlineArchive archive = mock(OnlineArchive.class);
    doReturn(existingDatasetName).when(archive).getDataSetName();
    doReturn(Optional.of(archive)).when(_onlineArchiveSvc).getOnlineArchive(any());
    doReturn(List.of()).when(_restoreOADatasetRequestDao).findUnfinishedByTargetDatasetName(any());

    _restorationSvc.cleanUpRestorationRequestsUnusedDataSets();

    verify(_restorationSvc, times(2))
        .performUnusedDatasetCleanup(eq(Optional.of(archive)), eq(request), any());
    verify(_restorationSvc, times(1))
        .performUnusedDatasetCleanup(
            eq(Optional.of(archive)), eq(request), eq(existingDatasetName));
    verify(_restorationSvc, times(1))
        .performUnusedDatasetCleanup(eq(Optional.of(archive)), eq(request), eq(targetDatasetName));
    verify(_dataLakeAdminApiClient, times(1)).destroyDataSets(any(), any());
    verify(_restoreOADatasetRequestDao, times(1))
        .setRestorationRequestCleanupUnusedDataSetsCompleted(any(), any());

    // skip destroying target dataset if being used in restoration request - success
    doReturn(List.of(mock(RestoreOADatasetRequest.class)))
        .when(_restoreOADatasetRequestDao)
        .findUnfinishedByTargetDatasetName(eq(targetDatasetName));

    _restorationSvc.cleanUpRestorationRequestsUnusedDataSets();

    verify(_restorationSvc, times(4))
        .performUnusedDatasetCleanup(eq(Optional.of(archive)), eq(request), any());
    verify(_restorationSvc, times(2))
        .performUnusedDatasetCleanup(
            eq(Optional.of(archive)), eq(request), eq(existingDatasetName));
    verify(_restorationSvc, times(2))
        .performUnusedDatasetCleanup(eq(Optional.of(archive)), eq(request), eq(targetDatasetName));
    verify(_dataLakeAdminApiClient, times(1)).destroyDataSets(any(), any());
    verify(_restoreOADatasetRequestDao, times(2))
        .setRestorationRequestCleanupUnusedDataSetsCompleted(any(), any());

    // error when destroying existing dataset - fail
    doReturn(targetDatasetName).when(archive).getDataSetName();
    doThrow(new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR))
        .when(_dataLakeAdminApiClient)
        .destroyDataSets(any(), any());

    _restorationSvc.cleanUpRestorationRequestsUnusedDataSets();

    verify(_restorationSvc, times(6))
        .performUnusedDatasetCleanup(eq(Optional.of(archive)), eq(request), any());
    verify(_restorationSvc, times(3))
        .performUnusedDatasetCleanup(
            eq(Optional.of(archive)), eq(request), eq(existingDatasetName));
    verify(_restorationSvc, times(3))
        .performUnusedDatasetCleanup(eq(Optional.of(archive)), eq(request), eq(targetDatasetName));
    verify(_dataLakeAdminApiClient, times(2)).destroyDataSets(any(), any());
    verify(_restoreOADatasetRequestDao, times(2))
        .setRestorationRequestCleanupUnusedDataSetsCompleted(any(), any());
  }
}
