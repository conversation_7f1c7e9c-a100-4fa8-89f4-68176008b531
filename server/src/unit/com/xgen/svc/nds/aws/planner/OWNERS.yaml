version: 1.0.0
aliases:
  - //.github/CODEOWNERS-aliases.yml
filters:
  - "*":
    approvers:
      - 10gen/code-review-team-atlas-clusters-performance
  - "/AWSSetupInstanceTargetGroup*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/AWSSetupInstanceTargetGroupForProxyProtocolStep*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-ii
  - "/AwsSwapIpMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform-ii
  - "/AWSSyncClusterWithPrivateLinkMove*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/AWSSetupMultiInstanceTargetGroupStepUnitTests*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/AWSDeleteEniStep*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-platform-ii
  - "/AWSEnsureSecurityGroupStepUnitTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/AWSEnsureNetworkPermissionsAppliedMoveUnitTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/AWSCreatePrometheusInstanceTargetGroupStepUnitTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/AWSDeletePrometheusInstanceTargetGroupStepUnitTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/AWSModifyPrometheusInstanceTargetGroupStepUnitTests.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security
  - "/privatenetworking/AWSPrometheusPrivateNetworking*.java":
    approvers:
      - 10gen/code-review-team-atlas-clusters-security-ii
