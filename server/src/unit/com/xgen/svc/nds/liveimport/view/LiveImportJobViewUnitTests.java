package com.xgen.svc.nds.liveimport.view;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.module.liveimport.model.LiveImport;
import com.xgen.module.liveimport.model.LiveImport.MigrationType;
import com.xgen.module.liveimport.model.MongoMirrorStatus.MongoMirrorStage;
import com.xgen.module.liveimport.model.MongomirrorPullLiveImport;
import com.xgen.module.liveimport.model.MongosyncPullLiveImport;
import com.xgen.module.liveimport.model.MongosyncPullLiveImport.UserWriteBlockingMode;
import com.xgen.module.liveimport.model.MongosyncShardCollection;
import com.xgen.module.liveimport.model.MongosyncShardEntry;
import com.xgen.module.liveimport.model.MongosyncShardingParams;
import com.xgen.module.liveimport.model.MongosyncStatus.MongosyncStage;
import com.xgen.module.liveimport.model.PullLiveImport;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;

public class LiveImportJobViewUnitTests {

  @Test
  public void testParse() throws JsonProcessingException {
    final ObjectMapper mapper = new ObjectMapper();
    final JSONObject json = new JSONObject();
    json.put("clusterName", "clusterName");
    json.put("hostname", "hostname");
    json.put("username", "username");
    json.put("password", "password");
    json.put("displayLabel", "displayLabel");
    json.put("caFile", "caFile");
    json.put("dropEnabled", true);
    json.put("sslEnabled", true);
    json.put("oplogBatchSize", 10d);
    json.put("collStatsThreshold", 20d);

    final LiveImportJobView v = mapper.readValue(json.toString(), LiveImportJobView.class);

    assertEquals("clusterName", v.getClusterName());
    assertEquals("hostname", v.getHostname());
    assertEquals("username", v.getUsername());
    assertEquals("password", v.getPassword());
    assertEquals("displayLabel", v.getDisplayLabel());
    assertEquals("caFile", v.getCaFile());
    assertTrue(v.isDropEnabled());
    assertTrue(v.isSslEnabled());
    assertEquals(Double.valueOf(10), v.getOplogBatchSize());
    assertEquals(Double.valueOf(20), v.getCollStatsThreshold());
  }

  @Test
  public void testParseAtlasToAtlas() throws JsonProcessingException {
    final ObjectMapper mapper = new ObjectMapper();
    final JSONObject json = new JSONObject();
    json.put("sourceGroupId", oid(1));
    json.put("sourceClusterName", "sourceClusterName");
    json.put("groupId", oid(2));
    json.put("clusterName", "clusterName");
    json.put("verificationEnabled", true);
    json.put("dropEnabled", true);
    json.put("migrationType", "ATLAS_TO_ATLAS");

    final LiveImportJobView v = mapper.readValue(json.toString(), LiveImportJobView.class);

    assertEquals("clusterName", v.getClusterName());
    assertEquals(oid(2), v.getDestinationGroupId());
    assertEquals("sourceClusterName", v.getSourceClusterName());
    assertEquals(oid(1), v.getSourceGroupId());
    assertTrue(v.isDropEnabled());
    assertTrue(v.isVerificationEnabled());
    assertEquals(MigrationType.ATLAS_TO_ATLAS, v.getMigrationType());
  }

  @Test
  public void testParseSelfToAtlas() throws JsonProcessingException {
    final ObjectMapper mapper = new ObjectMapper();
    final JSONObject json = new JSONObject();
    json.put("sourceConnectionString", "sourceConnectionString");
    json.put("groupId", oid(2));
    json.put("clusterName", "clusterName");
    json.put("verificationEnabled", true);
    json.put("dropEnabled", true);
    json.put("migrationType", "SELF_TO_ATLAS");

    final LiveImportJobView v = mapper.readValue(json.toString(), LiveImportJobView.class);

    assertEquals("clusterName", v.getClusterName());
    assertEquals(oid(2), v.getDestinationGroupId());
    assertEquals("sourceConnectionString", v.getSourceConnectionString());
    assertTrue(v.isDropEnabled());
    assertTrue(v.isVerificationEnabled());
    assertEquals(MigrationType.SELF_TO_ATLAS, v.getMigrationType());
  }

  @Test
  public void testToLiveImport_mongomirror() {
    final LiveImportJobView v =
        new LiveImportJobView(
            "clusterName",
            "hostname",
            null,
            "displayLabel",
            "username",
            "password",
            "caFile",
            true,
            true,
            10d,
            20d,
            null,
            "5.0",
            MigrationType.LEGACY_MIGRATION);
    final PullLiveImport mongomirrorLiveImport =
        v.toLiveImport(
            oid(1), oid(2), oid(3), oid(4), LiveImport.MigrationToolType.MONGOMIRROR, null);

    assertNotNull(mongomirrorLiveImport);
    assertTrue(mongomirrorLiveImport instanceof MongomirrorPullLiveImport);
    assertTrue(mongomirrorLiveImport.isInProgress());
    assertEquals(LiveImport.State.NEW, mongomirrorLiveImport.getState());
    assertNull(mongomirrorLiveImport.getExpireDate());
    assertNull(mongomirrorLiveImport.getStopDate());
    assertFalse(mongomirrorLiveImport.getMigrationToolVersion().isPresent());
    assertTrue(mongomirrorLiveImport.getShardIndexesRestartCounts().isEmpty());
    assertTrue(mongomirrorLiveImport.getShardIndexesNeedingRestart().isEmpty());
    assertTrue(mongomirrorLiveImport.getShardIndexesNeedingResync().isEmpty());
    assertTrue(mongomirrorLiveImport.getReplicationLagPerShard().isEmpty());
    assertEquals("clusterName", mongomirrorLiveImport.getDestination().getClusterName());
    assertEquals(oid(1), mongomirrorLiveImport.getGroupId());
    assertEquals(oid(2), mongomirrorLiveImport.getDestination().getClusterUniqueId());
    assertEquals(oid(3), mongomirrorLiveImport.getLiveImportLocation());
    assertTrue(mongomirrorLiveImport.getRequesterUserId().isPresent());
    assertEquals(oid(4), mongomirrorLiveImport.getRequesterUserId().get());
    assertEquals("hostname", mongomirrorLiveImport.getSource().getHostname());
    assertEquals("username", mongomirrorLiveImport.getSource().getUsername());
    assertEquals("password", mongomirrorLiveImport.getSource().getPassword());
    assertEquals("displayLabel", mongomirrorLiveImport.getSource().getDisplayLabel());
    assertEquals("caFile", mongomirrorLiveImport.getSource().getCaFile());
    assertEquals(Collections.emptyList(), mongomirrorLiveImport.getSource().getReplicaSetsUris());
    assertTrue(mongomirrorLiveImport.getSource().sslEnabled());
    assertTrue(mongomirrorLiveImport.isDropEnabled());
    assertFalse(mongomirrorLiveImport.isAcknowledged());
    assertFalse(mongomirrorLiveImport.isCancelled());
    assertFalse(mongomirrorLiveImport.isCutoverComplete());
    assertFalse(mongomirrorLiveImport.isExpired());
    assertEquals(Collections.emptyList(), mongomirrorLiveImport.getPorts());
    assertTrue(mongomirrorLiveImport.getOplogBatchSize().isPresent());
    assertEquals(Double.valueOf(10), mongomirrorLiveImport.getOplogBatchSize().get());
    assertTrue(mongomirrorLiveImport.getCollStatsThreshold().isPresent());
    assertEquals(Double.valueOf(20), mongomirrorLiveImport.getCollStatsThreshold().get());
    assertEquals(1, mongomirrorLiveImport.getMigrationStatuses().size());
    assertEquals(
        MongoMirrorStage.INITIALIZING,
        mongomirrorLiveImport.getMigrationStatuses().get(0).getStage());
  }

  @Test
  public void testToLiveImport_mongosync() {
    final LiveImportJobView v =
        new LiveImportJobView(
            "clusterName",
            "hostname",
            null,
            "displayLabel",
            "username",
            "password",
            "caFile",
            true,
            true,
            10d,
            20d,
            MongosyncShardingParams.builder()
                .withCreateSupportingIndexes(true)
                .withShardingEntries(
                    List.of(
                        MongosyncShardEntry.builder()
                            .withDatabase("db")
                            .withCollection("test")
                            .build()))
                .build(),
            "6.0",
            MigrationType
                .LEGACY_MIGRATION); // sharding params added should be null for a replica set
    // destination
    final MongosyncPullLiveImport mongosyncLiveImport =
        (MongosyncPullLiveImport)
            v.toLiveImport(
                oid(1),
                oid(2),
                oid(3),
                oid(4),
                LiveImport.MigrationToolType.MONGOSYNC,
                UserWriteBlockingMode.DESTINATION_ONLY);

    assertNotNull(mongosyncLiveImport);
    assertTrue(mongosyncLiveImport instanceof MongosyncPullLiveImport);
    assertTrue(mongosyncLiveImport.isInProgress());
    assertEquals(LiveImport.State.NEW, mongosyncLiveImport.getState());
    assertNull(mongosyncLiveImport.getExpireDate());
    assertNull(mongosyncLiveImport.getStopDate());
    assertFalse(mongosyncLiveImport.getMigrationToolVersion().isPresent());
    assertTrue(mongosyncLiveImport.getShardIndexesRestartCounts().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingRestart().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingResync().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingMongosyncRestart().isEmpty());
    assertTrue(mongosyncLiveImport.getReplicationLagPerShard().isEmpty());
    assertEquals("clusterName", mongosyncLiveImport.getDestination().getClusterName());
    assertEquals(oid(1), mongosyncLiveImport.getGroupId());
    assertEquals(oid(2), mongosyncLiveImport.getDestination().getClusterUniqueId());
    assertEquals(oid(3), mongosyncLiveImport.getLiveImportLocation());
    assertTrue(mongosyncLiveImport.getRequesterUserId().isPresent());
    assertEquals(oid(4), mongosyncLiveImport.getRequesterUserId().get());
    assertEquals("hostname", mongosyncLiveImport.getSource().getHostname());
    assertEquals("username", mongosyncLiveImport.getSource().getUsername());
    assertEquals("password", mongosyncLiveImport.getSource().getPassword());
    assertEquals("displayLabel", mongosyncLiveImport.getSource().getDisplayLabel());
    assertEquals("caFile", mongosyncLiveImport.getSource().getCaFile());
    assertEquals(Collections.emptyList(), mongosyncLiveImport.getSource().getReplicaSetsUris());
    assertTrue(mongosyncLiveImport.getSource().sslEnabled());
    assertTrue(mongosyncLiveImport.isDropEnabled());
    assertFalse(mongosyncLiveImport.isAcknowledged());
    assertFalse(mongosyncLiveImport.isCancelled());
    assertFalse(mongosyncLiveImport.isCutoverComplete());
    assertFalse(mongosyncLiveImport.isExpired());
    assertEquals(Collections.emptyList(), mongosyncLiveImport.getPorts());
    assertTrue(mongosyncLiveImport.getOplogBatchSize().isPresent());
    assertEquals(Double.valueOf(10), mongosyncLiveImport.getOplogBatchSize().get());
    assertTrue(mongosyncLiveImport.getCollStatsThreshold().isPresent());
    assertEquals(Double.valueOf(20), mongosyncLiveImport.getCollStatsThreshold().get());
    assertEquals(1, mongosyncLiveImport.getMigrationStatuses().size());
    assertEquals(
        MongosyncStage.INITIALIZING, mongosyncLiveImport.getMigrationStatuses().get(0).getStage());
    assertNull(mongosyncLiveImport.getMongosyncShardingParams());
    assertEquals(
        UserWriteBlockingMode.DESTINATION_ONLY, mongosyncLiveImport.getUserWriteBlocking());
  }

  @Test
  public void testToLiveImportAtlasToAtlas() {
    final LiveImportJobView v =
        new LiveImportJobView(
            oid(1),
            "sourceClusterName",
            oid(2),
            "destinationClusterName",
            true,
            MongosyncShardingParams.builder()
                .withCreateSupportingIndexes(true)
                .withShardingEntries(
                    List.of(
                        MongosyncShardEntry.builder()
                            .withDatabase("db")
                            .withCollection("test")
                            .build()))
                .build(),
            MigrationType.ATLAS_TO_ATLAS);

    final MongosyncPullLiveImport mongosyncLiveImport =
        (MongosyncPullLiveImport)
            v.toLiveImportAtlasToAtlas(
                oid(1),
                oid(2),
                oid(3),
                oid(4),
                LiveImport.MigrationToolType.MONGOSYNC,
                UserWriteBlockingMode.DESTINATION_ONLY);

    assertNotNull(mongosyncLiveImport);
    assertTrue(mongosyncLiveImport instanceof MongosyncPullLiveImport);
    assertTrue(mongosyncLiveImport.isInProgress());
    assertEquals(LiveImport.State.NEW, mongosyncLiveImport.getState());
    assertNull(mongosyncLiveImport.getExpireDate());
    assertNull(mongosyncLiveImport.getStopDate());
    assertFalse(mongosyncLiveImport.getMigrationToolVersion().isPresent());
    assertTrue(mongosyncLiveImport.getShardIndexesRestartCounts().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingRestart().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingResync().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingMongosyncRestart().isEmpty());
    assertTrue(mongosyncLiveImport.getReplicationLagPerShard().isEmpty());
    assertEquals("destinationClusterName", mongosyncLiveImport.getDestinationClusterName());
    assertEquals(oid(3), mongosyncLiveImport.getLiveImportLocation());
    assertTrue(mongosyncLiveImport.getRequesterUserId().isPresent());
    assertEquals(oid(4), mongosyncLiveImport.getRequesterUserId().get());
    assertTrue(mongosyncLiveImport.isDropEnabled());
    assertFalse(mongosyncLiveImport.isAcknowledged());
    assertFalse(mongosyncLiveImport.isCancelled());
    assertFalse(mongosyncLiveImport.isCutoverComplete());
    assertFalse(mongosyncLiveImport.isExpired());
    assertEquals(Collections.emptyList(), mongosyncLiveImport.getPorts());
    assertEquals(1, mongosyncLiveImport.getMigrationStatuses().size());
    assertEquals(
        MongosyncStage.INITIALIZING, mongosyncLiveImport.getMigrationStatuses().get(0).getStage());
    assertNull(mongosyncLiveImport.getMongosyncShardingParams());
    assertEquals(
        UserWriteBlockingMode.DESTINATION_ONLY, mongosyncLiveImport.getUserWriteBlocking());
  }

  @Test
  public void testToShardedClusterLiveImportAtlasToAtlas() {
    final LiveImportJobView v =
        new LiveImportJobView(
            oid(1),
            "sourceClusterName",
            oid(2),
            "destinationClusterName",
            true,
            MongosyncShardingParams.builder()
                .withCreateSupportingIndexes(true)
                .withShardingEntries(
                    List.of(
                        MongosyncShardEntry.builder()
                            .withDatabase("db")
                            .withCollection("test")
                            .build()))
                .build(),
            MigrationType.ATLAS_TO_ATLAS);
    final PullLiveImport mongosyncLiveImport =
        v.toShardedClusterLiveImportAtlasToAtlas(
            oid(1),
            oid(2),
            3,
            oid(3),
            oid(4),
            LiveImport.MigrationToolType.MONGOSYNC,
            UserWriteBlockingMode.DESTINATION_ONLY);
    assertInstanceOf(MongosyncPullLiveImport.class, mongosyncLiveImport);
    assertTrue(mongosyncLiveImport.isInProgress());
    assertEquals(LiveImport.State.NEW, mongosyncLiveImport.getState());
    assertNull(mongosyncLiveImport.getExpireDate());
    assertNull(mongosyncLiveImport.getStopDate());
    assertFalse(mongosyncLiveImport.getMigrationToolVersion().isPresent());
    assertTrue(mongosyncLiveImport.getShardIndexesRestartCounts().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingRestart().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingResync().isEmpty());
    assertTrue(
        ((MongosyncPullLiveImport) mongosyncLiveImport)
            .getShardIndexesNeedingMongosyncRestart()
            .isEmpty());
    assertTrue(mongosyncLiveImport.getReplicationLagPerShard().isEmpty());
    assertEquals("destinationClusterName", mongosyncLiveImport.getDestination().getClusterName());
    assertEquals(oid(1), mongosyncLiveImport.getDestination().getClusterUniqueId());
    assertEquals(oid(3), mongosyncLiveImport.getLiveImportLocation());
    assertTrue(mongosyncLiveImport.getRequesterUserId().isPresent());
    assertEquals(oid(4), mongosyncLiveImport.getRequesterUserId().get());
    assertEquals(Collections.emptyList(), mongosyncLiveImport.getSource().getReplicaSetsUris());
    assertTrue(mongosyncLiveImport.isDropEnabled());
    assertFalse(mongosyncLiveImport.isAcknowledged());
    assertFalse(mongosyncLiveImport.isCancelled());
    assertFalse(mongosyncLiveImport.isCutoverComplete());
    assertFalse(mongosyncLiveImport.isExpired());
    assertEquals(Collections.emptyList(), mongosyncLiveImport.getPorts());
    assertEquals(1, mongosyncLiveImport.getMigrationStatuses().size());
    assertEquals(
        MongosyncStage.INITIALIZING, mongosyncLiveImport.getMigrationStatuses().get(0).getStage());
    assertNotNull(((MongosyncPullLiveImport) mongosyncLiveImport).getMongosyncShardingParams());
    assertEquals(
        UserWriteBlockingMode.DESTINATION_ONLY,
        ((MongosyncPullLiveImport) mongosyncLiveImport).getUserWriteBlocking());
  }

  @Test
  public void testToLiveImportSelfToAtlas() {
    final LiveImportJobView v =
        new LiveImportJobView(
            "mongodb://user:pass/host:12345",
            oid(2),
            "destinationClusterName",
            false,
            "",
            true,
            MongosyncShardingParams.builder()
                .withCreateSupportingIndexes(true)
                .withShardingEntries(
                    List.of(
                        MongosyncShardEntry.builder()
                            .withDatabase("db")
                            .withCollection("test")
                            .build()))
                .build(),
            MigrationType.SELF_TO_ATLAS);

    final MongosyncPullLiveImport mongosyncLiveImport =
        (MongosyncPullLiveImport)
            v.toLiveImportSelfToAtlas(
                oid(1),
                oid(2),
                oid(3),
                LiveImport.MigrationToolType.MONGOSYNC,
                UserWriteBlockingMode.DESTINATION_ONLY);

    assertNotNull(mongosyncLiveImport);
    assertTrue(mongosyncLiveImport instanceof MongosyncPullLiveImport);
    assertTrue(mongosyncLiveImport.isInProgress());
    assertEquals(LiveImport.State.NEW, mongosyncLiveImport.getState());
    assertNull(mongosyncLiveImport.getExpireDate());
    assertNull(mongosyncLiveImport.getStopDate());
    assertFalse(mongosyncLiveImport.getMigrationToolVersion().isPresent());
    assertTrue(mongosyncLiveImport.getShardIndexesRestartCounts().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingRestart().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingResync().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingMongosyncRestart().isEmpty());
    assertTrue(mongosyncLiveImport.getReplicationLagPerShard().isEmpty());
    assertEquals("destinationClusterName", mongosyncLiveImport.getDestinationClusterName());
    assertEquals(oid(2), mongosyncLiveImport.getLiveImportLocation());
    assertTrue(mongosyncLiveImport.getRequesterUserId().isPresent());
    assertEquals(oid(3), mongosyncLiveImport.getRequesterUserId().get());
    assertTrue(mongosyncLiveImport.isDropEnabled());
    assertFalse(mongosyncLiveImport.isAcknowledged());
    assertFalse(mongosyncLiveImport.isCancelled());
    assertFalse(mongosyncLiveImport.isCutoverComplete());
    assertFalse(mongosyncLiveImport.isExpired());
    assertEquals(Collections.emptyList(), mongosyncLiveImport.getPorts());
    assertEquals(1, mongosyncLiveImport.getMigrationStatuses().size());
    assertEquals(
        MongosyncStage.INITIALIZING, mongosyncLiveImport.getMigrationStatuses().get(0).getStage());
    assertNull(mongosyncLiveImport.getMongosyncShardingParams());
    assertEquals(
        UserWriteBlockingMode.DESTINATION_ONLY, mongosyncLiveImport.getUserWriteBlocking());
  }

  @Test
  public void testToShardedClusterLiveImportSelfToAtlas() {
    final LiveImportJobView v =
        new LiveImportJobView(
            "******************************",
            oid(2),
            "destinationClusterName",
            false,
            "",
            true,
            MongosyncShardingParams.builder()
                .withCreateSupportingIndexes(true)
                .withShardingEntries(
                    List.of(
                        MongosyncShardEntry.builder()
                            .withDatabase("db")
                            .withCollection("test")
                            .build()))
                .build(),
            MigrationType.SELF_TO_ATLAS);
    final PullLiveImport mongosyncLiveImport =
        v.toShardedClusterLiveImportSelfToAtlas(
            oid(1),
            3,
            oid(2),
            oid(3),
            LiveImport.MigrationToolType.MONGOSYNC,
            UserWriteBlockingMode.DESTINATION_ONLY);
    assertInstanceOf(MongosyncPullLiveImport.class, mongosyncLiveImport);
    assertTrue(mongosyncLiveImport.isInProgress());
    assertEquals(LiveImport.State.NEW, mongosyncLiveImport.getState());
    assertNull(mongosyncLiveImport.getExpireDate());
    assertNull(mongosyncLiveImport.getStopDate());
    assertFalse(mongosyncLiveImport.getMigrationToolVersion().isPresent());
    assertTrue(mongosyncLiveImport.getShardIndexesRestartCounts().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingRestart().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingResync().isEmpty());
    assertTrue(
        ((MongosyncPullLiveImport) mongosyncLiveImport)
            .getShardIndexesNeedingMongosyncRestart()
            .isEmpty());
    assertTrue(mongosyncLiveImport.getReplicationLagPerShard().isEmpty());
    assertEquals("destinationClusterName", mongosyncLiveImport.getDestination().getClusterName());
    assertEquals(oid(1), mongosyncLiveImport.getDestination().getClusterUniqueId());
    assertEquals(oid(2), mongosyncLiveImport.getLiveImportLocation());
    assertTrue(mongosyncLiveImport.getRequesterUserId().isPresent());
    assertEquals(oid(3), mongosyncLiveImport.getRequesterUserId().get());
    assertEquals(Collections.emptyList(), mongosyncLiveImport.getSource().getReplicaSetsUris());
    assertTrue(mongosyncLiveImport.isDropEnabled());
    assertFalse(mongosyncLiveImport.isAcknowledged());
    assertFalse(mongosyncLiveImport.isCancelled());
    assertFalse(mongosyncLiveImport.isCutoverComplete());
    assertFalse(mongosyncLiveImport.isExpired());
    assertEquals(Collections.emptyList(), mongosyncLiveImport.getPorts());
    assertEquals(1, mongosyncLiveImport.getMigrationStatuses().size());
    assertEquals(
        MongosyncStage.INITIALIZING, mongosyncLiveImport.getMigrationStatuses().get(0).getStage());
    assertNotNull(((MongosyncPullLiveImport) mongosyncLiveImport).getMongosyncShardingParams());
    assertEquals(
        UserWriteBlockingMode.DESTINATION_ONLY,
        ((MongosyncPullLiveImport) mongosyncLiveImport).getUserWriteBlocking());
  }

  @Test
  public void testToShardedClusterLiveImport() {
    final LiveImportJobView v =
        new LiveImportJobView(
            "clusterName",
            "hostname",
            null,
            "displayLabel",
            "username",
            "password",
            "caFile",
            true,
            true,
            10d,
            20d,
            null,
            "5.0",
            MigrationType.LEGACY_MIGRATION);
    final PullLiveImport mongomirrorLiveImport =
        v.toShardedClusterLiveImport(
            oid(1), oid(2), 3, oid(3), oid(4), LiveImport.MigrationToolType.MONGOMIRROR, null);
    assertNotNull(mongomirrorLiveImport);
    assertTrue(mongomirrorLiveImport.isInProgress());
    assertEquals(LiveImport.State.NEW, mongomirrorLiveImport.getState());
    assertNull(mongomirrorLiveImport.getExpireDate());
    assertNull(mongomirrorLiveImport.getStopDate());
    assertFalse(mongomirrorLiveImport.getMigrationToolVersion().isPresent());
    assertTrue(mongomirrorLiveImport.getShardIndexesRestartCounts().isEmpty());
    assertTrue(mongomirrorLiveImport.getShardIndexesNeedingRestart().isEmpty());
    assertTrue(mongomirrorLiveImport.getShardIndexesNeedingResync().isEmpty());
    assertTrue(mongomirrorLiveImport.getReplicationLagPerShard().isEmpty());
    assertEquals("clusterName", mongomirrorLiveImport.getDestination().getClusterName());
    assertEquals(oid(1), mongomirrorLiveImport.getGroupId());
    assertEquals(oid(2), mongomirrorLiveImport.getDestination().getClusterUniqueId());
    assertEquals(oid(3), mongomirrorLiveImport.getLiveImportLocation());
    assertTrue(mongomirrorLiveImport.getRequesterUserId().isPresent());
    assertEquals(oid(4), mongomirrorLiveImport.getRequesterUserId().get());
    assertEquals("hostname", mongomirrorLiveImport.getSource().getHostname());
    assertEquals("username", mongomirrorLiveImport.getSource().getUsername());
    assertEquals("password", mongomirrorLiveImport.getSource().getPassword());
    assertEquals("displayLabel", mongomirrorLiveImport.getSource().getDisplayLabel());
    assertEquals("caFile", mongomirrorLiveImport.getSource().getCaFile());
    assertEquals(Collections.emptyList(), mongomirrorLiveImport.getSource().getReplicaSetsUris());
    assertTrue(mongomirrorLiveImport.getSource().sslEnabled());
    assertTrue(mongomirrorLiveImport.isDropEnabled());
    assertFalse(mongomirrorLiveImport.isAcknowledged());
    assertFalse(mongomirrorLiveImport.isCancelled());
    assertFalse(mongomirrorLiveImport.isCutoverComplete());
    assertFalse(mongomirrorLiveImport.isExpired());
    assertEquals(Collections.emptyList(), mongomirrorLiveImport.getPorts());
    assertTrue(mongomirrorLiveImport.getOplogBatchSize().isPresent());
    assertEquals(Double.valueOf(10), mongomirrorLiveImport.getOplogBatchSize().get());
    assertTrue(mongomirrorLiveImport.getCollStatsThreshold().isPresent());
    assertEquals(Double.valueOf(20), mongomirrorLiveImport.getCollStatsThreshold().get());
    // mongomirror-specific fields
    assertEquals(4, mongomirrorLiveImport.getMigrationStatuses().size());
    assertTrue(
        mongomirrorLiveImport.getMigrationStatuses().stream()
            .allMatch(status -> status.getStage().equals(MongoMirrorStage.INITIALIZING)));

    final PullLiveImport mongosyncLiveImport =
        v.toShardedClusterLiveImport(
            oid(1),
            oid(2),
            3,
            oid(3),
            oid(4),
            LiveImport.MigrationToolType.MONGOSYNC,
            UserWriteBlockingMode.NONE);
    // mongosync-specific fields
    assertInstanceOf(MongosyncPullLiveImport.class, mongosyncLiveImport);
    assertEquals(1, mongosyncLiveImport.getMigrationStatuses().size());
    assertEquals(
        MongosyncStage.INITIALIZING, mongosyncLiveImport.getMigrationStatuses().get(0).getStage());
    assertNull(((MongosyncPullLiveImport) mongosyncLiveImport).getMongosyncShardingParams());
    assertEquals(
        UserWriteBlockingMode.NONE,
        ((MongosyncPullLiveImport) mongosyncLiveImport).getUserWriteBlocking());
  }

  @Test
  public void testToShardedClusterLiveImport_mongosync() {
    final LiveImportJobView v =
        new LiveImportJobView(
            "clusterName",
            "hostname",
            "replicaset",
            "displayLabel",
            "username",
            "password",
            "caFile",
            true,
            true,
            10d,
            20d,
            MongosyncShardingParams.builder()
                .withCreateSupportingIndexes(true)
                .withShardingEntries(
                    List.of(
                        MongosyncShardEntry.builder()
                            .withDatabase("db")
                            .withCollection("test")
                            .withMongosyncShardCollection(null)
                            .build()))
                .build(),
            "6.0",
            MigrationType.LEGACY_MIGRATION);
    final PullLiveImport mongosyncLiveImport =
        v.toShardedClusterLiveImport(
            oid(1),
            oid(2),
            3,
            oid(3),
            oid(4),
            LiveImport.MigrationToolType.MONGOSYNC,
            UserWriteBlockingMode.DESTINATION_ONLY);
    assertInstanceOf(MongosyncPullLiveImport.class, mongosyncLiveImport);
    assertTrue(mongosyncLiveImport.isInProgress());
    assertEquals(LiveImport.State.NEW, mongosyncLiveImport.getState());
    assertNull(mongosyncLiveImport.getExpireDate());
    assertNull(mongosyncLiveImport.getStopDate());
    assertFalse(mongosyncLiveImport.getMigrationToolVersion().isPresent());
    assertTrue(mongosyncLiveImport.getShardIndexesRestartCounts().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingRestart().isEmpty());
    assertTrue(mongosyncLiveImport.getShardIndexesNeedingResync().isEmpty());
    assertTrue(
        ((MongosyncPullLiveImport) mongosyncLiveImport)
            .getShardIndexesNeedingMongosyncRestart()
            .isEmpty());
    assertTrue(mongosyncLiveImport.getReplicationLagPerShard().isEmpty());
    assertEquals("clusterName", mongosyncLiveImport.getDestination().getClusterName());
    assertEquals(oid(1), mongosyncLiveImport.getGroupId());
    assertEquals(oid(2), mongosyncLiveImport.getDestination().getClusterUniqueId());
    assertEquals(oid(3), mongosyncLiveImport.getLiveImportLocation());
    assertTrue(mongosyncLiveImport.getRequesterUserId().isPresent());
    assertEquals(oid(4), mongosyncLiveImport.getRequesterUserId().get());
    assertEquals("hostname", mongosyncLiveImport.getSource().getHostname());
    assertEquals("username", mongosyncLiveImport.getSource().getUsername());
    assertEquals("password", mongosyncLiveImport.getSource().getPassword());
    assertEquals("displayLabel", mongosyncLiveImport.getSource().getDisplayLabel());
    assertEquals("caFile", mongosyncLiveImport.getSource().getCaFile());
    assertEquals(Collections.emptyList(), mongosyncLiveImport.getSource().getReplicaSetsUris());
    assertTrue(mongosyncLiveImport.getSource().sslEnabled());
    assertTrue(mongosyncLiveImport.isDropEnabled());
    assertFalse(mongosyncLiveImport.isAcknowledged());
    assertFalse(mongosyncLiveImport.isCancelled());
    assertFalse(mongosyncLiveImport.isCutoverComplete());
    assertFalse(mongosyncLiveImport.isExpired());
    assertEquals(Collections.emptyList(), mongosyncLiveImport.getPorts());
    assertTrue(mongosyncLiveImport.getOplogBatchSize().isPresent());
    assertEquals(Double.valueOf(10), mongosyncLiveImport.getOplogBatchSize().get());
    assertTrue(mongosyncLiveImport.getCollStatsThreshold().isPresent());
    assertEquals(Double.valueOf(20), mongosyncLiveImport.getCollStatsThreshold().get());
    assertEquals(1, mongosyncLiveImport.getMigrationStatuses().size());
    assertEquals(
        MongosyncStage.INITIALIZING, mongosyncLiveImport.getMigrationStatuses().get(0).getStage());
    assertNotNull(((MongosyncPullLiveImport) mongosyncLiveImport).getMongosyncShardingParams());
    assertEquals(
        UserWriteBlockingMode.DESTINATION_ONLY,
        ((MongosyncPullLiveImport) mongosyncLiveImport).getUserWriteBlocking());
  }

  @Test
  public void testToShardedClusterLiveImport_mongosync_withShardParams() {
    final MongosyncShardEntry mongosyncShardEntry1 =
        MongosyncShardEntry.builder()
            .withDatabase("test")
            .withCollection("shardedColl")
            .withMongosyncShardCollection(
                MongosyncShardCollection.builder()
                    .withKey(
                        List.of(
                                new Object() {
                                  public final int type1 = 1;
                                  public final String type2 = "2";
                                })
                            .stream()
                            .map(
                                keyObject ->
                                    new ObjectMapper()
                                        .convertValue(
                                            keyObject, new TypeReference<Map<String, Object>>() {}))
                            .collect(Collectors.toList()))
                    .build())
            .build();

    final MongosyncShardEntry mongosyncShardEntry2 =
        MongosyncShardEntry.builder()
            .withDatabase("db")
            .withCollection("nds.clusterDescriptions")
            .build();

    final MongosyncShardingParams mongosyncShardingParams =
        MongosyncShardingParams.builder()
            .withCreateSupportingIndexes(true)
            .withShardingEntries(List.of(mongosyncShardEntry1, mongosyncShardEntry2))
            .build();

    final LiveImportJobView v =
        new LiveImportJobView(
            "clusterName",
            "hostname",
            "hostType",
            "displayLabel",
            "username",
            "password",
            "caFile",
            true,
            true,
            10d,
            20d,
            mongosyncShardingParams,
            "6.0",
            MigrationType.LEGACY_MIGRATION);
    final MongosyncPullLiveImport mongosyncLiveImport =
        (MongosyncPullLiveImport)
            v.toShardedClusterLiveImport(
                oid(1), oid(2), 3, oid(3), oid(4), LiveImport.MigrationToolType.MONGOSYNC, null);

    assertThat(mongosyncShardingParams)
        .usingRecursiveComparison()
        .isEqualTo(mongosyncLiveImport.getMongosyncShardingParams());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_validCredentials() {
    final String connectionString = "***********************************************";
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(connectionString);

    assertEquals("username", credentials.getLeft());
    assertEquals("password", credentials.getRight());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_passwordWithSpecialCharacters() {
    final String connectionString = "mongodb://user:p@ssw0rd!@#$%@host:27017/database";
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(connectionString);

    assertEquals("user", credentials.getLeft());
    assertEquals("p@ssw0rd!@#$%", credentials.getRight());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_passwordWithColon() {
    final String connectionString = "mongodb://admin:pass:word:123@host:27017/database";
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(connectionString);

    assertEquals("admin", credentials.getLeft());
    assertEquals("pass:word:123", credentials.getRight());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_urlEncodedCredentials() {
    final String connectionString = "******************************************************";
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(connectionString);

    assertEquals("user%40domain", credentials.getLeft());
    assertEquals("p%40ssw0rd", credentials.getRight());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_noCredentials() {
    final String connectionString = "mongodb://host:27017/database";
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(connectionString);

    assertNull(credentials.getLeft());
    assertNull(credentials.getRight());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_emptyUsername() {
    final String connectionString = "mongodb://:password@host:27017/database";
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(connectionString);

    assertEquals("", credentials.getLeft());
    assertEquals("password", credentials.getRight());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_emptyPassword() {
    final String connectionString = "mongodb://username:@host:27017/database";
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(connectionString);

    assertEquals("username", credentials.getLeft());
    assertEquals("", credentials.getRight());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_multipleHosts() {
    final String connectionString = "****************************************************";
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(connectionString);

    assertEquals("user", credentials.getLeft());
    assertEquals("pass", credentials.getRight());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_withQueryParams() {
    final String connectionString =
        "**************************************************************";
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(connectionString);

    assertEquals("admin", credentials.getLeft());
    assertEquals("secret", credentials.getRight());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_invalidFormat() {
    final String connectionString = "invalid-connection-string";
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(connectionString);

    assertNull(credentials.getLeft());
    assertNull(credentials.getRight());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_nullInput() {
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(null);

    assertNull(credentials.getLeft());
    assertNull(credentials.getRight());
  }

  @Test
  public void testExtractCredentialsFromConnectionString_emptyString() {
    final String connectionString = "";
    final Pair<String, String> credentials =
        LiveImportJobView.extractCredentialsFromConnectionString(connectionString);

    assertNull(credentials.getLeft());
    assertNull(credentials.getRight());
  }
}
