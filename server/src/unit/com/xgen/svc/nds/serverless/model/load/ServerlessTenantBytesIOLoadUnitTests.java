package com.xgen.svc.nds.serverless.model.load;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.google.common.testing.EqualsTester;
import com.xgen.cloud.common.util._public.util.PojoTestUtils;
import com.xgen.cloud.nds.serverless._public.model.load.ServerlessLoadHistoricalMetric;
import com.xgen.cloud.nds.serverless._public.model.load.ServerlessLoadMetricType;
import com.xgen.cloud.nds.serverless._public.model.load.ServerlessTenantBytesIOLoad;
import com.xgen.svc.nds.serverless.model.ServerlessTestFactory;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import org.bson.BsonDocument;
import org.junit.jupiter.api.Test;

public class ServerlessTenantBytesIOLoadUnitTests {
  @Test
  public void testConstructors() {
    // test nulls
    {
      final ServerlessTenantBytesIOLoad tenantBytesIOLoad =
          new ServerlessTenantBytesIOLoad(0, 0, null, null);
      assertFalse(tenantBytesIOLoad.hasUtilization());
      assertEquals(0, tenantBytesIOLoad.getCurrentRead());
      assertEquals(0, tenantBytesIOLoad.getCurrentWrite());
      assertNull(tenantBytesIOLoad.getHistoricalReadAverages());
      assertNull(tenantBytesIOLoad.getHistoricalWriteAverages());
    }

    // test values
    {
      final ServerlessTenantBytesIOLoad tenantBytesIOLoad =
          new ServerlessTenantBytesIOLoad(
              1,
              3,
              List.of(
                  new ServerlessLoadHistoricalMetric(
                      1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 1),
                  new ServerlessLoadHistoricalMetric(
                      6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 2),
                  new ServerlessLoadHistoricalMetric(
                      24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 3)),
              List.of(
                  new ServerlessLoadHistoricalMetric(
                      1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 4),
                  new ServerlessLoadHistoricalMetric(
                      6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 5),
                  new ServerlessLoadHistoricalMetric(
                      24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 6)));
      assertFalse(tenantBytesIOLoad.hasUtilization());
      assertEquals(1, tenantBytesIOLoad.getCurrentRead());
      assertEquals(3, tenantBytesIOLoad.getCurrentWrite());
      assertEquals(
          List.of(
              new ServerlessLoadHistoricalMetric(
                  1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 1),
              new ServerlessLoadHistoricalMetric(
                  6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 2),
              new ServerlessLoadHistoricalMetric(
                  24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 3)),
          tenantBytesIOLoad.getHistoricalReadAverages());
      assertEquals(
          List.of(
              new ServerlessLoadHistoricalMetric(
                  1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 4),
              new ServerlessLoadHistoricalMetric(
                  6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 5),
              new ServerlessLoadHistoricalMetric(
                  24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 6)),
          tenantBytesIOLoad.getHistoricalWriteAverages());
    }
  }

  @Test
  public void testEquals() {
    final ServerlessTenantBytesIOLoad tenantBytesIOLoad =
        ServerlessTestFactory.getServerlessTenantBytesIOLoad();

    // test identity
    new EqualsTester().addEqualityGroup(tenantBytesIOLoad, tenantBytesIOLoad).testEquals();

    // test equality
    assertEquals(tenantBytesIOLoad, ServerlessTestFactory.getServerlessTenantBytesIOLoad());

    // test null
    assertNotEquals(null, tenantBytesIOLoad);

    // test inequality
    assertNotEquals(tenantBytesIOLoad, tenantBytesIOLoad.toBuilder().currentRead(2).build());
    assertNotEquals(tenantBytesIOLoad, tenantBytesIOLoad.toBuilder().currentWrite(6).build());
    assertNotEquals(
        tenantBytesIOLoad,
        tenantBytesIOLoad.toBuilder()
            .historicalReadAverages(
                List.of(
                    new ServerlessLoadHistoricalMetric(
                        1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 4),
                    new ServerlessLoadHistoricalMetric(
                        6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 5),
                    new ServerlessLoadHistoricalMetric(
                        24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 6)))
            .build());
    assertNotEquals(
        tenantBytesIOLoad,
        tenantBytesIOLoad.toBuilder()
            .historicalWriteAverages(
                List.of(
                    new ServerlessLoadHistoricalMetric(
                        1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 1),
                    new ServerlessLoadHistoricalMetric(
                        6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 2),
                    new ServerlessLoadHistoricalMetric(
                        24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 3)))
            .build());
  }

  @Test
  public void testHashCode() {
    final ServerlessTenantBytesIOLoad tenantBytesIOLoad =
        ServerlessTestFactory.getServerlessTenantBytesIOLoad();

    // test identity
    assertEquals(tenantBytesIOLoad.hashCode(), tenantBytesIOLoad.hashCode());

    // test equality
    assertEquals(
        tenantBytesIOLoad.hashCode(),
        ServerlessTestFactory.getServerlessTenantBytesIOLoad().hashCode());

    // test inequality
    assertNotEquals(
        tenantBytesIOLoad.hashCode(),
        tenantBytesIOLoad.toBuilder().currentRead(2).build().hashCode());
    assertNotEquals(
        tenantBytesIOLoad.hashCode(),
        tenantBytesIOLoad.toBuilder().currentWrite(6).build().hashCode());
    assertNotEquals(
        tenantBytesIOLoad.hashCode(),
        tenantBytesIOLoad.toBuilder()
            .historicalReadAverages(
                List.of(
                    new ServerlessLoadHistoricalMetric(
                        1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 4),
                    new ServerlessLoadHistoricalMetric(
                        6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 5),
                    new ServerlessLoadHistoricalMetric(
                        24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 6)))
            .build()
            .hashCode());
    assertNotEquals(
        tenantBytesIOLoad.hashCode(),
        tenantBytesIOLoad.toBuilder()
            .historicalWriteAverages(
                List.of(
                    new ServerlessLoadHistoricalMetric(
                        1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 1),
                    new ServerlessLoadHistoricalMetric(
                        6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 2),
                    new ServerlessLoadHistoricalMetric(
                        24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 3)))
            .build()
            .hashCode());
  }

  @Test
  public void testToString() {
    final ServerlessTenantBytesIOLoad tenantBytesIOLoad =
        ServerlessTestFactory.getServerlessTenantBytesIOLoad();
    assertEquals(
        "ServerlessTenantBytesIOLoad(_currentRead=1, _currentWrite=3,"
            + " _historicalReadAverages=[ServerlessLoadHistoricalMetric{_duration=1,"
            + " _unit=HOURS, _type=AVG, _value=1.0}, ServerlessLoadHistoricalMetric{_duration=6,"
            + " _unit=HOURS, _type=AVG, _value=2.0}, ServerlessLoadHistoricalMetric{_duration=24,"
            + " _unit=HOURS, _type=AVG, _value=3.0}],"
            + " _historicalWriteAverages=[ServerlessLoadHistoricalMetric{_duration=1,"
            + " _unit=HOURS, _type=AVG, _value=4.0}, ServerlessLoadHistoricalMetric{_duration=6,"
            + " _unit=HOURS, _type=AVG, _value=5.0}, ServerlessLoadHistoricalMetric{_duration=24,"
            + " _unit=HOURS, _type=AVG, _value=6.0}])",
        tenantBytesIOLoad.toString());
  }

  @Test
  public void testToLoadSummaryString() {
    final ServerlessTenantBytesIOLoad tenantBytesIOLoad =
        ServerlessTestFactory.getServerlessTenantBytesIOLoad();
    assertEquals(
        "ServerlessTenantBytesIOLoad(_currentRead=1, _currentWrite=3)",
        tenantBytesIOLoad.toLoadSummaryString());
  }

  @Test
  public void testBuilder() {
    final ServerlessTenantBytesIOLoad tenantBytesIOLoad =
        ServerlessTestFactory.getServerlessTenantBytesIOLoad();

    // test empty
    assertEquals(
        new ServerlessTenantBytesIOLoad(0, 0, null, null),
        new ServerlessTenantBytesIOLoad.Builder().build());

    // test from instance
    assertEquals(
        tenantBytesIOLoad, new ServerlessTenantBytesIOLoad.Builder(tenantBytesIOLoad).build());

    // test toBuilder, change current read
    assertEquals(
        new ServerlessTenantBytesIOLoad(
            2,
            3,
            List.of(
                new ServerlessLoadHistoricalMetric(
                    1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 1),
                new ServerlessLoadHistoricalMetric(
                    6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 2),
                new ServerlessLoadHistoricalMetric(
                    24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 3)),
            List.of(
                new ServerlessLoadHistoricalMetric(
                    1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 4),
                new ServerlessLoadHistoricalMetric(
                    6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 5),
                new ServerlessLoadHistoricalMetric(
                    24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 6))),
        tenantBytesIOLoad.toBuilder().currentRead(2).build());

    // test toBuilder, change current write
    assertEquals(
        new ServerlessTenantBytesIOLoad(
            1,
            6,
            List.of(
                new ServerlessLoadHistoricalMetric(
                    1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 1),
                new ServerlessLoadHistoricalMetric(
                    6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 2),
                new ServerlessLoadHistoricalMetric(
                    24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 3)),
            List.of(
                new ServerlessLoadHistoricalMetric(
                    1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 4),
                new ServerlessLoadHistoricalMetric(
                    6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 5),
                new ServerlessLoadHistoricalMetric(
                    24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 6))),
        tenantBytesIOLoad.toBuilder().currentWrite(6).build());

    // test toBuilder, change historical read averages
    final List<ServerlessLoadHistoricalMetric> historicalReadAverages =
        List.of(
            new ServerlessLoadHistoricalMetric(1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 4),
            new ServerlessLoadHistoricalMetric(6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 5),
            new ServerlessLoadHistoricalMetric(
                24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 6));
    assertEquals(
        new ServerlessTenantBytesIOLoad(
            1,
            3,
            historicalReadAverages,
            List.of(
                new ServerlessLoadHistoricalMetric(
                    1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 4),
                new ServerlessLoadHistoricalMetric(
                    6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 5),
                new ServerlessLoadHistoricalMetric(
                    24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 6))),
        tenantBytesIOLoad.toBuilder().historicalReadAverages(historicalReadAverages).build());

    // test toBuilder, change historical write averages
    final List<ServerlessLoadHistoricalMetric> historicalWriteAverages =
        List.of(
            new ServerlessLoadHistoricalMetric(1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 1),
            new ServerlessLoadHistoricalMetric(6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 2),
            new ServerlessLoadHistoricalMetric(
                24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 3));
    assertEquals(
        new ServerlessTenantBytesIOLoad(
            1,
            3,
            List.of(
                new ServerlessLoadHistoricalMetric(
                    1, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 1),
                new ServerlessLoadHistoricalMetric(
                    6, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 2),
                new ServerlessLoadHistoricalMetric(
                    24, TimeUnit.HOURS, ServerlessLoadMetricType.AVG, 3)),
            historicalWriteAverages),
        tenantBytesIOLoad.toBuilder().historicalWriteAverages(historicalWriteAverages).build());

    // test builder
    assertEquals(
        new ServerlessTenantBytesIOLoad(0, 0, null, null),
        ServerlessTenantBytesIOLoad.builder().build());
  }

  @Test
  public void testSerialization() {
    final PojoTestUtils<ServerlessTenantBytesIOLoad> pojoSerializer =
        PojoTestUtils.of(ServerlessTenantBytesIOLoad.class);
    final ServerlessTenantBytesIOLoad originalTenantBytesIOLoad =
        ServerlessTestFactory.getServerlessTenantBytesIOLoad();
    final BsonDocument document = pojoSerializer.encode(originalTenantBytesIOLoad);

    assertEquals(
        Set.of(
            ServerlessTenantBytesIOLoad.FieldDefs.CURRENT_READ,
            ServerlessTenantBytesIOLoad.FieldDefs.CURRENT_WRITE,
            ServerlessTenantBytesIOLoad.FieldDefs.HISTORICAL_READ_AVERAGES,
            ServerlessTenantBytesIOLoad.FieldDefs.HISTORICAL_WRITE_AVERAGES),
        document.keySet());
    assertEquals(
        1, document.getInt64(ServerlessTenantBytesIOLoad.FieldDefs.CURRENT_READ).getValue());
    assertEquals(
        3, document.getInt64(ServerlessTenantBytesIOLoad.FieldDefs.CURRENT_WRITE).getValue());
    assertEquals(
        3,
        document.getArray(ServerlessTenantBytesIOLoad.FieldDefs.HISTORICAL_READ_AVERAGES).size());
    assertEquals(
        3,
        document.getArray(ServerlessTenantBytesIOLoad.FieldDefs.HISTORICAL_WRITE_AVERAGES).size());

    final ServerlessTenantBytesIOLoad deserializedTenantBytesIOLoad =
        pojoSerializer.decode(document);

    assertEquals(originalTenantBytesIOLoad, deserializedTenantBytesIOLoad);
  }
}
