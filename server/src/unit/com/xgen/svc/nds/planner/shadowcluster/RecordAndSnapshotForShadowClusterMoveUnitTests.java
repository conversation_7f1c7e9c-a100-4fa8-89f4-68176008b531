package com.xgen.svc.nds.planner.shadowcluster;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mongodb.WriteResult;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.restore._private.dao.ShadowClusterJobDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Attempt;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.SnapshotAttemptedInstance;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure.Status;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Permutation;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Permutation.PermutationType;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowClusterExposureJob;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Treatment;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterJobSvc;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.svc.nds.svc.planning.NDSPlanExecutorCallbackSvc;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ExtendWith(CloudProviderExtension.class)
public class RecordAndSnapshotForShadowClusterMoveUnitTests {

  private static final Logger LOG =
      LoggerFactory.getLogger(RecordAndSnapshotForShadowClusterMoveUnitTests.class);

  private static final ObjectId GROUP_ID = new ObjectId();
  private static final ObjectId SHADOW_CLUSTER_EXPOSURE_JOB_ID = new ObjectId();
  private static final ObjectId EXPOSURE_ID = new ObjectId();
  private static final ObjectId SNAPSHOT_ID = new ObjectId();
  private static final ObjectId SOURCE_PROJECT_ID = new ObjectId();
  private static final String SOURCE_CLUSTER_NAME = "test-cluster";
  private static final ObjectId SOURCE_ORG_ID = new ObjectId();
  private static final ObjectId SOURCE_CLUSTER_ID = new ObjectId();
  private static final ObjectId PERMUTATION_ID_1 = new ObjectId();
  private static final ObjectId PERMUTATION_ID_2 = new ObjectId();
  private static final ObjectId JOB_ID_1 = new ObjectId();
  private static final ObjectId JOB_ID_2 = new ObjectId();

  private PlanContext planContext;
  private ShadowClusterJobSvc shadowClusterJobSvc;
  private ShadowClusterSvc shadowClusterSvc;
  private ShadowClusterExposureJob shadowClusterExposureJob;
  private CpsSvc cpsSvc;
  private ShadowClusterJobDao shadowClusterJobDao;

  @BeforeEach
  public void setUp() throws SvcException {
    planContext = mock(PlanContext.class);
    shadowClusterJobSvc = mock(ShadowClusterJobSvc.class);
    shadowClusterSvc = mock(ShadowClusterSvc.class);
    shadowClusterExposureJob = mock(ShadowClusterExposureJob.class);
    cpsSvc = mock(CpsSvc.class);
    shadowClusterJobDao = mock(ShadowClusterJobDao.class);
    AppSettings appSettings = mock(AppSettings.class);
    final PlanDao planDao = mock(PlanDao.class);

    doReturn(planDao).when(planContext).getPlanDao();
    doReturn(Optional.empty()).when(planDao).findMoveValue(any(), any(), any());
    when(planContext.getLogger()).thenReturn(LOG);
    when(planContext.getScopedMoveObservable()).thenReturn(mock(Move.Observable.class));
    when(planContext.getAppSettings()).thenReturn(appSettings);
    when(planContext.getGroupId()).thenReturn(GROUP_ID);
    when(planContext.getPlanExecutorCallback()).thenReturn(mock(NDSPlanExecutorCallbackSvc.class));

    when(shadowClusterJobSvc.getShadowClusterExposureJob(
            eq(SHADOW_CLUSTER_EXPOSURE_JOB_ID), any(Logger.class)))
        .thenReturn(shadowClusterExposureJob);
    when(shadowClusterExposureJob.getId()).thenReturn(SHADOW_CLUSTER_EXPOSURE_JOB_ID);
    when(shadowClusterExposureJob.getExposureId()).thenReturn(EXPOSURE_ID);
    when(shadowClusterExposureJob.getJobStatus()).thenReturn(Exposure.Status.PROVISIONED);
    when(shadowClusterExposureJob.getSourceProjectId()).thenReturn(SOURCE_PROJECT_ID);
    when(shadowClusterExposureJob.getSourceClusterName()).thenReturn(SOURCE_CLUSTER_NAME);
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(null);
    when(shadowClusterExposureJob.targetDeletionDateReached()).thenReturn(false);
    final Exposure mockExposure = mock(Exposure.class);
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(mockExposure));
  }

  @Test
  public void testPerformInternal_JobNotFound() throws SvcException {
    when(shadowClusterJobSvc.getShadowClusterExposureJob(
            eq(SHADOW_CLUSTER_EXPOSURE_JOB_ID), any(Logger.class)))
        .thenThrow(new SvcException(NDSErrorCode.RESOURCE_NOT_FOUND));
    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isFailed());
    assertEquals(
        "Failed getting shadow cluster exposure job for jobId: " + SHADOW_CLUSTER_EXPOSURE_JOB_ID,
        result.getMessage());
  }

  @Test
  public void testPerformInternal_JobFailedStatus() {
    when(shadowClusterExposureJob.getJobStatus()).thenReturn(Exposure.Status.FAILED);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isFailed());
  }

  @Test
  public void testPerformInternal_SnapshotAlreadyExistsInDao() {
    when(shadowClusterExposureJob.getSourceSnapshotId())
        .thenReturn(null)
        .thenReturn(SNAPSHOT_ID)
        .thenReturn(SNAPSHOT_ID);

    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();

    assertTrue(result.getStatus().isDone());
    // setSourceSnapshotId should NOT be called when snapshot already exists
    verify(shadowClusterExposureJob, never()).setSourceSnapshotId(any());
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob,
            Exposure.Status.SNAPSHOT_READY,
            "Snapshot already created with id: " + SNAPSHOT_ID);
  }

  @Test
  public void testPerformInternal_SnapshotAlreadyExistsOnJob() throws SvcException {
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);
    when(shadowClusterExposureJob.getExposureId()).thenReturn(EXPOSURE_ID);
    final Exposure exposureMock = mock(Exposure.class);
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(exposureMock));
    final RecordAndSnapshotForShadowClusterMove move = getMove();

    final Result<?> result = move.performInternal();

    assertTrue(result.getStatus().isDone());
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob,
            Exposure.Status.SNAPSHOT_READY,
            "Snapshot already created with id: " + SNAPSHOT_ID);
    verify(shadowClusterJobSvc).createShadowClusterJobsForExposure(exposureMock, SNAPSHOT_ID);
  }

  @Test
  public void testPerformInternal_SnapshotCreationInProgress() throws SvcException {
    final RecordAndSnapshotForShadowClusterMove move = spy(getMove());
    doReturn(Result.inProgress()).when(move).waitForSnapshotStep(shadowClusterExposureJob);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isInProgress());
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob, Exposure.Status.SNAPSHOTTING, "Creating snapshot");
  }

  @Test
  public void testPerformInternal_SnapshotCreationFailed() throws SvcException {
    final RecordAndSnapshotForShadowClusterMove move = spy(getMove());
    doReturn(Result.failed("Snapshot failed"))
        .when(move)
        .waitForSnapshotStep(shadowClusterExposureJob);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isFailed());
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob, Exposure.Status.SNAPSHOTTING, "Creating snapshot");
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob,
            Exposure.Status.FAILED,
            "Failed to create snapshot: Snapshot failed");
  }

  @Test
  public void testPerformInternal_SnapshotCreationSuccess() throws SvcException {
    /*
     * COMPLEX MOCKING EXPLANATION:
     *
     * This test uses an intricate mocking pattern to simulate the side-effect based communication
     * between performInternal() and waitForSnapshotStep().
     *
     * INTERACTION FLOW BEING TESTED:
     * 1. performInternal() calls waitForSnapshotStep(shadowClusterJob)
     * 2. waitForSnapshotStep() internally calls queueSnapshot() when snapshot ID is null
     * 3. queueSnapshot() calls shadowClusterJob.setSnapshotId(newSnapshotId) - SIDE EFFECT
     * 4. performInternal() later calls shadowClusterJob.getSourceSnapshotId() to read the
     *    snapshot ID that was set by the previous step
     * 5. performInternal() uses this ID for status updates and completion logic
     *
     * WHY SIMPLE MOCKING WON'T WORK:
     * - The production code relies on stateful interaction where one method modifies shared
     *   state that another method reads
     * - We cannot use simple when().thenReturn() because the return value of getSourceSnapshotId()
     *   must change during test execution (null initially, then SNAPSHOT_ID after queueSnapshot)
     * - The ShadowClusterJob mock must behave like a real object with mutable state
     *
     * JUSTIFICATION FOR COMPLEXITY:
     * - Dedicated tests already cover waitForSnapshotStep() logic thoroughly
     * - This test focuses on the INTEGRATION between performInternal() and waitForSnapshotStep()
     * - The complex setup accurately simulates real production behavior
     *
     */

    // ObjectId holder simulates the mutable _sourceSnapshotId field in SystemClusterJob
    // This array acts as shared state between setSnapshotId() and getSourceSnapshotId() calls
    final ObjectId[] snapshotIdHolder = {null};

    // Mock getSourceSnapshotId() to return current value from our state holder
    // Uses thenAnswer() instead of thenReturn() because the return value changes during execution
    when(shadowClusterExposureJob.getSourceSnapshotId())
        .thenAnswer(invocation -> snapshotIdHolder[0]);

    // Mock setSnapshotId() to update our state holder, simulating real ShadowClusterJob behavior
    // Uses doAnswer() to capture the side effect of modifying internal state
    doAnswer(
            invocation -> {
              snapshotIdHolder[0] = invocation.getArgument(0);
              return null;
            })
        .when(shadowClusterExposureJob)
        .setSourceSnapshotId(SNAPSHOT_ID);

    // Mock exposure for job scheduling
    when(shadowClusterExposureJob.getExposureId()).thenReturn(EXPOSURE_ID);
    final Exposure exposure = createMockExposure();
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(exposure));
    when(shadowClusterJobSvc.createShadowClusterJobsForExposure(eq(exposure), any(ObjectId.class)))
        .thenReturn(List.of(JOB_ID_1, JOB_ID_2));

    final RecordAndSnapshotForShadowClusterMove move = spy(getMove());

    // Mock waitForSnapshotStep to simulate successful snapshot creation workflow
    // This simulates the complete queueSnapshot() behavior: setting snapshot ID and returning
    // success
    doAnswer(
            invocation -> {
              ShadowClusterExposureJob job = invocation.getArgument(0);
              // Simulate queueSnapshot() side effect: setting the snapshot ID on the job
              job.setSourceSnapshotId(SNAPSHOT_ID);
              // Return success to simulate completed snapshot creation
              return Result.done();
            })
        .when(move)
        .waitForSnapshotStep(shadowClusterExposureJob);

    final Result<?> result = move.performInternal();

    assertTrue(result.getStatus().isDone());

    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob, Exposure.Status.SNAPSHOTTING, "Creating snapshot");
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob,
            Exposure.Status.SNAPSHOT_READY,
            "Snapshot created successfully with id: " + SNAPSHOT_ID);
  }

  @Test
  public void testRollbackInternal_JobNotFound() throws SvcException {
    when(shadowClusterJobSvc.getShadowClusterExposureJob(
            eq(SHADOW_CLUSTER_EXPOSURE_JOB_ID), any(Logger.class)))
        .thenThrow(new SvcException(NDSErrorCode.RESOURCE_NOT_FOUND));
    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.rollbackInternal();
    assertTrue(result.getStatus().isFailed());
    assertEquals(
        "Failed getting shadow cluster exposure job during rollback for jobId: "
            + SHADOW_CLUSTER_EXPOSURE_JOB_ID,
        result.getMessage());
  }

  @Test
  public void testRollbackInternal_Success() {
    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.rollbackInternal();
    assertTrue(result.getStatus().isDone());
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob, Exposure.Status.FAILED, "Snapshot creation failed");
  }

  @Test
  public void testWaitForSnapshotStep_SnapshotIdIsNull_QueuesSnapshot() throws SvcException {
    /*
     * COMPLEX MOCKING PATTERN:
     * This test uses the same intricate mocking pattern as testPerformInternal_SnapshotCreationSuccess().
     * See the detailed explanation in that test method for why this complex setup is necessary.
     *
     * This pattern simulates the stateful behavior where queueSnapshot() calls setSnapshotId()
     * and subsequent calls to getSourceSnapshotId() must return the updated value.
     */

    // ObjectId holder simulates mutable state - see testPerformInternal_SnapshotCreationSuccess for
    // details
    final ObjectId[] snapshotIdHolder = {null};

    // Mock stateful getSourceSnapshotId() behavior - see
    // testPerformInternal_SnapshotCreationSuccess for explanation
    when(shadowClusterExposureJob.getSourceSnapshotId())
        .thenAnswer(invocation -> snapshotIdHolder[0]);

    // Mock setSnapshotId() side effect - see testPerformInternal_SnapshotCreationSuccess for
    // explanation
    doAnswer(
            invocation -> {
              snapshotIdHolder[0] = invocation.getArgument(0);
              return null;
            })
        .when(shadowClusterExposureJob)
        .setSourceSnapshotId(SNAPSHOT_ID);

    when(cpsSvc.createQueuedOnDemandSnapshot(
            eq(SOURCE_PROJECT_ID),
            eq(SOURCE_CLUSTER_NAME),
            eq(Duration.ofDays(ShadowClusterJobSvc.MAX_RETENTION_DAYS)),
            eq("Snapshot triggered due to shadow cluster exposure creation"),
            eq(AppUser.SYSTEM_USER),
            eq(null)))
        .thenReturn(SNAPSHOT_ID);

    final BackupSnapshot mockSnapshot = mock(BackupSnapshot.class);
    when(mockSnapshot.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.COMPLETED);
    when(cpsSvc.getBackupSnapshot(SOURCE_PROJECT_ID, SNAPSHOT_ID)).thenReturn(mockSnapshot);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.waitForSnapshotStep(shadowClusterExposureJob);

    // When snapshot ID is null, waitForSnapshotStep queues a snapshot and returns IN_PROGRESS
    assertTrue(result.getStatus().isInProgress());
    verify(cpsSvc, times(1))
        .createQueuedOnDemandSnapshot(
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            Duration.ofDays(ShadowClusterJobSvc.MAX_RETENTION_DAYS),
            "Snapshot triggered due to shadow cluster exposure creation",
            AppUser.SYSTEM_USER,
            null);
    verify(shadowClusterExposureJob, times(1)).setSourceSnapshotId(SNAPSHOT_ID);
    verify(shadowClusterJobSvc, times(1)).saveShadowClusterExposureJob(shadowClusterExposureJob);
    verify(shadowClusterJobSvc, times(1)).saveShadowClusterExposureJob(shadowClusterExposureJob);
  }

  @Test
  public void testWaitForSnapshotStep_SnapshotStatusFailed_ReturnsFailedResult()
      throws SvcException {
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);

    final BackupSnapshot mockSnapshot = mock(BackupSnapshot.class);
    when(mockSnapshot.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.FAILED);
    when(cpsSvc.getBackupSnapshot(SOURCE_PROJECT_ID, SNAPSHOT_ID)).thenReturn(mockSnapshot);

    final SnapshotAttemptedInstance instance = mock(SnapshotAttemptedInstance.class);
    final ObjectId instanceId = new ObjectId();
    when(instance.getFailedAt()).thenReturn(new Date());
    when(instance.getErrorMsg()).thenReturn("test error");
    when(instance.getInstanceId()).thenReturn(instanceId);
    final Attempt attempt = mock(Attempt.class);
    when(attempt.getInstances()).thenReturn(List.of(instance));
    final List<Attempt> attempts = List.of(attempt);
    when(mockSnapshot.getAttempts()).thenReturn(attempts);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.waitForSnapshotStep(shadowClusterExposureJob);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Snapshot failed: Instance: " + instanceId + " - test error", result.getMessage());
  }

  @Test
  public void testWaitForSnapshotStep_SnapshotStatusFailed_ReturnsNullResult() throws SvcException {
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);

    when(cpsSvc.getBackupSnapshot(SOURCE_PROJECT_ID, SNAPSHOT_ID)).thenReturn(null);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.waitForSnapshotStep(shadowClusterExposureJob);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Snapshot not found", result.getMessage());
  }

  @Test
  public void testWaitForSnapshotStep_SnapshotStatusInProgress_ReturnsInProgressResult()
      throws SvcException {
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);

    final BackupSnapshot mockSnapshot = mock(BackupSnapshot.class);
    when(mockSnapshot.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.IN_PROGRESS);
    when(cpsSvc.getBackupSnapshot(SOURCE_PROJECT_ID, SNAPSHOT_ID)).thenReturn(mockSnapshot);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.waitForSnapshotStep(shadowClusterExposureJob);

    assertTrue(result.getStatus().isInProgress());
  }

  @Test
  public void testWaitForSnapshotStep_SnapshotStatusQueued_ReturnsInProgressResult()
      throws SvcException {
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);

    final BackupSnapshot mockSnapshot = mock(BackupSnapshot.class);
    when(mockSnapshot.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.QUEUED);
    when(cpsSvc.getBackupSnapshot(SOURCE_PROJECT_ID, SNAPSHOT_ID)).thenReturn(mockSnapshot);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.waitForSnapshotStep(shadowClusterExposureJob);

    assertTrue(result.getStatus().isInProgress());
  }

  @Test
  public void testWaitForSnapshotStep_SnapshotStatusCompleted_ReturnsDoneResult()
      throws SvcException {
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);

    final BackupSnapshot mockSnapshot = mock(BackupSnapshot.class);
    when(mockSnapshot.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.COMPLETED);
    when(cpsSvc.getBackupSnapshot(SOURCE_PROJECT_ID, SNAPSHOT_ID)).thenReturn(mockSnapshot);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.waitForSnapshotStep(shadowClusterExposureJob);

    assertTrue(result.getStatus().isDone());
  }

  @Test
  public void testQueueSnapshot_CreatesSnapshotWithCorrectParameters() throws SvcException {
    when(cpsSvc.createQueuedOnDemandSnapshot(
            eq(SOURCE_PROJECT_ID),
            eq(SOURCE_CLUSTER_NAME),
            eq(Duration.ofDays(ShadowClusterJobSvc.MAX_RETENTION_DAYS)),
            eq("Snapshot triggered due to shadow cluster exposure creation"),
            eq(AppUser.SYSTEM_USER),
            eq(null)))
        .thenReturn(SNAPSHOT_ID);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    move.queueSnapshot(shadowClusterExposureJob);

    verify(cpsSvc, times(1))
        .createQueuedOnDemandSnapshot(
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            Duration.ofDays(ShadowClusterJobSvc.MAX_RETENTION_DAYS),
            "Snapshot triggered due to shadow cluster exposure creation",
            AppUser.SYSTEM_USER,
            null);
    verify(shadowClusterExposureJob, times(1)).setSourceSnapshotId(SNAPSHOT_ID);
    verify(shadowClusterJobSvc, times(1)).saveShadowClusterExposureJob(shadowClusterExposureJob);
    verify(shadowClusterJobSvc, times(1)).saveShadowClusterExposureJob(shadowClusterExposureJob);
  }

  @Test
  public void testQueueSnapshot_ThrowsSvcException_PropagatesException() throws SvcException {
    final SvcException testException = new SvcException(NDSErrorCode.BACKUP_JOB_NOT_FOUND);
    when(cpsSvc.createQueuedOnDemandSnapshot(
            eq(SOURCE_PROJECT_ID),
            eq(SOURCE_CLUSTER_NAME),
            eq(Duration.ofDays(ShadowClusterJobSvc.MAX_RETENTION_DAYS)),
            eq("Snapshot triggered due to shadow cluster exposure creation"),
            eq(AppUser.SYSTEM_USER),
            eq(null)))
        .thenThrow(testException);

    final RecordAndSnapshotForShadowClusterMove move = getMove();

    SvcException ex =
        assertThrows(SvcException.class, () -> move.queueSnapshot(shadowClusterExposureJob));
    assertEquals(NDSErrorCode.BACKUP_JOB_NOT_FOUND, ex.getErrorCode());
  }

  @Test
  public void testPerformInternal_CpsServiceThrowsExceptionDuringSnapshotCreation()
      throws SvcException {
    // Setup normal flow until snapshot creation, then make CPS service throw exception
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(null);
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(null);

    // Make cpsSvc throw exception when creating snapshot
    when(cpsSvc.createQueuedOnDemandSnapshot(
            eq(SOURCE_PROJECT_ID),
            eq(SOURCE_CLUSTER_NAME),
            eq(Duration.ofDays(ShadowClusterJobSvc.MAX_RETENTION_DAYS)),
            eq("Snapshot triggered due to shadow cluster exposure creation"),
            eq(AppUser.SYSTEM_USER),
            eq(null)))
        .thenThrow(new SvcException(NDSErrorCode.INTERNAL, "CPS service unavailable"));

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();

    assertTrue(result.getStatus().isFailed());
    assertEquals("Failed to create snapshot: Internal Error", result.getMessage());
  }

  @Test
  public void testPerformInternal_ExposureStatusRequested_ContinuesExecution() {
    when(shadowClusterExposureJob.getJobStatus()).thenReturn(Exposure.Status.REQUESTED);
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);
    final Exposure mockExposure = mock(Exposure.class);
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(mockExposure));

    // Mock successful snapshot completion
    final BackupSnapshot mockSnapshot = mock(BackupSnapshot.class);
    when(mockSnapshot.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.COMPLETED);
    when(cpsSvc.getBackupSnapshot(SOURCE_PROJECT_ID, SNAPSHOT_ID)).thenReturn(mockSnapshot);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();

    assertTrue(result.getStatus().isDone());
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob,
            Exposure.Status.SNAPSHOT_READY,
            "Snapshot already created with id: " + SNAPSHOT_ID);
  }

  @Test
  public void testPerformInternal_ExposureStatusNonFailed_DoesNotTerminateEarly() {
    // Test with PROVISIONED status (any non-FAILED status works)
    when(shadowClusterExposureJob.getJobStatus()).thenReturn(Exposure.Status.PROVISIONED);
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);
    // Mock getExposure instead of non-existent getExposureStatus method
    final Exposure mockExposure = mock(Exposure.class);
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(mockExposure));

    // Mock successful snapshot completion
    final BackupSnapshot mockSnapshot = mock(BackupSnapshot.class);
    when(mockSnapshot.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.COMPLETED);
    when(cpsSvc.getBackupSnapshot(SOURCE_PROJECT_ID, SNAPSHOT_ID)).thenReturn(mockSnapshot);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();

    // Should complete successfully, not terminate early due to non-FAILED status
    assertTrue(result.getStatus().isDone());
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob,
            Exposure.Status.SNAPSHOT_READY,
            "Snapshot already created with id: " + SNAPSHOT_ID);
  }

  @Test
  public void testPerformInternal_SnapshotIdNullButExistsInDao_UpdatesJobAndCompletes() {
    // Setup: sourceSnapshotId is null initially, but exists in DAO
    when(shadowClusterExposureJob.getSourceSnapshotId())
        .thenReturn(null) // First call returns null
        .thenReturn(SNAPSHOT_ID); // After setting, returns the snapshot ID
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();

    assertTrue(result.getStatus().isDone());

    // Verify the snapshot ID was NOT set again since it already exists
    verify(shadowClusterExposureJob, never()).setSourceSnapshotId(any());

    // Verify status was updated to SNAPSHOT_READY
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob,
            Exposure.Status.SNAPSHOT_READY,
            "Snapshot already created with id: " + SNAPSHOT_ID);
  }

  @Test
  public void testPerformInternal_SnapshotIdNullAndNotInDao_ProceedsToCreateSnapshot()
      throws SvcException {
    // Setup: sourceSnapshotId is null and doesn't exist in DAO
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(null);
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(null);

    // Mock snapshot creation and completion
    when(cpsSvc.createQueuedOnDemandSnapshot(
            eq(SOURCE_PROJECT_ID),
            eq(SOURCE_CLUSTER_NAME),
            eq(Duration.ofDays(ShadowClusterJobSvc.MAX_RETENTION_DAYS)),
            eq("Snapshot triggered due to shadow cluster exposure creation"),
            eq(AppUser.SYSTEM_USER),
            eq(null)))
        .thenReturn(SNAPSHOT_ID);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();

    // Should return IN_PROGRESS since snapshot was just queued
    assertTrue(result.getStatus().isInProgress());

    // Verify status was updated to SNAPSHOTTING
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob, Exposure.Status.SNAPSHOTTING, "Creating snapshot");
  }

  @Test
  public void testPerformInternal_ExposureDeleted_Fails() {
    when(shadowClusterExposureJob.targetDeletionDateReached()).thenReturn(true);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();

    // Should return IN_PROGRESS since snapshot was just queued
    assertTrue(result.getStatus().isFailed());

    // Verify status was updated to SNAPSHOTTING
    verify(shadowClusterJobSvc, times(1))
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJob, Status.FAILED, "Exposure deleted while provisioning");
    verify(shadowClusterExposureJob, never()).getSourceSnapshotId();
  }

  @Test
  public void testWaitForSnapshotStep_CpsSvcGetBackupSnapshotThrowsRuntimeException() {
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);
    when(cpsSvc.getBackupSnapshot(SOURCE_PROJECT_ID, SNAPSHOT_ID))
        .thenThrow(new RuntimeException("Database connection failed"));

    final RecordAndSnapshotForShadowClusterMove move = getMove();

    RuntimeException ex =
        assertThrows(
            RuntimeException.class, () -> move.waitForSnapshotStep(shadowClusterExposureJob));
    assertEquals("Database connection failed", ex.getMessage());
  }

  @Test
  public void testWaitForSnapshotStep_SnapshotNotFound_ReturnsFailedResult() throws SvcException {
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);
    when(cpsSvc.getBackupSnapshot(SOURCE_PROJECT_ID, SNAPSHOT_ID)).thenReturn(null);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final Result<?> result = move.waitForSnapshotStep(shadowClusterExposureJob);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Snapshot not found", result.getMessage());
  }

  @Test
  public void testScheduleShadowClusterJobs_ExposureNotFound_ThrowsException() {
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.empty());

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    final SvcException exception =
        assertThrows(
            SvcException.class, () -> move.scheduleShadowClusterJobs(shadowClusterExposureJob));

    assertEquals(EXPOSURE_ID + " cannot be found", exception.getMessage());
  }

  @Test
  public void testScheduleShadowClusterJobs_EmptyPermutations_ThrowsException()
      throws SvcException {
    final Exposure exposure = createMockExposureWithEmptyPermutations();
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(exposure));
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);
    when(shadowClusterJobSvc.createShadowClusterJobsForExposure(exposure, SNAPSHOT_ID))
        .thenThrow(
            new SvcException(NDSErrorCode.INVALID_ARGUMENT, "No permutations found for exposure"));

    final RecordAndSnapshotForShadowClusterMove move = getMove();

    assertThrows(
        SvcException.class, () -> move.scheduleShadowClusterJobs(shadowClusterExposureJob));
  }

  @Test
  public void
      testScheduleShadowClusterJobs_ExposureDeletedConcurrently_SchedulesJobsForDeletionToo()
          throws SvcException {
    final Exposure mockExposure = createMockExposure();
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(mockExposure));
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);
    final List<ObjectId> jobIds = List.of(new ObjectId(), new ObjectId());
    when(shadowClusterJobSvc.createShadowClusterJobsForExposure(mockExposure, SNAPSHOT_ID))
        .thenReturn(jobIds);

    when(shadowClusterExposureJob.targetDeletionDateReached()).thenReturn(true);
    final WriteResult writeResult = mock(WriteResult.class);
    when(shadowClusterJobDao.updateTargetDeletionDate(eq(jobIds), any(Date.class)))
        .thenReturn(writeResult);
    when(writeResult.getN()).thenReturn(jobIds.size());
    when(writeResult.wasAcknowledged()).thenReturn(true);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    move.scheduleShadowClusterJobs(shadowClusterExposureJob);

    verify(shadowClusterJobDao, times(1)).updateTargetDeletionDate(eq(jobIds), any(Date.class));
  }

  @Test
  public void
      testScheduleShadowClusterJobs_ExposureNotDeletedConcurrently_DoesNotScheduleJobsForDeletionToo()
          throws SvcException {
    final Exposure mockExposure = createMockExposure();
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(mockExposure));
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);
    final List<ObjectId> jobIds = List.of(new ObjectId(), new ObjectId());
    when(shadowClusterJobSvc.createShadowClusterJobsForExposure(mockExposure, SNAPSHOT_ID))
        .thenReturn(jobIds);

    when(shadowClusterExposureJob.targetDeletionDateReached()).thenReturn(false);

    final RecordAndSnapshotForShadowClusterMove move = getMove();
    move.scheduleShadowClusterJobs(shadowClusterExposureJob);

    verify(shadowClusterJobDao, never()).updateTargetDeletionDate(eq(jobIds), any(Date.class));
  }

  @Test
  public void testScheduleShadowClusterJobs_ExposureDeletedConcurrentlyButJobsNotUpdated_Throws()
      throws SvcException {
    final Exposure mockExposure = createMockExposure();
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(mockExposure));
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);
    final List<ObjectId> jobIds = List.of(new ObjectId(), new ObjectId());
    when(shadowClusterJobSvc.createShadowClusterJobsForExposure(mockExposure, SNAPSHOT_ID))
        .thenReturn(jobIds);

    when(shadowClusterExposureJob.targetDeletionDateReached()).thenReturn(true);
    final WriteResult writeResult = mock(WriteResult.class);
    when(shadowClusterJobDao.updateTargetDeletionDate(eq(jobIds), any(Date.class)))
        .thenReturn(writeResult);
    when(writeResult.getN()).thenReturn(jobIds.size() - 1);
    when(writeResult.wasAcknowledged()).thenReturn(true);

    final RecordAndSnapshotForShadowClusterMove move = getMove();

    assertThatThrownBy(() -> move.scheduleShadowClusterJobs(shadowClusterExposureJob))
        .isInstanceOf(SvcException.class)
        .hasFieldOrPropertyWithValue(
            "_messageParams",
            List.of("Failed to mark jobs as deleted. Check the logs for more details."));

    verify(shadowClusterJobDao, times(1)).updateTargetDeletionDate(eq(jobIds), any(Date.class));
  }

  @Test
  public void testScheduleShadowClusterJobs_ExposureDeletedConcurrentlyButUpdateNotAcked_Throws()
      throws SvcException {
    final Exposure mockExposure = createMockExposure();
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(mockExposure));
    when(shadowClusterExposureJob.getSourceSnapshotId()).thenReturn(SNAPSHOT_ID);
    final List<ObjectId> jobIds = List.of(new ObjectId(), new ObjectId());
    when(shadowClusterJobSvc.createShadowClusterJobsForExposure(mockExposure, SNAPSHOT_ID))
        .thenReturn(jobIds);

    when(shadowClusterExposureJob.targetDeletionDateReached()).thenReturn(true);
    final WriteResult writeResult = mock(WriteResult.class);
    when(shadowClusterJobDao.updateTargetDeletionDate(eq(jobIds), any(Date.class)))
        .thenReturn(writeResult);
    when(writeResult.getN()).thenReturn(jobIds.size());
    when(writeResult.wasAcknowledged()).thenReturn(false);

    final RecordAndSnapshotForShadowClusterMove move = getMove();

    assertThatThrownBy(() -> move.scheduleShadowClusterJobs(shadowClusterExposureJob))
        .isInstanceOf(SvcException.class)
        .hasFieldOrPropertyWithValue(
            "_messageParams",
            List.of("Failed to mark jobs as deleted. Check the logs for more details."));

    verify(shadowClusterJobDao, times(1)).updateTargetDeletionDate(eq(jobIds), any(Date.class));
  }

  @Test
  public void testPerformInternal_SnapshotCreationWithJobScheduling_Success() throws SvcException {
    // Setup state holder to track snapshot ID changes during test execution
    final ObjectId[] snapshotIdHolder = {null};

    // Mock getSourceSnapshotId() to return current value from our state holder
    when(shadowClusterExposureJob.getSourceSnapshotId())
        .thenAnswer(invocation -> snapshotIdHolder[0]);

    // Mock setSnapshotId() to update our state holder, simulating real ShadowClusterJob behavior
    doAnswer(
            invocation -> {
              snapshotIdHolder[0] = invocation.getArgument(0);
              return null;
            })
        .when(shadowClusterExposureJob)
        .setSourceSnapshotId(SNAPSHOT_ID);

    when(shadowClusterExposureJob.getExposureId()).thenReturn(EXPOSURE_ID);

    // Setup exposure with permutations for job scheduling
    final Exposure exposure = createMockExposure();
    when(shadowClusterSvc.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(exposure));

    // Mock job creation
    when(shadowClusterJobSvc.createShadowClusterJobsForExposure(exposure, SNAPSHOT_ID))
        .thenReturn(List.of(JOB_ID_1, JOB_ID_2));

    final RecordAndSnapshotForShadowClusterMove move = spy(getMove());

    // Mock waitForSnapshotStep to simulate successful snapshot creation workflow
    doAnswer(
            invocation -> {
              ShadowClusterExposureJob job = invocation.getArgument(0);
              // Simulate queueSnapshot() side effect: setting the snapshot ID on the job
              job.setSourceSnapshotId(SNAPSHOT_ID);
              // Return success to simulate completed snapshot creation
              return Result.done();
            })
        .when(move)
        .waitForSnapshotStep(shadowClusterExposureJob);

    final Result<?> result = move.performInternal();

    assertTrue(result.getStatus().isDone());

    // Verify jobs were scheduled with the correct snapshot ID
    verify(shadowClusterJobSvc, times(1)).createShadowClusterJobsForExposure(exposure, SNAPSHOT_ID);
  }

  private Exposure createMockExposure() {
    final Permutation permutation1 =
        new Permutation(
            PERMUTATION_ID_1,
            List.of(),
            PermutationType.BASELINE,
            new Treatment(new ObjectId()),
            1);
    final Permutation permutation2 =
        new Permutation(
            PERMUTATION_ID_2,
            List.of(),
            PermutationType.MODIFIED,
            new Treatment(new ObjectId()),
            1);

    return Exposure.builder()
        .id(EXPOSURE_ID)
        .sourceOrgId(SOURCE_ORG_ID)
        .sourceGroupId(GROUP_ID)
        .sourceClusterName(SOURCE_CLUSTER_NAME)
        .sourceClusterId(SOURCE_CLUSTER_ID)
        .permutations(List.of(permutation1, permutation2))
        .build();
  }

  private Exposure createMockExposureWithEmptyPermutations() {
    return Exposure.builder()
        .id(EXPOSURE_ID)
        .sourceOrgId(SOURCE_ORG_ID)
        .sourceGroupId(GROUP_ID)
        .sourceClusterName(SOURCE_CLUSTER_NAME)
        .sourceClusterId(SOURCE_CLUSTER_ID)
        .permutations(List.of())
        .build();
  }

  private RecordAndSnapshotForShadowClusterMove getMove() {
    return new RecordAndSnapshotForShadowClusterMove(
        planContext,
        SHADOW_CLUSTER_EXPOSURE_JOB_ID,
        shadowClusterJobSvc,
        shadowClusterSvc,
        cpsSvc,
        shadowClusterJobDao);
  }
}
