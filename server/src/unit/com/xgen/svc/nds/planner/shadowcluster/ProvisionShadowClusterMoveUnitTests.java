package com.xgen.svc.nds.planner.shadowcluster;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.inOrder;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.withSettings;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.restore._public.model.ShadowClusterJob;
import com.xgen.cloud.cps.restore._public.model.SystemClusterJob;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.SystemProjectType;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Exposure;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Permutation;
import com.xgen.cloud.nds.shadowcluster.model._public.model.Permutation.PermutationType;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowCluster;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowCluster.Status;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowClusterExposureJob;
import com.xgen.cloud.nds.shadowcluster.model._public.model.ShadowClusterStatusInfo;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterJobSvc;
import com.xgen.cloud.nds.shadowcluster.svc._public.svc.ShadowClusterSvc;
import com.xgen.module.common.planner.dao.PlanDao;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.svc.nds.model.ui.CloudProviderContainerView;
import com.xgen.svc.nds.svc.NDSCloudProviderContainerSvc;
import com.xgen.svc.nds.svc.SystemClusterJobSvc;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.svc.nds.svc.planning.NDSPlanExecutorCallbackSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InOrder;
import org.mockito.quality.Strictness;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ExtendWith(CloudProviderExtension.class)
public class ProvisionShadowClusterMoveUnitTests {

  private static final Logger LOG =
      LoggerFactory.getLogger(ProvisionShadowClusterMoveUnitTests.class);

  private static final ObjectId GROUP_ID = new ObjectId();
  private static final ObjectId SYSTEM_CLUSTER_JOB_ID = new ObjectId();
  private static final ObjectId PERMUTATION_ID = new ObjectId();
  private static final ObjectId EXPOSURE_ID = new ObjectId();
  private static final ObjectId SHADOW_CLUSTER_ID = new ObjectId();
  private static final ObjectId SOURCE_PROJECT_ID = new ObjectId();
  private static final String SOURCE_CLUSTER_NAME = "sourceCluster";
  private static final ObjectId SOURCE_CLUSTER_UNIQUE_ID = new ObjectId();
  private static final ObjectId CONTAINER_ID = new ObjectId();

  private PlanContext planContextMock;
  private SystemClusterJobSvc systemClusterJobSvcMock;
  private ShadowClusterJobSvc shadowClusterJobSvcMock;
  private ShadowClusterSvc shadowClusterSvcMock;
  private ShadowClusterJob shadowClusterJobMock;
  private NDSCloudProviderContainerSvc ndsCloudProviderContainerSvcMock;
  private NDSGroupDao ndsGroupDaoMock;
  private ClusterDescriptionDao clusterDescriptionDaoMock;
  private NDSClusterSvc ndsClusterSvcMock;
  private CpsSvc cpsSvcMock;
  private WaitForShadowClusterCreationStepFactory waitForShadowClusterCreationStepFactoryMock;

  private Permutation permutationMock;
  private Move.State stateMock;
  private Exposure exposureMock;
  private ShadowClusterExposureJob shadowClusterExposureJobMock;

  /** Creates a strict mock to catch unused stubs and improve test reliability. */
  private <T> T createStrictMock(Class<T> clazz) {
    return mock(clazz, withSettings().strictness(Strictness.STRICT_STUBS));
  }

  @BeforeEach
  public void setUp() throws SvcException {
    planContextMock = createStrictMock(PlanContext.class);
    systemClusterJobSvcMock = createStrictMock(SystemClusterJobSvc.class);
    shadowClusterJobSvcMock = createStrictMock(ShadowClusterJobSvc.class);
    shadowClusterSvcMock = createStrictMock(ShadowClusterSvc.class);
    shadowClusterJobMock = createStrictMock(ShadowClusterJob.class);
    ndsCloudProviderContainerSvcMock = createStrictMock(NDSCloudProviderContainerSvc.class);
    ndsGroupDaoMock = createStrictMock(NDSGroupDao.class);
    clusterDescriptionDaoMock = createStrictMock(ClusterDescriptionDao.class);
    ndsClusterSvcMock = createStrictMock(NDSClusterSvc.class);
    cpsSvcMock = createStrictMock(CpsSvc.class);
    waitForShadowClusterCreationStepFactoryMock =
        createStrictMock(WaitForShadowClusterCreationStepFactory.class);
    exposureMock = createStrictMock(Exposure.class);
    permutationMock = createStrictMock(Permutation.class);
    stateMock = createStrictMock(Move.State.class);
    shadowClusterExposureJobMock = createStrictMock(ShadowClusterExposureJob.class);
    AppSettings appSettingsMock = createStrictMock(AppSettings.class);
    final PlanDao planDaoMock = createStrictMock(PlanDao.class);

    doReturn(planDaoMock).when(planContextMock).getPlanDao();
    doReturn(Optional.empty())
        .when(planDaoMock)
        .findMoveValue(any(ObjectId.class), any(ObjectId.class), anyString());
    when(planContextMock.getLogger()).thenReturn(LOG);
    when(planContextMock.getScopedMoveObservable()).thenReturn(mock(Move.Observable.class));
    when(planContextMock.getAppSettings()).thenReturn(appSettingsMock);
    when(planContextMock.getGroupId()).thenReturn(GROUP_ID);
    when(planContextMock.getPlanExecutorCallback())
        .thenReturn(mock(NDSPlanExecutorCallbackSvc.class));

    when(shadowClusterJobSvcMock.getShadowClusterJob(eq(SYSTEM_CLUSTER_JOB_ID), any(Logger.class)))
        .thenReturn(shadowClusterJobMock);
    doNothing()
        .when(shadowClusterJobSvcMock)
        .abandonIfSourceClusterInInvalidState(
            SYSTEM_CLUSTER_JOB_ID, SOURCE_PROJECT_ID, SOURCE_CLUSTER_NAME);

    when(shadowClusterJobMock.getId()).thenReturn(SYSTEM_CLUSTER_JOB_ID);
    when(shadowClusterJobMock.getExposureId()).thenReturn(EXPOSURE_ID);
    when(shadowClusterJobMock.getJobStatus()).thenReturn(ShadowCluster.Status.PROVISIONING);
    doNothing().when(shadowClusterJobMock).setJobStatus(any(ShadowCluster.Status.class));
    doNothing().when(shadowClusterJobMock).setJobStatusReason(anyString());
    doNothing().when(shadowClusterJobSvcMock).saveShadowClusterJob(shadowClusterJobMock);
    when(shadowClusterJobMock.getSourceProjectId()).thenReturn(SOURCE_PROJECT_ID);
    when(shadowClusterJobMock.getSourceClusterName()).thenReturn(SOURCE_CLUSTER_NAME);
    when(shadowClusterJobMock.getSourceClusterUniqueId()).thenReturn(SOURCE_CLUSTER_UNIQUE_ID);
    when(shadowClusterJobMock.getSystemProjectType()).thenReturn(SystemProjectType.SHADOW_CLUSTER);
    when(shadowClusterSvcMock.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(exposureMock));
    when(shadowClusterSvcMock.getShadowClustersForPermutation(PERMUTATION_ID))
        .thenReturn(List.of());
    when(shadowClusterJobMock.targetDeletionDateReached()).thenReturn(false);
    when(exposureMock.getPermutations()).thenReturn(List.of(permutationMock));
    when(permutationMock.getId()).thenReturn(PERMUTATION_ID);
    when(permutationMock.getType()).thenReturn(PermutationType.BASELINE);
    when(exposureMock.getCurrentJobId()).thenReturn(new ObjectId());
  }

  // --- PerformInternal Tests ---

  @Test
  public void testPerformInternal_WhenJobNotFound_ReturnsFailed() throws SvcException {
    when(shadowClusterJobSvcMock.getShadowClusterJob(eq(SYSTEM_CLUSTER_JOB_ID), any(Logger.class)))
        .thenThrow(new SvcException(NDSErrorCode.RESOURCE_NOT_FOUND));
    final ProvisionShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isFailed());
    assertEquals(
        "Failed getting shadow cluster job for jobId: " + SYSTEM_CLUSTER_JOB_ID,
        result.getMessage());
    verify(shadowClusterSvcMock, never())
        .createShadowCluster(
            any(ObjectId.class),
            any(ObjectId.class),
            any(ObjectId.class),
            anyString(),
            any(ObjectId.class),
            any(PermutationType.class),
            any(ObjectId.class));
  }

  @Test
  public void testPerformInternal_WhenJobHasFailedStatus_ReturnsFailed() {
    when(shadowClusterJobMock.getJobStatus()).thenReturn(ShadowCluster.Status.FAILED);
    when(shadowClusterJobMock.getJobStatusReason()).thenReturn("failed");
    // Note: getExposureStatus method doesn't exist in ShadowClusterSvc - removing this mock setup

    final ProvisionShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();

    // Then it proceeds, but since the *job* object has FAILED, it should fail the move
    assertTrue(result.getStatus().isFailed());
    verify(shadowClusterSvcMock, never())
        .createShadowCluster(
            any(ObjectId.class),
            any(ObjectId.class),
            any(ObjectId.class),
            anyString(),
            any(ObjectId.class),
            any(PermutationType.class),
            any(ObjectId.class));
  }

  @Test
  public void testPerformInternal_WhenPermutationNotFound_ReturnsFailed() {
    when(permutationMock.getId()).thenReturn(new ObjectId()); // Different ID
    final ProvisionShadowClusterMove move = getMove();
    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isFailed());
    assertEquals(
        "Failed to get permutation type for permutationId: " + PERMUTATION_ID, result.getMessage());
    verify(shadowClusterSvcMock, never())
        .createShadowCluster(
            any(ObjectId.class),
            any(ObjectId.class),
            any(ObjectId.class),
            anyString(),
            any(ObjectId.class),
            any(PermutationType.class),
            any(ObjectId.class));
  }

  @Test
  public void testPerformInternal_CreateShadowCluster() throws SvcException {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.done())
        .when(move)
        .waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.done()).when(move).restoreToSystemCluster(shadowClusterJobMock);
    doNothing().when(move).updateExposureStatusIfAllClustersCreated(shadowClusterJobMock);

    when(shadowClusterSvcMock.createShadowCluster(
            EXPOSURE_ID,
            PERMUTATION_ID,
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            SOURCE_CLUSTER_UNIQUE_ID,
            PermutationType.BASELINE,
            SYSTEM_CLUSTER_JOB_ID))
        .thenReturn(SHADOW_CLUSTER_ID);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isDone());

    verify(shadowClusterSvcMock, times(1))
        .createShadowCluster(
            EXPOSURE_ID,
            PERMUTATION_ID,
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            SOURCE_CLUSTER_UNIQUE_ID,
            PermutationType.BASELINE,
            SYSTEM_CLUSTER_JOB_ID);
    // Verify state update for shadow cluster ID
    ArgumentCaptor<ObjectId> stateValueCaptor = ArgumentCaptor.forClass(ObjectId.class);
    verify(stateMock, times(1))
        .setValue(
            eq(ProvisionShadowClusterMove.StateFields.SHADOW_CLUSTER_ID),
            stateValueCaptor.capture());
    assertEquals(SHADOW_CLUSTER_ID, stateValueCaptor.getValue());

    verify(move, times(1)).updateExposureStatusIfAllClustersCreated(shadowClusterJobMock);
  }

  @Test
  public void testPerformInternal_SystemProjectCreationInProgress() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.inProgress())
        .when(move)
        .createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    when(shadowClusterSvcMock.createShadowCluster(
            EXPOSURE_ID,
            PERMUTATION_ID,
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            SOURCE_CLUSTER_UNIQUE_ID,
            PermutationType.BASELINE,
            SYSTEM_CLUSTER_JOB_ID))
        .thenReturn(SHADOW_CLUSTER_ID);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isInProgress());

    verify(move, times(1)).createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    verify(move, never())
        .waitForContainerAndClusterCreate(any(ShadowClusterJob.class), any(ObjectId.class));
  }

  @Test
  public void testPerformInternal_SystemProjectCreationFailed() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.failed("Project fail"))
        .when(move)
        .createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    when(shadowClusterSvcMock.createShadowCluster(
            EXPOSURE_ID,
            PERMUTATION_ID,
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            SOURCE_CLUSTER_UNIQUE_ID,
            PermutationType.BASELINE,
            SYSTEM_CLUSTER_JOB_ID))
        .thenReturn(SHADOW_CLUSTER_ID);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isFailed());
    assertEquals("Project fail", result.getMessage());

    verify(move, times(1)).createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    verify(move, never())
        .waitForContainerAndClusterCreate(any(ShadowClusterJob.class), any(ObjectId.class));
  }

  // Similar tests for Container Creation (In Progress, Failed)
  @Test
  public void testPerformInternal_ParallelExecutionInProgress() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.inProgress())
        .when(move)
        .waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    when(shadowClusterSvcMock.createShadowCluster(
            EXPOSURE_ID,
            PERMUTATION_ID,
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            SOURCE_CLUSTER_UNIQUE_ID,
            PermutationType.BASELINE,
            SYSTEM_CLUSTER_JOB_ID))
        .thenReturn(SHADOW_CLUSTER_ID);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isInProgress());

    verify(move, times(1))
        .waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);
  }

  @Test
  public void testPerformInternal_ParallelExecutionFailed() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.failed("Parallel execution fail"))
        .when(move)
        .waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    when(shadowClusterSvcMock.createShadowCluster(
            EXPOSURE_ID,
            PERMUTATION_ID,
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            SOURCE_CLUSTER_UNIQUE_ID,
            PermutationType.BASELINE,
            SYSTEM_CLUSTER_JOB_ID))
        .thenReturn(SHADOW_CLUSTER_ID);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isFailed());
    assertEquals("Parallel execution fail", result.getMessage());

    verify(move, times(1))
        .waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);
  }

  // --- Restore Step Tests ---
  @Test
  public void testPerformInternal_RestoreStepInProgress() throws SvcException {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.done())
        .when(move)
        .waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.inProgress()).when(move).waitForSystemClusterRestoreStep(shadowClusterJobMock);
    when(shadowClusterSvcMock.createShadowCluster(
            EXPOSURE_ID,
            PERMUTATION_ID,
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            SOURCE_CLUSTER_UNIQUE_ID,
            PermutationType.BASELINE,
            SYSTEM_CLUSTER_JOB_ID))
        .thenReturn(SHADOW_CLUSTER_ID);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isInProgress());

    verify(move, times(1)).waitForSystemClusterRestoreStep(shadowClusterJobMock);
    verify(move, never()).updateExposureStatusIfAllClustersCreated(any(ShadowClusterJob.class));
  }

  @Test
  public void testPerformInternal_RestoreStepFailed() throws SvcException {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.done())
        .when(move)
        .waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.failed("Restore fail"))
        .when(move)
        .waitForSystemClusterRestoreStep(shadowClusterJobMock);
    when(shadowClusterSvcMock.createShadowCluster(
            EXPOSURE_ID,
            PERMUTATION_ID,
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            SOURCE_CLUSTER_UNIQUE_ID,
            PermutationType.BASELINE,
            SYSTEM_CLUSTER_JOB_ID))
        .thenReturn(SHADOW_CLUSTER_ID);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isFailed());
    assertEquals("Restore fail", result.getMessage());

    verify(move, times(1)).waitForSystemClusterRestoreStep(shadowClusterJobMock);
    verify(move, never()).updateExposureStatusIfAllClustersCreated(any(ShadowClusterJob.class));
  }

  @Test
  public void testPerformInternal_CompleteFlowWithRestore() throws SvcException {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.done())
        .when(move)
        .waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.done()).when(move).restoreToSystemCluster(shadowClusterJobMock);
    doNothing().when(move).updateExposureStatusIfAllClustersCreated(shadowClusterJobMock);

    when(shadowClusterSvcMock.createShadowCluster(
            EXPOSURE_ID,
            PERMUTATION_ID,
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            SOURCE_CLUSTER_UNIQUE_ID,
            PermutationType.BASELINE,
            SYSTEM_CLUSTER_JOB_ID))
        .thenReturn(SHADOW_CLUSTER_ID);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isDone());

    // Verify all steps executed in correct order
    verify(move, times(1)).createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    verify(move, times(1))
        .waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    verify(move, times(1)).restoreToSystemCluster(shadowClusterJobMock);

    // Verify final exposure status update through updateExposureStatusIfAllClustersCreated
    verify(move, times(1)).updateExposureStatusIfAllClustersCreated(shadowClusterJobMock);
  }

  @Test
  public void testPerformInternal_RestoreToSystemClusterSuccess() throws SvcException {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.done())
        .when(move)
        .waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    doReturn(Result.done()).when(move).restoreToSystemCluster(shadowClusterJobMock);
    doNothing().when(move).updateExposureStatusIfAllClustersCreated(shadowClusterJobMock);

    when(shadowClusterSvcMock.createShadowCluster(
            EXPOSURE_ID,
            PERMUTATION_ID,
            SOURCE_PROJECT_ID,
            SOURCE_CLUSTER_NAME,
            SOURCE_CLUSTER_UNIQUE_ID,
            PermutationType.BASELINE,
            SYSTEM_CLUSTER_JOB_ID))
        .thenReturn(SHADOW_CLUSTER_ID);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isDone());

    verify(move, times(1)).createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    verify(move, times(1))
        .waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);
    verify(move, times(1)).restoreToSystemCluster(shadowClusterJobMock);
    verify(move, times(1)).updateExposureStatusIfAllClustersCreated(shadowClusterJobMock);
  }

  @Test
  public void testPerformInternal_TargetDeletionDateReached_Fails() {
    final ProvisionShadowClusterMove move = getMove();
    when(shadowClusterJobMock.targetDeletionDateReached()).thenReturn(true);

    final Result<?> result = move.performInternal();
    assertTrue(result.getStatus().isFailed());

    verify(shadowClusterJobSvcMock, times(1))
        .updateShadowClusterJobStatus(
            shadowClusterJobMock,
            Status.FAILED,
            "Target deletion date has been reached while provisioning");
  }

  // --- Parallel Execution Tests ---

  @Test
  public void testWaitForContainerAndClusterCreate_Success() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).initiateContainerCreation(shadowClusterJobMock);
    doReturn(Result.done()).when(move).waitForContainerProvisioning(shadowClusterJobMock);
    doReturn(Result.done()).when(move).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    final Result<Result.NoData> result =
        move.waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isDone());
    verify(move, times(1)).initiateContainerCreation(shadowClusterJobMock);
    verify(move, times(1)).waitForContainerProvisioning(shadowClusterJobMock);
    verify(move, times(1)).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);
  }

  @Test
  public void testWaitForContainerAndClusterCreate_InitiationFailed() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.failed("Container init failed"))
        .when(move)
        .initiateContainerCreation(shadowClusterJobMock);

    final Result<Result.NoData> result =
        move.waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Container init failed", result.getMessage());
    verify(move, times(1)).initiateContainerCreation(shadowClusterJobMock);
    verify(move, never()).waitForContainerProvisioning(any(ShadowClusterJob.class));
    verify(move, never()).createSystemCluster(any(ShadowClusterJob.class), any(ObjectId.class));
  }

  @Test
  public void testWaitForContainerAndClusterInParallel_ContainerProvisioningFailed() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).initiateContainerCreation(shadowClusterJobMock);
    doReturn(Result.failed("Container provisioning failed"))
        .when(move)
        .waitForContainerProvisioning(shadowClusterJobMock);
    doReturn(Result.done()).when(move).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    final Result<Result.NoData> result =
        move.waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isFailed());
    verify(move, times(1)).initiateContainerCreation(shadowClusterJobMock);
    verify(move, times(1)).waitForContainerProvisioning(shadowClusterJobMock);
    verify(move, times(1)).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);
  }

  @Test
  public void testWaitForContainerAndClusterInParallel_ClusterCreationFailed() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).initiateContainerCreation(shadowClusterJobMock);
    doReturn(Result.done()).when(move).waitForContainerProvisioning(shadowClusterJobMock);
    doReturn(Result.failed("Cluster creation failed"))
        .when(move)
        .createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    final Result<Result.NoData> result =
        move.waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isFailed());
    verify(move, times(1)).initiateContainerCreation(shadowClusterJobMock);
    verify(move, times(1)).waitForContainerProvisioning(shadowClusterJobMock);
    verify(move, times(1)).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);
  }

  @Test
  public void testWaitForContainerAndClusterInParallel_BothInProgress() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).initiateContainerCreation(shadowClusterJobMock);
    doReturn(Result.inProgress()).when(move).waitForContainerProvisioning(shadowClusterJobMock);
    doReturn(Result.inProgress())
        .when(move)
        .createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    final Result<Result.NoData> result =
        move.waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isInProgress());
    verify(move, times(1)).initiateContainerCreation(shadowClusterJobMock);
    verify(move, times(1)).waitForContainerProvisioning(shadowClusterJobMock);
    verify(move, times(1)).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);
  }

  @Test
  public void testWaitForContainerAndClusterInParallel_ContainerInProgress() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).initiateContainerCreation(shadowClusterJobMock);
    doReturn(Result.inProgress()).when(move).waitForContainerProvisioning(shadowClusterJobMock);
    doReturn(Result.done()).when(move).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    final Result<Result.NoData> result =
        move.waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isInProgress());
    verify(move, times(1)).initiateContainerCreation(shadowClusterJobMock);
    verify(move, times(1)).waitForContainerProvisioning(shadowClusterJobMock);
    verify(move, times(1)).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);
  }

  @Test
  public void testWaitForContainerAndClusterInParallel_InitiationInProgress() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.inProgress()).when(move).initiateContainerCreation(shadowClusterJobMock);

    final Result<Result.NoData> result =
        move.waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isInProgress());
    verify(move, times(1)).initiateContainerCreation(shadowClusterJobMock);
    verify(move, never()).waitForContainerProvisioning(any(ShadowClusterJob.class));
    verify(move, never()).createSystemCluster(any(ShadowClusterJob.class), any(ObjectId.class));
  }

  @Test
  public void testWaitForContainerAndClusterInParallel_ContainerDoneClusterInProgress() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).initiateContainerCreation(shadowClusterJobMock);
    doReturn(Result.done()).when(move).waitForContainerProvisioning(shadowClusterJobMock);
    doReturn(Result.inProgress())
        .when(move)
        .createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    final Result<Result.NoData> result =
        move.waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isInProgress());
    verify(move, times(1)).initiateContainerCreation(shadowClusterJobMock);
    verify(move, times(1)).waitForContainerProvisioning(shadowClusterJobMock);
    verify(move, times(1)).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);
  }

  @Test
  public void testWaitForContainerAndClusterInParallel_ContainerInProgressClusterDone() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).initiateContainerCreation(shadowClusterJobMock);
    doReturn(Result.inProgress()).when(move).waitForContainerProvisioning(shadowClusterJobMock);
    doReturn(Result.done()).when(move).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    final Result<Result.NoData> result =
        move.waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isInProgress());
    verify(move, times(1)).initiateContainerCreation(shadowClusterJobMock);
    verify(move, times(1)).waitForContainerProvisioning(shadowClusterJobMock);
    verify(move, times(1)).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);
  }

  @Test
  public void testWaitForContainerAndClusterCreate_FailureMessagePropagation() {
    final ProvisionShadowClusterMove move = getMove();
    final String failureMessage = "Container provisioning failed with specific error";
    doReturn(Result.done()).when(move).initiateContainerCreation(shadowClusterJobMock);
    doReturn(Result.failed(failureMessage))
        .when(move)
        .waitForContainerProvisioning(shadowClusterJobMock);
    doReturn(Result.done()).when(move).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    final Result<Result.NoData> result =
        move.waitForContainerAndClusterCreate(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isFailed());
    // Verify that awaitAll immediately returns failed status without preserving specific message
    // (awaitAll returns Result.failed() without message as per implementation)
    verify(move, times(1)).initiateContainerCreation(shadowClusterJobMock);
    verify(move, times(1)).waitForContainerProvisioning(shadowClusterJobMock);
    verify(move, times(1)).createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);
  }

  // --- RollbackInternal Tests ---

  @Test
  public void testRollbackInternal_JobNotFound() throws SvcException {
    when(shadowClusterJobSvcMock.getShadowClusterJob(eq(SYSTEM_CLUSTER_JOB_ID), any(Logger.class)))
        .thenThrow(new SvcException(NDSErrorCode.RESOURCE_NOT_FOUND));
    final ProvisionShadowClusterMove move = getMove();
    final Result<?> result = move.rollbackInternal();
    assertTrue(result.getStatus().isFailed());
    assertEquals(
        "Failed getting shadow cluster job during rollback for jobId: " + SYSTEM_CLUSTER_JOB_ID,
        result.getMessage());
    // Exposure status is now derived from job status - no direct updateExposureStatus call needed
  }

  @Test
  public void testRollbackInternal_ExposureNotFound() {
    when(shadowClusterSvcMock.getExposure(EXPOSURE_ID)).thenReturn(Optional.empty());
    final ProvisionShadowClusterMove move = getMove();

    final Result<?> result = move.rollbackInternal();

    assertTrue(result.getStatus().isFailed());
    assertEquals(
        "Failed to find exposure for shadow cluster job with ID: " + SYSTEM_CLUSTER_JOB_ID,
        result.getMessage());
  }

  @Test
  public void testRollbackInternal_ExposureJobNotFound() throws SvcException {
    when(shadowClusterJobSvcMock.getShadowClusterExposureJob(
            eq(exposureMock.getCurrentJobId()), any(Logger.class)))
        .thenThrow(new SvcException(NDSErrorCode.RESOURCE_NOT_FOUND));
    final ProvisionShadowClusterMove move = getMove();

    final Result<?> result = move.rollbackInternal();

    assertTrue(result.getStatus().isFailed());
    assertEquals(
        "Failed to find exposure job with id: " + exposureMock.getCurrentJobId(),
        result.getMessage());
  }

  @Test
  public void testRollbackInternal_ClusterDeletionInProgress() throws SvcException {
    when(shadowClusterJobSvcMock.getShadowClusterExposureJob(
            eq(exposureMock.getCurrentJobId()), any(Logger.class)))
        .thenReturn(shadowClusterExposureJobMock);
    when(shadowClusterJobMock.isShadowClusterDeleted()).thenReturn(false);
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.inProgress()).when(move).waitForShadowClusterDeletionStep(shadowClusterJobMock);

    final Result<?> result = move.rollbackInternal();
    assertTrue(result.getStatus().isInProgress());

    verify(move, times(1)).waitForShadowClusterDeletionStep(shadowClusterJobMock);
    verify(move, never()).waitForContainerDeletionStep(any(ShadowClusterJob.class));
    verify(shadowClusterJobSvcMock)
        .updateShadowClusterJobStatus(
            shadowClusterJobMock, Status.FAILED, "Shadow cluster provisioning failed.");
    verify(shadowClusterJobSvcMock)
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJobMock, Exposure.Status.FAILED, "Cluster provisioning failed.");
  }

  @Test
  public void testRollbackInternal_ContainerDeletionInProgress() throws SvcException {
    when(shadowClusterJobSvcMock.getShadowClusterExposureJob(
            eq(exposureMock.getCurrentJobId()), any(Logger.class)))
        .thenReturn(shadowClusterExposureJobMock);
    when(shadowClusterJobMock.isShadowClusterDeleted()).thenReturn(false);
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).waitForShadowClusterDeletionStep(shadowClusterJobMock);
    doReturn(Result.inProgress()).when(move).waitForContainerDeletionStep(shadowClusterJobMock);

    final Result<?> result = move.rollbackInternal();
    assertTrue(result.getStatus().isInProgress());

    verify(move, times(1)).waitForShadowClusterDeletionStep(shadowClusterJobMock);
    verify(move, times(1)).waitForContainerDeletionStep(shadowClusterJobMock);
    verify(move, never()).waitForSystemProjectDeletionStep(any(ShadowClusterJob.class));
    verify(shadowClusterJobSvcMock)
        .updateShadowClusterJobStatus(
            shadowClusterJobMock, Status.FAILED, "Shadow cluster provisioning failed.");
    verify(shadowClusterJobSvcMock)
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJobMock, Exposure.Status.FAILED, "Cluster provisioning failed.");
  }

  @Test
  public void testRollbackInternal_ProjectDeletionInProgress() throws SvcException {
    when(shadowClusterJobSvcMock.getShadowClusterExposureJob(
            eq(exposureMock.getCurrentJobId()), any(Logger.class)))
        .thenReturn(shadowClusterExposureJobMock);
    when(shadowClusterJobMock.isShadowClusterDeleted()).thenReturn(false);
    when(shadowClusterJobMock.isSystemProjectDeleted()).thenReturn(false);
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).waitForShadowClusterDeletionStep(shadowClusterJobMock);
    doReturn(Result.done()).when(move).waitForContainerDeletionStep(shadowClusterJobMock);
    doReturn(Result.inProgress()).when(move).waitForSystemProjectDeletionStep(shadowClusterJobMock);

    final Result<?> result = move.rollbackInternal();
    assertTrue(result.getStatus().isInProgress());

    verify(move, times(1)).waitForShadowClusterDeletionStep(shadowClusterJobMock);
    verify(move, times(1)).waitForContainerDeletionStep(shadowClusterJobMock);
    verify(move, times(1)).waitForSystemProjectDeletionStep(shadowClusterJobMock);
    verify(shadowClusterJobSvcMock)
        .updateShadowClusterJobStatus(
            shadowClusterJobMock, Status.FAILED, "Shadow cluster provisioning failed.");
    verify(shadowClusterJobSvcMock)
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJobMock, Exposure.Status.FAILED, "Cluster provisioning failed.");
  }

  @Test
  public void testRollbackInternal_Success() throws SvcException {
    when(shadowClusterJobSvcMock.getShadowClusterExposureJob(
            eq(exposureMock.getCurrentJobId()), any(Logger.class)))
        .thenReturn(shadowClusterExposureJobMock);
    final ProvisionShadowClusterMove move = getMove();
    // Assume everything is already deleted
    when(shadowClusterJobMock.isShadowClusterDeleted()).thenReturn(true);
    doReturn(Result.done()).when(move).waitForContainerDeletionStep(shadowClusterJobMock);
    when(shadowClusterJobMock.isSystemProjectDeleted()).thenReturn(true);

    final Result<?> result = move.rollbackInternal();
    assertTrue(result.getStatus().isDone());

    verify(move, times(1)).waitForContainerDeletionStep(shadowClusterJobMock);
    verify(move, never()).waitForShadowClusterDeletionStep(any(ShadowClusterJob.class)); // Skipped
    verify(move, never()).waitForSystemProjectDeletionStep(any(SystemClusterJob.class)); // Skipped
    verify(shadowClusterJobSvcMock)
        .updateShadowClusterJobStatus(
            shadowClusterJobMock, Status.FAILED, "Shadow cluster provisioning failed.");
    verify(shadowClusterJobSvcMock)
        .updateShadowClusterExposureJobStatus(
            shadowClusterExposureJobMock, Exposure.Status.FAILED, "Cluster provisioning failed.");
  }

  // --- Container Initiation Tests ---

  @Test
  public void testInitiateContainerCreation_ContainerExists() {
    when(shadowClusterJobMock.getContainerId()).thenReturn(CONTAINER_ID);
    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result = move.initiateContainerCreation(shadowClusterJobMock);

    assertTrue(result.getStatus().isDone());
    verify(move, never())
        .requestContainerCreation(any(ShadowClusterJob.class), any(ObjectId.class));
  }

  @Test
  public void testInitiateContainerCreation_Success() {
    when(shadowClusterJobMock.getContainerId()).thenReturn(null);
    when(shadowClusterJobMock.getSystemProjectId()).thenReturn(GROUP_ID);
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.inProgress())
        .when(move)
        .requestContainerCreation(shadowClusterJobMock, GROUP_ID);

    final Result<Result.NoData> result = move.initiateContainerCreation(shadowClusterJobMock);

    assertTrue(result.getStatus().isDone());
    verify(move, times(1)).requestContainerCreation(shadowClusterJobMock, GROUP_ID);
  }

  @Test
  public void testInitiateContainerCreation_RequestFailed() {
    when(shadowClusterJobMock.getContainerId()).thenReturn(null);
    when(shadowClusterJobMock.getSystemProjectId()).thenReturn(GROUP_ID);
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.failed("Request failed"))
        .when(move)
        .requestContainerCreation(shadowClusterJobMock, GROUP_ID);

    final Result<Result.NoData> result = move.initiateContainerCreation(shadowClusterJobMock);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Request failed", result.getMessage());
    verify(move, times(1)).requestContainerCreation(shadowClusterJobMock, GROUP_ID);
  }

  // --- Container Provisioning Tests ---

  @Test
  public void testWaitForContainerProvisioning_Success() throws SvcException {
    final CloudProviderContainer container = mock(CloudProviderContainer.class);
    when(container.isProvisioned()).thenReturn(true);
    when(shadowClusterJobMock.getContainerId()).thenReturn(CONTAINER_ID);
    when(shadowClusterJobMock.getSystemProjectId()).thenReturn(GROUP_ID);
    when(ndsCloudProviderContainerSvcMock.getContainerById(GROUP_ID, CONTAINER_ID))
        .thenReturn(Optional.of(container));
    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result = move.waitForContainerProvisioning(shadowClusterJobMock);

    assertTrue(result.getStatus().isDone());

    // Validate direct ShadowCluster status update
    shadowClusterJobSvcMock.updateShadowClusterJobStatus(
        shadowClusterJobMock, Status.CREATED_CONTAINER, "Created container");
  }

  @Test
  public void testWaitForContainerProvisioning_ContainerIdNull() {
    when(shadowClusterJobMock.getContainerId()).thenReturn(null);
    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result = move.waitForContainerProvisioning(shadowClusterJobMock);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Container not initialized", result.getMessage());
  }

  @Test
  public void testWaitForContainerProvisioning_GetContainerException() throws SvcException {
    when(shadowClusterJobMock.getContainerId()).thenReturn(CONTAINER_ID);
    when(shadowClusterJobMock.getSystemProjectId()).thenReturn(GROUP_ID);
    when(ndsCloudProviderContainerSvcMock.getContainerById(GROUP_ID, CONTAINER_ID))
        .thenThrow(new SvcException(NDSErrorCode.INTERNAL));
    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result = move.waitForContainerProvisioning(shadowClusterJobMock);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Failed to get container: Internal Error", result.getMessage());
  }

  @Test
  public void testWaitForContainerProvisioning_ContainerNotFound() throws SvcException {
    when(shadowClusterJobMock.getContainerId()).thenReturn(CONTAINER_ID);
    when(shadowClusterJobMock.getSystemProjectId()).thenReturn(GROUP_ID);
    when(ndsCloudProviderContainerSvcMock.getContainerById(GROUP_ID, CONTAINER_ID))
        .thenReturn(Optional.empty());
    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result = move.waitForContainerProvisioning(shadowClusterJobMock);

    assertTrue(result.getStatus().isInProgress());
  }

  @Test
  public void testWaitForContainerProvisioning_InProgress() throws SvcException {
    final CloudProviderContainer container = mock(CloudProviderContainer.class);
    when(container.isProvisioned()).thenReturn(false);
    when(shadowClusterJobMock.getContainerId()).thenReturn(CONTAINER_ID);
    when(shadowClusterJobMock.getSystemProjectId()).thenReturn(GROUP_ID);
    when(ndsCloudProviderContainerSvcMock.getContainerById(GROUP_ID, CONTAINER_ID))
        .thenReturn(Optional.of(container));
    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result = move.waitForContainerProvisioning(shadowClusterJobMock);

    assertTrue(result.getStatus().isInProgress());
  }

  // --- requestContainerCreation Tests ---

  @Test
  public void testRequestContainerCreation_SourceClusterDescriptionNotFound() throws SvcException {
    when(clusterDescriptionDaoMock.findByUniqueId(SOURCE_PROJECT_ID, SOURCE_CLUSTER_UNIQUE_ID))
        .thenReturn(Optional.empty());

    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result =
        move.requestContainerCreation(shadowClusterJobMock, GROUP_ID);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Source cluster description not found", result.getMessage());
    verify(ndsCloudProviderContainerSvcMock, never())
        .upsertCloudContainer(any(ObjectId.class), any(CloudProviderContainerView.class));
  }

  @Test
  public void testRequestContainerCreation_NoRegions() throws SvcException {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(clusterDescription.getRegionNames()).thenReturn(new HashSet<>());
    when(clusterDescriptionDaoMock.findByUniqueId(SOURCE_PROJECT_ID, SOURCE_CLUSTER_UNIQUE_ID))
        .thenReturn(Optional.of(clusterDescription));

    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result =
        move.requestContainerCreation(shadowClusterJobMock, GROUP_ID);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Source cluster must have exactly one region", result.getMessage());
    verify(ndsCloudProviderContainerSvcMock, never())
        .upsertCloudContainer(any(ObjectId.class), any(CloudProviderContainerView.class));
  }

  @Test
  public void testRequestContainerCreation_MultipleRegions() throws SvcException {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final RegionName region1 = mock(RegionName.class);
    final RegionName region2 = mock(RegionName.class);
    final Set<RegionName> regions = new HashSet<>();
    regions.add(region1);
    regions.add(region2);
    when(clusterDescription.getRegionNames()).thenReturn(regions);
    when(clusterDescriptionDaoMock.findByUniqueId(SOURCE_PROJECT_ID, SOURCE_CLUSTER_UNIQUE_ID))
        .thenReturn(Optional.of(clusterDescription));

    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result =
        move.requestContainerCreation(shadowClusterJobMock, GROUP_ID);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Source cluster must have exactly one region", result.getMessage());
    verify(ndsCloudProviderContainerSvcMock, never())
        .upsertCloudContainer(any(ObjectId.class), any(CloudProviderContainerView.class));
  }

  @Test
  public void testRequestContainerCreation_InvalidAWSRegion() throws SvcException {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final RegionName regionName = mock(RegionName.class);
    when(regionName.getName()).thenReturn("invalid-region");
    final Set<RegionName> regions = new HashSet<>();
    regions.add(regionName);
    when(clusterDescription.getRegionNames()).thenReturn(regions);
    when(clusterDescriptionDaoMock.findByUniqueId(SOURCE_PROJECT_ID, SOURCE_CLUSTER_UNIQUE_ID))
        .thenReturn(Optional.of(clusterDescription));

    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result =
        move.requestContainerCreation(shadowClusterJobMock, GROUP_ID);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Source cluster region is not a valid AWS region", result.getMessage());
    verify(ndsCloudProviderContainerSvcMock, never())
        .upsertCloudContainer(any(ObjectId.class), any(CloudProviderContainerView.class));
  }

  @Test
  public void testRequestContainerCreation_UpsertContainerThrowsException() throws SvcException {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final RegionName regionName = mock(RegionName.class);
    when(regionName.getName()).thenReturn("US_EAST_1");
    final Set<RegionName> regions = new HashSet<>();
    regions.add(regionName);
    when(clusterDescription.getRegionNames()).thenReturn(regions);
    when(clusterDescriptionDaoMock.findByUniqueId(SOURCE_PROJECT_ID, SOURCE_CLUSTER_UNIQUE_ID))
        .thenReturn(Optional.of(clusterDescription));
    when(ndsCloudProviderContainerSvcMock.upsertCloudContainer(
            eq(GROUP_ID), any(CloudProviderContainerView.class)))
        .thenThrow(new SvcException(NDSErrorCode.INTERNAL));

    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result =
        move.requestContainerCreation(shadowClusterJobMock, GROUP_ID);

    assertTrue(result.getStatus().isFailed());
    assertEquals("Failed to create container: Internal Error", result.getMessage());
    verify(shadowClusterJobMock, never()).setContainerId(any(ObjectId.class));
    verify(systemClusterJobSvcMock, never()).saveSystemClusterJob(any(SystemClusterJob.class));
    verify(ndsGroupDaoMock, never()).setPlanASAP(any(ObjectId.class));
  }

  @Test
  public void testRequestContainerCreation_Success() throws SvcException {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final RegionName regionName = mock(RegionName.class);
    when(regionName.getName()).thenReturn("US_EAST_1");
    final Set<RegionName> regions = new HashSet<>();
    regions.add(regionName);
    when(clusterDescription.getRegionNames()).thenReturn(regions);
    when(clusterDescriptionDaoMock.findByUniqueId(SOURCE_PROJECT_ID, SOURCE_CLUSTER_UNIQUE_ID))
        .thenReturn(Optional.of(clusterDescription));
    when(ndsCloudProviderContainerSvcMock.upsertCloudContainer(
            eq(GROUP_ID), any(CloudProviderContainerView.class)))
        .thenReturn(CONTAINER_ID);

    final ProvisionShadowClusterMove move = getMove();

    final Result<Result.NoData> result =
        move.requestContainerCreation(shadowClusterJobMock, GROUP_ID);

    assertTrue(result.getStatus().isInProgress());
    verify(shadowClusterJobMock, times(1)).setContainerId(CONTAINER_ID);
    verify(systemClusterJobSvcMock, times(1)).saveSystemClusterJob(shadowClusterJobMock);
    verify(ndsGroupDaoMock, times(1)).setPlanASAP(GROUP_ID);
  }

  // --- Restore to System Cluster Tests ---

  @Test
  public void testRestoreToSystemCluster_Success() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).waitForSystemClusterRestoreStep(shadowClusterJobMock);
    doNothing().when(shadowClusterJobMock).markRestoreCompleted();
    doNothing().when(systemClusterJobSvcMock).saveSystemClusterJob(shadowClusterJobMock);
    doNothing().when(shadowClusterJobSvcMock).saveShadowClusterJob(shadowClusterJobMock);

    final Result<Result.NoData> result = move.restoreToSystemCluster(shadowClusterJobMock);

    assertTrue(result.getStatus().isDone());

    // Verify method call sequence
    verify(move, times(1)).waitForSystemClusterRestoreStep(shadowClusterJobMock);
    verify(shadowClusterJobMock, times(1)).markRestoreCompleted();
    verify(systemClusterJobSvcMock, times(1)).saveSystemClusterJob(shadowClusterJobMock);
    verify(shadowClusterJobSvcMock, times(1))
        .updateShadowClusterJobStatus(
            shadowClusterJobMock, Status.RESTORED, "Finished restoring to Shadow Cluster");
  }

  @Test
  public void testRestoreToSystemCluster_StateManagementVerification() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).waitForSystemClusterRestoreStep(shadowClusterJobMock);
    doNothing().when(shadowClusterJobMock).markRestoreCompleted();
    doNothing().when(systemClusterJobSvcMock).saveSystemClusterJob(shadowClusterJobMock);
    doNothing().when(shadowClusterJobSvcMock).saveShadowClusterJob(shadowClusterJobMock);

    final Result<Result.NoData> result = move.restoreToSystemCluster(shadowClusterJobMock);

    assertTrue(result.getStatus().isDone());

    // Verify state management calls happen in correct order
    InOrder inOrder = inOrder(move, shadowClusterJobMock, systemClusterJobSvcMock);
    inOrder.verify(move).waitForSystemClusterRestoreStep(shadowClusterJobMock);
    inOrder.verify(shadowClusterJobMock).markRestoreCompleted();
    inOrder.verify(systemClusterJobSvcMock).saveSystemClusterJob(shadowClusterJobMock);

    // Note: updateShadowClusterStatus is not called within restoreToSystemCluster method
    // It's called later in the main performInternal flow
  }

  @Test
  public void testRestoreToSystemCluster_InProgress() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.inProgress()).when(move).waitForSystemClusterRestoreStep(shadowClusterJobMock);

    final Result<Result.NoData> result = move.restoreToSystemCluster(shadowClusterJobMock);

    assertTrue(result.getStatus().isInProgress());

    // Verify early return - no state management calls should happen
    verify(move, times(1)).waitForSystemClusterRestoreStep(shadowClusterJobMock);
    verify(shadowClusterJobMock, never()).markRestoreCompleted();
    verify(systemClusterJobSvcMock, never()).saveSystemClusterJob(shadowClusterJobMock);
  }

  @Test
  public void testRestoreToSystemCluster_Failed() {
    final ProvisionShadowClusterMove move = getMove();
    final String failureMessage = "Restore operation failed";
    doReturn(Result.failed(failureMessage))
        .when(move)
        .waitForSystemClusterRestoreStep(shadowClusterJobMock);

    final Result<Result.NoData> result = move.restoreToSystemCluster(shadowClusterJobMock);

    assertTrue(result.getStatus().isFailed());
    assertEquals(failureMessage, result.getMessage());

    // Verify failure handling
    verify(move, times(1)).waitForSystemClusterRestoreStep(shadowClusterJobMock);

    shadowClusterJobSvcMock.updateShadowClusterJobStatus(
        shadowClusterJobMock,
        Status.FAILED,
        "Failed to restore to Shadow Cluster for permutationId: " + PERMUTATION_ID);

    // Verify no success state management calls
    verify(shadowClusterJobMock, never()).markRestoreCompleted();
    verify(systemClusterJobSvcMock, never()).saveSystemClusterJob(shadowClusterJobMock);
  }

  @Test
  public void testRestoreToSystemCluster_FailureStatusSetting() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.failed("Test failure"))
        .when(move)
        .waitForSystemClusterRestoreStep(shadowClusterJobMock);

    move.restoreToSystemCluster(shadowClusterJobMock);

    shadowClusterJobSvcMock.updateShadowClusterJobStatus(
        shadowClusterJobMock,
        Status.FAILED,
        "Failed to restore to Shadow Cluster for permutationId: " + PERMUTATION_ID);
  }

  @Test
  public void testRestoreToSystemCluster_ParameterValidation() {
    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).waitForSystemClusterRestoreStep(shadowClusterJobMock);
    doNothing().when(shadowClusterJobMock).markRestoreCompleted();
    doNothing().when(systemClusterJobSvcMock).saveSystemClusterJob(shadowClusterJobMock);
    move.restoreToSystemCluster(shadowClusterJobMock);

    // Verify correct parameters passed to dependencies
    ArgumentCaptor<ShadowClusterJob> jobCaptor = ArgumentCaptor.forClass(ShadowClusterJob.class);
    verify(move).waitForSystemClusterRestoreStep(jobCaptor.capture());
    assertEquals(shadowClusterJobMock, jobCaptor.getValue());

    // Verify that the job status was updated correctly through service method
    verify(shadowClusterJobSvcMock, times(1))
        .updateShadowClusterJobStatus(
            shadowClusterJobMock, Status.RESTORED, "Finished restoring to Shadow Cluster");
  }

  @Test
  public void testCreateSystemCluster_WithStateUpdates() {
    final ObjectId systemClusterUniqueId = new ObjectId();
    final String systemClusterName = "test-system-cluster";
    when(shadowClusterJobMock.getSystemProjectId()).thenReturn(GROUP_ID);
    when(shadowClusterJobMock.getSystemClusterName()).thenReturn(systemClusterName);

    final ProvisionShadowClusterMove move = getMove();
    doReturn(Result.done()).when(move).waitForSystemClusterCreationStep(shadowClusterJobMock);
    doReturn(systemClusterUniqueId)
        .when(move)
        .getClusterUniqueId(any(ObjectId.class), eq(systemClusterName));
    doNothing().when(shadowClusterJobMock).markSystemClusterCreated(systemClusterUniqueId);
    doNothing().when(systemClusterJobSvcMock).saveSystemClusterJob(shadowClusterJobMock);
    doNothing()
        .when(shadowClusterSvcMock)
        .updateShadowClusterDetails(SHADOW_CLUSTER_ID, systemClusterUniqueId, systemClusterName);

    final Result<Result.NoData> result =
        move.createSystemCluster(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isDone());
    verify(move, times(1)).waitForSystemClusterCreationStep(shadowClusterJobMock);
    verify(move, times(1)).getClusterUniqueId(GROUP_ID, systemClusterName);
    verify(shadowClusterJobMock, times(1)).markSystemClusterCreated(systemClusterUniqueId);
    verify(systemClusterJobSvcMock, times(1)).saveSystemClusterJob(shadowClusterJobMock);
    verify(shadowClusterSvcMock, times(1))
        .updateShadowClusterDetails(SHADOW_CLUSTER_ID, systemClusterUniqueId, systemClusterName);
  }

  // --- State Management Tests ---

  @Test
  public void testCreateSystemProject_WithStateUpdates() throws Exception {
    final com.xgen.cloud.nds.project._public.model.NDSGroup ndsGroup =
        mock(com.xgen.cloud.nds.project._public.model.NDSGroup.class);
    when(ndsGroup.getGroupId()).thenReturn(GROUP_ID);

    final ProvisionShadowClusterMove move = getMove();
    when(systemClusterJobSvcMock.createSystemProject(
            any(Logger.class), eq(shadowClusterJobMock), eq(false)))
        .thenReturn(ndsGroup);
    doNothing().when(shadowClusterJobMock).markSystemProjectCreated(GROUP_ID);
    doNothing().when(systemClusterJobSvcMock).saveSystemClusterJob(shadowClusterJobMock);
    doNothing().when(shadowClusterSvcMock).updateShadowClusterGroupId(SHADOW_CLUSTER_ID, GROUP_ID);

    final Result<Result.NoData> result =
        move.createSystemProject(shadowClusterJobMock, SHADOW_CLUSTER_ID);

    assertTrue(result.getStatus().isDone());
    verify(shadowClusterJobMock, times(1)).markSystemProjectCreated(GROUP_ID);
    verify(systemClusterJobSvcMock, times(1)).saveSystemClusterJob(shadowClusterJobMock);
    verify(shadowClusterSvcMock, times(1)).updateShadowClusterGroupId(SHADOW_CLUSTER_ID, GROUP_ID);
    verify(shadowClusterJobSvcMock, times(1))
        .updateShadowClusterJobStatus(
            shadowClusterJobMock, Status.CREATED_SYSTEM_PROJECT, "Created system project");
  }

  // --- updateExposureStatusIfAllClustersCreated Tests ---

  @Test
  public void
      testUpdateExposureStatusIfAllClustersCreated_WhenAllClustersProvisioned_UpdatesStatusToProvisioned()
          throws SvcException {
    final ProvisionShadowClusterMove move = getMove();

    // Setup exposure with exactly 2 expected clusters across all permutations
    // This matches the 2 provisioned clusters from current permutation
    final Exposure exposureMock = createStrictMock(Exposure.class);
    final ObjectId exposureJobId = new ObjectId();
    when(exposureMock.getCurrentJobId()).thenReturn(exposureJobId);
    when(shadowClusterSvcMock.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(exposureMock));
    when(shadowClusterSvcMock.areAllClustersFromExposureInStatus(
            EXPOSURE_ID, ShadowCluster.Status.PROVISIONED))
        .thenReturn(true);

    // Mock exposure job retrieval and update
    final ShadowClusterExposureJob exposureJobMock = mock(ShadowClusterExposureJob.class);
    when(shadowClusterJobSvcMock.getShadowClusterExposureJob(eq(exposureJobId), any(Logger.class)))
        .thenReturn(exposureJobMock);

    move.updateExposureStatusIfAllClustersCreated(shadowClusterJobMock);

    // Validate exposure job status update through the service method
    verify(shadowClusterJobSvcMock, times(1))
        .updateShadowClusterExposureJobStatus(
            exposureJobMock,
            Exposure.Status.PROVISIONED,
            "Finished provisioning all shadow clusters");
  }

  @Test
  public void testUpdateExposureStatusIfAllClustersCreated_WhenExposureNotFound_ThrowsException()
      throws SvcException {
    final ProvisionShadowClusterMove move = getMove();

    // Setup: clusters exist but exposure is not found
    final ShadowCluster provisionedClusterMock = createStrictMock(ShadowCluster.class);
    final ObjectId provisionedClusterId = new ObjectId();
    when(provisionedClusterMock.getId()).thenReturn(provisionedClusterId);
    when(shadowClusterSvcMock.getShadowClustersForExposure(EXPOSURE_ID))
        .thenReturn(List.of(provisionedClusterMock));

    // Mock the bulk status retrieval
    when(shadowClusterSvcMock.getBulkShadowClusterStatusById(List.of(provisionedClusterId)))
        .thenReturn(
            List.of(
                new ShadowClusterStatusInfo(
                    provisionedClusterId, ShadowCluster.Status.PROVISIONED, "Provisioned")));

    when(shadowClusterSvcMock.getExposure(EXPOSURE_ID)).thenReturn(Optional.empty());

    // Verify that an exception is thrown when exposure is not found
    final SvcException exception =
        assertThrows(
            SvcException.class,
            () -> move.updateExposureStatusIfAllClustersCreated(shadowClusterJobMock));

    assertEquals("Cannot find resource Exposure not found.", exception.getMessage());
    verify(shadowClusterJobSvcMock, never()).getShadowClusterExposureJobById(any(ObjectId.class));
    verify(shadowClusterJobSvcMock, never())
        .saveShadowClusterExposureJob(any(ShadowClusterExposureJob.class));
  }

  @Test
  public void
      testUpdateExposureStatusIfAllClustersCreated_WhenMixedStatusClusters_DoesNotUpdateStatus()
          throws SvcException {
    final ProvisionShadowClusterMove move = getMove();

    // Setup: clusters with mixed status - one provisioned, one still provisioning
    final ShadowCluster provisionedClusterMock = createStrictMock(ShadowCluster.class);
    final ShadowCluster provisioningClusterMock = createStrictMock(ShadowCluster.class);
    final ObjectId provisionedClusterId = new ObjectId();
    final ObjectId provisioningClusterId = new ObjectId();
    when(provisionedClusterMock.getId()).thenReturn(provisionedClusterId);
    when(provisioningClusterMock.getId()).thenReturn(provisioningClusterId);
    when(shadowClusterSvcMock.getShadowClustersForExposure(EXPOSURE_ID))
        .thenReturn(List.of(provisionedClusterMock, provisioningClusterMock));

    // Mock the bulk status retrieval - one PROVISIONED, one PROVISIONING
    when(shadowClusterSvcMock.getBulkShadowClusterStatusById(
            List.of(provisionedClusterId, provisioningClusterId)))
        .thenReturn(
            List.of(
                new ShadowClusterStatusInfo(
                    provisionedClusterId, ShadowCluster.Status.PROVISIONED, "Provisioned"),
                new ShadowClusterStatusInfo(
                    provisioningClusterId, ShadowCluster.Status.PROVISIONING, "Provisioning")));

    // Setup exposure expecting 2 clusters but only 1 is provisioned
    final Exposure exposureMock = createStrictMock(Exposure.class);
    final Permutation permutationMock = createStrictMock(Permutation.class);
    when(permutationMock.getShadowClusters()).thenReturn(List.of(new ObjectId(), new ObjectId()));
    when(exposureMock.getPermutations()).thenReturn(List.of(permutationMock));
    when(shadowClusterSvcMock.getExposure(EXPOSURE_ID)).thenReturn(Optional.of(exposureMock));

    move.updateExposureStatusIfAllClustersCreated(shadowClusterJobMock);

    // Verify no status update occurs since not all clusters are provisioned
    verify(shadowClusterJobSvcMock, never()).getShadowClusterExposureJobById(any(ObjectId.class));
    verify(shadowClusterJobSvcMock, never())
        .saveShadowClusterExposureJob(any(ShadowClusterExposureJob.class));
  }

  // --- Helper Method ---

  private ProvisionShadowClusterMove getMove() {
    ProvisionShadowClusterMove move =
        new ProvisionShadowClusterMove(
            planContextMock,
            SYSTEM_CLUSTER_JOB_ID,
            PERMUTATION_ID,
            systemClusterJobSvcMock,
            shadowClusterJobSvcMock,
            shadowClusterSvcMock,
            ndsCloudProviderContainerSvcMock,
            ndsGroupDaoMock,
            clusterDescriptionDaoMock,
            ndsClusterSvcMock,
            cpsSvcMock,
            waitForShadowClusterCreationStepFactoryMock);
    ProvisionShadowClusterMove spiedMove = spy(move);
    doReturn(stateMock).when(spiedMove).getState();
    when(stateMock.getValue(ProvisionShadowClusterMove.StateFields.SHADOW_CLUSTER_ID))
        .thenReturn(Optional.empty());
    return spiedMove;
  }
}
