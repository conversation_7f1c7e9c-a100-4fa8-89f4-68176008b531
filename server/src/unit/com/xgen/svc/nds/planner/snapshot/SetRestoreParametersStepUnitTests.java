package com.xgen.svc.nds.planner.snapshot;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.cps.core._public.config.CpsAppSettings;
import com.xgen.cloud.cps.core._public.svc.CpsFeatureFlagSvc;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.DirectAttachReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.RestoreTargetCluster;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.DirectAttachPreWarmStrategy;
import com.xgen.cloud.deployment._public.model.MongoDbVersion;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.deployment._public.model.ProcessType;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.module.common.planner.model.Result;
import com.xgen.module.common.planner.model.Step;
import com.xgen.module.common.planner.model.Step.State;
import com.xgen.svc.mms.util.UnitTestUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.planner.BackupDependenciesProvider;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.planner.snapshot.SetRestoreParametersStep.Data;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import com.xgen.testlib.junit5.extensions.testname.TestName;
import com.xgen.testlib.junit5.extensions.testname.TestNameExtension;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(CloudProviderExtension.class)
@ExtendWith(TestNameExtension.class)
public class SetRestoreParametersStepUnitTests {
  private static final String TARGET_CLUSTER_NAME = "TargetCluster";
  private static final String SOURCE_CLUSTER_NAME = "SourceCluster";
  private static final String SOURCE_CLUSTER_DEPLOYMENT_NAME = "SourceClusterDeploymentName";
  private static final String HOSTNAME = "targetcluster-shard-00-00-2hcwi.mmscloudtest.com";
  private static final String HOSTNAME2 = "targetcluster-shard-00-01-2hcwi.mmscloudtest.com";
  private static final String VERIFICATION_KEY = "randomKey";
  private static final ObjectId TARGET_PROJECT_ID = new ObjectId();
  private static final ObjectId RESTORE_JOB1_ID = new ObjectId();
  private static final ObjectId RESTORE_JOB2_ID = new ObjectId();
  private static final Integer DESIRED_BACKUP_RS_VERSION = 2;
  private static final int AWS_PRE_WARM_BLOCK_SIZE = 1 << 19; // 512 KiB
  private static final int AZURE_PRE_WARM_BLOCK_SIZE = 1 << 12; // 4 KiB
  private static final int DEFAULT_RESTORE_REPL_WRITER_THREAD_COUNT = 16;
  private static final Long DESIRED_BACKUP_RESTORE_ELECTION_ID = 5L;

  public TestName _testName;

  @Test
  public void testAddParams() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(clusterDescription.getDeploymentClusterName()).thenReturn(TARGET_CLUSTER_NAME);

    final NDSPlanContext context = mock(NDSPlanContext.class);
    final Step.State state = mock(Step.State.class);

    final ObjectId snapshotId1 = new ObjectId();
    final BackupSnapshot snapshot1 = mock(BackupSnapshot.class);

    final DirectAttachReplicaSetBackupRestoreJob restoreJob1 =
        mock(DirectAttachReplicaSetBackupRestoreJob.class);
    final RestoreTargetCluster target1 = mock(RestoreTargetCluster.class);
    final DirectAttachReplicaSetBackupRestoreJob.AttachStatus status1 =
        mock(DirectAttachReplicaSetBackupRestoreJob.AttachStatus.class);
    when(status1.getHostname()).thenReturn(HOSTNAME);
    when(restoreJob1.getAttachStatuses()).thenReturn(Arrays.asList(status1));
    when(target1.getTargetProjectId()).thenReturn(TARGET_PROJECT_ID);
    when(restoreJob1.getTarget()).thenReturn(target1);
    when(restoreJob1.getId()).thenReturn(RESTORE_JOB1_ID);
    when(restoreJob1.getClusterName()).thenReturn(SOURCE_CLUSTER_NAME);
    when(restoreJob1.getVerificationKey()).thenReturn(VERIFICATION_KEY);
    when(restoreJob1.getTarget().getSystemUsersUUID()).thenReturn("UUID");
    when(restoreJob1.getTarget().getSystemRolesUUID()).thenReturn("UUID");
    when(restoreJob1.getSnapshotId()).thenReturn(snapshotId1);
    when(restoreJob1.getCloudProvider()).thenReturn(CloudProvider.AWS);
    when(restoreJob1.getHostnames()).thenReturn(List.of(HOSTNAME));

    final DirectAttachReplicaSetBackupRestoreJob restoreJob2 =
        mock(DirectAttachReplicaSetBackupRestoreJob.class);
    final DirectAttachReplicaSetBackupRestoreJob.AttachStatus status2 =
        mock(DirectAttachReplicaSetBackupRestoreJob.AttachStatus.class);
    when(status2.getHostname()).thenReturn(HOSTNAME2);
    final RestoreTargetCluster target2 = mock(RestoreTargetCluster.class);
    when(restoreJob2.getAttachStatuses()).thenReturn(Arrays.asList(status2));
    when(target2.getTargetProjectId()).thenReturn(TARGET_PROJECT_ID);
    when(restoreJob2.getTarget()).thenReturn(target2);
    when(restoreJob2.getId()).thenReturn(RESTORE_JOB2_ID);
    when(restoreJob2.getClusterName()).thenReturn(SOURCE_CLUSTER_NAME);
    when(restoreJob2.getVerificationKey()).thenReturn(VERIFICATION_KEY);
    when(restoreJob2.getTarget().getSystemUsersUUID()).thenReturn("UUID");
    when(restoreJob2.getTarget().getSystemRolesUUID()).thenReturn("UUID");
    when(restoreJob2.getCloudProvider()).thenReturn(CloudProvider.GCP);
    when(restoreJob2.getHostnames()).thenReturn(List.of(HOSTNAME2));

    // create automation config
    final AutomationConfig automationConfig = generateAutomationConfig();

    // automationConfigSvc
    final AutomationConfigPublishingSvc automationConfigSvc =
        mock(AutomationConfigPublishingSvc.class);

    when(automationConfigSvc.findPublishedOrEmpty(TARGET_PROJECT_ID)).thenReturn(automationConfig);

    final BackupSnapshotDao backupSnapshotDao = mock(BackupSnapshotDao.class);
    when(snapshot1.isWtcSnapshot()).thenReturn(false);
    when(snapshot1.getOplogCollectionFileName()).thenReturn("collection-10-**********");
    when(backupSnapshotDao.findById(any())).thenReturn(Optional.of(snapshot1));

    final BackupDependenciesProvider backupDependenciesProvider =
        mock(BackupDependenciesProvider.class);
    final CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmGlobsForAWS()).thenReturn("");
    when(appSettings.getDirectAttachPreWarmGlobsForAzure()).thenReturn("");
    when(appSettings.getDirectAttachPreWarmStrategy()).thenReturn("defaultPreWarm");
    when(appSettings.getDirectAttachPreWarmConcurrency()).thenReturn(16);
    when(backupDependenciesProvider.getCpsAppSettings()).thenReturn(appSettings);
    when(backupDependenciesProvider.getAutomationConfigSvc()).thenReturn(automationConfigSvc);
    when(backupDependenciesProvider.getBackupSnapshotDao()).thenReturn(backupSnapshotDao);

    final CpsSvc cpsSvc = mock(CpsSvc.class);
    final CpsFeatureFlagSvc cpsFeatureFlagSvc = mock(CpsFeatureFlagSvc.class);
    when(cpsFeatureFlagSvc.isAwsPrewarmStrategiesEnabled(any(ObjectId.class))).thenReturn(false);
    when(cpsFeatureFlagSvc.isAzurePrewarmStrategiesEnabled(any(ObjectId.class))).thenReturn(false);
    when(backupDependenciesProvider.getCpsFeatureFlagSvc()).thenReturn(cpsFeatureFlagSvc);
    when(backupDependenciesProvider.getCpsSvc()).thenReturn(cpsSvc);

    final SetRestoreParametersStep step =
        spy(
            new SetRestoreParametersStep(
                context,
                state,
                Arrays.asList(restoreJob1, restoreJob2),
                clusterDescription,
                restoreJob1,
                backupDependenciesProvider,
                mock(RestoreStepUtils.class)));

    final HostCluster hostCluster = mock(HostCluster.class);
    when(hostCluster.getBackupDesiredRsVersion()).thenReturn(DESIRED_BACKUP_RS_VERSION);
    when(hostCluster.getBackupDesiredElectionTerm()).thenReturn(DESIRED_BACKUP_RESTORE_ELECTION_ID);
    doReturn(hostCluster).when(step).getHostCluster(any(), any());

    final ClusterDescription sourceClusterDescription = mock(ClusterDescription.class);
    doReturn(SOURCE_CLUSTER_DEPLOYMENT_NAME)
        .when(sourceClusterDescription)
        .getDeploymentClusterName();

    doReturn(sourceClusterDescription).when(step).getSourceClusterDescription(any(), any());

    doReturn(DEFAULT_RESTORE_REPL_WRITER_THREAD_COUNT)
        .when(step)
        .getDirectAttachRestoreReplWriterThreadCount(any(), any(), any(), any());

    final Result<Data> result = step.performInternal();
    final Optional<AutomationConfig> updatedConfigOpt = result.getData().getAutomationConfig();

    assertTrue(updatedConfigOpt.isPresent());
    final AutomationConfig updatedConfig = updatedConfigOpt.get();
    final Deployment deployment = updatedConfig.getDeployment();
    final List<Process> processes = deployment.getProcesses();
    assertEquals(3, processes.size());

    verifyProcessHasRestoreParameters(processes, HOSTNAME, ProcessType.MONGOD, RESTORE_JOB1_ID);
    verifyProcessHasRestoreParameters(processes, HOSTNAME2, ProcessType.MONGOD, RESTORE_JOB2_ID);
    verifyProcessDoesNotHaveRestoreParameters(processes, HOSTNAME, ProcessType.MONGOS);

    verify(step).setInitialMongoVersionForSuccessiveUpgradeIfNeeded(automationConfig);
  }

  @Test
  public void testDirectAttachRestoreStrategyParsing() {
    assertEquals(
        DirectAttachPreWarmStrategy.defaultPreWarm, DirectAttachPreWarmStrategy.fromString(null));
    assertEquals(
        DirectAttachPreWarmStrategy.defaultPreWarm, DirectAttachPreWarmStrategy.fromString(""));
    assertEquals(
        DirectAttachPreWarmStrategy.defaultPreWarm,
        DirectAttachPreWarmStrategy.fromString("incorrectValue"));
    assertEquals(
        DirectAttachPreWarmStrategy.defaultPreWarm,
        DirectAttachPreWarmStrategy.fromString("defaultPreWarm"));
    assertEquals(
        DirectAttachPreWarmStrategy.journalAndOplogFirstPreWarm,
        DirectAttachPreWarmStrategy.fromString("journalAndOplogFirstPreWarm"));
  }

  private AutomationConfig generateAutomationConfig() {
    final AutomationConfig automationConfig = new AutomationConfig();

    // Processes
    final ArrayList<Process> processes = new ArrayList<>();
    final Process mongod = getProcess(HOSTNAME, ProcessType.MONGOD);
    final Process mongod2 = getProcess(HOSTNAME2, ProcessType.MONGOD);
    final Process mongos = getProcess(HOSTNAME, ProcessType.MONGOS);
    processes.add(mongod);
    processes.add(mongod2);
    processes.add(mongos);

    final Deployment deployment = new Deployment();
    deployment.setProcesses(processes);
    automationConfig.setDeployment(deployment);

    return automationConfig;
  }

  private Process getProcess(final String hostname, final ProcessType processType) {
    final Process process = new Process();
    process.setCluster(TARGET_CLUSTER_NAME);
    process.setHostname(hostname);
    process.setProcessType(processType);
    return process;
  }

  private void verifyProcessHasRestoreParameters(
      final List<Process> processes,
      final String hostname,
      final ProcessType type,
      final ObjectId restoreJobId) {
    final Process process =
        processes.stream()
            .filter(p -> p.getHostname().equals(hostname) && p.getProcessType() == type)
            .findFirst()
            .get();

    assertNotNull(process.getDirectAttachVerificationKey());
    assertEquals(restoreJobId.toHexString(), process.getBackupRestoreJobId());
    assertNotNull(process.getBackupRestoreSystemUsersUUID());
    assertNotNull(process.getBackupRestoreSystemRolesUUID());
    assertEquals(SOURCE_CLUSTER_DEPLOYMENT_NAME, process.getDirectAttachSourceClusterName());
    assertEquals(
        DirectAttachPreWarmStrategy.defaultPreWarm, process.getDirectAttachPreWarmStrategy());
    assertEquals("", process.getDirectAttachPreWarmGlobs());
    assertEquals(16, process.getDirectAttachPreWarmConcurrency());
    assertEquals(1 << 19, process.getDirectAttachPreWarmBlockSize());
    assertEquals(1, process.getDirectAttachPreWarmBlockReadSize());
    assertEquals(16, process.getDirectAttachReplWriterThreadCount());
    assertFalse(process.getDirectAttachPreWarmShouldUseGoBinary());
    assertFalse(process.getDirectAttachPreWarmShouldWarmEmptyBlocks());
    assertTrue(process.getDirectAttachPreWarmShouldSkipNonPriority());
    assertFalse(process.getDirectAttachShouldFilterByFilelists());
    assertEquals(DESIRED_BACKUP_RS_VERSION, process.getBackupRestoreRsVersion());
    assertEquals(DESIRED_BACKUP_RESTORE_ELECTION_ID, process.getBackupRestoreElectionTerm());
  }

  private void verifyProcessDoesNotHaveRestoreParameters(
      final List<Process> processes, final String hostname, final ProcessType type) {
    final Process process =
        processes.stream()
            .filter(p -> p.getHostname().equals(hostname) && p.getProcessType() == type)
            .findFirst()
            .get();

    assertNull(process.getDirectAttachVerificationKey());
    assertNull(process.getBackupRestoreJobId());
    assertNull(process.getBackupRestoreSystemUsersUUID());
    assertNull(process.getBackupRestoreSystemRolesUUID());
    assertNull(process.getDirectAttachSourceClusterName());
    assertNull(process.getDirectAttachPreWarmStrategy());
    assertNull(process.getDirectAttachPreWarmGlobs());
    assertNull(process.getDirectAttachPreWarmConcurrency());
    assertNull(process.getDirectAttachPreWarmBlockSize());
    assertNull(process.getDirectAttachPreWarmBlockReadSize());
    assertNull(process.getDirectAttachReplWriterThreadCount());
    assertNull(process.getDirectAttachPreWarmShouldUseGoBinary());
    assertNull(process.getDirectAttachPreWarmShouldSkipNonPriority());
    assertNull(process.getDirectAttachPreWarmShouldWarmEmptyBlocks());
    assertNull(process.getOplogCollectionFileName());
  }

  @Test
  public void testSetInitialMongoVersionForSuccessiveUpgradeIfNeeded() {
    final ClusterDescription cd = spy(NDSModelTestFactory.getClusterDescription(CloudProvider.AWS));

    final CpsSvc cpsSvc = mock(CpsSvc.class);
    final BackupSnapshotDao backupSnapshotDao = mock(BackupSnapshotDao.class);
    final RestoreStepUtils restoreStepUtils = mock(RestoreStepUtils.class);

    final ObjectId snapshotId1 = new ObjectId();

    final DirectAttachReplicaSetBackupRestoreJob restoreJob1 =
        mock(DirectAttachReplicaSetBackupRestoreJob.class);
    when(restoreJob1.getSnapshotId()).thenReturn(snapshotId1);

    final DirectAttachReplicaSetBackupRestoreJob restoreJob2 =
        mock(DirectAttachReplicaSetBackupRestoreJob.class);

    final SetRestoreParametersStep step =
        spy(
            new SetRestoreParametersStep(
                mock(PlanContext.class),
                mock(State.class),
                Arrays.asList(restoreJob1, restoreJob2),
                cd,
                restoreJob1,
                UnitTestUtils.create(BackupDependenciesProvider.class)
                    .withArgs(backupSnapshotDao, cpsSvc),
                restoreStepUtils));

    final BackupSnapshot snapshot = mock(BackupSnapshot.class);
    doReturn(Optional.of(snapshot)).when(backupSnapshotDao).findById(snapshotId1);

    final AutomationConfig automationConfig = mock(AutomationConfig.class);

    {
      // successive upgrade cannot be performed
      doReturn(false).when(cpsSvc).canPerformSuccessiveUpgrade(snapshot, cd);

      step.setInitialMongoVersionForSuccessiveUpgradeIfNeeded(automationConfig);
      verify(restoreStepUtils, never())
          .setProcessMdbVersion(any(), any(), anyString(), anyString(), eq(true));
    }
    {
      // successive upgrade can be performed
      final Version snapshotVersion = new Version("5.1.0");
      doReturn(snapshotVersion).when(snapshot).getMongoDbVersion();
      doReturn(true).when(cpsSvc).canPerformSuccessiveUpgrade(snapshot, cd);
      doReturn(
              Pair.of(
                  List.of(
                      new MongoDbVersion("5.1.0-ent", null), new MongoDbVersion("5.2.1-ent", null)),
                  List.of("5.1", "5.2")))
          .when(restoreStepUtils)
          .getSuccessiveUpgradePaths(snapshotVersion, cd);

      step.setInitialMongoVersionForSuccessiveUpgradeIfNeeded(automationConfig);
      verify(restoreStepUtils, times(1))
          .setProcessMdbVersion(automationConfig, cd, "5.1.0-ent", "5.1", true);
    }
  }

  @Test
  public void testGetDirectAttachPreWarmGlobs_whenAppSettingsNull_thenReturnEmptyString() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();

    String result =
        step.getDirectAttachPreWarmGlobs(
            null, CloudProvider.AWS, TARGET_PROJECT_ID, Optional.empty());

    assertEquals("", result);
  }

  @Test
  public void testGetDirectAttachPreWarmGlobs_whenCloudProviderGCP_thenReturnEmptyString() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);

    String result =
        step.getDirectAttachPreWarmGlobs(
            appSettings, CloudProvider.GCP, TARGET_PROJECT_ID, Optional.empty());

    assertEquals("", result);
  }

  @Test
  public void testGetDirectAttachPreWarmGlobs_whenAWSGlobsListEmpty_thenReturnEmptyString() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmGlobsForAWS()).thenReturn("");

    String result =
        step.getDirectAttachPreWarmGlobs(
            appSettings, CloudProvider.AWS, TARGET_PROJECT_ID, Optional.empty());

    assertEquals("", result);
  }

  @Test
  public void testGetDirectAttachPreWarmGlobs_whenAzureGlobsListEmpty_thenReturnEmptyString() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmGlobsForAzure()).thenReturn("");

    String result =
        step.getDirectAttachPreWarmGlobs(
            appSettings, CloudProvider.AZURE, TARGET_PROJECT_ID, Optional.empty());

    assertEquals("", result);
  }

  @Test
  public void testGetDirectAttachPreWarmGlobs_whenAWSGlobsListValid_thenReturnGlobsString() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmGlobsForAWS()).thenReturn("journal/*");

    String result =
        step.getDirectAttachPreWarmGlobs(
            appSettings, CloudProvider.AWS, TARGET_PROJECT_ID, Optional.empty());

    assertEquals("journal/*", result);
  }

  @Test
  public void testGetDirectAttachPreWarmGlobs_whenAzureGlobsListValid_thenReturnGlobsString() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmGlobsForAzure()).thenReturn("journal/*");

    String result =
        step.getDirectAttachPreWarmGlobs(
            appSettings, CloudProvider.AZURE, TARGET_PROJECT_ID, Optional.empty());

    assertEquals("journal/*", result);
  }

  @Test
  public void
      testGetDirectAttachPreWarmGlobs_whenAWSGlobsListContainsOplogButSnapshotMissingFileName_thenReturnGlobsWithoutOplogCollectionFileName() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmGlobsForAWS()).thenReturn("journal/*,OPLOG_COLLECTION");

    String result =
        step.getDirectAttachPreWarmGlobs(
            appSettings, CloudProvider.AWS, TARGET_PROJECT_ID, Optional.empty());

    assertEquals("journal/*", result);
  }

  @Test
  public void
      testGetDirectAttachPreWarmGlobs_whenAzureGlobsListContainsOplogButSnapshotMissingFileName_thenReturnGlobsWithoutOplogCollectionFileName() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmGlobsForAzure())
        .thenReturn("journal/*,OPLOG_COLLECTION");

    String result =
        step.getDirectAttachPreWarmGlobs(
            appSettings, CloudProvider.AZURE, TARGET_PROJECT_ID, Optional.empty());

    assertEquals("journal/*", result);
  }

  @Test
  public void
      testGetDirectAttachPreWarmGlobs_whenAWSGlobsListContainsOplogAndSnapshotContainsFileName_thenReturnGlobsWithOplogCollectionFileName() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmGlobsForAWS())
        .thenReturn("journal/*,WiredTigerHS.wt,OPLOG_COLLECTION");

    String result =
        step.getDirectAttachPreWarmGlobs(
            appSettings,
            CloudProvider.AWS,
            TARGET_PROJECT_ID,
            Optional.of("collection-**********"));

    assertEquals("journal/*,WiredTigerHS.wt,collection-**********.wt", result);
  }

  @Test
  public void
      testGetDirectAttachPreWarmGlobs_whenAzureGlobsListContainsOplogAndSnapshotContainsFileName_thenReturnGlobsWithOplogCollectionFileName() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmGlobsForAzure())
        .thenReturn("journal/*,WiredTigerHS.wt,OPLOG_COLLECTION");

    String result =
        step.getDirectAttachPreWarmGlobs(
            appSettings,
            CloudProvider.AZURE,
            TARGET_PROJECT_ID,
            Optional.of("collection-**********"));

    assertEquals("journal/*,WiredTigerHS.wt,collection-**********.wt", result);
  }

  @Test
  public void
      testGetDirectAttachPreWarmGlobs_whenAWSGlobsListOverridden_thenReturnOverriddenGlobsString() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmGlobsOverridesForAWS())
        .thenReturn(Map.of(TARGET_PROJECT_ID.toString(), "journal/*"));
    when(appSettings.getDirectAttachPreWarmGlobsForAWS()).thenReturn("journal/*,OPLOG_COLLECTION");

    String result =
        step.getDirectAttachPreWarmGlobs(
            appSettings, CloudProvider.AWS, TARGET_PROJECT_ID, Optional.empty());

    assertEquals("journal/*", result);
  }

  @Test
  public void
      testGetDirectAttachPreWarmGlobs_whenAzureGlobsListOverridden_thenReturnOverriddenGlobsString() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmGlobsOverridesForAzure())
        .thenReturn(Map.of(TARGET_PROJECT_ID.toString(), "journal/*"));
    when(appSettings.getDirectAttachPreWarmGlobsForAzure())
        .thenReturn("journal/*,OPLOG_COLLECTION");

    String result =
        step.getDirectAttachPreWarmGlobs(
            appSettings, CloudProvider.AZURE, TARGET_PROJECT_ID, Optional.empty());

    assertEquals("journal/*", result);
  }

  @Test
  public void testGetDirectAttachPreWarmStrategy_returnsDefaultPreWarm_whenAppSettingsIsNull() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();

    DirectAttachPreWarmStrategy result =
        step.getDirectAttachPreWarmStrategy(
            null, CloudProvider.AWS, new ObjectId(), mock(CpsFeatureFlagSvc.class));

    assertEquals(DirectAttachPreWarmStrategy.defaultPreWarm, result);
  }

  @Test
  public void testGetDirectAttachPreWarmStrategy_returnsSkipPreWarm_forGcpWhenSkipEnabled() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.isCpsSkipGcpPrewarmEnabled()).thenReturn(true);
    when(appSettings.getDirectAttachPreWarmStrategy()).thenReturn("journalAndOplogFirstPreWarm");

    DirectAttachPreWarmStrategy result =
        step.getDirectAttachPreWarmStrategy(
            appSettings, CloudProvider.GCP, new ObjectId(), mock(CpsFeatureFlagSvc.class));

    assertEquals(DirectAttachPreWarmStrategy.skipPreWarm, result);
  }

  @Test
  public void testGetDirectAttachPreWarmStrategy_returnsDefaultPreWarm_forGcpWhenSkipDisabled() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.isCpsSkipGcpPrewarmEnabled()).thenReturn(false);
    when(appSettings.getDirectAttachPreWarmStrategy()).thenReturn("journalAndOplogFirstPreWarm");

    DirectAttachPreWarmStrategy result =
        step.getDirectAttachPreWarmStrategy(
            appSettings, CloudProvider.GCP, new ObjectId(), mock(CpsFeatureFlagSvc.class));

    assertEquals(DirectAttachPreWarmStrategy.defaultPreWarm, result);
  }

  @Test
  public void testGetDirectAttachPreWarmStrategy_returnsDefaultPreWarm_forAzureWhenFfDisabled() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmStrategy()).thenReturn("journalAndOplogFirstPreWarm");
    CpsFeatureFlagSvc cpsFeatureFlagSvc = mock(CpsFeatureFlagSvc.class);
    when(cpsFeatureFlagSvc.isAzurePrewarmStrategiesEnabled(any(ObjectId.class))).thenReturn(false);

    DirectAttachPreWarmStrategy result =
        step.getDirectAttachPreWarmStrategy(
            appSettings, CloudProvider.AZURE, new ObjectId(), cpsFeatureFlagSvc);

    assertEquals(DirectAttachPreWarmStrategy.defaultPreWarm, result);
  }

  @Test
  public void
      testGetDirectAttachPreWarmStrategy_returnsJournalAndOplogFirstPreWarm_forAwsWhenFfEnabled() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmStrategy()).thenReturn("journalAndOplogFirstPreWarm");
    CpsFeatureFlagSvc cpsFeatureFlagSvc = mock(CpsFeatureFlagSvc.class);
    when(cpsFeatureFlagSvc.isAwsPrewarmStrategiesEnabled(any(ObjectId.class))).thenReturn(true);

    DirectAttachPreWarmStrategy result =
        step.getDirectAttachPreWarmStrategy(
            appSettings, CloudProvider.AWS, new ObjectId(), cpsFeatureFlagSvc);

    assertEquals(DirectAttachPreWarmStrategy.journalAndOplogFirstPreWarm, result);
  }

  @Test
  public void testGetDirectAttachPreWarmStrategy_returnsDefaultPreWarm_forInvalidStrategy() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmStrategy()).thenReturn("invalidStrategy");
    CpsFeatureFlagSvc cpsFeatureFlagSvc = mock(CpsFeatureFlagSvc.class);
    when(cpsFeatureFlagSvc.isAwsPrewarmStrategiesEnabled(any(ObjectId.class))).thenReturn(true);

    DirectAttachPreWarmStrategy result =
        step.getDirectAttachPreWarmStrategy(
            appSettings, CloudProvider.AWS, new ObjectId(), cpsFeatureFlagSvc);

    assertEquals(DirectAttachPreWarmStrategy.defaultPreWarm, result);
  }

  @Test
  public void testGetDirectAttachPreWarmConcurrency() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();

    int result = step.getDirectAttachPreWarmConcurrency(null);

    assertEquals(16, result);

    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    when(appSettings.getDirectAttachPreWarmConcurrency()).thenReturn(64);
    result = step.getDirectAttachPreWarmConcurrency(appSettings);

    assertEquals(64, result);
  }

  @Test
  public void testGetDirectAttachPreWarmBlockSize_whenCloudProviderAws_thenReturnsAwsDefault() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();

    int result = step.getDirectAttachPreWarmBlockSize(null, CloudProvider.AWS);

    assertEquals(AWS_PRE_WARM_BLOCK_SIZE, result);
  }

  @Test
  public void
      testGetDirectAttachPreWarmBlockSize_whenCloudProviderAzureAndAppSettingsNull_thenReturnsAzureDefault() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();

    int result = step.getDirectAttachPreWarmBlockSize(null, CloudProvider.AZURE);

    assertEquals(AZURE_PRE_WARM_BLOCK_SIZE, result);
  }

  @Test
  public void
      testGetDirectAttachPreWarmBlockSize_whenCloudProviderAzure_thenReturnsBlockSizeFromAppSettings() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    int testBlockSize = 8;
    when(appSettings.getDirectAttachPreWarmAzureBlockSize()).thenReturn(testBlockSize);

    int result = step.getDirectAttachPreWarmBlockSize(appSettings, CloudProvider.AZURE);

    assertEquals(testBlockSize, result);
  }

  @Test
  public void
      testGetDirectAttachPreWarmBlockReadSize_whenCloudProviderAzure_thenReturnsBlockReadSizeFromAppSettings() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    int testBlockReadSize = 4096;
    when(appSettings.getDirectAttachPreWarmAzureBlockReadSize()).thenReturn(testBlockReadSize);

    int result = step.getDirectAttachPreWarmBlockReadSize(appSettings, CloudProvider.AZURE);

    assertEquals(testBlockReadSize, result);
  }

  @Test
  public void
      testGetDirectAttachPreWarmBlockReadSize_whenCloudProviderAWS_thenReturnsDefaultBlockReadSize() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsAppSettings appSettings = mock(CpsAppSettings.class);

    int result = step.getDirectAttachPreWarmBlockReadSize(appSettings, CloudProvider.AWS);

    assertEquals(1, result);
    verifyNoInteractions(appSettings);
  }

  @Test
  public void
      testGetDirectAttachRestoreReplWriterThreadCount_whenFeatureFlagDisabled_thenReturnsDefaultValue() {
    final SetRestoreParametersStep step = getSetRestoreParametersStep();
    CpsFeatureFlagSvc featureFlagSvc = mock(CpsFeatureFlagSvc.class);
    CpsAppSettings appSettings = mock(CpsAppSettings.class);
    ObjectId projectId = new ObjectId();
    when(featureFlagSvc.isDirectAttachReplWriterThreadCountIncreaseEnabled(projectId))
        .thenReturn(false);

    int result =
        step.getDirectAttachRestoreReplWriterThreadCount(
            featureFlagSvc, appSettings, "hostname", projectId);

    assertEquals(DEFAULT_RESTORE_REPL_WRITER_THREAD_COUNT, result);
  }

  private static SetRestoreParametersStep getSetRestoreParametersStep() {
    return new SetRestoreParametersStep(
        mock(PlanContext.class),
        mock(State.class),
        List.of(),
        mock(ClusterDescription.class),
        mock(DirectAttachReplicaSetBackupRestoreJob.class),
        mock(BackupDependenciesProvider.class),
        mock(RestoreStepUtils.class));
  }
}
