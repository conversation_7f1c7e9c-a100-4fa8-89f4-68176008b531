load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    timeout = "long",
    srcs = glob(["*UnitTests.java"]),
    deny_warnings = True,
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/cps/restore",
        "//server/src/main/com/xgen/cloud/cps/restore/_private/dao",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cluster/common/context",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/shadowcluster/model",
        "//server/src/main/com/xgen/cloud/nds/shadowcluster/svc",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/test/com/xgen/testlib/junit5/extensions",
        "//server/src/unit/com/xgen/svc/nds/model",
        "@maven//:org_assertj_assertj_core",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_mockito_mockito_core",
    ],
)
