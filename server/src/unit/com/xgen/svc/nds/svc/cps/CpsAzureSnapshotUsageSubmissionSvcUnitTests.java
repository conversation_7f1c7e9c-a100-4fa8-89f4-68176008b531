package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.billingimport.snapshot._public.svc.SnapshotImportSvc;
import com.xgen.cloud.billingimport.snapshot._public.svc.model.ImportedSnapshotUsage;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.cps.billing._private.dao.CpsBillingPropertiesDao;
import com.xgen.cloud.cps.billing._private.dao.CpsOplogUsageSummaryDao;
import com.xgen.cloud.cps.billing._public.model.CpsBillingProperty;
import com.xgen.cloud.cps.billing._public.model.CpsOplogUsageSummary.FieldDefs;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.module.metering.common.exception.MeterSvcException;
import com.xgen.module.metering.common.model.MeterId;
import com.xgen.module.metering.common.model.MeterUsage;
import com.xgen.module.metering.common.model.internalusagedimensions.PitInternalUsageDimensions;
import com.xgen.module.metering.common.model.internalusagedimensions.SnapshotInternalUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.AzureUsageDimensions;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@MockitoSettings(strictness = Strictness.LENIENT)
public class CpsAzureSnapshotUsageSubmissionSvcUnitTests {

  @SuppressWarnings("unused")
  @Mock
  private AppSettings appSettings;

  @Mock private CpsBillingPropertiesDao cpsBillingPropertiesDao;
  @Mock private CpsOplogUsageSummaryDao cpsOplogUsageSummaryDao;
  @Mock private SnapshotImportSvc snapshotImportSvc;

  @Spy @InjectMocks private CpsAzureSnapshotUsageSubmissionSvc underTest;

  @BeforeEach
  public void setup() {
    final CpsBillingProperty collectionBillingProperty = mock(CpsBillingProperty.class);
    doReturn(Optional.of(collectionBillingProperty))
        .when(cpsBillingPropertiesDao)
        .findOneById(CpsBillingPropertiesDao.OPLOG_USAGE_COLLECTION);
    LocalDate collectionLastSuccessfulDate = LocalDate.parse("2023-03-21");
    doReturn(collectionLastSuccessfulDate).when(collectionBillingProperty).getLastSuccessfulDate();
  }

  @Test
  public void testSubmitSnapshotUsage_upToDate_NoOp() throws MeterSvcException {
    final CpsBillingProperty snapshotUsageBillingProperty = mock(CpsBillingProperty.class);
    doReturn(Optional.of(snapshotUsageBillingProperty))
        .when(cpsBillingPropertiesDao)
        .findOneById(CpsBillingPropertiesDao.AZURE_SNAPSHOT_USAGE_SUBMISSION);
    LocalDate submissionLastSuccessfulDate = LocalDate.parse("2023-03-21");
    doReturn(submissionLastSuccessfulDate)
        .when(snapshotUsageBillingProperty)
        .getLastSuccessfulDate();
    doReturn(true).when(underTest).isUsageSubmissionEnabled();

    underTest.submitSnapshotUsage();
    verify(underTest, times(0)).submitSnapshotUsageInternal(any(), any());
  }

  @Test
  public void testSubmitSnapshotUsage_noUnprocessedImport_NoOp() throws MeterSvcException {
    final CpsBillingProperty snapshotUsageBillingProperty = mock(CpsBillingProperty.class);
    doReturn(Optional.of(snapshotUsageBillingProperty))
        .when(cpsBillingPropertiesDao)
        .findOneById(CpsBillingPropertiesDao.AZURE_SNAPSHOT_USAGE_SUBMISSION);
    LocalDate submissionLastSuccessfulDate = LocalDate.parse("2023-03-20");
    doReturn(submissionLastSuccessfulDate)
        .when(snapshotUsageBillingProperty)
        .getLastSuccessfulDate();

    LocalDate lastImportSuccessfulDate = LocalDate.parse("2023-03-20");
    doReturn(lastImportSuccessfulDate).when(snapshotImportSvc).getLastSuccessfulImportDate();
    doReturn(true).when(underTest).isUsageSubmissionEnabled();

    underTest.submitSnapshotUsage();
    verify(underTest, times(0)).submitSnapshotUsageInternal(any(), any());
  }

  @Test
  public void testSubmitSnapshotUsage_multiple_days() throws MeterSvcException {
    final CpsBillingProperty snapshotUsageBillingProperty = mock(CpsBillingProperty.class);
    doReturn(Optional.of(snapshotUsageBillingProperty))
        .when(cpsBillingPropertiesDao)
        .findOneById(CpsBillingPropertiesDao.AZURE_SNAPSHOT_USAGE_SUBMISSION);
    LocalDate submissionLastSuccessfulDate = LocalDate.parse("2023-03-19");
    doReturn(submissionLastSuccessfulDate)
        .when(snapshotUsageBillingProperty)
        .getLastSuccessfulDate();

    LocalDate lastImportSuccessfulDate = LocalDate.parse("2023-03-21");
    doReturn(lastImportSuccessfulDate).when(snapshotImportSvc).getLastSuccessfulImportDate();

    final Set<ObjectId> groupIds = createGroupIds(1);
    doReturn(groupIds).when(snapshotImportSvc).getGroupIds(any());

    final List<LocalDate> datesToProcess =
        Arrays.asList(LocalDate.parse("2023-03-20"), LocalDate.parse("2023-03-21"));
    doNothing()
        .when(cpsBillingPropertiesDao)
        .upsertOneMajority(anyString(), argThat(datesToProcess::contains));

    doNothing().when(underTest).submitSnapshotUsageInternal(any(), any());

    // process 2 days, each with 1 batch
    doReturn(5).when(underTest).getNumberOfThreads();
    doReturn(true).when(underTest).isUsageSubmissionEnabled();
    underTest.submitSnapshotUsage();
    verify(underTest, times(2)).submitSnapshotUsageInternal(any(), any());
    verify(cpsBillingPropertiesDao, times(1))
        .upsertOneMajority(anyString(), eq(LocalDate.parse("2023-03-20")));
    verify(cpsBillingPropertiesDao, times(1))
        .upsertOneMajority(anyString(), eq(LocalDate.parse("2023-03-21")));
  }

  @Test
  public void testSubmitSnapshotUsage_multiple_batches() throws MeterSvcException {
    final CpsBillingProperty snapshotUsageBillingProperty = mock(CpsBillingProperty.class);
    doReturn(Optional.of(snapshotUsageBillingProperty))
        .when(cpsBillingPropertiesDao)
        .findOneById(CpsBillingPropertiesDao.AZURE_SNAPSHOT_USAGE_SUBMISSION);
    LocalDate submissionLastSuccessfulDate = LocalDate.parse("2023-03-19");
    doReturn(submissionLastSuccessfulDate)
        .when(snapshotUsageBillingProperty)
        .getLastSuccessfulDate();

    LocalDate lastImportSuccessfulDate = LocalDate.parse("2023-03-21");
    doReturn(lastImportSuccessfulDate).when(snapshotImportSvc).getLastSuccessfulImportDate();

    final Set<ObjectId> groupIds =
        createGroupIds(CpsAzureSnapshotUsageSubmissionSvc.GROUP_BATCH_SIZE * 3);
    doReturn(groupIds).when(snapshotImportSvc).getGroupIds(any());

    final List<LocalDate> datesToProcess =
        Arrays.asList(LocalDate.parse("2023-03-20"), LocalDate.parse("2023-03-21"));
    doNothing()
        .when(cpsBillingPropertiesDao)
        .upsertOneMajority(anyString(), argThat(datesToProcess::contains));

    doNothing().when(underTest).submitSnapshotUsageInternal(any(), any());

    // process 2 days, each with 3 batch
    doReturn(5).when(underTest).getNumberOfThreads();
    doReturn(true).when(underTest).isUsageSubmissionEnabled();
    underTest.submitSnapshotUsage();
    verify(underTest, times(6)).submitSnapshotUsageInternal(any(), any());
    verify(cpsBillingPropertiesDao, times(1))
        .upsertOneMajority(anyString(), eq(LocalDate.parse("2023-03-20")));
    verify(cpsBillingPropertiesDao, times(1))
        .upsertOneMajority(anyString(), eq(LocalDate.parse("2023-03-21")));
  }

  @Test
  public void testSubmitPitUsagesInternal_noData_noOp() throws MeterSvcException {
    final LocalDate importDate = LocalDate.parse("2023-03-21");
    final Set<ObjectId> groupIdSet = createGroupIds(5);
    final List<ObjectId> groupIdList = new ArrayList<>(groupIdSet);

    doReturn(new ArrayList<ImportedSnapshotUsage>())
        .when(snapshotImportSvc)
        .findAggregatedUsage(any(), any());

    underTest.submitSnapshotUsageInternal(importDate, groupIdList);

    verify(underTest, never()).submitMeterUsageWithRetry(anyList());
  }

  @Test
  public void testSubmitPitUsagesInternal() throws MeterSvcException {
    final LocalDate importDate = LocalDate.parse("2023-03-21");
    final ObjectId groupId1 = new ObjectId();
    final ObjectId groupId2 = new ObjectId();
    final ObjectId clusterId1 = new ObjectId();
    final ObjectId clusterId2 = new ObjectId();
    final RegionName regionName1 = AzureRegionName.US_EAST;
    final RegionName regionName2 = AzureRegionName.US_WEST;

    final List<ObjectId> groupIdList = Arrays.asList(groupId1, groupId2);

    ImportedSnapshotUsage importedUsage1 =
        ImportedSnapshotUsage.newBuilder()
            .usageDate(DateTimeUtils.dateOf(importDate))
            .groupId(groupId1)
            .clusterUniqueId(clusterId1)
            .regionName(regionName1)
            .build();
    ImportedSnapshotUsage importedUsage2 =
        ImportedSnapshotUsage.newBuilder()
            .usageDate(DateTimeUtils.dateOf(importDate))
            .groupId(groupId2)
            .clusterUniqueId(clusterId2)
            .regionName(regionName2)
            .build();

    doReturn(Arrays.asList(importedUsage1, importedUsage2))
        .when(snapshotImportSvc)
        .findAggregatedUsage(any(), any());

    final DBCursor clusterInfoCursor = mock(DBCursor.class);
    DBObject clusterInfo = new BasicDBObject();
    clusterInfo.put(FieldDefs.CLUSTER_UNIQUE_ID, clusterId1);
    clusterInfo.put(FieldDefs.REGION_NAME, regionName1.getValue());

    when(clusterInfoCursor.hasNext()).thenReturn(true, false);
    when(clusterInfoCursor.next()).thenReturn(clusterInfo);

    when(cpsOplogUsageSummaryDao.getPitClusterInfo(any(), any(), any()))
        .thenReturn(clusterInfoCursor);

    doNothing().when(underTest).submitMeterUsageWithRetry(any());

    underTest.submitSnapshotUsageInternal(importDate, groupIdList);
    verify(underTest, times(1))
        .submitMeterUsageWithRetry(argThat(pMeterUsages -> pMeterUsages.size() == 2));
  }

  @Test
  public void testToMeterUsage() {
    final LocalDate importDate = LocalDate.parse("2023-03-22");
    final Date usageDate1 = TimeUtils.fromISOString("2023-03-21");
    final Date usageDate2 = TimeUtils.fromISOString("2023-03-21");
    final ObjectId groupId1 = new ObjectId();
    final ObjectId groupId2 = new ObjectId();
    final ObjectId clusterId1 = new ObjectId();
    final ObjectId clusterId2 = new ObjectId();
    final RegionName regionName1 = AzureRegionName.US_EAST;
    final RegionName regionName2 = AzureRegionName.US_WEST;

    ImportedSnapshotUsage importedUsage1 =
        ImportedSnapshotUsage.newBuilder()
            .usageDate(usageDate1)
            .groupId(groupId1)
            .clusterUniqueId(clusterId1)
            .regionName(regionName1)
            .build();
    ImportedSnapshotUsage importedUsage2 =
        ImportedSnapshotUsage.newBuilder()
            .usageDate(usageDate2)
            .groupId(groupId2)
            .clusterUniqueId(clusterId2)
            .regionName(regionName2)
            .build();

    MeterUsage meterUsage1 = underTest.toMeterUsage(importedUsage1, true, importDate);
    MeterUsage meterUsage2 = underTest.toMeterUsage(importedUsage2, false, importDate);

    assertEquals(groupId1, meterUsage1.getGroupId());
    assertEquals(usageDate1, meterUsage1.getStartTime());
    assertEquals(
        regionName1,
        ((AzureUsageDimensions) meterUsage1.getUsageDimensions()).getAzureRegionName());
    assertEquals(MeterId.NDS_AZURE_PIT_RESTORE_STORAGE, meterUsage1.getMeterId());
    assertNotNull(meterUsage1.getInternalUsageDimensions());
    assertEquals(
        importDate,
        ((PitInternalUsageDimensions) meterUsage1.getInternalUsageDimensions())
            .getSourceReportDate());

    assertEquals(groupId2, meterUsage2.getGroupId());
    assertEquals(usageDate2, meterUsage2.getStartTime());
    assertEquals(
        regionName1,
        ((AzureUsageDimensions) meterUsage1.getUsageDimensions()).getAzureRegionName());
    assertEquals(MeterId.NDS_AZURE_BACKUP_SNAPSHOT_STORAGE, meterUsage2.getMeterId());
    assertEquals(
        importDate,
        ((SnapshotInternalUsageDimensions) meterUsage2.getInternalUsageDimensions())
            .getSourceReportDate());
  }

  private static Set<ObjectId> createGroupIds(final int count) {
    final Set<ObjectId> groupIds = new HashSet<>(count);
    for (int i = 0; i < count; i++) {
      groupIds.add(new ObjectId());
    }
    return groupIds;
  }
}
