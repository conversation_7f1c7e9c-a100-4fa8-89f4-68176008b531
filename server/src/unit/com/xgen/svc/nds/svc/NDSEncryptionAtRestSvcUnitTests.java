package com.xgen.svc.nds.svc;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.clearInvocations;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import com.amazonaws.services.kms.model.KeyMetadata;
import com.amazonaws.services.kms.model.KeyState;
import com.azure.resourcemanager.keyvault.models.Key;
import com.azure.resourcemanager.keyvault.models.Vault;
import com.azure.resourcemanager.network.fluent.models.PrivateEndpointInner;
import com.azure.resourcemanager.network.models.PrivateLinkServiceConnection;
import com.azure.resourcemanager.network.models.PrivateLinkServiceConnectionState;
import com.azure.security.keyvault.keys.models.KeyProperties;
import com.google.api.services.cloudkms.v1.model.CryptoKeyVersion;
import com.google.common.util.concurrent.SettableFuture;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.atm.core._public.model.AgentAudit;
import com.xgen.cloud.atm.core._public.svc.AutomationAgentAuditSvc;
import com.xgen.cloud.common.appsettings._public.model.AppEnv;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.aws._public.clients.AWSApiCredentialsProviderImplementations;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.jobqueue._public.model.AgentJob;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.ErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.GroupStatus;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.aws._public.model.AWSErrorCode;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.NDSAWSKMS;
import com.xgen.cloud.nds.aws._public.model.NDSAWSTempCredentials;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSKMSEARPrivateEndpoint;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureErrorCode;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.NDSAzureKeyVault;
import com.xgen.cloud.nds.azure._public.model.SupportedAzureEnvironment;
import com.xgen.cloud.nds.azure._public.model.error.AzureApiException;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzureKeyVaultEARPrivateEndpoint;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.CloudProviderPrivateEndpoint;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.gcp._public.model.GCPErrorCode;
import com.xgen.cloud.nds.gcp._public.model.NDSGoogleCloudKMS;
import com.xgen.cloud.nds.gcp._public.model.error.GCPApiException;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import com.xgen.cloud.nds.pointsofpresence._public.model.PointOfPresence;
import com.xgen.cloud.nds.pointsofpresence._public.svc.PointsOfPresenceSvc;
import com.xgen.cloud.nds.privatelink._public.model.KMSValidationJobResponse;
import com.xgen.cloud.nds.privatelink._public.svc.KMSValidationJobSubmissionSvc;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.EncryptionAtRestProvider;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest.FieldDefs;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.replicasethardware._public.ReplicaSetHardwareSvc;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.svc.mms.util.UnitTestUtils;
import com.xgen.svc.nds.azure.svc.AzureCloudProviderContainerSvc;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.NDSEncryptionAtRestSvc.ValidationPerformedState;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import java.time.Duration;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Stream;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(CloudProviderExtension.class)
public class NDSEncryptionAtRestSvcUnitTests {

  private MockedStatic<FeatureFlagSvc> featureFlagSvc;
  private static final List<AWSApiCredentialsProviderImplementations>
      DEFAULT_CREDENTIALS_PROVIDERS = List.of(AWSApiCredentialsProviderImplementations.STATIC);
  private static final List<AWSApiCredentialsProviderImplementations> WORKLOAD_IDENITTY_PROVIDERS =
      List.of(
          AWSApiCredentialsProviderImplementations.WORKLOAD_IDENTITY,
          AWSApiCredentialsProviderImplementations.STATIC);

  @BeforeEach
  public void setUp() {
    featureFlagSvc = mockStatic(FeatureFlagSvc.class);
  }

  @AfterEach
  public void tearDown() {
    featureFlagSvc.close();
  }

  @Test
  public void testValidateAWSKMSCredentials_NDSAWSKMS() throws SvcException {
    final NDSGroup ndsGroup = mock(NDSGroup.class);

    final NDSAWSKMS ndsAWSKMS = mock(NDSAWSKMS.class);
    doReturn(false).when(ndsAWSKMS).isEnabled();

    final String customerMasterKeyID = "cmk";
    doReturn(Optional.of(customerMasterKeyID)).when(ndsAWSKMS).getCustomerMasterKeyID();

    final AWSRegionName regionName = AWSRegionName.US_WEST_1;
    doReturn(Optional.of(regionName)).when(ndsAWSKMS).getRegion();

    final ObjectId roleId = new ObjectId();
    doReturn(Optional.of(roleId)).when(ndsAWSKMS).getRoleId();

    final GroupSvc groupSvc = mock(GroupSvc.class);

    final AppSettings appSettings = mock(AppSettings.class);

    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(UnitTestUtils.create(NDSEncryptionAtRestSvc.class).withArgs(groupSvc, appSettings));

    final ValidationPerformedState validationPerformedState = mock(ValidationPerformedState.class);

    // Test disabled
    encryptionAtRestSvc.validateAWSKMSCredentials(ndsGroup, ndsAWSKMS, null);
    verify(ndsAWSKMS, never()).validate(any(), any(), any());

    // Test model validation fails
    doReturn(true).when(ndsAWSKMS).isEnabled();
    doThrow(new SvcException(NDSErrorCode.INVALID_AWS_KMS_AUTH_CONFIG))
        .when(ndsAWSKMS)
        .validate(any(), any(), any());
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(ndsGroup, ndsAWSKMS, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_AWS_KMS_AUTH_CONFIG, pE.getErrorCode());
    }

    // Test model validation passes, roleId present - call the appropriate validateAwsKmsCredentials
    // method
    doNothing().when(ndsAWSKMS).validate(any(), any(), any());
    doNothing()
        .when(encryptionAtRestSvc)
        .validateAWSKMSCredentials(any(), any(), any(), any(), any());
    doNothing()
        .when(encryptionAtRestSvc)
        .validateAWSKMSCredentials(any(), any(), any(), any(), any(), any());
    encryptionAtRestSvc.validateAWSKMSCredentials(ndsGroup, ndsAWSKMS, validationPerformedState);
    verify(encryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(ndsGroup),
            eq(roleId),
            eq(customerMasterKeyID),
            eq(regionName),
            eq(validationPerformedState));
    verify(encryptionAtRestSvc, never())
        .validateAWSKMSCredentials(any(), any(), any(), any(), any(), any());
    verify(validationPerformedState, never()).setPerformedDate(any());

    // Test model validation passes, roleId not present, credentials present - call the appropriate
    // validateAwsKmsCredentials method
    doReturn(Optional.empty()).when(ndsAWSKMS).getRoleId();

    final String accessKeyID = "iamthekeymaster";
    final String secretAccessKey = "shhhhhdonttellasoul";
    doReturn(Optional.of(accessKeyID)).when(ndsAWSKMS).getAccessKeyID();
    doReturn(Optional.of(secretAccessKey)).when(ndsAWSKMS).getSecretAccessKey();
    encryptionAtRestSvc.validateAWSKMSCredentials(ndsGroup, ndsAWSKMS, validationPerformedState);
    verify(encryptionAtRestSvc).validateAWSKMSCredentials(any(), any(), any(), any(), any());
    verify(encryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(accessKeyID),
            eq(secretAccessKey),
            isNull(),
            eq(customerMasterKeyID),
            eq(regionName),
            eq(validationPerformedState));
  }

  @Test
  public void testValidateAWSKMSCredentials_Credentials() throws SvcException {
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(UnitTestUtils.create(NDSEncryptionAtRestSvc.class).withArgs(awsApiSvc));

    final String accessKeyID = "iamthekeymaster";
    final String secretAccessKey = "shhhhhdonttellasoul";
    final String sessionToken = "S3SS10NT0K3NS3SS10NT0K3NS3SS10NT0K3N";
    final String customerMasterKeyID = "cmk";
    final AWSRegionName regionName = AWSRegionName.EU_CENTRAL_1;

    // Test key metadata no authorization
    doThrow(new AWSApiException(CommonErrorCode.NO_AUTHORIZATION))
        .when(awsApiSvc)
        .findKeyMetadata(any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          accessKeyID, secretAccessKey, sessionToken, customerMasterKeyID, regionName, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_AWS_CREDENTIALS, pE.getErrorCode());
    }
    verify(awsApiSvc, times(1))
        .findKeyMetadata(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));
    verify(awsApiSvc, never())
        .checkEncryptDecrypt(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));

    // Test key metadata not found
    doThrow(new AWSApiException(CommonErrorCode.NOT_FOUND))
        .when(awsApiSvc)
        .findKeyMetadata(any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          accessKeyID, secretAccessKey, sessionToken, customerMasterKeyID, regionName, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.CUSTOMER_MASTER_KEY_NOT_FOUND, pE.getErrorCode());
    }
    verify(awsApiSvc, times(2))
        .findKeyMetadata(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));
    verify(awsApiSvc, never())
        .checkEncryptDecrypt(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));

    // Test "AWS Access Key Id needs a subscription for the service" error
    doThrow(new AWSApiException(AWSErrorCode.SUBSCRIPTION_REQUIRED_FOR_THE_SERVICE))
        .when(awsApiSvc)
        .findKeyMetadata(any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          accessKeyID, secretAccessKey, sessionToken, customerMasterKeyID, regionName, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.AWS_SUBSCRIPTION_REQUIRED, pE.getErrorCode());
    }
    verify(awsApiSvc, times(3))
        .findKeyMetadata(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));
    verify(awsApiSvc, never())
        .checkEncryptDecrypt(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));

    // Test key metadata unhandled error
    doThrow(new AWSApiException())
        .when(awsApiSvc)
        .findKeyMetadata(any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          accessKeyID, secretAccessKey, sessionToken, customerMasterKeyID, regionName, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INTERNAL, pE.getErrorCode());
    }
    verify(awsApiSvc, times(4))
        .findKeyMetadata(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));
    verify(awsApiSvc, never())
        .checkEncryptDecrypt(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));

    final KeyMetadata keyMetadata = mock(KeyMetadata.class);
    doReturn(keyMetadata).when(awsApiSvc).findKeyMetadata(any(), any(), any(), any(), any(), any());

    // Test key state pending deletion
    doReturn(KeyState.PendingDeletion.name()).when(keyMetadata).getKeyState();
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          accessKeyID, secretAccessKey, sessionToken, customerMasterKeyID, regionName, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.CUSTOMER_MASTER_KEY_PENDING_DELETION, pE.getErrorCode());
    }
    verify(awsApiSvc, times(5))
        .findKeyMetadata(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));
    verify(awsApiSvc, never())
        .checkEncryptDecrypt(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));

    // Test key state pending import
    doReturn(KeyState.PendingImport.name()).when(keyMetadata).getKeyState();
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          accessKeyID, secretAccessKey, sessionToken, customerMasterKeyID, regionName, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.CUSTOMER_MASTER_KEY_PENDING_IMPORT, pE.getErrorCode());
    }
    verify(awsApiSvc, times(6))
        .findKeyMetadata(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));
    verify(awsApiSvc, never())
        .checkEncryptDecrypt(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));

    // Test key metadata not enabled
    doReturn(KeyState.Enabled.name()).when(keyMetadata).getKeyState();
    doReturn(false).when(keyMetadata).isEnabled();
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          accessKeyID, secretAccessKey, sessionToken, customerMasterKeyID, regionName, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.CUSTOMER_MASTER_KEY_NOT_ENABLED, pE.getErrorCode());
    }
    verify(awsApiSvc, times(7))
        .findKeyMetadata(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));
    verify(awsApiSvc, never())
        .checkEncryptDecrypt(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));

    // Test encrypt / decrypt fails authorization
    doReturn(true).when(keyMetadata).isEnabled();
    doThrow(new AWSApiException(CommonErrorCode.NO_AUTHORIZATION))
        .when(awsApiSvc)
        .checkEncryptDecrypt(any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          accessKeyID, secretAccessKey, sessionToken, customerMasterKeyID, regionName, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT, pE.getErrorCode());
    }
    verify(awsApiSvc, times(8))
        .findKeyMetadata(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));
    verify(awsApiSvc, times(1))
        .checkEncryptDecrypt(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));

    // Test encrypt / decrypt fails unhandled error
    doThrow(new AWSApiException())
        .when(awsApiSvc)
        .checkEncryptDecrypt(any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          accessKeyID, secretAccessKey, sessionToken, customerMasterKeyID, regionName, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INTERNAL, pE.getErrorCode());
    }
    verify(awsApiSvc, times(9))
        .findKeyMetadata(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));
    verify(awsApiSvc, times(2))
        .checkEncryptDecrypt(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));

    // Test all checks pass
    doNothing().when(awsApiSvc).checkEncryptDecrypt(any(), any(), any(), any(), any(), any());
    encryptionAtRestSvc.validateAWSKMSCredentials(
        accessKeyID, secretAccessKey, sessionToken, customerMasterKeyID, regionName, null);
    verify(awsApiSvc, times(10))
        .findKeyMetadata(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));
    verify(awsApiSvc, times(3))
        .checkEncryptDecrypt(
            eq(accessKeyID),
            eq(secretAccessKey),
            eq(sessionToken),
            eq(regionName),
            any(),
            eq(customerMasterKeyID));
  }

  @Test
  public void testValidateAWSKMSCredentials_RoleId() throws SvcException {
    final NDSCloudProviderAccessSvc ndsCloudProviderAccessSvc =
        mock(NDSCloudProviderAccessSvc.class);
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(UnitTestUtils.create(NDSEncryptionAtRestSvc.class).withArgs(ndsCloudProviderAccessSvc));

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    final ObjectId roleId = new ObjectId();
    final String customerMasterKeyID = "cmk";
    final AWSRegionName regionName = AWSRegionName.EU_WEST_1;
    final ValidationPerformedState validationPerformedState = mock(ValidationPerformedState.class);

    // Test assume role fails
    doThrow(new SvcException(NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND))
        .when(ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(any(NDSGroup.class), any(), any());
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          ndsGroup, roleId, customerMasterKeyID, regionName, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND, pE.getErrorCode());
    }
    verify(ndsCloudProviderAccessSvc, times(1))
        .getAWSAssumeRoleTempCredentials(
            eq(ndsGroup), eq(roleId), eq(DEFAULT_CREDENTIALS_PROVIDERS));
    verify(encryptionAtRestSvc, never())
        .validateAWSKMSCredentials(any(), any(), any(), any(), any(), any());

    // Test validateAWSKMSCredentials fails
    final NDSAWSTempCredentials tempCredentials = mock(NDSAWSTempCredentials.class);
    final String tempAccessKeyID = "tempAccessKeyID";
    final String tempAccessSecret = "tempAccessSecret";
    final String tempSessionToken = "tempSessionToken";
    doReturn(tempAccessKeyID).when(tempCredentials).getAccessKey();
    doReturn(tempAccessSecret).when(tempCredentials).getAccessSecret();
    doReturn(tempSessionToken).when(tempCredentials).getSessionToken();

    doReturn(tempCredentials)
        .when(ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(any(NDSGroup.class), any(), any());
    doThrow(new SvcException(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT))
        .when(encryptionAtRestSvc)
        .validateAWSKMSCredentials(any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          ndsGroup, roleId, customerMasterKeyID, regionName, validationPerformedState);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT, pE.getErrorCode());
    }
    verify(ndsCloudProviderAccessSvc, times(2))
        .getAWSAssumeRoleTempCredentials(
            eq(ndsGroup), eq(roleId), eq(DEFAULT_CREDENTIALS_PROVIDERS));
    verify(encryptionAtRestSvc, times(1))
        .validateAWSKMSCredentials(
            eq(tempAccessKeyID),
            eq(tempAccessSecret),
            eq(tempSessionToken),
            eq(customerMasterKeyID),
            eq(regionName),
            eq(validationPerformedState));

    // Test all checks pass
    doNothing()
        .when(encryptionAtRestSvc)
        .validateAWSKMSCredentials(any(), any(), any(), any(), any(), any());
    encryptionAtRestSvc.validateAWSKMSCredentials(
        ndsGroup, roleId, customerMasterKeyID, regionName, validationPerformedState);
    verify(ndsCloudProviderAccessSvc, times(3))
        .getAWSAssumeRoleTempCredentials(
            eq(ndsGroup), eq(roleId), eq(DEFAULT_CREDENTIALS_PROVIDERS));
    verify(encryptionAtRestSvc, times(2))
        .validateAWSKMSCredentials(
            eq(tempAccessKeyID),
            eq(tempAccessSecret),
            eq(tempSessionToken),
            eq(customerMasterKeyID),
            eq(regionName),
            eq(validationPerformedState));
  }

  @Test
  public void testValidateAWSKMSCredentials_RoleId_WorkloadIdentity() throws SvcException {
    final ObjectId groupId = ObjectId.get();
    final NDSGroup ndsGroup = mock(NDSGroup.class);
    doReturn(groupId).when(ndsGroup).getGroupId();
    final Group group = mock(Group.class);
    final ObjectId roleId = new ObjectId();
    final String customerMasterKeyID = "cmk";
    final AWSRegionName regionName = AWSRegionName.EU_WEST_1;
    final ValidationPerformedState validationPerformedState = mock(ValidationPerformedState.class);

    final NDSCloudProviderAccessSvc ndsCloudProviderAccessSvc =
        mock(NDSCloudProviderAccessSvc.class);
    final AppSettings appSettings = mock(AppSettings.class);
    final GroupSvc groupSvc = mock(GroupSvc.class);
    doReturn(group).when(groupSvc).findById(eq(groupId));
    // Enable AWS_KMS_VALIDATION_WORKLOAD_IDENTITY
    when(FeatureFlagSvc.isFeatureFlagEnabled(
            eq(FeatureFlag.AWS_KMS_VALIDATION_WORKLOAD_IDENTITY), any(), any(), any()))
        .thenReturn(true);
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(ndsCloudProviderAccessSvc, appSettings, groupSvc));

    // Test assume role fails
    doThrow(new SvcException(NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND))
        .when(ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(any(NDSGroup.class), any(), any());
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          ndsGroup, roleId, customerMasterKeyID, regionName, null);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND, pE.getErrorCode());
    }
    verify(ndsCloudProviderAccessSvc, times(1))
        .getAWSAssumeRoleTempCredentials(eq(ndsGroup), eq(roleId), eq(WORKLOAD_IDENITTY_PROVIDERS));
    verify(encryptionAtRestSvc, never())
        .validateAWSKMSCredentials(any(), any(), any(), any(), any(), any());

    // Test validateAWSKMSCredentials fails
    final NDSAWSTempCredentials tempCredentials = mock(NDSAWSTempCredentials.class);
    final String tempAccessKeyID = "tempAccessKeyID";
    final String tempAccessSecret = "tempAccessSecret";
    final String tempSessionToken = "tempSessionToken";
    doReturn(tempAccessKeyID).when(tempCredentials).getAccessKey();
    doReturn(tempAccessSecret).when(tempCredentials).getAccessSecret();
    doReturn(tempSessionToken).when(tempCredentials).getSessionToken();

    doReturn(tempCredentials)
        .when(ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(any(NDSGroup.class), any(), any());
    doThrow(new SvcException(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT))
        .when(encryptionAtRestSvc)
        .validateAWSKMSCredentials(any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAWSKMSCredentials(
          ndsGroup, roleId, customerMasterKeyID, regionName, validationPerformedState);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT, pE.getErrorCode());
    }
    verify(ndsCloudProviderAccessSvc, times(2))
        .getAWSAssumeRoleTempCredentials(eq(ndsGroup), eq(roleId), eq(WORKLOAD_IDENITTY_PROVIDERS));
    verify(encryptionAtRestSvc, times(1))
        .validateAWSKMSCredentials(
            eq(tempAccessKeyID),
            eq(tempAccessSecret),
            eq(tempSessionToken),
            eq(customerMasterKeyID),
            eq(regionName),
            eq(validationPerformedState));

    // Test all checks pass
    doNothing()
        .when(encryptionAtRestSvc)
        .validateAWSKMSCredentials(any(), any(), any(), any(), any(), any());
    encryptionAtRestSvc.validateAWSKMSCredentials(
        ndsGroup, roleId, customerMasterKeyID, regionName, validationPerformedState);
    verify(ndsCloudProviderAccessSvc, times(3))
        .getAWSAssumeRoleTempCredentials(eq(ndsGroup), eq(roleId), eq(WORKLOAD_IDENITTY_PROVIDERS));
    verify(encryptionAtRestSvc, times(2))
        .validateAWSKMSCredentials(
            eq(tempAccessKeyID),
            eq(tempAccessSecret),
            eq(tempSessionToken),
            eq(customerMasterKeyID),
            eq(regionName),
            eq(validationPerformedState));
  }

  @Test
  public void testDoEncryptionAtRestValidation() throws Exception {
    final Group group = mock(Group.class);
    final GroupSvc groupSvc = mock(GroupSvc.class);
    doReturn(group).when(groupSvc).findById(any());

    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    doNothing().when(ndsGroupSvc).setAwsKmsValid(any(), anyBoolean(), any());
    doNothing().when(ndsGroupSvc).setGoogleCloudKmsValid(any(), anyBoolean(), any());
    doNothing().when(ndsGroupSvc).setAzureKeyVaultValid(any(), anyBoolean(), any());

    final OrganizationSvc organizationSvc = mock(OrganizationSvc.class);

    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);
    final ObjectId groupId = new ObjectId();

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    doReturn(Optional.of(ndsGroup)).when(ndsGroupDao).find(any(ObjectId.class));

    final NDSEncryptionAtRest ndsEncryptionAtRest = mock(NDSEncryptionAtRest.class);
    final NDSAWSKMS awsKMS = mock(NDSAWSKMS.class);
    final NDSAzureKeyVault azureKeyVault = mock(NDSAzureKeyVault.class);
    final NDSGoogleCloudKMS ndsGoogleCloudKMS = mock(NDSGoogleCloudKMS.class);

    doReturn(false).when(awsKMS).isEnabled();
    doReturn(false).when(azureKeyVault).isEnabled();
    doReturn(false).when(ndsGoogleCloudKMS).isEnabled();
    doNothing().when(awsKMS).validate(any(), any(), any());
    doNothing().when(azureKeyVault).validate(any(), any(), any());
    doNothing().when(ndsGoogleCloudKMS).validate(any(), any(), any());
    doReturn(awsKMS).when(ndsEncryptionAtRest).getAWSKMS();
    doReturn(azureKeyVault).when(ndsEncryptionAtRest).getAzureKeyVault();
    doReturn(ndsGoogleCloudKMS).when(ndsEncryptionAtRest).getGoogleCloudKMS();
    doReturn(ndsEncryptionAtRest).when(ndsGroup).getEncryptionAtRest();

    final AuditSvc auditSvc = mock(AuditSvc.class);
    doNothing().when(auditSvc).saveAuditEvent(any());

    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);
    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);
    final GCPApiSvc gcpApiSvc = mock(GCPApiSvc.class);

    final NDSEncryptionAtRestEmailSvc ndsEncryptionAtRestEmailSvc =
        mock(NDSEncryptionAtRestEmailSvc.class);

    final NDSClusterSvc ndsClusterSvc = mock(NDSClusterSvc.class);
    doReturn(List.of(mock(ClusterDescription.class)))
        .when(ndsClusterSvc)
        .getEncryptedClustersByProvider(any(), any());

    final AppSettings appSettings = mock(AppSettings.class);
    doReturn(AppEnv.LOCAL).when(appSettings).getAppEnv();

    final PointsOfPresenceSvc pointsOfPresenceSvc = mock(PointsOfPresenceSvc.class);
    when(pointsOfPresenceSvc.getControlPlaneIpAddresses(any())).thenCallRealMethod();
    doReturn(true).when(pointsOfPresenceSvc).isActive();
    doReturn(
            List.of(
                new PointOfPresence(
                    CloudProvider.AWS,
                    "us-east-1",
                    List.of("*********/32", "*********/16"),
                    List.of("*********/8", "*********/12"))))
        .when(pointsOfPresenceSvc)
        .getPointsOfPresence(any());

    final NDSEncryptionAtRestSvc ndsEncryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(
                    awsApiSvc,
                    azureApiSvc,
                    gcpApiSvc,
                    ndsClusterSvc,
                    ndsGroupSvc,
                    ndsGroupDao,
                    auditSvc,
                    groupSvc,
                    organizationSvc,
                    auditSvc,
                    ndsEncryptionAtRestEmailSvc,
                    pointsOfPresenceSvc,
                    appSettings));
    // Clear pending shutdowns after each processing
    doAnswer(
            invocation -> {
              invocation.callRealMethod();
              ndsEncryptionAtRestSvc.resetValidationRoundShutdownCounters();
              return null;
            })
        .when(ndsEncryptionAtRestSvc)
        .processCollectedShutdowns();

    // Test project is closed or dead
    final List<GroupStatus.Type> closedAndDeadTypes =
        Stream.concat(GroupStatus.CLOSED_TYPES.stream(), Stream.of(GroupStatus.Type.DEAD)).toList();
    for (GroupStatus.Type closedAndDeadType : closedAndDeadTypes) {
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(ndsEncryptionAtRestEmailSvc, never())
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, never()).saveAuditEvent(any());
    }

    // Test AWS KMS, Azure Key Vault, Google Cloud KMS not enabled
    ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
    verify(ndsEncryptionAtRestEmailSvc, never())
        .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
    verify(auditSvc, never()).saveAuditEvent(any());
    verify(ndsGroupDao, never()).updateEncryptionAtRest(any(), any());
    verify(ndsGroupSvc, never()).setAwsKmsValid(any(), anyBoolean(), any());
    verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), anyBoolean(), any());
    verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), anyBoolean(), any());

    // Test AWS KMS enabled, Azure Key Vault, Google Cloud KMS not enabled
    doReturn(true).when(awsKMS).isEnabled();
    doReturn(Optional.of("IAMTHEACCESSKEY")).when(awsKMS).getAccessKeyID();
    doReturn(Optional.of("ICANKEEPASECRET")).when(awsKMS).getSecretAccessKey();
    doReturn(Optional.of("ENCRYPTWITHME-ENCRYPTWITHME-ENCRYPTWITHME"))
        .when(awsKMS)
        .getCustomerMasterKeyID();
    doReturn(Optional.of(AWSRegionName.US_EAST_1)).when(awsKMS).getRegion();
    doReturn(true).when(awsKMS).isValid();

    // Test AWS KMS key not found
    {
      doThrow(new AWSApiException(CommonErrorCode.NOT_FOUND))
          .when(awsApiSvc)
          .findKeyMetadata(any(), any(), any(), any(), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(ndsEncryptionAtRestEmailSvc, times(1))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(1)).saveAuditEvent(any());
      verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), anyBoolean(), any());

      // Verify group queued for shutdown (pending shutdowns)
      verify(ndsEncryptionAtRestSvc)
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));

      verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), anyBoolean(), any());
      verify(ndsEncryptionAtRestSvc, times(1))
          .incrementPromErrorCounter(eq(CloudProvider.AWS), any());
    }

    // Test AWS KMS access denied - set NetworkAccessDeniedDate and do not send email
    {
      doThrow(new AWSApiException(CommonErrorCode.NO_AUTHORIZATION))
          .when(awsApiSvc)
          .findKeyMetadata(any(), any(), any(), any(), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(ndsGroupDao, times(1))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AWS_KMS), any(Date.class));
      verify(ndsEncryptionAtRestEmailSvc, times(1))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(1)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(2))
          .incrementPromErrorCounter(eq(CloudProvider.AWS), any());
      // do not set networkAccessDeniedDate if it already exists
      doReturn(Optional.of(new Date())).when(awsKMS).getNetworkAccessDeniedDate();
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(ndsGroupDao, times(1))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AWS_KMS), any(Date.class));
      verify(ndsGroupDao, never()).setAwsKmsLastSuccessfulValidationIPAddresses(any(), any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .incrementPromErrorCounter(eq(CloudProvider.AWS), any());
      verify(ndsEncryptionAtRestSvc)
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
    }

    doReturn(Optional.of(Set.of("*********/32", "*********/16", "*********/8", "*********/12")))
        .when(awsKMS)
        .getLastSuccessfulValidationIPAddresses();
    // Test invalid AWS credentials
    {
      doThrow(new AWSApiException(CommonErrorCode.NO_AUTHORIZATION))
          .when(awsApiSvc)
          .findKeyMetadata(any(), any(), any(), any(), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(ndsEncryptionAtRestEmailSvc, times(2))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(2)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, never()).setAwsKmsValid(any(), eq(true), any());
      verify(ndsGroupDao, times(1))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AWS_KMS), any(Date.class));
      verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), anyBoolean(), any());
      verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), anyBoolean(), any());
      verify(ndsEncryptionAtRestSvc, times(4))
          .incrementPromErrorCounter(eq(CloudProvider.AWS), any());
    }

    final KeyMetadata keyMetadata = new KeyMetadata();
    keyMetadata.setEnabled(true);
    keyMetadata.setKeyState(KeyState.Enabled);
    doReturn(keyMetadata).when(awsApiSvc).findKeyMetadata(any(), any(), any(), any(), any(), any());

    // Test check AWS KMS encrypt decrypt
    {
      doThrow(new AWSApiException(CommonErrorCode.NO_AUTHORIZATION))
          .when(awsApiSvc)
          .checkEncryptDecrypt(any(), any(), any(), any(), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(ndsEncryptionAtRestEmailSvc, times(3))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(3)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, never()).setAwsKmsValid(any(), eq(true), any());
      verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), anyBoolean(), any());
      verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), anyBoolean(), any());
      verify(ndsEncryptionAtRestSvc, times(5))
          .incrementPromErrorCounter(eq(CloudProvider.AWS), any());
    }

    doReturn(false).when(awsKMS).isValid();

    // Test flip valid to true on success
    {
      doNothing().when(awsApiSvc).checkEncryptDecrypt(any(), any(), any(), any(), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(ndsEncryptionAtRestEmailSvc, times(3))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(4)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAwsKmsValid(any(), eq(true), any());
      verify(ndsGroupDao, times(1))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AWS_KMS), eq(null));
      verify(ndsGroupDao)
          .setAwsKmsLastSuccessfulValidationIPAddresses(
              eq(groupId),
              eq(Set.of("*********/32", "*********/16", "*********/8", "*********/12")));
      verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), anyBoolean(), any());
      verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), anyBoolean(), any());
      verify(ndsEncryptionAtRestSvc, times(5))
          .incrementPromErrorCounter(eq(CloudProvider.AWS), any());
    }

    // Test Azure Key Vault enabled, AWS KMS, Google Cloud KMS not enabled
    doReturn(false).when(awsKMS).isEnabled();
    doReturn(true).when(azureKeyVault).isEnabled();
    doReturn(Optional.of("client id")).when(azureKeyVault).getClientID();
    doReturn(Optional.of("tenant id")).when(azureKeyVault).getTenantID();
    doReturn(Optional.of("secret")).when(azureKeyVault).getSecret();
    doReturn(Optional.of(SupportedAzureEnvironment.AZURE)).when(azureKeyVault).getEnvironment();
    doReturn(Optional.of("subscription id")).when(azureKeyVault).getSubscriptionID();
    doReturn(Optional.of("resource group name")).when(azureKeyVault).getResourceGroupName();
    doReturn(Optional.of("key vault name")).when(azureKeyVault).getKeyVaultName();
    doReturn(Optional.of("key identifier")).when(azureKeyVault).getKeyIdentifier();
    doReturn(true).when(azureKeyVault).isValid();

    // Test Azure Key Vault key not found sends email and saves an audit
    {
      doReturn(null)
          .when(azureApiSvc)
          .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(ndsEncryptionAtRestEmailSvc, times(4))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(5)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAwsKmsValid(any(), eq(true), any());
      verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), anyBoolean(), any());
      verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), eq(true), any());
      // Azure key not found should queue for shutdown
      verify(ndsEncryptionAtRestSvc)
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), eq(ndsGroup));
      verify(ndsEncryptionAtRestSvc, times(1))
          .incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
    }

    // Test invalid Azure Key Vault credentials sends email and saves an audit
    {
      doThrow(new AzureApiException(CommonErrorCode.NO_AUTHORIZATION))
          .when(azureApiSvc)
          .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(ndsEncryptionAtRestEmailSvc, times(5))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(6)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAwsKmsValid(any(), eq(true), any());
      verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), anyBoolean(), any());
      verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), eq(true), any());

      // Azure invalid credentials should queue for shutdown
      verify(ndsEncryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), eq(ndsGroup));

      verify(ndsEncryptionAtRestSvc, times(2))
          .incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
    }

    // Test Azure Key Vault access denied - set NetworkAccessDeniedDate and do not send email
    {
      doThrow(new AzureApiException(AzureErrorCode.FORBIDDEN_BY_FIREWALL))
          .when(azureApiSvc)
          .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(ndsGroupDao, times(1))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT), any(Date.class));
      verify(ndsEncryptionAtRestEmailSvc, times(5))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(6)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
      // do not set networkAccessDeniedDate if it already exists
      doReturn(Optional.of(new Date())).when(azureKeyVault).getNetworkAccessDeniedDate();
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(ndsGroupDao, times(1))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT), any(Date.class));
      verify(ndsEncryptionAtRestSvc, times(4))
          .incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
      verify(ndsEncryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), eq(ndsGroup));
    }

    final Key key = mock(Key.class);
    final KeyProperties keyAttributes = mock(KeyProperties.class);
    doReturn(keyAttributes).when(key).attributes();
    doReturn(true).when(keyAttributes).isEnabled();
    doReturn(key)
        .when(azureApiSvc)
        .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());

    // Test check Azure Key Vault encrypt decrypt sends email and saves an audit
    {
      doThrow(new AzureApiException(CommonErrorCode.NO_AUTHORIZATION))
          .when(azureApiSvc)
          .encryptStringWithKey(any(), any(), any(), any(), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(ndsEncryptionAtRestEmailSvc, times(5))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(6)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAwsKmsValid(any(), eq(true), any());
      verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), anyBoolean(), any());
      verify(ndsEncryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(5))
          .incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
    }

    doReturn(false).when(azureKeyVault).isValid();
    doReturn(Optional.empty()).when(azureKeyVault).getNetworkAccessDeniedDate();

    // Test flip valid to true on success
    {
      doReturn("encryptedData".getBytes())
          .when(azureApiSvc)
          .encryptStringWithKey(any(), any(), any(), any(), any(), any());
      doReturn(NDSEncryptionAtRestSvc.TEXT_TO_ENCRYPT.getBytes())
          .when(azureApiSvc)
          .decryptStringWithKey(any(), any(), any(), any(), eq("encryptedData".getBytes()), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(ndsEncryptionAtRestEmailSvc, times(5))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(7)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAwsKmsValid(any(), eq(true), any());
      verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), anyBoolean(), any());
      verify(ndsEncryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAzureKeyVaultValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(5))
          .incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
    }

    // Test clear networkAccessDeniedDate on success
    {
      doReturn(Optional.of(new Date())).when(azureKeyVault).getNetworkAccessDeniedDate();
      doReturn(true).when(azureKeyVault).isValid();
      doReturn("encryptedData".getBytes())
          .when(azureApiSvc)
          .encryptStringWithKey(any(), any(), any(), any(), any(), any());
      doReturn(NDSEncryptionAtRestSvc.TEXT_TO_ENCRYPT.getBytes())
          .when(azureApiSvc)
          .decryptStringWithKey(any(), any(), any(), any(), eq("encryptedData".getBytes()), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(ndsGroupDao, times(1))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT), eq(null));
    }

    // Test Google Cloud KMS enabled, AWS KMS, Azure Key Vault not enabled
    doReturn(false).when(azureKeyVault).isEnabled();
    doReturn(true).when(ndsGoogleCloudKMS).isEnabled();
    doReturn(Optional.of("service account key")).when(ndsGoogleCloudKMS).getServiceAccountKey();
    doReturn(Optional.of("key version resource id"))
        .when(ndsGoogleCloudKMS)
        .getKeyVersionResourceID();
    doReturn(Optional.of("key resource id")).when(ndsGoogleCloudKMS).getKeyResourceID();
    doReturn(true).when(ndsGoogleCloudKMS).isValid();
    doReturn(Optional.of(ndsGroup)).when(ndsGroupDao).find(any());

    // Test Google Cloud KMS key not found sends email and saves an audit
    {
      doThrow(new GCPApiException(CommonErrorCode.NOT_FOUND))
          .when(gcpApiSvc)
          .findKeyVersion(eq(null), any(String.class), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(ndsEncryptionAtRestEmailSvc, times(6))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(8)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAwsKmsValid(any(), eq(true), any());
      // GCP key not found should queue for shutdown
      verify(ndsEncryptionAtRestSvc)
          .addToPendingShutdowns(eq(CloudProvider.GCP), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAzureKeyVaultValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(1))
          .incrementPromErrorCounter(eq(CloudProvider.GCP), any());
    }

    // Test invalid Google Cloud KMS credentials sends email and saves an audit
    {
      doThrow(new GCPApiException(CommonErrorCode.NO_AUTHORIZATION))
          .when(gcpApiSvc)
          .findKeyVersion(eq(null), any(String.class), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(ndsEncryptionAtRestEmailSvc, times(7))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(9)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAwsKmsValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), eq(true), any());
      verify(ndsGroupSvc, times(1)).setAzureKeyVaultValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(2))
          .incrementPromErrorCounter(eq(CloudProvider.GCP), any());
      // GCP invalid credentials should queue for shutdown
      verify(ndsEncryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.GCP), eq(groupId), eq(ndsGroup));
    }

    // Test GCP Cloud KMS access denied - set NetworkAccessDeniedDate and do not send email
    {
      final JSONObject responseContent = new JSONObject();
      responseContent.put(
          "message",
          "Request is prohibited by organization's policy. vpcServiceControlsUniqueIdentifier:"
              + " ...");
      doThrow(new GCPApiException(responseContent, CommonErrorCode.NO_AUTHORIZATION))
          .when(gcpApiSvc)
          .findKeyVersion(eq(null), any(String.class), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(ndsGroupDao, times(1))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(FieldDefs.GOOGLE_CLOUD_KMS), any(Date.class));
      verify(ndsEncryptionAtRestEmailSvc, times(7))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(9)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .incrementPromErrorCounter(eq(CloudProvider.GCP), any());
      // do not set networkAccessDeniedDate if it already exists
      doReturn(Optional.of(new Date())).when(ndsGoogleCloudKMS).getNetworkAccessDeniedDate();
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(ndsGroupDao, times(1))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(FieldDefs.GOOGLE_CLOUD_KMS), any(Date.class));
      verify(ndsEncryptionAtRestSvc, times(4))
          .incrementPromErrorCounter(eq(CloudProvider.GCP), any());
      verify(ndsEncryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.GCP), eq(groupId), eq(ndsGroup));
    }

    final CryptoKeyVersion keyVersion = mock(CryptoKeyVersion.class);
    doReturn("ENABLED").when(keyVersion).getState();
    doReturn(keyVersion).when(gcpApiSvc).findKeyVersion(eq(null), any(String.class), any(), any());

    // Test check Google Cloud KMS encrypt decrypt sends email and saves an audit
    {
      doThrow(new GCPApiException(CommonErrorCode.NO_AUTHORIZATION))
          .when(gcpApiSvc)
          .encryptData(eq(null), any(String.class), any(), any(), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(ndsEncryptionAtRestEmailSvc, times(8))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(10)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAwsKmsValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.GCP), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAzureKeyVaultValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(5))
          .incrementPromErrorCounter(eq(CloudProvider.GCP), any());
    }

    doReturn(false).when(ndsGoogleCloudKMS).isValid();
    doReturn(Optional.empty()).when(ndsGoogleCloudKMS).getNetworkAccessDeniedDate();

    // Test flip valid to true on success
    {
      doReturn("encryptedData")
          .when(gcpApiSvc)
          .encryptData(eq(null), any(String.class), any(), any(), any(), any());
      doReturn(NDSEncryptionAtRestSvc.TEXT_TO_ENCRYPT)
          .when(gcpApiSvc)
          .decryptData(eq(null), any(String.class), any(), eq("encryptedData"), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(ndsEncryptionAtRestEmailSvc, times(8))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(11)).saveAuditEvent(any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAwsKmsValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(3))
          .addToPendingShutdowns(eq(CloudProvider.GCP), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setGoogleCloudKmsValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), eq(ndsGroup));
      verify(ndsGroupSvc, times(1)).setAzureKeyVaultValid(any(), eq(true), any());
      verify(ndsEncryptionAtRestSvc, times(5))
          .incrementPromErrorCounter(eq(CloudProvider.GCP), any());
    }

    // Test GCP Cloud KMS clear networkAccessDeniedDate on success
    {
      doReturn(Optional.of(new Date())).when(ndsGoogleCloudKMS).getNetworkAccessDeniedDate();
      doReturn(true).when(ndsGoogleCloudKMS).isValid();
      doReturn("encryptedData")
          .when(gcpApiSvc)
          .encryptData(eq(null), any(String.class), any(), any(), any(), any());
      doReturn(NDSEncryptionAtRestSvc.TEXT_TO_ENCRYPT)
          .when(gcpApiSvc)
          .decryptData(eq(null), any(String.class), any(), eq("encryptedData"), any(), any());
      ndsEncryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(ndsGroupDao, times(1))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.GOOGLE_CLOUD_KMS), eq(null));
    }
  }

  @Test
  public void testAzureEARPrivateEndpointSyncStatus() {
    final ObjectId groupId = new ObjectId();
    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);
    final NDSGroup ndsGroup = mock(NDSGroup.class);
    final AzureCloudProviderContainerSvc azureCloudProviderContainerSvc =
        mock(AzureCloudProviderContainerSvc.class);

    doReturn(groupId).when(ndsGroup).getGroupId();

    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);
    final NDSEncryptionAtRestSvc ndsEncryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(azureApiSvc, ndsGroupDao, azureCloudProviderContainerSvc));
    final NDSEncryptionAtRest ear = mock(NDSEncryptionAtRest.class);
    final NDSAzureKeyVault azureKeyVault = mock(NDSAzureKeyVault.class);

    final String privateEndpointName1 = "private-endpoint-1";
    final String privateEndpointName2 = "private-endpoint-2";
    final String privateEndpointName3 = "private-endpoint-3";
    final String privateEndpointName4 = "private-endpoint-4";
    final String privateEndpointName5 = "private-endpoint-5";
    final String azureSubscriptionId = "b37e4693849783ecf183a145";
    final String resourceGroupName = "rg";

    final AzureCloudProviderContainer azureCloudProviderContainerForEndpoint1 =
        mock(AzureCloudProviderContainer.class);
    final AzureCloudProviderContainer azureCloudProviderContainerForEndpoint2 =
        mock(AzureCloudProviderContainer.class);
    final AzureCloudProviderContainer azureCloudProviderContainerForEndpoint3 =
        mock(AzureCloudProviderContainer.class);

    doReturn(Optional.of(ndsGroup)).when(ndsGroupDao).find(eq(groupId));
    doReturn(ear).when(ndsGroup).getEncryptionAtRest();
    doReturn(azureKeyVault).when(ear).getAzureKeyVault();

    doReturn(Optional.of(azureCloudProviderContainerForEndpoint1))
        .when(azureCloudProviderContainerSvc)
        .getExistingContainerInRegion(eq(ndsGroup), eq(AzureRegionName.US_EAST));

    doReturn(Optional.of(azureCloudProviderContainerForEndpoint2))
        .when(azureCloudProviderContainerSvc)
        .getExistingContainerInRegion(eq(ndsGroup), eq(AzureRegionName.CANADA_EAST));

    doReturn(Optional.of(azureCloudProviderContainerForEndpoint3))
        .when(azureCloudProviderContainerSvc)
        .getExistingContainerInRegion(eq(ndsGroup), eq(AzureRegionName.POLAND_CENTRAL));

    doReturn(new ObjectId(azureSubscriptionId))
        .when(azureCloudProviderContainerForEndpoint1)
        .getAzureSubscriptionId();
    doReturn(new ObjectId(azureSubscriptionId))
        .when(azureCloudProviderContainerForEndpoint2)
        .getAzureSubscriptionId();
    doReturn(new ObjectId(azureSubscriptionId))
        .when(azureCloudProviderContainerForEndpoint3)
        .getAzureSubscriptionId();

    doReturn(Optional.of(resourceGroupName))
        .when(azureCloudProviderContainerForEndpoint1)
        .getResourceGroupName();
    doReturn(Optional.of(resourceGroupName))
        .when(azureCloudProviderContainerForEndpoint2)
        .getResourceGroupName();
    doReturn(Optional.of(resourceGroupName))
        .when(azureCloudProviderContainerForEndpoint3)
        .getResourceGroupName();

    // Test Azure private networking, sync private endpoints connection status
    {
      AzureKeyVaultEARPrivateEndpoint endpoint1 =
          spy(
              AzureKeyVaultEARPrivateEndpoint.builder()
                  .id(new ObjectId())
                  .privateEndpointConnectionName(privateEndpointName1)
                  .regionName(AzureRegionName.US_EAST)
                  .status(CloudProviderPrivateEndpoint.Status.PENDING_ACCEPTANCE)
                  .needsUpdateAfter(new Date())
                  .build());

      AzureKeyVaultEARPrivateEndpoint endpoint2 =
          spy(
              AzureKeyVaultEARPrivateEndpoint.builder()
                  .id(new ObjectId())
                  .privateEndpointConnectionName(privateEndpointName2)
                  .regionName(AzureRegionName.CANADA_EAST)
                  .status(CloudProviderPrivateEndpoint.Status.PENDING_ACCEPTANCE)
                  .needsUpdateAfter(new Date())
                  .build());

      AzureKeyVaultEARPrivateEndpoint endpoint3 =
          spy(
              AzureKeyVaultEARPrivateEndpoint.builder()
                  .id(new ObjectId())
                  .privateEndpointConnectionName(privateEndpointName3)
                  .regionName(AzureRegionName.POLAND_CENTRAL)
                  .status(CloudProviderPrivateEndpoint.Status.PENDING_ACCEPTANCE)
                  .needsUpdateAfter(new Date())
                  .build());

      AzureKeyVaultEARPrivateEndpoint endpoint4 =
          spy(
              AzureKeyVaultEARPrivateEndpoint.builder()
                  .id(new ObjectId())
                  .privateEndpointConnectionName(privateEndpointName4)
                  .regionName(AzureRegionName.POLAND_CENTRAL)
                  .status(CloudProviderPrivateEndpoint.Status.DELETING)
                  .deleteRequestedDate(new Date())
                  .build());

      AzureKeyVaultEARPrivateEndpoint endpoint5 =
          spy(
              AzureKeyVaultEARPrivateEndpoint.builder()
                  .id(new ObjectId())
                  .privateEndpointConnectionName(privateEndpointName5)
                  .regionName(AzureRegionName.POLAND_CENTRAL)
                  .status(CloudProviderPrivateEndpoint.Status.PENDING_RECREATION)
                  .deleteRequestedDate(new Date())
                  .build());

      doReturn(List.of(endpoint1, endpoint2, endpoint3, endpoint4, endpoint5))
          .when(azureKeyVault)
          .getPrivateEndpoints();

      // Mock PrivateEndpointInner and PrivateLinkServiceConnection
      final PrivateEndpointInner privateEndpointOneInnerMock = mock(PrivateEndpointInner.class);
      final PrivateEndpointInner privateEndpointTwoInnerMock = mock(PrivateEndpointInner.class);
      final PrivateEndpointInner privateEndpointThreeInnerMock = mock(PrivateEndpointInner.class);

      final PrivateLinkServiceConnection privateLinkServiceConnectionEndpoint1 =
          mock(PrivateLinkServiceConnection.class);
      final PrivateLinkServiceConnection privateLinkServiceConnectionEndpoint2 =
          mock(PrivateLinkServiceConnection.class);
      final PrivateLinkServiceConnection privateLinkServiceConnectionEndpoint3 =
          mock(PrivateLinkServiceConnection.class);

      final PrivateLinkServiceConnectionState privateLinkConnectionStateEndpoint1 =
          mock(PrivateLinkServiceConnectionState.class);
      final PrivateLinkServiceConnectionState privateLinkConnectionStateEndpoint2 =
          mock(PrivateLinkServiceConnectionState.class);
      final PrivateLinkServiceConnectionState privateLinkConnectionStateEndpoint3 =
          mock(PrivateLinkServiceConnectionState.class);

      doReturn(privateEndpointOneInnerMock)
          .when(azureApiSvc)
          .findPrivateEndpoint(
              any(ObjectId.class), any(String.class), eq(privateEndpointName1), any());
      doReturn(privateEndpointTwoInnerMock)
          .when(azureApiSvc)
          .findPrivateEndpoint(
              any(ObjectId.class), any(String.class), eq(privateEndpointName2), any());
      doReturn(privateEndpointThreeInnerMock)
          .when(azureApiSvc)
          .findPrivateEndpoint(
              any(ObjectId.class), any(String.class), eq(privateEndpointName3), any());

      doReturn(List.of(privateLinkServiceConnectionEndpoint1))
          .when(privateEndpointOneInnerMock)
          .manualPrivateLinkServiceConnections();
      doReturn(List.of(privateLinkServiceConnectionEndpoint2))
          .when(privateEndpointTwoInnerMock)
          .manualPrivateLinkServiceConnections();
      doReturn(List.of(privateLinkServiceConnectionEndpoint3))
          .when(privateEndpointThreeInnerMock)
          .manualPrivateLinkServiceConnections();

      doReturn(privateLinkConnectionStateEndpoint1)
          .when(privateLinkServiceConnectionEndpoint1)
          .privateLinkServiceConnectionState();
      doReturn(privateLinkConnectionStateEndpoint2)
          .when(privateLinkServiceConnectionEndpoint2)
          .privateLinkServiceConnectionState();
      doReturn(privateLinkConnectionStateEndpoint3)
          .when(privateLinkServiceConnectionEndpoint3)
          .privateLinkServiceConnectionState();

      doReturn(AzureApiSvc.PrivateEndpointAcceptanceStatus.APPROVED.getStatus())
          .when(privateLinkConnectionStateEndpoint1)
          .status();

      doReturn(AzureApiSvc.PrivateEndpointAcceptanceStatus.DISCONNECTED.getStatus())
          .when(privateLinkConnectionStateEndpoint2)
          .status();

      doReturn(AzureApiSvc.PrivateEndpointAcceptanceStatus.REJECTED.getStatus())
          .when(privateLinkConnectionStateEndpoint3)
          .status();

      // Perform the test
      ndsEncryptionAtRestSvc.syncAzurePrivateEndpointStatus(groupId);

      // verify all endpoints, except for those marked for deletion, are updated
      verify(ndsGroupDao, times(3))
          .updateEncryptionAtRestPrivateEndpoint(
              eq(groupId),
              eq(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT),
              any(AzureKeyVaultEARPrivateEndpoint.class),
              any());

      // verify planner asap is invoked to reap the failed endpoint
      verify(ndsGroupDao, times(1)).setPlanASAP(groupId);

      // test exception handling
      doReturn(
              List.of(privateLinkServiceConnectionEndpoint1, privateLinkServiceConnectionEndpoint2))
          .when(privateEndpointOneInnerMock)
          .manualPrivateLinkServiceConnections();
      try {
        ndsEncryptionAtRestSvc.syncAzurePrivateEndpointStatus(groupId);
        fail();
      } catch (final Exception pE) {
        assertEquals(IllegalStateException.class, pE.getClass());
      }
    }
  }

  @Test
  public void testIsAWSKMSExceptionDueToIpAddressesChange() {
    final NDSAWSKMS awsKMS = mock(NDSAWSKMS.class);
    final PointsOfPresenceSvc pointsOfPresenceSvc = mock(PointsOfPresenceSvc.class);
    final NDSEncryptionAtRestSvc ndsEncryptionAtRestSvc =
        spy(UnitTestUtils.create(NDSEncryptionAtRestSvc.class).withArgs(pointsOfPresenceSvc));

    assertFalse(
        ndsEncryptionAtRestSvc.isAWSKMSExceptionDueToIpAddressesChange(
            new SvcException(NDSErrorCode.AWS_KMS_CREDENTIALS_AUTH_DEPRECATED), awsKMS, Set.of()),
        "should return false for non access denied error");

    doReturn(false).when(pointsOfPresenceSvc).isActive();
    assertFalse(
        ndsEncryptionAtRestSvc.isAWSKMSExceptionDueToIpAddressesChange(
            new SvcException(NDSErrorCode.INVALID_AWS_CREDENTIALS), awsKMS, Set.of()),
        "should return false when pointsOfPresenceSvc is not active");

    doReturn(true).when(pointsOfPresenceSvc).isActive();
    assertTrue(
        ndsEncryptionAtRestSvc.isAWSKMSExceptionDueToIpAddressesChange(
            new SvcException(NDSErrorCode.INVALID_AWS_CREDENTIALS), awsKMS, Set.of()),
        "should return true when can not fetch control plane ip addresses");

    doReturn(Optional.empty()).when(awsKMS).getLastSuccessfulValidationIPAddresses();
    assertTrue(
        ndsEncryptionAtRestSvc.isAWSKMSExceptionDueToIpAddressesChange(
            new SvcException(NDSErrorCode.INVALID_AWS_CREDENTIALS),
            awsKMS,
            Set.of("*********/32", "*********/16")),
        "should return true when no ip addresses is stored in kms object");

    doReturn(Optional.of(Set.of("*********/16", "*********/32")))
        .when(awsKMS)
        .getLastSuccessfulValidationIPAddresses();
    assertFalse(
        ndsEncryptionAtRestSvc.isAWSKMSExceptionDueToIpAddressesChange(
            new SvcException(NDSErrorCode.INVALID_AWS_CREDENTIALS),
            awsKMS,
            Set.of("*********/32", "*********/16")),
        "should return false when ip addresses match");

    doReturn(Optional.of(Set.of("*********/32", "*********/16")))
        .when(awsKMS)
        .getLastSuccessfulValidationIPAddresses();
    assertTrue(
        ndsEncryptionAtRestSvc.isAWSKMSExceptionDueToIpAddressesChange(
            new SvcException(NDSErrorCode.INVALID_AWS_CREDENTIALS),
            awsKMS,
            Set.of("*********/32", "*********/16")),
        "should return true when ip addresses mismatch");
  }

  @Test
  public void testValidateAzureKeyVaultCredentials() throws SvcException {
    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);
    final AppSettings appSettings = mock(AppSettings.class);
    final KMSValidationJobSubmissionSvc KMSValidationJobSubmissionSvc =
        mock(KMSValidationJobSubmissionSvc.class);
    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    final NDSClusterSvc ndsClusterSvc = mock(NDSClusterSvc.class);
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(
                    KMSValidationJobSubmissionSvc,
                    ndsClusterSvc,
                    ndsGroupSvc,
                    azureApiSvc,
                    appSettings));

    final Group group = mock(Group.class);
    doReturn(false).when(group).useCNRegionsOnly();
    // Test for disabled Azure Key Vault
    final NDSAzureKeyVault azureKeyVaultDisabled = new NDSAzureKeyVault();
    encryptionAtRestSvc.validateAzureKeyVaultCredentials(
        group, azureKeyVaultDisabled, RegionUsageRestrictions.NONE, null);
    verify(encryptionAtRestSvc, never()).validateAzureKeyVaultKey(any());

    // Test invalid Azure Key Vault model
    final NDSAzureKeyVault azureKeyVaultInvalid =
        new NDSAzureKeyVault(
            true, null, null, null, null, null, null, null, null, null, null, null, false);
    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, azureKeyVaultInvalid, RegionUsageRestrictions.NONE, null);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
      verify(encryptionAtRestSvc, never()).validateAzureKeyVaultKey(any());
    }

    // Test valid Azure Key Vault model
    final NDSAzureKeyVault azureKeyVaultValid =
        new NDSAzureKeyVault(
            true,
            null,
            null,
            null,
            "cID",
            "tID",
            "s",
            SupportedAzureEnvironment.AZURE,
            "ea13bceac13a1414215421ad",
            "rGN",
            "6ix-416-647",
            "https://6ix-416-647.vault.azure.net/keys/6ix-416-647-789/t0r0nt0",
            true);
    doNothing().when(encryptionAtRestSvc).validateAzureKeyVaultKeyForEARValidation(any(), any());
    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, azureKeyVaultValid, RegionUsageRestrictions.NONE, null);
      verify(encryptionAtRestSvc, times(1)).validateAzureKeyVaultKeyForEARValidation(any(), any());
    } catch (final SvcException e) {
      fail();
    }

    // Test invalid credentials
    ValidationPerformedState validationPerformedState = new ValidationPerformedState();
    doThrow(new AzureApiException(CommonErrorCode.NO_AUTHORIZATION))
        .when(azureApiSvc)
        .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());
    doCallRealMethod()
        .when(encryptionAtRestSvc)
        .validateAzureKeyVaultKeyForEARValidation(any(), any());
    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, azureKeyVaultValid, RegionUsageRestrictions.NONE, validationPerformedState);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_AZURE_CREDENTIALS, e.getErrorCode());
      assertNotNull(validationPerformedState.getPerformedDate());
    }

    doThrow(new AzureApiException(CommonErrorCode.FORBIDDEN))
        .when(azureApiSvc)
        .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, azureKeyVaultValid, RegionUsageRestrictions.NONE, null);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_AZURE_CREDENTIALS, e.getErrorCode());
    }

    doThrow(new AzureApiException("{ \"error\": null }", AzureErrorCode.INVALID_REQUEST))
        .when(azureApiSvc)
        .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, azureKeyVaultValid, RegionUsageRestrictions.NONE, null);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INTERNAL, e.getErrorCode());
    }

    doThrow(new AzureApiException(CommonErrorCode.NOT_FOUND))
        .when(azureApiSvc)
        .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, azureKeyVaultValid, RegionUsageRestrictions.NONE, null);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_FOUND, e.getErrorCode());
    }

    final Key key = mock(Key.class);
    final KeyProperties keyAttributes = mock(KeyProperties.class);
    doReturn(keyAttributes).when(key).attributes();
    doReturn(false).when(keyAttributes).isEnabled();
    doReturn(key)
        .when(azureApiSvc)
        .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, azureKeyVaultValid, RegionUsageRestrictions.NONE, null);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_ENABLED, e.getErrorCode());
    }

    doReturn(true).when(keyAttributes).isEnabled();
    doReturn(OffsetDateTime.now().plusDays(10)).when(keyAttributes).getNotBefore();
    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, azureKeyVaultValid, RegionUsageRestrictions.NONE, null);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_ACTIVE, e.getErrorCode());
    }

    doReturn(OffsetDateTime.now().minusDays(5)).when(keyAttributes).getNotBefore();
    doReturn(OffsetDateTime.now().minusDays(3)).when(keyAttributes).getExpiresOn();
    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, azureKeyVaultValid, RegionUsageRestrictions.NONE, null);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.AZURE_KEY_VAULT_KEY_EXPIRED, e.getErrorCode());
    }

    // Test for not being able to encrypt
    doReturn(OffsetDateTime.now().plusDays(45)).when(keyAttributes).getExpiresOn();
    doThrow(new AzureApiException(CommonErrorCode.FORBIDDEN))
        .when(azureApiSvc)
        .encryptStringWithKey(any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, azureKeyVaultValid, RegionUsageRestrictions.NONE, null);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT, e.getErrorCode());
    }

    // Test for not being able to decrypt
    doReturn(new byte[0])
        .when(azureApiSvc)
        .encryptStringWithKey(any(), any(), any(), any(), any(), any());
    doThrow(new AzureApiException(CommonErrorCode.FORBIDDEN))
        .when(azureApiSvc)
        .decryptStringWithKey(any(), any(), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, azureKeyVaultValid, RegionUsageRestrictions.NONE, null);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT, e.getErrorCode());
    }

    final ObjectId groupId = new ObjectId();
    doReturn(groupId).when(group).getId();

    final NDSAzureKeyVault ndsAzureKeyVault = mock(NDSAzureKeyVault.class);
    doReturn(true).when(ndsAzureKeyVault).isEnabled();
    doReturn(true).when(ndsAzureKeyVault).getRequirePrivateNetworking();
    doReturn(Optional.of("keyId")).when(ndsAzureKeyVault).getKeyIdentifier();
    doReturn(Optional.of("clientId")).when(ndsAzureKeyVault).getClientID();
    doReturn(Optional.of("tenantId")).when(ndsAzureKeyVault).getTenantID();
    doReturn(Optional.of("secret")).when(ndsAzureKeyVault).getSecret();
    doReturn(Optional.of(SupportedAzureEnvironment.AZURE)).when(ndsAzureKeyVault).getEnvironment();
    doReturn(Optional.of("rg")).when(ndsAzureKeyVault).getResourceGroupName();
    doReturn(Optional.of("subscriptionId")).when(ndsAzureKeyVault).getSubscriptionID();
    doReturn(Optional.of("keyVault")).when(ndsAzureKeyVault).getKeyVaultName();
    doNothing().when(ndsAzureKeyVault).validate(any(), eq(group), any());

    final AzureRegionName regionName = AzureRegionName.US_EAST_2;
    final AzureKeyVaultEARPrivateEndpoint privateEndpoint =
        mock(AzureKeyVaultEARPrivateEndpoint.class);
    doReturn(CloudProviderPrivateEndpoint.Status.ACTIVE).when(privateEndpoint).getStatus();
    doReturn(regionName).when(privateEndpoint).getRegionName();
    doReturn(List.of(privateEndpoint)).when(ndsAzureKeyVault).getPrivateEndpoints();
    doReturn(mock(Vault.class))
        .when(azureApiSvc)
        .findVault(any(), any(), any(), any(), any(), any(), any(), any());

    final ClusterDescription cd = mock(ClusterDescription.class);
    final String clusterName = "validClusterName";
    doReturn(clusterName).when(cd).getName();
    doReturn(Set.of(CloudProvider.AZURE)).when(cd).getCloudProviders();
    doReturn(Set.of(regionName)).when(cd).getRegionNames();
    doReturn(EncryptionAtRestProvider.AZURE).when(cd).getEncryptionAtRestProvider();
    final ClusterDescription cd2 = mock(ClusterDescription.class);
    doReturn(true).when(cd2).isPaused();
    doReturn(List.of(cd2, cd)).when(ndsClusterSvc).getActiveClusterDescriptions(eq(groupId));

    doReturn("selected-hostname")
        .when(encryptionAtRestSvc)
        .getHostForAgentValidation(
            eq(groupId), eq(Set.of(clusterName)), eq(Set.of(regionName)), eq(CloudProvider.AZURE));

    final KMSValidationJobResponse KMSValidationJobResponse = mock(KMSValidationJobResponse.class);
    SettableFuture<KMSValidationJobResponse> future1 = SettableFuture.create();
    future1.set(KMSValidationJobResponse);
    doReturn(future1)
        .when(KMSValidationJobSubmissionSvc)
        .submitJob(any(), any(), anyLong(), any(), eq(groupId));

    try {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(
          group, ndsAzureKeyVault, RegionUsageRestrictions.NONE, null);
    } catch (final Exception e) {
      fail();
    }
  }

  @Test
  public void testGetHostForAgentValidation() throws SvcException {
    final ReplicaSetHardwareSvc replicaSetHardwareSvc = mock(ReplicaSetHardwareSvc.class);
    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    final AutomationAgentAuditSvc automationAgentAuditSvc = mock(AutomationAgentAuditSvc.class);
    doReturn(Duration.ofMinutes(1)).when(automationAgentAuditSvc).getStaleAgentThreshold();
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(
                    replicaSetHardwareSvc,
                    automationAgentAuditSvc,
                    ndsGroupSvc,
                    mock(AppSettings.class)));
    final ObjectId groupId = new ObjectId();
    final NDSGroup ndsGroup = NDSModelTestFactory.getAzureMockedGroup(groupId);
    doReturn(Optional.of(ndsGroup)).when(ndsGroupSvc).find(groupId);

    doReturn(
            List.of(
                new ReplicaSetHardware(
                    ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareNoHardware(0))))
        .when(replicaSetHardwareSvc)
        .getReplicaSetHardware(eq(groupId), eq("foo"));
    try {
      encryptionAtRestSvc.getHostForAgentValidation(
          groupId, Set.of("foo"), Set.of(AzureRegionName.US_EAST_2), CloudProvider.AZURE);
      fail("no instance hardwares should throw exception");
    } catch (final Exception pE) {
      assertTrue(pE instanceof SvcException);
      assertEquals(NDSErrorCode.NO_HOST_FOR_EAR_VALIDATION_JOB, ((SvcException) pE).getErrorCode());
    }

    final ObjectId containerId = new ObjectId();
    final AzureCloudProviderContainer container = mock(AzureCloudProviderContainer.class);
    when(container.getRegion()).thenReturn(AzureRegionName.US_EAST_2);
    doReturn(Optional.of(container)).when(ndsGroup).getCloudProviderContainer(containerId);
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAzureClusterDescription(groupId, "foo"));
    ReplicaSetHardware replicaSetHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, clusterDescription, List.of(Pair.of("provisioned", false))));
    doReturn(List.of(replicaSetHardware))
        .when(replicaSetHardwareSvc)
        .getReplicaSetHardware(eq(groupId), eq("foo"));
    try {
      encryptionAtRestSvc.getHostForAgentValidation(
          groupId, Set.of("foo"), Set.of(AzureRegionName.US_EAST_2), CloudProvider.AZURE);
      fail("no provisioned instances should throw exception");
    } catch (final Exception pE) {
      assertTrue(pE instanceof SvcException);
      assertEquals(NDSErrorCode.NO_HOST_FOR_EAR_VALIDATION_JOB, ((SvcException) pE).getErrorCode());
    }

    replicaSetHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, clusterDescription, List.of(Pair.of("isPaused", true))));
    doReturn(List.of(replicaSetHardware))
        .when(replicaSetHardwareSvc)
        .getReplicaSetHardware(eq(groupId), eq("foo"));
    try {
      encryptionAtRestSvc.getHostForAgentValidation(
          groupId, Set.of("foo"), Set.of(AzureRegionName.US_EAST_2), CloudProvider.AZURE);
      fail("all instances paused should throw exception");
    } catch (final Exception pE) {
      assertTrue(pE instanceof SvcException);
      assertEquals(NDSErrorCode.NO_HOST_FOR_EAR_VALIDATION_JOB, ((SvcException) pE).getErrorCode());
    }

    replicaSetHardware =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, clusterDescription, List.of()));
    doReturn(List.of(replicaSetHardware))
        .when(replicaSetHardwareSvc)
        .getReplicaSetHardware(eq(groupId), eq("foo"));
    try {
      encryptionAtRestSvc.getHostForAgentValidation(
          groupId, Set.of("foo"), Set.of(AzureRegionName.EUROPE_NORTH), CloudProvider.AZURE);
      fail("no instance for the region(s) should throw exception");
    } catch (final Exception pE) {
      assertTrue(pE instanceof SvcException);
      assertEquals(NDSErrorCode.NO_HOST_FOR_EAR_VALIDATION_JOB, ((SvcException) pE).getErrorCode());
    }

    try {
      encryptionAtRestSvc.getHostForAgentValidation(
          groupId, Set.of("foo"), Set.of(AzureRegionName.US_EAST_2), CloudProvider.AZURE);
      fail("no automation agent audits should throw exception");
    } catch (final Exception pE) {
      assertTrue(pE instanceof SvcException);
      assertEquals(NDSErrorCode.NO_HOST_FOR_EAR_VALIDATION_JOB, ((SvcException) pE).getErrorCode());
    }

    // a healthy cluster
    ClusterDescription clusterDescription2 =
        new ClusterDescription(NDSModelTestFactory.getAzureClusterDescription(groupId, "bar"));
    final ReplicaSetHardware replicaSetHardware2 =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, containerId, clusterDescription2, List.of()));
    doReturn(List.of(replicaSetHardware2))
        .when(replicaSetHardwareSvc)
        .getReplicaSetHardware(eq(groupId), eq("bar"));
    for (var i = 0; i < replicaSetHardware2.getHardware().size(); i++) {
      final InstanceHardware hardware = replicaSetHardware2.getHardware().get(i);
      final AgentAudit audit = mock(AgentAudit.class);
      doReturn(Optional.of(audit))
          .when(automationAgentAuditSvc)
          .findAgentAuditByGroupIdAndHostName(
              eq(groupId), eq(hardware.getHostnameForAgents().orElse(null)));
      doReturn(Date.from(Instant.now().minus(5, ChronoUnit.SECONDS))).when(audit).getLastConf();
    }

    assertEquals(
        replicaSetHardware2.getHardware().get(0).getHostnameForAgents().orElse(null),
        encryptionAtRestSvc.getHostForAgentValidation(
            groupId, Set.of("foo", "bar"), Set.of(AzureRegionName.US_EAST_2), CloudProvider.AZURE),
        "select a instance from the healthy cluster when there are multiple clusters");

    // all stale audits, with the latest being the last one
    for (var i = 0; i < replicaSetHardware.getHardware().size(); i++) {
      final InstanceHardware hardware = replicaSetHardware.getHardware().get(i);
      final AgentAudit audit = mock(AgentAudit.class);
      doReturn(Optional.of(audit))
          .when(automationAgentAuditSvc)
          .findAgentAuditByGroupIdAndHostName(
              eq(groupId), eq(hardware.getHostnameForAgents().orElse(null)));
      doReturn(Date.from(Instant.now().minus(100 - i, ChronoUnit.MINUTES)))
          .when(audit)
          .getLastConf();
    }
    assertEquals(
        replicaSetHardware
            .getHardware()
            .get(replicaSetHardware.getHardware().size() - 1)
            .getHostnameForAgents()
            .orElse(null),
        encryptionAtRestSvc.getHostForAgentValidation(
            groupId, Set.of("foo"), Set.of(AzureRegionName.US_EAST_2), CloudProvider.AZURE),
        "select the instance with the latest audit date if they are all stale");
    assertEquals(
        replicaSetHardware2.getHardware().get(0).getHostnameForAgents().orElse(null),
        encryptionAtRestSvc.getHostForAgentValidation(
            groupId, Set.of("foo", "bar"), Set.of(AzureRegionName.US_EAST_2), CloudProvider.AZURE),
        "select an instance from the healthy cluster when there are multiple clusters");

    // have one instance not stale
    final AgentAudit notStaleAudit = mock(AgentAudit.class);
    doReturn(Optional.of(notStaleAudit))
        .when(automationAgentAuditSvc)
        .findAgentAuditByGroupIdAndHostName(
            eq(groupId),
            eq(replicaSetHardware.getHardware().get(1).getHostnameForAgents().orElse(null)));
    doReturn(Date.from(Instant.now().minus(5, ChronoUnit.SECONDS)))
        .when(notStaleAudit)
        .getLastConf();
    assertEquals(
        replicaSetHardware.getHardware().get(1).getHostnameForAgents().orElse(null),
        encryptionAtRestSvc.getHostForAgentValidation(
            groupId, Set.of("foo"), Set.of(AzureRegionName.US_EAST_2), CloudProvider.AZURE),
        "select the instance which has a recent audit date");

    // all have recent audit date
    for (var i = 0; i < replicaSetHardware.getHardware().size(); i++) {
      final InstanceHardware hardware = replicaSetHardware.getHardware().get(i);
      final AgentAudit audit = mock(AgentAudit.class);
      doReturn(Optional.of(audit))
          .when(automationAgentAuditSvc)
          .findAgentAuditByGroupIdAndHostName(
              eq(groupId), eq(hardware.getHostnameForAgents().orElse(null)));
      doReturn(Date.from(Instant.now().minus(5, ChronoUnit.SECONDS))).when(audit).getLastConf();
    }
    assertEquals(
        replicaSetHardware.getHardware().get(0).getHostnameForAgents().orElse(null),
        encryptionAtRestSvc.getHostForAgentValidation(
            groupId, Set.of("foo"), Set.of(AzureRegionName.US_EAST_2), CloudProvider.AZURE),
        "select the first instance if all instances have recent audit date");
    assertEquals(
        replicaSetHardware2.getHardware().get(0).getHostnameForAgents().orElse(null),
        encryptionAtRestSvc.getHostForAgentValidation(
            groupId, Set.of("bar"), Set.of(AzureRegionName.US_EAST_2), CloudProvider.AZURE),
        "select the first instance if all instances have recent audit date");
  }

  @Test
  public void testDoEncryptionAtRestValidation_AgentValidation_Aws_SelectEARConfiguredRegion()
      throws Exception {
    final Group group = mock(Group.class);
    final GroupSvc groupSvc = mock(GroupSvc.class);
    doReturn(group).when(groupSvc).findById(any());

    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);

    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);
    final ObjectId groupId = new ObjectId();

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    doReturn(Optional.of(ndsGroup)).when(ndsGroupDao).find(any(ObjectId.class));
    doReturn(groupId).when(ndsGroup).getGroupId();

    final KMSValidationJobSubmissionSvc KMSValidationJobSubmissionSvc =
        mock(KMSValidationJobSubmissionSvc.class);
    final ReplicaSetHardwareSvc replicaSetHardwareSvc = mock(ReplicaSetHardwareSvc.class);
    final NDSClusterSvc ndsClusterSvc = mock(NDSClusterSvc.class);
    final NDSEncryptionAtRestEmailSvc ndsEncryptionAtRestEmailSvc =
        mock(NDSEncryptionAtRestEmailSvc.class);

    // setup encryption at rest settings
    final ObjectId roleID = new ObjectId();
    final AWSRegionName awsRegionName = AWSRegionName.US_EAST_1;

    final AWSKMSEARPrivateEndpoint privateEndpoint =
        new AWSKMSEARPrivateEndpoint.Builder()
            .id(new ObjectId())
            .privateEndpointResourceId("resourceID")
            .status(CloudProviderPrivateEndpoint.Status.ACTIVE)
            .regionName(awsRegionName)
            .build();

    final NDSAWSKMS ndsawskms =
        spy(
            new NDSAWSKMS.Builder()
                .setValid(true)
                .setEnabled(true)
                .setRoleId(roleID)
                .setAccessKeyID("accessKeyID")
                .setCustomerMasterKeyID("customerMasterKeyID")
                .setRegion(AWSRegionName.AP_NORTHEAST_1)
                .setRequirePrivateNetworking(true)
                .setPrivateEndpoints(List.of(privateEndpoint))
                .build());

    final NDSEncryptionAtRest ndsEncryptionAtRest =
        new NDSEncryptionAtRest(
            ndsawskms,
            new NDSAzureKeyVault.Builder().setEnabled(false).setValid(false).build(),
            new NDSGoogleCloudKMS.Builder().setEnabled(false).setValid(false).build());

    doReturn(ndsEncryptionAtRest).when(ndsGroup).getEncryptionAtRest();

    // mock aws kms validation
    doNothing().when(ndsawskms).validate(any(), eq(group), any());

    ShardedClusterDescription cd =
        NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
            .copy()
            .setEncryptionAtRestProvider(EncryptionAtRestProvider.AWS)
            .build();
    doReturn(List.of(cd)).when(ndsClusterSvc).getActiveClusterDescriptions(eq(groupId));

    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(
                    KMSValidationJobSubmissionSvc,
                    replicaSetHardwareSvc,
                    ndsEncryptionAtRestEmailSvc,
                    ndsClusterSvc,
                    ndsGroupSvc,
                    groupSvc,
                    ndsGroupDao,
                    mock(AuditSvc.class),
                    mock(AWSApiSvc.class),
                    mock(PointsOfPresenceSvc.class),
                    mock(AppSettings.class)));

    {
      // Case: Cluster region is US_EAST_1, but the configured EAR region is AP_NORTHEAST_1.
      // Expected: EAR validation should be skipped.
      encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(KMSValidationJobSubmissionSvc, times(0))
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
    }

    {
      // Case: Both the cluster region and the configured EAR region are US_EAST_1.
      // Expected: EAR validation job submission should occur.
      doReturn(Optional.of(AWSRegionName.US_EAST_1)).when(ndsawskms).getRegion();
      doReturn("selected-host")
          .when(encryptionAtRestSvc)
          .getHostForAgentValidation(
              eq(groupId),
              eq(Set.of(NDSModelTestFactory.DEFAULT_CLUSTER_NAME)),
              eq(Set.of(awsRegionName)),
              eq(CloudProvider.AWS));
      encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);
      verify(KMSValidationJobSubmissionSvc, times(1))
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
    }
  }

  @Test
  public void testDoEncryptionAtRestValidation_AgentValidation_Aws() throws SvcException {
    final Group group = mock(Group.class);
    final GroupSvc groupSvc = mock(GroupSvc.class);
    doReturn(group).when(groupSvc).findById(any());

    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    doNothing().when(ndsGroupSvc).setAwsKmsValid(any(), anyBoolean(), any());

    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);
    final ObjectId groupId = new ObjectId();

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    doReturn(Optional.of(ndsGroup)).when(ndsGroupDao).find(any(ObjectId.class));
    doReturn(groupId).when(ndsGroup).getGroupId();
    final AuditSvc auditSvc = mock(AuditSvc.class);
    doNothing().when(auditSvc).saveAuditEvent(any());

    final KMSValidationJobSubmissionSvc KMSValidationJobSubmissionSvc =
        mock(KMSValidationJobSubmissionSvc.class);
    final ReplicaSetHardwareSvc replicaSetHardwareSvc = mock(ReplicaSetHardwareSvc.class);
    final NDSClusterSvc ndsClusterSvc = mock(NDSClusterSvc.class);
    final NDSEncryptionAtRestEmailSvc ndsEncryptionAtRestEmailSvc =
        mock(NDSEncryptionAtRestEmailSvc.class);
    final AWSApiSvc awsApiSvc = mock(AWSApiSvc.class);

    // setup encryption at rest settings
    final ObjectId roleID = new ObjectId();
    final AWSRegionName awsRegionName = AWSRegionName.US_EAST_1;

    final AWSKMSEARPrivateEndpoint privateEndpoint =
        new AWSKMSEARPrivateEndpoint.Builder()
            .id(new ObjectId())
            .privateEndpointResourceId("resourceID")
            .status(CloudProviderPrivateEndpoint.Status.ACTIVE)
            .regionName(awsRegionName)
            .build();

    final NDSAWSKMS ndsawskms =
        spy(
            new NDSAWSKMS.Builder()
                .setValid(true)
                .setEnabled(true)
                .setRoleId(roleID)
                .setAccessKeyID("accessKeyID")
                .setCustomerMasterKeyID("customerMasterKeyID")
                .setRegion(awsRegionName)
                .setRequirePrivateNetworking(true)
                .setPrivateEndpoints(List.of(privateEndpoint))
                .build());

    final NDSEncryptionAtRest ndsEncryptionAtRest =
        new NDSEncryptionAtRest(
            ndsawskms,
            new NDSAzureKeyVault.Builder().setEnabled(false).setValid(false).build(),
            new NDSGoogleCloudKMS.Builder().setEnabled(false).setValid(false).build());

    doReturn(ndsEncryptionAtRest).when(ndsGroup).getEncryptionAtRest();

    // mock aws kms validation
    doNothing().when(ndsawskms).validate(any(), eq(group), any());

    ShardedClusterDescription cd =
        NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
            .copy()
            .setEncryptionAtRestProvider(EncryptionAtRestProvider.AWS)
            .build();
    doReturn(List.of(cd)).when(ndsClusterSvc).getActiveClusterDescriptions(eq(groupId));
    doReturn(groupId).when(group).getId();

    final PointsOfPresenceSvc pointsOfPresenceSvc = mock(PointsOfPresenceSvc.class);

    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(
                    KMSValidationJobSubmissionSvc,
                    replicaSetHardwareSvc,
                    ndsEncryptionAtRestEmailSvc,
                    ndsClusterSvc,
                    ndsGroupSvc,
                    groupSvc,
                    ndsGroupDao,
                    auditSvc,
                    awsApiSvc,
                    pointsOfPresenceSvc,
                    mock(AppSettings.class)));

    {
      // No host found does not invalidate Aws kms
      doThrow(new SvcException(NDSErrorCode.NO_HOST_FOR_EAR_VALIDATION_JOB))
          .when(encryptionAtRestSvc)
          .getHostForAgentValidation(
              eq(groupId),
              eq(Set.of(NDSModelTestFactory.DEFAULT_CLUSTER_NAME)),
              eq(Set.of(awsRegionName)),
              eq(CloudProvider.AWS));

      encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(KMSValidationJobSubmissionSvc, times(0))
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
      verify(encryptionAtRestSvc, times(1)).incrementPromErrorCounter(eq(CloudProvider.AWS), any());
      verify(ndsGroupSvc, times(0)).setAwsKmsValid(any(), eq(false), any());
      verify(ndsGroupDao, times(0))
          .setEncryptionAtRestNetworkAccessDeniedDate(eq(groupId), eq(FieldDefs.AWS_KMS), eq(null));
    }

    // clear invocations
    clearInvocations(KMSValidationJobSubmissionSvc, encryptionAtRestSvc, ndsGroupSvc, ndsGroupDao);

    doReturn("selected-host")
        .when(encryptionAtRestSvc)
        .getHostForAgentValidation(
            eq(groupId),
            eq(Set.of(NDSModelTestFactory.DEFAULT_CLUSTER_NAME)),
            eq(Set.of(awsRegionName)),
            eq(CloudProvider.AWS));

    // Thread interruption error does not invalidate azure key vault
    {
      SettableFuture<KMSValidationJobResponse> future = SettableFuture.create();
      doReturn(future)
          .when(KMSValidationJobSubmissionSvc)
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
      future.setException(new InterruptedException());

      encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(KMSValidationJobSubmissionSvc, times(1))
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
      verify(encryptionAtRestSvc, times(1)).incrementPromErrorCounter(eq(CloudProvider.AWS), any());
      verify(ndsEncryptionAtRestEmailSvc, times(0))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(0)).saveAuditEvent(any());
      verify(ndsGroupSvc, times(0)).setAwsKmsValid(any(), eq(false), any());
      verify(ndsGroupDao, times(0))
          .setEncryptionAtRestNetworkAccessDeniedDate(eq(groupId), eq(FieldDefs.AWS_KMS), eq(null));
    }

    // clear invocations
    clearInvocations(KMSValidationJobSubmissionSvc, encryptionAtRestSvc, ndsGroupSvc, ndsGroupDao);

    // Agent job failure from expected failure list sets valid to false and sends email and audit
    // event
    {
      SettableFuture<KMSValidationJobResponse> future = SettableFuture.create();
      future.setException(new SvcException(NDSErrorCode.CUSTOMER_MASTER_KEY_NOT_ENABLED));
      doReturn(future)
          .when(KMSValidationJobSubmissionSvc)
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));

      encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(KMSValidationJobSubmissionSvc, times(1))
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
      verify(encryptionAtRestSvc, times(1)).incrementPromErrorCounter(eq(CloudProvider.AWS), any());
      verify(ndsEncryptionAtRestEmailSvc, times(1))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(1)).saveAuditEvent(any());
      verify(ndsGroupSvc, times(0)).setAwsKmsValid(any(), eq(true), any());
      verify(encryptionAtRestSvc, times(1))
          .addToPendingShutdowns(eq(CloudProvider.AWS), eq(groupId), eq(ndsGroup));
      verify(ndsGroupDao, times(0))
          .setEncryptionAtRestNetworkAccessDeniedDate(eq(groupId), eq(FieldDefs.AWS_KMS), eq(null));
    }
  }

  @Test
  public void testDoEncryptionAtRestValidation_AgentValidation() throws SvcException {
    final Group group = mock(Group.class);
    final GroupSvc groupSvc = mock(GroupSvc.class);
    doReturn(group).when(groupSvc).findById(any());

    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    doNothing().when(ndsGroupSvc).setAzureKeyVaultValid(any(), anyBoolean(), any());

    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);
    final ObjectId groupId = new ObjectId();

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    doReturn(Optional.of(ndsGroup)).when(ndsGroupDao).find(any(ObjectId.class));
    final AuditSvc auditSvc = mock(AuditSvc.class);
    doNothing().when(auditSvc).saveAuditEvent(any());

    final KMSValidationJobSubmissionSvc KMSValidationJobSubmissionSvc =
        mock(KMSValidationJobSubmissionSvc.class);
    final ReplicaSetHardwareSvc replicaSetHardwareSvc = mock(ReplicaSetHardwareSvc.class);
    final NDSClusterSvc ndsClusterSvc = mock(NDSClusterSvc.class);
    final NDSEncryptionAtRestEmailSvc ndsEncryptionAtRestEmailSvc =
        mock(NDSEncryptionAtRestEmailSvc.class);
    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);

    final NDSEncryptionAtRest ndsEncryptionAtRest = mock(NDSEncryptionAtRest.class);
    final NDSAWSKMS awsKMS = mock(NDSAWSKMS.class);
    final NDSGoogleCloudKMS ndsGoogleCloudKMS = mock(NDSGoogleCloudKMS.class);
    doReturn(false).when(awsKMS).isEnabled();
    doReturn(false).when(ndsGoogleCloudKMS).isEnabled();
    doReturn(awsKMS).when(ndsEncryptionAtRest).getAWSKMS();
    doReturn(ndsGoogleCloudKMS).when(ndsEncryptionAtRest).getGoogleCloudKMS();
    doReturn(ndsEncryptionAtRest).when(ndsGroup).getEncryptionAtRest();

    final NDSAzureKeyVault ndsAzureKeyVault = mock(NDSAzureKeyVault.class);
    doReturn(true).when(ndsAzureKeyVault).isEnabled();
    doReturn(true).when(ndsAzureKeyVault).isValid();
    doReturn(Optional.of("clientId")).when(ndsAzureKeyVault).getClientID();
    doReturn(Optional.of("tenantId")).when(ndsAzureKeyVault).getTenantID();
    doReturn(Optional.of("secret")).when(ndsAzureKeyVault).getSecret();
    doReturn(Optional.of(SupportedAzureEnvironment.AZURE)).when(ndsAzureKeyVault).getEnvironment();
    doReturn(Optional.of("rg")).when(ndsAzureKeyVault).getResourceGroupName();
    doReturn(Optional.of("subscriptionId")).when(ndsAzureKeyVault).getSubscriptionID();
    doReturn(Optional.of("keyVault")).when(ndsAzureKeyVault).getKeyVaultName();

    doReturn(true).when(ndsAzureKeyVault).getRequirePrivateNetworking();
    doReturn(Optional.of("keyId")).when(ndsAzureKeyVault).getKeyIdentifier();
    doNothing().when(ndsAzureKeyVault).validate(any(), eq(group), any());

    final AzureRegionName regionName = AzureRegionName.US_EAST_2;
    final AzureKeyVaultEARPrivateEndpoint privateEndpoint =
        mock(AzureKeyVaultEARPrivateEndpoint.class);
    doReturn(CloudProviderPrivateEndpoint.Status.ACTIVE).when(privateEndpoint).getStatus();
    doReturn(regionName).when(privateEndpoint).getRegionName();

    doReturn(List.of(privateEndpoint)).when(ndsAzureKeyVault).getPrivateEndpoints();

    ShardedClusterDescription cd =
        NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AZURE)
            .copy()
            .setEncryptionAtRestProvider(EncryptionAtRestProvider.AZURE)
            .build();
    doReturn(List.of(cd)).when(ndsClusterSvc).getActiveClusterDescriptions(eq(groupId));

    doReturn(ndsAzureKeyVault).when(ndsEncryptionAtRest).getAzureKeyVault();

    doReturn(groupId).when(group).getId();
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(
                    KMSValidationJobSubmissionSvc,
                    replicaSetHardwareSvc,
                    ndsEncryptionAtRestEmailSvc,
                    ndsClusterSvc,
                    ndsGroupSvc,
                    groupSvc,
                    ndsGroupDao,
                    auditSvc,
                    azureApiSvc,
                    mock(AppSettings.class)));
    // Clear pending shutdowns after each processing
    doAnswer(
            invocation -> {
              invocation.callRealMethod();
              encryptionAtRestSvc.resetValidationRoundShutdownCounters();
              return null;
            })
        .when(encryptionAtRestSvc)
        .processCollectedShutdowns();

    doThrow(new AzureApiException(CommonErrorCode.NO_AUTHORIZATION))
        .doReturn(mock(Vault.class))
        .when(azureApiSvc)
        .findVault(any(), any(), any(), any(), any(), any(), any(), any());

    // No authorization to access key vault
    {
      encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(KMSValidationJobSubmissionSvc, times(0))
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
      verify(encryptionAtRestSvc, times(1))
          .incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
      verify(ndsEncryptionAtRestEmailSvc, times(1))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(1)).saveAuditEvent(any());
      verify(ndsGroupSvc, times(0)).setAzureKeyVaultValid(any(), eq(true), any());
      verify(encryptionAtRestSvc, times(1))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), eq(ndsGroup));
      verify(ndsGroupDao, times(0))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT), eq(null));
    }

    // No host found does not invalidate Azure key vault
    {
      doThrow(new SvcException(NDSErrorCode.NO_HOST_FOR_EAR_VALIDATION_JOB))
          .when(encryptionAtRestSvc)
          .getHostForAgentValidation(
              eq(groupId),
              eq(Set.of(NDSModelTestFactory.DEFAULT_CLUSTER_NAME)),
              eq(Set.of(AzureRegionName.US_EAST_2)),
              eq(CloudProvider.AZURE));

      encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(KMSValidationJobSubmissionSvc, times(0))
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
      verify(encryptionAtRestSvc, times(2))
          .incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
      verify(ndsEncryptionAtRestEmailSvc, times(1))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(1)).saveAuditEvent(any());
      verify(ndsGroupSvc, times(0)).setAzureKeyVaultValid(any(), eq(true), any());
      verify(encryptionAtRestSvc, times(1))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), eq(ndsGroup));
      verify(ndsGroupDao, times(0))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT), eq(null));
    }

    doReturn("selected-host")
        .when(encryptionAtRestSvc)
        .getHostForAgentValidation(
            eq(groupId),
            eq(Set.of(NDSModelTestFactory.DEFAULT_CLUSTER_NAME)),
            eq(Set.of(regionName)),
            eq(CloudProvider.AZURE));

    // Thread interruption error does not invalidate azure key vault
    {
      SettableFuture<KMSValidationJobResponse> future = SettableFuture.create();
      doReturn(future)
          .when(KMSValidationJobSubmissionSvc)
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
      future.setException(new InterruptedException());

      encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(KMSValidationJobSubmissionSvc, times(1))
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
      verify(encryptionAtRestSvc, times(3))
          .incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
      verify(ndsEncryptionAtRestEmailSvc, times(1))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(1)).saveAuditEvent(any());
      verify(ndsGroupSvc, times(0)).setAzureKeyVaultValid(any(), eq(true), any());
      verify(encryptionAtRestSvc, times(1))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), any());
      verify(ndsGroupDao, times(0))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT), eq(null));
    }

    // Agent job failure from expected failure list sets valid to false and sends email and audit
    // event
    {
      SettableFuture<KMSValidationJobResponse> future = SettableFuture.create();
      future.setException(new SvcException(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_FOUND));
      doReturn(future)
          .when(KMSValidationJobSubmissionSvc)
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));

      encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(KMSValidationJobSubmissionSvc, times(2))
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
      verify(encryptionAtRestSvc, times(4))
          .incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
      verify(ndsEncryptionAtRestEmailSvc, times(2))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(2)).saveAuditEvent(any());
      verify(ndsGroupSvc, times(0)).setAzureKeyVaultValid(any(), eq(true), any());
      verify(encryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), any());
      verify(ndsGroupDao, times(0))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT), eq(null));
    }

    // Agent validation success flips valid to true and resets network access denied date
    {
      doReturn(false).when(ndsAzureKeyVault).isValid();
      doReturn(Optional.of(new Date())).when(ndsAzureKeyVault).getNetworkAccessDeniedDate();
      final KMSValidationJobResponse jobResponse = mock(KMSValidationJobResponse.class);
      SettableFuture<KMSValidationJobResponse> future = SettableFuture.create();
      future.set(jobResponse);
      doReturn(future)
          .when(KMSValidationJobSubmissionSvc)
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));

      encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

      verify(KMSValidationJobSubmissionSvc, times(3))
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
      verify(encryptionAtRestSvc, times(4))
          .incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
      verify(ndsEncryptionAtRestEmailSvc, times(2))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
      verify(auditSvc, times(3)).saveAuditEvent(any());
      verify(ndsGroupSvc, times(1)).setAzureKeyVaultValid(any(), eq(true), any());
      verify(encryptionAtRestSvc, times(2))
          .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), any());
      verify(ndsGroupDao, times(1))
          .setEncryptionAtRestNetworkAccessDeniedDate(
              eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT), eq(null));
    }
  }

  private NDSEncryptionAtRestSvc setupAzureKeyVaultValidationMocks(
      final ObjectId pGroupId,
      final NDSGroupSvc pNDSGroupSvc,
      final NDSGroupDao pNDSGroupDao,
      final AuditSvc pAuditSvc,
      final KMSValidationJobSubmissionSvc pKMSValidationJobSubmissionSvc,
      final AzureApiSvc pAzureApiSvc,
      final boolean pEnablePrivateNetworking) {
    final Group group = mock(Group.class);
    final GroupSvc groupSvc = mock(GroupSvc.class);
    doReturn(group).when(groupSvc).findById(any());
    doReturn(pGroupId).when(group).getId();
    doNothing().when(pNDSGroupSvc).setAzureKeyVaultValid(any(), anyBoolean(), any());

    // setup encryption providers
    final NDSAWSKMS ndsawskms = mock(NDSAWSKMS.class);
    final NDSAzureKeyVault ndsAzureKeyVault = mock(NDSAzureKeyVault.class);
    final NDSGoogleCloudKMS ndsGoogleCloudKMS = mock(NDSGoogleCloudKMS.class);

    doReturn(false).when(ndsawskms).isEnabled();
    doReturn(false).when(ndsGoogleCloudKMS).isEnabled();

    // configure Azure key vault info
    doReturn(true).when(ndsAzureKeyVault).isEnabled();
    doReturn(pEnablePrivateNetworking).when(ndsAzureKeyVault).getRequirePrivateNetworking();
    doReturn(true).when(ndsAzureKeyVault).isValid();
    doReturn(Optional.of("clientId")).when(ndsAzureKeyVault).getClientID();
    doReturn(Optional.of("tenantId")).when(ndsAzureKeyVault).getTenantID();
    doReturn(Optional.of("secret")).when(ndsAzureKeyVault).getSecret();
    doReturn(Optional.of(SupportedAzureEnvironment.AZURE)).when(ndsAzureKeyVault).getEnvironment();
    doReturn(Optional.of("rg")).when(ndsAzureKeyVault).getResourceGroupName();
    doReturn(Optional.of("subscriptionId")).when(ndsAzureKeyVault).getSubscriptionID();
    doReturn(Optional.of("keyVault")).when(ndsAzureKeyVault).getKeyVaultName();

    final NDSEncryptionAtRest ndsEncryptionAtRest = mock(NDSEncryptionAtRest.class);
    doReturn(ndsawskms).when(ndsEncryptionAtRest).getAWSKMS();
    doReturn(ndsGoogleCloudKMS).when(ndsEncryptionAtRest).getGoogleCloudKMS();
    doReturn(ndsAzureKeyVault).when(ndsEncryptionAtRest).getAzureKeyVault();

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    doReturn(Optional.of(ndsGroup)).when(pNDSGroupDao).find(any(ObjectId.class));
    doReturn(pGroupId).when(ndsGroup).getGroupId();
    when(ndsGroup.getEncryptionAtRest()).thenReturn(ndsEncryptionAtRest);
    doNothing().when(pAuditSvc).saveAuditEvent(any());

    return spy(
        UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
            .withArgs(
                pKMSValidationJobSubmissionSvc,
                mock(ReplicaSetHardwareSvc.class),
                mock(NDSEncryptionAtRestEmailSvc.class),
                mock(NDSClusterSvc.class),
                pNDSGroupSvc,
                groupSvc,
                pNDSGroupDao,
                pAuditSvc,
                pAzureApiSvc,
                mock(AppSettings.class)));
  }

  @Test
  public void testAzureKeyVaultAccessValidation_ShutdownInProgress() throws SvcException {
    final ObjectId groupId = new ObjectId();
    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);
    final AuditSvc auditSvc = mock(AuditSvc.class);
    final KMSValidationJobSubmissionSvc kmsValidationJobSubmissionSvc =
        mock(KMSValidationJobSubmissionSvc.class);
    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);

    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        setupAzureKeyVaultValidationMocks(
            groupId,
            ndsGroupSvc,
            ndsGroupDao,
            auditSvc,
            kmsValidationJobSubmissionSvc,
            azureApiSvc,
            true);

    // configure illegal state exception (occurs during control plane restarts)
    doThrow(new IllegalStateException("Shutdown in progress"))
        .doReturn(mock(Vault.class))
        .when(azureApiSvc)
        .findVault(any(), any(), any(), any(), any(), any(), any(), any());

    encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

    // Verify Illegal state exception doesn't result in cluster shutdown
    verify(kmsValidationJobSubmissionSvc, never())
        .submitJob(any(), any(), anyLong(), any(), eq(groupId));

    verify(encryptionAtRestSvc, times(1))
        .onAzureKeyVaultValidationError(any(), any(), any(), any(), any());
    verify(encryptionAtRestSvc, times(1)).incrementPromErrorCounter(eq(CloudProvider.AZURE), any());

    verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), anyBoolean(), any());
    verify(ndsGroupDao, never())
        .setEncryptionAtRestNetworkAccessDeniedDate(
            eq(groupId), eq(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT), any());
    verify(auditSvc, never()).saveAuditEvent(any());
  }

  @Test
  public void testAzureKeyVaultAccessValidation_NoAuthorization() throws SvcException {
    final ObjectId groupId = new ObjectId();
    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);
    final AuditSvc auditSvc = mock(AuditSvc.class);
    final KMSValidationJobSubmissionSvc kmsValidationJobSubmissionSvc =
        mock(KMSValidationJobSubmissionSvc.class);
    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);

    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        setupAzureKeyVaultValidationMocks(
            groupId,
            ndsGroupSvc,
            ndsGroupDao,
            auditSvc,
            kmsValidationJobSubmissionSvc,
            azureApiSvc,
            true);

    // configure no auth error
    doThrow(new AzureApiException(CommonErrorCode.NO_AUTHORIZATION))
        .doReturn(mock(Vault.class))
        .when(azureApiSvc)
        .findVault(any(), any(), any(), any(), any(), any(), any(), any());

    encryptionAtRestSvc.doEncryptionAtRestValidation(groupId);

    // verify cluster shutdown is initiated
    verify(kmsValidationJobSubmissionSvc, never())
        .submitJob(any(), any(), anyLong(), any(), eq(groupId));
    verify(encryptionAtRestSvc, times(1)).incrementPromErrorCounter(eq(CloudProvider.AZURE), any());
    verify(encryptionAtRestSvc, times(1))
        .addToPendingShutdowns(eq(CloudProvider.AZURE), eq(groupId), any());
    verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), eq(true), any());
    verify(auditSvc, times(1)).saveAuditEvent(any());
  }

  @Test
  public void testValidateAzureKeyVaultFromAgent() throws SvcException {
    final KMSValidationJobSubmissionSvc KMSValidationJobSubmissionSvc =
        mock(KMSValidationJobSubmissionSvc.class);
    final NDSClusterSvc ndsClusterSvc = mock(NDSClusterSvc.class);
    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);

    final ObjectId groupId = new ObjectId();
    final Group group = mock(Group.class);
    doReturn(groupId).when(group).getId();
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(
                    KMSValidationJobSubmissionSvc,
                    ndsClusterSvc,
                    azureApiSvc,
                    mock(AppSettings.class)));

    final NDSAzureKeyVault ndsAzureKeyVault = mock(NDSAzureKeyVault.class);
    doReturn(true).when(ndsAzureKeyVault).isEnabled();

    doNothing().when(encryptionAtRestSvc).validateAzureKeyVaultKeyForEARValidation(any(), any());
    // validateAzureKeyVaultKey called when requireNetworking = false
    {
      doReturn(false).when(ndsAzureKeyVault).getRequirePrivateNetworking();
      doNothing().when(ndsAzureKeyVault).validate(any(), eq(group), any());
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(group, ndsAzureKeyVault, null, null);
      verify(encryptionAtRestSvc, never())
          .createValidateAzureKeyVaultJobForEARValidation(any(), any(), any(), any());
      verify(encryptionAtRestSvc, times(1)).validateAzureKeyVaultKeyForEARValidation(any(), any());
    }

    doReturn(true).when(ndsAzureKeyVault).getRequirePrivateNetworking();
    doReturn(Optional.of("keyId")).when(ndsAzureKeyVault).getKeyIdentifier();
    doReturn(Optional.of("clientId")).when(ndsAzureKeyVault).getClientID();
    doReturn(Optional.of("tenantId")).when(ndsAzureKeyVault).getTenantID();
    doReturn(Optional.of("secret")).when(ndsAzureKeyVault).getSecret();
    doReturn(Optional.of(SupportedAzureEnvironment.AZURE)).when(ndsAzureKeyVault).getEnvironment();
    doReturn(Optional.of("rg")).when(ndsAzureKeyVault).getResourceGroupName();
    doReturn(Optional.of("subscriptionId")).when(ndsAzureKeyVault).getSubscriptionID();
    doReturn(Optional.of("keyVault")).when(ndsAzureKeyVault).getKeyVaultName();
    doNothing().when(ndsAzureKeyVault).validate(any(), eq(group), any());
    doReturn(mock(Vault.class))
        .when(azureApiSvc)
        .findVault(any(), any(), any(), any(), any(), any(), any(), any());

    final AzureRegionName regionName = AzureRegionName.US_EAST_2;
    final AzureKeyVaultEARPrivateEndpoint privateEndpoint =
        mock(AzureKeyVaultEARPrivateEndpoint.class);
    doReturn(CloudProviderPrivateEndpoint.Status.ACTIVE).when(privateEndpoint).getStatus();
    doReturn(regionName).when(privateEndpoint).getRegionName();

    // skip validation if no active private endpoint regions
    {
      doReturn(List.of()).when(ndsAzureKeyVault).getPrivateEndpoints();
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(group, ndsAzureKeyVault, null, null);
      verify(encryptionAtRestSvc, never())
          .createValidateAzureKeyVaultJobForEARValidation(any(), any(), any(), any());
      verify(encryptionAtRestSvc, times(1)).validateAzureKeyVaultKeyForEARValidation(any(), any());
    }

    doReturn(List.of(privateEndpoint)).when(ndsAzureKeyVault).getPrivateEndpoints();

    ShardedClusterDescription cd =
        NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AZURE)
            .copy()
            .setEncryptionAtRestProvider(EncryptionAtRestProvider.NONE)
            .build();
    doReturn(List.of(cd)).when(ndsClusterSvc).getActiveClusterDescriptions(eq(groupId));

    // skip validation if cluster has encryption at rest disabled
    {
      encryptionAtRestSvc.validateAzureKeyVaultCredentials(group, ndsAzureKeyVault, null, null);
      verify(encryptionAtRestSvc, never())
          .createValidateAzureKeyVaultJobForEARValidation(any(), any(), any(), any());
    }

    cd = cd.copy().setEncryptionAtRestProvider(EncryptionAtRestProvider.AZURE).build();
    doReturn(List.of(cd)).when(ndsClusterSvc).getActiveClusterDescriptions(eq(groupId));

    doThrow(new IllegalStateException("no running instance"))
        .when(encryptionAtRestSvc)
        .getHostForAgentValidation(
            eq(groupId),
            eq(Set.of(NDSModelTestFactory.DEFAULT_CLUSTER_NAME)),
            eq(Set.of(AzureRegionName.US_EAST_2)),
            eq(CloudProvider.AZURE));
    assertThrows(
        IllegalStateException.class,
        () ->
            encryptionAtRestSvc.validateAzureKeyVaultCredentials(
                group, ndsAzureKeyVault, null, null));

    doReturn("selected-host")
        .when(encryptionAtRestSvc)
        .getHostForAgentValidation(
            eq(groupId),
            eq(Set.of(NDSModelTestFactory.DEFAULT_CLUSTER_NAME)),
            eq(Set.of(regionName)),
            eq(CloudProvider.AZURE));

    // agent validation is successful
    {
      final KMSValidationJobResponse jobResponse = mock(KMSValidationJobResponse.class);
      SettableFuture<KMSValidationJobResponse> future = SettableFuture.create();
      future.set(jobResponse);
      doReturn(future)
          .when(KMSValidationJobSubmissionSvc)
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));

      encryptionAtRestSvc.validateAzureKeyVaultCredentials(group, ndsAzureKeyVault, null, null);
      verify(KMSValidationJobSubmissionSvc, times(1))
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
    }

    // agent validation fails
    {
      SettableFuture<KMSValidationJobResponse> future = SettableFuture.create();
      doReturn(future)
          .when(KMSValidationJobSubmissionSvc)
          .submitJob(any(), any(), anyLong(), any(), eq(groupId));
      future.setException(new InterruptedException());
      final SvcException exception =
          assertThrows(
              SvcException.class,
              () ->
                  encryptionAtRestSvc.validateAzureKeyVaultCredentials(
                      group, ndsAzureKeyVault, null, null));
      assertEquals(NDSErrorCode.KMS_VALIDATION_JOB_FAILED, exception.getErrorCode());
    }

    // agent validation times out
    {
      final AgentJob agentJob = mock(AgentJob.class);
      SettableFuture<KMSValidationJobResponse> future = SettableFuture.create();
      doReturn(future)
          .when(KMSValidationJobSubmissionSvc)
          .submitJob(any(), any(), anyLong(), any(), any());

      final ExecutorService executor = Executors.newSingleThreadExecutor();
      final Future<SvcException> futureException =
          executor.submit(
              () -> {
                try {
                  KMSValidationJobSubmissionSvc.submitJob(
                          agentJob, CloudProvider.AZURE, 15, TimeUnit.MILLISECONDS, groupId)
                      .get(15, TimeUnit.MILLISECONDS); // Notice the reduced time unit
                  return null;
                } catch (TimeoutException e) {
                  return new SvcException(NDSErrorCode.VALIDATE_KMS_JOB_TIMED_OUT);
                } catch (InterruptedException | ExecutionException e) {
                  return new SvcException(NDSErrorCode.KMS_VALIDATION_JOB_FAILED);
                }
              });

      try {
        final SvcException exception = futureException.get();
        assertEquals(NDSErrorCode.VALIDATE_KMS_JOB_TIMED_OUT, exception.getErrorCode());
      } catch (InterruptedException | ExecutionException pE) {
        fail("Unexpected exception: " + pE.getMessage());
      }
    }
  }

  @Test
  public void testValidateAzureKeyVaultFromAgentForBackup() throws SvcException {
    final KMSValidationJobSubmissionSvc KMSValidationJobSubmissionSvc =
        mock(KMSValidationJobSubmissionSvc.class);
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(KMSValidationJobSubmissionSvc, mock(AppSettings.class)));

    final ObjectId groupId = new ObjectId();
    final String clusterName = "clusterName";
    final NDSAzureKeyVault ndsAzureKeyVault = mock(NDSAzureKeyVault.class);
    doReturn(Optional.of("keyIdentifier")).when(ndsAzureKeyVault).getKeyIdentifier();
    final byte[] bytesToEncrypt = "test".getBytes();

    // no active private endpoint regions
    doReturn(List.of()).when(ndsAzureKeyVault).getPrivateEndpoints();
    SvcException error =
        assertThrows(
            SvcException.class,
            () ->
                encryptionAtRestSvc.azureEncryptBytesFromAgentWithKeyVault(
                    groupId, clusterName, ndsAzureKeyVault, "fake bytes".getBytes()));
    assertEquals(
        NDSErrorCode.NO_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_CONNECTIONS_EXIST,
        error.getErrorCode());

    final AzureKeyVaultEARPrivateEndpoint privateEndpoint =
        mock(AzureKeyVaultEARPrivateEndpoint.class);
    doReturn(CloudProviderPrivateEndpoint.Status.ACTIVE).when(privateEndpoint).getStatus();
    doReturn(AzureRegionName.US_EAST_2).when(privateEndpoint).getRegionName();
    doReturn(List.of(privateEndpoint)).when(ndsAzureKeyVault).getPrivateEndpoints();

    // no instance to run the validation job
    doThrow(new IllegalStateException("no running instance"))
        .when(encryptionAtRestSvc)
        .getHostForAgentValidation(
            eq(groupId),
            eq(Set.of(clusterName)),
            eq(Set.of(AzureRegionName.US_EAST_2)),
            eq(CloudProvider.AZURE));
    assertThrows(
        IllegalStateException.class,
        () ->
            encryptionAtRestSvc.azureEncryptBytesFromAgentWithKeyVault(
                groupId, clusterName, ndsAzureKeyVault, bytesToEncrypt));

    doReturn("selected-hostname")
        .when(encryptionAtRestSvc)
        .getHostForAgentValidation(
            eq(groupId),
            eq(Set.of(clusterName)),
            eq(Set.of(AzureRegionName.US_EAST_2)),
            eq(CloudProvider.AZURE));
    // agent validation fails
    SettableFuture<KMSValidationJobResponse> future = SettableFuture.create();
    doReturn(future)
        .when(KMSValidationJobSubmissionSvc)
        .submitJob(any(), any(), anyLong(), any(), eq(groupId));
    future.setException(new SvcException(NDSErrorCode.KMS_VALIDATION_JOB_FAILED));
    error =
        assertThrows(
            SvcException.class,
            () ->
                encryptionAtRestSvc.azureEncryptBytesFromAgentWithKeyVault(
                    groupId, clusterName, ndsAzureKeyVault, bytesToEncrypt));
    assertEquals(NDSErrorCode.KMS_VALIDATION_JOB_FAILED, error.getErrorCode());

    // agent validation succeeds
    final byte[] encryptedBytes = Base64.getDecoder().decode("test");
    final KMSValidationJobResponse KMSValidationJobResponse = mock(KMSValidationJobResponse.class);
    doReturn("test").when(KMSValidationJobResponse).getEncryptedBytes();
    future = SettableFuture.create();
    future.set(KMSValidationJobResponse);
    doReturn(future)
        .when(KMSValidationJobSubmissionSvc)
        .submitJob(any(), any(), anyLong(), any(), eq(groupId));
    assertArrayEquals(
        encryptedBytes,
        encryptionAtRestSvc.azureEncryptBytesFromAgentWithKeyVault(
            groupId, clusterName, ndsAzureKeyVault, bytesToEncrypt));
  }

  @Test
  public void validateGoogleCloudKMSCredentials() throws SvcException {
    final GCPApiSvc gcpApiSvc = mock(GCPApiSvc.class);
    final AppSettings appSettings = mock(AppSettings.class);
    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);
    final OrganizationSvc organizationSvc = mock(OrganizationSvc.class);
    final ObjectId groupId = new ObjectId();
    final NDSEncryptionAtRest ndsEncryptionAtRest = mock(NDSEncryptionAtRest.class);
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(gcpApiSvc, appSettings, ndsGroupDao, organizationSvc));

    final Group group = mock(Group.class);
    doReturn(false).when(group).useCNRegionsOnly();
    final NDSGroup ndsGroup = NDSModelTestFactory.getGCPMockedGroup(groupId);
    doReturn(Optional.of(ndsGroup)).when(ndsGroupDao).find(any());
    // Test disabled Google Cloud KMS model
    final NDSGoogleCloudKMS googleCloudKMSDisabled = new NDSGoogleCloudKMS();
    doReturn(ndsEncryptionAtRest).when(ndsGroup).getEncryptionAtRest();
    doReturn(googleCloudKMSDisabled).when(ndsEncryptionAtRest).getGoogleCloudKMS();

    encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
        group, googleCloudKMSDisabled, RegionUsageRestrictions.NONE);
    verify(encryptionAtRestSvc, never()).validateGoogleCloudKMSKey(any(), any(), any(), any());

    // Test invalid Google Cloud KMS model
    final NDSGoogleCloudKMS googleCloudKMSInvalid =
        new NDSGoogleCloudKMS.Builder().setEnabled(true).build();
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMSInvalid, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
    }
    verify(encryptionAtRestSvc, never()).validateGoogleCloudKMSKey(any(), any(), any(), any());

    // Test valid Google Cloud KMS model
    final NDSGoogleCloudKMS googleCloudKMS =
        new NDSGoogleCloudKMS.Builder()
            .setEnabled(true)
            .setServiceAccountKey("{ \"secret\": \"stuff\" }")
            .setKeyVersionResourceID(
                "projects/p/locations/us/keyRings/keyRinkr/cryptoKeys/k/cryptoKeyVersions/1")
            .build();

    // Test bad API calls
    // Test no authorization for key access
    doThrow(new GCPApiException(CommonErrorCode.NO_AUTHORIZATION))
        .when(gcpApiSvc)
        .findKeyVersion(eq(null), any(String.class), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_GOOGLE_CLOUD_CREDENTIALS, e.getErrorCode());
    }

    // Test missing key version
    doThrow(new GCPApiException(CommonErrorCode.NOT_FOUND))
        .when(gcpApiSvc)
        .findKeyVersion(eq(null), any(String.class), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.GOOGLE_CLOUD_KMS_KEY_VERSION_NOT_FOUND, e.getErrorCode());
    }

    // Test failed precondition
    doThrow(new GCPApiException(GCPErrorCode.FAILED_PRECONDITION))
        .when(gcpApiSvc)
        .findKeyVersion(eq(null), any(String.class), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.GOOGLE_CLOUD_FAILED_PRECONDITION, e.getErrorCode());
    }

    // Test access not configured
    doThrow(new GCPApiException(GCPErrorCode.ACCESS_NOT_CONFIGURED))
        .when(gcpApiSvc)
        .findKeyVersion(eq(null), any(String.class), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.GOOGLE_CLOUD_ACCESS_NOT_CONFIGURED, e.getErrorCode());
    }

    // Test valid API calls
    final CryptoKeyVersion keyVersion = mock(CryptoKeyVersion.class);
    doReturn(keyVersion).when(gcpApiSvc).findKeyVersion(eq(null), any(String.class), any(), any());

    // Test key version pending generation
    doReturn("PENDING_GENERATION").when(keyVersion).getState();
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.GOOGLE_CLOUD_KMS_KEY_VERSION_PENDING_GENERATION, e.getErrorCode());
    }

    // Test key version destroyed
    doReturn("DESTROYED").when(keyVersion).getState();
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.GOOGLE_CLOUD_KMS_KEY_VERSION_DESTROYED, e.getErrorCode());
    }

    // Test key version destroy scheduled
    doReturn("DESTROY_SCHEDULED").when(keyVersion).getState();
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.GOOGLE_CLOUD_KMS_KEY_VERSION_DESTROY_SCHEDULED, e.getErrorCode());
    }

    // Test key version disabled
    doReturn("DISABLED").when(keyVersion).getState();
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.GOOGLE_CLOUD_KMS_KEY_VERSION_DISABLED, e.getErrorCode());
    }

    // Test key version enabled
    doReturn("ENABLED").when(keyVersion).getState();

    // Test no authorization for encryption
    doThrow(new GCPApiException(CommonErrorCode.NO_AUTHORIZATION))
        .when(gcpApiSvc)
        .encryptData(eq(null), any(String.class), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT, e.getErrorCode());
    }

    // Test failed precondition for encryption
    doThrow(new GCPApiException(GCPErrorCode.FAILED_PRECONDITION))
        .when(gcpApiSvc)
        .encryptData(eq(null), any(String.class), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.GOOGLE_CLOUD_FAILED_PRECONDITION, e.getErrorCode());
    }

    // Test failed to load credentials
    doThrow(new GCPApiException(GCPErrorCode.FAILED_TO_LOAD_CREDENTIALS))
        .when(gcpApiSvc)
        .encryptData(eq(null), any(String.class), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_GOOGLE_CLOUD_CREDENTIALS, e.getErrorCode());
    }

    // Test access not configured
    doThrow(new GCPApiException(GCPErrorCode.ACCESS_NOT_CONFIGURED))
        .when(gcpApiSvc)
        .encryptData(eq(null), any(String.class), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.GOOGLE_CLOUD_ACCESS_NOT_CONFIGURED, e.getErrorCode());
    }

    // Test no authorization for decryption
    doReturn("encrypted data")
        .when(gcpApiSvc)
        .encryptData(eq(null), any(String.class), any(), any(), any(), any());
    doThrow(new GCPApiException(CommonErrorCode.NO_AUTHORIZATION))
        .when(gcpApiSvc)
        .decryptData(eq(null), any(String.class), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT, e.getErrorCode());
    }

    // Test failed precondition for decryption
    doThrow(new GCPApiException(GCPErrorCode.FAILED_PRECONDITION))
        .when(gcpApiSvc)
        .decryptData(eq(null), any(String.class), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.GOOGLE_CLOUD_FAILED_PRECONDITION, e.getErrorCode());
    }

    // Test failed to load credentials
    doThrow(new GCPApiException(GCPErrorCode.FAILED_TO_LOAD_CREDENTIALS))
        .when(gcpApiSvc)
        .decryptData(eq(null), any(String.class), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.INVALID_GOOGLE_CLOUD_CREDENTIALS, e.getErrorCode());
    }

    // Test access not configured
    doThrow(new GCPApiException(GCPErrorCode.ACCESS_NOT_CONFIGURED))
        .when(gcpApiSvc)
        .decryptData(eq(null), any(String.class), any(), any(), any(), any());
    try {
      encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
          group, googleCloudKMS, RegionUsageRestrictions.NONE);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.GOOGLE_CLOUD_ACCESS_NOT_CONFIGURED, e.getErrorCode());
    }

    // Test success
    doReturn("This text is for testing")
        .when(gcpApiSvc)
        .decryptData(eq(null), any(String.class), any(), any(), any(), any());
    encryptionAtRestSvc.validateGoogleCloudKMSCredentials(
        group, googleCloudKMS, RegionUsageRestrictions.NONE);
  }

  @Test
  public void validateGoogleCloudKMSKey_networkAccessDenied_throwsSecurityPolicyViolated() {
    // Arrange
    final GCPApiSvc gcpApiSvc = mock(GCPApiSvc.class);
    final AppSettings appSettings = mock(AppSettings.class);
    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);

    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        new NDSEncryptionAtRestSvc(
            mock(AWSApiSvc.class),
            mock(AzureApiSvc.class),
            gcpApiSvc,
            mock(NDSClusterSvc.class),
            mock(NDSGroupSvc.class),
            ndsGroupDao,
            mock(GroupSvc.class),
            mock(OrganizationSvc.class),
            mock(AuditSvc.class),
            mock(NDSEncryptionAtRestEmailSvc.class),
            mock(NDSCloudProviderAccessSvc.class),
            mock(PointsOfPresenceSvc.class),
            appSettings,
            mock(ReplicaSetHardwareSvc.class),
            mock(AzureCloudProviderContainerSvc.class),
            mock(KMSValidationJobSubmissionSvc.class),
            mock(AutomationAgentAuditSvc.class));

    final CryptoKeyVersion keyVersion = mock(CryptoKeyVersion.class);
    doReturn("ENABLED").when(keyVersion).getState();
    doReturn(keyVersion).when(gcpApiSvc).findKeyVersion(eq(null), any(String.class), any(), any());

    final JSONObject responseContent = new JSONObject();
    responseContent.put(
        "message",
        "Request is prohibited by organization's policy. vpcServiceControlsUniqueIdentifier:"
            + " lT4AYqtVkuPKKRZvVWE0C7AObkd7AVEmCEn-fa8iJtwpfJFUM6yLBBIiGX3uXFslgK9ksAPDkWxw6OAu");
    doThrow(new GCPApiException(responseContent, CommonErrorCode.NO_AUTHORIZATION))
        .when(gcpApiSvc)
        .encryptData(eq(null), any(String.class), any(), any(), any(), any());

    // Act & Assert
    SvcException exception =
        assertThrows(
            SvcException.class,
            () -> {
              encryptionAtRestSvc.validateGoogleCloudKMSKey(
                  "test-service-account-key", null, "test-key", null);
            });

    assertEquals(NDSErrorCode.GOOGLE_CLOUD_SECURITY_POLICY_VIOLATED, exception.getErrorCode());
  }

  @Test
  public void validateGoogleCloudKMSKey_encryptRegularNoAuth_throwsNoPermissionToEncryptDecrypt() {
    // Arrange
    final GCPApiSvc gcpApiSvc = mock(GCPApiSvc.class);
    final AppSettings appSettings = mock(AppSettings.class);
    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);

    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        new NDSEncryptionAtRestSvc(
            mock(AWSApiSvc.class),
            mock(AzureApiSvc.class),
            gcpApiSvc,
            mock(NDSClusterSvc.class),
            mock(NDSGroupSvc.class),
            ndsGroupDao,
            mock(GroupSvc.class),
            mock(OrganizationSvc.class),
            mock(AuditSvc.class),
            mock(NDSEncryptionAtRestEmailSvc.class),
            mock(NDSCloudProviderAccessSvc.class),
            mock(PointsOfPresenceSvc.class),
            appSettings,
            mock(ReplicaSetHardwareSvc.class),
            mock(AzureCloudProviderContainerSvc.class),
            mock(KMSValidationJobSubmissionSvc.class),
            mock(AutomationAgentAuditSvc.class));

    final CryptoKeyVersion keyVersion = mock(CryptoKeyVersion.class);
    doReturn("ENABLED").when(keyVersion).getState();
    doReturn(keyVersion).when(gcpApiSvc).findKeyVersion(eq(null), any(String.class), any(), any());

    final JSONObject responseContent = new JSONObject();
    responseContent.put("message", "Access denied for some other reason");
    doThrow(new GCPApiException(responseContent, CommonErrorCode.NO_AUTHORIZATION))
        .when(gcpApiSvc)
        .encryptData(eq(null), any(String.class), any(), any(), any(), any());

    // Act & Assert
    SvcException exception =
        assertThrows(
            SvcException.class,
            () -> {
              encryptionAtRestSvc.validateGoogleCloudKMSKey(
                  "test-service-account-key", null, "test-key", null);
            });

    assertEquals(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT, exception.getErrorCode());
  }

  @Test
  public void testHandleValidationException() {
    final AuditSvc auditSvc = mock(AuditSvc.class);
    final GroupSvc groupSvc = mock(GroupSvc.class);
    final NDSEncryptionAtRestEmailSvc ndsEncryptionAtRestEmailSvc =
        mock(NDSEncryptionAtRestEmailSvc.class);
    final NDSClusterSvc ndsClusterSvc = mock(NDSClusterSvc.class);
    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);
    final NDSEncryptionAtRestSvc ndsEncryptionAtRestSvc =
        UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
            .withArgs(auditSvc, ndsClusterSvc, ndsEncryptionAtRestEmailSvc, ndsGroupDao, groupSvc);

    // Not a SvcException - return false
    assertFalse(
        ndsEncryptionAtRestSvc.handleValidationException(
            new IllegalArgumentException(), ObjectId.get(), CloudProvider.AWS, "AWS KMS"));

    // SvcException with unexpected error code - return false
    assertFalse(
        ndsEncryptionAtRestSvc.handleValidationException(
            new SvcException(NDSErrorCode.INVALID_ARGUMENT),
            ObjectId.get(),
            CloudProvider.AWS,
            "AWS KMS"));

    assertFalse(
        ndsEncryptionAtRestSvc.handleValidationException(
            new SvcException(NDSErrorCode.GOOGLE_CLOUD_FAILED_PRECONDITION),
            ObjectId.get(),
            CloudProvider.GCP,
            "Google Cloud KMS"));

    // internal error - not expected  return false
    assertFalse(
        ndsEncryptionAtRestSvc.handleValidationException(
            new SvcException(NDSErrorCode.INTERNAL),
            ObjectId.get(),
            CloudProvider.AZURE,
            "Azure Key Vault"));

    // SvcException with expected error code - return true
    final Group group = mock(Group.class);
    doReturn(group).when(groupSvc).findById(any());

    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn(List.of(clusterDescription))
        .when(ndsClusterSvc)
        .getEncryptedClustersByProvider(any(), any());

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    final NDSEncryptionAtRest encryptionAtRest = mock(NDSEncryptionAtRest.class);
    doReturn(encryptionAtRest).when(ndsGroup).getEncryptionAtRest();
    doReturn(Optional.of(ndsGroup)).when(ndsGroupDao).find(any());

    doNothing()
        .when(ndsEncryptionAtRestEmailSvc)
        .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());

    doNothing().when(auditSvc).saveAuditEvent(any());

    for (int i = 0; i < NDSEncryptionAtRestSvc.EXPECTED_KMS_ERRORS.size(); i++) {
      final ErrorCode errorCode = NDSEncryptionAtRestSvc.EXPECTED_KMS_ERRORS.get(i);
      assertTrue(
          ndsEncryptionAtRestSvc.handleValidationException(
              new SvcException(errorCode), ObjectId.get(), CloudProvider.AWS, "AWS KMS"));

      verify(ndsGroupDao, times(i + 1)).find(any());
      verify(ndsClusterSvc, times(i + 1)).getEncryptedClustersByProvider(any(), any());
      verify(ndsEncryptionAtRestEmailSvc, times(i + 1))
          .submitEncryptionAtRestValidationFailEmail(any(), any(), any(), any(), any());
    }
  }

  @Test
  public void testValidateAzureKeyVaultKey() throws Exception {
    final AzureApiSvc azureApiSvc = mock(AzureApiSvc.class);

    final NDSEncryptionAtRestSvc ndsEncryptionAtRestSvc =
        UnitTestUtils.create(NDSEncryptionAtRestSvc.class).withArgs(azureApiSvc);

    doReturn(null)
        .when(azureApiSvc)
        .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());

    final String clientId = "clientId";
    final String tenantId = "tenantId";
    final String secret = "shhhdontsayaword";
    final SupportedAzureEnvironment environment = SupportedAzureEnvironment.AZURE;
    final String resourceGroupName = "resourceGroupName";
    final String subscriptionId = "subscriptionId";
    final String keyVaultName = "keyVaultName";
    final String keyIdentifier = "keyIdentifier";

    // key is null - error
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_FOUND, pE.getErrorCode());
    }
    verify(azureApiSvc, times(1))
        .findKey(
            eq(clientId),
            eq(tenantId),
            eq(secret),
            eq(environment.getAzureEnvironment()),
            eq(resourceGroupName),
            eq(subscriptionId),
            eq(keyVaultName),
            eq(keyIdentifier),
            any());

    // key not enabled - error
    final Key key = mock(Key.class);

    doReturn(key)
        .when(azureApiSvc)
        .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());

    final KeyProperties keyAttributes = mock(KeyProperties.class);
    doReturn(keyAttributes).when(key).attributes();
    doReturn(false).when(keyAttributes).isEnabled();

    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_ENABLED, pE.getErrorCode());
    }

    // key not yet active - error
    doReturn(true).when(keyAttributes).isEnabled();
    doReturn(OffsetDateTime.now().plusDays(10)).when(keyAttributes).getNotBefore();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_ACTIVE, pE.getErrorCode());
    }

    // key expired - error
    doReturn(OffsetDateTime.now().minusDays(10)).when(keyAttributes).getNotBefore();
    doReturn(OffsetDateTime.now().minusDays(5)).when(keyAttributes).getExpiresOn();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.AZURE_KEY_VAULT_KEY_EXPIRED, pE.getErrorCode());
    }

    // Azure API Errors
    final AzureApiException azureApiException = mock(AzureApiException.class);
    doThrow(azureApiException)
        .when(azureApiSvc)
        .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());

    // no authorization
    doReturn(CommonErrorCode.NO_AUTHORIZATION).when(azureApiException).getErrorCode();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_AZURE_CREDENTIALS, pE.getErrorCode());
    }

    // forbidden
    doReturn(CommonErrorCode.FORBIDDEN).when(azureApiException).getErrorCode();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_AZURE_CREDENTIALS, pE.getErrorCode());
    }

    // forbidden by firewall
    doReturn(AzureErrorCode.FORBIDDEN_BY_FIREWALL).when(azureApiException).getErrorCode();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.AZURE_KEY_VAULT_FORBIDDEN_BY_FIREWALL, pE.getErrorCode());
    }

    // invalid request (non-transient error)
    doReturn(AzureErrorCode.INVALID_REQUEST).when(azureApiException).getErrorCode();
    doReturn("Some non-transient error message.").when(azureApiException).getMessage();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INVALID_AZURE_API_REQUEST, pE.getErrorCode());
    }

    // invalid request (transient error - null)
    doReturn("Some transient error message with \"error\":null\" in it.")
        .when(azureApiException)
        .getMessage();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INTERNAL, pE.getErrorCode());
    }

    // invalid request (transient error - temporarily_unavailable)
    doReturn("Some transient error message with \"error\":\"temporarily_unavailable\" in it.")
        .when(azureApiException)
        .getMessage();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INTERNAL, pE.getErrorCode());
    }

    // timeout
    doReturn(CommonErrorCode.TIMEOUT).when(azureApiException).getErrorCode();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(CommonErrorCode.TIMEOUT, pE.getErrorCode());
    }

    // any other error
    doReturn(CommonErrorCode.INTERRUPTED).when(azureApiException).getErrorCode();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INTERNAL, pE.getErrorCode());
    }

    // encrypt denied - error
    doReturn(key)
        .when(azureApiSvc)
        .findKey(any(), any(), any(), any(), any(), any(), any(), any(), any());
    doReturn(OffsetDateTime.now().plusDays(10)).when(keyAttributes).getExpiresOn();
    final AzureApiException encryptDecryptAzureApiException = mock(AzureApiException.class);
    doThrow(encryptDecryptAzureApiException)
        .when(azureApiSvc)
        .encryptStringWithKey(any(), any(), any(), any(), any(), any());
    doReturn(CommonErrorCode.FORBIDDEN).when(encryptDecryptAzureApiException).getErrorCode();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT, pE.getErrorCode());
    }

    // decrypt denied - error
    doReturn("encryptedData".getBytes())
        .when(azureApiSvc)
        .encryptStringWithKey(any(), any(), any(), any(), any(), any());
    doThrow(encryptDecryptAzureApiException)
        .when(azureApiSvc)
        .decryptStringWithKey(any(), any(), any(), any(), any(), any());
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT, pE.getErrorCode());
    }

    // unknown error - internal error
    doReturn(CommonErrorCode.INTERRUPTED).when(encryptDecryptAzureApiException).getErrorCode();
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INTERNAL, pE.getErrorCode());
    }

    // decrypted data doesn't match - error
    doReturn("decrypted data does not match".getBytes())
        .when(azureApiSvc)
        .decryptStringWithKey(any(), any(), any(), any(), any(), any());
    try {
      ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
          clientId,
          tenantId,
          secret,
          environment,
          resourceGroupName,
          subscriptionId,
          keyVaultName,
          keyIdentifier);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.INTERNAL, pE.getErrorCode());
    }

    // decrypted data matches - success
    doReturn(NDSEncryptionAtRestSvc.TEXT_TO_ENCRYPT.getBytes())
        .when(azureApiSvc)
        .decryptStringWithKey(any(), any(), any(), any(), any(), any());
    ndsEncryptionAtRestSvc.validateAzureKeyVaultKey(
        clientId,
        tenantId,
        secret,
        environment,
        resourceGroupName,
        subscriptionId,
        keyVaultName,
        keyIdentifier);
  }

  @Test
  public void testCopyEncryptionAtRest() {
    final NDSGroup sourceNdsGroup = mock(NDSGroup.class);
    final NDSEncryptionAtRest sourceEncryptionAtRest = mock(NDSEncryptionAtRest.class);
    doReturn(sourceEncryptionAtRest).when(sourceNdsGroup).getEncryptionAtRest();

    final NDSGroup targetNdsGroup = mock(NDSGroup.class);
    final ObjectId targetGroupId = ObjectId.get();
    doReturn(targetGroupId).when(targetNdsGroup).getGroupId();
    final NDSGroupDao ndsGroupDao = mock(NDSGroupDao.class);

    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(UnitTestUtils.create(NDSEncryptionAtRestSvc.class).withArgs(ndsGroupDao));
    encryptionAtRestSvc.copyEncryptionAtRest(sourceNdsGroup, targetNdsGroup);

    verify(ndsGroupDao, times(1)).setEncryptionAtRest(targetGroupId, sourceEncryptionAtRest);
    verifyNoMoreInteractions(ndsGroupDao);
  }

  @Test
  public void testShouldAllowClustersShutdown() {
    final AppSettings appSettings = mock(AppSettings.class);
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(UnitTestUtils.create(NDSEncryptionAtRestSvc.class).withArgs(appSettings));

    final ObjectId groupId = new ObjectId();
    final String groupIdHex = groupId.toHexString();

    // Test global enabled, no group-specific settings
    assertTrue(encryptionAtRestSvc.shouldAllowClustersShutdown(groupId));

    // Test global disabled
    doReturn(false)
        .when(appSettings)
        .getBoolProp(NDSEncryptionAtRestSvc.KMS_SHUTDOWN_ENABLED_SETTING, true);
    encryptionAtRestSvc.initializeKMSShutdownSettings();
    assertFalse(encryptionAtRestSvc.shouldAllowClustersShutdown(groupId));

    // Test excluded group overrides global enabled
    doReturn(true)
        .when(appSettings)
        .getBoolProp(NDSEncryptionAtRestSvc.KMS_SHUTDOWN_ENABLED_SETTING, true);
    doReturn(Set.of(groupIdHex))
        .when(appSettings)
        .getSetProperty(
            NDSEncryptionAtRestSvc.KMS_SHUTDOWN_EXCLUDED_GROUPS_SETTING,
            AppSettings.COMMA_DELIMITER);
    encryptionAtRestSvc.initializeKMSShutdownSettings();
    assertFalse(encryptionAtRestSvc.shouldAllowClustersShutdown(groupId));

    // Test included group overrides global disabled
    doReturn(false)
        .when(appSettings)
        .getBoolProp(NDSEncryptionAtRestSvc.KMS_SHUTDOWN_ENABLED_SETTING, true);
    doReturn(Set.of())
        .when(appSettings)
        .getSetProperty(
            NDSEncryptionAtRestSvc.KMS_SHUTDOWN_EXCLUDED_GROUPS_SETTING,
            AppSettings.COMMA_DELIMITER);
    doReturn(Set.of(groupIdHex))
        .when(appSettings)
        .getSetProperty(
            NDSEncryptionAtRestSvc.KMS_SHUTDOWN_GROUPS_SETTING, AppSettings.COMMA_DELIMITER);
    encryptionAtRestSvc.initializeKMSShutdownSettings();
    assertTrue(encryptionAtRestSvc.shouldAllowClustersShutdown(groupId));
  }

  @Test
  public void testProcessCollectedShutdowns_BelowThreshold() {
    final AppSettings appSettings = mock(AppSettings.class);
    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(UnitTestUtils.create(NDSEncryptionAtRestSvc.class).withArgs(appSettings, ndsGroupSvc));

    final ObjectId groupId1 = new ObjectId();
    final ObjectId groupId2 = new ObjectId();
    final NDSGroup ndsGroup1 = mock(NDSGroup.class);
    final NDSGroup ndsGroup2 = mock(NDSGroup.class);

    encryptionAtRestSvc.addToPendingShutdowns(CloudProvider.AWS, groupId1, ndsGroup1);
    encryptionAtRestSvc.addToPendingShutdowns(CloudProvider.AZURE, groupId2, ndsGroup2);

    // Process shutdowns - should proceed with shutdowns
    encryptionAtRestSvc.processCollectedShutdowns();

    // Verify circuit breaker was not triggered (since we're below threshold)
    verify(encryptionAtRestSvc, never()).triggerKMSShutdownCircuitBreaker();

    // Verify setValid is called for each group since we're below threshold
    verify(ndsGroupSvc, times(1)).setAwsKmsValid(eq(ndsGroup1), eq(false), any());
    verify(ndsGroupSvc, times(1)).setAzureKeyVaultValid(eq(ndsGroup2), eq(false), any());
  }

  @Test
  public void testProcessCollectedShutdowns_ExceedsThreshold() {
    final AppSettings appSettings = mock(AppSettings.class);
    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(UnitTestUtils.create(NDSEncryptionAtRestSvc.class).withArgs(appSettings, ndsGroupSvc));

    final ObjectId groupId1 = new ObjectId();
    final ObjectId groupId2 = new ObjectId();
    final ObjectId groupId3 = new ObjectId();
    final NDSGroup ndsGroup1 = mock(NDSGroup.class);
    final NDSGroup ndsGroup2 = mock(NDSGroup.class);
    final NDSGroup ndsGroup3 = mock(NDSGroup.class);

    // Initialize with threshold of 2 and ensure circuit breaker is globally enabled
    doReturn(2)
        .when(appSettings)
        .getIntProp(eq(NDSEncryptionAtRestSvc.KMS_SHUTDOWN_THRESHOLD_SETTING), eq(20));
    doReturn(true)
        .when(appSettings)
        .getBoolProp(eq(NDSEncryptionAtRestSvc.KMS_SHUTDOWN_ENABLED_SETTING), eq(true));
    encryptionAtRestSvc.initializeKMSShutdownSettings();

    doNothing().when(encryptionAtRestSvc).triggerKMSShutdownCircuitBreaker();

    // Add 3 groups to pending shutdowns (exceeds threshold of 2)
    encryptionAtRestSvc.addToPendingShutdowns(CloudProvider.AWS, groupId1, ndsGroup1);
    encryptionAtRestSvc.addToPendingShutdowns(CloudProvider.AZURE, groupId2, ndsGroup2);
    encryptionAtRestSvc.addToPendingShutdowns(CloudProvider.GCP, groupId3, ndsGroup3);

    // Process shutdowns - should trigger circuit breaker and prevent all shutdowns
    encryptionAtRestSvc.processCollectedShutdowns();

    // Verify circuit breaker was triggered
    verify(encryptionAtRestSvc, times(1)).triggerKMSShutdownCircuitBreaker();

    // Verify setValid is not called for any group since circuit breaker prevents shutdowns
    verify(ndsGroupSvc, never()).setAwsKmsValid(any(), anyBoolean(), any());
    verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), anyBoolean(), any());
    verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), anyBoolean(), any());
  }

  @Test
  public void testProcessCollectedShutdowns_CircuitBreakerAlreadyOpen() {
    final AppSettings appSettings = mock(AppSettings.class);
    final NDSGroupSvc ndsGroupSvc = mock(NDSGroupSvc.class);
    final NDSEncryptionAtRestSvc encryptionAtRestSvc =
        spy(UnitTestUtils.create(NDSEncryptionAtRestSvc.class).withArgs(appSettings, ndsGroupSvc));

    final ObjectId groupId1 = new ObjectId();
    final ObjectId groupId2 = new ObjectId();
    final ObjectId groupId3 = new ObjectId();
    final NDSGroup ndsGroup1 = mock(NDSGroup.class);
    final NDSGroup ndsGroup2 = mock(NDSGroup.class);
    final NDSGroup ndsGroup3 = mock(NDSGroup.class);

    // Circuit breaker already open
    doReturn(false)
        .when(appSettings)
        .getBoolProp(eq(NDSEncryptionAtRestSvc.KMS_SHUTDOWN_ENABLED_SETTING), eq(true));
    encryptionAtRestSvc.initializeKMSShutdownSettings();

    doNothing().when(encryptionAtRestSvc).triggerKMSShutdownCircuitBreaker();

    // Add 3 groups to pending shutdowns (exceeds threshold of 2)
    encryptionAtRestSvc.addToPendingShutdowns(CloudProvider.AWS, groupId1, ndsGroup1);
    encryptionAtRestSvc.addToPendingShutdowns(CloudProvider.AZURE, groupId2, ndsGroup2);
    encryptionAtRestSvc.addToPendingShutdowns(CloudProvider.GCP, groupId3, ndsGroup3);

    // Process shutdowns - should NOT trigger circuit breaker again since it's already open
    encryptionAtRestSvc.processCollectedShutdowns();

    // Verify circuit breaker was NOT triggered again
    verify(encryptionAtRestSvc, never()).triggerKMSShutdownCircuitBreaker();

    // Verify setValid is not called for any group since circuit breaker is already open
    verify(ndsGroupSvc, never()).setAwsKmsValid(any(), anyBoolean(), any());
    verify(ndsGroupSvc, never()).setAzureKeyVaultValid(any(), anyBoolean(), any());
    verify(ndsGroupSvc, never()).setGoogleCloudKmsValid(any(), anyBoolean(), any());
  }
}
