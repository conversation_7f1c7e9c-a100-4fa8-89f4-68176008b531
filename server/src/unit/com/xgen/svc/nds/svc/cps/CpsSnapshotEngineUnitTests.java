package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.common.model._public.misc.AuditDescription;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.PlanningType;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.Status;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.State;
import com.xgen.svc.nds.svc.planning.PlannedActionFactory;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.slf4j.Logger;

@MockitoSettings(strictness = Strictness.LENIENT)
public class CpsSnapshotEngineUnitTests {

  private CpsSnapshotEngine _cpsSnapshotEngine;

  @Mock private BackupSnapshotDao _backupSnapshotDao;
  @Mock private CpsSvc _cpsSvc;
  @Mock private CpsPitSvc _cpsPitSvc;
  @Mock private Logger _logger;
  @Mock private CpsPolicySvc _cpsPolicySvc;

  @Mock private Group _group;
  @Mock private BackupSnapshot _snapshot;
  @Mock private ClusterDescription _clusterDescription;

  private static final ObjectId PROJECT_ID = ObjectId.get();
  private static final ObjectId CLUSTER_UNIQUE_ID = ObjectId.get();

  @BeforeEach
  public void setUp() {
    _cpsSnapshotEngine =
        new CpsSnapshotEngine.Builder()
            .cpsSvc(_cpsSvc)
            .cpsPitSvc(_cpsPitSvc)
            .backupSnapshotDao(_backupSnapshotDao)
            .ndsBackupPolicySvc(_cpsPolicySvc)
            .logger(_logger)
            .build();

    doReturn(true).when(_cpsSvc).areConcurrentSnapshotsEnabled(_group);

    doReturn(State.WORKING).when(_clusterDescription).getState();
    doReturn(false).when(_clusterDescription).isDeleteRequested();
    doReturn(true).when(_clusterDescription).isDiskBackupEnabled();
    doReturn(false).when(_clusterDescription).isPaused();
    doReturn(PROJECT_ID).when(_clusterDescription).getGroupId();
    doReturn(CLUSTER_UNIQUE_ID).when(_clusterDescription).getUniqueId();
    doReturn(Collections.emptyList()).when(_clusterDescription).getRestoreJobIds();

    doReturn(BackupSnapshot.Status.QUEUED).when(_snapshot).getSnapshotStatus();
    doReturn(PlanningType.BLOCKING).when(_snapshot).getPlanningType();

    doReturn(List.of(_snapshot))
        .when(_backupSnapshotDao)
        .findActionableNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID);
    doReturn(Optional.of(_snapshot))
        .when(_backupSnapshotDao)
        .findQueuedNonCopySnapshotForPlanning(PROJECT_ID, CLUSTER_UNIQUE_ID);
  }

  @Test
  public void testShouldTakeSnapshotNow() {
    assertTrue(
        _cpsSnapshotEngine.shouldTakeSnapshotNow(
            _group, _clusterDescription, PlanningType.BLOCKING));
  }

  @Test
  public void testShouldTakeSnapshotNow_concurrent() {
    doReturn(PlanningType.CONCURRENT).when(_snapshot).getPlanningType();
    doReturn(true).when(_cpsSvc).areConcurrentSnapshotsEnabled(_group);

    assertTrue(
        _cpsSnapshotEngine.shouldTakeSnapshotNow(
            _group, _clusterDescription, PlanningType.CONCURRENT));
  }

  @Test
  public void testShouldTakeSnapshotNow_concurrentMismatch() {
    doReturn(true).when(_cpsSvc).areConcurrentSnapshotsEnabled(_group);

    assertFalse(
        _cpsSnapshotEngine.shouldTakeSnapshotNow(
            _group, _clusterDescription, PlanningType.CONCURRENT));
  }

  @Test
  public void testShouldTakeSnapshotNow_clusterPaused() {
    doReturn(true).when(_clusterDescription).isPaused();
    assertFalse(
        _cpsSnapshotEngine.shouldTakeSnapshotNow(
            _group, _clusterDescription, PlanningType.BLOCKING));
  }

  @Test
  public void testShouldTakeSnapshotNow_noFeatureFlag() {
    doReturn(PlanningType.CONCURRENT).when(_snapshot).getPlanningType();
    doReturn(false).when(_cpsSvc).areConcurrentSnapshotsEnabled(_group);

    assertFalse(
        _cpsSnapshotEngine.shouldTakeSnapshotNow(
            _group, _clusterDescription, PlanningType.CONCURRENT));
  }

  @Test
  public void testShouldTakeSnapshotNow_noSnapshots() {
    doReturn(Collections.emptyList())
        .when(_backupSnapshotDao)
        .findActionableNonCopyByCluster(PROJECT_ID, CLUSTER_UNIQUE_ID);
    doReturn(Optional.empty())
        .when(_backupSnapshotDao)
        .findQueuedNonCopySnapshotForPlanning(PROJECT_ID, CLUSTER_UNIQUE_ID);

    assertFalse(
        _cpsSnapshotEngine.shouldTakeSnapshotNow(
            _group, _clusterDescription, PlanningType.BLOCKING));
  }

  @Test
  public void testShouldTakeSnapshotNow_inProgressSnapshot() {
    doReturn(Status.IN_PROGRESS).when(_snapshot).getSnapshotStatus();
    doReturn(Optional.empty())
        .when(_backupSnapshotDao)
        .findQueuedNonCopySnapshotForPlanning(PROJECT_ID, CLUSTER_UNIQUE_ID);

    assertFalse(
        _cpsSnapshotEngine.shouldTakeSnapshotNow(
            _group, _clusterDescription, PlanningType.BLOCKING));
  }

  @Test
  public void testShouldTakeSnapshotNow_planningTypeMismatch() {
    doReturn(PlanningType.CONCURRENT).when(_snapshot).getPlanningType();

    assertFalse(
        _cpsSnapshotEngine.shouldTakeSnapshotNow(
            _group, _clusterDescription, PlanningType.BLOCKING));
  }

  @Test
  public void testShouldTakeSnapshotNow_restore() {
    doReturn(PlanningType.CONCURRENT).when(_snapshot).getPlanningType();
    doReturn(true).when(_cpsSvc).areConcurrentSnapshotsEnabled(_group);

    doReturn(List.of(new ObjectId())).when(_clusterDescription).getRestoreJobIds();

    assertFalse(
        _cpsSnapshotEngine.shouldTakeSnapshotNow(
            _group, _clusterDescription, PlanningType.CONCURRENT));
  }

  @Test
  public void testGetEarliestQueuedCopySnapshots() {
    final Map<ObjectId, List<ReplicaSetBackupSnapshot>> originalSnapshotToCopies = new HashMap<>();
    final BackupSnapshot originalSnapshot1 = mock(BackupSnapshot.class);
    final BackupSnapshot originalSnapshot2 = mock(BackupSnapshot.class);
    final BackupSnapshot originalSnapshot3 = mock(BackupSnapshot.class);
    final ReplicaSetBackupSnapshot copy1 = mock(ReplicaSetBackupSnapshot.class);
    final ReplicaSetBackupSnapshot copy2 = mock(ReplicaSetBackupSnapshot.class);
    final ReplicaSetBackupSnapshot copy3 = mock(ReplicaSetBackupSnapshot.class);
    final ReplicaSetBackupSnapshot copy4 = mock(ReplicaSetBackupSnapshot.class);
    final ReplicaSetBackupSnapshot copy5 = mock(ReplicaSetBackupSnapshot.class);
    when(originalSnapshot1.getId()).thenReturn(new ObjectId());
    when(originalSnapshot2.getId()).thenReturn(new ObjectId());
    when(originalSnapshot3.getId()).thenReturn(new ObjectId());
    originalSnapshotToCopies.put(originalSnapshot1.getId(), Arrays.asList(copy1, copy2, copy3));
    originalSnapshotToCopies.put(originalSnapshot2.getId(), Arrays.asList(copy4));
    originalSnapshotToCopies.put(originalSnapshot3.getId(), Arrays.asList(copy5));
    List<ReplicaSetBackupSnapshot> copies =
        _cpsSnapshotEngine.getEarliestQueuedCopySnapshots(originalSnapshotToCopies);
    assertEquals(copies.size(), 3);
  }

  @Test
  public void testGetCopyDiskBackupActions() {
    final ReplicaSetBackupSnapshot copy1 = mock(ReplicaSetBackupSnapshot.class);
    final ReplicaSetBackupSnapshot copy2 = mock(ReplicaSetBackupSnapshot.class);
    final ReplicaSetBackupSnapshot copy3 = mock(ReplicaSetBackupSnapshot.class);
    final ReplicaSetBackupSnapshot copy4 = mock(ReplicaSetBackupSnapshot.class);

    // copy snapshot 1, 2, 3 are replicaSet and copy snapshot 4 is sharded
    when(_backupSnapshotDao.findCopySnapshotsByStatus(any(), any(), any()))
        .thenReturn(Arrays.asList(copy1, copy2, copy3, copy4));
    final ObjectId originalSnapshotId1 = new ObjectId();
    final ObjectId originalSnapshotId2 = new ObjectId();
    when(copy1.getOriginalSnapshotId()).thenReturn(originalSnapshotId1);
    when(copy2.getOriginalSnapshotId()).thenReturn(originalSnapshotId1);
    when(copy3.getOriginalSnapshotId()).thenReturn(originalSnapshotId1);
    when(copy4.getOriginalSnapshotId()).thenReturn(originalSnapshotId2);

    when(copy1.isShard()).thenReturn(false);
    when(copy2.isShard()).thenReturn(false);
    when(copy3.isShard()).thenReturn(false);
    when(copy4.isShard()).thenReturn(true);
    when(copy4.getParentSnapshotId()).thenReturn(new ObjectId());

    final ReplicaSetBackupSnapshot originalSnapshot1 = mock(ReplicaSetBackupSnapshot.class);
    final ReplicaSetBackupSnapshot originalSnapshot2 = mock(ReplicaSetBackupSnapshot.class);
    when(originalSnapshot2.getParentSnapshotId()).thenReturn(new ObjectId());
    when(_backupSnapshotDao.findReplicaSetSnapshotById(originalSnapshotId2))
        .thenReturn(Optional.of(originalSnapshot2));
    final ClusterDescription cd = mock(ClusterDescription.class);
    when(cd.getGroupId()).thenReturn(new ObjectId());
    when(cd.getUniqueId()).thenReturn(new ObjectId());
    when(copy1.getClusterName()).thenReturn("cd");
    when(copy2.getClusterName()).thenReturn("cd");
    when(copy3.getClusterName()).thenReturn("cd");
    when(copy1.getId()).thenReturn(new ObjectId());
    when(copy2.getId()).thenReturn(new ObjectId());
    when(copy3.getId()).thenReturn(new ObjectId());

    PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    Pair<List<PlannedAction>, List<AuditDescription>> result =
        _cpsSnapshotEngine.getCopyDiskBackupActions(cd, plannedActionFactory);
    // assert contains 1 PreCopySnapshotMove, 1 PostCopySnapshotMove, and 3 CopySnapshotMoves
    assertEquals(result.getLeft().size(), 5);
    verify(plannedActionFactory, times(1)).forPreCopySnapshot(any(), any(), any());
    verify(plannedActionFactory, times(1)).forPostCopySnapshot(any(), any(), any());
    verify(plannedActionFactory, times(3)).forCopySnapshot(any(), any(), any());

    // all copy snapshots are sharded
    when(copy1.isShard()).thenReturn(true);
    when(copy2.isShard()).thenReturn(true);
    when(copy3.isShard()).thenReturn(true);
    when(_backupSnapshotDao.findReplicaSetSnapshotById(originalSnapshotId1))
        .thenReturn(Optional.of(originalSnapshot1));

    // the parent snapshot of originalSnapshot1 is created later than
    // the parent snapshot of originalSnapshot2
    when(originalSnapshot1.getParentSnapshotId()).thenReturn(new ObjectId());

    plannedActionFactory = mock(PlannedActionFactory.class);
    result = _cpsSnapshotEngine.getCopyDiskBackupActions(cd, plannedActionFactory);
    // assert contains 1 PreCopySnapshotMove, 1 PostCopySnapshotMove, and 1 CopySnapshotMove
    assertEquals(result.getLeft().size(), 3);
    verify(plannedActionFactory, times(1)).forPreCopySnapshot(any(), any(), any());
    verify(plannedActionFactory, times(1)).forPostCopySnapshot(any(), any(), any());
    verify(plannedActionFactory, times(1)).forCopySnapshot(any(), any(), any());

    // all copy snapshots are replicaSet
    when(copy1.isShard()).thenReturn(false);
    when(copy2.isShard()).thenReturn(false);
    when(copy3.isShard()).thenReturn(false);
    when(copy4.isShard()).thenReturn(false);

    plannedActionFactory = mock(PlannedActionFactory.class);
    result = _cpsSnapshotEngine.getCopyDiskBackupActions(cd, plannedActionFactory);
    // assert contains 1 PreCopySnapshotMove, 1 PostCopySnapshotMove, and 3 CopySnapshotMoves
    assertEquals(result.getLeft().size(), 5);
    verify(plannedActionFactory, times(1)).forPreCopySnapshot(any(), any(), any());
    verify(plannedActionFactory, times(1)).forPostCopySnapshot(any(), any(), any());
    verify(plannedActionFactory, times(3)).forCopySnapshot(any(), any(), any());
  }
}
