package com.xgen.svc.nds.svc.planning;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static com.xgen.cloud.nds.capacity._public.model.CapacityDenyListEntry.Status.CAPACITY_UNAVAILABLE;
import static com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareModelTestFactory.getAWSInstanceHardwareFull;
import static com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareModelTestFactory.getAzureInstanceHardwareFull;
import static com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareModelTestFactory.getGCPInstanceHardwareFull;
import static com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessGCPServiceAccount.ServiceAccountProvisionStatus;
import static com.xgen.svc.nds.svc.planning.BackupRestoreActionSvc.getPlanActionsForRestorePlanning;
import static com.xgen.svc.nds.svc.planning.PlanResult.shouldUpdateOSPolicyAndReboot;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.amazonaws.services.ec2.model.VolumeType;
import com.google.common.collect.Iterables;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.brs._public.model.BackupConfig;
import com.xgen.cloud.common.brs._public.model.BackupConfigState;
import com.xgen.cloud.common.brs._public.model.ReplicaSetBackupConfig;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.misc.AuditDescription;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.backupjob._public.model.CopySetting;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupRestoreJobPlanUnit;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot.PlanningType;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.RestoreTargetCluster;
import com.xgen.cloud.cps.restore._public.model.SLSRestoreJob;
import com.xgen.cloud.cps.restore._public.model.StreamingReplicaSetRestoreJob;
import com.xgen.cloud.cps.restore._public.model.VMBasedReplSetRestoreJob;
import com.xgen.cloud.deployment._public.model.AWSKMS;
import com.xgen.cloud.deployment._public.model.AWSKey;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.AzureKey;
import com.xgen.cloud.deployment._public.model.AzureKeyVault;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.EncryptionProviderType;
import com.xgen.cloud.deployment._public.model.EncryptionProviders;
import com.xgen.cloud.deployment._public.model.GoogleCloudKMS;
import com.xgen.cloud.deployment._public.model.GoogleCloudKey;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.PrometheusConfig;
import com.xgen.cloud.monitoring.topology._public.model.ClusterType;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostCluster;
import com.xgen.cloud.monitoring.topology._public.model.ReplicaSet;
import com.xgen.cloud.monitoring.topology._public.svc.HostLastPingSvc;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceHardware;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.AWSSubnet;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSCheckResult;
import com.xgen.cloud.nds.aws._public.model.admincapacity.AWSInstanceCapacitySpec;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSAutoScaling;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSMultiTargetConnectionRule;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkInterfaceEndpoint;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkTargetGroup;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSTenantEndpoint;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSTenantEndpointService;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceHardware;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceHardware.AzurePublicIPSKUType;
import com.xgen.cloud.nds.azure._public.model.AzureNDSDefaults;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzurePhysicalZoneId;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.admincapacity.AzureCheckResult;
import com.xgen.cloud.nds.azure._public.model.admincapacity.AzureInstanceCapacitySpec;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzurePrivateEndpoint;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzurePrivateLinkConnectionInboundNATRule;
import com.xgen.cloud.nds.capacity._public.model.azure.AzureCapacityDenyListEntry;
import com.xgen.cloud.nds.cloudprovider._public.model.BaseMultiTargetConnectionRule.NdsProcessPortTyped;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.Action;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.FieldDefs;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionalDedicatedCloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.ShardRegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.admincapacity.CheckCapacityRequest;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.BaseEndpointService.Status;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.CloudProviderPrivateEndpoint;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.DedicatedEndpointService;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.EndpointPrometheusStatus;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.PrivateLinkConnectionRule;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.PrivateLinkConnectionRule.Usage;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.TenantEndpoint;
import com.xgen.cloud.nds.common._public.model.BiConnectorReadPreference;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.HostnameScheme;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.SubdomainLevel;
import com.xgen.cloud.nds.common._public.model.MongoDBConfigType;
import com.xgen.cloud.nds.common._public.model.OS;
import com.xgen.cloud.nds.common._public.model.ReplicaSetScalingStrategy;
import com.xgen.cloud.nds.flex._public.model.FlexCloudProviderContainer;
import com.xgen.cloud.nds.flex._public.model.FlexTenantMigration;
import com.xgen.cloud.nds.flex._public.model.MigrationStatus;
import com.xgen.cloud.nds.free._public.model.FreeCloudProviderContainer;
import com.xgen.cloud.nds.free._public.model.FreeHardwareSpec;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceFamily;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceHardware;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction.ActionType;
import com.xgen.cloud.nds.planning.common._public.model.PlannedAction.MoveType;
import com.xgen.cloud.nds.planning.summary._public.model.cluster.MachineActionsDecisions;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.Cluster.ShardedCluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.BiConnector;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.State;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccess;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessGCPServiceAccount;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermissionList;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.cloud.nds.serverless._public.model.ServerlessCloudProviderContainer;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.pool.ServerlessMTMPool;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.EnvoyInstance;
import com.xgen.cloud.nds.serverless._public.model.loadbalancingdeployment.ServerlessLoadBalancingDeployment;
import com.xgen.cloud.nds.tenant._public.model.TenantCloudProviderContainer;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantBackupTask;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantRestore;
import com.xgen.cloud.nds.tenantupgrade._public.model.ServerlessUpgradeToDedicatedStatus;
import com.xgen.cloud.nds.tenantupgrade._public.model.TenantUpgradeStatus;
import com.xgen.module.common.planner.model.Move;
import com.xgen.module.common.planner.model.PlanContext;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFlexClusterDescriptionConfig;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFreeClusterDescriptionConfig;
import com.xgen.svc.nds.model.ServerlessDeploymentModelTestFactory;
import com.xgen.svc.nds.model.TenantPrivateNetworkingModelTestFactory;
import com.xgen.svc.nds.planner.DoServerlessToServerlessStreamingRestoreMove;
import com.xgen.svc.nds.planner.DoTenantUpgradeToServerlessStreamingRestoreMove;
import com.xgen.svc.nds.planner.DummyMove;
import com.xgen.svc.nds.planner.MoveProvider;
import com.xgen.svc.nds.planner.NDSPlanContext;
import com.xgen.svc.nds.planner.ProcessAutomationConfigPerClusterMove;
import com.xgen.svc.nds.security.svc.NDSACMESvc;
import com.xgen.svc.nds.serverless.model.ServerlessTestFactory;
import com.xgen.svc.nds.serverless.planner.ServerlessMoveProvider;
import com.xgen.svc.nds.svc.NDSCloudProviderContainerSvc;
import com.xgen.svc.nds.svc.NDSPrometheusEndpointSvc;
import com.xgen.svc.nds.svc.NDSPrometheusEndpointSvc.PrometheusEndpointForContainer;
import com.xgen.svc.nds.svc.cps.CpsPitSvc;
import com.xgen.svc.nds.svc.cps.CpsPolicySvc;
import com.xgen.svc.nds.svc.cps.CpsSnapshotEngine;
import com.xgen.svc.nds.svc.cps.CpsSvc;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.BasicBSONObject;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(CloudProviderExtension.class)
public class PlanResultUnitTests {

  private static final Logger LOGGER = LoggerFactory.getLogger(PlanResultUnitTests.class);

  @Test
  public void testGroupTenantClustersByCloudProvider() {
    final Cluster awsTenantCluster0 = mock(Cluster.class);
    final ClusterDescription tenantClusterAWSDescription0 = mock(ClusterDescription.class);
    final ObjectId awsClusterGroupId0 = new ObjectId();
    when(awsTenantCluster0.getClusterDescription()).thenReturn(tenantClusterAWSDescription0);
    when(tenantClusterAWSDescription0.isTenantCluster()).thenReturn(true);
    when(tenantClusterAWSDescription0.getBackingProvider()).thenReturn(CloudProvider.AWS);
    when(tenantClusterAWSDescription0.getHostnameSubdomainLevel())
        .thenReturn(InstanceHostname.SubdomainLevel.MONGODB);
    when(tenantClusterAWSDescription0.getGroupId()).thenReturn(awsClusterGroupId0);

    final Cluster awsTenantCluster1 = mock(Cluster.class);
    final ObjectId awsClusterGroupId1 = new ObjectId();
    final ClusterDescription tenantClusterAWSDescription1 = mock(ClusterDescription.class);
    when(awsTenantCluster1.getClusterDescription()).thenReturn(tenantClusterAWSDescription1);
    when(tenantClusterAWSDescription1.isTenantCluster()).thenReturn(true);
    when(tenantClusterAWSDescription1.getBackingProvider()).thenReturn(CloudProvider.AWS);
    when(tenantClusterAWSDescription1.getHostnameSubdomainLevel())
        .thenReturn(InstanceHostname.SubdomainLevel.MONGODB);
    when(tenantClusterAWSDescription1.getGroupId()).thenReturn(awsClusterGroupId1);

    final Cluster azureTenantCluster0 = mock(Cluster.class);
    final ClusterDescription tenantClusterAzureDescription = mock(ClusterDescription.class);
    final ObjectId azureClusterGroupId0 = new ObjectId();
    when(azureTenantCluster0.getClusterDescription()).thenReturn(tenantClusterAzureDescription);
    when(tenantClusterAzureDescription.isTenantCluster()).thenReturn(true);
    when(tenantClusterAzureDescription.getBackingProvider()).thenReturn(CloudProvider.AZURE);
    when(tenantClusterAzureDescription.getHostnameSubdomainLevel())
        .thenReturn(InstanceHostname.SubdomainLevel.MONGODB);
    when(tenantClusterAzureDescription.getGroupId()).thenReturn(azureClusterGroupId0);

    final Cluster gcpTenantCluster0 = mock(Cluster.class);
    final ClusterDescription tenantClusterGcpDescription = mock(ClusterDescription.class);
    final ObjectId gcpClusterGroupId0 = new ObjectId();
    when(gcpTenantCluster0.getClusterDescription()).thenReturn(tenantClusterGcpDescription);
    when(tenantClusterGcpDescription.isTenantCluster()).thenReturn(true);
    when(tenantClusterGcpDescription.getBackingProvider()).thenReturn(CloudProvider.GCP);
    when(tenantClusterGcpDescription.getHostnameSubdomainLevel())
        .thenReturn(InstanceHostname.SubdomainLevel.GCP);
    when(tenantClusterGcpDescription.getGroupId()).thenReturn(gcpClusterGroupId0);

    final List<Cluster> listOfCluster =
        List.of(awsTenantCluster0, awsTenantCluster1, azureTenantCluster0, gcpTenantCluster0);
    final Map<SubdomainLevel, List<Cluster>> groupTenantClustersByCloudProvider =
        NDSACMESvc.groupClustersBySubdomainLevel(listOfCluster);
    // 3 MONGODB level clusters (2 aws 1 azure)
    assertEquals(3, groupTenantClustersByCloudProvider.get(SubdomainLevel.MONGODB).size());
    // 1 PROVIDER level cluster
    assertEquals(1, groupTenantClustersByCloudProvider.get(SubdomainLevel.GCP).size());
    assertEquals(
        awsClusterGroupId0,
        groupTenantClustersByCloudProvider
            .get(SubdomainLevel.MONGODB)
            .get(0)
            .getClusterDescription()
            .getGroupId());
    assertEquals(
        awsClusterGroupId1,
        groupTenantClustersByCloudProvider
            .get(SubdomainLevel.MONGODB)
            .get(1)
            .getClusterDescription()
            .getGroupId());
    assertEquals(
        azureClusterGroupId0,
        groupTenantClustersByCloudProvider
            .get(SubdomainLevel.MONGODB)
            .get(2)
            .getClusterDescription()
            .getGroupId());
    assertEquals(
        gcpClusterGroupId0,
        groupTenantClustersByCloudProvider
            .get(SubdomainLevel.GCP)
            .get(0)
            .getClusterDescription()
            .getGroupId());
  }

  private Class<? extends TenantCloudProviderContainer>
      getTenantCloudProviderContainerClassByProvider(final CloudProvider pProvider) {
    return Optional.ofNullable(pProvider)
        .map(
            p ->
                switch (p) {
                  case FREE -> FreeCloudProviderContainer.class;
                  case SERVERLESS -> ServerlessCloudProviderContainer.class;
                  case FLEX -> FlexCloudProviderContainer.class;
                  default -> throw new IllegalArgumentException(p.name());
                })
        .orElse(null);
  }

  private void testGroupTenantContainersByBackingCloudProvider(
      final Set<CloudProvider> pProviders) {
    final Cluster awsTenantCluster0 = mock(Cluster.class);
    final ClusterDescription tenantClusterAWSDescription0 = mock(ClusterDescription.class);
    final String awsClusterName0 = "AWSCluster0";
    when(awsTenantCluster0.getClusterDescription()).thenReturn(tenantClusterAWSDescription0);
    when(tenantClusterAWSDescription0.isTenantCluster()).thenReturn(true);
    when(tenantClusterAWSDescription0.getBackingProvider()).thenReturn(CloudProvider.AWS);
    when(tenantClusterAWSDescription0.getName()).thenReturn(awsClusterName0);

    final Cluster awsTenantCluster1 = mock(Cluster.class);
    final ClusterDescription tenantClusterAWSDescription1 = mock(ClusterDescription.class);
    final String awsClusterName1 = "AWSCluster1";
    when(awsTenantCluster1.getClusterDescription()).thenReturn(tenantClusterAWSDescription1);
    when(tenantClusterAWSDescription1.isTenantCluster()).thenReturn(true);
    when(tenantClusterAWSDescription1.getBackingProvider()).thenReturn(CloudProvider.AWS);
    when(tenantClusterAWSDescription1.getName()).thenReturn("AWSCluster1");

    final Cluster azureTenantCluster0 = mock(Cluster.class);
    final ClusterDescription tenantClusterAzureDescription0 = mock(ClusterDescription.class);
    final String azureClusterName0 = "AzureCluster0";
    when(azureTenantCluster0.getClusterDescription()).thenReturn(tenantClusterAzureDescription0);
    when(tenantClusterAzureDescription0.isTenantCluster()).thenReturn(true);
    when(tenantClusterAzureDescription0.getBackingProvider()).thenReturn(CloudProvider.AZURE);
    when(tenantClusterAzureDescription0.getName()).thenReturn(azureClusterName0);

    final Cluster azureTenantCluster1 = mock(Cluster.class);
    final ClusterDescription tenantClusterAzureDescription1 = mock(ClusterDescription.class);
    final String azureClusterName1 = "AzureCluster1";
    when(azureTenantCluster1.getClusterDescription()).thenReturn(tenantClusterAzureDescription1);
    when(tenantClusterAzureDescription1.getName()).thenReturn(azureClusterName1);

    final List<Cluster> listOfCluster =
        List.of(awsTenantCluster0, awsTenantCluster1, azureTenantCluster0, azureTenantCluster1);

    final boolean isMultipleProviders = pProviders.size() > 1;
    final CloudProvider provider =
        !isMultipleProviders ? pProviders.stream().findFirst().get() : null;
    final Class<? extends TenantCloudProviderContainer> containerClass =
        getTenantCloudProviderContainerClassByProvider(provider);

    final TenantCloudProviderContainer awsTenantCPContainer0 =
        isMultipleProviders ? mock(FreeCloudProviderContainer.class) : mock(containerClass);
    final TenantCloudProviderContainer awsTenantCPContainer1 =
        isMultipleProviders ? mock(ServerlessCloudProviderContainer.class) : mock(containerClass);
    final TenantCloudProviderContainer azureTenantCPContainer0 =
        isMultipleProviders ? mock(FlexCloudProviderContainer.class) : mock(containerClass);
    doReturn(isMultipleProviders ? CloudProvider.FREE : provider)
        .when(awsTenantCPContainer0)
        .getCloudProvider();
    doReturn(isMultipleProviders ? CloudProvider.SERVERLESS : provider)
        .when(awsTenantCPContainer1)
        .getCloudProvider();
    doReturn(isMultipleProviders ? CloudProvider.FLEX : provider)
        .when(azureTenantCPContainer0)
        .getCloudProvider();
    when(awsTenantCPContainer0.getTenantClusterName()).thenReturn(awsClusterName0);
    when(awsTenantCPContainer1.getTenantClusterName()).thenReturn(awsClusterName1);
    when(azureTenantCPContainer0.getTenantClusterName()).thenReturn(azureClusterName0);

    final List<CloudProviderContainer> listOfCloudProviderContainers =
        List.of(awsTenantCPContainer0, awsTenantCPContainer1, azureTenantCPContainer0);
    final Map<CloudProvider, List<CloudProviderContainer>> groupCloudProviderContainersByProvider;
    if (isMultipleProviders) {
      groupCloudProviderContainersByProvider =
          PlanResult.groupTenantContainersByBackingCloudProvider(
              listOfCloudProviderContainers, listOfCluster);
    } else {
      if (provider == CloudProvider.FREE) {
        groupCloudProviderContainersByProvider =
            PlanResult.groupFreeContainersByBackingCloudProvider(
                listOfCloudProviderContainers, listOfCluster);
      } else if (provider == CloudProvider.SERVERLESS) {
        groupCloudProviderContainersByProvider =
            PlanResult.groupServerlessContainersByBackingCloudProvider(
                listOfCloudProviderContainers, listOfCluster);
      } else {
        groupCloudProviderContainersByProvider =
            PlanResult.groupFlexContainersByBackingCloudProvider(
                listOfCloudProviderContainers, listOfCluster);
      }
    }

    assertEquals(2, groupCloudProviderContainersByProvider.get(CloudProvider.AWS).size());
    assertEquals(1, groupCloudProviderContainersByProvider.get(CloudProvider.AZURE).size());
    assertEquals(
        awsClusterName0,
        ((TenantCloudProviderContainer)
                groupCloudProviderContainersByProvider.get(CloudProvider.AWS).get(0))
            .getTenantClusterName());
    assertEquals(
        awsClusterName1,
        ((TenantCloudProviderContainer)
                groupCloudProviderContainersByProvider.get(CloudProvider.AWS).get(1))
            .getTenantClusterName());
    assertEquals(
        azureClusterName0,
        ((TenantCloudProviderContainer)
                groupCloudProviderContainersByProvider.get(CloudProvider.AZURE).get(0))
            .getTenantClusterName());

    if (!isMultipleProviders) {
      final Class<? extends TenantCloudProviderContainer> otherContainerClass;
      if (provider == CloudProvider.FREE) {
        otherContainerClass = ServerlessCloudProviderContainer.class;
      } else if (provider == CloudProvider.SERVERLESS) {
        otherContainerClass = FlexCloudProviderContainer.class;
      } else {
        otherContainerClass = FreeCloudProviderContainer.class;
      }

      final TenantCloudProviderContainer azureTenantCPContainer1 = mock(otherContainerClass);
      doReturn(provider).when(azureTenantCPContainer1).getCloudProvider();

      final List<CloudProviderContainer> mixedListOfCloudProviderContainers =
          List.of(
              awsTenantCPContainer0,
              awsTenantCPContainer1,
              azureTenantCPContainer0,
              azureTenantCPContainer1);
      try {
        if (provider == CloudProvider.FREE) {
          PlanResult.groupFreeContainersByBackingCloudProvider(
              mixedListOfCloudProviderContainers, listOfCluster);
        } else if (provider == CloudProvider.SERVERLESS) {
          PlanResult.groupServerlessContainersByBackingCloudProvider(
              mixedListOfCloudProviderContainers, listOfCluster);
        } else {
          PlanResult.groupFlexContainersByBackingCloudProvider(
              mixedListOfCloudProviderContainers, listOfCluster);
        }
        fail();
      } catch (final Exception pE) {
        assertInstanceOf(IllegalArgumentException.class, pE);
        assertTrue(
            pE.getMessage()
                .contains(
                    String.format(
                        "Received unexpected cloud provider of type %s and container of type %s",
                        provider, otherContainerClass.getSimpleName())));
      }
    }
  }

  final NDSGroup produceGroup(final Stream<CloudProviderContainer> pContainers) {
    return produceGroup(pContainers, new ObjectId());
  }

  final NDSGroup produceGroup(
      final Stream<CloudProviderContainer> pContainers, final ObjectId pObjectId) {
    return new NDSGroup(
        NDSModelTestFactory.getNDSGroupAllFields(
            false,
            pContainers.map(CloudProviderContainer::toDBObject).collect(DbUtils.toBasicDBList()),
            pObjectId));
  }

  private static ReplicaSetHardware getReplicaSetHardware(
      final ObjectId pReplicationSpecId,
      final ClusterDescription pClusterDescription,
      final Stream<InstanceHardware> pHw) {
    return new ReplicaSetHardware(
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0,
            pHw.map(InstanceHardware::toDBObject).collect(DbUtils.toBasicDBList()),
            pReplicationSpecId,
            pClusterDescription));
  }

  private static AzureInstanceHardware getAzureInstanceHardware(
      final int pIndex, final AzureCloudProviderContainer pContainer) {
    return getAzureInstanceHardware(pIndex, pContainer, AzurePublicIPSKUType.STANDARD, true);
  }

  private static AzureInstanceHardware getAzureInstanceHardware(
      final int pIndex,
      final AzureCloudProviderContainer pContainer,
      final AzurePublicIPSKUType pSkuType,
      final boolean isProvisioned) {
    final BasicDBObject doc =
        getAzureInstanceHardwareFull(
            "hostname" + pIndex,
            pContainer.getId(),
            pIndex,
            AzureNDSInstanceSize.M10,
            AzureInstanceFamily.STANDARD_B,
            OS.AL2023);
    if (pSkuType != AzurePublicIPSKUType.STANDARD) {
      doc.put(AzureInstanceHardware.FieldDefs.PUBLIC_IP_SKU_TYPE, pSkuType);
    }
    doc.put(InstanceHardware.FieldDefs.PROVISIONED, isProvisioned);
    final AzureInstanceHardware azureInstanceHardware = new AzureInstanceHardware(doc);
    // Sanity check assumptions about helper doc creation fcn behavior
    assertEquals(azureInstanceHardware.isProvisioned(), isProvisioned);
    assertEquals(azureInstanceHardware.getPublicIPSKUType(), Optional.of(pSkuType));
    assertEquals(Action.NONE, azureInstanceHardware.getAction());
    return azureInstanceHardware;
  }

  private static AzureCloudProviderContainer getAzureEast2CloudProviderContainer(
      Boolean withPrivateLink) {
    final AzurePrivateEndpoint privateEndpoint =
        new AzurePrivateEndpoint(
            "rid",
            "********",
            "hostnameforendpoint",
            "mongodbname",
            "peconnectionname",
            withPrivateLink
                ? AzurePrivateEndpoint.Status.AVAILABLE
                : AzurePrivateEndpoint.Status.INITIATING,
            null,
            !withPrivateLink,
            0);
    return new AzureCloudProviderContainer(
        NDSModelTestFactory.getAzureContainerWithFullyProvisionedPrivateLink(
            DbUtils.toBasicDBList(privateEndpoint.toDBObject()), AzureRegionName.US_EAST_2));
  }

  private static ShardedClusterDescription getAzureShardedClusterDescription(
      final NDSGroup pNdsGroup, final ReplicationSpec pReplicationSpec) {

    final BasicDBObject clusterDescriptionDoc =
        NDSModelTestFactory.getAzureShardedClusterDescription(
                pNdsGroup.getGroupId(),
                "cluster",
                HostnameScheme.PRIVATE,
                List.of(AzureRegionName.US_EAST_2))
            .append(
                ShardedClusterDescription.FieldDefs.HAS_PRIVATE_ENDPOINT_LEGACY_CONNECTION_STRINGS,
                true);
    clusterDescriptionDoc.put(
        ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST,
        DbUtils.toBasicDBList(pReplicationSpec.toDBObject()));
    return new ShardedClusterDescription(clusterDescriptionDoc);
  }

  @Test
  public void
      testGetPrivateLinkActionsConvertDedicateConfigToConfigShardNeedSingleConnectionRule() {
    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
        .thenReturn(false);

    final AutomationConfig automationConfig = mock(AutomationConfig.class);
    doReturn(new Deployment()).when(automationConfig).getDeployment();
    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    final PlannedAction syncAzurePrivateLinkWithCluster = mock(PlannedAction.class);

    final AzureCloudProviderContainer azureContainerWithActivePLEndpoint =
        getAzureEast2CloudProviderContainer(true);

    final NDSGroup azureEast2OnlyGroupWithActivePL =
        produceGroup(Stream.of(azureContainerWithActivePLEndpoint));

    final ReplicationSpec azureEast2SingleRegionReplicationSpec =
        NDSModelTestFactory.getAzureReplicationSpec(
            new ObjectId(), NDSDefaults.ZONE_NAME, 1, 3, List.of(AzureRegionName.US_EAST_2));

    final ShardedClusterDescription shardedClusterDescription =
        getAzureShardedClusterDescription(
            azureEast2OnlyGroupWithActivePL, azureEast2SingleRegionReplicationSpec);

    final BasicDBObject shardedReplicaSetHardwareObject =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, azureContainerWithActivePLEndpoint.getId(), shardedClusterDescription);

    final ReplicaSetHardware shardedReplicaSetHardware =
        new ReplicaSetHardware(shardedReplicaSetHardwareObject);

    final List<AzurePrivateLinkConnectionInboundNATRule> rule =
        shardedReplicaSetHardware
            .getAllHardware()
            .map(
                instance ->
                    new AzurePrivateLinkConnectionInboundNATRule(
                        azureEast2OnlyGroupWithActivePL.getGroupId(),
                        instance.getInstanceId(),
                        1024 + instance.getMemberIndex(),
                        instance.getHostnameForAgents().orElse(null),
                        null,
                        27017,
                        azureContainerWithActivePLEndpoint.getEndpointService().get().getId(),
                        "natRule-" + instance.getMemberIndex(),
                        Usage.VISIBLE_NODE))
            .collect(Collectors.toList());

    doReturn(syncAzurePrivateLinkWithCluster)
        .when(plannedActionFactory)
        .forSyncClusterPrivateEndpointConnection(shardedClusterDescription, CloudProvider.AZURE);

    // dedicated config replicaset hardware without action
    // should not trigger any action
    final BasicDBObject configReplicaSetHardwareObjectWithoutAction =
        ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
            shardedClusterDescription, List.of(azureContainerWithActivePLEndpoint.getId()));

    final ReplicaSetHardware configReplicaSetHardwareWithoutAction =
        new ReplicaSetHardware(configReplicaSetHardwareObjectWithoutAction);

    assertEquals(
        List.of(),
        PlanResult.getPrivateLinkActions(
            null,
            azureEast2OnlyGroupWithActivePL,
            automationConfig,
            rule,
            Cluster.getCluster(
                shardedClusterDescription,
                List.of(shardedReplicaSetHardware, configReplicaSetHardwareWithoutAction)),
            List.of(),
            plannedActionFactory,
            LOGGER,
            appSettings));

    // create dedicated config replicaset hardware with action=RECONFIGURE_CONFIG_SHARD
    final BasicDBObject configReplicaSetHardwareObjectWithReConfigAction =
        ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
                shardedClusterDescription, List.of(azureContainerWithActivePLEndpoint.getId()))
            .append(
                ReplicaSetHardware.FieldDefs.ACTION,
                ReplicaSetHardware.Action.RECONFIGURE_CONFIG_SHARD);

    final ReplicaSetHardware configReplicaSetHardwareWithReConfigAction =
        new ReplicaSetHardware(configReplicaSetHardwareObjectWithReConfigAction);

    // should trigger syncAWSClusterWithPrivateLink action
    final BasicDBObject configReplicaSetHardwareObjectWithSetConfigAction =
        ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
                shardedClusterDescription,
                List.of(azureContainerWithActivePLEndpoint.getId()),
                CloudProvider.AZURE)
            .append(
                ReplicaSetHardware.FieldDefs.ACTION,
                ReplicaSetHardware.Action.SET_CONFIG_SHARD_ON_AGENT);

    final ReplicaSetHardware configReplicaSetHardwareWithSetConfigAction =
        new ReplicaSetHardware(configReplicaSetHardwareObjectWithSetConfigAction);

    assertEquals(
        List.of(syncAzurePrivateLinkWithCluster),
        PlanResult.getPrivateLinkActions(
            null,
            azureEast2OnlyGroupWithActivePL,
            automationConfig,
            rule,
            Cluster.getCluster(
                shardedClusterDescription,
                List.of(shardedReplicaSetHardware, configReplicaSetHardwareWithSetConfigAction)),
            List.of(),
            plannedActionFactory,
            LOGGER,
            appSettings));
  }

  @Test
  public void testGetPrivateLinkActionsConvertDedicateConfigToConfigShardNeedMultiConnectionRule() {
    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
        .thenReturn(false);

    final AutomationConfig automationConfig = mock(AutomationConfig.class);
    doReturn(new Deployment()).when(automationConfig).getDeployment();
    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    final PlannedAction syncAWSClusterWithPrivateLink = mock(PlannedAction.class);

    final ObjectId groupId = new ObjectId();
    final BasicDBList privateEndpoints = new BasicDBList();
    final String endpointId = "us-east-endpoint";
    privateEndpoints.add(
        new AWSPrivateLinkInterfaceEndpoint(
                endpointId,
                null,
                "us.east.com",
                AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                null,
                false,
                0)
            .toDBObject());
    final BasicDBObject awsContainerWithPrivateEndpointsDBObject =
        NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
            privateEndpoints, AWSRegionName.US_EAST_1, new ObjectId());
    final AWSCloudProviderContainer awsContainer =
        new AWSCloudProviderContainer(awsContainerWithPrivateEndpointsDBObject);
    final ObjectId privateEndpointConnectionId = awsContainer.getEndpointService().get().getId();

    final NDSGroup groupWithPrivateEndpoint =
        produceGroup(
            Stream.of(new AWSCloudProviderContainer(awsContainerWithPrivateEndpointsDBObject)),
            groupId);

    final ShardedClusterDescription cluster =
        NDSModelTestFactory.getDefaultShardedClusterDescription(
                groupId,
                "cluster0",
                NDSModelTestFactory.getRegionConfigs(3, List.of(AWSRegionName.US_EAST_1)))
            .copy()
            .setMongoDBVersion("6.0.0")
            .build();

    // create shard replicaset hardware
    final BasicDBObject shardedReplicaSetHardwareObject =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, awsContainer.getId(), cluster);

    final ReplicaSetHardware shardedReplicaSetHardware =
        new ReplicaSetHardware(shardedReplicaSetHardwareObject);

    final List<ObjectId> expectedShardedInstanceHardwareIds =
        shardedReplicaSetHardware
            .getAllHardware()
            .map(InstanceHardware::getInstanceId)
            .collect(Collectors.toList());

    final Function<Pair<ObjectId, String>, AWSMultiTargetConnectionRule>
        getMultiTargetRuleWithPrivateEndpointConnectionId =
            (connectionIdToClusterName) ->
                new AWSMultiTargetConnectionRule(
                    groupId,
                    connectionIdToClusterName.getRight(),
                    expectedShardedInstanceHardwareIds,
                    NdsProcessPortTyped.LOAD_BALANCED_MONGOS,
                    1024,
                    null,
                    awsContainer.getId(),
                    "groupArn",
                    "listenerArn",
                    connectionIdToClusterName.getLeft());

    final Function<Pair<ObjectId, List<InstanceHardware>>, List<AWSPrivateLinkTargetGroup>>
        getSingleTargetRulesWithPrivateEndpointConnectionId =
            (connectionIdAndHardwarePair) -> {
              final ObjectId connectionId = connectionIdAndHardwarePair.getLeft();
              final List<InstanceHardware> hardwares = connectionIdAndHardwarePair.getRight();
              final List<AWSPrivateLinkTargetGroup> singleTargetRules = new ArrayList<>();

              for (int i = 0; i < hardwares.size(); i++) {
                final InstanceHardware ih = hardwares.get(i);
                singleTargetRules.add(
                    new AWSPrivateLinkTargetGroup(
                        groupId,
                        ih.getInstanceId(),
                        "targetGroupArn" + i,
                        i,
                        "listenerArn" + i,
                        ih.getHostnameForAgents().orElse(null),
                        false,
                        null,
                        false,
                        27017,
                        connectionId,
                        false,
                        false));
              }
              return singleTargetRules;
            };

    final AWSMultiTargetConnectionRule multiTargetRule =
        getMultiTargetRuleWithPrivateEndpointConnectionId.apply(
            Pair.of(privateEndpointConnectionId, cluster.getName()));
    final List<AWSPrivateLinkTargetGroup> singleTargetRules =
        getSingleTargetRulesWithPrivateEndpointConnectionId.apply(
            Pair.of(
                privateEndpointConnectionId, shardedReplicaSetHardware.getAllHardware().toList()));

    doReturn(syncAWSClusterWithPrivateLink)
        .when(plannedActionFactory)
        .forSyncClusterPrivateEndpointConnection(cluster, CloudProvider.AWS);

    // dedicated config replicaset hardware without action
    // should not trigger any action
    final BasicDBObject configReplicaSetHardwareObjectWithoutAction =
        ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
            cluster, List.of(awsContainer.getId()));

    final ReplicaSetHardware configReplicaSetHardwareWithoutAction =
        new ReplicaSetHardware(configReplicaSetHardwareObjectWithoutAction);

    assertEquals(
        List.of(),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpoint,
            automationConfig,
            singleTargetRules,
            Cluster.getCluster(
                cluster, List.of(shardedReplicaSetHardware, configReplicaSetHardwareWithoutAction)),
            List.of(multiTargetRule),
            plannedActionFactory,
            LOGGER,
            appSettings));

    // create dedicated config replicaset hardware with action=RECONFIGURE_CONFIG_SHARD
    final BasicDBObject configReplicaSetHardwareObjectWithReConfigAction =
        ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
                cluster, List.of(awsContainer.getId()))
            .append(
                ReplicaSetHardware.FieldDefs.ACTION,
                ReplicaSetHardware.Action.RECONFIGURE_CONFIG_SHARD);

    final ReplicaSetHardware configReplicaSetHardwareWithReConfigAction =
        new ReplicaSetHardware(configReplicaSetHardwareObjectWithReConfigAction);

    // dedicated config replicaset hardware with action=RECONFIGURE_CONFIG_SHARD
    // should not trigger any action
    assertEquals(
        List.of(),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpoint,
            automationConfig,
            singleTargetRules,
            Cluster.getCluster(
                cluster,
                List.of(shardedReplicaSetHardware, configReplicaSetHardwareWithReConfigAction)),
            List.of(multiTargetRule),
            plannedActionFactory,
            LOGGER,
            appSettings));

    // dedicated config replicaset hardware with action=SET_CONFIG_SHARD_ON_AGENT
    // should trigger syncAWSClusterWithPrivateLink action
    final BasicDBObject configReplicaSetHardwareObjectWithSetConfigAction =
        ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
                cluster, List.of(awsContainer.getId()))
            .append(
                ReplicaSetHardware.FieldDefs.ACTION,
                ReplicaSetHardware.Action.SET_CONFIG_SHARD_ON_AGENT);

    final ReplicaSetHardware configReplicaSetHardwareWithSetConfigAction =
        new ReplicaSetHardware(configReplicaSetHardwareObjectWithSetConfigAction);

    assertEquals(
        List.of(syncAWSClusterWithPrivateLink),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpoint,
            automationConfig,
            singleTargetRules,
            Cluster.getCluster(
                cluster,
                List.of(shardedReplicaSetHardware, configReplicaSetHardwareWithSetConfigAction)),
            List.of(multiTargetRule),
            plannedActionFactory,
            LOGGER,
            appSettings));
  }

  @Test
  public void
      testGetPrivateLinkActionsConvertConfigShardToDedicateConfigRemoveSingleConnectionRule() {
    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
        .thenReturn(false);

    final AutomationConfig automationConfig = mock(AutomationConfig.class);
    doReturn(new Deployment()).when(automationConfig).getDeployment();
    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    final PlannedAction syncAzurePrivateLinkWithCluster = mock(PlannedAction.class);

    final AzureCloudProviderContainer azureContainerWithActivePLEndpoint =
        getAzureEast2CloudProviderContainer(true);

    final NDSGroup azureEast2OnlyGroupWithActivePL =
        produceGroup(Stream.of(azureContainerWithActivePLEndpoint));

    final ReplicationSpec azureEast2SingleRegionReplicationSpec =
        NDSModelTestFactory.getAzureReplicationSpec(
            new ObjectId(), NDSDefaults.ZONE_NAME, 1, 3, List.of(AzureRegionName.US_EAST_2));

    final ShardedClusterDescription shardedClusterDescription =
        getAzureShardedClusterDescription(
            azureEast2OnlyGroupWithActivePL, azureEast2SingleRegionReplicationSpec);

    final BasicDBObject shardedReplicaSetHardwareObject =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, azureContainerWithActivePLEndpoint.getId(), shardedClusterDescription);
    final ReplicaSetHardware shardedReplicaSetHardware =
        new ReplicaSetHardware(shardedReplicaSetHardwareObject);

    final BasicDBObject configReplicaSetHardwareObjectWithoutAction =
        ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
            shardedClusterDescription,
            List.of(azureContainerWithActivePLEndpoint.getId()),
            CloudProvider.AZURE);
    final ReplicaSetHardware configReplicaSetHardware =
        new ReplicaSetHardware(configReplicaSetHardwareObjectWithoutAction);

    // the isConfigShard should have been set to false
    assertFalse(configReplicaSetHardware.isConfigShard());

    // Combine config and shard instance hardware
    // and create connection rule for all instances (including config)
    final List<InstanceHardware> allInstanceHardware =
        Stream.concat(
                shardedReplicaSetHardware.getAllHardware(),
                configReplicaSetHardware.getAllHardware())
            .collect(Collectors.toList());

    final List<AzurePrivateLinkConnectionInboundNATRule> rule =
        allInstanceHardware.stream()
            .map(
                instance ->
                    new AzurePrivateLinkConnectionInboundNATRule(
                        azureEast2OnlyGroupWithActivePL.getGroupId(),
                        instance.getInstanceId(),
                        1024 + instance.getMemberIndex(),
                        instance.getHostnameForAgents().orElse(null),
                        null,
                        27017,
                        azureContainerWithActivePLEndpoint.getEndpointService().get().getId(),
                        "natRule-" + instance.getMemberIndex(),
                        Usage.VISIBLE_NODE))
            .collect(Collectors.toList());

    doReturn(syncAzurePrivateLinkWithCluster)
        .when(plannedActionFactory)
        .forSyncClusterPrivateEndpointConnection(shardedClusterDescription, CloudProvider.AZURE);

    assertEquals(
        List.of(syncAzurePrivateLinkWithCluster),
        PlanResult.getPrivateLinkActions(
            null,
            azureEast2OnlyGroupWithActivePL,
            automationConfig,
            rule,
            Cluster.getCluster(
                shardedClusterDescription,
                List.of(shardedReplicaSetHardware, configReplicaSetHardware)),
            List.of(),
            plannedActionFactory,
            LOGGER,
            appSettings));
  }

  @Test
  public void
      testGetPrivateLinkActionsConvertConfigShardToDedicateConfigRemoveMultiConnectionRule() {
    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
        .thenReturn(false);

    final AutomationConfig automationConfig = mock(AutomationConfig.class);
    doReturn(new Deployment()).when(automationConfig).getDeployment();
    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    final PlannedAction syncAWSClusterWithPrivateLink = mock(PlannedAction.class);

    final ObjectId groupId = new ObjectId();
    final BasicDBList privateEndpoints = new BasicDBList();
    final String endpointId = "us-east-endpoint";
    privateEndpoints.add(
        new AWSPrivateLinkInterfaceEndpoint(
                endpointId,
                null,
                "us.east.com",
                AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                null,
                false,
                0)
            .toDBObject());
    final BasicDBObject awsContainerWithPrivateEndpointsDBObject =
        NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
            privateEndpoints, AWSRegionName.US_EAST_1, new ObjectId());
    final AWSCloudProviderContainer awsContainer =
        new AWSCloudProviderContainer(awsContainerWithPrivateEndpointsDBObject);
    final ObjectId privateEndpointConnectionId = awsContainer.getEndpointService().get().getId();

    final NDSGroup groupWithPrivateEndpoint =
        produceGroup(
            Stream.of(new AWSCloudProviderContainer(awsContainerWithPrivateEndpointsDBObject)),
            groupId);

    final ShardedClusterDescription cluster =
        NDSModelTestFactory.getDefaultShardedClusterDescription(
                groupId,
                "cluster0",
                NDSModelTestFactory.getRegionConfigs(3, List.of(AWSRegionName.US_EAST_1)))
            .copy()
            .setMongoDBVersion("6.0.0")
            .build();

    // create shard replicaset hardware
    final BasicDBObject shardedReplicaSetHardwareObject =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, awsContainer.getId(), cluster);

    final ReplicaSetHardware shardedReplicaSetHardware =
        new ReplicaSetHardware(shardedReplicaSetHardwareObject);

    // create config replicaset hardware
    final BasicDBObject configReplicaSetHardwareObject =
        ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
            cluster, List.of(awsContainer.getId()));

    final ReplicaSetHardware configReplicaSetHardware =
        new ReplicaSetHardware(configReplicaSetHardwareObject);

    // Combine config and shard instance hardware
    // and create connection rule for all instances (including config)
    final List<InstanceHardware> allInstanceHardware =
        Stream.concat(
                shardedReplicaSetHardware.getAllHardware(),
                configReplicaSetHardware.getAllHardware())
            .collect(Collectors.toList());

    final List<ObjectId> expectedAllInstanceHardwareIds =
        allInstanceHardware.stream()
            .map(InstanceHardware::getInstanceId)
            .collect(Collectors.toList());

    final Function<Pair<ObjectId, String>, AWSMultiTargetConnectionRule>
        getMultiTargetRuleWithPrivateEndpointConnectionId =
            (connectionIdToClusterName) ->
                new AWSMultiTargetConnectionRule(
                    groupId,
                    connectionIdToClusterName.getRight(),
                    expectedAllInstanceHardwareIds,
                    NdsProcessPortTyped.LOAD_BALANCED_MONGOS,
                    1024,
                    null,
                    awsContainer.getId(),
                    "groupArn",
                    "listenerArn",
                    connectionIdToClusterName.getLeft());

    final Function<Pair<ObjectId, List<InstanceHardware>>, List<AWSPrivateLinkTargetGroup>>
        getSingleTargetRulesWithPrivateEndpointConnectionId =
            (connectionIdAndHardwarePair) -> {
              final ObjectId connectionId = connectionIdAndHardwarePair.getLeft();
              final List<InstanceHardware> hardwares = connectionIdAndHardwarePair.getRight();
              final List<AWSPrivateLinkTargetGroup> singleTargetRules = new ArrayList<>();

              for (int i = 0; i < hardwares.size(); i++) {
                final InstanceHardware ih = hardwares.get(i);
                singleTargetRules.add(
                    new AWSPrivateLinkTargetGroup(
                        groupId,
                        ih.getInstanceId(),
                        "targetGroupArn" + i,
                        i,
                        "listenerArn" + i,
                        ih.getHostnameForAgents().orElse(null),
                        false,
                        null,
                        false,
                        27017,
                        connectionId,
                        false,
                        false));
              }
              return singleTargetRules;
            };

    final AWSMultiTargetConnectionRule multiTargetRule =
        getMultiTargetRuleWithPrivateEndpointConnectionId.apply(
            Pair.of(privateEndpointConnectionId, cluster.getName()));
    final List<AWSPrivateLinkTargetGroup> singleTargetRules =
        getSingleTargetRulesWithPrivateEndpointConnectionId.apply(
            Pair.of(privateEndpointConnectionId, allInstanceHardware));

    doReturn(syncAWSClusterWithPrivateLink)
        .when(plannedActionFactory)
        .forSyncClusterPrivateEndpointConnection(cluster, CloudProvider.AWS);

    assertEquals(
        List.of(syncAWSClusterWithPrivateLink),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpoint,
            automationConfig,
            singleTargetRules,
            Cluster.getCluster(
                cluster, List.of(shardedReplicaSetHardware, configReplicaSetHardware)),
            List.of(multiTargetRule),
            plannedActionFactory,
            LOGGER,
            appSettings));
  }

  @Test
  public void testGetPrivateLinkActionsNeedsMultiTargetRule() {
    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
        .thenReturn(false);

    final AutomationConfig automationConfig = mock(AutomationConfig.class);
    doReturn(new Deployment()).when(automationConfig).getDeployment();
    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    final PlannedAction syncAWSClusterWithPrivateLink = mock(PlannedAction.class);

    // ================================================
    // Setup: container has no private endpoints
    // ================================================
    final ObjectId groupId = new ObjectId();
    final AWSCloudProviderContainer awsEastTwoContainer =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer(AWSRegionName.US_EAST_2));
    final AWSCloudProviderContainer awsWestTwoContainer =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer(AWSRegionName.US_WEST_2));
    final AWSInstanceFamily instanceFamily =
        (AWSInstanceFamily)
            AWSNDSDefaults.INSTANCE_SIZE.getInstanceFamilies().keySet().stream().findFirst().get();
    final Function<Integer, AWSHardwareSpec> awsHardwareSpec =
        (numNodes) ->
            new AWSHardwareSpec(
                numNodes,
                AWSNDSDefaults.INSTANCE_SIZE,
                instanceFamily,
                NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                    CloudProvider.AWS, instanceFamily),
                CpuArchitecture.X86_64,
                120,
                0,
                VolumeType.Gp2,
                true);
    final List<RegionConfig> regionConfigs =
        List.of(
            new ShardRegionConfig(
                awsEastTwoContainer.getRegion(),
                awsEastTwoContainer.getCloudProvider(),
                AWSAutoScaling.getDefaultAutoScaling(),
                AWSAutoScaling.getDefaultAutoScaling(),
                RegionConfig.MAX_PRIORITY,
                awsHardwareSpec.apply(3),
                awsHardwareSpec.apply(0),
                awsHardwareSpec.apply(0),
                awsHardwareSpec.apply(0),
                null,
                null,
                null,
                null));
    final ClusterDescription awsClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(
                groupId, "foo", Collections.emptyList(), 1, regionConfigs));
    final BasicDBObject replicaSetHardwareObject =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, awsEastTwoContainer.getId(), awsClusterDescription);
    final ReplicaSetHardware replicaSetHardware = new ReplicaSetHardware(replicaSetHardwareObject);
    final NDSGroup group =
        new NDSGroup(
            NDSModelTestFactory.getNDSGroupAllFields()
                .append(NDSGroup.FieldDefs.ID, groupId)
                .append(
                    NDSGroup.FieldDefs.CLOUD_PROVIDER_CONTAINERS,
                    Stream.of(awsEastTwoContainer.toDBObject(), awsWestTwoContainer.toDBObject())
                        .collect(DbUtils.toBasicDBList())));

    {
      assertEquals(
          List.of(),
          PlanResult.getPrivateLinkActions(
              null,
              group,
              automationConfig,
              List.of(),
              Cluster.getCluster(awsClusterDescription, List.of(replicaSetHardware)),
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings));
    }

    // ================================================
    // Setup: cluster supports optimized strings
    // no pertinent rules yet
    // ================================================
    final BasicDBList privateEndpoints = new BasicDBList();
    privateEndpoints.add(
        new AWSPrivateLinkInterfaceEndpoint(
                "us-east-endpoint",
                null,
                "us.east.com",
                AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                null,
                false,
                0)
            .toDBObject());

    // Container with privatelink
    final BasicDBObject awsEastTwoContainerWithPrivateEndpointsDBObject =
        NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
            privateEndpoints, AWSRegionName.US_EAST_2, awsEastTwoContainer.getId());

    final NDSGroup groupWithPrivateEndpoint =
        produceGroup(
            Stream.of(
                new AWSCloudProviderContainer(awsEastTwoContainerWithPrivateEndpointsDBObject),
                awsWestTwoContainer),
            groupId);
    final ShardedClusterDescription baseShardedCluster =
        NDSModelTestFactory.getDefaultShardedClusterDescription(
            groupId,
            "readyToBeOptimized",
            NDSModelTestFactory.getRegionConfigs(3, List.of(AWSRegionName.US_EAST_2)));
    final ShardedClusterDescription shardedClusterInEastTwoWithSpecifiedVersion =
        new ShardedClusterDescription(
            baseShardedCluster
                .toDBObject()
                .append(
                    ClusterDescription.FieldDefs.MONGODB_VERSION,
                    VersionUtils.MIN_LOAD_BALANCED_MODE_VERSION_FOR_MONGOS)
                .append(
                    ShardedClusterDescription.FieldDefs
                        .HAS_PRIVATE_ENDPOINT_LEGACY_CONNECTION_STRINGS,
                    false));
    final BasicDBObject shardedReplicaSetHardwareObject =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, awsEastTwoContainer.getId(), shardedClusterInEastTwoWithSpecifiedVersion);
    final ReplicaSetHardware shardedReplicaSetHardware =
        new ReplicaSetHardware(shardedReplicaSetHardwareObject);
    final Set<ObjectId> expectedInstanceHardwareIds =
        shardedReplicaSetHardware
            .getAllHardware()
            .map(InstanceHardware::getInstanceId)
            .collect(Collectors.toSet());

    doReturn(syncAWSClusterWithPrivateLink)
        .when(plannedActionFactory)
        .forSyncClusterPrivateEndpointConnection(
            shardedClusterInEastTwoWithSpecifiedVersion, CloudProvider.AWS);
    {
      assertEquals(
          List.of(syncAWSClusterWithPrivateLink),
          PlanResult.getPrivateLinkActions(
              null,
              groupWithPrivateEndpoint,
              automationConfig,
              List.of(),
              Cluster.getCluster(
                  shardedClusterInEastTwoWithSpecifiedVersion, List.of(shardedReplicaSetHardware)),
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings));
    }

    // ================================================
    // Setup: cluster supports optimized strings
    // has pertinent rule that needs update
    // ================================================
    final AWSMultiTargetConnectionRule pertinentRuleNeedsUpdate =
        new AWSMultiTargetConnectionRule(
            groupId,
            shardedClusterInEastTwoWithSpecifiedVersion.getName(),
            List.of(),
            NdsProcessPortTyped.LOAD_BALANCED_MONGOS,
            1024,
            null,
            awsEastTwoContainer.getId(),
            "groupArn",
            "listenerArn",
            null);
    {
      assertEquals(
          List.of(syncAWSClusterWithPrivateLink),
          PlanResult.getPrivateLinkActions(
              null,
              groupWithPrivateEndpoint,
              automationConfig,
              List.of(),
              Cluster.getCluster(
                  shardedClusterInEastTwoWithSpecifiedVersion, List.of(shardedReplicaSetHardware)),
              List.of(pertinentRuleNeedsUpdate),
              plannedActionFactory,
              LOGGER,
              appSettings));
    }

    // ================================================
    // Setup: cluster supports optimized strings
    // has pertinent rule that does not need update
    // ================================================
    final AWSMultiTargetConnectionRule pertinentRuleUpToDate =
        new AWSMultiTargetConnectionRule(
            groupId,
            shardedClusterInEastTwoWithSpecifiedVersion.getName(),
            new ArrayList<>(expectedInstanceHardwareIds),
            NdsProcessPortTyped.LOAD_BALANCED_MONGOS,
            1024,
            null,
            awsEastTwoContainer.getId(),
            "groupArn",
            "listenerArn",
            null);
    {
      assertEquals(
          List.of(),
          PlanResult.getPrivateLinkActions(
              null,
              groupWithPrivateEndpoint,
              automationConfig,
              List.of(),
              Cluster.getCluster(
                  shardedClusterInEastTwoWithSpecifiedVersion, List.of(shardedReplicaSetHardware)),
              List.of(pertinentRuleUpToDate),
              plannedActionFactory,
              LOGGER,
              appSettings));
    }
    // ================================================
    // Setup: cluster had all nodes removed from container
    // has pertinent rule that needs update - no instances on rule, but need listener torn down
    // ================================================
    {
      final AWSMultiTargetConnectionRule pertinentRuleNeedsResourceTearDown =
          new AWSMultiTargetConnectionRule(
              groupId,
              shardedClusterInEastTwoWithSpecifiedVersion.getName(),
              List.of(),
              NdsProcessPortTyped.LOAD_BALANCED_MONGOS,
              1025,
              null,
              awsWestTwoContainer.getId(),
              "groupToDestroy",
              "listenerToDestroy",
              null);
      assertEquals(
          List.of(syncAWSClusterWithPrivateLink),
          PlanResult.getPrivateLinkActions(
              null,
              groupWithPrivateEndpoint,
              automationConfig,
              List.of(),
              Cluster.getCluster(
                  shardedClusterInEastTwoWithSpecifiedVersion, List.of(shardedReplicaSetHardware)),
              List.of(pertinentRuleUpToDate, pertinentRuleNeedsResourceTearDown),
              plannedActionFactory,
              LOGGER,
              appSettings));
    }

    // ================================================
    // Setup: cluster had private endpoint removed
    // has pertinent rule that needs update
    // ================================================

    awsEastTwoContainerWithPrivateEndpointsDBObject.remove(
        AWSCloudProviderContainer.FieldDefs.PRIVATE_LINK_CONNECTION);
    // Recreate group, but without private endpoint
    final NDSGroup groupWithPrivateEndpointRemoved =
        produceGroup(
            Stream.of(
                new AWSCloudProviderContainer(awsEastTwoContainerWithPrivateEndpointsDBObject)),
            groupId);
    // No PE = need to sync to remove extant PLs
    assertEquals(
        List.of(syncAWSClusterWithPrivateLink),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpointRemoved,
            automationConfig,
            List.of(),
            Cluster.getCluster(
                shardedClusterInEastTwoWithSpecifiedVersion, List.of(shardedReplicaSetHardware)),
            List.of(pertinentRuleUpToDate),
            plannedActionFactory,
            LOGGER,
            appSettings));

    // ================================================
    // Setup: system project cluster
    // ================================================
    {
      final NDSGroup systemProject = mock(NDSGroup.class);
      doReturn(true).when(systemProject).isSystemProject();

      final Cluster cluster = mock(Cluster.class);

      assertEquals(
          List.of(),
          PlanResult.getPrivateLinkActions(
              null,
              systemProject,
              automationConfig,
              List.of(),
              cluster,
              List.of(pertinentRuleNeedsUpdate),
              plannedActionFactory,
              LOGGER,
              appSettings));

      verifyNoInteractions(cluster);
    }
  }

  @Test
  public void tesGetPrivateLinkActionsNeedsRemoveSingleTargetRule() {
    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
        .thenReturn(false);

    final AutomationConfig automationConfig = mock(AutomationConfig.class);
    doReturn(new Deployment()).when(automationConfig).getDeployment();
    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    final PlannedAction syncAWSClusterWithPrivateLink = mock(PlannedAction.class);

    // case: sharded cluster supports optimized connection string and has pertinent rule, has legacy
    // strings with up-to-date single-target rules
    // no work to be done
    final ObjectId groupId = new ObjectId();
    final BasicDBList eastTwoPrivateEndpoints = new BasicDBList();
    final String endpointId = "us-east-endpoint";
    // Container with privatelink
    eastTwoPrivateEndpoints.add(
        new AWSPrivateLinkInterfaceEndpoint(
                endpointId,
                null,
                "us.east.com",
                AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                null,
                false,
                0)
            .toDBObject());
    final BasicDBObject awsEastTwoContainerWithPrivateEndpointsDBObject =
        NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
            eastTwoPrivateEndpoints, AWSRegionName.US_EAST_2, new ObjectId());
    final AWSCloudProviderContainer awsEastTwoContainer =
        new AWSCloudProviderContainer(awsEastTwoContainerWithPrivateEndpointsDBObject);

    final NDSGroup groupWithPrivateEndpoint =
        produceGroup(
            Stream.of(
                new AWSCloudProviderContainer(awsEastTwoContainerWithPrivateEndpointsDBObject)),
            groupId);
    final ShardedClusterDescription baseShardedCluster =
        NDSModelTestFactory.getDefaultShardedClusterDescription(
            groupId,
            "eligibleForOptimizedStrings",
            NDSModelTestFactory.getRegionConfigs(3, List.of(AWSRegionName.US_EAST_2)));
    final BasicDBObject legacySRVAddressMap = new BasicDBObject();
    legacySRVAddressMap.append(endpointId, "srvAddress");
    final ShardedClusterDescription shardedClusterWithOptimizedAndLegacyStrings =
        new ShardedClusterDescription(
            baseShardedCluster
                .toDBObject()
                .append(
                    ClusterDescription.FieldDefs.MONGODB_VERSION,
                    VersionUtils.MIN_LOAD_BALANCED_MODE_VERSION_FOR_MONGOS)
                .append(
                    ShardedClusterDescription.FieldDefs
                        .HAS_PRIVATE_ENDPOINT_LEGACY_CONNECTION_STRINGS,
                    true)
                .append(ClusterDescription.FieldDefs.PRIVATE_SRV_ADDRESS_MAP, legacySRVAddressMap));
    final BasicDBObject shardedReplicaSetHardwareObject =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, awsEastTwoContainer.getId(), shardedClusterWithOptimizedAndLegacyStrings);
    final ReplicaSetHardware shardedReplicaSetHardware =
        new ReplicaSetHardware(shardedReplicaSetHardwareObject);
    final List<ObjectId> expectedInstanceHardwareIds =
        shardedReplicaSetHardware
            .getAllHardware()
            .map(InstanceHardware::getInstanceId)
            .collect(Collectors.toList());

    final AWSMultiTargetConnectionRule pertinentRuleUpToDate =
        new AWSMultiTargetConnectionRule(
            groupId,
            shardedClusterWithOptimizedAndLegacyStrings.getName(),
            expectedInstanceHardwareIds,
            NdsProcessPortTyped.LOAD_BALANCED_MONGOS,
            1024,
            null,
            awsEastTwoContainer.getId(),
            "groupArn",
            "listenerArn",
            null);

    final List<AWSPrivateLinkTargetGroup> singleTargetPrivateLinkTargetGroups = new ArrayList<>();

    final List<InstanceHardware> allHardware = shardedReplicaSetHardware.getAllHardware().toList();
    for (int i = 0; i < allHardware.size(); i++) {
      final InstanceHardware ih = allHardware.get(i);
      singleTargetPrivateLinkTargetGroups.add(
          new AWSPrivateLinkTargetGroup(
              groupId,
              ih.getInstanceId(),
              "targetGroupArn" + i,
              i,
              "listenerArn" + i,
              ih.getHostnameForAgents().orElse(null),
              false,
              null,
              false,
              27017,
              null,
              false,
              false));
    }

    {
      assertEquals(
          List.of(),
          PlanResult.getPrivateLinkActions(
              null,
              groupWithPrivateEndpoint,
              automationConfig,
              singleTargetPrivateLinkTargetGroups,
              Cluster.getCluster(
                  shardedClusterWithOptimizedAndLegacyStrings, List.of(shardedReplicaSetHardware)),
              List.of(pertinentRuleUpToDate),
              plannedActionFactory,
              LOGGER,
              appSettings));
    }

    final ShardedClusterDescription shardedClusterWithLegacyStringsRemoved =
        new ShardedClusterDescription(
            shardedClusterWithOptimizedAndLegacyStrings
                .toDBObject()
                .append(
                    ShardedClusterDescription.FieldDefs
                        .HAS_PRIVATE_ENDPOINT_LEGACY_CONNECTION_STRINGS,
                    false));

    doReturn(syncAWSClusterWithPrivateLink)
        .when(plannedActionFactory)
        .forSyncClusterPrivateEndpointConnection(
            shardedClusterWithLegacyStringsRemoved, CloudProvider.AWS);
    {
      assertEquals(
          List.of(syncAWSClusterWithPrivateLink),
          PlanResult.getPrivateLinkActions(
              null,
              groupWithPrivateEndpoint,
              automationConfig,
              singleTargetPrivateLinkTargetGroups,
              Cluster.getCluster(
                  shardedClusterWithLegacyStringsRemoved, List.of(shardedReplicaSetHardware)),
              List.of(pertinentRuleUpToDate),
              plannedActionFactory,
              LOGGER,
              appSettings));
    }
  }

  @Test
  public void tesGetPrivateLinkActionsRulesHaveStalePrivateEndpointConnectionId() {
    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
        .thenReturn(false);

    final AutomationConfig automationConfig = mock(AutomationConfig.class);
    doReturn(new Deployment()).when(automationConfig).getDeployment();
    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    final PlannedAction syncAWSClusterWithPrivateLink = mock(PlannedAction.class);

    final ObjectId groupId = new ObjectId();
    final BasicDBList eastTwoPrivateEndpoints = new BasicDBList();
    final String endpointId = "us-east-endpoint";
    eastTwoPrivateEndpoints.add(
        new AWSPrivateLinkInterfaceEndpoint(
                endpointId,
                null,
                "us.east.com",
                AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                null,
                false,
                0)
            .toDBObject());
    final BasicDBObject awsEastTwoContainerWithPrivateEndpointsDBObject =
        NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
            eastTwoPrivateEndpoints, AWSRegionName.US_EAST_2, new ObjectId());
    final AWSCloudProviderContainer awsEastTwoContainer =
        new AWSCloudProviderContainer(awsEastTwoContainerWithPrivateEndpointsDBObject);
    final ObjectId privateEndpointConnectionId =
        awsEastTwoContainer.getEndpointService().get().getId();

    final NDSGroup groupWithPrivateEndpoint =
        produceGroup(
            Stream.of(
                new AWSCloudProviderContainer(awsEastTwoContainerWithPrivateEndpointsDBObject)),
            groupId);
    final ShardedClusterDescription cluster =
        NDSModelTestFactory.getDefaultShardedClusterDescription(
                groupId,
                "cluster0",
                NDSModelTestFactory.getRegionConfigs(3, List.of(AWSRegionName.US_EAST_2)))
            .copy()
            .setMongoDBVersion("6.0.0")
            .build();

    final BasicDBObject shardedReplicaSetHardwareObject =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, awsEastTwoContainer.getId(), cluster);
    final ReplicaSetHardware shardedReplicaSetHardware =
        new ReplicaSetHardware(shardedReplicaSetHardwareObject);
    final List<ObjectId> expectedInstanceHardwareIds =
        shardedReplicaSetHardware
            .getAllHardware()
            .map(InstanceHardware::getInstanceId)
            .collect(Collectors.toList());

    final Function<Pair<ObjectId, String>, AWSMultiTargetConnectionRule>
        getMultiTargetRuleWithPrivateEndpointConnectionId =
            (connectionIdToClusterName) ->
                new AWSMultiTargetConnectionRule(
                    groupId,
                    connectionIdToClusterName.getRight(),
                    expectedInstanceHardwareIds,
                    NdsProcessPortTyped.LOAD_BALANCED_MONGOS,
                    1024,
                    null,
                    awsEastTwoContainer.getId(),
                    "groupArn",
                    "listenerArn",
                    connectionIdToClusterName.getLeft());

    final Function<Pair<ObjectId, List<InstanceHardware>>, List<AWSPrivateLinkTargetGroup>>
        getSingleTargetRulesWithPrivateEndpointConnectionId =
            (connectionIdAndHardwarePair) -> {
              final ObjectId connectionId = connectionIdAndHardwarePair.getLeft();
              final List<InstanceHardware> hardwares = connectionIdAndHardwarePair.getRight();
              final List<AWSPrivateLinkTargetGroup> singleTargetRules = new ArrayList<>();

              for (int i = 0; i < hardwares.size(); i++) {
                final InstanceHardware ih = hardwares.get(i);
                singleTargetRules.add(
                    new AWSPrivateLinkTargetGroup(
                        groupId,
                        ih.getInstanceId(),
                        "targetGroupArn" + i,
                        i,
                        "listenerArn" + i,
                        ih.getHostnameForAgents().orElse(null),
                        false,
                        null,
                        false,
                        27017,
                        connectionId,
                        false,
                        false));
              }
              return singleTargetRules;
            };

    final AWSMultiTargetConnectionRule multiTargetRule =
        getMultiTargetRuleWithPrivateEndpointConnectionId.apply(
            Pair.of(privateEndpointConnectionId, cluster.getName()));
    final List<AWSPrivateLinkTargetGroup> singleTargetRules =
        getSingleTargetRulesWithPrivateEndpointConnectionId.apply(
            Pair.of(
                privateEndpointConnectionId, shardedReplicaSetHardware.getAllHardware().toList()));

    doReturn(syncAWSClusterWithPrivateLink)
        .when(plannedActionFactory)
        .forSyncClusterPrivateEndpointConnection(cluster, CloudProvider.AWS);

    assertEquals(
        List.of(),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpoint,
            automationConfig,
            singleTargetRules,
            Cluster.getCluster(cluster, List.of(shardedReplicaSetHardware)),
            List.of(multiTargetRule),
            plannedActionFactory,
            LOGGER,
            appSettings));

    final AWSMultiTargetConnectionRule multiTargetRule_null =
        getMultiTargetRuleWithPrivateEndpointConnectionId.apply(Pair.of(null, cluster.getName()));
    final List<AWSPrivateLinkTargetGroup> singleTargetRules_null =
        getSingleTargetRulesWithPrivateEndpointConnectionId.apply(
            Pair.of(null, shardedReplicaSetHardware.getAllHardware().toList()));

    assertEquals(
        List.of(),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpoint,
            automationConfig,
            singleTargetRules_null,
            Cluster.getCluster(cluster, List.of(shardedReplicaSetHardware)),
            List.of(multiTargetRule_null),
            plannedActionFactory,
            LOGGER,
            appSettings));

    final ShardedClusterDescription differentCluster =
        NDSModelTestFactory.getDefaultShardedClusterDescription(
                groupId,
                "differentCluster",
                NDSModelTestFactory.getRegionConfigs(3, List.of(AWSRegionName.US_EAST_2)))
            .copy()
            .setMongoDBVersion("6.0.0")
            .build();
    final ReplicaSetHardware shardedReplicaSetHardwareForDifferentCluster =
        new ReplicaSetHardware(
            ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                0, awsEastTwoContainer.getId(), differentCluster));

    final AWSMultiTargetConnectionRule multiTargetRule_stale =
        getMultiTargetRuleWithPrivateEndpointConnectionId.apply(
            Pair.of(new ObjectId(), cluster.getName()));
    final AWSMultiTargetConnectionRule multiTargetRuleForDifferentCluster_stale =
        getMultiTargetRuleWithPrivateEndpointConnectionId.apply(
            Pair.of(new ObjectId(), differentCluster.getName()));
    final List<AWSPrivateLinkTargetGroup> singleTargetRules_stale =
        getSingleTargetRulesWithPrivateEndpointConnectionId.apply(
            Pair.of(new ObjectId(), shardedReplicaSetHardware.getAllHardware().toList()));
    final List<AWSPrivateLinkTargetGroup> singleTargetRulesForDifferentCluster_stale =
        getSingleTargetRulesWithPrivateEndpointConnectionId.apply(
            Pair.of(
                new ObjectId(),
                shardedReplicaSetHardwareForDifferentCluster.getAllHardware().toList()));

    assertEquals(
        List.of(syncAWSClusterWithPrivateLink),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpoint,
            automationConfig,
            singleTargetRules_stale,
            Cluster.getCluster(cluster, List.of(shardedReplicaSetHardware)),
            List.of(multiTargetRule_null),
            plannedActionFactory,
            LOGGER,
            appSettings));

    assertEquals(
        List.of(),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpoint,
            automationConfig,
            singleTargetRulesForDifferentCluster_stale,
            Cluster.getCluster(cluster, List.of(shardedReplicaSetHardware)),
            List.of(multiTargetRule_null),
            plannedActionFactory,
            LOGGER,
            appSettings));

    assertEquals(
        List.of(syncAWSClusterWithPrivateLink),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpoint,
            automationConfig,
            singleTargetRules_null,
            Cluster.getCluster(cluster, List.of(shardedReplicaSetHardware)),
            List.of(multiTargetRule_stale),
            plannedActionFactory,
            LOGGER,
            appSettings));

    assertEquals(
        List.of(syncAWSClusterWithPrivateLink),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpoint,
            automationConfig,
            singleTargetRules_null,
            Cluster.getCluster(cluster, List.of(shardedReplicaSetHardware)),
            List.of(multiTargetRuleForDifferentCluster_stale),
            plannedActionFactory,
            LOGGER,
            appSettings));

    assertEquals(
        List.of(syncAWSClusterWithPrivateLink),
        PlanResult.getPrivateLinkActions(
            null,
            groupWithPrivateEndpoint,
            automationConfig,
            singleTargetRules_stale,
            Cluster.getCluster(cluster, List.of(shardedReplicaSetHardware)),
            List.of(multiTargetRule_stale),
            plannedActionFactory,
            LOGGER,
            appSettings));
  }

  @Test
  public void testGetPrivateLinkActionsNeedsSingleTargetRule() {
    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
        .thenReturn(false);

    final AutomationConfig automationConfig = mock(AutomationConfig.class);
    doReturn(new Deployment()).when(automationConfig).getDeployment();

    final AzureCloudProviderContainer azureContainerWithActivePLEndpoint =
        getAzureEast2CloudProviderContainer(true);

    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    final PlannedAction syncAzurePrivateLinkWithCluster = mock(PlannedAction.class);

    final ObjectId replicationSpecId = new ObjectId();
    final ReplicationSpec azureEast2SingleRegionReplicationSpec =
        NDSModelTestFactory.getAzureReplicationSpec(
            replicationSpecId, NDSDefaults.ZONE_NAME, 1, 3, List.of(AzureRegionName.US_EAST_2));
    // Test cases for single region Azure cluster with privatelink
    {
      final NDSGroup azureEast2OnlyGroupWithActivePL =
          produceGroup(Stream.of(azureContainerWithActivePLEndpoint));
      // Cluster and move creator definition
      {
        final ShardedClusterDescription shardedClusterDescription =
            getAzureShardedClusterDescription(
                azureEast2OnlyGroupWithActivePL, azureEast2SingleRegionReplicationSpec);
        doReturn(syncAzurePrivateLinkWithCluster)
            .when(plannedActionFactory)
            .forSyncClusterPrivateEndpointConnection(
                shardedClusterDescription, CloudProvider.AZURE);
        {
          // A cluster with instance hw that has instances incompatible w standard
          // load balancer should not be eligible for PL - ie those with AzurePublicIPSKUType.BASIC
          final AzureInstanceHardware ih1 =
              getAzureInstanceHardware(
                  0, azureContainerWithActivePLEndpoint, AzurePublicIPSKUType.BASIC, true);
          assertEquals(ih1.getCloudContainerId(), azureContainerWithActivePLEndpoint.getId());
          final AzureInstanceHardware ih2 =
              getAzureInstanceHardware(1, azureContainerWithActivePLEndpoint);

          {
            final ShardedCluster legacyNetworkingShardedCluster =
                new ShardedCluster(
                    shardedClusterDescription,
                    List.of(
                        getReplicaSetHardware(
                            replicationSpecId, shardedClusterDescription, Stream.of(ih1, ih2))));
            // Due to ih1 having BASIC networking SKU, cluster is not eligible for PL
            assertEquals(
                List.of(),
                PlanResult.getPrivateLinkActions(
                    null,
                    azureEast2OnlyGroupWithActivePL,
                    automationConfig,
                    List.of(),
                    legacyNetworkingShardedCluster,
                    List.of(),
                    plannedActionFactory,
                    LOGGER,
                    appSettings));
            // All IP SKU type is STANDARD
            // ============
            // Setup - action needed - Cluster needs private link rule and container is available
            // ============
            final AzureInstanceHardware ih1nonlegacy =
                getAzureInstanceHardware(0, azureContainerWithActivePLEndpoint);
            final ShardedCluster nonLegacyNetworkingShardedCluster =
                new ShardedCluster(
                    shardedClusterDescription,
                    List.of(
                        getReplicaSetHardware(
                            replicationSpecId,
                            shardedClusterDescription,
                            Stream.of(ih1nonlegacy, ih2))));
            assertEquals(
                List.of(syncAzurePrivateLinkWithCluster),
                PlanResult.getPrivateLinkActions(
                    null,
                    azureEast2OnlyGroupWithActivePL,
                    automationConfig,
                    List.of(),
                    nonLegacyNetworkingShardedCluster,
                    List.of(),
                    plannedActionFactory,
                    LOGGER,
                    appSettings));
          }
        }

        {
          // Assert unprovisioned instance hw won't trigger PL sync - we expect instance setup to
          // update pertinent entries
          {
            final AzureInstanceHardware unprovisionedAzureInstance =
                getAzureInstanceHardware(
                    0, azureContainerWithActivePLEndpoint, AzurePublicIPSKUType.STANDARD, false);
            final ShardedCluster nonLegacyNetworkingClusterNoneProvisioned =
                new ShardedCluster(
                    shardedClusterDescription,
                    List.of(
                        getReplicaSetHardware(
                            replicationSpecId,
                            shardedClusterDescription,
                            Stream.of(unprovisionedAzureInstance))));
            // w unprovisioned hw being the only hw, no sync expected
            assertEquals(
                List.of(),
                PlanResult.getPrivateLinkActions(
                    null,
                    azureEast2OnlyGroupWithActivePL,
                    automationConfig,
                    List.of(),
                    nonLegacyNetworkingClusterNoneProvisioned,
                    List.of(),
                    plannedActionFactory,
                    LOGGER,
                    appSettings));
            final AzureInstanceHardware provisionedAzureInstance =
                getAzureInstanceHardware(1, azureContainerWithActivePLEndpoint);
            final ShardedCluster partialProvisionedCluster =
                new ShardedCluster(
                    shardedClusterDescription,
                    List.of(
                        getReplicaSetHardware(
                            replicationSpecId,
                            shardedClusterDescription,
                            Stream.of(unprovisionedAzureInstance, provisionedAzureInstance))));
            // w a mix of provisioned and unprovisioned, expect sync
            assertEquals(
                List.of(syncAzurePrivateLinkWithCluster),
                PlanResult.getPrivateLinkActions(
                    null,
                    azureEast2OnlyGroupWithActivePL,
                    automationConfig,
                    List.of(),
                    partialProvisionedCluster,
                    List.of(),
                    plannedActionFactory,
                    LOGGER,
                    appSettings));
          }
        }
      }
    }
    // ============
    // Setup - action needed - Azure has hanging private link connections
    // ============

    {
      final AzureCloudProviderContainer containerWoPL = getAzureEast2CloudProviderContainer(false);
      // Matching container does not have private link connection
      final NDSGroup azureGroupNoPrivateLink = produceGroup(Stream.of(containerWoPL));
      // Assert that PL endpoint is not active
      assertFalse(
          azureGroupNoPrivateLink
              .getCloudProviderContainers()
              .get(0)
              .getEndpointServices()
              .get(0)
              .isActive());
      final AzureInstanceHardware ih1 = getAzureInstanceHardware(0, containerWoPL);
      final AzureInstanceHardware ih2 = getAzureInstanceHardware(1, containerWoPL);

      final ShardedClusterDescription shardedClusterForGroupWoPl =
          getAzureShardedClusterDescription(
              azureGroupNoPrivateLink, azureEast2SingleRegionReplicationSpec);

      final ShardedCluster nonLegacyNetworkingCluster =
          new ShardedCluster(
              shardedClusterForGroupWoPl,
              List.of(
                  getReplicaSetHardware(
                      replicationSpecId, shardedClusterForGroupWoPl, Stream.of(ih1, ih2))));

      final ObjectId instanceId = ih1.getInstanceId();
      // Simulate 1st instance having a hanging PL rule
      final PrivateLinkConnectionRule rule = mock(PrivateLinkConnectionRule.class);
      doReturn(Collections.singletonList(instanceId)).when(rule).getInstanceIds();
      doReturn(syncAzurePrivateLinkWithCluster)
          .when(plannedActionFactory)
          .forSyncClusterPrivateEndpointConnection(shardedClusterForGroupWoPl, CloudProvider.AZURE);
      // Action required
      assertEquals(
          List.of(syncAzurePrivateLinkWithCluster),
          PlanResult.getPrivateLinkActions(
              null,
              azureGroupNoPrivateLink,
              automationConfig,
              List.of(rule),
              nonLegacyNetworkingCluster,
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings));

      // Action not required when no rules
      assertEquals(
          List.of(),
          PlanResult.getPrivateLinkActions(
              null,
              azureGroupNoPrivateLink,
              automationConfig,
              List.of(),
              nonLegacyNetworkingCluster,
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings));
    }

    // Adding a GCP node with no privatelink - action needed to remove privatelink
    {
      final GCPCloudProviderContainer gcpContainer =
          new GCPCloudProviderContainer(NDSModelTestFactory.getGCPContainer());
      final ObjectId crossProviderReplicationSpecId = new ObjectId();
      final ReplicationSpec crossProviderSpec =
          new ReplicationSpec(
              crossProviderReplicationSpecId,
              new ObjectId(),
              new ObjectId(),
              "zone",
              3,
              List.of(
                  NDSModelTestFactory.getShardRegionConfigForRegion(
                      GCPRegionName.US_EAST_4, GCPNDSInstanceSize.M10, 1, 1, 0, 0, 0),
                  NDSModelTestFactory.getShardRegionConfigForRegion(
                      AzureRegionName.US_EAST_2, AzureNDSInstanceSize.M10, 1, 1, 0, 0, 0)));
      final NDSGroup azureWPLAndGCPWOPLGroup =
          produceGroup(Stream.of(azureContainerWithActivePLEndpoint, gcpContainer));

      final AzureInstanceHardware azureIh =
          getAzureInstanceHardware(0, azureContainerWithActivePLEndpoint);
      final BasicDBObject gcpIHDoc =
          getGCPInstanceHardwareFull(
              "gcphostname",
              "public",
              "mesh",
              "private",
              gcpContainer.getId(),
              0,
              GCPNDSInstanceSize.M10,
              GCPInstanceFamily.N2,
              OS.AL2,
              false);
      final GCPInstanceHardware gcpIh = new GCPInstanceHardware(gcpIHDoc);
      assertTrue(gcpIh.isProvisioned());
      assertEquals(gcpIh.getAction(), Action.NONE);

      final BasicDBObject rsDoc =
          NDSModelTestFactory.getClusterDescription(
              azureWPLAndGCPWOPLGroup.getGroupId(), "cross provider cluster", List.of("localhost"));
      rsDoc.put(
          ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST,
          DbUtils.toBasicDBList(crossProviderSpec.toDBObject()));
      rsDoc.put(
          ClusterDescription.FieldDefs.CLUSTER_TYPE, ClusterDescription.ClusterType.REPLICASET);

      final ClusterDescription replicaSetDescription = new ClusterDescription(rsDoc);

      final PrivateLinkConnectionRule rule = mock(PrivateLinkConnectionRule.class);
      doReturn(List.of(azureIh.getInstanceId())).when(rule).getInstanceIds();

      final Cluster crossCloudCluster =
          Cluster.getCluster(
              replicaSetDescription,
              List.of(
                  getReplicaSetHardware(
                      crossProviderReplicationSpecId,
                      replicaSetDescription,
                      Stream.of(azureIh, gcpIh))));

      doReturn(syncAzurePrivateLinkWithCluster)
          .when(plannedActionFactory)
          .forSyncClusterPrivateEndpointConnection(replicaSetDescription, CloudProvider.AZURE);
      // With Azure rule present, need to sync to remove rule due to GCP node w/o PL
      assertEquals(
          List.of(syncAzurePrivateLinkWithCluster),
          PlanResult.getPrivateLinkActions(
              null,
              azureWPLAndGCPWOPLGroup,
              automationConfig,
              List.of(rule),
              crossCloudCluster,
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings));
      // W/o Azure rule present, no need to snyc
      assertEquals(
          List.of(),
          PlanResult.getPrivateLinkActions(
              null,
              azureWPLAndGCPWOPLGroup,
              automationConfig,
              List.of(),
              crossCloudCluster,
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings));
    }

    // Adding an AWS node in a region with privatelink - no action needed, provision will handle it
    {
      final AWSPrivateLinkInterfaceEndpoint endpoint =
          new AWSPrivateLinkInterfaceEndpoint(
              "endpointId2",
              null,
              "hostname2",
              AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
              null,
              false,
              1);
      final AWSCloudProviderContainer awsContainer =
          new AWSCloudProviderContainer(
              NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
                  DbUtils.toBasicDBList(endpoint.toDBObject())));

      final NDSGroup azureAndAWSBothWPLGroup =
          produceGroup(Stream.of(awsContainer, azureContainerWithActivePLEndpoint));

      final BasicDBObject awsHwBaseDoc =
          getAWSInstanceHardwareFull(
              "awsHostname",
              "awsPublic",
              "awsMesh",
              "awsPrivate",
              awsContainer.getId(),
              0,
              AWSNDSInstanceSize.M10,
              false);
      final AWSInstanceHardware awsIh1 = new AWSInstanceHardware(awsHwBaseDoc);
      assertTrue(awsIh1.isProvisioned());
      assertEquals(awsIh1.getAction(), Action.NONE);
      awsHwBaseDoc.put(FieldDefs.PROVISIONED, false);
      final AWSInstanceHardware awsIh1NotProvisioned = new AWSInstanceHardware(awsHwBaseDoc);
      assertFalse(awsIh1NotProvisioned.isProvisioned());
      assertEquals(awsIh1NotProvisioned.getAction(), Action.NONE);

      final AzureInstanceHardware azureIh =
          getAzureInstanceHardware(1, azureContainerWithActivePLEndpoint);
      final ObjectId crossProviderReplicationSpecId = new ObjectId();
      final ReplicationSpec crossProviderSpec =
          new ReplicationSpec(
              crossProviderReplicationSpecId,
              new ObjectId(),
              new ObjectId(),
              "zone",
              3,
              List.of(
                  NDSModelTestFactory.getShardRegionConfigForRegion(
                      AWSRegionName.US_EAST_1, AWSNDSInstanceSize.M10, 1, 1, 0, 0, 0),
                  NDSModelTestFactory.getShardRegionConfigForRegion(
                      AzureRegionName.US_EAST_2, AzureNDSInstanceSize.M10, 1, 1, 0, 0, 0)));
      final BasicDBObject rsDoc =
          NDSModelTestFactory.getClusterDescription(
              azureAndAWSBothWPLGroup.getGroupId(), "cross provider cluster", List.of("localhost"));
      rsDoc.put(
          ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST,
          DbUtils.toBasicDBList(crossProviderSpec.toDBObject()));
      rsDoc.put(
          ClusterDescription.FieldDefs.CLUSTER_TYPE, ClusterDescription.ClusterType.REPLICASET);

      final ClusterDescription replicaSetDescription = new ClusterDescription(rsDoc);

      final PrivateLinkConnectionRule rule = mock(PrivateLinkConnectionRule.class);
      doReturn(List.of(azureIh.getInstanceId())).when(rule).getInstanceIds();

      final PlannedAction syncAWSPrivateLinkWithCluster = mock(PlannedAction.class);
      doReturn(syncAWSPrivateLinkWithCluster)
          .when(plannedActionFactory)
          .forSyncClusterPrivateEndpointConnection(replicaSetDescription, CloudProvider.AWS);
      doReturn(syncAzurePrivateLinkWithCluster)
          .when(plannedActionFactory)
          .forSyncClusterPrivateEndpointConnection(replicaSetDescription, CloudProvider.AZURE);
      // If there is an AWS instance not provisioned, we should sync w Azure but not with AWS.
      {
        final Cluster azureAwsClusterAwsNotProvisioned =
            Cluster.getCluster(
                replicaSetDescription,
                List.of(
                    getReplicaSetHardware(
                        crossProviderReplicationSpecId,
                        replicaSetDescription,
                        Stream.of(azureIh, awsIh1NotProvisioned))));
        // If Azure has FW rule already, no need to sync
        assertEquals(
            List.of(),
            PlanResult.getPrivateLinkActions(
                null,
                azureAndAWSBothWPLGroup,
                automationConfig,
                List.of(rule),
                azureAwsClusterAwsNotProvisioned,
                List.of(),
                plannedActionFactory,
                LOGGER,
                appSettings));
        // If it does not have FW rule, needs sync
        assertEquals(
            List.of(syncAzurePrivateLinkWithCluster),
            PlanResult.getPrivateLinkActions(
                null,
                azureAndAWSBothWPLGroup,
                automationConfig,
                List.of(),
                azureAwsClusterAwsNotProvisioned,
                List.of(),
                plannedActionFactory,
                LOGGER,
                appSettings));
      }
      // When AZ rule present for all instances and AWS is provisioned, should just sync with AWS
      {
        final Cluster azureAwsClusterAwsProvisioned =
            Cluster.getCluster(
                replicaSetDescription,
                List.of(
                    getReplicaSetHardware(
                        crossProviderReplicationSpecId,
                        replicaSetDescription,
                        Stream.of(azureIh, awsIh1))));
        assertEquals(
            List.of(syncAWSPrivateLinkWithCluster),
            PlanResult.getPrivateLinkActions(
                null,
                azureAndAWSBothWPLGroup,
                automationConfig,
                List.of(rule),
                azureAwsClusterAwsProvisioned,
                List.of(),
                plannedActionFactory,
                LOGGER,
                appSettings));
        // When no rules present and AWS+Azure have provisioned HW, sync with both
        assertEquals(
            Set.of(syncAzurePrivateLinkWithCluster, syncAWSPrivateLinkWithCluster),
            new HashSet<>(
                PlanResult.getPrivateLinkActions(
                    null,
                    azureAndAWSBothWPLGroup,
                    automationConfig,
                    List.of(),
                    azureAwsClusterAwsProvisioned,
                    List.of(),
                    plannedActionFactory,
                    LOGGER,
                    appSettings)));
      }
    }
    // Adding a new serverless cluster - no action needed
    final NDSGroup serverlessGroup = spy(produceGroup(Stream.of()));
    when(serverlessGroup.getCloudProviderContainer(
            CloudProvider.SERVERLESS, AWSRegionName.US_EAST_1, "cluster0"))
        .thenReturn(Optional.empty());
    final ClusterDescription mcCd = mock(ClusterDescription.class);
    final ReplicationSpec mcRs = mock(ReplicationSpec.class);

    when(mcCd.getTenantType()).thenReturn(CloudProvider.SERVERLESS);
    when(mcCd.isTenantCluster()).thenReturn(true);
    when(mcCd.getReplicationSpecsWithShardData()).thenReturn(List.of(mcRs));

    final RegionConfig regionConfig1 = mock(RegionConfig.class);
    when(mcRs.getRegionConfigs()).thenReturn(List.of(regionConfig1));
    when(regionConfig1.getRegionName()).thenReturn(AWSRegionName.US_EAST_1);
    when(mcCd.getName()).thenReturn("cluster0");

    final ReplicaSetHardware mcRsh = mock(ReplicaSetHardware.class);

    doReturn(ClusterDescription.ClusterType.REPLICASET).when(mcCd).getClusterType();
    assertEquals(
        List.of(),
        PlanResult.getPrivateLinkActions(
            null,
            serverlessGroup,
            automationConfig,
            List.of(),
            Cluster.getCluster(mcCd, List.of(mcRsh)),
            List.of(),
            plannedActionFactory,
            LOGGER,
            appSettings));

    assertEquals(
        List.of(),
        PlanResult.getPrivateLinkActions(
            null,
            serverlessGroup,
            automationConfig,
            List.of(),
            Cluster.getCluster(mcCd, List.of(mcRsh)),
            List.of(),
            plannedActionFactory,
            LOGGER,
            appSettings));
  }

  @Test
  public void testGetPrivateLinkActionsNeedsProxyProtocolForAwsSync() {
    final AppSettings appSettings = mock(AppSettings.class);
    final Group group = mock(Group.class);
    final AutomationConfig automationConfig = mock(AutomationConfig.class);
    final Deployment deployment = mock(Deployment.class);
    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    final PlannedAction syncAWSClusterWithPrivateLink = mock(PlannedAction.class);

    final ObjectId groupId = new ObjectId();
    final BasicDBList privateEndpoints = new BasicDBList();
    final String endpointId = "us-east-endpoint";
    privateEndpoints.add(
        new AWSPrivateLinkInterfaceEndpoint(
                endpointId,
                null,
                "us.east.com",
                AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                null,
                false,
                0)
            .toDBObject());

    final BasicDBObject awsContainerWithPrivateEndpointsDBObject =
        NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
            privateEndpoints, AWSRegionName.US_EAST_1, new ObjectId());

    final AWSCloudProviderContainer awsContainer =
        new AWSCloudProviderContainer(awsContainerWithPrivateEndpointsDBObject);
    final ObjectId privateEndpointConnectionId = awsContainer.getEndpointService().get().getId();

    final NDSGroup groupWithPrivateEndpoint =
        produceGroup(
            Stream.of(new AWSCloudProviderContainer(awsContainerWithPrivateEndpointsDBObject)),
            groupId);

    // Create REPLICA SET cluster with both MongoDB version AND FCV set
    final ClusterDescription replicaSetCluster =
        NDSModelTestFactory.getDefaultClusterDescription(
                groupId,
                "replica-cluster",
                NDSModelTestFactory.getRegionConfigs(3, List.of(AWSRegionName.US_EAST_1)))
            .copy()
            .setMongoDBVersion("8.1.0")
            .setFixedFeatureCompatibilityVersion(Optional.of(new FixedVersion("8.1")))
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .build();

    final BasicDBObject replicaSetHardwareObject =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, awsContainer.getId(), replicaSetCluster);
    final ReplicaSetHardware replicaSetHardware = new ReplicaSetHardware(replicaSetHardwareObject);

    // Mock deployment - no sharded cluster exists for replica set
    when(automationConfig.getDeployment()).thenReturn(deployment);
    when(deployment.getShardedCluster(replicaSetCluster.getDeploymentClusterName()))
        .thenReturn(Optional.empty());

    // Set up the factory to return our mock action
    when(plannedActionFactory.forSyncClusterPrivateEndpointConnection(
            eq(replicaSetCluster), eq(CloudProvider.AWS)))
        .thenReturn(syncAWSClusterWithPrivateLink);

    final List<AWSPrivateLinkTargetGroup> rulesWithMongodPublicPorts =
        replicaSetHardware
            .getAllHardware()
            .map(
                instance ->
                    new AWSPrivateLinkTargetGroup(
                        groupId,
                        instance.getInstanceId(),
                        "targetGroupArn-" + instance.getMemberIndex(),
                        1024 + instance.getMemberIndex(),
                        "listenerArn-" + instance.getMemberIndex(),
                        instance.getHostnameForAgents().orElse(null),
                        false,
                        null,
                        false,
                        NDSDefaults
                            .MONGOD_PUBLIC_PORT, // Public port but cluster expects proxy protocol
                        privateEndpointConnectionId,
                        false,
                        false))
            .collect(Collectors.toList());

    final List<AWSPrivateLinkTargetGroup> rulesWithMongodProxyPorts =
        replicaSetHardware
            .getAllHardware()
            .map(
                instance ->
                    new AWSPrivateLinkTargetGroup(
                        groupId,
                        instance.getInstanceId(),
                        "targetGroupArn-" + instance.getMemberIndex(),
                        1024 + instance.getMemberIndex(),
                        "listenerArn-" + instance.getMemberIndex(),
                        instance.getHostnameForAgents().orElse(null),
                        false,
                        null,
                        false,
                        NDSDefaults
                            .MONGOD_PROXY_PROTOCOL_INGRESS_PORT, // Correct proxy protocol port
                        privateEndpointConnectionId,
                        false,
                        false))
            .collect(Collectors.toList());

    // Proxy protocol for aws ff enabled; rules with MONGOD public ports -> should sync (mismatch
    // detected)
    {
      when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
          .thenReturn(true);

      List<PlannedAction> result =
          PlanResult.getPrivateLinkActions(
              group,
              groupWithPrivateEndpoint,
              automationConfig,
              rulesWithMongodPublicPorts,
              Cluster.getCluster(replicaSetCluster, List.of(replicaSetHardware)),
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings);

      assertEquals(List.of(syncAWSClusterWithPrivateLink), result);
    }

    // Proxy protocol for aws ff disabled; rules with MONGOD public ports -> should NOT sync (no
    // mismatch)
    {
      when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
          .thenReturn(false);

      List<PlannedAction> result =
          PlanResult.getPrivateLinkActions(
              group,
              groupWithPrivateEndpoint,
              automationConfig,
              rulesWithMongodPublicPorts,
              Cluster.getCluster(replicaSetCluster, List.of(replicaSetHardware)),
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings);

      assertEquals(List.of(), result);
    }

    // Proxy protocol for aws ff enabled; rules with correct MONGOD proxy protocol ports -> should
    // NOT sync (no mismatch)
    {
      when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
          .thenReturn(true);

      List<PlannedAction> result =
          PlanResult.getPrivateLinkActions(
              group,
              groupWithPrivateEndpoint,
              automationConfig,
              rulesWithMongodProxyPorts,
              Cluster.getCluster(replicaSetCluster, List.of(replicaSetHardware)),
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings);

      assertEquals(List.of(), result);
    }

    // Proxy protocol for aws ff disabled; rules with MONGOD proxy protocol ports -> should sync
    // (reverse mismatch)
    {
      when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
          .thenReturn(false);

      List<PlannedAction> result =
          PlanResult.getPrivateLinkActions(
              group,
              groupWithPrivateEndpoint,
              automationConfig,
              rulesWithMongodProxyPorts,
              Cluster.getCluster(replicaSetCluster, List.of(replicaSetHardware)),
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings);

      assertEquals(List.of(syncAWSClusterWithPrivateLink), result);
    }
  }

  @Test
  public void testGetPrivateLinkActionsProxyProtocolOnlyUsesClusterRules() {
    // This test verifies that shouldSyncForProxyProtocolForAws only considers connection rules
    // for the specific cluster, not all rules in the group
    final AppSettings appSettings = mock(AppSettings.class);
    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    final Group group = mock(Group.class);
    final AutomationConfig automationConfig = mock(AutomationConfig.class);
    final Deployment deployment = mock(Deployment.class);
    final PlannedAction syncAWSClusterWithPrivateLink = mock(PlannedAction.class);

    doReturn(syncAWSClusterWithPrivateLink)
        .when(plannedActionFactory)
        .forSyncClusterPrivateEndpointConnection(
            any(ClusterDescription.class), eq(CloudProvider.AWS));

    final ObjectId groupId = new ObjectId();
    final BasicDBList privateEndpoints = new BasicDBList();
    final String endpointId = "vpce-123";
    privateEndpoints.add(
        new AWSPrivateLinkInterfaceEndpoint(
                endpointId,
                null,
                AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                null,
                false,
                0)
            .toDBObject());

    final BasicDBObject awsContainerWithPrivateEndpointsDBObject =
        NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
            privateEndpoints, AWSRegionName.US_EAST_1, new ObjectId());

    final AWSCloudProviderContainer awsContainer =
        new AWSCloudProviderContainer(awsContainerWithPrivateEndpointsDBObject);
    final ObjectId privateEndpointConnectionId = awsContainer.getEndpointService().get().getId();

    final NDSGroup groupWithPrivateEndpoint =
        produceGroup(
            Stream.of(new AWSCloudProviderContainer(awsContainerWithPrivateEndpointsDBObject)),
            groupId);

    // Create REPLICA SET cluster with both MongoDB version AND FCV set
    final ClusterDescription replicaSetCluster =
        NDSModelTestFactory.getDefaultClusterDescription(
                groupId,
                "replica-cluster",
                NDSModelTestFactory.getRegionConfigs(3, List.of(AWSRegionName.US_EAST_1)))
            .copy()
            .setMongoDBVersion("8.1.0")
            .setFixedFeatureCompatibilityVersion(Optional.of(new FixedVersion("8.1")))
            .setClusterType(ClusterDescription.ClusterType.REPLICASET)
            .build();

    final BasicDBObject replicaSetHardwareObject =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, awsContainer.getId(), replicaSetCluster);
    final ReplicaSetHardware replicaSetHardware = new ReplicaSetHardware(replicaSetHardwareObject);

    // Mock deployment - no sharded cluster exists for replica set
    when(automationConfig.getDeployment()).thenReturn(deployment);
    when(deployment.getShardedCluster(replicaSetCluster.getDeploymentClusterName()))
        .thenReturn(Optional.empty());

    // Set up the factory to return our mock action
    when(plannedActionFactory.forSyncClusterPrivateEndpointConnection(
            eq(replicaSetCluster), eq(CloudProvider.AWS)))
        .thenReturn(syncAWSClusterWithPrivateLink);

    // Create connection rules for the cluster instances (with mismatched ports)
    final List<AWSPrivateLinkTargetGroup> rulesForCluster =
        replicaSetHardware
            .getAllHardware()
            .map(
                instance ->
                    new AWSPrivateLinkTargetGroup(
                        groupId,
                        instance.getInstanceId(),
                        "targetGroupArn-" + instance.getMemberIndex(),
                        1024 + instance.getMemberIndex(),
                        "listenerArn-" + instance.getMemberIndex(),
                        instance.getHostnameForAgents().orElse(null),
                        false,
                        null,
                        false,
                        NDSDefaults.MONGOD_PUBLIC_PORT, // Mismatched port
                        privateEndpointConnectionId,
                        false,
                        false))
            .collect(Collectors.toList());

    // Create connection rules for OTHER clusters in the same group (also with mismatched ports)
    final ObjectId otherInstanceId1 = new ObjectId();
    final ObjectId otherInstanceId2 = new ObjectId();
    final List<AWSPrivateLinkTargetGroup> rulesForOtherClusters =
        List.of(
            new AWSPrivateLinkTargetGroup(
                groupId,
                otherInstanceId1,
                "targetGroupArn-other1",
                2048,
                "listenerArn-other1",
                "other-hostname-1",
                false,
                null,
                false,
                NDSDefaults.MONGOD_PUBLIC_PORT, // Also mismatched port
                privateEndpointConnectionId,
                false,
                false),
            new AWSPrivateLinkTargetGroup(
                groupId,
                otherInstanceId2,
                "targetGroupArn-other2",
                2049,
                "listenerArn-other2",
                "other-hostname-2",
                false,
                null,
                false,
                NDSDefaults.MONGOD_PUBLIC_PORT, // Also mismatched port
                privateEndpointConnectionId,
                false,
                false));

    // Combine all rules (cluster + other clusters)
    final List<AWSPrivateLinkTargetGroup> allRulesInGroup = new ArrayList<>();
    allRulesInGroup.addAll(rulesForCluster);
    allRulesInGroup.addAll(rulesForOtherClusters);

    // Test with proxy protocol enabled - should sync because cluster rules have mismatched ports
    {
      when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
          .thenReturn(true);

      List<PlannedAction> result =
          PlanResult.getPrivateLinkActions(
              group,
              groupWithPrivateEndpoint,
              automationConfig,
              allRulesInGroup, // Pass all rules including those for other clusters
              Cluster.getCluster(replicaSetCluster, List.of(replicaSetHardware)),
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings);

      assertEquals(List.of(syncAWSClusterWithPrivateLink), result);
    }

    // Test with rules for this cluster but all the replica set hardware instances are paused -
    // should NOT sync
    {
      final ReplicaSetHardware pausedReplicaSetHardware =
          new ReplicaSetHardware(replicaSetHardwareObject);
      pausedReplicaSetHardware.getAllHardware().forEach(instance -> instance.setIsPaused(true));

      List<PlannedAction> result =
          PlanResult.getPrivateLinkActions(
              group,
              groupWithPrivateEndpoint,
              automationConfig,
              allRulesInGroup, // Pass all rules including those for other clusters
              Cluster.getCluster(replicaSetCluster, List.of(pausedReplicaSetHardware)),
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings);

      assertEquals(List.of(), result);
    }

    // Test with only rules for other clusters (not this cluster) - should NOT sync
    // But we need to provide rules for this cluster with CORRECT ports to avoid triggering
    // shouldSyncForInstancesNeedingConnectionRule
    {
      when(appSettings.isFeatureFlagInEnabledState(FeatureFlag.PRIVATE_LINK_PROXY_PROTOCOL_AWS))
          .thenReturn(true);

      // Create rules for cluster instances with CORRECT ports (no proxy protocol mismatch)
      final List<AWSPrivateLinkTargetGroup> rulesForClusterWithCorrectPorts =
          replicaSetHardware
              .getAllHardware()
              .map(
                  instance ->
                      new AWSPrivateLinkTargetGroup(
                          groupId,
                          instance.getInstanceId(),
                          "targetGroupArn-" + instance.getMemberIndex(),
                          1024 + instance.getMemberIndex(),
                          "listenerArn-" + instance.getMemberIndex(),
                          instance.getHostnameForAgents().orElse(null),
                          false,
                          null,
                          false,
                          1024
                              + instance
                                  .getMemberIndex(), // CORRECT port (matches target group port)
                          privateEndpointConnectionId,
                          false,
                          false))
              .collect(Collectors.toList());

      // Combine correct cluster rules with other cluster rules (which have mismatched ports)
      final List<AWSPrivateLinkTargetGroup> rulesWithOtherClustersHavingMismatch =
          new ArrayList<>();
      rulesWithOtherClustersHavingMismatch.addAll(rulesForClusterWithCorrectPorts);
      rulesWithOtherClustersHavingMismatch.addAll(rulesForOtherClusters);

      List<PlannedAction> result =
          PlanResult.getPrivateLinkActions(
              group,
              groupWithPrivateEndpoint,
              automationConfig,
              rulesWithOtherClustersHavingMismatch, // Cluster has correct ports, others have
              // mismatched
              Cluster.getCluster(replicaSetCluster, List.of(replicaSetHardware)),
              List.of(),
              plannedActionFactory,
              LOGGER,
              appSettings);

      assertEquals(List.of(), result); // Should not sync because cluster rules have correct ports
    }
  }

  @Test
  public void testGroupFreeContainersByBackingCloudProvider() {
    testGroupTenantContainersByBackingCloudProvider(Set.of(CloudProvider.FREE));
  }

  @Test
  public void testGroupServerlessContainersByBackingCloudProvider() {
    testGroupTenantContainersByBackingCloudProvider(Set.of(CloudProvider.SERVERLESS));
  }

  @Test
  public void testGroupFlexContainersByBackingCloudProvider() {
    testGroupTenantContainersByBackingCloudProvider(Set.of(CloudProvider.FLEX));
  }

  @Test
  public void testGroupTenantContainersByBackingCloudProvider() {
    testGroupTenantContainersByBackingCloudProvider(
        Set.of(CloudProvider.FREE, CloudProvider.SERVERLESS, CloudProvider.FLEX));
  }

  private void testGetBackingCloudProviderForTenantContainer(final CloudProvider pProvider) {
    final Cluster tenantClusterOnAws0 = mock(Cluster.class);
    final String awsClusterName0 = "awsCluster0";
    final ClusterDescription tenantClusterAWSDescription0 = mock(ClusterDescription.class);
    when(tenantClusterOnAws0.getClusterDescription()).thenReturn(tenantClusterAWSDescription0);
    when(tenantClusterAWSDescription0.isTenantCluster()).thenReturn(true);
    when(tenantClusterAWSDescription0.getBackingProvider()).thenReturn(CloudProvider.AWS);

    final Cluster tenantClusterOnAzure0 = mock(Cluster.class);
    final String azureClusterName0 = "azureCluster0";
    final ClusterDescription tenantClusterAzureDescription0 = mock(ClusterDescription.class);
    when(tenantClusterOnAzure0.getClusterDescription()).thenReturn(tenantClusterAzureDescription0);
    when(tenantClusterAzureDescription0.isTenantCluster()).thenReturn(true);
    when(tenantClusterAzureDescription0.getBackingProvider()).thenReturn(CloudProvider.AZURE);
    final String azureClusterName1 = "azureCluster1";

    final Cluster tenantClusterOnAzureNotProvisioned = mock(Cluster.class);
    final String azureClusterName2 = "azureCluster2";
    final ClusterDescription tenantClusterAzureDescription2 = mock(ClusterDescription.class);
    when(tenantClusterOnAzureNotProvisioned.getClusterDescription())
        .thenReturn(tenantClusterAzureDescription2);
    when(tenantClusterAzureDescription2.isTenantCluster()).thenReturn(true);
    when(tenantClusterAzureDescription2.getBackingProvider()).thenReturn(CloudProvider.AZURE);

    final Cluster tenantClusterOnAzureClusterDescriptionNotExist = mock(Cluster.class);
    final String azureClusterName3 = "azureCluster3";
    when(tenantClusterOnAzureClusterDescriptionNotExist.getClusterDescription()).thenReturn(null);

    final Class<? extends TenantCloudProviderContainer> containerClass =
        getTenantCloudProviderContainerClassByProvider(pProvider);

    final TenantCloudProviderContainer aws0CloudProviderContainer = mock(containerClass);
    final TenantCloudProviderContainer azureCloudProviderContainer = mock(containerClass);
    final TenantCloudProviderContainer azureCloudProviderContainerNotInMapping =
        mock(containerClass);
    final TenantCloudProviderContainer azureCloudProviderContainerNotProvisioned =
        mock(containerClass);
    final TenantCloudProviderContainer azureCloudProviderContainerClusterDescriptionNotExist =
        mock(containerClass);

    when(aws0CloudProviderContainer.getTenantClusterName()).thenReturn(awsClusterName0);
    when(azureCloudProviderContainer.getTenantClusterName()).thenReturn(azureClusterName0);
    when(azureCloudProviderContainerNotInMapping.getTenantClusterName())
        .thenReturn(azureClusterName1);
    when(azureCloudProviderContainerNotProvisioned.getTenantClusterName())
        .thenReturn(azureClusterName2);
    when(azureCloudProviderContainerClusterDescriptionNotExist.getTenantClusterName())
        .thenReturn(azureClusterName3);

    when(aws0CloudProviderContainer.getCloudProvider()).thenReturn(pProvider);
    when(azureCloudProviderContainer.getCloudProvider()).thenReturn(pProvider);
    when(azureCloudProviderContainerNotInMapping.getCloudProvider()).thenReturn(pProvider);
    when(azureCloudProviderContainerNotProvisioned.getCloudProvider()).thenReturn(pProvider);
    when(azureCloudProviderContainerClusterDescriptionNotExist.getCloudProvider())
        .thenReturn(pProvider);

    final Map<String, Cluster> nameToCluster = new HashMap<>();

    // this test puts all but azureClusterName1 in map
    nameToCluster.put(awsClusterName0, tenantClusterOnAws0);
    nameToCluster.put(azureClusterName0, tenantClusterOnAzure0);
    nameToCluster.put(azureClusterName2, tenantClusterOnAzureNotProvisioned);
    nameToCluster.put(azureClusterName3, tenantClusterOnAzureClusterDescriptionNotExist);

    final BiFunction<CloudProviderContainer, Map<String, Cluster>, Optional<CloudProvider>>
        testFunc =
            (container, nameToClusterMap) ->
                switch (pProvider) {
                  case FREE ->
                      PlanResult.getBackingCloudProviderForFreeContainer(
                          container, nameToClusterMap);
                  case SERVERLESS ->
                      PlanResult.getBackingCloudProviderForServerlessContainer(
                          container, nameToClusterMap);
                  case FLEX ->
                      PlanResult.getBackingCloudProviderForFlexContainer(
                          container, nameToClusterMap);
                  default -> throw new IllegalArgumentException(pProvider.name());
                };

    final Optional<CloudProvider> getBackingCloudProviderForAwsFreeCloudContainer0 =
        testFunc.apply(aws0CloudProviderContainer, nameToCluster);
    assertEquals(CloudProvider.AWS, getBackingCloudProviderForAwsFreeCloudContainer0.get());

    final Optional<CloudProvider> getBackingCloudProviderForAzureFreeCloudContainer =
        testFunc.apply(azureCloudProviderContainer, nameToCluster);
    assertEquals(CloudProvider.AZURE, getBackingCloudProviderForAzureFreeCloudContainer.get());

    final Optional<CloudProvider> getBackingCloudProviderForAzureContainerNotInMapping =
        testFunc.apply(azureCloudProviderContainerNotInMapping, nameToCluster);
    assertTrue(getBackingCloudProviderForAzureContainerNotInMapping.isEmpty());

    final Optional<CloudProvider> getBackingCloudProviderForAzureContainerNotProvisioned =
        testFunc.apply(azureCloudProviderContainerNotProvisioned, nameToCluster);
    assertEquals(CloudProvider.AZURE, getBackingCloudProviderForAzureContainerNotProvisioned.get());

    final Optional<CloudProvider> getBackingCloudProviderForAzureClusterDescriptionNotExist =
        testFunc.apply(azureCloudProviderContainerClusterDescriptionNotExist, nameToCluster);
    assertTrue(getBackingCloudProviderForAzureClusterDescriptionNotExist.isEmpty());

    try {
      testFunc.apply(mock(AWSCloudProviderContainer.class), nameToCluster);
      fail();
    } catch (final Exception pE) {
      assertInstanceOf(IllegalArgumentException.class, pE);
      assertTrue(
          pE.getMessage()
              .contains(
                  String.format(
                      "Received unexpected cloud provider of type %s and container of type %s",
                      pProvider.name(), AWSCloudProviderContainer.class.getSimpleName())));
    }
  }

  @Test
  public void testGetBackingCloudProviderForFreeContainer() {
    testGetBackingCloudProviderForTenantContainer(CloudProvider.FREE);
  }

  @Test
  public void testGetBackingCloudProviderForServerlessContainer() {
    testGetBackingCloudProviderForTenantContainer(CloudProvider.SERVERLESS);
  }

  @Test
  public void testGetBackingCloudProviderForFlexContainer() {
    testGetBackingCloudProviderForTenantContainer(CloudProvider.FLEX);
  }

  @Test
  public void testPlanContainsCycle() {
    final Move m1 = new DummyMove();
    final Move m2 = new DummyMove();
    final Move m3 = new DummyMove();
    final PlannedAction p1 = new PlannedAction.Builder().moves(new Move[] {m1, m2, m3}).build();

    // No dependency
    assertFalse(PlanResult.planContainsCycle(Collections.singletonList(p1)));

    // Linear dependencies
    m2.addPredecessor(m1);
    m3.addPredecessor(m2);
    assertFalse(PlanResult.planContainsCycle(Collections.singletonList(p1)));

    // Circular dependencies
    m1.addPredecessor(m3);
    assertTrue(PlanResult.planContainsCycle(Collections.singletonList(p1)));

    final Move m4 = new DummyMove();
    final Move m5 = new DummyMove();
    final Move m6 = new DummyMove();
    final PlannedAction p2 = new PlannedAction.Builder().moves(new Move[] {m4, m5, m6}).build();

    // Undirected cycle but no directed cycle
    m5.addPredecessor(m4);
    m6.addPredecessor(m4);
    m6.addPredecessor(m5);

    assertFalse(PlanResult.planContainsCycle(Collections.singletonList(p2)));

    // Cycle in sub-graph
    final Move m7 = new DummyMove();
    final Move m8 = new DummyMove();
    final Move m9 = new DummyMove();
    final PlannedAction p3 = new PlannedAction.Builder().moves(new Move[] {m7, m8, m9}).build();

    m8.addPredecessor(m7);
    m8.addPredecessor(m9);
    m9.addPredecessor(m8);

    assertTrue(PlanResult.planContainsCycle(Collections.singletonList(p3)));

    // Two disconnected graph in which one is a complete cycle and one is not
    final Move m10 = new DummyMove();
    final Move m11 = new DummyMove();
    final Move m12 = new DummyMove();

    final PlannedAction p4 = new PlannedAction.Builder().moves(new Move[] {m10, m11, m12}).build();

    m11.addPredecessor(m12);
    m12.addPredecessor(m11);

    assertTrue(PlanResult.planContainsCycle(Collections.singletonList(p4)));
  }

  private void setHardwareAction(
      final List<ReplicaSetHardware> pReplicaSetHardware, final InstanceHardware.Action pAction) {
    pReplicaSetHardware.stream()
        .flatMap(rsh -> rsh.getHardware().stream())
        .forEach(
            ih -> {
              try {
                setHardwareAction(ih, pAction);
              } catch (final Exception e) {
                throw new RuntimeException(e);
              }
            });
  }

  private void setHardwareAction(
      final InstanceHardware pInstanceHardware, final InstanceHardware.Action pAction) {
    pInstanceHardware.setAction(pAction);
  }

  private void assertBiConnectorActions(
      final NDSGroup pNDSGroup,
      final Cluster pCluster,
      final PlannedActionFactory pPlannedActionFactory,
      final List<HostCluster> pHostClusters,
      final int pExpectedActionCount) {
    assertEquals(
        pExpectedActionCount,
        PlanResult.getBiConnectorActions(
                pNDSGroup, pCluster, pHostClusters, pPlannedActionFactory, LOGGER, false)
            .size());
  }

  @Test
  public void testGetBiConnectorActions() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup ndsGroup = mock(NDSGroup.class);
    doReturn(groupId).when(ndsGroup).getGroupId();

    final RegionConfig regionConfig = mock(RegionConfig.class);

    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn("analytics").when(clusterDescription).getName();
    doReturn(NDSDefaults.generateInternalDeploymentClusterName(ndsGroup.getGroupId(), "analytics"))
        .when(clusterDescription)
        .getDeploymentClusterName();
    doReturn(false).when(clusterDescription).isDeleteRequested();
    doReturn(ClusterDescription.State.IDLE).when(clusterDescription).getState();
    doReturn(Optional.of(HostnameScheme.INTERNAL))
        .when(clusterDescription)
        .getHostnameSchemeForAgents();

    final BiConnector cdBiConnectorSettings = mock(BiConnector.class);
    doReturn(cdBiConnectorSettings).when(clusterDescription).getBiConnector();
    doReturn(true).when(cdBiConnectorSettings).isEnabled();
    doReturn(BiConnectorReadPreference.SECONDARY).when(cdBiConnectorSettings).getReadPreference();

    final ReplicationSpec replicationSpec = mock(ReplicationSpec.class);
    doReturn(List.of(regionConfig)).when(replicationSpec).getRegionConfigs();
    doReturn(Optional.of(replicationSpec)).when(clusterDescription).getReplicationSpecById(any());

    final PlannedAction plannedAction = mock(PlannedAction.class);
    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    doReturn(plannedAction).when(plannedActionFactory).forSyncBiConnector(eq(clusterDescription));

    final ReplicaSetHardware replicaSetHardware = mock(ReplicaSetHardware.class);
    doReturn(new ObjectId()).when(replicaSetHardware).getReplicationSpecId();
    doReturn(true).when(replicaSetHardware).containsShardData();

    final List<InstanceHardware> instanceHardwareList =
        IntStream.range(0, 5)
            .mapToObj(
                i -> {
                  final InstanceHardware instanceHardware = mock(InstanceHardware.class);
                  final InstanceHardware.BiConnector ihBiConnector =
                      mock(InstanceHardware.BiConnector.class);
                  doReturn(false).when(ihBiConnector).isEnabled();
                  doReturn(ihBiConnector).when(instanceHardware).getBiConnector();
                  doReturn(true).when(instanceHardware).isProvisioned();
                  doReturn(true).when(instanceHardware).canHostBiConnector(any(), eq(false));
                  doReturn(InstanceHardware.Action.NONE).when(instanceHardware).getAction();
                  doReturn(new ObjectId()).when(instanceHardware).getInstanceId();

                  final Hostnames hostnames = mock(Hostnames.class);
                  doReturn(Optional.of(String.format("host-with-the-most-%d.partytime.com", i)))
                      .when(hostnames)
                      .getPublicHostname();
                  doReturn(
                          Optional.of(
                              String.format(
                                  "%s-shard-0-%s.partytime.com",
                                  clusterDescription.getDeploymentClusterName(), i)))
                      .when(instanceHardware)
                      .getHostnameForAgents();
                  doReturn(hostnames).when(instanceHardware).getHostnames();

                  // designate the last node as the analytics node
                  if (i == 4) {
                    doReturn(List.of(instanceHardware))
                        .when(replicaSetHardware)
                        .getAnalyticsHardware(any());
                  }
                  return instanceHardware;
                })
            .collect(Collectors.toList());
    doReturn(instanceHardwareList).when(replicaSetHardware).getHardware();

    final List<ReplicaSetHardware> replicaSetHardwareList = List.of(replicaSetHardware);

    final List<HostCluster> hostClusters =
        replicaSetHardwareList.stream()
            .map(
                rh -> {
                  final HostCluster hostCluster = mock(HostCluster.class);
                  final ReplicaSet replicaSet = mock(ReplicaSet.class);
                  doReturn(Set.of(replicaSet)).when(hostCluster).getReplicaSets();
                  doReturn(ClusterType.REPLICA_SET).when(hostCluster).getType();
                  doCallRealMethod().when(hostCluster).isReplicaSet();

                  final List<ReplicaSet.Member> members =
                      instanceHardwareList.stream()
                          .map(
                              ih -> {
                                final ReplicaSet.Member member = mock(ReplicaSet.Member.class);
                                doReturn(ReplicaSet.Member.State.SECONDARY).when(member).getState();
                                doReturn(
                                        String.format(
                                            "%s:27017", ih.getHostnameForAgents().orElse(null)))
                                    .when(member)
                                    .getHost();

                                return member;
                              })
                          .collect(Collectors.toList());
                  doReturn(members).when(replicaSet).getMembers();

                  doReturn(ndsGroup.getGroupId()).when(hostCluster).getGroupId();
                  return hostCluster;
                })
            .collect(Collectors.toList());

    // Enabled on cluster, disabled on hardware
    final Cluster cluster = mock(Cluster.class);
    doReturn(clusterDescription).when(cluster).getClusterDescription();
    doReturn(replicaSetHardwareList).when(cluster).getReplicaSets();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 1);

    // Enabled on cluster, enabled on hardware, instance in acceptable state, hardware available
    final InstanceHardware ihBiConnectorHost = instanceHardwareList.get(0);
    final InstanceHardware.BiConnector ihBiConnectorHostSettings =
        ihBiConnectorHost.getBiConnector();
    doReturn(true).when(ihBiConnectorHostSettings).isEnabled();
    doReturn(BiConnectorReadPreference.SECONDARY)
        .when(ihBiConnectorHostSettings)
        .getReadPreference();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 0);

    // Needs sync flag is set
    doReturn(Optional.of(new Date())).when(cdBiConnectorSettings).getNeedsSync();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 1);

    // Needs sync flag is not set
    doReturn(Optional.empty()).when(cdBiConnectorSettings).getNeedsSync();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 0);

    // Enabled on cluster, enabled on hardware, instance in acceptable state, current hardware
    // cannot host bi connector
    doReturn(false).when(ihBiConnectorHost).canHostBiConnector(any(), eq(false));
    hostClusters.stream()
        .map(HostCluster::getReplicaSets)
        .flatMap(Set::stream)
        .map(ReplicaSet::getMembers)
        .flatMap(List::stream)
        .forEach(m -> doReturn(ReplicaSet.Member.State.RECOVERING).when(m).getState());
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 0);

    // Enabled on cluster, enabled on hardware, instance in acceptable state, read preference
    // changed, hardware available
    doReturn(true).when(ihBiConnectorHost).canHostBiConnector(any(), eq(false));
    hostClusters.stream()
        .map(HostCluster::getReplicaSets)
        .flatMap(Set::stream)
        .map(ReplicaSet::getMembers)
        .flatMap(List::stream)
        .forEach(m -> doReturn(ReplicaSet.Member.State.SECONDARY).when(m).getState());
    doReturn(InstanceHardware.Action.NONE).when(ihBiConnectorHost).getAction();
    doReturn(BiConnectorReadPreference.PRIMARY).when(cdBiConnectorSettings).getReadPreference();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 1);

    // Enabled on cluster with analytics, not enabled on analytics node, should move to analytics
    // node
    doReturn(BiConnectorReadPreference.ANALYTICS).when(cdBiConnectorSettings).getReadPreference();
    final InstanceHardware ihBiConnectorAnalyticsHost = instanceHardwareList.get(4);
    final InstanceHardware.BiConnector ihBiConnectorAnalyticsHostSettings =
        ihBiConnectorAnalyticsHost.getBiConnector();
    doReturn(BiConnectorReadPreference.ANALYTICS)
        .when(ihBiConnectorAnalyticsHostSettings)
        .getReadPreference();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 1);

    // Enabled on cluster with analytics, enabled on analytics node, no action required
    doReturn(false).when(ihBiConnectorHostSettings).isEnabled();
    doReturn(true).when(ihBiConnectorAnalyticsHostSettings).isEnabled();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 0);

    // Enabled on cluster with analytics, enabled on previous analytics node
    // Note: This test covers the case where a cluster goes from
    // 3 electable, 1 analytics -> 5 electable, 1 analytics or
    // 3 electable, 1 read-only, 1 analytics, etc.
    // (basically anything that moves the analytics node replica set member to different hardware)
    final InstanceHardware ihBiConnectorAnalyticsMoveToHost = instanceHardwareList.get(4);
    final InstanceHardware.BiConnector ihBiConnectorAnalyticsMoveToHostSettings =
        ihBiConnectorAnalyticsMoveToHost.getBiConnector();
    doReturn(false).when(ihBiConnectorAnalyticsHostSettings).isEnabled();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 1);

    // Disabled on cluster, enabled on instance
    doReturn(false).when(cdBiConnectorSettings).isEnabled();
    doReturn(true).when(ihBiConnectorHostSettings).isEnabled();
    doReturn(false).when(ihBiConnectorAnalyticsMoveToHostSettings).isEnabled();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 1);

    // Disabled on cluster, enabled on instance, hardware unavailable
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 1);

    // Disabled on cluster, disabled on instance, hardware available
    doReturn(false).when(ihBiConnectorHostSettings).isEnabled();
    setHardwareAction(replicaSetHardwareList, InstanceHardware.Action.NONE);
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 0);

    // Cluster in a paused state
    doReturn(true).when(clusterDescription).isPaused();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, hostClusters, 0);
    doReturn(false).when(clusterDescription).isPaused();

    // Disabled on cluster, Cluster being deleted
    doReturn(true).when(clusterDescription).isDeleteRequested();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, List.of(), 0);

    // Enabled on cluster, Cluster being deleted
    doReturn(true).when(cdBiConnectorSettings).isEnabled();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, List.of(), 1);

    // Cluster in deleted state
    doReturn(State.DELETED).when(clusterDescription).getState();
    assertBiConnectorActions(ndsGroup, cluster, plannedActionFactory, List.of(), 0);
  }

  @Test
  public void testBiConnectorShouldRelocateForAnalytics() {
    // BI connector should never relocate for analytics if the BI connector is not configured to
    // read from analytics nodes.
    assertFalse(PlanResult.biConnectorShouldRelocateForAnalytics(null, null, false));

    // Create a cluster and instance hardware for tests.
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AWS);
    final List<RegionConfig> regionsConfig =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSNDSDefaults.REGION_NAME, RegionConfig.MAX_PRIORITY, 3, 1, 1, 0));
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription("analytics", regionsConfig));

    final List<ReplicaSetHardware> replicaSetHardwareList =
        Collections.singletonList(
            new ReplicaSetHardware(
                ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                    0, ndsGroup.getCloudProviderContainers().get(0).getId(), clusterDescription)));

    final Cluster cluster = Cluster.getCluster(clusterDescription, replicaSetHardwareList);
    final InstanceHardware analyticsInstanceHardware =
        replicaSetHardwareList.get(0).getHardware().get(4);

    // BI connector should not relocate if there is a BI connector active on the analytics hardware.
    assertFalse(
        PlanResult.biConnectorShouldRelocateForAnalytics(
            cluster, Collections.singletonList(analyticsInstanceHardware), true));

    // BI connector should relocate if the BI connector is active on a non-analytics node.
    assertTrue(
        PlanResult.biConnectorShouldRelocateForAnalytics(
            cluster,
            Collections.singletonList(replicaSetHardwareList.get(0).getHardware().get(3)),
            true));

    // Create a sharded cluster and instance hardware for tests.
    final ShardedClusterDescription shardedClusterDescription =
        new ShardedClusterDescription(
            NDSModelTestFactory.getAWSShardedClusterDescription("analytics", regionsConfig));

    final List<ReplicaSetHardware> shardedReplicaSetHardwareList =
        Arrays.asList(
            new ReplicaSetHardware(
                ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                    0,
                    ndsGroup.getCloudProviderContainers().get(0).getId(),
                    shardedClusterDescription)),
            new ReplicaSetHardware(
                ReplicaSetHardwareModelTestFactory.getConfigServerHardwareFull(
                    shardedClusterDescription)));

    final Cluster shardedCluster =
        Cluster.getCluster(shardedClusterDescription, shardedReplicaSetHardwareList);
    final InstanceHardware shardedAnalyticsInstanceHardware =
        shardedReplicaSetHardwareList.get(0).getHardware().get(4);

    // BI connector should not relocate if there is a BI connector active on the analytics hardware.
    assertFalse(
        PlanResult.biConnectorShouldRelocateForAnalytics(
            shardedCluster, Collections.singletonList(shardedAnalyticsInstanceHardware), true));

    // BI connector should relocate if the BI connector is active on a non-analytics node.
    assertTrue(
        PlanResult.biConnectorShouldRelocateForAnalytics(
            shardedCluster,
            Collections.singletonList(shardedReplicaSetHardwareList.get(0).getHardware().get(3)),
            true));
  }

  @Test
  public void testBiConnectorHardwareAvailable() throws IllegalAccessException {
    // Hardware should always be unavailable if the BI connector is disabled.
    assertFalse(
        PlanResult.biConnectorHardwareAvailable(
            null, null, List.of(), false, false, LOGGER, false));
    assertFalse(
        PlanResult.biConnectorHardwareAvailable(null, null, List.of(), false, true, LOGGER, false));

    // Create a cluster and instance hardware for tests.
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AWS);
    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1, RegionConfig.MAX_PRIORITY, 3, 1, 1, 0));
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(
                    ndsGroup.getGroupId(), "analytics", regionConfigs)
                .append(
                    "deploymentClusterName",
                    NDSDefaults.generateInternalDeploymentClusterName(
                        ndsGroup.getGroupId(), "analytics")));

    final List<ReplicaSetHardware> replicaSetHardwareList =
        Collections.singletonList(
            new ReplicaSetHardware(
                ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                    0, ndsGroup.getCloudProviderContainers().get(0).getId(), clusterDescription)));

    final List<HostCluster> hostClusters =
        replicaSetHardwareList.stream()
            .map(
                rh -> {
                  final HostCluster hc = new HostCluster();
                  hc.setGroupId(ndsGroup.getGroupId());
                  final String rsName = rh.getRsId();
                  hc.setName(String.format("Cluster_%s", rh.getRsId()));
                  hc.setTypeCode(ClusterType.REPLICA_SET.getCode());

                  final ReplicaSet rs =
                      new ReplicaSet.Builder(rsName)
                          .members(
                              rh.getHardware().stream()
                                  .map(
                                      ih ->
                                          new ReplicaSet.Member.Builder(
                                                  ih.getMemberIndex(),
                                                  ih.getHostnameForAgents().get() + ":27017")
                                              .state(
                                                  ih.getMemberIndex() == 0
                                                      ? ReplicaSet.Member.State.PRIMARY
                                                      : ReplicaSet.Member.State.SECONDARY)
                                              .build())
                                  .collect(Collectors.toList()))
                          .build();
                  hc.setReplicaSets(Set.of(rs));
                  return hc;
                })
            .collect(Collectors.toList());

    final List<ReplicaSetHardware> unprovisionedReplicaSetHardwareList =
        Stream.of(
                new ReplicaSetHardware(
                    ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
                        0,
                        ndsGroup.getCloudProviderContainers().get(0).getId(),
                        clusterDescription)))
            .peek(rsh -> rsh.getAllHardware().forEach(ih -> ih.setProvisioned(false)))
            .collect(Collectors.toList());

    final Cluster cluster = Cluster.getCluster(clusterDescription, replicaSetHardwareList);
    final Cluster unprovisionedCluster =
        Cluster.getCluster(clusterDescription, unprovisionedReplicaSetHardwareList);

    // Non-analytics cases.

    // 1. New cluster, no hardware, no monitoring data
    setHardwareAction(unprovisionedReplicaSetHardwareList, Action.CREATE);
    assertTrue(
        PlanResult.biConnectorHardwareAvailable(
            ndsGroup, unprovisionedCluster, List.of(), true, false, LOGGER, false));

    // 2. Hardware provisioned, but no healthy nodes
    setHardwareAction(replicaSetHardwareList, Action.HEAL_CANNOT_REPAIR);
    assertFalse(
        PlanResult.biConnectorHardwareAvailable(
            ndsGroup, cluster, hostClusters, true, false, LOGGER, false));

    // 3. Hardware provisioned, with healthy nodes
    setHardwareAction(replicaSetHardwareList, Action.NONE);
    assertTrue(
        PlanResult.biConnectorHardwareAvailable(
            ndsGroup, cluster, hostClusters, true, false, LOGGER, false));

    // 4. Hardware provisioned, has healthy nodes but missing monitoring data
    final List<HostCluster> incompleteHostClusters = List.copyOf(hostClusters);
    incompleteHostClusters.forEach(hc -> hc.setReplicaSets(null));
    assertFalse(
        PlanResult.biConnectorHardwareAvailable(
            ndsGroup, cluster, incompleteHostClusters, true, false, LOGGER, false));

    // 5. No hardware provisioned
    setHardwareAction(unprovisionedReplicaSetHardwareList, Action.NONE);
    assertFalse(
        PlanResult.biConnectorHardwareAvailable(
            ndsGroup, unprovisionedCluster, hostClusters, true, false, LOGGER, false));

    // Analytics cases.
    // 1. No hardware provisioned
    assertFalse(
        PlanResult.biConnectorHardwareAvailable(
            ndsGroup, unprovisionedCluster, List.of(), true, true, LOGGER, false));

    // 2. Hardware provisioned, but no healthy analytics nodes
    setHardwareAction(replicaSetHardwareList, Action.HEAL_CANNOT_REPAIR);
    assertFalse(
        PlanResult.biConnectorHardwareAvailable(
            ndsGroup, cluster, hostClusters, true, true, LOGGER, false));

    // 3. Hardware provisioned, with analytics healthy nodes
    setHardwareAction(replicaSetHardwareList.get(0).getHardware().get(4), Action.NONE);
    assertTrue(
        PlanResult.biConnectorHardwareAvailable(
            ndsGroup, cluster, hostClusters, true, true, LOGGER, false));
  }

  @Test
  public void testGetBackupActionsForCluster_terminateDiskBackup() {
    final Cluster cluster = mock(Cluster.class);
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(clusterDescription.getCloudProviders()).thenReturn(Set.of(CloudProvider.AWS));
    when(clusterDescription.isDeleteRequested()).thenReturn(true);
    when(cluster.getClusterDescription()).thenReturn(clusterDescription);
    final BackupJob backupJob = mock(BackupJob.class);
    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    // Case 1: paused cluster, and disk backup out of sync
    final Pair<PlannedAction, List<AuditDescription>> backupActionForPausedCluster =
        PlanResult.getBackupActionForCluster(
            cluster,
            null,
            null,
            null,
            null,
            mock(AppSettings.class),
            null,
            plannedActionFactory,
            null,
            new Date(),
            mock(Logger.class),
            false);
    assertNull(backupActionForPausedCluster);

    // Case 2: running cluster, and disk backup out of sync
    final Pair<PlannedAction, List<AuditDescription>> backupActionForCluster =
        PlanResult.getBackupActionForCluster(
            cluster,
            null,
            null,
            backupJob,
            null,
            mock(AppSettings.class),
            null,
            plannedActionFactory,
            null,
            new Date(),
            mock(Logger.class),
            false);
    assertNotNull(backupActionForCluster);
    assertEquals(1, backupActionForCluster.getLeft().getMoves().length);
    verify(plannedActionFactory, times(1)).forDeleteBackupConfig(clusterDescription);
  }

  @Test
  public void testGetBackupActionsForCluster_stopDiskBackup() {
    final Cluster cluster = mock(Cluster.class);
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(clusterDescription.getCloudProviders()).thenReturn(Set.of(CloudProvider.AWS));
    when(clusterDescription.isDiskBackupEnabled()).thenReturn(true);
    when(clusterDescription.isPaused()).thenReturn(true);
    when(clusterDescription.isBackupEnabled()).thenReturn(false);
    when(clusterDescription.isDeleteRequested()).thenReturn(false);
    when(cluster.getClusterDescription()).thenReturn(clusterDescription);

    final BackupJob backupJob = mock(BackupJob.class);
    when(backupJob.isPaused()).thenReturn(false);
    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final Pair<PlannedAction, List<AuditDescription>> backupActionForCluster =
        PlanResult.getBackupActionForCluster(
            cluster,
            null,
            null,
            backupJob,
            null,
            mock(AppSettings.class),
            null,
            plannedActionFactory,
            null,
            new Date(),
            mock(Logger.class),
            false);
    assertNotNull(backupActionForCluster);
    assertEquals(1, backupActionForCluster.getLeft().getMoves().length);
    verify(plannedActionFactory, times(1)).forSyncBackupConfig(clusterDescription);
  }

  @Test
  public void testGetBackupActionsForCluster_resumeDiskBackup() {
    final Cluster cluster = mock(Cluster.class);
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(clusterDescription.getCloudProviders()).thenReturn(Set.of(CloudProvider.AWS));
    when(clusterDescription.isDiskBackupEnabled()).thenReturn(true);
    when(clusterDescription.isPaused()).thenReturn(true);
    when(clusterDescription.isBackupEnabled()).thenReturn(false);
    when(clusterDescription.isDeleteRequested()).thenReturn(false);
    when(cluster.getClusterDescription()).thenReturn(clusterDescription);

    final BackupJob backupJob = mock(BackupJob.class);
    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);

    final Pair<PlannedAction, List<AuditDescription>> backupActionForCluster =
        PlanResult.getBackupActionForCluster(
            cluster,
            null,
            null,
            backupJob,
            null,
            appSettings,
            null,
            plannedActionFactory,
            null,
            new Date(),
            mock(Logger.class),
            false);
    assertNotNull(backupActionForCluster);
    assertEquals(1, backupActionForCluster.getLeft().getMoves().length);
    verify(plannedActionFactory, times(1)).forSyncBackupConfig(clusterDescription);
  }

  @Test
  public void testGetBackupActionsForCluster_topologyUpdate() {
    final Cluster cluster = mock(Cluster.class);
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(clusterDescription.getCloudProviders()).thenReturn(Set.of(CloudProvider.AWS));
    when(clusterDescription.isDiskBackupEnabled()).thenReturn(true);
    when(cluster.getClusterDescription()).thenReturn(clusterDescription);

    final BackupJob backupJob = mock(BackupJob.class);
    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);

    final Pair<PlannedAction, List<AuditDescription>> backupActionForCluster =
        PlanResult.getBackupActionForCluster(
            cluster,
            null,
            null,
            backupJob,
            null,
            appSettings,
            null,
            plannedActionFactory,
            null,
            new Date(),
            mock(Logger.class),
            true);
    assertNotNull(backupActionForCluster);
    assertEquals(1, backupActionForCluster.getLeft().getMoves().length);
    verify(plannedActionFactory, times(1)).forExpandBackupConfig(clusterDescription);
  }

  @Test
  public void testGetBackupActionsForCluster_pitOutOfSync() {
    final Cluster cluster = mock(Cluster.class);
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(clusterDescription.getCloudProviders()).thenReturn(Set.of(CloudProvider.AWS));
    when(clusterDescription.isPaused()).thenReturn(true);
    when(clusterDescription.isBackupEnabled()).thenReturn(false);
    when(clusterDescription.isDeleteRequested()).thenReturn(false);
    when(clusterDescription.isPitEnabled()).thenReturn(true);
    when(cluster.getClusterDescription()).thenReturn(clusterDescription);

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());
    final AppSettings appSettings = mock(AppSettings.class);

    when(clusterDescription.isPaused()).thenReturn(false);
    final BackupJob backupJob = mock(BackupJob.class);
    when(backupJob.isPitEnabled()).thenReturn(false);
    final BackupConfig backupConfigReplicaSet = mock(ReplicaSetBackupConfig.class);
    when(backupConfigReplicaSet.getState()).thenReturn(BackupConfigState.INACTIVE);
    when(backupJob.isPaused()).thenReturn(false);
    when(clusterDescription.isDiskBackupEnabled()).thenReturn(true);

    when(clusterDescription.isPaused()).thenReturn(true);
    when(backupJob.isPaused()).thenReturn(true);
    final Pair<PlannedAction, List<AuditDescription>> backupActionForCluster =
        PlanResult.getBackupActionForCluster(
            cluster,
            backupConfigReplicaSet,
            null,
            backupJob,
            null,
            appSettings,
            null,
            plannedActionFactory,
            null,
            new Date(),
            mock(Logger.class),
            false);
    assertNull(backupActionForCluster);
    verify(plannedActionFactory, times(0)).forSyncBackupConfig(clusterDescription);

    when(clusterDescription.isPaused()).thenReturn(false);
    when(backupJob.isPaused()).thenReturn(false);
    when(clusterDescription.isDiskBackupEnabled()).thenReturn(true);
    final Pair<PlannedAction, List<AuditDescription>> backupActionForCluster2 =
        PlanResult.getBackupActionForCluster(
            cluster,
            null,
            null,
            backupJob,
            null,
            appSettings,
            null,
            plannedActionFactory,
            null,
            new Date(),
            mock(Logger.class),
            false);
    assertNotNull(backupActionForCluster2);
    assertEquals(1, backupActionForCluster2.getLeft().getMoves().length);
    verify(plannedActionFactory, times(1)).forSyncBackupConfig(clusterDescription);
  }

  @Test
  public void testGetDiskBackupActions_encryptionAtRestEnabledAndInvalid() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn(true).when(clusterDescription).isEncryptionAtRestEnabled();

    final Group group = mock(Group.class);
    final NDSGroup ndsGroup = mock(NDSGroup.class);
    final NDSEncryptionAtRest encryptionAtRest = mock(NDSEncryptionAtRest.class);
    doReturn(false).when(encryptionAtRest).isValid(any());
    doReturn(encryptionAtRest).when(ndsGroup).getEncryptionAtRest();

    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));

    // encryption at east is not valid
    assertNull(
        PlanResult.getDiskBackupActionForCluster(
            clusterDescription,
            plannedActionFactory,
            null,
            group,
            ndsGroup,
            PlanningType.BLOCKING,
            mock(Logger.class)));
    verify(plannedActionFactory, never()).forDiskSnapshot(any());
  }

  @Test
  public void testGetRestoreActions_encryptionAtRestEnabledAndInvalid() {
    final Cluster cluster = mock(Cluster.class);
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn(true).when(clusterDescription).isEncryptionAtRestEnabled();

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    final NDSEncryptionAtRest encryptionAtRest = mock(NDSEncryptionAtRest.class);
    doReturn(false).when(encryptionAtRest).isValid(any());
    doReturn(encryptionAtRest).when(ndsGroup).getEncryptionAtRest();

    final BackupRestoreJobPlanUnit restoreTarget = mock(BackupRestoreJobPlanUnit.class);

    doReturn(clusterDescription).when(cluster).getClusterDescription();
    assertEquals(
        List.of(),
        PlanResult.getRestoreActions(
            ndsGroup,
            cluster,
            restoreTarget,
            mock(PlannedActionFactory.class),
            mock(CpsSnapshotEngine.class),
            null,
            mock(Logger.class)));
  }

  @Test
  public void testIsOplogEncryptionSettingOutOfSync() {
    testIsOplogEncryptionSettingOutOfSync_helper(EncryptionProviderType.AWS);
    testIsOplogEncryptionSettingOutOfSync_helper(EncryptionProviderType.GCP);
    testIsOplogEncryptionSettingOutOfSync_helper(EncryptionProviderType.AZURE);

    // tests where provider type is null
    final String pitSettingsCmk = "123242-4325-673-2345";

    final EncryptionProviders automationAgentConfigEncryptionProviders =
        getMockedEncryptionProviders(pitSettingsCmk);

    // cases where cmk is not enabled on the pitSettings nor should it be
    assertFalse(
        PlanResult.isOplogEncryptionSettingOutOfSync(
            null, automationAgentConfigEncryptionProviders, null, false));

    // cases where cmk is enabled on the pit settings but it should not be
    assertTrue(
        PlanResult.isOplogEncryptionSettingOutOfSync(
            pitSettingsCmk, automationAgentConfigEncryptionProviders, null, false));
  }

  private static void testIsOplogEncryptionSettingOutOfSync_helper(
      final EncryptionProviderType encryptionProviderType) {

    final String pitSettingsCmk = "123242-4325-673-2345";
    final String differentCmk = "4325342--2345234-5653-234";

    final EncryptionProviders automationAgentConfigEncryptionProviders =
        getMockedEncryptionProviders(pitSettingsCmk);

    // cases where cmk is not enabled on the pitSettings nor should it be
    assertFalse(
        PlanResult.isOplogEncryptionSettingOutOfSync(
            null, automationAgentConfigEncryptionProviders, encryptionProviderType, false));

    // case where cmk is not enabled on the pitSettings but it should be
    assertTrue(
        PlanResult.isOplogEncryptionSettingOutOfSync(
            null, automationAgentConfigEncryptionProviders, encryptionProviderType, true));

    // cases where cmk is enabled on the pit settings but it should not be
    assertTrue(
        PlanResult.isOplogEncryptionSettingOutOfSync(
            pitSettingsCmk,
            automationAgentConfigEncryptionProviders,
            encryptionProviderType,
            false));

    // cases oplog encryption should be enabled and pitSettings cmk is up to date
    assertFalse(
        PlanResult.isOplogEncryptionSettingOutOfSync(
            pitSettingsCmk,
            automationAgentConfigEncryptionProviders,
            encryptionProviderType,
            true));

    // case oplog encryption should be enabled but pitSettings cmk is out of date
    assertTrue(
        PlanResult.isOplogEncryptionSettingOutOfSync(
            differentCmk, automationAgentConfigEncryptionProviders, encryptionProviderType, true));
  }

  private static EncryptionProviders getMockedEncryptionProviders(final String pitSettingsCmk) {
    final EncryptionProviders automationAgentConfigEncryptionProviders =
        mock(EncryptionProviders.class);

    final AWSKey awsKey = mock(AWSKey.class);
    when(awsKey.getKeyId()).thenReturn(pitSettingsCmk);
    final AWSKMS awsKms = mock(AWSKMS.class);
    when(awsKms.getKeys()).thenReturn(List.of(awsKey));
    when(automationAgentConfigEncryptionProviders.getAWSKMS()).thenReturn(awsKms);

    final GoogleCloudKMS gcpKMS = mock(GoogleCloudKMS.class);
    final GoogleCloudKey gcpKey = mock(GoogleCloudKey.class);
    when(gcpKey.getKeyVersionResourceID()).thenReturn(pitSettingsCmk);
    when(gcpKMS.getKeys()).thenReturn(List.of(gcpKey));
    when(automationAgentConfigEncryptionProviders.getGoogleCloudKMS()).thenReturn(gcpKMS);

    final AzureKeyVault azureKeyVault = mock(AzureKeyVault.class);
    final AzureKey azureKey = mock(AzureKey.class);
    when(azureKeyVault.getKeys()).thenReturn(Collections.singletonList(azureKey));
    when(azureKey.getKeyIdentifier()).thenReturn(pitSettingsCmk);
    when(automationAgentConfigEncryptionProviders.getAzureKeyVault()).thenReturn(azureKeyVault);
    return automationAgentConfigEncryptionProviders;
  }

  @Test
  public void testTenantRestoreActions_encryptionAtRestEnabledAndInvalid() {
    final Cluster cluster = mock(Cluster.class);
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn(true).when(clusterDescription).isEncryptionAtRestEnabled();

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    final NDSEncryptionAtRest encryptionAtRest = mock(NDSEncryptionAtRest.class);
    doReturn(false).when(encryptionAtRest).isValid(any());
    doReturn(encryptionAtRest).when(ndsGroup).getEncryptionAtRest();

    final BackupRestoreJobPlanUnit restoreTarget = mock(BackupRestoreJobPlanUnit.class);

    doReturn(clusterDescription).when(cluster).getClusterDescription();
    assertEquals(
        List.of(),
        PlanResult.getRestoreActions(
            ndsGroup,
            cluster,
            restoreTarget,
            mock(PlannedActionFactory.class),
            mock(CpsSnapshotEngine.class),
            null,
            mock(Logger.class)));
  }

  @Test
  public void testGetRestoreActions_serverlessInstance() {
    final Cluster cluster = mock(Cluster.class);
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);

    doReturn(clusterDescription).when(cluster).getClusterDescription();
    doReturn(false).when(clusterDescription).isEncryptionAtRestEnabled();
    doReturn(false).when(clusterDescription).isDeleteRequested();
    doReturn(ClusterDescription.State.IDLE).when(clusterDescription).getState();
    doReturn(false).when(clusterDescription).isPaused();
    doReturn(List.of(new ObjectId())).when(clusterDescription).getRestoreJobIds();
    doReturn(true).when(clusterDescription).isServerlessTenantCluster();
    doReturn("ServerlessInstance0").when(clusterDescription).getName();
    doReturn(Set.of(CloudProvider.SERVERLESS)).when(clusterDescription).getCloudProviders();

    final PlannedActionFactory plannedActionFactory = spy(PlannedActionFactory.class);
    final ServerlessMoveProvider serverlessMoveProvider = mock(ServerlessMoveProvider.class);
    doReturn(serverlessMoveProvider)
        .when(plannedActionFactory)
        .getMoveProvider(CloudProvider.SERVERLESS);
    doReturn(mock(DoServerlessToServerlessStreamingRestoreMove.class))
        .when(serverlessMoveProvider)
        .getDoServerlessToServerlessStreamingRestoreMove(any());
    doReturn(mock(DoTenantUpgradeToServerlessStreamingRestoreMove.class))
        .when(serverlessMoveProvider)
        .getDoTenantUpgradeToServerlessStreamingRestoreMove(any());

    // Test 1: Restore target is null
    {
      final List<PlannedAction> restoreActions =
          PlanResult.getRestoreActions(
              mock(NDSGroup.class),
              cluster,
              null,
              plannedActionFactory,
              mock(CpsSnapshotEngine.class),
              null,
              mock(Logger.class));
      assertEquals(3, restoreActions.size());
      assertEquals(
          DoServerlessToServerlessStreamingRestoreMove.class.getName(),
          restoreActions.get(1).getMoves()[0].getClass().getName());
    }

    // Test 2: Restore target is non-null, strategy is not TENANT_UPGRADE_TO_SERVERLESS
    {
      final BackupRestoreJobPlanUnit restoreTarget = mock(BackupRestoreJobPlanUnit.class);
      doReturn(CpsRestoreMetadata.StrategyName.SERVERLESS_STREAMING)
          .when(restoreTarget)
          .getPlanStrategy();

      final List<PlannedAction> restoreActions =
          PlanResult.getRestoreActions(
              mock(NDSGroup.class),
              cluster,
              restoreTarget,
              plannedActionFactory,
              mock(CpsSnapshotEngine.class),
              null,
              mock(Logger.class));
      assertEquals(3, restoreActions.size());
      assertEquals(
          DoServerlessToServerlessStreamingRestoreMove.class.getName(),
          restoreActions.get(1).getMoves()[0].getClass().getName());
    }

    // Test 3: Restore target is non-null, strategy is TENANT_UPGRADE_TO_SERVERLESS
    {
      final BackupRestoreJobPlanUnit restoreTarget = mock(BackupRestoreJobPlanUnit.class);
      doReturn(CpsRestoreMetadata.StrategyName.TENANT_UPGRADE_TO_SERVERLESS)
          .when(restoreTarget)
          .getPlanStrategy();

      final List<PlannedAction> restoreActions =
          PlanResult.getRestoreActions(
              mock(NDSGroup.class),
              cluster,
              restoreTarget,
              plannedActionFactory,
              mock(CpsSnapshotEngine.class),
              null,
              mock(Logger.class));
      assertEquals(3, restoreActions.size());
      assertEquals(
          DoTenantUpgradeToServerlessStreamingRestoreMove.class.getName(),
          restoreActions.get(1).getMoves()[0].getClass().getName());
    }

    // Test 4: Flex Tenant Migration is in progress
    {
      final BackupRestoreJobPlanUnit restoreTarget = mock(BackupRestoreJobPlanUnit.class);
      doReturn(CpsRestoreMetadata.StrategyName.TENANT_UPGRADE_TO_SERVERLESS)
          .when(restoreTarget)
          .getPlanStrategy();

      final List<PlannedAction> restoreActions =
          PlanResult.getRestoreActions(
              mock(NDSGroup.class),
              cluster,
              restoreTarget,
              plannedActionFactory,
              mock(CpsSnapshotEngine.class),
              mock(FlexTenantMigration.class),
              mock(Logger.class));
      assertTrue(restoreActions.isEmpty());
    }
  }

  @Test
  public void testGetRestoreActions_whenSLSRestoreJob() {
    final Cluster cluster = mock(Cluster.class);
    final PlannedActionFactory plannedActionFactory = spy(PlannedActionFactory.class);
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);

    doReturn(clusterDescription).when(cluster).getClusterDescription();
    doReturn(false).when(clusterDescription).isEncryptionAtRestEnabled();
    doReturn(false).when(clusterDescription).isDeleteRequested();
    doReturn(State.IDLE).when(clusterDescription).getState();
    doReturn(List.of(new ObjectId())).when(clusterDescription).getRestoreJobIds();

    final BackupRestoreJobPlanUnit restoreTarget = mock(BackupRestoreJobPlanUnit.class);
    doReturn(CpsRestoreMetadata.StrategyName.SLS).when(restoreTarget).getPlanStrategy();

    final List<PlannedAction> restoreActions =
        PlanResult.getRestoreActions(
            mock(NDSGroup.class),
            cluster,
            restoreTarget,
            plannedActionFactory,
            mock(CpsSnapshotEngine.class),
            null,
            mock(Logger.class));

    assertEquals(0, restoreActions.size());
  }

  @Test
  public void testGetRestoreJobActions() {
    final InstanceHardware instanceHardware = mock(InstanceHardware.class);
    when(instanceHardware.getCloudProvider()).thenReturn(CloudProvider.AWS);
    when(instanceHardware.isProvisioned()).thenReturn(false);

    final StreamingReplicaSetRestoreJob.Builder jobBuilder =
        StreamingReplicaSetRestoreJob.Builder.aStreamingJob()
            .parentBuilder(
                VMBasedReplSetRestoreJob.Builder.aVMBasedJob()
                    .withParentBuilder(
                        ReplicaSetBackupRestoreJob.Builder.aReplicaSetBackupRestoreJob()
                            .withMetadataBuilder(CpsRestoreMetadata.Builder.aCpsRestoreMetadata()))
                    .withInstanceHardware(instanceHardware));
    final VMBasedReplSetRestoreJob job = jobBuilder.build();

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final Move provisionRestoreMachineMove = mock(Move.class);
    final Move doSnapshotRestoreMove = mock(Move.class);
    final Move destroyRestoreMove = mock(Move.class);

    when(moveProvider.getProvisionRestoreMachineMove(any()))
        .thenReturn(provisionRestoreMachineMove);
    when(moveProvider.getDoSnapshotRestoreMove(any())).thenReturn(doSnapshotRestoreMove);
    when(moveProvider.getDestroyRestoreMachineMove(any(), any())).thenReturn(destroyRestoreMove);

    final PlannedActionFactory actionFactory =
        spy(new PlannedActionFactory(mock(NDSPlanContext.class)));
    doReturn(moveProvider).when(actionFactory).getMoveProvider(any());

    final boolean isCpsSnapshotConsistentExportEnabledFeatureFlagOn = false;
    final List<PlannedAction> restoreJobActions =
        getPlanActionsForRestorePlanning(
            job, actionFactory, isCpsSnapshotConsistentExportEnabledFeatureFlagOn, false);

    // 2 actions, create machine and wait
    assertEquals(2, restoreJobActions.size());
    verify(actionFactory).forCreateRestoreMachine(job);
    verify(actionFactory).forDoSnapshotRestore(job.getMetadata(), CloudProvider.AWS);

    // Create machine should not run again
    reset(actionFactory);
    doReturn(moveProvider).when(actionFactory).getMoveProvider(any());
    when(instanceHardware.isProvisioned()).thenReturn(true);

    final List<PlannedAction> onlyWaitAction =
        getPlanActionsForRestorePlanning(
            job, actionFactory, isCpsSnapshotConsistentExportEnabledFeatureFlagOn, false);
    assertEquals(1, onlyWaitAction.size());
    verify(actionFactory, never()).forCreateRestoreMachine(job);
    verify(actionFactory).forDoSnapshotRestore(job.getMetadata(), CloudProvider.AWS);

    reset(actionFactory);

    jobBuilder
        .getParentBuilder()
        .getParentBuilder()
        .getMetadataBuilder()
        .withFinishedDate(new Date());
    final VMBasedReplSetRestoreJob job1 = jobBuilder.build();

    // Should contain a single destroy action
    doReturn(moveProvider).when(actionFactory).getMoveProvider(any());
    final List<PlannedAction> destroyAction =
        getPlanActionsForRestorePlanning(
            job1, actionFactory, isCpsSnapshotConsistentExportEnabledFeatureFlagOn, false);

    assertEquals(1, destroyAction.size());
    verify(actionFactory, never()).forCreateRestoreMachine(job1);
    verify(actionFactory, never()).forDoSnapshotRestore(job1.getMetadata(), CloudProvider.AWS);
    verify(actionFactory).forDestroyRestoreMachine(job1);
  }

  @Test
  public void testGetRestoreJobActions_whenSLSRestoreJob() {
    final PlannedActionFactory actionFactory =
        spy(new PlannedActionFactory(mock(NDSPlanContext.class)));
    final MoveProvider moveProvider = mock(MoveProvider.class);
    final RestoreTargetCluster restoreTargetCluster = mock(RestoreTargetCluster.class);
    doReturn("").when(restoreTargetCluster).getTargetClusterName();
    doReturn(moveProvider).when(actionFactory).getMoveProvider(any());

    final SLSRestoreJob job =
        new SLSRestoreJob(
            SLSRestoreJob.Builder.anSLSRestoreJobBuilder()
                .withHosts(List.of(new SLSRestoreJob.Host("h", new ObjectId(), 0)))
                .withCloudProvider(CloudProvider.AWS)
                .withParentBuilder(
                    ReplicaSetBackupRestoreJob.Builder.aReplicaSetBackupRestoreJob()
                        .withMetadataBuilder(
                            CpsRestoreMetadata.Builder.aCpsRestoreMetadata()
                                .withTarget(restoreTargetCluster))));

    final List<PlannedAction> restoreAction =
        getPlanActionsForRestorePlanning(job, actionFactory, true, false);

    assertEquals(1, restoreAction.size());
    assertEquals(MoveType.SNAPSHOT_RESTORE, restoreAction.get(0).getMoveType());
  }

  @Test
  public void testGetRestoreJobsByProvider() {
    // add self-restores for Azure, AWS, and GCP and one cross project Azure restore
    final NDSGroup ndsGroup = mock(NDSGroup.class);
    final BackupRestoreJobPlanUnit restoreSourceAzure = mock(BackupRestoreJobPlanUnit.class);
    final BackupRestoreJobPlanUnit restoreSourceAWS = mock(BackupRestoreJobPlanUnit.class);
    final BackupRestoreJobPlanUnit restoreSourceGCP = mock(BackupRestoreJobPlanUnit.class);
    final BackupRestoreJobPlanUnit restoreTargetAzure = mock(BackupRestoreJobPlanUnit.class);
    final List<BackupRestoreJobPlanUnit> restores = new ArrayList<>();
    restores.add(restoreSourceAzure);
    restores.add(restoreSourceAWS);
    restores.add(restoreSourceGCP);
    restores.add(restoreTargetAzure);
    final boolean isGCP = false;
    final boolean isAws = false;
    final boolean isAzure = true;
    final ObjectId sourceId = new ObjectId();
    final ObjectId targetId = new ObjectId();

    when(ndsGroup.getGroupId()).thenReturn(targetId);
    when(restoreTargetAzure.getSourceProjectId()).thenReturn(sourceId);
    when(restoreSourceAzure.getSourceProjectId()).thenReturn(targetId);
    when(restoreSourceAWS.getSourceProjectId()).thenReturn(targetId);
    when(restoreSourceGCP.getSourceProjectId()).thenReturn(targetId);
    doReturn(CpsRestoreMetadata.StrategyName.DIRECT_ATTACH)
        .when(restoreTargetAzure)
        .getPlanStrategy();
    doReturn(CloudProvider.AZURE).when(restoreSourceAzure).getCloudProvider();
    doReturn(CloudProvider.AWS).when(restoreSourceAWS).getCloudProvider();
    doReturn(CloudProvider.GCP).when(restoreSourceGCP).getCloudProvider();
    doReturn(CloudProvider.AZURE).when(restoreTargetAzure).getCloudProvider();

    final Map<CloudProvider, List<BackupRestoreJobPlanUnit>> sourceRestoreJobsByProvider =
        PlanResult.getRestoreJobsByProvider(restores, ndsGroup, isGCP, isAzure, isAws);

    assertEquals(sourceRestoreJobsByProvider.size(), 3);
    assertEquals(sourceRestoreJobsByProvider.get(CloudProvider.AZURE).size(), 2);
    assertEquals(sourceRestoreJobsByProvider.get(CloudProvider.AWS).size(), 1);
    assertEquals(sourceRestoreJobsByProvider.get(CloudProvider.GCP).size(), 1);
  }

  @Test
  public void testGetContainerActions_ServerlessUpgradeToDedicated() {
    final NDSGroup group = mock(NDSGroup.class);
    doReturn(new ObjectId()).when(group).getGroupId();

    final ServerlessCloudProviderContainer container = mock(ServerlessCloudProviderContainer.class);
    final NDSNetworkPermissionList networkPermissionList = mock(NDSNetworkPermissionList.class);
    final String serverlessInstanceName = "serverlessInstance0";
    when(container.getTenantClusterName()).thenReturn(serverlessInstanceName);
    when(container.getCloudProvider()).thenReturn(CloudProvider.SERVERLESS);
    when(container.isProvisioned()).thenReturn(true);
    when(group.getNetworkPermissionList()).thenReturn(networkPermissionList);

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());
    final AppSettings appSettings = mock(AppSettings.class);

    final ServerlessUpgradeToDedicatedStatus serverlessUpgradeToDedicatedStatus =
        new ServerlessUpgradeToDedicatedStatus(
            serverlessInstanceName,
            new ObjectId(),
            "mtm",
            new ObjectId(),
            new ObjectId(),
            new Date(),
            AWSRegionName.US_EAST_1,
            null,
            null);

    // Test in progress serverless upgrade to dedicated prevents deletion
    final List<PlannedAction> upgradePresentActions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            List.of(serverlessUpgradeToDedicatedStatus),
            List.of(),
            LOGGER);
    assertEquals(0, upgradePresentActions.size());
    verify(moveProvider, never()).getDestroyContainerMove(any());

    // Test no in progress serverless upgrade to dedicated allows deletion
    reset(moveProvider);
    final List<PlannedAction> noUpgradeActions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            LOGGER);
    assertEquals(1, noUpgradeActions.size());
    assertEquals(1, noUpgradeActions.get(0).getMoves().length);
    verify(moveProvider, times(1)).getDestroyContainerMove(any());
  }

  private void verifySnapshotsPreventContainerDeletion(
      final NDSGroup pGroup,
      final CloudProviderContainer pContainer,
      final RegionName pContainerRegion,
      final RegionName pOtherRegion,
      final Logger pLogger) {
    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);

    final List<PlannedAction> noSnapshots =
        PlanResult.getContainerActions(
            appSettings,
            pGroup,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            pContainer,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            pLogger);
    assertEquals(1, noSnapshots.size());
    assertEquals(1, noSnapshots.get(0).getMoves().length);
    verify(moveProvider, times(1)).getDestroyContainerMove(any());

    reset(moveProvider);
    final List<PlannedAction> withSnapshot =
        PlanResult.getContainerActions(
            appSettings,
            pGroup,
            mock(Group.class),
            Collections.emptyList(),
            Collections.singleton(pContainerRegion),
            plannedActionFactory,
            pContainer,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            pLogger);
    assertEquals(0, withSnapshot.size());
    verify(moveProvider, never()).getDestroyContainerMove(any());
    reset(moveProvider);

    final List<PlannedAction> snapshotDifferentRegion =
        PlanResult.getContainerActions(
            appSettings,
            pGroup,
            mock(Group.class),
            Collections.emptyList(),
            Collections.singleton(pOtherRegion),
            plannedActionFactory,
            pContainer,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            pLogger);
    assertEquals(1, snapshotDifferentRegion.size());
    assertEquals(1, snapshotDifferentRegion.get(0).getMoves().length);
    verify(moveProvider, times(1)).getDestroyContainerMove(any());
  }

  @Test
  public void testGetContainerActions_withSnapshots_Azure() {
    final AzureRegionName snapshotRegion = AzureRegionName.US_EAST_2;
    final NDSGroup group = mock(NDSGroup.class);
    doReturn(new ObjectId()).when(group).getGroupId();

    final AzureCloudProviderContainer container = mock(AzureCloudProviderContainer.class);
    final NDSNetworkPermissionList networkPermissionList = mock(NDSNetworkPermissionList.class);
    when(container.getCloudProvider()).thenReturn(CloudProvider.AZURE);
    when(container.getRegion()).thenReturn(snapshotRegion);
    when(container.isProvisioned()).thenReturn(true);
    when(group.getNetworkPermissionList()).thenReturn(networkPermissionList);

    verifySnapshotsPreventContainerDeletion(
        group, container, snapshotRegion, AzureRegionName.EUROPE_WEST, mock(Logger.class));
  }

  @Test
  public void testGetContainerActions_withSnapshots_AWS() {
    final AWSRegionName snapshotRegion = AWSRegionName.US_EAST_1;
    final NDSGroup group = mock(NDSGroup.class);
    doReturn(new ObjectId()).when(group).getGroupId();

    final AWSCloudProviderContainer container = mock(AWSCloudProviderContainer.class);
    final NDSNetworkPermissionList networkPermissionList = mock(NDSNetworkPermissionList.class);
    when(container.getCloudProvider()).thenReturn(CloudProvider.AWS);
    when(container.getRegion()).thenReturn(snapshotRegion);
    when(container.isProvisioned()).thenReturn(true);
    when(group.getNetworkPermissionList()).thenReturn(networkPermissionList);

    verifySnapshotsPreventContainerDeletion(
        group, container, snapshotRegion, AWSRegionName.US_WEST_1, mock(Logger.class));
  }

  @Test
  public void testGetContainerActions_withSnapshots_GCP() {
    final GCPRegionName snapshotRegion = GCPRegionName.CENTRAL_US;
    final NDSGroup group = mock(NDSGroup.class);
    doReturn(new ObjectId()).when(group).getGroupId();

    final GCPCloudProviderContainer container = mock(GCPCloudProviderContainer.class);
    final NDSNetworkPermissionList networkPermissionList = mock(NDSNetworkPermissionList.class);
    when(container.getCloudProvider()).thenReturn(CloudProvider.GCP);
    when(container.isProvisioned()).thenReturn(true);
    when(group.getNetworkPermissionList()).thenReturn(networkPermissionList);

    final RegionName differentProviderRegion = AWSRegionName.US_WEST_1;

    verifySnapshotsPreventContainerDeletion(
        group, container, snapshotRegion, differentProviderRegion, mock(Logger.class));
  }

  @Test
  public void testGetContainerActions_copySnapshotsRequiresContainer() {
    final NDSGroup group = mock(NDSGroup.class);
    doReturn(new ObjectId()).when(group).getGroupId();

    final AWSCloudProviderContainer container = mock(AWSCloudProviderContainer.class);
    doReturn(CloudProvider.AWS).when(container).getCloudProvider();
    doReturn(AWSRegionName.US_EAST_1).when(container).getRegion();
    doReturn(true).when(container).isResponsibleForThisRegion(eq(AWSRegionName.US_EAST_1));
    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());
    final AppSettings appSettings = mock(AppSettings.class);

    // no backup jobs
    List<PlannedAction> actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, actions.size());

    // backup jobs that does not have copy settings
    final BackupJob backupJob = mock(BackupJob.class);
    doReturn(null).when(backupJob).getCopySettings();
    actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            List.of(backupJob),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, actions.size());

    // when copy settings is an empty list
    doReturn(Collections.emptyList()).when(backupJob).getCopySettings();
    actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            List.of(backupJob),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, actions.size());

    // when copy settings is an empty list
    doReturn(Collections.emptyList()).when(backupJob).getCopySettings();
    actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            List.of(backupJob),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, actions.size());

    // when there are copy settings but none of the regions match with the container region

    final CopySetting cp1 = mock(CopySetting.class);
    final CopySetting cp2 = mock(CopySetting.class);
    doReturn(AWSRegionName.AP_NORTHEAST_3).when(cp1).getRegionName();
    doReturn(AWSRegionName.US_EAST_2).when(cp2).getRegionName();

    doReturn(List.of(cp1, cp2)).when(backupJob).getCopySettings();
    actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            List.of(backupJob),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, actions.size());

    // more than one backup job but no copy settings match the cloud provider container
    final CopySetting cp3 = mock(CopySetting.class);
    final CopySetting cp4 = mock(CopySetting.class);
    doReturn(AWSRegionName.AP_EAST_1).when(cp3).getRegionName();
    doReturn(AWSRegionName.AF_SOUTH_1).when(cp4).getRegionName();

    final BackupJob backupJob2 = mock(BackupJob.class);
    doReturn(List.of(cp3, cp4)).when(backupJob2).getCopySettings();
    actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            List.of(backupJob, backupJob2),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, actions.size());

    // one copy settings matches with the container region.
    doReturn(AWSRegionName.US_EAST_1).when(cp1).getRegionName();
    actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            List.of(backupJob, backupJob2),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(1, actions.size());
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any());
  }

  @Test
  public void
      testGetContainerActions_CombineFreeTierContainerAndClusterProvision_SkipsContainerCreation() {
    // Create a comprehensive test that verifies the container creation skipping behavior
    final NDSGroup ndsGroup = mock(NDSGroup.class);
    final Group group = mock(Group.class);
    final ObjectId groupId = new ObjectId();
    final ObjectId containerId = new ObjectId();
    final ObjectId accountId = new ObjectId();

    // Mock NDSGroup methods
    doReturn(groupId).when(ndsGroup).getGroupId();
    doReturn(false).when(ndsGroup).getContainsCopiedContainers();
    doReturn(false).when(ndsGroup).isServerlessMTMHolder();

    // Mock network permission list
    final NDSNetworkPermissionList networkPermissionList = mock(NDSNetworkPermissionList.class);
    doReturn(networkPermissionList).when(ndsGroup).getNetworkPermissionList();
    doReturn(null).when(networkPermissionList).getLastUpdated();

    // Create a FREE container that is not provisioned
    final CloudProviderContainer container = mock(CloudProviderContainer.class);
    doReturn(containerId).when(container).getId();
    doReturn(CloudProvider.FREE).when(container).getCloudProvider();
    doReturn(false).when(container).isProvisioned();
    doReturn(Collections.emptyList()).when(container).getContainerPeers();
    doReturn(Collections.emptyList()).when(container).getEndpointServices();
    doReturn(Collections.emptyList()).when(container).getTenantEndpointServices();
    doReturn(Optional.empty()).when(container).getNetworkPermissionListLastUpdated();
    doReturn(false).when(container).isResponsibleForThisRegion(any());
    doReturn(accountId).when(container).getCloudProviderAccountId();

    // Create a cluster that makes the container necessary
    final Cluster cluster = mock(Cluster.class);
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn(clusterDescription).when(cluster).getClusterDescription();
    doReturn(State.WORKING).when(clusterDescription).getState();
    doReturn(false).when(clusterDescription).isAutomaticallyPaused();
    doReturn(false).when(clusterDescription).isTenantCluster();
    doReturn(false).when(clusterDescription).isDeleteRequested();
    doReturn(Collections.emptyList()).when(cluster).getReplicaSets();
    doReturn(groupId).when(clusterDescription).getGroupId();
    doReturn("test-cluster").when(clusterDescription).getName();

    // Set up replication specs so the cluster uses the container
    final ReplicationSpec replicationSpec = mock(ReplicationSpec.class);
    final RegionConfig regionConfig = mock(RegionConfig.class);
    final RegionName regionName = mock(RegionName.class);
    doReturn(regionName).when(regionConfig).getRegionName();
    doReturn(CloudProvider.FREE).when(regionName).getProvider();
    doReturn(List.of(regionConfig)).when(replicationSpec).getRegionConfigs();
    doReturn(List.of(replicationSpec)).when(clusterDescription).getReplicationSpecsWithShardData();

    // Mock the cluster to use the FREE container
    doReturn(Optional.of(container))
        .when(ndsGroup)
        .getCloudProviderContainer(eq(CloudProvider.FREE), eq(regionName), eq("test-cluster"));

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());
    doReturn(false).when(moveProvider).isPeeringSupported();
    doReturn(false).when(moveProvider).isPrivateEndpointSupported();
    doReturn(false).when(moveProvider).isIpWhitelistSupported();

    // Mock the provision container move to return a valid move
    final Move provisionMove = mock(Move.class);
    doReturn(provisionMove)
        .when(moveProvider)
        .getProvisionContainerMove(anyMap(), any(ObjectId.class), anyList());

    final AppSettings appSettings = mock(AppSettings.class);
    doReturn(Optional.empty()).when(appSettings).getCapacityReservationCronInternalProjectId();
    final Logger logger = mock(Logger.class);

    // Test case 1: combineFreeTierContainerAndClusterProvision is enabled (true) - should skip
    // container creation
    doReturn(true)
        .when(appSettings)
        .getBoolProp("nds.combineFreeTierContainerAndClusterProvision.enabled", false);

    List<PlannedAction> actions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            group,
            List.of(cluster), // Add cluster to make container necessary
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false, // pHasStreamsPrivateNetworking = false
            Collections.emptyList(),
            List.of(),
            logger);

    // Should not create any container provision actions when combine setting is enabled
    assertEquals(0, actions.size());
    verify(moveProvider, never()).getProvisionContainerMove(any(), any(), any());

    // Test case 2: combineFreeTierContainerAndClusterProvision is disabled (false) - should create
    // container
    reset(moveProvider);
    doReturn(provisionMove)
        .when(moveProvider)
        .getProvisionContainerMove(anyMap(), any(ObjectId.class), anyList());
    doReturn(false)
        .when(appSettings)
        .getBoolProp("nds.combineFreeTierContainerAndClusterProvision.enabled", false);

    actions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            group,
            List.of(cluster), // Add cluster to make container necessary
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false, // pHasStreamsPrivateNetworking = false
            Collections.emptyList(),
            List.of(),
            logger);

    // Should create container provision action when combine setting is disabled
    assertEquals(1, actions.size());
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any());

    // Test case 3: Non-FREE container should not be affected by the setting
    reset(moveProvider);
    doReturn(provisionMove)
        .when(moveProvider)
        .getProvisionContainerMove(anyMap(), any(ObjectId.class), anyList());

    // Create a separate AWS container for this test case
    final AWSCloudProviderContainer awsContainer = mock(AWSCloudProviderContainer.class);
    doReturn(new ObjectId()).when(awsContainer).getId();
    doReturn(CloudProvider.AWS).when(awsContainer).getCloudProvider();
    doReturn(false).when(awsContainer).isProvisioned();
    doReturn(Collections.emptyList()).when(awsContainer).getContainerPeers();
    doReturn(Collections.emptyList()).when(awsContainer).getEndpointServices();
    doReturn(Collections.emptyList()).when(awsContainer).getTenantEndpointServices();
    doReturn(Optional.empty()).when(awsContainer).getNetworkPermissionListLastUpdated();
    doReturn(false).when(awsContainer).isResponsibleForThisRegion(any());
    doReturn(new ObjectId()).when(awsContainer).getCloudProviderAccountId();

    // Mock AWS-specific methods
    doReturn(Optional.of("vpc-123")).when(awsContainer).getVpcId();
    doReturn(new AWSSubnet[0]).when(awsContainer).getSubnets();
    doReturn(AWSRegionName.US_EAST_1).when(awsContainer).getRegion();
    doReturn(null).when(awsContainer).getNeedsSubnetUpdateAfter();

    // Update cluster and region to use AWS
    doReturn(Optional.of(CloudProvider.AWS)).when(clusterDescription).getOnlyCloudProvider();
    doReturn(CloudProvider.AWS).when(regionName).getProvider();
    doReturn(Optional.of(awsContainer))
        .when(ndsGroup)
        .getCloudProviderContainer(eq(CloudProvider.AWS), eq(regionName), eq("test-cluster"));

    doReturn(true)
        .when(appSettings)
        .getBoolProp("nds.combineFreeTierContainerAndClusterProvision.enabled", false);

    actions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            group,
            List.of(cluster), // Add cluster to make container necessary
            Collections.emptySet(),
            plannedActionFactory,
            awsContainer, // Use AWS container
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false, // pHasStreamsPrivateNetworking = false
            Collections.emptyList(),
            List.of(),
            logger);

    // AWS container should still be created even when combine setting is enabled
    assertEquals(1, actions.size());
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any());
  }

  @Test
  public void testGetContainerActions_projectWithCopiedContainers() {
    final NDSGroup ndsGroup = mock(NDSGroup.class);
    final Group group = mock(Group.class);
    doReturn(true).when(ndsGroup).getContainsCopiedContainers();
    final AppSettings appSettings = mock(AppSettings.class);
    final AWSCloudProviderContainer container = mock(AWSCloudProviderContainer.class);

    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));

    final CopySetting cp1 = mock(CopySetting.class);
    final BackupJob backupJob = mock(BackupJob.class);

    // system project.
    final List<PlannedAction> actions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            group,
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            List.of(backupJob),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, actions.size());

    verifyNoInteractions(cp1);
    verifyNoInteractions(backupJob);
  }

  @Test
  public void testGetContainerActions_NoClustersNeedContainer() {
    final ClusterDescription tenantClusterDesc = mock(ClusterDescription.class);
    doReturn("m0").when(tenantClusterDesc).getName();

    final Cluster tenantCluster = mock(Cluster.class);
    doReturn(tenantClusterDesc).when(tenantCluster).getClusterDescription();
    doReturn(Collections.emptyList()).when(tenantCluster).getReplicaSets();

    final ReplicationSpec replicationSpec = mock(ReplicationSpec.class);
    final RegionConfig regionConfig = mock(RegionConfig.class);
    doReturn(List.of(regionConfig)).when(replicationSpec).getRegionConfigs();
    doReturn(AWSRegionName.US_EAST_1).when(regionConfig).getRegionName();
    doReturn(List.of(replicationSpec)).when(tenantClusterDesc).getReplicationSpecsWithShardData();

    final NDSGroup group = mock(NDSGroup.class);
    doReturn(new ObjectId()).when(group).getGroupId();

    final FreeCloudProviderContainer container = mock(FreeCloudProviderContainer.class);
    doReturn(new ObjectId()).when(container).getId();
    doReturn(Optional.of(container)).when(group).getCloudProviderContainer(any(), any(), any());
    doReturn(Collections.emptyList()).when(container).getContainerPeers();
    doReturn(CloudProvider.FREE).when(container).getCloudProvider();
    doReturn(true).when(container).isProvisioned();
    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);
    final Logger logger = mock(Logger.class);

    // If no clusters present - delete container
    final List<PlannedAction> noClusters =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            logger);
    assertEquals(1, noClusters.size());
    assertEquals(1, noClusters.get(0).getMoves().length);
    verify(moveProvider, times(1)).getDestroyContainerMove(any());
    reset(moveProvider);

    // if all clusters automatically paused - delete container
    doReturn(State.DELETED).when(tenantClusterDesc).getState();
    final List<PlannedAction> oneClusterAutomaticallyPaused =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            List.of(tenantCluster),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            logger);
    assertEquals(1, oneClusterAutomaticallyPaused.size());
    assertEquals(1, oneClusterAutomaticallyPaused.get(0).getMoves().length);
    verify(moveProvider, times(1)).getDestroyContainerMove(any());
  }

  @Test
  public void testGetContainerActions_ActiveDedicatedSearchNeedsContainer() {
    final NDSGroup group = mock(NDSGroup.class);
    doReturn(new ObjectId()).when(group).getGroupId();

    final NDSNetworkPermissionList networkPermissionList = mock(NDSNetworkPermissionList.class);
    doReturn(networkPermissionList).when(group).getNetworkPermissionList();
    doReturn(new Date(0)).when(networkPermissionList).getLastUpdated();

    final ObjectId containerId = new ObjectId();
    final FreeCloudProviderContainer container = mock(FreeCloudProviderContainer.class);
    doReturn(containerId).when(container).getId();
    doReturn(Collections.emptyList()).when(container).getContainerPeers();
    doReturn(CloudProvider.FREE).when(container).getCloudProvider();
    doReturn(true).when(container).isProvisioned();

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);
    final Logger logger = mock(Logger.class);

    final List<PlannedAction> noClusters =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Set.of(containerId),
            false,
            Collections.emptyList(),
            List.of(),
            logger);
    assertTrue(noClusters.isEmpty());
    verify(moveProvider, never()).getDestroyContainerMove(any());
    reset(moveProvider);
  }

  @Test
  public void testGetContainerActions_StreamsPrivateLinkEnsureKafkaNetworkPermissions() {
    final NDSGroup group = mock(NDSGroup.class);
    doReturn(new ObjectId()).when(group).getGroupId();

    final NDSNetworkPermissionList networkPermissionList = mock(NDSNetworkPermissionList.class);
    doReturn(networkPermissionList).when(group).getNetworkPermissionList();
    doReturn(new Date()).when(networkPermissionList).getLastUpdated();

    final ObjectId containerId = new ObjectId();
    final AWSCloudProviderContainer container = mock(AWSCloudProviderContainer.class);
    doReturn(containerId).when(container).getId();
    doReturn(Collections.emptyList()).when(container).getContainerPeers();
    doReturn(CloudProvider.AWS).when(container).getCloudProvider();
    doReturn(AWSRegionName.US_EAST_1).when(container).getRegion();
    doReturn(true).when(container).isProvisioned();

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);
    final Logger logger = mock(Logger.class);

    // Turn on isIpWhitelistSupported to test ensureNetworkPermissions code path.
    doReturn(true).when(moveProvider).isIpWhitelistSupported();

    List<PlannedAction> actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            true,
            Collections.emptyList(),
            List.of(),
            logger);
    assertEquals(2, actions.size());
    verify(moveProvider, times(1)).getEnsureKafkaNetworkPermissionsAppliedMove(any());
    verify(moveProvider, times(1)).getEnsureIpWhitelistAppliedMove(any(), any());

    doReturn(false).when(container).isProvisioned();

    actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            true,
            Collections.emptyList(),
            List.of(),
            logger);

    // Should return an additional ensureContainer action.
    assertEquals(3, actions.size());
  }

  @Test
  public void testGetContainerActions_StreamsPrivateLinkNeedsContainer() {
    final NDSGroup group = mock(NDSGroup.class);
    doReturn(new ObjectId()).when(group).getGroupId();

    final NDSNetworkPermissionList networkPermissionList = mock(NDSNetworkPermissionList.class);
    doReturn(networkPermissionList).when(group).getNetworkPermissionList();
    doReturn(new Date(0)).when(networkPermissionList).getLastUpdated();

    final ObjectId containerId = new ObjectId();
    final AWSCloudProviderContainer container = mock(AWSCloudProviderContainer.class);
    doReturn(containerId).when(container).getId();
    doReturn(Collections.emptyList()).when(container).getContainerPeers();
    doReturn(CloudProvider.AWS).when(container).getCloudProvider();
    doReturn(AWSRegionName.US_EAST_1).when(container).getRegion();
    doReturn(false).when(container).isProvisioned();

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);
    final Logger logger = mock(Logger.class);

    List<PlannedAction> actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            true,
            Collections.emptyList(),
            List.of(),
            logger);
    assertEquals(1, actions.size());
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any());

    doReturn(true).when(container).isProvisioned();
    actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            true,
            Collections.emptyList(),
            List.of(),
            logger);
    assertEquals(0, actions.size());

    // Make sure that action is not generated for tenant containers
    CloudProvider provider = mock(CloudProvider.class);
    doReturn(true).when(provider).isTenantProvider();
    doReturn(CloudProvider.AWS.toString()).when(provider).name();
    doReturn(provider).when(container).getCloudProvider();
    doReturn(false).when(container).isProvisioned();
    actions =
        PlanResult.getContainerActions(
            appSettings,
            group,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            true,
            Collections.emptyList(),
            List.of(),
            logger);
    assertEquals(0, actions.size());
  }

  private CpsSnapshotEngine getMockedCpsSnapshotEngine(final BackupSnapshotDao backupSnapshotDao) {
    final CpsPolicySvc cpsPolicySvc = mock(CpsPolicySvc.class);
    final CpsPitSvc cpsPitSvc = mock(CpsPitSvc.class);
    final CpsSvc cpsSvc = mock(CpsSvc.class);
    doReturn(true).when(cpsSvc).areConcurrentSnapshotsEnabled(any(Group.class));
    final Logger logger = mock(Logger.class);
    return new CpsSnapshotEngine.Builder()
        .backupSnapshotDao(backupSnapshotDao)
        .cpsPitSvc(cpsPitSvc)
        .cpsSvc(cpsSvc)
        .ndsBackupPolicySvc(cpsPolicySvc)
        .logger(logger)
        .build();
  }

  @Test
  public void testGetContainerActions_ServerlessDeployment_unprovisionedContainer() {
    final NDSGroup ndsGroup =
        NDSModelTestFactory.getServerlessMTMHolderNdsGroup(CloudProvider.AWS, false);
    final NDSGroup nonMTMGroup = NDSModelTestFactory.getServerlessMockedGroup();
    final CloudProviderContainer cloudProviderContainer =
        ndsGroup.getCloudProviderContainers().get(0);

    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        ServerlessDeploymentModelTestFactory.getServerlessLoadBalancingDeployment(
            CloudProvider.AWS);

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);

    final Logger logger = mock(Logger.class);

    final List<PlannedAction> noServerlessDeployment =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            logger);
    assertTrue(noServerlessDeployment.isEmpty());
    verify(moveProvider, times(0)).getProvisionContainerMove(any(), any(), any());

    // a non mtm group shouldn't trigger provisioning container moves when isContainerNecessary is
    // false
    final List<PlannedAction> tenantGroupActions =
        PlanResult.getContainerActions(
            appSettings,
            nonMTMGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(serverlessLoadBalancingDeployment),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            logger);
    assertTrue(tenantGroupActions.isEmpty());
    verify(moveProvider, times(0)).getProvisionContainerMove(any(), any(), any());

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(serverlessLoadBalancingDeployment),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            logger);
    assertEquals(2, containerActions.size());
    assertEquals(1, containerActions.get(0).getMoves().length);
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any());
    assertEquals(1, containerActions.get(1).getMoves().length);
    verify(moveProvider, times(1)).getEnsureServerlessNetworkPermissionsAppliedMove(any());
  }

  @Test
  public void testGetContainerActions_ServerlessDeployment_provisionedContainer() {
    final NDSGroup ndsGroup =
        NDSModelTestFactory.getServerlessMTMHolderNdsGroup(CloudProvider.AWS, true);
    final CloudProviderContainer cloudProviderContainer =
        ndsGroup.getCloudProviderContainers().get(0);

    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        ServerlessDeploymentModelTestFactory.getServerlessLoadBalancingDeployment(
            CloudProvider.AWS);

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(serverlessLoadBalancingDeployment),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertTrue(containerActions.isEmpty());
    verify(moveProvider, never()).getProvisionContainerMove(any(), any(), any());
  }

  @Test
  public void testGetContainerActions_HeldAWSCapacityReservations_provisionsAWSContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AWS);
    final CloudProviderContainer cloudProviderContainer =
        ndsGroup.getCloudProviderContainers().get(0);

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);

    @SuppressWarnings("unchecked")
    final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> heldRequest =
        (CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult>)
            mock(CheckCapacityRequest.class);
    final AWSInstanceCapacitySpec awsInstanceCapacitySpec = mock(AWSInstanceCapacitySpec.class);
    doReturn(((AWSCloudProviderContainer) cloudProviderContainer).getAWSAccountId())
        .when(awsInstanceCapacitySpec)
        .getAccountId();
    doReturn(((RegionalDedicatedCloudProviderContainer) cloudProviderContainer).getRegion())
        .when(awsInstanceCapacitySpec)
        .getRegionName();
    doReturn(List.of(awsInstanceCapacitySpec)).when(heldRequest).getInstanceSpecs();

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            List.of(heldRequest),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(1, containerActions.size());
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any());
  }

  @Test
  public void testGetContainerActions_HeldAWSCapacityReservations_doesNotProvisionAzureContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AZURE);
    final CloudProviderContainer cloudProviderContainer =
        ndsGroup.getCloudProviderContainers().get(0);

    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));

    final AppSettings appSettings = mock(AppSettings.class);

    @SuppressWarnings("unchecked")
    final CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult> heldRequest =
        (CheckCapacityRequest<AWSInstanceCapacitySpec, AWSCheckResult>)
            mock(CheckCapacityRequest.class);

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            List.of(heldRequest),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, containerActions.size());
  }

  @Test
  public void
      testGetContainerActions_NonReleasedAzureCapacityReservations_provisionsAzureContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AZURE);
    final CloudProviderContainer cloudProviderContainer =
        ndsGroup.getCloudProviderContainers().get(0);

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);

    @SuppressWarnings("unchecked")
    final CheckCapacityRequest<AzureInstanceCapacitySpec, AzureCheckResult> req =
        (CheckCapacityRequest<AzureInstanceCapacitySpec, AzureCheckResult>)
            mock(CheckCapacityRequest.class);

    final AzureCloudProviderContainer azureContainer =
        (AzureCloudProviderContainer) cloudProviderContainer;

    final AzureInstanceCapacitySpec azureInstanceCapacitySpec =
        NDSModelTestFactory.getAzureInstanceCapacitySpec(
            azureContainer.getAzureSubscriptionId(), azureContainer.getRegion());

    doReturn(List.of(azureInstanceCapacitySpec)).when(req).getInstanceSpecs();

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            List.of(req),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(1, containerActions.size());
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any());

    assertEquals(1, containerActions.size());
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any());
  }

  @Test
  public void
      testGetContainerActions_NonReleasedAzureCapacityReservations_doesNotProvisionAWSContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AWS);
    final CloudProviderContainer cloudProviderContainer =
        ndsGroup.getCloudProviderContainers().get(0);

    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));

    final AppSettings appSettings = mock(AppSettings.class);

    @SuppressWarnings("unchecked")
    final CheckCapacityRequest<AzureInstanceCapacitySpec, AzureCheckResult> req =
        (CheckCapacityRequest<AzureInstanceCapacitySpec, AzureCheckResult>)
            mock(CheckCapacityRequest.class);

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            List.of(req),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, containerActions.size());
    assertEquals(0, containerActions.size());
  }

  @Test
  public void testGetTenantEndpointServiceDeploymentActions() {
    final CloudProviderContainer container = mock(CloudProviderContainer.class);

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final Logger logger = mock(Logger.class);

    // Test no tenant endpoint services present.
    {
      final List<PlannedAction> tenantEndpointServiceDeploymentActions =
          PlanResult.getTenantEndpointServiceDeploymentActions(
              container, plannedActionFactory, logger);
      assertTrue(tenantEndpointServiceDeploymentActions.isEmpty());
    }

    // Valid endpoint service to delete.
    final AWSTenantEndpointService endpointServiceToDelete =
        AWSTenantEndpointService.builder().deleteRequested(true).status(Status.INITIATING).build();

    // Valid endpoint service to create.
    final AWSTenantEndpointService endpointServiceToCreate =
        AWSTenantEndpointService.builder()
            .status(Status.INITIATING)
            .loadBalancingDeploymentId(new ObjectId())
            .build();

    // Invalid endpoint service to create.
    final AWSTenantEndpointService unlinkedEndpointService =
        AWSTenantEndpointService.builder().status(Status.INITIATING).build();

    final List<AWSTenantEndpointService> endpointServices =
        List.of(endpointServiceToDelete, endpointServiceToCreate, unlinkedEndpointService);
    doReturn(endpointServices).when(container).getTenantEndpointServices();

    // Test endpoint service to delete & endpoint service to create.
    {
      final List<PlannedAction> tenantEndpointServiceDeploymentActions =
          PlanResult.getTenantEndpointServiceDeploymentActions(
              container, plannedActionFactory, logger);
      assertEquals(1, tenantEndpointServiceDeploymentActions.size());
      verify(plannedActionFactory, times(1))
          .forTenantProducerSyncPrivateEndpointServices(
              container,
              Set.of(endpointServiceToCreate.getId()),
              Set.of(endpointServiceToDelete.getId()));
    }
  }

  @Test
  public void
      testGetContainerActions_ServerlessDeployment_ServerlessToFlexEnvoyBypassUpdateNeeded() {
    final NDSGroup ndsGroup =
        NDSModelTestFactory.getServerlessMTMHolderNdsGroup(CloudProvider.AWS, true);
    final CloudProviderContainer cloudProviderContainer =
        ndsGroup.getCloudProviderContainers().get(0);

    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        ServerlessDeploymentModelTestFactory.getServerlessLoadBalancingDeployment(
            CloudProvider.AWS);

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);
    final ServerlessMTMPool mtmPool =
        ServerlessTestFactory.getServerlessMTMPool(
                ndsGroup.getGroupId(), new ObjectId(), "poolName")
            .toBuilder()
            .envoyBypassRequestedDate(new Date())
            .build();

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(serverlessLoadBalancingDeployment),
            mtmPool,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));

    assertEquals(1, containerActions.size());
    verify(moveProvider, never()).getProvisionContainerMove(any(), any(), any());
    assertEquals(1, containerActions.get(0).getMoves().length);
    verify(moveProvider, times(1)).getEnsureServerlessNetworkPermissionsAppliedMove(any());
  }

  @Test
  public void
      testGetContainerActions_ServerlessDeployment_ServerlessToFlexEnvoyBypassRollbackUpdateNeeded() {
    final NDSGroup ndsGroup =
        NDSModelTestFactory.getServerlessMTMHolderNdsGroup(CloudProvider.AWS, true);
    final CloudProviderContainer cloudProviderContainer =
        ndsGroup.getCloudProviderContainers().get(0);

    final ServerlessLoadBalancingDeployment serverlessLoadBalancingDeployment =
        ServerlessDeploymentModelTestFactory.getServerlessLoadBalancingDeployment(
            CloudProvider.AWS);

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);
    final ServerlessMTMPool mtmPool =
        ServerlessTestFactory.getServerlessMTMPool(
                ndsGroup.getGroupId(), new ObjectId(), "poolName")
            .toBuilder()
            .envoyBypassRequestedDate(null)
            .envoyBypassRollbackRequestedDate(new Date())
            .build();

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(serverlessLoadBalancingDeployment),
            mtmPool,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));

    assertEquals(1, containerActions.size());
    verify(moveProvider, never()).getProvisionContainerMove(any(), any(), any());
    assertEquals(1, containerActions.get(0).getMoves().length);
    verify(moveProvider, times(1)).getEnsureServerlessNetworkPermissionsAppliedMove(any());
  }

  @Test
  public void testGetContainerActions_NoServerlessDeployment_provisionedContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AWS);

    final AWSCloudProviderContainer cloudProviderContainer =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer(AWSRegionName.US_EAST_1));

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(1, containerActions.size());
    assertEquals(1, containerActions.get(0).getMoves().length);
    verify(moveProvider, times(1)).getDestroyContainerMove(any());
  }

  @Test
  public void testGetContainerActions_UnprovisionedServerlessDeployment_provisionedContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AWS);
    doReturn(true).when(ndsGroup).isServerlessMTMHolder();

    final AWSCloudProviderContainer cloudProviderContainer =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer(AWSRegionName.US_EAST_1));

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final ServerlessLoadBalancingDeployment unprovsionedDeployment =
        ServerlessDeploymentModelTestFactory.getUnProvisionedAWSServerlessLoadBalancingDeployment(
            AWSRegionName.US_EAST_1);

    final AppSettings appSettings = mock(AppSettings.class);

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(unprovsionedDeployment),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(1, containerActions.size());
    assertEquals(1, containerActions.get(0).getMoves().length);
    verify(moveProvider, times(1)).getDestroyContainerMove(any());
  }

  @Test
  public void testGetContainerActions_NoServerlessDeployment_unprovisionedContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AWS);
    doReturn(true).when(ndsGroup).isServerlessMTMHolder();

    final AWSCloudProviderContainer cloudProviderContainer =
        new AWSCloudProviderContainer(
            NDSModelTestFactory.getUnprovisionedAWSContainer(AWSRegionName.US_EAST_1));

    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));

    final AppSettings appSettings = mock(AppSettings.class);

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertTrue(containerActions.isEmpty());
  }

  @Test
  public void testGetContainerActions_UpdateGCPSubnets() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.GCP);

    // this creates a container with all 32 subnets and a PSC region group in one of them.
    final GCPCloudProviderContainer cloudProviderContainer =
        new GCPCloudProviderContainer(
            NDSModelTestFactory.getGCPContainerWithPrivateServiceConnectRegionGroup());

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final AppSettings appSettings = mock(AppSettings.class);

    final List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            cloudProviderContainer,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(1, containerActions.size());
    assertEquals(1, containerActions.get(0).getMoves().length);
    verify(moveProvider, times(1)).getUpdateContainerSubnetsMove(any(), any(), any());
  }

  @Test
  public void testGetContainerActions_AWSKMSSyncInterfaceVPCEndpoint_provisionedContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AWS);
    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isFeatureFlagInEnabledState(
            FeatureFlag.ENCRYPTION_AT_REST_AWS_KMS_PRIVATE_ENDPOINT))
        .thenReturn(true);
    testGetContainerActions_EARPrivateEndpoint_provisionedContainer(ndsGroup, appSettings);
  }

  @Test
  public void testGetContainerActions_SyncAzureKeyVaultPrivateEndpoint_provisionedContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AZURE);
    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isFeatureFlagInEnabledState(
            FeatureFlag.ENCRYPTION_AT_REST_AZURE_KEY_VAULT_PRIVATE_ENDPOINT))
        .thenReturn(true);
    testGetContainerActions_EARPrivateEndpoint_provisionedContainer(ndsGroup, appSettings);
  }

  public void testGetContainerActions_EARPrivateEndpoint_provisionedContainer(
      final NDSGroup pNDSGroup, final AppSettings pAppSettings) {

    final AzureCloudProviderContainer container =
        new AzureCloudProviderContainer(NDSModelTestFactory.getAzureContainer());

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());
    doReturn(true).when(moveProvider).isPrivateEndpointSupported();

    // delete unused container if there is no private endpoints
    List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            pAppSettings,
            pNDSGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            container,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(1, containerActions.size());
    assertEquals(1, containerActions.get(0).getMoves().length);
    verify(moveProvider, times(1)).getDestroyContainerMove(eq(container.getId()));

    final NDSEncryptionAtRest encryptionAtRest = mock(NDSEncryptionAtRest.class);
    doReturn(encryptionAtRest).when(pNDSGroup).getEncryptionAtRest();
    final CloudProviderPrivateEndpoint endpoint = mock(CloudProviderPrivateEndpoint.class);
    doReturn(Optional.of(endpoint))
        .when(encryptionAtRest)
        .getPrivateEndpointForRegion(container.getRegion());

    // no action if no needs to update the private endpoint
    containerActions =
        PlanResult.getContainerActions(
            pAppSettings,
            pNDSGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            container,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, containerActions.size());

    // has needsUpdateAfter in the past
    Date needsUpdateAfter = Date.from(Instant.now().minus(5, ChronoUnit.SECONDS));
    doReturn(Optional.of(needsUpdateAfter)).when(endpoint).getNeedsUpdateAfter();
    containerActions =
        PlanResult.getContainerActions(
            pAppSettings,
            pNDSGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            container,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(1, containerActions.size());
    assertEquals(1, containerActions.get(0).getMoves().length);
    verify(moveProvider, times(1))
        .getSyncEncryptionAtRestPrivateEndpointMove(eq(container.getId()));

    // has needsUpdateAfter in the future
    needsUpdateAfter = Date.from(Instant.now().plus(5, ChronoUnit.MINUTES));
    doReturn(Optional.of(needsUpdateAfter)).when(endpoint).getNeedsUpdateAfter();
    containerActions =
        PlanResult.getContainerActions(
            pAppSettings,
            pNDSGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            container,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, containerActions.size());

    // has deleteRequested
    final CloudProviderPrivateEndpoint endpoint2 = mock(CloudProviderPrivateEndpoint.class);
    doReturn(Optional.of(endpoint2))
        .when(encryptionAtRest)
        .getPrivateEndpointForRegion(container.getRegion());
    doReturn(Optional.of(new Date())).when(endpoint2).getDeleteRequestedDate();
    containerActions =
        PlanResult.getContainerActions(
            pAppSettings,
            pNDSGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            container,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(1, containerActions.size());
    assertEquals(1, containerActions.get(0).getMoves().length);
    verify(moveProvider, times(2))
        .getSyncEncryptionAtRestPrivateEndpointMove(eq(container.getId()));
  }

  @Test
  public void testGetContainerActions_SyncAzureKeyVaultPrivateEndpoint_unprovisionedContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AZURE);
    final AzureCloudProviderContainer container =
        new AzureCloudProviderContainer(NDSModelTestFactory.getUnprovisionedAzureContainer());

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());
    doReturn(true).when(moveProvider).isPrivateEndpointSupported();

    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.isFeatureFlagInEnabledState(
            FeatureFlag.ENCRYPTION_AT_REST_AZURE_KEY_VAULT_PRIVATE_ENDPOINT))
        .thenReturn(true);
    // no action if there is no private endpoints
    List<PlannedAction> containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            container,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(0, containerActions.size());

    final NDSEncryptionAtRest encryptionAtRest = mock(NDSEncryptionAtRest.class);
    doReturn(encryptionAtRest).when(ndsGroup).getEncryptionAtRest();
    final CloudProviderPrivateEndpoint endpoint = mock(CloudProviderPrivateEndpoint.class);
    doReturn(Optional.of(Date.from(Instant.now().minus(5, ChronoUnit.SECONDS))))
        .when(endpoint)
        .getNeedsUpdateAfter();
    doReturn(Optional.of(endpoint))
        .when(encryptionAtRest)
        .getPrivateEndpointForRegion(container.getRegion());

    // provision container for the private endpoint
    containerActions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            List.of(),
            Set.of(),
            plannedActionFactory,
            container,
            List.of(),
            mock(ServerlessMTMPool.class),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            mock(Logger.class));
    assertEquals(2, containerActions.size());
    assertEquals(PlannedAction.ActionType.CREATE, containerActions.get(0).getActionType());
    assertEquals(PlannedAction.ActionType.UPDATE, containerActions.get(1).getActionType());
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any());
    verify(moveProvider, times(1))
        .getSyncEncryptionAtRestPrivateEndpointMove(eq(container.getId()));
  }

  @Test
  public void testGetContainerActions_usedForAZCapacityCheckCron_provisionContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AZURE);
    final AzureCloudProviderContainer container =
        new AzureCloudProviderContainer(NDSModelTestFactory.getUnprovisionedAzureContainer());

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());
    final AppSettings appSettings = mock(AppSettings.class);
    final Logger logger = mock(Logger.class);

    // matches internal project id, region matches -> should provision
    doReturn(Optional.of(ndsGroup.getGroupId()))
        .when(appSettings)
        .getCapacityReservationCronInternalProjectId();
    final Instant now = Instant.now();
    final AzureInstanceFamily azureUSEastInstanceFamily =
        (AzureInstanceFamily)
            Iterables.getLast(
                AzureNDSDefaults.INSTANCE_SIZE.getAvailableFamilies().get(AzureRegionName.US_EAST));

    final AzureCapacityDenyListEntry usEastDenylist =
        new AzureCapacityDenyListEntry(
            AzureRegionName.US_EAST,
            AzureNDSDefaults.INSTANCE_SIZE,
            azureUSEastInstanceFamily,
            Optional.of(new AzurePhysicalZoneId("zoneId1")),
            Date.from(now),
            CAPACITY_UNAVAILABLE,
            Optional.empty(),
            List.of());
    final AzureInstanceFamily azureUSWestInstanceFamily =
        (AzureInstanceFamily)
            Iterables.getLast(
                AzureNDSDefaults.INSTANCE_SIZE.getAvailableFamilies().get(AzureRegionName.US_WEST));
    final AzureCapacityDenyListEntry usWestDenylist =
        new AzureCapacityDenyListEntry(
            AzureRegionName.US_WEST,
            AzureNDSDefaults.INSTANCE_SIZE,
            azureUSWestInstanceFamily,
            Optional.of(new AzurePhysicalZoneId("zoneId1")),
            Date.from(now),
            CAPACITY_UNAVAILABLE,
            Optional.empty(),
            List.of());
    List<PlannedAction> actions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(usEastDenylist, usWestDenylist),
            logger);
    assertEquals(1, actions.size());
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any());

    // matches internal id, region does not match -> do not provision
    actions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(usWestDenylist),
            logger);
    assertTrue(actions.isEmpty());
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any()); // still 1

    // does not match internal id -> do not provision
    doReturn(Optional.of(ObjectId.get()))
        .when(appSettings)
        .getCapacityReservationCronInternalProjectId();
    actions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            logger);
    assertTrue(actions.isEmpty());
    verify(moveProvider, times(1)).getProvisionContainerMove(any(), any(), any()); // still 1
  }

  @Test
  public void testGetContainerActions_usedForAZCapacityCheckCron_destroyContainer() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AZURE);
    final AzureCloudProviderContainer container =
        (AzureCloudProviderContainer)
            ndsGroup.getCloudProviderContainersByType(CloudProvider.AZURE).get(0);
    doReturn(true).when(container).isProvisioned();
    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());
    final AppSettings appSettings = mock(AppSettings.class);
    final Logger logger = mock(Logger.class);

    // matches internal project id, region matches -> do not delete
    doReturn(Optional.of(ndsGroup.getGroupId()))
        .when(appSettings)
        .getCapacityReservationCronInternalProjectId();
    final Instant now = Instant.now();
    final AzureInstanceFamily azureUSEast2InstanceFamily =
        (AzureInstanceFamily)
            Iterables.getLast(
                AzureNDSDefaults.INSTANCE_SIZE
                    .getAvailableFamilies()
                    .get(AzureNDSDefaults.REGION_NAME));

    final AzureCapacityDenyListEntry usEast2Denylist =
        new AzureCapacityDenyListEntry(
            AzureNDSDefaults.REGION_NAME,
            AzureNDSDefaults.INSTANCE_SIZE,
            azureUSEast2InstanceFamily,
            Optional.of(new AzurePhysicalZoneId("zoneId1")),
            Date.from(now),
            CAPACITY_UNAVAILABLE,
            Optional.empty(),
            List.of());
    final AzureInstanceFamily azureUSWestInstanceFamily =
        (AzureInstanceFamily)
            Iterables.getLast(
                AzureNDSDefaults.INSTANCE_SIZE.getAvailableFamilies().get(AzureRegionName.US_WEST));
    final AzureCapacityDenyListEntry usWestDenylist =
        new AzureCapacityDenyListEntry(
            AzureRegionName.US_WEST,
            AzureNDSDefaults.INSTANCE_SIZE,
            azureUSWestInstanceFamily,
            Optional.of(new AzurePhysicalZoneId("zoneId1")),
            Date.from(now),
            CAPACITY_UNAVAILABLE,
            Optional.empty(),
            List.of());
    List<PlannedAction> actions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(usEast2Denylist, usWestDenylist),
            logger);
    assertTrue(actions.isEmpty());
    verify(moveProvider, never()).getDestroyContainerMove(any());

    // matches internal project id, region no longer needed -> delete
    actions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(usWestDenylist),
            logger);
    assertEquals(1, actions.size());
    verify(moveProvider, times(1)).getDestroyContainerMove(any());

    // does not match internal project id -> delete
    doReturn(Optional.of(ObjectId.get()))
        .when(appSettings)
        .getCapacityReservationCronInternalProjectId();
    actions =
        PlanResult.getContainerActions(
            appSettings,
            ndsGroup,
            mock(Group.class),
            Collections.emptyList(),
            Collections.emptySet(),
            plannedActionFactory,
            container,
            null,
            null,
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptyList(),
            Collections.emptySet(),
            false,
            Collections.emptyList(),
            List.of(),
            logger);
    assertEquals(1, actions.size());
    verify(moveProvider, times(2)).getDestroyContainerMove(any());
  }

  @Test
  public void testGetDiskBackupActionsForCluster() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(clusterDescription.getState()).thenReturn(ClusterDescription.State.WORKING);
    when(clusterDescription.isDeleteRequested()).thenReturn(false);
    when(clusterDescription.getCloudProviders()).thenReturn(Set.of(CloudProvider.AWS));
    when(clusterDescription.isDiskBackupEnabled()).thenReturn(false);
    when(clusterDescription.getClusterType()).thenReturn(ClusterDescription.ClusterType.REPLICASET);
    when(clusterDescription.isEncryptionAtRestEnabled()).thenReturn(false);

    final Group group = mock(Group.class);
    final NDSGroup ndsGroup = mock(NDSGroup.class);

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final BackupSnapshotDao mockedBackupSnapshotDao = mock(BackupSnapshotDao.class);
    when(mockedBackupSnapshotDao.findActionableNonCopyByCluster(any(), any()))
        .thenReturn(new ArrayList<>());
    when(mockedBackupSnapshotDao.findQueuedNonCopySnapshotForPlanning(any(), any()))
        .thenReturn(Optional.empty());

    final CpsSnapshotEngine cpsEngineEmptySnapshot =
        getMockedCpsSnapshotEngine(mockedBackupSnapshotDao);

    // test case for when disk backup is not enabled
    assertNull(
        PlanResult.getDiskBackupActionForCluster(
            clusterDescription,
            plannedActionFactory,
            cpsEngineEmptySnapshot,
            group,
            ndsGroup,
            PlanningType.BLOCKING,
            mock(Logger.class)));
    verify(plannedActionFactory, never()).forDiskSnapshot(any());

    when(clusterDescription.isDiskBackupEnabled()).thenReturn(true);

    // test case for when delete has been requested for the cluster
    when(clusterDescription.isDeleteRequested()).thenReturn(true);
    assertNull(
        PlanResult.getDiskBackupActionForCluster(
            clusterDescription,
            plannedActionFactory,
            cpsEngineEmptySnapshot,
            group,
            ndsGroup,
            PlanningType.BLOCKING,
            mock(Logger.class)));
    verify(plannedActionFactory, never()).forDiskSnapshot(any());

    when(clusterDescription.isDeleteRequested()).thenReturn(false);

    // test case for when cluster has been deleted
    when(clusterDescription.getState()).thenReturn(ClusterDescription.State.DELETED);
    assertNull(
        PlanResult.getDiskBackupActionForCluster(
            clusterDescription,
            plannedActionFactory,
            cpsEngineEmptySnapshot,
            group,
            ndsGroup,
            PlanningType.BLOCKING,
            mock(Logger.class)));
    verify(plannedActionFactory, never()).forDiskSnapshot(any());

    when(clusterDescription.getState()).thenReturn(ClusterDescription.State.WORKING);

    // test case for when disk backup is enabled but we have no queued snapshots
    assertNull(
        PlanResult.getDiskBackupActionForCluster(
            clusterDescription,
            plannedActionFactory,
            cpsEngineEmptySnapshot,
            group,
            ndsGroup,
            PlanningType.BLOCKING,
            mock(Logger.class)));
    verify(plannedActionFactory, never()).forDiskSnapshot(any());

    // test case for concurrent snapshot with blocking planning type
    final BackupSnapshot concurrentSnapshot = mock(BackupSnapshot.class);
    when(concurrentSnapshot.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.QUEUED);
    when(concurrentSnapshot.getPlanningType()).thenReturn(PlanningType.CONCURRENT);

    when(mockedBackupSnapshotDao.findActionableNonCopyByCluster(any(), any()))
        .thenReturn(List.of(concurrentSnapshot));
    when(mockedBackupSnapshotDao.findQueuedNonCopySnapshotForPlanning(any(), any()))
        .thenReturn(Optional.of(concurrentSnapshot));

    final CpsSnapshotEngine cpsEngineConcurrentSnapshot =
        getMockedCpsSnapshotEngine(mockedBackupSnapshotDao);
    assertNull(
        PlanResult.getDiskBackupActionForCluster(
            clusterDescription,
            plannedActionFactory,
            cpsEngineConcurrentSnapshot,
            group,
            ndsGroup,
            PlanningType.BLOCKING,
            mock(Logger.class)));
    verify(plannedActionFactory, never()).forDiskSnapshot(any());

    final Pair<List<PlannedAction>, List<AuditDescription>> concurrentSnapshotAction =
        PlanResult.getDiskBackupActionForCluster(
            clusterDescription,
            plannedActionFactory,
            cpsEngineConcurrentSnapshot,
            group,
            ndsGroup,
            PlanningType.CONCURRENT,
            mock(Logger.class));
    assertNotNull(concurrentSnapshotAction);
    verify(plannedActionFactory, times(1)).forDiskSnapshot(clusterDescription);
    assertEquals(
        PlannedAction.MoveType.MACHINE, concurrentSnapshotAction.getLeft().get(0).getMoveType());
    assertEquals(
        PlannedAction.ActionType.SNAPSHOT,
        concurrentSnapshotAction.getLeft().get(0).getActionType());
    assertEquals(concurrentSnapshotAction.getRight().size(), 1);
    assertEquals(concurrentSnapshotAction.getRight().get(0).getTitle(), "Cloud Backup");

    // test case with queued snapshots
    final BackupSnapshot snapshot = mock(BackupSnapshot.class);
    when(snapshot.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.QUEUED);
    when(snapshot.getPlanningType()).thenReturn(PlanningType.BLOCKING);

    final ArrayList<BackupSnapshot> snapshotList = new ArrayList<>();
    snapshotList.add(snapshot);

    when(mockedBackupSnapshotDao.findActionableNonCopyByCluster(any(), any()))
        .thenReturn(snapshotList);
    when(mockedBackupSnapshotDao.findQueuedNonCopySnapshotForPlanning(any(), any()))
        .thenReturn(Optional.of(snapshot));
    final CpsSnapshotEngine cpsEngineQueuedSnapshot =
        getMockedCpsSnapshotEngine(mockedBackupSnapshotDao);

    final Pair<List<PlannedAction>, List<AuditDescription>> snapshotAction =
        PlanResult.getDiskBackupActionForCluster(
            clusterDescription,
            plannedActionFactory,
            cpsEngineQueuedSnapshot,
            group,
            ndsGroup,
            PlanningType.BLOCKING,
            mock(Logger.class));
    assertNotNull(snapshotAction);
    verify(plannedActionFactory, times(2)).forDiskSnapshot(clusterDescription);
    assertEquals(PlannedAction.MoveType.MACHINE, snapshotAction.getLeft().get(0).getMoveType());
    assertEquals(
        PlannedAction.ActionType.SNAPSHOT, snapshotAction.getLeft().get(0).getActionType());
    assertEquals(snapshotAction.getRight().size(), 1);
    assertEquals(snapshotAction.getRight().get(0).getTitle(), "Cloud Backup");

    final BackupSnapshot resilient = mock(BackupSnapshot.class);
    when(resilient.isFallback()).thenReturn(true);
    when(resilient.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.QUEUED);
    when(resilient.getMemberIds()).thenReturn(List.of(new ObjectId()));
    when(resilient.getPlanningType()).thenReturn(PlanningType.BLOCKING);

    snapshotList.add(resilient);
    when(mockedBackupSnapshotDao.findActionableNonCopyByCluster(any(), any()))
        .thenReturn(snapshotList);
    when(mockedBackupSnapshotDao.findQueuedNonCopySnapshotForPlanning(any(), any()))
        .thenReturn(Optional.of(resilient));

    final CpsSnapshotEngine cpsEngineQueuedSnapshotWithResilient =
        getMockedCpsSnapshotEngine(mockedBackupSnapshotDao);

    final Pair<List<PlannedAction>, List<AuditDescription>> firstAction =
        PlanResult.getDiskBackupActionForCluster(
            clusterDescription,
            plannedActionFactory,
            cpsEngineQueuedSnapshotWithResilient,
            group,
            ndsGroup,
            PlanningType.BLOCKING,
            mock(Logger.class));
    assertNotNull(firstAction);
    verify(plannedActionFactory, times(2)).forDiskSnapshot(clusterDescription);
    verify(plannedActionFactory, times(1)).forResilientSnapshot(any(), any(), any());
    assertEquals(PlannedAction.MoveType.MACHINE, firstAction.getLeft().get(0).getMoveType());
    assertEquals(PlannedAction.ActionType.SNAPSHOT, firstAction.getLeft().get(0).getActionType());
    assertEquals(firstAction.getRight().size(), 1);
    assertEquals(firstAction.getRight().get(0).getBody(), "Started planning a resilient snapshot");

    // test sharded resilient plan creation
    when(clusterDescription.getClusterType()).thenReturn(ClusterDescription.ClusterType.SHARDED);
    final BackupSnapshot parentResilient = mock(BackupSnapshot.class);
    when(parentResilient.isFallback()).thenReturn(true);
    when(parentResilient.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.QUEUED);
    when(parentResilient.getMemberIds())
        .thenReturn(Arrays.asList(new ObjectId(), new ObjectId(), new ObjectId()));
    when(parentResilient.getPlanningType()).thenReturn(PlanningType.BLOCKING);

    snapshotList.add(parentResilient);
    when(mockedBackupSnapshotDao.findActionableNonCopyByCluster(any(), any()))
        .thenReturn(snapshotList);
    when(mockedBackupSnapshotDao.findQueuedNonCopySnapshotForPlanning(any(), any()))
        .thenReturn(Optional.of(parentResilient));

    final CpsSnapshotEngine cpsEngineQueuedSnapshotWithParentResilient =
        getMockedCpsSnapshotEngine(mockedBackupSnapshotDao);
    final Pair<List<PlannedAction>, List<AuditDescription>> secondAction =
        PlanResult.getDiskBackupActionForCluster(
            clusterDescription,
            plannedActionFactory,
            cpsEngineQueuedSnapshotWithParentResilient,
            group,
            ndsGroup,
            PlanningType.BLOCKING,
            mock(Logger.class));
    verify(plannedActionFactory, times(4)).forResilientSnapshot(any(), any(), any());
    assertEquals(4, secondAction.getLeft().size());
    assertEquals(firstAction.getRight().size(), 1);
  }

  @Test
  public void testM0ClusterNeedsNDSAccessRevoked() {
    final ObjectId groupId = new ObjectId();
    final ClusterDescription baseClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig().setGroupId(groupId)));

    // Tenant cluster is paused - no action needed
    final ClusterDescription m0Cluster_isPaused =
        baseClusterDescription.copy().setName("m0IsPaused").setIsPaused(true).build();
    assertTrue(m0Cluster_isPaused.isPaused());
    assertEquals(
        FreeInstanceSize.M0,
        m0Cluster_isPaused.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());
    assertFalse(PlanResult.m0ClusterNeedsNDSAccessRevoked(m0Cluster_isPaused));

    // Tenant cluster is not M0 - no action needed
    final List<ReplicationSpec> m2replicationSpecs =
        baseClusterDescription.getReplicationSpecsWithShardData().stream()
            .map(
                replicationSpec ->
                    replicationSpec
                        .copy()
                        .updateAllHardware(
                            new FreeHardwareSpec.Builder().setInstanceSize(FreeInstanceSize.M2))
                        .build())
            .collect(Collectors.toList());

    final ClusterDescription m2Cluster =
        new ClusterDescription(
                NDSModelTestFactory.getFreeClusterDescription(
                    new TestFreeClusterDescriptionConfig().setGroupId(groupId)))
            .copy()
            .setName("m2Cluster")
            .setReplicationSpecList(m2replicationSpecs)
            .build();
    assertFalse(m2Cluster.isPaused());
    assertEquals(
        FreeInstanceSize.M2, m2Cluster.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());
    assertFalse(PlanResult.m0ClusterNeedsNDSAccessRevoked(m2Cluster));

    // Tenant cluster is not paused and is an M0 and user has not been notified about pause date -
    // no action needed
    assertFalse(baseClusterDescription.isPaused());
    assertEquals(
        FreeInstanceSize.M0,
        baseClusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());

    final FreeTenantProviderOptions baseFreeTenantProviderOptions =
        (FreeTenantProviderOptions) baseClusterDescription.getFreeTenantProviderOptions();
    assertNull(baseFreeTenantProviderOptions.getNdsAccessRevokedDate());
    assertNull(baseFreeTenantProviderOptions.getUserNotifiedAboutPauseDate());
    assertFalse(PlanResult.m0ClusterNeedsNDSAccessRevoked(baseClusterDescription));

    // Tenant cluster is not paused and is an M0 but already has access revoked - no action needed
    final ClusterDescription m0ClusterAccessRevoked =
        new ClusterDescription(
                NDSModelTestFactory.getFreeClusterDescription(
                    new TestFreeClusterDescriptionConfig().setGroupId(groupId)))
            .copy()
            .setName("m0ClusterAccessRevoked")
            .setFreeTenantProviderOptions(
                baseFreeTenantProviderOptions.copy().setNdsAccessRevokedDate(new Date()).build())
            .build();
    assertFalse(m0ClusterAccessRevoked.isPaused());
    assertEquals(
        FreeInstanceSize.M0,
        m0ClusterAccessRevoked.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());

    final FreeTenantProviderOptions m0FreeTenantProviderOptions =
        (FreeTenantProviderOptions) m0ClusterAccessRevoked.getFreeTenantProviderOptions();
    assertNotNull(m0FreeTenantProviderOptions.getNdsAccessRevokedDate());
    assertFalse(PlanResult.m0ClusterNeedsNDSAccessRevoked(m0ClusterAccessRevoked));

    // Tenant cluster is not paused and is an M0 but user was notified recently about pause - no
    // action needed
    final Date now = new Date();
    final FreeTenantProviderOptions baseFreeTenantProviderOptions2 =
        (FreeTenantProviderOptions) baseClusterDescription.getFreeTenantProviderOptions();
    final ClusterDescription m0ClusterUserNotifiedRecently =
        new ClusterDescription(
                NDSModelTestFactory.getFreeClusterDescription(
                    new TestFreeClusterDescriptionConfig().setGroupId(groupId)))
            .copy()
            .setName("m0ClusterUserNotifiedRecently")
            .setFreeTenantProviderOptions(
                baseFreeTenantProviderOptions2.copy().setUserNotifiedAboutPauseDate(now).build())
            .build();
    assertFalse(m0ClusterUserNotifiedRecently.isPaused());
    assertEquals(
        FreeInstanceSize.M0,
        m0ClusterUserNotifiedRecently.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());

    final FreeTenantProviderOptions m0NotifiedRecentlyFreeTenantProviderOptions =
        (FreeTenantProviderOptions) m0ClusterUserNotifiedRecently.getFreeTenantProviderOptions();
    assertNull(m0NotifiedRecentlyFreeTenantProviderOptions.getNdsAccessRevokedDate());
    assertEquals(now, m0NotifiedRecentlyFreeTenantProviderOptions.getUserNotifiedAboutPauseDate());
    assertFalse(PlanResult.m0ClusterNeedsNDSAccessRevoked(m0ClusterUserNotifiedRecently));

    // Tenant cluster is not paused and is an M0 user was notified long ago about pause - action
    // needed
    final Date staleDate = new Date(0);

    final ClusterDescription m0ClusterUserNotifiedLongAgo =
        new ClusterDescription(
                NDSModelTestFactory.getFreeClusterDescription(
                    new TestFreeClusterDescriptionConfig().setGroupId(groupId)))
            .copy()
            .setName("m0ClusterUserNotifiedLongAgo")
            .setFreeTenantProviderOptions(
                ((FreeTenantProviderOptions) baseClusterDescription.getFreeTenantProviderOptions())
                    .copy()
                    .setUserNotifiedAboutPauseDate(staleDate)
                    .build())
            .build();
    assertFalse(m0ClusterUserNotifiedLongAgo.isPaused());
    assertEquals(
        FreeInstanceSize.M0,
        m0ClusterUserNotifiedLongAgo.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());
    assertNull(
        m0ClusterUserNotifiedLongAgo.getFreeTenantProviderOptions().getNdsAccessRevokedDate());
    assertEquals(
        staleDate,
        m0ClusterUserNotifiedLongAgo
            .getFreeTenantProviderOptions()
            .getUserNotifiedAboutPauseDate());
    assertTrue(PlanResult.m0ClusterNeedsNDSAccessRevoked(m0ClusterUserNotifiedLongAgo));

    // Tenant cluster is under compaction - no action needed
    final ClusterDescription m0ClusterUnderCompaction =
        new ClusterDescription(
                NDSModelTestFactory.getFreeClusterDescription(
                    new TestFreeClusterDescriptionConfig().setGroupId(groupId)))
            .copy()
            .setName("m0ClusterUserNotifiedLongAgo")
            .setFreeTenantProviderOptions(
                ((FreeTenantProviderOptions) baseClusterDescription.getFreeTenantProviderOptions())
                    .copy()
                    .setUserNotifiedAboutPauseDate(staleDate)
                    .setUnderCompaction(true)
                    .build())
            .build();
    assertFalse(PlanResult.m0ClusterNeedsNDSAccessRevoked(m0ClusterUnderCompaction));
  }

  @Test
  public void testGetContainersForClusterDescription() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup group = mock(NDSGroup.class);

    // Tenant cluster - not automatically paused
    // Case 1: container exists
    final ClusterDescription tenantCluster0 =
        new ClusterDescription(
                NDSModelTestFactory.getFreeClusterDescription(
                    new TestFreeClusterDescriptionConfig().setGroupId(groupId)))
            .copy()
            .setName("tenantCluster0")
            .build();
    assertFalse(tenantCluster0.isCrossCloudCluster());
    assertEquals(Set.of(CloudProvider.FREE), tenantCluster0.getCloudProviders());
    assertFalse(tenantCluster0.isPaused());
    final CloudProviderContainer tenantContainer0 = mock(CloudProviderContainer.class);

    doReturn(Optional.of(tenantContainer0))
        .when(group)
        .getCloudProviderContainer(
            eq(CloudProvider.FREE),
            eq(tenantCluster0.getRegionName()),
            eq(tenantCluster0.getName()));
    final Set<CloudProviderContainer> containersForCluster0 =
        NDSCloudProviderContainerSvc.getContainersInUse(group, tenantCluster0);
    assertFalse(containersForCluster0.isEmpty());
    assertTrue(containersForCluster0.contains(tenantContainer0));

    // Case 2: container does not exist -> exception
    doReturn(Optional.empty())
        .when(group)
        .getCloudProviderContainer(
            eq(CloudProvider.FREE),
            eq(tenantCluster0.getRegionName()),
            eq(tenantCluster0.getName()));
    try {
      NDSCloudProviderContainerSvc.getContainersInUse(group, tenantCluster0);
      fail("No cloud provider container should be unexpected for an unpaused tenant cluster.");
    } catch (final IllegalStateException pE) {
      assertEquals(
          "Cloud provider container does not exist for cluster with name tenantCluster0.",
          pE.getMessage());
    }

    // Tenant cluster - automatically paused
    // Case 1: container exists
    final ClusterDescription tenantCluster1 =
        new ClusterDescription(
                NDSModelTestFactory.getFreeClusterDescription(
                    new TestFreeClusterDescriptionConfig().setGroupId(groupId)))
            .copy()
            .setName("tenantCluster1")
            .setIsPaused(true)
            .build();
    assertFalse(tenantCluster1.isCrossCloudCluster());
    assertEquals(Set.of(CloudProvider.FREE), tenantCluster1.getCloudProviders());
    assertTrue(tenantCluster1.isPaused());
    final CloudProviderContainer tenantContainer1 = mock(CloudProviderContainer.class);
    doReturn(Optional.of(tenantContainer1))
        .when(group)
        .getCloudProviderContainer(
            eq(CloudProvider.FREE),
            eq(tenantCluster1.getRegionName()),
            eq(tenantCluster1.getName()));
    final Set<CloudProviderContainer> containersForCluster1 =
        NDSCloudProviderContainerSvc.getContainersInUse(group, tenantCluster1);
    assertFalse(containersForCluster1.isEmpty());
    assertTrue(containersForCluster1.contains(tenantContainer1));

    // Case 2: container does not exist -> no exception
    doReturn(Optional.empty())
        .when(group)
        .getCloudProviderContainer(
            eq(CloudProvider.FREE),
            eq(tenantCluster1.getRegionName()),
            eq(tenantCluster1.getName()));
    assertTrue(NDSCloudProviderContainerSvc.getContainersInUse(group, tenantCluster1).isEmpty());
  }

  @Test
  public void testIsCpsPitOutOfSync() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final BackupJob backupJob = mock(BackupJob.class);

    when(clusterDescription.isPitEnabled()).thenReturn(false);
    when(backupJob.isPitEnabled()).thenReturn(false);
    assertFalse(PlanResult.isCpsPitOutOfSync(clusterDescription, null));
    assertFalse(PlanResult.isCpsPitOutOfSync(clusterDescription, backupJob));

    when(clusterDescription.isPitEnabled()).thenReturn(false);
    when(backupJob.isPitEnabled()).thenReturn(true);
    assertTrue(PlanResult.isCpsPitOutOfSync(clusterDescription, backupJob));

    when(clusterDescription.isPitEnabled()).thenReturn(true);
    when(backupJob.isPitEnabled()).thenReturn(true);
    assertFalse(PlanResult.isCpsPitOutOfSync(clusterDescription, backupJob));

    when(clusterDescription.isPitEnabled()).thenReturn(true);
    when(backupJob.isPitEnabled()).thenReturn(false);
    assertTrue(PlanResult.isCpsPitOutOfSync(clusterDescription, backupJob));
  }

  @Test
  public void testGetTenantRestoreActions() {
    final NDSGroup ndsGroup = mock(NDSGroup.class);
    final Logger logger = mock(Logger.class);

    final TenantRestore tenantRestore =
        NDSModelTestFactory.getTenantRestore(
            "pro.xy", TenantBackupTask.State.PENDING, ObjectId.get(), ObjectId.get(), new Date());
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    final MoveProvider moveProvider = mock(MoveProvider.class);
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final Cluster cluster = mock(Cluster.class);
    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(new TestFreeClusterDescriptionConfig()));
    doReturn(clusterDescription).when(cluster).getClusterDescription();

    // Do not plan for a tenant restore if there is no tenant restore.
    final List<PlannedAction> actions0 =
        PlanResult.getTenantRestoreActions(
            ndsGroup, cluster, null, plannedActionFactory, logger, null);
    assertTrue(actions0.isEmpty());
    verify(plannedActionFactory, never()).forDoTenantRestore(any());
    verify(plannedActionFactory, never()).forDoTenantToTenantRestore(any());

    // Do not plan for a tenant restore if there is a backup restore job in progress.
    final ClusterDescription clusterDescription_restoreJobInProgress =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription()
                .append(
                    ClusterDescription.FieldDefs.RESTORE_JOB_IDS,
                    DbUtils.toBasicDBList(new ObjectId())));
    doReturn(clusterDescription_restoreJobInProgress).when(cluster).getClusterDescription();
    final List<PlannedAction> actions1 =
        PlanResult.getTenantRestoreActions(
            ndsGroup, cluster, tenantRestore, plannedActionFactory, logger, null);
    assertTrue(actions1.isEmpty());
    verify(plannedActionFactory, never()).forDoTenantRestore(any());
    verify(plannedActionFactory, never()).forDoTenantToTenantRestore(any());

    // Plan for a tenant restore if the destination cluster is a free tenant cluster
    // This should be the DoTenantToTenantRestoreMove which monitors the progress in order to show
    // activity in the UI
    final ClusterDescription clusterDescription_destinationFreeTenant =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig().setNeedsUnpauseTenantRestore(false)));
    doReturn(clusterDescription_destinationFreeTenant).when(cluster).getClusterDescription();

    final List<PlannedAction> actions2 =
        PlanResult.getTenantRestoreActions(
            ndsGroup, cluster, tenantRestore, plannedActionFactory, logger, null);
    assertEquals(1, actions2.size());
    assertEquals(PlannedAction.MoveType.TENANT_SNAPSHOT_RESTORE, actions2.get(0).getMoveType());
    assertEquals(PlannedAction.ActionType.UPDATE, actions2.get(0).getActionType());
    verify(plannedActionFactory, never()).forDoTenantRestore(any());
    verify(plannedActionFactory, times(1)).forDoTenantToTenantRestore(eq(tenantRestore));

    // Plan for a tenant restore if the destination cluster is a flex tenant cluster
    reset(plannedActionFactory);
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final ClusterDescription clusterDescription_destinationFlexTenant =
        new ClusterDescription(
            NDSModelTestFactory.getFlexClusterDescription(new TestFlexClusterDescriptionConfig()));
    doReturn(clusterDescription_destinationFlexTenant).when(cluster).getClusterDescription();

    final List<PlannedAction> actions3 =
        PlanResult.getTenantRestoreActions(
            ndsGroup, cluster, tenantRestore, plannedActionFactory, logger, null);
    assertEquals(1, actions3.size());
    assertEquals(PlannedAction.MoveType.TENANT_SNAPSHOT_RESTORE, actions3.get(0).getMoveType());
    assertEquals(PlannedAction.ActionType.UPDATE, actions3.get(0).getActionType());
    verify(plannedActionFactory, never()).forDoTenantRestore(any());
    verify(plannedActionFactory, times(1)).forDoTenantToTenantRestore(eq(tenantRestore));

    // Plan for a tenant restore if there is no backup restore job and there is a tenant restore.
    reset(plannedActionFactory);
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    final ClusterDescription clusterDescription_restoreButNoJob =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    doReturn(clusterDescription_restoreButNoJob).when(cluster).getClusterDescription();
    final List<PlannedAction> actions4 =
        PlanResult.getTenantRestoreActions(
            ndsGroup, cluster, tenantRestore, plannedActionFactory, logger, null);
    assertFalse(actions4.isEmpty());
    assertEquals(1, actions4.size());
    assertEquals(PlannedAction.MoveType.TENANT_SNAPSHOT_RESTORE, actions4.get(0).getMoveType());
    assertEquals(PlannedAction.ActionType.UPDATE, actions4.get(0).getActionType());
    verify(plannedActionFactory).forDoTenantRestore(eq(tenantRestore));
    verify(plannedActionFactory, never()).forDoTenantToTenantRestore(any());

    // Do not plan for a tenant restore if cluster is delete requested
    final ClusterDescription clusterDescription_deleteRequested =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig().setDeleteRequested(true)));
    doReturn(clusterDescription_deleteRequested).when(cluster).getClusterDescription();
    final List<PlannedAction> actions5 =
        PlanResult.getTenantRestoreActions(
            ndsGroup, cluster, tenantRestore, plannedActionFactory, logger, null);
    assertTrue(actions5.isEmpty());

    // Do not plan for a tenant restore if cluster is deleted
    final ClusterDescription clusterDescription_deleted =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                    new TestFreeClusterDescriptionConfig().setDeleteRequested(false))
                .append(ClusterDescription.FieldDefs.STATE, State.DELETED));
    doReturn(clusterDescription_deleted).when(cluster).getClusterDescription();

    final List<PlannedAction> actions6 =
        PlanResult.getTenantRestoreActions(
            ndsGroup, cluster, tenantRestore, plannedActionFactory, logger, null);
    assertTrue(actions6.isEmpty());

    // Throw exception if tenant restore target is a serverless instance
    final ClusterDescription clusterDescription_serverless =
        new ClusterDescription(NDSModelTestFactory.getServerlessClusterDescription());
    doReturn(clusterDescription_serverless).when(cluster).getClusterDescription();
    try {
      PlanResult.getTenantRestoreActions(
          ndsGroup, cluster, tenantRestore, plannedActionFactory, logger, null);
    } catch (final Exception pE) {
      assertInstanceOf(IllegalStateException.class, pE);
      assertTrue(
          pE.getMessage()
              .contains("Tenant restore is not currently supported for Serverless instances"));
    }

    // do not plan for tenant restore if cluster has a ongoing flex migration tenant
    doReturn(clusterDescription_destinationFreeTenant).when(cluster).getClusterDescription();
    final List<PlannedAction> actions7 =
        PlanResult.getTenantRestoreActions(
            ndsGroup,
            cluster,
            tenantRestore,
            plannedActionFactory,
            logger,
            mock(FlexTenantMigration.class));
    assertTrue(actions7.isEmpty());
  }

  @Test
  public void testIsShardedClusterFCVUpdated() {
    final ObjectId groupId = ObjectId.get();

    final Process process = mock(Process.class);
    when(process.getCluster()).thenReturn("test-deployment");
    when(process.isMongod()).thenReturn(true);
    when(process.getHostname()).thenReturn("host");
    when(process.getPort()).thenReturn(1);

    final AutomationConfig automationConfig = mock(AutomationConfig.class);
    final Deployment deployment = mock(Deployment.class);
    when(automationConfig.getDeployment()).thenReturn(deployment);
    when(deployment.getProcesses()).thenReturn(List.of(process));

    final HostLastPingSvc hostLastPingSvc = mock(HostLastPingSvc.class);
    final BasicBSONObject fcv = new BasicBSONObject().append("version", "8.0");
    final BasicBSONObject getParameterAll =
        new BasicBSONObject().append("featureCompatibilityVersion", fcv);
    doReturn(new BasicBSONObject().append("getParameterAll", getParameterAll))
        .when(hostLastPingSvc)
        .getLastPing(anyString());

    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn("test-deployment").when(clusterDescription).getDeploymentClusterName();
    doReturn(ClusterDescription.ClusterType.SHARDED).when(clusterDescription).getClusterType();

    // test FCV upgrade
    doReturn("8.1").when(clusterDescription).getFeatureCompatibilityVersion();
    assertTrue(
        PlanResult.isShardedClusterFCVUpdated(
            hostLastPingSvc, groupId, clusterDescription, automationConfig));

    // test FCV downgrade
    doReturn("7.0").when(clusterDescription).getFeatureCompatibilityVersion();
    assertTrue(
        PlanResult.isShardedClusterFCVUpdated(
            hostLastPingSvc, groupId, clusterDescription, automationConfig));

    // test no FCV change
    doReturn("8.0").when(clusterDescription).getFeatureCompatibilityVersion();
    assertFalse(
        PlanResult.isShardedClusterFCVUpdated(
            hostLastPingSvc, groupId, clusterDescription, automationConfig));

    doReturn(null).when(hostLastPingSvc).getLastPing(anyString());
    assertDoesNotThrow(
        () ->
            PlanResult.isShardedClusterFCVUpdated(
                hostLastPingSvc, groupId, clusterDescription, automationConfig));
  }

  @Test
  public void testGetTenantEndpointActions() {
    final Logger logger = mock(Logger.class);

    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn("clusterName").when(clusterDescription).getName();

    final Cluster cluster = mock(Cluster.class);
    doReturn(clusterDescription).when(cluster).getClusterDescription();
    doReturn(CloudProvider.AWS).when(clusterDescription).getBackingProvider();

    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    final MoveProvider moveProvider = mock(MoveProvider.class);
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());

    // Test no tenant endpoints.
    {
      final List<PlannedAction> actions =
          PlanResult.getTenantEndpointActions(
              cluster, Collections.emptyList(), plannedActionFactory, logger);
      assertTrue(actions.isEmpty());
      verify(logger, times(1)).info(contains("No tenant endpoints present"), anyString());
      verify(plannedActionFactory, never())
          .forTenantConsumerSyncPrivateEndpoints(any(), any(), any(), any());
    }

    // Test endpoints not needing moves.
    {
      final List<PlannedAction> actions =
          PlanResult.getTenantEndpointActions(
              cluster,
              List.of(
                  TenantPrivateNetworkingModelTestFactory.getFailedAWSTenantEndpoint(
                      oid(1), "tenant-instance")),
              plannedActionFactory,
              logger);
      assertTrue(actions.isEmpty());
      verify(plannedActionFactory, never())
          .forTenantConsumerSyncPrivateEndpoints(any(), any(), any(), any());
    }

    final AWSTenantEndpoint tenantEndpointNeedingAcceptance =
        TenantPrivateNetworkingModelTestFactory.getInitiatingAWSTenantEndpoint().toBuilder()
            .id(ObjectId.get())
            .build();
    final AWSTenantEndpoint tenantEndpointNeedingReservation =
        TenantPrivateNetworkingModelTestFactory.getReservationRequestedAWSTenantEndpoint(
                oid(1), "tenant-instance")
            .toBuilder()
            .id(ObjectId.get())
            .build();
    final AWSTenantEndpoint tenantEndpointNeedingRejection =
        TenantPrivateNetworkingModelTestFactory.getAvailableAWSTenantEndpoint().toBuilder()
            .id(ObjectId.get())
            .deleteRequested(true)
            .build();

    final List<TenantEndpoint> tenantEndpoints =
        List.of(
            tenantEndpointNeedingAcceptance,
            tenantEndpointNeedingRejection,
            tenantEndpointNeedingReservation);

    // Test generate moves to sync tenant endpoints.
    {
      final List<PlannedAction> actions =
          PlanResult.getTenantEndpointActions(
              cluster, tenantEndpoints, plannedActionFactory, logger);

      verify(plannedActionFactory, times(1))
          .forTenantConsumerSyncPrivateEndpoints(
              any(),
              eq(Set.of(tenantEndpointNeedingAcceptance.getId())),
              eq(Set.of(tenantEndpointNeedingRejection.getId())),
              eq(Set.of(tenantEndpointNeedingReservation.getId())));

      assertFalse(actions.isEmpty());

      final PlannedAction action = actions.get(0);
      assertEquals(MoveType.TENANT_PRIVATE_ENDPOINT, action.getMoveType());
      assertEquals(ActionType.UPDATE, action.getActionType());
      assertEquals(3, action.getMoves().length);
    }
  }

  @Test
  public void testClusterNeedsPublish() {

    {
      // needsMongoDBConfigPublishAfter is not set,
      // do not plan
      final Cluster cluster = mock(Cluster.class);
      final PlannedAction mongoDBConfigAction = mock(PlannedAction.class);
      final ClusterDescription clusterDescription = mock(ClusterDescription.class);

      doReturn(clusterDescription).when(cluster).getClusterDescription();
      doReturn(Optional.empty()).when(clusterDescription).getNeedsMongoDBConfigPublishAfter();

      assertFalse(PlanResult.clusterNeedsPublish(cluster, mongoDBConfigAction));
    }

    {
      // no action for configRS, needsMongoDBConfigPublishAfter is set
      // should schedule
      final Cluster cluster = mock(Cluster.class);
      final PlannedAction mongoDBConfigAction = mock(PlannedAction.class);
      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      final ProcessAutomationConfigPerClusterMove move =
          mock(ProcessAutomationConfigPerClusterMove.class);

      doReturn(clusterDescription).when(cluster).getClusterDescription();
      doReturn(new Move[] {move}).when(mongoDBConfigAction).getMoves();
      doReturn(MongoDBConfigType.AUTOMATION).when(move).getMongoDBConfigType();
      doReturn(MongoDBConfigType.AUTOMATION).when(clusterDescription).getMongoDBConfigType();
      doReturn(Optional.of(new Date()))
          .when(clusterDescription)
          .getNeedsMongoDBConfigPublishAfter();

      assertTrue(PlanResult.clusterNeedsPublish(cluster, mongoDBConfigAction));
    }
  }

  @Test
  public void testGetMaterialAndNonMaterialMachineActions() {
    final Cluster mockCluster = createMockMachineActionsCluster();
    Host primaryHost = mock(Host.class);
    doReturn(true).when(primaryHost).getIsPrimary();
    doReturn("host0").when(primaryHost).getName();
    doReturn(new Date()).when(primaryHost).getLastPing();

    PlannedAction action = mock(PlannedAction.class);
    doReturn(MoveType.MACHINE).when(action).getMoveType();

    PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    doReturn(action)
        .when(plannedActionFactory)
        .forReloadSslOnProcesses(any(), any(), any(), any(), any());
    doReturn(action).when(plannedActionFactory).forResyncMachine(any(), any(), any(), any());
    doReturn(action)
        .when(plannedActionFactory)
        .forRepairMachine(any(), any(), any(), any(), any(), anyBoolean(), eq(false), eq(false));

    final Pair<List<PlannedAction>, Boolean> actionsAndMaterialCategorization =
        PlanResult.getMachineActions(
            List.of(primaryHost),
            new ArrayList<>(),
            mockCluster,
            mock(NDSGroup.class),
            plannedActionFactory,
            mock(Logger.class),
            mock(AutomationConfig.class),
            false,
            new HashMap<>(),
            new MachineActionsDecisions.Builder(),
            Map.of(),
            Set.of(),
            false,
            false);

    final List<PlannedAction> materialActions = actionsAndMaterialCategorization.getLeft();
    final Boolean isMaterialAction = actionsAndMaterialCategorization.getRight();
    assertEquals(
        materialActions.size(), 3); // actions corresponding to HEAL_RESYNC, HEAL_REPAIR, and
    // REQUESTED_RELOAD_SSL_ON_PROCESSES
    assertEquals(false, isMaterialAction);
  }

  private Cluster createMockMachineActionsCluster() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);

    ReplicationSpec replSpec = mock(ReplicationSpec.class);
    doReturn(3).when(replSpec).getTotalNodes();

    doReturn(Optional.of(replSpec)).when(clusterDescription).getReplicationSpecById(any());

    final ReplicaSetHardware replicaSetHardware = mock(ReplicaSetHardware.class);
    doReturn(new ObjectId()).when(replicaSetHardware).getReplicationSpecId();

    // Reload SSL on processes is nonmaterial. others are material
    // actions
    List<Action> hardwareActions =
        List.of(Action.HEAL_REPAIR, Action.REQUESTED_RELOAD_SSL_ON_PROCESSES, Action.HEAL_RESYNC);

    // return instance hardware-s that each performs an material/non-material action
    doAnswer(
            invocation ->
                IntStream.range(0, 3)
                    .mapToObj(
                        i -> {
                          final InstanceHardware instanceHardware = mock(InstanceHardware.class);
                          doReturn(hardwareActions.get(i)).when(instanceHardware).getAction();
                          doReturn(new ObjectId()).when(instanceHardware).getInstanceId();

                          doReturn(Optional.of(String.format("host%d", i)))
                              .when(instanceHardware)
                              .getHostnameForAgents();

                          return instanceHardware;
                        }))
        .when(replicaSetHardware)
        .getAllHardware();

    final List<ReplicaSetHardware> replicaSetHardwareList = List.of(replicaSetHardware);

    final Cluster cluster = mock(Cluster.class);
    doReturn(clusterDescription).when(cluster).getClusterDescription();
    doReturn(replicaSetHardwareList).when(cluster).getReplicaSets();

    return cluster;
  }

  @Test
  public void testGetTransitionConfigServerActions() {
    final PlannedActionFactory plannedActionFactory = mock(PlannedActionFactory.class);
    final PlannedAction transitionConfigServerAction = mock(PlannedAction.class);
    doReturn(transitionConfigServerAction)
        .when(plannedActionFactory)
        .forTransitionConfigServerMove(any(), any());

    { // non-sharded cluster has no actions
      final Cluster cluster = mock(Cluster.class);
      doReturn(Optional.empty()).when(cluster).getReplicaSetWithConfigData();
      final List<PlannedAction> actions =
          PlanResult.getTransitionConfigServerActions(cluster, plannedActionFactory);
      assertEquals(0, actions.size());
    }

    { // sharded cluster with non-agent action on config ReplicaSetHardware
      final Cluster cluster = mock(Cluster.class);
      final ReplicaSetHardware configRS = mock(ReplicaSetHardware.class);
      doReturn(Optional.of(configRS)).when(cluster).getReplicaSetWithConfigData();
      doReturn(ReplicaSetHardware.Action.RECONFIGURE_CONFIG_SHARD).when(configRS).getAction();
      final List<PlannedAction> actions =
          PlanResult.getTransitionConfigServerActions(cluster, plannedActionFactory);
      assertEquals(0, actions.size());
    }

    { // sharded cluster with agent action on config ReplicaSetHardware
      final Cluster cluster = mock(Cluster.class);
      final ReplicaSetHardware configRS = mock(ReplicaSetHardware.class);
      doReturn(Optional.of(configRS)).when(cluster).getReplicaSetWithConfigData();
      doReturn(ReplicaSetHardware.Action.SET_CONFIG_SHARD_ON_AGENT).when(configRS).getAction();
      final List<PlannedAction> actions =
          PlanResult.getTransitionConfigServerActions(cluster, plannedActionFactory);
      assertEquals(1, actions.size());
      assertEquals(transitionConfigServerAction, actions.get(0));
    }
  }

  @Test
  public void testActionsExistAndAreScalingOnly_forScalingPlans() {
    final var builder = new PlannedAction.Builder();
    builder.moves(new Move[] {mock(Move.class)});
    final var action_0_no_strategy = builder.build();
    builder.replicaSetScalingStrategy(ReplicaSetScalingStrategy.SEQUENTIAL);
    final var action_1_sequential = builder.build();
    final var action_2_sequential = builder.build();

    assertTrue(
        PlanResult.actionsExistAndAreScalingOnly(
            List.of(action_1_sequential, action_2_sequential)));
    assertFalse(
        PlanResult.actionsExistAndAreScalingOnly(
            List.of(action_1_sequential, action_2_sequential, action_0_no_strategy)));
    assertFalse(PlanResult.actionsExistAndAreScalingOnly(List.of()));
  }

  @Test
  public void testIsUpscaling() {
    final var builder = new PlannedAction.Builder();
    builder.moves(new Move[] {mock(Move.class)});

    assertFalse(PlanResult.areScalingActionsUpscalingOnly(List.of()));

    final var noScaling = builder.build();
    assertFalse(PlanResult.areScalingActionsUpscalingOnly(List.of(noScaling)));

    builder.isUpscaling(true);
    final var actionWithUpscaling = builder.build();
    assertTrue(PlanResult.areScalingActionsUpscalingOnly(List.of(noScaling, actionWithUpscaling)));

    builder.isUpscaling(false);
    final var actionWithoutUpscaling = builder.build();
    assertFalse(
        PlanResult.areScalingActionsUpscalingOnly(
            List.of(noScaling, actionWithoutUpscaling, actionWithoutUpscaling)));
  }

  @Test
  public void testGetExcludedAnalyticsNodesHostnames() {
    final Cluster cluster = mock(Cluster.class);
    final String hostname0 = "hostname-electable-00-00";
    final String hostname1 = "hostname-electable-00-01";
    final String hostname2 = "hostname-electable-00-02";
    final String hostname3 = "hostname-analytics-00-03";
    doReturn(Optional.of(NodeType.ELECTABLE)).when(cluster).getNodeTypeForHostname(hostname0);
    doReturn(Optional.of(NodeType.ELECTABLE)).when(cluster).getNodeTypeForHostname(hostname1);
    doReturn(Optional.of(NodeType.ELECTABLE)).when(cluster).getNodeTypeForHostname(hostname2);
    doReturn(Optional.of(NodeType.ANALYTICS)).when(cluster).getNodeTypeForHostname(hostname3);

    doReturn(
            Map.of(
                hostname0,
                mock(AWSInstanceHardware.class),
                hostname1,
                mock(AWSInstanceHardware.class),
                hostname2,
                mock(AWSInstanceHardware.class),
                hostname3,
                mock(AWSInstanceHardware.class)))
        .when(cluster)
        .getHostnameForAgentsToInstanceHardwareMap();

    // Plan includes non-scaling action, so no hosts should be excluded.
    assertEquals(List.of(), PlanResult.getExcludedAnalyticsNodesHostnames(cluster, false));

    // Plan does not include any analytics node, so analytics hosts should be excluded.
    assertEquals(List.of(hostname3), PlanResult.getExcludedAnalyticsNodesHostnames(cluster, true));
  }

  @Test
  public void testShouldUpdateOSPolicyAndReboot() {
    final ServerlessLoadBalancingDeployment deployment =
        mock(ServerlessLoadBalancingDeployment.class);
    final EnvoyInstance envoyInstance = mock(EnvoyInstance.class);
    final AppSettings appSettings = mock(AppSettings.class);
    final Group group = mock(Group.class);

    final String appliedVersion = "1";
    doReturn(Optional.of(appliedVersion)).when(envoyInstance).getAppliedOSPolicyVersion();

    { // OS policy should be updated
      final String desiredVersion = "2";
      doReturn(Optional.of(desiredVersion))
          .when(deployment)
          .getOptionalDesiredEnvoyInstanceOSPolicyVersion();
      assertTrue(shouldUpdateOSPolicyAndReboot(deployment, envoyInstance, appSettings, group));
    }

    { // OS policy should not be updated
      final String desiredVersion = "1";
      doReturn(Optional.of(desiredVersion))
          .when(deployment)
          .getOptionalDesiredEnvoyInstanceOSPolicyVersion();
      assertFalse(shouldUpdateOSPolicyAndReboot(deployment, envoyInstance, appSettings, group));
    }

    {
      // OS policy should not be updated because it is empty on deployment
      doReturn(Optional.empty()).when(deployment).getOptionalDesiredEnvoyInstanceOSPolicyVersion();
      assertFalse(shouldUpdateOSPolicyAndReboot(deployment, envoyInstance, appSettings, group));
    }
  }

  @Test
  public void testIsFlexTenantMigrationPlanGenerationValid() {
    final TenantCloudProviderContainer tenantCloudProviderContainer =
        mock(TenantCloudProviderContainer.class);
    final ClusterDescription freeClusterDescription = mock(ClusterDescription.class);
    doReturn(Set.of(CloudProvider.FREE)).when(freeClusterDescription).getCloudProviders();
    final ClusterDescription serverlessClusterDescription = mock(ClusterDescription.class);
    doReturn(Set.of(CloudProvider.SERVERLESS))
        .when(serverlessClusterDescription)
        .getCloudProviders();
    final ClusterDescription flexClusterDescription = mock(ClusterDescription.class);
    doReturn(Set.of(CloudProvider.FLEX)).when(flexClusterDescription).getCloudProviders();
    final FlexTenantMigration migrationPending = mock(FlexTenantMigration.class);
    doReturn(MigrationStatus.MIGRATION_PENDING).when(migrationPending).getStatus();
    doReturn(CloudProvider.FREE).when(migrationPending).getSourceProvider();
    final FlexTenantMigration rollbackPending = mock(FlexTenantMigration.class);
    doReturn(MigrationStatus.ROLLBACK_PENDING).when(rollbackPending).getStatus();
    doReturn(CloudProvider.FLEX).when(rollbackPending).getSourceProvider();
    final TenantUpgradeStatus tenantUpgradeStatus = mock(TenantUpgradeStatus.class);

    // null flex tenant migration
    assertFalse(
        PlanResult.isFlexTenantMigrationPlanGenerationValid(
            null, null, freeClusterDescription, Optional.of(tenantCloudProviderContainer)));
    // non null flex tenant migration (Migration_pending) but tenant cluster description is type
    // FLEX
    assertFalse(
        PlanResult.isFlexTenantMigrationPlanGenerationValid(
            migrationPending,
            null,
            flexClusterDescription,
            Optional.of(tenantCloudProviderContainer)));

    // non null flex tenant migration (rollback pending) but tenant cluster description is type
    // shared
    assertFalse(
        PlanResult.isFlexTenantMigrationPlanGenerationValid(
            rollbackPending,
            null,
            freeClusterDescription,
            Optional.of(tenantCloudProviderContainer)));

    // non null tenant upgrade status
    assertFalse(
        PlanResult.isFlexTenantMigrationPlanGenerationValid(
            migrationPending,
            tenantUpgradeStatus,
            freeClusterDescription,
            Optional.of(tenantCloudProviderContainer)));

    // empty cloud provider container
    assertFalse(
        PlanResult.isFlexTenantMigrationPlanGenerationValid(
            migrationPending, null, freeClusterDescription, Optional.empty()));

    // passing for a valid migration
    assertTrue(
        PlanResult.isFlexTenantMigrationPlanGenerationValid(
            migrationPending,
            null,
            freeClusterDescription,
            Optional.of(tenantCloudProviderContainer)));

    // passing for a valid migration
    assertTrue(
        PlanResult.isFlexTenantMigrationPlanGenerationValid(
            migrationPending,
            null,
            serverlessClusterDescription,
            Optional.of(tenantCloudProviderContainer)));

    // passing for a valid rollback
    assertTrue(
        PlanResult.isFlexTenantMigrationPlanGenerationValid(
            rollbackPending,
            null,
            flexClusterDescription,
            Optional.of(tenantCloudProviderContainer)));
  }

  @Test
  public void testGCPServiceAccountCreation() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.GCP);
    final BasicDBObject gcpContainerDBObj =
        new GCPCloudProviderContainer(new ObjectId(), NDSDefaults.ATLAS_CIDR)
            .toDBObject()
            .append("id", new ObjectId());

    final GCPCloudProviderContainer container =
        spy(new GCPCloudProviderContainer(gcpContainerDBObj));

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final AppSettings appSettings = mock(AppSettings.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());
    doReturn(true).when(moveProvider).isPrivateEndpointSupported();
    doReturn(List.of(container)).when(ndsGroup).getCloudProviderContainers();

    { // case: no gcp projectID present and no request for service account setup
      // no actions planned
      List<PlannedAction> containerActions =
          PlanResult.getContainerActions(
              appSettings,
              ndsGroup,
              mock(Group.class),
              List.of(),
              Set.of(),
              plannedActionFactory,
              container,
              List.of(),
              mock(ServerlessMTMPool.class),
              Collections.emptyList(),
              Collections.emptyList(),
              Collections.emptyList(),
              Collections.emptyList(),
              Collections.emptySet(),
              false,
              Collections.emptyList(),
              List.of(),
              mock(Logger.class));
      assertEquals(0, containerActions.size());
    }

    {
      // case: no gcp projectID present with IN_PROGRESS service account setup status
      // plans GCPServiceAccountCreationMove
      final NDSCloudProviderAccessGCPServiceAccount serviceAccountMock =
          mock(NDSCloudProviderAccessGCPServiceAccount.class);
      doReturn(ServiceAccountProvisionStatus.IN_PROGRESS).when(serviceAccountMock).getStatus();
      final NDSCloudProviderAccess cloudProviderAccessMock = mock(NDSCloudProviderAccess.class);
      doReturn(cloudProviderAccessMock).when(ndsGroup).getCloudProviderAccess();
      doReturn(List.of(serviceAccountMock)).when(cloudProviderAccessMock).getGcpServiceAccounts();
      doReturn(Optional.of(container))
          .when(ndsGroup)
          .getCloudProviderContainerByType(CloudProvider.GCP);
      doReturn(Optional.empty()).when(container).getGcpProjectId();

      List<PlannedAction> containerActions =
          PlanResult.getContainerActions(
              appSettings,
              ndsGroup,
              mock(Group.class),
              List.of(),
              Set.of(),
              plannedActionFactory,
              container,
              List.of(),
              mock(ServerlessMTMPool.class),
              Collections.emptyList(),
              Collections.emptyList(),
              Collections.emptyList(),
              Collections.emptyList(),
              Collections.emptySet(),
              false,
              Collections.emptyList(),
              List.of(),
              mock(Logger.class));
      assertEquals(1, containerActions.size());
    }

    {
      // case: gcp projectID is present with any service account setup status
      // no actions planned
      doReturn(Optional.of("projectID-123")).when(container).getGcpProjectId();

      List<PlannedAction> containerActions =
          PlanResult.getContainerActions(
              appSettings,
              ndsGroup,
              mock(Group.class),
              List.of(),
              Set.of(),
              plannedActionFactory,
              container,
              List.of(),
              mock(ServerlessMTMPool.class),
              Collections.emptyList(),
              Collections.emptyList(),
              Collections.emptyList(),
              Collections.emptyList(),
              Collections.emptySet(),
              false,
              Collections.emptyList(),
              List.of(),
              mock(Logger.class));
      assertEquals(0, containerActions.size());
    }
  }

  @Test
  public void testGetContainerActions_PrometheusPrivateNetworking_Create() {
    final NDSGroup ndsGroup = NDSModelTestFactory.getMockedGroup(CloudProvider.AWS);
    final BasicDBObject awsContainerDBObj =
        new AWSCloudProviderContainer(
                new ObjectId(), AWSRegionName.US_EAST_1, NDSDefaults.ATLAS_CIDR)
            .toDBObject()
            .append("id", new ObjectId());

    final AWSCloudProviderContainer container =
        spy(new AWSCloudProviderContainer(awsContainerDBObj));

    final MoveProvider moveProvider = mock(MoveProvider.class);
    final AppSettings appSettings = mock(AppSettings.class);
    final PlannedActionFactory plannedActionFactory =
        spy(new PlannedActionFactory(mock(PlanContext.class)));
    doReturn(moveProvider).when(plannedActionFactory).getMoveProvider(any());
    doReturn(List.of(container)).when(ndsGroup).getCloudProviderContainers();

    // Fix the network permission issue that causes early return
    final Date networkPermissionDate = new Date();
    final NDSNetworkPermissionList networkPermissionList = mock(NDSNetworkPermissionList.class);
    doReturn(networkPermissionDate).when(networkPermissionList).getLastUpdated();
    doReturn(networkPermissionList).when(ndsGroup).getNetworkPermissionList();
    doReturn(Optional.of(networkPermissionDate))
        .when(container)
        .getNetworkPermissionListLastUpdated();

    // Prevent early returns
    doReturn(false)
        .when(ndsGroup)
        .getContainsCopiedContainers(); // Prevent copied containers early return
    doReturn(false).when(ndsGroup).isServerlessMTMHolder(); // Prevent serverless early returns
    doReturn(true).when(container).isProvisioned(); // Prevent not provisioned early return

    // Set up endpoint service on container to make it "needed" - same endpoint name as Prometheus
    // endpoint
    final DedicatedEndpointService endpointService = mock(DedicatedEndpointService.class);
    final AWSPrivateLinkInterfaceEndpoint endpoint = mock(AWSPrivateLinkInterfaceEndpoint.class);
    doReturn("vpce-test").when(endpoint).getEndpointId(); // Same as Prometheus endpoint
    doReturn(List.of(endpoint)).when(endpointService).getEndpoints();
    doReturn(List.of(endpointService)).when(container).getEndpointServices();

    // Set up common group and config for all test cases
    final Group group = mock(Group.class);
    doReturn(true).when(group).hasPrometheusIntegration();
    final PrometheusConfig promConfig = mock(PrometheusConfig.class);
    doReturn(promConfig).when(group).getPromConfig();
    doReturn(new Date(System.currentTimeMillis() - 1000)).when(promConfig).getNeedsSync();

    final ObjectId containerId = container.getId();

    {
      // Test case: PENDING endpoint triggers CREATE move
      final PrometheusEndpointForContainer pendingEndpoint =
          new PrometheusEndpointForContainer(
              "vpce-test", containerId, EndpointPrometheusStatus.PENDING);

      try (MockedStatic<NDSPrometheusEndpointSvc> mockedStatic =
          mockStatic(NDSPrometheusEndpointSvc.class)) {
        mockedStatic
            .when(() -> NDSPrometheusEndpointSvc.getPrometheusEndpointForContainer(container))
            .thenReturn(Optional.of(pendingEndpoint));

        final Move mockMove = mock(Move.class);
        doReturn(mockMove).when(moveProvider).getPrometheusPrivateNetworkingCreateMove(containerId);

        final List<PlannedAction> containerActions =
            PlanResult.getContainerActions(
                appSettings,
                ndsGroup,
                group,
                Collections.emptyList(),
                Collections.emptySet(),
                plannedActionFactory,
                container,
                Collections.emptyList(),
                mock(ServerlessMTMPool.class),
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptySet(),
                false,
                Collections.emptyList(),
                Collections.emptyList(),
                mock(Logger.class));

        assertEquals(1, containerActions.size());
        verify(moveProvider, times(1)).getPrometheusPrivateNetworkingCreateMove(containerId);
      }
    }

    {
      // Test case: AVAILABLE endpoint should not trigger any action
      reset(moveProvider); // Reset mock between test cases
      final PrometheusEndpointForContainer availableEndpoint =
          new PrometheusEndpointForContainer(
              "vpce-test", containerId, EndpointPrometheusStatus.AVAILABLE);

      try (MockedStatic<NDSPrometheusEndpointSvc> mockedStatic =
          mockStatic(NDSPrometheusEndpointSvc.class)) {
        mockedStatic
            .when(() -> NDSPrometheusEndpointSvc.getPrometheusEndpointForContainer(container))
            .thenReturn(Optional.of(availableEndpoint));

        final List<PlannedAction> containerActions =
            PlanResult.getContainerActions(
                appSettings,
                ndsGroup,
                group,
                List.of(),
                Set.of(),
                plannedActionFactory,
                container,
                List.of(),
                mock(ServerlessMTMPool.class),
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptySet(),
                false,
                Collections.emptyList(),
                List.of(),
                mock(Logger.class));

        assertEquals(0, containerActions.size());
        verify(moveProvider, never()).getPrometheusPrivateNetworkingCreateMove(any());
      }
    }

    {
      // Test case: DELETE endpoint should not trigger any action
      reset(moveProvider); // Reset mock between test cases
      final PrometheusEndpointForContainer deleteEndpoint =
          new PrometheusEndpointForContainer(
              "vpce-test", containerId, EndpointPrometheusStatus.DELETING);

      try (MockedStatic<NDSPrometheusEndpointSvc> mockedStatic =
          mockStatic(NDSPrometheusEndpointSvc.class)) {
        mockedStatic
            .when(() -> NDSPrometheusEndpointSvc.getPrometheusEndpointForContainer(container))
            .thenReturn(Optional.of(deleteEndpoint));

        final List<PlannedAction> containerActions =
            PlanResult.getContainerActions(
                appSettings,
                ndsGroup,
                group,
                List.of(),
                Set.of(),
                plannedActionFactory,
                container,
                List.of(),
                mock(ServerlessMTMPool.class),
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptySet(),
                false,
                Collections.emptyList(),
                List.of(),
                mock(Logger.class));

        assertEquals(0, containerActions.size());
        verify(moveProvider, never()).getPrometheusPrivateNetworkingCreateMove(any());
      }
    }
  }
}
