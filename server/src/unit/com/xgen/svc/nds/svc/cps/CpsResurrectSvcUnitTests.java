package com.xgen.svc.nds.svc.cps;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.amazonaws.services.ec2.model.Tag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.AzureBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.GCPBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._public.model.error.AzureApiException;
import com.xgen.cloud.nds.azure._public.svc.AzureApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.gcp._public.model.error.GCPApiException;
import com.xgen.cloud.nds.gcp._public.svc.GCPApiSvc;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import nl.altindag.log.LogCaptor;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ExtendWith(MockitoExtension.class)
public class CpsResurrectSvcUnitTests {

  @Mock private BackupSnapshotDao backupSnapshotDao;
  @Mock private AWSApiSvc awsApiSvc;
  @Mock private AzureApiSvc azureApiSvc;
  @Mock private GCPApiSvc gcpApiSvc;
  @Mock private CpsOplogStoreFactory cpsOplogStoreFactory;
  @Spy @InjectMocks private CpsResurrectSvc cpsResurrectSvc;

  private static final ObjectId GROUP_ID = oid(1);
  private static final String CLUSTER_NAME = "test";
  private static final ObjectId DELETED_CLUSTER_UNIQUE_ID = oid(4);
  private static final ObjectId RESURRECTED_CLUSTER_UNIQUE_ID = oid(3);
  private static final String EBS_SNAPSHOT_ID = "snap-0e41d027cebfb02a7";
  final Logger logger = LoggerFactory.getLogger(CpsResurrectSvcUnitTests.class);
  private AWSBackupSnapshot mockAWSSnapshot;
  private AzureBackupSnapshot mockAzureSnapshot;
  private GCPBackupSnapshot mockGCPSnapshot;

  @Nested
  @DisplayName("Update Snapshot Tags AWS tests")
  class UpdateSnapshotTagsAWSTests {
    @BeforeEach
    public void setup() {
      mockAWSSnapshot = mock(AWSBackupSnapshot.class);
      when(mockAWSSnapshot.getAwsAccountId()).thenReturn(oid(2));
      when(mockAWSSnapshot.getRegion()).thenReturn(AWSRegionName.US_EAST_1);
    }

    @Test
    public void testUpdateSnapshotTagsAWS_NoAwsAccountIdFound() {
      when(mockAWSSnapshot.getAwsAccountId()).thenReturn(null);
      assertThrows(
          SvcException.class,
          () ->
              cpsResurrectSvc.updateSnapshotTagsAws(
                  List.of(mockAWSSnapshot), RESURRECTED_CLUSTER_UNIQUE_ID));
    }

    @Test
    public void testUpdateSnapshotTagsAWS_TagsResourcesWithCorrectClusterUniqueId()
        throws SvcException {
      doNothing().when(awsApiSvc).tagResources(any(), any(), any(), any(), any());
      when(mockAWSSnapshot.getEbsSnapshotId()).thenReturn(EBS_SNAPSHOT_ID);
      cpsResurrectSvc.updateSnapshotTagsAws(
          List.of(mockAWSSnapshot), RESURRECTED_CLUSTER_UNIQUE_ID);
      verify(awsApiSvc, times(1))
          .tagResources(
              any(),
              any(),
              any(),
              eq(List.of(EBS_SNAPSHOT_ID)),
              eq(Set.of(new Tag("clusterUniqueId", RESURRECTED_CLUSTER_UNIQUE_ID.toString()))));
    }

    @Test
    public void testUpdateSnapshotTagsAWS_AwsApiException() {
      doThrow(new AWSApiException())
          .when(awsApiSvc)
          .tagResources(any(), any(), any(), any(), any());
      assertThrows(
          SvcException.class,
          () ->
              cpsResurrectSvc.updateSnapshotTagsAws(
                  List.of(mockAWSSnapshot), RESURRECTED_CLUSTER_UNIQUE_ID));
    }
  }

  @Nested
  @DisplayName("Update Snapshot Tags Azure tests")
  class UpdateSnapshotTagsAzureTests {

    @BeforeEach
    public void setup() {
      mockAzureSnapshot = mock(AzureBackupSnapshot.class);
      when(mockAzureSnapshot.getSubscriptionId()).thenReturn(oid(2));
      when(mockAzureSnapshot.getResourceGroup()).thenReturn("resourceGroup");
      when(mockAzureSnapshot.getName()).thenReturn("snapshotName");
    }

    @Test
    public void testUpdateSnapshotTagsAzure_TagsResourcesWithCorrectClusterUniqueId()
        throws SvcException {
      doNothing().when(azureApiSvc).tagSnapshot(any(), any(), any(), any(), any());
      cpsResurrectSvc.updateSnapshotTagsAzure(
          List.of(mockAzureSnapshot), RESURRECTED_CLUSTER_UNIQUE_ID);

      final Map<String, String> tags = new HashMap<>();
      tags.put("clusterUniqueId", RESURRECTED_CLUSTER_UNIQUE_ID.toString());
      verify(azureApiSvc, times(1)).tagSnapshot(any(), any(), any(), eq(tags), any());
    }

    @Test
    public void testUpdateSnapshotTagsAzure_AzureApiException() {
      doThrow(new AzureApiException())
          .when(azureApiSvc)
          .tagSnapshot(any(), any(), any(), any(), any());
      assertThrows(
          SvcException.class,
          () ->
              cpsResurrectSvc.updateSnapshotTagsAzure(
                  List.of(mockAzureSnapshot), RESURRECTED_CLUSTER_UNIQUE_ID));
    }
  }

  @Nested
  @DisplayName("Update Snapshot Tags GCP tests")
  class UpdateSnapshotTagsGCPTests {

    @BeforeEach
    public void setup() {
      mockGCPSnapshot = mock(GCPBackupSnapshot.class);
    }

    @Test
    public void testUpdateSnapshotTagsGCP_TagsResourcesWithCorrectClusterUniqueId()
        throws SvcException {
      doNothing().when(gcpApiSvc).updateSnapshotLabels(any(), any(), any(), any(), any());
      cpsResurrectSvc.updateSnapshotTagsGCP(
          List.of(mockGCPSnapshot), RESURRECTED_CLUSTER_UNIQUE_ID);

      final Map<String, String> labels = new HashMap<>();
      labels.put("clusteruniqueid", RESURRECTED_CLUSTER_UNIQUE_ID.toString());
      verify(gcpApiSvc, times(1)).updateSnapshotLabels(any(), any(), any(), eq(labels), any());
    }

    @Test
    public void testUpdateSnapshotTagsGCP_GCPApiException() {
      doThrow(new GCPApiException())
          .when(gcpApiSvc)
          .updateSnapshotLabels(any(), any(), any(), any(), any());

      assertThrows(
          SvcException.class,
          () ->
              cpsResurrectSvc.updateSnapshotTagsGCP(
                  List.of(mockGCPSnapshot), RESURRECTED_CLUSTER_UNIQUE_ID));
    }
  }

  @Nested
  @DisplayName("Attempt Update Snapshot Tags By Cloud Provider tests")
  class AttemptUpdateSnapshotTagsByCloudProviderTests {

    final LogCaptor nestedLogCaptor =
        LogCaptor.forClass(AttemptUpdateSnapshotTagsByCloudProviderTests.class);
    final Logger nestedLogger =
        LoggerFactory.getLogger(AttemptUpdateSnapshotTagsByCloudProviderTests.class);

    @BeforeEach
    public void setup() {
      // setup mock snapshots
      mockAWSSnapshot = mock(AWSBackupSnapshot.class);
      mockAzureSnapshot = mock(AzureBackupSnapshot.class);
      mockGCPSnapshot = mock(GCPBackupSnapshot.class);
    }

    @Test
    public void testAttemptUpdateSnapshotTagsByAWS_Success() throws SvcException {
      // always succeed on updateSnapshotTagsAws
      doNothing().when(cpsResurrectSvc).updateSnapshotTagsAws(any(), any());

      cpsResurrectSvc.attemptUpdateSnapshotTagsByCloudProvider(
          CloudProvider.AWS,
          List.of(mockAWSSnapshot),
          GROUP_ID,
          CLUSTER_NAME,
          RESURRECTED_CLUSTER_UNIQUE_ID,
          DELETED_CLUSTER_UNIQUE_ID,
          nestedLogger);

      // loop should only run 1 time and no errors should be logged
      verify(cpsResurrectSvc, times(1)).updateSnapshotTagsAws(any(), any());
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .noneMatch(
                  l ->
                      l.contains(
                          "Error updating snapshot tags during cluster resurrect with retained"
                              + " backups.")));
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .noneMatch(
                  l ->
                      l.contains(
                          "Reached max attempts attempting to update snapshot tags. Skipping"
                              + " snapshot tag update.")));
    }

    @Test
    public void testAttemptUpdateSnapshotTagsByAWS_OneFailure() throws SvcException {
      // fail on first run of updateSnapshotTagsAws, succeed on following runs
      doThrow(
              new SvcException(
                  NDSErrorCode.SNAPSHOT_TAGS_FAILED_TO_UPDATE, "foo", "bar", "AWS", "baz"))
          .doNothing()
          .when(cpsResurrectSvc)
          .updateSnapshotTagsAws(any(), any());

      cpsResurrectSvc.attemptUpdateSnapshotTagsByCloudProvider(
          CloudProvider.AWS,
          List.of(mockAWSSnapshot),
          GROUP_ID,
          CLUSTER_NAME,
          RESURRECTED_CLUSTER_UNIQUE_ID,
          DELETED_CLUSTER_UNIQUE_ID,
          nestedLogger);

      // retry loop runs twice
      verify(cpsResurrectSvc, times(2)).updateSnapshotTagsAws(any(), any());
      // per attempt error is logged once
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .anyMatch(
                  l ->
                      l.contains(
                          "Error updating snapshot tags during cluster resurrect with retained"
                              + " backups.")));
      // max attempts error is never logged
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .noneMatch(
                  l ->
                      l.contains(
                          "Reached max attempts attempting to update snapshot tags. Skipping"
                              + " snapshot tag update.")));
    }

    @Test
    public void testAttemptUpdateSnapshotTagsByAWS_MaxFailures() throws SvcException {
      // fail on every run of updateSnapshotTagsAws
      doThrow(
              new SvcException(
                  NDSErrorCode.SNAPSHOT_TAGS_FAILED_TO_UPDATE, "foo", "bar", "AWS", "baz"))
          .when(cpsResurrectSvc)
          .updateSnapshotTagsAws(any(), any());

      cpsResurrectSvc.attemptUpdateSnapshotTagsByCloudProvider(
          CloudProvider.AWS,
          List.of(mockAWSSnapshot),
          GROUP_ID,
          CLUSTER_NAME,
          RESURRECTED_CLUSTER_UNIQUE_ID,
          DELETED_CLUSTER_UNIQUE_ID,
          nestedLogger);

      // retry loop runs three times
      verify(cpsResurrectSvc, times(3)).updateSnapshotTagsAws(any(), any());
      // per attempt error is logged three times
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .anyMatch(
                  l ->
                      l.contains(
                          "Error updating snapshot tags during cluster resurrect with retained"
                              + " backups.")));
      // max attempts error is never logged
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .anyMatch(
                  l ->
                      l.contains(
                          "Reached max attempts attempting to update snapshot tags. Skipping"
                              + " snapshot tag update.")));
    }

    @Test
    public void testAttemptUpdateSnapshotTagsByAzure_Success() throws SvcException {
      // always succeed on updateSnapshotTagsAzure
      doNothing().when(cpsResurrectSvc).updateSnapshotTagsAzure(any(), any());

      cpsResurrectSvc.attemptUpdateSnapshotTagsByCloudProvider(
          CloudProvider.AZURE,
          List.of(mockAzureSnapshot),
          GROUP_ID,
          CLUSTER_NAME,
          RESURRECTED_CLUSTER_UNIQUE_ID,
          DELETED_CLUSTER_UNIQUE_ID,
          nestedLogger);

      // loop should only run 1 time and no errors should be logged
      verify(cpsResurrectSvc, times(1)).updateSnapshotTagsAzure(any(), any());
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .noneMatch(
                  l ->
                      l.contains(
                          "Error updating snapshot tags during cluster resurrect with retained"
                              + " backups.")));
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .noneMatch(
                  l ->
                      l.contains(
                          "Reached max attempts attempting to update snapshot tags. Skipping"
                              + " snapshot tag update.")));
    }

    @Test
    public void testAttemptUpdateSnapshotTagsByAzure_OneFailure() throws SvcException {
      // fail on first run of updateSnapshotTagsAzure, succeed on following runs
      doThrow(
              new SvcException(
                  NDSErrorCode.SNAPSHOT_TAGS_FAILED_TO_UPDATE, "foo", "bar", "AWS", "baz"))
          .doNothing()
          .when(cpsResurrectSvc)
          .updateSnapshotTagsAzure(any(), any());

      cpsResurrectSvc.attemptUpdateSnapshotTagsByCloudProvider(
          CloudProvider.AZURE,
          List.of(mockAzureSnapshot),
          GROUP_ID,
          CLUSTER_NAME,
          RESURRECTED_CLUSTER_UNIQUE_ID,
          DELETED_CLUSTER_UNIQUE_ID,
          nestedLogger);

      // retry loop runs twice
      verify(cpsResurrectSvc, times(2)).updateSnapshotTagsAzure(any(), any());
      // per attempt error is logged once
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .anyMatch(
                  l ->
                      l.contains(
                          "Error updating snapshot tags during cluster resurrect with retained"
                              + " backups.")));
      // max attempts error is never logged
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .noneMatch(
                  l ->
                      l.contains(
                          "Reached max attempts attempting to update snapshot tags. Skipping"
                              + " snapshot tag update.")));
    }

    @Test
    public void testAttemptUpdateSnapshotTagsByAzure_MaxFailures() throws SvcException {
      // fail on every run of updateSnapshotTagsAzure
      doThrow(
              new SvcException(
                  NDSErrorCode.SNAPSHOT_TAGS_FAILED_TO_UPDATE, "foo", "bar", "AWS", "baz"))
          .when(cpsResurrectSvc)
          .updateSnapshotTagsAzure(any(), any());

      cpsResurrectSvc.attemptUpdateSnapshotTagsByCloudProvider(
          CloudProvider.AZURE,
          List.of(mockAzureSnapshot),
          GROUP_ID,
          CLUSTER_NAME,
          RESURRECTED_CLUSTER_UNIQUE_ID,
          DELETED_CLUSTER_UNIQUE_ID,
          nestedLogger);

      // retry loop runs three times
      verify(cpsResurrectSvc, times(3)).updateSnapshotTagsAzure(any(), any());
      // per attempt error is logged three times
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .anyMatch(
                  l ->
                      l.contains(
                          "Error updating snapshot tags during cluster resurrect with retained"
                              + " backups.")));
      // max attempts error is never logged
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .anyMatch(
                  l ->
                      l.contains(
                          "Reached max attempts attempting to update snapshot tags. Skipping"
                              + " snapshot tag update.")));
    }

    @Test
    public void testAttemptUpdateSnapshotTagsByGCP_Success() throws SvcException {
      // always succeed on updateSnapshotTagsGCP
      doNothing().when(cpsResurrectSvc).updateSnapshotTagsGCP(any(), any());

      cpsResurrectSvc.attemptUpdateSnapshotTagsByCloudProvider(
          CloudProvider.GCP,
          List.of(mockGCPSnapshot),
          GROUP_ID,
          CLUSTER_NAME,
          RESURRECTED_CLUSTER_UNIQUE_ID,
          DELETED_CLUSTER_UNIQUE_ID,
          nestedLogger);

      // loop should only run 1 time and no errors should be logged
      verify(cpsResurrectSvc, times(1)).updateSnapshotTagsGCP(any(), any());
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .noneMatch(
                  l ->
                      l.contains(
                          "Error updating snapshot tags during cluster resurrect with retained"
                              + " backups.")));
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .noneMatch(
                  l ->
                      l.contains(
                          "Reached max attempts attempting to update snapshot tags. Skipping"
                              + " snapshot tag update.")));
    }

    @Test
    public void testAttemptUpdateSnapshotTagsByGCP_OneFailure() throws SvcException {
      // fail on first run of updateSnapshotTagsGCP, succeed on following runs
      doThrow(
              new SvcException(
                  NDSErrorCode.SNAPSHOT_TAGS_FAILED_TO_UPDATE, "foo", "bar", "AWS", "baz"))
          .doNothing()
          .when(cpsResurrectSvc)
          .updateSnapshotTagsGCP(any(), any());

      cpsResurrectSvc.attemptUpdateSnapshotTagsByCloudProvider(
          CloudProvider.GCP,
          List.of(mockGCPSnapshot),
          GROUP_ID,
          CLUSTER_NAME,
          RESURRECTED_CLUSTER_UNIQUE_ID,
          DELETED_CLUSTER_UNIQUE_ID,
          nestedLogger);

      // retry loop runs twice
      verify(cpsResurrectSvc, times(2)).updateSnapshotTagsGCP(any(), any());
      // per attempt error is logged once
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .anyMatch(
                  l ->
                      l.contains(
                          "Error updating snapshot tags during cluster resurrect with retained"
                              + " backups.")));
      // max attempts error is never logged
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .noneMatch(
                  l ->
                      l.contains(
                          "Reached max attempts attempting to update snapshot tags. Skipping"
                              + " snapshot tag update.")));
    }

    @Test
    public void testAttemptUpdateSnapshotTagsByGCP_MaxFailures() throws SvcException {
      // fail on every run of updateSnapshotTagsGCP
      doThrow(
              new SvcException(
                  NDSErrorCode.SNAPSHOT_TAGS_FAILED_TO_UPDATE, "foo", "bar", "AWS", "baz"))
          .when(cpsResurrectSvc)
          .updateSnapshotTagsGCP(any(), any());

      cpsResurrectSvc.attemptUpdateSnapshotTagsByCloudProvider(
          CloudProvider.GCP,
          List.of(mockGCPSnapshot),
          GROUP_ID,
          CLUSTER_NAME,
          RESURRECTED_CLUSTER_UNIQUE_ID,
          DELETED_CLUSTER_UNIQUE_ID,
          nestedLogger);

      // retry loop runs three times
      verify(cpsResurrectSvc, times(3)).updateSnapshotTagsGCP(any(), any());
      // per attempt error is logged three times
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .anyMatch(
                  l ->
                      l.contains(
                          "Error updating snapshot tags during cluster resurrect with retained"
                              + " backups.")));
      // max attempts error is never logged
      assertTrue(
          nestedLogCaptor.getErrorLogs().stream()
              .anyMatch(
                  l ->
                      l.contains(
                          "Reached max attempts attempting to update snapshot tags. Skipping"
                              + " snapshot tag update.")));
    }
  }

  @Test
  public void testUpdateSnapshotTags() {
    // setup mock snapshots
    mockAWSSnapshot = mock(AWSBackupSnapshot.class);
    when(mockAWSSnapshot.getOnlyCloudProvider()).thenReturn(CloudProvider.AWS);
    mockAzureSnapshot = mock(AzureBackupSnapshot.class);
    when(mockAzureSnapshot.getOnlyCloudProvider()).thenReturn(CloudProvider.AZURE);
    mockGCPSnapshot = mock(GCPBackupSnapshot.class);
    when(mockGCPSnapshot.getOnlyCloudProvider()).thenReturn(CloudProvider.GCP);
    List<ReplicaSetBackupSnapshot> mockSnapshots =
        List.of(mockAWSSnapshot, mockAzureSnapshot, mockGCPSnapshot);
    when(backupSnapshotDao.findActiveReplicaSnapshots(GROUP_ID, RESURRECTED_CLUSTER_UNIQUE_ID))
        .thenReturn(mockSnapshots);

    // do nothing on attemptUpdateSnapshotTagsByCloudProvider
    doNothing()
        .when(cpsResurrectSvc)
        .attemptUpdateSnapshotTagsByCloudProvider(any(), any(), any(), any(), any(), any(), any());

    cpsResurrectSvc.updateSnapshotTags(
        GROUP_ID, CLUSTER_NAME, RESURRECTED_CLUSTER_UNIQUE_ID, DELETED_CLUSTER_UNIQUE_ID, logger);

    // verify that attemptUpdateSnapshotTagsByCloudProvider is called for the right cloud provider
    // on the right snapshots
    verify(cpsResurrectSvc, times(1))
        .attemptUpdateSnapshotTagsByCloudProvider(
            CloudProvider.AWS,
            List.of(mockAWSSnapshot),
            GROUP_ID,
            CLUSTER_NAME,
            RESURRECTED_CLUSTER_UNIQUE_ID,
            DELETED_CLUSTER_UNIQUE_ID,
            logger);
    verify(cpsResurrectSvc, times(1))
        .attemptUpdateSnapshotTagsByCloudProvider(
            CloudProvider.AZURE,
            List.of(mockAzureSnapshot),
            GROUP_ID,
            CLUSTER_NAME,
            RESURRECTED_CLUSTER_UNIQUE_ID,
            DELETED_CLUSTER_UNIQUE_ID,
            logger);
    verify(cpsResurrectSvc, times(1))
        .attemptUpdateSnapshotTagsByCloudProvider(
            CloudProvider.GCP,
            List.of(mockGCPSnapshot),
            GROUP_ID,
            CLUSTER_NAME,
            RESURRECTED_CLUSTER_UNIQUE_ID,
            DELETED_CLUSTER_UNIQUE_ID,
            logger);
  }
}
