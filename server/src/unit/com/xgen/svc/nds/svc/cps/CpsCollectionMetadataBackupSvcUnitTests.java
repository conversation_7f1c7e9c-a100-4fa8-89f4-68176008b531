package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.luben.zstd.Zstd;
import com.xgen.cloud.common.db.legacy._public.cursor.ModelCursor;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.backupjob._public.model.CollectionMetadataConfig;
import com.xgen.cloud.cps.backupjob._public.model.CustomerCollectionMetadata;
import com.xgen.cloud.cps.restore._private.dao.CpsBackupCursorFileListsDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.CpsBackupCursorFileList;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSErrorCode;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.error.AWSApiException;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.ComplianceLevel;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.svc.nds.svc.cps.CpsCollectionMetadataBackupSvc.CompressedMetadata;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CpsCollectionMetadataBackupSvcUnitTests {
  @Mock private AWSAccountDao awsAccountDao;
  @Mock private AWSApiSvc awsApiSvc;
  @Mock private CpsSvc cpsSvc;
  @Mock private CpsBackupCursorFileListsDao cpsBackupCursorFileListsDao;
  @Spy @InjectMocks private CpsCollectionMetadataBackupSvc cpsCollectionMetadataBackupSvc;

  static final String JSON_VALUE =
      "{\"snapshotId\":\"000000000000000000000001\",\"namespaceStats\":{\"db1.coll1\":{\"shard-0\":{\"indexSize\":1024,\"dataStorageSize\":2048}},"
          + " \"db1.coll2\":{\"shard-1\":{\"indexSize\":512,\"dataStorageSize\":1024}},\"db2.coll1\":{\"shard-1\":{\"indexSize\":4096,\"dataStorageSize\":4096}}}}";

  @Test
  public void testGetCustomerCollectionMetadataFromS3()
      throws SvcException, JsonProcessingException {
    BackupSnapshot snapshot = mock(BackupSnapshot.class);
    final CollectionMetadataConfig collectionMetadataConfig =
        new CollectionMetadataConfig(
            CloudProvider.AWS,
            AWSRegionName.US_EAST_1,
            "bucketName",
            "testKey",
            CollectionMetadataConfig.Status.COMPLETED,
            Instant.now(),
            null);

    final AWSAccount awsAccount = mock(AWSAccount.class);
    when(awsAccountDao.findCPSOplogStoreAccount(any(), anyBoolean(), any()))
        .thenReturn(Optional.of(awsAccount));

    final ObjectId projectId = new ObjectId();
    doReturn(projectId).when(snapshot).getProjectId();
    doReturn(true).when(cpsSvc).isCpsBackupCustomerCollectionMetadataEnabled(projectId);

    final byte[] compressedData = Zstd.compress(JSON_VALUE.getBytes(StandardCharsets.UTF_8));
    final InputStream inputStream = new ByteArrayInputStream(compressedData);

    when(snapshot.getCollectionMetadataConfig()).thenReturn(collectionMetadataConfig);
    doReturn(inputStream)
        .when(awsApiSvc)
        .downloadFileStream(any(), eq(AWSRegionName.US_EAST_1), any(), anyInt(), any());

    final Optional<CustomerCollectionMetadata> customerCollectionMetadata =
        cpsCollectionMetadataBackupSvc.getCustomerCollectionMetadata(snapshot);

    verify(cpsCollectionMetadataBackupSvc, times(1)).downloadS3Object(any(), any());
    verify(awsApiSvc, times(1))
        .downloadFileStream(any(), eq(AWSRegionName.US_EAST_1), any(), anyInt(), any());
    assertTrue(customerCollectionMetadata.isPresent());
    assertEquals(
        customerCollectionMetadata.get(), CustomerCollectionMetadata.fromJsonString(JSON_VALUE));
  }

  @Test
  public void testPurgeCollectionMetadata() {
    final BackupSnapshot snapshot = mock(BackupSnapshot.class);
    final ObjectId snapshotId = ObjectId.get();
    when(snapshot.getId()).thenReturn(snapshotId);

    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    final String bucketName = "testBucket";
    final CollectionMetadataConfig collectionMetadataConfig =
        new CollectionMetadataConfig(
            CloudProvider.AWS,
            regionName,
            bucketName,
            "key",
            CollectionMetadataConfig.Status.COMPLETED,
            Instant.now(),
            null);
    when(snapshot.getCollectionMetadataConfig()).thenReturn(collectionMetadataConfig);

    final AWSAccount awsAccount = mock(AWSAccount.class);
    when(awsAccountDao.findCPSOplogStoreAccount(any(), anyBoolean(), any()))
        .thenReturn(Optional.of(awsAccount));

    // fail on first two tries
    doThrow(new AWSApiException(AWSErrorCode.INVALID_REQUEST))
        .doThrow(new AWSApiException(AWSErrorCode.INVALID_REQUEST))
        .doNothing()
        .when(awsApiSvc)
        .deleteObject(eq(awsAccount), eq(regionName), eq(bucketName), any(), any());

    cpsCollectionMetadataBackupSvc.purgeCollectionMetadata(snapshot);

    verify(awsApiSvc, times(3))
        .deleteObject(eq(awsAccount), eq(regionName), eq(bucketName), any(), any());
    verify(cpsSvc, times(1)).markCustomerCollectionMetadataConfigPurged(eq(snapshotId), any());
  }

  @Test
  public void testUploadCustomerCollectionMetadata() throws SvcException {
    final CollectionMetadataConfig collectionMetadataConfig =
        new CollectionMetadataConfig(
            CloudProvider.AWS,
            AWSRegionName.US_EAST_1,
            "bucketName",
            "testKey",
            CollectionMetadataConfig.Status.IN_PROGRESS,
            Instant.now(),
            null);

    final AWSAccount awsAccount = mock(AWSAccount.class);
    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    when(awsAccountDao.findCPSOplogStoreAccount(
            ComplianceLevel.COMMERCIAL, regionName.isCNRegion(), RegionUsageRestrictions.NONE))
        .thenReturn(Optional.of(awsAccount));

    final ObjectId snapshotId = ObjectId.get();

    final CustomerCollectionMetadata.ShardSizes shardSizes =
        new CustomerCollectionMetadata.ShardSizes(1024, 1024);
    final Map<String, CustomerCollectionMetadata.ShardSizes> shardSizeMap =
        Map.of("shard-0", shardSizes);
    final Map<String, Map<String, CustomerCollectionMetadata.ShardSizes>> namespaceMap =
        Map.of("db.collectionName", shardSizeMap);
    CustomerCollectionMetadata customerCollectionMetadata =
        new CustomerCollectionMetadata(snapshotId.toHexString(), namespaceMap);

    final CompressedMetadata compressedMetadata =
        cpsCollectionMetadataBackupSvc.compressCustomerCollectionMetadata(
            snapshotId, customerCollectionMetadata);
    cpsCollectionMetadataBackupSvc.uploadCustomerCollectionMetadata(
        snapshotId, collectionMetadataConfig, compressedMetadata);

    verify(awsApiSvc, times(1))
        .putObject(
            any(),
            eq(AWSRegionName.US_EAST_1),
            eq(collectionMetadataConfig.getBucketName()),
            eq(collectionMetadataConfig.getKey()),
            any(),
            any(),
            any());
  }

  @Test
  public void testCreateCollectionMetadataForReplicaSetSnapshot() {

    final ObjectId snapshotId = ObjectId.get();

    final CpsBackupCursorFileList cpsBackupCursorFileList = mock(CpsBackupCursorFileList.class);

    final List<CpsBackupCursorFileList.CursorFile> files = new ArrayList<>();
    files.add(new CpsBackupCursorFileList.CursorFile("/collection-file1", "db.col", 1024));
    files.add(new CpsBackupCursorFileList.CursorFile("/collection-file2", "db.col", 512));
    files.add(new CpsBackupCursorFileList.CursorFile("/collection-file3", "db.col2", 2048));
    files.add(new CpsBackupCursorFileList.CursorFile("/index-file1", "db.col", 1024));

    @SuppressWarnings("unchecked")
    final ModelCursor<CpsBackupCursorFileList> cursor =
        (ModelCursor<CpsBackupCursorFileList>) mock(ModelCursor.class);
    when(cpsBackupCursorFileListsDao.getCursorFiles(snapshotId)).thenReturn(cursor);

    when(cursor.hasNext()).thenReturn(true, false);
    when(cursor.next()).thenReturn(cpsBackupCursorFileList);
    when(cpsBackupCursorFileList.hasFiles()).thenReturn(true);
    when(cpsBackupCursorFileList.getFiles()).thenReturn(files);

    when(cpsBackupCursorFileList.getRsId()).thenReturn("rsId1");

    final CustomerCollectionMetadata metadata =
        cpsCollectionMetadataBackupSvc.createCollectionMetadataForReplicaSetSnapshot(snapshotId);

    final Map<String, Map<String, CustomerCollectionMetadata.ShardSizes>> expectedNamespaceStats =
        new HashMap<>();
    Map<String, CustomerCollectionMetadata.ShardSizes> shardSizesMap = new HashMap<>();
    shardSizesMap.put("rsId1", new CustomerCollectionMetadata.ShardSizes(1024, 1536));
    expectedNamespaceStats.put("db.col", shardSizesMap);

    Map<String, CustomerCollectionMetadata.ShardSizes> shardSizesMap2 = new HashMap<>();
    shardSizesMap2.put("rsId1", new CustomerCollectionMetadata.ShardSizes(0, 2048));
    expectedNamespaceStats.put("db.col2", shardSizesMap2);

    assertEquals(expectedNamespaceStats, metadata.getNamespaceStats());
  }

  @Test
  public void testPopulateNamespaceStats() {
    final CpsBackupCursorFileList fileList = mock(CpsBackupCursorFileList.class);
    final CpsBackupCursorFileList.CursorFile collectionFile =
        new CpsBackupCursorFileList.CursorFile("/path/collection_1.wt", "db.coll", 100);
    final CpsBackupCursorFileList.CursorFile indexFile =
        new CpsBackupCursorFileList.CursorFile("/path/index_1.wt", "db.coll", 50);

    when(fileList.hasFiles()).thenReturn(true);
    when(fileList.getRsId()).thenReturn("rsId0");
    when(fileList.getFiles()).thenReturn(List.of(collectionFile, indexFile));

    @SuppressWarnings("unchecked")
    ModelCursor<CpsBackupCursorFileList> cursor =
        (ModelCursor<CpsBackupCursorFileList>) mock(ModelCursor.class);
    when(cursor.hasNext()).thenReturn(true, false);
    when(cursor.next()).thenReturn(fileList);

    final Map<String, Map<String, CustomerCollectionMetadata.ShardSizes>> namespaceStats =
        new HashMap<>();
    CpsCollectionMetadataBackupSvc.populateNamespaceStats(cursor, namespaceStats, null);

    assertTrue(namespaceStats.containsKey("db.coll"));
    final Map<String, CustomerCollectionMetadata.ShardSizes> rsMap = namespaceStats.get("db.coll");
    assertTrue(rsMap.containsKey("rsId0"));
    final CustomerCollectionMetadata.ShardSizes sizes = rsMap.get("rsId0");
    assertEquals(100, sizes.getDataStorageSize());
    assertEquals(50, sizes.getIndexSize());
  }
}
