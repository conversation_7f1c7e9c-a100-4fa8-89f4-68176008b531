package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.pit._public.model.PitSetting;
import com.xgen.cloud.cps.pit._public.model.PitStorage;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@MockitoSettings(strictness = Strictness.LENIENT)
public class CpsOplogPurgeSvcUnitTests {
  @Spy @InjectMocks private CpsOplogPurgeSvc oplogPurgeSvc;

  @Test
  public void testPurgeSlicesInRs_error_handling() {
    final BackupJob backupJob = mock(BackupJob.class);

    final String rsId1 = "rs1";
    final String rsId2 = "rs2";
    doReturn(Set.of(rsId1, rsId2)).when(backupJob).getAllRsIds();
    doReturn(ObjectId.get()).when(backupJob).getId();
    doReturn(ObjectId.get()).when(backupJob).getProjectId();
    doReturn(ObjectId.get()).when(backupJob).getClusterUniqueId();
    doReturn("cluster1").when(backupJob).getClusterName();
    doThrow(mock(RuntimeException.class))
        .when(oplogPurgeSvc)
        .purgeSlicesInRsInEachStore(backupJob, rsId1);

    oplogPurgeSvc.purgeSlicesInCluster(backupJob);
    verify(oplogPurgeSvc, times(1)).purgeSlicesInRsInEachStore(backupJob, rsId2);
  }

  @Test
  public void testPurgeSlicesInJob_purgeCount() {
    final String rsId1 = "rs1";
    final PitSetting pitSetting1 = mock(PitSetting.class);

    final String rsId2 = "rs2";
    final PitSetting pitSetting2 = mock(PitSetting.class);

    final PitStorage pitStorage1 = new PitStorage("meta1", "blob1", "us-east-1");
    final PitStorage pitStorage2 = new PitStorage("meta2", "blob2", "us-east-2");
    final PitStorage pitStorage3 = new PitStorage("meta3", "blob3", "us-west-1");

    doReturn(Arrays.asList(pitStorage1, pitStorage2)).when(pitSetting1).getAllPitStorages();
    doReturn(List.of(pitStorage3)).when(pitSetting2).getAllPitStorages();

    doReturn(3L)
        .when(oplogPurgeSvc)
        .purgeSlicesInRs(any(BackupJob.class), eq("rs1"), any(PitStorage.class));

    doReturn(4L)
        .when(oplogPurgeSvc)
        .purgeSlicesInRs(any(BackupJob.class), eq("rs2"), any(PitStorage.class));

    final BackupJob backupJob =
        BackupJob.builder()
            .id(ObjectId.get())
            .projectId(ObjectId.get())
            .pitSettings(Map.of(rsId1, pitSetting1, rsId2, pitSetting2))
            .build();

    final long purgeCount = oplogPurgeSvc.purgeSlicesInCluster(backupJob);

    verify(oplogPurgeSvc).purgeSlicesInRsInEachStore(backupJob, rsId1);
    verify(oplogPurgeSvc).purgeSlicesInRsInEachStore(backupJob, rsId2);

    assertEquals(10L, purgeCount);
  }

  @Test
  public void testPurgeSlicesInCluster() {
    final BackupJob backupJob = mock(BackupJob.class);
    final String rsId = "rs1";
    final PitSetting pitSetting = mock(PitSetting.class);
    doReturn(pitSetting).when(backupJob).getPitSettingByRsId(rsId);

    final PitStorage pitStorage1 = new PitStorage("meta1", "blob1", "us-east-1");
    final PitStorage pitStorage2 = new PitStorage("meta2", "blob2", "us-east-2");
    final PitStorage pitStorage3 = new PitStorage("meta2", "blob2", "us-east-2");

    doReturn(List.of(pitStorage1)).when(pitSetting).getAllPitStorages();
    doReturn(2L)
        .when(oplogPurgeSvc)
        .purgeSlicesInRs(any(BackupJob.class), anyString(), any(PitStorage.class));

    oplogPurgeSvc.purgeSlicesInRsInEachStore(backupJob, rsId);

    verify(oplogPurgeSvc).purgeSlicesInRs(backupJob, rsId, pitStorage1);
    verify(oplogPurgeSvc, never()).purgeSlicesInRs(backupJob, rsId, pitStorage2);
    verify(oplogPurgeSvc, never()).purgeSlicesInRs(backupJob, rsId, pitStorage3);

    Mockito.reset(oplogPurgeSvc);

    doReturn(Arrays.asList(pitStorage1, pitStorage2)).when(pitSetting).getAllPitStorages();
    doReturn(3L)
        .when(oplogPurgeSvc)
        .purgeSlicesInRs(any(BackupJob.class), anyString(), any(PitStorage.class));
    oplogPurgeSvc.purgeSlicesInRsInEachStore(backupJob, rsId);

    verify(oplogPurgeSvc).purgeSlicesInRs(backupJob, rsId, pitStorage1);
    verify(oplogPurgeSvc).purgeSlicesInRs(backupJob, rsId, pitStorage2);
    verify(oplogPurgeSvc, times(2)).purgeSlicesInRs(any(), anyString(), any());

    Mockito.reset(oplogPurgeSvc);

    doReturn(Arrays.asList(pitStorage1, pitStorage2, pitStorage3))
        .when(pitSetting)
        .getAllPitStorages();
    doReturn(2L)
        .when(oplogPurgeSvc)
        .purgeSlicesInRs(any(BackupJob.class), anyString(), any(PitStorage.class));

    oplogPurgeSvc.purgeSlicesInRsInEachStore(backupJob, rsId);

    verify(oplogPurgeSvc, times(2)).purgeSlicesInRs(any(), anyString(), any());
  }
}
