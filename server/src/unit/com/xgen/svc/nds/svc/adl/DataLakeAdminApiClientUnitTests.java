package com.xgen.svc.nds.svc.adl;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static com.xgen.cloud.nds.datalake._public.model.NDSDataLakeS3Store.SAMPLE_BUCKET_NAME;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.protobuf.Any;
import com.google.protobuf.ByteString;
import com.google.protobuf.Int32Value;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.LazyStringArrayList;
import com.google.protobuf.ProtocolStringList;
import com.google.protobuf.Timestamp;
import com.xgen.cloud.common.appsettings._public.model.VersionInfo;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.compression.GZipCompressionUtils;
import com.xgen.cloud.common.util._public.json.driver.BSON;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAtlasStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAtlasStoreReadConcern;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAtlasStoreReadPreference;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAtlasStoreReadPreferenceTag;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAzureBlobStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeCollection;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeDLSAWSStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeDLSAzureStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeDatabase;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeHTTPStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeModelTestFactory;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeOnlineArchiveStore;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeS3Store;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeS3Store.StorageClass;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeStorage;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeStoreProvider;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeDataProcessRegion;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant.NDSDataLakeTenantBuilder;
import com.xgen.cloud.nds.datalake._public.model.dls.DataSetMetricsResult;
import com.xgen.cloud.nds.datalake._public.model.dls.NDSDataSetDLS;
import com.xgen.mhouse.backend.grpc.MetricsServiceGrpc;
import com.xgen.mhouse.backend.grpc.MetricsServiceGrpc.MetricsServiceBlockingStub;
import com.xgen.mhouse.backend.grpc.Mhmetrics;
import com.xgen.mhouse.backend.grpc.Mhmetrics.FieldInfo;
import com.xgen.mhouse.backend.grpc.Mhmetrics.GetDataSetMetricsRequest;
import com.xgen.mhouse.backend.grpc.Models.DataSetRequest;
import com.xgen.mhouse.backend.grpc.Models.DestroyDataSetsResponse;
import com.xgen.mhouse.backend.grpc.Models.ListDataSetsRequest;
import com.xgen.mhouse.backend.grpc.Models.ListDataSetsResponse;
import com.xgen.mhouse.backend.grpc.Models.MetadataLocation;
import com.xgen.mhouse.backend.grpc.StorageServiceGrpc;
import com.xgen.mhouse.backend.grpc.StorageServiceGrpc.StorageServiceBlockingStub;
import com.xgen.mhouse.backend.grpc.common.Bson.BSONType;
import com.xgen.mhouse.backend.grpc.common.Bson.BSONValue;
import com.xgen.mhouse.common.bson.v1.Bson;
import com.xgen.mhouse.common.cloud.v1.Cloud;
import com.xgen.mhouse.common.tenant.v1.Tenant.Settings;
import com.xgen.mhouse.common.v1.CorrelationId.CorrelationID;
import com.xgen.mhouse.common.v1.NamespaceOuterClass;
import com.xgen.mhouse.common.v1.ProjectId.ProjectID;
import com.xgen.mhouse.common.v1.TenantId.TenantID;
import com.xgen.mhouse.services.andoncord.grpc.v1.AndonCordServiceGrpc;
import com.xgen.mhouse.services.andoncord.grpc.v1.AndonCordServiceGrpc.AndonCordServiceBlockingStub;
import com.xgen.mhouse.services.andoncord.v1.Models.AndonCord;
import com.xgen.mhouse.services.andoncord.v1.Models.AndonCordState;
import com.xgen.mhouse.services.andoncord.v1.Models.CreateOrUpdateRequest;
import com.xgen.mhouse.services.andoncord.v1.Models.DataSet;
import com.xgen.mhouse.services.andoncord.v1.Models.ListRequest;
import com.xgen.mhouse.services.andoncord.v1.Models.ListResponse;
import com.xgen.mhouse.services.billinglimits.grpc.v1.UsageLimitsServiceGrpc.UsageLimitsServiceBlockingStub;
import com.xgen.mhouse.services.billinglimits.v1.Models.DataScanningLimitStatus;
import com.xgen.mhouse.services.billinglimits.v1.Models.DeleteUsageLimitRequest;
import com.xgen.mhouse.services.billinglimits.v1.Models.DeleteUsageLimitResponse;
import com.xgen.mhouse.services.billinglimits.v1.Models.GetUsageLimitsResponse;
import com.xgen.mhouse.services.billinglimits.v1.Models.LimitSpan;
import com.xgen.mhouse.services.billinglimits.v1.Models.OverrunPolicy;
import com.xgen.mhouse.services.billinglimits.v1.Models.SetUsageLimitRequest;
import com.xgen.mhouse.services.billinglimits.v1.Models.SetUsageLimitResponse;
import com.xgen.mhouse.services.billinglimits.v1.Models.TimeSpanScanningLimit;
import com.xgen.mhouse.services.billinglimits.v1.Models.UsageLimit;
import com.xgen.mhouse.services.logs.grpc.v1.LogsServiceGrpc;
import com.xgen.mhouse.services.logs.v1.Models;
import com.xgen.mhouse.services.op.grpc.v1.OpServiceGrpc.OpServiceBlockingStub;
import com.xgen.mhouse.services.op.v1.Models.CurrentOp;
import com.xgen.mhouse.services.op.v1.Models.OpUser;
import com.xgen.mhouse.services.privatelink.grpc.v1.PrivateLinkServiceGrpc;
import com.xgen.mhouse.services.privatelink.grpc.v1.PrivateLinkServiceGrpc.PrivateLinkServiceBlockingStub;
import com.xgen.mhouse.services.privatelink.v1.Models.AcceptPrivateEndpointResponse;
import com.xgen.mhouse.services.privatelink.v1.Models.DeletePrivateEndpointResponse;
import com.xgen.mhouse.services.querymetrics.admin.v1.Models.CalculateTenantMetricsResponse;
import com.xgen.mhouse.services.regions.grpc.v1.RegionsServiceGrpc.RegionsServiceBlockingStub;
import com.xgen.mhouse.services.regions.v1.Models.GetRegionsResponse;
import com.xgen.mhouse.services.regions.v1.Models.Region;
import com.xgen.mhouse.services.schema.grpc.v1.SchemaServiceGrpc;
import com.xgen.mhouse.services.schema.v1.Models.FailedNamespace;
import com.xgen.mhouse.services.schema.v1.Models.Frequency;
import com.xgen.mhouse.services.schema.v1.Models.GenerateAllSchemasResponse;
import com.xgen.mhouse.services.schema.v1.Models.GenerateSchemasResponse;
import com.xgen.mhouse.services.schema.v1.Models.GetSchemasResponse;
import com.xgen.mhouse.services.schema.v1.Models.Schema;
import com.xgen.mhouse.services.schema.v1.Models.SchemaWithMetadata;
import com.xgen.mhouse.services.storageconfig.grpc.v1.StorageConfigServiceGrpc;
import com.xgen.mhouse.services.storageconfig.v1.Models.DeleteStorageResponse;
import com.xgen.mhouse.services.storageconfig.v1.Models.GetStorageResponse;
import com.xgen.mhouse.services.storageconfig.v1.Models.SetStorageConfigOutdatedError;
import com.xgen.mhouse.services.storageconfig.v1.Models.SetStorageResponse;
import com.xgen.mhouse.services.storageconfig.v1.Models.Storage;
import com.xgen.mhouse.services.storageconfig.v1.Models.ValidateStorageResponse;
import com.xgen.mhouse.services.tenant.grpc.v1.TenantServiceGrpc;
import com.xgen.mhouse.services.tenant.grpc.v1.TenantServiceGrpc.TenantServiceBlockingStub;
import com.xgen.mhouse.services.tenant.v1.TenantStorage;
import com.xgen.mhouse.services.tenant.v1.TenantStorage.AtlasDataSource;
import com.xgen.mhouse.services.tenant.v1.TenantStorage.DLSDataSource;
import com.xgen.mhouse.services.tenant.v1.TenantStorage.DLSDataSource.ProviderRegion;
import com.xgen.mhouse.services.tenant.v1.TenantStorage.DLSDataSourceV2;
import com.xgen.mhouse.services.tenant.v1.TenantStorage.DataSource;
import com.xgen.mhouse.services.tenant.v1.TenantStorage.HTTPDataSource;
import com.xgen.mhouse.services.tenant.v1.TenantStorage.S3DataSource;
import com.xgen.mhouse.services.tenant.v1.TenantStorage.Store;
import com.xgen.mhouse.services.tenantsettings.grpc.v1.TenantSettingsServiceGrpc;
import com.xgen.mhouse.services.tenantsettings.grpc.v1.TenantSettingsServiceGrpc.TenantSettingsServiceBlockingStub;
import com.xgen.mhouse.services.tenantsettings.v1.Models.GetSettingsResponse;
import com.xgen.mhouse.services.tenantsettings.v1.Models.SetSettingsRequest;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeAndonCordView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCreateOrUpdateAndonCordValueDataSetView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCreateOrUpdateAndonCordValueView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCreateOrUpdateAndonCordView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeCurrentOpView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeDataSetView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeMetricsView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLAllSchemasView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLScheduledUpdateView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLSchemaStatus;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLSchemaView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeSQLSchemaWithMetadataView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageValidationErrorsView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeTenantSettingsView;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiClient.Methods;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.protobuf.StatusProto;
import io.grpc.stub.StreamObserver;
import io.prometheus.client.Histogram;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.Instant;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.BasicBSONObject;
import org.bson.BsonBinaryReader;
import org.bson.BsonBinaryWriter;
import org.bson.Document;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.DocumentCodec;
import org.bson.codecs.EncoderContext;
import org.bson.io.BasicOutputBuffer;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.MockedStatic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DataLakeAdminApiClientUnitTests {

  private static final Logger LOG = LoggerFactory.getLogger(DataLakeAdminApiClientUnitTests.class);
  private AppSettings _appSettings;
  private GroupSvc _groupSvc;
  private DataLakeGrpcClient _awsGrpcClient;
  private DataLakeGrpcClient _azureGrpcClient;
  private DataLakeGrpcClient _gcpGrpcClient;
  private DataLakeAdminApiClient _dataLakeAdminApiClient;

  private final String authToken = "dummyAuthToken";

  // Begin building the Schema field
  private static Bson.Value getTestSQLSchema(Number version) {
    final Bson.Value SchemaValue;
    try {
      DocumentCodec dc = new DocumentCodec();
      final Document doc =
          new Document().append("jsonSchema", new Document()).append("version", version);
      final BasicOutputBuffer buffer = new BasicOutputBuffer();

      dc.encode(new BsonBinaryWriter(buffer), doc, EncoderContext.builder().build());

      final ByteArrayOutputStream os = new ByteArrayOutputStream();
      buffer.pipe(os);

      final com.xgen.mhouse.common.bson.v1.Bson.Type schemaType =
          com.xgen.mhouse.common.bson.v1.Bson.Type.TYPE_EMBEDDED_DOCUMENT;
      Bson.Value.Builder newBuilder = Bson.Value.newBuilder().setType(schemaType);

      SchemaValue = newBuilder.setData(ByteString.copyFrom(os.toByteArray())).build();

      return SchemaValue;
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  private static SchemaWithMetadata getTestSQLSchemaWithMetadata() throws IOException {
    return buildSchemaDataInternal(
        new Date().toInstant().getEpochSecond(),
        new Date().toInstant().getNano(),
        getTestEmptySchema());
  }

  private static SchemaWithMetadata getTestSQLSchemaWithMetadata(
      long lastUpdatedBySeconds, int lastUpdatedByNanos) throws IOException {
    return buildSchemaDataInternal(lastUpdatedBySeconds, lastUpdatedByNanos, getTestEmptySchema());
  }

  private static SchemaWithMetadata getTestSQLSchemaWithMetadata(Bson.Value schema)
      throws IOException {
    return buildSchemaDataInternal(
        new Date().toInstant().getEpochSecond(), new Date().toInstant().getNano(), schema);
  }

  private static SchemaWithMetadata buildSchemaDataInternal(
      long lastUpdatedBySeconds, int lastUpdatedByNanos, Bson.Value schemaValue)
      throws IOException {

    // Create the Namespace field
    final NamespaceOuterClass.Namespace namespace =
        com.xgen.mhouse.common.v1.NamespaceOuterClass.Namespace.newBuilder()
            .setDatabaseName("db")
            .setCollectionName("coll")
            .build();

    // Set the Source enum field
    final SchemaWithMetadata.Source source = SchemaWithMetadata.Source.SOURCE_SET_IN_UI;

    // Create a timestamp we expect from Data Federation
    Timestamp testTime =
        Timestamp.newBuilder()
            .setSeconds(lastUpdatedBySeconds)
            .setNanos(lastUpdatedByNanos)
            .build();

    // Create the lastUpdate field
    final com.xgen.mhouse.services.schema.v1.Models.LastUpdateMetadata lastUpdate =
        com.xgen.mhouse.services.schema.v1.Models.LastUpdateMetadata.newBuilder()
            .setLastUpdatedBy("User")
            .setLastUpdatedTime(testTime)
            .setLastFailureDetails("Error")
            .setLastFailureTime(testTime)
            .build();

    // Create the final SchemaWithMetadata response
    return SchemaWithMetadata.newBuilder()
        .setSchema(schemaValue)
        .setNamespace(namespace)
        .setSource(source)
        .setLastUpdate(lastUpdate)
        .build();
  }

  private static Bson.Value getTestEmptySchema() throws IOException {
    DocumentCodec dc = new DocumentCodec();
    final BasicOutputBuffer buffer = new BasicOutputBuffer();

    dc.encode(new BsonBinaryWriter(buffer), new Document(), EncoderContext.builder().build());

    final ByteArrayOutputStream os = new ByteArrayOutputStream();
    buffer.pipe(os);

    final Bson.Type schemaType = Bson.Type.TYPE_EMBEDDED_DOCUMENT;
    Bson.Value.Builder newBuilder = Bson.Value.newBuilder().setType(schemaType);

    return newBuilder.setData(ByteString.copyFrom(os.toByteArray())).build();
  }

  private static TenantStorage.Storage getTestADLStorageV0() {
    final String oaStoreName = "oaStore";
    final String dlsAWSStoreName = "dlsAWSStore";
    final String dlsAzureStoreName = "dlsAzureStore";
    final ObjectId archiveId = oid(100);
    final ProviderRegion providerRegion =
        ProviderRegion.newBuilder().setProvider("aws").setRegion("us-east-1").build();

    return TenantStorage.Storage.newBuilder()
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName("s3Store1")
                .setS3(
                    TenantStorage.S3Provider.newBuilder()
                        .setDataRegion("us-west-1")
                        .setBucket("bucket1")
                        .setPublic(false)
                        .setDelimiter("x")
                        .setReplacementDelimiter("-")
                        .setDocumentCountMetadataKey("mdb")
                        .setIncludeTags(false)
                        .setPrefix("prefix")
                        .addAllAdditionalStorageClasses(
                            List.of("INTELLIGENT_TIERING", "STANDARD"))))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName("s3Store2")
                .setS3(
                    TenantStorage.S3Provider.newBuilder()
                        .setDataRegion("")
                        .setBucket("bucket2")
                        .setPublic(false)
                        .setDelimiter("")
                        .setReplacementDelimiter("")
                        .setDocumentCountMetadataKey("")
                        .setIncludeTags(true)
                        .setPrefix("")))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName("sampleS3Store")
                .setS3(
                    TenantStorage.S3Provider.newBuilder()
                        .setDataRegion("")
                        .setBucket(SAMPLE_BUCKET_NAME)
                        .setPublic(true)
                        .setDelimiter("")
                        .setReplacementDelimiter("")
                        .setDocumentCountMetadataKey("")
                        .setIncludeTags(true)
                        .setPrefix("")))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName("publicS3Store")
                .setS3(
                    TenantStorage.S3Provider.newBuilder()
                        .setDataRegion("us-east-1")
                        .setBucket("aws-public-blockchain")
                        .setPublic(true)
                        .setDelimiter("/")
                        .setReplacementDelimiter("")
                        .setDocumentCountMetadataKey("")
                        .setIncludeTags(false)
                        .setPrefix("")))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName("http1")
                .setHttp(
                    TenantStorage.HTTPProvider.newBuilder()
                        .addAllUrls(List.of("http://foo"))
                        .setInsecure(TenantStorage.Insecure.ALLOW)))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName("http2")
                .setHttp(
                    TenantStorage.HTTPProvider.newBuilder()
                        .setInsecure(TenantStorage.Insecure.UNDEFINED)))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName("atlas")
                .setAtlas(
                    TenantStorage.AtlasProvider.newBuilder()
                        .setProjectId(oid(3).toHexString())
                        .setClusterName("clusterName")
                        .setReadPreference(
                            TenantStorage.ReadPreference.newBuilder()
                                .setMode(
                                    TenantStorage.ReadPreference.Mode.valueOf(
                                        "primary".toUpperCase()))
                                .addAllTagSets(
                                    List.of(
                                        TenantStorage.TagSet.newBuilder()
                                            .addTagSet(
                                                TenantStorage.Tag.newBuilder()
                                                    .setName("region")
                                                    .setValue("US-WEST-1")
                                                    .build())
                                            .build()))
                                .setMaxStalenessSeconds(Int32Value.of(120)))
                        .setReadConcern(
                            TenantStorage.ReadConcern.newBuilder()
                                .setLevel(
                                    TenantStorage.ReadConcern.Level.valueOf(
                                        "local".toUpperCase())))))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName("atlas2")
                .setAtlas(
                    TenantStorage.AtlasProvider.newBuilder()
                        .setProjectId("")
                        .setClusterName("clusterName2")))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName(oaStoreName)
                .setOnlineArchive(
                    TenantStorage.OnlineArchiveProvider.newBuilder()
                        .setProjectId(oid(5).toHexString())
                        .setClusterId(oid(6).toHexString())
                        .setClusterName("oaClusterName")
                        .build()))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName("publicAzureBlobStore")
                .setAzure(
                    TenantStorage.AzureProvider.newBuilder()
                        .setServiceUrl("https://somestorageaccount.blob.core.windows.net")
                        .setContainerName("public-container")
                        .setDataRegion("eastus2")
                        .setPrefix("")
                        .setDelimiter("")
                        .setReplacementDelimiter("")
                        .setPublic(true)
                        .build()))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName("privateAzureBlobStore")
                .setAzure(
                    TenantStorage.AzureProvider.newBuilder()
                        .setServiceUrl("https://subdomain.customdomain")
                        .setContainerName("testContainer")
                        .setDataRegion("northeurope")
                        .setPrefix("somePrefix")
                        .setDelimiter("/")
                        .setReplacementDelimiter("-")
                        .setPublic(false)
                        .build()))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName(dlsAWSStoreName)
                .setDlsaws(
                    TenantStorage.DLSAWSProvider.newBuilder().setDataRegion("us-east-1").build()))
        .addStores(
            TenantStorage.Store.newBuilder()
                .setName(dlsAzureStoreName)
                .setDlsAzure(
                    TenantStorage.DLSAzureProvider.newBuilder().setDataRegion("eastus2").build()))
        .addDatabases(
            TenantStorage.Database.newBuilder()
                .setName("db1")
                .addCollections(
                    TenantStorage.Collection.newBuilder()
                        .setName("coll")
                        .addDataSources(
                            TenantStorage.DataSource.newBuilder()
                                .setStoreName("http1")
                                .setHttp(
                                    TenantStorage.HTTPDataSource.newBuilder()
                                        .addUrls("http://foo")))
                        .addDataSources(
                            TenantStorage.DataSource.newBuilder()
                                .setStoreName("s3Store1")
                                .setS3(
                                    TenantStorage.S3DataSource.newBuilder()
                                        .setTemplate("a/b/c")
                                        .setDefaultFormat("")
                                        .setOmitAttributes(false)))))
        .addDatabases(
            TenantStorage.Database.newBuilder()
                .setName("db2")
                .setMaxWildcardCollections(Int32Value.of(100))
                .addCollections(
                    TenantStorage.Collection.newBuilder()
                        .setName("coll2")
                        .addDataSources(
                            TenantStorage.DataSource.newBuilder()
                                .setStoreName("atlas")
                                .setAtlas(
                                    TenantStorage.AtlasDataSource.newBuilder()
                                        .setDatabaseName("d")
                                        .setCollectionName("c")
                                        .setCollectionRegex("")))
                        .addDataSources(
                            TenantStorage.DataSource.newBuilder()
                                .setStoreName("atlas2")
                                .setAtlas(
                                    TenantStorage.AtlasDataSource.newBuilder()
                                        .setDatabaseName("d")
                                        .setCollectionName("")
                                        .setCollectionRegex("r")))))
        .addDatabases(
            TenantStorage.Database.newBuilder()
                .setName("*")
                .addCollections(
                    TenantStorage.Collection.newBuilder()
                        .setName("*")
                        .addDataSources(
                            TenantStorage.DataSource.newBuilder()
                                .setStoreName("atlas")
                                .setAtlas(TenantStorage.AtlasDataSource.newBuilder()))))
        .addDatabases(
            TenantStorage.Database.newBuilder()
                .setName("db3")
                .addCollections(
                    TenantStorage.Collection.newBuilder()
                        .setName("coll3")
                        .addDataSources(
                            TenantStorage.DataSource.newBuilder()
                                .setStoreName(oaStoreName)
                                .setOnlineArchive(
                                    TenantStorage.DLSDataSource.newBuilder()
                                        .setDatasetName(archiveId.toHexString())
                                        .setMetadataLocation(providerRegion)
                                        .build()))))
        .addDatabases(
            TenantStorage.Database.newBuilder()
                .setName("db4")
                .addCollections(
                    TenantStorage.Collection.newBuilder()
                        .setName("coll4")
                        .addDataSources(
                            TenantStorage.DataSource.newBuilder()
                                .setStoreName(dlsAWSStoreName)
                                .setDlsaws(
                                    TenantStorage.DLSDataSourceV2.newBuilder()
                                        .setDatasetPrefix(
                                            "v1$atlas$snapshot$cluster$db$collection$date")
                                        .setTrimLevel(Int32Value.of(5))
                                        .build()))
                        .addDataSources(
                            TenantStorage.DataSource.newBuilder()
                                .setStoreName(dlsAzureStoreName)
                                .setDls(
                                    TenantStorage.DLSDataSourceV2.newBuilder()
                                        .setDatasetPrefix(
                                            "v1$atlas$snapshot$cluster$db$collection$date")
                                        .setTrimLevel(Int32Value.of(5))
                                        .build()))))
        .build();
  }

  private static Storage getTestADLStorage() {
    final Document configDoc = getTestStorageConfigDocument();
    try {
      final DocumentCodec dc = new DocumentCodec();
      final BasicOutputBuffer buffer = new BasicOutputBuffer();
      dc.encode(new BsonBinaryWriter(buffer), configDoc, EncoderContext.builder().build());

      final ByteArrayOutputStream os = new ByteArrayOutputStream();
      buffer.pipe(os);

      final Bson.Type configType = Bson.Type.TYPE_EMBEDDED_DOCUMENT;
      final Bson.Value.Builder newBuilder = Bson.Value.newBuilder().setType(configType);
      final Bson.Value config = newBuilder.setData(ByteString.copyFrom(os.toByteArray())).build();

      return Storage.newBuilder().setConfig(config).build();
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  private static NDSDataLakeStorageV1View getTestNDSStorageV1View() {
    final Document config = getTestStorageConfigDocument();
    return new NDSDataLakeStorageV1View(config);
  }

  private static Document getTestStorageConfigDocument() {
    final String oaStoreName = "oaStore";
    final String dlsAWSStoreName = "dlsAWSStore";
    final String dlsAzureStoreName = "dlsAzureStore";
    final ObjectId archiveId = oid(100);

    return new Document()
        .append(
            NDSDataLakeStorageV1View.FieldDefs.CONFIG,
            new Document()
                .append(
                    DataLakeTestUtils.StorageConfigFieldDefs.STORES,
                    List.of(
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.S3)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION,
                                AWSRegionName.US_WEST_1.getValue())
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.BUCKET, "bucket1")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.DELIMITER, "x")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store
                                    .REPLACEMENT_DELIMITER,
                                "-")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store
                                    .DOCUMENT_COUNT_METADATA_KEY,
                                "mdb")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.PREFIX, "prefix")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store
                                    .ADDITIONAL_STORAGE_CLASSES,
                                List.of("INTELLIGENT_TIERING", "STANDARD"))
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME, "s3Store1"),
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.S3)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION,
                                AWSRegionName.US_WEST_1.getValue())
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.BUCKET, "bucket2")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.DELIMITER, "")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store
                                    .REPLACEMENT_DELIMITER,
                                "")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store
                                    .DOCUMENT_COUNT_METADATA_KEY,
                                "")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.PREFIX, "")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.INCLUDE_TAGS, true)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME, "s3Store2"),
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.S3)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION,
                                AWSRegionName.US_WEST_1.getValue())
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.BUCKET,
                                SAMPLE_BUCKET_NAME)
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.DELIMITER, "")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store
                                    .REPLACEMENT_DELIMITER,
                                "")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store
                                    .DOCUMENT_COUNT_METADATA_KEY,
                                "")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.PREFIX, "")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.INCLUDE_TAGS, true)
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.PUBLIC, true)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME,
                                "publicS3Store"),
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.HTTP)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.URLS,
                                List.of("http://foo"))
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.ALLOW_INSECURE, true)
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME, "http1"),
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.HTTP)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.DEFAULT_FORMAT,
                                ".json")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME, "http2"),
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.ATLAS)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.READ_CONCERN,
                                new Document().append("level", "local"))
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.READ_PREFERENCE,
                                new Document()
                                    .append("mode", "secondary")
                                    .append(
                                        "tagSets",
                                        List.of(
                                            List.of(
                                                new Document()
                                                    .append("name", "provider")
                                                    .append("value", "AWS"))))
                                    .append("maxStalenessSeconds", 100))
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROJECT_ID,
                                oid(3).toHexString())
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.CLUSTER_NAME,
                                "clusterName")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME, "atlas"),
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.ATLAS)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.CLUSTER_NAME,
                                "clusterName2")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME, "atlas2"),
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.ONLINE_ARCHIVE)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROJECT_ID,
                                oid(3).toHexString())
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.CLUSTER_ID,
                                oid(4).toHexString())
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.CLUSTER_NAME,
                                "oaClusterName")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME, oaStoreName),
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.AZURE_BLOB_STORAGE)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION,
                                AzureRegionName.US_EAST_2.getValue())
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.SERVICE_URL,
                                "https://somestorageaccount.blob.core.windows.net")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.CONTAINER_NAME,
                                "public-container")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.PREFIX, "")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.DELIMITER, "")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store
                                    .REPLACEMENT_DELIMITER,
                                "")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.PUBLIC, true)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME,
                                "publicAzureBlobStore"),
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.AZURE_BLOB_STORAGE)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION,
                                AzureRegionName.EUROPE_NORTH.getValue())
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.SERVICE_URL,
                                "https://subdomain.customdomain")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.CONTAINER_NAME,
                                "test-container")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PREFIX, "somePrefix")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.DELIMITER, "/")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store
                                    .REPLACEMENT_DELIMITER,
                                "-")
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Store.PUBLIC, false)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME,
                                "privateAzureBlobStore"),
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.DLS_AWS)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION,
                                AWSRegionName.US_EAST_1.getValue())
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME,
                                dlsAWSStoreName),
                        new Document()
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER,
                                NDSDataLakeStoreProvider.ProviderValues.DLS_AZURE)
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION,
                                AzureRegionName.US_EAST_2.getValue())
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME,
                                dlsAzureStoreName)))
                .append(
                    DataLakeTestUtils.StorageConfigFieldDefs.DATABASES,
                    List.of(
                        new Document()
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Database.NAME, "db1")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS,
                                List.of(
                                    new Document()
                                        .append(
                                            DataLakeTestUtils.StorageConfigFieldDefs.Collection
                                                .NAME,
                                            "coll")
                                        .append(
                                            DataLakeTestUtils.StorageConfigFieldDefs.Collection
                                                .DATA_SOURCES,
                                            List.of(
                                                new Document()
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.STORE_NAME,
                                                        "http1")
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.URLS,
                                                        List.of("http://foo")),
                                                new Document()
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.STORE_NAME,
                                                        "s3Store1")
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.PATH,
                                                        "a/b/c")
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.DEFAULT_FORMAT,
                                                        ""))))),
                        new Document()
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Database.NAME, "db2")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS,
                                List.of(
                                    new Document()
                                        .append(
                                            DataLakeTestUtils.StorageConfigFieldDefs.Collection
                                                .NAME,
                                            "coll2")
                                        .append(
                                            DataLakeTestUtils.StorageConfigFieldDefs.Collection
                                                .DATA_SOURCES,
                                            List.of(
                                                new Document()
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.STORE_NAME,
                                                        "atlas")
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.DATABASE,
                                                        "d")
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.COLLECTION,
                                                        "c"),
                                                new Document()
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.STORE_NAME,
                                                        "atlas2")
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.DATABASE,
                                                        "d")
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.COLLECTION_REGEX,
                                                        "r"))))),
                        new Document()
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Database.NAME, "*")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS,
                                List.of(
                                    new Document()
                                        .append(
                                            DataLakeTestUtils.StorageConfigFieldDefs.Collection
                                                .NAME,
                                            "*")
                                        .append(
                                            DataLakeTestUtils.StorageConfigFieldDefs.Collection
                                                .DATA_SOURCES,
                                            List.of(
                                                new Document()
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.STORE_NAME,
                                                        "atlas"))))),
                        new Document()
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Database.NAME, "db3")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS,
                                List.of(
                                    new Document()
                                        .append(
                                            DataLakeTestUtils.StorageConfigFieldDefs.Collection
                                                .NAME,
                                            "coll3")
                                        .append(
                                            DataLakeTestUtils.StorageConfigFieldDefs.Collection
                                                .DATA_SOURCES,
                                            List.of(
                                                new Document()
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.STORE_NAME,
                                                        oaStoreName)
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.DATASET_NAME,
                                                        archiveId.toHexString())
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.METADATA_LOCATION,
                                                        new Document()
                                                            .append("provider", "aws")
                                                            .append("region", "us-east-1")))))),
                        new Document()
                            .append(DataLakeTestUtils.StorageConfigFieldDefs.Database.NAME, "db4")
                            .append(
                                DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS,
                                List.of(
                                    new Document()
                                        .append(
                                            DataLakeTestUtils.StorageConfigFieldDefs.Collection
                                                .NAME,
                                            "coll4")
                                        .append(
                                            DataLakeTestUtils.StorageConfigFieldDefs.Collection
                                                .DATA_SOURCES,
                                            List.of(
                                                new Document()
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.STORE_NAME,
                                                        dlsAWSStoreName)
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.DATASET_PREFIX,
                                                        "v1$atlas$snapshot$cluster$db$collection$date")
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.PROVIDER,
                                                        "dls:aws")
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.TRIM_LEVEL,
                                                        5),
                                                new Document()
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.STORE_NAME,
                                                        dlsAzureStoreName)
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.DATASET_PREFIX,
                                                        "v1$atlas$snapshot$cluster$db$collection$date")
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.PROVIDER,
                                                        "dls:azure")
                                                    .append(
                                                        DataLakeTestUtils.StorageConfigFieldDefs
                                                            .DataSource.TRIM_LEVEL,
                                                        5))))))));
  }

  private static void assertNdsStorageV0MatchingTest(final NDSDataLakeStorage pNdsStorage) {
    final NDSDataLakeS3Store s3Store1 = (NDSDataLakeS3Store) pNdsStorage.getStores().get(0);
    final NDSDataLakeS3Store s3Store2 = (NDSDataLakeS3Store) pNdsStorage.getStores().get(1);
    final NDSDataLakeS3Store s3Store3 = (NDSDataLakeS3Store) pNdsStorage.getStores().get(2);
    final NDSDataLakeS3Store s3Store4 = (NDSDataLakeS3Store) pNdsStorage.getStores().get(3);
    final NDSDataLakeHTTPStore httpStore1 = (NDSDataLakeHTTPStore) pNdsStorage.getStores().get(4);
    final NDSDataLakeHTTPStore httpStore2 = (NDSDataLakeHTTPStore) pNdsStorage.getStores().get(5);
    final NDSDataLakeAtlasStore atlasStore = (NDSDataLakeAtlasStore) pNdsStorage.getStores().get(6);
    final NDSDataLakeAtlasStore atlasStore2 =
        (NDSDataLakeAtlasStore) pNdsStorage.getStores().get(7);
    final NDSDataLakeOnlineArchiveStore oaStore =
        (NDSDataLakeOnlineArchiveStore) pNdsStorage.getStores().get(8);
    final NDSDataLakeAzureBlobStore azureBlobStore1 =
        (NDSDataLakeAzureBlobStore) pNdsStorage.getStores().get(9);
    final NDSDataLakeAzureBlobStore azureBlobStore2 =
        (NDSDataLakeAzureBlobStore) pNdsStorage.getStores().get(10);
    final NDSDataLakeDLSAWSStore dlsAWSStore =
        pNdsStorage.getStores().stream()
            .filter(s -> s.getProvider().equals(NDSDataLakeStoreProvider.DLS_AWS))
            .map(NDSDataLakeDLSAWSStore.class::cast)
            .findFirst()
            .orElseThrow();
    final NDSDataLakeDLSAzureStore dlsAzureStore =
        pNdsStorage.getStores().stream()
            .filter(s -> s.getProvider().equals(NDSDataLakeStoreProvider.DLS_AZURE))
            .map(NDSDataLakeDLSAzureStore.class::cast)
            .findFirst()
            .orElseThrow();
    final NDSDataLakeDatabase db1 = pNdsStorage.getDatabases().get(0);
    final TenantStorage.DataSource ds1 = db1.getCollections().get(0).getDataSources().get(0);
    final TenantStorage.DataSource ds2 = db1.getCollections().get(0).getDataSources().get(1);
    final NDSDataLakeDatabase db2 = pNdsStorage.getDatabases().get(1);
    final TenantStorage.DataSource ds3 = db2.getCollections().get(0).getDataSources().get(0);
    final TenantStorage.DataSource ds4 = db2.getCollections().get(0).getDataSources().get(1);
    final NDSDataLakeDatabase wildcardDb = pNdsStorage.getDatabases().get(2);
    final TenantStorage.DataSource ds5 = wildcardDb.getCollections().get(0).getDataSources().get(0);

    // http store
    assertEquals("http1", httpStore1.getName());
    assertTrue(httpStore1.getAllowInsecure());
    assertEquals(List.of("http://foo"), httpStore1.getUrls());

    assertEquals("http2", httpStore2.getName());
    assertNull(httpStore2.getAllowInsecure());
    assertEquals(List.of(), httpStore2.getUrls());

    // s3 store
    assertEquals("s3Store1", s3Store1.getName());
    assertEquals(AWSRegionName.US_WEST_1, s3Store1.getRegion());
    assertEquals("bucket1", s3Store1.getBucket());
    assertFalse(s3Store1.isPublic());
    assertEquals("x", s3Store1.getDelimiter());
    assertEquals("-", s3Store1.getReplacementDelimiter());
    assertEquals("mdb", s3Store1.getDocumentCountMetadataKey());
    assertNull(s3Store1.isIncludeTags());
    assertEquals("prefix", s3Store1.getPrefix());
    assertEquals(
        List.of(StorageClass.INTELLIGENT_TIERING, StorageClass.STANDARD),
        s3Store1.getAdditionalStorageClasses());

    assertEquals("s3Store2", s3Store2.getName());
    assertNull(s3Store2.getRegion());
    assertEquals("bucket2", s3Store2.getBucket());
    assertFalse(s3Store2.isPublic());
    assertNull(s3Store2.getDelimiter());
    assertNull(s3Store2.getReplacementDelimiter());
    assertNull(s3Store2.getDocumentCountMetadataKey());
    assertTrue(s3Store2.isIncludeTags());
    assertNull(s3Store2.getPrefix());
    assertNull(s3Store2.getAdditionalStorageClasses());

    assertEquals("sampleS3Store", s3Store3.getName());
    assertNull(s3Store3.getRegion());
    assertEquals(SAMPLE_BUCKET_NAME, s3Store3.getBucket());
    assertTrue(s3Store3.isPublic());
    assertNull(s3Store3.getDelimiter());
    assertNull(s3Store3.getReplacementDelimiter());
    assertNull(s3Store3.getDocumentCountMetadataKey());
    assertTrue(s3Store3.isIncludeTags());
    assertNull(s3Store3.getPrefix());
    assertNull(s3Store3.getAdditionalStorageClasses());

    assertEquals("publicS3Store", s3Store4.getName());
    assertEquals(AWSRegionName.US_EAST_1, s3Store4.getRegion());
    assertEquals("aws-public-blockchain", s3Store4.getBucket());
    assertTrue(s3Store4.isPublic());
    assertEquals("/", s3Store4.getDelimiter());
    assertNull(s3Store4.getReplacementDelimiter());
    assertNull(s3Store4.getDocumentCountMetadataKey());
    assertNull(s3Store4.isIncludeTags());
    assertNull(s3Store4.getPrefix());
    assertNull(s3Store4.getAdditionalStorageClasses());

    // atlas store
    assertEquals("atlas", atlasStore.getName());
    assertEquals(oid(3), atlasStore.getProjectId());
    assertEquals("clusterName", atlasStore.getClusterName());
    final NDSDataLakeAtlasStoreReadPreference readPreference = atlasStore.getReadPreference();
    assertEquals("primary", readPreference.getMode());
    assertEquals(Integer.valueOf(120), readPreference.getMaxStalenessSeconds());
    assertEquals(
        List.of(List.of(new NDSDataLakeAtlasStoreReadPreferenceTag("region", "US-WEST-1"))),
        readPreference.getTagSets());
    final NDSDataLakeAtlasStoreReadConcern readConcern = atlasStore.getReadConcern();
    assertEquals("local", readConcern.getLevel());

    assertEquals("atlas2", atlasStore2.getName());
    assertNull(atlasStore2.getProjectId());
    assertEquals("clusterName2", atlasStore2.getClusterName());

    // oa store
    assertEquals("oaStore", oaStore.getName());
    assertEquals(oid(5).toHexString(), oaStore.getProjectId());
    assertEquals(oid(6).toHexString(), oaStore.getClusterId());
    assertEquals("oaClusterName", oaStore.getClusterName());

    // azure blob store
    assertEquals("publicAzureBlobStore", azureBlobStore1.getName());
    assertEquals(
        "https://somestorageaccount.blob.core.windows.net", azureBlobStore1.getServiceUrl());
    assertEquals("public-container", azureBlobStore1.getContainerName());
    assertEquals(AzureRegionName.US_EAST_2, azureBlobStore1.getRegion());
    assertTrue(azureBlobStore1.isPublic());
    assertNull(azureBlobStore1.getPrefix());
    assertNull(azureBlobStore1.getDelimiter());
    assertNull(azureBlobStore1.getReplacementDelimiter());

    assertEquals("privateAzureBlobStore", azureBlobStore2.getName());
    assertEquals("https://subdomain.customdomain", azureBlobStore2.getServiceUrl());
    assertEquals("testContainer", azureBlobStore2.getContainerName());
    assertEquals(AzureRegionName.EUROPE_NORTH, azureBlobStore2.getRegion());
    assertFalse(azureBlobStore2.isPublic());
    assertEquals("somePrefix", azureBlobStore2.getPrefix());
    assertEquals("/", azureBlobStore2.getDelimiter());
    assertEquals("-", azureBlobStore2.getReplacementDelimiter());

    // dls AWS store
    assertEquals("dlsAWSStore", dlsAWSStore.getName());
    assertEquals(AWSRegionName.US_EAST_1, dlsAWSStore.getRegion());

    // dls Azure store
    assertEquals("dlsAzureStore", dlsAzureStore.getName());
    assertEquals(AzureRegionName.US_EAST_2, dlsAzureStore.getRegion());

    // db1
    assertEquals("db1", db1.getName());
    assertNull(db1.getMaxWildcardCollections());
    assertEquals("coll", db1.getCollections().get(0).getName());
    assertEquals("http1", ds1.getStoreName());
    assertEquals(List.of("http://foo"), ds1.getHttp().getUrlsList());
    assertEquals("s3Store1", ds2.getStoreName());
    assertEquals("a/b/c", ds2.getS3().getTemplate());
    assertTrue(isEmpty(ds2.getS3().getDefaultFormat()));
    assertFalse(ds2.getS3().getOmitAttributes());
    // db2
    assertEquals("db2", db2.getName());
    assertEquals(100, (long) db2.getMaxWildcardCollections());
    assertEquals("coll2", db2.getCollections().get(0).getName());
    assertEquals("atlas", ds3.getStoreName());
    assertEquals("d", ds3.getAtlas().getDatabaseName());
    assertEquals("c", ds3.getAtlas().getCollectionName());
    assertTrue(isEmpty(ds3.getAtlas().getCollectionRegex()));

    assertEquals("atlas2", ds4.getStoreName());
    assertEquals("d", ds4.getAtlas().getDatabaseName());
    assertTrue(isEmpty(ds4.getAtlas().getCollectionName()));
    assertEquals("r", ds4.getAtlas().getCollectionRegex());

    // wildcard db
    assertEquals("*", wildcardDb.getName());
    assertEquals("*", wildcardDb.getCollections().get(0).getName());
    assertEquals("atlas", ds5.getStoreName());
    assertTrue(isEmpty((ds5.getAtlas().getDatabaseName())));
    assertTrue(isEmpty((ds5.getAtlas().getCollectionName())));
    assertTrue(isEmpty((ds5.getAtlas().getCollectionRegex())));

    // oa data source
    final TenantStorage.DataSource oaSource =
        pNdsStorage.getDatabases().stream()
            .filter(d -> d.getName().equals("db3"))
            .findFirst()
            .stream()
            .flatMap(d -> d.getCollections().stream())
            .filter(c -> c.getName().equals("coll3"))
            .flatMap(c -> c.getDataSources().stream())
            .filter(s -> s.getStoreName().equals("oaStore"))
            .findFirst()
            .orElseThrow();

    assertEquals(oid(100).toHexString(), oaSource.getOnlineArchive().getDatasetName());
    assertEquals("aws", oaSource.getOnlineArchive().getMetadataLocation().getProvider());
    assertEquals("us-east-1", oaSource.getOnlineArchive().getMetadataLocation().getRegion());

    // dls AWS data source
    final TenantStorage.DataSource dlsAWSSource =
        pNdsStorage.getDatabases().stream()
            .flatMap(d -> d.getCollections().stream())
            .flatMap(c -> c.getDataSources().stream())
            .filter(DataSource::hasDlsaws)
            .findFirst()
            .orElseThrow();

    assertEquals("dlsAWSStore", dlsAWSStore.getName());
    assertEquals(
        "v1$atlas$snapshot$cluster$db$collection$date",
        dlsAWSSource.getDlsaws().getDatasetPrefix());
    assertEquals(5, dlsAWSSource.getDlsaws().getTrimLevel().getValue());

    // dls Azure data source
    final TenantStorage.DataSource dlsAzureSource =
        pNdsStorage.getDatabases().stream()
            .flatMap(d -> d.getCollections().stream())
            .flatMap(c -> c.getDataSources().stream())
            .filter(DataSource::hasDls)
            .findFirst()
            .orElseThrow();

    assertEquals("dlsAzureStore", dlsAzureStore.getName());
    assertEquals(
        "v1$atlas$snapshot$cluster$db$collection$date", dlsAzureSource.getDls().getDatasetPrefix());
    assertEquals(5, dlsAzureSource.getDls().getTrimLevel().getValue());
  }

  private static void assertNDSStorageV1ViewMatchingTest(NDSDataLakeStorageV1View pStorageView) {
    final Document config = pStorageView.getConfig();
    assertStorageConfigDocumentMatchingTest(config);
  }

  private static void assertAdlStorageMatchingTest(final Storage pAdlStorage) {
    final DocumentCodec dc = new DocumentCodec();
    final ByteString rawConfig = pAdlStorage.getConfig().getData();
    final Document config =
        dc.decode(
            new BsonBinaryReader(rawConfig.asReadOnlyByteBuffer()),
            DecoderContext.builder().build());
    assertStorageConfigDocumentMatchingTest(config);
  }

  private static void assertStorageConfigDocumentMatchingTest(Document pConfig) {
    final Document config = pConfig.get(NDSDataLakeStorageV1View.FieldDefs.CONFIG, Document.class);
    final Document s3Store1 =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).get(0);
    final Document s3Store2 =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).get(1);
    final Document s3Store3 =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).get(2);
    final Document httpStore1 =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).get(3);
    final Document httpStore2 =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).get(4);
    final Document atlasStore =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).get(5);
    final Document atlasStore2 =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).get(6);
    final Document oaStore =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).get(7);
    final Document azureStore1 =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).get(8);
    final Document azureStore2 =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).get(9);
    final Document dlsAWSStore =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).stream()
            .filter(
                (doc) ->
                    doc.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER)
                        .equals("dls:aws"))
            .findFirst()
            .orElseThrow();
    final Document dlsAzureStore =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.STORES, Document.class).stream()
            .filter(
                (doc) ->
                    doc.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER)
                        .equals("dls:azure"))
            .findFirst()
            .orElseThrow();

    final Document db1 =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.DATABASES, Document.class).get(0);
    final Document ds1 =
        db1.getList(DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS, Document.class)
            .get(0)
            .getList(
                DataLakeTestUtils.StorageConfigFieldDefs.Collection.DATA_SOURCES, Document.class)
            .get(0);
    final Document ds2 =
        db1.getList(DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS, Document.class)
            .get(0)
            .getList(
                DataLakeTestUtils.StorageConfigFieldDefs.Collection.DATA_SOURCES, Document.class)
            .get(1);
    final Document db2 =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.DATABASES, Document.class).get(1);
    final Document ds3 =
        db2.getList(DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS, Document.class)
            .get(0)
            .getList(
                DataLakeTestUtils.StorageConfigFieldDefs.Collection.DATA_SOURCES, Document.class)
            .get(0);
    final Document ds4 =
        db2.getList(DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS, Document.class)
            .get(0)
            .getList(
                DataLakeTestUtils.StorageConfigFieldDefs.Collection.DATA_SOURCES, Document.class)
            .get(1);
    final Document wildcardDb =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.DATABASES, Document.class).get(2);
    final Document ds5 =
        wildcardDb
            .getList(DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS, Document.class)
            .get(0)
            .getList(
                DataLakeTestUtils.StorageConfigFieldDefs.Collection.DATA_SOURCES, Document.class)
            .get(0);

    // http store
    assertEquals(
        "http1", httpStore1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertEquals(
        "http", httpStore1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER));
    assertTrue(
        httpStore1.getBoolean(DataLakeTestUtils.StorageConfigFieldDefs.Store.ALLOW_INSECURE));
    assertEquals(
        "http://foo",
        httpStore1
            .getList(DataLakeTestUtils.StorageConfigFieldDefs.Store.URLS, String.class)
            .get(0));
    assertNull(httpStore1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.DEFAULT_FORMAT));

    assertEquals(
        "http2", httpStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertEquals(
        "http", httpStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PROVIDER));
    assertNull(
        httpStore2.getBoolean(DataLakeTestUtils.StorageConfigFieldDefs.Store.ALLOW_INSECURE));
    assertNull(
        httpStore2.getList(DataLakeTestUtils.StorageConfigFieldDefs.Store.URLS, String.class));
    assertEquals(
        ".json",
        httpStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.DEFAULT_FORMAT));

    // s3 store
    assertEquals(
        "s3Store1", s3Store1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertEquals(
        "us-west-1", s3Store1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION));
    assertEquals(
        "bucket1", s3Store1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.BUCKET));
    assertNull(s3Store1.getBoolean(DataLakeTestUtils.StorageConfigFieldDefs.Store.PUBLIC));
    assertEquals("x", s3Store1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.DELIMITER));
    assertNull(s3Store1.getBoolean(DataLakeTestUtils.StorageConfigFieldDefs.Store.INCLUDE_TAGS));
    assertEquals(
        "-",
        s3Store1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.REPLACEMENT_DELIMITER));
    assertEquals(
        "mdb",
        s3Store1.getString(
            DataLakeTestUtils.StorageConfigFieldDefs.Store.DOCUMENT_COUNT_METADATA_KEY));
    assertEquals(
        "prefix", s3Store1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PREFIX));
    assertEquals(
        List.of("INTELLIGENT_TIERING", "STANDARD"),
        s3Store1.getList(
            DataLakeTestUtils.StorageConfigFieldDefs.Store.ADDITIONAL_STORAGE_CLASSES,
            String.class));

    assertEquals(
        "s3Store2", s3Store2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertEquals(
        "us-west-1", s3Store2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION));
    assertEquals(
        "bucket2", s3Store2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.BUCKET));
    assertNull(s3Store2.getBoolean(DataLakeTestUtils.StorageConfigFieldDefs.Store.PUBLIC));
    assertEquals("", s3Store2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.DELIMITER));
    assertTrue(s3Store2.getBoolean(DataLakeTestUtils.StorageConfigFieldDefs.Store.INCLUDE_TAGS));
    assertEquals(
        "",
        s3Store2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.REPLACEMENT_DELIMITER));
    assertEquals(
        "",
        s3Store2.getString(
            DataLakeTestUtils.StorageConfigFieldDefs.Store.DOCUMENT_COUNT_METADATA_KEY));
    assertEquals("", s3Store2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PREFIX));
    assertNull(
        s3Store2.getList(
            DataLakeTestUtils.StorageConfigFieldDefs.Store.ADDITIONAL_STORAGE_CLASSES,
            String.class));

    assertEquals(
        "publicS3Store", s3Store3.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertEquals(
        "us-west-1", s3Store3.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION));
    assertEquals(
        SAMPLE_BUCKET_NAME,
        s3Store3.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.BUCKET));
    assertTrue(s3Store3.getBoolean(DataLakeTestUtils.StorageConfigFieldDefs.Store.PUBLIC));
    assertEquals("", s3Store3.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.DELIMITER));
    assertTrue(s3Store3.getBoolean(DataLakeTestUtils.StorageConfigFieldDefs.Store.INCLUDE_TAGS));
    assertEquals(
        "",
        s3Store3.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.REPLACEMENT_DELIMITER));
    assertEquals(
        "",
        s3Store3.getString(
            DataLakeTestUtils.StorageConfigFieldDefs.Store.DOCUMENT_COUNT_METADATA_KEY));
    assertEquals("", s3Store3.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PREFIX));
    assertNull(
        s3Store3.getList(
            DataLakeTestUtils.StorageConfigFieldDefs.Store.ADDITIONAL_STORAGE_CLASSES,
            String.class));

    // atlas store
    assertEquals(
        "atlas", atlasStore.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertEquals(
        oid(3).toHexString(),
        atlasStore.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PROJECT_ID));
    assertEquals(
        "clusterName",
        atlasStore.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.CLUSTER_NAME));
    assertEquals(
        100,
        atlasStore
            .get(DataLakeTestUtils.StorageConfigFieldDefs.Store.READ_PREFERENCE, Document.class)
            .getInteger("maxStalenessSeconds"));
    assertEquals(
        "secondary",
        atlasStore
            .get(DataLakeTestUtils.StorageConfigFieldDefs.Store.READ_PREFERENCE, Document.class)
            .getString("mode"));

    final Document tagSet =
        (Document)
            atlasStore
                .get(DataLakeTestUtils.StorageConfigFieldDefs.Store.READ_PREFERENCE, Document.class)
                .getList("tagSets", List.class)
                .get(0)
                .get(0);
    assertEquals("provider", tagSet.getString("name"));
    assertEquals("AWS", tagSet.getString("value"));
    assertEquals(
        "local",
        atlasStore
            .get(DataLakeTestUtils.StorageConfigFieldDefs.Store.READ_CONCERN, Document.class)
            .getString("level"));

    assertEquals(
        "atlas2", atlasStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertNull(atlasStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PROJECT_ID));
    assertEquals(
        "clusterName2",
        atlasStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.CLUSTER_NAME));

    // oa store
    assertEquals("oaStore", oaStore.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertEquals(
        oid(3).toHexString(),
        oaStore.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PROJECT_ID));
    assertEquals(
        oid(4).toHexString(),
        oaStore.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.CLUSTER_ID));
    assertEquals(
        "oaClusterName",
        oaStore.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.CLUSTER_NAME));

    // azure blob store
    assertEquals(
        "publicAzureBlobStore",
        azureStore1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertEquals(
        "https://somestorageaccount.blob.core.windows.net",
        azureStore1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.SERVICE_URL));
    assertEquals(
        "public-container",
        azureStore1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.CONTAINER_NAME));
    assertEquals(
        "eastus2", azureStore1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION));
    assertTrue(azureStore1.getBoolean(DataLakeTestUtils.StorageConfigFieldDefs.Store.PUBLIC));
    assertEquals("", azureStore1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PREFIX));
    assertEquals(
        "", azureStore1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.DELIMITER));
    assertEquals(
        "",
        azureStore1.getString(
            DataLakeTestUtils.StorageConfigFieldDefs.Store.REPLACEMENT_DELIMITER));

    assertEquals(
        "privateAzureBlobStore",
        azureStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertEquals(
        "https://subdomain.customdomain",
        azureStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.SERVICE_URL));
    assertEquals(
        "test-container",
        azureStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.CONTAINER_NAME));
    assertEquals(
        "northeurope",
        azureStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION));
    assertFalse(azureStore2.getBoolean(DataLakeTestUtils.StorageConfigFieldDefs.Store.PUBLIC));
    assertEquals(
        "somePrefix", azureStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.PREFIX));
    assertEquals(
        "/", azureStore2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.DELIMITER));
    assertEquals(
        "-",
        azureStore2.getString(
            DataLakeTestUtils.StorageConfigFieldDefs.Store.REPLACEMENT_DELIMITER));

    // dls AWS store
    assertEquals(
        "dlsAWSStore", dlsAWSStore.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertEquals(
        "us-east-1", dlsAWSStore.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION));

    // dls Azure store
    assertEquals(
        "dlsAzureStore",
        dlsAzureStore.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.NAME));
    assertEquals(
        "eastus2", dlsAzureStore.getString(DataLakeTestUtils.StorageConfigFieldDefs.Store.REGION));

    // db1
    assertEquals("db1", db1.getString(DataLakeTestUtils.StorageConfigFieldDefs.Database.NAME));
    assertEquals(
        "coll",
        db1.getList(DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS, Document.class)
            .get(0)
            .getString(DataLakeTestUtils.StorageConfigFieldDefs.Collection.NAME));
    assertEquals(
        "http1", ds1.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.STORE_NAME));
    assertEquals(
        "http://foo",
        ds1.getList(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.URLS, String.class).get(0));
    assertEquals(
        "s3Store1", ds2.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.STORE_NAME));
    assertEquals("a/b/c", ds2.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.PATH));
    assertEquals(
        "", ds2.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.DEFAULT_FORMAT));

    // db2
    assertEquals("db2", db2.getString(DataLakeTestUtils.StorageConfigFieldDefs.Database.NAME));
    assertEquals(
        "coll2",
        db2.getList(DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS, Document.class)
            .get(0)
            .getString(DataLakeTestUtils.StorageConfigFieldDefs.Collection.NAME));
    assertEquals(
        "atlas", ds3.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.STORE_NAME));
    assertEquals("d", ds3.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.DATABASE));
    assertEquals(
        "c", ds3.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.COLLECTION));
    assertNull(ds3.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.COLLECTION_REGEX));
    assertEquals(
        "atlas2", ds4.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.STORE_NAME));
    assertEquals("d", ds4.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.DATABASE));
    assertNull(ds4.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.COLLECTION));
    assertEquals(
        "r", ds4.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.COLLECTION_REGEX));

    // wildcard db
    assertEquals("*", wildcardDb.getString(DataLakeTestUtils.StorageConfigFieldDefs.Database.NAME));
    assertEquals(
        "*",
        wildcardDb
            .getList(DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS, Document.class)
            .get(0)
            .getString(DataLakeTestUtils.StorageConfigFieldDefs.Collection.NAME));
    assertEquals(
        "atlas", ds5.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.STORE_NAME));
    assertNull(ds5.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.DATABASE));
    assertNull(ds5.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.COLLECTION));
    assertNull(ds5.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.COLLECTION_REGEX));

    // oa data source
    final Document oaSource =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.DATABASES, Document.class).stream()
            .filter(
                d ->
                    d.getString(DataLakeTestUtils.StorageConfigFieldDefs.Database.NAME)
                        .equals("db3"))
            .findFirst()
            .stream()
            .flatMap(
                d ->
                    d
                        .getList(
                            DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS,
                            Document.class)
                        .stream())
            .filter(
                c ->
                    c.getString(DataLakeTestUtils.StorageConfigFieldDefs.Collection.NAME)
                        .equals("coll3"))
            .flatMap(
                c ->
                    c
                        .getList(
                            DataLakeTestUtils.StorageConfigFieldDefs.Collection.DATA_SOURCES,
                            Document.class)
                        .stream())
            .filter(
                s ->
                    s.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.STORE_NAME)
                        .equals("oaStore"))
            .findFirst()
            .orElseThrow();
    assertEquals(
        oid(100).toHexString(),
        oaSource.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.DATASET_NAME));
    assertEquals(
        "aws",
        oaSource
            .get(
                DataLakeTestUtils.StorageConfigFieldDefs.DataSource.METADATA_LOCATION,
                Document.class)
            .getString("provider"));
    assertEquals(
        "us-east-1",
        oaSource
            .get(
                DataLakeTestUtils.StorageConfigFieldDefs.DataSource.METADATA_LOCATION,
                Document.class)
            .getString("region"));

    // dls AWS data source
    final Document awsDataSource =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.DATABASES, Document.class).stream()
            .flatMap(
                d ->
                    d
                        .getList(
                            DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS,
                            Document.class)
                        .stream())
            .flatMap(
                c ->
                    c
                        .getList(
                            DataLakeTestUtils.StorageConfigFieldDefs.Collection.DATA_SOURCES,
                            Document.class)
                        .stream())
            .filter(
                s ->
                    Objects.nonNull(
                        s.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.PROVIDER)))
            // figure out what this is supposed to be under cause confusion
            .filter(
                s ->
                    s.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.PROVIDER)
                        .equals("dls:aws"))
            .findFirst()
            .orElseThrow();
    assertEquals(
        "dlsAWSStore",
        awsDataSource.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.STORE_NAME));
    assertEquals(
        "v1$atlas$snapshot$cluster$db$collection$date",
        awsDataSource.getString(
            DataLakeTestUtils.StorageConfigFieldDefs.DataSource.DATASET_PREFIX));
    assertEquals(
        5,
        awsDataSource.getInteger(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.TRIM_LEVEL));

    // dls Azure data source
    final Document azureDataSource =
        config.getList(DataLakeTestUtils.StorageConfigFieldDefs.DATABASES, Document.class).stream()
            .flatMap(
                d ->
                    d
                        .getList(
                            DataLakeTestUtils.StorageConfigFieldDefs.Database.COLLECTIONS,
                            Document.class)
                        .stream())
            .flatMap(
                c ->
                    c
                        .getList(
                            DataLakeTestUtils.StorageConfigFieldDefs.Collection.DATA_SOURCES,
                            Document.class)
                        .stream())
            .filter(
                s ->
                    Objects.nonNull(
                        s.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.PROVIDER)))
            .filter(
                s ->
                    s.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.PROVIDER)
                        .equals("dls:azure"))
            .findFirst()
            .orElseThrow();
    assertEquals(
        "dlsAzureStore",
        azureDataSource.getString(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.STORE_NAME));
    assertEquals(
        "v1$atlas$snapshot$cluster$db$collection$date",
        azureDataSource.getString(
            DataLakeTestUtils.StorageConfigFieldDefs.DataSource.DATASET_PREFIX));
    assertEquals(
        5,
        azureDataSource.getInteger(DataLakeTestUtils.StorageConfigFieldDefs.DataSource.TRIM_LEVEL));
  }

  private static NDSDataLakeStorage getTestNDSStorage() {
    final String oaStoreName = "oaStore";
    final String dlsAWSStoreName = "dlsAWSStore";
    final String dlsAzureStoreName = "dlsAzureStore";
    final ObjectId archiveId = oid(100);

    return NDSDataLakeStorage.builder()
        .stores(
            List.of(
                NDSDataLakeS3Store.builder()
                    .region(AWSRegionName.US_WEST_1)
                    .bucket("bucket1")
                    .delimiter("x")
                    .replacementDelimiter("-")
                    .documentCountMetadataKey("mdb")
                    .prefix("prefix")
                    .includeTags(null)
                    .additionalStorageClasses(
                        List.of(StorageClass.INTELLIGENT_TIERING, StorageClass.STANDARD))
                    .name("s3Store1")
                    .build(),
                NDSDataLakeS3Store.builder()
                    .region(AWSRegionName.US_WEST_1)
                    .bucket("bucket2")
                    .delimiter("")
                    .replacementDelimiter("")
                    .documentCountMetadataKey("")
                    .prefix("")
                    .includeTags(true)
                    .name("s3Store2")
                    .build(),
                NDSDataLakeS3Store.builder()
                    .region(AWSRegionName.US_WEST_1)
                    .bucket(SAMPLE_BUCKET_NAME)
                    .delimiter("")
                    .replacementDelimiter("")
                    .documentCountMetadataKey("")
                    .prefix("")
                    .includeTags(true)
                    .name("publicS3Store")
                    .build(),
                NDSDataLakeHTTPStore.builder()
                    .urls(List.of("http://foo"))
                    .allowInsecure(true)
                    .defaultFormat(null)
                    .name("http1")
                    .build(),
                NDSDataLakeHTTPStore.builder()
                    .urls(null)
                    .allowInsecure(null)
                    .defaultFormat(".json")
                    .name("http2")
                    .build(),
                NDSDataLakeAtlasStore.builder()
                    .readConcern(new NDSDataLakeAtlasStoreReadConcern("local"))
                    .readPreference(
                        new NDSDataLakeAtlasStoreReadPreference(
                            "secondary",
                            List.of(
                                List.of(
                                    new NDSDataLakeAtlasStoreReadPreferenceTag("provider", "AWS"))),
                            100))
                    .projectId(oid(3))
                    .clusterName("clusterName")
                    .name("atlas")
                    .build(),
                NDSDataLakeAtlasStore.builder()
                    .projectId(null)
                    .clusterName("clusterName2")
                    .name("atlas2")
                    .build(),
                NDSDataLakeOnlineArchiveStore.builder()
                    .projectId(oid(3).toHexString())
                    .clusterId(oid(4).toHexString())
                    .clusterName("oaClusterName")
                    .name(oaStoreName)
                    .build(),
                NDSDataLakeAzureBlobStore.builder()
                    .region(AzureRegionName.US_EAST_2)
                    .serviceUrl("https://somestorageaccount.blob.core.windows.net")
                    .containerName("public-container")
                    .prefix("")
                    .delimiter("")
                    .replacementDelimiter("")
                    .setPublic(true)
                    .name("publicAzureBlobStore")
                    .build(),
                NDSDataLakeAzureBlobStore.builder()
                    .region(AzureRegionName.EUROPE_NORTH)
                    .serviceUrl("https://subdomain.customdomain")
                    .containerName("testContainer")
                    .prefix("somePrefix")
                    .delimiter("/")
                    .replacementDelimiter("-")
                    .setPublic(false)
                    .name("privateAzureBlobStore")
                    .build(),
                NDSDataLakeDLSAWSStore.builder()
                    .region(AWSRegionName.US_EAST_1)
                    .name(dlsAWSStoreName)
                    .build(),
                NDSDataLakeDLSAzureStore.builder()
                    .region(AzureRegionName.US_EAST_2)
                    .name(dlsAzureStoreName)
                    .build()))
        .databases(
            List.of(
                NDSDataLakeDatabase.builder()
                    .name("db1")
                    .collections(
                        List.of(
                            NDSDataLakeCollection.builder()
                                .name("coll")
                                .dataSources(
                                    List.of(
                                        DataSource.newBuilder()
                                            .setStoreName("http1")
                                            .setHttp(
                                                HTTPDataSource.newBuilder().addUrls("http://foo"))
                                            .build(),
                                        DataSource.newBuilder()
                                            .setStoreName("s3Store1")
                                            .setS3(S3DataSource.newBuilder().setTemplate("a/b/c"))
                                            .build()))
                                .build()))
                    .build(),
                NDSDataLakeDatabase.builder()
                    .name("db2")
                    .collections(
                        List.of(
                            NDSDataLakeCollection.builder()
                                .name("coll2")
                                .dataSources(
                                    List.of(
                                        DataSource.newBuilder()
                                            .setStoreName("atlas")
                                            .setAtlas(
                                                AtlasDataSource.newBuilder()
                                                    .setDatabaseName("d")
                                                    .setCollectionName("c"))
                                            .build(),
                                        DataSource.newBuilder()
                                            .setStoreName("atlas2")
                                            .setAtlas(
                                                AtlasDataSource.newBuilder()
                                                    .setDatabaseName("d")
                                                    .setCollectionRegex("r"))
                                            .build()))
                                .build()))
                    .build(),
                NDSDataLakeDatabase.builder()
                    .name("*")
                    .collections(
                        List.of(
                            NDSDataLakeCollection.builder()
                                .name("*")
                                .dataSources(
                                    List.of(
                                        DataSource.newBuilder()
                                            .setStoreName("atlas")
                                            .setAtlas(AtlasDataSource.newBuilder())
                                            .build()))
                                .build()))
                    .build(),
                NDSDataLakeDatabase.builder()
                    .name("db3")
                    .collections(
                        List.of(
                            NDSDataLakeCollection.builder()
                                .name("coll3")
                                .dataSources(
                                    List.of(
                                        DataSource.newBuilder()
                                            .setStoreName(oaStoreName)
                                            .setOnlineArchive(
                                                DLSDataSource.newBuilder()
                                                    .setDatasetName(archiveId.toHexString())
                                                    .setMetadataLocation(
                                                        ProviderRegion.newBuilder()
                                                            .setProvider("aws")
                                                            .setRegion("us-east-1")))
                                            .build()))
                                .build()))
                    .build(),
                NDSDataLakeDatabase.builder()
                    .name("db4")
                    .collections(
                        List.of(
                            NDSDataLakeCollection.builder()
                                .name("coll4")
                                .dataSources(
                                    List.of(
                                        DataSource.newBuilder()
                                            .setStoreName(dlsAWSStoreName)
                                            .setDlsaws(
                                                DLSDataSourceV2.newBuilder()
                                                    .setDatasetPrefix(
                                                        "v1$atlas$snapshot$cluster$db$collection$date")
                                                    .setTrimLevel(Int32Value.of(5)))
                                            .build(),
                                        DataSource.newBuilder()
                                            .setStoreName(dlsAzureStoreName)
                                            .setDls(
                                                DLSDataSourceV2.newBuilder()
                                                    .setDatasetPrefix(
                                                        "v1$atlas$snapshot$cluster$db$collection$date")
                                                    .setTrimLevel(Int32Value.of(5)))
                                            .build()))
                                .build()))
                    .build()))
        .build();
  }

  private static void assertAdlStorageV0MatchingTest(final TenantStorage.Storage pAdlStorage) {
    final TenantStorage.Store s3Store1 = pAdlStorage.getStores(0);
    final TenantStorage.Store s3Store2 = pAdlStorage.getStores(1);
    final TenantStorage.Store s3Store3 = pAdlStorage.getStores(2);
    final TenantStorage.Store httpStore1 = pAdlStorage.getStores(3);
    final TenantStorage.Store httpStore2 = pAdlStorage.getStores(4);
    final TenantStorage.Store atlasStore = pAdlStorage.getStores(5);
    final TenantStorage.Store atlasStore2 = pAdlStorage.getStores(6);
    final TenantStorage.Store oaStore = pAdlStorage.getStores(7);
    final TenantStorage.Store azureStore1 = pAdlStorage.getStores(8);
    final TenantStorage.Store azureStore2 = pAdlStorage.getStores(9);
    final TenantStorage.Store dlsAWSStore =
        pAdlStorage.getStoresList().stream().filter(Store::hasDlsaws).findFirst().orElseThrow();
    final TenantStorage.Store dlsAzureStore =
        pAdlStorage.getStoresList().stream().filter(Store::hasDlsAzure).findFirst().orElseThrow();
    final TenantStorage.Database db1 = pAdlStorage.getDatabases(0);
    final TenantStorage.DataSource ds1 = db1.getCollections(0).getDataSources(0);
    final TenantStorage.DataSource ds2 = db1.getCollections(0).getDataSources(1);
    final TenantStorage.Database db2 = pAdlStorage.getDatabases(1);
    final TenantStorage.DataSource ds3 = db2.getCollections(0).getDataSources(0);
    final TenantStorage.DataSource ds4 = db2.getCollections(0).getDataSources(1);
    final TenantStorage.Database wildcardDb = pAdlStorage.getDatabases(2);
    final TenantStorage.DataSource ds5 = wildcardDb.getCollections(0).getDataSources(0);

    // http store
    assertEquals("http1", httpStore1.getName());
    assertTrue(httpStore1.hasHttp());
    assertEquals(TenantStorage.Insecure.ALLOW, httpStore1.getHttp().getInsecure());
    assertEquals("http://foo", httpStore1.getHttp().getUrls(0));
    assertEquals("", httpStore1.getHttp().getDefaultFormat());

    assertEquals("http2", httpStore2.getName());
    assertTrue(httpStore2.hasHttp());
    assertEquals(TenantStorage.Insecure.UNDEFINED, httpStore2.getHttp().getInsecure());
    assertEquals(List.of(), httpStore2.getHttp().getUrlsList());
    assertEquals(".json", httpStore2.getHttp().getDefaultFormat());

    // s3 store
    assertEquals("s3Store1", s3Store1.getName());
    assertEquals("us-west-1", s3Store1.getS3().getDataRegion());
    assertEquals("bucket1", s3Store1.getS3().getBucket());
    assertFalse(s3Store1.getS3().getPublic());
    assertEquals("x", s3Store1.getS3().getDelimiter());
    assertFalse(s3Store1.getS3().getIncludeTags());
    assertEquals("-", s3Store1.getS3().getReplacementDelimiter());
    assertEquals("mdb", s3Store1.getS3().getDocumentCountMetadataKey());
    assertEquals("prefix", s3Store1.getS3().getPrefix());
    assertEquals(
        List.of("INTELLIGENT_TIERING", "STANDARD"),
        s3Store1.getS3().getAdditionalStorageClassesList());

    assertEquals("s3Store2", s3Store2.getName());
    assertEquals("us-west-1", s3Store2.getS3().getDataRegion());
    assertEquals("bucket2", s3Store2.getS3().getBucket());
    assertFalse(s3Store2.getS3().getPublic());
    assertEquals("", s3Store2.getS3().getDelimiter());
    assertTrue(s3Store2.getS3().getIncludeTags());
    assertEquals("", s3Store2.getS3().getReplacementDelimiter());
    assertEquals("", s3Store2.getS3().getDocumentCountMetadataKey());
    assertEquals("", s3Store2.getS3().getPrefix());
    assertEquals(List.of(), s3Store2.getS3().getAdditionalStorageClassesList());

    assertEquals("publicS3Store", s3Store3.getName());
    assertEquals("us-west-1", s3Store3.getS3().getDataRegion());
    assertEquals(SAMPLE_BUCKET_NAME, s3Store3.getS3().getBucket());
    assertTrue(s3Store3.getS3().getPublic());
    assertEquals("", s3Store3.getS3().getDelimiter());
    assertTrue(s3Store3.getS3().getIncludeTags());
    assertEquals("", s3Store3.getS3().getReplacementDelimiter());
    assertEquals("", s3Store3.getS3().getDocumentCountMetadataKey());
    assertEquals("", s3Store3.getS3().getPrefix());
    assertEquals(List.of(), s3Store3.getS3().getAdditionalStorageClassesList());

    // atlas store
    assertEquals("atlas", atlasStore.getName());
    assertEquals(oid(3).toHexString(), atlasStore.getAtlas().getProjectId());
    assertEquals("clusterName", atlasStore.getAtlas().getClusterName());
    assertEquals(
        100, atlasStore.getAtlas().getReadPreference().getMaxStalenessSeconds().getValue());
    assertEquals("SECONDARY", atlasStore.getAtlas().getReadPreference().getMode().toString());
    assertEquals(
        "provider",
        atlasStore.getAtlas().getReadPreference().getTagSetsList().get(0).getTagSet(0).getName());
    assertEquals(
        "AWS",
        atlasStore.getAtlas().getReadPreference().getTagSetsList().get(0).getTagSet(0).getValue());
    assertEquals("LOCAL", atlasStore.getAtlas().getReadConcern().getLevel().toString());

    assertEquals("atlas2", atlasStore2.getName());
    assertEquals("", atlasStore2.getAtlas().getProjectId());
    assertEquals("clusterName2", atlasStore2.getAtlas().getClusterName());

    // oa store
    assertEquals("oaStore", oaStore.getName());
    assertEquals(oid(3).toHexString(), oaStore.getOnlineArchive().getProjectId());
    assertEquals(oid(4).toHexString(), oaStore.getOnlineArchive().getClusterId());
    assertEquals("oaClusterName", oaStore.getOnlineArchive().getClusterName());

    // azure blob store
    assertEquals("publicAzureBlobStore", azureStore1.getName());
    assertEquals(
        "https://somestorageaccount.blob.core.windows.net", azureStore1.getAzure().getServiceUrl());
    assertEquals("public-container", azureStore1.getAzure().getContainerName());
    assertEquals("eastus2", azureStore1.getAzure().getDataRegion());
    assertTrue(azureStore1.getAzure().getPublic());
    assertEquals("", azureStore1.getAzure().getPrefix());
    assertEquals("", azureStore1.getAzure().getDelimiter());
    assertEquals("", azureStore1.getAzure().getReplacementDelimiter());

    assertEquals("privateAzureBlobStore", azureStore2.getName());
    assertEquals("https://subdomain.customdomain", azureStore2.getAzure().getServiceUrl());
    assertEquals("testContainer", azureStore2.getAzure().getContainerName());
    assertEquals("northeurope", azureStore2.getAzure().getDataRegion());
    assertFalse(azureStore2.getAzure().getPublic());
    assertEquals("somePrefix", azureStore2.getAzure().getPrefix());
    assertEquals("/", azureStore2.getAzure().getDelimiter());
    assertEquals("-", azureStore2.getAzure().getReplacementDelimiter());

    // dls AWS store
    assertEquals("dlsAWSStore", dlsAWSStore.getName());
    assertEquals("us-east-1", dlsAWSStore.getDlsaws().getDataRegion());

    // dls Azure store
    assertEquals("dlsAzureStore", dlsAzureStore.getName());
    assertEquals("eastus2", dlsAzureStore.getDlsAzure().getDataRegion());

    // db1
    assertEquals("db1", db1.getName());
    assertEquals("coll", db1.getCollections(0).getName());
    assertTrue(ds1.hasHttp());
    assertEquals("http1", ds1.getStoreName());
    assertEquals("http://foo", ds1.getHttp().getUrls(0));
    assertTrue(ds2.hasS3());
    assertEquals("s3Store1", ds2.getStoreName());
    assertEquals("a/b/c", ds2.getS3().getTemplate());
    assertEquals("", ds2.getS3().getDefaultFormat());
    assertFalse(ds2.getS3().getOmitAttributes());

    // db2
    assertEquals("db2", db2.getName());
    assertEquals("coll2", db2.getCollections(0).getName());
    assertTrue(ds3.hasAtlas());
    assertEquals("atlas", ds3.getStoreName());
    assertEquals("d", ds3.getAtlas().getDatabaseName());
    assertEquals("c", ds3.getAtlas().getCollectionName());
    assertEquals("", ds3.getAtlas().getCollectionRegex());
    assertTrue(ds4.hasAtlas());
    assertEquals("atlas2", ds4.getStoreName());
    assertEquals("d", ds4.getAtlas().getDatabaseName());
    assertEquals("", ds4.getAtlas().getCollectionName());
    assertEquals("r", ds4.getAtlas().getCollectionRegex());

    // wildcard db
    assertEquals("*", wildcardDb.getName());
    assertEquals("*", wildcardDb.getCollections(0).getName());
    assertEquals("atlas", ds5.getStoreName());
    assertEquals("", ds5.getAtlas().getDatabaseName());
    assertEquals("", ds5.getAtlas().getCollectionName());
    assertEquals("", ds5.getAtlas().getCollectionRegex());

    // oa data source
    final TenantStorage.DataSource oaSource =
        pAdlStorage.getDatabasesList().stream()
            .filter(d -> d.getName().equals("db3"))
            .findFirst()
            .stream()
            .flatMap(d -> d.getCollectionsList().stream())
            .filter(c -> c.getName().equals("coll3"))
            .flatMap(c -> c.getDataSourcesList().stream())
            .filter(s -> s.getStoreName().equals("oaStore"))
            .findFirst()
            .orElseThrow();
    assertEquals(oid(100).toHexString(), oaSource.getOnlineArchive().getDatasetName());
    assertEquals("aws", oaSource.getOnlineArchive().getMetadataLocation().getProvider());
    assertEquals("us-east-1", oaSource.getOnlineArchive().getMetadataLocation().getRegion());

    // dls AWS data source
    {
      final TenantStorage.DataSource awsDataSource =
          pAdlStorage.getDatabasesList().stream()
              .flatMap(d -> d.getCollectionsList().stream())
              .flatMap(c -> c.getDataSourcesList().stream())
              .filter(DataSource::hasDlsaws)
              .findFirst()
              .orElseThrow();
      assertEquals("dlsAWSStore", awsDataSource.getStoreName());
      assertEquals(
          "v1$atlas$snapshot$cluster$db$collection$date",
          awsDataSource.getDlsaws().getDatasetPrefix());
      assertEquals(5, awsDataSource.getDlsaws().getTrimLevel().getValue());
    }

    // dls Azure data source
    {
      final TenantStorage.DataSource azureDataSource =
          pAdlStorage.getDatabasesList().stream()
              .flatMap(d -> d.getCollectionsList().stream())
              .flatMap(c -> c.getDataSourcesList().stream())
              .filter(DataSource::hasDls)
              .findFirst()
              .orElseThrow();
      assertEquals("dlsAzureStore", azureDataSource.getStoreName());
      assertEquals(
          "v1$atlas$snapshot$cluster$db$collection$date",
          azureDataSource.getDls().getDatasetPrefix());
      assertEquals(5, azureDataSource.getDls().getTrimLevel().getValue());
    }
  }

  private static boolean isAdlStorageV0MatchingTest(final TenantStorage.Storage pAdlStorage) {
    try {
      assertAdlStorageV0MatchingTest(pAdlStorage);
      return true;
    } catch (final Exception pE) {
      LOG.error(pE.getMessage());
      return false;
    }
  }

  private static boolean isAdlStorageMatchingTest(final Storage pAdlStorage) {
    try {
      assertAdlStorageMatchingTest(pAdlStorage);
      return true;
    } catch (final Exception pE) {
      LOG.error(pE.getMessage());
      return false;
    }
  }

  protected static ServletOutputStream getTestServletOutputStream(OutputStream out) {
    return new ServletOutputStream() {
      @Override
      public boolean isReady() {
        throw new UnsupportedOperationException();
      }

      @Override
      public void setWriteListener(WriteListener writeListener) {
        throw new UnsupportedOperationException();
      }

      @Override
      public synchronized void close() throws IOException {
        out.flush();
        out.close();
      }

      @Override
      public synchronized void write(byte[] bytes, int off, int len) throws IOException {
        out.write(bytes, off, len);
      }

      public void write(int b) throws IOException {
        out.write(b);
      }
    };
  }

  private static UsageLimit getTestUsageLimit() {
    final TimeSpanScanningLimit timeSpanScanningLimit =
        TimeSpanScanningLimit.newBuilder()
            .setLimitSpan(LimitSpan.LIMIT_SPAN_DAILY)
            .setLimitBytes(100000000)
            .setOverrunPolicy(OverrunPolicy.OVERRUN_POLICY_BLOCK)
            .build();
    return UsageLimit.newBuilder().setTimeSpanScanningLimit(timeSpanScanningLimit).build();
  }

  private static DataScanningLimitStatus getTestDataScanningLimitStatus() {
    return DataScanningLimitStatus.newBuilder()
        .setUsageLimit(getTestUsageLimit())
        .setActualUsageBytes(100)
        .build();
  }

  private static List<DataScanningLimitStatus> getTestDataScanningLimitStatuses() {
    return List.of(getTestDataScanningLimitStatus());
  }

  private static void assertDataScanningLimitStatusesMatchingTest(
      final List<DataScanningLimitStatus> pDataScanningLimitStatuses) {
    final DataScanningLimitStatus limitStatus = pDataScanningLimitStatuses.get(0);
    final TimeSpanScanningLimit limit = limitStatus.getUsageLimit().getTimeSpanScanningLimit();
    assertEquals(limit.getLimitBytes(), 100000000);
    assertEquals(limit.getLimitSpan(), LimitSpan.LIMIT_SPAN_DAILY);
    assertEquals(limit.getOverrunPolicy(), OverrunPolicy.OVERRUN_POLICY_BLOCK);
    assertEquals(limitStatus.getActualUsageBytes(), 100);
  }

  @BeforeEach
  public void setUp() {
    _appSettings = mock(AppSettings.class);
    _groupSvc = mock(GroupSvc.class);
    doReturn(mock(VersionInfo.class)).when(_appSettings).getVersionInfo();
    _awsGrpcClient = generateGrpcClient(_appSettings);
    _azureGrpcClient = generateGrpcClient(_appSettings);
    _gcpGrpcClient = generateGrpcClient(_appSettings);
    _dataLakeAdminApiClient =
        spy(
            new DataLakeAdminApiClient(
                Map.of(
                    CloudProvider.AWS,
                    _awsGrpcClient,
                    CloudProvider.AZURE,
                    _azureGrpcClient,
                    CloudProvider.GCP,
                    _gcpGrpcClient),
                _appSettings,
                _groupSvc));
  }

  static Stream<Arguments> tenantAndUsernameProvider() {
    return Stream.of(arguments(null, "name"), arguments(mock(NDSDataLakeTenant.class), null));
  }

  @ParameterizedTest
  @MethodSource("tenantAndUsernameProvider")
  public void testGenerateSchemasWithInvalidArguments(
      final NDSDataLakeTenant pTenant, final String pUsername) throws SvcException {

    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GENERATE_SCHEMAS);
    try {
      // Expect tenant, username fields to cause a throw
      _dataLakeAdminApiClient.generateSchemas(pTenant, pUsername, "db", "collection", authToken);
      fail("Expected request to fail");
    } catch (final DataLakeAdminApiException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
      verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
      verify(timer, never()).observeDuration();
    }
  }

  public void generateSchemasUnexpectedErrorTest(
      GenerateSchemasResponse response, NDSErrorCode expectedError) throws SvcException {
    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceStub();
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);

    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GENERATE_SCHEMAS);
    doReturn(tenantId).when(tenant).getTenantId();

    when(schemaServiceStub.generateSchemas(any())).thenReturn(response);

    try {
      _dataLakeAdminApiClient.generateSchemas(tenant, "username", "db", "coll", authToken);
      fail("Expected error from mhouse to be propagated");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(expectedError, pE.getErrorCode());
    }
    verify(_dataLakeAdminApiClient, times(1))
        .incrementAdminApiExceptionCounter(any(), any(), any());
    verify(_dataLakeAdminApiClient, times(1))
        .incrementAdminApiRequestCounter(eq(Methods.GENERATE_SCHEMAS));
    verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GENERATE_SCHEMAS));
    verify(timer, times(1)).observeDuration();
  }

  @Test
  public void testGenerateSchemasThrowsExceptionWithNoNamespaces() throws SvcException {
    final GenerateSchemasResponse noNamespaces =
        GenerateSchemasResponse.newBuilder()
            .addAllFailedNamespaces(List.of())
            .addAllSchemas(List.of())
            .build();

    // No Namespaces
    generateSchemasUnexpectedErrorTest(noNamespaces, NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR);
  }

  @Test
  public void testGenerateSchemasThrowsExceptionWithFailedNamespaces() throws SvcException {
    final GenerateSchemasResponse failedNamespace =
        GenerateSchemasResponse.newBuilder()
            .addAllFailedNamespaces(
                List.of(
                    FailedNamespace.newBuilder()
                        .setNamespace(
                            NamespaceOuterClass.Namespace.newBuilder()
                                .setDatabaseName("db")
                                .setCollectionName("coll")
                                .build())
                        .setError("Schema too cool for school")
                        .build()))
            .build();

    // Failed namespaces
    generateSchemasUnexpectedErrorTest(
        failedNamespace, NDSErrorCode.DATA_FEDERATION_GENERATE_SCHEMA_ERROR);
  }

  @Test
  public void testGenerateSchemasTimeout() throws SvcException {
    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceStub();
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final RuntimeException adlException = new StatusRuntimeException(Status.DEADLINE_EXCEEDED);

    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GENERATE_SCHEMAS);
    doReturn(tenantId).when(tenant).getTenantId();

    when(schemaServiceStub.generateSchemas(any())).thenThrow(adlException);

    try {
      _dataLakeAdminApiClient.generateSchemas(tenant, "username", "db", "coll", authToken);
      fail("Expected error from mhouse to be propagated");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_ASYNC_OPERATION_TIMED_OUT, pE.getErrorCode());
    }
    verify(_dataLakeAdminApiClient, times(1))
        .incrementAdminApiExceptionCounter(any(), any(), any());
    verify(_dataLakeAdminApiClient, times(1))
        .incrementAdminApiRequestCounter(eq(Methods.GENERATE_SCHEMAS));
    verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GENERATE_SCHEMAS));
    verify(timer, times(1)).observeDuration();
  }

  @Test
  public void testGenerateSchemasHappyPath() throws SvcException {
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceStub();

    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GENERATE_SCHEMAS);
    doReturn(tenantId).when(tenant).getTenantId();

    final GenerateSchemasResponse successResponse =
        GenerateSchemasResponse.newBuilder()
            .addAllSchemas(
                List.of(
                    Schema.newBuilder()
                        .setSchema(getTestSQLSchema(1))
                        .setNamespace(
                            NamespaceOuterClass.Namespace.newBuilder()
                                .setDatabaseName("db")
                                .setCollectionName("coll")
                                .build())
                        .build()))
            .build();

    when(schemaServiceStub.generateSchemas(any())).thenReturn(successResponse);

    final NDSDataLakeSQLSchemaView mockedResponse =
        _dataLakeAdminApiClient.generateSchemas(tenant, "username", "db", "coll", authToken);

    verify(_dataLakeAdminApiClient, never()).incrementAdminApiExceptionCounter(any(), any(), any());
    verify(_dataLakeAdminApiClient, times(1))
        .incrementAdminApiRequestCounter(eq(Methods.GENERATE_SCHEMAS));
    verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GENERATE_SCHEMAS));
    verify(timer, times(1)).observeDuration();
    assertFalse(mockedResponse.getVersion().isEmpty());
    assertFalse(mockedResponse.getJsonSchema().isEmpty());
  }

  @ParameterizedTest
  @MethodSource("tenantAndUsernameProvider")
  public void testSetSchemasWithInvalidArguments(
      final NDSDataLakeTenant pTenant, final String pUsername) {
    final NDSDataLakeSQLSchemaView schemaToSet =
        new NDSDataLakeSQLSchemaView("1", "{\"bsonType\": \"object\"}");
    final Histogram.Timer timer = mock(Histogram.Timer.class);

    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.SET_SCHEMA);
    try {
      // Expect tenant / username to cause a throw here.
      _dataLakeAdminApiClient.setSchema(pTenant, pUsername, "db", "collection", false, schemaToSet);
      fail("Expected request to fail");
    } catch (final DataLakeAdminApiException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
      verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
      verify(timer, never()).observeDuration();
    }
  }

  @Test
  public void testSetSchemasHappyPath() throws SvcException {
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final NDSDataLakeSQLSchemaView schemaToSet =
        new NDSDataLakeSQLSchemaView("1", "{\"bsonType\": \"object\"}");
    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceStub();

    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.SET_SCHEMA);
    doReturn(tenantId).when(tenant).getTenantId();

    when(schemaServiceStub.setSchema(any())).thenReturn(null);

    _dataLakeAdminApiClient.setSchema(tenant, "username", "db", "coll", false, schemaToSet);

    verify(_dataLakeAdminApiClient, never()).incrementAdminApiExceptionCounter(any(), any(), any());
    verify(_dataLakeAdminApiClient, times(1))
        .incrementAdminApiRequestCounter(eq(Methods.SET_SCHEMA));
    verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.SET_SCHEMA));
    verify(timer, times(1)).observeDuration();
  }

  @ParameterizedTest
  @MethodSource("tenantAndUsernameProvider")
  public void testGenerateAllSchemasWithInvalidArguments(
      final NDSDataLakeTenant pTenant, final String pUsername) throws SvcException {
    final SchemaServiceGrpc.SchemaServiceStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceAsyncStub(600);

    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GENERATE_ALL_SCHEMAS);
    try {
      _dataLakeAdminApiClient.generateAllSchemas(pTenant, pUsername, authToken);
      fail("Expected request to fail");
    } catch (final DataLakeAdminApiException e) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
      verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
      verify(timer, never()).observeDuration();
    }
  }

  @Test
  public void testGenerateAllSchemasThrowsException() throws SvcException {
    final SchemaServiceGrpc.SchemaServiceStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceAsyncStub(600);
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);

    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GENERATE_ALL_SCHEMAS);
    doReturn(tenantId).when(tenant).getTenantId();

    doAnswer(
            (invocation) -> {
              final StreamObserver<
                      com.xgen.mhouse.services.schema.v1.Models.GenerateAllSchemasResponse>
                  observer = invocation.getArgument(1);
              observer.onError(new StatusRuntimeException(Status.INTERNAL));
              return null;
            })
        .when(schemaServiceStub)
        .generateAllSchemas(any(), any());
    try {
      _dataLakeAdminApiClient.generateAllSchemas(tenant, "username", authToken);
      fail("Expected error from mhouse to be propagated");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE.getErrorCode());
    }
    verify(_dataLakeAdminApiClient, times(1))
        .incrementAdminApiExceptionCounter(any(), any(), any());
    verify(_dataLakeAdminApiClient, times(1))
        .incrementAdminApiRequestCounter(eq(Methods.GENERATE_ALL_SCHEMAS));
    verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GENERATE_ALL_SCHEMAS));
    verify(timer, times(1)).observeDuration();
  }

  @Test
  public void testGenerateAllSchemasHappyPath() throws SvcException {
    final SchemaServiceGrpc.SchemaServiceStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceAsyncStub(600);
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);

    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GENERATE_ALL_SCHEMAS);
    doReturn(tenantId).when(tenant).getTenantId();

    doAnswer(
            (invocation) -> {
              final StreamObserver<
                      com.xgen.mhouse.services.schema.v1.Models.GenerateAllSchemasResponse>
                  observer = invocation.getArgument(1);
              observer.onNext(mock(GenerateAllSchemasResponse.class));
              observer.onCompleted();
              return null;
            })
        .when(schemaServiceStub)
        .generateAllSchemas(any(), any());

    _dataLakeAdminApiClient.generateAllSchemas(tenant, "username", authToken);

    verify(_dataLakeAdminApiClient, never()).incrementAdminApiExceptionCounter(any(), any(), any());
    verify(_dataLakeAdminApiClient, times(1))
        .incrementAdminApiRequestCounter(eq(Methods.GENERATE_ALL_SCHEMAS));
    verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GENERATE_ALL_SCHEMAS));
    verify(timer, times(1)).observeDuration();
  }

  @Test
  public void testGetAllSchemasThrowsException() throws DataLakeAdminApiException, IOException {
    final SchemaServiceGrpc.SchemaServiceStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceAsyncStub(600);
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);

    // nulls
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_ALL_SCHEMAS);
      try {
        _dataLakeAdminApiClient.getAllSchemas(null, authToken, false);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
    }
    doReturn(tenantId).when(tenant).getTenantId();

    // error from mhouse should get propagated to client
    {
      reset(_dataLakeAdminApiClient);
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_ALL_SCHEMAS);

      doAnswer(
              (invocation) -> {
                final StreamObserver<
                        com.xgen.mhouse.services.schema.v1.Models.GetAllSchemasResponse>
                    observer = invocation.getArgument(1);
                observer.onError(new StatusRuntimeException(Status.INTERNAL));
                return null;
              })
          .when(schemaServiceStub)
          .getAllSchemas(any(), any());
      try {
        _dataLakeAdminApiClient.getAllSchemas(tenant, authToken, false);
        fail("Expected error from mhouse to be propagated");
      } catch (final DataLakeAdminApiException pE) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE.getErrorCode());
      }
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.GET_ALL_SCHEMAS));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GET_ALL_SCHEMAS));
      verify(timer, times(1)).observeDuration();
    }

    // unexpected error
    {
      reset(_dataLakeAdminApiClient);
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_ALL_SCHEMAS);
      final Exception adlException = new RuntimeException("foo");
      doThrow(adlException).when(schemaServiceStub).getAllSchemas(any(), any());
      try {
        _dataLakeAdminApiClient.getAllSchemas(tenant, authToken, false);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException pE) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE.getErrorCode());
        assertEquals(
            String.format("Unexpected error (Tenant ID: %s).", tenant.getTenantId()),
            pE.getMessage());
        assertTrue(pE.getCause().getMessage().contains("foo"));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.GET_ALL_SCHEMAS), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiRequestCounter(eq(Methods.GET_ALL_SCHEMAS));
        verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GET_ALL_SCHEMAS));
        verify(timer, times(1)).observeDuration();
      }
    }
  }

  @Test
  public void testGetAllSchemasSuccess() throws DataLakeAdminApiException, IOException {
    final SchemaServiceGrpc.SchemaServiceStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceAsyncStub(600);
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    doReturn(tenantId).when(tenant).getTenantId();

    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_ALL_SCHEMAS);

    final com.xgen.mhouse.services.schema.v1.Models.GetAllSchemasResponse response =
        mock(com.xgen.mhouse.services.schema.v1.Models.GetAllSchemasResponse.class);

    when(response.getSchemasList()).thenReturn(List.of(getTestSQLSchemaWithMetadata()));
    when(response.getTotalSchemas()).thenReturn(1);
    doAnswer(
            (invocation) -> {
              final StreamObserver<com.xgen.mhouse.services.schema.v1.Models.GetAllSchemasResponse>
                  observer = invocation.getArgument(1);
              observer.onNext(response);
              observer.onCompleted();
              return null;
            })
        .when(schemaServiceStub)
        .getAllSchemas(any(), any());

    // ok
    {
      NDSDataLakeSQLAllSchemasView result =
          _dataLakeAdminApiClient.getAllSchemas(tenant, authToken, false);
      assertEquals(result.getSchemas().size(), 1);
      assertTrue(result.getHasSchemas());
      assertEquals(result.getTotalSchemas().intValue(), 1);
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.GET_ALL_SCHEMAS));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GET_ALL_SCHEMAS));
      verify(timer, times(1)).observeDuration();
    }

    // shows deep schema when pIncludeSchemaBody set to true
    {
      NDSDataLakeSQLAllSchemasView result =
          _dataLakeAdminApiClient.getAllSchemas(tenant, authToken, true);
      assertEquals(result.getSchemas().size(), 1);
      List<NDSDataLakeSQLSchemaWithMetadataView> schemas = result.getSchemas();
      NDSDataLakeSQLSchemaWithMetadataView deepSchema = schemas.get(0);
      assertNotNull(deepSchema.getSchema());
      System.out.println(deepSchema);
      System.out.println(deepSchema.getSchema());
    }

    // returns deep schema as null when pIncludeSchemaBody set to false
    {
      NDSDataLakeSQLAllSchemasView result =
          _dataLakeAdminApiClient.getAllSchemas(tenant, authToken, false);
      assertEquals(result.getSchemas().size(), 1);
      List<NDSDataLakeSQLSchemaWithMetadataView> schemas = result.getSchemas();
      NDSDataLakeSQLSchemaWithMetadataView deepSchema = schemas.get(0);
      assertNull(deepSchema.getSchema());
    }
  }

  @ParameterizedTest
  @MethodSource("schemaVersionProvider")
  public void testGetSchemaVersionTypes(Number version)
      throws DataLakeAdminApiException, IOException {
    // validate that getSchemas() can parse the version field as int or long.

    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceStub();
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    doReturn(tenantId).when(tenant).getTenantId();

    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_SCHEMA);

    final GetSchemasResponse response = mock(GetSchemasResponse.class);

    final SchemaWithMetadata mockReturn = getTestSQLSchemaWithMetadata(getTestSQLSchema(version));
    when(response.getSchemasList()).thenReturn(List.of(mockReturn));
    when(response.getSchemas(0)).thenReturn(mockReturn);
    when(schemaServiceStub.getSchemas(any())).thenReturn(response);

    NDSDataLakeSQLSchemaWithMetadataView result =
        _dataLakeAdminApiClient.getSchema(tenant, "db", "collection");
    assertNotNull(result.getSchema());
    assertEquals(result.getSchema().getVersion(), version.toString());
  }

  static Stream<Arguments> schemaVersionProvider() {
    return Stream.of(arguments(1L), arguments(1), arguments(2L), arguments(2), arguments(0));
  }

  @Test
  public void testGetSchema() throws DataLakeAdminApiException, IOException {
    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceStub();
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);

    // nulls
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_SCHEMA);
      try {
        _dataLakeAdminApiClient.getSchema(null, "db", "collection");
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
    }
    doReturn(tenantId).when(tenant).getTenantId();

    // ok
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_SCHEMA);

      final GetSchemasResponse response = mock(GetSchemasResponse.class);

      final SchemaWithMetadata mockReturn = getTestSQLSchemaWithMetadata();
      when(response.getSchemasList()).thenReturn(List.of(mockReturn));
      when(response.getSchemas(0)).thenReturn(mockReturn);
      when(schemaServiceStub.getSchemas(any())).thenReturn(response);

      NDSDataLakeSQLSchemaWithMetadataView result =
          _dataLakeAdminApiClient.getSchema(tenant, "db", "collection");
      // We know we're asserting against a constant mock return.
      assertNotNull(result.getSchema());
      assertTrue(result.getSchema().getJsonSchema().isBlank());
      assertFalse(result.getNamespace().getDb().isEmpty());
      assertFalse(result.getNamespace().getCollection().isEmpty());
      assertEquals(NDSDataLakeSQLSchemaStatus.EMPTY, result.getStatus());

      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.GET_SCHEMA));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GET_SCHEMA));
      verify(timer, times(1)).observeDuration();
    }

    // error from mhouse should get propagated to client
    {
      reset(_dataLakeAdminApiClient);
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_SCHEMA);
      final GetSchemasResponse response = mock(GetSchemasResponse.class);

      when(response.getSchemasList()).thenReturn(List.of());
      when(schemaServiceStub.getSchemas(any())).thenReturn(response);
      try {
        _dataLakeAdminApiClient.getSchema(tenant, "db", "collection");
        fail("Expected error from mhouse to be propagated");
      } catch (final DataLakeAdminApiException pE) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE.getErrorCode());
      }
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.GET_SCHEMA));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GET_SCHEMA));
      verify(timer, times(1)).observeDuration();
    }

    // unexpected error
    {
      reset(_dataLakeAdminApiClient);
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_SCHEMA);
      final Exception adlException = new RuntimeException("foo");
      doThrow(adlException).when(schemaServiceStub).getSchemas(any());
      try {
        _dataLakeAdminApiClient.getSchema(tenant, "db", "collection");
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException pE) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE.getErrorCode());
        assertEquals(
            String.format("Unexpected error (Tenant ID: %s).", tenant.getTenantId()),
            pE.getMessage());
        assertTrue(pE.getCause().getMessage().contains("foo"));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.GET_SCHEMA), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiRequestCounter(eq(Methods.GET_SCHEMA));
        verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GET_SCHEMA));
        verify(timer, times(1)).observeDuration();
      }
    }
  }

  @Test
  public void testGetSchemaWithZeroTimestamp() throws DataLakeAdminApiException, IOException {
    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceStub();
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    doReturn(tenantId).when(tenant).getTenantId();

    final GetSchemasResponse response = mock(GetSchemasResponse.class);
    final SchemaWithMetadata mockReturn = getTestSQLSchemaWithMetadata(0, 0);
    when(response.getSchemasList()).thenReturn(List.of(mockReturn));
    when(response.getSchemas(0)).thenReturn(mockReturn);
    when(schemaServiceStub.getSchemas(any())).thenReturn(response);

    NDSDataLakeSQLSchemaWithMetadataView result =
        _dataLakeAdminApiClient.getSchema(tenant, "db", "collection");
    assertNull(result.getLastUpdate());
  }

  @Test
  public void testGetScheduledUpdate() throws DataLakeAdminApiException {
    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceStub();
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);

    // nulls
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.GET_SCHEDULED_UPDATE);
      try {
        _dataLakeAdminApiClient.getScheduledUpdate(null);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
    }
    doReturn(tenantId).when(tenant).getTenantId();

    // ok
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.GET_SCHEDULED_UPDATE);

      final com.xgen.mhouse.services.schema.v1.Models.GetScheduledUpdateResponse response =
          mock(com.xgen.mhouse.services.schema.v1.Models.GetScheduledUpdateResponse.class);

      when(response.getFrequency()).thenReturn(Frequency.FREQUENCY_DAILY);
      when(schemaServiceStub.getScheduledUpdate(any())).thenReturn(response);

      NDSDataLakeSQLScheduledUpdateView result = _dataLakeAdminApiClient.getScheduledUpdate(tenant);
      assertEquals(result.getFrequency(), Frequency.FREQUENCY_DAILY);
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.GET_SCHEDULED_UPDATE));
      verify(_dataLakeAdminApiClient, times(1))
          .startDurationTimer(eq(Methods.GET_SCHEDULED_UPDATE));
      verify(timer, times(1)).observeDuration();
    }
  }

  @Test
  public void testCreateScheduledUpdate() throws DataLakeAdminApiException {
    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceStub();
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final Frequency updateFrequency = Frequency.FREQUENCY_DAILY;
    final String username = "<EMAIL>";

    // nulls
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.CREATE_SCHEDULED_UPDATE);
      try {
        _dataLakeAdminApiClient.createScheduledUpdate(null, updateFrequency, username);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
    }
    doReturn(tenantId).when(tenant).getTenantId();

    // ok
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.CREATE_SCHEDULED_UPDATE);

      final com.xgen.mhouse.services.schema.v1.Models.CreateScheduledUpdateResponse response =
          mock(com.xgen.mhouse.services.schema.v1.Models.CreateScheduledUpdateResponse.class);

      when(schemaServiceStub.createScheduledUpdate(any())).thenReturn(response);

      NDSDataLakeSQLScheduledUpdateView result =
          _dataLakeAdminApiClient.createScheduledUpdate(tenant, updateFrequency, username);
      assertEquals(result.getFrequency(), updateFrequency);
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.CREATE_SCHEDULED_UPDATE));
      verify(_dataLakeAdminApiClient, times(1))
          .startDurationTimer(eq(Methods.CREATE_SCHEDULED_UPDATE));
      verify(timer, times(1)).observeDuration();
    }
  }

  @Test
  public void testModifyScheduledUpdate() throws DataLakeAdminApiException {
    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceStub();
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final Frequency updateFrequency = Frequency.FREQUENCY_DAILY;
    final String username = "<EMAIL>";

    // nulls
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.MODIFY_SCHEDULED_UPDATE);
      try {
        _dataLakeAdminApiClient.modifyScheduledUpdate(null, updateFrequency, username);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
    }
    doReturn(tenantId).when(tenant).getTenantId();

    // ok
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.MODIFY_SCHEDULED_UPDATE);

      final com.xgen.mhouse.services.schema.v1.Models.ModifyScheduledUpdateResponse response =
          mock(com.xgen.mhouse.services.schema.v1.Models.ModifyScheduledUpdateResponse.class);

      when(schemaServiceStub.modifyScheduledUpdate(any())).thenReturn(response);

      NDSDataLakeSQLScheduledUpdateView result =
          _dataLakeAdminApiClient.modifyScheduledUpdate(tenant, updateFrequency, username);
      assertEquals(result.getFrequency(), updateFrequency);
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.MODIFY_SCHEDULED_UPDATE));
      verify(_dataLakeAdminApiClient, times(1))
          .startDurationTimer(eq(Methods.MODIFY_SCHEDULED_UPDATE));
      verify(timer, times(1)).observeDuration();
    }
  }

  @Test
  public void testDeleteScheduledUpdate() throws DataLakeAdminApiException {
    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        _awsGrpcClient.getSchemaServiceStub();
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);

    // nulls
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.DELETE_SCHEDULED_UPDATE);
      try {
        _dataLakeAdminApiClient.getScheduledUpdate(null);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
    }
    doReturn(tenantId).when(tenant).getTenantId();

    // ok
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.DELETE_SCHEDULED_UPDATE);

      final com.xgen.mhouse.services.schema.v1.Models.DeleteScheduledUpdateResponse response =
          mock(com.xgen.mhouse.services.schema.v1.Models.DeleteScheduledUpdateResponse.class);

      when(schemaServiceStub.deleteScheduledUpdate(any())).thenReturn(response);

      // Delete the update with no errors, returns unspecified frequency.
      NDSDataLakeSQLScheduledUpdateView result =
          _dataLakeAdminApiClient.deleteScheduledUpdate(tenant);
      assertEquals(result.getFrequency(), Frequency.FREQUENCY_UNSPECIFIED);
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.DELETE_SCHEDULED_UPDATE));
      verify(_dataLakeAdminApiClient, times(1))
          .startDurationTimer(eq(Methods.DELETE_SCHEDULED_UPDATE));
      verify(timer, times(1)).observeDuration();
    }
  }

  @Test
  public void testGetStorageConfig() throws DataLakeAdminApiException {
    final StorageConfigServiceGrpc.StorageConfigServiceBlockingStub storageConfigStub =
        _awsGrpcClient.getStorageConfigServiceStub();
    final ObjectId tenantId = new ObjectId();
    final ObjectId groupId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_STORAGE);
    final Histogram.Timer timerV3 = mock(Histogram.Timer.class);
    doReturn(timerV3)
        .when(_dataLakeAdminApiClient)
        .startDurationTimerV3(eq(Methods.GET_STORAGE), any(), any());
    // nulls
    {
      try {
        _dataLakeAdminApiClient.findStorageConfig(tenant);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimerV3(any(), any(), any());
        verify(timer, never()).observeDuration();
        verify(timerV3, never()).observeDuration();
      }
    }
    doReturn(tenantId).when(tenant).getTenantId();
    doReturn(groupId).when(tenant).getGroupId();
    // not found
    {
      final GetStorageResponse response = mock(GetStorageResponse.class);
      when(response.hasStorage()).thenReturn(false);
      when(storageConfigStub.getStorage(any())).thenReturn(response);
      assertEquals(Optional.empty(), _dataLakeAdminApiClient.findStorageConfig(tenant));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.GET_STORAGE));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GET_STORAGE));
      verify(_dataLakeAdminApiClient, times(1))
          .startDurationTimerV3(eq(Methods.GET_STORAGE), any(), any());
      verify(timer, times(1)).observeDuration();
      verify(timerV3, times(1)).observeDuration();
    }

    // ok
    {
      final Date lastModifiedAt = new Date();
      final Instant lastModifiedAtInstant = lastModifiedAt.toInstant();
      final GetStorageResponse response = mock(GetStorageResponse.class);
      when(response.hasStorage()).thenReturn(true);
      when(response.getLastModifiedAt())
          .thenReturn(
              Timestamp.newBuilder()
                  .setSeconds(lastModifiedAtInstant.getEpochSecond())
                  .setNanos(lastModifiedAtInstant.getNano())
                  .build());
      final Storage adlStorage = getTestADLStorage();
      when(response.getStorage()).thenReturn(adlStorage);
      when(storageConfigStub.getStorage(any())).thenReturn(response);
      final NDSDataLakeStorageV1View result =
          _dataLakeAdminApiClient.findStorageConfig(tenant).orElseThrow();
      assertNDSStorageV1ViewMatchingTest(result);
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(2))
          .incrementAdminApiRequestCounter(eq(Methods.GET_STORAGE));
      verify(_dataLakeAdminApiClient, times(2)).startDurationTimer(eq(Methods.GET_STORAGE));
      verify(_dataLakeAdminApiClient, times(2))
          .startDurationTimerV3(eq(Methods.GET_STORAGE), any(), any());
      verify(timer, times(2)).observeDuration();
      verify(timerV3, times(2)).observeDuration();
    }

    // not found exception - should not increment exception counter
    {
      final StatusRuntimeException notFoundException = new StatusRuntimeException(Status.NOT_FOUND);
      doThrow(notFoundException).when(storageConfigStub).getStorage(any());
      assertEquals(Optional.empty(), _dataLakeAdminApiClient.findStorageConfig(tenant));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(3))
          .incrementAdminApiRequestCounter(eq(Methods.GET_STORAGE));
      verify(_dataLakeAdminApiClient, times(3)).startDurationTimer(eq(Methods.GET_STORAGE));
      verify(_dataLakeAdminApiClient, times(3))
          .startDurationTimerV3(eq(Methods.GET_STORAGE), any(), any());
      verify(timer, times(3)).observeDuration();
      verify(timerV3, times(3)).observeDuration();
    }

    // unexpected exception
    {
      final RuntimeException adlException = new RuntimeException("foo");
      doThrow(adlException).when(storageConfigStub).getStorage(any());
      try {
        _dataLakeAdminApiClient.findStorageConfig(tenant);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
        assertEquals(
            String.format("Unexpected error (Tenant ID: %s, Group ID: %s).", tenantId, groupId),
            e.getMessage());
        assertEquals(adlException, e.getCause());
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.GET_STORAGE), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounterV3(
                eq(adlException), eq(Methods.GET_STORAGE), eq(Status.UNKNOWN), any(), any());
        verify(_dataLakeAdminApiClient, times(4))
            .incrementAdminApiRequestCounter(eq(Methods.GET_STORAGE));
        verify(_dataLakeAdminApiClient, times(4)).startDurationTimer(eq(Methods.GET_STORAGE));
        verify(_dataLakeAdminApiClient, times(4))
            .startDurationTimerV3(eq(Methods.GET_STORAGE), any(), any());
        verify(timer, times(4)).observeDuration();
        verify(timerV3, times(4)).observeDuration();
      }
    }
  }

  @Test
  public void testGetJobErrors() throws DataLakeAdminApiException {
    final String jobId = "jobId";
    final String cloudProvider = "AWS";
    final String region = "us-region-1";

    final Histogram.Timer timerV3 = mock(Histogram.Timer.class);
    doReturn(timerV3)
        .when(_dataLakeAdminApiClient)
        .startDurationTimerV3(Methods.GET_JOB_ERRORS, cloudProvider, region);
    doReturn(Collections.emptyIterator())
        .when(_dataLakeAdminApiClient)
        .getJobErrors(jobId, cloudProvider);
    _dataLakeAdminApiClient.getJobErrors(jobId, cloudProvider, region);
    verify(_dataLakeAdminApiClient, never())
        .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
    verify(_dataLakeAdminApiClient, times(1)).startDurationTimerV3(any(), any(), any());
    verify(timerV3, times(1)).observeDuration();
  }

  @Test
  public void testGetJobErrorsThrowsException() throws DataLakeAdminApiException {
    final String jobId = "jobId";
    final String cloudProvider = "AWS";
    final String region = "us-region-1";

    final Histogram.Timer timerV3 = mock(Histogram.Timer.class);
    doReturn(timerV3)
        .when(_dataLakeAdminApiClient)
        .startDurationTimerV3(Methods.GET_JOB_ERRORS, cloudProvider, region);

    final RuntimeException adlException = new RuntimeException("foo");
    doThrow(adlException).when(_dataLakeAdminApiClient).getJobErrors(jobId, cloudProvider);

    try {
      _dataLakeAdminApiClient.getJobErrors(jobId, cloudProvider, region);
    } catch (final Exception e) {
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimerV3(any(), any(), any());
      verify(timerV3, times(1)).observeDuration();
    }
  }

  @Test
  public void testGetJobProgress() throws DataLakeAdminApiException {
    final String jobId = "jobId";
    final String cloudProvider = "AWS";
    final String region = "us-region-1";

    final Histogram.Timer timerV3 = mock(Histogram.Timer.class);
    doReturn(timerV3)
        .when(_dataLakeAdminApiClient)
        .startDurationTimerV3(Methods.GET_JOB_PROGRESS, cloudProvider, region);
    doReturn(null).when(_dataLakeAdminApiClient).getJobProgress(jobId, cloudProvider);

    _dataLakeAdminApiClient.getJobProgress(jobId, cloudProvider, region);
    verify(_dataLakeAdminApiClient, never())
        .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
    verify(_dataLakeAdminApiClient, times(1)).startDurationTimerV3(any(), any(), any());
    verify(timerV3, times(1)).observeDuration();
  }

  @Test
  public void testGetJobProgressThrowsException() throws DataLakeAdminApiException {
    final String jobId = "jobId";
    final String cloudProvider = "AWS";
    final String region = "us-region-1";

    final Histogram.Timer timerV3 = mock(Histogram.Timer.class);
    doReturn(timerV3)
        .when(_dataLakeAdminApiClient)
        .startDurationTimerV3(Methods.GET_JOB_PROGRESS, cloudProvider, region);

    final RuntimeException adlException = new RuntimeException("foo");
    doThrow(adlException).when(_dataLakeAdminApiClient).getJobProgress(jobId, cloudProvider);

    try {
      _dataLakeAdminApiClient.getJobProgress(jobId, cloudProvider, region);
    } catch (final Exception e) {
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimerV3(any(), any(), any());
      verify(timerV3, times(1)).observeDuration();
    }
  }

  @Test
  public void testUpdateStorageConfig() throws DataLakeAdminApiException {
    final Date lastUpdatedDate = new Date();
    final NDSDataLakeStorageV1View ndsStorage = getTestNDSStorageV1View();
    final ObjectId tenantId = new ObjectId();
    final ObjectId groupId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.SET_STORAGE);
    final Histogram.Timer timerV3 = mock(Histogram.Timer.class);
    doReturn(timerV3)
        .when(_dataLakeAdminApiClient)
        .startDurationTimerV3(eq(Methods.SET_STORAGE), any(), any());
    // nulls
    {
      try {
        _dataLakeAdminApiClient.updateStorageConfig(
            tenant, ndsStorage, lastUpdatedDate, "modifiedBy", null);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimerV3(any(), any(), any());
        verify(timer, never()).observeDuration();
        verify(timerV3, never()).observeDuration();
      }
      try {
        _dataLakeAdminApiClient.updateStorageConfig(
            tenant, null, lastUpdatedDate, "modifiedBy", null);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimerV3(any(), any(), any());
        verify(timer, never()).observeDuration();
        verify(timerV3, never()).observeDuration();
      }
    }

    doReturn(tenantId).when(tenant).getTenantId();
    doReturn(groupId).when(tenant).getGroupId();
    // ok
    {
      final SetStorageResponse response = mock(SetStorageResponse.class);
      when(_awsGrpcClient.getStorageConfigServiceStub().setStorage(any())).thenReturn(response);

      // without optional fields
      final NDSDataLakeStorageV1View ndsStorageNoLastUpdatedDate = getTestNDSStorageV1View();
      _dataLakeAdminApiClient.updateStorageConfig(
          tenant, ndsStorageNoLastUpdatedDate, lastUpdatedDate, null, null);
      verify(_awsGrpcClient.getStorageConfigServiceStub(), times(1))
          .setStorage(
              argThat(
                  req ->
                      req.getLastModifiedAt().getSeconds()
                              == lastUpdatedDate.toInstant().getEpochSecond()
                          && req.getLastModifiedAt().getNanos()
                              == lastUpdatedDate.toInstant().getNano()
                          && tenantId.toHexString().equals(req.getTenantId().getValue())
                          && req.getModifiedBy().isEmpty()
                          && isAdlStorageMatchingTest(req.getStorage())));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.SET_STORAGE));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.SET_STORAGE));
      verify(_dataLakeAdminApiClient, times(1))
          .startDurationTimerV3(eq(Methods.SET_STORAGE), any(), any());
      verify(timer, times(1)).observeDuration();
      verify(timerV3, times(1)).observeDuration();

      // with optional fields
      _dataLakeAdminApiClient.updateStorageConfig(
          tenant, ndsStorage, lastUpdatedDate, "user", null);
      verify(_awsGrpcClient.getStorageConfigServiceStub(), times(1))
          .setStorage(
              argThat(
                  req ->
                      req.getLastModifiedAt().getSeconds()
                              == lastUpdatedDate.toInstant().getEpochSecond()
                          && req.getLastModifiedAt().getNanos()
                              == lastUpdatedDate.toInstant().getNano()
                          && tenantId.toHexString().equals(req.getTenantId().getValue())
                          && "user".equals(req.getModifiedBy())
                          && isAdlStorageMatchingTest(req.getStorage())));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(2))
          .incrementAdminApiRequestCounter(eq(Methods.SET_STORAGE));
      verify(_dataLakeAdminApiClient, times(2)).startDurationTimer(eq(Methods.SET_STORAGE));
      verify(_dataLakeAdminApiClient, times(2))
          .startDurationTimerV3(eq(Methods.SET_STORAGE), any(), any());
      verify(timer, times(2)).observeDuration();
      verify(timerV3, times(2)).observeDuration();
    }

    // unexpected exception
    {
      final RuntimeException adlException = new RuntimeException("foo");
      when(_awsGrpcClient.getStorageConfigServiceStub().setStorage(any())).thenThrow(adlException);
      final ObjectId tenantId2 = new ObjectId();
      final NDSDataLakeTenant tenant2 = mock(NDSDataLakeTenant.class);
      doReturn(tenantId2).when(tenant2).getTenantId();
      doReturn(groupId).when(tenant2).getGroupId();
      try {
        _dataLakeAdminApiClient.updateStorageConfig(
            tenant2, ndsStorage, lastUpdatedDate, "user", null);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
        assertEquals(
            String.format("Unexpected error (Tenant ID: %s, Group ID: %s).", tenantId2, groupId),
            e.getMessage());
        assertEquals(adlException, e.getCause());
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.SET_STORAGE), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounterV3(
                eq(adlException), eq(Methods.SET_STORAGE), eq(Status.UNKNOWN), any(), any());
        verify(_dataLakeAdminApiClient, times(3))
            .incrementAdminApiRequestCounter(eq(Methods.SET_STORAGE));
        verify(_dataLakeAdminApiClient, times(3)).startDurationTimer(eq(Methods.SET_STORAGE));
        verify(_dataLakeAdminApiClient, times(3))
            .startDurationTimerV3(eq(Methods.SET_STORAGE), any(), any());
        verify(timer, times(3)).observeDuration();
        verify(timerV3, times(3)).observeDuration();
      }
    }
  }

  @Test
  public void testUpdateStorageConfigForStreams() throws DataLakeAdminApiException {
    final Date lastUpdatedDate = new Date();
    final NDSDataLakeStorageV1View ndsStorage = getTestNDSStorageV1View();
    final ObjectId tenantId = new ObjectId();
    final ObjectId groupId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.SET_STORAGE);
    final Histogram.Timer timerV3 = mock(Histogram.Timer.class);
    doReturn(timerV3)
        .when(_dataLakeAdminApiClient)
        .startDurationTimerV3(eq(Methods.SET_STORAGE), any(), any());
    // nulls
    {
      try {
        _dataLakeAdminApiClient.updateStorageConfigForStreams(
            tenant, ndsStorage, lastUpdatedDate, "modifiedBy", null);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimerV3(any(), any(), any());
        verify(timer, never()).observeDuration();
        verify(timerV3, never()).observeDuration();
      }
      try {
        _dataLakeAdminApiClient.updateStorageConfigForStreams(
            tenant, null, lastUpdatedDate, "modifiedBy", null);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimerV3(any(), any(), any());
        verify(timer, never()).observeDuration();
        verify(timerV3, never()).observeDuration();
      }
    }

    doReturn(tenantId).when(tenant).getTenantId();
    doReturn(groupId).when(tenant).getGroupId();
    // ok
    {
      final SetStorageResponse response = mock(SetStorageResponse.class);
      when(_awsGrpcClient.getStorageConfigServiceStub(any()).setStorage(any()))
          .thenReturn(response);

      // without optional fields
      final NDSDataLakeStorageV1View ndsStorageNoLastUpdatedDate = getTestNDSStorageV1View();
      _dataLakeAdminApiClient.updateStorageConfigForStreams(
          tenant, ndsStorageNoLastUpdatedDate, lastUpdatedDate, null, null);
      verify(_awsGrpcClient.getStorageConfigServiceStub(), times(1))
          .setStorage(
              argThat(
                  req ->
                      req.getLastModifiedAt().getSeconds()
                              == lastUpdatedDate.toInstant().getEpochSecond()
                          && req.getLastModifiedAt().getNanos()
                              == lastUpdatedDate.toInstant().getNano()
                          && tenantId.toHexString().equals(req.getTenantId().getValue())
                          && req.getModifiedBy().isEmpty()
                          && isAdlStorageMatchingTest(req.getStorage())));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.SET_STORAGE));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.SET_STORAGE));
      verify(_dataLakeAdminApiClient, times(1))
          .startDurationTimerV3(eq(Methods.SET_STORAGE), any(), any());
      verify(timer, times(1)).observeDuration();
      verify(timerV3, times(1)).observeDuration();

      // with optional fields
      _dataLakeAdminApiClient.updateStorageConfig(
          tenant, ndsStorage, lastUpdatedDate, "user", null);
      verify(_awsGrpcClient.getStorageConfigServiceStub(), times(1))
          .setStorage(
              argThat(
                  req ->
                      req.getLastModifiedAt().getSeconds()
                              == lastUpdatedDate.toInstant().getEpochSecond()
                          && req.getLastModifiedAt().getNanos()
                              == lastUpdatedDate.toInstant().getNano()
                          && tenantId.toHexString().equals(req.getTenantId().getValue())
                          && "user".equals(req.getModifiedBy())
                          && isAdlStorageMatchingTest(req.getStorage())));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(2))
          .incrementAdminApiRequestCounter(eq(Methods.SET_STORAGE));
      verify(_dataLakeAdminApiClient, times(2)).startDurationTimer(eq(Methods.SET_STORAGE));
      verify(_dataLakeAdminApiClient, times(2))
          .startDurationTimerV3(eq(Methods.SET_STORAGE), any(), any());
      verify(timer, times(2)).observeDuration();
      verify(timerV3, times(2)).observeDuration();
    }

    // unexpected exception
    {
      final RuntimeException adlException = new RuntimeException("foo");
      when(_awsGrpcClient.getStorageConfigServiceStub().setStorage(any())).thenThrow(adlException);
      final ObjectId tenantId2 = new ObjectId();
      final NDSDataLakeTenant tenant2 = mock(NDSDataLakeTenant.class);
      doReturn(tenantId2).when(tenant2).getTenantId();
      doReturn(groupId).when(tenant2).getGroupId();
      try {
        _dataLakeAdminApiClient.updateStorageConfig(
            tenant2, ndsStorage, lastUpdatedDate, "user", null);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
        assertEquals(
            String.format("Unexpected error (Tenant ID: %s, Group ID: %s).", tenantId2, groupId),
            e.getMessage());
        assertEquals(adlException, e.getCause());
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.SET_STORAGE), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounterV3(
                eq(adlException), eq(Methods.SET_STORAGE), eq(Status.UNKNOWN), any(), any());
        verify(_dataLakeAdminApiClient, times(3))
            .incrementAdminApiRequestCounter(eq(Methods.SET_STORAGE));
        verify(_dataLakeAdminApiClient, times(3)).startDurationTimer(eq(Methods.SET_STORAGE));
        verify(_dataLakeAdminApiClient, times(3))
            .startDurationTimerV3(eq(Methods.SET_STORAGE), any(), any());
        verify(timer, times(3)).observeDuration();
        verify(timerV3, times(3)).observeDuration();
      }
    }
  }

  @Test
  public void testUpdateStorageConfig_OutdatedStorageConfig()
      throws DataLakeAdminApiException, InvalidProtocolBufferException {
    final Date lastUpdatedDate = new Date();
    final NDSDataLakeStorageV1View ndsStorage = getTestNDSStorageV1View();
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.SET_STORAGE);
    doReturn(tenantId).when(tenant).getTenantId();

    final StatusRuntimeException notFoundException = new StatusRuntimeException(Status.NOT_FOUND);
    final com.google.rpc.Status statusMessage = mock(com.google.rpc.Status.class);
    final MockedStatic<StatusProto> mockedStatusProto = mockStatic(StatusProto.class);
    mockedStatusProto.when(() -> StatusProto.fromThrowable(any())).thenReturn(statusMessage);
    final Any any = mock(Any.class);
    when(statusMessage.getDetailsList()).thenReturn(List.of(any));
    when(any.is(SetStorageConfigOutdatedError.class)).thenReturn(true);
    final SetStorageConfigOutdatedError error = mock(SetStorageConfigOutdatedError.class);
    when(any.unpack(SetStorageConfigOutdatedError.class)).thenReturn(error);
    when(error.getLastModifiedAt()).thenReturn(Timestamp.getDefaultInstance());
    when(_awsGrpcClient.getStorageConfigServiceStub().setStorage(any()))
        .thenThrow(notFoundException);

    try {
      _dataLakeAdminApiClient.updateStorageConfig(
          tenant, ndsStorage, lastUpdatedDate, "user", null);
      fail("Expected request to fail");
    } catch (final DataLakeAdminApiException e) {
      assertEquals(NDSErrorCode.DATA_LAKE_STORAGE_CONFIG_OUTDATED, e.getErrorCode());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiExceptionCounter(
              eq(notFoundException), eq(Methods.SET_STORAGE), eq(Status.NOT_FOUND));
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.SET_STORAGE));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.SET_STORAGE));
      verify(timer, times(1)).observeDuration();
    } finally {
      mockedStatusProto.close();
    }
  }

  @Test
  public void testDeleteStorageConfigV0() throws DataLakeAdminApiException {
    final ObjectId tenantId = new ObjectId();
    final ObjectId groupId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.DELETE_STORAGE);
    // nulls
    {
      try {
        _dataLakeAdminApiClient.deleteStorageConfigV0(tenant);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiRequestCounter(eq(Methods.DELETE_STORAGE));
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
    }

    doReturn(tenantId).when(tenant).getTenantId();
    doReturn(groupId).when(tenant).getGroupId();
    // ok
    {
      final TenantStorage.DeleteStorageResponse response =
          mock(TenantStorage.DeleteStorageResponse.class);
      when(_awsGrpcClient.getTenantServiceStub().deleteStorage(any())).thenReturn(response);
      _dataLakeAdminApiClient.deleteStorageConfigV0(tenant);
      verify(_awsGrpcClient.getTenantServiceStub(), times(1))
          .deleteStorage(argThat(req -> tenantId.toHexString().equals(req.getTenantId())));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.DELETE_STORAGE));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.DELETE_STORAGE));
      verify(timer, times(1)).observeDuration();
    }

    // unexpected exception
    {
      final RuntimeException adlException = new RuntimeException("foo");
      when(_awsGrpcClient.getTenantServiceStub().deleteStorage(any())).thenThrow(adlException);
      try {
        _dataLakeAdminApiClient.deleteStorageConfigV0(tenant);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
        assertEquals(
            String.format("Unexpected error (Tenant ID: %s, Group ID: %s).", tenantId, groupId),
            e.getMessage());
        assertEquals(adlException, e.getCause());
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.DELETE_STORAGE), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(2))
            .incrementAdminApiRequestCounter(eq(Methods.DELETE_STORAGE));
        verify(_dataLakeAdminApiClient, times(2)).startDurationTimer(eq(Methods.DELETE_STORAGE));
        verify(timer, times(2)).observeDuration();
      }
    }
  }

  @Test
  public void testDeleteStorageConfig() throws DataLakeAdminApiException {
    final ObjectId tenantId = new ObjectId();
    final ObjectId groupId = new ObjectId();
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.DELETE_STORAGE);
    final Histogram.Timer timerV3 = mock(Histogram.Timer.class);
    doReturn(timerV3)
        .when(_dataLakeAdminApiClient)
        .startDurationTimerV3(eq(Methods.DELETE_STORAGE), any(), any());
    // nulls
    {
      try {
        _dataLakeAdminApiClient.deleteStorageConfig(tenant);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiRequestCounter(eq(Methods.DELETE_STORAGE));
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
    }

    doReturn(tenantId).when(tenant).getTenantId();
    doReturn(groupId).when(tenant).getGroupId();
    // ok
    {
      final DeleteStorageResponse response = mock(DeleteStorageResponse.class);
      when(_awsGrpcClient.getStorageConfigServiceStub().deleteStorage(any())).thenReturn(response);
      _dataLakeAdminApiClient.deleteStorageConfig(tenant);
      verify(_awsGrpcClient.getStorageConfigServiceStub(), times(1))
          .deleteStorage(
              argThat(req -> tenantId.toHexString().equals(req.getTenantId().getValue())));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.DELETE_STORAGE));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.DELETE_STORAGE));
      verify(_dataLakeAdminApiClient, times(1))
          .startDurationTimerV3(eq(Methods.DELETE_STORAGE), any(), any());
      verify(timer, times(1)).observeDuration();
      verify(timerV3, times(1)).observeDuration();
    }

    // unexpected exception
    {
      final RuntimeException adlException = new RuntimeException("foo");
      when(_awsGrpcClient.getStorageConfigServiceStub().deleteStorage(any()))
          .thenThrow(adlException);
      try {
        _dataLakeAdminApiClient.deleteStorageConfig(tenant);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
        assertEquals(
            String.format("Unexpected error (Tenant ID: %s, Group ID: %s).", tenantId, groupId),
            e.getMessage());
        assertEquals(adlException, e.getCause());
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.DELETE_STORAGE), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounterV3(
                eq(adlException), eq(Methods.DELETE_STORAGE), eq(Status.UNKNOWN), any(), any());
        verify(_dataLakeAdminApiClient, times(2))
            .incrementAdminApiRequestCounter(eq(Methods.DELETE_STORAGE));
        verify(_dataLakeAdminApiClient, times(2)).startDurationTimer(eq(Methods.DELETE_STORAGE));
        verify(_dataLakeAdminApiClient, times(2))
            .startDurationTimerV3(eq(Methods.DELETE_STORAGE), any(), any());
        verify(timer, times(2)).observeDuration();
        verify(timerV3, times(2)).observeDuration();
      }
    }
  }

  @Test
  public void testWriteQueryLogsToOutputStream() throws DataLakeAdminApiException, IOException {
    final com.xgen.mhouse.services.logs.grpc.v1.LogsServiceGrpc.LogsServiceStub logsStub =
        _awsGrpcClient.getLogsServiceStub(600);
    final ObjectId groupId = new ObjectId();
    final NDSDataLakeTenant tenant = NDSDataLakeModelTestFactory.getDataLakeTenant(groupId, "test");
    final ByteArrayOutputStream output = new ByteArrayOutputStream();
    final ServletOutputStream outputStream = getTestServletOutputStream(output);
    // null
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.DOWNLOAD_LOGS);
      try {
        _dataLakeAdminApiClient.writeQueryLogsToOutputStream(
            null, tenant, Instant.now(), Instant.now(), Models.BucketType.BUCKET_TYPE_SAFE);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
      try {
        _dataLakeAdminApiClient.writeQueryLogsToOutputStream(
            outputStream, null, Instant.now(), Instant.now(), Models.BucketType.BUCKET_TYPE_SAFE);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
      try {
        _dataLakeAdminApiClient.writeQueryLogsToOutputStream(
            outputStream, tenant, null, Instant.now(), Models.BucketType.BUCKET_TYPE_SAFE);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
      try {
        _dataLakeAdminApiClient.writeQueryLogsToOutputStream(
            outputStream, tenant, Instant.now(), null, Models.BucketType.BUCKET_TYPE_SAFE);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
    }

    // ok
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.DOWNLOAD_LOGS);
      final byte[] compressedStr =
          GZipCompressionUtils.gzipStr("my favorite number is " + (Math.random() * 10));
      doAnswer(
              (invocation) -> {
                final StreamObserver<Models.DownloadLogsResponse> observer =
                    invocation.getArgument(1);
                observer.onNext(
                    Models.DownloadLogsResponse.newBuilder()
                        .setLogs(ByteString.copyFrom(compressedStr))
                        .build());
                observer.onCompleted();
                return null;
              })
          .when(logsStub)
          .downloadLogs(any(), any());
      _dataLakeAdminApiClient.writeQueryLogsToOutputStream(
          outputStream, tenant, Instant.now(), Instant.now(), Models.BucketType.BUCKET_TYPE_SAFE);
      assertEquals(
          new String(GZipCompressionUtils.ungzipBytes(compressedStr)),
          new String(GZipCompressionUtils.ungzipBytes(output.toByteArray())));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.DOWNLOAD_LOGS));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.DOWNLOAD_LOGS));
      verify(timer, times(1)).observeDuration();
    }

    // error from mhouse should get propagated to client
    {
      reset(_dataLakeAdminApiClient);
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.DOWNLOAD_LOGS);
      doAnswer(
              (invocation) -> {
                final StreamObserver<Models.DownloadLogsResponse> observer =
                    invocation.getArgument(1);
                observer.onError(new StatusRuntimeException(Status.INTERNAL));
                return null;
              })
          .when(logsStub)
          .downloadLogs(any(), any());
      try {
        _dataLakeAdminApiClient.writeQueryLogsToOutputStream(
            outputStream, tenant, Instant.now(), Instant.now(), Models.BucketType.BUCKET_TYPE_SAFE);
        fail("Expected error from mhouse to be propagated");
      } catch (final DataLakeAdminApiException pE) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE.getErrorCode());
      }
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.DOWNLOAD_LOGS));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.DOWNLOAD_LOGS));
      verify(timer, times(1)).observeDuration();
    }

    // unexpected error
    {
      reset(_dataLakeAdminApiClient);
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.DOWNLOAD_LOGS);
      final Exception adlException = new RuntimeException("foo");
      doThrow(adlException).when(logsStub).downloadLogs(any(), any());
      try {
        _dataLakeAdminApiClient.writeQueryLogsToOutputStream(
            outputStream, tenant, Instant.now(), Instant.now(), Models.BucketType.BUCKET_TYPE_SAFE);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException pE) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE.getErrorCode());
        assertEquals(
            String.format("Unexpected error (Tenant ID: %s).", tenant.getTenantId()),
            pE.getMessage());
        assertTrue(pE.getCause().getMessage().contains("foo"));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.DOWNLOAD_LOGS), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiRequestCounter(eq(Methods.DOWNLOAD_LOGS));
        verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.DOWNLOAD_LOGS));
        verify(timer, times(1)).observeDuration();
      }
    }
  }

  private byte[] encodeBsonDateValue(final Date pDate) {
    final long dateValue = pDate.getTime();

    final byte[] bytes = new byte[8];

    bytes[0] = (byte) (dateValue & 0xFF);
    bytes[1] = (byte) ((dateValue >> 8) & 0xFF);
    bytes[2] = (byte) ((dateValue >> 16) & 0xFF);
    bytes[3] = (byte) ((dateValue >> 24) & 0xFF);
    bytes[4] = (byte) ((dateValue >> 32) & 0xFF);
    bytes[5] = (byte) ((dateValue >> 40) & 0xFF);
    bytes[6] = (byte) ((dateValue >> 48) & 0xFF);
    bytes[7] = (byte) ((dateValue >> 56) & 0xFF);

    return bytes;
  }

  @Test
  public void testGetDataSetMetrics_cacheMiss() throws DataLakeAdminApiException {
    final ObjectId archiveId = ObjectId.get();
    final ObjectId projectId = ObjectId.get();

    final Mhmetrics.GetDataSetMetricsResponse response =
        mock(Mhmetrics.GetDataSetMetricsResponse.class);

    // indicates a cache miss.
    when(response.getLastUpdatedAt())
        .thenReturn(Timestamp.newBuilder().setNanos(0).setSeconds(0).build());
    when(response.getIsUpdateInProgress()).thenReturn(true);
    when(response.getTotalDocumentCount()).thenReturn(0L);
    when(response.getTotalUncompressedSizeBytes()).thenReturn(0L);
    when(response.getFieldsList()).thenReturn(List.of());

    when(_awsGrpcClient
            .getMetricsServiceStub()
            .getDataSetMetrics(
                GetDataSetMetricsRequest.newBuilder()
                    .setProvider("aws")
                    .setRegion("us-east-1")
                    .setProjectId(projectId.toHexString())
                    .setDataSetName("data-set-name")
                    .addFields("dateField")
                    .build()))
        .thenReturn(response);

    final DataSetMetricsResult dataSetMetricsResult =
        _dataLakeAdminApiClient.getDataSetMetrics(
            archiveId, "aws", "us-east-1", projectId, "data-set-name", "dateField");

    assertEquals(dataSetMetricsResult.getTotalUncompressedSizeBytes(), (Long) 0L);
    assertEquals(dataSetMetricsResult.getTotalDocumentCount(), (Long) 0L);
    assertNull(dataSetMetricsResult.getMinDateField());
    assertNull(dataSetMetricsResult.getMaxDateField());
    assertTrue(dataSetMetricsResult.getIsUpdateInProgress());
    assertTrue(dataSetMetricsResult.getCacheMissed());
  }

  @Test
  public void testGetDataSetMetrics_cacheHit_dateCriteria() throws DataLakeAdminApiException {
    final ObjectId archiveId = ObjectId.get();
    final ObjectId projectId = ObjectId.get();

    final Calendar minCalendar = Calendar.getInstance();
    minCalendar.set(Calendar.YEAR, 2020);
    final Date minDateField = minCalendar.getTime();

    final Calendar maxCalendar = Calendar.getInstance();
    maxCalendar.set(Calendar.YEAR, 2021);
    final Date maxDateField = maxCalendar.getTime();

    final BSONValue min =
        BSONValue.newBuilder()
            .setType(BSONType.DATE_TIME)
            .setData(ByteString.copyFrom(encodeBsonDateValue(minDateField)))
            .build();
    final BSONValue max =
        BSONValue.newBuilder()
            .setType(BSONType.DATE_TIME)
            .setData(ByteString.copyFrom(encodeBsonDateValue(maxDateField)))
            .build();

    final BasicBSONObject attributesBSONObject =
        new BasicBSONObject()
            .append("max", maxDateField.toInstant().toEpochMilli())
            .append("min", minDateField.toInstant().toEpochMilli());
    final ByteString attributes = ByteString.copyFrom(BSON.encode(attributesBSONObject));
    final FieldInfo fieldInfo =
        Mhmetrics.FieldInfo.newBuilder().setMax(max).setMin(min).setAttributes(attributes).build();

    final Mhmetrics.GetDataSetMetricsResponse response =
        mock(Mhmetrics.GetDataSetMetricsResponse.class);

    when(response.getLastUpdatedAt())
        .thenReturn(Timestamp.newBuilder().setNanos(10000000).setSeconds(0).build());
    when(response.getIsUpdateInProgress()).thenReturn(true);
    when(response.getTotalDocumentCount()).thenReturn(500L);
    when(response.getTotalUncompressedSizeBytes()).thenReturn(20000L);
    when(response.getFieldsList()).thenReturn(List.of(fieldInfo));
    when(response.getFields(0)).thenReturn(fieldInfo);

    when(_awsGrpcClient
            .getMetricsServiceStub()
            .getDataSetMetrics(
                GetDataSetMetricsRequest.newBuilder()
                    .setProvider("aws")
                    .setRegion("us-east-1")
                    .setProjectId(projectId.toHexString())
                    .setDataSetName("data-set-name")
                    .addFields("dateField")
                    .build()))
        .thenReturn(response);

    final DataSetMetricsResult dataSetMetricsResult =
        _dataLakeAdminApiClient.getDataSetMetrics(
            archiveId, "aws", "us-east-1", projectId, "data-set-name", "dateField");

    assertEquals(dataSetMetricsResult.getTotalUncompressedSizeBytes(), (Long) 20000L);
    assertEquals(dataSetMetricsResult.getTotalDocumentCount(), (Long) 500L);
    assertEquals(dataSetMetricsResult.getMinDateField(), minCalendar.getTimeInMillis());
    assertEquals(dataSetMetricsResult.getMaxDateField(), maxCalendar.getTimeInMillis());
    assertTrue(dataSetMetricsResult.getIsUpdateInProgress());
    assertFalse(dataSetMetricsResult.getCacheMissed());
  }

  @Test
  public void testGetDataSetMetrics_cacheHit_customCriteria() throws DataLakeAdminApiException {
    final ObjectId archiveId = ObjectId.get();
    final ObjectId projectId = ObjectId.get();

    final Mhmetrics.GetDataSetMetricsResponse response =
        mock(Mhmetrics.GetDataSetMetricsResponse.class);

    when(response.getLastUpdatedAt())
        .thenReturn(Timestamp.newBuilder().setNanos(0).setSeconds(100).build());
    when(response.getIsUpdateInProgress()).thenReturn(true);
    when(response.getTotalDocumentCount()).thenReturn(500L);
    when(response.getTotalUncompressedSizeBytes()).thenReturn(20000L);
    when(response.getFieldsList()).thenReturn(List.of());

    when(_awsGrpcClient
            .getMetricsServiceStub()
            .getDataSetMetrics(
                GetDataSetMetricsRequest.newBuilder()
                    .setProvider("aws")
                    .setRegion("us-east-1")
                    .setProjectId(projectId.toHexString())
                    .setDataSetName("data-set-name")
                    .build()))
        .thenReturn(response);

    final DataSetMetricsResult dataSetMetricsResult =
        _dataLakeAdminApiClient.getDataSetMetrics(
            archiveId, "aws", "us-east-1", projectId, "data-set-name", null);

    assertEquals(dataSetMetricsResult.getTotalUncompressedSizeBytes(), (Long) 20000L);
    assertEquals(dataSetMetricsResult.getTotalDocumentCount(), (Long) 500L);
    assertNull(dataSetMetricsResult.getMinDateField());
    assertNull(dataSetMetricsResult.getMaxDateField());
    assertTrue(dataSetMetricsResult.getIsUpdateInProgress());
    assertFalse(dataSetMetricsResult.getCacheMissed());
  }

  @Test
  public void testGetDataSetMetrics_serviceUnavailability() throws DataLakeAdminApiException {
    final ObjectId archiveId = ObjectId.get();
    final ObjectId projectId = ObjectId.get();

    try {
      _dataLakeAdminApiClient.getDataSetMetrics(
          archiveId, "aws", "us-east-1", projectId, "data-set-name", "dateField");
      fail("Expected request to fail.");
    } catch (final DataLakeAdminApiException e) {
      assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
    }
  }

  @Test
  public void testSetUsageLimit() throws DataLakeAdminApiException {
    final UsageLimit usageLimit = UsageLimit.newBuilder().build();
    final SetUsageLimitResponse response = SetUsageLimitResponse.newBuilder().build();
    when(_awsGrpcClient.getUsageLimitsServiceStub().setUsageLimit(any())).thenReturn(response);

    // AWS
    _dataLakeAdminApiClient.setUsageLimit("AWS", usageLimit);

    // aws
    _dataLakeAdminApiClient.setUsageLimit("aws", usageLimit);

    verify(_awsGrpcClient.getUsageLimitsServiceStub(), times(2))
        .setUsageLimit(eq(SetUsageLimitRequest.newBuilder().setUsageLimit(usageLimit).build()));
    verify(_azureGrpcClient.getUsageLimitsServiceStub(), never())
        .setUsageLimit(eq(SetUsageLimitRequest.newBuilder().setUsageLimit(usageLimit).build()));

    // AZURE
    _dataLakeAdminApiClient.setUsageLimit("AZURE", usageLimit);

    // azure
    _dataLakeAdminApiClient.setUsageLimit("azure", usageLimit);

    verify(_awsGrpcClient.getUsageLimitsServiceStub(), times(2))
        .setUsageLimit(eq(SetUsageLimitRequest.newBuilder().setUsageLimit(usageLimit).build()));
    verify(_azureGrpcClient.getUsageLimitsServiceStub(), times(2))
        .setUsageLimit(eq(SetUsageLimitRequest.newBuilder().setUsageLimit(usageLimit).build()));
  }

  @Test
  public void testSetUsageLimit_error() throws DataLakeAdminApiException {
    final UsageLimit usageLimit = UsageLimit.newBuilder().build();

    // null provider
    try {
      _dataLakeAdminApiClient.setUsageLimit(null, usageLimit);
      fail("Expected request with null cloud provider to fail");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.INVALID_CLOUD_PROVIDER, pE.getErrorCode());
      verify(_awsGrpcClient.getUsageLimitsServiceStub(), never()).setUsageLimit(any());
    }

    // unsupported provider
    try {
      _dataLakeAdminApiClient.setUsageLimit("SERVERLESS", usageLimit);
      fail("Expected request with unsupported cloud provider to fail");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_UNSUPPORTED_CLOUD_PROVIDER, pE.getErrorCode());
      verify(_awsGrpcClient.getUsageLimitsServiceStub(), never()).setUsageLimit(any());
    }

    // unexpected exception
    {
      final RuntimeException adlException = new RuntimeException("foo");
      when(_awsGrpcClient.getUsageLimitsServiceStub().setUsageLimit(any())).thenThrow(adlException);
      try {
        _dataLakeAdminApiClient.setUsageLimit("AWS", usageLimit);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
        assertEquals(
            String.format("Unexpected error (Usage Limit: %s).", usageLimit), e.getMessage());
        assertEquals(adlException, e.getCause());
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.SET_USAGE_LIMIT), eq(Status.UNKNOWN));
      }
    }
  }

  @Test
  public void testDeleteUsageLimit() throws DataLakeAdminApiException {
    final UsageLimit usageLimit = UsageLimit.newBuilder().build();
    // ok
    {
      final DeleteUsageLimitResponse response = DeleteUsageLimitResponse.newBuilder().build();
      when(_awsGrpcClient.getUsageLimitsServiceStub().deleteUsageLimit(any())).thenReturn(response);
      _dataLakeAdminApiClient.deleteUsageLimit("AWS", usageLimit);
      verify(_awsGrpcClient.getUsageLimitsServiceStub(), times(1))
          .deleteUsageLimit(
              eq(DeleteUsageLimitRequest.newBuilder().setUsageLimit(usageLimit).build()));
    }

    // unexpected exception
    {
      final RuntimeException adlException = new RuntimeException("foo");
      when(_awsGrpcClient.getUsageLimitsServiceStub().deleteUsageLimit(any()))
          .thenThrow(adlException);
      try {
        _dataLakeAdminApiClient.deleteUsageLimit("AWS", usageLimit);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
        assertEquals(
            String.format("Unexpected error (Usage Limit: %s).", usageLimit), e.getMessage());
        assertEquals(adlException, e.getCause());
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.DELETE_USAGE_LIMIT), eq(Status.UNKNOWN));
      }
    }

    // null cloud provider
    try {
      _dataLakeAdminApiClient.deleteUsageLimit(null, usageLimit);
      fail("Expected request with null provider to fail");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.INVALID_CLOUD_PROVIDER, pE.getErrorCode());
    }

    // unsupported cloud provider
    try {
      _dataLakeAdminApiClient.deleteUsageLimit("SERVERLESS", usageLimit);
      fail("Expected request with unsupported provider to fail");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_UNSUPPORTED_CLOUD_PROVIDER, pE.getErrorCode());
    }
  }

  @Test
  public void testGetMetrics() throws DataLakeAdminApiException {
    final ObjectId groupId = new ObjectId();
    final NDSDataLakeTenant tenant = NDSDataLakeModelTestFactory.getDataLakeTenant(groupId, "test");
    // nulls
    {
      try {
        _dataLakeAdminApiClient.getMetrics(null, null, null);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiRequestCounter(eq(Methods.GET_METRICS));
      }
    }

    // ok
    {
      final CalculateTenantMetricsResponse response = mock(CalculateTenantMetricsResponse.class);
      when(response.getAvgQueryExecutionTimeMillis()).thenReturn(1L);
      when(response.getTotalDataReturnedBytes()).thenReturn(2L);
      when(response.getTotalDataScannedBytes()).thenReturn(3L);
      when(response.getTotalSuccessfulQueries()).thenReturn(4L);
      when(response.getTotalFailedQueries()).thenReturn(5L);
      when(_awsGrpcClient.getMetricsV1ServiceStub().calculateTenantMetrics(any()))
          .thenReturn(response);
      final Date endDate = new Date();
      final Date startDate = DateUtils.addDays(endDate, -1);
      final NDSDataLakeMetricsView view =
          _dataLakeAdminApiClient.getMetrics(tenant, startDate, endDate);
      verify(_awsGrpcClient.getMetricsV1ServiceStub(), times(1))
          .calculateTenantMetrics(
              argThat(
                  req ->
                      tenant.getTenantId().toHexString().equals(req.getTenantId().getValue())
                          && DateUtils.truncatedEquals(
                              startDate,
                              new Date(req.getStartTime().getSeconds() * 1000),
                              Calendar.SECOND)
                          && DateUtils.truncatedEquals(
                              endDate,
                              new Date(req.getEndTime().getSeconds() * 1000),
                              Calendar.SECOND)));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      assertEquals(Double.valueOf(1), view.getAvgExecutionTime());
      assertEquals(Long.valueOf(2), view.getTotalDataReturned());
      assertEquals(Long.valueOf(3), view.getTotalDataScanned());
      assertEquals(Long.valueOf(4), view.getTotalSuccessfulQueries());
      assertEquals(Long.valueOf(5), view.getTotalFailedQueries());
    }

    // unexpected exception
    {
      final RuntimeException adlException = new RuntimeException("foo");
      when(_awsGrpcClient.getMetricsV1ServiceStub().calculateTenantMetrics(any()))
          .thenThrow(adlException);
      final Date endDate = new Date();
      final Date startDate = DateUtils.addDays(endDate, -1);
      try {
        _dataLakeAdminApiClient.getMetrics(tenant, startDate, endDate);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
        assertEquals(
            String.format("Unexpected error (Tenant ID: %s).", tenant.getTenantId()),
            e.getMessage());
        assertEquals(adlException, e.getCause());
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.GET_METRICS), eq(Status.UNKNOWN));
      }
    }
  }

  @Test
  public void testGetMetrics_timeout() throws DataLakeAdminApiException {
    final ObjectId groupId = new ObjectId();
    final NDSDataLakeTenant tenant = NDSDataLakeModelTestFactory.getDataLakeTenant(groupId, "test");
    final RuntimeException adlException = new StatusRuntimeException(Status.DEADLINE_EXCEEDED);
    when(_awsGrpcClient.getMetricsV1ServiceStub().calculateTenantMetrics(any()))
        .thenThrow(adlException);
    final Date endDate = new Date();
    final Date startDate = DateUtils.addDays(endDate, -1);
    try {
      _dataLakeAdminApiClient.getMetrics(tenant, startDate, endDate);
      fail("Expected request to fail");
    } catch (final DataLakeAdminApiException e) {
      assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
      assertEquals(
          String.format("Unexpected error (Tenant ID: %s).", tenant.getTenantId()), e.getMessage());
      assertEquals(adlException, e.getCause());
      // skips prometheus increment
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), eq(Methods.GET_METRICS), any());
    }
  }

  @Test
  public void testGetLimits() throws DataLakeAdminApiException {
    final UsageLimitsServiceBlockingStub usageLimitsServiceStub =
        _awsGrpcClient.getUsageLimitsServiceStub();
    final ObjectId groupId = new ObjectId();
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_USAGE_LIMITS);

    // nulls
    {
      try {
        _dataLakeAdminApiClient.getUsageLimits("AWS", null);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(timer, never()).observeDuration();
      }
    }

    // ok
    {
      final GetUsageLimitsResponse response = mock(GetUsageLimitsResponse.class);

      final List<DataScanningLimitStatus> dataScanningLimitStatuses =
          getTestDataScanningLimitStatuses();
      when(response.getDataScanningLimitStatusesList()).thenReturn(dataScanningLimitStatuses);
      when(response.getDataScanningLimitStatusesCount())
          .thenReturn(dataScanningLimitStatuses.size());
      when(usageLimitsServiceStub.getUsageLimits(any())).thenReturn(response);
      final List<DataScanningLimitStatus> result =
          _dataLakeAdminApiClient.getUsageLimits("AWS", groupId);
      assertDataScanningLimitStatusesMatchingTest(result);
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.GET_USAGE_LIMITS));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GET_USAGE_LIMITS));
      verify(timer, times(1)).observeDuration();
    }

    // unexpected exception
    {
      final RuntimeException adlException = new RuntimeException("foo");
      when(_azureGrpcClient.getUsageLimitsServiceStub().getUsageLimits(any()))
          .thenThrow(adlException);
      try {
        _dataLakeAdminApiClient.getUsageLimits("azure", groupId);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
        assertEquals(String.format("Unexpected error (Group ID: %s).", groupId), e.getMessage());
        assertEquals(adlException, e.getCause());
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.GET_USAGE_LIMITS), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(2))
            .incrementAdminApiRequestCounter(eq(Methods.GET_USAGE_LIMITS));
        verify(_dataLakeAdminApiClient, times(2)).startDurationTimer(eq(Methods.GET_USAGE_LIMITS));
        verify(timer, times(2)).observeDuration();
      }
    }

    // null provider
    try {
      _dataLakeAdminApiClient.getUsageLimits(null, groupId);
      fail("Expected request with null provider to fail");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.INVALID_CLOUD_PROVIDER, pE.getErrorCode());
    }

    // unsupported provider
    try {
      _dataLakeAdminApiClient.getUsageLimits("SEVERLESS", groupId);
      fail("Expected request with unsupported provider to fail");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_UNSUPPORTED_CLOUD_PROVIDER, pE.getErrorCode());
    }
  }

  @Test
  public void testValidateStorageConfig() throws DataLakeAdminApiException {
    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeStorageV1View ndsStorage = getTestNDSStorageV1View();
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.VALIDATE_STORAGE);
    final Histogram.Timer timerV3 = mock(Histogram.Timer.class);
    doReturn(timerV3)
        .when(_dataLakeAdminApiClient)
        .startDurationTimerV3(eq(Methods.VALIDATE_STORAGE), any(), any());
    // nulls
    {
      try {
        _dataLakeAdminApiClient.validateStorageConfig(tenantId, "AWS", null);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimerV3(any(), any(), any());
        verify(timer, never()).observeDuration();
        verify(timerV3, never()).observeDuration();
      }
      try {
        _dataLakeAdminApiClient.validateStorageConfig(null, "AWS", ndsStorage);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_ARGUMENT, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimerV3(any(), any(), any());
        verify(timer, never()).observeDuration();
        verify(timerV3, never()).observeDuration();
      }
      try {
        _dataLakeAdminApiClient.validateStorageConfig(tenantId, null, ndsStorage);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.INVALID_CLOUD_PROVIDER, e.getErrorCode());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounter(any(), any(), any());
        verify(_dataLakeAdminApiClient, never())
            .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
        verify(_dataLakeAdminApiClient, never()).incrementAdminApiRequestCounter(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimer(any());
        verify(_dataLakeAdminApiClient, never()).startDurationTimerV3(any(), any(), any());
        verify(timer, never()).observeDuration();
        verify(timerV3, never()).observeDuration();
      }
    }

    // ok, errors
    {
      final ValidateStorageResponse response = mock(ValidateStorageResponse.class);
      final String errorMessage = "your storage config is no good";
      final String anotherErrorMessage = "another error";
      final ProtocolStringList errors =
          new LazyStringArrayList(List.of(errorMessage, anotherErrorMessage));
      when(response.getErrorsList()).thenReturn(errors);
      when(_awsGrpcClient.getStorageConfigServiceStub().validateStorage(any()))
          .thenReturn(response);
      final NDSDataLakeStorageValidationErrorsView validateResponse =
          _dataLakeAdminApiClient.validateStorageConfig(tenantId, "AWS", ndsStorage);
      verify(_awsGrpcClient.getStorageConfigServiceStub(), times(1))
          .validateStorage(
              argThat(
                  req ->
                      tenantId.toHexString().equals(req.getTenantId().getValue())
                          && isAdlStorageMatchingTest(req.getStorage())));
      errors.forEach(error -> validateResponse.getErrors().contains(error));
      assertEquals(validateResponse.getErrorsCount(), errors.size());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.VALIDATE_STORAGE));
      verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.VALIDATE_STORAGE));
      verify(_dataLakeAdminApiClient, times(1))
          .startDurationTimerV3(eq(Methods.VALIDATE_STORAGE), any(), any());
      verify(timer, times(1)).observeDuration();
      verify(timerV3, times(1)).observeDuration();
      verify(_azureGrpcClient.getStorageConfigServiceStub(), never()).validateStorage(any());
    }

    // ok, no errors
    {
      final ValidateStorageResponse response = mock(ValidateStorageResponse.class);
      when(response.getErrorsList()).thenReturn(new LazyStringArrayList());
      when(_awsGrpcClient.getStorageConfigServiceStub().validateStorage(any()))
          .thenReturn(response);
      _dataLakeAdminApiClient.validateStorageConfig(tenantId, "AWS", ndsStorage);
      verify(_awsGrpcClient.getStorageConfigServiceStub(), times(2))
          .validateStorage(
              argThat(
                  req ->
                      tenantId.toHexString().equals(req.getTenantId().getValue())
                          && isAdlStorageMatchingTest(req.getStorage())));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(2))
          .incrementAdminApiRequestCounter(eq(Methods.VALIDATE_STORAGE));
      verify(_dataLakeAdminApiClient, times(2)).startDurationTimer(eq(Methods.VALIDATE_STORAGE));
      verify(_dataLakeAdminApiClient, times(2))
          .startDurationTimerV3(eq(Methods.VALIDATE_STORAGE), any(), any());
      verify(timer, times(2)).observeDuration();
      verify(timerV3, times(2)).observeDuration();
      verify(_azureGrpcClient.getStorageConfigServiceStub(), never()).validateStorage(any());
    }

    // azure
    {
      final ValidateStorageResponse response = mock(ValidateStorageResponse.class);
      when(response.getErrorsList()).thenReturn(new LazyStringArrayList());
      when(_azureGrpcClient.getStorageConfigServiceStub().validateStorage(any()))
          .thenReturn(response);
      _dataLakeAdminApiClient.validateStorageConfig(tenantId, "azure", ndsStorage);
      verify(_azureGrpcClient.getStorageConfigServiceStub(), times(1))
          .validateStorage(
              argThat(
                  req ->
                      tenantId.toHexString().equals(req.getTenantId().getValue())
                          && isAdlStorageMatchingTest(req.getStorage())));
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounterV3(any(), any(), any(), any(), any());
      verify(_dataLakeAdminApiClient, times(3))
          .incrementAdminApiRequestCounter(eq(Methods.VALIDATE_STORAGE));
      verify(_dataLakeAdminApiClient, times(3)).startDurationTimer(eq(Methods.VALIDATE_STORAGE));
      verify(_dataLakeAdminApiClient, times(3))
          .startDurationTimerV3(eq(Methods.VALIDATE_STORAGE), any(), any());
      verify(timer, times(3)).observeDuration();
      verify(timerV3, times(3)).observeDuration();
      verify(_awsGrpcClient.getStorageConfigServiceStub(), times(2)).validateStorage(any());
    }

    // unexpected exception
    {
      final Exception adlException = new RuntimeException("foo");
      when(_awsGrpcClient.getStorageConfigServiceStub().validateStorage(any()))
          .thenThrow(adlException);
      try {
        _dataLakeAdminApiClient.validateStorageConfig(tenantId, "AWS", ndsStorage);
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException e) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, e.getErrorCode());
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.VALIDATE_STORAGE), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounterV3(
                eq(adlException), eq(Methods.VALIDATE_STORAGE), eq(Status.UNKNOWN), any(), any());
        verify(_dataLakeAdminApiClient, times(4))
            .incrementAdminApiRequestCounter(eq(Methods.VALIDATE_STORAGE));
        verify(_dataLakeAdminApiClient, times(4)).startDurationTimer(eq(Methods.VALIDATE_STORAGE));
        verify(_dataLakeAdminApiClient, times(4))
            .startDurationTimerV3(eq(Methods.VALIDATE_STORAGE), any(), any());
        verify(timer, times(4)).observeDuration();
        verify(timerV3, times(4)).observeDuration();
      }
    }
  }

  @Test
  public void testGetSupportedRegions() throws DataLakeAdminApiException {
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_REGIONS);
    final List<Region> regions =
        List.of(
            Region.newBuilder()
                .setProviderRegion(
                    Cloud.ProviderRegion.newBuilder()
                        .setProvider(Cloud.Provider.PROVIDER_AWS)
                        .setRegion("us-east-1")
                        .build())
                .setMongohouseRegion("us-east-2")
                .build(),
            Region.newBuilder()
                .setProviderRegion(
                    Cloud.ProviderRegion.newBuilder()
                        .setProvider(Cloud.Provider.PROVIDER_AZURE)
                        .setRegion("regionA")
                        .build())
                .setMongohouseRegion("regionB")
                .build(),
            Region.newBuilder()
                .setProviderRegion(
                    Cloud.ProviderRegion.newBuilder()
                        .setProvider(Cloud.Provider.PROVIDER_AWS)
                        .setRegion("regionC")
                        .build())
                .setMongohouseRegion("regionD")
                .build());
    final GetRegionsResponse response =
        GetRegionsResponse.newBuilder().addAllRegions(regions).build();
    when(_awsGrpcClient.getRegionsServiceStub().getRegions(any())).thenReturn(response);
    assertEquals(
        regions,
        _dataLakeAdminApiClient.getSupportedRegions(DataLakeAdminApiClient.CLOUD_PROVIDER_AWS));
    verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GET_REGIONS));

    // error handling
    when(_awsGrpcClient.getRegionsServiceStub().getRegions(any()))
        .thenThrow(new StatusRuntimeException(Status.INTERNAL));
    try {
      _dataLakeAdminApiClient.getSupportedRegions(DataLakeAdminApiClient.CLOUD_PROVIDER_AWS);
      fail("Expected operation to fail");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE.getErrorCode());
    }
    verify(_dataLakeAdminApiClient, never()).incrementAdminApiExceptionCounter(any(), any(), any());
  }

  @Test
  public void testGetCloudProvider() throws DataLakeAdminApiException {
    try {
      _dataLakeAdminApiClient.getDLSAcceptedCloudProvider("unknown");
      fail("Expected operation to fail");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_UNSUPPORTED_CLOUD_PROVIDER, pE.getErrorCode());
    }
    assertEquals("aws", _dataLakeAdminApiClient.getDLSAcceptedCloudProvider("AWS"));
    assertEquals("azure", _dataLakeAdminApiClient.getDLSAcceptedCloudProvider("AZURE"));
    assertEquals("gcp", _dataLakeAdminApiClient.getDLSAcceptedCloudProvider("GCP"));

    try {
      _dataLakeAdminApiClient.getCloudProvider(List.of());
      fail("Expected operation to fail");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.DATA_SETS_REQUEST_INVALID_CLOUD_PROVIDER, pE.getErrorCode());
    }

    try {
      _dataLakeAdminApiClient.getCloudProvider(
          List.of(
              DataSetRequest.newBuilder()
                  .setDataSetLocation(
                      MetadataLocation.newBuilder()
                          .setProvider("aws")
                          .setRegion("us-east-1")
                          .build())
                  .build(),
              DataSetRequest.newBuilder()
                  .setDataSetLocation(
                      MetadataLocation.newBuilder()
                          .setProvider("azure")
                          .setRegion("eastus2")
                          .build())
                  .build()));
      fail("Expected operation to fail");
    } catch (final DataLakeAdminApiException pE) {
      // only 1 cloud provider can be in the parameters at once
      assertEquals(NDSErrorCode.DATA_SETS_REQUEST_INVALID_CLOUD_PROVIDER, pE.getErrorCode());
    }
    assertEquals(
        "aws",
        _dataLakeAdminApiClient.getCloudProvider(
            List.of(
                DataSetRequest.newBuilder()
                    .setDataSetLocation(
                        MetadataLocation.newBuilder()
                            .setProvider("aws")
                            .setRegion("us-east-1")
                            .build())
                    .build(),
                DataSetRequest.newBuilder()
                    .setDataSetLocation(
                        MetadataLocation.newBuilder()
                            .setProvider("aws")
                            .setRegion("us-west-1")
                            .build())
                    .build())));

    assertEquals(
        "aws",
        _dataLakeAdminApiClient.getDLSAcceptedCloudProvider(
            new NDSDataLakeTenantBuilder().build()));
    assertEquals(
        "azure",
        _dataLakeAdminApiClient.getDLSAcceptedCloudProvider(
            new NDSDataLakeTenantBuilder()
                .dataProcessRegion(new NDSDataLakeDataProcessRegion(CloudProvider.AZURE, null))
                .build()));
    assertEquals(
        "gcp",
        _dataLakeAdminApiClient.getDLSAcceptedCloudProvider(
            new NDSDataLakeTenantBuilder()
                .dataProcessRegion(new NDSDataLakeDataProcessRegion(CloudProvider.GCP, null))
                .build()));
    try {
      _dataLakeAdminApiClient.getDLSAcceptedCloudProvider(
          new NDSDataLakeTenantBuilder()
              .dataProcessRegion(new NDSDataLakeDataProcessRegion(CloudProvider.SERVERLESS, null))
              .build());
      fail("Expected operation to fail");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_UNSUPPORTED_CLOUD_PROVIDER, pE.getErrorCode());
    }
  }

  @Test
  public void testGetFirstRegionEmptyList() {
    assertEquals(
        DataLakeAdminApiClient.REGION_UNKNOWN,
        _dataLakeAdminApiClient.getFirstRegion(Collections.emptyList()));
  }

  @Test
  public void testGetFirstRegion() {
    // Create two mock DataSetRequest instances with different regions.
    assertEquals(
        "us-east-1",
        _dataLakeAdminApiClient.getFirstRegion(
            List.of(
                DataSetRequest.newBuilder()
                    .setDataSetLocation(
                        MetadataLocation.newBuilder()
                            .setProvider("aws")
                            .setRegion("us-east-1")
                            .build())
                    .build(),
                DataSetRequest.newBuilder()
                    .setDataSetLocation(
                        MetadataLocation.newBuilder()
                            .setProvider("aws")
                            .setRegion("us-west-1")
                            .build())
                    .build())));
  }

  @Test
  public void testAcceptPrivateEndpoint() throws DataLakeAdminApiException {
    final PrivateLinkServiceGrpc.PrivateLinkServiceBlockingStub privateLinkServiceStub =
        _azureGrpcClient.getPrivateLinkServiceStub(300);

    // ok
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.ACCEPT_PRIVATE_ENDPOINT);

      final AcceptPrivateEndpointResponse response = mock(AcceptPrivateEndpointResponse.class);
      when(response.getLinkIdentifier()).thenReturn("link");
      when(privateLinkServiceStub.acceptPrivateEndpoint(any())).thenReturn(response);

      final String result = _dataLakeAdminApiClient.acceptPrivateEndpoint("conn", "eastus2");
      assertEquals("link", result);

      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.ACCEPT_PRIVATE_ENDPOINT));
      verify(_dataLakeAdminApiClient, times(1))
          .startDurationTimer(eq(Methods.ACCEPT_PRIVATE_ENDPOINT));
      verify(timer, times(1)).observeDuration();
    }

    // unexpected error
    {
      reset(_dataLakeAdminApiClient);
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.ACCEPT_PRIVATE_ENDPOINT);

      final Exception adlException = new RuntimeException("foo");
      doThrow(adlException).when(privateLinkServiceStub).acceptPrivateEndpoint(any());
      try {
        _dataLakeAdminApiClient.acceptPrivateEndpoint("conn", "eastus2");
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException pE) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE.getErrorCode());
        assertTrue(pE.getCause().getMessage().contains("foo"));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.ACCEPT_PRIVATE_ENDPOINT), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiRequestCounter(eq(Methods.ACCEPT_PRIVATE_ENDPOINT));
        verify(_dataLakeAdminApiClient, times(1))
            .startDurationTimer(eq(Methods.ACCEPT_PRIVATE_ENDPOINT));
        verify(timer, times(1)).observeDuration();
      }
    }
  }

  @Test
  public void testDeletePrivateEndpoint() throws DataLakeAdminApiException {
    final PrivateLinkServiceGrpc.PrivateLinkServiceBlockingStub privateLinkServiceStub =
        _azureGrpcClient.getPrivateLinkServiceStub(300);

    // ok
    {
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.DELETE_PRIVATE_ENDPOINT);

      final DeletePrivateEndpointResponse response = mock(DeletePrivateEndpointResponse.class);
      when(privateLinkServiceStub.deletePrivateEndpoint(any())).thenReturn(response);

      _dataLakeAdminApiClient.deletePrivateEndpoint("conn", "eastus2");

      verify(_dataLakeAdminApiClient, never())
          .incrementAdminApiExceptionCounter(any(), any(), any());
      verify(_dataLakeAdminApiClient, times(1))
          .incrementAdminApiRequestCounter(eq(Methods.DELETE_PRIVATE_ENDPOINT));
      verify(_dataLakeAdminApiClient, times(1))
          .startDurationTimer(eq(Methods.DELETE_PRIVATE_ENDPOINT));
      verify(timer, times(1)).observeDuration();
    }

    // unexpected error
    {
      reset(_dataLakeAdminApiClient);
      final Histogram.Timer timer = mock(Histogram.Timer.class);
      doReturn(timer)
          .when(_dataLakeAdminApiClient)
          .startDurationTimer(Methods.DELETE_PRIVATE_ENDPOINT);

      final Exception adlException = new RuntimeException("foo");
      doThrow(adlException).when(privateLinkServiceStub).deletePrivateEndpoint(any());
      try {
        _dataLakeAdminApiClient.deletePrivateEndpoint("conn", "eastus2");
        fail("Expected request to fail");
      } catch (final DataLakeAdminApiException pE) {
        assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE.getErrorCode());
        assertTrue(pE.getCause().getMessage().contains("foo"));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiExceptionCounter(
                eq(adlException), eq(Methods.DELETE_PRIVATE_ENDPOINT), eq(Status.UNKNOWN));
        verify(_dataLakeAdminApiClient, times(1))
            .incrementAdminApiRequestCounter(eq(Methods.DELETE_PRIVATE_ENDPOINT));
        verify(_dataLakeAdminApiClient, times(1))
            .startDurationTimer(eq(Methods.DELETE_PRIVATE_ENDPOINT));
        verify(timer, times(1)).observeDuration();
      }
    }
  }

  @Test
  public void testDestroyDataSets_fail() throws DataLakeAdminApiException {
    final StorageServiceGrpc.StorageServiceStub stub =
        _awsGrpcClient.getStorageServiceAsyncStub(300);
    final NDSDataSetDLS dataSetDLS = mock(NDSDataSetDLS.class);
    final ObjectId groupId = new ObjectId();
    @SuppressWarnings("unchecked")
    final StreamObserver<DataSetRequest> requester =
        (StreamObserver<DataSetRequest>) mock(StreamObserver.class);
    doReturn(groupId).when(dataSetDLS).getGroupId();
    doReturn("data-set-name").when(dataSetDLS).getName();
    doReturn("aws").when(dataSetDLS).getMetadataProvider();
    doReturn("us-east-1").when(dataSetDLS).getMetadataRegion();
    doAnswer(
            (invocation) -> {
              final StreamObserver<DestroyDataSetsResponse> observer = invocation.getArgument(0);
              observer.onError(new StatusRuntimeException(Status.INTERNAL));
              return requester;
            })
        .when(stub)
        .destroyDataSets(any());
    doNothing().when(requester).onNext(any());
    doNothing().when(requester).onCompleted();

    try {
      _dataLakeAdminApiClient.destroyDataSets(groupId, List.of(dataSetDLS));
      fail("Expected operation to fail");
    } catch (final DataLakeAdminApiException pE) {
      assertEquals(NDSErrorCode.DATA_LAKE_UNEXPECTED_ERROR, pE.getErrorCode());
    }
  }

  @Test
  public void testListAndonCords() throws DataLakeAdminApiException {
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.LIST_ANDON_CORDS);

    final AndonCordServiceGrpc.AndonCordServiceBlockingStub awsStub =
        _awsGrpcClient.getAndonCordServiceStub();
    final AndonCordServiceGrpc.AndonCordServiceBlockingStub azureStub =
        _azureGrpcClient.getAndonCordServiceStub();
    final AndonCordServiceGrpc.AndonCordServiceBlockingStub gcpStub =
        _gcpGrpcClient.getAndonCordServiceStub();
    when(awsStub.list(
            ListRequest.newBuilder().setState(AndonCordState.ANDON_CORD_STATE_ENABLED).build()))
        .thenReturn(
            ListResponse.newBuilder()
                .addAndonCords(
                    AndonCord.newBuilder()
                        .setName("feature_a")
                        .setRegion("us-east-1")
                        .setState(AndonCordState.ANDON_CORD_STATE_ENABLED)
                        .build())
                .build());
    when(azureStub.list(
            ListRequest.newBuilder().setState(AndonCordState.ANDON_CORD_STATE_ENABLED).build()))
        .thenReturn(ListResponse.newBuilder().build());
    when(gcpStub.list(
            ListRequest.newBuilder().setState(AndonCordState.ANDON_CORD_STATE_ENABLED).build()))
        .thenReturn(ListResponse.newBuilder().build());

    final List<NDSDataLakeAndonCordView> resp1 =
        _dataLakeAdminApiClient.listAndonCords(null, null, "enabled");
    verify(_dataLakeAdminApiClient, never()).incrementAdminApiExceptionCounter(any(), any(), any());
    verify(_dataLakeAdminApiClient, times(3)).startDurationTimer(eq(Methods.LIST_ANDON_CORDS));
    verify(timer, times(3)).observeDuration();
    assertEquals(resp1.size(), 1);
    NDSDataLakeAndonCordView featureAAc = resp1.get(0);
    assertEquals(featureAAc.getName(), "feature_a");

    when(awsStub.list(
            ListRequest.newBuilder()
                .setRegion("")
                .setName("")
                .setState(AndonCordState.ANDON_CORD_STATE_ALL)
                .build()))
        .thenReturn(ListResponse.newBuilder().build());
    when(azureStub.list(
            ListRequest.newBuilder()
                .setRegion("")
                .setName("")
                .setState(AndonCordState.ANDON_CORD_STATE_ALL)
                .build()))
        .thenReturn(
            ListResponse.newBuilder()
                .addAndonCords(
                    AndonCord.newBuilder()
                        .setName("feature_b")
                        .setRegion("eastus2")
                        .setState(AndonCordState.ANDON_CORD_STATE_DISABLED)
                        .build())
                .build());
    when(gcpStub.list(
            ListRequest.newBuilder()
                .setRegion("")
                .setName("")
                .setState(AndonCordState.ANDON_CORD_STATE_ALL)
                .build()))
        .thenReturn(
            ListResponse.newBuilder()
                .addAndonCords(
                    AndonCord.newBuilder()
                        .setName("feature_c")
                        .setRegion("us-east-4")
                        .setState(AndonCordState.ANDON_CORD_STATE_UNAVAILABLE))
                .build());

    final List<NDSDataLakeAndonCordView> resp2 = _dataLakeAdminApiClient.listAndonCords("", "", "");
    verify(_dataLakeAdminApiClient, never()).incrementAdminApiExceptionCounter(any(), any(), any());
    verify(_dataLakeAdminApiClient, times(6)).startDurationTimer(eq(Methods.LIST_ANDON_CORDS));
    verify(timer, times(6)).observeDuration();
    assertEquals(resp2.size(), 2);
    resp2.sort(Comparator.comparing(NDSDataLakeAndonCordView::getName));
    NDSDataLakeAndonCordView featureBAc = resp2.get(0);
    assertEquals(featureBAc.getName(), "feature_b");
    NDSDataLakeAndonCordView featureCAc = resp2.get(1);
    assertEquals(featureCAc.getName(), "feature_c");
  }

  @Test
  public void testMakeCreateOrUpdateRequest() throws Exception {
    final ObjectId oid = new ObjectId();
    final List<NDSDataLakeCreateOrUpdateAndonCordValueDataSetView> dataSets1 =
        List.of(
            NDSDataLakeCreateOrUpdateAndonCordValueDataSetView.builder()
                .dataSetName("a")
                .dataSetValue(100)
                .projectId(oid.toHexString())
                .build(),
            NDSDataLakeCreateOrUpdateAndonCordValueDataSetView.builder()
                .dataSetName("b")
                .dataSetValue(200)
                .projectId(oid.toHexString())
                .build());
    final NDSDataLakeCreateOrUpdateAndonCordValueView value1 =
        NDSDataLakeCreateOrUpdateAndonCordValueView.builder()
            .defaultValue(5)
            .dataSets(dataSets1)
            .build();
    final NDSDataLakeCreateOrUpdateAndonCordView andonCord1 =
        NDSDataLakeCreateOrUpdateAndonCordView.builder()
            .name("feature_a")
            .region("us-east-1")
            .state("enabled")
            .value(value1)
            .build();

    final boolean override1 = true;
    final CreateOrUpdateRequest request1 =
        _dataLakeAdminApiClient.makeCreateOrUpdateRequest(andonCord1, override1);

    final List<DataSet> expectedDataSets1 =
        List.of(
            DataSet.newBuilder()
                .setName("a")
                .setJsonValue("{\"value\":100}")
                .setProjectId(ProjectID.newBuilder().setValue(oid.toHexString()).build())
                .build(),
            DataSet.newBuilder()
                .setName("b")
                .setJsonValue("{\"value\":200}")
                .setProjectId(ProjectID.newBuilder().setValue(oid.toHexString()).build())
                .build());
    final CreateOrUpdateRequest expected1 =
        CreateOrUpdateRequest.newBuilder()
            .setRegion("us-east-1")
            .setName("feature_a")
            .setState(AndonCordState.ANDON_CORD_STATE_ENABLED)
            .addAllDataSets(expectedDataSets1)
            .setDefaultValue("{\"defaultValue\":5}")
            .setOverride(true)
            .build();

    assertEquals(expected1, request1);

    // Ensure things work with no data sets and a lack of region value.
    final NDSDataLakeCreateOrUpdateAndonCordValueView value2 =
        NDSDataLakeCreateOrUpdateAndonCordValueView.builder().defaultValue("monkeys").build();
    final NDSDataLakeCreateOrUpdateAndonCordView andonCord2 =
        NDSDataLakeCreateOrUpdateAndonCordView.builder()
            .name("feature_b")
            .state("disabled")
            .value(value2)
            .build();

    final boolean override2 = false;
    final CreateOrUpdateRequest request2 =
        _dataLakeAdminApiClient.makeCreateOrUpdateRequest(andonCord2, override2);

    final CreateOrUpdateRequest expected2 =
        CreateOrUpdateRequest.newBuilder()
            .setName("feature_b")
            .setState(AndonCordState.ANDON_CORD_STATE_DISABLED)
            .setDefaultValue("{\"defaultValue\":\"monkeys\"}")
            .setOverride(false)
            .build();

    assertEquals(expected2, request2);

    assertThrows(
        IllegalArgumentException.class,
        () -> {
          final NDSDataLakeCreateOrUpdateAndonCordView andonCord =
              NDSDataLakeCreateOrUpdateAndonCordView.builder()
                  .name("feature_b")
                  .state("bad-state")
                  .build();
          _dataLakeAdminApiClient.makeCreateOrUpdateRequest(andonCord, false);
        });

    assertThrows(
        IllegalArgumentException.class,
        () -> {
          final NDSDataLakeCreateOrUpdateAndonCordView andonCord =
              NDSDataLakeCreateOrUpdateAndonCordView.builder().state("enabled").build();
          _dataLakeAdminApiClient.makeCreateOrUpdateRequest(andonCord, false);
        });

    assertThrows(
        IllegalArgumentException.class,
        () -> {
          final NDSDataLakeCreateOrUpdateAndonCordValueDataSetView dataSet1 =
              NDSDataLakeCreateOrUpdateAndonCordValueDataSetView.builder()
                  .dataSetName("a")
                  .dataSetValue(100)
                  .projectId("not-a-valid-project-id")
                  .build();
          final NDSDataLakeCreateOrUpdateAndonCordValueView value =
              NDSDataLakeCreateOrUpdateAndonCordValueView.builder()
                  .defaultValue(5)
                  .dataSets(List.of(dataSet1))
                  .build();

          final NDSDataLakeCreateOrUpdateAndonCordView andonCord =
              NDSDataLakeCreateOrUpdateAndonCordView.builder()
                  .name("feature_b")
                  .value(value)
                  .build();
          _dataLakeAdminApiClient.makeCreateOrUpdateRequest(andonCord, false);
        });
  }

  @Test
  public void testListDataSets() throws DataLakeAdminApiException {
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.LIST_DATA_SETS);

    final StorageServiceGrpc.StorageServiceBlockingStub awsStub =
        _awsGrpcClient.getStorageServiceStub();
    final StorageServiceGrpc.StorageServiceBlockingStub azureStub =
        _azureGrpcClient.getStorageServiceStub();
    final StorageServiceGrpc.StorageServiceBlockingStub gcpStub =
        _gcpGrpcClient.getStorageServiceStub();

    {
      DataLakeAdminApiException nullDataSetsException =
          assertThrows(
              DataLakeAdminApiException.class,
              () -> {
                _dataLakeAdminApiClient.listDataSets(null);
              });
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, nullDataSetsException.getErrorCode());
    }

    {
      DataLakeAdminApiException emptyDataSetsException =
          assertThrows(
              DataLakeAdminApiException.class,
              () -> {
                _dataLakeAdminApiClient.listDataSets("");
              });
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, emptyDataSetsException.getErrorCode());
    }

    {
      // Test when project doesn't exist
      final String nonExistentProjectId = "686d3be41eacce5e1877d199";
      when(_groupSvc.findById(any(ObjectId.class))).thenReturn(null);

      DataLakeAdminApiException projectNotFoundException =
          assertThrows(
              DataLakeAdminApiException.class,
              () -> {
                _dataLakeAdminApiClient.listDataSets(nonExistentProjectId);
              });
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, projectNotFoundException.getErrorCode());
      assertTrue(projectNotFoundException.getMessage().contains("does not exist"));
    }

    final Timestamp createdAtTimestamp =
        Timestamp.newBuilder().setSeconds(1672531200L).setNanos(0).build();

    final String projectId = "686d3be41eacce5e1877d188";

    // Mock the Group to exist
    final Group mockGroup = mock(Group.class);
    when(_groupSvc.findById(any(ObjectId.class))).thenReturn(mockGroup);

    final ListDataSetsResponse awsEnabledDatasets =
        ListDataSetsResponse.newBuilder()
            .addDataSets(
                ListDataSetsResponse.DataSet.newBuilder()
                    .setDataSetLocation(
                        MetadataLocation.newBuilder()
                            .setProvider("aws")
                            .setRegion("us-east-1")
                            .build())
                    .setDataSetName("dataset_a")
                    .setCreatedAt(createdAtTimestamp)
                    .setProjectId(projectId)
                    .build())
            .addDataSets(
                ListDataSetsResponse.DataSet.newBuilder()
                    .setDataSetLocation(
                        MetadataLocation.newBuilder()
                            .setProvider("aws")
                            .setRegion("us-west-2")
                            .build())
                    .setDataSetName("dataset_b")
                    .setCreatedAt(createdAtTimestamp)
                    .setProjectId(projectId)
                    .build())
            .build();
    final ListDataSetsResponse azureEnabledDatasets =
        ListDataSetsResponse.newBuilder()
            .addDataSets(
                ListDataSetsResponse.DataSet.newBuilder()
                    .setDataSetLocation(
                        MetadataLocation.newBuilder()
                            .setProvider("azure")
                            .setRegion("eastus2")
                            .build())
                    .setDataSetName("dataset_c")
                    .setCreatedAt(createdAtTimestamp)
                    .setProjectId(projectId)
                    .build())
            .build();
    final ListDataSetsResponse gcpEnabledDatasets = ListDataSetsResponse.newBuilder().build();

    when(awsStub.listDataSets(ListDataSetsRequest.newBuilder().setProjectId(projectId).build()))
        .thenReturn(awsEnabledDatasets);
    when(awsStub.listDataSets(
            ListDataSetsRequest.newBuilder().setProjectId(projectId).setEnabledOnly(true).build()))
        .thenReturn(awsEnabledDatasets);
    when(azureStub.listDataSets(ListDataSetsRequest.newBuilder().setProjectId(projectId).build()))
        .thenReturn(azureEnabledDatasets);
    when(azureStub.listDataSets(
            ListDataSetsRequest.newBuilder().setProjectId(projectId).setEnabledOnly(true).build()))
        .thenReturn(azureEnabledDatasets);
    when(gcpStub.listDataSets(ListDataSetsRequest.newBuilder().setProjectId(projectId).build()))
        .thenReturn(gcpEnabledDatasets);
    when(gcpStub.listDataSets(
            ListDataSetsRequest.newBuilder().setProjectId(projectId).setEnabledOnly(true).build()))
        .thenReturn(gcpEnabledDatasets);

    final List<NDSDataLakeDataSetView> resp1 = _dataLakeAdminApiClient.listDataSets(projectId);

    verify(_dataLakeAdminApiClient, never()).incrementAdminApiExceptionCounter(any(), any(), any());
    verify(_dataLakeAdminApiClient, times(6)).startDurationTimer(eq(Methods.LIST_DATA_SETS));
    verify(timer, times(6)).observeDuration();
    assertEquals(resp1.size(), 3);

    resp1.sort(Comparator.comparing(NDSDataLakeDataSetView::getDataSetName));
    final NDSDataLakeDataSetView datasetA = resp1.get(0);
    assertEquals(datasetA.getDataSetName(), "dataset_a");
    assertEquals(datasetA.getDataSetLocationProvider(), "aws");
    assertEquals(datasetA.getDataSetLocationRegion(), "us-east-1");
    assertNotNull(datasetA.getCreatedAt());
    assertTrue(datasetA.getEnabled());

    final NDSDataLakeDataSetView datasetB = resp1.get(1);
    assertEquals(datasetB.getDataSetName(), "dataset_b");
    assertEquals(datasetB.getDataSetLocationProvider(), "aws");
    assertEquals(datasetB.getDataSetLocationRegion(), "us-west-2");
    assertTrue(datasetB.getEnabled());

    final NDSDataLakeDataSetView datasetC = resp1.get(2);
    assertEquals(datasetC.getDataSetName(), "dataset_c");
    assertEquals(datasetC.getDataSetLocationProvider(), "azure");
    assertEquals(datasetC.getDataSetLocationRegion(), "eastus2");
    assertTrue(datasetC.getEnabled());
  }

  @Test
  public void testListDataSetsWithDeletedDataSet() throws DataLakeAdminApiException {
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.LIST_DATA_SETS);

    final StorageServiceGrpc.StorageServiceBlockingStub awsStub =
        _awsGrpcClient.getStorageServiceStub();
    final StorageServiceGrpc.StorageServiceBlockingStub azureStub =
        _azureGrpcClient.getStorageServiceStub();
    final StorageServiceGrpc.StorageServiceBlockingStub gcpStub =
        _gcpGrpcClient.getStorageServiceStub();

    final Timestamp createdAtTimestamp =
        Timestamp.newBuilder().setSeconds(1672531200L).setNanos(0).build();
    final Timestamp deletedAtTimestamp =
        Timestamp.newBuilder().setSeconds(1672617600L).setNanos(0).build();

    final String projectId = "686d3be41eacce5e1877d189";

    // Mock the Group to exist
    final Group mockGroup = mock(Group.class);
    when(_groupSvc.findById(any(ObjectId.class))).thenReturn(mockGroup);

    when(awsStub.listDataSets(ListDataSetsRequest.newBuilder().setProjectId(projectId).build()))
        .thenReturn(
            ListDataSetsResponse.newBuilder()
                .addDataSets(
                    ListDataSetsResponse.DataSet.newBuilder()
                        .setDataSetLocation(
                            MetadataLocation.newBuilder()
                                .setProvider("aws")
                                .setRegion("us-east-1")
                                .build())
                        .setDataSetName("deleted_dataset")
                        .setCreatedAt(createdAtTimestamp)
                        .setDeletedAt(deletedAtTimestamp)
                        .setProjectId(projectId)
                        .build())
                .build());
    when(awsStub.listDataSets(
            ListDataSetsRequest.newBuilder().setProjectId(projectId).setEnabledOnly(true).build()))
        .thenReturn(ListDataSetsResponse.newBuilder().build());
    when(azureStub.listDataSets(ListDataSetsRequest.newBuilder().setProjectId(projectId).build()))
        .thenReturn(ListDataSetsResponse.newBuilder().build());
    when(azureStub.listDataSets(
            ListDataSetsRequest.newBuilder().setProjectId(projectId).setEnabledOnly(true).build()))
        .thenReturn(ListDataSetsResponse.newBuilder().build());
    when(gcpStub.listDataSets(ListDataSetsRequest.newBuilder().setProjectId(projectId).build()))
        .thenReturn(ListDataSetsResponse.newBuilder().build());
    when(gcpStub.listDataSets(
            ListDataSetsRequest.newBuilder().setProjectId(projectId).setEnabledOnly(true).build()))
        .thenReturn(ListDataSetsResponse.newBuilder().build());

    final List<NDSDataLakeDataSetView> resp = _dataLakeAdminApiClient.listDataSets(projectId);

    verify(_dataLakeAdminApiClient, never()).incrementAdminApiExceptionCounter(any(), any(), any());
    verify(_dataLakeAdminApiClient, times(6)).startDurationTimer(eq(Methods.LIST_DATA_SETS));
    verify(timer, times(6)).observeDuration();
    assertEquals(resp.size(), 1);

    final NDSDataLakeDataSetView deletedDataset = resp.get(0);
    assertEquals(deletedDataset.getDataSetName(), "deleted_dataset");
    assertNotNull(deletedDataset.getCreatedAt());
    assertNotNull(deletedDataset.getDeletedAt());
    assertFalse(deletedDataset.getEnabled());
  }

  @Test
  public void testGetTenantSettings() throws DataLakeAdminApiException {
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.GET_TENANT_SETTINGS);

    final TenantSettingsServiceBlockingStub awsStub = _awsGrpcClient.getTenantSettingsServiceStub();
    when(awsStub.getSettings(any()))
        .thenReturn(
            GetSettingsResponse.newBuilder()
                .setSettings(
                    Settings.newBuilder()
                        .setMaxConnections(1000)
                        .setQueryExecutionBranchingDepth(2)
                        .setParquetWriterVersion(1)
                        .build())
                .build());

    final ObjectId tenantId = new ObjectId();
    final NDSDataLakeTenantSettingsView tenantSettings =
        _dataLakeAdminApiClient.getTenantSettings(
            new NDSDataLakeTenantBuilder().tenantId(tenantId).build());
    verify(_dataLakeAdminApiClient, never()).incrementAdminApiExceptionCounter(any(), any(), any());
    verify(_dataLakeAdminApiClient, times(1)).startDurationTimer(eq(Methods.GET_TENANT_SETTINGS));
    verify(timer, times(1)).observeDuration();
    assertEquals(1000, tenantSettings.getMaxConnections());
    assertEquals(2, tenantSettings.getQueryExecutionBranchingDepth());
    assertEquals(1, tenantSettings.getParquetWriterVersion());
  }

  @Test
  public void testMakeSetSettingsRequest() throws Exception {
    final NDSDataLakeTenant tenant = mock(NDSDataLakeTenant.class);
    final ObjectId tenantId = new ObjectId();
    when(tenant.getTenantId()).thenReturn(tenantId);

    final String modifiedAtString = "2025-06-30T17:26:44.387Z";
    final NDSDataLakeTenantSettingsView tenantSettings1 =
        NDSDataLakeTenantSettingsView.builder().modifiedAt(modifiedAtString).build();

    final SetSettingsRequest request1 =
        _dataLakeAdminApiClient.makeSetSettingsRequest(tenant, tenantSettings1);
    assertEquals(tenantId.toHexString(), request1.getTenantId().getValue());
    assertEquals(
        modifiedAtString, _dataLakeAdminApiClient.toISOString(request1.getLastModifiedAt()));

    final NDSDataLakeTenantSettingsView tenantSettings2 =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAtString)
            .maxConcurrentQueries(10)
            .maxConnections(10)
            .allowUnlimitedConcurrentQueries(false)
            .parquetWriterVersion(2)
            .tracingEnabled(true)
            .cursorMaxFileSize(10)
            .cursorMaxFiles(1000)
            .cursorMaxWaitTimeForAvailableSpace(1000)
            .queryExecutionBranchingDepth(2)
            .queryExecutionBranchingFactor(4)
            .queryExecutionMaxConcurrency(10)
            .queryExecutionMaxSerialNum(10)
            .queryExecutionMaxSerialSize(10)
            .queryRoutingStrategy("round_robin")
            .build();

    final SetSettingsRequest request2 =
        _dataLakeAdminApiClient.makeSetSettingsRequest(tenant, tenantSettings2);
    assertEquals(tenantId.toHexString(), request2.getTenantId().getValue());
    assertEquals(
        modifiedAtString, _dataLakeAdminApiClient.toISOString(request2.getLastModifiedAt()));

    assertThrows(
        DataLakeAdminApiException.class,
        () -> {
          NDSDataLakeTenantSettingsView tenantSettings3 =
              NDSDataLakeTenantSettingsView.builder().build();
          _dataLakeAdminApiClient.makeSetSettingsRequest(tenant, tenantSettings3);
        });
  }

  @Test
  public void testListCurrentOps() throws Exception {
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    doReturn(timer).when(_dataLakeAdminApiClient).startDurationTimer(Methods.LIST_CURRENT_OPS);

    final ObjectId tenantId1 = new ObjectId();
    final Timestamp timestamp = Timestamp.newBuilder().setSeconds(1000).build();
    final Document clientMetadata = new Document();
    clientMetadata.append("foo", "bar");

    final DocumentCodec codec = new DocumentCodec();
    final BasicOutputBuffer buffer = new BasicOutputBuffer();
    codec.encode(new BsonBinaryWriter(buffer), clientMetadata, EncoderContext.builder().build());
    final ByteArrayOutputStream os = new ByteArrayOutputStream();
    buffer.pipe(os);

    ByteString byteString = ByteString.copyFrom(os.toByteArray());

    CurrentOp op1 =
        CurrentOp.newBuilder()
            .setTenantId(TenantID.newBuilder().setValue(tenantId1.toHexString()).build())
            .setCorrelationId(CorrelationID.newBuilder().setValue(tenantId1.toHexString()).build())
            .setClient("client1")
            .setClientMetadata(byteString)
            .setConnectionId(1L)
            .setNamespace("foo.bar")
            .setUser(OpUser.newBuilder().setName("user1").setDatabaseName("db1").build())
            .setStartTime(timestamp)
            .build();
    CurrentOp op2 =
        CurrentOp.newBuilder()
            .setTenantId(TenantID.newBuilder().setValue(tenantId1.toHexString()).build())
            .setCorrelationId(CorrelationID.newBuilder().setValue(tenantId1.toHexString()).build())
            .setClient("client2")
            .setClientMetadata(byteString)
            .setConnectionId(2L)
            .setNamespace("foo.bar")
            .setUser(OpUser.newBuilder().setName("user2").setDatabaseName("db2").build())
            .setStartTime(timestamp)
            .build();
    final OpServiceBlockingStub stub = _awsGrpcClient.getOpServiceStub();
    com.xgen.mhouse.services.op.v1.Models.ListResponse resp1 =
        com.xgen.mhouse.services.op.v1.Models.ListResponse.newBuilder()
            .addOps(op1)
            .addOps(op2)
            .setComplete(true)
            .build();
    com.xgen.mhouse.services.op.v1.Models.ListResponse resp2 =
        com.xgen.mhouse.services.op.v1.Models.ListResponse.newBuilder()
            .addOps(op1)
            .addOps(op2)
            .setComplete(true)
            .build();

    NDSDataLakeTenant tenantMock = mock(NDSDataLakeTenant.class);
    when(tenantMock.getTenantId()).thenReturn(tenantId1);

    Iterator<com.xgen.mhouse.services.op.v1.Models.ListResponse> itr =
        List.of(resp1, resp2).iterator();
    when(stub.list(any())).thenReturn(itr);

    List<NDSDataLakeCurrentOpView> currentOps = _dataLakeAdminApiClient.listCurrentOps(tenantMock);
    assertEquals(4, currentOps.size());
    assertEquals(currentOps.get(0), currentOps.get(2));
    assertEquals(currentOps.get(1), currentOps.get(3));

    NDSDataLakeCurrentOpView actualOp1 = currentOps.get(0);
    assertEquals(tenantId1.toHexString(), actualOp1.getTenantId());
    assertEquals(tenantId1.toHexString(), actualOp1.getCorrelationId());
    assertEquals("client1", actualOp1.getClient());
    assertEquals(clientMetadata, actualOp1.getClientMetadata());
    assertEquals(1L, actualOp1.getConnectionId());
    assertEquals("foo.bar", actualOp1.getNamespace());
    assertEquals("user1", actualOp1.getUser().getName());
    assertEquals("db1", actualOp1.getUser().getDatabaseName());
    assertEquals(_dataLakeAdminApiClient.toISOString(timestamp), actualOp1.getStartTime());

    NDSDataLakeCurrentOpView actualOp2 = currentOps.get(1);
    assertEquals(tenantId1.toHexString(), actualOp2.getTenantId());
    assertEquals(tenantId1.toHexString(), actualOp2.getCorrelationId());
    assertEquals("client2", actualOp2.getClient());
    assertEquals(actualOp2.getClientMetadata(), clientMetadata);
    assertEquals(2L, actualOp2.getConnectionId());
    assertEquals("foo.bar", actualOp2.getNamespace());
    assertEquals("user2", actualOp2.getUser().getName());
    assertEquals("db2", actualOp2.getUser().getDatabaseName());
    assertEquals(_dataLakeAdminApiClient.toISOString(timestamp), actualOp2.getStartTime());
  }

  private DataLakeGrpcClient generateGrpcClient(final AppSettings pAppSettings) {
    final TenantServiceBlockingStub tenantServiceStub =
        mock(TenantServiceGrpc.TenantServiceBlockingStub.class);
    when(tenantServiceStub.withMaxInboundMessageSize(anyInt())).thenReturn(tenantServiceStub);
    when(tenantServiceStub.withInterceptors(any())).thenReturn(tenantServiceStub);
    when(tenantServiceStub.withDeadlineAfter(anyLong(), any())).thenReturn(tenantServiceStub);
    final TenantSettingsServiceBlockingStub tenantSettingsServiceStub =
        mock(TenantSettingsServiceGrpc.TenantSettingsServiceBlockingStub.class);
    when(tenantSettingsServiceStub.withMaxInboundMessageSize(anyInt()))
        .thenReturn(tenantSettingsServiceStub);
    when(tenantSettingsServiceStub.withInterceptors(any())).thenReturn(tenantSettingsServiceStub);
    when(tenantSettingsServiceStub.withDeadlineAfter(anyLong(), any()))
        .thenReturn(tenantSettingsServiceStub);
    final StorageConfigServiceGrpc.StorageConfigServiceBlockingStub storageConfigServiceStub =
        mock(StorageConfigServiceGrpc.StorageConfigServiceBlockingStub.class);
    when(storageConfigServiceStub.withMaxInboundMessageSize(anyInt()))
        .thenReturn(storageConfigServiceStub);
    when(storageConfigServiceStub.withInterceptors(any())).thenReturn(storageConfigServiceStub);
    when(storageConfigServiceStub.withDeadlineAfter(anyLong(), any()))
        .thenReturn(storageConfigServiceStub);
    final StorageServiceBlockingStub storageServiceStub =
        mock(StorageServiceGrpc.StorageServiceBlockingStub.class);
    when(storageServiceStub.withMaxInboundMessageSize(anyInt())).thenReturn(storageServiceStub);
    when(storageServiceStub.withInterceptors(any())).thenReturn(storageServiceStub);
    when(storageServiceStub.withDeadlineAfter(anyLong(), any())).thenReturn(storageServiceStub);
    final StorageServiceGrpc.StorageServiceStub storageServiceAsyncStub =
        mock(StorageServiceGrpc.StorageServiceStub.class);
    when(storageServiceAsyncStub.withMaxInboundMessageSize(anyInt()))
        .thenReturn(storageServiceAsyncStub);
    when(storageServiceAsyncStub.withInterceptors(any())).thenReturn(storageServiceAsyncStub);
    when(storageServiceAsyncStub.withDeadlineAfter(anyLong(), any()))
        .thenReturn(storageServiceAsyncStub);
    final LogsServiceGrpc.LogsServiceStub logsServiceStub =
        mock(LogsServiceGrpc.LogsServiceStub.class);
    when(logsServiceStub.withMaxInboundMessageSize(anyInt())).thenReturn(logsServiceStub);
    when(logsServiceStub.withInterceptors(any())).thenReturn(logsServiceStub);
    when(logsServiceStub.withDeadlineAfter(anyLong(), any())).thenReturn(logsServiceStub);
    final MetricsServiceBlockingStub metricsServiceStub =
        mock(MetricsServiceGrpc.MetricsServiceBlockingStub.class);
    when(metricsServiceStub.withMaxInboundMessageSize(anyInt())).thenReturn(metricsServiceStub);
    when(metricsServiceStub.withInterceptors(any())).thenReturn(metricsServiceStub);
    when(metricsServiceStub.withDeadlineAfter(anyLong(), any())).thenReturn(metricsServiceStub);
    final com.xgen.mhouse.services.querymetrics.admin.grpc.v1.MetricsServiceGrpc
            .MetricsServiceBlockingStub
        metricsV1ServiceStub =
            mock(
                com.xgen.mhouse.services.querymetrics.admin.grpc.v1.MetricsServiceGrpc
                    .MetricsServiceBlockingStub.class);
    when(metricsV1ServiceStub.withMaxInboundMessageSize(anyInt())).thenReturn(metricsV1ServiceStub);
    when(metricsV1ServiceStub.withInterceptors(any())).thenReturn(metricsV1ServiceStub);
    when(metricsV1ServiceStub.withDeadlineAfter(anyLong(), any())).thenReturn(metricsV1ServiceStub);
    final UsageLimitsServiceBlockingStub usageLimitsServiceStub =
        mock(UsageLimitsServiceBlockingStub.class);
    when(usageLimitsServiceStub.withMaxInboundMessageSize(anyInt()))
        .thenReturn(usageLimitsServiceStub);
    when(usageLimitsServiceStub.withInterceptors(any())).thenReturn(usageLimitsServiceStub);
    when(usageLimitsServiceStub.withDeadlineAfter(anyLong(), any()))
        .thenReturn(usageLimitsServiceStub);
    final RegionsServiceBlockingStub regionsServiceStub = mock(RegionsServiceBlockingStub.class);
    when(regionsServiceStub.withMaxInboundMessageSize(anyInt())).thenReturn(regionsServiceStub);
    when(regionsServiceStub.withInterceptors(any())).thenReturn(regionsServiceStub);
    when(regionsServiceStub.withDeadlineAfter(anyLong(), any())).thenReturn(regionsServiceStub);
    final SchemaServiceGrpc.SchemaServiceStub schemaServiceAsyncStub =
        mock(SchemaServiceGrpc.SchemaServiceStub.class);
    when(schemaServiceAsyncStub.withMaxInboundMessageSize(anyInt()))
        .thenReturn(schemaServiceAsyncStub);
    when(schemaServiceAsyncStub.withInterceptors(any())).thenReturn(schemaServiceAsyncStub);
    when(schemaServiceAsyncStub.withDeadlineAfter(anyLong(), any()))
        .thenReturn(schemaServiceAsyncStub);
    final SchemaServiceGrpc.SchemaServiceBlockingStub schemaServiceStub =
        mock(SchemaServiceGrpc.SchemaServiceBlockingStub.class);
    when(schemaServiceStub.withMaxInboundMessageSize(anyInt())).thenReturn(schemaServiceStub);
    when(schemaServiceStub.withInterceptors(any())).thenReturn(schemaServiceStub);
    when(schemaServiceStub.withDeadlineAfter(anyLong(), any())).thenReturn(schemaServiceStub);
    final PrivateLinkServiceBlockingStub privateLinkServiceStub =
        mock(PrivateLinkServiceBlockingStub.class);
    when(privateLinkServiceStub.withMaxInboundMessageSize(anyInt()))
        .thenReturn(privateLinkServiceStub);
    when(privateLinkServiceStub.withInterceptors(any())).thenReturn(privateLinkServiceStub);
    when(privateLinkServiceStub.withDeadlineAfter(anyLong(), any()))
        .thenReturn(privateLinkServiceStub);
    final AndonCordServiceBlockingStub andonCordServiceStub =
        mock(AndonCordServiceBlockingStub.class);
    when(andonCordServiceStub.withMaxInboundMessageSize(anyInt())).thenReturn(andonCordServiceStub);
    when(andonCordServiceStub.withInterceptors(any())).thenReturn(andonCordServiceStub);
    when(andonCordServiceStub.withDeadlineAfter(anyLong(), any())).thenReturn(andonCordServiceStub);
    final OpServiceBlockingStub opServiceStub = mock(OpServiceBlockingStub.class);
    when(opServiceStub.withMaxInboundMessageSize(anyInt())).thenReturn(opServiceStub);
    when(opServiceStub.withInterceptors(any())).thenReturn(opServiceStub);
    when(opServiceStub.withDeadlineAfter(anyLong(), any())).thenReturn(opServiceStub);
    return new DataLakeGrpcClient(
        tenantServiceStub,
        tenantSettingsServiceStub,
        storageConfigServiceStub,
        storageServiceStub,
        storageServiceAsyncStub,
        logsServiceStub,
        metricsServiceStub,
        metricsV1ServiceStub,
        usageLimitsServiceStub,
        regionsServiceStub,
        schemaServiceAsyncStub,
        schemaServiceStub,
        privateLinkServiceStub,
        andonCordServiceStub,
        opServiceStub,
        pAppSettings,
        CloudProvider.AWS);
  }
}
