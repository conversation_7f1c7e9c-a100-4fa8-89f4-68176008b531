package com.xgen.svc.nds.svc.backup;

import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;

import com.xgen.cloud.atm.core._public.svc.AutomationMongoDbVersionSvc;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.deployment._public.model.DefaultVersionConfiguration;
import com.xgen.cloud.deployment._public.model.MongoDbVersion;
import com.xgen.cloud.deployment._public.model.VersionManifest;
import com.xgen.svc.mms.util.UnitTestUtils;
import com.xgen.svc.nds.svc.BackupSvc;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.mockito.Mockito;

public class BackupUnitTestHelpers {
  public static final ObjectId GROUP_ID = new ObjectId();

  public static final List<MongoDbVersion> AVAILABLE_VERSIONS =
      Stream.of(
              "5.0.0-ent",
              "5.0.13-ent",
              "5.1.0-ent",
              "5.3.1-ent",
              "5.3.2-ent",
              "5.1.1-ent",
              "5.3.0-ent",
              "5.0.0-ent",
              "5.1.2-ent",
              "5.2.0-ent",
              "5.2.1-ent",
              "6.0.0-ent",
              "6.0.10-ent",
              "6.1.3-ent",
              "6.2.1-ent",
              "6.3.2-ent",
              "7.0.2-ent",
              "7.1.3-ent")
          .map(MongoDbVersion::forName)
          .collect(Collectors.toList());

  public static AutomationMongoDbVersionSvc getMockAutomationMongoDbVersionSvc() {
    return getMockAutomationMongoDbVersionSvc(AVAILABLE_VERSIONS, VersionUtils.parse("7.1.3"));
  }

  public static AutomationMongoDbVersionSvc getMockAutomationMongoDbVersionSvc(
      List<MongoDbVersion> versions, VersionUtils.Version latestContinuousReleaseVersion) {
    final VersionManifest versionManifest = mock(VersionManifest.class);
    Mockito.doReturn(versions).when(versionManifest).getEnterpriseVersions();
    doReturn(latestContinuousReleaseVersion).when(versionManifest).getCurrentLatestReleaseVersion();
    final DefaultVersionConfiguration defaultVersionConfiguration =
        mock(DefaultVersionConfiguration.class);
    doReturn(versionManifest).when(defaultVersionConfiguration).getVersionManifest();

    final AutomationMongoDbVersionSvc svc = mock(AutomationMongoDbVersionSvc.class);
    doReturn(defaultVersionConfiguration).when(svc).getDefaultVersionConfiguration(anyBoolean());
    return svc;
  }

  public static BackupSvc getMockBackupSvc() {
    return spy(
        UnitTestUtils.create(BackupSvc.class).withArgs(getMockAutomationMongoDbVersionSvc()));
  }
}
