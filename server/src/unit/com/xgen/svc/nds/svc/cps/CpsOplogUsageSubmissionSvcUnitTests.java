package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.cps.billing._private.dao.CpsBillingPropertiesDao;
import com.xgen.cloud.cps.billing._private.dao.CpsOplogUsageSummaryDao;
import com.xgen.cloud.cps.billing._public.model.CpsBillingProperty;
import com.xgen.module.metering.client.svc.IMeterReportSvc;
import com.xgen.module.metering.client.svc.api_2023_05_03.MeterReportSvc;
import com.xgen.module.metering.common.exception.MeterErrorCode;
import com.xgen.module.metering.common.exception.MeterSvcException;
import com.xgen.module.metering.common.model.MeterUsage;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

@ExtendWith(MockitoExtension.class)
public class CpsOplogUsageSubmissionSvcUnitTests {
  @Mock private AppSettings appSettings;

  @Spy private IMeterReportSvc meterReportSvc = new MeterReportSvc(null);

  @Mock private CpsBillingPropertiesDao cpsBillingPropertiesDao;
  @Mock private CpsOplogUsageSummaryDao cpsOplogUsageSummaryDao;

  @Spy @InjectMocks private CpsOplogUsageSubmissionSvc underTest;

  @Test
  public void testSubmitPitUsages() throws MeterSvcException {
    doReturn(true).when(underTest).isCpsUsageSubmissionEnabled();

    var e = assertThrows(RuntimeException.class, () -> underTest.submitOplogUsage());
    assertEquals(
        "Not running pit usage submission job because the last successful date for the oplog"
            + " collection is not set in the db",
        e.getMessage());

    final CpsBillingProperty collectionBillingProperty = mock(CpsBillingProperty.class);

    when(cpsBillingPropertiesDao.findOneById(CpsBillingPropertiesDao.OPLOG_USAGE_COLLECTION))
        .thenReturn(Optional.of(collectionBillingProperty));

    e = assertThrows(RuntimeException.class, () -> underTest.submitOplogUsage());
    assertEquals(
        "Not running pit usage submission job because the last successful date on the oplog"
            + " submission job is not set in the db",
        e.getMessage());

    final CpsBillingProperty submissionBillingProperty = mock(CpsBillingProperty.class);
    when(cpsBillingPropertiesDao.findOneById(CpsBillingPropertiesDao.OPLOG_USAGE_SUBMISSION))
        .thenReturn(Optional.of(submissionBillingProperty));

    when(collectionBillingProperty.getLastSuccessfulDate())
        .thenReturn(LocalDate.parse("2022-02-18"));
    when(submissionBillingProperty.getLastSuccessfulDate())
        .thenReturn(LocalDate.parse("2022-02-19"));

    e = assertThrows(RuntimeException.class, () -> underTest.submitOplogUsage());
    assertEquals(
        "Last oplog usage submission date if past the last oplog usage collection date",
        e.getMessage());

    when(submissionBillingProperty.getLastSuccessfulDate())
        .thenReturn(LocalDate.parse("2022-02-18"));

    // submission is up-to-date with the collection
    underTest.submitOplogUsage();
    verify(underTest, times(0)).submitOplogUsageInternal(any());

    doNothing().when(underTest).submitOplogUsageInternal(any());

    when(collectionBillingProperty.getLastSuccessfulDate())
        .thenReturn(LocalDate.parse("2022-02-20"));

    underTest.submitOplogUsage();
    verify(underTest, times(2)).submitOplogUsageInternal(any());
    verify(underTest, times(1))
        .submitOplogUsageInternal(submissionBillingProperty.getLastSuccessfulDate().plusDays(1));
    verify(underTest, times(1))
        .submitOplogUsageInternal(submissionBillingProperty.getLastSuccessfulDate().plusDays(2));
  }

  @Test
  public void testSubmitPitUsagesInternal() throws MeterSvcException {
    final List<DBObject> results = new ArrayList<>(Collections.nCopies(5004, new BasicDBObject()));

    final AtomicInteger index = new AtomicInteger(0);

    final DBCursor usageCursor = mock(DBCursor.class);
    when(cpsOplogUsageSummaryDao.getAllUsageByDate(any())).thenReturn(usageCursor);

    when(usageCursor.hasNext())
        .thenAnswer((Answer<Boolean>) pInvocationOnMock -> index.get() < results.size());

    when(usageCursor.next())
        .thenAnswer((Answer<DBObject>) pInvocationOnMock -> results.get(index.getAndIncrement()));

    final MeterUsage dummyMeterUsage = mock(MeterUsage.class);

    doReturn(dummyMeterUsage).when(underTest).toMeterUsage(any(), any());
    doNothing().when(underTest).submitMeterUsageWithRetry(any());

    final LocalDate date = LocalDate.parse("2022-02-20");
    underTest.submitOplogUsageInternal(date);

    verify(underTest, times(6)).submitMeterUsageWithRetry(any());
  }

  @Test
  public void testSubmitMeterUsageWithRetry_happy() throws MeterSvcException {
    doNothing().when(meterReportSvc).submitMeterUsage(any(), any());

    final List<MeterUsage> meterUsages =
        new ArrayList<>(Collections.nCopies(1000, mock(MeterUsage.class)));
    underTest.submitMeterUsageWithRetry(meterUsages);
    verify(meterReportSvc, times(1))
        .submitMeterUsage(meterUsages, CpsOplogUsageSubmissionSvc.METER_SVC_JOB_NAME);
  }

  @Test
  public void testSubmitMeterUsageWithRetry_sadWithRetry() throws MeterSvcException {
    doThrow(new MeterSvcException(MeterErrorCode.UNAVAILABLE))
        .when(meterReportSvc)
        .submitMeterUsage(any(), any());

    final List<MeterUsage> meterUsages =
        new ArrayList<>(Collections.nCopies(1000, mock(MeterUsage.class)));

    var e =
        assertThrows(
            RuntimeException.class, () -> underTest.submitMeterUsageWithRetry(meterUsages));
    verify(meterReportSvc, times(5))
        .submitMeterUsage(meterUsages, CpsOplogUsageSubmissionSvc.METER_SVC_JOB_NAME);
    assertEquals(
        "Exhausted max retry attempts for submitting to the metering service", e.getMessage());
  }

  @Test
  public void testSubmitMeterUsageWithRetry_sadNoRetry() throws MeterSvcException {
    final MeterSvcException meterSvcException =
        new MeterSvcException(MeterErrorCode.USAGE_START_TIME_END_TIME_NOT_ON_SAME_DAY);
    doThrow(meterSvcException).when(meterReportSvc).submitMeterUsage(any(), any());

    final List<MeterUsage> meterUsages =
        new ArrayList<>(Collections.nCopies(1000, mock(MeterUsage.class)));

    var e =
        assertThrows(
            MeterSvcException.class, () -> underTest.submitMeterUsageWithRetry(meterUsages));
    verify(meterReportSvc, times(1))
        .submitMeterUsage(meterUsages, CpsOplogUsageSubmissionSvc.METER_SVC_JOB_NAME);
    assertEquals(meterSvcException, e);
  }
}
