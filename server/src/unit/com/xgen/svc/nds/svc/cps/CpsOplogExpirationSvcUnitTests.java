package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.pit._public.model.PitSetting;
import com.xgen.cloud.cps.pit._public.model.PitStorage;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CpsOplogExpirationSvcUnitTests {
  @Mock private BackupSnapshotDao backupSnapshotDao;
  @Mock private BackupRestoreJobDao backupRestoreJobDao;
  @Mock private BackupSnapshot snapshot1;
  @Mock private BackupSnapshot snapshot2;
  @Mock private BackupJob backupJob;
  @Mock private CpsOplogStoreFactory cpsOplogStoreFactory;

  @Spy @InjectMocks private CpsOplogExpirationSvc cpsOplogExpirationSvc;

  @Test
  public void testReadyToInvalidateSlices() {

    when(snapshot1.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.IN_PROGRESS);
    when(snapshot2.getSnapshotStatus()).thenReturn(BackupSnapshot.Status.COMPLETED);

    // case 1: has in progress snapshot, no in progress PIT restore
    when(backupSnapshotDao.findActionableNonCopyByCluster(any(), any()))
        .thenReturn(Arrays.asList(snapshot1, snapshot2));
    when(backupRestoreJobDao.hasInProgressSourceClusterPITRestore(any(), any())).thenReturn(false);
    assertFalse(cpsOplogExpirationSvc.readyToInvalidateSlices(backupJob));

    // case 2: has no in progress snapshot, no in progress PIT restore
    when(backupSnapshotDao.findActionableNonCopyByCluster(any(), any()))
        .thenReturn(List.of(snapshot2));
    assertTrue(cpsOplogExpirationSvc.readyToInvalidateSlices(backupJob));

    // case 3: has no in progress snapshot, has in progress restore
    when(backupSnapshotDao.findActionableNonCopyByCluster(any(), any()))
        .thenReturn(List.of(snapshot2));
    when(backupRestoreJobDao.hasInProgressSourceClusterPITRestore(any(), any())).thenReturn(true);
    assertFalse(cpsOplogExpirationSvc.readyToInvalidateSlices(backupJob));

    // case 4: has in progress snapshot, has in progress restore
    when(backupSnapshotDao.findActionableNonCopyByCluster(any(), any()))
        .thenReturn(Arrays.asList(snapshot1, snapshot2));
    assertFalse(cpsOplogExpirationSvc.readyToInvalidateSlices(backupJob));
  }

  @Test
  public void testExpireSlicesInJob_error_handling() {

    final ObjectId clusterUniqueId = ObjectId.get();
    final String rsId1 = "rs1";
    final String rsId2 = "rs2";
    doReturn(Set.of(rsId1, rsId2)).when(backupJob).getAllRsIds();
    doReturn(ObjectId.get()).when(backupJob).getId();
    doReturn(ObjectId.get()).when(backupJob).getProjectId();
    doReturn(clusterUniqueId).when(backupJob).getClusterUniqueId();
    doReturn("cluster1").when(backupJob).getClusterName();
    final Date now = TimeUtils.fromISOString("2017-07-03T06:34:56Z");

    doThrow(mock(RuntimeException.class))
        .when(cpsOplogExpirationSvc)
        .expireSlicesInJob(now, backupJob, rsId1, 0.0, false);

    when(backupSnapshotDao.findActionableNonCopyByCluster(any(), any()))
        .thenReturn(Collections.emptyList());
    when(backupRestoreJobDao.hasInProgressSourceClusterPITRestore(any(), any())).thenReturn(false);

    doReturn(now).when(cpsOplogExpirationSvc).getNow();
    cpsOplogExpirationSvc.expireSlicesInJob(backupJob, 0.0, false);
    verify(cpsOplogExpirationSvc, times(1)).expireSlicesInJob(now, backupJob, rsId2, 0.0, false);
  }

  @Test
  public void testExpireSlicesInJob() {
    doNothing()
        .when(cpsOplogExpirationSvc)
        .expireSlicesInner(
            any(Date.class),
            any(BackupJob.class),
            anyString(),
            any(CpsOplogSliceMetadataDaoProxy.class),
            anyDouble());

    final BackupJob activeBackupJob = mock(BackupJob.class);
    final BackupJob queuedBackupJob = mock(BackupJob.class);
    final ObjectId clusterUniqueId = ObjectId.get();
    final String rsId = "rs2";
    doReturn(ObjectId.get()).when(queuedBackupJob).getProjectId();
    doReturn(clusterUniqueId).when(queuedBackupJob).getClusterUniqueId();

    final BackupJobDao backupJobDao = mock(BackupJobDao.class);
    final CpsOplogStoreFactory cpsOplogStoreFactory = mock(CpsOplogStoreFactory.class);
    when(backupJobDao.findActiveByClusterUniqueId(any(), any()))
        .thenReturn(Optional.of(activeBackupJob));
    doReturn(backupJobDao).when(cpsOplogExpirationSvc).getBackupJobDao();
    doReturn(cpsOplogStoreFactory).when(cpsOplogExpirationSvc).getCpsOplogStoreFactory();
    final CpsOplogSliceMetadataDaoProxy cpsOplogSliceMetadataDao =
        mock(CpsOplogSliceMetadataDaoProxy.class);
    doReturn(cpsOplogSliceMetadataDao)
        .when(cpsOplogStoreFactory)
        .getCpsSliceDaoProxy(any(), any(), any());

    final PitStorage storage1 = new PitStorage("metadata1", "blob1", "us-east");
    final PitStorage storage2 = new PitStorage("metadata2", "blob2", "us-west");
    final PitStorage storage2_copy = new PitStorage("metadata2", "blob1", "us-east-1");
    final PitSetting ps1 = mock(PitSetting.class);
    final PitSetting ps2 = mock(PitSetting.class);

    // Active Backup Job contains ps1(storage2)
    doReturn(ps1).when(activeBackupJob).getPitSettingByRsId(rsId);
    doReturn(List.of(storage2)).when(ps1).getAllPitStorages();

    // Queued Backup Job contains ps2(storage1, storage2_copy)
    doReturn(ps2).when(queuedBackupJob).getPitSettingByRsId(rsId);
    doReturn(List.of(storage1, storage2_copy)).when(ps2).getAllPitStorages();
    final Date now = TimeUtils.fromISOString("2017-07-03T06:34:56Z");

    cpsOplogExpirationSvc.expireSlicesInJob(now, queuedBackupJob, rsId, 0.0, true);

    // storage2_copy will be skipped since the same metadata config storage2 in active backup job
    verify(cpsOplogExpirationSvc, times(1))
        .expireSlicesInner(now, queuedBackupJob, rsId, cpsOplogSliceMetadataDao, 0.0);
  }
}
