package com.xgen.svc.nds.svc.alert.check.agent;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.xgen.cloud.alerts.checks.common._public.svc.Result;
import com.xgen.cloud.alerts.checks.common._public.svc.Result.WithoutState;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.util._public.util.DriverUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.model.GroupStorageConfig;
import com.xgen.cloud.monitoring.agent._public.model.alert.AgentAlertConfig;
import com.xgen.cloud.monitoring.agent._public.model.event.AgentEvent;
import com.xgen.cloud.monitoring.common._public.model.retention.RetentionPolicy;
import com.xgen.cloud.monitoring.metrics._private.dao.rrd.RetentionPolicyDao;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._private.dao.MTMClusterDao;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.NDSProxyAudit;
import com.xgen.cloud.nds.common._public.model.ProxyVersion;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.SharedMTMCluster;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.mms.model.grouptype.GroupType;
import com.xgen.svc.mms.svc.alert.GroupAlertProcessingContext;
import com.xgen.svc.mms.util.UnitTestUtils;
import com.xgen.svc.nds.svc.NDSLookupSvc;
import com.xgen.svc.nds.svc.NDSProxyAuditSvc;
import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.lang.RandomStringUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class NDSProxyDownUnitTests {
  private static final int PING_AGE_MINUTES = 1;

  private Organization _organization;
  private Group _group;

  @BeforeEach
  public void setup() {
    _organization = new Organization.Builder().id(new ObjectId()).name("Test").build();
    _group = new Group();
    _group.setName("Internal MTM0 Group Name"); // Intentionally contains literal "MTM"
    _group.setId(new ObjectId());
    _group.setGroupType(GroupType.NDS);
    _group.setOrgId(_organization.getId());
    final GroupStorageConfig groupStorageConfig =
        new GroupStorageConfig(GroupStorageConfig.Mode.V3, null);
    _group.setGroupStorageConfig(groupStorageConfig);
  }

  @Test
  public void doGetTargets() {
    final MTMClusterDao mtmClusterDao = mock(MTMClusterDao.class);
    final NDSProxyAuditSvc ndsProxyAuditSvc = mock(NDSProxyAuditSvc.class);

    final RetentionPolicyDao retentionPolicyDao = mock(RetentionPolicyDao.class);
    when(retentionPolicyDao.findCachedById(null)).thenReturn(mock(RetentionPolicy.class));

    final NDSLookupSvc ndsLookupSvc = mock(NDSLookupSvc.class);
    final NDSGroup ndsGroup = mock(NDSGroup.class);
    when(ndsGroup.isMTMHolder()).thenReturn(true);
    when(ndsLookupSvc.getNDSGroup(_group.getId())).thenReturn(Optional.of(ndsGroup));

    final GroupAlertProcessingContext context =
        UnitTestUtils.create(GroupAlertProcessingContext.class)
            .withArgs(
                _group,
                _organization,
                ndsProxyAuditSvc,
                mtmClusterDao,
                retentionPolicyDao,
                ndsLookupSvc);

    final NDSProxyDown ndsProxyDown = new NDSProxyDown(mock(AppSettings.class));

    when(mtmClusterDao.findClustersByGroupId(
            eq(_group.getId()), eq(DriverUtils.SECONDARY_PREFERRED_MINIMUM)))
        .thenReturn(null);
    assertEquals(Collections.emptyList(), ndsProxyDown.doGetTargets(context, null));

    final SharedMTMCluster sharedMTMCluster = mock(SharedMTMCluster.class);
    final ServerlessMTMCluster serverlessMtmCluster = mock(ServerlessMTMCluster.class);
    when(mtmClusterDao.findClustersByGroupId(
            eq(_group.getId()), eq(DriverUtils.SECONDARY_PREFERRED_MINIMUM)))
        .thenReturn(List.of(sharedMTMCluster, serverlessMtmCluster));
    when(sharedMTMCluster.getGroupId()).thenReturn(_group.getId());
    when(serverlessMtmCluster.getGroupId()).thenReturn(_group.getId());

    final String hostname = RandomStringUtils.randomAlphanumeric(15);
    final Map<String, Long> seenMap = Collections.singletonMap(hostname, 0L);
    final ProxyVersion proxyVersion = new ProxyVersion(0, seenMap);
    when(sharedMTMCluster.getProxyVersions())
        .thenReturn(Collections.singletonMap(RandomStringUtils.randomAlphabetic(10), proxyVersion));
    when(serverlessMtmCluster.getProxyVersions())
        .thenReturn(Collections.singletonMap(RandomStringUtils.randomAlphabetic(10), proxyVersion));

    final List<NDSProxyAudit> ndsProxyAudits = Collections.singletonList(new NDSProxyAudit());
    when(ndsProxyAuditSvc.findCurrent(
            _group.getId(), List.of(sharedMTMCluster, serverlessMtmCluster)))
        .thenReturn(ndsProxyAudits);
    assertEquals(ndsProxyAudits, ndsProxyDown.doGetTargets(context, null));

    // returns empty list for non-MTM
    when(ndsGroup.isMTMHolder()).thenReturn(false);
    assertEquals(0, ndsProxyDown.doGetTargets(context, null).size());

    // doesn't throw an NPE when nds group does not yet exist
    when(ndsLookupSvc.getNDSGroup(_group.getId())).thenReturn(Optional.empty());
    assertEquals(0, ndsProxyDown.doGetTargets(context, null).size());
  }

  @Test
  public void doGetTargets_matchingTag() {
    final NDSGroup ndsGroup = mock(NDSGroup.class);
    when(ndsGroup.isMTMHolder()).thenReturn(true);

    final Group group = new Group();
    group.setGroupType(GroupType.NDS);

    final GroupAlertProcessingContext context = mock(GroupAlertProcessingContext.class);
    when(context.getGroup()).thenReturn(group);
    when(context.getNDSGroup()).thenReturn(ndsGroup);
    when(context.getMtmClustersForGroup())
        .thenReturn(List.of(mock(SharedMTMCluster.class), mock(ServerlessMTMCluster.class)));
    when(context.getNDSProxyAudits())
        .thenReturn(Collections.singletonList(mock(NDSProxyAudit.class)));

    // Default: no tag configured on the environment
    final AppSettings settings1 = mock(AppSettings.class);
    when(settings1.getStrProp(eq("mms.alerts.NdsProxyDown.matchingGroupTag"), eq("")))
        .thenReturn("");
    final NDSProxyDown ndsProxyDown1 = new NDSProxyDown(settings1);

    final Collection<NDSProxyAudit> ndsProxyAudits1 = ndsProxyDown1.doGetTargets(context, null);
    assertEquals(
        1,
        ndsProxyAudits1.size(),
        "Expected one audit when no tag specified, was: " + ndsProxyAudits1);

    // Matching tag IS configured on the environment
    final AppSettings settings2 = mock(AppSettings.class);
    when(settings2.getStrProp(eq("mms.alerts.NdsProxyDown.matchingGroupTag"), eq("")))
        .thenReturn("A");
    final NDSProxyDown ndsProxyDown2 = new NDSProxyDown(settings2);

    // And Group doesn't have tag.
    final Collection<NDSProxyAudit> ndsProxyAudits2 = ndsProxyDown2.doGetTargets(context, null);
    assertEquals(
        0,
        ndsProxyAudits2.size(),
        "Expected one audit when no tag specified, was: " + ndsProxyAudits2);

    // Group has different tag.
    group.setTags(Collections.singletonList("B"));
    final Collection<NDSProxyAudit> ndsProxyAudits3 = ndsProxyDown2.doGetTargets(context, null);
    assertEquals(
        0,
        ndsProxyAudits3.size(),
        "Expected one audit when no tag specified, was: " + ndsProxyAudits3);

    // Group has matching tag.
    group.setTags(Collections.singletonList("A"));
    final Collection<NDSProxyAudit> ndsProxyAudits4 = ndsProxyDown2.doGetTargets(context, null);
    assertEquals(
        1,
        ndsProxyAudits4.size(),
        "Expected one audit when no tag specified, was: " + ndsProxyAudits4);
  }

  @Test
  public void doGetTargetsAndRunCheck() throws Exception {
    assertResult(WithoutState.UNKNOWN, null);
    assertResult(WithoutState.NO_ALERT, Duration.ofMinutes(PING_AGE_MINUTES));
    assertResult(WithoutState.HAS_ALERT, Duration.ofMinutes(PING_AGE_MINUTES).plusMillis(100L));
  }

  private void assertResult(
      final Result.WithoutState pExpectedState, final Duration pLastConfigBeforePing)
      throws Exception {
    final AppSettings appSettings = mock(AppSettings.class);
    when(appSettings.getIntProp("mms.alerts.NdsProxyDown.maximumPingAgeMinutes"))
        .thenReturn(PING_AGE_MINUTES);

    final NDSProxyDown ndsProxyDown = new NDSProxyDown(appSettings);
    final MTMClusterDao mtmClusterDao = mock(MTMClusterDao.class);
    final NDSProxyAuditSvc ndsProxyAuditSvc = mock(NDSProxyAuditSvc.class);
    final RetentionPolicyDao retentionPolicyDao = mock(RetentionPolicyDao.class);
    when(retentionPolicyDao.findCachedById(null)).thenReturn(mock(RetentionPolicy.class));
    final NDSLookupSvc ndsLookupSvc = mock(NDSLookupSvc.class);
    final NDSGroup ndsGroup = mock(NDSGroup.class);
    when(ndsGroup.isMTMHolder()).thenReturn(true);
    when(ndsLookupSvc.getNDSGroup(_group.getId())).thenReturn(Optional.of(ndsGroup));

    final GroupAlertProcessingContext context =
        UnitTestUtils.create(GroupAlertProcessingContext.class)
            .withArgs(
                _group,
                _organization,
                ndsProxyAuditSvc,
                mtmClusterDao,
                retentionPolicyDao,
                ndsLookupSvc);

    final NDSProxyAudit ndsProxyAudit = mock(NDSProxyAudit.class);
    final Date lastConfDate;

    if (pLastConfigBeforePing != null) {
      lastConfDate = Date.from(Instant.now().minus(pLastConfigBeforePing));
    } else {
      lastConfDate = null;
    }

    final AgentAlertConfig config =
        new AgentAlertConfig.Builder(AgentEvent.Type.NDS_PROXY_DOWN, ObjectId.get()).build();
    final SharedMTMCluster sharedMTMCluster =
        new SharedMTMCluster(
            _group.getId(),
            "foo",
            false,
            0,
            CloudProvider.AWS,
            AWSRegionName.US_EAST_1,
            FreeInstanceSize.M0,
            "4.2",
            new ObjectId());
    final ServerlessMTMCluster serverlessMTMCluster =
        new ServerlessMTMCluster(
            _group.getId(),
            "bar",
            false,
            0,
            CloudProvider.AWS,
            AWSRegionName.US_EAST_1,
            ServerlessInstanceSize.SERVERLESS_V2,
            "4.2",
            null,
            new Date(),
            Date.from(Instant.now().minus(Duration.ofMinutes(12))),
            Date.from(Instant.now().minus(Duration.ofMinutes(12))),
            null,
            null,
            new ObjectId());
    when(mtmClusterDao.findClustersByGroupId(
            eq(_group.getId()), eq(DriverUtils.SECONDARY_PREFERRED_MINIMUM)))
        .thenReturn(List.of(sharedMTMCluster, serverlessMTMCluster));

    when(ndsProxyAudit.getLastConf()).thenReturn(lastConfDate);
    when(ndsProxyAuditSvc.findCurrent(
            _group.getId(), List.of(sharedMTMCluster, serverlessMTMCluster)))
        .thenReturn(Collections.singletonList(ndsProxyAudit));

    final Collection<NDSProxyAudit> targets = ndsProxyDown.doGetTargets(context, config);
    assertEquals(1, targets.size());
    assertEquals(ndsProxyAudit, targets.iterator().next());
    assertEquals(pExpectedState, ndsProxyDown.doRunCheck(context, config, ndsProxyAudit));
  }
}
