package com.xgen.svc.nds.svc.cps;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.amazonaws.services.kms.model.EncryptResult;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.AzureBackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.GCPBackupSnapshotEncryptionCredentials;
import com.xgen.cloud.deployment._public.model.AWSKMS;
import com.xgen.cloud.deployment._public.model.AWSKey;
import com.xgen.cloud.deployment._public.model.EncryptionProviderType;
import com.xgen.cloud.deployment._public.model.EncryptionProviders;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.NDSAWSKMS;
import com.xgen.cloud.nds.aws._public.model.NDSAWSTempCredentials;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._public.model.NDSAzureKeyVault;
import com.xgen.cloud.nds.azure._public.model.SupportedAzureEnvironment;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.privatelink._public.model.KMSValidationJobResponse;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.EncryptionAtRestProvider;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.svc.mms.util.UnitTestUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.NDSCloudProviderAccessSvc;
import com.xgen.svc.nds.svc.NDSEncryptionAtRestSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import java.nio.ByteBuffer;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ExtendWith(CloudProviderExtension.class)
public class CpsEncryptionAtRestSvcUnitTests {
  private static final Logger LOG = LoggerFactory.getLogger(CpsEncryptionAtRestSvcUnitTests.class);
  private static final ObjectId sourceProjectId = new ObjectId();
  private static final ObjectId targetProjectId = new ObjectId();
  private static final String targetClusterName = "targetClusterName";

  // aws snapshot constants
  private static final String snapshotKey = "123";
  private static final String snapshotSecret = "fffaaa";
  private static final String snapshotCmkId = "0033";
  private static final String targetAssumedRoleAccessKey = "456";
  private static final String targetAssumedRoleSecret = "ajsldfj";
  private static final String targetAssumedRoleSessionToken = "###!!!";
  private static final ObjectId snapshotRoleId = new ObjectId();

  private AWSApiSvc awsApiSvc;
  private NDSAWSKMS ndsawskms;
  private NDSEncryptionAtRestSvc ndsEncryptionAtRestSvc;
  private NDSGroupSvc ndsGroupSvc;
  private NDSGroup ndsGroup;
  private NDSEncryptionAtRest ndsEncryptionAtRest;
  private NDSCloudProviderAccessSvc ndsCloudProviderAccessSvc;
  private CpsEncryptionAtRestSvc cpsEncryptionAtRestSvc;

  private MockedStatic<FeatureFlagSvc> mockedStaticFeatureFlagSvc;

  @BeforeEach
  public void setup() {
    // mock nds group with encryption credentials
    ndsGroupSvc = mock(NDSGroupSvc.class);
    ndsGroup = mock(NDSGroup.class);
    doReturn(Optional.of(ndsGroup)).when(ndsGroupSvc).find(any());

    // mock target project group with ndsEncryptionAtRest
    final Group targetProject = mock(Group.class);
    final GroupSvc groupSvc = mock(GroupSvc.class);
    doReturn(targetProject).when(groupSvc).findById(eq(targetProjectId));

    ndsEncryptionAtRest = mock(NDSEncryptionAtRest.class);
    doReturn(ndsEncryptionAtRest).when(ndsGroup).getEncryptionAtRest();
    ndsawskms = mock(NDSAWSKMS.class);
    doReturn(ndsawskms).when(ndsEncryptionAtRest).getAWSKMS();
    doReturn(false).when(ndsawskms).getRequirePrivateNetworking();

    // mock ndsEncryptionAtRestSvc
    ndsEncryptionAtRestSvc =
        spy(
            UnitTestUtils.create(NDSEncryptionAtRestSvc.class)
                .withArgs(groupSvc, mock(AppSettings.class)));
    ndsCloudProviderAccessSvc = mock(NDSCloudProviderAccessSvc.class);
    when(ndsEncryptionAtRestSvc.getNdsCloudProviderAccessSvc())
        .thenReturn(ndsCloudProviderAccessSvc);

    awsApiSvc = mock(AWSApiSvc.class);

    when(ndsEncryptionAtRestSvc.getNdsCloudProviderAccessSvc())
        .thenReturn(ndsCloudProviderAccessSvc);
    cpsEncryptionAtRestSvc =
        UnitTestUtils.create(CpsEncryptionAtRestSvc.class)
            .withArgs(
                awsApiSvc,
                ndsGroupSvc,
                ndsCloudProviderAccessSvc,
                ndsEncryptionAtRestSvc,
                groupSvc);

    // this is to stub out feature flag checks
    mockedStaticFeatureFlagSvc = Mockito.mockStatic(FeatureFlagSvc.class);
    mockedStaticFeatureFlagSvc
        .when(() -> isFeatureFlagEnabled(any(), any(), any(), any()))
        .thenReturn(false);
  }

  @AfterEach
  public void teardown() {
    mockedStaticFeatureFlagSvc.close();
  }

  @Test
  public void testValidateEncryptionCredentialsForRestoreNonEncryptedSnapshot() throws Exception {
    final BackupSnapshot backupSnapshot = getAwsBackupSnapshot(getNoneEncryptionDetails());
    cpsEncryptionAtRestSvc.validateEncryptionCredentialsForRestore(
        backupSnapshot, mock(ClusterDescription.class));
  }

  @Test
  public void testValidateEncryptionCredentialsForRestoreNonEncryptedSnapshotToEncryptedCluster() {
    final BackupSnapshot backupSnapshot = getAwsBackupSnapshot(getNoneEncryptionDetails());
    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(EncryptionAtRestProvider.AWS);

    try {
      cpsEncryptionAtRestSvc.validateEncryptionCredentialsForRestore(
          backupSnapshot, targetClusterDescription);
      Assertions.fail();
    } catch (final SvcException e) {
      Assertions.assertEquals(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION, e.getErrorCode());
    }
  }

  @Test
  public void testValidateEncryptionCredentialsForRestoreEncryptedSnapshotToNonEncryptedCluster() {
    final BackupSnapshot backupSnapshot = getAwsBackupSnapshot(getAwsEncryptionDetailsCredsBased());
    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(EncryptionAtRestProvider.NONE);

    try {
      cpsEncryptionAtRestSvc.validateEncryptionCredentialsForRestore(
          backupSnapshot, targetClusterDescription);
      Assertions.fail();
    } catch (final SvcException e) {
      Assertions.assertEquals(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION, e.getErrorCode());
    }
  }

  @Test
  public void testValidateAWSEncryptionCredentialsForRestoreSuccess() throws Exception {
    final BackupSnapshot backupSnapshot = getAwsBackupSnapshot(getAwsEncryptionDetailsCredsBased());
    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(EncryptionAtRestProvider.AWS);

    doNothing().when(ndsEncryptionAtRestSvc).validateBackupAwsKmsCredentials(any(), any());
    doNothing().when(ndsEncryptionAtRestSvc).validateAWSKMSCredentials(any(), any(), any());

    cpsEncryptionAtRestSvc.validateEncryptionCredentialsForRestore(
        backupSnapshot, targetClusterDescription);
    verify(ndsEncryptionAtRestSvc, times(1)).validateBackupAwsKmsCredentials(any(), any());
    verify(ndsEncryptionAtRestSvc, times(1)).validateAWSKMSCredentials(any(), any(), any());
  }

  @Test
  public void testValidateAWSEncryptionCredentialsForRestoreFallbackValidations() throws Exception {
    final BackupSnapshot backupSnapshot = getAwsBackupSnapshot(getAwsEncryptionDetailsCredsBased());
    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(EncryptionAtRestProvider.AWS);

    doNothing().when(ndsEncryptionAtRestSvc).validateBackupAwsKmsCredentials(any(), any());
    doNothing().when(ndsEncryptionAtRestSvc).validateAWSKMSCredentials(any(), any(), any());

    // Case where the snapshot creds are valid
    cpsEncryptionAtRestSvc.validateEncryptionCredentialsForRestore(
        backupSnapshot, targetClusterDescription);

    doThrow(new SvcException(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION))
        .when(ndsEncryptionAtRestSvc)
        .validateBackupAwsKmsCredentials(any(), any());

    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateBackupAWSKMSCredentialsWithSeparateMasterKey(any(), any(), any());

    // Case where the snapshot creds are invalid, but we can access the snapshot CMK using AWS keys
    // on the target project
    cpsEncryptionAtRestSvc.validateEncryptionCredentialsForRestore(
        backupSnapshot, targetClusterDescription);

    doThrow(new SvcException(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION))
        .when(ndsEncryptionAtRestSvc)
        .validateBackupAWSKMSCredentialsWithSeparateMasterKey(any(), any(), any());
    try {
      // Case where both the snapshot creds and the target group creds does not have access to
      // snapshot CMK
      cpsEncryptionAtRestSvc.validateEncryptionCredentialsForRestore(
          backupSnapshot, targetClusterDescription);
      Assertions.fail();
    } catch (final SvcException e) {
      Assertions.assertEquals(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION, e.getErrorCode());
    }

    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateBackupAWSKMSCredentialsWithSeparateMasterKey(any(), any(), any());

    doThrow(new SvcException(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION))
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(any(), any(), any());

    try {
      // Case where both the snapshot creds and the target group creds does not have access to
      // snapshot CMK
      cpsEncryptionAtRestSvc.validateEncryptionCredentialsForRestore(
          backupSnapshot, targetClusterDescription);
      Assertions.fail();
    } catch (final SvcException e) {
      Assertions.assertEquals(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION, e.getErrorCode());
    }
  }

  @Test
  public void testValidateAWSRoleBasedEncryptionCredentialsForRestoreSuccess() throws SvcException {
    final BackupSnapshot backupSnapshot = getAwsBackupSnapshot(getAwsEncryptionDetailsRoleBased());
    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(EncryptionAtRestProvider.AWS);

    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateBackupAWSKMSCredentialsWithSeparateMasterKey(any(), any(), any());

    cpsEncryptionAtRestSvc.validateEncryptionCredentialsForRestore(
        backupSnapshot, targetClusterDescription);

    verify(ndsEncryptionAtRestSvc, times(1))
        .validateBackupAWSKMSCredentialsWithSeparateMasterKey(any(), any(), any());
  }

  @Test
  public void testValidateAwsEncryptedSnapshotAccessFromAgentRoleBasedEncryptionTarget()
      throws SvcException {
    doReturn(true).when(ndsawskms).getRequirePrivateNetworking();
    doReturn(Optional.of("testKey")).when(ndsawskms).getCustomerMasterKeyID();
    final BackupSnapshot backupSnapshot = getAwsBackupSnapshot(getAwsEncryptionDetailsRoleBased());
    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(EncryptionAtRestProvider.AWS);

    doReturn(mock(KMSValidationJobResponse.class))
        .when(ndsEncryptionAtRestSvc)
        .createAwsKmsValidationJob(any(), any(), any(), any(), any(), any(), any(), any());

    doReturn("testKey").when(ndsEncryptionAtRestSvc).extractKeyIdFromAwsArn(any());

    doReturn("selected-hostname")
        .when(ndsEncryptionAtRestSvc)
        .getHostForAgentValidation(any(), any(), any(), eq(CloudProvider.AWS));

    doReturn("test-region")
        .when(ndsEncryptionAtRestSvc)
        .getInstanceHardwareRegionFromHostName(any(), any());

    cpsEncryptionAtRestSvc.validateEncryptionCredentialsForRestore(
        backupSnapshot, targetClusterDescription);

    verify(ndsEncryptionAtRestSvc, times(2)).awsValidateKeysFromAgent(any(), any(), any(), any());
  }

  @Test
  public void testValidateAwsEncryptedSnapshotAccessFromAgentCredentialsBasedEncryptionTarget()
      throws SvcException {
    doReturn(true).when(ndsawskms).getRequirePrivateNetworking();
    doReturn(Optional.of("testKey")).when(ndsawskms).getCustomerMasterKeyID();
    ObjectId testRoleId = new ObjectId();
    doReturn(Optional.of(testRoleId)).when(ndsawskms).getRoleId();
    final BackupSnapshot backupSnapshot = getAwsBackupSnapshot(getAwsEncryptionDetailsRoleBased());
    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(EncryptionAtRestProvider.AWS);

    doReturn(mock(KMSValidationJobResponse.class))
        .when(ndsEncryptionAtRestSvc)
        .createAwsKmsValidationJob(any(), any(), any(), any(), any(), any(), any(), any());

    doReturn("testKey").when(ndsEncryptionAtRestSvc).extractKeyIdFromAwsArn(any());

    doReturn("selected-hostname")
        .when(ndsEncryptionAtRestSvc)
        .getHostForAgentValidation(any(), any(), any(), eq(CloudProvider.AWS));

    doReturn("test-region")
        .when(ndsEncryptionAtRestSvc)
        .getInstanceHardwareRegionFromHostName(any(), any());

    cpsEncryptionAtRestSvc.validateEncryptionCredentialsForRestore(
        backupSnapshot, targetClusterDescription);

    verify(ndsEncryptionAtRestSvc, times(2)).awsValidateKeysFromAgent(any(), any(), any(), any());
  }

  @Test
  public void testValidateAzureEncryptedSnapshotAccess() throws Exception {
    final AzureBackupSnapshotEncryptionCredentials azureBackupSnapshotEncryptionCredentials =
        new AzureBackupSnapshotEncryptionCredentials(
            null, null, null, null, null, null, null, null);
    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(ClusterDescription.EncryptionAtRestProvider.AZURE);

    NDSAzureKeyVault keyVault = mock(NDSAzureKeyVault.class);
    doReturn(keyVault).when(ndsEncryptionAtRest).getAzureKeyVault();
    doReturn(false).when(keyVault).getRequirePrivateNetworking();

    doNothing().when(ndsEncryptionAtRestSvc).validateBackupAzureKeyVaultCredentials(any());
    doNothing().when(ndsEncryptionAtRestSvc).validateAzureKeyVaultKey(any());

    cpsEncryptionAtRestSvc.validateAzureEncryptedSnapshotAccess(
        azureBackupSnapshotEncryptionCredentials, targetClusterDescription);

    doThrow(new SvcException(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_FOUND))
        .when(ndsEncryptionAtRestSvc)
        .validateBackupAzureKeyVaultCredentials(any());

    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAzureKeyVaultCredentialsWithSeparateMasterKey(
            any(), any(), any(), any(), any(), any(), any());
    cpsEncryptionAtRestSvc.validateAzureEncryptedSnapshotAccess(
        azureBackupSnapshotEncryptionCredentials, targetClusterDescription);

    doThrow(new SvcException(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_FOUND))
        .when(ndsEncryptionAtRestSvc)
        .validateAzureKeyVaultCredentialsWithSeparateMasterKey(
            any(), any(), any(), any(), any(), any(), any());

    try {
      cpsEncryptionAtRestSvc.validateAzureEncryptedSnapshotAccess(
          azureBackupSnapshotEncryptionCredentials, targetClusterDescription);
      Assertions.fail("Validation should fail but passed.");
    } catch (final SvcException e) {
      Assertions.assertEquals(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_FOUND, e.getErrorCode());
    }

    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAzureKeyVaultCredentialsWithSeparateMasterKey(
            any(), any(), any(), any(), any(), any(), any());

    doThrow(new SvcException(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_FOUND))
        .when(ndsEncryptionAtRestSvc)
        .validateAzureKeyVaultKey(any());

    try {
      cpsEncryptionAtRestSvc.validateAzureEncryptedSnapshotAccess(
          azureBackupSnapshotEncryptionCredentials, targetClusterDescription);
      Assertions.fail("Validation should fail but passed.");
    } catch (final SvcException e) {
      Assertions.assertEquals(NDSErrorCode.AZURE_KEY_VAULT_KEY_NOT_FOUND, e.getErrorCode());
    }
  }

  @Test
  public void testValidateAzureEncryptedSnapshotAccessPrivateNetworkingSuccess() throws Exception {

    final AzureBackupSnapshotEncryptionCredentials azureBackupSnapshotEncryptionCredentials =
        new AzureBackupSnapshotEncryptionCredentials(
            null, null, null, null, null, null, "vault1", null);

    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(ClusterDescription.EncryptionAtRestProvider.AZURE);

    NDSAzureKeyVault keyVault = mock(NDSAzureKeyVault.class);
    doReturn(keyVault).when(ndsEncryptionAtRest).getAzureKeyVault();
    doReturn(true).when(keyVault).getRequirePrivateNetworking();

    doReturn(Optional.of("vault1")).when(keyVault).getKeyVaultName();
    doReturn(Optional.of("key1")).when(keyVault).getKeyIdentifier();

    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAzureKeyVaultFromAgent(any(), any(), any(), any());

    cpsEncryptionAtRestSvc.validateAzureEncryptedSnapshotAccess(
        azureBackupSnapshotEncryptionCredentials, targetClusterDescription);
  }

  @Test
  public void testValidateAzureEncryptedSnapshotAccessPrivateNetworkingFailure()
      throws SvcException {
    final AzureBackupSnapshotEncryptionCredentials azureBackupSnapshotEncryptionCredentials =
        new AzureBackupSnapshotEncryptionCredentials(
            null, null, null, null, null, null, "vault1", "key1");

    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(ClusterDescription.EncryptionAtRestProvider.AZURE);

    NDSAzureKeyVault keyVault = mock(NDSAzureKeyVault.class);
    doReturn(keyVault).when(ndsEncryptionAtRest).getAzureKeyVault();
    doReturn(true).when(keyVault).getRequirePrivateNetworking();

    doReturn(Optional.of("vault1")).when(keyVault).getKeyVaultName();
    doReturn(Optional.of("key2")).when(keyVault).getKeyIdentifier();

    doThrow(new SvcException(NDSErrorCode.KMS_VALIDATION_JOB_FAILED))
        .when(ndsEncryptionAtRestSvc)
        .validateAzureKeyVaultFromAgent(any(), any(), any(), eq("key1"));

    // CASE: can't access snapshot CMK
    SvcException exception1 =
        assertThrows(
            SvcException.class,
            () ->
                cpsEncryptionAtRestSvc.validateAzureEncryptedSnapshotAccess(
                    azureBackupSnapshotEncryptionCredentials, targetClusterDescription));
    Assertions.assertEquals(NDSErrorCode.KMS_VALIDATION_JOB_FAILED, exception1.getErrorCode());

    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAzureKeyVaultFromAgent(any(), any(), any(), eq("key1"));
    doThrow(new SvcException(NDSErrorCode.KMS_VALIDATION_JOB_FAILED))
        .when(ndsEncryptionAtRestSvc)
        .validateAzureKeyVaultFromAgent(any(), any(), any(), eq("key2"));

    // CASE: can't access target cluster credentials
    SvcException exception2 =
        assertThrows(
            SvcException.class,
            () ->
                cpsEncryptionAtRestSvc.validateAzureEncryptedSnapshotAccess(
                    azureBackupSnapshotEncryptionCredentials, targetClusterDescription));
    Assertions.assertEquals(NDSErrorCode.KMS_VALIDATION_JOB_FAILED, exception2.getErrorCode());
  }

  @Test
  public void testValidateKeyAccessForAzureKeyVaultPrivateNetworkingCrossProjectFailure() {
    final AzureBackupSnapshotEncryptionCredentials azureBackupSnapshotEncryptionCredentials =
        new AzureBackupSnapshotEncryptionCredentials(
            null, null, null, null, null, null, "vault1", null);

    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(ClusterDescription.EncryptionAtRestProvider.AZURE);

    NDSAzureKeyVault keyVault = mock(NDSAzureKeyVault.class);
    doReturn(keyVault).when(ndsEncryptionAtRest).getAzureKeyVault();
    doReturn(true).when(keyVault).getRequirePrivateNetworking();

    doReturn(Optional.of("vault1")).when(keyVault).getKeyVaultName();
    doReturn(Optional.of("vault2")).when(keyVault).getKeyVaultName();

    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                cpsEncryptionAtRestSvc.validateAzureEncryptedSnapshotAccess(
                    azureBackupSnapshotEncryptionCredentials, targetClusterDescription));
    Assertions.assertEquals(
        NDSErrorCode.RESTORE_UNAVAILABLE_DIFFERENT_KEY_VAULTS, exception.getErrorCode());
  }

  @Test
  public void testValidateGcpEncryptedSnapshotAccess() throws Exception {
    GCPBackupSnapshotEncryptionCredentials gcpBackupSnapshotEncryptionCredentials =
        new GCPBackupSnapshotEncryptionCredentials(null, null, null, null);
    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(ClusterDescription.EncryptionAtRestProvider.GCP);

    doNothing().when(ndsEncryptionAtRestSvc).validateGoogleCloudKMSKey(any(), any(), any(), any());

    doNothing().when(ndsEncryptionAtRestSvc).validateGoogleCloudKMSCredentials(any(), any(), any());

    cpsEncryptionAtRestSvc.validateGcpEncryptedSnapshotAccess(
        gcpBackupSnapshotEncryptionCredentials, targetClusterDescription);

    doThrow(new SvcException(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION))
        .when(ndsEncryptionAtRestSvc)
        .validateGoogleCloudKMSKey(any(), any(), any(), any());

    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateGCPKMSCredentialsWithSeparateVersionResourceKey(any(), any(), any(), any(), any());

    cpsEncryptionAtRestSvc.validateGcpEncryptedSnapshotAccess(
        gcpBackupSnapshotEncryptionCredentials, targetClusterDescription);

    doThrow(new SvcException(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION))
        .when(ndsEncryptionAtRestSvc)
        .validateGCPKMSCredentialsWithSeparateVersionResourceKey(any(), any(), any(), any(), any());

    try {
      cpsEncryptionAtRestSvc.validateGcpEncryptedSnapshotAccess(
          gcpBackupSnapshotEncryptionCredentials, targetClusterDescription);
      Assertions.fail("Validation should fail but passed.");
    } catch (final SvcException e) {
      Assertions.assertEquals(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION, e.getErrorCode());
    }

    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateGCPKMSCredentialsWithSeparateVersionResourceKey(any(), any(), any(), any(), any());

    doThrow(new SvcException(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION))
        .when(ndsEncryptionAtRestSvc)
        .validateGoogleCloudKMSCredentials(any(), any(), any());

    try {
      cpsEncryptionAtRestSvc.validateGcpEncryptedSnapshotAccess(
          gcpBackupSnapshotEncryptionCredentials, targetClusterDescription);
      Assertions.fail("Validation should fail but passed.");
    } catch (final SvcException e) {
      Assertions.assertEquals(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION, e.getErrorCode());
    }
  }

  @Test
  public void testValidateKeyAccessForAwsKmsTargetClusterKeyInvalid() throws Exception {
    final AWSBackupSnapshotEncryptionCredentials awsBackupSnapshotEncryptionCredentials =
        new AWSBackupSnapshotEncryptionCredentials("key", "secret", null, "123", null, false);

    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(ClusterDescription.EncryptionAtRestProvider.AWS);

    doNothing().when(ndsEncryptionAtRestSvc).validateBackupAwsKmsCredentials(any(), any());
    doThrow(new SvcException(NDSErrorCode.CUSTOMER_MASTER_KEY_NOT_FOUND))
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(any(), any(), any());

    try {
      cpsEncryptionAtRestSvc.validateAwsEncryptedSnapshotAccess(
          awsBackupSnapshotEncryptionCredentials, new ObjectId(), targetClusterDescription);
      Assertions.fail("Validation should fail but passed.");
    } catch (final SvcException e) {
      verify(ndsEncryptionAtRestSvc, times(0))
          .validateBackupAWSKMSCredentialsWithSeparateMasterKey(any(), any(), any());
      Assertions.assertEquals(NDSErrorCode.CUSTOMER_MASTER_KEY_NOT_FOUND, e.getErrorCode());
    }
  }

  @Test
  public void testValidateAwsEncryptedSnapshotAccessKmsCredsBasedSnapshot() throws Exception {

    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(ClusterDescription.EncryptionAtRestProvider.AWS);

    LOG.info(
        "Case 1: snapshots are creds based, target cluster is role based kms, "
            + "snapshot creds have no access to cmk, but target role has. Pass validation");

    // snapshot setup (creds based)
    final AWSBackupSnapshotEncryptionCredentials awsBackupSnapshotEncryptionCredentials =
        new AWSBackupSnapshotEncryptionCredentials(
            snapshotKey, snapshotSecret, null, snapshotCmkId, null, false);

    // target group setup (role based)
    NDSGroup targetNdsGroup =
        new NDSGroup(
            NDSModelTestFactory.getNDSGroupAllFields()
                .append("_id", targetProjectId)
                .append("encryptionAtRest", NDSModelTestFactory.getAwsEncryptionAtRest(true)));
    doReturn(Optional.of(targetNdsGroup)).when(ndsGroupSvc).find(any());

    // temp creds from group
    NDSAWSKMS targetNdsAwsKms = (NDSAWSKMS) targetNdsGroup.getEncryptionAtRest().getAWSKMS();
    final Date expiration = DateUtils.addDays(new Date(), 1);
    final NDSAWSTempCredentials ndsAwsTempCredentials =
        new NDSAWSTempCredentials(
            targetAssumedRoleAccessKey,
            targetAssumedRoleSecret,
            targetAssumedRoleSessionToken,
            expiration);
    when(ndsCloudProviderAccessSvc.getAWSAssumeRoleTempCredentials(
            targetNdsGroup, targetNdsAwsKms.getRoleId().get()))
        .thenReturn(ndsAwsTempCredentials);
    when(ndsCloudProviderAccessSvc.getAWSAssumeRoleTempCredentials(
            eq(targetNdsGroup), eq(targetNdsAwsKms.getRoleId().get()), any()))
        .thenReturn(ndsAwsTempCredentials);

    // snapshot creds can access CMK
    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(snapshotKey), eq(snapshotSecret), any(), eq(snapshotCmkId), any(), any());

    // target project can access its own CMK
    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(targetAssumedRoleAccessKey),
            eq(targetAssumedRoleSecret),
            any(),
            eq(targetNdsAwsKms.getCustomerMasterKeyID().get()),
            any(),
            any());

    // validation passes
    cpsEncryptionAtRestSvc.validateAwsEncryptedSnapshotAccess(
        awsBackupSnapshotEncryptionCredentials, sourceProjectId, targetClusterDescription);

    LOG.info(
        "Case 2: snapshots are creds based, target cluster is role based kms, snapshot creds have"
            + " no access to cmk, and target role also don't have access. Fail validation");

    // snapshot creds cannot access CMK
    doThrow(new SvcException(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT))
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(snapshotKey), eq(snapshotSecret), any(), eq(snapshotCmkId), any(), any());

    // target role based project cannot access CMK
    doThrow(new SvcException(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT))
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(targetAssumedRoleAccessKey),
            eq(targetAssumedRoleSecret),
            any(),
            eq(snapshotCmkId),
            any(),
            any());
    try {
      cpsEncryptionAtRestSvc.validateAwsEncryptedSnapshotAccess(
          awsBackupSnapshotEncryptionCredentials, sourceProjectId, targetClusterDescription);
      Assertions.fail("Expected validation to fail");
    } catch (final SvcException e) {
      Assertions.assertEquals(e.getErrorCode(), NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION);
    }

    LOG.info(
        "Case 3: snapshots are creds based, target cluster is role based kms, snapshot creds have"
            + " access to cmk, but target role don't have access. Pass validation");

    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(snapshotKey), eq(snapshotSecret), any(), eq(snapshotCmkId), any(), any());

    // validation pass
    cpsEncryptionAtRestSvc.validateAwsEncryptedSnapshotAccess(
        awsBackupSnapshotEncryptionCredentials, sourceProjectId, targetClusterDescription);

    LOG.info(
        "Case 4: snapshots are creds based, target cluster is creds based kms, snapshot creds"
            + " don't have access to cmk, but target creds have access. Pass validation");

    // target group setup (creds  based)
    targetNdsGroup =
        new NDSGroup(
            NDSModelTestFactory.getNDSGroupAllFields()
                .append("_id", targetProjectId)
                .append("encryptionAtRest", NDSModelTestFactory.getAwsEncryptionAtRest(false)));
    doReturn(Optional.of(targetNdsGroup)).when(ndsGroupSvc).find(any());
    targetNdsAwsKms = (NDSAWSKMS) targetNdsGroup.getEncryptionAtRest().getAWSKMS();

    // snapshot creds cannot access CMK
    doThrow(new SvcException(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT))
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(snapshotKey), eq(snapshotSecret), any(), eq(snapshotCmkId), any(), any());

    // target project creds can access CMK
    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(targetNdsAwsKms.getAccessKeyID().get()),
            eq(targetNdsAwsKms.getSecretAccessKey().get()),
            any(),
            eq(snapshotCmkId),
            any(),
            any());

    // give target project access to its own cmk
    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(targetNdsAwsKms.getAccessKeyID().get()),
            eq(targetNdsAwsKms.getSecretAccessKey().get()),
            any(),
            eq(targetNdsAwsKms.getCustomerMasterKeyID().get()),
            any(),
            any());

    cpsEncryptionAtRestSvc.validateAwsEncryptedSnapshotAccess(
        awsBackupSnapshotEncryptionCredentials, sourceProjectId, targetClusterDescription);

    LOG.info(
        "Case 5: snapshots are creds based, target cluster is creds based kms, snapshot creds have"
            + " access to cmk, but target creds don't have access. Pass validation");

    // make snapshot creds have to access snapshot cmk
    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(snapshotKey), eq(snapshotSecret), any(), eq(snapshotCmkId), any(), any());

    doThrow(new SvcException(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT))
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(targetNdsAwsKms.getAccessKeyID().get()),
            eq(targetNdsAwsKms.getSecretAccessKey().get()),
            any(),
            eq(snapshotCmkId),
            any(),
            any());

    cpsEncryptionAtRestSvc.validateAwsEncryptedSnapshotAccess(
        awsBackupSnapshotEncryptionCredentials, sourceProjectId, targetClusterDescription);
  }

  @Test
  public void testValidateAwsEncryptedSnapshotAccessRoleBasedSnapshot() throws Exception {
    final AWSBackupSnapshotEncryptionCredentials awsBackupSnapshotEncryptionCredentials =
        new AWSBackupSnapshotEncryptionCredentials(
            null, null, snapshotRoleId, snapshotCmkId, null, false);
    final ClusterDescription targetClusterDescription =
        getAwsClusterDescription(ClusterDescription.EncryptionAtRestProvider.AWS);

    LOG.info(
        "Case 1: snapshots are role based, target cluster is role based kms, "
            + "target role has access to snapshot cmk. Pass validation");

    // target group setup (role based)
    NDSGroup targetNdsGroup =
        new NDSGroup(
            NDSModelTestFactory.getNDSGroupAllFields()
                .append("_id", targetProjectId)
                .append("encryptionAtRest", NDSModelTestFactory.getAwsEncryptionAtRest(true)));
    doReturn(Optional.of(targetNdsGroup)).when(ndsGroupSvc).find(any());

    // temp creds from group
    NDSAWSKMS targetNdsAwsKms = (NDSAWSKMS) targetNdsGroup.getEncryptionAtRest().getAWSKMS();
    final Date expiration = DateUtils.addDays(new Date(), 1);
    final NDSAWSTempCredentials ndsAwsTempCredentials =
        new NDSAWSTempCredentials(
            targetAssumedRoleAccessKey,
            targetAssumedRoleSecret,
            targetAssumedRoleSessionToken,
            expiration);
    when(ndsCloudProviderAccessSvc.getAWSAssumeRoleTempCredentials(
            targetNdsGroup, targetNdsAwsKms.getRoleId().get()))
        .thenReturn(ndsAwsTempCredentials);
    when(ndsCloudProviderAccessSvc.getAWSAssumeRoleTempCredentials(
            eq(targetNdsGroup), eq(targetNdsAwsKms.getRoleId().get()), any()))
        .thenReturn(ndsAwsTempCredentials);

    /* SETUP */

    // give target project access to snapshot cmk, by doNothing(), it won't throw exception, which
    // means validation success
    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(targetAssumedRoleAccessKey),
            eq(targetAssumedRoleSecret),
            any(),
            eq(snapshotCmkId),
            any(),
            any());

    // give target project access to its own cmk
    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(targetAssumedRoleAccessKey),
            eq(targetAssumedRoleSecret),
            any(),
            eq(targetNdsAwsKms.getCustomerMasterKeyID().get()),
            any(),
            any());

    /* TEST */

    // validation pass
    cpsEncryptionAtRestSvc.validateAwsEncryptedSnapshotAccess(
        awsBackupSnapshotEncryptionCredentials, sourceProjectId, targetClusterDescription);

    LOG.info(
        "Case 2: snapshots are role based, target cluster is role based kms, "
            + "target role doesn't have access to snapshot cmk. Fail validation");

    // target project has no access to snapshot cmk
    doThrow(new SvcException(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT))
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(targetAssumedRoleAccessKey),
            eq(targetAssumedRoleSecret),
            any(),
            eq(snapshotCmkId),
            any(),
            any());

    try {
      cpsEncryptionAtRestSvc.validateAwsEncryptedSnapshotAccess(
          awsBackupSnapshotEncryptionCredentials, sourceProjectId, targetClusterDescription);
      Assertions.fail("Expected validation to fail");
    } catch (final SvcException e) {
      Assertions.assertEquals(e.getErrorCode(), NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION);
    }

    LOG.info(
        "Case 3: snapshots are role based, target cluster is creds based kms, "
            + "target creds have access to snapshot cmk. Pass validation");

    // target group setup (creds  based)
    targetNdsGroup =
        new NDSGroup(
            NDSModelTestFactory.getNDSGroupAllFields()
                .append("_id", targetProjectId)
                .append("encryptionAtRest", NDSModelTestFactory.getAwsEncryptionAtRest(false)));
    doReturn(Optional.of(targetNdsGroup)).when(ndsGroupSvc).find(any());
    targetNdsAwsKms = (NDSAWSKMS) targetNdsGroup.getEncryptionAtRest().getAWSKMS();

    /* SETUP */

    // give target project access to snapshot cmk, by doNothing(), it won't throw exception, which
    // means validation success
    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(targetNdsAwsKms.getAccessKeyID().get()),
            eq(targetNdsAwsKms.getSecretAccessKey().get()),
            any(),
            eq(snapshotCmkId),
            any(),
            any());

    // give target project access to its own cmk
    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(targetNdsAwsKms.getAccessKeyID().get()),
            eq(targetNdsAwsKms.getSecretAccessKey().get()),
            any(),
            eq(targetNdsAwsKms.getCustomerMasterKeyID().get()),
            any(),
            any());

    cpsEncryptionAtRestSvc.validateAwsEncryptedSnapshotAccess(
        awsBackupSnapshotEncryptionCredentials, sourceProjectId, targetClusterDescription);

    LOG.info(
        "Case 4: snapshots are role based, target cluster is creds based kms, "
            + "target creds doesn't have access to snapshot cmk. Fail validation");

    doThrow(new SvcException(NDSErrorCode.NO_PERMISSION_TO_ENCRYPT_DECRYPT))
        .when(ndsEncryptionAtRestSvc)
        .validateAWSKMSCredentials(
            eq(targetNdsAwsKms.getAccessKeyID().get()),
            eq(targetNdsAwsKms.getSecretAccessKey().get()),
            any(),
            eq(snapshotCmkId),
            any(),
            any());

    try {
      cpsEncryptionAtRestSvc.validateAwsEncryptedSnapshotAccess(
          awsBackupSnapshotEncryptionCredentials, sourceProjectId, targetClusterDescription);
      Assertions.fail("Expected validation to fail");
    } catch (final SvcException e) {
      Assertions.assertEquals(e.getErrorCode(), NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION);
    }
  }

  @Test
  public void testValidateEncryptionCredentialsForExportSnapshot() throws Exception {
    final BackupSnapshot backupSnapshot = getAwsBackupSnapshot(getAwsEncryptionDetailsRoleBased());

    doNothing().when(ndsEncryptionAtRestSvc).validateBackupAwsKmsCredentials(any(), any());
    doNothing()
        .when(ndsEncryptionAtRestSvc)
        .validateBackupAWSKMSCredentialsWithSeparateMasterKey(any(), any(), any());

    cpsEncryptionAtRestSvc.validateEncryptionCredentialsForExportSnapshot(backupSnapshot);
    verify(ndsEncryptionAtRestSvc, times(1)).validateBackupAwsKmsCredentials(any(), any());

    doThrow(new SvcException(NDSErrorCode.INVALID_SNAPSHOT_RESTORE_ENCRYPTION))
        .when(ndsEncryptionAtRestSvc)
        .validateBackupAwsKmsCredentials(any(), any());
    cpsEncryptionAtRestSvc.validateEncryptionCredentialsForExportSnapshot(backupSnapshot);
    verify(ndsEncryptionAtRestSvc, times(2)).validateBackupAwsKmsCredentials(any(), any());
    verify(ndsEncryptionAtRestSvc, times(1))
        .validateBackupAWSKMSCredentialsWithSeparateMasterKey(any(), any(), any());
  }

  @Test
  public void testValidateEncryptionCredentialsForExportSnapshot_AzurePrivateNetworkingRequired() {
    final BackupSnapshot backupSnapshot = getAwsBackupSnapshot(getAzureEncryptionDetails());

    NDSAzureKeyVault kms = mock(NDSAzureKeyVault.class);
    doReturn(kms).when(ndsEncryptionAtRest).getAzureKeyVault();
    doReturn(true).when(kms).getRequirePrivateNetworking();

    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                cpsEncryptionAtRestSvc.validateEncryptionCredentialsForExportSnapshot(
                    backupSnapshot));
    Assertions.assertEquals(
        NDSErrorCode.SNAPSHOT_EXPORT_WITH_PRIVATE_LINK_UNSUPPORTED, exception.getErrorCode());
  }

  @Test
  public void testValidateEncryptionCredentialsForExportSnapshot_AWSPrivateNetworkingRequired() {
    final BackupSnapshot backupSnapshot = getAwsBackupSnapshot(getAwsEncryptionDetailsRoleBased());

    NDSAWSKMS kms = mock(NDSAWSKMS.class);
    doReturn(kms).when(ndsEncryptionAtRest).getAWSKMS();
    doReturn(true).when(kms).getRequirePrivateNetworking();

    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                cpsEncryptionAtRestSvc.validateEncryptionCredentialsForExportSnapshot(
                    backupSnapshot));
    Assertions.assertEquals(
        NDSErrorCode.SNAPSHOT_EXPORT_WITH_PRIVATE_LINK_UNSUPPORTED, exception.getErrorCode());
  }

  // helpers
  private BackupSnapshot getAwsBackupSnapshot(final BasicDBObject encryptionDetails) {
    final BasicDBObject defaultAwsBackupSnapshot =
        NDSModelTestFactory.getDefaultAwsBackupSnapshot();
    defaultAwsBackupSnapshot.remove(BackupSnapshot.FieldDefs.ENCRYPTION_DETAILS);
    defaultAwsBackupSnapshot.append(BackupSnapshot.FieldDefs.ENCRYPTION_DETAILS, encryptionDetails);
    return new AWSBackupSnapshot(defaultAwsBackupSnapshot);
  }

  private BasicDBObject getNoneEncryptionDetails() {
    return new BasicDBObject()
        .append(
            BackupSnapshot.SnapshotEncryptionDetails.FieldDefs.ENCRYPTION_PROVIDER,
            EncryptionAtRestProvider.NONE.name());
  }

  private ClusterDescription getAwsClusterDescription(EncryptionAtRestProvider provider) {
    final BasicDBList restoreJobIds = new BasicDBList();
    final BasicDBObject awsClusterDescription =
        NDSModelTestFactory.getAWSClusterDescription(
            CpsEncryptionAtRestSvcUnitTests.targetProjectId);

    awsClusterDescription.append("restoreJobIds", restoreJobIds);
    awsClusterDescription.append("encryptionAtRestProvider", provider.name());
    awsClusterDescription.append("diskBackupEnabled", true);
    return ClusterDescription.getCloudProviderClusterDescription(awsClusterDescription);
  }

  private BasicDBObject getAwsEncryptionDetailsCredsBased() {
    final String secretKey = EncryptionUtils.genEncryptStr("secretKey");
    return new BasicDBObject()
        .append(
            BackupSnapshot.SnapshotEncryptionDetails.FieldDefs.ENCRYPTION_PROVIDER,
            EncryptionAtRestProvider.AWS.name())
        .append(AWSBackupSnapshotEncryptionCredentials.FieldDefs.AWS_ACCESS_KEY_ID, "awsAccessKey")
        .append(AWSBackupSnapshotEncryptionCredentials.FieldDefs.AWS_SECRET_ACCESS_KEY, secretKey)
        .append(
            AWSBackupSnapshotEncryptionCredentials.FieldDefs.AWS_CUSTOMER_MASTER_KEY_ID,
            "awsCustomerMasterKey")
        .append(
            AWSBackupSnapshotEncryptionCredentials.FieldDefs.AWS_REGION,
            AWSRegionName.US_EAST_1.name());
  }

  private BasicDBObject getAwsEncryptionDetailsRoleBased() {
    return new BasicDBObject()
        .append(
            BackupSnapshot.SnapshotEncryptionDetails.FieldDefs.ENCRYPTION_PROVIDER,
            EncryptionAtRestProvider.AWS.name())
        .append(AWSBackupSnapshotEncryptionCredentials.FieldDefs.AWS_IAM_ROLE_ID, new ObjectId())
        .append(
            AWSBackupSnapshotEncryptionCredentials.FieldDefs.AWS_CUSTOMER_MASTER_KEY_ID,
            "awsCustomerMasterKey")
        .append(
            AWSBackupSnapshotEncryptionCredentials.FieldDefs.AWS_REGION,
            AWSRegionName.US_EAST_1.name());
  }

  @Test
  public void testGetAwsCipherTextRawBlobRoleBased() throws SvcException {
    final EncryptionProviders encryptionProviders = mock(EncryptionProviders.class);
    final AWSKMS awskms = mock(AWSKMS.class);
    final ObjectId roleId = new ObjectId();

    final NDSAWSTempCredentials tempCredentials = mock(NDSAWSTempCredentials.class);
    final AWSKey awsKey = mock(AWSKey.class);
    doNothing().when(ndsEncryptionAtRestSvc).validateAWSKMSCredentials(any(), any(), any());

    doReturn(targetProjectId).when(ndsGroup).getGroupId();
    doReturn("us-east-1").when(awsKey).getRegion();
    doReturn(List.of(awsKey)).when(awskms).getKeys();
    doReturn(awskms).when(encryptionProviders).getAWSKMS();
    doReturn(true).when(ndsawskms).isEnabled();
    doReturn(roleId).when(awskms).getRoleId();
    doReturn(tempCredentials)
        .when(ndsCloudProviderAccessSvc)
        .getAWSAssumeRoleTempCredentials(targetProjectId, roleId);
    doReturn(null).when(tempCredentials).getAccessKey();
    doReturn(null).when(tempCredentials).getAccessSecret();
    doReturn(null).when(tempCredentials).getSessionToken();

    final EncryptResult encryptResult = mock(EncryptResult.class);
    doReturn(encryptResult)
        .when(awsApiSvc)
        .encrypt(any(), any(), any(), any(), any(), any(), any());
    doReturn(ByteBuffer.wrap(new byte[] {})).when(encryptResult).getCiphertextBlob();

    cpsEncryptionAtRestSvc.getCipherTextBlob(
        encryptionProviders, EncryptionProviderType.AWS, targetProjectId, targetClusterName);

    // role based -> get temp creds using role
    verify(ndsCloudProviderAccessSvc, times(1))
        .getAWSAssumeRoleTempCredentials(targetProjectId, roleId);
    verify(awsApiSvc, times(1)).encrypt(any(), any(), any(), any(), any(), any(), any());
  }

  @Test
  public void testGetAwsCipherTextRawBlobCredsBased() throws SvcException {
    final EncryptionProviders encryptionProviders = mock(EncryptionProviders.class);
    final AWSKMS awskms = mock(AWSKMS.class);
    final AWSKey awsKey = mock(AWSKey.class);

    doNothing().when(ndsEncryptionAtRestSvc).validateAWSKMSCredentials(any(), any(), any());
    doReturn(targetProjectId).when(ndsGroup).getGroupId();
    doReturn("us-east-1").when(awsKey).getRegion();
    doReturn(List.of(awsKey)).when(awskms).getKeys();
    doReturn(awskms).when(encryptionProviders).getAWSKMS();
    doReturn(true).when(ndsawskms).isEnabled();
    doReturn(null).when(awskms).getRoleId();

    final EncryptResult encryptResult = mock(EncryptResult.class);
    doReturn(encryptResult)
        .when(awsApiSvc)
        .encrypt(any(), any(), any(), any(), any(), any(), any());
    doReturn(ByteBuffer.wrap(new byte[] {})).when(encryptResult).getCiphertextBlob();

    cpsEncryptionAtRestSvc.getCipherTextBlob(
        encryptionProviders, EncryptionProviderType.AWS, targetProjectId, targetClusterName);

    // creds based -> do not generate temp credentials
    verify(ndsCloudProviderAccessSvc, times(0))
        .getAWSAssumeRoleTempCredentials((ObjectId) any(), any());
    verify(awsApiSvc, times(1)).encrypt(any(), any(), any(), any(), any(), any(), any());
  }

  @Test
  public void testGetAwsCipherTextRawBlobRequirePrivateNetworking() throws SvcException {
    final EncryptionProviders encryptionProviders = mock(EncryptionProviders.class);
    final AWSKMS awskms = mock(AWSKMS.class);
    final String clusterName = "cluster0";
    final AWSKey awsKey = mock(AWSKey.class);

    doNothing().when(ndsEncryptionAtRestSvc).validateAWSKMSCredentials(any(), any(), any());
    doReturn(new byte[] {})
        .when(ndsEncryptionAtRestSvc)
        .awsEncryptBytesFromAgentWithAwsKMS(any(), any(), any(), any());
    doReturn(targetProjectId).when(ndsGroup).getGroupId();
    doReturn("us-east-1").when(awsKey).getRegion();
    doReturn(List.of(awsKey)).when(awskms).getKeys();
    doReturn(awskms).when(encryptionProviders).getAWSKMS();
    doReturn(true).when(ndsawskms).isEnabled();
    doReturn(null).when(awskms).getRoleId();

    doReturn(true).when(ndsawskms).getRequirePrivateNetworking();

    cpsEncryptionAtRestSvc.getCipherTextBlob(
        encryptionProviders, EncryptionProviderType.AWS, targetProjectId, clusterName);

    // private networking -> do not make kms calls in mms
    verify(ndsCloudProviderAccessSvc, times(0))
        .getAWSAssumeRoleTempCredentials((ObjectId) any(), any());
    verify(awsApiSvc, times(0)).encrypt(any(), any(), any(), any(), any(), any(), any());
    verify(ndsEncryptionAtRestSvc, times(1))
        .awsEncryptBytesFromAgentWithAwsKMS(any(), any(), any(), any());
  }

  private BasicDBObject getAzureEncryptionDetails() {
    final String secretKey = EncryptionUtils.genEncryptStr("secretKey");
    final BasicDBObject encryptionDetails =
        new BasicDBObject()
            .append(
                BackupSnapshot.SnapshotEncryptionDetails.FieldDefs.ENCRYPTION_PROVIDER,
                ClusterDescription.EncryptionAtRestProvider.AZURE.name())
            .append(
                AzureBackupSnapshotEncryptionCredentials.FieldDefs.AZURE_CLIENT_ID, "azureClientId")
            .append(
                AzureBackupSnapshotEncryptionCredentials.FieldDefs.AZURE_TENANT_ID, "azureTenantId")
            .append(AzureBackupSnapshotEncryptionCredentials.FieldDefs.AZURE_SECRET, secretKey)
            .append(
                AzureBackupSnapshotEncryptionCredentials.FieldDefs.AZURE_ENVIRONMENT,
                SupportedAzureEnvironment.AZURE.name())
            .append(
                AzureBackupSnapshotEncryptionCredentials.FieldDefs.AZURE_SUBSCRIPTION_ID,
                "azureSubscriptionId")
            .append(
                AzureBackupSnapshotEncryptionCredentials.FieldDefs.AZURE_RESOURCE_GROUP_NAME,
                "azureResourceGroupName")
            .append(
                AzureBackupSnapshotEncryptionCredentials.FieldDefs.AZURE_KEY_VAULT_NAME,
                "azureKeyVaultName")
            .append(
                AzureBackupSnapshotEncryptionCredentials.FieldDefs.AZURE_KEY_IDENTIFIER,
                "azureKeyIdentifier");
    return encryptionDetails;
  }
}
