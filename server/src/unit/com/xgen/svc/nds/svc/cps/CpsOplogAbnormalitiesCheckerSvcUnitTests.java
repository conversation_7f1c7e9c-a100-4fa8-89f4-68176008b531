package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.MongoException;
import com.xgen.cloud.common.driverwrappers._public.legacy.DB;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCollection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CpsOplogAbnormalitiesCheckerSvcUnitTests {
  @Mock private DB _database;
  @Mock private DBCollection _collection;

  @Spy @InjectMocks private CpsOplogAbnormalitiesCheckerSvc _service;

  @Test
  public void checkAbnormalitiesForDatabase_metadataDB_shouldCheckCollections() {
    final ObjectId groupId = new ObjectId();
    final String dbName = groupId.toHexString() + "_cps";
    final ObjectId clusterId = new ObjectId();

    when(_database.getName()).thenReturn(dbName);
    when(_database.getCollectionNames()).thenReturn(Set.of(clusterId.toHexString() + "_cluster1"));
    doNothing().when(_service).checkAbnormalitiesForCollection(any(), any(), any());

    _service.checkAbnormalitiesForDatabase("config1", _database);

    verify(_database).getCollectionNames();
  }

  @Test
  public void checkAbnormalitiesForDatabase_generalException_shouldThrow() {
    final ObjectId groupId = new ObjectId();
    final String dbName = groupId.toHexString() + "_cps";

    when(_database.getName()).thenReturn(dbName);
    when(_database.getCollectionNames()).thenThrow(new RuntimeException("Unexpected error"));

    // Should throw exception
    assertThrows(
        RuntimeException.class, () -> _service.checkAbnormalitiesForDatabase("config1", _database));
  }

  @Test
  public void checkAbnormalitiesForCollection_validCollection_shouldCheckIndexes() {
    final String dbName = "test_db";
    final String collectionName = "test_collection";

    when(_database.getName()).thenReturn(dbName);
    when(_database.getCollection(collectionName)).thenReturn(_collection);

    doNothing().when(_service).checkIndexes(any(), any(), any());
    _service.checkAbnormalitiesForCollection("config1", _database, collectionName);

    verify(_service).checkIndexes("config1", dbName, _collection);
  }

  @Test
  public void checkAbnormalitiesForCollection_mongoException_shouldThrow() {
    final String dbName = "test_db";
    final String collectionName = "test_collection";

    when(_database.getName()).thenReturn(dbName);
    when(_database.getCollection(collectionName)).thenReturn(_collection);
    when(_collection.getIndexInfo()).thenThrow(new MongoException("Connection failed"));

    // Should throw exception
    assertThrows(
        MongoException.class,
        () -> _service.checkAbnormalitiesForCollection("config1", _database, collectionName));
  }

  @Test
  public void checkIndexes_withAllRequiredIndexes_shouldNotDetectAbnormalities() {
    final String configurationId = "config1";
    final String dbName = "test_db";
    final String collectionName = "test_collection";

    when(_collection.getName()).thenReturn(collectionName);
    when(_collection.getIndexInfo()).thenReturn(createCompleteIndexList());

    _service.checkIndexes(configurationId, dbName, _collection);

    assertFalse(
        _service.hasMissingRequiredIndex(
            _service.getIndexSignatures(_collection), dbName, collectionName, configurationId));
  }

  @Test
  public void checkIndexes_withMissingRequiredIndexes_shouldDetectAndRemediate() {
    final String configurationId = "config1";
    final String dbName = "test_db_cps";
    final String collectionName = "507f1f77bcf86cd799439011_cluster1";

    when(_collection.getName()).thenReturn(collectionName);
    // Only has _id index, missing required CPS oplog indexes
    when(_collection.getIndexInfo())
        .thenReturn(
            List.of(new BasicDBObject("name", "_id_").append("key", new BasicDBObject("_id", 1))));

    assertTrue(
        _service.hasMissingRequiredIndex(
            _service.getIndexSignatures(_collection), dbName, collectionName, configurationId));

    doReturn(true).when(_service).remediateMissingIndexes(any(), any(), any());
    _service.checkIndexes(configurationId, dbName, _collection);
    verify(_service).remediateMissingIndexes(dbName, collectionName, configurationId);
  }

  @Test
  public void checkIndexes_withUnexpectedIndexes_shouldDetectAbnormality() {
    final String configurationId = "config1";
    final String dbName = "test_db_cps";
    final String collectionName = "507f1f77bcf86cd799439011_cluster1";

    when(_collection.getName()).thenReturn(collectionName);

    final List<DBObject> indexesWithUnexpected = new ArrayList<>(createCompleteIndexList());
    indexesWithUnexpected.add(
        new BasicDBObject("name", "unexpected_index")
            .append("key", new BasicDBObject("random_field", 1)));
    when(_collection.getIndexInfo()).thenReturn(indexesWithUnexpected);

    _service.checkIndexes(configurationId, dbName, _collection);
    assertFalse(
        _service.hasMissingRequiredIndex(
            _service.getIndexSignatures(_collection), dbName, collectionName, configurationId));
    assertTrue(
        _service.hasUnexpectedIndex(
            _service.getIndexSignatures(_collection), dbName, collectionName, configurationId));
  }

  @Test
  public void hasMissingRequiredIndex_withAllIndexesPresent_shouldReturnFalse() {
    final Set<String> allRequiredSignatures =
        Set.of(
            "_id_1",
            "rsId_1_valid_1_end_1_start_1",
            "rsId_1_valid_1_purged_1_end_1",
            "rsId_1_valid_1_purged_1_end_date_1",
            "purged_date_1_cre_1",
            "purged_1_cre_1",
            "sliceId_1_storageRegionName_1");

    final boolean result =
        _service.hasMissingRequiredIndex(
            allRequiredSignatures, "test_db", "test_collection", "config1");

    assertFalse(result);
  }

  @Test
  public void hasMissingRequiredIndex_withMissingIndexes_shouldReturnTrue() {
    final Set<String> partialSignatures = Set.of("_id_1", "rsId_1_valid_1_end_1_start_1");

    final boolean result =
        _service.hasMissingRequiredIndex(
            partialSignatures, "test_db", "test_collection", "config1");

    assertTrue(result);
  }

  @Test
  public void hasUnexpectedIndex_withOnlyRequiredIndexes_shouldReturnFalse() {
    final Set<String> requiredSignatures =
        Set.of("_id_1", "rsId_1_valid_1_end_1_start_1", "rsId_1_valid_1_purged_1_end_1");

    final boolean result =
        _service.hasUnexpectedIndex(requiredSignatures, "test_db", "test_collection", "config1");

    assertFalse(result);
  }

  @Test
  public void hasUnexpectedIndex_withUnexpectedIndexes_shouldReturnTrue() {
    final Set<String> signaturesWithUnexpected =
        Set.of("_id_1", "rsId_1_valid_1_end_1_start_1", "unexpected_field_1");

    final boolean result =
        _service.hasUnexpectedIndex(
            signaturesWithUnexpected, "test_db", "test_collection", "config1");

    assertTrue(result);
  }

  @Test
  public void getIndexSignatures_withValidIndexes_shouldReturnCorrectSignatures() {
    when(_collection.getIndexInfo())
        .thenReturn(
            Arrays.asList(
                new BasicDBObject("key", new BasicDBObject("_id", 1)),
                new BasicDBObject(
                    "key", new BasicDBObject("rsId", 1).append("valid", 1).append("end", 1))));

    final Set<String> signatures = _service.getIndexSignatures(_collection);

    assertEquals(Set.of("_id_1", "rsId_1_valid_1_end_1"), signatures);
  }

  @Test
  public void getIndexSignatures_withNullIndexKeys_shouldHandleGracefully() {
    when(_collection.getIndexInfo())
        .thenReturn(
            List.of(
                new BasicDBObject("name", "malformed_index") // Missing "key" field
                ));

    final Set<String> signatures = _service.getIndexSignatures(_collection);

    assertTrue(signatures.isEmpty());
  }

  @Test
  public void getIndexSignatures_withEmptyIndexList_shouldReturnEmptySet() {
    when(_collection.getIndexInfo()).thenReturn(new ArrayList<>());

    final Set<String> signatures = _service.getIndexSignatures(_collection);

    assertTrue(signatures.isEmpty());
  }

  @Test
  public void buildIndexSignature_withSingleField_shouldReturnCorrectSignature() {
    final DBObject indexKeys = new BasicDBObject("field1", 1);

    final String signature = _service.buildIndexSignature(indexKeys);

    assertEquals("field1_1", signature);
  }

  @Test
  public void buildIndexSignature_withMultipleFields_shouldReturnCorrectSignature() {
    final DBObject indexKeys =
        new BasicDBObject("field1", 1).append("field2", -1).append("field3", 1);

    final String signature = _service.buildIndexSignature(indexKeys);

    // Note: The order might vary depending on BasicDBObject implementation
    assertTrue(signature.contains("field1_1"));
    assertTrue(signature.contains("field2_-1"));
    assertTrue(signature.contains("field3_1"));
  }

  private List<DBObject> createCompleteIndexList() {
    return Arrays.asList(
        new BasicDBObject("key", new BasicDBObject("_id", 1)),
        new BasicDBObject(
            "key",
            new BasicDBObject("rsId", 1).append("valid", 1).append("end", 1).append("start", 1)),
        new BasicDBObject(
            "key",
            new BasicDBObject("rsId", 1).append("valid", 1).append("purged", 1).append("end", 1)),
        new BasicDBObject(
            "key",
            new BasicDBObject("rsId", 1)
                .append("valid", 1)
                .append("purged", 1)
                .append("end_date", 1)),
        new BasicDBObject("key", new BasicDBObject("purged_date", 1).append("cre", 1)),
        new BasicDBObject("key", new BasicDBObject("purged", 1).append("cre", 1)),
        new BasicDBObject("key", new BasicDBObject("sliceId", 1).append("storageRegionName", 1)));
  }
}
