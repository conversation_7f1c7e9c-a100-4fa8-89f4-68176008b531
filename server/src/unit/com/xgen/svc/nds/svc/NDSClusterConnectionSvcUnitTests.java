package com.xgen.svc.nds.svc;

import static com.xgen.cloud.common.model._public.error.CommonErrorCode.OPERATION_ERROR;
import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.getUuidRepresentationConfiguredCodecRegistry;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.RETURNS_SMART_NULLS;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.mongodb.MongoCommandException;
import com.mongodb.MongoException;
import com.mongodb.MongoSocketException;
import com.mongodb.MongoTimeoutException;
import com.mongodb.ServerAddress;
import com.mongodb.client.FindIterable;
import com.mongodb.client.ListCollectionNamesIterable;
import com.mongodb.client.ListCollectionsIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoIterable;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfo._public.model.EventSource;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.driverwrappers._public.legacy.MongoClient;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.security._public.util.TLSUtil;
import com.xgen.cloud.mongod._public.model.GetCollectionInfoResponse;
import com.xgen.cloud.mongod._public.model.MongoDbCollectionInfo.DbUnknown;
import com.xgen.cloud.nds.atlasSQL._public.model.ui.AtlasSQLSchemaMetadata;
import com.xgen.cloud.nds.atlasSQL._public.model.ui.AtlasSQLSchemaStatusView;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.dbusers.INDSDBRole;
import com.xgen.cloud.nds.project._public.model.dbusers.NDSDBRole;
import com.xgen.cloud.nds.project._public.model.event.ClusterConnectionAudit;
import com.xgen.cloud.nds.project._public.model.event.ClusterConnectionAudit.Type;
import com.xgen.cloud.nds.project._public.svc.NDSDBRoleSvc;
import com.xgen.cloud.nds.project._public.util.NDSClusterConnectionUtil;
import com.xgen.cloud.nds.project._public.view.AggregatedViewInfoView;
import com.xgen.cloud.nds.project._public.view.SearchAggregationResponseView;
import com.xgen.cloud.nds.project._public.view.SearchWildcardPathQueryRequestView;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.util.UnitTestUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.NDSClusterConnectionSvc.Steps;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import io.prometheus.client.Histogram;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import javax.net.ssl.SSLContext;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bson.BsonDocument;
import org.bson.BsonInt32;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(CloudProviderExtension.class)
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class NDSClusterConnectionSvcUnitTests {

  private NDSClusterConnectionSvc _ndsClusterConnectionSvc;
  private AuditSvc _auditSvc;
  @Mock private NDSGroupSvc _ndsGroupSvc;
  @Mock private NDSClusterSvc _ndsClusterSvc;

  private AuditInfo _auditInfo;

  @BeforeEach
  public void setUp() {
    AppUser appUser = mock(AppUser.class);
    _auditInfo =
        new AuditInfo(
            "",
            null,
            null,
            EventSource.USER,
            appUser.getId(),
            appUser.getUsername(),
            false,
            false,
            appUser.getCurrentOrgId(),
            appUser.getType());

    _auditSvc = mock(AuditSvc.class, RETURNS_SMART_NULLS);
    final AppSettings appSettings = mock(AppSettings.class);
    final NDSDBRoleSvc ndsdbRoleSvc = UnitTestUtils.create(NDSDBRoleSvc.class).withNoArgs();
    _ndsClusterConnectionSvc =
        spy(
            UnitTestUtils.create(NDSClusterConnectionSvc.class)
                .withArgs(appSettings, _auditSvc, _ndsGroupSvc, _ndsClusterSvc, ndsdbRoleSvc));
  }

  @Test
  public void testObserveGenerateX509Cert() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(clusterDescription.getRegionNames())
        .thenReturn(
            Set.of(AWSRegionName.US_WEST_1, AWSRegionName.US_EAST_1, AWSRegionName.EU_CENTRAL_1));
    when(clusterDescription.getMaxInstanceSize(NodeType.ELECTABLE))
        .thenReturn(Optional.of(AWSNDSInstanceSize.M10));
    final Histogram.Timer timer = mock(Histogram.Timer.class);
    final String regionsLabelValues =
        AWSRegionName.EU_CENTRAL_1.getName() + "," + AWSRegionName.US_EAST_1.getName();
    doReturn(timer)
        .when(_ndsClusterConnectionSvc)
        .startDurationTimer(
            eq(Steps.GENERATE_X509_CERT),
            eq(regionsLabelValues),
            eq(AWSNDSInstanceSize.M10.getPrintableName()));

    try {
      _ndsClusterConnectionSvc.generateX509Cert(
          AuditInfoHelpers.fromSystem(),
          mock(INDSDBRole.class),
          clusterDescription,
          mock(NDSGroup.class));
      fail(); // generate x509 cert will fail given dummy data
    } catch (Exception ignored) {
    }

    verify(_ndsClusterConnectionSvc, times(1))
        .startDurationTimer(
            eq(Steps.GENERATE_X509_CERT),
            eq(regionsLabelValues),
            eq(AWSNDSInstanceSize.M10.getPrintableName()));
    verify(timer, times(1)).observeDuration();
  }

  @Test
  public void testObserveAggregate() throws SvcException {
    final var groupId = new ObjectId();
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(clusterDescription.getUniqueId()).thenReturn(new ObjectId());
    when(clusterDescription.getGroupId()).thenReturn(groupId);
    when(clusterDescription.getRegionNames()).thenReturn(Set.of(AWSRegionName.US_EAST_1));
    when(clusterDescription.getMaxInstanceSize(NodeType.ELECTABLE))
        .thenReturn(Optional.of(AWSNDSInstanceSize.M10));

    final String dbName = "database";
    final String collectionName = "collection";
    final MongoClient mongoClient = mock(MongoClient.class);
    final MongoDatabase mongoDatabase = mock(MongoDatabase.class);
    when(mongoDatabase.runCommand(new BasicDBObject("ping", 1))).thenReturn(mock(Document.class));
    when(mongoClient.getDatabase(dbName)).thenReturn(mongoDatabase);
    doReturn(mongoClient).when(_ndsClusterConnectionSvc).getMongoClient(any(), any(), any());
    doReturn(List.of(new Document()))
        .when(_ndsClusterConnectionSvc)
        .runAggregation(any(), any(), any(), any());

    final Histogram.Timer pingTimer = mock(Histogram.Timer.class);
    doReturn(pingTimer)
        .when(_ndsClusterConnectionSvc)
        .startDurationTimer(
            eq(Steps.PING_DATABASE),
            eq(AWSRegionName.US_EAST_1.getName()),
            eq(AWSNDSInstanceSize.M10.getPrintableName()));
    final Histogram.Timer runAggregationTimer = mock(Histogram.Timer.class);
    doReturn(runAggregationTimer)
        .when(_ndsClusterConnectionSvc)
        .startDurationTimer(
            eq(Steps.RUN_SEARCH_AGGREGATION),
            eq(AWSRegionName.US_EAST_1.getName()),
            eq(AWSNDSInstanceSize.M10.getPrintableName()));
    final Histogram.Timer loadDocumentTimer = mock(Histogram.Timer.class);
    doReturn(loadDocumentTimer)
        .when(_ndsClusterConnectionSvc)
        .startDurationTimer(
            eq(Steps.LOAD_DOCUMENTS),
            eq(AWSRegionName.US_EAST_1.getName()),
            eq(AWSNDSInstanceSize.M10.getPrintableName()));

    final SearchWildcardPathQueryRequestView requestView =
        new SearchWildcardPathQueryRequestView("default", "query", "syn");
    final Document explainResult =
        new Document("stages", List.of(new Document("executionTimeMillisEstimate", 500L)));
    when(mongoDatabase.runCommand(requestView.getExplainCmd(collectionName)))
        .thenReturn(explainResult);

    _ndsClusterConnectionSvc.aggregate(
        clusterDescription, dbName, collectionName, requestView, _auditInfo);

    verify(_ndsClusterConnectionSvc, times(1))
        .startDurationTimer(
            eq(Steps.PING_DATABASE),
            eq(AWSRegionName.US_EAST_1.getName()),
            eq(AWSNDSInstanceSize.M10.getPrintableName()));
    verify(pingTimer, times(1)).observeDuration();
    verify(_ndsClusterConnectionSvc, times(1))
        .startDurationTimer(
            eq(Steps.RUN_SEARCH_AGGREGATION),
            eq(AWSRegionName.US_EAST_1.getName()),
            eq(AWSNDSInstanceSize.M10.getPrintableName()));
    verify(runAggregationTimer, times(1)).observeDuration();
    verify(_ndsClusterConnectionSvc, times(1))
        .observe(
            eq(0.5),
            eq(Steps.SEARCH_EXECUTION_TIME),
            eq(AWSRegionName.US_EAST_1.getName()),
            eq(AWSNDSInstanceSize.M10.getPrintableName()));
    verify(_ndsClusterConnectionSvc, never())
        .startDurationTimer(eq(Steps.LOAD_DOCUMENTS), any(), any());
    verify(loadDocumentTimer, never()).observeDuration();

    ArgumentCaptor<Event> auditEvent = ArgumentCaptor.forClass(Event.class);
    // verify auditing
    verify(_auditSvc, times(1)).saveAuditEvent(auditEvent.capture());
    assertEquals(
        ClusterConnectionAudit.Type.CLUSTER_CONNECTION_AGGREGATE,
        auditEvent.getValue().getEventType());
    assertEquals(groupId, auditEvent.getValue().getGroupId());

    final SearchWildcardPathQueryRequestView loadDocumentsRequest =
        new SearchWildcardPathQueryRequestView("default", "", null);
    _ndsClusterConnectionSvc.aggregate(
        clusterDescription, dbName, collectionName, loadDocumentsRequest, _auditInfo);

    verify(_ndsClusterConnectionSvc, times(2))
        .startDurationTimer(
            eq(Steps.PING_DATABASE),
            eq(AWSRegionName.US_EAST_1.getName()),
            eq(AWSNDSInstanceSize.M10.getPrintableName()));
    verify(pingTimer, times(2)).observeDuration();
    verify(_ndsClusterConnectionSvc, times(1))
        .startDurationTimer(
            eq(Steps.LOAD_DOCUMENTS),
            eq(AWSRegionName.US_EAST_1.getName()),
            eq(AWSNDSInstanceSize.M10.getPrintableName()));
    verify(loadDocumentTimer, times(1)).observeDuration();
  }

  @Test
  public void testAggregate() throws SvcException {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    when(clusterDescription.getUniqueId()).thenReturn(new ObjectId());
    when(clusterDescription.getRegionNames()).thenReturn(Set.of(AWSRegionName.US_EAST_1));

    final String dbName = "database";
    final String collectionName = "collection";
    final MongoClient mongoClient = mock(MongoClient.class);
    final MongoDatabase mongoDatabase = mock(MongoDatabase.class);
    when(mongoDatabase.runCommand(new BasicDBObject("ping", 1))).thenReturn(mock(Document.class));
    when(mongoClient.getDatabase(dbName)).thenReturn(mongoDatabase);
    doReturn(mongoClient).when(_ndsClusterConnectionSvc).getMongoClient(any(), any(), any());

    final SearchWildcardPathQueryRequestView loadDocumentsRequestView =
        new SearchWildcardPathQueryRequestView("default", "", null);
    final SearchWildcardPathQueryRequestView searchRequestView =
        new SearchWildcardPathQueryRequestView("default", "query", null);

    // run aggregation fails
    {
      doThrow(new MongoCommandException(new BsonDocument("code", new BsonInt32(1)), null))
          .when(_ndsClusterConnectionSvc)
          .runAggregation(any(), any(), any(), any());

      var exception =
          assertThrows(
              SvcException.class,
              () ->
                  _ndsClusterConnectionSvc.aggregate(
                      clusterDescription,
                      dbName,
                      collectionName,
                      loadDocumentsRequestView,
                      _auditInfo));
      assertEquals(OPERATION_ERROR, exception.getErrorCode());
    }

    {
      var exception =
          assertThrows(
              SvcException.class,
              () ->
                  _ndsClusterConnectionSvc.aggregate(
                      clusterDescription, dbName, collectionName, searchRequestView, _auditInfo));
      assertEquals(OPERATION_ERROR, exception.getErrorCode());
    }

    // load documents aggregation success
    {
      final List<Document> documents =
          List.of(
              new Document("k1", "v1"),
              new Document("k2", "v2"),
              new Document("k3", "v3".getBytes()));
      final List<String> results =
          documents.stream()
              .map(doc -> doc.toJson(NDSClusterConnectionUtil.WRITE_EXTENDED))
              .collect(Collectors.toList());
      doReturn(results).when(_ndsClusterConnectionSvc).runAggregation(any(), any(), any(), any());

      final SearchAggregationResponseView responseView =
          _ndsClusterConnectionSvc.aggregate(
              clusterDescription, dbName, collectionName, loadDocumentsRequestView, _auditInfo);
      assertEquals(results, responseView.getDocuments());
      assertNull(responseView.getTimeElapsed());
    }

    final Document explainResult =
        new Document("stages", List.of(new Document("executionTimeMillisEstimate", 500L)));
    when(mongoDatabase.runCommand(searchRequestView.getExplainCmd(collectionName)))
        .thenReturn(explainResult);
    doReturn(getSearchAggregationResults())
        .when(_ndsClusterConnectionSvc)
        .runAggregation(any(), any(), any(), any());

    // search aggregation success
    {
      final SearchAggregationResponseView responseView =
          _ndsClusterConnectionSvc.aggregate(
              clusterDescription, dbName, collectionName, searchRequestView, _auditInfo);
      testSearchHighlightResults(responseView.getDocuments());
      assertEquals(0.5, responseView.getTimeElapsed(), 0.0001);
    }

    // run explain cmd fails
    {
      when(mongoDatabase.runCommand(searchRequestView.getExplainCmd(collectionName)))
          .thenThrow(new MongoCommandException(new BsonDocument("code", new BsonInt32(1)), null));
      final SearchAggregationResponseView responseView =
          _ndsClusterConnectionSvc.aggregate(
              clusterDescription, dbName, collectionName, searchRequestView, _auditInfo);
      testSearchHighlightResults(responseView.getDocuments());
      assertNull(responseView.getTimeElapsed());
    }
  }

  @Test
  public void testGetExecutionTimeSeconds() {
    // invalid result
    {
      assertThrows(
          ClassCastException.class,
          () ->
              NDSClusterConnectionUtil.getExecutionTimeSeconds(
                  new Document("stages", new Document())));

      assertEquals(
          Optional.empty(),
          NDSClusterConnectionUtil.getExecutionTimeSeconds(
              new Document("stages", new ArrayList<Document>())));
      assertEquals(
          Optional.empty(),
          NDSClusterConnectionUtil.getExecutionTimeSeconds(
              new Document("stages", List.of(new Document("key", "value")))));
    }

    // success
    final Optional<Double> executionTime =
        NDSClusterConnectionUtil.getExecutionTimeSeconds(
            new Document("stages", List.of(new Document("executionTimeMillisEstimate", 500L))));
    assertTrue(executionTime.isPresent());
    assertEquals(0.5, executionTime.get(), 0.0001);
  }

  @Test
  public void testConvertSearchResultToHighlightedResult() {
    testSearchHighlightResults(getSearchAggregationResults());
  }

  @Test
  public void testCreateCollection() throws SvcException {
    final ClusterDescription cluster =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    final ObjectId groupId = cluster.getGroupId();
    final String dbName = "database";
    final String collectionName = "collection";
    final MongoClient mongoClient = mock(MongoClient.class);
    final MongoDatabase mongoDatabase = mock(MongoDatabase.class);
    doNothing().when(mongoDatabase).createCollection(eq(collectionName));
    when(mongoClient.getDatabase(dbName)).thenReturn(mongoDatabase);
    doReturn(mongoClient).when(_ndsClusterConnectionSvc).getMongoClient(any(), any(), any());
    _ndsClusterConnectionSvc.createCollection(cluster, dbName, collectionName, _auditInfo);

    ArgumentCaptor<Event> auditEvent = ArgumentCaptor.forClass(Event.class);
    // verify auditing
    verify(_auditSvc, times(1)).saveAuditEvent(auditEvent.capture());
    assertEquals(
        ClusterConnectionAudit.Type.CLUSTER_CONNECTION_CREATE_COLLECTION,
        auditEvent.getValue().getEventType());
    assertEquals(groupId, auditEvent.getValue().getGroupId());

    // verify db op
    verify(mongoDatabase, times(1)).createCollection(eq(collectionName));

    // with MongoException exception
    doThrow(new MongoException(420, "error")).when(mongoDatabase).createCollection(any());
    try {
      _ndsClusterConnectionSvc.createCollection(cluster, dbName, collectionName, _auditInfo);
      fail("Expected operation to fail");
    } catch (final MongoException e) {
      assertEquals(420, e.getCode());
    }

    // with timeout exception
    doThrow(new MongoTimeoutException("timeout")).when(mongoDatabase).createCollection(any());
    try {
      _ndsClusterConnectionSvc.createCollection(cluster, dbName, collectionName, _auditInfo);
      fail("Expected operation to fail");
    } catch (final SvcException e) {
      assertEquals(CommonErrorCode.TIMEOUT, e.getErrorCode());
    }

    // with socket exception
    doThrow(new MongoSocketException("socket", new ServerAddress()))
        .when(mongoDatabase)
        .createCollection(any());
    try {
      _ndsClusterConnectionSvc.createCollection(cluster, dbName, collectionName, _auditInfo);
      fail("Expected operation to fail");
    } catch (final SvcException e) {
      assertEquals(OPERATION_ERROR, e.getErrorCode());
    }
  }

  @Test
  public void testListSqlNamespaces_skipOrphanSchemas() throws SvcException {
    final ClusterDescription cluster =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    final AuditInfo auditInfo = mock(AuditInfo.class);
    final MongoClient mongoClient = mock(MongoClient.class);
    final String DATABASE_NAME = "myDatabase";
    final String COLLECTION_NAME = "col1";
    final String COLLECTION_TO_SKIP = "otherCol";
    final Date LAST_UPDATED = new Date();

    final MongoDatabase mongoDatabase = mock(MongoDatabase.class);
    lenient().doReturn(mongoDatabase).when(mongoClient).getDatabase(DATABASE_NAME);
    doReturn(mongoClient).when(_ndsClusterConnectionSvc).getMongoClient(any(), any(), any(), any());

    final MongoIterable<?> databaseIterable = mock(MongoIterable.class);
    final MongoCursor<?> dbCursor = mock(MongoCursor.class);
    doReturn(true, false).when(dbCursor).hasNext();
    doReturn(DATABASE_NAME).when(dbCursor).next();
    doReturn(dbCursor).when(databaseIterable).iterator();
    doReturn(databaseIterable).when(mongoClient).listDatabaseNames();

    // One user collection, one __sql_schema collection. Note COLLECTION_TO_SKIP not returned from
    // db, does not exist.
    final List<String> expectedCollectionNames = List.of(COLLECTION_NAME, "__sql_schemas");
    mockListCollectionNamesCall(mongoDatabase, expectedCollectionNames.stream());

    // Mock [.find, .projection, .into] on __sql_schemas collection
    MongoCollection<?> collection = mock(MongoCollection.class);
    doReturn(collection)
        .when(mongoDatabase)
        .getCollection("__sql_schemas", AtlasSQLSchemaMetadata.class);

    FindIterable<?> findIterable = mock(FindIterable.class);
    doReturn(findIterable).when(findIterable).projection(any(Document.class));
    List<AtlasSQLSchemaMetadata> schemaDocs =
        List.of(
            new AtlasSQLSchemaMetadata(
                COLLECTION_NAME, AtlasSQLSchemaStatusView.AVAILABLE.getStatusName(), LAST_UPDATED),
            new AtlasSQLSchemaMetadata(
                COLLECTION_TO_SKIP,
                AtlasSQLSchemaStatusView.AVAILABLE.getStatusName(),
                LAST_UPDATED));
    doAnswer(
            invocation -> {
              List<AtlasSQLSchemaMetadata> target = invocation.getArgument(0);
              target.addAll(schemaDocs);
              return target;
            })
        .when(findIterable)
        .into(any());
    doReturn(findIterable).when(collection).find();

    HashMap<String, HashMap<String, AtlasSQLSchemaMetadata>> result =
        _ndsClusterConnectionSvc.listSqlNamespaces(cluster, auditInfo);
    assertTrue(result.containsKey(DATABASE_NAME));
    // We don't want to track __sql_schemas as a namespace in of itself.
    assertFalse(result.get(DATABASE_NAME).containsKey("__sql_schemas"));
    // We don't want to track collection/view names that only exist in the __sql_schema collection
    // and not the database itself.
    assertFalse(result.get(DATABASE_NAME).containsKey(COLLECTION_TO_SKIP));

    AtlasSQLSchemaMetadata doc = result.get(DATABASE_NAME).get(COLLECTION_NAME);
    assertEquals("AVAILABLE", doc.getStatus());
    assertEquals(LAST_UPDATED, doc.getLastUpdated());
  }

  @Test
  public void testListSqlNamespaces_mongoFailures() throws SvcException {
    final ClusterDescription cluster =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    final AuditInfo auditInfo = mock(AuditInfo.class);

    // with timeout exception
    doThrow(new MongoTimeoutException("timeout"))
        .when(_ndsClusterConnectionSvc)
        .getMongoClient(any(), any(), any(), any());
    try {
      _ndsClusterConnectionSvc.listSqlNamespaces(cluster, auditInfo);
      fail("Expected operation to fail");
    } catch (final SvcException e) {
      assertEquals(CommonErrorCode.TIMEOUT, e.getErrorCode());
    }

    // with socket exception
    doThrow(new MongoSocketException("socket", new ServerAddress()))
        .when(_ndsClusterConnectionSvc)
        .getMongoClient(any(), any(), any(), any());
    try {
      _ndsClusterConnectionSvc.listSqlNamespaces(cluster, auditInfo);
      fail("Expected operation to fail");
    } catch (final SvcException e) {
      assertEquals(OPERATION_ERROR, e.getErrorCode());
    }
  }

  private void assertEmptySqlNamespaceProjectionResult(
      final AtlasSQLSchemaMetadata result, final String expectedCollectionOrViewName) {
    assertEquals(expectedCollectionOrViewName, result.getId());
    assertEquals(AtlasSQLSchemaStatusView.EMPTY.getStatusName(), result.getStatus());
    assertNull(result.getLastUpdated());
  }

  @Test
  public void testListSqlNamespaces_handlesNoSqlCollections() throws SvcException {
    final ClusterDescription cluster =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    final AuditInfo auditInfo = mock(AuditInfo.class);
    final MongoClient mongoClient = mock(MongoClient.class);
    final String DATABASE_NAME_1 = "myDatabase1";
    final String DATABASE_NAME_2 = "myDatabase2";
    final String COLLECTION_NAME_1 = "col1";
    final String COLLECTION_NAME_2 = "col2";

    final MongoDatabase mongoDatabase1 = mock(MongoDatabase.class);
    final MongoDatabase mongoDatabase2 = mock(MongoDatabase.class);
    lenient().doReturn(mongoDatabase1).when(mongoClient).getDatabase(DATABASE_NAME_1);
    lenient().doReturn(mongoDatabase2).when(mongoClient).getDatabase(DATABASE_NAME_2);
    doReturn(mongoClient).when(_ndsClusterConnectionSvc).getMongoClient(any(), any(), any(), any());

    final MongoIterable<?> databaseIterable = mock(MongoIterable.class);
    final MongoCursor<?> dbCursor = mock(MongoCursor.class);
    doReturn(true, true, false).when(dbCursor).hasNext();
    doReturn(DATABASE_NAME_1, DATABASE_NAME_2).when(dbCursor).next();
    doReturn(dbCursor).when(databaseIterable).iterator();
    doReturn(databaseIterable).when(mongoClient).listDatabaseNames();

    // mock listCollectionNames
    final ListCollectionNamesIterable mockCollectionNamesIterableDb1 =
        mock(ListCollectionNamesIterable.class);
    final ListCollectionNamesIterable mockCollectionNamesIterableDb2 =
        mock(ListCollectionNamesIterable.class);
    final List<String> expectedCollectionNames = List.of(COLLECTION_NAME_1, COLLECTION_NAME_2);
    mockListCollectionNamesCall(
        mongoDatabase1, expectedCollectionNames.stream(), mockCollectionNamesIterableDb1);
    mockListCollectionNamesCall(
        mongoDatabase2, expectedCollectionNames.stream(), mockCollectionNamesIterableDb2);

    HashMap<String, HashMap<String, AtlasSQLSchemaMetadata>> result =
        _ndsClusterConnectionSvc.listSqlNamespaces(cluster, auditInfo);

    assertTrue(result.containsKey(DATABASE_NAME_1));
    assertTrue(result.get(DATABASE_NAME_1).containsKey(COLLECTION_NAME_1));
    assertTrue(result.get(DATABASE_NAME_1).containsKey(COLLECTION_NAME_2));

    final AtlasSQLSchemaMetadata db1Col1 = result.get(DATABASE_NAME_1).get(COLLECTION_NAME_1);
    assertEmptySqlNamespaceProjectionResult(db1Col1, COLLECTION_NAME_1);

    final AtlasSQLSchemaMetadata db1Col2 = result.get(DATABASE_NAME_1).get(COLLECTION_NAME_2);
    assertEmptySqlNamespaceProjectionResult(db1Col2, COLLECTION_NAME_2);

    assertTrue(result.containsKey(DATABASE_NAME_2));
    assertTrue(result.get(DATABASE_NAME_2).containsKey(COLLECTION_NAME_1));
    assertTrue(result.get(DATABASE_NAME_2).containsKey(COLLECTION_NAME_2));

    final AtlasSQLSchemaMetadata db2Col1 = result.get(DATABASE_NAME_1).get(COLLECTION_NAME_1);
    assertEmptySqlNamespaceProjectionResult(db2Col1, COLLECTION_NAME_1);

    final AtlasSQLSchemaMetadata db2Col2 = result.get(DATABASE_NAME_2).get(COLLECTION_NAME_2);
    assertEmptySqlNamespaceProjectionResult(db2Col2, COLLECTION_NAME_2);
  }

  @Test
  public void testLookupDocumentFieldNames() {
    final Document root = exampleDocumentForLookupFieldNames();
    final Document documentField = exampleDocumentForLookupFieldNames();
    addSubDocumentForLookupFieldNames(documentField, 1, 5);
    final Document documentFieldTooDeep = exampleDocumentForLookupFieldNames();
    addSubDocumentForLookupFieldNames(documentFieldTooDeep, 1, 15);
    root.append("documentField", documentField)
        .append("documentFieldTooDeep", documentFieldTooDeep);
    final List<String> fieldsToCheck = List.of("stringField", "intField", "boolField", "listField");
    final List<String> fieldNames =
        NDSClusterConnectionUtil.lookupDocumentFieldNames(
            root.toBsonDocument(
                BsonDocument.class, getUuidRepresentationConfiguredCodecRegistry()));

    fieldsToCheck.forEach(field -> assertTrue(fieldNames.contains(field)));
    final String fieldNotExist = // documentField.level1...level5
        IntStream.range(1, 6)
            .mapToObj(idx -> String.format("level%d", idx))
            .reduce(
                "documentField",
                (prefix, docField) -> {
                  // documentField1\.(level%d\.)*\.[string|int|bool|list]Field
                  fieldsToCheck.forEach(
                      field -> assertTrue(fieldNames.contains(prefix + "." + field)));
                  return prefix + "." + docField;
                });
    assertFalse(fieldNames.contains(fieldNotExist));

    final String fieldCutoff = // documentFieldTooDeep.level1...level9
        IntStream.range(1, NDSClusterConnectionUtil.LOOKUP_DOCUMENT_FIELD_NAME_MAX_DEPTH)
            .mapToObj(idx -> String.format("level%d", idx))
            .reduce(
                "documentFieldTooDeep",
                (prefix, docField) -> {
                  fieldsToCheck.forEach(
                      field -> assertTrue(fieldNames.contains(prefix + "." + field)));
                  return prefix + "." + docField;
                });
    assertTrue(fieldNames.contains(fieldCutoff));
    fieldsToCheck.forEach(field -> assertFalse(fieldNames.contains(fieldCutoff + "." + field)));
  }

  @Test
  public void testIsSystemDatabase() {
    assertTrue(NDSClusterConnectionSvc.isSystemDatabase("admin"));
    assertTrue(NDSClusterConnectionSvc.isSystemDatabase("config"));
    assertTrue(NDSClusterConnectionSvc.isSystemDatabase("local"));
    assertFalse(NDSClusterConnectionSvc.isSystemDatabase("sample_airbnb"));
  }

  @Test
  public void testIsSystemCollection() {
    assertTrue(NDSClusterConnectionSvc.isSystemCollection("system.views"));
    assertTrue(NDSClusterConnectionSvc.isSystemCollection("system.buckets.listingsAndReviews"));
    assertFalse(NDSClusterConnectionSvc.isSystemCollection("listingsAndReviews"));
  }

  @Test
  public void testListDatabaseNames() throws SvcException {
    final String clusterName = "clusterName";
    final String databaseName = "databaseName";
    final AuditInfo auditInfo = spy(AuditInfo.class);

    // set up database
    final MongoDatabase database = setUpDatabase(databaseName);

    // set up cluster
    final var cluster = new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    final ObjectId groupId = cluster.getGroupId();
    doReturn(Optional.of(cluster))
        .when(_ndsClusterSvc)
        .getActiveClusterDescription(groupId, clusterName);

    // successfully returns collection names
    final List<String> expectedCollectionNames = List.of("col1", "col2");
    mockListCollectionsCall(database, expectedCollectionNames.stream());

    final List<String> actualCollectionNames =
        _ndsClusterConnectionSvc.listCollectionNames(groupId, clusterName, databaseName, auditInfo);

    assertEquals(actualCollectionNames, expectedCollectionNames);
    ArgumentCaptor<Event> auditEvent = ArgumentCaptor.forClass(Event.class);
    // verify auditing
    verify(_auditSvc, times(1)).saveAuditEvent(auditEvent.capture());
    assertEquals(
        ClusterConnectionAudit.Type.CLUSTER_CONNECTION_GET_DATABASE_COLLECTIONS,
        auditEvent.getValue().getEventType());
    assertEquals(groupId, auditEvent.getValue().getGroupId());

    // verify database operation
    verify(database, times(1)).listCollections(GetCollectionInfoResponse.class);

    // with MongoException exception
    doThrow(new MongoException(420, "error"))
        .when(database)
        .listCollections(GetCollectionInfoResponse.class);
    var errorException =
        assertThrows(
            MongoException.class,
            () ->
                _ndsClusterConnectionSvc.listCollectionNames(
                    groupId, clusterName, databaseName, auditInfo));
    assertEquals(420, errorException.getCode());

    // with timeout exception
    doThrow(new MongoTimeoutException("timeout"))
        .when(database)
        .listCollections(GetCollectionInfoResponse.class);
    var timeoutException =
        assertThrows(
            SvcException.class,
            () ->
                _ndsClusterConnectionSvc.listCollectionNames(
                    groupId, clusterName, databaseName, auditInfo));
    assertEquals(CommonErrorCode.TIMEOUT, timeoutException.getErrorCode());

    // with socket exception
    doThrow(new MongoSocketException("socket", new ServerAddress()))
        .when(database)
        .listCollections(GetCollectionInfoResponse.class);
    var socketException =
        assertThrows(
            SvcException.class,
            () ->
                _ndsClusterConnectionSvc.listCollectionNames(
                    groupId, clusterName, databaseName, auditInfo));
    assertEquals(OPERATION_ERROR, socketException.getErrorCode());
  }

  @Test
  public void testListNamespaces() throws SvcException {
    final String clusterName = "clusterName";
    final String databaseName = "databaseName";
    final AuditInfo auditInfo = spy(AuditInfo.class);

    // set up database
    setUpDatabase(databaseName);

    // set up cluster
    final var cluster = new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());

    // successfully returns collection names
    mockListNamespacesCall(
        _ndsClusterConnectionSvc.getMongoClient(
            auditInfo, NDSDBRole.factoryCreate("admin", "readAnyDatabase"), cluster));

    _ndsClusterConnectionSvc.listNamespaces(cluster, auditInfo);

    ArgumentCaptor<Event> auditEvent = ArgumentCaptor.forClass(Event.class);
    // verify auditing
    verify(_auditSvc, times(1)).saveAuditEvent(auditEvent.capture());
    assertEquals(
        ClusterConnectionAudit.Type.CLUSTER_CONNECTION_GET_DATABASE_NAMESPACES,
        auditEvent.getValue().getEventType());
    assertEquals(cluster.getGroupId(), auditEvent.getValue().getGroupId());

    // with timeout exception
    doThrow(new MongoTimeoutException("timeout"))
        .when(_ndsClusterConnectionSvc)
        .getMongoClient(any(), any(), any());
    try {
      _ndsClusterConnectionSvc.listNamespaces(cluster, auditInfo);
      fail("Expected operation to fail");
    } catch (final SvcException e) {
      assertEquals(CommonErrorCode.TIMEOUT, e.getErrorCode());
    }

    // with socket exception
    doThrow(new MongoSocketException("socket", new ServerAddress()))
        .when(_ndsClusterConnectionSvc)
        .getMongoClient(any(), any(), any());
    try {
      _ndsClusterConnectionSvc.listNamespaces(cluster, auditInfo);
      fail("Expected operation to fail");
    } catch (final SvcException e) {
      assertEquals(OPERATION_ERROR, e.getErrorCode());
    }
  }

  @Test
  public void testListAggregatedViewInfos() throws SvcException {
    final String databaseName = "databaseName";
    final AuditInfo auditInfo = spy(AuditInfo.class);
    final MongoClient mongoClient = mock(MongoClient.class);

    // set up cluster
    final var cluster = new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());

    // set up database
    final MongoDatabase mongoDatabase = setUpDatabase(databaseName, mongoClient);
    doReturn(databaseName).when(mongoDatabase).getName();

    // mock listDatabaseNames
    final MongoIterable<?> databaseIterable = mock(MongoIterable.class);
    final MongoCursor<?> dbCursor = mock(MongoCursor.class);
    doReturn(true, false).when(dbCursor).hasNext();
    doReturn(databaseName).when(dbCursor).next();
    doReturn(dbCursor).when(databaseIterable).iterator();
    doReturn(databaseIterable).when(mongoClient).listDatabaseNames();

    Document rootColl =
        new Document() // root collection
            .append("type", "collection")
            .append("name", "rootColl")
            .append("info", new Document("uuid", "uuid1"));

    Document view1 =
        new Document() // view on root collection
            .append("type", "view")
            .append("name", "view1")
            .append(
                "options",
                new Document(
                        "pipeline",
                        List.of(new Document("$addFields", new Document("newField", "$price"))))
                    .append("viewOn", "rootColl"));

    Document view2 =
        new Document() // view on view
            .append("type", "view")
            .append("name", "view2")
            .append(
                "options",
                new Document("pipeline", List.of(new Document("$limit", 1)))
                    .append("viewOn", "view1"));

    List<Document> mockCollectionsList = List.of(rootColl, view1, view2);
    final ListCollectionsIterable<?> collectionsIterable = mock(ListCollectionsIterable.class);

    doAnswer(
            invocation -> {
              Iterator<?> iterator = mockCollectionsList.iterator();
              MongoCursor<?> cursor = mock(MongoCursor.class);
              doAnswer(inner -> iterator.next()).when(cursor).next();
              doAnswer(inner -> iterator.hasNext()).when(cursor).hasNext();

              return cursor;
            })
        .when(collectionsIterable)
        .iterator();

    doReturn(collectionsIterable).when(mongoDatabase).listCollections();

    final List<AggregatedViewInfoView> response =
        _ndsClusterConnectionSvc.listAggregatedViewInfos(cluster, auditInfo);

    // verify the returned aggregated view infos
    Assertions.assertNotNull(response);
    assertEquals(2, response.size());
    assertEquals(
        List.of(
            new AggregatedViewInfoView(
                databaseName,
                "view1",
                "rootColl",
                "rootColl",
                "uuid1",
                "[{\"$addFields\": {\"newField\": \"$price\"}}]",
                true),
            new AggregatedViewInfoView(
                databaseName,
                "view2",
                "view1",
                "rootColl",
                "uuid1",
                "[{\"$addFields\": {\"newField\": \"$price\"}}, {\"$limit\": 1}]",
                false)),
        response);

    ArgumentCaptor<Event> auditEvent = ArgumentCaptor.forClass(Event.class);
    // verify auditing
    verify(_auditSvc, times(1)).saveAuditEvent(auditEvent.capture());
    assertEquals(
        Type.CLUSTER_CONNECTION_GET_AGGREGATED_VIEW_INFOS, auditEvent.getValue().getEventType());
    assertEquals(cluster.getGroupId(), auditEvent.getValue().getGroupId());

    // with timeout exception
    doThrow(new MongoTimeoutException("timeout"))
        .when(_ndsClusterConnectionSvc)
        .getMongoClient(any(), any(), any());
    try {
      _ndsClusterConnectionSvc.listAggregatedViewInfos(cluster, auditInfo);
      fail("Expected operation to fail");
    } catch (final SvcException e) {
      assertEquals(CommonErrorCode.TIMEOUT, e.getErrorCode());
    }

    // with socket exception
    doThrow(new MongoSocketException("socket", new ServerAddress()))
        .when(_ndsClusterConnectionSvc)
        .getMongoClient(any(), any(), any());
    try {
      _ndsClusterConnectionSvc.listAggregatedViewInfos(cluster, auditInfo);
      fail("Expected operation to fail");
    } catch (final SvcException e) {
      assertEquals(OPERATION_ERROR, e.getErrorCode());
    }
  }

  @Test
  public void testHasNamespaces() throws SvcException {
    final String databaseName = "databaseName";
    final String collectionName = "collectionName";
    final AuditInfo auditInfo = spy(AuditInfo.class);
    final MongoClient mongoClient = mock(MongoClient.class);

    // set up cluster
    final var cluster = new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());

    // set up database
    final MongoDatabase mongoDatabase = setUpDatabase(databaseName, mongoClient);

    // mock listDatabaseNames
    final MongoIterable<?> databaseIterable = mock(MongoIterable.class);
    final MongoCursor<?> dbCursor = mock(MongoCursor.class);
    doReturn(true, false).when(dbCursor).hasNext();
    doReturn(databaseName).when(dbCursor).next();
    doReturn(dbCursor).when(databaseIterable).iterator();
    doReturn(databaseIterable).when(mongoClient).listDatabaseNames();

    // mock listCollectionNames
    final ListCollectionNamesIterable collectionNamesIterable =
        mock(ListCollectionNamesIterable.class);
    final MongoCursor<?> collectionCursor = mock(MongoCursor.class);
    doReturn(true, false).when(collectionCursor).hasNext();
    doReturn(collectionName).when(collectionCursor).next();
    doReturn(collectionCursor).when(collectionNamesIterable).iterator();
    doReturn(collectionNamesIterable).when(mongoDatabase).listCollectionNames();

    final boolean result = _ndsClusterConnectionSvc.hasNamespaces(cluster, auditInfo);
    assertTrue(result);

    ArgumentCaptor<Event> auditEvent = ArgumentCaptor.forClass(Event.class);
    // verify auditing
    verify(_auditSvc, times(1)).saveAuditEvent(auditEvent.capture());
    assertEquals(Type.CLUSTER_CONNECTION_HAS_NAMESPACES, auditEvent.getValue().getEventType());
    assertTrue(auditEvent.getValue().isHidden());

    // with timeout exception
    doThrow(new MongoTimeoutException("timeout"))
        .when(_ndsClusterConnectionSvc)
        .getMongoClient(any(), any(), any());
    try {
      _ndsClusterConnectionSvc.listNamespaces(cluster, auditInfo);
      fail("Expected operation to fail");
    } catch (final SvcException e) {
      assertEquals(CommonErrorCode.TIMEOUT, e.getErrorCode());
    }

    // with socket exception
    doThrow(new MongoSocketException("socket", new ServerAddress()))
        .when(_ndsClusterConnectionSvc)
        .getMongoClient(any(), any(), any());
    try {
      _ndsClusterConnectionSvc.listNamespaces(cluster, auditInfo);
      fail("Expected operation to fail");
    } catch (final SvcException e) {
      assertEquals(OPERATION_ERROR, e.getErrorCode());
    }
  }

  @Test
  public void listCollections() throws SvcException {
    final String databaseName = "databaseName";
    final AuditInfo auditInfo = spy(AuditInfo.class);

    // set up database
    final var database = setUpDatabase(databaseName);

    // set up cluster
    final var cluster = new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    mockListCollectionsCall(database, Stream.of("coll1", "coll2", "coll3"));

    assertEquals(
        List.of(
            new DbUnknown("coll1", "other"),
            new DbUnknown("coll2", "other"),
            new DbUnknown("coll3", "other")),
        _ndsClusterConnectionSvc.listCollections(cluster, databaseName, auditInfo));
  }

  @Mock public ListCollectionsIterable<GetCollectionInfoResponse> mockCollectionIterable;

  private void mockListCollectionsCall(
      MongoDatabase mockDatabase, Stream<String> collectionNameStream) {
    doReturn(mockCollectionIterable)
        .when(mockDatabase)
        .listCollections(GetCollectionInfoResponse.class);

    final var mockedSpliterator =
        collectionNameStream
            .map(name -> new GetCollectionInfoResponse(name, "other", null, null))
            .spliterator();

    doReturn(mockedSpliterator).when(mockCollectionIterable).spliterator();
  }

  @Mock public ListCollectionNamesIterable mockCollectionNamesIterable;

  private void mockListCollectionNamesCall(
      MongoDatabase mockDatabase, Stream<String> collectionNameStream) {
    doReturn(mockCollectionNamesIterable).when(mockDatabase).listCollectionNames();

    // Create a list from the stream
    final var collectionNames = collectionNameStream.toList();
    final MongoCursor<?> mockCursor = mock(MongoCursor.class);
    // Set up the cursor to iterate over the collection names
    final Iterator<String> iterator = collectionNames.iterator();
    doAnswer(invocation -> iterator.hasNext()).when(mockCursor).hasNext();
    doAnswer(invocation -> iterator.next()).when(mockCursor).next();
    doReturn(mockCursor).when(mockCollectionNamesIterable).iterator();
  }

  private void mockListCollectionNamesCall(
      MongoDatabase mockDatabase,
      Stream<String> collectionNameStream,
      ListCollectionNamesIterable mockCollectionNamesIterable) {
    doReturn(mockCollectionNamesIterable).when(mockDatabase).listCollectionNames();

    // Create a list from the stream
    final var collectionNames = collectionNameStream.toList();
    final MongoCursor<?> mockCursor = mock(MongoCursor.class);
    // Set up the cursor to iterate over the collection names
    final Iterator<String> iterator = collectionNames.iterator();
    doAnswer(invocation -> iterator.hasNext()).when(mockCursor).hasNext();
    doAnswer(invocation -> iterator.next()).when(mockCursor).next();
    doReturn(mockCursor).when(mockCollectionNamesIterable).iterator();
  }

  private static void mockListNamespacesCall(final MongoClient client) {
    final MongoIterable<?> namespacesIterable = mock(MongoIterable.class);
    final MongoCursor<?> namespaceCursor = mock(MongoCursor.class);
    doReturn(namespaceCursor).when(namespacesIterable).iterator();
    doReturn(false).when(namespaceCursor).hasNext();
    doReturn(namespacesIterable).when(client).listDatabaseNames();
  }

  private MongoDatabase setUpDatabase(final String databaseName) throws SvcException {
    final MongoClient mongoClient = mock(MongoClient.class);
    final MongoDatabase mongoDatabase = mock(MongoDatabase.class);

    lenient().when(mongoClient.getDatabase(databaseName)).thenReturn(mongoDatabase);
    doReturn(mongoClient).when(_ndsClusterConnectionSvc).getMongoClient(any(), any(), any());
    return mongoDatabase;
  }

  private MongoDatabase setUpDatabase(final String databaseName, final MongoClient mongoClient)
      throws SvcException {
    final MongoDatabase mongoDatabase = mock(MongoDatabase.class);
    lenient().doReturn(mongoDatabase).when(mongoClient).getDatabase(databaseName);
    doReturn(mongoClient).when(_ndsClusterConnectionSvc).getMongoClient(any(), any(), any());
    return mongoDatabase;
  }

  private void testSearchHighlightResults(final List<String> pResults) {
    assertEquals(3, pResults.size());
    pResults.stream()
        .map(Document::parse)
        .forEach(
            r -> {
              assertEquals(5, r.keySet().size());
              assertFalse(r.containsKey("field3"));
              assertFalse(r.containsKey("field4"));
            });
  }

  private List<String> getSearchAggregationResults() {
    final Document flatDocument =
        new Document("_id", new ObjectId())
            .append(SearchWildcardPathQueryRequestView.SCORE_FIELD, 2.0)
            .append(
                SearchWildcardPathQueryRequestView.HIGHLIGHTS_FIELD,
                List.of(new Document("path", "field1"), new Document("path", "field2")))
            .append("field1", "test")
            .append("field2", "test something else")
            .append("field3", "not related")
            .append("field4", new Document("document", "field"));
    final Document dotInHighlightedField =
        new Document("_id", new ObjectId())
            .append(SearchWildcardPathQueryRequestView.SCORE_FIELD, 1.5)
            .append(
                SearchWildcardPathQueryRequestView.HIGHLIGHTS_FIELD,
                List.of(new Document("path", "field1"), new Document("path", "field2.has.dot")))
            .append("field1", "test")
            .append("field2.has.dot", "test something else")
            .append("field3", "not related")
            .append("field4", new Document("document", "field"));
    final Document nestedFieldHighlighted =
        new Document("_id", new ObjectId())
            .append(SearchWildcardPathQueryRequestView.SCORE_FIELD, 1.0)
            .append(
                SearchWildcardPathQueryRequestView.HIGHLIGHTS_FIELD,
                List.of(
                    new Document("path", "field1"),
                    new Document("path", "field2.a"),
                    new Document("path", "field2.b.c")))
            .append("field1", "test")
            .append(
                "field2",
                new Document("a", "test")
                    .append("b", new Document("c", "test"))
                    .append("d", "not related"))
            .append("field3", "not related")
            .append("field4", new Document("document", "field"));

    return List.of(flatDocument, dotInHighlightedField, nestedFieldHighlighted).stream()
        .map(NDSClusterConnectionUtil::convertSearchResultToHighlightedResult)
        .map(doc -> doc.toJson(NDSClusterConnectionUtil.WRITE_EXTENDED))
        .collect(Collectors.toList());
  }

  /**
   * recursively add sub document e.g. level =1, depth = 5 leaf field:
   * level1...level4.[string|int|bool|list]Field
   */
  private void addSubDocumentForLookupFieldNames(
      final Document parent, final int level, final int depth) {
    if (level == depth) {
      return;
    }
    final Document doc = exampleDocumentForLookupFieldNames();
    addSubDocumentForLookupFieldNames(doc, level + 1, depth);
    parent.append(String.format("level%d", level), doc);
  }

  @Test
  public void getMongoClient_flexWithServerlessNetworking_usesLoadBalancedMode() throws Exception {
    // Arrange
    final AuditInfo auditInfo = mock(AuditInfo.class);
    when(auditInfo.getUsername()).thenReturn("testUser");

    final INDSDBRole dbRole = mock(INDSDBRole.class);
    when(dbRole.getRole()).thenReturn("readWrite");
    when(dbRole.getDatabase()).thenReturn("testDb");

    final ClusterDescription cluster = mock(ClusterDescription.class);
    final ObjectId groupId = new ObjectId();

    when(cluster.getGroupId()).thenReturn(groupId);
    when(cluster.getName()).thenReturn("flexCluster");
    when(cluster.isServerlessTenantCluster()).thenReturn(false);
    when(cluster.isFlexWithServerlessNetworking()).thenReturn(true);
    when(cluster.getUniqueId()).thenReturn(new ObjectId());

    final NDSGroup group = mock(NDSGroup.class);
    when(_ndsGroupSvc.find(groupId)).thenReturn(Optional.of(group));
    when(_ndsClusterSvc.getClusterMeshHostAddresses(groupId, "flexCluster"))
        .thenReturn(List.of("flex-mesh1-lb.mongodb.net:30000"));

    // Mock the certificate generation and SSL context creation
    final TLSUtil.PEMKeyFile mockPemKeyFile = mock(TLSUtil.PEMKeyFile.class);
    when(mockPemKeyFile.getCAChain()).thenReturn(List.of());
    when(mockPemKeyFile.getKey()).thenReturn(mock(PrivateKeyInfo.class));
    doReturn(mockPemKeyFile)
        .when(_ndsClusterConnectionSvc)
        .generateX509Cert(any(), any(), any(), any());

    try (MockedStatic<TLSUtil> tlsUtilMock = mockStatic(TLSUtil.class)) {
      final SSLContext mockSslContext = mock(SSLContext.class);
      tlsUtilMock
          .when(() -> TLSUtil.getX509SslContext(any(), any(), any()))
          .thenReturn(mockSslContext);

      // Mock the MongoClient creation to verify load balanced parameters and return appropriate
      // client
      doAnswer(
              invocation -> {
                // Get the cluster parameter to verify load balanced condition
                final ClusterDescription clusterParam =
                    invocation.getArgument(2, ClusterDescription.class);
                final boolean shouldBeLoadBalanced =
                    clusterParam.isServerlessTenantCluster()
                        || clusterParam.isFlexWithServerlessNetworking();

                // Verify that this cluster should indeed be load balanced
                assertTrue(
                    shouldBeLoadBalanced,
                    "Expected cluster to meet load balanced condition (isServerlessTenantCluster ||"
                        + " isFlexWithServerlessNetworking)");

                // Call the real method to get the actual MongoClient
                final MongoClient realClient = (MongoClient) invocation.callRealMethod();

                // Create a spy of the real client and override getIsClusterBehindLoadBalancer to
                // match expected behavior
                final MongoClient spyClient = spy(realClient);

                return spyClient;
              })
          .when(_ndsClusterConnectionSvc)
          .getMongoClient(any(), any(), any());

      // Act
      final MongoClient result =
          _ndsClusterConnectionSvc.getMongoClient(auditInfo, dbRole, cluster);

      // Assert
      assertNotNull(result);
    }
  }

  @Test
  public void getMongoClient_serverlessCluster_usesLoadBalancedMode() throws Exception {
    // Arrange
    final AuditInfo auditInfo = mock(AuditInfo.class);
    when(auditInfo.getUsername()).thenReturn("testUser");

    final INDSDBRole dbRole = mock(INDSDBRole.class);
    when(dbRole.getRole()).thenReturn("readWrite");
    when(dbRole.getDatabase()).thenReturn("testDb");

    final ClusterDescription cluster = mock(ClusterDescription.class);
    final ObjectId groupId = new ObjectId();

    when(cluster.getGroupId()).thenReturn(groupId);
    when(cluster.getName()).thenReturn("serverlessCluster");
    when(cluster.isServerlessTenantCluster()).thenReturn(true);
    when(cluster.isFlexWithServerlessNetworking()).thenReturn(false);
    when(cluster.getUniqueId()).thenReturn(new ObjectId());

    final NDSGroup group = mock(NDSGroup.class);
    when(_ndsGroupSvc.find(groupId)).thenReturn(Optional.of(group));
    when(_ndsClusterSvc.getClusterMeshHostAddresses(groupId, "serverlessCluster"))
        .thenReturn(List.of("serverless-mesh1-lb.mongodb.net:30000"));

    // Mock the certificate generation and SSL context creation
    final TLSUtil.PEMKeyFile mockPemKeyFile = mock(TLSUtil.PEMKeyFile.class);
    when(mockPemKeyFile.getCAChain()).thenReturn(List.of());
    when(mockPemKeyFile.getKey()).thenReturn(mock(PrivateKeyInfo.class));
    doReturn(mockPemKeyFile)
        .when(_ndsClusterConnectionSvc)
        .generateX509Cert(any(), any(), any(), any());

    try (MockedStatic<TLSUtil> tlsUtilMock = mockStatic(TLSUtil.class)) {
      final SSLContext mockSslContext = mock(SSLContext.class);
      tlsUtilMock
          .when(() -> TLSUtil.getX509SslContext(any(), any(), any()))
          .thenReturn(mockSslContext);

      // Mock the MongoClient creation to verify load balanced parameters and return appropriate
      // client
      doAnswer(
              invocation -> {
                // Get the cluster parameter to verify load balanced condition
                final ClusterDescription clusterParam =
                    invocation.getArgument(2, ClusterDescription.class);
                final boolean shouldBeLoadBalanced =
                    clusterParam.isServerlessTenantCluster()
                        || clusterParam.isFlexWithServerlessNetworking();

                // Verify that this cluster should indeed be load balanced
                assertTrue(
                    shouldBeLoadBalanced,
                    "Expected cluster to meet load balanced condition (isServerlessTenantCluster ||"
                        + " isFlexWithServerlessNetworking)");

                // Call the real method to get the actual MongoClient
                final MongoClient realClient = (MongoClient) invocation.callRealMethod();

                // Create a spy of the real client and override getIsClusterBehindLoadBalancer to
                // match expected behavior
                final MongoClient spyClient = spy(realClient);

                return spyClient;
              })
          .when(_ndsClusterConnectionSvc)
          .getMongoClient(any(), any(), any());

      // Act
      final MongoClient result =
          _ndsClusterConnectionSvc.getMongoClient(auditInfo, dbRole, cluster);

      // Assert
      assertNotNull(result);
    }
  }

  @Test
  public void getMongoClient_regularCluster_usesStandardMode() throws Exception {
    // Arrange
    final AuditInfo auditInfo = mock(AuditInfo.class);
    when(auditInfo.getUsername()).thenReturn("testUser");

    final INDSDBRole dbRole = mock(INDSDBRole.class);
    when(dbRole.getRole()).thenReturn("readWrite");
    when(dbRole.getDatabase()).thenReturn("testDb");

    final ClusterDescription cluster = mock(ClusterDescription.class);
    final ObjectId groupId = new ObjectId();

    when(cluster.getGroupId()).thenReturn(groupId);
    when(cluster.getName()).thenReturn("regularCluster");
    when(cluster.isServerlessTenantCluster()).thenReturn(false);
    when(cluster.isFlexWithServerlessNetworking()).thenReturn(false);
    when(cluster.getUniqueId()).thenReturn(new ObjectId());

    final NDSGroup group = mock(NDSGroup.class);
    when(_ndsGroupSvc.find(groupId)).thenReturn(Optional.of(group));
    when(_ndsClusterSvc.getClusterMeshHostAddresses(groupId, "regularCluster"))
        .thenReturn(List.of("mesh1.mongodb.net:30000", "mesh2.mongodb.net:30000"));

    // Mock the certificate generation and SSL context creation
    final TLSUtil.PEMKeyFile mockPemKeyFile = mock(TLSUtil.PEMKeyFile.class);
    when(mockPemKeyFile.getCAChain()).thenReturn(List.of());
    when(mockPemKeyFile.getKey()).thenReturn(mock(PrivateKeyInfo.class));
    doReturn(mockPemKeyFile)
        .when(_ndsClusterConnectionSvc)
        .generateX509Cert(any(), any(), any(), any());

    try (MockedStatic<TLSUtil> tlsUtilMock = mockStatic(TLSUtil.class)) {
      final SSLContext mockSslContext = mock(SSLContext.class);
      tlsUtilMock
          .when(() -> TLSUtil.getX509SslContext(any(), any(), any()))
          .thenReturn(mockSslContext);

      // Mock the MongoClient creation to verify load balanced parameters and return appropriate
      // client
      doAnswer(
              invocation -> {
                // Get the cluster parameter to verify load balanced condition
                final ClusterDescription clusterParam =
                    invocation.getArgument(2, ClusterDescription.class);
                final boolean shouldBeLoadBalanced =
                    clusterParam.isServerlessTenantCluster()
                        || clusterParam.isFlexWithServerlessNetworking();

                // Verify that this cluster should NOT be load balanced
                assertFalse(
                    shouldBeLoadBalanced,
                    "Expected cluster to NOT meet load balanced condition"
                        + " (isServerlessTenantCluster || isFlexWithServerlessNetworking)");

                // Call the real method to get the actual MongoClient
                final MongoClient realClient = (MongoClient) invocation.callRealMethod();

                // Create a spy of the real client and override getIsClusterBehindLoadBalancer to
                // match expected behavior
                final MongoClient spyClient = spy(realClient);

                return spyClient;
              })
          .when(_ndsClusterConnectionSvc)
          .getMongoClient(any(), any(), any());

      // Act
      final MongoClient result =
          _ndsClusterConnectionSvc.getMongoClient(auditInfo, dbRole, cluster);

      // Assert
      assertNotNull(result);
    }
  }

  private Document exampleDocumentForLookupFieldNames() {
    return new Document()
        .append("stringField", "stringValue")
        .append("intField", 2)
        .append("boolField", true)
        .append("listField", List.of("string1", "string2", 3, new Document("key", "value")));
  }
}
