package com.xgen.svc.nds.svc.cps;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.pit._public.model.PitSetting;
import com.xgen.cloud.cps.pit._public.model.PitStorage;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CpsOplogRemovalSvcUnitTests {
  @Mock private AppSettings settings;
  @Spy @InjectMocks private CpsOplogRemovalSvc cpsOplogRemovalSvc;

  @Test
  public void testGetRemoveOplogBeforeDate() {
    assertEquals(
        TimeUtils2.fromISOString("2021-01-06T00:00:00.000Z"),
        cpsOplogRemovalSvc.getRemoveOplogBeforeDate(
            TimeUtils2.fromISOString("2021-03-11T12:00:00.000Z")));
  }

  @Test
  public void testPurgeSlicesInJob_error_handling() {
    final BackupJob backupJob = mock(BackupJob.class);

    final String rsId1 = "rs1";
    final String rsId2 = "rs2";
    when(backupJob.getAllRsIds()).thenReturn(Set.of(rsId1, rsId2));
    when(backupJob.getId()).thenReturn(ObjectId.get());
    when(backupJob.getProjectId()).thenReturn(ObjectId.get());
    when(backupJob.getClusterUniqueId()).thenReturn(ObjectId.get());
    when(backupJob.getPitSettingByRsId(any())).thenReturn(mock(PitSetting.class));
    when(backupJob.getClusterName()).thenReturn("cluster1");

    final Date date = new Date();
    doThrow(mock(RuntimeException.class))
        .when(cpsOplogRemovalSvc)
        .removePurgedOplogSliceMetadataForRs(backupJob, date, rsId1, false);

    cpsOplogRemovalSvc.removePurgedOplogSliceMetadataForJob(backupJob, date, false);
    verify(cpsOplogRemovalSvc, times(1))
        .removePurgedOplogSliceMetadataForRs(backupJob, date, rsId2, false);
  }

  @Test
  public void testBackupJobUsesMetadataStore() {
    final BackupJob backupJob = mock(BackupJob.class);

    final PitStorage storage1 = new PitStorage("metadata1", "blob1", "us-east");
    final PitStorage storage2 = new PitStorage("metadata2", "blob2", "us-west");

    final PitSetting ps1 = mock(PitSetting.class);
    when(ps1.getAllPitStorages()).thenReturn(List.of(storage1, storage2));

    final PitSetting ps2 = mock(PitSetting.class);
    when(ps2.getAllPitStorages()).thenReturn(List.of(storage1));

    final Map<String, PitSetting> pitSettings = Map.of("rs1", ps1, "rs2", ps2);
    when(backupJob.getPitSettings()).thenReturn(pitSettings);

    assertTrue(cpsOplogRemovalSvc.backupJobUsesMetadataStore(backupJob, "metadata1"));
    assertTrue(cpsOplogRemovalSvc.backupJobUsesMetadataStore(backupJob, "metadata2"));
    assertFalse(cpsOplogRemovalSvc.backupJobUsesMetadataStore(backupJob, "metadata3"));
  }

  @Test
  public void testRemovePurgedOplogSliceMetadataForRs() {
    final Date date = new Date();
    final BackupJob activeBackupJob = mock(BackupJob.class);
    final PitStorage storage2 = new PitStorage("metadata2", "blob2", "us-west");
    final PitStorage storage2_copy = new PitStorage("metadata2", "blob3", "us-east");
    final PitSetting ps2 = mock(PitSetting.class);
    when(ps2.getAllPitStorages()).thenReturn(List.of(storage2));

    final BackupJob queuedBackupJob = mock(BackupJob.class);
    final BackupJobDao backupJobDao = mock(BackupJobDao.class);
    when(activeBackupJob.getPitSettingByRsId("rs2")).thenReturn(ps2);
    when(queuedBackupJob.getProjectId()).thenReturn(ObjectId.get());
    when(queuedBackupJob.getClusterUniqueId()).thenReturn(ObjectId.get());
    when(queuedBackupJob.getClusterName()).thenReturn("cluster1");
    doReturn(backupJobDao).when(cpsOplogRemovalSvc).getBackupJobDao();
    when(backupJobDao.findActiveByClusterUniqueId(any(), any()))
        .thenReturn(Optional.of(activeBackupJob));

    assertTrue(
        cpsOplogRemovalSvc.removePurgedOplogSliceMetadataForRs(
            queuedBackupJob, date, "rs2", storage2_copy, true));

    final CpsOplogStoreFactory cpsOplogStoreFactory = mock(CpsOplogStoreFactory.class);
    doReturn(cpsOplogStoreFactory).when(cpsOplogRemovalSvc).getCpsOplogStoreFactory();
    final CpsOplogSliceMetadataDaoProxy sliceDao = mock(CpsOplogSliceMetadataDaoProxy.class);
    when(cpsOplogStoreFactory.getCpsSliceDaoProxy(any(), any(), any())).thenReturn(sliceDao);
    when(sliceDao.removeInvalidPurgedSlicesBeforeDateForAllRegions(any(), any())).thenReturn(1);
    when(sliceDao.isAllRemoved(any(), any())).thenReturn(false);
    assertFalse(
        cpsOplogRemovalSvc.removePurgedOplogSliceMetadataForRs(
            queuedBackupJob, date, "rs2", storage2_copy, false));
    verify(cpsOplogRemovalSvc, times(1)).getCpsOplogStoreFactory();
  }
}
