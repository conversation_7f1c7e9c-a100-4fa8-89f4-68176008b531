package com.xgen.svc.nds.svc.cps;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.cps.billing._private.dao.CpsBillingPropertiesDao;
import com.xgen.cloud.cps.billing._public.model.CpsBillingProperty;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.module.metering.common.exception.MeterSvcException;
import com.xgen.module.metering.common.model.MeterId;
import com.xgen.module.metering.common.model.MeterUsage;
import com.xgen.module.metering.common.model.usagedimensions.AwsBackupDownloadExportUsageDimensions;
import com.xgen.module.metering.common.model.usagedimensions.UsageDimensions;
import com.xgen.svc.mms.model.billing.NDSBackupSnapshotDownloadVMUsage;
import java.sql.Date;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.slf4j.Logger;

@MockitoSettings(strictness = Strictness.STRICT_STUBS)
public class CpsAbstractDownloadExportUsageSubmissionSvcUnitTests {
  @Mock private CpsBillingPropertiesDao cpsBillingPropertiesDao;
  @Mock private Logger logger;
  @Mock private AppSettings appSettings;

  @Mock(answer = Answers.CALLS_REAL_METHODS)
  private CpsDownloadAndExportUsageSubmissionSvc testCpsDownloadAndExportUsageSubmissionSvc;

  private static final String METER_SVC_JOB_NAME = "CpsAwsDownloadUsageSubmission";

  @Test
  public void testSubmitUsage_disabled_throwsException() {
    when(testCpsDownloadAndExportUsageSubmissionSvc.getMeterJobName())
        .thenReturn(METER_SVC_JOB_NAME);
    when(testCpsDownloadAndExportUsageSubmissionSvc.isUsageSubmissionEnabled()).thenReturn(true);
    when(testCpsDownloadAndExportUsageSubmissionSvc.getLogger()).thenReturn(logger);

    when(testCpsDownloadAndExportUsageSubmissionSvc.isUsageSubmissionEnabled()).thenReturn(false);
    testCpsDownloadAndExportUsageSubmissionSvc.submitUsage();

    verify(cpsBillingPropertiesDao, never()).upsertOneMajority(any(String.class), any());
    verify(testCpsDownloadAndExportUsageSubmissionSvc, never()).calculateMeterUsages(any(), any());
    verify(logger).info("Not starting {} job because it is not enabled", METER_SVC_JOB_NAME);
  }

  @Test
  public void testSubmitUsage_LastSuccessNotSet_throwsException() {
    when(testCpsDownloadAndExportUsageSubmissionSvc.getMeterJobName())
        .thenReturn(METER_SVC_JOB_NAME);
    when(testCpsDownloadAndExportUsageSubmissionSvc.getBillingPropSubmissionId())
        .thenReturn(METER_SVC_JOB_NAME);
    when(testCpsDownloadAndExportUsageSubmissionSvc.isUsageSubmissionEnabled()).thenReturn(true);
    when(testCpsDownloadAndExportUsageSubmissionSvc.getLogger()).thenReturn(logger);
    when(testCpsDownloadAndExportUsageSubmissionSvc.getCpsBillingPropertiesDao())
        .thenReturn(cpsBillingPropertiesDao);

    when(cpsBillingPropertiesDao.findOneById(any())).thenReturn(Optional.empty());

    RuntimeException ex =
        assertThrows(
            RuntimeException.class, () -> testCpsDownloadAndExportUsageSubmissionSvc.submitUsage());
    assertEquals(
        "Not running "
            + METER_SVC_JOB_NAME
            + " job because the last successful date for usage submission is not set in the db",
        ex.getMessage());
  }

  @Test
  public void testSubmitUsage() {
    when(testCpsDownloadAndExportUsageSubmissionSvc.getMeterJobName())
        .thenReturn(METER_SVC_JOB_NAME);
    when(testCpsDownloadAndExportUsageSubmissionSvc.getBillingPropSubmissionId())
        .thenReturn(METER_SVC_JOB_NAME);
    when(testCpsDownloadAndExportUsageSubmissionSvc.isUsageSubmissionEnabled()).thenReturn(true);
    when(testCpsDownloadAndExportUsageSubmissionSvc.calculateMeterUsages(any(), any()))
        .thenReturn(List.of());
    when(testCpsDownloadAndExportUsageSubmissionSvc.getLogger()).thenReturn(logger);
    when(testCpsDownloadAndExportUsageSubmissionSvc.getCpsBillingPropertiesDao())
        .thenReturn(cpsBillingPropertiesDao);

    final CpsBillingProperty snapshotUsageBillingProperty = mock(CpsBillingProperty.class);
    when(cpsBillingPropertiesDao.findOneById(any()))
        .thenReturn(Optional.of(snapshotUsageBillingProperty));
    LocalDate submissionLastSuccessfulDate = LocalDate.now().minusDays(3);
    when(snapshotUsageBillingProperty.getLastSuccessfulDate())
        .thenReturn(submissionLastSuccessfulDate);

    testCpsDownloadAndExportUsageSubmissionSvc.submitUsage();

    verify(cpsBillingPropertiesDao)
        .upsertOneMajority(METER_SVC_JOB_NAME, LocalDate.now().minusDays(2));
    verify(cpsBillingPropertiesDao)
        .upsertOneMajority(METER_SVC_JOB_NAME, LocalDate.now().minusDays(1));
    verify(testCpsDownloadAndExportUsageSubmissionSvc, times(2)).calculateMeterUsages(any(), any());
  }

  @Test
  public void testSubmitUsage_MeterSvcException_NoOp() throws MeterSvcException {
    when(testCpsDownloadAndExportUsageSubmissionSvc.getMeterJobName())
        .thenReturn(METER_SVC_JOB_NAME);
    when(testCpsDownloadAndExportUsageSubmissionSvc.getBillingPropSubmissionId())
        .thenReturn(METER_SVC_JOB_NAME);
    when(testCpsDownloadAndExportUsageSubmissionSvc.isUsageSubmissionEnabled()).thenReturn(true);
    when(testCpsDownloadAndExportUsageSubmissionSvc.calculateMeterUsages(any(), any()))
        .thenReturn(List.of());
    when(testCpsDownloadAndExportUsageSubmissionSvc.getLogger()).thenReturn(logger);
    when(testCpsDownloadAndExportUsageSubmissionSvc.getCpsBillingPropertiesDao())
        .thenReturn(cpsBillingPropertiesDao);
    final CpsBillingProperty snapshotUsageBillingProperty = mock(CpsBillingProperty.class);
    when(cpsBillingPropertiesDao.findOneById(any()))
        .thenReturn(Optional.of(snapshotUsageBillingProperty));
    LocalDate submissionLastSuccessfulDate = LocalDate.now().minusDays(3);
    when(snapshotUsageBillingProperty.getLastSuccessfulDate())
        .thenReturn(submissionLastSuccessfulDate);

    when(testCpsDownloadAndExportUsageSubmissionSvc.calculateMeterUsages(any(), any()))
        .thenReturn(List.of(mock(MeterUsage.class)));
    doThrow(MeterSvcException.class)
        .when(testCpsDownloadAndExportUsageSubmissionSvc)
        .submitMeterUsageWithRetry(any());
    testCpsDownloadAndExportUsageSubmissionSvc.submitUsage();

    verify(cpsBillingPropertiesDao, never()).upsertOneMajority(any(String.class), any());
    verify(testCpsDownloadAndExportUsageSubmissionSvc, times(1)).calculateMeterUsages(any(), any());
  }

  @Test
  public void testSubmitUsage_usagesExist() throws MeterSvcException {
    when(testCpsDownloadAndExportUsageSubmissionSvc.getMeterJobName())
        .thenReturn(METER_SVC_JOB_NAME);
    when(testCpsDownloadAndExportUsageSubmissionSvc.getBillingPropSubmissionId())
        .thenReturn(METER_SVC_JOB_NAME);
    when(testCpsDownloadAndExportUsageSubmissionSvc.isUsageSubmissionEnabled()).thenReturn(true);
    when(testCpsDownloadAndExportUsageSubmissionSvc.calculateMeterUsages(any(), any()))
        .thenReturn(List.of());
    when(testCpsDownloadAndExportUsageSubmissionSvc.getLogger()).thenReturn(logger);
    when(testCpsDownloadAndExportUsageSubmissionSvc.getCpsBillingPropertiesDao())
        .thenReturn(cpsBillingPropertiesDao);
    final CpsBillingProperty snapshotUsageBillingProperty = mock(CpsBillingProperty.class);
    when(cpsBillingPropertiesDao.findOneById(any()))
        .thenReturn(Optional.of(snapshotUsageBillingProperty));
    LocalDate submissionLastSuccessfulDate = LocalDate.now().minusDays(3);
    when(snapshotUsageBillingProperty.getLastSuccessfulDate())
        .thenReturn(submissionLastSuccessfulDate);

    doNothing().when(testCpsDownloadAndExportUsageSubmissionSvc).submitMeterUsageWithRetry(any());
    when(testCpsDownloadAndExportUsageSubmissionSvc.calculateMeterUsages(any(), any()))
        .thenReturn(List.of(mock(MeterUsage.class)));
    testCpsDownloadAndExportUsageSubmissionSvc.submitUsage();

    verify(cpsBillingPropertiesDao, times(2)).upsertOneMajority(any(String.class), any());
    verify(testCpsDownloadAndExportUsageSubmissionSvc, times(2)).calculateMeterUsages(any(), any());
  }

  @Test
  public void testFilterZeroQuantityUsages() {
    final NDSBackupSnapshotDownloadVMUsage usage =
        NDSBackupSnapshotDownloadVMUsage.builder()
            .clusterUniqueId(oid(1))
            .clusterName("one")
            .awsRegionName(AWSRegionName.US_EAST_1)
            .diskSizeGB(41)
            .rsId("rsId")
            .restoreJobId(oid(2))
            .build();

    final UsageDimensions usageDimensions =
        new AwsBackupDownloadExportUsageDimensions.Builder()
            .awsRegionName(usage.getAwsRegionName())
            .clusterUniqueId(usage.getClusterUniqueId())
            .clusterName(usage.getClusterName())
            .rsName(usage.getRsId())
            .restoreJobId(usage.getRestoreJobId())
            .build();

    MeterId meterId = MeterId.NDS_AWS_INSTANCE_M30;
    final MeterUsage meterUsage1 =
        new MeterUsage.Builder(appSettings)
            .groupId(oid(1))
            .meterId(meterId)
            .usageDimensions(usageDimensions)
            .unit(meterId.getSkuUnits())
            .quantity(0)
            .startTime(Date.from(Instant.now()))
            .endTime(Date.from(Instant.now()))
            .build();

    final MeterUsage meterUsage2 =
        new MeterUsage.Builder(appSettings)
            .groupId(oid(1))
            .meterId(meterId)
            .usageDimensions(usageDimensions)
            .unit(meterId.getSkuUnits())
            .quantity(10)
            .startTime(Date.from(Instant.now()))
            .endTime(Date.from(Instant.now()))
            .build();

    List<MeterUsage> meterUsages =
        testCpsDownloadAndExportUsageSubmissionSvc.filterZeroQuantityMeterUsage(
            List.of(meterUsage1, meterUsage2));

    assertEquals(1, meterUsages.size());
    assertEquals(10, meterUsages.get(0).getQuantity(), 0);
  }
}
