package com.xgen.svc.nds.svc.cps;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.mongodb.BasicDBObject;
import com.mongodb.BulkWriteError;
import com.mongodb.BulkWriteException;
import com.mongodb.MongoNotPrimaryException;
import com.mongodb.ServerAddress;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.pit._public.model.CpsOplogId;
import com.xgen.cloud.cps.pit._public.model.CpsOplogSlice;
import com.xgen.cloud.cps.pit._public.model.PitSetting;
import com.xgen.cloud.cps.pit._public.model.PitStorage;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.svc.mms.util.UnitTestUtils;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.BsonDocument;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

public class CpsOplogIngestionSvcUnitTests {

  private static final ObjectId PROJECT_ID = ObjectId.get();
  private static final ObjectId CLUSTER_UNIQUE_ID = ObjectId.get();
  private static final String RS_ID = "rs-1";
  private static final String RS_ID_2 = "rs-2";
  private static final String RS_ID_3 = "rs-3";
  private static final BulkWriteError DUPLICATE_KEY_WRITE_ERROR =
      new BulkWriteError(
          DbUtils.DUPLICATE_KEY_ERROR_CODE, "duplicate key exception", new BasicDBObject(), 0);
  private static final BulkWriteError NON_DUPLICATE_KEY_WRITE_ERROR =
      new BulkWriteError(54, "not a duplicate key exception", new BasicDBObject(), 1);
  private static final BulkWriteError NON_DUPLICATE_KEY_WRITE_ERROR_2 =
      new BulkWriteError(123, "not a duplicate key exception", new BasicDBObject(), 0);

  @Test
  public void testIngestOplogMetadataRequestBatch_Success() {
    final BackupJob backupJob = mock(BackupJob.class);
    doReturn(oid(1)).when(backupJob).getId();
    doReturn(PROJECT_ID).when(backupJob).getProjectId();
    doReturn(CLUSTER_UNIQUE_ID).when(backupJob).getClusterUniqueId();

    CpsOplogSlice cpsOplogSliceOne =
        new CpsOplogSlice.Builder()
            .setId(oid(1))
            .setGroupId(PROJECT_ID)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setReplicaSetId(RS_ID)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(1, 1))
            .setEndTimestamp(new BSONTimestamp(2, 1))
            .setLastHash(3L)
            .setLastTerm(1L)
            .setNumDocs(1)
            .setProcessedSize(100)
            .setRawSize(200)
            .setCloudProvider(CloudProvider.AWS)
            .setMongodVersion("4.2.11")
            .setCompressor("snappy")
            .build();
    CpsOplogSlice cpsOplogSliceTwo =
        new CpsOplogSlice.Builder()
            .setId(oid(2))
            .setGroupId(PROJECT_ID)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setReplicaSetId(RS_ID)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(2, 1))
            .setEndTimestamp(new BSONTimestamp(3, 1))
            .setLastHash(3L)
            .setLastTerm(1L)
            .setNumDocs(1)
            .setProcessedSize(100)
            .setRawSize(200)
            .setCloudProvider(CloudProvider.AWS)
            .setMongodVersion("4.2.11")
            .setCompressor("snappy")
            .build();

    final BackupJobDao mockBackupJobDao = mock(BackupJobDao.class);
    doReturn(true)
        .when(mockBackupJobDao)
        .updateOplogStatus(any(ObjectId.class), anyString(), any(CpsOplogId.class));

    final CpsOplogSliceMetadataDaoProxy mockCpsOplogSliceMetadataDao =
        mock(CpsOplogSliceMetadataDaoProxy.class);
    final CpsOplogStoreFactory mockCpsOplogStoreFactory = mock(CpsOplogStoreFactory.class);
    when(mockCpsOplogStoreFactory.getCpsSliceDaoProxy(any(BackupJob.class), any(), any()))
        .thenReturn(mockCpsOplogSliceMetadataDao);

    final CpsOplogIngestionSvc cpsOplogIngestionSvc =
        UnitTestUtils.create(CpsOplogIngestionSvc.class)
            .withArgs(mockBackupJobDao, mockCpsOplogStoreFactory);

    // Make addMetadatas() operation "succeed"
    doNothing().when(mockCpsOplogSliceMetadataDao).addMetadatas(anyCollection());

    cpsOplogIngestionSvc.ingestOplogMetadataRequestBatch(
        backupJob, RS_ID, List.of(cpsOplogSliceOne, cpsOplogSliceTwo), "storeId", true);

    // Verify the last slice is used to set oplog status on job
    ArgumentCaptor<ObjectId> jobIdArgument = ArgumentCaptor.forClass(ObjectId.class);
    ArgumentCaptor<String> rsIdArgument = ArgumentCaptor.forClass(String.class);
    ArgumentCaptor<CpsOplogId> oplogIdArgument = ArgumentCaptor.forClass(CpsOplogId.class);
    verify(mockBackupJobDao)
        .updateOplogStatus(
            jobIdArgument.capture(), rsIdArgument.capture(), oplogIdArgument.capture());

    assertEquals(cpsOplogSliceTwo.getEndOplogId(), oplogIdArgument.getValue());
  }

  @Test
  public void testIngestOplogMetadataRequestBatch_BulkErrorHandling() {
    final BackupJob backupJob = mock(BackupJob.class);
    doReturn(oid(1)).when(backupJob).getId();
    doReturn(PROJECT_ID).when(backupJob).getProjectId();
    doReturn(CLUSTER_UNIQUE_ID).when(backupJob).getClusterUniqueId();

    CpsOplogSlice cpsOplogSliceOne =
        new CpsOplogSlice.Builder()
            .setId(oid(1))
            .setGroupId(PROJECT_ID)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setReplicaSetId(RS_ID)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(1, 1))
            .setEndTimestamp(new BSONTimestamp(2, 1))
            .setLastHash(3L)
            .setLastTerm(1L)
            .setNumDocs(1)
            .setProcessedSize(100)
            .setRawSize(200)
            .setCloudProvider(CloudProvider.AWS)
            .setMongodVersion("4.2.11")
            .setCompressor("snappy")
            .build();
    CpsOplogSlice cpsOplogSliceTwo =
        new CpsOplogSlice.Builder()
            .setId(oid(2))
            .setGroupId(PROJECT_ID)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setReplicaSetId(RS_ID)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(2, 1))
            .setEndTimestamp(new BSONTimestamp(3, 1))
            .setLastHash(3L)
            .setLastTerm(1L)
            .setNumDocs(1)
            .setProcessedSize(100)
            .setRawSize(200)
            .setCloudProvider(CloudProvider.AWS)
            .setMongodVersion("4.2.11")
            .setCompressor("snappy")
            .build();

    final BackupJobDao mockBackupJobDao = mock(BackupJobDao.class);

    when(mockBackupJobDao.updateOplogStatus(
            any(ObjectId.class), anyString(), any(CpsOplogId.class)))
        .thenReturn(true);

    final CpsOplogSliceMetadataDaoProxy mockCpsOplogSliceMetadataDao =
        mock(CpsOplogSliceMetadataDaoProxy.class);
    final CpsOplogStoreFactory mockCpsOplogStoreFactory = mock(CpsOplogStoreFactory.class);
    when(mockCpsOplogStoreFactory.getCpsSliceDaoProxy(any(BackupJob.class), any(), any()))
        .thenReturn(mockCpsOplogSliceMetadataDao);

    final CpsOplogIngestionSvc cpsOplogIngestionSvc =
        UnitTestUtils.create(CpsOplogIngestionSvc.class)
            .withArgs(mockBackupJobDao, mockCpsOplogStoreFactory);

    // Case 1: handling when none of the write errors are DuplicateKeyException, expect rethrown
    final BulkWriteException mockBulkWriteException = mock(BulkWriteException.class);
    when(mockBulkWriteException.getWriteErrors())
        .thenReturn(List.of(NON_DUPLICATE_KEY_WRITE_ERROR, NON_DUPLICATE_KEY_WRITE_ERROR_2));
    doThrow(mockBulkWriteException)
        .when(mockCpsOplogSliceMetadataDao)
        .addMetadatas(anyCollection());

    try {
      cpsOplogIngestionSvc.ingestOplogMetadataRequestBatch(
          backupJob, RS_ID, List.of(cpsOplogSliceOne, cpsOplogSliceTwo), "storeId", true);
      fail("Expected exception");
    } catch (IllegalStateException pE) {
      assertEquals(
          "Bulk insert for oplog metadata encountered a non-DuplicateKey error", pE.getMessage());
    }

    // Case 2: handling a mix of both duplicate key and non-duplicate key errors, except thrown
    when(mockBulkWriteException.getWriteErrors())
        .thenReturn(List.of(DUPLICATE_KEY_WRITE_ERROR, NON_DUPLICATE_KEY_WRITE_ERROR));
    try {
      cpsOplogIngestionSvc.ingestOplogMetadataRequestBatch(
          backupJob, RS_ID, List.of(cpsOplogSliceOne, cpsOplogSliceTwo), "storeId", true);
      fail("Expected exception");
    } catch (IllegalStateException pE) {
      assertEquals(
          "Bulk insert for oplog metadata encountered a non-DuplicateKey error", pE.getMessage());
    }

    // Case 3: If other MongoException thrown besides BulkWriteException, it bubbles
    doThrow(new MongoNotPrimaryException(new BsonDocument(), new ServerAddress()))
        .when(mockCpsOplogSliceMetadataDao)
        .addMetadatas(anyCollection());

    try {
      cpsOplogIngestionSvc.ingestOplogMetadataRequestBatch(
          backupJob, RS_ID, List.of(cpsOplogSliceOne, cpsOplogSliceTwo), "storeId", true);
      fail("Expected exception");
    } catch (MongoNotPrimaryException pE) {
      assertTrue(
          pE.getMessage().startsWith("Command failed with error"),
          "Unexpected exception message: " + pE.getMessage());
    }

    // Case 4: If all duplicate key errors, we continue on and update oplog status with last slice
    when(mockBulkWriteException.getWriteErrors())
        .thenReturn(List.of(DUPLICATE_KEY_WRITE_ERROR, DUPLICATE_KEY_WRITE_ERROR));
    doThrow(mockBulkWriteException)
        .when(mockCpsOplogSliceMetadataDao)
        .addMetadatas(anyCollection());

    cpsOplogIngestionSvc.ingestOplogMetadataRequestBatch(
        backupJob, RS_ID, List.of(cpsOplogSliceOne, cpsOplogSliceTwo), "storeId", true);

    ArgumentCaptor<ObjectId> jobIdArgument = ArgumentCaptor.forClass(ObjectId.class);
    ArgumentCaptor<String> rsIdArgument = ArgumentCaptor.forClass(String.class);
    ArgumentCaptor<CpsOplogId> oplogIdArgument = ArgumentCaptor.forClass(CpsOplogId.class);
    verify(mockBackupJobDao)
        .updateOplogStatus(
            jobIdArgument.capture(), rsIdArgument.capture(), oplogIdArgument.capture());

    assertEquals(cpsOplogSliceTwo.getEndOplogId(), oplogIdArgument.getValue());
  }

  @Test
  public void testGetOplogSlices() throws Exception {
    final BackupJob backupJob = mock(BackupJob.class);
    doReturn(oid(1)).when(backupJob).getProjectId();
    doReturn(PROJECT_ID).when(backupJob).getProjectId();
    doReturn(Set.of(RS_ID)).when(backupJob).getAllRsIds();
    doReturn(CLUSTER_UNIQUE_ID).when(backupJob).getClusterUniqueId();

    CpsOplogSlice slice1 =
        new CpsOplogSlice.Builder()
            .setId(oid(1))
            .setGroupId(PROJECT_ID)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setReplicaSetId(RS_ID)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(99, 1))
            .setEndTimestamp(new BSONTimestamp(102, 1))
            .setLastHash(3L)
            .setLastTerm(1L)
            .setNumDocs(1)
            .setProcessedSize(100)
            .setRawSize(200)
            .setCloudProvider(CloudProvider.AWS)
            .setMongodVersion("4.2.11")
            .setCompressor("snappy")
            .build();
    CpsOplogSlice slice2 =
        new CpsOplogSlice.Builder()
            .setId(oid(2))
            .setGroupId(PROJECT_ID)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setReplicaSetId(RS_ID)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(102, 1))
            .setEndTimestamp(new BSONTimestamp(200, 2))
            .setLastHash(3L)
            .setLastTerm(1L)
            .setNumDocs(1)
            .setProcessedSize(100)
            .setRawSize(200)
            .setCloudProvider(CloudProvider.AWS)
            .setMongodVersion("4.2.11")
            .setCompressor("snappy")
            .build();
    CpsOplogSlice slice3 =
        new CpsOplogSlice.Builder()
            .setId(oid(3))
            .setGroupId(PROJECT_ID)
            .setClusterUniqueId(CLUSTER_UNIQUE_ID)
            .setReplicaSetId(RS_ID)
            .setRegionName(AWSRegionName.US_EAST_1)
            .setStartTimestamp(new BSONTimestamp(200, 2))
            .setEndTimestamp(new BSONTimestamp(203, 2))
            .setLastHash(3L)
            .setLastTerm(1L)
            .setNumDocs(1)
            .setProcessedSize(100)
            .setRawSize(200)
            .setCloudProvider(CloudProvider.AWS)
            .setMongodVersion("4.2.11")
            .setCompressor("snappy")
            .build();

    final List<BSONTimestamp> snapshotPitSentinelOptimes =
        List.of(new BSONTimestamp(99, 1), new BSONTimestamp(102, 1), new BSONTimestamp(200, 2));
    final List<BackupSnapshot> snapshots = new LinkedList<>();

    snapshotPitSentinelOptimes.forEach(
        (optime -> {
          final BackupSnapshot snapshot = mock(BackupSnapshot.class);
          when(snapshot.getPitSentinelOptime()).thenReturn(optime);
          snapshots.add(snapshot);
        }));

    final BackupSnapshotDao backupSnapshotDao = mock(BackupSnapshotDao.class);
    when(backupSnapshotDao.findCompletedByCluster(any(), any(), anyBoolean()))
        .thenReturn(snapshots);

    final BackupJobDao mockBackupJobDao = mock(BackupJobDao.class);
    doReturn(true)
        .when(mockBackupJobDao)
        .updateOplogStatus(any(ObjectId.class), anyString(), any(CpsOplogId.class));

    final CpsOplogSliceMetadataDaoProxy mockCpsOplogSliceMetadataDao =
        mock(CpsOplogSliceMetadataDaoProxy.class);
    final CpsOplogStoreFactory mockCpsOplogStoreFactory = mock(CpsOplogStoreFactory.class);
    when(mockCpsOplogStoreFactory.getCpsSliceDaoProxy(any(BackupJob.class), any(), any()))
        .thenReturn(mockCpsOplogSliceMetadataDao);

    final CpsOplogIngestionSvc cpsOplogIngestionSvc =
        UnitTestUtils.create(CpsOplogIngestionSvc.class)
            .withArgs(mockBackupJobDao, backupSnapshotDao, mockCpsOplogStoreFactory);

    doReturn(AWSRegionName.US_EAST_1.name()).when(backupJob).getBlobStoreRegionByRsId(anyString());

    BSONTimestamp reqStartTimestamp = new BSONTimestamp(100, 0);
    BSONTimestamp reqEndTimestamp = new BSONTimestamp(200, 1);

    doReturn(List.of(slice1, slice2))
        .when(mockCpsOplogSliceMetadataDao)
        .getSlices(anyString(), anyString(), any(), any(), anyBoolean());

    // happy path
    List<CpsOplogSlice> slices =
        cpsOplogIngestionSvc.getOplogSlices(backupJob, RS_ID, reqStartTimestamp, reqEndTimestamp);

    assertEquals(2, slices.size());
    assertEquals(oid(1), slices.get(0).getId());
    assertEquals(oid(2), slices.get(1).getId());

    reqStartTimestamp = new BSONTimestamp(98, 1);
    reqEndTimestamp = new BSONTimestamp(200, 1);

    // the start time is before the first slice, so we expect error
    try {
      cpsOplogIngestionSvc.getOplogSlices(backupJob, RS_ID, reqStartTimestamp, reqEndTimestamp);
      fail("Expected exception");
    } catch (final SvcException e) {
      assertTrue(
          e.getMessage().contains("Attempting to fetch oplogs from"),
          "Unexpected exception message: " + e.getMessage());
    }

    reqStartTimestamp = new BSONTimestamp(100, 1);
    reqEndTimestamp = new BSONTimestamp(200, 3);

    // the end time is after the last slice, so we expect error
    try {
      cpsOplogIngestionSvc.getOplogSlices(backupJob, RS_ID, reqStartTimestamp, reqEndTimestamp);
      fail("Expected exception");
    } catch (final SvcException e) {
      assertTrue(
          e.getMessage().contains("Attempting to fetch oplogs from"),
          "Unexpected exception message: " + e.getMessage());
    }

    reqStartTimestamp = new BSONTimestamp(100, 0);
    reqEndTimestamp = new BSONTimestamp(200, 1);

    doReturn(List.of(slice1, slice3))
        .when(mockCpsOplogSliceMetadataDao)
        .getSlices(eq(RS_ID), anyString(), any(), any(), anyBoolean());

    // the slices are not contiguous (mocked above), so we expect error
    // the end time is after the last slice, so we expect error
    try {
      cpsOplogIngestionSvc.getOplogSlices(backupJob, RS_ID, reqStartTimestamp, reqEndTimestamp);
      fail("Expected exception");
    } catch (final SvcException e) {
      assertTrue(
          e.getMessage().contains("are not contiguous"),
          "Unexpected exception message: " + e.getMessage());
    }
  }

  @Test
  public void testIntersectIntervals1() {
    final List<Pair<BSONTimestamp, BSONTimestamp>> intervals1 =
        List.of(
            Pair.of(new BSONTimestamp(1, 1), new BSONTimestamp(5, 1)),
            Pair.of(new BSONTimestamp(7, 1), new BSONTimestamp(10, 1)));
    final List<Pair<BSONTimestamp, BSONTimestamp>> intervals2 =
        List.of(
            Pair.of(new BSONTimestamp(3, 1), new BSONTimestamp(5, 1)),
            Pair.of(new BSONTimestamp(6, 1), new BSONTimestamp(10, 1)));

    final List<Pair<BSONTimestamp, BSONTimestamp>> setIntersect1_2 =
        CpsOplogIngestionSvc.intersectIntervals(intervals1, intervals2);

    assertEquals(
        List.of(
            Pair.of(new BSONTimestamp(3, 1), new BSONTimestamp(5, 1)),
            Pair.of(new BSONTimestamp(7, 1), new BSONTimestamp(10, 1))),
        setIntersect1_2);

    final List<Pair<BSONTimestamp, BSONTimestamp>> intervals3 =
        List.of(Pair.of(new BSONTimestamp(6, 1), new BSONTimestamp(7, 1)));

    final List<Pair<BSONTimestamp, BSONTimestamp>> setIntersect1_2_3 =
        CpsOplogIngestionSvc.intersectIntervals(setIntersect1_2, intervals3);

    assertEquals(Collections.emptyList(), setIntersect1_2_3);
  }

  /** Interval 2 has a range that overlaps two ranges from interval 1 */
  @Test
  public void testIntersectIntervals2() {
    final List<Pair<BSONTimestamp, BSONTimestamp>> intervals1 =
        List.of(
            Pair.of(new BSONTimestamp(1, 1), new BSONTimestamp(5, 1)),
            Pair.of(new BSONTimestamp(10, 1), new BSONTimestamp(15, 1)));
    final List<Pair<BSONTimestamp, BSONTimestamp>> intervals2 =
        List.of(Pair.of(new BSONTimestamp(3, 1), new BSONTimestamp(12, 3)));

    final List<Pair<BSONTimestamp, BSONTimestamp>> setIntersect1_2 =
        CpsOplogIngestionSvc.intersectIntervals(intervals1, intervals2);

    assertEquals(
        List.of(
            Pair.of(new BSONTimestamp(3, 1), new BSONTimestamp(5, 1)),
            Pair.of(new BSONTimestamp(10, 1), new BSONTimestamp(12, 3))),
        setIntersect1_2);

    final List<Pair<BSONTimestamp, BSONTimestamp>> intervals3 =
        List.of(
            Pair.of(new BSONTimestamp(4, 1), new BSONTimestamp(7, 1)),
            Pair.of(new BSONTimestamp(9, 1), new BSONTimestamp(11, 10)));

    final List<Pair<BSONTimestamp, BSONTimestamp>> setIntersect1_2_3 =
        CpsOplogIngestionSvc.intersectIntervals(setIntersect1_2, intervals3);

    assertEquals(
        List.of(
            Pair.of(new BSONTimestamp(4, 1), new BSONTimestamp(5, 1)),
            Pair.of(new BSONTimestamp(10, 1), new BSONTimestamp(11, 10))),
        setIntersect1_2_3);
  }

  @Test
  public void testGetValidOplogRangesReplicaSet() {
    final BackupJob backupJob = mock(BackupJob.class);
    doReturn(true).when(backupJob).isPitEnabled();
    doReturn(oid(1)).when(backupJob).getId();
    doReturn(PROJECT_ID).when(backupJob).getProjectId();
    doReturn(CLUSTER_UNIQUE_ID).when(backupJob).getClusterUniqueId();
    doReturn(Set.of(RS_ID)).when(backupJob).getAllRsIds();
    doReturn(AWSRegionName.US_EAST_1.name()).when(backupJob).getBlobStoreRegionByRsId(anyString());

    final CpsOplogSliceMetadataDaoProxy mockCpsOplogSliceMetadataDao =
        mock(CpsOplogSliceMetadataDaoProxy.class);

    final CpsOplogStoreFactory mockCpsOplogStoreFactory = mock(CpsOplogStoreFactory.class);
    when(mockCpsOplogStoreFactory.getCpsSliceDaoProxy(any(BackupJob.class), any(), any()))
        .thenReturn(mockCpsOplogSliceMetadataDao);

    final CpsOplogIngestionSvc cpsOplogIngestionSvc =
        UnitTestUtils.create(CpsOplogIngestionSvc.class).withArgs(mockCpsOplogStoreFactory);

    CpsOplogSlice slice1 =
        buildSliceWithRange(oid(1), new BSONTimestamp(99, 1), new BSONTimestamp(102, 1));
    CpsOplogSlice slice2 =
        buildSliceWithRange(oid(2), new BSONTimestamp(102, 1), new BSONTimestamp(200, 2));
    CpsOplogSlice slice3 =
        buildSliceWithRange(oid(3), new BSONTimestamp(200, 2), new BSONTimestamp(400, 1));

    doReturn(List.of(slice1, slice2))
        .when(mockCpsOplogSliceMetadataDao)
        .getAllValidSliceRanges(eq(RS_ID), anyString());

    assertEquals(
        List.of(Pair.of(new BSONTimestamp(99, 1), new BSONTimestamp(200, 2))),
        cpsOplogIngestionSvc.getValidOplogRanges(backupJob));

    doReturn(List.of(slice1, slice3))
        .when(mockCpsOplogSliceMetadataDao)
        .getAllValidSliceRanges(eq(RS_ID), anyString());

    assertEquals(
        List.of(
            Pair.of(new BSONTimestamp(99, 1), new BSONTimestamp(102, 1)),
            Pair.of(new BSONTimestamp(200, 2), new BSONTimestamp(400, 1))),
        cpsOplogIngestionSvc.getValidOplogRanges(backupJob));
  }

  @Test
  public void testGetServerlessOplogRanges() {
    final BackupJob backupJob = mock(BackupJob.class);
    final PitSetting pitSetting = mock(PitSetting.class);
    doReturn(oid(1)).when(backupJob).getId();
    doReturn(PROJECT_ID).when(backupJob).getProjectId();
    doReturn(CLUSTER_UNIQUE_ID).when(backupJob).getClusterUniqueId();
    doReturn(Set.of(RS_ID)).when(backupJob).getAllRsIds();
    doReturn(pitSetting).when(backupJob).getPitSettingByRsId(RS_ID);
    doReturn(AWSRegionName.US_EAST_1.name()).when(backupJob).getBlobStoreRegionByRsId(anyString());
    doReturn(new PitStorage("", "", "")).when(pitSetting).getPrimaryStorage();

    final CpsOplogSliceMetadataDaoProxy mockCpsOplogSliceMetadataDao =
        mock(CpsOplogSliceMetadataDaoProxy.class);

    final CpsOplogStoreFactory mockCpsOplogStoreFactory = mock(CpsOplogStoreFactory.class);
    when(mockCpsOplogStoreFactory.getCpsSliceDaoProxy(any(BackupJob.class), any(), anyString()))
        .thenReturn(mockCpsOplogSliceMetadataDao);

    final CpsOplogIngestionSvc cpsOplogIngestionSvc =
        UnitTestUtils.create(CpsOplogIngestionSvc.class).withArgs(mockCpsOplogStoreFactory);

    CpsOplogSlice slice1 =
        buildSliceWithRange(oid(1), new BSONTimestamp(99, 1), new BSONTimestamp(102, 1));
    CpsOplogSlice slice2 =
        buildSliceWithRange(oid(2), new BSONTimestamp(102, 1), new BSONTimestamp(200, 2));
    CpsOplogSlice slice3 =
        buildSliceWithRange(oid(3), new BSONTimestamp(200, 2), new BSONTimestamp(400, 1));
    CpsOplogSlice slice4 =
        buildSliceWithRange(oid(3), new BSONTimestamp(450, 1), new BSONTimestamp(500, 1));

    doReturn(List.of(slice1, slice2))
        .when(mockCpsOplogSliceMetadataDao)
        .getSlices(eq(RS_ID), anyString(), any(), any(), anyBoolean());

    assertEquals(
        List.of(Pair.of(new BSONTimestamp(99, 1), new BSONTimestamp(200, 2))),
        cpsOplogIngestionSvc.getServerlessOplogRanges(
            backupJob, new BSONTimestamp(), new BSONTimestamp()));

    doReturn(List.of(slice1, slice3))
        .when(mockCpsOplogSliceMetadataDao)
        .getSlices(eq(RS_ID), anyString(), any(), any(), anyBoolean());

    assertEquals(
        List.of(
            Pair.of(new BSONTimestamp(99, 1), new BSONTimestamp(102, 1)),
            Pair.of(new BSONTimestamp(200, 2), new BSONTimestamp(400, 1))),
        cpsOplogIngestionSvc.getServerlessOplogRanges(
            backupJob, new BSONTimestamp(), new BSONTimestamp()));

    // when slices are not contiguous
    doReturn(List.of(slice3, slice4))
        .when(mockCpsOplogSliceMetadataDao)
        .getSlices(eq(RS_ID), anyString(), any(), any(), anyBoolean());

    assertEquals(
        List.of(
            Pair.of(new BSONTimestamp(200, 2), new BSONTimestamp(400, 1)),
            Pair.of(new BSONTimestamp(450, 1), new BSONTimestamp(500, 1))),
        cpsOplogIngestionSvc.getServerlessOplogRanges(
            backupJob, new BSONTimestamp(), new BSONTimestamp()));
  }

  private CpsOplogSlice buildSliceWithRange(
      final ObjectId id, final BSONTimestamp start, final BSONTimestamp end) {
    return new CpsOplogSlice.Builder()
        .setId(id)
        .setGroupId(PROJECT_ID)
        .setCloudProvider(CloudProvider.AWS)
        .setStartTimestamp(start)
        .setEndTimestamp(end)
        .setLastHash(3L)
        .setLastTerm(1L)
        .build();
  }

  /*-

  The Ranges for all 3 rsIds look something like this:

  -----     -----   -
     ---------  --- ---
     ---    --  -

   */
  @Test
  public void testGetValidOplogRangesShardedCluster() {
    final BackupJob backupJob = mock(BackupJob.class);
    doReturn(true).when(backupJob).isPitEnabled();
    doReturn(oid(1)).when(backupJob).getId();
    doReturn(PROJECT_ID).when(backupJob).getProjectId();
    doReturn(CLUSTER_UNIQUE_ID).when(backupJob).getClusterUniqueId();
    doReturn(Set.of(RS_ID, RS_ID_2, RS_ID_3)).when(backupJob).getAllRsIds();
    doReturn(AWSRegionName.US_EAST_1.name()).when(backupJob).getBlobStoreRegionByRsId(anyString());

    final CpsOplogSliceMetadataDaoProxy mockCpsOplogSliceMetadataDao =
        mock(CpsOplogSliceMetadataDaoProxy.class);
    final CpsOplogStoreFactory mockCpsOplogStoreFactory = mock(CpsOplogStoreFactory.class);
    when(mockCpsOplogStoreFactory.getCpsSliceDaoProxy(any(BackupJob.class), any(), any()))
        .thenReturn(mockCpsOplogSliceMetadataDao);

    final CpsOplogIngestionSvc cpsOplogIngestionSvc =
        UnitTestUtils.create(CpsOplogIngestionSvc.class).withArgs(mockCpsOplogStoreFactory);

    CpsOplogSlice slice1 =
        buildSliceWithRange(oid(1), new BSONTimestamp(1, 1), new BSONTimestamp(5, 1));
    CpsOplogSlice slice2 =
        buildSliceWithRange(oid(2), new BSONTimestamp(11, 2), new BSONTimestamp(16, 2));
    CpsOplogSlice slice3 =
        buildSliceWithRange(oid(3), new BSONTimestamp(19, 1), new BSONTimestamp(20, 1));
    CpsOplogSlice slice4 =
        buildSliceWithRange(oid(4), new BSONTimestamp(4, 2), new BSONTimestamp(12, 1));
    CpsOplogSlice slice5 =
        buildSliceWithRange(oid(5), new BSONTimestamp(15, 2), new BSONTimestamp(18, 3));
    CpsOplogSlice slice6 =
        buildSliceWithRange(oid(6), new BSONTimestamp(19, 1), new BSONTimestamp(21, 1));
    CpsOplogSlice slice7 =
        buildSliceWithRange(oid(7), new BSONTimestamp(4, 2), new BSONTimestamp(6, 1));
    CpsOplogSlice slice8 =
        buildSliceWithRange(oid(8), new BSONTimestamp(11, 2), new BSONTimestamp(12, 1));
    CpsOplogSlice slice9 =
        buildSliceWithRange(oid(9), new BSONTimestamp(15, 2), new BSONTimestamp(17, 1));
    CpsOplogSlice sliceNoOverlap =
        buildSliceWithRange(oid(10), new BSONTimestamp(100, 1), new BSONTimestamp(200, 1));

    doReturn(List.of(slice1, slice2, slice3))
        .when(mockCpsOplogSliceMetadataDao)
        .getAllValidSliceRanges(eq(RS_ID), anyString());

    doReturn(List.of(slice4, slice5, slice6))
        .when(mockCpsOplogSliceMetadataDao)
        .getAllValidSliceRanges(eq(RS_ID_2), anyString());

    doReturn(List.of(slice7, slice8, slice9))
        .when(mockCpsOplogSliceMetadataDao)
        .getAllValidSliceRanges(eq(RS_ID_3), anyString());

    assertEquals(
        List.of(
            Pair.of(new BSONTimestamp(4, 2), new BSONTimestamp(5, 1)),
            Pair.of(new BSONTimestamp(11, 2), new BSONTimestamp(12, 1)),
            Pair.of(new BSONTimestamp(15, 2), new BSONTimestamp(16, 2))),
        cpsOplogIngestionSvc.getValidOplogRanges(backupJob));

    doReturn(List.of(sliceNoOverlap))
        .when(mockCpsOplogSliceMetadataDao)
        .getAllValidSliceRanges(eq(RS_ID_3), anyString());

    assertEquals(Collections.emptyList(), cpsOplogIngestionSvc.getValidOplogRanges(backupJob));
  }

  @Test
  public void testGetPitRestoreableOplogRangesShardedCluster() {
    final BackupSnapshotDao backupSnapshotDao = mock(BackupSnapshotDao.class);
    final CpsOplogIngestionSvc cpsOplogIngestionSvc =
        spy(UnitTestUtils.create(CpsOplogIngestionSvc.class).withArgs(backupSnapshotDao));
    final List<BSONTimestamp> snapshotPitSentinelOptimes =
        Arrays.asList(
            null, new BSONTimestamp(1, 1), new BSONTimestamp(5, 1), new BSONTimestamp(9, 4));
    final List<BackupSnapshot> snapshots = new LinkedList<>();

    snapshotPitSentinelOptimes.forEach(
        (optime -> {
          final BackupSnapshot snapshot = mock(BackupSnapshot.class);
          when(snapshot.getPitSentinelOptime()).thenReturn(optime);
          snapshots.add(snapshot);
        }));

    final BackupJob backupJob = mock(BackupJob.class);
    when(backupJob.getProjectId()).thenReturn(oid(200));
    when(backupJob.getClusterUniqueId()).thenReturn(oid(400));

    when(backupSnapshotDao.findCompletedByCluster(any(), any(), anyBoolean()))
        .thenReturn(snapshots);
    doReturn(
            List.of(
                Pair.of(new BSONTimestamp(2, 1), new BSONTimestamp(5, 1)),
                Pair.of(new BSONTimestamp(8, 3), new BSONTimestamp(15, 1))))
        .when(cpsOplogIngestionSvc)
        .getValidOplogRanges(backupJob);
    assertEquals(
        List.of(
            Pair.of(new BSONTimestamp(5, 1), new BSONTimestamp(5, 1)),
            Pair.of(new BSONTimestamp(9, 4), new BSONTimestamp(15, 1))),
        cpsOplogIngestionSvc.getPitRestoreableOplogRanges(backupJob));
  }
}
