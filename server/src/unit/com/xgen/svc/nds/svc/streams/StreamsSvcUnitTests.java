package com.xgen.svc.nds.svc.streams;

import static com.xgen.cloud.deployment._public.model.diff.ItemType.NDS_STREAM_PROCESSING_INSTANCE_CLOUD_PROVIDER_CONFIG;
import static com.xgen.cloud.deployment._public.model.diff.ItemType.NDS_STREAM_PROCESSING_INSTANCE_DATA_PROCESS_REGION;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_AUTHORIZED;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.DATA_FEDERATION_SUPPORTED_REGIONS_NOT_LOADED;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.DATA_LAKE_STORAGE_CONFIG_OUTDATED;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.INVALID_ARGUMENT;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.STREAM_AWSLAMBDA_NOT_SUPPORTED_IN_CLOUD_PROVIDER;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.STREAM_CONNECTION_NOT_FOUND;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.STREAM_NOT_AWS_CONNECTION;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.STREAM_PRIVATE_NETWORK_PROVIDER_MISMATCH;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.STREAM_SUPPORTED_REGIONS_NOT_LOADED;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.STREAM_TENANT_CONCURRENCY_ERROR;
import static com.xgen.cloud.nds.common._public.model.error.NDSErrorCode.STREAM_VPC_PROXY_DEPLOYMENT_DESCRIPTION_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.services.ec2.model.Route;
import com.amazonaws.services.ec2.model.RouteTable;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.Bucket;
import com.amazonaws.services.s3.model.ListBucketsPaginatedRequest;
import com.amazonaws.services.s3.model.ListBucketsPaginatedResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.access.rolecheck._public.svc.RoleSetSvc;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.common.appsettings._public.model.AppEnv;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.authn._public.svc.AuthnOAuthClient;
import com.xgen.cloud.common.constants._public.model.user.UserType;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.customermetrics._public.svc.CustomerMetricsQuerySvc;
import com.xgen.cloud.customermetrics._public.view.PromQLResponseView;
import com.xgen.cloud.customermetrics._public.view.PromQLResponseView.Data;
import com.xgen.cloud.deployment._public.model.diff.DeploymentItemParameterDiff;
import com.xgen.cloud.deployment._public.model.diff.ItemDiff;
import com.xgen.cloud.deployment._public.model.diff.ItemDiffs;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.nds.aws._private.dao.AWSAccountDao;
import com.xgen.cloud.nds.aws._private.model.registry.AWSProvider;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.NDSAWSTempCredentials;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.azure._private.dao.AzureSubscriptionDao;
import com.xgen.cloud.nds.azure._private.model.registry.AzureProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionNameHelper;
import com.xgen.cloud.nds.cloudprovider._public.model.registry.CloudProviderRegistry;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeAWSCloudProviderConfig;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeCloudProviderConfig;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeState;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeStoreProvider.ProviderValues;
import com.xgen.cloud.nds.datalake._public.model.NDSDataLakeTenant;
import com.xgen.cloud.nds.datalake._public.model.ui.NDSDataLakeDataProcessRegionView;
import com.xgen.cloud.nds.gcp._private.model.registry.GCPProvider;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccessAWSIAMRole;
import com.xgen.cloud.nds.streams._private.dao.AWSLambdaConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsAWSKinesisDataStreamsConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsClusterConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsHttpsConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsKafkaConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsPrivateLinkDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsS3ConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsSampleConnectionDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsTenantDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsTransitGatewayAttachmentsDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsTransitGatewayResourceSharesDao;
import com.xgen.cloud.nds.streams._private.dao.StreamsTransitGatewayRouteDao;
import com.xgen.cloud.nds.streams._public.model.AWSConnectionConfig;
import com.xgen.cloud.nds.streams._public.model.AWSKinesisDataStreamsConnection;
import com.xgen.cloud.nds.streams._public.model.AWSLambdaConnection;
import com.xgen.cloud.nds.streams._public.model.AWSS3ConnectionConfig;
import com.xgen.cloud.nds.streams._public.model.ClusterConnection;
import com.xgen.cloud.nds.streams._public.model.DBRoleToExecute;
import com.xgen.cloud.nds.streams._public.model.DBRoleType;
import com.xgen.cloud.nds.streams._public.model.HttpsConnection;
import com.xgen.cloud.nds.streams._public.model.KafkaConnection;
import com.xgen.cloud.nds.streams._public.model.ProxyInfo;
import com.xgen.cloud.nds.streams._public.model.S3Connection;
import com.xgen.cloud.nds.streams._public.model.SampleConnection;
import com.xgen.cloud.nds.streams._public.model.StreamConfig;
import com.xgen.cloud.nds.streams._public.model.StreamsConnection;
import com.xgen.cloud.nds.streams._public.model.StreamsConnectionType;
import com.xgen.cloud.nds.streams._public.model.StreamsPrivateLink;
import com.xgen.cloud.nds.streams._public.model.StreamsPrivateLinkProviderType;
import com.xgen.cloud.nds.streams._public.model.StreamsPrivateLinkState;
import com.xgen.cloud.nds.streams._public.model.StreamsPrivateLinkVendorType;
import com.xgen.cloud.nds.streams._public.model.StreamsTenant;
import com.xgen.cloud.nds.streams._public.model.StreamsTransitGateway;
import com.xgen.cloud.nds.streams._public.model.Tier;
import com.xgen.cloud.nds.streams._public.model.ui.AWSLambdaConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamConfigView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsAWSKinesisDataStreamsConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsS3ConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsTenantView;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.StreamsKafkaConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.authentication.KafkaAuthUsernamePassword;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.authentication.NDSDataLakeKafkaSASLPlain;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.Access;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.Access.AccessBuilder;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.NDSDataLakeStreamsNetworking;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.Type;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.security.KafkaSSLSecret;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.security.NDSDataLakeKafkaSecurityPlain;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.security.NDSDataLakeKafkaSecuritySSL;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.payments.standalone.common._public.gateway.PaymentMethodGateway;
import com.xgen.cloud.streams._public.model.VPCPeeringConnection;
import com.xgen.cloud.streams._public.model.view.ApiStreamsPrivateLinkView;
import com.xgen.cloud.streams._public.svc.IVPCProxyDeploymentDescriptionSvc;
import com.xgen.cloud.streams._public.svc.VPCPeeringConnectionSvc;
import com.xgen.cloud.streams._public.svc.VPCProxyInstanceDescriptionSvc;
import com.xgen.cloud.streams._public.util.StreamsConstants;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.mhouse.services.streamprocessormanager.v1.Models;
import com.xgen.svc.nds.factory.VPCProxyFactory;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.model.ui.RegionView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessAWSIAMRoleView;
import com.xgen.svc.nds.model.ui.cloudProviderAccess.NDSCloudProviderAccessView;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View;
import com.xgen.svc.nds.model.ui.dataLake.NDSDataLakeStorageV1View.FieldDefs;
import com.xgen.svc.nds.svc.NDSCloudProviderContainerSvc;
import com.xgen.svc.nds.svc.NDSDataLakePublicSvc;
import com.xgen.svc.nds.svc.NDSDataLakeTenantSvc;
import com.xgen.svc.nds.svc.StreamsProcessManagerSvc;
import com.xgen.svc.nds.svc.StreamsSvc;
import com.xgen.svc.nds.svc.adl.DataLakeAdminApiException;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.svc.nds.svc.project.NDSUISvc;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Stream;
import org.apache.commons.lang.NotImplementedException;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@MockitoSettings(strictness = Strictness.LENIENT)
public class StreamsSvcUnitTests {

  private final AppEnv _defaultAppEnv = AppEnv.LOCAL;
  private final StreamConfig _defaultStreamConfig = new StreamConfig(Tier.SP30);
  private final ObjectMapper _objectMapper = new ObjectMapper();
  @Mock private NDSDataLakeTenantSvc _dataLakeTenantSvc;
  @Mock private NDSGroupSvc _ndsGroupSvc;
  @Mock private GroupSvc _groupSvc;
  @Mock private RoleSetSvc _roleSetSvc;
  @Mock private NDSDataLakePublicSvc _dataLakePublicSvc;
  @Mock private StreamsKafkaConnectionDao _streamsKafkaConnectionDao;
  @Mock private StreamsClusterConnectionDao _clusterConnectionDao;
  @Mock private StreamsSampleConnectionDao _sampleConnectionDao;
  @Mock private AWSLambdaConnectionDao _awsLambdaConnectionDao;
  @Mock private StreamsS3ConnectionDao _s3ConnectionDao;
  @Mock private StreamsAWSKinesisDataStreamsConnectionDao _kinesisConnectionDao;
  @Mock private StreamsProcessManagerSvc _spmManager;
  @Mock private StreamsTenantDao _streamsTenantDao;
  @Mock private StreamsHttpsConnectionDao _httpsConnectionDao;
  @Mock private StreamsTransitGatewayResourceSharesDao _transitGatewayResourceSharesDao;
  @Mock private StreamsTransitGatewayRouteDao _transitGatewayRouteDao;
  @Mock private StreamsTransitGatewayAttachmentsDao _transitGatewayAttachmentsDao;
  @Mock private SegmentEventSvc _segmentEventSvc;
  @Mock private ClusterDescriptionDao _clusterDescriptionDao;
  @Mock private AuditSvc _auditSvc;
  @Mock private NDSUISvc _ndsUiSvc;
  @Mock private FeatureFlagSvc _featureFlagSvc;
  @Mock private VPCProxyInstanceDescriptionSvc _vpcProxyInstanceDescriptionSvc;
  @Mock private VPCProxyFactory _vpcProxyFactory;
  @Mock private IVPCProxyDeploymentDescriptionSvc _vpcDeploymentProxy;
  @Mock private VPCPeeringConnectionSvc _vpcPeeringConnectionSvc;
  @Mock private StreamsPrivateLinkDao _privateLinkDao;
  @Mock private NDSCloudProviderContainerSvc _containerSvc;
  @Mock private CustomerMetricsQuerySvc _customerMetricsQuerySvc;
  @Mock private AppSettings _appSettings;
  @Mock private AWSAccountDao _awsAccountDao;
  @Mock private AzureSubscriptionDao _azureSubscriptionDao;
  @Mock private AWSApiSvc _awsApiSvc;
  @Mock private PaymentMethodGateway _paymentMethodGateway;
  @Mock private OrganizationSvc _organizationSvc;

  private StreamsSvc _streamsSvc;
  @Mock private Organization _org;

  @BeforeEach
  public void setUp() {
    CloudProviderRegistry.registerProvider(new AWSProvider());
    CloudProviderRegistry.registerProvider(new AzureProvider());
    CloudProviderRegistry.registerProvider(new GCPProvider());
    doReturn(_vpcDeploymentProxy).when(_vpcProxyFactory).createProxy();
    doReturn(_defaultAppEnv).when(_appSettings).getAppEnv();
    doReturn(true).when(_paymentMethodGateway).hasEffectivePaymentMethod(any());
    doReturn(_org).when(_organizationSvc).findById(any());
    doReturn(true).when(_org).getStreamsCrossGroupEnabled();

    _streamsSvc =
        spy(
            new StreamsSvc(
                _ndsGroupSvc,
                _groupSvc,
                _roleSetSvc,
                _dataLakePublicSvc,
                _dataLakeTenantSvc,
                _clusterDescriptionDao,
                _streamsTenantDao,
                _streamsKafkaConnectionDao,
                _clusterConnectionDao,
                _sampleConnectionDao,
                _httpsConnectionDao,
                _privateLinkDao,
                _awsLambdaConnectionDao,
                _s3ConnectionDao,
                _kinesisConnectionDao,
                _awsAccountDao,
                _azureSubscriptionDao,
                _transitGatewayResourceSharesDao,
                _transitGatewayRouteDao,
                _transitGatewayAttachmentsDao,
                _spmManager,
                _segmentEventSvc,
                _auditSvc,
                _ndsUiSvc,
                _featureFlagSvc,
                _vpcProxyInstanceDescriptionSvc,
                _vpcPeeringConnectionSvc,
                _vpcProxyFactory,
                _containerSvc,
                _customerMetricsQuerySvc,
                _awsApiSvc,
                _appSettings,
                _paymentMethodGateway,
                _organizationSvc));
  }

  @Test
  public void
      testCreateVPCProxyDeploymentDescriptionForApi_generatesIdAndCallsProxyWithResolvedParams()
          throws SvcException {
    ObjectId deploymentId = new ObjectId();
    ObjectId groupId = new ObjectId();
    ObjectId tenantId = new ObjectId();
    String tenantName = "tenantA";
    String connectionName = "connA";

    StreamsTenant tenant =
        new StreamsTenant(
            tenantId, tenantName, groupId, false, new Date(), new Date(), _defaultStreamConfig);

    // Data lake tenant with AWS provider/region
    NDSDataLakeTenant dlTenant =
        NDSDataLakeTenant.builder()
            .id(new NDSDataLakeTenant.NDSDataLakeTenantId(groupId, tenantName))
            .tenantId(tenantId)
            .dataProcessRegion(
                new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(
                    CloudProvider.AWS, AWSRegionName.US_EAST_1.name()))
            .build();

    // Kafka connection with PRIVATE_LINK networking so getNetworkingMode resolves to AWS PL
    KafkaConnection kafkaConn =
        new KafkaConnection(
            new ObjectId(),
            tenantId,
            connectionName,
            new Date(),
            new Date(),
            null,
            null,
            null,
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PRIVATE_LINK).build()),
            null);

    doReturn(tenant).when(_streamsSvc).getStreamsTenantById(groupId, tenantId);
    doReturn(dlTenant).when(_streamsSvc).getDataLakeTenant(tenant);
    doReturn(kafkaConn).when(_streamsSvc).findConnectionModel(groupId, tenantName, connectionName);

    // Capture args to the proxy svc
    final com.xgen.cloud.streams._public.model.VPCProxyDeploymentDescription createdModel =
        new com.xgen.cloud.streams._public.model.VPCProxyDeploymentDescription(
            deploymentId,
            tenantId,
            connectionName,
            groupId,
            Instant.now(),
            com.xgen.cloud.streams._public.model.VPCProxyDeploymentDescription.State.IDLE,
            false,
            Instant.now(),
            List.of(),
            com.xgen.mhouse.services.streamprocessormanager.v1.Models.ProxyType
                .PROXY_TYPE_UNSPECIFIED_VALUE,
            com.xgen.mhouse.services.streamprocessormanager.v1.Models.ConnectionType
                .CONNECTION_TYPE_KAFKA_VALUE,
            null,
            null,
            null,
            CloudProvider.AWS,
            null,
            null,
            new ObjectId());

    doReturn(createdModel)
        .when(_vpcDeploymentProxy)
        .createVPCProxyDeploymentDescription(
            any(ObjectId.class),
            eq(tenantId),
            eq(connectionName),
            eq(groupId),
            any(),
            eq(CloudProvider.AWS),
            any(ObjectId.class),
            any(Integer.class),
            eq(
                com.xgen.mhouse.services.streamprocessormanager.v1.Models.ConnectionType
                    .CONNECTION_TYPE_KAFKA_VALUE));

    com.xgen.cloud.streams._public.model.VPCProxyDeploymentDescription result =
        _streamsSvc.createVPCProxyDeploymentDescriptionForApi(
            deploymentId, groupId, tenantId, connectionName);

    assertEquals(deploymentId, result.getId());
    verify(_vpcDeploymentProxy, times(1))
        .createVPCProxyDeploymentDescription(
            eq(deploymentId),
            eq(tenantId),
            eq(connectionName),
            eq(groupId),
            any(),
            eq(CloudProvider.AWS),
            any(ObjectId.class),
            any(Integer.class),
            eq(
                com.xgen.mhouse.services.streamprocessormanager.v1.Models.ConnectionType
                    .CONNECTION_TYPE_KAFKA_VALUE));
  }

  @Test
  public void testCreateVPCProxyDeploymentDescriptionForApi_autoGeneratesIdWhenNull()
      throws SvcException {
    ObjectId groupId = new ObjectId();
    ObjectId tenantId = new ObjectId();
    String tenantName = "tenantB";
    String connectionName = "connB";

    StreamsTenant tenant =
        new StreamsTenant(
            tenantId, tenantName, groupId, false, new Date(), new Date(), _defaultStreamConfig);

    NDSDataLakeTenant dlTenant =
        NDSDataLakeTenant.builder()
            .id(new NDSDataLakeTenant.NDSDataLakeTenantId(groupId, tenantName))
            .tenantId(tenantId)
            .dataProcessRegion(
                new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(
                    CloudProvider.AWS, AWSRegionName.US_EAST_1.name()))
            .build();

    KafkaConnection kafkaConn =
        new KafkaConnection(
            new ObjectId(),
            tenantId,
            connectionName,
            new Date(),
            new Date(),
            null,
            null,
            null,
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PRIVATE_LINK).build()),
            null);

    doReturn(tenant).when(_streamsSvc).getStreamsTenantById(groupId, tenantId);
    doReturn(dlTenant).when(_streamsSvc).getDataLakeTenant(tenant);
    doReturn(kafkaConn).when(_streamsSvc).findConnectionModel(groupId, tenantName, connectionName);

    // We’ll assert that whatever id passed is non-null by capturing the argument
    final com.xgen.cloud.streams._public.model.VPCProxyDeploymentDescription createdModel =
        mock(com.xgen.cloud.streams._public.model.VPCProxyDeploymentDescription.class);
    doReturn(createdModel)
        .when(_vpcDeploymentProxy)
        .createVPCProxyDeploymentDescription(
            any(ObjectId.class),
            eq(tenantId),
            eq(connectionName),
            eq(groupId),
            any(),
            eq(CloudProvider.AWS),
            any(ObjectId.class),
            any(Integer.class),
            eq(
                com.xgen.mhouse.services.streamprocessormanager.v1.Models.ConnectionType
                    .CONNECTION_TYPE_KAFKA_VALUE));

    _streamsSvc.createVPCProxyDeploymentDescriptionForApi(null, groupId, tenantId, connectionName);

    verify(_vpcDeploymentProxy, times(1))
        .createVPCProxyDeploymentDescription(
            isNull(),
            eq(tenantId),
            eq(connectionName),
            eq(groupId),
            any(),
            eq(CloudProvider.AWS),
            any(ObjectId.class),
            any(Integer.class),
            eq(
                com.xgen.mhouse.services.streamprocessormanager.v1.Models.ConnectionType
                    .CONNECTION_TYPE_KAFKA_VALUE));
  }

  @Test
  public void testViewConnectionRegistryItem() throws SvcException {
    final ObjectId groupId = new ObjectId();
    final ObjectId streamsTenantId = new ObjectId();
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    final StreamsTenant ndsStreamsTenant =
        new StreamsTenant(
            streamsTenantId,
            "tenantName",
            groupId,
            false,
            new Date(),
            new Date(),
            _defaultStreamConfig);

    doReturn(groupId).when(group).getId();

    doReturn(Optional.of(ndsStreamsTenant))
        .when(_streamsTenantDao)
        .findByGroupAndName(groupId, "tenantName");

    _streamsSvc.viewConnectionRegistryEvent("foo", "tenantName", group, auditInfo);

    verify(_auditSvc, times(1)).saveAuditEvent(any());
  }

  @Test
  public void testGetAvailableRegions_regionsNotLoaded() throws SvcException {
    doThrow(new SvcException(DATA_FEDERATION_SUPPORTED_REGIONS_NOT_LOADED))
        .when(_dataLakeTenantSvc)
        .getDataLakeCloudProviderRegions();

    assertThrows(
        SvcException.class,
        () -> _streamsSvc.getAvailableRegions(null),
        STREAM_SUPPORTED_REGIONS_NOT_LOADED.getMessage());
  }

  @Test
  public void testGetAvailableRegions_azureEnabled() throws SvcException {
    final Group group = mock(Group.class);

    doReturn(true)
        .when(_featureFlagSvc)
        .isFeatureFlagEnabled(FeatureFlag.STREAMS_ENABLE_AZURE, null, group);

    doReturn(mockRegions()).when(_dataLakeTenantSvc).getDataLakeCloudProviderRegions();

    var result = _streamsSvc.getAvailableRegions(group);

    final Consumer<RegionView> getAndAssert =
        regionView -> {
          Optional<RegionView> foundRegion =
              result.stream().filter(region -> region.equals(regionView)).findFirst();

          assertEquals(Optional.of(regionView), foundRegion);
        };

    getAndAssert.accept(new RegionView(createRegion(CloudProvider.PROVIDER_AZURE, "US_EAST_2")));
    getAndAssert.accept(new RegionView(createRegion(CloudProvider.PROVIDER_AWS, "US_EAST_1")));
  }

  @Test
  public void testGetAvailableRegions_default() throws SvcException {
    final Group group = mock(Group.class);

    doReturn(mockRegions()).when(_dataLakeTenantSvc).getDataLakeCloudProviderRegions();

    var expected =
        List.of(
            "AP_NORTHEAST_1",
            "AP_SOUTHEAST_1",
            "AP_SOUTHEAST_2",
            "AP_SOUTH_1",
            "CA_CENTRAL_1",
            "CENTRAL_US",
            "EU_CENTRAL_1",
            "EU_WEST_1",
            "EU_WEST_2",
            "SA_EAST_1",
            "US_EAST_1",
            "US_EAST_2",
            "US_EAST_4",
            "US_WEST_2",
            "WESTERN_EUROPE");

    var result =
        _streamsSvc.getAvailableRegions(group).stream().map(RegionView::getKey).sorted().toList();
    assertEquals(expected, result);
  }

  @Test
  public void testGetAvailableRegions_additionalRegionsEnabled() throws SvcException {
    final Group group = mock(Group.class);

    doReturn(true)
        .when(_featureFlagSvc)
        .isFeatureFlagEnabled(FeatureFlag.STREAMS_ENABLE_ADDITIONAL_REGIONS, null, group);

    doReturn(mockRegions()).when(_dataLakeTenantSvc).getDataLakeCloudProviderRegions();

    final BiConsumer<List<RegionView>, RegionView> getAndAssert =
        (regions, regionView) -> {
          Optional<RegionView> foundRegion =
              regions.stream().filter(region -> region.equals(regionView)).findFirst();

          assertEquals(Optional.of(regionView), foundRegion);
        };

    var result = _streamsSvc.getAvailableRegions(group);

    for (String region : StreamsConstants.getAllowedAWSRegions(_defaultAppEnv)) {
      getAndAssert.accept(result, new RegionView(createRegion(CloudProvider.PROVIDER_AWS, region)));
    }
    for (String additionalRegion :
        StreamsConstants.getAllowedAWSAdditionalRegions(_defaultAppEnv)) {
      getAndAssert.accept(
          result, new RegionView(createRegion(CloudProvider.PROVIDER_AWS, additionalRegion)));
    }

    result = _streamsSvc.getAvailableRegions(group);

    for (String region : StreamsConstants.getAllowedAWSRegions(_defaultAppEnv)) {
      getAndAssert.accept(result, new RegionView(createRegion(CloudProvider.PROVIDER_AWS, region)));
    }
    for (String additionalRegion :
        StreamsConstants.getAllowedAWSAdditionalRegions(_defaultAppEnv)) {
      getAndAssert.accept(
          result, new RegionView(createRegion(CloudProvider.PROVIDER_AWS, additionalRegion)));
    }
  }

  @Test
  public void testGetAvailableRegions_additionalAzureRegionsEnabled() throws SvcException {
    final Group group = mock(Group.class);

    doReturn(true)
        .when(_featureFlagSvc)
        .isFeatureFlagEnabled(FeatureFlag.STREAMS_ENABLE_AZURE, null, group);

    doReturn(true)
        .when(_featureFlagSvc)
        .isFeatureFlagEnabled(FeatureFlag.STREAMS_ENABLE_ADDITIONAL_AZURE_REGIONS, null, group);

    doReturn(mockRegions()).when(_dataLakeTenantSvc).getDataLakeCloudProviderRegions();

    final BiConsumer<List<RegionView>, RegionView> getAndAssert =
        (regions, regionView) -> {
          Optional<RegionView> foundRegion =
              regions.stream().filter(region -> region.equals(regionView)).findFirst();

          assertEquals(Optional.of(regionView), foundRegion);
        };

    var result = _streamsSvc.getAvailableRegions(group);

    for (String region : StreamsConstants.getAllowedAzureRegions(_defaultAppEnv)) {
      getAndAssert.accept(
          result, new RegionView(createRegion(CloudProvider.PROVIDER_AZURE, region)));
    }
    for (String additionalRegion :
        StreamsConstants.getAllowedAzureAdditionalRegions(_defaultAppEnv)) {
      getAndAssert.accept(
          result, new RegionView(createRegion(CloudProvider.PROVIDER_AZURE, additionalRegion)));
    }

    result = _streamsSvc.getAvailableRegions(group);

    for (String region : StreamsConstants.getAllowedAzureRegions(_defaultAppEnv)) {
      getAndAssert.accept(
          result, new RegionView(createRegion(CloudProvider.PROVIDER_AZURE, region)));
    }
    for (String additionalRegion :
        StreamsConstants.getAllowedAzureAdditionalRegions(_defaultAppEnv)) {
      getAndAssert.accept(
          result, new RegionView(createRegion(CloudProvider.PROVIDER_AZURE, additionalRegion)));
    }
  }

  @Test
  public void testGetAvailableRegions_additionalRegionsDisabled() throws SvcException {
    final Group group = mock(Group.class);

    doReturn(mockRegions()).when(_dataLakeTenantSvc).getDataLakeCloudProviderRegions();

    var expected =
        Stream.concat(
                StreamsConstants.getAllowedAWSRegions(_defaultAppEnv).stream(),
                StreamsConstants.getAllowedGCPRegions(_defaultAppEnv).stream())
            .sorted()
            .toList();

    var result =
        _streamsSvc.getAvailableRegions(group).stream().map(RegionView::getKey).sorted().toList();
    assertEquals(expected, result);
  }

  @Test
  public void testGetByGroupAndName_returnsStorageInformation() throws SvcException {
    final Group group = mock(Group.class);
    final ObjectId groupId = new ObjectId();
    doReturn(groupId).when(group).getId();
    final ObjectId streamsTenantId = new ObjectId();

    final StreamsTenant ndsStreamsTenant =
        new StreamsTenant(
            streamsTenantId,
            "tenantName",
            groupId,
            false,
            new Date(),
            new Date(),
            _defaultStreamConfig);
    final NDSDataLakeTenant ndsDataLakeTenant = mock(NDSDataLakeTenant.class);

    // Connection & Store
    final String expectedName = "kafka";
    final String expectedHost = "host:2020";
    final NDSDataLakeStreamsNetworking expectedNetworking =
        new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.PUBLIC).build());
    final NDSDataLakeKafkaSASLPlain expectedAuth =
        new NDSDataLakeKafkaSASLPlain(new KafkaAuthUsernamePassword("Bob", "hunter2"));
    final NDSDataLakeKafkaSecurityPlain expectedSecurity = new NDSDataLakeKafkaSecurityPlain();

    final KafkaConnection kafkaConn =
        new KafkaConnection(
            new ObjectId(),
            new ObjectId(),
            expectedName,
            new Date(),
            new Date(),
            expectedAuth,
            expectedSecurity,
            new HashMap<>(),
            expectedNetworking,
            null);

    doReturn(Optional.of(ndsStreamsTenant))
        .when(_streamsTenantDao)
        .findByGroupAndName(groupId, "tenantName");
    doReturn(Optional.of(ndsDataLakeTenant))
        .when(_dataLakeTenantSvc)
        .findByTenantId(streamsTenantId);

    Document config =
        new Document(
            FieldDefs.STORES,
            List.of(
                new Document()
                    .append(StreamsConnectionView.FieldDefs.NAME, expectedName)
                    .append(StreamsConnectionView.FieldDefs.PROVIDER, ProviderValues.KAFKA)
                    .append(
                        StreamsKafkaConnectionView.FieldDefs.BOOTSTRAP_SERVERS,
                        List.of(expectedHost.split(",")))));
    NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(config);

    doReturn(new ObjectId()).when(ndsDataLakeTenant).getTenantId();
    doReturn(new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1"))
        .when(ndsDataLakeTenant)
        .getDataProcessRegion();
    doReturn(view).when(_dataLakeTenantSvc).getStorageConfig(ndsDataLakeTenant);
    doReturn(List.of(kafkaConn)).when(_streamsKafkaConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of()).when(_clusterConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of()).when(_sampleConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of()).when(_httpsConnectionDao).fetchByTenantIdAndType(any());

    // pIncludeConnectionDetails false (Read only view)
    {
      final StreamsTenantView result =
          _streamsSvc.findTenantWithConnections(
              group, "tenantName", false, mock(AuditInfo.class), null);

      final StreamsKafkaConnectionView expected =
          new StreamsKafkaConnectionView(
              expectedName, expectedHost, null, null, null, expectedNetworking, null, false, false);

      Optional<StreamsConnectionView> conn =
          result.getConnections().stream()
              .filter(s -> s.getName().equals(expectedName))
              .findFirst();
      assertTrue(conn.isPresent());
      assertEquals(expected, conn.get());
    }
    // pIncludeConnectionDetails true (With permissions)
    {
      final StreamsTenantView result =
          _streamsSvc.findTenantWithConnections(
              group, "tenantName", true, mock(AuditInfo.class), null);

      final StreamsKafkaConnectionView expected =
          new StreamsKafkaConnectionView(
              expectedName,
              expectedHost,
              expectedSecurity,
              expectedAuth,
              new HashMap<>(),
              expectedNetworking,
              null,
              false,
              false);

      Optional<StreamsConnectionView> conn =
          result.getConnections().stream()
              .filter(s -> s.getName().equals(expectedName))
              .findFirst();
      assertTrue(conn.isPresent());
      assertEquals(expected, conn.get());
    }
  }

  @Test
  public void testInstanceCreateError() throws SvcException {
    doThrow(new SvcException(CommonErrorCode.SERVER_ERROR))
        .when(_dataLakeTenantSvc)
        .createTenant(any(), any(), any());

    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    final StreamsTenantView ndsStreamsTenant =
        new StreamsTenantView(
            "testStream",
            false,
            new NDSDataLakeDataProcessRegionView(
                new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1")),
            new StreamConfigView(Tier.SP30));

    try {
      _streamsSvc.createTenant(ndsStreamsTenant, group, auditInfo, true);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_streamsSvc, never()).incrementConnectionErrorCounter(any(), any());
      verify(_streamsSvc, times(1)).incrementInstanceErrorCounter(any(), any());
    }

    doReturn(false)
        .when(_featureFlagSvc)
        .isFeatureFlagEnabled(eq(FeatureFlag.ATLAS_STREAMS_SP10_TIER), any(), any());
    final StreamsTenantView ndsStreamsTenantWithInvalidTier =
        new StreamsTenantView(
            "testStream",
            false,
            new NDSDataLakeDataProcessRegionView(
                new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1")),
            new StreamConfigView(Tier.SP10));

    assertThrows(
        SvcException.class,
        () -> _streamsSvc.createTenant(ndsStreamsTenantWithInvalidTier, group, auditInfo, true));
    verify(_streamsSvc, never()).incrementConnectionErrorCounter(any(), any());
    verify(_streamsSvc, times(1)).incrementInstanceErrorCounter(any(), any());

    doReturn(false)
        .when(_featureFlagSvc)
        .isFeatureFlagEnabled(eq(FeatureFlag.STREAMS_ENABLE_WORKSPACES), any(), any());
    final StreamsTenantView ndsStreamsTenantWithWorkspaceFfOff =
        new StreamsTenantView(
            "workspaceStream",
            false,
            new NDSDataLakeDataProcessRegionView(
                new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1")),
            new StreamConfigView(Tier.SP30, Tier.SP30, Tier.SP50));

    assertThrows(
        SvcException.class,
        () -> _streamsSvc.createTenant(ndsStreamsTenantWithWorkspaceFfOff, group, auditInfo, true));
    verify(_streamsSvc, never()).incrementConnectionErrorCounter(any(), any());
    verify(_streamsSvc, times(1)).incrementInstanceErrorCounter(any(), any());
  }

  @Test
  public void testInstanceCreateError_paymentMethodMissing() {
    final Group group = mock(Group.class);
    group.setOrgId(new ObjectId());
    final AuditInfo auditInfo = mock(AuditInfo.class);

    when(_paymentMethodGateway.hasEffectivePaymentMethod(eq(group.getOrgId()))).thenReturn(false);

    final StreamsTenantView ndsStreamsTenant =
        new StreamsTenantView(
            "testStream",
            false,
            new NDSDataLakeDataProcessRegionView(
                new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1")),
            new StreamConfigView(Tier.SP30));

    final Exception thrown =
        assertThrows(
            SvcException.class,
            () -> _streamsSvc.createTenant(ndsStreamsTenant, group, auditInfo, false));
    assertTrue(thrown.getMessage().contains("Payment method required"));
  }

  @Test
  public void testInstanceDeleteError() throws SvcException {
    doThrow(new SvcException(CommonErrorCode.SERVER_ERROR))
        .when(_dataLakeTenantSvc)
        .deleteByGroupIdAndName(any(), any(), any());

    final Group group = mock(Group.class);
    doReturn(new ObjectId()).when(group).getId();

    final AuditInfo auditInfo = mock(AuditInfo.class);

    assertThrows(
        SvcException.class, () -> _streamsSvc.deleteTenant(group, "testStream", auditInfo, true));
    verify(_streamsSvc, never()).incrementConnectionErrorCounter(any(), any());
    verify(_streamsSvc, times(1)).incrementInstanceErrorCounter(any(), any());
  }

  @Test
  public void testInstanceDeleteDataLakeErrorDoesNotRemoveConnection() throws SvcException {
    final Group group = mock(Group.class);
    final ObjectId groupId = new ObjectId();
    doReturn(groupId).when(group).getId();

    final AuditInfo auditInfo = mock(AuditInfo.class);
    final ObjectId streamsTenantId = new ObjectId();

    final StreamsTenant ndsStreamsTenant =
        new StreamsTenant(
            streamsTenantId,
            "tenantName",
            groupId,
            false,
            new Date(),
            new Date(),
            _defaultStreamConfig);
    final NDSDataLakeTenant ndsDataLakeTenant = mock(NDSDataLakeTenant.class);

    // Get tenants
    doReturn(Optional.of(ndsStreamsTenant))
        .when(_streamsTenantDao)
        .findByGroupAndName(any(), any());
    doReturn(Optional.of(ndsDataLakeTenant))
        .when(_dataLakeTenantSvc)
        .findByTenantId(streamsTenantId);
    // Drop existing processors (by returning none; do nothing)
    doReturn(List.of())
        .when(_streamsSvc)
        .getStreamTenantProcessors(
            eq(ndsStreamsTenant), eq(ndsDataLakeTenant), anyBoolean(), anyBoolean(), anyBoolean());

    // Return connection names
    doReturn(List.of()).when(_clusterConnectionDao).fetchByTenantId(any());

    doThrow(new DataLakeAdminApiException(DATA_LAKE_STORAGE_CONFIG_OUTDATED))
        .when(_dataLakePublicSvc)
        .deleteByGroupAndName(any(), any(), any());

    assertThrows(
        SvcException.class,
        () -> _streamsSvc.deleteTenant(group, "tenantName", auditInfo, true),
        STREAM_TENANT_CONCURRENCY_ERROR.getMessage());

    // Will retry 6 times, should never delete without first deleting datalake.
    verify(_streamsTenantDao, never()).delete(any());
  }

  @Test
  public void testInstanceDeleteWithoutDataLake() throws SvcException {
    final Group group = mock(Group.class);
    doReturn(new ObjectId()).when(group).getId();

    final AuditInfo auditInfo = mock(AuditInfo.class);
    final ObjectId groupId = new ObjectId();
    final ObjectId streamsTenantId = new ObjectId();

    final StreamsTenant ndsStreamsTenant =
        new StreamsTenant(
            streamsTenantId,
            "tenantName",
            groupId,
            false,
            new Date(),
            new Date(),
            _defaultStreamConfig);

    // Get tenants
    doReturn(Optional.of(ndsStreamsTenant))
        .when(_streamsTenantDao)
        .findByGroupAndName(any(), any());
    // Can't find data lake tenant
    doThrow(new DataLakeAdminApiException(DATA_LAKE_TENANT_NOT_FOUND_FOR_NAME))
        .when(_dataLakePublicSvc)
        .deleteByGroupAndName(any(), any(), any());
    // Return connection names
    doReturn(List.of()).when(_clusterConnectionDao).fetchByTenantId(any());

    _streamsSvc.deleteTenant(group, "tenantName", auditInfo, true);

    verify(_streamsTenantDao, times(1)).delete(any());
  }

  @Test
  public void testInstanceUpdateError() throws SvcException {
    doThrow(new SvcException(CommonErrorCode.SERVER_ERROR))
        .when(_dataLakeTenantSvc)
        .updateTenant(any(), any(), any(), any());

    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.updateTenant(
          group,
          "testStream",
          new NDSDataLakeDataProcessRegionView(
              new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1")),
          auditInfo);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_streamsSvc, never()).incrementConnectionErrorCounter(any(), any());
      verify(_streamsSvc, times(1)).incrementInstanceErrorCounter(any(), any());
    }
  }

  @Test
  public void testCreateVPCPeeringKafkaConnection() throws SvcException {
    final StreamsTenant testTenant = mock(StreamsTenant.class);
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    setupMocksForConnection(testTenant);

    final StreamsKafkaConnectionView kafkaConn =
        new StreamsKafkaConnectionView(
            "kafkaPlain",
            "host:2020",
            new NDSDataLakeKafkaSecuritySSL(new KafkaSSLSecret("")),
            new NDSDataLakeKafkaSASLPlain(new KafkaAuthUsernamePassword("Bob", "hunter2")),
            Map.of(),
            new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.VPC).build()),
            new ProxyInfo());

    _streamsSvc.createConnection(kafkaConn, testTenant.getName(), group, auditInfo, null);

    verify(_vpcDeploymentProxy, times(1))
        .createVPCProxyDeploymentDescription(
            any(), any(), any(), any(), any(), any(), any(), any(), any());
    verify(_streamsSvc, never()).incrementInstanceErrorCounter(any(), any());
  }

  @Test
  public void testPrivateLinkValidation() throws SvcException {
    final StreamsTenant testTenant = mock(StreamsTenant.class);

    setupMocksForConnection(testTenant);

    final ObjectId groupId = new ObjectId();
    final ObjectId privateLinkID = new ObjectId();
    final String connectionName = "PrivateLinkConnection";
    final String DNS_DOMAIN = "test-kafka-namespace.servicebus.windows.net";
    final String BOOTSTRAP_SERVERS = DNS_DOMAIN + ":9093";

    final StreamsTenant ndsStreamsTenant =
        new StreamsTenant(
            testTenant.getId(),
            "tenantName",
            groupId,
            false,
            new Date(),
            new Date(),
            _defaultStreamConfig);

    doReturn(Optional.of(ndsStreamsTenant))
        .when(_streamsTenantDao)
        .findByGroupAndName(groupId, "tenantName");

    // Mock PrivateLink enabled Kafka connection
    var conn =
        new KafkaConnection(
            new ObjectId(),
            testTenant.getId(),
            connectionName,
            new Date(),
            new Date(),
            null,
            null,
            null,
            new NDSDataLakeStreamsNetworking(
                new Access.AccessBuilder(Type.PRIVATE_LINK).setConnectionId(privateLinkID).build()),
            new ProxyInfo());

    doReturn(Optional.of(conn))
        .when(_streamsKafkaConnectionDao)
        .fetchByTenantIdAndName(any(), any());

    StreamsPrivateLink streamsPrivateLink =
        getMockPrivateLinkForProvider(
            groupId,
            StreamsPrivateLinkProviderType.AWS,
            StreamsPrivateLinkVendorType.GENERIC,
            DNS_DOMAIN,
            "us-east-1");
    doReturn(Optional.of(streamsPrivateLink))
        .when(_privateLinkDao)
        .findByGroupIdAndId(any(), any());

    // This should work.
    _streamsSvc.isPrivateLinkValid(groupId, conn, BOOTSTRAP_SERVERS, ndsStreamsTenant);

    // This should fail because of a vendor mismatch.
    streamsPrivateLink =
        getMockPrivateLinkForProvider(
            groupId,
            StreamsPrivateLinkProviderType.AZURE,
            StreamsPrivateLinkVendorType.GENERIC,
            DNS_DOMAIN,
            "eastus2");
    doReturn(Optional.of(streamsPrivateLink))
        .when(_privateLinkDao)
        .findByGroupIdAndId(any(), any());

    // This should fail (as we are creating an Azure resource in an AWS tenant).
    assertThrows(
        SvcException.class,
        () -> _streamsSvc.isPrivateLinkValid(groupId, conn, BOOTSTRAP_SERVERS, ndsStreamsTenant),
        STREAM_PRIVATE_NETWORK_PROVIDER_MISMATCH.getMessage());

    // This should fail because the bootstrap servers aren't in the dnsDomain provided.
    streamsPrivateLink =
        getMockPrivateLinkForProvider(
            groupId,
            StreamsPrivateLinkProviderType.AWS,
            StreamsPrivateLinkVendorType.CONFLUENT,
            "us-east-1.aws.glb.confluent.cloud",
            "us-east-1");
    doReturn(Optional.of(streamsPrivateLink))
        .when(_privateLinkDao)
        .findByGroupIdAndId(any(), any());
    assertThrows(
        SvcException.class,
        () ->
            _streamsSvc.isPrivateLinkValid(
                groupId, conn, "us-east-1.glb.notconfluent.cloud:9093", ndsStreamsTenant),
        STREAM_PRIVATE_NETWORK_PROVIDER_MISMATCH.getMessage());

    // This should pass because it's in the same top level domain, despite
    // the bootstrap servers being in a different subdomain, and using different
    // case.
    streamsPrivateLink =
        getMockPrivateLinkForProvider(
            groupId,
            StreamsPrivateLinkProviderType.AWS,
            StreamsPrivateLinkVendorType.CONFLUENT,
            "us-east-1.aws.glb.confluent.cloud",
            "us-east-1");
    doReturn(Optional.of(streamsPrivateLink))
        .when(_privateLinkDao)
        .findByGroupIdAndId(any(), any());
    _streamsSvc.isPrivateLinkValid(
        groupId, conn, "NoT-a-ReaL-BrokeR.us-east-1.ConfluenT.Cloud:9093", ndsStreamsTenant);
  }

  private StreamsPrivateLink getMockPrivateLinkForProvider(
      ObjectId groupId,
      StreamsPrivateLinkProviderType streamsPrivateLinkProviderType,
      StreamsPrivateLinkVendorType streamsPrivateLinkVendorType,
      String pDnsDomain,
      String region) {
    return new StreamsPrivateLink(
        new ObjectId(),
        groupId,
        streamsPrivateLinkVendorType,
        streamsPrivateLinkProviderType,
        "not-a-real-endpoint",
        "arn",
        null,
        null,
        null,
        pDnsDomain,
        null,
        null,
        null,
        new Date(),
        StreamsPrivateLinkState.DONE,
        "",
        0,
        null,
        null,
        region,
        null,
        null);
  }

  private void setupMocksForConnection(final StreamsTenant testTenant)
      throws DataLakeAdminApiException {
    doReturn(Optional.of(testTenant)).when(_streamsTenantDao).findByGroupAndName(any(), any());
    ObjectId streamsTenantId = new ObjectId();
    doReturn(streamsTenantId).when(testTenant).getId();
    final NDSDataLakeTenant ndsDataLakeTenant = mock(NDSDataLakeTenant.class);
    doReturn(Optional.of(ndsDataLakeTenant))
        .when(_dataLakeTenantSvc)
        .findByTenantId(streamsTenantId);
    doReturn(mock(NDSDataLakeTenant.NDSDataLakeDataProcessRegion.class))
        .when(ndsDataLakeTenant)
        .getDataProcessRegion();
    final AWSRegionName regionName = mock(AWSRegionName.class);
    when(ndsDataLakeTenant.getDataProcessRegion().getDataProcessRegionAsRegionName())
        .thenReturn(Optional.of(regionName));
    // getCloudProvider() defaults to AWS if no cloud provider was specified, ensure
    // mock mimics this behavior as well.
    when(ndsDataLakeTenant.getCloudProvider()).thenReturn(CloudProvider.AWS);

    doReturn(CloudProvider.AWS).when(regionName).getProvider();

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(new Document());
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());

    final NDSDataLakeStorageV1View.Builder mockBuilder =
        mock(NDSDataLakeStorageV1View.Builder.class);
    doReturn(mockBuilder).when(fooSpy).toBuilder();

    final NDSDataLakeTenant.NDSDataLakeTenantBuilder mockTenantBuilder =
        mock(NDSDataLakeTenant.NDSDataLakeTenantBuilder.class);
    doReturn(mockTenantBuilder).when(ndsDataLakeTenant).toBuilder();
    doReturn(mockTenantBuilder).when(mockTenantBuilder).lastUpdatedDate(any());
    doReturn(ndsDataLakeTenant).when(mockTenantBuilder).build();
  }

  @Test
  public void testConnectionCreateError() throws SvcException {
    doThrow(new DataLakeAdminApiException(CommonErrorCode.SERVER_ERROR))
        .when(_dataLakeTenantSvc)
        .getStorageConfig(any());

    final StreamsTenant testTenant = mock(StreamsTenant.class);

    doReturn(Optional.of(testTenant)).when(_streamsTenantDao).findByGroupAndName(any(), any());

    doReturn(new ObjectId()).when(testTenant).getId();

    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    final StreamsKafkaConnectionView kafkaConn =
        new StreamsKafkaConnectionView(
            "kafkaPlain",
            "host:2020",
            new NDSDataLakeKafkaSecurityPlain(),
            new NDSDataLakeKafkaSASLPlain(new KafkaAuthUsernamePassword("Bob", "hunter2")),
            Map.of(),
            new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());

    try {
      _streamsSvc.createConnection(kafkaConn, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_streamsSvc, times(1)).incrementConnectionErrorCounter(any(), any());
      verify(_streamsSvc, never()).incrementInstanceErrorCounter(any(), any());
    }
  }

  @Test
  public void testValidateConnectionForClusterInCrossProject_orgNotExist() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    final Date now = new Date();
    final ObjectId anotherProject = ObjectId.get();
    ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            now,
            now,
            "cluster0",
            new DBRoleToExecute(DBRoleType.BUILT_IN, "atlasAdmin"),
            anotherProject.toHexString());
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.LOCAL).when(appUser).getType();
    doReturn(null).when(_organizationSvc).findById(any());

    try {
      _streamsSvc.validateConnection(group, "fakeTenant", clusterConnection, appUser);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(NDSErrorCode.STREAM_INVALID_ORG_ID, ex.getErrorCode());
      assertEquals(
          "The org id " + orgId.toHexString() + " of the tenant group is invalid", ex.getMessage());
    }
  }

  @Test
  public void testValidateConnectionForClusterInCrossProject_orgSettingNotEnabled()
      throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    final Date now = new Date();
    final ObjectId anotherProject = ObjectId.get();
    ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            now,
            now,
            "cluster0",
            new DBRoleToExecute(DBRoleType.BUILT_IN, "atlasAdmin"),
            anotherProject.toHexString());
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.LOCAL).when(appUser).getType();
    doReturn(false).when(_org).getStreamsCrossGroupEnabled();

    try {
      _streamsSvc.validateConnection(group, "fakeTenant", clusterConnection, appUser);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(NDSErrorCode.STREAM_CROSS_PROJECT_NOT_ENABLED, ex.getErrorCode());
      assertEquals(
          "The organization setting for stream cross project is not enabled", ex.getMessage());
    }
  }

  @Test
  public void testValidateConnectionForClusterInCrossProject_UI() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    final Date now = new Date();
    final ObjectId anotherGroupId = ObjectId.get();
    final Group anotherGroup = mock(Group.class);
    doReturn(anotherGroupId).when(anotherGroup).getId();
    ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            now,
            now,
            "cluster0",
            new DBRoleToExecute(DBRoleType.BUILT_IN, "atlasAdmin"),
            anotherGroupId.toHexString());
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.LOCAL).when(appUser).getType();

    doReturn(List.of(anotherGroup)).when(_groupSvc).getVisibleGroups(any(), any());
    doReturn(true).when(_roleSetSvc).doRoleCheck(any(), any(), any(), any());
    doReturn(List.of(Role.GROUP_STREAM_PROCESSING_OWNER))
        .when(appUser)
        .getGroupRoles(anotherGroupId);
    final ClusterDescription cluster = mock(ClusterDescription.class);
    doReturn(Optional.of(cluster))
        .when(_clusterDescriptionDao)
        .findByName(
            new ObjectId(clusterConnection.getClusterGroupId()), clusterConnection.getCluster());
    _streamsSvc.validateConnection(group, "fakeTenant", clusterConnection, appUser);
  }

  @Test
  public void testValidateConnectionForClusterInCrossProject_UI_groupOwnerUser()
      throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    final Date now = new Date();
    final ObjectId anotherGroupId = ObjectId.get();
    final Group anotherGroup = mock(Group.class);
    doReturn(anotherGroupId).when(anotherGroup).getId();
    ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            now,
            now,
            "cluster0",
            new DBRoleToExecute(DBRoleType.BUILT_IN, "atlasAdmin"),
            anotherGroupId.toHexString());
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.LOCAL).when(appUser).getType();

    doReturn(List.of(anotherGroup)).when(_groupSvc).getVisibleGroups(any(), any());
    doReturn(true).when(_roleSetSvc).doRoleCheck(any(), any(), any(), any());
    final ClusterDescription cluster = mock(ClusterDescription.class);
    doReturn(Optional.of(cluster))
        .when(_clusterDescriptionDao)
        .findByName(
            new ObjectId(clusterConnection.getClusterGroupId()), clusterConnection.getCluster());
    _streamsSvc.validateConnection(group, "fakeTenant", clusterConnection, appUser);
  }

  @Test
  public void testValidateConnectionForClusterInCrossProject_API() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    final Date now = new Date();
    final ObjectId anotherProject = ObjectId.get();
    ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            now,
            now,
            "cluster0",
            new DBRoleToExecute(DBRoleType.BUILT_IN, "atlasAdmin"),
            anotherProject.toHexString());
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.API).when(appUser).getType();

    doReturn(true).when(_roleSetSvc).doRoleCheck(any(), any(), any(), any());
    final ClusterDescription cluster = mock(ClusterDescription.class);
    doReturn(Optional.of(cluster))
        .when(_clusterDescriptionDao)
        .findByName(
            new ObjectId(clusterConnection.getClusterGroupId()), clusterConnection.getCluster());

    _streamsSvc.validateConnection(group, "fakeTenant", clusterConnection, appUser);
  }

  @Test
  public void testValidateConnectionForClusterInSameProject_API() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    final Date now = new Date();
    ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            now,
            now,
            "cluster0",
            new DBRoleToExecute(DBRoleType.BUILT_IN, "atlasAdmin"),
            groupId.toHexString());
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.API).when(appUser).getType();

    final ClusterDescription cluster = mock(ClusterDescription.class);
    doReturn(Optional.of(cluster))
        .when(_clusterDescriptionDao)
        .findByName(
            new ObjectId(clusterConnection.getClusterGroupId()), clusterConnection.getCluster());
    _streamsSvc.validateConnection(group, "fakeTenant", clusterConnection, appUser);
  }

  @Test
  public void testValidateConnectionForClusterInCrossProject_UI_ProjectNotAccessible()
      throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    final Date now = new Date();
    final ObjectId anotherProject = ObjectId.get();
    ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            now,
            now,
            "cluster0",
            new DBRoleToExecute(DBRoleType.BUILT_IN, "atlasAdmin"),
            anotherProject.toHexString());
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.LOCAL).when(appUser).getType();

    try {
      _streamsSvc.validateConnection(group, "fakeTenant", clusterConnection, appUser);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(NDSErrorCode.STREAM_CLUSTER_PROJECT_ID_NOT_FOUND, ex.getErrorCode());
      assertEquals(
          "The project with id " + anotherProject.toHexString() + " is not found or not accessible",
          ex.getMessage());
    }
  }

  @Test
  public void testValidateConnectionForClusterInCrossProject_UI_ProjectAccessible_NoProperRole()
      throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    final Date now = new Date();
    final ObjectId anotherGroupId = ObjectId.get();
    final Group anotherGroup = mock(Group.class);
    doReturn(anotherGroupId).when(anotherGroup).getId();
    ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            now,
            now,
            "cluster0",
            new DBRoleToExecute(DBRoleType.BUILT_IN, "atlasAdmin"),
            anotherGroupId.toHexString());
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.LOCAL).when(appUser).getType();

    doReturn(List.of(anotherGroup)).when(_groupSvc).getVisibleGroups(any(), any());
    try {
      _streamsSvc.validateConnection(group, "fakeTenant", clusterConnection, appUser);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(NDSErrorCode.STREAM_CLUSTER_PROJECT_ID_NOT_FOUND, ex.getErrorCode());
      assertEquals(
          "The project with id " + anotherGroupId.toHexString() + " is not found or not accessible",
          ex.getMessage());
    }
  }

  @Test
  public void testValidateConnectionForClusterInCrossProject_API_NotOrgOwner() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    final Date now = new Date();
    final ObjectId anotherProject = ObjectId.get();
    ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            now,
            now,
            "cluster0",
            new DBRoleToExecute(DBRoleType.BUILT_IN, "atlasAdmin"),
            anotherProject.toHexString());
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.API).when(appUser).getType();

    doReturn(Set.of(RoleAssignment.forOrg(Role.ORG_OWNER, ObjectId.get())))
        .when(appUser)
        .getRoles();
    try {
      _streamsSvc.validateConnection(group, "fakeTenant", clusterConnection, appUser);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(NDSErrorCode.STREAM_PROJECT_NOT_ALLOWED, ex.getErrorCode());
      assertEquals(
          "The API key doesn't have permission to the project with id "
              + anotherProject.toHexString(),
          ex.getMessage());
    }
  }

  @Test
  public void testValidateConnectionForClusterInCrossProject_API_ClusterNotExist()
      throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    final Date now = new Date();
    final ObjectId anotherProject = ObjectId.get();
    ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            now,
            now,
            "cluster0",
            new DBRoleToExecute(DBRoleType.BUILT_IN, "atlasAdmin"),
            anotherProject.toHexString());
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.API).when(appUser).getType();

    doReturn(true).when(_roleSetSvc).doRoleCheck(any(), any(), any(), any());
    try {
      _streamsSvc.validateConnection(group, "fakeTenant", clusterConnection, appUser);
    } catch (SvcException ex) {
      assertEquals(NDSErrorCode.CLUSTER_NOT_FOUND, ex.getErrorCode());
      assertEquals(
          "No cluster named "
              + clusterConnection.getCluster()
              + " in group "
              + anotherProject.toHexString(),
          ex.getMessage());
    }
  }

  @Test
  public void testValidateConnectionForClusterInCrossProject_UserTypeNotSupported()
      throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    final Date now = new Date();
    final ObjectId anotherProject = ObjectId.get();
    ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            now,
            now,
            "cluster0",
            new DBRoleToExecute(DBRoleType.BUILT_IN, "atlasAdmin"),
            anotherProject.toHexString());
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.SERVICE_ACCOUNT).when(appUser).getType();

    try {
      _streamsSvc.validateConnection(group, "fakeTenant", clusterConnection, appUser);
    } catch (SvcException ex) {
      assertEquals(NDSErrorCode.STREAM_USER_TYPE_NOT_SUPPORTED, ex.getErrorCode());
      assertEquals("The type " + appUser.getType() + " of user is not supported", ex.getMessage());
    }
  }

  @Test
  public void testValidateConnectionForKinesis() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    NDSCloudProviderAccessView accessView = mock(NDSCloudProviderAccessView.class);
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    final AppUser appUser = mock(AppUser.class);
    String roleArn = "arn:aws:iam::************:role/forKinesis";
    ObjectId roleId = new ObjectId();
    doReturn(accessView).when(_ndsUiSvc).getCloudProviderAccess(eq(groupId));

    final NDSCloudProviderAccessAWSIAMRole role =
        NDSCloudProviderAccessAWSIAMRole.builder()
            .roleId(roleId)
            .iamAssumedRoleArn(roleArn)
            .atlasAssumedRoleExternalId("externalId")
            .atlasAWSAccountArn("arn:aws:iam::************:root")
            .atlasAWSAccountId(new ObjectId())
            .featureUsages(List.of())
            .authorizedDate(new Date())
            .createdDate(new Date())
            .build();

    NDSCloudProviderAccessAWSIAMRoleView roleView = new NDSCloudProviderAccessAWSIAMRoleView(role);
    ArrayList<NDSCloudProviderAccessAWSIAMRoleView> arr = new ArrayList<>();
    arr.add(roleView);
    doReturn(arr).when(accessView).getNDSCloudProviderAccessAWSIAMRoleViews();

    final Date now = new Date();
    AWSKinesisDataStreamsConnection conn =
        new AWSKinesisDataStreamsConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "kinesisConn",
            now,
            now,
            new AWSConnectionConfig(roleArn),
            null,
            new ProxyInfo());

    // null networking.
    assertDoesNotThrow(() -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));

    // public networking.
    Access access = new Access();
    access.setType(Type.PUBLIC);
    conn.setNetworking(new NDSDataLakeStreamsNetworking(access));
    assertDoesNotThrow(() -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));

    access.setType(Type.VPC);
    SvcException exception =
        assertThrows(
            SvcException.class,
            () -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));
    assertEquals(NDSErrorCode.STREAM_INVALID_NETWORKING_ACCESS_TYPE, exception.getErrorCode());
    assertEquals(
        "Stream networking access type VPC is invalid for connection type AWSKinesisDataStreams",
        exception.getMessage());

    // PRIVATE_LINK w/o connectionId.
    access.setType(Type.PRIVATE_LINK);
    exception =
        assertThrows(
            SvcException.class,
            () -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));
    assertEquals(INVALID_ARGUMENT, exception.getErrorCode());
    assertEquals(
        "Invalid connectionId. PRIVATE_LINK access type must have a connectionId",
        exception.getMessage());

    doReturn(true)
        .when(_featureFlagSvc)
        .isFeatureFlagEnabled(FeatureFlag.STREAMS_AWS_PRIVATE_LINK, null, group);

    // PRIVATE_LINK w/ invalid connectionId w/o Kinesis PL feature flag.
    access.setConnectionId(new ObjectId());
    exception =
        assertThrows(
            SvcException.class,
            () -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));
    assertEquals(NDSErrorCode.STREAM_PRIVATE_LINK_NOT_FOUND, exception.getErrorCode());

    ObjectId connectionId = new ObjectId();
    StreamsPrivateLink mockPrivateLink = mock(StreamsPrivateLink.class);
    doReturn(Optional.of(mockPrivateLink)).when(_privateLinkDao).findByGroupIdAndId(any(), any());
    doReturn(StreamsPrivateLinkVendorType.KINESIS).when(mockPrivateLink).getVendor();

    // PRIVATE_LINK w/ valid connectionId w/o Kinesis PL feature flag.
    access.setConnectionId(connectionId);
    exception =
        assertThrows(
            SvcException.class,
            () -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));
    assertEquals(
        NDSErrorCode.STREAM_KINESIS_PRIVATE_LINK_IS_NOT_SUPPORTED, exception.getErrorCode());

    // PRIVATE_LINK w/ connectionId w/ feature flag.
    doReturn(true)
        .when(_featureFlagSvc)
        .isFeatureFlagEnabled(FeatureFlag.STREAMS_AWS_KINESIS_PRIVATE_LINK, null, group);
    assertDoesNotThrow(() -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));
  }

  @Test
  public void testValidateConnectionForS3() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    NDSCloudProviderAccessView accessView = mock(NDSCloudProviderAccessView.class);
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    final AppUser appUser = mock(AppUser.class);
    String roleArn = "arn:aws:iam::************:role/forKinesis";
    ObjectId roleId = new ObjectId();
    doReturn(accessView).when(_ndsUiSvc).getCloudProviderAccess(eq(groupId));

    final NDSCloudProviderAccessAWSIAMRole role =
        NDSCloudProviderAccessAWSIAMRole.builder()
            .roleId(roleId)
            .iamAssumedRoleArn(roleArn)
            .atlasAssumedRoleExternalId("externalId")
            .atlasAWSAccountArn("arn:aws:iam::************:root")
            .atlasAWSAccountId(new ObjectId())
            .featureUsages(List.of())
            .authorizedDate(new Date())
            .createdDate(new Date())
            .build();

    NDSCloudProviderAccessAWSIAMRoleView roleView = new NDSCloudProviderAccessAWSIAMRoleView(role);
    ArrayList<NDSCloudProviderAccessAWSIAMRoleView> arr = new ArrayList<>();
    arr.add(roleView);
    doReturn(arr).when(accessView).getNDSCloudProviderAccessAWSIAMRoleViews();

    final Date now = new Date();
    S3Connection conn =
        new S3Connection(
            new ObjectId(),
            mockedTenant.getId(),
            "S3Conn",
            now,
            now,
            new AWSS3ConnectionConfig(roleId.toHexString(), roleArn, null),
            null,
            new ProxyInfo());

    // null networking.
    assertDoesNotThrow(() -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));

    // public networking.
    Access access = new Access();
    access.setType(Type.PUBLIC);
    conn.setNetworking(new NDSDataLakeStreamsNetworking(access));
    assertDoesNotThrow(() -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));

    access.setType(Type.VPC);
    SvcException exception =
        assertThrows(
            SvcException.class,
            () -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));
    assertEquals(NDSErrorCode.INVALID_ARGUMENT, exception.getErrorCode());
    assertEquals(
        "Invalid networking type. S3 networking type can only be PUBLIC or PRIVATE_LINK",
        exception.getMessage());

    // PRIVATE_LINK w/o connectionId.
    access.setType(Type.PRIVATE_LINK);
    exception =
        assertThrows(
            SvcException.class,
            () -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));
    assertEquals(INVALID_ARGUMENT, exception.getErrorCode());
    assertEquals(
        "Invalid connectionId. PRIVATE_LINK access type must have a connectionId",
        exception.getMessage());

    doReturn(true)
        .when(_featureFlagSvc)
        .isFeatureFlagEnabled(FeatureFlag.STREAMS_AWS_PRIVATE_LINK, null, group);

    // PRIVATE_LINK w/ invalid connectionId w/o S3 PL feature flag.
    access.setConnectionId(new ObjectId());
    exception =
        assertThrows(
            SvcException.class,
            () -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));
    assertEquals(NDSErrorCode.STREAM_PRIVATE_LINK_NOT_FOUND, exception.getErrorCode());

    ObjectId connectionId = new ObjectId();
    StreamsPrivateLink mockPrivateLink = mock(StreamsPrivateLink.class);
    doReturn(Optional.of(mockPrivateLink)).when(_privateLinkDao).findByGroupIdAndId(any(), any());
    doReturn(StreamsPrivateLinkVendorType.S3).when(mockPrivateLink).getVendor();

    // PRIVATE_LINK w/ valid connectionId w/o S3 PL feature flag.
    access.setConnectionId(connectionId);
    exception =
        assertThrows(
            SvcException.class,
            () -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));
    assertEquals(NDSErrorCode.STREAM_S3_PRIVATE_LINK_IS_NOT_SUPPORTED, exception.getErrorCode());

    // PRIVATE_LINK w/ connectionId w/ feature flag.
    doReturn(true)
        .when(_featureFlagSvc)
        .isFeatureFlagEnabled(FeatureFlag.STREAMS_AWS_S3_PRIVATE_LINK, null, group);
    assertDoesNotThrow(() -> _streamsSvc.validateConnection(group, "fakeTenant", conn, appUser));
  }

  @Test
  public void testValidateAWSBucket() throws SvcException {
    final ObjectId groupId = ObjectId.get();
    NDSCloudProviderAccessView accessView = mock(NDSCloudProviderAccessView.class);
    AmazonS3 amazonS3Mock = mock(AmazonS3.class);
    String roleArn = "arn:aws:iam::************:role/forKinesis";
    ObjectId roleId = new ObjectId();
    doReturn(new NDSAWSTempCredentials("", "", "", new Date()))
        .when(_ndsUiSvc)
        .getAWSAssumeRoleTempCredentials(any(), any());
    doReturn(amazonS3Mock).when(_awsApiSvc).getS3Client(any(AWSCredentials.class), anyBoolean());
    doReturn(accessView).when(_ndsUiSvc).getCloudProviderAccess(eq(groupId));

    final NDSCloudProviderAccessAWSIAMRole role =
        NDSCloudProviderAccessAWSIAMRole.builder()
            .roleId(roleId)
            .iamAssumedRoleArn(roleArn)
            .atlasAssumedRoleExternalId("externalId")
            .atlasAWSAccountArn("arn:aws:iam::************:root")
            .atlasAWSAccountId(new ObjectId())
            .featureUsages(List.of())
            .authorizedDate(new Date())
            .createdDate(new Date())
            .build();
    NDSCloudProviderAccessAWSIAMRoleView roleView = new NDSCloudProviderAccessAWSIAMRoleView(role);
    ArrayList<NDSCloudProviderAccessAWSIAMRoleView> arr = new ArrayList<>();
    arr.add(roleView);
    doReturn(arr).when(accessView).getNDSCloudProviderAccessAWSIAMRoleViews();

    List<Bucket> buckets = new ArrayList<>();

    // with invalid bucket name
    ListBucketsPaginatedResult listBucketsPaginatedResult =
        new ListBucketsPaginatedResult().withBuckets(buckets);
    doReturn(listBucketsPaginatedResult)
        .when(amazonS3Mock)
        .listBuckets(any(ListBucketsPaginatedRequest.class));
    try {
      _streamsSvc.validateAWSBucket("test-bucket", groupId, roleArn, "tenant-name");
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(amazonS3Mock, times(1)).listBuckets(any(ListBucketsPaginatedRequest.class));
      assertEquals(NDSErrorCode.STREAM_AWS_BUCKET_NOT_AVAILABLE, ex.getErrorCode());
    }

    // with valid bucket name
    buckets.add(new Bucket("test-bucket"));
    listBucketsPaginatedResult = new ListBucketsPaginatedResult().withBuckets(buckets);
    doReturn(listBucketsPaginatedResult)
        .when(amazonS3Mock)
        .listBuckets(any(ListBucketsPaginatedRequest.class));
    assertDoesNotThrow(
        () -> _streamsSvc.validateAWSBucket("test-bucket", groupId, roleArn, "tenant-name"));

    // with AccessDenied exception
    AmazonServiceException ase = new AmazonServiceException("blah");
    ase.setErrorCode("AccessDenied");
    doThrow(ase).when(amazonS3Mock).listBuckets(any(ListBucketsPaginatedRequest.class));
    try {
      _streamsSvc.validateAWSBucket("test-bucket", groupId, roleArn, "tenant-name");
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(NDSErrorCode.STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED, ex.getErrorCode());
    }
  }

  @Test
  public void testCreatePrivateLinkConnection_S3FeatureFlagDisabled() {
    ApiStreamsPrivateLinkView testPlView =
        new ApiStreamsPrivateLinkView("S3", "AWS", "", "", null, "", null, "us-east-1", "");
    final Group group = mock(Group.class);
    doReturn(false)
        .when(_featureFlagSvc)
        .isFeatureFlagEnabled(FeatureFlag.STREAMS_AWS_S3_PRIVATE_LINK, null, group);
    try {
      _streamsSvc.createPrivateLinkConnection(testPlView, group, new ObjectId());
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.STREAM_S3_PRIVATE_LINK_IS_NOT_SUPPORTED, e.getErrorCode());
      assertEquals(
          NDSErrorCode.STREAM_S3_PRIVATE_LINK_IS_NOT_SUPPORTED.getMessage(), e.getMessage());
    }
  }

  @Test
  public void testIsPLVendorCompatibleWithConnectionType() {
    // Positive cases
    assertTrue(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.KINESIS, StreamsConnectionType.AWSKinesisDataStreams));
    assertTrue(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.S3, StreamsConnectionType.S3));
    assertTrue(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.GENERIC, StreamsConnectionType.Kafka));
    assertTrue(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.EVENTHUB, StreamsConnectionType.Kafka));
    assertTrue(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.MSK, StreamsConnectionType.Kafka));
    assertTrue(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.CONFLUENT, StreamsConnectionType.Kafka));

    // Negative cases
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.S3, StreamsConnectionType.AWSKinesisDataStreams));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.GENERIC, StreamsConnectionType.AWSKinesisDataStreams));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.EVENTHUB, StreamsConnectionType.AWSKinesisDataStreams));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.MSK, StreamsConnectionType.AWSKinesisDataStreams));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.CONFLUENT, StreamsConnectionType.AWSKinesisDataStreams));

    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.KINESIS, StreamsConnectionType.S3));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.GENERIC, StreamsConnectionType.S3));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.EVENTHUB, StreamsConnectionType.S3));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.MSK, StreamsConnectionType.S3));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.CONFLUENT, StreamsConnectionType.S3));

    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.KINESIS, StreamsConnectionType.Kafka));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.S3, StreamsConnectionType.Kafka));

    // Cases where connection type does not support private link
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.S3, StreamsConnectionType.Https));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.KINESIS, StreamsConnectionType.Https));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.GENERIC, StreamsConnectionType.Https));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.EVENTHUB, StreamsConnectionType.Https));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.MSK, StreamsConnectionType.Https));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.CONFLUENT, StreamsConnectionType.Https));

    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.S3, StreamsConnectionType.Cluster));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.KINESIS, StreamsConnectionType.Cluster));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.GENERIC, StreamsConnectionType.Cluster));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.EVENTHUB, StreamsConnectionType.Cluster));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.MSK, StreamsConnectionType.Cluster));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.CONFLUENT, StreamsConnectionType.Cluster));

    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.S3, StreamsConnectionType.Sample));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.KINESIS, StreamsConnectionType.Sample));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.GENERIC, StreamsConnectionType.Sample));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.EVENTHUB, StreamsConnectionType.Sample));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.MSK, StreamsConnectionType.Sample));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.CONFLUENT, StreamsConnectionType.Sample));

    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.S3, StreamsConnectionType.AWSLambda));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.KINESIS, StreamsConnectionType.AWSLambda));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.GENERIC, StreamsConnectionType.AWSLambda));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.EVENTHUB, StreamsConnectionType.AWSLambda));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.MSK, StreamsConnectionType.AWSLambda));
    assertFalse(
        StreamsSvc.IsPLVendorCompatibleWithConnectionType(
            StreamsPrivateLinkVendorType.CONFLUENT, StreamsConnectionType.AWSLambda));
  }

  @Test
  public void testCreateConnectionForAWSLambda_missingAWSConfig() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final AWSLambdaConnectionView lambdaConnectionView =
        new AWSLambdaConnectionView("unitTestAWS", null);
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.createConnection(lambdaConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, ex.getErrorCode());
      assertEquals("Invalid AWS connection config", ex.getMessage());
    }
  }

  @Test
  public void testCreateConnectionForAWSLambda_roleArnNotFound() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    doReturn(new NDSAWSTempCredentials("", "", "", new Date()))
        .when(_ndsUiSvc)
        .getAWSAssumeRoleTempCredentials(any(), any());

    String testRoleArn = "arn:aws:iam::************:root/forLambdaFunction";
    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(List.of(), null, null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final AWSLambdaConnectionView lambdaConnectionView =
        new AWSLambdaConnectionView("unitTestAWS", new AWSConnectionConfig(testRoleArn));
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.createConnection(lambdaConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_ndsUiSvc, times(1)).getCloudProviderAccess(any());
      assertEquals(CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND, ex.getErrorCode());
    }
  }

  @Test
  public void testCreateConnectionForAWSLambda_invalidGCPCloudProvider() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    doReturn(Optional.of(mockedTenant)).when(_streamsTenantDao).findByGroupAndName(any(), any());
    ObjectId streamsTenantId = new ObjectId();
    doReturn(streamsTenantId).when(mockedTenant).getId();
    final NDSDataLakeTenant ndsDataLakeTenant = mock(NDSDataLakeTenant.class);
    doReturn(Optional.of(ndsDataLakeTenant))
        .when(_dataLakeTenantSvc)
        .findByTenantId(streamsTenantId);
    doReturn(mock(NDSDataLakeTenant.NDSDataLakeDataProcessRegion.class))
        .when(ndsDataLakeTenant)
        .getDataProcessRegion();
    final AWSRegionName regionName = mock(AWSRegionName.class);
    when(ndsDataLakeTenant.getDataProcessRegion().getDataProcessRegionAsRegionName())
        .thenReturn(Optional.of(regionName));
    when(ndsDataLakeTenant.getCloudProvider()).thenReturn(CloudProvider.GCP);

    ObjectId testRoleId = new ObjectId();
    String testRoleArn = "arn:aws:iam::************:root/forLambdaFunction";
    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    testRoleId, testRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final AWSLambdaConnectionView lambdaConnectionView =
        new AWSLambdaConnectionView("unitTestAWS", new AWSConnectionConfig(testRoleArn));
    try {
      _streamsSvc.createConnection(
          lambdaConnectionView, "testConnection", mock(Group.class), mock(AuditInfo.class), null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(STREAM_AWSLAMBDA_NOT_SUPPORTED_IN_CLOUD_PROVIDER, ex.getErrorCode());
      assertEquals(
          STREAM_AWSLAMBDA_NOT_SUPPORTED_IN_CLOUD_PROVIDER.formatMessage(
              CloudProvider.GCP.getDescription()),
          ex.getMessage());
    }
  }

  @Test
  public void testCreateConnectionForAWSLambda_invalidNONECloudProvider() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    doReturn(Optional.of(mockedTenant)).when(_streamsTenantDao).findByGroupAndName(any(), any());
    ObjectId streamsTenantId = new ObjectId();
    doReturn(streamsTenantId).when(mockedTenant).getId();
    final NDSDataLakeTenant ndsDataLakeTenant = mock(NDSDataLakeTenant.class);
    doReturn(Optional.of(ndsDataLakeTenant))
        .when(_dataLakeTenantSvc)
        .findByTenantId(streamsTenantId);
    doReturn(mock(NDSDataLakeTenant.NDSDataLakeDataProcessRegion.class))
        .when(ndsDataLakeTenant)
        .getDataProcessRegion();
    final AWSRegionName regionName = mock(AWSRegionName.class);
    when(ndsDataLakeTenant.getDataProcessRegion().getDataProcessRegionAsRegionName())
        .thenReturn(Optional.of(regionName));
    when(ndsDataLakeTenant.getCloudProvider()).thenReturn(CloudProvider.NONE);

    ObjectId testRoleId = new ObjectId();
    String testRoleArn = "arn:aws:iam::************:root/forLambdaFunction";
    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    testRoleId, testRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final AWSLambdaConnectionView lambdaConnectionView =
        new AWSLambdaConnectionView("unitTestAWS", new AWSConnectionConfig(testRoleArn));
    try {
      _streamsSvc.createConnection(
          lambdaConnectionView, "testConnection", mock(Group.class), mock(AuditInfo.class), null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(STREAM_AWSLAMBDA_NOT_SUPPORTED_IN_CLOUD_PROVIDER, ex.getErrorCode());
      assertEquals(
          STREAM_AWSLAMBDA_NOT_SUPPORTED_IN_CLOUD_PROVIDER.formatMessage(
              CloudProvider.NONE.getDescription()),
          ex.getMessage());
    }
  }

  @Test
  public void testCreateConnectionForAWSLambda_validConnection() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    ObjectId testRoleId = new ObjectId();
    String testRoleArn = "arn:aws:iam::************:root/forLambdaFunction";
    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    testRoleId, testRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final AWSLambdaConnectionView lambdaConnectionView =
        new AWSLambdaConnectionView("unitTestAWS", new AWSConnectionConfig(testRoleArn));
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    StreamsConnectionView actualConnectionView =
        _streamsSvc.createConnection(
            lambdaConnectionView, "testConnection", group, auditInfo, null);
    AWSLambdaConnectionView actualLambdaConnectionView =
        (AWSLambdaConnectionView) actualConnectionView;
    assertEquals(testRoleId.toHexString(), actualLambdaConnectionView.getAwsConfig().getRoleId());
    assertEquals(testRoleArn, actualLambdaConnectionView.getAwsConfig().getRoleArn());
  }

  @Test
  public void testCreateConnectionForAWSLambda_validConnectionWithPendingRole()
      throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    ObjectId testRoleId = new ObjectId();
    String testRoleArn = "arn:aws:iam::************:root/forLambdaFunction";
    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    new ObjectId(), null, null, null, null, null, null),
                new NDSCloudProviderAccessAWSIAMRoleView(
                    testRoleId, testRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final AWSLambdaConnectionView lambdaConnectionView =
        new AWSLambdaConnectionView("unitTestAWS", new AWSConnectionConfig(testRoleArn));
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    StreamsConnectionView actualConnectionView =
        _streamsSvc.createConnection(
            lambdaConnectionView, "testConnection", group, auditInfo, null);
    AWSLambdaConnectionView actualLambdaConnectionView =
        (AWSLambdaConnectionView) actualConnectionView;
    assertEquals(testRoleId.toHexString(), actualLambdaConnectionView.getAwsConfig().getRoleId());
    assertEquals(testRoleArn, actualLambdaConnectionView.getAwsConfig().getRoleArn());
  }

  @Test
  public void testCreateConnectionForAWSLambda_deleteSavedConnection() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    doReturn(Optional.of(mockedTenant)).when(_streamsTenantDao).findByGroupAndName(any(), any());
    ObjectId streamsTenantId = new ObjectId();
    doReturn(streamsTenantId).when(mockedTenant).getId();
    final NDSDataLakeTenant ndsDataLakeTenant = mock(NDSDataLakeTenant.class);
    doReturn(Optional.of(ndsDataLakeTenant))
        .when(_dataLakeTenantSvc)
        .findByTenantId(streamsTenantId);
    doReturn(mock(NDSDataLakeTenant.NDSDataLakeDataProcessRegion.class))
        .when(ndsDataLakeTenant)
        .getDataProcessRegion();
    final AWSRegionName regionName = mock(AWSRegionName.class);
    when(ndsDataLakeTenant.getDataProcessRegion().getDataProcessRegionAsRegionName())
        .thenReturn(Optional.of(regionName));
    when(ndsDataLakeTenant.getCloudProvider()).thenReturn(CloudProvider.AWS);

    doReturn(CloudProvider.AWS).when(regionName).getProvider();

    doThrow(new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID))
        .when(_dataLakeTenantSvc)
        .getStorageConfig(any());

    String testRoleArn = "arn:aws:iam::************:root/forLambdaFunction";
    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    new ObjectId(), testRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final AWSLambdaConnectionView lambdaConnectionView =
        new AWSLambdaConnectionView("unitTestAWS", new AWSConnectionConfig(testRoleArn));
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.createConnection(lambdaConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_awsLambdaConnectionDao, times(1)).save(any());
      verify(_awsLambdaConnectionDao, times(1)).delete(streamsTenantId, "unitTestAWS");
    }
  }

  @Test
  public void testCreateConnectionForAWSLambda_notAuthorizedRole() throws SvcException {
    doThrow(new DataLakeAdminApiException(CommonErrorCode.SERVER_ERROR))
        .when(_dataLakeTenantSvc)
        .getStorageConfig(any());

    final StreamsTenant mockedTenant = mock(StreamsTenant.class);

    doReturn(Optional.of(mockedTenant)).when(_streamsTenantDao).findByGroupAndName(any(), any());

    doReturn(new ObjectId()).when(mockedTenant).getId();

    doThrow(new SvcException(CLOUD_PROVIDER_ACCESS_ROLE_NOT_AUTHORIZED, ""))
        .when(_ndsUiSvc)
        .getAWSAssumeRoleTempCredentials(any(), any());

    String testRoleArn = "arn:aws:iam::************:root/forLambdaFunction";
    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    new ObjectId(), testRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());
    final AWSLambdaConnectionView lambdaConnectionView =
        new AWSLambdaConnectionView("unitTestAWS", new AWSConnectionConfig(testRoleArn));
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.createConnection(lambdaConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_ndsUiSvc, times(1)).getAWSAssumeRoleTempCredentials(any(), any());
      assertEquals(STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED, ex.getErrorCode());
    }
  }

  @Test
  public void testUpdateConnectionForAWSLambda_validConnection() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    String testConnectionName = "unitTestAWS";

    Document existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", "awslambda");

    Document testStore = new Document("stores", List.of(existingConnection));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());

    doReturn("testTenant").when(mockedTenant).getName();

    final ClusterConnection clusterConnection = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.AWSLambda).when(clusterConnection).getConnectionType();
    doReturn(Optional.of(clusterConnection))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    ObjectId existingRoleId = new ObjectId();
    String existingRoleArn = "arn:aws:iam::************:root/lambda1";
    ObjectId newRoleId = new ObjectId();
    String newRoleArn = "arn:aws:iam::************:root/lambda2";
    AWSLambdaConnection existingLambdaConnection =
        new AWSLambdaConnection(
            new ObjectId(),
            mockedTenant.getId(),
            testConnectionName,
            new Date(),
            new Date(),
            new AWSConnectionConfig(existingRoleId.toHexString(), null));

    doReturn(Optional.of(existingLambdaConnection))
        .when(_awsLambdaConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    existingRoleId, existingRoleArn, null, null, null, null, null),
                new NDSCloudProviderAccessAWSIAMRoleView(
                    newRoleId, newRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final AWSLambdaConnectionView newlambdaConnectionView =
        new AWSLambdaConnectionView(testConnectionName, new AWSConnectionConfig(newRoleArn));
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    StreamsConnectionView newConnectionView =
        _streamsSvc.updateConnection(
            newlambdaConnectionView, testConnectionName, "testTenant", group, auditInfo, null);
    AWSLambdaConnectionView actualLambdaConnectionView =
        (AWSLambdaConnectionView) newConnectionView;
    assertEquals(newRoleId.toHexString(), actualLambdaConnectionView.getAwsConfig().getRoleId());
    assertEquals(newRoleArn, actualLambdaConnectionView.getAwsConfig().getRoleArn());
  }

  @Test
  public void testUpdateConnectionForAWSLambda_roleArnNotFound() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    String testConnectionName = "unitTestAWS";

    Document existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", "awslambda");

    Document testStore = new Document("stores", List.of(existingConnection));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());
    doReturn("testTenant").when(mockedTenant).getName();

    final ClusterConnection clusterConnection = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.AWSLambda).when(clusterConnection).getConnectionType();

    doReturn(Optional.of(clusterConnection))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    ObjectId existingRoleId = new ObjectId();
    String newRoleArn = "arn:aws:iam::************:root/lambda2";
    AWSLambdaConnection existingLambdaConnection =
        new AWSLambdaConnection(
            new ObjectId(),
            mockedTenant.getId(),
            testConnectionName,
            new Date(),
            new Date(),
            new AWSConnectionConfig(existingRoleId.toHexString(), null));

    doReturn(Optional.of(existingLambdaConnection))
        .when(_awsLambdaConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(List.of(), null, null);
    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final AWSLambdaConnectionView newLambdaConnectionView =
        new AWSLambdaConnectionView(testConnectionName, new AWSConnectionConfig(newRoleArn));
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.updateConnection(
          newLambdaConnectionView, testConnectionName, "testTenant", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_ndsUiSvc, times(1)).getCloudProviderAccess(any());
      assertEquals(CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND, ex.getErrorCode());
    }
  }

  @Test
  public void testUpdateConnectionForAWSLambda_notAuthorizedRole() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    String testConnectionName = "unitTestAWS";

    Document existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", "awslambda");

    Document testStore = new Document("stores", List.of(existingConnection));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());
    doReturn("testTenant").when(mockedTenant).getName();

    final ClusterConnection clusterConnection = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.AWSLambda).when(clusterConnection).getConnectionType();

    doReturn(Optional.of(clusterConnection))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    ObjectId testRoleId1 = new ObjectId();
    String testRoleArn2 = "arn:aws:iam::************:root/lambda2";
    AWSLambdaConnection existingLambdaConnection =
        new AWSLambdaConnection(
            new ObjectId(),
            mockedTenant.getId(),
            testConnectionName,
            new Date(),
            new Date(),
            new AWSConnectionConfig(testRoleId1.toHexString(), null));

    doReturn(Optional.of(existingLambdaConnection))
        .when(_awsLambdaConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    testRoleId1,
                    "arn:aws:iam::************:root/lambda2",
                    null,
                    null,
                    null,
                    null,
                    null),
                new NDSCloudProviderAccessAWSIAMRoleView(
                    new ObjectId(), testRoleArn2, null, null, null, null, null)),
            null,
            null);
    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final AWSLambdaConnectionView lambdaConnectionView =
        new AWSLambdaConnectionView(testConnectionName, new AWSConnectionConfig(testRoleArn2));
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    doThrow(new SvcException(CLOUD_PROVIDER_ACCESS_ROLE_NOT_AUTHORIZED, ""))
        .when(_ndsUiSvc)
        .getAWSAssumeRoleTempCredentials(any(), any());

    try {
      _streamsSvc.updateConnection(
          lambdaConnectionView, testConnectionName, "testTenant", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_ndsUiSvc, times(1)).getCloudProviderAccess(any());
      verify(_ndsUiSvc, times(1)).getAWSAssumeRoleTempCredentials(any(), any());
      assertEquals(STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED, ex.getErrorCode());
    }
  }

  @Test
  public void testGetAwsAssumeRoleTempCredentials_nonAWSConnectionType() {
    ObjectId testGroupId = new ObjectId();
    Group testGroup = new Group();
    testGroup.setId(testGroupId);
    ObjectId testTenantId = new ObjectId();
    String connectionName = "nonAWSConnectionType";

    try {
      _streamsSvc.getAwsAssumeRoleTempCredentials(
          testGroup, testTenantId, connectionName, StreamsConnectionType.Kafka);
      fail("trying to assumeRole for a non-aws connection type should fail");
    } catch (SvcException ex) {
      assertEquals(STREAM_NOT_AWS_CONNECTION, ex.getErrorCode());
    }
  }

  @Test
  public void testGetAwsAssumeRoleTempCredentials_validLambdaCredentials() throws SvcException {
    ObjectId testGroupId = new ObjectId();
    Group testGroup = new Group();
    testGroup.setId(testGroupId);
    ObjectId testTenantId = new ObjectId();
    String connectionName = "aws";
    ObjectId testRoleId = new ObjectId();
    AWSLambdaConnection awsLambdaConnection =
        new AWSLambdaConnection(
            new ObjectId(),
            testTenantId,
            connectionName,
            null,
            null,
            new AWSConnectionConfig(
                testRoleId.toHexString(), "arn:aws:iam::************:root/forLambdaFunction"));
    doReturn(Optional.of(awsLambdaConnection))
        .when(_awsLambdaConnectionDao)
        .fetchByTenantIdAndName(testTenantId, connectionName);
    Date testDate = new Date();
    NDSAWSTempCredentials returnedTempCredentials =
        new NDSAWSTempCredentials("key", "secret", "token", testDate);
    doReturn(returnedTempCredentials)
        .when(_ndsUiSvc)
        .getAWSAssumeRoleTempCredentials(
            testGroupId, new ObjectId(awsLambdaConnection.getAwsConnection().getRoleId()));
    NDSAWSTempCredentials actualTempCredentials =
        _streamsSvc.getAwsAssumeRoleTempCredentials(
            testGroup, testTenantId, connectionName, StreamsConnectionType.AWSLambda);
    assertEquals(
        new NDSAWSTempCredentials("key", "secret", "token", testDate), actualTempCredentials);
  }

  @Test
  public void testGetAwsAssumeRoleTempCredentials_validAWSS3Credentials() throws SvcException {
    ObjectId testGroupId = new ObjectId();
    Group testGroup = new Group();
    testGroup.setId(testGroupId);
    ObjectId testTenantId = new ObjectId();
    String connectionName = "aws";
    String roleARN = "arn:aws:iam::************:root/forS3";
    String roleId = new ObjectId().toHexString();
    S3Connection s3Connection =
        new S3Connection(
            new ObjectId(),
            testTenantId,
            connectionName,
            null,
            null,
            new AWSS3ConnectionConfig(roleId, roleARN, null),
            null,
            null);
    doReturn(Optional.of(s3Connection))
        .when(_s3ConnectionDao)
        .fetchByTenantIdAndName(testTenantId, connectionName);

    Date testDate = new Date();
    NDSAWSTempCredentials returnedTempCredentials =
        new NDSAWSTempCredentials("key", "secret", "token", testDate);
    doReturn(returnedTempCredentials)
        .when(_ndsUiSvc)
        .getAWSAssumeRoleTempCredentials(
            testGroupId, new ObjectId(s3Connection.getAWS().getRoleId()));
    NDSAWSTempCredentials actualTempCredentials =
        _streamsSvc.getAwsAssumeRoleTempCredentials(
            testGroup, testTenantId, connectionName, StreamsConnectionType.S3);
    assertEquals(
        new NDSAWSTempCredentials("key", "secret", "token", testDate), actualTempCredentials);
  }

  @Test
  public void testGetAwsAssumeRoleTempCredentials_validKinesisCredentials() throws SvcException {
    ObjectId testGroupId = new ObjectId();
    Group testGroup = new Group();
    testGroup.setId(testGroupId);
    ObjectId testTenantId = new ObjectId();
    String connectionName = "aws";
    ObjectId testRoleId = new ObjectId();
    AWSKinesisDataStreamsConnection AWSKinesisDataStreamsConnection =
        new AWSKinesisDataStreamsConnection(
            new ObjectId(),
            testTenantId,
            connectionName,
            null,
            null,
            new AWSConnectionConfig(
                testRoleId.toHexString(), "arn:aws:iam::************:root/forKinesis"),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());
    doReturn(Optional.of(AWSKinesisDataStreamsConnection))
        .when(_kinesisConnectionDao)
        .fetchByTenantIdAndName(testTenantId, connectionName);
    Date testDate = new Date();
    NDSAWSTempCredentials returnedTempCredentials =
        new NDSAWSTempCredentials("key", "secret", "token", testDate);
    doReturn(returnedTempCredentials)
        .when(_ndsUiSvc)
        .getAWSAssumeRoleTempCredentials(
            testGroupId, new ObjectId(AWSKinesisDataStreamsConnection.getAWS().getRoleId()));
    NDSAWSTempCredentials actualTempCredentials =
        _streamsSvc.getAwsAssumeRoleTempCredentials(
            testGroup, testTenantId, connectionName, StreamsConnectionType.AWSKinesisDataStreams);
    assertEquals(
        new NDSAWSTempCredentials("key", "secret", "token", testDate), actualTempCredentials);
  }

  @Test
  public void testGetAwsAssumeRoleTempCredentials_connectionNotFound() {
    ObjectId testGroupId = new ObjectId();
    Group testGroup = new Group();
    testGroup.setId(testGroupId);
    ObjectId testTenantId = new ObjectId();
    String connectionName = "aws";
    doReturn(Optional.empty())
        .when(_awsLambdaConnectionDao)
        .fetchByTenantIdAndName(testTenantId, connectionName);
    try {
      _streamsSvc.getAwsAssumeRoleTempCredentials(
          testGroup, testTenantId, connectionName, StreamsConnectionType.AWSLambda);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(STREAM_CONNECTION_NOT_FOUND, ex.getErrorCode());
    }
  }

  @Test
  public void testCreateConnectionForS3_missingAWSConfig() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final StreamsS3ConnectionView s3ConnectionView =
        new StreamsS3ConnectionView("unitTestAWS", null, true);
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.createConnection(s3ConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, ex.getErrorCode());
      assertEquals("Invalid AWS connection config", ex.getMessage());
    }
  }

  @Test
  public void testCreateConnectionForS3_roleArnNotFound() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    doReturn(new NDSAWSTempCredentials("", "", "", new Date()))
        .when(_ndsUiSvc)
        .getAWSAssumeRoleTempCredentials(any(), any());

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(List.of(), null, null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final StreamsConnectionView s3ConnectionView =
        new StreamsS3ConnectionView(
            "unitTestAWS",
            new AWSS3ConnectionConfig(null, "arn:aws:iam::************:root/forS3", "mytestbucket"),
            true);
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.createConnection(s3ConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_ndsUiSvc, times(1)).getCloudProviderAccess(any());
      assertEquals(CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND, ex.getErrorCode());
    }
  }

  private AWSConnectionConfig setupMocksForAWSCredentials() throws SvcException {
    ObjectId testRoleId = new ObjectId();
    String testRoleArn = "arn:aws:iam::************:root/s3Role";
    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    testRoleId, testRoleArn, null, null, null, null, null)),
            null,
            null);
    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    NDSAWSTempCredentials mockNDSTempCredentials = mock(NDSAWSTempCredentials.class);
    doReturn("abc").when(mockNDSTempCredentials).getAccessKey();
    doReturn("def").when(mockNDSTempCredentials).getAccessSecret();
    doReturn(mockNDSTempCredentials).when(_ndsUiSvc).getAWSAssumeRoleTempCredentials(any(), any());

    return new AWSConnectionConfig(testRoleId.toHexString(), testRoleArn);
  }

  @Test
  public void testCreateConnectionForS3_listBucketsThrowsUnauthrorizedException()
      throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);
    AWSConnectionConfig testCreds = setupMocksForAWSCredentials();

    String testBucket = "myTestBucket";
    final StreamsS3ConnectionView s3ConnectionView =
        new StreamsS3ConnectionView(
            "unitTestAWS",
            new AWSS3ConnectionConfig(null, testCreds.getRoleArn(), testBucket),
            true);
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    // make s3 client throws a AccessDenied exception when calling listBucket.

    AmazonS3 mockClient = mock(AmazonS3.class);
    AmazonServiceException ase = new AmazonServiceException("foo");
    ase.setErrorCode("AccessDenied");
    doThrow(ase).when(mockClient).listBuckets(any(ListBucketsPaginatedRequest.class));
    doReturn(mockClient).when(_awsApiSvc).getS3Client(any(AWSCredentials.class), anyBoolean());

    try {
      _streamsSvc.createConnection(s3ConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(mockClient, times(1)).listBuckets(any(ListBucketsPaginatedRequest.class));
      assertEquals(NDSErrorCode.STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED, ex.getErrorCode());
    }
  }

  @Test
  public void testCreateConnectionForS3_noBucketsMatchTestBucket_0results() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);
    AWSConnectionConfig testCreds = setupMocksForAWSCredentials();

    String testBucket = "myTestBucket";
    final StreamsS3ConnectionView s3ConnectionView =
        new StreamsS3ConnectionView(
            "unitTestAWS",
            new AWSS3ConnectionConfig(null, testCreds.getRoleArn(), testBucket),
            true);
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    // make s3 client return 0 buckets.
    ListBucketsPaginatedResult mockedResult = mock(ListBucketsPaginatedResult.class);
    when(mockedResult.getBuckets()).thenReturn(List.of());
    AmazonS3 mockClient = mock(AmazonS3.class);
    doReturn(mockedResult).when(mockClient).listBuckets(any(ListBucketsPaginatedRequest.class));
    doReturn(mockClient).when(_awsApiSvc).getS3Client(any(AWSCredentials.class), anyBoolean());

    try {
      _streamsSvc.createConnection(s3ConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(mockClient, times(1)).listBuckets(any(ListBucketsPaginatedRequest.class));
      assertEquals(NDSErrorCode.STREAM_AWS_BUCKET_NOT_AVAILABLE, ex.getErrorCode());
    }
  }

  @Test
  public void testCreateConnectionForS3_noBucketsMatchTestBucket_10results() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);
    AWSConnectionConfig testCreds = setupMocksForAWSCredentials();

    String testBucket = "myTestBucket";
    final StreamsS3ConnectionView s3ConnectionView =
        new StreamsS3ConnectionView(
            "unitTestAWS",
            new AWSS3ConnectionConfig(null, testCreds.getRoleArn(), testBucket),
            true);
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    AmazonS3 mockClient = mock(AmazonS3.class);
    doReturn(mockClient).when(_awsApiSvc).getS3Client(any(AWSCredentials.class), anyBoolean());

    // make s3 client make 2 paginated requests. none of the responses will contain a bucket
    // matching the test bucket.
    ListBucketsPaginatedResult mockedResult = mock(ListBucketsPaginatedResult.class);
    doReturn(mockedResult).when(mockClient).listBuckets(any(ListBucketsPaginatedRequest.class));

    Bucket bucket = mock(Bucket.class);
    doReturn("bar").when(bucket).getName();
    List<Bucket> mockedBuckets = List.of(bucket);
    when(mockedResult.getContinuationToken()).thenReturn("foo").thenReturn(null);
    when(mockedResult.getBuckets()).thenReturn(mockedBuckets).thenReturn(mockedBuckets);

    try {
      _streamsSvc.createConnection(s3ConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(mockClient, times(2)).listBuckets(any(ListBucketsPaginatedRequest.class));
      assertEquals(NDSErrorCode.STREAM_AWS_BUCKET_NOT_AVAILABLE, ex.getErrorCode());
    }
  }

  @Test
  public void testCreateConnectionForS3_validConnection_noTestBucket() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);
    AWSConnectionConfig testCreds = setupMocksForAWSCredentials();

    final StreamsS3ConnectionView s3ConnectionView =
        new StreamsS3ConnectionView(
            "unitTestAWS", new AWSS3ConnectionConfig(null, testCreds.getRoleArn(), null), true);
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    AmazonS3 mockClient = mock(AmazonS3.class);
    doReturn(mockClient).when(_awsApiSvc).getS3Client(any(AWSCredentials.class), anyBoolean());

    StreamsConnectionView actualConnectionView =
        _streamsSvc.createConnection(s3ConnectionView, "testConnection", group, auditInfo, null);
    StreamsS3ConnectionView actualS3ConnectionView = (StreamsS3ConnectionView) actualConnectionView;

    assertEquals(testCreds.getRoleId(), actualS3ConnectionView.getAwsConfig().getRoleId());
    assertEquals(testCreds.getRoleArn(), actualS3ConnectionView.getAwsConfig().getRoleArn());
    verify(mockClient, times(0)).listBuckets(any(ListBucketsPaginatedRequest.class));
  }

  @Test
  public void testCreateConnectionForS3_validConnection_withTestBucket() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);
    AWSConnectionConfig testCreds = setupMocksForAWSCredentials();

    String testBucket = "myTestBucket";
    final StreamsS3ConnectionView s3ConnectionView =
        new StreamsS3ConnectionView(
            "unitTestAWS",
            new AWSS3ConnectionConfig(null, testCreds.getRoleArn(), testBucket),
            true);
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    AmazonS3 mockClient = mock(AmazonS3.class);
    doReturn(mockClient).when(_awsApiSvc).getS3Client(any(AWSCredentials.class), anyBoolean());

    ListBucketsPaginatedResult mockedResult = mock(ListBucketsPaginatedResult.class);
    doReturn(mockedResult).when(mockClient).listBuckets(any(ListBucketsPaginatedRequest.class));

    // make s3 client make 2 paginated requests. the last bucket in the 2nd request matches the test
    // bucket.
    Bucket nonMatchingBucket = mock(Bucket.class);
    doReturn("foo").when(nonMatchingBucket).getName();
    List<Bucket> nonMatchingBuckets = List.of(nonMatchingBucket);
    Bucket matchingBucket = mock(Bucket.class);
    doReturn(testBucket).when(matchingBucket).getName();
    List<Bucket> matchingBuckets = List.of(nonMatchingBucket, matchingBucket);
    when(mockedResult.getContinuationToken()).thenReturn("foo").thenReturn(null);
    when(mockedResult.getBuckets()).thenReturn(nonMatchingBuckets).thenReturn(matchingBuckets);

    StreamsConnectionView actualConnectionView =
        _streamsSvc.createConnection(s3ConnectionView, "testConnection", group, auditInfo, null);
    StreamsS3ConnectionView actualS3ConnectionView = (StreamsS3ConnectionView) actualConnectionView;

    assertEquals(testCreds.getRoleId(), actualS3ConnectionView.getAwsConfig().getRoleId());
    assertEquals(testCreds.getRoleArn(), actualS3ConnectionView.getAwsConfig().getRoleArn());
    verify(mockClient, times(2)).listBuckets(any(ListBucketsPaginatedRequest.class));
  }

  @Test
  public void testCreateConnectionForKinesis_missingAWSConfig() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final StreamsAWSKinesisDataStreamsConnectionView kinesisConnectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            "unitTestAWS",
            null,
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.createConnection(kinesisConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(NDSErrorCode.INVALID_ARGUMENT, ex.getErrorCode());
      assertEquals("Invalid AWS connection config", ex.getMessage());
    }
  }

  @Test
  public void testCreateConnectionForKinesis_roleArnNotFound() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    String testConnectionName = "unitTestAWS";

    Document existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", ProviderValues.AWS_KINESIS_DATA_STREAMS);

    Document testStore = new Document("stores", List.of(existingConnection));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());
    doReturn("testTenant").when(mockedTenant).getName();

    String newRoleArn = "arn:aws:iam::************:root/kinesis";

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    new ObjectId(),
                    "arn:aws:iam::************:root/some-arn",
                    null,
                    null,
                    null,
                    null,
                    null)),
            null,
            null);
    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final StreamsAWSKinesisDataStreamsConnectionView connectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            testConnectionName,
            new AWSConnectionConfig(newRoleArn),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.createConnection(connectionView, "testTenant", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_ndsUiSvc, times(1)).getCloudProviderAccess(any());
      assertEquals(CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND, ex.getErrorCode());
    }
  }

  @Test
  public void testCreateConnectionForKinesis_validConnection() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    String testConnectionName = "unitTestAWS";

    Document existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", ProviderValues.AWS_KINESIS_DATA_STREAMS);

    Document testStore = new Document("stores", List.of(existingConnection));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());

    doReturn("testTenant").when(mockedTenant).getName();

    final ClusterConnection clusterConnection = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.AWSKinesisDataStreams)
        .when(clusterConnection)
        .getConnectionType();
    doReturn(Optional.of(clusterConnection))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    ObjectId newRoleId = new ObjectId();
    String newRoleArn = "arn:aws:iam::************:root/kinesis";

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    new ObjectId(),
                    "arn:aws:iam::************:root/some-arn",
                    null,
                    null,
                    null,
                    null,
                    null),
                new NDSCloudProviderAccessAWSIAMRoleView(
                    newRoleId, newRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final StreamsAWSKinesisDataStreamsConnectionView newKinesisConnectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            testConnectionName,
            new AWSConnectionConfig(newRoleArn),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    StreamsConnectionView actualConnectionView =
        _streamsSvc.createConnection(
            newKinesisConnectionView, "testTenant", group, auditInfo, null);
    StreamsAWSKinesisDataStreamsConnectionView actualKinesisConnectionView =
        (StreamsAWSKinesisDataStreamsConnectionView) actualConnectionView;
    assertEquals(newRoleId.toHexString(), actualKinesisConnectionView.getAwsConfig().getRoleId());
    assertEquals(newRoleArn, actualKinesisConnectionView.getAwsConfig().getRoleArn());
    assertEquals(
        newKinesisConnectionView.getNetworking(), actualKinesisConnectionView.getNetworking());
  }

  @Test
  public void testCreateConnectionForKinesis_validConnectionWithPendingRole() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    ObjectId testRoleId = new ObjectId();
    String testRoleArn = "arn:aws:iam::************:root/forKinesis";
    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    new ObjectId(), null, null, null, null, null, null),
                new NDSCloudProviderAccessAWSIAMRoleView(
                    testRoleId, testRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final StreamsAWSKinesisDataStreamsConnectionView kinesisConnectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            "unitTestAWS",
            new AWSConnectionConfig(testRoleArn),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    StreamsConnectionView actualConnectionView =
        _streamsSvc.createConnection(
            kinesisConnectionView, "testConnection", group, auditInfo, null);
    StreamsAWSKinesisDataStreamsConnectionView actualKinesisConnectionView =
        (StreamsAWSKinesisDataStreamsConnectionView) actualConnectionView;
    assertEquals(testRoleId.toHexString(), actualKinesisConnectionView.getAwsConfig().getRoleId());
    assertEquals(testRoleArn, actualKinesisConnectionView.getAwsConfig().getRoleArn());
    assertEquals(
        kinesisConnectionView.getNetworking(), actualKinesisConnectionView.getNetworking());
  }

  @Test
  public void testCreateConnectionForKinesis_deleteSavedConnection() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    doReturn(Optional.of(mockedTenant)).when(_streamsTenantDao).findByGroupAndName(any(), any());
    ObjectId streamsTenantId = new ObjectId();
    doReturn(streamsTenantId).when(mockedTenant).getId();
    final NDSDataLakeTenant ndsDataLakeTenant = mock(NDSDataLakeTenant.class);
    doReturn(Optional.of(ndsDataLakeTenant))
        .when(_dataLakeTenantSvc)
        .findByTenantId(streamsTenantId);
    doReturn(mock(NDSDataLakeTenant.NDSDataLakeDataProcessRegion.class))
        .when(ndsDataLakeTenant)
        .getDataProcessRegion();
    final AWSRegionName regionName = mock(AWSRegionName.class);
    when(ndsDataLakeTenant.getDataProcessRegion().getDataProcessRegionAsRegionName())
        .thenReturn(Optional.of(regionName));
    when(ndsDataLakeTenant.getCloudProvider()).thenReturn(CloudProvider.AWS);

    doReturn(CloudProvider.AWS).when(regionName).getProvider();

    doThrow(new DataLakeAdminApiException(NDSErrorCode.DATA_LAKE_TENANT_NOT_FOUND_FOR_ID))
        .when(_dataLakeTenantSvc)
        .getStorageConfig(any());

    String testRoleArn = "arn:aws:iam::************:root/forKinesis";
    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    new ObjectId(), testRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final StreamsAWSKinesisDataStreamsConnectionView kinesisConnectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            "unitTestAWS",
            new AWSConnectionConfig(testRoleArn),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.createConnection(kinesisConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_kinesisConnectionDao, times(1)).save(any());
      verify(_kinesisConnectionDao, times(1)).delete(streamsTenantId, "unitTestAWS");
    }
  }

  @Test
  public void testCreateConnectionForKinesis_notAuthorizedRole() throws SvcException {
    doThrow(new DataLakeAdminApiException(CommonErrorCode.SERVER_ERROR))
        .when(_dataLakeTenantSvc)
        .getStorageConfig(any());

    final StreamsTenant mockedTenant = mock(StreamsTenant.class);

    doReturn(Optional.of(mockedTenant)).when(_streamsTenantDao).findByGroupAndName(any(), any());

    doReturn(new ObjectId()).when(mockedTenant).getId();

    doThrow(new SvcException(CLOUD_PROVIDER_ACCESS_ROLE_NOT_AUTHORIZED, ""))
        .when(_ndsUiSvc)
        .getAWSAssumeRoleTempCredentials(any(), any());

    String testRoleArn = "arn:aws:iam::************:root/forKinesis";
    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    new ObjectId(), testRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());
    final StreamsAWSKinesisDataStreamsConnectionView kinesisConnectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            "unitTestAWS",
            new AWSConnectionConfig(testRoleArn),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.createConnection(kinesisConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_ndsUiSvc, times(1)).getAWSAssumeRoleTempCredentials(any(), any());
      assertEquals(STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED, ex.getErrorCode());
    }
  }

  @Test
  public void testCreateConnectionForKinesis_invalidNetworkingConfig() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);
    setupMocksForAWSCredentials();

    final StreamsAWSKinesisDataStreamsConnectionView kinesisConnectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            "unitTestAWS",
            new AWSConnectionConfig(),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.VPC).build()),
            new ProxyInfo());
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.createConnection(kinesisConnectionView, "testConnection", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(INVALID_ARGUMENT, ex.getErrorCode());
    }
  }

  @Test
  public void testUpdateConnectionForKinesis_validConnection() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    String testConnectionName = "unitTestAWS";

    Document existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", ProviderValues.AWS_KINESIS_DATA_STREAMS);

    Document testStore = new Document("stores", List.of(existingConnection));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());

    doReturn("testTenant").when(mockedTenant).getName();

    final ClusterConnection clusterConnection = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.AWSKinesisDataStreams)
        .when(clusterConnection)
        .getConnectionType();
    doReturn(Optional.of(clusterConnection))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    ObjectId existingRoleId = new ObjectId();
    String existingRoleArn = "arn:aws:iam::************:root/kinesis1";
    ObjectId newRoleId = new ObjectId();
    String newRoleArn = "arn:aws:iam::************:root/kinesis2";
    AWSKinesisDataStreamsConnection existingAWSKinesisDataStreamsConnection =
        new AWSKinesisDataStreamsConnection(
            new ObjectId(),
            mockedTenant.getId(),
            testConnectionName,
            new Date(),
            new Date(),
            new AWSConnectionConfig(existingRoleId.toHexString(), null),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());

    doReturn(Optional.of(existingAWSKinesisDataStreamsConnection))
        .when(_kinesisConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    existingRoleId, existingRoleArn, null, null, null, null, null),
                new NDSCloudProviderAccessAWSIAMRoleView(
                    newRoleId, newRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final StreamsAWSKinesisDataStreamsConnectionView newKinesisConnectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            testConnectionName,
            new AWSConnectionConfig(newRoleArn),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    StreamsConnectionView newConnectionView =
        _streamsSvc.updateConnection(
            newKinesisConnectionView, testConnectionName, "testTenant", group, auditInfo, null);
    StreamsAWSKinesisDataStreamsConnectionView actualKinesisConnectionView =
        (StreamsAWSKinesisDataStreamsConnectionView) newConnectionView;
    assertEquals(newRoleId.toHexString(), actualKinesisConnectionView.getAwsConfig().getRoleId());
    assertEquals(newRoleArn, actualKinesisConnectionView.getAwsConfig().getRoleArn());
    assertEquals(
        newKinesisConnectionView.getNetworking(), actualKinesisConnectionView.getNetworking());
  }

  @Test
  public void testUpdateConnectionForKinesis_invalidNetworkingConfig() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    String testConnectionName = "unitTestAWS";

    Document existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", ProviderValues.AWS_KINESIS_DATA_STREAMS);

    Document testStore = new Document("stores", List.of(existingConnection));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());

    doReturn("testTenant").when(mockedTenant).getName();

    final ClusterConnection clusterConnection = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.AWSKinesisDataStreams)
        .when(clusterConnection)
        .getConnectionType();
    doReturn(Optional.of(clusterConnection))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    ObjectId existingRoleId = new ObjectId();
    String existingRoleArn = "arn:aws:iam::************:root/kinesis1";
    ObjectId newRoleId = new ObjectId();
    String newRoleArn = "arn:aws:iam::************:root/kinesis2";
    AWSKinesisDataStreamsConnection existingAWSKinesisDataStreamsConnection =
        new AWSKinesisDataStreamsConnection(
            new ObjectId(),
            mockedTenant.getId(),
            testConnectionName,
            new Date(),
            new Date(),
            new AWSConnectionConfig(existingRoleId.toHexString(), null),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());

    doReturn(Optional.of(existingAWSKinesisDataStreamsConnection))
        .when(_kinesisConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    existingRoleId, existingRoleArn, null, null, null, null, null),
                new NDSCloudProviderAccessAWSIAMRoleView(
                    newRoleId, newRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final StreamsAWSKinesisDataStreamsConnectionView newKinesisConnectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            testConnectionName,
            new AWSConnectionConfig(newRoleArn),
            new NDSDataLakeStreamsNetworking(null),
            new ProxyInfo());
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.updateConnection(
          newKinesisConnectionView, testConnectionName, "testTenant", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      assertEquals(INVALID_ARGUMENT, ex.getErrorCode());
    }
  }

  @Test
  public void testUpdateConnectionForKinesis_roleArnNotFound() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    String testConnectionName = "unitTestAWS";

    Document existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", ProviderValues.AWS_KINESIS_DATA_STREAMS);

    Document testStore = new Document("stores", List.of(existingConnection));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());
    doReturn("testTenant").when(mockedTenant).getName();

    final ClusterConnection clusterConnection = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.AWSKinesisDataStreams)
        .when(clusterConnection)
        .getConnectionType();

    doReturn(Optional.of(clusterConnection))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    ObjectId existingRoleId = new ObjectId();
    String newRoleArn = "arn:aws:iam::************:root/kinesis2";
    AWSKinesisDataStreamsConnection existingAWSKinesisDataStreamsConnection =
        new AWSKinesisDataStreamsConnection(
            new ObjectId(),
            mockedTenant.getId(),
            testConnectionName,
            new Date(),
            new Date(),
            new AWSConnectionConfig(existingRoleId.toHexString(), null),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());

    doReturn(Optional.of(existingAWSKinesisDataStreamsConnection))
        .when(_kinesisConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(List.of(), null, null);
    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final StreamsAWSKinesisDataStreamsConnectionView newKinesisConnectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            testConnectionName,
            new AWSConnectionConfig(newRoleArn),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    try {
      _streamsSvc.updateConnection(
          newKinesisConnectionView, testConnectionName, "testTenant", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_ndsUiSvc, times(1)).getCloudProviderAccess(any());
      assertEquals(CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND, ex.getErrorCode());
    }
  }

  @Test
  public void testUpdateConnectionForKinesis_notAuthorizedRole() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    String testConnectionName = "unitTestAWS";

    Document existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", ProviderValues.AWS_KINESIS_DATA_STREAMS);

    Document testStore = new Document("stores", List.of(existingConnection));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());
    doReturn("testTenant").when(mockedTenant).getName();

    final ClusterConnection clusterConnection = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.AWSKinesisDataStreams)
        .when(clusterConnection)
        .getConnectionType();

    doReturn(Optional.of(clusterConnection))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    ObjectId testRoleId1 = new ObjectId();
    String testRoleArn2 = "arn:aws:iam::************:root/kinesis2";
    AWSKinesisDataStreamsConnection existingAWSKinesisDataStreamsConnection =
        new AWSKinesisDataStreamsConnection(
            new ObjectId(),
            mockedTenant.getId(),
            testConnectionName,
            new Date(),
            new Date(),
            new AWSConnectionConfig(testRoleId1.toHexString(), null),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());

    doReturn(Optional.of(existingAWSKinesisDataStreamsConnection))
        .when(_kinesisConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    testRoleId1,
                    "arn:aws:iam::************:root/kinesis2",
                    null,
                    null,
                    null,
                    null,
                    null),
                new NDSCloudProviderAccessAWSIAMRoleView(
                    new ObjectId(), testRoleArn2, null, null, null, null, null)),
            null,
            null);
    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final StreamsAWSKinesisDataStreamsConnectionView kinesisConnectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            testConnectionName,
            new AWSConnectionConfig(testRoleArn2),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    doThrow(new SvcException(CLOUD_PROVIDER_ACCESS_ROLE_NOT_AUTHORIZED, ""))
        .when(_ndsUiSvc)
        .getAWSAssumeRoleTempCredentials(any(), any());

    try {
      _streamsSvc.updateConnection(
          kinesisConnectionView, testConnectionName, "testTenant", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_ndsUiSvc, times(1)).getCloudProviderAccess(any());
      verify(_ndsUiSvc, times(1)).getAWSAssumeRoleTempCredentials(any(), any());
      assertEquals(STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED, ex.getErrorCode());
    }
  }

  @Test
  public void testUpdateConnection_networkingAccessTypeCannotBeModified() throws SvcException {
    final ObjectId privateLinkID = new ObjectId();
    final String DNS_DOMAIN = "test-kafka-namespace.servicebus.windows.net";
    final String BOOTSTRAP_SERVERS = DNS_DOMAIN + ":9093";
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);

    setupMocksForConnection(mockedTenant);

    var testConnectionName = "unitTestKafka";
    var existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", ProviderValues.KAFKA);
    existingConnection.append("bootstrapServers", List.of(BOOTSTRAP_SERVERS));

    var testStore = new Document("stores", List.of(existingConnection));
    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    var fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());

    doReturn("testTenant").when(mockedTenant).getName();

    final ClusterConnection clusterConnection = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.Kafka).when(clusterConnection).getConnectionType();
    doReturn(Optional.of(clusterConnection))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    var proxyInfo = new ProxyInfo();
    var kafkaConnection =
        new KafkaConnection(
            new ObjectId(),
            mockedTenant.getId(),
            testConnectionName,
            new Date(),
            new Date(),
            new NDSDataLakeKafkaSASLPlain(),
            new NDSDataLakeKafkaSecurityPlain(),
            null,
            new NDSDataLakeStreamsNetworking(
                new Access.AccessBuilder(Type.PRIVATE_LINK).setConnectionId(privateLinkID).build()),
            proxyInfo);

    doReturn(Optional.of(kafkaConnection))
        .when(_streamsKafkaConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    final StreamsKafkaConnectionView kafkaConnectionView =
        new StreamsKafkaConnectionView(
            testConnectionName,
            BOOTSTRAP_SERVERS,
            new NDSDataLakeKafkaSecurityPlain(),
            new NDSDataLakeKafkaSASLPlain(new KafkaAuthUsernamePassword("username", "password")),
            null,
            new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.PUBLIC).build()),
            proxyInfo,
            false,
            true);

    assertThrows(
        SvcException.class,
        () ->
            _streamsSvc.updateConnection(
                kafkaConnectionView, testConnectionName, "testTenant", group, auditInfo, null),
        NDSErrorCode.STREAM_NETWORKING_ACCESS_TYPE_CANNOT_BE_MODIFIED.getMessage());
  }

  @Test
  public void testDeleteConnectionForKinesis_connectionExists() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    String testConnectionName = "unitTestAWS";

    Document existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", ProviderValues.AWS_KINESIS_DATA_STREAMS);

    Document testStore = new Document("stores", List.of(existingConnection));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());

    doReturn("testTenant").when(mockedTenant).getName();

    final ClusterConnection clusterConnection = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.AWSKinesisDataStreams)
        .when(clusterConnection)
        .getConnectionType();
    doReturn(Optional.of(clusterConnection))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    ObjectId existingRoleId = new ObjectId();
    String existingRoleArn = "arn:aws:iam::************:root/kinesis1";
    AWSKinesisDataStreamsConnection existingAWSKinesisDataStreamsConnection =
        new AWSKinesisDataStreamsConnection(
            new ObjectId(),
            mockedTenant.getId(),
            testConnectionName,
            new Date(),
            new Date(),
            new AWSConnectionConfig(existingRoleId.toHexString(), null),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());

    doReturn(Optional.of(existingAWSKinesisDataStreamsConnection))
        .when(_kinesisConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    existingRoleId, existingRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    assertDoesNotThrow(
        () ->
            _streamsSvc.deleteConnection(testConnectionName, "testTenant", group, auditInfo, null));
  }

  @Test
  public void testDeleteConnectionForKinesis_connectionDoesNotExists() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    String testConnectionName = "unitTestAWS";

    Document existingConnection = new Document();
    existingConnection.append("name", testConnectionName);
    existingConnection.append("provider", ProviderValues.AWS_KINESIS_DATA_STREAMS);

    Document testStore = new Document("stores", List.of(existingConnection));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());

    doReturn("testTenant").when(mockedTenant).getName();

    final ClusterConnection clusterConnection = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.AWSKinesisDataStreams)
        .when(clusterConnection)
        .getConnectionType();
    doReturn(Optional.of(clusterConnection))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    ObjectId existingRoleId = new ObjectId();
    String existingRoleArn = "arn:aws:iam::************:root/kinesis1";
    AWSKinesisDataStreamsConnection existingAWSKinesisDataStreamsConnection =
        new AWSKinesisDataStreamsConnection(
            new ObjectId(),
            mockedTenant.getId(),
            testConnectionName,
            new Date(),
            new Date(),
            new AWSConnectionConfig(existingRoleId.toHexString(), null),
            new NDSDataLakeStreamsNetworking(new AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());

    doReturn(Optional.of(existingAWSKinesisDataStreamsConnection))
        .when(_kinesisConnectionDao)
        .fetchByTenantIdAndName(any(), eq(testConnectionName));

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    existingRoleId, existingRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    assertThrows(
        SvcException.class,
        () ->
            _streamsSvc.deleteConnection(
                "nonExistentConnection", "testTenant", group, auditInfo, null),
        STREAM_CONNECTION_NOT_FOUND.getMessage());
  }

  @Test
  public void testConnectionDeleteError() {
    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    final StreamsTenant testTenant = mock(StreamsTenant.class);

    doReturn(Optional.of(testTenant)).when(_streamsTenantDao).findByGroupAndName(any(), any());

    doReturn(new ObjectId()).when(testTenant).getId();

    try {
      _streamsSvc.deleteConnection("connectionName", "testStream", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_streamsSvc, times(1)).incrementConnectionErrorCounter(any(), any());
      verify(_streamsSvc, never()).incrementInstanceErrorCounter(any(), any());
    }
  }

  @Test
  public void testConnectionDeleteDataLakeErrorDoesNotRemoveConnection() throws SvcException {
    final Group group = mock(Group.class);
    final ObjectId groupId = new ObjectId();
    final ObjectId streamsTenantId = new ObjectId();
    final String connectionName = "connectionName";

    final StreamsTenant ndsStreamsTenant =
        new StreamsTenant(
            streamsTenantId,
            "tenantName",
            groupId,
            false,
            new Date(),
            new Date(),
            _defaultStreamConfig);
    final NDSDataLakeTenant ndsDataLakeTenant = mock(NDSDataLakeTenant.class);

    doReturn(Optional.of(ndsStreamsTenant))
        .when(_streamsTenantDao)
        .findByGroupAndName(any(), any());
    doReturn(Optional.of(ndsDataLakeTenant))
        .when(_dataLakeTenantSvc)
        .findByTenantId(streamsTenantId);

    doReturn(
            Optional.of(
                new ClusterConnection(
                    new ObjectId(),
                    streamsTenantId,
                    connectionName,
                    new Date(),
                    new Date(),
                    "cluster0",
                    null,
                    null)))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), any());

    final NDSDataLakeStorageV1View mockStorage = mock(NDSDataLakeStorageV1View.class);
    doReturn(mockStorage).when(_dataLakeTenantSvc).getStorageConfig(any());
    doReturn(mock(NDSDataLakeStorageV1View.Builder.class)).when(mockStorage).toBuilder();
    final NDSDataLakeTenant.NDSDataLakeTenantBuilder mockTenantBuilder =
        mock(NDSDataLakeTenant.NDSDataLakeTenantBuilder.class);
    doReturn(mockTenantBuilder).when(ndsDataLakeTenant).toBuilder();
    doReturn(mockTenantBuilder).when(mockTenantBuilder).lastUpdatedDate(any());
    doReturn(ndsDataLakeTenant).when(mockTenantBuilder).build();
    doReturn(new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1"))
        .when(ndsDataLakeTenant)
        .getDataProcessRegion();

    doThrow(new DataLakeAdminApiException(DATA_LAKE_STORAGE_CONFIG_OUTDATED))
        .when(_dataLakeTenantSvc)
        .updateStorageConfig(any(), any(), any());

    assertThrows(
        SvcException.class,
        () ->
            _streamsSvc.deleteConnection(
                connectionName, "tenantName", group, mock(AuditInfo.class), null),
        STREAM_TENANT_CONCURRENCY_ERROR.getMessage());

    // Will retry 6 times, should never delete without first deleting datalake.
    verify(_streamsKafkaConnectionDao, never()).delete(any(), any());
  }

  @Test
  public void testKafkaVPCConnectionDeleteFailsWithVPCDeploymentDescriptionNotFound()
      throws SvcException {
    final Group group = mock(Group.class);
    final ObjectId streamsTenantId = new ObjectId();
    final String connectionName = "connectionName";

    final StreamsTenant ndsStreamsTenant = mock(StreamsTenant.class);
    final NDSDataLakeTenant ndsDataLakeTenant = mock(NDSDataLakeTenant.class);

    doReturn(Optional.of(ndsStreamsTenant))
        .when(_streamsTenantDao)
        .findByGroupAndName(any(), any());
    doReturn(Optional.of(ndsDataLakeTenant))
        .when(_dataLakeTenantSvc)
        .findByTenantId(streamsTenantId);

    var networking = new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.VPC).build());
    doReturn(
            Optional.of(
                new KafkaConnection(
                    new ObjectId(),
                    streamsTenantId,
                    connectionName,
                    new Date(),
                    new Date(),
                    null,
                    null,
                    null,
                    networking,
                    new ProxyInfo())))
        .when(_streamsKafkaConnectionDao)
        .fetchByTenantIdAndName(any(), any());

    var clusterConnectionRepresentation = mock(ClusterConnection.class);
    doReturn(StreamsConnectionType.Kafka).when(clusterConnectionRepresentation).getConnectionType();

    doReturn(Optional.of(clusterConnectionRepresentation))
        .when(_clusterConnectionDao)
        .fetchByTenantIdAndName(any(), any());

    setupMocksForConnection(ndsStreamsTenant);

    assertThrows(
        SvcException.class,
        () ->
            _streamsSvc.deleteConnection(
                connectionName, ndsStreamsTenant.getName(), group, mock(AuditInfo.class), null),
        STREAM_VPC_PROXY_DEPLOYMENT_DESCRIPTION_NOT_FOUND.getMessage());

    verify(_vpcDeploymentProxy, never()).requestDeploymentDelete(any(), any());
  }

  @Test
  public void testGetConnections() throws SvcException {
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    doReturn(_defaultStreamConfig).when(mockedTenant).getStreamConfig();

    String kafkaConnectionName = "kafka";
    Document kafkaConnectionStore = new Document();
    kafkaConnectionStore.append("name", kafkaConnectionName);
    kafkaConnectionStore.append("provider", "kafka");
    kafkaConnectionStore.append("bootstrapServers", List.of("foo:9090"));

    String clusterConnectionName = "clusterConn";
    Document clusterConnectionStore = new Document();
    clusterConnectionStore.append("name", clusterConnectionName);
    clusterConnectionStore.append("provider", "atlas");
    clusterConnectionStore.append("clusterName", "cluster");

    String lambdaConnectionName = "lamb";
    Document lambdaConnectionStore = new Document();
    lambdaConnectionStore.append("name", lambdaConnectionName);
    lambdaConnectionStore.append("provider", "awslambda");

    Document testStore =
        new Document(
            "stores", List.of(kafkaConnectionStore, clusterConnectionStore, lambdaConnectionStore));

    final NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(testStore);
    NDSDataLakeStorageV1View fooSpy = spy(view);
    doReturn(fooSpy).when(_dataLakeTenantSvc).getStorageConfig(any());

    final NDSDataLakeStreamsNetworking expectedNetworking =
        new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.PUBLIC).build());
    final KafkaConnection kafkaConn = getKafkaConnection(kafkaConnectionName, expectedNetworking);

    final ClusterConnection clusterConnection =
        new ClusterConnection(
            new ObjectId(),
            mockedTenant.getId(),
            "clusterConn",
            new Date(),
            new Date(),
            "cluster",
            null,
            null);

    ObjectId testRoleId = new ObjectId();
    String testRoleArn = "arn:aws:iam::************:root/forLambdaFunction";

    NDSCloudProviderAccessView testAccessView =
        new NDSCloudProviderAccessView(
            List.of(
                new NDSCloudProviderAccessAWSIAMRoleView(
                    testRoleId, testRoleArn, null, null, null, null, null)),
            null,
            null);

    doReturn(testAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    AWSLambdaConnection lambdaConnection =
        new AWSLambdaConnection(
            new ObjectId(),
            mockedTenant.getId(),
            lambdaConnectionName,
            new Date(),
            new Date(),
            new AWSConnectionConfig(testRoleId.toHexString(), ""));

    doReturn(List.of(kafkaConn)).when(_streamsKafkaConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(clusterConnection)).when(_clusterConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(lambdaConnection)).when(_awsLambdaConnectionDao).fetchByTenantIdAndType(any());

    final Group mockedGroup = mock(Group.class);
    doReturn(mockedTenant.getGroupId()).when(mockedGroup).getId();
    StreamsTenantView tenantView =
        _streamsSvc.findTenantWithConnections(
            mockedGroup, mockedTenant.getName(), false, null, null);

    List<StreamsConnectionView> connectionViews = tenantView.getConnections();
    assertEquals(3, connectionViews.size());
    assertEquals(kafkaConnectionName, connectionViews.get(0).getName());
    assertEquals(clusterConnectionName, connectionViews.get(1).getName());
    assertEquals(lambdaConnectionName, connectionViews.get(2).getName());

    AWSLambdaConnectionView lambdaConnectionView = (AWSLambdaConnectionView) connectionViews.get(2);
    assertEquals(testRoleArn, lambdaConnectionView.getAwsConfig().getRoleArn());
  }

  private static KafkaConnection getKafkaConnection(
      final String kafkaConnectionName, final NDSDataLakeStreamsNetworking expectedNetworking) {
    final NDSDataLakeKafkaSASLPlain expectedAuth =
        new NDSDataLakeKafkaSASLPlain(new KafkaAuthUsernamePassword("Bob", "hunter2"));
    final NDSDataLakeKafkaSecurityPlain expectedSecurity = new NDSDataLakeKafkaSecurityPlain();

    return new KafkaConnection(
        new ObjectId(),
        new ObjectId(),
        kafkaConnectionName,
        new Date(),
        new Date(),
        expectedAuth,
        expectedSecurity,
        new HashMap<>(),
        expectedNetworking,
        null);
  }

  @Test
  public void testConnectionUpdateError() throws SvcException {
    doThrow(new SvcException(CommonErrorCode.SERVER_ERROR))
        .when(_dataLakeTenantSvc)
        .updateTenant(any(), any(), any(), any());

    final StreamsTenant testTenant = mock(StreamsTenant.class);

    doReturn(Optional.of(testTenant)).when(_streamsTenantDao).findByGroupAndName(any(), any());

    doReturn(new ObjectId()).when(testTenant).getId();

    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);
    final StreamsKafkaConnectionView kafkaConn =
        new StreamsKafkaConnectionView(
            "kafkaPlain",
            "host:2020",
            new NDSDataLakeKafkaSecurityPlain(),
            new NDSDataLakeKafkaSASLPlain(new KafkaAuthUsernamePassword("Bob", "hunter2")),
            Map.of(),
            new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.PUBLIC).build()),
            new ProxyInfo());

    try {
      _streamsSvc.updateConnection(kafkaConn, "connName", "tenantName", group, auditInfo, null);
      fail("Expected this to fail");
    } catch (SvcException ex) {
      verify(_streamsSvc, times(1)).incrementConnectionErrorCounter(any(), any());
      verify(_streamsSvc, never()).incrementInstanceErrorCounter(any(), any());
    }
  }

  @Test
  public void streamsTenantMakeNewDiff() {
    final ObjectId groupId = new ObjectId();
    final ObjectId tenantId = new ObjectId();

    final StreamsTenant ndsStreamsTenant =
        new StreamsTenant(
            tenantId, "tenantName", groupId, false, new Date(), new Date(), _defaultStreamConfig);

    NDSDataLakeTenant.NDSDataLakeDataProcessRegion region =
        new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1");

    NDSDataLakeCloudProviderConfig config =
        new NDSDataLakeCloudProviderConfig(
            new NDSDataLakeAWSCloudProviderConfig("arn", "id", null, new ObjectId()));

    ItemDiffs newDiffs = ndsStreamsTenant.makeNewDiffStreamTenant(region, config);

    final List<DeploymentItemParameterDiff> flatDiffs =
        newDiffs.getDiffs().stream().map(ItemDiff::getItems).flatMap(List::stream).toList();

    assertTrue(
        newDiffs.getDiffs().stream().allMatch(o -> o.getItemStatus().equals(ItemDiff.Status.NEW)));

    final Optional<DeploymentItemParameterDiff> nameOpt =
        flatDiffs.stream()
            .filter(i -> i.getParamName().equals(StreamsTenant.FieldDefs.NAME))
            .findFirst();
    assertTrue(nameOpt.isPresent());
    assertEquals(ndsStreamsTenant.getName(), nameOpt.get().getNewValue());
    assertNull(nameOpt.get().getOldValue());

    final DeploymentItemParameterDiff cloudProviderDiff =
        flatDiffs.stream()
            .filter(
                i ->
                    i.getParamName()
                        .equals(
                            NDSDataLakeTenant.NDSDataLakeDataProcessRegion.FieldDefs
                                .CLOUD_PROVIDER))
            .findFirst()
            .orElseThrow();
    assertEquals(region.getCloudProvider().getDescription(), cloudProviderDiff.getNewValue());

    final DeploymentItemParameterDiff regionDiff =
        flatDiffs.stream()
            .filter(
                i ->
                    i.getParamName()
                        .equals(NDSDataLakeTenant.NDSDataLakeDataProcessRegion.FieldDefs.REGION))
            .findFirst()
            .orElseThrow();
    assertEquals(region.getRegion(), regionDiff.getNewValue());

    final DeploymentItemParameterDiff streamConfig =
        flatDiffs.stream()
            .filter(i -> i.getParamName().equals(StreamsTenant.FieldDefs.STREAM_CONFIG))
            .findFirst()
            .orElseThrow();

    assertEquals(ndsStreamsTenant.getStreamConfig(), streamConfig.getNewValue());
    assertNull(streamConfig.getOldValue());
  }

  @Test
  public void streamsTenantMakeRemovedDiff() {
    final ObjectId groupId = new ObjectId();
    final ObjectId tenantId = new ObjectId();

    final StreamsTenant ndsStreamsTenant =
        new StreamsTenant(
            tenantId, "tenantName", groupId, false, new Date(), new Date(), _defaultStreamConfig);

    NDSDataLakeTenant.NDSDataLakeDataProcessRegion region =
        new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1");

    NDSDataLakeCloudProviderConfig config =
        new NDSDataLakeCloudProviderConfig(
            new NDSDataLakeAWSCloudProviderConfig("arn", "id", null, new ObjectId()));

    ItemDiffs newDiffs = ndsStreamsTenant.makeRemovedDiffStreamTenant(region, config);

    final List<DeploymentItemParameterDiff> flatDiffs =
        newDiffs.getDiffs().stream().map(ItemDiff::getItems).flatMap(List::stream).toList();

    assertTrue(
        newDiffs.getDiffs().stream()
            .allMatch(o -> o.getItemStatus().equals(ItemDiff.Status.REMOVED)));

    final DeploymentItemParameterDiff name =
        flatDiffs.stream()
            .filter(i -> i.getParamName().equals(StreamsTenant.FieldDefs.NAME))
            .findFirst()
            .orElseThrow();

    assertEquals(ndsStreamsTenant.getName(), name.getOldValue());
    assertNull(name.getNewValue());

    final DeploymentItemParameterDiff cloudProviderDiff =
        flatDiffs.stream()
            .filter(
                i ->
                    i.getParamName()
                        .equals(
                            NDSDataLakeTenant.NDSDataLakeDataProcessRegion.FieldDefs
                                .CLOUD_PROVIDER))
            .findFirst()
            .orElseThrow();
    assertEquals(region.getCloudProvider().getDescription(), cloudProviderDiff.getOldValue());
    assertNull(cloudProviderDiff.getNewValue());

    final DeploymentItemParameterDiff regionDiff =
        flatDiffs.stream()
            .filter(
                i ->
                    i.getParamName()
                        .equals(NDSDataLakeTenant.NDSDataLakeDataProcessRegion.FieldDefs.REGION))
            .findFirst()
            .orElseThrow();
    assertEquals(region.getRegion(), regionDiff.getOldValue());
    assertNull(regionDiff.getNewValue());

    final DeploymentItemParameterDiff streamConfig =
        flatDiffs.stream()
            .filter(i -> i.getParamName().equals(StreamsTenant.FieldDefs.STREAM_CONFIG))
            .findFirst()
            .orElseThrow();

    assertEquals(ndsStreamsTenant.getStreamConfig(), streamConfig.getOldValue());
    assertNull(streamConfig.getNewValue());
  }

  @Test
  public void streamsTenantMakeUpdatedDiff() {
    final ObjectId groupId = new ObjectId();
    final ObjectId tenantId = new ObjectId();

    final StreamsTenant ndsStreamsTenant =
        new StreamsTenant(
            tenantId, "tenantName", groupId, false, new Date(), new Date(), _defaultStreamConfig);

    NDSDataLakeTenant.NDSDataLakeDataProcessRegion oldRegion =
        new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1");

    NDSDataLakeCloudProviderConfig oldConfig =
        new NDSDataLakeCloudProviderConfig(
            new NDSDataLakeAWSCloudProviderConfig("arn", "id", null, new ObjectId()));

    NDSDataLakeTenant.NDSDataLakeDataProcessRegion newRegion =
        new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AZURE, "eastus");

    NDSDataLakeCloudProviderConfig newConfig =
        new NDSDataLakeCloudProviderConfig(
            new NDSDataLakeAWSCloudProviderConfig("nar", "di", null, new ObjectId()));

    final NDSDataLakeTenant oldTenant = buildSimpleDataLakeTenant(oldRegion, oldConfig);
    final NDSDataLakeTenant newTenant = buildSimpleDataLakeTenant(newRegion, newConfig);

    ItemDiffs updatedDiffs = ndsStreamsTenant.makeUpdatedDiffStreamTenant(newTenant, oldTenant);

    List<ItemDiff> updatedDiffsWithMetadata = new ArrayList<>(updatedDiffs.getDiffs());

    final ItemDiff regionDiffmetaData =
        updatedDiffsWithMetadata.stream()
            .filter(i -> i.getItemType().equals(NDS_STREAM_PROCESSING_INSTANCE_DATA_PROCESS_REGION))
            .findFirst()
            .orElseThrow();

    assertEquals(
        NDS_STREAM_PROCESSING_INSTANCE_DATA_PROCESS_REGION, regionDiffmetaData.getItemType());

    final ItemDiff configDiffmetaData =
        updatedDiffsWithMetadata.stream()
            .filter(
                i -> i.getItemType().equals(NDS_STREAM_PROCESSING_INSTANCE_CLOUD_PROVIDER_CONFIG))
            .findFirst()
            .orElseThrow();

    assertEquals(
        NDS_STREAM_PROCESSING_INSTANCE_CLOUD_PROVIDER_CONFIG, configDiffmetaData.getItemType());

    final List<DeploymentItemParameterDiff> flatDiffs =
        updatedDiffs.getDiffs().stream().map(ItemDiff::getItems).flatMap(List::stream).toList();

    assertTrue(
        updatedDiffs.getDiffs().stream()
            .allMatch(o -> o.getItemStatus().equals(ItemDiff.Status.MODIFIED)));

    final DeploymentItemParameterDiff cloudProviderDiff =
        flatDiffs.stream()
            .filter(
                i ->
                    i.getParamName()
                        .equals(
                            NDSDataLakeTenant.NDSDataLakeDataProcessRegion.FieldDefs
                                .CLOUD_PROVIDER))
            .findFirst()
            .orElseThrow();
    assertEquals(oldRegion.getCloudProvider().getDescription(), cloudProviderDiff.getOldValue());
    assertEquals(newRegion.getCloudProvider().getDescription(), cloudProviderDiff.getNewValue());

    final DeploymentItemParameterDiff regionDiff =
        flatDiffs.stream()
            .filter(
                i ->
                    i.getParamName()
                        .equals(NDSDataLakeTenant.NDSDataLakeDataProcessRegion.FieldDefs.REGION))
            .findFirst()
            .orElseThrow();
    assertEquals(oldRegion.getRegion(), regionDiff.getOldValue());
    assertEquals(newRegion.getRegion(), regionDiff.getNewValue());
  }

  private NDSDataLakeTenant buildSimpleDataLakeTenant(
      NDSDataLakeTenant.NDSDataLakeDataProcessRegion pRegion,
      NDSDataLakeCloudProviderConfig pConfig) {
    return new NDSDataLakeTenant(
        new NDSDataLakeTenant.NDSDataLakeTenantId(new ObjectId(), "foo"),
        new ObjectId(),
        Date.from(Instant.EPOCH),
        Date.from(Instant.EPOCH),
        NDSDataLakeState.UNVERIFIED,
        pRegion,
        false,
        false,
        0,
        null,
        false,
        pConfig,
        List.of("foo-aa001.virginia-usa.a.query.mongodb.net"),
        "foo-aa001.a.query.mongodb.net",
        Date.from(Instant.EPOCH),
        Map.of("endpoint", "hostname"));
  }

  private Set<RegionName> mockRegions() {
    Set<RegionName> result = new HashSet<>();

    result.add(createRegion(CloudProvider.PROVIDER_AWS, "SA_EAST_1"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "AP_SOUTHEAST_2"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "AP_SOUTH_1"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "AP_SOUTHEAST_1"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "EU_WEST_1"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "EU_CENTRAL_1"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "EU_WEST_2"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "US_EAST_1"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "US_EAST_2"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "US_WEST_2"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "AP_NORTHEAST_1"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "EU_CENTRAL_1"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "SA_EAST_1"));
    result.add(createRegion(CloudProvider.PROVIDER_AWS, "CA_CENTRAL_1"));

    result.add(createRegion(CloudProvider.PROVIDER_AZURE, "EUROPE_WEST"));
    result.add(createRegion(CloudProvider.PROVIDER_AZURE, "AUSTRALIA_EAST"));
    result.add(createRegion(CloudProvider.PROVIDER_AZURE, "EUROPE_NORTH"));
    result.add(createRegion(CloudProvider.PROVIDER_AZURE, "US_WEST"));
    result.add(createRegion(CloudProvider.PROVIDER_AZURE, "US_EAST"));
    result.add(createRegion(CloudProvider.PROVIDER_AZURE, "US_EAST_2"));
    result.add(createRegion(CloudProvider.PROVIDER_AZURE, "ASIA_SOUTH_EAST"));

    result.add(createRegion(CloudProvider.PROVIDER_GCP, "US_EAST_4"));
    result.add(createRegion(CloudProvider.PROVIDER_GCP, "CENTRAL_US"));
    result.add(createRegion(CloudProvider.PROVIDER_GCP, "WESTERN_EUROPE"));
    result.add(createRegion(CloudProvider.PROVIDER_GCP, "EASTERN_US"));

    return result;
  }

  public RegionName createRegion(String pCloudProvider, String pName) {
    return RegionNameHelper.findByNameOrValue(CloudProvider.findByName(pCloudProvider), pName)
        .orElseThrow();
  }

  @Test
  public void testHasActiveStreamsResources_none() throws SvcException {
    ObjectId groupId = new ObjectId();

    doReturn(new ArrayList<>())
        .when(_dataLakeTenantSvc)
        .findTenantsByGroupAndType(groupId, NDSDataLakeTenant.DataLakeType.STREAM);
    doReturn(new ArrayList<>()).when(_streamsTenantDao).findByGroupId(groupId);
    doReturn(new ArrayList<>()).when(_privateLinkDao).findByGroupId(groupId);
    doReturn(new ArrayList<>()).when(_vpcPeeringConnectionSvc).findByGroupId(groupId);

    boolean nothing = _streamsSvc.hasActiveStreamsResources(groupId);
    assertFalse(nothing);
  }

  @Test
  public void testHasActiveStreamsResources_VPCPeering() throws SvcException {
    ObjectId groupId = new ObjectId();

    List<VPCPeeringConnection> vpcPeeringConnections = new ArrayList<>();
    VPCPeeringConnection mockVpc = mock(VPCPeeringConnection.class);
    doReturn(true).when(mockVpc).isActive();
    vpcPeeringConnections.add(mockVpc);
    doReturn(vpcPeeringConnections).when(_vpcPeeringConnectionSvc).findByGroupId(groupId);

    boolean hasVPCPeeringConnections = _streamsSvc.hasActiveStreamsResources(groupId);
    assertTrue(hasVPCPeeringConnections);
  }

  @Test
  public void testHasActiveStreamsResources_InactiveVPCPeering() throws SvcException {
    ObjectId groupId = new ObjectId();

    List<VPCPeeringConnection> vpcPeeringConnections = new ArrayList<>();
    VPCPeeringConnection mockVpc = mock(VPCPeeringConnection.class);
    doReturn(false).when(mockVpc).isActive();
    vpcPeeringConnections.add(mockVpc);
    doReturn(vpcPeeringConnections).when(_vpcPeeringConnectionSvc).findByGroupId(groupId);

    boolean hasVPCPeeringConnections = _streamsSvc.hasActiveStreamsResources(groupId);
    assertFalse(hasVPCPeeringConnections);
  }

  @Test
  public void testHasActiveStreamsResources_privateLink() throws SvcException {
    ObjectId groupId = new ObjectId();

    List<StreamsPrivateLink> privateLinks = new ArrayList<>();
    StreamsPrivateLink mockPrivateLink = mock(StreamsPrivateLink.class);
    doReturn(true).when(mockPrivateLink).isActive();
    privateLinks.add(mockPrivateLink);

    doReturn(privateLinks).when(_privateLinkDao).findByGroupId(groupId);
    boolean hasPrivateLinkConnections = _streamsSvc.hasActiveStreamsResources(groupId);
    assertTrue(hasPrivateLinkConnections);
  }

  @Test
  public void testHasActiveStreamsResources_tgwResourceShares() throws SvcException {
    ObjectId groupId = new ObjectId();
    StreamsTransitGateway tgw =
        new StreamsTransitGateway(
            new ObjectId(),
            groupId,
            List.of(
                new StreamsTransitGateway.ResourceShare(
                    "tgw-1234",
                    "arn:aws:ram:us-east-1:400000220050:resource-share/5ef69000-2222-3333-4444-000e558ea000",
                    "arn:aws:ram:us-east-1:000000220050:resource-share-invitation/5ef69000-2222-3333-4444-000e558ea000")));

    doReturn(Optional.of(tgw)).when(_transitGatewayResourceSharesDao).findByGroupId(groupId);
    boolean hasTGWResourceShares = _streamsSvc.hasActiveStreamsResources(groupId);
    assertTrue(hasTGWResourceShares);
  }

  @Test
  public void testHasActiveStreamsResources_emptyTgwResourceShares() throws SvcException {
    ObjectId groupId = new ObjectId();
    StreamsTransitGateway mockTGW = mock(StreamsTransitGateway.class);

    doReturn(Optional.of(mockTGW)).when(_transitGatewayResourceSharesDao).findByGroupId(groupId);
    boolean hasTGWResourceShares = _streamsSvc.hasActiveStreamsResources(groupId);
    assertFalse(hasTGWResourceShares);

    ObjectId groupId2 = new ObjectId();
    doReturn(Optional.empty()).when(_transitGatewayResourceSharesDao).findByGroupId(groupId2);
    hasTGWResourceShares = _streamsSvc.hasActiveStreamsResources(groupId2);
    assertFalse(hasTGWResourceShares);
  }

  @Test
  public void testHasActiveStreamsResources_InactivePrivateLink() throws SvcException {
    ObjectId groupId = new ObjectId();

    List<StreamsPrivateLink> privateLinks = new ArrayList<>();
    StreamsPrivateLink mockPrivateLink = mock(StreamsPrivateLink.class);
    doReturn(false).when(mockPrivateLink).isActive();
    privateLinks.add(mockPrivateLink);

    doReturn(privateLinks).when(_privateLinkDao).findByGroupId(groupId);
    boolean hasPrivateLinkConnections = _streamsSvc.hasActiveStreamsResources(groupId);
    assertFalse(hasPrivateLinkConnections);
  }

  @Test
  public void testHasActiveStreamsResources_tenant() throws SvcException {
    ObjectId groupId = new ObjectId();

    ObjectId streamsTenantId = new ObjectId();
    final NDSDataLakeTenant ndsDataLakeTenant = mock(NDSDataLakeTenant.class);

    List<NDSDataLakeTenant> dataLakeTenants = new ArrayList<>();
    dataLakeTenants.add(ndsDataLakeTenant);

    doReturn(dataLakeTenants)
        .when(_dataLakeTenantSvc)
        .findTenantsByGroupAndType(groupId, NDSDataLakeTenant.DataLakeType.STREAM);
    doReturn(streamsTenantId).when(ndsDataLakeTenant).getTenantId();

    final StreamsTenant ndsStreamsTenant =
        new StreamsTenant(
            streamsTenantId,
            "tenantName",
            groupId,
            false,
            new Date(),
            new Date(),
            _defaultStreamConfig);

    List<StreamsTenant> streams = new ArrayList<>();
    streams.add(ndsStreamsTenant);

    doReturn(streams).when(_streamsTenantDao).findByGroupId(groupId);

    boolean hasTenants = _streamsSvc.hasActiveStreamsResources(groupId);
    assertTrue(hasTenants);
  }

  @Test
  public void testBuildMetricsQuery_invalidMetricName() {
    String someValidId = new ObjectId().toHexString();
    SvcException e =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.buildMetricsQuery(
                    someValidId, "bogusMetric", List.of(someValidId), null, null));
    assertEquals("Invalid metricName", e.getMessage());
  }

  @Test
  public void testBuildMetricsQuery_invalidGroupId() {
    String someValidId = new ObjectId().toHexString();
    SvcException e =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.buildMetricsQuery(
                    "bogusId", "kafka_total_offset_lag", List.of(someValidId), null, null));
    assertEquals("Invalid groupId bogusId is invalid", e.getMessage());
  }

  @Test
  public void testBuildMetricsQuery_invalidFunction() {
    String someValidId = new ObjectId().toHexString();
    SvcException e =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.buildMetricsQuery(
                    someValidId,
                    "kafka_total_offset_lag",
                    List.of(someValidId),
                    "bogusFunction",
                    null));
    assertTrue(e.getMessage().startsWith("Invalid function must be one of"));
  }

  @Test
  public void testBuildMetricsQuery_invalidProcessorId() {
    String someValidId = new ObjectId().toHexString();
    SvcException e =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.buildMetricsQuery(
                    someValidId,
                    "kafka_total_offset_lag",
                    List.of(someValidId, "bogusId"),
                    "sum",
                    null));
    assertEquals("Invalid processorId bogusId is invalid", e.getMessage());
  }

  @Test
  public void testBuildMetricsQuery_withCounter() throws SvcException {
    final String someValidId = "67d9cf44c70bff5d134f249a";
    String query =
        _streamsSvc.buildMetricsQuery(
            someValidId, "dlq_message_count_total", new ArrayList<>(), null, null);
    assertEquals("dlq_message_count_total{group_id=\"67d9cf44c70bff5d134f249a\"}", query);
  }

  @Test
  public void testBuildMetricsQuery_withoutAggregation() throws SvcException {
    final String someValidId1 = "67d9cf44c70bff5d134f249a";
    final String someValidId2 = "67d9cf44c70bff5d134f249b";
    final String someValidId3 = "67d9cf44c70bff5d134f249c";
    String query =
        _streamsSvc.buildMetricsQuery(
            someValidId1,
            "kafka_total_offset_lag",
            List.of(someValidId2, someValidId3),
            null,
            null);
    assertEquals(
        "kafka_total_offset_lag{group_id=\"67d9cf44c70bff5d134f249a\","
            + " processor_id=~\"67d9cf44c70bff5d134f249b|67d9cf44c70bff5d134f249c\"}",
        query);
  }

  @Test
  public void testBuildMetricsQuery_withAggregation() throws SvcException {
    final String someValidId1 = "67d9cf44c70bff5d134f249a";
    final String someValidId2 = "67d9cf44c70bff5d134f249b";
    final String someValidId3 = "67d9cf44c70bff5d134f249c";
    String query =
        _streamsSvc.buildMetricsQuery(
            someValidId1,
            "kafka_total_offset_lag",
            List.of(someValidId2, someValidId3),
            "avg",
            null);
    assertEquals(
        "avg(kafka_total_offset_lag{group_id=\"67d9cf44c70bff5d134f249a\","
            + " processor_id=~\"67d9cf44c70bff5d134f249b|67d9cf44c70bff5d134f249c\"})",
        query);
  }

  @Test
  public void testBuildMetricsQuery_withoutProcessorIds() throws SvcException {
    final String someValidId = "67d9cf44c70bff5d134f249a";
    String query =
        _streamsSvc.buildMetricsQuery(
            someValidId, "kafka_total_offset_lag", new ArrayList<>(), "sum", null);
    assertEquals("sum(kafka_total_offset_lag{group_id=\"67d9cf44c70bff5d134f249a\"})", query);
  }

  @Test
  public void testBuildMetricsQuery_withLookback() throws SvcException {
    final String someValidId = "67d9cf44c70bff5d134f249a";
    String query =
        _streamsSvc.buildMetricsQuery(
            someValidId, "kafka_total_offset_lag", new ArrayList<>(), "increase", "50s");
    assertEquals(
        "increase(kafka_total_offset_lag{group_id=\"67d9cf44c70bff5d134f249a\"}[50s])", query);
  }

  @Test
  public void testGetMetricsInstantQuery()
      throws SvcException, AuthnOAuthClient.UnableToRetrieveCloudJwtException, IOException {
    final String someValidId = "67d9cf44c70bff5d134f249a";
    final String jsonResponse =
        """
        {
          "status": "success",
          "data": {
            "resultType": "vector",
            "result": [
              {
                "metric": {
                  "group_id": "65094f059e0776665611598b",
                  "host_name": "streams-spp-sp10-6db6949d86-l22wh",
                  "processor_id": "67573137546d303d22f3bba9",
                  "processor_name": "orders",
                  "tenant_id": "67572e51c8fd9b4bd9d36c41"
                },
                "value": [
                  1741883471,
                  "0.0"
                ]
              }
            ]
          }
        }""";
    PromQLResponseView<Data> promQLResponseView =
        _objectMapper.readValue(jsonResponse, new TypeReference<>() {});
    doReturn(promQLResponseView).when(_customerMetricsQuerySvc).getMetricsViaInstantQuery(any());
    PromQLResponseView.Data data =
        _streamsSvc.getMetricsInstantQuery(
            someValidId, "kafka_total_offset_lag", new ArrayList<>(), null, null, "123");
    assertEquals("vector", data.getResultType());
    assertEquals(1, data.getResult().size());
    assertEquals("65094f059e0776665611598b", data.getResult().get(0).getMetric().get("group_id"));
  }

  @Test
  public void testGetMetricsRangeQuery()
      throws SvcException, AuthnOAuthClient.UnableToRetrieveCloudJwtException, IOException {
    final String someValidId = "67d9cf44c70bff5d134f249a";
    final String jsonResponse =
        """
        {
          "status": "success",
          "data": {
            "resultType": "matrix",
            "result": [
              {
                "metric": {
                  "group_id": "65094f059e0776665611598b",
                  "host_name": "streams-spp-sp10-6db6949d86-l22wh",
                  "processor_id": "67573137546d303d22f3bba9",
                  "processor_name": "orders",
                  "tenant_id": "67572e51c8fd9b4bd9d36c41"
                },
                "values": [
                  [1741883471, "0.0"],
                  [1741883472, "1.0"],
                  [1741883473, "2.0"]
                ]
              }
            ]
          }
        }""";
    PromQLResponseView<Data> promQLResponseView =
        _objectMapper.readValue(jsonResponse, new TypeReference<>() {});
    doReturn(promQLResponseView).when(_customerMetricsQuerySvc).getMetricsViaRangeQuery(any());
    PromQLResponseView.Data data =
        _streamsSvc.getMetricsRangeQuery(
            someValidId,
            "kafka_total_offset_lag",
            new ArrayList<>(),
            null,
            null,
            "123",
            "234",
            "1s");
    assertEquals("matrix", data.getResultType());
    assertEquals(1, data.getResult().size());
    assertEquals("65094f059e0776665611598b", data.getResult().get(0).getMetric().get("group_id"));
  }

  @Test
  public void testSetProxyInfo() {
    ProxyInfo proxyInfo = new ProxyInfo();
    proxyInfo.setDnsName("kafkaDns");
    proxyInfo.setAuthKey("kafkaKey");

    StreamsConnection kafkaConn =
        new KafkaConnection(
            new ObjectId(),
            new ObjectId(),
            "kafka_name",
            new Date(),
            new Date(),
            null,
            null,
            null,
            null,
            null);
    assertDoesNotThrow(() -> kafkaConn.setProxyInfo(proxyInfo));

    StreamsConnection kinesisConn =
        new AWSKinesisDataStreamsConnection(
            new ObjectId(),
            new ObjectId(),
            "kinesis_name",
            new Date(),
            new Date(),
            null,
            null,
            null);

    assertDoesNotThrow(() -> kinesisConn.setProxyInfo(proxyInfo));

    StreamsConnection s3Conn =
        new S3Connection(
            new ObjectId(), new ObjectId(), "s3_name", new Date(), new Date(), null, null, null);

    assertDoesNotThrow(() -> s3Conn.setProxyInfo(proxyInfo));

    StreamsConnection httpsConn =
        new HttpsConnection(
            new ObjectId(),
            new ObjectId(),
            "https_conn",
            new Date(),
            new Date(),
            "https://ifconfig.me",
            null);

    NotImplementedException exception =
        assertThrows(NotImplementedException.class, () -> httpsConn.setProxyInfo(proxyInfo));
    assertEquals("Not implemented", exception.getMessage());

    StreamsConnection awsLambdaConn =
        new AWSLambdaConnection(
            new ObjectId(), new ObjectId(), "awslambda_conn", new Date(), new Date(), null);
    exception =
        assertThrows(NotImplementedException.class, () -> awsLambdaConn.setProxyInfo(proxyInfo));
    assertEquals("Not implemented", exception.getMessage());

    StreamsConnection clusterConn =
        new ClusterConnection(
            new ObjectId(),
            new ObjectId(),
            "cluster_conn",
            new Date(),
            new Date(),
            "cluster_name",
            new DBRoleToExecute(DBRoleType.CUSTOM, "atlasAdmin"),
            new ObjectId().toHexString());
    exception =
        assertThrows(NotImplementedException.class, () -> clusterConn.setProxyInfo(proxyInfo));
    assertEquals("Not implemented", exception.getMessage());

    StreamsConnection sampleConn =
        new SampleConnection(
            new ObjectId(), new ObjectId(), "sample_stream_solar", new Date(), new Date());

    exception =
        assertThrows(NotImplementedException.class, () -> sampleConn.setProxyInfo(proxyInfo));
    assertEquals("Not implemented", exception.getMessage());
  }

  @Test
  public void testUnsetProxyInfo() {
    ProxyInfo proxyInfo = new ProxyInfo();
    proxyInfo.setDnsName("kafkaDns");
    proxyInfo.setAuthKey("kafkaKey");
    Access access = new Access();
    access.setType(Type.PRIVATE_LINK);
    access.setConnectionId(new ObjectId());

    // Kafka
    StreamsConnectionView connectionView =
        new StreamsKafkaConnectionView(
            "kafka", null, null, null, null, new NDSDataLakeStreamsNetworking(access), proxyInfo);
    assertEquals("kafkaDns", connectionView.getProxyInfo().getDnsName());
    assertEquals("kafkaKey", connectionView.getProxyInfo().getAuthKey());
    StreamsConnectionView updateView = _streamsSvc.unsetProxyInfo(connectionView);
    assertInstanceOf(StreamsKafkaConnectionView.class, updateView);
    assertTrue(updateView.getProxyInfo().getDnsName().isEmpty());
    assertTrue(updateView.getProxyInfo().getAuthKey().isEmpty());

    // Kinesis
    proxyInfo.setDnsName("kinesisDns");
    proxyInfo.setAuthKey("kinesisKey");
    connectionView =
        new StreamsAWSKinesisDataStreamsConnectionView(
            "kinesis", null, new NDSDataLakeStreamsNetworking(access), proxyInfo);
    assertNotNull(connectionView.getProxyInfo());
    assertEquals("kinesisDns", connectionView.getProxyInfo().getDnsName());
    assertEquals("kinesisKey", connectionView.getProxyInfo().getAuthKey());
    updateView = _streamsSvc.unsetProxyInfo(connectionView);
    assertInstanceOf(StreamsAWSKinesisDataStreamsConnectionView.class, updateView);
    assertTrue(updateView.getProxyInfo().getDnsName().isEmpty());
    assertTrue(updateView.getProxyInfo().getAuthKey().isEmpty());
  }

  @Test
  public void testUnsetProxyInfoList() {
    ProxyInfo proxyInfo = new ProxyInfo();
    proxyInfo.setDnsName("dns");
    proxyInfo.setAuthKey("test");
    Access access = new Access();
    access.setType(Type.PRIVATE_LINK);
    access.setConnectionId(new ObjectId());
    StreamsConnectionView kafkaConnectionView =
        new StreamsKafkaConnectionView(
            "kafka", null, null, null, null, new NDSDataLakeStreamsNetworking(access), proxyInfo);
    List<StreamsConnectionView> streamsConnectionViewList = List.of(kafkaConnectionView);
    _streamsSvc.unsetProxyInfoInList(streamsConnectionViewList);
    for (StreamsConnectionView streamsConnectionView : streamsConnectionViewList) {
      assertInstanceOf(StreamsKafkaConnectionView.class, streamsConnectionView);
      StreamsKafkaConnectionView updatedKafkaView =
          (StreamsKafkaConnectionView) streamsConnectionView;
      assertTrue(updatedKafkaView.getProxyInfo().getDnsName().isEmpty());
      assertTrue(updatedKafkaView.getProxyInfo().getAuthKey().isEmpty());
    }
  }

  @Test
  public void testUnsetProxyInfo_nullView() {
    StreamsConnectionView updateView = _streamsSvc.unsetProxyInfo(null);
    assertNull(updateView);
  }

  @Test
  public void testUnsetProxyInfo_nullProxyInfo() {
    StreamsConnectionView kafkaConnectionView =
        new StreamsKafkaConnectionView("kafka", null, null, null, null, null, null);
    StreamsConnectionView updateView = _streamsSvc.unsetProxyInfo(kafkaConnectionView);
    assertInstanceOf(StreamsKafkaConnectionView.class, updateView);
    StreamsKafkaConnectionView updatedKafkaView = (StreamsKafkaConnectionView) updateView;
    assertNull(updatedKafkaView.getProxyInfo());
  }

  @Test
  public void testValidateTransitGatewayRouteCreationInRouteTable_DefaultRoute() {
    final RouteTable routeTable = new RouteTable();
    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());

    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                StreamsSvc.validateTransitGatewayRouteCreationInRouteTable(
                    "0.0.0.0/0", routeTable, container));
    assertEquals(INVALID_ARGUMENT, exception.getErrorCode());
    assertEquals(
        "Invalid destinationCidr. The provided destination CIDR: '0.0.0.0/0' is the default route"
            + " which is not allowed.",
        exception.getMessage());
  }

  @Test
  public void testValidateTransitGatewayRouteCreationInRouteTable_OverlappingRoute() {
    final RouteTable routeTable = new RouteTable();
    Route sillyRoute = new Route().withDestinationCidrBlock("*******/8");
    routeTable.setRoutes(Collections.singletonList(sillyRoute));

    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());

    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                StreamsSvc.validateTransitGatewayRouteCreationInRouteTable(
                    "1.2.0.0/24", routeTable, container));
    assertEquals(INVALID_ARGUMENT, exception.getErrorCode());
    assertEquals(
        "Invalid destinationCidr. Invalid destination CIDR provided (1.2.0.0/24), overlaps existing"
            + " peering route(s): *******/8",
        exception.getMessage());
  }

  @Test
  public void testValidateTransitGatewayRouteCreationInRouteTable_InvalidRoute_InvalidMask() {
    // Invalid cidr mask
    final RouteTable routeTable = new RouteTable();

    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());

    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                StreamsSvc.validateTransitGatewayRouteCreationInRouteTable(
                    "*******/39", routeTable, container));
    assertEquals(INVALID_ARGUMENT, exception.getErrorCode());
    assertEquals(
        "Invalid destinationCidr. The provided destination CIDR: '*******/39' is not a valid IPv4"
            + " CIDR.",
        exception.getMessage());
  }

  @Test
  public void testValidateTransitGatewayRouteCreationInRouteTable_InvalidRoute_InvalidString() {
    // Invalid route string
    final RouteTable routeTable = new RouteTable();

    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());

    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                StreamsSvc.validateTransitGatewayRouteCreationInRouteTable(
                    "not an actual route", routeTable, container));
    assertEquals(INVALID_ARGUMENT, exception.getErrorCode());
    assertEquals(
        "Invalid destinationCidr. The provided destination CIDR: 'not an actual route' is not a"
            + " valid IPv4 CIDR.",
        exception.getMessage());
  }

  @Test
  public void testValidateTransitGatewayRouteCreationInRouteTable_InvalidRoute_UnsupportedIPv6() {
    // Unsupported IPv6 route
    final RouteTable routeTable = new RouteTable();

    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());

    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                StreamsSvc.validateTransitGatewayRouteCreationInRouteTable(
                    "2001:DB8:2:2::/64", routeTable, container));
    assertEquals(INVALID_ARGUMENT, exception.getErrorCode());
    assertEquals(
        "Invalid destinationCidr. The provided destination CIDR: '2001:DB8:2:2::/64' is not a valid"
            + " IPv4 CIDR.",
        exception.getMessage());
  }

  @Test
  public void testValidateTransitGatewayRouteCreationInRouteTable_IgnoreDefaultRoute() {
    final RouteTable routeTable = new RouteTable();
    Route defaultRoute = new Route().withDestinationCidrBlock("0.0.0.0/0");
    routeTable.setRoutes(Collections.singletonList(defaultRoute));

    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());

    // Should not throw any exception.
    assertDoesNotThrow(
        () ->
            StreamsSvc.validateTransitGatewayRouteCreationInRouteTable(
                "*******/24", routeTable, container));
  }

  @Test
  public void testValidateTransitGatewayRouteCreationInRouteTable_RouteAlreadyExists() {
    final RouteTable routeTable = new RouteTable();
    Route existingRoute =
        new Route()
            .withDestinationCidrBlock("*******/24")
            .withTransitGatewayId("tgw-ebeebe00000000000");
    routeTable.setRoutes(Collections.singletonList(existingRoute));

    final AWSCloudProviderContainer container =
        new AWSCloudProviderContainer(NDSModelTestFactory.getAWSContainer());

    SvcException exception =
        assertThrows(
            SvcException.class,
            () ->
                StreamsSvc.validateTransitGatewayRouteCreationInRouteTable(
                    "*******/24", routeTable, container));
    assertEquals(INVALID_ARGUMENT, exception.getErrorCode());
    assertEquals(
        "Invalid destinationCidr. Invalid destination CIDR provided (*******/24), overlaps existing"
            + " peering route(s): *******/24",
        exception.getMessage());
  }

  @Test
  public void testIsNetworkingTypeVPC() {
    assertFalse(_streamsSvc.isNetworkingTypeVPC(null));

    NDSDataLakeStreamsNetworking networking = new NDSDataLakeStreamsNetworking();
    assertFalse(_streamsSvc.isNetworkingTypeVPC(networking));

    Access access = new Access();
    networking.setAccess(access);
    assertFalse(_streamsSvc.isNetworkingTypeVPC(networking));

    access.setType(Type.PRIVATE_LINK);
    assertFalse(_streamsSvc.isNetworkingTypeVPC(networking));

    access.setType(Type.VPC);
    assertTrue(_streamsSvc.isNetworkingTypeVPC(networking));
  }

  @Test
  public void testIsNetworkingTypePrivateLink() {
    assertFalse(_streamsSvc.isNetworkingTypePrivateLink(null));

    NDSDataLakeStreamsNetworking networking = new NDSDataLakeStreamsNetworking();
    assertFalse(_streamsSvc.isNetworkingTypePrivateLink(networking));

    Access access = new Access();
    networking.setAccess(access);
    assertFalse(_streamsSvc.isNetworkingTypePrivateLink(networking));

    access.setType(Type.VPC);
    assertFalse(_streamsSvc.isNetworkingTypePrivateLink(networking));

    access.setType(Type.PRIVATE_LINK);
    assertTrue(_streamsSvc.isNetworkingTypePrivateLink(networking));
  }

  @Test
  public void testGetNetworkingMode() {
    CloudProvider aws = CloudProvider.AWS;
    CloudProvider azure = CloudProvider.AZURE;
    CloudProvider gcp = CloudProvider.GCP;

    Access plAccess = new Access();
    plAccess.setType(Type.PRIVATE_LINK);
    final NDSDataLakeStreamsNetworking plNetworking = new NDSDataLakeStreamsNetworking(plAccess);

    Access vpcAccess = new Access();
    vpcAccess.setType(Type.VPC);
    final NDSDataLakeStreamsNetworking vpcNetworking = new NDSDataLakeStreamsNetworking(vpcAccess);

    Access tgwAccess = new Access();
    tgwAccess.setType(Type.TRANSIT_GATEWAY);
    final NDSDataLakeStreamsNetworking tgwNetworking = new NDSDataLakeStreamsNetworking(tgwAccess);

    Access publicAccess = new Access();
    publicAccess.setType(Type.PUBLIC);
    final NDSDataLakeStreamsNetworking publicNetworking =
        new NDSDataLakeStreamsNetworking(publicAccess);

    try {
      assertEquals(
          Models.ProxyType.PROXY_TYPE_UNSPECIFIED_VALUE, _streamsSvc.getNetworkingMode(aws, null));
      assertEquals(
          Models.ProxyType.PROXY_TYPE_UNSPECIFIED_VALUE,
          _streamsSvc.getNetworkingMode(azure, null));

      NDSDataLakeStreamsNetworking networking = new NDSDataLakeStreamsNetworking();
      assertEquals(
          Models.ProxyType.PROXY_TYPE_UNSPECIFIED_VALUE, _streamsSvc.getNetworkingMode(aws, null));
      assertEquals(
          Models.ProxyType.PROXY_TYPE_UNSPECIFIED_VALUE,
          _streamsSvc.getNetworkingMode(azure, null));

      Access access = new Access();
      access.setType(null);
      networking.setAccess(access);
      assertEquals(
          Models.ProxyType.PROXY_TYPE_UNSPECIFIED_VALUE,
          _streamsSvc.getNetworkingMode(aws, networking));
      assertEquals(
          Models.ProxyType.PROXY_TYPE_UNSPECIFIED_VALUE,
          _streamsSvc.getNetworkingMode(azure, networking));

      // Private Link networking mode.
      assertEquals(
          Models.ProxyType.PROXY_TYPE_AWS_PRIVATE_LINK_VALUE,
          _streamsSvc.getNetworkingMode(aws, plNetworking));
      assertEquals(
          Models.ProxyType.PROXY_TYPE_AZURE_PRIVATE_LINK_VALUE,
          _streamsSvc.getNetworkingMode(azure, plNetworking));
      assertEquals(
          Models.ProxyType.PROXY_TYPE_GCP_PRIVATE_LINK_VALUE,
          _streamsSvc.getNetworkingMode(gcp, plNetworking));

      // VPC_PEERING networking mode.
      assertEquals(
          Models.ProxyType.PROXY_TYPE_AWS_VPC_PEERING_VALUE,
          _streamsSvc.getNetworkingMode(aws, vpcNetworking));

      SvcException exception =
          assertThrows(
              SvcException.class, () -> _streamsSvc.getNetworkingMode(azure, vpcNetworking));
      assertEquals(CommonErrorCode.VALIDATION_ERROR, exception.getErrorCode());
      assertEquals("Reason: Invalid cloud provider AZURE for VPC_PEERING", exception.getMessage());

      exception =
          assertThrows(SvcException.class, () -> _streamsSvc.getNetworkingMode(gcp, vpcNetworking));
      assertEquals(CommonErrorCode.VALIDATION_ERROR, exception.getErrorCode());
      assertEquals("Reason: Invalid cloud provider GCP for VPC_PEERING", exception.getMessage());

      // Transit Gateway Networking mode.
      assertEquals(
          Models.ProxyType.PROXY_TYPE_AWS_TRANSIT_GATEWAY_VALUE,
          _streamsSvc.getNetworkingMode(aws, tgwNetworking));

      exception =
          assertThrows(
              SvcException.class, () -> _streamsSvc.getNetworkingMode(azure, tgwNetworking));
      assertEquals(CommonErrorCode.VALIDATION_ERROR, exception.getErrorCode());
      assertEquals(
          "Reason: Invalid cloud provider AZURE for TRANSIT_GATEWAY", exception.getMessage());

      exception =
          assertThrows(SvcException.class, () -> _streamsSvc.getNetworkingMode(gcp, tgwNetworking));
      assertEquals(CommonErrorCode.VALIDATION_ERROR, exception.getErrorCode());
      assertEquals(
          "Reason: Invalid cloud provider GCP for TRANSIT_GATEWAY", exception.getMessage());

      // Public Networking mode.
      exception =
          assertThrows(
              SvcException.class, () -> _streamsSvc.getNetworkingMode(aws, publicNetworking));
      assertEquals(CommonErrorCode.VALIDATION_ERROR, exception.getErrorCode());
      assertEquals(
          "Reason: Invalid networking access type PUBLIC for retrieving private networking mode for"
              + " cloud provider AWS",
          exception.getMessage());

      exception =
          assertThrows(
              SvcException.class, () -> _streamsSvc.getNetworkingMode(azure, publicNetworking));
      assertEquals(CommonErrorCode.VALIDATION_ERROR, exception.getErrorCode());
      assertEquals(
          "Reason: Invalid networking access type PUBLIC for retrieving private networking mode for"
              + " cloud provider AZURE",
          exception.getMessage());

      exception =
          assertThrows(
              SvcException.class, () -> _streamsSvc.getNetworkingMode(gcp, publicNetworking));
      assertEquals(CommonErrorCode.VALIDATION_ERROR, exception.getErrorCode());
      assertEquals(
          "Reason: Invalid networking access type PUBLIC for retrieving private networking mode for"
              + " cloud provider GCP",
          exception.getMessage());
    } catch (Exception ex) {
      fail(ex.getMessage());
    }
  }

  @ParameterizedTest
  @EnumSource(
      value = StreamsPrivateLinkVendorType.class,
      names = {"KINESIS", "S3"})
  public void testValidateAWSRegionalStreamPrivateLink_happyPath(
      StreamsPrivateLinkVendorType pVendor) {
    String pServiceEndpointIdUsEast1;
    if (pVendor == StreamsPrivateLinkVendorType.S3) {
      pServiceEndpointIdUsEast1 = "com.amazonaws.us-east-1.s3";
    } else {
      pServiceEndpointIdUsEast1 = "com.amazonaws.us-east-1.kinesis-streams";
    }
    final ApiStreamsPrivateLinkView validRegionalPl =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(),
            StreamsPrivateLinkProviderType.AWS.getProvider(),
            pServiceEndpointIdUsEast1,
            null,
            null,
            null,
            null,
            "us-east-1",
            "");
    assertDoesNotThrow(
        () -> _streamsSvc.validateAWSRegionalStreamPrivateLink(validRegionalPl, new ObjectId()));
  }

  @Test
  public void testValidateAWSRegionalStreamPrivateLink_vendorValidation() {

    SvcException svcException = getSvcExceptionForNullVendor();

    assertEquals("Invalid vendor. Vendor is missing.", svcException.getMessage());

    final ApiStreamsPrivateLinkView plWithEmptyVendor =
        new ApiStreamsPrivateLinkView(
            "",
            StreamsPrivateLinkProviderType.AWS.getProvider(),
            null,
            null,
            null,
            null,
            null,
            "us-east-1",
            "");

    svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithEmptyVendor, new ObjectId()));
    assertEquals("Invalid vendor. Vendor is missing.", svcException.getMessage());

    final ApiStreamsPrivateLinkView plWithIncorrectVendor =
        new ApiStreamsPrivateLinkView(
            "GENERIC",
            StreamsPrivateLinkProviderType.AWS.getProvider(),
            null,
            null,
            null,
            null,
            null,
            "us-east-1",
            "");
    svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithIncorrectVendor, new ObjectId()));
    assertEquals(
        "Invalid vendor. Vendor must be one of "
            + StreamsPrivateLinkVendorType.regionalPrivateLinkVendors,
        svcException.getMessage());
  }

  private SvcException getSvcExceptionForNullVendor() {
    final ApiStreamsPrivateLinkView plWithNullVendor =
        new ApiStreamsPrivateLinkView(
            null,
            StreamsPrivateLinkProviderType.AWS.getProvider(),
            null,
            null,
            null,
            null,
            null,
            "us-east-1",
            "");
    return assertThrows(
        SvcException.class,
        () -> _streamsSvc.validateAWSRegionalStreamPrivateLink(plWithNullVendor, new ObjectId()));
  }

  @ParameterizedTest
  @EnumSource(
      value = StreamsPrivateLinkVendorType.class,
      names = {"KINESIS", "S3"})
  public void testValidateAWSRegionalStreamPrivateLink_providerValidation(
      StreamsPrivateLinkVendorType pVendor) {

    final ApiStreamsPrivateLinkView plWithNullProvider =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(), null, null, null, null, null, null, "us-east-1", "");
    SvcException svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithNullProvider, new ObjectId()));
    assertEquals("Invalid provider", svcException.getMessage());

    final ApiStreamsPrivateLinkView plWithEmptyProvider =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(), "", null, null, null, null, null, "us-east-1", "");
    svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithEmptyProvider, new ObjectId()));
    assertEquals("Invalid provider", svcException.getMessage());

    final ApiStreamsPrivateLinkView plWithInvalidProvider =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(),
            "SHARAN_WEB_SERVICES",
            null,
            null,
            null,
            null,
            null,
            "us-east-1",
            "");
    svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithInvalidProvider, new ObjectId()));
    assertEquals("Invalid provider", svcException.getMessage());

    final ApiStreamsPrivateLinkView plWithUnsupportedProvider =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(), "AZURE", null, null, null, null, null, "us-east-1", "");
    svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithUnsupportedProvider, new ObjectId()));
    assertEquals(
        "Invalid provider. "
            + pVendor.getVendor()
            + " Private Link is supported for 'AWS' provider only",
        svcException.getMessage());
  }

  @ParameterizedTest
  @EnumSource(
      value = StreamsPrivateLinkVendorType.class,
      names = {"KINESIS", "S3"})
  public void testValidateAWSRegionalStreamPrivateLink_regionValidation(
      StreamsPrivateLinkVendorType pVendor) {
    final ApiStreamsPrivateLinkView plWithNullRegion =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(), "AWS", null, null, null, null, null, null, "");
    SvcException svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(plWithNullRegion, new ObjectId()));
    assertTrue(svcException.getMessage().startsWith("Invalid region. Region must be one of "));

    final ApiStreamsPrivateLinkView plWithEmptyRegion =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(), "AWS", null, null, null, null, null, "", "");
    svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithEmptyRegion, new ObjectId()));
    assertTrue(svcException.getMessage().startsWith("Invalid region. Region must be one of "));

    final ApiStreamsPrivateLinkView plWithInvalidRegion =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(), "AWS", null, null, null, null, null, "eu-east-3", "");
    svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithInvalidRegion, new ObjectId()));
    assertTrue(svcException.getMessage().startsWith("Invalid region. Region must be one of "));
  }

  @ParameterizedTest
  @EnumSource(
      value = StreamsPrivateLinkVendorType.class,
      names = {"KINESIS", "S3"})
  public void testValidateAWSRegionalStreamPrivateLink_serviceEndpointIdValidation(
      StreamsPrivateLinkVendorType pVendor) {
    ObjectId groupId = new ObjectId();
    String validServiceEndpointId;
    String invalidServiceEndpointId;
    String wrongVendorServiceEndpointId;
    String wrongRegionServiceEndpointId;

    if (pVendor == StreamsPrivateLinkVendorType.S3) {
      validServiceEndpointId = "com.amazonaws.us-east-1.s3";
      invalidServiceEndpointId = "invalid.endpoint.id";
      wrongVendorServiceEndpointId =
          "com.amazonaws.us-east-1.kinesis-streams"; // KINESIS format for S3
      wrongRegionServiceEndpointId = "com.amazonaws.eu-west-1.s3"; // Wrong region
    } else { // KINESIS
      validServiceEndpointId = "com.amazonaws.us-east-1.kinesis-streams";
      invalidServiceEndpointId = "invalid.endpoint.id";
      wrongVendorServiceEndpointId = "com.amazonaws.us-east-1.s3"; // S3 format for KINESIS
      wrongRegionServiceEndpointId = "com.amazonaws.eu-west-1.kinesis-streams"; // Wrong region
    }

    // Test null serviceEndpointId
    SvcException svcException = getSvcException(pVendor, groupId);
    assertEquals(NDSErrorCode.INVALID_ARGUMENT, svcException.getErrorCode());
    assertEquals(
        "Invalid serviceEndpointId. serviceEndpointId is missing.", svcException.getMessage());

    // Test empty serviceEndpointId
    final ApiStreamsPrivateLinkView plWithEmptyServiceEndpointId =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(),
            StreamsPrivateLinkProviderType.AWS.getProvider(),
            "",
            null,
            null,
            null,
            null,
            "us-east-1",
            "");
    svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithEmptyServiceEndpointId, groupId));
    assertEquals(NDSErrorCode.INVALID_ARGUMENT, svcException.getErrorCode());
    assertEquals(
        "Invalid serviceEndpointId. serviceEndpointId is missing.", svcException.getMessage());

    // Test invalid serviceEndpointId format
    final ApiStreamsPrivateLinkView plWithInvalidServiceEndpointId =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(),
            StreamsPrivateLinkProviderType.AWS.getProvider(),
            invalidServiceEndpointId,
            null,
            null,
            null,
            null,
            "us-east-1",
            "");
    svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithInvalidServiceEndpointId, groupId));
    assertEquals(NDSErrorCode.INVALID_ARGUMENT, svcException.getErrorCode());
    assertEquals(
        String.format(
            "Invalid serviceEndpointId '%s'. ServiceEndpointId should be '%s'",
            invalidServiceEndpointId, validServiceEndpointId),
        svcException.getMessage());

    // Test wrong vendor in serviceEndpointId
    final ApiStreamsPrivateLinkView plWithWrongVendorServiceEndpointId =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(),
            StreamsPrivateLinkProviderType.AWS.getProvider(),
            wrongVendorServiceEndpointId,
            null,
            null,
            null,
            null,
            "us-east-1",
            "");
    svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithWrongVendorServiceEndpointId, groupId));
    assertEquals(NDSErrorCode.INVALID_ARGUMENT, svcException.getErrorCode());
    assertEquals(
        String.format(
            "Invalid serviceEndpointId '%s'. ServiceEndpointId should be '%s'",
            wrongVendorServiceEndpointId, validServiceEndpointId),
        svcException.getMessage());

    // Test wrong region in serviceEndpointId
    final ApiStreamsPrivateLinkView plWithWrongRegionServiceEndpointId =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(),
            StreamsPrivateLinkProviderType.AWS.getProvider(),
            wrongRegionServiceEndpointId,
            null,
            null,
            null,
            null,
            "us-east-1", // Region is us-east-1 but serviceEndpointId has eu-west-1
            "");
    svcException =
        assertThrows(
            SvcException.class,
            () ->
                _streamsSvc.validateAWSRegionalStreamPrivateLink(
                    plWithWrongRegionServiceEndpointId, groupId));
    assertEquals(NDSErrorCode.INVALID_ARGUMENT, svcException.getErrorCode());
    assertEquals(
        String.format(
            "Invalid serviceEndpointId '%s'. ServiceEndpointId should be '%s'",
            wrongRegionServiceEndpointId, validServiceEndpointId),
        svcException.getMessage());

    // Test valid serviceEndpointId (should not throw)
    final ApiStreamsPrivateLinkView plWithValidServiceEndpointId =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(),
            StreamsPrivateLinkProviderType.AWS.getProvider(),
            validServiceEndpointId,
            null,
            null,
            null,
            null,
            "us-east-1",
            "");
    assertDoesNotThrow(
        () ->
            _streamsSvc.validateAWSRegionalStreamPrivateLink(
                plWithValidServiceEndpointId, groupId));
  }

  private SvcException getSvcException(
      final StreamsPrivateLinkVendorType pVendor, final ObjectId groupId) {
    final ApiStreamsPrivateLinkView plWithNullServiceEndpointId =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(),
            StreamsPrivateLinkProviderType.AWS.getProvider(),
            null,
            null,
            null,
            null,
            null,
            "us-east-1",
            "");
    return assertThrows(
        SvcException.class,
        () ->
            _streamsSvc.validateAWSRegionalStreamPrivateLink(plWithNullServiceEndpointId, groupId));
  }

  @ParameterizedTest
  @EnumSource(
      value = StreamsPrivateLinkVendorType.class,
      names = {"KINESIS", "S3"})
  public void testValidateAWSRegionalStreamPrivateLink_existingPLValidation(
      StreamsPrivateLinkVendorType pVendor) {
    ObjectId groupId = new ObjectId();
    Optional<AWSRegionName> regionOpt = AWSRegionName.findByNameOrValue("us-east-1");
    RegionName region = regionOpt.orElseThrow();
    doReturn(true)
        .when(_privateLinkDao)
        .alreadyExistsVendorAndRegion(eq(groupId), eq(pVendor.getVendor()), eq(region));

    String pServiceEndpointIdUsEast1;
    String pServiceEndpointIdEuWest1;
    if (pVendor == StreamsPrivateLinkVendorType.S3) {
      pServiceEndpointIdUsEast1 = "com.amazonaws.us-east-1.s3";
      pServiceEndpointIdEuWest1 = "com.amazonaws.eu-west-1.s3";
    } else {
      pServiceEndpointIdUsEast1 = "com.amazonaws.us-east-1.kinesis-streams";
      pServiceEndpointIdEuWest1 = "com.amazonaws.eu-west-1.kinesis-streams";
    }
    final ApiStreamsPrivateLinkView duplicatePl =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(),
            "AWS",
            pServiceEndpointIdUsEast1,
            null,
            null,
            null,
            null,
            "us-east-1",
            "");
    SvcException svcException =
        assertThrows(
            SvcException.class,
            () -> _streamsSvc.validateAWSRegionalStreamPrivateLink(duplicatePl, groupId));
    assertEquals(
        "Private Link already exists in provided region us-east-1 for vendor "
            + pVendor.getVendor()
            + ".",
        svcException.getMessage());

    ObjectId differentGroupId = new ObjectId();
    assertDoesNotThrow(
        () -> _streamsSvc.validateAWSRegionalStreamPrivateLink(duplicatePl, differentGroupId));

    final ApiStreamsPrivateLinkView plInDifferentRegion =
        new ApiStreamsPrivateLinkView(
            pVendor.getVendor(),
            "AWS",
            pServiceEndpointIdEuWest1,
            null,
            null,
            null,
            null,
            "eu-west-1",
            "");
    assertDoesNotThrow(
        () -> _streamsSvc.validateAWSRegionalStreamPrivateLink(plInDifferentRegion, groupId));
  }

  @Test
  public void testFindTenantWithConnections_inUseFlagAllConnectionsInUse() throws SvcException {
    final Group group = mock(Group.class);
    final ObjectId groupId = new ObjectId();
    doReturn(groupId).when(group).getId();
    final ObjectId streamsTenantId = new ObjectId();
    final ObjectId tenantId = new ObjectId();

    final StreamsTenant streamsTenant =
        new StreamsTenant(
            streamsTenantId,
            "tenantName",
            groupId,
            false,
            new Date(),
            new Date(),
            _defaultStreamConfig);
    final NDSDataLakeTenant dataLakeTenant = mock(NDSDataLakeTenant.class);

    // ALL connections are marked as in use
    final Set<String> usedConnectionNames =
        Set.of(
            "kafka-connection",
            "cluster-connection",
            "sample-connection",
            "https-connection",
            "s3-connection",
            "lambda-connection",
            "kinesis-connection");

    // Mock connections
    final KafkaConnection kafkaConn = createMockKafkaConnection(tenantId);
    final ClusterConnection clusterConn = createMockClusterConnection(tenantId, groupId);
    final SampleConnection sampleConn = createMockSampleConnection(tenantId);
    final HttpsConnection httpsConn = createMockHttpsConnection(tenantId);
    final S3Connection s3Conn = createMockS3Connection(tenantId);
    final AWSLambdaConnection lambdaConn = createMockLambdaConnection(tenantId);
    final AWSKinesisDataStreamsConnection kinesisConn = createMockKinesisConnection(tenantId);

    // Mock basic setup
    doReturn(Optional.of(streamsTenant))
        .when(_streamsTenantDao)
        .findByGroupAndName(groupId, "tenantName");
    doReturn(Optional.of(dataLakeTenant)).when(_dataLakeTenantSvc).findByTenantId(streamsTenantId);

    doReturn(tenantId).when(dataLakeTenant).getTenantId();
    doReturn(new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1"))
        .when(dataLakeTenant)
        .getDataProcessRegion();

    // Mock storage config with all connection types
    Document config = createStorageConfigWithAllConnectionTypes();
    NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(config);
    doReturn(view).when(_dataLakeTenantSvc).getStorageConfig(dataLakeTenant);

    // Mock connection DAOs
    doReturn(List.of(kafkaConn)).when(_streamsKafkaConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(clusterConn)).when(_clusterConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(sampleConn)).when(_sampleConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(httpsConn)).when(_httpsConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(s3Conn)).when(_s3ConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(lambdaConn)).when(_awsLambdaConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(kinesisConn)).when(_kinesisConnectionDao).fetchByTenantIdAndType(any());

    // Mock SPM service to return processors and used connection names
    doReturn(List.of())
        .when(_spmManager)
        .getStreamProcessors(any(), any(), any(), anyBoolean(), anyBoolean(), anyBoolean());
    doReturn(usedConnectionNames).when(_spmManager).getSourceConnectionNames(any());

    // Mock VPC proxy names
    doReturn(List.of()).when(_streamsSvc).findDeployingVpcKafkaProxyNames(any(), any());

    // Mock cloud provider access for AWS connections to avoid NPE
    final NDSCloudProviderAccessView cloudProviderAccessView =
        mock(NDSCloudProviderAccessView.class);
    final NDSCloudProviderAccessAWSIAMRoleView roleView =
        mock(NDSCloudProviderAccessAWSIAMRoleView.class);
    final ObjectId mockRoleId =
        new ObjectId("507f1f77bcf86cd799439011"); // Fixed ObjectId for testing
    doReturn(mockRoleId).when(roleView).getRoleId();
    doReturn("arn:aws:iam::************:role/test-role").when(roleView).getIamAssumedRoleArn();
    doReturn(List.of(roleView))
        .when(cloudProviderAccessView)
        .getNDSCloudProviderAccessAWSIAMRoleViews();
    doReturn(cloudProviderAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    // Mock AppUser to avoid cross-group access validation errors
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.LOCAL).when(appUser).getType();

    // Call the method
    final StreamsTenantView result =
        _streamsSvc.findTenantWithConnections(
            group, "tenantName", true, mock(AuditInfo.class), appUser);

    // Verify we got exactly 7 connections and ALL are marked as in use
    assertEquals(7, result.getConnections().size(), "Should have exactly 7 connection types");
    for (StreamsConnectionView connection : result.getConnections()) {
      assertTrue(
          connection.getInUse(),
          "Connection " + connection.getName() + " should be marked as in use");
    }
  }

  @Test
  public void testFindTenantWithConnections_inUseFlagNoConnectionsInUse() throws SvcException {
    final Group group = mock(Group.class);
    final ObjectId groupId = new ObjectId();
    doReturn(groupId).when(group).getId();
    final ObjectId streamsTenantId = new ObjectId();
    final ObjectId tenantId = new ObjectId();

    final StreamsTenant streamsTenant =
        new StreamsTenant(
            streamsTenantId,
            "tenantName",
            groupId,
            false,
            new Date(),
            new Date(),
            _defaultStreamConfig);
    final NDSDataLakeTenant dataLakeTenant = mock(NDSDataLakeTenant.class);

    // Create connections for all types
    final KafkaConnection kafkaConn = createMockKafkaConnection(tenantId);
    final ClusterConnection clusterConn = createMockClusterConnection(tenantId, groupId);
    final SampleConnection sampleConn = createMockSampleConnection(tenantId);
    final HttpsConnection httpsConn = createMockHttpsConnection(tenantId);
    final S3Connection s3Conn = createMockS3Connection(tenantId);
    final AWSLambdaConnection lambdaConn = createMockLambdaConnection(tenantId);
    final AWSKinesisDataStreamsConnection kinesisConn = createMockKinesisConnection(tenantId);

    // NO connections are in use
    final Set<String> usedConnectionNames = Set.of();

    // Mock basic setup
    doReturn(Optional.of(streamsTenant))
        .when(_streamsTenantDao)
        .findByGroupAndName(groupId, "tenantName");
    doReturn(Optional.of(dataLakeTenant)).when(_dataLakeTenantSvc).findByTenantId(streamsTenantId);

    doReturn(tenantId).when(dataLakeTenant).getTenantId();
    doReturn(new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1"))
        .when(dataLakeTenant)
        .getDataProcessRegion();

    // Mock storage config with all connection types
    Document config = createStorageConfigWithAllConnectionTypes();
    NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(config);
    doReturn(view).when(_dataLakeTenantSvc).getStorageConfig(dataLakeTenant);

    // Mock connection DAOs
    doReturn(List.of(kafkaConn)).when(_streamsKafkaConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(clusterConn)).when(_clusterConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(sampleConn)).when(_sampleConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(httpsConn)).when(_httpsConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(s3Conn)).when(_s3ConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(lambdaConn)).when(_awsLambdaConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(kinesisConn)).when(_kinesisConnectionDao).fetchByTenantIdAndType(any());

    // Mock SPM service to return NO connections as used
    doReturn(List.of())
        .when(_spmManager)
        .getStreamProcessors(any(), any(), any(), anyBoolean(), anyBoolean(), anyBoolean());
    doReturn(usedConnectionNames).when(_spmManager).getSourceConnectionNames(any());

    // Mock VPC proxy names
    doReturn(List.of()).when(_streamsSvc).findDeployingVpcKafkaProxyNames(any(), any());

    // Mock cloud provider access for AWS connections to avoid NPE
    final NDSCloudProviderAccessView cloudProviderAccessView =
        mock(NDSCloudProviderAccessView.class);
    final NDSCloudProviderAccessAWSIAMRoleView roleView =
        mock(NDSCloudProviderAccessAWSIAMRoleView.class);
    final ObjectId mockRoleId =
        new ObjectId("507f1f77bcf86cd799439011"); // Fixed ObjectId for testing
    doReturn(mockRoleId).when(roleView).getRoleId();
    doReturn("arn:aws:iam::************:role/test-role").when(roleView).getIamAssumedRoleArn();
    doReturn(List.of(roleView))
        .when(cloudProviderAccessView)
        .getNDSCloudProviderAccessAWSIAMRoleViews();
    doReturn(cloudProviderAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    // Mock AppUser to avoid cross-group access validation errors
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.LOCAL).when(appUser).getType();

    // Call the method
    final StreamsTenantView result =
        _streamsSvc.findTenantWithConnections(
            group, "tenantName", true, mock(AuditInfo.class), appUser);

    // Verify we got exactly 7 connections and NONE are marked as in use
    assertEquals(7, result.getConnections().size(), "Should have exactly 7 connection types");
    for (StreamsConnectionView connection : result.getConnections()) {
      assertFalse(
          connection.getInUse(),
          "Connection " + connection.getName() + " should NOT be marked as in use");
    }
  }

  @Test
  public void testFindTenantWithConnections_inUseFlagMixedCase() throws SvcException {
    final Group group = mock(Group.class);
    final ObjectId groupId = new ObjectId();
    doReturn(groupId).when(group).getId();
    final ObjectId streamsTenantId = new ObjectId();
    final ObjectId tenantId = new ObjectId();

    final StreamsTenant streamsTenant =
        new StreamsTenant(
            streamsTenantId,
            "tenantName",
            groupId,
            false,
            new Date(),
            new Date(),
            _defaultStreamConfig);
    final NDSDataLakeTenant dataLakeTenant = mock(NDSDataLakeTenant.class);

    // Create connections for all types
    final KafkaConnection kafkaConn = createMockKafkaConnection(tenantId);
    final ClusterConnection clusterConn = createMockClusterConnection(tenantId, groupId);
    final SampleConnection sampleConn = createMockSampleConnection(tenantId);
    final HttpsConnection httpsConn = createMockHttpsConnection(tenantId);
    final S3Connection s3Conn = createMockS3Connection(tenantId);
    final AWSLambdaConnection lambdaConn = createMockLambdaConnection(tenantId);
    final AWSKinesisDataStreamsConnection kinesisConn = createMockKinesisConnection(tenantId);

    // SOME connections are in use - mixed case to test both setInUse() and parent constructor
    // patterns
    final Set<String> usedConnectionNames =
        Set.of(
            "kafka-connection", // setInUse() pattern
            "cluster-connection", // parent constructor pattern
            "https-connection", // setInUse() pattern
            "kinesis-connection"); // setInUse() pattern

    // Mock basic setup
    doReturn(Optional.of(streamsTenant))
        .when(_streamsTenantDao)
        .findByGroupAndName(groupId, "tenantName");
    doReturn(Optional.of(dataLakeTenant)).when(_dataLakeTenantSvc).findByTenantId(streamsTenantId);

    doReturn(tenantId).when(dataLakeTenant).getTenantId();
    doReturn(new NDSDataLakeTenant.NDSDataLakeDataProcessRegion(CloudProvider.AWS, "US_EAST_1"))
        .when(dataLakeTenant)
        .getDataProcessRegion();

    // Mock storage config with all connection types
    Document config = createStorageConfigWithAllConnectionTypes();
    NDSDataLakeStorageV1View view = new NDSDataLakeStorageV1View(config);
    doReturn(view).when(_dataLakeTenantSvc).getStorageConfig(dataLakeTenant);

    // Mock connection DAOs
    doReturn(List.of(kafkaConn)).when(_streamsKafkaConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(clusterConn)).when(_clusterConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(sampleConn)).when(_sampleConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(httpsConn)).when(_httpsConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(s3Conn)).when(_s3ConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(lambdaConn)).when(_awsLambdaConnectionDao).fetchByTenantIdAndType(any());
    doReturn(List.of(kinesisConn)).when(_kinesisConnectionDao).fetchByTenantIdAndType(any());

    // Mock SPM service to return some connections as used
    doReturn(List.of())
        .when(_spmManager)
        .getStreamProcessors(any(), any(), any(), anyBoolean(), anyBoolean(), anyBoolean());
    doReturn(usedConnectionNames).when(_spmManager).getSourceConnectionNames(any());

    // Mock VPC proxy names
    doReturn(List.of()).when(_streamsSvc).findDeployingVpcKafkaProxyNames(any(), any());

    // Mock cloud provider access for AWS connections to avoid NPE
    final NDSCloudProviderAccessView cloudProviderAccessView =
        mock(NDSCloudProviderAccessView.class);
    final NDSCloudProviderAccessAWSIAMRoleView roleView =
        mock(NDSCloudProviderAccessAWSIAMRoleView.class);
    final ObjectId mockRoleId =
        new ObjectId("507f1f77bcf86cd799439011"); // Fixed ObjectId for testing
    doReturn(mockRoleId).when(roleView).getRoleId();
    doReturn("arn:aws:iam::************:role/test-role").when(roleView).getIamAssumedRoleArn();
    doReturn(List.of(roleView))
        .when(cloudProviderAccessView)
        .getNDSCloudProviderAccessAWSIAMRoleViews();
    doReturn(cloudProviderAccessView).when(_ndsUiSvc).getCloudProviderAccess(any());

    // Mock AppUser to avoid cross-group access validation errors
    final AppUser appUser = mock(AppUser.class);
    doReturn(UserType.LOCAL).when(appUser).getType();

    // Call the method
    final StreamsTenantView result =
        _streamsSvc.findTenantWithConnections(
            group, "tenantName", true, mock(AuditInfo.class), appUser);

    // Verify we got exactly 7 connections
    assertEquals(7, result.getConnections().size(), "Should have exactly 7 connection types");

    // Create a map of connection names to their inUse status
    final Map<String, Boolean> connectionInUseMap =
        result.getConnections().stream()
            .collect(
                HashMap::new,
                (map, conn) -> map.put(conn.getName(), conn.getInUse()),
                HashMap::putAll);

    // Assert inUse flags are set correctly - exact assertions since we know all 7 are returned
    // Connections that should be in use
    assertTrue(
        connectionInUseMap.get("kafka-connection"), "Kafka connection should be marked as in use");
    assertTrue(
        connectionInUseMap.get("cluster-connection"),
        "Cluster connection should be marked as in use");
    assertTrue(
        connectionInUseMap.get("https-connection"), "HTTPS connection should be marked as in use");
    assertTrue(
        connectionInUseMap.get("kinesis-connection"),
        "Kinesis connection should be marked as in use");

    // Connections that should NOT be in use
    assertFalse(
        connectionInUseMap.get("sample-connection"),
        "Sample connection should NOT be marked as in use");
    assertFalse(
        connectionInUseMap.get("s3-connection"), "S3 connection should NOT be marked as in use");
    assertFalse(
        connectionInUseMap.get("lambda-connection"),
        "Lambda connection should NOT be marked as in use");
  }

  // Helper methods for creating mock connections
  private KafkaConnection createMockKafkaConnection(ObjectId tenantId) {
    final NDSDataLakeStreamsNetworking networking =
        new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.PUBLIC).build());
    final NDSDataLakeKafkaSASLPlain auth =
        new NDSDataLakeKafkaSASLPlain(new KafkaAuthUsernamePassword("user", "pass"));
    final NDSDataLakeKafkaSecurityPlain security = new NDSDataLakeKafkaSecurityPlain();

    return new KafkaConnection(
        new ObjectId(),
        tenantId,
        "kafka-connection",
        new Date(),
        new Date(),
        auth,
        security,
        new HashMap<>(),
        networking,
        null);
  }

  private ClusterConnection createMockClusterConnection(ObjectId tenantId, ObjectId groupId) {
    return new ClusterConnection(
        new ObjectId(),
        tenantId,
        "cluster-connection",
        new Date(),
        new Date(),
        new ObjectId().toHexString(), // cluster
        new DBRoleToExecute(DBRoleType.BUILT_IN, "readWriteAnyDatabase"),
        groupId.toHexString()); // clusterGroupId - use test groupId for cross-group access
  }

  private SampleConnection createMockSampleConnection(ObjectId tenantId) {
    return new SampleConnection(
        new ObjectId(), tenantId, "sample-connection", new Date(), new Date());
  }

  private HttpsConnection createMockHttpsConnection(ObjectId tenantId) {
    return new HttpsConnection(
        new ObjectId(),
        tenantId,
        "https-connection",
        new Date(),
        new Date(),
        "https://example.com",
        new HashMap<>());
  }

  private S3Connection createMockS3Connection(ObjectId tenantId) {
    final ObjectId mockRoleId =
        new ObjectId("507f1f77bcf86cd799439011"); // Same as in cloud provider access mock
    final AWSS3ConnectionConfig awsConfig =
        new AWSS3ConnectionConfig(
            mockRoleId.toHexString(), "arn:aws:iam::************:role/test-role", "test-bucket");
    final NDSDataLakeStreamsNetworking networking =
        new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.PUBLIC).build());
    return new S3Connection(
        new ObjectId(),
        tenantId,
        "s3-connection",
        new Date(),
        new Date(),
        awsConfig,
        networking,
        null);
  }

  private AWSLambdaConnection createMockLambdaConnection(ObjectId tenantId) {
    final ObjectId mockRoleId =
        new ObjectId("507f1f77bcf86cd799439011"); // Same as in cloud provider access mock
    final AWSConnectionConfig awsConfig =
        new AWSConnectionConfig(
            mockRoleId.toHexString(), "arn:aws:iam::************:role/test-role");
    return new AWSLambdaConnection(
        new ObjectId(), tenantId, "lambda-connection", new Date(), new Date(), awsConfig);
  }

  private AWSKinesisDataStreamsConnection createMockKinesisConnection(ObjectId tenantId) {
    final ObjectId mockRoleId =
        new ObjectId("507f1f77bcf86cd799439011"); // Same as in cloud provider access mock
    final AWSConnectionConfig awsConfig =
        new AWSConnectionConfig(
            mockRoleId.toHexString(), "arn:aws:iam::************:role/test-role");
    final NDSDataLakeStreamsNetworking networking =
        new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.PUBLIC).build());
    return new AWSKinesisDataStreamsConnection(
        new ObjectId(),
        tenantId,
        "kinesis-connection",
        new Date(),
        new Date(),
        awsConfig,
        networking,
        null);
  }

  // Helper methods for creating storage configs
  private Document createStorageConfigWithAllConnectionTypes() {
    return new Document(
        FieldDefs.STORES,
        List.of(
            new Document()
                .append(StreamsConnectionView.FieldDefs.NAME, "kafka-connection")
                .append(StreamsConnectionView.FieldDefs.PROVIDER, ProviderValues.KAFKA)
                .append(
                    StreamsConnectionView.FieldDefs.BOOTSTRAP_SERVERS, List.of("localhost:9092")),
            new Document()
                .append(StreamsConnectionView.FieldDefs.NAME, "cluster-connection")
                .append(StreamsConnectionView.FieldDefs.PROVIDER, ProviderValues.ATLAS)
                .append(StreamsConnectionView.FieldDefs.CLUSTER_NAME, "test-cluster"),
            new Document()
                .append(StreamsConnectionView.FieldDefs.NAME, "sample-connection")
                .append(StreamsConnectionView.FieldDefs.PROVIDER, ProviderValues.IN_MEMORY),
            new Document()
                .append(StreamsConnectionView.FieldDefs.NAME, "https-connection")
                .append(StreamsConnectionView.FieldDefs.PROVIDER, ProviderValues.HTTPS),
            new Document()
                .append(StreamsConnectionView.FieldDefs.NAME, "s3-connection")
                .append(StreamsConnectionView.FieldDefs.PROVIDER, ProviderValues.STREAMS_S3_SINK),
            new Document()
                .append(StreamsConnectionView.FieldDefs.NAME, "lambda-connection")
                .append(StreamsConnectionView.FieldDefs.PROVIDER, ProviderValues.AWS_LAMBDA),
            new Document()
                .append(StreamsConnectionView.FieldDefs.NAME, "kinesis-connection")
                .append(
                    StreamsConnectionView.FieldDefs.PROVIDER,
                    ProviderValues.AWS_KINESIS_DATA_STREAMS)));
  }

  // ==================== Tests for ProxyInfo Creation Logic ====================

  @Test
  public void testCreateConnectionWithPublicNetworking_noProxyInfoGeneration() throws SvcException {
    // Arrange
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final StreamsKafkaConnectionView kafkaConnectionView =
        new StreamsKafkaConnectionView(
            "publicKafkaConnection",
            "host:2020",
            new NDSDataLakeKafkaSecurityPlain(),
            new NDSDataLakeKafkaSASLPlain(new KafkaAuthUsernamePassword("Bob", "hunter2")),
            new HashMap<>(),
            new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.PUBLIC).build()),
            null); // No ProxyInfo initially

    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    // Act
    StreamsConnectionView result =
        _streamsSvc.createConnection(kafkaConnectionView, "testConnection", group, auditInfo, null);

    // Assert
    assertNotNull(result);
    assertEquals("publicKafkaConnection", result.getName());
    // Verify that the connection was processed without throwing exceptions
    // This tests that the private networking branch is NOT executed for public connections
    verify(_streamsKafkaConnectionDao, times(1)).save(argThat(x -> (x.getProxyInfo() == null)));
  }

  @Test
  public void testCreateConnectionWithPrivateNetworking_successfulCreation() throws SvcException {
    // Arrange
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    setupMocksForConnection(mockedTenant);

    final StreamsKafkaConnectionView kafkaConnectionView =
        new StreamsKafkaConnectionView(
            "privateKafkaConnection",
            "host:2020",
            new NDSDataLakeKafkaSecuritySSL(new KafkaSSLSecret("")),
            new NDSDataLakeKafkaSASLPlain(new KafkaAuthUsernamePassword("Bob", "hunter2")),
            new HashMap<>(),
            new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.VPC).build()),
            null); // No ProxyInfo initially

    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    // Act
    StreamsConnectionView result =
        _streamsSvc.createConnection(kafkaConnectionView, "testConnection", group, auditInfo, null);

    // Assert
    assertNotNull(result);
    assertEquals("privateKafkaConnection", result.getName());
    // Verify that the connection was processed without throwing exceptions
    // Note: ProxyInfo is set on the model but may not be reflected in the returned view
    verify(_streamsKafkaConnectionDao, times(1))
        .save(
            argThat(
                x ->
                    (x.getProxyInfo() != null)
                        && !x.getProxyInfo().getAuthKey().isBlank()
                        && x.getProxyInfo().getDnsName() == null));
  }

  @Test
  public void testCreateConnectionWithPrivateNetworking_existingProxyInfoHandled()
      throws SvcException {
    // Arrange
    final StreamsTenant mockedTenant = mock(StreamsTenant.class);
    ObjectId tenantId = new ObjectId();
    doReturn(tenantId).when(mockedTenant).getId();
    setupMocksForConnection(mockedTenant);

    ProxyInfo existingProxyInfo = new ProxyInfo();
    existingProxyInfo.setAuthKey("existing-key");
    existingProxyInfo.setDnsName("existing-dns");

    final StreamsKafkaConnectionView kafkaConnectionView =
        new StreamsKafkaConnectionView(
            "privateKafkaConnection",
            "host:2020",
            new NDSDataLakeKafkaSecuritySSL(new KafkaSSLSecret("")),
            new NDSDataLakeKafkaSASLPlain(new KafkaAuthUsernamePassword("Bob", "hunter2")),
            new HashMap<>(),
            new NDSDataLakeStreamsNetworking(new Access.AccessBuilder(Type.VPC).build()),
            existingProxyInfo); // Existing ProxyInfo

    final Group group = mock(Group.class);
    final AuditInfo auditInfo = mock(AuditInfo.class);

    // Act
    StreamsConnectionView result =
        _streamsSvc.createConnection(kafkaConnectionView, "testConnection", group, auditInfo, null);

    // Assert
    assertNotNull(result);
    assertEquals("privateKafkaConnection", result.getName());
    // Verify that the connection was processed without throwing exceptions
    // The new logic always creates a new ProxyInfo() regardless of existing one
    // This tests the branch where existing ProxyInfo is ignored and new one is created
    verify(_streamsKafkaConnectionDao, times(1))
        .save(
            argThat(
                x ->
                    !x.getProxyInfo().getAuthKey().isBlank()
                        && !x.getProxyInfo().getAuthKey().equals(existingProxyInfo.getAuthKey())
                        && x.getProxyInfo().getDnsName() == null));
  }
}
