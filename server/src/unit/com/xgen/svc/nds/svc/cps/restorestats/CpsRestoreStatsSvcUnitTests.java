package com.xgen.svc.nds.svc.cps.restorestats;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.atm.agentjobs._public.svc.AgentJobSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.cps.core._public.svc.CpsFeatureFlagSvc;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._public.model.AttachStats;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.DirectAttachReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.StreamingReplicaSetRestoreJob;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CpsRestoreStatsSvcUnitTests {
  private static final ObjectId RESTORE_JOB_ID = new ObjectId();
  private static final DirectAttachReplicaSetBackupRestoreJob restoreJob =
      mock(DirectAttachReplicaSetBackupRestoreJob.class);
  @Mock private BackupRestoreJobDao backupRestoreJobDao;

  @Mock private CpsFeatureFlagSvc cpsFeatureFlagSvc;

  @Mock private AgentJobSvc agentJobSvc;

  @InjectMocks private CpsRestoreStatsSvc cpsRestoreStatsSvc;

  @Test
  public void testSubmitRestoreStatsJobs_RestoreJobNotFound() throws SvcException {
    when(backupRestoreJobDao.findReplicaSetById(RESTORE_JOB_ID)).thenReturn(null);

    cpsRestoreStatsSvc.submitDirectAttachRestoreStatsJobs(RESTORE_JOB_ID);
    verify(agentJobSvc, never()).putJob(any());
  }

  @Test
  public void testSubmitRestoreStatsJobs_InvalidStrategyType() throws SvcException {
    when(backupRestoreJobDao.findReplicaSetById(RESTORE_JOB_ID)).thenReturn(restoreJob);
    when(restoreJob.getTargetProjectId()).thenReturn(Optional.of(new ObjectId()));
    when(restoreJob.getStrategyType()).thenReturn(CpsRestoreMetadata.StrategyName.STREAMING);
    when(cpsFeatureFlagSvc.isCpsCollectDirectAttachRestoreStatsEnabled(any())).thenReturn(true);

    cpsRestoreStatsSvc.submitDirectAttachRestoreStatsJobs(RESTORE_JOB_ID);

    verify(agentJobSvc, never()).putJob(any());
  }

  @Test
  public void testSubmitRestoreStatsJobs_Success() throws SvcException {
    when(backupRestoreJobDao.findReplicaSetById(RESTORE_JOB_ID)).thenReturn(restoreJob);
    when(restoreJob.getTargetProjectId()).thenReturn(Optional.of(new ObjectId()));
    when(restoreJob.getStrategyType()).thenReturn(CpsRestoreMetadata.StrategyName.DIRECT_ATTACH);
    when(restoreJob.getFinishedDate()).thenReturn(Optional.of(new java.util.Date()));

    final DirectAttachReplicaSetBackupRestoreJob.AttachStatus attachStatus =
        mock(DirectAttachReplicaSetBackupRestoreJob.AttachStatus.class);
    when(attachStatus.getHostname()).thenReturn("test-host");
    when(attachStatus.getTimeAttached()).thenReturn(new Date());
    when(restoreJob.getAttachStatuses()).thenReturn(List.of(attachStatus));
    when(cpsFeatureFlagSvc.isCpsCollectDirectAttachRestoreStatsEnabled(any())).thenReturn(true);

    cpsRestoreStatsSvc.submitDirectAttachRestoreStatsJobs(RESTORE_JOB_ID);

    verify(agentJobSvc, times(1)).putJob(any());
  }

  @Test
  public void testUpdateAttachStatsMetrics_Success() throws SvcException {
    final String hostname = "test-hostname";
    final AttachStats attachStats =
        new AttachStats(
            null, // mongoStartupStats - not updating
            null, // mongoShutdownStats - not updating
            150L, // detachVolumeDurationMilliseconds - override existing value
            null, // createVolumeFromSnapshotDurationSeconds - not updating
            null, // attachVolumeDurationMilliseconds - not updating
            null, // mountVolumeWithSnapshotDataDurationMilliseconds - not updating
            null, // diskType - not updating
            null, // bounceStopIfUpWithForceKillDurationSeconds - not updating
            null, // getDirectAttachFileListDurationSeconds - not updating
            800L, // diskPreWarmDurationSeconds - override existing value
            null, // diskPreWarmThroughputMbPerSecond - not updating
            null, // pitMetaReaderDurationSeconds - not updating
            null, // pitPullerDurationSeconds - not updating
            null, // pitOpProviderDurationSeconds - not updating
            null // pitInsertOpsDurationSeconds - not updating
            );

    when(backupRestoreJobDao.findReplicaSetById(RESTORE_JOB_ID)).thenReturn(restoreJob);

    cpsRestoreStatsSvc.updateAttachStatsMetrics(RESTORE_JOB_ID, hostname, attachStats);

    verify(backupRestoreJobDao)
        .updateDirectAttachRestoreAttachStatsMetrics(RESTORE_JOB_ID, hostname, attachStats);
  }

  @Test
  public void testUpdateAttachStatsMetrics_RestoreJobNotFound() {
    final String hostname = "test-hostname";
    final AttachStats attachStats =
        new AttachStats(
            null, null, 150L, null, null, null, null, null, null, null, null, null, null, null,
            null);

    when(backupRestoreJobDao.findReplicaSetById(RESTORE_JOB_ID)).thenReturn(null);

    try {
      cpsRestoreStatsSvc.updateAttachStatsMetrics(RESTORE_JOB_ID, hostname, attachStats);
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.RESTORE_JOB_NOT_FOUND, e.getErrorCode());
    }

    verify(backupRestoreJobDao, never())
        .updateDirectAttachRestoreAttachStatsMetrics(any(), any(), any());
  }

  @Test
  public void testUpdateAttachStatsMetrics_NotDirectAttachJob() {
    final String hostname = "test-hostname";
    final AttachStats attachStats =
        new AttachStats(
            null, null, 150L, null, null, null, null, null, null, null, null, null, null, null,
            null);
    final StreamingReplicaSetRestoreJob streamingJob = mock(StreamingReplicaSetRestoreJob.class);
    when(streamingJob.getStrategyType()).thenReturn(CpsRestoreMetadata.StrategyName.STREAMING);
    when(backupRestoreJobDao.findReplicaSetById(RESTORE_JOB_ID)).thenReturn(streamingJob);
    try {
      cpsRestoreStatsSvc.updateAttachStatsMetrics(RESTORE_JOB_ID, hostname, attachStats);
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.RESTORE_JOB_UNEXPECTED_STRATEGY, e.getErrorCode());
    }

    verify(backupRestoreJobDao, never())
        .updateDirectAttachRestoreAttachStatsMetrics(any(), any(), any());
  }

  @Test
  public void testUpdateAttachStatsMetrics_EmptyMetrics() throws SvcException {
    final String hostname = "test-hostname";
    final AttachStats emptyAttachStats =
        new AttachStats(
            null, null, null, null, null, null, null, null, null, null, null, null, null, null,
            null);

    when(backupRestoreJobDao.findReplicaSetById(RESTORE_JOB_ID)).thenReturn(restoreJob);

    cpsRestoreStatsSvc.updateAttachStatsMetrics(RESTORE_JOB_ID, hostname, emptyAttachStats);

    verify(backupRestoreJobDao)
        .updateDirectAttachRestoreAttachStatsMetrics(RESTORE_JOB_ID, hostname, emptyAttachStats);
  }
}
