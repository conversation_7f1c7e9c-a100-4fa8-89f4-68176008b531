load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*UnitTests.java"]),
    deny_warnings = True,
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/access/authz",
        "//server/src/main/com/xgen/cloud/access/role",
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/atm/publish",
        "//server/src/main/com/xgen/cloud/auditinfosvc",
        "//server/src/main/com/xgen/cloud/billingimport/snapshot",
        "//server/src/main/com/xgen/cloud/brs/restore",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/brs",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/common/jobqueue",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/mongo",
        "//server/src/main/com/xgen/cloud/common/security",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/cps/agent",
        "//server/src/main/com/xgen/cloud/cps/agent/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/backupjob",
        "//server/src/main/com/xgen/cloud/cps/backupjob/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/billing",
        "//server/src/main/com/xgen/cloud/cps/billing/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/core",
        "//server/src/main/com/xgen/cloud/cps/core/_public/config",
        "//server/src/main/com/xgen/cloud/cps/pit",
        "//server/src/main/com/xgen/cloud/cps/pit/_private/dao",
        "//server/src/main/com/xgen/cloud/cps/restore",
        "//server/src/main/com/xgen/cloud/cps/restore/_private/dao",
        "//server/src/main/com/xgen/cloud/deployment",
        "//server/src/main/com/xgen/cloud/email",
        "//server/src/main/com/xgen/cloud/externalanalytics",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/group/_private/dao",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/topology",
        "//server/src/main/com/xgen/cloud/nds/activity",
        "//server/src/main/com/xgen/cloud/nds/admin",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/aws/_private/model",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/azure/_private/model",
        "//server/src/main/com/xgen/cloud/nds/billing",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/cluster/common/context",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/main/com/xgen/cloud/nds/dbcheck",
        "//server/src/main/com/xgen/cloud/nds/dns",
        "//server/src/main/com/xgen/cloud/nds/free",
        "//server/src/main/com/xgen/cloud/nds/gcp",
        "//server/src/main/com/xgen/cloud/nds/gcp/_private/model",
        "//server/src/main/com/xgen/cloud/nds/metrics/_public/svc",
        "//server/src/main/com/xgen/cloud/nds/mtmcompaction",
        "//server/src/main/com/xgen/cloud/nds/planning/common",
        "//server/src/main/com/xgen/cloud/nds/planning/tracing",
        "//server/src/main/com/xgen/cloud/nds/privatelink",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/project/_private/dao",
        "//server/src/main/com/xgen/cloud/nds/replicasethardware",
        "//server/src/main/com/xgen/cloud/nds/rollingresync",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/nds/tenant",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/organization/_private/dao",
        "//server/src/main/com/xgen/cloud/search/external",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/module/common/planner",
        "//server/src/main/com/xgen/module/metering/client/svc",
        "//server/src/main/com/xgen/module/metering/client/svc/api_2023_05_03",
        "//server/src/main/com/xgen/module/metering/common/exception",
        "//server/src/main/com/xgen/module/metering/common/model",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//server/src/main/com/xgen/svc/mms/model/grouptype",
        "//server/src/test/com/xgen/testlib/junit5/extensions",
        "//server/src/unit/com/xgen/cloud/nds/project/_public/model:commonTestUtil",
        "//server/src/unit/com/xgen/svc/atm",
        "//server/src/unit/com/xgen/svc/mms/util",
        "//server/src/unit/com/xgen/svc/nds/model",
        "//third_party:driverwrappers",
        "@maven//:com_amazonaws_aws_java_sdk_core",
        "@maven//:com_amazonaws_aws_java_sdk_ec2",
        "@maven//:com_amazonaws_aws_java_sdk_kms",
        "@maven//:com_amazonaws_aws_java_sdk_sts",
        "@maven//:com_github_luben_zstd_jni",
        "@maven//:com_google_apis_google_api_services_compute",
        "@maven//:com_google_auth_google_auth_library_oauth2_http",
        "@maven//:io_github_hakky54_logcaptor",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:junit_junit",
        "@maven//:org_assertj_assertj_core",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_junit_jupiter_junit_jupiter_params",
        "@maven//:org_mockito_mockito_core",
        "@maven//:org_mockito_mockito_junit_jupiter",
    ],
)
