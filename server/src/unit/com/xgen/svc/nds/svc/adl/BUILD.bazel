load("//server/src/unit:rules.bzl", "unit_package")

unit_package(
    name = "TestLibrary",
    srcs = glob(["*UnitTests.java"]),
    deps = [
        "//server/src/main",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/nds/aws",
        "//server/src/main/com/xgen/cloud/nds/azure",
        "//server/src/main/com/xgen/cloud/nds/cloudprovider",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/datalake",
        "//server/src/test/com/xgen/svc/nds/svc/adl:utils",
        "//server/src/test/com/xgen/testlib/junit5/extensions",
        "//server/src/unit/com/xgen/cloud/nds/datalake/_public/model:datalakeTestUtil",
        "@com_xgen_mdb_idl//:mhouse_definitions",
        "@io_grpc_grpc_java//api",
        "@io_grpc_grpc_java//protobuf",
        "@io_grpc_grpc_java//stub",
        "@maven//:com_google_api_grpc_proto_google_common_protos",
        "@maven//:io_prometheus_simpleclient",
        "@maven//:jakarta_servlet_jakarta_servlet_api",
        "@maven//:org_junit_jupiter_junit_jupiter_api",
        "@maven//:org_junit_jupiter_junit_jupiter_params",
        "@maven//:org_mockito_mockito_core",
    ],
)
