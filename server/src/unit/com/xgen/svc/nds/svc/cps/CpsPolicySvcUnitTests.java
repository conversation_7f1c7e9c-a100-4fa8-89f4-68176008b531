package com.xgen.svc.nds.svc.cps;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.email.EmailValidation;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob.AutoExportSettings;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.backupjob._public.model.CopySetting;
import com.xgen.cloud.cps.backupjob._public.model.DataProtectionSettings;
import com.xgen.cloud.cps.backupjob._public.model.DataProtectionSettings.DataProtectionSettingsBuilder;
import com.xgen.cloud.cps.backupjob._public.model.DataProtectionSettings.State;
import com.xgen.cloud.cps.backupjob._public.model.ExtraRetentionSetting;
import com.xgen.cloud.cps.backupjob._public.model.Policy;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.cloud.cps.backupjob._public.ui.BackupSnapshotRetentionView;
import com.xgen.cloud.cps.backupjob._public.ui.DataProtectionClusterView;
import com.xgen.cloud.cps.backupjob._public.ui.DeleteCopiedBackupsView;
import com.xgen.cloud.cps.restore._private.dao.BackupSnapshotDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.ExportBucket;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cluster.common.context._public.model.ClusterUpdateContext;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.StorageSystem;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.util.UnitTestUtils;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(CloudProviderExtension.class)
public class CpsPolicySvcUnitTests {

  private final Logger LOG = LoggerFactory.getLogger(CpsPolicySvcUnitTests.class);

  public static final ObjectId GROUP_ID = new ObjectId();
  public static final ObjectId EXPORT_BUCKET_ID = new ObjectId();
  private static final ObjectId POLICY_ID = new ObjectId();
  private static final int REFERENCE_TIME = 10;
  private static final int UPDATED_REFERENCE_TIME = 20;
  private static final Date NEXT_SNAPSHOT_DATE =
      TimeUtils.fromISOString("2019-02-18T00:10:00.111Z");
  private static final ObjectId POLICY_ITEM_ID_1 = new ObjectId();
  private static final ObjectId POLICY_ITEM_ID_2 = new ObjectId();
  private static final ObjectId POLICY_ITEM_ID_3 = new ObjectId();
  private static final ObjectId HOURLY_POLICY_ITEM_ID = new ObjectId();

  private static final PolicyItem POLICY_ITEM_1 =
      new PolicyItem(
          POLICY_ITEM_ID_1,
          BackupFrequencyType.DAILY,
          1,
          NEXT_SNAPSHOT_DATE,
          Duration.ofDays(3),
          BackupRetentionUnit.DAYS);
  private static final PolicyItem POLICY_ITEM_2 =
      new PolicyItem(
          POLICY_ITEM_ID_2,
          BackupFrequencyType.DAILY,
          1,
          NEXT_SNAPSHOT_DATE,
          Duration.ofDays(2),
          BackupRetentionUnit.DAYS);
  // same as POLICY_ITEM_2 but with a weekly frequency of 4 days (updated)
  private static final PolicyItem POLICY_ITEM_3 =
      new PolicyItem(
          POLICY_ITEM_ID_2,
          BackupFrequencyType.WEEKLY,
          4,
          NEXT_SNAPSHOT_DATE,
          Duration.ofDays(7),
          BackupRetentionUnit.DAYS);
  private static final PolicyItem HOURLY_POLICY_ITEM =
      new PolicyItem(
          HOURLY_POLICY_ITEM_ID,
          BackupFrequencyType.HOURLY,
          6,
          NEXT_SNAPSHOT_DATE,
          Duration.ofDays(7),
          BackupRetentionUnit.DAYS);

  private static final PolicyItem NEW_POLICY_ITEM_1 =
      new PolicyItem(
          POLICY_ITEM_ID_1,
          BackupFrequencyType.DAILY,
          1,
          Duration.ofDays(3),
          BackupRetentionUnit.DAYS);
  private static final PolicyItem NEW_POLICY_ITEM_2 =
      new PolicyItem(
          POLICY_ITEM_ID_2,
          BackupFrequencyType.DAILY,
          1,
          Duration.ofDays(2),
          BackupRetentionUnit.DAYS);
  // same as POLICY_ITEMS_VIEW_2 without an id (for adding a new item)
  private static final PolicyItem NEW_POLICY_ITEM_3 =
      new PolicyItem(
          new ObjectId(),
          BackupFrequencyType.DAILY,
          1,
          Duration.ofDays(2),
          BackupRetentionUnit.DAYS);
  // same as POLICY_ITEMS_VIEW_2 but with an updated
  private static final PolicyItem NEW_POLICY_ITEM_4 =
      new PolicyItem(
          POLICY_ITEM_ID_2,
          BackupFrequencyType.WEEKLY,
          4,
          Duration.ofDays(7),
          BackupRetentionUnit.DAYS);

  private static final PolicyItem NEW_POLICY_ITEM_5 =
      new PolicyItem(
          new ObjectId(),
          BackupFrequencyType.MONTHLY,
          4,
          Duration.ofDays(31),
          BackupRetentionUnit.DAYS);

  private static final PolicyItem NEW_POLICY_ITEM_6 =
      new PolicyItem(
          new ObjectId(),
          BackupFrequencyType.YEARLY,
          4,
          Duration.ofDays(372),
          BackupRetentionUnit.DAYS);
  // invalid duration policy
  private static final PolicyItem BAD_POLICY_1 =
      new PolicyItem(
          new ObjectId(),
          BackupFrequencyType.WEEKLY,
          1,
          Duration.ofDays(6),
          BackupRetentionUnit.DAYS);
  private static final PolicyItem BAD_POLICY_2 =
      new PolicyItem(
          new ObjectId(),
          BackupFrequencyType.MONTHLY,
          1,
          Duration.ofDays(22),
          BackupRetentionUnit.DAYS);
  private static final PolicyItem BAD_POLICY_3 =
      new PolicyItem(
          new ObjectId(),
          BackupFrequencyType.YEARLY,
          1,
          Duration.ofDays(220),
          BackupRetentionUnit.DAYS);
  private static final PolicyItem GOOD_POLICY_1 =
      new PolicyItem(
          new ObjectId(),
          BackupFrequencyType.MONTHLY,
          1,
          Duration.ofDays(34),
          BackupRetentionUnit.DAYS);

  private static final PolicyItem GOOD_POLICY_2 =
      new PolicyItem(
          new ObjectId(),
          BackupFrequencyType.YEARLY,
          1,
          Duration.ofDays(400),
          BackupRetentionUnit.DAYS);

  private static final Policy POLICY_WITH_DAILY_AND_WEEKLY =
      new Policy(POLICY_ID, Arrays.asList(NEW_POLICY_ITEM_1, NEW_POLICY_ITEM_4));

  private static final Policy POLICY_WITH_MONTHLY = new Policy(POLICY_ID, List.of(GOOD_POLICY_1));

  private static final Policy POLICY_WITH_YEARLY = new Policy(POLICY_ID, List.of(GOOD_POLICY_2));

  private static final Policy POLICY_WITH_HOURLY =
      new Policy(POLICY_ID, List.of(HOURLY_POLICY_ITEM));

  private static final Set<AzureRegionName> AVAILABLE_PV2_REGIONS =
      Set.of(
          AzureRegionName.US_EAST,
          AzureRegionName.US_EAST_2,
          AzureRegionName.US_WEST_2,
          AzureRegionName.EUROPE_WEST,
          AzureRegionName.EUROPE_NORTH,
          AzureRegionName.UK_SOUTH,
          AzureRegionName.AUSTRALIA_EAST,
          AzureRegionName.SWEDEN_CENTRAL,
          AzureRegionName.SWITZERLAND_NORTH,
          AzureRegionName.UAE_NORTH);

  @Captor private ArgumentCaptor<List<PolicyItem>> _policyItemListCaptor;
  @Mock private AppSettings _appSettings;
  @Mock private CpsExportSvc _cpsExportSvc;
  @Mock private GroupDao _groupDao;
  @Mock private ClusterDescriptionDao _clusterDescriptionDao;
  @Mock private BackupSnapshotDao _backupSnapshotDao;
  @Mock private CpsSvc _cpsSvc;
  @Mock private NDSGroupSvc _ndsGroupSvc;
  @Mock private NDSClusterSvc _ndsClusterSvc;
  @Mock private BackupJobDao _backupJobDao;
  @Mock private AuthzSvc _authzSvc;
  @InjectMocks @Spy private CpsPolicySvc _underTest;

  private MockedStatic<FeatureFlagSvc> mockedStaticFeatureFlagSvc;

  @BeforeEach
  public void setup() {
    final ObjectId groupId = new ObjectId();
    final Group group = mock(Group.class);
    doReturn(group).when(_groupDao).findById(groupId);

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    doReturn(Optional.of(ndsGroup)).when(_ndsGroupSvc).find(GROUP_ID);

    doReturn(AVAILABLE_PV2_REGIONS).when(_ndsClusterSvc).getAzureSsdV2Regions(any(Group.class));
    doReturn(AVAILABLE_PV2_REGIONS).when(_ndsClusterSvc).getAzureSsdV2Regions(any(ObjectId.class));
    final ExportBucket exportBucket = mock(ExportBucket.class);
    when(exportBucket.getId()).thenReturn(EXPORT_BUCKET_ID);
    when(exportBucket.getProjectId()).thenReturn(GROUP_ID);
    when(_cpsExportSvc.findExportBucket(EXPORT_BUCKET_ID)).thenReturn(Optional.of(exportBucket));
    doReturn(EmailValidation.STRICT).when(_appSettings).getEmailValidationMode();
    doReturn(true)
        .when(_cpsSvc)
        .isBackupCompliancePolicyPostGaFeatureFlagEnabled(any(ObjectId.class));

    // this is to stub out feature flag checks
    mockedStaticFeatureFlagSvc = Mockito.mockStatic(FeatureFlagSvc.class);
    mockedStaticFeatureFlagSvc
        .when(() -> isFeatureFlagEnabled(any(), any(), any(), any()))
        .thenReturn(false);
    mockedStaticFeatureFlagSvc
        .when(
            () ->
                isFeatureFlagEnabled(
                    eq(FeatureFlag.CPS_SNAPSHOT_EXPORT_HIGH_FREQUENCY), any(), any(), any()))
        .thenReturn(true);
  }

  @AfterEach
  public void teardown() {
    mockedStaticFeatureFlagSvc.close();
  }

  @Test
  public void testValidateWeeklyAndMonthlyAndYearlyPolicy() throws SvcException {

    try {
      CpsPolicySvc.validateWeeklyAndMonthlyAndYearlyPolicy(List.of(BAD_POLICY_1));
      fail("should be a validation failure");
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
    }

    try {
      CpsPolicySvc.validateWeeklyAndMonthlyAndYearlyPolicy(List.of(BAD_POLICY_2));
      fail("should be a validation failure");
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
    }

    try {
      CpsPolicySvc.validateWeeklyAndMonthlyAndYearlyPolicy(List.of(BAD_POLICY_3));
      fail("should be a validation failure");
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
    }

    CpsPolicySvc.validateWeeklyAndMonthlyAndYearlyPolicy(List.of(POLICY_ITEM_3));
    CpsPolicySvc.validateWeeklyAndMonthlyAndYearlyPolicy(List.of(GOOD_POLICY_1));
    CpsPolicySvc.validateWeeklyAndMonthlyAndYearlyPolicy(List.of(GOOD_POLICY_2));
  }

  @Test
  public void testValidateExportSettings_disabled() throws SvcException {
    _underTest.validateAutoExportSettings(
        false, null, Collections.singletonList(POLICY_WITH_DAILY_AND_WEEKLY), GROUP_ID);
  }

  @Test
  public void testValidateExportSettings_enabledValid() throws SvcException {
    final AutoExportSettings dailyExport =
        new AutoExportSettings(BackupFrequencyType.DAILY, EXPORT_BUCKET_ID);
    final AutoExportSettings weeklyExport =
        new AutoExportSettings(BackupFrequencyType.WEEKLY, EXPORT_BUCKET_ID);

    _underTest.validateAutoExportSettings(
        true, dailyExport, Collections.singletonList(POLICY_WITH_DAILY_AND_WEEKLY), GROUP_ID);

    _underTest.validateAutoExportSettings(
        true, weeklyExport, Collections.singletonList(POLICY_WITH_DAILY_AND_WEEKLY), GROUP_ID);
  }

  @Test
  public void testValidateExportSettings_badFrequencyType() {
    final AutoExportSettings hourlyExport =
        new AutoExportSettings(BackupFrequencyType.HOURLY, EXPORT_BUCKET_ID);

    assertThrows(
        SvcException.class,
        () ->
            _underTest.validateAutoExportSettings(
                true, hourlyExport, Collections.singletonList(POLICY_WITH_HOURLY), GROUP_ID));
  }

  @Test
  public void testValidateExportSettings_badFrequencyType2() {
    final AutoExportSettings onDemandExport =
        new AutoExportSettings(BackupFrequencyType.ON_DEMAND, EXPORT_BUCKET_ID);

    assertThrows(
        SvcException.class,
        () ->
            _underTest.validateAutoExportSettings(
                true, onDemandExport, Collections.singletonList(POLICY_WITH_HOURLY), GROUP_ID));
  }

  @Test
  public void testValidateExportSettings_exportFrequencyHasNoPolicy() {
    final AutoExportSettings monthlyExport =
        new AutoExportSettings(BackupFrequencyType.MONTHLY, EXPORT_BUCKET_ID);

    assertThrows(
        SvcException.class,
        () ->
            _underTest.validateAutoExportSettings(
                true,
                monthlyExport,
                Collections.singletonList(POLICY_WITH_DAILY_AND_WEEKLY),
                GROUP_ID));
  }

  @Test
  public void testValidateExportSettings_noSettings() {
    assertThrows(
        SvcException.class,
        () ->
            _underTest.validateAutoExportSettings(
                true, null, Collections.singletonList(POLICY_WITH_DAILY_AND_WEEKLY), GROUP_ID));
  }

  @Test
  public void testValidateExportSettings_settingsWithDisabled() {
    final AutoExportSettings monthlyExport =
        new AutoExportSettings(BackupFrequencyType.MONTHLY, EXPORT_BUCKET_ID);

    assertThrows(
        SvcException.class,
        () ->
            _underTest.validateAutoExportSettings(
                false, monthlyExport, List.of(POLICY_WITH_DAILY_AND_WEEKLY), GROUP_ID));
  }

  @Test
  public void testValidateExportSettings_noBucketId() {
    final AutoExportSettings monthlyExport =
        new AutoExportSettings(BackupFrequencyType.MONTHLY, null);

    assertThrows(
        SvcException.class,
        () ->
            _underTest.validateAutoExportSettings(
                true, monthlyExport, List.of(POLICY_WITH_DAILY_AND_WEEKLY), GROUP_ID));
  }

  @Test
  public void testValidateExportSettings_invalidBucket() {
    final AutoExportSettings monthlyExport =
        new AutoExportSettings(BackupFrequencyType.MONTHLY, EXPORT_BUCKET_ID);
    doReturn(Optional.empty()).when(_cpsExportSvc).findExportBucket(EXPORT_BUCKET_ID);

    assertThrows(
        SvcException.class,
        () ->
            _underTest.validateAutoExportSettings(
                true, monthlyExport, List.of(POLICY_WITH_DAILY_AND_WEEKLY), GROUP_ID));
  }

  @Test
  public void testValidateExportSettings_noHighFrequencyFeatureFlag() throws SvcException {
    final AutoExportSettings dailyExport =
        new AutoExportSettings(BackupFrequencyType.DAILY, EXPORT_BUCKET_ID);

    _underTest.validateAutoExportSettings(
        true, dailyExport, Collections.singletonList(POLICY_WITH_DAILY_AND_WEEKLY), GROUP_ID);

    mockedStaticFeatureFlagSvc
        .when(
            () ->
                isFeatureFlagEnabled(
                    eq(FeatureFlag.CPS_SNAPSHOT_EXPORT_HIGH_FREQUENCY), any(), any(), any()))
        .thenReturn(false);

    {
      var e =
          assertThrows(
              SvcException.class,
              () ->
                  _underTest.validateAutoExportSettings(
                      true, dailyExport, Collections.singletonList(POLICY_WITH_MONTHLY), GROUP_ID));

      assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
    }
    {
      var e =
          assertThrows(
              SvcException.class,
              () ->
                  _underTest.validateAutoExportSettings(
                      true, dailyExport, Collections.singletonList(POLICY_WITH_YEARLY), GROUP_ID));

      assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
    }
  }

  @Test
  public void testValidatePITOplogWindowSupport() throws SvcException {

    CpsPolicySvc.validateHourlySnapshotPolicyAgainstPITRestoreWindow(
        List.of(new Policy(new ObjectId(), Arrays.asList(HOURLY_POLICY_ITEM, POLICY_ITEM_1))), 2);

    CpsPolicySvc.validateHourlySnapshotPolicyAgainstPITRestoreWindow(
        List.of(new Policy(new ObjectId(), Arrays.asList(HOURLY_POLICY_ITEM, POLICY_ITEM_1))), 7);

    // An empty policy list is allowed regardless of pit.
    // https://jira.mongodb.org/browse/CLOUDP-87012
    CpsPolicySvc.validateHourlySnapshotPolicyAgainstPITRestoreWindow(
        List.of(new Policy(new ObjectId(), List.of())), 8);

    try {
      CpsPolicySvc.validateHourlySnapshotPolicyAgainstPITRestoreWindow(
          List.of(new Policy(new ObjectId(), List.of(POLICY_ITEM_1))), 3);
      fail("No hourly policy. should have failed validation");
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
      assertEquals(
          "Backup policy is invalid: Continuous Cloud Backup requires an hourly policy item",
          e.getMessage());
    }

    try {
      CpsPolicySvc.validateHourlySnapshotPolicyAgainstPITRestoreWindow(
          List.of(new Policy(new ObjectId(), Arrays.asList(HOURLY_POLICY_ITEM, POLICY_ITEM_1))), 8);
      fail("hourly policy retention less than restore window. should fail");
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
      assertEquals(
          "Backup policy is invalid: Hourly policy item retention cannot be less than restore"
              + " window",
          e.getMessage());
    }
  }

  @Test
  public void testGetUpdatedPolicyRefTimeChanged() {
    final CpsPolicySvc policySvc = createEmptyCpsPolicySvc();
    final BackupJob backupJob = mock(BackupJob.class);
    doReturn(REFERENCE_TIME).when(backupJob).getReferenceTime();

    final List<PolicyItem> existingPolicyItems = new LinkedList<>();
    existingPolicyItems.add(POLICY_ITEM_1);
    existingPolicyItems.add(POLICY_ITEM_2);

    final List<PolicyItem> policyItemsViews = new LinkedList<>();
    policyItemsViews.add(NEW_POLICY_ITEM_1);
    policyItemsViews.add(NEW_POLICY_ITEM_2);
    final List<PolicyItem> newAndUpdatedPolicyItems =
        policySvc.getNewAndUpdatedPolicyItems(
            backupJob, existingPolicyItems, policyItemsViews, new Date(), UPDATED_REFERENCE_TIME);

    assertNotEquals(existingPolicyItems, newAndUpdatedPolicyItems);
  }

  @Test
  public void testGetUpdatedPolicyItemsUnchanged() {
    final CpsPolicySvc policySvc = createEmptyCpsPolicySvc();
    final BackupJob backupJob = mock(BackupJob.class);
    doReturn(REFERENCE_TIME).when(backupJob).getReferenceTime();

    final List<PolicyItem> existingPolicyItems = new LinkedList<>();
    existingPolicyItems.add(POLICY_ITEM_1);
    existingPolicyItems.add(POLICY_ITEM_2);

    final List<PolicyItem> policyItemsViews = new LinkedList<>();
    policyItemsViews.add(NEW_POLICY_ITEM_1);
    policyItemsViews.add(NEW_POLICY_ITEM_2);

    final List<PolicyItem> newAndUpdatedPolicyItems =
        policySvc.getNewAndUpdatedPolicyItems(
            backupJob, existingPolicyItems, policyItemsViews, new Date(), REFERENCE_TIME);
    assertEquals(existingPolicyItems, newAndUpdatedPolicyItems);
  }

  @Test
  public void testGetUpdatedPolicyItemsAdditionalItem() {
    final CpsPolicySvc policySvc = createEmptyCpsPolicySvc();
    final BackupJob backupJob = mock(BackupJob.class);
    doReturn(REFERENCE_TIME).when(backupJob).getReferenceTime();

    final List<PolicyItem> existingPolicyItems = new LinkedList<>();
    existingPolicyItems.add(POLICY_ITEM_1);

    final List<PolicyItem> policyItemsViews = new LinkedList<>();
    policyItemsViews.add(NEW_POLICY_ITEM_1);
    policyItemsViews.add(NEW_POLICY_ITEM_3);

    final List<PolicyItem> newAndUpdatedPolicyItems =
        policySvc.getNewAndUpdatedPolicyItems(
            backupJob, existingPolicyItems, policyItemsViews, new Date(), REFERENCE_TIME);

    assertEquals(2, newAndUpdatedPolicyItems.size());
    assertEquals(POLICY_ITEM_1, newAndUpdatedPolicyItems.get(0));

    // The newly added policy item will have a new _id so need to compare individual fields
    final PolicyItem newPolicyItem2 = newAndUpdatedPolicyItems.get(1);
    assertNotNull(newPolicyItem2.getId());
    assertEquals(POLICY_ITEM_2.getFrequencyInterval(), newPolicyItem2.getFrequencyInterval());
    assertEquals(POLICY_ITEM_2.getFrequencyType(), newPolicyItem2.getFrequencyType());
    assertEquals(POLICY_ITEM_2.getRetention(), newPolicyItem2.getRetention());
    assertEquals(POLICY_ITEM_2.getRetentionUnit(), newPolicyItem2.getRetentionUnit());
  }

  @Test
  public void testGetUpdatedPolicyItemsDeletedItem() {
    final CpsPolicySvc policySvc = createEmptyCpsPolicySvc();
    final BackupJob backupJob = mock(BackupJob.class);
    doReturn(REFERENCE_TIME).when(backupJob).getReferenceTime();

    final List<PolicyItem> existingPolicyItems = new LinkedList<>();
    existingPolicyItems.add(POLICY_ITEM_1);
    existingPolicyItems.add(POLICY_ITEM_2);

    final List<PolicyItem> policyItemsViews = new LinkedList<>();
    policyItemsViews.add(POLICY_ITEM_1);

    final List<PolicyItem> expectedPolicyItems = new LinkedList<>();
    expectedPolicyItems.add(POLICY_ITEM_1);

    final List<PolicyItem> newAndUpdatedPolicyItems =
        policySvc.getNewAndUpdatedPolicyItems(
            backupJob, existingPolicyItems, policyItemsViews, new Date(), REFERENCE_TIME);
    assertEquals(expectedPolicyItems, newAndUpdatedPolicyItems);
  }

  @Test
  public void testGetUpdatedPolicyItemsUpdatedItem() {
    final CpsPolicySvc policySvc = createEmptyCpsPolicySvc();
    final BackupJob backupJob = mock(BackupJob.class);
    doReturn(REFERENCE_TIME).when(backupJob).getReferenceTime();

    final List<PolicyItem> existingPolicyItems = new LinkedList<>();
    existingPolicyItems.add(POLICY_ITEM_1);
    existingPolicyItems.add(POLICY_ITEM_2);

    final List<PolicyItem> policyItemsViews = new LinkedList<>();
    policyItemsViews.add(NEW_POLICY_ITEM_1);
    policyItemsViews.add(NEW_POLICY_ITEM_4);
    policyItemsViews.add(NEW_POLICY_ITEM_5);
    policyItemsViews.add(NEW_POLICY_ITEM_6);

    final List<PolicyItem> expectedPolicyItems = new LinkedList<>();
    expectedPolicyItems.add(POLICY_ITEM_1);
    expectedPolicyItems.add(POLICY_ITEM_3);

    final List<PolicyItem> newAndUpdatedPolicyItems =
        policySvc.getNewAndUpdatedPolicyItems(
            backupJob, existingPolicyItems, policyItemsViews, new Date(), REFERENCE_TIME);

    // Policy Item 1 remains unchanges
    final Optional<PolicyItem> policyItemOne =
        newAndUpdatedPolicyItems.stream()
            .filter(item -> item.getId().equals(POLICY_ITEM_ID_1))
            .findFirst();
    assertTrue(policyItemOne.isPresent());
    assertEquals(POLICY_ITEM_1, policyItemOne.get());

    // PolicyItem 2 is updated
    final Optional<PolicyItem> policyItemTwo =
        newAndUpdatedPolicyItems.stream()
            .filter(item -> item.getId().equals(POLICY_ITEM_ID_2))
            .findFirst();
    assertTrue(policyItemTwo.isPresent());

    final PolicyItem two = policyItemTwo.get();
    assertEquals(NEW_POLICY_ITEM_4.getFrequencyType(), two.getFrequencyType());
    assertEquals(NEW_POLICY_ITEM_4.getFrequencyInterval(), two.getFrequencyInterval());
    assertEquals(NEW_POLICY_ITEM_4.getRetentionUnit(), two.getRetentionUnit());
    assertEquals(NEW_POLICY_ITEM_4.getRetention(), two.getRetention());
    assertNotEquals(NEXT_SNAPSHOT_DATE, two.getNextSnapshotDate());
  }

  private List<Policy> toPolicy(final PolicyItem... pPolicyItems) {
    if (pPolicyItems == null || pPolicyItems[0] == null) {
      return List.of(new Policy(POLICY_ID, new ArrayList<>()));
    }
    final List<PolicyItem> policyItemList = Arrays.asList(pPolicyItems);
    return List.of(new Policy(POLICY_ID, policyItemList));
  }

  @Test
  public void testValidatePolicies() throws SvcException {
    // No difference between the old policy and the new policy. Pass the validation.
    {
      final PolicyItem oldPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.DAILY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.DAILY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
    }

    // Cannot change frequency unit once a policy item is created. Throw an exception.
    {
      final PolicyItem oldPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.DAILY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.WEEKLY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Cannot change frequency unit once a policy item is created",
            e.getMessage());
      }
    }

    // Less frequent policy item needs to have equal or longer retention than more frequent ones.
    // Throw an exception.
    {
      final PolicyItem oldPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.DAILY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.DAILY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem2 =
          new PolicyItem(
              POLICY_ITEM_ID_2,
              BackupFrequencyType.WEEKLY,
              1,
              Duration.ofDays(2),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1, newPolicyItem2));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Less frequent policy item needs to have equal or longer"
                + " retention than more frequent ones.",
            e.getMessage());
      }
    }

    // Valid weekly policy item. Pass the validation.
    {
      final PolicyItem oldPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.DAILY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.DAILY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem2 =
          new PolicyItem(
              POLICY_ITEM_ID_2,
              BackupFrequencyType.WEEKLY,
              1,
              Duration.ofDays(8),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1, newPolicyItem2));
    }

    // There can be at most one minutely policy item. Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.MINUTELY,
              5,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem2 =
          new PolicyItem(
              POLICY_ITEM_ID_2,
              BackupFrequencyType.MINUTELY,
              10,
              Duration.ofDays(2),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());
      doReturn(true).when(clusterDescription).isDisaggregatedStorageSystem();

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1, newPolicyItem2));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: There can be at most one minutely policy item",
            e.getMessage());
      }
    }

    // There can be at most one hourly policy item. Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.HOURLY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem2 =
          new PolicyItem(
              POLICY_ITEM_ID_2,
              BackupFrequencyType.HOURLY,
              2,
              Duration.ofDays(2),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1, newPolicyItem2));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: There can be at most one hourly policy item",
            e.getMessage());
      }
    }

    // There can be at most one daily policy item. Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.DAILY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem2 =
          new PolicyItem(
              POLICY_ITEM_ID_2,
              BackupFrequencyType.DAILY,
              2,
              Duration.ofDays(2),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1, newPolicyItem2));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: There can be at most one daily policy item", e.getMessage());
      }
    }

    // Invalid minutely policy item for a non-DS cluster.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.MINUTELY,
              10,
              Duration.ofDays(15),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());
      doReturn(false).when(clusterDescription).isDisaggregatedStorageSystem();

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Cannot create a minutely snapshot policy.", e.getMessage());
      }
    }

    // Invalid hourly interval for non-NVMe clusters (hourly interval is not one of: 1,2,4,6,8,12).
    // Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.HOURLY,
              7,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Invalid hourly interval. Please choose one of: 1,2,4,6,8,12",
            e.getMessage());
      }
    }

    // Valid hourly frequency for non-NVMe clusters (hourly interval is one of: 1,2,4,6,8,12).
    // Pass the exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.HOURLY,
              12,
              Duration.ofDays(9),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
    }

    // Invalid hourly interval for NVMe cluster (hourly interval != 12). Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.HOURLY,
              6,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(true).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Invalid hourly interval. Please choose one of: 12",
            e.getMessage());
      }
    }

    // Valid hourly interval for NVMe cluster (hourly interval == 12). Pass the validation.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.HOURLY,
              12,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(true).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
    }

    // Invalid weekly interval (weekly interval > 7). Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.WEEKLY,
              8,
              Duration.ofDays(8),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Invalid weekly interval. Please choose from 1-7. (1 is"
                + " Monday, 7 is Sunday.)",
            e.getMessage());
      }
    }

    // Invalid weekly interval (weekly interval < 1). Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.WEEKLY,
              0,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Invalid weekly interval. Please choose from 1-7. (1 is"
                + " Monday, 7 is Sunday.)",
            e.getMessage());
      }
    }

    // Valid weekly frequency (weekly interval is 1-7). Pass the validation.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.WEEKLY,
              1,
              Duration.ofDays(8),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
    }

    // Invalid monthly interval (monthly interval < 1). Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.MONTHLY,
              0,
              Duration.ofDays(34),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Invalid monthly interval. Please choose from 1-28, and 40."
                + " (40 represents last day of month)",
            e.getMessage());
      }
    }

    // Invalid monthly interval (monthly interval from 29-39). Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.MONTHLY,
              29,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Invalid monthly interval. Please choose from 1-28, and 40."
                + " (40 represents last day of month)",
            e.getMessage());
      }
    }

    // Invalid monthly interval (monthly interval > 40). Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.MONTHLY,
              41,
              Duration.ofDays(33),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Invalid monthly interval. Please choose from 1-28, and 40."
                + " (40 represents last day of month)",
            e.getMessage());
      }
    }

    // Valid monthly interval (monthly interval == 40). Pass the validation.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.MONTHLY,
              40,
              Duration.ofDays(35),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
    }

    // Valid monthly interval (monthly interval is 1-28). Pass the validation.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.MONTHLY,
              27,
              Duration.ofDays(33),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
    }

    // Invalid yearly interval (monthly interval < 1). Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.YEARLY,
              0,
              Duration.ofDays(372),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Invalid yearly interval. Please choose from 1-12. (1 is"
                + " January, 12 is December.)",
            e.getMessage());
      }
    }

    // Invalid yearly interval (yearly interval > 12). Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.YEARLY,
              13,
              Duration.ofDays(372),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Invalid yearly interval. Please choose from 1-12. (1 is"
                + " January, 12 is December.)",
            e.getMessage());
      }
    }

    // Valid yearly interval (yearly interval == 12). Pass the validation.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.YEARLY,
              12,
              Duration.ofDays(372),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
    }

    // ON_DEMAND cannot be configured on a policy item. Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.ON_DEMAND,
              27,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: ON_DEMAND cannot be configured on a policy item",
            e.getMessage());
      }
    }

    // Retention is too big and exceeded maximum retention of 50000 months. Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.MONTHLY,
              27,
              Duration.ofDays(60000L * 28),
              BackupRetentionUnit.MONTHS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Exceeded maximum retention of 50000 months", e.getMessage());
      }
    }

    // Valid retention (retention < 50000 months). Pass the validation.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.MONTHLY,
              27,
              Duration.ofDays(40000L * 28),
              BackupRetentionUnit.MONTHS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
    }

    // Valid 5 policy items with different types. Pass the validation.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.HOURLY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem2 =
          new PolicyItem(
              POLICY_ITEM_ID_2,
              BackupFrequencyType.DAILY,
              1,
              Duration.ofDays(7),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem3 =
          new PolicyItem(
              new ObjectId(),
              BackupFrequencyType.WEEKLY,
              6,
              Duration.ofDays(7 * 2),
              BackupRetentionUnit.WEEKS);
      final PolicyItem newPolicyItem4 =
          new PolicyItem(
              new ObjectId(),
              BackupFrequencyType.MONTHLY,
              40,
              Duration.ofDays(28 * 4),
              BackupRetentionUnit.MONTHS);
      final PolicyItem newPolicyItem5 =
          new PolicyItem(
              new ObjectId(),
              BackupFrequencyType.YEARLY,
              8,
              Duration.ofDays(372 * 3),
              BackupRetentionUnit.YEARS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription,
          toPolicy(oldPolicyItem1),
          toPolicy(newPolicyItem1, newPolicyItem2, newPolicyItem3, newPolicyItem4, newPolicyItem5));
    }

    // Valid multiple weekly policy items. Pass the validation.
    {
      final PolicyItem oldPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.WEEKLY,
              1,
              Duration.ofDays(36),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.WEEKLY,
              1,
              Duration.ofDays(36),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem2 =
          new PolicyItem(
              POLICY_ITEM_ID_2,
              BackupFrequencyType.WEEKLY,
              1,
              Duration.ofDays(32),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem3 =
          new PolicyItem(
              new ObjectId(),
              BackupFrequencyType.WEEKLY,
              6,
              Duration.ofDays(7 * 2),
              BackupRetentionUnit.WEEKS);
      final PolicyItem newPolicyItem4 =
          new PolicyItem(
              new ObjectId(),
              BackupFrequencyType.MONTHLY,
              40,
              Duration.ofDays(28 * 4),
              BackupRetentionUnit.MONTHS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription,
          toPolicy(oldPolicyItem1),
          toPolicy(newPolicyItem1, newPolicyItem2, newPolicyItem3, newPolicyItem4));
    }

    // The policy item is empty and no hourly policy, but we allow this case as a requirement in
    // https://jira.mongodb.org/browse/CLOUDP-87012. Pass the validation.
    {
      final PolicyItem oldPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.HOURLY,
              1,
              Duration.ofDays(3),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem1 = null;

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      CpsPolicySvc.validatePolicies(
          clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1));
    }

    // The policy retention date should greater than 0. Throw an exception.
    {
      final PolicyItem oldPolicyItem1 = null;
      final PolicyItem newPolicyItem1 =
          new PolicyItem(
              POLICY_ITEM_ID_1,
              BackupFrequencyType.WEEKLY,
              1,
              Duration.ofDays(5),
              BackupRetentionUnit.DAYS);
      final PolicyItem newPolicyItem2 =
          new PolicyItem(
              POLICY_ITEM_ID_2,
              BackupFrequencyType.HOURLY,
              2,
              Duration.ofDays(0),
              BackupRetentionUnit.DAYS);

      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doReturn(false).when(clusterDescription).isNVMe(any());

      try {
        CpsPolicySvc.validatePolicies(
            clusterDescription, toPolicy(oldPolicyItem1), toPolicy(newPolicyItem1, newPolicyItem2));
        fail("should be a validation failure");
      } catch (final SvcException e) {
        assertEquals(NDSErrorCode.BACKUP_POLICY_INVALID, e.getErrorCode());
        assertEquals(
            "Backup policy is invalid: Retention need to be greater than 0", e.getMessage());
      }
    }
  }

  @Test
  public void testSanitizePolicyItem() {
    PolicyItem oldPolicyItem =
        new PolicyItem(
            POLICY_ITEM_ID_1,
            BackupFrequencyType.DAILY,
            1,
            NEXT_SNAPSHOT_DATE,
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);
    PolicyItem expectedSanitizedPolicyItem =
        new PolicyItem(
            POLICY_ITEM_ID_1,
            BackupFrequencyType.DAILY,
            1,
            NEXT_SNAPSHOT_DATE,
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);

    // valid config, should return same policy item
    assertEquals(
        expectedSanitizedPolicyItem, CpsPolicySvc.sanitizePolicyItem(oldPolicyItem, false));

    oldPolicyItem =
        new PolicyItem(
            POLICY_ITEM_ID_1,
            BackupFrequencyType.HOURLY,
            1,
            NEXT_SNAPSHOT_DATE,
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);
    expectedSanitizedPolicyItem =
        new PolicyItem(
            POLICY_ITEM_ID_1,
            BackupFrequencyType.HOURLY,
            1,
            NEXT_SNAPSHOT_DATE,
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);

    // valid hourly for non-nvme, should return same policy item
    assertEquals(
        expectedSanitizedPolicyItem, CpsPolicySvc.sanitizePolicyItem(oldPolicyItem, false));

    oldPolicyItem =
        new PolicyItem(
            POLICY_ITEM_ID_1,
            BackupFrequencyType.HOURLY,
            1,
            NEXT_SNAPSHOT_DATE,
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);
    expectedSanitizedPolicyItem =
        new PolicyItem(
            POLICY_ITEM_ID_1,
            BackupFrequencyType.HOURLY,
            12,
            NEXT_SNAPSHOT_DATE,
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);

    // invalid hourly for nvme, should fix the frequency interval
    assertEquals(expectedSanitizedPolicyItem, CpsPolicySvc.sanitizePolicyItem(oldPolicyItem, true));
  }

  @Test
  public void testDedupPolicyItems() {
    final PolicyItem policyItem1 =
        new PolicyItem(
            POLICY_ITEM_ID_1,
            BackupFrequencyType.DAILY,
            1,
            NEXT_SNAPSHOT_DATE,
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);
    final PolicyItem policyItem2 =
        new PolicyItem(
            POLICY_ITEM_ID_2,
            BackupFrequencyType.WEEKLY,
            1,
            NEXT_SNAPSHOT_DATE,
            Duration.ofDays(4),
            BackupRetentionUnit.DAYS);
    final PolicyItem policyItem3 =
        new PolicyItem(
            POLICY_ITEM_ID_3,
            BackupFrequencyType.DAILY,
            1,
            new Date(),
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);

    assertFalse(CpsPolicySvc.hasDuplicatePolicyItems(Arrays.asList(policyItem1, policyItem2)));
    assertTrue(
        CpsPolicySvc.hasDuplicatePolicyItems(Arrays.asList(policyItem1, policyItem2, policyItem1)));
    assertTrue(
        CpsPolicySvc.hasDuplicatePolicyItems(Arrays.asList(policyItem1, policyItem2, policyItem3)));
    assertFalse(CpsPolicySvc.hasDuplicatePolicyItems(Arrays.asList(policyItem2, policyItem3)));
  }

  @Test
  public void testUpdatePitWindowLessThanMinimum() throws Exception {
    final CpsPolicySvc svcSpy = spy(UnitTestUtils.create(CpsPolicySvc.class).withNoArgs());
    doReturn(2).when(svcSpy).getMinimumPitWindowDaysAllowed();
    assertThrows(
        SvcException.class,
        () -> svcSpy.updatePitWindow(new ObjectId(), new ObjectId(), 1, false, false));
  }

  @Test
  public void testUpdatePitWindowSuccessfully() throws Exception {
    final BackupJob dumbJob = mock(BackupJob.class);
    final BackupJobDao mockJobDao = mock(BackupJobDao.class);
    when(mockJobDao.updatePitWindow(any(), any(), eq(3.0))).thenReturn(Optional.of(dumbJob));
    final CpsPolicySvc svcSpy = spy(UnitTestUtils.create(CpsPolicySvc.class).withArgs(mockJobDao));
    doReturn(2).when(svcSpy).getMinimumPitWindowDaysAllowed();
    final Optional<BackupJob> jobOpt =
        svcSpy.updatePitWindow(new ObjectId(), new ObjectId(), 3.0, false, false);
    assertTrue(jobOpt.isPresent());
    assertEquals(dumbJob, jobOpt.get());
  }

  @Test
  public void testValidateNewCopySettingsWithDeleteCopiedBackups() {
    final ObjectId zoneId1 = new ObjectId();
    final ObjectId zoneId2 = new ObjectId();

    final AWSRegionName region1 = AWSRegionName.AF_SOUTH_1;
    final AWSRegionName region2 = AWSRegionName.AP_NORTHEAST_2;

    try {
      _underTest.validateNewCopySettingsWithDeleteCopiedBackups(
          List.of(
              new CopySetting(CloudProvider.AWS, region1, zoneId2, true, List.of()),
              new CopySetting(CloudProvider.AWS, region2, zoneId1, true, List.of())),
          List.of(
              new DeleteCopiedBackupsView(CloudProvider.AWS, region1.getName(), zoneId1),
              new DeleteCopiedBackupsView(CloudProvider.AWS, region2.getName(), zoneId2)));
    } catch (SvcException e) {
      LOG.error("Error validating delete copied backup", e);
      fail(); // call should succeed
    }

    try {
      _underTest.validateNewCopySettingsWithDeleteCopiedBackups(
          List.of(
              new CopySetting(CloudProvider.AWS, region1, zoneId2, true, List.of()),
              new CopySetting(CloudProvider.AWS, region2, zoneId2, false, List.of())),
          List.of(
              new DeleteCopiedBackupsView(CloudProvider.AWS, region1.getName(), zoneId1),
              new DeleteCopiedBackupsView(CloudProvider.AWS, region2.getName(), zoneId2)));

      fail("should be a validation failure");
    } catch (final SvcException e) {
      assertEquals(
          NDSErrorCode.SELECTED_REGIONS_TO_DELETE_BACKUPS_INVALID_COMPARED_WITH_NEW_SETTING,
          e.getErrorCode());
    }
  }

  @Test
  public void testValidateOldCopySettingWithDeleteCopiedBackups() {
    final ObjectId zoneId1 = new ObjectId();
    final ObjectId zoneId2 = new ObjectId();

    final AWSRegionName region1 = AWSRegionName.AF_SOUTH_1;
    final AWSRegionName region2 = AWSRegionName.AP_NORTHEAST_2;

    try {
      _underTest.validateOldCopySettingWithDeleteCopiedBackups(
          List.of(
              new CopySetting(CloudProvider.AWS, region1, zoneId1, true, List.of()),
              new CopySetting(CloudProvider.AWS, region2, zoneId2, true, List.of()),
              new CopySetting(CloudProvider.AWS, region2, zoneId1, true, List.of())),
          List.of(
              new DeleteCopiedBackupsView(CloudProvider.AWS, region1.getName(), zoneId1),
              new DeleteCopiedBackupsView(CloudProvider.AWS, region2.getName(), zoneId2)));
    } catch (SvcException e) {
      LOG.error("Error validating delete copied backup", e);
      fail(); // call should succeed
    }

    try {
      _underTest.validateOldCopySettingWithDeleteCopiedBackups(
          List.of(
              new CopySetting(CloudProvider.AWS, region1, zoneId1, true, List.of()),
              new CopySetting(CloudProvider.AWS, region2, zoneId2, true, List.of()),
              new CopySetting(CloudProvider.AWS, region2, zoneId1, true, List.of())),
          List.of(
              new DeleteCopiedBackupsView(CloudProvider.AWS, region1.getName(), zoneId1),
              new DeleteCopiedBackupsView(CloudProvider.AWS, region1.getName(), zoneId2)));

      fail("should be a validation failure");
    } catch (SvcException e) {
      assertEquals(
          NDSErrorCode.SELECTED_REGIONS_TO_DELETE_BACKUPS_INVALID_COMPARED_WITH_OLD_SETTING,
          e.getErrorCode());
    }
  }

  @Test
  public void testValidateDataProtectionSettings_groupNotExist() {
    final DataProtectionSettings settings = getDataProtectionSettingsForTesting();

    doReturn(Optional.empty()).when(_ndsGroupSvc).find(GROUP_ID);

    assertDataProtectionValidationThrowsError(settings, false);
  }

  @Test
  public void testValidateDataProtectionSettings_groupMismatch() {
    final DataProtectionSettings settings =
        getDataProtectionSettingsForTesting().toBuilder().setProjectId(ObjectId.get()).build();

    assertDataProtectionValidationThrowsError(settings, false);
  }

  @Test
  public void testValidateDataProtectionSettings_scheduledPolicyItemsFrequencyAndRetentionOrder() {
    DataProtectionSettings settings = getDataProtectionSettingsForTesting();

    // hourly item has lower retention than weekly
    PolicyItem newPolicyItem1 =
        new PolicyItem(
            null, BackupFrequencyType.HOURLY, 1, Duration.ofDays(8), BackupRetentionUnit.DAYS);
    PolicyItem newPolicyItem2 =
        new PolicyItem(
            null, BackupFrequencyType.WEEKLY, 1, Duration.ofDays(9), BackupRetentionUnit.DAYS);

    settings =
        settings.toBuilder()
            .setScheduledPolicyItems(List.of(newPolicyItem1, newPolicyItem2))
            .build();

    assertDataProtectionValidationSucceeds(settings, false);

    // both items have same retention
    newPolicyItem1 =
        new PolicyItem(
            null, BackupFrequencyType.HOURLY, 1, Duration.ofDays(9), BackupRetentionUnit.DAYS);
    settings =
        settings.toBuilder()
            .setScheduledPolicyItems(List.of(newPolicyItem1, newPolicyItem2))
            .build();

    assertDataProtectionValidationSucceeds(settings, false);

    // hourly item has higher retention than weekly
    newPolicyItem1 =
        new PolicyItem(
            null, BackupFrequencyType.HOURLY, 1, Duration.ofDays(10), BackupRetentionUnit.DAYS);
    settings =
        settings.toBuilder()
            .setScheduledPolicyItems(List.of(newPolicyItem1, newPolicyItem2))
            .build();

    assertDataProtectionValidationThrowsError(settings, false);
  }

  @Test
  public void testValidateDataProtectionSettings_removeScheduledPolicyItems() {
    //  disallow deleting Backup Compliance Policy frequency items after enabling Backup Compliance
    // Policy. (except by
    // certain support roles)
    DataProtectionSettings baseSettings = getDataProtectionSettingsForTesting();

    PolicyItem newPolicyItem1 =
        new PolicyItem(
            null, BackupFrequencyType.HOURLY, 1, Duration.ofDays(2), BackupRetentionUnit.DAYS);
    PolicyItem newPolicyItem2 =
        new PolicyItem(
            null, BackupFrequencyType.WEEKLY, 1, Duration.ofDays(7), BackupRetentionUnit.DAYS);

    DataProtectionSettings oldSettings =
        baseSettings.toBuilder()
            .setScheduledPolicyItems(List.of(newPolicyItem1, newPolicyItem2))
            .build();

    DataProtectionSettings updatedSettings =
        baseSettings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem1)).build();

    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    // test case #1: user has permission
    assertDataProtectionValidationSucceeds(updatedSettings, true);

    // test case #2: user does not have permission
    assertDataProtectionValidationThrowsError(updatedSettings, false);
  }

  @Test
  public void testValidateDataProtectionSettings_removeOnDemandPolicyItem() {
    //  disallow deleting Backup Compliance Policy on-demand policy frequency items after enabling
    // Backup Compliance Policy.
    // (except by
    // certain support roles)
    DataProtectionSettings baseSettings = getDataProtectionSettingsForTesting();

    DataProtectionSettings updatedSettings =
        baseSettings.toBuilder().setOnDemandPolicyItem(null).build();

    doReturn(Optional.of(baseSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    // test case #1: user has permission
    assertDataProtectionValidationSucceeds(updatedSettings, true);

    // test case #2: user does not have permission
    assertDataProtectionValidationThrowsError(updatedSettings, false);
  }

  @Test
  public void testValidateDataProtectionSettings_updateAuthorizedUserInfoWithNewDataProtection() {
    // test case: user does not have permission but first time enable Backup Compliance Policy
    // settings
    DataProtectionSettings baseSettings = getDataProtectionSettingsForTesting();
    DataProtectionSettings updatedSettings =
        baseSettings.toBuilder()
            .setAuthorizedEmail("<EMAIL>")
            .setAuthorizedUserFirstName("newFoo")
            .setAuthorizedUserLastName("new_bar")
            .build();

    {
      doReturn(Optional.empty()).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);
      assertDataProtectionValidationSucceeds(updatedSettings, false);
      assertDataProtectionValidationSucceeds(updatedSettings, true);
    }

    {
      DataProtectionSettings updateBaseSettings =
          baseSettings.toBuilder()
              .setAuthorizedEmail("<EMAIL>")
              .setAuthorizedUserFirstName("")
              .setAuthorizedUserLastName(null)
              .build();

      doReturn(Optional.of(updateBaseSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);
      assertDataProtectionValidationSucceeds(updatedSettings, false);
      assertDataProtectionValidationSucceeds(updatedSettings, true);
    }
  }

  @Test
  public void
      testValidateDataProtectionSettings_updateAuthorizedUserInfoWithInternalUserPermission() {
    DataProtectionSettings baseSettings = getDataProtectionSettingsForTesting();

    DataProtectionSettings updatedSettings =
        baseSettings.toBuilder()
            .setAuthorizedEmail("<EMAIL>")
            .setAuthorizedUserFirstName("newFoo")
            .setAuthorizedUserLastName("new_bar")
            .build();

    DataProtectionSettings oldSettings = baseSettings.toBuilder().build();
    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    //  with POST GA FF on
    assertDataProtectionValidationSucceeds(updatedSettings, true);

    //  with POST GA FF off
    doReturn(false)
        .when(_cpsSvc)
        .isBackupCompliancePolicyPostGaDisablePolicyFeatureFlagEnabled(any(ObjectId.class));
    try {
      assertDataProtectionValidationSucceeds(updatedSettings, true);
    } finally {
      doReturn(true)
          .when(_cpsSvc)
          .isBackupCompliancePolicyPostGaDisablePolicyFeatureFlagEnabled(any(ObjectId.class));
    }
  }

  @Test
  public void testValidateDataProtectionSettings_updateTheSameAuthorizedInfo() {
    DataProtectionSettings baseSettings = getDataProtectionSettingsForTesting();
    DataProtectionSettings updatedSettings = baseSettings.toBuilder().build();

    DataProtectionSettings oldSettings = baseSettings.toBuilder().build();
    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    //  with POST GA FF on
    assertDataProtectionValidationSucceeds(updatedSettings, false);

    // with POST GA FF off
    doReturn(false)
        .when(_cpsSvc)
        .isBackupCompliancePolicyPostGaDisablePolicyFeatureFlagEnabled(any(ObjectId.class));
    try {
      assertDataProtectionValidationSucceeds(updatedSettings, true);
    } finally {
      doReturn(true)
          .when(_cpsSvc)
          .isBackupCompliancePolicyPostGaDisablePolicyFeatureFlagEnabled(any(ObjectId.class));
    }
  }

  @Test
  public void testValidateDataProtectionSettings_updateFromDefaultNameValuesSucceeds() {
    DataProtectionSettings baseSettings =
        getDataProtectionSettingsForTesting().toBuilder()
            .setAuthorizedUserFirstName(CpsPolicySvc.DEFAULT_BCP_AUTHORIZED_NAME_VALUE)
            .setAuthorizedUserLastName(CpsPolicySvc.DEFAULT_BCP_AUTHORIZED_NAME_VALUE)
            .build();
    DataProtectionSettings newSettings = getDataProtectionSettingsForTesting();

    doReturn(Optional.of(baseSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    //  with POST GA FF on
    assertDataProtectionValidationSucceeds(newSettings, false);
  }

  @Test
  public void testValidateDataProtectionSettings_updateFromExistingFirstNameFails() {
    DataProtectionSettings baseSettings =
        getDataProtectionSettingsForTesting().toBuilder()
            .setAuthorizedUserFirstName("firstName")
            .build();
    DataProtectionSettings newSettings = getDataProtectionSettingsForTesting();

    doReturn(Optional.of(baseSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    //  with POST GA FF on
    assertDataProtectionValidationThrowsError(newSettings, false);
  }

  @Test
  public void testValidateDataProtectionSettings_updateFromExistingLastNameFails() {
    DataProtectionSettings baseSettings =
        getDataProtectionSettingsForTesting().toBuilder()
            .setAuthorizedUserLastName("lastName")
            .build();
    DataProtectionSettings newSettings = getDataProtectionSettingsForTesting();

    doReturn(Optional.of(baseSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    //  with POST GA FF on
    assertDataProtectionValidationThrowsError(newSettings, false);
  }

  @Test
  public void
      testValidateDataProtectionSettings_updateAuthorizedUserInfoWithoutInternalPermission() {
    DataProtectionSettings baseSettings = getDataProtectionSettingsForTesting();
    DataProtectionSettings oldSettings = baseSettings.toBuilder().build();
    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);
    {
      DataProtectionSettings updatedSettings =
          baseSettings.toBuilder().setAuthorizedEmail("<EMAIL>").build();
      assertDataProtectionValidationThrowsError(updatedSettings, false);
    }
    {
      DataProtectionSettings updatedSettings =
          baseSettings.toBuilder().setAuthorizedUserFirstName("new_foo").build();
      assertDataProtectionValidationThrowsError(updatedSettings, false);
    }
    {
      DataProtectionSettings updatedSettings =
          baseSettings.toBuilder().setAuthorizedUserLastName("new_bar").build();
      assertDataProtectionValidationThrowsError(updatedSettings, false);
    }
  }

  @Test
  public void testValidateDataProtectionSettings_updateWithoutAuthorizedEmail() {
    DataProtectionSettings baseSettings = getDataProtectionSettingsForTesting();
    DataProtectionSettings updatedSettings =
        baseSettings.toBuilder().setAuthorizedEmail(null).build();

    // test case: required field authorizedEmail is missing
    assertDataProtectionValidationThrowsError(updatedSettings, true);
  }

  @Test
  public void testValidateDataProtectionSettings_updateWithInvalidAuthorizedEmail() {
    DataProtectionSettings baseSettings = getDataProtectionSettingsForTesting();

    DataProtectionSettings updatedSettings =
        baseSettings.toBuilder().setAuthorizedEmail("invalid email address").build();

    // test case: the authorizedEmail value is invalid
    assertDataProtectionValidationThrowsError(updatedSettings, true);
  }

  @Test
  public void testValidateDataProtectionSettings_scheduledPolicyItemsNonzeroRetention() {
    // regardless of permission or whether this is a create or update action, 0 retention duration
    // is prohibited
    // test case #1: schedule policy contains policy item with 0 retention duration
    DataProtectionSettings settings = getDataProtectionSettingsForTesting();
    DataProtectionSettings oldSettings = settings.toBuilder().build();

    PolicyItem newPolicyItem1 =
        new PolicyItem(
            null, BackupFrequencyType.HOURLY, 1, Duration.ofDays(0), BackupRetentionUnit.DAYS);
    PolicyItem newPolicyItem2 =
        new PolicyItem(
            null, BackupFrequencyType.WEEKLY, 1, Duration.ofDays(0), BackupRetentionUnit.DAYS);

    settings =
        settings.toBuilder()
            .setScheduledPolicyItems(List.of(newPolicyItem1, newPolicyItem2))
            .build();

    // test case #1: create action
    doReturn(Optional.empty()).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);
    assertDataProtectionValidationThrowsError(settings, true);
    assertDataProtectionValidationThrowsError(settings, false);

    // test case #2: update action
    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);
    assertDataProtectionValidationThrowsError(settings, true);
    assertDataProtectionValidationThrowsError(settings, false);
  }

  @Test
  public void testValidateDataProtectionSettings_onDemandPolicyItemsNonzeroRetention() {
    // regardless of permission or whether this is a create or update action, 0 retention duration
    // is prohibited
    // test case: on demand policy contains policy item with 0 retention duration
    DataProtectionSettings settings = getDataProtectionSettingsForTesting();
    DataProtectionSettings oldSettings = settings.toBuilder().build();

    PolicyItem newPolicyItem1 =
        new PolicyItem(
            null, BackupFrequencyType.HOURLY, 1, Duration.ofDays(0), BackupRetentionUnit.DAYS);
    settings = settings.toBuilder().setOnDemandPolicyItem(newPolicyItem1).build();

    // test case #1:  create action
    doReturn(Optional.empty()).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);
    assertDataProtectionValidationThrowsError(settings, true);
    assertDataProtectionValidationThrowsError(settings, false);

    // test case #2: update action
    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);
    assertDataProtectionValidationThrowsError(settings, true);
    assertDataProtectionValidationThrowsError(settings, false);
  }

  @Test
  public void testValidateDataProtectionSettings_scheduledPolicyItemDuplicateFrequency() {
    DataProtectionSettings settings = getDataProtectionSettingsForTesting();

    final PolicyItem hourlyItem1 =
        new PolicyItem(
            null, BackupFrequencyType.HOURLY, 1, Duration.ofDays(7), BackupRetentionUnit.DAYS);
    final PolicyItem hourlyItem2 =
        new PolicyItem(
            null, BackupFrequencyType.HOURLY, 1, Duration.ofDays(8), BackupRetentionUnit.DAYS);

    final PolicyItem dailyItem =
        new PolicyItem(
            null, BackupFrequencyType.DAILY, 1, Duration.ofDays(9), BackupRetentionUnit.DAYS);

    final PolicyItem monthlyItem1 =
        new PolicyItem(
            null, BackupFrequencyType.MONTHLY, 1, Duration.ofDays(31), BackupRetentionUnit.DAYS);
    final PolicyItem monthlyItem2 =
        new PolicyItem(
            null, BackupFrequencyType.MONTHLY, 1, Duration.ofDays(32), BackupRetentionUnit.DAYS);

    // no duplicate frequency
    settings =
        settings.toBuilder()
            .setScheduledPolicyItems(List.of(hourlyItem1, dailyItem, monthlyItem1))
            .build();

    assertDataProtectionValidationSucceeds(settings, false);

    // duplicate hourly frequency
    settings =
        settings.toBuilder()
            .setScheduledPolicyItems(List.of(hourlyItem1, dailyItem, hourlyItem2, monthlyItem1))
            .build();

    assertDataProtectionValidationThrowsError(settings, false);

    // duplicate monthly frequency
    settings =
        settings.toBuilder()
            .setScheduledPolicyItems(List.of(hourlyItem1, dailyItem, monthlyItem2, monthlyItem1))
            .build();

    assertDataProtectionValidationThrowsError(settings, false);
  }

  @Test
  public void testValidateDataProtectionSettings_scheduledPolicyItemValues() {
    DataProtectionSettings settings = getDataProtectionSettingsForTesting();

    assertDataProtectionValidationSucceeds(settings, false);

    // invalid hourly frequency
    PolicyItem newPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.HOURLY, 11, Duration.ofDays(7), BackupRetentionUnit.DAYS);

    settings = settings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem)).build();

    assertDataProtectionValidationThrowsError(settings, false);

    // valid hourly frequency
    newPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.HOURLY, 4, Duration.ofDays(7), BackupRetentionUnit.DAYS);

    settings = settings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem)).build();

    assertDataProtectionValidationSucceeds(settings, false);

    // retention too large
    newPolicyItem =
        new PolicyItem(
            null,
            BackupFrequencyType.HOURLY,
            11,
            Duration.ofDays(60000L * 28),
            BackupRetentionUnit.DAYS);

    settings = settings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem)).build();

    assertDataProtectionValidationThrowsError(settings, false);

    // retention within bound
    newPolicyItem =
        new PolicyItem(
            null,
            BackupFrequencyType.HOURLY,
            4,
            Duration.ofDays(40000L * 28),
            BackupRetentionUnit.DAYS);

    settings = settings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem)).build();

    assertDataProtectionValidationSucceeds(settings, false);

    // monthly retention too small
    newPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.MONTHLY, 0, Duration.ofDays(30), BackupRetentionUnit.DAYS);

    settings = settings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem)).build();

    assertDataProtectionValidationThrowsError(settings, false);

    // monthly retention ok
    newPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.MONTHLY, 0, Duration.ofDays(32), BackupRetentionUnit.DAYS);

    settings = settings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem)).build();

    assertDataProtectionValidationSucceeds(settings, false);

    // weekly retention too small
    newPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.WEEKLY, 0, Duration.ofDays(6), BackupRetentionUnit.DAYS);

    settings = settings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem)).build();

    assertDataProtectionValidationThrowsError(settings, false);

    // weekly retention ok
    newPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.WEEKLY, 0, Duration.ofDays(8), BackupRetentionUnit.DAYS);

    settings = settings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem)).build();

    assertDataProtectionValidationSucceeds(settings, false);

    // yealry retention too small
    newPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.YEARLY, 0, Duration.ofDays(300), BackupRetentionUnit.DAYS);

    settings = settings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem)).build();

    assertDataProtectionValidationThrowsError(settings, false);

    // yearly retention ok
    newPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.YEARLY, 0, Duration.ofDays(390), BackupRetentionUnit.DAYS);

    settings = settings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem)).build();

    assertDataProtectionValidationSucceeds(settings, false);

    // on-demand retention not supported
    newPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.ON_DEMAND, 0, Duration.ofDays(8), BackupRetentionUnit.DAYS);

    settings = settings.toBuilder().setScheduledPolicyItems(List.of(newPolicyItem)).build();

    assertDataProtectionValidationThrowsError(settings, false);
  }

  @Test
  public void testValidateDataProtectionSettings_pit() {
    DataProtectionSettings settings = getDataProtectionSettingsForTesting();

    assertDataProtectionValidationSucceeds(settings, false);

    doReturn(3).when(_underTest).getMinimumPitWindowDaysAllowed();

    // PIT enabled and PIT window is below minimum
    assertDataProtectionValidationThrowsError(
        settings.toBuilder().setPitEnabled(true).setRestoreWindowDays(2).build(), false);

    // PIT disabled and PIT window is below minimum
    assertDataProtectionValidationSucceeds(
        settings.toBuilder().setPitEnabled(false).setRestoreWindowDays(2).build(), false);

    // PIT enabled and PIT window is 0
    doReturn(0).when(_underTest).getMinimumPitWindowDaysAllowed();

    assertDataProtectionValidationThrowsError(
        settings.toBuilder().setPitEnabled(true).setRestoreWindowDays(0).build(), false);

    // Backup Compliance Policy wasn't previously enabled, new setting has pit disabled, no
    // permission to disable
    // pit
    settings = settings.toBuilder().setPitEnabled(false).build();
    doReturn(Optional.empty()).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    assertDataProtectionValidationSucceeds(settings, false);

    // Backup Compliance Policy was previously enabled but pit disabled, new setting has pit
    // disabled, no
    // permission to disable pit
    settings = settings.toBuilder().setPitEnabled(false).build();
    DataProtectionSettings oldSettings = settings.toBuilder().setPitEnabled(false).build();
    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    assertDataProtectionValidationSucceeds(settings, false);

    // Backup Compliance Policy was previously enabled and pit enabled, new setting has pit
    // disabled, no
    // permission to disable pit
    settings = settings.toBuilder().setPitEnabled(false).build();
    oldSettings = settings.toBuilder().setPitEnabled(true).build();
    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    assertDataProtectionValidationThrowsError(settings, false);

    // Backup Compliance Policy was previously enabled and pit enabled, new setting has pit
    // disabled, has
    // permission to disable pit
    settings = settings.toBuilder().setPitEnabled(false).build();
    oldSettings = settings.toBuilder().setPitEnabled(true).build();
    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    assertDataProtectionValidationSucceeds(settings, true);

    // Backup Compliance Policy was previously enabled and pit enabled, new setting has pit enabled
    // but pit
    // window decreased, no permission to disable pit
    settings = settings.toBuilder().setPitEnabled(true).setRestoreWindowDays(5).build();
    oldSettings = settings.toBuilder().setPitEnabled(true).setRestoreWindowDays(6).build();
    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    assertDataProtectionValidationThrowsError(settings, false);

    // Backup Compliance Policy was previously enabled and pit enabled, new setting has pit enabled
    // but pit
    // window increased, no permission to disable pit
    settings = settings.toBuilder().setPitEnabled(true).setRestoreWindowDays(5).build();
    oldSettings = settings.toBuilder().setPitEnabled(true).setRestoreWindowDays(4).build();
    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    assertDataProtectionValidationSucceeds(settings, false);

    // Backup Compliance Policy was previously enabled and pit enabled, new setting has pit
    // disabled, has
    // permission to disable pit
    settings = settings.toBuilder().setPitEnabled(false).build();
    oldSettings = settings.toBuilder().setPitEnabled(true).build();
    doReturn(Optional.of(oldSettings)).when(_cpsSvc).getDataProtectionSettings(GROUP_ID);

    assertDataProtectionValidationSucceeds(settings, true);
  }

  @Test
  public void testValidateDataProtectionSettings_hourlyPolicyForPit() {
    DataProtectionSettings settings = getDataProtectionSettingsForTesting();

    final PolicyItem hourlyItem =
        new PolicyItem(
            null, BackupFrequencyType.HOURLY, 1, Duration.ofDays(7), BackupRetentionUnit.DAYS);

    final PolicyItem weeklyItem =
        new PolicyItem(
            null, BackupFrequencyType.WEEKLY, 1, Duration.ofDays(7), BackupRetentionUnit.DAYS);

    // pit enabled but no hourly policy
    settings =
        settings.toBuilder()
            .setScheduledPolicyItems(List.of(weeklyItem))
            .setPitEnabled(true)
            .setRestoreWindowDays(7)
            .build();

    assertDataProtectionValidationThrowsError(settings, false);

    // pit enabled, has hourly policy, and hourly retention is not less than pit window
    settings =
        settings.toBuilder()
            .setScheduledPolicyItems(List.of(hourlyItem, weeklyItem))
            .setPitEnabled(true)
            .setRestoreWindowDays(7)
            .build();

    assertDataProtectionValidationSucceeds(settings, true);

    // pit enabled, has hourly policy, but hourly retention is less than pit window
    settings =
        settings.toBuilder()
            .setScheduledPolicyItems(List.of(hourlyItem, weeklyItem))
            .setPitEnabled(true)
            .setRestoreWindowDays(8)
            .build();

    assertDataProtectionValidationThrowsError(settings, false);
  }

  private void assertDataProtectionValidationSucceeds(
      DataProtectionSettings pSettings, boolean pHasInternalRolePermission) {
    try {
      _underTest.validateDataProtectionSettings(
          GROUP_ID, pSettings, pHasInternalRolePermission, false);
    } catch (SvcException e) {
      LOG.error("call should succeed", e);
      fail();
    }
  }

  private void assertDataProtectionValidationThrowsError(
      final DataProtectionSettings pSettings, boolean pHasInternalRolePermission) {
    try {
      _underTest.validateDataProtectionSettings(
          GROUP_ID, pSettings, pHasInternalRolePermission, false);
      fail("should be a validation failure");
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.BACKUP_COMPLIANCE_POLICY_SETTINGS_INVALID, e.getErrorCode());
    }
  }

  @Test
  public void testValidateDataProtectionSettings_missingValues() {
    final DataProtectionSettings settings = getDataProtectionSettingsForTesting();

    assertDataProtectionValidationSucceeds(settings, false);

    // missing scheduled items
    assertDataProtectionValidationThrowsError(
        settings.toBuilder().setScheduledPolicyItems(null).build(), false);
  }

  @Test
  public void testValidateDataProtectionSettings_EAR() {
    DataProtectionSettings settings =
        getDataProtectionSettingsForTesting().toBuilder().setEncryptionAtRestEnabled(true).build();

    final ClusterDescription clusterDescription1 = mock(ClusterDescription.class);
    final ClusterDescription clusterDescription2 = mock(ClusterDescription.class);
    final ClusterDescription clusterDescription3 = mock(ClusterDescription.class);

    final NDSGroup ndsGroup = mock(NDSGroup.class);
    doReturn(Optional.of(ndsGroup)).when(_ndsGroupSvc).find(GROUP_ID);

    final NDSEncryptionAtRest ear = mock(NDSEncryptionAtRest.class);
    doReturn(ear).when(ndsGroup).getEncryptionAtRest();

    // project and all existing dedicated clusters have EAR
    doReturn(true).when(ear).isEnabled();

    doReturn(List.of(clusterDescription1, clusterDescription2, clusterDescription3))
        .when(_ndsClusterSvc)
        .getAllClusterDescriptions(GROUP_ID);

    doReturn(true).when(clusterDescription1).isEncryptionAtRestEnabled();
    doReturn(true).when(clusterDescription2).isEncryptionAtRestEnabled();

    doReturn(false).when(clusterDescription3).isEncryptionAtRestEnabled();
    doReturn(true).when(clusterDescription3).isTenantCluster();

    doReturn(List.of(clusterDescription1, clusterDescription2, clusterDescription3))
        .when(_ndsClusterSvc)
        .getAllClusterDescriptions(GROUP_ID);

    assertDataProtectionValidationSucceeds(settings, false);

    // project has EAR enabled but some dedicated cluster has EAR disabled
    doReturn(false).when(clusterDescription1).isEncryptionAtRestEnabled();

    assertDataProtectionValidationThrowsError(settings, false);

    // project has EAR disabled
    doReturn(List.of()).when(_ndsClusterSvc).getAllClusterDescriptions(GROUP_ID);
    doReturn(false).when(ear).isEnabled();

    assertDataProtectionValidationThrowsError(settings, false);

    // project has EAR disabled, but Backup Compliance Policy EAR is disabled
    settings = settings.toBuilder().setEncryptionAtRestEnabled(false).build();

    assertDataProtectionValidationSucceeds(settings, false);

    // project has EAR enabled but some dedicated cluster has EAR disabled, and Backup Compliance
    // Policy has EAR
    // disabled
    doReturn(true).when(ear).isEnabled();
    doReturn(List.of(clusterDescription1, clusterDescription2, clusterDescription3))
        .when(_clusterDescriptionDao)
        .findByGroup(GROUP_ID);

    settings = settings.toBuilder().setEncryptionAtRestEnabled(false).build();

    assertDataProtectionValidationSucceeds(settings, false);
  }

  @Test
  public void testValidateDataProtectionSettingsAgainstBackupPolicyChange() {
    final PolicyItem protectionHourlyPolicy =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.HOURLY,
            6,
            Duration.ofDays(7),
            BackupRetentionUnit.DAYS);

    final PolicyItem protectionWeeklyPolicy =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.WEEKLY,
            6,
            Duration.ofDays(28),
            BackupRetentionUnit.WEEKS);

    final PolicyItem protectionOnDemandPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.ON_DEMAND, 0, Duration.ofDays(3), BackupRetentionUnit.DAYS);

    final DataProtectionSettingsBuilder builder =
        DataProtectionSettings.newBuilder()
            .setProjectId(GROUP_ID)
            .setRestoreWindowDays(5)
            .setEncryptionAtRestEnabled(false)
            .setPitEnabled(true)
            .setCopyProtectionEnabled(false)
            .setOnDemandPolicyItem(protectionOnDemandPolicyItem)
            .setScheduledPolicyItems(List.of(protectionHourlyPolicy, protectionWeeklyPolicy));

    final DataProtectionSettings dataProtectionSettings = builder.build();

    final PolicyItem hourlyPolicyNvme =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.HOURLY,
            12,
            Duration.ofDays(7),
            BackupRetentionUnit.DAYS);

    final PolicyItem hourlyPolicyNonNvme =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.HOURLY,
            6,
            Duration.ofDays(7),
            BackupRetentionUnit.DAYS);

    final PolicyItem hourlyPolicyShortRetention =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.HOURLY,
            6,
            Duration.ofDays(6),
            BackupRetentionUnit.DAYS);

    final PolicyItem dailyPolicy =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.DAILY,
            100,
            Duration.ofDays(1),
            BackupRetentionUnit.DAYS);

    final PolicyItem weeklyPolicy1 =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.WEEKLY,
            7,
            Duration.ofDays(28),
            BackupRetentionUnit.WEEKS);

    final PolicyItem weeklyPolicy2 =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.WEEKLY,
            7,
            Duration.ofDays(30),
            BackupRetentionUnit.MONTHS);

    final PolicyItem weeklyPolicyShortRetention =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.WEEKLY,
            10,
            Duration.ofDays(27),
            BackupRetentionUnit.MONTHS);

    // happy path
    try {
      _underTest.validateDataProtectionSettingsAgainstBackupPolicyChange(
          dataProtectionSettings,
          List.of(hourlyPolicyNonNvme, dailyPolicy, weeklyPolicy1, weeklyPolicy2),
          6,
          false);
    } catch (SvcException e) {
      LOG.error("call should succeed", e);
      fail();
    }

    // cluster PIT window is less than Backup Compliance Policy PIT window
    try {
      _underTest.validateDataProtectionSettingsAgainstBackupPolicyChange(
          dataProtectionSettings,
          List.of(hourlyPolicyNonNvme, dailyPolicy, weeklyPolicy1, weeklyPolicy2),
          4,
          false);
      fail("should be a validation failure");
    } catch (final SvcException e) {
      assertEquals(
          NDSErrorCode.BACKUP_POLICIES_NOT_MEETING_BACKUP_COMPLIANCE_POLICY_REQUIREMENTS,
          e.getErrorCode());
      assertEquals(
          "The following backup policies do not comply with the specified backup compliance policy:"
              + " Policy validation errors: [The backup policy does not meet the Backup Compliance"
              + " Policy requirements for restore window]",
          e.getMessage());
    }

    // cluster PIT window is less than Backup Compliance Policy PIT window, but Backup Compliance
    // Policy PIT is disabled
    try {
      _underTest.validateDataProtectionSettingsAgainstBackupPolicyChange(
          dataProtectionSettings.toBuilder().setPitEnabled(false).build(),
          List.of(hourlyPolicyNonNvme, dailyPolicy, weeklyPolicy1, weeklyPolicy2),
          4,
          false);
    } catch (SvcException e) {
      LOG.error("call should succeed", e);
      fail();
    }

    // cluster policy missing hourly frequency type
    try {
      _underTest.validateDataProtectionSettingsAgainstBackupPolicyChange(
          dataProtectionSettings, List.of(dailyPolicy, weeklyPolicy1, weeklyPolicy2), 6, false);
      fail("should be a validation failure");
    } catch (final SvcException e) {
      assertEquals(
          NDSErrorCode.BACKUP_POLICIES_NOT_MEETING_BACKUP_COMPLIANCE_POLICY_REQUIREMENTS,
          e.getErrorCode());
      assertEquals(
          "The following backup policies do not comply with the specified backup compliance policy:"
              + " Policy validation errors: [The backup policy does not meet the Backup Compliance"
              + " Policy requirements for HOURLY policy item]",
          e.getMessage());
    }

    // one of the weekly item has retention that's too short
    try {
      _underTest.validateDataProtectionSettingsAgainstBackupPolicyChange(
          dataProtectionSettings,
          List.of(hourlyPolicyNonNvme, dailyPolicy, weeklyPolicy1, weeklyPolicyShortRetention),
          6,
          false);
      fail("should be a validation failure");
    } catch (final SvcException e) {
      assertEquals(
          NDSErrorCode.BACKUP_POLICIES_NOT_MEETING_BACKUP_COMPLIANCE_POLICY_REQUIREMENTS,
          e.getErrorCode());
      assertEquals(
          "The following backup policies do not comply with the specified backup compliance policy:"
              + " Policy validation errors: [The backup policy WEEKLY retention of 0 MONTHS must be"
              + " greater than or equal to the backup compliance policy WEEKLY retention of 4"
              + " MONTHS]",
          e.getMessage());
    }

    // hourly item has retention that's too short
    try {
      _underTest.validateDataProtectionSettingsAgainstBackupPolicyChange(
          dataProtectionSettings,
          List.of(hourlyPolicyShortRetention, dailyPolicy, weeklyPolicy1, weeklyPolicy2),
          6,
          false);
      fail("should be a validation failure");
    } catch (final SvcException e) {
      assertEquals(
          NDSErrorCode.BACKUP_POLICIES_NOT_MEETING_BACKUP_COMPLIANCE_POLICY_REQUIREMENTS,
          e.getErrorCode());
      assertEquals(
          "The following backup policies do not comply with the specified backup compliance policy:"
              + " Policy validation errors: [The backup policy HOURLY retention of 6 DAYS must be"
              + " greater than or equal to the backup compliance policy HOURLY retention of 7"
              + " DAYS]",
          e.getMessage());
    }

    // hourly item has interval that's too long, but is NVME
    try {
      _underTest.validateDataProtectionSettingsAgainstBackupPolicyChange(
          dataProtectionSettings,
          List.of(hourlyPolicyNvme, dailyPolicy, weeklyPolicy1, weeklyPolicy2),
          6,
          true);
    } catch (final SvcException e) {
      LOG.error("call should succeed", e);
      fail();
    }

    // hourly item has interval that's too long, and is not NVME
    try {
      _underTest.validateDataProtectionSettingsAgainstBackupPolicyChange(
          dataProtectionSettings,
          List.of(hourlyPolicyNvme, dailyPolicy, weeklyPolicy1, weeklyPolicy2),
          6,
          false);
      fail("should be a validation failure");
    } catch (final SvcException e) {
      assertEquals(
          NDSErrorCode.BACKUP_POLICIES_NOT_MEETING_BACKUP_COMPLIANCE_POLICY_REQUIREMENTS,
          e.getErrorCode());
      assertEquals(
          "The following backup policies do not comply with the specified backup compliance policy:"
              + " Policy validation errors: [The backup policy HOURLY frequency of 12 must be less"
              + " than or equal to the backup compliance policy HOURLY frequency of 6]",
          e.getMessage());
    }
  }

  @Test
  public void testValidateDataProtectionAgainstClusterPolicies_withInvalidFrequencyAndRetention() {
    // Data protection hourly policy of 1 snapshot per hour and retention of 8
    final PolicyItem protectionHourlyPolicy =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.HOURLY,
            1,
            Duration.ofDays(8),
            BackupRetentionUnit.DAYS);

    final DataProtectionSettingsBuilder builder =
        DataProtectionSettings.newBuilder()
            .setProjectId(GROUP_ID)
            .setRestoreWindowDays(5)
            .setEncryptionAtRestEnabled(false)
            .setPitEnabled(true)
            .setCopyProtectionEnabled(false)
            .setOnDemandPolicyItem(protectionHourlyPolicy)
            .setScheduledPolicyItems(List.of(protectionHourlyPolicy));

    final DataProtectionSettings dataProtectionSettings = builder.build();

    // backup policy of 6 snapshot per hour and retention of 7
    final PolicyItem hourlyPolicy =
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.HOURLY,
            6,
            Duration.ofDays(7),
            BackupRetentionUnit.DAYS);

    // expect error to invalidate retention and frequency for hourly policy item
    List<String> errors =
        _underTest.validateDataProtectionSettingsAgainstBackupPoliciesForCluster(
            dataProtectionSettings, List.of(hourlyPolicy), 6, false);

    assertEquals(2, errors.size());
  }

  @Test
  public void testGetUpdatedDataProtectionFields_different() {
    final DataProtectionSettings oldSettings =
        DataProtectionSettings.newBuilder()
            .setScheduledPolicyItems(new ArrayList<>())
            .setOnDemandPolicyItem(
                PolicyItem.builder()
                    .id(new ObjectId())
                    .frequencyInterval(0)
                    .frequencyType(BackupFrequencyType.ON_DEMAND)
                    .retention(Duration.ofDays(2))
                    .build())
            .setPitEnabled(true)
            .setRestoreWindowDays(5)
            .setCopyProtectionEnabled(false)
            .setEncryptionAtRestEnabled(true)
            .build();

    final DataProtectionSettings newSettings =
        DataProtectionSettings.newBuilder()
            .setScheduledPolicyItems(
                List.of(
                    PolicyItem.builder()
                        .id(new ObjectId())
                        .frequencyInterval(4)
                        .frequencyType(BackupFrequencyType.HOURLY)
                        .retention(Duration.ofDays(3))
                        .build()))
            .setOnDemandPolicyItem(
                PolicyItem.builder()
                    .id(new ObjectId())
                    .frequencyInterval(0)
                    .frequencyType(BackupFrequencyType.ON_DEMAND)
                    .retention(Duration.ofDays(3))
                    .build())
            .setPitEnabled(false)
            .setRestoreWindowDays(6)
            .setCopyProtectionEnabled(true)
            .setEncryptionAtRestEnabled(false)
            .build();

    final List<String> updatedFields =
        _underTest.getUpdatedDataProtectionFields(oldSettings, newSettings);

    Assertions.assertEquals(
        Set.of(
            "scheduledPolicyItems",
            "onDemandPolicyItem",
            "pitEnabled",
            "restoreWindowDays",
            "encryptionAtRestEnabled",
            "copyProtectionEnabled"),
        new HashSet<>(updatedFields));
  }

  @Test
  public void testGetUpdatedDataProtectionFields_same() {
    final DataProtectionSettings oldSettings =
        DataProtectionSettings.newBuilder()
            .setScheduledPolicyItems(
                List.of(
                    PolicyItem.builder()
                        .id(new ObjectId())
                        .frequencyInterval(4)
                        .frequencyType(BackupFrequencyType.HOURLY)
                        .retention(Duration.ofDays(3))
                        .build(),
                    PolicyItem.builder()
                        .id(new ObjectId())
                        .frequencyInterval(6)
                        .frequencyType(BackupFrequencyType.DAILY)
                        .retention(Duration.ofDays(8))
                        .build()))
            .setOnDemandPolicyItem(
                PolicyItem.builder()
                    .id(new ObjectId())
                    .frequencyInterval(0)
                    .frequencyType(BackupFrequencyType.ON_DEMAND)
                    .retention(Duration.ofDays(2))
                    .build())
            .setPitEnabled(true)
            .setRestoreWindowDays(5)
            .setCopyProtectionEnabled(false)
            .setEncryptionAtRestEnabled(true)
            .build();

    final DataProtectionSettings newSettings =
        DataProtectionSettings.newBuilder()
            .setScheduledPolicyItems(
                List.of(
                    PolicyItem.builder()
                        .id(new ObjectId())
                        .frequencyInterval(6)
                        .frequencyType(BackupFrequencyType.DAILY)
                        .retention(Duration.ofDays(8))
                        .build(),
                    PolicyItem.builder()
                        .id(new ObjectId())
                        .frequencyInterval(4)
                        .frequencyType(BackupFrequencyType.HOURLY)
                        .retention(Duration.ofDays(3))
                        .build()))
            .setOnDemandPolicyItem(
                PolicyItem.builder()
                    .id(new ObjectId())
                    .frequencyInterval(0)
                    .frequencyType(BackupFrequencyType.ON_DEMAND)
                    .retention(Duration.ofDays(2))
                    .build())
            .setPitEnabled(true)
            .setRestoreWindowDays(5)
            .setCopyProtectionEnabled(false)
            .setEncryptionAtRestEnabled(true)
            .build();

    final List<String> updatedFields =
        _underTest.getUpdatedDataProtectionFields(oldSettings, newSettings);

    Assertions.assertEquals(Set.of(), new HashSet<>(updatedFields));
  }

  @Test
  public void testSyncBackupJobWithDataProtectionPolicyItems() {
    //   Test cases (retention in days in parentheses):
    //   ORIGINAL POLICY   |  Backup Compliance Policy    | EXPECTED RESULT
    //   hourly (3)        |    hourly (3)              | no change
    //   daily (3)         |    daily (5)               | retention updated to 5, same policy item
    // ID
    //                     |    weekly (14)             | added
    //   monthly (60)      |                            | no change

    final ClusterDescription cd = mock(ClusterDescription.class);

    when(cd.isNVMe(any())).thenReturn(false);

    doNothing().when(_underTest).updatePolicyItems(any(), any(), _policyItemListCaptor.capture());

    ObjectId hourlyId = new ObjectId();
    ObjectId dailyId = new ObjectId();
    ObjectId monthlyId = new ObjectId();
    ObjectId yearlyId = new ObjectId();
    final Policy originalPolicy =
        new Policy(
            new ObjectId(),
            Arrays.asList(
                PolicyItem.builder()
                    .id(hourlyId)
                    .frequencyInterval(4)
                    .frequencyType(BackupFrequencyType.HOURLY)
                    .retention(Duration.ofDays(3))
                    .build(),
                PolicyItem.builder()
                    .id(dailyId)
                    .frequencyInterval(4)
                    .frequencyType(BackupFrequencyType.DAILY)
                    .retention(Duration.ofDays(3))
                    .build(),
                PolicyItem.builder()
                    .id(monthlyId)
                    .frequencyInterval(6)
                    .frequencyType(BackupFrequencyType.MONTHLY)
                    .retention(Duration.ofDays(60))
                    .build(),
                PolicyItem.builder()
                    .id(yearlyId)
                    .frequencyInterval(6)
                    .frequencyType(BackupFrequencyType.YEARLY)
                    .retention(Duration.ofDays(372))
                    .build()));

    final BackupJob backupJob =
        BackupJob.builder().projectId(GROUP_ID).policies(List.of(originalPolicy)).build();

    final DataProtectionSettings dpSettings =
        DataProtectionSettings.newBuilder()
            .setScheduledPolicyItems(
                List.of(
                    PolicyItem.builder()
                        .id(new ObjectId())
                        .frequencyInterval(4)
                        .frequencyType(BackupFrequencyType.HOURLY)
                        .retention(Duration.ofDays(3))
                        .build(),
                    PolicyItem.builder()
                        .id(new ObjectId())
                        .frequencyInterval(4)
                        .frequencyType(BackupFrequencyType.DAILY)
                        .retention(Duration.ofDays(5))
                        .build(),
                    PolicyItem.builder()
                        .id(new ObjectId())
                        .frequencyInterval(6)
                        .frequencyType(BackupFrequencyType.WEEKLY)
                        .retention(Duration.ofDays(14))
                        .build()))
            .setOnDemandPolicyItem(
                PolicyItem.builder()
                    .id(new ObjectId())
                    .frequencyInterval(0)
                    .frequencyType(BackupFrequencyType.ON_DEMAND)
                    .retention(Duration.ofDays(2))
                    .build())
            .setPitEnabled(true)
            .setRestoreWindowDays(5)
            .setCopyProtectionEnabled(false)
            .setEncryptionAtRestEnabled(true)
            .build();

    _underTest.syncBackupJobWithDataProtectionPolicyItems(cd, backupJob, dpSettings);
    List<PolicyItem> updatedPolicyItems = _policyItemListCaptor.getValue();

    for (final PolicyItem item : updatedPolicyItems) {
      if (item.getFrequencyType() == BackupFrequencyType.HOURLY) {
        assertEquals(item.getId(), hourlyId);
        assertEquals(item.getRetention(), Duration.ofDays(3));
      } else if (item.getFrequencyType() == BackupFrequencyType.DAILY) {
        assertEquals(item.getId(), dailyId);
        assertEquals(item.getRetention(), Duration.ofDays(5));
      } else if (item.getFrequencyType() == BackupFrequencyType.WEEKLY) {
        assertEquals(item.getRetention(), Duration.ofDays(14));
      } else if (item.getFrequencyType() == BackupFrequencyType.MONTHLY) {
        assertEquals(item.getId(), monthlyId);
        assertEquals(item.getRetention(), Duration.ofDays(60));
      } else if (item.getFrequencyType() == BackupFrequencyType.YEARLY) {
        assertEquals(item.getId(), yearlyId);
        assertEquals(item.getRetention(), Duration.ofDays(372));
      }
    }
  }

  @Test
  public void testSyncBackupJobsWithDataProtection() throws SvcException {
    final ClusterDescription cluster1 =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription("tenantCluster", GROUP_ID));
    final ClusterDescription cluster2 =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(GROUP_ID, "pausedCluster"))
            .copy()
            .setIsPaused(true)
            .build();
    final ClusterDescription cluster3 =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(GROUP_ID, "backupDisabledCluster"))
            .copy()
            .setDiskBackupEnabled(false)
            .build();
    final ClusterDescription cluster4 =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(
                    GROUP_ID, "backupEnabledClusterWithoutBackupJob"))
            .copy()
            .setDiskBackupEnabled(true)
            .build();
    final ClusterDescription cluster5 =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(
                    GROUP_ID, "backupEnabledClusterWithBackupJob"))
            .copy()
            .setDiskBackupEnabled(true)
            .build();
    final ClusterDescription cluster6 =
        new ClusterDescription(
                NDSModelTestFactory.getAWSClusterDescription(GROUP_ID, "deleteRequestedCluster"))
            .copy()
            .setDeleteRequested(true)
            .build();
    doReturn(Set.of(cluster1, cluster2, cluster3, cluster4, cluster5, cluster6))
        .when(_ndsClusterSvc)
        .getMergedClusterDescriptions(GROUP_ID);

    doReturn(Optional.empty()).when(_backupJobDao).findActive(eq(GROUP_ID), eq(cluster4.getName()));
    doReturn(Optional.of(mock(BackupJob.class)))
        .when(_backupJobDao)
        .findActive(eq(GROUP_ID), eq(cluster5.getName()));

    final DataProtectionSettings dataProtectionSettings = mock(DataProtectionSettings.class);
    doReturn(GROUP_ID).when(dataProtectionSettings).getProjectId();
    doReturn(true).when(dataProtectionSettings).isPitEnabled();

    doNothing()
        .when(_ndsClusterSvc)
        .updateCluster(
            any(),
            eq(null),
            any(),
            eq(null),
            eq(ClusterUpdateContext.forSyncBackupWithDataProtection()));
    doNothing().when(_underTest).syncClusterAndBackupJobWithDataProtectionPit(any(), any(), any());
    doNothing().when(_underTest).syncBackupJobWithDataProtectionPolicyItems(any(), any(), any());

    _underTest.syncClusterAndBackupJobWithDataProtection(dataProtectionSettings);

    verify(_backupJobDao, never()).findActive(GROUP_ID, cluster1.getName());
    verify(_backupJobDao, never()).findActive(GROUP_ID, cluster2.getName());
    verify(_backupJobDao, never()).findActive(GROUP_ID, cluster3.getName());
    verify(_backupJobDao, times(1)).findActive(GROUP_ID, cluster4.getName());
    verify(_backupJobDao, times(1)).findActive(GROUP_ID, cluster5.getName());
    verify(_backupJobDao, never()).findActive(GROUP_ID, cluster6.getName());
    verify(_ndsClusterSvc, times(2))
        .updateCluster(
            any(),
            eq(null),
            any(),
            eq(null),
            eq(ClusterUpdateContext.forSyncBackupWithDataProtection()));
  }

  @Test
  public void testGetSnapshotsAffectedByPolicyChange() {
    final ObjectId clusterUniqueId = ObjectId.get();
    PolicyItem oldPolicyItem1 =
        new PolicyItem(
            POLICY_ITEM_ID_1,
            BackupFrequencyType.DAILY,
            1,
            Duration.ofDays(3),
            BackupRetentionUnit.DAYS);
    PolicyItem newPolicyItem1 =
        new PolicyItem(
            POLICY_ITEM_ID_1,
            BackupFrequencyType.DAILY,
            1,
            Duration.ofDays(5),
            BackupRetentionUnit.DAYS);
    final List<Policy> existingPolicyList = toPolicy(oldPolicyItem1);
    final List<Policy> newPolicyList = toPolicy(newPolicyItem1);

    // backupSnapshot1 is ignored due to retention decrease since the snapshot is protected.
    final BackupSnapshot backupSnapshot1 = mock(BackupSnapshot.class);
    doReturn(false).when(backupSnapshot1).isOnDemand();
    final long snapshotCompletionTime1 = System.currentTimeMillis();
    final long scheduledDeletionTime1 = snapshotCompletionTime1 + Duration.ofDays(100).toMillis();
    doReturn(List.of(POLICY_ITEM_ID_1)).when(backupSnapshot1).getPolicyItems();
    doReturn(new Date(snapshotCompletionTime1)).when(backupSnapshot1).getSnapshotCompletionDate();
    doReturn(new Date(scheduledDeletionTime1)).when(backupSnapshot1).getScheduledDeletionDate();

    // backupSnapshot2 is not ignored because its retention is not decreased
    final BackupSnapshot backupSnapshot2 = mock(BackupSnapshot.class);
    doReturn(false).when(backupSnapshot2).isOnDemand();
    final long snapshotCompletionTime2 = System.currentTimeMillis();
    final long scheduledDeletionTime2 = snapshotCompletionTime2 + Duration.ofDays(1).toMillis();
    doReturn(List.of(POLICY_ITEM_ID_1)).when(backupSnapshot2).getPolicyItems();
    doReturn(new Date(snapshotCompletionTime2)).when(backupSnapshot2).getSnapshotCompletionDate();
    doReturn(new Date(scheduledDeletionTime2)).when(backupSnapshot2).getScheduledDeletionDate();

    doReturn(List.of(backupSnapshot1, backupSnapshot2))
        .when(_backupSnapshotDao)
        .findSnapshotsAffectedByPolicy(eq(GROUP_ID), eq(clusterUniqueId), anyList());
    doReturn(true).when(_cpsSvc).isSnapshotProtectionEnabled(any());

    List<BackupSnapshotRetentionView> snapshotsAffected =
        _underTest.getSnapshotsAffectedByPolicyChange(
            GROUP_ID, clusterUniqueId, existingPolicyList, newPolicyList);

    assertEquals(1, snapshotsAffected.size());
    assertEquals(backupSnapshot2.getId(), snapshotsAffected.get(0).getId());
  }

  @Test
  public void testValidateCanDisableDataProtectionSettings_SYSTEM_USERIsAllowed() {
    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    doReturn(List.of()).when(_cpsSvc).getDedicatedDataProtectionClusterViews(group);
    doReturn(false).when(_authzSvc).isGlobalBackupCompliancePolicyAdmin(any());
    doReturn(false)
        .when(_cpsSvc)
        .isBackupCompliancePolicyPostGaDisablePolicyFeatureFlagEnabled(group);
    // Can disable backup compliance policy because user is a SYSTEM_USER
    try {
      _underTest.validateCanDisableDataProtectionSettings(group, AppUser.SYSTEM_USER);
    } catch (SvcException e) {
      fail();
    }
  }

  @Test
  public void testValidateCanDisableDataProtectionSettings() {
    final Group group = mock(Group.class);
    final ObjectId groupId = ObjectId.get();
    final ObjectId orgId = ObjectId.get();
    doReturn(groupId).when(group).getId();
    doReturn(orgId).when(group).getOrgId();
    final AppUser appUser = mock(AppUser.class);
    doReturn(List.of()).when(_cpsSvc).getDedicatedDataProtectionClusterViews(group);

    doReturn(false)
        .when(_cpsSvc)
        .isBackupCompliancePolicyPostGaDisablePolicyFeatureFlagEnabled(group);
    // Can disable backup compliance policy because user is a Global Atlas Admin when FF is
    // disabled.
    try {
      doReturn(true).when(_authzSvc).isGlobalAtlasAdmin(appUser);
      _underTest.validateCanDisableDataProtectionSettings(group, appUser);
    } catch (SvcException e) {
      fail();
    }
    // Cannot disable backup compliance policy because user is not a Global Atlas Admin when FF is
    // disabled.
    try {
      doReturn(false).when(_authzSvc).isGlobalAtlasAdmin(appUser);
      _underTest.validateCanDisableDataProtectionSettings(group, appUser);
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.CANNOT_DISABLE_BACKUP_COMPLIANCE_POLICY, e.getErrorCode());
      assertEquals(
          "Cannot disable Backup Compliance Policy because not a global atlas admin. "
              + "This can only be edited by filing a support ticket.",
          e.getMessage());
    }

    doReturn(true)
        .when(_cpsSvc)
        .isBackupCompliancePolicyPostGaDisablePolicyFeatureFlagEnabled(group);
    // Can disable backup compliance policy because user is a Global Atlas Admin when FF is enabled.
    try {
      doReturn(true).when(_authzSvc).isGlobalAtlasAdmin(appUser);
      _underTest.validateCanDisableDataProtectionSettings(group, appUser);
    } catch (SvcException e) {
      fail();
    }
    // Can disable backup compliance policy because the user is a group owner and the project has no
    // active cluster or retained snapshot when FF is enabled.
    try {
      doReturn(false).when(_authzSvc).isGlobalAtlasAdmin(appUser);
      _underTest.validateCanDisableDataProtectionSettings(group, appUser);
    } catch (SvcException e) {
      fail();
    }
    // Cannot disable backup compliance policy because the project has at least one active cluster
    // or retained snapshot when FF is enabled.
    try {
      doReturn(false).when(_authzSvc).isGlobalAtlasAdmin(appUser);
      final List<DataProtectionClusterView> dataProtectionClusterViews =
          List.of(mock(DataProtectionClusterView.class));
      doReturn(dataProtectionClusterViews)
          .when(_cpsSvc)
          .getDedicatedDataProtectionClusterViews(group);
      _underTest.validateCanDisableDataProtectionSettings(group, appUser);
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.CANNOT_DISABLE_BACKUP_COMPLIANCE_POLICY, e.getErrorCode());
      assertEquals(
          "Cannot disable Backup Compliance Policy because the project has at least one active"
              + " cluster or retained snapshot. This can only be edited by filing a support"
              + " ticket.",
          e.getMessage());
    }
  }

  @Test
  public void testUpdateBackupPolicyFailsWithInvalidSsdV2RegionWithExtendedStorage() {
    ClusterDescription mockClusterDescription = mock(ClusterDescription.class);

    final CpsPolicySvc cpsPolicySvc =
        UnitTestUtils.create(CpsPolicySvc.class).withArgs(_ndsClusterSvc);
    doReturn(GROUP_ID).when(mockClusterDescription).getGroupId();
    doReturn(Set.of(CloudProvider.AZURE)).when(mockClusterDescription).getCloudProviders();
    doReturn(true).when(mockClusterDescription).isAnyDiskAzurePv2();
    doReturn(AzureDiskType.P60.getSizeGB() + 10.0).when(mockClusterDescription).getDiskSizeGB();

    List<CopySetting> copySettings =
        List.of(
            new CopySetting(
                CloudProvider.AZURE,
                AzureRegionName.CANADA_CENTRAL,
                new ObjectId(),
                false,
                List.of()));

    try {
      cpsPolicySvc.validateRegionNamesForAzurePV2(copySettings, mockClusterDescription);
      fail();
    } catch (SvcException e) {
      assertEquals(NDSErrorCode.INVALID_AZURE_PV2_REGION, e.getErrorCode());
    }
  }

  @Test
  public void testUpdateBackupPolicyPassesWithInvalidSsdV2RegionWithoutExtendedStorage()
      throws SvcException {
    ClusterDescription mockClusterDescription = mock(ClusterDescription.class);
    doReturn(GROUP_ID).when(mockClusterDescription).getGroupId();
    final CpsPolicySvc cpsPolicySvc =
        UnitTestUtils.create(CpsPolicySvc.class).withArgs(_ndsClusterSvc);
    doReturn(Set.of(CloudProvider.AZURE)).when(mockClusterDescription).getCloudProviders();
    doReturn(AzureDiskType.P50.getSizeGB() + 0.0).when(mockClusterDescription).getDiskSizeGB();
    doReturn(mock(Group.class)).when(_groupDao).findById((ObjectId) any());

    List<CopySetting> copySettings =
        List.of(
            new CopySetting(
                CloudProvider.AZURE,
                AzureRegionName.CANADA_CENTRAL,
                new ObjectId(),
                new ObjectId(),
                false,
                List.of()));
    cpsPolicySvc.validateRegionNamesForAzurePV2(copySettings, mockClusterDescription);
  }

  @Test
  public void testUpdateBackupPolicyPassesWithValidSsdV2RegionWithExtendedStorage()
      throws SvcException {
    ClusterDescription mockClusterDescription = mock(ClusterDescription.class);
    AzureHardwareSpec mockAzureHardwareSpec = mock(AzureHardwareSpec.class);

    final CpsPolicySvc cpsPolicySvc =
        UnitTestUtils.create(CpsPolicySvc.class).withArgs(_ndsClusterSvc);
    doReturn(GROUP_ID).when(mockClusterDescription).getGroupId();
    doReturn(Set.of(CloudProvider.AZURE)).when(mockClusterDescription).getCloudProviders();
    doReturn(Optional.of(mockAzureHardwareSpec))
        .when(mockClusterDescription)
        .getOnlyHardwareSpecForProvider(any(), any());
    doReturn(AzureDiskType.V2).when(mockAzureHardwareSpec).getDiskType();
    doReturn(AzureDiskType.P60.getSizeGB() + 10.0).when(mockClusterDescription).getDiskSizeGB();

    // validate with null copy settings
    cpsPolicySvc.validateRegionNamesForAzurePV2(null, mockClusterDescription);

    for (final RegionName region : AVAILABLE_PV2_REGIONS) {
      List<CopySetting> copySettings =
          List.of(new CopySetting(CloudProvider.AZURE, region, new ObjectId(), false, List.of()));

      cpsPolicySvc.validateRegionNamesForAzurePV2(copySettings, mockClusterDescription);
    }
  }

  @Test
  public void testUpdateExtraRetention_backupCompliancePolicyNotEnabled() {
    final BackupJob backupJob = mock(BackupJob.class);
    final ObjectId backupJobId = ObjectId.get();
    final ObjectId projectId = ObjectId.get();
    doReturn(backupJobId).when(backupJob).getId();
    doReturn(projectId).when(backupJob).getProjectId();
    doReturn(true).when(_cpsSvc).isBackupCompliancePolicyPostGaFeatureFlagEnabled(projectId);
    final List<ExtraRetentionSetting> extraRetentionSettings =
        List.of(new ExtraRetentionSetting(10, BackupFrequencyType.MONTHLY));
    try {
      _underTest.updateExtraRetention(backupJob, extraRetentionSettings, false);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.BACKUP_EXTRA_RETENTION_SETTINGS_INVALID, e.getErrorCode());
      assertTrue(e.getMessage().contains("Backup Compliance Policy setting is not enabled"));
    }
  }

  @Test
  public void testUpdateExtraRetention_duplicateFrequencyType() {
    final BackupJob backupJob = mock(BackupJob.class);
    final ObjectId backupJobId = ObjectId.get();
    final ObjectId projectId = ObjectId.get();
    doReturn(backupJobId).when(backupJob).getId();
    doReturn(projectId).when(backupJob).getProjectId();
    doReturn(true).when(_cpsSvc).isBackupCompliancePolicyPostGaFeatureFlagEnabled(projectId);
    final List<ExtraRetentionSetting> extraRetentionSettings =
        List.of(
            new ExtraRetentionSetting(10, BackupFrequencyType.MONTHLY),
            new ExtraRetentionSetting(10, BackupFrequencyType.WEEKLY),
            new ExtraRetentionSetting(20, BackupFrequencyType.MONTHLY));
    try {
      _underTest.updateExtraRetention(backupJob, extraRetentionSettings, true);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.BACKUP_EXTRA_RETENTION_SETTINGS_INVALID, e.getErrorCode());
      assertTrue(e.getMessage().contains("duplicated frequency type is not allowed"));
    }
  }

  @Test
  public void testUpdateExtraRetention_overMaxRetention() {
    final BackupJob backupJob = mock(BackupJob.class);
    final ObjectId backupJobId = ObjectId.get();
    final ObjectId projectId = ObjectId.get();
    doReturn(backupJobId).when(backupJob).getId();
    doReturn(projectId).when(backupJob).getProjectId();
    doReturn(true).when(_cpsSvc).isBackupCompliancePolicyPostGaFeatureFlagEnabled(projectId);
    final List<ExtraRetentionSetting> extraRetentionSettings =
        List.of(
            new ExtraRetentionSetting(6, BackupFrequencyType.WEEKLY),
            new ExtraRetentionSetting(-1, BackupFrequencyType.MONTHLY));
    try {
      _underTest.updateExtraRetention(backupJob, extraRetentionSettings, true);
      fail();
    } catch (final SvcException e) {
      assertEquals(NDSErrorCode.BACKUP_EXTRA_RETENTION_SETTINGS_INVALID, e.getErrorCode());
      assertTrue(
          e.getMessage()
              .contains(
                  String.format(
                      "retention days should be a non-negative integer less than %d days",
                      CpsSvc.MAX_SNAPSHOT_RETENTION_VALUE)));
    }
  }

  @Test
  public void testUpdateExtraRetention_valid() {
    final BackupJob backupJob = mock(BackupJob.class);
    final ObjectId backupJobId = ObjectId.get();
    final ObjectId projectId = ObjectId.get();
    doReturn(backupJobId).when(backupJob).getId();
    doReturn(projectId).when(backupJob).getProjectId();
    doReturn(true).when(_cpsSvc).isBackupCompliancePolicyPostGaFeatureFlagEnabled(projectId);
    final List<ExtraRetentionSetting> extraRetentionSettings =
        List.of(
            new ExtraRetentionSetting(0, BackupFrequencyType.ON_DEMAND),
            new ExtraRetentionSetting(0, BackupFrequencyType.HOURLY),
            new ExtraRetentionSetting(0, BackupFrequencyType.DAILY),
            new ExtraRetentionSetting(10, BackupFrequencyType.WEEKLY),
            new ExtraRetentionSetting(20, BackupFrequencyType.MONTHLY),
            new ExtraRetentionSetting(30, BackupFrequencyType.YEARLY));
    try {
      _underTest.updateExtraRetention(backupJob, extraRetentionSettings, true);
    } catch (SvcException e) {
      fail();
    }
  }

  @Test
  public void testValidateBackupPolicyDoesNotExceedSnapshotLimit() {
    // Non-Azure clusters have no snapshot limits
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn(Set.of(CloudProvider.AWS)).when(clusterDescription).getCloudProviders();
    final Policy policy = mock(Policy.class);
    doReturn(1000).when(_underTest).getExpectedNumSnapshotsForPolicy(List.of(policy));

    try {
      _underTest.validateBackupPolicyDoesNotExceedSnapshotLimit(
          clusterDescription, List.of(policy));
    } catch (SvcException e) {
      fail();
    }
    // Azure cluster well under limit
    doReturn(Set.of(CloudProvider.AZURE)).when(clusterDescription).getCloudProviders();
    doReturn(100).when(_underTest).getExpectedNumSnapshotsForPolicy(List.of(policy));

    try {
      _underTest.validateBackupPolicyDoesNotExceedSnapshotLimit(
          clusterDescription, List.of(policy));
    } catch (SvcException e) {
      fail();
    }

    // Azure cluster above allowed number of snapshots for policy
    doReturn(Set.of(CloudProvider.AZURE)).when(clusterDescription).getCloudProviders();
    doReturn(480).when(_underTest).getExpectedNumSnapshotsForPolicy(List.of(policy));
    assertThrows(
        SvcException.class,
        () ->
            _underTest.validateBackupPolicyDoesNotExceedSnapshotLimit(
                clusterDescription, List.of(policy)));

    // Azure cluster on disaggregated storage has no limit.
    doReturn(true).when(clusterDescription).isDisaggregatedStorageSystem();
    try {
      _underTest.validateBackupPolicyDoesNotExceedSnapshotLimit(
          clusterDescription, List.of(policy));
    } catch (SvcException e) {
      fail();
    }
  }

  @Test
  public void testGetExpectedNumSnapshotsForPolicy_Empty() {
    final List<Policy> emptyPolicies = new ArrayList<>();
    final int result = _underTest.getExpectedNumSnapshotsForPolicy(emptyPolicies);
    assertEquals(0, result);
  }

  @Test
  public void testGetExpectedNumSnapshotsForPolicy_Hourly() {
    // 1 hour frequency, 24 hours retention = 24 snapshots
    final List<PolicyItem> policyItems = new ArrayList<>();
    policyItems.add(
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.HOURLY,
            1,
            Duration.ofDays(1),
            BackupRetentionUnit.DAYS));

    final List<Policy> policies =
        Collections.singletonList(new Policy(new ObjectId(), policyItems));
    final int result = _underTest.getExpectedNumSnapshotsForPolicy(policies);
    assertEquals(24, result);
  }

  @Test
  public void testGetExpectedNumSnapshotsForPolicy_Daily() {
    // 1 day frequency, 7 days retention = 7 snapshots
    final List<PolicyItem> policyItems = new ArrayList<>();
    policyItems.add(
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.DAILY,
            1,
            Duration.ofDays(7),
            BackupRetentionUnit.DAYS));

    final List<Policy> policies =
        Collections.singletonList(new Policy(new ObjectId(), policyItems));
    final int result = _underTest.getExpectedNumSnapshotsForPolicy(policies);
    assertEquals(7, result);
  }

  @Test
  public void testGetExpectedNumSnapshotsForPolicy_Weekly() {
    // 1 week frequency, 4 weeks retention = 4 snapshots
    final List<PolicyItem> policyItems = new ArrayList<>();
    policyItems.add(
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.WEEKLY,
            1,
            Duration.ofDays(28),
            BackupRetentionUnit.WEEKS));

    final List<Policy> policies =
        Collections.singletonList(new Policy(new ObjectId(), policyItems));
    final int result = _underTest.getExpectedNumSnapshotsForPolicy(policies);
    assertEquals(4, result);
  }

  @Test
  public void testGetExpectedNumSnapshotsForPolicy_Monthly() {
    // 1 month frequency, 3 months retention = 3 snapshots
    final List<PolicyItem> policyItems = new ArrayList<>();
    policyItems.add(
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.MONTHLY,
            1,
            Duration.ofDays(31 * 3),
            BackupRetentionUnit.MONTHS));

    final List<Policy> policies =
        Collections.singletonList(new Policy(new ObjectId(), policyItems));
    final int result = _underTest.getExpectedNumSnapshotsForPolicy(policies);
    assertEquals(3, result);
  }

  @Test
  public void testGetExpectedNumSnapshotsForPolicy_Yearly() {
    // 1 year frequency, 2 years retention = 2 snapshots
    final List<PolicyItem> policyItems = new ArrayList<>();
    policyItems.add(
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.YEARLY,
            1,
            Duration.ofDays(31 * 12 * 2),
            BackupRetentionUnit.YEARS));

    final List<Policy> policies =
        Collections.singletonList(new Policy(new ObjectId(), policyItems));
    final int result = _underTest.getExpectedNumSnapshotsForPolicy(policies);
    assertEquals(2, result);
  }

  @Test
  public void testGetExpectedNumSnapshotsForPolicy_MultiplePolicyItems() {
    final List<PolicyItem> policyItems = new ArrayList<>();
    // 6 hour frequency, 2 days retention = 8 snapshots
    policyItems.add(
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.HOURLY,
            6,
            Duration.ofDays(2),
            BackupRetentionUnit.DAYS));

    // 1 day frequency, 7 days retention = 7 snapshots
    policyItems.add(
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.DAILY,
            1,
            Duration.ofDays(7),
            BackupRetentionUnit.DAYS));

    // 1 week frequency, 4 weeks retention = 4 snapshots
    policyItems.add(
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.WEEKLY,
            1,
            Duration.ofDays(28),
            BackupRetentionUnit.WEEKS));

    final List<Policy> policies =
        Collections.singletonList(new Policy(new ObjectId(), policyItems));
    final int result = _underTest.getExpectedNumSnapshotsForPolicy(policies);
    // Total: 8 + 7 + 4 = 19 snapshots
    assertEquals(19, result);
  }

  @Test
  public void testGetExpectedNumSnapshotsForPolicy_NotCleanDivision() {
    // 5 hour frequency, 24 hours retention = 24/5 = 4 snapshots (integer division)
    final List<PolicyItem> policyItems = new ArrayList<>();
    policyItems.add(
        new PolicyItem(
            new ObjectId(),
            BackupFrequencyType.HOURLY,
            5,
            Duration.ofDays(1),
            BackupRetentionUnit.DAYS));

    final List<Policy> policies =
        Collections.singletonList(new Policy(new ObjectId(), policyItems));
    final int result = _underTest.getExpectedNumSnapshotsForPolicy(policies);
    assertEquals(4, result);
  }

  @Test
  public void testCreateDefaultPolicyItemListWithStorageSystem() throws SvcException {
    List<PolicyItem> policyItems = CpsPolicySvc.createDefaultPolicyItemList(false, false, null);
    assertFalse(
        policyItems.stream().anyMatch(p -> p.getFrequencyType() == BackupFrequencyType.MINUTELY));

    policyItems = CpsPolicySvc.createDefaultPolicyItemList(false, false, StorageSystem.LOCAL);
    assertFalse(
        policyItems.stream().anyMatch(p -> p.getFrequencyType() == BackupFrequencyType.MINUTELY));

    policyItems =
        CpsPolicySvc.createDefaultPolicyItemList(false, false, StorageSystem.DISAGGREGATED_STORAGE);
    assertTrue(
        policyItems.stream().anyMatch(p -> p.getFrequencyType() == BackupFrequencyType.MINUTELY));

    policyItems =
        CpsPolicySvc.createDefaultPolicyItemList(false, true, StorageSystem.DISAGGREGATED_STORAGE);
    assertTrue(
        policyItems.stream().anyMatch(p -> p.getFrequencyType() == BackupFrequencyType.MINUTELY));

    policyItems =
        CpsPolicySvc.createDefaultPolicyItemList(true, true, StorageSystem.DISAGGREGATED_STORAGE);
    assertTrue(
        policyItems.stream().anyMatch(p -> p.getFrequencyType() == BackupFrequencyType.MINUTELY));
    for (PolicyItem item : policyItems) {
      item.validate(false);
    }
  }

  private DataProtectionSettings getDataProtectionSettingsForTesting() {
    final List<PolicyItem> scheduledPolicyItemList =
        CpsPolicySvc.createDefaultPolicyItemList(false, true);

    final PolicyItem onDemandPolicyItem =
        new PolicyItem(
            null, BackupFrequencyType.ON_DEMAND, 0, Duration.ofDays(3), BackupRetentionUnit.DAYS);

    final DataProtectionSettingsBuilder builder =
        DataProtectionSettings.newBuilder()
            .setProjectId(GROUP_ID)
            .setState(State.ACTIVE)
            .setAuthorizedEmail("<EMAIL>")
            .setAuthorizedUserFirstName("foo")
            .setAuthorizedUserLastName("bar")
            .setRestoreWindowDays(5)
            .setEncryptionAtRestEnabled(false)
            .setPitEnabled(false)
            .setCopyProtectionEnabled(false)
            .setOnDemandPolicyItem(onDemandPolicyItem)
            .setScheduledPolicyItems(scheduledPolicyItemList);

    return builder.build();
  }

  private CpsPolicySvc createEmptyCpsPolicySvc() {
    return new CpsPolicySvc(
        null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
        null);
  }
}
