package com.xgen.svc.nds.healthCheck;

import static com.xgen.cloud.monitoring.topology._public.model.ReplicaSet.Member.State.DOWN;
import static com.xgen.cloud.monitoring.topology._public.model.ReplicaSet.Member.State.PRIMARY;
import static com.xgen.cloud.monitoring.topology._public.model.ReplicaSet.Member.State.RECOVERING;
import static com.xgen.cloud.monitoring.topology._public.model.ReplicaSet.Member.State.SECONDARY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.BulkWriteResult;
import com.mongodb.WriteResult;
import com.xgen.cloud.common.appsettings._public.model.VersionInfo;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.driverwrappers._public.legacy.BulkWriteOperation;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.svc.JobHandlerSvc;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.topology._public.model.ReplicaSet;
import com.xgen.cloud.nds.aws._public.svc.AWSApiSvc;
import com.xgen.cloud.nds.cloudprovider._public.model.ActionMonitoring;
import com.xgen.cloud.nds.cloudprovider._public.model.ActionMonitoring.ActionMonitoringData;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.Action;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware.ScheduledAction;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareHealth;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareHealth.HealthItem;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardwareModelTestFactory;
import com.xgen.cloud.nds.module._public.util.CloudProviderRegistryUtil;
import com.xgen.cloud.nds.project._private.dao.NDSGroupDao;
import com.xgen.cloud.nds.project._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ElevatedHealthMonitoringModelTestFactory;
import com.xgen.cloud.nds.project._public.model.HealthCheckMetadata;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardwareModelTestFactory;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.ActionToMonitor;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.ActionToMonitor.Status;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.ElevatedHealthMonitoring;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.ElevatedHealthMonitoring.MonitoringCadence;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.ElevatedHealthMonitoring.MonitoringStats;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.ElevatedHealthMonitoringResult;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults.BaselineData;
import com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringThresholds;
import com.xgen.cloud.nds.project._public.svc.elevatedhealthmonitoring.ElevatedHealthMonitoringResultSvc;
import com.xgen.cloud.nds.project._public.svc.elevatedhealthmonitoring.ElevatedHealthMonitoringSvc;
import com.xgen.cloud.nds.project._public.svc.elevatedhealthmonitoring.ElevatedHealthMonitoringSvc.LogContext;
import com.xgen.svc.nds.healthCheck.NDSInstanceHealthCheckJobHandler.HealthCheckOutcome;
import com.xgen.svc.nds.healthCheck.NDSInstanceHealthCheckJobHandler.InstanceHardwareHealthResult;
import com.xgen.svc.nds.model.NDSModelTestFactory;
import com.xgen.svc.nds.svc.NDSInstanceHealthCheckSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang.time.DateUtils;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

public class NDSInstanceHealthCheckJobHandlerUnitTests {
  private JobHandlerSvc _jobHandlerSvc;
  private NDSGroupDao _ndsGroupDao;
  private NDSClusterSvc _ndsClusterSvc;
  private ReplicaSetHardwareDao _replicaSetHardwareDao;
  private HostClusterLifecycleSvc _hostClusterLifecycleSvc;
  private AWSApiSvc _awsApiSvc;
  private AppSettings _appSettings;
  private NDSGroupSvc _ndsGroupSvc;
  private GroupSvc _groupSvc;
  private ElevatedHealthMonitoringSvc _elevatedHealthMonitoringSvc;
  private ElevatedHealthMonitoringResultSvc _elevatedHealthMonitoringResultSvc;
  private NDSInstanceHealthCheckJobHandler _jobHandler;

  @BeforeEach
  public void setUp() {
    CloudProviderRegistryUtil.registerAllProvider();
    _jobHandlerSvc = mock(JobHandlerSvc.class);
    _ndsGroupDao = mock(NDSGroupDao.class);
    _ndsClusterSvc = mock(NDSClusterSvc.class);
    _replicaSetHardwareDao = mock(ReplicaSetHardwareDao.class);
    doNothing()
        .when(_replicaSetHardwareDao)
        .appendHealthUpdate(any(), any(), any(), anyBoolean(), any());
    final BulkWriteOperation bulkWriteOperation = mock(BulkWriteOperation.class);
    doReturn(mock(BulkWriteResult.class)).when(bulkWriteOperation).execute();
    doReturn(bulkWriteOperation).when(_replicaSetHardwareDao).getBulkWriteOperation();
    _hostClusterLifecycleSvc = mock(HostClusterLifecycleSvc.class);
    _awsApiSvc = mock(AWSApiSvc.class);
    _appSettings = mock(AppSettings.class);
    _ndsGroupSvc = mock(NDSGroupSvc.class);
    _groupSvc = mock(GroupSvc.class);
    _elevatedHealthMonitoringSvc = mock(ElevatedHealthMonitoringSvc.class);
    _elevatedHealthMonitoringResultSvc = mock(ElevatedHealthMonitoringResultSvc.class);
    _groupSvc = mock(GroupSvc.class);
    doReturn(true)
        .when(_appSettings)
        .getBoolProp(
            eq(NDSInstanceHealthCheckJobHandler.EHM_CHECK_RESOURCE_USAGE_DECREASE_PROP),
            anyBoolean());

    _jobHandler =
        spy(
            new NDSInstanceHealthCheckJobHandler(
                _jobHandlerSvc,
                _ndsGroupDao,
                _replicaSetHardwareDao,
                _hostClusterLifecycleSvc,
                _awsApiSvc,
                _ndsClusterSvc,
                _appSettings,
                _ndsGroupSvc,
                _groupSvc,
                _elevatedHealthMonitoringSvc,
                _elevatedHealthMonitoringResultSvc));
    doReturn(mock(VersionInfo.class)).when(_appSettings).getVersionInfo();
  }

  @AfterEach
  public void teardown() {
    CloudProviderRegistryUtil.deregisterAllProvider();
  }

  @Test
  public void testHandleWork() {
    final ObjectId groupId = new ObjectId();
    final NDSGroup group = mock(NDSGroup.class);
    final HealthCheckMetadata healthCheckMetadata = mock(HealthCheckMetadata.class);
    doReturn(healthCheckMetadata).when(group).getHealthCheckMetadata();

    final ObjectId jobId = new ObjectId();
    final BasicDBObject params = new BasicDBObject("groupId", groupId);

    doReturn(groupId).when(group).getGroupId();
    doReturn(Optional.of(group)).when(_ndsGroupDao).find(eq(groupId));
    doNothing()
        .when(_jobHandler)
        .setNextHealthCheckAndUnsetJobId(any(), any(), anyInt(), anyInt(), any(), any());

    final Job job = mock(Job.class);
    doReturn(job).when(_jobHandlerSvc).getJob(eq(jobId));
    doReturn((new Date()).getTime()).when(job).getCreated();

    doNothing().when(_jobHandlerSvc).processJobAsWaitingForEvent(any(), anyLong());
    doNothing().when(_jobHandlerSvc).handleTerminalError(any(), any(), any());
    doNothing().when(_jobHandler).publishGroupHealthCheckMetrics(any(), any());

    // If health check succeeds, group is marked as ready for next health check and job isn't put
    // back on queue
    final Date healWaitDate = new Date();
    doReturn(Optional.of(healWaitDate)).when(_jobHandler).doHealthCheck(eq(group), any());
    doReturn(false).when(_jobHandler).isAllowListFeatureFlagEnabled(eq(group));
    _jobHandler.handleWork(params, jobId);
    verify(_jobHandler, times(1)).doHealthCheck(eq(group), any());
    verify(_jobHandler, times(1))
        .setNextHealthCheckAndUnsetJobId(any(), any(), anyInt(), anyInt(), eq(healWaitDate), any());
    verify(_jobHandlerSvc, times(0)).processJobAsWaitingForEvent(eq(jobId), anyLong());
    verify(_jobHandlerSvc, times(0)).handleTerminalError(eq(jobId), any(), any());
    verify(_jobHandler, times(1)).publishGroupHealthCheckMetrics(any(), any());

    // If health check fails with throttle exception, job is put back on queue as waiting for event
    doThrow(new NDSInstanceHealthCheckSvc.HealthCheckThrottleException(CloudProvider.AWS))
        .when(_jobHandler)
        .doHealthCheck(eq(group), any());
    _jobHandler.handleWork(params, jobId);
    verify(_jobHandler, times(2)).doHealthCheck(eq(group), any());
    verify(_jobHandler, times(1))
        .setNextHealthCheckAndUnsetJobId(any(), any(), anyInt(), anyInt(), any(), any());
    verify(_jobHandlerSvc, times(1)).processJobAsWaitingForEvent(eq(jobId), anyLong());
    verify(_jobHandlerSvc, times(0)).handleTerminalError(eq(jobId), any(), any());
    verify(_jobHandler, times(1)).publishGroupHealthCheckMetrics(any(), any());

    // If health check fails with unexpected exception, job is marked as failing with terminal error
    // and group is set have another health check
    doThrow(new RuntimeException()).when(_jobHandler).doHealthCheck(eq(group), any());
    _jobHandler.handleWork(params, jobId);
    verify(_jobHandler, times(3)).doHealthCheck(eq(group), any());
    verify(_jobHandler, times(1))
        .setNextHealthCheckAndUnsetJobId(any(), any(), anyInt(), anyInt(), eq(null), any());
    verify(_jobHandlerSvc, times(1)).processJobAsWaitingForEvent(eq(jobId), anyLong());
    verify(_jobHandlerSvc, times(1)).handleTerminalError(eq(jobId), any(), any());
    verify(_jobHandler, times(1)).publishGroupHealthCheckMetrics(any(), any());
    verify(_jobHandler, times(1)).incrementCounter(any(), any(Throwable.class));
  }

  @Test
  public void testHandleWork_withHardware_provisioned() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final ClusterDescription cluster =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getGroupId()));
    final BasicDBObject replicaSetHardware =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, group.getCloudProviderContainers().get(0).getId(), cluster);
    testHandleWork_withHardware(group, replicaSetHardware, 3, false);
  }

  @Test
  public void testHandleWork_withHardware_provisioned_inUnableToPlanCluster() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final ClusterDescription cluster =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getGroupId()));
    doReturn(List.of(cluster.getName())).when(group).getUnableToPlanClusterNames();

    final BasicDBObject replicaSetHardware =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, group.getCloudProviderContainers().get(0).getId(), cluster);
    testHandleWork_withHardware(group, replicaSetHardware, 3, true);
  }

  @Test
  public void testHandleWork_withHardware_unprovisioned() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final ClusterDescription cluster =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getGroupId()));
    final BasicDBObject replicaSetHardware =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, group.getCloudProviderContainers().get(0).getId(), cluster);
    // mark one hardware as unprovisioned
    ((BasicDBObject) ((BasicDBList) replicaSetHardware.get("cloudProviderHardware")).get(0))
        .put("provisioned", false);
    testHandleWork_withHardware(group, replicaSetHardware, 2, false);
  }

  @Test
  public void testHandleWork_withHardware_noPublicIP() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final ClusterDescription cluster =
        ClusterDescription.getCloudProviderClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription(group.getGroupId()));
    final BasicDBObject replicaSetHardware =
        ReplicaSetHardwareModelTestFactory.getReplicaSetHardwareFull(
            0, group.getCloudProviderContainers().get(0).getId(), cluster);
    // mark one hardware as missing publicIP
    ((BasicDBObject) ((BasicDBList) replicaSetHardware.get("cloudProviderHardware")).get(0))
        .put("publicIP", null);
    testHandleWork_withHardware(group, replicaSetHardware, 2, false);
  }

  private void testHandleWork_withHardware(
      final NDSGroup pGroup,
      final BasicDBObject pReplicaSetHardwareJson,
      final int pExpectedChecks,
      final boolean pSetPlanAsapCalled) {
    final ObjectId groupId = pGroup.getGroupId();
    final DBCursor hardwareCursor = mock(DBCursor.class);
    final ReplicaSetHardware replicaSetHardware = new ReplicaSetHardware(pReplicaSetHardwareJson);
    final InstanceHardware instanceHardware =
        replicaSetHardware.getAllHardware().findFirst().orElseThrow();

    // set up mock health result with a healthy primary
    final InstanceHardwareHealthResult healthResult =
        new InstanceHardwareHealthResult(
            InstanceHardwareModelTestFactory.getAWSInstanceHardwareHealth().toBuilder()
                .setReplicaSetMemberState(
                    new InstanceHardwareHealth.HealthItem(
                        new Date(), ReplicaSet.Member.State.PRIMARY.name()))
                .setCollectedForDate(new Date())
                .build(),
            replicaSetHardware,
            instanceHardware);
    doReturn(healthResult)
        .when(_jobHandler)
        .performChecks(eq(pGroup), any(), any(), any(), any(), any());

    // cursor returns one hardware
    doReturn(true, false).when(hardwareCursor).hasNext();
    doReturn(pReplicaSetHardwareJson).when(hardwareCursor).next();
    doReturn(hardwareCursor).when(_replicaSetHardwareDao).findAllWithActiveInstances(eq(groupId));

    _jobHandler.doHealthCheck(pGroup, new Date());

    verify(_jobHandler, times(pExpectedChecks))
        .performChecks(eq(pGroup), any(), any(), any(), any(), any());

    final int setPlanAsapCalls = pSetPlanAsapCalled ? 1 : 0;
    verify(_ndsGroupDao, times(setPlanAsapCalls)).setPlanASAP(eq(groupId));
  }

  @Test
  public void testDetectHealthCheckOutcome() {
    final NDSGroup group = mock(NDSGroup.class);
    final String clusterName = "foo";

    doNothing().when(_jobHandler).publishHealActionDetectionLatency(any(), any(), any());

    final HealthCheckOutcome noHealthResultsOutcome =
        _jobHandler.detectHealthCheckOutcome(group, new ArrayList<>());
    assertFalse(noHealthResultsOutcome.isPlanAsapRequired());
    assertTrue(noHealthResultsOutcome.healthCheckDate().isEmpty());

    final InstanceHardwareHealthResult healthResult = mock(InstanceHardwareHealthResult.class);
    final InstanceHardware instanceHardware = mock(InstanceHardware.class);
    final ReplicaSetHardware replicaSetHardware = mock(ReplicaSetHardware.class);
    final InstanceHardware.Builder builder = mock(InstanceHardware.Builder.class);
    final InstanceHardware updatedInstanceHardware = mock(InstanceHardware.class);

    doReturn(instanceHardware).when(healthResult).getInstanceHardware();
    doReturn(replicaSetHardware).when(healthResult).getReplicaSetHardware();
    doReturn(builder).when(instanceHardware).copy();
    doReturn(builder).when(builder).setHealth(any());
    doReturn(updatedInstanceHardware).when(builder).build();

    doReturn(clusterName).when(replicaSetHardware).getClusterName();

    // case Action.NONE, isPlanAsapRequired is false since action is not actionable
    final ScheduledAction noneAction = new ScheduledAction(Action.NONE, new Date());
    doReturn(noneAction).when(updatedInstanceHardware).getHealAction(any(), any());

    final HealthCheckOutcome noneOutcome =
        _jobHandler.detectHealthCheckOutcome(group, Collections.singletonList(healthResult));
    assertFalse(noneOutcome.isPlanAsapRequired());
    assertTrue(noneOutcome.healthCheckDate().isEmpty());

    // test that cluster that was in unableToPlanClusters returns HealthCheckOutcome
    // with isPlanAsapRequired set correctly: all have case Action.NONE
    final InstanceHardwareHealth instanceHardwareHealth = mock(InstanceHardwareHealth.class);
    doReturn(List.of(clusterName)).when(group).getUnableToPlanClusterNames();
    doReturn(instanceHardwareHealth).when(healthResult).getHealth();
    final Date now = new Date();

    // case cluster name is in unableToPlanClusters and replica set member state is secondary ->
    // should return (false, any)
    final InstanceHardwareHealth.HealthItem notPrimaryOrSecondary =
        new HealthItem(now, RECOVERING.name());
    doReturn(Optional.of(notPrimaryOrSecondary))
        .when(instanceHardwareHealth)
        .getReplicaSetMemberState();

    final HealthCheckOutcome stillUnhealthyOutcome =
        _jobHandler.detectHealthCheckOutcome(group, Collections.singletonList(healthResult));
    assertFalse(stillUnhealthyOutcome.isPlanAsapRequired());

    // case cluster name is in unableToPlanClusters and replica set member state is primary ->
    // should return (true, any)
    final InstanceHardwareHealth.HealthItem primary = new HealthItem(now, PRIMARY.name());
    doReturn(Optional.of(primary)).when(instanceHardwareHealth).getReplicaSetMemberState();

    final HealthCheckOutcome primaryOutcome =
        _jobHandler.detectHealthCheckOutcome(group, Collections.singletonList(healthResult));
    assertTrue(primaryOutcome.isPlanAsapRequired());

    // case cluster name is in unableToPlanClusters and replica set member state is secondary ->
    // should return (true, any)
    final InstanceHardwareHealth.HealthItem secondary = new HealthItem(now, SECONDARY.name());
    doReturn(Optional.of(secondary)).when(instanceHardwareHealth).getReplicaSetMemberState();

    final HealthCheckOutcome secondaryOutcome =
        _jobHandler.detectHealthCheckOutcome(group, Collections.singletonList(healthResult));
    assertTrue(secondaryOutcome.isPlanAsapRequired());

    // test behavior when cluster is not in unableToPlanClusters
    doReturn(List.of()).when(group).getUnableToPlanClusterNames();

    // case Action.HEAL_RESTART, isPlanAsapRequired is false since new Action is same as
    // previous Action for instanceHardware
    final ScheduledAction healRestartAction = new ScheduledAction(Action.HEAL_RESTART, new Date());
    doReturn(healRestartAction).when(updatedInstanceHardware).getHealAction(any(), any());
    doReturn(healRestartAction).when(instanceHardware).getHealAction(any(), any());

    final HealthCheckOutcome healRestartOutcome1 =
        _jobHandler.detectHealthCheckOutcome(group, Collections.singletonList(healthResult));
    assertFalse(healRestartOutcome1.isPlanAsapRequired());
    assertTrue(healRestartOutcome1.healthCheckDate().isEmpty());

    // case Action.HEAL_RESTART, isPlanAsapRequired is true since new Action is different from
    // previous Action for instanceHardware
    doReturn(healRestartAction).when(updatedInstanceHardware).getHealAction(any(), any());
    doReturn(noneAction).when(instanceHardware).getHealAction(any(), any());

    final HealthCheckOutcome healRestartOutcome2 =
        _jobHandler.detectHealthCheckOutcome(group, Collections.singletonList(healthResult));
    assertTrue(healRestartOutcome2.isPlanAsapRequired());
    assertTrue(healRestartOutcome2.healthCheckDate().isEmpty());

    // case Action.HEAL_WAIT -> Action.HEAL_REPAIR, isPlanAsapRequired is true and Prometheus
    // metric emitted since new Action is different from previous Action for instanceHardware
    final ScheduledAction healRepairAction = new ScheduledAction(Action.HEAL_REPAIR, new Date());
    final Date healWaitDate = new Date();
    final ScheduledAction healWaitAction = new ScheduledAction(Action.HEAL_WAIT, healWaitDate);
    doReturn(healRepairAction).when(updatedInstanceHardware).getHealAction(any(), any());
    doReturn(healWaitAction).when(instanceHardware).getHealAction(any(), any());

    final HealthCheckOutcome healRepairOutcome =
        _jobHandler.detectHealthCheckOutcome(group, Collections.singletonList(healthResult));
    assertTrue(healRepairOutcome.isPlanAsapRequired());
    assertTrue(healRepairOutcome.healthCheckDate().isEmpty());
    verify(_jobHandler, times(1)).publishHealActionDetectionLatency(any(), any(), any());

    // case Action.NONE -> ACTION.HEAL_WAIT, isPlanAsapRequired is false and healthCheckDate
    // is present and equal to the Date associated with HEAL_WAIT (metric should not be emitted)
    doReturn(healWaitAction).when(updatedInstanceHardware).getHealAction(any(), any());
    doReturn(noneAction).when(instanceHardware).getHealAction(any(), any());

    final HealthCheckOutcome healWaitOutcome =
        _jobHandler.detectHealthCheckOutcome(group, Collections.singletonList(healthResult));
    assertFalse(healWaitOutcome.isPlanAsapRequired());
    assertTrue(healWaitOutcome.healthCheckDate().isPresent());
    assertEquals(healWaitDate, healWaitOutcome.healthCheckDate().get());
    verify(_jobHandler, times(1)).publishHealActionDetectionLatency(any(), any(), any());
    final ArgumentCaptor<ObjectId> groupIdCaptor = ArgumentCaptor.forClass(ObjectId.class);
    final ArgumentCaptor<String> clusterNameCaptor = ArgumentCaptor.forClass(String.class);
    verify(_ndsGroupSvc, times(1))
        .enableIncreasedFrequencyFTDCPushes(
            groupIdCaptor.capture(), clusterNameCaptor.capture(), any());
    assertEquals(group.getGroupId(), groupIdCaptor.getValue());
    assertEquals(clusterName, clusterNameCaptor.getValue());

    // case Action.NONE -> ACTION.HEAL_WAIT with multiple instance hardware, isPlanAsapRequired is
    // false and earliest HEAL_WAIT is returned as healthCheckDate in HealthCheckOutcome (metric
    // should not be emitted)
    final String clusterName2 = "foo2";
    final InstanceHardwareHealthResult healthResult2 = mock(InstanceHardwareHealthResult.class);
    final InstanceHardware instanceHardware2 = mock(InstanceHardware.class);
    final InstanceHardware.Builder builder2 = mock(InstanceHardware.Builder.class);
    final InstanceHardware updatedInstanceHardware2 = mock(InstanceHardware.class);
    final ReplicaSetHardware replicaSetHardware2 = mock(ReplicaSetHardware.class);

    doReturn(clusterName2).when(replicaSetHardware2).getClusterName();
    doReturn(replicaSetHardware2).when(healthResult2).getReplicaSetHardware();
    doReturn(instanceHardware2).when(healthResult2).getInstanceHardware();
    doReturn(builder2).when(instanceHardware2).copy();
    doReturn(builder2).when(builder2).setHealth(any());
    doReturn(updatedInstanceHardware2).when(builder2).build();

    final ScheduledAction laterHealWaitAction =
        new ScheduledAction(Action.HEAL_WAIT, DateUtils.addMinutes(healWaitDate, 1));
    doReturn(laterHealWaitAction).when(updatedInstanceHardware2).getHealAction(any(), any());
    doReturn(noneAction).when(instanceHardware2).getHealAction(any(), any());

    final HealthCheckOutcome healWaitOutcomeWithMultipleInstanceHardware =
        _jobHandler.detectHealthCheckOutcome(group, List.of(healthResult, healthResult2));
    assertFalse(healWaitOutcomeWithMultipleInstanceHardware.isPlanAsapRequired());
    assertTrue(healWaitOutcomeWithMultipleInstanceHardware.healthCheckDate().isPresent());
    assertEquals(healWaitDate, healWaitOutcome.healthCheckDate().get());
    verify(_jobHandler, times(1)).publishHealActionDetectionLatency(any(), any(), any());
    verify(_ndsGroupSvc, times(2))
        .enableIncreasedFrequencyFTDCPushes(
            groupIdCaptor.capture(), clusterNameCaptor.capture(), any());
    assertEquals(group.getGroupId(), groupIdCaptor.getValue());
    assertEquals(clusterName, clusterNameCaptor.getValue());

    // case Action.NONE -> Action.HEAL_WAIT and Action.HEAL_WAIT -> ACTION.HEAL_REPAIR,
    // isPlanAsapRequired is true, healthCheckDate is present, and metric is emitted
    doReturn(healWaitAction).when(updatedInstanceHardware).getHealAction(any(), any());
    doReturn(noneAction).when(instanceHardware).getHealAction(any(), any());
    doReturn(healRepairAction).when(updatedInstanceHardware2).getHealAction(any(), any());
    doReturn(healWaitAction).when(instanceHardware2).getHealAction(any(), any());

    final HealthCheckOutcome multipleActionOutcome =
        _jobHandler.detectHealthCheckOutcome(group, List.of(healthResult, healthResult2));
    assertTrue(multipleActionOutcome.isPlanAsapRequired());
    assertTrue(multipleActionOutcome.healthCheckDate().isPresent());
    assertEquals(healWaitDate, healWaitOutcome.healthCheckDate().get());
    verify(_jobHandler, times(2)).publishHealActionDetectionLatency(any(), any(), any());
    verify(_ndsGroupSvc, times(3))
        .enableIncreasedFrequencyFTDCPushes(
            groupIdCaptor.capture(), clusterNameCaptor.capture(), any());
    assertEquals(group.getGroupId(), groupIdCaptor.getValue());
    assertEquals(clusterName, clusterNameCaptor.getValue());
  }

  @Test
  public void testSetNextHealthCheckAndUnsetJobId_RaceCondition() {
    final ObjectId groupId = new ObjectId();
    final Date now = new Date();

    Date nextHealthCheckDateAtJobStart = DateUtils.addSeconds(now, -1);
    NDSGroup mockedRefreshedGroup = mock(NDSGroup.class);
    doReturn(nextHealthCheckDateAtJobStart).when(mockedRefreshedGroup).getNextHealthCheckDate();
    doReturn(Optional.of(mockedRefreshedGroup)).when(_ndsGroupDao).find(groupId);
    _jobHandler.setNextHealthCheckAndUnsetJobId(
        groupId, now, 20, 40, null, nextHealthCheckDateAtJobStart);
    verify(_ndsGroupDao, times(1))
        .setNextHealthCheckAndUnsetJobId(eq(groupId), argThat(date -> date.after(now)));

    nextHealthCheckDateAtJobStart = DateUtils.addSeconds(now, -1);
    mockedRefreshedGroup = mock(NDSGroup.class);
    doReturn(now).when(mockedRefreshedGroup).getNextHealthCheckDate();
    doReturn(Optional.of(mockedRefreshedGroup)).when(_ndsGroupDao).find(groupId);

    _jobHandler.setNextHealthCheckAndUnsetJobId(
        groupId, now, 20, 40, null, nextHealthCheckDateAtJobStart);
    verify(_ndsGroupDao, times(1)).setNextHealthCheckAndUnsetJobId(eq(groupId), eq(now));
  }

  @Test
  public void testSetNextHealthCheckAndUnsetJobId() {
    final ObjectId groupId = new ObjectId();
    final Date now = new Date();
    final int lowerBound = 20;
    final int upperBound = 40;
    final Date twentyAfter = DateUtils.addMinutes(now, lowerBound);
    final Date fortyAfter = DateUtils.addMinutes(now, upperBound);
    final Date earlierHealWaitDate = DateUtils.addMinutes(now, 10);
    final Date laterHealWaitDate = DateUtils.addMinutes(now, 50);

    final Date nextHealthCheckDateAtJobStart = DateUtils.addSeconds(now, -1);
    final NDSGroup mockedRefreshedGroup = mock(NDSGroup.class);
    doReturn(nextHealthCheckDateAtJobStart).when(mockedRefreshedGroup).getNextHealthCheckDate();
    doReturn(Optional.of(mockedRefreshedGroup)).when(_ndsGroupDao).find(groupId);

    // case 0: pNextHealWaitThreshold is null -> default is chosen
    _jobHandler.setNextHealthCheckAndUnsetJobId(
        groupId, now, lowerBound, upperBound, null, nextHealthCheckDateAtJobStart);
    verify(_ndsGroupDao, times(1))
        .setNextHealthCheckAndUnsetJobId(
            eq(groupId), argThat(date -> date.after(twentyAfter) && date.before(fortyAfter)));

    // case 1: pNextHealWaitThreshold < pIntervalLowerBoundMinutes -> pNextHealWaitThreshold is
    // chosen
    _jobHandler.setNextHealthCheckAndUnsetJobId(
        groupId, now, lowerBound, upperBound, earlierHealWaitDate, nextHealthCheckDateAtJobStart);
    verify(_ndsGroupDao, times(1))
        .setNextHealthCheckAndUnsetJobId(eq(groupId), eq(earlierHealWaitDate));

    // case 2: pNextHealWaitThreshold > pIntervalUpperBoundMinutes -> pNextHealWaitThreshold is not
    // chosen
    _jobHandler.setNextHealthCheckAndUnsetJobId(
        groupId, now, lowerBound, upperBound, laterHealWaitDate, nextHealthCheckDateAtJobStart);
    verify(_ndsGroupDao, times(2))
        .setNextHealthCheckAndUnsetJobId(
            eq(groupId), argThat(date -> date.after(twentyAfter) && date.before(fortyAfter)));
  }

  @Test
  public void testCompareMetrics() {
    final ActionMonitoringData.MonitoringData cpuPre =
        new ActionMonitoringData.MonitoringData(23, null);
    final ActionMonitoringData.MonitoringData memoryPre =
        new ActionMonitoringData.MonitoringData(23, null);
    final ActionMonitoringData.MonitoringData connectionCountPre =
        new ActionMonitoringData.MonitoringData(null, 32L);
    final ActionMonitoringData.MonitoringData readWriteOpsPre =
        new ActionMonitoringData.MonitoringData(null, 100L);
    final ActionMonitoringData.ReplicaSetMemberState replicaSetMemberStatePre =
        new ActionMonitoringData.ReplicaSetMemberState(new Date(), "shard0", PRIMARY);

    final ActionMonitoringData.MonitoringData cpuPostNormal =
        new ActionMonitoringData.MonitoringData(24, null);
    final ActionMonitoringData.MonitoringData memoryPostNormal =
        new ActionMonitoringData.MonitoringData(25, null);
    final ActionMonitoringData.MonitoringData connectionCountPostNormal =
        new ActionMonitoringData.MonitoringData(null, 33L);
    final ActionMonitoringData.MonitoringData readWriteOpsPostNormal =
        new ActionMonitoringData.MonitoringData(null, 105L);
    final ActionMonitoringData.ReplicaSetMemberState replicaSetMemberStatePost =
        new ActionMonitoringData.ReplicaSetMemberState(new Date(), "shard0", SECONDARY);

    final Date now = new Date();
    final ActionMonitoringData pPre =
        new ActionMonitoringData(
            now,
            cpuPre,
            memoryPre,
            connectionCountPre,
            readWriteOpsPre,
            replicaSetMemberStatePre,
            null);
    final ActionMonitoringData nodeNormal =
        new ActionMonitoringData(
            now,
            cpuPostNormal,
            memoryPostNormal,
            connectionCountPostNormal,
            readWriteOpsPostNormal,
            replicaSetMemberStatePost,
            null);

    final MonitoringThresholds thresholds = getMonitoringThresholds();
    final List<
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason>
        reasonsNormal = _jobHandler.compareMetrics(pPre, nodeNormal, thresholds);
    assertEquals(0, reasonsNormal.size());

    final ActionMonitoringData.MonitoringData cpuPost =
        new ActionMonitoringData.MonitoringData(96, null);
    final ActionMonitoringData.MonitoringData memoryPost =
        new ActionMonitoringData.MonitoringData(96, null);
    final ActionMonitoringData.MonitoringData connectionCountPost =
        new ActionMonitoringData.MonitoringData(null, 42L);
    final ActionMonitoringData.MonitoringData readWriteOpsPost =
        new ActionMonitoringData.MonitoringData(null, 110L);

    final ActionMonitoringData pPost =
        new ActionMonitoringData(
            now,
            cpuPost,
            memoryPost,
            connectionCountPost,
            readWriteOpsPost,
            replicaSetMemberStatePost,
            null);
    final List<
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason>
        reasons = _jobHandler.compareMetrics(pPre, pPost, thresholds);

    // compareMetrics now only handles host-level metrics (CPU, memory)
    assertEquals(2, reasons.size());
    assertTrue(
        reasons.contains(
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.HIGH_CPU_USAGE_INCREASE));
    assertTrue(
        reasons.contains(
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.HIGH_MEMORY_USAGE_INCREASE));

    final ActionMonitoringData.MonitoringData cpuNodeDown =
        new ActionMonitoringData.MonitoringData(0, null);
    final ActionMonitoringData.MonitoringData memoryNodeDown =
        new ActionMonitoringData.MonitoringData(0, null);
    final ActionMonitoringData.MonitoringData connectionCountNodeDown =
        new ActionMonitoringData.MonitoringData(null, 0L);
    final ActionMonitoringData.MonitoringData readWriteOpsNodeDown =
        new ActionMonitoringData.MonitoringData(null, 0L);
    final ActionMonitoringData nodeDown =
        new ActionMonitoringData(
            now,
            cpuNodeDown,
            memoryNodeDown,
            connectionCountNodeDown,
            readWriteOpsNodeDown,
            replicaSetMemberStatePost,
            null);
    final List<
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason>
        reasonsForNodeDown = _jobHandler.compareMetrics(pPre, nodeDown, thresholds);
    // compareMetrics now only handles host-level metrics (CPU, memory)
    assertEquals(2, reasonsForNodeDown.size());
    assertTrue(
        reasonsForNodeDown.contains(
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.HIGH_CPU_USAGE_DECREASE));
    assertTrue(
        reasonsForNodeDown.contains(
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.HIGH_MEMORY_USAGE_DECREASE));
    // Connection count comparison has been moved to compareShardLevelMetrics
    // so HIGH_CONNECTION_USAGE_DECREASE is no longer generated by compareMetrics

    // pre maintenance connection count as 0
    final ActionMonitoringData.MonitoringData connectionCountPreZero =
        new ActionMonitoringData.MonitoringData(null, 0L);
    final ActionMonitoringData.MonitoringData readWriteOpsPreZero =
        new ActionMonitoringData.MonitoringData(null, 0L);
    final ActionMonitoringData nodeDownZeroPre =
        new ActionMonitoringData(
            now,
            cpuNodeDown,
            memoryNodeDown,
            connectionCountPreZero,
            readWriteOpsPreZero,
            replicaSetMemberStatePost,
            null);
    final List<
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason>
        reasonsForNodeDownZero = _jobHandler.compareMetrics(nodeDownZeroPre, pPost, thresholds);
    assertEquals(2, reasonsForNodeDownZero.size());
    assertTrue(
        reasonsForNodeDownZero.contains(
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.HIGH_CPU_USAGE_INCREASE));
    assertTrue(
        reasonsForNodeDownZero.contains(
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.HIGH_MEMORY_USAGE_INCREASE));
  }

  @Test
  public void testCompareShardLevelMetrics() {
    final Date now = new Date();

    // Set up thresholds with read/write ops threshold
    final MonitoringThresholds.ResourceThreshold connectionThreshold =
        new MonitoringThresholds.ResourceThreshold(10, 15, 5, 95); // 10% decrease, 15% increase
    final MonitoringThresholds.ResourceThreshold readWriteOpsThreshold =
        new MonitoringThresholds.ResourceThreshold(20, 25, 10, 90); // 20% decrease, 25% increase

    final MonitoringThresholds thresholds =
        ElevatedHealthMonitoringModelTestFactory.getMonitoringThresholdsBuilder()
            .setConnectionCountThreshold(connectionThreshold)
            .setReadWriteOpsThreshold(readWriteOpsThreshold)
            .setUnhealthyClustersThresholdWarning(1)
            .setUnhealthyClustersThresholdCritical(1)
            .setMaxFleetClustersToMonitorCount(1)
            .setStuckPlanThresholdWarning(1)
            .setStuckPlanThresholdCritical(1)
            .setStalenessThresholdInMinutes(30)
            .build();

    final ActionMonitoringData.ReplicaSetMemberState replicaSetMemberState =
        new ActionMonitoringData.ReplicaSetMemberState(new Date(), "shard0", PRIMARY);

    // Create baseline data with three hosts having different values
    final ActionMonitoringData.MonitoringData baselineConnectionCountHost1 =
        new ActionMonitoringData.MonitoringData(null, 100L);
    final ActionMonitoringData.MonitoringData baselineReadWriteOpsHost1 =
        new ActionMonitoringData.MonitoringData(null, 1000L);
    final ActionMonitoringData baselineHost1 =
        new ActionMonitoringData(
            now,
            null,
            null,
            baselineConnectionCountHost1,
            baselineReadWriteOpsHost1,
            replicaSetMemberState,
            null);

    final ActionMonitoringData.MonitoringData baselineConnectionCountHost2 =
        new ActionMonitoringData.MonitoringData(null, 150L);
    final ActionMonitoringData.MonitoringData baselineReadWriteOpsHost2 =
        new ActionMonitoringData.MonitoringData(null, 1500L);
    final ActionMonitoringData baselineHost2 =
        new ActionMonitoringData(
            now,
            null,
            null,
            baselineConnectionCountHost2,
            baselineReadWriteOpsHost2,
            replicaSetMemberState,
            null);

    final ActionMonitoringData.MonitoringData baselineConnectionCountHost3 =
        new ActionMonitoringData.MonitoringData(null, 200L);
    final ActionMonitoringData.MonitoringData baselineReadWriteOpsHost3 =
        new ActionMonitoringData.MonitoringData(null, 2000L);
    final ActionMonitoringData baselineHost3 =
        new ActionMonitoringData(
            now,
            null,
            null,
            baselineConnectionCountHost3,
            baselineReadWriteOpsHost3,
            replicaSetMemberState,
            null);

    final Map<String, ActionMonitoringData> baselineData =
        Map.of(
            "host1", baselineHost1,
            "host2", baselineHost2,
            "host3", baselineHost3);
    // Baseline totals: connections = 450L, read/write ops = 4500L

    // Test case 1: Normal metrics - no alerts (small decreases within threshold)
    final ActionMonitoringData.MonitoringData normalConnectionCountHost1 =
        new ActionMonitoringData.MonitoringData(null, 95L); // 5% decrease
    final ActionMonitoringData.MonitoringData normalReadWriteOpsHost1 =
        new ActionMonitoringData.MonitoringData(null, 950L); // 5% decrease
    final ActionMonitoringData currentHost1Normal =
        new ActionMonitoringData(
            now,
            null,
            null,
            normalConnectionCountHost1,
            normalReadWriteOpsHost1,
            replicaSetMemberState,
            null);

    final ActionMonitoringData.MonitoringData normalConnectionCountHost2 =
        new ActionMonitoringData.MonitoringData(null, 145L); // ~3% decrease
    final ActionMonitoringData.MonitoringData normalReadWriteOpsHost2 =
        new ActionMonitoringData.MonitoringData(null, 1450L); // ~3% decrease
    final ActionMonitoringData currentHost2Normal =
        new ActionMonitoringData(
            now,
            null,
            null,
            normalConnectionCountHost2,
            normalReadWriteOpsHost2,
            replicaSetMemberState,
            null);

    final ActionMonitoringData.MonitoringData normalConnectionCountHost3 =
        new ActionMonitoringData.MonitoringData(null, 190L); // 5% decrease
    final ActionMonitoringData.MonitoringData normalReadWriteOpsHost3 =
        new ActionMonitoringData.MonitoringData(null, 1900L); // 5% decrease
    final ActionMonitoringData currentHost3Normal =
        new ActionMonitoringData(
            now,
            null,
            null,
            normalConnectionCountHost3,
            normalReadWriteOpsHost3,
            replicaSetMemberState,
            null);

    final Map<String, ActionMonitoringData> currentDataNormal =
        Map.of(
            "host1", currentHost1Normal,
            "host2", currentHost2Normal,
            "host3", currentHost3Normal);
    // Current totals: connections = 430L (4.4% decrease), read/write ops = 4300L (4.4% decrease)

    List<
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason>
        reasons = _jobHandler.compareShardLevelMetrics(baselineData, currentDataNormal, thresholds);
    assertEquals(0, reasons.size(), "Normal metrics should not trigger alerts");

    // Test case 2: Connection count decrease exceeding threshold (>10%)
    final ActionMonitoringData.MonitoringData lowConnectionCountHost1 =
        new ActionMonitoringData.MonitoringData(null, 80L); // 20% decrease
    final ActionMonitoringData.MonitoringData lowConnectionCountHost2 =
        new ActionMonitoringData.MonitoringData(null, 120L); // 20% decrease
    final ActionMonitoringData.MonitoringData lowConnectionCountHost3 =
        new ActionMonitoringData.MonitoringData(null, 160L); // 20% decrease
    // Total connections: 360L (20% decrease from 450L - exceeds 10% threshold)

    final ActionMonitoringData currentHost1LowConn =
        new ActionMonitoringData(
            now,
            null,
            null,
            lowConnectionCountHost1,
            normalReadWriteOpsHost1,
            replicaSetMemberState,
            null);
    final ActionMonitoringData currentHost2LowConn =
        new ActionMonitoringData(
            now,
            null,
            null,
            lowConnectionCountHost2,
            normalReadWriteOpsHost2,
            replicaSetMemberState,
            null);
    final ActionMonitoringData currentHost3LowConn =
        new ActionMonitoringData(
            now,
            null,
            null,
            lowConnectionCountHost3,
            normalReadWriteOpsHost3,
            replicaSetMemberState,
            null);

    final Map<String, ActionMonitoringData> currentDataLowConn =
        Map.of(
            "host1", currentHost1LowConn,
            "host2", currentHost2LowConn,
            "host3", currentHost3LowConn);

    reasons = _jobHandler.compareShardLevelMetrics(baselineData, currentDataLowConn, thresholds);
    assertEquals(1, reasons.size());
    assertTrue(
        reasons.contains(
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.HIGH_CONNECTION_USAGE_DECREASE));

    // Test case 3: Read/write ops decrease exceeding threshold (>20%)
    final ActionMonitoringData.MonitoringData lowReadWriteOpsHost1 =
        new ActionMonitoringData.MonitoringData(null, 750L); // 25% decrease
    final ActionMonitoringData.MonitoringData lowReadWriteOpsHost2 =
        new ActionMonitoringData.MonitoringData(null, 1125L); // 25% decrease
    final ActionMonitoringData.MonitoringData lowReadWriteOpsHost3 =
        new ActionMonitoringData.MonitoringData(null, 1500L); // 25% decrease
    // Total read/write ops: 3375L (25% decrease from 4500L - exceeds 20% threshold)

    final ActionMonitoringData currentHost1LowOps =
        new ActionMonitoringData(
            now,
            null,
            null,
            normalConnectionCountHost1,
            lowReadWriteOpsHost1,
            replicaSetMemberState,
            null);
    final ActionMonitoringData currentHost2LowOps =
        new ActionMonitoringData(
            now,
            null,
            null,
            normalConnectionCountHost2,
            lowReadWriteOpsHost2,
            replicaSetMemberState,
            null);
    final ActionMonitoringData currentHost3LowOps =
        new ActionMonitoringData(
            now,
            null,
            null,
            normalConnectionCountHost3,
            lowReadWriteOpsHost3,
            replicaSetMemberState,
            null);

    final Map<String, ActionMonitoringData> currentDataLowOps =
        Map.of(
            "host1", currentHost1LowOps,
            "host2", currentHost2LowOps,
            "host3", currentHost3LowOps);

    reasons = _jobHandler.compareShardLevelMetrics(baselineData, currentDataLowOps, thresholds);
    assertEquals(1, reasons.size());
    assertTrue(
        reasons.contains(
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.HIGH_READ_WRITE_OPS_DECREASE));

    // Test case 4: Both connection count and read/write ops decrease exceeding thresholds
    final Map<String, ActionMonitoringData> currentDataBothLow =
        Map.of(
            "host1",
                new ActionMonitoringData(
                    now,
                    null,
                    null,
                    lowConnectionCountHost1,
                    lowReadWriteOpsHost1,
                    replicaSetMemberState,
                    null),
            "host2",
                new ActionMonitoringData(
                    now,
                    null,
                    null,
                    lowConnectionCountHost2,
                    lowReadWriteOpsHost2,
                    replicaSetMemberState,
                    null),
            "host3",
                new ActionMonitoringData(
                    now,
                    null,
                    null,
                    lowConnectionCountHost3,
                    lowReadWriteOpsHost3,
                    replicaSetMemberState,
                    null));

    reasons = _jobHandler.compareShardLevelMetrics(baselineData, currentDataBothLow, thresholds);
    assertEquals(2, reasons.size());
    assertTrue(
        reasons.contains(
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.HIGH_CONNECTION_USAGE_DECREASE));
    assertTrue(
        reasons.contains(
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.HIGH_READ_WRITE_OPS_DECREASE));
  }

  @Test
  public void testCompareMetrics_AL2023ConnectionEstablishmentIssues() {
    // Setup test data with AL2023 connection establishment issues
    final MonitoringThresholds thresholds = getMonitoringThresholds();

    // Mock app settings for AL2023 thresholds
    doReturn(200L)
        .when(_appSettings)
        .getLongProp(
            eq(
                NDSInstanceHealthCheckJobHandler
                    .AL2023_CONNECTIONS_QUEUED_FOR_ESTABLISHMENT_THRESHOLD),
            anyLong());
    doReturn(0L)
        .when(_appSettings)
        .getLongProp(
            eq(NDSInstanceHealthCheckJobHandler.AL2023_CONNECTIONS_REJECTED_THRESHOLD), anyLong());
    doReturn(5.0)
        .when(_appSettings)
        .getDoubleProp(
            eq(NDSInstanceHealthCheckJobHandler.AL2023_CONNECTIONS_INTERRUPTED_RATE_THRESHOLD),
            anyDouble());

    // Create lastPing data with connection establishment issues
    final var connectionsData =
        com.xgen.cloud.nds.cloudprovider._public.model.LastPingData.ConnectionsData.builder()
            .queuedForEstablishment(250L)
            .establishmentRateLimit(
                com.xgen.cloud.nds.cloudprovider._public.model.LastPingData.EstablishmentRateLimit
                    .builder()
                    .rejected(5L)
                    .interruptedDueToClientDisconnect(10L)
                    .build())
            .build();

    final var lastPingData =
        com.xgen.cloud.nds.cloudprovider._public.model.LastPingData.builder()
            .connections(connectionsData)
            .build();

    // Baseline data with normal CPU/memory values and no lastPing data
    final var baselineData =
        new ActionMonitoringData(
            new Date(),
            new ActionMonitoringData.MonitoringData(50, null), // cpuData
            new ActionMonitoringData.MonitoringData(60, null), // memoryData
            new ActionMonitoringData.MonitoringData(null, 20L), // connectionCountData
            new ActionMonitoringData.MonitoringData(null, 100L), // readWriteOpsData
            new ActionMonitoringData.ReplicaSetMemberState(
                new Date(), "shard0", PRIMARY), // replicaSetMemberState
            createDefaultPingData()); // baseline with default ping data that won't trigger alerts

    // Current data with AL2023 connection establishment issues
    final var currentData =
        new ActionMonitoringData(
            new Date(),
            new ActionMonitoringData.MonitoringData(55, null), // cpuData
            new ActionMonitoringData.MonitoringData(65, null), // memoryData
            new ActionMonitoringData.MonitoringData(null, 25L), // connectionCountData
            new ActionMonitoringData.MonitoringData(null, 105L), // readWriteOpsData
            new ActionMonitoringData.ReplicaSetMemberState(
                new Date(), "shard0", PRIMARY), // replicaSetMemberState
            lastPingData);

    // Test the compareMetrics method
    final List<MonitoringResults.HostDetails.Reason> reasons =
        _jobHandler.compareMetrics(baselineData, currentData, thresholds);

    // Verify that AL2023_CONNECTION_ESTABLISHMENT_ISSUES reason is included
    assertTrue(
        reasons.contains(
            MonitoringResults.HostDetails.Reason.AL2023_CONNECTION_ESTABLISHMENT_ISSUES));
  }

  @Test
  public void testDetermineClusterUnhealthyHosts_replicaSet_DuplicateBaselineData() {
    final ObjectId actionId = ObjectId.get();
    final ActionToMonitor action = mock(ActionToMonitor.class);
    doReturn(actionId).when(action).getActionId();

    // Create pre-maintenance data with duplicate hostname
    final List<BaselineData> baselineData = populateClusterBaselineData(1, true);
    final BaselineData dataDuplicateHostname =
        new BaselineData(
            baselineData.get(0).hostname(),
            new ActionMonitoringData(
                new Date(),
                new ActionMonitoringData.MonitoringData(10, null),
                new ActionMonitoringData.MonitoringData(10, null),
                new ActionMonitoringData.MonitoringData(null, 10L),
                null,
                baselineData.get(0).monitoringData().replicaSetMemberState(),
                null));
    baselineData.add(dataDuplicateHostname);

    // Get hardware health results
    final List<InstanceHardwareHealthResult> healthResults =
        getClusterHardwareHealthResults(
            actionId,
            1,
            1,
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.NO_PRIMARY);

    // Run the method under test
    _jobHandler.determineClusterUnhealthyHosts(
        action, baselineData, healthResults, getMonitoringThresholds());

    // Create map without duplicates and verify that the first one was used and second duplicate
    // entry was discarded
    baselineData.remove(baselineData.size() - 1);
    final Map<String, ActionMonitoringData> monitoringDataMap =
        baselineData.stream()
            .collect(Collectors.toMap(BaselineData::hostname, BaselineData::monitoringData));

    verify(_jobHandler, times(1))
        .determineReplicaSetUnhealthyHosts(eq(action), eq(monitoringDataMap), any(), any());
  }

  @Test
  public void testFindNextElevatedMonitoringHealthCheckDate() throws ParseException {
    final ObjectId actionId = ObjectId.get();
    final int ITERATION_INTERVALS = 15;
    final ActionToMonitor.Builder builder =
        new ActionToMonitor.Builder(actionId, ObjectId.get(), "foo")
            .setIterationIntervalInMinutes(ITERATION_INTERVALS);
    final ActionToMonitor actionToMonitor = builder.build();

    final Date now = new SimpleDateFormat("yyyy-MM-dd").parse("2023-09-22");
    Optional<Date> nextElevatedMonitoringHealthCheckDate =
        _jobHandler.findNextElevatedMonitoringHealthCheckDate(List.of(actionToMonitor), now);
    assertTrue(nextElevatedMonitoringHealthCheckDate.isEmpty());

    Date lastMonitoredAt = DateUtils.addMinutes(now, -5);
    ActionToMonitor monitoringStartedAction =
        builder
            .setStatus(ActionToMonitor.Status.MONITORING_STARTED)
            .setLastMonitoredAt(lastMonitoredAt)
            .build();
    nextElevatedMonitoringHealthCheckDate =
        _jobHandler.findNextElevatedMonitoringHealthCheckDate(
            List.of(monitoringStartedAction), now);
    assertEquals(
        DateUtils.addMinutes(lastMonitoredAt, ITERATION_INTERVALS),
        nextElevatedMonitoringHealthCheckDate.orElseThrow());

    // When the next health check date come out to be in the past, it should return empty.
    lastMonitoredAt = DateUtils.addMinutes(now, -25);
    monitoringStartedAction =
        builder
            .setStatus(ActionToMonitor.Status.MONITORING_STARTED)
            .setLastMonitoredAt(lastMonitoredAt)
            .build();
    nextElevatedMonitoringHealthCheckDate =
        _jobHandler.findNextElevatedMonitoringHealthCheckDate(
            List.of(monitoringStartedAction), now);
    assertTrue(nextElevatedMonitoringHealthCheckDate.isEmpty());
  }

  @Test
  public void testDetermineClusterUnhealthyHosts_replicaSet() {
    final ObjectId actionId = ObjectId.get();
    final ActionToMonitor action = mock(ActionToMonitor.class);
    doReturn(actionId).when(action).getActionId();
    assertTrue(
        _jobHandler.determineClusterUnhealthyHosts(action, List.of(), List.of(), null).isEmpty());

    List<BaselineData> baselineData = populateClusterBaselineData(1, false);
    List<InstanceHardwareHealthResult> healthResult =
        getClusterHardwareHealthResults(actionId, 1, -1, null);
    List<
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails>
        hostDetails =
            _jobHandler.determineClusterUnhealthyHosts(
                action, baselineData, healthResult, getMonitoringThresholds());
    assertTrue(
        hostDetails.isEmpty(), "the cluster should be healthy if gain primary after maintenance");

    baselineData = populateClusterBaselineData(1, true);
    hostDetails =
        _jobHandler.determineClusterUnhealthyHosts(
            action, baselineData, healthResult, getMonitoringThresholds());
    assertTrue(
        hostDetails.isEmpty(),
        "the cluster should be healthy when the metrics are the same before and after maintenance");

    healthResult =
        getClusterHardwareHealthResults(
            actionId,
            1,
            1,
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.NO_PRIMARY);
    final List<
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails>
        noPrimary =
            _jobHandler.determineClusterUnhealthyHosts(
                action, baselineData, healthResult, getMonitoringThresholds());
    assertFalse(noPrimary.isEmpty());
    for (com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
            .HostDetails
        host : noPrimary) {
      assertTrue(
          host.reasons()
              .contains(
                  com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring
                      .MonitoringResults.HostDetails.Reason.NO_PRIMARY),
          "no primary should be a reason for host to be unhealthy");
    }

    healthResult =
        getClusterHardwareHealthResults(
            actionId,
            1,
            0,
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.REPLICA_SET_MEMBER_UNHEALTHY);
    final List<
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails>
        hostDown =
            _jobHandler.determineClusterUnhealthyHosts(
                action, baselineData, healthResult, getMonitoringThresholds());
    assertTrue(
        hostDown
            .get(0)
            .reasons()
            .contains(
                com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                    .HostDetails.Reason.REPLICA_SET_MEMBER_UNHEALTHY),
        "host down");

    final int primaryHostIndex = 1;
    final List<
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason>
        reasons =
            List.of(
                com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                    .HostDetails.Reason.HIGH_CPU_USAGE_INCREASE,
                com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                    .HostDetails.Reason.HIGH_CPU_USAGE_DECREASE,
                com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                    .HostDetails.Reason.HIGH_MEMORY_USAGE_INCREASE,
                com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                    .HostDetails.Reason.HIGH_MEMORY_USAGE_DECREASE,
                com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                    .HostDetails.Reason.HIGH_CONNECTION_USAGE_DECREASE);
    for (com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
            .HostDetails.Reason
        reason : reasons) {
      healthResult = getClusterHardwareHealthResults(actionId, 1, primaryHostIndex, reason);
      final List<
              com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                  .HostDetails>
          primaryMetrics =
              _jobHandler.determineClusterUnhealthyHosts(
                  action, baselineData, healthResult, getMonitoringThresholds());
      // With the new shard-level architecture, connection-related reasons may affect multiple hosts
      assertTrue(primaryMetrics.size() >= 1, "At least one host should be unhealthy");
      assertTrue(
          primaryMetrics.stream().anyMatch(host -> "shard0-01".equals(host.hostname())),
          "shard0-01 should be among the unhealthy hosts");
      assertEquals(
          "shard0-00",
          primaryMetrics.get(0).baselineHostname(),
          "primary should be shard0-00 before maintenance");
      // With the new shard-level architecture, connection-related reasons may generate
      // additional shard-level reasons, so we check that the expected reason is present
      assertTrue(
          primaryMetrics.get(0).reasons().contains(reason),
          "01 should have the expected reason: " + reason);

      healthResult = getClusterHardwareHealthResults(actionId, 1, 0, reason);
      final List<
              com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                  .HostDetails>
          host00Metrics =
              _jobHandler.determineClusterUnhealthyHosts(
                  action, baselineData, healthResult, getMonitoringThresholds());
      // With the new shard-level architecture, connection-related reasons may affect multiple hosts
      assertTrue(host00Metrics.size() >= 1, "At least one host should be unhealthy");
      assertTrue(
          host00Metrics.stream().anyMatch(host -> "shard0-00".equals(host.hostname())),
          "shard0-00 should be among the unhealthy hosts");
      // With the new shard-level architecture, baseline hostname mapping may vary
      // The important thing is that we have a valid baseline hostname
      assertNotNull(host00Metrics.get(0).baselineHostname(), "should have a baseline hostname");
      // With the new shard-level architecture, connection-related reasons may generate
      // additional shard-level reasons, so we check that the expected reason is present
      assertTrue(
          host00Metrics.get(0).reasons().contains(reason),
          "00 should have the expected reason: " + reason);

      healthResult = getClusterHardwareHealthResults(actionId, 1, 2, reason);
      final List<
              com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                  .HostDetails>
          host02Metrics =
              _jobHandler.determineClusterUnhealthyHosts(
                  action, baselineData, healthResult, getMonitoringThresholds());
      // With the new shard-level architecture, connection-related reasons may affect multiple hosts
      assertTrue(host02Metrics.size() >= 1, "At least one host should be unhealthy");
      assertTrue(
          host02Metrics.stream().anyMatch(host -> "shard0-02".equals(host.hostname())),
          "shard0-02 should be among the unhealthy hosts");
      // With the new shard-level architecture, baseline hostname mapping may vary
      // The important thing is that we have a valid baseline hostname
      assertNotNull(host02Metrics.get(0).baselineHostname(), "should have a baseline hostname");
      // With the new shard-level architecture, connection-related reasons may generate
      // additional shard-level reasons, so we check that the expected reason is present
      assertTrue(
          host02Metrics.get(0).reasons().contains(reason),
          "02 should have the expected reason: " + reason);
    }
  }

  @Test
  public void testDetermineClusterUnhealthyHosts_sharded() {
    final ObjectId actionId = ObjectId.get();
    final ActionToMonitor action = mock(ActionToMonitor.class);
    doReturn(actionId).when(action).getActionId();

    List<BaselineData> baselineData = populateClusterBaselineData(2, false);
    List<InstanceHardwareHealthResult> healthResult =
        getClusterHardwareHealthResults(actionId, 2, -1, null);

    List<
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails>
        hostDetails =
            _jobHandler.determineClusterUnhealthyHosts(
                action, baselineData, healthResult, getMonitoringThresholds());
    assertTrue(
        hostDetails.isEmpty(), "the cluster should be healthy if gain primary after maintenance");

    baselineData = populateClusterBaselineData(2, true);
    hostDetails =
        _jobHandler.determineClusterUnhealthyHosts(
            action, baselineData, healthResult, getMonitoringThresholds());
    assertTrue(
        hostDetails.isEmpty(),
        "the cluster should be healthy when the metrics are the same before and after maintenance");

    final int primaryHostIndex = 1;
    for (var i = 0; i < 2; i++) {
      // 0: one shard (shard0) not healthy, 1: both shards not healthy
      final boolean allShardUnhealthy = i == 1;
      healthResult =
          getClusterHardwareHealthResults(
              actionId,
              2,
              primaryHostIndex,
              com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                  .HostDetails.Reason.NO_PRIMARY,
              allShardUnhealthy);
      final List<
              com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                  .HostDetails>
          noPrimary =
              _jobHandler.determineClusterUnhealthyHosts(
                  action, baselineData, healthResult, getMonitoringThresholds());
      assertEquals(
          allShardUnhealthy ? 6 : 3,
          noPrimary.size(),
          "all hosts should be considered unhealthy if there is no primary in a replica set");
      for (com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
              .HostDetails
          host : noPrimary) {
        if (!allShardUnhealthy) {
          assertEquals("shard0", host.monitoringData().replicaSetMemberState().replicaSetId());
        }
        assertTrue(
            host.reasons()
                .contains(
                    com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring
                        .MonitoringResults.HostDetails.Reason.NO_PRIMARY),
            "no primary should be a reason for host to be unhealthy");
      }

      healthResult =
          getClusterHardwareHealthResults(
              actionId,
              2,
              0,
              com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                  .HostDetails.Reason.REPLICA_SET_MEMBER_UNHEALTHY,
              allShardUnhealthy);
      final List<
              com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                  .HostDetails>
          hostDown =
              _jobHandler.determineClusterUnhealthyHosts(
                  action, baselineData, healthResult, getMonitoringThresholds());
      if (i == 0) {
        assertEquals(
            "shard0",
            hostDown.get(0).monitoringData().replicaSetMemberState().replicaSetId(),
            "shard0 should have a down host");
      } else {
        assertEquals(2, hostDown.size());
      }
      assertTrue(
          hostDown
              .get(0)
              .reasons()
              .contains(
                  com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring
                      .MonitoringResults.HostDetails.Reason.REPLICA_SET_MEMBER_UNHEALTHY),
          "should have REPLICA_SET_MEMBER_UNHEALTHY as a reason due to down host");
    }

    final List<
            com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason>
        reasons =
            List.of(
                com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                    .HostDetails.Reason.HIGH_CPU_USAGE_INCREASE,
                com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                    .HostDetails.Reason.HIGH_CPU_USAGE_DECREASE,
                com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                    .HostDetails.Reason.HIGH_MEMORY_USAGE_INCREASE,
                com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                    .HostDetails.Reason.HIGH_MEMORY_USAGE_DECREASE,
                // HIGH_CONNECTION_USAGE_INCREASE removed - no longer generated by backend
                com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                    .HostDetails.Reason.HIGH_CONNECTION_USAGE_DECREASE);
    for (com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
            .HostDetails.Reason
        reason : reasons) {
      healthResult = getClusterHardwareHealthResults(actionId, 2, primaryHostIndex, reason);
      final List<
              com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                  .HostDetails>
          primaryMetrics =
              _jobHandler.determineClusterUnhealthyHosts(
                  action, baselineData, healthResult, getMonitoringThresholds());
      // With the new shard-level architecture, connection-related reasons may affect multiple hosts
      assertTrue(primaryMetrics.size() >= 1, "At least one host should be unhealthy");
      assertTrue(
          primaryMetrics.stream().anyMatch(host -> "shard0-01".equals(host.hostname())),
          "shard0-01 should be among the unhealthy hosts");
      assertEquals(
          "shard0-00",
          primaryMetrics.get(0).baselineHostname(),
          "primary should be 00 before maintenance");
      // With the new shard-level architecture, connection-related reasons may generate
      // additional shard-level reasons, so we check that the expected reason is present
      assertTrue(
          primaryMetrics.get(0).reasons().contains(reason),
          "01 should have the expected reason: " + reason);

      healthResult = getClusterHardwareHealthResults(actionId, 2, 0, reason);
      final List<
              com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                  .HostDetails>
          host00Metrics =
              _jobHandler.determineClusterUnhealthyHosts(
                  action, baselineData, healthResult, getMonitoringThresholds());
      // With the new shard-level architecture, connection-related reasons may affect multiple hosts
      assertTrue(host00Metrics.size() >= 1, "At least one host should be unhealthy");
      assertTrue(
          host00Metrics.stream().anyMatch(host -> "shard0-00".equals(host.hostname())),
          "shard0-00 should be among the unhealthy hosts");
      // With the new shard-level architecture, baseline hostname mapping may vary
      // The important thing is that we have a valid baseline hostname
      assertNotNull(host00Metrics.get(0).baselineHostname(), "should have a baseline hostname");
      // With the new shard-level architecture, connection-related reasons may generate
      // additional shard-level reasons, so we check that the expected reason is present
      assertTrue(
          host00Metrics.get(0).reasons().contains(reason),
          "00 should have the expected reason: " + reason);

      healthResult = getClusterHardwareHealthResults(actionId, 2, 2, reason);
      final List<
              com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                  .HostDetails>
          host02Metrics =
              _jobHandler.determineClusterUnhealthyHosts(
                  action, baselineData, healthResult, getMonitoringThresholds());
      // With the new shard-level architecture, connection-related reasons may affect multiple hosts
      assertTrue(host02Metrics.size() >= 1, "At least one host should be unhealthy");
      assertTrue(
          host02Metrics.stream().anyMatch(host -> "shard0-02".equals(host.hostname())),
          "shard0-02 should be among the unhealthy hosts");
      // With the new shard-level architecture, baseline hostname mapping may vary
      // The important thing is that we have a valid baseline hostname
      assertNotNull(host02Metrics.get(0).baselineHostname(), "should have a baseline hostname");
      // With the new shard-level architecture, connection-related reasons may generate
      // additional shard-level reasons, so we check that the expected reason is present
      assertTrue(
          host02Metrics.get(0).reasons().contains(reason),
          "02 should have the expected reason: " + reason);
    }
  }

  @Test
  public void testUpdateActionResult() {
    // empty List<HostDetails> but current iteration not 0
    final ActionToMonitor actionToMonitor = mock(ActionToMonitor.class);
    doReturn(1).when(actionToMonitor).getCurrentIteration();

    final ElevatedHealthMonitoringResult monitoringItem =
        mock(ElevatedHealthMonitoringResult.class);
    final MonitoringResults monitoringResults = mock(MonitoringResults.class);
    doReturn(monitoringResults).when(monitoringItem).getMonitoringResults();
    assertFalse(_jobHandler.updateActionResult(List.of(), actionToMonitor, monitoringItem));
    verify(_elevatedHealthMonitoringResultSvc, times(1))
        .addIterationToMonitoringResults(any(), any(), any(), anyInt());

    // one unhealthy host
    final ActionMonitoring.ActionMonitoringData iteration =
        new ActionMonitoring.ActionMonitoringData(
            Date.from(Instant.now().minus(10, ChronoUnit.MINUTES)),
            new ActionMonitoring.ActionMonitoringData.MonitoringData(70, null),
            new ActionMonitoring.ActionMonitoringData.MonitoringData(70, null),
            new ActionMonitoring.ActionMonitoringData.MonitoringData(null, 70L),
            null,
            new ActionMonitoring.ActionMonitoringData.ReplicaSetMemberState(
                new Date(), "rs-0", ReplicaSet.Member.State.SECONDARY),
            null);
    List<MonitoringResults.HostDetails> unhealthyHosts =
        List.of(
            new MonitoringResults.HostDetails(
                "hostname1",
                List.of(MonitoringResults.HostDetails.Reason.HIGH_CONNECTION_USAGE_DECREASE),
                new Date(),
                iteration,
                "hostname1"));

    doReturn(true).when(monitoringResults).isHealthy();
    assertTrue(
        _jobHandler.updateActionResult(unhealthyHosts, actionToMonitor, monitoringItem),
        "should return true as saw unhealthy hosts and previous status was HEALTHY");
    verify(_elevatedHealthMonitoringResultSvc, times(2))
        .addIterationToMonitoringResults(any(), any(), any(), anyInt());

    doReturn(false).when(monitoringResults).isHealthy();
    assertFalse(
        _jobHandler.updateActionResult(unhealthyHosts, actionToMonitor, monitoringItem),
        "should return false as monitoring result was already UNHEALTHY");
    verify(_elevatedHealthMonitoringResultSvc, times(3))
        .addIterationToMonitoringResults(any(), any(), any(), anyInt());
  }

  @Test
  public void testAlertOnUnhealthyClustersCountIfNecessary() {
    final int warningThreshold = 10;
    final int criticalThreshold = 25;
    final int newFleetUnhealthyClustersCount = 5;
    final ElevatedHealthMonitoring monitoring = mock(ElevatedHealthMonitoring.class);
    doReturn("ehmAction").when(monitoring).getActionName();
    final MonitoringThresholds thresholds =
        ElevatedHealthMonitoringModelTestFactory.getMonitoringThresholdsBuilder()
            .setUnhealthyClustersThresholdWarning(warningThreshold)
            .setUnhealthyClustersThresholdCritical(criticalThreshold)
            .setMaxFleetClustersToMonitorCount(100)
            .build();
    doReturn(thresholds).when(monitoring).getMonitoringThresholds();
    doReturn(MonitoringCadence.DISCRETE).when(monitoring).getMonitoringCadence();

    final ElevatedHealthMonitoring.MonitoringIterationUpdates updates =
        mock(ElevatedHealthMonitoring.MonitoringIterationUpdates.class);
    doReturn(newFleetUnhealthyClustersCount).when(updates).getNewFleetUnhealthyClustersCount();

    doReturn(getMonitoringStats(warningThreshold - newFleetUnhealthyClustersCount - 1, 0))
        .when(monitoring)
        .getMonitoringStats();
    _jobHandler.alertOnUnhealthyClustersCountIfNecessary(monitoring, updates);
    verify(_jobHandler, never()).incrementEMHUnhealthyCounterAndAlert(any(), any());

    reset(_jobHandler);
    MonitoringStats monitoringStats =
        getMonitoringStats(warningThreshold - newFleetUnhealthyClustersCount, 0);
    doReturn(monitoringStats).when(monitoring).getMonitoringStats();
    doReturn(MonitoringCadence.DISCRETE).when(monitoring).getMonitoringCadence();
    doReturn(Optional.of(monitoringStats)).when(monitoring).getOptionalOfMonitoringStats();
    _jobHandler.alertOnUnhealthyClustersCountIfNecessary(monitoring, updates);
    verify(_jobHandler, times(1))
        .incrementEMHUnhealthyCounterAndAlert(
            eq(NDSInstanceHealthCheckJobHandler.EHMUnhealthyLevel.WARNING), eq("ehmAction"));

    int alert = 0;
    for (int i = warningThreshold + 1; i < criticalThreshold; i++) {
      reset(_jobHandler);
      monitoringStats = getMonitoringStats(i - newFleetUnhealthyClustersCount, 0);
      doReturn(MonitoringCadence.DISCRETE).when(monitoring).getMonitoringCadence();
      doReturn(Optional.of(monitoringStats)).when(monitoring).getOptionalOfMonitoringStats();
      doReturn(monitoringStats).when(monitoring).getMonitoringStats();
      _jobHandler.alertOnUnhealthyClustersCountIfNecessary(monitoring, updates);
      if (i % warningThreshold == 0) {
        alert++;
        verify(_jobHandler, times(1))
            .incrementEMHUnhealthyCounterAndAlert(
                eq(NDSInstanceHealthCheckJobHandler.EHMUnhealthyLevel.WARNING), eq("ehmAction"));
      } else {
        verify(_jobHandler, times(0)).incrementEMHUnhealthyCounterAndAlert(any(), any());
      }
    }
    assertEquals(1, alert, "should alert on i == 20 only");

    reset(_jobHandler);
    monitoringStats = getMonitoringStats(criticalThreshold - newFleetUnhealthyClustersCount, 0);
    doReturn(MonitoringCadence.DISCRETE).when(monitoring).getMonitoringCadence();
    doReturn(Optional.of(monitoringStats)).when(monitoring).getOptionalOfMonitoringStats();
    doReturn(monitoringStats).when(monitoring).getMonitoringStats();
    _jobHandler.alertOnUnhealthyClustersCountIfNecessary(monitoring, updates);
    verify(_jobHandler, times(1))
        .incrementEMHUnhealthyCounterAndAlert(
            eq(NDSInstanceHealthCheckJobHandler.EHMUnhealthyLevel.CRITICAL), eq("ehmAction"));

    alert = 0;
    for (int i = criticalThreshold - 1; i <= warningThreshold * 10; i++) {
      reset(_jobHandler);
      monitoringStats = getMonitoringStats(i - newFleetUnhealthyClustersCount, 0);
      doReturn(MonitoringCadence.DISCRETE).when(monitoring).getMonitoringCadence();
      doReturn(Optional.of(monitoringStats)).when(monitoring).getOptionalOfMonitoringStats();
      doReturn(monitoringStats).when(monitoring).getMonitoringStats();
      _jobHandler.alertOnUnhealthyClustersCountIfNecessary(monitoring, updates);
      if (i % warningThreshold == 0 || i == criticalThreshold) {
        alert++;
        verify(_jobHandler, times(1))
            .incrementEMHUnhealthyCounterAndAlert(
                eq(NDSInstanceHealthCheckJobHandler.EHMUnhealthyLevel.CRITICAL), eq("ehmAction"));
      } else {
        verify(_jobHandler, times(0)).incrementEMHUnhealthyCounterAndAlert(any(), any());
      }
    }
    assertEquals(9, alert, "should alert on i == 25, then 30, 40, 50, ..., 100");
  }

  @Test
  public void testAlertOnStuckPlansCountIfNecessary() {
    final int warningThreshold = 5;
    final int criticalThreshold = 10;
    final int newStuckPlansCount = 2;

    final ObjectId actionId = ObjectId.get();
    final String actionName = "test-action";

    // Create LogContext for structured logging verification
    final LogContext logContext = LogContext.getInstance();

    final ActionToMonitor actionToMonitor = mock(ActionToMonitor.class);
    final ElevatedHealthMonitoring monitoring = mock(ElevatedHealthMonitoring.class);
    final ElevatedHealthMonitoring.MonitoringIterationUpdates updates =
        mock(ElevatedHealthMonitoring.MonitoringIterationUpdates.class);

    // Setup common mocks
    doReturn(actionName).when(monitoring).getActionName();
    doReturn(actionId).when(monitoring).getActionId();
    doReturn(newStuckPlansCount).when(updates).getNewStuckPlansCount();
    doReturn(MonitoringCadence.DISCRETE).when(monitoring).getMonitoringCadence();

    final MonitoringThresholds thresholds =
        ElevatedHealthMonitoringModelTestFactory.getMonitoringThresholdsBuilder()
            .setStuckPlanThresholdWarning(warningThreshold)
            .setStuckPlanThresholdCritical(criticalThreshold)
            .build();
    doReturn(thresholds).when(monitoring).getMonitoringThresholds();

    // Continuous monitoring - should increment counter and return early
    {
      doReturn(MonitoringCadence.CONTINUOUS).when(monitoring).getMonitoringCadence();
      _jobHandler.alertOnStuckPlansCountIfNecessary(
          actionToMonitor, monitoring, updates, logContext);
      verify(_jobHandler, times(1)).incrementEHMContinuousStuckPlansCounter(eq(actionName));

      // Reset for next test
      reset(_jobHandler);
      doReturn(MonitoringCadence.DISCRETE).when(monitoring).getMonitoringCadence();
    }

    // No monitoring stats - should return early without alerting
    {
      doReturn(Optional.empty()).when(monitoring).getOptionalOfMonitoringStats();
      _jobHandler.alertOnStuckPlansCountIfNecessary(
          actionToMonitor, monitoring, updates, logContext);

      verify(_jobHandler, never()).incrementEHMStuckPlansCounterAndAlert(any(), any());
      reset(_jobHandler);
    }

    // Below warning threshold - no alert
    {
      final MonitoringStats stats =
          getMonitoringStats(0, warningThreshold - newStuckPlansCount - 1);
      doReturn(Optional.of(stats)).when(monitoring).getOptionalOfMonitoringStats();

      _jobHandler.alertOnStuckPlansCountIfNecessary(
          actionToMonitor, monitoring, updates, logContext);

      verify(_jobHandler, never()).incrementEHMStuckPlansCounterAndAlert(any(), any());
      reset(_jobHandler);
    }

    // Warning threshold reached - should alert with WARNING level
    {
      final MonitoringStats stats = getMonitoringStats(0, warningThreshold - newStuckPlansCount);
      doReturn(Optional.of(stats)).when(monitoring).getOptionalOfMonitoringStats();

      _jobHandler.alertOnStuckPlansCountIfNecessary(
          actionToMonitor, monitoring, updates, logContext);

      verify(_jobHandler, times(1))
          .incrementEHMStuckPlansCounterAndAlert(
              eq(NDSInstanceHealthCheckJobHandler.EHMUnhealthyLevel.WARNING), eq(actionName));

      reset(_jobHandler);
    }

    // Critical threshold reached - should alert with CRITICAL level
    {
      final MonitoringStats stats = getMonitoringStats(0, criticalThreshold - newStuckPlansCount);
      doReturn(Optional.of(stats)).when(monitoring).getOptionalOfMonitoringStats();

      _jobHandler.alertOnStuckPlansCountIfNecessary(
          actionToMonitor, monitoring, updates, logContext);

      verify(_jobHandler, times(1))
          .incrementEHMStuckPlansCounterAndAlert(
              eq(NDSInstanceHealthCheckJobHandler.EHMUnhealthyLevel.CRITICAL), eq(actionName));

      reset(_jobHandler);
    }
  }

  @Test
  public void testRemovePendingActionToMonitorIfStale() {
    // ActionToMonitor is in PLAN_STARTED state - no action expected
    final ObjectId groupId = new ObjectId();
    final ObjectId actionId = ObjectId.get();
    final ObjectId itemId = ObjectId.get();
    final String clusterName = "test-cluster";
    final Date now = new Date();
    final ActionToMonitor actionToMonitor = mock(ActionToMonitor.class);

    // Create LogContext for method signature compatibility
    final LogContext logContext =
        LogContext.getInstance()
            .setActionId(actionId)
            .setGroupId(groupId)
            .setItemId(itemId)
            .setClusterName(clusterName);

    doReturn(Status.PLAN_STARTED).when(actionToMonitor).getStatus();
    assertFalse(
        _jobHandler.removePendingActionToMonitorIfStale(actionToMonitor, groupId, now, logContext));

    // ActionToMonitor is in PLAN_PENDING state but not stale - no logging expected
    {
      doReturn(NDSInstanceHealthCheckJobHandler.STALE_PENDING_STATE_DURATION_THRESHOLD_DEFAULT_MS)
          .when(_jobHandler)
          .getStalePendingStateDuration();
      doReturn(Status.PLAN_PENDING).when(actionToMonitor).getStatus();
      final Date fiveMinutesAgo = DateUtils.addMinutes(now, -5);
      final WriteResult writeResult = mock(WriteResult.class);
      doReturn(1).when(writeResult).getN();
      doReturn(writeResult).when(_ndsGroupDao).removeActionToMonitor(any(), any(), any(), any());
      doReturn(fiveMinutesAgo).when(actionToMonitor).getCreatedAt();

      assertFalse(
          _jobHandler.removePendingActionToMonitorIfStale(
              actionToMonitor, groupId, now, logContext));
    }

    // ActionToMonitor is stale with successful removal
    {
      final Date twoDaysAgo = DateUtils.addDays(now, -2);
      final WriteResult successfulWriteResult = mock(WriteResult.class);
      doReturn(1).when(successfulWriteResult).getN();
      doReturn(successfulWriteResult)
          .when(_ndsGroupDao)
          .removeActionToMonitor(any(), any(), any(), any());
      doReturn(twoDaysAgo).when(actionToMonitor).getCreatedAt();

      assertTrue(
          _jobHandler.removePendingActionToMonitorIfStale(
              actionToMonitor, groupId, now, logContext));

      reset(_ndsGroupDao);
    }

    // ActionToMonitor is stale with failed removal
    {
      final Date twoDaysAgo = DateUtils.addDays(now, -2);
      final WriteResult failedWriteResult = mock(WriteResult.class);
      doReturn(0).when(failedWriteResult).getN(); // Failed removal
      doReturn(failedWriteResult)
          .when(_ndsGroupDao)
          .removeActionToMonitor(any(), any(), any(), any());
      doReturn(twoDaysAgo).when(actionToMonitor).getCreatedAt();

      assertFalse(
          _jobHandler.removePendingActionToMonitorIfStale(
              actionToMonitor, groupId, now, logContext));
    }
  }

  private BaselineData getBaselineData(
      final String pHostname,
      final String replicaSetId,
      final int pCpu,
      final int pMemory,
      final long pConnectionCount,
      final ReplicaSet.Member.State pState) {
    return getBaselineData(
        pHostname, replicaSetId, pCpu, pMemory, pConnectionCount, 100L, pState, null);
  }

  private BaselineData getBaselineData(
      final String pHostname,
      final String replicaSetId,
      final int pCpu,
      final int pMemory,
      final long pConnectionCount,
      final long pReadWriteOps,
      final ReplicaSet.Member.State pState,
      final com.xgen.cloud.nds.cloudprovider._public.model.LastPingData pLastPingData) {
    final ActionMonitoringData.MonitoringData cpu =
        new ActionMonitoringData.MonitoringData(pCpu, null);
    final ActionMonitoringData.MonitoringData memory =
        new ActionMonitoringData.MonitoringData(pMemory, null);
    final ActionMonitoringData.MonitoringData connectionCount =
        new ActionMonitoringData.MonitoringData(null, pConnectionCount);
    final ActionMonitoringData.MonitoringData readWriteOps =
        new ActionMonitoringData.MonitoringData(null, pReadWriteOps);
    final ActionMonitoringData.ReplicaSetMemberState replicaSetMemberState =
        new ActionMonitoringData.ReplicaSetMemberState(new Date(), replicaSetId, pState);
    return new BaselineData(
        pHostname,
        new ActionMonitoringData(
            new Date(),
            cpu,
            memory,
            connectionCount,
            readWriteOps,
            replicaSetMemberState,
            pLastPingData));
  }

  /** Creates default LastPingData that will not trigger AL2023 connection establishment alerts */
  private com.xgen.cloud.nds.cloudprovider._public.model.LastPingData createDefaultPingData() {
    final var establishmentRateLimit =
        com.xgen.cloud.nds.cloudprovider._public.model.LastPingData.EstablishmentRateLimit.builder()
            .rejected(0L)
            .exempted(1L)
            .interruptedDueToClientDisconnect(2L)
            .build();

    final var connectionsData =
        com.xgen.cloud.nds.cloudprovider._public.model.LastPingData.ConnectionsData.builder()
            .queuedForEstablishment(50L)
            .establishmentRateLimit(establishmentRateLimit)
            .build();

    return com.xgen.cloud.nds.cloudprovider._public.model.LastPingData.builder()
        .connections(connectionsData)
        .build();
  }

  private List<BaselineData> populateClusterBaselineData(
      final int pNumShards, final boolean pHasPrimary) {
    final int NORMAL_VALUE = 23;
    final List<BaselineData> baselineData = new ArrayList<>();
    for (var j = 0; j < pNumShards; j++) {
      for (var i = 0; i < 3; i++) {
        baselineData.add(
            getBaselineData(
                "shard" + j + "-0" + i,
                "shard" + j,
                NORMAL_VALUE,
                NORMAL_VALUE,
                NORMAL_VALUE,
                i == 0 && pHasPrimary ? PRIMARY : SECONDARY));
      }
    }
    return baselineData;
  }

  private List<InstanceHardwareHealthResult> getClusterHardwareHealthResults(
      final ObjectId pMaintenanceId,
      final int pNumShards,
      final int pUnhealthyHostIndex,
      final com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
              .HostDetails.Reason
          pReason) {
    return getClusterHardwareHealthResults(
        pMaintenanceId, pNumShards, pUnhealthyHostIndex, pReason, false);
  }

  private List<InstanceHardwareHealthResult> getClusterHardwareHealthResults(
      final ObjectId pMaintenanceId,
      final int pNumShards,
      final int pUnhealthyHostIndex,
      final com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
              .HostDetails.Reason
          pReason,
      final boolean pAllShardUnhealthy) {
    final int HIGH_VALUE = 96;
    final int NORMAL_VALUE = 23;
    final int ZERO_VALUE = 0;
    final int cpu =
        pReason
                == com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring
                    .MonitoringResults.HostDetails.Reason.HIGH_CPU_USAGE_INCREASE
            ? HIGH_VALUE
            : (pReason
                    == com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring
                        .MonitoringResults.HostDetails.Reason.HIGH_CPU_USAGE_DECREASE
                ? ZERO_VALUE
                : NORMAL_VALUE);
    final int memory =
        pReason
                == com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring
                    .MonitoringResults.HostDetails.Reason.HIGH_MEMORY_USAGE_INCREASE
            ? HIGH_VALUE
            : (pReason
                    == com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring
                        .MonitoringResults.HostDetails.Reason.HIGH_MEMORY_USAGE_DECREASE
                ? ZERO_VALUE
                : NORMAL_VALUE);
    final long connection =
        pReason
                == com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring
                    .MonitoringResults.HostDetails.Reason.HIGH_CONNECTION_USAGE_DECREASE
            ? ZERO_VALUE
            : NORMAL_VALUE;
    final List<InstanceHardwareHealthResult> healthResults = new ArrayList<>();
    for (var j = 0; j < pNumShards; j++) {
      for (var i = 0; i < 3; i++) {
        ReplicaSet.Member.State state = i == 1 ? PRIMARY : SECONDARY;
        boolean isHealthy = i != pUnhealthyHostIndex;
        if (!isHealthy && !pAllShardUnhealthy && j > 0) {
          isHealthy = true; // only shard 0 is unhealthy
        }
        if (isHealthy) {
          healthResults.add(
              getHardwareHealthResult(
                  pMaintenanceId,
                  "shard" + j + "-0" + i,
                  "shard" + j,
                  NORMAL_VALUE,
                  NORMAL_VALUE,
                  NORMAL_VALUE,
                  state));
          continue;
        }
        // unhealthy host due to replica set member state
        if (pReason
            == com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.NO_PRIMARY) {
          state = SECONDARY;
        } else if (pReason
            == com.xgen.cloud.nds.project._public.model.elevatedhealthmonitoring.MonitoringResults
                .HostDetails.Reason.REPLICA_SET_MEMBER_UNHEALTHY) {
          state = DOWN;
        }
        healthResults.add(
            getHardwareHealthResult(
                pMaintenanceId,
                "shard" + j + "-0" + i,
                "shard" + j,
                cpu,
                memory,
                connection,
                state));
      }
    }
    return healthResults;
  }

  private InstanceHardwareHealthResult getHardwareHealthResult(
      final ObjectId pMaintenanceId,
      final String pHostname,
      final String pReplicaSetId,
      final int pCpu,
      final int pMemory,
      final long pConnectionCount,
      final ReplicaSet.Member.State pState) {
    final ActionMonitoringData.MonitoringData cpu =
        new ActionMonitoringData.MonitoringData(pCpu, null);
    final ActionMonitoringData.MonitoringData memory =
        new ActionMonitoringData.MonitoringData(pMemory, null);
    final ActionMonitoringData.MonitoringData connectionCount =
        new ActionMonitoringData.MonitoringData(null, pConnectionCount);
    final ActionMonitoringData.ReplicaSetMemberState replicaSetMemberState =
        new ActionMonitoringData.ReplicaSetMemberState(new Date(), pReplicaSetId, pState);
    final InstanceHardwareHealth health = mock(InstanceHardwareHealth.class);
    final ActionMonitoring monitoring =
        new ActionMonitoring(
            pMaintenanceId,
            List.of(
                new ActionMonitoringData(
                    new Date(), cpu, memory, connectionCount, null, replicaSetMemberState, null)));
    doReturn(List.of(monitoring)).when(health).getActionMonitorings();
    final ReplicaSetHardware replicaSetHardware = mock(ReplicaSetHardware.class);
    final InstanceHardware instanceHardware = mock(InstanceHardware.class);
    doReturn(Optional.of(pHostname)).when(instanceHardware).getHostnameForAgents();
    return new InstanceHardwareHealthResult(health, replicaSetHardware, instanceHardware);
  }

  private MonitoringThresholds getMonitoringThresholds() {
    return ElevatedHealthMonitoringModelTestFactory.getMonitoringThresholdsBuilder()
        .setUnhealthyClustersThresholdWarning(1)
        .setUnhealthyClustersThresholdCritical(1)
        .setMaxFleetClustersToMonitorCount(1)
        .setStuckPlanThresholdWarning(1)
        .setStuckPlanThresholdCritical(1)
        .setStalenessThresholdInMinutes(30)
        .build();
  }

  private ElevatedHealthMonitoring.MonitoringStats getMonitoringStats(
      final int pUnhealthyCount, final int pStuckPlansCount) {
    return new ElevatedHealthMonitoring.MonitoringStats(
        100, 10, 100, 0, pStuckPlansCount, pUnhealthyCount);
  }
}
