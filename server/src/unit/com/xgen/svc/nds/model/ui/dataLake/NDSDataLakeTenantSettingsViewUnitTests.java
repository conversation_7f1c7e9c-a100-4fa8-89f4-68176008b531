package com.xgen.svc.nds.model.ui.dataLake;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Date;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;

public class NDSDataLakeTenantSettingsViewUnitTests {
  @Test
  public void testGetters() {
    final Date modifiedAt = new Date();
    NDSDataLakeTenantSettingsView view1 =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(10)
            .maxConnections(10)
            .allowUnlimitedConcurrentQueries(false)
            .parquetWriterVersion(2)
            .tracingEnabled(true)
            .cursorMaxFileSize(10)
            .cursorMaxFiles(1000)
            .cursorMaxWaitTimeForAvailableSpace(1000)
            .queryExecutionBranchingDepth(2)
            .queryExecutionBranchingFactor(4)
            .queryExecutionMaxConcurrency(10)
            .queryExecutionMaxSerialNum(10)
            .queryExecutionMaxSerialSize(10)
            .queryRoutingStrategy("round_robin")
            .build();
    assertEquals(modifiedAt.toString(), view1.getModifiedAt());
    assertEquals(10, view1.getMaxConcurrentQueries());
    assertEquals(10, view1.getMaxConnections());
    assertFalse(view1.getAllowUnlimitedConcurrentQueries());
    assertEquals(2, view1.getParquetWriterVersion());
    assertTrue(view1.getTracingEnabled());
    assertEquals(10, view1.getCursorMaxFileSize());
    assertEquals(1000, view1.getCursorMaxFiles());
    assertEquals(1000, view1.getCursorMaxWaitTimeForAvailableSpace());
    assertEquals(2, view1.getQueryExecutionBranchingDepth());
    assertEquals(4, view1.getQueryExecutionBranchingFactor());
    assertEquals(10, view1.getQueryExecutionMaxConcurrency());
    assertEquals(10, view1.getQueryExecutionMaxSerialNum());
    assertEquals(10, view1.getQueryExecutionMaxSerialSize());
    assertEquals("round_robin", view1.getQueryRoutingStrategy());

    NDSDataLakeTenantSettingsView view2 = NDSDataLakeTenantSettingsView.builder().build();
    assertNull(view2.getModifiedAt());
    assertEquals(0, view2.getMaxConcurrentQueries());
    assertEquals(0, view2.getMaxConnections());
    assertFalse(view2.getAllowUnlimitedConcurrentQueries());
    assertEquals(0, view2.getParquetWriterVersion());
    assertFalse(view2.getTracingEnabled());
    assertEquals(0, view2.getCursorMaxFileSize());
    assertEquals(0, view2.getCursorMaxFiles());
    assertEquals(0, view2.getCursorMaxWaitTimeForAvailableSpace());
    assertEquals(0, view2.getQueryExecutionBranchingDepth());
    assertEquals(0, view2.getQueryExecutionBranchingFactor());
    assertEquals(0, view2.getQueryExecutionMaxConcurrency());
    assertEquals(0, view2.getQueryExecutionMaxSerialNum());
    assertEquals(0, view2.getQueryExecutionMaxSerialSize());
    assertNull(view2.getQueryRoutingStrategy());
  }

  @Test
  public void testHashCode() {
    final Date modifiedAt = new Date();
    NDSDataLakeTenantSettingsView view1 =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(10)
            .maxConnections(10)
            .allowUnlimitedConcurrentQueries(false)
            .parquetWriterVersion(2)
            .tracingEnabled(true)
            .cursorMaxFileSize(10)
            .cursorMaxFiles(1000)
            .cursorMaxWaitTimeForAvailableSpace(1000)
            .queryExecutionBranchingDepth(2)
            .queryExecutionBranchingFactor(4)
            .queryExecutionMaxConcurrency(10)
            .queryExecutionMaxSerialNum(10)
            .queryExecutionMaxSerialSize(10)
            .queryRoutingStrategy("round_robin")
            .build();
    NDSDataLakeTenantSettingsView view2 =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(10)
            .maxConnections(10)
            .allowUnlimitedConcurrentQueries(false)
            .parquetWriterVersion(2)
            .tracingEnabled(true)
            .cursorMaxFileSize(10)
            .cursorMaxFiles(1000)
            .cursorMaxWaitTimeForAvailableSpace(1000)
            .queryExecutionBranchingDepth(2)
            .queryExecutionBranchingFactor(4)
            .queryExecutionMaxConcurrency(10)
            .queryExecutionMaxSerialNum(10)
            .queryExecutionMaxSerialSize(10)
            .queryRoutingStrategy("round_robin")
            .build();
    NDSDataLakeTenantSettingsView view3 =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(20)
            .maxConnections(20)
            .allowUnlimitedConcurrentQueries(true)
            .parquetWriterVersion(3)
            .tracingEnabled(false)
            .cursorMaxFileSize(20)
            .cursorMaxFiles(2000)
            .cursorMaxWaitTimeForAvailableSpace(2000)
            .queryExecutionBranchingDepth(3)
            .queryExecutionBranchingFactor(5)
            .queryExecutionMaxConcurrency(20)
            .queryExecutionMaxSerialNum(20)
            .queryExecutionMaxSerialSize(20)
            .queryRoutingStrategy("random")
            .build();
    NDSDataLakeTenantSettingsView view4 =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(30)
            .maxConnections(30)
            .allowUnlimitedConcurrentQueries(true)
            .parquetWriterVersion(4)
            .tracingEnabled(false)
            .cursorMaxFileSize(30)
            .cursorMaxFiles(3000)
            .cursorMaxWaitTimeForAvailableSpace(3000)
            .queryExecutionBranchingDepth(4)
            .queryExecutionBranchingFactor(6)
            .queryExecutionMaxConcurrency(30)
            .queryExecutionMaxSerialNum(30)
            .queryExecutionMaxSerialSize(30)
            .queryRoutingStrategy("least_connections")
            .build();

    assertEquals(view1.hashCode(), view2.hashCode());
    assertNotEquals(view1.hashCode(), view3.hashCode());
    assertNotEquals(view1.hashCode(), view4.hashCode());
    assertNotEquals(view2.hashCode(), view3.hashCode());
    assertNotEquals(view2.hashCode(), view4.hashCode());
    assertNotEquals(view3.hashCode(), view4.hashCode());
  }

  @Test
  public void testEquals() {
    final Date modifiedAt = new Date();
    NDSDataLakeTenantSettingsView view1 =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(10)
            .maxConnections(10)
            .allowUnlimitedConcurrentQueries(false)
            .parquetWriterVersion(2)
            .tracingEnabled(true)
            .cursorMaxFileSize(10)
            .cursorMaxFiles(1000)
            .cursorMaxWaitTimeForAvailableSpace(1000)
            .queryExecutionBranchingDepth(2)
            .queryExecutionBranchingFactor(4)
            .queryExecutionMaxConcurrency(10)
            .queryExecutionMaxSerialNum(10)
            .queryExecutionMaxSerialSize(10)
            .queryRoutingStrategy("round_robin")
            .build();
    NDSDataLakeTenantSettingsView view2 =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(10)
            .maxConnections(10)
            .allowUnlimitedConcurrentQueries(false)
            .parquetWriterVersion(2)
            .tracingEnabled(true)
            .cursorMaxFileSize(10)
            .cursorMaxFiles(1000)
            .cursorMaxWaitTimeForAvailableSpace(1000)
            .queryExecutionBranchingDepth(2)
            .queryExecutionBranchingFactor(4)
            .queryExecutionMaxConcurrency(10)
            .queryExecutionMaxSerialNum(10)
            .queryExecutionMaxSerialSize(10)
            .queryRoutingStrategy("round_robin")
            .build();
    NDSDataLakeTenantSettingsView view3 =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(20)
            .maxConnections(20)
            .allowUnlimitedConcurrentQueries(true)
            .parquetWriterVersion(3)
            .tracingEnabled(false)
            .cursorMaxFileSize(20)
            .cursorMaxFiles(2000)
            .cursorMaxWaitTimeForAvailableSpace(2000)
            .queryExecutionBranchingDepth(3)
            .queryExecutionBranchingFactor(5)
            .queryExecutionMaxConcurrency(20)
            .queryExecutionMaxSerialNum(20)
            .queryExecutionMaxSerialSize(20)
            .queryRoutingStrategy("random")
            .build();
    NDSDataLakeTenantSettingsView view4 =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(30)
            .maxConnections(30)
            .allowUnlimitedConcurrentQueries(true)
            .parquetWriterVersion(4)
            .tracingEnabled(false)
            .cursorMaxFileSize(30)
            .cursorMaxFiles(3000)
            .cursorMaxWaitTimeForAvailableSpace(3000)
            .queryExecutionBranchingDepth(4)
            .queryExecutionBranchingFactor(6)
            .queryExecutionMaxConcurrency(30)
            .queryExecutionMaxSerialNum(30)
            .queryExecutionMaxSerialSize(30)
            .queryRoutingStrategy("least_connections")
            .build();

    assertEquals(view1, view2);
    assertNotEquals(view1, view3);
    assertNotEquals(view1, view4);
    assertNotEquals(view2, view3);
    assertNotEquals(view2, view4);
    assertNotEquals(view3, view4);
  }

  @Test
  public void testToDBObject() {
    final Date modifiedAt = new Date();
    NDSDataLakeTenantSettingsView view =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(10)
            .maxConnections(10)
            .allowUnlimitedConcurrentQueries(false)
            .parquetWriterVersion(2)
            .tracingEnabled(true)
            .cursorMaxFileSize(10)
            .cursorMaxFiles(1000)
            .cursorMaxWaitTimeForAvailableSpace(1000)
            .queryExecutionBranchingDepth(2)
            .queryExecutionBranchingFactor(4)
            .queryExecutionMaxConcurrency(10)
            .queryExecutionMaxSerialNum(10)
            .queryExecutionMaxSerialSize(10)
            .queryRoutingStrategy("round_robin")
            .build();

    var dbObject = view.toDBObject();
    assertEquals(modifiedAt.toString(), dbObject.get("modifiedAt"));
    assertEquals(10L, dbObject.get("maxConcurrentQueries"));
    assertEquals(10L, dbObject.get("maxConnections"));
    assertEquals(false, dbObject.get("allowUnlimitedConcurrentQueries"));
    assertEquals(2, dbObject.get("parquetWriterVersion"));
    assertEquals(true, dbObject.get("tracingEnabled"));
    assertEquals(10L, dbObject.get("cursorMaxFileSize"));
    assertEquals(1000, dbObject.get("cursorMaxFiles"));
    assertEquals(1000L, dbObject.get("cursorMaxWaitTimeForAvailableSpace"));
    assertEquals(2, dbObject.get("queryExecutionBranchingDepth"));
    assertEquals(4, dbObject.get("queryExecutionBranchingFactor"));
    assertEquals(10, dbObject.get("queryExecutionMaxConcurrency"));
    assertEquals(10, dbObject.get("queryExecutionMaxSerialNum"));
    assertEquals(10L, dbObject.get("queryExecutionMaxSerialSize"));
    assertEquals("round_robin", dbObject.get("queryRoutingStrategy"));
  }

  @Test
  public void testJSONMapping() throws Exception {
    final Date modifiedAt = new Date();
    NDSDataLakeTenantSettingsView view =
        NDSDataLakeTenantSettingsView.builder()
            .modifiedAt(modifiedAt.toString())
            .maxConcurrentQueries(10)
            .maxConnections(10)
            .allowUnlimitedConcurrentQueries(false)
            .parquetWriterVersion(2)
            .tracingEnabled(true)
            .cursorMaxFileSize(10)
            .cursorMaxFiles(1000)
            .cursorMaxWaitTimeForAvailableSpace(1000)
            .queryExecutionBranchingDepth(2)
            .queryExecutionBranchingFactor(4)
            .queryExecutionMaxConcurrency(10)
            .queryExecutionMaxSerialNum(10)
            .queryExecutionMaxSerialSize(10)
            .queryRoutingStrategy("round_robin")
            .build();

    // Ensure that the view can be serialized to JSON without errors.
    ObjectMapper mapper = new ObjectMapper();
    String jsonString = mapper.writeValueAsString(view);
    JSONObject obj = new JSONObject(jsonString);

    assertEquals(modifiedAt.toString(), obj.getString("modifiedAt"));
    assertEquals(10, obj.getInt("maxConcurrentQueries"));
    assertEquals(10, obj.getInt("maxConnections"));
    assertFalse(obj.getBoolean("allowUnlimitedConcurrentQueries"));
    assertEquals(2, obj.getInt("parquetWriterVersion"));
    assertTrue(obj.getBoolean("tracingEnabled"));
    assertEquals(10, obj.getInt("cursorMaxFileSize"));
    assertEquals(1000, obj.getInt("cursorMaxFiles"));
    assertEquals(1000, obj.getInt("cursorMaxWaitTimeForAvailableSpace"));
    assertEquals(2, obj.getInt("queryExecutionBranchingDepth"));
    assertEquals(4, obj.getInt("queryExecutionBranchingFactor"));
    assertEquals(10, obj.getInt("queryExecutionMaxConcurrency"));
    assertEquals(10, obj.getInt("queryExecutionMaxSerialNum"));
    assertEquals(10, obj.getInt("queryExecutionMaxSerialSize"));
    assertEquals("round_robin", obj.getString("queryRoutingStrategy"));
  }
}
