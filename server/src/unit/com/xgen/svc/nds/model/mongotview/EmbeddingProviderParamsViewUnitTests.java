package com.xgen.svc.nds.model.mongotview;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import java.time.Instant;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;

public class EmbeddingProviderParamsViewUnitTests {
  private static final String EXPECTED_JSON_STRING =
      """
      {
           "changeStream" : {
                   "apiToken" : "api_token_1",
                   "expirationDate" : "2023-10-26T10:30:00Z"
               },
               "collectionScan" : {
                   "apiToken" : "api_token_1",
                   "expirationDate" : "2023-10-26T10:30:00Z"
               },
               "query" : {
                   "apiToken" : "api_token_1",
                   "expirationDate" : "2023-10-26T10:30:00Z"
               },
               "region" : "us-east-1"
       }
      """;

  private static final EmbeddingWorkloadParamsView QUERY_PARAMS =
      new EmbeddingWorkloadParamsView("api_token_1", Instant.parse("2023-10-26T10:30:00Z"));

  private static final EmbeddingWorkloadParamsView CHANGE_STREAM_PARAMS =
      new EmbeddingWorkloadParamsView("api_token_1", Instant.parse("2023-10-26T10:30:00Z"));

  private static final EmbeddingWorkloadParamsView COLLECTION_SCAN_PARAMS =
      new EmbeddingWorkloadParamsView("api_token_1", Instant.parse("2023-10-26T10:30:00Z"));

  private static final EmbeddingProviderParamsView EXPECTED_PARAMS =
      new EmbeddingProviderParamsView(
          "us-east-1", QUERY_PARAMS, CHANGE_STREAM_PARAMS, COLLECTION_SCAN_PARAMS);

  private static final ObjectMapper MAPPER = CustomJacksonJsonProvider.createObjectMapper();

  @Test
  public void serializesToJson() throws JsonProcessingException {
    final String actualJsonString = MAPPER.writeValueAsString(EXPECTED_PARAMS);
    JSONAssert.assertEquals(EXPECTED_JSON_STRING, actualJsonString, JSONCompareMode.LENIENT);
  }

  @Test
  public void deserializesFromJson() throws JsonProcessingException {
    final EmbeddingProviderParamsView actualView =
        MAPPER.readValue(EXPECTED_JSON_STRING, EmbeddingProviderParamsView.class);
    assertEquals(EXPECTED_PARAMS, actualView);
  }
}
