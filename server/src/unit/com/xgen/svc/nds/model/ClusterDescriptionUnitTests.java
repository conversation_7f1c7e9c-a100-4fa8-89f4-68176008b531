package com.xgen.svc.nds.model;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.getNestedValue;
import static com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider.AWS;
import static com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider.AZURE;
import static com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider.FLEX;
import static com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider.GCP;
import static com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider.SERVERLESS;
import static com.xgen.cloud.nds.common._public.model.INDSDefaults.PAUSE_IDLE_M0_CLUSTER_EMAIL_WARNING_BUFFER;
import static com.xgen.cloud.nds.project._public.model.ClusterDescription.CLOUD_PROVIDER_DIFF;
import static com.xgen.cloud.nds.project._public.model.ClusterDescription.VALID_NAME_REGEX;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.CLUSTER_NAME_REGEX;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyDouble;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.amazonaws.services.ec2.model.VolumeType;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.model._public.access.EmployeeAccessGrantType;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.deployment._public.model.IndexConfig;
import com.xgen.cloud.deployment._public.model.diff.DeploymentItemParameterDiff;
import com.xgen.cloud.deployment._public.model.diff.ItemDiff;
import com.xgen.cloud.deployment._public.model.diff.ItemDiffs;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSAutoScaling;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSComputeAutoScaling;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureNDSDefaults;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.autoscaling.AzureAutoScaling;
import com.xgen.cloud.nds.azure._public.model.autoscaling.AzureComputeAutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.ClusterDescriptionProviderOptions;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceFamilyClass;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NetworkingPerformance;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.RollableValue;
import com.xgen.cloud.nds.cloudprovider._public.model.ShardRegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoIndexing;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleCrossCloudInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleInstanceSizeBuilder;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleInstanceSizeBuilderFactory;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaleSingleCloudInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.DiskGBAutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.xcloud.CrossCloudInstanceSize;
import com.xgen.cloud.nds.common._public.model.BiConnectorReadPreference;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.common._public.model.ExtendedDiskRestrictions;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.NDSLabel;
import com.xgen.cloud.nds.common._public.model.OS;
import com.xgen.cloud.nds.common._public.model.ProxyProtocolForPrivateLinkMode;
import com.xgen.cloud.nds.common._public.model.ReplicaSetScalingStrategy;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.flex._public.model.FlexHardwareSpec;
import com.xgen.cloud.nds.flex._public.model.FlexNDSDefaults;
import com.xgen.cloud.nds.flex._public.model.FlexTenantProviderOptions;
import com.xgen.cloud.nds.flex._public.model.autoscaling.FlexAutoScaling;
import com.xgen.cloud.nds.free._public.model.FreeHardwareSpec;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.free._public.model.autoscaling.FreeAutoScaling;
import com.xgen.cloud.nds.gcp._public.model.GCPHardwareSpec;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceFamily;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.autoscaling.GCPAutoScaling;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyStatus;
import com.xgen.cloud.nds.mongotune.policies.common._public.model.PolicyType;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyArgVersion;
import com.xgen.cloud.nds.mongotune.policies.writeblock._public.model.DiskWriteBlockPolicyStatus;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.BumperFileOverride;
import com.xgen.cloud.nds.project._public.model.ClusterConnectionStringConfiguration;
import com.xgen.cloud.nds.project._public.model.ClusterConnectionStringConfiguration.Status;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.BiConnector;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.BiConnector.Builder;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.EncryptionAtRestProvider;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.RootCertType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.SwapIpMaintenanceRound;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.ClusterDescriptionFactory;
import com.xgen.cloud.nds.project._public.model.EmployeeAccessGrant;
import com.xgen.cloud.nds.project._public.model.FlexTenantMigrationState;
import com.xgen.cloud.nds.project._public.model.GeoSharding;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus;
import com.xgen.cloud.nds.project._public.model.MongotuneStatus.State;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.RegionSpec;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.versions.FixedVersion;
import com.xgen.cloud.nds.project._public.model.versions.PhasedVersion;
import com.xgen.cloud.nds.serverless._public.model.ServerlessBackupOptions;
import com.xgen.cloud.nds.serverless._public.model.ServerlessHardwareSpec;
import com.xgen.cloud.nds.serverless._public.model.ServerlessNDSDefaults;
import com.xgen.cloud.nds.serverless._public.model.ServerlessTenantProviderOptions;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.ServerlessAutoScaling;
import com.xgen.svc.nds.free.FreeNDSDefaults;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFlexClusterDescriptionConfig;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestFreeClusterDescriptionConfig;
import com.xgen.svc.nds.model.NDSModelTestFactory.TestServerlessClusterDescriptionConfig;
import com.xgen.svc.nds.model.ui.GeoShardingView;
import com.xgen.testlib.junit5.extensions.cloudprovider.CloudProviderExtension;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentMatchers;

@ExtendWith(CloudProviderExtension.class)
public class ClusterDescriptionUnitTests {

  public enum DummyInstanceFamily implements InstanceFamily {
    M5(new Builder().displayName("m5")),
    R5(new Builder().displayName("r5"));

    private final String _displayName;

    DummyInstanceFamily(final Builder pBuilder) {
      _displayName = pBuilder.displayName;
    }

    public String getName() {
      return name();
    }

    public String getDisplayName() {
      return _displayName;
    }

    public CpuArchitecture getCpuArchitecture() {
      return CpuArchitecture.X86_64;
    }

    public Optional<OS> getOS() {
      return Optional.empty();
    }

    @Override
    public CloudProvider getCloudProvider() {
      return CloudProvider.NONE;
    }

    @Override
    public boolean isCpuBurstable() {
      return false;
    }

    public static class Builder {

      private String displayName;

      public Builder displayName(final String pDisplayName) {
        displayName = pDisplayName;
        return this;
      }
    }
  }

  public enum DummyInstanceSize implements NDSInstanceSize {
    P10(
        new Builder()
            .printableName("P10")
            .instanceFamilies(null)
            .availableFamilies(null)
            .ramSizeGB(Collections.emptyMap())
            .numCPUs(0.5)
            .maxAllowedDiskSizeGB(128)
            .storageOptionsGB(new int[] {10, 20, 40})
            .wiredTigerEngineConfigString("")
            .maxIncomingConnections(100)
            .electionTimeoutMillis(5000)
            .networkingPerformance(NetworkingPerformance.LOW)
            .isNVMe(false)
            .mongotMaxHeapSize("-Xmx1g")),
    P20(
        new Builder()
            .printableName("P20")
            .instanceFamilies(
                Map.of(DummyInstanceFamily.M5, "m5-medium", DummyInstanceFamily.R5, "r5-medium"))
            .availableFamilies(
                Map.of(
                    AWSRegionName.US_EAST_1,
                    Arrays.asList(DummyInstanceFamily.M5, DummyInstanceFamily.R5),
                    AWSRegionName.US_WEST_2,
                    Collections.singletonList(DummyInstanceFamily.M5),
                    AWSRegionName.EU_CENTRAL_1,
                    Arrays.asList(DummyInstanceFamily.M5, DummyInstanceFamily.R5),
                    AWSRegionName.AP_SOUTHEAST_2,
                    Collections.singletonList(DummyInstanceFamily.R5),
                    AWSRegionName.AP_SOUTH_1,
                    Collections.emptyList()))
            .ramSizeGB(Map.of(DummyInstanceFamily.M5, 4.0, DummyInstanceFamily.R5, 4.0))
            .numCPUs(1.0)
            .maxAllowedDiskSizeGB(256)
            .storageOptionsGB(new int[] {20, 40, 80})
            .wiredTigerEngineConfigString("")
            .maxIncomingConnections(200)
            .electionTimeoutMillis(10000)
            .networkingPerformance(NetworkingPerformance.LOW)
            .isNVMe(false)
            .mongotMaxHeapSize("-Xmx2g"));

    private final String _printableName;
    private final Map<InstanceFamily, String> _instanceFamilies;
    private final Map<RegionName, List<InstanceFamily>> _availableFamilies;
    private final Map<InstanceFamily, Double> _ramSizeGBInstanceFamilyMap;
    private final double _numCPUs;
    private final int _maxAllowedDiskSizeGB;
    private final int[] _storageOptionsGB;
    private final String _wiredTigerEngineConfigString;
    private final Double _wiredTigerEngineCacheSizeGB;
    private final Integer _maxIncomingConnections;
    private final Integer _electionTimeoutMillis;
    private final NetworkingPerformance _networkingPerformance;
    private final boolean _isNVMe;
    private final boolean _isComputeAutoScalingSupported;
    private final String _mongotMaxHeapSize;

    DummyInstanceSize(final Builder pBuilder) {
      _printableName = pBuilder.printableName;
      _instanceFamilies = pBuilder.instanceFamilies;
      _availableFamilies = pBuilder.availableFamilies;
      _ramSizeGBInstanceFamilyMap = pBuilder.ramSizeGBInstanceFamilyMap;
      _numCPUs = pBuilder.numCPUs;
      _maxAllowedDiskSizeGB = pBuilder.maxAllowedDiskSizeGB;
      _storageOptionsGB = pBuilder.storageOptionsGB;
      _wiredTigerEngineConfigString = pBuilder.wiredTigerEngineConfigString;
      _wiredTigerEngineCacheSizeGB = pBuilder.wiredTigerEngineCacheSizeGB;
      _maxIncomingConnections = pBuilder.maxIncomingConnections;
      _electionTimeoutMillis = pBuilder.electionTimeoutMillis;
      _networkingPerformance = pBuilder.networkingPerformance;
      _isNVMe = pBuilder.isNVMe;
      _isComputeAutoScalingSupported = pBuilder.isComputeAutoScalingSupported;
      _mongotMaxHeapSize = pBuilder.mongotMaxHeapSize;
    }

    public NDSInstanceSize[] getValues() {
      return values();
    }

    public int getOrdinal() {
      return ordinal();
    }

    public String getPrintableName() {
      return _printableName;
    }

    public Map<InstanceFamily, String> getInstanceFamilies() {
      return _instanceFamilies;
    }

    public Map<RegionName, List<InstanceFamily>> getAvailableFamilies() {
      return _availableFamilies;
    }

    @Override
    public CloudProvider getCloudProvider() {
      return CloudProvider.NONE;
    }

    @Override
    public Map<InstanceFamily, Double> getNumCPUInstanceFamilyMap() {
      return Map.of();
    }

    public double getNumCPUs(final InstanceFamily pInstanceFamily) {
      return 1;
    }

    @Override
    public double getRamSizeGBForLatestInstanceFamily() {
      return _ramSizeGBInstanceFamilyMap.values().stream().min(Double::compareTo).orElse(0.0);
    }

    @Override
    public Map<InstanceFamily, Double> getRamSizeGBInstanceFamilyMap() {
      return _ramSizeGBInstanceFamilyMap;
    }

    public double getNumCPUs() {
      return _numCPUs;
    }

    @Override
    public int getMaxCloudProviderDiskSizeGB(final boolean pIsAzureDiskV2) {
      return getMaxCloudProviderDiskSizeGB();
    }

    @Override
    public int getMaxCloudProviderDiskSizeGB() {
      return 80;
    }

    @Override
    public int getMaxAllowedDiskSizeGB(
        final boolean pIsAzureDiskV2, final boolean pExtendMaxAllowedDiskSize) {
      return getMaxAllowedDiskSizeGB(pExtendMaxAllowedDiskSize);
    }

    @Override
    public int getMaxAllowedDiskSizeGB(final boolean pExtendMaxAllowedDiskSizeGB) {
      return Math.min(_maxAllowedDiskSizeGB, getMaxCloudProviderDiskSizeGB());
    }

    public Optional<Integer> getMaxIncomingConnections() {
      return Optional.ofNullable(_maxIncomingConnections);
    }

    @Override
    public RollableValue<Integer> getMaxIndexBuildMemoryUsageMegabytes() {
      return RollableValue.everywhere(null);
    }

    @Override
    public RollableValue<Integer> getMaxNumActiveUserIndexBuilds() {
      return RollableValue.everywhere(null);
    }

    public Optional<Integer> getElectionTimeoutMillis() {
      return Optional.ofNullable(_electionTimeoutMillis);
    }

    public Optional<String> getWiredTigerEngineConfigString() {
      return Optional.ofNullable(_wiredTigerEngineConfigString);
    }

    public Optional<Double> getWiredTigerEngineCacheSizeGB() {
      return Optional.ofNullable(_wiredTigerEngineCacheSizeGB);
    }

    public int[] getStorageOptionsGB() {
      return _storageOptionsGB;
    }

    @Override
    public int getDefaultDiskSizeGB() {
      return 50;
    }

    public NetworkingPerformance getNetworkingPerformance() {
      return _networkingPerformance;
    }

    @Override
    public boolean isNVMe() {
      return false;
    }

    @Override
    public boolean isLowCPU() {
      return false;
    }

    @Override
    public Optional<InstanceSize> getHighCPUEquivalent() {
      return Optional.empty();
    }

    @Override
    public String getHypotheticalHighCPUEquivalentString() {
      return null;
    }

    @Override
    public boolean isComputeAutoScalingSupported() {
      return _isComputeAutoScalingSupported;
    }

    public String getMongotMaxHeapSize() {
      return _mongotMaxHeapSize;
    }

    @Override
    public float getMaxNormalizedCPUUsage(InstanceFamily pInstanceFamily) {
      return 1f;
    }

    @Override
    public Optional<DummyInstanceSize> getPreviousInstanceSize(
        final Predicate<NDSInstanceSize> pPredicate) {
      return getPreviousInstanceSize(ordinal(), values(), pPredicate)
          .map(DummyInstanceSize.class::cast);
    }

    @Override
    public Optional<DummyInstanceSize> getNextInstanceSize(
        final Predicate<NDSInstanceSize> pPredicate) {
      return getNextInstanceSize(ordinal(), values(), pPredicate)
          .map(DummyInstanceSize.class::cast);
    }

    @Override
    public Optional<? extends NDSInstanceSize> getNextInstanceSizeWithMaxInstanceSize(
        final NDSInstanceSize pMaxInstanceSize) {
      return Optional.empty();
    }

    @Override
    public int compareByTypes(final InstanceSize pOther) {
      if (!(pOther instanceof DummyInstanceSize)) {
        throw new IllegalArgumentException(
            String.format(
                "Expected pOther to be of type %s, but received %s",
                DummyInstanceSize.class.getName(), pOther.getClass().getName()));
      }

      return ordinal() - ((DummyInstanceSize) pOther).ordinal();
    }

    @Override
    public boolean shouldEnableSwap() {
      return true;
    }

    @Override
    public NDSInstanceFamilyClass getFamilyClass() {
      return null;
    }

    public boolean isDeprecated() {
      return false;
    }

    public static class Builder {

      private String printableName;
      private Map<InstanceFamily, String> instanceFamilies;
      private Map<RegionName, List<InstanceFamily>> availableFamilies;
      private Map<InstanceFamily, Double> ramSizeGBInstanceFamilyMap;
      private double numCPUs;
      private int maxAllowedDiskSizeGB;
      private int[] storageOptionsGB;
      private String wiredTigerEngineConfigString;
      private Double wiredTigerEngineCacheSizeGB;
      private Integer maxIncomingConnections;
      private Integer electionTimeoutMillis;
      private NetworkingPerformance networkingPerformance;
      private boolean isNVMe;
      private boolean isComputeAutoScalingSupported;
      private String mongotMaxHeapSize;

      public Builder printableName(final String pPrintableName) {
        printableName = pPrintableName;
        return this;
      }

      public Builder instanceFamilies(final Map<InstanceFamily, String> pInstanceFamilies) {
        instanceFamilies = pInstanceFamilies;
        return this;
      }

      public Builder availableFamilies(
          final Map<RegionName, List<InstanceFamily>> pAvailableFamilies) {
        availableFamilies = pAvailableFamilies;
        return this;
      }

      public Builder ramSizeGB(final Map<InstanceFamily, Double> pRamSizeGBInstanceFamilyMap) {
        ramSizeGBInstanceFamilyMap = pRamSizeGBInstanceFamilyMap;
        return this;
      }

      public Builder numCPUs(final double pNumCPUs) {
        numCPUs = pNumCPUs;
        return this;
      }

      public Builder maxAllowedDiskSizeGB(final int pMaxAllowedDiskSizeGB) {
        maxAllowedDiskSizeGB = pMaxAllowedDiskSizeGB;
        return this;
      }

      public Builder storageOptionsGB(final int[] pStorageOptionsGB) {
        storageOptionsGB = pStorageOptionsGB;
        return this;
      }

      public Builder wiredTigerEngineConfigString(final String pConfigString) {
        wiredTigerEngineConfigString = pConfigString;
        return this;
      }

      public Builder wiredTigerEngineCacheSizeGB(final Double pWiredTigerEngineCacheSizeGB) {
        wiredTigerEngineCacheSizeGB = pWiredTigerEngineCacheSizeGB;
        return this;
      }

      public Builder maxIncomingConnections(final Integer pMaxIncomingConnections) {
        maxIncomingConnections = pMaxIncomingConnections;
        return this;
      }

      public Builder electionTimeoutMillis(final Integer pElectionTimeoutMillis) {
        electionTimeoutMillis = pElectionTimeoutMillis;
        return this;
      }

      public Builder networkingPerformance(final NetworkingPerformance pNetworkingPerformance) {
        networkingPerformance = pNetworkingPerformance;
        return this;
      }

      public Builder isNVMe(final boolean pIsNVMe) {
        isNVMe = pIsNVMe;
        return this;
      }

      public Builder isComputeAutoScalingSupported(final boolean pIsComputeAutoScalingSupported) {
        isComputeAutoScalingSupported = pIsComputeAutoScalingSupported;
        return this;
      }

      public Builder mongotMaxHeapSize(final String pMaxHeapSize) {
        mongotMaxHeapSize = pMaxHeapSize;
        return this;
      }
    }
  }

  private final HardwareSpec _testHardwareSpec =
      NDSModelTestFactory.getAWSHardwareSpec(
          100, AWSNDSInstanceSize.M400_NVME, AWSRegionName.AP_SOUTHEAST_2);

  @Test
  public void testSetOSPolicyVersion() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();
    final String testOSPolicyVersion = "5.0";

    final ClusterDescription testCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "testCluster", CloudProvider.AWS)
            .copy()
            .setOSPolicyVersion(testOSPolicyVersion)
            .build();

    assertEquals(testOSPolicyVersion, testCluster.getOSPolicyVersion().orElse(""));
  }

  @Test
  public void testSetIsCriticalOSPolicyRelease() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription testCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "testCluster", CloudProvider.AWS)
            .copy()
            .setIsCriticalOSPolicyRelease(true)
            .build();

    assertEquals(true, testCluster.getIsCriticalOSPolicyRelease().orElse(false));
  }

  @Test
  public void testFlexMigration_notPresentSerializedAndDeserialized() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription testCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "testCluster", CloudProvider.FREE)
            .copy()
            .build();

    final ClusterDescription deserializedTestCluster =
        new ClusterDescription(testCluster.toDBObject());

    assertEquals(Optional.empty(), deserializedTestCluster.getFlexTenantMigrationState());
  }

  @Test
  public void testFlexMigration_presentSerializedAndDeserialized() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final FlexTenantMigrationState testMigrationState =
        FlexTenantMigrationState.builder()
            .setFormerCloudProvider(CloudProvider.FREE)
            .setFormerInstanceSize(FreeInstanceSize.M2)
            .setIsTenantCreatedFromApi(false)
            .build();
    final ClusterDescription testCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "testCluster", CloudProvider.FREE)
            .copy()
            .setFlexTenantMigrationState(testMigrationState)
            .build();

    final ClusterDescription deserializedTestCluster =
        new ClusterDescription(testCluster.toDBObject());

    assertEquals(
        testMigrationState, deserializedTestCluster.getFlexTenantMigrationState().orElseThrow());
  }

  @Test
  public void testFlexMigration_notPresent() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription testCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "testCluster", CloudProvider.FREE)
            .copy()
            .build();

    assertEquals(Optional.empty(), testCluster.getFlexTenantMigrationState());
  }

  @Test
  public void testFlexMigration_present() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final FlexTenantMigrationState testMigrationState =
        FlexTenantMigrationState.builder()
            .setIsTenantCreatedFromApi(true)
            .setTenantApiCloudProvider(CloudProvider.FREE)
            .setTenantApiInstanceSize(FreeInstanceSize.M2)
            .build();
    final ClusterDescription testCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "testCluster", CloudProvider.FREE)
            .copy()
            .setFlexTenantMigrationState(testMigrationState)
            .build();

    assertEquals(testMigrationState, testCluster.getFlexTenantMigrationState().orElseThrow());
  }

  @Test
  public void testOldFlexTenantMigrationStateFieldPresent() {
    final FlexTenantMigrationState testMigrationState =
        FlexTenantMigrationState.builder()
            .setIsTenantCreatedFromApi(true)
            .setTenantApiCloudProvider(CloudProvider.FREE)
            .setTenantApiInstanceSize(FreeInstanceSize.M2)
            .build();
    final ClusterDescription testCluster =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription()
                .append(
                    FieldDefs.OLD_FLEX_TENANT_MIGRATION_STATE, testMigrationState.toDBObject()));
    assertEquals(testMigrationState, testCluster.getFlexTenantMigrationState().orElseThrow());
  }

  @Test
  public void testAddPendingIndexes() {
    final List<IndexConfig> pendingIndexes =
        IntStream.range(0, 3)
            .mapToObj(
                i ->
                    new IndexConfig(
                        Collections.singletonList(
                            Arrays.asList(String.format("field-%d", i), "-1")),
                        null,
                        "rsFunFun",
                        "test",
                        "test",
                        null))
            .collect(Collectors.toList());

    // Add three distinct pending indexes to CD with no pending indexes: CD should have three
    // pending indexes
    final ClusterDescription clusterDescription =
        ClusterDescriptionFactory.get(NDSModelTestFactory.getAWSClusterDescription());
    final ClusterDescription.Builder<?, ClusterDescription> builderUniqueIndexes =
        clusterDescription.copy();
    builderUniqueIndexes.setName("TEST");
    builderUniqueIndexes.addPendingIndexes(pendingIndexes);
    final ClusterDescription cdWithUniqueIndexes = builderUniqueIndexes.build();
    assertEquals(pendingIndexes.size(), cdWithUniqueIndexes.getPendingIndexes().size());

    // Add the same three distinct pending indexes to CD with three pending indexes: CD should still
    // have three pending indexes
    final ClusterDescription.Builder<?, ClusterDescription> builderUniqueIndexesAgain =
        cdWithUniqueIndexes.copy();
    builderUniqueIndexesAgain.setName("TEST");
    builderUniqueIndexesAgain.addPendingIndexes(pendingIndexes);
    final ClusterDescription cdWithUniqueIndexesAgain = builderUniqueIndexesAgain.build();
    assertEquals(pendingIndexes.size(), cdWithUniqueIndexesAgain.getPendingIndexes().size());

    // Add six non-distinct (i.e. duplicate) pending indexes to CD with no pending indexes: CD
    // should have three pending indexes
    final List<IndexConfig> pendingDupeIndexes =
        Stream.concat(pendingIndexes.stream(), pendingIndexes.stream())
            .collect(Collectors.toList());
    final ClusterDescription.Builder<?, ClusterDescription> builderDupeIndexes =
        clusterDescription.copy();
    builderDupeIndexes.setName("TEST");
    builderDupeIndexes.addPendingIndexes(pendingDupeIndexes);
    final ClusterDescription cdWithDupeIndexes = builderDupeIndexes.build();
    assertEquals(pendingIndexes.size(), cdWithDupeIndexes.getPendingIndexes().size());
  }

  @Test
  public void testClusterDescriptionDiffs() {

    final ClusterDescription azureClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AZURE);

    final ItemDiff newDiff = azureClusterDescription.getNewClusterDiff();
    assertTrue(
        newDiff.getItems().stream().anyMatch(d -> d.getParamName().equals(FieldDefs.CLUSTER_TYPE)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.BACKUP_ENABLED)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.DISK_BACKUP_ENABLED)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.PIT_ENABLED)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.DISK_SIZE_GB)));
    assertEquals(
        7,
        newDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(RegionConfig.FieldDefs.AUTO_SCALING))
            .count());

    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.CLOUD_PROVIDER_DIFF)));
    assertEquals(
        "Azure",
        newDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(ClusterDescription.CLOUD_PROVIDER_DIFF))
            .findFirst()
            .get()
            .getNewValue());
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(
                d ->
                    d.getParamName()
                        .equals(ClusterDescription.FieldDefs.ENCRYPTION_AT_REST_PROVIDER)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.INSTANCE_SIZE)));
    assertTrue(
        newDiff.getItems().stream().anyMatch(d -> d.getParamName().equals(FieldDefs.LABELS)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(
                d ->
                    d.getParamName()
                        .equals(ClusterDescription.FieldDefs.TERMINATION_PROTECTION_ENABLED)));

    final ItemDiff deletedDiff = azureClusterDescription.getDeletedClusterDiff();
    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.CLUSTER_TYPE)));
    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.BACKUP_ENABLED)));
    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.DISK_BACKUP_ENABLED)));
    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.PIT_ENABLED)));
    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.DISK_SIZE_GB)));
    assertEquals(
        7,
        deletedDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(RegionConfig.FieldDefs.AUTO_SCALING))
            .count());

    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION)));
    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST)));
    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.CLOUD_PROVIDER_DIFF)));
    assertEquals(
        "Azure",
        deletedDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(ClusterDescription.CLOUD_PROVIDER_DIFF))
            .findFirst()
            .get()
            .getOldValue());
    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(
                d ->
                    d.getParamName()
                        .equals(ClusterDescription.FieldDefs.ENCRYPTION_AT_REST_PROVIDER)));
    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.INSTANCE_SIZE)));
    assertTrue(
        deletedDiff.getItems().stream()
            .noneMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.INSTANCE_FAMILY)));
    assertTrue(
        deletedDiff.getItems().stream().anyMatch(d -> d.getParamName().equals(FieldDefs.LABELS)));
    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(
                d ->
                    d.getParamName()
                        .equals(ClusterDescription.FieldDefs.TERMINATION_PROTECTION_ENABLED)));

    final ItemDiff deletedClusterInternalFieldsDiff =
        azureClusterDescription.getDeletedClusterInternalFieldsDiff();

    assertTrue(
        deletedClusterInternalFieldsDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.INSTANCE_FAMILY)));
    final AzureComputeAutoScaling newComputeAutoScaling =
        new AzureComputeAutoScaling(
            AzureNDSInstanceSize.M20, AzureNDSInstanceSize.M200, true, true, true);
    final AutoScaling newAutoScaling =
        new AzureAutoScaling(
            newComputeAutoScaling, new DiskGBAutoScaling(true), new AutoIndexing(true));

    final List<RegionConfig> newRegionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                    AzureRegionName.AUSTRALIA_EAST, RegionConfig.MAX_PRIORITY, 3, 1, 1, 0)
                .copy()
                .setBaseAutoScaling(newAutoScaling)
                .updateAllHardware(
                    new AzureHardwareSpec.Builder()
                        .setInstanceFamilyAndOS(
                            AzureInstanceFamily.STANDARD_DSV5,
                            NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                                CloudProvider.AZURE, AzureInstanceFamily.STANDARD_DSV5))
                        .setInstanceSize(AzureNDSInstanceSize.M200))
                .build(),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                    AzureRegionName.AUSTRALIA_SOUTH_EAST, RegionConfig.MAX_PRIORITY - 1, 0, 2, 2, 0)
                .copy()
                .setBaseAutoScaling(newAutoScaling)
                .updateAllHardware(
                    new AzureHardwareSpec.Builder()
                        .setInstanceFamilyAndOS(
                            AzureInstanceFamily.STANDARD_DSV5,
                            NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                                CloudProvider.AZURE, AzureInstanceFamily.STANDARD_DSV5))
                        .setInstanceSize(AzureNDSInstanceSize.M200))
                .build());
    final ReplicationSpec newReplicationSpec =
        new ReplicationSpec(
            new ObjectId(),
            new ObjectId(),
            new ObjectId(),
            NDSDefaults.ZONE_NAME,
            azureClusterDescription.getNumShards() + 1,
            newRegionConfigs);

    final GeoSharding newGeoSharding =
        new GeoSharding(
            new BasicDBObject()
                .append(GeoSharding.FieldDefs.MANAGED_NAMESPACES, new BasicDBList())
                .append(
                    GeoSharding.FieldDefs.CUSTOM_ZONE_MAPPING,
                    new BasicDBObject("fruit", newReplicationSpec.getId())));
    final ClusterDescription.BiConnector newBiConnector =
        new ClusterDescription.BiConnector(true, BiConnectorReadPreference.PRIMARY);

    final List<NDSLabel> ndsLabels = Collections.singletonList(new NDSLabel("new", "label"));
    final ClusterDescription updatedAzureClusterDescription =
        new ShardedClusterDescription.Builder(azureClusterDescription.toDBObject())
            .setClusterType(ClusterDescription.ClusterType.SHARDED)
            .setEmbeddedConfigServerReplicationSpec(newReplicationSpec, false)
            .setBackupEnabled(!azureClusterDescription.isBackupEnabled())
            .setDiskBackupEnabled(!azureClusterDescription.isDiskBackupEnabled())
            .setPitEnabled(!azureClusterDescription.isPitEnabled())
            .setDiskSizeGB(AzureDiskType.P30.getSizeGB())
            .setMongoDBMajorVersion(
                PhasedVersion.VALID_MONGODB_MAJOR_VERSIONS.keySet().stream()
                    .filter(v -> !v.equals(azureClusterDescription.getMongoDBMajorVersion()))
                    .findFirst()
                    .get())
            .setReplicationSpecList(Collections.singletonList(newReplicationSpec))
            .setEncryptionAtRestProvider(EncryptionAtRestProvider.AWS)
            .setIsPaused(true)
            .setBiConnector(newBiConnector)
            .setGeoSharding(newGeoSharding)
            .setLabels(ndsLabels)
            .setTerminationProtectionEnabled(true)
            .setNeedsSampleDataLoadAfter(null)
            .build();
    final ItemDiff updatedDiff =
        updatedAzureClusterDescription.getUpdatedClusterDiff(azureClusterDescription);
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.CLUSTER_TYPE)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.BACKUP_ENABLED)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.DISK_BACKUP_ENABLED)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.PIT_ENABLED)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.DISK_SIZE_GB)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(
                d ->
                    d.getParamName()
                        .equals(ClusterDescription.FieldDefs.ENCRYPTION_AT_REST_PROVIDER)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.IS_PAUSED)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.GEO_SHARDING)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.BI_CONNECTOR)));

    final List<DeploymentItemParameterDiff> updatedAutoScalingDiffs =
        updatedDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(RegionConfig.FieldDefs.AUTO_SCALING))
            .toList();
    assertEquals(7, updatedAutoScalingDiffs.size());

    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.INSTANCE_SIZE)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.LABELS)));

    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(
                d ->
                    d.getParamName()
                        .equals(ClusterDescription.FieldDefs.TERMINATION_PROTECTION_ENABLED)));

    assertFalse(
        updatedDiff.getItems().stream()
            .anyMatch(
                d ->
                    d.getParamName()
                        .equals(ClusterDescription.FieldDefs.NEEDS_SAMPLE_DATA_LOAD_AFTER)));

    final DeploymentItemParameterDiff replicationSpecDiff =
        updatedDiff.getItems().stream()
            .filter(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST))
            .findFirst()
            .get();

    assertEquals(
        replicationSpecDiff.getOldValue(),
        azureClusterDescription.getReplicationSpecsWithShardData().toString());
    assertEquals(
        replicationSpecDiff.getNewValue(),
        updatedAzureClusterDescription.getReplicationSpecsWithShardData().toString());

    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(RegionConfig.FieldDefs.AUTO_SCALING)));

    final ItemDiff equalDiff =
        azureClusterDescription.getUpdatedClusterDiff(azureClusterDescription.copy().build());
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.CLUSTER_TYPE)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.BACKUP_ENABLED)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.DISK_BACKUP_ENABLED)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.PIT_ENABLED)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.DISK_SIZE_GB)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(
                d -> d.getParamName().equals(ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(
                d ->
                    d.getParamName()
                        .equals(ClusterDescription.FieldDefs.ENCRYPTION_AT_REST_PROVIDER)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.IS_PAUSED)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.GEO_SHARDING)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.BI_CONNECTOR)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(RegionConfig.FieldDefs.AUTO_SCALING)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(ClusterDescription.FieldDefs.LABELS)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(
                d ->
                    d.getParamName()
                        .equals(ClusterDescription.FieldDefs.TERMINATION_PROTECTION_ENABLED)));
    assertFalse(
        equalDiff.getItems().stream()
            .anyMatch(
                d ->
                    d.getParamName()
                        .equals(ClusterDescription.FieldDefs.NEEDS_SAMPLE_DATA_LOAD_AFTER)));
  }

  @Test
  public void testGetBaseInstanceSize() {
    final ShardRegionConfig baseRegion =
        NDSModelTestFactory.getShardRegionConfigForRegion(AWSRegionName.US_EAST_1, 7, 1, 1, 0, 0);
    final HardwareSpec m10HardwareSpec =
        NDSModelTestFactory.getAWSHardwareSpec(3, AWSNDSInstanceSize.M10, AWSRegionName.US_EAST_1);
    final HardwareSpec m20HardwareSpec =
        NDSModelTestFactory.getAWSHardwareSpec(3, AWSNDSInstanceSize.M20, AWSRegionName.US_EAST_1);

    // symmetric hardware
    {
      final RegionConfig region =
          baseRegion
              .copy()
              .setAnalyticsSpecs(m10HardwareSpec)
              .setElectableSpecs(m10HardwareSpec)
              .build();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getDefaultClusterDescription(
              new ObjectId(), "Cluster0", List.of(region));

      assertEquals(
          Set.of(AWSNDSInstanceSize.M10), clusterDescription.getInstanceSizes(NodeType.ANALYTICS));
      assertEquals(
          Set.of(AWSNDSInstanceSize.M10), clusterDescription.getInstanceSizes(NodeType.ELECTABLE));
    }

    // asymmetric hardware
    {
      final RegionConfig region =
          baseRegion
              .copy()
              .setAnalyticsSpecs(m10HardwareSpec)
              .setElectableSpecs(m20HardwareSpec)
              .build();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getDefaultClusterDescription(
              new ObjectId(), "Cluster0", List.of(region));

      assertEquals(
          Set.of(AWSNDSInstanceSize.M10), clusterDescription.getInstanceSizes(NodeType.ANALYTICS));
      assertEquals(
          Set.of(AWSNDSInstanceSize.M20), clusterDescription.getInstanceSizes(NodeType.ELECTABLE));
    }
  }

  @Test
  public void testGetAnalyticsInstanceSize() {
    final ShardRegionConfig defaultRegion =
        NDSModelTestFactory.getShardRegionConfigForRegion(AWSRegionName.US_EAST_1, 7, 1, 1, 0, 0);
    final HardwareSpec m10HardwareSpec =
        NDSModelTestFactory.getAWSHardwareSpec(3, AWSNDSInstanceSize.M10, AWSRegionName.US_EAST_1);
    final HardwareSpec m20HardwareSpec =
        NDSModelTestFactory.getAWSHardwareSpec(3, AWSNDSInstanceSize.M20, AWSRegionName.US_EAST_1);

    // symmetric hardware
    {
      final RegionConfig region =
          defaultRegion
              .copy()
              .setAnalyticsSpecs(m10HardwareSpec)
              .setElectableSpecs(m10HardwareSpec)
              .build();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getDefaultClusterDescription(
              new ObjectId(), "Cluster0", List.of(region));

      assertEquals(
          Set.of(AWSNDSInstanceSize.M10), clusterDescription.getInstanceSizes(NodeType.ANALYTICS));
      assertEquals(
          Set.of(AWSNDSInstanceSize.M10), clusterDescription.getInstanceSizes(NodeType.ELECTABLE));
    }

    // asymmetric hardware
    {
      final RegionConfig region =
          defaultRegion
              .copy()
              .setAnalyticsSpecs(m20HardwareSpec)
              .setElectableSpecs(m10HardwareSpec)
              .build();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getDefaultClusterDescription(
              new ObjectId(), "Cluster0", List.of(region));

      assertEquals(
          Set.of(AWSNDSInstanceSize.M20), clusterDescription.getInstanceSizes(NodeType.ANALYTICS));
      assertEquals(
          Set.of(AWSNDSInstanceSize.M10), clusterDescription.getInstanceSizes(NodeType.ELECTABLE));
    }
  }

  @Test
  public void testHasAsymmetricHardware() {
    final ShardRegionConfig defaultRegion =
        NDSModelTestFactory.getShardRegionConfigForRegion(AWSRegionName.US_EAST_1, 7, 1, 1, 0, 0);
    final ShardRegionConfig tenantRegion =
        NDSModelTestFactory.getShardRegionConfigForFreeRegion(AWSRegionName.US_EAST_1, 7, 1, 1, 0);
    final HardwareSpec m10HardwareSpec =
        NDSModelTestFactory.getAWSHardwareSpec(3, AWSNDSInstanceSize.M10, AWSRegionName.US_EAST_1);
    final HardwareSpec m20HardwareSpec =
        NDSModelTestFactory.getAWSHardwareSpec(3, AWSNDSInstanceSize.M20, AWSRegionName.US_EAST_1);
    final HardwareSpec tenantHardwareSpec =
        NDSModelTestFactory.getTenantHardwareSpec(3, AWSRegionName.US_EAST_1);

    // symmetric hardware
    {
      final RegionConfig region =
          defaultRegion
              .copy()
              .setAnalyticsSpecs(m10HardwareSpec)
              .setElectableSpecs(m10HardwareSpec)
              .build();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getDefaultClusterDescription(
              new ObjectId(), "Cluster0", List.of(region));

      assertFalse(clusterDescription.hasAsymmetricAnalyticsSize());
    }

    // asymmetric hardware - analytics > base instance size
    {
      final RegionConfig region =
          defaultRegion
              .copy()
              .setAnalyticsSpecs(m20HardwareSpec)
              .setElectableSpecs(m10HardwareSpec)
              .build();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getDefaultClusterDescription(
              new ObjectId(), "Cluster0", List.of(region));

      assertTrue(clusterDescription.hasAsymmetricAnalyticsSize());
    }

    // asymmetric hardware - analytics < base instance size
    {
      final RegionConfig region =
          defaultRegion
              .copy()
              .setAnalyticsSpecs(m10HardwareSpec)
              .setElectableSpecs(m20HardwareSpec)
              .build();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getDefaultClusterDescription(
              new ObjectId(), "Cluster0", List.of(region));

      assertTrue(clusterDescription.hasAsymmetricAnalyticsSize());
    }

    // symmetric tenant hardware
    {
      final RegionConfig region =
          tenantRegion
              .copy()
              .setAnalyticsSpecs(tenantHardwareSpec)
              .setElectableSpecs(tenantHardwareSpec)
              .build();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getDefaultClusterDescription(
              new ObjectId(), "Cluster0", List.of(region));

      assertFalse(clusterDescription.hasAsymmetricAnalyticsSize());
    }
  }

  @Test
  public void testHasAsymmetricInstanceClass() {
    final ShardRegionConfig defaultRegion =
        NDSModelTestFactory.getShardRegionConfigForRegion(AWSRegionName.US_EAST_1, 7, 1, 1, 0, 0);
    final HardwareSpec m10HardwareSpec =
        NDSModelTestFactory.getAWSHardwareSpec(3, AWSNDSInstanceSize.M10, AWSRegionName.US_EAST_1);
    final HardwareSpec r50HardwareSpec =
        NDSModelTestFactory.getAWSHardwareSpec(3, AWSNDSInstanceSize.R50, AWSRegionName.US_EAST_1);

    // symmetric instance classes
    {
      final RegionConfig region =
          defaultRegion
              .copy()
              .setAnalyticsSpecs(m10HardwareSpec)
              .setElectableSpecs(m10HardwareSpec)
              .build();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getDefaultClusterDescription(
              new ObjectId(), "Cluster0", List.of(region));

      assertFalse(clusterDescription.hasAsymmetricAnalyticsInstanceFamilyClass());
    }

    // asymmetric instance classes
    {
      final RegionConfig region =
          defaultRegion
              .copy()
              .setAnalyticsSpecs(r50HardwareSpec)
              .setElectableSpecs(m10HardwareSpec)
              .build();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getDefaultClusterDescription(
              new ObjectId(), "Cluster0", List.of(region));

      assertTrue(clusterDescription.hasAsymmetricAnalyticsInstanceFamilyClass());
    }

    // asymmetric instance classes
    {
      final RegionConfig region =
          defaultRegion
              .copy()
              .setAnalyticsSpecs(m10HardwareSpec)
              .setElectableSpecs(r50HardwareSpec)
              .build();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getDefaultClusterDescription(
              new ObjectId(), "Cluster0", List.of(region));

      assertTrue(clusterDescription.hasAsymmetricAnalyticsInstanceFamilyClass());
    }
  }

  @Test
  public void testDiffs_analyticsNodes() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ClusterDescription analyticsClusterDescription =
        NDSModelTestFactory.changeSingleZoneClusterAnalyticsNodes(
            clusterDescription, 1, AWSNDSInstanceSize.R40, AWSInstanceFamily.R6G);

    // test new diff without analytics nodes
    final ItemDiff newDiffNoAnalyticsNodes = clusterDescription.getNewClusterDiff();
    final DeploymentItemParameterDiff baseSizeDiff_new_noAnalyticsNodes =
        newDiffNoAnalyticsNodes.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.INSTANCE_SIZE))
            .findFirst()
            .get();
    final Optional<DeploymentItemParameterDiff> analyticsSizeDiff_new_noAnalyticsNodes =
        newDiffNoAnalyticsNodes.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.ANALYTICS_INSTANCE_SIZE))
            .findFirst();

    assertTrue(analyticsSizeDiff_new_noAnalyticsNodes.isEmpty());
    assertEquals(AWSNDSInstanceSize.M30, baseSizeDiff_new_noAnalyticsNodes.getNewValue());

    // test new diff with analytics nodes
    final ItemDiff newDiff = analyticsClusterDescription.getNewClusterDiff();
    final DeploymentItemParameterDiff baseSizeDiff_new =
        newDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.INSTANCE_SIZE))
            .findFirst()
            .get();
    final DeploymentItemParameterDiff analyticsSizeDiff_new =
        newDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.ANALYTICS_INSTANCE_SIZE))
            .findFirst()
            .get();

    assertEquals(AWSNDSInstanceSize.R40, analyticsSizeDiff_new.getNewValue());
    assertEquals(AWSNDSInstanceSize.M30, baseSizeDiff_new.getNewValue());

    // test deleted diff
    final ItemDiff deletedDiff = analyticsClusterDescription.getDeletedClusterDiff();
    final DeploymentItemParameterDiff baseSizeDiff_deleted =
        deletedDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.INSTANCE_SIZE))
            .findFirst()
            .get();
    final DeploymentItemParameterDiff analyticsSizeDiff_deleted =
        deletedDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.ANALYTICS_INSTANCE_SIZE))
            .findFirst()
            .get();

    // instance family and analytics instance family are not shown in deletedDiff since they
    // are shown to internal users only
    assertTrue(
        deletedDiff.getItems().stream()
            .noneMatch(d -> d.getParamName().equals(FieldDefs.INSTANCE_FAMILY)));
    assertTrue(
        deletedDiff.getItems().stream()
            .noneMatch(d -> d.getParamName().equals(FieldDefs.ANALYTICS_INSTANCE_FAMILY)));

    final ItemDiff deletedClusterInternalFieldsDiff =
        analyticsClusterDescription.getDeletedClusterInternalFieldsDiff();

    final DeploymentItemParameterDiff baseFamilyDiff_deleted =
        deletedClusterInternalFieldsDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.INSTANCE_FAMILY))
            .findFirst()
            .get();
    final DeploymentItemParameterDiff analyticsFamilyDiff_deleted =
        deletedClusterInternalFieldsDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.ANALYTICS_INSTANCE_FAMILY))
            .findFirst()
            .get();

    assertEquals(AWSNDSInstanceSize.R40, analyticsSizeDiff_deleted.getOldValue());
    assertEquals(AWSNDSInstanceSize.M30, baseSizeDiff_deleted.getOldValue());
    assertEquals(AWSInstanceFamily.R6G, analyticsFamilyDiff_deleted.getOldValue());
    assertEquals(AWSInstanceFamily.M6G, baseFamilyDiff_deleted.getOldValue());

    // test updated diff with analytics nodes
    final ClusterDescription analyticsClusterDescriptionTwoNodes =
        NDSModelTestFactory.changeSingleZoneClusterAnalyticsNodes(
            analyticsClusterDescription, 2, AWSNDSInstanceSize.M30, AWSInstanceFamily.M6G);
    final ItemDiff updateDiff =
        analyticsClusterDescriptionTwoNodes.getUpdatedClusterDiff(analyticsClusterDescription);
    final DeploymentItemParameterDiff analyticsSizeDiff_updated =
        updateDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.ANALYTICS_INSTANCE_SIZE))
            .findFirst()
            .get();

    // analytics instance family is not shown in updateDiff since it is shown to internal users only
    assertTrue(
        updateDiff.getItems().stream()
            .noneMatch(d -> d.getParamName().equals(FieldDefs.ANALYTICS_INSTANCE_FAMILY)));

    final ItemDiff updatedClusterInternalFieldsDiff =
        analyticsClusterDescriptionTwoNodes.getUpdatedClusterInternalFieldsDiff(
            analyticsClusterDescription);
    final DeploymentItemParameterDiff analyticsFamilyDiff_updated =
        updatedClusterInternalFieldsDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.ANALYTICS_INSTANCE_FAMILY))
            .findFirst()
            .get();

    assertEquals(AWSNDSInstanceSize.M30, analyticsSizeDiff_updated.getNewValue());
    assertEquals(AWSInstanceFamily.M6G, analyticsFamilyDiff_updated.getNewValue());

    // base size and family not updated
    assertTrue(
        updateDiff.getItems().stream()
            .noneMatch(d -> d.getParamName().equals(FieldDefs.INSTANCE_SIZE)));
    assertTrue(
        updatedClusterInternalFieldsDiff.getItems().stream()
            .noneMatch(d -> d.getParamName().equals(FieldDefs.INSTANCE_FAMILY)));

    // test updated diff with no analytics nodes
    final ClusterDescription clusterDescriptionNewBaseInstanceSize =
        clusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M10),
                NodeTypeFamily.BASE)
            .build();
    final ItemDiff updateDiffNoAnalyticsNodes =
        clusterDescription.getUpdatedClusterDiff(clusterDescriptionNewBaseInstanceSize);
    final Optional<DeploymentItemParameterDiff> analyticsSizeDiffNoNodes_updated =
        updateDiffNoAnalyticsNodes.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.ANALYTICS_INSTANCE_SIZE))
            .findFirst();

    // analytics instance family is not shown in updateDiffNoAnalyticsNodes since it is shown to
    // internal users only
    assertTrue(
        updateDiffNoAnalyticsNodes.getItems().stream()
            .noneMatch(d -> d.getParamName().equals(FieldDefs.ANALYTICS_INSTANCE_FAMILY)));

    final ItemDiff updatedClusterInternalFieldsDiffNoAnalyticsNodes =
        clusterDescription.getUpdatedClusterInternalFieldsDiff(
            clusterDescriptionNewBaseInstanceSize);

    final Optional<DeploymentItemParameterDiff> analyticsFamilyDiffNoNodes_updated =
        updatedClusterInternalFieldsDiffNoAnalyticsNodes.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.ANALYTICS_INSTANCE_FAMILY))
            .findFirst();

    assertTrue(analyticsSizeDiffNoNodes_updated.isEmpty());
    assertTrue(analyticsFamilyDiffNoNodes_updated.isEmpty());
  }

  @Test
  public void testNewDiffs_analyticsAutoScaling() {
    testAnalyticsAutoScalingDiffs("NEW");
  }

  @Test
  public void testUpdatedDiffs_analyticsAutoScaling() {
    testAnalyticsAutoScalingDiffs("UPDATE");
  }

  @Test
  public void testDeletedDiffs_analyticsAutoScaling() {
    testAnalyticsAutoScalingDiffs("DELETE");
  }

  private void testAnalyticsAutoScalingDiffs(final String pAutoScalingDiffType) {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ClusterDescription clusterDescription_withAnalyticsNodes =
        NDSModelTestFactory.changeSingleZoneClusterAnalyticsNodes(
            clusterDescription,
            1,
            clusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow(),
            clusterDescription.getInstanceFamily(NodeType.ANALYTICS));
    final AutoScaling analyticsAutoScaling =
        new AWSAutoScaling(
            new AWSComputeAutoScaling(
                AWSNDSInstanceSize.M10, AWSNDSInstanceSize.M50, true, true, false),
            new DiskGBAutoScaling(false),
            new AutoIndexing(false));
    final ClusterDescription clusterDescription_withAnalyticsAutoScaling =
        clusterDescription_withAnalyticsNodes
            .copy()
            .setAutoScalingForProvider(
                CloudProvider.AWS, analyticsAutoScaling, NodeTypeFamily.ANALYTICS)
            .build();
    final AutoScaling updatedAnalyticsAutoScaling =
        new AWSAutoScaling(
            new AWSComputeAutoScaling(null, null, false, false, false),
            new DiskGBAutoScaling(true),
            new AutoIndexing(true));
    final ClusterDescription clusterDescription_withUpdatedAnalyticsAutoScaling =
        clusterDescription_withAnalyticsNodes
            .copy()
            .setAutoScalingForProvider(
                CloudProvider.AWS, updatedAnalyticsAutoScaling, NodeTypeFamily.ANALYTICS)
            .build();

    final boolean isNewClusterDiff = pAutoScalingDiffType.equals("NEW");
    final boolean isDeleteClusterDiff = pAutoScalingDiffType.equals("DELETE");
    final boolean isUpdateClusterDiff = pAutoScalingDiffType.equals("UPDATE");
    final ItemDiff clusterDiff;
    if (isNewClusterDiff) {
      clusterDiff = clusterDescription_withAnalyticsAutoScaling.getNewClusterDiff();
    } else if (isDeleteClusterDiff) {
      clusterDiff = clusterDescription_withAnalyticsAutoScaling.getDeletedClusterDiff();
    } else if (isUpdateClusterDiff) {
      clusterDiff =
          clusterDescription_withUpdatedAnalyticsAutoScaling.getUpdatedClusterDiff(
              clusterDescription_withAnalyticsAutoScaling);
    } else {
      throw new IllegalStateException("Provided invalid diff type.");
    }

    final Optional<DeploymentItemParameterDiff> analyticsAutoScaling_DiskAutoScalingDiff =
        clusterDiff.getItems().stream()
            .filter(
                d ->
                    d.getParamName().equals(RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING)
                        && d.getDisplayName().equals("Disk Auto Scaling Enabled"))
            .findAny();
    final Optional<DeploymentItemParameterDiff> analyticsAutoScaling_AutoIndexingDiff =
        clusterDiff.getItems().stream()
            .filter(
                d ->
                    d.getParamName().equals(RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING)
                        && d.getDisplayName().equals("Auto Indexing Enabled"))
            .findAny();
    final Optional<DeploymentItemParameterDiff> analyticsAutoScaling_ComputeEnabledDiff =
        clusterDiff.getItems().stream()
            .filter(
                d ->
                    d.getParamName().equals(RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING)
                        && d.getDisplayName().equals("Analytics Compute Auto Scaling Enabled"))
            .findAny();
    final Optional<DeploymentItemParameterDiff> analyticsAutoScaling_ScaleDownDiff =
        clusterDiff.getItems().stream()
            .filter(
                d ->
                    d.getParamName().equals(RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING)
                        && d.getDisplayName()
                            .equals("Analytics Compute Auto Scaling Scale Down Enabled"))
            .findAny();
    final Optional<DeploymentItemParameterDiff> analyticsAutoScaling_MinInstanceDiff =
        clusterDiff.getItems().stream()
            .filter(
                d ->
                    d.getParamName().equals(RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING)
                        && d.getDisplayName()
                            .equals("Analytics Compute Auto Scaling Min Instance Size"))
            .findAny();
    final Optional<DeploymentItemParameterDiff> analyticsAutoScaling_MaxInstanceDiff =
        clusterDiff.getItems().stream()
            .filter(
                d ->
                    d.getParamName().equals(RegionConfig.FieldDefs.ANALYTICS_AUTO_SCALING)
                        && d.getDisplayName()
                            .equals("Analytics Compute Auto Scaling Max Instance Size"))
            .findAny();

    if (isNewClusterDiff) {
      assertTrue(analyticsAutoScaling_ComputeEnabledDiff.isPresent());
      assertTrue(analyticsAutoScaling_ScaleDownDiff.isPresent());
      assertTrue(analyticsAutoScaling_MinInstanceDiff.isPresent());
      assertTrue(analyticsAutoScaling_MaxInstanceDiff.isPresent());

      assertTrue((boolean) analyticsAutoScaling_ComputeEnabledDiff.get().getNewValue());
      assertTrue((boolean) analyticsAutoScaling_ScaleDownDiff.get().getNewValue());
      assertEquals(
          AWSNDSInstanceSize.M10, analyticsAutoScaling_MinInstanceDiff.get().getNewValue());
      assertEquals(
          AWSNDSInstanceSize.M50, analyticsAutoScaling_MaxInstanceDiff.get().getNewValue());
    } else if (isDeleteClusterDiff) {
      assertTrue(analyticsAutoScaling_ComputeEnabledDiff.isPresent());
      assertTrue(analyticsAutoScaling_ScaleDownDiff.isPresent());
      assertTrue(analyticsAutoScaling_MinInstanceDiff.isPresent());
      assertTrue(analyticsAutoScaling_MaxInstanceDiff.isPresent());

      assertTrue((boolean) analyticsAutoScaling_ComputeEnabledDiff.get().getOldValue());
      assertTrue((boolean) analyticsAutoScaling_ScaleDownDiff.get().getOldValue());
      assertEquals(
          AWSNDSInstanceSize.M10, analyticsAutoScaling_MinInstanceDiff.get().getOldValue());
      assertEquals(
          AWSNDSInstanceSize.M50, analyticsAutoScaling_MaxInstanceDiff.get().getOldValue());
    } else if (isUpdateClusterDiff) {
      assertTrue(analyticsAutoScaling_ComputeEnabledDiff.isPresent());
      assertTrue(analyticsAutoScaling_ScaleDownDiff.isPresent());
      assertTrue(analyticsAutoScaling_MinInstanceDiff.isPresent());
      assertTrue(analyticsAutoScaling_MaxInstanceDiff.isPresent());

      assertTrue((boolean) analyticsAutoScaling_ComputeEnabledDiff.get().getOldValue());
      assertTrue((boolean) analyticsAutoScaling_ScaleDownDiff.get().getOldValue());
      assertEquals(
          AWSNDSInstanceSize.M10, analyticsAutoScaling_MinInstanceDiff.get().getOldValue());
      assertEquals(
          AWSNDSInstanceSize.M50, analyticsAutoScaling_MaxInstanceDiff.get().getOldValue());

      assertFalse((boolean) analyticsAutoScaling_ComputeEnabledDiff.get().getNewValue());
      assertFalse((boolean) analyticsAutoScaling_ScaleDownDiff.get().getNewValue());
      assertNull(analyticsAutoScaling_MinInstanceDiff.get().getNewValue());
      assertNull(analyticsAutoScaling_MaxInstanceDiff.get().getNewValue());
    }
  }

  @Test
  public void testDiffs_awsOptions() {
    final BasicDBObject descDoc = NDSModelTestFactory.getAWSClusterDescription();

    final BasicDBObject regionConfig =
        (BasicDBObject)
            ((BasicDBList)
                    ((BasicDBObject)
                            ((BasicDBList) descDoc.get(FieldDefs.REPLICATION_SPEC_LIST)).get(0))
                        .get(ReplicationSpec.FieldDefs.REGION_CONFIGS))
                .get(0);

    ((BasicDBObject) regionConfig.get(RegionConfig.FieldDefs.ELECTABLE_SPECS))
        .put(AWSHardwareSpec.FieldDefs.ENCRYPT_EBS_VOLUME, false);

    ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.READ_ONLY_SPECS))
        .put(AWSHardwareSpec.FieldDefs.ENCRYPT_EBS_VOLUME, false);

    ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.ANALYTICS_SPECS))
        .put(AWSHardwareSpec.FieldDefs.ENCRYPT_EBS_VOLUME, false);

    ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.HIDDEN_SECONDARY_SPECS))
        .put(AWSHardwareSpec.FieldDefs.ENCRYPT_EBS_VOLUME, false);

    final ClusterDescription clusterDescription = new ClusterDescription(descDoc);

    final ItemDiff newDiff = clusterDescription.getNewClusterDiff();

    assertTrue(
        newDiff.getItems().stream().anyMatch(d -> d.getParamName().equals(FieldDefs.DISK_IOPS)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.ENCRYPT_EBS_VOLUME)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.EBS_VOLUME_TYPE)));

    final ItemDiff deletedDiff = clusterDescription.getDeletedClusterDiff();

    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.DISK_IOPS)));
    assertTrue(
        deletedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.ENCRYPT_EBS_VOLUME)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.EBS_VOLUME_TYPE)));

    final HardwareSpec pHardwareSpec =
        ((AWSHardwareSpec)
                clusterDescription
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()
                    .get(0)
                    .getElectableSpecs())
            .copy()
            .setDiskIOPS(1000)
            .setEncryptEBSVolume(true)
            .build();
    final ClusterDescription newClusterDescription =
        clusterDescription
            .copy()
            .setReplicationSpecList(
                clusterDescription.getReplicationSpecsWithShardData().stream()
                    .map(
                        replicationSpec ->
                            replicationSpec.updateExistingHardwareSpecsWithProviderOptions(
                                pHardwareSpec))
                    .collect(Collectors.toList()))
            .build();

    final ItemDiff updatedDiff = clusterDescription.getUpdatedClusterDiff(newClusterDescription);

    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.DISK_IOPS)));
    assertTrue(
        updatedDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.ENCRYPT_EBS_VOLUME)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.EBS_VOLUME_TYPE)));

    // moving to aws from another cloud provider
    final ClusterDescription azureClusterDescription =
        clusterDescription
            .copy()
            .setReplicationSpecList(
                List.of(
                    NDSModelTestFactory.getAzureReplicationSpec(
                        new ObjectId(),
                        NDSDefaults.ZONE_NAME,
                        1,
                        3,
                        List.of(AzureRegionName.ASIA_EAST))))
            .build();

    final ItemDiff acrossCloudProviderDiff =
        clusterDescription.getUpdatedClusterDiff(azureClusterDescription);

    final Optional<DeploymentItemParameterDiff> diskIOPS =
        acrossCloudProviderDiff.getItems().stream()
            .filter(d -> d.getParamName().equals(FieldDefs.DISK_IOPS))
            .findFirst();
    assertTrue(diskIOPS.isPresent());
    assertEquals(120, diskIOPS.get().getOldValue());
    assertEquals(3000, diskIOPS.get().getNewValue());

    assertTrue(
        acrossCloudProviderDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.ENCRYPT_EBS_VOLUME)));
    assertTrue(
        newDiff.getItems().stream()
            .anyMatch(d -> d.getParamName().equals(FieldDefs.EBS_VOLUME_TYPE)));
  }

  @Test
  @Deprecated
  public void testPerformDiskAutoScale() {
    // test fail -- tenant cluster
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn(true).when(clusterDescription).isTenantCluster();
    doCallRealMethod()
        .when(clusterDescription)
        .performDiskAutoScale(anyDouble(), any(ExtendedDiskRestrictions.class));

    try {
      clusterDescription.performDiskAutoScale(.2d, ExtendedDiskRestrictions.DISALLOWED);
      fail();
    } catch (final Exception pE) {
      assertTrue(pE instanceof UnsupportedOperationException);
    }
  }

  @Test
  public void testDiskAutoScaledResult_IsPositiveResult() {
    Arrays.stream(ClusterDescription.DiskAutoScaleResult.values())
        .forEach(
            r -> {
              if (Set.of(
                      ClusterDescription.DiskAutoScaleResult.DISK_SIZE_INCREASED,
                      ClusterDescription.DiskAutoScaleResult.INSTANCE_SIZE_INCREASED)
                  .contains(r)) {
                assertTrue(r.isPositiveScalingResult());
              } else {
                assertFalse(r.isPositiveScalingResult());
              }
            });
  }

  @Test
  public void testGetDiskSizeForDiskAutoScaling_AzurePv2() {
    final ClusterDescription clusterDescription =
        ClusterDescription.getCloudProviderClusterDescription(
                NDSModelTestFactory.getAzureClusterDescription())
            .copy()
            .setDiskSizeGB(128)
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(
                new AzureHardwareSpec.Builder()
                    .setInstanceSize(AzureNDSInstanceSize.M20)
                    .setDiskType(AzureDiskType.V2))
            .build();
    assertEquals(160D, clusterDescription.getDiskSizeForDiskAutoScaling(.875D, .7D), 0.1D);
  }

  @Test
  public void testGetDiskSizeForDiskAutoScaling_NegativeDiskSize() {
    // test negative disk size -- fail
    try {
      final ClusterDescription clusterDescription = mock(ClusterDescription.class);
      doCallRealMethod()
          .when(clusterDescription)
          .getDiskSizeForDiskAutoScaling(anyDouble(), anyDouble());
      doReturn(100D).when(clusterDescription).getDiskSizeGB();

      clusterDescription.getDiskSizeForDiskAutoScaling(-.8D, .6D);

      fail();
    } catch (final Exception pE) {
      assertTrue(pE instanceof IllegalStateException);
      assertTrue(pE.getMessage().startsWith("Invalid target disk size"));
    }
  }

  @Test
  public void testPerformDiskAutoScaleWithInstanceSizeUpdate_TenantClusters() {
    // test fail -- tenant cluster
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn(true).when(clusterDescription).isTenantCluster();
    doCallRealMethod()
        .when(clusterDescription)
        .performDiskAutoScaleWithInstanceSizeUpdate(
            anyDouble(), anyDouble(), any(), any(ExtendedDiskRestrictions.class), any());

    try {
      clusterDescription.performDiskAutoScaleWithInstanceSizeUpdate(
          .8d,
          .75d,
          new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M100),
          ExtendedDiskRestrictions.DISALLOWED,
          NodeType.ELECTABLE);
      fail();
    } catch (final Exception pE) {
      assertTrue(pE instanceof UnsupportedOperationException);
    }
  }

  @Test
  public void testPerformComputeAutoScaleForCluster() throws Exception {

    // test fail -- tenant cluster
    final ClusterDescription tenantClusterDescription = mock(ClusterDescription.class);
    doCallRealMethod()
        .when(tenantClusterDescription)
        .performComputeAutoScaleForCluster(any(), any());
    doReturn(true).when(tenantClusterDescription).isTenantCluster();

    try {
      tenantClusterDescription.performComputeAutoScaleForCluster(
          new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M20), NodeTypeFamily.BASE);
      fail();
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.AUTO_SCALING_NOT_SUPPORTED, pE.getErrorCode());
    }

    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAzureClusterDescription());

    final HardwareSpec baseHardwareSpec =
        clusterDescription
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs();

    final AutoScaling baseAutoScaling =
        AzureAutoScaling.getDefaultAutoScaling().toBuilder()
            .autoIndexing(new AutoIndexing(true))
            .build();

    // test fail -- wrong type
    try {
      final Optional<ClusterDescription> scaledClusterDescription =
          clusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M20), NodeTypeFamily.BASE);
      fail();
    } catch (final Exception pE) {
      assertTrue(pE instanceof IllegalArgumentException);
    }

    // test fail -- equal instance sizes
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          clusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  clusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow()),
              NodeTypeFamily.BASE);
      assertTrue(scaledClusterDescription.isEmpty());
    }

    // test success -- scale up
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          clusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  clusterDescription
                      .getOnlyInstanceSize(NodeType.ELECTABLE)
                      .orElseThrow()
                      .getNextInstanceSize()
                      .orElseThrow()),
              NodeTypeFamily.BASE);
      assertEquals(
          Set.of(
              clusterDescription
                  .getOnlyInstanceSize(NodeType.ELECTABLE)
                  .orElseThrow()
                  .getNextInstanceSize()
                  .orElseThrow()),
          scaledClusterDescription.orElseThrow().getInstanceSizes(NodeType.ELECTABLE));
    }

    // test success -- scale down
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          clusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  clusterDescription
                      .getOnlyInstanceSize(NodeType.ELECTABLE)
                      .orElseThrow()
                      .getPreviousInstanceSize()
                      .orElseThrow()),
              NodeTypeFamily.BASE);
      assertEquals(
          Set.of(
              clusterDescription
                  .getOnlyInstanceSize(NodeType.ELECTABLE)
                  .orElseThrow()
                  .getPreviousInstanceSize()
                  .orElseThrow()),
          scaledClusterDescription.orElseThrow().getInstanceSizes(NodeType.ELECTABLE));
    }

    // test success -- scale up above MAX_AUTO_INDEXING_INSTANCE_SIZE with auto-indexing enabled
    {
      final HardwareSpec cdHardwareSpec =
          baseHardwareSpec.copy().setInstanceSize(AzureNDSInstanceSize.M30).build();

      final ClusterDescription cdUnderTest =
          clusterDescription
              .copy()
              .setReplicationSpecList(
                  clusterDescription.getReplicationSpecsWithShardData().stream()
                      .map(
                          replicationSpec ->
                              replicationSpec
                                  .updateExistingHardwareSpecsAndAutoScalingWithProviderOptions(
                                      cdHardwareSpec, baseAutoScaling))
                      .collect(Collectors.toList()))
              .build();

      final Optional<ClusterDescription> scaledClusterDescription =
          cdUnderTest.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  cdUnderTest
                      .getOnlyInstanceSize(NodeType.ELECTABLE)
                      .orElseThrow()
                      .getNextInstanceSize()
                      .orElseThrow()),
              NodeTypeFamily.BASE);
      assertEquals(
          Set.of(
              cdUnderTest
                  .getOnlyInstanceSize(NodeType.ELECTABLE)
                  .orElseThrow()
                  .getNextInstanceSize()
                  .orElseThrow()),
          scaledClusterDescription.orElseThrow().getInstanceSizes(NodeType.ELECTABLE));
      assertFalse(
          scaledClusterDescription
              .get()
              .getAutoScaling(NodeTypeFamily.BASE)
              .getAutoIndexing()
              .isEnabled());
    }

    // test success -- scale up above MAX_AUTO_INDEXING_INSTANCE_SIZE with auto-indexing disabled
    {
      final HardwareSpec cdHardwareSpec =
          baseHardwareSpec.copy().setInstanceSize(AzureNDSInstanceSize.M30).build();

      final AutoScaling pAutoScaling = AzureAutoScaling.getDefaultAutoScaling();
      final ClusterDescription cdUnderTest =
          clusterDescription
              .copy()
              .setReplicationSpecList(
                  clusterDescription.getReplicationSpecsWithShardData().stream()
                      .map(
                          replicationSpec ->
                              replicationSpec
                                  .updateExistingHardwareSpecsAndAutoScalingWithProviderOptions(
                                      cdHardwareSpec, pAutoScaling))
                      .collect(Collectors.toList()))
              .build();

      final Optional<ClusterDescription> scaledClusterDescription =
          cdUnderTest.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  cdUnderTest
                      .getOnlyInstanceSize(NodeType.ELECTABLE)
                      .orElseThrow()
                      .getNextInstanceSize()
                      .orElseThrow()),
              NodeTypeFamily.BASE);
      assertEquals(
          Set.of(
              cdUnderTest
                  .getOnlyInstanceSize(NodeType.ELECTABLE)
                  .orElseThrow()
                  .getNextInstanceSize()
                  .orElseThrow()),
          scaledClusterDescription.orElseThrow().getInstanceSizes(NodeType.ELECTABLE));
      assertFalse(
          scaledClusterDescription
              .get()
              .getAutoScaling(NodeTypeFamily.BASE)
              .getAutoIndexing()
              .isEnabled());
    }

    // test success -- scale up below MAX_AUTO_INDEXING_INSTANCE_SIZE with auto-indexing enabled
    {
      final HardwareSpec cdHardwareSpec =
          baseHardwareSpec.copy().setInstanceSize(AzureNDSInstanceSize.M10).build();

      final ClusterDescription cdUnderTest =
          clusterDescription
              .copy()
              .setReplicationSpecList(
                  clusterDescription.getReplicationSpecsWithShardData().stream()
                      .map(
                          replicationSpec ->
                              replicationSpec
                                  .updateExistingHardwareSpecsAndAutoScalingWithProviderOptions(
                                      cdHardwareSpec, baseAutoScaling))
                      .collect(Collectors.toList()))
              .build();

      final Optional<ClusterDescription> scaledClusterDescription =
          cdUnderTest.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  cdUnderTest
                      .getOnlyInstanceSize(NodeType.ELECTABLE)
                      .orElseThrow()
                      .getNextInstanceSize()
                      .orElseThrow()),
              NodeTypeFamily.BASE);
      assertEquals(
          Set.of(
              cdUnderTest
                  .getOnlyInstanceSize(NodeType.ELECTABLE)
                  .orElseThrow()
                  .getNextInstanceSize()
                  .orElseThrow()),
          scaledClusterDescription.orElseThrow().getInstanceSizes(NodeType.ELECTABLE));
      assertTrue(
          scaledClusterDescription
              .get()
              .getAutoScaling(NodeTypeFamily.BASE)
              .getAutoIndexing()
              .isEnabled());
    }

    // test scale up still even if auto-indexing is null
    {
      final HardwareSpec cdHardwareSpec =
          baseHardwareSpec.copy().setInstanceSize(AzureNDSInstanceSize.M10).build();

      final AutoScaling autoScaleNullAutoIndexing =
          baseAutoScaling.toBuilder().autoIndexing(null).build();

      List<ReplicationSpec> result;
      final HardwareSpec pHardwareSpec = cdHardwareSpec;
      final AutoScaling pAutoScaling = autoScaleNullAutoIndexing;
      result =
          clusterDescription.getReplicationSpecsWithShardData().stream()
              .map(
                  replicationSpec ->
                      replicationSpec.updateExistingHardwareSpecsAndAutoScalingWithProviderOptions(
                          pHardwareSpec, pAutoScaling))
              .collect(Collectors.toList());
      final ClusterDescription cdUnderTest =
          clusterDescription.copy().setReplicationSpecList(result).build();

      final Optional<ClusterDescription> scaledClusterDescription =
          cdUnderTest.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  cdUnderTest
                      .getOnlyInstanceSize(NodeType.ELECTABLE)
                      .orElseThrow()
                      .getNextInstanceSize()
                      .orElseThrow()),
              NodeTypeFamily.BASE);
      assertEquals(
          Set.of(
              cdUnderTest
                  .getOnlyInstanceSize(NodeType.ELECTABLE)
                  .orElseThrow()
                  .getNextInstanceSize()
                  .orElseThrow()),
          scaledClusterDescription.orElseThrow().getInstanceSizes(NodeType.ELECTABLE));
      assertNull(
          scaledClusterDescription.get().getAutoScaling(NodeTypeFamily.BASE).getAutoIndexing());
    }

    // AWS test fail -- non-matching instance family
    final ClusterDescription awsClusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());

    try {
      final Optional<ClusterDescription> scaledClusterDescription =
          awsClusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.R40), NodeTypeFamily.BASE);
      fail();
    } catch (final Exception pE) {
      assertTrue(pE instanceof IllegalArgumentException);
      assertTrue(pE.getMessage().contains("family class"));
    }

    final ClusterDescription asymmetricClusterDescription =
        clusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(
                new AzureHardwareSpec.Builder()
                    .setNodeCount(1)
                    .setInstanceSize(AzureNDSInstanceSize.M50),
                NodeTypeFamily.ANALYTICS)
            .build();

    // test success -- scale up NodeTypeFamily=ANALYTICS
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          asymmetricClusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  asymmetricClusterDescription
                      .getOnlyInstanceSize(NodeType.ANALYTICS)
                      .orElseThrow()
                      .getNextInstanceSize()
                      .orElseThrow()),
              NodeTypeFamily.ANALYTICS);
      assertEquals(
          Set.of(
              asymmetricClusterDescription
                  .getOnlyInstanceSize(NodeType.ANALYTICS)
                  .orElseThrow()
                  .getNextInstanceSize()
                  .orElseThrow()),
          scaledClusterDescription.orElseThrow().getInstanceSizes(NodeType.ANALYTICS));
      assertNotEquals(
          Set.of(
              asymmetricClusterDescription
                  .getOnlyInstanceSize(NodeType.ANALYTICS)
                  .orElseThrow()
                  .getNextInstanceSize()
                  .orElseThrow()),
          scaledClusterDescription.get().getInstanceSizes(NodeType.ELECTABLE));
    }

    // test success -- scale down NodeTypeFamily=ANALYTICS
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          asymmetricClusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleSingleCloudInstanceSize(
                  asymmetricClusterDescription
                      .getOnlyInstanceSize(NodeType.ANALYTICS)
                      .orElseThrow()
                      .getPreviousInstanceSize()
                      .orElseThrow()),
              NodeTypeFamily.ANALYTICS);
      assertEquals(
          Set.of(
              asymmetricClusterDescription
                  .getOnlyInstanceSize(NodeType.ANALYTICS)
                  .orElseThrow()
                  .getPreviousInstanceSize()
                  .orElseThrow()),
          scaledClusterDescription.orElseThrow().getInstanceSizes(NodeType.ANALYTICS));
      assertNotEquals(
          Set.of(
              asymmetricClusterDescription
                  .getOnlyInstanceSize(NodeType.ANALYTICS)
                  .orElseThrow()
                  .getPreviousInstanceSize()
                  .orElseThrow()),
          scaledClusterDescription.get().getInstanceSizes(NodeType.ELECTABLE));
    }
  }

  @Test
  public void testPerformComputeAutoScaleForClusterCrossCloud() throws Exception {
    // Test multiple orders to ensure there are no lingering single-cloud getters
    // (e.g. getInstanceSize()) that result in different behavior for different orders.
    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AWS, AWSRegionName.US_EAST_1, 7, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AZURE, AzureRegionName.US_EAST_2, 6, 1, 0, 0, 0));
    final List<RegionConfig> regionConfigsReversed =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AZURE, AzureRegionName.US_EAST_2, 7, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AWS, AWSRegionName.US_EAST_1, 6, 1, 0, 0, 0));

    testPerformComputeAutoScaleForClusterCrossCloud(regionConfigs);
    testPerformComputeAutoScaleForClusterCrossCloud(regionConfigsReversed);
  }

  private void testPerformComputeAutoScaleForClusterCrossCloud(
      final List<RegionConfig> pRegionConfigs) throws Exception {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AZURE, "foo", pRegionConfigs);

    // test fail -- equal instance sizes
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          clusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleCrossCloudInstanceSize(
                  clusterDescription.getInstanceSizes(NodeType.ELECTABLE).iterator().next()),
              NodeTypeFamily.BASE);
      assertTrue(scaledClusterDescription.isEmpty());
    }

    // test success -- scale up
    {
      final Optional<ClusterDescription> scaledClusterDescription =
          clusterDescription.performComputeAutoScaleForCluster(
              new AutoScaleCrossCloudInstanceSize(
                  clusterDescription
                      .getInstanceSizes(NodeType.ELECTABLE)
                      .iterator()
                      .next()
                      .getNextInstanceSize()
                      .orElseThrow()),
              NodeTypeFamily.BASE);
      assertEquals(
          clusterDescription.getInstanceSizes(NodeType.ELECTABLE).stream()
              .map(i -> i.getNextInstanceSize().orElseThrow())
              .collect(Collectors.toSet()),
          scaledClusterDescription.orElseThrow().getInstanceSizes(NodeType.ELECTABLE));
    }

    try {
      clusterDescription.performComputeAutoScaleForCluster(
          new AutoScaleCrossCloudInstanceSize(AWSNDSInstanceSize.R40), NodeTypeFamily.BASE);
      fail();
    } catch (final Exception pE) {
      assertTrue(pE instanceof IllegalArgumentException);
      assertTrue(pE.getMessage().contains("family class"));
    }
  }

  @Test
  public void testGetReplicationSpecList() {
    final ClusterDescription awsCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final List<ReplicationSpec> awsReplicationSpecList =
        awsCluster.getReplicationSpecsWithShardData();
    assertEquals(NDSDefaults.ZONE_NAME, awsReplicationSpecList.get(0).getZoneName());
    assertEquals(1, (int) awsReplicationSpecList.get(0).getNumShards());
    final ShardRegionConfig awsRegionConfig =
        (ShardRegionConfig) awsReplicationSpecList.get(0).getRegionConfigs().get(0);
    assertEquals(AWSNDSDefaults.REGION_NAME, awsRegionConfig.getRegionName());
    assertEquals(CloudProvider.AWS, awsRegionConfig.getCloudProvider());
    assertEquals(
        NDSModelTestFactory.getDefaultAutoScaling(),
        awsRegionConfig.getBaseAutoScaling().toDBObject());
    assertEquals(RegionSpec.MAX_PRIORITY, awsRegionConfig.getPriority());
    final AWSHardwareSpec awsElectableSpec = (AWSHardwareSpec) awsRegionConfig.getElectableSpecs();
    assertEquals(3, awsElectableSpec.getNodeCount());
    assertEquals(AWSNDSInstanceSize.M30, awsElectableSpec.getInstanceSize());
    assertEquals(AWSInstanceFamily.M6G, awsElectableSpec.getInstanceFamily());
    assertEquals(3000, awsElectableSpec.getDiskIOPS());
    assertEquals(VolumeType.Gp3, awsElectableSpec.getEBSVolumeType());
    assertTrue(awsElectableSpec.getEncryptEBSVolume());
    final AWSHardwareSpec awsAnalyticsSpec = (AWSHardwareSpec) awsRegionConfig.getAnalyticsSpecs();
    assertEquals(0, awsAnalyticsSpec.getNodeCount());
    final AWSHardwareSpec awsReadOnlySpec = (AWSHardwareSpec) awsRegionConfig.getReadOnlySpecs();
    assertEquals(0, awsReadOnlySpec.getNodeCount());
    final AWSHardwareSpec awsHiddenSecondarySpec =
        (AWSHardwareSpec) awsRegionConfig.getHiddenSecondarySpecs();
    assertEquals(0, awsHiddenSecondarySpec.getNodeCount());

    final ClusterDescription azureCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AZURE);
    final List<ReplicationSpec> azureReplicationSpecList =
        azureCluster.getReplicationSpecsWithShardData();
    assertEquals(NDSDefaults.ZONE_NAME, azureReplicationSpecList.get(0).getZoneName());
    assertEquals(1, (int) azureReplicationSpecList.get(0).getNumShards());
    final ShardRegionConfig azureRegionConfig =
        (ShardRegionConfig) azureReplicationSpecList.get(0).getRegionConfigs().get(0);
    assertEquals(AzureNDSDefaults.REGION_NAME, azureRegionConfig.getRegionName());
    assertEquals(CloudProvider.AZURE, azureRegionConfig.getCloudProvider());
    assertEquals(
        NDSModelTestFactory.getDefaultAutoScaling(),
        azureRegionConfig.getBaseAutoScaling().toDBObject());
    assertEquals(RegionSpec.MAX_PRIORITY, azureRegionConfig.getPriority());
    final AzureHardwareSpec azureElectableSpec =
        (AzureHardwareSpec) azureRegionConfig.getElectableSpecs();
    assertEquals(3, azureElectableSpec.getNodeCount());
    assertEquals(AzureNDSInstanceSize.M30, azureElectableSpec.getInstanceSize());
    assertEquals(AzureInstanceFamily.STANDARD_DSV5, azureElectableSpec.getInstanceFamily());
    assertEquals(AzureDiskType.P4, azureElectableSpec.getDiskType());
    final AzureHardwareSpec azureAnalyticsSpec =
        (AzureHardwareSpec) azureRegionConfig.getAnalyticsSpecs();
    assertEquals(0, azureAnalyticsSpec.getNodeCount());
    final AzureHardwareSpec azureReadOnlySpec =
        (AzureHardwareSpec) azureRegionConfig.getReadOnlySpecs();
    assertEquals(0, azureReadOnlySpec.getNodeCount());
    final AzureHardwareSpec azureHiddenSecondarySpec =
        (AzureHardwareSpec) azureRegionConfig.getHiddenSecondarySpecs();
    assertEquals(0, azureHiddenSecondarySpec.getNodeCount());

    final ClusterDescription gcpCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.GCP);
    final List<ReplicationSpec> gcpReplicationSpecList =
        gcpCluster.getReplicationSpecsWithShardData();
    assertEquals(NDSDefaults.ZONE_NAME, gcpReplicationSpecList.get(0).getZoneName());
    assertEquals(1, (int) gcpReplicationSpecList.get(0).getNumShards());
    final ShardRegionConfig gcpRegionConfig =
        (ShardRegionConfig) gcpReplicationSpecList.get(0).getRegionConfigs().get(0);
    assertEquals(GCPNDSDefaults.REGION_NAME, gcpRegionConfig.getRegionName());
    assertEquals(CloudProvider.GCP, gcpRegionConfig.getCloudProvider());
    assertEquals(
        NDSModelTestFactory.getDefaultAutoScaling(),
        gcpRegionConfig.getBaseAutoScaling().toDBObject());
    assertEquals(RegionSpec.MAX_PRIORITY, gcpRegionConfig.getPriority());
    final GCPHardwareSpec gcpElectableSpec = (GCPHardwareSpec) gcpRegionConfig.getElectableSpecs();
    assertEquals(3, gcpElectableSpec.getNodeCount());
    assertEquals(GCPNDSInstanceSize.M30, gcpElectableSpec.getInstanceSize());
    assertEquals(GCPInstanceFamily.N2, gcpElectableSpec.getInstanceFamily());
    final GCPHardwareSpec gcpAnalyticsSpec = (GCPHardwareSpec) gcpRegionConfig.getAnalyticsSpecs();
    assertEquals(0, gcpAnalyticsSpec.getNodeCount());
    final GCPHardwareSpec gcpReadOnlySpec = (GCPHardwareSpec) gcpRegionConfig.getReadOnlySpecs();
    assertEquals(0, gcpReadOnlySpec.getNodeCount());
    final GCPHardwareSpec gcpHiddenSecondarySpec =
        (GCPHardwareSpec) gcpRegionConfig.getHiddenSecondarySpecs();
    assertEquals(0, gcpHiddenSecondarySpec.getNodeCount());

    final ClusterDescription freeCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.FREE);
    final List<ReplicationSpec> freeReplicationSpecList =
        freeCluster.getReplicationSpecsWithShardData();
    assertEquals(NDSDefaults.ZONE_NAME, freeReplicationSpecList.get(0).getZoneName());
    assertEquals(1, (int) freeReplicationSpecList.get(0).getNumShards());
    final ShardRegionConfig freeRegionConfig =
        (ShardRegionConfig) freeReplicationSpecList.get(0).getRegionConfigs().get(0);
    assertEquals(AWSRegionName.US_EAST_1, freeRegionConfig.getRegionName());
    assertEquals(CloudProvider.FREE, freeRegionConfig.getCloudProvider());
    assertEquals(
        NDSModelTestFactory.getDefaultAutoScaling(),
        freeRegionConfig.getBaseAutoScaling().toDBObject());
    assertEquals(RegionSpec.MAX_PRIORITY, freeRegionConfig.getPriority());

    final FreeHardwareSpec freeElectableSpec =
        (FreeHardwareSpec) freeRegionConfig.getElectableSpecs();
    final FreeTenantProviderOptions freeOptions =
        (FreeTenantProviderOptions) freeCluster.getFreeTenantProviderOptions();

    assertEquals(3, freeElectableSpec.getNodeCount());
    assertEquals(FreeNDSDefaults.INSTANCE_SIZE, freeElectableSpec.getInstanceSize());
    assertEquals(CloudProvider.AWS, freeElectableSpec.getBackingProvider());
    assertEquals(FreeInstanceSize.LimitsProfile.NORMAL, freeOptions.getLimitsProfile());
    assertFalse(freeOptions.isTenantBackupEnabled());
    final FreeHardwareSpec freeAnalyticsSpec =
        (FreeHardwareSpec) freeRegionConfig.getAnalyticsSpecs();
    assertEquals(0, freeAnalyticsSpec.getNodeCount());
    final FreeHardwareSpec freeReadOnlySpec =
        (FreeHardwareSpec) freeRegionConfig.getReadOnlySpecs();
    assertEquals(0, freeReadOnlySpec.getNodeCount());
    final FreeHardwareSpec freeHiddenSecondarySpec =
        (FreeHardwareSpec) freeRegionConfig.getHiddenSecondarySpecs();
    assertEquals(0, freeHiddenSecondarySpec.getNodeCount());

    final ClusterDescription serverlessCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.SERVERLESS);
    final List<ReplicationSpec> serverlessReplicationSpecList =
        serverlessCluster.getReplicationSpecsWithShardData();
    assertEquals(NDSDefaults.ZONE_NAME, serverlessReplicationSpecList.get(0).getZoneName());
    assertEquals(1, (int) serverlessReplicationSpecList.get(0).getNumShards());
    final ShardRegionConfig serverlessRegionConfig =
        (ShardRegionConfig) serverlessReplicationSpecList.get(0).getRegionConfigs().get(0);
    assertEquals(AWSRegionName.US_EAST_1, serverlessRegionConfig.getRegionName());
    assertEquals(CloudProvider.SERVERLESS, serverlessRegionConfig.getCloudProvider());
    assertEquals(
        NDSModelTestFactory.getDefaultAutoScaling(),
        serverlessRegionConfig.getBaseAutoScaling().toDBObject());
    assertEquals(RegionSpec.MAX_PRIORITY, serverlessRegionConfig.getPriority());
    final ServerlessHardwareSpec serverlessElectableSpec =
        (ServerlessHardwareSpec) serverlessRegionConfig.getElectableSpecs();
    assertEquals(3, serverlessElectableSpec.getNodeCount());
    assertEquals(ServerlessNDSDefaults.INSTANCE_SIZE, serverlessElectableSpec.getInstanceSize());
    assertEquals(CloudProvider.AWS, serverlessElectableSpec.getBackingProvider());
    final ServerlessTenantProviderOptions serverlessTenantProviderOptions =
        (ServerlessTenantProviderOptions) serverlessCluster.getServerlessTenantProviderOptions();
    assertEquals(
        ServerlessNDSDefaults.INSTANCE_SIZE.getMaxAllowedDiskSizeGBForServerless(),
        serverlessTenantProviderOptions.getDiskSizeGBLimit());
    final ServerlessHardwareSpec serverlessAnalyticsSpec =
        (ServerlessHardwareSpec) serverlessRegionConfig.getAnalyticsSpecs();
    assertEquals(0, serverlessAnalyticsSpec.getNodeCount());
    final ServerlessHardwareSpec serverlessReadOnlySpec =
        (ServerlessHardwareSpec) serverlessRegionConfig.getReadOnlySpecs();
    assertEquals(0, serverlessReadOnlySpec.getNodeCount());
    final ServerlessHardwareSpec serverlessHiddenSecondarySpec =
        (ServerlessHardwareSpec) serverlessRegionConfig.getHiddenSecondarySpecs();
    assertEquals(0, serverlessHiddenSecondarySpec.getNodeCount());

    final ClusterDescription flexCluster = NDSModelTestFactory.getClusterDescription(FLEX);
    final List<ReplicationSpec> flexReplicationSpecList =
        flexCluster.getReplicationSpecsWithShardData();
    assertNotNull(flexReplicationSpecList.get(0).getId());
    assertNotNull(flexReplicationSpecList.get(0).getExternalId());
    assertNotNull(flexReplicationSpecList.get(0).getZoneId());
    assertEquals(NDSDefaults.ZONE_NAME, flexReplicationSpecList.get(0).getZoneName());
    assertEquals(1, (int) flexReplicationSpecList.get(0).getNumShards());
    final ShardRegionConfig flexRegionConfig =
        (ShardRegionConfig) flexReplicationSpecList.get(0).getRegionConfigs().get(0);
    assertEquals(AWSRegionName.US_EAST_1, flexRegionConfig.getRegionName());
    assertEquals(FLEX, flexRegionConfig.getCloudProvider());
    assertEquals(
        NDSModelTestFactory.getDefaultAutoScaling(),
        flexRegionConfig.getBaseAutoScaling().toDBObject());
    assertEquals(RegionSpec.MAX_PRIORITY, flexRegionConfig.getPriority());
    final FlexHardwareSpec flexElectableSpec =
        (FlexHardwareSpec) flexRegionConfig.getElectableSpecs();
    assertEquals(3, flexElectableSpec.getNodeCount());
    assertEquals(FlexNDSDefaults.INSTANCE_SIZE, flexElectableSpec.getInstanceSize());
    assertEquals(CloudProvider.AWS, flexElectableSpec.getBackingProvider());
    final FlexTenantProviderOptions flexTenantProviderOptions =
        (FlexTenantProviderOptions) flexCluster.getFlexTenantProviderOptions();
    assertEquals(
        FlexNDSDefaults.INSTANCE_SIZE.getMaxCollections(),
        flexTenantProviderOptions.getCollectionLimit());
    assertEquals(
        FlexNDSDefaults.INSTANCE_SIZE.getMaxIncomingConnections().orElseThrow(),
        flexTenantProviderOptions.getConnectionLimit());
    assertEquals(
        FlexNDSDefaults.INSTANCE_SIZE.getMaxDatabases(),
        flexTenantProviderOptions.getDatabaseLimit());
    assertNotNull(flexTenantProviderOptions.getNextBackupDate());
    assertEquals(
        FlexNDSDefaults.INSTANCE_SIZE.getMaxOperationsPerSecond(),
        flexTenantProviderOptions.getOperationsPerSecondLimit());
    assertEquals(
        FlexNDSDefaults.INSTANCE_SIZE.getMaxAllowedDiskSizeGBForFlex(),
        flexTenantProviderOptions.getDiskSizeGBLimit());
    final FlexHardwareSpec flexAnalyticsSpec =
        (FlexHardwareSpec) flexRegionConfig.getAnalyticsSpecs();
    assertEquals(0, flexAnalyticsSpec.getNodeCount());
    final FlexHardwareSpec flexReadOnlySpec =
        (FlexHardwareSpec) flexRegionConfig.getReadOnlySpecs();
    assertEquals(0, flexReadOnlySpec.getNodeCount());
    final FlexHardwareSpec flexHiddenSecondarySpec =
        (FlexHardwareSpec) flexRegionConfig.getHiddenSecondarySpecs();
    assertEquals(0, flexHiddenSecondarySpec.getNodeCount());
  }

  @Test
  public void testIsGlobalCluster() {
    final ReplicationSpec rs1 =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1));
    final ReplicationSpec rs2 =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), "Zone 2", 1, 3, List.of(AWSRegionName.US_EAST_2));
    final ReplicationSpec rs3 =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), "Zone 2", 1, 3, List.of(AWSRegionName.US_EAST_2));

    // Single replication spec
    {
      final ClusterDescription cd =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setReplicationSpecList(List.of(rs1))
              .build();
      assertFalse(cd.isGlobalCluster());
    }

    // Multiple replication specs with unique zone names
    {
      final ClusterDescription cd =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setReplicationSpecList(List.of(rs1, rs2))
              .build();
      assertTrue(cd.isGlobalCluster());
    }

    // Multiple replication specs with duplicate zone names
    {
      final ClusterDescription cd =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setReplicationSpecList(List.of(rs1, rs2, rs3))
              .build();
      assertTrue(cd.isGlobalCluster());
    }
  }

  @Test
  public void testGetCloudProvider() {
    final ClusterDescription awsCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    assertEquals(CloudProvider.AWS, awsCluster.getCloudProvider());

    final ClusterDescription azureCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AZURE);
    assertEquals(CloudProvider.AZURE, azureCluster.getCloudProvider());

    final ClusterDescription gcpCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.GCP);
    assertEquals(CloudProvider.GCP, gcpCluster.getCloudProvider());

    final ClusterDescription freeCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.FREE);
    assertEquals(CloudProvider.FREE, freeCluster.getCloudProvider());

    final ClusterDescription serverlessCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.SERVERLESS);
    assertEquals(CloudProvider.SERVERLESS, serverlessCluster.getCloudProvider());

    final ClusterDescription flexCluster = NDSModelTestFactory.getClusterDescription(FLEX);
    assertEquals(FLEX, flexCluster.getCloudProvider());
  }

  @Test
  public void testGetTenantType() {
    final ClusterDescription awsCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    assertEquals(CloudProvider.NONE, awsCluster.getTenantType());

    final ClusterDescription azureCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AZURE);
    assertEquals(CloudProvider.NONE, azureCluster.getTenantType());

    final ClusterDescription gcpCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.GCP);
    assertEquals(CloudProvider.NONE, gcpCluster.getTenantType());

    final ClusterDescription freeCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.FREE);
    assertEquals(CloudProvider.FREE, freeCluster.getTenantType());

    final ClusterDescription serverlessCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.SERVERLESS);
    assertEquals(CloudProvider.SERVERLESS, serverlessCluster.getTenantType());

    final ClusterDescription flexCluster = NDSModelTestFactory.getClusterDescription(FLEX);
    assertEquals(FLEX, flexCluster.getTenantType());
  }

  @Test
  public void testPrivateUriHostsMap_InvalidChars() {
    final ClusterDescription cd =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setPrivateLinkMongoUriHosts(Map.of("a.b", List.of("x", "y", "z")))
            .setPrivateLinkSrvAddresses(Map.of("a.b", "xyz"))
            .build();

    assertTrue(cd.getPrivateMongoDBUriHostsMap().containsKey("a.b"));
    assertFalse(cd.getPrivateMongoDBUriHostsMap().containsKey("a@b"));
    assertEquals(List.of("x", "y", "z"), cd.getPrivateMongoDBUriHostsMap().get("a.b"));

    assertTrue(cd.getPrivateSrvAddressMap().containsKey("a.b"));
    assertFalse(cd.getPrivateSrvAddressMap().containsKey("a@b"));
    assertEquals("xyz", cd.getPrivateSrvAddressMap().get("a.b"));

    final BasicDBObject serializedHostsMap =
        (BasicDBObject) cd.toDBObject().get(FieldDefs.PRIVATE_MONGODB_URI_HOSTS_MAP);

    final BasicDBObject serializedSrvMap =
        (BasicDBObject) cd.toDBObject().get(FieldDefs.PRIVATE_SRV_ADDRESS_MAP);

    assertTrue(serializedHostsMap.containsField("a@b"));
    assertFalse(serializedHostsMap.containsField("a.b"));
    assertEquals(DbUtils.toBasicDBList("x", "y", "z"), serializedHostsMap.get("a@b"));

    assertTrue(serializedSrvMap.containsField("a@b"));
    assertFalse(serializedSrvMap.containsField("a.b"));
    assertEquals("xyz", serializedSrvMap.get("a@b"));

    final ClusterDescription parsedCD = new ClusterDescription(cd.toDBObject());

    assertTrue(parsedCD.getPrivateMongoDBUriHostsMap().containsKey("a.b"));
    assertFalse(parsedCD.getPrivateMongoDBUriHostsMap().containsKey("a@b"));
    assertEquals(List.of("x", "y", "z"), parsedCD.getPrivateMongoDBUriHostsMap().get("a.b"));

    assertTrue(cd.getPrivateSrvAddressMap().containsKey("a.b"));
    assertFalse(cd.getPrivateSrvAddressMap().containsKey("a@b"));
    assertEquals("xyz", cd.getPrivateSrvAddressMap().get("a.b"));
  }

  @Test
  public void testIsAutomaticallyPaused() {
    Stream.of(
            // Paused Dedicated single cloud, SERVERLESS, and flex clusters are not marked
            // automatically paused
            Pair.of(CloudProvider.AWS, false),
            Pair.of(CloudProvider.SERVERLESS, false),
            Pair.of(FLEX, false),
            // Paused FREE clusters are marked automatically paused
            Pair.of(CloudProvider.FREE, true))
        .forEach(
            cloudProviderAndIsAutomaticallyPaused -> {
              final ClusterDescription cluster =
                  NDSModelTestFactory.getClusterDescription(
                          cloudProviderAndIsAutomaticallyPaused.getLeft())
                      .copy()
                      .setIsPaused(true)
                      .build();
              assertEquals(
                  cloudProviderAndIsAutomaticallyPaused.getLeft(), cluster.getCloudProvider());
              assertTrue(cluster.isPaused());
              assertEquals(
                  cloudProviderAndIsAutomaticallyPaused.getRight(),
                  cluster.isAutomaticallyPaused());
            });

    // Cross-cloud is not automatically paused
    {
      final ClusterDescription crossCloudCluster = mock(ClusterDescription.class);
      doReturn(true).when(crossCloudCluster).isCrossCloudCluster();
      assertFalse(crossCloudCluster.isAutomaticallyPaused());
    }
  }

  @Test
  public void testGetAutoScaling() {
    final ClusterDescription awsCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setAutoScalingForProvider(
                CloudProvider.AWS, AWSAutoScaling.getDefaultAutoScaling(), NodeTypeFamily.BASE)
            .build();
    assertEquals(
        AWSAutoScaling.getDefaultAutoScaling(), awsCluster.getAutoScaling(NodeTypeFamily.BASE));

    final ClusterDescription azureCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AZURE)
            .copy()
            .setAutoScalingForProvider(
                CloudProvider.AZURE, AzureAutoScaling.getDefaultAutoScaling(), NodeTypeFamily.BASE)
            .build();
    assertEquals(
        AzureAutoScaling.getDefaultAutoScaling(), azureCluster.getAutoScaling(NodeTypeFamily.BASE));

    final ClusterDescription gcpCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.GCP)
            .copy()
            .setAutoScalingForProvider(
                CloudProvider.GCP, GCPAutoScaling.getDefaultAutoScaling(), NodeTypeFamily.BASE)
            .build();
    assertEquals(
        GCPAutoScaling.getDefaultAutoScaling(), gcpCluster.getAutoScaling(NodeTypeFamily.BASE));

    final ClusterDescription freeCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.FREE)
            .copy()
            .setAutoScalingForProvider(
                CloudProvider.FREE, FreeAutoScaling.getDefaultAutoScaling(), NodeTypeFamily.BASE)
            .build();
    assertEquals(
        FreeAutoScaling.getDefaultAutoScaling(), freeCluster.getAutoScaling(NodeTypeFamily.BASE));

    final ClusterDescription serverlessCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.SERVERLESS)
            .copy()
            .setAutoScalingForProvider(
                CloudProvider.SERVERLESS,
                ServerlessAutoScaling.getDefaultAutoScaling(),
                NodeTypeFamily.BASE)
            .build();
    assertEquals(
        ServerlessAutoScaling.getDefaultAutoScaling(),
        serverlessCluster.getAutoScaling(NodeTypeFamily.BASE));

    final ClusterDescription flexCluster =
        NDSModelTestFactory.getClusterDescription(FLEX)
            .copy()
            .setAutoScalingForProvider(
                FLEX, FlexAutoScaling.getDefaultAutoScaling(), NodeTypeFamily.BASE)
            .build();
    assertEquals(
        FlexAutoScaling.getDefaultAutoScaling(), flexCluster.getAutoScaling(NodeTypeFamily.BASE));
  }

  @Test
  public void testGetAnalyticsAutoScaling() {
    // AWS
    {
      final ClusterDescription defaultCluster =
          NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AWS, AWSAutoScaling.getDisabledAutoScaling(), NodeTypeFamily.BASE)
              .build();

      final ClusterDescription clusterWithoutAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AWS, new AWSAutoScaling(null, null, null), NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          defaultCluster.getAutoScaling(NodeTypeFamily.BASE),
          clusterWithoutAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());

      final ClusterDescription clusterWithAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AWS,
                  AWSAutoScaling.getDefaultAutoScaling(),
                  NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          AWSAutoScaling.getDefaultAutoScaling(),
          clusterWithAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());
    }

    // Azure
    {
      final ClusterDescription defaultCluster =
          NDSModelTestFactory.getClusterDescription(CloudProvider.AZURE)
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AZURE,
                  AzureAutoScaling.getDisabledAutoScaling(),
                  NodeTypeFamily.BASE)
              .build();

      final ClusterDescription clusterWithoutAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AZURE,
                  new AzureAutoScaling(null, null, null),
                  NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          defaultCluster.getAutoScaling(NodeTypeFamily.BASE),
          clusterWithoutAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());

      final ClusterDescription clusterWithAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AZURE,
                  AzureAutoScaling.getDefaultAutoScaling(),
                  NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          AzureAutoScaling.getDefaultAutoScaling(),
          clusterWithAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());
    }

    // GCP
    {
      final ClusterDescription defaultCluster =
          NDSModelTestFactory.getClusterDescription(CloudProvider.GCP)
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.GCP, GCPAutoScaling.getDisabledAutoScaling(), NodeTypeFamily.BASE)
              .build();

      final ClusterDescription clusterWithoutAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.GCP, new GCPAutoScaling(null, null, null), NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          defaultCluster.getAutoScaling(NodeTypeFamily.BASE),
          clusterWithoutAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());

      final ClusterDescription clusterWithAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.GCP,
                  GCPAutoScaling.getDefaultAutoScaling(),
                  NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          GCPAutoScaling.getDefaultAutoScaling(),
          clusterWithAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());
    }

    // Free
    {
      final ClusterDescription defaultCluster =
          NDSModelTestFactory.getClusterDescription(CloudProvider.FREE)
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.FREE, FreeAutoScaling.getDisabledAutoScaling(), NodeTypeFamily.BASE)
              .build();

      final ClusterDescription clusterWithoutAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.FREE,
                  new FreeAutoScaling(null, null, null),
                  NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          defaultCluster.getAutoScaling(NodeTypeFamily.BASE),
          clusterWithoutAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());

      final ClusterDescription clusterWithAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.FREE,
                  FreeAutoScaling.getDefaultAutoScaling(),
                  NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          FreeAutoScaling.getDefaultAutoScaling(),
          clusterWithAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());
    }

    // Serverless
    {
      final ClusterDescription defaultCluster =
          NDSModelTestFactory.getClusterDescription(CloudProvider.SERVERLESS)
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.SERVERLESS,
                  ServerlessAutoScaling.getDisabledAutoScaling(),
                  NodeTypeFamily.BASE)
              .build();

      final ClusterDescription clusterWithoutAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.SERVERLESS,
                  new ServerlessAutoScaling(null, null, null),
                  NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          defaultCluster.getAutoScaling(NodeTypeFamily.BASE),
          clusterWithoutAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());

      final ClusterDescription clusterWithAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.SERVERLESS,
                  ServerlessAutoScaling.getDefaultAutoScaling(),
                  NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          ServerlessAutoScaling.getDefaultAutoScaling(),
          clusterWithAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());
    }

    // flex
    {
      final ClusterDescription defaultCluster =
          NDSModelTestFactory.getClusterDescription(FLEX)
              .copy()
              .setAutoScalingForProvider(
                  FLEX, FlexAutoScaling.getDisabledAutoScaling(), NodeTypeFamily.BASE)
              .build();

      final ClusterDescription clusterWithoutAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  FLEX, new FlexAutoScaling(null, null, null), NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          defaultCluster.getAutoScaling(NodeTypeFamily.BASE),
          clusterWithoutAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());

      final ClusterDescription clusterWithAnalyticsAutoScaling =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(
                  FLEX, FlexAutoScaling.getDefaultAutoScaling(), NodeTypeFamily.ANALYTICS)
              .build();
      assertEquals(
          FlexAutoScaling.getDefaultAutoScaling(),
          clusterWithAnalyticsAutoScaling.getAnalyticsAutoScalingWithFallbackToBase());
    }

    // Return disabled auto scaling if analytics scaling is ambiguous but there are 0 analytics
    // nodes.
    {
      final AWSAutoScaling computeEnabledAutoScaling =
          new AWSAutoScaling(
              new AWSComputeAutoScaling(
                  AWSNDSInstanceSize.M30, AWSNDSInstanceSize.M50, true, true, false),
              new DiskGBAutoScaling(false),
              new AutoIndexing(false));
      final ClusterDescription defaultCluster =
          NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AWS, computeEnabledAutoScaling, NodeTypeFamily.BASE)
              .build();

      final HardwareSpec.Builder analyticsHardware =
          new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M10);
      final ClusterDescription cdAmbiguousAutoScalingNoAnalyticsNodes =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(CloudProvider.AWS, null, NodeTypeFamily.ANALYTICS)
              .mixin(ClusterDescriptionBuilderTestMixin::new)
              .updateHardware(analyticsHardware, NodeTypeFamily.ANALYTICS)
              .build();
      assertNotEquals(
          cdAmbiguousAutoScalingNoAnalyticsNodes.getAutoScaling(NodeTypeFamily.BASE),
          cdAmbiguousAutoScalingNoAnalyticsNodes.getAnalyticsAutoScalingWithFallbackToBase());
      assertEquals(
          AWSAutoScaling.getDisabledAutoScaling(),
          cdAmbiguousAutoScalingNoAnalyticsNodes.getAnalyticsAutoScalingWithFallbackToBase());
    }

    // Throw IllegalStateException if analytics scaling is ambiguous and there are analytics nodes.
    {
      final AWSAutoScaling computeEnabledAutoScaling =
          new AWSAutoScaling(
              new AWSComputeAutoScaling(
                  AWSNDSInstanceSize.M30, AWSNDSInstanceSize.M50, true, true, false),
              new DiskGBAutoScaling(false),
              new AutoIndexing(false));
      final ClusterDescription defaultCluster =
          NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
              .copy()
              .setAutoScalingForProvider(
                  CloudProvider.AWS, computeEnabledAutoScaling, NodeTypeFamily.BASE)
              .build();

      final HardwareSpec.Builder analyticsHardware =
          new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M10).setNodeCount(1);
      final ClusterDescription cdAmbiguousAutoScalingWithAnalyticsNodes =
          defaultCluster
              .copy()
              .setAutoScalingForProvider(CloudProvider.AWS, null, NodeTypeFamily.ANALYTICS)
              .mixin(ClusterDescriptionBuilderTestMixin::new)
              .updateHardware(analyticsHardware, NodeTypeFamily.ANALYTICS)
              .build();
      final IllegalStateException exception =
          assertThrows(
              IllegalStateException.class,
              cdAmbiguousAutoScalingWithAnalyticsNodes::getAnalyticsAutoScalingWithFallbackToBase);
      assertTrue(
          exception
              .getMessage()
              .contains("Ambiguous analytics autoscaling configuration for cluster"));
    }
  }

  @Test
  public void testToDBObject_RegionConfigs() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final BasicDBList replicationSpecsDBList =
        clusterDescription.getReplicationSpecsWithShardData().stream()
            .map(replicationSpec -> replicationSpec.toLegacyModelDBObject())
            .collect(DbUtils.toBasicDBList());

    for (int i = 0; i < replicationSpecsDBList.size(); i++) {
      final List<RegionConfig> regionConfigs =
          clusterDescription.getReplicationSpecsWithShardData().get(i).getRegionConfigs();
      final BasicDBObject regionsConfigDBObject =
          (BasicDBObject)
              ((BasicDBObject) replicationSpecsDBList.get(i))
                  .get(ReplicationSpec.FieldDefs.REGIONS_CONFIG);
      assertEquals(regionConfigs.size(), regionsConfigDBObject.keySet().size());
      for (final RegionConfig regionConfig : regionConfigs) {
        final ShardRegionConfig shardRegionConfig = (ShardRegionConfig) regionConfig;
        final BasicDBObject regionSpecDBObject =
            (BasicDBObject) regionsConfigDBObject.get(shardRegionConfig.getRegionName().getName());
        assertEquals(
            shardRegionConfig.getElectableSpecs().getNodeCount(),
            regionSpecDBObject.getInt(RegionSpec.FieldDefs.ELECTABLE_NODES));
        assertEquals(
            shardRegionConfig.getReadOnlySpecs().getNodeCount(),
            regionSpecDBObject.getInt(RegionSpec.FieldDefs.READ_ONLY_NODES));
        assertEquals(
            shardRegionConfig.getAnalyticsSpecs().getNodeCount(),
            regionSpecDBObject.getInt(RegionSpec.FieldDefs.ANALYTICS_NODES));
        assertEquals(
            shardRegionConfig.getPriority(),
            regionSpecDBObject.getInt(RegionSpec.FieldDefs.PRIORITY));
      }
    }
  }

  @Test
  public void testBuilder_setBiConnector() {
    final BasicDBObject clusterDescriptionObj = NDSModelTestFactory.getAWSClusterDescription();
    final BasicDBObject biConnectorObj =
        new ClusterDescription.BiConnector(true, BiConnectorReadPreference.SECONDARY).toDBObject();
    clusterDescriptionObj.append(ClusterDescription.FieldDefs.BI_CONNECTOR, biConnectorObj);
    final Hostnames hostnames = new Hostnames("myhost.mongoisawesome.com");
    biConnectorObj.put(ClusterDescription.BiConnector.FieldDefs.HOSTNAMES, hostnames.toDBList());

    final Date needsSync = new Date();
    biConnectorObj.put(ClusterDescription.BiConnector.FieldDefs.NEEDS_SYNC, needsSync);

    final ClusterDescription updated =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setBiConnector(new ClusterDescription.BiConnector(biConnectorObj))
            .build();
    assertTrue(updated.getBiConnector().isEnabled());
    assertTrue(updated.getBiConnector().getLastDisabledDate().isEmpty());
    assertEquals(hostnames, updated.getBiConnector().getHostnames());
    assertEquals(needsSync, updated.getBiConnector().getNeedsSync().get());

    // Test disabling BI connector does not remove hostnames
    final ClusterDescription disabledWithHostname =
        updated
            .copy()
            .setBiConnector(
                new ClusterDescription.BiConnector(
                    biConnectorObj
                        .append(ClusterDescription.BiConnector.FieldDefs.ENABLED, false)
                        .append(
                            ClusterDescription.BiConnector.FieldDefs.HOSTNAMES, new BasicDBList())))
            .build();
    assertFalse(disabledWithHostname.getBiConnector().isEnabled());
    assertFalse(disabledWithHostname.getBiConnector().getHostnames().isEmpty());
    assertEquals(hostnames, disabledWithHostname.getBiConnector().getHostnames());
    assertTrue(disabledWithHostname.getBiConnector().getLastDisabledDate().isPresent());

    final ClusterDescription updatedDoubleDisable =
        disabledWithHostname
            .copy()
            .setBiConnector(
                new ClusterDescription.BiConnector(false, BiConnectorReadPreference.SECONDARY))
            .build();
    assertFalse(updatedDoubleDisable.getBiConnector().isEnabled());
    assertEquals(
        disabledWithHostname.getBiConnector().getLastDisabledDate(),
        updatedDoubleDisable.getBiConnector().getLastDisabledDate());
  }

  @Test
  public void testBuilder_setBiConnector_preservesAtlasManagedFields() {
    // create test entities
    final Builder bicBuilder = new BiConnector(true, BiConnectorReadPreference.ANALYTICS).copy();

    final var bicHostnames = new Hostnames("abcdefg");
    bicBuilder.setHostnames(bicHostnames);
    final var bicSrvMapForPL = Map.of("endpointHere", "srvHere");
    bicBuilder.setPrivateLinkHostnamesMap(bicSrvMapForPL);

    final var clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setBiConnector(bicBuilder.build())
            .build();

    final BiConnector updatedClusterBic =
        clusterDescription
            .copy()
            .setBiConnector(new BiConnector(true, BiConnectorReadPreference.PRIMARY))
            .build()
            .getBiConnector();
    assertEquals(bicHostnames, updatedClusterBic.getHostnames());
    assertEquals(bicSrvMapForPL, updatedClusterBic.getPrivateLinkHostnamesMap());
    assertEquals(BiConnectorReadPreference.PRIMARY, updatedClusterBic.getReadPreference());
  }

  @Test
  public void testBuilder_updateHardware() {
    // Free hardware updates
    final ClusterDescription freeCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.FREE);
    final FreeTenantProviderOptions freeOptions =
        (FreeTenantProviderOptions) freeCluster.getFreeTenantProviderOptions();

    assertFalse(freeOptions.isNeedsUnpauseTenantRestore());
    assertNull(freeOptions.getUserNotifiedAboutPauseDate());
    assertFalse(freeOptions.shouldExcludeFromPause());
    assertNull(freeOptions.getNdsAccessRevokedDate());
    assertFalse(freeOptions.isUnderCompaction());

    final Date now = new Date();
    final ClusterDescription updatedCluster =
        freeCluster
            .copy()
            .setFreeTenantProviderOptions(
                freeOptions
                    .copy()
                    .setExcludeFromPause(true)
                    .setNdsAccessRevokedDate(new Date())
                    .setNeedsUnpauseTenantRestore(true)
                    .setUserNotifiedAboutPauseDate(now)
                    .setUnderCompaction(true)
                    .build())
            .build();
    final FreeTenantProviderOptions updatedFreeOptions =
        (FreeTenantProviderOptions) updatedCluster.getFreeTenantProviderOptions();

    assertTrue(updatedFreeOptions.isNeedsUnpauseTenantRestore());
    assertNotNull(updatedFreeOptions.getUserNotifiedAboutPauseDate());
    assertNotNull(updatedFreeOptions.getNdsAccessRevokedDate());
    assertEquals(now, updatedFreeOptions.getUserNotifiedAboutPauseDate());
    assertTrue(updatedFreeOptions.shouldExcludeFromPause());
    assertTrue(updatedFreeOptions.isUnderCompaction());
  }

  @Test
  public void testMaxStorageSizeForCluster() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    assertEquals(
        clusterDescription
            .getOnlyInstanceSize(NodeType.ELECTABLE)
            .orElseThrow()
            .getMaxAllowedDiskSizeGB(false),
        clusterDescription.getMaxStorageSizeGBForCluster(ExtendedDiskRestrictions.DISALLOWED),
        0.1);

    final ClusterDescription overLimitCluster =
        clusterDescription
            .copy()
            .setDiskSizeGB(
                clusterDescription
                        .getOnlyInstanceSize(NodeType.ELECTABLE)
                        .orElseThrow()
                        .getMaxAllowedDiskSizeGB(false)
                    + 1)
            .build();
    assertEquals(
        clusterDescription
            .getOnlyInstanceSize(NodeType.ELECTABLE)
            .orElseThrow()
            .getMaxCloudProviderDiskSizeGB(),
        overLimitCluster.getMaxStorageSizeGBForCluster(ExtendedDiskRestrictions.DISALLOWED),
        0.1);
  }

  @Test
  public void testGetMaxAllowedDiskSizeGB() {
    final AWSInstanceSize testSize = AWSNDSInstanceSize.M40;
    final AWSHardwareSpec.Builder hardwareSpec =
        new AWSHardwareSpec.Builder().setInstanceSize(testSize);
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(hardwareSpec)
            .build();
    final int defaultMax = testSize.getMaxAllowedDiskSizeGB(false);
    final int extendedMax = testSize.getMaxAllowedDiskSizeGB(true);
    assertNotEquals(defaultMax, extendedMax);
    assertEquals(
        defaultMax,
        clusterDescription.getMaxAllowedDiskSizeGB(
            NodeType.ELECTABLE, ExtendedDiskRestrictions.DISALLOWED));
    // extended disk size is allowed for single-region clusters
    assertEquals(
        extendedMax,
        clusterDescription.getMaxAllowedDiskSizeGB(
            NodeType.ELECTABLE, ExtendedDiskRestrictions.ALLOWED));

    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AWS, AWSRegionName.US_EAST_1, 7, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AWS, AWSRegionName.US_EAST_2, 6, 1, 0, 0, 0));
    final ClusterDescription multiRegionCluster =
        NDSModelTestFactory.getClusterDescription(AWS, "multiRegion", regionConfigs)
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(hardwareSpec)
            .build();
    assertEquals(
        defaultMax,
        multiRegionCluster.getMaxAllowedDiskSizeGB(
            NodeType.ELECTABLE, ExtendedDiskRestrictions.DISALLOWED));
    // extended disk size for multi-region clusters
    assertEquals(
        extendedMax,
        multiRegionCluster.getMaxAllowedDiskSizeGB(
            NodeType.ELECTABLE, ExtendedDiskRestrictions.ALLOWED));
  }

  @Test
  public void testGetCloudProviderOptions_AWSClusterDescription() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);

    final BasicDBObject regionConfig =
        (BasicDBObject)
            ((BasicDBList)
                    ((BasicDBObject)
                            ((BasicDBList)
                                    NDSModelTestFactory.getAWSClusterDescription()
                                        .get(FieldDefs.REPLICATION_SPEC_LIST))
                                .get(0))
                        .get(ReplicationSpec.FieldDefs.REGION_CONFIGS))
                .get(0);

    assertInstanceOf(
        AWSInstanceSize.class,
        clusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());
    assertEquals(
        ((BasicDBObject) regionConfig.get(RegionConfig.FieldDefs.ELECTABLE_SPECS))
            .getString(AWSHardwareSpec.FieldDefs.INSTANCE_SIZE),
        clusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow().name());
    assertEquals(
        ((BasicDBObject) regionConfig.get(RegionConfig.FieldDefs.ELECTABLE_SPECS))
            .getString(AWSHardwareSpec.FieldDefs.INSTANCE_FAMILY),
        clusterDescription.getInstanceFamily(NodeType.ELECTABLE).getName());
    assertEquals(
        ((BasicDBObject) regionConfig.get(RegionConfig.FieldDefs.ELECTABLE_SPECS))
            .getString(AWSHardwareSpec.FieldDefs.OS),
        clusterDescription.getOS(NodeType.ELECTABLE).name());
    assertEquals(
        ((BasicDBObject) regionConfig.get(RegionConfig.FieldDefs.ELECTABLE_SPECS))
            .getInt(AWSHardwareSpec.FieldDefs.DISK_IOPS),
        clusterDescription.getDiskIOPS(NodeType.ELECTABLE));
    assertEquals(
        ((BasicDBObject) regionConfig.get(RegionConfig.FieldDefs.ELECTABLE_SPECS))
            .getBoolean(AWSHardwareSpec.FieldDefs.ENCRYPT_EBS_VOLUME),
        ((AWSHardwareSpec)
                clusterDescription
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()
                    .get(0)
                    .getElectableSpecs())
            .getEncryptEBSVolume());

    assertEquals(
        Set.of(
            ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.READ_ONLY_SPECS))
                .getString(AWSHardwareSpec.FieldDefs.INSTANCE_SIZE)),
        clusterDescription.getInstanceSizes(NodeType.ELECTABLE).stream()
            .map(InstanceSize::name)
            .collect(Collectors.toSet()));
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.READ_ONLY_SPECS))
            .getString(AWSHardwareSpec.FieldDefs.INSTANCE_FAMILY),
        clusterDescription.getInstanceFamily(NodeType.ELECTABLE).getName());
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.READ_ONLY_SPECS))
            .getString(AWSHardwareSpec.FieldDefs.OS),
        clusterDescription.getOS(NodeType.ELECTABLE).name());
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.READ_ONLY_SPECS))
            .getInt(AWSHardwareSpec.FieldDefs.DISK_IOPS),
        clusterDescription.getDiskIOPS(NodeType.READ_ONLY));
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.READ_ONLY_SPECS))
            .getBoolean(AWSHardwareSpec.FieldDefs.ENCRYPT_EBS_VOLUME),
        ((AWSHardwareSpec)
                clusterDescription
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()
                    .get(0)
                    .getReadOnlySpecs())
            .getEncryptEBSVolume());

    assertEquals(
        Set.of(
            ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.ANALYTICS_SPECS))
                .getString(AWSHardwareSpec.FieldDefs.INSTANCE_SIZE)),
        clusterDescription.getInstanceSizes(NodeType.ELECTABLE).stream()
            .map(InstanceSize::name)
            .collect(Collectors.toSet()));
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.ANALYTICS_SPECS))
            .getString(AWSHardwareSpec.FieldDefs.INSTANCE_FAMILY),
        clusterDescription.getInstanceFamily(NodeType.ELECTABLE).getName());
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.ANALYTICS_SPECS))
            .getString(AWSHardwareSpec.FieldDefs.OS),
        clusterDescription.getOS(NodeType.ELECTABLE).name());
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.ANALYTICS_SPECS))
            .getInt(AWSHardwareSpec.FieldDefs.DISK_IOPS),
        clusterDescription.getDiskIOPS(NodeType.ANALYTICS));
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.ANALYTICS_SPECS))
            .getBoolean(AWSHardwareSpec.FieldDefs.ENCRYPT_EBS_VOLUME),
        ((AWSHardwareSpec)
                clusterDescription
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()
                    .get(0)
                    .getReadOnlySpecs())
            .getEncryptEBSVolume());
  }

  @Test
  public void testGetCloudProviderOptions_AzureClusterDescription() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AZURE);

    final BasicDBObject regionConfig =
        (BasicDBObject)
            ((BasicDBList)
                    ((BasicDBObject)
                            ((BasicDBList)
                                    NDSModelTestFactory.getAzureClusterDescription()
                                        .get(FieldDefs.REPLICATION_SPEC_LIST))
                                .get(0))
                        .get(ReplicationSpec.FieldDefs.REGION_CONFIGS))
                .get(0);

    assertInstanceOf(
        AzureNDSInstanceSize.class,
        clusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());

    assertEquals(
        Set.of(
            ((BasicDBObject) regionConfig.get(RegionConfig.FieldDefs.ELECTABLE_SPECS))
                .getString(AzureHardwareSpec.FieldDefs.INSTANCE_SIZE)),
        clusterDescription.getInstanceSizes(NodeType.ELECTABLE).stream()
            .map(InstanceSize::name)
            .collect(Collectors.toSet()));
    assertEquals(
        ((BasicDBObject) regionConfig.get(RegionConfig.FieldDefs.ELECTABLE_SPECS))
            .getString(AzureHardwareSpec.FieldDefs.INSTANCE_FAMILY),
        clusterDescription.getInstanceFamily(NodeType.ELECTABLE).getName());
    assertEquals(
        ((BasicDBObject) regionConfig.get(RegionConfig.FieldDefs.ELECTABLE_SPECS))
            .getString(AzureHardwareSpec.FieldDefs.DISK_TYPE),
        ((AzureHardwareSpec)
                clusterDescription
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()
                    .get(0)
                    .getElectableSpecs())
            .getDiskType()
            .name());
    assertEquals(
        Set.of(
            ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.READ_ONLY_SPECS))
                .getString(AzureHardwareSpec.FieldDefs.INSTANCE_SIZE)),
        clusterDescription.getInstanceSizes(NodeType.ELECTABLE).stream()
            .map(InstanceSize::name)
            .collect(Collectors.toSet()));
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.READ_ONLY_SPECS))
            .getString(AzureHardwareSpec.FieldDefs.INSTANCE_FAMILY),
        clusterDescription.getInstanceFamily(NodeType.ELECTABLE).getName());
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.READ_ONLY_SPECS))
            .getString(AzureHardwareSpec.FieldDefs.DISK_TYPE),
        ((AzureHardwareSpec)
                clusterDescription
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()
                    .get(0)
                    .getElectableSpecs())
            .getDiskType()
            .name());

    assertEquals(
        Set.of(
            ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.ANALYTICS_SPECS))
                .getString(AzureHardwareSpec.FieldDefs.INSTANCE_SIZE)),
        clusterDescription.getInstanceSizes(NodeType.ELECTABLE).stream()
            .map(InstanceSize::name)
            .collect(Collectors.toSet()));
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.ANALYTICS_SPECS))
            .getString(AzureHardwareSpec.FieldDefs.INSTANCE_FAMILY),
        clusterDescription.getInstanceFamily(NodeType.ELECTABLE).getName());
    assertEquals(
        ((BasicDBObject) regionConfig.get(ShardRegionConfig.FieldDefs.ANALYTICS_SPECS))
            .getString(AzureHardwareSpec.FieldDefs.DISK_TYPE),
        ((AzureHardwareSpec)
                clusterDescription
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()
                    .get(0)
                    .getElectableSpecs())
            .getDiskType()
            .name());
  }

  @Test
  public void testGetCloudProviderOptions_GCPClusterDescription() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.GCP);

    final BasicDBObject cloudProviderOptions =
        (BasicDBObject) clusterDescription.toDBObject().get(FieldDefs.CLOUD_PROVIDER_OPTIONS);

    // Cloud provider options are now just a shared / serverless / flex property
    assertNull(cloudProviderOptions);
  }

  @Test
  public void testGetCloudProviderOptions_FreeClusterDescription() {
    final BasicDBObject clusterDescriptionObject =
        NDSModelTestFactory.getFreeClusterDescription(
            new TestFreeClusterDescriptionConfig().setInstanceSize(FreeInstanceSize.M2));
    final ClusterDescription clusterDescription = new ClusterDescription(clusterDescriptionObject);

    final BasicDBObject cloudProviderOptions =
        (BasicDBObject) clusterDescriptionObject.get(FieldDefs.CLOUD_PROVIDER_OPTIONS);

    final BasicDBObject regionConfig =
        (BasicDBObject)
            ((BasicDBList)
                    ((BasicDBObject)
                            ((BasicDBList)
                                    clusterDescriptionObject.get(FieldDefs.REPLICATION_SPEC_LIST))
                                .get(0))
                        .get(ReplicationSpec.FieldDefs.REGION_CONFIGS))
                .get(0);

    assertEquals(
        cloudProviderOptions.get(FreeHardwareSpec.FieldDefs.PROVIDER_NAME),
        clusterDescription.getBackingProvider().name());
    assertEquals(
        regionConfig.getString(RegionConfig.FieldDefs.REGION_NAME),
        clusterDescription
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigs()
            .get(0)
            .getRegionName()
            .getName());
    assertInstanceOf(
        FreeInstanceSize.class,
        clusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow());
    assertEquals(
        Set.of(cloudProviderOptions.get(FreeHardwareSpec.FieldDefs.INSTANCE_SIZE)),
        clusterDescription.getInstanceSizes(NodeType.ELECTABLE).stream()
            .map(InstanceSize::name)
            .collect(Collectors.toSet()));
    assertEquals(
        cloudProviderOptions.get(FreeTenantProviderOptions.FieldDefs.LIMITS_PROFILE),
        ((FreeTenantProviderOptions) clusterDescription.getFreeTenantProviderOptions())
            .getLimitsProfile()
            .name());
    assertEquals(
        cloudProviderOptions.get(FreeTenantProviderOptions.FieldDefs.TENANT_BACKUP_ENABLED),
        clusterDescription.getFreeTenantProviderOptions().isTenantBackupEnabled());
    assertEquals(
        cloudProviderOptions
            .getDate(FreeTenantProviderOptions.FieldDefs.NEXT_BACKUP_DATE)
            .getTime(),
        clusterDescription.getNextTenantBackupDate().getTime());
  }

  @Test
  public void testToDBObject_cloudProviderOptions() {
    final ClusterDescription awsClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final BasicDBObject awsDBObject = awsClusterDescription.toDBObject();

    assertNull(awsDBObject.get("cloudProviderOptions"));

    final BasicDBList awsReplicationSpecList = (BasicDBList) awsDBObject.get("replicationSpecList");
    final BasicDBObject awsReplicationSpec = (BasicDBObject) awsReplicationSpecList.get(0);
    final BasicDBList awsRegionConfigs = (BasicDBList) awsReplicationSpec.get("regionConfigs");
    final BasicDBObject awsRegionConfig = (BasicDBObject) awsRegionConfigs.get(0);
    final BasicDBObject awsElectableSpecs = (BasicDBObject) awsRegionConfig.get("electableSpecs");

    assertEquals(
        awsClusterDescription.getDiskIOPS(NodeType.ELECTABLE),
        awsElectableSpecs.getInt(AWSHardwareSpec.FieldDefs.DISK_IOPS));
    assertEquals(
        awsClusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow().name(),
        awsElectableSpecs.getString(AWSHardwareSpec.FieldDefs.INSTANCE_SIZE));
    assertEquals(
        awsClusterDescription.getInstanceFamily(NodeType.ELECTABLE).getName(),
        awsElectableSpecs.getString(AWSHardwareSpec.FieldDefs.INSTANCE_FAMILY));
    assertEquals(
        awsClusterDescription.getOS(NodeType.ELECTABLE).name(),
        awsElectableSpecs.getString(AWSHardwareSpec.FieldDefs.OS));
    assertEquals(
        ((AWSHardwareSpec)
                awsClusterDescription
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()
                    .get(0)
                    .getElectableSpecs())
            .getEBSVolumeType()
            .name(),
        awsElectableSpecs.getString(AWSHardwareSpec.FieldDefs.EBS_VOLUME_TYPE));
    assertEquals(
        ((AWSHardwareSpec)
                awsClusterDescription
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()
                    .get(0)
                    .getElectableSpecs())
            .getEncryptEBSVolume(),
        awsElectableSpecs.getBoolean(AWSHardwareSpec.FieldDefs.ENCRYPT_EBS_VOLUME));

    final ClusterDescription azureClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AZURE);

    final BasicDBObject azureDBObject = azureClusterDescription.toDBObject();

    assertNull(azureDBObject.get("cloudProviderOptions"));

    final BasicDBList azureReplicationSpecList =
        (BasicDBList) azureDBObject.get("replicationSpecList");
    final BasicDBObject azureReplicationSpec = (BasicDBObject) azureReplicationSpecList.get(0);
    final BasicDBList azureRegionConfigs = (BasicDBList) azureReplicationSpec.get("regionConfigs");
    final BasicDBObject azureRegionConfig = (BasicDBObject) azureRegionConfigs.get(0);
    final BasicDBObject azureElectableSpecs =
        (BasicDBObject) azureRegionConfig.get("electableSpecs");

    assertEquals(
        ((AzureHardwareSpec)
                azureClusterDescription
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()
                    .get(0)
                    .getElectableSpecs())
            .getDiskType()
            .name(),
        azureElectableSpecs.getString(AzureHardwareSpec.FieldDefs.DISK_TYPE));
    assertEquals(
        azureClusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow().name(),
        azureElectableSpecs.getString(AzureHardwareSpec.FieldDefs.INSTANCE_SIZE));
    assertEquals(
        azureClusterDescription.getInstanceFamily(NodeType.ELECTABLE).getName(),
        azureElectableSpecs.getString(AzureHardwareSpec.FieldDefs.INSTANCE_FAMILY));

    final ClusterDescription gcpClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.GCP);

    final BasicDBObject gcpDBObject = gcpClusterDescription.toDBObject();

    assertNull(gcpDBObject.get("cloudProviderOptions"));

    final BasicDBList gcpReplicationSpecList = (BasicDBList) gcpDBObject.get("replicationSpecList");
    final BasicDBObject gcpReplicationSpec = (BasicDBObject) gcpReplicationSpecList.get(0);
    final BasicDBList gcpRegionConfigs = (BasicDBList) gcpReplicationSpec.get("regionConfigs");
    final BasicDBObject gcpRegionConfig = (BasicDBObject) gcpRegionConfigs.get(0);
    final BasicDBObject gcpElectableSpecs = (BasicDBObject) gcpRegionConfig.get("electableSpecs");

    assertEquals(
        gcpClusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow().name(),
        gcpElectableSpecs.getString(GCPHardwareSpec.FieldDefs.INSTANCE_SIZE));
    assertEquals(
        gcpClusterDescription.getInstanceFamily(NodeType.ELECTABLE).getName(),
        gcpElectableSpecs.getString(GCPHardwareSpec.FieldDefs.INSTANCE_FAMILY));

    final ClusterDescription freeClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.FREE);
    final BasicDBObject freeDBObject = freeClusterDescription.toDBObject();
    final BasicDBObject freeProviderOptions =
        (BasicDBObject) freeDBObject.get("cloudProviderOptions");

    final BasicDBObject regionConfigObject =
        (BasicDBObject)
            ((BasicDBList)
                    ((BasicDBObject)
                            ((BasicDBList) freeDBObject.get(FieldDefs.REPLICATION_SPEC_LIST))
                                .get(0))
                        .get(ReplicationSpec.FieldDefs.REGION_CONFIGS))
                .get(0);
    final BasicDBObject electableSpecsObject =
        (BasicDBObject) regionConfigObject.get(RegionConfig.FieldDefs.ELECTABLE_SPECS);
    assertEquals(
        ((FreeHardwareSpec)
                freeClusterDescription
                    .getReplicationSpecsWithShardData()
                    .get(0)
                    .getRegionConfigs()
                    .get(0)
                    .getElectableSpecs())
            .getBackingProvider()
            .name(),
        electableSpecsObject.getString(FreeHardwareSpec.FieldDefs.PROVIDER_NAME));
    // After we are saving the replicationSpecList on free clusters, amend this test to check the
    // regionname on regionConfigs matches
    assertEquals(
        freeClusterDescription
            .getReplicationSpecsWithShardData()
            .get(0)
            .getRegionConfigs()
            .get(0)
            .getRegionName()
            .getName(),
        regionConfigObject.getString(RegionConfig.FieldDefs.REGION_NAME));
    assertEquals(
        freeClusterDescription.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow().name(),
        electableSpecsObject.get(FreeHardwareSpec.FieldDefs.INSTANCE_SIZE));

    final FreeTenantProviderOptions freeTenantProviderOptions =
        (FreeTenantProviderOptions) freeClusterDescription.getFreeTenantProviderOptions();
    assertEquals(
        freeTenantProviderOptions.getLimitsProfile().name(),
        freeProviderOptions.get(FreeTenantProviderOptions.FieldDefs.LIMITS_PROFILE));
    assertEquals(
        freeTenantProviderOptions.getConnectionLimit(),
        freeProviderOptions.get(FreeTenantProviderOptions.FieldDefs.CONNECTION_LIMIT));
    assertEquals(
        freeTenantProviderOptions.getOperationsPerSecondLimit(),
        freeProviderOptions.get(FreeTenantProviderOptions.FieldDefs.OPERATIONS_PER_SECOND_LIMIT));
    assertEquals(
        freeTenantProviderOptions.getDatabaseLimit(),
        freeProviderOptions.get(FreeTenantProviderOptions.FieldDefs.DATABASE_LIMIT));
    assertEquals(
        freeTenantProviderOptions.getCollectionLimit(),
        freeProviderOptions.get(FreeTenantProviderOptions.FieldDefs.COLLECTION_LIMIT));
    assertEquals(
        freeTenantProviderOptions.getGbPerWeekOutLimit(),
        freeProviderOptions.get(FreeTenantProviderOptions.FieldDefs.GB_PER_WEEK_OUT_LIMIT));
    assertEquals(
        freeTenantProviderOptions.getGbPerWeekInLimit(),
        freeProviderOptions.get(FreeTenantProviderOptions.FieldDefs.GB_PER_WEEK_IN_LIMIT));
    assertEquals(
        freeTenantProviderOptions.getThrottledKBPerSecondLimit(),
        freeProviderOptions.get(FreeTenantProviderOptions.FieldDefs.THROTTLED_KB_PER_SECOND_LIMIT));
    assertEquals(
        freeTenantProviderOptions.getQueryUtilizationBucketWidthSeconds(),
        freeProviderOptions.get(
            FreeTenantProviderOptions.FieldDefs.QUERY_UTILIZATION_BUCKET_WIDTH_SECONDS));
    assertEquals(
        freeTenantProviderOptions.getQueryUtilizationWindowLengthSeconds(),
        freeProviderOptions.get(
            FreeTenantProviderOptions.FieldDefs.QUERY_UTILIZATION_WINDOW_LENGTH_SECONDS));
    assertEquals(
        freeTenantProviderOptions.getQueryUtilizationTimeThreshold(),
        freeProviderOptions.get(
            FreeTenantProviderOptions.FieldDefs.QUERY_UTILIZATION_TIME_THRESHOLD));
    assertEquals(
        freeTenantProviderOptions.getQueryUtilizationMaxSleepTimeSeconds(),
        freeProviderOptions.get(
            FreeTenantProviderOptions.FieldDefs.QUERY_UTILIZATION_MAX_SLEEP_TIME_SECONDS));
    assertEquals(
        freeClusterDescription.getFreeTenantProviderOptions().isTenantBackupEnabled(),
        freeProviderOptions.get(FreeTenantProviderOptions.FieldDefs.TENANT_BACKUP_ENABLED));
    assertEquals(
        freeClusterDescription.getNextTenantBackupDate(),
        freeProviderOptions.get(FreeTenantProviderOptions.FieldDefs.NEXT_BACKUP_DATE));

    final ClusterDescription serverlessClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.SERVERLESS);
    final BasicDBObject serverlessDBObject = serverlessClusterDescription.toDBObject();
    final BasicDBObject serverlessProviderOptions =
        (BasicDBObject) serverlessDBObject.get(FieldDefs.CLOUD_PROVIDER_OPTIONS);

    final ServerlessHardwareSpec serverlessHardwareSpec =
        (ServerlessHardwareSpec)
            serverlessClusterDescription
                .getReplicationSpecsWithShardData()
                .get(0)
                .getRegionConfigs()
                .get(0)
                .getElectableSpecs();

    final ServerlessTenantProviderOptions serverlessTenantProviderOptions =
        (ServerlessTenantProviderOptions)
            serverlessClusterDescription.getServerlessTenantProviderOptions();

    assertEquals(
        serverlessHardwareSpec.getInstanceSize().name(),
        serverlessProviderOptions.get(ServerlessTenantProviderOptions.FieldDefs.INSTANCE_SIZE));
    assertEquals(
        serverlessTenantProviderOptions.getDiskSizeGBLimit(),
        serverlessProviderOptions.get(
            ServerlessTenantProviderOptions.FieldDefs.DISK_SIZE_GB_LIMIT));
    assertEquals(
        serverlessTenantProviderOptions.getConnectionLimit(),
        serverlessProviderOptions.get(ServerlessTenantProviderOptions.FieldDefs.CONNECTION_LIMIT));
    assertEquals(
        serverlessTenantProviderOptions.isExcludeFromPool(),
        serverlessProviderOptions.get(ServerlessTenantProviderOptions.FieldDefs.EXCLUDE_FROM_POOL));

    final ClusterDescription flexClusterDescription =
        NDSModelTestFactory.getClusterDescription(FLEX);
    final BasicDBObject flexDBObject = flexClusterDescription.toDBObject();
    final BasicDBObject flexProviderOptions =
        (BasicDBObject) flexDBObject.get(FieldDefs.CLOUD_PROVIDER_OPTIONS);

    final FlexHardwareSpec flexHardwareSpec =
        (FlexHardwareSpec)
            flexClusterDescription
                .getReplicationSpecsWithShardData()
                .get(0)
                .getRegionConfigs()
                .get(0)
                .getElectableSpecs();

    final FlexTenantProviderOptions flexTenantProviderOptions =
        (FlexTenantProviderOptions) flexClusterDescription.getFlexTenantProviderOptions();

    assertEquals(
        flexTenantProviderOptions.getConnectionLimit(),
        flexProviderOptions.get(FlexTenantProviderOptions.FieldDefs.CONNECTION_LIMIT));
    assertEquals(
        flexTenantProviderOptions.getCollectionLimit(),
        flexProviderOptions.get(FlexTenantProviderOptions.FieldDefs.COLLECTION_LIMIT));
    assertEquals(
        flexTenantProviderOptions.getDatabaseLimit(),
        flexProviderOptions.get(FlexTenantProviderOptions.FieldDefs.DATABASE_LIMIT));
    assertEquals(
        flexTenantProviderOptions.getOperationsPerSecondLimit(),
        flexProviderOptions.get(FlexTenantProviderOptions.FieldDefs.OPERATIONS_PER_SECOND_LIMIT));
    assertNotNull(flexTenantProviderOptions.getNextBackupDate());
    assertEquals(
        flexTenantProviderOptions.getDiskSizeGBLimit(),
        flexProviderOptions.get(FlexTenantProviderOptions.FieldDefs.DISK_SIZE_GB_LIMIT));
  }

  record GetFeatureCompatibilityVersionTestParam(
      VersionUtils.Version mongodbVersion,
      VersionReleaseSystem versionReleaseSystem,
      Optional<String> continuousFCV,
      Optional<FixedVersion> fixedFCV,
      Optional<FixedVersion> fixedMongoDBVersion,
      VersionUtils.Version expectedResult) {}

  public static List<GetFeatureCompatibilityVersionTestParam>
      getTestGetFeatureCompatibilityVersion_testParams() {
    return List.of(
        // Fixed FCV takes total precedence
        new GetFeatureCompatibilityVersionTestParam(
            VersionUtils.FIVE_ZERO_ZERO,
            VersionReleaseSystem.LTS,
            Optional.empty(),
            Optional.of(new FixedVersion("4.4")),
            Optional.empty(),
            VersionUtils.FOUR_FOUR_ZERO),
        // Fixed Rapid Release FCV is next in priority as long as mdb version is not fixed
        new GetFeatureCompatibilityVersionTestParam(
            VersionUtils.SIX_ONE_ZERO,
            VersionReleaseSystem.CONTINUOUS,
            Optional.of("6.0"),
            Optional.empty(),
            Optional.empty(),
            VersionUtils.SIX_ZERO_ZERO),
        // if mdb version is not fixed, use mongodbVersion (not fixedMongoDBVersion?) even if rapid
        // release fcv is set
        new GetFeatureCompatibilityVersionTestParam(
            VersionUtils.FIVE_ZERO_TWENTY_FIVE,
            VersionReleaseSystem.CONTINUOUS,
            Optional.of("6.0"),
            Optional.empty(),
            Optional.of(new FixedVersion("6.0")),
            VersionUtils.FIVE_ZERO_ZERO),
        // default to mongodbVersion
        new GetFeatureCompatibilityVersionTestParam(
            VersionUtils.SIX_ZERO_ZERO,
            VersionReleaseSystem.LTS,
            Optional.empty(),
            Optional.empty(),
            Optional.empty(),
            VersionUtils.SIX_ZERO_ZERO));
  }

  @ParameterizedTest
  @MethodSource("getTestGetFeatureCompatibilityVersion_testParams")
  public void testGetFeatureCompatibilityVersion(
      final GetFeatureCompatibilityVersionTestParam pParams) {
    final ObjectId groupId = new ObjectId();
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription(groupId))
            .copy()
            .setMongoDBVersion(pParams.mongodbVersion.getVersion())
            .setFixedMongoDBVersion(pParams.fixedMongoDBVersion)
            .setVersionReleaseSystem(pParams.versionReleaseSystem)
            .setContinuousDeliveryFCV(pParams.continuousFCV.orElse(null))
            .setFixedFeatureCompatibilityVersion(pParams.fixedFCV)
            .build();
    assertEquals(
        pParams.expectedResult.getMajorVersionString(),
        clusterDescription.getFeatureCompatibilityVersion());
  }

  @ParameterizedTest
  @MethodSource("getTestGetFeatureCompatibilityVersion_testParams")
  public void testGetFeatureCompatibilityVersionStaticUtility(
      final GetFeatureCompatibilityVersionTestParam pParams) {
    assertEquals(
        pParams.expectedResult,
        ClusterDescription.getFeatureCompatibilityVersion(
            pParams.mongodbVersion,
            pParams.versionReleaseSystem,
            pParams.continuousFCV,
            pParams.fixedFCV,
            pParams.fixedMongoDBVersion.isPresent()));
  }

  @Test
  public void testDiffs_TerminationProtectionEnabled() {
    final ObjectId groupId = new ObjectId();
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS);

    // new cluster diff
    {
      final ItemDiff diff = clusterDescription.getNewClusterDiff();
      final Optional<DeploymentItemParameterDiff> terminationProtectionDiff =
          diff.getItems().stream()
              .filter(
                  i ->
                      i.getParamName()
                          .equals(ClusterDescription.FieldDefs.TERMINATION_PROTECTION_ENABLED))
              .findFirst();
      assertTrue(terminationProtectionDiff.isPresent());
      assertNull(terminationProtectionDiff.get().getOldValue());
      assertFalse((boolean) terminationProtectionDiff.get().getNewValue());
    }

    // updated cluster diff
    {
      final ItemDiff diff =
          clusterDescription.getUpdatedClusterDiff(
              clusterDescription.copy().setTerminationProtectionEnabled(true).build());

      final Optional<DeploymentItemParameterDiff> terminationProtectionDiff =
          diff.getItems().stream()
              .filter(
                  i ->
                      i.getParamName()
                          .equals(ClusterDescription.FieldDefs.TERMINATION_PROTECTION_ENABLED))
              .findFirst();
      assertTrue(terminationProtectionDiff.isPresent());
      assertTrue((boolean) terminationProtectionDiff.get().getOldValue());
      assertFalse((boolean) terminationProtectionDiff.get().getNewValue());
    }

    // deleted cluster diff
    {
      final ItemDiff diff = clusterDescription.getDeletedClusterDiff();
      final Optional<DeploymentItemParameterDiff> terminationProtectionDiff =
          diff.getItems().stream()
              .filter(
                  i ->
                      i.getParamName()
                          .equals(ClusterDescription.FieldDefs.TERMINATION_PROTECTION_ENABLED))
              .findFirst();
      assertTrue(terminationProtectionDiff.isPresent());
      assertFalse((boolean) terminationProtectionDiff.get().getOldValue());
      assertNull(terminationProtectionDiff.get().getNewValue());
    }
  }

  @Test
  public void testDiffs_NeedsSampleDataLoadAfter() {
    final ObjectId groupId = new ObjectId();
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS);

    // new cluster diff
    {
      final ItemDiff diff = clusterDescription.getNewClusterDiff();
      final Optional<DeploymentItemParameterDiff> sampleDataDiff =
          diff.getItems().stream()
              .filter(
                  i ->
                      i.getParamName()
                          .equals(ClusterDescription.FieldDefs.NEEDS_SAMPLE_DATA_LOAD_AFTER))
              .findFirst();
      assertFalse(sampleDataDiff.isPresent());
    }

    // updated cluster diff
    Date today = new Date();
    {
      final ItemDiff diff =
          clusterDescription.getUpdatedClusterDiff(
              clusterDescription.copy().setNeedsSampleDataLoadAfter(today).build());

      final Optional<DeploymentItemParameterDiff> sampleDataDiff =
          diff.getItems().stream()
              .filter(
                  i ->
                      i.getParamName()
                          .equals(ClusterDescription.FieldDefs.NEEDS_SAMPLE_DATA_LOAD_AFTER))
              .findFirst();
      assertTrue(sampleDataDiff.isPresent());
      assertEquals(today, sampleDataDiff.get().getOldValue());
      assertNull(sampleDataDiff.get().getNewValue());
    }

    // deleted cluster diff
    {
      final ItemDiff diff = clusterDescription.getDeletedClusterDiff();
      final Optional<DeploymentItemParameterDiff> sampleDataDiff =
          diff.getItems().stream()
              .filter(
                  i ->
                      i.getParamName()
                          .equals(ClusterDescription.FieldDefs.NEEDS_SAMPLE_DATA_LOAD_AFTER))
              .findFirst();
      assertFalse(sampleDataDiff.isPresent());
    }
  }

  @Test
  public void testDiffs_SwapIpMaintenanceRound() {
    final ObjectId groupId = new ObjectId();

    // Null against null
    {
      final ClusterDescription clusterDescription1 =
          NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS);

      final ClusterDescription clusterDescription2 =
          NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS);

      final var diff = clusterDescription1.getUpdatedClusterDiff(clusterDescription2);
      final var swapIpMaintenanceRoundDiff =
          diff.getItems().stream()
              .filter(i -> i.getParamName().equals(FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED))
              .findFirst();
      assertTrue(swapIpMaintenanceRoundDiff.isEmpty());
    }

    // Rounds match
    {
      final ClusterDescription clusterDescription1 =
          NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS)
              .copy()
              .setSwapIpMaintenanceRound(Optional.of(SwapIpMaintenanceRound.FIRST))
              .build();

      final ClusterDescription clusterDescription2 =
          NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS)
              .copy()
              .setSwapIpMaintenanceRound(Optional.of(SwapIpMaintenanceRound.FIRST))
              .build();

      final var diff = clusterDescription1.getUpdatedClusterDiff(clusterDescription2);
      final var swapIpMaintenanceRoundDiff =
          diff.getItems().stream()
              .filter(i -> i.getParamName().equals(FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED))
              .findFirst();
      assertTrue(swapIpMaintenanceRoundDiff.isEmpty());
    }

    // Rounds do not match
    {
      final ClusterDescription clusterDescription1 =
          NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS)
              .copy()
              .setSwapIpMaintenanceRound(Optional.of(SwapIpMaintenanceRound.SECOND))
              .build();

      final ClusterDescription clusterDescription2 =
          NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS)
              .copy()
              .setSwapIpMaintenanceRound(Optional.of(SwapIpMaintenanceRound.FIRST))
              .build();

      final var diff = clusterDescription1.getUpdatedClusterDiff(clusterDescription2);
      final var swapIpMaintenanceRoundDiff =
          diff.getItems().stream()
              .filter(i -> i.getParamName().equals(FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED))
              .findFirst();
      assertTrue(swapIpMaintenanceRoundDiff.isPresent());
      assertEquals(
          SwapIpMaintenanceRound.FIRST.name(), swapIpMaintenanceRoundDiff.get().getOldValue());
      assertEquals(
          SwapIpMaintenanceRound.SECOND.name(), swapIpMaintenanceRoundDiff.get().getNewValue());
    }

    // Null against a set round
    {
      final ClusterDescription clusterDescription1 =
          NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS);

      final ClusterDescription clusterDescription2 =
          NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS)
              .copy()
              .setSwapIpMaintenanceRound(Optional.of(SwapIpMaintenanceRound.FIRST))
              .build();

      final var diff = clusterDescription1.getUpdatedClusterDiff(clusterDescription2);
      final var swapIpMaintenanceRoundDiff =
          diff.getItems().stream()
              .filter(i -> i.getParamName().equals(FieldDefs.SWAP_IP_MAINTENANCE_ROUND_COMPLETED))
              .findFirst();
      assertTrue(swapIpMaintenanceRoundDiff.isPresent());
      assertEquals(
          SwapIpMaintenanceRound.FIRST.name(), swapIpMaintenanceRoundDiff.get().getOldValue());
      assertNull(swapIpMaintenanceRoundDiff.get().getNewValue());
    }
  }

  @Test
  public void testDiffs_Serverless() {
    final ObjectId groupId = new ObjectId();
    final BasicDBObject baseDescDoc =
        NDSModelTestFactory.getServerlessClusterDescription(
            new TestServerlessClusterDescriptionConfig().setGroupId(groupId));
    // new cluster diff
    {
      final ClusterDescription clusterDescription = new ClusterDescription(baseDescDoc);
      final ItemDiff newDiff = clusterDescription.getNewClusterDiff();
      assertTrue(
          newDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.INSTANCE_SIZE)));
      assertTrue(
          newDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(RegionConfig.FieldDefs.REGION_NAME)));
      assertFalse(
          newDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.CLUSTER_TYPE)));
      assertFalse(
          newDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.REPLICATION_SPEC_LIST)));
      assertFalse(
          newDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(RegionConfig.FieldDefs.AUTO_SCALING)));
    }

    // deleted cluster diff
    {
      final ClusterDescription clusterDescription = new ClusterDescription(baseDescDoc);
      final ItemDiff deletedDiff = clusterDescription.getDeletedClusterDiff();
      assertTrue(
          deletedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(ClusterDescription.FieldDefs.INSTANCE_SIZE)));
      assertTrue(
          deletedDiff.getItems().stream()
              .anyMatch(
                  i ->
                      i.getParamName()
                          .equals(ClusterDescriptionProviderOptions.FieldDefs.REGION_NAME)));
      assertFalse(
          deletedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.CLUSTER_TYPE)));
      assertFalse(
          deletedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.REPLICATION_SPEC_LIST)));
      assertFalse(
          deletedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(RegionConfig.FieldDefs.AUTO_SCALING)));
    }

    // modified cluster diff
    {
      final ClusterDescription clusterDescription = new ClusterDescription(baseDescDoc);
      final ClusterDescription modifiedClusterDescription =
          clusterDescription
              .copy()
              .setReplicationSpecList(
                  clusterDescription.getReplicationSpecsWithShardData().stream()
                      .map(
                          replicationSpec ->
                              replicationSpec
                                  .copy()
                                  .setRegionConfigs(
                                      replicationSpec.getRegionConfigs().stream()
                                          .map(
                                              regionConfig ->
                                                  regionConfig
                                                      .copy()
                                                      .setRegionName(AWSRegionName.EU_WEST_1)
                                                      .build())
                                          .collect(Collectors.toList()))
                                  .build())
                      .collect(Collectors.toList()))
              .build();

      final ItemDiff modifiedDiff =
          modifiedClusterDescription.getUpdatedClusterDiff(clusterDescription);
      assertEquals(1, modifiedDiff.getItems().size(), modifiedDiff.getItems().toString());
      assertTrue(
          modifiedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(RegionConfig.FieldDefs.REGION_NAME)));
      assertFalse(
          modifiedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.CLUSTER_TYPE)));
      assertFalse(
          modifiedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.REPLICATION_SPEC_LIST)));
      assertFalse(
          modifiedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(RegionConfig.FieldDefs.AUTO_SCALING)));
    }
    // modifying backup
    {
      final ClusterDescription clusterDescription = new ClusterDescription(baseDescDoc);
      final ClusterDescription modifiedClusterDescription =
          clusterDescription
              .copy()
              .setServerlessBackupOptions(
                  new ServerlessBackupOptions.Builder()
                      .setServerlessContinuousBackupEnabled(true)
                      .build())
              .build();
      final ItemDiff modifiedDiff =
          modifiedClusterDescription.getUpdatedClusterDiff(clusterDescription);
      assertEquals(1, modifiedDiff.getItems().size(), modifiedDiff.getItems().toString());
      assertTrue(
          modifiedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.SERVERLESS_BACKUP_OPTIONS)));
    }
  }

  @Test
  public void testConstructFromDBObject_Serverless() {
    final ObjectId groupId = new ObjectId();
    final BasicDBObject baseDescDoc =
        NDSModelTestFactory.getServerlessClusterDescription(
            new TestServerlessClusterDescriptionConfig().setGroupId(groupId));
    final ClusterDescription clusterDescription = new ClusterDescription(baseDescDoc);

    final ServerlessHardwareSpec hardwareSpec =
        (ServerlessHardwareSpec)
            clusterDescription
                .getOnlyHardwareSpecForProvider(SERVERLESS, NodeType.ELECTABLE)
                .orElseThrow();

    final ServerlessTenantProviderOptions serverlessTenantProviderOptions =
        (ServerlessTenantProviderOptions) clusterDescription.getServerlessTenantProviderOptions();

    assertTrue(clusterDescription.isTenantCluster());
    assertTrue(clusterDescription.isServerlessTenantCluster());
    assertFalse(clusterDescription.isSharedTenantCluster());
    assertFalse(clusterDescription.isFreeTenantCluster());
    assertFalse(clusterDescription.isFlexTenantCluster());
    assertFalse(clusterDescription.isFlexOrSharedTenantCluster());

    assertEquals(SERVERLESS, clusterDescription.getTenantType());

    assertEquals(
        ((BasicDBObject)
                ((BasicDBList)
                        ((BasicDBObject)
                                ((BasicDBList)
                                        baseDescDoc.get(
                                            ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST))
                                    .get(0))
                            .get(ReplicationSpec.FieldDefs.REGION_CONFIGS))
                    .get(0))
            .getString(RegionConfig.FieldDefs.CLOUD_PROVIDER),
        clusterDescription.getCloudProvider().name());

    final BasicDBObject cloudProviderOptions =
        (BasicDBObject)
            clusterDescription
                .toDBObject()
                .get(ClusterDescription.FieldDefs.CLOUD_PROVIDER_OPTIONS);

    assertEquals(
        cloudProviderOptions.getString(ServerlessTenantProviderOptions.FieldDefs.INSTANCE_SIZE),
        serverlessTenantProviderOptions.getInstanceSize().name());

    assertEquals(
        (cloudProviderOptions.containsField(
                ServerlessTenantProviderOptions.FieldDefs.DISK_SIZE_GB_LIMIT)
            ? cloudProviderOptions.getInt(
                ServerlessTenantProviderOptions.FieldDefs.DISK_SIZE_GB_LIMIT)
            : clusterDescription
                .getOnlyInstanceSize(NodeType.ELECTABLE)
                .orElseThrow()
                .getMaxAllowedDiskSizeGB(false)),
        serverlessTenantProviderOptions.getDiskSizeGBLimit());

    assertEquals(
        (cloudProviderOptions.containsField(
                ServerlessTenantProviderOptions.FieldDefs.CONNECTION_LIMIT)
            ? cloudProviderOptions.getInt(
                ServerlessTenantProviderOptions.FieldDefs.CONNECTION_LIMIT)
            : clusterDescription
                .getOnlyInstanceSize(NodeType.ELECTABLE)
                .orElseThrow()
                .getMaxIncomingConnections()),
        serverlessTenantProviderOptions.getConnectionLimit());

    assertEquals(
        cloudProviderOptions.getBoolean(
            ServerlessTenantProviderOptions.FieldDefs.EXCLUDE_FROM_POOL),
        serverlessTenantProviderOptions.isExcludeFromPool());

    assertFalse(cloudProviderOptions.containsField("tenantBackupEnabled"));
    assertFalse(serverlessTenantProviderOptions.isTenantBackupEnabled());
    assertFalse(cloudProviderOptions.containsField("nextBackupDate"));
    assertNull(serverlessTenantProviderOptions.getNextBackupDate());
  }

  @Test
  public void testDiffs_flex() {
    final ObjectId groupId = new ObjectId();
    final BasicDBObject baseDescDoc =
        NDSModelTestFactory.getFlexClusterDescription(
            new TestFlexClusterDescriptionConfig().setGroupId(groupId));
    // new cluster diff
    {
      final ClusterDescription flexInstance = new ClusterDescription(baseDescDoc);
      final ItemDiff newDiff = flexInstance.getNewClusterDiff();

      // flex should align with serverless style instances in the future, so hide the cluster type
      // and replication spec list
      assertFalse(
          newDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.CLUSTER_TYPE)));
      assertFalse(
          newDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.REPLICATION_SPEC_LIST)));
      // Instance size is not hidden in the serverless diff, which is probably a mistake since we
      // don't return it in the public API, it should be hidden in flex
      assertFalse(
          newDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.INSTANCE_SIZE)));
      // Dedicate auto-scaling is not support on tenant clusters
      assertFalse(
          newDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(RegionConfig.FieldDefs.AUTO_SCALING)));

      assertTrue(
          newDiff.getItems().stream().anyMatch(i -> i.getParamName().equals(CLOUD_PROVIDER_DIFF)));
      assertTrue(
          newDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(RegionConfig.FieldDefs.REGION_NAME)));
      assertTrue(
          newDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.TERMINATION_PROTECTION_ENABLED)));
    }

    // modified cluster diff
    {
      final ClusterDescription clusterDescription = new ClusterDescription(baseDescDoc);
      final ClusterDescription modifiedClusterDescription =
          clusterDescription
              .copy()
              .setReplicationSpecList(
                  clusterDescription.getReplicationSpecsWithShardData().stream()
                      .map(
                          replicationSpec ->
                              replicationSpec
                                  .copy()
                                  .setRegionConfigs(
                                      replicationSpec.getRegionConfigs().stream()
                                          .map(
                                              regionConfig ->
                                                  regionConfig
                                                      .copy()
                                                      .setRegionName(AWSRegionName.EU_WEST_1)
                                                      .build())
                                          .collect(Collectors.toList()))
                                  .build())
                      .collect(Collectors.toList()))
              .setTerminationProtectionEnabled(true)
              .build();

      final ItemDiff modifiedDiff =
          modifiedClusterDescription.getUpdatedClusterDiff(clusterDescription);
      assertEquals(2, modifiedDiff.getItems().size(), modifiedDiff.getItems().toString());
      assertTrue(
          modifiedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(RegionConfig.FieldDefs.REGION_NAME)));
      assertTrue(
          modifiedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.TERMINATION_PROTECTION_ENABLED)));
    }

    // deleted cluster diff
    {
      final ClusterDescription clusterDescription = new ClusterDescription(baseDescDoc);
      final ItemDiff deletedDiff = clusterDescription.getDeletedClusterDiff();

      // flex should align with serverless style instances in the future, so hide the cluster type
      // and replication spec list
      assertFalse(
          deletedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.CLUSTER_TYPE)));
      assertFalse(
          deletedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.REPLICATION_SPEC_LIST)));
      // Instance size is not hidden in the serverless diff, which is probably a mistake since we
      // don't return it in the public API, it should be hidden in flex
      assertFalse(
          deletedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.INSTANCE_SIZE)));
      // Dedicate auto-scaling is not support on tenant clusters
      assertFalse(
          deletedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(RegionConfig.FieldDefs.AUTO_SCALING)));

      assertTrue(
          deletedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(CLOUD_PROVIDER_DIFF)));
      assertTrue(
          deletedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(RegionConfig.FieldDefs.REGION_NAME)));
      assertTrue(
          deletedDiff.getItems().stream()
              .anyMatch(i -> i.getParamName().equals(FieldDefs.TERMINATION_PROTECTION_ENABLED)));
    }
  }

  @Test
  public void testConstructFromDBObject_flex() {
    final ObjectId groupId = new ObjectId();
    final BasicDBObject baseDescDoc =
        NDSModelTestFactory.getFlexClusterDescription(
            new TestFlexClusterDescriptionConfig().setGroupId(groupId));

    final ClusterDescription flexInstance = new ClusterDescription(baseDescDoc);
    assertTrue(flexInstance.isTenantCluster());
    assertTrue(flexInstance.isFlexTenantCluster());
    assertFalse(flexInstance.isSharedTenantCluster());
    assertFalse(flexInstance.isFreeTenantCluster());
    assertFalse(flexInstance.isServerlessTenantCluster());
    assertTrue(flexInstance.isFlexOrSharedTenantCluster());
    assertEquals(FLEX, flexInstance.getTenantType());

    final BasicDBObject regionConfigDBObject =
        (BasicDBObject)
            getNestedValue(
                baseDescDoc,
                ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST,
                "0",
                ReplicationSpec.FieldDefs.REGION_CONFIGS,
                "0");
    assertEquals(1, flexInstance.getCloudProviders().size());
    assertEquals(
        regionConfigDBObject.getString(RegionConfig.FieldDefs.CLOUD_PROVIDER),
        flexInstance.getCloudProviders().stream().findFirst().orElseThrow().name());

    final BasicDBObject hardwareSpecDBObject =
        (BasicDBObject) regionConfigDBObject.get(RegionConfig.FieldDefs.ELECTABLE_SPECS);
    final FlexHardwareSpec hardwareSpec =
        (FlexHardwareSpec) flexInstance.findFirstHardwareSpecForProvider(FLEX).orElseThrow();
    assertEquals(
        hardwareSpecDBObject.getInt(HardwareSpec.FieldDefs.NODE_COUNT),
        hardwareSpec.getNodeCount());
    assertEquals(1, flexInstance.getInstanceSizes(NodeType.ELECTABLE).size());
    assertEquals(
        hardwareSpecDBObject.getString(FlexHardwareSpec.FieldDefs.INSTANCE_SIZE),
        flexInstance.getInstanceSizes(NodeType.ELECTABLE).stream()
            .findFirst()
            .orElseThrow()
            .name());
    assertEquals(
        hardwareSpecDBObject.getString(FlexHardwareSpec.FieldDefs.PROVIDER_NAME),
        flexInstance.getBackingProvider().name());

    final BasicDBObject cloudProviderOptionsDBObject =
        (BasicDBObject) baseDescDoc.get(ClusterDescription.FieldDefs.CLOUD_PROVIDER_OPTIONS);
    final FlexTenantProviderOptions cloudProviderOptions =
        (FlexTenantProviderOptions) flexInstance.getFlexTenantProviderOptions();

    assertEquals(6, cloudProviderOptionsDBObject.keySet().size());
    assertEquals(
        cloudProviderOptionsDBObject.getInt(FlexTenantProviderOptions.FieldDefs.CONNECTION_LIMIT),
        cloudProviderOptions.getConnectionLimit());
    assertEquals(
        cloudProviderOptionsDBObject.getInt(FlexTenantProviderOptions.FieldDefs.COLLECTION_LIMIT),
        cloudProviderOptions.getCollectionLimit());
    assertEquals(
        cloudProviderOptionsDBObject.getInt(FlexTenantProviderOptions.FieldDefs.DATABASE_LIMIT),
        cloudProviderOptions.getDatabaseLimit());
    assertNotNull(cloudProviderOptions.getNextBackupDate());
    assertEquals(
        cloudProviderOptionsDBObject.getInt(
            FlexTenantProviderOptions.FieldDefs.OPERATIONS_PER_SECOND_LIMIT),
        cloudProviderOptions.getOperationsPerSecondLimit());
    assertEquals(
        cloudProviderOptionsDBObject.getInt(FlexTenantProviderOptions.FieldDefs.DISK_SIZE_GB_LIMIT),
        cloudProviderOptions.getDiskSizeGBLimit());
  }

  @Test
  public void testConstructFromDBObject_ContinuousDelivery() {
    final ObjectId groupId = new ObjectId();
    final BasicDBObject descDoc =
        NDSModelTestFactory.getServerlessClusterDescription(
                new TestServerlessClusterDescriptionConfig().setGroupId(groupId))
            .append(
                FieldDefs.VERSION_RELEASE_SYSTEM,
                ClusterDescription.VersionReleaseSystem.CONTINUOUS);

    final ClusterDescription clusterDescription1 = new ClusterDescription(descDoc);
    assertEquals(
        ClusterDescription.VersionReleaseSystem.CONTINUOUS,
        clusterDescription1.getVersionReleaseSystem());
    assertEquals(Optional.empty(), clusterDescription1.getContinuousDeliveryFCV());

    descDoc.append(FieldDefs.CONTINUOUS_DELIVERY_FCV, "10.1");
    final ClusterDescription clusterDescription2 = new ClusterDescription(descDoc);
    assertEquals(Optional.of("10.1"), clusterDescription2.getContinuousDeliveryFCV());
  }

  @Test
  public void testGetMinShardByInstanceSizeForNodeTypeFamily() {
    final ClusterDescription baseClusterDescription =
        ClusterDescriptionFactory.get(
            NDSModelTestFactory.getAsymmetricShardedClusterDescriptionWithEmbeddedConfigServer(
                new ObjectId(), "cluster1"));
    List<ReplicationSpec> replicationSpecs =
        List.of(
            baseClusterDescription
                .getReplicationSpecsWithShardData()
                .get(0)
                .copy()
                .updateHardwareForProvider(
                    AWS,
                    HardwareSpec.Builder.getBuilderForCloudProvider(AWS)
                        .setInstanceSize(AWSNDSInstanceSize.M10),
                    NodeType.ANALYTICS)
                .build(),
            baseClusterDescription.getReplicationSpecsWithShardData().get(1));
    final ClusterDescription clusterDescription =
        baseClusterDescription.copy().setReplicationSpecList(replicationSpecs).build();

    // M20 is the lowest base and is the second in the replication spec list
    {
      final ReplicationSpec expectedReplicationSpec =
          clusterDescription.getReplicationSpecsWithShardData().get(1);
      final ReplicationSpec minimumReplicationSpec =
          clusterDescription
              .getMinShardByInstanceSizeForNodeTypeFamily(NodeTypeFamily.BASE)
              .orElseThrow();

      assertEquals(expectedReplicationSpec, minimumReplicationSpec);
      assertEquals(AWSNDSInstanceSize.M20, minimumReplicationSpec.getElectableInstanceSize());
    }

    // M10 is the lowest analytics and is the first in the replication spec list
    {
      final ReplicationSpec expectedReplicationSpec =
          clusterDescription.getReplicationSpecsWithShardData().get(0);
      final ReplicationSpec minimumReplicationSpec =
          clusterDescription
              .getMinShardByInstanceSizeForNodeTypeFamily(NodeTypeFamily.ANALYTICS)
              .orElseThrow();

      assertEquals(expectedReplicationSpec, minimumReplicationSpec);
      assertEquals(AWSNDSInstanceSize.M10, minimumReplicationSpec.getAnalyticsInstanceSize());
    }
  }

  @Test
  public void testNeedsMongoDBConfigPublishRestartAllowed() {
    // needsMongoDBConfigPublishRestartAllowed not present on dbObject
    final BasicDBObject clusterDescriptionObj1 = NDSModelTestFactory.getAWSClusterDescription();
    final ClusterDescription clusterDescription1 = new ClusterDescription(clusterDescriptionObj1);
    assertEquals(
        ClusterDescription.ProcessRestartAllowedState.NONE,
        clusterDescription1.getNeedsMongoDBConfigPublishRestartAllowed());
    assertTrue(
        clusterDescription1
            .toDBObject()
            .containsField(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED));

    // needsMongoDBConfigPublishRestartAllowed: IMMEDIATE set on dbObject
    final BasicDBObject clusterDescriptionObj3 =
        NDSModelTestFactory.getAWSClusterDescription()
            .append(
                FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
                ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.name());
    final ClusterDescription clusterDescription3 = new ClusterDescription(clusterDescriptionObj3);
    assertEquals(
        ClusterDescription.ProcessRestartAllowedState.IMMEDIATE,
        clusterDescription3.getNeedsMongoDBConfigPublishRestartAllowed());
    final BasicDBObject toDBObjectResult3 = clusterDescription3.toDBObject();
    assertTrue(
        toDBObjectResult3.containsField(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED));
    assertEquals(
        ClusterDescription.ProcessRestartAllowedState.IMMEDIATE.name(),
        toDBObjectResult3.get(FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED));
  }

  @Test
  public void testIsCNRegionsOnlyCluster() throws SvcException {
    final ClusterDescription cnRegionsOnlyClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getClusterDescription(
                NDSDefaults.CLUSTER_NAME,
                CloudProvider.AWS,
                3,
                List.of(AWSRegionName.CN_NORTH_1, AWSRegionName.CN_NORTHWEST_1)));
    assertTrue(cnRegionsOnlyClusterDescription.isCNRegionsOnlyCluster());

    final ClusterDescription nonCNRegionsOnlyClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getClusterDescription(
                NDSDefaults.CLUSTER_NAME,
                CloudProvider.AWS,
                3,
                List.of(AWSRegionName.US_EAST_1, AWSRegionName.US_EAST_2)));
    assertFalse(nonCNRegionsOnlyClusterDescription.isCNRegionsOnlyCluster());

    final ClusterDescription mixedRegionsClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getClusterDescription(
                NDSDefaults.CLUSTER_NAME,
                CloudProvider.AWS,
                3,
                List.of(AWSRegionName.US_EAST_1, AWSRegionName.CN_NORTH_1)));
    try {
      mixedRegionsClusterDescription.isCNRegionsOnlyCluster();
      fail("Expected an IllegalStateException");
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.MIXED_CN_STANDARD_REGIONS, pE.getErrorCode());
      assertEquals("A cluster cannot have both China and non-China regions.", pE.getMessage());
    }
  }

  @Test
  public void testIsGovRegionsOnlyCluster() throws SvcException {
    final ClusterDescription govRegionsOnlyClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getClusterDescription(
                NDSDefaults.CLUSTER_NAME,
                CloudProvider.AWS,
                3,
                List.of(AWSRegionName.US_GOV_WEST_1, AWSRegionName.US_GOV_EAST_1)));
    assertTrue(govRegionsOnlyClusterDescription.isGovRegionsOnlyCluster());

    final ClusterDescription nonGovRegionsOnlyClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getClusterDescription(
                NDSDefaults.CLUSTER_NAME,
                CloudProvider.AWS,
                3,
                List.of(AWSRegionName.US_EAST_1, AWSRegionName.US_EAST_2)));
    assertFalse(nonGovRegionsOnlyClusterDescription.isGovRegionsOnlyCluster());
    final ClusterDescription mixedRegionsClusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getClusterDescription(
                NDSDefaults.CLUSTER_NAME,
                CloudProvider.AWS,
                3,
                List.of(AWSRegionName.US_EAST_1, AWSRegionName.US_GOV_WEST_1)));
    try {
      mixedRegionsClusterDescription.isGovRegionsOnlyCluster();
      fail("Expected an IllegalStateException");
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS, pE.getErrorCode());
      assertEquals("A cluster cannot have both GovCloud and Commercial regions.", pE.getMessage());
    }

    // GCP
    assertFalse(
        new ClusterDescription(
                NDSModelTestFactory.getClusterDescription(
                    NDSDefaults.CLUSTER_NAME,
                    CloudProvider.GCP,
                    3,
                    List.of(GCPRegionName.EASTERN_US)))
            .isGovRegionsOnlyCluster());

    assertTrue(
        new ClusterDescription(
                NDSModelTestFactory.getClusterDescription(
                    NDSDefaults.CLUSTER_NAME,
                    CloudProvider.GCP,
                    3,
                    List.of(GCPRegionName.EASTERN_US_AW)))
            .isGovRegionsOnlyCluster());

    try {
      new ClusterDescription(
              NDSModelTestFactory.getClusterDescription(
                  NDSDefaults.CLUSTER_NAME,
                  CloudProvider.GCP,
                  3,
                  List.of(GCPRegionName.EASTERN_US_AW, GCPRegionName.EASTERN_US)))
          .isGovRegionsOnlyCluster();
      fail("Expected SvcException");
    } catch (final SvcException pE) {
      assertEquals(NDSErrorCode.MIXED_GOVCLOUD_COMMERCIAL_REGIONS, pE.getErrorCode());
    }
  }

  @Test
  public void testRootCertType() {
    // rootCertType field not present on dbObject
    final BasicDBObject clusterDescriptionObj1 = NDSModelTestFactory.getAWSClusterDescription();
    final ClusterDescription clusterDescription1 = new ClusterDescription(clusterDescriptionObj1);
    assertEquals(RootCertType.ISRGROOTX1, clusterDescription1.getRootCertType());
    assertTrue(clusterDescription1.toDBObject().containsField(FieldDefs.ROOT_CERT_TYPE));

    // rootCertType: DST set on dbObject
    final BasicDBObject clusterDescriptionObj3 =
        NDSModelTestFactory.getAWSClusterDescription()
            .append(FieldDefs.ROOT_CERT_TYPE, RootCertType.DST.name());
    final ClusterDescription clusterDescription3 = new ClusterDescription(clusterDescriptionObj3);
    assertEquals(RootCertType.DST, clusterDescription3.getRootCertType());
    final BasicDBObject toDBObjectResult3 = clusterDescription3.toDBObject();
    assertTrue(toDBObjectResult3.containsField(FieldDefs.ROOT_CERT_TYPE));
    assertEquals(RootCertType.DST.name(), toDBObjectResult3.get(FieldDefs.ROOT_CERT_TYPE));
  }

  private void assertHardwareSpecsMatch(final HardwareSpec pSpec1, final HardwareSpec pSpec2) {
    assertTrue(
        pSpec1.getNodeCount() == pSpec2.getNodeCount()
            && pSpec1.getInstanceSize() == pSpec2.getInstanceSize());
  }

  private void assertHardwareSpecsDoNotMatch(final HardwareSpec pSpec1, final HardwareSpec pSpec2) {
    assertFalse(
        pSpec1.getNodeCount() == pSpec2.getNodeCount()
            && pSpec1.getInstanceSize() == pSpec2.getInstanceSize());
  }

  private void assertHardwareSpecMatchesClusterDescription(
      final ClusterDescription pClusterDescription,
      final HardwareSpec pHardwareSpec,
      final EnumSet<NodeTypeFamily> pFamiliesToMatch) {
    pClusterDescription.getReplicationSpecsWithShardData().stream()
        .flatMap(replicationSpec -> replicationSpec.getRegionConfigs().stream())
        .forEach(
            regionConfig -> {
              assertTrue(regionConfig instanceof ShardRegionConfig);
              if (pFamiliesToMatch.contains(NodeTypeFamily.BASE)) {
                regionConfig
                    .getBaseHardwareSpecs()
                    .forEach(spec -> assertHardwareSpecsMatch(spec, pHardwareSpec));
              } else {
                regionConfig
                    .getBaseHardwareSpecs()
                    .forEach(spec -> assertHardwareSpecsDoNotMatch(spec, pHardwareSpec));
              }
              if (pFamiliesToMatch.contains(NodeTypeFamily.ANALYTICS)) {
                assertHardwareSpecsMatch(regionConfig.getAnalyticsSpecs(), pHardwareSpec);
              } else {
                assertHardwareSpecsDoNotMatch(regionConfig.getAnalyticsSpecs(), pHardwareSpec);
              }
            });
  }

  @Test
  public void testUpdateAllHardware() {
    final ClusterDescription awsClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ClusterDescription updatedClusterDescription =
        awsClusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardware(_testHardwareSpec.copy())
            .build();

    assertHardwareSpecMatchesClusterDescription(
        updatedClusterDescription,
        _testHardwareSpec,
        EnumSet.of(NodeTypeFamily.BASE, NodeTypeFamily.ANALYTICS));
  }

  @Test
  public void testUpdateBaseHardware() {
    final ClusterDescription awsClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ClusterDescription updatedClusterDescription =
        awsClusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(_testHardwareSpec.copy(), NodeTypeFamily.BASE)
            .build();

    assertHardwareSpecMatchesClusterDescription(
        updatedClusterDescription, _testHardwareSpec, EnumSet.of(NodeTypeFamily.BASE));
  }

  @Test
  public void testUpdateAnalyticsHardware() {
    final ClusterDescription awsClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ClusterDescription updatedClusterDescription =
        awsClusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardware(_testHardwareSpec.copy(), NodeTypeFamily.ANALYTICS)
            .build();

    assertHardwareSpecMatchesClusterDescription(
        updatedClusterDescription, _testHardwareSpec, EnumSet.of(NodeTypeFamily.ANALYTICS));
  }

  @Test
  public void testUpdateAllHardwareForProvider() {
    final ClusterDescription awsClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ClusterDescription nonUpdatedClusterDescription =
        awsClusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardwareForProvider(CloudProvider.GCP, _testHardwareSpec.copy())
            .build();
    final ClusterDescription updatedClusterDescription =
        awsClusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateAllHardwareForProvider(CloudProvider.AWS, _testHardwareSpec.copy())
            .build();

    assertHardwareSpecMatchesClusterDescription(
        nonUpdatedClusterDescription, _testHardwareSpec, EnumSet.noneOf(NodeTypeFamily.class));
    assertHardwareSpecMatchesClusterDescription(
        updatedClusterDescription,
        _testHardwareSpec,
        EnumSet.of(NodeTypeFamily.BASE, NodeTypeFamily.ANALYTICS));
  }

  @Test
  public void testUpdateBaseHardwareForProvider() {
    final ClusterDescription awsClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    final ClusterDescription nonUpdatedClusterDescription =
        awsClusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardwareForProvider(
                CloudProvider.GCP, _testHardwareSpec.copy(), NodeTypeFamily.BASE)
            .build();
    final ClusterDescription updatedClusterDescription =
        awsClusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardwareForProvider(
                CloudProvider.AWS, _testHardwareSpec.copy(), NodeTypeFamily.BASE)
            .build();

    assertHardwareSpecMatchesClusterDescription(
        nonUpdatedClusterDescription, _testHardwareSpec, EnumSet.noneOf(NodeTypeFamily.class));
    assertHardwareSpecMatchesClusterDescription(
        updatedClusterDescription, _testHardwareSpec, EnumSet.of(NodeTypeFamily.BASE));
  }

  @Test
  public void testUpdateAnalyticsHardwareForProvider() {
    final ClusterDescription awsClusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);

    final ClusterDescription nonUpdatedClusterDescription =
        awsClusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardwareForProvider(
                CloudProvider.GCP, _testHardwareSpec.copy(), NodeTypeFamily.ANALYTICS)
            .build();
    final ClusterDescription updatedClusterDescription =
        awsClusterDescription
            .copy()
            .mixin(ClusterDescriptionBuilderTestMixin::new)
            .updateHardwareForProvider(
                CloudProvider.AWS, _testHardwareSpec.copy(), NodeTypeFamily.ANALYTICS)
            .build();

    assertHardwareSpecMatchesClusterDescription(
        nonUpdatedClusterDescription, _testHardwareSpec, EnumSet.noneOf(NodeTypeFamily.class));
    assertHardwareSpecMatchesClusterDescription(
        updatedClusterDescription, _testHardwareSpec, EnumSet.of(NodeTypeFamily.ANALYTICS));
  }

  @Test
  public void testGetHardwareSpec_byReplicationSpecId() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doCallRealMethod().when(clusterDescription).getHardwareSpec(any(), any(), any());

    final ObjectId replicationSpecId = oid(1);

    // When replication spec is not found, returns empty Optional
    doReturn(Optional.empty())
        .when(clusterDescription)
        .getReplicationSpecById(eq(replicationSpecId));
    assertEquals(
        Optional.empty(),
        clusterDescription.getHardwareSpec(
            replicationSpecId, AWSRegionName.US_EAST_1, NodeType.ELECTABLE));

    final ReplicationSpec replicationSpec = mock(ReplicationSpec.class);
    doReturn(Optional.of(replicationSpec))
        .when(clusterDescription)
        .getReplicationSpecById(eq(replicationSpecId));

    // When region config is not found, returns empty Optional
    doReturn(Optional.empty())
        .when(replicationSpec)
        .getRegionConfigByRegion(eq(AWSRegionName.US_EAST_1));
    assertEquals(
        Optional.empty(),
        clusterDescription.getHardwareSpec(
            replicationSpecId, AWSRegionName.US_EAST_1, NodeType.ELECTABLE));

    final RegionConfig regionConfig = mock(RegionConfig.class);
    doReturn(Optional.of(regionConfig))
        .when(replicationSpec)
        .getRegionConfigByRegion(eq(AWSRegionName.US_EAST_1));

    // When hardware spec is not found, returns empty Optional
    doReturn(null).when(regionConfig).getHardwareSpecByNodeType(eq(NodeType.ELECTABLE));
    assertEquals(
        Optional.empty(),
        clusterDescription.getHardwareSpec(
            replicationSpecId, AWSRegionName.US_EAST_1, NodeType.ELECTABLE));

    // When hardware spec is found, returns Optional with that hardware spec
    final HardwareSpec hardwareSpec = mock(HardwareSpec.class);
    doReturn(hardwareSpec).when(regionConfig).getHardwareSpecByNodeType(eq(NodeType.ELECTABLE));
    assertEquals(
        Optional.of(hardwareSpec),
        clusterDescription.getHardwareSpec(
            replicationSpecId, AWSRegionName.US_EAST_1, NodeType.ELECTABLE));
  }

  @Test
  public void testGetInstanceSizeForProvider() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final CloudProvider cloudProvider = mock(CloudProvider.class);
    final ShardRegionConfig regionConfig = mock(ShardRegionConfig.class);
    final ReplicationSpec replicationSpec = mock(ReplicationSpec.class);
    final HardwareSpec baseHardwareSpec = mock(HardwareSpec.class);
    final HardwareSpec analyticsHardwareSpec = mock(HardwareSpec.class);
    final HardwareSpec hiddenSecondaryHardwareSpec = mock(HardwareSpec.class);
    final HardwareSpec readOnlyHardwareSpec = mock(HardwareSpec.class);
    final InstanceSize baseInstanceSize = mock(NDSInstanceSize.class);
    final InstanceSize analyticsInstanceSize = mock(NDSInstanceSize.class);

    doReturn(baseInstanceSize).when(baseHardwareSpec).getInstanceSize();
    doReturn(1).when(baseHardwareSpec).getNodeCount();
    doReturn(analyticsInstanceSize).when(analyticsHardwareSpec).getInstanceSize();
    doReturn(1).when(analyticsHardwareSpec).getNodeCount();
    doReturn(baseInstanceSize).when(readOnlyHardwareSpec).getInstanceSize();
    doReturn(1).when(readOnlyHardwareSpec).getNodeCount();
    doReturn(baseInstanceSize).when(hiddenSecondaryHardwareSpec).getInstanceSize();
    doReturn(0).when(hiddenSecondaryHardwareSpec).getNodeCount();
    doCallRealMethod().when(regionConfig).getHardwareSpecByNodeType(any());
    doReturn(cloudProvider).when(regionConfig).getCloudProvider();

    doReturn(List.of(replicationSpec)).when(clusterDescription).getReplicationSpecsWithShardData();
    doReturn(List.of(regionConfig)).when(replicationSpec).getRegionConfigs();
    doReturn(baseHardwareSpec).when(regionConfig).getElectableSpecs();
    doReturn(analyticsHardwareSpec).when(regionConfig).getAnalyticsSpecs();
    doReturn(hiddenSecondaryHardwareSpec).when(regionConfig).getHiddenSecondarySpecs();
    doReturn(readOnlyHardwareSpec).when(regionConfig).getReadOnlySpecs();

    doCallRealMethod().when(clusterDescription).getOnlyHardwareSpecForProvider(any(), any());
    doCallRealMethod().when(clusterDescription).getHardwareSpecsForProvider(any(), any());
    doCallRealMethod()
        .when(clusterDescription)
        .getInstanceSizesForProviderAndNodeType(any(), any());
    doCallRealMethod()
        .when(clusterDescription)
        .getOnlyInstanceSizeForProvider(any(), ArgumentMatchers.<NodeType>any());

    for (final NodeType nodeType : List.of(NodeType.ELECTABLE, NodeType.READ_ONLY)) {
      assertEquals(
          Optional.of(baseInstanceSize),
          clusterDescription.getOnlyInstanceSizeForProvider(cloudProvider, nodeType));
    }

    // No value here because we didn't set nodetype to $gt 1
    assertEquals(
        Optional.empty(),
        clusterDescription.getOnlyInstanceSizeForProvider(
            cloudProvider, NodeType.HIDDEN_SECONDARY));

    assertEquals(
        Optional.of(analyticsInstanceSize),
        clusterDescription.getOnlyInstanceSizeForProvider(cloudProvider, NodeType.ANALYTICS));
  }

  @Test
  public void testIsFullyNVME() {
    final ObjectId groupId = new ObjectId();
    final ClusterDescription clusterDescription0 =
        new ClusterDescription(NDSModelTestFactory.getAWSNVMeClusterDescription(groupId));
    assertTrue(clusterDescription0.isFullyNVMe());

    final ReplicationSpec rs = clusterDescription0.getReplicationSpecsWithShardData().get(0);
    final RegionConfig rc = rs.getHighestPriorityRegionConfig();
    final ClusterDescription newCD =
        clusterDescription0
            .copy()
            .setReplicationSpecList(
                List.of(
                    rs.copy()
                        .updateHardwareForProvider(
                            AWS, rc.getAnalyticsSpecs().copy().setNodeCount(1), NodeType.ANALYTICS)
                        .updateHardwareForProvider(
                            AWS, rc.getReadOnlySpecs().copy().setNodeCount(1), NodeType.READ_ONLY)
                        .build()))
            .build();
    assertTrue(newCD.isFullyNVMe());
  }

  @Test
  public void testGetInstanceFamily() {
    final NDSInstanceSize baseInstanceSize = AWSNDSInstanceSize.R40;
    final NDSInstanceSize analyticsInstanceSize = AWSNDSInstanceSize.M10;
    final RegionName regionName = AWSRegionName.US_EAST_1;
    final List<RegionConfig> regionConfigs =
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSRegionName.US_EAST_1,
                baseInstanceSize,
                RegionSpec.MAX_PRIORITY,
                3,
                2,
                analyticsInstanceSize,
                1,
                0,
                false));
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getDefaultClusterDescription(
            new ObjectId(), "asymmetricAnalytics", regionConfigs);

    final VersionUtils.Version version = clusterDescription.getMongoDBVersion();

    final Function<NDSInstanceSize, InstanceFamily> findExpectedInstanceFamily =
        (instanceSize) ->
            instanceSize.getAvailableFamilies().get(regionName).stream()
                .filter(
                    family -> {
                      if (family == AWSInstanceFamily.M8G
                          || family == AWSInstanceFamily.R8G
                          || family == AWSInstanceFamily.I8G) {
                        return version.supportsArmv9();
                      }
                      return true;
                    })
                .reduce((first, second) -> second) // get the last element
                .orElse(null);
    final InstanceFamily expectedBaseInstanceFamily =
        findExpectedInstanceFamily.apply(baseInstanceSize);
    final InstanceFamily expectedAnalyticsInstanceFamily =
        findExpectedInstanceFamily.apply(analyticsInstanceSize);

    for (final NodeType nodeType :
        List.of(NodeType.ELECTABLE, NodeType.HIDDEN_SECONDARY, NodeType.READ_ONLY)) {
      assertEquals(
          baseInstanceSize,
          clusterDescription
              .getReplicationSpecsWithShardData()
              .get(0)
              .getRegionConfigs()
              .get(0)
              .getHardwareSpecByNodeType(nodeType)
              .getInstanceSize());
      assertEquals(expectedBaseInstanceFamily, clusterDescription.getInstanceFamily(nodeType));
    }

    assertEquals(
        Set.of(analyticsInstanceSize), clusterDescription.getInstanceSizes(NodeType.ANALYTICS));
    assertEquals(
        expectedAnalyticsInstanceFamily, clusterDescription.getInstanceFamily(NodeType.ANALYTICS));
  }

  @Test
  public void testGetOS() {
    final ClusterDescription clusterDescription =
        spy(NDSModelTestFactory.getClusterDescription(AWS));
    final ObjectId replicationSpecId = new ObjectId();

    // When hardware spec is not found, throws IllegalArgumentException
    doReturn(Optional.empty())
        .when(clusterDescription)
        .getHardwareSpec(
            eq(replicationSpecId), eq(AWSRegionName.US_EAST_1), eq(NodeType.ELECTABLE));
    assertThrows(
        IllegalArgumentException.class,
        () ->
            clusterDescription.getOS(
                replicationSpecId, AWSRegionName.US_EAST_1, NodeType.ELECTABLE));

    // When hardware spec is found, returns OS set on the hardware spec
    final HardwareSpec hardwareSpec = mock(HardwareSpec.class);
    doReturn(Optional.of(hardwareSpec))
        .when(clusterDescription)
        .getHardwareSpec(
            eq(replicationSpecId), eq(AWSRegionName.US_EAST_1), eq(NodeType.ELECTABLE));

    doReturn(OS.AL2).when(hardwareSpec).getOS();
    assertEquals(
        OS.AL2,
        clusterDescription.getOS(replicationSpecId, AWSRegionName.US_EAST_1, NodeType.ELECTABLE));
  }

  @Test
  public void testUsesOS() {
    // Defaults to AL2023, and 3 electable nodes
    final ClusterDescription clusterDescription = NDSModelTestFactory.getClusterDescription(AWS);

    assertTrue(clusterDescription.usesOs(OS.AL2023));

    // Set AL2 on electable nodes - cluster has no analytics nodes
    final var rsList = clusterDescription.getAllReplicationSpecsIncludingConfig();
    final var electableBuilder = rsList.get(0).getElectableSpec().copy();
    final var al2ElectableSpec =
        rsList
            .get(0)
            .copy()
            .updateHardware(electableBuilder.setOS(OS.AL2), NodeType.ELECTABLE)
            .build();

    final var al2023Cluster =
        clusterDescription.copy().setReplicationSpecList(List.of(al2ElectableSpec)).build();
    assertTrue(al2023Cluster.usesOs(OS.AL2));
    assertFalse(al2023Cluster.usesOs(OS.AL2023));

    // Set 1 analytics node that uses AL2 - now usesOs should return true for both AL2 and AL2023
    final var analyticsBuilder = rsList.get(0).getAnalyticsSpec().copy();
    final var al2023andAL2ReplicationSpec =
        al2ElectableSpec
            .copy()
            .updateHardware(analyticsBuilder.setOS(OS.AL2023).setNodeCount(1), NodeType.ANALYTICS)
            .build();
    final var mixedOSCluster =
        clusterDescription
            .copy()
            .setReplicationSpecList(List.of(al2023andAL2ReplicationSpec))
            .build();
    assertTrue(mixedOSCluster.usesOs(OS.AL2));
    assertTrue(mixedOSCluster.usesOs(OS.AL2023));
  }

  @Test
  public void testGetInstanceFamilyForProvider() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final CloudProvider cloudProvider = mock(CloudProvider.class);
    final ShardRegionConfig regionConfig = mock(ShardRegionConfig.class);
    final HardwareSpec baseHardwareSpec = mock(HardwareSpec.class);
    final HardwareSpec analyticsHardwareSpec = mock(HardwareSpec.class);
    final HardwareSpec readOnlyHardwareSpec = mock(HardwareSpec.class);
    final HardwareSpec hiddenSecondarySpec = mock(HardwareSpec.class);
    final InstanceFamily baseInstanceFamily = DummyInstanceFamily.M5;
    final InstanceFamily analyticsInstanceFamily = DummyInstanceFamily.R5;

    doReturn(baseInstanceFamily).when(baseHardwareSpec).getInstanceFamily();
    doReturn(baseInstanceFamily).when(readOnlyHardwareSpec).getInstanceFamily();
    doReturn(baseInstanceFamily).when(hiddenSecondarySpec).getInstanceFamily();
    doReturn(analyticsInstanceFamily).when(analyticsHardwareSpec).getInstanceFamily();

    doReturn(cloudProvider).when(regionConfig).getCloudProvider();
    doReturn(List.of(regionConfig)).when(clusterDescription).getAllRegionConfigs();

    doReturn(baseHardwareSpec).when(regionConfig).getElectableSpecs();
    doReturn(analyticsHardwareSpec).when(regionConfig).getAnalyticsSpecs();
    doReturn(readOnlyHardwareSpec).when(regionConfig).getReadOnlySpecs();
    doReturn(hiddenSecondarySpec).when(regionConfig).getHiddenSecondarySpecs();
    doCallRealMethod().when(regionConfig).getHardwareSpecByNodeType(any());

    doCallRealMethod().when(clusterDescription).getOnlyHardwareSpecForProvider(any(), any());
    doCallRealMethod().when(clusterDescription).getHardwareSpecsForProvider(any(), any());
    doCallRealMethod().when(clusterDescription).getInstanceFamiliesForProvider(any(), any());
    doCallRealMethod().when(clusterDescription).getOnlyInstanceFamilyForProvider(any(), any());

    for (final NodeType nodeType :
        List.of(NodeType.ELECTABLE, NodeType.HIDDEN_SECONDARY, NodeType.READ_ONLY)) {
      assertEquals(
          Optional.of(baseInstanceFamily),
          clusterDescription.getOnlyInstanceFamilyForProvider(cloudProvider, nodeType));
    }

    assertEquals(
        Optional.of(analyticsInstanceFamily),
        clusterDescription.getOnlyInstanceFamilyForProvider(cloudProvider, NodeType.ANALYTICS));
  }

  @Test
  public void testIsNVMe() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final InstanceSize baseInstanceSize = mock(NDSInstanceSize.class);
    final InstanceSize analyticsInstanceSize = mock(NDSInstanceSize.class);
    final boolean baseIsNVMe = false;
    final boolean analyticsIsNVMe = true;

    doReturn(baseIsNVMe).when(baseInstanceSize).isNVMe();
    doReturn(analyticsIsNVMe).when(analyticsInstanceSize).isNVMe();

    doCallRealMethod().when(clusterDescription).getInstanceSizes(any());
    doCallRealMethod().when(clusterDescription).getInstanceSizes(any());
    doCallRealMethod().when(clusterDescription).isNVMe(any());

    for (final NodeType nodeType :
        List.of(NodeType.ELECTABLE, NodeType.HIDDEN_SECONDARY, NodeType.READ_ONLY)) {
      doReturn(Set.of(baseInstanceSize)).when(clusterDescription).getInstanceSizes(nodeType);
      assertEquals(baseIsNVMe, clusterDescription.isNVMe(nodeType));
    }

    doReturn(Set.of(analyticsInstanceSize))
        .when(clusterDescription)
        .getInstanceSizes(NodeType.ANALYTICS);
    assertEquals(analyticsIsNVMe, clusterDescription.isNVMe(NodeType.ANALYTICS));
  }

  @Test
  public void testGetDiskIOPS() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final HardwareSpec baseHardwareSpec = mock(HardwareSpec.class);
    final HardwareSpec analyticsHardwareSpec = mock(HardwareSpec.class);
    final int baseIOPS = 10;
    final int analyticsIOPS = 12;

    doReturn(baseIOPS).when(baseHardwareSpec).getDiskIOPS(anyDouble());
    doReturn(analyticsIOPS).when(analyticsHardwareSpec).getDiskIOPS(anyDouble());

    doReturn(analyticsHardwareSpec)
        .when(clusterDescription)
        .getHardwareSpec(eq(NodeType.ANALYTICS));

    doCallRealMethod().when(clusterDescription).getDiskIOPS(any());

    for (final NodeType nodeType :
        List.of(NodeType.ELECTABLE, NodeType.HIDDEN_SECONDARY, NodeType.READ_ONLY)) {
      doReturn(baseHardwareSpec).when(clusterDescription).getHardwareSpec(eq(nodeType));
      assertEquals(baseIOPS, clusterDescription.getDiskIOPS(nodeType));
    }

    assertEquals(analyticsIOPS, clusterDescription.getDiskIOPS(NodeType.ANALYTICS));
  }

  @Test
  public void testGetInstanceSizeMaxNormalizedCPUUsage_multiInstanceFamilies() {
    // test GetInstanceSizeMaxNormalizedCPUUsage can handle cluster uses different instance families
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    doReturn(AutoScalingMode.CLUSTER).when(clusterDescription).getAutoScalingMode();

    doReturn(Set.of(AZURE)).when(clusterDescription).getCloudProvidersForNodeType(any());

    final NDSInstanceSize instanceSize = mock(NDSInstanceSize.class);
    doReturn(AZURE).when(instanceSize).getCloudProvider();

    doReturn(Optional.of(instanceSize))
        .when(clusterDescription)
        .getOnlyInstanceSizeForProvider(eq(AZURE), eq(NodeType.ELECTABLE));

    doReturn(Set.of(AzureInstanceFamily.STANDARD_DSV2, AzureInstanceFamily.STANDARD_B))
        .when(clusterDescription)
        .getInstanceFamiliesForProvider(eq(AZURE), eq(NodeType.ELECTABLE));

    final float dsv2MaxNormalizedCPUUsage = 0.123f;
    final float standardBMaxNormalizedCPUUsage = 0.234f;

    doReturn(dsv2MaxNormalizedCPUUsage)
        .when(instanceSize)
        .getMaxNormalizedCPUUsage(eq(AzureInstanceFamily.STANDARD_DSV2));

    doReturn(standardBMaxNormalizedCPUUsage)
        .when(instanceSize)
        .getMaxNormalizedCPUUsage(eq(AzureInstanceFamily.STANDARD_B));

    doCallRealMethod().when(clusterDescription).getInstanceSizeMaxNormalizedCPUUsage(any(), any());
    assertEquals(
        dsv2MaxNormalizedCPUUsage, // expect lower value
        clusterDescription.getInstanceSizeMaxNormalizedCPUUsage(
            Optional.empty(), NodeTypeFamily.BASE),
        0.001);
  }

  @Test
  public void testGetInstanceSizeMaxNormalizedCPUUsage() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final float baseInstanceSizeMaxNormalizedCPUUsage = 0.01234f;
    final float analyticsInstanceSizeMaxNormalizedCPUUsage = 0.12378f;
    final InstanceSize baseInstanceSize = mock(NDSInstanceSize.class);
    final InstanceFamily baseInstanceFamily = AWSInstanceFamily.M6G;
    final InstanceSize analyticsInstanceSize = mock(NDSInstanceSize.class);
    final InstanceFamily analyticsInstanceFamily = AWSInstanceFamily.I3;

    doReturn(Set.of(CloudProvider.AWS))
        .when(clusterDescription)
        .getCloudProvidersForNodeType(any());
    doReturn(CloudProvider.AWS).when(baseInstanceSize).getCloudProvider();
    doReturn(baseInstanceSizeMaxNormalizedCPUUsage)
        .when(baseInstanceSize)
        .getMaxNormalizedCPUUsage(eq(baseInstanceFamily));

    doReturn(Optional.of(analyticsInstanceSize))
        .when(clusterDescription)
        .getOnlyInstanceSizeForProvider(eq(CloudProvider.AWS), eq(NodeType.ANALYTICS));
    doReturn(CloudProvider.AWS).when(analyticsInstanceSize).getCloudProvider();
    doReturn(Set.of(analyticsInstanceFamily))
        .when(clusterDescription)
        .getInstanceFamiliesForProvider(eq(CloudProvider.AWS), eq(NodeType.ANALYTICS));
    doReturn(analyticsInstanceSizeMaxNormalizedCPUUsage)
        .when(analyticsInstanceSize)
        .getMaxNormalizedCPUUsage(eq(analyticsInstanceFamily));

    doCallRealMethod().when(clusterDescription).getInstanceSizeMaxNormalizedCPUUsage(any(), any());
    doReturn(AutoScalingMode.CLUSTER).when(clusterDescription).getAutoScalingMode();

    final NodeType[] electableNodeTypes =
        new NodeType[] {NodeType.ELECTABLE, NodeType.HIDDEN_SECONDARY, NodeType.READ_ONLY, null};
    for (final NodeType nodeType : electableNodeTypes) {
      doReturn(Optional.of(baseInstanceSize))
          .when(clusterDescription)
          .getOnlyInstanceSizeForProvider(eq(CloudProvider.AWS), eq(nodeType));
      doReturn(Set.of(baseInstanceFamily))
          .when(clusterDescription)
          .getInstanceFamiliesForProvider(eq(CloudProvider.AWS), eq(nodeType));
    }

    assertEquals(
        baseInstanceSizeMaxNormalizedCPUUsage,
        clusterDescription.getInstanceSizeMaxNormalizedCPUUsage(
            Optional.empty(), NodeTypeFamily.BASE),
        0.001);
    assertEquals(
        analyticsInstanceSizeMaxNormalizedCPUUsage,
        clusterDescription.getInstanceSizeMaxNormalizedCPUUsage(
            Optional.empty(), NodeTypeFamily.ANALYTICS),
        0.001);
  }

  @Test
  public void testGetInstanceSizeMaxNormalizedCPUUsage_replicationSpec() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final float baseInstanceSizeMaxNormalizedCPUUsage = 0.01234f;
    final float analyticsInstanceSizeMaxNormalizedCPUUsage = 0.12378f;
    final InstanceSize baseInstanceSize = mock(NDSInstanceSize.class);
    final InstanceFamily baseInstanceFamily = AWSInstanceFamily.M6G;
    final InstanceSize analyticsInstanceSize = mock(NDSInstanceSize.class);
    final InstanceFamily analyticsInstanceFamily = AWSInstanceFamily.I3;
    final ReplicationSpec replicationSpec = mock(ReplicationSpec.class);

    doReturn(Set.of(CloudProvider.AWS)).when(replicationSpec).getCloudProviders();
    doReturn(CloudProvider.AWS).when(baseInstanceSize).getCloudProvider();
    doReturn(baseInstanceSizeMaxNormalizedCPUUsage)
        .when(baseInstanceSize)
        .getMaxNormalizedCPUUsage(eq(baseInstanceFamily));
    doReturn(Optional.of(replicationSpec)).when(clusterDescription).getReplicationSpecById(any());

    doReturn(analyticsInstanceSize)
        .when(replicationSpec)
        .getInstanceSizeByProviderAndNodeType(eq(CloudProvider.AWS), eq(NodeType.ANALYTICS));
    doReturn(CloudProvider.AWS).when(analyticsInstanceSize).getCloudProvider();
    doReturn(analyticsInstanceFamily)
        .when(replicationSpec)
        .getInstanceFamilyByProviderAndNodeType(eq(CloudProvider.AWS), eq(NodeType.ANALYTICS));
    doReturn(analyticsInstanceSizeMaxNormalizedCPUUsage)
        .when(analyticsInstanceSize)
        .getMaxNormalizedCPUUsage(eq(analyticsInstanceFamily));

    doCallRealMethod().when(clusterDescription).getInstanceSizeMaxNormalizedCPUUsage(any(), any());

    final NodeType[] electableNodeTypes =
        new NodeType[] {NodeType.ELECTABLE, NodeType.HIDDEN_SECONDARY, NodeType.READ_ONLY, null};
    for (final NodeType nodeType : electableNodeTypes) {
      doReturn(baseInstanceSize)
          .when(replicationSpec)
          .getInstanceSizeByProviderAndNodeType(eq(CloudProvider.AWS), eq(nodeType));
      doReturn(baseInstanceFamily)
          .when(replicationSpec)
          .getInstanceFamilyByProviderAndNodeType(eq(CloudProvider.AWS), eq(nodeType));
    }

    assertEquals(
        baseInstanceSizeMaxNormalizedCPUUsage,
        clusterDescription.getInstanceSizeMaxNormalizedCPUUsage(
            Optional.of(new ObjectId()), NodeTypeFamily.BASE),
        0.001);
    assertEquals(
        analyticsInstanceSizeMaxNormalizedCPUUsage,
        clusterDescription.getInstanceSizeMaxNormalizedCPUUsage(
            Optional.of(new ObjectId()), NodeTypeFamily.ANALYTICS),
        0.001);
  }

  @Test
  public void testGetInstanceSizeMaxNormalizedCPUUsage_geoshardedCluster() {
    final ObjectId replicationSpecId = new ObjectId();
    final ReplicationSpec spyReplicationSpec =
        spy(
            new ReplicationSpec(
                replicationSpecId,
                new ObjectId(),
                new ObjectId(),
                "zone1",
                1,
                NDSModelTestFactory.getRegionConfigs(
                    5, CloudProvider.AWS, List.of(AWSRegionName.US_EAST_1))));
    final List<ReplicationSpec> replicationSpecs =
        List.of(
            spyReplicationSpec,
            new ReplicationSpec(
                new ObjectId(),
                new ObjectId(),
                new ObjectId(),
                "zone2",
                1,
                NDSModelTestFactory.getRegionConfigs(
                    5,
                    CloudProvider.AZURE,
                    List.of(
                        AzureRegionName.AUSTRALIA_EAST, AzureRegionName.AUSTRALIA_SOUTH_EAST))));
    final ClusterDescription clusterDescription =
        spy(
            NDSModelTestFactory.getGeoShardedClusterDescription(
                replicationSpecs,
                GeoShardingView.getDefaultGeoShardingView().toGeoSharding(),
                "cluster",
                "",
                new ObjectId()));

    final float baseInstanceSizeMaxNormalizedCPUUsage = 0.01234f;
    final float analyticsInstanceSizeMaxNormalizedCPUUsage = 0.12378f;

    final InstanceSize baseInstanceSize = mock(NDSInstanceSize.class);
    final InstanceFamily baseInstanceFamily = AWSInstanceFamily.M6G;
    final InstanceSize analyticsInstanceSize = mock(NDSInstanceSize.class);
    final InstanceFamily analyticsInstanceFamily = AWSInstanceFamily.I3;

    doReturn(CloudProvider.AWS).when(baseInstanceSize).getCloudProvider();
    doReturn(baseInstanceSizeMaxNormalizedCPUUsage)
        .when(baseInstanceSize)
        .getMaxNormalizedCPUUsage(eq(baseInstanceFamily));

    doReturn(analyticsInstanceSize)
        .when(spyReplicationSpec)
        .getInstanceSizeByProviderAndNodeType(eq(CloudProvider.AWS), eq(NodeType.ANALYTICS));
    doReturn(CloudProvider.AWS).when(analyticsInstanceSize).getCloudProvider();
    doReturn(analyticsInstanceFamily)
        .when(spyReplicationSpec)
        .getInstanceFamilyByProviderAndNodeType(eq(CloudProvider.AWS), eq(NodeType.ANALYTICS));
    doReturn(analyticsInstanceSizeMaxNormalizedCPUUsage)
        .when(analyticsInstanceSize)
        .getMaxNormalizedCPUUsage(eq(analyticsInstanceFamily));

    doReturn(Optional.of(spyReplicationSpec))
        .when(clusterDescription)
        .getReplicationSpecById(replicationSpecId);

    final NodeType[] electableNodeTypes =
        new NodeType[] {NodeType.ELECTABLE, NodeType.HIDDEN_SECONDARY, NodeType.READ_ONLY, null};
    for (final NodeType nodeType : electableNodeTypes) {
      doReturn(baseInstanceSize)
          .when(spyReplicationSpec)
          .getInstanceSizeByProviderAndNodeType(eq(CloudProvider.AWS), eq(nodeType));
      doReturn(baseInstanceFamily)
          .when(spyReplicationSpec)
          .getInstanceFamilyByProviderAndNodeType(eq(CloudProvider.AWS), eq(nodeType));
    }

    assertEquals(
        baseInstanceSizeMaxNormalizedCPUUsage,
        clusterDescription.getInstanceSizeMaxNormalizedCPUUsage(
            Optional.of(replicationSpecId), NodeTypeFamily.BASE),
        0.001);
    assertEquals(
        analyticsInstanceSizeMaxNormalizedCPUUsage,
        clusterDescription.getInstanceSizeMaxNormalizedCPUUsage(
            Optional.of(replicationSpecId), NodeTypeFamily.ANALYTICS),
        0.001);

    Arrays.stream(electableNodeTypes)
        .forEach(
            nodeType -> {
              verify(spyReplicationSpec, never())
                  .getInstanceSizeByProviderAndNodeType(eq(CloudProvider.AZURE), eq(nodeType));
              verify(spyReplicationSpec, never())
                  .getInstanceFamilyByProviderAndNodeType(eq(CloudProvider.AZURE), eq(nodeType));
            });
  }

  @Test
  public void testGetAutoScaleInstanceSize() {
    final ClusterDescription clusterDescription = mock(ClusterDescription.class);
    final HardwareSpec hardwareSpec = mock(HardwareSpec.class);
    when(clusterDescription.getCloudProviders())
        .thenReturn(Set.of(CloudProvider.AWS, CloudProvider.GCP));
    when(clusterDescription.getAutoScaleInstanceSize(any())).thenCallRealMethod();
    when(clusterDescription.getHardwareSpec(any())).thenReturn(hardwareSpec);
    when(hardwareSpec.getInstanceSize()).thenReturn(AWSNDSInstanceSize.M10);

    final AutoScaleInstanceSize baseAutoScaleInstanceSize =
        new AutoScaleCrossCloudInstanceSize(CrossCloudInstanceSize.M10);

    assertEquals(
        baseAutoScaleInstanceSize,
        clusterDescription.getAutoScaleInstanceSize(NodeTypeFamily.BASE));

    when(clusterDescription.getCloudProviders()).thenReturn(Set.of(CloudProvider.AWS));
    final AutoScaleInstanceSize analyticsAutoScaleInstanceSize =
        new AutoScaleSingleCloudInstanceSize(AWSNDSInstanceSize.M10);

    assertEquals(
        analyticsAutoScaleInstanceSize,
        clusterDescription.getAutoScaleInstanceSize(NodeTypeFamily.ANALYTICS));
  }

  @Test
  public void testGetAccessToBeRevokedDateForM0Cluster() {
    // Not a free cluster
    final ClusterDescription clusterDescription_dedicated =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());

    assertEquals(
        clusterDescription_dedicated.getAccessToBeRevokedDateForM0Cluster(), Optional.empty());

    // Non-free tenant cluster
    final ClusterDescription clusterDescription_notM0 =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig().setInstanceSize(FreeInstanceSize.M2)));

    assertEquals(clusterDescription_notM0.getAccessToBeRevokedDateForM0Cluster(), Optional.empty());

    //  Already paused cluster
    final ClusterDescription clusterDescription_alreadyPaused =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig()
                    .setInstanceSize(FreeInstanceSize.M0)
                    .setPaused(true)));
    assertEquals(
        clusterDescription_alreadyPaused.getAccessToBeRevokedDateForM0Cluster(), Optional.empty());

    // Access already revoked
    final ClusterDescription clusterDescription_accessAlreadyRevoked =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig()
                    .setNDSAccessRevokedDate(new Date())
                    .setInstanceSize(FreeInstanceSize.M0)
                    .setPaused(false)));
    assertEquals(
        clusterDescription_accessAlreadyRevoked.getAccessToBeRevokedDateForM0Cluster(),
        Optional.empty());

    // Cluster pause email not sent
    final ClusterDescription clusterDescription_emailNotSent =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig()
                    .setNDSAccessRevokedDate(null)
                    .setUserNotifiedAboutPauseDate(null)
                    .setInstanceSize(FreeInstanceSize.M0)
                    .setPaused(false)));
    assertEquals(
        clusterDescription_emailNotSent.getAccessToBeRevokedDateForM0Cluster(), Optional.empty());

    // Get a date
    final Date now = new Date();
    final ClusterDescription clusterDescription_userNotified =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig()
                    .setNDSAccessRevokedDate(null)
                    .setUserNotifiedAboutPauseDate(now)
                    .setInstanceSize(FreeInstanceSize.M0)
                    .setPaused(false)));
    assertEquals(
        clusterDescription_userNotified.getAccessToBeRevokedDateForM0Cluster().get(),
        DateUtils.addDays(now, PAUSE_IDLE_M0_CLUSTER_EMAIL_WARNING_BUFFER));
  }

  @Test
  public void testValidNameRegexIsEqualToOpenApiClusterNameRegex() {
    assertEquals(CLUSTER_NAME_REGEX, VALID_NAME_REGEX);
  }

  @Test
  public void
      isFlexWithServerlessNetworking_flexClusterWithServerlessFormerProviderAndLoadBalancedHostname_returnsTrue() {
    // Create a Flex cluster that was migrated from Serverless with load balanced hostname
    final FlexTenantMigrationState migrationState =
        FlexTenantMigrationState.builder().setFormerCloudProvider(CloudProvider.SERVERLESS).build();

    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFlexClusterDescription()
                .append(
                    ClusterDescription.FieldDefs.FLEX_TENANT_MIGRATION_STATE,
                    migrationState.toDBObject())
                .append(
                    ClusterDescription.FieldDefs.LOAD_BALANCED_HOSTNAME,
                    "test-lb-hostname.mongodb.net"));

    assertTrue(clusterDescription.isFlexWithServerlessNetworking());
  }

  @Test
  public void isFlexWithServerlessNetworking_flexClusterWithoutFormerCloudProvider_returnsFalse() {
    // Create a Flex cluster without migration state
    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFlexClusterDescription()
                .append(
                    ClusterDescription.FieldDefs.LOAD_BALANCED_HOSTNAME,
                    "test-lb-hostname.mongodb.net"));

    assertFalse(clusterDescription.isFlexWithServerlessNetworking());
  }

  @Test
  public void isFlexWithServerlessNetworking_flexClusterWithSharedFormerProvider_returnsFalse() {
    // Create a Flex cluster that was migrated from Shared (not Serverless)
    final FlexTenantMigrationState migrationState =
        FlexTenantMigrationState.builder()
            .setFormerCloudProvider(CloudProvider.FREE)
            .setFormerInstanceSize(FreeInstanceSize.M2)
            .build();

    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFlexClusterDescription()
                .append(
                    ClusterDescription.FieldDefs.FLEX_TENANT_MIGRATION_STATE,
                    migrationState.toDBObject())
                .append(
                    ClusterDescription.FieldDefs.LOAD_BALANCED_HOSTNAME,
                    "test-lb-hostname.mongodb.net"));

    assertFalse(clusterDescription.isFlexWithServerlessNetworking());
  }

  @Test
  public void
      isFlexWithServerlessNetworking_flexClusterWithServerlessFormerProviderButNoLoadBalancedHostname_returnsFalse() {
    // Create a Flex cluster that was migrated from Serverless but has no load balanced hostname
    final FlexTenantMigrationState migrationState =
        FlexTenantMigrationState.builder().setFormerCloudProvider(CloudProvider.SERVERLESS).build();

    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFlexClusterDescription()
                .append(
                    ClusterDescription.FieldDefs.FLEX_TENANT_MIGRATION_STATE,
                    migrationState.toDBObject()));

    assertFalse(clusterDescription.isFlexWithServerlessNetworking());
  }

  @Test
  public void
      isFlexWithServerlessNetworking_flexClusterWithServerlessFormerProviderAndEmptyLoadBalancedHostname_returnsFalse() {
    // Create a Flex cluster that was migrated from Serverless but has empty load balanced hostname
    final FlexTenantMigrationState migrationState =
        FlexTenantMigrationState.builder().setFormerCloudProvider(CloudProvider.SERVERLESS).build();

    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getFlexClusterDescription()
                .append(
                    ClusterDescription.FieldDefs.FLEX_TENANT_MIGRATION_STATE,
                    migrationState.toDBObject())
                .append(ClusterDescription.FieldDefs.LOAD_BALANCED_HOSTNAME, ""));

    assertFalse(clusterDescription.isFlexWithServerlessNetworking());
  }

  @Test
  public void isFlexWithServerlessNetworking_nonFlexCluster_returnsFalse() {
    // Create a regular AWS cluster (not Flex)
    final FlexTenantMigrationState migrationState =
        FlexTenantMigrationState.builder().setFormerCloudProvider(CloudProvider.SERVERLESS).build();

    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getAWSClusterDescription()
                .append(
                    ClusterDescription.FieldDefs.FLEX_TENANT_MIGRATION_STATE,
                    migrationState.toDBObject())
                .append(
                    ClusterDescription.FieldDefs.LOAD_BALANCED_HOSTNAME,
                    "test-lb-hostname.mongodb.net"));

    assertFalse(clusterDescription.isFlexWithServerlessNetworking());
  }

  @Test
  public void isFlexWithServerlessNetworking_serverlessCluster_returnsFalse() {
    // Create a pure Serverless cluster (not migrated to Flex)
    final ClusterDescription clusterDescription =
        new ClusterDescription(
            NDSModelTestFactory.getServerlessClusterDescription()
                .append(
                    ClusterDescription.FieldDefs.LOAD_BALANCED_HOSTNAME,
                    "test-lb-hostname.mongodb.net"));

    assertFalse(clusterDescription.isFlexWithServerlessNetworking());
  }

  @Test
  public void testGetCorruptionDetectionFields() {
    final ClusterDescription defaultCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    assertNull(defaultCluster.getLastDataValidationDate());
    assertNull(defaultCluster.getLastDbCheckDate());
    assertNull(defaultCluster.getNeedsDbCheckAfter());
    assertEquals(
        0,
        defaultCluster
            .getDbCheckPreflightRetryCount()); // Can be null in the DB, but is an int so should
    // initialize to 0 in the model.

    final Date lastDbCheckDate = new Date();
    lastDbCheckDate.setTime(1000);
    final Date lastDataValidationDate = new Date();
    lastDataValidationDate.setTime(123456);
    final Date needsDbCheckAfterDate = new Date();
    needsDbCheckAfterDate.setTime(54321);

    final ClusterDescription corruptedCluster =
        defaultCluster
            .copy()
            .setLastDbCheckDate(lastDbCheckDate)
            .setLastDataValidationDate(lastDataValidationDate)
            .setNeedsDbCheckAfter(needsDbCheckAfterDate)
            .setDbCheckPreflightRetryCount(101)
            .build();

    assertEquals(lastDataValidationDate, corruptedCluster.getLastDataValidationDate());
    assertEquals(lastDbCheckDate, corruptedCluster.getLastDbCheckDate());
    assertEquals(needsDbCheckAfterDate, corruptedCluster.getNeedsDbCheckAfter());
    assertEquals(101, corruptedCluster.getDbCheckPreflightRetryCount());
  }

  @Test
  public void testFixedVersionFields() {
    final ClusterDescription clusterDescription0 =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    assertTrue(clusterDescription0.getFixedMongoDBVersion().isEmpty());
    assertTrue(clusterDescription0.getFixedFeatureCompatibilityVersion().isEmpty());
    assertTrue(clusterDescription0.getFixedCpuArch().isEmpty());
    assertTrue(clusterDescription0.getFixedOs().isEmpty());

    final BasicDBObject basicDBObject0 = clusterDescription0.toDBObject();
    assertNull(basicDBObject0.get(FieldDefs.FIXED_MONGODB_VERSION));
    assertNull(basicDBObject0.get(FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION));
    assertNull(basicDBObject0.get(FieldDefs.FIXED_CPU_ARCH));
    assertNull(basicDBObject0.get(FieldDefs.FIXED_OS));

    final BasicDBObject cluster1DBObject = NDSModelTestFactory.getAWSClusterDescription();
    final String mdbVersion = "7.0";
    final String mdbReason = "CLOUDP-1234";
    final Date pinnedDate = DateUtils.addDays(new Date(), -3);
    final Date expirationDate = DateUtils.addDays(new Date(), 1);
    final String cpuVersion = "1.0.0";
    final String cpuReason = "CLOUDP-4321";
    final String osVersion = "AL2023";
    final String osReason = "CLOUDP-277652";

    cluster1DBObject.put(
        FieldDefs.FIXED_MONGODB_VERSION,
        new FixedVersion(mdbVersion, mdbReason, pinnedDate, expirationDate, null).toDBObject());
    cluster1DBObject.put(
        FieldDefs.FIXED_CPU_ARCH,
        new FixedVersion(cpuVersion, cpuReason, null, null, null).toDBObject());
    cluster1DBObject.put(FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION, null);
    cluster1DBObject.put(
        FieldDefs.FIXED_OS, new FixedVersion(osVersion, osReason, null, null, null).toDBObject());
    final ClusterDescription clusterDescription1 = new ClusterDescription(cluster1DBObject);

    assertEquals(mdbVersion, clusterDescription1.getFixedMongoDBVersion().get().getVersion());
    assertEquals(mdbReason, clusterDescription1.getFixedMongoDBVersion().get().getReason());
    assertEquals(pinnedDate, clusterDescription1.getFixedMongoDBVersion().get().getPinnedDate());
    assertEquals(
        expirationDate, clusterDescription1.getFixedMongoDBVersion().get().getExpirationDate());
    assertEquals(cpuVersion, clusterDescription1.getFixedCpuArch().get().getVersion());
    assertEquals(cpuReason, clusterDescription1.getFixedCpuArch().get().getReason());
    assertNull(clusterDescription1.getFixedCpuArch().get().getExpirationDate());
    assertNull(clusterDescription1.getFixedCpuArch().get().getPinnedDate());
    assertTrue(clusterDescription1.getFixedFeatureCompatibilityVersion().isEmpty());
    assertEquals(osVersion, clusterDescription1.getFixedOs().get().getVersion());
    assertEquals(osReason, clusterDescription1.getFixedOs().get().getReason());
    assertNull(clusterDescription1.getFixedOs().get().getExpirationDate());
    assertNull(clusterDescription1.getFixedOs().get().getPinnedDate());

    final BasicDBObject dbObjectResult1 = clusterDescription1.toDBObject();
    final FixedVersion fixedMongoDBVersionResult =
        new FixedVersion((BasicDBObject) dbObjectResult1.get(FieldDefs.FIXED_MONGODB_VERSION));
    final FixedVersion fixedCPUArchResult =
        new FixedVersion((BasicDBObject) dbObjectResult1.get(FieldDefs.FIXED_CPU_ARCH));
    final FixedVersion fixedOsResult =
        new FixedVersion((BasicDBObject) dbObjectResult1.get(FieldDefs.FIXED_OS));

    assertEquals(mdbVersion, fixedMongoDBVersionResult.getVersion());
    assertEquals(mdbReason, fixedMongoDBVersionResult.getReason());
    assertEquals(pinnedDate, fixedMongoDBVersionResult.getPinnedDate());
    assertEquals(expirationDate, fixedMongoDBVersionResult.getExpirationDate());
    assertEquals(cpuVersion, fixedCPUArchResult.getVersion());
    assertEquals(cpuReason, fixedCPUArchResult.getReason());
    assertNull(fixedCPUArchResult.getPinnedDate());
    assertNull(fixedCPUArchResult.getExpirationDate());
    assertNull(dbObjectResult1.get(FieldDefs.FIXED_FEATURE_COMPATIBILITY_VERSION));
    assertEquals(osVersion, fixedOsResult.getVersion());
    assertEquals(osReason, fixedOsResult.getReason());
    assertNull(fixedOsResult.getPinnedDate());
    assertNull(fixedOsResult.getExpirationDate());
  }

  @Test
  public void testGetGoalNumberBumperFiles() {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setBumperFileOverrides(List.of(new BumperFileOverride("host1", 2)))
            .build();
    assertEquals(2, clusterDescription.getGoalNumberBumperFiles("host1"));
    assertEquals(3, clusterDescription.getGoalNumberBumperFiles("host2"));
  }

  @Test
  public void testGetReplicaSetScalingStrategy() {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setReplicaSetScalingStrategy(null)
            .build();
    assertTrue(clusterDescription.getReplicaSetScalingStrategy().isEmpty());

    for (final ReplicaSetScalingStrategy strategy : ReplicaSetScalingStrategy.values()) {
      final ClusterDescription updatedDescription =
          new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
              .copy()
              .setReplicaSetScalingStrategy(strategy)
              .build();
      assertEquals(strategy, updatedDescription.getReplicaSetScalingStrategy().orElseThrow());
    }
  }

  @Test
  public void testHasSplitReplicationSpecs() {
    final ReplicationSpec rs1 =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1));

    // Single replication spec, 1 shard
    {
      final ClusterDescription cd =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setReplicationSpecList(List.of(rs1))
              .build();
      assertTrue(cd.hasSplitReplicationSpecs());
    }

    final ReplicationSpec rs2 =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), "Zone 2", 2, 3, List.of(AWSRegionName.US_EAST_2));

    // Multiple replication specs, one of them describing 2 shards
    {
      final ClusterDescription cd =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setReplicationSpecList(List.of(rs1, rs2))
              .build();
      assertFalse(cd.hasSplitReplicationSpecs());
    }

    final ReplicationSpec rs3 = rs2.copy().setNumShards(1).build();
    final ReplicationSpec rs4 =
        rs2.copy()
            .setReplicationSpecId(new ObjectId())
            .setExternalId(new ObjectId())
            .setNumShards(1)
            .build();

    // Split replication specs. 2 of them belong to the same zone, but all of them refer to 1 shard
    {
      final ClusterDescription cd =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setReplicationSpecList(List.of(rs1, rs3, rs4))
              .build();
      assertTrue(cd.hasSplitReplicationSpecs());
    }
  }

  @Test
  public void testHasMergeableReplicationSpecs() {
    final ReplicationSpec rs1 =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), oid(1), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1));

    // Single replication spec, 1 shard
    {
      final ClusterDescription cd =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setReplicationSpecList(List.of(rs1))
              .build();
      assertFalse(cd.hasMergeableReplicationSpecs());
    }

    final ReplicationSpec rs2 =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), oid(2), "Zone 2", 2, 3, List.of(AWSRegionName.US_EAST_2));

    // Multiple replication specs, one of them describing 2 shards
    {
      final ClusterDescription cd =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setReplicationSpecList(List.of(rs1, rs2))
              .build();
      assertFalse(cd.hasMergeableReplicationSpecs());
    }

    final ReplicationSpec rs3 = rs2.copy().setNumShards(1).build();
    final ReplicationSpec rs4 =
        rs3.copy()
            .setReplicationSpecId(new ObjectId())
            .setExternalId(new ObjectId())
            .setNumShards(1)
            .build();

    // Split replication specs. 2 of them belong to the same zone, but all of them refer to 1 shard
    {
      final ClusterDescription cd =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setReplicationSpecList(List.of(rs1, rs3, rs4))
              .build();
      assertTrue(cd.hasMergeableReplicationSpecs());
    }
  }

  @Test
  public void testGetReplicationSpecsInZone() {
    final ObjectId zoneId1 = new ObjectId();
    final ReplicationSpec rs1 =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), zoneId1, "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1));

    final ObjectId zoneId2 = new ObjectId();
    final ReplicationSpec rs2 =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), zoneId2, "Zone 2", 2, 3, List.of(AWSRegionName.US_EAST_2));
    final ReplicationSpec rs3 =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), zoneId2, "Zone 2", 2, 3, List.of(AWSRegionName.US_EAST_2));

    // rs1 is in Zone 1, rs2 and rs3 are in Zone 2
    final ClusterDescription cd =
        NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
            .copy()
            .setReplicationSpecList(List.of(rs1, rs2, rs3))
            .build();
    assertEquals(List.of(rs1), cd.getReplicationSpecsInZone(zoneId1));
    assertEquals(List.of(rs2, rs3), cd.getReplicationSpecsInZone(zoneId2));
  }

  @Test
  public void testGetReplicationSpecForCustomZoneMapping() {
    final ObjectId zoneId1 = new ObjectId();
    final ObjectId rsId1 = new ObjectId();

    final ReplicationSpec rs1 =
        NDSModelTestFactory.getAWSReplicationSpec(
            rsId1, zoneId1, "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1));

    final ObjectId zoneId2 = new ObjectId();
    final ObjectId rsId2 = new ObjectId();
    final ObjectId rsId3 = new ObjectId();

    final ReplicationSpec rs2 =
        NDSModelTestFactory.getAWSReplicationSpec(
            rsId2, zoneId2, "Zone 2", 2, 3, List.of(AWSRegionName.EU_WEST_1));
    final ReplicationSpec rs3 =
        NDSModelTestFactory.getAWSReplicationSpec(
            rsId3, zoneId2, "Zone 2", 2, 3, List.of(AWSRegionName.EU_WEST_1));

    final ClusterDescription cd =
        NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
            .copy()
            .setReplicationSpecList(List.of(rs1, rs2, rs3))
            .build();

    assertEquals(Optional.of(rs1), cd.getReplicationSpecForCustomZoneMapping(rsId1));
    assertEquals(Optional.of(rs2), cd.getReplicationSpecForCustomZoneMapping(rsId2));
    assertEquals(Optional.of(rs3), cd.getReplicationSpecForCustomZoneMapping(rsId3));
    assertEquals(Optional.of(rs1), cd.getReplicationSpecForCustomZoneMapping(zoneId1));
    assertEquals(Optional.of(rs2), cd.getReplicationSpecForCustomZoneMapping(zoneId2));
    assertTrue(cd.getReplicationSpecForCustomZoneMapping(new ObjectId()).isEmpty());
  }

  @Test
  public void testGetAutoScalingMode_null() {
    final AutoScalingMode defaultAutoScalingStrategy = AutoScalingMode.CLUSTER;
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setAutoScalingMode(null)
            .build();
    assertEquals(defaultAutoScalingStrategy, clusterDescription.getAutoScalingMode());
  }

  @ParameterizedTest
  @EnumSource(AutoScalingMode.class)
  public void testGetAutoScalingMode(final AutoScalingMode pAutoScalingMode) {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setAutoScalingMode(pAutoScalingMode)
            .build();
    assertEquals(pAutoScalingMode, clusterDescription.getAutoScalingMode());
  }

  @Test
  public void testGetAutoScaleInstanceSizeFromReplicationSpecId() {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    final ReplicationSpec replicationSpec =
        clusterDescription.getReplicationSpecsWithShardData().get(0);
    final AutoScaleInstanceSizeBuilder builder =
        AutoScaleInstanceSizeBuilderFactory.getAutoScaleInstanceSizeBuilder(
            clusterDescription.getCloudProviders());

    assertEquals(
        builder.getAutoScaleInstanceSize(replicationSpec.getElectableInstanceSize()),
        clusterDescription
            .getAutoScaleInstanceSizeFromReplicationSpecId(
                replicationSpec.getId(), NodeTypeFamily.BASE)
            .orElseThrow());

    // Non-existent replicationSpec should return empty optional
    assertTrue(
        clusterDescription
            .getAutoScaleInstanceSizeFromReplicationSpecId(new ObjectId(), NodeTypeFamily.BASE)
            .isEmpty());
  }

  @Test
  public void testPerformComputeAutoScaleForShards() throws Exception {
    final AutoScaling autoScaling = new AWSAutoScaling(null, null, new AutoIndexing(true));
    final ClusterDescription clusterDescription =
        new ShardedClusterDescription(
                NDSModelTestFactory.getAsymmetricShardedClusterDescriptionWithDedicatedConfigServer(
                    new ObjectId(), "Cluster0"))
            .copy()
            .setAutoScalingForProvider(AWS, autoScaling, NodeTypeFamily.BASE)
            .build();

    final List<ReplicationSpec> replicationSpecs =
        clusterDescription.getReplicationSpecsWithShardData();

    final Map<ObjectId, AutoScaleInstanceSize> replicationIdToNewAutoScaleInstanceSize =
        Map.of(
            replicationSpecs.get(0).getId(),
            new AutoScaleCrossCloudInstanceSize(CrossCloudInstanceSize.M200),
            replicationSpecs.get(1).getId(),
            new AutoScaleCrossCloudInstanceSize(CrossCloudInstanceSize.M300));

    final ClusterDescription scaledClusterDescription =
        clusterDescription.performComputeAutoScaleForShards(
            replicationIdToNewAutoScaleInstanceSize, NodeTypeFamily.BASE);

    final ReplicationSpec firstReplicationSpec =
        scaledClusterDescription
            .getReplicationSpecById(replicationSpecs.get(0).getId())
            .orElseThrow();
    firstReplicationSpec
        .getRegionConfigs()
        .forEach(
            rc -> {
              assertEquals(AWSNDSInstanceSize.M200, rc.getElectableSpecs().getInstanceSize());
              assertFalse(rc.getBaseAutoScaling().getAutoIndexing().isEnabled());
            });

    final ReplicationSpec secondReplicationSpec =
        scaledClusterDescription
            .getReplicationSpecById(replicationSpecs.get(1).getId())
            .orElseThrow();
    secondReplicationSpec
        .getRegionConfigs()
        .forEach(
            rc -> {
              assertEquals(AWSNDSInstanceSize.M300, rc.getElectableSpecs().getInstanceSize());
              assertFalse(rc.getBaseAutoScaling().getAutoIndexing().isEnabled());
            });
  }

  @Test
  public void testPerformComputeAutoScaleForShards_geoSharded() throws Exception {
    final AutoScaling autoScaling = new AWSAutoScaling(null, null, new AutoIndexing(true));
    final ClusterDescription clusterDescription =
        new ShardedClusterDescription(
                NDSModelTestFactory.getAsymmetricGeoShardedClusterDescriptionWithInstanceSizes(
                    new ObjectId(),
                    "Cluster0",
                    List.of(
                        List.of(AWSNDSInstanceSize.M50),
                        List.of(AzureNDSInstanceSize.M50),
                        List.of(GCPNDSInstanceSize.M40)),
                    true))
            .copy()
            .setAutoScalingForProvider(AWS, autoScaling, NodeTypeFamily.BASE)
            .setAutoScalingForProvider(GCP, autoScaling, NodeTypeFamily.BASE)
            .setAutoScalingForProvider(AZURE, autoScaling, NodeTypeFamily.BASE)
            .build();

    final List<ReplicationSpec> replicationSpecs =
        clusterDescription.getReplicationSpecsWithShardData();

    final Map<ObjectId, AutoScaleInstanceSize> replicationIdToNewAutoScaleInstanceSize =
        Map.of(
            replicationSpecs.get(0).getId(),
            new AutoScaleCrossCloudInstanceSize(CrossCloudInstanceSize.M60),
            replicationSpecs.get(1).getId(),
            new AutoScaleCrossCloudInstanceSize(CrossCloudInstanceSize.M60));

    final ClusterDescription scaledClusterDescription =
        clusterDescription.performComputeAutoScaleForShards(
            replicationIdToNewAutoScaleInstanceSize, NodeTypeFamily.BASE);

    final ReplicationSpec firstReplicationSpec =
        scaledClusterDescription
            .getReplicationSpecById(replicationSpecs.get(0).getId())
            .orElseThrow();
    firstReplicationSpec
        .getRegionConfigs()
        .forEach(
            rc -> {
              assertEquals(AWSNDSInstanceSize.M60, rc.getElectableSpecs().getInstanceSize());
              assertFalse(rc.getBaseAutoScaling().getAutoIndexing().isEnabled());
            });

    final ReplicationSpec secondReplicationSpec =
        scaledClusterDescription
            .getReplicationSpecById(replicationSpecs.get(1).getId())
            .orElseThrow();
    secondReplicationSpec
        .getRegionConfigs()
        .forEach(
            rc -> {
              assertEquals(AzureNDSInstanceSize.M60, rc.getElectableSpecs().getInstanceSize());
              assertFalse(rc.getBaseAutoScaling().getAutoIndexing().isEnabled());
            });

    assertEquals(
        scaledClusterDescription.getReplicationSpecById(replicationSpecs.get(2).getId()),
        clusterDescription.getReplicationSpecById(replicationSpecs.get(2).getId()));
  }

  @Test
  public void testHasAsymmetricShards() {
    final AWSHardwareSpec noNodes =
        new AWSHardwareSpec(
            0,
            AWSNDSInstanceSize.M10,
            AWSInstanceFamily.M5,
            OS.AL2,
            CpuArchitecture.ARM64,
            1000,
            1000,
            VolumeType.Gp3,
            true);
    final AWSHardwareSpec m10 = noNodes.copy().setNodeCount(3).build();
    final AWSHardwareSpec m20 = m10.copy().setInstanceSize(AWSNDSInstanceSize.M20).build();
    final List<RegionConfig> m10s =
        List.of(
            new ShardRegionConfig(
                AWSRegionName.US_EAST_1,
                AWS,
                AWSAutoScaling.getDisabledAutoScaling(),
                AWSAutoScaling.getDisabledAutoScaling(),
                RegionConfig.MAX_PRIORITY,
                m10,
                m10,
                m10,
                noNodes,
                null,
                null,
                null,
                null));
    final List<RegionConfig> electableM20s =
        List.of(
            new ShardRegionConfig(
                AWSRegionName.US_EAST_1,
                AWS,
                AWSAutoScaling.getDisabledAutoScaling(),
                AWSAutoScaling.getDisabledAutoScaling(),
                RegionConfig.MAX_PRIORITY,
                m20,
                m10,
                m10,
                noNodes,
                null,
                null,
                null,
                null));
    final List<RegionConfig> analyticsM20s =
        List.of(
            new ShardRegionConfig(
                AWSRegionName.US_EAST_1,
                AWS,
                AWSAutoScaling.getDisabledAutoScaling(),
                AWSAutoScaling.getDisabledAutoScaling(),
                RegionConfig.MAX_PRIORITY,
                m10,
                m20,
                m10,
                noNodes,
                null,
                null,
                null,
                null));
    final ReplicationSpec shard0m10 =
        new ReplicationSpec(oid(0), oid(100), oid(1000), "Zone 0", 1, m10s);
    final ReplicationSpec shard1m10 =
        new ReplicationSpec(oid(1), oid(101), oid(1001), "Zone 1", 1, m10s);
    final ReplicationSpec shard2m20 =
        new ReplicationSpec(oid(2), oid(102), oid(1002), "Zone 2", 1, electableM20s);
    final ReplicationSpec shard3m20 =
        new ReplicationSpec(oid(3), oid(103), oid(1003), "Zone 3", 1, analyticsM20s);

    assertFalse(
        ClusterDescription.hasAsymmetricShardsForNodeType(
            List.of(shard0m10, shard1m10), NodeType.ELECTABLE));
    assertFalse(
        ClusterDescription.hasAsymmetricShardsForNodeType(
            List.of(shard0m10, shard1m10), NodeType.ANALYTICS));
    assertTrue(
        ClusterDescription.hasAsymmetricShardsForNodeType(
            List.of(shard0m10, shard2m20), NodeType.ELECTABLE));
    assertFalse(
        ClusterDescription.hasAsymmetricShardsForNodeType(
            List.of(shard0m10, shard2m20), NodeType.ANALYTICS));
    assertFalse(
        ClusterDescription.hasAsymmetricShardsForNodeType(
            List.of(shard0m10, shard3m20), NodeType.ELECTABLE));
    assertTrue(
        ClusterDescription.hasAsymmetricShardsForNodeType(
            List.of(shard0m10, shard3m20), NodeType.ANALYTICS));
  }

  @Test
  public void testGetRegion_Free() {
    final RegionName regionName = AWSRegionName.US_EAST_1;
    final TestFreeClusterDescriptionConfig freeClusterDescriptionConfig =
        new NDSModelTestFactory.TestFreeClusterDescriptionConfig().setRegionName(regionName);
    final ClusterDescription freeCluster =
        new ClusterDescription(
            NDSModelTestFactory.getFreeClusterDescription(freeClusterDescriptionConfig));

    assertTrue(freeCluster.isSharedTenantCluster());
    assertTrue(freeCluster.isFreeTenantCluster());

    final InstanceHardware instanceHardware = mock(InstanceHardware.class);
    doReturn(CloudProvider.FREE).when(instanceHardware).getCloudProvider();

    assertEquals(regionName, freeCluster.getRegionName(instanceHardware, Optional.empty()));
  }

  @Test
  public void testGetRegion_Serverless() {
    final RegionName regionName = AWSRegionName.US_EAST_1;
    final TestServerlessClusterDescriptionConfig serverlessClusterDescriptionConfig =
        new NDSModelTestFactory.TestServerlessClusterDescriptionConfig().setRegionName(regionName);
    final ClusterDescription serverlessInstance =
        new ClusterDescription(
            NDSModelTestFactory.getServerlessClusterDescription(
                serverlessClusterDescriptionConfig));

    assertTrue(serverlessInstance.isServerlessTenantCluster());

    final InstanceHardware instanceHardware = mock(InstanceHardware.class);
    doReturn(CloudProvider.SERVERLESS).when(instanceHardware).getCloudProvider();

    assertEquals(regionName, serverlessInstance.getRegionName(instanceHardware, Optional.empty()));
  }

  @Test
  public void testGetRegion_flex() {
    final RegionName regionName = AWSRegionName.US_EAST_1;
    final TestFlexClusterDescriptionConfig flexClusterDescriptionConfig =
        new TestFlexClusterDescriptionConfig().setRegionName(regionName);
    final ClusterDescription flexInstance =
        new ClusterDescription(
            NDSModelTestFactory.getFlexClusterDescription(flexClusterDescriptionConfig));

    assertTrue(flexInstance.isFlexTenantCluster());

    final InstanceHardware instanceHardware = mock(InstanceHardware.class);
    doReturn(CloudProvider.FLEX).when(instanceHardware).getCloudProvider();

    assertEquals(regionName, flexInstance.getRegionName(instanceHardware, Optional.empty()));
  }

  @Test
  public void testGetInstanceSizesForProvider() {
    final BasicDBObject cdDoc =
        NDSModelTestFactory.getAsymmetricShardedClusterDescriptionWithDedicatedConfigServer(
            oid(1), "test");
    final BasicDBObject analyticsSpecs =
        (BasicDBObject)
            ((BasicDBObject)
                    ((BasicDBList)
                            ((BasicDBObject)
                                    ((BasicDBList) cdDoc.get("replicationSpecList")).get(0))
                                .get("regionConfigs"))
                        .get(0))
                .get("analyticsSpecs");

    analyticsSpecs.put("nodeCount", 1);
    analyticsSpecs.put("instanceSize", AWSNDSInstanceSize.M80);
    final ClusterDescription cd = ClusterDescriptionFactory.get(cdDoc);

    assertEquals(
        Set.of(AWSNDSInstanceSize.M20, AWSNDSInstanceSize.M30, AWSNDSInstanceSize.M80),
        cd.getInstanceSizesForProvider(CloudProvider.AWS));
  }

  @Test
  public void testGetInstanceSizesForProviderAndNodeType() {
    final BasicDBObject cdDoc =
        NDSModelTestFactory.getAsymmetricShardedClusterDescriptionWithDedicatedConfigServer(
            oid(1), "test");
    final BasicDBObject analyticsSpecs =
        (BasicDBObject)
            ((BasicDBObject)
                    ((BasicDBList)
                            ((BasicDBObject)
                                    ((BasicDBList) cdDoc.get("replicationSpecList")).get(0))
                                .get("regionConfigs"))
                        .get(0))
                .get("analyticsSpecs");

    analyticsSpecs.put("nodeCount", 1);
    analyticsSpecs.put("instanceSize", AWSNDSInstanceSize.M80);
    final ClusterDescription cd = ClusterDescriptionFactory.get(cdDoc);

    assertEquals(
        Set.of(AWSNDSInstanceSize.M20, AWSNDSInstanceSize.M30),
        cd.getInstanceSizesForProviderAndNodeType(CloudProvider.AWS, NodeType.ELECTABLE));
    assertEquals(
        Set.of(AWSNDSInstanceSize.M80),
        cd.getInstanceSizesForProviderAndNodeType(CloudProvider.AWS, NodeType.ANALYTICS));
  }

  @ParameterizedTest
  @EnumSource(EmployeeAccessGrantType.class)
  public void testGetActiveEmployeeAccessGrant(final EmployeeAccessGrantType pGrantType) {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    assertNull(clusterDescription.getActiveEmployeeAccessGrant().orElse(null));

    final Date validDate = Date.from(new Date().toInstant().plusSeconds(100));
    final Date expiredDate = Date.from(new Date().toInstant().minusSeconds(100));

    final ClusterDescription employeeAccessGrantDescription =
        clusterDescription
            .copy()
            .setEmployeeAccessGrant(new EmployeeAccessGrant(pGrantType, validDate))
            .build();
    final EmployeeAccessGrant employeeAccessGrantView =
        employeeAccessGrantDescription.getActiveEmployeeAccessGrant().orElse(null);
    assertNotNull(employeeAccessGrantView);
    assertEquals(pGrantType, employeeAccessGrantView.getGrantType());
    assertEquals(validDate, employeeAccessGrantView.getExpiration());

    final ClusterDescription allAccessExpiredDescription =
        clusterDescription
            .copy()
            .setEmployeeAccessGrant(new EmployeeAccessGrant(pGrantType, expiredDate))
            .build();
    final EmployeeAccessGrant employeeAccessGrantViewExpired =
        allAccessExpiredDescription.getActiveEmployeeAccessGrant().orElse(null);
    assertNull(employeeAccessGrantViewExpired);
  }

  @ParameterizedTest
  @EnumSource(EmployeeAccessGrantType.class)
  public void testHasTemporaryAccessGrantedForGrantType_emptyGrantHasNoAccess(
      final EmployeeAccessGrantType pGrantType) {
    final ClusterDescription clusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription());
    assertTrue(clusterDescription.getActiveEmployeeAccessGrant().isEmpty());
    assertFalse(clusterDescription.hasTemporaryAccessGrantedForGrantType(pGrantType));
  }

  @ParameterizedTest
  @EnumSource(EmployeeAccessGrantType.class)
  public void testHasTemporaryAccessGrantedForGrantType_expiredGrantHasNoAccess(
      final EmployeeAccessGrantType pGrantTypeOnCluster) {
    final Date expired = Date.from(new Date().toInstant().minusSeconds(100));
    final ClusterDescription expiredGrantClusterDescription =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setEmployeeAccessGrant(new EmployeeAccessGrant(pGrantTypeOnCluster, expired))
            .build();
    assertFalse(expiredGrantClusterDescription.getActiveEmployeeAccessGrant().isPresent());

    for (final EmployeeAccessGrantType grantType : EmployeeAccessGrantType.values()) {
      assertFalse(expiredGrantClusterDescription.hasTemporaryAccessGrantedForGrantType(grantType));
    }
  }

  @Test
  public void testHasTemporaryAccessGrantedForGrantType_validGrantTypes() {
    final Date notExpired = Date.from(new Date().toInstant().plusSeconds(100));

    final ClusterDescription logsGrant =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setEmployeeAccessGrant(
                new EmployeeAccessGrant(EmployeeAccessGrantType.CLUSTER_DATABASE_LOGS, notExpired))
            .build();
    assertTrue(logsGrant.getActiveEmployeeAccessGrant().isPresent());
    assertTrue(
        logsGrant.hasTemporaryAccessGrantedForGrantType(
            EmployeeAccessGrantType.CLUSTER_DATABASE_LOGS));
    assertFalse(
        logsGrant.hasTemporaryAccessGrantedForGrantType(
            EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE));
    assertFalse(
        logsGrant.hasTemporaryAccessGrantedForGrantType(
            EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA));

    final ClusterDescription infrastructureGrant =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setEmployeeAccessGrant(
                new EmployeeAccessGrant(EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE, notExpired))
            .build();
    assertTrue(infrastructureGrant.getActiveEmployeeAccessGrant().isPresent());
    assertTrue(
        infrastructureGrant.hasTemporaryAccessGrantedForGrantType(
            EmployeeAccessGrantType.CLUSTER_DATABASE_LOGS));
    assertTrue(
        infrastructureGrant.hasTemporaryAccessGrantedForGrantType(
            EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE));
    assertFalse(
        infrastructureGrant.hasTemporaryAccessGrantedForGrantType(
            EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA));

    final ClusterDescription syncDebugGrant =
        new ClusterDescription(NDSModelTestFactory.getAWSClusterDescription())
            .copy()
            .setEmployeeAccessGrant(
                new EmployeeAccessGrant(
                    EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA,
                    notExpired))
            .build();
    assertTrue(syncDebugGrant.getActiveEmployeeAccessGrant().isPresent());
    assertTrue(
        syncDebugGrant.hasTemporaryAccessGrantedForGrantType(
            EmployeeAccessGrantType.CLUSTER_DATABASE_LOGS));
    assertTrue(
        syncDebugGrant.hasTemporaryAccessGrantedForGrantType(
            EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE));
    assertTrue(
        syncDebugGrant.hasTemporaryAccessGrantedForGrantType(
            EmployeeAccessGrantType.CLUSTER_INFRASTRUCTURE_AND_APP_SERVICES_SYNC_DATA));
  }

  @Test
  public void testWasMigratedFromServerlessToFlex_true() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription testCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "testCluster", CloudProvider.FLEX)
            .copy()
            .setFlexTenantMigrationState(
                FlexTenantMigrationState.builder()
                    .setFormerCloudProvider(CloudProvider.SERVERLESS)
                    .build())
            .build();

    assertTrue(testCluster.wasMigratedFromServerlessToFlex());
  }

  @Test
  public void testWasMigratedFromServerlessToFlex_falseNotCurrentlyFlex() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription testCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "testCluster", CloudProvider.AWS)
            .copy()
            .setFlexTenantMigrationState(
                FlexTenantMigrationState.builder()
                    .setFormerCloudProvider(CloudProvider.SERVERLESS)
                    .build())
            .build();

    assertFalse(testCluster.wasMigratedFromServerlessToFlex());
  }

  @Test
  public void testWasMigratedFromServerlessToFlex_falseNoMigration() {
    final NDSGroup group = NDSModelTestFactory.getAWSMockedGroup();

    final ClusterDescription testCluster =
        NDSModelTestFactory.getClusterDescription(
                group.getGroupId(), "testCluster", CloudProvider.FLEX)
            .copy()
            .build();

    assertFalse(testCluster.wasMigratedFromServerlessToFlex());
  }

  @Test
  public void testRedactClientLogData() {
    // A newly created cluster description will default the option to false.
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    assertTrue(clusterDescription.getRedactClientLogData().isEmpty());

    // Check whether the option is preserved when converted to/from a BasicDBObject.
    {
      final ClusterDescription cluster =
          clusterDescription.copy().setRedactClientLogData(true).build();
      final BasicDBObject clusterDescriptionDBObject = cluster.toDBObject();
      assertEquals(clusterDescriptionDBObject.get(FieldDefs.REDACT_CLIENT_LOG_DATA), true);
    }
    {
      final ClusterDescription cluster =
          clusterDescription.copy().setRedactClientLogData(false).build();
      final BasicDBObject clusterDescriptionDBObject = cluster.toDBObject();
      assertEquals(clusterDescriptionDBObject.get(FieldDefs.REDACT_CLIENT_LOG_DATA), false);
    }
    {
      final ClusterDescription cluster =
          clusterDescription.copy().setRedactClientLogData(null).build();
      final BasicDBObject clusterDescriptionDBObject = cluster.toDBObject();
      assertEquals(clusterDescriptionDBObject.get(FieldDefs.REDACT_CLIENT_LOG_DATA), null);
    }

    // Cluster must be creatable from a DBObject.
    {
      final BasicDBObject clusterDescriptionDBObject = clusterDescription.toDBObject();
      clusterDescriptionDBObject.append(FieldDefs.REDACT_CLIENT_LOG_DATA, true);
      final ClusterDescription redactedCluster = new ClusterDescription(clusterDescriptionDBObject);
      assertTrue(redactedCluster.getRedactClientLogData().isPresent());
      assertTrue(redactedCluster.getRedactClientLogData().get());
    }
    {
      final BasicDBObject clusterDescriptionDBObject = clusterDescription.toDBObject();
      clusterDescriptionDBObject.append(FieldDefs.REDACT_CLIENT_LOG_DATA, false);
      final ClusterDescription redactedCluster = new ClusterDescription(clusterDescriptionDBObject);
      assertTrue(redactedCluster.getRedactClientLogData().isPresent());
      assertFalse(redactedCluster.getRedactClientLogData().get());
    }

    // A cluster description should be creatable from an object without the REDACT_CLIENT_LOG_DATA
    // field set.
    {
      final BasicDBObject clusterDescriptionDBObject = clusterDescription.toDBObject();
      clusterDescriptionDBObject.remove(FieldDefs.REDACT_CLIENT_LOG_DATA);
      assertNull(clusterDescriptionDBObject.get(FieldDefs.REDACT_CLIENT_LOG_DATA));
      final ClusterDescription reconstructedClusterDescription =
          new ClusterDescription(clusterDescriptionDBObject);
      assertTrue(reconstructedClusterDescription.getRedactClientLogData().isEmpty());
    }
  }

  @Test
  public void testGetCloudProvidersForNodeType() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getDefaultClusterDescription(
            new ObjectId(),
            "Cluster0",
            List.of(
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    CloudProvider.AWS, AWSRegionName.US_EAST_1, 7, 2, 0, 0, 0),
                NDSModelTestFactory.getShardRegionConfigForRegion(
                    CloudProvider.AZURE, AzureRegionName.US_EAST_2, 6, 0, 1, 0, 0)));

    assertEquals(
        Set.of(CloudProvider.AWS),
        clusterDescription.getCloudProvidersForNodeType(NodeType.ELECTABLE));
    assertEquals(
        Set.of(CloudProvider.AZURE),
        clusterDescription.getCloudProvidersForNodeType(NodeType.ANALYTICS));
    assertEquals(Set.of(), clusterDescription.getCloudProvidersForNodeType(NodeType.READ_ONLY));
    assertEquals(
        Set.of(), clusterDescription.getCloudProvidersForNodeType(NodeType.HIDDEN_SECONDARY));
  }

  @Test
  public void testIsConfiguredForPrivateEndpointLegacyConnectionStrings() {
    // DEFAULT status, replica set
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .legacyConnectionStringStatus(Status.DEFAULT)
                      .build())
              .build();
      // No matter the boolean value, method value returns true
      assertTrue(clusterDescription.isConfiguredForPrivateEndpointLegacyConnectionStrings(true));
      assertTrue(clusterDescription.isConfiguredForPrivateEndpointLegacyConnectionStrings(false));
    }

    // DEFAULT status, sharded cluster, has private endpoint legacy connection strings
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .legacyConnectionStringStatus(Status.DEFAULT)
                      .build())
              .setHasPrivateEndpointLegacyConnectionStrings(true)
              .build();
      // No matter the boolean value, method value returns true
      assertTrue(clusterDescription.isConfiguredForPrivateEndpointLegacyConnectionStrings(true));
      assertTrue(clusterDescription.isConfiguredForPrivateEndpointLegacyConnectionStrings(false));
    }

    // DEFAULT status, sharded cluster, does not have endpoint legacy connection strings
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .legacyConnectionStringStatus(Status.DEFAULT)
                      .build())
              .setHasPrivateEndpointLegacyConnectionStrings(false)
              .build();
      // Only enabled when cluster does not support load balanced strings
      assertFalse(clusterDescription.isConfiguredForPrivateEndpointLegacyConnectionStrings(true));
      assertTrue(clusterDescription.isConfiguredForPrivateEndpointLegacyConnectionStrings(false));
    }

    // ENABLED status
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .legacyConnectionStringStatus(Status.ENABLED)
                      .build())
              .build();
      // Always enabled when the status is enabled
      assertTrue(clusterDescription.isConfiguredForPrivateEndpointLegacyConnectionStrings(true));
      assertTrue(clusterDescription.isConfiguredForPrivateEndpointLegacyConnectionStrings(false));
    }

    // DISABLED status
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .legacyConnectionStringStatus(Status.DISABLED)
                      .build())
              .build();
      // Always disabled when the status is disabled
      assertFalse(clusterDescription.isConfiguredForPrivateEndpointLegacyConnectionStrings(true));
      assertFalse(clusterDescription.isConfiguredForPrivateEndpointLegacyConnectionStrings(false));
    }
  }

  @Test
  public void testIsConfiguredForPrivateEndpointRegionalizedConnectionStrings() {
    final NDSGroup group = mock(NDSGroup.class);

    // Replica set
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
      assertFalse(
          clusterDescription.isConfiguredForPrivateEndpointRegionalizedConnectionStrings(group));
    }

    // ENABLED, sharded cluster
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .regionalizedConnectionStringStatus(Status.ENABLED)
                      .build())
              .build();
      assertTrue(
          clusterDescription.isConfiguredForPrivateEndpointRegionalizedConnectionStrings(group));
    }

    // DISABLED, sharded cluster
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .regionalizedConnectionStringStatus(Status.DISABLED)
                      .build())
              .build();
      assertFalse(
          clusterDescription.isConfiguredForPrivateEndpointRegionalizedConnectionStrings(group));
    }

    // DEFAULT, sharded cluster, regionalized private link enabled
    {
      doReturn(true).when(group).isRegionalizedPrivateLinkEnabled();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .regionalizedConnectionStringStatus(Status.DEFAULT)
                      .build())
              .build();
      assertTrue(
          clusterDescription.isConfiguredForPrivateEndpointRegionalizedConnectionStrings(group));
    }

    // DISABLED, sharded cluster, regionalized private link disabled
    {
      doReturn(false).when(group).isRegionalizedPrivateLinkEnabled();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .regionalizedConnectionStringStatus(Status.DEFAULT)
                      .build())
              .build();
      assertFalse(
          clusterDescription.isConfiguredForPrivateEndpointRegionalizedConnectionStrings(group));
    }
  }

  @Test
  public void testIsConfiguredForPrivateEndpointNonRegionalizedConnectionStrings() {
    final NDSGroup group = mock(NDSGroup.class);

    // Replica set
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
      assertTrue(
          clusterDescription.isConfiguredForPrivateEndpointNonRegionalizedConnectionStrings(group));
    }

    // ENABLED, sharded cluster
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .nonRegionalizedConnectionStringStatus(Status.ENABLED)
                      .build())
              .build();
      assertTrue(
          clusterDescription.isConfiguredForPrivateEndpointNonRegionalizedConnectionStrings(group));
    }

    // DISABLED, sharded cluster
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .nonRegionalizedConnectionStringStatus(Status.DISABLED)
                      .build())
              .build();
      assertFalse(
          clusterDescription.isConfiguredForPrivateEndpointNonRegionalizedConnectionStrings(group));
    }

    // DEFAULT, sharded cluster, regionalized private link enabled
    {
      doReturn(true).when(group).isRegionalizedPrivateLinkEnabled();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .nonRegionalizedConnectionStringStatus(Status.DEFAULT)
                      .build())
              .build();
      assertFalse(
          clusterDescription.isConfiguredForPrivateEndpointNonRegionalizedConnectionStrings(group));
    }

    // DISABLED, sharded cluster, regionalized private link disabled
    {
      doReturn(false).when(group).isRegionalizedPrivateLinkEnabled();
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .nonRegionalizedConnectionStringStatus(Status.DEFAULT)
                      .build())
              .build();
      assertTrue(
          clusterDescription.isConfiguredForPrivateEndpointNonRegionalizedConnectionStrings(group));
    }
  }

  @Test
  public void testIsConfiguredForLoadBalancedConnectionStrings() {
    // DEFAULT status, replica set
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .loadBalancedConnectionStringStatus(Status.DEFAULT)
                      .build())
              .build();
      assertFalse(clusterDescription.isConfiguredForLoadBalancedConnectionStrings());
    }

    // DEFAULT status, sharded cluster
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .loadBalancedConnectionStringStatus(Status.DEFAULT)
                      .build())
              .build();
      assertTrue(clusterDescription.isConfiguredForLoadBalancedConnectionStrings());
    }

    // ENABLED status, sharded cluster
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getShardedClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .loadBalancedConnectionStringStatus(Status.ENABLED)
                      .build())
              .build();
      assertTrue(clusterDescription.isConfiguredForLoadBalancedConnectionStrings());
    }

    // DISABLED status
    {
      final ClusterDescription clusterDescription =
          NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
              .copy()
              .setClusterConnectionStringConfiguration(
                  new ClusterConnectionStringConfiguration.Builder()
                      .loadBalancedConnectionStringStatus(Status.DISABLED)
                      .build())
              .build();
      assertFalse(clusterDescription.isConfiguredForLoadBalancedConnectionStrings());
    }
  }

  @Test
  public void testHasSingleTargetPrivateEndpointConnectionStrings() {
    final ClusterDescription mockCluster = mock(ClusterDescription.class);
    final Map<String, String> privateEndpointSRVMap =
        Map.of("private-endpoint-id", "pe-address.hostname.com");
    final Map<String, List<String>> privateEndpointURIs =
        Map.of("private-endpoint-id", List.of("pe.hostname.com:101", "pe.hostname.com:102"));
    doReturn(privateEndpointSRVMap).when(mockCluster).getPrivateSrvAddressMap();
    doReturn(privateEndpointURIs).when(mockCluster).getPrivateMongoDBUriHostsMap();
    doCallRealMethod().when(mockCluster).hasSingleTargetPrivateEndpointStrings();

    assertTrue(mockCluster.hasSingleTargetPrivateEndpointStrings());

    doReturn(Collections.emptyMap()).when(mockCluster).getPrivateSrvAddressMap();
    assertTrue(mockCluster.hasSingleTargetPrivateEndpointStrings());

    doReturn(Collections.emptyMap()).when(mockCluster).getPrivateMongoDBUriHostsMap();
    assertFalse(mockCluster.hasSingleTargetPrivateEndpointStrings());

    doReturn(privateEndpointSRVMap).when(mockCluster).getPrivateSrvAddressMap();
    assertTrue(mockCluster.hasSingleTargetPrivateEndpointStrings());
  }

  @Test
  public void testIsEligibleForReducedFlexPricing() {
    final ClusterDescription clusterDescription1 =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    assertFalse(clusterDescription1.isEligibleForReducedFlexPricing());

    final ClusterDescription clusterDescription2 =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setIsEligibleForReducedFlexPricing(true)
            .build();
    assertTrue(clusterDescription2.isEligibleForReducedFlexPricing());
  }

  @Test
  public void testMongotuneConfiguration() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    assertTrue(clusterDescription.getMongotuneStatus().isEmpty());
    final ClusterDescription clusterDescription2 =
        clusterDescription.copy().setMongotuneStatus(MongotuneStatus.builder().build()).build();
    assertEquals(
        State.DISABLE,
        clusterDescription2.getMongotuneStatus().map(MongotuneStatus::state).orElseThrow(),
        "When mongotune status is present, default state should be DISABLE.");

    final String mongotuneVersion = "2025.04.28";
    final ClusterDescription clusterDescription3 =
        clusterDescription
            .copy()
            .setMongotuneStatus(
                MongotuneStatus.builder().state(State.ENABLE).version(mongotuneVersion).build())
            .build();
    assertEquals(
        State.ENABLE,
        clusterDescription3.getMongotuneStatus().map(MongotuneStatus::state).orElseThrow());
    assertEquals(
        mongotuneVersion,
        clusterDescription3.getMongotuneStatus().map(MongotuneStatus::version).orElseThrow());

    final PolicyStatus diskWriteBlockPolicy =
        new DiskWriteBlockPolicyStatus.Builder()
            .setEligible(true)
            .setEnabled(true)
            .setArgVersion(DiskWriteBlockPolicyArgVersion.V1)
            .build();
    final Map<PolicyType, PolicyStatus> policies =
        Map.of(PolicyType.DISK_WRITE_BLOCKING, diskWriteBlockPolicy);
    final ClusterDescription clusterDescription4 =
        clusterDescription
            .copy()
            .setMongotuneStatus(
                MongotuneStatus.builder()
                    .state(State.ENABLE)
                    .version(mongotuneVersion)
                    .policies(policies)
                    .build())
            .build();
    assertTrue(
        clusterDescription4
            .getMongotuneStatus()
            .map(MongotuneStatus::policies)
            .orElseThrow()
            .containsKey(PolicyType.DISK_WRITE_BLOCKING));
    assertEquals(
        DiskWriteBlockPolicyArgVersion.V1,
        clusterDescription4
            .getMongotuneStatus()
            .map(MongotuneStatus::policies)
            .orElseThrow()
            .get(PolicyType.DISK_WRITE_BLOCKING)
            .getArgVersion());
  }

  @Test
  public void testProxyProtocolForPrivateLinkMode() {
    final ClusterDescription clusterDescription =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS);
    assertEquals(
        ProxyProtocolForPrivateLinkMode.STANDARD,
        clusterDescription.getProxyProtocolForPrivateLinkMode().orElseThrow());

    final ClusterDescription clusterDescription2 =
        clusterDescription
            .copy()
            .setProxyProtocolForPrivateLinkMode(ProxyProtocolForPrivateLinkMode.DISABLED)
            .build();
    assertEquals(
        ProxyProtocolForPrivateLinkMode.DISABLED,
        clusterDescription2.getProxyProtocolForPrivateLinkMode().orElseThrow());
  }

  @Test
  public void
      testGetUpdatedClusterReplicationSpecDiff_handlesNewAndModifiedAsymmetricReplicationSpecs() {
    final ObjectId groupId = new ObjectId();

    // Create existing replication spec
    final ReplicationSpec existingReplicationSpec =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), new ObjectId(), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1));

    // Create modified version of existing spec with M20 instance size
    final ReplicationSpec modifiedReplicationSpec =
        existingReplicationSpec
            .copy()
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setInstanceSize(AWSNDSInstanceSize.M20))
            .build();

    // Create completely new replication spec
    final ObjectId newReplicationSpecId = new ObjectId();
    final ReplicationSpec newReplicationSpec =
        NDSModelTestFactory.getAWSReplicationSpec(
            newReplicationSpecId, new ObjectId(), "Zone 2", 1, 3, List.of(AWSRegionName.US_WEST_2));

    // Old cluster has only the existing replication spec
    final ClusterDescription oldCluster =
        NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS)
            .copy()
            .setReplicationSpecList(List.of(existingReplicationSpec))
            .build();

    // New cluster has the modified existing spec plus a new spec
    final ClusterDescription newCluster =
        NDSModelTestFactory.getClusterDescription(groupId, CloudProvider.AWS)
            .copy()
            .setReplicationSpecList(List.of(modifiedReplicationSpec, newReplicationSpec))
            .build();

    // Call method to test
    final ItemDiffs result = newCluster.getUpdatedClusterReplicationSpecDiff(oldCluster);

    // Verify that diffs were created for both specs
    assertFalse(result.isEmpty());
    assertEquals(2, result.size());

    // Find the modified spec diff (should be MODIFIED status)
    final Optional<ItemDiff> modifiedDiff =
        result.getDiffs().stream()
            .filter(diff -> diff.getItemStatus() == ItemDiff.Status.MODIFIED)
            .findFirst();
    assertTrue(modifiedDiff.isPresent());
    assertEquals("shard 0", modifiedDiff.get().getItemId());

    // Find the new spec diff (should be NEW status)
    final Optional<ItemDiff> newDiff =
        result.getDiffs().stream()
            .filter(diff -> diff.getItemStatus() == ItemDiff.Status.NEW)
            .findFirst();
    assertTrue(newDiff.isPresent());
    assertEquals("shard 1", newDiff.get().getItemId());

    // Verify the new diff contains expected items with null old values
    final List<DeploymentItemParameterDiff> newItems = newDiff.get().getItems();
    assertEquals(2, newItems.size());
    assertTrue(
        newItems.stream()
            .anyMatch(
                item ->
                    item.getParamName().equals(ClusterDescription.FieldDefs.IS_ASYMMETRIC_SHARD)
                        && item.getOldValue() == null
                        && item.getNewValue().equals(true)));

    assertTrue(
        newItems.stream()
            .anyMatch(
                item ->
                    item.getParamName().equals(ClusterDescription.FieldDefs.INSTANCE_SIZE)
                        && item.getOldValue() == null
                        && item.getNewValue()
                            .equals(newReplicationSpec.getElectableInstanceSize().name())));
  }

  @Test
  public void testIsPausedWithinDuration() {
    // Create a cluster that is paused within one day from the current system date
    final Date now = new Date();
    final ClusterDescription pausedCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setIsPaused(true)
            .setPausedDate(now)
            .build();

    // Test with different durations
    assertTrue(pausedCluster.isPausedWithinDuration(java.time.Duration.ofDays(1)));
    assertTrue(pausedCluster.isPausedWithinDuration(java.time.Duration.ofHours(1)));

    // Create a cluster that was paused a while ago
    final Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DAY_OF_MONTH, -10);
    final Date oldDate = cal.getTime();

    final ClusterDescription oldPausedCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setIsPaused(true)
            .setPausedDate(oldDate)
            .build();

    // Should be false for short durations, true for longer ones
    assertFalse(oldPausedCluster.isPausedWithinDuration(java.time.Duration.ofDays(1)));
    assertTrue(oldPausedCluster.isPausedWithinDuration(java.time.Duration.ofDays(20)));

    // Test with a cluster that is not paused
    final ClusterDescription notPausedCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setIsPaused(false)
            .setPausedDate(now)
            .build();

    // Should always be false if not paused
    assertFalse(notPausedCluster.isPausedWithinDuration(java.time.Duration.ofDays(1)));

    // Test with a paused cluster with no paused date
    final ClusterDescription pausedNoDateCluster =
        NDSModelTestFactory.getClusterDescription(CloudProvider.AWS)
            .copy()
            .setIsPaused(true)
            .setPausedDate(null)
            .build();

    // Should be false if no paused date is set
    assertFalse(pausedNoDateCluster.isPausedWithinDuration(java.time.Duration.ofDays(1)));
  }
}
