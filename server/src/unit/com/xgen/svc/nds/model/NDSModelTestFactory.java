package com.xgen.svc.nds.model;

import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.oid;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.withSettings;

import com.amazonaws.services.ec2.model.VolumeType;
import com.azure.core.management.AzureEnvironment;
import com.google.api.services.compute.model.Operation;
import com.google.common.collect.Iterables;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.xgen.cloud.atm.core._public.model.LastAgentStatus;
import com.xgen.cloud.atm.core._public.model.status.EnhancedProcessStatus;
import com.xgen.cloud.common.dao.codec._public.encrypted.string.EncryptedString;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils;
import com.xgen.cloud.common.mongo._public.mongo.DbUtils.BasicDBListCollector;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.cloud.common.security._public.util.KeyPairUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.util._public.util.AgentType;
import com.xgen.cloud.common.util._public.util.NetUtils;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.backupjob._public.model.BackupRetentionUnit;
import com.xgen.cloud.cps.backupjob._public.model.Policy;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.cloud.cps.pit._public.model.CpsOplogSlice;
import com.xgen.cloud.cps.pit._public.model.ProviderStorage;
import com.xgen.cloud.cps.pit._public.ui.CpsOplogIdView;
import com.xgen.cloud.cps.pit._public.ui.CpsOplogMetadataBatch;
import com.xgen.cloud.cps.pit._public.ui.CpsOplogSliceView;
import com.xgen.cloud.cps.pit._public.ui.CpsOplogSliceView.Builder;
import com.xgen.cloud.cps.restore._public.model.AWSBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.AzureBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupReplicaSetType;
import com.xgen.cloud.cps.restore._public.model.BackupRestoreJobDelivery;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshotEncryptionCredentials;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata.Status;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata.StrategyName;
import com.xgen.cloud.cps.restore._public.model.DirectAttachReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.GCPBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.ReplicaSetBackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.RestoreTargetCluster;
import com.xgen.cloud.cps.restore._public.model.ShardedClusterBackupRestoreJob;
import com.xgen.cloud.cps.restore._public.model.SnapshotUpdate;
import com.xgen.cloud.cps.restore._public.model.StreamingReplicaSetRestoreJob;
import com.xgen.cloud.cps.restore._public.model.VMBasedReplSetRestoreJob;
import com.xgen.cloud.deployment._public.model.IndexConfig;
import com.xgen.cloud.deployment._public.model.IndexOptions;
import com.xgen.cloud.group._public.model.AuditLog;
import com.xgen.cloud.group._public.model.GroupVisibility;
import com.xgen.cloud.nds.aws._public.model.AWSAccount;
import com.xgen.cloud.nds.aws._public.model.AWSAdminBackupSnapshot;
import com.xgen.cloud.nds.aws._public.model.AWSAvailabilityZone;
import com.xgen.cloud.nds.aws._public.model.AWSCloudProviderContainer;
import com.xgen.cloud.nds.aws._public.model.AWSHardwareSpec;
import com.xgen.cloud.nds.aws._public.model.AWSInstanceFamily;
import com.xgen.cloud.nds.aws._public.model.AWSLeakedItem;
import com.xgen.cloud.nds.aws._public.model.AWSNDSDefaults;
import com.xgen.cloud.nds.aws._public.model.AWSNDSInstanceSize;
import com.xgen.cloud.nds.aws._public.model.AWSPeerVpc;
import com.xgen.cloud.nds.aws._public.model.AWSPhysicalZoneId;
import com.xgen.cloud.nds.aws._public.model.AWSRegion;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.aws._public.model.AWSSubnet;
import com.xgen.cloud.nds.aws._public.model.NDSAWSKMS;
import com.xgen.cloud.nds.aws._public.model.NDSAWSTempCredentials;
import com.xgen.cloud.nds.aws._public.model.autoscaling.AWSAutoScaling;
import com.xgen.cloud.nds.aws._public.model.autoscaling.ui.AWSAutoScalingView;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSEndpointService;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSKMSEARPrivateEndpoint;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSMultiTargetConnectionRule;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkConnection;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkInterfaceEndpoint;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkInterfaceEndpoint.ConnectionStatus;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSPrivateLinkTargetGroup;
import com.xgen.cloud.nds.aws._public.model.privatelink.AWSTenantEndpointServiceDeployment;
import com.xgen.cloud.nds.azure._public.model.AzureAdminBackupSnapshot;
import com.xgen.cloud.nds.azure._public.model.AzureAvailabilityZone;
import com.xgen.cloud.nds.azure._public.model.AzureAvailabilityZoneName;
import com.xgen.cloud.nds.azure._public.model.AzureCloudProviderContainer;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType;
import com.xgen.cloud.nds.azure._public.model.AzureDiskType.PreferredStorageType;
import com.xgen.cloud.nds.azure._public.model.AzureHardwareSpec;
import com.xgen.cloud.nds.azure._public.model.AzureInstanceFamily;
import com.xgen.cloud.nds.azure._public.model.AzureNDSDefaults;
import com.xgen.cloud.nds.azure._public.model.AzureNDSInstanceSize;
import com.xgen.cloud.nds.azure._public.model.AzurePeerNetwork;
import com.xgen.cloud.nds.azure._public.model.AzurePhysicalZoneId;
import com.xgen.cloud.nds.azure._public.model.AzureRegion;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureSubnet;
import com.xgen.cloud.nds.azure._public.model.AzureSubscription;
import com.xgen.cloud.nds.azure._public.model.NDSAzureKeyVault;
import com.xgen.cloud.nds.azure._public.model.SupportedAzureEnvironment;
import com.xgen.cloud.nds.azure._public.model.admincapacity.AzureInstanceCapacitySpec;
import com.xgen.cloud.nds.azure._public.model.autoscaling.AzureAutoScaling;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzureKeyVaultEARPrivateEndpoint;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzurePrivateEndpoint;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzurePrivateLinkConnection;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzurePrivateLinkConnectionInboundNATRule;
import com.xgen.cloud.nds.azure._public.model.privatelink.AzureTenantEndpointServiceDeployment;
import com.xgen.cloud.nds.capacity._public.model.CapacityDenyListEntry;
import com.xgen.cloud.nds.capacity._public.model.CapacityDenylistStatBucket;
import com.xgen.cloud.nds.capacity._public.model.aws.AWSCapacityDenyListEntry;
import com.xgen.cloud.nds.capacity._public.model.azure.AzureCapacityDenyListEntry;
import com.xgen.cloud.nds.capacity._public.model.gcp.GCPCapacityDenyListEntry;
import com.xgen.cloud.nds.cloudprovider._public.model.AdminBackupSnapshot;
import com.xgen.cloud.nds.cloudprovider._public.model.BaseMultiTargetConnectionRule;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProviderContainer;
import com.xgen.cloud.nds.cloudprovider._public.model.ClusterDescriptionProviderOptions;
import com.xgen.cloud.nds.cloudprovider._public.model.ContainerPeer;
import com.xgen.cloud.nds.cloudprovider._public.model.HardwareSpec;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceFamilyFlags;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceHardware;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.KeyManagementConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMCluster.MTMClusterType;
import com.xgen.cloud.nds.cloudprovider._public.model.MTMSentinel;
import com.xgen.cloud.nds.cloudprovider._public.model.NDSInstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeTypeFamily;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.ShardRegionConfig;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoIndexing;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.AutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.ComputeAutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.autoscaling.DiskGBAutoScaling;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.BaseEndpointService;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.CloudProviderPrivateEndpoint;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.DedicatedEndpointService;
import com.xgen.cloud.nds.cloudprovider._public.model.privatelink.PrivateLinkConnectionRule.Usage;
import com.xgen.cloud.nds.common._public.model.BiConnectorReadPreference;
import com.xgen.cloud.nds.common._public.model.ComplianceLevel;
import com.xgen.cloud.nds.common._public.model.ConfigServerType;
import com.xgen.cloud.nds.common._public.model.CpuArchitecture;
import com.xgen.cloud.nds.common._public.model.Hostnames;
import com.xgen.cloud.nds.common._public.model.InstanceHostname;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.HostnameScheme;
import com.xgen.cloud.nds.common._public.model.InstanceHostname.SubdomainLevel;
import com.xgen.cloud.nds.common._public.model.Limits;
import com.xgen.cloud.nds.common._public.model.Limits.Defaults;
import com.xgen.cloud.nds.common._public.model.NDSLabel;
import com.xgen.cloud.nds.common._public.model.OS;
import com.xgen.cloud.nds.common._public.model.OperationalLimits;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.common._public.model.ThrottleState;
import com.xgen.cloud.nds.common._public.view.NDSLabelView;
import com.xgen.cloud.nds.dbcheck._public.model.ClusterInfo;
import com.xgen.cloud.nds.dbcheck._public.model.DbCheck;
import com.xgen.cloud.nds.dbcheck._public.model.Failure;
import com.xgen.cloud.nds.dbcheck._public.model.IncompleteValidation;
import com.xgen.cloud.nds.dbcheck._public.model.Inconsistency;
import com.xgen.cloud.nds.dbcheck._public.model.OperationStatus;
import com.xgen.cloud.nds.dbcheck._public.model.RunStatus;
import com.xgen.cloud.nds.dbcheck._public.model.ShardStatus;
import com.xgen.cloud.nds.dbcheck._public.model.hostLevelStatus.HostLevelStatus;
import com.xgen.cloud.nds.dbcheck._public.model.hostLevelStatus.HostLevelStatus.HostLevelStatusId;
import com.xgen.cloud.nds.flex._public.model.FastFlexPreAllocatedRecord;
import com.xgen.cloud.nds.flex._public.model.FlexCloudProviderContainer;
import com.xgen.cloud.nds.flex._public.model.FlexHardwareSpec;
import com.xgen.cloud.nds.flex._public.model.FlexInstanceSize;
import com.xgen.cloud.nds.flex._public.model.FlexNDSDefaults;
import com.xgen.cloud.nds.flex._public.model.FlexTenantProviderOptions;
import com.xgen.cloud.nds.flex._public.model.LinkedFlexMTM;
import com.xgen.cloud.nds.flex._public.model.NDSFlexMTMProfile;
import com.xgen.cloud.nds.flex._public.model.autoscaling.FlexAutoScaling;
import com.xgen.cloud.nds.free._public.model.FastSharedPreAllocatedRecord;
import com.xgen.cloud.nds.free._public.model.FreeCloudProviderContainer;
import com.xgen.cloud.nds.free._public.model.FreeHardwareSpec;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize;
import com.xgen.cloud.nds.free._public.model.FreeInstanceSize.LimitsProfile;
import com.xgen.cloud.nds.free._public.model.FreeTenantProviderOptions;
import com.xgen.cloud.nds.free._public.model.LinkedSharedMTM;
import com.xgen.cloud.nds.free._public.model.NDSSharedMTMProfile;
import com.xgen.cloud.nds.free._public.model.autoscaling.FreeAutoScaling;
import com.xgen.cloud.nds.fts._public.model.FTSIndex;
import com.xgen.cloud.nds.fts._public.model.FTSSearchIndex;
import com.xgen.cloud.nds.gcp._public.model.GCPAdminBackupSnapshot;
import com.xgen.cloud.nds.gcp._public.model.GCPAtlasSubnet;
import com.xgen.cloud.nds.gcp._public.model.GCPCloudProviderContainer;
import com.xgen.cloud.nds.gcp._public.model.GCPHardwareSpec;
import com.xgen.cloud.nds.gcp._public.model.GCPInstanceFamily;
import com.xgen.cloud.nds.gcp._public.model.GCPInternalLoadBalancer;
import com.xgen.cloud.nds.gcp._public.model.GCPNATSubnet;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSDefaults;
import com.xgen.cloud.nds.gcp._public.model.GCPNDSInstanceSize;
import com.xgen.cloud.nds.gcp._public.model.GCPOrganization;
import com.xgen.cloud.nds.gcp._public.model.GCPPeerVpc;
import com.xgen.cloud.nds.gcp._public.model.GCPPhysicalZoneId;
import com.xgen.cloud.nds.gcp._public.model.GCPPrivateServiceConnection;
import com.xgen.cloud.nds.gcp._public.model.GCPRegion;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.nds.gcp._public.model.GCPZone;
import com.xgen.cloud.nds.gcp._public.model.GCPZoneName;
import com.xgen.cloud.nds.gcp._public.model.NDSGoogleCloudKMS;
import com.xgen.cloud.nds.gcp._public.model.autoscaling.GCPAutoScaling;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPConsumerForwardingRule;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPPrivateServiceConnectEndpointGroup;
import com.xgen.cloud.nds.gcp._public.model.privatelink.GCPPrivateServiceConnectRegionGroup;
import com.xgen.cloud.nds.project._public.model.AutoScalingMode;
import com.xgen.cloud.nds.project._public.model.CheckMetadataConsistency;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterProvisionType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ClusterType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.FieldDefs;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.InternalClusterRole;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.ProcessRestartAllowedState;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.RootCertType;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.VersionReleaseSystem;
import com.xgen.cloud.nds.project._public.model.ConfigServerReplicationSpec;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionOperationOrigin;
import com.xgen.cloud.nds.project._public.model.CorruptionDetectionRunResult;
import com.xgen.cloud.nds.project._public.model.DedicatedConfigServerReplicationSpec;
import com.xgen.cloud.nds.project._public.model.EmbeddedConfigServerReplicationSpec;
import com.xgen.cloud.nds.project._public.model.FTDCExport;
import com.xgen.cloud.nds.project._public.model.FlexTenantMigrationState;
import com.xgen.cloud.nds.project._public.model.GeoSharding;
import com.xgen.cloud.nds.project._public.model.HealthCheckMetadata;
import com.xgen.cloud.nds.project._public.model.NDSDefaults;
import com.xgen.cloud.nds.project._public.model.NDSEncryptionAtRest;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.NDSGroupMaintenanceWindow;
import com.xgen.cloud.nds.project._public.model.PushBasedLogExport;
import com.xgen.cloud.nds.project._public.model.RegionSpec;
import com.xgen.cloud.nds.project._public.model.ReplicaSetHardware;
import com.xgen.cloud.nds.project._public.model.ReplicationSpec;
import com.xgen.cloud.nds.project._public.model.SecureSSHKey;
import com.xgen.cloud.nds.project._public.model.ShardedClusterDescription;
import com.xgen.cloud.nds.project._public.model.cloudprovideraccess.NDSCloudProviderAccess;
import com.xgen.cloud.nds.project._public.model.dbusers.NDSDBRole;
import com.xgen.cloud.nds.project._public.model.dbusers.NDSDBUser;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermission;
import com.xgen.cloud.nds.project._public.model.networkpermission.NDSNetworkPermissionList;
import com.xgen.cloud.nds.project._public.model.privatenetwork.NDSPrivateNetworkSettings;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSCustomerX509;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSLDAP;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSManagedX509;
import com.xgen.cloud.nds.project._public.model.usersecurity.NDSUserSecurity;
import com.xgen.cloud.nds.project._public.model.versions.FixedAgentVersion;
import com.xgen.cloud.nds.security._public.model.NDSACMECert;
import com.xgen.cloud.nds.security._public.model.NDSAcmeCAStatus;
import com.xgen.cloud.nds.serverless._public.model.NDSServerlessMTMProfile;
import com.xgen.cloud.nds.serverless._public.model.ServerlessBackupOptions;
import com.xgen.cloud.nds.serverless._public.model.ServerlessCloudProviderContainer;
import com.xgen.cloud.nds.serverless._public.model.ServerlessHardwareSpec;
import com.xgen.cloud.nds.serverless._public.model.ServerlessInstanceSize;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster;
import com.xgen.cloud.nds.serverless._public.model.ServerlessMTMCluster.Range;
import com.xgen.cloud.nds.serverless._public.model.ServerlessNDSDefaults;
import com.xgen.cloud.nds.serverless._public.model.ServerlessTenantProviderOptions;
import com.xgen.cloud.nds.serverless._public.model.autoscaling.ServerlessAutoScaling;
import com.xgen.cloud.nds.tenant._public.model.FastInstance;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantBackupTask;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantBackupTask.SnapshotType;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantBackupTask.State;
import com.xgen.cloud.nds.tenant._public.model.backup.TenantRestore;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasDedicatedHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasDedicatedRegionConfig20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasHardwareSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.api_2024_08_05.ApiAtlasReplicationSpec20240805View;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasAutoScalingV15View;
import com.xgen.svc.mms.api.view.atlas.autoScaling.ApiAtlasComputeAutoScalingV15View;
import com.xgen.svc.nds.free.FreeNDSDefaults;
import com.xgen.svc.nds.gcp.svc.GCPPrivateServiceConnectSvc;
import com.xgen.svc.nds.model.ui.BiConnectorView;
import com.xgen.svc.nds.svc.TemporaryCredentialSvc;
import com.xgen.svc.nds.svc.project.NDSClusterSvc;
import com.xgen.svc.security.acme.model.ACMECert;
import com.xgen.svc.security.acme.model.ACMEProvider;
import dev.morphia.mapping.Mapper;
import inet.ipaddr.IPAddress;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.Random;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;
import org.json.JSONArray;
import org.json.JSONObject;

public class NDSModelTestFactory {

  public static final String TEST_FREE_MONGODB_MAJOR_VERSION = "8.0";
  public static final String TEST_FREE_MONGODB_VERSION = "8.0.3";
  public static final String TEST_FLEX_MONGODB_MAJOR_VERSION = "8.0";
  public static final String TEST_FLEX_MONGODB_VERSION = "8.0.0";
  public static final String TEST_SERVERLESS_MONGODB_MAJOR_VERSION = "8.0";
  public static final String TEST_SERVERLESS_MONGODB_VERSION = "8.0.3";
  public static final String TEST_PREVIOUS_DEFAULT_DEDICATED_MAJOR_VERSION =
      NDSDefaults.PREVIOUS_MONGODB_MAJOR_VERSION;
  public static final String TEST_PREVIOUS_DEFAULT_DEDICATED_VERSION = "7.0.4";
  public static final String TEST_DEDICATED_MONGODB_MAJOR_VERSION =
      NDSDefaults.DEFAULT_MONGODB_MAJOR_VERSION;
  public static final String TEST_DEDICATED_MONGODB_VERSION = "8.0.3";
  public static final String DEFAULT_DNS_PIN = "abc1234";
  public static final String TEST_AWS_EAST_PRIVATE_ENDPOINT_ID_NAME = "vpce-us-east-endpoint";
  public static final String TEST_AWS_WEST_PRIVATE_ENDPOINT_ID_NAME = "vpce-us-west-endpoint";
  public static final List<Version> TEST_MONGOD_VERSIONS =
      List.of(
          VersionUtils.TWO_FOUR_ZERO,
          VersionUtils.TWO_SIX_ZERO,
          VersionUtils.THREE_ZERO_ZERO,
          VersionUtils.THREE_TWO_ZERO,
          VersionUtils.THREE_FOUR_ZERO,
          VersionUtils.THREE_FIVE_ZERO,
          VersionUtils.THREE_SIX_ZERO,
          VersionUtils.THREE_SEVEN_ZERO,
          VersionUtils.FOUR_ZERO_ZERO,
          VersionUtils.FOUR_ONE_ZERO,
          VersionUtils.FOUR_TWO_ZERO,
          VersionUtils.FIVE_ZERO_ZERO);
  public static final String DEFAULT_CLUSTER_NAME = "foo";
  public static final String DEFAULT_GCP_PROJECT_ID = "fakeProjectId";
  public static final int DEFAULT_MULTI_TARGET_CONNECTION_RULE_FRONTEND_PORT = 12345;
  public static final String AZURE_PRIVATE_ENDPOIONT_CONNECTION_NAME = "connectionName";
  private static final Mapper _morphiaMapper = new Mapper();
  public static VersionUtils.Version TEST_MONGODB_VERSION = VersionUtils.parse("6.0.18");
  public static VersionUtils.Version MONGODB_VERSION_DEPRECATED = VersionUtils.FIVE_ZERO_ZERO;
  public static VersionUtils.Version MONGODB_VERSION_CURRENT = VersionUtils.SIX_ZERO_ZERO;
  public static VersionUtils.Version MONGODB_VERSION_NEXT = VersionUtils.SEVEN_ZERO_ZERO;
  public static VersionUtils.Version MONGODB_VERSION_UPCOMING = VersionUtils.EIGHT_ZERO_ZERO;
  public static VersionUtils.Version MONGODB_VERSION_SEARCH_QUERY_TELEMETRY =
      VersionUtils.FIVE_ZERO_SIXTEEN;

  public static String DEFAULT_OS_POLICY_VERSION = "163";
  public static String DEFAULT_MONGOTUNE_VERSION = "1.0.2";

  public static BasicDBObject getFullyAvailableAWSAccount() {
    return getFullyAvailableAWSAccount(false);
  }

  public static BasicDBObject getFullyAvailableAWSAccount(final boolean pExcludeGlobalRegion) {
    return getFullyAvailableAWSAccount(pExcludeGlobalRegion, null);
  }

  public static AWSAvailabilityZone getMockAWSAvailabilityZone(
      final AWSRegionName pRegionName, final int pIndex) {
    // hacky guesstimate of a zone id
    final List<String> nameSegments = List.of(pRegionName.getValue().toLowerCase().split("-"));
    final AWSPhysicalZoneId zoneId =
        new AWSPhysicalZoneId(
            nameSegments.get(0).substring(0, 2)
                + String.join(
                    "",
                    nameSegments.subList(1, nameSegments.size()).stream()
                        .map(s -> s.substring(0, 1))
                        .toList())
                + "-az"
                + (pIndex + 1));
    return new AWSAvailabilityZone(
        pRegionName.getValue() + (char) ('a' + pIndex),
        AWSAvailabilityZone.Status.AVAILABLE,
        new Date(),
        zoneId);
  }

  public static BasicDBObject getFullyAvailableAWSAccount(
      final boolean pExcludeGlobalRegion, final ComplianceLevel pComplianceLevel) {
    final BasicDBList regions = new BasicDBList();
    Arrays.stream(AWSRegionName.values())
        .filter(awsRegionName -> !(awsRegionName == AWSRegionName.GLOBAL && pExcludeGlobalRegion))
        .forEach(
            regionName -> {
              final AWSRegion region = new AWSRegion(regionName);
              region.getAvailabilityZones().add(getMockAWSAvailabilityZone(regionName, 0));
              regions.add(region.toDBObject());
            });

    final BasicDBList internalSSHCidrs = new BasicDBList();
    internalSSHCidrs.add("0.0.0.0/0");

    try {
      final BasicDBObject account =
          new BasicDBObject()
              .append(AWSAccount.FieldDefs.ID, new ObjectId())
              .append(
                  AWSAccount.FieldDefs.NAME, "foobar" + RandomStringUtils.randomAlphanumeric(16))
              .append(AWSAccount.FieldDefs.CREATED, new Date())
              .append(AWSAccount.FieldDefs.LAST_UPDATED, new Date())
              .append(AWSAccount.FieldDefs.ASSIGNMENT_ENABLED, true)
              .append(AWSAccount.FieldDefs.ACCESS_KEY, EncryptionUtils.genEncryptStr("accesskeyz"))
              .append(AWSAccount.FieldDefs.SECRET_KEY, EncryptionUtils.genEncryptStr("supersecret"))
              .append(AWSAccount.FieldDefs.CAPACITY_REMAINING, 200)
              .append(AWSAccount.FieldDefs.REGIONS, regions)
              .append(AWSAccount.FieldDefs.INTERNAL_SSH_CIDRS, internalSSHCidrs);
      Optional.ofNullable(pComplianceLevel)
          .map(Enum::name)
          .ifPresent(c -> account.append(AWSAccount.FieldDefs.AWS_ENV, c));
      return account;
    } catch (final Exception e) {
      throw new IllegalArgumentException("Failed to encrypt", e);
    }
  }

  public static AWSAccount getFullyAvailableAWSAccount4RegionsWithCreds(
      final ObjectId pAccountId,
      final String pAccountName,
      final boolean pForCapacityCheck,
      final String pAccessKey,
      final String pSecretKey,
      final ComplianceLevel pEnv) {
    final AWSRegion usEast2 = new AWSRegion(AWSRegionName.US_EAST_2);
    usEast2.getGroupIds().add(new ObjectId());
    usEast2
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                "us-east-2a",
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                new AWSPhysicalZoneId("use2-az1")));
    usEast2
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                "us-east-2b",
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                new AWSPhysicalZoneId("use2-az2")));
    usEast2
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                "us-east-2c",
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                new AWSPhysicalZoneId("use2-az3")));

    final AWSRegion apNorthEast1 = new AWSRegion(AWSRegionName.AP_NORTHEAST_1);
    apNorthEast1.getGroupIds().add(new ObjectId());
    apNorthEast1
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                "ap-northeast-1a",
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                new AWSPhysicalZoneId("apne1-az1")));
    apNorthEast1
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                "ap-northeast-1c",
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                new AWSPhysicalZoneId("apne1-az2")));
    apNorthEast1
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                "ap-northeast-1d",
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                new AWSPhysicalZoneId("apne1-az3")));

    final AWSRegion apNorthEast2 = new AWSRegion(AWSRegionName.AP_NORTHEAST_2);
    apNorthEast2.getGroupIds().add(new ObjectId());
    apNorthEast2
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                "ap-northeast-2a",
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                new AWSPhysicalZoneId("apne2-az1")));
    apNorthEast2
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                "ap-northeast-2c",
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                new AWSPhysicalZoneId("apne2-az2")));

    final AWSRegion euWest1 = new AWSRegion(AWSRegionName.EU_WEST_1);
    euWest1.getGroupIds().add(new ObjectId());
    euWest1
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                "eu-west-1a",
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                new AWSPhysicalZoneId("euw1-az1")));
    euWest1
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                "eu-west-1b",
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                new AWSPhysicalZoneId("euw1-az2")));
    euWest1
        .getAvailabilityZones()
        .add(
            new AWSAvailabilityZone(
                "eu-west-1c",
                AWSAvailabilityZone.Status.AVAILABLE,
                new Date(),
                new AWSPhysicalZoneId("euw1-az3")));

    final BasicDBList regions = new BasicDBList();
    regions.add(usEast2.toDBObject());
    regions.add(apNorthEast1.toDBObject());
    regions.add(apNorthEast2.toDBObject());
    regions.add(euWest1.toDBObject());

    final BasicDBList internalSSHCidrs = new BasicDBList();
    internalSSHCidrs.add("0.0.0.0/0");

    try {
      return new AWSAccount(
          new BasicDBObject()
              .append(AWSAccount.FieldDefs.ID, pAccountId)
              .append(AWSAccount.FieldDefs.NAME, pAccountName)
              .append(AWSAccount.FieldDefs.FOR_CAPACITY_CHECK, pForCapacityCheck)
              .append(AWSAccount.FieldDefs.CREATED, Date.from(Instant.EPOCH))
              .append(AWSAccount.FieldDefs.LAST_UPDATED, Date.from(Instant.EPOCH))
              .append(AWSAccount.FieldDefs.ASSIGNMENT_ENABLED, true)
              .append(AWSAccount.FieldDefs.ACCESS_KEY, EncryptionUtils.genEncryptStr(pAccessKey))
              .append(AWSAccount.FieldDefs.SECRET_KEY, EncryptionUtils.genEncryptStr(pSecretKey))
              .append(AWSAccount.FieldDefs.CAPACITY_REMAINING, 200)
              .append(AWSAccount.FieldDefs.REGIONS, regions)
              .append(AWSAccount.FieldDefs.INTERNAL_SSH_CIDRS, internalSSHCidrs)
              .append(
                  AWSAccount.FieldDefs.AWS_ENV,
                  Optional.ofNullable(pEnv).orElse(ComplianceLevel.COMMERCIAL).name()));
    } catch (final Exception e) {
      throw new IllegalArgumentException("Failed create AWSAccount", e);
    }
  }

  public static AWSAccount getFullyAvailableAWSAccount4RegionsWithCreds(
      final ObjectId pAccountId,
      final String pAccountName,
      final boolean pForCapacityCheck,
      final String pAccessKey,
      final String pSecretKey) {
    return getFullyAvailableAWSAccount4RegionsWithCreds(
        pAccountId,
        pAccountName,
        pForCapacityCheck,
        pAccessKey,
        pSecretKey,
        ComplianceLevel.COMMERCIAL);
  }

  public static AWSAccount getFullyAvailableAWSAccount4Regions(
      final ObjectId pAccountId, final String pAccountName, final boolean pForCapacityCheck) {
    return getFullyAvailableAWSAccount4RegionsWithCreds(
        pAccountId, pAccountName, pForCapacityCheck, "accesskeyz", "supersecret");
  }

  public static BasicDBObject getFullyAvailableAzureSubscription() {
    return new AzureSubscription(
            new ObjectId(),
            "test" + RandomStringUtils.randomAlphanumeric(16),
            new Date(),
            new Date(),
            "fakeId",
            "fakeId",
            "fakeId" + RandomStringUtils.randomAlphanumeric(16),
            "fakeSecret",
            null,
            null,
            null,
            SupportedAzureEnvironment.AZURE,
            new ArrayList<>(),
            Collections.emptyList(),
            true,
            false,
            false,
            false,
            false)
        .toDBObject()
        .append(
            AzureSubscription.FieldDefs.REGIONS,
            AzureRegionName.findRegionsByEnvironment(SupportedAzureEnvironment.AZURE).stream()
                .map(
                    regionName ->
                        new AzureRegion(
                                regionName,
                                List.of(
                                    new AzureAvailabilityZone(
                                        AzureAvailabilityZoneName.ZONE_1,
                                        new AzurePhysicalZoneId("fakeZone2")),
                                    new AzureAvailabilityZone(
                                        AzureAvailabilityZoneName.ZONE_2,
                                        new AzurePhysicalZoneId("fakeZone1")),
                                    new AzureAvailabilityZone(
                                        AzureAvailabilityZoneName.ZONE_3,
                                        new AzurePhysicalZoneId("fakeZone3"))))
                            .toDBObject())
                .collect(DbUtils.toBasicDBList()));
  }

  public static BasicDBObject getFullyAvailableGCPOrganization() {
    return getFullyAvailableGCPOrganizationWithCredentials("fakeCredentials");
  }

  private static List<GCPRegion> getAllRegionsWithAvailableZones() {
    return Arrays.stream(GCPRegionName.values())
        .map(
            regionName -> {
              final GCPRegion region = new GCPRegion(regionName);
              region
                  .getZones()
                  .addAll(
                      Arrays.stream(GCPZoneName.values())
                          .filter(z -> z.getRegion().equals(regionName))
                          .map(
                              z ->
                                  new GCPZone(
                                      GCPZoneName.valueOf(z.name()), GCPZone.Status.AVAILABLE))
                          .toList());
              return region;
            })
        .collect(Collectors.toList());
  }

  private static BasicDBObject getFullyAvailableGCPOrganizationWithCredentials(
      final String pCredentials) {
    return new GCPOrganization("test", 1000, "testFolder", pCredentials, "fakeId")
        .toDBObject()
        .append(
            GCPOrganization.FieldDefs.REGIONS,
            getAllRegionsWithAvailableZones().stream()
                .map(GCPRegion::toDBObject)
                .collect(DbUtils.toBasicDBList()));
  }

  public static GCPOrganization getFullyAvailableGCPOrganizationWithServiceAccount(
      final String pServiceAccountName, final List<String> pScopes) {
    return getFullyAvailableGCPOrganizationWithServiceAccount(null, pServiceAccountName, pScopes);
  }

  public static GCPOrganization getFullyAvailableGCPOrganizationWithServiceAccount(
      final String pCredentials, final String pServiceAccountName, final List<String> pScopes) {
    return new GCPOrganization("test", 1000, "testFolder", pCredentials, "fakeId")
        .toBuilder()
            .regions(getAllRegionsWithAvailableZones())
            .serviceAccountResourceName(pServiceAccountName)
            .serviceAccountScopes(pScopes)
            .build();
  }

  public static GCPOrganization getFullyAvailableGovGCPOrganizationWithCredentials() {
    return GCPOrganization.builder()
        .name("test")
        .capacityRemaining(1000)
        .projectsFolder("testFolder")
        .credentials("fakeCredentials")
        .billingAccountId("fakeId")
        .assignmentEnabled(true)
        .complianceLevel(ComplianceLevel.US_GOV)
        .regions(getAllRegionsWithAvailableZones())
        .build();
  }

  public static BasicDBObject getFullyAvailableGCPOrganizationWithCredentials() {
    return getFullyAvailableGCPOrganizationWithCredentials(getGCPOrganizationCredentials());
  }

  public static String getGCPOrganizationCredentials() {
    final KeyPair keyPair = KeyPairUtils.createKeyPair(512);
    final PrivateKey privateKey = keyPair.getPrivate();
    final Base64.Encoder encoder = Base64.getEncoder();
    final String privateKeyEncoded = encoder.encodeToString(privateKey.getEncoded());

    return "{"
        + "\"type\": \"service_account\","
        + "\"project_id\": \"project-id\","
        + "\"private_key_id\": \"key-id\","
        + "\"private_key\": \"-----BEGIN PRIVATE KEY-----\\n"
        + privateKeyEncoded
        + "\\n"
        + "-----END PRIVATE KEY-----\",\"client_email\":"
        + " \"service-account-email\",\"client_id\": \"client-id\",\"auth_uri\":"
        + " \"https://accounts.google.com/o/oauth2/auth\",\"token_uri\":"
        + " \"https://accounts.google.com/o/oauth2/token\",\"auth_provider_x509_cert_url\":"
        + " \"https://www.googleapis.com/oauth2/v1/certs\",\"client_x509_cert_url\":"
        + " \"https://www.googleapis.com/robot/v1/metadata/x509/service-account-email\"}";
  }

  public static Operation getGCPOperationWithError(final String pErrorCode) {
    final Operation.Error.Errors errors = new Operation.Error.Errors();
    errors.setCode(pErrorCode);

    final Operation.Error error = new Operation.Error();
    error.setErrors(List.of(errors));

    return new Operation().setError(error);
  }

  public static NDSGroup getAzureMockedGroup() {
    return getAzureMockedGroup(ObjectId.get());
  }

  public static NDSGroup getAzureMockedGroupWithPrivateLink() {
    return getAzureMockedGroup(ObjectId.get(), true, false);
  }

  public static NDSGroup getAzureMockedGroupWithTenantEndpointServiceDeployment(
      final ObjectId groupId) {
    return getAzureMockedGroup(groupId, false, true);
  }

  public static NDSGroup getAzureMockedGroup(final ObjectId groupId) {
    return getAzureMockedGroup(groupId, false, false);
  }

  public static CloudProviderContainer getCloudProviderContainerByCloudProvider(
      final CloudProvider pCloudProvider) {
    switch (pCloudProvider) {
      case AWS:
        return new AWSCloudProviderContainer(getAWSContainer());
      case AZURE:
        return new AzureCloudProviderContainer(getAzureContainer());
      case GCP:
        return new GCPCloudProviderContainer(getGCPContainer());
      case FREE:
        return new FreeCloudProviderContainer(getFreeContainer());
      case SERVERLESS:
        return new ServerlessCloudProviderContainer(getServerlessContainer());
      case FLEX:
        return new FlexCloudProviderContainer(getFlexContainer());
      default:
        throw new IllegalArgumentException(
            String.format("Cloud provider not supported: %s", pCloudProvider));
    }
  }

  public static CloudProviderContainer getCloudProviderContainerByRegion(
      final RegionName pRegionName) {
    switch (pRegionName.getProvider()) {
      case AWS:
        return new AWSCloudProviderContainer(getAWSContainer((AWSRegionName) pRegionName));
      case AZURE:
        return new AzureCloudProviderContainer(getAzureContainer((AzureRegionName) pRegionName));
      case GCP:
        return new GCPCloudProviderContainer(getGCPContainer());
      default:
        throw new IllegalArgumentException(
            String.format("Cloud provider not supported: %s", pRegionName.getProvider()));
    }
  }

  public static NDSGroup getAzureMockedGroup(
      final ObjectId pGroupId,
      final boolean pHasPrivateLink,
      final boolean pHasTenantEndpointDeployment) {
    final NDSGroup group = mock(NDSGroup.class, withSettings().lenient());
    final OperationalLimits ol = spy(new OperationalLimits());
    when(group.getCloudProviderType(any())).thenReturn(CloudProvider.AZURE);
    when(group.getGroupId()).thenReturn(pGroupId);
    when(group.getDNSPin()).thenReturn("123");
    when(group.getNetworkPermissionList())
        .thenReturn(
            new NDSNetworkPermissionList(
                NDSNetworkPermission.getPermissionsFromStrings("***************/32")));
    when(group.getOperationalLimits()).thenReturn(ol);
    when(group.getPushBasedLogExport()).thenReturn(new PushBasedLogExport());

    final CloudProvider cloudProvider = CloudProvider.AZURE;
    final AzureCloudProviderContainer cloudProviderContainer =
        mock(AzureCloudProviderContainer.class, withSettings().lenient());
    when(cloudProviderContainer.getAzureSubscriptionId()).thenReturn(new ObjectId());
    when(cloudProviderContainer.getCloudProvider()).thenReturn(cloudProvider);
    when(cloudProviderContainer.getAtlasCidr()).thenReturn(NDSDefaults.AZURE_ATLAS_CIDR);

    final AzureRegionName regionName = AzureRegionName.US_EAST_2;
    when(cloudProviderContainer.getRegion()).thenReturn(regionName);
    when(cloudProviderContainer.isResponsibleForThisRegion(AzureRegionName.US_EAST_2))
        .thenReturn(true);
    when(cloudProviderContainer.getId()).thenReturn(new ObjectId());
    if (pHasPrivateLink) {
      final AzurePrivateLinkConnection privateLinkConnection =
          mock(AzurePrivateLinkConnection.class);
      when(cloudProviderContainer.getEndpointServices()).thenReturn(List.of(privateLinkConnection));
    }
    if (pHasTenantEndpointDeployment) {
      when(group.isServerlessMTMHolder()).thenReturn(true);
      final AzureTenantEndpointServiceDeployment tenantEndpointServiceDeployment =
          mock(AzureTenantEndpointServiceDeployment.class);
      when(group.getCloudProviderContainerWithTenantEndpointServiceDeployment())
          .thenReturn(Optional.of(cloudProviderContainer));
      when(cloudProviderContainer.getTenantEndpointServiceDeployment())
          .thenReturn(Optional.of(tenantEndpointServiceDeployment));
    }

    when(group.getCloudProviderContainer(eq(cloudProviderContainer.getId())))
        .thenReturn(Optional.of(cloudProviderContainer));
    when(group.getCloudProviderContainers())
        .thenReturn(Collections.singletonList(cloudProviderContainer));
    when(group.getCloudProviderContainer(eq(cloudProvider), eq(regionName), any()))
        .thenReturn(Optional.of(cloudProviderContainer));
    when(group.getCloudProviderContainersByType(eq(cloudProvider)))
        .thenReturn(Collections.singletonList(cloudProviderContainer));

    final NDSUserSecurity ndsUserSecurity = getMockedUserSecurity();
    when(group.getUserSecurity()).thenReturn(ndsUserSecurity);

    final NDSEncryptionAtRest encryptionAtRest = getEmptyEncryptionAtRest();
    when(group.getEncryptionAtRest()).thenReturn(encryptionAtRest);

    final AuditLog auditLog = getMockedAuditLog();
    when(group.getAuditLog()).thenReturn(auditLog);

    when(group.getFTDCExport()).thenReturn(new FTDCExport());

    return group;
  }

  public static AzureCapacityDenyListEntry getAzureCapacityDenyListEntry(
      final AzureRegionName pRegionName, final AzureNDSInstanceSize pInstanceSize) {
    final Date now = new Date();
    return new AzureCapacityDenyListEntry(
        pRegionName,
        pInstanceSize,
        (AzureInstanceFamily)
            Iterables.getLast(pInstanceSize.getAvailableFamilies().get(pRegionName)),
        Optional.of(new AzurePhysicalZoneId("fakeZoneId")),
        now,
        CapacityDenyListEntry.Status.CAPACITY_UNAVAILABLE_OVERRIDE,
        Optional.of("CLOUDP-12345"),
        List.of(new CapacityDenylistStatBucket(now.toInstant(), 42, 13)));
  }

  public static NDSGroup getGCPMockedGroup() {
    return getGCPMockedGroup(new ObjectId());
  }

  public static NDSGroup getGCPMockedGroup(final ObjectId pGroupId) {
    return getGCPMockedGroup(pGroupId, false);
  }

  public static NDSGroup getGCPMockedGroupWithPrivateServiceConnect() {
    return getGCPMockedGroup(ObjectId.get(), true);
  }

  public static NDSGroup getGCPMockedGroup(
      final ObjectId pGroupId, final boolean pHasPrivateServiceConnect) {
    final NDSGroup group = mock(NDSGroup.class, withSettings().lenient());
    final OperationalLimits ol = spy(new OperationalLimits());
    when(group.getCloudProviderType(any())).thenReturn(CloudProvider.GCP);
    when(group.getGroupId()).thenReturn(pGroupId);
    when(group.getDNSPin()).thenReturn("123");
    when(group.getNetworkPermissionList())
        .thenReturn(
            new NDSNetworkPermissionList(
                NDSNetworkPermission.getPermissionsFromStrings("***************/32")));
    when(group.getOperationalLimits()).thenReturn(ol);
    when(group.getPushBasedLogExport()).thenReturn(new PushBasedLogExport());

    final CloudProvider cloudProvider = CloudProvider.GCP;
    final GCPCloudProviderContainer cloudProviderContainer =
        mock(GCPCloudProviderContainer.class, withSettings().lenient());
    when(cloudProviderContainer.getCloudProvider()).thenReturn(cloudProvider);

    when(cloudProviderContainer.getId()).thenReturn(new ObjectId());
    when(cloudProviderContainer.getGcpOrganizationId()).thenReturn(new ObjectId());
    when(cloudProviderContainer.getGcpProjectId()).thenReturn(Optional.of(DEFAULT_GCP_PROJECT_ID));
    final GCPAtlasSubnet gcpSubnet = mock(GCPAtlasSubnet.class, withSettings().lenient());
    when(gcpSubnet.getCidrBlock()).thenReturn("***************/32");

    final GCPRegionName regionName = GCPNDSDefaults.REGION_NAME;
    when(gcpSubnet.getRegion()).thenReturn(regionName);
    when(cloudProviderContainer.getSubnets()).thenReturn(Collections.singletonList(gcpSubnet));

    if (pHasPrivateServiceConnect) {
      final GCPPrivateServiceConnectRegionGroup regionGroup =
          mock(GCPPrivateServiceConnectRegionGroup.class);
      when(regionGroup.getRegionName()).thenReturn(Optional.of(regionName));
      when(cloudProviderContainer.getEndpointServices()).thenReturn(List.of(regionGroup));
    }

    when(group.getCloudProviderContainers())
        .thenReturn(Collections.singletonList(cloudProviderContainer));
    when(group.getCloudProviderContainer(eq(cloudProviderContainer.getId())))
        .thenReturn(Optional.of(cloudProviderContainer));
    when(group.getCloudProviderContainerByType(cloudProvider))
        .thenReturn(Optional.of(cloudProviderContainer));
    when(group.getCloudProviderContainer(eq(cloudProvider), eq(regionName), any()))
        .thenReturn(Optional.of(cloudProviderContainer));

    final NDSUserSecurity ndsUserSecurity = getMockedUserSecurity();
    when(group.getUserSecurity()).thenReturn(ndsUserSecurity);

    final NDSEncryptionAtRest encryptionAtRest = getEmptyEncryptionAtRest();
    when(group.getEncryptionAtRest()).thenReturn(encryptionAtRest);

    final AuditLog auditLog = getMockedAuditLog();
    when(group.getAuditLog()).thenReturn(auditLog);
    when(group.getRegionUsageRestrictions()).thenReturn(RegionUsageRestrictions.NONE);

    when(group.getFTDCExport()).thenReturn(new FTDCExport());

    return group;
  }

  public static GCPCapacityDenyListEntry getGCPCapacityDenyListEntry(
      final GCPRegionName pRegionName, final GCPNDSInstanceSize pInstanceSize) {
    final Date now = new Date();
    return new GCPCapacityDenyListEntry(
        pRegionName,
        pInstanceSize,
        (GCPInstanceFamily)
            Iterables.getLast(pInstanceSize.getAvailableFamilies().get(pRegionName)),
        new GCPPhysicalZoneId(GCPZoneName.findByRegionName(pRegionName).get(0)),
        Optional.empty(),
        now,
        CapacityDenyListEntry.Status.CAPACITY_UNAVAILABLE_OVERRIDE,
        Optional.of("CLOUDP-12345"),
        List.of(new CapacityDenylistStatBucket(now.toInstant(), 42, 13)));
  }

  private static NDSUserSecurity getMockedUserSecurity() {
    final NDSUserSecurity ndsUserSecurity = mock(NDSUserSecurity.class, withSettings().lenient());
    final NDSLDAP ndsldap = mock(NDSLDAP.class, withSettings().lenient());
    final NDSCustomerX509 ndsCustomerX509 = mock(NDSCustomerX509.class);
    final NDSManagedX509 ndsManagedX509 = mock(NDSManagedX509.class);
    doReturn(false).when(ndsldap).isAuthorizationEnabled();
    doReturn(false).when(ndsldap).isAuthenticationEnabled();
    doReturn(Optional.empty()).when(ndsldap).getBindUsername();
    doReturn(Optional.empty()).when(ndsldap).getBindPassword();
    doReturn(Optional.empty()).when(ndsldap).getPort();
    doReturn(Optional.empty()).when(ndsldap).getCaCertificate();

    doReturn(ndsldap).when(ndsUserSecurity).getLDAP();
    doReturn(ndsCustomerX509).when(ndsUserSecurity).getCustomerX509();
    doReturn(ndsManagedX509).when(ndsUserSecurity).getManagedX509();

    return ndsUserSecurity;
  }

  public static NDSEncryptionAtRest getMockedEncryptionAtRest() {
    final NDSAWSKMS awsKMS = mock(NDSAWSKMS.class);
    final NDSAzureKeyVault azureKeyVault = mock(NDSAzureKeyVault.class);
    final NDSGoogleCloudKMS googleCloudKMS = mock(NDSGoogleCloudKMS.class);
    final NDSEncryptionAtRest encryptionAtRest = mock(NDSEncryptionAtRest.class);
    when(encryptionAtRest.getAWSKMS()).thenReturn(awsKMS);
    when(encryptionAtRest.getAzureKeyVault()).thenReturn(azureKeyVault);
    when(encryptionAtRest.getGoogleCloudKMS()).thenReturn(googleCloudKMS);
    return encryptionAtRest;
  }

  private static NDSEncryptionAtRest getEmptyEncryptionAtRest() {
    return new NDSEncryptionAtRest(
        new NDSAWSKMS(), new NDSAzureKeyVault(), new NDSGoogleCloudKMS());
  }

  public static AWSKMSEARPrivateEndpoint getAWSKMSEARPrivateEndpoint() {
    return new AWSKMSEARPrivateEndpoint.Builder()
        .id(ObjectId.get())
        .status(CloudProviderPrivateEndpoint.Status.ACTIVE)
        .regionName(AWSRegionName.US_WEST_1)
        .privateEndpointResourceId("endpoint resourceId")
        .build();
  }

  public static AzureKeyVaultEARPrivateEndpoint getAzureKeyVaultEARPrivateEndpoint(
      final Date epoch) {
    return new AzureKeyVaultEARPrivateEndpoint(
        ObjectId.get(),
        CloudProviderPrivateEndpoint.Status.INITIATING,
        "error message",
        null,
        AzureRegionName.US_EAST,
        epoch,
        "resource id",
        "*************",
        "connection name");
  }

  private static AuditLog getMockedAuditLog() {
    final AuditLog auditLog = mock(AuditLog.class, withSettings().lenient());
    doReturn(false).when(auditLog).isEnabled();
    doReturn(Optional.empty()).when(auditLog).getAuditFilter();
    doReturn(false).when(auditLog).isAuditAuthorizationSuccess();
    doReturn(AuditLog.ConfigurationType.NONE).when(auditLog).getConfigurationType();
    return auditLog;
  }

  public static NDSGroup getMockedGroup(final CloudProvider pCloudProvider) {
    switch (pCloudProvider) {
      case AWS:
        return getAWSMockedGroup();
      case AZURE:
        return getAzureMockedGroup();
      case FREE:
        return getFreeMockedGroup();
      case GCP:
        return getGCPMockedGroup();
      case SERVERLESS:
        return getServerlessMockedGroup();
      case FLEX:
        return getFlexMockedGroup();
      default:
        throw new IllegalArgumentException(
            String.format("Cloud provider not supported: %s", pCloudProvider));
    }
  }

  public static NDSGroup getServerlessMTMHolderNdsGroup(
      final CloudProvider pCloudProvider, final boolean pWithProvisionedContainer) {
    // sync up networkPermissionListLastUpdated in container and group to prevent adding Update
    // Permissions List Plan
    final Date date = new Date();
    final CloudProviderContainer container;

    switch (pCloudProvider) {
      case AWS:
        container =
            new AWSCloudProviderContainer(
                (pWithProvisionedContainer ? getAWSContainer() : getUnprovisionedAWSContainer())
                    .append("networkPermissionListLastUpdated", date));
        break;
      case AZURE:
        container =
            new AzureCloudProviderContainer(
                (pWithProvisionedContainer ? getAzureContainer() : getUnprovisionedAzureContainer())
                    .append("networkPermissionListLastUpdated", date));
        break;
      case GCP:
        container =
            new GCPCloudProviderContainer(
                (pWithProvisionedContainer ? getGCPContainer() : getUnprovisionedGCPContainer())
                    .append("networkPermissionListLastUpdated", date));
        break;
      default:
        throw new IllegalArgumentException(
            String.format("Cloud provider %s not supported.", pCloudProvider));
    }

    return new NDSGroup(
        getNDSGroupAllFields(container.toDBObject())
            .append(
                NDSGroup.FieldDefs.MTM_HOLDER_TYPES,
                Set.of(NDSGroup.MTMHolderType.SERVERLESS).stream()
                    .map(Enum::name)
                    .collect(DbUtils.toBasicDBList()))
            .append(
                "networkPermissionList",
                new NDSNetworkPermissionList(
                        NDSNetworkPermission.getPermissionsFromStrings("***************/32"), date)
                    .toDBObject()));
  }

  public static NDSGroup getAWSMockedGroup() {
    return getAWSMockedGroup(new ObjectId());
  }

  public static NDSGroup getAWSMockedGroupWithPrivateLink() {
    return getAWSMockedGroup(new ObjectId(), true, false);
  }

  public static NDSGroup getAWSMockedGroupWithTenantEndpointServiceDeployment(
      final ObjectId groupId) {
    return getAWSMockedGroup(groupId, false, true);
  }

  public static NDSGroup getAWSMockedGroup(final ObjectId groupId) {
    return getAWSMockedGroup(groupId, false, false);
  }

  public static NDSGroup getAWSMockedGroup(
      final ObjectId groupId,
      final boolean pHasPrivateLink,
      final boolean pHasTenantEndpointDeployment) {

    final NDSGroup g = mock(NDSGroup.class, withSettings().lenient());
    final Limits l = spy(new Limits());
    final OperationalLimits ol = spy(new OperationalLimits());
    when(g.getCloudProviderType(any())).thenReturn(CloudProvider.AWS);
    when(g.getGroupId()).thenReturn(groupId);
    when(g.getDNSPin()).thenReturn("abc12");
    when(g.getNetworkPermissionList())
        .thenReturn(
            new NDSNetworkPermissionList(
                NDSNetworkPermission.getPermissionsFromStrings("***************/32")));
    when(g.getLimits()).thenReturn(l);

    when(g.getOperationalLimits()).thenReturn(ol);
    when(g.getPushBasedLogExport()).thenReturn(new PushBasedLogExport());

    final CloudProvider cloudProvider = CloudProvider.AWS;
    final AWSCloudProviderContainer cloudProviderContainer =
        mock(AWSCloudProviderContainer.class, withSettings().lenient());
    when(cloudProviderContainer.getId()).thenReturn(new ObjectId());
    final ObjectId accountId = new ObjectId();
    when(cloudProviderContainer.getAWSAccountId()).thenReturn(accountId);
    when(cloudProviderContainer.getCloudProviderAccountId()).thenReturn(accountId);
    when(cloudProviderContainer.getAtlasCidr()).thenReturn(NDSDefaults.ATLAS_CIDR);
    when(cloudProviderContainer.getCloudProvider()).thenReturn(cloudProvider);

    final AWSRegionName regionName = AWSRegionName.US_EAST_1;
    when(cloudProviderContainer.getRegion()).thenReturn(regionName);
    if (pHasPrivateLink) {
      final AWSPrivateLinkConnection privateLinkConnection = mock(AWSPrivateLinkConnection.class);
      when(cloudProviderContainer.getEndpointServices()).thenReturn(List.of(privateLinkConnection));
    }
    if (pHasTenantEndpointDeployment) {
      when(g.isServerlessMTMHolder()).thenReturn(true);
      final AWSTenantEndpointServiceDeployment tenantEndpointServiceDeployment =
          mock(AWSTenantEndpointServiceDeployment.class);
      when(g.getCloudProviderContainerWithTenantEndpointServiceDeployment())
          .thenReturn(Optional.of(cloudProviderContainer));
      when(cloudProviderContainer.getTenantEndpointServiceDeployment())
          .thenReturn(Optional.of(tenantEndpointServiceDeployment));
    }
    when(g.getCloudProviderContainers())
        .thenReturn(Collections.singletonList(cloudProviderContainer));
    when(g.getCloudProviderContainer(eq(cloudProviderContainer.getId())))
        .thenReturn(Optional.of(cloudProviderContainer));

    when(g.getCloudProviderContainer(eq(cloudProvider), eq(regionName), any()))
        .thenReturn(Optional.of(cloudProviderContainer));

    final NDSUserSecurity ndsUserSecurity = getMockedUserSecurity();
    when(g.getUserSecurity()).thenReturn(ndsUserSecurity);

    final NDSEncryptionAtRest encryptionAtRest = getEmptyEncryptionAtRest();
    when(g.getEncryptionAtRest()).thenReturn(encryptionAtRest);

    final AuditLog auditLog = getMockedAuditLog();
    when(g.getAuditLog()).thenReturn(auditLog);

    final HealthCheckMetadata healthCheckMetadata = mock(HealthCheckMetadata.class);
    doReturn(healthCheckMetadata).when(g).getHealthCheckMetadata();

    when(g.getFTDCExport()).thenReturn(new FTDCExport());

    when(g.getMaintenanceWindow())
        .thenReturn(NDSGroupMaintenanceWindow.getSystemGeneratedMaintenanceWindow());
    when(g.getMaintenanceSystemHourOfDay()).thenCallRealMethod();
    return g;
  }

  public static AWSPeerVpc getAWSPeerVpcWithState(
      final ObjectId pContainerID,
      final String vpcID,
      final String awsAccountID,
      final String pRouteTableCidrBlock,
      final AWSPeerVpc.Status status) {
    return new AWSPeerVpc(
        pContainerID,
        new BasicDBObject()
            .append(ContainerPeer.FieldDefs.ID, new ObjectId())
            .append(AWSPeerVpc.FieldDefs.AWS_ACCOUNT_ID, awsAccountID)
            .append(AWSPeerVpc.FieldDefs.VPC_ID, vpcID)
            .append(AWSPeerVpc.FieldDefs.ROUTE_TABLE_CIDR_BLOCK, pRouteTableCidrBlock)
            .append(AWSPeerVpc.FieldDefs.CONNECTION_ID, "pcx-1234")
            .append(AWSPeerVpc.FieldDefs.STATUS, status.name()));
  }

  public static AWSPeerVpc getAWSPeerVpc(final ObjectId pContainerId) {
    return new AWSPeerVpc(pContainerId, "vpc-1234", "AAAAAAA", "**********/21");
  }

  public static GCPPeerVpc getGCPPeerVpc(final ObjectId pContainerId) {
    return new GCPPeerVpc(pContainerId, "my-network", "my-gcp-project");
  }

  public static AzurePeerNetwork getAzurePeerNetwork(final ObjectId pContainerId) {
    return new AzurePeerNetwork(
        pContainerId, "my-subscription-id", "my-directory-id", "my-resource-group", "my-vnet");
  }

  public static BaseMultiTargetConnectionRule
      getProvisionedMultiTargetConnectionRuleForReplicaSetHardwares(
          final ClusterDescription pClusterDescription,
          final List<ReplicaSetHardware> pReplicaSetHardware,
          final CloudProviderContainer pCloudProviderContainer) {
    final ObjectId privateEndpointServiceId =
        pCloudProviderContainer.getEndpointServices().stream()
            .findFirst()
            .map(BaseEndpointService::getId)
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "Expected Private Endpoint Service ID to be present"));
    return new AWSMultiTargetConnectionRule(
        new ObjectId(),
        pClusterDescription.getGroupId(),
        pClusterDescription.getName(),
        pReplicaSetHardware.stream()
            .flatMap(ReplicaSetHardware::getAllHardware)
            .map(InstanceHardware::getInstanceId)
            .collect(Collectors.toList()),
        BaseMultiTargetConnectionRule.NdsProcessPortTyped.LOAD_BALANCED_MONGOS,
        DEFAULT_MULTI_TARGET_CONNECTION_RULE_FRONTEND_PORT,
        null,
        pCloudProviderContainer.getId(),
        privateEndpointServiceId,
        "targetGroup" + pCloudProviderContainer.getId().toHexString(),
        "listener" + pCloudProviderContainer.getId().toHexString());
  }

  public static List<AWSPrivateLinkTargetGroup> getProvisionedAWSTargetGroupsForReplicaSetHardwares(
      final ObjectId pGroupId,
      final List<ReplicaSetHardware> pReplicaSetHardware,
      final boolean pIsMongos) {
    final String targetGroupArnBase = "target-%s-%s";
    final String listenerArnBase = "listener-%s-%s";
    final String hostnameBase = "hostname-%s-%s";

    return pReplicaSetHardware.stream()
        .flatMap(
            rs -> {
              final int rsIndex = rs.getIndex();

              return rs.getHardware().stream()
                  .map(
                      instance -> {
                        final int instanceIndex = instance.getMemberIndex();

                        return new AWSPrivateLinkTargetGroup(
                            pGroupId,
                            instance.getInstanceId(),
                            String.format(targetGroupArnBase, rsIndex, instanceIndex),
                            instanceIndex,
                            String.format(listenerArnBase, rsIndex, instanceIndex),
                            String.format(hostnameBase, rsIndex, instanceIndex),
                            false,
                            null,
                            false,
                            pIsMongos
                                ? NDSDefaults.MONGOS_PUBLIC_PORT
                                : NDSDefaults.MONGOD_PUBLIC_PORT,
                            new ObjectId(),
                            false,
                            false);
                      });
            })
        .collect(Collectors.toList());
  }

  public static List<AzurePrivateLinkConnectionInboundNATRule>
      getProvisionedAzureNATRulesForReplicaSetHardwares(
          final ObjectId pGroupId,
          final List<ReplicaSetHardware> pReplicaSetHardware,
          final boolean pIsMongos) {
    final String natRuleBase = "natRule-%s-%s";
    final String hostnameBase = "hostname-%s-%s";

    return pReplicaSetHardware.stream()
        .flatMap(
            rs -> {
              final int rsIndex = rs.getIndex();

              return rs.getHardware().stream()
                  .map(
                      instance -> {
                        final int instanceIndex = instance.getMemberIndex();

                        return new AzurePrivateLinkConnectionInboundNATRule(
                            pGroupId,
                            instance.getInstanceId(),
                            instanceIndex,
                            String.format(hostnameBase, rsIndex, instanceIndex),
                            null,
                            pIsMongos
                                ? NDSDefaults.MONGOS_PUBLIC_PORT
                                : NDSDefaults.MONGOD_PUBLIC_PORT,
                            new ObjectId(),
                            String.format(natRuleBase, rsIndex, instanceIndex),
                            Usage.VISIBLE_NODE);
                      });
            })
        .collect(Collectors.toList());
  }

  public static AWSPrivateLinkTargetGroup getProvisionedAWSPrivateLinkTargetGroup(
      final ObjectId pGroupId,
      final int pPort,
      final ObjectId pInstanceId,
      final String pInstanceHostname) {
    return getProvisionedAWSPrivateLinkTargetGroup(
        pGroupId, pPort, pInstanceId, pInstanceHostname, NDSDefaults.MONGOD_PUBLIC_PORT);
  }

  public static AWSPrivateLinkTargetGroup getProvisionedAWSPrivateLinkTargetGroup(
      final ObjectId pGroupId,
      final int pPort,
      final ObjectId pInstanceId,
      final String pInstanceHostname,
      final int pNdsProcessPort) {
    return new AWSPrivateLinkTargetGroup(
        pGroupId,
        pInstanceId,
        "targetGroup",
        pPort,
        "listener",
        pInstanceHostname,
        false,
        new Date(),
        false,
        pNdsProcessPort,
        new ObjectId(),
        false,
        false);
  }

  public static AWSPrivateLinkConnection getAWSPrivateLinkConnection(
      final List<AWSPrivateLinkInterfaceEndpoint> pInterfaceEndpoints) {

    final BasicDBObject dbObject =
        new BasicDBObject()
            .append(DedicatedEndpointService.FieldDefs.ID, new ObjectId())
            .append(
                AWSPrivateLinkConnection.FieldDefs.INTERFACE_ENDPOINTS,
                pInterfaceEndpoints.stream()
                    .map(AWSPrivateLinkInterfaceEndpoint::toDBObject)
                    .collect(DbUtils.toBasicDBList()))
            .append(
                DedicatedEndpointService.FieldDefs.STATUS,
                AWSPrivateLinkConnection.Status.INITIATING.name())
            .append(DedicatedEndpointService.FieldDefs.ERROR_MESSAGE, null)
            .append(DedicatedEndpointService.FieldDefs.DELETE_REQUESTED, false)
            .append(DedicatedEndpointService.FieldDefs.NEEDS_UPDATE_AFTER, null)
            .append(
                AWSEndpointService.FieldDefs.ENDPOINT_SERVICE_NAME, "atlas-service-endpoint-name")
            .append(AWSEndpointService.FieldDefs.ENDPOINT_SERVICE_ID, "atlas-service-endpoint-id")
            .append(AWSEndpointService.FieldDefs.LOAD_BALANCER_ARN, "load-balancer-arn");

    return new AWSPrivateLinkConnection(dbObject);
  }

  public static AWSPrivateLinkConnection getAWSPrivateLinkConnection() {
    final List<AWSPrivateLinkInterfaceEndpoint> ei =
        Collections.singletonList(
            new AWSPrivateLinkInterfaceEndpoint(
                "vpce-9876",
                "host-name.mmscloudtest.com",
                ConnectionStatus.PENDING_ACCEPTANCE,
                null,
                false,
                0));

    return getAWSPrivateLinkConnection(ei);
  }

  public static AzurePrivateLinkConnectionInboundNATRule
      getProvisionedAzurePrivateLinkConnectionInboundNATRule(
          final ObjectId pGroupId,
          final Integer pPort,
          final Integer pNDSProcessPort,
          final ObjectId pInstanceId,
          final String pInstanceHostname,
          final Usage pUsage,
          final ObjectId pPrivateEndpointConnectionId) {
    return new AzurePrivateLinkConnectionInboundNATRule(
        pGroupId,
        pInstanceId,
        pPort,
        pInstanceHostname,
        null,
        pNDSProcessPort,
        pPrivateEndpointConnectionId,
        String.format("%s-%s", pInstanceId, pPort),
        pUsage);
  }

  public static ClusterDescription getClusterDescription(final CloudProvider pCloudProvider) {
    return getClusterDescription(new ObjectId(), pCloudProvider);
  }

  public static ClusterDescription getClusterDescription(
      final ObjectId pGroupId, final CloudProvider pCloudProvider) {
    switch (pCloudProvider) {
      case AWS:
        return new ClusterDescription(getAWSClusterDescription(pGroupId));
      case AZURE:
        return new ClusterDescription(getAzureClusterDescription(pGroupId));
      case FREE:
        return new ClusterDescription(
            getFreeClusterDescription(new TestFreeClusterDescriptionConfig().setGroupId(pGroupId)));
      case GCP:
        return new ClusterDescription(getGCPClusterDescription(pGroupId));
      case SERVERLESS:
        return new ClusterDescription(
            getServerlessClusterDescription(
                new TestServerlessClusterDescriptionConfig().setGroupId(pGroupId)));
      case FLEX:
        return new ClusterDescription(
            getFlexClusterDescription(new TestFlexClusterDescriptionConfig().setGroupId(pGroupId)));
      default:
        throw new IllegalArgumentException(
            String.format("Cloud provider not supported: %s", pCloudProvider));
    }
  }

  public static ClusterDescription getClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final CloudProvider pCloudProvider) {
    switch (pCloudProvider) {
      case AWS:
        return new ClusterDescription(getAWSClusterDescription(pGroupId, pClusterName));
      case AZURE:
        return new ClusterDescription(getAzureClusterDescription(pGroupId, pClusterName));
      case FREE:
        return new ClusterDescription(
            getFreeClusterDescription(
                new TestFreeClusterDescriptionConfig()
                    .setGroupId(pGroupId)
                    .setClusterName(pClusterName)));
      case GCP:
        return new ClusterDescription(getGCPClusterDescription(pGroupId, pClusterName));
      case SERVERLESS:
        return new ClusterDescription(
            getServerlessClusterDescription(
                new TestServerlessClusterDescriptionConfig()
                    .setGroupId(pGroupId)
                    .setClusterName(pClusterName)));
      case FLEX:
        return new ClusterDescription(getFlexClusterDescription(pClusterName, pGroupId));
      default:
        throw new IllegalArgumentException(
            String.format("Cloud provider not supported: %s", pCloudProvider));
    }
  }

  public static ClusterDescription getClusterDescription(
      final CloudProvider pCloudProvider,
      final String pName,
      final List<RegionConfig> pRegionConfigs) {
    final ReplicationSpec spec =
        new ReplicationSpec(
            new ObjectId(),
            new ObjectId(),
            new ObjectId(),
            NDSDefaults.ZONE_NAME,
            1,
            pRegionConfigs);
    return getClusterDescription(pCloudProvider)
        .copy()
        .setReplicationSpecList(List.of(spec))
        .setName(pName)
        .build();
  }

  public static ClusterDescription changeSingleZoneClusterElectableInstanceSize(
      final ClusterDescription pCurrent,
      final InstanceSize pInstanceSize,
      final InstanceFamily pInstanceFamily) {
    final ReplicationSpec currentSpec = pCurrent.getReplicationSpecsWithShardData().get(0);
    final List<RegionConfig> configs =
        currentSpec.getRegionConfigs().stream()
            .map(ShardRegionConfig.class::cast)
            .map(
                currentRegionConfig -> {
                  final AWSHardwareSpec currentElectableSpec =
                      (AWSHardwareSpec) currentRegionConfig.getElectableSpecs();
                  final AWSHardwareSpec hiddenSecondarySpec =
                      new AWSHardwareSpec(
                          0,
                          AWSNDSInstanceSize.M30,
                          AWSInstanceFamily.M5,
                          getOSForCloudProviderAndInstanceFamily(
                              CloudProvider.AWS, AWSInstanceFamily.M5),
                          CpuArchitecture.X86_64,
                          3000,
                          125,
                          VolumeType.Gp3,
                          true);

                  final HardwareSpec newElectableSpec =
                      new AWSHardwareSpec(
                          currentElectableSpec.getNodeCount(),
                          (AWSNDSInstanceSize) pInstanceSize,
                          (AWSInstanceFamily) pInstanceFamily,
                          getOSForCloudProviderAndInstanceFamily(
                              CloudProvider.AWS, pInstanceFamily),
                          CpuArchitecture.X86_64,
                          currentElectableSpec.getDiskIOPS(),
                          currentElectableSpec.getDiskThroughput(),
                          currentElectableSpec.getEBSVolumeType(),
                          currentElectableSpec.getEncryptEBSVolume());
                  return new ShardRegionConfig(
                      currentRegionConfig.getRegionName(),
                      currentRegionConfig.getCloudProvider(),
                      currentRegionConfig.getBaseAutoScaling(),
                      currentRegionConfig.getAnalyticsAutoScaling(),
                      currentRegionConfig.getPriority(),
                      newElectableSpec,
                      currentRegionConfig.getAnalyticsSpecs(),
                      currentRegionConfig.getReadOnlySpecs(),
                      pInstanceSize.isNVMe()
                          ? hiddenSecondarySpec.copy().setNodeCount(1).build()
                          : hiddenSecondarySpec,
                      null,
                      null,
                      null,
                      null);
                })
            .collect(Collectors.toList());

    return pCurrent
        .copy()
        .setReplicationSpecList(List.of(currentSpec.copy().setRegionConfigs(configs).build()))
        .build();
  }

  public static ClusterDescription changeSingleZoneClusterInstanceSizeAndFamilyForAllNodeTypes(
      final ClusterDescription pCurrent,
      final InstanceSize pInstanceSize,
      final InstanceFamily pInstanceFamily) {

    final Function<HardwareSpec, HardwareSpec> hardwareSpecUpdateFunc =
        (hardwareSpec) -> {
          HardwareSpec spec = hardwareSpec;
          if (pInstanceSize != null) {
            spec = updateInstanceSizeForHardwareSpec(pInstanceSize, spec);
          }
          if (pInstanceFamily != null) {
            spec = updateInstanceFamilyForHardwareSpec(pInstanceFamily, spec);
          }
          return spec;
        };

    final ReplicationSpec currentSpec = pCurrent.getReplicationSpecsWithShardData().get(0);
    final List<RegionConfig> configs =
        currentSpec.getRegionConfigs().stream()
            .map(ShardRegionConfig.class::cast)
            .map(
                currentRegionConfig ->
                    new ShardRegionConfig(
                        currentRegionConfig.getRegionName(),
                        currentRegionConfig.getCloudProvider(),
                        currentRegionConfig.getBaseAutoScaling(),
                        currentRegionConfig.getAnalyticsAutoScaling(),
                        currentRegionConfig.getPriority(),
                        hardwareSpecUpdateFunc.apply(currentRegionConfig.getElectableSpecs()),
                        hardwareSpecUpdateFunc.apply(currentRegionConfig.getAnalyticsSpecs()),
                        hardwareSpecUpdateFunc.apply(currentRegionConfig.getReadOnlySpecs()),
                        hardwareSpecUpdateFunc.apply(currentRegionConfig.getHiddenSecondarySpecs()),
                        null,
                        null,
                        null,
                        null))
            .collect(Collectors.toList());

    return pCurrent
        .copy()
        .setReplicationSpecList(List.of(currentSpec.copy().setRegionConfigs(configs).build()))
        .build();
  }

  public static ClusterDescription changeSingleZoneClusterInstanceSizeForDedicatedCsrs(
      final ShardedClusterDescription pCurrent, final InstanceSize pInstanceSize) {
    final ReplicationSpec currentSpec =
        pCurrent.getDedicatedConfigServerReplicationSpec().orElseThrow();

    final List<RegionConfig> configs =
        currentSpec.getRegionConfigs().stream()
            .map(
                currentRegionConfig ->
                    new RegionConfig(
                        currentRegionConfig.getRegionName(),
                        currentRegionConfig.getCloudProvider(),
                        currentRegionConfig.getBaseAutoScaling(),
                        currentRegionConfig.getAnalyticsAutoScaling(),
                        currentRegionConfig.getPriority(),
                        updateInstanceSizeForHardwareSpec(
                            pInstanceSize, currentRegionConfig.getElectableSpecs()),
                        null))
            .collect(Collectors.toList());

    return pCurrent
        .copy()
        .setDedicatedConfigServerReplicationSpec(
            currentSpec.copy().setRegionConfigs(configs).build(), pCurrent.isConfigServerPinned())
        .build();
  }

  private static HardwareSpec updateInstanceSizeForHardwareSpec(
      final InstanceSize pInstanceSize, final HardwareSpec pHardwareSpec) {
    return pHardwareSpec.copy().setInstanceSize(pInstanceSize).build();
  }

  private static HardwareSpec updateInstanceFamilyForHardwareSpec(
      final InstanceFamily pInstanceFamily, final HardwareSpec pHardwareSpec) {
    return pHardwareSpec.copy().setInstanceFamilyAndOS(pInstanceFamily, OS.AL2).build();
  }

  public static ClusterDescription changeSingleZoneClusterElectableNodes(
      final ClusterDescription pCurrent, final int pNewNodes) {
    final ReplicationSpec currentSpec = pCurrent.getReplicationSpecsWithShardData().get(0);
    final RegionConfig currentRegionConfig = currentSpec.getRegionConfigs().get(0);
    return pCurrent
        .copy()
        .setReplicationSpecList(
            List.of(
                currentSpec
                    .copy()
                    .setRegionConfigs(
                        List.of(
                            currentRegionConfig
                                .copy()
                                .setElectableSpecs(
                                    currentRegionConfig
                                        .getElectableSpecs()
                                        .copy()
                                        .setNodeCount(pNewNodes)
                                        .build())
                                .build()))
                    .build()))
        .build();
  }

  public static ClusterDescription changeSingleZoneClusterAnalyticsNodes(
      final ClusterDescription pCurrent, final int pNewNodes) {
    return changeSingleZoneClusterAnalyticsNodes(
        pCurrent,
        pNewNodes,
        pCurrent.getOnlyInstanceSize(NodeType.ELECTABLE).orElseThrow(),
        pCurrent.getInstanceFamily(NodeType.ELECTABLE));
  }

  public static ClusterDescription changeSingleZoneClusterAnalyticsNodes(
      final ClusterDescription pCurrent,
      final int pNewNodes,
      final InstanceSize pInstanceSize,
      final InstanceFamily pInstanceFamily) {
    final ReplicationSpec currentSpec = pCurrent.getReplicationSpecsWithShardData().get(0);
    final ShardRegionConfig currentRegionConfig =
        (ShardRegionConfig) currentSpec.getRegionConfigs().get(0);
    return pCurrent
        .copy()
        .setReplicationSpecList(
            List.of(
                currentSpec
                    .copy()
                    .setRegionConfigs(
                        List.of(
                            currentRegionConfig
                                .copy()
                                .setAnalyticsSpecs(
                                    currentRegionConfig
                                        .getAnalyticsSpecs()
                                        .copy()
                                        .setNodeCount(pNewNodes)
                                        .setInstanceSize(pInstanceSize)
                                        .setInstanceFamilyAndOS(
                                            pInstanceFamily,
                                            getOSForCloudProviderAndInstanceFamily(
                                                currentRegionConfig.getCloudProvider(),
                                                pInstanceFamily))
                                        .build())
                                .build()))
                    .build()))
        .build();
  }

  public static ClusterDescription changeSingleZoneClusterNumShards(
      final ClusterDescription pCurrent, final int pNewShards) {
    final ReplicationSpec currentSpec = pCurrent.getReplicationSpecsWithShardData().get(0);
    return pCurrent
        .copy()
        .setReplicationSpecList(List.of(currentSpec.copy().setNumShards(pNewShards).build()))
        .build();
  }

  public static ReplicationSpec getDefaultCrossCloudReplicationSpec() {
    return new ReplicationSpec(
        new ObjectId(),
        new ObjectId(),
        new ObjectId(),
        NDSDefaults.ZONE_NAME,
        1,
        List.of(
            new RegionConfig(
                AWSRegionName.US_EAST_1,
                CloudProvider.AWS,
                null,
                null,
                7,
                new AWSHardwareSpec(
                    3,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.T3,
                    getOSForCloudProviderAndInstanceFamily(CloudProvider.AWS, AWSInstanceFamily.T3),
                    CpuArchitecture.X86_64,
                    AWSNDSInstanceSize.M10.getStandardEBSIOPS(10),
                    125,
                    VolumeType.Gp3,
                    true),
                null)));
  }

  public static JSONObject getDefaultMultiRegionReplicationSpec(
      final List<MultiRegionConfig> regionConfigsList) {
    final JSONObject replicationSpec = new JSONObject();
    final JSONArray regionConfigsJsonArray = new JSONArray();

    for (MultiRegionConfig regionConfigData : regionConfigsList) {
      final JSONObject regionConfig = new JSONObject();

      // Add electable specs
      if (regionConfigData.instanceSize != null) {
        final JSONObject electableSpecs = new JSONObject();
        electableSpecs.put(
            ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD, regionConfigData.diskSizeGB);
        electableSpecs.put(
            ApiAtlasHardwareSpec20240805View.INSTANCE_SIZE_FIELD, regionConfigData.instanceSize);
        electableSpecs.put(
            ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD, regionConfigData.nodeCount);
        regionConfig.put(
            ApiAtlasDedicatedRegionConfig20240805View.ELECTABLE_SPECS_FIELD, electableSpecs);
      }

      // Add analytics specs
      if (regionConfigData.analyticsInstanceSize != null) {
        final JSONObject analyticsSpecs = new JSONObject();
        analyticsSpecs.put(
            ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD,
            regionConfigData.analyticsDiskSizeGB);
        analyticsSpecs.put(
            ApiAtlasHardwareSpec20240805View.INSTANCE_SIZE_FIELD,
            regionConfigData.analyticsInstanceSize);
        analyticsSpecs.put(
            ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD,
            regionConfigData.analyticsNodeCount);
        regionConfig.put(
            ApiAtlasDedicatedRegionConfig20240805View.ANALYTICS_SPECS_FIELD, analyticsSpecs);
      }

      // Add read-only specs
      if (regionConfigData.readOnlyInstanceSize != null) {
        final JSONObject readOnlySpecs = new JSONObject();
        readOnlySpecs.put(
            ApiAtlasHardwareSpec20240805View.DISK_SIZE_GB_FIELD,
            regionConfigData.readOnlyDiskSizeGB);
        readOnlySpecs.put(
            ApiAtlasHardwareSpec20240805View.INSTANCE_SIZE_FIELD,
            regionConfigData.readOnlyInstanceSize);
        readOnlySpecs.put(
            ApiAtlasDedicatedHardwareSpec20240805View.NODE_COUNT_FIELD,
            regionConfigData.readOnlyNodeCount);
        regionConfig.put(
            ApiAtlasDedicatedRegionConfig20240805View.READ_ONLY_SPECS_FIELD, readOnlySpecs);
      }

      // Add provider, region, and priority
      regionConfig.put(
          ApiAtlasDedicatedRegionConfig20240805View.REGION_NAME_FIELD, regionConfigData.region);
      regionConfig.put(
          ApiAtlasDedicatedRegionConfig20240805View.PROVIDER_NAME_FIELD, regionConfigData.provider);
      regionConfig.put(
          ApiAtlasDedicatedRegionConfig20240805View.PRIORITY_FIELD, regionConfigData.priority);

      // Add auto-scaling configuration if enabled
      if (regionConfigData.computeAutoscalingEnabled) {
        final JSONObject computeAutoScaling = new JSONObject();
        computeAutoScaling.put(ApiAtlasComputeAutoScalingV15View.ENABLED_FIELD, true);
        computeAutoScaling.put(ApiAtlasComputeAutoScalingV15View.MAX_INSTANCE_SIZE_FIELD, "M200");
        computeAutoScaling.put(ApiAtlasComputeAutoScalingV15View.MIN_INSTANCE_SIZE_FIELD, "M10");
        computeAutoScaling.put(ApiAtlasComputeAutoScalingV15View.SCALE_DOWN_ENABLED_FIELD, true);

        final JSONObject autoScaling = new JSONObject();
        autoScaling.put(ApiAtlasAutoScalingV15View.COMPUTE_FIELD, computeAutoScaling);

        regionConfig.put(ApiAtlasDedicatedRegionConfig20240805View.AUTOSCALING_FIELD, autoScaling);
      }

      regionConfigsJsonArray.put(regionConfig);
    }

    replicationSpec.put(
        ApiAtlasReplicationSpec20240805View.REGION_CONFIGS_FIELD, regionConfigsJsonArray);
    return replicationSpec;
  }

  public record MultiRegionConfig(
      String instanceSize,
      String provider,
      String region,
      Double diskSizeGB,
      int nodeCount,
      int priority,
      boolean computeAutoscalingEnabled,

      // Read-Only specifications
      String readOnlyInstanceSize,
      Double readOnlyDiskSizeGB,
      int readOnlyNodeCount,

      // Analytics specifications
      String analyticsInstanceSize,
      Double analyticsDiskSizeGB,
      int analyticsNodeCount) {}

  public static ShardedClusterDescription getShardedClusterDescription(
      final CloudProvider pCloudProvider) {
    return getShardedClusterDescription(ObjectId.get(), pCloudProvider);
  }

  public static ShardedClusterDescription getShardedClusterDescription(
      final ObjectId pGroupId, final CloudProvider pCloudProvider) {
    switch (pCloudProvider) {
      case AWS:
        return new ShardedClusterDescription(getAWSShardedClusterDescription(pGroupId));
      case AZURE:
        return new ShardedClusterDescription(getAzureShardedClusterDescription(pGroupId));
      case GCP:
        return new ShardedClusterDescription(getGCPShardedClusterDescription(pGroupId));
      default:
        throw new IllegalArgumentException(
            String.format("Cloud provider not supported: %s", pCloudProvider));
    }
  }

  public static ShardedClusterDescription getAWSShardedClusterDescriptionWithEmbeddedConfig() {
    return new ShardedClusterDescription(getAWSShardedClusterDescriptionWithEmbeddedConfigServer());
  }

  public static ShardedClusterDescription getAWSShardedClusterDescriptionWithEmbeddedConfig(
      final ObjectId pGroupID, final boolean pPinned) {
    return new ShardedClusterDescription(
        getAWSShardedClusterDescriptionWithEmbeddedConfigServer(pGroupID, pPinned));
  }

  public static ShardedClusterDescription getAWSShardedClusterDescriptionWithDedicatedConfig() {
    return new ShardedClusterDescription(getAWSShardedClusterDescription());
  }

  public static ShardedClusterDescription getAWSShardedClusterDescriptionWithEmbeddedConfig(
      final ObjectId pGroupId, final String pClusterName, final boolean pPinned) {
    return new ShardedClusterDescription(
        getAWSShardedClusterDescriptionWithEmbeddedConfigServer(pGroupId, pClusterName, pPinned));
  }

  public static ShardedClusterDescription getShardedClusterDescription(
      final ObjectId pGroupId, final CloudProvider pCloudProvider, final String pVersion) {
    final VersionUtils.Version version = VersionUtils.parse(pVersion);
    return getShardedClusterDescription(pGroupId, pCloudProvider)
        .copy()
        .setMongoDBVersion(version.getVersion())
        .setMongoDBMajorVersion(version.getMajorVersionString())
        .build();
  }

  public static BasicDBObject getAutoScaleEnabledAWSClusterDescription(final ObjectId pGroupId) {
    final BasicDBList regionConfigs = new BasicDBList();
    regionConfigs.add(
        getShardRegionConfigForAWSRegion(
                AWSNDSDefaults.REGION_NAME,
                RegionConfig.MAX_PRIORITY,
                3,
                0,
                0,
                0,
                CpuArchitecture.ARM64,
                new AWSAutoScaling(NDSModelTestFactory.getAutoScalingEnabled()),
                new AWSAutoScaling(NDSModelTestFactory.getAutoScalingEnabled()))
            .toDBObject());
    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append("regionConfigs", regionConfigs)
            .append("id", new ObjectId())
            .append("zoneName", NDSDefaults.ZONE_NAME)
            .append("zoneId", new ObjectId())
            .append("externalId", new ObjectId())
            .append("numShards", 1);
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec);

    return getAWSClusterDescription(pGroupId, DEFAULT_CLUSTER_NAME)
        .append("clusterType", ClusterDescription.ClusterType.REPLICASET.name())
        .append("replicationSpecList", replicationSpecList);
  }

  public static BasicDBObject getAWSClusterDescription(final ObjectId pGroupId) {
    final BasicDBList regionConfigs = new BasicDBList();
    regionConfigs.add(
        getShardRegionConfigForAWSRegion(
                AWSNDSDefaults.REGION_NAME,
                RegionConfig.MAX_PRIORITY,
                3,
                0,
                0,
                0,
                CpuArchitecture.ARM64,
                new AWSAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()))
            .toDBObject());
    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append("regionConfigs", regionConfigs)
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", new ObjectId())
            .append("zoneName", NDSDefaults.ZONE_NAME)
            .append("numShards", 1);
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec);

    return getAWSClusterDescription(pGroupId, DEFAULT_CLUSTER_NAME)
        .append("clusterType", ClusterDescription.ClusterType.REPLICASET.name())
        .append("replicationSpecList", replicationSpecList);
  }

  public static BasicDBObject getAWSDisaggregatedStorageClusterDescription(
      final ObjectId pGroupId) {
    final BasicDBList regionConfigs = new BasicDBList();
    regionConfigs.add(
        getShardRegionConfigForAWSRegion(
                AWSNDSDefaults.REGION_NAME,
                RegionConfig.MAX_PRIORITY,
                1,
                0,
                0,
                0,
                CpuArchitecture.ARM64,
                new AWSAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()))
            .toDBObject());
    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append("regionConfigs", regionConfigs)
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", new ObjectId())
            .append("zoneName", NDSDefaults.ZONE_NAME)
            .append("numShards", 1);
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec);

    return getAWSClusterDescription(pGroupId, DEFAULT_CLUSTER_NAME)
        .append("clusterType", ClusterDescription.ClusterType.REPLICASET.name())
        .append("storageSystem", ClusterDescription.StorageSystem.DISAGGREGATED_STORAGE.name())
        .append("replicationSpecList", replicationSpecList);
  }

  public static BasicDBObject getClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final List<String> uriHosts) {
    final BasicDBList hostsDBList = new BasicDBList();
    hostsDBList.addAll(uriHosts);

    final BasicDBList privateHostsDBList = new BasicDBList();
    // Add every uri to private list but with -pri added before the first dot
    uriHosts.forEach(uri -> privateHostsDBList.add(uri.replaceFirst("\\.", "-pri.")));

    final BasicDBObject id =
        new BasicDBObject().append("name", pClusterName).append("groupId", pGroupId);

    final BasicDBObject defaultBiConnectorDoc = getDefaultBiConnector();
    final BasicDBObject defaultGeoShardingDoc = getDefaultGeoSharding();
    final BasicDBList defaultLabelDocs = getDefaultLabelsDBList();

    return new BasicDBObject()
        .append("_id", id)
        .append("uniqueId", new ObjectId())
        .append("mongoDBVersion", TEST_MONGODB_VERSION.getVersion())
        .append("mongoDBMajorVersion", TEST_MONGODB_VERSION.getMajorVersionString())
        .append("createDate", new Date())
        .append("lastUpdateDate", new Date())
        .append("diskSizeGB", 50.0)
        .append("mongoDBUriHosts", hostsDBList)
        .append("mongoDBUriHostsLastUpdateDate", new Date())
        .append("privateMongoDBUriHosts", privateHostsDBList)
        .append("state", "WORKING")
        .append(ClusterDescription.FieldDefs.RESTORE_JOB_IDS, new BasicDBList())
        .append("isMTM", false)
        .append("isPaused", false)
        .append("pausedDate", null)
        .append("backupEnabled", true)
        .append("diskBackupEnabled", false)
        .append("biConnector", defaultBiConnectorDoc)
        .append("geoSharding", defaultGeoShardingDoc)
        .append("encryptionAtRestProvider", ClusterDescription.EncryptionAtRestProvider.NONE.name())
        .append(
            FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
            ProcessRestartAllowedState.NONE.name())
        .append("pendingIndexes", new BasicDBList())
        .append("labels", defaultLabelDocs)
        .append("hostnameSchemeForAgents", InstanceHostname.HostnameScheme.LEGACY.name())
        .append("deploymentClusterName", pClusterName)
        .append("dnsPin", "abc12")
        .append("clusterNamePrefix", pClusterName)
        .append("forceReplicaSetReconfigRequestDate", null)
        .append("cloudProviderAccess", getDefaultCloudProviderAccess())
        .append("hostnameSubdomainLevel", InstanceHostname.SubdomainLevel.MONGODB)
        .append(
            FieldDefs.TERMINATION_PROTECTION_ENABLED, NDSDefaults.TERMINATION_PROTECTION_ENABLED)
        .append("internalClusterRole", InternalClusterRole.NONE.name())
        .append("osPolicyVersion", DEFAULT_OS_POLICY_VERSION)
        .append("isCriticalOSPolicyRelease", false);
  }

  public static BasicDBObject getAzureClusterDescription() {
    return getAzureClusterDescription(new ObjectId(), DEFAULT_CLUSTER_NAME);
  }

  public static BasicDBObject getAzureClusterDescription(final boolean pIsOnSsdV2) {
    return getAzureClusterDescription(new ObjectId(), DEFAULT_CLUSTER_NAME, pIsOnSsdV2);
  }

  public static BasicDBObject getAzureClusterDescription(
      final AzureRegionName pRegionName, final boolean pIsOnSsdV2) {
    return getAzureClusterDescription(
        new ObjectId(), DEFAULT_CLUSTER_NAME, pRegionName, pIsOnSsdV2);
  }

  public static BasicDBObject getAzureClusterDescription(final ObjectId pGroupId) {
    return getAzureClusterDescription(pGroupId, DEFAULT_CLUSTER_NAME);
  }

  public static BasicDBObject getAzureClusterDescription(final String pName) {
    return getAzureClusterDescription(new ObjectId(), pName);
  }

  public static BasicDBObject getAzureClusterDescription(
      final ObjectId pGroupId, final String pName, final List<RegionConfig> pRegionConfigs) {
    return getAzureClusterDescription(
        pGroupId,
        pName,
        Arrays.asList("azure-host-01.mmsclouddb.com:27017", "azure-host-02.mmsclouddb.com:27017"),
        1,
        pRegionConfigs);
  }

  public static BasicDBObject getAzureClusterDescription(
      final String pName, final List<RegionConfig> pRegionConfigs) {
    return getAzureClusterDescription(new ObjectId(), pName, pRegionConfigs);
  }

  public static BasicDBObject getAWSClusterDescription(
      final String pClusterName, final List<RegionConfig> pRegionConfigs) {
    return getAWSClusterDescription(
        new ObjectId(), pClusterName, Collections.emptyList(), 1, pRegionConfigs);
  }

  public static BasicDBObject getAWSClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final List<RegionConfig> pRegionConfigs) {
    return getAWSClusterDescription(pGroupId, pClusterName, List.of(), 1, pRegionConfigs);
  }

  public static BasicDBObject getAWSClusterDescriptionWithVersion(
      final ObjectId pGroupId,
      final String pName,
      final String pVersion,
      final List<RegionConfig> pRegionConfigs) {
    return versionedClusterDescription(
        getAWSClusterDescription(pGroupId, pName, List.of(), 1, pRegionConfigs), pVersion);
  }

  public static BasicDBObject getAWSShardedClusterDescriptionWithVersion(
      final ObjectId pGroupId,
      final String pName,
      final String pVersion,
      final List<RegionConfig> pRegionConfigs) {
    return versionedClusterDescription(
        getAWSShardedClusterDescription(pGroupId, pName, pRegionConfigs), pVersion);
  }

  public static BasicDBObject getClusterDescription(
      final String pName,
      final CloudProvider pCloudProvider,
      final int pNodes,
      final List<RegionName> pRegions) {
    final List<RegionConfig> regionConfigs = getRegionConfigs(pNodes, pRegions);
    switch (pCloudProvider) {
      case AWS:
        return getAWSClusterDescription(pName, regionConfigs);
      case AZURE:
        return getAzureClusterDescription(pName, regionConfigs);
      case GCP:
        return getGCPClusterDescription(pName, regionConfigs);
      case FREE:
        return getFreeClusterDescription(
            new TestFreeClusterDescriptionConfig()
                .setClusterName(pName)
                .setBackingProvider(pRegions.get(0).getProvider())
                .setRegionName(pRegions.get(0)));
      case SERVERLESS:
        return getServerlessClusterDescription(
            new TestServerlessClusterDescriptionConfig()
                .setClusterName(pName)
                .setHostUris(Collections.emptyList())
                .setBackingProvider(pRegions.get(0).getProvider())
                .setRegionName(pRegions.get(0)));
      case FLEX:
        return getFlexClusterDescription(
            new TestFlexClusterDescriptionConfig()
                .setClusterName(pName)
                .setBackingProvider(pRegions.get(0).getProvider())
                .setRegionName(pRegions.get(0)));
      default:
        throw new IllegalArgumentException("Unsupported Cloud Replication Spec: " + pCloudProvider);
    }
  }

  @Deprecated
  public static ReplicationSpec getAWSReplicationSpec(
      final ObjectId pSpecId,
      final String pZoneName,
      final int pNumShards,
      final int pNodes,
      final List<AWSRegionName> pRegions) {
    return getAWSReplicationSpec(pSpecId, oid(1), pZoneName, pNumShards, pNodes, pRegions);
  }

  public static ReplicationSpec getAWSReplicationSpec(
      final ObjectId pSpecId,
      final ObjectId pZoneId,
      final String pZoneName,
      final int pNumShards,
      final int pNodes,
      final List<AWSRegionName> pRegions) {
    return new ReplicationSpec(
        pSpecId,
        new ObjectId(),
        pZoneId,
        pZoneName,
        pNumShards,
        getRegionConfigs(pNodes, pRegions));
  }

  public static ReplicationSpec getAWSReplicationSpec(
      final ObjectId pSpecId,
      final ObjectId pExternalId,
      final ObjectId pZoneId,
      final String pZoneName,
      final int pNumShards,
      final int pNodes,
      final List<AWSRegionName> pRegions) {
    return new ReplicationSpec(
        pSpecId, pExternalId, pZoneId, pZoneName, pNumShards, getRegionConfigs(pNodes, pRegions));
  }

  public static ReplicationSpec getGCPReplicationSpec(
      final ObjectId pSpecId,
      final ObjectId pZoneId,
      final String pZoneName,
      final int pNumShards,
      final int pNodes,
      final List<GCPRegionName> pRegions) {
    return new ReplicationSpec(
        pSpecId,
        new ObjectId(),
        pZoneId,
        pZoneName,
        pNumShards,
        getRegionConfigs(pNodes, pRegions));
  }

  public static ReplicationSpec getGCPReplicationSpec(
      final ObjectId pSpecId,
      final String pZoneName,
      final int pNumShards,
      final int pNodes,
      final List<GCPRegionName> pRegions) {
    return getGCPReplicationSpec(pSpecId, oid(1), pZoneName, pNumShards, pNodes, pRegions);
  }

  public static ReplicationSpec getAzureReplicationSpec(
      final ObjectId pSpecId,
      final ObjectId pZoneId,
      final String pZoneName,
      final int pNumShards,
      final int pNodes,
      final List<AzureRegionName> pRegions) {
    return new ReplicationSpec(
        pSpecId,
        new ObjectId(),
        pZoneId,
        pZoneName,
        pNumShards,
        getRegionConfigs(pNodes, pRegions));
  }

  public static ReplicationSpec getAzureReplicationSpec(
      final ObjectId pSpecId,
      final String pZoneName,
      final int pNumShards,
      final int pNodes,
      final List<AzureRegionName> pRegions) {
    return getAzureReplicationSpec(pSpecId, oid(1), pZoneName, pNumShards, pNodes, pRegions);
  }

  public static ReplicationSpec getAWSReplicationSpecFull() {
    return new ReplicationSpec(
        new ObjectId(),
        new ObjectId(),
        new ObjectId(),
        "zone0",
        1,
        List.of(
            new ShardRegionConfig(
                AWSRegionName.US_EAST_1,
                CloudProvider.AWS,
                AWSAutoScaling.getDisabledAutoScaling(),
                AWSAutoScaling.getDisabledAutoScaling(),
                7,
                new AWSHardwareSpec(
                    2,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                new AWSHardwareSpec(
                    0,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                new AWSHardwareSpec(
                    2,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                new AWSHardwareSpec(
                    1,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                null,
                null,
                null,
                null),
            new ShardRegionConfig(
                AWSRegionName.US_WEST_1,
                CloudProvider.AWS,
                AWSAutoScaling.getDisabledAutoScaling(),
                AWSAutoScaling.getDisabledAutoScaling(),
                6,
                new AWSHardwareSpec(
                    1,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                new AWSHardwareSpec(
                    0,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                new AWSHardwareSpec(
                    0,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                new AWSHardwareSpec(
                    0,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                null,
                null,
                null,
                null),
            new ShardRegionConfig(
                AWSRegionName.AP_SOUTHEAST_2,
                CloudProvider.AWS,
                AWSAutoScaling.getDisabledAutoScaling(),
                AWSAutoScaling.getDisabledAutoScaling(),
                0,
                new AWSHardwareSpec(
                    0,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                new AWSHardwareSpec(
                    1,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                new AWSHardwareSpec(
                    1,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                new AWSHardwareSpec(
                    0,
                    AWSNDSInstanceSize.M10,
                    AWSInstanceFamily.M5,
                    NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                        CloudProvider.AWS, AWSInstanceFamily.M5),
                    CpuArchitecture.X86_64,
                    120,
                    0,
                    VolumeType.Gp2,
                    true),
                null,
                null,
                null,
                null)));
  }

  /**
   * Return a regions config for the appropriate cloud provider with approximately even number of
   * nodes in each specified region
   *
   * <p>For example: getRegionConfigs(5, [US_EAST_1, US_WEST_1]) will return { US_EAST_1: {
   * electableNodes: 2, readOnlyNodes: 0, priority: 7 }, US_WEST_1: { electableNodes: 3,
   * readOnlyNodes: 0, priority: 6 } }
   */
  public static List<RegionConfig> getRegionConfigs(
      final int pNodes, final List<? extends RegionName> pRegions) {
    return getRegionConfigs(pNodes, pRegions, 0);
  }

  public static List<RegionConfig> getRegionConfigs(
      final int pNodes, final List<? extends RegionName> pRegions, final int pHiddenSecondaries) {
    int remainingNodes = pNodes;
    final List<RegionConfig> regionConfigs = new ArrayList<>();
    for (int i = 0; i < pRegions.size(); i++) {
      final RegionName region = pRegions.get(i);
      final int regionsRemaining = pRegions.size() - i;
      final int numNodes = remainingNodes / regionsRemaining;
      regionConfigs.add(
          NDSModelTestFactory.getShardRegionConfigForRegion(
              region.getProvider(),
              region,
              RegionSpec.MAX_PRIORITY - i,
              numNodes,
              0,
              0,
              pHiddenSecondaries));
      remainingNodes -= numNodes;
    }
    return regionConfigs;
  }

  public static List<RegionConfig> getRegionConfigs(
      final int pNodes, final CloudProvider pProvider, final List<? extends RegionName> pRegions) {
    int remainingNodes = pNodes;
    final List<RegionConfig> regionConfigs = new ArrayList<>();
    for (int i = 0; i < pRegions.size(); i++) {
      final RegionName region = pRegions.get(i);
      final int regionsRemaining = pRegions.size() - i;
      final int numNodes = remainingNodes / regionsRemaining;
      regionConfigs.add(
          NDSModelTestFactory.getShardRegionConfigForRegion(
              pProvider, region, RegionSpec.MAX_PRIORITY - i, numNodes, 0, 0, 0));
      remainingNodes -= numNodes;
    }
    return regionConfigs;
  }

  public static List<RegionConfig> getConfigRegionConfigs(
      final List<? extends RegionName> pRegions) {
    return getConfigRegionConfigs(pRegions, true);
  }

  public static List<RegionConfig> getConfigRegionConfigs(
      final List<? extends RegionName> pRegions, final boolean isUsingAzureSsdV2) {

    switch (pRegions.size()) {
      case 0:
        return List.of();
      case 1:
        return List.of(
            NDSModelTestFactory.getDedicatedConfigRegionConfigForRegion(
                pRegions.get(0).getProvider(),
                pRegions.get(0),
                RegionSpec.MAX_PRIORITY,
                3,
                CpuArchitecture.ARM64,
                isUsingAzureSsdV2));
      case 2:
        return List.of(
            NDSModelTestFactory.getDedicatedConfigRegionConfigForRegion(
                pRegions.get(0).getProvider(),
                pRegions.get(0),
                RegionSpec.MAX_PRIORITY,
                2,
                CpuArchitecture.ARM64,
                isUsingAzureSsdV2),
            NDSModelTestFactory.getDedicatedConfigRegionConfigForRegion(
                pRegions.get(1).getProvider(),
                pRegions.get(1),
                RegionSpec.MAX_PRIORITY - 1,
                1,
                CpuArchitecture.ARM64,
                isUsingAzureSsdV2));
      case 3:
        return List.of(
            NDSModelTestFactory.getDedicatedConfigRegionConfigForRegion(
                pRegions.get(0).getProvider(),
                pRegions.get(0),
                RegionSpec.MAX_PRIORITY,
                1,
                CpuArchitecture.ARM64,
                isUsingAzureSsdV2),
            NDSModelTestFactory.getDedicatedConfigRegionConfigForRegion(
                pRegions.get(1).getProvider(),
                pRegions.get(1),
                RegionSpec.MAX_PRIORITY - 1,
                1,
                CpuArchitecture.ARM64,
                isUsingAzureSsdV2),
            NDSModelTestFactory.getDedicatedConfigRegionConfigForRegion(
                pRegions.get(2).getProvider(),
                pRegions.get(2),
                RegionSpec.MAX_PRIORITY - 2,
                1,
                CpuArchitecture.ARM64,
                isUsingAzureSsdV2));
      default:
        throw new IllegalArgumentException(
            String.format(
                "Config server replica can have only 3 nodes, however %d regions were provided",
                pRegions.size()));
    }
  }

  public static BasicDBObject getAzureClusterDescription(
      final ObjectId pGroupId, final String pName) {
    return getAzureClusterDescription(pGroupId, pName, false);
  }

  public static BasicDBObject getAzureClusterDescription(
      final ObjectId pGroupId, final String pName, final boolean pIsOnSsdV2) {
    return getAzureClusterDescription(pGroupId, pName, AzureNDSDefaults.REGION_NAME, pIsOnSsdV2);
  }

  public static BasicDBObject getAzureClusterDescription(
      final ObjectId pGroupId,
      final String pName,
      final AzureRegionName pRegionName,
      final boolean pIsOnSsdV2) {
    final List<RegionConfig> azureRegionConfigs = getAzureRegionConfigs(pRegionName, pIsOnSsdV2);

    return getAzureClusterDescription(pGroupId, pName, azureRegionConfigs);
  }

  public static List<RegionConfig> getAzureRegionConfigs(final boolean pIsOnSsdV2) {
    return getAzureRegionConfigs(AzureNDSDefaults.REGION_NAME, pIsOnSsdV2);
  }

  public static List<RegionConfig> getAzureRegionConfigs(
      final AzureRegionName pRegionName, final boolean pIsOnSsdV2) {
    return List.of(
        getShardRegionConfigForAzureRegion(
            pRegionName, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0, pIsOnSsdV2));
  }

  public static BasicDBObject getAzureClusterDescription(
      final ObjectId pGroupId,
      final String pName,
      final AzureNDSInstanceSize pAzureInstanceSize,
      final AzureDiskType pAzureDiskType,
      final boolean pIsOnSsdV2) {
    final List<RegionConfig> azureRegionConfigs =
        getAzureRegionConfigs(pAzureInstanceSize, pAzureDiskType, pIsOnSsdV2);

    return getAzureClusterDescription(pGroupId, pName, azureRegionConfigs);
  }

  public static List<RegionConfig> getAzureRegionConfigs(
      final AzureNDSInstanceSize pAzureInstanceSize,
      final AzureDiskType pAzureDiskType,
      final boolean pIsOnSsdV2) {
    return List.of(
        getShardRegionConfigForAzureRegion(
            AzureNDSDefaults.REGION_NAME,
            pAzureInstanceSize,
            pAzureDiskType,
            RegionConfig.MAX_PRIORITY,
            3,
            0,
            0,
            0,
            pIsOnSsdV2));
  }

  public static BasicDBObject getAzureClusterDescription(
      final ObjectId pGroupId, final String pName, final String pVersion) {
    return versionedClusterDescription(getAzureClusterDescription(pGroupId, pName), pVersion);
  }

  public static BasicDBObject getAzureClusterDescriptionWithEncryptionAtRestProvider(
      final ObjectId pGroupId,
      final String pClusterName,
      ClusterDescription.EncryptionAtRestProvider pEncryptionAtRestProvider) {
    final BasicDBObject doc = getAzureClusterDescription(pGroupId, pClusterName);
    doc.put(
        ClusterDescription.FieldDefs.ENCRYPTION_AT_REST_PROVIDER, pEncryptionAtRestProvider.name());
    return doc;
  }

  public static BasicDBObject getAzureClusterDescription(
      final ObjectId pGroupId,
      final String pName,
      final List<String> hostUris,
      final int pNumShards,
      final List<RegionConfig> pRegionConfigs) {
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(
        new BasicDBObject(
                "regionConfigs",
                pRegionConfigs.stream()
                    .map(RegionConfig::toDBObject)
                    .collect(DbUtils.toBasicDBList()))
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", new ObjectId())
            .append("zoneName", NDSDefaults.ZONE_NAME)
            .append("numShards", pNumShards));
    final boolean isSharded = pNumShards > 1; // not a correct isSharded calculation
    final BasicDBObject clusterDescription =
        getClusterDescription(pGroupId, pName, hostUris)
            .append(
                "clusterType",
                isSharded
                    ? ClusterDescription.ClusterType.SHARDED.name()
                    : ClusterDescription.ClusterType.REPLICASET.name())
            .append("replicationSpecList", replicationSpecList)
            .append("hostnameSubdomainLevel", SubdomainLevel.AZURE.name())
            .append(
                "diskSizeGB",
                ((AzureHardwareSpec) pRegionConfigs.get(0).getElectableSpecs())
                    .getDiskType()
                    .getSizeGB());
    if (isSharded) {
      final BasicDBObject csReplicationSpec =
          new ReplicationSpec(
                  new ObjectId(),
                  new ObjectId(),
                  new ObjectId(),
                  NDSDefaults.ZONE_NAME,
                  1,
                  pRegionConfigs)
              .copy()
              .updateAllHardware(
                  new AWSHardwareSpec.Builder()
                      .setDiskIOPS(
                          AWSNDSInstanceSize.M30.getGP3StandardEBSIOPS(
                              clusterDescription.getInt(ClusterDescription.FieldDefs.DISK_SIZE_GB)))
                      .setDiskThroughput(
                          AWSNDSInstanceSize.M30.getMinThroughput(
                              AWSNDSInstanceSize.M30.getDefaultDiskSizeGB()))
                      .setEncryptEBSVolume(true)
                      .setEBSVolumeType(VolumeType.Gp3)
                      .setInstanceSize(AWSNDSInstanceSize.M30)
                      .setInstanceFamilyAndOS(
                          AWSInstanceFamily.M5,
                          getOSForCloudProviderAndInstanceFamily(
                              CloudProvider.AWS, AWSInstanceFamily.M5))
                      .setIsDedicatedConfigServerHardware(true))
              .build()
              .toDBObject();
      clusterDescription.append("configServerReplicationSpec", csReplicationSpec);
      clusterDescription.append("configDiskSizeGB", 10.0);
    }
    clusterDescription.append(ClusterDescription.FieldDefs.BI_CONNECTOR, getDefaultBiConnector());
    return clusterDescription;
  }

  public static BasicDBObject getGCPClusterDescription() {
    return getGCPClusterDescription(DEFAULT_CLUSTER_NAME);
  }

  public static BasicDBObject getGCPClusterDescription(final ObjectId pGroupId) {
    return getGCPClusterDescription(pGroupId, DEFAULT_CLUSTER_NAME);
  }

  public static BasicDBObject getGCPClusterDescription(final String pClusterName) {
    return getGCPClusterDescription(new ObjectId(), pClusterName);
  }

  public static BasicDBObject getGCPClusterDescription(
      final String pName, final List<RegionConfig> pSpec) {
    return getGCPClusterDescription(
        new ObjectId(),
        pName,
        Arrays.asList("gcp-host-01.mmsclouddb.com:27017", "gcp-host-02.mmsclouddb.com:27017"),
        1,
        pSpec);
  }

  public static BasicDBObject getGCPClusterDescription(
      final ObjectId pGroupId, final String pName) {
    final List<RegionConfig> regionConfigs =
        List.of(
            getShardRegionConfigForRegion(
                GCPNDSDefaults.REGION_NAME, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0));

    return getGCPClusterDescription(
        pGroupId,
        pName,
        Arrays.asList("gcp-host-01.mmsclouddb.com:27017", "gcp-host-02.mmsclouddb.com:27017"),
        1,
        regionConfigs);
  }

  public static BasicDBObject getGCPClusterDescription(
      final ObjectId pGroupId, final String pName, final String pVersion) {
    return versionedClusterDescription(getGCPClusterDescription(pGroupId, pName), pVersion);
  }

  public static BasicDBObject getGCPClusterDescription(
      final ObjectId pGroupId, final String pName, final List<RegionConfig> pSpec) {
    return getGCPClusterDescription(
        pGroupId,
        pName,
        Arrays.asList("gcp-host-01.mmsclouddb.com:27017", "gcp-host-02.mmsclouddb.com:27017"),
        1,
        pSpec);
  }

  public static BasicDBObject getGCPClusterDescriptionWithEncryptionAtRestProvider(
      final ObjectId pGroupId,
      final String pClusterName,
      ClusterDescription.EncryptionAtRestProvider pEncryptionAtRestProvider) {
    final BasicDBObject doc = getGCPClusterDescription(pGroupId, pClusterName);
    doc.put(
        ClusterDescription.FieldDefs.ENCRYPTION_AT_REST_PROVIDER, pEncryptionAtRestProvider.name());
    return doc;
  }

  public static BasicDBObject getGCPClusterDescription(
      final ObjectId pGroupId,
      final String pName,
      final List<String> hostUris,
      final int pNumShards,
      final List<RegionConfig> pRegionConfigs) {
    final ReplicationSpec replicationSpec =
        new ReplicationSpec(
            new ObjectId(),
            new ObjectId(),
            new ObjectId(),
            NDSDefaults.ZONE_NAME,
            pNumShards,
            pRegionConfigs);
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec.toDBObject());
    final boolean isSharded = pNumShards > 1; // not a correct isSharded calculation
    final BasicDBObject clusterDescription =
        getClusterDescription(pGroupId, pName, hostUris)
            .append(
                "clusterType",
                isSharded
                    ? ClusterDescription.ClusterType.SHARDED.name()
                    : ClusterDescription.ClusterType.REPLICASET.name())
            .append("replicationSpecList", replicationSpecList)
            .append("hostnameSubdomainLevel", InstanceHostname.SubdomainLevel.GCP.name());

    if (isSharded) {
      final BasicDBObject csReplicationSpec =
          new ReplicationSpec(
                  new ObjectId(),
                  new ObjectId(),
                  new ObjectId(),
                  NDSDefaults.ZONE_NAME,
                  1,
                  pRegionConfigs)
              .copy()
              .updateAllHardware(
                  new AWSHardwareSpec.Builder()
                      .setDiskIOPS(
                          AWSNDSInstanceSize.M30.getGP3StandardEBSIOPS(
                              clusterDescription.getInt(ClusterDescription.FieldDefs.DISK_SIZE_GB)))
                      .setDiskThroughput(
                          AWSNDSInstanceSize.M30.getMinThroughput(
                              AWSNDSInstanceSize.M30.getDefaultDiskSizeGB()))
                      .setEncryptEBSVolume(true)
                      .setEBSVolumeType(VolumeType.Gp3)
                      .setInstanceSize(AWSNDSInstanceSize.M30)
                      .setInstanceFamilyAndOS(
                          AWSInstanceFamily.M5,
                          getOSForCloudProviderAndInstanceFamily(
                              CloudProvider.AWS, AWSInstanceFamily.M5))
                      .setIsDedicatedConfigServerHardware(true))
              .build()
              .toDBObject();
      clusterDescription.append("configServerReplicationSpec", csReplicationSpec);
      clusterDescription.append("configDiskSizeGB", 10.0);
    }
    clusterDescription.append(ClusterDescription.FieldDefs.BI_CONNECTOR, getDefaultBiConnector());
    return clusterDescription;
  }

  public static BasicDBObject getAWSClusterDescription(
      final ObjectId pGroupId, final String pClusterName) {
    return getAWSClusterDescriptionWithMongoURIHosts(
        pGroupId,
        pClusterName,
        Arrays.asList("aws-host-01.mmsclouddb.com:27017", "aws-host-02.mmsclouddb.com:27017"));
  }

  public static BasicDBObject getMultiRegionAWSClusterDescription(
      final ObjectId pGroupId, final String pClusterName) {
    return getMultiRegionAWSClusterDescriptionWithMongoURIHosts(
        pGroupId,
        pClusterName,
        Arrays.asList("aws-host-01.mmsclouddb.com:27017", "aws-host-02.mmsclouddb.com:27017"));
  }

  public static BasicDBObject getAWSClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final boolean pShouldAddAnalyticsNode) {
    return getAWSClusterDescriptionWithMongoURIHosts(
        pGroupId,
        pClusterName,
        Arrays.asList("aws-host-01.mmsclouddb.com:27017", "aws-host-02.mmsclouddb.com:27017"),
        pShouldAddAnalyticsNode);
  }

  public static BasicDBObject getAWSClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final CpuArchitecture pCpuArchitecture) {
    return getAWSClusterDescriptionWithMongoURIHosts(
        pGroupId,
        pClusterName,
        pCpuArchitecture,
        Arrays.asList("aws-host-01.mmsclouddb.com:27017", "aws-host-02.mmsclouddb.com:27017"),
        false);
  }

  public static BasicDBObject getAWSClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final String pVersion) {
    return versionedClusterDescription(getAWSClusterDescription(pGroupId, pClusterName), pVersion);
  }

  public static BasicDBObject getAWSClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final CpuArchitecture pCpuArchitecture,
      final String pVersion) {
    return versionedClusterDescription(
        getAWSClusterDescription(pGroupId, pClusterName, pCpuArchitecture), pVersion);
  }

  public static BasicDBObject getAWSClusterDescriptionWithMongoURIHosts(
      final ObjectId pGroupId, final String pClusterName, final List<String> hostUris) {
    return getAWSClusterDescriptionWithMongoURIHosts(
        pGroupId, pClusterName, CpuArchitecture.ARM64, hostUris, false);
  }

  public static BasicDBObject getMultiRegionAWSClusterDescriptionWithMongoURIHosts(
      final ObjectId pGroupId, final String pClusterName, final List<String> hostUris) {
    final List<RegionConfig> regionConfigs =
        List.of(
            getShardRegionConfigForRegion(
                AWSNDSDefaults.REGION_NAME,
                CpuArchitecture.ARM64,
                RegionConfig.MAX_PRIORITY,
                1,
                0,
                0,
                0),
            getShardRegionConfigForRegion(
                AWSRegionName.US_WEST_1,
                CpuArchitecture.ARM64,
                RegionConfig.MAX_PRIORITY,
                2,
                0,
                0,
                0));

    return getAWSClusterDescription(pGroupId, pClusterName, hostUris, 1, regionConfigs);
  }

  public static BasicDBObject getAWSClusterDescriptionWithMongoURIHosts(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<String> hostUris,
      final boolean pShouldAddAnalyticsNode) {
    return getAWSClusterDescriptionWithMongoURIHosts(
        pGroupId, pClusterName, CpuArchitecture.ARM64, hostUris, pShouldAddAnalyticsNode);
  }

  public static BasicDBObject getAWSClusterDescriptionWithMongoURIHosts(
      final ObjectId pGroupId,
      final String pClusterName,
      final CpuArchitecture pCpuArchitecture,
      final List<String> hostUris,
      final boolean pShouldAddAnalyticsNode) {
    final List<RegionConfig> regionConfigs =
        List.of(
            getShardRegionConfigForRegion(
                AWSNDSDefaults.REGION_NAME,
                pCpuArchitecture,
                RegionConfig.MAX_PRIORITY,
                3,
                pShouldAddAnalyticsNode ? 1 : 0,
                0,
                0));

    return getAWSClusterDescription(pGroupId, pClusterName, hostUris, 1, regionConfigs);
  }

  public static BasicDBObject getShardedAWSClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final CpuArchitecture pCpuArchitecture,
      final int pNumberOfShards) {
    final List<RegionConfig> regionConfigs =
        List.of(
            getShardRegionConfigForRegion(
                AWSNDSDefaults.REGION_NAME,
                pCpuArchitecture,
                RegionConfig.MAX_PRIORITY,
                3,
                0,
                0,
                0));
    final BasicDBObject cluster =
        getAWSClusterDescription(
            pGroupId, pClusterName, Collections.emptyList(), pNumberOfShards, regionConfigs);
    final List<ReplicationSpec> replicationSpecs =
        ShardedClusterDescription.readCrossCloudReplicationSpecs(cluster);
    final DedicatedConfigServerReplicationSpec config =
        new DedicatedConfigServerReplicationSpec(
            NDSClusterSvc.calculateDedicatedConfigServerReplicationSpec(
                replicationSpecs,
                10.0,
                null,
                TEST_MONGODB_VERSION,
                false,
                false,
                false,
                InstanceFamilyFlags.withAllFlagsFalse()),
            false);
    return cluster
        .append(
            ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC, config.toDBObject())
        .append(ShardedClusterDescription.FieldDefs.CONFIG_DISK_SIZE_GB, 10.0);
  }

  public static BasicDBObject getShardedAWSClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final CpuArchitecture pCpuArchitecture,
      final String pVersion,
      final int pNumberOfShards) {
    return versionedClusterDescription(
        getShardedAWSClusterDescription(pGroupId, pClusterName, pCpuArchitecture, pNumberOfShards),
        pVersion);
  }

  public static BasicDBObject getShardedAWSClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final int pNumberOfShards) {
    return getShardedAWSClusterDescription(
        pGroupId, pClusterName, CpuArchitecture.ARM64, pNumberOfShards);
  }

  public static BasicDBObject getShardedAWSClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final int pNumberOfShards,
      final List<RegionConfig> regionConfigs) {
    return getAWSClusterDescription(
        pGroupId, pClusterName, Collections.emptyList(), pNumberOfShards, regionConfigs);
  }

  public static BasicDBObject getAWSClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<String> hostUris,
      final int pNumShards,
      final List<RegionConfig> pRegionConfigs) {
    final boolean sharded = pNumShards > 1;

    final ObjectId zoneId = new ObjectId();
    final String zoneName = NDSDefaults.ZONE_NAME;

    final BasicDBObject clusterDescription =
        getClusterDescription(pGroupId, pClusterName, hostUris)
            .append(
                "clusterType",
                sharded
                    ? ClusterDescription.ClusterType.SHARDED.name()
                    : ClusterDescription.ClusterType.REPLICASET.name())
            .append("hostnameSubdomainLevel", InstanceHostname.SubdomainLevel.MONGODB.name());
    final ReplicationSpec replicationSpec =
        new ReplicationSpec(
                new ObjectId(), new ObjectId(), zoneId, zoneName, pNumShards, pRegionConfigs)
            .copy()
            .updateAllHardware(
                new AWSHardwareSpec.Builder()
                    .setDiskIOPS(
                        AWSNDSInstanceSize.M30.getGP3StandardEBSIOPS(
                            clusterDescription.getInt(ClusterDescription.FieldDefs.DISK_SIZE_GB)))
                    .setDiskThroughput(
                        AWSNDSInstanceSize.M30.getMinThroughput(
                            AWSNDSInstanceSize.M30.getDefaultDiskSizeGB()))
                    .setEncryptEBSVolume(true)
                    .setEBSVolumeType(VolumeType.Gp3)
                    .setInstanceSize(AWSNDSInstanceSize.M30)
                    .setInstanceFamilyAndOS(
                        AWSInstanceFamily.M4,
                        getOSForCloudProviderAndInstanceFamily(
                            CloudProvider.AWS, AWSInstanceFamily.M4)))
            .build();
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec.toDBObject());
    clusterDescription.append("replicationSpecList", replicationSpecList);
    clusterDescription.append(ClusterDescription.FieldDefs.BI_CONNECTOR, getDefaultBiConnector());

    if (sharded) {
      final BasicDBObject csReplicationSpec =
          new ReplicationSpec(new ObjectId(), new ObjectId(), zoneId, zoneName, 1, pRegionConfigs)
              .copy()
              .updateAllHardware(
                  new AWSHardwareSpec.Builder()
                      .setDiskIOPS(
                          AWSNDSInstanceSize.M30.getGP3StandardEBSIOPS(
                              clusterDescription.getInt(ClusterDescription.FieldDefs.DISK_SIZE_GB)))
                      .setDiskThroughput(
                          AWSNDSInstanceSize.M30.getMinThroughput(
                              AWSNDSInstanceSize.M30.getDefaultDiskSizeGB()))
                      .setEncryptEBSVolume(true)
                      .setEBSVolumeType(VolumeType.Gp3)
                      .setInstanceSize(AWSNDSInstanceSize.M30)
                      .setInstanceFamilyAndOS(
                          AWSInstanceFamily.M5,
                          getOSForCloudProviderAndInstanceFamily(
                              CloudProvider.AWS, AWSInstanceFamily.M5))
                      .setIsDedicatedConfigServerHardware(true))
              .build()
              .toDBObject();
      clusterDescription.append("configServerReplicationSpec", csReplicationSpec);
      clusterDescription.append("configDiskSizeGB", 10.0);
    }
    return clusterDescription;
  }

  public static BasicDBObject getAWSClusterDescriptionWithoutRegionConfigOverride(
      final ObjectId pGroupId, final String pClusterName, final List<RegionConfig> pRegionConfigs) {
    final BasicDBObject clusterDescription =
        getClusterDescription(pGroupId, pClusterName, List.of())
            .append("clusterType", ClusterDescription.ClusterType.REPLICASET.name())
            .append("hostnameSubdomainLevel", InstanceHostname.SubdomainLevel.MONGODB.name());
    final ReplicationSpec replicationSpec =
        new ReplicationSpec(
            new ObjectId(),
            new ObjectId(),
            new ObjectId(),
            NDSDefaults.ZONE_NAME,
            pRegionConfigs.size(),
            pRegionConfigs);
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec.toDBObject());
    clusterDescription.append("replicationSpecList", replicationSpecList);
    clusterDescription.append(ClusterDescription.FieldDefs.BI_CONNECTOR, getDefaultBiConnector());
    return clusterDescription;
  }

  public static BasicDBObject getAWSClusterDescription() {
    return getAWSClusterDescription(new ObjectId());
  }

  public static BasicDBObject getAWSClusterDescription(final boolean pBiConnectorEnabled) {
    return getAWSClusterDescription(new ObjectId())
        .append(ClusterDescription.FieldDefs.BI_CONNECTOR, getBiConnector(pBiConnectorEnabled));
  }

  public static BasicDBObject getAWSClusterDescription(
      final ObjectId pGroupId,
      final String pMongoDBMajorVersion,
      final String pName,
      final List<String> pHostURIs) {
    return getAWSClusterDescriptionWithMongoURIHosts(pGroupId, pName, pHostURIs)
        .append("clusterType", ClusterDescription.ClusterType.REPLICASET.name())
        .append(ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION, pMongoDBMajorVersion);
  }

  public static BasicDBObject getAWSClusterDescriptionWithEncryptionAtRestProvider(
      final ObjectId pGroupId,
      final String pClusterName,
      ClusterDescription.EncryptionAtRestProvider pEncryptionAtRestProvider) {
    final BasicDBObject doc = getAWSClusterDescription(pGroupId, pClusterName);
    doc.put(
        ClusterDescription.FieldDefs.ENCRYPTION_AT_REST_PROVIDER, pEncryptionAtRestProvider.name());
    return doc;
  }

  public static ClusterDescription getDefaultClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<RegionConfig> pRegionConfigs,
      final Boolean pBiConnectorEnabled) {
    final List<String> hostURIs =
        Arrays.asList(
            "crossCloud-host-01.mmsclouddb.com:27017",
            "crossCloud-host-02.mmsclouddb.com:27017",
            "crossCloud-host-03.mmsclouddb.com:27017");

    final List<ReplicationSpec> replicationSpecList =
        List.of(
            CrossCloudReplicationSpecModelTestFactory.getDefaultCrossCloudReplicationSpec(
                1, pRegionConfigs));

    return new ClusterDescription(
        getClusterDescription(pGroupId, pClusterName, hostURIs)
            .append(
                FieldDefs.REPLICATION_SPEC_LIST,
                replicationSpecList.stream()
                    .map(ReplicationSpec::toDBObject)
                    .collect(DbUtils.toBasicDBList()))
            .append(FieldDefs.CLUSTER_TYPE, ClusterType.REPLICASET)
            .append(FieldDefs.BACKUP_ENABLED, false)
            .append(
                ClusterDescription.FieldDefs.BI_CONNECTOR, getBiConnector(pBiConnectorEnabled)));
  }

  public static BasicDBObject getAzureNVMeClusterDescription(final ObjectId pGroupId) {
    return getAzureNVMeClusterDescription(pGroupId, NDSModelTestFactory.DEFAULT_CLUSTER_NAME);
  }

  public static BasicDBObject getAzureNVMeClusterDescription(
      final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject regionConfig =
        NDSModelTestFactory.getShardRegionConfigForRegion(
                AzureNDSDefaults.REGION_NAME, RegionConfig.MAX_PRIORITY, 3, 0, 0, 1)
            .toDBObject();
    final BasicDBList regionConfigs = new BasicDBList();
    regionConfigs.add(regionConfig);
    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append("regionConfigs", regionConfigs)
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", new ObjectId())
            .append("zoneName", NDSDefaults.ZONE_NAME)
            .append("numShards", 1);
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec);

    List.of("electableSpecs", "readOnlySpecs", "analyticsSpecs", "hiddenSecondarySpecs")
        .forEach(
            specs ->
                ((BasicDBObject) regionConfig.get(specs))
                    .append("instanceSize", AzureNDSInstanceSize.M60_NVME.name())
                    .append("instanceFamily", AzureInstanceFamily.STANDARD_LSV3.name())
                    .append(
                        "os",
                        getOSForCloudProviderAndInstanceFamily(
                                CloudProvider.AZURE, AzureInstanceFamily.STANDARD_LSV3)
                            .name()));

    return getAzureClusterDescription(pGroupId, pClusterName)
        .append("clusterType", ClusterDescription.ClusterType.REPLICASET.name())
        .append("replicationSpecList", replicationSpecList);
  }

  public static BasicDBObject getAWSNVMeClusterDescription(final ObjectId pGroupId) {
    return getAWSNVMeClusterDescription(pGroupId, NDSModelTestFactory.DEFAULT_CLUSTER_NAME);
  }

  public static BasicDBObject getAWSNVMeClusterDescription(
      final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject regionConfig =
        NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSNDSDefaults.REGION_NAME, RegionConfig.MAX_PRIORITY, 3, 0, 0, 1)
            .toDBObject();
    final BasicDBList regionConfigs = new BasicDBList();
    regionConfigs.add(regionConfig);
    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append("regionConfigs", regionConfigs)
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", new ObjectId())
            .append("zoneName", NDSDefaults.ZONE_NAME)
            .append("numShards", 1);
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec);

    List.of("electableSpecs", "readOnlySpecs", "analyticsSpecs", "hiddenSecondarySpecs")
        .forEach(
            specs ->
                ((BasicDBObject) regionConfig.get(specs))
                    .append("diskIOPS", AWSNDSInstanceSize.M40_NVME.getMaxEBSIOPS())
                    .append("ebsVolumeType", VolumeType.Io1.name())
                    .append("encryptEBSVolume", false)
                    .append("instanceSize", AWSNDSInstanceSize.M40_NVME.name())
                    .append("instanceFamily", AWSInstanceFamily.I3.name())
                    .append(
                        "os",
                        NDSModelTestFactory.getOSForCloudProviderAndInstanceFamily(
                                CloudProvider.AWS, AWSInstanceFamily.I3)
                            .name()));

    return getAWSClusterDescription(pGroupId, pClusterName)
        .append("clusterType", ClusterDescription.ClusterType.REPLICASET.name())
        .append("replicationSpecList", replicationSpecList);
  }

  public static ClusterDescription getDefaultClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final List<RegionConfig> pRegionConfigs) {
    return getDefaultClusterDescription(pGroupId, pClusterName, pRegionConfigs, false);
  }

  public static ClusterDescription getDefaultClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pVersion,
      final List<RegionConfig> pRegionConfigs) {
    final VersionUtils.Version version = VersionUtils.parse(pVersion);
    return getDefaultClusterDescription(pGroupId, pClusterName, pRegionConfigs)
        .copy()
        .setMongoDBVersion(version.getVersion())
        .setMongoDBMajorVersion(version.getMajorVersionString())
        .build();
  }

  public static ClusterDescription getCrossCloudClusterDescription(
      final ObjectId pClusterId, final String pName) {
    return NDSModelTestFactory.getDefaultClusterDescription(
        pClusterId,
        pName,
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AWS, AWSRegionName.US_EAST_1, 7, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.AZURE, AzureRegionName.US_EAST_2, 6, 2, 0, 0, 0),
            NDSModelTestFactory.getShardRegionConfigForRegion(
                CloudProvider.GCP, GCPRegionName.ASIA_EAST_2, 5, 1, 0, 0, 0)));
  }

  public static ShardedClusterDescription getDefaultShardedClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<RegionConfig> pRegionConfigs,
      final boolean pHasPrivateEndpointLegacyConnectionStrings) {
    final List<ReplicationSpec> replicationSpecList =
        List.of(
            CrossCloudReplicationSpecModelTestFactory.getDefaultCrossCloudReplicationSpec(
                2, pRegionConfigs));

    final ReplicationSpec configReplicationSpec =
        new ReplicationSpec(
            new ObjectId(),
            new ObjectId(),
            new ObjectId(),
            NDSDefaults.ZONE_NAME,
            1,
            getConfigRegionConfigs(
                pRegionConfigs.stream()
                    .map(RegionConfig::getRegionName)
                    .collect(Collectors.toList())));

    final BasicDBObject shardedClusterDescription =
        getShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL);

    return new ShardedClusterDescription(
        shardedClusterDescription
            .append(
                FieldDefs.REPLICATION_SPEC_LIST,
                replicationSpecList.stream()
                    .map(ReplicationSpec::toDBObject)
                    .collect(DbUtils.toBasicDBList()))
            .append(
                ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
                configReplicationSpec.toDBObject())
            .append(FieldDefs.BACKUP_ENABLED, false)
            .append(FieldDefs.CLUSTER_TYPE, ClusterType.SHARDED)
            .append(
                ShardedClusterDescription.FieldDefs.HAS_PRIVATE_ENDPOINT_LEGACY_CONNECTION_STRINGS,
                pHasPrivateEndpointLegacyConnectionStrings));
  }

  public static ShardedClusterDescription getDefaultShardedClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pVersion,
      final List<RegionConfig> pRegionConfigs,
      final boolean pHasPrivateEndpointLegacyConnectionStrings) {
    final VersionUtils.Version version = VersionUtils.parse(pVersion);
    return getDefaultShardedClusterDescription(
            pGroupId, pClusterName, pRegionConfigs, pHasPrivateEndpointLegacyConnectionStrings)
        .copy()
        .setMongoDBVersion(version.getVersion())
        .setMongoDBMajorVersion(version.getMajorVersionString())
        .build();
  }

  public static ShardedClusterDescription getDefaultShardedClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final List<RegionConfig> pRegionConfigs) {
    return getDefaultShardedClusterDescription(pGroupId, pClusterName, pRegionConfigs, false);
  }

  public static ShardedClusterDescription getDefaultShardedClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pVersion,
      final List<RegionConfig> pRegionConfigs) {
    final VersionUtils.Version version = VersionUtils.parse(pVersion);
    return getDefaultShardedClusterDescription(pGroupId, pClusterName, pRegionConfigs)
        .copy()
        .setMongoDBVersion(version.getVersion())
        .setMongoDBMajorVersion(version.getMajorVersionString())
        .build();
  }

  public static ShardedClusterDescription
      getDefaultShardedClusterDescriptionWithEmbeddedConfigServer(
          final ObjectId pGroupId,
          final String pClusterName,
          final List<RegionConfig> pRegionConfigs) {
    final ShardedClusterDescription shardedClusterDescription =
        getDefaultShardedClusterDescription(pGroupId, pClusterName, pRegionConfigs);

    return shardedClusterDescription
        .copy()
        .setEmbeddedConfigServerReplicationSpec(
            shardedClusterDescription.getReplicationSpecsWithShardData().get(0), false)
        .build();
  }

  public static ShardedClusterDescription getShardedClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final int pNumShards,
      final List<RegionConfig> pRegionConfigs) {
    final List<ReplicationSpec> replicationSpecs =
        List.of(
            CrossCloudReplicationSpecModelTestFactory.getDefaultCrossCloudReplicationSpec(
                pNumShards, pRegionConfigs));

    final BasicDBObject configServerReplicationSpec =
        new BasicDBObject()
            .append(
                "regionConfigs",
                pRegionConfigs.stream()
                    .map(RegionConfig::toDBObject)
                    .collect(DbUtils.toBasicDBList()))
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", new ObjectId())
            .append("zoneName", new ObjectId())
            .append("numShards", 2);

    return new ShardedClusterDescription(
        getShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL)
            .append(
                FieldDefs.REPLICATION_SPEC_LIST,
                replicationSpecs.stream()
                    .map(ReplicationSpec::toDBObject)
                    .collect(DbUtils.toBasicDBList()))
            .append(FieldDefs.BACKUP_ENABLED, false)
            .append(
                ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
                configServerReplicationSpec)
            .append(ShardedClusterDescription.FieldDefs.CONFIG_DISK_SIZE_GB, 10.0));
  }

  public static BasicDBObject getFreeClusterDescription() {
    return getFreeClusterDescription(new TestFreeClusterDescriptionConfig());
  }

  public static BasicDBObject getFreeClusterDescriptionWithClusterPin(
      final String pClusterName, final ObjectId pGroupId, final String pDnsPin) {
    return getFreeClusterDescription(
        new TestFreeClusterDescriptionConfig()
            .setProvisionType(ClusterProvisionType.FAST)
            .setClusterName(pClusterName)
            .setGroupId(pGroupId)
            .setDnsPin(pDnsPin));
  }

  public static BasicDBObject getFreeClusterDescription(
      final String pClusterName, final ObjectId pGroupId) {
    return getFreeClusterDescription(
        new TestFreeClusterDescriptionConfig()
            .setProvisionType(ClusterProvisionType.FAST)
            .setClusterName(pClusterName)
            .setGroupId(pGroupId));
  }

  public static BasicDBObject getFreeClusterDescription(
      final TestFreeClusterDescriptionConfig testFreeClusterDescriptionConfig) {
    final BasicDBList hostsDBList = new BasicDBList();
    hostsDBList.addAll(testFreeClusterDescriptionConfig.getHostUris());

    final BasicDBList privateHostsDBList = new BasicDBList();
    // Add every uri to private list but with -pri added before the first dot
    testFreeClusterDescriptionConfig
        .getHostUris()
        .forEach(uri -> privateHostsDBList.add(uri.replaceFirst("\\.", "-pri.")));

    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", new ObjectId())
            .append("zoneName", NDSDefaults.ZONE_NAME)
            .append("numShards", 1)
            .append(
                "regionConfigs",
                testFreeClusterDescriptionConfig.getRegionConfigs().stream()
                    .map(RegionConfig::toDBObject)
                    .collect(DbUtils.toBasicDBList()));

    final BasicDBList specList = new BasicDBList();
    specList.add(replicationSpec);

    final BasicDBObject id =
        new BasicDBObject()
            .append(
                ClusterDescription.FieldDefs.NAME,
                testFreeClusterDescriptionConfig.getClusterName())
            .append(
                ClusterDescription.FieldDefs.GROUP_ID,
                testFreeClusterDescriptionConfig.getGroupId());

    final BasicDBObject biConnector = getDefaultBiConnector();
    final BasicDBObject geoSharding = getDefaultGeoSharding();

    final BasicDBObject freeProviderOptions =
        new BasicDBObject()
            .append(
                ClusterDescription.FieldDefs.PROVIDER_NAME,
                testFreeClusterDescriptionConfig.getBackingProvider().name())
            .append(
                ClusterDescriptionProviderOptions.FieldDefs.REGION_NAME,
                testFreeClusterDescriptionConfig.getRegionName().getName())
            .append(
                ClusterDescription.FieldDefs.INSTANCE_SIZE,
                testFreeClusterDescriptionConfig.getInstanceSize().name())
            .append(
                FreeTenantProviderOptions.FieldDefs.LIMITS_PROFILE,
                testFreeClusterDescriptionConfig.getLimitsProfile().name())
            .append(
                FreeTenantProviderOptions.FieldDefs.TENANT_BACKUP_ENABLED,
                testFreeClusterDescriptionConfig.getTenantBackupEnabled())
            .append(
                FreeTenantProviderOptions.FieldDefs.NEXT_BACKUP_DATE,
                testFreeClusterDescriptionConfig.getNextBackupDate())
            .append(
                FreeTenantProviderOptions.FieldDefs.USER_NOTIFIED_ABOUT_PAUSE_DATE,
                testFreeClusterDescriptionConfig.getUserNotifiedAboutPauseDate())
            .append(
                FreeTenantProviderOptions.FieldDefs.NDS_ACCESS_REVOKED_DATE,
                testFreeClusterDescriptionConfig.getNDSAccessRevokedDate())
            .append(
                FreeTenantProviderOptions.FieldDefs.NEEDS_UNPAUSE_TENANT_RESTORE,
                testFreeClusterDescriptionConfig.needsUnpauseTenantRestore())
            .append(
                FreeTenantProviderOptions.FieldDefs.CONNECTION_LIMIT,
                testFreeClusterDescriptionConfig.getConnectionLimit())
            .append(
                FreeTenantProviderOptions.FieldDefs.EXCLUDE_FROM_PAUSE,
                testFreeClusterDescriptionConfig.shouldExcludeFromPause());

    final String subdomainLevel;
    if (testFreeClusterDescriptionConfig.getBackingProvider() == CloudProvider.AZURE) {
      subdomainLevel = SubdomainLevel.AZURE.name();
    } else if (testFreeClusterDescriptionConfig.getBackingProvider() == CloudProvider.GCP) {
      subdomainLevel = SubdomainLevel.GCP.name();
    } else {
      subdomainLevel = SubdomainLevel.MONGODB.name();
    }
    return new BasicDBObject()
        .append(
            ClusterDescription.FieldDefs.CLUSTER_TYPE,
            ClusterDescription.ClusterType.REPLICASET.name())
        .append(ClusterDescription.FieldDefs.INTERNAL_CLUSTER_ROLE, InternalClusterRole.NONE.name())
        .append(ClusterDescription.FieldDefs.ID, id)
        .append(
            ClusterDescription.FieldDefs.UNIQUE_ID, testFreeClusterDescriptionConfig.getUniqueId())
        .append(
            ClusterDescription.FieldDefs.MONGODB_VERSION,
            VersionUtils.Version.fromString(testFreeClusterDescriptionConfig.getMongoDBVersion())
                .getMaintenanceVersionString())
        .append(
            ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION,
            testFreeClusterDescriptionConfig.getMongoDBVersion())
        .append(ClusterDescription.FieldDefs.CREATE_DATE, new Date())
        .append(ClusterDescription.FieldDefs.LAST_UPDATE_DATE, new Date())
        .append(ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST, specList)
        .append(
            ClusterDescription.FieldDefs.DISK_SIZE_GB,
            testFreeClusterDescriptionConfig.getInstanceSize().getDiskSizeGB())
        .append(ClusterDescription.FieldDefs.MONGODB_URI_HOSTS, hostsDBList)
        .append(ClusterDescription.FieldDefs.MONGODB_URI_LAST_UPDATE_DATE, new Date())
        .append(ClusterDescription.FieldDefs.PRIVATE_MONGODB_URI_HOSTS, privateHostsDBList)
        .append(ClusterDescription.FieldDefs.STATE, "WORKING")
        .append(ClusterDescription.FieldDefs.IS_MTM, false)
        .append(ClusterDescription.FieldDefs.IS_PAUSED, testFreeClusterDescriptionConfig.isPaused())
        .append(
            ClusterDescription.FieldDefs.PAUSED_DATE,
            testFreeClusterDescriptionConfig.getPausedDate())
        .append(ClusterDescription.FieldDefs.BACKUP_ENABLED, false)
        .append(ClusterDescription.FieldDefs.DISK_BACKUP_ENABLED, false)
        .append(ClusterDescription.FieldDefs.PIT_ENABLED, false)
        .append(ClusterDescription.FieldDefs.BI_CONNECTOR, biConnector)
        .append(ClusterDescription.FieldDefs.GEO_SHARDING, geoSharding)
        .append(
            ClusterDescription.FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
            ClusterDescription.EncryptionAtRestProvider.NONE.name())
        .append(
            FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
            ProcessRestartAllowedState.NONE.name())
        .append(
            ClusterDescription.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
            InstanceHostname.HostnameScheme.LEGACY.name())
        .append(ClusterDescription.FieldDefs.HOSTNAME_SUBDOMAIN_LEVEL, subdomainLevel)
        .append(ClusterDescription.FieldDefs.CLOUD_PROVIDER_OPTIONS, freeProviderOptions)
        .append(
            ClusterDescription.FieldDefs.DEPLOYMENT_CLUSTER_NAME,
            testFreeClusterDescriptionConfig.getClusterName())
        .append(FieldDefs.ROOT_CERT_TYPE, RootCertType.ISRGROOTX1.name())
        .append(FieldDefs.DELETE_REQUESTED, testFreeClusterDescriptionConfig.isDeleteRequested())
        .append("dnsPin", testFreeClusterDescriptionConfig.getDnsPin())
        .append("clusterNamePrefix", testFreeClusterDescriptionConfig.getClusterName())
        .append(
            FieldDefs.CLUSTER_PROVISION_TYPE,
            testFreeClusterDescriptionConfig.getProvisionType().name())
        .append(
            FieldDefs.TERMINATION_PROTECTION_ENABLED,
            testFreeClusterDescriptionConfig.getTerminationProtectionEnabled());
  }

  public static BasicDBObject getDefaultServerlessClusterDescription(
      final ObjectId pGroupId, final String pClusterName) {
    return getServerlessClusterDescription(
        new TestServerlessClusterDescriptionConfig()
            .setGroupId(pGroupId)
            .setClusterName(pClusterName)
            .setHostUris(
                Arrays.asList(
                    pClusterName + "-shard-00-01-ab123.mmsclouddb.com:27017",
                    pClusterName + "-shard-00-02-ab123.mmsclouddb.com:27017")));
  }

  public static BasicDBObject getServerlessClusterDescription() {
    return getServerlessClusterDescription(new TestServerlessClusterDescriptionConfig());
  }

  public static BasicDBObject getServerlessClusterDescription(
      final TestServerlessClusterDescriptionConfig testServerlessClusterDescriptionConfig) {
    final BasicDBList hostsDBList = new BasicDBList();
    hostsDBList.addAll(testServerlessClusterDescriptionConfig.getHostUris());

    final BasicDBList privateHostsDBList = new BasicDBList();
    // Add every uri to private list but with -pri added before the first dot
    testServerlessClusterDescriptionConfig
        .getHostUris()
        .forEach(uri -> privateHostsDBList.add(uri.replaceFirst("\\.", "-pri.")));

    final List<RegionConfig> regionConfigs =
        testServerlessClusterDescriptionConfig.getRegionConfigs();
    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", new ObjectId())
            .append("zoneName", NDSDefaults.ZONE_NAME)
            .append("numShards", 1)
            .append(
                "regionConfigs",
                regionConfigs.stream()
                    .map(RegionConfig::toDBObject)
                    .collect(DbUtils.toBasicDBList()));

    final BasicDBList specList = new BasicDBList();
    specList.add(replicationSpec);

    final BasicDBObject id =
        new BasicDBObject()
            .append(
                ClusterDescription.FieldDefs.NAME,
                testServerlessClusterDescriptionConfig.getClusterName())
            .append(
                ClusterDescription.FieldDefs.GROUP_ID,
                testServerlessClusterDescriptionConfig.getGroupId());

    final BasicDBObject biConnector = getDefaultBiConnector();
    final BasicDBObject geoSharding = getDefaultGeoSharding();

    final HardwareSpec hardwareSpec =
        new ServerlessHardwareSpec(
            3,
            testServerlessClusterDescriptionConfig.getInstanceSize(),
            testServerlessClusterDescriptionConfig.getBackingProvider());

    ((BasicDBObject) ((BasicDBList) replicationSpec.get("regionConfigs")).get(0))
        .append("electableSpecs", hardwareSpec.toDBObject())
        .append("analyticsSpecs", hardwareSpec.copy().setNodeCount(0).build().toDBObject())
        .append("readOnlySpecs", hardwareSpec.copy().setNodeCount(0).build().toDBObject());

    final ServerlessTenantProviderOptions serverlessTenantProviderOptions =
        new ServerlessTenantProviderOptions(
            testServerlessClusterDescriptionConfig.getInstanceSize().getMaxCollections(),
            testServerlessClusterDescriptionConfig
                .getInstanceSize()
                .getMaxIncomingConnections()
                .get(),
            testServerlessClusterDescriptionConfig.getInstanceSize().getMaxDatabases(),
            testServerlessClusterDescriptionConfig
                .getInstanceSize()
                .getMaxAllowedDiskSizeGBForServerless(),
            false,
            testServerlessClusterDescriptionConfig.getInstanceSize(),
            null,
            null,
            false,
            null);
    final BasicDBObject providerOptions = hardwareSpec.getCloudProviderOptionsDBObject();
    serverlessTenantProviderOptions.toDBObject().forEach(providerOptions::append);

    final String subdomainLevel;
    if (testServerlessClusterDescriptionConfig.getBackingProvider() == CloudProvider.AZURE) {
      subdomainLevel = SubdomainLevel.AZURE.name();
    } else if (testServerlessClusterDescriptionConfig.getBackingProvider() == CloudProvider.GCP) {
      subdomainLevel = SubdomainLevel.GCP.name();
    } else {
      subdomainLevel = SubdomainLevel.MONGODB.name();
    }
    return new BasicDBObject()
        .append(
            ClusterDescription.FieldDefs.CLUSTER_TYPE,
            ClusterDescription.ClusterType.REPLICASET.name())
        .append(ClusterDescription.FieldDefs.INTERNAL_CLUSTER_ROLE, InternalClusterRole.NONE.name())
        .append(ClusterDescription.FieldDefs.ID, id)
        .append(
            ClusterDescription.FieldDefs.UNIQUE_ID,
            testServerlessClusterDescriptionConfig.getUniqueId())
        .append(ClusterDescription.FieldDefs.CLOUD_PROVIDER_OPTIONS, providerOptions)
        .append(FieldDefs.VERSION_RELEASE_SYSTEM, VersionReleaseSystem.LTS.name())
        .append(
            ClusterDescription.FieldDefs.MONGODB_VERSION,
            VersionUtils.Version.fromString(TEST_SERVERLESS_MONGODB_VERSION)
                .getMaintenanceVersionString())
        .append(
            ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION,
            TEST_SERVERLESS_MONGODB_MAJOR_VERSION)
        .append(ClusterDescription.FieldDefs.CREATE_DATE, new Date())
        .append(ClusterDescription.FieldDefs.LAST_UPDATE_DATE, new Date())
        .append(ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST, specList)
        .append(
            ClusterDescription.FieldDefs.DISK_SIZE_GB,
            testServerlessClusterDescriptionConfig
                .getInstanceSize()
                .getMaxAllowedDiskSizeGBForServerless())
        .append(ClusterDescription.FieldDefs.MONGODB_URI_HOSTS, hostsDBList)
        .append(ClusterDescription.FieldDefs.MONGODB_URI_LAST_UPDATE_DATE, new Date())
        .append(ClusterDescription.FieldDefs.PRIVATE_MONGODB_URI_HOSTS, privateHostsDBList)
        .append(ClusterDescription.FieldDefs.STATE, "WORKING")
        .append(ClusterDescription.FieldDefs.IS_MTM, false)
        .append(ClusterDescription.FieldDefs.IS_PAUSED, false)
        .append(ClusterDescription.FieldDefs.BACKUP_ENABLED, false)
        .append(ClusterDescription.FieldDefs.DISK_BACKUP_ENABLED, false)
        .append(ClusterDescription.FieldDefs.PIT_ENABLED, false)
        .append(ClusterDescription.FieldDefs.BI_CONNECTOR, biConnector)
        .append(ClusterDescription.FieldDefs.GEO_SHARDING, geoSharding)
        .append(
            ClusterDescription.FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
            ClusterDescription.EncryptionAtRestProvider.NONE.name())
        .append(
            FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
            ProcessRestartAllowedState.NONE.name())
        .append(
            ClusterDescription.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS,
            InstanceHostname.HostnameScheme.LEGACY.name())
        .append(ClusterDescription.FieldDefs.HOSTNAME_SUBDOMAIN_LEVEL, subdomainLevel)
        .append(
            ClusterDescription.FieldDefs.DEPLOYMENT_CLUSTER_NAME,
            testServerlessClusterDescriptionConfig.getClusterName())
        .append(
            FieldDefs.LOAD_BALANCED_HOSTNAME,
            testServerlessClusterDescriptionConfig.getLoadBalancedHostname())
        .append(
            FieldDefs.LOAD_BALANCED_MESH_HOSTNAME,
            testServerlessClusterDescriptionConfig.getLoadBalancedMeshHostname())
        .append(
            FieldDefs.CLUSTER_PROVISION_TYPE,
            testServerlessClusterDescriptionConfig.getClusterProvisionType())
        .append(
            FieldDefs.DELETE_REQUESTED, testServerlessClusterDescriptionConfig.isDeleteRequested())
        .append(
            FieldDefs.CLUSTER_NAME_PREFIX,
            testServerlessClusterDescriptionConfig
                .getClusterName()
                .toLowerCase()
                .substring(
                    0,
                    Math.min(
                        testServerlessClusterDescriptionConfig.getClusterName().length(),
                        Defaults.CLUSTER_NAME_UNIQUE_PREFIX_LENGTH)))
        .append(FieldDefs.DNS_PIN, testServerlessClusterDescriptionConfig.getDnsPin())
        .append(
            FieldDefs.SERVERLESS_BACKUP_OPTIONS,
            testServerlessClusterDescriptionConfig.getBackupOptions().toDBObject());
  }

  public static BasicDBObject getFlexClusterDescription() {
    return getFlexClusterDescription(new TestFlexClusterDescriptionConfig());
  }

  public static ClusterDescription getFormerServerlessFlexClusterDescription(
      final ObjectId pGroupId) {
    return getFormerServerlessFlexClusterDescription(
        new TestFlexClusterDescriptionConfig().setGroupId(pGroupId));
  }

  public static ClusterDescription getFormerServerlessFlexClusterDescription(
      final TestFlexClusterDescriptionConfig pConfig) {
    return new ClusterDescription(
        NDSModelTestFactory.getFlexClusterDescription(pConfig)
            .append(
                ClusterDescription.FieldDefs.FLEX_TENANT_MIGRATION_STATE,
                FlexTenantMigrationState.builder()
                    .setFormerCloudProvider(CloudProvider.SERVERLESS)
                    .setVisibleAutoIndexingState(
                        pConfig.getFlexTenantMigrationState() == null
                            ? false
                            : pConfig
                                .getFlexTenantMigrationState()
                                .getVisibleAutoIndexingState()
                                .orElse(false))
                    .build()
                    .toDBObject()));
  }

  public static BasicDBObject getFormerSharedFlexClusterDescription(
      final TestFlexClusterDescriptionConfig pConfig) {
    return getFlexClusterDescription(pConfig)
        .append(
            ClusterDescription.FieldDefs.FLEX_TENANT_MIGRATION_STATE,
            FlexTenantMigrationState.builder()
                .setFormerCloudProvider(CloudProvider.FREE)
                .setFormerInstanceSize(FreeInstanceSize.M2)
                .setIsTenantCreatedFromApi(false)
                .build()
                .toDBObject());
  }

  public static BasicDBObject getFlexClusterDescriptionWithClusterPin(
      final String pClusterName, final ObjectId pGroupId, final String pDnsPin) {
    return getFlexClusterDescription(
        new TestFlexClusterDescriptionConfig()
            .setProvisionType(ClusterProvisionType.FAST)
            .setClusterName(pClusterName)
            .setGroupId(pGroupId)
            .setDnsPin(pDnsPin));
  }

  public static BasicDBObject getFlexClusterDescription(
      final String pClusterName, final ObjectId pGroupId) {
    return getFlexClusterDescription(
        new TestFlexClusterDescriptionConfig()
            .setProvisionType(ClusterProvisionType.FAST)
            .setClusterName(pClusterName)
            .setGroupId(pGroupId));
  }

  public static BasicDBObject getFlexClusterDescription(
      final TestFlexClusterDescriptionConfig pTestFlexClusterDescriptionConfig) {
    final BasicDBList hostsDBList = new BasicDBList();
    hostsDBList.addAll(pTestFlexClusterDescriptionConfig.getHostUris());

    final BasicDBList privateHostsDBList = new BasicDBList();
    // Add every uri to private list but with -pri added before the first dot
    pTestFlexClusterDescriptionConfig
        .getHostUris()
        .forEach(uri -> privateHostsDBList.add(uri.replaceFirst("\\.", "-pri.")));

    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", new ObjectId())
            .append("zoneName", NDSDefaults.ZONE_NAME)
            .append("numShards", 1)
            .append(
                "regionConfigs",
                pTestFlexClusterDescriptionConfig.getRegionConfigs().stream()
                    .map(RegionConfig::toDBObject)
                    .collect(DbUtils.toBasicDBList()));

    final BasicDBList specList = new BasicDBList();
    specList.add(replicationSpec);

    final BasicDBObject id =
        new BasicDBObject()
            .append(
                ClusterDescription.FieldDefs.NAME,
                pTestFlexClusterDescriptionConfig.getClusterName())
            .append(
                ClusterDescription.FieldDefs.GROUP_ID,
                pTestFlexClusterDescriptionConfig.getGroupId());

    final BasicDBObject biConnector = getDefaultBiConnector();
    final BasicDBObject geoSharding = getDefaultGeoSharding();

    final HardwareSpec hardwareSpec =
        new FlexHardwareSpec(
            3,
            pTestFlexClusterDescriptionConfig.getInstanceSize(),
            pTestFlexClusterDescriptionConfig.getBackingProvider());

    ((BasicDBObject) ((BasicDBList) replicationSpec.get("regionConfigs")).get(0))
        .append("electableSpecs", hardwareSpec.toDBObject())
        .append("analyticsSpecs", hardwareSpec.copy().setNodeCount(0).build().toDBObject())
        .append("readOnlySpecs", hardwareSpec.copy().setNodeCount(0).build().toDBObject());

    final FlexTenantProviderOptions flexTenantProviderOptions =
        new FlexTenantProviderOptions.Builder()
            .setCollectionLimit(FlexInstanceSize.FLEX.getMaxCollections())
            .setConnectionLimit(FlexInstanceSize.FLEX.getMaxIncomingConnections().orElseThrow())
            .setDatabaseLimit(FlexInstanceSize.FLEX.getMaxDatabases())
            .setNextBackupDate(new Date())
            .setOperationsPerSecondLimit(FlexInstanceSize.FLEX.getMaxOperationsPerSecond())
            .setDiskSizeGBLimit(FlexInstanceSize.FLEX.getMaxAllowedDiskSizeGBForFlex())
            .build();

    final BasicDBObject providerOptions = flexTenantProviderOptions.toDBObject();
    // NOTE: There was a prior(?) requirement that we backfill the provider options with fields from
    //       the hardware spec related to cross-cloud. Omitting for now, but leaving context in case
    //       there are still dependencies.
    // final BasicDBObject providerOptions = hardwareSpec.getCloudProviderOptionsDBObject();
    // flexTenantProviderOptions.toDBObject().forEach(providerOptions::append);

    final String subdomainLevel;
    if (pTestFlexClusterDescriptionConfig.getBackingProvider() == CloudProvider.AZURE) {
      subdomainLevel = SubdomainLevel.AZURE.name();
    } else if (pTestFlexClusterDescriptionConfig.getBackingProvider() == CloudProvider.GCP) {
      subdomainLevel = SubdomainLevel.GCP.name();
    } else {
      subdomainLevel = SubdomainLevel.MONGODB.name();
    }

    final BasicDBObject flexTenantMigrationState =
        pTestFlexClusterDescriptionConfig.getFlexTenantMigrationState() != null
            ? pTestFlexClusterDescriptionConfig.getFlexTenantMigrationState().toDBObject()
            : null;

    return new BasicDBObject()
        .append(
            ClusterDescription.FieldDefs.CLUSTER_TYPE,
            ClusterDescription.ClusterType.REPLICASET.name())
        .append(ClusterDescription.FieldDefs.INTERNAL_CLUSTER_ROLE, InternalClusterRole.NONE.name())
        .append(ClusterDescription.FieldDefs.ID, id)
        .append(
            ClusterDescription.FieldDefs.UNIQUE_ID, pTestFlexClusterDescriptionConfig.getUniqueId())
        .append(
            ClusterDescription.FieldDefs.MONGODB_VERSION,
            VersionUtils.Version.fromString(pTestFlexClusterDescriptionConfig.getMongoDBVersion())
                .getMaintenanceVersionString())
        .append(
            ClusterDescription.FieldDefs.MONGODB_MAJOR_VERSION,
            pTestFlexClusterDescriptionConfig.getMongoDBVersion())
        .append(
            ClusterDescription.FieldDefs.CREATE_DATE,
            Objects.requireNonNullElse(
                pTestFlexClusterDescriptionConfig.getCreatedDate(), new Date()))
        .append(ClusterDescription.FieldDefs.LAST_UPDATE_DATE, new Date())
        .append(ClusterDescription.FieldDefs.REPLICATION_SPEC_LIST, specList)
        .append(
            ClusterDescription.FieldDefs.DISK_SIZE_GB,
            pTestFlexClusterDescriptionConfig.getInstanceSize().getDiskSizeGB())
        .append(ClusterDescription.FieldDefs.MONGODB_URI_HOSTS, hostsDBList)
        .append(ClusterDescription.FieldDefs.MONGODB_URI_LAST_UPDATE_DATE, new Date())
        .append(ClusterDescription.FieldDefs.PRIVATE_MONGODB_URI_HOSTS, privateHostsDBList)
        .append(
            ClusterDescription.FieldDefs.STATE,
            Optional.ofNullable(pTestFlexClusterDescriptionConfig.getDeletedDate()).isEmpty()
                ? ClusterDescription.State.WORKING
                : ClusterDescription.State.DELETED)
        .append(ClusterDescription.FieldDefs.IS_MTM, false)
        .append(ClusterDescription.FieldDefs.IS_PAUSED, false)
        .append(ClusterDescription.FieldDefs.BACKUP_ENABLED, false)
        .append(ClusterDescription.FieldDefs.DISK_BACKUP_ENABLED, false)
        .append(ClusterDescription.FieldDefs.PIT_ENABLED, false)
        .append(ClusterDescription.FieldDefs.BI_CONNECTOR, biConnector)
        .append(ClusterDescription.FieldDefs.GEO_SHARDING, geoSharding)
        .append(
            ClusterDescription.FieldDefs.ENCRYPTION_AT_REST_PROVIDER,
            ClusterDescription.EncryptionAtRestProvider.NONE.name())
        .append(
            FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
            ProcessRestartAllowedState.NONE.name())
        .append(
            ClusterDescription.FieldDefs.HOSTNAME_SCHEME_FOR_AGENTS, HostnameScheme.INTERNAL.name())
        .append(ClusterDescription.FieldDefs.HOSTNAME_SUBDOMAIN_LEVEL, subdomainLevel)
        .append(ClusterDescription.FieldDefs.CLOUD_PROVIDER_OPTIONS, providerOptions)
        .append(
            ClusterDescription.FieldDefs.DEPLOYMENT_CLUSTER_NAME,
            pTestFlexClusterDescriptionConfig.getClusterName())
        .append(FieldDefs.ROOT_CERT_TYPE, RootCertType.ISRGROOTX1.name())
        .append(FieldDefs.DELETE_REQUESTED, pTestFlexClusterDescriptionConfig.isDeleteRequested())
        .append(FieldDefs.DNS_PIN, pTestFlexClusterDescriptionConfig.getDnsPin())
        .append(FieldDefs.DELETED_DATE, pTestFlexClusterDescriptionConfig.getDeletedDate())
        .append(
            FieldDefs.CLOUD_PROVIDER_OPTIONS,
            new FlexTenantProviderOptions(
                    new BasicDBObject(
                        FlexTenantProviderOptions.FieldDefs.NEXT_BACKUP_DATE,
                        pTestFlexClusterDescriptionConfig.getNextBackupDate()),
                    FlexInstanceSize.FLEX)
                .toDBObject())
        .append(
            FieldDefs.CLUSTER_NAME_PREFIX,
            pTestFlexClusterDescriptionConfig
                .getClusterName()
                .toLowerCase()
                .substring(
                    0,
                    Math.min(
                        pTestFlexClusterDescriptionConfig.getClusterName().length(),
                        Defaults.CLUSTER_NAME_UNIQUE_PREFIX_LENGTH)))
        .append(
            FieldDefs.CLUSTER_PROVISION_TYPE,
            pTestFlexClusterDescriptionConfig.getProvisionType().name())
        .append(
            FieldDefs.TERMINATION_PROTECTION_ENABLED,
            pTestFlexClusterDescriptionConfig.getTerminationProtectionEnabled())
        .append(FieldDefs.FLEX_TENANT_MIGRATION_STATE, flexTenantMigrationState)
        .append(
            FieldDefs.LOAD_BALANCED_HOSTNAME,
            pTestFlexClusterDescriptionConfig.getLoadBalancedHostname())
        .append(
            FieldDefs.LOAD_BALANCED_MESH_HOSTNAME,
            pTestFlexClusterDescriptionConfig.getLoadBalancedMeshHostname());
  }

  public static BasicDBObject getAzureShardedClusterDescription() {
    return getAzureShardedClusterDescription(new ObjectId());
  }

  public static BasicDBObject getAzureShardedClusterDescription(final ObjectId pGroupId) {
    return getAzureShardedClusterDescription(
        pGroupId, DEFAULT_CLUSTER_NAME, InstanceHostname.HostnameScheme.LEGACY);
  }

  public static BasicDBObject getAzureShardedClusterDescription(
      final ObjectId pGroupId, final String pName, final boolean pIsOnSsdV2) {
    final List<RegionConfig> azureRegionConfigs = getAzureRegionConfigs(pIsOnSsdV2);

    return getAzureShardedClusterDescription(pGroupId, pName, azureRegionConfigs);
  }

  public static BasicDBObject getAzureShardedClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final InstanceHostname.HostnameScheme pHostnameScheme) {
    return getAzureShardedClusterDescription(
        pGroupId, pClusterName, pHostnameScheme, List.of(AzureNDSDefaults.REGION_NAME));
  }

  public static BasicDBObject getAzureShardedClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final List<RegionConfig> pRegionConfigs) {
    final AzureDiskType diskType = AzureDiskType.P10;
    final AzureDiskType configServerDiskType = AzureDiskType.P4;

    final ObjectId zoneId = new ObjectId();
    final String zoneName = NDSDefaults.ZONE_NAME;

    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append(
                "regionConfigs",
                pRegionConfigs.stream()
                    .map(RegionConfig::toDBObject)
                    .collect(DbUtils.toBasicDBList()))
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", zoneId)
            .append("zoneName", zoneName)
            .append("numShards", 1);
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec);

    final BasicDBObject configServerReplicationSpec =
        new BasicDBObject()
            .append(
                "regionConfigs",
                pRegionConfigs.stream()
                    .map(RegionConfig::toDBObject)
                    .collect(DbUtils.toBasicDBList()))
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", zoneId)
            .append("zoneName", zoneName)
            .append("numShards", 2);

    return getShardedClusterDescription(
            pGroupId, pClusterName, InstanceHostname.HostnameScheme.LEGACY)
        .append("clusterType", ClusterType.SHARDED.name())
        .append("hostnameSubdomainLevel", SubdomainLevel.AZURE.name())
        .append("replicationSpecList", replicationSpecList)
        .append("configServerReplicationSpec", configServerReplicationSpec)
        .append("diskSizeGB", diskType.getSizeGB())
        .append("configDiskSizeGB", configServerDiskType.getSizeGB());
  }

  public static BasicDBObject getAzureShardedClusterDescriptionWithEmbeddedConfig(
      final ObjectId pGroupId) {
    return getAzureShardedClusterDescriptionWithEmbeddedConfig(
        pGroupId,
        DEFAULT_CLUSTER_NAME,
        InstanceHostname.HostnameScheme.LEGACY,
        List.of(AzureNDSDefaults.REGION_NAME));
  }

  public static BasicDBObject getAzureShardedClusterDescriptionWithEmbeddedConfig(
      final ObjectId pGroupId,
      final String pClusterName,
      final InstanceHostname.HostnameScheme pHostnameScheme,
      final List<AzureRegionName> pRegions) {
    final AzureDiskType diskType = AzureDiskType.P10;

    final ObjectId zoneId = new ObjectId();
    final String zoneName = NDSDefaults.ZONE_NAME;

    final BasicDBList regionConfigs = new BasicDBList();
    final List<BasicDBObject> rsRegionConfigList =
        pRegions.stream()
            .map((regionName) -> getReplicaSetRegionConfig(diskType, regionName))
            .collect(Collectors.toList());
    regionConfigs.addAll(rsRegionConfigList);
    final ObjectId rsId = new ObjectId();
    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append("regionConfigs", regionConfigs)
            .append("id", rsId)
            .append("externalId", new ObjectId())
            .append("zoneId", zoneId)
            .append("zoneName", zoneName)
            .append("numShards", 2);
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec);

    final BasicDBObject embeddedConfigServerReplicationSpec =
        new BasicDBObject()
            .append("configShardReplicationSpecId", rsId)
            .append("pinned", false)
            .append("type", ConfigServerType.EMBEDDED);

    return getShardedClusterDescription(pGroupId, pClusterName, pHostnameScheme)
        .append("clusterType", ClusterType.SHARDED.name())
        .append("hostnameSubdomainLevel", SubdomainLevel.AZURE.name())
        .append("replicationSpecList", replicationSpecList)
        .append("configServerReplicationSpec", embeddedConfigServerReplicationSpec)
        .append("diskSizeGB", diskType.getSizeGB())
        // The cluster uses embed config so the actual embedd config server disk will not use this
        // value.
        .append("configDiskSizeGB", AzureDiskType.P4.getSizeGB());
  }

  public static BasicDBObject getAzureShardedClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final InstanceHostname.HostnameScheme pHostnameScheme,
      final List<AzureRegionName> pRegions) {
    final AzureDiskType diskType = AzureDiskType.P10;
    final AzureDiskType configServerDiskType = AzureDiskType.P4;

    final ObjectId zoneId = new ObjectId();
    final String zoneName = NDSDefaults.ZONE_NAME;

    final BasicDBList regionConfigs = new BasicDBList();
    final List<BasicDBObject> rsRegionConfigList =
        pRegions.stream()
            .map((regionName) -> getReplicaSetRegionConfig(diskType, regionName))
            .collect(Collectors.toList());
    regionConfigs.addAll(rsRegionConfigList);
    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append("regionConfigs", regionConfigs)
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", zoneId)
            .append("zoneName", zoneName)
            .append("numShards", 2);
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec);

    final BasicDBList csRegionConfigs = new BasicDBList();
    final List<BasicDBObject> csRegionConfigList =
        pRegions.stream()
            .map((regionName) -> getConfigServerRegionConfig(configServerDiskType, regionName))
            .collect(Collectors.toList());
    csRegionConfigs.addAll(csRegionConfigList);
    final BasicDBObject configServerReplicationSpec =
        new BasicDBObject()
            .append("regionConfigs", csRegionConfigs)
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", zoneId)
            .append("zoneName", zoneName)
            .append("numShards", 1);

    return getShardedClusterDescription(pGroupId, pClusterName, pHostnameScheme)
        .append("clusterType", ClusterType.SHARDED.name())
        .append("hostnameSubdomainLevel", SubdomainLevel.AZURE.name())
        .append("replicationSpecList", replicationSpecList)
        .append("configServerReplicationSpec", configServerReplicationSpec)
        .append("diskSizeGB", diskType.getSizeGB())
        .append("configDiskSizeGB", configServerDiskType.getSizeGB());
  }

  private static BasicDBObject getReplicaSetRegionConfig(
      final AzureDiskType pDiskType, final AzureRegionName pRegion) {
    return new BasicDBObject()
        .append("cloudProvider", CloudProvider.AZURE.name())
        .append("regionName", pRegion.name())
        .append("priority", RegionConfig.MAX_PRIORITY)
        .append("autoScaling", getDefaultAutoScaling())
        .append("analyticsAutoScaling", getDefaultAutoScaling())
        .append(
            "electableSpecs",
            new BasicDBObject()
                .append("nodeCount", 3)
                .append("instanceSize", AzureNDSInstanceSize.M30.name())
                .append("instanceFamily", AzureInstanceFamily.STANDARD_DSV2.name())
                .append(
                    "os",
                    getOSForCloudProviderAndInstanceFamily(
                            CloudProvider.AZURE, AzureInstanceFamily.STANDARD_DSV2)
                        .name())
                .append("diskType", pDiskType.name()))
        .append(
            "readOnlySpecs",
            new BasicDBObject()
                .append("nodeCount", 0)
                .append("instanceSize", AzureNDSInstanceSize.M30.name())
                .append("instanceFamily", AzureInstanceFamily.STANDARD_DSV2.name())
                .append(
                    "os",
                    getOSForCloudProviderAndInstanceFamily(
                            CloudProvider.AZURE, AzureInstanceFamily.STANDARD_DSV2)
                        .name())
                .append("diskType", pDiskType.name()))
        .append(
            "analyticsSpecs",
            new BasicDBObject()
                .append("nodeCount", 0)
                .append("instanceSize", AzureNDSInstanceSize.M30.name())
                .append("instanceFamily", AzureInstanceFamily.STANDARD_DSV2.name())
                .append(
                    "os",
                    getOSForCloudProviderAndInstanceFamily(
                            CloudProvider.AZURE, AzureInstanceFamily.STANDARD_DSV2)
                        .name())
                .append("diskType", pDiskType.name()))
        .append(
            "hiddenSecondarySpecs",
            new BasicDBObject()
                .append("nodeCount", 0)
                .append("instanceSize", AzureNDSInstanceSize.M30.name())
                .append("instanceFamily", AzureInstanceFamily.STANDARD_DSV2.name())
                .append(
                    "os",
                    getOSForCloudProviderAndInstanceFamily(
                            CloudProvider.AZURE, AzureInstanceFamily.STANDARD_DSV2)
                        .name())
                .append("diskType", pDiskType.name()));
  }

  private static BasicDBObject getConfigServerRegionConfig(
      final AzureDiskType pDiskType, final AzureRegionName pRegionName) {
    return new BasicDBObject()
        .append("cloudProvider", CloudProvider.AZURE.name())
        .append("regionName", pRegionName.name())
        .append("priority", RegionConfig.MAX_PRIORITY)
        .append("autoScaling", getDefaultAutoScaling())
        .append("analyticsAutoScaling", getDefaultAutoScaling())
        .append(
            "electableSpecs",
            new BasicDBObject()
                .append("nodeCount", 3)
                .append("isConfigServerHardware", true)
                .append("configServerInstanceSize", AzureNDSInstanceSize.M30.name())
                .append("configServerInstanceFamily", AzureInstanceFamily.STANDARD_DSV2.name())
                .append(
                    "os",
                    getOSForCloudProviderAndInstanceFamily(
                            CloudProvider.AZURE, AzureInstanceFamily.STANDARD_DSV2)
                        .name())
                .append("configServerDiskType", pDiskType.name()))
        .append(
            "readOnlySpecs",
            new BasicDBObject()
                .append("nodeCount", 0)
                .append("isConfigServerHardware", true)
                .append("configServerInstanceSize", AzureNDSInstanceSize.M30.name())
                .append("configServerInstanceFamily", AzureInstanceFamily.STANDARD_DSV2.name())
                .append(
                    "os",
                    getOSForCloudProviderAndInstanceFamily(
                            CloudProvider.AZURE, AzureInstanceFamily.STANDARD_DSV2)
                        .name())
                .append("configServerDiskType", pDiskType.name()))
        .append(
            "analyticsSpecs",
            new BasicDBObject()
                .append("nodeCount", 0)
                .append("isConfigServerHardware", true)
                .append("configServerInstanceSize", AzureNDSInstanceSize.M30.name())
                .append("configServerInstanceFamily", AzureInstanceFamily.STANDARD_DSV2.name())
                .append(
                    "os",
                    getOSForCloudProviderAndInstanceFamily(
                            CloudProvider.AZURE, AzureInstanceFamily.STANDARD_DSV2)
                        .name())
                .append("configServerDiskType", pDiskType.name()))
        .append(
            "hiddenSecondarySpecs",
            new BasicDBObject()
                .append("nodeCount", 0)
                .append("isConfigServerHardware", true)
                .append("configServerInstanceSize", AzureNDSInstanceSize.M30.name())
                .append("configServerInstanceFamily", AzureInstanceFamily.STANDARD_DSV2.name())
                .append(
                    "os",
                    getOSForCloudProviderAndInstanceFamily(
                            CloudProvider.AZURE, AzureInstanceFamily.STANDARD_DSV2)
                        .name())
                .append("configServerDiskType", pDiskType.name()));
  }

  public static BasicDBObject getGCPShardedClusterDescription() {
    return getGCPShardedClusterDescription(new ObjectId());
  }

  public static BasicDBObject getGCPShardedClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final int pNumShards) {
    return getGCPShardedClusterDescription(
        pGroupId,
        pClusterName,
        InstanceHostname.HostnameScheme.LEGACY,
        List.of(GCPNDSDefaults.REGION_NAME),
        pNumShards);
  }

  public static BasicDBObject getGCPShardedClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final InstanceHostname.HostnameScheme pHostnameScheme) {
    return getGCPShardedClusterDescription(
        pGroupId, pClusterName, pHostnameScheme, List.of(GCPNDSDefaults.REGION_NAME));
  }

  public static BasicDBObject getGCPShardedClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final InstanceHostname.HostnameScheme pHostnameScheme,
      final List<GCPRegionName> pGCPRegionNames) {
    return getGCPShardedClusterDescription(
        pGroupId, pClusterName, pHostnameScheme, pGCPRegionNames, 2);
  }

  public static BasicDBObject getGCPShardedClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final InstanceHostname.HostnameScheme pHostnameScheme,
      final List<GCPRegionName> pGCPRegionNames,
      final int pNumShards) {
    final BasicDBList regionConfigs = new BasicDBList();
    for (int i = 0; i < pGCPRegionNames.size(); i++) {
      final BasicDBObject regionConfig =
          new BasicDBObject()
              .append("cloudProvider", CloudProvider.GCP.name())
              .append("regionName", pGCPRegionNames.get(i).name())
              .append("priority", RegionConfig.MAX_PRIORITY - i)
              .append("autoScaling", getDefaultAutoScaling())
              .append("analyticsAutoScaling", getDefaultAutoScaling())
              .append(
                  "electableSpecs",
                  new BasicDBObject()
                      .append("nodeCount", i == 0 ? 3 : 2)
                      .append("instanceSize", GCPNDSInstanceSize.M30.name())
                      .append("instanceFamily", GCPInstanceFamily.N2.name())
                      .append(
                          "os",
                          getOSForCloudProviderAndInstanceFamily(
                                  CloudProvider.GCP, GCPInstanceFamily.N2)
                              .name()))
              .append(
                  "readOnlySpecs",
                  new BasicDBObject()
                      .append("nodeCount", 0)
                      .append("instanceSize", GCPNDSInstanceSize.M30.name())
                      .append("instanceFamily", GCPInstanceFamily.N2.name())
                      .append(
                          "os",
                          getOSForCloudProviderAndInstanceFamily(
                                  CloudProvider.GCP, GCPInstanceFamily.N2)
                              .name()))
              .append(
                  "analyticsSpecs",
                  new BasicDBObject()
                      .append("nodeCount", 0)
                      .append("instanceSize", GCPNDSInstanceSize.M30.name())
                      .append("instanceFamily", GCPInstanceFamily.N2.name())
                      .append(
                          "os",
                          getOSForCloudProviderAndInstanceFamily(
                                  CloudProvider.GCP, GCPInstanceFamily.N2)
                              .name()))
              .append(
                  "hiddenSecondarySpecs",
                  new BasicDBObject()
                      .append("nodeCount", 0)
                      .append("instanceSize", GCPNDSInstanceSize.M30.name())
                      .append("instanceFamily", GCPInstanceFamily.N2.name())
                      .append(
                          "os",
                          getOSForCloudProviderAndInstanceFamily(
                                  CloudProvider.GCP, GCPInstanceFamily.N2)
                              .name()));
      regionConfigs.add(regionConfig);
    }

    final BasicDBObject csRegionConfig =
        new BasicDBObject()
            .append("cloudProvider", CloudProvider.GCP.name())
            .append("regionName", pGCPRegionNames.get(0).name())
            .append("priority", RegionConfig.MAX_PRIORITY)
            .append("autoScaling", getDefaultAutoScaling())
            .append("analyticsAutoScaling", getDefaultAutoScaling())
            .append(
                "electableSpecs",
                new BasicDBObject()
                    .append("nodeCount", 3)
                    .append("isConfigServerHardware", true)
                    .append("configServerInstanceSize", GCPNDSInstanceSize.M30.name())
                    .append("configServerInstanceFamily", GCPInstanceFamily.N2.name())
                    .append(
                        "os",
                        getOSForCloudProviderAndInstanceFamily(
                                CloudProvider.GCP, GCPInstanceFamily.N2)
                            .name()))
            .append(
                "readOnlySpecs",
                new BasicDBObject()
                    .append("nodeCount", 0)
                    .append("isConfigServerHardware", true)
                    .append("configServerInstanceSize", GCPNDSInstanceSize.M30.name())
                    .append("configServerInstanceFamily", GCPInstanceFamily.N2.name())
                    .append(
                        "os",
                        getOSForCloudProviderAndInstanceFamily(
                                CloudProvider.GCP, GCPInstanceFamily.N2)
                            .name()))
            .append(
                "analyticsSpecs",
                new BasicDBObject()
                    .append("nodeCount", 0)
                    .append("isConfigServerHardware", true)
                    .append("configServerInstanceSize", GCPNDSInstanceSize.M30.name())
                    .append("configServerInstanceFamily", GCPInstanceFamily.N2.name())
                    .append(
                        "os",
                        getOSForCloudProviderAndInstanceFamily(
                                CloudProvider.GCP, GCPInstanceFamily.N2)
                            .name()))
            .append(
                "hiddenSecondarySpecs",
                new BasicDBObject()
                    .append("nodeCount", 0)
                    .append("isConfigServerHardware", true)
                    .append("configServerInstanceSize", GCPNDSInstanceSize.M30.name())
                    .append("configServerInstanceFamily", GCPInstanceFamily.N2.name())
                    .append(
                        "os",
                        getOSForCloudProviderAndInstanceFamily(
                                CloudProvider.GCP, GCPInstanceFamily.N2)
                            .name()));

    final ObjectId zoneId = new ObjectId();
    final String zoneName = NDSDefaults.ZONE_NAME;

    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append("regionConfigs", regionConfigs)
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", zoneId)
            .append("zoneName", zoneName)
            .append("numShards", pNumShards);

    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec);

    final BasicDBList csRegionConfigs = new BasicDBList();
    csRegionConfigs.add(csRegionConfig);

    final BasicDBObject csReplicationSpec =
        new BasicDBObject()
            .append("regionConfigs", csRegionConfigs)
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", zoneId)
            .append("zoneName", zoneName)
            .append("numShards", 1);

    return getShardedClusterDescription(pGroupId, pClusterName, pHostnameScheme)
        .append("clusterType", ClusterType.SHARDED.name())
        .append("hostnameSubdomainLevel", SubdomainLevel.GCP.name())
        .append("replicationSpecList", replicationSpecList)
        .append("configServerReplicationSpec", csReplicationSpec)
        .append("configDiskSizeGB", 25.0);
  }

  public static BasicDBObject getGCPShardedClusterDescription(final ObjectId pGroupId) {
    return getGCPShardedClusterDescription(
        pGroupId, DEFAULT_CLUSTER_NAME, InstanceHostname.HostnameScheme.LEGACY);
  }

  public static BasicDBObject getAWSShardedClusterDescription(
      final String pClusterName, final List<RegionConfig> pRegionConfigs) {
    return getAWSShardedClusterDescription(
        new ObjectId(), pClusterName, pRegionConfigs, InstanceHostname.HostnameScheme.LEGACY);
  }

  public static BasicDBObject getAWSShardedClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final List<RegionConfig> pRegionConfigs) {
    return getAWSShardedClusterDescription(
        pGroupId, pClusterName, pRegionConfigs, InstanceHostname.HostnameScheme.LEGACY);
  }

  public static BasicDBObject getAWSShardedClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<RegionConfig> pRegionConfigs,
      final InstanceHostname.HostnameScheme pHostnameScheme) {
    final ObjectId zoneId = new ObjectId();
    final String zoneName = NDSDefaults.ZONE_NAME;
    final BasicDBObject replicationSpec =
        new BasicDBObject()
            .append(
                "regionConfigs",
                pRegionConfigs.stream()
                    .map(
                        rc ->
                            rc.copy()
                                .updateAllHardware(
                                    new AWSHardwareSpec.Builder()
                                        .setInstanceSize(AWSNDSInstanceSize.M30)
                                        .setInstanceFamilyAndOS(
                                            AWSInstanceFamily.M4,
                                            getOSForCloudProviderAndInstanceFamily(
                                                CloudProvider.AWS, AWSInstanceFamily.M4)))
                                .build())
                    .map(RegionConfig::toDBObject)
                    .collect(DbUtils.toBasicDBList()))
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", zoneId)
            .append("zoneName", zoneName)
            .append("numShards", 2);
    final BasicDBList replicationSpecList = new BasicDBList();
    replicationSpecList.add(replicationSpec);

    final BasicDBObject doc =
        getShardedClusterDescription(pGroupId, pClusterName, pHostnameScheme)
            .append("clusterType", ClusterDescription.ClusterType.SHARDED.name())
            .append("hostnameSubdomainLevel", InstanceHostname.SubdomainLevel.MONGODB.name())
            .append("replicationSpecList", replicationSpecList);

    final BasicDBObject configReplicationSpec =
        new BasicDBObject()
            .append(
                "regionConfigs",
                pRegionConfigs.stream()
                    .map(
                        rc ->
                            rc.copy()
                                .updateAllHardware(
                                    new AWSHardwareSpec.Builder()
                                        .setDiskIOPS(
                                            AWSNDSInstanceSize.M10.getGP3StandardEBSIOPS(
                                                doc.getDouble(
                                                    ShardedClusterDescription.FieldDefs
                                                        .CONFIG_DISK_SIZE_GB)))
                                        .setEncryptEBSVolume(true)
                                        .setEBSVolumeType(VolumeType.Gp3)
                                        .setIsDedicatedConfigServerHardware(true)
                                        .setInstanceSize(AWSNDSInstanceSize.M10)
                                        .setInstanceFamilyAndOS(
                                            AWSInstanceFamily.T2,
                                            getOSForCloudProviderAndInstanceFamily(
                                                CloudProvider.AWS, AWSInstanceFamily.T2)))
                                .build()
                                .toDBObject())
                    .collect(DbUtils.toBasicDBList()))
            .append("id", new ObjectId())
            .append("externalId", new ObjectId())
            .append("zoneId", zoneId)
            .append("zoneName", zoneName)
            .append("numShards", 1)
            .append(ConfigServerReplicationSpec.FieldDefs.TYPE, ConfigServerType.DEDICATED.name());

    doc.append("configServerReplicationSpec", configReplicationSpec);
    return doc;
  }

  public static BasicDBObject getAWSShardedClusterDescriptionWithEmbeddedConfigServer(
      final ObjectId pGroupId, final boolean pPinned) {
    return getAWSShardedClusterDescriptionWithEmbeddedConfigServer(
        pGroupId, DEFAULT_CLUSTER_NAME, pPinned);
  }

  public static BasicDBObject getAWSShardedClusterDescriptionWithEmbeddedConfigServer(
      final ObjectId pGroupId, final String pClusterName, final boolean pPinned) {
    final ShardedClusterDescription cluster =
        new ShardedClusterDescription(getAWSShardedClusterDescription(pGroupId, pClusterName));
    return cluster
        .copy()
        .setEmbeddedConfigServerReplicationSpec(
            cluster.getReplicationSpecsWithShardData().get(0), pPinned)
        .setMongoDBVersion(VersionUtils.EIGHT_ZERO_ZERO.getVersion())
        .setMongoDBMajorVersion(VersionUtils.EIGHT_ZERO_ZERO.getMajorVersionString())
        .setVersionReleaseSystem(VersionReleaseSystem.LTS)
        .build()
        .toDBObject();
  }

  public static BasicDBObject getAWSShardedClusterDescriptionWithEmbeddedConfigServer(
      final ObjectId pGroupId,
      final String pClusterName,
      final boolean pPinned,
      final List<RegionConfig> pRegionConfigs) {
    final ShardedClusterDescription cluster =
        new ShardedClusterDescription(
            getAWSShardedClusterDescription(pGroupId, pClusterName, pRegionConfigs));
    return cluster
        .copy()
        .setEmbeddedConfigServerReplicationSpec(
            cluster.getReplicationSpecsWithShardData().get(0), pPinned)
        .setMongoDBVersion(VersionUtils.EIGHT_ZERO_ZERO.getVersion())
        .setMongoDBMajorVersion(VersionUtils.EIGHT_ZERO_ZERO.getMajorVersionString())
        .setVersionReleaseSystem(VersionReleaseSystem.LTS)
        .build()
        .toDBObject();
  }

  public static BasicDBObject getAWSShardedClusterDescriptionWithEmbeddedConfigServer() {
    return getAWSShardedClusterDescriptionWithEmbeddedConfigServer(new ObjectId(), false);
  }

  public static BasicDBObject getAWSShardedClusterDescription(final ObjectId pGroupId) {
    return getAWSShardedClusterDescription(pGroupId, DEFAULT_CLUSTER_NAME);
  }

  public static BasicDBObject getAWSShardedClusterDescription(
      final ObjectId pGroupId, final String pClusterName, final int pShardCount) {
    final BasicDBObject cluster = getAWSShardedClusterDescription(pGroupId, pClusterName);
    ((BasicDBObject) ((BasicDBList) cluster.get(FieldDefs.REPLICATION_SPEC_LIST)).get(0))
        .append("numShards", pShardCount);
    return cluster;
  }

  public static BasicDBObject getAWSShardedClusterDescription(
      final ObjectId pGroupId, final String pClusterName) {
    return getAWSShardedClusterDescription(
        pGroupId,
        pClusterName,
        List.of(
            NDSModelTestFactory.getShardRegionConfigForRegion(
                AWSNDSDefaults.REGION_NAME, RegionConfig.MAX_PRIORITY, 3, 0, 0, 0)),
        InstanceHostname.HostnameScheme.LEGACY);
  }

  public static BasicDBObject getAsymmetricShardedClusterDescriptionWithAsymmetricIOPS(
      final ObjectId pGroupId, final String pClusterName) {
    // generate configuration: AWS sharded cluster with 80 diskSizeGB
    // 1 shard: M30, IO2 disk, 2000 provisioned IOPS
    // 1 shard: M40, IO2 disk, 3000 provisioned IOPS

    final BasicDBObject clusterDescriptionDbObj =
        getShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL);
    final ObjectId m30ReplicationSpecId = new ObjectId();
    final ObjectId m40ReplicationSpecId = new ObjectId();

    // one replication spec with default size, M30
    final ReplicationSpec originalReplicationSpec =
        getAWSReplicationSpec(new ObjectId(), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1));
    final AWSHardwareSpec originalHardwareSpec =
        (AWSHardwareSpec) originalReplicationSpec.getRegionConfigs().get(0).getElectableSpecs();

    // M30, IO2 disk, 2000 provisioned IOPS
    final HardwareSpec m30ElectableSpec =
        originalHardwareSpec
            .copy()
            .setEBSVolumeType(VolumeType.Io2)
            .setDiskIOPS(2000)
            .setInstanceSize(AWSNDSInstanceSize.M30)
            .setInstanceFamilyAndOS(AWSInstanceFamily.M6G, OS.AL2)
            .build();

    final HardwareSpec m30ReadOnlySpec = m30ElectableSpec.copy().setNodeCount(0).build();
    final HardwareSpec m30AnalyticsSpec = m30ElectableSpec.copy().setNodeCount(0).build();

    final ShardRegionConfig.Builder m30RegionConfigBuilder =
        (ShardRegionConfig.Builder) originalReplicationSpec.getRegionConfigs().get(0).copy();

    final RegionConfig m30RegionConfig =
        m30RegionConfigBuilder
            .setReadOnlySpecs(m30ReadOnlySpec)
            .setAnalyticsSpecs(m30AnalyticsSpec)
            .setElectableSpecs(m30ElectableSpec)
            .build();
    final ReplicationSpec m30ReplicationSpec =
        originalReplicationSpec
            .copy()
            .setRegionConfigs(List.of(m30RegionConfig))
            .setReplicationSpecId(m30ReplicationSpecId)
            .build();

    // M40, IO2 disk, 3000 provisioned IOPS
    final HardwareSpec m40ElectableSpec =
        originalHardwareSpec
            .copy()
            .setEBSVolumeType(VolumeType.Io2)
            .setDiskIOPS(3000)
            .setInstanceSize(AWSNDSInstanceSize.M40)
            .setInstanceFamilyAndOS(AWSInstanceFamily.M6G, OS.AL2)
            .build();

    final HardwareSpec m40ReadOnlySpec = m40ElectableSpec.copy().setNodeCount(0).build();
    final HardwareSpec m40AnalyticsSpec = m40ElectableSpec.copy().setNodeCount(0).build();

    final ShardRegionConfig.Builder m40RegionConfigBuilder =
        (ShardRegionConfig.Builder) originalReplicationSpec.getRegionConfigs().get(0).copy();

    final RegionConfig m40RegionConfig =
        m40RegionConfigBuilder
            .setReadOnlySpecs(m40ReadOnlySpec)
            .setAnalyticsSpecs(m40AnalyticsSpec)
            .setElectableSpecs(m40ElectableSpec)
            .build();
    final ReplicationSpec m40ReplicationSpec =
        originalReplicationSpec
            .copy()
            .setRegionConfigs(List.of(m40RegionConfig))
            .setReplicationSpecId(m40ReplicationSpecId)
            .build();

    // embedded config server on M40
    final EmbeddedConfigServerReplicationSpec embeddedConfigServerReplicationSpec =
        new EmbeddedConfigServerReplicationSpec(m40ReplicationSpec.getId(), false);

    final BasicDBList replicationSpecListDbObj = new BasicDBList();
    replicationSpecListDbObj.add(m30ReplicationSpec.toDBObject());
    replicationSpecListDbObj.add(m40ReplicationSpec.toDBObject());

    // use 80GB disk
    clusterDescriptionDbObj.append(FieldDefs.DISK_SIZE_GB, 80.0);
    clusterDescriptionDbObj.append(FieldDefs.REPLICATION_SPEC_LIST, replicationSpecListDbObj);
    clusterDescriptionDbObj.append(
        ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
        embeddedConfigServerReplicationSpec.toDBObject());
    clusterDescriptionDbObj.append(FieldDefs.BACKUP_ENABLED, false);

    return clusterDescriptionDbObj;
  }

  public static BasicDBObject getAsymmetricGeoShardedClusterDescriptionWithInstanceSizes(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<List<NDSInstanceSize>> pShardInstanceSizes,
      final boolean pComputeAutoScalingEnabled) {
    final BasicDBObject clusterDescriptionDbObj =
        getShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL);
    final BasicDBList replicationSpecListDbObj =
        getGeoShardedAsymmetricReplicationSpecList(pShardInstanceSizes, pComputeAutoScalingEnabled);

    // dedicated config server
    final DedicatedConfigServerReplicationSpec config =
        new DedicatedConfigServerReplicationSpec(
            NDSClusterSvc.calculateDedicatedConfigServerReplicationSpec(
                List.of(
                    getAWSReplicationSpec(
                        new ObjectId(), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1))),
                10.0,
                null,
                TEST_MONGODB_VERSION,
                false,
                false,
                false,
                InstanceFamilyFlags.withAllFlagsFalse()),
            false);

    clusterDescriptionDbObj.append(FieldDefs.REPLICATION_SPEC_LIST, replicationSpecListDbObj);
    clusterDescriptionDbObj
        .append(
            ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC, config.toDBObject())
        .append(ShardedClusterDescription.FieldDefs.CONFIG_DISK_SIZE_GB, 10.0);
    clusterDescriptionDbObj.append(FieldDefs.BACKUP_ENABLED, false);
    clusterDescriptionDbObj.append(FieldDefs.AUTO_SCALING_MODE, AutoScalingMode.SHARD);

    return clusterDescriptionDbObj;
  }

  public static BasicDBObject getAsymmetricShardedClusterDescriptionWithInstanceSizes(
      final ObjectId pGroupId,
      final String pClusterName,
      final List<NDSInstanceSize> pShardInstanceSizes,
      final boolean pComputeAutoScalingEnabled) {
    final BasicDBObject clusterDescriptionDbObj =
        getShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL);
    final BasicDBList replicationSpecListDbObj =
        getGeoShardedAsymmetricReplicationSpecList(
            List.of(pShardInstanceSizes), pComputeAutoScalingEnabled);

    // dedicated config server
    final DedicatedConfigServerReplicationSpec config =
        new DedicatedConfigServerReplicationSpec(
            NDSClusterSvc.calculateDedicatedConfigServerReplicationSpec(
                List.of(
                    getAWSReplicationSpec(
                        new ObjectId(), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1))),
                10.0,
                null,
                TEST_MONGODB_VERSION,
                false,
                false,
                false,
                InstanceFamilyFlags.withAllFlagsFalse()),
            false);

    clusterDescriptionDbObj.append(FieldDefs.REPLICATION_SPEC_LIST, replicationSpecListDbObj);
    clusterDescriptionDbObj
        .append(
            ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC, config.toDBObject())
        .append(ShardedClusterDescription.FieldDefs.CONFIG_DISK_SIZE_GB, 10.0);
    clusterDescriptionDbObj.append(FieldDefs.BACKUP_ENABLED, false);
    clusterDescriptionDbObj.append(FieldDefs.AUTO_SCALING_MODE, AutoScalingMode.SHARD);

    return clusterDescriptionDbObj;
  }

  private static BasicDBList getGeoShardedAsymmetricReplicationSpecList(
      List<List<NDSInstanceSize>> pZoneInstanceSizes, boolean pComputeAutoScalingEnabled) {
    final BasicDBList replicationSpecListDbObj = new BasicDBList();
    final Queue<AWSRegionName> awsRegions =
        new LinkedList<>(
            List.of(AWSRegionName.US_EAST_1, AWSRegionName.US_WEST_2, AWSRegionName.EU_WEST_1));
    final Queue<AzureRegionName> azureRegions =
        new LinkedList<>(
            List.of(
                AzureRegionName.US_EAST_2,
                AzureRegionName.US_WEST_2,
                AzureRegionName.EUROPE_NORTH));
    final Queue<GCPRegionName> gcpRegions =
        new LinkedList<>(
            List.of(
                GCPRegionName.US_EAST_4, GCPRegionName.US_WEST_2, GCPRegionName.WESTERN_EUROPE));

    int zoneNumCounter = 1;
    for (List<NDSInstanceSize> shardInstanceSizes : pZoneInstanceSizes) {
      final Set<CloudProvider> shardProviders =
          shardInstanceSizes.stream()
              .map(InstanceSize::getCloudProvider)
              .collect(Collectors.toSet());
      final ObjectId zoneId = new ObjectId();
      final String zoneName = "Zone " + zoneNumCounter++;
      final Optional<AWSRegionName> zoneAwsRegion =
          shardProviders.contains(CloudProvider.AWS)
              ? Optional.of(awsRegions.remove())
              : Optional.empty();
      final Optional<AzureRegionName> zoneAzureRegion =
          shardProviders.contains(CloudProvider.AZURE)
              ? Optional.of(azureRegions.remove())
              : Optional.empty();
      final Optional<GCPRegionName> zoneGCPRegion =
          shardProviders.contains(CloudProvider.GCP)
              ? Optional.of(gcpRegions.remove())
              : Optional.empty();

      for (NDSInstanceSize shardInstanceSize : shardInstanceSizes) {
        final ObjectId replicationSpecId = new ObjectId();
        final ReplicationSpec baseReplicationSpec =
            switch (shardInstanceSize.getCloudProvider()) {
              case AWS ->
                  getAWSReplicationSpec(
                      replicationSpecId,
                      zoneId,
                      zoneName,
                      1,
                      3,
                      List.of(zoneAwsRegion.orElseThrow()));
              case AZURE ->
                  getAzureReplicationSpec(
                      replicationSpecId,
                      zoneId,
                      zoneName,
                      1,
                      3,
                      List.of(zoneAzureRegion.orElseThrow()));
              case GCP ->
                  getGCPReplicationSpec(
                      replicationSpecId,
                      zoneId,
                      zoneName,
                      1,
                      3,
                      List.of(zoneGCPRegion.orElseThrow()));
              default -> throw new IllegalArgumentException("Unsupported provider");
            };
        final AutoScaling autoScaling =
            switch (shardInstanceSize.getCloudProvider()) {
              case AWS ->
                  pComputeAutoScalingEnabled
                      ? new AWSAutoScaling(getAutoScalingEnabled())
                      : new AWSAutoScaling(getDefaultAutoScaling());
              case AZURE ->
                  pComputeAutoScalingEnabled
                      ? new AzureAutoScaling(getAutoScalingEnabled())
                      : new AzureAutoScaling(getDefaultAutoScaling());
              case GCP ->
                  pComputeAutoScalingEnabled
                      ? new GCPAutoScaling(getAutoScalingEnabled())
                      : new GCPAutoScaling(getDefaultAutoScaling());
              default -> throw new IllegalArgumentException("Unsupported provider");
            };

        final HardwareSpec.Builder builder =
            switch (shardInstanceSize.getCloudProvider()) {
              case AWS -> new AWSHardwareSpec.Builder();
              case AZURE -> new AzureHardwareSpec.Builder();
              case GCP -> new GCPHardwareSpec.Builder();
              default -> throw new IllegalArgumentException("unsupported provider");
            };

        final RegionConfig originalRegionConfig = baseReplicationSpec.getRegionConfigs().get(0);
        final ShardRegionConfig.Builder regionConfigBuilder =
            (ShardRegionConfig.Builder) originalRegionConfig.copy();

        final List<InstanceFamily> availableFamilies =
            shardInstanceSize.getAvailableFamilies().get(originalRegionConfig.getRegionName());
        final RegionConfig regionConfig =
            regionConfigBuilder
                .updateHardware(
                    builder
                        .setInstanceSize(shardInstanceSize)
                        .setInstanceFamilyAndOS(
                            availableFamilies.get(availableFamilies.size() - 1), OS.AL2),
                    NodeTypeFamily.BASE)
                .setAutoScaling(autoScaling, NodeTypeFamily.BASE)
                .build();

        final ReplicationSpec customReplicationSpec =
            baseReplicationSpec
                .copy()
                .setRegionConfigs(List.of(regionConfig))
                .setReplicationSpecId(replicationSpecId)
                .build();

        replicationSpecListDbObj.add(customReplicationSpec.toDBObject());
      }
    }
    return replicationSpecListDbObj;
  }

  public static BasicDBObject
      getInvalidAsymmetricShardedClusterDescription_inconsistentAnalyticsSpecs(
          final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject clusterDescriptionDbObj =
        getShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL);
    final ObjectId m30ReplicationSpecId = new ObjectId();
    final ObjectId replicationSpecId = new ObjectId();

    // one replication spec with default size, M30
    final ReplicationSpec m30ReplicationSpec =
        getAWSReplicationSpec(
                new ObjectId(),
                "Zone 1",
                1,
                3,
                List.of(AWSRegionName.US_EAST_1, AWSRegionName.US_EAST_2))
            .copy()
            .setReplicationSpecId(m30ReplicationSpecId)
            .build();

    // one region has analytics nodes with a different instance size than the analytics nodes on the
    // other region
    final HardwareSpec m60AnalyticsSpec =
        m30ReplicationSpec
            .getRegionConfigs()
            .get(0)
            .getAnalyticsSpecs()
            .copy()
            .setInstanceSize(AWSNDSInstanceSize.M60)
            .setInstanceFamilyAndOS(AWSInstanceFamily.M4, OS.AL2)
            .build();

    final ShardRegionConfig.Builder asymmetricRegionConfigBuilder =
        (ShardRegionConfig.Builder) m30ReplicationSpec.getRegionConfigs().get(0).copy();

    final RegionConfig asymmetricRegionConfig =
        asymmetricRegionConfigBuilder.setAnalyticsSpecs(m60AnalyticsSpec).build();

    final ReplicationSpec m20ReplicationSpec =
        m30ReplicationSpec
            .copy()
            .setRegionConfigs(
                List.of(asymmetricRegionConfig, m30ReplicationSpec.getRegionConfigs().get(1)))
            .setReplicationSpecId(replicationSpecId)
            .build();

    // embedded config server on first, larger shard
    final EmbeddedConfigServerReplicationSpec embeddedConfigServerReplicationSpec =
        new EmbeddedConfigServerReplicationSpec(m30ReplicationSpec.getId(), false);

    final BasicDBList replicationSpecListDbObj = new BasicDBList();
    replicationSpecListDbObj.add(m30ReplicationSpec.toDBObject());
    replicationSpecListDbObj.add(m20ReplicationSpec.toDBObject());

    clusterDescriptionDbObj.append(FieldDefs.REPLICATION_SPEC_LIST, replicationSpecListDbObj);
    clusterDescriptionDbObj.append(
        ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
        embeddedConfigServerReplicationSpec.toDBObject());
    clusterDescriptionDbObj.append(FieldDefs.BACKUP_ENABLED, false);

    return clusterDescriptionDbObj;
  }

  public static BasicDBObject
      getInvalidAsymmetricShardedClusterDescription_inconsistentReadOnlySpecs(
          final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject clusterDescriptionDbObj =
        getShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL);
    final ObjectId m30ReplicationSpecId = new ObjectId();
    final ObjectId m20ReplicationSpecId = new ObjectId();

    // one replication spec with default size, M30
    final ReplicationSpec m30ReplicationSpec =
        getAWSReplicationSpec(new ObjectId(), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1))
            .copy()
            .setReplicationSpecId(m30ReplicationSpecId)
            .build();

    // one replication spec with alternate size, M20
    final HardwareSpec m20ElectableSpec =
        m30ReplicationSpec
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs()
            .copy()
            .setInstanceSize(AWSNDSInstanceSize.M20)
            .setInstanceFamilyAndOS(AWSInstanceFamily.T4G, OS.AL2)
            .build();

    // but its read-only specs have a different instance size - invalid!
    final HardwareSpec m20ReadOnlySpec =
        m20ElectableSpec
            .copy()
            .setNodeCount(1)
            .setInstanceSize(AWSNDSInstanceSize.M30)
            .setInstanceFamilyAndOS(AWSInstanceFamily.M6G, OS.AL2)
            .build();

    final ShardRegionConfig.Builder m20RegionConfigBuilder =
        (ShardRegionConfig.Builder) m30ReplicationSpec.getRegionConfigs().get(0).copy();

    final RegionConfig m20RegionConfig =
        m20RegionConfigBuilder
            .setReadOnlySpecs(m20ReadOnlySpec)
            .setElectableSpecs(m20ElectableSpec)
            .build();

    final ReplicationSpec m20ReplicationSpec =
        m30ReplicationSpec
            .copy()
            .setRegionConfigs(List.of(m20RegionConfig))
            .setReplicationSpecId(m20ReplicationSpecId)
            .build();

    // embedded config server on first, larger shard
    final EmbeddedConfigServerReplicationSpec embeddedConfigServerReplicationSpec =
        new EmbeddedConfigServerReplicationSpec(m30ReplicationSpec.getId(), false);

    final BasicDBList replicationSpecListDbObj = new BasicDBList();
    replicationSpecListDbObj.add(m30ReplicationSpec.toDBObject());
    replicationSpecListDbObj.add(m20ReplicationSpec.toDBObject());

    clusterDescriptionDbObj.append(FieldDefs.REPLICATION_SPEC_LIST, replicationSpecListDbObj);
    clusterDescriptionDbObj.append(
        ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
        embeddedConfigServerReplicationSpec.toDBObject());
    clusterDescriptionDbObj.append(FieldDefs.BACKUP_ENABLED, false);

    return clusterDescriptionDbObj;
  }

  public static BasicDBObject getAsymmetricShardedClusterDescriptionWithEmbeddedConfigServer(
      final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject clusterDescriptionDbObj =
        getShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL);
    final ObjectId m30ReplicationSpecId = new ObjectId();
    final ObjectId m30ExternalId = new ObjectId();
    final ObjectId m20ReplicationSpecId = new ObjectId();
    final ObjectId m20ExternalId = new ObjectId();

    // one replication spec with default size, M30
    final ReplicationSpec m30ReplicationSpec =
        getAWSReplicationSpec(new ObjectId(), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1))
            .copy()
            .setReplicationSpecId(m30ReplicationSpecId)
            .setExternalId(m30ExternalId)
            .build();

    // one replication spec with alternate size, M20
    final HardwareSpec m20ElectableSpec =
        m30ReplicationSpec
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs()
            .copy()
            .setInstanceSize(AWSNDSInstanceSize.M20)
            .setInstanceFamilyAndOS(AWSInstanceFamily.T4G, OS.AL2023)
            .build();
    final HardwareSpec m20ReadOnlySpec = m20ElectableSpec.copy().setNodeCount(0).build();

    final ShardRegionConfig.Builder m20RegionConfigBuilder =
        (ShardRegionConfig.Builder) m30ReplicationSpec.getRegionConfigs().get(0).copy();

    final RegionConfig m20RegionConfig =
        m20RegionConfigBuilder
            .setReadOnlySpecs(m20ReadOnlySpec)
            .setElectableSpecs(m20ElectableSpec)
            .build();

    final ReplicationSpec m20ReplicationSpec =
        m30ReplicationSpec
            .copy()
            .setRegionConfigs(List.of(m20RegionConfig))
            .setReplicationSpecId(m20ReplicationSpecId)
            .setExternalId(m20ExternalId)
            .build();

    // embedded config server on first, larger shard
    final EmbeddedConfigServerReplicationSpec embeddedConfigServerReplicationSpec =
        new EmbeddedConfigServerReplicationSpec(m30ReplicationSpec.getId(), false);

    final BasicDBList replicationSpecListDbObj = new BasicDBList();
    replicationSpecListDbObj.add(m30ReplicationSpec.toDBObject());
    replicationSpecListDbObj.add(m20ReplicationSpec.toDBObject());

    clusterDescriptionDbObj.append(FieldDefs.REPLICATION_SPEC_LIST, replicationSpecListDbObj);
    clusterDescriptionDbObj.append(
        ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC,
        embeddedConfigServerReplicationSpec.toDBObject());
    clusterDescriptionDbObj.append(FieldDefs.BACKUP_ENABLED, false);

    return clusterDescriptionDbObj;
  }

  public static BasicDBObject getAsymmetricShardedClusterDescriptionWithDedicatedConfigServer(
      final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject clusterDescriptionDbObj =
        getShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL);
    final ObjectId m30ReplicationSpecId = new ObjectId();
    final ObjectId m20ReplicationSpecId = new ObjectId();

    // one replication spec with default size, M30
    final ReplicationSpec m30ReplicationSpec =
        getAWSReplicationSpec(new ObjectId(), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1))
            .copy()
            .setReplicationSpecId(m30ReplicationSpecId)
            .build();

    // one replication spec with alternate size, M20
    final HardwareSpec m20ElectableSpec =
        m30ReplicationSpec
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs()
            .copy()
            .setInstanceSize(AWSNDSInstanceSize.M20)
            .setInstanceFamilyAndOS(AWSInstanceFamily.T4G, OS.AL2)
            .build();
    final HardwareSpec m20ReadOnlySpec = m20ElectableSpec.copy().setNodeCount(0).build();

    final ShardRegionConfig.Builder m20RegionConfigBuilder =
        (ShardRegionConfig.Builder) m30ReplicationSpec.getRegionConfigs().get(0).copy();

    final RegionConfig m20RegionConfig =
        m20RegionConfigBuilder
            .setReadOnlySpecs(m20ReadOnlySpec)
            .setElectableSpecs(m20ElectableSpec)
            .build();

    final ReplicationSpec m20ReplicationSpec =
        m30ReplicationSpec
            .copy()
            .setRegionConfigs(List.of(m20RegionConfig))
            .setReplicationSpecId(m20ReplicationSpecId)
            .build();

    // dedicated config server
    final DedicatedConfigServerReplicationSpec config =
        new DedicatedConfigServerReplicationSpec(
            NDSClusterSvc.calculateDedicatedConfigServerReplicationSpec(
                List.of(m30ReplicationSpec, m20ReplicationSpec),
                10.0,
                null,
                TEST_MONGODB_VERSION,
                false,
                false,
                false,
                InstanceFamilyFlags.withAllFlagsFalse()),
            false);

    final BasicDBList replicationSpecListDbObj = new BasicDBList();
    replicationSpecListDbObj.add(m30ReplicationSpec.toDBObject());
    replicationSpecListDbObj.add(m20ReplicationSpec.toDBObject());

    clusterDescriptionDbObj.append(FieldDefs.REPLICATION_SPEC_LIST, replicationSpecListDbObj);
    clusterDescriptionDbObj
        .append(
            ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC, config.toDBObject())
        .append(ShardedClusterDescription.FieldDefs.CONFIG_DISK_SIZE_GB, 10.0);
    clusterDescriptionDbObj.append(FieldDefs.BACKUP_ENABLED, false);
    clusterDescriptionDbObj.append(FieldDefs.AUTO_SCALING_MODE, AutoScalingMode.SHARD);

    return clusterDescriptionDbObj;
  }

  public static BasicDBObject getAsymmetricGeoShardedClusterDescriptionWithDedicatedConfigServer(
      final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject clusterDescriptionDbObj =
        getShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL);
    final ObjectId m30ReplicationSpecId = new ObjectId();
    final ObjectId m20ReplicationSpecId = new ObjectId();

    // one replication spec with default size, M30
    final ReplicationSpec m30ReplicationSpec =
        getAWSReplicationSpec(
            m30ReplicationSpecId, oid(1), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1));

    // one replication spec with alternate size, M20
    final HardwareSpec m20ElectableSpec =
        m30ReplicationSpec
            .getRegionConfigs()
            .get(0)
            .getElectableSpecs()
            .copy()
            .setInstanceSize(AWSNDSInstanceSize.M20)
            .setInstanceFamilyAndOS(AWSInstanceFamily.T4G, OS.AL2)
            .build();
    final HardwareSpec m20ReadOnlySpec = m20ElectableSpec.copy().setNodeCount(0).build();

    final ShardRegionConfig.Builder m20RegionConfigBuilder =
        (ShardRegionConfig.Builder) m30ReplicationSpec.getRegionConfigs().get(0).copy();

    final RegionConfig m20RegionConfig =
        m20RegionConfigBuilder
            .setReadOnlySpecs(m20ReadOnlySpec)
            .setElectableSpecs(m20ElectableSpec)
            .build();

    final ReplicationSpec m20ReplicationSpec =
        getAWSReplicationSpec(
                m20ReplicationSpecId, oid(2), "Zone 2", 1, 3, List.of(AWSRegionName.US_EAST_1))
            .copy()
            .setRegionConfigs(List.of(m20RegionConfig))
            .build();

    // dedicated config server
    final DedicatedConfigServerReplicationSpec config =
        new DedicatedConfigServerReplicationSpec(
            NDSClusterSvc.calculateDedicatedConfigServerReplicationSpec(
                List.of(m30ReplicationSpec, m20ReplicationSpec),
                10.0,
                null,
                TEST_MONGODB_VERSION,
                false,
                false,
                false,
                InstanceFamilyFlags.withAllFlagsFalse()),
            false);

    final BasicDBList replicationSpecListDbObj = new BasicDBList();
    replicationSpecListDbObj.add(m30ReplicationSpec.toDBObject());
    replicationSpecListDbObj.add(m20ReplicationSpec.toDBObject());

    clusterDescriptionDbObj.append(FieldDefs.REPLICATION_SPEC_LIST, replicationSpecListDbObj);
    clusterDescriptionDbObj
        .append(
            ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC, config.toDBObject())
        .append(ShardedClusterDescription.FieldDefs.CONFIG_DISK_SIZE_GB, 10.0);
    clusterDescriptionDbObj.append(FieldDefs.BACKUP_ENABLED, false);

    return clusterDescriptionDbObj;
  }

  public static BasicDBObject getSymmetricShardedClusterDescriptionWithSplitReplicationSpecs(
      final ObjectId pGroupId, final String pClusterName) {
    return getSymmetricShardedClusterDescriptionWithSplitReplicationSpecs(
        pGroupId, pClusterName, 2);
  }

  public static BasicDBObject getSymmetricShardedClusterDescriptionWithSplitReplicationSpecs(
      final ObjectId pGroupId, final String pClusterName, final int pNumShards) {
    final BasicDBObject clusterDescriptionDbObj =
        getShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL);

    final ReplicationSpec spec =
        getAWSReplicationSpec(new ObjectId(), "Zone 1", 1, 3, List.of(AWSRegionName.US_EAST_1));

    final BasicDBList replicationSpecListDbObj =
        IntStream.range(0, pNumShards)
            .mapToObj(
                i ->
                    spec.copy()
                        .setReplicationSpecId(new ObjectId())
                        .setExternalId(new ObjectId())
                        .build()
                        .toDBObject())
            .collect(DbUtils.toBasicDBList());
    clusterDescriptionDbObj.append(FieldDefs.REPLICATION_SPEC_LIST, replicationSpecListDbObj);

    final DedicatedConfigServerReplicationSpec config =
        new DedicatedConfigServerReplicationSpec(
            spec.copy().setReplicationSpecId(new ObjectId()).build(), false);
    clusterDescriptionDbObj.append(
        ShardedClusterDescription.FieldDefs.CONFIG_SERVER_REPLICATION_SPEC, config.toDBObject());

    clusterDescriptionDbObj.append(FieldDefs.BACKUP_ENABLED, false);

    return clusterDescriptionDbObj;
  }

  public static BasicDBObject getShardedClusterDescription(
      final ObjectId pGroupId,
      final String pClusterName,
      final InstanceHostname.HostnameScheme pHostnameScheme) {
    final BasicDBList hosts = new BasicDBList();
    hosts.add("a");
    hosts.add("b");

    final BasicDBList privateHostsDBList = new BasicDBList();
    privateHostsDBList.add("a-pri");
    privateHostsDBList.add("b-pri");

    final BasicDBObject id =
        new BasicDBObject().append("name", pClusterName).append("groupId", pGroupId);

    final BasicDBObject defaultBiConnectorDoc = getDefaultBiConnector();
    final BasicDBObject defaultGeoShardingDoc = getDefaultGeoSharding();

    return new BasicDBObject()
        .append("_id", id)
        .append("uniqueId", new ObjectId())
        .append("mongoDBVersion", TEST_MONGODB_VERSION.getVersion())
        .append("mongoDBMajorVersion", TEST_MONGODB_VERSION.getMajorVersionString())
        .append("createDate", new Date())
        .append("lastUpdateDate", new Date())
        .append("diskSizeGB", 50.0)
        .append("mongoDBUriHosts", hosts)
        .append("mongoDBUriHostsLastUpdateDate", new Date())
        .append("privateMongoDBUriHosts", privateHostsDBList)
        .append("configDiskSizeGB", 25.0)
        .append("numMongoS", 1)
        .append("state", "WORKING")
        .append(ClusterDescription.FieldDefs.RESTORE_JOB_IDS, new BasicDBList())
        .append("backupEnabled", true)
        .append("diskBackupEnabled", false)
        .append("isPaused", false)
        .append("biConnector", defaultBiConnectorDoc)
        .append("geoSharding", defaultGeoShardingDoc)
        .append("encryptionAtRestProvider", ClusterDescription.EncryptionAtRestProvider.NONE.name())
        .append(
            FieldDefs.NEEDS_MONGODB_CONFIG_PUBLISH_RESTART_ALLOWED,
            ProcessRestartAllowedState.NONE.name())
        .append("hostnameSchemeForAgents", pHostnameScheme.name())
        .append(
            "deploymentClusterName",
            pHostnameScheme.equals(HostnameScheme.LEGACY)
                ? pClusterName
                : NDSDefaults.generateInternalDeploymentClusterName(pGroupId, pClusterName))
        .append("dnsPin", "abc12")
        .append("clusterNamePrefix", pClusterName)
        .append("hostnameSubdomainLevel", InstanceHostname.SubdomainLevel.MONGODB)
        .append(
            ShardedClusterDescription.FieldDefs.HAS_PRIVATE_ENDPOINT_LEGACY_CONNECTION_STRINGS,
            false)
        .append(
            ShardedClusterDescription.FieldDefs
                .PRIVATE_ENDPOINT_ID_TO_LOAD_BALANCED_SRV_ADDRESS_MAP,
            new BasicDBObject(Map.of()))
        .append(
            FieldDefs.TERMINATION_PROTECTION_ENABLED, NDSDefaults.TERMINATION_PROTECTION_ENABLED)
        .append("internalClusterRole", InternalClusterRole.NONE.name())
        .append("clusterType", ClusterType.SHARDED.name())
        .append(
            ShardedClusterDescription.FieldDefs.CHECK_METADATA_CONSISTENCY,
            new CheckMetadataConsistency(null, false, false, null, null, 0).toDBObject());
  }

  public static BasicDBObject getAWSShardedClusterDescription() {
    return getAWSShardedClusterDescription(new ObjectId());
  }

  public static BasicDBObject getNDSGroupAllFields() {
    return getNDSGroupAllFields(false, getAWSContainer());
  }

  public static BasicDBObject getNDSGroupAllFields(final BasicDBObject container) {
    return getNDSGroupAllFields(false, container);
  }

  public static BasicDBObject getNDSGroupAllFields(final BasicDBList containers) {
    return getNDSGroupAllFields(false, containers);
  }

  public static BasicDBObject getNDSGroupAllFields(final boolean pLdapAuthenticationEnabled) {
    return getNDSGroupAllFields(pLdapAuthenticationEnabled, getAWSContainer());
  }

  public static BasicDBObject getNDSGroupAllFields(
      final BasicDBObject container, final ObjectId id) {
    return getNDSGroupAllFields(container).append("_id", id);
  }

  public static BasicDBObject getNDSGroupAllFields(final ObjectId id) {
    final BasicDBObject dbObject =
        getNDSGroupAllFields()
            .append("_id", id)
            .append(NDSGroup.FieldDefs.DNS_PIN, id.toHexString());
    dbObject.remove(HealthCheckMetadata.FieldDefs.HEALTH_CHECK_JOB_ID);
    return dbObject;
  }

  public static BasicDBObject getNDSGroupAllFields(
      final boolean pLdapAuthenticationEnabled, final BasicDBObject container) {
    final BasicDBList containers = new BasicDBList();
    containers.add(container);
    return getNDSGroupAllFields(pLdapAuthenticationEnabled, containers);
  }

  public static BasicDBObject getNDSGroupAllFields(
      final boolean pLdapAuthenticationEnabled, final BasicDBList pContainers) {
    return getNDSGroupAllFields(pLdapAuthenticationEnabled, pContainers, new ObjectId());
  }

  public static BasicDBObject getNDSGroupAllFields(
      final boolean pLdapAuthenticationEnabled,
      final BasicDBList pContainers,
      final ObjectId pGroupId) {

    final BasicDBList fixedAgentVersions = new BasicDBList();
    fixedAgentVersions.add(
        new FixedAgentVersion(AgentType.AUTOMATION, "3.9.0.2119-1").toDBObject());
    fixedAgentVersions.add(new FixedAgentVersion(AgentType.MONITORING, "5.7.0.368-1").toDBObject());
    fixedAgentVersions.add(new FixedAgentVersion(AgentType.BACKUP, "5.4.0.493-1").toDBObject());

    return new BasicDBObject()
        .append("_id", pGroupId)
        .append("tseSSHKey", new BasicDBObject())
        .append("toorSSHKey", new BasicDBObject())
        .append("state", NDSGroup.NDSState.IDLE.name())
        .append("currentPlanId", new ObjectId())
        .append("createDate", new Date())
        .append("lastPlanningDate", new Date())
        .append("nextPlanningDate", new Date())
        .append("failedPlanCount", 1)
        .append("lastPlanCreateDate", new Date())
        .append("lastPlanCompleteDate", new Date())
        .append("cloudProviderContainers", pContainers)
        .append("throttleState", ThrottleState.INHERIT.name())
        .append("dnsPin", pGroupId.toHexString())
        .append(
            "networkPermissionList",
            new NDSNetworkPermissionList(
                    NDSNetworkPermission.getPermissionsFromStrings("***************/32"))
                .toDBObject())
        .append(
            "privateNetworkSettings",
            new BasicDBObject()
                .append(
                    "endpointIds",
                    Stream.of(
                            new BasicDBObject("endpointId", "endpoint1")
                                .append("type", "DATA_LAKE")
                                .append("provider", "AWS")
                                .append("comment", "comment1"))
                        .collect(DbUtils.toBasicDBList())))
        .append("users", new BasicDBList())
        .append(NDSGroup.FieldDefs.FIXED_AGENT_VERSIONS, fixedAgentVersions)
        .append("userSecurity", getDefaultUserSecurity(pLdapAuthenticationEnabled))
        .append("auditLog", new AuditLog().toDBObject())
        .append("streamsAuditLog", new AuditLog().toDBObject())
        .append("encryptionAtRest", getDefaultEncryptionAtRest())
        .append("cloudProviderAccess", getDefaultCloudProviderAccess())
        .append("useCNRegionsOnly", false)
        .append("regionUsageRestrictions", RegionUsageRestrictions.NONE.name())
        .append("nextBillingMeterCheckDate", new Date())
        .append("lastSuccessfulMeterReportDate", new Date())
        .append("nextFlexBillingMeterCheckDate", new Date())
        .append("lastSuccessfulFlexBillingMeterReportDate", new Date())
        .append("healthCheckJobId", new ObjectId())
        .append("dataProcessingRegion", RegionName.toBasicDBObject(AWSRegionName.US_EAST_1))
        .append("pushBasedLogExport", new PushBasedLogExport().toDBObject())
        .append("healthCheckMetadata", new HealthCheckMetadata(new Date()).toDBObject())
        .append("ftdcExport", new FTDCExport().toDBObject())
        .append(
            NDSGroup.FieldDefs.MAINTENANCE_WINDOW,
            NDSGroupMaintenanceWindow.getSystemGeneratedMaintenanceWindow().toDBObject());
  }

  public static BasicDBObject getAzureGroupAllFields() {
    final ObjectId id = new ObjectId();
    final BasicDBList containers = new BasicDBList();
    containers.add(getAzureContainer());

    return new BasicDBObject()
        .append("_id", id)
        .append("tseSSHKey", new BasicDBObject())
        .append("toorSSHKey", new BasicDBObject())
        .append("state", NDSGroup.NDSState.IDLE.name())
        .append("currentPlanId", new ObjectId())
        .append("createDate", new Date())
        .append("lastPlanningDate", new Date())
        .append("nextPlanningDate", new Date())
        .append("failedPlanCount", 1)
        .append("lastPlanCreateDate", new Date())
        .append("lastPlanCompleteDate", new Date())
        .append("cloudProviderContainers", containers)
        .append("throttleState", ThrottleState.INHERIT.name())
        .append("dnsPin", id.toHexString())
        .append(
            "networkPermissionList",
            new NDSNetworkPermissionList(
                    NDSNetworkPermission.getPermissionsFromStrings("***************/32"))
                .toDBObject())
        .append("users", new BasicDBList())
        .append("userSecurity", getDefaultUserSecurity())
        .append("auditLog", new AuditLog().toDBObject())
        .append("encryptionAtRest", getDefaultEncryptionAtRest())
        .append("cloudProviderAccess", getDefaultCloudProviderAccess())
        .append("nextBillingMeterCheckDate", new Date())
        .append("lastSuccessfulMeterReportDate", new Date());
  }

  public static BasicDBObject getNDSGroupAllFields_WithThrottledClusters() {
    final BasicDBObject baseObj = getNDSGroupAllFields();

    final BasicDBList throttledClusters = new BasicDBList();
    throttledClusters.add("cluster1");
    throttledClusters.add("cluster2");
    baseObj.append("throttledClusterNames", throttledClusters);

    return baseObj;
  }

  public static BasicDBObject getDefaultUserSecurity() {
    return getDefaultUserSecurity(false);
  }

  public static BasicDBObject getDefaultUserSecurity(final boolean pAuthenticationEnabled) {
    final BasicDBObject ldapDoc =
        new BasicDBObject()
            .append(NDSLDAP.FieldDefs.AUTHENTICATION_ENABLED, pAuthenticationEnabled)
            .append(NDSLDAP.FieldDefs.AUTHORIZATION_ENABLED, false)
            .append(NDSLDAP.FieldDefs.BIND_USERNAME, null)
            .append(NDSLDAP.FieldDefs.BIND_PASSWORD, null)
            .append(NDSLDAP.FieldDefs.PORT, null)
            .append(NDSLDAP.FieldDefs.CA_CERTIFICATE, null);
    final BasicDBObject managedX509Doc =
        new BasicDBObject()
            .append(NDSManagedX509.FieldDefs.CAS, new BasicDBList())
            .append(NDSManagedX509.FieldDefs.IS_ENABLED, false);

    final BasicDBObject customerX509Doc =
        new BasicDBObject()
            .append(NDSCustomerX509.FieldDefs.CAS, new BasicDBList())
            .append(NDSCustomerX509.FieldDefs.CRL_DISTRIBUTION_POINTS, new BasicDBList())
            .append(NDSCustomerX509.FieldDefs.CRLS, new BasicDBList());

    return new BasicDBObject()
        .append(NDSUserSecurity.FieldDefs.LDAP, ldapDoc)
        .append(NDSUserSecurity.FieldDefs.MANAGED_X509, managedX509Doc)
        .append(NDSUserSecurity.FieldDefs.CUSTOMER_X509, customerX509Doc);
  }

  public static BasicDBObject getDefaultAWSKeyManagementConfig() {
    return new BasicDBObject()
        .append(KeyManagementConfig.FieldDefs.ENABLED, false)
        .append(NDSAWSKMS.FieldDefs.ACCESS_KEY_ID, null)
        .append(NDSAWSKMS.FieldDefs.SECRET_ACCESS_KEY, null)
        .append(NDSAWSKMS.FieldDefs.CUSTOMER_MASTER_KEY_ID, null)
        .append(NDSAWSKMS.FieldDefs.REGION, null)
        .append(KeyManagementConfig.FieldDefs.LAST_UPDATED_KEY_ID, null)
        .append(KeyManagementConfig.FieldDefs.LAST_KMIP_MASTER_KEY_ROTATION, null);
  }

  public static BasicDBObject getAWSKeyManagementConfig() {
    return getAWSKeyManagementConfig(false);
  }

  public static BasicDBObject getAWSKeyManagementConfig(final boolean pIsRoleBasedKMS) {
    final String encryptedSecretAccessKey = EncryptionUtils.genEncryptStr("awsSecretAccessKey");
    final BasicDBObject awsKmsDoc =
        getDefaultAWSKeyManagementConfig()
            .append(KeyManagementConfig.FieldDefs.ENABLED, true)
            .append(KeyManagementConfig.FieldDefs.VALID, true)
            .append(NDSAWSKMS.FieldDefs.CUSTOMER_MASTER_KEY_ID, "customerKMSMasterKey")
            .append(NDSAWSKMS.FieldDefs.REGION, AWSRegionName.US_EAST_1.name())
            .append(KeyManagementConfig.FieldDefs.LAST_UPDATED_KEY_ID, new Date())
            .append(KeyManagementConfig.FieldDefs.LAST_KMIP_MASTER_KEY_ROTATION, new Date());
    if (pIsRoleBasedKMS) {
      awsKmsDoc.append(NDSAWSKMS.FieldDefs.ROLE_ID, new ObjectId());
    } else {
      awsKmsDoc
          .append(NDSAWSKMS.FieldDefs.ACCESS_KEY_ID, "awsAccessKey")
          .append(NDSAWSKMS.FieldDefs.SECRET_ACCESS_KEY, encryptedSecretAccessKey);
    }
    return awsKmsDoc;
  }

  public static BasicDBObject getDefaultAzureKeyManagementConfig() {
    return new BasicDBObject()
        .append(KeyManagementConfig.FieldDefs.ENABLED, false)
        .append(KeyManagementConfig.FieldDefs.LAST_UPDATED_KEY_ID, null)
        .append(KeyManagementConfig.FieldDefs.LAST_KMIP_MASTER_KEY_ROTATION, null)
        .append(NDSAzureKeyVault.FieldDefs.CLIENT_ID, null)
        .append(NDSAzureKeyVault.FieldDefs.TENANT_ID, null)
        .append(NDSAzureKeyVault.FieldDefs.SECRET, null)
        .append(NDSAzureKeyVault.FieldDefs.AZURE_ENVIRONMENT, null)
        .append(NDSAzureKeyVault.FieldDefs.SUBSCRIPTION_ID, null)
        .append(NDSAzureKeyVault.FieldDefs.RESOURCE_GROUP_NAME, null)
        .append(NDSAzureKeyVault.FieldDefs.KEY_VAULT_NAME, null)
        .append(NDSAzureKeyVault.FieldDefs.KEY_IDENTIFIER, null);
  }

  public static BasicDBObject getAzureKeyManagementConfig() {
    return getDefaultAzureKeyManagementConfig()
        .append(KeyManagementConfig.FieldDefs.ENABLED, true)
        .append(KeyManagementConfig.FieldDefs.VALID, true)
        .append(KeyManagementConfig.FieldDefs.LAST_UPDATED_KEY_ID, new Date())
        .append(KeyManagementConfig.FieldDefs.LAST_KMIP_MASTER_KEY_ROTATION, new Date())
        .append(NDSAzureKeyVault.FieldDefs.CLIENT_ID, "azureKeyVaultClientId")
        .append(NDSAzureKeyVault.FieldDefs.TENANT_ID, "azureKeyVaultTenantId")
        .append(
            NDSAzureKeyVault.FieldDefs.SECRET,
            "64eed9e777fd1e4e5fafc176b4e778e83a798189dec64e556315a5832e84d85a-1d4eccba8068adb51533a2dd3ba41c87-acaaf411fc4b56bdf8ffb0a4e0d81eaf")
        .append(NDSAzureKeyVault.FieldDefs.AZURE_ENVIRONMENT, AzureEnvironment.AZURE)
        .append(NDSAzureKeyVault.FieldDefs.SUBSCRIPTION_ID, "azureKeyVaultSubscriptionId")
        .append(NDSAzureKeyVault.FieldDefs.RESOURCE_GROUP_NAME, "azureKeyVaultResourceGroup")
        .append(NDSAzureKeyVault.FieldDefs.KEY_VAULT_NAME, "azureKeyVaultName")
        .append(NDSAzureKeyVault.FieldDefs.KEY_IDENTIFIER, "azureKeyIdentifier");
  }

  public static BasicDBObject getDefaultGCPKeyManagementConfig() {
    return new BasicDBObject()
        .append(KeyManagementConfig.FieldDefs.ENABLED, false)
        .append(KeyManagementConfig.FieldDefs.LAST_UPDATED_KEY_ID, null)
        .append(KeyManagementConfig.FieldDefs.LAST_KMIP_MASTER_KEY_ROTATION, null)
        .append(NDSGoogleCloudKMS.FieldDefs.SERVICE_ACCOUNT_KEY, null)
        .append(NDSGoogleCloudKMS.FieldDefs.KEY_VERSION_RESOURCE_ID, null);
  }

  public static BasicDBObject getGCPKeyManagementConfig() {
    return getDefaultGCPKeyManagementConfig()
        .append(KeyManagementConfig.FieldDefs.ENABLED, true)
        .append(KeyManagementConfig.FieldDefs.VALID, true)
        .append(KeyManagementConfig.FieldDefs.LAST_UPDATED_KEY_ID, new Date())
        .append(KeyManagementConfig.FieldDefs.LAST_KMIP_MASTER_KEY_ROTATION, new Date())
        .append(
            NDSGoogleCloudKMS.FieldDefs.SERVICE_ACCOUNT_KEY,
            EncryptionUtils.genEncryptStr("serviceAccountKey"))
        .append(NDSGoogleCloudKMS.FieldDefs.KEY_VERSION_RESOURCE_ID, "resourceId");
  }

  public static BasicDBObject getDefaultEncryptionAtRest() {
    return new BasicDBObject()
        .append(NDSEncryptionAtRest.FieldDefs.AWS_KMS, getDefaultAWSKeyManagementConfig())
        .append(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT, getDefaultAzureKeyManagementConfig())
        .append(NDSEncryptionAtRest.FieldDefs.GOOGLE_CLOUD_KMS, getDefaultGCPKeyManagementConfig());
  }

  public static BasicDBObject getAwsEncryptionAtRest() {
    return getAwsEncryptionAtRest(false);
  }

  public static BasicDBObject getAwsEncryptionAtRest(final boolean isRoleBasedKMS) {
    return getDefaultEncryptionAtRest()
        .append(NDSEncryptionAtRest.FieldDefs.AWS_KMS, getAWSKeyManagementConfig(isRoleBasedKMS));
  }

  public static BasicDBObject getAzureEncryptionAtRest() {
    return getDefaultEncryptionAtRest()
        .append(NDSEncryptionAtRest.FieldDefs.AZURE_KEY_VAULT, getAzureKeyManagementConfig());
  }

  public static BasicDBObject getGCPEncryptionAtRest() {
    return getDefaultEncryptionAtRest()
        .append(NDSEncryptionAtRest.FieldDefs.GOOGLE_CLOUD_KMS, getGCPKeyManagementConfig());
  }

  public static BasicDBObject getDefaultCloudProviderAccess() {
    return new BasicDBObject()
        .append(NDSCloudProviderAccess.FieldDefs.AWS_IAM_ROLES, new BasicDBList());
  }

  public static BasicDBObject getUnprovisionedAWSContainer() {
    return getUnprovisionedAWSContainer(AWSRegionName.US_EAST_1);
  }

  public static BasicDBObject getUnprovisionedAWSContainer(final AWSRegionName pRegion) {

    return new BasicDBObject()
        .append("id", new ObjectId())
        .append("providerName", CloudProvider.AWS.name())
        .append("awsAccountId", new ObjectId())
        .append("region", pRegion.name())
        .append("peers", new BasicDBList());
  }

  public static BasicDBObject getUnprovisionedAWSContainer(
      final AWSRegionName pRegion, final ObjectId pAWSAccount) {
    return new BasicDBObject()
        .append("id", new ObjectId())
        .append("providerName", CloudProvider.AWS.name())
        .append("awsAccountId", pAWSAccount)
        .append("region", pRegion)
        .append("peers", new BasicDBList());
  }

  public static BasicDBObject getUnprovisionedAzureContainer() {
    return getUnprovisionedAzureContainer(AzureRegionName.US_EAST);
  }

  public static BasicDBObject getUnprovisionedGCPContainer() {
    return new BasicDBObject()
        .append("id", new ObjectId())
        .append("providerName", CloudProvider.GCP.name())
        .append("organizationId", new ObjectId())
        .append("peers", new BasicDBList());
  }

  public static BasicDBObject getAzureContainer() {
    return getAzureContainer(AzureRegionName.US_EAST_2);
  }

  public static BasicDBObject getAzureContainer(final AzureRegionName pRegionName) {
    return getAzureContainer(pRegionName, new ObjectId());
  }

  public static BasicDBObject getAzureContainer(
      final AzureRegionName pRegionName, final ObjectId pSubscriptionId) {
    return new BasicDBObject()
        .append("id", new ObjectId())
        .append("providerName", CloudProvider.AZURE.name())
        .append("azureSubscriptionId", pSubscriptionId)
        .append("region", pRegionName.name())
        .append("vnetName", "123")
        .append("resourceGroupName", "456")
        .append("addressSpace", "*************/21")
        .append("peers", new BasicDBList())
        .append("subnets", DbUtils.toBasicDBList(new AzureSubnet("subnet-1").toDBObject()));
  }

  public static BasicDBObject getUnprovisionedAzureContainer(final AzureRegionName pRegionName) {
    return getUnprovisionedAzureContainer(pRegionName, new ObjectId());
  }

  public static BasicDBObject getUnprovisionedAzureContainer(
      final AzureRegionName pRegionName, final ObjectId pSubscriptionId) {
    return new BasicDBObject()
        .append("id", new ObjectId())
        .append("providerName", CloudProvider.AZURE.name())
        .append("azureSubscriptionId", pSubscriptionId)
        .append("region", pRegionName.name())
        .append("vnetName", null)
        .append("resourceGroupName", null)
        .append("addressSpace", "*************/21")
        .append("peers", new BasicDBList());
  }

  public static BasicDBObject getAzureContainerWithPrivateLink(
      final AzureRegionName pAzureRegionName) {
    final BasicDBObject privateLinkObject = new AzurePrivateLinkConnection().toDBObject();
    privateLinkObject.append("needsUpdateAfter", new Date());
    return getAzureContainer(pAzureRegionName)
        .append(AzureCloudProviderContainer.FieldDefs.PRIVATE_LINK_CONNECTION, privateLinkObject);
  }

  public static BasicDBObject getUnprovisionedAzureContainerWithPrivateLink() {
    final BasicDBObject privateLinkObject = new AzurePrivateLinkConnection().toDBObject();
    privateLinkObject.append("needsUpdateAfter", new Date());

    return getUnprovisionedAzureContainer(AzureRegionName.US_EAST)
        .append(AzureCloudProviderContainer.FieldDefs.PRIVATE_LINK_CONNECTION, privateLinkObject);
  }

  public static BasicDBObject getAzureContainerWithFullyProvisionedPrivateLink(
      final BasicDBList pPrivateEndpoints, final AzureRegionName pAzureRegionName) {
    return getAzureContainerWithFullyProvisionedPrivateLink(
        pPrivateEndpoints, pAzureRegionName, new ObjectId());
  }

  public static BasicDBObject getAzureContainerWithFullyProvisionedPrivateLink(
      final BasicDBList pPrivateEndpoints,
      final AzureRegionName pAzureRegionName,
      final ObjectId pContainerId) {
    final DBObject privateLinkConnection =
        new BasicDBObject()
            .append("privateLinkServiceResourceId", "plsrid")
            .append("privateLinkServiceName", "plsn")
            .append("loadBalancerName", "lbn")
            .append("privateEndpoints", pPrivateEndpoints)
            .append("status", AzurePrivateLinkConnection.Status.AVAILABLE.name())
            .append("id", new ObjectId())
            .append("errorMessage", null)
            .append("deleteRequested", false)
            .append("needsUpdateAfter", null);

    return getAzureContainer(pAzureRegionName)
        .append("id", pContainerId)
        .append(
            AzureCloudProviderContainer.FieldDefs.PRIVATE_LINK_CONNECTION, privateLinkConnection);
  }

  public static AzurePrivateEndpoint getInitiatingAzurePrivateEndpoint() {
    return getInitiatingAzurePrivateEndpoint("resourceId", "*************");
  }

  public static AzurePrivateEndpoint getInitiatingAzurePrivateEndpoint(
      final String pResourceId, final String pPrivateIPAddress) {
    return getAzurePrivateEndpoint(
        pResourceId, pPrivateIPAddress, AzurePrivateEndpoint.Status.INITIATING);
  }

  public static AzurePrivateEndpoint getAzurePrivateEndpoint(
      final String pResourceId,
      final String pPrivateIPAddress,
      final AzurePrivateEndpoint.Status pStatus) {
    final BasicDBObject dbObject =
        new BasicDBObject()
            .append(AzurePrivateEndpoint.FieldDefs.PRIVATE_ENDPOINT_RESOURCE_ID, pResourceId)
            .append(AzurePrivateEndpoint.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS, pPrivateIPAddress)
            .append(
                AzurePrivateEndpoint.FieldDefs.PRIVATE_ENDPOINT_PROVIDER_HOSTNAME, "example.com")
            .append(
                AzurePrivateEndpoint.FieldDefs.PRIVATE_ENDPOINT_CONNECTION_NAME,
                AZURE_PRIVATE_ENDPOIONT_CONNECTION_NAME)
            .append(AzurePrivateEndpoint.FieldDefs.STATUS, pStatus.name())
            .append(AzurePrivateEndpoint.FieldDefs.ERROR_MESSAGE, "example error")
            .append(AzurePrivateEndpoint.FieldDefs.DELETE_REQUESTED, false)
            .append(AzurePrivateEndpoint.FieldDefs.INDEX, 1);

    return new AzurePrivateEndpoint(dbObject);
  }

  public static AzurePrivateEndpoint getAvailableAzurePrivateEndpoint() {
    final BasicDBObject dbObject =
        new BasicDBObject()
            .append(
                AzurePrivateEndpoint.FieldDefs.PRIVATE_ENDPOINT_RESOURCE_ID,
                "/subscriptions/a1b2c3d4-dead-abcd-1111-000000000000/resourceGroups/test-private-link/providers/Microsoft.Network/privateEndpoints/testEndpoint")
            .append(AzurePrivateEndpoint.FieldDefs.PRIVATE_ENDPOINT_IP_ADDRESS, "*************")
            .append(
                AzurePrivateEndpoint.FieldDefs.PRIVATE_ENDPOINT_PROVIDER_HOSTNAME,
                "example.azure.com")
            .append(AzurePrivateEndpoint.FieldDefs.PRIVATE_ENDPOINT_MONGODB_HOSTNAME, "example.com")
            .append(
                AzurePrivateEndpoint.FieldDefs.PRIVATE_ENDPOINT_CONNECTION_NAME, "connectionName")
            .append(
                AzurePrivateEndpoint.FieldDefs.STATUS, AzurePrivateEndpoint.Status.AVAILABLE.name())
            .append(AzurePrivateEndpoint.FieldDefs.ERROR_MESSAGE, "example error")
            .append(AzurePrivateEndpoint.FieldDefs.DELETE_REQUESTED, false)
            .append(AzurePrivateEndpoint.FieldDefs.INDEX, 0);

    return new AzurePrivateEndpoint(dbObject);
  }

  public static AzurePrivateEndpoint getRejectedAzurePrivateEndpoint(final int pIndex) {
    return new AzurePrivateEndpoint(
        "id",
        "********",
        "hostname",
        "mongodb",
        null,
        AzurePrivateEndpoint.Status.FAILED,
        "invalid",
        false,
        pIndex);
  }

  public static AzurePrivateLinkConnection getAzurePrivateLinkConnection() {
    return getAzurePrivateLinkConnection(
        List.of(
            new AzurePrivateEndpoint(
                "/subscriptions/a1b2c3d4-dead-abcd-1111-000000000000/resourceGroups/test-private-link/providers/Microsoft.Network/privateEndpoints/testEndpoint",
                "ipAddr",
                "hostname",
                "mongodbHostname",
                "connectionName",
                AzurePrivateEndpoint.Status.INITIATING,
                "error0",
                false,
                0)));
  }

  public static AzurePrivateLinkConnection getAzurePrivateLinkConnection(
      final List<AzurePrivateEndpoint> pEndpoints) {
    return getAzurePrivateLinkConnection(ObjectId.get(), pEndpoints);
  }

  public static AzurePrivateLinkConnection getAzurePrivateLinkConnection(
      final ObjectId pEndpointServiceId, final List<AzurePrivateEndpoint> pEndpoints) {
    return new AzurePrivateLinkConnection(
        pEndpointServiceId,
        "/subscriptions/a1b2c3d4-dead-abcd-1111-000000000000/resourceGroups/test-private-link/providers/Microsoft.Network/privateEndpoints/testEndpoint",
        "serviceName0",
        "balancerName0",
        pEndpoints,
        DedicatedEndpointService.Status.AVAILABLE,
        "errorMessage0",
        false,
        null);
  }

  public static AzurePrivateLinkConnectionInboundNATRule
      getAzurePrivateLinkConnectionInboundNATRule(
          final ObjectId pGroupId,
          final int pPort,
          final ObjectId pInstanceId,
          final String pInstanceHostname) {
    return getAzurePrivateLinkConnectionInboundNATRule(
        pGroupId, pPort, pInstanceId, pInstanceHostname, NDSDefaults.MONGOD_PUBLIC_PORT);
  }

  public static AzurePrivateLinkConnectionInboundNATRule
      getAzurePrivateLinkConnectionInboundNATRule(
          final ObjectId pGroupId,
          final int pPort,
          final ObjectId pInstanceId,
          final String pInstanceHostname,
          final int pNdsProcessPort) {
    return new AzurePrivateLinkConnectionInboundNATRule(
        pGroupId,
        pInstanceId,
        pPort,
        pInstanceHostname,
        null,
        pNdsProcessPort,
        new ObjectId(),
        "inboundNATRule",
        Usage.VISIBLE_NODE);
  }

  public static GCPPrivateServiceConnectEndpointGroup getGCPPrivateServiceConnectEndpointGroup(
      final String pEndpointGroupName,
      final String pConsumerGCPProjectId,
      final List<GCPConsumerForwardingRule> pForwardingRules,
      final int pIndex) {
    return new GCPPrivateServiceConnectEndpointGroup(
        pEndpointGroupName, pConsumerGCPProjectId, pForwardingRules, pIndex);
  }

  public static GCPPrivateServiceConnectEndpointGroup getGCPPrivateServiceConnectEndpointGroup(
      final String pEndpointGroupName,
      final String pConsumerGCPProjectId,
      final List<GCPConsumerForwardingRule> pForwardingRules,
      final int pIndex,
      final GCPPrivateServiceConnectEndpointGroup.Status pStatus) {
    return new GCPPrivateServiceConnectEndpointGroup(
        new ObjectId(),
        pEndpointGroupName,
        pConsumerGCPProjectId,
        false,
        null,
        pStatus,
        pForwardingRules,
        pIndex);
  }

  public static List<GCPConsumerForwardingRule>
      getGCPConsumerForwardingRulesForPrivateServiceConnection(
          final ObjectId pPrivateServiceConnectionId,
          final int pNumForwardingRules,
          final String pEndpointGroupName) {
    return IntStream.range(0, pNumForwardingRules)
        .mapToObj(
            (i) ->
                getGCPConsumerForwardingRule(
                    pPrivateServiceConnectionId,
                    String.format("%s-%d", pEndpointGroupName, i),
                    i,
                    String.format("0.0.0.%s", i)))
        .collect(Collectors.toList());
  }

  public static List<GCPConsumerForwardingRule>
      getGCPConsumerForwardingRulesForPrivateServiceConnection(
          final int pNumForwardingRules,
          final String pEndpointGroupName,
          final GCPConsumerForwardingRule.Status pStatus) {
    return IntStream.range(0, pNumForwardingRules)
        .mapToObj(
            i ->
                new GCPConsumerForwardingRule(
                    ObjectId.get(),
                    ObjectId.get(),
                    String.format("%s-%d", pEndpointGroupName, i),
                    i,
                    String.format("0.0.0.0.%d", i),
                    "hostname.gcp.com",
                    "hostname.com",
                    pStatus,
                    null,
                    false))
        .collect(Collectors.toList());
  }

  public static GCPConsumerForwardingRule getGCPConsumerForwardingRule(
      final ObjectId pPrivateServiceConnectionId,
      final String pForwardingRuleName,
      final int pIndex,
      final String pIPAddress) {
    return new GCPConsumerForwardingRule(pForwardingRuleName, pIndex, pIPAddress);
  }

  public static GCPPrivateServiceConnectRegionGroup getGCPPSCRegionGroupWithEndpointGroup(
      final int pSize) {
    return getGCPPSCRegionGroupWithEndpointGroup(
        pSize, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, "melodrama-endpoints");
  }

  public static GCPPrivateServiceConnectRegionGroup getGCPPSCRegionGroupWithEndpointGroup(
      final int pSize, final GCPRegionName pRegionName, final String pEndpointGroupName) {
    final List<GCPConsumerForwardingRule> forwardingRules =
        getGCPConsumerForwardingRulesForPrivateServiceConnection(
            ObjectId.get(), pSize, pEndpointGroupName);
    final GCPPrivateServiceConnectEndpointGroup endpointGroup =
        getGCPPrivateServiceConnectEndpointGroup(
            pEndpointGroupName, "melodrama", forwardingRules, 0);
    return getGCPPrivateServiceConnectRegionGroup(
        pRegionName,
        GCPPrivateServiceConnectRegionGroup.Status.AVAILABLE,
        false,
        List.of(endpointGroup),
        pSize);
  }

  public static GCPPrivateServiceConnectRegionGroup getGCPPSCRegionGroupWithActiveEndpointGroup(
      final int pSize, final GCPRegionName pRegionName, final String pEndpointGroupName) {
    final List<GCPConsumerForwardingRule> forwardingRules =
        getGCPConsumerForwardingRulesForPrivateServiceConnection(
            pSize, pEndpointGroupName, GCPConsumerForwardingRule.Status.AVAILABLE);
    final GCPPrivateServiceConnectEndpointGroup endpointGroup =
        getGCPPrivateServiceConnectEndpointGroup(
            pEndpointGroupName,
            "melodrama",
            forwardingRules,
            0,
            GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE);
    return getGCPPrivateServiceConnectRegionGroup(
        pRegionName,
        GCPPrivateServiceConnectRegionGroup.Status.AVAILABLE,
        false,
        List.of(endpointGroup),
        pSize);
  }

  public static GCPPrivateServiceConnectRegionGroup getGCPPSCRegionGroupWithActiveEndpointGroups(
      final int pSize,
      final int pNumEndpointGroups,
      final GCPRegionName pRegionName,
      final String pEndpointGroupBaseName) {
    final List<GCPPrivateServiceConnectEndpointGroup> endpointGroups = new ArrayList<>();
    for (int i = 0; i < pNumEndpointGroups; i++) {
      final String endpointGroupName = pEndpointGroupBaseName + i;
      final List<GCPConsumerForwardingRule> forwardingRules =
          getGCPConsumerForwardingRulesForPrivateServiceConnection(
              pSize, endpointGroupName, GCPConsumerForwardingRule.Status.AVAILABLE);
      final GCPPrivateServiceConnectEndpointGroup endpointGroup =
          getGCPPrivateServiceConnectEndpointGroup(
              endpointGroupName,
              "melodrama",
              forwardingRules,
              i,
              GCPPrivateServiceConnectEndpointGroup.Status.AVAILABLE);
      endpointGroups.add(endpointGroup);
    }
    return getGCPPrivateServiceConnectRegionGroup(
        pRegionName,
        GCPPrivateServiceConnectRegionGroup.Status.AVAILABLE,
        false,
        endpointGroups,
        pSize);
  }

  public static GCPPrivateServiceConnectRegionGroup getGCPPrivateServiceConnectRegionGroup() {
    return getGCPPrivateServiceConnectRegionGroup(
        GCPRegionName.CENTRAL_US, DedicatedEndpointService.Status.AVAILABLE);
  }

  public static GCPPrivateServiceConnectRegionGroup getGCPPrivateServiceConnectRegionGroup(
      final GCPRegionName pRegionName, final DedicatedEndpointService.Status pStatus) {
    return getGCPPrivateServiceConnectRegionGroup(pRegionName, pStatus, false);
  }

  public static GCPPrivateServiceConnectRegionGroup getGCPPrivateServiceConnectRegionGroup(
      final GCPRegionName pRegionName,
      final DedicatedEndpointService.Status pStatus,
      final boolean pDeleteRequested) {
    return getGCPPrivateServiceConnectRegionGroup(
        pRegionName, pStatus, pDeleteRequested, List.of(), 50);
  }

  public static GCPPrivateServiceConnectRegionGroup getGCPPrivateServiceConnectRegionGroup(
      final GCPRegionName pRegionName,
      final DedicatedEndpointService.Status pStatus,
      final boolean pDeleteRequested,
      final List<GCPPrivateServiceConnectEndpointGroup> pEndpointGroups,
      final int pSize) {
    return new GCPPrivateServiceConnectRegionGroup(
        ObjectId.get(),
        pStatus,
        null,
        pDeleteRequested,
        null,
        pRegionName,
        pEndpointGroups,
        getGCPPSCRegionGroupCIDRBlock(27, pSize),
        pSize);
  }

  public static List<GCPPrivateServiceConnection> getGCPPrivateServiceConnections(
      final ObjectId pProjectId, final GCPPrivateServiceConnectRegionGroup pRegionGroup) {
    return getGCPPrivateServiceConnectionsWithStatus(
        pProjectId, pRegionGroup, GCPPrivateServiceConnection.Status.AVAILABLE);
  }

  public static List<GCPPrivateServiceConnection> getGCPPrivateServiceConnectionsWithStatus(
      final ObjectId pProjectId,
      final GCPPrivateServiceConnectRegionGroup pRegionGroup,
      final GCPPrivateServiceConnection.Status pStatus) {
    final List<GCPPrivateServiceConnection> privateServiceConnections = new ArrayList<>();
    for (int i = 0; i < pRegionGroup.getSize().get(); i++) {
      privateServiceConnections.add(
          getGCPPrivateServiceConnection(pProjectId, pRegionGroup, i, pStatus));
    }
    return privateServiceConnections;
  }

  public static List<GCPPrivateServiceConnection> getGCPPrivateServiceConnectionsWithMixedStatuses(
      final ObjectId pProjectId,
      final GCPPrivateServiceConnectRegionGroup pRegionGroup,
      final int pNumInitiating,
      final int pNumFailed) {
    final List<GCPPrivateServiceConnection> privateServiceConnections = new ArrayList<>();

    for (int i = 0; i < pNumInitiating; i++) {
      privateServiceConnections.add(
          getGCPPrivateServiceConnection(
              pProjectId, pRegionGroup, i, GCPPrivateServiceConnection.Status.AVAILABLE));
    }

    for (int i = pNumInitiating; i < pNumInitiating + pNumFailed; i++) {
      privateServiceConnections.add(
          getGCPPrivateServiceConnection(
              pProjectId, pRegionGroup, i, GCPPrivateServiceConnection.Status.FAILED));
    }

    for (int i = pNumInitiating + pNumFailed; i < pRegionGroup.getSize().get(); i++) {
      privateServiceConnections.add(
          getGCPPrivateServiceConnection(
              pProjectId, pRegionGroup, i, GCPPrivateServiceConnection.Status.INITIATING));
    }
    return privateServiceConnections;
  }

  public static List<GCPPrivateServiceConnection>
      getGCPPrivateServiceConnectionsWithMixedStatuses_defaultWaitingForUser(
          final ObjectId pProjectId,
          final GCPPrivateServiceConnectRegionGroup pRegionGroup,
          final int pNumDeleted,
          final int pNumFailed) {

    final List<GCPPrivateServiceConnection> privateServiceConnections = new ArrayList<>();

    for (int i = 0; i < pNumDeleted; i++) {
      privateServiceConnections.add(
          getGCPPrivateServiceConnection(
              pProjectId, pRegionGroup, i, GCPPrivateServiceConnection.Status.DELETED));
    }

    for (int i = pNumDeleted; i < pNumDeleted + pNumFailed; i++) {
      privateServiceConnections.add(
          getGCPPrivateServiceConnection(
              pProjectId, pRegionGroup, i, GCPPrivateServiceConnection.Status.FAILED));
    }

    for (int i = pNumDeleted + pNumFailed; i < pRegionGroup.getSize().get(); i++) {
      privateServiceConnections.add(
          getGCPPrivateServiceConnection(
              pProjectId, pRegionGroup, i, GCPPrivateServiceConnection.Status.AVAILABLE));
    }
    return privateServiceConnections;
  }

  private static GCPPrivateServiceConnection getGCPPrivateServiceConnection(
      final ObjectId pProjectId,
      final GCPPrivateServiceConnectRegionGroup pRegionGroup,
      final int pIndex,
      final GCPPrivateServiceConnection.Status pStatus) {
    return getGCPPrivateServiceConnectionWithStatus(
        pProjectId,
        pRegionGroup.getId(),
        pRegionGroup.getRegionName().get(),
        pIndex,
        pStatus,
        pRegionGroup.getSize().get());
  }

  public static GCPPrivateServiceConnection getGCPPrivateServiceConnection(
      final ObjectId pProjectId,
      final ObjectId pRegionGroupId,
      final GCPRegionName pRegionName,
      final int pIndex,
      final int pNumPSCs) {
    return getGCPPrivateServiceConnectionWithStatus(
        pProjectId,
        pRegionGroupId,
        pRegionName,
        pIndex,
        GCPPrivateServiceConnection.Status.AVAILABLE,
        pNumPSCs);
  }

  public static GCPPrivateServiceConnection getGCPPrivateServiceConnectionWithStatus(
      final ObjectId pProjectId,
      final ObjectId pRegionGroupId,
      final GCPRegionName pRegionName,
      final int pIndex,
      final GCPPrivateServiceConnection.Status pStatus,
      final int pRegionGroupSize) {
    final String serviceAttachmentName =
        String.format("sa-%s-%s-%d", pRegionName.getValue(), pProjectId, pIndex);
    final String cidrBlock = getGCPPSCRegionGroupCIDRBlock(27, pRegionGroupSize);
    return new GCPPrivateServiceConnection(
        ObjectId.get(),
        pProjectId,
        pRegionGroupId,
        pRegionName,
        pIndex,
        serviceAttachmentName,
        List.of(),
        getGCPInternalLoadBalancer(pProjectId, pRegionName, pIndex),
        getNATSubnet(pRegionName, cidrBlock, pIndex, pRegionGroupSize),
        pStatus,
        null,
        false,
        null,
        null);
  }

  public static String getGCPPSCRegionGroupCIDRBlock(final int pSubnetMask, final int pNumPSCs) {
    try {
      return GCPPrivateServiceConnectSvc.calculatePSCRegionGroupCIDRBlock(
          null, pSubnetMask, pNumPSCs, null);
    } catch (final SvcException pE) {
      return "172.31.240.0/21";
    }
  }

  public static GCPInternalLoadBalancer getGCPInternalLoadBalancer(
      final ObjectId pProjectId, final GCPRegionName pRegionName, final int index) {
    final String backendServiceName =
        String.format("ilb-%s-%s-%d", pRegionName.getValue(), pProjectId, index);
    final String healthCheckName =
        String.format("hc-%s-%s-%d", pRegionName.getValue(), pProjectId, index);
    final String internalForwardingRuleName =
        String.format("ifr-%s-%s-%d", pRegionName.getValue(), pProjectId, index);
    return new GCPInternalLoadBalancer(
        backendServiceName, healthCheckName, internalForwardingRuleName, "***********");
  }

  public static GCPNATSubnet getNATSubnet(
      final GCPRegionName pRegionName,
      final String pRegionGroupCIDR,
      final int pIndex,
      final int pNumPSCs) {
    List<String> natSubnets =
        NetUtils.divideCidrBlockIntoSubnets(pRegionGroupCIDR, NetUtils.nextPowerOf2(pNumPSCs))
            .stream()
            .map(IPAddress::toString)
            .collect(Collectors.toList());

    return new GCPNATSubnet(
        String.format("nat-sn-%s-%s-%d", pRegionName.getValue(), pRegionName.getValue(), pIndex),
        pRegionName,
        natSubnets.get(pIndex));
  }

  public static String getPSCResourceNameForTest(
      final String pResourcePrefix,
      final GCPRegionName pRegionName,
      final ObjectId pProjectId,
      final int pIndex) {
    return String.format(
        "%s-%s-%s-%d", pResourcePrefix, pRegionName.getValue(), pProjectId, pIndex);
  }

  public static BasicDBObject getUnprovisionedGCPContainerWithPSC(final Date pNeedsUpdateAfter) {
    final BasicDBObject pscObject =
        getGCPPrivateServiceConnectRegionGroup()
            .toDBObject()
            .append("needsUpdateAfter", pNeedsUpdateAfter);

    return getUnprovisionedGCPContainer()
        .append("pscRegionGroups", Stream.of(pscObject).collect(DbUtils.toBasicDBList()));
  }

  public static BasicDBObject getGCPContainer() {
    return new GCPCloudProviderContainer(new ObjectId(), NDSDefaults.ATLAS_CIDR)
        .toDBObject()
        .append("id", new ObjectId())
        .append("projectId", DEFAULT_GCP_PROJECT_ID)
        .append("networkName", "bar")
        .append("peers", new BasicDBList());
  }

  public static BasicDBList getProvisionedSubnets(final List<GCPRegionName> pRegionNames) {
    final List<IPAddress> cidrBlocks =
        NetUtils.divideCidrBlockIntoSubnets(
            NDSDefaults.ATLAS_CIDR, GCPNDSDefaults.INTERNAL_MAX_REGIONS_PER_CONTAINER);
    return IntStream.range(0, pRegionNames.size())
        .mapToObj(
            i ->
                new GCPAtlasSubnet(
                    "subnet-" + pRegionNames.get(i).name(),
                    pRegionNames.get(i),
                    cidrBlocks.get(i).toString(),
                    null))
        .map(GCPAtlasSubnet::toDBObject)
        .collect(new BasicDBListCollector<>());
  }

  public static BasicDBObject getGCPContainerWithProvisionedSubnets(
      final List<GCPRegionName> pSubnetRegions) {
    return getGCPContainer().append("subnets", getProvisionedSubnets(pSubnetRegions));
  }

  public static BasicDBObject getGCPContainerWithPrivateServiceConnectRegionGroup() {
    return getGCPContainerWithPrivateServiceConnectRegionGroup(50);
  }

  public static BasicDBObject getGCPContainerWithPrivateServiceConnectRegionGroup(final int pSize) {
    final GCPCloudProviderContainer container = new GCPCloudProviderContainer(getGCPContainer());
    final BasicDBList regionGroups = new BasicDBList();
    regionGroups.add(
        getGCPPrivateServiceConnectRegionGroup(
                GCPRegionName.CENTRAL_US,
                DedicatedEndpointService.Status.AVAILABLE,
                false,
                List.of(),
                pSize)
            .toDBObject()
            .append("needsUpdateAfter", new Date()));
    return container
        .toDBObject()
        .append(GCPCloudProviderContainer.FieldDefs.PSC_REGION_GROUPS, regionGroups);
  }

  public static BasicDBObject getGCPContainerWithPSCRegionGroupAndEndpointGroup(final int pSize) {
    return getGCPContainerWithPSCRegionGroupAndEndpointGroup(
        pSize, GCPRegionName.NORTH_AMERICA_NORTHEAST_1, "melodrama-endpoints");
  }

  public static BasicDBObject getGCPContainerWithPSCRegionGroupAndEndpointGroup(
      final int pSize, final GCPRegionName pRegionName, final String pEndpointGroupName) {
    return getGCPContainerWithPSCRegionGroupAndEndpointGroup(
        pSize, pRegionName, pEndpointGroupName, null);
  }

  public static BasicDBObject getGCPContainerWithPSCRegionGroupAndEndpointGroup(
      final int pSize,
      final GCPRegionName pRegionName,
      final String pEndpointGroupName,
      final Date pNeedsUpdateAfter) {
    final GCPCloudProviderContainer container =
        new GCPCloudProviderContainer(getGCPContainerWithProvisionedSubnets(List.of(pRegionName)));
    final BasicDBList regionGroups = new BasicDBList();
    regionGroups.add(
        getGCPPSCRegionGroupWithEndpointGroup(pSize, pRegionName, pEndpointGroupName)
            .toDBObject()
            .append("needsUpdateAfter", pNeedsUpdateAfter));
    return container
        .toDBObject()
        .append(GCPCloudProviderContainer.FieldDefs.PSC_REGION_GROUPS, regionGroups);
  }

  public static BasicDBObject getGCPContainerWithActivePSCRegionGroupAndEndpointGroup(
      final int pSize, final GCPRegionName pPSCRegionName, final String pEndpointGroupName) {
    final GCPCloudProviderContainer container =
        new GCPCloudProviderContainer(
            getGCPContainerWithProvisionedSubnets(List.of(pPSCRegionName)));
    final BasicDBList regionGroups = new BasicDBList();
    regionGroups.add(
        getGCPPSCRegionGroupWithActiveEndpointGroup(pSize, pPSCRegionName, pEndpointGroupName)
            .toDBObject());
    return container
        .toDBObject()
        .append(GCPCloudProviderContainer.FieldDefs.PSC_REGION_GROUPS, regionGroups);
  }

  public static BasicDBObject getGCPContainerWithActivePSCRegionGroupAndEndpointGroups(
      final int pNumForwardingRules,
      final int pNumEndpointGroups,
      final GCPRegionName pPSCRegionName,
      final String pEndpointGroupBaseName) {
    final GCPCloudProviderContainer container =
        new GCPCloudProviderContainer(
            getGCPContainerWithProvisionedSubnets(List.of(pPSCRegionName)));
    final BasicDBList regionGroups = new BasicDBList();
    regionGroups.add(
        getGCPPSCRegionGroupWithActiveEndpointGroups(
                pNumForwardingRules, pNumEndpointGroups, pPSCRegionName, pEndpointGroupBaseName)
            .toDBObject());
    return container
        .toDBObject()
        .append(GCPCloudProviderContainer.FieldDefs.PSC_REGION_GROUPS, regionGroups);
  }

  public static BasicDBObject getGCPContainerWithActivePSCRegionGroupAndEndpointGroup(
      final int pSize, final List<GCPRegionName> pPSCRegions, final String pEndpointGroupName) {
    final GCPCloudProviderContainer container =
        new GCPCloudProviderContainer(getGCPContainerWithProvisionedSubnets(pPSCRegions));
    final BasicDBList regionGroups = new BasicDBList();
    for (final GCPRegionName pscRegionName : pPSCRegions) {
      regionGroups.add(
          getGCPPSCRegionGroupWithActiveEndpointGroup(pSize, pscRegionName, pEndpointGroupName)
              .toDBObject());
    }

    return container
        .toDBObject()
        .append(GCPCloudProviderContainer.FieldDefs.PSC_REGION_GROUPS, regionGroups);
  }

  public static BasicDBObject getGCPContainerWithEndpointServices(
      final List<GCPPrivateServiceConnectRegionGroup> pRegionGroups, final ObjectId pContainerId) {
    final GCPCloudProviderContainer container =
        new GCPCloudProviderContainer(
            getGCPContainerWithProvisionedSubnets(
                pRegionGroups.stream()
                    .map(GCPPrivateServiceConnectRegionGroup::getRegionName)
                    .map(Optional::get)
                    .collect(Collectors.toList())));
    final BasicDBList regionGroups = new BasicDBList();
    pRegionGroups.forEach(regionGroup -> regionGroups.add(regionGroup.toDBObject()));
    return container
        .toDBObject()
        .append("id", pContainerId)
        .append(GCPCloudProviderContainer.FieldDefs.PSC_REGION_GROUPS, regionGroups);
  }

  public static BasicDBObject getAWSContainer() {
    return getAWSContainer(AWSRegionName.US_EAST_1);
  }

  public static BasicDBObject getAWSContainer(final AWSRegionName pRegionName) {
    return getAWSContainer(pRegionName, new ObjectId());
  }

  public static BasicDBObject getAWSContainer(final ObjectId pAWSAccountId) {
    return getAWSContainer(AWSRegionName.US_EAST_1, pAWSAccountId);
  }

  public static BasicDBObject getAWSContainer(
      final AWSRegionName pRegionName, final ObjectId pAWSAccountId) {

    final BasicDBList subnets = new BasicDBList();
    final List<AWSPhysicalZoneId> zoneIds =
        getAWSZoneIdMap()
            .getOrDefault(
                pRegionName,
                List.of(
                    new AWSPhysicalZoneId("zoneId1"),
                    new AWSPhysicalZoneId("zoneId2"),
                    new AWSPhysicalZoneId("zoneId3")));
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-1234")
            .availabilityZone("zone-1")
            .zoneId(zoneIds.get(0))
            .build()
            .toDBObject());
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-5678")
            .availabilityZone("zone-2")
            .zoneId(zoneIds.get(1))
            .build()
            .toDBObject());
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-9012")
            .availabilityZone("zone-3")
            .zoneId(zoneIds.get(2))
            .build()
            .toDBObject());

    return new BasicDBObject()
        .append("id", new ObjectId())
        .append("providerName", CloudProvider.AWS.name())
        .append("awsAccountId", pAWSAccountId)
        .append("region", pRegionName.name())
        .append("vpcId", "vpc-123")
        .append("igwId", "igw-456")
        .append("subnets", subnets)
        .append("atlasCidr", "172.31.248.0/21")
        .append(
            "networkPermissionList",
            new NDSNetworkPermissionList(
                    NDSNetworkPermission.getPermissionsFromStrings("1.2.3.4/32", "0.0.0.0/0"))
                .toDBObject())
        .append("peers", new BasicDBList());
  }

  private static Map<AWSRegionName, List<AWSPhysicalZoneId>> getAWSZoneIdMap() {
    final Map<AWSRegionName, List<AWSPhysicalZoneId>> zoneIdMap = new HashMap<>();
    zoneIdMap.put(
        AWSRegionName.US_EAST_1,
        List.of(
            new AWSPhysicalZoneId("use1-az1"),
            new AWSPhysicalZoneId("use1-az2"),
            new AWSPhysicalZoneId("use1-az3"),
            new AWSPhysicalZoneId("use1-az4"),
            new AWSPhysicalZoneId("use1-az5")));
    zoneIdMap.put(
        AWSRegionName.US_EAST_2,
        List.of(
            new AWSPhysicalZoneId("use2-az1"),
            new AWSPhysicalZoneId("use2-az2"),
            new AWSPhysicalZoneId("use2-az3")));
    return zoneIdMap;
  }

  public static BasicDBObject getAWSContainerWithPeers() {
    final BasicDBList subnets = new BasicDBList();
    final ObjectId containerId = new ObjectId();
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-1234")
            .availabilityZone("zone-1")
            .zoneId(new AWSPhysicalZoneId("zoneId1"))
            .build()
            .toDBObject());
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-5678")
            .availabilityZone("zone-2")
            .zoneId(new AWSPhysicalZoneId("zoneId2"))
            .build()
            .toDBObject());
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-9012")
            .availabilityZone("zone-3")
            .zoneId(new AWSPhysicalZoneId("zoneId3"))
            .build()
            .toDBObject());

    final BasicDBList peers = new BasicDBList();
    peers.add(getAWSPeerVpc(containerId).toDbObject());

    return new BasicDBObject()
        .append("id", containerId)
        .append("providerName", CloudProvider.AWS.name())
        .append("awsAccountId", new ObjectId())
        .append("region", AWSRegionName.US_EAST_1.name())
        .append("vpcId", "vpc-123")
        .append("igwId", "igw-456")
        .append("subnets", subnets)
        .append("peers", peers)
        .append(
            "networkPermissionList",
            new NDSNetworkPermissionList(
                    NDSNetworkPermission.getPermissionsFromStrings("1.2.3.4/32", "0.0.0.0/0"))
                .toDBObject());
  }

  public static BasicDBObject getAWSContainerWithPrivateLink(final AWSRegionName pAWSRegionName) {
    final BasicDBObject privateLinkObject = new AWSPrivateLinkConnection().toDBObject();
    privateLinkObject.append("needsUpdateAfter", new Date());

    final BasicDBList subnets = new BasicDBList();
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-1234")
            .availabilityZone("zone-1")
            .zoneId(new AWSPhysicalZoneId("zoneId1"))
            .build()
            .toDBObject());
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-5678")
            .availabilityZone("zone-2")
            .zoneId(new AWSPhysicalZoneId("zoneId2"))
            .build()
            .toDBObject());
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-9012")
            .availabilityZone("zone-3")
            .zoneId(new AWSPhysicalZoneId("zoneId3"))
            .build()
            .toDBObject());

    return new BasicDBObject()
        .append("id", new ObjectId())
        .append("providerName", CloudProvider.AWS.name())
        .append("awsAccountId", new ObjectId())
        .append("region", pAWSRegionName.name())
        .append("vpcId", "vpc-123")
        .append("igwId", "igw-456")
        .append("subnets", subnets)
        .append("peers", new BasicDBList())
        .append(
            "networkPermissionList",
            new NDSNetworkPermissionList(
                    NDSNetworkPermission.getPermissionsFromStrings("1.2.3.4/32", "0.0.0.0/0"))
                .toDBObject())
        .append(AWSCloudProviderContainer.FieldDefs.PRIVATE_LINK_CONNECTION, privateLinkObject);
  }

  public static BasicDBObject getAWSContainerWithFullyProvisionedPrivateLink(
      final BasicDBList pInterfaceEndpoints) {
    return getAWSContainerWithFullyProvisionedPrivateLink(
        pInterfaceEndpoints, AWSRegionName.US_EAST_1, new ObjectId());
  }

  public static BasicDBObject getAWSContainerWithFullyProvisionedPrivateLink(
      final BasicDBList pInterfaceEndpoints, final AWSRegionName pAWSRegionName) {
    return getAWSContainerWithFullyProvisionedPrivateLink(
        pInterfaceEndpoints, pAWSRegionName, new ObjectId());
  }

  public static BasicDBObject getAWSContainerWithFullyProvisionedPrivateLink(
      final BasicDBList pInterfaceEndpoints,
      final AWSRegionName pAWSRegionName,
      final ObjectId pContainerId) {
    final BasicDBObject privateLinkObject =
        new AWSPrivateLinkConnection().toDBObject().append("loadBalancerArn", "load/balancer/arn");

    privateLinkObject.append("interfaceEndpoints", pInterfaceEndpoints);

    final BasicDBList subnets = new BasicDBList();
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-1234")
            .availabilityZone("zone-1")
            .zoneId(new AWSPhysicalZoneId("zoneId1"))
            .build()
            .toDBObject());
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-5678")
            .availabilityZone("zone-2")
            .zoneId(new AWSPhysicalZoneId("zoneId2"))
            .build()
            .toDBObject());
    subnets.add(
        AWSSubnet.builder()
            .subnetId("subnet-9012")
            .availabilityZone("zone-3")
            .zoneId(new AWSPhysicalZoneId("zoneId3"))
            .build()
            .toDBObject());

    return new BasicDBObject()
        .append("id", pContainerId)
        .append("providerName", CloudProvider.AWS.name())
        .append("awsAccountId", new ObjectId())
        .append("region", pAWSRegionName.name())
        .append("vpcId", "vpc-123")
        .append("igwId", "igw-456")
        .append("subnets", subnets)
        .append("peers", new BasicDBList())
        .append(AWSCloudProviderContainer.FieldDefs.PRIVATE_LINK_CONNECTION, privateLinkObject);
  }

  public static List<AWSCloudProviderContainer> getAWSCrossRegionPrivateLinkContainers() {
    return getAWSCrossRegionPrivateLinkContainers(
        TEST_AWS_EAST_PRIVATE_ENDPOINT_ID_NAME, TEST_AWS_WEST_PRIVATE_ENDPOINT_ID_NAME);
  }

  public static List<AWSCloudProviderContainer> getAWSCrossRegionPrivateLinkContainers(
      final String pEastEndpointIDName, final String pWestEndpointIDName) {
    return getAWSCrossRegionPrivateLinkContainers(
        pEastEndpointIDName,
        ConnectionStatus.AVAILABLE,
        pWestEndpointIDName,
        ConnectionStatus.AVAILABLE);
  }

  public static List<AWSCloudProviderContainer> getAWSCrossRegionPrivateLinkContainers(
      final String pEastEndpointIDName,
      final ConnectionStatus pEastEndpointStatus,
      final String pWestEndpointIDName,
      final ConnectionStatus pWestEndpointStatus) {
    final AWSCloudProviderContainer containerUsEast =
        new AWSCloudProviderContainer(
            NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
                DbUtils.toBasicDBList(
                    new AWSPrivateLinkInterfaceEndpoint(
                            pEastEndpointIDName,
                            null,
                            "us.east.com",
                            pEastEndpointStatus,
                            null,
                            false,
                            0)
                        .toDBObject()),
                AWSRegionName.US_EAST_1,
                new ObjectId()));

    final AWSCloudProviderContainer containerUsWest =
        new AWSCloudProviderContainer(
            NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
                DbUtils.toBasicDBList(
                    new AWSPrivateLinkInterfaceEndpoint(
                            pWestEndpointIDName,
                            "us.west.com",
                            "us.west.com",
                            pWestEndpointStatus,
                            null,
                            false,
                            0)
                        .toDBObject()),
                AWSRegionName.US_WEST_1,
                new ObjectId()));

    return List.of(containerUsEast, containerUsWest);
  }

  public static List<AWSCloudProviderContainer>
      getAWSCrossRegionPrivateLinkContainers_multipleEndpoints() {
    final AWSCloudProviderContainer containerUsEast =
        new AWSCloudProviderContainer(
            NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
                DbUtils.toBasicDBList(
                    new AWSPrivateLinkInterfaceEndpoint(
                            "us-east-endpoint0",
                            null,
                            "us.east0.com",
                            AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                            null,
                            false,
                            0)
                        .toDBObject(),
                    new AWSPrivateLinkInterfaceEndpoint(
                            "us-east-endpoint1",
                            null,
                            "us.east1.com",
                            AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                            null,
                            false,
                            1)
                        .toDBObject()),
                AWSRegionName.US_EAST_1,
                new ObjectId()));

    final AWSCloudProviderContainer containerUsWest =
        new AWSCloudProviderContainer(
            NDSModelTestFactory.getAWSContainerWithFullyProvisionedPrivateLink(
                DbUtils.toBasicDBList(
                    new AWSPrivateLinkInterfaceEndpoint(
                            "us-west-endpoint0",
                            "us.west0.com",
                            "us.west0.com",
                            AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                            null,
                            false,
                            0)
                        .toDBObject(),
                    new AWSPrivateLinkInterfaceEndpoint(
                            "us-west-endpoint1",
                            "us.west1.com",
                            "us.west1.com",
                            AWSPrivateLinkInterfaceEndpoint.ConnectionStatus.AVAILABLE,
                            null,
                            false,
                            1)
                        .toDBObject()),
                AWSRegionName.US_WEST_1,
                new ObjectId()));

    return List.of(containerUsEast, containerUsWest);
  }

  public static BasicDBObject getAWSInterfaceEndpoint() {
    return new BasicDBObject("id", new ObjectId())
        .append("interfaceEndpointId", "endpoint-id-123")
        .append("interfaceEndpointDNSName", "endpointNameAws.com")
        .append("privateLinkAwareHostname", "privateLinkHostname.com")
        .append("privateLinkHostname", "privateLinkRevamped.com")
        .append("connectionStatus", ConnectionStatus.AVAILABLE.name())
        .append("index", 0)
        .append("errorMessage", null)
        .append("deleteRequested", false);
  }

  public static BasicDBObject getAWSPrivateLinkFullyProvisioned() {
    return getAWSPrivateLinkFullyProvisioned(DbUtils.toBasicDBList(getAWSInterfaceEndpoint()));
  }

  public static BasicDBObject getAWSPrivateLinkFullyProvisioned(
      final BasicDBList pInterfaceEndpoints) {

    return new AWSPrivateLinkConnection()
        .toDBObject()
        .append("loadBalancerArn", "load/balancer/arn")
        .append("interfaceEndpoints", pInterfaceEndpoints);
  }

  public static Stream<AWSCapacityDenyListEntry> getAWSCapacityDenyListEntries(
      final AWSRegionName pRegionName,
      final AWSNDSInstanceSize pInstanceSize,
      final AWSPhysicalZoneId pPhysicalZoneId,
      final CapacityDenyListEntry.Status pStatus,
      final Date pDate) {
    final Date now = new Date();
    return pInstanceSize.getAvailableFamilies().get(pRegionName).stream()
        .map(
            family ->
                new AWSCapacityDenyListEntry(
                    pRegionName,
                    pInstanceSize,
                    (AWSInstanceFamily) family,
                    pPhysicalZoneId,
                    pDate,
                    pStatus,
                    Optional.of("CLOUDP-12345"),
                    List.of(new CapacityDenylistStatBucket(now.toInstant(), 42, 13))));
  }

  public static AWSCapacityDenyListEntry getAWSCapacityDenyListEntry(
      final AWSRegionName pRegionName, final AWSNDSInstanceSize pInstanceSize) {
    final Date now = new Date();
    return new AWSCapacityDenyListEntry(
        pRegionName,
        pInstanceSize,
        (AWSInstanceFamily)
            Iterables.getLast(pInstanceSize.getAvailableFamilies().get(pRegionName)),
        new AWSPhysicalZoneId("fakeZoneId"),
        now,
        CapacityDenyListEntry.Status.CAPACITY_UNAVAILABLE_OVERRIDE,
        Optional.of("CLOUDP-12345"),
        List.of(new CapacityDenylistStatBucket(now.toInstant(), 42, 13)));
  }

  public static BasicDBObject getUnProvisionedAWSContainerWithPrivateLink() {
    final BasicDBObject privateLinkObject = new AWSPrivateLinkConnection().toDBObject();
    privateLinkObject.append("needsUpdateAfter", new Date());

    return new BasicDBObject()
        .append("id", new ObjectId())
        .append("providerName", CloudProvider.AWS.name())
        .append("awsAccountId", new ObjectId())
        .append("region", AWSRegionName.US_EAST_1.name())
        .append("vpcId", null)
        .append("igwId", null)
        .append("subnets", new BasicDBList())
        .append("peers", new BasicDBList())
        .append(
            "networkPermissionList",
            new NDSNetworkPermissionList(
                    NDSNetworkPermission.getPermissionsFromStrings("1.2.3.4/32", "0.0.0.0/0"))
                .toDBObject())
        .append(AWSCloudProviderContainer.FieldDefs.PRIVATE_LINK_CONNECTION, privateLinkObject);
  }

  public static BasicDBObject getUnProvisionedAWSContainerWithPeers() {
    final BasicDBList peers = new BasicDBList();
    final ObjectId containerId = new ObjectId();
    peers.add(new AWSPeerVpc(containerId, "vpc-1234", "AAAAAAA", "1.0.0.0/21").toDbObject());

    return new BasicDBObject()
        .append("id", containerId)
        .append("providerName", CloudProvider.AWS.name())
        .append("awsAccountId", new ObjectId())
        .append("region", AWSRegionName.US_EAST_1.name())
        .append("vpcId", null)
        .append("igwId", null)
        .append("subnets", new BasicDBList())
        .append("peers", peers)
        .append(
            "networkPermissionList",
            new NDSNetworkPermissionList(
                    NDSNetworkPermission.getPermissionsFromStrings("1.2.3.4/32", "0.0.0.0/0"))
                .toDBObject());
  }

  public static BasicDBObject getFreeContainer() {
    return getFreeContainer(true, true);
  }

  public static BasicDBObject getFreeContainer(
      final boolean pIsProvisioned, final boolean pIsAssigned) {
    return getTenantContainer(pIsProvisioned, pIsAssigned, CloudProvider.FREE);
  }

  public static BasicDBObject getFreeContainer(
      final ObjectId pGroupId, final String pBackingClusterName, final String pTenantClusterName) {
    return getTenantContainer(
        pGroupId, pBackingClusterName, pTenantClusterName, CloudProvider.FREE);
  }

  public static BasicDBObject getServerlessContainer() {
    return getServerlessContainer(true, true);
  }

  public static BasicDBObject getServerlessContainer(
      final boolean pIsProvisioned, final boolean pIsAssigned) {
    return getTenantContainer(pIsProvisioned, pIsAssigned, CloudProvider.SERVERLESS);
  }

  public static BasicDBObject getServerlessContainer(
      final ObjectId pGroupId, final String pBackingClusterName, final String pTenantClusterName) {
    return getTenantContainer(
        pGroupId, pBackingClusterName, pTenantClusterName, CloudProvider.SERVERLESS);
  }

  public static BasicDBObject getSharedContainer(
      final ObjectId pGroupId, final String pBackingClusterName, final String pTenantClusterName) {
    return getTenantContainer(
        pGroupId, pBackingClusterName, pTenantClusterName, CloudProvider.FREE);
  }

  public static BasicDBObject getFlexContainer() {
    return getFlexContainer(true, true);
  }

  public static BasicDBObject getFlexContainer(
      final boolean pIsProvisioned, final boolean pIsAssigned) {
    return getTenantContainer(pIsProvisioned, pIsAssigned, CloudProvider.FLEX);
  }

  public static BasicDBObject getFlexContainer(
      final ObjectId pGroupId, final String pBackingClusterName, final String pTenantClusterName) {
    return getTenantContainer(
        pGroupId, pBackingClusterName, pTenantClusterName, CloudProvider.FLEX);
  }

  public static BasicDBObject getTenantContainer(
      final ObjectId pGroupId,
      final String pBackingClusterName,
      final String pTenantClusterName,
      final CloudProvider pProvider) {
    final DBObject clusterId =
        new BasicDBObject().append("clusterName", pBackingClusterName).append("groupId", pGroupId);
    return new BasicDBObject()
        .append("id", new ObjectId())
        .append("providerName", pProvider)
        .append("clusterId", clusterId)
        .append("peers", new BasicDBList())
        .append("tenantClusterName", pTenantClusterName);
  }

  private static BasicDBObject getTenantContainer(
      final boolean pIsProvisioned, final boolean pIsAssigned, final CloudProvider pCloudProvider) {
    final DBObject clusterId =
        new BasicDBObject()
            .append("clusterName", pIsProvisioned ? "fakeMTM" : null)
            .append("groupId", pIsProvisioned ? ObjectId.get() : null);
    return new BasicDBObject()
        .append("id", new ObjectId())
        .append("providerName", pCloudProvider.name())
        .append("clusterId", clusterId)
        .append("peers", new BasicDBList())
        .append("tenantClusterName", pIsAssigned ? DEFAULT_CLUSTER_NAME : null);
  }

  public static NDSGroup getFreeMockedGroup() {
    return getFreeMockedGroup(new ObjectId());
  }

  public static NDSGroup getFreeMockedGroup(final ObjectId groupId) {
    final NDSGroup g = mock(NDSGroup.class, withSettings().lenient());
    when(g.getCloudProviderType(any())).thenReturn(CloudProvider.FREE);
    when(g.getGroupId()).thenReturn(groupId);
    when(g.getDNSPin()).thenReturn("abc12");
    when(g.getNetworkPermissionList())
        .thenReturn(
            new NDSNetworkPermissionList(
                NDSNetworkPermission.getPermissionsFromStrings("***************/32")));

    final FreeCloudProviderContainer cloudProviderContainer =
        mock(FreeCloudProviderContainer.class);
    when(g.getCloudProviderContainers())
        .thenReturn(Collections.singletonList(cloudProviderContainer));
    when(g.getCloudProviderContainer(eq(cloudProviderContainer.getId())))
        .thenReturn(Optional.of(cloudProviderContainer));

    final NDSUserSecurity ndsUserSecurity = getMockedUserSecurity();
    when(g.getUserSecurity()).thenReturn(ndsUserSecurity);

    final NDSEncryptionAtRest encryptionAtRest = getEmptyEncryptionAtRest();
    when(g.getEncryptionAtRest()).thenReturn(encryptionAtRest);

    final AuditLog auditLog = getMockedAuditLog();
    when(g.getAuditLog()).thenReturn(auditLog);

    return g;
  }

  public static NDSGroup getServerlessMockedGroup() {
    return getServerlessMockedGroup(new ObjectId());
  }

  public static NDSGroup getServerlessMockedGroup(final ObjectId groupId) {
    return getServerlessMockedGroup(groupId, Date.from(Instant.now().minus(Duration.ofHours(3L))));
  }

  public static NDSGroup getServerlessMockedGroup(
      final ObjectId groupId, final Date pLastSuccessfulMeterReportDate) {
    final NDSGroup g = mock(NDSGroup.class, withSettings().lenient());
    when(g.getCloudProviderType(any())).thenReturn(CloudProvider.SERVERLESS);
    when(g.getGroupId()).thenReturn(groupId);
    when(g.getNetworkPermissionList())
        .thenReturn(
            new NDSNetworkPermissionList(
                NDSNetworkPermission.getPermissionsFromStrings("***************/32")));

    final ServerlessCloudProviderContainer cloudProviderContainer =
        mock(ServerlessCloudProviderContainer.class);
    when(g.getCloudProviderContainers())
        .thenReturn(Collections.singletonList(cloudProviderContainer));
    when(g.getCloudProviderContainer(eq(cloudProviderContainer.getId())))
        .thenReturn(Optional.of(cloudProviderContainer));

    final NDSUserSecurity ndsUserSecurity = getMockedUserSecurity();
    when(g.getUserSecurity()).thenReturn(ndsUserSecurity);

    when(g.getLastSuccessfulMeterReportDate())
        .thenReturn(Optional.of(pLastSuccessfulMeterReportDate));

    final Date nextBillingMeterCheckDate = new Date();
    when(g.getNextBillingMeterCheckDate()).thenReturn(Optional.of(nextBillingMeterCheckDate));

    final NDSEncryptionAtRest encryptionAtRest = getEmptyEncryptionAtRest();
    when(g.getEncryptionAtRest()).thenReturn(encryptionAtRest);

    final AuditLog auditLog = getMockedAuditLog();
    when(g.getAuditLog()).thenReturn(auditLog);

    final Limits limits = mock(Limits.class, withSettings().lenient());
    final int clusterNameUniquePrefixLength = 23;
    doReturn(clusterNameUniquePrefixLength).when(limits).getClusterNameUniquePrefixLength();
    when(g.getLimits()).thenReturn(limits);

    final String dnsPin = "abc12";
    when(g.getDNSPin()).thenReturn(dnsPin);

    when(g.getCreateDate()).thenReturn(pLastSuccessfulMeterReportDate);

    return g;
  }

  public static NDSGroup getFlexMockedGroup() {
    return getFlexMockedGroup(new ObjectId());
  }

  public static NDSGroup getFlexMockedGroup(final ObjectId groupId) {
    final NDSGroup g = mock(NDSGroup.class, withSettings().lenient());
    when(g.getCloudProviderType(any())).thenReturn(CloudProvider.FLEX);
    when(g.getGroupId()).thenReturn(groupId);
    when(g.getDNSPin()).thenReturn("abc12");
    when(g.getNetworkPermissionList())
        .thenReturn(
            new NDSNetworkPermissionList(
                NDSNetworkPermission.getPermissionsFromStrings("***************/32")));

    final FlexCloudProviderContainer cloudProviderContainer =
        mock(FlexCloudProviderContainer.class);
    when(g.getCloudProviderContainers())
        .thenReturn(Collections.singletonList(cloudProviderContainer));
    when(g.getCloudProviderContainer(eq(cloudProviderContainer.getId())))
        .thenReturn(Optional.of(cloudProviderContainer));

    final NDSUserSecurity ndsUserSecurity = getMockedUserSecurity();
    when(g.getUserSecurity()).thenReturn(ndsUserSecurity);

    final NDSEncryptionAtRest encryptionAtRest = getEmptyEncryptionAtRest();
    when(g.getEncryptionAtRest()).thenReturn(encryptionAtRest);

    final AuditLog auditLog = getMockedAuditLog();
    when(g.getAuditLog()).thenReturn(auditLog);

    return g;
  }

  public static BasicDBObject getNDSGroupEmpty() {
    return new BasicDBObject()
        .append("_id", new ObjectId())
        .append("tseSSHKey", new BasicDBObject())
        .append("toorSSHKey", new BasicDBObject())
        .append("state", NDSGroup.NDSState.IDLE.name())
        .append("createDate", new Date())
        .append("nextPlanningDate", new Date())
        .append("cloudProviderContainers", new BasicDBList())
        .append("throttleState", ThrottleState.INHERIT.name())
        .append("dnsPin", "abc12")
        .append("networkPermissionList", new NDSNetworkPermissionList().toDBObject())
        .append(
            "privateNetworkSettings",
            new NDSPrivateNetworkSettings(new BasicDBObject()).toDBObject())
        .append("users", new BasicDBList())
        .append("userSecurity", getDefaultUserSecurity())
        .append("auditLog", new AuditLog().toDBObject())
        .append("encryptionAtRest", getDefaultEncryptionAtRest())
        .append("cloudProviderAccess", getDefaultCloudProviderAccess())
        .append("nextBillingMeterCheckDate", new Date())
        .append("lastSuccessfulMeterReportDate", new Date())
        .append("pushBasedLogExport", new PushBasedLogExport().toDBObject())
        .append("healthCheckMetadata", new HealthCheckMetadata(new Date()).toDBObject())
        .append("ftdcExport", new FTDCExport().toDBObject());
  }

  public static SecureSSHKey getSecureSSHKey() {
    return new SecureSSHKey(new BasicDBObject());
  }

  public static BasicDBObject getSharedMTMCluster() {
    return getSharedMTMCluster(
        DEFAULT_CLUSTER_NAME,
        new ObjectId(),
        FreeNDSDefaults.INSTANCE_SIZE,
        AWSRegionName.US_EAST_1,
        TEST_FREE_MONGODB_MAJOR_VERSION);
  }

  public static BasicDBObject getSharedMTMCluster(final String pName) {
    return getSharedMTMCluster(
        pName,
        new ObjectId(),
        FreeNDSDefaults.INSTANCE_SIZE,
        AWSRegionName.US_EAST_1,
        TEST_FREE_MONGODB_MAJOR_VERSION);
  }

  public static BasicDBObject getSharedMTMCluster(final ObjectId pGroupId, final String pName) {
    return getSharedMTMCluster(
        pName,
        pGroupId,
        FreeNDSDefaults.INSTANCE_SIZE,
        AWSRegionName.US_EAST_1,
        TEST_FREE_MONGODB_MAJOR_VERSION);
  }

  public static BasicDBObject getSharedMTMCluster(
      final ObjectId pGroupId, final String pName, final String pMajorVersion) {
    return getSharedMTMCluster(
        pName, pGroupId, FreeNDSDefaults.INSTANCE_SIZE, AWSRegionName.US_EAST_1, pMajorVersion);
  }

  public static BasicDBObject getSharedMTMCluster(final FreeInstanceSize pInstanceSize) {
    return getSharedMTMCluster(
        DEFAULT_CLUSTER_NAME,
        new ObjectId(),
        pInstanceSize,
        AWSRegionName.US_EAST_1,
        TEST_FREE_MONGODB_MAJOR_VERSION);
  }

  public static BasicDBObject getSharedMTMCluster(
      final String pName,
      final FreeInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion) {
    return getSharedMTMCluster(
        pName, new ObjectId(), pInstanceSize, pRegionName, pMongoDBMajorVersion);
  }

  public static BasicDBObject getSharedMTMCluster(
      final String pName,
      final FreeInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion,
      final Set<ObjectId> pIsolationGroupIds) {
    return getSharedMTMCluster(
        pName,
        new ObjectId(),
        pInstanceSize,
        pRegionName,
        pMongoDBMajorVersion,
        pIsolationGroupIds);
  }

  public static BasicDBObject getSharedMTMCluster(
      final String pClusterName,
      final ObjectId pGroupId,
      final FreeInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion) {
    return getSharedMTMCluster(
        pClusterName,
        pGroupId,
        pInstanceSize,
        pRegionName,
        pMongoDBMajorVersion,
        Collections.emptySet());
  }

  public static BasicDBObject getSharedMTMCluster(
      final String pClusterName,
      final ObjectId pGroupId,
      final FreeInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion,
      final Set<ObjectId> pIsolationGroupIds) {
    return getTenantMTMCluster(
        pClusterName,
        pGroupId,
        MTMCluster.MTMClusterType.SHARED,
        pInstanceSize,
        pRegionName,
        pMongoDBMajorVersion,
        pIsolationGroupIds);
  }

  public static BasicDBObject getFlexMTMCluster() {
    return getFlexMTMCluster(
        DEFAULT_CLUSTER_NAME,
        new ObjectId(),
        FlexInstanceSize.FLEX,
        AWSRegionName.US_EAST_1,
        TEST_FLEX_MONGODB_MAJOR_VERSION);
  }

  public static BasicDBObject getFlexMTMCluster(final String pName) {
    return getFlexMTMCluster(
        pName,
        new ObjectId(),
        FlexInstanceSize.FLEX,
        AWSRegionName.US_EAST_1,
        TEST_FLEX_MONGODB_MAJOR_VERSION);
  }

  public static BasicDBObject getFlexMTMCluster(
      final String pName,
      final FlexInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion,
      final Set<ObjectId> pIsolationGroupIds) {
    return getFlexMTMCluster(
        pName,
        new ObjectId(),
        pInstanceSize,
        pRegionName,
        pMongoDBMajorVersion,
        pIsolationGroupIds);
  }

  public static BasicDBObject getFlexMTMCluster(
      final String pName,
      final FlexInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion) {
    return getFlexMTMCluster(
        pName, new ObjectId(), pInstanceSize, pRegionName, pMongoDBMajorVersion);
  }

  public static BasicDBObject getFlexMTMCluster(
      final String pClusterName,
      final ObjectId pGroupId,
      final FlexInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion) {
    return getFlexMTMCluster(
        pClusterName,
        pGroupId,
        pInstanceSize,
        pRegionName,
        pMongoDBMajorVersion,
        Collections.emptySet());
  }

  public static BasicDBObject getFlexMTMCluster(
      final String pClusterName, final ObjectId pGroupId) {
    return getFlexMTMCluster(
        pClusterName,
        pGroupId,
        FlexInstanceSize.FLEX,
        AWSRegionName.US_EAST_1,
        TEST_FLEX_MONGODB_MAJOR_VERSION,
        Collections.emptySet());
  }

  public static BasicDBObject getFlexMTMCluster(
      final String pClusterName,
      final ObjectId pGroupId,
      final FlexInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion,
      final Set<ObjectId> pIsolationGroupIds) {
    return getTenantMTMCluster(
        pClusterName,
        pGroupId,
        MTMClusterType.FLEX,
        pInstanceSize,
        pRegionName,
        pMongoDBMajorVersion,
        pIsolationGroupIds);
  }

  public static BasicDBObject getServerlessMTMClusterDescriptionByCloudProvider(
      final CloudProvider pCloudProvider,
      final ObjectId pMTMGroupId,
      final String pMTMClusterName) {

    switch (pCloudProvider) {
      case AWS:
        return NDSModelTestFactory.getAWSClusterDescription(pMTMGroupId, pMTMClusterName);
      case AZURE:
        return NDSModelTestFactory.getAzureClusterDescription(pMTMGroupId, pMTMClusterName);
      case GCP:
        return NDSModelTestFactory.getGCPClusterDescription(pMTMGroupId, pMTMClusterName);
      default:
        throw new IllegalArgumentException(
            String.format("Cloud provider not supported: %s", pCloudProvider));
    }
  }

  public static BasicDBObject getServerlessMTMClusterByCloudProvider(
      final CloudProvider pCloudProvider,
      final ObjectId pMTMGroupId,
      final String pMTMClusterName) {
    return getServerlessMTMCluster(
        pMTMGroupId,
        ServerlessNDSDefaults.INSTANCE_SIZE,
        getDefaultRegionNameForCloudProvider(pCloudProvider),
        TEST_SERVERLESS_MONGODB_MAJOR_VERSION,
        Collections.emptySet(),
        new Date(),
        null,
        null,
        pMTMClusterName,
        null,
        null);
  }

  public static RegionName getDefaultRegionNameForCloudProvider(
      final CloudProvider pCloudProvider) {
    switch (pCloudProvider) {
      case AWS:
        return AWSRegionName.US_EAST_1;
      case AZURE:
        return AzureRegionName.US_EAST_2;
      case GCP:
        return GCPRegionName.EASTERN_US;
      default:
        throw new IllegalArgumentException(
            String.format("Cloud provider not supported: %s", pCloudProvider));
    }
  }

  public static BasicDBObject getServerlessMTMCluster() {
    return getServerlessMTMCluster(
        DEFAULT_CLUSTER_NAME,
        new ObjectId(),
        ServerlessNDSDefaults.INSTANCE_SIZE,
        AWSRegionName.US_EAST_1,
        TEST_SERVERLESS_MONGODB_MAJOR_VERSION);
  }

  public static BasicDBObject getServerlessMTMCluster(final String pName) {
    return getServerlessMTMCluster(
        pName,
        new ObjectId(),
        ServerlessNDSDefaults.INSTANCE_SIZE,
        AWSRegionName.US_EAST_1,
        TEST_SERVERLESS_MONGODB_MAJOR_VERSION);
  }

  public static BasicDBObject getServerlessMTMCluster(final ObjectId pGroupId, final String pName) {
    return getServerlessMTMCluster(pGroupId, pName, null);
  }

  public static BasicDBObject getServerlessMTMCluster(
      final ObjectId pGroupId, final String pName, final ObjectId pPoolId) {
    return getServerlessMTMCluster(
        pGroupId,
        ServerlessNDSDefaults.INSTANCE_SIZE,
        AWSRegionName.US_EAST_1,
        TEST_SERVERLESS_MONGODB_MAJOR_VERSION,
        Collections.emptySet(),
        new Date(),
        null,
        pPoolId,
        pName,
        null,
        null);
  }

  public static BasicDBObject getServerlessMTMCluster(final ServerlessInstanceSize pInstanceSize) {
    return getServerlessMTMCluster(
        DEFAULT_CLUSTER_NAME,
        new ObjectId(),
        pInstanceSize,
        AWSRegionName.US_EAST_1,
        TEST_SERVERLESS_MONGODB_MAJOR_VERSION);
  }

  public static BasicDBObject getServerlessMTMCluster(
      final String pName,
      final ServerlessInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion) {
    return getServerlessMTMCluster(
        pName, new ObjectId(), pInstanceSize, pRegionName, pMongoDBMajorVersion);
  }

  public static BasicDBObject getServerlessMTMCluster(
      final String pName,
      final ServerlessInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion,
      final Set<ObjectId> pIsolationGroupIds,
      final Date pNextLoadCheckDate,
      final Date pLastLoadCheckDate) {
    return getServerlessMTMCluster(
        new ObjectId(),
        pInstanceSize,
        pRegionName,
        pMongoDBMajorVersion,
        pIsolationGroupIds,
        pNextLoadCheckDate,
        pLastLoadCheckDate,
        null,
        pName,
        null,
        null);
  }

  public static BasicDBObject getServerlessMTMCluster(
      final String pClusterName,
      final ObjectId pGroupId,
      final ServerlessInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion) {
    return getServerlessMTMCluster(
        pGroupId,
        pInstanceSize,
        pRegionName,
        pMongoDBMajorVersion,
        Collections.emptySet(),
        new Date(),
        null,
        null,
        pClusterName,
        null,
        null);
  }

  public static BasicDBObject getServerlessMTMCluster(
      final ObjectId pGroupId,
      final ServerlessInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion,
      final Set<ObjectId> pIsolationGroupIds,
      final Date pNextLoadCheckDate,
      final Date pLastLoadCheckDate,
      final ObjectId pPoolId,
      final String pClusterName,
      final Date pVersionUpdateWindowStartDate,
      final Date pVersionUpgradeWindowEndDate) {
    return getTenantMTMCluster(
            pClusterName,
            pGroupId,
            MTMCluster.MTMClusterType.SERVERLESS,
            pInstanceSize,
            pRegionName,
            pMongoDBMajorVersion,
            pIsolationGroupIds)
        .append(ServerlessMTMCluster.FieldDefs.NEXT_LOAD_CHECK_DATE, pNextLoadCheckDate)
        .append(ServerlessMTMCluster.FieldDefs.LAST_LOAD_CHECK_DATE, pLastLoadCheckDate)
        .append(ServerlessMTMCluster.FieldDefs.POOL_ID, pPoolId)
        .append(
            ServerlessMTMCluster.FieldDefs.VERSION_UPGRADE_WINDOW_START_DATE,
            pVersionUpdateWindowStartDate)
        .append(
            ServerlessMTMCluster.FieldDefs.VERSION_UPGRADE_WINDOW_END_DATE,
            pVersionUpgradeWindowEndDate)
        .append(
            ServerlessMTMCluster.FieldDefs.CURSOR_ID_RANGE, getRandomCursorRange().toDBObject());
  }

  public static BasicDBObject getTenantMTMCluster(
      final String pClusterName,
      final ObjectId pGroupId,
      final MTMCluster.MTMClusterType pMTMType,
      final InstanceSize pInstanceSize,
      final RegionName pRegionName,
      final String pMongoDBMajorVersion,
      final Set<ObjectId> pIsolationGroupIds) {
    return new BasicDBObject()
        .append(
            MTMCluster.FieldDefs.ID,
            new BasicDBObject()
                .append(MTMCluster.FieldDefs.NAME, pClusterName)
                .append(MTMCluster.FieldDefs.GROUP_ID, pGroupId)
                .append(MTMCluster.FieldDefs.MTM_TYPE, pMTMType.name()))
        .append(MTMCluster.FieldDefs.CREATED, new Date())
        .append(MTMCluster.FieldDefs.LAST_UPDATED, new Date())
        .append(MTMCluster.FieldDefs.ASSIGNMENT_ENABLED, true)
        .append(MTMCluster.FieldDefs.CAPACITY_REMAINING, 1000)
        .append(MTMCluster.FieldDefs.MAX_CAPACITY, 1000)
        .append(MTMCluster.FieldDefs.LINKED_CONTAINER_IDS, new BasicDBList())
        .append(MTMCluster.FieldDefs.PROXY_VERSIONS, new BasicDBObject())
        .append(MTMCluster.FieldDefs.PROVIDER_NAME, pRegionName.getProvider().name())
        .append(MTMCluster.FieldDefs.REGION, pRegionName.getName())
        .append(MTMCluster.FieldDefs.TENANT_INSTANCE_SIZE, pInstanceSize.name())
        .append(MTMCluster.FieldDefs.MONGODB_MAJOR_VERSION, pMongoDBMajorVersion)
        .append(
            MTMCluster.FieldDefs.ISOLATION_GROUP_IDS,
            pIsolationGroupIds.stream().collect(DbUtils.toBasicDBList()))
        .append(MTMCluster.FieldDefs.MTM_CLUSTER_DESCRIPTION_UNIQUE_ID, new ObjectId())
        .append(MTMCluster.FieldDefs.SENTINEL, new MTMSentinel().toDBObject());
  }

  public static BasicDBObject getAutoScalingEnabled() {
    return new BasicDBObject()
        .append(AutoScaling.FieldDefs.DISK_GB, getDiskGBAutoScaling(true))
        .append(
            AutoScaling.FieldDefs.COMPUTE,
            getComputeAutoScaling(true, true, true, AWSNDSInstanceSize.M10, AWSNDSInstanceSize.M50))
        .append(AutoScaling.FieldDefs.AUTO_INDEXING, getAutoIndexing(true));
  }

  public static BasicDBObject getDefaultAutoScaling() {
    return new BasicDBObject()
        .append(AutoScaling.FieldDefs.DISK_GB, getDiskGBAutoScaling(false))
        .append(AutoScaling.FieldDefs.COMPUTE, getDisabledComputeAutoScaling())
        .append(AutoScaling.FieldDefs.AUTO_INDEXING, getAutoIndexing(false));
  }

  public static BasicDBObject getAutoScaling(final boolean enabled) {
    return enabled ? getAutoScalingEnabled() : getDefaultAutoScaling();
  }

  public static BasicDBObject getDiskGBAutoScaling(final boolean enabled) {
    return new BasicDBObject(DiskGBAutoScaling.FieldDefs.ENABLED, enabled);
  }

  public static BasicDBObject getAutoIndexing(final boolean enabled) {
    return new BasicDBObject(AutoIndexing.FieldDefs.ENABLED, enabled);
  }

  public static BasicDBObject getDisabledComputeAutoScaling() {
    return new BasicDBObject()
        .append(ComputeAutoScaling.FieldDefs.ENABLED, false)
        .append(ComputeAutoScaling.FieldDefs.SCALE_DOWN_ENABLED, false)
        .append(ComputeAutoScaling.FieldDefs.PREDICTIVE_ENABLED, false)
        .append(ComputeAutoScaling.FieldDefs.MIN_INSTANCE_SIZE, null)
        .append(ComputeAutoScaling.FieldDefs.MAX_INSTANCE_SIZE, null);
  }

  public static BasicDBObject getComputeAutoScaling(
      final boolean enabled,
      final boolean downScaleEnabled,
      final boolean predictiveEnabled,
      final NDSInstanceSize minInstanceSize,
      final NDSInstanceSize maxInstanceSize) {
    if (!enabled) {
      return getDisabledComputeAutoScaling();
    }
    return new BasicDBObject()
        .append(ComputeAutoScaling.FieldDefs.ENABLED, enabled)
        .append(ComputeAutoScaling.FieldDefs.SCALE_DOWN_ENABLED, downScaleEnabled)
        .append(ComputeAutoScaling.FieldDefs.PREDICTIVE_ENABLED, predictiveEnabled)
        .append(ComputeAutoScaling.FieldDefs.MIN_INSTANCE_SIZE, minInstanceSize.name())
        .append(ComputeAutoScaling.FieldDefs.MAX_INSTANCE_SIZE, maxInstanceSize.name());
  }

  public static BasicDBObject getDefaultFreeComputeAutoScaling() {
    return new BasicDBObject()
        .append(ComputeAutoScaling.FieldDefs.ENABLED, false)
        .append(ComputeAutoScaling.FieldDefs.SCALE_DOWN_ENABLED, false)
        .append(ComputeAutoScaling.FieldDefs.PREDICTIVE_ENABLED, false)
        .append(ComputeAutoScaling.FieldDefs.MIN_INSTANCE_SIZE, null)
        .append(ComputeAutoScaling.FieldDefs.MAX_INSTANCE_SIZE, null);
  }

  public static BasicDBObject getDefaultServerlessComputeAutoScaling() {
    return new BasicDBObject()
        .append(ComputeAutoScaling.FieldDefs.ENABLED, false)
        .append(ComputeAutoScaling.FieldDefs.SCALE_DOWN_ENABLED, false)
        .append(ComputeAutoScaling.FieldDefs.PREDICTIVE_ENABLED, false)
        .append(ComputeAutoScaling.FieldDefs.MIN_INSTANCE_SIZE, null)
        .append(ComputeAutoScaling.FieldDefs.MAX_INSTANCE_SIZE, null);
  }

  public static BasicDBObject getDefaultBiConnector() {
    return getBiConnector(false);
  }

  public static BasicDBObject getBiConnector(final boolean pEnabled) {
    return getBiConnector(pEnabled, BiConnectorReadPreference.SECONDARY.getValue());
  }

  public static BasicDBObject getBiConnector(final BiConnectorReadPreference pReadPreference) {
    return getBiConnector(true, pReadPreference.getValue());
  }

  private static BasicDBObject getBiConnector(
      final boolean pEnabled, final String pReadPreference) {
    return new BasicDBObject()
        .append(ClusterDescription.BiConnector.FieldDefs.ENABLED, pEnabled)
        .append(ClusterDescription.BiConnector.FieldDefs.READ_PREFERENCE, pReadPreference)
        .append(ClusterDescription.BiConnector.FieldDefs.LAST_DISABLED_DATE, null)
        .append(ClusterDescription.BiConnector.FieldDefs.NEEDS_SYNC, null)
        .append(ClusterDescription.BiConnector.FieldDefs.HOSTNAMES, new BasicDBList());
  }

  public static BasicDBList getDefaultLabelsDBList() {
    final BasicDBList defaultLabels = new BasicDBList();
    defaultLabels.add(
        new BasicDBObject(NDSLabel.FieldDefs.KEY, "foo").append(NDSLabel.FieldDefs.VALUE, "bar"));
    return defaultLabels;
  }

  public static List<NDSLabel> getDefaultLabels() {
    return Collections.singletonList(new NDSLabel("foo", "bar"));
  }

  public static List<NDSLabelView> getDefaultLabelViews() {
    return Collections.singletonList(new NDSLabelView("foo", "bar"));
  }

  public static BasicDBObject getDefaultGeoSharding() {
    return new BasicDBObject()
        .append(GeoSharding.FieldDefs.MANAGED_NAMESPACES, new BasicDBList())
        .append(GeoSharding.FieldDefs.CUSTOM_ZONE_MAPPING, new BasicDBObject());
  }

  public static BackupJob.BackupJobBuilder getBackupJobBuilder() {
    final Date currentDate = new Date();
    final DBObject item =
        new BasicDBObject()
            .append(PolicyItem.FieldDefs.ID, new ObjectId())
            .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.DAILY.name())
            .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, 1)
            .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, currentDate)
            .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, Duration.ofDays(3).toMillis())
            .append(PolicyItem.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.DAYS.name());
    final BasicDBObject policy =
        new BasicDBObject(Policy.FieldDefs.POLICY_ITEMS, List.of(item))
            .append(Policy.FieldDefs.ID, new ObjectId());

    final List<Policy> policies = List.of(new Policy(policy));

    return BackupJob.builder()
        .id(new ObjectId())
        .projectId(new ObjectId())
        .clusterName(DEFAULT_CLUSTER_NAME)
        .clusterUniqueId(new ObjectId())
        .clusterType(BackupJob.ClusterType.REPLICA_SET)
        .diskBackupState(BackupJob.DiskBackupState.ACTIVE)
        .nextSnapshotDate(currentDate)
        .restoreWindowDays(2)
        .referenceTimeInMins(BackupJob.calculateReferenceTime(currentDate))
        .policies(policies)
        .copySettings(new ArrayList<>())
        .version(1);
  }

  public static BackupJob getDefaultBackupJob() {
    return getBackupJobBuilder().build();
  }

  public static BasicDBObject getDefaultAzureBackupSnapshot(final boolean pIsV2Disk) {
    final Date expirationDate = new Date();
    return getDefaultAzureBackupSnapshot(expirationDate, BackupSnapshot.Type.SCHEDULED, pIsV2Disk);
  }

  public static BasicDBObject getDefaultAzureBackupSnapshot() {
    return getDefaultAzureBackupSnapshot(false);
  }

  public static BasicDBObject getDefaultAzureBackupSnapshot(
      final Date pExpirationDate,
      final BackupSnapshot.Type pSnapshotType,
      final boolean pIsV2Disk) {
    final BasicDBObject snapshotDoc =
        new BasicDBObject()
            .append(AzureBackupSnapshot.FieldDefs.NAME, "test-snapshot")
            .append(AzureBackupSnapshot.FieldDefs.SUBSCRIPTION_ID, new ObjectId())
            .append(AzureBackupSnapshot.FieldDefs.RESOURCE_GROUP, "test-resource-group")
            .append(ReplicaSetBackupSnapshot.FieldDefs.REGION_NAME, AzureRegionName.US_EAST.name())
            .append(BackupSnapshot.FieldDefs.TYPE, pSnapshotType.name());

    if (pIsV2Disk) {
      snapshotDoc
          .append(AzureBackupSnapshot.FieldDefs.DISK_TYPE, AzureDiskType.V2.name())
          .append(AzureBackupSnapshot.FieldDefs.DISK_SIZE_GB, 4099L);
    } else {
      snapshotDoc.append(AzureBackupSnapshot.FieldDefs.DISK_TYPE, AzureDiskType.P10.name());
    }

    return getBackupSnapshotFields(pExpirationDate, "AZURE", snapshotDoc);
  }

  public static BasicDBObject getDefaultAzureBackupSnapshot(
      final Date pExpirationDate, final BackupSnapshot.Type pSnapshotType) {
    return getDefaultAzureBackupSnapshot(pExpirationDate, pSnapshotType, false);
  }

  public static BasicDBObject getDefaultAwsBackupSnapshot() {
    final Date expirationDate = new Date();
    return getDefaultAwsBackupSnapshot(expirationDate);
  }

  public static BasicDBObject getDefaultAwsBackupSnapshot(final Date pExpirationDate) {
    final BasicDBObject snapshotDoc =
        new BasicDBObject()
            .append(AWSBackupSnapshot.FieldDefs.EBS_SNAPSHOT_ID, "test-snapshot-aws")
            .append(AWSBackupSnapshot.FieldDefs.EBS_SNAPSHOT_DESCRIPTION, "test-snapshot")
            .append(AWSBackupSnapshot.FieldDefs.AWS_ACCOUNT_ID, new ObjectId())
            .append(AWSBackupSnapshot.FieldDefs.EBS_VOLUME_SIZE, 200000)
            .append(AWSBackupSnapshot.FieldDefs.EBS_DISK_IOPS, 100)
            .append(ReplicaSetBackupSnapshot.FieldDefs.REGION_NAME, AWSRegionName.US_EAST_1.name());

    return getBackupSnapshotFields(pExpirationDate, "AWS", snapshotDoc);
  }

  public static BasicDBObject getBackupSnapshotFields(
      final Date pExpirationDate, final String pProvider, final BasicDBObject pSnapshotDoc) {
    final BasicDBObject encryptionDetails =
        new BasicDBObject()
            .append(
                BackupSnapshot.SnapshotEncryptionDetails.FieldDefs.ENCRYPTION_PROVIDER,
                ClusterDescription.EncryptionAtRestProvider.NONE.name());
    return new BasicDBObject()
        .append(BackupSnapshot.FieldDefs.ID, new ObjectId())
        .append(BackupSnapshot.FieldDefs.PROJECT_ID, new ObjectId())
        .append(BackupSnapshot.FieldDefs.CLUSTER_NAME, "test-cluster")
        .append(BackupSnapshot.FieldDefs.DEPLOYMENT_CLUSTER_NAME, "test-cluster")
        .append(BackupSnapshot.FieldDefs.CLUSTER_UNIQUE_ID, new ObjectId())
        .append(BackupSnapshot.FieldDefs.SCHEDULED_CREATION_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.SNAPSHOT_INITIATION_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.SNAPSHOT_COMPLETION_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.PROVIDERS, Collections.singletonList(pProvider))
        .append(BackupSnapshot.FieldDefs.MONGO_DB_VERSION, VersionUtils.FOUR_ZERO_ZERO.getVersion())
        .append(BackupSnapshot.FieldDefs.USED_DISK_SPACE, 2048L)
        .append(ReplicaSetBackupSnapshot.FieldDefs.SNAPSHOT, pSnapshotDoc)
        .append(
            ReplicaSetBackupSnapshot.FieldDefs.BACKUP_REPLICA_SET_TYPE,
            BackupReplicaSetType.REPLICA_SET)
        .append(BackupSnapshot.FieldDefs.DELETED, false)
        .append(BackupSnapshot.FieldDefs.PURGED, false)
        .append(BackupSnapshot.FieldDefs.SCHEDULED_DELETION_DATE, pExpirationDate)
        .append(BackupSnapshot.FieldDefs.PURGED_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.ENCRYPTION_DETAILS, encryptionDetails)
        .append(BackupSnapshot.FieldDefs.STATUS, BackupSnapshot.Status.COMPLETED.name())
        .append(BackupSnapshot.FieldDefs.TYPE, BackupSnapshot.Type.SCHEDULED.name())
        .append(BackupSnapshot.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.DAILY.name())
        .append(BackupSnapshot.FieldDefs.POLICY_ITEMS, new BasicDBList())
        .append(BackupSnapshot.FieldDefs.OVERRIDE_RETENTION_POLICY, false)
        .append(BackupSnapshot.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.DAYS.name());
  }

  public static BasicDBObject getDefaultGcpBackupSnapshot(final Date pExpirationDate) {
    final BasicDBObject snapshotDoc =
        new BasicDBObject()
            .append(GCPBackupSnapshot.FieldDefs.GCP_PROJECT_ID, "p-1111111111111")
            .append(GCPBackupSnapshot.FieldDefs.GCP_ORGANIZATION_ID, new ObjectId())
            .append(GCPBackupSnapshot.FieldDefs.GCP_SOURCE_DISK, "source-disk-name")
            .append(GCPBackupSnapshot.FieldDefs.GCP_SNAPSHOT_NAME, "test-snapshot")
            .append(GCPBackupSnapshot.FieldDefs.GCP_SNAPSHOT_DISK_SIZE_GB, 100L);

    final BasicDBObject encryptionDetails =
        new BasicDBObject()
            .append(
                BackupSnapshot.SnapshotEncryptionDetails.FieldDefs.ENCRYPTION_PROVIDER,
                ClusterDescription.EncryptionAtRestProvider.NONE.name());

    return new BasicDBObject()
        .append(BackupSnapshot.FieldDefs.ID, new ObjectId())
        .append(BackupSnapshot.FieldDefs.PROJECT_ID, new ObjectId())
        .append(CpsRestoreMetadata.FieldDefs.SNAPSHOT_ID, new ObjectId())
        .append(BackupSnapshot.FieldDefs.CLUSTER_NAME, "test-cluster")
        .append(BackupSnapshot.FieldDefs.DEPLOYMENT_CLUSTER_NAME, "test-cluster")
        .append(BackupSnapshot.FieldDefs.CLUSTER_UNIQUE_ID, new ObjectId())
        .append(BackupSnapshot.FieldDefs.SCHEDULED_CREATION_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.SNAPSHOT_INITIATION_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.SNAPSHOT_COMPLETION_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.PROVIDERS, List.of("GCP"))
        .append(BackupSnapshot.FieldDefs.MONGO_DB_VERSION, VersionUtils.FOUR_ZERO_ZERO.getVersion())
        .append(BackupSnapshot.FieldDefs.USED_DISK_SPACE, 2048L)
        .append(ReplicaSetBackupSnapshot.FieldDefs.SNAPSHOT, snapshotDoc)
        .append(BackupSnapshot.FieldDefs.DELETED, false)
        .append(BackupSnapshot.FieldDefs.PURGED, false)
        .append(BackupSnapshot.FieldDefs.SCHEDULED_DELETION_DATE, pExpirationDate)
        .append(BackupSnapshot.FieldDefs.PURGED_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.ENCRYPTION_DETAILS, encryptionDetails)
        .append(BackupSnapshot.FieldDefs.STATUS, BackupSnapshot.Status.COMPLETED.name())
        .append(BackupSnapshot.FieldDefs.TYPE, BackupSnapshot.Type.SCHEDULED.name())
        .append(BackupSnapshot.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.DAILY.name())
        .append(BackupSnapshot.FieldDefs.POLICY_ITEMS, new BasicDBList())
        .append(BackupSnapshot.FieldDefs.OVERRIDE_RETENTION_POLICY, false)
        .append(BackupSnapshot.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.DAYS.name());
  }

  public static BasicDBObject getDefaultGcpGovBackupSnapshot(
      final Date pExpirationDate, final String regionName) {
    final BasicDBObject snapshotDoc =
        new BasicDBObject()
            .append(GCPBackupSnapshot.FieldDefs.GCP_PROJECT_ID, "p-1111111111111")
            .append(GCPBackupSnapshot.FieldDefs.GCP_ORGANIZATION_ID, new ObjectId())
            .append(GCPBackupSnapshot.FieldDefs.GCP_SOURCE_DISK, "source-disk-name")
            .append(GCPBackupSnapshot.FieldDefs.GCP_SNAPSHOT_NAME, "test-snapshot")
            .append(ReplicaSetBackupSnapshot.FieldDefs.REGION_NAME, regionName)
            .append(GCPBackupSnapshot.FieldDefs.GCP_SNAPSHOT_DISK_SIZE_GB, 100L);

    final BasicDBObject encryptionDetails =
        new BasicDBObject()
            .append(
                BackupSnapshot.SnapshotEncryptionDetails.FieldDefs.ENCRYPTION_PROVIDER,
                ClusterDescription.EncryptionAtRestProvider.NONE.name());

    return new BasicDBObject()
        .append(BackupSnapshot.FieldDefs.ID, new ObjectId())
        .append(BackupSnapshot.FieldDefs.PROJECT_ID, new ObjectId())
        .append(CpsRestoreMetadata.FieldDefs.SNAPSHOT_ID, new ObjectId())
        .append(BackupSnapshot.FieldDefs.CLUSTER_NAME, "test-cluster")
        .append(BackupSnapshot.FieldDefs.DEPLOYMENT_CLUSTER_NAME, "test-cluster")
        .append(BackupSnapshot.FieldDefs.CLUSTER_UNIQUE_ID, new ObjectId())
        .append(BackupSnapshot.FieldDefs.SCHEDULED_CREATION_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.SNAPSHOT_INITIATION_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.SNAPSHOT_COMPLETION_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.PROVIDERS, List.of("GCP"))
        .append(BackupSnapshot.FieldDefs.MONGO_DB_VERSION, VersionUtils.FOUR_ZERO_ZERO.getVersion())
        .append(BackupSnapshot.FieldDefs.USED_DISK_SPACE, 2048L)
        .append(ReplicaSetBackupSnapshot.FieldDefs.SNAPSHOT, snapshotDoc)
        .append(BackupSnapshot.FieldDefs.DELETED, false)
        .append(BackupSnapshot.FieldDefs.PURGED, false)
        .append(BackupSnapshot.FieldDefs.SCHEDULED_DELETION_DATE, pExpirationDate)
        .append(BackupSnapshot.FieldDefs.PURGED_DATE, new Date())
        .append(BackupSnapshot.FieldDefs.ENCRYPTION_DETAILS, encryptionDetails)
        .append(BackupSnapshot.FieldDefs.STATUS, BackupSnapshot.Status.COMPLETED.name())
        .append(BackupSnapshot.FieldDefs.TYPE, BackupSnapshot.Type.SCHEDULED.name())
        .append(BackupSnapshot.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.DAILY.name())
        .append(BackupSnapshot.FieldDefs.POLICY_ITEMS, new BasicDBList())
        .append(BackupSnapshot.FieldDefs.OVERRIDE_RETENTION_POLICY, false)
        .append(BackupSnapshot.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.DAYS.name());
  }

  public static CpsOplogSliceView getCpsOplogSliceView() {
    return new CpsOplogSliceView.Builder()
        .id(ObjectId.get())
        .numDocs(2)
        .compressor("snappy")
        .start(new CpsOplogIdView(new BSONTimestamp(1, 1), 3L, 1L))
        .end(new CpsOplogIdView(new BSONTimestamp(2, 1), 5L, 6L))
        .rawSize(1234)
        .processedSize(123)
        .blobKey("526b23fbd4e7aec7a093c175/5febcbcf1cbbe3a8aacaee8a")
        .mongodVersion("4.2.10")
        .storageRegion("us-east-1")
        .storageProvider("AWS")
        .build();
  }

  public static CpsOplogMetadataBatch createCpsOplogMetadataBatch(final List<String> regions) {
    final List<ProviderStorage> providerStorages = new ArrayList<>();
    for (final String region : regions) {
      final ProviderStorage providerStorage =
          new ProviderStorage(CloudProvider.AWS, region, region + "-bucket");
      providerStorages.add(providerStorage);
    }
    return getCpsOplogMetadataBatch(providerStorages);
  }

  public static CpsOplogMetadataBatch getCpsOplogMetadataBatch(
      final List<ProviderStorage> providerStorages) {
    final List<CpsOplogSliceView> sliceViews = new ArrayList<>();
    for (int i = 0; i < 5; i++) {
      final CpsOplogSliceView sliceView =
          new Builder()
              .id(ObjectId.get())
              .numDocs(2)
              .compressor("snappy")
              .start(new CpsOplogIdView(new BSONTimestamp(1, 1), 3L, 1L))
              .end(new CpsOplogIdView(new BSONTimestamp(2, 1), 5L, 6L))
              .rawSize(1234)
              .processedSize(123)
              .blobKey("526b23fbd4e7aec7a093c175/5febcbcf1cbbe3a8aacaee8a")
              .mongodVersion("4.2.10")
              .build();
      sliceViews.add(sliceView);
    }

    return new CpsOplogMetadataBatch(sliceViews, providerStorages);
  }

  public static CpsOplogSlice.Builder getCpsOplogSliceBuilder() {
    return new CpsOplogSlice.Builder()
        .setId(ObjectId.get())
        .setSliceId(ObjectId.get())
        .setNumDocs(2)
        .setCompressor("snappy")
        .setStartTimestamp(new BSONTimestamp(1, 1))
        .setEndTimestamp(new BSONTimestamp(2, 1))
        .setRawSize(1234)
        .setProcessedSize(123)
        .setMongodVersion("4.2.10");
  }

  public static StreamingReplicaSetRestoreJob.Builder getStreamingRestoreJobBuilder(
      VMBasedReplSetRestoreJob.Builder parentBuilder) {
    parentBuilder.getParentBuilder().getMetadataBuilder().withStrategy(StrategyName.STREAMING);

    return StreamingReplicaSetRestoreJob.Builder.aStreamingJob()
        .parentBuilder(parentBuilder)
        .status(new Status())
        .areAllVmsProvisioned(true);
  }

  public static VMBasedReplSetRestoreJob.Builder getVMBasedJobBuilder(
      CpsRestoreMetadata.Builder metadataBuilder) {

    final ReplicaSetBackupRestoreJob.Builder parent =
        getReplicaSetBackupRestoreJobBuilder(metadataBuilder);
    return VMBasedReplSetRestoreJob.Builder.aVMBasedJob()
        .withParentBuilder(parent)
        .withRestoreServerRunning(true)
        .withInstanceHardware(
            InstanceHardware.getHardware(
                InstanceHardware.getEmptyHardware(
                    CloudProvider.AZURE, new ObjectId(), new Date(), 0)));
  }

  public static DirectAttachReplicaSetBackupRestoreJob.Builder getDirectAttachRestoreJobBuilder(
      final ObjectId rId) {
    final CpsRestoreMetadata.Builder metadataBuilder =
        getMetadataBuilder(rId, getDefaultRestoreTargetCluster())
            .withStrategy(StrategyName.DIRECT_ATTACH);

    final ReplicaSetBackupRestoreJob.Builder parent =
        getReplicaSetBackupRestoreJobBuilder(metadataBuilder);
    return DirectAttachReplicaSetBackupRestoreJob.Builder.aDirectAttachReplicaSetRestoreJob()
        .withParentBuilder(parent)
        .withAttachStatuses(Collections.emptyList())
        .withCloudProvider(CloudProvider.AWS);
  }

  private static ReplicaSetBackupRestoreJob.Builder getReplicaSetBackupRestoreJobBuilder(
      final CpsRestoreMetadata.Builder metadataBuilder) {
    return ReplicaSetBackupRestoreJob.Builder.aReplicaSetBackupRestoreJob()
        .withMetadataBuilder(metadataBuilder)
        .withRegionName(AzureRegionName.US_EAST_2)
        .withVerificationKey("ZTk0OThlYWQwYzhkNGYzNjk4MmRjNGI2NDc0MTNjN2E=")
        .withRsId("randomRsId");
  }

  public static CpsRestoreMetadata.Builder getMetadataBuilder(
      final ObjectId id, final RestoreTargetCluster restoreTarget) {
    return CpsRestoreMetadata.Builder.aCpsRestoreMetadata()
        .withId(id)
        .withProjectId(new ObjectId())
        .withClusterUniqueId(new ObjectId())
        .withClusterName("testCluster")
        .withSnapshotId(new ObjectId())
        .withExpired(false)
        .withCanceled(false)
        .withExpirationDate(new Date())
        .withRequestingUser(new CpsRestoreMetadata.RequestingUser())
        .withTarget(restoreTarget);
  }

  public static RestoreTargetCluster getDefaultRestoreTargetCluster() {
    return RestoreTargetCluster.createRestoreTargetCluster(
        new ObjectId(),
        GroupVisibility.DEFAULT_VISIBLE,
        "testCluster",
        "testCluster",
        "",
        "UUID",
        "UUID");
  }

  public static BasicDBObject getDefaultShardedClusterBackupRestoreJob(
      final int numberOfMembers, final BasicDBObject delivery) {
    final ObjectId projectId = new ObjectId();

    final BasicDBList members = new BasicDBList();
    for (int i = 0; i < numberOfMembers; i++) {
      members.add(
          new BasicDBObject(ShardedClusterBackupRestoreJob.Member.FieldDefs.RS_ID, "rsId" + i)
              .append(
                  ShardedClusterBackupRestoreJob.Member.FieldDefs.RESTORE_JOB_ID, new ObjectId()));
    }

    final BasicDBList cloudProviders = new BasicDBList();
    cloudProviders.add(CloudProvider.AWS.name());

    return new BasicDBObject()
        .append(CpsRestoreMetadata.FieldDefs.ID, new ObjectId())
        .append(CpsRestoreMetadata.FieldDefs.PROJECT_ID, projectId)
        .append(CpsRestoreMetadata.FieldDefs.CLUSTER_UNIQUE_ID, new ObjectId())
        .append(CpsRestoreMetadata.FieldDefs.CLUSTER_NAME, "test-cluster")
        .append(CpsRestoreMetadata.FieldDefs.SNAPSHOT_ID, new ObjectId())
        .append(CpsRestoreMetadata.FieldDefs.EXPIRED, false)
        .append(CpsRestoreMetadata.FieldDefs.CANCELED, false)
        .append(CpsRestoreMetadata.FieldDefs.DELIVERY, delivery)
        .append(BackupRestoreJobDelivery.FieldDefs.TARGET_MDB_VERSION, "mdb-version")
        .append(ShardedClusterBackupRestoreJob.FieldDefs.MEMBERS, members)
        .append(ShardedClusterBackupRestoreJob.FieldDefs.CLOUD_PROVIDERS, cloudProviders);
  }

  private static BasicDBObject versionedClusterDescription(
      final BasicDBObject clusterBasicDbObject, final String pVersion) {
    final VersionUtils.Version version = VersionUtils.parse(pVersion);
    return clusterBasicDbObject
        .append("mongoDBVersion", version.getVersion())
        .append("mongoDBMajorVersion", version.getMajorVersionString());
  }

  public static BasicDBObject getNonTargetedRestoreDelivery() {
    return new BasicDBObject(
        BackupRestoreJobDelivery.FieldDefs.DELIVERY_TYPE, StrategyName.STREAMING.name());
  }

  public static BasicDBObject getTargetedRestoreDelivery(final StrategyName strategyName) {
    return new BasicDBObject()
        .append(BackupRestoreJobDelivery.FieldDefs.DELIVERY_TYPE, strategyName.name())
        .append(BackupRestoreJobDelivery.FieldDefs.TARGET_CLUSTER_NAME, "test-cluster")
        .append(BackupRestoreJobDelivery.FieldDefs.TARGET_DEPLOYMENT_ITEM_NAME, "test-cluster")
        .append(BackupRestoreJobDelivery.FieldDefs.TARGET_PROJECT_ID, new ObjectId());
  }

  public static DBObject getIndexConfig(final String pRSName, final String pKey) {
    return _morphiaMapper.toDBObject(getIndexConfigModel(pRSName, pKey));
  }

  public static IndexConfig getIndexConfigModel(final String pRSName, final String pKey) {
    final IndexOptions indexOptions =
        new IndexOptions(null, false, null, null, null, null, null, null);
    return new IndexConfig(
        Collections.singletonList(Arrays.asList(pKey, "1")),
        indexOptions,
        pRSName,
        "myindexeddb",
        "mycollection",
        null);
  }

  public static TenantBackupSnapshot getTenantBackupSnapshot(
      final String pHandlingProxyHost,
      final State pState,
      final ObjectId pGroupId,
      final ObjectId pClusterUniqueId,
      final String pClusterName,
      final Date pScheduledForDate,
      final SnapshotType pType,
      final ObjectId pMTMGroupId,
      final String pMTMClusterName) {
    return new TenantBackupSnapshot(
        new ObjectId(),
        pGroupId,
        pClusterUniqueId,
        pClusterName,
        pHandlingProxyHost,
        pState,
        pScheduledForDate,
        pState == State.COMPLETED ? pScheduledForDate : null,
        pState == State.COMPLETED
            ? Date.from(pScheduledForDate.toInstant().plus(Duration.ofMinutes(10)))
            : null,
        null,
        pType,
        null,
        TEST_FREE_MONGODB_VERSION,
        CloudProvider.AWS,
        "source.member",
        Date.from(pScheduledForDate.toInstant().plus(Duration.ofDays(7))),
        null,
        false,
        null,
        false,
        null,
        new TenantBackupSnapshot.TenantSnapshotInfo(AWSRegionName.US_EAST_1, "foo", "foo/bar.tgz"),
        null,
        pMTMGroupId,
        pMTMClusterName);
  }

  public static TenantBackupSnapshot getTenantBackupSnapshot(
      final String pHandlingProxyHost,
      final TenantBackupTask.State pState,
      final ObjectId pGroupId,
      final ObjectId pClusterUniqueId,
      final Date pScheduledForDate) {
    return getTenantBackupSnapshot(
        pHandlingProxyHost, pState, pGroupId, pClusterUniqueId, "testCluster", pScheduledForDate);
  }

  public static TenantBackupSnapshot getTenantBackupSnapshot(
      final String pHandlingProxyHost,
      final TenantBackupTask.State pState,
      final ObjectId pGroupId,
      final ObjectId pClusterUniqueId,
      final String pClusterName,
      final Date pScheduledForDate) {
    return getTenantBackupSnapshot(
        pHandlingProxyHost,
        pState,
        pGroupId,
        pClusterUniqueId,
        pClusterName,
        pScheduledForDate,
        TenantBackupTask.SnapshotType.BACKUP,
        null,
        null);
  }

  public static TenantRestore getTenantRestore(
      final String pHandlingProxyHost,
      final TenantBackupTask.State pState,
      final ObjectId pGroupId,
      final ObjectId pClusterUniqueId,
      final Date pScheduledForDate) {
    return getTenantRestore(
        pHandlingProxyHost,
        pState,
        pGroupId,
        pClusterUniqueId,
        pScheduledForDate,
        TenantBackupTask.SnapshotType.BACKUP);
  }

  public static TenantRestore getTenantRestore(
      final String pHandlingProxyHost,
      final TenantBackupTask.State pState,
      final ObjectId pGroupId,
      final ObjectId pClusterUniqueId,
      final Date pScheduledForDate,
      final TenantBackupTask.SnapshotType pSnapshotType) {
    return getTenantRestore(
        pHandlingProxyHost,
        pState,
        pGroupId,
        pClusterUniqueId,
        "testCluster",
        pScheduledForDate,
        new ObjectId(),
        "target-deployment-0",
        pSnapshotType);
  }

  public static TenantRestore getTenantRestore(
      final String pHandlingProxyHost,
      final TenantBackupTask.State pState,
      final ObjectId pGroupId,
      final ObjectId pClusterUniqueId,
      final String pClusterName,
      final Date pScheduledForDate) {
    return getTenantRestore(
        pHandlingProxyHost,
        pState,
        pGroupId,
        pClusterUniqueId,
        pClusterName,
        pScheduledForDate,
        new ObjectId(),
        "target-deployment-0");
  }

  public static TenantRestore getTenantRestore(
      final String pHandlingProxyHost,
      final TenantBackupTask.State pState,
      final ObjectId pGroupId,
      final ObjectId pClusterUniqueId,
      final String pClusterName,
      final Date pScheduledForDate,
      final TenantBackupTask.SnapshotType pSnapshotType) {
    return getTenantRestore(
        pHandlingProxyHost,
        pState,
        pGroupId,
        pClusterUniqueId,
        pClusterName,
        pScheduledForDate,
        new ObjectId(),
        "target-deployment-0",
        pSnapshotType);
  }

  public static TenantRestore getTenantRestore(
      final String pHandlingProxyHost,
      final TenantBackupTask.State pState,
      final ObjectId pGroupId,
      final ObjectId pClusterUniqueId,
      final String pClusterName,
      final Date pScheduledForDate,
      final ObjectId pTargetGroupId,
      final String pTargetDeploymentItemName) {
    return getTenantRestore(
        pHandlingProxyHost,
        pState,
        pGroupId,
        pClusterUniqueId,
        pClusterName,
        pScheduledForDate,
        pTargetGroupId,
        pTargetDeploymentItemName,
        TenantBackupTask.SnapshotType.BACKUP);
  }

  public static TenantRestore getTenantRestore(
      final String pHandlingProxyHost,
      final TenantBackupTask.State pState,
      final ObjectId pGroupId,
      final ObjectId pClusterUniqueId,
      final String pClusterName,
      final Date pScheduledForDate,
      final ObjectId pTargetGroupId,
      final String pTargetDeploymentItemName,
      final TenantBackupTask.SnapshotType pSnapshotType) {
    return new TenantRestore(
        new ObjectId(),
        pGroupId,
        pClusterUniqueId,
        pClusterName,
        pHandlingProxyHost,
        pState,
        pScheduledForDate,
        null,
        null,
        null,
        pSnapshotType,
        234,
        new ObjectId(),
        "snapshot url",
        null,
        new Date(),
        Date.from(pScheduledForDate.toInstant().plus(Duration.ofHours(4))),
        "destination",
        new TenantRestore.RequestingUser("test", "<EMAIL>"),
        new TenantRestore.RestoreDelivery(
            TenantRestore.DeliveryType.RESTORE, pTargetGroupId, pTargetDeploymentItemName),
        pHandlingProxyHost == null ? null : new ObjectId(),
        pHandlingProxyHost == null ? null : "mtm-us-east-1-0");
  }

  public static TenantRestore getTenantDownload(
      final ObjectId pGroupId,
      final ObjectId pClusterUniqueId,
      final String pClusterName,
      final TenantBackupTask.State pState,
      final Date pNowDate,
      final ObjectId pSnapshotId) {
    return new TenantRestore(
        new ObjectId(),
        pGroupId,
        pClusterUniqueId,
        pClusterName,
        null,
        pState,
        pNowDate,
        pNowDate,
        pNowDate,
        null,
        TenantBackupTask.SnapshotType.BACKUP,
        null,
        pSnapshotId,
        "https://example.org/",
        null,
        new Date(),
        Date.from(pNowDate.toInstant().plus(Duration.ofHours(4))),
        null,
        new TenantRestore.RequestingUser("test", "<EMAIL>"),
        new TenantRestore.RestoreDelivery(
            TenantRestore.DeliveryType.DOWNLOAD, pGroupId, pClusterName),
        null,
        null);
  }

  public static FTSIndex getFTSIndex() {
    final BasicDBObject indexObj = new BasicDBObject();
    indexObj.put(FTSIndex.FieldDefs.INDEX_ID, new ObjectId());
    indexObj.put(FTSIndex.FieldDefs.NAME, "fooIndex");
    indexObj.put(FTSIndex.FieldDefs.DATABASE, "database");
    indexObj.put(FTSIndex.FieldDefs.LAST_OBSERVED_COLLECTION_NAME, "collection");
    addCommonFieldsToFtsIndex(indexObj);
    return FTSIndex.create(indexObj);
  }

  public static FTSIndex getFTSIndex(
      final ObjectId pIndexId,
      final String pIndexName,
      final String pDatabase,
      final String pCollection) {
    final BasicDBObject indexObj = new BasicDBObject();
    indexObj.put(FTSIndex.FieldDefs.INDEX_ID, pIndexId);
    indexObj.put(FTSIndex.FieldDefs.NAME, pIndexName);
    indexObj.put(FTSIndex.FieldDefs.DATABASE, pDatabase);
    indexObj.put(FTSIndex.FieldDefs.LAST_OBSERVED_COLLECTION_NAME, pCollection);
    addCommonFieldsToFtsIndex(indexObj);
    return FTSIndex.create(indexObj);
  }

  private static void addCommonFieldsToFtsIndex(final BasicDBObject pIndexObj) {
    pIndexObj.put(FTSIndex.FieldDefs.COLLECTION_UUID, new UUID(1L, 1L));
    pIndexObj.put(FTSIndex.FieldDefs.OPTIME, 1L);
    pIndexObj.put(FTSSearchIndex.FieldDefs.ANALYZER, "lucene.foo");
    pIndexObj.put(FTSSearchIndex.FieldDefs.ANALYZERS, new BasicDBList());
    pIndexObj.put(FTSSearchIndex.FieldDefs.SEARCH_ANALYZER, "lucene.foo.search");
    pIndexObj.put(FTSSearchIndex.FieldDefs.MAPPINGS, new BasicDBObject());
    pIndexObj.put(FTSIndex.FieldDefs.DELETE_REQUESTED_DATE, null);
  }

  public static List<Policy> getPolicy(final List<DBObject> items) {
    final Policy policy =
        new Policy(
            new BasicDBObject(Policy.FieldDefs.POLICY_ITEMS, items)
                .append(Policy.FieldDefs.ID, new ObjectId()));
    return List.of(policy);
  }

  public static List<Policy> getPolicyItems1(final Date date, final ObjectId id) {
    final Date oneHourBefore = DateUtils.addHours(date, -1);
    final List<DBObject> items = new ArrayList<>();
    items.add(
        new BasicDBObject()
            .append(PolicyItem.FieldDefs.ID, id)
            .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.DAILY.name())
            .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, 1)
            .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, oneHourBefore)
            .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, Duration.ofDays(3).toMillis())
            .append(PolicyItem.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.WEEKS.name()));
    return getPolicy(items);
  }

  public static List<Policy> getPolicyItems2(
      final Date date, final ObjectId id, final ObjectId id1) {
    final Date oneHourBefore = DateUtils.addHours(date, -1);
    final Date oneHourLater = DateUtils.addHours(date, 1);

    final List<DBObject> items = new ArrayList<>();
    items.add(
        // 3 snapshots
        new BasicDBObject()
            .append(PolicyItem.FieldDefs.ID, id)
            .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.DAILY.name())
            .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, 1)
            .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, oneHourLater)
            .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, Duration.ofDays(3).toMillis())
            .append(PolicyItem.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.DAYS.name()));
    items.add(
        // 48 snapshots
        new BasicDBObject()
            .append(PolicyItem.FieldDefs.ID, id1)
            .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.HOURLY.name())
            .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, 1)
            .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, oneHourBefore)
            .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, Duration.ofDays(2).toMillis())
            .append(PolicyItem.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.DAYS.name()));
    return getPolicy(items);
  }

  public static List<Policy> getPolicyItems3(
      final Date date, final ObjectId id, final ObjectId id1, final ObjectId id2) {
    final Date oneHourBefore = DateUtils.addHours(date, -1);
    final Date oneDayBefore = DateUtils.addDays(date, -1);
    final List<DBObject> items = new ArrayList<>();
    items.add(
        // 7 snapshots
        new BasicDBObject()
            .append(PolicyItem.FieldDefs.ID, id)
            .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.DAILY.name())
            .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, 2) // this number should not matter
            .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, oneHourBefore)
            .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, Duration.ofDays(7).toMillis())
            .append(PolicyItem.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.DAYS.name()));
    items.add(
        // 2 snapshots
        new BasicDBObject()
            .append(PolicyItem.FieldDefs.ID, id1)
            .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.WEEKLY.name())
            .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, 1)
            .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, oneHourBefore)
            .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, Duration.ofDays(14).toMillis())
            .append(PolicyItem.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.DAYS.name()));
    items.add(
        // 2 snapshots
        new BasicDBObject()
            .append(PolicyItem.FieldDefs.ID, id2)
            .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.MONTHLY.name())
            .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, 1)
            .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, oneDayBefore)
            .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, Duration.ofDays(62).toMillis())
            .append(PolicyItem.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.MONTHS.name()));
    return getPolicy(items);
  }

  public static List<Policy> getPolicyItems4(
      final Date date,
      final ObjectId id,
      final ObjectId id1,
      final ObjectId id2,
      final ObjectId id3) {
    final Date oneHourBefore = DateUtils.addHours(date, -1);
    final Date oneDayBefore = DateUtils.addDays(date, -1);
    final List<DBObject> items = new ArrayList<>();
    items.add(
        // 7 snapshots
        new BasicDBObject()
            .append(PolicyItem.FieldDefs.ID, id)
            .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.DAILY.name())
            .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, 2) // this number should not matter
            .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, oneHourBefore)
            .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, Duration.ofDays(7).toMillis())
            .append(PolicyItem.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.DAYS.name()));
    items.add(
        // 2 snapshots
        new BasicDBObject()
            .append(PolicyItem.FieldDefs.ID, id1)
            .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.WEEKLY.name())
            .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, 1)
            .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, oneHourBefore)
            .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, Duration.ofDays(14).toMillis())
            .append(PolicyItem.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.DAYS.name()));
    items.add(
        // 2 snapshots
        new BasicDBObject()
            .append(PolicyItem.FieldDefs.ID, id2)
            .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.MONTHLY.name())
            .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, 1)
            .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, oneDayBefore)
            .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, Duration.ofDays(62).toMillis())
            .append(PolicyItem.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.MONTHS.name()));
    items.add(
        // 2 snapshots
        new BasicDBObject()
            .append(PolicyItem.FieldDefs.ID, id3)
            .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, BackupFrequencyType.YEARLY.name())
            .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, 1)
            .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, oneDayBefore)
            .append(PolicyItem.FieldDefs.RETENTION_IN_MILLIS, Duration.ofDays(372).toMillis())
            .append(PolicyItem.FieldDefs.RETENTION_UNIT, BackupRetentionUnit.YEARS.name()));
    return getPolicy(items);
  }

  public static NDSAcmeCAStatus getNDSAcmeCaStatus() {
    return new NDSAcmeCAStatus(
        new ObjectId(),
        ACMEProvider.LETS_ENCRYPT_V2,
        NDSAcmeCAStatus.Status.ENABLED,
        new Date(),
        new Date());
  }

  public static NDSACMECert getNDSACMECert(
      final String pSerial,
      final String pHostname,
      final String pCommonName,
      final EncryptedString pEncryptedString) {
    return new NDSACMECert(
        new ACMECert.ID(ACMEProvider.LETS_ENCRYPT_V2_STAGING, pSerial),
        "imagine PEM text here",
        new Date(System.currentTimeMillis() + Duration.ofDays(14).toMillis()),
        ACMECert.Status.ISSUED,
        pHostname,
        pCommonName,
        List.of("*.mms.com"),
        pEncryptedString,
        false);
  }

  public static NDSACMECert getNDSACMECert(
      final String pSerial,
      final String pHostname,
      final Date pExpirationDate,
      final String pCommonName,
      final EncryptedString pEncryptedString) {
    return new NDSACMECert(
        new ACMECert.ID(ACMEProvider.LETS_ENCRYPT_V2_STAGING, pSerial),
        "imagine PEM text here",
        pExpirationDate,
        ACMECert.Status.ISSUED,
        pHostname,
        pCommonName,
        List.of("*.mms.com"),
        pEncryptedString,
        false);
  }

  public static AWSHardwareSpec getAWSHardwareSpec(
      final int pNodes, final AWSNDSInstanceSize pInstanceSize, final RegionName pRegionName) {
    return getAWSHardwareSpec(pNodes, pInstanceSize, pRegionName, CpuArchitecture.ARM64);
  }

  public static AWSHardwareSpec getAWSHardwareSpec(
      final int pNodes,
      final AWSNDSInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final CpuArchitecture pPreferredCpuArch) {
    return getAWSHardwareSpec(pNodes, false, pInstanceSize, pRegionName, pPreferredCpuArch);
  }

  public static AWSHardwareSpec getAWSHardwareSpec(
      final int pNodes,
      final boolean pIsDedicatedConfigServerHardware,
      final AWSNDSInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final CpuArchitecture pPreferredCpuArch) {
    return getAWSHardwareSpec(
        pNodes,
        pIsDedicatedConfigServerHardware,
        pInstanceSize,
        VolumeType.Gp3,
        pInstanceSize.getGP3StandardEBSIOPS(pInstanceSize.getDefaultDiskSizeGB()),
        pInstanceSize.getMinThroughput(pInstanceSize.getDefaultDiskSizeGB()),
        pRegionName,
        pPreferredCpuArch);
  }

  public static AWSHardwareSpec getAWSHardwareSpec(
      final int pNodes,
      final AWSNDSInstanceSize pInstanceSize,
      final VolumeType pVolumeType,
      int pIOPS,
      int pThroughput,
      final RegionName pRegionName,
      final CpuArchitecture pPreferredCpuArch) {
    return getAWSHardwareSpec(
        pNodes,
        false,
        pInstanceSize,
        pVolumeType,
        pIOPS,
        pThroughput,
        pRegionName,
        pPreferredCpuArch);
  }

  public static AWSHardwareSpec getAWSHardwareSpec(
      final int pNodes,
      final boolean pIsDedicatedConfigServerHardware,
      final AWSNDSInstanceSize pInstanceSize,
      final VolumeType pVolumeType,
      int pIOPS,
      int pThroughput,
      final RegionName pRegionName,
      final CpuArchitecture pPreferredCpuArch) {

    final AWSInstanceFamily instanceFamily =
        ((AWSInstanceFamily)
            pInstanceSize.getAvailableFamilies().get(pRegionName).stream()
                .filter(
                    family -> {
                      if (CpuArchitecture.ARM64.equals(pPreferredCpuArch)) {
                        return true;
                      }
                      return family.getCpuArchitecture().equals(pPreferredCpuArch);
                    })
                .reduce((first, second) -> second) // get the last element
                .orElse(null));

    return getAWSHardwareSpec(
        pNodes,
        pIsDedicatedConfigServerHardware,
        pInstanceSize,
        pVolumeType,
        pIOPS,
        pThroughput,
        pRegionName,
        pPreferredCpuArch,
        getOSForCloudProviderAndInstanceFamily(CloudProvider.AWS, instanceFamily),
        TEST_MONGODB_VERSION);
  }

  public static AWSHardwareSpec getAWSHardwareSpec(
      final int pNodes,
      final boolean pIsDedicatedConfigServerHardware,
      final AWSNDSInstanceSize pInstanceSize,
      final VolumeType pVolumeType,
      int pIOPS,
      int pThroughput,
      final RegionName pRegionName,
      final CpuArchitecture pPreferredCpuArch,
      final OS pOs,
      final VersionUtils.Version pMongoDBVersion) {

    final AWSInstanceFamily instanceFamily =
        ((AWSInstanceFamily)
            pInstanceSize.getAvailableFamilies().get(pRegionName).stream()
                .filter(
                    family -> {
                      if (family == AWSInstanceFamily.M8G
                          || family == AWSInstanceFamily.R8G
                          || family == AWSInstanceFamily.I8G) {
                        return pMongoDBVersion.supportsArmv9();
                      }
                      return true;
                    })
                .filter(
                    family -> {
                      if (CpuArchitecture.ARM64.equals(pPreferredCpuArch)) {
                        return true;
                      }
                      return family.getCpuArchitecture().equals(pPreferredCpuArch);
                    })
                .reduce((first, second) -> second) // get the last element
                .orElse(null));

    return new AWSHardwareSpec(
        pNodes,
        pIsDedicatedConfigServerHardware,
        pInstanceSize,
        instanceFamily,
        pOs,
        pPreferredCpuArch,
        pIOPS,
        pThroughput,
        pVolumeType,
        true);
  }

  public static AzureHardwareSpec getAzureHardwareSpec(
      final int pNodes, final RegionName pRegionName) {
    return getAzureHardwareSpec(pNodes, AzureNDSInstanceSize.M10, pRegionName);
  }

  public static AzureHardwareSpec getAzureHardwareSpec(
      final int pNodes, final RegionName pRegionName, final boolean pIsUsingSsdV2) {
    return getAzureHardwareSpec(
        pNodes, false, AzureNDSInstanceSize.M10, pRegionName, pIsUsingSsdV2);
  }

  public static AzureHardwareSpec getAzureHardwareSpec(
      final int pNodes, final AzureNDSInstanceSize pInstanceSize, final RegionName pRegionName) {
    return getAzureHardwareSpec(pNodes, false, pInstanceSize, pRegionName, true);
  }

  public static AzureHardwareSpec getAzureHardwareSpec(
      final int pNodes,
      final boolean pIsDedicatedConfigServerHardware,
      final AzureNDSInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final boolean pIsUsingSsdV2) {

    final AzureInstanceFamily instanceFamily =
        ((AzureInstanceFamily)
            pInstanceSize.getAvailableFamilies().get(pRegionName).stream()
                .reduce((first, second) -> second) // get the last element
                .orElse(null));

    return new AzureHardwareSpec(
        pNodes,
        pIsDedicatedConfigServerHardware,
        pInstanceSize,
        instanceFamily,
        getOSForCloudProviderAndInstanceFamily(CloudProvider.AZURE, instanceFamily),
        CpuArchitecture.X86_64,
        pIsUsingSsdV2 ? AzureDiskType.V2 : AzureDiskType.P4,
        null,
        null,
        PreferredStorageType.NONE);
  }

  public static AzureHardwareSpec getAzureHardwareSpec(
      final int pNodes,
      final AzureDiskType pDiskType,
      final AzureNDSInstanceSize pInstanceSize,
      final RegionName pRegionName) {
    final int pv2IOPS = pInstanceSize.getPV2IOPSForDiskSize(pInstanceSize.getDefaultDiskSizeGB());
    final int pv2Throughput =
        AzureNDSInstanceSize.getPV2ThroughputForDiskSizeAndIOPS(
            pInstanceSize.getDefaultDiskSizeGB(), pv2IOPS);
    return getAzureHardwareSpec(
        pNodes,
        pDiskType,
        pDiskType.equals(AzureDiskType.V2) ? pv2IOPS : null,
        pDiskType.equals(AzureDiskType.V2) ? pv2Throughput : null,
        pInstanceSize,
        pRegionName);
  }

  public static AzureHardwareSpec getAzureHardwareSpec(
      final int pNodes,
      final AzureDiskType pDiskType,
      final Integer pDiskIOPS,
      final Integer pDiskThroughput,
      final AzureNDSInstanceSize pInstanceSize,
      final RegionName pRegionName) {

    final AzureInstanceFamily instanceFamily =
        ((AzureInstanceFamily)
            pInstanceSize.getAvailableFamilies().get(pRegionName).stream()
                .reduce((first, second) -> second) // get the last element
                .orElse(null));

    return new AzureHardwareSpec(
        pNodes,
        false,
        pInstanceSize,
        instanceFamily,
        getOSForCloudProviderAndInstanceFamily(CloudProvider.AZURE, instanceFamily),
        CpuArchitecture.X86_64,
        pDiskType,
        pDiskIOPS,
        pDiskThroughput,
        PreferredStorageType.NONE);
  }

  public static GCPHardwareSpec getGCPHardwareSpec(final int pNodes, final RegionName pRegionName) {
    return getGCPHardwareSpec(pNodes, GCPNDSInstanceSize.M10, pRegionName);
  }

  public static GCPHardwareSpec getGCPHardwareSpec(
      final int pNodes, final GCPNDSInstanceSize pInstanceSize, final RegionName pRegionName) {
    return getGCPHardwareSpec(pNodes, false, pInstanceSize, pRegionName);
  }

  public static GCPHardwareSpec getGCPHardwareSpec(
      final int pNodes,
      final boolean pIsDedicatedConfigServerHardware,
      final GCPNDSInstanceSize pInstanceSize,
      final RegionName pRegionName) {

    final GCPInstanceFamily instanceFamily =
        ((GCPInstanceFamily)
            pInstanceSize.getAvailableFamilies().get(pRegionName).stream()
                // TODO: In CLOUDP-305122, we'll update tests to not filter out ARM/C4A or N4
                .filter(family -> family != GCPInstanceFamily.N4)
                .filter(family -> family.getCpuArchitecture() == CpuArchitecture.X86_64)
                .reduce((first, second) -> second) // get the last element
                .orElse(null));

    return getGCPHardwareSpec(
        pNodes,
        pIsDedicatedConfigServerHardware,
        pInstanceSize,
        pRegionName,
        getOSForCloudProviderAndInstanceFamily(CloudProvider.GCP, instanceFamily));
  }

  public static GCPHardwareSpec getGCPHardwareSpec(
      final int pNodes,
      final boolean pIsDedicatedConfigServerHardware,
      final GCPNDSInstanceSize pInstanceSize,
      final RegionName pRegionName,
      final OS pOS) {

    final GCPInstanceFamily instanceFamily =
        ((GCPInstanceFamily)
            pInstanceSize.getAvailableFamilies().get(pRegionName).stream()
                // TODO: In CLOUDP-305122, we'll update tests to not filter out ARM/C4A or N4
                .filter(family -> family != GCPInstanceFamily.N4)
                .filter(family -> family.getCpuArchitecture() == CpuArchitecture.X86_64)
                .reduce((first, second) -> second) // get the last element
                .orElse(null));

    return new GCPHardwareSpec(
        pNodes,
        pIsDedicatedConfigServerHardware,
        pInstanceSize,
        instanceFamily,
        pOS,
        CpuArchitecture.X86_64,
        null,
        null);
  }

  public static FreeHardwareSpec getFreeHardwareSpec(final int pNodes, final RegionName pRegion) {
    return new FreeHardwareSpec(pNodes, FreeInstanceSize.M0, pRegion.getProvider());
  }

  public static FreeHardwareSpec getTenantHardwareSpec(final int pNodes, final RegionName pRegion) {
    return new FreeHardwareSpec(pNodes, FreeInstanceSize.M2, pRegion.getProvider());
  }

  public static ServerlessHardwareSpec getServerlessHardwareSpec(
      final int pNodes, final RegionName pRegion) {
    return new ServerlessHardwareSpec(
        pNodes, ServerlessInstanceSize.SERVERLESS_V2, pRegion.getProvider());
  }

  public static AutoScaling getDefaultAutoScalingForProvider(final CloudProvider pProvider) {
    return getDefaultAutoScalingForProvider(pProvider, true);
  }

  public static AutoScaling getDefaultAutoScalingForProvider(
      final CloudProvider pProvider, final boolean pDiskAutoScalingEnabled) {
    switch (pProvider) {
      case AWS:
        return AWSAutoScaling.getDefaultAutoScaling().toBuilder()
            .diskGB(new DiskGBAutoScaling(pDiskAutoScalingEnabled))
            .build();
      case AZURE:
        return AzureAutoScaling.getDefaultAutoScaling().toBuilder()
            .diskGB(new DiskGBAutoScaling(pDiskAutoScalingEnabled))
            .build();
      case GCP:
        return GCPAutoScaling.getDefaultAutoScaling().toBuilder()
            .diskGB(new DiskGBAutoScaling(pDiskAutoScalingEnabled))
            .build();
      default:
        throw new IllegalArgumentException(
            "Cannot generate auto scaling for provider " + pProvider);
    }
  }

  public static ShardRegionConfig getShardRegionConfigForRegion(
      final RegionName pRegionName,
      final InstanceSize pInstanceSize,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries) {
    final CloudProvider provider = pRegionName.getProvider();
    return new ShardRegionConfig(
        pRegionName,
        provider,
        getDefaultAutoScalingForProvider(provider),
        null,
        pPriority,
        getHardwareSpec(provider, pElectableNodes, pInstanceSize, pRegionName),
        getHardwareSpec(provider, pAnalyticsNodes, pInstanceSize, pRegionName),
        getHardwareSpec(provider, pReadOnlyNodes, pInstanceSize, pRegionName),
        getHardwareSpec(provider, pHiddenSecondaries, pInstanceSize, pRegionName),
        null,
        null,
        null,
        null);
  }

  public static ShardRegionConfig getShardRegionConfigForRegion(
      final RegionName pRegionName,
      final InstanceSize pInstanceSize,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries,
      final boolean pDiskAutoScaleEnabled) {
    final CloudProvider provider = pRegionName.getProvider();
    return new ShardRegionConfig(
        pRegionName,
        provider,
        getDefaultAutoScalingForProvider(provider, pDiskAutoScaleEnabled),
        null,
        pPriority,
        getHardwareSpec(provider, pElectableNodes, pInstanceSize, pRegionName),
        getHardwareSpec(provider, pAnalyticsNodes, pInstanceSize, pRegionName),
        getHardwareSpec(provider, pReadOnlyNodes, pInstanceSize, pRegionName),
        getHardwareSpec(provider, pHiddenSecondaries, pInstanceSize, pRegionName),
        null,
        null,
        null,
        null);
  }

  // For more custom analytics node configurations
  public static ShardRegionConfig getShardRegionConfigForRegion(
      final RegionName pRegionName,
      final InstanceSize pInstanceSize,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final InstanceSize pInstanceSizeForAnalyticsNode,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries,
      final boolean pDiskAutoScaleEnabled) {
    final CloudProvider provider = pRegionName.getProvider();
    return new ShardRegionConfig(
        pRegionName,
        provider,
        getDefaultAutoScalingForProvider(provider, pDiskAutoScaleEnabled),
        null,
        pPriority,
        getHardwareSpec(provider, pElectableNodes, pInstanceSize, pRegionName),
        getHardwareSpec(provider, pAnalyticsNodes, pInstanceSizeForAnalyticsNode, pRegionName),
        getHardwareSpec(provider, pReadOnlyNodes, pInstanceSize, pRegionName),
        getHardwareSpec(provider, pHiddenSecondaries, pInstanceSize, pRegionName),
        null,
        null,
        null,
        null);
  }

  public static HardwareSpec getHardwareSpec(
      final CloudProvider pProvider,
      final int pNodes,
      final InstanceSize pInstanceSize,
      final RegionName pRegionName) {
    switch (pProvider) {
      case AWS:
        return getAWSHardwareSpec(
            pNodes,
            (AWSNDSInstanceSize) pInstanceSize,
            VolumeType.Gp3,
            ((AWSNDSInstanceSize) pInstanceSize)
                .getGP3StandardEBSIOPS(pInstanceSize.getDefaultDiskSizeGB()),
            ((AWSNDSInstanceSize) pInstanceSize)
                .getMinThroughput(pInstanceSize.getDefaultDiskSizeGB()),
            pRegionName,
            CpuArchitecture.ARM64);
      case AZURE:
        return getAzureHardwareSpec(pNodes, (AzureNDSInstanceSize) pInstanceSize, pRegionName);
      case GCP:
        return getGCPHardwareSpec(pNodes, (GCPNDSInstanceSize) pInstanceSize, pRegionName);
      default:
        throw new IllegalArgumentException(
            "Cannot generate hardware spec for provider " + pProvider);
    }
  }

  public static ShardRegionConfig getShardRegionConfigForRegion(
      final RegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries,
      final CpuArchitecture pPreferredArchitecture) {
    return getShardRegionConfigForRegion(
        pRegionName.getProvider(),
        pRegionName,
        pPriority,
        pElectableNodes,
        pAnalyticsNodes,
        pReadOnlyNodes,
        pHiddenSecondaries,
        pPreferredArchitecture);
  }

  public static ShardRegionConfig getShardRegionConfigForRegion(
      final RegionName pRegionName,
      final CpuArchitecture pCpuArchitecture,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries) {
    return getShardRegionConfigForRegion(
        pRegionName.getProvider(),
        pRegionName,
        pCpuArchitecture,
        pPriority,
        pElectableNodes,
        pAnalyticsNodes,
        pReadOnlyNodes,
        pHiddenSecondaries);
  }

  public static ShardRegionConfig getShardRegionConfigForRegion(
      final RegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries) {
    return getShardRegionConfigForRegion(
        pRegionName.getProvider(),
        pRegionName,
        CpuArchitecture.ARM64,
        pPriority,
        pElectableNodes,
        pAnalyticsNodes,
        pReadOnlyNodes,
        pHiddenSecondaries);
  }

  public static ShardRegionConfig getShardRegionConfigForRegion(
      final CloudProvider pProvider,
      final RegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries) {
    return getShardRegionConfigForRegion(
        pProvider,
        pRegionName,
        CpuArchitecture.ARM64,
        pPriority,
        pElectableNodes,
        pAnalyticsNodes,
        pReadOnlyNodes,
        pHiddenSecondaries);
  }

  public static ShardRegionConfig getShardRegionConfigForRegion(
      final CloudProvider pProvider,
      final RegionName pRegionName,
      final CpuArchitecture cpuArchitecture,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries) {
    return getShardRegionConfigForRegion(
        pProvider,
        pRegionName,
        pPriority,
        pElectableNodes,
        pAnalyticsNodes,
        pReadOnlyNodes,
        pHiddenSecondaries,
        cpuArchitecture);
  }

  public static ShardRegionConfig getShardRegionConfigForRegion(
      final CloudProvider pProvider,
      final RegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries,
      final CpuArchitecture pPreferredArchitecture) {
    switch (pProvider) {
      case AWS:
        return getShardRegionConfigForAWSRegion(
            ((AWSRegionName) pRegionName),
            pPriority,
            pElectableNodes,
            pAnalyticsNodes,
            pReadOnlyNodes,
            pHiddenSecondaries,
            pPreferredArchitecture,
            new AWSAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()));
      case AZURE:
        return getShardRegionConfigForAzureRegion(
            ((AzureRegionName) pRegionName),
            pPriority,
            pElectableNodes,
            pAnalyticsNodes,
            pReadOnlyNodes,
            pHiddenSecondaries);
      case GCP:
        return getShardRegionConfigForGCPRegion(
            ((GCPRegionName) pRegionName),
            pPriority,
            pElectableNodes,
            pAnalyticsNodes,
            pReadOnlyNodes,
            pHiddenSecondaries);
      case FREE:
        return getShardRegionConfigForFreeRegion(
            pRegionName, pElectableNodes, pAnalyticsNodes, pReadOnlyNodes, pHiddenSecondaries);
      case SERVERLESS:
        return getShardRegionConfigForServerlessRegion(
            pRegionName, pElectableNodes, pAnalyticsNodes, pReadOnlyNodes, pHiddenSecondaries);
      default:
        throw new IllegalArgumentException(
            "Cannot generate hardware spec for provider " + pRegionName.getProvider());
    }
  }

  private static ShardRegionConfig getShardRegionConfigForAWSRegion(
      final AWSRegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries,
      final CpuArchitecture pPreferredArchitecture,
      final AWSAutoScaling pAwsAutoScaling) {
    final boolean nvme = pHiddenSecondaries > 0;
    final AWSNDSInstanceSize instanceSize =
        nvme ? AWSNDSInstanceSize.M40_NVME : AWSNDSInstanceSize.M30;
    return new ShardRegionConfig(
        pRegionName,
        CloudProvider.AWS,
        pAwsAutoScaling,
        null,
        pPriority,
        getAWSHardwareSpec(pElectableNodes, instanceSize, pRegionName, pPreferredArchitecture),
        getAWSHardwareSpec(pAnalyticsNodes, instanceSize, pRegionName, pPreferredArchitecture),
        getAWSHardwareSpec(pReadOnlyNodes, instanceSize, pRegionName, pPreferredArchitecture),
        getAWSHardwareSpec(pHiddenSecondaries, instanceSize, pRegionName, pPreferredArchitecture),
        null,
        null,
        null,
        null);
  }

  private static ShardRegionConfig getShardRegionConfigForAWSRegion(
      final AWSRegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries,
      final CpuArchitecture pPreferredArchitecture,
      final AWSAutoScaling pAwsBaseAutoScaling,
      final AWSAutoScaling pAwsAnalyticsAutoScaling) {
    final boolean nvme = pHiddenSecondaries > 0;
    final AWSNDSInstanceSize instanceSize =
        nvme ? AWSNDSInstanceSize.M40_NVME : AWSNDSInstanceSize.M30;
    return new ShardRegionConfig(
        pRegionName,
        CloudProvider.AWS,
        pAwsBaseAutoScaling,
        pAwsAnalyticsAutoScaling,
        pPriority,
        getAWSHardwareSpec(pElectableNodes, instanceSize, pRegionName, pPreferredArchitecture),
        getAWSHardwareSpec(pAnalyticsNodes, instanceSize, pRegionName, pPreferredArchitecture),
        getAWSHardwareSpec(pReadOnlyNodes, instanceSize, pRegionName, pPreferredArchitecture),
        getAWSHardwareSpec(pHiddenSecondaries, instanceSize, pRegionName, pPreferredArchitecture),
        null,
        null,
        null,
        null);
  }

  public static ShardRegionConfig getShardRegionConfigSpecificVolumeTypeForAWSRegion(
      final AWSRegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries,
      final CpuArchitecture pPreferredArchitecture,
      final VolumeType pVolumeType,
      final int pIops) {
    final boolean nvme = pHiddenSecondaries > 0;
    return new ShardRegionConfig(
        pRegionName,
        CloudProvider.AWS,
        new AWSAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()),
        null,
        pPriority,
        getAWSHardwareSpec(
            pElectableNodes,
            nvme ? AWSNDSInstanceSize.M40_NVME : AWSNDSInstanceSize.M30,
            pVolumeType,
            pIops,
            4000,
            pRegionName,
            pPreferredArchitecture),
        getAWSHardwareSpec(
            pAnalyticsNodes,
            nvme ? AWSNDSInstanceSize.M40_NVME : AWSNDSInstanceSize.M30,
            pVolumeType,
            pIops,
            4000,
            pRegionName,
            pPreferredArchitecture),
        getAWSHardwareSpec(
            pReadOnlyNodes,
            nvme ? AWSNDSInstanceSize.M40_NVME : AWSNDSInstanceSize.M30,
            pVolumeType,
            pIops,
            4000,
            pRegionName,
            pPreferredArchitecture),
        getAWSHardwareSpec(
            pHiddenSecondaries,
            AWSNDSInstanceSize.M30,
            pVolumeType,
            pIops,
            4000,
            pRegionName,
            pPreferredArchitecture),
        null,
        null,
        null,
        null);
  }

  private static ShardRegionConfig getShardRegionConfigForAzureRegion(
      final AzureRegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries) {
    return getShardRegionConfigForAzureRegion(
        pRegionName,
        pPriority,
        pElectableNodes,
        pAnalyticsNodes,
        pReadOnlyNodes,
        pHiddenSecondaries,
        false);
  }

  public static ShardRegionConfig getShardRegionConfigForAzureRegion(
      final AzureRegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries,
      final boolean pIsOnSsdV2) {
    return getShardRegionConfigForAzureRegion(
        pRegionName,
        AzureNDSInstanceSize.M30,
        AzureDiskType.P4,
        pPriority,
        pElectableNodes,
        pAnalyticsNodes,
        pReadOnlyNodes,
        pHiddenSecondaries,
        pIsOnSsdV2);
  }

  private static ShardRegionConfig getShardRegionConfigForAzureRegion(
      final AzureRegionName pRegionName,
      final AzureNDSInstanceSize pAzureInstanceSize,
      final AzureDiskType pAzureDiskType,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries,
      final boolean pIsOnSsdV2) {
    final boolean nvme = pHiddenSecondaries > 0;
    final AzureNDSInstanceSize instanceSize =
        nvme ? AzureNDSInstanceSize.M60_NVME : pAzureInstanceSize;
    final AzureDiskType azureDiskType = pIsOnSsdV2 ? AzureDiskType.V2 : pAzureDiskType;
    return new ShardRegionConfig(
        pRegionName,
        CloudProvider.AZURE,
        new AzureAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()),
        null,
        pPriority,
        getAzureHardwareSpec(pElectableNodes, azureDiskType, instanceSize, pRegionName),
        getAzureHardwareSpec(pAnalyticsNodes, azureDiskType, instanceSize, pRegionName),
        getAzureHardwareSpec(pReadOnlyNodes, azureDiskType, instanceSize, pRegionName),
        getAzureHardwareSpec(pHiddenSecondaries, azureDiskType, instanceSize, pRegionName),
        null,
        null,
        null,
        null);
  }

  public static ShardRegionConfig getShardRegionConfigForAzureRegion(
      final AzureRegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries,
      final AzureDiskType pDiskType,
      final Integer pDiskIOPS,
      final Integer pDiskThroughput,
      final AzureNDSInstanceSize pInstanceSize) {
    return new ShardRegionConfig(
        pRegionName,
        CloudProvider.AZURE,
        new AzureAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()),
        null,
        pPriority,
        getAzureHardwareSpec(
            pElectableNodes, pDiskType, pDiskIOPS, pDiskThroughput, pInstanceSize, pRegionName),
        getAzureHardwareSpec(
            pAnalyticsNodes, pDiskType, pDiskIOPS, pDiskThroughput, pInstanceSize, pRegionName),
        getAzureHardwareSpec(
            pReadOnlyNodes, pDiskType, pDiskIOPS, pDiskThroughput, pInstanceSize, pRegionName),
        getAzureHardwareSpec(
            pHiddenSecondaries, pDiskType, pDiskIOPS, pDiskThroughput, pInstanceSize, pRegionName),
        null,
        null,
        null,
        null);
  }

  public static ShardRegionConfig getShardRegionConfigForAzureRegion(
      final AzureRegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries,
      final AzureDiskType pDiskType,
      final AzureNDSInstanceSize pInstanceSize) {
    return getShardRegionConfigForAzureRegion(
        pRegionName,
        pPriority,
        pElectableNodes,
        pAnalyticsNodes,
        pReadOnlyNodes,
        pHiddenSecondaries,
        pDiskType,
        null,
        null,
        pInstanceSize);
  }

  private static ShardRegionConfig getShardRegionConfigForGCPRegion(
      final GCPRegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaries) {
    return new ShardRegionConfig(
        pRegionName,
        CloudProvider.GCP,
        new GCPAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()),
        null,
        pPriority,
        getGCPHardwareSpec(pElectableNodes, GCPNDSInstanceSize.M30, pRegionName),
        getGCPHardwareSpec(pAnalyticsNodes, GCPNDSInstanceSize.M30, pRegionName),
        getGCPHardwareSpec(pReadOnlyNodes, GCPNDSInstanceSize.M30, pRegionName),
        getGCPHardwareSpec(pHiddenSecondaries, GCPNDSInstanceSize.M30, pRegionName),
        null,
        null,
        null,
        null);
  }

  public static ShardRegionConfig getShardRegionConfigForFreeRegion(
      final RegionName pRegionName,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaryNodes) {
    return new ShardRegionConfig(
        pRegionName,
        CloudProvider.FREE,
        new FreeAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()),
        null,
        RegionConfig.MAX_PRIORITY,
        getFreeHardwareSpec(pElectableNodes, pRegionName),
        getFreeHardwareSpec(pAnalyticsNodes, pRegionName),
        getFreeHardwareSpec(pReadOnlyNodes, pRegionName),
        getFreeHardwareSpec(pHiddenSecondaryNodes, pRegionName),
        null,
        null,
        null,
        null);
  }

  public static ShardRegionConfig getShardRegionConfigForServerlessRegion(
      final RegionName pRegionName,
      final int pElectableNodes,
      final int pAnalyticsNodes,
      final int pReadOnlyNodes,
      final int pHiddenSecondaryNodes) {
    return new ShardRegionConfig(
        pRegionName,
        CloudProvider.SERVERLESS,
        new ServerlessAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()),
        null,
        RegionConfig.MAX_PRIORITY,
        getServerlessHardwareSpec(pElectableNodes, pRegionName),
        getServerlessHardwareSpec(pAnalyticsNodes, pRegionName),
        getServerlessHardwareSpec(pReadOnlyNodes, pRegionName),
        getServerlessHardwareSpec(pHiddenSecondaryNodes, pRegionName),
        null,
        null,
        null,
        null);
  }

  public static RegionConfig getDedicatedConfigRegionConfigForRegion(
      final CloudProvider pProvider,
      final RegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final CpuArchitecture pPreferredArchitecture,
      final boolean isOnAzureSsdV2) {
    switch (pProvider) {
      case AWS:
        return getDedicatedConfigRegionConfigForAWSRegion(
            ((AWSRegionName) pRegionName),
            pPriority,
            pElectableNodes,
            pPreferredArchitecture,
            new AWSAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()));
      case AZURE:
        return getDedicatedConfigRegionConfigForAzureRegion(
            ((AzureRegionName) pRegionName), pPriority, pElectableNodes, isOnAzureSsdV2);
      case GCP:
        return getDedicatedConfigRegionConfigForGCPRegion(
            ((GCPRegionName) pRegionName), pPriority, pElectableNodes);
      default:
        throw new IllegalArgumentException(
            "Cannot generate hardware spec for provider " + pRegionName.getProvider());
    }
  }

  private static RegionConfig getDedicatedConfigRegionConfigForAWSRegion(
      final AWSRegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final CpuArchitecture pPreferredArchitecture,
      final AWSAutoScaling pAwsAutoScaling) {
    return new RegionConfig(
        pRegionName,
        CloudProvider.AWS,
        pAwsAutoScaling,
        null,
        pPriority,
        getAWSHardwareSpec(
            pElectableNodes, true, AWSNDSInstanceSize.M30, pRegionName, pPreferredArchitecture),
        null);
  }

  private static RegionConfig getDedicatedConfigRegionConfigForAzureRegion(
      final AzureRegionName pRegionName,
      final int pPriority,
      final int pElectableNodes,
      final boolean pIsUsingSsdV2) {
    return new RegionConfig(
        pRegionName,
        CloudProvider.AZURE,
        new AzureAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()),
        null,
        pPriority,
        getAzureHardwareSpec(
            pElectableNodes, true, AzureNDSInstanceSize.M30, pRegionName, pIsUsingSsdV2),
        null);
  }

  private static RegionConfig getDedicatedConfigRegionConfigForGCPRegion(
      final GCPRegionName pRegionName, final int pPriority, final int pElectableNodes) {
    return new RegionConfig(
        pRegionName,
        CloudProvider.GCP,
        new GCPAutoScaling(NDSModelTestFactory.getDefaultAutoScaling()),
        null,
        pPriority,
        getGCPHardwareSpec(pElectableNodes, true, GCPNDSInstanceSize.M30, pRegionName),
        null);
  }

  // Can be used to avoid unwanted duplicate key exceptions due to MTMClusterDao's
  // (poolId, cursorIdRange.max) index
  public static Range getRandomCursorRange() {
    final Date randomDate =
        new Date(Math.abs(System.currentTimeMillis() - (new Random()).nextLong()));
    return Range.getRange(randomDate);
  }

  public static NDSCustomerX509 getCustomerX509ForTest() {
    return new NDSCustomerX509(
        Collections.singletonList(new NDSCustomerX509.CA("testCa", "CN=customerX509", new Date())),
        Collections.singletonList("testPoint"),
        Collections.singletonList(
            new NDSCustomerX509.CRL("testCrl", "CN=customerX509", new Date())));
  }

  public static List<NDSServerlessMTMProfile> getNDSServerlessMTMProfilesForTest() {
    final Stream<NDSServerlessMTMProfile> aws_m50 =
        Stream.of(AWSRegionName.AP_SOUTH_1, AWSRegionName.EU_CENTRAL_1, AWSRegionName.US_EAST_1)
            .map(
                r ->
                    new NDSServerlessMTMProfile(
                        r,
                        ServerlessInstanceSize.SERVERLESS_V2,
                        AWSNDSInstanceSize.M50,
                        1500,
                        50,
                        AWSNDSInstanceSize.M30,
                        ServerlessNDSDefaults.DEFAULT_MONGODB_MAJOR_VERSION,
                        new ObjectId()));

    final Stream<NDSServerlessMTMProfile> aws_m30 =
        Stream.of(
                AWSRegionName.AP_SOUTHEAST_1,
                AWSRegionName.AP_SOUTHEAST_2,
                AWSRegionName.EU_WEST_1,
                AWSRegionName.US_WEST_2)
            .map(
                r ->
                    new NDSServerlessMTMProfile(
                        r,
                        ServerlessInstanceSize.SERVERLESS_V2,
                        AWSNDSInstanceSize.M30,
                        600,
                        10,
                        null,
                        ServerlessNDSDefaults.DEFAULT_MONGODB_MAJOR_VERSION,
                        new ObjectId()));

    final Stream<NDSServerlessMTMProfile> gcp_m30 =
        Stream.of(
                GCPRegionName.CENTRAL_US,
                GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC,
                GCPRegionName.EASTERN_ASIA_PACIFIC,
                GCPRegionName.WESTERN_EUROPE,
                GCPRegionName.SOUTH_AMERICA_EAST_1,
                GCPRegionName.NORTHEASTERN_ASIA_PACIFIC,
                GCPRegionName.ASIA_SOUTH_1)
            .map(
                r ->
                    new NDSServerlessMTMProfile(
                        r,
                        ServerlessInstanceSize.SERVERLESS_V2,
                        GCPNDSInstanceSize.M30,
                        600,
                        10,
                        null,
                        ServerlessNDSDefaults.DEFAULT_MONGODB_MAJOR_VERSION,
                        new ObjectId()));

    final Stream<NDSServerlessMTMProfile> azure_m30 =
        Stream.of(
                AzureRegionName.ASIA_EAST,
                AzureRegionName.EUROPE_NORTH,
                AzureRegionName.EUROPE_WEST,
                AzureRegionName.US_EAST_2,
                AzureRegionName.US_WEST,
                AzureRegionName.CANADA_CENTRAL)
            .map(
                r ->
                    new NDSServerlessMTMProfile(
                        r,
                        ServerlessInstanceSize.SERVERLESS_V2,
                        AzureNDSInstanceSize.M30,
                        600,
                        10,
                        null,
                        ServerlessNDSDefaults.DEFAULT_MONGODB_MAJOR_VERSION,
                        new ObjectId()));

    return Stream.of(aws_m50, aws_m30, gcp_m30, azure_m30)
        .flatMap(s -> s)
        .collect(Collectors.toList());
  }

  public static List<NDSFlexMTMProfile> getNDSFlexMTMProfilesForTest() {
    final Stream<NDSFlexMTMProfile> aws_m50 =
        Stream.of(AWSRegionName.AP_SOUTH_1, AWSRegionName.EU_CENTRAL_1, AWSRegionName.US_EAST_1)
            .map(
                r ->
                    new NDSFlexMTMProfile(
                        r,
                        FlexInstanceSize.FLEX,
                        AWSNDSInstanceSize.M50,
                        1500,
                        50,
                        null,
                        // TODO: CLOUDP-264376 Change this when we enable rapid release for flex
                        FlexNDSDefaults.DEFAULT_MAJOR_VERSION));

    final Stream<NDSFlexMTMProfile> aws_m30 =
        Stream.of(
                AWSRegionName.AP_SOUTHEAST_1,
                AWSRegionName.AP_SOUTHEAST_2,
                AWSRegionName.EU_WEST_1,
                AWSRegionName.US_WEST_2)
            .map(
                r ->
                    new NDSFlexMTMProfile(
                        r,
                        FlexInstanceSize.FLEX,
                        AWSNDSInstanceSize.M30,
                        600,
                        10,
                        null,
                        FlexNDSDefaults.DEFAULT_MAJOR_VERSION));

    final Stream<NDSFlexMTMProfile> gcp_m30 =
        Stream.of(
                GCPRegionName.CENTRAL_US,
                GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC,
                GCPRegionName.EASTERN_ASIA_PACIFIC,
                GCPRegionName.WESTERN_EUROPE,
                GCPRegionName.SOUTH_AMERICA_EAST_1,
                GCPRegionName.NORTHEASTERN_ASIA_PACIFIC,
                GCPRegionName.ASIA_SOUTH_1)
            .map(
                r ->
                    new NDSFlexMTMProfile(
                        r,
                        FlexInstanceSize.FLEX,
                        GCPNDSInstanceSize.M30,
                        600,
                        10,
                        null,
                        FlexNDSDefaults.DEFAULT_MAJOR_VERSION));

    final Stream<NDSFlexMTMProfile> azure_m30 =
        Stream.of(
                AzureRegionName.ASIA_EAST,
                AzureRegionName.EUROPE_NORTH,
                AzureRegionName.EUROPE_WEST,
                AzureRegionName.US_EAST_2,
                AzureRegionName.US_WEST,
                AzureRegionName.CANADA_CENTRAL)
            .map(
                r ->
                    new NDSFlexMTMProfile(
                        r,
                        FlexInstanceSize.FLEX,
                        AzureNDSInstanceSize.M30,
                        600,
                        10,
                        null,
                        FlexNDSDefaults.DEFAULT_MAJOR_VERSION));

    return Stream.of(aws_m50, aws_m30, gcp_m30, azure_m30)
        .flatMap(s -> s)
        .collect(Collectors.toList());
  }

  public static List<NDSSharedMTMProfile> getNDSMTMProfilesForTest() {
    final List<NDSSharedMTMProfile> profiles = new ArrayList<>();
    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M0,
            List.of(AWSRegionName.AP_SOUTH_1, AWSRegionName.EU_CENTRAL_1, AWSRegionName.US_EAST_1),
            AWSNDSInstanceSize.M50,
            1500,
            50,
            AWSNDSInstanceSize.M30,
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M0,
            List.of(
                AWSRegionName.AP_SOUTHEAST_1,
                AWSRegionName.AP_SOUTHEAST_2,
                AWSRegionName.EU_WEST_1,
                AWSRegionName.US_WEST_2,
                AWSRegionName.AF_SOUTH_1,
                AWSRegionName.AP_NORTHEAST_2,
                AWSRegionName.EU_WEST_3,
                AWSRegionName.SA_EAST_1,
                AWSRegionName.AP_EAST_1),
            AWSNDSInstanceSize.M30,
            400,
            10,
            null,
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M2,
            List.of(
                AWSRegionName.AP_SOUTHEAST_1,
                AWSRegionName.AP_SOUTHEAST_2,
                AWSRegionName.AP_SOUTH_1,
                AWSRegionName.EU_CENTRAL_1,
                AWSRegionName.EU_WEST_1,
                AWSRegionName.US_EAST_1,
                AWSRegionName.US_WEST_2,
                AWSRegionName.AF_SOUTH_1,
                AWSRegionName.AP_NORTHEAST_2,
                AWSRegionName.EU_WEST_3,
                AWSRegionName.SA_EAST_1,
                AWSRegionName.AP_EAST_1),
            AWSNDSInstanceSize.M30,
            100,
            5,
            null,
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M5,
            List.of(
                AWSRegionName.AP_SOUTHEAST_1,
                AWSRegionName.AP_SOUTHEAST_2,
                AWSRegionName.AP_SOUTH_1,
                AWSRegionName.EU_CENTRAL_1,
                AWSRegionName.EU_WEST_1,
                AWSRegionName.US_EAST_1,
                AWSRegionName.US_WEST_2,
                AWSRegionName.AP_NORTHEAST_1,
                AWSRegionName.EU_NORTH_1,
                AWSRegionName.ME_SOUTH_1,
                AWSRegionName.AF_SOUTH_1,
                AWSRegionName.AP_NORTHEAST_2,
                AWSRegionName.EU_WEST_3,
                AWSRegionName.SA_EAST_1,
                AWSRegionName.AP_EAST_1),
            AWSNDSInstanceSize.M30,
            100,
            5,
            null,
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M0,
            List.of(
                GCPRegionName.CENTRAL_US,
                GCPRegionName.SOUTHEASTERN_ASIA_PACIFIC,
                GCPRegionName.EASTERN_ASIA_PACIFIC,
                GCPRegionName.WESTERN_EUROPE,
                GCPRegionName.SOUTH_AMERICA_EAST_1,
                GCPRegionName.NORTHEASTERN_ASIA_PACIFIC,
                GCPRegionName.ASIA_SOUTH_1),
            GCPNDSInstanceSize.M30,
            400,
            10,
            null,
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M2,
            List.of(
                GCPRegionName.CENTRAL_US,
                GCPRegionName.EASTERN_ASIA_PACIFIC,
                GCPRegionName.WESTERN_EUROPE,
                GCPRegionName.SOUTH_AMERICA_EAST_1,
                GCPRegionName.NORTHEASTERN_ASIA_PACIFIC,
                GCPRegionName.ASIA_SOUTH_1),
            GCPNDSInstanceSize.M30,
            100,
            5,
            null,
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M5,
            List.of(
                GCPRegionName.CENTRAL_US,
                GCPRegionName.EASTERN_ASIA_PACIFIC,
                GCPRegionName.WESTERN_EUROPE,
                GCPRegionName.SOUTH_AMERICA_EAST_1,
                GCPRegionName.NORTHEASTERN_ASIA_PACIFIC,
                GCPRegionName.ASIA_SOUTH_1,
                GCPRegionName.ASIA_SOUTHEAST_2,
                GCPRegionName.ASIA_NORTHEAST_3),
            GCPNDSInstanceSize.M30,
            100,
            5,
            null,
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M0,
            List.of(
                AzureRegionName.ASIA_EAST,
                AzureRegionName.EUROPE_NORTH,
                AzureRegionName.EUROPE_WEST,
                AzureRegionName.US_EAST_2,
                AzureRegionName.US_WEST,
                AzureRegionName.CANADA_CENTRAL,
                AzureRegionName.INDIA_CENTRAL),
            AzureNDSInstanceSize.M30,
            400,
            10,
            null,
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M2,
            List.of(
                AzureRegionName.EUROPE_NORTH,
                AzureRegionName.US_EAST_2,
                AzureRegionName.US_WEST,
                AzureRegionName.CANADA_CENTRAL,
                AzureRegionName.INDIA_CENTRAL),
            AzureNDSInstanceSize.M30,
            100,
            5,
            null,
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M5,
            List.of(
                AzureRegionName.EUROPE_NORTH,
                AzureRegionName.US_EAST_2,
                AzureRegionName.US_WEST,
                AzureRegionName.CANADA_CENTRAL,
                AzureRegionName.INDIA_CENTRAL),
            AzureNDSInstanceSize.M30,
            100,
            5,
            null,
            NDSModelTestFactory.MONGODB_VERSION_NEXT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M0,
            List.of(AWSRegionName.AP_SOUTH_1),
            AzureNDSInstanceSize.M30,
            100,
            5,
            null,
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M2,
            List.of(AWSRegionName.AP_SOUTH_1),
            AzureNDSInstanceSize.M30,
            100,
            5,
            null,
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M5,
            List.of(AWSRegionName.AP_SOUTH_1),
            AzureNDSInstanceSize.M30,
            100,
            5,
            null,
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString()));

    profiles.addAll(
        registerNDSMTMProfileForTest(
            FreeInstanceSize.M5,
            List.of(AWSRegionName.US_EAST_2),
            AWSNDSInstanceSize.M40,
            100,
            5,
            AWSNDSInstanceSize.M40,
            NDSModelTestFactory.MONGODB_VERSION_CURRENT.getMajorVersionString()));
    return profiles;
  }

  private static List<NDSSharedMTMProfile> registerNDSMTMProfileForTest(
      final FreeInstanceSize pTenantInstanceSize,
      final List<RegionName> pRegionNames,
      final InstanceSize pBackingInstanceSize,
      final int pDefaultCapacity,
      final int pLowCapacityBuffer,
      final InstanceSize pAutoScaleMinSize,
      final String pVersion) {
    final List<NDSSharedMTMProfile> profiles = new ArrayList<>();
    pRegionNames.forEach(
        r ->
            profiles.add(
                new NDSSharedMTMProfile(
                    r,
                    pTenantInstanceSize,
                    pBackingInstanceSize,
                    pDefaultCapacity,
                    pLowCapacityBuffer,
                    pAutoScaleMinSize,
                    pVersion)));
    return profiles;
  }

  public static AWSAdminBackupSnapshot getAWSAdminBackupSnapshot(
      final ObjectId pProjectId,
      final String pClusterName,
      final String pInstanceHostName,
      final AdminBackupSnapshot.Status pStatus) {
    return new AWSAdminBackupSnapshot(
        new ObjectId(),
        pProjectId,
        pClusterName,
        new ObjectId(),
        new ObjectId(),
        pInstanceHostName,
        new Date(),
        new Date(),
        pStatus,
        "commentAWS");
  }

  public static GCPAdminBackupSnapshot getGCPAdminBackupSnapshot(
      final ObjectId pProjectId,
      final String pClusterName,
      final String pInstanceHostName,
      final AdminBackupSnapshot.Status pStatus) {
    return new GCPAdminBackupSnapshot(
        new ObjectId(),
        pProjectId,
        pClusterName,
        new ObjectId(),
        new ObjectId(),
        pInstanceHostName,
        new Date(),
        new Date(),
        pStatus,
        "commentGCP");
  }

  public static AzureAdminBackupSnapshot getAzureAdminBackupSnapshot(
      final ObjectId pProjectId,
      final String pClusterName,
      final String pInstanceHostName,
      final AdminBackupSnapshot.Status pStatus) {
    return new AzureAdminBackupSnapshot(
        new ObjectId(),
        pProjectId,
        pClusterName,
        new ObjectId(),
        new ObjectId(),
        pInstanceHostName,
        new Date(),
        new Date(),
        pStatus,
        "commentAzure");
  }

  public static AWSLeakedItem getAWSLeakedVPC(
      final ObjectId pAWSAccountId, final AWSRegionName pRegionName) {
    return getAWSLeakedItem(pAWSAccountId, pRegionName, AWSLeakedItem.Type.VPC, "vpc-id");
  }

  public static AWSLeakedItem getAWSLeakedItem(
      final ObjectId pAWSAccountId,
      final AWSRegionName pRegionName,
      final AWSLeakedItem.Type pType,
      final String pId) {
    return new AWSLeakedItem.Builder()
        .setAWSAccountId(pAWSAccountId)
        .setRegionName(pRegionName)
        .setType(pType)
        .setId(pId)
        .build();
  }

  public static NDSAWSTempCredentials getPBLETempCredentialsForTest() {
    return new NDSAWSTempCredentials(
        "access-key",
        "access-secret",
        "session-token",
        Date.from(Instant.now().plus(TemporaryCredentialSvc.PBLE_DEFAULT_CREDENTIAL_DURATION)));
  }

  public static FastFlexPreAllocatedRecord getProvisionedFastFlexPreAllocatedRecord(
      final ObjectId pContainerId, final LinkedFlexMTM pMtm) {
    return new FastFlexPreAllocatedRecord(
        new ObjectId(),
        "ac-abc1234",
        FastFlexPreAllocatedRecord.State.AVAILABLE,
        DEFAULT_DNS_PIN,
        null,
        List.of(
            new FastInstance(
                true,
                List.of(
                    new InstanceHostname(HostnameScheme.PUBLIC, "abc1234-shard-00.mongodb.com"))),
            new FastInstance(
                true,
                List.of(
                    new InstanceHostname(HostnameScheme.PUBLIC, "abc1234-shard-01.mongodb.com"))),
            new FastInstance(
                true,
                List.of(
                    new InstanceHostname(HostnameScheme.PUBLIC, "abc1234-shard-02.mongodb.com")))),
        NDSDefaults.generateInternalDeploymentClusterName(new ObjectId(), "abc1234"),
        pMtm,
        pContainerId,
        new Date(),
        new Date(),
        new Date());
  }

  public static FastFlexPreAllocatedRecord getUnprovisionedFastFlexPreAllocatedRecord(
      final ObjectId pContainerId, final String pDnsPin, final LinkedFlexMTM pMtm) {
    final ObjectId id = new ObjectId();

    return new FastFlexPreAllocatedRecord(
        id,
        "ac-abc1234",
        FastSharedPreAllocatedRecord.State.CREATING,
        pDnsPin,
        null,
        List.of(
            new FastInstance(false, new Hostnames()),
            new FastInstance(false, new Hostnames()),
            new FastInstance(false, new Hostnames())),
        NDSDefaults.generateInternalDeploymentClusterName(id, "abc1234"),
        pMtm,
        pContainerId,
        new Date(),
        new Date(),
        null);
  }

  public static FastSharedPreAllocatedRecord getProvisionedFastSharedPreAllocatedRecord(
      final ObjectId pContainerId, final LinkedSharedMTM pMtm) {
    return getProvisionedFastSharedPreAllocatedRecord(pContainerId, DEFAULT_DNS_PIN, pMtm);
  }

  public static FastSharedPreAllocatedRecord getClaimedFastSharedPreAllocatedRecord(
      final ObjectId pContainerId, final String pDNSPin, final LinkedSharedMTM pMtm) {

    final ObjectId id = new ObjectId();

    return new FastSharedPreAllocatedRecord(
        id,
        "ac-abc1234",
        FastSharedPreAllocatedRecord.State.CLAIMED,
        pDNSPin,
        "*." + pDNSPin + ".mongodb.com",
        List.of(
            new FastInstance(
                true,
                List.of(
                    new InstanceHostname(HostnameScheme.PUBLIC, "abc1234-shard-00.mongodb.com"))),
            new FastInstance(
                true,
                List.of(
                    new InstanceHostname(HostnameScheme.PUBLIC, "abc1234-shard-01.mongodb.com"))),
            new FastInstance(
                true,
                List.of(
                    new InstanceHostname(HostnameScheme.PUBLIC, "abc1234-shard-02.mongodb.com")))),
        NDSDefaults.generateInternalDeploymentClusterName(id, "abc1234"),
        pMtm,
        pContainerId,
        new Date(),
        new Date(),
        new Date());
  }

  public static FastSharedPreAllocatedRecord getProvisionedFastSharedPreAllocatedRecord(
      final ObjectId pContainerId, final String pDNSPin, final LinkedSharedMTM pMtm) {

    final ObjectId id = new ObjectId();

    return new FastSharedPreAllocatedRecord(
        id,
        "ac-abc1234",
        FastSharedPreAllocatedRecord.State.AVAILABLE,
        pDNSPin,
        null,
        List.of(
            new FastInstance(
                true,
                List.of(
                    new InstanceHostname(HostnameScheme.PUBLIC, "abc1234-shard-00.mongodb.com"))),
            new FastInstance(
                true,
                List.of(
                    new InstanceHostname(HostnameScheme.PUBLIC, "abc1234-shard-01.mongodb.com"))),
            new FastInstance(
                true,
                List.of(
                    new InstanceHostname(HostnameScheme.PUBLIC, "abc1234-shard-02.mongodb.com")))),
        NDSDefaults.generateInternalDeploymentClusterName(id, "abc1234"),
        pMtm,
        pContainerId,
        new Date(),
        new Date(),
        new Date());
  }

  public static FastSharedPreAllocatedRecord getUnprovisionedFastSharedPreAllocatedRecord(
      final ObjectId pContainerId, final String pDnsPin, final LinkedSharedMTM pMtm) {
    final ObjectId id = new ObjectId();

    return new FastSharedPreAllocatedRecord(
        id,
        "ac-abc1234",
        FastSharedPreAllocatedRecord.State.CREATING,
        pDnsPin,
        null,
        List.of(
            new FastInstance(false, new Hostnames()),
            new FastInstance(false, new Hostnames()),
            new FastInstance(false, new Hostnames())),
        NDSDefaults.generateInternalDeploymentClusterName(id, "abc1234"),
        pMtm,
        pContainerId,
        new Date(),
        new Date(),
        null);
  }

  public static FastSharedPreAllocatedRecord getAvailableRecordWithExpiredCert(
      final ObjectId pGroupId, final String pClusterName) {
    final LinkedSharedMTM mtm =
        new LinkedSharedMTM(
            CloudProvider.AWS,
            AWSRegionName.US_EAST_1.getName(),
            FreeInstanceSize.M0,
            pGroupId,
            pClusterName);

    final ObjectId id = new ObjectId();

    return new FastSharedPreAllocatedRecord(
        id,
        "ac-abc1234",
        FastSharedPreAllocatedRecord.State.AVAILABLE,
        DEFAULT_DNS_PIN,
        "srv.ac-abcde1234",
        List.of(
            new FastInstance(true, new Hostnames()),
            new FastInstance(true, new Hostnames()),
            new FastInstance(true, new Hostnames())),
        NDSDefaults.generateInternalDeploymentClusterName(id, "abc1234"),
        mtm,
        new ObjectId(),
        new Date(),
        new Date(),
        DateUtils.addDays(new Date(), (int) Duration.ofDays(-91).toSeconds()));
  }

  public static FastFlexPreAllocatedRecord getAvailableFastFlexPreallocatedRecordWithExpiredCert(
      final ObjectId pGroupId, final String pClusterName) {
    final LinkedFlexMTM mtm =
        new LinkedFlexMTM(
            CloudProvider.AWS,
            AWSRegionName.US_EAST_1.getName(),
            FlexInstanceSize.FLEX,
            pGroupId,
            pClusterName);

    final ObjectId id = new ObjectId();

    return new FastFlexPreAllocatedRecord(
        id,
        "ac-abc1234",
        FastFlexPreAllocatedRecord.State.AVAILABLE,
        DEFAULT_DNS_PIN,
        "srv.ac-abcde1234",
        List.of(
            new FastInstance(true, new Hostnames()),
            new FastInstance(true, new Hostnames()),
            new FastInstance(true, new Hostnames())),
        NDSDefaults.generateInternalDeploymentClusterName(id, "abc1234"),
        mtm,
        new ObjectId(),
        new Date(),
        new Date(),
        DateUtils.addDays(new Date(), (int) Duration.ofDays(-91).toSeconds()));
  }

  public static SnapshotUpdate generateSnapshotUpdates(
      ObjectId id,
      ObjectId groupId,
      String clusterName,
      String initiationDate,
      ObjectId clusterUniqueId,
      Boolean deleted) {
    return new SnapshotUpdate()
        .setId(id)
        .setClusterUniqueId(clusterUniqueId)
        .setProjectId(groupId)
        .setClusterName(clusterName)
        .setDeleted(deleted)
        .setPurged(false)
        .setEncryptionDetails(BackupSnapshotEncryptionCredentials.EMPTY)
        .setScheduledCreationDate(TimeUtils.fromISOString("2017-07-02T00:00:00Z"))
        .setScheduledDeletionDate(TimeUtils.fromISOString("2017-07-02T00:00:00Z"))
        .setSnapshotInitiationDate(TimeUtils.fromISOString(initiationDate))
        .setMongoDbVersion(VersionUtils.Version.fromString("3.6.3"))
        .setUsedDiskSpace(0L)
        .setCloudProviders(List.of(CloudProvider.AWS))
        .setAwsSnapshotField(
            new SnapshotUpdate.AwsSnapshotFieldBuilder()
                .withEbsSnapshotDescription("awsSnapshot")
                .withRegionName(AWSRegionName.US_EAST_1.getName()))
        .setStatus(BackupSnapshot.Status.COMPLETED)
        .setType(BackupSnapshot.Type.SCHEDULED)
        .setRequestingUser(null)
        .setFrequencyType(BackupFrequencyType.DAILY)
        .setPolicyItemIds(Collections.emptyList())
        .setMainPolicyItemId(null)
        .setOverrideRetentionPolicy(false)
        .setBackupRetentionUnit(BackupRetentionUnit.DAYS)
        .setSnapshotCompletionDate(new Date());
  }

  public static LastAgentStatus getLastAgentStatus(
      final ObjectId pGroupId, final String pHostname, final int pLastGoalVersionAchieved) {
    final EnhancedProcessStatus processStatus =
        new EnhancedProcessStatus(pHostname, pLastGoalVersionAchieved, List.of());
    return new LastAgentStatus.Builder(null, pGroupId, pHostname)
        .enhancedProcessStatus(List.of(processStatus))
        .build();
  }

  public static NDSDBUser getBasicDBUser() {
    return NDSDBUser.builder()
        .username("foo")
        .database("db")
        .roles(Collections.singletonList(NDSDBRole.factoryCreate("db", "role")))
        .scopes(Collections.emptyList())
        .labels(NDSModelTestFactory.getDefaultLabels())
        .isEditable(true)
        .build();
  }

  public static DbCheck getDbCheck(
      final CorruptionDetectionRunResult pResult,
      final RunStatus pStatus,
      final OperationStatus pOperationStatus,
      final Date pOpCompleteDate) {
    final ReplicationSpec dummySpec =
        NDSModelTestFactory.getAWSReplicationSpec(
            new ObjectId(), "zone", 1, 3, List.of(AWSRegionName.AP_EAST_1));
    final ClusterInfo clusterInfo = new ClusterInfo(200, "6.0", List.of(dummySpec), null, null);

    return new DbCheck(
        new ObjectId(),
        new ObjectId(),
        "myCluster",
        new ObjectId(),
        new Date(),
        null,
        "",
        clusterInfo,
        pResult,
        null,
        pOperationStatus != null
            ? List.of(
                new ShardStatus(
                    "rs",
                    new Date(),
                    pOpCompleteDate,
                    pOperationStatus,
                    UUID.randomUUID(),
                    UUID.randomUUID(),
                    null,
                    null,
                    (EncryptedString) null,
                    null,
                    null,
                    null))
            : List.of(),
        pStatus,
        null,
        null,
        CorruptionDetectionOperationOrigin.MANUAL,
        null,
        null,
        null,
        null,
        null);
  }

  public static List<HostLevelStatus> getHostLevelStatuses(
      final Date pLastExportDate,
      final List<Failure> pFailures,
      final List<Inconsistency> pInconsistencies,
      final List<IncompleteValidation> pIncompleteValidations) {
    return List.of(
        new HostLevelStatus(
            new HostLevelStatusId(new ObjectId(), "foo.com"),
            pLastExportDate,
            pFailures,
            pInconsistencies,
            pIncompleteValidations,
            null,
            null,
            null,
            null));
  }

  public static OS getOSForCloudProviderAndInstanceFamily(
      final CloudProvider pCloudProvider, final InstanceFamily pInstanceFamily) {
    return Optional.ofNullable(pInstanceFamily)
        .map(instanceFamily -> instanceFamily.getOS().orElse(getOSForCloudProvider(pCloudProvider)))
        .orElse(null);
  }

  /**
   * Default OS for cloud provider is set in AppSettings. To make this setting easier to access in
   * unit tests, we hardcode the values here. When we update default OS in AppSettings, we should
   * also update it here for consistency, however for most tests the actual value won't matter.
   */
  public static OS getOSForCloudProvider(final CloudProvider pCloudProvider) {
    return switch (pCloudProvider) {
      case AWS, GCP -> OS.AL2023;
      case AZURE -> OS.AL2;
      default -> throw new AssertionError("Cannot get OS for Cloud Provider " + pCloudProvider);
    };
  }

  public static BasicDBObject getSymmetricUnsplitGeoShardedGCPCluster(
      final ObjectId pGroupId, final String pClusterName) {
    final BasicDBObject shardedCd =
        getGCPShardedClusterDescription(pGroupId, pClusterName, HostnameScheme.INTERNAL);
    final BasicDBList replicationSpecList = (BasicDBList) shardedCd.get("replicationSpecList");
    final BasicDBObject firstRs = (BasicDBObject) replicationSpecList.get(0);
    final BasicDBList firstRegionConfigs = (BasicDBList) firstRs.get("regionConfigs");
    final BasicDBObject firstRegionConfig = (BasicDBObject) firstRegionConfigs.get(0);
    final BasicDBObject firstElectableSpec =
        (BasicDBObject) firstRegionConfig.get("electableSpecs");
    final BasicDBObject firstReadOnlySpec = (BasicDBObject) firstRegionConfig.get("readOnlySpecs");
    final BasicDBObject firstAnalyticsSpec =
        (BasicDBObject) firstRegionConfig.get("analyticsSpecs");
    final BasicDBObject firstHiddenSecondarySpec =
        (BasicDBObject) firstRegionConfig.get("hiddenSecondarySpecs");

    final BasicDBObject secondRS =
        new ReplicationSpec(
                oid(0),
                oid(10),
                oid(100),
                "Zone 2",
                3,
                List.of(
                    new ShardRegionConfig(
                        GCPRegionName.ASIA_EAST_2,
                        CloudProvider.GCP,
                        GCPAutoScaling.getDisabledAutoScaling(),
                        GCPAutoScaling.getDisabledAutoScaling(),
                        RegionConfig.MAX_PRIORITY,
                        new GCPHardwareSpec(firstElectableSpec),
                        new GCPHardwareSpec(firstAnalyticsSpec),
                        new GCPHardwareSpec(firstReadOnlySpec),
                        new GCPHardwareSpec(firstHiddenSecondarySpec),
                        null,
                        null,
                        null,
                        null)))
            .toDBObject();

    shardedCd.put("clusterType", "GEOSHARDED");
    firstRs.put("numShards", 3);
    replicationSpecList.add(secondRS);
    return shardedCd;
  }

  public static class TestFreeClusterDescriptionConfig {
    private ObjectId _groupId = new ObjectId();
    private String _clusterName = DEFAULT_CLUSTER_NAME;
    private List<String> _hostUris;
    private CloudProvider _backingProvider = CloudProvider.AWS;
    private RegionName _regionName = AWSNDSDefaults.REGION_NAME;
    private List<RegionConfig> _regionConfigs;
    private FreeInstanceSize _instanceSize = FreeNDSDefaults.INSTANCE_SIZE;
    private LimitsProfile _limitsProfile = LimitsProfile.NORMAL;
    private String _mongoDBVersion = TEST_FREE_MONGODB_MAJOR_VERSION;
    private Boolean _tenantBackupEnabled;
    private Date _nextBackupDate;
    private String _dnsPin = DEFAULT_DNS_PIN;
    private ClusterProvisionType _provisionType = ClusterProvisionType.REGULAR;
    private Boolean _terminationProtectionEnabled;
    private ObjectId _uniqueId = new ObjectId();

    // Metadata for M0 automatic pause
    private Date _userNotifiedAboutPauseDate;
    private Date _ndsAccessRevokedDate;
    private Boolean _excludeFromPause;
    private boolean _deleteRequested = false;
    private boolean _needsUnpauseTenantRestore = false;
    private boolean _paused = false;
    private Date _pausedDate = null;

    private int _connectionLimit = _instanceSize.getMaxIncomingConnections().get();

    public TestFreeClusterDescriptionConfig() {}

    public ObjectId getGroupId() {
      return _groupId;
    }

    public TestFreeClusterDescriptionConfig setGroupId(final ObjectId pGroupId) {
      _groupId = pGroupId;
      return this;
    }

    public String getClusterName() {
      return _clusterName;
    }

    public TestFreeClusterDescriptionConfig setClusterName(final String pClusterName) {
      _clusterName = pClusterName;
      return this;
    }

    public boolean getTerminationProtectionEnabled() {
      return Objects.requireNonNullElse(_terminationProtectionEnabled, false);
    }

    public TestFreeClusterDescriptionConfig setTerminationProtectionEnabled(
        final boolean pTerminationProtectionEnabled) {
      _terminationProtectionEnabled = pTerminationProtectionEnabled;
      return this;
    }

    public ObjectId getUniqueId() {
      return _uniqueId;
    }

    public TestFreeClusterDescriptionConfig setUniqueId(final ObjectId pUniqueId) {
      _uniqueId = pUniqueId;
      return this;
    }

    public List<String> getHostUris() {
      if (_hostUris != null) {
        return _hostUris;
      }
      return Arrays.asList(
          _clusterName + "-shard-00-01.ab123.mmsclouddb.com:27017",
          _clusterName + "-shard-00-02.ab123.mmsclouddb.com:27017");
    }

    public TestFreeClusterDescriptionConfig setHostUris(final List<String> pHostUris) {
      _hostUris = pHostUris;
      return this;
    }

    public CloudProvider getBackingProvider() {
      return _backingProvider;
    }

    public TestFreeClusterDescriptionConfig setBackingProvider(
        final CloudProvider pBackingProvider) {
      _backingProvider = pBackingProvider;
      return this;
    }

    public RegionName getRegionName() {
      return _regionName;
    }

    public TestFreeClusterDescriptionConfig setRegionName(final RegionName pRegionName) {
      _regionName = pRegionName;
      return this;
    }

    public List<RegionConfig> getRegionConfigs() {
      if (_regionConfigs != null) {
        return _regionConfigs;
      }

      final FreeHardwareSpec electableSpecs =
          new FreeHardwareSpec(3, getInstanceSize(), getBackingProvider());
      final FreeHardwareSpec zeroNodeSpecs = electableSpecs.copy().setNodeCount(0).build();

      return List.of(
          new ShardRegionConfig(
              getRegionName(),
              CloudProvider.FREE,
              FreeAutoScaling.getDefaultAutoScaling(),
              null,
              RegionConfig.MAX_PRIORITY,
              electableSpecs,
              zeroNodeSpecs,
              zeroNodeSpecs,
              zeroNodeSpecs,
              null,
              null,
              null,
              null));
    }

    public TestFreeClusterDescriptionConfig setRegionConfigs(
        final List<RegionConfig> pRegionConfigs) {
      _regionConfigs = pRegionConfigs;
      return this;
    }

    public FreeInstanceSize getInstanceSize() {
      return _instanceSize;
    }

    public TestFreeClusterDescriptionConfig setInstanceSize(final FreeInstanceSize pInstanceSize) {
      _instanceSize = pInstanceSize;
      return this;
    }

    public LimitsProfile getLimitsProfile() {
      return _limitsProfile;
    }

    public TestFreeClusterDescriptionConfig setLimitsProfile(final LimitsProfile pLimitsProfile) {
      _limitsProfile = pLimitsProfile;
      return this;
    }

    public String getMongoDBVersion() {
      return _mongoDBVersion;
    }

    public TestFreeClusterDescriptionConfig setMongoDBVersion(final String pMongoDBVersion) {
      _mongoDBVersion = pMongoDBVersion;
      return this;
    }

    public boolean getTenantBackupEnabled() {
      if (_tenantBackupEnabled != null) {

        return _tenantBackupEnabled;
      }
      return getInstanceSize() != FreeInstanceSize.M0;
    }

    public TestFreeClusterDescriptionConfig setTenantBackupEnabled(
        final boolean pTenantBackupEnabled) {
      _tenantBackupEnabled = pTenantBackupEnabled;
      return this;
    }

    public Date getNextBackupDate() {
      if (_nextBackupDate != null) {
        return _nextBackupDate;
      }

      return getInstanceSize() == FreeInstanceSize.M0 ? null : new Date();
    }

    public TestFreeClusterDescriptionConfig setNextBackupDate(final Date pNextBackupDate) {
      _nextBackupDate = pNextBackupDate;
      return this;
    }

    public Date getUserNotifiedAboutPauseDate() {
      return _userNotifiedAboutPauseDate;
    }

    public TestFreeClusterDescriptionConfig setUserNotifiedAboutPauseDate(
        final Date pUserNotifiedAboutPauseDate) {
      _userNotifiedAboutPauseDate = pUserNotifiedAboutPauseDate;
      return this;
    }

    public Date getNDSAccessRevokedDate() {
      return _ndsAccessRevokedDate;
    }

    public TestFreeClusterDescriptionConfig setNDSAccessRevokedDate(
        final Date pNDSAccessRevokedDate) {
      _ndsAccessRevokedDate = pNDSAccessRevokedDate;
      return this;
    }

    public Boolean shouldExcludeFromPause() {
      return _excludeFromPause;
    }

    public String getDnsPin() {
      return _dnsPin;
    }

    public TestFreeClusterDescriptionConfig setDnsPin(final String pDnsPin) {
      _dnsPin = pDnsPin;
      return this;
    }

    public ClusterProvisionType getProvisionType() {
      return _provisionType;
    }

    public TestFreeClusterDescriptionConfig setProvisionType(
        final ClusterProvisionType pProvisionType) {
      _provisionType = pProvisionType;
      return this;
    }

    public TestFreeClusterDescriptionConfig setExcludeFromPause(
        final boolean pShouldExcludeFromPause) {
      _excludeFromPause = pShouldExcludeFromPause;
      return this;
    }

    public boolean isDeleteRequested() {
      return _deleteRequested;
    }

    public TestFreeClusterDescriptionConfig setDeleteRequested(final boolean pIsDeleteRequested) {
      _deleteRequested = pIsDeleteRequested;
      return this;
    }

    public boolean needsUnpauseTenantRestore() {
      return _needsUnpauseTenantRestore;
    }

    public TestFreeClusterDescriptionConfig setNeedsUnpauseTenantRestore(
        final boolean pNeedsUnpauseTenantRestore) {
      _needsUnpauseTenantRestore = pNeedsUnpauseTenantRestore;
      return this;
    }

    public int getConnectionLimit() {
      return _connectionLimit;
    }

    public TestFreeClusterDescriptionConfig setConnectionLimit(final int pConnectionLimit) {
      _connectionLimit = pConnectionLimit;
      return this;
    }

    public boolean isPaused() {
      return _paused;
    }

    public TestFreeClusterDescriptionConfig setPaused(final boolean pPaused) {
      _paused = pPaused;
      return this;
    }

    public Date getPausedDate() {
      return _pausedDate;
    }

    public TestFreeClusterDescriptionConfig setPausedDate(final Date pPausedDate) {
      _pausedDate = pPausedDate;
      return this;
    }
  }

  public static class TestServerlessClusterDescriptionConfig {
    private ObjectId _groupId = new ObjectId();
    private String _clusterName = DEFAULT_CLUSTER_NAME;
    private List<String> _hostUris = null;
    private CloudProvider _backingProvider = CloudProvider.AWS;
    private RegionName _regionName = AWSNDSDefaults.REGION_NAME;
    private List<RegionConfig> _regionConfigs;
    private ServerlessInstanceSize _instanceSize = ServerlessNDSDefaults.INSTANCE_SIZE;
    private String _loadBalancedHostname = null;
    private String _loadBalancedMeshHostname = null;
    private String _dnsPin = "abc12";
    private ClusterProvisionType _clusterProvisionType = ClusterProvisionType.REGULAR;
    private boolean _deleteRequested = false;
    private ObjectId _uniqueId = new ObjectId();
    private ServerlessBackupOptions _serverlessBackupOptions =
        new ServerlessBackupOptions(false, Optional.empty());

    public TestServerlessClusterDescriptionConfig() {}

    public ObjectId getGroupId() {
      return _groupId;
    }

    public TestServerlessClusterDescriptionConfig setGroupId(final ObjectId pGroupId) {
      _groupId = pGroupId;
      return this;
    }

    public String getClusterName() {
      return _clusterName;
    }

    public TestServerlessClusterDescriptionConfig setClusterName(final String pClusterName) {
      _clusterName = pClusterName;
      return this;
    }

    public ObjectId getUniqueId() {
      return _uniqueId;
    }

    public TestServerlessClusterDescriptionConfig setClusterUniqueId(
        final ObjectId pClusterUniqueId) {
      _uniqueId = pClusterUniqueId;
      return this;
    }

    public List<String> getHostUris() {
      if (_hostUris != null) {
        return _hostUris;
      }
      return Arrays.asList(
          getClusterName() + "-shard-00-00-ab123.mmsclouddb.com:27017",
          getClusterName() + "-shard-00-01-ab123.mmsclouddb.com:27017",
          getClusterName() + "-shard-00-02-ab123.mmsclouddb.com:27017");
    }

    public TestServerlessClusterDescriptionConfig setHostUris(final List<String> pHostUris) {
      _hostUris = pHostUris;
      return this;
    }

    public CloudProvider getBackingProvider() {
      return _backingProvider;
    }

    public TestServerlessClusterDescriptionConfig setBackingProvider(
        final CloudProvider pBackingProvider) {
      _backingProvider = pBackingProvider;
      return this;
    }

    public RegionName getRegionName() {
      return _regionName;
    }

    public TestServerlessClusterDescriptionConfig setRegionName(final RegionName pRegionName) {
      _regionName = pRegionName;
      return this;
    }

    public List<RegionConfig> getRegionConfigs() {
      if (_regionConfigs != null) {
        return _regionConfigs;
      }

      return List.of(
          new ShardRegionConfig(
              getRegionName(),
              CloudProvider.SERVERLESS,
              ServerlessAutoScaling.getDefaultAutoScaling(),
              null,
              RegionConfig.MAX_PRIORITY,
              getServerlessHardwareSpec(3, getRegionName()),
              getServerlessHardwareSpec(0, getRegionName()),
              getServerlessHardwareSpec(0, getRegionName()),
              getServerlessHardwareSpec(0, getRegionName()),
              null,
              null,
              null,
              null));
    }

    public TestServerlessClusterDescriptionConfig setRegionConfigs(
        final List<RegionConfig> pRegionConfigs) {
      _regionConfigs = pRegionConfigs;
      return this;
    }

    public String getLoadBalancedHostname() {
      return _loadBalancedHostname;
    }

    public TestServerlessClusterDescriptionConfig setLoadBalancedHostname(
        final String pLoadBalancedHostname) {
      _loadBalancedHostname = pLoadBalancedHostname;
      return this;
    }

    public String getLoadBalancedMeshHostname() {
      return _loadBalancedMeshHostname;
    }

    public TestServerlessClusterDescriptionConfig setLoadBalancedMeshHostname(
        final String pLoadBalancedMeshHostname) {
      _loadBalancedMeshHostname = pLoadBalancedMeshHostname;
      return this;
    }

    public String getDnsPin() {
      return _dnsPin;
    }

    public TestServerlessClusterDescriptionConfig setDnsPin(final String pDnsPin) {
      _dnsPin = pDnsPin;
      return this;
    }

    public ServerlessInstanceSize getInstanceSize() {
      return _instanceSize;
    }

    public TestServerlessClusterDescriptionConfig setInstanceSize(
        final ServerlessInstanceSize pInstanceSize) {
      _instanceSize = pInstanceSize;
      return this;
    }

    public ClusterProvisionType getClusterProvisionType() {
      return _clusterProvisionType;
    }

    public TestServerlessClusterDescriptionConfig setClusterProvisionType(
        final ClusterProvisionType pClusterProvisionType) {
      _clusterProvisionType = pClusterProvisionType;
      return this;
    }

    public boolean isDeleteRequested() {
      return _deleteRequested;
    }

    public TestServerlessClusterDescriptionConfig setDeleteRequested(
        final boolean pDeleteRequested) {
      _deleteRequested = pDeleteRequested;
      return this;
    }

    public ServerlessBackupOptions getBackupOptions() {
      return _serverlessBackupOptions;
    }

    public TestServerlessClusterDescriptionConfig setBackupOptions(
        final ServerlessBackupOptions pServerlessBackupOptions) {
      _serverlessBackupOptions = pServerlessBackupOptions;
      return this;
    }
  }

  public static class TestFlexClusterDescriptionConfig {
    private ObjectId _groupId = new ObjectId();
    private String _clusterName = DEFAULT_CLUSTER_NAME;
    private List<String> _hostUris;
    private CloudProvider _backingProvider = CloudProvider.AWS;
    private RegionName _regionName = AWSNDSDefaults.REGION_NAME;
    private List<RegionConfig> _regionConfigs;
    private FlexInstanceSize _instanceSize = FlexNDSDefaults.INSTANCE_SIZE;
    private LimitsProfile _limitsProfile = LimitsProfile.NORMAL;
    private String _mongoDBVersion = TEST_FLEX_MONGODB_MAJOR_VERSION;
    private Date _nextBackupDate;
    private String _dnsPin = DEFAULT_DNS_PIN;
    private ClusterProvisionType _provisionType = ClusterProvisionType.REGULAR;
    private Boolean _terminationProtectionEnabled;
    private ObjectId _uniqueId = new ObjectId();
    private boolean _deleteRequested = false;
    private Date _createdDate = null;
    private Date _deletedDate = null;
    private String _loadBalancedHostname = null;
    private String _loadBalancedMeshHostname = null;
    private FlexTenantMigrationState _flexTenantMigrationState = null;

    public TestFlexClusterDescriptionConfig() {}

    public ObjectId getGroupId() {
      return _groupId;
    }

    public TestFlexClusterDescriptionConfig setGroupId(final ObjectId pGroupId) {
      _groupId = pGroupId;
      return this;
    }

    public String getClusterName() {
      return _clusterName;
    }

    public TestFlexClusterDescriptionConfig setClusterName(final String pClusterName) {
      _clusterName = pClusterName;
      return this;
    }

    public boolean getTerminationProtectionEnabled() {
      return Objects.requireNonNullElse(_terminationProtectionEnabled, false);
    }

    public TestFlexClusterDescriptionConfig setTerminationProtectionEnabled(
        final boolean pTerminationProtectionEnabled) {
      _terminationProtectionEnabled = pTerminationProtectionEnabled;
      return this;
    }

    public ObjectId getUniqueId() {
      return _uniqueId;
    }

    public TestFlexClusterDescriptionConfig setUniqueId(final ObjectId pUniqueId) {
      _uniqueId = pUniqueId;
      return this;
    }

    public List<String> getHostUris() {
      if (_hostUris != null) {
        return _hostUris;
      }
      return Arrays.asList(
          _clusterName + "-shard-00-01.ab123.mmsclouddb.com:27017",
          _clusterName + "-shard-00-02.ab123.mmsclouddb.com:27017");
    }

    public TestFlexClusterDescriptionConfig setHostUris(final List<String> pHostUris) {
      _hostUris = pHostUris;
      return this;
    }

    public CloudProvider getBackingProvider() {
      return _backingProvider;
    }

    public TestFlexClusterDescriptionConfig setBackingProvider(
        final CloudProvider pBackingProvider) {
      _backingProvider = pBackingProvider;
      return this;
    }

    public RegionName getRegionName() {
      return _regionName;
    }

    public TestFlexClusterDescriptionConfig setRegionName(final RegionName pRegionName) {
      _regionName = pRegionName;
      return this;
    }

    public List<RegionConfig> getRegionConfigs() {
      if (_regionConfigs != null) {
        return _regionConfigs;
      }

      final FlexHardwareSpec electableSpecs =
          new FlexHardwareSpec(3, getInstanceSize(), getBackingProvider());
      final FlexHardwareSpec zeroNodeSpecs = electableSpecs.copy().setNodeCount(0).build();

      return List.of(
          new ShardRegionConfig(
              getRegionName(),
              CloudProvider.FLEX,
              FlexAutoScaling.getDefaultAutoScaling(),
              null,
              RegionConfig.MAX_PRIORITY,
              electableSpecs,
              zeroNodeSpecs,
              zeroNodeSpecs,
              zeroNodeSpecs,
              null,
              null,
              null,
              null));
    }

    public TestFlexClusterDescriptionConfig setRegionConfigs(
        final List<RegionConfig> pRegionConfigs) {
      _regionConfigs = pRegionConfigs;
      return this;
    }

    public FlexInstanceSize getInstanceSize() {
      return _instanceSize;
    }

    public TestFlexClusterDescriptionConfig setInstanceSize(final FlexInstanceSize pInstanceSize) {
      _instanceSize = pInstanceSize;
      return this;
    }

    public LimitsProfile getLimitsProfile() {
      return _limitsProfile;
    }

    public TestFlexClusterDescriptionConfig setLimitsProfile(final LimitsProfile pLimitsProfile) {
      _limitsProfile = pLimitsProfile;
      return this;
    }

    public String getMongoDBVersion() {
      return _mongoDBVersion;
    }

    public TestFlexClusterDescriptionConfig setMongoDBVersion(final String pMongoDBVersion) {
      _mongoDBVersion = pMongoDBVersion;
      return this;
    }

    public boolean getTenantBackupEnabled() {
      return true;
    }

    public TestFreeClusterDescriptionConfig setTenantBackupEnabled(
        final boolean pTenantBackupEnabled) {
      throw new UnsupportedOperationException("Backups are always enabled for flex");
    }

    public Date getNextBackupDate() {
      if (_nextBackupDate != null) {
        return _nextBackupDate;
      }

      return new Date();
    }

    public TestFlexClusterDescriptionConfig setNextBackupDate(final Date pNextBackupDate) {
      _nextBackupDate = pNextBackupDate;
      return this;
    }

    public String getDnsPin() {
      return _dnsPin;
    }

    public TestFlexClusterDescriptionConfig setDnsPin(final String pDnsPin) {
      _dnsPin = pDnsPin;
      return this;
    }

    public ClusterProvisionType getProvisionType() {
      return _provisionType;
    }

    public TestFlexClusterDescriptionConfig setProvisionType(
        final ClusterProvisionType pProvisionType) {
      _provisionType = pProvisionType;
      return this;
    }

    public boolean isDeleteRequested() {
      return _deleteRequested;
    }

    public TestFlexClusterDescriptionConfig setDeleteRequested(final boolean pIsDeleteRequested) {
      _deleteRequested = pIsDeleteRequested;
      return this;
    }

    public Date getCreatedDate() {
      return _createdDate;
    }

    public TestFlexClusterDescriptionConfig setCreatedDate(final Date pCreatedDate) {
      _createdDate = pCreatedDate;
      return this;
    }

    public Date getDeletedDate() {
      return _deletedDate;
    }

    public TestFlexClusterDescriptionConfig setDeletedDate(final Date pDeletedDate) {
      _deletedDate = pDeletedDate;
      return this;
    }

    public TestFlexClusterDescriptionConfig setFlexTenantMigrationState(
        final FlexTenantMigrationState pFlexTenantMigrationState) {
      _flexTenantMigrationState = pFlexTenantMigrationState;
      return this;
    }

    public FlexTenantMigrationState getFlexTenantMigrationState() {
      return _flexTenantMigrationState;
    }

    public String getLoadBalancedHostname() {
      return _loadBalancedHostname;
    }

    public TestFlexClusterDescriptionConfig setLoadBalancedHostname(
        final String pLoadBalancedHostname) {
      _loadBalancedHostname = pLoadBalancedHostname;
      return this;
    }

    public String getLoadBalancedMeshHostname() {
      return _loadBalancedMeshHostname;
    }

    public TestFlexClusterDescriptionConfig setLoadBalancedMeshHostname(
        final String pLoadBalancedMeshHostname) {
      _loadBalancedMeshHostname = pLoadBalancedMeshHostname;
      return this;
    }
  }

  public static ShardedClusterDescription getGeoShardedClusterDescription(
      final List<ReplicationSpec> pReplicationSpecs,
      final GeoSharding pGeoSharding,
      final String pClusterName,
      final String pDNSPin,
      final ObjectId pGroupId) {
    final RegionConfig highPriRegionConfig =
        pReplicationSpecs.get(0).getHighestPriorityRegionConfig();
    final ReplicationSpec configReplSpec =
        new ReplicationSpec(
                new ObjectId(),
                new ObjectId(),
                new ObjectId(),
                NDSDefaults.ZONE_NAME,
                1,
                List.of(highPriRegionConfig))
            .copy()
            .updateAllHardware(
                new AWSHardwareSpec.Builder().setIsDedicatedConfigServerHardware(true))
            .build();
    final AWSHardwareSpec.Builder hardwareUpdate =
        new AWSHardwareSpec.Builder()
            .setInstanceSize(AWSNDSInstanceSize.M30)
            .setDiskIOPS(AWSNDSInstanceSize.M30.getGP3StandardEBSIOPS(10));
    return new ShardedClusterDescription.Builder()
        .setReplicationSpecList(pReplicationSpecs)
        .mixin(ClusterDescriptionBuilderTestMixin::new)
        .updateAllHardware(hardwareUpdate)
        .setDedicatedConfigServerDiskSizeGB(10.0)
        .setDedicatedConfigServerReplicationSpec(configReplSpec, false)
        .updateDedicatedConfigServerHardware(hardwareUpdate)
        .setClusterType(ClusterDescription.ClusterType.GEOSHARDED)
        .setInternalClusterRole(InternalClusterRole.NONE)
        .setName(pClusterName)
        .setClusterNamePrefix(pClusterName.toLowerCase())
        .setDnsPin(pDNSPin)
        .setCreateDate(new Date())
        .setDiskSizeGB(10)
        .setUniqueId(new ObjectId())
        .setGroupId(pGroupId)
        .setLastUpdateDate(new Date())
        .setMongoDBVersion(TEST_MONGODB_VERSION.getVersion())
        .setMongoDBMajorVersion(TEST_MONGODB_VERSION.getMajorVersionString())
        .setMongoUriHosts(new String[0])
        .setPrivateMongoUriHosts(new String[0])
        .setMongoUriLastUpdateDate(new Date())
        .setState(ClusterDescription.State.WORKING)
        .setRestoreJobIds(Collections.emptyList())
        .setNeedsMongoDBConfigPublishAfter(new Date())
        .setAutoScalingForProvider(
            CloudProvider.AWS,
            AWSAutoScalingView.getDefaultAutoScalingView().toAutoScaling(),
            NodeTypeFamily.BASE)
        .setBiConnector(BiConnectorView.getDefaultBiConnectorView().toBiConnector())
        .setGeoSharding(pGeoSharding)
        .setEncryptionAtRestProvider(ClusterDescription.EncryptionAtRestProvider.NONE)
        .setHostnameSchemeForAgents(HostnameScheme.INTERNAL)
        .setHostnameSubdomainLevel(SubdomainLevel.MONGODB)
        .setDeploymentClusterName(
            NDSDefaults.generateInternalDeploymentClusterName(pGroupId, pClusterName))
        .build();
  }

  public static ObjectId getObjectIdForMaintenanceHour(final Integer pHourOfDay) {
    // generate an ObjectId with a date value such that the epoch seconds % 8 + 9 == pHourOfDay
    return ObjectId.getSmallestWithDate(new Date(1732291200000L + (pHourOfDay - 9) * 1000L));
  }

  public static NDSGroup getMockedGroupWithSystemHourOfDay(final Integer pHourOfDay) {
    return getAWSMockedGroup(getObjectIdForMaintenanceHour(pHourOfDay));
  }

  public static AzureInstanceCapacitySpec getAzureInstanceCapacitySpec() {
    return new AzureInstanceCapacitySpec(
        ObjectId.get(), AzureRegionName.US_EAST, "1", "Standard_B1ms", 1);
  }

  public static AzureInstanceCapacitySpec getAzureInstanceCapacitySpec(
      final ObjectId subscriptionId, final AzureRegionName regionName) {
    return new AzureInstanceCapacitySpec(subscriptionId, regionName, "1", "Standard_B1ms", 1);
  }
}
