- atm:
    - atm:
        - "//server/src/unit/com/xgen/svc/atm/..."
        - "//server/src/unit/com/xgen/cloud/atm/..."
        - "//server/src/unit/com/xgen/cloud/deployment/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/deployment/..."
        - "//server/src/unit/com/xgen/cloud/agent/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/agent/..."
        - "//server/src/unit/com/xgen/cloud/common/agentaudit/..."
- brs:
    - brs:
        - "//server/src/unit/com/xgen/cloud/brs/autodownloader/..."
        - "//server/src/unit/com/xgen/cloud/brs/core/..."
        - "//server/src/unit/com/xgen/cloud/brs/daemon/..."
        - "//server/src/unit/com/xgen/cloud/brs/publish/..."
        - "//server/src/unit/com/xgen/cloud/brs/restore/..."
        - "//server/src/unit/com/xgen/cloud/brs/thirdparty/..."
        - "//server/src/unit/com/xgen/cloud/brs/web/..."
        - "//server/src/unit/com/xgen/cloud/nds/serverlessbackup/..."
        - "//server/src/unit/com/xgen/cloud/common/brs/..."
- slsbackup:
    - slsbackup:
        - "//server/src/unit/com/xgen/cloud/services/slsbackup/..."
- sls:
    - sls:
        - "//server/src/unit/com/xgen/cloud/sls/..."
- mms:
    - mms:
        - "//server/src/unit/com/xgen/svc/core/..."
        - "//server/src/unit/com/xgen/svc/mms:all"
        - "//server/src/unit/com/xgen/svc/mms/api/..."
        - "//server/src/unit/com/xgen/svc/mms/dao/..."
        - "//server/src/unit/com/xgen/svc/mms/form/..."
        - "//server/src/unit/com/xgen/svc/mms/misc/..."
        - "//server/src/unit/com/xgen/svc/mms/model/..."
        - "//server/src/unit/com/xgen/svc/mms/svc:all"
        - "//server/src/unit/com/xgen/svc/mms/svc/admin/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/allclusters/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/authz/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/discovery/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/grpc/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/host/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/marketing/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/metrics/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/prometheus/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/searchanalytics/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/smartlink/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/support/..."
        - "//server/src/unit/com/xgen/svc/mms/util/..."
        - "//server/src/unit/com/xgen/svc/common/..."
        - "//server/src/unit/com/xgen/cloud/logging/..."
        - "//server/src/unit/com/xgen/cloud/naturallanguagequery/..."
- mms_res:
  - mms_res:
        - "//server/src/unit/com/xgen/svc/mms/res:all"
        - "//server/src/unit/com/xgen/svc/mms/res/auth/..."
        - "//server/src/unit/com/xgen/svc/mms/res/cors/..."
        - "//server/src/unit/com/xgen/svc/mms/res/exception/..."
        - "//server/src/unit/com/xgen/svc/mms/res/filter/..."
        - "//server/src/unit/com/xgen/svc/mms/res/inject/..."
        - "//server/src/unit/com/xgen/svc/mms/res/marketplace/..."
        - "//server/src/unit/com/xgen/svc/mms/res/scim/..."
        - "//server/src/unit/com/xgen/svc/mms/res/validation/..."
        - "//server/src/unit/com/xgen/svc/mms/res/view/..."
        - "//server/src/unit/com/xgen/svc/res/..."
- cps:
    - cps:
        - "//server/src/unit/com/xgen/svc/nds/svc/cps/..."
        - "//server/src/unit/com/xgen/cloud/cps/..."
        - "//server/src/unit/com/xgen/svc/cps/..."
- mms_iam:
    - mms_iam:
        - "//server/src/unit/com/xgen/cloud/access/accesslist/..."
        - "//server/src/unit/com/xgen/cloud/access/api/..."
        - "//server/src/unit/com/xgen/cloud/access/authn/..."
        - "//server/src/unit/com/xgen/cloud/common/authn/..."
        - "//server/src/unit/com/xgen/cloud/access/authz/..."
        - "//server/src/unit/com/xgen/cloud/common/authz/..."
        - "//server/src/unit/com/xgen/cloud/access/recaptcha/..."
        - "//server/src/unit/com/xgen/cloud/access/role/..."
        - "//server/src/unit/com/xgen/cloud/access/rolecheck/..."
        - "//server/src/unit/com/xgen/cloud/access/usergroups/..."
        - "//server/src/unit/com/xgen/cloud/account/..."
        - "//server/src/unit/com/xgen/cloud/apiuser/..."
        - "//server/src/unit/com/xgen/cloud/authz_tools/..."
        - "//server/src/unit/com/xgen/cloud/common/access/..."
        - "//server/src/unit/com/xgen/cloud/common/groupcreation/..."
        - "//server/src/unit/com/xgen/cloud/common/jwt/..."
        - "//server/src/unit/com/xgen/cloud/common/okta/..."
        - "//server/src/unit/com/xgen/cloud/common/role/..."
        - "//server/src/unit/com/xgen/cloud/configlimit/..."
        - "//server/src/unit/com/xgen/cloud/criticalanalytics/..."
        - "//server/src/unit/com/xgen/cloud/federation/..."
        - "//server/src/unit/com/xgen/cloud/group/..."
        - "//server/src/unit/com/xgen/cloud/instrument/..."
        - "//server/src/unit/com/xgen/cloud/invitation/..."
        - "//server/src/unit/com/xgen/cloud/openrewrite/..."
        - "//server/src/unit/com/xgen/cloud/organization/..."
        - "//server/src/unit/com/xgen/cloud/partnerintegrations/vercelnative/_private/svc:VercelNativeAccountSvcImplUnitTests.java"
        - "//server/src/unit/com/xgen/cloud/passwordreset/..."
        - "//server/src/unit/com/xgen/cloud/roles/..."
        - "//server/src/unit/com/xgen/cloud/organizationsettings/..."
        - "//server/src/unit/com/xgen/cloud/user/..."
        - "//server/src/unit/com/xgen/cloud/usergroupsync/..."
        - "//server/src/unit/com/xgen/cloud/usersandinvites/..."
        - "//server/src/unit/com/xgen/module/account/..."
        - "//server/src/unit/com/xgen/module/federation/..."
        - "//server/src/unit/com/xgen/module/iam/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/user/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/vercel/..."
        - "//server/src/unit/com/xgen/cloud/userlookup/..."
        - "//server/src/unit/com/xgen/cloud/partnerintegrations/common/..."
    - endpoint_auth_coverage_tool: "//server/src/unit/com/xgen/cloud/authz_tools/_public/tools:EndpointAuthCoverageToolUnitTests"
- mms_cap:
    - mms_cap:
        - "//server/src/unit/com/xgen/cloud/access/activity/..."
        - "//server/src/unit/com/xgen/cloud/activity/..."
        - "//server/src/unit/com/xgen/cloud/activityfeed/..."
        - "//server/src/unit/com/xgen/cloud/alerts/..."
        - "//server/src/unit/com/xgen/cloud/brs/activity/..."
        - "//server/src/unit/com/xgen/cloud/clusters/..."
        - "//server/src/unit/com/xgen/cloud/communication/..."
        - "//server/src/unit/com/xgen/cloud/services/communication/..."
        - "//server/src/unit/com/xgen/cloud/email/..."
        - "//server/src/unit/com/xgen/cloud/event/..."
        - "//server/src/unit/com/xgen/cloud/eventcommitter/..."
        - "//server/src/unit/com/xgen/cloud/eventbackfill/..."
        - "//server/src/unit/com/xgen/cloud/explorer/activity/..."
        - "//server/src/unit/com/xgen/cloud/fts/activity/..."
        - "//server/src/unit/com/xgen/cloud/integration/..."
        - "//server/src/unit/com/xgen/cloud/integrations/..."
        - "//server/src/unit/com/xgen/cloud/legacyintegration/..."
        - "//server/src/unit/com/xgen/cloud/nds/activity/..."
        - "//server/src/unit/com/xgen/cloud/realm/activity/..."
        - "//server/src/unit/com/xgen/cloud/slack/..."
        - "//server/src/unit/com/xgen/cloud/team/activity/..."
        - "//server/src/unit/com/xgen/svc/mms/model/alert/..."
        - "//server/src/unit/com/xgen/svc/mms/model/event/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/alert/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/event/..."
        - "//server/src/unit/com/xgen/cloud/notification/..."
        - "//server/src/unit/com/xgen/cloud/auditinfosvc/..."
- soa_java_sdk:
    - soa_java_sdk:
        - "//server/src/unit/com/xgen/cloud/services/core/..."
- mms_fep:
    - mms_fep:
        - "//server/src/unit/com/xgen/cloud/common/clientmetrics/..."
        - "//server/src/unit/com/xgen/cloud/ui/..."
- atlas_growth:
    - atlas_growth:
        - "//server/src/unit/com/xgen/cloud/abtesting/..."
        - "//server/src/unit/com/xgen/cloud/azurenative/..."
        - "//server/src/unit/com/xgen/cloud/config/..."
        - "//server/src/unit/com/xgen/cloud/partnerintegrations/vercelnative/... -//server/src/unit/com/xgen/cloud/partnerintegrations/vercelnative/_private/svc:VercelNativeAccountSvcImplUnitTests.java"
        - "//server/src/unit/com/xgen/cloud/externalanalytics/..."
        - "//server/src/unit/com/xgen/cloud/externalanalyticsjobhandlers/..."
        - "//server/src/unit/com/xgen/cloud/featureFlag/_public/view/..."
        - "//server/src/unit/com/xgen/cloud/sandbox/..."
        - "//server/src/unit/com/xgen/cloud/toolbartips/..."
        - "//server/src/unit/com/xgen/cloud/services/config/..."
        - "//server/src/unit/com/xgen/cloud/util/_public/featureflag/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/experiments/..."
        - "//server/src/unit/com/xgen/svc/nds/analytics/..."
        - "//server/src/unit/com/xgen/svc/nds/clusteractive/..."
        - "//server/src/unit/com/xgen/cloud/clusterdraft/..."
        - "//server/src/unit/com/xgen/cloud/personalizationwizard/..."
    - configservicesdk:
        - "//server/src/unit/com/xgen/cloud/configsdk/..."
        - "//server/src/unit/com/xgen/devtools/configservicesdk/..."

- authn:
    - authn:
        - "//server/src/unit/com/xgen/cloud/services/authn/..."
    - authn_mms:
        - "//server/src/unit/com/xgen/cloud/authn/..."

- authz:
    - authz:
        - "//server/src/unit/com/xgen/cloud/services/authz/..."
    - authz_mms:
        - "//server/src/unit/com/xgen/cloud/authz/..."
- metrics:
    - metrics:
        - "//server/src/unit/com/xgen/cloud/services/metrics/..."

- clusterconnection:
    - clusterconnection:
        - "//server/src/unit/com/xgen/cloud/services/clusterconnection/..."

- event:
    - event:
        - "//server/src/unit/com/xgen/cloud/services/event/..."
- websocket:
    - websocket:
        - "//server/src/unit/com/xgen/cloud/services/websocket/..."
- nds_api_ui:
    - nds_api_ui:
        - "//server/src/unit/com/xgen/svc/nds/res/..."
        - "//server/src/unit/com/xgen/svc/mms/api/res/atlas/..."
        - "//server/src/unit/com/xgen/svc/mms/api/view/atlas/..."
- nds_common:
    - nds_common:
        - "//server/src/unit/com/xgen/svc/nds/svc/... -//server/src/unit/com/xgen/svc/nds/svc/cps/... -//server/src/unit/com/xgen/svc/nds/svc/onlinearchive/... -//server/src/unit/com/xgen/svc/nds/svc/planning/... -//server/src/unit/com/xgen/svc/nds/svc/streams/... -//server/src/unit/com/xgen/svc/nds/svc/streamsexportmetrics/..."
        - "//server/src/unit/com/xgen/cloud/nds/cron/..."
        - "//server/src/unit/com/xgen/svc/nds/xcloud/..."
        - "//server/src/unit/com/xgen/cloud/nds/agentApiKeys/..."
        - "//server/src/unit/com/xgen/cloud/nds/canine/..."
        - "//server/src/unit/com/xgen/cloud/nds/vmimage/..."
        - "//server/src/unit/com/xgen/cloud/nds/resourcepolicy/_public/..."
        - "//server/src/unit/com/xgen/cloud/nds/capacity/..."
- nds_platform:
    - nds_platform:
        - "//server/src/unit/com/xgen/cloud/nds/admin/..."
        - "//server/src/unit/com/xgen/cloud/nds/deployment/..."
        - "//server/src/unit/com/xgen/cloud/nds/cloudprovider/..."
        - "//server/src/unit/com/xgen/cloud/nds/common/..."
        - "//server/src/unit/com/xgen/cloud/nds/regionalization/..."
        - "//server/src/unit/com/xgen/cloud/nds/project/..."
        - "//server/src/unit/com/xgen/svc/nds/model/..."
        - "//server/src/unit/com/xgen/svc/nds/util/..."
        - "//server/src/unit/com/xgen/cloud/nds/dao/..."
        - "//server/src/unit/com/xgen/cloud/mongod/..."
        - "//server/src/unit/com/xgen/svc/nds:NDSSettingsUnitTests"
- nds_tenants:
    - nds_tenants:
        - "//server/src/unit/com/xgen/cloud/nds/mtmcompaction/..."
        - "//server/src/unit/com/xgen/svc/nds/serverless/..."
        - "//server/src/unit/com/xgen/svc/nds/flex/..."
        - "//server/src/unit/com/xgen/cloud/nds/serverless/..."
        - "//server/src/unit/com/xgen/cloud/nds/flex/..."
        - "//server/src/unit/com/xgen/cloud/nds/tenant/..."
        - "//server/src/unit/com/xgen/svc/nds/tenant/..."
- nds_clusters_networking:
    - nds_clusters_networking:
        - "//server/src/unit/com/xgen/cloud/nds/privatelink/..."
        - "//server/src/unit/com/xgen/cloud/nds/dns/..."
        - "//server/src/unit/com/xgen/cloud/nds/hostname/..."
        - "//server/src/unit/com/xgen/cloud/nds/pointsofpresence/..."
        - "//server/src/unit/com/xgen/cloud/nds/accesstransparency/..."
        - "//server/src/unit/com/xgen/cloud/nds/mpa/..."
- nds_performance:
    - nds_performance:
        - "//server/src/unit/com/xgen/cloud/nds/autoscaling/..."
        - "//server/src/unit/com/xgen/cloud/nds/mongotune/binary/..."
        - "//server/src/unit/com/xgen/cloud/nds/mongotune/policies/..."
- nds_ifr:
    - nds_ifr:
        - "//server/src/unit/com/xgen/cloud/nds/ifr/..."
- nds_regional_outage:
    - nds_regional_outage:
        - "//server/src/unit/com/xgen/svc/nds/healthCheck/..."
        - "//server/src/unit/com/xgen/cloud/nds/logingestion/..."
        - "//server/src/unit/com/xgen/cloud/nds/rollingresync/..."
        - "//server/src/unit/com/xgen/cloud/nds/diskwarming/_public/svc/..."
        - "//server/src/unit/com/xgen/cloud/nds/maintenance/_public/svc/..."
        - "//server/src/unit/com/xgen/cloud/nds/simulateregionoutage/..."
        - "//server/src/unit/com/xgen/cloud/nds/spothealthcheck/..."
        - "//server/src/unit/com/xgen/svc/nds/simulateregionoutage/..."
- online_archive:
    - online_archive:
        - "//server/src/unit/com/xgen/cloud/nds/onlinearchive/..."
        - "//server/src/unit/com/xgen/svc/nds/svc/onlinearchive/..."
- nds_data_lake:
    - nds_data_lake:
        - "//server/src/unit/com/xgen/cloud/nds/datalake/..."
- nds_clusters_billing_metering:
    - nds_clusters_billing_metering:
        - "//server/src/unit/com/xgen/svc/mms/svc/atlasbilling/..."
        - "//server/src/unit/com/xgen/svc/nds/meteringChecks/..."
        - "//server/src/unit/com/xgen/cloud/nds/metering/..."
        - "//server/src/unit/com/xgen/svc/nds/billing/..."
        - "//server/src/unit/com/xgen/cloud/nds/billing/..."
- nds_data_validation:
    - nds_data_validation:
        - "//server/src/unit/com/xgen/svc/nds/datavalidation/..."
        - "//server/src/unit/com/xgen/cloud/nds/datavalidation/..."
        - "//server/src/unit/com/xgen/cloud/nds/dbcheck/..."
        - "//server/src/unit/com/xgen/cloud/nds/checkmetadataconsistency/..."
        - "//server/src/unit/com/xgen/cloud/nds/corruptiondetection/..."
- nds_data_exfiltration_prevention:
    - nds_data_exfiltration_prevention:
        - "//server/src/unit/com/xgen/cloud/nds/dataexfiltrationprevention/..."
        - "//server/src/unit/com/xgen/cloud/nds/temporarydownloadlinks/..."
- nds_sample_dataset:
    - nds_sample_dataset:
        - "//server/src/unit/com/xgen/svc/nds/sampleDatasetLoad/..."
        - "//server/src/unit/com/xgen/cloud/nds/sampledataset/..."
- nds_priority_takeover:
    - nds_priority_takeover:
        - "//server/src/unit/com/xgen/svc/nds/prioritytakeover/planner/..."
- nds_tenant_upgrade:
    - nds_tenant_upgrade:
        - "//server/src/unit/com/xgen/svc/nds/tenantUpgrade/..."
- nds_live_import:
    - nds_live_import:
        - "//server/src/unit/com/xgen/svc/nds/liveimport/..."
        - "//server/src/unit/com/xgen/module/liveimport/..."
        - "//server/src/unit/com/xgen/module/liveexport/..."
- nds_security:
    - nds_security:
        - "//server/src/unit/com/xgen/svc/nds/security/..."
        - "//server/src/unit/com/xgen/cloud/nds/security/..."
        - "//server/src/unit/com/xgen/cloud/security/..."
- nds_planner:
    - nds_planner:
        - "//server/src/unit/com/xgen/svc/nds/svc/planning/..."
        - "//server/src/unit/com/xgen/svc/nds/planner/..."
        - "//server/src/unit/com/xgen/cloud/nds/planning/..."
        - "//server/src/unit/com/xgen/cloud/nds/autohealing/..."
- nds_aws:
    - nds_aws:
        - "//server/src/unit/com/xgen/svc/nds/aws/..."
        - "//server/src/unit/com/xgen/cloud/common/aws/..."
        - "//server/src/unit/com/xgen/cloud/nds/aws/..."
- nds_azure:
    - nds_azure:
        - "//server/src/unit/com/xgen/svc/nds/azure/..."
        - "//server/src/unit/com/xgen/cloud/nds/azure/..."
- nds_shadow_clusters:
    - nds_shadow_clusters:
        - "//server/src/unit/com/xgen/cloud/nds/shadowcluster/..."
- mms_apix:
    - mms_api_service_accounts:
        - "//server/src/unit/com/xgen/cloud/apiserviceaccount/..."
    - mms_api_usagedata:
        - "//server/src/unit/com/xgen/cloud/apiusagedata/..."
    - mms_openapi:
        - "//server/src/unit/com/xgen/cloud/openapi/..."
        - "//server/src/unit/com/xgen/devtools/bugchecker/swagger/..."
    - mms_versioning:
        - "//server/src/unit/com/xgen/cloud/common/versioning/..."
        - "//server/src/unit/com/xgen/devtools/bugchecker/versioning/..."
    - api_registry_service:
        - "//server/src/unit/com/xgen/cloud/services/apiregistry/..."
        - "//server/src/unit/com/xgen/cloud/apiregistry/..."
    - api_sunset:
        - "//server/src/unit/com/xgen/cloud/apisunset/..."
    - ratelmit:
        - "//server/src/unit/com/xgen/cloud/common/ratelimit/..."
        - "//server/src/unit/com/xgen/cloud/ratelimitmanagement/..."
- mms_intel_i:
    - mms_intel_i:
        - "//server/src/unit/com/xgen/cloud/customermetrics/..."
        - "//server/src/unit/com/xgen/cloud/ping/tokenization/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/ping/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/agent/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/common/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/fts/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/internaltestusersonly/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/lifecycle/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/metrics/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/ratelimit/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/strategy/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/topology/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/tsstrategy/..."
- mms_intel_ii:
    - mms_intel_ii:
        - "//server/src/unit/com/xgen/cloud/performanceadvisor/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/performanceadvisor/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/logs/..."
        - "//server/src/unit/com/xgen/cloud/monitoring/querystats/..."
        - "//server/src/unit/com/xgen/svc/indexing/..."
- mms_intel_explorer:
    - mms_intel_explorer:
        - "//server/src/unit/com/xgen/svc/explorer/..."
        - "//server/src/unit/com/xgen/svc/mms/svc/explorer/..."
- mms_version:
    - mms_version:
        - "//server/src/unit/com/xgen/cloud/version/..."
- nds_free:
    - nds_free:
        - "//server/src/unit/com/xgen/svc/nds/free/..."
        - "//server/src/unit/com/xgen/cloud/nds/free/..."
- nds_gcp:
    - nds_gcp:
        - "//server/src/unit/com/xgen/svc/nds/gcp/..."
        - "//server/src/unit/com/xgen/cloud/nds/gcp/..."
- nds_exmaintenance:
    - nds_exmaintenance:
        - "//server/src/unit/com/xgen/svc/nds/exmaintenance/..."
        - "//server/src/unit/com/xgen/cloud/nds/exmaintenance/..."
- fts_decoupled:
    - fts_decoupled:
        - "//server/src/unit/com/xgen/cloud/search/..."
        - "//server/src/unit/com/xgen/svc/search/..."
        - "//server/src/unit/com/xgen/cloud/nds/fts/..."
- streams:
    - streams:
        - "//server/src/unit/com/xgen/cloud/nds/streams/..."
        - "//server/src/unit/com/xgen/cloud/streams/_public/model/..."
        - "//server/src/unit/com/xgen/cloud/streams/_public/svc/..."
        - "//server/src/unit/com/xgen/cloud/streams/runtime/res/..."
        - "//server/src/unit/com/xgen/svc/streams/..."
        - "//server/src/unit/com/xgen/svc/nds/svc/streams/..."
        - "//server/src/unit/com/xgen/svc/nds/svc/streamsexportmetrics/..."
- security:
    - security:
        - "//server/src/unit/com/xgen/svc/security/..."
        - "//server/src/unit/com/xgen/devtools/annotationprocessor/..."
        - "//server/src/unit/com/xgen/devtools/bugchecker/..."
        - "//server/src/unit/com/xgen/devtools/cucumber/..."
- payments:
    - payments:
        - "//server/src/unit/com/xgen/cloud/partners/..."
        - "//server/src/unit/com/xgen/cloud/payments/..."
- module:
    - module:
        - "//server/src/unit/com/xgen/module/common/planner/..."
        - "//server/src/unit/com/xgen/module/library/control/..."
        - "//server/src/unit/com/xgen/module/mdc/..."
        - "//server/src/unit/com/xgen/module/metrics/nds/..."
        - "//server/src/unit/com/xgen/cloud/common/access/..."
        - "//server/src/unit/com/xgen/cloud/common/appsettings/..."
        - "//server/src/unit/com/xgen/cloud/common/appversion/..."
        - "//server/src/unit/com/xgen/cloud/common/bulkheading/..."
        - "//server/src/unit/com/xgen/cloud/common/constants/..."
        - "//server/src/unit/com/xgen/cloud/common/datatables/..."
        - "//server/src/unit/com/xgen/cloud/common/event/..."
        - "//server/src/unit/com/xgen/cloud/common/filter/..."
        - "//server/src/unit/com/xgen/cloud/common/geolocation/..."
        - "//server/src/unit/com/xgen/cloud/common/guice/..."
        - "//server/src/unit/com/xgen/cloud/common/http/..."
        - "//server/src/unit/com/xgen/cloud/common/jackson/..."
        - "//server/src/unit/com/xgen/cloud/common/jersey/..."
        - "//server/src/unit/com/xgen/cloud/common/jira/..."
        - "//server/src/unit/com/xgen/cloud/common/jobqueue/..."
        - "//server/src/unit/com/xgen/cloud/common/logging/..."
        - "//server/src/unit/com/xgen/cloud/common/messagebus/..."
        - "//server/src/unit/com/xgen/cloud/common/metrics/..."
        - "//server/src/unit/com/xgen/cloud/common/res/..."
        - "//server/src/unit/com/xgen/cloud/common/retry/..."
        - "//server/src/unit/com/xgen/cloud/common/security/..."
        - "//server/src/unit/com/xgen/cloud/common/sharedpool/..."
        - "//server/src/unit/com/xgen/cloud/common/streamex/..."
        - "//server/src/unit/com/xgen/cloud/common/system/..."
        - "//server/src/unit/com/xgen/cloud/common/temporal/..."
        - "//server/src/unit/com/xgen/cloud/common/tracing/..."
        - "//server/src/unit/com/xgen/cloud/common/util/..."
        - "//server/src/unit/com/xgen/cloud/common/v1degradedexperience/..."
        - "//server/src/unit/com/xgen/cloud/common/view/..."
        - "//server/src/unit/com/xgen/cloud/migration/..."
        - "//server/src/unit/com/xgen/cloud/resourcetags/..."
- mongo:
    - mongo:
        - "//server/src/unit/com/xgen/cloud/common/dao/..."
        - "//server/src/unit/com/xgen/cloud/common/db/..."
        - "//server/src/unit/com/xgen/cloud/common/driverwrappers/..."
        - "//server/src/unit/com/xgen/cloud/common/model/..."
        - "//server/src/unit/com/xgen/cloud/common/mongo/..."
- mms_onprem:
    - mms_onprem:
        - "//server/src/unit/com/xgen/cloud/controlledfeature/..."
- onlinearchive_restorationtool:
    - restorationtool_planner:
        - "//server/src/unit/com/xgen/svc/nds/onlinearchive/restorationtool/planner/..."
    - restorationtool_svc:
        - "//server/src/unit/com/xgen/svc/nds/onlinearchive/restorationtool/svc/..."
    - restorationtool_jobs:
        - "//server/src/unit/com/xgen/svc/nds/onlinearchive/restorationtool/jobs/..."
- onlinearchive_v3migration:
    - planner:
        - "//server/src/unit/com/xgen/svc/nds/onlinearchive/v3migration/planner/..."
    - svc:
        - "//server/src/unit/com/xgen/svc/nds/onlinearchive/v3migration/svc/..."
    - jobs:
        - "//server/src/unit/com/xgen/svc/nds/onlinearchive/v3migration/jobs/..."
- stack_traces:
    - stack_traces:
        - "//server/src/unit/com/xgen/cloud/stacktrace/..."
- billing_monolith:
    - billing_monolith:
        - "//server/src/unit/com/xgen/svc/mms/svc/billing/..."
- billing_module:
    - billing_module:
        - "//server/src/unit/com/xgen/cloud/billing/..."
        - "//server/src/unit/com/xgen/cloud/billingplatform/..."
        - "//server/src/unit/com/xgen/cloud/sfdc/..."
        - "//server/src/unit/com/xgen/cloud/sfsc/..."
        - "//server/src/unit/com/xgen/cloud/services/payments/..."
        - "//server/src/unit/com/xgen/cloud/billingshared/..."
        - "//server/src/unit/com/xgen/cloud/pricing/..."
        - "//server/src/unit/com/xgen/cloud/salesforce/..."
- metering:
    - metering:
        - "//server/src/unit/com/xgen/cloud/billingimport/..."
        - "//server/src/unit/com/xgen/module/metering/..."
- dataexport:
    - dataexport:
        - "//server/src/unit/com/xgen/cloud/dataexport/..."
- block_endpoints:
    - block_endpoints:
        - "//server/src/unit/com/xgen/cloud/access/endpoint/_public/model/..."
        - "//server/src/unit/com/xgen/cloud/access/endpoint/_public/svc/..."

- snapshotvalidation:
    - snapshotvalidation:
        - "//server/src/unit/com/xgen/cloud/services/snapshotvalidation/..."
- charts_api:
    - charts_api:
        - "//server/src/unit/com/xgen/cloud/charts/_public/charts/runtime/res/..."
- voyage:
    - voyage_svc:
        - "//server/src/unit/com/xgen/cloud/voyage/_public/svc/..."
        - "//server/src/unit/com/xgen/cloud/voyage/_public/model/..."
        - "//server/src/unit/com/xgen/cloud/voyage/_private/svc/..."
        - "//server/src/unit/com/xgen/svc/temporal/voyage/billing/..."
