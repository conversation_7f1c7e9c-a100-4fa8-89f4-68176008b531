package com.xgen.cloud.alerts.flapping.detection._public.model;

import static com.xgen.cloud.alerts.flapping.model._public.api.FlappingState.UNDETECTED;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.activity._public.model.alert.config.AlertConfig;
import com.xgen.cloud.alerts.checks.common._public.svc.Result.Type;
import com.xgen.cloud.alerts.flapping.detection._private.strategy.FlappingDetectionStrategy.NewFlappingResult;
import com.xgen.cloud.alerts.flapping.model._public.api.FlappingState;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/** Represents a result of every trackable alert check execution. */
public class FlappingResult {
  public static final String ID_FIELD = "_id";
  public static final String TARGET_ID_FIELD = "targetId";
  public static final String ALERT_CONFIG_ID_FIELD = "alertConfigId";
  public static final String CONTEXT_FIELD = "context";
  public static final String UPDATED_AT_FIELD = "updatedAt";
  public static final String RESULT_STATES_FIELD = "resultStates";
  public static final String FLAPPING_STATE_FIELD = "flappingState";
  public static final String TRANSITION_RATE_FIELD = "transitionRate";
  public static final String THRESHOLD_FIELD = "threshold";

  @JsonProperty(ID_FIELD)
  @BsonId
  private ObjectId id;

  @JsonProperty(TARGET_ID_FIELD)
  @BsonProperty(TARGET_ID_FIELD)
  private String targetId;

  @JsonProperty(ALERT_CONFIG_ID_FIELD)
  @BsonProperty(ALERT_CONFIG_ID_FIELD)
  private ObjectId alertConfigId;

  @JsonProperty(CONTEXT_FIELD)
  @BsonProperty(CONTEXT_FIELD)
  private Context context;

  @JsonProperty(UPDATED_AT_FIELD)
  @BsonProperty(UPDATED_AT_FIELD)
  private Date updatedAt;

  @JsonProperty(RESULT_STATES_FIELD)
  @BsonProperty(RESULT_STATES_FIELD)
  private List<Type> resultStates;

  @JsonProperty(FLAPPING_STATE_FIELD)
  @BsonProperty(FLAPPING_STATE_FIELD)
  private FlappingState flappingState;

  @JsonProperty(TRANSITION_RATE_FIELD)
  @BsonProperty(TRANSITION_RATE_FIELD)
  private double transitionRate;

  @JsonProperty(THRESHOLD_FIELD)
  @BsonProperty(THRESHOLD_FIELD)
  private double threshold;

  @BsonCreator
  public FlappingResult(
      @BsonId final ObjectId id,
      @BsonProperty(TARGET_ID_FIELD) final String targetId,
      @BsonProperty(ALERT_CONFIG_ID_FIELD) final ObjectId alertConfigId,
      @BsonProperty(CONTEXT_FIELD) final Context context,
      @BsonProperty(RESULT_STATES_FIELD) final List<Type> resultStates,
      @BsonProperty(FLAPPING_STATE_FIELD) final FlappingState flappingState,
      @BsonProperty(TRANSITION_RATE_FIELD) final double transitionRate,
      @BsonProperty(THRESHOLD_FIELD) final double threshold) {
    this.id = id;
    this.targetId = targetId;
    this.alertConfigId = alertConfigId;
    this.context = context;
    this.updatedAt = new Date();
    this.resultStates = resultStates;
    this.flappingState = flappingState;
    this.transitionRate = transitionRate;
    this.threshold = threshold;
  }

  public FlappingResult(
      final ObjectId id, final String targetId, AlertConfig alertConfig, double threshold) {
    this(
        id,
        targetId,
        alertConfig.getId(),
        new Context(
            alertConfig.isOrgAlertConfig() ? alertConfig.getOrgId() : alertConfig.getGroupId(),
            alertConfig.getScope()),
        new LinkedList<>(),
        UNDETECTED,
        0,
        threshold);
  }

  public ObjectId getId() {
    return id;
  }

  public String getTargetId() {
    return targetId;
  }

  public ObjectId getAlertConfigId() {
    return alertConfigId;
  }

  public Context getContext() {
    return context;
  }

  public Date getUpdatedAt() {
    return updatedAt;
  }

  public List<Type> getResultStates() {
    return new LinkedList<>(resultStates);
  }

  public FlappingState getFlappingState() {
    return flappingState;
  }

  public double getTransitionRate() {
    return transitionRate;
  }

  public double getThreshold() {
    return threshold;
  }

  public void setUpdatedAt(Date time) {
    this.updatedAt = time;
  }

  public void updateResult(
      FlappingState state, double transitionRate, double threshold, List<Type> resultStates) {
    this.updatedAt = new Date();
    this.flappingState = state;
    this.transitionRate = transitionRate;
    this.threshold = threshold;
    this.resultStates = resultStates;
  }

  public void updateResult(NewFlappingResult newResult, List<Type> resultStates) {
    updateResult(
        newResult.getFlappingState(),
        newResult.getTransitionRate(),
        newResult.getThreshold(),
        resultStates);
  }
}
