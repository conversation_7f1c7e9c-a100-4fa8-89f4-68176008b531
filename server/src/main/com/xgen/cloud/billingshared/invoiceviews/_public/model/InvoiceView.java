package com.xgen.cloud.billingshared.invoiceviews._public.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice.Status;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice.Type;
import com.xgen.cloud.payments.paymentview._public.model.PaymentAmountsAggregatedByCurrency;
import com.xgen.cloud.payments.paymentview._public.model.PaymentView;
import com.xgen.cloud.payments.paymentview._public.model.PaymentViewComparator;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.bson.types.ObjectId;

@Hidden
public final class InvoiceView {

  public static final String JSON_ID = "id";
  public static final String STATUS = "status";
  public static final String START_DATE = "startDate";
  public static final String END_DATE = "endDate";
  public static final String SUBTOTAL_CENTS = "subtotalCents";
  public static final String ORG_ID = "orgId";
  public static final String ORG_NAME = "orgName";
  public static final String ORG_TYPE = "orgType";
  public static final String TYPE = "type";
  public static final String SALES_TAX_CENTS = "salesTaxCents";
  public static final String PAYING_ORG_NAME = "payingOrgName";
  public static final String PAYMENT_AMOUNTS_AGGREGATED_BY_CURRENCY =
      "paymentAmountsAggregatedByCurrency";
  public static final String CROSS_ORG_PAYMENT_AMOUNTS_AGGREGATED_BY_CURRENCY =
      "crossOrgPaymentAmountsAggregatedByCurrency";
  public static final String LINKED_ORG_IDS = "linkedOrgIds";
  public static final String USAGE_CENTS_BY_CURRENCY_AND_UNIT_PRICE =
      "usageCentsByCurrencyAndUnitPrice";
  public static final String DOCUMENT_DOWNLOAD_MEMU_ITEMS = "documentDownloadMenuItems";

  public static final String HAS_SALES_SOLD_CREDIT_APPLIED = "hasSalesSoldCreditApplied";
  public static final String IS_FX_RATE_MODEL_PRESENT = "isFxRateModelPresent";
  public static final String LINKED_INVOICES = "linkedInvoices";
  public static final String PAYMENT_VIEWS = "paymentViews";
  public static final String USAGE_CENTS_TOTAL = "usageCentsTotal";
  public static final String BILLED_USAGE_CENTS_TOTAL = "billedUsageCentsTotal";
  public static final String CROSS_ORG_USAGE_CENTS_TOTAL = "crossOrgUsageCentsTotal";
  public static final String CROSS_ORG_BILLED_USAGE_CENTS_TOTAL = "crossOrgBilledUsageCentsTotal";

  @JsonProperty(JSON_ID)
  private ObjectId id;

  @JsonProperty(STATUS)
  private Status status;

  @JsonProperty(START_DATE)
  private Date startDate;

  @JsonProperty(END_DATE)
  private Date endDate;

  @JsonProperty(SUBTOTAL_CENTS)
  private Long subtotalCents;

  @JsonProperty(ORG_ID)
  private ObjectId orgId;

  @JsonProperty(ORG_NAME)
  private String orgName;

  @JsonProperty(ORG_TYPE)
  private OrgTypeForInvoiceView orgType;

  @JsonProperty(TYPE)
  private Type type;

  @JsonProperty(SALES_TAX_CENTS)
  private long salesTaxCents;

  @JsonProperty(PAYING_ORG_NAME)
  private String payingOrgName;

  @JsonProperty(PAYMENT_AMOUNTS_AGGREGATED_BY_CURRENCY)
  private Map<String, PaymentAmountsAggregatedByCurrency> paymentAmountsAggregatedByCurrency;

  @JsonProperty(CROSS_ORG_PAYMENT_AMOUNTS_AGGREGATED_BY_CURRENCY)
  private Map<String, PaymentAmountsAggregatedByCurrency>
      crossOrgPaymentAmountsAggregatedByCurrency;

  @JsonProperty(LINKED_ORG_IDS)
  @Nullable
  private List<ObjectId> linkedOrgIds;

  @JsonProperty(USAGE_CENTS_BY_CURRENCY_AND_UNIT_PRICE)
  private List<UsageCentsByCurrencyAndUnitPrice> usageCentsByCurrencyAndUnitPrice;

  @JsonProperty(HAS_SALES_SOLD_CREDIT_APPLIED)
  private boolean hasSalesSoldCreditApplied;

  @JsonProperty(IS_FX_RATE_MODEL_PRESENT)
  private boolean isFxRateModelPresent;

  @JsonProperty(DOCUMENT_DOWNLOAD_MEMU_ITEMS)
  @Nullable
  private List<BillingDocumentDownloadMenuItem> documentDownloadMenuItems;

  @JsonProperty(LINKED_INVOICES)
  private List<InvoiceView> linkedInvoices;

  @NotNull
  @JsonProperty(PAYMENT_VIEWS)
  private List<PaymentView> paymentViews;

  @JsonProperty(USAGE_CENTS_TOTAL)
  private Long usageCentsTotal;

  @JsonProperty(BILLED_USAGE_CENTS_TOTAL)
  private Long billedUsageCentsTotal;

  @JsonProperty(CROSS_ORG_USAGE_CENTS_TOTAL)
  private Long crossOrgUsageCentsTotal;

  @JsonProperty(CROSS_ORG_BILLED_USAGE_CENTS_TOTAL)
  private Long crossOrgBilledUsageCentsTotal;

  private InvoiceView(Builder builder) {
    id = builder.id;
    status = builder.status;
    startDate = builder.startDate;
    endDate = builder.endDate;
    subtotalCents = builder.subtotalCents;
    orgId = builder.orgId;
    orgName = builder.orgName;
    orgType = builder.orgType;
    type = builder.type;
    salesTaxCents = builder.salesTaxCents;
    payingOrgName = builder.payingOrgName;
    paymentAmountsAggregatedByCurrency = builder.paymentAmountsAggregatedByCurrency;
    crossOrgPaymentAmountsAggregatedByCurrency = builder.crossOrgPaymentAmountsAggregatedByCurrency;
    linkedOrgIds = builder.linkedOrgIds;
    usageCentsByCurrencyAndUnitPrice = builder.usageCentsByCurrencyAndUnitPrice;
    hasSalesSoldCreditApplied = builder.hasSalesSoldCreditApplied;
    isFxRateModelPresent = builder.isFxRateModelPresent;
    documentDownloadMenuItems = builder.documentDownloadMenuItems;
    linkedInvoices = Objects.requireNonNullElse(builder.linkedInvoices, List.of());
    paymentViews =
        Objects.requireNonNullElse(builder.paymentViews, List.<PaymentView>of()).stream()
            .sorted(new PaymentViewComparator())
            .toList();
    usageCentsTotal = builder.usageCentsTotal;
    billedUsageCentsTotal = builder.billedUsageCentsTotal;
    crossOrgUsageCentsTotal = builder.crossOrgUsageCentsTotal;
    crossOrgBilledUsageCentsTotal = builder.crossOrgBilledUsageCentsTotal;
  }

  @JsonIgnore
  public static Builder builder() {
    return new Builder();
  }

  @JsonIgnore
  public Builder toBuilder() {
    return new Builder(this);
  }

  public ObjectId getId() {
    return id;
  }

  public Status getStatus() {
    return status;
  }

  public Date getStartDate() {
    return startDate;
  }

  public Date getEndDate() {
    return endDate;
  }

  public Long getSubtotalCents() {
    return subtotalCents;
  }

  public ObjectId getOrgId() {
    return orgId;
  }

  public String getOrgName() {
    return orgName;
  }

  public OrgTypeForInvoiceView getOrgType() {
    return orgType;
  }

  public Type getType() {
    return type;
  }

  public long getSalesTaxCents() {
    return salesTaxCents;
  }

  public String getPayingOrgName() {
    return payingOrgName;
  }

  public Map<String, PaymentAmountsAggregatedByCurrency> getPaymentAmountsAggregatedByCurrency() {
    return paymentAmountsAggregatedByCurrency;
  }

  @Nullable
  public List<ObjectId> getLinkedOrgIds() {
    return linkedOrgIds;
  }

  public List<UsageCentsByCurrencyAndUnitPrice> getUsageCentsByCurrencyAndUnitPrice() {
    return usageCentsByCurrencyAndUnitPrice;
  }

  @JsonProperty(HAS_SALES_SOLD_CREDIT_APPLIED)
  public boolean hasSalesSoldCreditApplied() {
    return hasSalesSoldCreditApplied;
  }

  @JsonProperty(IS_FX_RATE_MODEL_PRESENT)
  public boolean isFxRateModelPresent() {
    return isFxRateModelPresent;
  }

  @Nullable
  public List<BillingDocumentDownloadMenuItem> getDocumentDownloadMenuItems() {
    return documentDownloadMenuItems;
  }

  public List<InvoiceView> getLinkedInvoices() {
    return linkedInvoices;
  }

  public Map<String, PaymentAmountsAggregatedByCurrency>
      getCrossOrgPaymentAmountsAggregatedByCurrency() {
    return crossOrgPaymentAmountsAggregatedByCurrency;
  }

  public @NotNull List<PaymentView> getPaymentViews() {
    return paymentViews;
  }

  public Long getUsageCentsTotal() {
    return usageCentsTotal;
  }

  public Long getBilledUsageCentsTotal() {
    return billedUsageCentsTotal;
  }

  public Long getCrossOrgUsageCentsTotal() {
    return crossOrgUsageCentsTotal;
  }

  public Long getCrossOrgBilledUsageCentsTotal() {
    return crossOrgBilledUsageCentsTotal;
  }

  public static final class Builder {

    private ObjectId id;
    private Status status;
    private Date startDate;
    private Date endDate;
    private Long subtotalCents;
    private ObjectId orgId;
    private String orgName;
    private OrgTypeForInvoiceView orgType;
    private Type type;
    private long salesTaxCents;
    private String payingOrgName;
    private List<ObjectId> linkedOrgIds;
    private Map<String, PaymentAmountsAggregatedByCurrency> paymentAmountsAggregatedByCurrency;
    private Map<String, PaymentAmountsAggregatedByCurrency>
        crossOrgPaymentAmountsAggregatedByCurrency;
    private List<UsageCentsByCurrencyAndUnitPrice> usageCentsByCurrencyAndUnitPrice;
    private boolean hasSalesSoldCreditApplied;
    private boolean isFxRateModelPresent;
    private List<BillingDocumentDownloadMenuItem> documentDownloadMenuItems;
    private List<InvoiceView> linkedInvoices;
    private List<PaymentView> paymentViews;
    private Long usageCentsTotal;
    private Long billedUsageCentsTotal;
    private Long crossOrgUsageCentsTotal;
    private Long crossOrgBilledUsageCentsTotal;

    public Builder() {}

    public Builder(InvoiceView copy) {
      this.id = copy.getId();
      this.status = copy.getStatus();
      this.startDate = copy.getStartDate();
      this.endDate = copy.getEndDate();
      this.subtotalCents = copy.getSubtotalCents();
      this.orgId = copy.getOrgId();
      this.orgName = copy.getOrgName();
      this.orgType = copy.getOrgType();
      this.type = copy.getType();
      this.salesTaxCents = copy.getSalesTaxCents();
      this.payingOrgName = copy.getPayingOrgName();
      this.paymentAmountsAggregatedByCurrency = copy.getPaymentAmountsAggregatedByCurrency();
      this.linkedOrgIds = copy.getLinkedOrgIds();
      this.usageCentsByCurrencyAndUnitPrice = copy.getUsageCentsByCurrencyAndUnitPrice();
      this.hasSalesSoldCreditApplied = copy.hasSalesSoldCreditApplied();
      this.isFxRateModelPresent = copy.isFxRateModelPresent();
      this.documentDownloadMenuItems = copy.getDocumentDownloadMenuItems();
      this.linkedInvoices = Objects.requireNonNullElse(copy.getLinkedInvoices(), List.of());
      this.crossOrgPaymentAmountsAggregatedByCurrency =
          copy.getCrossOrgPaymentAmountsAggregatedByCurrency();
      this.paymentViews = copy.getPaymentViews();
      this.usageCentsTotal = copy.getUsageCentsTotal();
      this.billedUsageCentsTotal = copy.getBilledUsageCentsTotal();
      this.crossOrgUsageCentsTotal = copy.getCrossOrgUsageCentsTotal();
      this.crossOrgBilledUsageCentsTotal = copy.getCrossOrgBilledUsageCentsTotal();
    }

    public Builder id(ObjectId val) {
      id = val;
      return this;
    }

    public Builder status(Status val) {
      status = val;
      return this;
    }

    public Builder startDate(Date val) {
      startDate = val;
      return this;
    }

    public Builder endDate(Date val) {
      endDate = val;
      return this;
    }

    public Builder subtotalCents(Long val) {
      subtotalCents = val;
      return this;
    }

    public Builder orgId(ObjectId val) {
      orgId = val;
      return this;
    }

    public Builder orgName(String val) {
      orgName = val;
      return this;
    }

    public Builder orgType(OrgTypeForInvoiceView val) {
      orgType = val;
      return this;
    }

    public Builder type(Type val) {
      type = val;
      return this;
    }

    public Builder salesTaxCents(long val) {
      salesTaxCents = val;
      return this;
    }

    public Builder payingOrgName(String val) {
      payingOrgName = val;
      return this;
    }

    public Builder linkedOrgIds(List<ObjectId> val) {
      linkedOrgIds = val;
      return this;
    }

    public Builder paymentAmountsAggregatedByCurrency(
        Map<String, PaymentAmountsAggregatedByCurrency> val) {
      paymentAmountsAggregatedByCurrency = val;
      return this;
    }

    public Builder crossOrgPaymentAmountsAggregatedByCurrency(
        Map<String, PaymentAmountsAggregatedByCurrency> val) {
      crossOrgPaymentAmountsAggregatedByCurrency = val;
      return this;
    }

    public Builder usageCentsByCurrencyAndUnitPrice(List<UsageCentsByCurrencyAndUnitPrice> val) {
      usageCentsByCurrencyAndUnitPrice = val;
      return this;
    }

    public Builder hasSalesSoldCreditApplied(boolean val) {
      hasSalesSoldCreditApplied = val;
      return this;
    }

    public Builder isFxRateModelPresent(boolean val) {
      isFxRateModelPresent = val;
      return this;
    }

    public Builder documentDownloadMenuItems(List<BillingDocumentDownloadMenuItem> val) {
      documentDownloadMenuItems = val;
      return this;
    }

    public Builder linkedInvoices(List<InvoiceView> val) {
      linkedInvoices = val;
      return this;
    }

    public Builder paymentViews(List<PaymentView> val) {
      paymentViews = val;
      return this;
    }

    public Builder usageCentsTotal(Long val) {
      usageCentsTotal = val;
      return this;
    }

    public Builder billedUsageCentsTotal(Long val) {
      billedUsageCentsTotal = val;
      return this;
    }

    public Builder crossOrgUsageCentsTotal(Long val) {
      crossOrgUsageCentsTotal = val;
      return this;
    }

    public Builder crossOrgBilledUsageCentsTotal(Long val) {
      crossOrgBilledUsageCentsTotal = val;
      return this;
    }

    public InvoiceView build() {
      return new InvoiceView(this);
    }
  }
}
