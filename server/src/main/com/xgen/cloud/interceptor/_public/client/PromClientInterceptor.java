package com.xgen.cloud.interceptor._public.client;

import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.ForwardingClientCallListener.SimpleForwardingClientCallListener;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import io.grpc.Status;
import io.grpc.Status.Code;
import io.prometheus.client.Counter;
import jakarta.inject.Singleton;

@Singleton
public class PromClientInterceptor implements ClientInterceptor {
  private static final Counter CLIENT_CALLS_COUNTER =
      Counter.build()
          .name("mms_grpc_client_calls_total")
          .help("Total number of client-side RPCs")
          .labelNames("grpc_service", "grpc_method", "grpc_type", "grpc_status_code")
          .register();
  private static final Counter CLIENT_ERRORS_COUNTER =
      Counter.build()
          .name("mms_grpc_client_errors_total")
          .help("Total number of client-side errors in RPCs")
          .labelNames("grpc_service", "grpc_method", "grpc_type", "grpc_error")
          .register();

  @Override
  public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
      MethodDescriptor<ReqT, RespT> method, CallOptions callOptions, Channel next) {
    return new ForwardingClientCall.SimpleForwardingClientCall<>(
        next.newCall(method, callOptions)) {

      @Override
      public void start(Listener<RespT> responseListener, Metadata headers) {
        super.start(
            new SimpleForwardingClientCallListener<>(responseListener) {
              @Override
              public void onClose(Status status, Metadata trailers) {
                CLIENT_CALLS_COUNTER
                    .labels(
                        method.getServiceName(),
                        method.getFullMethodName(),
                        method.getType().name(),
                        status.getCode().name())
                    .inc();

                if (status.getCode() != Code.OK) {
                  CLIENT_ERRORS_COUNTER
                      .labels(
                          method.getServiceName(),
                          method.getFullMethodName(),
                          method.getType().name(),
                          status.getCode().name())
                      .inc();
                }

                super.onClose(status, trailers);
              }
            },
            headers);
      }
    };
  }
}
