package com.xgen.cloud.pricing._public.svc;

import com.xgen.cloud.billingplatform.model.sku._public.model.PricingConsumers;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuPricing;
import jakarta.annotation.Nullable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;

/**
 * Service interface for SKU pricing operations. All methods follow the pattern: SKU, Date,
 * organizationId, then other parameters.
 */
public interface SkuPricingSvc {

  default SkuPricing getPricing(SKU sku, Date usageTimestamp, PricingConsumers consumer) {
    return getPricing(sku, usageTimestamp, null, consumer);
  }

  default Map<SKU, SkuPricing> getPricing(
      List<SKU> sku, Date usageTimestamp, PricingConsumers consumer) {
    return getPricing(sku, usageTimestamp, null, consumer);
  }

  SkuPricing getPricing(
      SKU sku, Date usageTimestamp, ObjectId organizationId, PricingConsumers consumer);

  Map<SKU, SkuPricing> getPricing(
      List<SKU> sku,
      Date usageTimestamp,
      @Nullable ObjectId organizationId,
      PricingConsumers consumer);
}
