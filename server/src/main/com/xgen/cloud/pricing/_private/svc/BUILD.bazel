load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "svc",
    srcs = glob(["**/*.java"]),
    visibility = [
        "//server/src/main/com/xgen/cloud/pricing:__subpackages__",
        "//server/src/main/com/xgen/svc/mms/util/billing/testFactories:__pkg__",
        "//server/src/test:__subpackages__",
        "//server/src/unit:__subpackages__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/billingplatform/model/sku",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/featureFlag",
        "//server/src/main/com/xgen/cloud/featureFlag",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/organization/_private/dao",
        "//server/src/main/com/xgen/cloud/pricing/_private/client",
        "//server/src/main/com/xgen/cloud/pricing/_public/svc",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:org_mongodb_bson",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
