package com.xgen.cloud.pricing._private.svc;

import com.xgen.cloud.billingplatform.model.sku._public.model.PricingConsumers;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuPricing;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.pricing._private.client.SkuPriceServiceClient;
import com.xgen.cloud.pricing._public.svc.SkuPricingSvc;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class SkuPricingSvcImpl implements SkuPricingSvc {

  private static final Logger LOG = LoggerFactory.getLogger(SkuPricingSvcImpl.class);

  private final FeatureFlagSvc featureFlagSvc;
  private final OrganizationDao organizationDao;
  private final SkuPriceServiceClient skuPriceServiceClient;
  private final AppSettings appSettings;

  @Inject
  public SkuPricingSvcImpl(
      FeatureFlagSvc featureFlagSvc,
      OrganizationDao organizationDao,
      SkuPriceServiceClient skuPriceServiceClient,
      AppSettings appSettings) {
    this.featureFlagSvc = featureFlagSvc;
    this.organizationDao = organizationDao;
    this.skuPriceServiceClient = skuPriceServiceClient;
    this.appSettings = appSettings;
  }

  @Override
  public SkuPricing getPricing(
      SKU sku, Date usageTimestamp, @Nullable ObjectId organizationId, PricingConsumers consumer) {
    return getPricing(List.of(sku), usageTimestamp, organizationId, consumer).get(sku);
  }

  @Override
  public Map<SKU, SkuPricing> getPricing(
      List<SKU> sku,
      Date usageTimestamp,
      @Nullable ObjectId organizationId,
      PricingConsumers consumer) {
    boolean useApi = checkIfConsumerUsesApi(organizationId, consumer);
    return getSkuPricingMap(sku, usageTimestamp, useApi);
  }

  private boolean checkIfConsumerUsesApi(ObjectId organizationId, PricingConsumers consumer) {
    if (consumer == PricingConsumers.BILLING) {
      Organization organization = null;
      if (organizationId != null) {
        organization = organizationDao.findById(organizationId);
      }
      return featureFlagSvc.isFeatureFlagEnabled(
          FeatureFlag.BILLING_USE_PRICING_API, organization, null);

    } else {
      return appSettings
          .getSetProperty("mms.billing.pricingApiConsumers", AppSettings.COMMA_DELIMITER)
          .contains(consumer.name());
    }
  }

  private Map<SKU, SkuPricing> getSkuPricingMap(
      List<SKU> skus, Date usageTimestamp, boolean useApi) {
    if (useApi) {
      LOG.debug("Making gRPC call to fetch pricing for {} SKUs", skus.size());

      Map<SKU, SkuPricing> pricingMap = skuPriceServiceClient.fetchSkuPricing(skus, usageTimestamp);

      return skus.stream()
          .collect(
              Collectors.toMap(
                  Function.identity(),
                  sku -> {
                    if (pricingMap.containsKey(sku)) {
                      return pricingMap.get(sku);
                    } else {
                      LOG.warn(
                          "No pricing found in gRPC response for SKU: {}, using local pricing",
                          sku);
                      return sku.getInfo().getPricingForDate(usageTimestamp);
                    }
                  }));
    } else {
      return skus.stream()
          .collect(
              Collectors.toMap(
                  Function.identity(), sku -> sku.getInfo().getPricingForDate(usageTimestamp)));
    }
  }
}
