package com.xgen.cloud.pricing._private.client;

import static java.util.stream.Collectors.toMap;

import com.google.protobuf.Timestamp;
import com.xgen.cloud.billingplatform.model.sku._public.model.DatePeriod;
import com.xgen.cloud.billingplatform.model.sku._public.model.SKU;
import com.xgen.cloud.billingplatform.model.sku._public.model.SkuPricing;
import com.xgen.cloud.common.authn._public.svc.AuthnOAuthClient;
import com.xgen.cloud.common.mapstruct.grpc._public.TimestampMapper;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.services.core.client._public.impl.AbstractGrpcClient;
import com.xgen.pricing.skupriceapi.grpc.v1.SkuPriceServiceGrpc;
import com.xgen.pricing.skupriceapi.grpc.v1.SkuPriceServiceGrpc.SkuPriceServiceBlockingStub;
import com.xgen.pricing.skupriceapi.v1.Models.PricingInfoRequest;
import com.xgen.pricing.skupriceapi.v1.Models.PricingInfoResponse;
import io.grpc.Channel;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.configuration2.ImmutableConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * gRPC client for the SKU Price Service. Provides methods to fetch pricing information for SKUs.
 */
@Singleton
public class SkuPriceServiceClient extends AbstractGrpcClient<SkuPriceServiceBlockingStub> {
  private static final Logger LOG = LoggerFactory.getLogger(SkuPriceServiceClient.class);

  private static final String SERVICE_NAME = "pricing";

  private static final Duration STUB_TIMEOUT = Duration.ofSeconds(30);

  @Inject
  public SkuPriceServiceClient(AuthnOAuthClient authnClient, ImmutableConfiguration settings) {
    super(SERVICE_NAME, authnClient, settings);
  }

  @Override
  protected SkuPriceServiceBlockingStub createStub(Channel channel) {
    return SkuPriceServiceGrpc.newBlockingStub(channel);
  }

  /**
   * Fetches pricing information for the given SKUs and usage date.
   *
   * @param skus the list of SKUs to fetch pricing for
   * @param usageDate the usage date for pricing lookup
   * @return map of SKU to SkuPricing containing pricing data
   */
  public Map<SKU, SkuPricing> fetchSkuPricing(List<SKU> skus, Date usageDate) {
    LOG.debug("Fetching SKU pricing for {} SKUs for {}", skus, usageDate);

    // Convert SKUs to names for proto request
    List<String> skuNames = skus.stream().map(SKU::name).toList();

    // Convert client request to proto request
    Timestamp usageTimestampProto = TimestampMapper.mapDateToTimestamp(usageDate);
    PricingInfoRequest protoRequest =
        PricingInfoRequest.newBuilder()
            .addAllSkus(skuNames)
            .setUsageDate(usageTimestampProto)
            .build();

    // Make gRPC call
    SkuPriceServiceBlockingStub stub = getStub(STUB_TIMEOUT);
    PricingInfoResponse protoResponse = stub.fetchSkuPricing(protoRequest);

    // Convert proto response to domain objects
    return convertProtoResponse(protoResponse);
  }

  /** Converts proto response to domain SkuPricing objects. */
  private Map<SKU, SkuPricing> convertProtoResponse(PricingInfoResponse protoResponse) {
    return protoResponse.getPricingMap().entrySet().stream()
        .collect(
            toMap(
                entry -> SKU.valueOf(entry.getKey()),
                entry -> convertProtoToSkuPricing(SKU.valueOf(entry.getKey()), entry.getValue())));
  }

  /** Converts a proto SkuPricing to domain SkuPricing. */
  private SkuPricing convertProtoToSkuPricing(
      SKU sku, com.xgen.pricing.skupriceapi.v1.Models.SkuPricing protoSkuPricing) {
    SkuPricing.Builder builder =
        new SkuPricing.Builder()
            .sku(sku)
            .unitPriceDollars(protoSkuPricing.getUnitPriceDollars())
            .unitPriceGovDollars(protoSkuPricing.getUnitPriceGovDollars())
            .floatPriceDollars(protoSkuPricing.getFloatPriceDollars())
            .freeTierQuantity(protoSkuPricing.getFreeTierQuantity())
            .advancedSecurityUpcharge(protoSkuPricing.getAdvancedSecurityUpcharge())
            .enterpriseAuditingUpcharge(protoSkuPricing.getEnterpriseAuditingUpcharge())
            .biConnectorLow(protoSkuPricing.getBiConnectorLow())
            .biConnectorHigh(protoSkuPricing.getBiConnectorHigh());

    protoSkuPricing
        .getRegionUnitPriceDollarsMap()
        .forEach(
            (provider, prices) ->
                prices
                    .getPricingMap()
                    .forEach(
                        (regionName, price) ->
                            builder.regionUnitPriceDollars(
                                getCloudProviderRegionName(provider, regionName), price)));

    protoSkuPricing
        .getTieredRegionUnitPriceDollarsMap()
        .forEach(
            (provider, regionPrices) -> {
              regionPrices
                  .getPricingMap()
                  .forEach(
                      (regionName, prices) -> {
                        double[] tieredPrices =
                            prices.getPricesList().stream()
                                .mapToDouble(Double::doubleValue)
                                .toArray();
                        builder.tieredRegionUnitPriceDollars(
                            getCloudProviderRegionName(provider, regionName), tieredPrices);
                      });
            });
    // Tiered pricing
    if (protoSkuPricing.hasTieredUnitPricesDollars()) {
      double[] tieredPrices =
          protoSkuPricing.getTieredUnitPricesDollars().getPricesList().stream()
              .mapToDouble(Double::doubleValue)
              .toArray();
      builder.tieredUnitPricesDollars(tieredPrices);
    }

    double[] quantities =
        protoSkuPricing.getTieredPricingQuantitiesList().stream()
            .mapToDouble(Double::doubleValue)
            .toArray();
    builder.tieredPricingQuantities(quantities);

    if (protoSkuPricing.getTieredPricingQuantityHardCap() != 0.0) {
      builder.tieredPricingQuantityHardCap(protoSkuPricing.getTieredPricingQuantityHardCap());
    }

    // Applicability period
    if (protoSkuPricing.hasApplicabilityPeriod()) {
      com.xgen.pricing.skupriceapi.v1.Models.DatePeriod protoPeriod =
          protoSkuPricing.getApplicabilityPeriod();
      Date startDate = null;
      Date endDate = null;

      if (protoPeriod.hasStartTimestamp()) {
        startDate = TimestampMapper.mapTimestampToDate(protoPeriod.getStartTimestamp());
      }

      if (protoPeriod.hasEndTimestamp()) {
        endDate = TimestampMapper.mapTimestampToDate(protoPeriod.getEndTimestamp());
      }

      if (startDate != null) {
        builder.applicabilityPeriod(new DatePeriod(startDate, endDate));
      }
    }

    return builder.build();
  }

  public RegionName getCloudProviderRegionName(String cloudProvider, String regionName) {
    CloudProvider providerToUse = CloudProvider.valueOf(cloudProvider);
    Optional<? extends RegionName> region =
        switch (providerToUse) {
          case AWS -> AWSRegionName.findByName(regionName);
          case AZURE -> AzureRegionName.findByName(regionName);
          case GCP -> GCPRegionName.findByName(regionName);
          default -> Optional.empty();
        };
    return region.orElseThrow();
  }
}
