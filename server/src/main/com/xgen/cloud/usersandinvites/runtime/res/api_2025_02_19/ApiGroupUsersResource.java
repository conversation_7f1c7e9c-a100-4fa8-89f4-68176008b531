package com.xgen.cloud.usersandinvites.runtime.res.api_2025_02_19;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.localDateOf;
import static com.xgen.cloud.common.versioning._public.constants.VersioningConstants.X_SUNSET_DATE_2026_07_31;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.VERB_OVERRIDE;
import static java.util.stream.Collectors.toSet;

import com.mongodb.client.MongoCursor;
import com.xgen.cloud.access.role._public.model.GroupRole;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.access.rolecheck._public.svc.RequestingUserRedactionSvc;
import com.xgen.cloud.billing._public.svc.ICreditSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.common.versioning._public.constants.VersioningConstants;
import com.xgen.cloud.common.view._public.base.ApiListView;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.invitation._public.model.Invitation;
import com.xgen.cloud.invitation._public.svc.InvitationSvc;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.cloud.usersandinvites._private.svc.ActiveUserSvc;
import com.xgen.cloud.usersandinvites._private.svc.PendingUserSvc;
import com.xgen.cloud.usersandinvites._private.svc.UserValidationSvc;
import com.xgen.cloud.usersandinvites._private.util.UserViewUtil;
import com.xgen.cloud.usersandinvites._private.view.ApiAddOrRemoveGroupRoleRequestView;
import com.xgen.cloud.usersandinvites._private.view.ApiGroupActiveUserResponseView;
import com.xgen.cloud.usersandinvites._private.view.ApiGroupPendingUserResponseView;
import com.xgen.cloud.usersandinvites._private.view.ApiGroupRoleView;
import com.xgen.cloud.usersandinvites._private.view.ApiGroupUserRequestView;
import com.xgen.cloud.usersandinvites._private.view.ApiGroupUserResponseView;
import com.xgen.cloud.usersandinvites._private.view.ApiOrgMembershipStatusView;
import com.xgen.cloud.usersandinvites._public.svc.UsersAndInvitesSvc;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.api.view.PaginatedApiAppUserView;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Collections;
import java.util.Date;
import java.util.Optional;
import java.util.Set;
import org.bson.Document;
import org.bson.types.ObjectId;

@Singleton
@Path("/api/atlas/v2/groups")
public class ApiGroupUsersResource extends ApiBaseResource {
  private final UserSvc userSvc;
  private final InvitationSvc invitationSvc;
  private final UserValidationSvc userValidationSvc;
  private final OrganizationSvc organizationSvc;
  private final GroupSvc groupSvc;
  private final UsersAndInvitesSvc usersAndInvitesSvc;
  private final PendingUserSvc pendingUserSvc;
  private final ActiveUserSvc activeUserSvc;
  private final ICreditSvc creditSvc;
  private final RequestingUserRedactionSvc requestingUserRedactionSvc;

  @Inject
  public ApiGroupUsersResource(
      AppSettings settings,
      UserSvc userSvc,
      InvitationSvc invitationSvc,
      GroupSvc groupSvc,
      UsersAndInvitesSvc usersAndInvitesSvc,
      PendingUserSvc pendingUserSvc,
      ActiveUserSvc activeUserSvc,
      UserValidationSvc userValidationSvc,
      OrganizationSvc organizationSvc,
      ICreditSvc creditSvc,
      RequestingUserRedactionSvc requestingUserRedactionSvc) {
    super(settings);
    this.userSvc = userSvc;
    this.invitationSvc = invitationSvc;
    this.userValidationSvc = userValidationSvc;
    this.organizationSvc = organizationSvc;
    this.groupSvc = groupSvc;
    this.usersAndInvitesSvc = usersAndInvitesSvc;
    this.pendingUserSvc = pendingUserSvc;
    this.activeUserSvc = activeUserSvc;
    this.creditSvc = creditSvc;
    this.requestingUserRedactionSvc = requestingUserRedactionSvc;
  }

  @POST
  @Path("/{groupId}/users")
  @Produces(VersionMediaType.V_2025_02_19_JSON)
  @RolesAllowed(NAME.GROUP_USER_ADMIN)
  @Operation(
      summary = "Add One MongoDB Cloud User to One Project",
      operationId = "addGroupUsers",
      description =
          "Adds one MongoDB Cloud user to one project. To use this resource, the requesting Service"
              + " Account or API Key must have the Project Owner role. \n"
              + "- If the user has a pending invitation to join the project's organization, MongoDB"
              + " Cloud modifies it and grants project access. \n"
              + "- If the user doesn't have an invitation to join the organization, MongoDB Cloud"
              + " sends a new invitation that grants the user organization and project access. \n"
              + "- If the user is already active in the project's organization, MongoDB Cloud"
              + " grants access to the project. \n",
      tags = {"MongoDB Cloud Users"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
      },
      requestBody =
          @RequestBody(
              required = true,
              description =
                  "The active or pending MongoDB Cloud user that you want to add to the specified"
                      + " project.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2025_02_19_JSON,
                      schema = @Schema(implementation = ApiGroupUserRequestView.class))),
      responses = {
        @ApiResponse(
            responseCode = "201",
            description = "Created",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2025_02_19_JSON,
                    schema = @Schema(implementation = ApiGroupUserResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_2025_02_19_EXTENSION_TYPE)
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "IAM")}),
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-117-description-ends-with-period",
                  value = "Description ends with an item list."),
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "add"),
              @ExtensionProperty(name = "customMethod", value = "True", parseValue = true)
            })
      })
  public Response addGroupUser(
      @Context Group group,
      @Context AppUser requestingUser,
      @Context AuditInfo auditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") Boolean envelope,
      ApiGroupUserRequestView groupUserRequestView)
      throws SvcException {
    ObjectId groupId = group.getId();
    ObjectId orgId = group.getOrgId();
    String username = groupUserRequestView.getUsername();
    Organization organization = organizationSvc.findById(orgId);
    Set<Role> rolesSet = UserViewUtil.getGroupRolesFromViews(groupUserRequestView.getRoles());
    userValidationSvc.validateFieldsForAddGroupUser(username, rolesSet, envelope);

    Set<GroupRole> groupRoles =
        rolesSet.stream().map(role -> new GroupRole(role, groupId)).collect(toSet());

    Optional<AppUser> activeUserOpt = userSvc.findLocalUserInOrgByUsername(username, orgId);
    if (activeUserOpt.isEmpty()) {
      Optional<Invitation> orgInviteOpt =
          invitationSvc.findOrgInviteByUsernameAndOrgId(username, orgId);
      userValidationSvc.validateUserCanBeAddedToGroup(
          null, orgInviteOpt.orElse(null), groupId, orgId, true, username);
      if (orgInviteOpt.isPresent()) {
        Invitation orgInvite = orgInviteOpt.get();
        if (!orgInvite.getGroupRolesByGroupId(groupId).isEmpty()) {
          throw ApiErrorCode.USER_ALREADY_IN_GROUP.exception(envelope, username, groupId);
        }
        Invitation updatedOrgInvite =
            invitationSvc.updateOrgInvitationToGrantAccessToGroup(
                requestingUser, username, groupId, orgInvite.getId(), groupRoles, auditInfo);
        ApiGroupPendingUserResponseView groupPendingUserResponseView =
            UserViewUtil.invitationToGroupPendingUserView(updatedOrgInvite, groupId);
        return new ApiResponseBuilder(envelope).ok().content(groupPendingUserResponseView).build();
      }
      Date now = new Date();
      Boolean isSalesSold = creditSvc.isSalesSoldOnDate(orgId, localDateOf(now));
      // user is not active in org and does not have a pending org invite, so invite user to org
      // with access to group
      Invitation orgInvite =
          invitationSvc.inviteUserToOrgWithTeamsAndGroupRoleAssignments(
              requestingUser,
              username,
              organization,
              Set.of(Role.ORG_MEMBER),
              Collections.emptySet(),
              groupRoles,
              auditInfo,
              isSalesSold);
      ApiGroupPendingUserResponseView groupPendingUserResponseView =
          UserViewUtil.invitationToGroupPendingUserView(orgInvite, groupId);
      return new ApiResponseBuilder(envelope).ok().content(groupPendingUserResponseView).build();
    }
    AppUser activeUser = activeUserOpt.get();
    if (activeUser.hasGroupId(groupId)) {
      throw ApiErrorCode.USER_ALREADY_IN_GROUP.exception(envelope, username, groupId);
    }
    userValidationSvc.validateUserCanBeAddedToGroup(
        activeUser, null, groupId, orgId, true, username);
    userSvc.addUserToGroup(username, groupId, rolesSet, auditInfo);
    AppUser updatedUser = userSvc.findByUsername(username);
    ApiGroupActiveUserResponseView groupActiveUserResponseView =
        UserViewUtil.userToGroupActiveUserView(
            updatedUser, groupId, requestingUser, requestingUserRedactionSvc);
    return new ApiResponseBuilder(envelope).ok().content(groupActiveUserResponseView).build();
  }

  @GET
  @Path("/{groupId}/users/{userId}")
  @Produces(VersionMediaType.V_2025_02_19_JSON)
  @RolesAllowed(NAME.GROUP_READ_ONLY)
  @Operation(
      summary = "Return One MongoDB Cloud User in One Project",
      operationId = "getGroupUser",
      description =
          "Returns information about the specified MongoDB Cloud user within the context of the"
              + " specified project. To use this resource, the requesting Service Account or API"
              + " Key must have the Project Read Only role.\n\n"
              + "**Note**: You can only use this resource to fetch information about MongoDB Cloud"
              + " human users. To return information about an API Key, use the [Return One"
              + " Organization API Key](#tag/Programmatic-API-Keys/operation/getApiKey)"
              + " endpoint.\n\n"
              + "**Note**: This resource does not return information about pending users invited"
              + " via the deprecated [Invite One MongoDB Cloud User to Join One"
              + " Project](#tag/Projects/operation/createProjectInvitation) endpoint.",
      tags = {"MongoDB Cloud Users"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "userId",
            description =
                "Unique 24-hexadecimal digit string that identifies the pending or active user in"
                    + " the project. If you need to lookup a user's userId or verify a user's"
                    + " status in the organization, use the Return All MongoDB Cloud Users in One"
                    + " Project resource and filter by username.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.OBJECT_ID_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2025_02_19_JSON,
                    schema = @Schema(implementation = ApiGroupUserResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_2025_02_19_EXTENSION_TYPE)
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "IAM")}),
        @Extension(
            name = IPA_EXCEPTION, // TODO CLOUDP-309198
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-117-description-should-not-use-inline-links",
                  value = "Description has multiple links, and externalDocs only supports one."),
            })
      })
  public Response getGroupUser(
      @Context Group group,
      @Context AppUser requestingUser,
      @Parameter(hidden = true) @PathParam("userId") ObjectId userId,
      @Parameter(hidden = true) @QueryParam("envelope") Boolean envelope) {
    ObjectId groupId = group.getId();
    Optional<AppUser> activeUserOpt = userSvc.findLocalUserInGroupById(userId, groupId);
    if (activeUserOpt.isEmpty()) {
      Invitation orgInviteWithGroupRoleAssignment =
          invitationSvc
              .findOrgInviteByUserIdAndGroupId(userId, groupId)
              .orElseThrow(
                  () -> ApiErrorCode.USER_NOT_IN_GROUP.exception(envelope, userId, groupId));
      ApiGroupPendingUserResponseView groupPendingUserView =
          UserViewUtil.invitationToGroupPendingUserView(orgInviteWithGroupRoleAssignment, groupId);
      return new ApiResponseBuilder(envelope).ok().content(groupPendingUserView).build();
    }
    ApiGroupActiveUserResponseView groupActiveUserView =
        UserViewUtil.userToGroupActiveUserView(
            activeUserOpt.get(), groupId, requestingUser, requestingUserRedactionSvc);
    return new ApiResponseBuilder(envelope).ok().content(groupActiveUserView).build();
  }

  @DELETE
  @Path("/{groupId}/users/{userId}")
  @Produces(VersionMediaType.V_2025_02_19_JSON)
  @RolesAllowed(NAME.GROUP_USER_ADMIN)
  @Operation(
      summary = "Remove One MongoDB Cloud User from One Project",
      operationId = "removeGroupUser",
      description =
          "Removes one MongoDB Cloud user from the specified project. You can remove an active user"
              + " or a user that has not yet accepted the invitation to join the organization. To"
              + " use this resource, the requesting Service Account or API Key must have the"
              + " Project Owner role.\n\n"
              + "**Note**: This resource cannot be used to remove pending users invited via the"
              + " deprecated Invite One MongoDB Cloud User to Join One Project endpoint.\n\n"
              + "**Note**: To remove pending or active users, use v2-{2025-02-19} or later. If"
              + " using a deprecated version, only active users can be removed.",
      externalDocs =
          @ExternalDocumentation(
              description = "Deprecated: Invite One MongoDB Cloud User to Join One Project",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Projects/operation/createProjectInvitation"),
      tags = {"MongoDB Cloud Users"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "userId",
            description =
                "Unique 24-hexadecimal digit string that identifies the pending or active user in"
                    + " the project. If you need to lookup a user's userId or verify a user's"
                    + " status in the organization, use the [Return All MongoDB Cloud Users in One"
                    + " Project](#tag/MongoDB-Cloud-Users/operation/listProjectUsers) resource and"
                    + " filter by username.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.OBJECT_ID_REGEX),
            required = true,
            extensions = {
              @Extension(
                  name = IPA_EXCEPTION,
                  properties = {
                    @ExtensionProperty(
                        name = "xgen-IPA-117-description-should-not-use-inline-links",
                        value = "Parameters don't support externalDocs."),
                  })
            })
      },
      responses = {
        @ApiResponse(
            responseCode = "204",
            description = OpenApiConst.ResponseDescriptions.NO_BODY,
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2025_02_19_JSON,
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = "x-xgen-version",
                              value = VersionMediaType.V_2025_02_19_EXTENSION_TYPE)
                        }),
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-xgen-version", value = "2023-01-01")
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-sunset", value = X_SUNSET_DATE_2026_07_31)
                        })
                  })
            }),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "IAM")}),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "remove"),
              @ExtensionProperty(name = "customMethod", value = "True", parseValue = true)
            })
      })
  public Response removeGroupUser(
      @Context Group group,
      @Context AuditInfo auditInfo,
      @Parameter(hidden = true) @PathParam("userId") ObjectId userId,
      @Parameter(hidden = true) @QueryParam("envelope") Boolean envelope)
      throws Exception {
    ObjectId groupId = group.getId();
    Optional<AppUser> activeUserOpt = userSvc.findLocalUserInGroupById(userId, groupId);
    if (activeUserOpt.isEmpty()) {
      Invitation orgInviteWithGroupRoleAssignment =
          invitationSvc
              .findOrgInviteByUserIdAndGroupId(userId, groupId)
              .orElseThrow(
                  () -> ApiErrorCode.USER_NOT_IN_GROUP.exception(envelope, userId, groupId));
      invitationSvc.removeAllGroupRoleAssignmentsForGroupFromOrgInvitation(
          orgInviteWithGroupRoleAssignment, groupId, auditInfo);
      return new ApiResponseBuilder(envelope).noContent().build();
    }
    groupSvc.removeUserFromGroup(activeUserOpt.get(), group, auditInfo);
    return new ApiResponseBuilder(envelope).noContent().build();
  }

  @POST
  @Path("{groupId}/users/{userId}:addRole")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces(VersionMediaType.V_2025_02_19_JSON)
  @RolesAllowed(NAME.GROUP_USER_ADMIN)
  @Operation(
      summary = "Add One Project Role to One MongoDB Cloud User",
      operationId = "addGroupUserRole",
      description =
          "Adds one project-level role to the MongoDB Cloud user. You can add a role to an active"
              + " user or a user that has been invited to join the project. To use this resource,"
              + " the requesting Service Account or API Key must have the Project Owner role.\n\n"
              + "**Note**: This resource cannot be used to add a role to users invited using the"
              + " deprecated Invite One MongoDB Cloud User to Join One Project endpoint.",
      externalDocs =
          @ExternalDocumentation(
              description = "Deprecated: Invite One MongoDB Cloud User to Join One Project",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Projects/operation/createProjectInvitation"),
      tags = {"MongoDB Cloud Users"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "userId",
            description =
                "Unique 24-hexadecimal digit string that identifies the pending or active user in"
                    + " the project. If you need to lookup a user's userId or verify a user's"
                    + " status in the organization, use the Return All MongoDB Cloud Users in One"
                    + " Project resource and filter by username.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.OBJECT_ID_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2025_02_19_JSON,
                    schema = @Schema(implementation = ApiGroupUserResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_2025_02_19_EXTENSION_TYPE)
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Project-level role to assign to the MongoDB Cloud user.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2025_02_19_JSON,
                      schema = @Schema(implementation = ApiAddOrRemoveGroupRoleRequestView.class))),
      extensions = {
        @Extension(properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "IAM")})
      })
  public Response addGroupRole(
      @Context Group group,
      @Context AuditInfo auditInfo,
      @Context AppUser requestingUser,
      @Parameter(hidden = true) @PathParam("userId") ObjectId userId,
      @Parameter(hidden = true) @QueryParam("envelope") Boolean envelope,
      ApiAddOrRemoveGroupRoleRequestView addGroupRoleRequestView)
      throws SvcException {
    ObjectId groupId = group.getId();
    Role role =
        Optional.ofNullable(addGroupRoleRequestView.getGroupRole())
            .map(ApiGroupRoleView::getRole)
            .orElseThrow(
                () ->
                    ApiErrorCode.VALIDATION_ERROR.exception(
                        envelope, ApiAddOrRemoveGroupRoleRequestView.GROUP_ROLE_FIELD));

    Optional<AppUser> activeUserOpt = userSvc.findLocalUserInGroupById(userId, groupId);
    if (activeUserOpt.isEmpty()) {
      Invitation orgInvite =
          invitationSvc
              .findOrgInviteByUserIdAndGroupId(userId, groupId)
              .orElseThrow(
                  () -> ApiErrorCode.USER_NOT_IN_GROUP.exception(envelope, userId, groupId));
      Invitation updatedInvitation =
          invitationSvc.addGroupRoleAssignmentToOrgInvitation(orgInvite, role, groupId);
      ApiGroupPendingUserResponseView groupPendingUserResponseView =
          UserViewUtil.invitationToGroupPendingUserView(updatedInvitation, groupId);
      return new ApiResponseBuilder(envelope).ok().content(groupPendingUserResponseView).build();
    }
    userValidationSvc.validateUserIsNotFederatedWithRoleMappings(
        activeUserOpt.get(), group.getOrgId(), envelope);
    AppUser updatedUser = userSvc.addUserRoleInGroup(activeUserOpt.get(), groupId, role, auditInfo);
    ApiGroupActiveUserResponseView groupActiveUserResponseView =
        UserViewUtil.userToGroupActiveUserView(
            updatedUser, groupId, requestingUser, requestingUserRedactionSvc);
    return new ApiResponseBuilder(envelope).ok().content(groupActiveUserResponseView).build();
  }

  @POST
  @Path("{groupId}/users/{userId}:removeRole")
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces(VersionMediaType.V_2025_02_19_JSON)
  @RolesAllowed(NAME.GROUP_USER_ADMIN)
  @Operation(
      summary = "Remove One Project Role from One MongoDB Cloud User",
      operationId = "removeGroupUserRole",
      description =
          "Removes one project-level role from the MongoDB Cloud user. You can remove a role from"
              + " an active user or a user that has been invited to join the project. To replace a"
              + " user's only role, add the new role before removing the old role. A user must have"
              + " at least one role at all times. To use this resource, the requesting Service"
              + " Account or API Key must have the Project Owner role.\n\n"
              + "**Note**: This resource cannot be used to remove a role from users invited using"
              + " the deprecated Invite One MongoDB Cloud User to Join One Project endpoint.",
      externalDocs =
          @ExternalDocumentation(
              description = "Deprecated: Invite One MongoDB Cloud User to Join One Project",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Projects/operation/createProjectInvitation"),
      tags = {"MongoDB Cloud Users"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "userId",
            description =
                "Unique 24-hexadecimal digit string that identifies the pending or active user in"
                    + " the project. If you need to lookup a user's userId or verify a user's"
                    + " status in the organization, use the Return All MongoDB Cloud Users in One"
                    + " Project resource and filter by username.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.OBJECT_ID_REGEX),
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2025_02_19_JSON,
                    schema = @Schema(implementation = ApiGroupUserResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_2025_02_19_EXTENSION_TYPE)
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Project-level role to remove from the MongoDB Cloud user.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2025_02_19_JSON,
                      schema = @Schema(implementation = ApiAddOrRemoveGroupRoleRequestView.class))),
      extensions = {
        @Extension(properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "IAM")})
      })
  public Response removeGroupRole(
      @Context Group group,
      @Context AuditInfo auditInfo,
      @Context AppUser requestingUser,
      @Parameter(hidden = true) @PathParam("userId") ObjectId userId,
      @Parameter(hidden = true) @QueryParam("envelope") Boolean envelope,
      ApiAddOrRemoveGroupRoleRequestView addGroupRoleRequestView)
      throws SvcException {
    ObjectId groupId = group.getId();
    Role role =
        Optional.ofNullable(addGroupRoleRequestView.getGroupRole())
            .map(ApiGroupRoleView::getRole)
            .orElseThrow(
                () ->
                    ApiErrorCode.VALIDATION_ERROR.exception(
                        envelope, ApiAddOrRemoveGroupRoleRequestView.GROUP_ROLE_FIELD));

    Optional<AppUser> activeUserOpt = userSvc.findLocalUserInGroupById(userId, groupId);
    if (activeUserOpt.isEmpty()) {
      Invitation orgInvite =
          invitationSvc
              .findOrgInviteByUserIdAndGroupId(userId, groupId)
              .orElseThrow(
                  () -> ApiErrorCode.USER_NOT_IN_GROUP.exception(envelope, userId, groupId));
      Invitation updatedInvitation =
          pendingUserSvc.removeUserRoleInGroup(orgInvite, role, groupId, envelope);
      ApiGroupPendingUserResponseView groupPendingUserResponseView =
          UserViewUtil.invitationToGroupPendingUserView(updatedInvitation, groupId);
      return new ApiResponseBuilder(envelope).ok().content(groupPendingUserResponseView).build();
    }
    userValidationSvc.validateUserIsNotFederatedWithRoleMappings(
        activeUserOpt.get(), group.getOrgId(), envelope);
    AppUser updatedUser =
        activeUserSvc.removeUserRoleInGroup(
            activeUserOpt.get(), role, groupId, envelope, auditInfo);
    ApiGroupActiveUserResponseView groupActiveUserResponseView =
        UserViewUtil.userToGroupActiveUserView(
            updatedUser, groupId, requestingUser, requestingUserRedactionSvc);
    return new ApiResponseBuilder(envelope).ok().content(groupActiveUserResponseView).build();
  }

  @GET
  @Path("/{groupId}/users/")
  @Produces(VersionMediaType.V_2025_02_19_JSON)
  @RolesAllowed(NAME.GROUP_READ_ONLY)
  @Operation(
      summary = "Return All MongoDB Cloud Users in One Project",
      operationId = "listGroupUsers",
      description =
          "Returns details about the pending and active MongoDB Cloud users associated with the"
              + " specified project. To use this resource, the requesting Service Account or API"
              + " Key must have the Project Read Only role.\n\n"
              + "**Note**: This resource cannot be used to view details about users invited via the"
              + " deprecated Invite One MongoDB Cloud User to Join One Project endpoint.\n\n"
              + "**Note**: To return both pending and active users, use v2-{2025-02-19} or later."
              + " If using a deprecated version, only active users will be returned.",
      externalDocs =
          @ExternalDocumentation(
              description = "Deprecated: Invite One MongoDB Cloud User to Join One Project",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Projects/operation/createProjectInvitation"),
      tags = {"MongoDB Cloud Users"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "includeCount"),
        @Parameter(ref = "itemsPerPage"),
        @Parameter(ref = "pageNum"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "flattenTeams",
            description =
                "Flag that indicates whether the returned list should include users who belong to"
                    + " a team with a role in this project. You might not have assigned the"
                    + " individual users a role in this project. If `\"flattenTeams\" : false`,"
                    + " this resource returns only users with a role in the project. "
                    + " If `\"flattenTeams\" : true`, this resource returns both users with roles"
                    + " in the project and users who belong to teams with roles in the project.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "boolean", defaultValue = "false")),
        @Parameter(
            name = "includeOrgUsers",
            description =
                "Flag that indicates whether the returned list should include users with implicit"
                    + " access to the project, the Organization Owner or Organization"
                    + " Read Only role. You might not have assigned the individual users a role in"
                    + " this project. If `\"includeOrgUsers\": false`, this resource returns only"
                    + " users with a role in the project. If `\"includeOrgUsers\": true`, this"
                    + " resource returns both users with roles in the project and users who have"
                    + " implicit access to the project through their organization role.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "boolean", defaultValue = "false")),
        @Parameter(
            name = "orgMembershipStatus",
            description =
                "Flag that indicates whether to filter the returned list by users organization"
                    + " membership status. If you exclude this parameter, this resource returns"
                    + " both pending and active users. Not supported in deprecated versions.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "string", example = "ACTIVE")),
        @Parameter(
            name = "username",
            description = "Email address to filter users by. Not supported in deprecated versions.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "string", format = "email")),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2025_02_19_JSON,
                  schema = @Schema(implementation = PaginatedApiGroupUserView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = "x-xgen-version",
                              value = VersionMediaType.V_2025_02_19_EXTENSION_TYPE)
                        }),
                  }),
              @Content(
                  mediaType = VersionMediaType.V_2023_01_01_JSON,
                  schema =
                      @Schema(implementation = PaginatedApiAppUserView.class, deprecated = true),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-xgen-version", value = "2023-01-01")
                        }),
                    @Extension(
                        properties = {
                          @ExtensionProperty(
                              name = "x-sunset",
                              value = VersioningConstants.X_SUNSET_DATE_TO_BE_DECIDED)
                        })
                  })
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "IAM")})
      })
  public Response getGroupUsers(
      @Context HttpServletRequest request,
      @Context Group group,
      @Context AppUser requestingUser,
      @Parameter(hidden = true) @QueryParam("flattenTeams") @DefaultValue("false")
          boolean flattenTeams,
      @Parameter(hidden = true) @QueryParam("includeOrgUsers") @DefaultValue("false")
          boolean includeOrgUsers,
      @Parameter(hidden = true) @QueryParam("orgMembershipStatus")
          ApiOrgMembershipStatusView orgMembershipStatus,
      @Parameter(hidden = true) @QueryParam("username") String username,
      @Parameter(hidden = true) @QueryParam("envelope") @DefaultValue("false") boolean envelope) {
    boolean returnActiveUsers = true;
    boolean returnInvites = true;
    if (orgMembershipStatus == ApiOrgMembershipStatusView.PENDING) {
      returnActiveUsers = false;
    } else if (orgMembershipStatus == ApiOrgMembershipStatusView.ACTIVE) {
      returnInvites = false;
    }

    try (MongoCursor<Document> cursor =
        usersAndInvitesSvc.findByGroup(
            group, returnActiveUsers, returnInvites, flattenTeams, includeOrgUsers, username)) {
      return handlePagination(
          request,
          cursor,
          doc ->
              doc.getString(Invitation.TOKEN_FIELD) == null
                  ? UserViewUtil.documentToGroupActiveUserView(
                      doc, group.getId(), requestingUser, requestingUserRedactionSvc)
                  : UserViewUtil.documentToGroupPendingUserView(doc, group.getId()),
          envelope);
    }
  }

  /** See https://github.com/swagger-api/swagger-core/issues/3496 */
  @Schema(name = "PaginatedGroupUserView")
  public static final class PaginatedApiGroupUserView
      extends ApiListView<ApiGroupUserResponseView> {}
}
