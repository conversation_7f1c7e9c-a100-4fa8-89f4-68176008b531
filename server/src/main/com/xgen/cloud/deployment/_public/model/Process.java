package com.xgen.cloud.deployment._public.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.xgen.cloud.common.model._public.annotation.UnmanagedApiExposedField;
import com.xgen.cloud.common.model._public.annotation.UnmanagedApiIgnoredField;
import com.xgen.cloud.common.model._public.annotation.UnmanagedApiRedactedField;
import com.xgen.cloud.common.util._public.json.JsonViews;
import com.xgen.cloud.common.util._public.json.JsonViews.Agent;
import com.xgen.cloud.common.util._public.util.BaseHostUtils;
import com.xgen.cloud.common.util._public.util.PathUtils;
import com.xgen.cloud.deployment._public.model.dataexplorer.DataExplorerConfig;
import com.xgen.cloud.deployment._public.model.monitoring.ProcessMonitoringState;
import com.xgen.cloud.deployment._public.model.realtime.RealtimeConfig;
import dev.morphia.annotations.Converters;
import dev.morphia.annotations.Embedded;
import dev.morphia.annotations.Property;
import dev.morphia.annotations.Transient;
import jakarta.annotation.Nullable;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang.builder.HashCodeBuilder;
import org.bson.types.BSONTimestamp;

@Embedded
@Converters(ProcessTypeConverter.class)
@JsonIgnoreProperties({"lastChangeRsConfForRestore"})
public class Process extends WithProcessArguments {

  public static final String NAME_FIELD = "name";
  public static final String HOSTNAME_FIELD = "hostname";
  public static final String PROCESS_TYPE_FIELD = "processType";
  public static final String VERSION_FIELD = "version";
  public static final String AUTH_SCHEMA_VERSION_FIELD = "authSchemaVersion";
  public static final String FEATURE_COMPATIBILITY_VERSION = "featureCompatibilityVersion";
  public static final String FULL_VERSION_FIELD = "fullVersion";
  public static final String ALIAS_FIELD = "alias";
  public static final String TEMPORARY_PORT_FIELD = "temporaryPort";
  public static final String MANAGED_FIELD = "managed";
  public static final String STATE_FIELD = "state";
  public static final String CLUSTER_FIELD = "cluster";
  public static final String BACKUP_RESTORE_URL_FIELD = "backupRestoreUrl";
  public static final String BACKUP_RESTORE_IS_SUCCESSIVE_UPGRADE =
      "backupRestoreIsSuccessiveUpgrade";
  public static final String BACKUP_RESTORE_URL_V3_FIELD = "BackupRestoreUrlV3";
  public static final String BACKUP_PARALLEL_RESTORE_URL_FIELD = "backupParallelRestoreUrl";
  public static final String BACKUP_PARALLEL_RESTORE_NUM_CHUNKS = "backupParallelRestoreNumChunks";
  public static final String BACKUP_PARALLEL_RESTORE_NUM_WORKERS =
      "backupParallelRestoreNumWorkers";
  public static final String BACKUP_THIRD_PARTY_RESTORE_BASE_URL_FIELD =
      "backupThirdPartyRestoreBaseUrl";

  public static final String BACKUP_THIRD_PARTY_OPLOG_STORE_TYPE = "backupThirdPartyOplogStoreType";
  public static final String BACKUP_RESTORE_OPLOG_BASE_URL_FIELD = "backupRestoreOplogBaseUrl";
  public static final String BACKUP_RESTORE_RS_VERSION_FIELD = "backupRestoreRsVersion";
  public static final String BACKUP_RESTORE_ELECTION_TERM_FIELD = "backupRestoreElectionTerm";
  public static final String BACKUP_RESTORE_CHECKPOINT_TIMESTAMP_FIELD =
      "backupRestoreCheckpointTimestamp";
  public static final String BACKUP_RESTORE_CERTIFICATE_VALIDATION_HOSTNAME =
      "backupRestoreCertificateValidationHostname";
  public static final String BACKUP_RESTORE_OPLOG_FIELD = "backupRestoreOplog";
  public static final String BACKUP_RESTORE_DESIRED_TIME = "backupRestoreDesiredTime";
  public static final String BACKUP_RESTORE_SOURCE_RS_ID = "backupRestoreSourceRsId";
  public static final String BACKUP_RESTORE_FILTER_LIST = "backupRestoreFilterList";
  public static final String BACKUP_RESTORE_FILTERED_FILELIST_URL =
      "backupRestoreFilteredFileListUrl";
  public static final String BACKUP_RESTORE_JOB_ID = "backupRestoreJobId";
  public static final String BACKUP_RESTORE_VERIFICATION_KEY = "backupRestoreVerificationKey";
  public static final String BACKUP_RESTORE_SOURCE_GROUP_ID = "backupRestoreSourceGroupId";
  public static final String BACKUP_PIT_RESTORE_TYPE = "backupPitRestoreType";
  public static final String BACKUP_RESTORE_SYSTEM_USERS_UUID = "backupRestoreSystemUsersUUID";
  public static final String BACKUP_RESTORE_SYSTEM_ROLES_UUID = "backupRestoreSystemRolesUUID";
  public static final String BACKUP_SHARD_ID_RESTORE_MAPS = "backupShardIdRestoreMaps";
  public static final String BACKUP_RESTORE_BALANCER_SETTINGS = "backupRestoreBalancerSettings";
  public static final String BACKUP_RESTORE_CONFIG_SETTINGS_UUID =
      "backupRestoreConfigSettingsUUID";
  public static final String BACKUP_RESTORE_IS_CONFIG_SHARD = "backupRestoreIsConfigShard";
  public static final String BACKUP_RESTORE_IS_S3_DIRECT_RESTORE = "backupRestoreIsS3DirectRestore";
  public static final String BACKUP_RESTORE_S3_DIRECT_RESTORE_NUM_WORKERS =
      "backupRestoreS3DirectRestoreNumWorkers";
  public static final String REALTIME_CONFIG = "realtimeConfig";
  public static final String DATAEXPLORER_CONFIG = "dataExplorerConfig";
  public static final String ENCRYPTION_PROVIDER_FIELD = "encryptionProvider";
  public static final String ENCRYPTION_KEY_REGION_OVERRIDE_FIELD = "encryptionKeyRegionOverride";
  public static final String KMIP_PROXY_PORT_FIELD = "kmipProxyPort";
  public static final String KMIP_PROXY_DISABLED_FIELD = "kmipProxyDisabled";
  public static final String DIRECT_ATTACH_VERIFICATION_KEY = "directAttachVerificationKey";
  public static final String DIRECT_ATTACH_SOURCE_CLUSTER_KEY = "directAttachSourceClusterName";
  public static final String DIRECT_ATTACH_PRE_WARM_STRATEGY = "directAttachPreWarmStrategy";
  public static final String DIRECT_ATTACH_PRE_WARM_GLOBS = "directAttachPreWarmGlobs";
  public static final String DIRECT_ATTACH_PRE_WARM_CONCURRENCY = "directAttachPreWarmConcurrency";
  public static final String DIRECT_ATTACH_PRE_WARM_BLOCK_SIZE = "directAttachPreWarmBlockSize";
  public static final String DIRECT_ATTACH_PRE_WARM_BLOCK_READ_SIZE =
      "directAttachPreWarmBlockReadSize";
  public static final String DIRECT_ATTACH_PRE_WARM_SHOULD_USE_GO_BINARY =
      "directAttachPreWarmShouldUseGoBinary";
  public static final String DIRECT_ATTACH_PRE_WARM_SHOULD_WARM_EMPTY_BLOCKS =
      "directAttachPreWarmShouldWarmEmptyBlocks";
  public static final String DIRECT_ATTACH_PRE_WARM_SHOULD_SKIP_NON_PRIORITY =
      "directAttachPreWarmShouldSkipNonPriority";

  public static final String DIRECT_ATTACH_SHOULD_FILTER_BY_FILE_LISTS_KEY =
      "directAttachShouldFilterByFileList";
  public static final String DIRECT_ATTACH_REPL_WRITER_THREAD_COUNT =
      "directAttachReplWriterThreadCount";
  public static final String OPLOG_COLLECTION_FILE_NAME = "oplogCollectionFileName";
  public static final String CREDENTIALS_VERSION_FIELD = "credentialsVersion";
  public static final String LDAP_QUERY_PASSWORD_VERSION_FIELD = "ldapQueryPasswordVersion";
  public static final String HORIZONS_FIELD = "horizons";
  public static final String DEFAULT_RW_CONCERN_FIELD = "defaultRWConcern";
  public static final String PROCESS_IMPORT_CLUSTER_WIDE_CONFIGURATION = "clusterWideConfiguration";
  public static final String PROFILING_CONFIGURATION = "profilingConfig";
  public static final String REGIONAL_BASE_URL_FIELD = "regionBaseUrl";
  public static final String REGIONAL_BASE_AGENT_URL_FIELD = "regionBaseAgentUrl";
  public static final String REGIONAL_BASE_REALTIME_URL_FIELD = "regionBaseRealtimeUrl";
  public static final String RESTART_INTERVAL_TIME_MS = "restartIntervalTimeMs";
  public static final String UNSAFE_ROLLING_OPERATION_FIELD = "unsafeRollingOperation";

  public static final String REDACTED_VALUE = "<redacted>";

  @JsonProperty(NAME_FIELD)
  @Property(NAME_FIELD)
  @UnmanagedApiExposedField
  protected String _name;

  @JsonProperty(HOSTNAME_FIELD)
  @Property(HOSTNAME_FIELD)
  @UnmanagedApiExposedField
  protected String _hostname;

  @JsonProperty(BACKUP_RESTORE_URL_FIELD)
  @JsonInclude(Include.NON_NULL)
  @JsonSerialize(using = RedactOnUIJsonSerializer.class)
  @Property(BACKUP_RESTORE_URL_FIELD)
  @UnmanagedApiRedactedField
  protected String _backupRestoreUrl;

  @JsonProperty(BACKUP_RESTORE_URL_V3_FIELD)
  @JsonInclude(Include.NON_NULL)
  @JsonSerialize(using = RedactOnUIJsonSerializer.class)
  @Property(BACKUP_RESTORE_URL_V3_FIELD)
  @UnmanagedApiRedactedField
  protected String _backupRestoreUrlV3;

  @JsonProperty(BACKUP_PARALLEL_RESTORE_URL_FIELD)
  @JsonInclude(Include.NON_NULL)
  @JsonSerialize(using = RedactOnUIJsonSerializer.class)
  @Property(BACKUP_PARALLEL_RESTORE_URL_FIELD)
  @UnmanagedApiRedactedField
  protected String _backupParallelRestoreUrl;

  @JsonProperty(BACKUP_PARALLEL_RESTORE_NUM_CHUNKS)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_PARALLEL_RESTORE_NUM_CHUNKS)
  @UnmanagedApiExposedField
  protected Integer _backupParallelRestoreNumChunks;

  @JsonProperty(BACKUP_PARALLEL_RESTORE_NUM_WORKERS)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_PARALLEL_RESTORE_NUM_WORKERS)
  @UnmanagedApiExposedField
  protected Integer _backupParallelRestoreNumWorkers;

  @JsonProperty(BACKUP_THIRD_PARTY_RESTORE_BASE_URL_FIELD)
  @JsonInclude(Include.NON_NULL)
  @JsonSerialize(using = RedactOnUIJsonSerializer.class)
  @Property(BACKUP_THIRD_PARTY_RESTORE_BASE_URL_FIELD)
  @UnmanagedApiRedactedField
  protected String _backupThirdPartyRestoreBaseUrl;

  @JsonProperty(BACKUP_THIRD_PARTY_OPLOG_STORE_TYPE)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_THIRD_PARTY_OPLOG_STORE_TYPE)
  @UnmanagedApiRedactedField
  protected String _backupThirdPartyOplogStoreType;

  @JsonProperty(BACKUP_RESTORE_OPLOG_BASE_URL_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_RESTORE_OPLOG_BASE_URL_FIELD)
  @UnmanagedApiRedactedField
  protected String _backupRestoreOplogBaseUrl;

  @JsonProperty(BACKUP_RESTORE_RS_VERSION_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_RESTORE_RS_VERSION_FIELD)
  @UnmanagedApiExposedField
  protected Integer _backupRestoreRsVersion;

  @JsonProperty(BACKUP_RESTORE_ELECTION_TERM_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_RESTORE_ELECTION_TERM_FIELD)
  @UnmanagedApiExposedField
  protected Long _backupRestoreElectionTerm;

  @JsonProperty(BACKUP_RESTORE_CHECKPOINT_TIMESTAMP_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Embedded(BACKUP_RESTORE_CHECKPOINT_TIMESTAMP_FIELD)
  @UnmanagedApiExposedField
  protected BSONTimestamp _backupRestoreCheckpointTime;

  @JsonProperty(BACKUP_RESTORE_CERTIFICATE_VALIDATION_HOSTNAME)
  @JsonInclude(Include.NON_NULL)
  @JsonSerialize(using = RedactOnUIJsonSerializer.class)
  @Property(BACKUP_RESTORE_CERTIFICATE_VALIDATION_HOSTNAME)
  @UnmanagedApiExposedField
  protected String _backupRestoreCertificateValidationHostname;

  @JsonProperty(BACKUP_RESTORE_OPLOG_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Embedded(BACKUP_RESTORE_OPLOG_FIELD)
  @UnmanagedApiExposedField
  protected BackupRestoreOplog _backupRestoreOplog;

  @JsonProperty(BACKUP_RESTORE_DESIRED_TIME)
  @JsonInclude(Include.NON_NULL)
  @Embedded(BACKUP_RESTORE_DESIRED_TIME)
  @UnmanagedApiExposedField
  protected BSONTimestamp _backupRestoreDesiredTime;

  @JsonProperty(BACKUP_RESTORE_SOURCE_RS_ID)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_RESTORE_SOURCE_RS_ID)
  @UnmanagedApiExposedField
  protected String _backupRestoreSourceRsId;

  @JsonProperty(BACKUP_RESTORE_IS_CONFIG_SHARD)
  @JsonInclude(Include.NON_NULL)
  @Embedded(BACKUP_RESTORE_IS_CONFIG_SHARD)
  @UnmanagedApiExposedField
  protected Boolean _backupRestoreIsConfigShard;

  @JsonProperty(BACKUP_RESTORE_IS_S3_DIRECT_RESTORE)
  @JsonInclude(Include.NON_NULL)
  @Embedded(BACKUP_RESTORE_IS_S3_DIRECT_RESTORE)
  @UnmanagedApiExposedField
  protected Boolean _backupRestoreIsS3DirectRestore;

  @JsonProperty(BACKUP_RESTORE_S3_DIRECT_RESTORE_NUM_WORKERS)
  @JsonInclude(Include.NON_NULL)
  @Embedded(BACKUP_RESTORE_S3_DIRECT_RESTORE_NUM_WORKERS)
  @UnmanagedApiExposedField
  protected Integer _backupRestoreS3DirectRestoreNumWorkers;

  @JsonProperty(BACKUP_RESTORE_IS_SUCCESSIVE_UPGRADE)
  @JsonInclude(Include.NON_NULL)
  @Embedded(BACKUP_RESTORE_IS_SUCCESSIVE_UPGRADE)
  @UnmanagedApiExposedField
  protected Boolean _backupRestoreIsSuccessiveUpgrade;

  @JsonProperty(BACKUP_RESTORE_FILTER_LIST)
  @JsonInclude(Include.NON_NULL)
  @Embedded(BACKUP_RESTORE_FILTER_LIST)
  @UnmanagedApiExposedField
  protected BackupRestoreFilterList _backupRestoreFilterList;

  @JsonProperty(BACKUP_RESTORE_FILTERED_FILELIST_URL)
  @JsonInclude(Include.NON_NULL)
  @Embedded(BACKUP_RESTORE_FILTERED_FILELIST_URL)
  @UnmanagedApiRedactedField
  protected String _backupRestoreFilteredFileListUrl;

  @JsonProperty(BACKUP_RESTORE_JOB_ID)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_RESTORE_JOB_ID)
  @UnmanagedApiExposedField
  protected String _backupRestoreJobId;

  @JsonProperty(BACKUP_RESTORE_VERIFICATION_KEY)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_RESTORE_VERIFICATION_KEY)
  @UnmanagedApiExposedField(ignoreSuspiciousFieldNameError = true)
  protected String _backupRestoreVerificationKey;

  @JsonProperty(BACKUP_RESTORE_SOURCE_GROUP_ID)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_RESTORE_SOURCE_GROUP_ID)
  @UnmanagedApiExposedField
  protected String _backupRestoreSourceGroupId;

  @JsonProperty(BACKUP_PIT_RESTORE_TYPE)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_PIT_RESTORE_TYPE)
  @UnmanagedApiExposedField
  protected String _backupPitRestoreType;

  @JsonProperty(BACKUP_RESTORE_SYSTEM_USERS_UUID)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_RESTORE_SYSTEM_USERS_UUID)
  @UnmanagedApiExposedField
  protected String _backupRestoreSystemUsersUUID;

  @JsonProperty(BACKUP_RESTORE_SYSTEM_ROLES_UUID)
  @JsonInclude(Include.NON_NULL)
  @Property(BACKUP_RESTORE_SYSTEM_ROLES_UUID)
  @UnmanagedApiExposedField
  protected String _backupRestoreSystemRolesUUID;

  @JsonProperty(BACKUP_SHARD_ID_RESTORE_MAPS)
  // NON_EMPTY is used here instead of NON_NULL because an empty list is just as useless as a list
  // that is null
  @JsonInclude(Include.NON_EMPTY)
  @Embedded(BACKUP_SHARD_ID_RESTORE_MAPS)
  @UnmanagedApiExposedField
  protected List<BackupRestoreShardIdRestoreMap> _backupBackupRestoreShardIdRestoreMaps;

  @JsonProperty(BACKUP_RESTORE_BALANCER_SETTINGS)
  @JsonInclude(Include.NON_NULL)
  @Embedded(BACKUP_RESTORE_BALANCER_SETTINGS)
  @UnmanagedApiExposedField
  protected BalancerSettings _backupRestoreBalancerSettings;

  @JsonProperty(BACKUP_RESTORE_CONFIG_SETTINGS_UUID)
  @JsonInclude(Include.NON_NULL)
  @Embedded(BACKUP_RESTORE_CONFIG_SETTINGS_UUID)
  @UnmanagedApiExposedField
  protected String _backupRestoreConfigSettingsUUID;

  @JsonProperty(ALIAS_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Property(ALIAS_FIELD)
  @UnmanagedApiExposedField
  protected String _alias;

  @JsonProperty(TEMPORARY_PORT_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Property(TEMPORARY_PORT_FIELD)
  @UnmanagedApiExposedField
  protected Integer _temporaryPort;

  @JsonProperty(VERSION_FIELD)
  @Property(VERSION_FIELD)
  @UnmanagedApiExposedField
  protected String _version;

  @JsonProperty(AUTH_SCHEMA_VERSION_FIELD)
  @Property(AUTH_SCHEMA_VERSION_FIELD)
  @UnmanagedApiExposedField
  protected Integer _authSchemaVersion;

  @JsonProperty(FEATURE_COMPATIBILITY_VERSION)
  @JsonInclude(Include.NON_NULL)
  @Property(FEATURE_COMPATIBILITY_VERSION)
  @UnmanagedApiExposedField
  protected String _featureCompatibilityVersion;

  @JsonProperty(FULL_VERSION_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Embedded(FULL_VERSION_FIELD)
  @UnmanagedApiExposedField
  protected MongoDbBuild _fullVersion;

  @JsonProperty(PROCESS_TYPE_FIELD)
  @Property(PROCESS_TYPE_FIELD)
  @UnmanagedApiExposedField
  protected ProcessType _processType;

  @JsonProperty(CLUSTER_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Property(CLUSTER_FIELD)
  @UnmanagedApiExposedField
  protected String _cluster;

  @JsonProperty("disabled")
  @JsonInclude(Include.NON_NULL)
  @Property("disabled")
  @UnmanagedApiExposedField
  protected Boolean _disabled;

  @JsonProperty("manualMode")
  @JsonInclude(Include.NON_NULL)
  @Property("manualMode")
  @UnmanagedApiExposedField
  protected Boolean _manualMode;

  @JsonProperty("logLevel")
  @JsonInclude(Include.NON_NULL)
  @Property("logLevel")
  @UnmanagedApiExposedField
  protected Integer _logLevel;

  @JsonProperty("numCores")
  @JsonInclude(Include.NON_NULL)
  @Property("numCores")
  @UnmanagedApiExposedField
  protected Integer _numCores;

  @JsonProperty("cpuAffinity")
  @JsonInclude(Include.NON_NULL)
  @Property("cpuAffinity")
  @UnmanagedApiExposedField
  protected List<Integer> _cpuAffinity;

  @JsonProperty("cpuSocketBinding")
  @JsonInclude(Include.NON_NULL)
  @Property("cpuSocketBinding")
  @UnmanagedApiExposedField
  protected List<Integer> _cpuSocketBinding;

  @JsonProperty("logRotate")
  @JsonInclude(Include.NON_NULL)
  @Embedded("logRotate")
  @UnmanagedApiExposedField
  protected LogRotate _logRotate;

  @JsonProperty("auditLogRotate")
  @JsonInclude(Include.NON_NULL)
  @Embedded("auditLogRotate")
  @UnmanagedApiExposedField
  protected LogRotate _auditLogRotate;

  @JsonProperty("lastResync")
  @JsonInclude(Include.NON_NULL)
  @Property("lastResync")
  @UnmanagedApiExposedField
  protected Date _lastResync;

  @JsonProperty("stepDownPrimaryForResync")
  @JsonInclude(Include.NON_NULL)
  @Property("stepDownPrimaryForResync")
  @UnmanagedApiExposedField
  protected Boolean _stepDownPrimaryForResync;

  @JsonProperty("lastThirdPartyRestoreResync")
  @JsonInclude(Include.NON_NULL)
  @Property("lastThirdPartyRestoreResync")
  @UnmanagedApiExposedField
  protected Date _lastThirdPartyRestoreResync;

  @JsonProperty("lastCompact")
  @JsonInclude(Include.NON_NULL)
  @Property("lastCompact")
  @UnmanagedApiExposedField
  protected Date _lastCompact;

  @JsonProperty("lastRestart")
  @JsonInclude(Include.NON_NULL)
  @Property("lastRestart")
  @UnmanagedApiExposedField
  protected Date _lastRestart;

  @JsonProperty("lastKmipMasterKeyRotation")
  @JsonInclude(Include.NON_NULL)
  @Property("lastKmipMasterKeyRotation")
  @UnmanagedApiExposedField(ignoreSuspiciousFieldNameError = true)
  protected Date _lastKmipMasterKeyRotation;

  @JsonProperty("kerberos")
  @JsonInclude(Include.NON_NULL)
  @Embedded("kerberos")
  @UnmanagedApiExposedField
  protected Kerberos _kerberos;

  @JsonProperty("repair")
  @JsonInclude(Include.NON_NULL)
  @Embedded("repair")
  @UnmanagedApiExposedField
  protected Map<String, RepairConfig> _repair;

  @JsonProperty(ENCRYPTION_PROVIDER_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Embedded(ENCRYPTION_PROVIDER_FIELD)
  @UnmanagedApiExposedField
  protected EncryptionProviderType _encryptionProviderType;

  @JsonProperty(ENCRYPTION_KEY_REGION_OVERRIDE_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Embedded(ENCRYPTION_KEY_REGION_OVERRIDE_FIELD)
  @UnmanagedApiExposedField(ignoreSuspiciousFieldNameError = true)
  protected String _encryptionKeyRegionOverride;

  @JsonProperty(KMIP_PROXY_PORT_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Embedded(KMIP_PROXY_PORT_FIELD)
  @UnmanagedApiExposedField
  protected Integer _kmipProxyPort;

  @JsonProperty(KMIP_PROXY_DISABLED_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Embedded(KMIP_PROXY_DISABLED_FIELD)
  @UnmanagedApiExposedField
  protected Boolean _kmipProxyDisabled;

  @JsonProperty(value = MANAGED_FIELD, access = JsonProperty.Access.READ_ONLY)
  @JsonView(JsonViews.WebUI.class)
  @Transient
  @UnmanagedApiExposedField
  protected boolean _managed = true;

  @JsonProperty(value = STATE_FIELD, access = JsonProperty.Access.READ_ONLY)
  @JsonView(JsonViews.WebUI.class)
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @Transient
  @UnmanagedApiIgnoredField
  protected ProcessMonitoringState _monitoringState;

  @JsonView(JsonViews.Agent.class)
  @JsonInclude(Include.NON_NULL)
  @JsonProperty(REALTIME_CONFIG)
  @Transient
  @UnmanagedApiExposedField
  protected RealtimeConfig _realtimeConfig;

  @JsonView(JsonViews.Agent.class)
  @JsonInclude(Include.NON_NULL)
  @JsonProperty(DATAEXPLORER_CONFIG)
  @Transient
  @UnmanagedApiExposedField
  protected DataExplorerConfig _dataExplorerConfig;

  @JsonProperty(DIRECT_ATTACH_VERIFICATION_KEY)
  @JsonInclude(Include.NON_NULL)
  @Property(DIRECT_ATTACH_VERIFICATION_KEY)
  @JsonSerialize(using = RedactOnUIJsonSerializer.class)
  @UnmanagedApiRedactedField
  protected String _directAttachVerificationKey;

  @JsonProperty(DIRECT_ATTACH_SOURCE_CLUSTER_KEY)
  @JsonInclude(Include.NON_NULL)
  @Property(DIRECT_ATTACH_SOURCE_CLUSTER_KEY)
  @UnmanagedApiExposedField
  protected String _directAttachSourceClusterName;

  @JsonProperty(DIRECT_ATTACH_PRE_WARM_GLOBS)
  @JsonInclude(Include.NON_NULL)
  @Property(DIRECT_ATTACH_PRE_WARM_GLOBS)
  @UnmanagedApiExposedField
  protected String _directAttachPreWarmGlobs;

  @JsonProperty(DIRECT_ATTACH_PRE_WARM_STRATEGY)
  @JsonInclude(Include.NON_NULL)
  @Property(DIRECT_ATTACH_PRE_WARM_STRATEGY)
  @UnmanagedApiExposedField
  protected DirectAttachPreWarmStrategy _directAttachPreWarmStrategy;

  @JsonProperty(DIRECT_ATTACH_PRE_WARM_CONCURRENCY)
  @JsonInclude(Include.NON_NULL)
  @Property(DIRECT_ATTACH_PRE_WARM_CONCURRENCY)
  @UnmanagedApiExposedField
  protected Integer _directAttachPreWarmConcurrency;

  @JsonProperty(DIRECT_ATTACH_PRE_WARM_BLOCK_SIZE)
  @JsonInclude(Include.NON_NULL)
  @Property(DIRECT_ATTACH_PRE_WARM_BLOCK_SIZE)
  @UnmanagedApiExposedField
  protected Integer _directAttachPreWarmBlockSize;

  @JsonProperty(DIRECT_ATTACH_PRE_WARM_BLOCK_READ_SIZE)
  @JsonInclude(Include.NON_NULL)
  @Property(DIRECT_ATTACH_PRE_WARM_BLOCK_READ_SIZE)
  @UnmanagedApiExposedField
  protected Integer _directAttachPreWarmBlockReadSize;

  @JsonProperty(DIRECT_ATTACH_REPL_WRITER_THREAD_COUNT)
  @JsonInclude(Include.NON_NULL)
  @Embedded(DIRECT_ATTACH_REPL_WRITER_THREAD_COUNT)
  @UnmanagedApiExposedField
  protected Integer _directAttachReplWriterThreadCount;

  @JsonProperty(DIRECT_ATTACH_PRE_WARM_SHOULD_USE_GO_BINARY)
  @JsonInclude(Include.NON_NULL)
  @Property(DIRECT_ATTACH_PRE_WARM_SHOULD_USE_GO_BINARY)
  @UnmanagedApiExposedField
  protected Boolean _directAttachPreWarmShouldUseGoBinary;

  @JsonProperty(DIRECT_ATTACH_PRE_WARM_SHOULD_WARM_EMPTY_BLOCKS)
  @JsonInclude(Include.NON_NULL)
  @Property(DIRECT_ATTACH_PRE_WARM_SHOULD_WARM_EMPTY_BLOCKS)
  @UnmanagedApiExposedField
  protected Boolean _directAttachPreWarmShouldWarmEmptyBlocks;

  @JsonProperty(DIRECT_ATTACH_PRE_WARM_SHOULD_SKIP_NON_PRIORITY)
  @JsonInclude(Include.NON_NULL)
  @Property(DIRECT_ATTACH_PRE_WARM_SHOULD_SKIP_NON_PRIORITY)
  @UnmanagedApiExposedField
  protected Boolean _directAttachPreWarmShouldSkipNonPriority;

  @JsonProperty(DIRECT_ATTACH_SHOULD_FILTER_BY_FILE_LISTS_KEY)
  @JsonInclude(Include.NON_NULL)
  @Property(DIRECT_ATTACH_SHOULD_FILTER_BY_FILE_LISTS_KEY)
  @UnmanagedApiExposedField
  protected Boolean _directAttachShouldFilterByFileLists;

  @JsonProperty(OPLOG_COLLECTION_FILE_NAME)
  @JsonInclude(Include.NON_NULL)
  @Property(OPLOG_COLLECTION_FILE_NAME)
  @UnmanagedApiExposedField
  protected String _oplogCollectionFileName;

  @JsonProperty(CREDENTIALS_VERSION_FIELD)
  @JsonView(JsonViews.Agent.class)
  @JsonInclude(Include.NON_NULL)
  @Property(CREDENTIALS_VERSION_FIELD)
  @UnmanagedApiExposedField(ignoreSuspiciousFieldNameError = true)
  protected Integer _credentialsVersion;

  @JsonProperty(LDAP_QUERY_PASSWORD_VERSION_FIELD)
  @JsonView(JsonViews.Agent.class)
  @JsonInclude(Include.NON_NULL)
  @Property(LDAP_QUERY_PASSWORD_VERSION_FIELD)
  @UnmanagedApiExposedField(ignoreSuspiciousFieldNameError = true)
  protected Integer _ldapQueryPasswordVersion;

  @JsonProperty(HORIZONS_FIELD)
  @JsonInclude(Include.NON_NULL)
  @UnmanagedApiExposedField
  protected Map<String, String> _horizons;

  @JsonProperty(DEFAULT_RW_CONCERN_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Embedded(DEFAULT_RW_CONCERN_FIELD)
  @UnmanagedApiExposedField
  protected DefaultRWConcern _defaultRWConcern;

  @JsonProperty(RESTART_INTERVAL_TIME_MS)
  @JsonInclude(Include.NON_NULL)
  @Embedded(RESTART_INTERVAL_TIME_MS)
  @UnmanagedApiExposedField
  protected Integer _restartIntervalTimeMS;

  @JsonProperty(UNSAFE_ROLLING_OPERATION_FIELD)
  @JsonInclude(Include.NON_NULL)
  @Property(UNSAFE_ROLLING_OPERATION_FIELD)
  @UnmanagedApiExposedField
  protected Boolean _unsafeRollingOperation;

  // The following variable will only be used to read the cluster wide configuration as part of
  // GetProcessConfigJobs. It will be rolled up into the deployment cluster wide configuration
  // when we import a process config.
  @JsonProperty(
      value = PROCESS_IMPORT_CLUSTER_WIDE_CONFIGURATION,
      access = Access.WRITE_ONLY) // property may only be written (set) for deserialization
  @JsonInclude(Include.NON_NULL)
  @Embedded(PROCESS_IMPORT_CLUSTER_WIDE_CONFIGURATION)
  @UnmanagedApiExposedField
  protected String _processImportClusterWideConfiguration;

  @JsonProperty(PROFILING_CONFIGURATION)
  @JsonInclude(Include.NON_NULL)
  @Embedded(PROFILING_CONFIGURATION)
  @UnmanagedApiExposedField
  protected ProfilingConfig _profilingConfig;

  @JsonProperty(REGIONAL_BASE_URL_FIELD)
  @JsonInclude(Include.NON_NULL)
  @JsonView(Agent.class)
  @Transient
  @UnmanagedApiExposedField
  protected String _regionalBaseUrl;

  @JsonProperty(REGIONAL_BASE_AGENT_URL_FIELD)
  @JsonInclude(Include.NON_NULL)
  @JsonView(Agent.class)
  @Transient
  @UnmanagedApiExposedField
  protected String _regionalBaseAgentUrl;

  @JsonProperty(REGIONAL_BASE_REALTIME_URL_FIELD)
  @JsonInclude(Include.NON_NULL)
  @JsonView(Agent.class)
  @Transient
  @UnmanagedApiExposedField
  protected String _regionalBaseRealtimeUrl;

  public Process() {
    // empty default constructor
  }

  // copy constructor
  public Process(final Process pCopy) {
    _name = pCopy.getName();
    _hostname = pCopy.getHostname();
    _alias = pCopy.getAlias();
    _temporaryPort = pCopy.getTemporaryPort();
    _version = pCopy.getVersion();
    _authSchemaVersion = pCopy.getAuthSchemaVersion();
    _featureCompatibilityVersion = pCopy.getFeatureCompatibilityVersion();
    _fullVersion = pCopy.getFullVersion();
    _processType = pCopy.getProcessType();
    _cluster = pCopy.getCluster();
    _disabled = pCopy._disabled;
    _manualMode = pCopy._manualMode;
    _logLevel = pCopy.getLogLevel();
    _numCores = pCopy.getNumCores();
    _cpuAffinity = pCopy.getCpuAffinity();
    _cpuSocketBinding = pCopy.getCpuSocketBinding();
    _logRotate = pCopy.getLogRotate();
    _lastResync = pCopy.getLastResync();
    _stepDownPrimaryForResync = pCopy.getStepDownPrimaryForResync();
    _lastThirdPartyRestoreResync = pCopy.getLastThirdPartyRestoreResync();
    _lastCompact = pCopy.getLastCompact();
    _lastRestart = pCopy.getLastRestart();
    _lastKmipMasterKeyRotation = pCopy.getLastKmipMasterKeyRotation();

    if (pCopy.getRepair() != null) {
      _repair = new HashMap<>();
      for (final Map.Entry<String, RepairConfig> config : pCopy.getRepair().entrySet()) {
        _repair.put(config.getKey(), new RepairConfig(config.getValue()));
      }
    }

    _encryptionProviderType = pCopy.getEncryptionProviderType();
    _encryptionKeyRegionOverride = pCopy.getEncryptionKeyRegionOverride();
    _kmipProxyPort = pCopy.getKmipProxyPort();
    _kmipProxyDisabled = pCopy.getKmipProxyDisabled();

    _managed = pCopy.isManaged();
    _monitoringState = pCopy.getMonitoringState();
    _kerberos = pCopy._kerberos == null ? null : new Kerberos(pCopy._kerberos);

    final ProcessArguments2_6 args = (ProcessArguments2_6) pCopy.getArgs();
    this.setArgs2_6((ProcessArguments2_6) SerializationUtils.clone(args));

    _realtimeConfig = pCopy.getRealtimeConfig();
    _dataExplorerConfig = pCopy.getDataExplorerConfig();

    _backupRestoreUrl = pCopy.getBackupRestoreUrl();
    _backupRestoreUrlV3 = pCopy.getBackupRestoreUrlV3();
    _backupParallelRestoreUrl = pCopy.getBackupRestoreUrl();
    _backupParallelRestoreNumChunks = pCopy.getBackupParallelRestoreNumChunks();
    _backupParallelRestoreNumWorkers = pCopy.getBackupParallelRestoreNumWorkers();
    _backupThirdPartyRestoreBaseUrl = pCopy.getBackupThirdPartyRestoreUrl();
    _backupRestoreIsS3DirectRestore = pCopy.getBackupRestoreIsS3DirectRestore();
    _backupRestoreS3DirectRestoreNumWorkers = pCopy.getBackupRestoreS3DirectRestoreNumWorkers();
    _backupRestoreRsVersion = pCopy.getBackupRestoreRsVersion();
    _backupRestoreElectionTerm = pCopy.getBackupRestoreElectionTerm();
    _backupRestoreCertificateValidationHostname =
        pCopy.getBackupRestoreCertificateValidationHostname();
    _backupRestoreCheckpointTime = pCopy.getBackupRestoreCheckpointTime();
    _backupRestoreFilterList = pCopy.getBackupRestoreFilterList();
    _backupRestoreFilteredFileListUrl = pCopy.getBackupRestoreFilteredFilelistUrl();
    _backupRestoreDesiredTime = pCopy.getBackupRestoreDesiredTime();
    _backupRestoreJobId = pCopy.getBackupRestoreJobId();
    _backupRestoreOplog = pCopy.getBackupRestoreOplog();
    _backupRestoreSourceRsId = pCopy.getBackupRestoreSourceRsId();
    _backupRestoreVerificationKey = pCopy.getBackupRestoreVerificationKey();
    _backupRestoreSourceGroupId = pCopy.getBackupRestoreSourceGroupId();
    _backupRestoreSourceGroupId = pCopy.getBackupPitRestoreType();
    _backupRestoreSystemRolesUUID = pCopy.getBackupRestoreSystemRolesUUID();
    _backupRestoreSystemUsersUUID = pCopy.getBackupRestoreSystemUsersUUID();
    _backupRestoreBalancerSettings = pCopy.getBackupRestoreBalancerSettings();
    _backupRestoreIsSuccessiveUpgrade = pCopy.getBackupRestoreIsSuccessiveUpgrade();
    _backupThirdPartyOplogStoreType = pCopy.getBackUpThirdPartyOplogStoreType();
    _backupRestoreConfigSettingsUUID = pCopy.getBackupRestoreConfigSettingsUUID();
    _directAttachVerificationKey = pCopy.getDirectAttachVerificationKey();
    _directAttachSourceClusterName = pCopy.getDirectAttachSourceClusterName();
    _directAttachShouldFilterByFileLists = pCopy.getDirectAttachShouldFilterByFilelists();
    _credentialsVersion = pCopy.getCredentialsVersion();
    _ldapQueryPasswordVersion = pCopy.getLdapQueryPasswordVersion();
    _defaultRWConcern = pCopy.getDefaultRWConcern();
    _restartIntervalTimeMS = pCopy.getRestartIntervalTimeMs();
    _unsafeRollingOperation = pCopy.getUnsafeRollingOperation();
    _processImportClusterWideConfiguration = pCopy.getClusterWideConfiguration();
    _profilingConfig = pCopy.getProfilingConfig();
  }

  public String getName() {
    return _name;
  }

  public void setName(final String pName) {
    _name = pName;
  }

  public String getHostname() {
    return _hostname;
  }

  public void setHostname(final String pHostname) {
    _hostname = pHostname;
  }

  public String getHostnameAndPort() {
    try {
      return BaseHostUtils.assembleHostnameAndPort(getHostname(), getPort());
    } catch (final IllegalArgumentException e) {
      // hostname or port must be null
      return String.format("%s:%d", getHostname(), getPort());
    }
  }

  public String getBackupRestoreUrl() {
    return _backupRestoreUrl;
  }

  public void setBackupRestoreUrl(final String pBackupRestoreUrl) {
    _backupRestoreUrl = pBackupRestoreUrl;
  }

  public String getBackupRestoreUrlV3() {
    return _backupRestoreUrlV3;
  }

  public void setBackupRestoreUrlV3(final String pBackupRestoreUrl) {
    _backupRestoreUrlV3 = pBackupRestoreUrl;
  }

  public String getBackupParallelRestoreUrl() {
    return _backupParallelRestoreUrl;
  }

  public void setBackupParallelRestoreUrl(final String pBackupParallelRestoreUrl) {
    _backupParallelRestoreUrl = pBackupParallelRestoreUrl;
  }

  public Integer getBackupParallelRestoreNumChunks() {
    return _backupParallelRestoreNumChunks;
  }

  public void setBackupParallelRestoreNumChunks(final Integer pBackupParallelRestoreNumChunks) {
    _backupParallelRestoreNumChunks = pBackupParallelRestoreNumChunks;
  }

  public Integer getBackupParallelRestoreNumWorkers() {
    return _backupParallelRestoreNumWorkers;
  }

  public void setBackupParallelRestoreNumWorkers(final Integer pBackupParallelRestoreNumWorkers) {
    _backupParallelRestoreNumWorkers = pBackupParallelRestoreNumWorkers;
  }

  public String getBackupThirdPartyRestoreUrl() {
    return _backupThirdPartyRestoreBaseUrl;
  }

  public String getBackupThirdPartyRestoreBaseUrl() {
    return _backupThirdPartyRestoreBaseUrl;
  }

  public void setBackupThirdPartyRestoreBaseUrl(final String pBackupThirdPartyRestoreBaseUrl) {
    _backupThirdPartyRestoreBaseUrl = pBackupThirdPartyRestoreBaseUrl;
  }

  public String getBackupRestoreOplogBaseUrl() {
    return _backupRestoreOplogBaseUrl;
  }

  public void setBackUpThirdPartyOplogStoreType(final String pBackupThirdPartyOplogStoreType) {
    _backupThirdPartyOplogStoreType = pBackupThirdPartyOplogStoreType;
  }

  public String getBackUpThirdPartyOplogStoreType() {
    return _backupThirdPartyOplogStoreType;
  }

  public void setBackupRestoreOplogBaseUrl(final String pUrl) {
    _backupRestoreOplogBaseUrl = pUrl;
  }

  public Integer getBackupRestoreRsVersion() {
    return _backupRestoreRsVersion;
  }

  public void setBackupRestoreRsVersion(final Integer pBackupRestoreRsVersion) {
    _backupRestoreRsVersion = pBackupRestoreRsVersion;
  }

  public Long getBackupRestoreElectionTerm() {
    return _backupRestoreElectionTerm;
  }

  public void setBackupRestoreElectionTerm(final Long pBackupRestoreElectionTerm) {
    _backupRestoreElectionTerm = pBackupRestoreElectionTerm;
  }

  public BSONTimestamp getBackupRestoreCheckpointTime() {
    return _backupRestoreCheckpointTime;
  }

  public void setBackupRestoreCheckpointTime(final BSONTimestamp backupRestoreCheckpointTime) {
    _backupRestoreCheckpointTime = backupRestoreCheckpointTime;
  }

  public String getBackupRestoreCertificateValidationHostname() {
    return _backupRestoreCertificateValidationHostname;
  }

  public void setBackupRestoreCertificateValidationHostname(
      final String pBackupRestoreCertificateValidationHostname) {
    _backupRestoreCertificateValidationHostname = pBackupRestoreCertificateValidationHostname;
  }

  public BackupRestoreOplog getBackupRestoreOplog() {
    return _backupRestoreOplog;
  }

  public void setBackupRestoreOplog(final BackupRestoreOplog pBackupRestoreOplog) {
    _backupRestoreOplog = pBackupRestoreOplog;
  }

  public BSONTimestamp getBackupRestoreDesiredTime() {
    return _backupRestoreDesiredTime;
  }

  public void setBackupRestoreDesiredTime(final BSONTimestamp pBackupRestoreDesiredTime) {
    _backupRestoreDesiredTime = pBackupRestoreDesiredTime;
  }

  public String getBackupRestoreSourceRsId() {
    return _backupRestoreSourceRsId;
  }

  public void setBackupRestoreSourceRsId(final String pBackupRestoreSourceRsId) {
    _backupRestoreSourceRsId = pBackupRestoreSourceRsId;
  }

  public BackupRestoreFilterList getBackupRestoreFilterList() {
    return _backupRestoreFilterList;
  }

  public void setBackupRestoreFilterList(final BackupRestoreFilterList backupRestoreFilterList) {
    _backupRestoreFilterList = backupRestoreFilterList;
  }

  public String getBackupRestoreFilteredFilelistUrl() {
    return _backupRestoreFilteredFileListUrl;
  }

  public void setBackupRestoreFilteredFileListUrl(final String pFilteredFileListUrl) {
    _backupRestoreFilteredFileListUrl = pFilteredFileListUrl;
  }

  public String getBackupRestoreJobId() {
    return _backupRestoreJobId;
  }

  public void setBackupRestoreJobId(final String pBackupRestoreJobId) {
    _backupRestoreJobId = pBackupRestoreJobId;
  }

  public String getBackupRestoreVerificationKey() {
    return _backupRestoreVerificationKey;
  }

  public void setBackupRestoreVerificationKey(final String pBackupRestoreVerificationKey) {
    _backupRestoreVerificationKey = pBackupRestoreVerificationKey;
  }

  public String getBackupRestoreSourceGroupId() {
    return _backupRestoreSourceGroupId;
  }

  public void setBackupRestoreSourceGroupId(final String pBackupRestoreSourceGroupId) {
    _backupRestoreSourceGroupId = pBackupRestoreSourceGroupId;
  }

  public String getBackupRestoreSystemUsersUUID() {
    return _backupRestoreSystemUsersUUID;
  }

  public void setBackupRestoreSystemUsersUUID(final String pBackupRestoreSystemUsersUUID) {
    _backupRestoreSystemUsersUUID = pBackupRestoreSystemUsersUUID;
  }

  public String getBackupRestoreSystemRolesUUID() {
    return _backupRestoreSystemRolesUUID;
  }

  public void setBackupRestoreSystemRolesUUID(final String pBackupRestoreSystemRolesUUID) {
    _backupRestoreSystemRolesUUID = pBackupRestoreSystemRolesUUID;
  }

  public String getAlias() {
    return _alias;
  }

  public void setAlias(final String pAlias) {
    _alias = pAlias;
  }

  public Integer getTemporaryPort() {
    return _temporaryPort;
  }

  public void setTemporaryPort(final Integer pTemporaryPort) {
    _temporaryPort = pTemporaryPort;
  }

  public String getVersion() {
    return _version;
  }

  public void setVersion(final String pVersion) {
    _version = pVersion;
  }

  public Integer getAuthSchemaVersion() {
    return _authSchemaVersion;
  }

  public void setAuthSchemaVersion(final Integer pAuthSchemaVersion) {
    _authSchemaVersion = pAuthSchemaVersion;
  }

  public String getFeatureCompatibilityVersion() {
    return _featureCompatibilityVersion;
  }

  public void setFeatureCompatibilityVersion(final String pFeatureCompatibilityVersion) {
    _featureCompatibilityVersion = pFeatureCompatibilityVersion;
  }

  public MongoDbBuild getFullVersion() {
    return _fullVersion;
  }

  public void setFullVersion(final MongoDbBuild pFullVersion) {
    _fullVersion = pFullVersion;
  }

  public ProcessType getProcessType() {
    return _processType;
  }

  public void setProcessType(final ProcessType pProcessType) {
    _processType = pProcessType;
  }

  public String getCluster() {
    return _cluster;
  }

  public void setCluster(final String pClusterName) {
    _cluster = pClusterName;
  }

  public boolean isDisabled() {
    return _disabled != null && _disabled;
  }

  public Boolean isDisabledForMapstruct() {
    return _disabled;
  }

  public void setDisabled(final Boolean pDisabled) {
    _disabled = pDisabled;
  }

  public boolean isManualMode() {
    return _manualMode != null && _manualMode;
  }

  public Boolean isManualModeForMapstruct() {
    return _manualMode;
  }

  public void setManualMode(final Boolean pManualMode) {
    _manualMode = pManualMode;
  }

  public Integer getLogLevel() {
    return _logLevel;
  }

  public void setLogLevel(final Integer pLogLevel) {
    _logLevel = pLogLevel;
  }

  public Integer getNumCores() {
    return _numCores;
  }

  public void setNumCores(final Integer pNumCores) {
    _numCores = pNumCores;
  }

  public List<Integer> getCpuAffinity() {
    return _cpuAffinity;
  }

  public void setCpuAffinity(final List<Integer> pCpuAffinity) {
    _cpuAffinity = pCpuAffinity;
  }

  public List<Integer> getCpuSocketBinding() {
    return _cpuSocketBinding;
  }

  public void setCpuSocketBinding(final List<Integer> pCpuSocketBinding) {
    _cpuSocketBinding = pCpuSocketBinding;
  }

  public LogRotate getLogRotate() {
    return _logRotate;
  }

  public void setLogRotate(final LogRotate pLogRotate) {
    _logRotate = pLogRotate;
  }

  public LogRotate getAuditLogRotate() {
    return _auditLogRotate;
  }

  public void setAuditLogRotate(final LogRotate pLogRotate) {
    _auditLogRotate = pLogRotate;
  }

  public Date getLastResync() {
    return _lastResync;
  }

  public Date getLastThirdPartyRestoreResync() {
    return _lastThirdPartyRestoreResync;
  }

  public void setLastResync(final Date pLastResync) {
    _lastResync = pLastResync;
  }

  public void setLastThirdPartyRestoreResync(final Date pLastThirdPartyRestoreResync) {
    _lastThirdPartyRestoreResync = pLastThirdPartyRestoreResync;
  }

  public Boolean getStepDownPrimaryForResync() {
    return _stepDownPrimaryForResync;
  }

  public void setStepDownPrimaryForResync(final Boolean pStepDownPrimaryForResync) {
    _stepDownPrimaryForResync = pStepDownPrimaryForResync;
  }

  public Date getLastCompact() {
    return _lastCompact;
  }

  public void setLastCompact(final Date pLastCompact) {
    _lastCompact = pLastCompact;
  }

  public Date getLastRestart() {
    return _lastRestart;
  }

  public void setLastRestart(final Date pLastRestart) {
    _lastRestart = pLastRestart;
  }

  public Date getLastKmipMasterKeyRotation() {
    return _lastKmipMasterKeyRotation;
  }

  public void setLastKmipMasterKeyRotation(final Date pLastKmipMasterKeyRotation) {
    _lastKmipMasterKeyRotation = pLastKmipMasterKeyRotation;
  }

  public Kerberos getKerberos() {
    return _kerberos;
  }

  public void setKerberos(final Kerberos pKerberos) {
    _kerberos = pKerberos;
  }

  public Map<String, RepairConfig> getRepair() {
    return _repair;
  }

  public void setRepair(final Map<String, RepairConfig> pRepair) {
    _repair = pRepair;
  }

  public EncryptionProviderType getEncryptionProviderType() {
    return _encryptionProviderType;
  }

  public void setEncryptionProviderType(final EncryptionProviderType pType) {
    _encryptionProviderType = pType;
  }

  public String getEncryptionKeyRegionOverride() {
    return _encryptionKeyRegionOverride;
  }

  public void setEncryptionKeyRegionOverride(final String pRegion) {
    _encryptionKeyRegionOverride = pRegion;
  }

  public Integer getKmipProxyPort() {
    return _kmipProxyPort;
  }

  public void setKmipProxyPort(final Integer pPort) {
    _kmipProxyPort = pPort;
  }

  public Boolean getKmipProxyDisabled() {
    return _kmipProxyDisabled;
  }

  public void setKmipProxyDisabled(final Boolean pDisabled) {
    _kmipProxyDisabled = pDisabled;
  }

  public String getBackupPitRestoreType() {
    return _backupPitRestoreType;
  }

  public void setBackupPitRestoreType(final String pBackupPitRestoreType) {
    _backupPitRestoreType = pBackupPitRestoreType;
  }

  public void setBackupShardIdRestoreMaps(
      final List<BackupRestoreShardIdRestoreMap> pBackupBackupRestoreShardIdRestoreMaps) {
    _backupBackupRestoreShardIdRestoreMaps = pBackupBackupRestoreShardIdRestoreMaps;
  }

  public Boolean getBackupRestoreIsConfigShard() {
    return _backupRestoreIsConfigShard;
  }

  public void setBackupRestoreIsConfigShard(final Boolean backupRestoreIsConfigShard) {
    _backupRestoreIsConfigShard = backupRestoreIsConfigShard;
  }

  public Boolean getBackupRestoreIsS3DirectRestore() {
    return _backupRestoreIsS3DirectRestore;
  }

  public void setBackupRestoreIsS3DirectRestore(final Boolean backupRestoreIsS3DirectRestore) {
    _backupRestoreIsS3DirectRestore = backupRestoreIsS3DirectRestore;
  }

  public Integer getBackupRestoreS3DirectRestoreNumWorkers() {
    return _backupRestoreS3DirectRestoreNumWorkers;
  }

  public void setBackupRestoreS3DirectRestoreNumWorkers(
      final Integer backupRestoreS3DirectRestoreNumWorkers) {
    _backupRestoreS3DirectRestoreNumWorkers = backupRestoreS3DirectRestoreNumWorkers;
  }

  public void setBackupRestoreBalancerSettings(
      final BalancerSettings backupRestoreBalancerSettings) {
    _backupRestoreBalancerSettings = backupRestoreBalancerSettings;
  }

  public BalancerSettings getBackupRestoreBalancerSettings() {
    return _backupRestoreBalancerSettings;
  }

  public String getBackupRestoreConfigSettingsUUID() {
    return _backupRestoreConfigSettingsUUID;
  }

  public Boolean getBackupRestoreIsSuccessiveUpgrade() {
    return _backupRestoreIsSuccessiveUpgrade;
  }

  public void setBackupRestoreIsSuccessiveUpgrade(final Boolean backupRestoreIsSuccessiveUpgrade) {
    _backupRestoreIsSuccessiveUpgrade = backupRestoreIsSuccessiveUpgrade;
  }

  public void setBackupRestoreConfigSettingsUUID(final String backupRestoreConfigSettingsUUID) {
    _backupRestoreConfigSettingsUUID = backupRestoreConfigSettingsUUID;
  }

  public List<BackupRestoreShardIdRestoreMap> getBackupShardIdRestoreMaps() {
    return _backupBackupRestoreShardIdRestoreMaps;
  }

  public String getDirectAttachVerificationKey() {
    return _directAttachVerificationKey;
  }

  public void setDirectAttachVerificationKey(final String pDirectAttachVerificationKey) {
    _directAttachVerificationKey = pDirectAttachVerificationKey;
  }

  public Map<String, String> getHorizons() {
    return _horizons;
  }

  public void setHorizons(Map<String, String> _horizons) {
    this._horizons = _horizons;
  }

  public Integer getLdapQueryPasswordVersion() {
    return _ldapQueryPasswordVersion;
  }

  public Integer getCredentialsVersion() {
    return _credentialsVersion;
  }

  public void setCredentialsVersion(final Integer pCredentialsVersion) {
    _credentialsVersion = pCredentialsVersion;
  }

  public void setLdapQueryPasswordVersion(final Integer pLdapQueryPasswordVersion) {
    _ldapQueryPasswordVersion = pLdapQueryPasswordVersion;
  }

  public String getDirectAttachSourceClusterName() {
    return _directAttachSourceClusterName;
  }

  public void setDirectAttachSourceClusterName(final String pDirectAttachSourceClusterName) {
    _directAttachSourceClusterName = pDirectAttachSourceClusterName;
  }

  public String getDirectAttachPreWarmGlobs() {
    return _directAttachPreWarmGlobs;
  }

  public void setDirectAttachPreWarmGlobs(final String pDirectAttachPreWarmGlobs) {
    _directAttachPreWarmGlobs = pDirectAttachPreWarmGlobs;
  }

  public DirectAttachPreWarmStrategy getDirectAttachPreWarmStrategy() {
    return _directAttachPreWarmStrategy;
  }

  public void setDirectAttachPreWarmStrategy(
      final DirectAttachPreWarmStrategy pDirectAttachPreWarmStrategy) {
    _directAttachPreWarmStrategy = pDirectAttachPreWarmStrategy;
  }

  public Integer getDirectAttachPreWarmConcurrency() {
    return _directAttachPreWarmConcurrency;
  }

  public void setDirectAttachPreWarmConcurrency(final Integer pDirectAttachPreWarmConcurrency) {
    _directAttachPreWarmConcurrency = pDirectAttachPreWarmConcurrency;
  }

  public Integer getDirectAttachPreWarmBlockSize() {
    return _directAttachPreWarmBlockSize;
  }

  public void setDirectAttachPreWarmBlockSize(final Integer pDirectAttachPreWarmBlockSize) {
    _directAttachPreWarmBlockSize = pDirectAttachPreWarmBlockSize;
  }

  public Integer getDirectAttachPreWarmBlockReadSize() {
    return _directAttachPreWarmBlockReadSize;
  }

  public void setDirectAttachPreWarmBlockReadSize(final Integer pDirectAttachPreWarmBlockReadSize) {
    _directAttachPreWarmBlockReadSize = pDirectAttachPreWarmBlockReadSize;
  }

  public Integer getDirectAttachReplWriterThreadCount() {
    return _directAttachReplWriterThreadCount;
  }

  public void setDirectAttachReplWriterThreadCount(
      final Integer directAttachReplWriterThreadCount) {
    _directAttachReplWriterThreadCount = directAttachReplWriterThreadCount;
  }

  public Boolean getDirectAttachPreWarmShouldUseGoBinary() {
    return _directAttachPreWarmShouldUseGoBinary;
  }

  public void setDirectAttachPreWarmShouldUseGoBinary(
      final Boolean pDirectAttachPreWarmShouldUseGoBinary) {
    _directAttachPreWarmShouldUseGoBinary = pDirectAttachPreWarmShouldUseGoBinary;
  }

  public Boolean getDirectAttachPreWarmShouldWarmEmptyBlocks() {
    return _directAttachPreWarmShouldWarmEmptyBlocks;
  }

  public void setDirectAttachPreWarmShouldWarmEmptyBlocks(
      final Boolean pDirectAttachPreWarmShouldWarmEmptyBlocks) {
    _directAttachPreWarmShouldWarmEmptyBlocks = pDirectAttachPreWarmShouldWarmEmptyBlocks;
  }

  public Boolean getDirectAttachPreWarmShouldSkipNonPriority() {
    return _directAttachPreWarmShouldSkipNonPriority;
  }

  public void setDirectAttachPreWarmShouldSkipNonPriority(
      final Boolean pDirectAttachPreWarmShouldSkipNonPriority) {
    _directAttachPreWarmShouldSkipNonPriority = pDirectAttachPreWarmShouldSkipNonPriority;
  }

  public Boolean getDirectAttachShouldFilterByFilelists() {
    return _directAttachShouldFilterByFileLists;
  }

  public void setDirectAttachFilterByFilelists(final Boolean pDirectAttachFilterByFileLists) {
    _directAttachShouldFilterByFileLists = pDirectAttachFilterByFileLists;
  }

  public String getOplogCollectionFileName() {
    return _oplogCollectionFileName;
  }

  public void setOplogCollectionFileName(final String pOplogCollectionFileName) {
    _oplogCollectionFileName = pOplogCollectionFileName;
  }

  public DefaultRWConcern getDefaultRWConcern() {
    return _defaultRWConcern;
  }

  public void setDefaultRWConcern(final DefaultRWConcern pDefaultRWConcern) {
    _defaultRWConcern = pDefaultRWConcern;
  }

  public Integer getRestartIntervalTimeMs() {
    return _restartIntervalTimeMS;
  }

  public void setRestartIntervalTimeMs(final Integer pRestartIntervalTimeMS) {
    _restartIntervalTimeMS = pRestartIntervalTimeMS;
  }

  public Boolean getUnsafeRollingOperation() {
    return _unsafeRollingOperation;
  }

  public void setUnsafeRollingOperation(final Boolean pUnsafeRollingOperation) {
    _unsafeRollingOperation = pUnsafeRollingOperation;
  }

  public String getClusterWideConfiguration() {
    return _processImportClusterWideConfiguration;
  }

  public void setClusterWideConfiguration(final String clusterWideConfiguration) {
    _processImportClusterWideConfiguration = clusterWideConfiguration;
  }

  public ProfilingConfig getProfilingConfig() {
    return _profilingConfig;
  }

  public void setProfilingConfig(final ProfilingConfig pProfilingConfig) {
    _profilingConfig = pProfilingConfig;
  }

  public void removeProfilingConfig() {
    _profilingConfig = null;
  }

  @Override
  public String toString() {
    return String.format("host=%s", getHostnameAndPort());
  }

  public boolean isMongod() {
    return ProcessType.MONGOD == getProcessType();
  }

  public boolean isMongos() {
    return ProcessType.MONGOS == getProcessType();
  }

  public boolean isReplicaSetMember() {
    final ProcessArguments args = getArgs();
    return args != null && args.getReplSetName() != null;
  }

  public String getReplSetName() {
    final ProcessArguments args = getArgs();
    return args != null ? args.getReplSetName() : null;
  }

  public boolean isConfigSvr() {
    final ProcessArguments args = getArgs();
    return args != null && args.isConfigSvr();
  }

  public boolean isStandalone() {
    return isMongod() && !isReplicaSetMember() && !isConfigSvr();
  }

  public boolean isRunningOnWindows() {
    final ProcessArguments args = getArgs();
    return args != null
        && (PathUtils.isWindowsAbsolutePath(args.getDbPath())
            || PathUtils.isWindowsAbsolutePath(args.getLogPath()));
  }

  public boolean isSslEnabled() {
    final ProcessArguments args = getArgs();
    if (args == null) {
      return false;
    }

    return (args.getTlsMode() != null && !args.getTlsMode().equals(TlsMode.DISABLED))
        || (args.getTlsOnNormalPorts() != null && args.getTlsOnNormalPorts());
  }

  public boolean isManaged() {
    return _managed;
  }

  public void setManaged(final boolean pManaged) {
    _managed = pManaged;
  }

  @Nullable
  public ProcessMonitoringState getMonitoringState() {
    return _monitoringState;
  }

  public void setMonitoringState(final ProcessMonitoringState pState) {
    _monitoringState = pState;
  }

  public RealtimeConfig getRealtimeConfig() {
    return _realtimeConfig;
  }

  public void setRealtimeConfig(final RealtimeConfig pRealtimeConfig) {
    _realtimeConfig = pRealtimeConfig;
  }

  public DataExplorerConfig getDataExplorerConfig() {
    return _dataExplorerConfig;
  }

  public void setDataExplorerConfig(final DataExplorerConfig pDataExplorerConfig) {
    _dataExplorerConfig = pDataExplorerConfig;
  }

  public String getRegionalBaseUrl() {
    return _regionalBaseUrl;
  }

  public void setRegionalBaseUrl(final String pRegionalBaseUrl) {
    _regionalBaseUrl = pRegionalBaseUrl;
  }

  public String getRegionalBaseAgentUrl() {
    return _regionalBaseAgentUrl;
  }

  public void setRegionalBaseAgentUrl(final String pRegionalBaseAgentUrl) {
    _regionalBaseAgentUrl = pRegionalBaseAgentUrl;
  }

  public String getRegionalBaseRealtimeUrl() {
    return _regionalBaseRealtimeUrl;
  }

  public void setRegionalBaseRealtimeUrl(final String pRegionalBaseRealtimeUrl) {
    _regionalBaseRealtimeUrl = pRegionalBaseRealtimeUrl;
  }

  public Integer getPort() {
    return getArgs().getPort();
  }

  public List<String> getMaxIncomingConnectionsOverride() {
    return getArgs().getMaxIncomingConnectionsOverride();
  }

  protected HashCodeBuilder getHashCodeBuilder() {
    return new HashCodeBuilder()
        .append(getName())
        .append(getHostname())
        .append(getAlias())
        .append(getTemporaryPort())
        .append(getVersion())
        .append(getAuthSchemaVersion())
        .append(getFeatureCompatibilityVersion())
        .append(getProcessType())
        .append(getCluster())
        .append(getLogLevel())
        .append(getNumCores())
        .append(getCpuAffinity())
        .append(getCpuSocketBinding())
        .append(isDisabled())
        .append(isManualMode())
        .append(getLastResync())
        .append(getStepDownPrimaryForResync())
        .append(getLastThirdPartyRestoreResync())
        .append(getLastCompact())
        .append(getLastRestart())
        .append(getLastKmipMasterKeyRotation())
        .append(getRepair())
        .append(getKerberos())
        .append(getBackupRestoreDesiredTime())
        .append(getBackupRestoreFilterList())
        .append(getBackupRestoreFilteredFilelistUrl())
        .append(getBackupRestoreJobId())
        .append(getBackupRestoreOplog())
        .append(getBackupRestoreSourceGroupId())
        .append(getBackupRestoreUrl())
        .append(getBackupRestoreRsVersion())
        .append(getBackupRestoreElectionTerm())
        .append(getBackupRestoreCheckpointTime())
        .append(getBackupRestoreCertificateValidationHostname())
        .append(getBackupRestoreSourceRsId())
        .append(getBackupRestoreVerificationKey())
        .append(getBackupRestoreSystemUsersUUID())
        .append(getBackupRestoreSystemRolesUUID())
        .append(getBackupPitRestoreType())
        .append(getEncryptionProviderType())
        .append(getKmipProxyPort())
        .append(getKmipProxyDisabled())
        .append(getArgs())
        .append(getDirectAttachVerificationKey())
        .append(getDirectAttachSourceClusterName())
        .append(getDirectAttachShouldFilterByFilelists())
        .append(getCredentialsVersion())
        .append(getLdapQueryPasswordVersion())
        .append(getDefaultRWConcern())
        .append(getRestartIntervalTimeMs())
        .append(getUnsafeRollingOperation())
        .append(getBackupShardIdRestoreMaps());
  }

  @Override
  public int hashCode() {
    return getHashCodeBuilder().toHashCode();
  }

  protected EqualsBuilder getEqualsBuilder(final Object obj) {
    final Process process = (Process) obj;
    return new EqualsBuilder()
        .append(getName(), process.getName())
        .append(getHostname(), process.getHostname())
        .append(getAlias(), process.getAlias())
        .append(getTemporaryPort(), process.getTemporaryPort())
        .append(getVersion(), process.getVersion())
        .append(getAuthSchemaVersion(), process.getAuthSchemaVersion())
        .append(getFeatureCompatibilityVersion(), process.getFeatureCompatibilityVersion())
        .append(getProcessType(), process.getProcessType())
        .append(getCluster(), process.getCluster())
        .append(getLogLevel(), process.getLogLevel())
        .append(getNumCores(), process.getNumCores())
        .append(getCpuAffinity(), process.getCpuAffinity())
        .append(getCpuSocketBinding(), process.getCpuSocketBinding())
        .append(isDisabled(), process.isDisabled())
        .append(isManualMode(), process.isManualMode())
        .append(getLastResync(), process.getLastResync())
        .append(getStepDownPrimaryForResync(), process.getStepDownPrimaryForResync())
        .append(getLastThirdPartyRestoreResync(), process.getLastThirdPartyRestoreResync())
        .append(getLastCompact(), process.getLastCompact())
        .append(getLastRestart(), process.getLastRestart())
        .append(getLastKmipMasterKeyRotation(), process.getLastKmipMasterKeyRotation())
        .append(getRepair(), process.getRepair())
        .append(getKerberos(), process.getKerberos())
        .append(getBackupRestoreDesiredTime(), process.getBackupRestoreDesiredTime())
        .append(getBackupRestoreFilterList(), process.getBackupRestoreFilterList())
        .append(
            getBackupRestoreFilteredFilelistUrl(), process.getBackupRestoreFilteredFilelistUrl())
        .append(getBackupRestoreJobId(), process.getBackupRestoreJobId())
        .append(getBackupRestoreOplog(), process.getBackupRestoreOplog())
        .append(getBackupRestoreSourceGroupId(), process.getBackupRestoreSourceGroupId())
        .append(getBackupRestoreUrl(), process.getBackupRestoreUrl())
        .append(getBackupRestoreUrlV3(), process.getBackupRestoreUrlV3())
        .append(getBackupParallelRestoreUrl(), process.getBackupParallelRestoreUrl())
        .append(getBackupRestoreRsVersion(), process.getBackupRestoreRsVersion())
        .append(getBackupRestoreElectionTerm(), process.getBackupRestoreElectionTerm())
        .append(
            getBackupRestoreCertificateValidationHostname(),
            process.getBackupRestoreCertificateValidationHostname())
        .append(getBackupRestoreSourceRsId(), process.getBackupRestoreSourceRsId())
        .append(getBackupRestoreCheckpointTime(), process.getBackupRestoreCheckpointTime())
        .append(getBackupRestoreVerificationKey(), process.getBackupRestoreVerificationKey())
        .append(getBackupRestoreSystemUsersUUID(), process.getBackupRestoreSystemUsersUUID())
        .append(getBackupRestoreSystemRolesUUID(), process.getBackupRestoreSystemRolesUUID())
        .append(getBackupRestoreBalancerSettings(), process.getBackupRestoreBalancerSettings())
        .append(getBackupRestoreConfigSettingsUUID(), process.getBackupRestoreConfigSettingsUUID())
        .append(getBackupPitRestoreType(), process.getBackupPitRestoreType())
        .append(getEncryptionProviderType(), process.getEncryptionProviderType())
        .append(getKmipProxyPort(), process.getKmipProxyPort())
        .append(getKmipProxyDisabled(), process.getKmipProxyDisabled())
        .append(getArgs(), process.getArgs())
        .append(getDirectAttachVerificationKey(), process.getDirectAttachVerificationKey())
        .append(getDirectAttachSourceClusterName(), process.getDirectAttachSourceClusterName())
        .append(
            getDirectAttachShouldFilterByFilelists(),
            process.getDirectAttachShouldFilterByFilelists())
        .append(getCredentialsVersion(), process.getCredentialsVersion())
        .append(getLdapQueryPasswordVersion(), process.getLdapQueryPasswordVersion())
        .append(getDefaultRWConcern(), process.getDefaultRWConcern())
        .append(getRestartIntervalTimeMs(), process.getRestartIntervalTimeMs())
        .append(getUnsafeRollingOperation(), process.getUnsafeRollingOperation())
        .append(getProfilingConfig(), process.getProfilingConfig())
        .append(getBackupShardIdRestoreMaps(), process.getBackupShardIdRestoreMaps());
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == null || obj.getClass() != getClass()) {
      return false;
    }
    if (obj == this) {
      return true;
    }
    return getEqualsBuilder(obj).isEquals();
  }

  @Embedded
  public static class BackupRestoreOplog {
    @JsonProperty("ts")
    @Embedded("ts")
    @UnmanagedApiExposedField
    private BSONTimestamp _ts;

    @JsonProperty("t")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Property("t")
    @UnmanagedApiExposedField
    private Long _t;

    @JsonProperty("h")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Property("h")
    @UnmanagedApiExposedField
    private Long _h;

    public BSONTimestamp getTs() {
      return _ts;
    }

    public void setTs(final BSONTimestamp pTs) {
      _ts = pTs;
    }

    public Long getT() {
      return _t;
    }

    public void setT(final Long pT) {
      _t = pT;
    }

    public Long getH() {
      return _h;
    }

    public void setH(final Long pH) {
      _h = pH;
    }

    protected HashCodeBuilder getHashCodeBuilder() {
      return new HashCodeBuilder().append(_ts).append(_t).append(_h);
    }

    @Override
    public int hashCode() {
      return getHashCodeBuilder().toHashCode();
    }

    protected EqualsBuilder getEqualsBuilder(final Object obj) {
      final BackupRestoreOplog oplog = (BackupRestoreOplog) obj;
      return new EqualsBuilder()
          .append(_ts, oplog.getTs())
          .append(_t, oplog.getT())
          .append(_h, oplog.getH());
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == null || obj.getClass() != getClass()) {
        return false;
      }
      if (obj == this) {
        return true;
      }
      return getEqualsBuilder(obj).isEquals();
    }
  }

  @Embedded
  public static class BackupRestoreFilterList {
    @JsonProperty("type")
    @Property("type")
    @UnmanagedApiExposedField
    private String _type;

    @JsonProperty("list")
    @Property("list")
    @UnmanagedApiExposedField
    private Set<String> _list;

    public String getType() {
      return _type;
    }

    public void setType(final String type) {
      _type = type;
    }

    public Set<String> getList() {
      return _list;
    }

    public void setList(final Set<String> list) {
      _list = list;
    }

    protected HashCodeBuilder getHashCodeBuilder() {
      return new HashCodeBuilder().append(_type).append(_list);
    }

    @Override
    public int hashCode() {
      return getHashCodeBuilder().toHashCode();
    }

    protected EqualsBuilder getEqualsBuilder(final Object obj) {
      final BackupRestoreFilterList filterList = (BackupRestoreFilterList) obj;
      return new EqualsBuilder()
          .append(_type, filterList.getType())
          .append(_list, filterList.getList());
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == null || obj.getClass() != getClass()) {
        return false;
      }
      if (obj == this) {
        return true;
      }
      return getEqualsBuilder(obj).isEquals();
    }
  }

  @Embedded
  public static class Kerberos {

    @JsonProperty("keytab")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Property("keytab")
    @UnmanagedApiExposedField(ignoreSuspiciousFieldNameError = true)
    private String _keytab;

    public Kerberos() {}

    public Kerberos(final Kerberos pCopy) {
      _keytab = pCopy.getKeytab();
    }

    public String getKeytab() {
      return _keytab;
    }

    public void setKeytab(final String pKeytab) {
      _keytab = pKeytab;
    }

    protected HashCodeBuilder getHashCodeBuilder() {
      return new HashCodeBuilder().append(_keytab);
    }

    @Override
    public int hashCode() {
      return getHashCodeBuilder().toHashCode();
    }

    protected EqualsBuilder getEqualsBuilder(final Object obj) {
      final Kerberos kerberos = (Kerberos) obj;
      return new EqualsBuilder().append(_keytab, kerberos._keytab);
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == null || obj.getClass() != getClass()) {
        return false;
      }
      if (obj == this) {
        return true;
      }
      return getEqualsBuilder(obj).isEquals();
    }
  }

  @Embedded
  public static class RepairConfig {
    @JsonProperty("preserveClonedFilesOnFailure")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Property("preserveClonedFilesOnFailure")
    @UnmanagedApiExposedField
    private Boolean _preserveClonedFilesOnFailure;

    @JsonProperty("backupOriginalFiles")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Property("backupOriginalFiles")
    @UnmanagedApiExposedField
    private Boolean _backupOriginalFiles;

    @JsonProperty("lastRepair")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Property("lastRepair")
    @UnmanagedApiExposedField
    private Date _lastRepair;

    public RepairConfig() {}

    public RepairConfig(final RepairConfig pCopy) {
      _preserveClonedFilesOnFailure = pCopy.isPreserveClonedFilesOnFailure();
      _backupOriginalFiles = pCopy.isBackupOriginalFiles();
      _lastRepair = pCopy.getLastRepair();
    }

    public Boolean isPreserveClonedFilesOnFailure() {
      return _preserveClonedFilesOnFailure;
    }

    public void setPreserveClonedFilesOnFailure(Boolean pPreserveClonedFilesOnFailure) {
      _preserveClonedFilesOnFailure = pPreserveClonedFilesOnFailure;
    }

    public Boolean isBackupOriginalFiles() {
      return _backupOriginalFiles;
    }

    public void setBackupOriginalFiles(Boolean pBackupOriginalFiles) {
      _backupOriginalFiles = pBackupOriginalFiles;
    }

    public Date getLastRepair() {
      return _lastRepair;
    }

    public void setLastRepair(final Date pLastRepair) {
      _lastRepair = pLastRepair;
    }

    @Override
    public int hashCode() {
      return new HashCodeBuilder()
          .append(_preserveClonedFilesOnFailure)
          .append(_backupOriginalFiles)
          .append(_lastRepair)
          .toHashCode();
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == null || obj.getClass() != getClass()) {
        return false;
      }
      if (obj == this) {
        return true;
      }

      final RepairConfig lastRepair = (RepairConfig) obj;
      return new EqualsBuilder()
          .append(_preserveClonedFilesOnFailure, lastRepair._preserveClonedFilesOnFailure)
          .append(_backupOriginalFiles, lastRepair._backupOriginalFiles)
          .append(_lastRepair, lastRepair._lastRepair)
          .isEquals();
    }
  }

  public static class ProfilingConfig {
    @JsonProperty("profilingLevel")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Property("profilingLevel")
    @UnmanagedApiExposedField
    private Integer _profilingLevel;

    @JsonProperty("profilingLevelFilter")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Property("profilingLevelFilter")
    @UnmanagedApiExposedField
    private String _profilingLevelFilter;

    @JsonProperty("profilingLevelSlowMs")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Property("profilingLevelSlowMs")
    @UnmanagedApiExposedField
    private Integer _profilingLevelSlowMs;

    @JsonProperty("profilingLevelSampleRate")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Property("profilingLevelSampleRate")
    @UnmanagedApiExposedField
    private Double _profilingLevelSampleRate;

    public ProfilingConfig() {}

    public ProfilingConfig(final ProfilingConfig pCopy) {
      _profilingLevel = pCopy.getProfilingLevel();
      _profilingLevelFilter = pCopy.getProfilingLevelFilter();
      _profilingLevelSlowMs = pCopy.getProfilingLevelSlowMs();
      _profilingLevelSampleRate = pCopy.getProfilingLevelSampleRate();
    }

    public Integer getProfilingLevel() {
      return _profilingLevel;
    }

    public void setProfilingLevel(final Integer pProfilingLevel) {
      _profilingLevel = pProfilingLevel;
    }

    public String getProfilingLevelFilter() {
      return _profilingLevelFilter;
    }

    public void setProfilingLevelFilter(final String pProfilingLevelFilter) {
      _profilingLevelFilter = pProfilingLevelFilter;
    }

    public Integer getProfilingLevelSlowMs() {
      return _profilingLevelSlowMs;
    }

    public void setProfilingLevelSlowMs(final Integer pProfilingLevelSlowMs) {
      _profilingLevelSlowMs = pProfilingLevelSlowMs;
    }

    public Double getProfilingLevelSampleRate() {
      return _profilingLevelSampleRate;
    }

    public void setProfilingLevelSampleRate(final Double pProfilingLevelSampleRate) {
      _profilingLevelSampleRate = pProfilingLevelSampleRate;
    }

    @Override
    public int hashCode() {
      return new HashCodeBuilder()
          .append(_profilingLevel)
          .append(_profilingLevelFilter)
          .append(_profilingLevelSlowMs)
          .append(_profilingLevelSampleRate)
          .toHashCode();
    }

    protected EqualsBuilder getEqualsBuilder(final Object obj) {
      final ProfilingConfig config = (ProfilingConfig) obj;
      return new EqualsBuilder()
          .append(_profilingLevel, config._profilingLevel)
          .append(_profilingLevelFilter, config._profilingLevelFilter)
          .append(_profilingLevelSlowMs, config._profilingLevelSlowMs)
          .append(_profilingLevelSampleRate, config._profilingLevelSampleRate);
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == null || obj.getClass() != getClass()) {
        return false;
      }
      if (obj == this) {
        return true;
      }
      return getEqualsBuilder(obj).isEquals();
    }
  }

  public static class RedactOnUIJsonSerializer extends JsonSerializer<String> {
    @Override
    public void serialize(
        final String value, final JsonGenerator jgen, final SerializerProvider provider)
        throws IOException {
      if (provider.getActiveView() != null
          && provider.getActiveView() == JsonViews.WebUI_IncludeReadyOnly.class) {
        jgen.writeString(REDACTED_VALUE);
      } else {
        jgen.writeString(value);
      }
    }
  }

  /**
   * we don't visit FTDC and MONGODB for mongos with syslogDestination=syslog and
   * diagnosticDataCollectionDirectoryPath=null
   *
   * @return true if the process is a mongos with syslog configured and
   *     diagnosticDataCollectionDirectoryPath=null
   */
  public boolean isMongoSWithSysLogConfiguredAndDiagnosticDataCollectionSetToNull() {
    return isMongos()
        && getArgs2_6().isSysLog()
        && getArgs2_6().getDiagnosticDataCollectionDirectoryPath() == null;
  }
}
