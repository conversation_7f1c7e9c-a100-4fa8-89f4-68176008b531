package com.xgen.cloud.common.ratelimit.api._public.filter;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.common.filter._public.FilterContext;
import com.xgen.cloud.common.filter._public.ResourceFilter;
import com.xgen.cloud.common.ratelimit.api._private.monitoring.RateLimitMetrics;
import com.xgen.cloud.common.ratelimit.core._public.model.RateLimitOperationResult;
import com.xgen.cloud.common.ratelimit.core._public.model.RateLimitResult;
import com.xgen.cloud.common.ratelimit.core._public.model.TokenBucketConfig;
import com.xgen.cloud.common.ratelimit.core._public.svc.Bucket4jTokenBucketService;
import com.xgen.cloud.common.ratelimit.core._public.svc.CircuitBreakerService;
import com.xgen.cloud.common.requestparams._public.RequestParams;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.core.Response;
import java.time.Instant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Jersey ResourceFilter that applies system rate limiting to incoming requests with access to
 * Organization, Group, and AppUser objects from FilterContext.
 *
 * <p>This filter checks if rate limiting is enabled, matches the request path, and applies rate
 * limiting logic. It generates and attaches rate limit headers (X-RateLimit-Limit,
 * X-RateLimit-Remaining, X-RateLimit-Reset) to the response using values from a RateLimitResult.
 */
@Singleton
public class SystemRateLimitFilter implements ResourceFilter {

  private static final Logger LOG = LoggerFactory.getLogger(SystemRateLimitFilter.class);

  /** Metrics component name for rate limiting filter operations. */
  private static final String METRICS_COMPONENT = "filter";

  private final SystemRateLimitFilterSettings settings;
  private final CircuitBreakerService circuitBreaker;
  private final Bucket4jTokenBucketService rateLimitService;
  private final RateLimitPolicyMatcher policyMatcher;
  private final String endpointSetName;

  /** HTTP header for the maximum number of requests allowed in the current rate limit window. */
  public static final String HEADER_LIMIT = "X-RateLimit-Limit";

  /** HTTP header for the number of requests remaining in the current rate limit window. */
  public static final String HEADER_REMAINING = "X-RateLimit-Remaining";

  /**
   * HTTP header for the time at which the current rate limit window resets (in seconds since
   * epoch).
   */
  public static final String HEADER_RESET = "X-RateLimit-Reset";

  /** HTTP header for the bucket name used for rate limiting. */
  public static final String HEADER_BUCKET_NAME = "X-RateLimit-Bucket-Name";

  /** HTTP header that clients can send to request debug information. */
  public static final String DEBUG_HEADER = "X-RATE-LIMIT-DEBUG";

  /** HTTP header for the storage backend used for rate limiting (cache or redis). */
  public static final String HEADER_STORE = "X-RateLimit-Store";

  @Inject
  public SystemRateLimitFilter(
      SystemRateLimitFilterSettings settings,
      CircuitBreakerService circuitBreaker,
      Bucket4jTokenBucketService rateLimitService,
      RateLimitPolicyMatcher policyMatcher,
      String endpointSetName) {
    this.settings = settings;
    this.circuitBreaker = circuitBreaker;
    this.rateLimitService = rateLimitService;
    this.policyMatcher = policyMatcher;
    this.endpointSetName = endpointSetName;
  }

  /**
   * Applies rate limiting to the incoming request. If rate limiting is enabled and the path
   * matches, it generates rate limit headers and attaches them to the request context.
   *
   * @param requestContext the JAX-RS request context
   * @param filterContext the custom filter context
   * @return the filter context, possibly modified
   */
  @Override
  public FilterContext filter(ContainerRequestContext requestContext, FilterContext filterContext) {
    final Instant startTime = Instant.now();
    final String httpPath = requestContext.getUriInfo().getPath();
    final String httpMethod = requestContext.getMethod();

    if (!settings.isFilterEnabled(filterContext)) {
      LOG.debug("Rate limiting disabled via feature flag for path: {}", httpPath);
      return filterContext;
    }

    LOG.debug("Applying rate limiting for path: {}", httpPath);

    final RequestParams requestParams = filterContext.getRequestParams();
    if (requestParams == null) {
      return filterContext;
    }

    final String ipAddress = filterContext.getHttpServletRequest().getRemoteAddr();
    final RateLimitContext context =
        RateLimitContext.of(requestParams, ipAddress, httpPath, httpMethod);

    try {
      LOG.debug("Rate limit scope: {}", context.getPrimaryScope());

      final RateLimitResult result =
          circuitBreaker.executeSupplier(() -> executeRateLimits(context));

      handleRateLimitResult(requestContext, filterContext, result, context);
      recordMetrics(result, context, httpPath, httpMethod, startTime, Instant.now());
    } catch (CallNotPermittedException e) {
      LOG.warn("Circuit breaker open, allowing request through", e);
      recordMetricsPassthroughSuccess(context, httpPath, httpMethod, startTime, Instant.now());
    } catch (WebApplicationException e) {
      // Re-throw WebApplicationException (like rate limit 429 responses) only when rejection is
      // enabled
      if (settings.shouldRejectRequests()) {
        throw e;
      }
      // In shadow mode, log the rate limit violation but allow the request through
      LOG.warn("Rate limit exceeded in shadow mode, allowing request through: {}", e.getMessage());
      recordMetricsPassthroughSuccess(context, httpPath, httpMethod, startTime, Instant.now());
    } catch (Exception e) {
      LOG.warn("Rate limiting error, allowing request through: {}", e.getMessage());
      String scope =
          context.getPrimaryScope() != null
              ? context.getPrimaryScope().getScopeName()
              : METRICS_COMPONENT;
      RateLimitMetrics.recordError(scope, e, httpPath, httpMethod, startTime, Instant.now());
    }

    return filterContext;
  }

  @VisibleForTesting
  protected RateLimitResult executeRateLimits(RateLimitContext context) {
    String rateLimitKey = context.buildRateLimitKey(settings.getKeyPrefix(), endpointSetName);

    if (rateLimitKey == null) {
      LOG.error("No valid rate limit key for path: {}", context.httpPath());
      return RateLimitResult.builder()
          .result(RateLimitOperationResult.ACCEPTED_PASSTHROUGH)
          .remainingTokens(0)
          .bucketResetInSeconds(0)
          .build();
    }

    RateLimitContext.RateLimitScope scope = context.getPrimaryScope();
    TokenBucketConfig config = policyMatcher.getTokenBucketConfig(scope);
    return rateLimitService.consumeTokens(config, rateLimitKey, 1);
  }

  /**
   * Handles the rate limit result, implementing shadow mode logic and debug headers.
   *
   * @param requestContext the JAX-RS request context
   * @param filterContext the filter context containing the HTTP response
   * @param result the RateLimitResult containing rate limit information
   * @param context the rate limit context for logging
   */
  private void handleRateLimitResult(
      ContainerRequestContext requestContext,
      FilterContext filterContext,
      RateLimitResult result,
      RateLimitContext context) {

    if (result.config() == null) {
      LOG.debug("Rate limiting passthrough for path: {}", context.httpPath());
      return;
    }

    if (shouldAddDebugHeaders(requestContext)) {
      addRateLimitHeaders(filterContext, result, context);
    }

    if (!result.isAccepted()) {
      logRateLimitViolation(result, context, requestContext);

      if (settings.shouldRejectRequests()) {
        throwRateLimitException(requestContext, filterContext, result, context);
      }
    }
  }

  /**
   * Determines whether debug headers should be added to the response. Headers are added when the
   * debug header is present.
   *
   * @param requestContext the JAX-RS request context
   * @return true if debug headers should be added
   */
  private boolean shouldAddDebugHeaders(ContainerRequestContext requestContext) {
    String debugHeader = requestContext.getHeaderString(DEBUG_HEADER);
    return "true".equalsIgnoreCase(debugHeader);
  }

  /**
   * Throws a rate limit exception with HTTP 429 status and rate limit headers
   *
   * @param requestContext the JAX-RS request context
   * @param filterContext the filter context containing the HTTP response
   * @param result the RateLimitResult containing rate limit information
   * @param context the rate limit context for bucket name
   */
  private void throwRateLimitException(
      ContainerRequestContext requestContext,
      FilterContext filterContext,
      RateLimitResult result,
      RateLimitContext context) {

    String httpPath = requestContext.getUriInfo().getPath();
    addRateLimitHeaders(filterContext, result, context);

    throw new WebApplicationException(
        Response.status(ApiErrorCode.RATE_LIMITED.getStatus())
            .entity(createRateLimitErrorResponse(httpPath, result))
            .type("application/json")
            .build());
  }

  /**
   * Logs detailed information about rate limit violations for shadow mode.
   *
   * @param result the RateLimitResult containing rate limit information
   * @param context the rate limit context for additional details
   * @param requestContext the JAX-RS request context
   */
  private void logRateLimitViolation(
      RateLimitResult result, RateLimitContext context, ContainerRequestContext requestContext) {

    String rateLimitKey = context.buildRateLimitKey(settings.getKeyPrefix(), endpointSetName);
    String httpPath = requestContext.getUriInfo().getPath();
    String httpMethod = requestContext.getMethod();
    RateLimitContext.RateLimitScope primaryScope = context.getPrimaryScope();
    String scope = primaryScope != null ? primaryScope.toString() : "unknown";

    if (result.config() == null) {
      LOG.warn(
          "Rate limit exceeded - Path: {}, Method: {}, Scope: {}, Key: {}, Shadow Mode: {} (config"
              + " unavailable)",
          httpPath,
          httpMethod,
          scope,
          rateLimitKey,
          !settings.shouldRejectRequests());
      return;
    }

    LOG.warn(
        "Rate limit exceeded - Path: {}, Method: {}, Scope: {}, Key: {}, "
            + "Limit: {}, Remaining: {}, Reset: {}s, Shadow Mode: {}",
        httpPath,
        httpMethod,
        scope,
        rateLimitKey,
        result.config().capacity(),
        result.remainingTokens(),
        result.bucketResetInSeconds(),
        !settings.shouldRejectRequests());
  }

  /**
   * Adds rate limit headers to the HTTP response based on the provided RateLimitResult. Headers are
   * only added when rate limiting is enabled and the debug header is present.
   *
   * @param filterContext the filter context containing the HTTP response
   * @param result the RateLimitResult containing rate limit information
   * @param context the rate limit context for bucket name
   */
  private void addRateLimitHeaders(
      FilterContext filterContext, RateLimitResult result, RateLimitContext context) {
    // Skip header addition for passthrough scenarios (no rate limiting config)
    if (result.config() == null) {
      LOG.debug("Skipping rate limit headers for passthrough scenario");
      return;
    }

    filterContext
        .getHttpServletResponse()
        .addHeader(HEADER_LIMIT, String.valueOf(result.config().capacity()));
    filterContext
        .getHttpServletResponse()
        .addHeader(HEADER_REMAINING, String.valueOf(result.remainingTokens()));
    filterContext
        .getHttpServletResponse()
        .addHeader(HEADER_RESET, String.valueOf(result.bucketResetInSeconds()));

    // Add bucket name header for debugging
    String bucketName = context.buildRateLimitKey(settings.getKeyPrefix(), endpointSetName);
    if (bucketName != null) {
      filterContext.getHttpServletResponse().addHeader(HEADER_BUCKET_NAME, bucketName);
    }

    // Add storage backend header for debugging
    String storeType = rateLimitService.isUsingRedis() ? "distributed" : "local";
    filterContext.getHttpServletResponse().addHeader(HEADER_STORE, storeType);
  }

  /**
   * Creates the error response entity for rate limit violations using ApiErrorCode.RATE_LIMITED.
   * This follows the same pattern as existing rate limiting implementations.
   *
   * @param httpPath the HTTP path that was rate limited
   * @param result the RateLimitResult containing rate limit information
   * @return the error response entity
   */
  private Object createRateLimitErrorResponse(String httpPath, RateLimitResult result) {
    if (result.config() != null) {
      // Use the rate limit configuration to provide detailed error message
      return ApiErrorCode.RATE_LIMITED
          .response(
              false, // envelope = false for consistent behavior
              httpPath,
              result.config().capacity(),
              1 // period in minutes - using 1 as default since config doesn't expose period
              // directly
              )
          .getEntity();
    } else {
      // Fallback for cases where config is not available
      return ApiErrorCode.RATE_LIMITED.response(false, httpPath, 0, 1).getEntity();
    }
  }

  private void recordMetrics(
      RateLimitResult result,
      RateLimitContext context,
      String httpPath,
      String httpMethod,
      Instant startTime,
      Instant endTime) {
    if (result != null) {
      String scope =
          context.getPrimaryScope() != null
              ? context.getPrimaryScope().getScopeName()
              : METRICS_COMPONENT;

      String status = determineMetricStatus(result);

      RateLimitMetrics.recordSuccess(scope, status, httpPath, httpMethod, startTime, endTime);
    } else {
      recordMetricsPassthroughSuccess(context, httpPath, httpMethod, startTime, endTime);
    }
  }

  /**
   * Determines the metric status based on the rate limit result and rejection setting. When
   * rejection is disabled (shadow mode), rejected requests are recorded as SHADOW_REJECTED. When
   * rejection is enabled, rejected requests are recorded as REJECTED.
   *
   * @param result the rate limit result
   * @return the status string for metrics
   */
  private String determineMetricStatus(RateLimitResult result) {
    if (result.isAccepted()) {
      return result.result().toString();
    }

    if (settings.shouldRejectRequests()) {
      return RateLimitOperationResult.REJECTED.toString();
    }

    return RateLimitOperationResult.SHADOW_REJECTED.toString();
  }

  private void recordMetricsPassthroughSuccess(
      RateLimitContext context,
      String httpPath,
      String httpMethod,
      Instant startTime,
      Instant endTime) {
    String scope =
        context.getPrimaryScope() != null
            ? context.getPrimaryScope().getScopeName()
            : METRICS_COMPONENT;

    RateLimitMetrics.recordSuccess(
        scope,
        RateLimitOperationResult.ACCEPTED_PASSTHROUGH.toString(),
        httpPath,
        httpMethod,
        startTime,
        endTime);
  }
}
