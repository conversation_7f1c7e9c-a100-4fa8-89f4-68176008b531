package com.xgen.cloud.common.ratelimit.core._public.svc;

import com.google.common.base.Suppliers;
import com.xgen.cloud.common.ratelimit.core._public.model.RateLimitOperationResult;
import com.xgen.cloud.common.ratelimit.core._public.model.RateLimitResult;
import com.xgen.cloud.common.ratelimit.core._public.model.TokenBucketConfig;
import com.xgen.cloud.common.ratelimit.core._public.svc.externalcache.redis.RedisClientFactory;
import com.xgen.cloud.common.ratelimit.core._public.svc.externalcache.redis.RedisClientSettings;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.BucketConfiguration;
import io.github.bucket4j.ConsumptionProbe;
import io.github.bucket4j.distributed.proxy.ProxyManager;
import io.github.bucket4j.redis.lettuce.Bucket4jLettuce;
import io.lettuce.core.RedisException;
import io.lettuce.core.cluster.RedisClusterClient;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Bucket4j-based token bucket service
 *
 * <p>This service provides both local (in-memory) and distributed (Redis-backed) token buckets
 * using the Bucket4j library.
 */
@Singleton
public class Bucket4jTokenBucketService {

  private static final Logger LOG = LoggerFactory.getLogger(Bucket4jTokenBucketService.class);

  @Nullable private final RedisClientFactory redisClientFactory;
  @Nullable private final RedisClusterClient redisClient;
  @Nullable private final RedisClientSettings settings;

  // This memoized supplier guarantees that a proxy manager is computed upon the first call and only
  // once unless initialization throws an exception, then we'll try to do it again
  private final Supplier<ProxyManager<byte[]>> redisProxyManagerSupplier =
      Suppliers.memoize(this::initializeRedisProxyManager);

  private final ConcurrentHashMap<String, Bucket> localBuckets = new ConcurrentHashMap<>();

  // used only in tests
  public Bucket4jTokenBucketService() {
    this.redisClientFactory = null;
    this.redisClient = null;
    this.settings = null;
  }

  // used only in tests
  public Bucket4jTokenBucketService(RedisClusterClient redisClient) {
    this.redisClient = redisClient;
    this.redisClientFactory = null;
    this.settings = null;
  }

  @Inject
  public Bucket4jTokenBucketService(
      RedisClientSettings redisClientSettings, RedisClientFactory redisClientFactory) {
    this.settings = redisClientSettings;
    this.redisClientFactory = redisClientFactory;
    this.redisClient = null;
  }

  /**
   * Attempts to consume tokens from the bucket and returns the result.
   *
   * @param config The token bucket configuration (nullable - returns ACCEPTED_PASSTHROUGH if null)
   * @param bucketKey Unique identifier for the bucket
   * @param tokensToConsume Number of tokens to consume (typically 1)
   * @return RateLimitResult containing the outcome and remaining tokens
   */
  public RateLimitResult consumeTokens(
      @Nullable TokenBucketConfig config, String bucketKey, int tokensToConsume) {

    if (config == null) {
      LOG.warn("Rate Limit enabled but no config found for key: {}", bucketKey);
      return RateLimitResult.builder()
          .config(null)
          .result(RateLimitOperationResult.ACCEPTED_PASSTHROUGH)
          .build();
    }

    Bucket bucket = getBucket(config, bucketKey);
    ConsumptionProbe probe = bucket.tryConsumeAndReturnRemaining(tokensToConsume);

    RateLimitOperationResult operationResult =
        probe.isConsumed() ? RateLimitOperationResult.ACCEPTED : RateLimitOperationResult.REJECTED;

    return RateLimitResult.builder()
        .result(operationResult)
        .remainingTokens(probe.getRemainingTokens())
        .bucketResetInSeconds(probe.getNanosToWaitForReset() / 1_000_000L)
        .config(config)
        .build();
  }

  /** Gets or creates a bucket for the given configuration and key. */
  private Bucket getBucket(TokenBucketConfig config, String bucketKey) {
    if (isUsingRedis()) {
      return getDistributedBucket(config, bucketKey);
    } else {
      return getLocalBucket(config, bucketKey);
    }
  }

  /** Gets or creates a local in-memory bucket. */
  private Bucket getLocalBucket(TokenBucketConfig config, String bucketKey) {
    return localBuckets.computeIfAbsent(
        bucketKey,
        key -> {
          Duration refillPeriod = config.refillDuration();
          return Bucket.builder()
              .addLimit(
                  limit ->
                      limit
                          .capacity(config.capacity())
                          .refillGreedy(config.refillRate(), refillPeriod))
              .build();
        });
  }

  /** Gets or creates a distributed Redis-backed bucket. */
  private Bucket getDistributedBucket(TokenBucketConfig config, String bucketKey) {
    var redisProxyManager =
        redisProxyManagerSupplier
            .get(); // calls redis client initialization inside, if it fails, exception will be
    // thrown here

    byte[] keyBytes = bucketKey.getBytes();
    BucketConfiguration bucketConfig = convertToBucket4jConfig(config);
    return redisProxyManager.builder().build(keyBytes, () -> bucketConfig);
  }

  private ProxyManager<byte[]> initializeRedisProxyManager() {
    if (redisClientFactory == null && redisClient == null) {
      throw new IllegalStateException(
          "Either redis client factory or redis client must be initialized");
    }

    RedisClusterClient client = redisClient;
    if (redisClientFactory != null) {
      client = redisClientFactory.createRedisClient();
    }

    try {
      return Bucket4jLettuce.casBasedBuilder(client).build();
    } catch (RedisException e) {
      LOG.error("Error while establishing connection to redis: ", e);
      throw e;
    }
  }

  /** Converts TokenBucketConfig to Bucket4j BucketConfiguration. */
  private BucketConfiguration convertToBucket4jConfig(TokenBucketConfig config) {
    Duration refillPeriod = config.refillDuration();

    return BucketConfiguration.builder()
        .addLimit(
            limit ->
                limit.capacity(config.capacity()).refillGreedy(config.refillRate(), refillPeriod))
        .build();
  }

  /** Clears all local buckets. Only affects in-memory buckets, not Redis-backed ones. */
  public void clearLocalBuckets() {
    localBuckets.clear();
  }

  /** Returns the number of local buckets currently cached. */
  public int getLocalBucketCount() {
    return localBuckets.size();
  }

  /** Checks if this service is using Redis for distributed buckets. */
  public boolean isUsingRedis() {
    return settings != null && settings.isEnabled();
  }
}
