package com.xgen.cloud.common.ratelimit.core.runtime.examples;

import com.xgen.cloud.common.ratelimit.core._public.model.RateLimitResult;
import com.xgen.cloud.common.ratelimit.core._public.model.TokenBucketConfig;
import com.xgen.cloud.common.ratelimit.core._public.svc.Bucket4jTokenBucketService;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.BucketConfiguration;
import io.github.bucket4j.ConsumptionProbe;
import io.github.bucket4j.distributed.proxy.ProxyManager;
import io.github.bucket4j.redis.lettuce.Bucket4jLettuce;
import io.lettuce.core.cluster.RedisClusterClient;
import java.time.Duration;

/** Example class demonstrating rate limit policy usage */
public class RateLimitCoreExample {

  public static void main(String[] args) throws InterruptedException {
    System.out.println("=== Bucket4j Rate Limiting Example ===\n");

    // Configure Redis client
    RedisClusterClient redisClient = RedisClusterClient.create("redis://127.0.0.1:6379");

    // Example 1: Local Bucket4j bucket (in-memory)
    demonstrateLocalBucket();

    // Example 2: Distributed Bucket4j bucket with Redis (if Redis is available)
    try {
      demonstrateDistributedBucket(redisClient);
    } catch (Exception e) {
      System.out.println("=== Distributed Bucket4j with Redis Example ===");
      System.out.println("Redis connection failed: " + e.getMessage());
      System.out.println(
          "To test distributed buckets, start Redis with: docker run -p 6379:6379 redis:alpine");
      System.out.println();
    }

    // Example 3: New Bucket4jTokenBucketService (replacement for old implementation)
    demonstrateBucket4jService(redisClient);

    redisClient.shutdown();
  }

  private static void demonstrateLocalBucket() {
    System.out.println("=== Local Bucket4j Example ===");

    // Create a local bucket: 10 tokens capacity, refill 5 tokens per second
    Bucket bucket =
        Bucket.builder()
            .addLimit(limit -> limit.capacity(10).refillGreedy(5, Duration.ofSeconds(1)))
            .build();

    System.out.println("Testing local bucket with 10 capacity, 5 tokens/second refill:");

    for (int i = 0; i < 12; i++) {
      ConsumptionProbe probe = bucket.tryConsumeAndReturnRemaining(1);
      System.out.printf(
          "Request %d: %s, Remaining: %d tokens%n",
          i + 1, probe.isConsumed() ? "ALLOWED" : "REJECTED", probe.getRemainingTokens());

      if (!probe.isConsumed()) {
        System.out.printf(
            "  -> Wait %d ms for next token%n", probe.getNanosToWaitForRefill() / 1_000_000);
      }
    }
    System.out.println();
  }

  private static void demonstrateDistributedBucket(RedisClusterClient redisClient) {
    System.out.println("=== Distributed Bucket4j with Redis Example ===");

    // Create Bucket4j Redis proxy manager - note it uses byte[] keys
    ProxyManager<byte[]> proxyManager = Bucket4jLettuce.casBasedBuilder(redisClient).build();

    // Create bucket configuration: 10 tokens capacity, refill 5 tokens per second
    BucketConfiguration bucketConfig =
        BucketConfiguration.builder()
            .addLimit(limit -> limit.capacity(10).refillGreedy(5, Duration.ofSeconds(1)))
            .build();

    byte[] bucketKey = "bucket4j:example:distributed".getBytes();
    Bucket distributedBucket = proxyManager.builder().build(bucketKey, () -> bucketConfig);

    System.out.println("Testing distributed bucket with Redis backend:");

    for (int i = 0; i < 12; i++) {
      ConsumptionProbe probe = distributedBucket.tryConsumeAndReturnRemaining(1);
      System.out.printf(
          "Request %d: %s, Remaining: %d tokens%n",
          i + 1, probe.isConsumed() ? "ALLOWED" : "REJECTED", probe.getRemainingTokens());

      if (!probe.isConsumed()) {
        System.out.printf(
            "  -> Wait %d ms for next token%n", probe.getNanosToWaitForRefill() / 1_000_000);
      }
    }
    System.out.println();
  }

  private static void demonstrateBucket4jService(RedisClusterClient redisClient) {
    System.out.println("=== Bucket4jTokenBucketService Example (Replacement Implementation) ===");

    // Create token bucket configuration: 10 tokens capacity, refill 5 tokens per second
    TokenBucketConfig config = TokenBucketConfig.ofDuration(10, 5, Duration.ofSeconds(1), 1);

    // Test local (in-memory) service
    System.out.println("Testing local Bucket4jTokenBucketService:");
    Bucket4jTokenBucketService localService = new Bucket4jTokenBucketService();

    for (int i = 0; i < 12; i++) {
      RateLimitResult result = localService.consumeTokens(config, "test:bucket:local", 1);
      System.out.printf(
          "Request %d: %s, Remaining: %d tokens%n",
          i + 1, result.isAccepted() ? "ALLOWED" : "REJECTED", result.remainingTokens());
    }

    System.out.println("\nLocal service stats:");
    System.out.printf("- Using Redis: %s%n", localService.isUsingRedis());
    System.out.printf("- Local bucket count: %d%n", localService.getLocalBucketCount());

    // Test distributed (Redis) service if available
    try {
      System.out.println("\nTesting distributed Bucket4jTokenBucketService:");
      Bucket4jTokenBucketService distributedService = new Bucket4jTokenBucketService(redisClient);

      for (int i = 0; i < 12; i++) {
        RateLimitResult result =
            distributedService.consumeTokens(config, "test:bucket:distributed", 1);
        System.out.printf(
            "Request %d: %s, Remaining: %d tokens%n",
            i + 1, result.isAccepted() ? "ALLOWED" : "REJECTED", result.remainingTokens());
      }

      System.out.println("\nDistributed service stats:");
      System.out.printf("- Using Redis: %s%n", distributedService.isUsingRedis());
      System.out.printf("- Local bucket count: %d%n", distributedService.getLocalBucketCount());

    } catch (Exception e) {
      System.out.println(
          "\nDistributed service test skipped - Redis not available: " + e.getMessage());
    }

    System.out.println();
  }
}
