package com.xgen.cloud.common.temporal._public.svc;

import static net.logstash.logback.argument.StructuredArguments.kv;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.uber.m3.tally.RootScopeBuilder;
import com.uber.m3.tally.Scope;
import com.uber.m3.util.Duration;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.common.temporal._public.model.TemporalClientConfig;
import com.xgen.cloud.common.temporal._public.settings.DefaultTemporalSettings;
import io.micrometer.core.instrument.MeterRegistry;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowClientOptions;
import io.temporal.client.schedules.ScheduleClient;
import io.temporal.client.schedules.ScheduleClientOptions;
import io.temporal.common.converter.DefaultDataConverter;
import io.temporal.common.converter.JacksonJsonPayloadConverter;
import io.temporal.common.reporter.MicrometerClientStatsReporter;
import io.temporal.serviceclient.WorkflowServiceStubs;
import io.temporal.serviceclient.WorkflowServiceStubsOptions;
import jakarta.inject.Inject;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TemporalClientSvc {

  private static final Logger LOG = LoggerFactory.getLogger(TemporalClientSvc.class);
  public static final String TEMPORAL_API_KEY = "TEMPORAL_API_KEY";
  private final AppSettings _appSettings;
  private final DefaultTemporalSettings _defaultTemporalSettings;
  private final MeterRegistry _meterRegistry;

  @Inject
  public TemporalClientSvc(
      final AppSettings appSettings,
      final DefaultTemporalSettings defaultTemporalSettings,
      final MeterRegistry pMeterRegistry) {
    _appSettings = appSettings;
    _defaultTemporalSettings = defaultTemporalSettings;
    _meterRegistry = pMeterRegistry;
  }

  public WorkflowClient getClientForDefaultJobQueueNamespace() {
    return getWorkflowClient(_defaultTemporalSettings.getDefaultTemporalClientConfig());
  }

  public WorkflowClient getWorkflowClient(final TemporalClientConfig temporalClientConfig) {
    final WorkflowClientOptions workflowClientOptions =
        getWorkflowClientOptions(temporalClientConfig.getNamespace());
    return WorkflowClient.newInstance(
        getWorkflowServiceStubs(temporalClientConfig), workflowClientOptions);
  }

  private WorkflowClientOptions getWorkflowClientOptions(final String pTemporalNamespace) {
    final ObjectMapper objectMapper = CustomJacksonJsonProvider.createObjectMapper();
    return WorkflowClientOptions.newBuilder()
        .setDataConverter(
            DefaultDataConverter.newDefaultInstance()
                // We want to use our own ObjectMapper with specific
                // serialization/deserialization modules that support things like ObjectId
                .withPayloadConverterOverrides(new JacksonJsonPayloadConverter(objectMapper)))
        .setNamespace(pTemporalNamespace)
        .build();
  }

  public ScheduleClient getScheduleClient(final TemporalClientConfig temporalClientConfig) {
    final ScheduleClientOptions scheduleClientOptions =
        getScheduleClientOptions(temporalClientConfig.getNamespace());
    return ScheduleClient.newInstance(
        getWorkflowServiceStubs(temporalClientConfig), scheduleClientOptions);
  }

  private ScheduleClientOptions getScheduleClientOptions(final String pTemporalNamespace) {
    final ObjectMapper objectMapper = CustomJacksonJsonProvider.createObjectMapper();
    return ScheduleClientOptions.newBuilder()
        .setDataConverter(
            DefaultDataConverter.newDefaultInstance()
                // We want to use our own ObjectMapper with specific
                // serialization/deserialization modules that support things like ObjectId
                .withPayloadConverterOverrides(new JacksonJsonPayloadConverter(objectMapper)))
        .setNamespace(pTemporalNamespace)
        .build();
  }

  private WorkflowServiceStubs getWorkflowServiceStubs(
      final TemporalClientConfig temporalClientConfig) {
    final Optional<String> temporalApiKey =
        Optional.ofNullable(_defaultTemporalSettings.getTemporalApiKey());
    final boolean isGovCloud = _appSettings.getAppEnv().isGovCloud();
    if (temporalApiKey.isEmpty() && !isGovCloud) {
      if (_appSettings.getAppEnv().isLocalOrTest()) {
        LOG.warn(
            "No Temporal API key configured for {} environment. Will use local test workflow client"
                + " for the default namespace.",
            _appSettings.getAppEnv());
        return WorkflowServiceStubs.newLocalServiceStubs();
      } else {
        throw new IllegalStateException(
            "No Temporal API key configured. Please set the environment variable "
                + TEMPORAL_API_KEY
                + " to a valid API key.");
      }
    }

    final String temporalNamespace = temporalClientConfig.getNamespace();
    final String temporalGrpcAddress = temporalClientConfig.getGrpcAddress();
    final String temporalTLSOverride = temporalClientConfig.getTlsOverride();

    WorkflowServiceStubsOptions.Builder stubOptions =
        WorkflowServiceStubsOptions.newBuilder()
            .setTarget(temporalGrpcAddress)
            .setMetricsScope(getMetricsScope());

    if (!isGovCloud) {
      // For commercial environment, we communicate with Temporal Cloud.
      // This requires setting an API key and TLS server override.
      LOG.info(
          "Initializing Temporal workflow client using Temporal API key. {} {} {}",
          kv("grpcAddress", temporalGrpcAddress),
          kv("namespace", temporalNamespace),
          kv("tlsServerOverride", temporalTLSOverride));

      stubOptions
          .addApiKey(temporalApiKey::get)
          .setChannelInitializer((channel) -> channel.overrideAuthority(temporalTLSOverride))
          .setEnableHttps(true);
    } else {
      // For gov env, we communicate with a self-hosted Temporal instance through istio.
      // We use HTTP since the connection is secured within the istio service mesh.
      LOG.info(
          "Initializing Temporal workflow client for gov environment. {} {}",
          kv("grpcAddress", temporalGrpcAddress),
          kv("namespace", temporalNamespace));
      stubOptions.setEnableHttps(false);
    }

    return WorkflowServiceStubs.newServiceStubs(stubOptions.build());
  }

  private Scope getMetricsScope() {
    final MicrometerClientStatsReporter reporter =
        new MicrometerClientStatsReporter(_meterRegistry);
    return new RootScopeBuilder().reporter(reporter).reportEvery(Duration.ofSeconds(10));
  }
}
