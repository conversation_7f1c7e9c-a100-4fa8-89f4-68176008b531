/* (C) Copyright 2013, MongoDB, Inc. */

package com.xgen.cloud.common.appsettings._public.svc;

import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.ALERTS_HOST_MEASUREMENT_LOOKBACK_DURATION_MILLIS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.ALLOW_LOCALHOST_CORS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.APPSETTINGS_DB_POLLING_INTERVAL;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.AUTHN_SERVICE_GRPC_ADDRESS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.AUTHN_SERVICE_GRPC_ADDRESS_ENV_VAR;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.BACKUP_MIN_FILES_FOR_SMALL_FILE_OPTIMIZATION;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.BACKUP_SMALL_FILE_SIZE_LIMIT;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.CLUSTER_CHANGE_SVC_CRON_JOB_FREQUENCY;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.CLUSTER_CHANGE_SVC_PROCESS_CHANGES_BEFORE;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.CONFIG_SERVICE_GRPC_ADDRESS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.CONFIG_SERVICE_GRPC_ADDRESS_ENV_VAR;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.DISABLE_HTTP_PROXY_FOR_S3;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.DRY_RUN_INTEGRATION_DELETION;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.GIT_BRANCH;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.GIT_LATEST_COMMIT;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.HTTP_PROXY_HOST;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.HTTP_PROXY_NON_PROXY_HOSTS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.HTTP_PROXY_PASSWORD;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.HTTP_PROXY_PORT;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.HTTP_PROXY_USERNAME;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.JAVA_FIPS_PROVIDER_DEFAULT;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.JETTY_SSL_SNI_HOST_CHECK;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.JETTY_SSL_SNI_REQUIRED;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.JETTY_SSL_STS_INCLUDE_SUBDOMAINS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.JETTY_SSL_STS_MAX_AGE_SECONDS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.JOB_PROCESSOR_ENABLED_RUNTIME;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.METERING_CENTRAL_URL_SCHEME;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.METERING_USER_PRIVATE_KEY;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.METERING_USER_PUBLIC_KEY;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_ALERTS_GLOBAL_SUMMARY_EMAIL_INTERVAL_HOURS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_APPSETTINGS_FETCH_SECRETS_IN_BATCH;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_APP_HOSTNAME;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_HTTPS_CAFILE;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_HTTPS_PEMKEYFILE;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_HTTPS_PEMKEYFILE_PASSWORD;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_HTTP_BINDHOSTNAME;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_HTTP_MAX_REQUEST_HEADER_SIZE_IN_BYTES;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_JETTY_BUFFER_POOL_DIRECT_SIZE_MB;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_JETTY_BUFFER_POOL_HEAP_SIZE_MB;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_JETTY_BUFFER_POOL_LEAK_DETECTION_ENABLED;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_JETTY_BUFFER_POOL_OVERRIDE_ENABLED;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_JETTY_ENABLE_GZIP_HANDLER;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.MMS_SECURITY_SECURE_RANDOM_POOL_SIZE;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NDS_DRY_RUN_MODE_ENABLED;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NDS_PROXY_NA_REGEX_THROTTLING_MILLIS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NDS_PROXY_NA_REGEX_THROTTLING_WATCHED_NAMESPACES;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NEXT_PROJECT_FOR_INTEGRATION_MIGRATION;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NS_JVM_KRB5_CONF;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NS_JVM_KRB5_KDC;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NS_JVM_KRB5_REALM;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NS_MMS_KERBEROS_DEBUG;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.NS_MMS_KERBEROS_PRINCIPAL;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.PROMETHEUS_INTEGRATION_BURST_TOKEN_COUNT;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.PROMETHEUS_INTEGRATION_TOKEN_FILL_RATE_SECONDS;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.PROMETHEUS_LISTENING_PORT;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.Fields.REMOTE_IP_HEADER;
import static com.xgen.cloud.common.appsettings._public.svc.AppSettings.HostMeasurementAlertReadMode.READ_BOTH;
import static com.xgen.cloud.common.db.base._public.config.ClientConfigCache.DELIM;
import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.getCutoverUri;
import static com.xgen.cloud.common.db.legacy._public.svc.impl.MongoSvcUtils.getUri;
import static com.xgen.cloud.common.driverwrappers._public.common.MongoClientRuntimeSettingsCache.CUTOVER;
import static com.xgen.cloud.common.driverwrappers._public.common.MongoClientRuntimeSettingsCache.CUTOVER_REGEX_PATTERN;
import static com.xgen.cloud.common.driverwrappers._public.common.MongoClientRuntimeSettingsCache.parseDbNames;
import static com.xgen.cloud.common.model._public.email.TemplateMap.IS_ON_PREM;
import static com.xgen.cloud.common.model._public.email.TemplateMap.REALM_CENTRAL_URL_FIELD;
import static com.xgen.cloud.common.util._public.util.SysProp.Property.BASE_PORT;
import static com.xgen.cloud.common.util._public.util.SysProp.Property.BASE_SSL_PORT;
import static com.xgen.cloud.common.util._public.util.SysProp.Property.INSTANCE_ID;
import static com.xgen.cloud.configsdk._public.wrapper.ConfigServiceSdkWrapper.MMS_NAMESPACE;
import static java.util.Objects.nonNull;
import static java.util.Optional.ofNullable;
import static java.util.Spliterator.ORDERED;
import static java.util.Spliterators.spliteratorUnknownSize;
import static java.util.stream.Collectors.toSet;
import static net.logstash.logback.util.StringUtils.trimToNull;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import com.amazonaws.ClientConfiguration;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Splitter;
import com.google.inject.Stage;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.ConnectionString;
import com.mongodb.DBObject;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoClientURI;
import com.mongodb.MongoException;
import com.mongodb.client.MongoClients;
import com.mongodb.internal.VisibleForTesting.AccessModifier;
import com.xgen.cloud.common.appsettings._private.dao.AppPropertyDao;
import com.xgen.cloud.common.appsettings._private.dao.DataRegionConfigDao;
import com.xgen.cloud.common.appsettings._public.model.AppEnv;
import com.xgen.cloud.common.appsettings._public.model.AppSettingsChange;
import com.xgen.cloud.common.appsettings._public.model.DataRegionConfig;
import com.xgen.cloud.common.appsettings._public.model.MultiFactorAuthLevel;
import com.xgen.cloud.common.appsettings._public.model.ThemePreference;
import com.xgen.cloud.common.appsettings._public.model.VersionInfo;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings.ResolvedProperty.Source;
import com.xgen.cloud.common.aws._public.secret.AwsManagedSecrets;
import com.xgen.cloud.common.constants._public.model.communication.DatadogRegion;
import com.xgen.cloud.common.dao.codec._public.provider.CodecRegistryProvider;
import com.xgen.cloud.common.db.base._public.config.ClientConfig;
import com.xgen.cloud.common.db.base._public.config.ClientConfigCache;
import com.xgen.cloud.common.db.legacy._public.svc.SingleClientMongoSvc;
import com.xgen.cloud.common.db.mongo._public.config.MongoClientConfigCache;
import com.xgen.cloud.common.db.mongo._public.settings.MongoClientSettingsBuilder;
import com.xgen.cloud.common.db.mongo._public.util.MongoClientUtils;
import com.xgen.cloud.common.driverwrappers._public.common.MongoClientRuntimeSettingsCache;
import com.xgen.cloud.common.driverwrappers._public.legacy.MongoClient;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag.EnvState;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.model._public.email.EmailValidation;
import com.xgen.cloud.common.model._public.email.TemplateMap;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.math.Units;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils;
import com.xgen.cloud.common.security._public.model.IgnoreGenEncryptMetadata;
import com.xgen.cloud.common.security._public.model.SSLContextSettings;
import com.xgen.cloud.common.security._public.settings.SecuritySettings;
import com.xgen.cloud.common.security._public.util.EncryptionUtils;
import com.xgen.cloud.common.system._public.util.SystemUtils;
import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.cloud.common.util._public.util.AgentType;
import com.xgen.cloud.common.util._public.util.AgentVersion;
import com.xgen.cloud.common.util._public.util.DaemonThreadFactory;
import com.xgen.cloud.common.util._public.util.LogUtils;
import com.xgen.cloud.common.util._public.util.SysProp;
import com.xgen.cloud.common.util._public.util.SysProp.Property;
import com.xgen.cloud.common.util._public.util.UrlReadingUtils;
import com.xgen.cloud.configsdk._public.wrapper.ConfigServiceSdkWrapper;
import com.xgen.cloud.search.util._public.version.MongotVersion;
import configservicesdk.com.xgen.devtools.configservicesdk.ConfigServiceRequestContext;
import configservicesdk.com.xgen.devtools.configservicesdk.Secret;
import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import io.prometheus.client.Histogram;
import jakarta.annotation.Nullable;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.validation.constraints.NotNull;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.Authenticator;
import java.net.InetSocketAddress;
import java.net.MalformedURLException;
import java.net.PasswordAuthentication;
import java.net.URL;
import java.net.UnknownHostException;
import java.security.Security;
import java.time.Duration;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;
import javax.security.auth.login.Configuration;
import org.apache.commons.configuration2.AbstractConfiguration;
import org.apache.commons.configuration2.convert.ConversionHandler;
import org.apache.commons.configuration2.convert.DefaultConversionHandler;
import org.apache.commons.configuration2.convert.DefaultListDelimiterHandler;
import org.apache.commons.configuration2.ex.ConversionException;
import org.apache.commons.configuration2.interpol.ConfigurationInterpolator;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * The application configuration/settings object (env specific config).
 *
 * <p>This component is manually created in AppConfig and then bound to an existing instance.
 *
 * <p>Configuration is loaded from a few sources, with a hierarchy as follows: Database ->
 * Properties File -> System Properties -> In Memory Overrides
 */
@Singleton
public class AppSettings extends AbstractConfiguration implements Closeable {

  private static final Logger LOG = LoggerFactory.getLogger(AppSettings.class);

  private static final String LDAP_SSL_CONTEXT_PREFIX = "mms.ldap";
  private static final String MONGODB_SSL_CONTEXT_PREFIX = "mongodb";

  private static final String ENV_VAR_PREFIX = "MMSENV_";
  public static final String COMMA_DELIMITER = ",";

  public static final String SFDC_SUPPORT_MAINTENANCE_MESSAGE = "sfdc.support.maintenanceMessage";

  private static final long DEFAULT_FREQUENCY_DEBOUNCE_THRESHOLD_SECONDS =
      Duration.ofMinutes(15).toSeconds();
  private static final String DEFAULT_DATA_ROOT = "/data";
  public static final String DEFAULT_DOWNLOAD_BASE = "/var/lib/mongodb-mms-automation";
  private static final String DEFAULT_BACKUP_AGENT_LOG_FILE =
      "/var/log/mongodb-mms-automation/backup-agent.log";
  private static final String DEFAULT_CPS_MODULE_LOG_FILE =
      "/var/log/mongodb-mms-automation/cps.log";
  private static final String DEFAULT_ONLINE_ARCHIVE_MODULE_LOG_FILE =
      "/var/log/mongodb-mms-automation/online-archive.log";
  private static final String DEFAULT_DB_CHECK_MODULE_LOG_FILE =
      "/var/log/mongodb-mms-automation/db-check.log";
  private static final String DEFAULT_MONGOSQLD_LOG_FILE =
      "/var/log/mongodb-mms-automation/mongosqld.log";
  private static final String DEFAULT_MONITORING_AGENT_LOG_FILE =
      "/var/log/mongodb-mms-automation/monitoring-agent.log";

  private static final String DEFAULT_DOWNLOAD_BASE_WINDOWS =
      "%SystemDrive%\\MMSAutomation\\versions";
  private static final String DEFAULT_BACKUP_AGENT_LOG_FILE_WINDOWS =
      "%SystemDrive%\\MMSAutomation\\log\\mongodb-mms-automation\\backup-agent.log";
  private static final String DEFAULT_MONGOSQLD_LOG_FILE_WINDOWS =
      "%SystemDrive%\\MMSAutomation\\log\\mongodb-mms-automation\\mongosqld.log";
  private static final String DEFAULT_MONITORING_AGENT_LOG_FILE_WINDOWS =
      "%SystemDrive%\\MMSAutomation\\log\\mongodb-mms-automation\\monitoring-agent.log";

  private static final Double DEFAULT_AUTOMATION_SENTRY_SAMPLE_RATE = 0.1;

  private static final String SECRET_MANAGER_STRING = "<SECRETMANAGER>";

  /**
   * Used in {@link AppSettings#getProp} as a sentinel value to indicate an expected return default
   * value of {@code null}.
   *
   * <p>The Config Service SDK does not support {@code null} default values. This placeholder is
   * used instead of {@code null}. If the Config Service {@link
   * AppSettings#getStringPropertyFromConfigService} returns the {@code NULL_PLACEHOLDER} string we
   * can assume that {@code null} is the intended default value for MMS.
   */
  private static final String NULL_PLACEHOLDER = "__NULL_PLACEHOLDER_VALUE__";

  private static final String USER_SVC_DB_CLASS = "UserSvcDb";
  private static final String USER_SVC_OKTA_CLASS = "UserSvcOkta";

  public static final String AWS_SECRET_MANAGER_DISABLED_PROPERTY = "local.aws.secrets.disabled";
  public static final String ATLAS_CLUSTER_UPDATE_OPTIMISTIC_CONCURRENCY =
      "mms.atlasClusterUpdateOptimisticConcurrency";
  public static final String HAS_HTTP_DUAL_CONNECTORS = "mms.https.dualConnectors";
  public static final Integer DEFAULT_PORT = 8080;
  public static final Integer DEFAULT_SSL_PORT = 8443;
  public static final Integer DEFAULT_INSTANCE_ID = 0;
  public static final Float DEFAULT_DAILY_SAMPLE_PERCENT = 0.0F;
  public static final Integer DEFAULT_MIN_DAYS_BETWEEN_CLUSTER_VALIDATIONS = 182;
  public static final Integer DEFAULT_MIN_CLUSTER_AGE_DAYS = 182;

  public static final Float DEFAULT_GEN_AI_COMPASS_ACCESS_PERCENT = 0.0F;
  public static final Integer DEFAULT_NDS_METER_USAGE_SUBMISSION_SPREAD = 550;
  public static final Integer DEFAULT_NDS_METER_USAGE_RETRY_SUBMISSION_SPREAD = 1000;
  public static final Integer DEFAULT_NDS_METER_USAGE_SUBSCRIPTION_SPREAD = 200;
  public static final String DEFAULT_INSTANCE_OS = "AL2";
  public static final String DEFAULT_SERVERLESS_PROXY_OS = "AL2";

  public static final String DEFAULT_STREAMS_PROXY_OS = "AL2";
  private static final Integer DEFAULT_CPS_OPLOG_GAP_TIMESTAMP = 1741636800;

  public static final String GCP_API_HTTP_TRANSPORT = "nds.gcp.api.transport";

  // Settings for Planner
  public static final Long GROUP_PLANNING_INTERVAL_LAST_FAILED = Duration.ofMinutes(1).toMillis();
  public static final Long GROUP_PLANNING_INTERVAL_SHORT = Duration.ofMinutes(15).toMillis();
  public static final Long GROUP_PLANNING_INTERVAL_MEDIUM = Duration.ofHours(1).toMillis();
  public static final Long GROUP_PLANNING_INTERVAL_LONG = Duration.ofHours(24).toMillis();
  public static final Long GROUP_PLANNING_INTERVAL_WHILE_UNDER_MAINTENANCE =
      Duration.ofMinutes(15).toMillis();

  // Service mesh regions for ADL GRPC server interactions. GCP is handled with an app setting
  // since region changes based on environment.
  private static final String AWS_SERVICE_MESH_HOST_REGION_NAME = "US_EAST_1";
  private static final String AZURE_SERVICE_MESH_HOST_REGION_NAME = "EASTUS2";
  private static final String GCP_DEFAULT_SERVICE_MESH_HOST_REGION_NAME = "US_EAST_4";

  private static final Histogram SECRETS_FETCH_LOAD_TIME =
      new Histogram.Builder()
          .name("app_settings_secrets_fetch_seconds")
          .help("amount of time it took to load app settings secrets")
          .labelNames("method")
          .register();
  private static final Counter SECRETS_FETCH_COUNT =
      new Counter.Builder()
          .name("app_settings_secrets_fetch_total")
          .help("number of times app settings fetched secrets")
          .labelNames("method", "error")
          .register();

  private static final Gauge CODE_VERSION_GAUGE =
      PromMetricsSvc.registerGauge(
          "mms_code_version", "code version info for mms", "git_hash", "version");

  private static final Counter CONFIG_SERVICE_FEATURE_FLAG_PHASE_EVAL_COUNTER =
      PromMetricsSvc.registerCounter(
          "mms_config_service_feature_flag_phase_eval_failure_total",
          "Total number of config service feature flag phase evaluation failures",
          "error",
          "featureFlag",
          "envState");

  /** a helix pod regexp pattern e.g. mms-jobs-alerts-5dc8d7c4f9-97kz4 */
  private static final Pattern HELIX_POD_NAME_PATTERN =
      Pattern.compile("((\\w+-*\\w+)+)-[a-z,0-9]{10}-[a-z,0-9]{5}");

  public static final int DEFAULT_ATLAS_SEARCH_PLANNER_TIMEOUT_HOURS = 168; // 1 week

  /**
   * A DAO to access DB properties and Cache to sit in front of those properties so that they are
   * only loaded every DB_CACHE_SECONDS seconds.
   */
  private final AppPropertyDao _dbProperties;

  private final ScheduledExecutorService _scheduler =
      Executors.newScheduledThreadPool(1, new DaemonThreadFactory("AppSettingsDBProperties"));
  private ScheduledFuture<?> _scheduledFuture;
  private final AtomicReference<Properties> _cachedDbProperties;
  private final DatabasePropertiesRefresher _dbPropertiesRefresher;

  /**
   * The set of properties loaded from configuration files for bootstrapping. These properties are
   * immutable after AppSettings is constructed.
   */
  private final Properties _defaultFileProperties;

  private final Properties _overrideFileProperties;

  private final Properties _overrideEnvProperties;

  private final String _defaultPropFilePath;
  private final List<String> _overridePropFilePaths;

  /**
   * Properties overridden at runtime and stored in memory. Using a HashMap vs. Properties so that a
   * property can be set to `null` effectively hiding the value of a db, file, or system property.
   */
  private final Map<String, String> _memoryProperties;

  /**
   * Property names that are for managed secrets. This set is used to facilitate Config Service
   * migration. During a certain migration phase, we will only use Config Service for non-secret
   * properties, and this set will help us decide which properties to ignore. Note: Making this
   * package private for testing purposes.
   */
  Set<String> propertyNamesOfSecrets = new HashSet<>();

  private final AppEnv _appEnv;
  private final String _appId;
  // expected to be values of the AWSRegionName enum (eg. us-east-1 but not US_EAST_1)
  private final String _region;
  @Nullable private final DataRegionConfig _dataRegionConfig;

  private final int _globalAlertsSummaryEmailIntervalHours;

  private static final Duration DB_CACHE_REFRESH_INTERVAL = Duration.ofSeconds(30);
  public static final String APP_PROPERTY_GLOBAL_PREFIX = "AppSettings.Global.";

  public static final String APP_PROPERTY_INSTANCE_PREFIX = "AppSettings.Instance.";
  public static final String APP_PROPERTY_VERSION_NUMBER = "versionNo";

  // set of AppSettings properties that should be obfuscated, i.e. passwords
  public static Set<String> SECRET_PROPERTIES =
      Set.of(
          "auto.provision.aws.reaper.secretkey",
          "auto.provision.aws.trusted.secretkey",
          "aws.secretkey",
          "brs.queryable.pem.pwd",
          "okta.cmab.client.secret",
          "dyn.password",
          "http.proxy.password",
          MMS_HTTPS_PEMKEYFILE_PASSWORD.value,
          "mms.mail.password",
          "mongodb.ssl.PEMKeyFilePassword",
          "mms.saml.ssl.PEMKeyFilePassword",
          "sfdc.api.password",
          Fields.STRIPE_API_KEY.value,
          "twilio.auth.token",
          Fields.VERCEL_CLIENT_ID.value,
          Fields.VERCEL_SECRET.value,
          Fields.CHARTS_REALM_API_SECRET.value,
          Fields.MMS_ALERTS_WEBHOOK_ADMIN_ENDPOINT.value,
          Fields.MMS_ALERTS_WEBHOOK_ADMIN_SECRET.value);

  public static Set<String> EXCLUDE_FROM_MD5_HEX_PROPERTIES =
      Set.of(
          Fields.MMS_ALERTS_WEBHOOK_ADMIN_ENDPOINT.value,
          Fields.MMS_ALERTS_WEBHOOK_ADMIN_SECRET.value);

  private static final List<String> BASE_NON_CONFIG_APP_PROPS =
      List.of(
          "authn.internalClient.id",
          "authn.internalClient.secret",
          "client.authn.http.address",
          "client.authn.http.addressEnvVar",
          "mms.https.bindhostname",
          "mms.jetty.enableHTTP2Cleartext",
          "mms.jetty.stopTimeoutMS",
          "prom.listening.enabled",
          "prom.listening.publicMetricsOnly",
          "prom.saving.publicMetrics",
          APPSETTINGS_DB_POLLING_INTERVAL.value,
          AUTHN_SERVICE_GRPC_ADDRESS.value,
          AUTHN_SERVICE_GRPC_ADDRESS_ENV_VAR.value,
          BASE_PORT.value(),
          BASE_SSL_PORT.value(),
          CONFIG_SERVICE_GRPC_ADDRESS.value,
          CONFIG_SERVICE_GRPC_ADDRESS_ENV_VAR.value,
          GIT_BRANCH.value,
          GIT_LATEST_COMMIT.value,
          HAS_HTTP_DUAL_CONNECTORS,
          HTTP_PROXY_HOST.value,
          HTTP_PROXY_NON_PROXY_HOSTS.value,
          HTTP_PROXY_PASSWORD.value,
          HTTP_PROXY_PORT.value,
          HTTP_PROXY_USERNAME.value,
          INSTANCE_ID.value(),
          JAVA_FIPS_PROVIDER_DEFAULT.value,
          JETTY_SSL_SNI_HOST_CHECK.value,
          JETTY_SSL_SNI_REQUIRED.value,
          JETTY_SSL_STS_INCLUDE_SUBDOMAINS.value,
          JETTY_SSL_STS_MAX_AGE_SECONDS.value,
          JOB_PROCESSOR_ENABLED_RUNTIME.value,
          MMS_ALERTS_GLOBAL_SUMMARY_EMAIL_INTERVAL_HOURS.value,
          // below prop is passed in to initialize the Config Service SDK
          MMS_APPSETTINGS_FETCH_SECRETS_IN_BATCH.value,
          MMS_HTTPS_CAFILE.value,
          MMS_HTTPS_PEMKEYFILE_PASSWORD.value,
          MMS_HTTP_MAX_REQUEST_HEADER_SIZE_IN_BYTES.value,
          MMS_JETTY_BUFFER_POOL_DIRECT_SIZE_MB.value,
          MMS_JETTY_BUFFER_POOL_HEAP_SIZE_MB.value,
          MMS_JETTY_BUFFER_POOL_LEAK_DETECTION_ENABLED.value,
          MMS_JETTY_BUFFER_POOL_OVERRIDE_ENABLED.value,
          MMS_JETTY_ENABLE_GZIP_HANDLER.value,
          MMS_SECURITY_SECURE_RANDOM_POOL_SIZE.value,
          NS_JVM_KRB5_CONF.value,
          NS_JVM_KRB5_KDC.value,
          NS_JVM_KRB5_REALM.value,
          NS_MMS_KERBEROS_DEBUG.value,
          NS_MMS_KERBEROS_PRINCIPAL.value,
          PROMETHEUS_LISTENING_PORT.value,
          REMOTE_IP_HEADER.value,
          /* This property is used to avoid reaching out to AWS in unit tests */
          AWS_SECRET_MANAGER_DISABLED_PROPERTY,
          /*
           * This property is defined in NDSSettings.Properties. "getRootAuthorizedKeys" uses a
           * "PROPERTIES_FILE" SettingType - never needs to be overridden by database value.
           */
          "nds.instances.rootAuthorizedKeys",
          /*
           * This is checked by MongoAppender on every LOG call. Integration tests use a code path
           * that hits this property before the mock Config Service SDK is initialized causing
           * time-out issues in Evergreen.
           */
          "mmsdbserverlog.logs.enabled");

  /**
   * The following properties are used in Int tests that do not have access to Config Service when
   * the properties are accessed. As part of CLOUDP-272997 the code paths will be checked to see if
   * the properties can be access through Config Service.
   */
  private static final List<String> INT_TEST_PROPERTIES =
      List.of(
          "mms.test.res.http.connectRequestTimeout",
          "mms.test.res.http.connectTimeout",
          "mms.test.res.http.socketTimeout");

  /**
   * The following properties all throw mismatch errors only in the "prod-gov" environment. This was
   * discovered late in the migration process.
   *
   * <p>Further investigation to occur as part of CLOUDP-272997.
   */
  private static final List<String> NON_CONFIG_SERVICE_PROD_GOV_APP_PROPS =
      List.of(
          "automation.agent.location",
          "brs.agent.location",
          "brs.client.pit.location",
          "mms.root.redirect",
          "monitoring.agent.location",
          "nds.aws.dns.domain",
          "nds.aws.dns.hostedzoneid");

  /**
   * The following properties are owned by SRE and managed with Chef. They are not set in conf files
   * since they are overridden by Chef.
   */
  private static final List<String> CHEF_DEFINED_APP_PROPS =
      List.of(
          MMS_HTTP_BINDHOSTNAME.value,
          MMS_HTTPS_PEMKEYFILE.value,
          "mongodb.ssl.PEMKeyFile",
          "mongodb.ssl.CAFile",
          "local.aws.accessKey",
          "local.aws.secretKey",
          "nds.reservedPorts.basePort",
          "nds.reservedPorts.maxPorts");

  /**
   * The Config Service does not support programmatically setting an application property. Legacy
   * properties that require that functionality are defined here.
   */
  private static final List<String> PROGRAMMATICALLY_TOGGLED_DATABASE_PROPERTIES =
      List.of(
          /* The following properties are programmatically set in application code */
          DRY_RUN_INTEGRATION_DELETION.value,
          METERING_USER_PRIVATE_KEY.value,
          METERING_USER_PUBLIC_KEY.value,
          NDS_DRY_RUN_MODE_ENABLED.value,
          NDS_PROXY_NA_REGEX_THROTTLING_MILLIS.value,
          NDS_PROXY_NA_REGEX_THROTTLING_WATCHED_NAMESPACES.value,
          NEXT_PROJECT_FOR_INTEGRATION_MIGRATION.value,
          "nds.azure.billingReport.lastSuccessfulDate",
          "nds.aws.billingReport.lastImportDate",
          "mms.atlas.encryptionAtRest.kmsShutdownOnValidationFailure.enabled",
          /* The following properties are programmatically set in feature tests */
          "brs.thirdparty.baseOplogFilePath",
          Fields.THIRD_PARTY_EXTRA_LOGGING_ENABLED.value,
          "mms.backup.wtcheckpoint.archiveMaxSize",
          "mms.backup.wtcheckpoint.minFilesForSmallFileOptimization",
          "mms.backup.wtcheckpoint.smallFileSizeLimit",
          "brs.useAwsSdkV2",
          "mms.cps.exportWithThreeNodesPerShardThresholdGB",
          "nds.admin.dataPlaneAccessRequestsOnlineValidation",
          "nds.admin.reasonsRequireJira",
          "nds.embeddedConfig.maxShardCount",
          "nds.embeddedConfig.rsDirectToEmbeddedMinMongoDBVersion",
          "nds.mtm.maxReservedTenants.m0",
          "nds.mtm.sentinel.cron.enabled",
          "nds.serverless.sentinel.organization.id",
          "nds.shared.sentinel.organization.id",
          "nds.flex.sentinel.organization.id",
          "oa.dlzTTLService.aws.ttlDays",
          "oa.dlzTTLService.azure.ttlDays",
          "oa.dlzTTLService.gcp.ttlDays",
          "oa.s3.inventory.configId.override",
          "mms.atlas.encryptionAtRest.kmsShutdownOnValidationFailure.threshold",
          /*
           * The following properties are programmatically set in integration tests and did not
           * support switching over to setProp with a SettingType parameter of MEMORY
           */
          "mms.backup.cutoverDbEnabled",
          "mms.backup.cutoverDbRefreshIntervalSeconds");

  /**
   * List of application properties that for one reason or another cannot leverage the Config
   * Service. The bulk of this list is populated with values that are required to bootstrap MMS and
   * Config Service. Other groupings of properties are called out in follow-up comments.
   *
   * <p>There is a follow-up ticket (CLOUDP-272997) to further examine if these properties can use
   * the Config Service with some code tweaks.
   */
  public static final List<String> NON_CONFIG_SERVICE_APP_PROPS =
      Stream.of(
              CHEF_DEFINED_APP_PROPS,
              BASE_NON_CONFIG_APP_PROPS,
              INT_TEST_PROPERTIES,
              NON_CONFIG_SERVICE_PROD_GOV_APP_PROPS,
              PROGRAMMATICALLY_TOGGLED_DATABASE_PROPERTIES,
              SECRET_PROPERTIES.stream().toList())
          .flatMap(List::stream)
          .toList();

  public final boolean readOnly;

  @Inject private ConfigServiceSdkWrapper configServiceSdkWrapper;

  public Optional<String> getSudoUser() {
    return ofNullable(getStrProp("nds.liveImport.sudoUser", null));
  }

  public long getMongoStartTimeout() {
    return getLongProp("brs.daemon.mongodStartTimeoutMs", Duration.ofSeconds(300).toMillis());
  }

  public long getMongoStopTimeout() {
    return getLongProp("brs.daemon.mongodStopTimeoutMs", Duration.ofMinutes(30).toMillis());
  }

  public long getQueryableMongoStartTimeout() {
    return getLongProp("brs.daemon.queryableMongodStartTimeoutMs", 28_800_000);
  }

  public int getBackupS3WriteTimeoutMS() {
    return getIntProp("brs.s3WriteTimeoutMS", ClientConfiguration.DEFAULT_REQUEST_TIMEOUT);
  }

  public int getBackupS3ConnectionTimeoutMS() {
    return getIntProp("brs.s3ConnectionTimeoutMS", ClientConfiguration.DEFAULT_CONNECTION_TIMEOUT);
  }

  public int getBackupS3SocketTimeoutMS() {
    return getIntProp("brs.s3SocketTimeoutMS", ClientConfiguration.DEFAULT_SOCKET_TIMEOUT);
  }

  public int getBackupGroomS3BlockWriteTimeoutMS() {
    return getIntProp("brs.groom.s3BlockWriteTimeoutMS", getBackupS3WriteTimeoutMS());
  }

  public int getBackupGroomS3BlockConnectionTimeoutMS() {
    return getIntProp("brs.groom.s3BlockConnectionTimeoutMS", getBackupS3ConnectionTimeoutMS());
  }

  public int getBackupGroomS3BlockSocketTimeoutMS() {
    return getIntProp("brs.groom.s3BlockSocketTimeoutMS", getBackupS3SocketTimeoutMS());
  }

  public int getBackupWTS3BlockWriteTimeoutMS() {
    return getIntProp("brs.wtcheckpoint.s3BlockWriteTimeoutMS", getBackupS3WriteTimeoutMS());
  }

  public int getBackupWTS3BlockConnectionTimeoutMS() {
    return getIntProp(
        "brs.wtcheckpoint.s3BlockConnectionTimeoutMS", getBackupS3ConnectionTimeoutMS());
  }

  public int getBackupWTS3BlockSocketTimeoutMS() {
    return getIntProp("brs.wtcheckpoint.s3BlockSocketTimeoutMS", getBackupS3SocketTimeoutMS());
  }

  public Optional<DataRegionConfig> getDataRegionConfig() {
    return Optional.ofNullable(_dataRegionConfig);
  }

  public int getBackupMinFilesRequiredForSmallFileOptimization() {
    return getIntProp(BACKUP_MIN_FILES_FOR_SMALL_FILE_OPTIMIZATION.value, 200);
  }

  public long getBackupSmallFileSizeLimit() {
    return getLongProp(BACKUP_SMALL_FILE_SIZE_LIMIT.value, 1024 * 1024);
  }

  public boolean getBackupSkipBuildIncrementalDiffForSmallFiles() {
    return getBoolProp(
        "mms.backup.wtcheckpoints.dataBlocks.skipBuildIncrementalDiffForSmallFiles", false);
  }

  public boolean getBackupSnapshotValidationJobRunnerEnabled() {
    return getBoolProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_ENABLED.value, false);
  }

  public int getBackupSnapshotValidationJobRunnerFrequencyInMinutes() {
    return getIntProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_FREQUENCY_IN_MINUTES.value, 30);
  }

  public int getBackupSnapshotValidationKubernetesRetryLimit() {
    return getIntProp(Fields.BACKUP_SNAPSHOT_VALIDATION_KUBERNETES_RETRY_LIMIT.value, 1);
  }

  public int getBackupSnapshotValidationJobRunnerTtlAfterCompleteInSeconds() {
    return getIntProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_TTL_AFTER_COMPLETE_IN_SECONDS.value, 60);
  }

  public boolean getBackupSnapshotValidationJobRunnerDeleteInProgressSecrets() {
    return getBoolProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_DELETE_IN_PROGRESS_SECRETS.value, false);
  }

  public boolean getBackupSnapshotValidationJobRunnerPurgeAllRunningJobs() {
    return getBoolProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_PURGE_ALL_RUNNING_JOBS.value, false);
  }

  public int getBackupSnapshotValidationJobRunnerMaxAllowedSizeInGb() {
    return getIntProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_MAX_ALLOWED_SIZE_IN_GB.value, 0);
  }

  public boolean getBackupSnapshotValidationJobRunnerTargetMinikube() {
    return getBoolProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_TARGET_MINIKUBE.value, false);
  }

  public boolean getBackupSnapshotValidationJobRunnerDeleteSecrets() {
    return getBoolProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_DELETE_SECRETS.value, true);
  }

  public boolean getBackupSnapshotValidationJobRunnerDeletePersistentVolumeClaims() {
    return getBoolProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_DELETE_PERSISTENT_VOLUME_CLAIMS.value, true);
  }

  public int getBackupSnapshotValidationJobRunnerRunningJobLimit() {
    return getIntProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_RUNNING_JOB_LIMIT.value, 2);
  }

  public String getBackupSnapshotValidationKubernetesNamespace() {
    return getStrProp(Fields.BACKUP_SNAPSHOT_VALIDATION_KUBERNETES_NAMESPACE.value, "default");
  }

  public String getBackupSnapshotValidationKubernetesContext() {
    return getStrProp(Fields.BACKUP_SNAPSHOT_VALIDATION_KUBERNETES_CONTEXT.value, null);
  }

  public boolean getBackupSnapshotValidationRunnerUseDummyImage() {
    return getBoolProp(Fields.BACKUP_SNAPSHOT_VALIDATION_RUNNER_USE_DUMMY_IMAGE.value, false);
  }

  public boolean getBackupSnapshotValidationRunnerUseLocalImage() {
    return getBoolProp(Fields.BACKUP_SNAPSHOT_VALIDATION_RUNNER_USE_LOCAL_IMAGE.value, false);
  }

  public String getBackupSnapshotValidationPinnedImageVersion() {
    return getStrProp(Fields.BACKUP_SNAPSHOT_VALIDATION_PINNED_IMAGE_VERSION.value, "");
  }

  public String getBackupSnapshotValidationIngestionEndpointForDevelopment() {
    return getStrProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_INGESTION_ENDPOINT_FOR_DEVELOPMENT.value, "");
  }

  public String getBackupSnapshotValidationSecretVolumeMountPoint() {
    return getStrProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_SECRET_VOLUME_MOUNT_POINT.value, "/etc/secret-volume");
  }

  public String getBackupSnapshotValidationDataVolumeMountPoint() {
    return getStrProp(Fields.BACKUP_SNAPSHOT_VALIDATION_DATA_VOLUME_MOUNT_POINT.value, "/etc/data");
  }

  public int getBackupSnapshotValidationJobUnhealthyThresholdInSeconds() {
    return getIntProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_UNHEALTHY_THRESHOLD_IN_SECONDS.value, 5 * 60);
  }

  public boolean getBackupSnapshotValidationJobDryRunEnabled() {
    return getBoolProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_DRY_RUN_ENABLED.value, false);
  }

  public boolean getBackupSnapshotValidationJobDelayCompletionEnabled() {
    return getBoolProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_DELAY_COMPLETION_ENABLED.value, false);
  }

  public int getBackupSnapshotValidationDownloaderFileThreadPoolSize() {
    return getIntProp(Fields.BACKUP_SNAPSHOT_VALIDATION_DOWNLOADER_FILE_THREAD_POOL_SIZE.value, 4);
  }

  public int getBackupSnapshotValidationDownloaderBlockThreadPoolSize() {
    return getIntProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_DOWNLOADER_BLOCK_THREAD_POOL_SIZE.value, 16);
  }

  public int getBackupSnapshotValidationDownloaderBlockQueueSize() {
    return getIntProp(Fields.BACKUP_SNAPSHOT_VALIDATION_DOWNLOADER_BLOCK_QUEUE_SIZE.value, 32);
  }

  public int getBackupSnapshotValidationDownloaderPipeSizeInMegabytes() {
    return getIntProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_DOWNLOADER_PIPE_SIZE_IN_MEGABYTES.value, 160);
  }

  public int getBackupSnapshotValidationJVMHeapSizeInGigabytes() {
    return getIntProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JVM_MAX_HEAP_SPACE_IN_GIGABYTES.value, 4);
  }

  public int getBackupSnapshotValidationJobHttpClientConnectionTimeoutSecs() {
    return getIntProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_HTTP_CLIENT_CONNECTION_TIMEOUT_IN_SECONDS.value, 300);
  }

  public int getBackupSnapshotValidationJobHttpClientMaxRetryAttempts() {
    return getIntProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_HTTP_CLIENT_MAX_RETRY_ATTEMPTS.value, 10);
  }

  public int getBackupSnapshotValidationJobMongoManagerConnectTimeoutMS() {
    return getIntProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_MONGO_MANAGER_CONNECT_TIMEOUT_IN_MS.value, 1_800_000);
  }

  public int getBackupSnapshotValidationJobMongoManagerStopTimeoutMS() {
    return getIntProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_MONGO_MANAGER_STOP_TIMEOUT_IN_MS.value, 10_000);
  }

  public int getBackupSnapshotValidationJobDelayCompletionInSeconds() {
    return getIntProp(
        Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_DELAY_COMPLETION_IN_SECONDS.value, 30 * 60);
  }

  public boolean getBackupSnapshotValidationJobPreSignedUrlEnabled() {
    return getBoolProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_PRE_SIGNED_URL_ENABLED.value, true);
  }

  public int getSnapshotValidationjobsMetricsLookbackSecs() {
    return getIntProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_METRICS_LOOKBACK_SECS.value, 60);
  }

  public boolean disableNamespaceFilteringWithDirectoryPerDBOrIndexesValidation() {
    return getBoolProp(
        "mms.backup.restore.disableNsFilteringWithDirectoryPerDBOrIndexesValidation", false);
  }

  public boolean isBackupOplogStoreDualWriteEnabled() {
    return getBoolProp("mms.backup.oplogStore.dualWriteEnabled", false);
  }

  public boolean isCheckExtraMaintenanceEnabled() {
    return getBoolProp("mms.checkExtraMaintenanceEnabled", false);
  }

  public boolean isVoyageMetricsEnabled() {
    return getBoolProp("mms.voyage.metrics.enabled", false);
  }

  public String getDefaultPropFilePath() {
    return this._defaultPropFilePath;
  }

  public List<String> getOverridePropFilePaths() {
    return this._overridePropFilePaths;
  }

  public boolean isOrgTagsEnabled() {
    return getBoolProp("mms.organization.tags.ui.enabled", false);
  }

  @SuppressWarnings("unchecked")
  @Override
  public <T> T get(Class<T> cls, String key) {
    if (cls != Secret.class) {
      return super.get(cls, key);
    }
    return (T) getSecret(key);
  }

  @Override
  public <T> T get(Class<T> cls, String key, T defaultValue) {
    if (cls != Secret.class) {
      return super.get(cls, key, defaultValue);
    }
    throw new UnsupportedOperationException(
        "Default values are not supported for Secret property evaluation, key " + key);
  }

  @VisibleForTesting
  Secret getSecret(String secretKey) {
    if (!hasProp(secretKey)) {
      throw new NoSuchElementException(
          String.format("No secret found for this property: %s", secretKey));
    }

    return new Secret(getString(secretKey));
  }

  public enum SettingType {
    EFFECTIVE,
    DATABASE,
    PROPERTIES_FILE,
    SYSTEM,
    MEMORY,
    ENV,
  }

  public enum LifeCycleState {
    STARTUP,
    RUNNING,
    STOPPING
  }

  private volatile int _settingsVersion;

  private final CountDownLatch _runningLatch = new CountDownLatch(1);
  private volatile boolean _routingPingsToQueue = false;
  private volatile LifeCycleState _lifeCycleState = LifeCycleState.STARTUP;

  public AppSettings() {
    this(true);
  }

  private Properties _migrationProperties;
  private final boolean standaloneMigrationMode;

  public static AppSettings forStandaloneMigration() {
    return new AppSettings(false, true, false, AppSysPropProvider.getAppEnv());
  }

  private Properties getMongoClientConfigProperties(final Properties pProps) {
    if (standaloneMigrationMode) {
      // Only use the property keys from the migration properties file
      final Properties migrationProps = new Properties();
      _migrationProperties.keySet().forEach((key) -> migrationProps.put(key, pProps.get(key)));
      return migrationProps;
    }
    return pProps;
  }

  public AppSettings(final boolean useDatabaseProperties) {
    this(useDatabaseProperties, false);
  }

  /**
   * Constructor that is currently only used for testing purposes and shouldn't have to be used
   * outside of it
   */
  @com.mongodb.internal.VisibleForTesting(otherwise = AccessModifier.PRIVATE)
  public AppSettings(
      final boolean useDatabaseProperties, ConfigServiceSdkWrapper configServiceSdkWrapper) {
    this(useDatabaseProperties, false);
    this.configServiceSdkWrapper = configServiceSdkWrapper;
  }

  public AppSettings(final boolean useDatabaseProperties, final boolean readOnly) {
    this(useDatabaseProperties, readOnly, AppSysPropProvider.getAppEnv());
  }

  public AppSettings(
      final boolean useDatabaseProperties, final boolean readOnly, final AppEnv appEnv) {
    this(useDatabaseProperties, false, readOnly, appEnv);
  }

  public AppSettings(
      final boolean useDatabaseProperties,
      final boolean standaloneMigrationMode,
      final boolean readOnly,
      final AppEnv appEnv) {
    this.readOnly = readOnly;
    this.standaloneMigrationMode = standaloneMigrationMode;
    this._appEnv = appEnv;
    _appId = AppSysPropProvider.getAppId();
    if (_appId == null) {
      throw new IllegalStateException(Property.APP_ID + " is null - set to continue");
    }
    _settingsVersion = 0;
    _cachedDbProperties = new AtomicReference<>();

    final AppSettingsPropertyFilePaths appSettingsPropertyFilePaths =
        new AppSettingsPropertyFilePaths(appEnv);

    this._defaultPropFilePath = appSettingsPropertyFilePaths.getEnvConfPath();
    _defaultFileProperties =
        initializeFileProperties(Collections.singletonList(this._defaultPropFilePath));
    if (_defaultFileProperties.isEmpty()) {
      throw new IllegalStateException(
          String.format(
              "Required default properties file %s not found.", this._defaultPropFilePath));
    }

    warnOnMissingUrl(appSettingsPropertyFilePaths.getAppOverrideConfPath(), "mongo.mongoUri");

    _memoryProperties = Collections.synchronizedMap(new HashMap<>());
    _region = SysProp.strOrNull(Property.MMS_REGION_NAME);

    this._overridePropFilePaths = appSettingsPropertyFilePaths.getAllOverrideConfPaths();

    _overrideFileProperties = initializeFileProperties(this._overridePropFilePaths);

    if (this.standaloneMigrationMode) {
      _migrationProperties =
          initializeFileProperties(appSettingsPropertyFilePaths.getMigrationConfPaths());
      _overrideFileProperties.putAll(_migrationProperties);
    }

    _overrideEnvProperties =
        initializeOverrideEnvProperties(_overrideFileProperties, _defaultFileProperties);

    propertyNamesOfSecrets.addAll(
        extractNamesOfSecretsFromPropertiesObject(_defaultFileProperties));
    propertyNamesOfSecrets.addAll(
        extractNamesOfSecretsFromPropertiesObject(_overrideFileProperties));

    initializeFIPSProvider();

    /*
     * TODO: The "initializeManagedSecrets" method returns an empty Properties object and overwrites
     * the "_defaultFileProperties" directly with secret values. This operation is effectively a
     * no-op.
     *
     * <p>This behavior was uncovered during the App Settings to Config Service migration, which was
     * completed under a time crunch. To avoid adding scope to this PR, the code remains unchanged,
     * and this comment serves as a note for future maintainers.
     */
    final Properties defaultManagedSecretProperties =
        initializeManagedSecrets(_overrideFileProperties, _defaultFileProperties);
    _defaultFileProperties.putAll(defaultManagedSecretProperties);

    initializeKrb5();

    // The configuration to connect to the backing database is described in AppSettings, but
    // AppSettings isn't initialized yet...
    // All the information required to make this connection must be supplied by some way
    // other than the database. All the getters in this class are guarded against the
    // database properties not yet being available.
    if (useDatabaseProperties) {
      _dbProperties = getAppPropertyDao();
      _dbPropertiesRefresher = new DatabasePropertiesRefresher();
      refreshDatabaseCache();

      final long refreshInterval =
          getLongProp(
              Fields.APPSETTINGS_DB_POLLING_INTERVAL.value, DB_CACHE_REFRESH_INTERVAL.toSeconds());
      _scheduledFuture =
          _scheduler.scheduleAtFixedRate(
              _dbPropertiesRefresher,
              refreshInterval + getRandomNumber(Math.toIntExact(refreshInterval)),
              refreshInterval,
              TimeUnit.SECONDS);
      if (!getAppEnv().isGovCloud()) {
        _dataRegionConfig = lookupDataRegionConfigForRegion(_region).orElse(null);
      } else {
        _dataRegionConfig = null;
      }
    } else {
      _dataRegionConfig = null;
      _dbProperties = null;
      _dbPropertiesRefresher = null;
      _scheduledFuture = null;
    }

    initializeHTTPProxy();

    _globalAlertsSummaryEmailIntervalHours = loadGlobalAlertsSummaryEmailIntervalHours();

    PromMetricsSvc.setGauge(
        CODE_VERSION_GAUGE, 1, getVersionInfo().getShortGitHash(), getVersionInfo().versionString);

    setConversionHandler(createConversionHandler());
  }

  private Properties initializeManagedSecrets(
      final Properties fileOverrideProperties, final Properties managedProperties) {
    final Properties managedSecretProperties = new Properties();
    final boolean isAwsSecretManagerEnabled =
        !getBoolProp(AWS_SECRET_MANAGER_DISABLED_PROPERTY, false, SettingType.SYSTEM);

    if (isAwsSecretManagerEnabled) {
      LOG.info("Setting up managed secrets for environment {}", _appEnv);

      final String defaultSecretRegion =
          managedProperties.getProperty("local.aws.secretsRegion", "us-east-1");
      final String[] awsRegions =
          fileOverrideProperties
              .getProperty("local.aws.secretsRegion", defaultSecretRegion)
              .split(COMMA_DELIMITER);

      final String defaultPrimaryAwsRegion =
          managedProperties.getProperty("local.aws.secretsPrimaryRegion");
      final String primaryAwsRegion =
          fileOverrideProperties.getProperty(
              "local.aws.secretsPrimaryRegion", defaultPrimaryAwsRegion);

      final String[] awsSecretRegions =
          AwsManagedSecrets.filterAwsRegionsForSecrets(awsRegions, primaryAwsRegion);
      final String awsAccess = fileOverrideProperties.getProperty("local.aws.accessKey");
      final String awsSecret = fileOverrideProperties.getProperty("local.aws.secretKey");

      LOG.info(
          "{} are available regions for managed secrets for environment {}",
          awsSecretRegions,
          _appEnv);
      // Temporary hack to allow secrets setup for local gov. Will be removed in
      // CLOUDP-62581 once secrets for GOV environments are created
      final String secretPrefix = this.getSecretPrefix();

      if (awsAccess == null || awsSecret == null) {
        LOG.warn(
            "Could not load managed secrets - this environment will not be fully functional. See"
                + " https://wiki.corp.mongodb.com/x/wg3PBQ.");
      } else {
        LOG.info("Loading managed secrets for environment {}", _appEnv);
        final var managedSecrets =
            new AwsManagedSecrets(awsSecretRegions, awsAccess, awsSecret, secretPrefix);
        var batch = getBoolProp(MMS_APPSETTINGS_FETCH_SECRETS_IN_BATCH.value, false);
        var beforeFetch = System.currentTimeMillis();
        var methodLabelValue = batch ? "batch" : "serial";
        var timer = SECRETS_FETCH_LOAD_TIME.labels(methodLabelValue).startTimer();
        try {
          if (batch) {
            fetchSecretsInBatch(managedProperties, managedSecrets);
          } else {
            fetchSecretsSerially(managedProperties, managedSecrets);
          }
          SECRETS_FETCH_COUNT.labels(methodLabelValue, "none").inc();
        } catch (Exception e) {
          SECRETS_FETCH_COUNT.labels(methodLabelValue, e.getClass().getSimpleName()).inc();
          throw e;
        } finally {
          timer.observeDuration();
        }
        LOG.info(
            "Finished setting up managed secrets {} for environment {} in {}ms",
            batch ? "in batch" : "serially",
            _appEnv,
            System.currentTimeMillis() - beforeFetch);
      }
    }
    return managedSecretProperties;
  }

  /** fetch secrets, properties with a value of a secrets marker placeholder, one by one */
  private void fetchSecretsSerially(
      final Properties managedProperties, final AwsManagedSecrets managedSecrets) {
    managedProperties.forEach(
        (key, value) -> {
          if (StringUtils.equals(SECRET_MANAGER_STRING, (String) value)) {
            final String secret = managedSecrets.getSecret((String) key);
            managedProperties.put(key, secret);
          }
        });
  }

  /** fetch secrets, properties with a value of a secrets marker placeholder, in batch */
  private void fetchSecretsInBatch(
      final Properties managedProperties, final AwsManagedSecrets managedSecrets) {
    var names =
        managedProperties.entrySet().stream()
            .filter(e -> Objects.equals(SECRET_MANAGER_STRING, e.getValue()))
            .map(e -> (String) e.getKey())
            .collect(Collectors.toSet());
    managedProperties.putAll(managedSecrets.getSecrets(names));
  }

  /**
   * Retrieves the AWS Secrets Manager secret prefix based on current application environment.
   *
   * <ul>
   *   <li>If the environment is {@code AppEnv.LOCAL_GOV}, the prefix uses the {@code AppEnv.LOCAL}
   *       prefix. In other environments the prefixes matches the {@code _appEnv} value.
   *   <li>The prefix is formatted as {@code {envCode}/mms/}.
   * </ul>
   *
   * @return the secret prefix used to identify and fetch managed secrets.
   */
  public String getSecretPrefix() {
    final String appEnvCode =
        _appEnv.equals(AppEnv.LOCAL_GOV) ? AppEnv.LOCAL.getCode() : _appEnv.getCode();
    return String.format("%s/mms/", appEnvCode);
  }

  /**
   * Logs a warning message about propertyName if the provided url is unreadable.
   *
   * @param url the full url including protocol
   * @param propertyName the name of the property that a warning should appear for
   */
  private void warnOnMissingUrl(final String url, final String propertyName) {
    if (!UrlReadingUtils.urlIsReadable(url)) {
      LOG.warn(
          "Unable to read from {}. {} being set to {}",
          url,
          propertyName,
          _defaultFileProperties.getProperty(propertyName));
    }
  }

  /**
   * Populates the Config Service SDK store and starts the polling to Config Service for updates.
   * This method MUST be called to be able to fetch values via the Config Service SDK.
   *
   * <p>ConfigServiceSdkWrapper either has to be available to AppSettings via Guice injection or
   * passed in explicitly via the startConfigService(sdkWrapper) overloaded method.
   */
  public synchronized void startConfigService() throws Exception {
    configServiceSdkWrapper.start();
  }

  /**
   * Populates the Config Service SDK store and starts the polling to Config Service for updates.
   * This method MUST be called to be able to fetch values via the Config Service SDK.
   *
   * <p>This version allows a manually constructed ConfigServiceSdkWrapper to be passed in and used.
   */
  public synchronized void startConfigService(ConfigServiceSdkWrapper sdkWrapper) throws Exception {
    if (configServiceSdkWrapper == null) {
      configServiceSdkWrapper = sdkWrapper;
    } else {
      LOG.warn("Using AppSettings's existing instance of the SDK");
    }

    startConfigService();
  }

  private int loadGlobalAlertsSummaryEmailIntervalHours() {
    final int interval =
        getIntProp(Fields.MMS_ALERTS_GLOBAL_SUMMARY_EMAIL_INTERVAL_HOURS.value, 24);
    if (interval < 1 || interval > 24) {
      LOG.warn(
          "{} value {} is not in the range 1 through 24, using 24 instead",
          Fields.MMS_ALERTS_GLOBAL_SUMMARY_EMAIL_INTERVAL_HOURS.value,
          interval);
      return 24;
    } else if (24 % interval == 0) {
      return interval;
    } else {
      final int nearestFactor;
      if (interval == 5) {
        nearestFactor = 4;
      } else if (interval == 7) {
        nearestFactor = 6;
      } else if (interval == 9 || interval == 10) {
        nearestFactor = 8;
      } else if (interval >= 11 && interval <= 18) {
        nearestFactor = 12;
      } else {
        nearestFactor = 24;
      }
      LOG.warn(
          "{} value {} does not divide 24, using {} instead",
          Fields.MMS_ALERTS_GLOBAL_SUMMARY_EMAIL_INTERVAL_HOURS.value,
          interval,
          nearestFactor);
      return nearestFactor;
    }
  }

  /**
   * Get the current version of the settings for this instance. The version will start at zero and
   * increment every time any setting changes. There are no calls that provide a setting value and
   * version in an atomic matter. The following pattern should be used if you want to cache a
   * setting.
   *
   * <p>if (_value != null && _version == settings.getSettingsVersion()) { return _value; } _version
   * = settings.getSettingsVersion(); _value = settings.getProp("Test"); return _value;
   *
   * <p>This will guarantee that the cached version is not newer then the cached setting, but it is
   * possible the next call will reload the cached value unnecessarily a value was changed between
   * the two gets.
   */
  public int getSettingsVersion() {
    return _settingsVersion;
  }

  public void clearMemory() {
    if (!this.getAppEnv().equals(AppEnv.TEST)) {
      throw new IllegalStateException("Clear is only supported for unit testing.");
    }

    synchronized (this) {
      _memoryProperties.clear();
      _settingsVersion++;
    }
  }

  @Override
  public void close() {
    shutdown();
  }

  @PreDestroy
  public void shutdown() {
    _scheduler.shutdownNow();
    try {
      if (nonNull(_dbProperties)) {
        _dbProperties.closeMongo();
      }
    } catch (final Throwable t) {
      LOG.debug("shutdown error", t);
    }
  }

  private AppPropertyDao getAppPropertyDao() {
    final MongoClientConfigCache dbConfigs = getDbConfigs();
    if (dbConfigs.getConfig(AppPropertyDao.DB_NAME) == null) {
      throw new RuntimeException(
          "Could not find configuration to connect to App Settings Database.");
    }

    try {
      final MongoClient legacyClient = getMongoLegacyClient(AppPropertyDao.DB_NAME);
      final com.mongodb.client.MongoClient client = getMongoClient(AppPropertyDao.DB_NAME);
      final MongoClient cutoverLegacyClient = getCutoverMongoLegacyClient(AppPropertyDao.DB_NAME);
      final com.mongodb.client.MongoClient cutoverClient =
          getCutoverMongoClient(AppPropertyDao.DB_NAME);
      return new AppPropertyDao(
          new SingleClientMongoSvc(legacyClient, client, cutoverLegacyClient, cutoverClient));
    } catch (final RuntimeException e) {
      throw new RuntimeException("Failed to initialize connection to App Settings Database.", e);
    }
  }

  /**
   * If you need to call this outside of AppSettings, please use the DataProcessingRegionSvc so as
   * to avoid creating MongoClients on demand.
   */
  @VisibleForTesting
  public Optional<DataRegionConfig> lookupDataRegionConfigForRegion(final String pRegion) {
    try (final MongoClient legacyClient =
            getMongoLegacyClient(DataRegionConfigDao.CONNECTION_NAME);
        final com.mongodb.client.MongoClient client =
            getMongoClient(DataRegionConfigDao.CONNECTION_NAME);
        final MongoClient cutoverLegacyClient =
            getCutoverMongoLegacyClient(DataRegionConfigDao.CONNECTION_NAME);
        final com.mongodb.client.MongoClient cutoverClient =
            getCutoverMongoClient(DataRegionConfigDao.CONNECTION_NAME)) {
      if (client == null) {
        return Optional.empty();
      }
      final DataRegionConfigDao dataRegionConfigDao =
          new DataRegionConfigDao(
              new SingleClientMongoSvc(legacyClient, client, cutoverLegacyClient, cutoverClient),
              new CodecRegistryProvider().get());
      final Optional<DataRegionConfig> dataRegionConfig =
          dataRegionConfigDao.getConfigForRegion(pRegion);
      dataRegionConfig.ifPresent(
          regionConfig ->
              regionConfig.addSSLContext(
                  ClientConfigCache.getSslContext(
                      MongoClientConfigCache.SSL_PREFIX,
                      getAllPropertiesWithoutConfigService(false))));
      return dataRegionConfig;
    } catch (final MongoException e) {
      LOG.error(
          "Mongo error getting a data region config: {}", LogUtils.entries("region", pRegion), e);
      return Optional.empty();
    }
  }

  public Set<String> getConfiguredRegions() {
    try (final MongoClient legacyClient =
            getMongoLegacyClient(DataRegionConfigDao.CONNECTION_NAME);
        final com.mongodb.client.MongoClient client =
            getMongoClient(DataRegionConfigDao.CONNECTION_NAME);
        final MongoClient cutoverLegacyClient =
            getCutoverMongoLegacyClient(DataRegionConfigDao.CONNECTION_NAME);
        final com.mongodb.client.MongoClient cutoverClient =
            getCutoverMongoClient(DataRegionConfigDao.CONNECTION_NAME)) {

      return Optional.ofNullable(client)
          .map(
              c ->
                  new DataRegionConfigDao(
                      new SingleClientMongoSvc(
                          legacyClient, client, cutoverLegacyClient, cutoverClient),
                      new CodecRegistryProvider().get()))
          .map(DataRegionConfigDao::getAllRegions)
          .orElse(Set.of());
    } catch (final MongoException e) {
      LOG.error("Mongo error getting configured regions", e);
      return Set.of();
    }
  }

  public MongoClientConfigCache getDbConfigs() {
    final Properties pProps = getAllPropertiesWithoutConfigService(false);
    final var sslContext =
        ClientConfigCache.getSslContext(MongoClientConfigCache.SSL_PREFIX, pProps);
    return MongoClientConfigCache.from(getMongoClientConfigProperties(pProps), sslContext);
  }

  public boolean isMongoClientAwsWorkloadIdentityEnabled() {
    // return getBoolProp("mms.mongo.workloadIdentity.enabled", false);
    return false;
  }

  private MongoClient createLegacyMongoClient(final MongoClientURI uri) {
    return isMongoClientAwsWorkloadIdentityEnabled()
        ? MongoClientUtils.tryAwsAuthLegacyClient(
            MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(uri.toString()))
                .build())
        : new MongoClient(uri);
  }

  @VisibleForTesting
  MongoClient getMongoLegacyClient(final String dbName) {
    final MongoClientConfigCache dbConfigs = getDbConfigs();
    if (dbConfigs.getConfig(dbName) == null) {
      LOG.error(
          "Unable to get a Mongo client configuration when looking up data region: {}",
          LogUtils.entries("dbName", dbName));
      return null;
    }
    return createLegacyMongoClient(getUri(dbConfigs.getConfig(dbName)));
  }

  @VisibleForTesting
  com.mongodb.client.MongoClient getMongoClient(final String dbName) {
    final MongoClientConfigCache dbConfigs = getDbConfigs();
    if (dbConfigs.getConfig(dbName) == null) {
      LOG.error(
          "Unable to get a Mongo client configuration when looking up data region: {}",
          LogUtils.entries("dbName", dbName));
      return null;
    }
    final MongoClientSettings mongoClientSettings =
        new MongoClientSettingsBuilder(dbConfigs.getConfig(dbName)).build();
    return isMongoClientAwsWorkloadIdentityEnabled()
        ? MongoClientUtils.tryAwsAuthClient(mongoClientSettings)
        : MongoClients.create(mongoClientSettings);
  }

  @VisibleForTesting
  @Nullable
  MongoClient getCutoverMongoLegacyClient(final String dbName) {
    final MongoClientConfigCache dbConfigs = getDbConfigs();
    if (dbConfigs.getConfig(dbName) == null) {
      LOG.error(
          "Unable to get a Mongo client configuration when looking up data region: {}",
          LogUtils.entries("dbName", dbName));
      return null;
    }
    final MongoClientURI cutoverUri = getCutoverUri(dbConfigs.getConfig(dbName));
    if (cutoverUri == null) {
      return null;
    }
    return createLegacyMongoClient(cutoverUri);
  }

  @VisibleForTesting
  @Nullable
  com.mongodb.client.MongoClient getCutoverMongoClient(final String dbName) {
    final MongoClientConfigCache dbConfigs = getDbConfigs();
    if (dbConfigs.getConfig(dbName) == null) {
      LOG.error(
          "Unable to get a Mongo client configuration when looking up data region: {}",
          LogUtils.entries("dbName", dbName));
      return null;
    }
    final ClientConfig config = dbConfigs.getConfig(dbName);
    if (config.getCutoverUriString() == null) {
      return null;
    }
    final MongoClientSettings mongoClientSettings = new MongoClientSettingsBuilder(config).build();
    return new com.xgen.cloud.common.driverwrappers._public.sync.MongoClient(
        MongoClients.create(new MongoClientSettingsBuilder(config, true).build()),
        mongoClientSettings,
        config.getCutoverUriString());
  }

  /**
   * ResolvedProperty.Source value is only used in private methods. We want to test the correct
   * source attribute is set in the "getPropOutsideOfConfigService" method.
   *
   * <p>This method should only be used in tests.
   */
  @com.mongodb.internal.VisibleForTesting(otherwise = AccessModifier.PRIVATE)
  public ResolvedProperty onlyUseForTestingGetProp(
      final String pName, final SettingType pSettingType) {
    if (!_appEnv.isTest()) {
      throw new UnsupportedOperationException(
          "This method is only available for testing purposes.");
    }
    return getPropWithoutConfigService(pName, pSettingType);
  }

  /**
   * This will only apply to the HOST measurement alerts, since none of the alert reads apply to the
   * DATABASE measurement alerts
   */
  static final String HOST_METRICS_ALERT_READ_MODE_APP_PROPERTY_KEY =
      "monitoring.hosts.metrics.alert.mode";

  public enum HostMeasurementAlertReadMode {
    READ_BOTH,
    READ_DEST_ONLY,
    READ_SRC_ONLY
  }

  /**
   * return READ_BOTH by default if no value set, or wrong value app property supplied
   *
   * <p>before writes cutover can be set to READ_SRC_ONLY 25 mins after cut over can be set to
   * READ_DEST_ONLY
   *
   * <p>right after dual reads enabled, READ_SRC_ONLY can be set if any impact on the alerts
   */
  public HostMeasurementAlertReadMode getHostMeasurementAlertMode() {
    //
    return getEnumProperty(
            HostMeasurementAlertReadMode.class,
            HOST_METRICS_ALERT_READ_MODE_APP_PROPERTY_KEY,
            READ_BOTH.toString())
        .orElse(READ_BOTH);
  }

  private class DatabasePropertiesRefresher implements Runnable {

    @Override
    public synchronized void run() {
      try {
        final Properties properties = getDBProperties();
        if (!properties.equals(_cachedDbProperties.get())) {
          final int settingsVersion;
          synchronized (AppSettings.this) {
            updateDBPollingIntervalIfNeeded(properties);
            _cachedDbProperties.set(properties);
            MongoClientRuntimeSettingsCache.update(properties);
            _settingsVersion++;
            settingsVersion = _settingsVersion;
          }
          LOG.debug(
              "Updated view of DB properties with {} entries, "
                  + "local JVM settings version counter is now {}",
              properties.size(),
              settingsVersion);
        }
      } catch (final Throwable t) {
        LOG.warn("Failed to refresh database properties", t);
      }
    }
  }

  protected void updateDBPollingIntervalIfNeeded(final Properties pProperties) {
    final long cachedRefreshInterval =
        getLongProp(
            Fields.APPSETTINGS_DB_POLLING_INTERVAL.value, DB_CACHE_REFRESH_INTERVAL.toSeconds());
    final long updatedRefreshInterval =
        Optional.ofNullable(pProperties.getProperty(Fields.APPSETTINGS_DB_POLLING_INTERVAL.value))
            .map(Long::parseLong)
            .orElse(DB_CACHE_REFRESH_INTERVAL.toSeconds());
    if (cachedRefreshInterval != updatedRefreshInterval) {
      if (this._scheduledFuture != null) {
        this._scheduledFuture.cancel(true);
      }
      _scheduledFuture =
          _scheduler.scheduleAtFixedRate(
              _dbPropertiesRefresher,
              updatedRefreshInterval + getRandomNumber(Math.toIntExact(updatedRefreshInterval)),
              updatedRefreshInterval,
              TimeUnit.SECONDS);

      LOG.info(
          "Refreshed AppSettingsDBProperties schedulerExecutorService with an interval of {}"
              + " seconds",
          updatedRefreshInterval);
    }
  }

  @VisibleForTesting
  protected void updateMongoClientRuntimeSettings(final Properties pProperties) {
    StreamSupport.stream(
            spliteratorUnknownSize(pProperties.propertyNames().asIterator(), ORDERED), false)
        .map(prop -> (String) prop)
        .filter(prop -> Pattern.matches(CUTOVER_REGEX_PATTERN.pattern(), prop))
        .forEach(
            prop -> {
              Matcher matcher = CUTOVER_REGEX_PATTERN.matcher(prop);
              if (matcher.find()) {
                String cutoverDbConnectionNames = matcher.group(1);
                Set<String> dbConnectionNameSet = parseDbNames(cutoverDbConnectionNames);
                String cutoverCollectionNames = matcher.group(2);
                Set<String> cutoverCollectionNameSet = parseDbNames(cutoverCollectionNames);
                final String uri = trimToNull(pProperties.getProperty(prop));
                MongoClientRuntimeSettingsCache.updateCutoverProps(
                    dbConnectionNameSet, cutoverCollectionNameSet, prop, uri);
              }
            });
  }

  public boolean hasProp(final String pName) {
    final String value = getStrProp(pName, null);
    return value != null && !value.isEmpty();
  }

  public Properties getDefaultFileProperties() {
    return _defaultFileProperties;
  }

  public Properties getDBProperties() {
    final Properties result = new Properties();
    if (_dbProperties != null) {
      _dbProperties
          .getValues(APP_PROPERTY_GLOBAL_PREFIX, String.class)
          .forEach(
              (key1, value) -> {
                if (value == null) {
                  return;
                }

                final String key = key1.substring(APP_PROPERTY_GLOBAL_PREFIX.length());
                if (EncryptionUtils.isEncrypted(value)) {
                  try {
                    result.setProperty(key, EncryptionUtils.genDecryptStr(value));
                  } catch (final Exception e) {
                    throw new RuntimeException("Failed to decrypt value for setting: " + key, e);
                  }
                } else {
                  result.setProperty(key, value);
                }
              });
    }
    return result;
  }

  public String getSettingsVersionNo() {
    return (_dbProperties != null
            && _dbProperties.getStringValue(APP_PROPERTY_VERSION_NUMBER) != null)
        ? _dbProperties.getStringValue(APP_PROPERTY_VERSION_NUMBER)
        : "0";
  }

  public void incrementSettingsVersionNo(@Nullable final String versionNo) throws SvcException {
    // If the client does not pass in versionNo (i.e from webhook calls), use the latest value in
    // the DB
    String curValue = versionNo != null ? versionNo : getSettingsVersionNo();
    if (curValue.equals("0")) {
      _dbProperties.set(APP_PROPERTY_VERSION_NUMBER, "1");
      return;
    }

    DBObject updatedDoc =
        _dbProperties.incrementPropertyVersionNumber(APP_PROPERTY_VERSION_NUMBER, curValue);
    if (updatedDoc == null) {
      throw new SvcException(
          CommonErrorCode.VALIDATION_ERROR,
          "Conf settings properties have been changed, please refresh the page and try again");
    }
  }

  public void setSettingsVersionNo() {
    String curValue = _dbProperties.getStringValue(APP_PROPERTY_VERSION_NUMBER);
    int nextValue = (curValue != null ? Integer.parseInt(curValue) : 0) + 1;
    _dbProperties.set(APP_PROPERTY_VERSION_NUMBER, String.valueOf(nextValue));
  }

  public String getPropertyEnvName(final String pName) {
    return ENV_VAR_PREFIX + pName.toUpperCase().replaceAll("[.\\-/]", "_");
  }

  public String getDataRegion() {
    return _region;
  }

  public String getDataRegionOrCentral() {
    return _region == null ? "us-east-1" : _region;
  }

  public boolean isCentralRegion() {
    return _region == null || _region.equals("us-east-1") || _region.equals("us-east-2");
  }

  /*
   * Get the current state of all properties, bypassing any caching.
   *
   * Note: This method does not call to Config Service for properties' values, meaning overrides
   * from Config Service Admin UI would not take effect here.
   * Consider using {@code getProp} instead, which will leverage Config Service for property values.
   *
   * TODO(CLOUDP-300049): consider deprecating this method if possible.
   */
  public Properties getAllPropertiesWithoutConfigService(final boolean bypassCache) {
    final Properties result = new Properties();
    result.putAll(_defaultFileProperties);
    if (bypassCache) {
      result.putAll(getDBProperties());
    } else if (_cachedDbProperties.get() != null) {
      result.putAll(_cachedDbProperties.get());
    }
    result.putAll(_overrideFileProperties);
    result.putAll(System.getProperties());
    synchronized (_memoryProperties) {
      _memoryProperties.forEach(
          (key, value) -> {
            if (value == null) {
              result.remove(key);
            } else {
              result.setProperty(key, value);
            }
          });
    }
    result.putAll(_overrideEnvProperties);
    return result;
  }

  private void tryLoadProperty(final Properties result, final String pName) {
    if (result.containsKey(pName)) {
      return;
    }
    final String envName = getPropertyEnvName(pName);
    final String fromEnv = System.getenv(envName);
    if (fromEnv != null) {
      LOG.info("Property {} loaded from environment variable {}", pName, envName);
      result.setProperty(pName, fromEnv);
    }
  }

  private Properties initializeOverrideEnvProperties(
      final Properties overrideFileProperties, final Properties fileProperties) {
    LOG.info("Loading properties from environment");
    final Properties result = new Properties();
    for (final Enumeration<?> e = overrideFileProperties.propertyNames(); e.hasMoreElements(); ) {
      tryLoadProperty(result, (String) e.nextElement());
    }
    for (final Enumeration<?> e = fileProperties.propertyNames(); e.hasMoreElements(); ) {
      tryLoadProperty(result, (String) e.nextElement());
    }
    return result;
  }

  private void addInstanceStats(final Properties pProperties) {
    pProperties.put(
        "om.instance.runtime.availableProcessors",
        String.valueOf(Runtime.getRuntime().availableProcessors()));
    pProperties.put(
        "om.instance.runtime.maxMemory", String.valueOf(Runtime.getRuntime().maxMemory()));
    pProperties.put(
        "om.instance.runtime.freeMemory", String.valueOf(Runtime.getRuntime().freeMemory()));
    pProperties.put(
        "om.instance.runtime.totalMemory", String.valueOf(Runtime.getRuntime().totalMemory()));
  }

  /**
   * <strong>⚠️Important Note ⚠️</strong>
   *
   * <p>Use of {@code recordInstanceOverrides} is discouraged. Instance overrides are incompatible
   * with the Config Service.
   */
  @VisibleForTesting
  void recordInstanceOverrides() {
    final Properties mergedProperties = new Properties();
    mergedProperties.putAll(_overrideFileProperties);
    mergedProperties.putAll(System.getProperties());
    mergedProperties.putAll(_overrideEnvProperties);
    addInstanceStats(mergedProperties);

    final BasicDBList instanceProperties = new BasicDBList();

    mergedProperties.forEach(
        (key, value) -> {
          // "."s are not allowed in field names
          final BasicDBObject entry = new BasicDBObject();
          entry.put("key", key);

          if (SECRET_PROPERTIES.contains(key)) {
            try {
              @IgnoreGenEncryptMetadata(
                  description = "Encrypted fields in AppSettings",
                  isPersisted = true)
              final String encValue = EncryptionUtils.genEncryptStr((String) value);
              entry.put("value", encValue);
            } catch (final Exception e) {
              throw new RuntimeException(
                  "Failed to encrypt setting value for storage in database: " + key, e);
            }
          } else {
            entry.put("value", value);
          }
          instanceProperties.add(entry);
        });

    try {
      _dbProperties.set(getInstanceDbOverrideAppSettingId(), instanceProperties);
    } catch (UnknownHostException e) {
      throw new RuntimeException("Unable to get instance db override by appId", e);
    }
  }

  void clearInstanceOverride(final String pPropertyInstanceId) {
    _dbProperties.unset(APP_PROPERTY_INSTANCE_PREFIX + pPropertyInstanceId);
  }

  public void deleteInstanceConfigProperties(final String pHostname, final String pType) {
    _dbProperties.unset(getInstanceAppSettingId(pHostname, pType));
  }

  public Map<String, Properties> getInstanceOverrides() {
    final Map<String, Properties> result = new HashMap<>();
    if (_dbProperties != null) {
      _dbProperties
          .getValues(APP_PROPERTY_INSTANCE_PREFIX, BasicDBList.class)
          .forEach(
              (key1, value1) -> {
                final Properties instanceProperties = new Properties();
                value1.forEach(
                    setting -> {
                      final String key = (String) ((DBObject) setting).get("key");
                      final String value = (String) ((DBObject) setting).get("value");
                      if (EncryptionUtils.isEncrypted(value)) {
                        try {
                          instanceProperties.put(key, EncryptionUtils.genDecryptStr(value));
                        } catch (final Exception e) {
                          throw new RuntimeException("Failed to decrypt value for setting: " + key);
                        }
                      } else {
                        instanceProperties.put(key, value);
                      }
                    });
                result.put(
                    key1.substring(APP_PROPERTY_INSTANCE_PREFIX.length()), instanceProperties);
              });
    }

    return result;
  }

  /**
   * Calls {@link AppSettings#getProp} with a default return value of {@code null} to be used when
   * value is not found in Config Service local store.
   */
  @com.mongodb.internal.VisibleForTesting(otherwise = AccessModifier.PRIVATE)
  String getProp(final String pName, final SettingType pSettingType) {
    return getProp(pName, pSettingType, null);
  }

  /**
   * If {@code pConfigServiceDefaultValue} is {@code null}, the placeholder ({@code
   * NULL_PLACEHOLDER}) is used as the default value of the Config Service request context (since
   * Config Service does not support null values).
   *
   * <p>If the Config Service returns the {@code NULL_PLACEHOLDER} value we treat the response as if
   * {@code null} was returned.
   */
  @com.mongodb.internal.VisibleForTesting(otherwise = AccessModifier.PRIVATE)
  String getProp(final String pName, final SettingType pSettingType, final String pDefaultValue) {
    final ResolvedProperty resolvedProperty = getPropWithoutConfigService(pName, pSettingType);

    if (shouldUseConfigServiceToRetrieveProperty(pName, resolvedProperty)) {
      final String defaultValueToUse = pDefaultValue == null ? NULL_PLACEHOLDER : pDefaultValue;
      final ConfigServiceRequestContext context =
          new ConfigServiceRequestContext.Builder().defaultValue(defaultValueToUse).build();

      String configServiceValue;
      if (propertyNamesOfSecrets.contains(pName)) {
        final Optional<Secret> configServiceSecret = getSecretFromConfigService(pName);
        configServiceValue = configServiceSecret.map(Secret::toPlainTextValue).orElse(null);
      } else {
        configServiceValue = getStringPropertyFromConfigService(pName, context);
      }

      if (NULL_PLACEHOLDER.equals(configServiceValue)) {
        configServiceValue = null;
      }

      return configServiceValue;
    }

    return resolvedProperty.getValue();
  }

  /**
   * Fetches the property value for the given property name and setting type.
   *
   * <p>This method does not call to Config Service for a property's value. HOWEVER, this method
   * also does not always mean the legacy, non-Config Service resolution logic is 100% followed. if
   * the pSettingType is EFFECTIVE and the property is managed by the Config Service, this method
   * can return early and can return null, which acts as a flag for {@link
   * #shouldUseConfigServiceToRetrieveProperty} to return true, and triggers Config Service property
   * resolution in {@link #getProp(String, SettingType, String)}
   *
   * <p>This logic was written in this way to accommodate monitoring during the Config Service
   * Application Property migration. This could be refactored to be more straightforward.
   */
  @com.mongodb.internal.VisibleForTesting(otherwise = AccessModifier.PRIVATE)
  ResolvedProperty getPropWithoutConfigService(final String pName, final SettingType pSettingType) {
    switch (pSettingType) {
      case DATABASE:
        if (_cachedDbProperties.get() == null) {
          return new ResolvedProperty(null, Source.DATABASE);
        }

        return new ResolvedProperty(_cachedDbProperties.get().getProperty(pName), Source.DATABASE);
      case PROPERTIES_FILE:
        Source propertiesFileSource = Source.PROPERTY_FILE_OVERRIDE;
        String filePropValue = _overrideFileProperties.getProperty(pName);

        if (filePropValue == null) {
          propertiesFileSource = Source.PROPERTY_FILE_DEFAULT;
          filePropValue = _defaultFileProperties.getProperty(pName);
        }

        return new ResolvedProperty(filePropValue, propertiesFileSource);
      case SYSTEM:
        return new ResolvedProperty(System.getProperty(pName), Source.SYSTEM);
      case MEMORY:
        return new ResolvedProperty(_memoryProperties.get(pName), Source.MEMORY);
      case ENV:
        return new ResolvedProperty(System.getenv(pName), Source.ENVIRONMENT);
      case EFFECTIVE:
      default:
        // Can contain `null` values to hide a property from
        // appearing to be set
        synchronized (_memoryProperties) {
          if (_memoryProperties.containsKey(pName)) {
            return new ResolvedProperty(_memoryProperties.get(pName), Source.MEMORY);
          }
        }

        Source effectiveSource = Source.ENVIRONMENT_OVERRIDE;
        String propValue = _overrideEnvProperties.getProperty(pName);
        if (propValue != null) {
          return new ResolvedProperty(propValue, effectiveSource);
        }
        effectiveSource = Source.SYSTEM;
        propValue = System.getProperty(pName);

        /*
         If this is a Config Service application property, immediately return the ResolvedProperty, which could be null.
        */
        if (!NON_CONFIG_SERVICE_APP_PROPS.contains(pName)
            && !this.isPropertySetToNullAtTheMemoryLevelForIntTests(pName)) {
          return new ResolvedProperty(propValue, effectiveSource);
        }

        if (propValue == null) {
          effectiveSource = Source.PROPERTY_FILE_OVERRIDE;
          propValue = _overrideFileProperties.getProperty(pName);
        }
        if (propValue == null && _cachedDbProperties.get() != null) {
          effectiveSource = Source.DATABASE;
          propValue = _cachedDbProperties.get().getProperty(pName);
        }

        if (propValue == null) {
          effectiveSource = Source.PROPERTY_FILE_DEFAULT;
          propValue = _defaultFileProperties.getProperty(pName);
        }

        return new ResolvedProperty(propValue, effectiveSource);
    }
  }

  private String getPropRequiredEffective(final String pName) {
    final String propValue = getProp(pName, SettingType.EFFECTIVE);
    if (propValue == null) {
      throw new IllegalStateException("Key not found: " + pName);
    }

    return propValue;
  }

  /**
   * Set a property in either the DATABASE or MEMORY. A property set in the DATABASE will be visible
   * by all instances of this class connected to the same Application Database. A MEMORY property
   * will only be effective for this instance and lost on shutdown.
   *
   * <p><strong>⚠️Important Note ⚠️</strong>
   *
   * <p>Use of {@code setProp} is discouraged. The majority of properties are managed by the Config
   * Service which does not support programmatic setting of property values.
   *
   * <p><strong>Testing Guidance</strong>: If a property needs to be configured for tests consider
   * the following approaches:
   *
   * <ul>
   *   <li><strong>Mocking {@code getProp}</strong>: Mocking the {@code getProp} method directly to
   *       return desired values for each property.
   *   <li><strong>Mocking Config Service SDK</strong>: Mocking {@code ConfigServiceSdkWrapper}
   *       class which {@link AppSettings} uses to interact with the Config Service.
   * </ul>
   */
  public void setProp(final String pName, final String pValue, final SettingType pSettingType) {
    switch (pSettingType) {
      case DATABASE:
        setDatabaseProp(pName, pValue);
        refreshDatabaseCache();
        return;
      case MEMORY:
        synchronized (this) {
          _memoryProperties.put(pName, pValue);
          _settingsVersion++;
        }
        return;
    }

    throw new RuntimeException("SettingType `" + pSettingType + "` can not be set");
  }

  private void setDatabaseProp(final String pName, final String pValue) {
    if (pValue == null) {
      if (MongoClientRuntimeSettingsCache.containsConfig(pName)) {
        MongoClientRuntimeSettingsCache.removeConfig(pName);
      }
      _dbProperties.unset(APP_PROPERTY_GLOBAL_PREFIX + pName);
    } else if (SECRET_PROPERTIES.contains(pName)) {
      try {
        @IgnoreGenEncryptMetadata(
            description = "Encrypted fields in AppSettings DB",
            isPersisted = true)
        final String encPropertyValue = EncryptionUtils.genEncryptStr(pValue);
        _dbProperties.set(APP_PROPERTY_GLOBAL_PREFIX + pName, encPropertyValue);
      } catch (final Exception e) {
        throw new RuntimeException(
            "Failed to encrypt setting value for storage in database: " + pName);
      }
    } else {
      _dbProperties.set(APP_PROPERTY_GLOBAL_PREFIX + pName, pValue);
    }
  }

  public void setDatabaseProps(
      final Map<String, String> pProps, final Consumer<AppSettingsChange> auditConsumer) {

    pProps.forEach(
        (key, value) -> {
          final String prop = getProp(key, SettingType.DATABASE);
          final AppSettingsChange change =
              new AppSettingsChange(
                  key,
                  StringUtils.isEmpty(prop) ? null : prop,
                  value,
                  SECRET_PROPERTIES.contains(key));
          setDatabaseProp(key, value);
          auditConsumer.accept(change);
        });
    refreshDatabaseCache();
  }

  public void setDatabaseProps(final Map<String, String> pProps) {
    setDatabaseProps(pProps, change -> {});
  }

  void refreshDatabaseCache() {
    _dbPropertiesRefresher.run();
  }

  public void setClassReferenceProp(
      final String pName, final Class<?> pClass, final SettingType pSettingType) {
    setProp(pName, pClass.getName(), pSettingType);
  }

  public String getStrProp(
      final String pName, final String pDefault, final SettingType pSettingType) {
    final String propValue = getProp(pName, pSettingType, pDefault);
    return propValue != null ? propValue : pDefault;
  }

  public String getStrProp(final String pName, final String pDefault) {
    return getStrProp(pName, pDefault, SettingType.EFFECTIVE);
  }

  public String getStrProp(final String pName) {
    return getPropRequiredEffective(pName);
  }

  public Optional<String> getOptionalStrProp(final String pName) {
    return ofNullable(getStrProp(pName, null));
  }

  public Optional<Integer> getOptionalIntProp(final String name) {
    try {
      return Optional.of(getIntProp(name));
    } catch (final IllegalStateException | NumberFormatException e) {
      return Optional.empty();
    }
  }

  public Date getDateStrProp(final String pName, final Date pDefault) {
    final String propValue = getProp(pName, SettingType.EFFECTIVE, TimeUtils.toISOString(pDefault));
    return StringUtils.isNotEmpty(propValue) ? TimeUtils.fromISOString(propValue) : pDefault;
  }

  public Date getDateStrProp(final String pName) {
    return TimeUtils.fromISOString(getStrProp(pName));
  }

  public List<String> getListProperty(final String pFieldName, final String pSep) {
    return Stream.of(getStrProp(pFieldName, "").split(pSep))
        .map(String::trim)
        .filter(p -> !p.isEmpty())
        .toList();
  }

  public Map<String, String> getMapProperty(final String pFieldName) {
    return getMapProperty(pFieldName, ",", "=");
  }

  /**
   * Parses an AppSetting into a map. For example, if the AppSetting value is {@code
   * "key1=value1;key2=value2"}:
   *
   * <ul>
   *   <li>{@code pEntrySep} would be {@code ";"} to separate each key-value pair.
   *   <li>{@code pKeyValueSep} would be {@code "="} to split keys from their values.
   * </ul>
   *
   * <p>This would result in a map with entries: {@code {"key1"="value1", "key2"="value2"}}.
   *
   * @param pFieldName the AppSettings name
   * @param pEntrySep the separator used to delineate individual entries in the map
   * @param pKeyValueSep the separator used to split keys from their values
   * @return a {@code Map<String, String>} containing the parsed key-value pairs
   */
  public Map<String, String> getMapProperty(
      final String pFieldName, final String pEntrySep, final String pKeyValueSep) {
    return Splitter.on(pEntrySep)
        .omitEmptyStrings()
        .trimResults()
        .withKeyValueSeparator(pKeyValueSep)
        .split(getStrProp(pFieldName, ""));
  }

  /**
   * If an AppSetting is stored as a map, retrieve the value associated with a specific key from the
   * map.
   *
   * @param pFieldName the name of the AppSettings property
   * @param key the key of interest in the AppSettings map property
   * @return an {@code Optional} containing the value associated with the specified key, or an empty
   *     {@code Optional} if the key is not found or the AppSetting is not a valid map
   */
  public Optional<String> getMapPropertyValue(final String pFieldName, final String key) {
    try {
      return Optional.ofNullable(getMapProperty(pFieldName).get(key));
    } catch (IllegalArgumentException e) {
      LOG.error("AppSetting {} is not a valid map", pFieldName, e);
      return Optional.empty();
    }
  }

  public Set<String> getSetProperty(final String pFieldName, final String pSep) {
    return Set.copyOf(getListProperty(pFieldName, pSep));
  }

  public void setDateStrProp(final String pName, final Date pDate) {
    setProp(pName, TimeUtils.toISOString(pDate), SettingType.DATABASE);
  }

  public boolean getBoolProp(
      final String pName, final boolean pDefault, final SettingType pSettingType) {
    final String propValue = getProp(pName, pSettingType, String.valueOf(pDefault));
    return propValue != null ? Boolean.parseBoolean(propValue) : pDefault;
  }

  public Boolean getBoolPropWithFallbacks(final Boolean pDefaultValue, final String... pNames) {
    return getPropWithFallbacks(Boolean.class, pDefaultValue, pNames);
  }

  public Integer getValidatedIntPropWithFallbacks(
      final Integer pDefaultValue, final Predicate<Integer> pValidator, final String... pNames) {
    return getPropWithFallbacks(Integer.class, pValidator, pDefaultValue, pNames);
  }

  private <T> T getPropWithFallbacks(
      final Class<T> pPropType, final T pDefaultValue, final String... pNames) {
    return getPropWithFallbacks(pPropType, value -> true, pDefaultValue, pNames);
  }

  private <T> T getPropWithFallbacks(
      final Class<T> pPropType,
      final Predicate<T> pValidator,
      final T pDefaultValue,
      final String... pNames) {
    for (final String name : pNames) {
      final Optional<T> propValue = tryGetConvertedProp(name, pPropType).filter(pValidator);
      if (propValue.isPresent()) {
        return propValue.get();
      }
    }
    return pDefaultValue;
  }

  private <T> Optional<T> tryGetConvertedProp(final String pName, final Class<T> pPropType) {
    try {
      return Optional.ofNullable(get(pPropType, pName, null));
    } catch (final ConversionException ex) {
      LOG.error("Could not get converted property {}", pName, ex);
    }
    return Optional.empty();
  }

  public boolean getBoolProp(final String pName, final boolean pDefault) {
    return getBoolProp(pName, pDefault, SettingType.EFFECTIVE);
  }

  public boolean getBoolProp(final String pName) {
    return Boolean.parseBoolean(getPropRequiredEffective(pName));
  }

  public int getIntProp(final String pName, final int pDefault, final SettingType pSettingType) {
    final String propValue = getProp(pName, pSettingType, String.valueOf(pDefault));
    return propValue != null && !propValue.isEmpty() ? Integer.parseInt(propValue) : pDefault;
  }

  public int getIntProp(final String pName, final int pDefault) {
    return getIntProp(pName, pDefault, SettingType.EFFECTIVE);
  }

  public int getIntProp(final String pName) {
    return Integer.parseInt(getPropRequiredEffective(pName));
  }

  public long getLongProp(final String pName, final long pDefault, final SettingType pSettingType) {
    final String propValue = getProp(pName, pSettingType, String.valueOf(pDefault));
    return propValue != null && !propValue.isEmpty() ? Long.parseLong(propValue) : pDefault;
  }

  public long getLongProp(final String pName, final long pDefault) {
    return getLongProp(pName, pDefault, SettingType.EFFECTIVE);
  }

  public long getLongProp(final String pName) {
    return Long.parseLong(getPropRequiredEffective(pName));
  }

  public double getDoubleProp(
      final String pName, final double pDefault, final SettingType pSettingType) {
    final String propValue = getProp(pName, pSettingType, String.valueOf(pDefault));
    return propValue != null && !propValue.isEmpty() ? Double.parseDouble(propValue) : pDefault;
  }

  public double getDoubleProp(final String pName, final double pDefault) {
    return getDoubleProp(pName, pDefault, SettingType.EFFECTIVE);
  }

  public double getDoubleProp(final String pName) {
    return Double.parseDouble(getPropRequiredEffective(pName));
  }

  public boolean isUsingHttpProxy() {
    return hasProp(HTTP_PROXY_HOST.value) && hasProp(HTTP_PROXY_PORT.value);
  }

  public boolean shouldDisableHttpProxyForS3() {
    return getBoolProp(DISABLE_HTTP_PROXY_FOR_S3.value, false);
  }

  public boolean isBackupSnapshotExpiryUpdateAllowed() {
    return getBoolProp("mms.backup.allowSnapshotExpiryUpdate", false);
  }

  public InetSocketAddress getHttpProxyAddress() {
    if (isUsingHttpProxy()) {
      return new InetSocketAddress(
          getStrProp(HTTP_PROXY_HOST.value), getIntProp(HTTP_PROXY_PORT.value));
    } else {
      return null;
    }
  }

  private Properties initializeFileProperties(final List<String> propertiesPaths) {
    final Properties fileProperties = new Properties();
    propertiesPaths.forEach(
        confFile -> {
          if (confFile.isEmpty()) {
            return;
          }
          try (final InputStream stream = UrlReadingUtils.getInputStreamFromUrl(confFile)) {
            final Properties extraProperties = new Properties();
            extraProperties.load(stream);
            fileProperties.putAll(extraProperties);
            LOG.debug(
                "Loaded extra configuration file {} with {} properties",
                confFile,
                extraProperties.size());
          } catch (final Exception e) {
            LOG.debug(
                "Could not load extra configuration file {}. Message: {}",
                confFile,
                e.getMessage());
          }
        });

    return fileProperties;
  }

  private void initializeKrb5() {
    if (hasProp(Fields.NS_JVM_KRB5_CONF.value)) {
      final String krbConf = getStrProp(Fields.NS_JVM_KRB5_CONF.value);
      System.setProperty("java.security.krb5.conf", krbConf);
      LOG.info("Set Java Security krb5.conf to {}", krbConf);
    }

    if (hasProp(Fields.NS_JVM_KRB5_KDC.value)
        && hasProp(Fields.NS_JVM_KRB5_REALM.value)
        && hasProp(Fields.NS_MMS_KERBEROS_PRINCIPAL.value)) {

      System.setProperty("java.security.krb5.kdc", getStrProp(Fields.NS_JVM_KRB5_KDC.value));
      System.setProperty("java.security.krb5.realm", getStrProp(Fields.NS_JVM_KRB5_REALM.value));
      // Suggested by the Mongo Java Driver.
      System.setProperty("javax.security.auth.useSubjectCredsOnly", "false");
      final Krb5Configuration conf = new Krb5Configuration(Configuration.getConfiguration(), this);
      Configuration.setConfiguration(conf);
      LOG.info("Set Java Security krb5 kdc and realm along with useSubjectCredsOnly=false");
    }

    if (hasProp(Fields.NS_MMS_KERBEROS_DEBUG.value)
        && "true".equalsIgnoreCase(getStrProp(Fields.NS_MMS_KERBEROS_DEBUG.value))) {
      System.setProperty("sun.security.krb5.debug", "true");
      System.setProperty("sun.security.jgss.debug", "true");
      LOG.info("Enabled Java Security krb5 and jgss debug");
    }
  }

  private void initializeFIPSProvider() {
    Security.setProperty("ssl.KeyManagerFactory.algorithm", "PKIX");
    Security.setProperty("ssl.TrustManagerFactory.algorithm", "PKIX");

    int secureRandomPoolSize = getIntProp(MMS_SECURITY_SECURE_RANDOM_POOL_SIZE.value, 1);
    SecuritySettings.presetSecureRandomPoolSize(secureRandomPoolSize);

    if (isJavaFipsProviderDefault()) {
      SecuritySettings.installFIPSProviderAsDefault();
    } else {
      SecuritySettings.installFIPSProvider();
    }
  }

  /**
   * This property is used to setup Prom metrics server, which starts before the config service SDK
   * is initialized.
   */
  public boolean isPromEnabled() {
    return getBoolProp("prom.listening.enabled", true);
  }

  /**
   * This property is used to setup Prom metrics server, which starts before the config service SDK
   * is initialized.
   */
  public boolean publicPromMetricsOnly() {
    return getBoolProp("prom.listening.publicMetricsOnly", false);
  }

  /**
   * This property is used to setup Prom metrics server, which starts before the config service SDK
   * is initialized.
   */
  public boolean publicPromSavingEnabled() {
    return getBoolProp("prom.saving.publicMetrics", true);
  }

  /**
   * This property is used to setup Jetty server, which is before the config service SDK is
   * initialized.
   */
  public boolean isHttp2ClearTextEnabled() {
    return getBoolProp("mms.jetty.enableHTTP2Cleartext", false);
  }

  /**
   * This property is used to enable (only locally) HTTP/2. This requires mms.https.PEMKeyFile being
   * set.
   */
  public boolean isHttp2EnabledForLocal() {
    return isLocal() && getBoolProp("mms.jetty.enableHTTP2ForLocal", false);
  }

  /**
   * This property is used to setup Jetty server, which is before the config service SDK is
   * initialized.
   */
  public long getJettyStopTimeoutMs() {
    return getLongProp("mms.jetty.stopTimeoutMS", 5_000L);
  }

  public int getDiagnosticsFileSizeLimitInMB() {
    return getIntProp("prom.public.diagnostics.fileSizeLimitInMB", 100);
  }

  public boolean isJavaFipsProviderDefault() {
    return getBoolProp(Fields.JAVA_FIPS_PROVIDER_DEFAULT.value, false);
  }

  private void initializeHTTPProxy() {
    if (isUsingHttpProxy()) {
      System.setProperty("http.proxyHost", getStrProp(HTTP_PROXY_HOST.value));
      System.setProperty("http.proxyPort", getStrProp(HTTP_PROXY_PORT.value));
      System.setProperty("https.proxyHost", getStrProp(HTTP_PROXY_HOST.value));
      System.setProperty("https.proxyPort", getStrProp(HTTP_PROXY_PORT.value));

      final boolean hasNonProxyHosts = hasProp(HTTP_PROXY_NON_PROXY_HOSTS.value);
      if (hasNonProxyHosts) {
        System.setProperty("http.nonProxyHosts", getStrProp(HTTP_PROXY_NON_PROXY_HOSTS.value));
      }
      LOG.info(
          "Configured HTTP proxy host and port {} non proxy hosts",
          hasNonProxyHosts ? "with" : "without");
    }

    if (hasProp(HTTP_PROXY_USERNAME.value)) {
      Authenticator.setDefault(
          new ProxyAuthenticator(
              getStrProp(HTTP_PROXY_USERNAME.value), getStrProp(HTTP_PROXY_PASSWORD.value)));
      LOG.info("Configured HTTP proxy with authentication");
    }
  }

  private static class ProxyAuthenticator extends Authenticator {

    private final PasswordAuthentication auth;

    private ProxyAuthenticator(final String pUsername, String pPassword) {
      // Proxy doesn't necessarily require a password
      if (pPassword == null) {
        pPassword = StringUtils.EMPTY;
      }
      auth = new PasswordAuthentication(pUsername, pPassword.toCharArray());
    }

    @Override
    protected PasswordAuthentication getPasswordAuthentication() {
      return auth;
    }
  }

  public enum ClientAuthenticationMode {
    NONE,
    AGENTS_ONLY,
    REQUIRED
  }

  // As of 2021-12, we are going to switch MMS from running on `HOST`s to `DOCKER`.
  // Next step will be to migrate from Docker to Kubernetes. Once we will migrate
  // to Kubernetes, any code-branches which only target `DOCKER` will be used for a
  // local-only environment. `HOST` must be preserved for the Ops Manager use-case.
  public enum AppRuntimeEnvironment {
    HOST {
      @Override
      public boolean isEphemeral() {
        return false;
      }
    },
    DOCKER {
      @Override
      public boolean isEphemeral() {
        return true;
      }
    };

    public abstract boolean isEphemeral();

    public static AppRuntimeEnvironment fromString(final String pValue) {
      if (pValue == null) {
        return HOST;
      }
      return valueOf(pValue);
    }
  }

  public ClientAuthenticationMode getClientAuthenticationMode() {
    final String mode = this.getStrProp("mms.https.ClientCertificateMode", null);
    if (mode == null) {
      return ClientAuthenticationMode.NONE;
    }
    return switch (mode) {
      case "required" -> ClientAuthenticationMode.REQUIRED;
      case "agents_only" -> ClientAuthenticationMode.AGENTS_ONLY;
      default -> ClientAuthenticationMode.NONE;
    };
  }

  public AppRuntimeEnvironment getAppRuntimeEnvironment() {
    final String mode = this.getStrProp("mms.app.instanceType", null);
    if (mode == null) {
      return AppRuntimeEnvironment.HOST;
    }
    return switch (mode) {
      case "docker" -> AppRuntimeEnvironment.DOCKER;
      default -> AppRuntimeEnvironment.HOST;
    };
  }

  public String getConfiguredHostname() {
    return this.getStrProp(MMS_APP_HOSTNAME.value, null);
  }

  public String getHTTPSCAFilename() {
    if (!this.hasProp("mms.https.CAFile")) {
      return null;
    }
    return getStrProp("mms.https.CAFile");
  }

  /**
   * This method is used to setup Prom server, which starts before the config service SDK is
   * initialized.
   */
  public boolean hasHTTPSPEMKeyFile() {
    return this.hasProp(MMS_HTTPS_PEMKEYFILE.value);
  }

  public boolean hasHTTPDualConnectors() {
    return getBoolProp(HAS_HTTP_DUAL_CONNECTORS, false);
  }

  public ImmutablePair<String, String> getHTTPSPEMKeyFileSettings() {
    if (!this.hasProp(MMS_HTTPS_PEMKEYFILE.value)) {
      return null;
    }

    final String sslPEMFile = getStrProp(MMS_HTTPS_PEMKEYFILE.value);
    final String sslPEMFilePassword = getStrProp(MMS_HTTPS_PEMKEYFILE_PASSWORD.value, null);

    return new ImmutablePair<>(sslPEMFile, sslPEMFilePassword);
  }

  public SSLContextSettings getMongoDBSSLContextSettings() throws Exception {
    return getSSLContextSettings(MONGODB_SSL_CONTEXT_PREFIX);
  }

  private SSLContextSettings getSSLContextSettings(final String pSettingPrefix) throws Exception {
    final String settingCAFile = pSettingPrefix + ".ssl.CAFile";
    final String settingPEMFile = pSettingPrefix + ".ssl.PEMKeyFile";
    final String settingPEMFilePassword = pSettingPrefix + ".ssl.PEMKeyFilePassword";

    final String caFilePath = getStrProp(settingCAFile, null);
    final InputStream caFile =
        StringUtils.isNotBlank(caFilePath) ? new FileInputStream(caFilePath) : null;
    final String pemFile = getStrProp(settingPEMFile, null);
    final String pemFilePassword = getStrProp(settingPEMFilePassword, null);

    LOG.debug(
        "Creating {} SSL context settings with "
            + "CAFile={}, PEMFileFile={}, PEMKeyFilePassword={}",
        pSettingPrefix,
        caFilePath,
        pemFile,
        isNotBlank(pemFilePassword) ? "<redacted>" : "<empty>");
    return new SSLContextSettings(pSettingPrefix, caFile, pemFile, pemFilePassword);
  }

  public Stage getStage() {
    return (getAppEnv().isLocal() || getAppEnv().isTest()) ? Stage.DEVELOPMENT : Stage.PRODUCTION;
  }

  public AppEnv getAppEnv() {
    return _appEnv;
  }

  public String getEnvCode() {
    return _appEnv.getCode();
  }

  public String getInstanceId() {
    return getStrProp(
        Property.INSTANCE_ID.value(), DEFAULT_INSTANCE_ID.toString(), SettingType.SYSTEM);
  }

  /**
   * Gets the base port. This method doesn't rely on config service as it's used as part of prom
   * metrics server setup, which starts before config service SDK initialization.
   */
  public Integer getBasePort() {
    return hasHTTPSPEMKeyFile() ? getSslBasePort() : getNonSslBasePort();
  }

  public Integer getNonSslBasePort() {
    final int instanceId = Integer.parseInt(getInstanceId());
    return getIntProp(BASE_PORT.value(), DEFAULT_PORT, SettingType.SYSTEM) + instanceId;
  }

  public Integer getSslBasePort() {
    final int instanceId = Integer.parseInt(getInstanceId());
    return getIntProp(BASE_SSL_PORT.value(), DEFAULT_SSL_PORT, SettingType.SYSTEM) + instanceId;
  }

  /**
   * @return E.g., "mms8.10gen.cc-1" where -Dinstance-id=1 *
   */
  public String getHostnameId() {
    return getHostnameId(getHostname());
  }

  public String getPoolName(final String hostname) {
    final Matcher matcher = HELIX_POD_NAME_PATTERN.matcher(hostname);
    return matcher.matches() ? matcher.group(1) : "";
  }

  public String getHostname() {
    try {
      return SystemUtils.getHostname();
    } catch (final UnknownHostException e) {
      throw new IllegalStateException(e);
    }
  }

  public String getHostnameId(final String pHostname) {
    return pHostname + "-" + getInstanceId();
  }

  public String getInstanceDbOverrideAppSettingId() throws UnknownHostException {
    return getInstanceAppSettingId(SystemUtils.getHostname(), getAppId());
  }

  public String getInstanceAppSettingId(final String pHostname, final String pType) {
    return APP_PROPERTY_INSTANCE_PREFIX + getHostnameId(pHostname) + "-" + pType;
  }

  public boolean isTest() {
    return _appEnv == AppEnv.TEST;
  }

  public boolean awaitRunning(final long pWait, final TimeUnit pTimeUnit)
      throws InterruptedException {
    return _runningLatch.await(pWait, pTimeUnit);
  }

  public void awaitRunning() throws InterruptedException {
    _runningLatch.await();
  }

  public void startRunning() {
    _lifeCycleState = LifeCycleState.RUNNING;
    _runningLatch.countDown();
  }

  public boolean isRunning() {
    return _lifeCycleState == LifeCycleState.RUNNING;
  }

  public void stopRunning() {
    _lifeCycleState = LifeCycleState.STOPPING;
  }

  public boolean isStopping() {
    return _lifeCycleState == LifeCycleState.STOPPING;
  }

  public void ensureAppNotStopping() throws AppShutdownException {
    if (isStopping()) {
      throw new AppShutdownException();
    }
  }

  public String getAppId() {
    return _appId;
  }

  public boolean isRoutingPingsToQueue() {
    return _routingPingsToQueue;
  }

  public void setRoutingPingsToQueue(final boolean pV) {
    _routingPingsToQueue = pV;
  }

  public String getDocsUrl() {
    return getDocsUrlForGroupType(null);
  }

  public String getDocsSearchUrl() {
    return getStrProp(Fields.DOCS_SEARCH_URL.value);
  }

  public <E extends Enum<E>> String getDocsUrlForGroupType(final E pGroupType) {
    String docsUrl = getStrProp("mms.docsUrl");

    if (pGroupType != null) {
      docsUrl = getStrProp("mms.docsUrl." + pGroupType.name().toLowerCase(), docsUrl);
    }

    return docsUrl;
  }

  public URL getWebappAppIdURL() throws MalformedURLException {
    final File file = new File("src", "webapp-" + _appId);
    final URL resource =
        file.exists()
            ? file.toURI().toURL()
            : getClass().getClassLoader().getResource("webapp-" + _appId);
    return resource;
  }

  public boolean isLocal() {
    return getAppEnv().isLocal();
  }

  public String getHostedVersion() {
    return getStrProp(Fields.HOSTED_VERSION.value, "current");
  }

  /**
   * @return an OM version string if in hosted app environment, or the git hash otherwise
   */
  public String getDisplayVersion() {
    return getGitVersion();
  }

  /**
   * @return version information depending on the current app environment (hosted or other)
   */
  public VersionInfo getVersionInfo() {
    // Cloud Manager and Atlas: consider the branch as version string
    return new VersionInfo(getGitVersion(), getGitBranch());
  }

  public String getGitVersion() {
    return getStrProp(Fields.GIT_LATEST_COMMIT.value);
  }

  public String getGitBranch() {
    return getStrProp(Fields.GIT_BRANCH.value);
  }

  public boolean isAlertProcessingServiceEnabled() {
    return getBoolProp(Fields.MMS_ALERT_PROCESSING_ENABLED.value, false);
  }

  public boolean isValidEnvForBillingStatusPage() {
    return getBoolProp(Fields.MMS_BILLING_STATUSPAGE_ENABLED.value, false);
  }

  public boolean allowsUnrestrictedAgentApiKey() {
    return getBoolProp(Fields.NDS_ALLOW_UNRESTRICTED_AGENT_API_KEY.value, false);
  }

  public boolean isMMSBackupUsageMeteringEnabled() {
    return getBoolProp(Fields.MMS_BILLING_BACKUP_USAGE_ENABLED.value, true);
  }

  public boolean isCpsOplogUsageCollectionEnabled() {
    return getBoolProp(Fields.NDS_CPS_OPLOG_USAGE_COLLECTION.value, false);
  }

  public boolean isCpsOplogUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_CPS_OPLOG_SUBMISSION.value, false);
  }

  public boolean isStorageRegionNameUsedForCpsOplogUsageEnabled() {
    return getBoolProp(Fields.NDS_CPS_OPLOG_STORAGE_REGION_USAGE.value, false);
  }

  public boolean isCpsOplogUsageMigrationEnabled() {
    return getBoolProp(Fields.NDS_CPS_OPLOG_USAGE_MIGRATION.value, false);
  }

  public boolean isCpsAWSSnapshotUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_CPS_AWS_SNAPSHOT_USAGE_SUBMISSION.value, false);
  }

  public boolean isCpsAzureSnapshotUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_CPS_AZURE_SNAPSHOT_USAGE_SUBMISSION.value, false);
  }

  public boolean isCpsGCPSnapshotUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_CPS_GCP_SNAPSHOT_USAGE_SUBMISSION.value, false);
  }

  public boolean isCpsExportUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_CPS_EXPORT_USAGE_SUBMISSION.value, false);
  }

  public boolean isCpsAWSDownloadUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_CPS_AWS_DOWNLOAD_USAGE_SUBMISSION.value, false);
  }

  public boolean isCpsAWSExportUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_CPS_AWS_EXPORT_USAGE_SUBMISSION.value, false);
  }

  public boolean isCpsAzureExportUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_CPS_AZURE_EXPORT_USAGE_SUBMISSION.value, false);
  }

  public boolean isCpsAzureDownloadUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_CPS_AZURE_DOWNLOAD_USAGE_SUBMISSION.value, false);
  }

  public boolean isCpsGCPDownloadUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_CPS_GCP_DOWNLOAD_USAGE_SUBMISSION.value, false);
  }

  public boolean isCpsGCPExportUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_CPS_GCP_EXPORT_USAGE_SUBMISSION.value, false);
  }

  public boolean isUseSimpleQueryToFindSnapshotsToPurgeEnabled() {
    return getBoolProp(Fields.NDS_CPS_USE_SIMPLE_QUERY_TO_FIND_SNAPSHOTS_TO_PURGE.value, true);
  }

  public int getCpsConfCallSessionWinnerTimeoutMins() {
    return getInt(Fields.NDS_CPS_CONF_CALL_SESSION_WINNER_TIMEOUT_IN_MINS.value, 3);
  }

  public int getCpsOplogPurgeMaxDbWriteBatchSize() {
    return getInt(Fields.NDS_CPS_OPLOG_PURGE_MAX_DB_WRITE_BATCH_SIZE.value, 50);
  }

  public Integer getCpsOplogGapTimestamp() {
    return getInteger(Fields.NDS_CPS_OPLOG_GAP_TIMESTAMP.value, DEFAULT_CPS_OPLOG_GAP_TIMESTAMP);
  }

  public DatadogRegion getDefaultDatadogRegion() {
    return getAppEnv().isGovCloud() ? DatadogRegion.US1_FED : DatadogRegion.US;
  }

  public boolean getCpsPitApplyOpsWithConfCallSessionUpdate() {
    return getBoolProp(Fields.NDS_CPS_PIT_APPLY_OPS_WITH_CONF_CALL_SESSION_UPDATE.value, true);
  }

  public int getCpsExportWithThreeNodesPerShardThresholdGb() {
    return getIntProp(
        Fields.NDS_CPS_EXPORT_WITH_THREE_NODES_PER_SHARD_THRESHOLD_SIZE_GB.value, 1024);
  }

  public boolean xdsRequiresTls() {
    return getBoolProp(Fields.SERVERLESS_XDS_TLS.value, true);
  }

  public String xdsTrustedCAFilePath() {
    return getStrProp(Fields.SERVERLESS_XDS_TRUSTED_CA_FILE_PATH.value, "");
  }

  public String getAccountCentralUrl() {
    return normalizeCentralUrl(getStrProp("account.centralUrl", null));
  }

  public String getCentralUrl() {
    return normalizeCentralUrl(getStrProp("mms.centralUrl", null));
  }

  public String getGatewayRouterServiceAddressOverride() {
    return getStrProp("mms.gatewayRouter.serviceAddress", "");
  }

  public String getGatewayRouterLogLevel() {
    return getStrProp("mms.gatewayRouter.logLevel", "debug");
  }

  public String getSlackOAuthUrl() {
    final String slackRootUrlOverride = getString("slack.rootUrl.override");
    if (StringUtils.isNotBlank(slackRootUrlOverride)) {
      return slackRootUrlOverride;
    }
    return getCentralUrl();
  }

  /** This secret is used for alerts UAT, and is therefore only available in dev and local. */
  public String getAlertTestSlackChannel() {
    return normalizeCentralUrl(getStrProp("mms.slack.test.channel", null));
  }

  /** This secret is used for alerts UAT, and is therefore only available in dev and local. */
  public String getAlertTestDatadogApiToken() {
    return getStrProp("mms.alert.test.datadog.apiToken", null);
  }

  /** This secret is used for alerts UAT, and is therefore only available in dev and local. */
  public String getAlertTestPagerDutyServiceKey() {
    return getStrProp("mms.alert.test.pagerduty.serviceKey", null);
  }

  /** This secret is used for alerts UAT, and is therefore only available in dev and local. */
  public String getAlertTestOpsGenieApiKey() {
    return getStrProp("mms.alert.test.opsGenie.apiKey", null);
  }

  /** This secret is used for alerts UAT, and is therefore only available in dev and local. */
  public String getAlertTestMicrosoftTeamsUrl() {
    return getStrProp("mms.alert.test.microsoftTeams.url", null);
  }

  /** This secret is used for alerts UAT, and is therefore only available in dev and local. */
  public String getAlertTestWebhookApiKey() {
    return getStrProp("mms.alert.test.webhook.apiKey", null);
  }

  /** This secret is used for alerts UAT, and is therefore only available in dev and local. */
  public String getAlertTestVictorOpsApiKey() {
    return getStrProp("mms.alert.test.victorOps.apiKey", null);
  }

  /** This secret is used for alerts UAT, and is therefore only available in dev and local. */
  public String getAlertTestVictorOpsRoutingKey() {
    return getStrProp("mms.alert.test.victorOps.routingKey", null);
  }

  /** This secret is used for alerts UAT, and is therefore only available in dev and local. */
  public String getAlertTestSlackToken() {
    return normalizeCentralUrl(getStrProp("mms.slack.test.token", null));
  }

  /** This secret is used for alerts UAT, and is therefore only available in dev and local. */
  public String getAlertTestEmailAddress() {
    return normalizeCentralUrl(getStrProp("mms.alert.test.email.emailAddress", null));
  }

  /** This secret is used for alerts UAT, and is therefore only available in dev and local. */
  public String getAlertTestPhoneNumber() {
    return normalizeCentralUrl(getStrProp("mms.alert.test.sms.phoneNumber", null));
  }

  public long getAlertsHostMeasurementLookbackDuration(final long pDefaultLookbackDuration) {
    return getLongProp(
        ALERTS_HOST_MEASUREMENT_LOOKBACK_DURATION_MILLIS.value, pDefaultLookbackDuration);
  }

  public String getCentralDomainAllowedOrigin() {
    return normalizeCentralUrl(getStrProp("mms.centralDomainAllowedOrigin", null));
  }

  public String getBaasCentralUrl() {
    return normalizeCentralUrl(getStrProp("baas.centralUrl", null));
  }

  public String getBaasLegacyCentralUrl() {
    return normalizeCentralUrl(getStrProp("baas.legacyCentralUrl", null));
  }

  public String getChartsCentralUrl() {
    return normalizeCentralUrl(getStrProp("charts.centralUrl", null));
  }

  public String getChartsStitchAppId() {
    return getOptionalStrProp(Fields.CHARTS_STITCH_APP_ID.value).orElse("");
  }

  public String getChartsRealmApiSecret() {
    return getOptionalStrProp(Fields.CHARTS_REALM_API_SECRET.value).orElse("");
  }

  public String getMeteringCentralUrl() {
    String url = getStrProp(Fields.METERING_CENTRAL_URL.value, null);
    if (url == null) {
      url =
          String.format(
              "%s://%s",
              getMeteringCentralUrlScheme(),
              System.getenv(getStrProp(Fields.METERING_SERVICE_MESH_ENV_VAR.value)));
    }
    return normalizeCentralUrl(url);
  }

  private String getMeteringCentralUrlScheme() {
    return getStrProp(METERING_CENTRAL_URL_SCHEME.value, "http");
  }

  public String getUniversityCentralUrl() {
    // University typically has only one centralUrl, the only exception is their fake /etc/hosts
    // entry they use when used locally, which should only be considered for CORS purposes, hence
    // it's safe to use the first entry only when creating UI links or doing redirects
    return normalizeCentralUrl(
        getListProperty("university.centralUrls", COMMA_DELIMITER).stream()
            .findFirst()
            .orElse(null));
  }

  public String getSupportAppEmbedLink() {
    return getStrProp("support.appEmbedLink", null);
  }

  public int getLogCollectionMaxDiskSpaceMB() {
    // Default to 20GB
    return getIntProp(
        "logCollection.maxDiskSpaceMB", (int) Units.GIGABYTES.convertTo(20, Units.MEGABYTES));
  }

  public String getAgentCentralUrl() {
    return normalizeCentralUrl(getStrProp("mms.agentCentralUrl", getCentralUrl()));
  }

  public String getXdsCentralUrl() {
    return getStrProp("nds.xdsCentralUrl", getCentralUrl());
  }

  public int getXdsPort() {
    return getIntProp(Fields.XDS_PORT.value, 443);
  }

  public String getXdsBindAddress() {
    return getStrProp(Fields.XDS_BIND_ADDRESS.value, "127.0.0.1");
  }

  public String getUniformFrontendXDSBindAddress() {
    return getStrProp(Fields.UNIFORM_FRONTEND_XDS_BIND_ADDRESS.value, "127.0.0.1");
  }

  public String getMmsGrpcServerBindAddress() {
    return getStrProp(Fields.MMS_GRPC_SERVER_BIND_ADDRESS.value, "127.0.0.1");
  }

  public int getMonitoringRealmRequestThreads() {
    return getIntProp(Fields.MONITORING_REALM_REQUEST_THREADS.value, 0);
  }

  public int getMonitoringRealmRecentMeasurementsInterval() {
    return getIntProp(
        Fields.MONITORING_REALM_RECENT_MEASUREMENTS_INTERVAL_MILLIS.value, 10 * 60 * 1000);
  }

  public int getMonitoringRealmAppDiscoveryInterval() {
    return getIntProp(Fields.MONITORING_REALM_APP_DISCOVERY_INTERVAL_SECS.value, 60 * 60);
  }

  public boolean isMonitoringRealmEnabled() {
    return getBoolProp(Fields.MONITORING_REALM_ENABLED.value, false);
  }

  public boolean isFTSRrdEnabled() {
    return getBoolProp(Fields.MONITORING_FTS_ENABLED.value, false);
  }

  public String getAutomationDefaultDownloadBase() {
    return getStrProp(Fields.AUTOMATION_DEFAULT_DOWNLOAD_BASE.value, DEFAULT_DOWNLOAD_BASE);
  }

  public String getAutomationDefaultDownloadBaseWindows() {
    return getStrProp(
        Fields.AUTOMATION_DEFAULT_DOWNLOAD_BASE_WINDOWS.value, DEFAULT_DOWNLOAD_BASE_WINDOWS);
  }

  public String getAutomationDefaultMonitoringAgentLogFile() {
    return getStrProp(
        Fields.AUTOMATION_DEFAULT_MONITORING_AGENT_LOG_FILE.value,
        DEFAULT_MONITORING_AGENT_LOG_FILE);
  }

  public String getAutomationDefaultMonitoringAgentLogFileWindows() {
    return getStrProp(
        Fields.AUTOMATION_DEFAULT_MONITORING_AGENT_LOG_FILE_WINDOWS.value,
        DEFAULT_MONITORING_AGENT_LOG_FILE_WINDOWS);
  }

  public String getAutomationDefaultBackupAgentLogFile() {
    return getStrProp(
        Fields.AUTOMATION_DEFAULT_BACKUP_AGENT_LOG_FILE.value, DEFAULT_BACKUP_AGENT_LOG_FILE);
  }

  public String getAutomationDefaultCpsModuleLogFile() {
    return getStrProp(
        Fields.AUTOMATION_DEFAULT_CPS_MODULE_LOG_FILE.value, DEFAULT_CPS_MODULE_LOG_FILE);
  }

  public String getAutomationDefaultOnlineArchiveModuleLogFile() {
    return getStrProp(
        Fields.AUTOMATION_DEFAULT_ONLINE_ARCHIVE_MODULE_LOG_FILE.value,
        DEFAULT_ONLINE_ARCHIVE_MODULE_LOG_FILE);
  }

  public String getAutomationDefaultDbCheckModuleLogFile() {
    return getStrProp(
        Fields.AUTOMATION_DEFAULT_ONLINE_ARCHIVE_MODULE_LOG_FILE.value,
        DEFAULT_DB_CHECK_MODULE_LOG_FILE);
  }

  public String getAutomationDefaultBackupAgentLogFileWindows() {
    return getStrProp(
        Fields.AUTOMATION_DEFAULT_BACKUP_AGENT_LOG_FILE_WINDOWS.value,
        DEFAULT_BACKUP_AGENT_LOG_FILE_WINDOWS);
  }

  public int getAutomationAgentPromInternalPort() {
    return getIntProp(Fields.AUTOMATION_AGENT_PROM_INTERNAL_PORT.value, 8091);
  }

  public String getMongoSqldDefaultLogFile() {
    return getStrProp(
        Fields.AUTOMATION_DEFAULT_MONGOSQLD_LOG_FILE.value, DEFAULT_MONGOSQLD_LOG_FILE);
  }

  public String getMongoSqldDefaultLogFileWindows() {
    return getStrProp(
        Fields.AUTOMATION_DEFAULT_MONGOSQLD_LOG_FILE_WINDOWS.value,
        DEFAULT_MONGOSQLD_LOG_FILE_WINDOWS);
  }

  public String getAutomationDefaultCAFile() {
    return getStrProp(Fields.AUTOMATION_DEFAULT_CA_FILE.value, null);
  }

  public String getAutomationDefaultCAFileWindows() {
    return getStrProp(Fields.AUTOMATION_DEFAULT_CA_FILE_WINDOWS.value, null);
  }

  public String getAutomationDefaultDataRoot() {
    return getStrProp(Fields.AUTOMATION_DEFAULT_DATA_ROOT.value, DEFAULT_DATA_ROOT);
  }

  public int getStaleMultiFactorAuthCodeLimit() {
    return getIntProp(Fields.STALE_AUTH_CODE_LIMIT.value, 2);
  }

  public boolean isAlertSystemEnabled() {
    final boolean alertsEnabled = SysProp.bool(Property.XGEN_ALERTS_ENABLED, true);
    return getBoolProp(Fields.ALERTS_ENABLED.value, alertsEnabled);
  }

  public boolean isOFACEnabled() {
    return getBoolProp(Fields.OFAC_ENABLED.value, false);
  }

  public String getOFACNotificationEmailAddress() {
    return getStrProp(Fields.OFAC_NOTIFICATION_EMAIL_ADDRESS.value, null);
  }

  public String getOFACSupportEmailAddress() {
    return getStrProp(Fields.OFAC_SUPPORT_EMAIL_ADDRESS.value, null);
  }

  public String getPublishableKeyInc() {
    return getStrProp(Fields.STRIPE_PUB_KEY.value, null);
  }

  public String getPublishableKeyLtd() {
    return getStrProp(Fields.STRIPE_PUB_KEY_LTD.value, null);
  }

  public String getLowPrepaidCreditEmailAddress() {
    return getStrProp(Fields.LOW_PREPAID_CREDIT_EMAIL_ADDRESS.value, null);
  }

  public String getInvoicingStatusEmailAddress() {
    return getStrProp(Fields.BILLING_INVOICE_STATUS_EMAIL_ADDRESS.value, null);
  }

  public MultiFactorAuthLevel getMultiFactorAuthLevel() {
    final String rawValue = getStrProp(Fields.MULTI_FACTOR_AUTH_LEVEL.value, null);
    if (rawValue != null) {
      for (final MultiFactorAuthLevel value : MultiFactorAuthLevel.values()) {
        if (rawValue.equalsIgnoreCase(value.name())) {
          return value;
        }
      }
    }
    return MultiFactorAuthLevel.OFF;
  }

  public String getRecaptchaPrivateKey() {
    return getStrProp(Fields.RECAPTCHA_PRIVATE_KEY.value, null);
  }

  public String getRecaptchaPublicKey() {
    return getStrProp(Fields.RECAPTCHA_PUBLIC_KEY.value, null);
  }

  public boolean isAccountMultiFactorAuthEnabled() {
    return getBoolProp(Fields.ACCOUNT_MULTI_FACTOR_AUTH_ENABLED.value, false);
  }

  public boolean isMultiFactorAuthResetAllowed() {
    return getBoolProp(Fields.MULTI_FACTOR_AUTH_ALLOW_RESET.value, false);
  }

  public boolean isEmailVerificationEnabled() {
    return getBoolProp(Fields.EMAIL_VERIFICATION_ENABLED.value, false);
  }

  public String getDataExfiltrationLoadBalancerNames() {
    return getStrProp("nds.dataExfiltration.loadBalancerNames", "");
  }

  public boolean isSecurityBackdoorEnabled() {
    return getBoolProp(Fields.SECURITY_BACKDOOR.value, false);
  }

  public int getAlertsWebhookSocketTimeoutMs() {
    return getIntProp(Fields.ALERTS_WEBHOOK_SOCKET_TIMEOUT_MS.value, 2000);
  }

  public int getAlertsWebhookReadTimeoutMs() {
    return getIntProp(Fields.ALERTS_WEBHOOK_READ_TIMEOUT_MS.value, 5000);
  }

  public String getNDSSiteName() {
    return getStrProp(Fields.NDS_SITE_NAME.value, "Atlas");
  }

  public boolean getLiveMigrationServersHasPublicIp() {
    return getBoolProp(Fields.NDS_LIVE_MIGRATION_SERVERS_HAS_PUBLIC_IP.value, false);
  }

  public void setDryRunIntegrations(final boolean shouldDryRun) {
    setProp(
        Fields.DRY_RUN_INTEGRATION_DELETION.value,
        Boolean.toString(shouldDryRun),
        SettingType.DATABASE);
  }

  public boolean getDryRunIntegrations() {
    return getBoolProp(Fields.DRY_RUN_INTEGRATION_DELETION.value, true);
  }

  public String getGovSiteName() {
    return getStrProp(Fields.NDS_GOV_US_SITE_NAME.value, "Atlas Government");
  }

  public int getLeakedItemCleanupRetries() {
    return getIntProp(Fields.NDS_LEAKED_ITEM_CLEANUP_RETRIES.value, 3);
  }

  public int getLeakedItemDetectionRetries() {
    return getIntProp(Fields.NDS_LEAKED_ITEM_DETECTION_RETRIES.value, 5);
  }

  public String getLeakedItemCleanupMode() {
    return getStrProp(Fields.NDS_LEAKED_ITEM_CLEANUP_MODE.value, "CRON");
  }

  public String getAWSLeakedItemDetectionMode() {
    return getStrProp(Fields.NDS_AWS_LEAKED_ITEM_DETECTION_MODE.value, "CRON");
  }

  public String getAzureLeakedItemDetectionMode() {
    return getStrProp(Fields.NDS_AZURE_LEAKED_ITEM_DETECTION_MODE.value, "CRON");
  }

  public String getGCPLeakedItemDetectionMode() {
    return getStrProp(Fields.NDS_GCP_LEAKED_ITEM_DETECTION_MODE.value, "CRON");
  }

  public int getGCPLeakedItemDetectionChunkSize() {
    return getIntProp(Fields.NDS_GCP_LEAKED_ITEM_DETECTION_CHUNK_SIZE.value, 900);
  }

  public int getGCPLeakedItemDetectionProjectsProcessedPerIteration() {
    return getIntProp(
        Fields.NDS_GCP_LEAKED_ITEM_DETECTION_PROJECTS_PROCESSED_PER_ITERATION.value, 50);
  }

  public int getGCPLeakedItemDetectionScheduledForWindow() {
    return getIntProp(
        Fields.NDS_GCP_LEAKED_ITEM_DETECTION_JOB_SCHEDULED_FOR_WINDOW_MINUTES.value, 180);
  }

  public boolean getLeakedItemHandlerDryRunEnabled() {
    return getBoolProp(Fields.NDS_LEAKED_ITEM_HANDLER_ENABLE_DRY_RUN.value, true);
  }

  public Duration getDefaultStatusCheckRepeatDuration() {
    return Duration.ofSeconds(getLongProp(Fields.DEFAULT_STATUS_CHECK_INTERVAL.value, 240));
  }

  public boolean getNDSFastSharedTierEnabled() {
    return getBoolProp(Fields.NDS_FAST_SHARED_TIER_ENABLED.value, false);
  }

  public boolean isNDSFixedVersionsVersionManagerUIEnabled() {
    return getBoolProp(Fields.NDS_FIXED_VERSIONS_VERSION_MANAGER_UI_ENABLED.value, false);
  }

  public boolean getNDSFastServerlessProvisioningEnabled() {
    return getBoolProp(Fields.NDS_FAST_SERVERLESS_PROVISIONING_ENABLED.value, false);
  }

  public boolean getNDSFastFlexProvisioningEnabled() {
    return getBoolProp(Fields.NDS_FAST_FLEX_PROVISIONING_ENABLED.value, false);
  }

  public boolean getNDSFlexModeForServerlessEnabled() {
    return getBoolProp(Fields.NDS_FLEX_MODE_FOR_SERVERLESS_ENABLED.value, false);
  }

  public int getNDSServerlessMTMDrainTimeBuffer() {
    return getIntProp(Fields.NDS_SERVERLESS_MTM_DRAIN_TIME_BUFFER.value, 12);
  }

  public float getNDSServerlessMergeStrategyCpuUtilizationThreshold() {
    return getFloat(Fields.NDS_SERVERLESS_MERGE_STRATEGY_CPU_UTILIZATION_THRESHOLD.value, 0.01f);
  }

  public boolean getNDSFastPrecreateCronSvcEnabled() {
    return getBoolProp(Fields.NDS_FAST_PRECREATE_CRON_SVC_ENABLED.value, false);
  }

  public int getMaxCreatingFastSharedRecordsPerRegion() {
    return getIntProp(Fields.NDS_MAX_FAST_SHARED_RECORDS_PER_REGION.value, 1);
  }

  public int getMaxCreatingFastFlexRecordsPerRegion() {
    return getIntProp(Fields.NDS_MAX_FAST_FLEX_RECORDS_PER_REGION.value, 1);
  }

  public String getFlexApiSharedMode() {
    return getStrProp(Fields.NDS_FLEX_API_SHARED_MODE.value, "EXISTING");
  }

  public String getFlexApiServerlessMode() {
    return getStrProp(Fields.NDS_FLEX_API_SERVERLESS_MODE.value, "EXISTING");
  }

  public boolean getNDSGovUSEnabled() {
    return getBoolProp(Fields.NDS_GOV_US_ENABLED.value, false);
  }

  public Optional<String> getGovGCPKeyProjectName() {
    return getOptionalStrProp(Fields.NDS_GOV_US_GCP_CMEK_PROJECT_NAME.value);
  }

  public boolean getNDSGatewayProxyEnabled() {
    return getBoolProp(Fields.NDS_GW_PROXY_ENABLED.value, false);
  }

  // if false, we will not auto--scale base compute/analytics nodes.
  public boolean getComputeAutoScalingEnabled() {
    return getBoolProp(Fields.NDS_AUTO_SCALING_COMPUTE_ENABLED.value, true);
  }

  // to enable autoscaling with burstable thresholds.
  public boolean getBurstableAutoScalingNewClusterEnabled() {
    return getBoolProp(Fields.NDS_BURSTABLE_AUTO_SCALING_NEW_CLUSTER_ENABLED.value, false);
  }

  public int getPlanStateInMemoryObjectRolloutPercentage() {
    return getIntProp(Fields.NDS_PLAN_STATE_IN_MEMORY_OBJECT_ROLLOUT_PERCENTAGE.value, 0);
  }

  public boolean getPlanStateInMemoryObjectReadsEnabled() {
    return getBoolProp(Fields.NDS_PLAN_STATE_IN_MEMORY_OBJECT_READS_ENABLED.value, false);
  }

  public ZoneId getAutoScalingScaleDownTimezone() {
    return ZoneId.of(
        getStrProp(Fields.NDS_AUTO_SCALING_SCALE_DOWN_TIME_ZONE.value, "America/New_York"));
  }

  public String getAutoScalingScaleDownDays() {
    return getStrProp(
        Fields.NDS_AUTO_SCALING_SCALE_DOWN_DAYS.value,
        "MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY");
  }

  public String getAutoScalingScaleDownHours() {
    return getStrProp(Fields.NDS_AUTO_SCALING_SCALE_DOWN_HOURS.value, "9:00, 17:00");
  }

  public int getAutoScalingMonitoringIngestionQueueSize() {
    return getIntProp(Fields.NDS_AUTO_SCALING_MONITORING_INGESTION_QUEUE_SIZE.value, 400);
  }

  public Duration getAutoScalingMonitoringIngestionQueueOfferTimeMillis() {
    return Duration.ofMillis(
        getLongProp(Fields.NDS_AUTO_SCALING_MONITORING_INGESTION_OFFER_TIME_MILLIS.value, 1));
  }

  public int getAutoScalingMonitoringIngestionPoolSize() {
    return getIntProp(Fields.NDS_AUTO_SCALING_MONITORING_INGESTION_POOL_SIZE.value, 1);
  }

  public boolean getAutoScalingDownScaleCheckCloudProviderCapacityConstraint() {
    return getBoolProp(
        Fields.NDS_AUTO_SCALING_DOWN_SCALE_CHECK_CLOUD_PROVIDER_CONSTRAINTS.value, false);
  }

  public boolean getNDSAutoScaleMTMCapacityEnabled() {
    return getBoolProp(Fields.NDS_MTM_AUTOSCALE_CAPACITY_ENABLED.value, false);
  }

  public boolean getNDSAutoScaleServerlessMTMCapacityEnabled() {
    return getBoolProp(Fields.NDS_SERVERLESS_MTM_AUTOSCALE_CAPACITY_ENABLED.value, false);
  }

  public boolean getNDSAutoScaleFlexMTMCapacityEnabled() {
    return getBoolProp(Fields.NDS_FLEX_MTM_AUTOSCALE_CAPACITY_ENABLED.value, false);
  }

  public boolean getNDSUpdateServerlessMTMLoadDocuments() {
    return getBoolProp(Fields.NDS_MTM_UPDATE_SERVERLESS_LOAD_DOCUMENTS_ENABLED.value, false);
  }

  public boolean getNDSMetricsDeliverySystemForBillingEnabled() {
    return getBoolProp(Fields.NDS_METRICS_DELIVERY_SYSTEM_FOR_BILLING_ENABLED.value, false);
  }

  public boolean getServerlessRestoreVMUsageBillingEnabled() {
    return getBoolProp(Fields.SERVERLESS_RESTORE_VM_USAGE_BILLING_ENABLED.value, true);
  }

  public boolean getNDSServerlessMetricsUseMaxIops() {
    return getBoolProp(Fields.NDS_SERVERLESS_METRICS_USE_MAX_IOPS.value, false);
  }

  public boolean isDatabaseIdpDiscoveryEnabled() {
    return getBoolProp(Fields.DATABASE_IDP_DISCOVERY_ENABLED.value, false);
  }

  public String getDatabaseIdpDiscoveryMongoOktaId() {
    return getStrProp(Fields.DATABASE_IDP_DISCOVERY_MONGO_OKTA_ID.value, null);
  }

  public boolean isAtlasStreamsAlertingEnabled() {
    return getBoolProp(Fields.ATLAS_STREAMS_ALERTING_ENABLED.value, true);
  }

  public boolean getNDSUpdateServerlessTenantLoadDocuments() {
    return getBoolProp(Fields.NDS_TENANT_UPDATE_SERVERLESS_LOAD_DOCUMENTS_ENABLED.value, false);
  }

  public long getNDSSharedProxyDbstatsCacheMillis() {
    return getLongProp(Fields.NDS_SHARED_PROXY_DB_CACHE_MILLIS.value, 600000L);
  }

  public long getNDSServerlessProxyDbstatsCacheMillis() {
    return getLongProp(Fields.NDS_SERVERLESS_PROXY_DB_CACHE_MILLIS.value, 240000L);
  }

  public int getNDSSharedMtmSocketTimeoutSecs() {
    return getIntProp(Fields.NDS_SHARED_PROXY_MTM_SOCKET_TIMEOUT_SECS.value, 30);
  }

  public int getNDSServerlessMtmSocketTimeoutSecs() {
    return getIntProp(Fields.NDS_SERVERLESS_PROXY_MTM_SOCKET_TIMEOUT_SECS.value, 21600);
  }

  public boolean getAllowSetupServerlessCreateTestInstance() {
    return getBoolProp(
        Fields.NDS_SERVERLESS_ALLOW_SETUP_SERVERLESS_CREATE_TEST_INSTANCE.value, false);
  }

  public boolean isServerlessEnabled() {
    return getBoolProp(Fields.NDS_SERVERLESS_FEATURE_ENABLED.value, false);
  }

  public boolean isFlexEnabled() {
    return getBoolProp(Fields.NDS_FLEX_FEATURE_ENABLED.value, false);
  }

  public boolean isFlexMigrationWarningEnabled() {
    return getBoolProp(Fields.NDS_FLEX_MIGRATION_WARNING_ENABLED.value, false);
  }

  public boolean isServerlessTraceAutoScaleEnabled() {
    return getBoolProp(Fields.NDS_SERVERLESS_TRACE_AUTOSCALE_ENABLED.value, false);
  }

  public boolean isServerlessUpgradeToDedicatedEnabled() {
    return getBoolProp(Fields.NDS_SERVERLESS_UPGRADE_TO_DEDICATED_ENABLED.value, false);
  }

  public Optional<String> getServerlessTraceAutoScaleExporter() {
    return getOptionalStrProp(Fields.NDS_SERVERLESS_TRACE_AUTOSCALE_EXPORTER.value);
  }

  // Note: Used to prefix non-standard application specific open telemetry attribute names.
  public String getNDSServerlessTraceAutoScalePrefix() {
    final String prefix =
        getStrProp(
            Fields.NDS_SERVERLESS_TRACE_AUTOSCALE_PREFIX.value,
            "com.xgen.nds.serverless.autoscale");
    return StringUtils.isEmpty(prefix) ? "com.xgen.nds.serverless.autoscale" : prefix;
  }

  public Optional<String> getServerlessTraceAutoScaleProcessor() {
    return getOptionalStrProp(Fields.NDS_SERVERLESS_TRACE_AUTOSCALE_PROCESSOR.value);
  }

  public boolean isNDSPlanningTraceEnabled() {
    return getBoolProp(Fields.NDS_PLANNING_TRACE_ENABLED.value, false);
  }

  public boolean isNDSPlanningOtelTraceExportEnabled() {
    return getBoolProp(Fields.NDS_PLANNING_OTEL_EXPORT_ENABLED.value, false);
  }

  public int getNDSPlanningTraceExportLimit() {
    return getIntProp(Fields.NDS_PLANNING_TRACE_EXPORT_LIMIT.value, 0);
  }

  public int getNDSPlanningMinGroupCountBeforeExport() {
    return getIntProp(Fields.NDS_PLANNING_TRACE_MIN_GROUP_COUNT_BEFORE_EXPORT.value, 3000);
  }

  public long getNDSPlanningTraceDurationThresholdMillis() {
    return getLongProp(Fields.NDS_PLANNING_TRACE_DURATION_THRESHOLD_MILLIS.value, 10000);
  }

  public int getNDSPlanningTraceReportRootSpanLimit() {
    return getIntProp(Fields.NDS_PLANNING_TRACE_REPORT_ROOT_SPAN_LIMIT.value, 5);
  }

  public boolean getNDSPlanningTraceReportEnabled() {
    return getBoolProp(Fields.NDS_PLANNING_TRACE_REPORT_ENABLED.value, false);
  }

  public String getNDSPlanningTraceReportRecipientEmailAddr() {
    return getStrProp(
        Fields.NDS_PLANNING_TRACE_REPORT_RECIPIENT_EMAIL_ADDR.value, "<EMAIL>");
  }

  public boolean supportsLBDeploymentIdOnEnvoyInstance() {
    return getBoolProp(
        Fields.NDS_SERVERLESS_SUPPORTS_LB_DEPLOYMENT_ID_ON_ENVOY_INSTANCE.value, false);
  }

  public Optional<ObjectId> getNDSMTMOrganizationId() {
    final String orgId = getStrProp(Fields.NDS_MTM_ORGANIZATION_ID.value, null);
    return StringUtils.isBlank(orgId) ? Optional.empty() : Optional.of(new ObjectId(orgId));
  }

  public int getNDSLeakedItemsProcessorMinimumItemLifeSeconds() {
    return getIntProp(
        Fields.NDS_LEAKED_LEAKED_ITEMS_PROCESSOR_MINIMUM_ITEM_LIFE_SECONDS.value, 30 * 60);
  }

  public int getNdsSearchMetricsMaxSearchIndexWriteCount() {
    return getIntProp(Fields.NDS_SEARCH_METRICS_MAX_SEARCH_INDEX_WRITE_COUNT.value, 100);
  }

  public Optional<ObjectId> getNDSServerlessMTMOrganizationId() {
    final String orgId = getStrProp(Fields.NDS_SERVERLESS_ORGANIZATION_ID.value, null);
    return StringUtils.isBlank(orgId) ? Optional.empty() : Optional.of(new ObjectId(orgId));
  }

  public Optional<ObjectId> getNDSFlexMTMOrganizationId() {
    final String orgId = getStrProp(Fields.NDS_FLEX_ORGANIZATION_ID.value, null);
    return StringUtils.isBlank(orgId) ? Optional.empty() : Optional.of(new ObjectId(orgId));
  }

  public Optional<ObjectId> getNDSServerlessSentinelOrganizationId() {
    final String orgId = getStrProp(Fields.NDS_SERVERLESS_SENTINEL_ORGANIZATION_ID.value, null);
    return StringUtils.isBlank(orgId) ? Optional.empty() : Optional.of(new ObjectId(orgId));
  }

  public Optional<ObjectId> getNDSSharedSentinelOrganizationId() {
    final String orgId = getStrProp(Fields.NDS_SHARED_SENTINEL_ORGANIZATION_ID.value, null);
    return StringUtils.isBlank(orgId) ? Optional.empty() : Optional.of(new ObjectId(orgId));
  }

  public Optional<ObjectId> getNDSFlexSentinelOrganizationId() {
    final String orgId = getStrProp(Fields.NDS_FLEX_SENTINEL_ORGANIZATION_ID.value, null);
    return StringUtils.isBlank(orgId) ? Optional.empty() : Optional.of(new ObjectId(orgId));
  }

  public boolean getMTMSentinelCronEnabled() {
    return getBoolProp(Fields.NDS_MTM_SENTINEL_CRON_ENABLED.value, false);
  }

  public <E extends Enum<E>> int getNDSMTMLowThreshold(final E pTenantInstanceSize) {
    final String fieldPrefix = "nds.mtm.lowThreshold";
    final boolean isInstanceSizeM2M5 =
        pTenantInstanceSize.name().equalsIgnoreCase("m2")
            || pTenantInstanceSize.name().equalsIgnoreCase("m5");
    return getIntProp(
        String.format("%s.%s", fieldPrefix, pTenantInstanceSize.name().toLowerCase()),
        isInstanceSizeM2M5 ? 0 : 50);
  }

  public <E extends Enum<E>, P extends Enum<P>> int getFastProvisionedInstanceCount(
      final E pTenantInstanceSize, final P pBackingCloudProvider, final String pRegionName) {
    final String fieldPrefix = "nds.mtm.maxReservedTenants";
    final String limitPerTenantConfiguration =
        String.format(
            "%s.%s.%s.%s",
            fieldPrefix,
            pBackingCloudProvider.name().toLowerCase(),
            pRegionName.toLowerCase(),
            pTenantInstanceSize.name().toLowerCase());
    final String limitPerInstanceSize =
        String.format("%s.%s", fieldPrefix, pTenantInstanceSize.name().toLowerCase());

    return getIntProp(limitPerTenantConfiguration, getIntProp(limitPerInstanceSize, 0));
  }

  public String getNDSACMECertificateChainFilename() {
    return getStrProp(Fields.NDS_ACME_CERTIFICATE_CHAIN_FILE.value);
  }

  public String getNDSACMETrackingPublicKey() {
    return getStrProp(Fields.NDS_ACME_ORDER_TRACKING_PUBLIC_KEY.value, "");
  }

  public String getNDSACMETrackingPrivateKey() {
    return getStrProp(Fields.NDS_ACME_ORDER_TRACKING_PRIVATE_KEY.value, "");
  }

  public String getGenericSiteName() {
    return getSiteNameForGroupType(null);
  }

  public <E extends Enum<E>> String getSiteNameForGroupType(final E pGroupType) {
    if (pGroupType == null) {
      return getStrProp(Fields.SITE_NAME.value, "Cloud");
    }

    return getStrProp(
        Fields.SITE_NAME.value + "." + pGroupType.name().toLowerCase(), "Cloud Manager");
  }

  public String getGenericSiteFullName() {
    return getSiteFullNameForGroupType(null);
  }

  public <E extends Enum<E>> String getSiteFullNameForGroupType(final E pGroupType) {
    if (pGroupType == null) {
      return getStrProp(Fields.SITE_FULL_NAME.value, "MongoDB Cloud");
    }

    return getStrProp(
        Fields.SITE_FULL_NAME.value + "." + pGroupType.name().toLowerCase(),
        "MongoDB Cloud Manager");
  }

  private String getEmailLogo() {
    return getStrProp(Fields.EMAIL_LOGO.value, "/images/static/logo-mongodb.png");
  }

  private int getEmailLogoWidth() {
    return getIntProp(Fields.EMAIL_LOGO_WIDTH.value, 200);
  }

  private int getEmailLogoHeight() {
    return getIntProp(Fields.EMAIL_LOGO_HEIGHT.value, 41);
  }

  public EmailValidation getEmailValidationMode() {
    final String stringProperty = getStrProp(Fields.EMAIL_VALIDATION.value, null);
    if (stringProperty != null) {
      for (final EmailValidation mode : EmailValidation.values()) {
        if (stringProperty.equalsIgnoreCase(mode.name())) {
          return mode;
        }
      }
    }
    return EmailValidation.NONE;
  }

  public boolean countryRequired() {
    return getBoolProp(Fields.MMS_ADD_USER_API_COUNTRY_REQUIRED.value, true);
  }

  public boolean shouldCacheStaticAssets() {
    return getBoolProp(Fields.UI_CACHE_STATIC.value, true);
  }

  public boolean shouldUseS3InIntTests() {
    final boolean shouldUseS3InIntTests =
        getBoolProp(Fields.BACKUP_USE_S3_IN_INT_TESTS.value, true);
    if (!shouldUseS3InIntTests) {
      LOG.info(
          "Use mock S3 bucket in brs int tests because mms.backup.useS3InIntTests is set to"
              + " false.");
    }
    return shouldUseS3InIntTests;
  }

  public boolean shouldDetectVersionMismatch() {
    return getBoolProp(Fields.DETECT_VERSION_MISMATCH.value, true);
  }

  public boolean isGenerativeAIForCompassEnabled() {
    return getBoolProp(Fields.GEN_AI_COMPASS.value, false);
  }

  public boolean isGenerativeAIForCompassUnauthenticatedEnabled() {
    return getBoolProp(Fields.GEN_AI_COMPASS_UNAUTHENTICATED.value, false);
  }

  public boolean isGenerativeAIForDataExplorerEnabled() {
    return getBoolProp(Fields.GEN_AI_DATA_EXPLORER.value, false);
  }

  public boolean shouldIgnoreVersionMismatch() {
    return getBoolProp(Fields.IGNORE_VERSION_MISMATCH.value, false);
  }

  public long getAppVersionPingInterval() {
    return getLongProp(Fields.APP_VERSION_PING_INTERVAL.value);
  }

  public TimeUnit getAppVersionPingIntervalUnit() {
    final String unit = getStrProp(Fields.APP_VERSION_PING_INTERVAL_UNIT.value);
    return TimeUnit.valueOf(unit);
  }

  public long getAppVersionPingToStale() {
    return getLongProp(Fields.APP_VERSION_PINGS_TO_STALE.value);
  }

  public boolean isLegacyUsernameMigrationEnabled() {
    return getBoolProp("mms.legacyUsernameMigration.enabled", false);
  }

  public String getEloquaSiteId() {
    return getStrProp("eloqua.siteId", "");
  }

  public Boolean getEloquaSyncEnabled() {
    return getBoolProp("eloqua.sync.enabled", false);
  }

  public boolean isGoogleAuthEnabled() {
    return getBoolProp("mms.auth.methods.google.enabled", false);
  }

  public boolean isGithubAuthEnabled() {
    return getBoolProp("mms.auth.methods.github.enabled", false);
  }

  public boolean isSocialProviderEnabled(final String pSocialProvider) {
    final String enabledProp = String.format("mms.auth.methods.%s.enabled", pSocialProvider);
    return getBoolProp(enabledProp, false);
  }

  // These settings override the account or cloud central URL to run mms locally with a different
  // central URL (e.g. an ngrok URL to run atlas locally)
  public String getAccountCentralUrlOverride() {
    final var propOverride = getStrProp("account.centralUrl.override", "");
    return StringUtils.isEmpty(propOverride) ? getAccountCentralUrl() : propOverride;
  }

  public String getCloudCentralUrlOverride() {
    final var propOverride = getStrProp("mms.centralUrl.override", "");
    return StringUtils.isEmpty(propOverride) ? getCentralUrl() : propOverride;
  }

  public String getAgentCentralUrlOverride() {
    final var propOverride = getStrProp("mms.agentCentralUrl.override", "");
    return StringUtils.isEmpty(propOverride) ? getAgentCentralUrl() : propOverride;
  }

  public boolean isOktaNonceRequired() {
    return getBoolProp("okta.requireNonce", true);
  }

  public Set<String> getOktaUsersGroupIds() {
    return splitPropValueOnCommas(Fields.OKTA_USER_GROUP_IDS.value);
  }

  public boolean isAccountSuspensionEnabled() {
    return getBoolProp(Fields.ACCOUNT_SUSPENSION_ENABLED.value, false);
  }

  public boolean isAccountDeletionEnabled() {
    return getBoolProp(Fields.ACCOUNT_DELETION_ENABLED.value, false);
  }

  public boolean isSendAccountDeletionEmailEnabled() {
    return getBoolProp(Fields.SEND_ACCOUNT_DELETION_EMAIL_ENABLED.value, false);
  }

  public boolean isNewAdminUsersPageEnabled() {
    return getBoolProp(Fields.NEW_ADMIN_USERS_PAGE_ENABLED.value, false);
  }

  public boolean isCommentServiceEnabled() {
    return getBoolProp(Fields.COMMENT_SERVICE_ENABLED.value, false);
  }

  public boolean isServerlessProfilingFilterEnabled() {
    return getBoolProp("mms.monitoring.serverlessProfilingFilter.enabled", false);
  }

  public boolean isAuthzServiceEnabled() {
    return getBoolProp(Fields.AUTHZ_SERVICE_ENABLED.value, false);
  }

  public boolean isAuthzServiceClassOverrideEnabled() {
    return getBoolProp(Fields.AUTHZ_SERVICE_CLASS_OVERRIDE_ENABLED.value, false);
  }

  public String getConfigServiceGrpcAddress() {
    // CONFIG_SERVICE_GRPC_ADDRESS is populated in local envs, CONFIG_SERVICE_GRPC_ADDRESS_ENV_VAR
    // is populated in higher (Helix) envs
    String url = getStrProp(CONFIG_SERVICE_GRPC_ADDRESS.value, null);
    if (url == null) {
      url = System.getenv(getStrProp(Fields.CONFIG_SERVICE_GRPC_ADDRESS_ENV_VAR.value));
    }

    return url;
  }

  public ConfigServiceSdkWrapper getConfigServiceSdkWrapper() {
    return configServiceSdkWrapper;
  }

  public boolean isAuthzSyncResourcesEnabled() {
    return getBoolProp(Fields.AUTHZ_SERVICE_SYNC_RESOURCES.value, false);
  }

  public boolean isAuthzSyncTeamsToUserGroupsEnabled() {
    return getBoolProp(Fields.AUTHZ_SERVICE_SYNC_TEAMS_TO_USER_GROUPS.value, false);
  }

  public int getUserGroupSyncFailureJobNumberRetries() {
    return getIntProp(Fields.USER_GROUP_SYNC_FAILURE_JOB_NUM_RETRIES.value, 3);
  }

  public int getUserGroupSyncFailureJobRetryDelayInMin() {
    return getIntProp(Fields.USER_GROUP_SYNC_FAILURE_JOB_RETRY_DELAY_MIN.value, 3);
  }

  // consider using NdsClusterSvc.getAzureSsdV2Regions(Group) instead
  public List<String> getAzureSsdV2Regions() {
    return getListProperty(Fields.AZURE_SSD_V2_REGIONS.value, COMMA_DELIMITER);
  }

  // consider using NdsClusterSvc.getAzureSsdV2Regions(Group) instead
  public List<String> getAzureSsdV2RolloutRegions() {
    return getListProperty(Fields.AZURE_SSD_V2_ROLLOUT_REGIONS.value, COMMA_DELIMITER);
  }

  // consider using NdsClusterSvc.getAzureSsdV2Regions(Group) instead
  public List<String> getAzureSsdV2PreviewRegions() {
    return getListProperty(Fields.AZURE_SSD_V2_PREVIEW_REGIONS.value, COMMA_DELIMITER);
  }

  // these are dev/qa/stage regions that we can currently create Azure clusters in, but do not yet
  // have storage accounts or entries in the blobstores or azureStorageAccounts collections for:
  // CLOUDP-225188
  public List<String> getAzureOplogStoreUnsupportedRegions() {
    return getListProperty(Fields.NDS_AZURE_OPLOG_STORE_UNSUPPORTED_REGIONS.value, COMMA_DELIMITER);
  }

  public Optional<ObjectId> getCapacityReservationCronInternalProjectId() {
    final Optional<String> projectId =
        getOptionalStrProp(Fields.NDS_CAPACITY_RESERVATION_CRON_PROJECT.value);
    return projectId.map(ObjectId::new);
  }

  public boolean shouldRunFgaOverRbacWhenAnnotated() {
    return getBoolProp(Fields.RUN_FGA_OVER_RBAC_WHEN_ANNOTATED.value, false);
  }

  public boolean isVercelIntegrationEnabled() {
    return getBoolProp(Fields.VERCEL_INTEGRATION_ENABLED.value, false);
  }

  public String getVercelClientId() {
    return getOptionalStrProp(Fields.VERCEL_CLIENT_ID.value).orElse("");
  }

  public String getVercelSecret() {
    return getOptionalStrProp(Fields.VERCEL_SECRET.value).orElse("");
  }

  public boolean isBillingEnabled() {
    return getBoolProp(Fields.BILLING_ENABLED.value, false);
  }

  public boolean isBillingCronJobsDisabled() {
    return getBoolProp(Fields.BILLING_CRON_JOBS_DISABLED.value, false);
  }

  public boolean isBillingAuditorsJiraIntegrationEnabled() {
    return getBoolProp(Fields.BILLING_AUDITORS_JIRA_INTEGRATION_ENABLED.value, false);
  }

  /** When behind a load balancer this header contains the real client client IP address */
  public String getRemoteIPHeader() {
    return getStrProp(Fields.REMOTE_IP_HEADER.value, null);
  }

  public Optional<Integer> getMaxRequestHeaderSizeInBytes() {
    try {
      return Optional.of(getIntProp(MMS_HTTP_MAX_REQUEST_HEADER_SIZE_IN_BYTES.value));
    } catch (final IllegalStateException e) {
      return Optional.empty();
    }
  }

  public String getFromEmailAddress() {
    return getStrProp("mms.fromEmailAddr");
  }

  public String getReplyToEmailAddress() {
    return getStrProp("mms.replyToEmailAddr");
  }

  public String getReplyToSuspensionAdminEmailAddress() {
    return getStrProp("mms.replyToSuspensionAdminEmailAddr");
  }

  public String getAdminEmailAddress() {
    return getStrProp("mms.adminEmailAddr");
  }

  public String getSecurityEmailAddress() {
    return getStrProp("mms.securityEmailAddr", "<EMAIL>");
  }

  public String getApiDeprecationFromEmailAddress() {
    return getStrProp("mms.atlasApiDeprecation.fromEmailAddr", "<EMAIL>");
  }

  public String getApiDeprecationReplyToEmailAddress() {
    return getStrProp("mms.atlasApiDeprecation.replyToEmailAddr", "<EMAIL>");
  }

  public String getAtlasAdminEmailAddress() {
    return getStrProp("nds.adminEmailAddress", "<EMAIL>");
  }

  public String getAtlasClusterWebsocketConnectionBaseUrl() {
    return getStrProp(Fields.ATLAS_CLUSTER_WEBSOCKET_CONNECTION_BASE_URL.value, "");
  }

  public <E extends Enum<E>> String getAlertFromEmailAddress(final E pGroupType) {
    if (pGroupType != null) {
      return getStrProp(
          "mms.alertFromEmailAddr." + pGroupType.name().toLowerCase(), getFromEmailAddress());
    }
    // GroupType==null occurs for System Alert emails and Global Alert Digests
    else {
      return getFromEmailAddress();
    }
  }

  public <E extends Enum<E>> String getAlertReplyToEmailAddress(final E pGroupType) {
    if (pGroupType != null) {
      return getStrProp(
          "mms.alertReplyToEmailAddr." + pGroupType.name().toLowerCase(), getReplyToEmailAddress());
    }
    // GroupType==null occurs for System Alert emails and Global Alert Digests
    else {
      return getReplyToEmailAddress();
    }
  }

  public String getBackupAlertFromEmailAddress() {
    return getStrProp("mms.backup.alertsEmailAddr", getFromEmailAddress());
  }

  public int getBackupDefaultBatchSizeForFileListInsert() {
    return getIntProp("mms.backup.defaultFileListInsertBatchSize", 10);
  }

  public int getGlobalAlertsSummaryEmailIntervalHours() {
    return _globalAlertsSummaryEmailIntervalHours;
  }

  public int getQueryableMemoryQuotaMB() {
    return getIntProp(Fields.RESTORE_QUERYABLE_MONGOD_MEMORYQUOTAMB.value, 8192);
  }

  public long getWTIncrementalBlockCopierTimeoutMinutes() {
    // long-ish timeout to allow for lots of disk I/O, but still bounded by agent request timeout
    return getLongProp("brs.wtcheckpoint.incrementalBlockCopier.timeoutMinutes", 10L);
  }

  public int getWTIncrementalBlockCopierBatchSize() {
    return getIntProp("brs.wtcheckpoint.incrementalBlockCopier.batchSize", 500);
  }

  public int getWTIncrementalBlockCopierThreadCount() {
    return getIntProp("brs.wtcheckpoint.incrementalBlockCopier.threadCount", 8);
  }

  public int getWTIncrementalMaxNumDaysBeforeFullIncremental() {
    return getIntProp("brs.wtcheckpoint.fullIncrementalDayOfWeek.maxDays", 9);
  }

  public int getWTIncrementalMinNumDaysBeforeFullIncremental() {
    return getIntProp("brs.wtcheckpoint.fullIncrementalDayOfWeek.minDays", 2);
  }

  public int getWTS3BlockPoolMinThread(int inferredThreadCount) {
    return getIntProp(
        "brs.s3blockdao.saveblocks.thread.core",
        (int)
            (getWTBlockSavePoolMaxThreads(inferredThreadCount)
                * getWTS3BlockPoolMinThreadsConfigFactor()));
  }

  public int getWTS3BlockPoolMaxThread(int inferredThreadCount) {
    return getIntProp(
        "brs.s3blockdao.saveblocks.thread.max",
        (int)
            (getWTBlockSavePoolMaxThreads(inferredThreadCount)
                * getWTS3BlockPoolMaxThreadsConfigFactor()));
  }

  public double getWTS3BlockPoolMaxThreadsConfigFactor() {
    return getDoubleProp("brs.s3blockdao.saveblocks", 2);
  }

  public double getWTS3BlockPoolMinThreadsConfigFactor() {
    return getDoubleProp("brs.s3blockdao.saveblocks", 0.5);
  }

  public int getWTBlockSavePoolThreadCountPerCore() {
    return getIntProp("backup.wtcheckpoints.blocksavepool.threadCountPerCore", 2);
  }

  public int getWTBlockSavePoolMinThreads(int inferredThreadCount) {
    return getIntProp("backup.wtcheckpoints.blocksavepool.threads.core", inferredThreadCount);
  }

  public int getWTBlockSavePoolMaxThreads(int inferredThreadCount) {
    return getIntProp("backup.wtcheckpoints.blocksavepool.threads.max", inferredThreadCount);
  }

  public int getWTBlockSavePoolMaxDesiredSize(int inferredThreadCount) {
    return getIntProp(
        "backup.wtcheckpoints.blocksavepool.threads.maxDesiredSize",
        getWTBlockSavePoolMaxThreads(inferredThreadCount));
  }

  public int getWTReservationTimeoutMillis() {
    // A value of -1 means disabled
    return getIntProp("backup.wtcheckpoints.blocksavepool.threads.reservationTimeoutMillis", -1);
  }

  public int getWTInsertTimeoutMinutes() {
    return getIntProp("backup.wtcheckpoints.blocksavepool.insertTimeoutMinutes", 9);
  }

  public long getWTBlockSavePoolKeepAliveSeconds() {
    return getLongProp("backup.wtcheckpoints.blocksavepool.threads.keepAliveTimeSeconds", 0);
  }

  public int getBackupGzipCompressionLevel() {
    return getIntProp(Fields.BACKUP_GZIP_COMPRESSION_LEVEL.value, 1);
  }

  public int getBackupS3GzipCompressionLevel() {
    return getIntProp(Fields.BACKUP_S3_GZIP_COMPRESSION_LEVEL.value, 1);
  }

  public int getBackupDestroyVmBufferMinutes() {
    return getIntProp(Fields.BACKUP_DESTROY_VM_BUFFER_MINS.value, 10);
  }

  /**
   * Current threshold is very generous, will be used more as a debugging tool, somtehing that we
   * can enable as need be.
   */
  public long getBackupBlockSaveSlowThresholdMS() {
    return getLongProp("backup.wtcheckpoints.blockSaveSlowThresholdMS", 15000L);
  }

  public boolean getBackupAllowTargetingWithIpAddress() {
    return getBoolProp(Fields.BACKUP_WTC_ALLOW_TARGETING_WITH_IP_ADDRESS.value, true);
  }

  public List<String> getBackupForceFullSnapshotIntervals() {
    final String intervals =
        getStrProp(Fields.BACKUP_WTC_FORCE_FULL_SNAPSHOT_INTERVALS.value, "2419200");
    return Splitter.on(COMMA_DELIMITER).omitEmptyStrings().trimResults().splitToList(intervals);
  }

  /** The configured number of fetcher threads if specified, else NumCPUs + 12 */
  public int getNumSANFetcherThreads() {
    return getIntProp("brs.numSanFetcherThreads", Runtime.getRuntime().availableProcessors() + 12);
  }

  /**
   * The configured max block fetcher memory in KB if specified, else 16MB <= (fetcher
   * threads*2)*16MB <= maxMemory/25
   *
   * <p>The idea is that we want to ideally support fetcher threads * block size, plus a little
   * extra buffer, such that memory footprint is rarely a bottleneck on fetcher thread pool
   * utilization. However we also want to ensure we have enough memory to support up to 25 block
   * fetchers at once. So, if max heap memory / 25 is less than our ideal size, then we have no
   * choice -- use max heap memory / 25. But if we have at least enough heap to support (fetcher
   * threads*2) * 16MB, then use that amount, up to 1GB max.
   *
   * <p>Note that max memory is *not* the -Xmx value, but rather is more closely the size of the old
   * generation. So the max memory is roughly -Xmx minus the size of new generation, or typically
   * about 0.9 * -Xmx value.
   *
   * <p>Refer to unit test BlockFetcherUnitTests for value examples.
   */
  public int getMaxBlockFetcherMemoryKB() {
    return getIntProp(
        "brs.maxBlockFetcherMemoryKB",
        getInferredMaxBlockFetcherMemoryKB(
            getNumSANFetcherThreads(),
            getMaxBlockFetchersSupported(),
            Runtime.getRuntime().maxMemory()));
  }

  public int getMaxBlockFetchersSupported() {
    return getIntProp("brs.maxBlockFetchersSupported", 25);
  }

  public static int getInferredMaxBlockFetcherMemoryKB(
      final int fetcherThreads, final int maxBlockFetchersSupported, final long maxMemory) {
    final long oneBlockSizeKB = 16 * 1024; // 16MB
    final long maxSizeKB = 1024 * 1024; // 1GB
    final long idealMaxBlockFetcherMemoryKB = (fetcherThreads * 2L) * oneBlockSizeKB;

    final long maxAvailableHeapKB = maxMemory / 1024;
    final long availableMemoryPerFetcherKB = maxAvailableHeapKB / maxBlockFetchersSupported;

    return (int)
        Math.min(
            maxSizeKB,
            Math.max(
                oneBlockSizeKB,
                Math.min(availableMemoryPerFetcherKB, idealMaxBlockFetcherMemoryKB)));
  }

  /**
   * The configured max block fetcher memory in KB for Daemon Jobs (Rollback/Theft) if specified,
   * else 64MB
   *
   * <p>This restore usage differs from `getMaxBlockFetcherMemoryKB()` above in that it's aimed at
   * being conservative and small, given the possibility that many dozens of rollback/theft jobs may
   * be processed at once, such as from a bulk theft. (While the number of daemon workers provides
   * an upper bound on the number of rollback/theft's active per Daemon, still keeping this value
   * simple and independent).
   */
  public int getMaxDaemonJobBlockFetcherMemoryKB() {
    return getIntProp("brs.maxDaemonJobBlockFetcherMemoryKB", 64 * 1024 /*64MB*/);
  }

  public String getThirdPartyBaseOplogFilePath() {
    return getStrProp("brs.thirdparty.baseOplogFilePath", "");
  }

  public int getThirdPartyOplogFileTimeSpanSeconds() {
    return getIntProp("brs.thirdparty.oplogFileTimeSpanSeconds", 60);
  }

  public int getThirdPartyOplogSnapshotTimeout() {
    return getIntProp("brs.thirdparty.oplogSnapshotTimeoutMin", 30);
  }

  public int getThirdPartySnapshotTimeoutMinutes() {
    return getIntProp("brs.thirdparty.snapshotTimeoutMin", 30);
  }

  public int getThirdPartyOplogFileErrorDisplayTimeoutMinutes() {
    return getIntProp("brs.thirdparty.oplogFileErrorDisplayTimeoutMinutes", 15);
  }

  public boolean getThirdPartyRestoreTimeoutExtensionEnabled() {
    return getBoolProp("brs.thirdparty.restoreTimeoutExtensionEnabled", true);
  }

  public boolean getThirdPartySnapshotFailingEnabled() {
    return getBoolProp("brs.thirdparty.snapshotFailingEnabled", true);
  }

  /**
   * Format a URL. The address might end with "/".
   *
   * @param url The URL to normalize.
   * @return url without the tailing "/"
   */
  public static String normalizeCentralUrl(String url) {
    if (url != null) {
      url = url.trim();
      while (url.endsWith("/")) {
        url = url.substring(0, url.length() - 1);
      }
    }
    return url;
  }

  private AgentVersion getAgentVersion(final Fields pAgentVersionProperty, AgentType pAgentType) {
    final String version = getStrProp(pAgentVersionProperty.value);
    if (version == null) {
      return AgentVersion.LatestAgentVersion(pAgentType);
    }
    return new AgentVersion(version, pAgentType);
  }

  public VersionUtils.Version getBiConnectorVersion() {
    return VersionUtils.parse(getStrProp(Fields.BI_CONNECTOR_VERSION.value));
  }

  public VersionUtils.Version getAtlasBiConnectorVersion() {
    return VersionUtils.parse(getStrProp(Fields.ATLAS_BI_CONNECTOR_VERSION.value));
  }

  public VersionUtils.Version getBiConnectorMinimumVersion() {
    return VersionUtils.parse(getStrProp(Fields.BI_CONNECTOR_MINIMUM_VERSION.value));
  }

  public VersionUtils.Version getAwsGravitonMinimumMongoDBVersion() {
    return VersionUtils.parse(getStrProp(Fields.AWS_GRAVITON_MINIMUM_MONGODB_VERSION.value, "5.0"));
  }

  public VersionUtils.Version getEmbeddedConfigMinimumMongoDBVersion() {
    return VersionUtils.parse(
        getStrProp(Fields.EMBEDDED_CONFIG_MINIMUM_MONGODB_VERSION.value, "8.0"));
  }

  // Update this method when an MDB version exists that allows direct RS to embedded config
  // transitions
  public boolean mongoDBVersionSupportsDirectRSToEmbeddedTransition(
      final VersionUtils.Version pVersion) {
    return Optional.ofNullable(
            getStrProp(Fields.RS_DIRECT_TO_EMBEDDED_MINIMUM_MONGODB_VERSION.value, null))
        .map(VersionUtils::parse)
        .map(minSupportedVersion -> minSupportedVersion.isLessThanOrEqualTo(pVersion))
        .orElse(false);
  }

  public int getEmbeddedConfigMaxShardCount() {
    return getIntProp(Fields.EMBEDDED_CONFIG_MAX_SHARD_COUNT.value, 3);
  }

  public MongotVersion getMongotVersion() {
    return MongotVersion.parseBackwardsCompatible(
        getStrProp(Fields.MONGOT_VERSION.value, "0.0.0.0"));
  }

  public long getMongotMmsConfigUpdatePeriodSeconds() {
    return getLongProp(Fields.MONGOT_MMS_CONFIG_UPDATE_PERIOD_SECONDS.value, 20);
  }

  public VersionUtils.Version getAtlasProxyVersion() {
    return VersionUtils.parse(getStrProp(Fields.ATLAS_PROXY_VERSION.value, "0.0.0.0"));
  }

  public VersionUtils.Version getAtlasProxyMinimumVersion() {
    return VersionUtils.parse(getStrProp(Fields.ATLAS_PROXY_MINIMUM_VERSION.value, "0.0.0.0"));
  }

  public VersionUtils.Version getAtlasUserIdentityServiceVersion() {
    return VersionUtils.parse(getStrProp(Fields.ATLAS_UIS_VERSION.value, "0.0.0.0"));
  }

  public VersionUtils.Version getServerlessAgentVersion() {
    return VersionUtils.parse(
        getStrProp(Fields.AUTOMATION_SERVERLESS_AGENT_VERSION.value, "0.0.0.0"));
  }

  public AgentVersion getServerlessAgentMinimumVersion() {
    return getAgentVersion(
        Fields.AUTOMATION_SERVERLESS_AGENT_MINIMUM_VERSION, AgentType.SERVERLESS);
  }

  public boolean isDataExplorerRateLimitCacheEnabled() {
    return getBoolProp(Fields.DATA_EXPLORER_RATE_LIMIT_CACHE_ENABLED.value, true);
  }

  public boolean isDataExplorerRateLimitingEnabled() {
    return getBoolProp(Fields.DATA_EXPLORER_RATE_LIMIT_ENABLED.value, true);
  }

  public int getDataExplorerRateLimitCacheSize() {
    return getIntProp(Fields.DATA_EXPLORER_RATE_LIMIT_CACHE_SIZE.value, 1_000);
  }

  public int getDataExplorerRateLimitCacheExpirationMinutes() {
    return getIntProp(Fields.DATA_EXPLORER_RATE_LIMIT_CACHE_EXPIRATION_MINUTES.value, 5);
  }

  public int getDataExplorerRateLimit() {
    return getIntProp(Fields.DATA_EXPLORER_RATE_LIMIT.value, 100);
  }

  public boolean isAtlasRegionalDataEnabledWithDataRegionCheck() {
    return getDataRegion() != null && isAtlasRegionalDataEnabled();
  }

  public boolean isAtlasRegionalDataEnabled() {
    return getBoolProp(Fields.ATLAS_REGIONAL_DATA_ENABLED.value, false);
  }

  public boolean isRegionalLogMaintenanceJobsEnabled() {
    return getBoolProp(Fields.ATLAS_REGIONAL_MAINTENANCE_JOBS_ENABLED.value, false);
  }

  public VersionUtils.Version getServerlessProxyVersion() {
    return VersionUtils.parse(getStrProp(Fields.SERVERLESS_PROXY_VERSION.value, "0.0.0.0"));
  }

  public String getServerlessProxyLocation() {
    return getStrProp(Fields.SERVERLESS_PROXY_LOCATION.value, "");
  }

  public VersionUtils.Version getRamiVersion() {
    return VersionUtils.parse(getStrProp(Fields.RAMI_VERSION.value, "0.0.0"));
  }

  public String getRamiLocation() {
    return getStrProp(Fields.RAMI_LOCATION.value, "");
  }

  public int getRamiDataCollectorPeriodInSecs() {
    return getIntProp(Fields.RAMI_DATA_COLLECTOR_PERIOD_IN_SECS.value, 60);
  }

  public int getRamiMitigatorPeriodInSecs() {
    return getIntProp(Fields.RAMI_MITIGATOR_PERIOD_IN_SECS.value, 600);
  }

  public VersionUtils.Version getMongotuneVersion() {
    return VersionUtils.parse(getStrProp(Fields.MONGOTUNE_VERSION.value, "0.0.0"));
  }

  public VersionUtils.Version getMongotunePreviousVersion() {
    return VersionUtils.parse(getStrProp(Fields.MONGOTUNE_PREVIOUS_VERSION.value, "0.0.0"));
  }

  public String getMongotuneLocation() {
    return getStrProp(Fields.MONGOTUNE_LOCATION.value, "");
  }

  public String getMongotuneLogLevel() {
    return getStrProp(Fields.MONGOTUNE_LOGLEVEL.value, "DEBUG");
  }

  public String getMongotuneLogFileMode() {
    return getStrProp(Fields.MONGOTUNE_LOG_FILE_MODE.value, "Append");
  }

  public String getMongotuneRotateUserAfterAgentVersion() {
    return getStrProp(Fields.MONGOTUNE_ROTATE_USER_AFTER_AGENT_VERSION.value, "");
  }

  public VersionUtils.Version getAtlasStateCacheVersion() {
    return VersionUtils.parse(
        getStrProp(Fields.AUTOMATION_SERVERLESS_STATE_CACHE_VERSION.value, "0.0.0.0"));
  }

  public String getAtlasStateCacheLocation() {
    return getStrProp(Fields.AUTOMATION_SERVERLESS_STATE_CACHE_LOCATION.value, "");
  }

  public String getAutomationVersionDefaultRefreshUrl() {
    return getStrProp(Fields.AUTOMATION_VERSIONS_AUTO_REFRESH_URI.value, null);
  }

  public String getAutomationVersionAtlasRefreshUrl() {
    return getStrProp(
        Fields.AUTOMATION_VERSIONS_AUTO_REFRESH_ATLAS_URI.value,
        "classpath://atlas_mongodb_version_manifest.json");
  }

  public String getAutomationVersionCmRefreshUrl() {
    return getStrProp(
        Fields.AUTOMATION_VERSIONS_AUTO_REFRESH_CM_URI.value,
        "classpath://cm_mongodb_version_manifest.json");
  }

  public VersionUtils.Version getServerlessProxyMinimumVersion() {
    return VersionUtils.parse(getStrProp(Fields.SERVERLESS_PROXY_MINIMUM_VERSION.value, "0.0.0.0"));
  }

  public VersionUtils.Version getMongotMinimumVersion() {
    return VersionUtils.parse(getStrProp(Fields.MONGOT_MINIMUM_VERSION.value, "0.0.0.0"));
  }

  public String getMongoDbToolsVersion() {
    return getStrProp(Fields.MONGODB_TOOLS_VERSION.value);
  }

  public String getMongoshVersion() {
    return getStrProp(Fields.MONGOSH_VERSION.value);
  }

  public VersionUtils.Version getChartsVersion() {
    return VersionUtils.parse(getStrProp(Fields.CHARTS_VERSION.value));
  }

  public AgentVersion getLastLegacyMonitoringAgentVersion() {
    return getAgentVersion(Fields.MONITORING_AGENT_VERSION, AgentType.MONITORING);
  }

  public AgentVersion getMonitoringAgentVersion() {
    // With One-Agent, the current monitoring agent version is the same as the automation version
    return getAgentVersion(Fields.AUTOMATION_AGENT_VERSION, AgentType.MONITORING);
  }

  public AgentVersion getMonitoringAgentMinimumVersion() {
    return getAgentVersion(Fields.MONITORING_AGENT_MINIMUM_VERSION, AgentType.MONITORING);
  }

  public boolean getLegacyBackupSupportsAtlas() {
    return getBoolProp("brs.legacy.backup.atlas", false);
  }

  public AgentVersion getLastLegacyBackupAgentVersion() {
    return getAgentVersion(Fields.BACKUP_AGENT_VERSION, AgentType.BACKUP);
  }

  public AgentVersion getBackupAgentVersion() {
    // With One-Agent, the current backup agent version is the same as the automation version
    return getAgentVersion(Fields.AUTOMATION_AGENT_VERSION, AgentType.BACKUP);
  }

  public AgentVersion getBackupAgentMinimumVersion() {
    return getAgentVersion(Fields.BACKUP_AGENT_MINIMUM_VERSION, AgentType.BACKUP);
  }

  public AgentVersion getBackupTunnelVersion() {
    return getAgentVersion(Fields.BACKUP_TUNNEL_VERSION, AgentType.BACKUP);
  }

  public AgentVersion getAutomationAgentVersion() {
    return getAgentVersion(Fields.AUTOMATION_AGENT_VERSION, AgentType.AUTOMATION);
  }

  public AgentVersion getAutomationAgentMinimumVersion() {
    return getAgentVersion(Fields.AUTOMATION_AGENT_MINIMUM_VERSION, AgentType.AUTOMATION);
  }

  public AgentVersion getAtlasAutomationAgentMinimumVersion() {
    return getAgentVersion(Fields.ATLAS_AUTOMATION_AGENT_MINIMUM_VERSION, AgentType.AUTOMATION);
  }

  public AgentVersion getAutomationAgentMinimumVersionForClientPIT() {
    return getAgentVersion(
        Fields.AUTOMATION_AGENT_MINIMUM_VERSION_FOR_CLIENT_PIT, AgentType.AUTOMATION);
  }

  public AgentVersion getClientPITVersion() {
    return getAgentVersion(Fields.BACKUP_CLIENT_PIT_VERSION, AgentType.BACKUP);
  }

  public AgentVersion getNDSProxyVersion() {
    return getAgentVersion(Fields.NDS_PROXY_VERSION, AgentType.ATLAS_PROXY);
  }

  public AgentVersion getNDSProxyMinimumVersion() {
    return getAgentVersion(Fields.NDS_PROXY_MINIMUM_VERSION, AgentType.ATLAS_PROXY);
  }

  public String getSegmentDefaultUserId() {
    String strProp = getStrProp(Fields.SEGMENT_DEFAULT_USER_ID.value, null);
    if (strProp != null && strProp.isEmpty()) {
      return null;
    }
    return strProp;
  }

  public String getSegmentClientSideWriteKey() {
    return getStrProp(Fields.SEGMENT_CLIENT_SIDE_WRITE_KEY.value, null);
  }

  public String getBeamerApiKey() {
    return getStrProp(Fields.BEAMER_API_KEY.value, null);
  }

  public String getMongoUri(final String pConnectionName) {
    // Note: this function does not support the multi-dbname case "mongo.<a>/<b>/<c>.mongoUri=123"
    if (StringUtils.isEmpty(pConnectionName)) {
      // Global DB mongoURI is not used in production. This concept doesn't exist anywhere outside
      // of local env.
      String globalMongoUriKey = String.join(DELIM, "mongo", "mongoUri");
      return getStrProp(globalMongoUriKey);
    }

    String cutoverMongoUriKey = String.join(DELIM, "mongo", pConnectionName, CUTOVER, "mongoUri");
    String mongoUriKey = String.join(DELIM, "mongo", pConnectionName, "mongoUri");

    // Internal Atlas Migration Cutover
    if (MongoClientRuntimeSettingsCache.isDbCutoverEnabled(pConnectionName)) {
      if (hasProp(cutoverMongoUriKey)) {
        return getStrProp(cutoverMongoUriKey);
      } else {
        throw new IllegalStateException(
            String.format(
                "Cutover is enabled for %s but required cutover uri key is missing from Prop file",
                pConnectionName));
      }
    }
    return getStrProp(mongoUriKey);
  }

  public Boolean getMongoUriEncryptedCredentials(final String pConnectionName) {
    return getBoolProp(
        "mongo." + pConnectionName + ".encryptedCredentials",
        getBoolProp("mongo.encryptedCredentials", Boolean.FALSE));
  }

  public boolean allowCorsRequestFromLocalHost() {
    return getBoolProp(ALLOW_LOCALHOST_CORS.value, false);
  }

  public TemplateMap generateTemplateParameters() {
    final TemplateMap params = new TemplateMap();
    params.put("centralUrl", getCentralUrl());
    params.put(REALM_CENTRAL_URL_FIELD, getBaasCentralUrl());
    params.put("accountCentralUrl", getAccountCentralUrl());
    params.put("appEnv", getAppEnv());
    params.put(IS_ON_PREM, false);
    params.put(TemplateMap.DOCS_URL_FIELD, getDocsUrl());
    params.put("docsSearchUrl", getDocsSearchUrl());
    params.put("emailLogo", getEmailLogo());
    params.put("emailLogoWidth", getEmailLogoWidth());
    params.put("emailLogoHeight", getEmailLogoHeight());
    params.put("govUSEnabled", getNDSGovUSEnabled());

    return params;
  }

  public boolean isInvitationOnlyMode() {
    return getBoolProp(Fields.INVITATION_ONLY.value, false);
  }

  public String getInvitationOnlyRedirectUrl() {
    return getStrProp(Fields.INVITATION_ONLY_REDIRECT_URL.value, "");
  }

  public boolean isTestUtilEnabled() {
    return getBoolProp("mms.testUtil.enabled");
  }

  public boolean isRedactEnabled() {
    return getBoolProp("mms.security.redactSecretsInStoredMessages", true);
  }

  public boolean isSelfServePaymentsEnabled() {
    return getBoolProp(Fields.SELF_SERVE_PAYMENTS_ENABLED.value, false);
  }

  public boolean isSystemInitiatedSnapshotEnabled() {
    return getBoolProp(Fields.SYSTEM_INITIATED_SNAPSHOT_ENABLED.value, false);
  }

  public boolean isSfdcSyncCronEnabled() {
    return getBoolProp("sfdc.sync.cron.enabled");
  }

  public boolean isSfscSyncEnabled() {
    return getBoolProp(Fields.SFSC_SYNC_ENABLED.value, false);
  }

  public boolean isAnalyticsEnabled() {
    return getBoolProp(Fields.ANALYTICS_ENABLED.value, false);
  }

  public boolean isBrowserErrorTrackingEnabled() {
    return getBoolProp(Fields.BROWSER_ERROR_TRACKING_ENABLED.value, false);
  }

  public String getBrowserErrorTrackingApiKey() {
    return getStrProp(AppSettings.Fields.BROWSER_ERROR_TRACKING_APIKEY.value, null);
  }

  public int getWTCSnapshotNumFilesLimit() {
    return getIntProp(Fields.SNAPSHOT_WTC_NUM_FILES_LIMIT.value, 100000);
  }

  public int getMaxNumBufferedFileBatchesOnAgent() {
    return getIntProp(Fields.BACKUP_MAX_NUM_BUFFERED_FILE_BATCHES_ON_AGENT.value, 1);
  }

  public boolean isPersonalizationWizardEnabled() {
    return getBoolProp(Fields.PERSONALIZATION_WIZARD_ENABLED.value, false);
  }

  public boolean isPersonalizationWizardRedirectEnabled() {
    return getBoolProp(Fields.PERSONALIZATION_WIZARD_REDIRECT_ENABLED.value, false);
  }

  /**
   * Creates an {@link ArrayList} of the snapshot schedule 5 entries with indexed values as follows:
   *
   * <p>
   *
   * <ul>
   *   <li>snapshotSchedule[0] = The interval at which to take a snapshot
   *   <li>snapshotSchedule[1] = The base retention policy
   *   <li>snapshotSchedule[2] = The daily retention policy (0 if not set)
   *   <li>snapshotSchedule[3] = The weekly retention policy (0 if not set)
   *   <li>snapshotSchedule[4] = The monthly retention policy (0 if not set)
   * </ul>
   *
   * <p>
   *
   * @return An {@link ArrayList} with the default global snapshot schedule
   */
  public List<Integer> getSnapshotScheduleProperties() {
    final Integer interval = getIntRequired(Fields.SNAPSHOT_SCHEDULE_INTERVAL.value);
    final Integer base = getIntRequired(Fields.SNAPSHOT_SCHEDULE_RETENTION_BASE.value);
    final Integer daily = getIntOrZeroOnEmptyString(Fields.SNAPSHOT_SCHEDULE_RETENTION_DAILY.value);
    final Integer weekly =
        getIntOrZeroOnEmptyString(Fields.SNAPSHOT_SCHEDULE_RETENTION_WEEKLY.value);
    final Integer monthly =
        getIntOrZeroOnEmptyString(Fields.SNAPSHOT_SCHEDULE_RETENTION_MONTHLY.value);

    final List<Integer> snapshotSchedule = new ArrayList<>(5);
    snapshotSchedule.add(interval);
    snapshotSchedule.add(base);
    snapshotSchedule.add(daily);
    snapshotSchedule.add(weekly);
    snapshotSchedule.add(monthly);

    return snapshotSchedule;
  }

  /**
   * Get the numeric value for the given key. If a numeric value is not found check if value is an
   * empty string. Return 0 if string is empty. For any other (invalid) value throw an {@link
   * IllegalArgumentException}.
   *
   * @return The numeric value that maps to the key name
   */
  private Integer getIntOrZeroOnEmptyString(final String pName) {
    try {
      return getIntProp(pName);
    } catch (final NumberFormatException e) {
      if (StringUtils.isEmpty(getProp(pName, SettingType.EFFECTIVE))) {
        return 0;
      } else {
        throw new IllegalArgumentException(
            "Invalid value for " + pName + ". Must either be empty or numeric.");
      }
    }
  }

  /**
   * Get the numeric value for the given key. If the value cannot be parsed into an integer throw an
   * {@link IllegalArgumentException}.
   *
   * @return The numeric value that maps to the key name.
   */
  private Integer getIntRequired(final String pName) {
    try {
      return getIntProp(pName);
    } catch (final NumberFormatException e) {
      throw new IllegalArgumentException(
          "Invalid value for " + pName + ". Must be a numeric value. Cannot be empty.");
    }
  }

  public enum BRSRestoreDigestMethod {
    NONE,
    SHA1
  }

  public BRSRestoreDigestMethod getRestoreDigestMethod() {
    String value = getStrProp(Fields.BACKUP_RESTORE_DIGEST_METHOD.value, "SHA1");
    if (value == null) {
      value = "SHA1";
    }

    return Enum.valueOf(BRSRestoreDigestMethod.class, value);
  }

  public int getServerlessBackupTemporaryLockTimeoutSeconds() {
    return getIntProp(Fields.SERVERLESS_BACKUP_TEMPORARY_LOCK_TIMEOUT.value, 20);
  }

  public long getServerlessBackupSubnetBuffer() {
    return getLongProp(Fields.SERVERLESS_BACKUP_SUBNET_BUFFER.value, 50);
  }

  public int getServerlessBackupRestoreMTMPoolLimit() {
    return getIntProp(Fields.SERVERLESS_BACKUP_RESTORE_MTM_POOL_LIMIT.value, 50);
  }

  public int getServerlessBackupRestoreMTMLimit() {
    return getIntProp(Fields.SERVERLESS_BACKUP_RESTORE_MTM_LIMIT.value, 3);
  }

  public boolean getUseNetworkAccessListEnabled() {
    return getBoolProp(Fields.SERVERLESS_BACKUP_NETWORK_ACCESS_LIST_ENABLED.value, true);
  }

  public int getServerlessSnapshotNeededCheckIntervalMinutes() {
    return getIntProp(Fields.SERVERLESS_BACKUP_SNAPSHOT_NEEDED_CHECK_INTERVAL_MINUTES.value, 5);
  }

  public int getServerlessOndemandSnapshotIntervalMinutes() {
    return getIntProp(Fields.SERVERLESS_BACKUP_ONDEMAND_SNAPSHOT_INTERVAL_MINUTES.value, 15);
  }

  public int getBackupRestoreMaximumConcurrentExports() {
    return getIntProp(Fields.BACKUP_RESTORE_MAXIMUM_CONCURRENT_EXPORTS.value, 5);
  }

  public int getOplogUsageCollectionJobNumThreads() {
    return getIntProp(Fields.NDS_CPS_OPLOG_USAGE_COLLECTION_THREAD_COUNT.value, 15);
  }

  public int getAwsSnapshotUsageSubmissionJobNumThreads() {
    return getIntProp(Fields.NDS_CPS_AWS_SNAPSHOT_USAGE_SUBMISSION_THREAD_COUNT.value, 15);
  }

  public int getGcpSnapshotUsageSubmissionJobNumThreads() {
    return getIntProp(Fields.NDS_CPS_GCP_SNAPSHOT_USAGE_SUBMISSION_THREAD_COUNT.value, 15);
  }

  public int getAzureSnapshotUsageSubmissionJobNumThreads() {
    return getIntProp(Fields.NDS_CPS_AZURE_SNAPSHOT_USAGE_SUBMISSION_THREAD_COUNT.value, 15);
  }

  public int getOplogUsageCollectionJobGroupBatchSize() {
    return getIntProp(Fields.NDS_CPS_OPLOG_USAGE_COLLECTION_GROUP_BATCH_SIZE.value, 1000);
  }

  public double getStandardDSV5RolloutPercentage() {
    return getDoubleProp(Fields.NDS_BACKUP_STANDARD_DSV5_ROLLOUT_PERCENTAGE.value, 1);
  }

  public boolean getPitRestoreValidationEnabled() {
    return getBoolProp(Fields.BACKUP_RESTORE_PIT_RESTORE_VALIDATION_ENABLED.value, true);
  }

  public boolean getPitWindowGapCheckEnabled() {
    return getBoolProp(Fields.BACKUP_RESTORE_PIT_GAP_CHECK_ENABLED.value, true);
  }

  public int getPitWindowInSeconds() {
    return getIntProp(Fields.BACKUP_RESTORE_PIT_WINDOW_HOURS.value, 24) * 3600;
  }

  public int getBlockFetcherTimeoutThresholdInSeconds() {
    return getIntProp(Fields.BACKUP_RESTORE_BLOCK_FETCHER_TIMEOUT_THRESHOLD_SECONDS.value, 5);
  }

  public int getBackupMeterUsageBatchSize() {
    return getIntProp(Fields.BACKUP_METER_USAGE_BATCH_SIZE_KEY.value, 1000);
  }

  public int getInvitationCronSvcSegmentBatchSize() {
    return getIntProp(Fields.INVITATION_CRON_BATCH_SIZE_KEY.value, 500000);
  }

  public int getInvitationCronSvcTimeout() {
    return getIntProp(Fields.INVITATION_CRON_TIMEOUT_KEY.value, 50);
  }

  public String getWebhookAdminEndpoint() {
    return getStrProp(Fields.MMS_ALERTS_WEBHOOK_ADMIN_ENDPOINT.value, null);
  }

  public String getWebhookAdminSecret() {
    return getStrProp(Fields.MMS_ALERTS_WEBHOOK_ADMIN_SECRET.value, null);
  }

  public boolean getEnableHttpReferrer() {
    return getBoolProp(Fields.MMS_SECURITY_ENABLE_HTTP_REFERRER.value, true);
  }

  public Set<Pattern> getRestrictedUsernameDomains() {
    Set<Pattern> restrictedUsernamePatterns = new HashSet<>();
    Set<String> restrictedRegexes =
        splitPropValueOnCommas(Fields.RESTRICTED_USERNAME_DOMAINS.value);
    for (String regex : restrictedRegexes) {
      restrictedUsernamePatterns.add(Pattern.compile(regex));
    }
    return restrictedUsernamePatterns;
  }

  /**
   * Returns a comma-delimited string of packages to reflect over and extract and then register
   * classes annotated with Morphia based annotations
   *
   * <p>Intended primarily for use with MongoSvcUriImpl
   */
  public String getMorphiaPackages() {
    return getStrProp("morphia.search.packages", null);
  }

  public Set<String> splitPropValueOnCommas(final String pPropName) {
    final String propValue = getStrProp(pPropName, "");

    if (StringUtils.isBlank(propValue)) {
      return Set.of();
    }

    return Arrays.stream(propValue.split(COMMA_DELIMITER)).collect(toSet());
  }

  public <T extends Enum<T>> Optional<T> getEnumProperty(
      final Class<T> pEnumClass, final String pPropName) {
    return getEnumProperty(pEnumClass, pPropName, "");
  }

  public <T extends Enum<T>> Optional<T> getEnumProperty(
      final Class<T> pEnumClass, final String pPropName, final String pDefaultValue) {
    try {
      final String propVal = getStrProp(pPropName, pDefaultValue);
      return Optional.of(Enum.valueOf(pEnumClass, propVal));
    } catch (final IllegalArgumentException pE) {
      // An IllegalArugmentException is thrown if the string value doesn't exist on the enum
      return Optional.empty();
    }
  }

  public double getGovUpliftRatio() {
    if (getNDSGovUSEnabled()) {
      return getIntProp(Fields.GOV_UPLIFT_PERCENTAGE_PROPERTY.value, 0) / 100.0;
    }
    return 0.0;
  }

  public int getDiagnosticArchiveCountLimit() {
    return getIntProp(Fields.DIAGNOSTIC_ARCHIVE_COUNT_LIMIT.value, 10000);
  }

  public int getDiagnosticArchiveSizeLimit() {
    return getIntProp(Fields.DIAGNOSTIC_ARCHIVE_SIZE_LIMIT.value, 50_000_000);
  }

  public int getDiagnosticArchiveAgeLimit() {
    return getIntProp(Fields.DIAGNOSTIC_ARCHIVE_AGE_LIMIT.value, 7);
  }

  public int getAutomationDiagnosticLastAgentStatusAgeLimit() {
    return getIntProp(Fields.AUTOMATION_DIAGNOSTIC_LAST_AGENT_STATUS_DOC_AGE_LIMIT.value, 7);
  }

  public boolean getAutomationDiagnosticIncludeEmptyProcessConfigs() {
    return getBoolProp(Fields.AUTOMATION_DIAGNOSTIC_INCLUDE_EMPTY_PROCESS_CONFIGS.value, false);
  }

  public String getDefaultRootCertType() {
    return getStrProp(Fields.NDS_ROOT_CERT.value, "ISRGROOTX1");
  }

  public String getDataLakeAdminApiUrlProp(final String pCloudProviderName) {
    return switch (pCloudProviderName) {
      case "AWS" -> Fields.DATA_LAKE_ADMIN_API_URL.value;
      case "AZURE" -> Fields.DATA_LAKE_ADMIN_API_AZURE_URL.value;
      case "GCP" -> Fields.DATA_LAKE_ADMIN_API_GCP_URL.value;
      default ->
          throw new IllegalArgumentException(
              "Unsupported provider for data lake admin API: " + pCloudProviderName);
    };
  }

  public String getDataLakeAdminApiUrl(final String pCloudProviderName) {
    final String adminApiUrlOverrideProp = getDataLakeAdminApiUrlProp(pCloudProviderName);
    return getOptionalStrProp(adminApiUrlOverrideProp)
        .orElse(System.getenv(getDataLakeAdminApiUrlEnv(pCloudProviderName)));
  }

  public String getStreamAdminApiUrl(final String pCloudProviderName, final String pSPMRegion) {
    if (!pCloudProviderName.equals("AWS")
        && !pCloudProviderName.equals("AZURE")
        && !pCloudProviderName.equals("GCP")) {
      throw new IllegalArgumentException("Unsupported cloud provider: " + pCloudProviderName);
    }

    return getOptionalStrProp(Fields.STREAM_PROCESS_MANAGER_URL.value)
        .orElse(
            System.getenv(getServiceMeshHostEnv("STREAMS_SPM", pCloudProviderName, pSPMRegion)));
  }

  public boolean isStreamProcessorEnabled() {
    return getBoolProp(Fields.STREAM_PROCESS_MANAGER_ENABLED.value, false);
  }

  public String getDataLakeFrontendPortProp(final String pCloudProviderName) {
    if ("AZURE".equalsIgnoreCase(pCloudProviderName)) {
      return Fields.DATA_LAKE_FRONTEND_AZURE_PORT.value;
    } else if ("GCP".equalsIgnoreCase(pCloudProviderName)) {
      return Fields.DATA_LAKE_FRONTEND_GCP_PORT.value;
    }
    return Fields.DATA_LAKE_FRONTEND_PORT.value;
  }

  public Optional<Integer> getDataLakeFrontendPort(final String pCloudProviderName) {
    return getOptionalStrProp(getDataLakeFrontendPortProp(pCloudProviderName))
        .map(String::trim)
        .filter(s -> !s.isEmpty())
        .map(Integer::parseInt);
  }

  public String getServiceMeshHost(
      final String pServiceName,
      final String pServiceCloudProvider,
      final String pServiceRegionName) {
    return System.getenv(
        getServiceMeshHostEnv(pServiceName, pServiceCloudProvider, pServiceRegionName));
  }

  public String getDataLakeAdminApiUrlEnv(final String pCloudProviderName) {
    final String serviceMeshHostRegionName =
        switch (pCloudProviderName) {
          case "AWS" -> AWS_SERVICE_MESH_HOST_REGION_NAME;
          case "AZURE" -> AZURE_SERVICE_MESH_HOST_REGION_NAME;
          case "GCP" ->
              // GCP uses an appSetting for the region since different regions are used in different
              // envs.
              // AWS and Azure just happen to use the same region in all envs.
              getStrProp(
                  Fields.GCP_SERVICE_MESH_HOST_REGION_NAME.value,
                  GCP_DEFAULT_SERVICE_MESH_HOST_REGION_NAME);
          default ->
              throw new IllegalArgumentException(
                  "Unsupported cloud provider: " + pCloudProviderName);
        };
    return getServiceMeshHostEnv("MHOUSE_BACKEND", pCloudProviderName, serviceMeshHostRegionName);
  }

  public String getServiceMeshHostEnv(
      final String pServiceName,
      final String pServiceCloudProvider,
      final String pServiceRegionName) {
    final AppEnv env = getAppEnv();
    final String template =
        (env == AppEnv.PROD || env == AppEnv.STAGING)
            ? "MESH_%1$s_%2$s_%4$s_%3$s_CLOUD_10GEN_CC"
            : "MESH_%1$s_%2$s_%4$s_%3$s_CLOUD_%2$s_10GEN_CC";
    return String.format(
        template, pServiceName, env.name(), pServiceCloudProvider, pServiceRegionName);
  }

  public boolean getServerlessXDSServerEnabled() {
    return getBoolProp(Fields.XDS_SERVER_ENABLED.value, false);
  }

  public boolean getUniformFrontendXDSServerEnabled() {
    return getBoolProp(Fields.UNIFORM_FRONTEND_XDS_SERVER_ENABLED.value, false);
  }

  public boolean getMmsGrpcServerEnabled() {
    return getBoolProp(Fields.MMS_GRPC_SERVER_ENABLED.value, false);
  }

  public boolean isMMSCronEnabled() {
    final String mmsCronEnabledKey = Fields.MMS_CRON_ENABLED.value;
    final String mmsCronEnabledPerProcessKey = mmsCronEnabledKey + "." + getHostnameId();
    return getBoolProp(mmsCronEnabledPerProcessKey, getBoolProp(mmsCronEnabledKey, false));
  }

  public boolean isNDSMeterUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_METER_USAGE_SUBMISSION_ENABLED.value, false);
  }

  public boolean isNDSKMSPrivateNetworkingMeterUsageSubmissionEnabled() {
    return getBoolProp(
        Fields.NDS_KMS_PRIVATE_NETWORKING_METER_USAGE_SUBMISSION_ENABLED.value, false);
  }

  public List<String> getNDSMeterUsageEnabledMeterIds() {
    final String meterIds = getStrProp(Fields.NDS_METER_USAGE_ENABLED_METER_IDS.value, "");
    return Splitter.on(COMMA_DELIMITER).omitEmptyStrings().trimResults().splitToList(meterIds);
  }

  public double getNDSMeterUsageGroupsPercentage() {
    return Math.max(
        Math.min(getDoubleProp(Fields.NDS_METER_USAGE_GROUPS_PERCENTAGE.value, 1), 1), 0);
  }

  public int getNDSMeterUsageSubmissionHours() {
    return getInt(Fields.NDS_METER_USAGE_SUBMISSION_HOURS.value, 6);
  }

  public boolean isNDSPremiumSKUMeterUsageSubmissionEnabled() {
    return getBoolProp(Fields.NDS_PREMIUM_SKU_METER_USAGE_SUBMISSION_ENABLED.value, false);
  }

  public int getNDSMeterUsageSubmissionSpread() {
    return getInt(
        Fields.NDS_METER_USAGE_SUBMISSION_SPREAD.value, DEFAULT_NDS_METER_USAGE_SUBMISSION_SPREAD);
  }

  public int getNDSMeterUsageRetrySubmissionSpread() {
    return getInt(
        Fields.NDS_METER_USAGE_RETRY_SUBMISSION_SPREAD.value,
        DEFAULT_NDS_METER_USAGE_RETRY_SUBMISSION_SPREAD);
  }

  public int getCalcAllSubscriptionUsageSpread() {
    return getInt(
        Fields.NDS_METER_USAGE_SUBSCRIPTION_SPREAD.value,
        DEFAULT_NDS_METER_USAGE_SUBSCRIPTION_SPREAD);
  }

  public boolean getNDSBillingPurgeIsEnabled() {
    return getBoolProp(Fields.NDS_BILLING_PURGE_ENABLED.value, true);
  }

  public boolean getNDSBillingPurgeIsDryRun() {
    return getBoolProp(Fields.NDS_BILLING_PURGE_IS_DRY_RUN.value, true);
  }

  public int getNDSBillingPurgeExpireAfterSeconds() {
    return getInt(
        Fields.NDS_BILLING_PURGE_EXPIRE_AFTER_SECONDS.value, (int) TimeUnit.DAYS.toSeconds(274));
  }

  public int getNDSBillingPurgeMaxNumThreads() {
    return getInt(Fields.NDS_BILLING_PURGE_MAX_NUM_THREADS.value, 2);
  }

  public int getNDSBillingPurgeMinExpirationDurationDays() {
    return getInt(Fields.NDS_BILLING_PURGE_MIN_EXPIRATION_DURATION_DAYS.value, 180);
  }

  public int getNDSBillingPurgeDeleteLimit() {
    return getIntProp(Fields.NDS_BILLING_PURGE_DELETE_LIMIT.value, 3_000_000);
  }

  public int getNDSBillingPurgeBulkDeleteBatchSize() {
    return getInt(Fields.NDS_BILLING_PURGE_BULK_DELETE_BATCH_SIZE.value, 1000);
  }

  public int getNDSBillingPurgeMaxHoursDeleteLimit() {
    return getInt(Fields.NDS_BILLING_PURGE_MAX_HOURS_DELETE_LIMIT.value, 3);
  }

  public List<String> getCronJobAcquisitionStrategies() {
    final String strategies = getStrProp(Fields.MMS_CRON_JOB_ACQUISITION_STRATEGIES.value, "");
    return Splitter.on(COMMA_DELIMITER).omitEmptyStrings().trimResults().splitToList(strategies);
  }

  public Duration getCronFrequencyDebounceThreshold() {
    final long thresholdInSeconds =
        getLongProp(
            Fields.MMS_CRON_FREQUENCY_DEBOUNCE_THRESHOLD_SECONDS.value,
            DEFAULT_FREQUENCY_DEBOUNCE_THRESHOLD_SECONDS);
    return Duration.ofSeconds(thresholdInSeconds);
  }

  public Optional<String> getAccountSupportReplyToEmailAddress() {
    if (!getAppEnv().isGovCloud()) {
      return Optional.empty();
    }
    return Optional.of(
        getStrProp(
            Fields.EMAIL_REPLY_ADDR_ACCOUNT_SUPPORT.value,
            "<EMAIL>"));
  }

  public Optional<String> getCloudSupportReplyToEmailAddress() {
    if (!getAppEnv().isGovCloud()) {
      return Optional.empty();
    }
    return Optional.of(
        getStrProp(
            Fields.EMAIL_REPLY_ADDR_CLOUD_SUPPORT.value, "<EMAIL>"));
  }

  public Optional<String> getCloudAlertsReplyToEmailAddress() {
    if (!getAppEnv().isGovCloud()) {
      return Optional.empty();
    }
    return Optional.of(
        getStrProp(
            Fields.EMAIL_REPLY_ADDR_CLOUD_ALERTS.value, "<EMAIL>"));
  }

  public Optional<String> getAccountSupportFromEmailAddress() {
    if (!getAppEnv().isGovCloud()) {
      return Optional.empty();
    }
    return Optional.of(
        getStrProp(
            Fields.EMAIL_FROM_ADDR_ACCOUNT_SUPPORT.value,
            "MongoDB Account for Government <<EMAIL>>"));
  }

  public Optional<String> getCloudSupportFromEmailAddress() {
    if (!getAppEnv().isGovCloud()) {
      return Optional.empty();
    }
    return Optional.of(
        getStrProp(
            Fields.EMAIL_FROM_ADDR_CLOUD_SUPPORT.value,
            "MongoDB Atlas for Government <<EMAIL>>"));
  }

  public Optional<String> getCloudAlertsFromEmailAddress() {
    if (!getAppEnv().isGovCloud()) {
      return Optional.empty();
    }
    return Optional.of(
        getStrProp(
            Fields.EMAIL_FROM_ADDR_CLOUD_ALERTS.value,
            "MongoDB Atlas for Government <<EMAIL>>"));
  }

  public int getDNSProcessingCronInterval() {
    return getIntProp(Fields.DNS_CRON_INTERVAL_SECONDS.value, 1);
  }

  public List<String> getOktaTokenAuthorizedClientIds() {
    // List of tokens that are allowed to access role based auth
    final String clientIds = getStrProp(Fields.OKTA_TOKEN_AUTHORIZED_CLIENT_IDS.value, "");
    return Splitter.on(COMMA_DELIMITER).omitEmptyStrings().trimResults().splitToList(clientIds);
  }

  public String getDefaultEnvoyInstanceSize() {
    return getStrProp(Fields.DEFAULT_ENVOY_INSTANCE_SIZE.value, "M10");
  }

  public String getEnvoyHostnameScheme() {
    return getNDSGatewayProxyEnabled() ? "MESH" : "INTERNAL";
  }

  public boolean isOktaEnabled() {
    return isOktaEnabled(false);
  }

  public boolean isOktaEnabled(final boolean pShouldDefault) {
    final String userSvcClass =
        getStrProp("mms.userSvcClass", pShouldDefault ? USER_SVC_OKTA_CLASS : USER_SVC_DB_CLASS);
    return userSvcClass.matches(".*\\.?UserSvcOkta$");
  }

  public boolean isBypassInvitationsEnabled() {
    return getBoolProp(AppSettings.Fields.BYPASS_INVITATIONS_EXISTING_USERS.value, false);
  }

  public boolean isMultiFactorAuthEnabled() {
    return getMultiFactorAuthLevel() != MultiFactorAuthLevel.OFF;
  }

  public int getGroupSearchMaxNumOfReturnAllowed() {
    return getIntProp(Fields.GROUP_SEARCH_MAX_NUM_OF_RETURN_ALLOWED.value, 3000);
  }

  public String getRedisPrimaryServerHost() {
    return getStrProp(Fields.NDS_REDIS_PRIMARY_SERVER_HOST.value, "localhost");
  }

  public int getRedisPrimaryServerPort() {
    return getIntProp(Fields.NDS_REDIS_PRIMARY_SERVER_PORT.value, 6379);
  }

  public String getRedisReplicaServerHost() {
    return getStrProp(Fields.NDS_REDIS_REPLICA_SERVER_HOST.value, "localhost");
  }

  public int getRedisReplicaServerPort() {
    return getIntProp(Fields.NDS_REDIS_REPLICA_SERVER_PORT.value, 6379);
  }

  public String getRedisUsername() {
    return getStrProp(Fields.NDS_REDIS_USERNAME.value, "");
  }

  public String getRedisPassword() {
    return getStrProp(Fields.NDS_REDIS_PASSWORD.value, "");
  }

  public long getRedisCommandTimeoutMS() {
    return getLongProp(Fields.NDS_REDIS_COMMAND_TIMEOUT_MS.value, 250);
  }

  public long getRedisMetaCommandTimeoutMS() {
    return getLongProp(Fields.NDS_REDIS_META_COMMAND_TIMEOUT_MS.value, 1000);
  }

  public long getRedisConnectTimeoutMS() {
    // Socket connect timeout should be lower than command timeout for Lettuce
    return getLongProp(Fields.NDS_REDIS_CONNECT_TIMEOUT_MS.value, 100);
  }

  public int getTagTimeoutMS() {
    return getIntProp(Fields.MMS_AUTHZ_TAGS_TIMEOUT_MS.value, 100);
  }

  public Duration getRedisEntryTTLHours() {
    final long redisEntryTTLHour = getLongProp(Fields.NDS_REDIS_ENTRY_TTL_HOURS.value, 1);
    return Duration.ofHours(redisEntryTTLHour);
  }

  public Duration getRedisEntryExtendTTLBeforeExpiryThresholdMins() {
    final long redisEntryExtendTTLBeforeExpiryThresholdMins =
        getLongProp(Fields.NDS_REDIS_ENTRY_EXTEND_TTL_BEFORE_EXPIRY_THRESHOLD_MINS.value, 15);
    return Duration.ofMinutes(redisEntryExtendTTLBeforeExpiryThresholdMins);
  }

  public Duration getRedisMaxExtraTTLSecs() {
    final long maxExtraTTLSeconds = getLongProp(Fields.NDS_REDIS_ENTRY_MAX_EXTRA_TTL_SECS.value, 0);
    return Duration.ofSeconds(maxExtraTTLSeconds);
  }

  public boolean isReadThroughOnCacheError() {
    return getBoolProp(Fields.NDS_REDIS_ENTRY_READ_THROUGH_ON_CACHE_ERROR.value, true);
  }

  public boolean isRedisCacheEnabled() {
    return getBoolProp(Fields.NDS_REDIS_CACHE_ENABLED.value, false);
  }

  public boolean isRedisSSLEnabled() {
    return getBoolProp(Fields.NDS_REDIS_SSL_ENABLED.value, true);
  }

  public boolean isDomainAuthLookupUsingRedis() {
    return getBoolProp(Fields.NDS_DOMAIN_AUTH_ROUTE_REDIS_ENABLED.value, false);
  }

  public Duration getNdsLookupSvcRedisEntryTTLHours() {
    final long redisEntryTTLHours =
        getLongProp(Fields.NDS_LOOKUP_SVC_REDIS_ENTRY_TTL_HOURS.value, 1);
    return Duration.ofHours(redisEntryTTLHours);
  }

  public Duration getDomainAuthExpirationEntryTTLDays() {
    final long redisEntryTTLDays =
        getLongProp(Fields.DOMAIN_AUTH_EXPIRATION_ENTRY_TTL_DAYS.value, 30);
    return Duration.ofDays(redisEntryTTLDays);
  }

  public Duration getNdsLookupSvcRedisEntryExtendTTLBeforeExpiryThresholdMins() {
    final long redisEntryExtendTTLBeforeExpiryThresholdMins =
        getLongProp(
            Fields.NDS_LOOKUP_SVC_REDIS_ENTRY_EXTEND_TTL_BEFORE_EXPIRY_THRESHOLD_MINS.value, 0);
    return Duration.ofMinutes(redisEntryExtendTTLBeforeExpiryThresholdMins);
  }

  public Duration getNdsLookupSvcRedisMaxExtraTTLSecs() {
    final long maxExtraTTLSeconds =
        getLongProp(Fields.NDS_LOOKUP_SVC_REDIS_ENTRY_MAX_EXTRA_TTL_SECS.value, 10 * 60);
    return Duration.ofSeconds(maxExtraTTLSeconds);
  }

  public int getDeletedIngestionPipelinesThreadPoolSize(final int pDefaultValue) {
    return getIntProp(
        Fields.CLEANUP_DELETED_INGESTION_PIPELINES_THREAD_POOL_SIZE.value, pDefaultValue);
  }

  public int getExpiredIngestionPipelinesThreadPoolSize(final int pDefaultValue) {
    return getIntProp(
        Fields.CLEANUP_EXPIRED_INGESTION_PIPELINES_THREAD_POOL_SIZE.value, pDefaultValue);
  }

  public int getIngestionPipelinesNumSnapshotLoadLimit(final int pDefaultValue) {
    return getIntProp(Fields.INGESTION_PIPELINES_NUM_SNAPSHOT_LOAD_LIMIT.value, pDefaultValue);
  }

  public int getExpiredDatasetsThreadPoolSize(final int pDefaultValue) {
    return getIntProp(Fields.CLEANUP_EXPIRED_DATA_SETS_THREAD_POOL_SIZE.value, pDefaultValue);
  }

  public int getCleanupDeletedArchivesThreadPoolSize(final int pDefaultValue) {
    return getIntProp(Fields.CLEANUP_DELETED_ARCHIVES_THREAD_POOL_SIZE.value, pDefaultValue);
  }

  public int getCleanupDeletedArchivesThreadTimeoutMinutes(final int pDefaultValue) {
    return getIntProp(Fields.CLEANUP_DELETED_ARCHIVES_THREAD_TIMEOUT_MINUTES.value, pDefaultValue);
  }

  public int getDeletedIngestionPipelinesQueryLimit(final int pDefaultValue) {
    return getIntProp(Fields.DELETED_INGESTION_PIPELINES_QUERY_LIMIT.value, pDefaultValue);
  }

  public int getExpiredIngestionPipelinesQueryLimit(final int pDefaultValue) {
    return getIntProp(Fields.EXPIRED_INGESTION_PIPELINES_QUERY_LIMIT.value, pDefaultValue);
  }

  public int getExpiredDatasetsQueryLimit(final int pDefaultValue) {
    return getIntProp(Fields.EXPIRED_DATA_SETS_QUERY_LIMIT.value, pDefaultValue);
  }

  public Duration getDeletedIngestionPipelineGracePeriod(final long pDefaultValueMinutes) {
    return Duration.ofMinutes(
        getLongProp(Fields.DELETED_INGESTION_PIPELINE_GRACE_PERIOD.value, pDefaultValueMinutes));
  }

  public Duration getDeletedDatasetGracePeriod(final long pDefaultValueMinutes) {
    return Duration.ofMinutes(
        getLongProp(Fields.DELETED_DATA_SET_GRACE_PERIOD.value, pDefaultValueMinutes));
  }

  public boolean isNdsLookupSvcReadThroughOnCacheError() {
    return getBoolProp(Fields.NDS_LOOKUP_SVC_REDIS_ENTRY_READ_THROUGH_ON_CACHE_ERROR.value, true);
  }

  public boolean isPushLiveMigrationsEnabled() {
    return getBoolProp(Fields.PUSH_LIVE_MIGRATIONS_ENABLED.value, false);
  }

  protected int getRandomNumber(final int pUpperBound) {
    return new Random().nextInt(pUpperBound);
  }

  private int getIntPropOrDefault(final String pFieldName, final int pDefaultValue) {
    try {
      return getIntProp(pFieldName, pDefaultValue);
    } catch (NumberFormatException e) {
      LOG.warn(
          "Unable to read from {}. Setting default value {}. Error Message: {}",
          pFieldName,
          pDefaultValue,
          e.getMessage(),
          e);
      return pDefaultValue;
    }
  }

  public boolean isPushLiveMigrationsMongoClientValidationsEnabled() {
    return getBoolProp(Fields.PUSH_LIVE_MIGRATIONS_MONGO_CLIENT_VALIDATIONS_ENABLED.value, false);
  }

  public Duration getLiveMigrationCutoverHoursUntilCutoverExpiration() {
    return Duration.ofHours(
        getIntProp(Fields.LIVE_MIGRATION_HOURS_UNTIL_CUT_OVER_EXPIRATION.value, 120));
  }

  public Duration getLiveMigrationCutoverHoursUntilCutoverExpirationWarning() {
    return Duration.ofHours(
        getIntProp(Fields.LIVE_MIGRATION_HOURS_UNTIL_CUT_OVER_EXPIRATION_WARNING.value, 24));
  }

  public int getLiveImportMongomirrorOplogBatchSize() {
    return getIntPropOrDefault(Fields.NDS_LIVE_IMPORT_MONGOMIRROR_OPLOG_BATCH_SIZE.value, 1000);
  }

  public int getLiveImportValidationJobTimeoutMins() {
    return getIntPropOrDefault(Fields.NDS_LIVE_IMPORT_VALIDATION_JOB_TIMEOUT_MINS.value, 10);
  }

  /**
   * Attempts to get the feature flag phase value from the properties file for the given env state.
   * NOTE: this is used as a fallback for feature flags that cannot be resolve EnvState due to an
   * incorrect string value in the config
   *
   * @param featureFlag
   * @return
   */
  private EnvState getFeatureFlagDefaultEnvStateFromPropertiesFile(FeatureFlag featureFlag) {
    ResolvedProperty prop =
        getPropWithoutConfigService(
            featureFlag.getEnvironmentProperty(), SettingType.PROPERTIES_FILE);

    return EnvState.fromValue(prop.getValue());
  }

  private void incrementConfigServiceFeatureFlagErrorCounter(
      FeatureFlag featureFlag, EnvState envState, String errorMessage) {
    PromMetricsSvc.incrementCounter(
        CONFIG_SERVICE_FEATURE_FLAG_PHASE_EVAL_COUNTER,
        errorMessage,
        featureFlag.name(),
        envState.name());
  }

  public boolean evaluateFeatureFlagDefaultPhase(FeatureFlag featureFlag, EnvState envState) {
    try {
      LOG.info(
          "Attempting to evaluate feature flag {} based on properties file", featureFlag.name());

      // attempt to evaluate feature flag phase value using the default value from properties file
      return getFeatureFlagDefaultEnvStateFromPropertiesFile(featureFlag) == envState;
    } catch (Exception exception) {
      LOG.error(
          "Unable to evaluate feature flag phase value from properties file for feature flag: {}",
          featureFlag.name(),
          exception);
      incrementConfigServiceFeatureFlagErrorCounter(
          featureFlag, EnvState.ENABLED, "default properties file eval failure");
      throw new RuntimeException(exception);
    }
  }

  /**
   * Evaluate feature flag using old legacy feature flag system
   *
   * @param featureFlag the feature flag to evaluate
   * @param envState the envState to compare against
   * @param errorMessage the error message
   * @param configServiceFailureFallback used to log the error accordingly
   * @return true if the feature flag's evaluated phase matches the passed envState
   */
  private boolean evaluateFeatureFlagWithOldSystem(
      FeatureFlag featureFlag,
      EnvState envState,
      String errorMessage,
      boolean configServiceFailureFallback) {
    try {
      return EnvState.fromValue(getStrProp(featureFlag.getEnvironmentProperty())) == envState;
    } catch (Exception exception) {
      LOG.error(
          "Unable to evaluate feature flag phase value for feature flag: {} with envState: {} {}",
          configServiceFailureFallback ? "fallback" : "",
          featureFlag.name(),
          envState.name(),
          exception);
      incrementConfigServiceFeatureFlagErrorCounter(featureFlag, envState, errorMessage);
      throw new RuntimeException(exception);
    }
  }

  /**
   * Evaluate feature flag phase using config service for migrated feature flags and MMS for
   * non-migrated ones against the given EnvState.
   *
   * @param featureFlag the feature flag to evaluate
   * @param envState the environment state to evaluate the feature flag against
   * @return true if the feature flag's phase matches the given env state
   */
  private boolean getFeatureFlagPhaseEval(
      @NotNull FeatureFlag featureFlag, @NotNull EnvState envState) {
    // if feature flag is not managed by the config service, eval and return using the old
    // method
    if (FeatureFlag.NON_CONFIG_SERVICE_FEATURE_FLAGS.contains(featureFlag)) {
      String errorMessage = "legacy feature flag eval failure";
      return evaluateFeatureFlagWithOldSystem(featureFlag, envState, errorMessage, false);
    }
    try {
      // derive the phase value based on the passed env state
      String phase = getConfigServiceFeatureFlagPhase(envState);
      // attempt to evaluate feature flag phase value using the config service
      return configServiceSdkWrapper
          .getFeatureFlagPhase(featureFlag.getEnvironmentProperty())
          .name()
          .equals(phase);
    } catch (Exception exception) {
      LOG.error(
          "Unable to evaluate feature flag phase value using config service for feature flag: {}"
              + " with envState: {}",
          featureFlag.name(),
          envState.name(),
          exception);
      incrementConfigServiceFeatureFlagErrorCounter(
          featureFlag, envState, "config service eval failure");
      // fallback to evaluating feature flag phase value using the old system
      throw new RuntimeException(exception);
    }
  }

  /**
   * Determines if {@code featureFlag} is set to "enabled" state.
   *
   * <p>NOTE: THIS SHOULD NOT BE USED FOR FEATURE FLAG EVALUATION since it does not take into
   * account the "controlled" state. For feature flag evaluation, use {@link
   * com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc}
   */
  public boolean isFeatureFlagInEnabledState(@NotNull FeatureFlag featureFlag) {
    return getFeatureFlagPhaseEval(featureFlag, EnvState.ENABLED);
  }

  public boolean isFeatureFlagInControlledState(@NotNull FeatureFlag featureFlag) {
    return getFeatureFlagPhaseEval(featureFlag, EnvState.CONTROLLED);
  }

  public boolean isFeatureFlagInDisabledState(@NotNull FeatureFlag featureFlag) {
    return getFeatureFlagPhaseEval(featureFlag, EnvState.DISABLED);
  }

  public EnvState getFeatureFlagEnvState(@NotNull FeatureFlag featureFlag) {
    if (isFeatureFlagInEnabledState(featureFlag)) {
      return EnvState.ENABLED;
    }
    if (isFeatureFlagInControlledState(featureFlag)) {
      return EnvState.CONTROLLED;
    }
    if (isFeatureFlagInDisabledState(featureFlag)) {
      return EnvState.DISABLED;
    }
    return null;
  }

  public Set<String> getProxyNaRegexThrottlingPrefixes() {
    return getSetProperty(NDS_PROXY_NA_REGEX_THROTTLING_WATCHED_NAMESPACES.value, ",");
  }

  public void setProxyNaRegexThrottlingPrefixes(final Set<String> prefixes) {
    prefixes.forEach(
        (prefix) -> {
          if (prefix.contains(",")) {
            throw new IllegalStateException(
                "Collection prefix should not contain commas. Got: " + prefix);
          }
        });

    setProp(
        NDS_PROXY_NA_REGEX_THROTTLING_WATCHED_NAMESPACES.value,
        StringUtils.join(prefixes, ","),
        SettingType.DATABASE);
  }

  public int getProxyNaRegexThrottlingMillis() {
    return getInt(NDS_PROXY_NA_REGEX_THROTTLING_MILLIS.value, 0);
  }

  public void setProxyNaRegexThrottlingMillis(final int amountMillis) {
    setProp(
        NDS_PROXY_NA_REGEX_THROTTLING_MILLIS.value,
        Integer.toString(amountMillis),
        SettingType.DATABASE);
  }

  public boolean isParseHardwarePayloadOnIngestionEnabled() {
    return getBoolProp(Fields.PARSE_HARDWARE_PING_PAYLOAD_ON_INGESTION_ENABLED.value, false);
  }

  public boolean isParseFTSPayloadOnIngestionEnabled() {
    return getBoolProp(Fields.PARSE_FTS_PING_PAYLOAD_ON_INGESTION_ENABLED.value, false);
  }

  public boolean isParseProfilerEntryEnabled() {
    return getBoolProp(Fields.PARSE_PROFILER_ENTRY_ON_INGESTION_ENABLED.value, false);
  }

  public Duration getACMEHTTPTimeout() {
    return Duration.ofSeconds(getLongProp(Fields.ACME_HTTP_TIMEOUT_SECONDS.value, 10));
  }

  public boolean isNDSPlanExecutorJobPriorityEnabled() {
    return getBoolProp(Fields.NDS_PLAN_EXECUTOR_JOB_PRIORITY_ENABLED.value, false);
  }

  public boolean isMultiFactorAuthEncouragementEnabled() {
    return getBoolProp(Fields.MULTI_FACTOR_AUTH_ENCOURAGEMENT_ENABLED.value, false);
  }

  public boolean isMultiFactorAuthEncouragementEnabledForNoFactors() {
    return getBoolProp(Fields.MULTI_FACTOR_AUTH_ENCOURAGEMENT_ENABLED_FOR_NO_FACTORS.value, false);
  }

  public double getAWSRegionInHealingPercent() {
    return getDoubleProp(Fields.NDS_AWS_REGION_IN_HEALING_PERCENT.value, 0.50);
  }

  public double getAzureRegionInHealingPercent() {
    return getDoubleProp(Fields.NDS_AZURE_REGION_IN_HEALING_PERCENT.value, 0.50);
  }

  public double getGCPRegionInHealingPercent() {
    return getDoubleProp(Fields.NDS_GCP_REGION_IN_HEALING_PERCENT.value, 0.50);
  }

  public String getBackupSnapshotAwsKmsKeyArnTemplate() {
    return getStrProp(Fields.NDS_BACKUP_SNAPSHOTS_AWS_KMS_KEY_ARN_TEMPLATE.value);
  }

  public String getBackupSnapshotAwsKmsGovKeyArnTemplate() {
    return getStrProp(Fields.NDS_BACKUP_SNAPSHOTS_AWS_KMS_GOV_KEY_ARN_TEMPLATE.value);
  }

  public String getBackupFasterRestoreKmsKeyArnTemplate() {
    return getStrProp(Fields.NDS_BACKUP_FASTER_RESTORE_KMS_KEY_ARN_TEMPLATE.value);
  }

  public String getBackupFasterRestoreKmsGovKeyArnTemplate() {
    return getStrProp(Fields.NDS_BACKUP_FASTER_RESTORE_KMS_GOV_KEY_ARN_TEMPLATE.value);
  }

  public String getDeviceSyncDebugAccessSharedSecretJson() {
    return getStrProp(AppSettings.Fields.NDS_DEVICE_SYNC_DEBUG_ACCESS_SHARED_SECRET.value, "");
  }

  public long getGroupPlanningIntervalLastFailed() {
    return getLongProp(
        Fields.NDS_PLANNER_GROUP_INTERVAL_LAST_FAILED_PROP.value,
        GROUP_PLANNING_INTERVAL_LAST_FAILED);
  }

  public long getGroupPlanningIntervalShort() {
    return getLongProp(
        Fields.NDS_PLANNER_GROUP_INTERVAL_SHORT_PROP.value, GROUP_PLANNING_INTERVAL_SHORT);
  }

  public long getGroupPlanningIntervalMedium() {
    return getLongProp(
        Fields.NDS_PLANNER_GROUP_INTERVAL_MEDIUM_PROP.value, GROUP_PLANNING_INTERVAL_MEDIUM);
  }

  public long getGroupPlanningIntervalLong() {
    return getLongProp(
        Fields.NDS_PLANNER_GROUP_INTERVAL_LONG_PROP.value, GROUP_PLANNING_INTERVAL_LONG);
  }

  public long getNdsPlannerGroupIntervalWhileUnderMaintenance() {
    return getLongProp(
        Fields.NDS_PLANNER_GROUP_INTERVAL_WHILE_UNDER_MAINTENANCE_PROP.value,
        GROUP_PLANNING_INTERVAL_WHILE_UNDER_MAINTENANCE);
  }

  public Set<String> getNdsPlannerGroupLoggerEnhancement() {
    final String strValue = getStrProp(Fields.NDS_PLANNER_GROUP_LOGGER_ENHANCEMENT.value, "");
    return Stream.of(strValue.split(","))
        .map(String::trim)
        .filter(s -> s.matches("[0-9a-z]+$"))
        .collect(Collectors.toSet());
  }

  public boolean isAtlasStreamsPlannerEnabled() {
    return getBoolProp(Fields.ATLAS_STREAMS_PLANNER_ENABLED.value, false);
  }

  public boolean isAtlasStreamsVPCPeeringScannerPlannerEnabled() {
    return getBoolProp(Fields.ATLAS_STREAMS_VPC_PEERING_SCANNER_PLANNER_ENABLED.value, false);
  }

  public long getStreamsVPCPeeringScannerPlannerDelaySeconds() {
    return getLongProp(Fields.ATLAS_STREAMS_VPC_PEERING_SCANNER_PLANNER_DELAY_SECONDS.value, 300);
  }

  public boolean isAtlasStreamsPrivateLinkPlannerEnabled() {
    return getBoolProp(Fields.ATLAS_STREAMS_PRIVATE_LINK_PLANNER_ENABLED.value, false);
  }

  public String getVPCPeeringProxyHostedZoneId() {
    return getStrProp(Fields.ATLAS_STREAMS_VPC_PEERING_PROXY_HOSTED_ZONE_ID.value, null);
  }

  public boolean isQueuedAdminActionsEnabled() {
    return getBoolProp(AppSettings.Fields.NDS_QUEUED_ADMIN_ACTIONS_ENABLED.value, false);
  }

  public boolean isQueuedAdminActionsUIEnabled() {
    return getBoolProp(AppSettings.Fields.MMS_QUEUED_ADMIN_ACTIONS_UI_ENABLED.value, false);
  }

  public boolean isRespectProtectedHoursMaintenanceOptionAdminUIEnabled() {
    return getBoolProp(
        AppSettings.Fields.NDS_RESPECT_PROTECTED_HOURS_MAINTENANCE_ADMIN_UI_ENABLED.value, false);
  }

  // The purpose of "nds.continuousDelivery.overrideMongoDBVersion" is to allow an easy control to
  // set the quarterly version to test.  Right now our system will automatically use the latest
  // release version which normally works fine but if we need to test a patch version for a
  // previous
  // quarterly version that requires us to rip out all the later versions in the
  // mongodb_version_manifest.json file
  public Optional<VersionUtils.Version> getContinuousDeliveryOverrideMongoDBVersion() {
    return getOptionalStrProp(Fields.NDS_CONTINUOUS_DELIVERY_OVERRIDE_MONGODB_VERSION.value)
        .map(VersionUtils::parse);
  }

  public Optional<Integer> getPromIntegrationFallbackTokenFillRate() {
    return getOptionalIntProp(PROMETHEUS_INTEGRATION_TOKEN_FILL_RATE_SECONDS.value);
  }

  /**
   * This property is used to setup Prom server, which is before the config service SDK is
   * initialized.
   */
  public int getPromListeningPort() {
    return getIntProp(PROMETHEUS_LISTENING_PORT.value, getBasePort() + 50);
  }

  public Optional<Integer> getPromIntegrationBurstTokenCount() {
    return getOptionalIntProp(PROMETHEUS_INTEGRATION_BURST_TOKEN_COUNT.value);
  }

  public int getLiveImportMongosyncLogTTLDays() {
    return getIntProp(Fields.NDS_LIVE_IMPORT_MONGOSYNC_LOG_TTL_DAYS.value, 30);
  }

  public String getLiveImportMongosyncWorkingDir() {
    return getStrProp(Fields.NDS_LIVE_IMPORT_MONGOSYNC_WORKING_DIR.value, "/tmp/mongosync");
  }

  public VersionUtils.Version getLiveImportMongosyncVersion() {
    return VersionUtils.parse(getStrProp(Fields.NDS_LIVE_IMPORT_MONGOSYNC_VERSION.value));
  }

  public VersionUtils.Version getLiveImportMongosyncLatestVersion() {
    return VersionUtils.parse(getStrProp(Fields.NDS_LIVE_IMPORT_MONGOSYNC_LATEST_VERSION.value));
  }

  public VersionUtils.Version getPushLiveImportMongosyncVersion() {
    return VersionUtils.parse(getStrProp(Fields.NDS_PUSH_LIVE_IMPORT_MONGOSYNC_VERSION.value));
  }

  public VersionUtils.Version getLiveImportMongomirrorVersion() {
    return VersionUtils.parse(getStrProp(Fields.NDS_LIVE_IMPORT_MONGOMIRROR_VERSION.value));
  }

  public String getMongomirrorDockerImageTag() {
    return getStrProp(Fields.NDS_LIVE_IMPORT_MONGOMIRROR_DOCKER_IMAGE_TAG.value);
  }

  public boolean isLiveImportKubeRestartsEnabled() {
    return getBoolProp(Fields.NDS_LIVE_IMPORT_KUBE_RESTARTS_ENABLED.value, true);
  }

  public boolean isLiveImportKubeResourceCleanerEnabled() {
    return getBoolProp(Fields.NDS_LIVE_IMPORT_KUBE_RESOURCE_CLEANER_ENABLED.value, false);
  }

  public Optional<String> getMongosyncBinaryPathPrefix() {
    return getOptionalStrProp(Fields.NDS_LIVE_IMPORT_MONGOSYNC_BINARY_PATH_PREFIX.value);
  }

  public String getMongosyncVerifierHelixCPURequirement() {
    return getStrProp(Fields.NDS_LIVE_IMPORT_MONGOSYNC_VERIFIER_HELIX_CPU_REQ.value, "40");
  }

  public String getMongosyncVerifierHelixMemoryRequirement() {
    return getStrProp(Fields.NDS_LIVE_IMPORT_MONGOSYNC_VERIFIER_HELIX_MEMORY_REQ.value, "180Gi");
  }

  public String getMongosyncLogVerbosity() {
    return getStrProp(Fields.NDS_MONGOSYNC_LOG_VERBOSITY.value, "DEBUG");
  }

  public Optional<Integer> getMongosyncMaxNumParallelPartitions() {
    return getOptionalIntProp(Fields.NDS_MONGOSYNC_MAX_NUM_PARALLEL_PARTITIONS.value);
  }

  public Optional<Integer> getMongosyncNumInsertersPerPartition() {
    return getOptionalIntProp(Fields.NDS_MONGOSYNC_NUM_INSERTERS_PER_PARTITION.value);
  }

  public String getMongosyncBaseMemoryRequirementGB() {
    return getStrProp(Fields.NDS_MONGOSYNC_BASE_MEMORY_REQUIREMENT_GB.value, "10");
  }

  public String getMongosyncVerifierMemoryRateGBPerMillionDocs() {
    return getStrProp(Fields.NDS_MONGOSYNC_VERIFIER_MEMORY_RATE_GB_PER_MILLION_DOCS.value, "0.6");
  }

  public String getMongosyncSourceSizeLimitToOverrideNumberOfPartitionsAndInserters() {
    return getStrProp(
        Fields.NDS_MONGOSYNC_SOURCE_GB_SIZE_LIMIT_TO_OVERRIDE_NUM_PARTITIONS_INSERTERS.value, "32");
  }

  public String getMongosyncVerifierStartAtPhase() {
    return getStrProp(Fields.NDS_MONGOSYNC_VERIFIER_START_AT_PHASE.value, null);
  }

  public List<String> getBypassSsoDomains() {
    return getListProperty(Fields.BYPASS_SSO_DOMAINS.value, COMMA_DELIMITER);
  }

  public boolean isCreatingSamlIdpDisabled() {
    return getBoolProp(Fields.CREATING_SAML_IDP_DISABLED.value, false);
  }

  public boolean isOidcIdpEnabledNonAtlas() {
    return getBoolProp(Fields.ENABLE_OIDC_IDP_NON_ATLAS.value, false);
  }

  public float getGenAICompassAccessPercent() {
    return getFloat(
        Fields.GEN_AI_COMPASS_ACCESS_PERCENT.value, DEFAULT_GEN_AI_COMPASS_ACCESS_PERCENT);
  }

  public boolean isServerlessConsumptionDebugLoggingEnabled() {
    return getBoolProp(Fields.NDS_SERVERLESS_MTM_CONSUMPTION_DEBUG_LOGGING_ENABLED.value, false);
  }

  public void ensureDataFederationWritesAllowed() {
    if (getBoolProp("nds.dataFederation.rejectWrites", false)) {
      throw new UnsupportedOperationException("Data Federation maintenance in progress");
    }
  }

  public boolean isMTMCompactionEnabled() {
    return getBoolProp(Fields.NDS_MTM_COMPACTION_ENABLED.value, false);
  }

  public boolean isEhmForExternalMaintenanceEnabled() {
    return getBoolProp(
        Fields.NDS_ELEVATED_HEALTH_MONITORING_EXTERNAL_MAINTENANCE_ENABLED.value, true);
  }

  public int getMongodbEolVersionUpgradeMonitoringPeriodHours() {
    return getIntProp(Fields.NDS_MONGODB_EOL_VERSION_UPGRADE_MONITORING_PERIOD_HOURS.value, 48);
  }

  public int getAtlasResourcePolicyTraceLoggingPerSecond() {
    return getIntProp(Fields.ATLAS_RESOURCE_POLICY_TRACE_LOGGING_PER_SECOND.value, 0);
  }

  public boolean pauseOnTenantCompactionFailure() {
    return getBoolProp(Fields.NDS_MTM_COMPACTION_PAUSE_ON_TENANT_FAILURE.value, true);
  }

  public int getMaxTenantCompactionConcurrent() {
    return getIntProp(Fields.NDS_MTM_COMPACTION_MAX_TENANT_CONCURRENT.value, 3);
  }

  public List<String> getAllowedMongoDBVersionsForCompaction() {
    List<String> allowedMongoDBVersions =
        getListProperty(Fields.NDS_MTM_COMPACTION_ALLOWED_MONGODB_VERSIONS.value, COMMA_DELIMITER);
    if (allowedMongoDBVersions.isEmpty()) {
      allowedMongoDBVersions = List.of("8.0");
    }
    return allowedMongoDBVersions;
  }

  public int getMaxCompactionPerRegionConcurrent() {
    return getIntProp(Fields.NDS_MTM_COMPACTION_MAX_MTM_PER_REGION_CONCURRENT.value, 1);
  }

  public boolean isAtlasProxyProcessRestartOnFallbackCertRotationEnabled() {
    return getBoolProp(
        Fields.NDS_ATLASPROXY_RESTART_PROXY_PROCESS_ON_FALLBACK_CERT_ROTATION.value, false);
  }

  public boolean isDarkModePreviewEnabled() {
    return getBoolProp(Fields.DARK_MODE_PREVIEW_ENABLED.value, false);
  }

  public boolean isAccountAppDarkModeEnabled() {
    return getBoolProp(Fields.DARK_MODE_ACCOUNT_APP_ENABLED.value, false);
  }

  public boolean isCloudNavOverrideEnabled() {
    return getBoolProp(Fields.CLOUD_NAV_OVERRIDE_ENABLED.value, false);
  }

  public boolean isCloudNavEnabledForCloudManager() {
    return getBoolProp(Fields.CLOUD_NAV_CLOUD_MANAGER_ENABLED.value, false);
  }

  public ThemePreference getDefaultTheme() {
    return ThemePreference.valueOf(getStrProp(Fields.THEME_DEFAULT.value, "LIGHT"));
  }

  public int getServerlessMaxResidentTenantsPerMTM(final int pDefaultValue) {
    return getIntProp(Fields.NDS_SERVERLESS_MTM_LIMITS_MAX_RESIDENT_TENANTS.value, pDefaultValue);
  }

  public int getServerlessMaxResidentMTMsPerGroup(final int pDefaultValue) {
    return getIntProp(
        Fields.NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_MTMS_PER_GROUP.value, pDefaultValue);
  }

  public int getServerlessMaxResidentMTMsPerPool(final int pDefaultValue) {
    return getIntProp(
        Fields.NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_MTMS_PER_POOL.value, pDefaultValue);
  }

  public int getServerlessMaxResidentMTMsForAutoscaleMTMCapacity(final int pDefaultValue) {
    return getIntProp(
        Fields.NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_MTMS_FOR_AUTOSCALE_MTM_CAPACITY.value,
        pDefaultValue);
  }

  public int getServerlessMaxResidentTenantsPerPool(final int pDefaultValue) {
    return getIntProp(
        Fields.NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_TENANTS_PER_POOL.value, pDefaultValue);
  }

  public double getServerlessMaxResidentTenantsPerPoolThreshold(final double pDefaultValue) {
    return getDoubleProp(
        Fields.NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_TENANTS_THRESHOLD.value, pDefaultValue);
  }

  public String getServerlessMTMBackingInstanceSize() {
    return getStrProp(Fields.NDS_SERVERLESS_MTM_BACKING_INSTANCE_SIZE.value, "M80");
  }

  public boolean isAtlasDeploysUserIdentityServiceEnabledOrControlled() {
    return isFeatureFlagInEnabledState(FeatureFlag.ATLAS_DEPLOYS_UIS)
        || isFeatureFlagInControlledState(
            FeatureFlag.ATLAS_DEPLOYS_UIS); // If there are some projects with AtlasUIS-es in this
    // environment, we want to turn on the cron job.

  }

  public String getAutomationSentryDSN() {
    return getStrProp(Fields.MMS_AUTOMATION_SENTRY_DSN.value, "");
  }

  public double getAutomationSentrySampleRate() {
    return getDoubleProp(
        Fields.MMS_AUTOMATION_SENTRY_SAMPLE_RATE.value, DEFAULT_AUTOMATION_SENTRY_SAMPLE_RATE);
  }

  public String getRealTimeAgentCentralUrl() {
    return getStrProp(Fields.MMS_REAL_TIME_AGENT_CENTRAL_URL.value, getCentralUrl());
  }

  public int getAuthzServiceClientRequestThreads(final int pDefaultValue) {
    return getIntProp(Fields.MMS_AUTHZ_SERVICE_CLIENT_REQUEST_THREADS.value, pDefaultValue);
  }

  public int getAuthzServiceFutureRequestThreads(final int pDefaultValue) {
    return getIntProp(Fields.MMS_AUTHZ_SERVICE_FUTURE_REQUEST_THREADS.value, pDefaultValue);
  }

  public String getNDSOktaTestOIDCAuthorizationServerApiKey() {
    return getStrProp(Fields.NDS_OKTA_TEST_OIDC_AUTHORIZATION_SERVER_APIKEY.value, "");
  }

  public List<Integer> getNDSGCPDailyBillingAuditDays() {
    final String auditDaysRawString = getStrProp(Fields.NDS_GCP_DAILY_BILLING_AUDIT_DAYS.value, "");

    try {
      return Arrays.stream(auditDaysRawString.split(",")).map(Integer::parseInt).toList();
    } catch (final NumberFormatException ignored) {
    }

    return List.of();
  }

  public boolean isBackupRegionalOplogEnsureIndexEnabled() {
    return getBoolProp(Fields.BRS_REGIONAL_OPLOG_ENSURE_INDEX_ENABLED.value, false);
  }

  public boolean isBackupSnapshotQueryEnabled() {
    return getBoolProp("mms.backup.snapshotQueryEnabled", true);
  }

  public int getServerlessBackupImportCollectionWorkers() {
    return getIntProp("mms.backup.importCollectionWorkers", 1);
  }

  public List<String> getServerlessRestoreNameSpaceDenyList() {
    return getListProperty("nds.serverless.restore.denylist", COMMA_DELIMITER);
  }

  public List<String> getServerlessRestoreNameSpaceAllowList() {
    return getListProperty("nds.serverless.restore.allowlist", COMMA_DELIMITER);
  }

  public long getServerlessRestoreAgentUpgradeGracePeriodInMin() {
    if (shouldUseVersionManagerForServerlessRestore()) {
      return 0;
    }
    return getLongProp("mms.backup.restoreVMAgentUpgradeGracePeriod", 5);
  }

  public boolean shouldUseVersionManagerForServerlessRestore() {
    return getBoolProp("mms.backup.versionManagerIntegrationEnabled", true);
  }

  public int getServerlessBackupServerLogVerbosityLevel() {
    return getIntProp("mms.backup.sysLogLevel", 0);
  }

  public String getNDSAWSInstanceOS() {
    return getStrProp(Fields.NDS_INSTANCES_OS_AWS.value, DEFAULT_INSTANCE_OS);
  }

  public String getNDSAzureInstanceOS() {
    return getStrProp(Fields.NDS_INSTANCES_OS_AZURE.value, DEFAULT_INSTANCE_OS);
  }

  public String getNDSGCPInstanceOS() {
    return getStrProp(Fields.NDS_INSTANCES_OS_GCP.value, DEFAULT_INSTANCE_OS);
  }

  public String getNDSAWSServerlessProxyOS() {
    return getStrProp(Fields.NDS_SERVERLESS_PROXY_OS_AWS.value, DEFAULT_SERVERLESS_PROXY_OS);
  }

  public String getNDSAzureServerlessProxyOS() {
    return getStrProp(Fields.NDS_SERVERLESS_PROXY_OS_AZURE.value, DEFAULT_SERVERLESS_PROXY_OS);
  }

  public String getNDSGCPServerlessProxyOS() {
    return getStrProp(Fields.NDS_SERVERLESS_PROXY_OS_GCP.value, DEFAULT_SERVERLESS_PROXY_OS);
  }

  public String getNDSAWSStreamsProxyOS() {
    return getStrProp(Fields.NDS_STREAMS_PROXY_OS_AWS.value, DEFAULT_STREAMS_PROXY_OS);
  }

  public String getNDSAzureStreamsProxyOS() {
    return getStrProp(Fields.NDS_STREAMS_PROXY_OS_AZURE.value, DEFAULT_STREAMS_PROXY_OS);
  }

  public float getDataValidationDailySamplePercent() {
    return getFloat(
        Fields.DATA_VALIDATION_DAILY_SAMPLE_PERCENT.value, DEFAULT_DAILY_SAMPLE_PERCENT);
  }

  public int getDataValidationMinDaysBetweenClusterValidations() {
    return getIntProp(
        Fields.DATA_VALIDATION_MIN_DAYS_BETWEEN_CLUSTER_VALIDATIONS.value,
        DEFAULT_MIN_DAYS_BETWEEN_CLUSTER_VALIDATIONS);
  }

  public int getDataValidationMinClusterAgeDays() {
    return getIntProp(
        Fields.DATA_VALIDATION_MIN_CLUSTER_AGE_DAYS.value, DEFAULT_MIN_CLUSTER_AGE_DAYS);
  }

  public float getDbCheckDailySamplePercent() {
    return getFloat(Fields.DB_CHECK_DAILY_SAMPLE_PERCENT.value, DEFAULT_DAILY_SAMPLE_PERCENT);
  }

  public int getDbCheckMinDaysBetweenClusterValidations() {
    return getIntProp(
        Fields.DB_CHECK_MIN_DAYS_BETWEEN_CLUSTER_VALIDATIONS.value,
        DEFAULT_MIN_DAYS_BETWEEN_CLUSTER_VALIDATIONS);
  }

  public int getDbCheckMinClusterAgeDays() {
    return getIntProp(Fields.DB_CHECK_MIN_CLUSTER_AGE_DAYS.value, DEFAULT_MIN_CLUSTER_AGE_DAYS);
  }

  public float getCheckMetadataConsistencyDailySamplePercent() {
    return getFloat(
        Fields.CHECK_METADATA_CONSISTENCY_DAILY_SAMPLE_PERCENT.value, DEFAULT_DAILY_SAMPLE_PERCENT);
  }

  public int getCheckMetadataConsistencyMinDaysBetweenClusterValidations() {
    return getIntProp(
        Fields.CHECK_METADATA_CONSISTENCY_MIN_DAYS_BETWEEN_CLUSTER_VALIDATIONS.value,
        DEFAULT_MIN_DAYS_BETWEEN_CLUSTER_VALIDATIONS);
  }

  public int getCheckMetadataConsistencyMinClusterAgeDays() {
    return getIntProp(
        Fields.CHECK_METADATA_CONSISTENCY_MIN_CLUSTER_AGE_DAYS.value, DEFAULT_MIN_CLUSTER_AGE_DAYS);
  }

  public Optional<String> getCorruptionDetectionTargetClusterVersionRegex() {
    return getOptionalStrProp(Fields.CORRUPTION_DETECTION_TARGET_CLUSTER_VERSION_REGEX.value)
        .filter(Predicate.not(String::isEmpty));
  }

  public boolean isFleetAttributeCollectionEnabled() {
    return getBoolProp(Fields.NDS_FLEET_ATTRIBUTE_COLLECTION.value, false);
  }

  public boolean isReasonsRequireJira() {
    return getBoolProp(Fields.NDS_ADMIN_REASONS_REQUIRE_JIRA.value, false);
  }

  public boolean isDataPlaneAccessRequestsOnlineValidation() {
    return getBoolProp(Fields.NDS_ADMIN_DATA_PLANE_ACCESS_REQUESTS_ONLINE_VALIDATION.value, false);
  }

  public boolean isIpBasedRateLimitingForUnauthedEnpointsEnforced() {
    return getBoolProp(Fields.ENFORCE_IP_RATE_LIMIT_FOR_UNAUTHED_ENDPOINTS.value, true);
  }

  public String getContentStackApiKey() {
    return getStrProp(Fields.CONTENTSTACK_API_KEY.value, null);
  }

  public String getContentStackDeliveryToken() {
    return getStrProp(Fields.CONTENTSTACK_DELIVERY_TOKEN.value, null);
  }

  public String getContentStackBranch() {
    return getStrProp(Fields.CONTENTSTACK_BRANCH.value, null);
  }

  public boolean getContentstackEnabled() {
    return getBoolProp(Fields.CONTENTSTACK_ENABLED.value, false);
  }

  public String getOktaWebhooksSecretKey() {
    return getStrProp(Fields.OKTA_WEBHOOK_SECRET_KEY.value, null);
  }

  public long getMaxMessageAgeCutoffMilliseconds() {
    return getIntProp(Fields.MAX_MESSAGE_AGE_CUTOFF_MILLISECONDS.value, 60 * 60 * 1000);
  }

  public boolean isEcosystemEnabled() {
    return getBoolProp(Fields.ECOSYSTEM_ENABLED.value, false);
  }

  public int getClusterChangeSvcProcessChangesBeforeInMins(final int pDefaultValue) {
    return getIntProp(CLUSTER_CHANGE_SVC_PROCESS_CHANGES_BEFORE.value, pDefaultValue);
  }

  public int getClusterChangeSvcCronJobFrequencyInMins(final int pDefaultValue) {
    return getIntProp(CLUSTER_CHANGE_SVC_CRON_JOB_FREQUENCY.value, pDefaultValue);
  }

  public boolean isServiceAccountOauthEnabled() {
    return getBoolProp(Fields.SERVICE_ACCOUNT_OAUTH_ENABLED.value, false);
  }

  public boolean isServiceAccountOauthAlertsEnabled() {
    return getBoolProp(Fields.SERVICE_ACCOUNT_OAUTH_ALERTS_ENABLED.value, false);
  }

  public boolean isRollingReplacementJobEmailServiceEnabled() {
    return getBoolProp(Fields.BACKUP_ROLLING_REPLACEMENT_EMAIL.value, false);
  }

  public int getSnapshotValidationPreSignedUrlExpiryHours() {
    return getIntProp(Fields.BACKUP_SNAPSHOT_VALIDATION_PRESIGNED_URL_EXPIRY_HOURS.value, 24);
  }

  public String getSnapshotValidationBucketNamePrefix() {
    return getStrProp(Fields.BACKUP_SNAPSHOT_VALIDATION_BUCKET_NAME_PREFIX.value, "");
  }

  public String getSnapshotValidationRoleArnPrefix() {
    return getStrProp(Fields.BACKUP_SNAPSHOT_VALIDATION_ROLE_ARN_PREFIX.value, "");
  }

  public boolean isMultiRegionJobCreationEnabled() {
    return getBoolProp("mms.backup.enableMultiRegionJobCreation", false);
  }

  public boolean isClusterLevelMultiRegionQueryableEnabled() {
    return getBoolProp("mms.backup.enableClusterLevelMultiRegionQueryable", false);
  }

  public String getDefaultReplicaSetScalingStrategy() {
    return getStrProp(Fields.MMS_ATLAS_DEFAULT_REPLICA_SET_SCALING_STRATEGY.value, "");
  }

  public ObjectId getNextProjectForIntegrationMigration() {
    final String nextProjectId =
        getProp(Fields.NEXT_PROJECT_FOR_INTEGRATION_MIGRATION.value, SettingType.DATABASE);
    if (StringUtils.isBlank(nextProjectId)) {
      return null;
    }
    return new ObjectId(nextProjectId);
  }

  public void setNextProjectForIntegrationMigration(final ObjectId id) {
    setProp(
        Fields.NEXT_PROJECT_FOR_INTEGRATION_MIGRATION.value,
        id == null ? null : id.toHexString(),
        SettingType.DATABASE);
  }

  public Duration getAtlasEncryptionAtRestPrivateEndpointStatusSyncInterval() {
    return Duration.ofSeconds(
        getLongProp(
            Fields.MMS_ATLAS_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_STATUS_SYNC_INTERVAL.value, 180));
  }

  public int getAtlasMaxHostCountForMonitoringAgents() {
    return getIntProp(Fields.MMS_ATLAS_MAX_HOST_COUNT_FOR_MONITORING.value, 2);
  }

  public boolean getExperimentsEnabled() {
    return getBoolProp(Fields.EXPERIMENTS_ENABLED.value, false);
  }

  public boolean getIsAtlasAccessTransparencyX509CertsEnabled() {
    return getBoolProp(Fields.MMS_ATLAS_ACCESS_TRANSPARENCY_X509_CERTS_ENABLED.value, false);
  }

  // This is a completely arbitrary delimiter value, as user agents can have commas in them
  private static final String USER_AGENT_DELIMITER = "\\{==\\}";

  public Set<String> getBlockedUserAgents() {
    return getSetProperty(Fields.IAM_BLOCKED_USER_AGENTS.value, USER_AGENT_DELIMITER);
  }

  public boolean configureOplogAfterRestore() {
    return getBoolProp(Fields.NDS_RESTORE_RESIZE_OPLOG_ENABLED.value, true);
  }

  public int getSnapshotValidationJobFrequencyInMinutes() {
    return getIntProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_FREQUENCY_IN_MINUTES.value, 5);
  }

  public float getSnapshotValidationExtraDiskSpaceFactor() {
    return getFloat(Fields.BACKUP_SNAPSHOT_VALIDATION_EXTRA_DISK_SPACE_FACTOR.value, 1.25f);
  }

  public boolean isSnapshotValidationJobSchedulerEnabled() {
    return getBoolProp(Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_SCHEDULER_ENABLED.value, false);
  }

  public List<String> getValidSnapshotValidationDeploymentIdList() {
    final List<String> deploymentIdList =
        getListProperty(
            Fields.BACKUP_SNAPSHOT_VALIDATION_JOB_VALID_DEPLOYMENT_ID_LIST.value, COMMA_DELIMITER);
    return deploymentIdList.isEmpty() ? Arrays.asList("NY_NJ", "us-east-1") : deploymentIdList;
  }

  public boolean isAppServicesGuideCueEnabled() {
    return getBoolProp(Fields.APP_SERVICES_GUIDE_CUE_ENABLED.value, false);
  }

  public boolean isNDSDeleteReapedClucstersCronEnabled() {
    return getBoolProp(Fields.NDS_DELETE_REAPED_CLUSTERS_CRON_ENABLED.value, false);
  }

  public boolean canUserOptIntoCloudNav(final ObjectId userId) throws Exception {
    final ConfigServiceRequestContext context =
        new ConfigServiceRequestContext.Builder().defaultValue(false).entityId(userId).build();
    // NOTE: This is an unorthodox use of the config service with feature flags!
    // Please do not copy/paste this without first checking in with Growth.
    // In most cases, feature flag access should be going through FeatureFlagSvc.java
    return configServiceSdkWrapper.getBoolean("global", "mms.featureFlag.allowCloudNav", context);
  }

  public int getLoginAttemptsAllowedBeforeTimeout() {
    return getIntProp("mms.login.ratelimit.attemptsAllowed", 0);
  }

  public int getLoginAttemptsTimeoutInMinutes() {
    return getIntProp("mms.login.ratelimit.lockedPeriodMinutes", 0);
  }

  public int getInviteSalesSoldRateLimitMaxAllowed() {
    return getIntProp(Fields.MMS_INVITE_SALES_SOLD_RATE_LIMIT_MAX_ALLOWED.value, 0);
  }

  public int getInviteRateLimitMaxAllowed() {
    return getIntProp(Fields.MMS_INVITE_RATE_LIMIT_MAX_ALLOWED.value, 0);
  }

  public int getInviteRateLimitMaxPeriodMinutes() {
    return getIntProp(Fields.MMS_INVITE_RATE_LIMIT_PERIOD_MINUTES.value, 0);
  }

  public boolean isISSForNewGroups() {
    return getBoolProp(Fields.NDS_INDEPENDENT_SHARD_SCALING_FOR_NEW_GROUPS.value, true);
  }

  public boolean isNdsRapidReleaseNonExperimentEvents() {
    return getBoolProp(Fields.NDS_IFR_RAPID_RELEASE_NON_EXPERIMENT_EVENTS.value, true);
  }

  public boolean isGradualRolloutEnabledForAbTesting() {
    return getBoolProp(Fields.AB_TESTING_GRADUAL_ROLLOUT_ENABLED.value, false);
  }

  public boolean isGcpProvisionedIOPSEnabled() {
    return getBoolProp(Fields.GCP_PROVISIONED_IOPS_ENABLED.value, false);
  }

  public int getNewDbUserScramSha1IterationCount() {
    return getIntProp(Fields.MMS_NEW_DB_USER_SCRAM_SHA1_ITERATION_COUNT.value, 10000);
  }

  public int getNewDbUserScramSha256IterationCount() {
    return getIntProp(Fields.MMS_NEW_DB_USER_SCRAM_SHA256_ITERATION_COUNT.value, 15000);
  }

  public int getAccessLevelSSHRequestsPercentageRollout() {
    return getIntProp(Fields.ACCESS_LEVEL_SSH_REQUESTS_PERCENTAGE_ROLLOUT.value, 0);
  }

  public String getAzureApiHttpLogDetailLevel() {
    return getString(Fields.AZURE_API_HTTP_LOG_DETAIL_LEVEL.value, "BASIC");
  }

  public Duration getVPCProxyInstanceDaoTTLDays() {
    var ttlDays = getLongProp(Fields.ATLAS_STREAMS_VPC_PROXY_INSTANCE_DAO_TTL.value, 90);
    return Duration.ofDays(ttlDays);
  }

  public Duration getVPCProxyDeploymentDaoTTLDays() {
    var ttlDays = getLongProp(Fields.ATLAS_STREAMS_VPC_PROXY_DEPLOYMENT_DAO_TTL.value, 90);
    return Duration.ofDays(ttlDays);
  }

  public String getVoyageControlPlaneURL() {
    return getString(Fields.VOYAGE_CONTROL_PLANE_URL.value, "");
  }

  /**
   * Whether to always update the versions of MongoDB tools and MongoDB Shell on the NDS side,
   * regardless of the version specified in the version manifest.
   *
   * @return true if always update is enabled, false otherwise
   */
  public boolean isAlwaysUpdateVersionsEnabled() {
    return getBoolProp(Fields.NDS_ALWAYS_UPDATE_VERSIONS.value, false);
  }

  public enum Fields {
    NEXT_PROJECT_FOR_INTEGRATION_MIGRATION("project.integrations.migrate.next"),
    AB_TESTING_ENABLED("mms.abTesting.enabled"),
    ACCOUNT_SUSPENSION_ENABLED("mms.accountSuspension.enabled"),
    ACCOUNT_DELETION_ENABLED("mms.accountDeletion.enabled"),
    SEND_ACCOUNT_DELETION_EMAIL_ENABLED("mms.sendAccountDeletionEmailEnabled"),
    NEW_ADMIN_USERS_PAGE_ENABLED("mms.enableNewAdminUsersPage"),
    BYPASS_SSO_DOMAINS("mms.federation.bypassSsoDomains"),
    CREATING_SAML_IDP_DISABLED("mms.federation.creatingSamlIdpDisabled"),
    ENABLE_OIDC_IDP_NON_ATLAS("mms.enableOidcIdpNonAtlas"),
    COMMENT_SERVICE_ENABLED("mms.enableCommentService"),
    AUTHZ_SERVICE_ENABLED("mms.authz.enableAuthzSvc"),
    AUTHZ_SERVICE_CLASS_OVERRIDE_ENABLED("mms.authz.authzSvcClassOverride.enabled"),
    CONFIG_SERVICE_GRPC_ADDRESS("client.config.grpc.address"),
    CONFIG_SERVICE_GRPC_ADDRESS_ENV_VAR("client.config.grpc.addressEnvVar"),
    AUTHN_SERVICE_GRPC_ADDRESS("client.authn.grpc.address"),
    AUTHN_SERVICE_GRPC_ADDRESS_ENV_VAR("client.authn.grpc.addressEnvVar"),
    AUTHZ_SERVICE_SYNC_RESOURCES("mms.authz.syncResources"),
    AUTHZ_SERVICE_SYNC_TEAMS_TO_USER_GROUPS("mms.authz.syncTeamsToUserGroups"),
    USER_GROUP_SYNC_FAILURE_JOB_RETRY_DELAY_MIN("mms.userGroupFailureJob.retryDelayInMin"),
    USER_GROUP_SYNC_FAILURE_JOB_NUM_RETRIES("mms.userGroupFailureJob.numberRetries"),
    AUTHZ_ORG_IDS_CACHE_DURATION_SECONDS("mms.authz.fgaOrgIdsCacheDurationSeconds"),
    AUTHZ_ORG_IDS_CACHE_ENABLED("mms.authz.fgaOrgIdsCache.enabled"),
    AUTHZ_ROLES_CACHE_ENABLED("mms.authz.rolesCache.enabled"),
    AUTHZ_ROLES_CACHE_DURATION_SECONDS("mms.authz.rolesCache.durationSeconds"),
    AUTHZ_ROLES_CACHE_SIZE("mms.authz.rolesCache.size"),

    AZURE_SSD_V2_REGIONS("mms.azure.ssdv2.regions"),

    AZURE_SSD_V2_ROLLOUT_REGIONS("mms.azure.ssdv2.rollout.regions"),
    AZURE_SSD_V2_PREVIEW_REGIONS("mms.azure.ssdv2.preview.regions"),
    RUN_FGA_OVER_RBAC_WHEN_ANNOTATED("mms.runFgaOverRbacWhenAnnotated"),
    ACCOUNT_MULTI_FACTOR_AUTH_ENABLED("mms.accountMultiFactorAuth.enabled"),
    ACCOUNT_SESSION_MAX_HOURS("account.session.maxHours"),
    ALERTS_ENABLED("alerts.enabled"),
    ALERTS_WEBHOOK_READ_TIMEOUT_MS("mms.alerts.webhook.readTimeoutMs"),
    ALERTS_WEBHOOK_SOCKET_TIMEOUT_MS("mms.alerts.webhook.socketTimeoutMs"),
    ALERTS_HOST_MEASUREMENT_LOOKBACK_DURATION_MILLIS(
        "mms.alerts.hostMeasurementLookbackDurationMillis"),
    ALLOW_OPS_MANAGER_ORGS("mms.allowOpsManagerOrgs"),
    ANALYTICS_ENABLED("analytics.enabled"),
    APPSETTINGS_DB_POLLING_INTERVAL("mms.appsettings.db.pollingInterval"),
    MMS_SECURITY_SECURE_RANDOM_POOL_SIZE("mms.security.secureRandom.poolSize"),
    APP_VERSION_PING_INTERVAL("mms.versionMismatch.pingInterval"),
    APP_VERSION_PING_INTERVAL_UNIT("mms.versionMismatch.pingInterval.unit"),
    //  In the case of ephemeral instances - how many pings we should miss to decide we should
    // remove it
    APP_VERSION_PINGS_TO_STALE("mms.versionMismatch.pingInterval.staleCount"),
    ASSETS_MINIFIED("mms.assets.minified"),
    ATLAS_CLUSTER_WEBSOCKET_CONNECTION_BASE_URL("mms.atlasClusterWebsocketConnection.baseUrl"),
    ATLAS_PROXY_VERSION("atlasProxy.version"),
    ATLAS_PROXY_MINIMUM_VERSION("atlasProxy.minimumVersion"),
    ATLAS_REGIONAL_DATA_ENABLED("atlas.regionalData.enabled"),
    ATLAS_REGIONAL_MAINTENANCE_JOBS_ENABLED("mms.monitoring.regional.maintenance.enabled"),
    ATLAS_UIS_VERSION("atlasUIS.version"),
    AUTH_GOOGLE_OKTA_IDP_ID("mms.auth.methods.google.oktaIdpId"),
    AUTH_GITHUB_OKTA_IDP_ID("mms.auth.methods.github.oktaIdpId"),
    MAX_MESSAGE_AGE_CUTOFF_MILLISECONDS("communication.message.age.cutoff.milliseconds"),
    AZURE_OPENAI_COMPASS_TIMEOUT("azure.ai.openai.compass.timeout"),
    AZURE_OPENAI_COMPASS_API_SECRET("azure.ai.openai.compass.secretKey"),
    AZURE_OPENAI_COMPASS_API_KEY_1("azure.ai.openai.compass.accessKey.1"),
    AZURE_OPENAI_COMPASS_API_BASE_1("azure.ai.openai.compass.apiBase.1"),
    AZURE_OPENAI_COMPASS_API_KEY_2("azure.ai.openai.compass.accessKey.2"),
    AZURE_OPENAI_COMPASS_API_BASE_2("azure.ai.openai.compass.apiBase.2"),
    AZURE_OPENAI_COMPASS_MODEL_NAME("azure.ai.openai.compass.modelName"),
    AZURE_OPENAI_COMPASS_ENDPOINT_MAX_QUERIES_PER_MINUTE(
        "azure.ai.openai.compass.maxQueriesPerMinute"),
    AZURE_OPENAI_COMPASS_ENDPOINT_MAX_QUERIES_PER_MINUTE_UNAUTHENTICATED(
        "azure.ai.openai.compass.maxQueriesPerMinuteUnauthenticated"),
    AZURE_OPENAI_CHARTS_API_KEY_1("azure.ai.openai.charts.accessKey.1"),
    AZURE_OPENAI_CHARTS_API_BASE_1("azure.ai.openai.charts.apiBase.1"),
    AZURE_OPENAI_CHARTS_API_KEY_2("azure.ai.openai.charts.accessKey.2"),
    AZURE_OPENAI_CHARTS_API_BASE_2("azure.ai.openai.charts.apiBase.2"),
    AZURE_OPENAI_CHARTS_GPT4O_API_KEY_1("azure.ai.openai.charts.gpt4o.accessKey.1"),
    AZURE_OPENAI_CHARTS_GPT4O_API_BASE_1("azure.ai.openai.charts.gpt4o.apiBase.1"),
    AZURE_OPENAI_MIGRATOR_API_KEY_1("azure.ai.openai.migrator.accessKey.1"),
    AZURE_OPENAI_MIGRATOR_API_BASE_1("azure.ai.openai.migrator.apiBase.1"),
    AZURE_OPENAI_MIGRATOR_API_KEY_2("azure.ai.openai.migrator.accessKey.2"),
    AZURE_OPENAI_MIGRATOR_API_BASE_2("azure.ai.openai.migrator.apiBase.2"),
    AZURE_OPENAI_MIGRATOR_GPT4O_API_KEY_1("azure.ai.openai.migrator.gpt4o.accessKey.1"),
    AZURE_OPENAI_MIGRATOR_GPT4O_API_BASE_1("azure.ai.openai.migrator.gpt4o.apiBase.1"),
    AZURE_OPENAI_MIGRATOR_GPT4_TURBO_API_KEY_1("azure.ai.openai.migrator.gpt4-turbo.accessKey.1"),
    AZURE_OPENAI_MIGRATOR_GPT4_TURBO_API_BASE_1("azure.ai.openai.migrator.gpt4-turbo.apiBase.1"),
    OKTA_SESSION_DISCOVERY_ENABLED("okta.sessionDiscovery.enabled"),
    AUTOMATION_AGENT_MINIMUM_VERSION("automation.agent.minimumVersion"),
    ATLAS_AUTOMATION_AGENT_MINIMUM_VERSION("automation.agent.atlasMinimumVersion"),
    AUTOMATION_AGENT_MINIMUM_VERSION_FOR_CLIENT_PIT("automation.agent.minimumVersionForClientPIT"),
    AUTOMATION_AGENT_PROM_INTERNAL_PORT("automation.agent.prom.internal.port"),
    AUTOMATION_AGENT_VERSION("automation.agent.version"),
    AUTOMATION_DEFAULT_BACKUP_AGENT_LOG_FILE("automation.default.backupAgentLogFile"),
    AUTOMATION_DEFAULT_BACKUP_AGENT_LOG_FILE_WINDOWS(
        "automation.default.backupAgentLogFileWindows"),
    AUTOMATION_DEFAULT_CPS_MODULE_LOG_FILE("automation.default.cpsModuleLogFile"),
    AUTOMATION_DEFAULT_ONLINE_ARCHIVE_MODULE_LOG_FILE(
        "automation.default.onlineArchiveModuleLogFile"),
    AUTOMATION_DEFAULT_DB_CHECK_MODULE_LOG_FILE("automation.default.dbCheckModuleLogFile"),
    AUTOMATION_DEFAULT_CA_FILE("automation.default.certificateAuthorityFile"),
    AUTOMATION_DEFAULT_CA_FILE_WINDOWS("automation.default.certificateAuthorityFileWindows"),
    AUTOMATION_DEFAULT_DATA_ROOT("automation.default.dataRoot"),
    AUTOMATION_DEFAULT_DOWNLOAD_BASE("automation.default.downloadBase"),
    AUTOMATION_DEFAULT_DOWNLOAD_BASE_WINDOWS("automation.default.downloadBaseWindows"),
    AUTOMATION_DEFAULT_MONGOSQLD_LOG_FILE("automation.default.mongoSqldLogFile"),
    AUTOMATION_DEFAULT_MONGOSQLD_LOG_FILE_WINDOWS("automation.default.mongoSqldLogFileWindows"),
    AUTOMATION_DEFAULT_MONITORING_AGENT_LOG_FILE("automation.default.monitoringAgentLogFile"),
    AUTOMATION_DEFAULT_MONITORING_AGENT_LOG_FILE_WINDOWS(
        "automation.default.monitoringAgentLogFileWindows"),
    AUTOMATION_SERVERLESS_AGENT_MINIMUM_VERSION("automation.agent.serverlessMinimumVersion"),
    AUTOMATION_SERVERLESS_AGENT_LOCATION("automation.agent.serverlessLocation"),
    AUTOMATION_SERVERLESS_AGENT_VERSION("automation.agent.serverlessVersion"),
    AUTOMATION_SERVERLESS_STATE_CACHE_LOCATION("automation.agent.serverlessStateCacheLocation"),
    AUTOMATION_SERVERLESS_STATE_CACHE_VERSION("automation.agent.serverlessStateCacheVersion"),
    AUTOMATION_VERSIONS_ATLAS_DECOUPLED_FROM_CM("automation.versions.atlasDecoupledFromCm"),
    AUTOMATION_VERSIONS_DIRECTORY("automation.versions.directory"),
    AUTOMATION_VERSIONS_SOURCE("automation.versions.source"),
    AUTOMATION_VERSIONS_DOWNLOAD_BASE_URL("automation.versions.download.baseUrl"),
    AUTOMATION_VERSIONS_AUTO_REFRESH_URI("automation.versions.autoRefreshUri"),
    AUTOMATION_VERSIONS_AUTO_REFRESH_ATLAS_URI("automation.versions.autoRefreshAtlasUri"),
    AUTOMATION_VERSIONS_AUTO_REFRESH_CM_URI("automation.versions.autoRefreshCmUri"),
    AWS_DATAWAREHOUSE_BUCKET("aws.dataWarehouse.bucket"),
    AWS_GRAVITON_MINIMUM_MONGODB_VERSION("awsGraviton.minimumMongoDBVersion"),
    AWS_RELEASE_AWS_IP_THRESHOLD_DAYS("aws.releaseAwsIpThreshold.days"),
    EMBEDDED_CONFIG_MINIMUM_MONGODB_VERSION("nds.embeddedConfig.minMongoDBVersion"),
    RS_DIRECT_TO_EMBEDDED_MINIMUM_MONGODB_VERSION(
        "nds.embeddedConfig.rsDirectToEmbeddedMinMongoDBVersion"),
    EMBEDDED_CONFIG_MAX_SHARD_COUNT("nds.embeddedConfig.maxShardCount"),
    BACKUP_AGENT_MINIMUM_VERSION("brs.agent.minimumVersion"),
    BACKUP_AGENT_VERSION("brs.agent.version"),
    BACKUP_AUTOMATED_RESTORE_EXP_HOURS("mms.backup.automatedRestoreExpirationHours"),
    BACKUP_BLOCKSTORE_MAXIMUM_SPACE_USED_PERCENT(
        "mms.alerts.OutsideSpaceUsedThreshold.maximumSpaceUsedPercent"),
    BACKUP_MAX_NUM_BUFFERED_FILE_BATCHES_ON_AGENT("brs.wtc.maxNumBufferedFileBatchesOnAgent"),
    BACKUP_CLIENT_PIT_VERSION("brs.client.pit.version"),
    BACKUP_DAEMON_LAST_PING("mms.alerts.DaemonDown.maximumPingAgeMinutes"),
    BACKUP_DAEMON_MINIMUM_HEAD_SPACE_FREE_GB("mms.alerts.LowHeadFreeSpace.minimumHeadFreeSpaceGB"),
    BACKUP_GZIP_COMPRESSION_LEVEL("mms.backup.gzipCompressionLevel"),
    BACKUP_S3_GZIP_COMPRESSION_LEVEL("mms.backup.gzipS3CompressionLevel"),
    BACKUP_VERY_LATE_SNAPSHOT_PERIOD("mms.alerts.lateSnapshot.lookbackDays"),
    BACKUP_KMIP_SERVER_CA_FILE("backup.kmip.server.ca.file"),
    BACKUP_KMIP_SERVER_HOST("backup.kmip.server.host"),
    BACKUP_KMIP_SERVER_PORT("backup.kmip.server.port"),
    BACKUP_METER_USAGE_BATCH_SIZE_KEY("brs.meterUsage.BatchSize"),
    INVITATION_CRON_BATCH_SIZE_KEY("mms.invitationCron.batchSizeBytes"),
    INVITATION_CRON_TIMEOUT_KEY("mms.invitationCron.timeoutMs"),
    BACKUP_OPLOG_PRUNE_JOB_INTERVAL_MINUTES("brs.oplog.prune.job.intervalMinutes"),
    BACKUP_OPLOG_ORPHAN_SLICE_JOB_INTERVAL_DAYS("brs.oplog.orphanSlice.job.intervalDays"),
    BACKUP_RESTORE_DIGEST_METHOD("brs.restore.digest.method"),
    BACKUP_RESTORE_PIT_GAP_CHECK_ENABLED("brs.pitRestoreGapCheckEnabled"),
    BACKUP_RESTORE_PIT_RESTORE_VALIDATION_ENABLED("brs.pitRestoreValidationEnabled"),
    BACKUP_RESTORE_PIT_WINDOW_HOURS("brs.pitWindowInHours"),
    BACKUP_RESTORE_BLOCK_FETCHER_TIMEOUT_THRESHOLD_SECONDS(
        "brs.blockFetcherTimeoutThresholdInSeconds"),
    BACKUP_WTC_ALLOW_TARGETING_WITH_IP_ADDRESS("brs.wtc.allowTargetingWithIpAddress"),
    BACKUP_WTC_FORCE_FULL_SNAPSHOT_INTERVALS("brs.wtc.forceFullSnapshotIntervals"),
    BACKUP_RESTORE_MAXIMUM_CONCURRENT_EXPORTS("nds.restore.maxConcurrentExports"),
    BACKUP_DESTROY_VM_BUFFER_MINS("mms.backup.destroyVmBuffer"),
    BACKUP_ROLLING_REPLACEMENT_EMAIL("mms.backup.rollingReplacement.email"),
    BACKUP_TUNNEL_VERSION("brs.tunnel.version"),
    BACKUP_MIN_FILES_FOR_SMALL_FILE_OPTIMIZATION(
        "mms.backup.wtcheckpoint.minFilesForSmallFileOptimization"),
    BACKUP_USE_S3_IN_INT_TESTS("mms.backup.useS3InIntTests"),
    BACKUP_SNAPSHOT_VALIDATION_EXTRA_DISK_SPACE_FACTOR(
        "mms.backup.snapshotValidation.extraDiskSpaceFactor"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_FREQUENCY_IN_MINUTES(
        "mms.backup.snapshotValidation.jobFrequencyInMinutes"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_SCHEDULER_ENABLED(
        "mms.backup.snapshotValidation.jobSchedulerEnabled"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_VALID_DEPLOYMENT_ID_LIST(
        "mms.backup.snapshotValidation.validDeploymentIdList"),
    BACKUP_SMALL_FILE_SIZE_LIMIT("mms.backup.wtcheckpoint.smallFileSizeLimit"),
    BACKUP_SNAPSHOT_VALIDATION_PRESIGNED_URL_EXPIRY_HOURS(
        "mms.backup.snapshotValidation.presignedUrl.expiryHours"),
    BACKUP_SNAPSHOT_VALIDATION_BUCKET_NAME_PREFIX("mms.backup.snapshotValidation.s3Bucket.prefix"),
    BACKUP_SNAPSHOT_VALIDATION_ROLE_ARN_PREFIX("mms.backup.snapshotValidation.roleArnPrefix"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_ENABLED("mms.backup.snapshotValidation.jobRunnerEnabled"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_RUNNING_JOB_LIMIT(
        "mms.backup.snapshotValidation.jobRunnerRunningJobLimit"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_FREQUENCY_IN_MINUTES(
        "mms.backup.snapshotValidation.jobRunnerFrequencyInMinutes"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_TTL_AFTER_COMPLETE_IN_SECONDS(
        "mms.backup.snapshotValidation.jobRunnerTTLAfterCompleteInSeconds"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_DELETE_IN_PROGRESS_SECRETS(
        "mms.backup.snapshotValidation.jobRunnerDeleteInProgressSecrets"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_PURGE_ALL_RUNNING_JOBS(
        "mms.backup.snapshotValidation.jobRunnerPurgeAllRunningJobs"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_MAX_ALLOWED_SIZE_IN_GB(
        "mms.backup.snapshotValidation.jobRunnerMaxAllowedSizeInGb"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_TARGET_MINIKUBE(
        "mms.backup.snapshotValidation.jobRunnerTargetMinikube"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_DELETE_SECRETS(
        "mms.backup.snapshotValidation.jobRunnerDeleteSecrets"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_RUNNER_DELETE_PERSISTENT_VOLUME_CLAIMS(
        "mms.backup.snapshotValidation.jobRunnerDeletePersistentVolumeClaims"),
    BACKUP_SNAPSHOT_VALIDATION_KUBERNETES_RETRY_LIMIT(
        "mms.backup.snapshotValidation.kubernetesRetryLimit"),
    BACKUP_SNAPSHOT_VALIDATION_KUBERNETES_NAMESPACE(
        "mms.backup.snapshotValidation.kubernetesNamespace"),
    BACKUP_SNAPSHOT_VALIDATION_KUBERNETES_CONTEXT(
        "mms.backup.snapshotValidation.kubernetesContext"),
    BACKUP_SNAPSHOT_VALIDATION_RUNNER_USE_DUMMY_IMAGE(
        "mms.backup.snapshotValidation.UseDummyImage"),
    BACKUP_SNAPSHOT_VALIDATION_RUNNER_USE_LOCAL_IMAGE(
        "mms.backup.snapshotValidation.UseLocalImage"),
    BACKUP_SNAPSHOT_VALIDATION_PINNED_IMAGE_VERSION(
        "mms.backup.snapshotValidation.PinnedImageVersion"),
    BACKUP_SNAPSHOT_VALIDATION_INGESTION_ENDPOINT_FOR_DEVELOPMENT(
        "mms.backup.snapshotValidation.IngestionEndpointForDevelopment"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_UNHEALTHY_THRESHOLD_IN_SECONDS(
        "mms.backup.snapshotValidation.jobUnhealthyThresholdInSeconds"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_DRY_RUN_ENABLED("mms.backup.snapshotValidation.dryRunEnabled"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_DELAY_COMPLETION_ENABLED(
        "mms.backup.snapshotValidation.delayCompletionEnabled"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_DELAY_COMPLETION_IN_SECONDS(
        "mms.backup.snapshotValidation.delayCompletionInSeconds"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_HTTP_CLIENT_CONNECTION_TIMEOUT_IN_SECONDS(
        "mms.backup.snapshotValidation.httpClient.connectionTimeoutInSeconds"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_HTTP_CLIENT_MAX_RETRY_ATTEMPTS(
        "mms.backup.snapshotValidation.httpClient.maxRetryAttempts"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_MONGO_MANAGER_CONNECT_TIMEOUT_IN_MS(
        "mms.backup.snapshotValidation.mongoManager.connectTimeoutInMs"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_MONGO_MANAGER_STOP_TIMEOUT_IN_MS(
        "mms.backup.snapshotValidation.mongoManager.stopTimeoutInMs"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_PRE_SIGNED_URL_ENABLED(
        "mms.backup.snapshotValidation.preSignedUrlEnabled"),
    BACKUP_SNAPSHOT_VALIDATION_JOB_METRICS_LOOKBACK_SECS(
        "mms.backup.snapshotValidation.jobsMetricsLookbackSecs"),
    BACKUP_SNAPSHOT_VALIDATION_SECRET_VOLUME_MOUNT_POINT(
        "mms.backup.snapshotValidation.secretVolumeMountPoint"),
    BACKUP_SNAPSHOT_VALIDATION_DATA_VOLUME_MOUNT_POINT(
        "mms.backup.snapshotValidation.dataVolumeMountPoint"),
    BACKUP_SNAPSHOT_VALIDATION_DOWNLOADER_FILE_THREAD_POOL_SIZE(
        "mms.backup.snapshotValidation.downloader.globalFileThreadPoolSize"),
    BACKUP_SNAPSHOT_VALIDATION_DOWNLOADER_BLOCK_THREAD_POOL_SIZE(
        "mms.backup.snapshotValidation.downloader.globalBlockThreadPoolSize"),
    BACKUP_SNAPSHOT_VALIDATION_DOWNLOADER_BLOCK_QUEUE_SIZE(
        "mms.backup.snapshotValidation.downloader.maxBlockQueueSize"),
    BACKUP_SNAPSHOT_VALIDATION_DOWNLOADER_PIPE_SIZE_IN_MEGABYTES(
        "mms.backup.snapshotValidation.downloader.pipeSizeInMegabytes"),
    BACKUP_SNAPSHOT_VALIDATION_JVM_MAX_HEAP_SPACE_IN_GIGABYTES(
        "mms.backup.snapshotValidation.jvm.maxHeapSpaceInGigaBytes"),
    SERVERLESS_BACKUP_TEMPORARY_LOCK_TIMEOUT("nds.serverless.backup.temporaryLockTimeout"),
    SERVERLESS_BACKUP_SUBNET_BUFFER("nds.serverless.backup.subnetBuffer"),
    SERVERLESS_BACKUP_RESTORE_MTM_LIMIT("nds.serverless.backup.restore.mtm.limit"),
    SERVERLESS_BACKUP_RESTORE_MTM_POOL_LIMIT("nds.serverless.backup.restore.mtm.pool.limit"),
    SERVERLESS_BACKUP_NETWORK_ACCESS_LIST_ENABLED("nds.serverless.backup.networkAccessListEnabled"),
    SERVERLESS_BACKUP_SNAPSHOT_NEEDED_CHECK_INTERVAL_MINUTES(
        "nds.serverless.backup.snapshotNeededCheckIntervalMinutes"),
    SERVERLESS_BACKUP_ONDEMAND_SNAPSHOT_INTERVAL_MINUTES(
        "nds.serverless.backup.ondemandSnapshotIntervalMinutes"),
    BI_CONNECTOR_MINIMUM_VERSION("biConnector.minimumVersion"),
    BI_CONNECTOR_VERSION("biConnector.version"),
    ATLAS_BI_CONNECTOR_VERSION("atlas.biConnector.version"),
    ATLAS_API_VERSIONING_ENABLED("atlas.api.versioning.enabled"),
    ATLAS_API_VERSIONING_REJECT_FUTURE_DATE_CALLS("atlas.api.versioning.rejectFutureDateCalls"),
    GEN_AI_COMPASS_ACCESS_PERCENT("mms.generativeAI.compass.percentAccessEnabled"),
    GEN_AI_COMPASS_THREADS_MAX_PERMITS("mms.generativeAI.compass.maxRunningPermits"),
    GEN_AI_COMPASS_THREADS_MAX_TIMEOUT("mms.generativeAI.compass.maxTimeoutMillis"),
    BILLING_ENABLED("mms.billing.enabled"),
    BILLING_CRON_JOBS_DISABLED("mms.billing.cronJobsDisabled"),
    BILLING_AUDITORS_JIRA_INTEGRATION_ENABLED("mms.billing.auditors.jiraIntegrationEnabled"),
    BILLING_INVOICE_STATUS_EMAIL_ADDRESS("mms.billing.invoicingStatusEmailAddress"),
    CORE_BI_MAILING_EMAIL_ADDRESS("mms.billing.coreBIEmailAddress"),
    BROWSER_ERROR_TRACKING_APIKEY("mms.assets.browserErrorTrackingApiKey"),
    BROWSER_ERROR_TRACKING_ENABLED("mms.assets.browserErrorTracking"),
    BYPASS_INVITATIONS_EXISTING_USERS("mms.user.bypassInviteForExistingUsers"),
    BUCKETED_METRIC_ROLLUP_ENABLED("mms.monitoring.ingestion.bucketedMetricRollup.enabled"),
    BUCKETED_METRIC_ROLLUP_BUCKET_COUNT(
        "mms.monitoring.ingestion.bucketedMetricRollup.bucketCount"),
    BUCKETED_METRIC_ROLLUP_SERVERLESS_BUCKET_COUNT(
        "mms.monitoring.ingestion.bucketedMetricRollup.serverlessBucketCount"),
    BUCKETED_METRIC_ROLLUP_REALM_BUCKET_COUNT(
        "mms.monitoring.ingestion.bucketedMetricRollup.realmBucketCount"),
    INVITATION_ONLY("mms.user.invitationOnly"),
    INVITATION_ONLY_REDIRECT_URL("mms.user.invitationOnly.redirectUrl"),
    ASSETS_HOSTNAME("mms.assets.assetsHostname"),
    FERN_FRONT_END("mms.assets.fernHostedFrontEndWebpack"),

    ASSETS_CROSS_ORIGIN("mms.assets.crossOrigin"),
    CHARTS_REALM_API_SECRET("charts.apiSecret"),
    CHARTS_STITCH_APP_ID("charts.stitch.app.id"),
    CHARTS_VERSION("charts.version"),
    CMAB_SERVICE_URL_SUFFIX("cmabService.urlSuffix"),
    GEN_AI_COMPASS("mms.generativeAI.compass.enabled"),
    GEN_AI_COMPASS_UNAUTHENTICATED("mms.generativeAI.compass.enableUnauthenticated"),
    GEN_AI_DATA_EXPLORER("mms.generativeAI.dataExplorer.enabled"),
    DETECT_VERSION_MISMATCH("mms.versionMismatch.detect"),
    DATA_EXPLORER_RATE_LIMIT_CACHE_ENABLED("mms.dataExplorer.rateLimit.cache.enabled"),
    DATA_EXPLORER_RATE_LIMIT_ENABLED("mms.dataExplorer.rateLimit.enabled"),
    DATA_EXPLORER_RATE_LIMIT("mms.dataExplorer.rateLimit"),
    DATA_EXPLORER_RATE_LIMIT_CACHE_SIZE("mms.dataExplorer.rateLimit.cacheSize"),
    DATA_EXPLORER_RATE_LIMIT_CACHE_EXPIRATION_MINUTES(
        "mms.dataExplorer.rateLimit.cacheExpirationMinutes"),
    DATA_LAKE_ADMIN_API_URL("queryengine.adminApi.url"),
    DATA_LAKE_ADMIN_API_AZURE_URL("queryengine.adminApi.azure.url"),
    DATA_LAKE_ADMIN_API_GCP_URL("queryengine.adminApi.gcp.url"),
    DATA_LAKE_FRONTEND_PORT("queryengine.frontend.port"),
    DATA_LAKE_FRONTEND_AZURE_PORT("queryengine.frontend.azure.port"),
    DATA_LAKE_FRONTEND_GCP_PORT("queryengine.frontend.gcp.port"),
    DARK_MODE_PREVIEW_ENABLED("mms.darkMode.preview.enabled"),
    DARK_MODE_ACCOUNT_APP_ENABLED("mms.darkMode.account.enabled"),
    CLOUD_NAV_OVERRIDE_ENABLED("mms.cloudNav.override.enabled"),
    CLOUD_NAV_CLOUD_MANAGER_ENABLED("mms.cloudNav.cloudManager.enabled"),
    DATABASE_IDP_DISCOVERY_ENABLED("mms.databaseIdpDiscovery.enabled"),
    DATABASE_IDP_DISCOVERY_MONGO_OKTA_ID("mms.databaseIdpDiscovery.mongoOktaId"),
    DOCS_URL("mms.docsUrl"),
    DOCS_SEARCH_URL("mms.docsUrl.search"),
    ECOSYSTEM_ENABLED("ecosystem.enabled"),
    EMAIL_INCLUDE_APP_ENV_NAME("mms.email.includeAppEnvName"),
    EMAIL_LOGO("mms.email.logo"),
    EMAIL_LOGO_HEIGHT("mms.email.logo.height"),
    EMAIL_LOGO_WIDTH("mms.email.logo.width"),
    EMAIL_REPLY_ADDR_ACCOUNT_SUPPORT("account.email.replyAddr.support"),
    EMAIL_REPLY_ADDR_CLOUD_SUPPORT("mms.email.replyAddr.support"),
    EMAIL_REPLY_ADDR_CLOUD_ALERTS("mms.email.replyAddr.alerts"),
    EMAIL_FROM_ADDR_ACCOUNT_SUPPORT("account.email.fromAddr.support"),
    EMAIL_FROM_ADDR_CLOUD_SUPPORT("mms.email.fromAddr.support"),
    EMAIL_FROM_ADDR_CLOUD_ALERTS("mms.email.fromAddr.alerts"),
    EMAIL_VALIDATION("mms.email.validation"),
    EMAIL_VERIFICATION_ENABLED("mms.emailVerificationEnabled"),
    ENABLE_OLD_HOST_CHECK("mms.enableOldHostCheck"),
    ENFORCE_LAST_AUTH_MINS("mms.security.enforceLastAuthMins"),
    GA_ACCOUNT_ID("ga.accountId"),
    GA_ENABLED("ga.enabled"),
    GA_P12_FILE("ga.p12file"),
    GA_PROPERTY_ID("ga.propertyId"),
    GA_SERVICE_ACCOUNT("ga.serviceAccount"),
    GA_VIEW_ID("ga.viewId"),
    GCP_SERVICE_MESH_HOST_REGION_NAME("queryengine.adminApi.gcp.regionName"),
    GIT_BRANCH("git.branch"),
    GIT_LATEST_COMMIT("git.latestCommit"),
    GOV_UPLIFT_PERCENTAGE_PROPERTY("mms.billing.govUpliftPercentage"),
    GROUP_TYPE_DEFAULT("mms.groupType.default"),
    GTM_CONTAINER_ID("gtm.containerId"),
    HOSTED("mms.hosted"),
    HOSTED_VERSION("mms.hosted.version"),
    HTTP_PROXY_HOST("http.proxy.host"),
    HTTP_PROXY_PASSWORD("http.proxy.password"),
    HTTP_PROXY_PORT("http.proxy.port"),
    HTTP_PROXY_USERNAME("http.proxy.username"),
    HTTP_PROXY_NON_PROXY_HOSTS("http.proxy.nonProxyHosts"),
    DISABLE_HTTP_PROXY_FOR_S3("http.proxy.disabled.for.s3"),
    IGNORE_VERSION_MISMATCH("mms.versionMismatch.ignore"),
    INTERCOM_API_KEY("intercom.apiKey"),
    INTERCOM_APP_ID("intercom.appId"),
    INTERCOM_ENABLED("intercom.enabled"),
    INTERCOM_ENABLED_LOGGED_IN_PAGES("intercom.enabledLoggedInPages"),
    INTERCOM_QUALTRICS_SURVEY_API_TOKEN("intercom.qualtricsSurvey.api.token"),
    INTERCOM_SECRET_KEY("intercom.secretKey"),
    JAVA_FIPS_PROVIDER_DEFAULT("java.fipsprovider.default"),
    JOB_PROCESSOR_ENABLED_RUNTIME("job.processor.enabled.runtime"),
    LOW_PREPAID_CREDIT_EMAIL_ADDRESS("mms.billing.lowPrepaidCreditEmailAddress"),
    SERVERLESS_PROXY_VERSION("serverlessProxy.version"),
    SERVERLESS_PROXY_LOCATION("serverlessProxy.location"),
    SERVERLESS_PROXY_MINIMUM_VERSION("serverlessProxy.minimumVersion"),
    RAMI_VERSION("rami.version"),
    RAMI_LOCATION("rami.location"),
    RAMI_DATA_COLLECTOR_PERIOD_IN_SECS("rami.dataCollectorPeriodInSecs"),
    RAMI_MITIGATOR_PERIOD_IN_SECS("rami.mitigatorPeriodInSecs"),
    MONGOTUNE_VERSION("mongotune.version"),
    MONGOTUNE_PREVIOUS_VERSION("mongotune.previous.version"),
    MONGOTUNE_LOCATION("mongotune.location"),
    MONGOTUNE_LOGLEVEL("mongotune.loglevel"),
    MONGOTUNE_LOG_FILE_MODE("mongotune.logFileMode"),
    MONGOTUNE_ROTATE_USER_AFTER_AGENT_VERSION("mongotune.rotateUserAfterAgentVersion"),
    METERING_USER_PRIVATE_KEY("metering.user.privateKey"),
    METERING_SERVICE_MESH_ENV_VAR("metering.serviceMeshEnvVar"),
    METERING_USER_PUBLIC_KEY("metering.user.publicKey"),
    METERING_READ_FROM_DB("metering.readFromDb"),
    METERING_CENTRAL_URL("metering.centralUrl"),
    METERING_CENTRAL_URL_SCHEME("metering.centralUrlScheme"),
    MMS_ADD_USER_API_COUNTRY_REQUIRED("mms.addUserApi.countryRequired"),
    MMS_APP_HOSTNAME("mms.app.hostname"),
    MMS_ALERTS_GLOBAL_SUMMARY_EMAIL_INTERVAL_HOURS("mms.alerts.globalSummaryEmailIntervalHours"),
    MMS_ALERTS_WEBHOOK_ADMIN_ENDPOINT("mms.alerts.webhook.adminEndpoint"),
    MMS_ALERTS_WEBHOOK_ADMIN_SECRET("mms.alerts.webhook.adminSecret"),
    MMS_ALL_CLUSTERS_ONLY_MEMBERSHIP("mms.allclusters.onlyMembership"),
    MMS_BACKUP_DEFAULT_REGION("mms.backup.region.default"),
    MMS_BACKUP_NOSYNCSTORE("mms.backup.noSyncStore"),
    MMS_BACKUP_CUTOVER_DB_INTERVAL_REFRESH_SECONDS("mms.backup.cutoverDbRefreshIntervalSeconds"),
    MMS_BILLING_BACKUP_USAGE_ENABLED("mms.billing.backupUsage.enabled"),
    MMS_BILLING_STATUSPAGE_ENABLED("mms.billing.statusPage.enabled"),
    MMS_ALERT_PROCESSING_ENABLED("mms.alertProcessing.service.enabled"),
    MMS_BILLING_RESOURCETAGGING_TEST("mms.billing.resourceTagging.test"),
    MMS_CANONICAL_HOSTS_TTL_MINUTES("mms.monitoring.canonicalHostsTTLMinutes"),
    MMS_CANONICAL_HOSTS_TTL_WRITES_PROBABILISTIC_PERCENTAGE(
        "mms.monitoring.canonicalHostsTTLWrites.probabilisticPercentage"),
    MMS_CANONICAL_HOSTS_TTL_WRITES_GUARANTEED_PERCENTAGE(
        "mms.monitoring.canonicalHostsTTLWrites.guaranteedPercentage"),
    MMS_SECURITY_ENABLE_HTTP_REFERRER("mms.security.enableHttpReferrer"),
    MMS_PUBLIC_API_GLOBAL_KEY_ROLE_DENYLIST("mms.publicApi.globalKeyRoleDenylist"),
    MMS_LOGIN_NEW_DEVICE_NOTIFICATION_ENABLED("mms.login.newDeviceNotification.enabled"),
    MMS_HTTPS_CAFILE("mms.https.CAFile"),
    MMS_HTTPS_PEMKEYFILE("mms.https.PEMKeyFile"),
    MMS_HTTPS_PEMKEYFILE_PASSWORD("mms.https.PEMKeyFilePassword"),
    MONGOT_VERSION("mongot.version"),
    MONGOT_MINIMUM_VERSION("mongot.minimumVersion"),

    MONGOT_MMS_CONFIG_UPDATE_PERIOD_SECONDS("mongot.mmsConfigUpdatePeriodSeconds"),
    MONGODB_TOOLS_VERSION("mongotools.version"),
    MONGOSH_VERSION("mongosh.version"),
    MONITORING_AGENT_MINIMUM_VERSION("monitoring.agent.minimumVersion"),
    MONITORING_AGENT_VERSION("monitoring.agent.version"),
    MONITORING_CHARTDOMAIN("mms.monitoring.chartDomain"),
    MONITORING_FTS_METRICS_CLUSTER_ENABLED("mms.monitoring.rrd.fts.metricscluster.enabled"),
    MONITORING_REALM_REQUEST_THREADS("mms.monitoring.realm.requestThreads"),
    MONITORING_REALM_ENABLED("mms.monitoring.realm.enabled"),

    MONITORING_FTS_ENABLED("mms.monitoring.rrd.ftsMaintenanceEnabled"),
    MONITORING_REALM_RECENT_MEASUREMENTS_INTERVAL_MILLIS(
        "mms.monitoring.realm.recentMeasurementsInterval"),
    MONITORING_REALM_APP_DISCOVERY_INTERVAL_SECS("mms.monitoring.realm.appDiscoveryInterval"),
    MONITORING_TOKENIZED_QUERY_SHAPE_STATS_METRICS_COLLECTION_INTERVAL_MILLIS(
        "mms.monitoring.tokenizedQueryShapeStatsMetrics.collectionIntervalMillis"),
    MONITORING_COLL_STATS_STORAGE_STATS_METRICS_COLLECTION_INTERVAL_MILLIS(
        "mms.monitoring.collStatsStorage.collectionIntervalMillis"),
    MONITORING_QUERYSTATS_METRICS_COLLECTION_INTERVAL_MILLIS(
        "mms.monitoring.queryStats.agent.collectionIntervalMillis"),
    MULTI_FACTOR_AUTH_ALLOW_RESET("mms.multiFactorAuth.allowReset"),
    MULTI_FACTOR_AUTH_ISSUER("mms.multiFactorAuth.issuer"),
    MULTI_FACTOR_AUTH_LEVEL("mms.multiFactorAuth.level"),
    MULTI_FACTOR_AUTH_ENCOURAGEMENT_ENABLED("mms.multiFactorAuth.encouragement.enabled"),
    MULTI_FACTOR_AUTH_ENCOURAGEMENT_ENABLED_FOR_NO_FACTORS(
        "mms.multiFactorAuth.encouragement.enabledForNoFactors"),
    NDS_METER_USAGE_SUBMISSION_ENABLED("nds.meterUsage.submission.enabled"),
    NDS_KMS_PRIVATE_NETWORKING_METER_USAGE_SUBMISSION_ENABLED(
        "nds.kms.privateNetworking.meterUsage.submission.enabled"),
    NDS_METER_USAGE_SUBMISSION_HOURS("nds.meterUsage.submission.hours"),
    NDS_PREMIUM_SKU_METER_USAGE_SUBMISSION_ENABLED("nds.premiumSKU.meterUsage.submission.enabled"),
    NDS_METER_USAGE_ENABLED_METER_IDS("nds.meterUsage.enabled.meterIds"),
    NDS_METER_USAGE_GROUPS_PERCENTAGE("nds.meterUsage.groups.percentage"),
    NDS_METER_USAGE_SUBMISSION_SPREAD("nds.meterUsage.submission.spread"),
    NDS_METER_USAGE_SUBSCRIPTION_SPREAD("nds.meterUsage.subscriptionMetering.spread"),
    NDS_METER_USAGE_RETRY_SUBMISSION_SPREAD("nds.meterUsage.retrySubmission.spread"),
    NDS_BILLING_PURGE_ENABLED("nds.billing.purge.enabled"),
    NDS_BILLING_PURGE_IS_DRY_RUN("nds.billing.purge.isDryRun"),
    NDS_BILLING_PURGE_EXPIRE_AFTER_SECONDS("nds.billing.purge.expireAfterSeconds"),
    NDS_BILLING_PURGE_MAX_NUM_THREADS("nds.billing.purge.maxNumThreads"),
    NDS_BILLING_PURGE_MIN_EXPIRATION_DURATION_DAYS("nds.billing.purge.minExpirationDurationDays"),
    NDS_BILLING_PURGE_DELETE_LIMIT("nds.billing.purge.deleteLimit"),
    NDS_BILLING_PURGE_BULK_DELETE_BATCH_SIZE("nds.billing.purge.bulkDeleteBatchSize"),
    NDS_BILLING_PURGE_MAX_HOURS_DELETE_LIMIT("nds.billing.purge.maxHoursDeleteLimit"),
    NDS_ACME_CERTIFICATE_CHAIN_FILE("nds.acme.certificateChainFile"),
    NDS_ACME_ORDER_TRACKING_PUBLIC_KEY("mms.acme.tracking.publicKey"),
    NDS_ACME_ORDER_TRACKING_PRIVATE_KEY("mms.acme.tracking.privateKey"),
    NDS_ADMIN_REASONS_REQUIRE_JIRA("nds.admin.reasonsRequireJira"),
    NDS_ADMIN_DATA_PLANE_ACCESS_REQUESTS_ONLINE_VALIDATION(
        "nds.admin.dataPlaneAccessRequestsOnlineValidation"),
    NDS_AGENT_ENVOY_CONFIG_SERVER_ENABLED("nds.agent.envoyConfigServer.enabled"),
    NDS_AGENT_ENVOY_CONFIG_SERVER_NODE_ID("nds.agent.envoyConfigServer.nodeId"),
    NDS_AGENT_ENVOY_CONFIG_SERVER_PORT("nds.agent.envoyConfigServer.port"),
    NDS_AGENT_ENVOY_CONFIG_SERVER_ADMIN_PORT("nds.agent.envoyConfigServer.adminPort"),
    NDS_AGENT_ENVOY_CONFIG_SERVER_PROM_EXTERNAL_PORT(
        "nds.agent.envoyConfigServer.promExternalPort"),
    NDS_AGENT_ENVOY_CONFIG_SERVER_PROM_INTERNAL_PORT(
        "nds.agent.envoyConfigServer.promInternalPort"),
    NDS_AGENT_ENVOY_CONFIG_SERVER_GATEWAY_PROXY_EXTERNAL_PORT(
        "nds.agent.envoyConfigServer.gatewayProxyExternalPort"),
    NDS_AGENT_ENVOY_CONFIG_SERVER_MONGOT_PROM_PORT("nds.agent.envoyConfigServer.mongotPromPort"),

    NDS_AGENT_ENVOY_CONFIG_SERVER_SEARCH_ENVOY_ADMIN_PORT(
        "nds.agent.envoyConfigServer.searchEnvoyAdminPort"),
    NDS_AGENT_ENVOY_CONFIG_SERVER_TLS_CERT_CONFIG_PATH(
        "nds.agent.envoyConfigServer.tlsCertConfigPath"),
    NDS_AGENT_ENVOY_CONFIG_SERVER_TLS_FILTER_SERVER_NAME(
        "nds.agent.envoyConfigServer.tlsFilterServerName"),
    NDS_AUTO_SCALING_COMPUTE_ENABLED("nds.autoScaling.compute.enabled"),
    NDS_BURSTABLE_AUTO_SCALING_NEW_CLUSTER_ENABLED(
        "nds.autoScaling.burstable.compute.newClusterEnabled"),
    NDS_AUTO_SCALING_MONITORING_INGESTION_QUEUE_SIZE(
        "mms.monitoring.ingestion.ndsautoscaling.queuesize"),
    NDS_AUTO_SCALING_MONITORING_INGESTION_POOL_SIZE(
        "mms.monitoring.ingestion.ndsautoscaling.poolsize"),
    NDS_AUTO_SCALING_MONITORING_INGESTION_OFFER_TIME_MILLIS(
        "mms.monitoring.ingestion.ndsautoscaling.offerTimeMillis"),
    NDS_AUTO_SCALING_SCALE_DOWN_TIME_ZONE(
        "nds.autoScaling.compute.initiateScaleDownWindow.timeZone"),
    NDS_AUTO_SCALING_SCALE_DOWN_DAYS("nds.autoScaling.compute.initiateScaleDownWindow.days"),
    NDS_AUTO_SCALING_SCALE_DOWN_HOURS("nds.autoScaling.compute.initiateScaleDownWindow.hours"),
    NDS_AUTO_SCALING_DOWN_SCALE_CHECK_CLOUD_PROVIDER_CONSTRAINTS(
        "nds.autoScaling.downScale.checkCloudProviderConstraints"),
    NDS_AZURE_OPLOG_STORE_UNSUPPORTED_REGIONS("nds.azure.oplogStore.unsupportedRegions"),
    NDS_CAPACITY_RESERVATION_CRON_PROJECT("mms.azCapacity.internalProjectId"),
    NDS_CPS_OPLOG_USAGE_COLLECTION("mms.cps.billing.oplogUsageCollection"),
    NDS_CPS_OPLOG_USAGE_COLLECTION_THREAD_COUNT("mms.cps.billing.oplogUsageCollection.threadCount"),
    NDS_CPS_AWS_SNAPSHOT_USAGE_SUBMISSION_THREAD_COUNT(
        "mms.cps.billing.aws.snapshotUsageSubmission.threadCount"),
    NDS_CPS_GCP_SNAPSHOT_USAGE_SUBMISSION_THREAD_COUNT(
        "mms.cps.billing.gcp.snapshotUsageSubmission.threadCount"),
    NDS_CPS_AZURE_SNAPSHOT_USAGE_SUBMISSION_THREAD_COUNT(
        "mms.cps.billing.azure.snapshotUsageSubmission.threadCount"),
    NDS_CPS_OPLOG_USAGE_COLLECTION_GROUP_BATCH_SIZE(
        "mms.cps.billing.oplogUsageCollection.groupBatchSize"),
    NDS_CPS_OPLOG_SUBMISSION("mms.cps.billing.oplogUsageSubmission"),
    NDS_CPS_OPLOG_STORAGE_REGION_USAGE("mms.cps.billing.oplogStorageRegionUsage"),
    NDS_CPS_OPLOG_USAGE_MIGRATION("mms.cps.billing.oplogUsageMigration"),

    NDS_CPS_AWS_SNAPSHOT_USAGE_SUBMISSION("mms.cps.billing.cpsAwsSnapshotUsageSubmission"),
    NDS_CPS_AZURE_SNAPSHOT_USAGE_SUBMISSION("mms.cps.billing.cpsAzureSnapshotUsageSubmission"),
    NDS_CPS_GCP_SNAPSHOT_USAGE_SUBMISSION("mms.cps.billing.cpsGcpSnapshotUsageSubmission"),
    NDS_CPS_EXPORT_USAGE_SUBMISSION("mms.cps.billing.cpsExportUsageSubmission"),
    NDS_CPS_AWS_DOWNLOAD_USAGE_SUBMISSION("mms.cps.billing.cpsAwsDownloadUsageSubmission"),
    NDS_CPS_AWS_EXPORT_USAGE_SUBMISSION("mms.cps.billing.cpsAwsExportUsageSubmission"),
    NDS_CPS_AZURE_EXPORT_USAGE_SUBMISSION("mms.cps.billing.cpsAzureExportUsageSubmission"),
    NDS_CPS_AZURE_DOWNLOAD_USAGE_SUBMISSION("mms.cps.billing.cpsAzureDownloadUsageSubmission"),
    NDS_CPS_GCP_DOWNLOAD_USAGE_SUBMISSION("mms.cps.billing.cpsGcpDownloadUsageSubmission"),
    NDS_CPS_GCP_EXPORT_USAGE_SUBMISSION("mms.cps.billing.cpsGcpExportUsageSubmission"),
    NDS_CPS_CONF_CALL_SESSION_WINNER_TIMEOUT_IN_MINS("nds.cps.confCallSessionWinnerTimeoutInMins"),
    NDS_CPS_OPLOG_PURGE_MAX_DB_WRITE_BATCH_SIZE("nds.cps.oplogPurgeMaxDbWriteBatchSize"),
    NDS_CPS_PIT_APPLY_OPS_WITH_CONF_CALL_SESSION_UPDATE(
        "nds.cps.pitApplyOpsWithConfCallSessionUpdate"),
    NDS_CPS_OPLOG_GAP_TIMESTAMP("mms.cps.oplogGap.timestamp"),
    NDS_CPS_USE_SIMPLE_QUERY_TO_FIND_SNAPSHOTS_TO_PURGE(
        "mms.cps.snapshots.purge.simple.query.enabled"),
    NDS_CPS_EXPORT_WITH_THREE_NODES_PER_SHARD_THRESHOLD_SIZE_GB(
        "mms.cps.exportWithThreeNodesPerShardThresholdGB"),
    NDS_DEVICE_SYNC_DEBUG_ACCESS_SHARED_SECRET(
        "nds.instances.secrets.deviceSyncDebugAccessSharedSecret"),
    NDS_GCP_BILLING_REPORT_BUCKET_NAME("nds.gcp.billingReport.bucketName"),
    NDS_GCP_BILLING_REPORT_PREFIX("nds.gcp.billingReport.prefix"),
    NDS_GCP_SNAPSHOT_BILLING_REPORT_PROJECT_ID("nds.gcp.SnapshotBillingReport.projectId"),
    NDS_GCP_SNAPSHOT_BILLING_REPORT_DATASET("nds.gcp.SnapshotBillingReport.dataset"),
    NDS_GCP_PSC_REGION_GROUP_DEFAULT_SIZE("nds.gcp.psc.regionGroup.defaultSize"),
    NDS_DRY_RUN_MODE_ENABLED("nds.dry.run.mode.enabled"),
    NDS_QUEUED_ADMIN_ACTIONS_ENABLED("mms.admin.queuedAdminActions.enabled"),
    MMS_QUEUED_ADMIN_ACTIONS_UI_ENABLED("mms.admin.queuedAdminActions.ui.enabled"),
    NDS_RESPECT_PROTECTED_HOURS_MAINTENANCE_ADMIN_UI_ENABLED(
        "nds.admin.createRespectProtectedHoursMaintenanceAdminUi.enabled"),
    NDS_GROUP_SSH_KEY_PERMITS_PER_USER("nds.sshkey.maximumPermitsPerUser"),
    NDS_MTM_AUTOSCALE_CAPACITY_ENABLED("nds.mtm.autoscale.capacity.enabled"),
    NDS_ALLOW_UNRESTRICTED_AGENT_API_KEY("nds.agentApiKey.allowUnrestricted"),
    NDS_SERVERLESS_MTM_AUTOSCALE_CAPACITY_ENABLED("nds.serverless.mtm.autoscale.capacity.enabled"),
    NDS_FLEX_MTM_AUTOSCALE_CAPACITY_ENABLED("nds.flex.mtm.autoscale.capacity.enabled"),
    NDS_MTM_UPDATE_SERVERLESS_LOAD_DOCUMENTS_ENABLED(
        "nds.serverless.mtm.load.updateDocuments.enabled"),
    NDS_TENANT_UPDATE_SERVERLESS_LOAD_DOCUMENTS_ENABLED(
        "nds.serverless.tenant.load.updateDocuments.enabled"),
    NDS_MTM_ORGANIZATION_ID("nds.mtm.organization.id"),
    NDS_LEAKED_ITEM_CLEANUP_RETRIES("nds.leakedItem.cleanup.retries"),
    NDS_LEAKED_ITEM_DETECTION_RETRIES("nds.leakedItem.detection.retries"),
    NDS_LEAKED_ITEM_CLEANUP_MODE("nds.leakedItem.cleanup.mode"),
    NDS_AWS_LEAKED_ITEM_DETECTION_MODE("nds.leakedItem.detection.aws.mode"),
    NDS_AZURE_LEAKED_ITEM_DETECTION_MODE("nds.leakedItem.detection.azure.mode"),
    NDS_GCP_LEAKED_ITEM_DETECTION_MODE("nds.leakedItem.detection.gcp.mode"),
    NDS_GCP_LEAKED_ITEM_DETECTION_CHUNK_SIZE("nds.leakedItem.detection.gcp.chunkSize"),
    NDS_GCP_LEAKED_ITEM_DETECTION_PROJECTS_PROCESSED_PER_ITERATION(
        "nds.leakedItem.detection.gcp.projectsProcessedPerIteration"),
    NDS_GCP_LEAKED_ITEM_DETECTION_JOB_SCHEDULED_FOR_WINDOW_MINUTES(
        "nds.leakedItem.detection.gcp.scheduledForSplayMinutes"),
    NDS_DELETE_REAPED_CLUSTERS_CRON_ENABLED("nds.delete.reaped.clusters.cron.enabled"),
    NDS_LEAKED_ITEM_HANDLER_ENABLE_DRY_RUN("nds.leakedItem.handler.enableDryRun"),

    NDS_JIRA_PAT("nds.jira.privateAccessToken"),
    NDS_JIRA_BYPASS_JIRA_STAGING_FIREWALL_HEADER("nds.jira.bypassJiraStagingFirewallHeader"),
    NDS_JIRA_BYPASS_JIRA_STAGING_FIREWALL_VALUE("nds.jira.bypassJiraStagingFirewallValue"),
    NDS_IFR_S3_BUCKET_NAME("nds.ifr.s3BucketName"),
    NDS_IFR_S3_REGION_NAME("nds.ifr.s3RegionName"),
    NDS_IFR_S3_BUCKET_READ_ONLY_ARN("nds.ifr.s3BucketReadOnlyRoleArn"),
    NDS_IFR_S3_BUCKET_EXPERIMENT_ID_OVERRIDE("nds.ifr.s3BucketExperimentIdOverride"),
    NDS_IFR_S3_BUCKET_WAVE_NUMBER_OVERRIDE("nds.ifr.s3BucketWaveNumberOverride"),
    DEFAULT_STATUS_CHECK_INTERVAL("nds.azStatusCheck.intervalSeconds"),
    NDS_INDEPENDENT_SHARD_SCALING_FOR_NEW_GROUPS("nds.independentShardScaling.newGroups"),
    NDS_IFR_RAPID_RELEASE_NON_EXPERIMENT_EVENTS("nds.ifr.rapidReleaseNonExperimentEvents"),
    NDS_FAST_SHARED_TIER_ENABLED("nds.fastSharedTier.enabled"),
    NDS_FAST_SERVERLESS_PROVISIONING_ENABLED("nds.fastServerlessProvisioning.enabled"),
    NDS_FAST_FLEX_PROVISIONING_ENABLED("nds.fastFlexProvisioning.enabled"),
    NDS_FLEX_MODE_FOR_SERVERLESS_ENABLED("nds.flexModeForServerless.enabled"),
    NDS_SERVERLESS_LOAD_BALANCER_DEFAULT_NUM_PRE_ALLOCATED_RECORDS(
        "nds.serverless.loadbalancer.defaultNumPreAllocatedRecords"),
    NDS_SERVERLESS_MTM_DRAIN_TIME_BUFFER("nds.serverless.mtmDrain.timeBuffer"),
    NDS_SERVERLESS_MERGE_STRATEGY_CPU_UTILIZATION_THRESHOLD(
        "nds.serverless.mergeStrategy.cpuUtilizationThreshold"),
    NDS_FAST_PRECREATE_CRON_SVC_ENABLED("nds.fastSharedTier.precreateCronSvc.enabled"),
    NDS_FIXED_VERSIONS_VERSION_MANAGER_UI_ENABLED("nds.fixedVersions.versionManager.ui.enabled"),
    NDS_MAX_FAST_SHARED_RECORDS_PER_REGION("nds.fastSharedTier.maxRecordsPerRegion"),
    NDS_MAX_FAST_FLEX_RECORDS_PER_REGION("nds.fastFlex.maxRecordsPerRegion"),
    NDS_FLEX_API_SHARED_MODE("nds.flex.api.shared.mode"),
    NDS_FLEX_API_SERVERLESS_MODE("nds.flex.api.serverless.mode"),
    NDS_LEAKED_LEAKED_ITEMS_PROCESSOR_MINIMUM_ITEM_LIFE_SECONDS(
        "nds.leakedItemsProcessor.minimumItemLifeSeconds"),
    NDS_SEARCH_METRICS_MAX_SEARCH_INDEX_WRITE_COUNT("nds.metrics.fts.maxSearchIndexStatCount"),
    NDS_METRICS_DELIVERY_SYSTEM_FOR_BILLING_ENABLED("nds.metrics.deliverySystemForBilling.enabled"),
    SERVERLESS_RESTORE_VM_USAGE_BILLING_ENABLED(
        "nds.serverless.backup.restoreVmUsageBilling.enabled"),
    NDS_RESTORE_RESIZE_OPLOG_ENABLED("nds.backup.configureOplogSizeAfterRestore.enabled"),
    NDS_SERVERLESS_ORGANIZATION_ID("nds.serverless.mtm.organization.id"),
    NDS_FLEX_ORGANIZATION_ID("nds.flex.mtm.organization.id"),
    NDS_SERVERLESS_SENTINEL_ORGANIZATION_ID("nds.serverless.sentinel.organization.id"),
    NDS_SHARED_SENTINEL_ORGANIZATION_ID("nds.shared.sentinel.organization.id"),
    NDS_FLEX_SENTINEL_ORGANIZATION_ID("nds.flex.sentinel.organization.id"),
    NDS_MTM_SENTINEL_CRON_ENABLED("nds.mtm.sentinel.cron.enabled"),
    NDS_SERVERLESS_PROXY_DB_CACHE_MILLIS("nds.serverless.proxy.dbstatsCacheMillis"),
    NDS_SHARED_PROXY_DB_CACHE_MILLIS("nds.shared.proxy.dbstatsCacheMillis"),
    NDS_SERVERLESS_PROXY_MTM_SOCKET_TIMEOUT_SECS("nds.serverless.proxy.mtmSocketTimeoutSecs"),
    NDS_SERVERLESS_ALLOW_SETUP_SERVERLESS_CREATE_TEST_INSTANCE(
        "nds.serverless.allowSetupServerlessCreateTestInstance"),
    NDS_SERVERLESS_FEATURE_ENABLED("nds.serverless.feature.enabled"),
    NDS_FLEX_FEATURE_ENABLED("nds.flex.feature.enabled"),
    NDS_FLEX_MIGRATION_WARNING_ENABLED("nds.flex.migration.warning.enabled"),
    NDS_SERVERLESS_TRACE_AUTOSCALE_ENABLED("nds.serverless.trace.autoscale.enabled"),
    NDS_SERVERLESS_TRACE_AUTOSCALE_EXPORTER("nds.serverless.trace.autoscale.exporter"),
    NDS_SERVERLESS_TRACE_AUTOSCALE_PREFIX("nds.serverless.trace.autoscale.prefix"),
    NDS_SERVERLESS_TRACE_AUTOSCALE_PROCESSOR("nds.serverless.trace.autoscale.processor"),
    NDS_SERVERLESS_SUPPORTS_LB_DEPLOYMENT_ID_ON_ENVOY_INSTANCE(
        "nds.serverless.supportsLBDeploymentIdOnEnvoyInstance"),
    NDS_SERVERLESS_PRIVATE_NETWORKING_SUPPORTED_CLOUD_PROVIDERS(
        "nds.serverless.privateNetworking.supportedCloudProviders"),
    NDS_SERVERLESS_METRICS_USE_MAX_IOPS("nds.serverless.metrics.useMaxIops"),
    NDS_SERVERLESS_UPGRADE_TO_DEDICATED_ENABLED(
        "nds.serverless.serverlessUpgradeToDedicated.enabled"),
    NDS_SHARED_PROXY_MTM_SOCKET_TIMEOUT_SECS("nds.shared.proxy.mtmSocketTimeoutSecs"),
    NDS_PLAN_STATE_IN_MEMORY_OBJECT_ROLLOUT_PERCENTAGE(
        "nds.planState.inMemoryObject.rolloutPercentage"),
    NDS_PLAN_STATE_IN_MEMORY_OBJECT_READS_ENABLED("nds.planState.inMemoryObject.reads.enabled"),
    NDS_PLANNING_TRACE_ENABLED("nds.planning.trace.enabled"),
    NDS_PLANNING_TRACE_EXPORT_LIMIT("nds.planning.trace.export.limit"),
    NDS_PLANNING_TRACE_MIN_GROUP_COUNT_BEFORE_EXPORT(
        "nds.planning.trace.minGroupCountBeforeExport"),
    NDS_PLANNING_TRACE_DURATION_THRESHOLD_MILLIS("nds.planning.trace.durationThresholdMillis"),
    NDS_PLANNING_TRACE_REPORT_ROOT_SPAN_LIMIT("nds.planning.trace.report.rootSpanLimit"),
    NDS_PLANNING_TRACE_REPORT_ENABLED("nds.planning.trace.report.enabled"),
    NDS_PLANNING_TRACE_REPORT_RECIPIENT_EMAIL_ADDR("nds.planning.trace.report.recipientEmailAddr"),
    NDS_PLANNING_OTEL_EXPORT_ENABLED("nds.planning.trace.otelExportEnabled"),
    NDS_PROXY_MINIMUM_VERSION("nds.proxy.minimumVersion"),
    NDS_PROXY_VERSION("nds.proxy.version"),
    NDS_PROXY_NA_REGEX_THROTTLING_MILLIS("nds.proxy.throttling.nonAnchoredRegexMillis"),
    NDS_PROXY_NA_REGEX_THROTTLING_WATCHED_NAMESPACES(
        "nds.proxy.throttling.nonAnchoredRegexWatchedNamespaces"),
    NDS_SITE_NAME("mms.siteName.nds"),
    NDS_GOV_US_SITE_NAME("nds.gov.us.siteName"),
    NDS_GOV_US_ENABLED("nds.gov.us.enabled"),
    NDS_GOV_US_GCP_CMEK_PROJECT_NAME("nds.gov.us.gcp.cmek.projectName"),
    NDS_ROOT_CERT("nds.root.cert"),
    NDS_GW_PROXY_ENABLED("nds.gateway.proxy.enabled"),
    NDS_LIVE_IMPORT_MONGOSYNC_LOG_TTL_DAYS("nds.liveImport.mongosyncLog.ttlDays"),
    NDS_LIVE_IMPORT_MONGOSYNC_VERSION("nds.liveImport.mongosync.version"),
    NDS_LIVE_IMPORT_MONGOSYNC_LATEST_VERSION("nds.liveImport.mongosync.latestVersion"),
    NDS_PUSH_LIVE_IMPORT_MONGOSYNC_VERSION("nds.pushLiveImport.mongosync.version"),
    NDS_LIVE_IMPORT_MONGOMIRROR_VERSION("nds.liveImport.mongomirror.version"),
    NDS_LIVE_IMPORT_MONGOSYNC_WORKING_DIR("nds.liveImport.mongosyncWorkingDir"),
    NDS_LIVE_IMPORT_MONGOSYNC_BINARY_PATH_PREFIX("nds.liveImport.mongosync.binaryPathPrefix"),
    NDS_LIVE_IMPORT_MONGOSYNC_VERIFIER_HELIX_CPU_REQ(
        "nds.liveImport.mongosync.verifierHelixCPUReq"),
    NDS_LIVE_IMPORT_MONGOSYNC_VERIFIER_HELIX_MEMORY_REQ(
        "nds.liveImport.mongosync.verifierHelixMemoryReq"),
    NDS_MONGOSYNC_LOG_VERBOSITY("nds.mongosync.log.verbosity"),
    NDS_MONGOSYNC_MAX_NUM_PARALLEL_PARTITIONS("nds.liveImport.mongosync.maxNumParallelPartitions"),
    NDS_MONGOSYNC_NUM_INSERTERS_PER_PARTITION("nds.liveImport.mongosync.numInsertersPerPartition"),
    NDS_MONGOSYNC_BASE_MEMORY_REQUIREMENT_GB("nds.liveImport.mongosync.baseMemoryRequirementGB"),
    NDS_MONGOSYNC_VERIFIER_MEMORY_RATE_GB_PER_MILLION_DOCS(
        "nds.liveImport.mongosync.verifierMemoryRateGBPerMillionDocs"),
    NDS_MONGOSYNC_SOURCE_GB_SIZE_LIMIT_TO_OVERRIDE_NUM_PARTITIONS_INSERTERS(
        "nds.liveImport.mongosync.sourceSizeLimitToOverridePartitionsAndInserters"),
    NDS_MONGOSYNC_VERIFIER_START_AT_PHASE("nds.liveImport.mongosync.verifierStartAtPhase"),
    NDS_LIVE_IMPORT_MONGOMIRROR_DOCKER_IMAGE_TAG("nds.liveImport.mongomirror.dockerImageTag"),
    NDS_LIVE_IMPORT_KUBE_RESTARTS_ENABLED("nds.liveImport.kubeRestarts.enabled"),
    NDS_LIVE_IMPORT_KUBE_RESOURCE_CLEANER_ENABLED("nds.liveImport.kubeResourceCleaner.enabled"),
    NDS_LIVE_MIGRATION_SERVERS_HAS_PUBLIC_IP("nds.liveImport.servers.hasPublicIp"),
    DRY_RUN_INTEGRATION_DELETION("integrations.deletion.dryRun"),
    NDS_LIVE_IMPORT_MONGOMIRROR_OPLOG_BATCH_SIZE("nds.liveImport.mongomirror.oplogBatchSize"),
    NDS_LIVE_IMPORT_VALIDATION_JOB_TIMEOUT_MINS("nds.liveImport.validationJob.timeoutMins"),
    NS_JVM_KRB5_CONF("jvm.java.security.krb5.conf"),
    NS_JVM_KRB5_KDC("jvm.java.security.krb5.kdc"),
    NS_JVM_KRB5_REALM("jvm.java.security.krb5.realm"),
    NS_MMS_KERBEROS_DEBUG("mms.kerberos.debug"),
    NS_MMS_KERBEROS_KEYTAB("mms.kerberos.keyTab"),
    NS_MMS_KERBEROS_PRINCIPAL("mms.kerberos.principal"),
    OFAC_ENABLED("mms.ofac.enabled"),
    OFAC_NOTIFICATION_EMAIL_ADDRESS("mms.ofac.notificationEmailAddr"),
    OFAC_SUPPORT_EMAIL_ADDRESS("mms.ofac.supportEmailAddr"),
    OKTA_CMAB_CLIENT_SECRET("okta.cmab.client.secret"),
    OKTA_CMAB_CLIENT_ID("okta.cmab.client.id"),
    OKTA_USER_GROUP_IDS("okta.users.groupids"),
    OKTA_SESSION_MAX_MINUTES("okta.session.maxMinutes"),
    PERSONALIZATION_WIZARD_ENABLED("mms.personalizationWizardEnabled"),
    PERSONALIZATION_WIZARD_REDIRECT_ENABLED("mms.personalizationWizardRedirectEnabled"),
    PHOLIOTA_GET_EXPERIMENTS_URL("pholiota.getExperimentsUrl"),
    PHOLIOTA_MMS_API_KEY("pholiota.mmsApiKey"),
    PHOLIOTA_MMS_HTTP_REFERER("pholiota.mmsHttpReferer"),
    EXPERIMENTS_ENABLED("experiments.enabled"),
    EXPERIMENTSSVC_FALLBACK_FILE("experimentsSvc.fallbackFile"),
    PRODUCTION_SCORE_HOSTNAME("productionScore.hostname"),
    PUBLIC_API_ENABLE_FOR_GLOBAL_ROLES_FROM_SUBNETS(
        "mms.publicApi.enableForGlobalRolesFromSubnets"),
    PUBLIC_API_GLOBAL_READ_ONLY_USER_PERIOD_MINUTES(
        "mms.publicApi.globalReadOnlyUserPeriodMinutes"),
    PUBLIC_API_GLOBAL_READ_ONLY_USER_LIMIT("mms.publicApi.globalReadOnlyUserLimit"),
    PUBLIC_API_METRIC_RATE_LIMIT_GRANULARITY("mms.publicApi.metric.rateLimitGranularity"),
    PUBLIC_API_METRIC_RATE_LIMIT_MAX_PER_GROUP("mms.publicApi.metric.rateLimitMaxPerGroup"),
    PUBLIC_API_PERFORMANCE_ADVISOR_RATE_LIMIT_GRANULARITY(
        "mms.publicApi.performanceAdvisor.rateLimitGranularity"),
    PUBLIC_API_PERFORMANCE_ADVISOR_RATE_LIMIT_MAX_PER_GROUP(
        "mms.publicApi.performanceAdvisor.rateLimitMaxPerGroup"),
    PUBLIC_API_QUERY_SHAPE_INSIGHTS_RATE_LIMIT_GRANULARITY(
        "mms.publicApi.queryShapeInsights.rateLimitGranularity"),
    PUBLIC_API_QUERY_SHAPE_INSIGHTS_RATE_LIMIT_MAX_PER_GROUP(
        "mms.publicApi.queryShapeInsights.rateLimitMaxPerGroup"),
    PUBLIC_API_DATA_EXPLORER_RATE_LIMIT_GRANULARITY(
        "mms.publicApi.dataExplorer.rateLimitGranularity"),
    PUBLIC_API_DATA_EXPLORER_RATE_LIMIT_MAX_PER_GROUP(
        "mms.publicApi.dataExplorer.rateLimitMaxPerGroup"),
    PUBLIC_API_EVENTS_RATE_LIMIT_GRANULARITY("mms.publicApi.events.rateLimitGranularity"),
    PUBLIC_API_EVENTS_RATE_LIMIT_MAX_PER_GROUP("mms.publicApi.events.rateLimitMaxPerGroup"),
    PUBLIC_API_BACKUP_RATE_LIMIT_GRANULARITY("mms.publicApi.backup.rateLimitGranularity"),
    PUBLIC_API_BACKUP_RATE_LIMIT_MAX_PER_GROUP("mms.publicApi.backup.rateLimitMaxPerGroup"),
    PUBLIC_API_MAX_TEMP_KEY_LIMIT("mms.publicApi.maxTempKeys"),
    REALM_METRICS_NUM_REQUEST_PAGES_1_MIN("mms.monitoring.realm.realmMetricPages1Min"),
    RECAPTCHA_ENABLED_EXTERNAL_REGISTRATION("reCaptcha.enabled.externalRegistration"),
    RECAPTCHA_ENABLED_LOGIN("reCaptcha.enabled.login"),
    RECAPTCHA_ENABLED_ATLAS("reCaptcha.enabled.atlas"),
    RECAPTCHA_PRIVATE_KEY("reCaptcha.private.key"),
    RECAPTCHA_PUBLIC_KEY("reCaptcha.public.key"),
    REDIRECT_IF_PASSWORD_IS_EMPTY("mms.user.redirectIfPasswordIsEmpty"),
    REMOTE_IP_HEADER("mms.remoteIp.header"),
    RESTORE_QUERYABLE_MONGOD_MEMORYQUOTAMB("brs.queryable.mongod.memoryQuotaMB"),
    RESTRICTED_USERNAME_DOMAINS("mms.user.username.restrictedDomains"),
    SECURITY_BACKDOOR("mms.security.backdoor"),
    SEGMENT_CLIENT_SIDE_WRITE_KEY("segment.clientSide.writeKey"),
    SEGMENT_SERVER_SIDE_WRITE_KEY("segment.serverSide.writeKey"),
    SEGMENT_DEFAULT_USER_ID("segment.default.userId"),
    SELF_SERVE_PAYMENTS_ENABLED("mms.billing.selfServePayments.enabled"),
    SENTRY_API_KEY("mms.server.sentry.apiKey"),
    SESSION_MAX_HOURS("mms.session.maxHours"),
    SESSION_IDLE_TIME_MINUTES("mms.session.idleTimeoutMinutes"),
    SESSION_CUSTOM_TIMEOUTS_ENABLED("mms.session.customSessionTimeouts.enabled"),
    SFDC_SYNC_CRON_ENABLED("sfdc.sync.cron.enabled"),
    SFSC_SYNC_ENABLED("mms.billing.sfscSyncEnabled"),
    SITE_FULL_NAME("mms.siteFullName"),
    SITE_NAME("mms.siteName"),
    SNAPSHOT_WTC_NUM_FILES_LIMIT("brs.wtc.snapshotNumFilesLimit"),
    SNAPSHOT_SCHEDULE_INTERVAL("brs.snapshotSchedule.interval"),
    SNAPSHOT_SCHEDULE_RETENTION_BASE("brs.snapshotSchedule.retention.base"),
    SNAPSHOT_SCHEDULE_RETENTION_DAILY("brs.snapshotSchedule.retention.daily"),
    SNAPSHOT_SCHEDULE_RETENTION_MONTHLY("brs.snapshotSchedule.retention.monthly"),
    SNAPSHOT_SCHEDULE_RETENTION_WEEKLY("brs.snapshotSchedule.retention.weekly"),
    STALE_AUTH_CODE_LIMIT("mms.staleAuthCodeLimit"),
    STREAM_PROCESS_MANAGER_ENABLED("streams.processManager.enabled"),
    STREAM_PROCESS_MANAGER_URL("streams.processManager.url"),
    STRIPE_API_KEY("stripe.apiKey.inc"),
    STRIPE_API_KEY_LTD("stripe.apiKey.ltd"),
    STRIPE_PUB_KEY("stripe.pubKey.inc"),
    STRIPE_PUB_KEY_LTD("stripe.pubKey.ltd"),
    STRIPE_WEBHOOK_UPDATECARD_SECRET("stripe.webhook.updatecard.secret"),
    STRIPE_WEBHOOK_UPDATECARD_SECRET_LTD("stripe.webhook.updatecard.secret.ltd"),
    THEME_DEFAULT("mms.darkMode.default"),
    TWILIO_ACCOUNT_SID("twilio.account.sid"),
    TWILIO_AUTH_TOKEN("twilio.auth.token"),
    TWILIO_FROM_NUM("twilio.from.num"),
    TWILIO_MESSAGINGSERVICE_ID("twilio.messagingService.sid"),
    UI_CACHE_STATIC("mms.ui.cacheStatic"),
    USE_SOFT_GROUP_DELETE("mms.group.useSoftDelete"),
    GROUP_SEARCH_MAX_NUM_OF_RETURN_ALLOWED("mms.limit.groupSearchMaxNumOfReturnAllowed"),
    USER_SVC_CLASS("mms.userSvcClass"),
    VERCEL_CLIENT_ID("mms.vercel.clientId"),
    VERCEL_INTEGRATION_ENABLED("mms.vercelIntegration.enabled"),
    VERCEL_SECRET("mms.vercel.secret"),
    STITCH_BILLING_API_URL("stitch.billing.api.url"),
    REALM_BILLING_API_URL("realm.billing.api.url"),
    REALM_METRICS_API_URL("mms.monitoring.realm.metrics.api.url"),
    STITCH_ANALYTICS_API_URL("stitch.analytics.api.url"),
    STITCH_API_KEY("stitch.api.key"),
    SLACK_OAUTH2_CLIENT_ID("slack.oauth2.clientId"),
    STITCH_NEXT_BACKFILL_DATE_MS_PROP("stitch.usageTrackingNextBackfillDateMs"),
    STITCH_MONTHLY_FREE_FUNCTION_CALLS("stitch.monthlyFreeFunctionCalls"),
    STITCH_MONTHLY_FREE_MEM_TIME_GB_SECONDS("stitch.monthlyFreeMemTimeGigabyteSeconds"),
    CHARTS_NEXT_BACKFILL_DATE_MS_PROP("charts.usageTrackingNextBackfillDateMs"),
    EMAIL_DAO_CLASS("mms.emailDaoClass"),
    DIAGNOSTIC_ARCHIVE_COUNT_LIMIT("mms.admin.diagnostics.archiveDocCountLimit"),
    DIAGNOSTIC_ARCHIVE_SIZE_LIMIT("mms.admin.diagnostics.archiveDocTotalSizeLimit"),
    DIAGNOSTIC_ARCHIVE_AGE_LIMIT("mms.admin.diagnostics.archiveDocAgeLimit"),
    AUTOMATION_DIAGNOSTIC_LAST_AGENT_STATUS_DOC_AGE_LIMIT(
        "mms.automation.diagnostics.lastAgentStatusDocAgeLimit"),
    AUTOMATION_DIAGNOSTIC_INCLUDE_EMPTY_PROCESS_CONFIGS(
        "mms.automation.diagnostics.includeEmptyProcessConfigs"),
    ALLOW_LOCALHOST_CORS("mms.security.cors.allow.localhost.origin"),
    MMS_CRON_ENABLED("mms.cron.enabled"),
    MMS_CRON_JOB_ACQUISITION_STRATEGIES("mms.cron.job.acquisition.strategies"),
    MMS_CRON_FREQUENCY_DEBOUNCE_THRESHOLD_SECONDS("mms.cron.frequency.debounce.threshold.seconds"),
    XDS_SERVER_ENABLED("mms.serverless.xDSServer.enabled"),
    DEFAULT_ENVOY_INSTANCE_SIZE("mms.serverless.envoyInstanceSize"),
    XDS_PORT("nds.xdsPort"),
    XDS_BIND_ADDRESS("nds.xds.bindAddress"),
    SERVERLESS_XDS_TLS("mms.serverless.xDSServer.tlsRequired"),
    SERVERLESS_XDS_TRUSTED_CA_FILE_PATH("mms.serverless.xDSServer.trustedCAFilePath"),
    STRIPE_LEVEL3_INTEGRATION_ENABLED("stripe.level3.enabled"),
    DNS_CRON_INTERVAL_SECONDS("nds.dns.cron.interval.seconds"),
    ADFA_NEW_REGIONS_PENDING_SUPPORT("adfa.newRegions.pendingSupport"),
    OKTA_TOKEN_AUTHORIZED_CLIENT_IDS("okta.token.authorized.client.ids"),
    NDS_REDIS_PRIMARY_SERVER_HOST("nds.externalcaching.redis.primary.host"),
    NDS_REDIS_PRIMARY_SERVER_PORT("nds.externalcaching.redis.primary.port"),
    NDS_REDIS_REPLICA_SERVER_HOST("nds.externalcaching.redis.replica.host"),
    NDS_REDIS_REPLICA_SERVER_PORT("nds.externalcaching.redis.replica.port"),
    NDS_REDIS_USERNAME("nds.externalcaching.redis.username"),
    NDS_REDIS_PASSWORD("nds.externalcaching.redis.password"),
    NDS_REDIS_COMMAND_TIMEOUT_MS("nds.externalcaching.redis.command.timeoutMS"),
    NDS_REDIS_META_COMMAND_TIMEOUT_MS("nds.externalcaching.redis.metaCommand.timeoutMS"),
    NDS_REDIS_CONNECT_TIMEOUT_MS("nds.externalcaching.redis.connect.timeoutMS"),
    NDS_REDIS_ENTRY_TTL_HOURS("nds.externalcaching.redis.entry.ttlHours"),
    NDS_REDIS_ENTRY_EXTEND_TTL_BEFORE_EXPIRY_THRESHOLD_MINS(
        "nds.externalcaching.redis.entry.extendTtlBeforeExpiryThresholdMins"),
    NDS_REDIS_ENTRY_MAX_EXTRA_TTL_SECS("nds.externalcaching.redis.entry.maxExtraTTLSecs"),
    NDS_REDIS_ENTRY_READ_THROUGH_ON_CACHE_ERROR(
        "nds.externalcaching.redis.readThroughOnCacheError"),
    NDS_REDIS_CACHE_ENABLED("nds.externalcaching.redis.cacheEnabled"),
    NDS_REDIS_SSL_ENABLED("nds.externalcaching.redis.sslEnabled"),

    NDS_DOMAIN_AUTH_ROUTE_REDIS_ENABLED("nds.externalcaching.redis.domainAuthRedisEnabled"),
    DOMAIN_AUTH_EXPIRATION_ENTRY_TTL_DAYS(
        "nds.domainAuthExpirationLookupSvc.redisCache.entry.ttlDays"),
    CLEANUP_DELETED_INGESTION_PIPELINES_THREAD_POOL_SIZE(
        "nds.deleted.ingestion.pipelines.thread.pool.size"),
    CLEANUP_EXPIRED_INGESTION_PIPELINES_THREAD_POOL_SIZE(
        "nds.expired.ingestion.pipelines.thread.pool.size"),
    CLEANUP_EXPIRED_DATA_SETS_THREAD_POOL_SIZE("nds.expired.datasets.thread.pool.size"),
    CLIENT_METRICS_RATE_LIMIT_MAX_PER_USER("mms.clientmetrics.rateLimitMaxPerUser"),
    DELETED_INGESTION_PIPELINES_QUERY_LIMIT("nds.deleted.ingestion.pipelines.query.limit"),
    EXPIRED_INGESTION_PIPELINES_QUERY_LIMIT("nds.expired.ingestion.pipelines.query.limit"),
    INGESTION_PIPELINES_NUM_SNAPSHOT_LOAD_LIMIT("nds.ingestion.pipelines.num.snapshot.load.limit"),
    EXPIRED_DATA_SETS_QUERY_LIMIT("nds.expired.datasets.query.limit"),
    DELETED_INGESTION_PIPELINE_GRACE_PERIOD("nds.deleted.ingestion.pipeline.grace.period.minutes"),
    DELETED_DATA_SET_GRACE_PERIOD("nds.deleted.dataset.grace.period.minutes"),
    CLEANUP_DELETED_ARCHIVES_THREAD_POOL_SIZE("nds.deleted.archive.thread.pool.size"),
    CLEANUP_DELETED_ARCHIVES_THREAD_TIMEOUT_MINUTES("nds.deleted.archive.thread.timeout.minutes"),
    NDS_LOOKUP_SVC_REDIS_ENTRY_TTL_HOURS("nds.lookupSvc.redisCache.entry.ttlHours"),
    NDS_LOOKUP_SVC_REDIS_ENTRY_EXTEND_TTL_BEFORE_EXPIRY_THRESHOLD_MINS(
        "nds.lookupSvc.redisCache.entry.extendTtlBeforeExpiryThresholdMins"),
    NDS_LOOKUP_SVC_REDIS_ENTRY_MAX_EXTRA_TTL_SECS("nds.lookupSvc.redisCache.entry.maxExtraTTLSecs"),
    NDS_LOOKUP_SVC_REDIS_ENTRY_READ_THROUGH_ON_CACHE_ERROR(
        "nds.lookupSvc.redisCache.entry.readThroughOnCacheError"),
    PUBLIC_API_PROMETHEUS_RATE_LIMIT_GRANULARITY("mms.publicApi.prometheus.rateLimitGranularity"),
    PUBLIC_API_PROMETHEUS_RATE_LIMIT_MAX_PER_REMOTE_ADDRESS(
        "mms.publicApi.prometheus.rateLimitMaxPerRemoteAddress"),
    PROMETHEUS_INTEGRATION_BURST_TOKEN_COUNT("mms.monitoring.prometheus.burstTokenCount"),
    PROMETHEUS_INTEGRATION_TOKEN_FILL_RATE_SECONDS("mms.monitoring.prometheus.tokenFillRate"),
    PROMETHEUS_LISTENING_PORT("prom.listening.port"),
    AUTO_INDEXING_SHARED_TIER_MAX_INDEXES("mms.autoIndexing.sharedTier.maxIndexes"),
    PUSH_LIVE_MIGRATIONS_ENABLED("mms.pushLiveMigrations.enabled"),
    PUSH_LIVE_MIGRATIONS_VPC_PEERING_ENABLED("mms.pushLiveMigrations.vpcPeering.enabled"),
    PUSH_LIVE_MIGRATIONS_MONGO_CLIENT_VALIDATIONS_ENABLED(
        "mms.pushLiveMigrations.mongoClient.validations.enabled"),
    LIVE_MIGRATION_HOURS_UNTIL_CUT_OVER_EXPIRATION("mms.liveImport.hoursUntilCutOverExpiration"),
    LIVE_MIGRATION_HOURS_UNTIL_CUT_OVER_EXPIRATION_WARNING(
        "mms.liveImport.hoursUntilCutOverExpirationWarning"),
    PAGERDUTY_APP_ID("pagerduty.app.id"),
    PARSE_HARDWARE_PING_PAYLOAD_ON_INGESTION_ENABLED(
        "mms.kinesis.parseHardwarePingPayload.enabled"),
    PARSE_FTS_PING_PAYLOAD_ON_INGESTION_ENABLED("mms.kinesis.parseFTSPingPayload.enabled"),
    PARSE_PROFILER_ENTRY_ON_INGESTION_ENABLED("mms.monitoring.parseProfilerPayload.enabled"),
    ACME_HTTP_TIMEOUT_SECONDS("mms.acme.http.timeout.seconds"),
    NDS_PLAN_EXECUTOR_JOB_PRIORITY_ENABLED("nds.planExecutorJobPriority.enabled"),
    SERVERLESS_PROXIES_STOPPED_REPORTING_MIN_TENANTS(
        "mms.alerts.ServerlessProxiesStoppedReportingForMTM.minTenants"),
    SERVERLESS_PROXIES_STOPPED_REPORTING_MAX_TENANT_SAMPLE(
        "mms.alerts.ServerlessProxiesStoppedReportingForMTM.maxTenantSample"),
    SERVERLESS_PROXY_DOWN_MAX_PING_AGE_MINS("mms.alerts.ServerlessProxyDown.maximumPingAgeMinutes"),
    FLEX_PROXIES_STOPPED_REPORTING_MIN_TENANTS(
        "mms.alerts.FlexProxiesStoppedReportingForMTM.minTenants"),
    FLEX_PROXIES_STOPPED_REPORTING_MAX_TENANT_SAMPLE(
        "mms.alerts.FlexProxiesStoppedReportingForMTM.maxTenantSample"),
    FLEX_PROXY_DOWN_MAX_PING_AGE_MINS("mms.alerts.FlexProxyDown.maximumPingAgeMinutes"),
    PROFILING_LEVEL_FILTER_EXPRESSION_ROLLOUT_PERCENTAGE(
        "mms.monitoring.managedslowms.setprofilinglevel.filterExpression.rollout.percentage"),
    NDS_BACKUP_SNAPSHOTS_AWS_KMS_KEY_ARN_TEMPLATE(
        "nds.backup.snapshots.aws.copySnapshotKmsKeyArnTemplate"),
    NDS_BACKUP_SNAPSHOTS_AWS_KMS_GOV_KEY_ARN_TEMPLATE(
        "nds.backup.snapshots.aws.copySnapshotKmsGovKeyArnTemplate"),
    NDS_BACKUP_FASTER_RESTORE_KMS_KEY_ARN_TEMPLATE(
        "nds.backup.snapshots.aws.fasterRestoreJobKmsKeyArnTemplate"),
    NDS_BACKUP_FASTER_RESTORE_KMS_GOV_KEY_ARN_TEMPLATE(
        "nds.backup.snapshots.aws.fasterRestoreJobKmsGovKeyArnTemplate"),
    NDS_BACKUP_STANDARD_DSV5_ROLLOUT_PERCENTAGE("nds.backup.standardDSV5RolloutPercentage"),
    NDS_PLANNER_GROUP_INTERVAL_LAST_FAILED_PROP("nds.planner.group.interval.lastFailed"),
    NDS_PLANNER_GROUP_INTERVAL_SHORT_PROP("nds.planner.group.interval.short"),
    NDS_PLANNER_GROUP_INTERVAL_MEDIUM_PROP("nds.planner.group.interval.medium"),
    NDS_PLANNER_GROUP_INTERVAL_LONG_PROP("nds.planner.group.interval.long"),
    NDS_PLANNER_GROUP_INTERVAL_WHILE_UNDER_MAINTENANCE_PROP(
        "nds.planner.group.interval.whileUnderMaintenance"),
    NDS_PLANNER_GROUP_LOGGER_ENHANCEMENT("nds.planner.group.logger.enhancement"),
    BEAMER_API_KEY("mms.beamerApiKey"),
    ATLAS_SEARCH_PLANNER_ENABLED("fts.decoupled.planner.enabled"),
    ATLAS_STREAMS_ALERTING_ENABLED("streams.alerts.enabled"),
    ATLAS_STREAMS_PLANNER_ENABLED("streams.planner.enabled"),
    ATLAS_STREAMS_VPC_PEERING_SCANNER_PLANNER_ENABLED("streams.vpcpeering.scanner.planner.enabled"),
    ATLAS_STREAMS_VPC_PEERING_SCANNER_PLANNER_DELAY_SECONDS(
        "streams.vpcpeering.scanner.planner.delaySeconds"),
    ATLAS_STREAMS_PRIVATE_LINK_PLANNER_ENABLED("streams.privatelink.planner.enabled"),
    ATLAS_STREAMS_VPC_PEERING_PROXY_HOSTED_ZONE_ID("streams.vpcpeering.proxy.hostedZoneId"),
    ATLAS_STREAMS_VPC_PEERING_ENABLE_LOCAL("streams.vpcpeering.proxy.enable.local"),
    ATLAS_STREAMS_PROXY_INSTANCE_STATUS_UPDATE_ALLOWED_LAG_MINUTES(
        "streams.proxy.instance.statusUpdateAllowedLagMinutes"),
    ATLAS_STREAMS_PROXY_INSTANCE_RECENCY_CHECK_THRESHOLD_MINUTES(
        "streams.proxy.instance.recencyCheckThresholdMinutes"),
    ATLAS_STREAMS_VPC_PROXY_INSTANCE_DAO_TTL("streams.proxyInstanceDao.ttlDays"),
    ATLAS_STREAMS_VPC_PROXY_DEPLOYMENT_DAO_TTL("streams.proxyDeploymentDao.ttlDays"),
    NDS_CUSTOM_ROLES_ANY_DB_RESOURCES_ENABLED("nds.custom.roles.any.db.resources.enabled"),
    NDS_AWS_REGION_IN_HEALING_PERCENT("nds.aws.region.inHealingPercent"),
    NDS_AZURE_REGION_IN_HEALING_PERCENT("nds.azure.region.inHealingPercent"),
    NDS_GCP_REGION_IN_HEALING_PERCENT("nds.gcp.region.inHealingPercent"),
    NDS_CONTINUOUS_DELIVERY_OVERRIDE_MONGODB_VERSION(
        "nds.continuousDelivery.overrideMongoDBVersion"),
    NDS_SERVERLESS_MTM_CONSUMPTION_DEBUG_LOGGING_ENABLED(
        "nds.serverless.mtm.consumption.debug.logging.enabled"),
    NDS_SERVERLESS_MTM_LIMITS_MAX_RESIDENT_TENANTS("nds.serverless.mtm.limits.maxResidentTenants"),
    NDS_MTM_COMPACTION_ENABLED("nds.mtmCompaction.enabled"),
    NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_TENANTS_PER_POOL(
        "nds.serverless.pool.limits.maxResidentTenants"),
    NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_MTMS_PER_POOL(
        "nds.serverless.pool.limits.maxResidentMTMs"),
    NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_MTMS_FOR_AUTOSCALE_MTM_CAPACITY(
        "nds.serverless.pool.limits.maxResidentMTMsForAutoScaleMTMCapacity"),
    NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_MTMS_PER_GROUP(
        "nds.serverless.group.limits.maxResidentMTMs"),
    NDS_SERVERLESS_POOL_LIMITS_MAX_RESIDENT_TENANTS_THRESHOLD(
        "nds.serverless.pool.limits.maxResidentTenantsThreshold"),
    NDS_SERVERLESS_MTM_BACKING_INSTANCE_SIZE("nds.serverless.mtm.backingInstanceSize"),
    NDS_MTM_COMPACTION_PAUSE_ON_TENANT_FAILURE("nds.mtmCompaction.pauseOnTenantFailure"),
    NDS_MTM_COMPACTION_MAX_TENANT_CONCURRENT("nds.mtmCompaction.maxTenantConcurrent"),
    NDS_MTM_COMPACTION_ALLOWED_MONGODB_VERSIONS("nds.mtmCompaction.allowedMongoDBVersions"),
    NDS_MTM_COMPACTION_MAX_MTM_PER_REGION_CONCURRENT("nds.mtmCompaction.maxMTMPerRegionConcurrent"),

    NDS_ATLASPROXY_RESTART_PROXY_PROCESS_ON_FALLBACK_CERT_ROTATION(
        "nds.atlasproxy.restartProxyProcessOnFallbackCertRotation"),
    MMS_HTTP_MAX_REQUEST_HEADER_SIZE_IN_BYTES("mms.http.maxRequestHeaderSizeInBytes"),
    MMS_REAL_TIME_AGENT_CENTRAL_URL("mms.automation.realtime.centralUrl"),
    MMS_AUTHZ_SERVICE_CLIENT_REQUEST_THREADS("mms.authz-service.client.requestThreads"),
    MMS_AUTHZ_SERVICE_FUTURE_REQUEST_THREADS("mms.authz-service.futures.requestThreads"),
    MMS_AUTHZ_TAGS_TIMEOUT_MS("mms.authz.tags.timeoutMillis"),
    MMS_JETTY_ENABLE_GZIP_HANDLER("mms.jetty.enableGzipHandler"),
    MMS_JETTY_BUFFER_POOL_OVERRIDE_ENABLED("mms.jetty.bufferPool.overrideEnabled"),
    MMS_JETTY_BUFFER_POOL_LEAK_DETECTION_ENABLED("mms.jetty.bufferPool.leakDetectionEnabled"),
    MMS_JETTY_BUFFER_POOL_HEAP_SIZE_MB("mms.jetty.bufferPool.heapSizeMB"),
    MMS_JETTY_BUFFER_POOL_DIRECT_SIZE_MB("mms.jetty.bufferPool.directSizeMB"),
    MMS_APPSETTINGS_FETCH_SECRETS_IN_BATCH("mms.appsettings.fetchSecretsInBatch"),
    MMS_HTTP_BINDHOSTNAME("mms.http.bindhostname"),
    NDS_OKTA_TEST_OIDC_AUTHORIZATION_SERVER_APIKEY("nds.okta.oidc.testAuthorizationServer.apiKey"),
    NDS_GCP_DAILY_BILLING_AUDIT_DAYS("nds.gcp.dailyBilling.auditDays"),
    BRS_REGIONAL_OPLOG_ENSURE_INDEX_ENABLED("brs.regionalOplogEnsureIndex.enabled"),
    SYSTEM_INITIATED_SNAPSHOT_ENABLED("brs.snapshot.systemInitiated.enabled"),
    NDS_INSTANCES_OS_AWS("nds.instances.os.aws"),
    NDS_INSTANCES_OS_AZURE("nds.instances.os.azure"),
    NDS_INSTANCES_OS_GCP("nds.instances.os.gcp"),
    NDS_SERVERLESS_PROXY_OS_AWS("nds.serverlessProxy.os.aws"),
    NDS_SERVERLESS_PROXY_OS_AZURE("nds.serverlessProxy.os.azure"),
    NDS_SERVERLESS_PROXY_OS_GCP("nds.serverlessProxy.os.gcp"),
    NDS_STREAMS_PROXY_OS_AWS("nds.streamsProxy.os.aws"),
    NDS_STREAMS_PROXY_OS_AZURE("nds.streamsProxy.os.azure"),
    INVOICE_API_RATE_LIMIT_PER_MINUTE("mms.invoiceApi.rateLimitPerMinute"),
    INVOICE_API_RATE_LIMIT_PER_MINUTE_GET_ALL_INVOICES(
        "mms.invoiceApi.rateLimitPerMinute.getAllInvoices"),
    LINE_ITEMS_API_RATE_LIMIT_PER_MINUTE("mms.lineItemsApi.rateLimitPerMinute"),
    DATA_VALIDATION_DAILY_SAMPLE_PERCENT(
        "nds.corruptionDetection.dataValidation.dailySamplePercent"),
    DATA_VALIDATION_MIN_DAYS_BETWEEN_CLUSTER_VALIDATIONS(
        "nds.corruptionDetection.dataValidation.minDaysBetweenClusterValidations"),
    DATA_VALIDATION_MIN_CLUSTER_AGE_DAYS(
        "nds.corruptionDetection.dataValidation.minClusterAgeDays"),
    CORRUPTION_DETECTION_TARGET_CLUSTER_VERSION_REGEX(
        "nds.corruptionDetection.targetClusterVersionRegex"),
    DB_CHECK_DAILY_SAMPLE_PERCENT("nds.corruptionDetection.dbCheck.dailySamplePercent"),
    DB_CHECK_MIN_DAYS_BETWEEN_CLUSTER_VALIDATIONS(
        "nds.corruptionDetection.dbCheck.minDaysBetweenClusterValidations"),
    DB_CHECK_MIN_CLUSTER_AGE_DAYS("nds.corruptionDetection.dbCheck.minClusterAgeDays"),
    CHECK_METADATA_CONSISTENCY_DAILY_SAMPLE_PERCENT(
        "nds.corruptionDetection.checkMetadataConsistency.dailySamplePercent"),
    CHECK_METADATA_CONSISTENCY_MIN_DAYS_BETWEEN_CLUSTER_VALIDATIONS(
        "nds.corruptionDetection.checkMetadataConsistency.minDaysBetweenClusterValidations"),
    CHECK_METADATA_CONSISTENCY_MIN_CLUSTER_AGE_DAYS(
        "nds.corruptionDetection.checkMetadataConsistency.minClusterAgeDays"),
    NDS_FLEET_ATTRIBUTE_COLLECTION("nds.fleetAttributeCollection"),

    ENFORCE_IP_RATE_LIMIT_FOR_UNAUTHED_ENDPOINTS("mms.enforceIpRateLimitForUnauthedEndpoints"),
    JETTY_SSL_SNI_REQUIRED("jetty.ssl.sniRequired"),
    JETTY_SSL_SNI_HOST_CHECK("jetty.ssl.sniHostCheck"),
    JETTY_SSL_STS_MAX_AGE_SECONDS("jetty.ssl.stsMaxAgeSeconds"),
    JETTY_SSL_STS_INCLUDE_SUBDOMAINS("jetty.ssl.stsIncludeSubdomains"),

    // ContentStack CMS
    CONTENTSTACK_API_KEY("mms.contentStackApiKey"),
    CONTENTSTACK_DELIVERY_TOKEN("mms.contentStackDeliveryToken"),

    CONTENTSTACK_BRANCH("mms.contentStackBranch"),

    CONTENTSTACK_ENABLED("mms.contentStackEnabled"),

    OKTA_WEBHOOK_SECRET_KEY("okta.webhooks.secretKey"),
    SERVICE_ACCOUNT_OAUTH_ENABLED("authn.oauth.serviceAccounts.enabled"),
    SERVICE_ACCOUNT_OAUTH_ALERTS_ENABLED("authn.oauth.serviceAccounts.alertsEnabled"),

    UNIFORM_FRONTEND_XDS_SERVER_ENABLED("mms.uniformFrontend.xDSServer.enabled"),
    UNIFORM_FRONTEND_XDS_BIND_ADDRESS("mms.uniformFrontend.xDSServer.bindAddress"),

    CLUSTER_CHANGE_SVC_PROCESS_CHANGES_BEFORE("brs.daemon.clusterChange.processChangesBeforeMins"),
    CLUSTER_CHANGE_SVC_CRON_JOB_FREQUENCY("brs.daemon.clusterChange.cronJobFrequencyMins"),
    MMS_GRPC_SERVER_ENABLED("mms.mmsGrpcServer.enabled"),
    MMS_GRPC_SERVER_BIND_ADDRESS("mms.mmsGrpcServer.bindAddress"),
    SFDC_USER_SYNC_PARTITION_SIZE("mms.billing.sfdc.user.sync.partition.size"),
    MMS_AUTOMATION_SENTRY_DSN("mms.automation.sentry.dsn"),
    MMS_AUTOMATION_SENTRY_SAMPLE_RATE("mms.automation.sentry.sampleRate"),
    MMS_ATLAS_DEFAULT_REPLICA_SET_SCALING_STRATEGY("mms.atlas.defaultReplicaSetScalingStrategy"),
    MMS_ATLAS_ENCRYPTION_AT_REST_PRIVATE_ENDPOINT_STATUS_SYNC_INTERVAL(
        "mms.atlas.encryptionAtRestPrivateEndpointStatusSyncInterval"),
    IAM_BLOCKED_USER_AGENTS("mms.iam.blockedUserAgents"),
    APP_SERVICES_GUIDE_CUE_ENABLED("mms.appServicesGuideCue.enabled"),
    MMS_ATLAS_MAX_HOST_COUNT_FOR_MONITORING("mms.atlas.maxHostCountForMonitoring"),
    MMS_ATLAS_ACCESS_TRANSPARENCY_X509_CERTS_ENABLED("mms.atlasAccessTransparencyX509CertsEnabled"),

    MMS_INVITE_SALES_SOLD_RATE_LIMIT_MAX_ALLOWED("mms.invite.salesSold.ratelimit.maxAllowed"),
    MMS_INVITE_RATE_LIMIT_MAX_ALLOWED("mms.invite.ratelimit.maxAllowed"),
    MMS_INVITE_RATE_LIMIT_PERIOD_MINUTES("mms.invite.ratelimit.periodMinutes"),

    NDS_ELEVATED_HEALTH_MONITORING_EXTERNAL_MAINTENANCE_ENABLED(
        "nds.elevatedHealthMonitoring.externalMaintenance.enabled"),
    NDS_MONGODB_EOL_VERSION_UPGRADE_MONITORING_PERIOD_HOURS(
        "nds.mongodb.eolVersionUpgrade.monitoringPeriodHours"),
    THIRD_PARTY_EXTRA_LOGGING_ENABLED("brs.thirdparty.extraLoggingEnabled"),
    ATLAS_RESOURCE_POLICY_TRACE_LOGGING_PER_SECOND(
        "mms.atlas.resourcePolicy.traceLoggingPerSecond"),
    AB_TESTING_GRADUAL_ROLLOUT_ENABLED("mms.abTesting.gradualRollout.enabled"),
    GCP_PROVISIONED_IOPS_ENABLED("nds.gcp.provisionedIOPS.enabled"),

    MMS_NEW_DB_USER_SCRAM_SHA1_ITERATION_COUNT("mms.new.db.user.scramIterationCount"),
    MMS_NEW_DB_USER_SCRAM_SHA256_ITERATION_COUNT("mms.new.db.user.scramSHA256IterationCount"),
    ACCESS_LEVEL_SSH_REQUESTS_PERCENTAGE_ROLLOUT("mms.accessLevel.sshRequests.percentageRollout"),
    AZURE_API_HTTP_LOG_DETAIL_LEVEL("mms.azure.api.httpLogDetailLevel"),
    VOYAGE_CONTROL_PLANE_URL("voyage.controlPlane.url"),
    NDS_ALWAYS_UPDATE_VERSIONS("nds.alwaysUpdateVersions");

    public final String value;

    Fields(final String _value) {
      value = _value;
    }
  }

  @Override
  protected synchronized void addPropertyDirect(final String key, final Object value) {
    _memoryProperties.put(key, value != null ? value.toString() : null);
    _settingsVersion++;
  }

  @Override
  protected synchronized void clearInternal() {
    if (!this.getAppEnv().equals(AppEnv.TEST)) {
      throw new IllegalStateException("Clear is only supported for unit testing.");
    }

    _defaultFileProperties.clear();
    _overrideFileProperties.clear();
    _memoryProperties.clear();
    _settingsVersion++;
  }

  @Override
  protected synchronized void clearPropertyDirect(final String key) {
    _memoryProperties.remove(key);
    _settingsVersion++;
  }

  @Override
  protected Iterator<String> getKeysInternal() {
    return getKeysStream().map(Object::toString).collect(toSet()).iterator();
  }

  @Override
  protected Object getPropertyInternal(final String key) {
    return getProp(key, SettingType.EFFECTIVE);
  }

  @Override
  protected boolean isEmptyInternal() {
    return getKeysStream().findAny().isEmpty();
  }

  @Override
  protected boolean containsKeyInternal(final String key) {
    return getKeysStream().anyMatch(k -> k.equals(key));
  }

  private Stream<?> getKeysStream() {
    return Stream.of(
            _memoryProperties.keySet(),
            System.getProperties().keySet(),
            _overrideFileProperties.keySet(),
            ofNullable(_cachedDbProperties.get()).map(Map::keySet).orElseGet(Set::of),
            _defaultFileProperties.keySet())
        .flatMap(Collection::stream)
        .filter(Objects::nonNull);
  }

  private ConversionHandler createConversionHandler() {
    final DefaultListDelimiterHandler listDelimiterHandler = new DefaultListDelimiterHandler(',');
    return new DefaultConversionHandler() {
      @Override
      protected Collection<?> extractValues(Object source, int limit) {
        return source instanceof String
            ? listDelimiterHandler.split((String) source, true)
            : super.extractValues(source, limit);
      }

      @Override
      @SuppressWarnings("unchecked")
      protected <T> T convertValue(
          final Object src, final Class<T> targetCls, final ConfigurationInterpolator ci) {
        return targetCls == ObjectId.class
            ? (T) new ObjectId((String) src)
            : super.convertValue(src, targetCls, ci);
      }
    };
  }

  @com.mongodb.internal.VisibleForTesting(otherwise = AccessModifier.PRIVATE)
  String getStringPropertyFromConfigService(
      String propertyName, ConfigServiceRequestContext context) {
    String configServiceValue = null;
    try {
      configServiceValue = configServiceSdkWrapper.getString(MMS_NAMESPACE, propertyName, context);
    } catch (Exception ex) {
      LOG.error("Error getting property={} from config service", propertyName, ex);
    }
    return configServiceValue;
  }

  private Set<String> extractNamesOfSecretsFromPropertiesObject(Properties properties) {
    return properties.entrySet().stream()
        .filter(entry -> SECRET_MANAGER_STRING.equals(entry.getValue()))
        .map(entry -> (String) entry.getKey())
        .collect(toSet());
  }

  private Optional<Secret> getSecretFromConfigService(String propertyName) {
    Optional<Secret> configServiceValue = Optional.empty();
    try {
      configServiceValue = configServiceSdkWrapper.getOptionalSecret(propertyName);
    } catch (Exception ex) {
      LOG.error("Error getting secret={} from config service", propertyName);
    }
    return configServiceValue;
  }

  private String getConfigServiceFeatureFlagPhase(EnvState envState) {
    return switch (envState) {
      case ENABLED -> "PHASE_ENABLED";
      case DISABLED -> "PHASE_DISABLED";
      case CONTROLLED -> "PHASE_CONTROLLED";
      default -> "PHASE_UNSPECIFIED";
    };
  }

  /**
   * Properties are sometimes deliberately set to {@code null} to hide the value at database, file,
   * or system level.
   *
   * <p>At the time of commit this functionality was only used in integration tests. To preserve
   * backwards compatibility with AppSettings if a value is purposefully set to "null" the legacy
   * flow should be respected and the value should not be retrieved from the Config Service.
   */
  private boolean isPropertySetToNullAtTheMemoryLevelForIntTests(String pName) {
    return _memoryProperties.containsKey(pName) && _memoryProperties.get(pName) == null;
  }

  /**
   * Determines if Config Service flow should be used instead of legacy AppSettings to retrieve a
   * given property.
   */
  boolean shouldUseConfigServiceToRetrieveProperty(
      final String pName, ResolvedProperty pResolvedProperty) {
    /*
     A certain subset of properties cannot use the Config Service. If we are
     trying to retrieve one of those properties we return false to indicate that
     property should fall back to the legacy functionality.
    */
    if (NON_CONFIG_SERVICE_APP_PROPS.contains(pName)) {
      return false;
    }

    if (this.isPropertySetToNullAtTheMemoryLevelForIntTests(pName)) {
      return false;
    }

    /*
     * Use the Config Service as the source of truth for Application Properties.
     * If the value is null, then getProp should retrieve the value from Config Service.
     * If the value is not null, then this property was defined:
     *   at the environment override level
     *   OR defined at the system override level
     *   OR defined at the memory override level
     *   OR a property that is not managed by the config service
     *   See `AppSettings.getPropWithoutConfigService`
     */
    return pResolvedProperty.getValue() == null;
  }

  public static class ResolvedProperty {

    private final String value;
    private final Source source;

    public enum Source {
      DATABASE,
      ENVIRONMENT,
      ENVIRONMENT_OVERRIDE,
      MEMORY,
      PROPERTY_FILE_DEFAULT,
      PROPERTY_FILE_OVERRIDE,
      SYSTEM
    }

    public ResolvedProperty(String value, Source source) {
      this.value = value;
      this.source = source;
    }

    public String getValue() {
      return this.value;
    }

    public Source getSource() {
      return this.source;
    }
  }
}
