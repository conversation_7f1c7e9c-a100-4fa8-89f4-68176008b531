package com.xgen.cloud.common.featureFlag._public.model;

import com.mongodb.internal.VisibleForTesting;
import com.mongodb.internal.VisibleForTesting.AccessModifier;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanType;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import dev.morphia.converters.SimpleValueConverter;
import dev.morphia.converters.TypeConverter;
import dev.morphia.mapping.MappedField;
import io.prometheus.client.Counter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.lang3.ArrayUtils;

/**
 * Enumeration of features under active development. There are 2 Feature Flag systems that use this
 * file in MMS: "legacy" flags and Config Service Feature Flags. Please see this wiki for more
 * details on the differences, and for more info on Config Service Feature Flags:
 * https://wiki.corp.mongodb.com/display/MMS/Which+Feature+Flag+System+To+Use+in+MMS
 *
 * <p>Please see the NON_CONFIG_SERVICE_FEATURE_FLAGS list in this file for the flags using the
 * "legacy" system. Every flag not in this list can be assumed to be using Config Service Feature
 * Flags.
 *
 * <p>Each "legacy" FeatureFlag is associated with an app setting, which is managed in our
 * conf-*.properties file. Config Service Feature Flags use definition files.
 *
 * <p>A Feature Flag (in either system) may have one of the following 3 values:
 *
 * <p>disabled : This feature is completely disabled / invisible in this environment
 *
 * <p>controlled : If a "legacy" flag, this feature is toggleable through either the Org or Group
 * settings page for those with Administrative privileges. To have a feature flag on/off switch
 * placed in the admin beta features page, controlled should be chosen. If a Config Service Flag,
 * this allows evaluation of allowlist, blocklist, and gradual rollout features.
 *
 * <p>enabled: This feature is fully enabled for all orgs and projects.
 *
 * <p>For BOTH systems, if adding a new Feature for use in MMS, add a value to the FeatureFlag enum
 * with the opts() method for an Options object. In order for the Feature Flag to be consumed by the
 * code at all, you must call both .appSetting and .scope on the options object.
 *
 * <p>In the "legacy" system, you can "turn on" a new feature for specific groups by adding the
 * desired enum value to the Group or Orgs featureFlag array. You can only do this with Feature
 * Flags whose AppSetting is set to controlled. If the AppSetting is set to enabled, the feature
 * will be enabled for all groups within that environment, and if it is disabled it is disabled for
 * the environment and adding the Flag to the Group or Org will not override this.
 *
 * <p>To manually enable a "legacy" flag for groupId XXX:
 *
 * <pre>
 *   # connect to the MongoDB instance where the mmsdbconfig db resides
 *   > db.getSiblingDB("mmsdbconfig").config.customers.update( { _id: ObjectId("XXX") }, { $push: { featureFlags:
 *   {flag: "COOL_NEW_FEATURE", enabled: true} } } );
 * </pre>
 *
 * <p>To manually enable a "legacy" flag for orgId XXX:
 *
 * <pre>
 *   # connect to the MongoDB instance where the mmsdbconfig db resides
 *   > db.getSiblingDB("mmsdbconfig").config.orgs.update( { _id: ObjectId("XXX") },{ $push: { featureFlags:
 *   {flag: "COOL_NEW_FEATURE", enabled: true} } } );
 * </pre>
 *
 * <p>To check if a feature flag is enabled use FeatureFlagSvc::isEnabled which will see if the flag
 * is enabled for a group or organization depending on what type of flag it is
 *
 * <p>You can also use the @Feature annotation on a Resource path to also limit access to paths with
 * a groupId. Eg.
 *
 * <pre>{@code
 * @Path("/my/path/to/a/resource")
 * @Feature(FeatureFlag.COOL_NEW_FEATURE)
 * public Response getCoolNewFeature() {...}
 * }</pre>
 *
 * <p>As a best practice, you should clean up feature flag checks once a feature is fully rolled out
 * and the flag is no longer needed. For a "legacy" feature flag, this should include:
 *
 * <ul>
 *   <li>Remove all feature flag checks in the Java and UI code.
 *   <li>Remove the flag from the "featureFlags" array of any Group or Org documents that have it.
 *   <li>Remove the flag from the enum (optional).
 * </ul>
 *
 * <p>For a Config Service feature flag, please see this page:
 * https://wiki.corp.mongodb.com/display/MMS/Config+Service+Feature+Flags+Best+Practices+in+MMS
 */
@SuppressWarnings("SwaggerSchemaUsageChecker")
@Schema(implementation = String.class)
public enum FeatureFlag {
  DISABLE_SCRAM_SHA1_AUTH(
      opts().appSetting("mms.featureFlag.disableScramSha1Auth").scope(Scope.GROUP)),
  ENABLE_SCRAM_SHA256_AUTH(
      opts().appSetting("mms.featureFlag.enableScramSha256Auth").scope(Scope.GROUP)),
  // This MOCK_CONTROLLED_FEATURE_FLAG is for e2e testing for interactions between MMS and the
  // Config Service
  MOCK_CONTROLLED_FEATURE_FLAG(opts().appSetting("mockControlledFeatureFlag").scope(Scope.GROUP)),
  AWS_PRIVATELINK_EXPORTS(
      opts().appSetting("mms.featureFlag.backup.awsPrivateLinkExports").scope(Scope.GROUP)),
  ALERT_HOST_SSH_SESSION_STARTED(
      opts().appSetting("mms.featureFlag.alertHostSSHSessionStarted").scope(Scope.ORGANIZATION)),
  ATLAS_ADVANCED_REGIONALIZED_PRIVATE_ENDPOINTS(
      opts()
          .appSetting("mms.featureFlag.atlasAdvancedRegionalizedPrivateEndpoints")
          .scope(Scope.GROUP)),
  ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI(
      opts()
          .appSetting("mms.featureFlag.atlasResourcePoliciesExtensionWithUi")
          .scope(Scope.ORGANIZATION)),
  STREAMS_ENABLED(opts().appSetting("mms.featureFlag.streamsEnabled").scope(Scope.GROUP)),

  STREAMS_AUDIT_LOGS(opts().appSetting("mms.featureFlag.streamsAuditLogs").scope(Scope.GROUP)),
  STREAMS_ENABLE_WORKSPACES(
      opts().appSetting("mms.featureFlag.streamsEnableWorkspaces").scope(Scope.GROUP)),
  STREAMS_ENABLE_KAFKA_OIDC(
      opts().appSetting("mms.featureFlag.streamsEnableKafkaOIDC").scope(Scope.GROUP)),
  ADFA_ALLOW_ROUTING_VIA_CRM_INSTEAD_OF_ENVOY(
      opts()
          .appSetting("mms.featureFlag.adfa.allowRoutingViaCRMInsteadOfEnvoy")
          .scope(Scope.GROUP)),

  ADL_READ_CONCERN_MAJORITY(
      opts().appSetting("mms.featureFlag.adl.readConcernMajority").scope(Scope.GROUP)),

  ADL_SCHEMA_UNIQUE_FIELD_LIMIT(
      opts().appSetting("mms.featureFlag.adl.schemaUniqueFieldLimit").scope(Scope.GROUP)),

  ADL_TEMPLATE_REGEX_GENERATION_OPTIMIZATION(
      opts()
          .appSetting("mms.featureFlag.adl.templateRegexGenerationOptimization")
          .scope(Scope.ORGANIZATION)),

  ADL_TEST_MANUAL_CONTROLLED_FLAG(
      opts().appSetting("mms.featureFlag.adl.testManualControlledFlag").scope(Scope.GROUP)),

  ADL_TEST_AUTOMATED_CONTROLLED_FLAG(
      opts().appSetting("mms.featureFlag.adl.testAutomatedControlledFlag").scope(Scope.GROUP)),

  ADL_TEST_ENABLED_FLAG(
      opts().appSetting("mms.featureFlag.adl.testEnabledFlag").scope(Scope.GROUP)),

  ADL_TEST_DISABLED_FLAG(
      opts().appSetting("mms.featureFlag.adl.testDisabledFlag").scope(Scope.GROUP)),

  ADL_USE_MONGOD_INSTEAD_OF_MQLRUN(
      opts().appSetting("mms.featureFlag.adl.useMongodInsteadOfMqlrun").scope(Scope.GROUP)),

  ATLAS_ALLOW_DEPRECATED_VERSIONS(
      opts().appSetting("mms.featureFlag.atlasAllowDeprecatedVersions").scope(Scope.GROUP)),
  ALLOW_AUTOMATED_OPERATOR_ACTIONS(
      opts().appSetting("mms.featureFlag.allowAutomatedOperatorActions").scope(Scope.GROUP)),
  ATLAS_CONFIGURABLE_GP3_IOPS(
      opts().appSetting("mms.featureFlag.atlasConfigurableGp3Iops").scope(Scope.GROUP)),

  ATLAS_CHAIN_PAUSE_MOVES(
      opts().appSetting("mms.featureFlag.atlasChainPauseMoves").scope(Scope.GROUP)),

  ATLAS_SEARCH_RESOLVE_VIEWS(
      opts().appSetting("mms.featureFlag.atlasSearchResolveViews").scope(Scope.GROUP)),

  ATLAS_X509_CRL(opts().appSetting("mms.featureFlag.atlasX509CRL").scope(Scope.GROUP)),
  ATLAS_X509_MIXED_CERTS_BY_GROUP(
      opts().appSetting("mms.featureFlag.mixedCertsClustersByGroup").scope(Scope.GROUP)),
  ATLAS_X509_MIXED_CERTS_BY_ORG(
      opts().appSetting("mms.featureFlag.mixedCertsClustersByOrg").scope(Scope.ORGANIZATION)),
  ATLAS_AZURE_SSD_PV2(opts().appSetting("mms.featureFlag.atlasAzureSsdPV2").scope(Scope.GROUP)),

  ATLAS_AZURE_SSD_PV2_WAVE2(
      opts().appSetting("mms.featureFlag.atlasAzureSsdPV2Wave2").scope(Scope.GROUP)),

  ATLAS_AZURE_SSD_PV2_ENABLE_PREVIEW_REGIONS(
      opts().appSetting("mms.featureFlag.atlasAzureSsdPV2EnablePreviewRegions").scope(Scope.GROUP)),

  ATLAS_AZURE_SSD_FORCE_PV1(
      opts().appSetting("mms.featureFlag.atlasAzureSsdForcePV1").scope(Scope.GROUP)),
  ATLAS_MOUNT_AZURE_DISK_WITH_LUN(
      opts().appSetting("mms.featureFlag.atlasMountAzureDiskWithLun").scope(Scope.GROUP)),

  ATLAS_AZURE_FORCE_MIGRATION_TO_AZS(
      opts().appSetting("mms.featureFlag.atlasAzureForceMigrationToAZs").scope(Scope.GROUP)),
  ATLAS_SERVERLESS_GRPC_SUPPORT(
      opts().appSetting("mms.featureFlag.atlasServerlessGRPCSupport").scope(Scope.GROUP)),
  ATLAS_FLEX_DISABLE_DYNAMIC_RATE_LIMITER(
      opts().appSetting("mms.featureFlag.atlasFlexDisableDynamicRateLimiter").scope(Scope.GROUP)),

  ATLAS_PROXY_USE_SERVERLESS_PERF_TEST_SETTINGS(
      opts()
          .appSetting("mms.featureFlag.atlasProxyUseServerlessPerfTestSettings")
          .scope(Scope.GROUP)),

  ATLAS_PROXY_USE_SERVERLESS_PERF_TEST_SETTINGS_ORG(
      opts()
          .appSetting("mms.featureFlag.atlasProxyUseServerlessPerfTestSettingsOrg")
          .scope(Scope.ORGANIZATION)),

  ATLAS_PROXY_DISABLE_RATE_LIMITING(
      opts().appSetting("mms.featureFlag.atlasProxyDisableRateLimiting").scope(Scope.ORGANIZATION)),

  ATLAS_PROXY_USS_MODE(opts().appSetting("mms.featureFlag.atlasProxyUSSMode").scope(Scope.GROUP)),
  ATLAS_PROXY_FLEX_ROLLOUT_SERVERLESS_METRICS_MODE(
      opts()
          .appSetting("mms.featureFlag.atlasProxyFlexRolloutServerlessMetricsMode")
          .scope(Scope.GROUP)),
  ATLAS_ENABLE_TEST_COMMANDS(
      opts().appSetting("mms.featureFlag.atlasEnableTestCommands").scope(Scope.GROUP)),
  ATLAS_PROVIDE_HARDCODED_OIDC_IDP_INFORMATION(
      opts()
          .appSetting("mms.featureFlag.atlasProvideHardcodedOidcIdpInformation")
          .scope(Scope.GROUP)),
  ATLAS_DAILY_BIG_QUERY_BILLING(
      opts().appSetting("mms.featureFlag.atlasDailyBigQueryBilling").scope(Scope.GROUP)),
  ATLAS_DATA_REGIONALIZATION_ENABLED(
      opts().appSetting("mms.featureFlag.atlasDataRegionalization").scope(Scope.ORGANIZATION)),
  // This is a group level feature flag that checks if an (MTM) group has regionalization enabled.
  // If either the org level FF or the group level FF is enabled, then regionalization will be
  // enabled.
  ATLAS_DATA_REGIONALIZATION_ENABLED_GROUP(
      opts().appSetting("mms.featureFlag.atlasDataRegionalizationGroup").scope(Scope.GROUP)),
  ATLAS_DEFER_SERVER_NODE_TYPE_TAG_UNTIL_PHASE_2(
      opts()
          .appSetting("mms.featureFlag.atlasDeferServerNodeTypeTagUntilPhase2")
          .scope(Scope.GROUP)),
  ATLAS_DEPLOYS_UIS(opts().appSetting("mms.featureFlag.atlasDeploysUIS").scope(Scope.GROUP)),

  ATLAS_HALT_GROUP_LOG_INGESTION(
      opts().appSetting("mms.featureFlag.atlasHaltGroupLogIngestion").scope(Scope.GROUP)),

  ATLAS_CN_REGIONS_ONLY(
      opts().appSetting("mms.featureFlag.atlasCNRegionsOnly").scope(Scope.ORGANIZATION)),

  ATLAS_USE_DATA_EXPLORER_SERVICE(
      opts()
          .appSetting("mms.featureFlag.atlasUseDataExplorerService")
          .scope(Scope.GROUP)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)),
  // For testing only. After 10/20/2020 all clusters are provisioned using MONGODB subdomains to
  // facilitate multi-cloud. However, clusters created before then still have AZURE or GCP
  // subdomains, and we need to be able to test that scenario.
  ATLAS_USE_PROVIDER_SUBDOMAINS(
      opts().appSetting("mms.featureFlag.atlasUseProviderSubdomains").scope(Scope.GROUP)),

  // For testing only. LEGACY hostnames are deprecated but a number of clusters still use them.
  ATLAS_USE_LEGACY_HOSTNAME_SCHEME(
      opts().appSetting("mms.featureFlag.atlasUseLegacyHostnameScheme").scope(Scope.GROUP)),

  // For testing only. Force e2e tests against regular-provisioned tenants even after we enable
  // Fast Shared Tier.
  ATLAS_FORCE_SKIP_FAST_PROVISION(
      opts().appSetting("mms.featureFlag.atlasForceSkipFastProvision").scope(Scope.GROUP)),

  ATLAS_CHURN_SURVEY(opts().appSetting("mms.featureFlag.atlasChurnSurvey").scope(Scope.GROUP)),

  ATLAS_HIDE_QUICK_ACCESS_PAGE(
      opts()
          .appSetting("mms.featureFlag.atlasHideQuickAccessPage")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  EHM_ALLOW_LIST_ENABLED(
      opts().appSetting("mms.featureFlag.ehm.allowListEnabled").scope(Scope.GROUP)),

  ATLAS_EOL_VERSION_UPGRADE_MAINTENANCE(
      opts().appSetting("mms.featureFlag.eolVersionUpgradeMaintenance").scope(Scope.GROUP)),

  ATLAS_ENFORCE_EOL_SINGLE_VERSION_UPGRADE_FOR_ORG(
      opts()
          .appSetting("mms.featureFlag.enforceEolSingleVersionUpgradeForOrg")
          .scope(Scope.ORGANIZATION)),

  ATLAS_NO_EXTRA_INTERNAL_MAINTENANCES(
      opts().appSetting("mms.featureFlag.noExtraInternalMaintenances").scope(Scope.GROUP)),

  CLUSTER_TAGGING_BILLING_EXPORT(
      opts().appSetting("mms.featureFlag.billingClusterTaggingExport").scope(Scope.ORGANIZATION)),
  BILLING_RESOURCE_TAGGING_BULK_ENDPOINT(
      opts()
          .appSetting("mms.featureFlag.billingResourceTaggingBulkEndpoint")
          .scope(Scope.ORGANIZATION)),

  ATLAS_PUSH_BASED_LOG_EXPORT(
      opts().appSetting("mms.featureFlag.atlasPushBasedLogExport").scope(Scope.GROUP)),

  ATLAS_PUSH_BASED_LOG_EXPORT_CLUSTER_LEVEL(
      opts().appSetting("mms.featureFlag.atlasPushBasedLogExportClusterLevel").scope(Scope.GROUP)),

  SINGLE_TARGET_SERVERLESS_DEPLOYMENT(
      opts().appSetting("mms.featureFlag.singleTargetServerlessDeployment").scope(Scope.GROUP)),

  // PlanType.FREE_TIER is a little weird, because we don't let them use Automation, but we still
  // show all the elements. (But greyed out.)
  AUTOMATION(
      opts()
          .appSetting("mms.featureFlag.automation")
          .enabledByDefaultForPlanTypes(
              PlanType.PREMIUM, PlanType.STANDARD, PlanType.FREE_TIER, PlanType.ONPREM)
          .scope(Scope.GROUP)),

  AUTOMATION_CHANGE_LISTENER(
      opts()
          .appSetting("mms.featureFlag.automation.changeListener")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  AUTOMATION_CONFIG_PUBLISH_CHANGE_EVENT(
      opts()
          .appSetting("mms.featureFlag.automation.configPublishChangeEvent")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  AUTOMATION_CONFIG_FOR_AGENT_RETURNS_ONLY_LOCAL(
      opts()
          .appSetting("mms.featureFlag.automation.configForAgentReturnsOnlyLocal")
          .scope(Scope.GROUP)),

  AUTOMATION_EDITOR_AUTOFILL(
      opts().appSetting("mms.featureFlag.automation.exposeEditorAutofill").scope(Scope.GROUP)),

  AUTOMATION_MONGO_DEVELOPMENT_VERSIONS(
      opts()
          .appSetting("mms.featureFlag.automation.mongoDevelopmentVersions")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  AUTOMATION_SENTRY(opts().appSetting("mms.featureFlag.automation.sentry").scope(Scope.GROUP)),

  AUTOMATION_VERIFY_DOWNLOADS(
      opts().appSetting("mms.featureFlag.automation.verifyDownloads").scope(Scope.GROUP)),

  AUTOMATION_PROXY_CONFIG_CHANGE_EVENT(
      opts()
          .appSetting("mms.featureFlag.automation.proxyConfigChangeEvent")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  AUTOMATION_ENABLE_V6(
      opts()
          .appSetting("mms.featureFlag.automation.enableV6")
          .scope(Scope.GROUP)
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanType.ONPREM)),

  AUTOMATION_ENABLE_V7(opts().appSetting("mms.featureFlag.automation.enableV7").scope(Scope.GROUP)),

  AUTOMATION_ENABLE_V8(opts().appSetting("mms.featureFlag.automation.enableV8").scope(Scope.GROUP)),

  AUTOMATION_ENABLE_V82(
      opts().appSetting("mms.featureFlag.automation.enableV82").scope(Scope.GROUP)),

  AUTOMATION_ENABLE_ATLAS_CDN_FOR_AUTOMATION(
      opts()
          .appSetting("mms.featureFlag.automation.atlasSpecificCdnForAutomation")
          .scope(Scope.GROUP)),

  AUTOMATION_ENABLE_ATLAS_CDN_FOR_BACKUP(
      opts().appSetting("mms.featureFlag.automation.atlasSpecificCdnForBackup").scope(Scope.GROUP)),

  AUTOMATION_ENABLE_ATLAS_CDN_FOR_MONGOD_MONGOSH_TOOLS(
      opts()
          .appSetting("mms.featureFlag.automation.atlasSpecificCdnForMongodbAndMongoshAndTools")
          .scope(Scope.GROUP)),

  ATLAS_AUTO_RETRIEVE_DRAFT_CLUSTER_CONFIG(
      opts()
          .appSetting("mms.featureFlag.atlasAutoRetrieveDraftClusterConfig")
          .scope(Scope.ORGANIZATION)),

  AWS_USAGE_REPORT_CREATION(
      opts()
          .appSetting("mms.featureFlag.payments.partners.aws.usageReportCreation")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.ORGANIZATION)),
  AZURE_USAGE_REPORT_CREATION(
      opts()
          .appSetting("mms.featureFlag.payments.partners.azure.usageReportCreation")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.ORGANIZATION)),
  GCP_USAGE_REPORT_CREATION(
      opts()
          .appSetting("mms.featureFlag.payments.partners.gcp.usageReportCreation")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.ORGANIZATION)),

  AWS_PRIVATE_ENDPOINT_WAITING_FOR_USER(
      opts().appSetting("mms.featureFlag.awsPrivateEndpoint.waitingForUser").scope(Scope.GROUP)),

  AWS_GRAVITON(opts().appSetting("mms.featureFlag.awsGraviton").scope(Scope.GROUP)),

  AWS_INTEL_OVER_GRAVITON(
      opts().appSetting("mms.featureFlag.awsIntelOverGraviton").scope(Scope.GROUP)),

  BAAS(opts().appSetting("mms.featureFlag.baas").scope(Scope.GROUP)),

  BAAS_BILLING_MIGRATION(
      opts().appSetting("mms.featureFlag.baas.billingMigration").scope(Scope.GROUP)),

  BAAS_PRICING_CHANGE(opts().appSetting("mms.featureFlag.baas.PricingChange").scope(Scope.GROUP)),

  DEPRECATE_THIRD_PARTY_SERVICES(
      opts().appSetting("mms.featureFlag.deprecateThirdPartyServices").scope(Scope.GROUP)),

  BACKUP_ENABLE_AWS_PRIVATELINK_OPTION(
      opts()
          .appSetting("mms.featureFlag.backup.enableAWSPrivateLinkOption")
          .scope(Scope.ORGANIZATION)),

  BACKUP_ALLOW_EDIT_OPLOG_WINDOW(
      opts().appSetting("mms.featureFlag.backup.allowEditOplogWindow").scope(Scope.GROUP)),

  BACKUP_INCREMENTAL_WT(
      opts()
          .appSetting("mms.featureFlag.backup.incrementalWtEnabled")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .enabledByDefaultForPlanTypes(PlanType.ONPREM)
          .scope(Scope.GROUP)),

  BACKUP_S3_OPLOG_STORE_IN_OM(
      opts()
          .appSetting("mms.featureFlag.backup.s3OplogStoreInOm")
          .enabledByDefaultForPlanTypes(PlanType.ONPREM)
          .scope(Scope.GROUP)),

  BACKUP_INCREMENTAL_WT_FULL_SNAPSHOT_DAY_OF_WEEK(
      opts()
          .appSetting("mms.featureFlag.backup.incrementalWtFullSnapshotDayOfWeek")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .enabledByDefaultForPlanTypes(PlanType.ONPREM)
          .scope(Scope.GROUP)),

  BACKUP_WT_ENCRYPTION(
      opts()
          .appSetting("mms.featureFlag.backup.wtEncryptionAtRest")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanType.ONPREM)
          .scope(Scope.GROUP)),

  // This flag will allow 1) backup of a node encrypted with a local key file and 2) backup of a
  // cluster with mixed type of encryption. Setting up a restore target that knows how to properly
  // decrypt the backup is a responsibility of the customer. See CLOUDP-80540.
  BACKUP_WT_LOCAL_KEY_FILE_ENCRYPTION(
      opts().appSetting("mms.featureFlag.backup.wtLocalKeyFile").scope(Scope.GROUP)),
  BACKUP_WT_LOCAL_KEY_FILE_ENCRYPTION_FOR_ORG(
      opts().appSetting("mms.featureFlag.backup.wtLocalKeyFile.forOrg").scope(Scope.ORGANIZATION)),

  BACKUP_AUTO_RECOVERY_UNSAFE_APPLY_OPS(
      opts()
          .appSetting("mms.featureFlag.backup.autoRecoveryUnsafeApplyOps")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .enabledByDefaultForPlanTypes(PlanType.ONPREM)
          .scope(Scope.GROUP)),

  BACKUP_WTC_CONCURRENT_GROOMS(
      opts()
          .appSetting("mms.featureFlag.backup.wtConcurrentGrooms")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .enabledByDefaultForPlanTypes(PlanTypeSet.ONPREM)
          .scope(Scope.GROUP)),

  BACKUP_WTC_CONCURRENT_MONGO_BLOCKSTORE_GROOMS(
      opts()
          .appSetting("mms.featureFlag.backup.wtConcurrentMongoBlockstoreGrooms")
          .scope(Scope.GROUP)),

  BACKUP_WTC_NAMESPACE_FILTERING(
      opts()
          .appSetting("mms.featureFlag.backup.wtNamespaceFiltering")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .enabledByDefaultForPlanTypes(PlanTypeSet.ONPREM)
          .scope(Scope.GROUP)),

  BACKUP_PARALLEL_RESTORES(
      opts()
          .appSetting("mms.featureFlag.backup.parallelRestores")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .enabledByDefaultForPlanTypes(PlanType.ONPREM)
          .scope(Scope.GROUP)),
  BACKUP_DIRECT_S3_RESTORE(
      opts()
          .appSetting("mms.featureFlag.backup.directS3Restore")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .enabledByDefaultForPlanTypes(PlanType.ONPREM)
          .scope(Scope.GROUP)),

  BACKUP_CALCULATE_MD5_S3BLOCKSTORE(
      opts().appSetting("mms.featureFlag.backup.s3blockstore.calculateMD5").scope(Scope.GROUP)),

  BACKUP_CALCULATE_MD5_S3OPLOGSTORE(
      opts().appSetting("mms.featureFlag.backup.s3oplogstore.calculateMD5").scope(Scope.GROUP)),

  BACKUP_THIRD_PARTY_MANAGED(
      opts().appSetting("mms.featureFlag.backup.thirdPartyManaged").scope(Scope.GROUP)),

  BACKUP_THIRD_PARTY_WITH_MANAGED_OPLOG(
      opts().appSetting("mms.featureFlag.backup.thirdPartyWithManagedOplog").scope(Scope.GROUP)),

  BACKUP_WTC_BACKPRESSURE(
      opts()
          .appSetting("mms.featureFlag.backup.wtBackpressure")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .enabledByDefaultForPlanTypes(PlanType.ONPREM)
          .scope(Scope.GROUP)),

  BACKUP_MULTIPLE_WORKERS_PER_FILE(
      opts().appSetting("mms.featureFlag.backup.multipleWorkersPerFile").scope(Scope.GROUP)),

  BACKUP_WHITELISTS(opts().appSetting("mms.featureFlag.backup.allowWhitelists").scope(Scope.GROUP)),

  BI_CONNECTOR(
      opts()
          .appSetting("mms.featureFlag.biConnector")
          .enabledByDefaultForPlanTypes(PlanType.PREMIUM, PlanType.ONPREM)
          .scope(Scope.GROUP)),

  CHARTS(opts().appSetting("mms.featureFlag.charts").scope(Scope.GROUP)),

  CLASSIC_API_ACCESS(opts().appSetting("mms.featureFlag.classicApiAccess").scope(Scope.GROUP)),

  // Retirement of CLASSIC and BASIC planType's
  CLASSIC_CHARTS(opts().appSetting("mms.featureFlag.classicCharts").scope(Scope.GROUP)),

  CHARTS_ACTIVATION_OPTIMIZATION(
      opts().appSetting("mms.featureFlag.chartsActivationOptimization").scope(Scope.GROUP)),

  CHEF_CONF_PUSH(
      opts().appSetting("mms.featureFlag.automation.chefConfigChangeEvent").scope(Scope.GROUP)),
  CLIENT_METADATA_COLLECTION(
      opts()
          .appSetting("mms.featureFlag.clientMetadataCollection")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),
  COMMENT_SERVICE(
      opts().appSetting("mms.featureFlag.commentServiceEnabled").scope(Scope.ORGANIZATION)),

  // As of CLOUDP-55082, new AWS clusters cannot be created with continuous backup. This feature
  // flag allows tests to still create clusters with continuous backup enabled as setup
  CONTINUOUS_BACKUP_ALLOWED_FOR_NEW_AWS_CLUSTERS(
      opts()
          .appSetting("mms.featureFlag.backup.continuousBackupAllowedForNewAWSClusters")
          .scope(Scope.GROUP)),

  CPS_EXTENDED_SNAPSHOT_RETRY(
      opts().appSetting("mms.featureFlag.backup.cpsExtendedSnapshotRetry").scope(Scope.GROUP)),
  CPS_RESURRECT_WITH_RETAINED_BACKUPS(
      opts()
          .appSetting("mms.featureFlag.backup.cpsResurrectWithRetainedBackups")
          .scope(Scope.GROUP)),

  CPS_GCP_INCREMENTAL_COPY_SNAPSHOTS(
      opts()
          .appSetting("mms.featureFlag.backup.cpsGcpIncrementalCopySnapshots")
          .scope(Scope.GROUP)),

  CPS_BACKUP_LOCK_MVP(
      opts().appSetting("mms.featureFlag.backup.cpsBackupLockMVP").scope(Scope.GROUP)),

  CPS_BACKUP_COMPLIANCE_POLICY_POST_GA(
      opts()
          .appSetting("mms.featureFlag.backup.cpsBackupCompliancePolicyPostGA")
          .scope(Scope.GROUP)),

  CPS_BACKUP_COMPLIANCE_POLICY_POST_GA_DISABLE_POLICY(
      opts()
          .appSetting("mms.featureFlag.backup.cpsBackupCompliancePolicyPostGADisablePolicy")
          .scope(Scope.GROUP)),
  CPS_BACKUP_COMPLIANCE_POLICY_2_PERSON_DISABLEMENT(
      opts()
          .appSetting("mms.featureFlag.backup.cpsBackupCompliancePolicy2PersonDisablement")
          .scope(Scope.GROUP)),
  CPS_BACKUP_CUSTOMER_COLLECTION_METADATA(
      opts()
          .appSetting("mms.featureFlag.backup.cpsBackupCustomerCollectionMetadata")
          .scope(Scope.GROUP)),
  CPS_COLLECT_DIRECT_ATTACH_RESTORE_STATS(
      opts()
          .appSetting("mms.featureFlag.backup.cpsCollectDirectAttachRestoreStats")
          .scope(Scope.GROUP)),
  CPS_BACKUP_COLLECTION_LEVEL_RESTORE(
      opts().appSetting("mms.featureFlag.backup.cpsCollectionLevelRestore").scope(Scope.GROUP)),
  CPS_BACKUP_COLLECTION_LEVEL_RESTORE_UI(
      opts().appSetting("mms.featureFlag.backup.cpsCollectionLevelRestoreUi").scope(Scope.GROUP)),
  CPS_USE_NO_WHOLE_FILE_RSYNC(
      opts().appSetting("mms.featureFlag.backup.useNoWholeFileRsync").scope(Scope.GROUP)),

  CPS_NO_WHOLE_FILE_RSYNC_OFF(
      opts().appSetting("mms.featureFlag.backup.cpsNoWholeFileRsyncOff").scope(Scope.GROUP)),

  CPS_CONCURRENT_SNAPSHOTS(
      opts().appSetting("mms.featureFlag.backup.cpsConcurrentSnapshots").scope(Scope.GROUP)),

  CPS_DIRECT_ATTACH(opts().appSetting("mms.featureFlag.backup.cpsDirectAttach").scope(Scope.GROUP)),

  CPS_DIRECT_ATTACH_PREWARM_STRATEGIES_FOR_AWS(
      opts()
          .appSetting("mms.featureFlag.backup.cpsDirectAttachPrewarmStrategiesForAws")
          .scope(Scope.GROUP)),

  CPS_DIRECT_ATTACH_PREWARM_STRATEGIES_FOR_AZURE(
      opts()
          .appSetting("mms.featureFlag.backup.cpsDirectAttachPrewarmStrategiesForAzure")
          .scope(Scope.GROUP)),

  CPS_DIRECT_ATTACH_REPL_WRITER_THREAD_COUNT_INCREASE(
      opts()
          .appSetting("mms.featureFlag.backup.cpsDirectAttachReplWriterThreadCountIncrease")
          .scope(Scope.GROUP)),

  CPS_EMBEDDED_CONFIG(
      opts().appSetting("mms.featureFlag.backup.cpsEmbeddedConfig").scope(Scope.GROUP)),

  CPS_DONT_PLAN_PACPCM_FOR_DIRECT_ATTACH(
      opts()
          .appSetting("mms.featureFlag.backup.cpsDontPlanPacpcmForDirectAttach")
          .scope(Scope.GROUP)),

  CPS_EMBEDDED_CONFIG_UI(
      opts().appSetting("mms.featureFlag.backup.cpsEmbeddedConfigUi").scope(Scope.GROUP)),

  TURN_CPS_PV2_INSTANT_RESTORE_OFF(
      opts().appSetting("mms.featureFlag.backup.cpsPv2InstantRestoreOff").scope(Scope.GROUP)),

  CPS_PV2_STREAMING_RESTORE(
      opts().appSetting("mms.featureFlag.backup.cpsPv2StreamingRestore").scope(Scope.GROUP)),

  CPS_RESTORE_SEARCH_INDEX(
      opts().appSetting("mms.featureFlag.backup.cpsRestoreSearchIndex").scope(Scope.GROUP)),

  CPS_RESTORE_CROSS_PROJECT_AWS(
      opts().appSetting("mms.featureFlag.backup.cpsRestoreCrossProjectAws").scope(Scope.GROUP)),

  CPS_RESTORE_CROSS_PROJECT_AWS_MIGRATION(
      opts()
          .appSetting("mms.featureFlag.backup.cpsRestoreCrossProjectAwsMigration")
          .scope(Scope.GROUP)),

  CPS_RESTORE_CROSS_PROJECT_AWS_NEW_CMK(
      opts()
          .appSetting("mms.featureFlag.backup.cpsRestoreCrossProjectAwsNewCMK")
          .scope(Scope.GROUP)),

  CPS_RESTORE_CROSS_PROJECT_GCP(
      opts().appSetting("mms.featureFlag.backup.cpsRestoreCrossProjectGCP").scope(Scope.GROUP)),

  CPS_RESTORE_CROSS_PROJECT_AZURE(
      opts().appSetting("mms.featureFlag.backup.cpsRestoreCrossProjectAzure").scope(Scope.GROUP)),

  CPS_OPTIMIZED_DA_RESTORE_AWS(
      opts().appSetting("mms.featureFlag.backup.cpsOptimizedDaRestoreAws").scope(Scope.GROUP)),

  CPS_OPTIMIZED_DA_RESTORE_AZURE(
      opts().appSetting("mms.featureFlag.backup.cpsOptimizedDaRestoreAzure").scope(Scope.GROUP)),

  CPS_OPTIMIZED_DA_RESTORE_GCP(
      opts().appSetting("mms.featureFlag.backup.cpsOptimizedDaRestoreGcp").scope(Scope.GROUP)),

  CPS_QUERY_RESTORE_STATUS_FOR_DISK_SWAP_AWS(
      opts()
          .appSetting("mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAws")
          .scope(Scope.GROUP)),

  CPS_QUERY_RESTORE_STATUS_FOR_DISK_SWAP_AZURE(
      opts()
          .appSetting("mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapAzure")
          .scope(Scope.GROUP)),

  CPS_QUERY_RESTORE_STATUS_FOR_DISK_SWAP_GCP(
      opts()
          .appSetting("mms.featureFlag.backup.cpsQueryRestoreStatusForDiskSwapGcp")
          .scope(Scope.GROUP)),

  CPS_AWS_DA_RESTORE_WITH_IO2(
      opts().appSetting("mms.featureFlag.backup.cpsAwsDaRestoreWithIo2").scope(Scope.GROUP)),

  TURN_CPS_DIRECT_ATTACH_OFF(
      opts().appSetting("mms.featureFlag.backup.cpsDirectAttachOff").scope(Scope.GROUP)),

  CPS_DOWNLOAD_ENCRYPTED_SNAPSHOT(
      opts().appSetting("mms.featureFlag.backup.cpsDownloadEncryptedSnapshot").scope(Scope.GROUP)),

  CPS_EXTRA_OPEN_BACKUP_CURSOR_TIMEOUT(
      opts()
          .appSetting("mms.featureFlag.backup.cpsExtraOpenBackupCursorTimeout")
          .scope(Scope.GROUP)),

  CPS_EXTRA_WAIT_FOR_LAST_BATCH_TIMEOUT(
      opts()
          .appSetting("mms.featureFlag.backup.cpsExtraWaitForLastBatchTimeout")
          .scope(Scope.GROUP)),

  CPS_GCP_AND_AZURE_NEW_CLUSTERS_ONLY_CPS(
      opts()
          .appSetting("mms.featureFlag.backup.cpsGcpAndAzureNewClustersOnlyCps")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  CPS_SNAPSHOT_EXPORT_HIGH_FREQUENCY(
      opts()
          .appSetting("mms.featureFlag.backup.cpsSnapshotExportHighFrequency")
          .scope(Scope.GROUP)),

  CPS_SNAPSHOT_EXPORT_UI(
      opts().appSetting("mms.featureFlag.backup.cpsSnapshotExportUi").scope(Scope.GROUP)),

  CPS_SNAPSHOT_EXPORT_AZURE(
      opts().appSetting("mms.featureFlag.backup.cpsSnapshotExportAzure").scope(Scope.GROUP)),

  CPS_SNAPSHOT_EXPORT_GCP(
      opts().appSetting("mms.featureFlag.backup.cpsSnapshotExportGCP").scope(Scope.GROUP)),

  CPS_SNAPSHOT_CONSISTENT_EXPORT(
      opts().appSetting("mms.featureFlag.backup.cpsSnapshotConsistentExport").scope(Scope.GROUP)),

  CPS_SNAPSHOT_CONSISTENT_EXPORT_SPLIT_LARGE_COLLECTIONS(
      opts()
          .appSetting("mms.featureFlag.backup.cpsSnapshotConsistentExportSplitLargeCollections")
          .scope(Scope.GROUP)),

  CPS_SKIP_SYSTEM_CLUSTER_DESTROY(
      opts().appSetting("mms.featureFlag.backup.cpsSkipSystemClusterDestroy").scope(Scope.GROUP)),

  CPS_SYSTEM_CLUSTER_MINIMAL_DEPLOY(
      opts().appSetting("mms.featureFlag.backup.cpsSystemClusterMinimalDeploy").scope(Scope.GROUP)),

  CPS_SYSTEM_PROJECTS_FOR_EXPORTS(
      opts().appSetting("mms.featureFlag.backup.cpsSystemProjectsForExports").scope(Scope.GROUP)),

  CPS_SYSTEM_PROJECTS_FOR_DATA_LAKE_INGESTION_PIPELINE_EXPORTS(
      opts()
          .appSetting("mms.featureFlag.backup.cpsSystemProjectsForDataLakeIngestionPipelineExports")
          .scope(Scope.GROUP)),

  CPS_SNAPSHOT_DISTRIBUTION_AWS(
      opts().appSetting("mms.featureFlag.backup.cpsSnapshotDistributionAws").scope(Scope.GROUP)),

  CPS_SNAPSHOT_DISTRIBUTION_GCP(
      opts().appSetting("mms.featureFlag.backup.cpsSnapshotDistributionGcp").scope(Scope.GROUP)),

  CPS_SNAPSHOT_DISTRIBUTION_AZURE(
      opts().appSetting("mms.featureFlag.backup.cpsSnapshotDistributionAzure").scope(Scope.GROUP)),

  CPS_SNAPSHOT_DISTRIBUTION_LARGE_REGION_SET(
      opts()
          .appSetting("mms.featureFlag.backup.cpsSnapshotDistributionLargeRegionSet")
          .scope(Scope.GROUP)),

  CPS_SNAPSHOT_DISTRIBUTION_UI(
      opts().appSetting("mms.featureFlag.backup.cpsSnapshotDistributionUi").scope(Scope.GROUP)),

  CPS_SNAPSHOT_AWS_PRIVATE_DOWNLOAD(
      opts().appSetting("mms.featureFlag.backup.cpsSnapshotAWSPrivateDownload").scope(Scope.GROUP)),

  TURN_CPS_SNAPSHOT_AWS_PRIVATE_DOWNLOAD_OFF(
      opts()
          .appSetting("mms.featureFlag.backup.turnCpsSnapshotAWSPrivateDownloadOff")
          .scope(Scope.GROUP)),

  CPS_SNAPSHOT_AZURE_PRIVATE_DOWNLOAD(
      opts()
          .appSetting("mms.featureFlag.backup.cpsSnapshotAzurePrivateDownload")
          .scope(Scope.GROUP)),

  TURN_CPS_SNAPSHOT_AZURE_PRIVATE_DOWNLOAD_OFF(
      opts()
          .appSetting("mms.featureFlag.backup.turnCpsSnapshotAzurePrivateDownloadOff")
          .scope(Scope.GROUP)),

  CPS_OPLOG_IN_GCP(opts().appSetting("mms.featureFlag.backup.cpsOplogInGcp").scope(Scope.GROUP)),
  CPS_OPLOG_IN_GCP_EXTEND_MIGRATION(
      opts().appSetting("mms.featureFlag.backup.cpsOplogInGcpExtendMigration").scope(Scope.GROUP)),

  CPS_OPLOG_MIGRATION(
      opts().appSetting("mms.featureFlag.backup.cpsOplogMigration").scope(Scope.GROUP)),

  CPS_OPLOG_IN_AZURE(
      opts().appSetting("mms.featureFlag.backup.cpsOplogInAzure").scope(Scope.GROUP)),
  CPS_PRE_RESTORE_UPGRADE(
      opts().appSetting("mms.featureFlag.backup.cpsPreRestoreUpgrade").scope(Scope.GROUP)),
  CPS_SUCCESSIVE_UPGRADE_QUARTERLY(
      opts().appSetting("mms.featureFlag.backup.cpsSuccessiveUpgradeQuarterly").scope(Scope.GROUP)),

  CPS_OPTIMAL_SUCCESSIVE_UPGRADE_ENABLED(
      opts()
          .appSetting("mms.featureFlag.backup.optimalSuccessiveUpgradeForRestoreEnabled")
          .scope(Scope.GROUP)),
  CPS_MANUAL_DOWNLOAD_PGZIP_ENABLED(
      opts().appSetting("mms.featureFlag.backup.pGzipEnabled").scope(Scope.GROUP)),
  CPS_LEGACY_BACKUP_MIGRATION(
      opts().appSetting("mms.featureFlag.backup.cpsLegacyBackupMigration").scope(Scope.GROUP)),

  CPS_DISAGGREGATED_STORAGE(
      opts().appSetting("mms.featureFlag.backup.cpsDisaggregatedStorage").scope(Scope.GROUP)),

  CROSS_ORG_BILLING(opts().appSetting("mms.featureFlag.crossOrgBilling").scope(Scope.ORGANIZATION)),
  CROSS_ORG_PROJECT_LEVEL_SUPPORT(
      opts().appSetting("mms.featureFlag.crossOrgProjectLevelSupport").scope(Scope.ORGANIZATION)),
  BILLING_USE_PRICING_API(
      opts().appSetting("mms.featureFlag.billingUsePricingApi").scope(Scope.ORGANIZATION)),

  CUSTOMER_FEDERATION(
      opts().appSetting("mms.featureFlag.customerFederation").scope(Scope.ORGANIZATION)),

  PAGERDUTY_MAINTENANCE_WINDOW_STATUS(
      opts()
          .appSetting("mms.featureFlag.pagerduty.includeMaintenanceWindowStatus")
          .scope(Scope.ORGANIZATION)),

  OM_CONFIG_FOR_GLOBAL_MONITORING_ADMIN(
      opts()
          .appSetting("mms.featureFlag.opsManagerConfigForGlobalMonitoringAdmin")
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  DATADOG_METRICS_INTEGRATION(
      opts()
          .appSetting("mms.featureFlag.monitoring.dataDogMetrics")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  DATADOG_DB_AND_COLL_METRICS_INTEGRATION(
      opts()
          .appSetting("mms.featureFlag.monitoring.dataDogDbAndCollMetrics")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  DATADOG_CUSTOM_ENDPOINT(
      opts().appSetting("mms.featureFlag.datadogCustomEndpoint").scope(Scope.GROUP)),

  DATADOG_CLIENT_CONNECTION_TTL(
      opts()
          .appSetting("mms.featureFlag.monitoring.dataDogClientConnectionTtl")
          .scope(Scope.GROUP)),

  DATA_EXPLORER(
      opts()
          .appSetting("mms.featureFlag.dataExplorer")
          .enabledByDefaultForPlanTypes(PlanType.STANDARD, PlanType.PREMIUM, PlanType.ONPREM)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  DATA_EXPLORER_AGGREGATION(
      opts()
          .appSetting("mms.featureFlag.dataExplorerAggregation")
          .enabledByDefaultForPlanTypes(PlanType.STANDARD, PlanType.PREMIUM, PlanType.ONPREM)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  DATA_EXPLORER_CRUD(
      opts()
          .appSetting("mms.featureFlag.dataExplorerCrud")
          .enabledByDefaultForPlanTypes(PlanType.STANDARD, PlanType.PREMIUM, PlanType.ONPREM)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  DATA_EXPLORER_MULTI_TENANT(
      opts()
          .appSetting("mms.featureFlag.dataExplorerMultiTenant")
          .enabledByDefaultForPlanTypes(PlanType.STANDARD, PlanType.PREMIUM, PlanType.ONPREM)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  EXTEND_MAX_ALLOWED_DISK_SIZES(
      opts().appSetting("mms.featureFlag.extendMaxAllowedDiskSizes").scope(Scope.GROUP)),
  ALERT_STATE_FLAPPING_DETECTION(
      opts().appSetting("mms.featureFlag.alertStateFlappingDetection").scope(Scope.GROUP)),
  FINE_GRAINED_AUTH(
      opts().appSetting("mms.featureFlag.enableFineGrainedAuth").scope(Scope.ORGANIZATION)),
  FINE_GRAINED_AUTH_USER_GROUPS(
      opts().appSetting("mms.featureFlag.fineGrainedAuth.userGroups").scope(Scope.ORGANIZATION)),
  INSTALL_AGENT_SCRIPT(
      opts()
          .appSetting("mms.featureFlag.installAgentScript")
          .enabledByDefaultForPlanTypes(PlanType.STANDARD, PlanType.PREMIUM)
          .scope(Scope.GROUP)),

  ATLAS_SERVERLESS_USES_SERVERLESS_AGENT(
      opts().appSetting("mms.featureFlag.atlasServerlessUsesServerlessAgent").scope(Scope.GROUP)),

  ENABLE_RAMI_AGENT(
      opts().appSetting("mms.featureFlag.automation.enableRamiAgent").scope(Scope.GROUP)),

  ENABLE_MONGOTUNE(
      opts().appSetting("mms.featureFlag.automation.enableMongotune").scope(Scope.GROUP)),

  ENABLE_SYNC_INDEX_DELETION(
      opts().appSetting("mms.featureFlag.atlasSearch.enableSyncIndexDeletion").scope(Scope.GROUP)),

  ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY(
      opts().appSetting("mms.featureFlag.mongotune.enableWriteBlockPolicy").scope(Scope.GROUP)),

  MANAGED_SLOW_MS(
      opts()
          .appSetting("mms.featureFlag.managedSlowMs")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  MAX_AGGREGATED_DISK_CHARTS_FOR_ATLAS(
      opts().appSetting("mms.featureFlag.maxAggregatedDiskChartsForAtlas").scope(Scope.GROUP)),

  MONGODB_ACCESS_HISTORY(
      opts()
          .appSetting("mms.featureFlag.mongoDBAccessHistory")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  MONITORING_CLUSTER_REFERENCES_AUTOCORRECT(
      opts().appSetting("mms.featureFlag.clusterReferencesAutocorrect").scope(Scope.GROUP)),

  MONITORING_HOST_MAPPINGS_AUTOCORRECT(
      opts().appSetting("mms.featureFlag.hostMappingsAutocorrect").scope(Scope.GROUP)),

  M10_SHARDED_CLUSTERS(opts().appSetting("mms.featureFlag.m10ShardedClusters").scope(Scope.GROUP)),

  NDS_CLUSTERS(
      opts()
          .appSetting("mms.featureFlag.ndsClusters")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  NEW_DEPLOYMENT_SERVER_UI(
      opts().appSetting("mms.featureFlag.automation.newDeploymentServerUI").scope(Scope.GROUP)),

  NEWRELIC_INTEGRATION(opts().appSetting("mms.featureFlag.monitoring.newRelic").scope(Scope.GROUP)),

  PA_FEEDBACK(
      opts()
          .appSetting("mms.featureFlag.paFeedback")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  PA_BUGS_AND_SUSTAINABILITY(
      opts()
          .appSetting("mms.featureFlag.paBugsAndSustainability")
          .enabledByDefaultForPlanTypes(PlanType.STANDARD, PlanType.PREMIUM, PlanType.ONPREM)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  PERFORMANCE_ADVISOR(
      opts()
          .appSetting("mms.featureFlag.performanceAdvisor")
          .enabledByDefaultForPlanTypes(PlanType.PREMIUM, PlanType.ONPREM)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  CUSTOMER_QUERY_SHAPE_INSIGHTS_TOGGLE(
      opts()
          .appSetting("mms.featureFlag.customer.queryShapeInsights")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  SCHEMA_ADVISOR(
      opts()
          .appSetting("mms.featureFlag.schemaAdvisor")
          .enabledByDefaultForPlanTypes(PlanType.PREMIUM, PlanType.ONPREM)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  HISTORICAL_REPLICA_STATE(
      opts().appSetting("mms.featureFlag.historicalReplicaState").scope(Scope.GROUP)),
  HISTORICAL_REPLICA_STATE_INGESTION(
      opts().appSetting("mms.featureFlag.historicalReplicaState.ingestion").scope(Scope.GROUP)),

  RESOURCE_TAG_COMPONENT(
      opts().appSetting("mms.featureFlag.resourceTagComponent.clusters").scope(Scope.GROUP)),

  RESOURCE_TAG_COMPONENT_PROJECTS(
      opts().appSetting("mms.featureFlag.resourceTagComponent.projects").scope(Scope.ORGANIZATION)),

  ADD_RESOURCE_TAGS_TO_EXPORTED_METRICS(
      opts().appSetting("mms.featureFlag.addResourceTagsToExportedMetrics").scope(Scope.GROUP)),

  SEGMENT_GROUP_SYNCHRONIZER(
      opts().appSetting("mms.featureFlag.segmentGroupSynchronizer").scope(Scope.ORGANIZATION)),

  SELF_SERVE_CONSULTING_UNITS(
      opts().appSetting("mms.featureFlag.selfServeConsultingUnits").scope(Scope.ORGANIZATION)),

  // Now enabled by default for both NDS and OM users
  PROFILER_NDS(
      opts()
          .appSetting("mms.featureFlag.profilerNDS")
          .enabledByDefaultForPlanTypes(PlanType.ONPREM)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  // The Revamped Profiler
  PROFILER_V2(
      opts()
          .appSetting("mms.featureFlag.profilerv2")
          .enabledByDefaultForPlanTypes(PlanType.PREMIUM, PlanType.ONPREM)
          .scope(Scope.GROUP)),

  // The Revamped Profiler (again)
  PROFILER_V3(
      opts()
          .appSetting("mms.featureFlag.profilerv3")
          .enabledByDefaultForPlanTypes(PlanType.PREMIUM, PlanType.ONPREM)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  PROMETHEUS_INTEGRATION(opts().appSetting("mms.featureFlag.prometheus").scope(Scope.GROUP)),

  PROMETHEUS_INTEGRATION_RATE_LIMIT_CONFIG(
      opts()
          .appSetting("mms.FeatureFlag.prometheus.allowCustomerRateLimitConfig")
          .scope(Scope.GROUP)),

  QUERYABLE_BACKUP(opts().appSetting("mms.featureFlag.backup.queryable").scope(Scope.GROUP)),

  QUERYABLE_FSCACHE(
      opts().appSetting("mms.featureFlag.backup.queryableFsCache").scope(Scope.GROUP)),

  QUERYABLE_WT_BACKUP(opts().appSetting("mms.featureFlag.backup.wt.queryable").scope(Scope.GROUP)),
  ON_DEMAND_SNAPSHOT(
      opts().appSetting("mms.featureFlag.backup.snapshot.onDemand").scope(Scope.GROUP)),

  REALM_SYNC_BETA(opts().appSetting("mms.featureFlag.realm.syncBeta").scope(Scope.GROUP)),

  REALTIME(
      opts()
          .appSetting("mms.featureFlag.realtime")
          .enabledByDefaultForPlanTypes(PlanTypeSet.ALL)
          .scope(Scope.GROUP)),

  ROLLING_INDEXES(
      opts()
          .appSetting("mms.featureFlag.automation.rollingIndexes")
          .enabledByDefaultForPlanTypes(PlanTypeSet.ALL)
          .scope(Scope.GROUP)),

  RSYNC_BASED_HEAD_CREATION(
      opts().appSetting("mms.featureFlag.rsyncBasedHeadCreation").scope(Scope.GROUP)),

  S3_VALIDATION_TESTING(opts().appSetting("brs.s3.validation.testing").scope(Scope.GROUP)),

  STORAGE_ENGINE_BACKUP(
      opts().appSetting("mms.featureFlag.backup.storageEngine").scope(Scope.GROUP)),

  TEAMS(opts().appSetting("mms.featureFlag.teams").scope(Scope.GROUP)),

  ATLAS_SHARED_TIER_X509_Auth(
      opts().appSetting("mms.featureFlag.atlasSharedTierX509Auth").scope(Scope.GROUP)),

  OPLATENCY(
      opts()
          .appSetting("mms.featureFlag.opLatency")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  AUTO_INDEXING(opts().appSetting("mms.featureFlag.autoIndexing").scope(Scope.GROUP)),

  DEDICATED_ATLAS_SEARCH_NODES(
      opts().appSetting("mms.featureFlag.atlasSearch.dedicatedNodes").scope(Scope.GROUP)),

  DEDICATED_ATLAS_SEARCH_NODES_AWS_SERIES_SEVEN(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.dedicatedNodesAWSSeriesSeven")
          .scope(Scope.GROUP)),

  DEDICATED_ATLAS_SEARCH_NODES_READ_PREFERENCE(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.dedicatedNodesReadPreference")
          .enabledByDefaultForPlanTypes(PlanTypeSet.ALL)
          .scope(Scope.GROUP)),

  DEDICATED_ATLAS_SEARCH_NODES_READ_PREFERENCE_GROUP_OVERRIDE(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.dedicatedNodesReadPreferenceGroupOverride")
          .scope(Scope.GROUP)),

  DEDICATED_ATLAS_SEARCH_NODES_REPLICATION_COMPRESSION(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.dedicatedNodesEnableReplicationCompression")
          .scope(Scope.GROUP)),

  DEDICATED_ATLAS_SEARCH_NODES_FORCE_DISABLE_REPLICATION(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.dedicatedNodesForceDisableReplication")
          .scope(Scope.GROUP)),

  DEDICATED_ATLAS_SEARCH_NODES_DISABLE_PROVISION_TIMEOUT(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.dedicatedNodesDisableProvisionTimeout")
          .scope(Scope.GROUP)),

  ATLAS_SEARCH_MULTIPLE_SUB_INDEXES(
      opts().appSetting("mms.featureFlag.atlasSearch.multipleSubIndexes").scope(Scope.GROUP)),

  ATLAS_SEARCH_STALE_INDEXES(
      opts().appSetting("mms.featureFlag.atlasSearch.staleIndexes").scope(Scope.GROUP)),

  ATLAS_SEARCH_BYOK(opts().appSetting("mms.featureFlag.atlasSearch.byok").scope(Scope.GROUP)),

  ATLAS_SEARCH_ENABLE_NATURAL_ORDER_SCAN(
      opts().appSetting("mms.featureFlag.atlasSearch.enableNaturalOrderScan").scope(Scope.GROUP)),

  SEARCH_TESTER_QUERY_TEMPLATES(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.searchTesterQueryTemplates")
          .scope(Scope.GROUP)),

  SEARCH_INDEX_STATUS_REPORTING(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.searchIndexStatusReporting")
          .scope(Scope.GROUP)),

  ATLAS_SEARCH_SYNCHRONOUS_STEADY_STATE_REPLICATION(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.synchronousSteadyStateReplication")
          .enabledByDefaultForPlanTypes(PlanTypeSet.ALL)
          .scope(Scope.GROUP)),

  ATLAS_SEARCH_SYNCHRONOUS_STEADY_STATE_REPLICATION_GROUP_OVERRIDE(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.synchronousSteadyStateReplicationGroupOverride")
          .scope(Scope.GROUP)),

  ATLAS_SEARCH_INDEX_MANAGEMENT(
      opts().appSetting("mms.featureFlag.atlasSearch.indexManagement").scope(Scope.GROUP)),

  ATLAS_SEARCH_INDEX_PARTITIONING_UI(
      opts().appSetting("mms.featureFlag.atlasSearch.indexPartitioningUI").scope(Scope.GROUP)),

  ATLAS_SEARCH_VIB_CUSTOM_ANALYZERS(
      opts().appSetting("mms.featureFlag.atlasSearch.vibCustomAnalyzersUI").scope(Scope.GROUP)),

  ATLAS_SEARCH_SEARCH_CONF_CALL_VIA_SEARCH_GATEWAY(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.confCallViaSearchGateway")
          .enabledByDefaultForPlanTypes(PlanTypeSet.ALL)
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_SKIP_DETAILED_STATUS_WRITE(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.disableDetailedStatusWrite")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_USE_LIFECYCLE_MANAGER(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.useLifecycleManager")
          .enabledByDefaultForPlanTypes(PlanTypeSet.ALL)
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENVOY_DISABLE_STREAM_IDLE_TIMEOUT(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.envoy.disableStreamIdleTimeout")
          .enabledByDefaultForPlanTypes(PlanTypeSet.ALL)
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENVOY_LOAD_BALANCING_POLICY_RANDOM(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.envoy.loadBalancingPolicyRandom")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENVOY_LOG_LEVEL(
      opts().appSetting("mms.featureFlag.atlasSearch.envoy.logLevel").scope(Scope.GROUP)),
  ATLAS_SEARCH_INDEX_CONFIG_STATS_WRITES(
      opts().appSetting("mms.featureFlag.atlasSearch.indexConfigStatsWrites").scope(Scope.GROUP)),
  ATLAS_SEARCH_SEARCH_HOST_STATS_WRITES(
      opts().appSetting("mms.featureFlag.atlasSearch.searchHostStatsWrites").scope(Scope.GROUP)),
  ATLAS_SEARCH_READ_INDEX_STATS_FROM_NEW_COLLECTION(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.readIndexStatsFromNewCollection")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_TEXT_OPERATOR_NEW_SYNONYMS_SYNTAX(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.enableTextOperatorNewSynonymsSyntax")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_INDEX_CONFIG_UPDATER(
      opts().appSetting("mms.featureFlag.atlasSearch.indexConfigUpdater").scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_BLOBSTORE_ACCESS(
      opts().appSetting("mms.featureFlag.atlasSearch.enableBlobstoreAccess").scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_BLOBSTORE_DOWNLOAD_ACCESS(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.enableBlobstoreDownloadAccess")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_BLOBSTORE_UPLOAD_ACCESS(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.enableBlobstoreUploadAccess")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_NON_ZERO_ATTEMPT_UPLOADS(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.enableNonZeroAttemptUploads")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_OVERRIDE_BYOK_CHECK_FOR_BLOBSTORE_CUSTOMER_APPROVED(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.overrideBlobstoreByokCheckWithCustomerApproval")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_AZURE_BLOBSTORE_UPLOAD_ACCESS(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.enableAzureBlobstoreUploadAccess")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_AZURE_BLOBSTORE_DOWNLOAD_ACCESS(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.enableAzureBlobstoreDownloadAccess")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_DISABLE_JVM_NATIVE_ACCESS(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.disableMongotJvmNativeAccess")
          .enabledByDefaultForPlanTypes(PlanTypeSet.ALL)
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_INDEX_QUANTIZATION_UI(
      opts().appSetting("mms.featureFlag.atlasSearch.indexQuantizationUI").scope(Scope.GROUP)),
  ATLAS_SEARCH_DISABLE_VIB_FOR_UNSUPPORTED_FEATURES(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.disableVIBForUnsupportedFeatures")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_AUTO_EMBEDDING_JSON_EDITOR(
      opts().appSetting("mms.featureFlag.atlasSearch.autoEmbeddingJsonEditor").scope(Scope.GROUP)),
  ATLAS_SEARCH_CDI_JSON_EDITOR(
      opts().appSetting("mms.featureFlag.atlasSearch.cdiJsonEditor").scope(Scope.GROUP)),
  ATLAS_SEARCH_AUTO_EMBEDDING_SIMILARITY_MIGRATION(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.autoEmbeddingSimilarityMigration")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_MONGOT_PUSH_BASED_LOG_EXPORT_DOWNLOAD(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.mongotPushBasedLogExportDownload")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_STORAGE_OPTIMIZED_SEARCH_NODES_UI(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.storageOptimizedSearchNodesUI")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_CONF_CALL_TIMEOUT(
      opts().appSetting("mms.featureFlag.atlasSearch.confCallTimeout").scope(Scope.GROUP)),
  ATLAS_SEARCH_PREVENT_DELETES_AFTER_COLLECTION_RENAME(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.preventDeletesAfterCollectionRename")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_AUTOMATION_AGENT_DISK_UNAVAILABLE_DETECTION(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.automationAgentDiskUnavailableDetection")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_IGNORE_INDEX_FAILURE_FOR_READY_STATE(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.ignoreIndexFailureForReadyState")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_EXCLUDE_DISAMBIGUATED_PATHS(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.excludeDisambiguatedPaths")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_MAAS_METRICS(
      opts().appSetting("mms.featureFlag.atlasSearch.enableMaasMetrics").scope(Scope.GROUP)),
  ATLAS_SEARCH_VOYAGE_API_SERVICE(
      opts().appSetting("mms.featureFlag.atlasSearch.voyageApiService").scope(Scope.ORGANIZATION)),
  ATLAS_SEARCH_CREATE_SAMPLE_SEARCH_INDEX(
      opts().appSetting("mms.featureFlag.atlasSearch.createSampleSearchIndex").scope(Scope.GROUP)),
  ATLAS_SEARCH_MATCH_COLLECTION_UUID_FOR_UPDATE_LOOKUP(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.matchCollectionUUIDForUpdateLookup")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_SPLIT_LARGE_CHANGE_STREAM_EVENTS(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.enableSplitLargeChangeStreamEvents")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_SHUT_DOWN_REPLICATION_WHEN_COLLECTION_NOT_FOUND(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.shutDownReplicationWhenCollectionNotFound")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_INITIAL_INDEX_STATUS_UNKNOWN(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.initialIndexStatusUnknown")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_INDEX_FEATURE_VERSION_FOUR(
      opts().appSetting("mms.featureFlag.atlasSearch.indexFeatureVersionFour").scope(Scope.GROUP)),
  ATLAS_SEARCH_DISABLE_PLANNER_REPLICATION_LAG_CATCH_UP_TIMEOUT(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.disablePlannerReplicationLagCatchUpTimeout")
          .scope(Scope.GROUP)),
  ATLAS_DATA_LAKE_STORAGE_FOR_ONLINE_ARCHIVE(
      opts().appSetting("mms.featureFlag.atlasDataLakeStorageForOnlineArchive").scope(Scope.GROUP)),

  ATLAS_DATA_LAKE_STORAGE_FORCE_V2_UPGRADE_FOR_ONLINE_ARCHIVE(
      opts()
          .appSetting("mms.featureFlag.atlasDataLakeStorageForceV2UpgradeForOnlineArchive")
          .scope(Scope.GROUP)),
  ATLAS_DATA_LAKE_INGESTION_PIPELINES_DEPRECATION(
      opts()
          .appSetting("mms.featureFlag.atlasDataLakeIngestionPipelinesDeprecation")
          .scope(Scope.GROUP)),

  ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_HALT_DATA_SET_DESTRUCTION(
      opts().appSetting("mms.featureFlag.dataLakeEOL.haltDataSetDestruction").scope(Scope.GROUP)),

  ATLAS_DATA_LAKE_INGESTION_PIPELINES_EOL_DISABLE_PIPELINES(
      opts().appSetting("mms.featureFlag.dataLakeEOL.disablePipelines").scope(Scope.GROUP)),

  ATLAS_DATA_LAKE_STORAGE_ALLOW_HOURLY_INGESTION(
      opts()
          .appSetting("mms.featureFlag.atlasDataLakeStorageAllowHourlyIngestion")
          .scope(Scope.GROUP)),

  ATLAS_DATA_LAKE_STORAGE_DATASET_RETENTION_POLICY(
      opts()
          .appSetting("mms.featureFlag.atlasDataLakeStorageDatasetRetentionPolicy")
          .scope(Scope.GROUP)),

  ATLAS_DATA_FEDERATION_DEDICATED_HOSTNAMES(
      opts()
          .appSetting("mms.featureFlag.atlasDataFederationDedicatedHostnames")
          .scope(Scope.GROUP)),

  ATLAS_DATA_FEDERATION_ON_GCP(
      opts().appSetting("mms.featureFlag.atlasDataFederationOnGCP").scope(Scope.GROUP)),

  ATLAS_CLOUD_PROVIDER_ACCESS_FOR_GCP(
      opts().appSetting("mms.featureFlag.atlasCloudProviderAccessForGCP").scope(Scope.GROUP)),

  ONLINE_ARCHIVE_V1(opts().appSetting("mms.featureFlag.atlasOnlineArchiveV1").scope(Scope.GROUP)),

  ONLINE_ARCHIVE_V3_TIME_SERIES(
      opts().appSetting("mms.featureFlag.atlasOnlineArchiveV3TimeSeries").scope(Scope.GROUP)),

  ONLINE_ARCHIVE_GCP(opts().appSetting("mms.featureFlag.atlasOnlineArchiveGCP").scope(Scope.GROUP)),

  ONLINE_ARCHIVE_PARTITION_FIELDS(
      opts().appSetting("mms.featureFlag.atlasOnlineArchivePartitionFields").scope(Scope.GROUP)),

  ONLINE_ARCHIVE_CROSS_CLOUD_CREATION(
      opts().appSetting("mms.featureFlag.atlasOnlineArchiveCrossCloudCreation").scope(Scope.GROUP)),

  ONLINE_ARCHIVE_AS_DATA_SOURCE(
      opts().appSetting("mms.featureFlag.atlasOnlineArchiveAsDataSource").scope(Scope.GROUP)),
  ONLINE_ARCHIVE_IGNORE_DATA_SIZE_THRESHOLD(
      opts()
          .appSetting("mms.featureFlag.atlasOnlineArchiveIgnoreDataSizeThreshold")
          .scope(Scope.GROUP)),
  ONLINE_ARCHIVE_ENCRYPTION(
      opts().appSetting("mms.featureFlag.atlasOnlineArchiveEncryption").scope(Scope.GROUP)),
  ONLINE_ARCHIVE_PIN_TO_PRIMARY_REGION(
      opts().appSetting("mms.featureFlag.atlasOnlineArchivePinToPrimaryRegion").scope(Scope.GROUP)),

  ATLAS_DATA_FEDERATION_TEMPLATED_ONBOARDING(
      opts()
          .appSetting("mms.featureFlag.atlasDataFederationTemplatedOnboarding")
          .scope(Scope.GROUP)),

  ATLAS_DATA_FEDERATION_AZURE_FEED_DOWNSTREAM_SYSTEMS(
      opts()
          .appSetting("mms.featureFlag.atlasDataFederationAzureFeedDownstreamSystems")
          .scope(Scope.GROUP)),

  ATLAS_DATA_FEDERATION_AZURE_PRIVATE_LINK(
      opts().appSetting("mms.featureFlag.atlasDataFederationAzurePrivateLink").scope(Scope.GROUP)),

  ADFA_SUPPORT_NEW_REGIONS(
      opts().appSetting("mms.featureFlag.adfaSupportNewRegions").scope(Scope.GROUP)),

  ALLOW_VULNERABILITY_SCANNING(
      opts().appSetting("mms.featureFlag.allowVulnerabilityScanning").scope(Scope.GROUP)),

  ATLAS_CONTINUOUS_DELIVERY(
      opts().appSetting("mms.featureFlag.atlasContinuousDelivery").scope(Scope.GROUP)),

  REALM_EVENTS(opts().appSetting("mms.featureFlag.realmEvents").scope(Scope.GROUP)),

  FAILED_ROLLING_INDEX_CLEANUP(
      opts().appSetting("mms.featureFlag.failedRollingIndexCleanup").scope(Scope.GROUP)),

  REALM_METRICS(
      opts()
          .appSetting("mms.featureFlag.realmMetrics")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  REALM_METRICS_ALERTS(
      opts()
          .appSetting("mms.featureFlag.realmMetricsAlerts")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_MONGOSYNC_REPLICASET_TO_SHARDED_CLUSTER(
      opts()
          .appSetting("mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToShardedCluster")
          .scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_MONGOSYNC_PUSH_REPLICASET_TO_SHARDED_CLUSTER(
      opts()
          .appSetting(
              "mms.featureFlag.atlasLiveMigrateMongosyncPushBasedReplicasetToShardedCluster")
          .scope(Scope.GROUP)),

  ATLAS_PUSH_LIVE_MIGRATIONS_MONGOSYNC_PRIVATE_ENDPOINTS(
      opts()
          .appSetting("mms.featureFlag.atlasPushLiveMigrationsMongosyncPrivateEndpoints")
          .scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_MONGOSYNC_VARIABLE_SHARD_COUNT(
      opts()
          .appSetting("mms.featureFlag.atlasLiveMigrateMongosyncVariableShardCount")
          .scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_MONGOSYNC_EMBEDDED_VERIFIER(
      opts()
          .appSetting("mms.featureFlag.atlasLiveMigrateMongosyncEmbeddedVerifier")
          .scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_DESTINATION_OPLOG_VALIDATION_FOR_VERIFIER(
      opts()
          .appSetting("mms.featureFlag.atlasLiveMigrateDestinationOplogValidationForVerifier")
          .scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_MONGOSYNC_REPLICASET_TO_SINGLE_SHARD(
      opts()
          .appSetting("mms.featureFlag.atlasLiveMigrateMongosyncReplicasetToSingleShard")
          .scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_SHARDED_HORIZONS(
      opts().appSetting("mms.featureFlag.atlasLiveMigrateShardedHorizons").scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_MONGOSYNC_OLDER_VERSION_SUPPORT(
      opts().appSetting("mms.featureFlag.atlasLiveMigrateOlderVersionSupport").scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_MONGOSYNC_OLDER_VERSION_GAP_SUPPORT(
      opts()
          .appSetting("mms.featureFlag.atlasLiveMigrateMongosyncOlderVersionGapSupport")
          .scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_MONGOSYNC_ONE_NINE_PREVIEW_SUPPORT(
      opts()
          .appSetting("mms.featureFlag.atlasLiveMigrateOneNinePreviewSupport")
          .scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_LIFT_OPLOG_LAG_RESTRICTION(
      opts()
          .appSetting("mms.featureFlag.atlasLiveMigrateLiftOplogLagRestriction")
          .scope(Scope.GROUP)),

  ATLAS_LIVE_MIGRATE_RUNS_ONLY_ON_AL2_HOSTS(
      opts().appSetting("mms.featureFlag.atlasLiveMigrateRunsOnlyOnAL2Hosts").scope(Scope.GROUP)),

  ATLAS_MIGRATION_HUB_MILESTONE_ONE(
      opts().appSetting("mms.featureFlag.atlasMigrationHubMilestoneOne").scope(Scope.GROUP)),

  OPT_OUT_FROM_KINESIS(opts().appSetting("mms.featureFlag.optOutFromKinesis").scope(Scope.GROUP)),

  CANONICAL_HOSTS_TTL_FILTERED_READS_ENABLED(
      opts()
          .appSetting("mms.featureFlag.monitoring.canonicalHostsTTLFilteredReads.enabled")
          .scope(Scope.GROUP)),

  CANONICAL_HOSTS_TTL_WRITES_ENABLED(
      opts()
          .appSetting("mms.featureFlag.monitoring.canonicalHostsTTLWrites.enabled")
          .scope(Scope.GROUP)),
  SHARDED_CLUSTER_NAMESPACE_METRICS(
      opts()
          .appSetting("mms.featureFlag.monitoring.shardedClusterNamespaceMetrics")
          .scope(Scope.GROUP)),
  NAMESPACE_QUERY_LATENCY_METRICS(
      opts()
          .appSetting("mms.featureFlag.monitoring.namespaceQueryLatencyMetrics")
          .enabledByDefaultForPlanTypes(PlanTypeSet.ALL)
          .scope(Scope.GROUP)),
  SKIP_UPDATE_CLUSTERS_DURING_LIVE_MIGRATION(
      opts()
          .appSetting("mms.featureFlag.monitoring.skipUpdateClustersDuringLiveMigration")
          .scope(Scope.GROUP)),
  PERFORMANCE_ADVISOR_RECOMMEND_SEARCH(
      opts()
          .appSetting("mms.featureFlag.performanceAdvisorRecommendSearch")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),
  UI_ACCESS_LIST(opts().appSetting("mms.featureFlag.uiAccessList").scope(Scope.ORGANIZATION)),
  ATLAS_SEARCH_QUERY_TELEMETRY(
      opts()
          .appSetting("mms.featureFlag.atlasSearchQueryTelemetry")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),
  PROFILER_RANDOM_SAMPLING_SLOW_LOG_STREAMER(
      opts().appSetting("mms.featureFlag.randomSamplingSlowLogStreamer").scope(Scope.GROUP)),
  DISABLE_PREMIUM_DATADOG(
      opts().appSetting("mms.featureFlag.disablePremiumDatadog").scope(Scope.GROUP)),
  SERVERLESS_AUTO_INDEXING(
      opts().appSetting("mms.featureFlag.serverlessAutoIndexing").scope(Scope.GROUP)),
  AUTOMATION_CHANGES_WEB_CLIENT(
      opts().appSetting("mms.featureFlag.automationChangesWebClient").scope(Scope.GROUP)),
  LOGCOLLECTION_S3_BACKEND(
      opts()
          .appSetting("mms.featureFlag.logcollectionS3Backend")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .enabledByDefaultForPlanTypes(PlanTypeSet.CLOUD_MANAGER)
          .scope(Scope.GROUP)),
  TOKENIZED_QUERY_SHAPE_STATS_METRICS(
      opts()
          .appSetting("mms.featureFlag.tokenizedQueryShapeStatsMetrics")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.ORGANIZATION)),
  ENABLE_EXPERIMENT_HOLDOUTS(
      opts().appSetting("mms.featureFlag.enableExperimentHoldouts").scope(Scope.ORGANIZATION)),
  CLUSTER_CENTRIC_DROP_INDEX(
      opts().appSetting("mms.featureFlag.clusterCentricDropIndex").scope(Scope.GROUP)),
  CLUSTER_CENTRIC_PERFORMANCE_ADVISOR(
      opts().appSetting("mms.featureFlag.clusterCentricPerformanceAdvisor").scope(Scope.GROUP)),
  CLUSTER_CENTRIC_QUERY_PROFILER(
      opts()
          .appSetting("mms.featureFlag.clusterCentricQueryProfiler")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .enabledByDefaultForPlanTypes(PlanType.PREMIUM)
          .scope(Scope.GROUP)),
  CLUSTER_CENTRIC_QUERY_PROFILER_FOLLOWUPS(
      opts().appSetting("mms.featureFlag.clusterCentricQueryProfilerFollowups").scope(Scope.GROUP)),
  CLUSTER_CENTRIC_SCHEMA_ADVISOR(
      opts().appSetting("mms.featureFlag.clusterCentricSchemaAdvisor").scope(Scope.GROUP)),
  EXTERNAL_LOGS_SINK(opts().appSetting("mms.featureFlag.externalLogsSink").scope(Scope.GROUP)),
  QUERY_SHAPE_INSIGHTS(opts().appSetting("mms.featureFlag.queryShapeInsights").scope(Scope.GROUP)),
  QUERY_SHAPE_INSIGHTS_SHAREABLE_URLS(
      opts().appSetting("mms.featureFlag.queryShapeInsightsShareableUrls").scope(Scope.GROUP)),
  REJECT_QUERY_SHAPE(opts().appSetting("mms.featureFlag.rejectQueryShape").scope(Scope.GROUP)),
  SEND_QUERYSTATS_TO_DATADOG(
      opts().appSetting("mms.featureFlag.queryShapeInsightsSendToDatadog").scope(Scope.GROUP)),
  QUERY_SHAPE_INSIGHTS_ADD_DATADOG_LABELS(
      opts().appSetting("mms.featureFlag.queryShapeInsightsAddDatadogLabels").scope(Scope.GROUP)),
  COLLECT_MONGOTUNE_LOGS(
      opts().appSetting("mms.featureFlag.collectMongoTuneLogs").scope(Scope.GROUP)),
  NAMESPACE_INSIGHTS(
      opts()
          .appSetting("mms.featureFlag.namespaceInsights")
          .enabledByDefaultForPlanTypes(PlanTypeSet.ALL)
          .scope(Scope.GROUP)),
  // This Feature Flag is deprecated and replaced by the more granular
  // CLUSTER_CENTRIC_QUERY_PROFILER and NAMESPACE_INSIGHTS Feature Flags. It's kept for Morphia
  // backwards compatibility reasons.
  @Deprecated
  QUERY_INSIGHTS(opts().appSetting("mms.featureFlag.queryInsights").scope(Scope.GROUP)),
  UPDATE_METRICS_UI_FOR_EMBEDDED_CONFIG_SERVERS(
      opts()
          .appSetting("mms.featureFlag.updateMetricsUiForEmbeddedConfigServers")
          .scope(Scope.GROUP)),
  ATLAS_OPT_OUT_PERIODIC_CORRUPTION_DETECTION(
      opts()
          .appSetting("mms.featureFlag.atlasOptOutPeriodicCorruptionDetection")
          .scope(Scope.GROUP)),
  SALES_SOLD_WARNING_ORG_PAYMENT_STATUS(
      opts()
          .appSetting("mms.featureFlag.payments.salesSoldWarningOrgPaymentStatus")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.ORGANIZATION)),
  NEW_BILLING_DOC_DOWNLOAD_MENU(
      opts()
          .appSetting("mms.featureFlag.payments.newBillingDocDownloadMenu")
          .scope(Scope.ORGANIZATION)),
  BATCH_CREATE_NETSUITE_INVOICES(
      opts()
          .appSetting("mms.featureFlag.payments.batchCreateNetsuiteInvoices")
          .scope(Scope.ORGANIZATION)),
  MC_DISCREPANCIES_UI(
      opts().appSetting("mms.featureFlag.payments.mcDiscrepancies.ui").scope(Scope.ORGANIZATION)),
  MC_DISCREPANCIES_BILLING_MODEL(
      opts()
          .appSetting("mms.featureFlag.payments.mcDiscrepancies.billingModel")
          .scope(Scope.ORGANIZATION)),
  SERVERLESS_GRPC_INCREMENTAL_ROLLOUT_GLOBAL_ENABLED(
      opts()
          .appSetting("mms.featureFlag.serverless.grpc.incrementalRollout.global.enabled")
          .scope(Scope.GROUP)),
  SERVERLESS_GRPC_INCREMENTAL_ROLLOUT_TENANT_ENABLED(
      opts()
          .appSetting("mms.featureFlag.serverless.grpc.incrementalRollout.tenant.enabled")
          .scope(Scope.GROUP)),
  SERVERLESS_GRPC_INCREMENTAL_ROLLOUT_CONTINUOUS_ENABLED(
      opts()
          .appSetting("mms.featureFlag.serverless.grpc.incrementalRollout.continuous.enabled")
          .scope(Scope.GROUP)),
  SERVERLESS_MTM_ELIGIBLE_FOR_UPGRADE_TO_DEDICATED(
      opts()
          .appSetting("mms.featureFlag.serverless.serverlessMtmEligibleForUpgradeToDedicated")
          .scope(Scope.GROUP)),
  SERVERLESS_SHARED_UI_OPTION_ENABLED(
      opts()
          .appSetting("mms.featureFlag.serverless.serverlessSharedUIOptionEnabled")
          .scope(Scope.GROUP)),
  SERVERLESS_UPGRADE_TO_DEDICATED_UI_ENABLED(
      opts()
          .appSetting("mms.featureFlag.serverlessUpgradeToDedicatedUIEnabled")
          .scope(Scope.ORGANIZATION)),

  /**
   * This feature is only used in dev/qa environments and should remain 'controlled' in dev/qa and
   * 'disabled' in prod environments. If the feature is enabled for the organization, daily billing
   * cron job will create a fake meter usage data on daily basis making the organization availbale
   * for billing/payments testing.
   */
  BILLING_TEST_METER_DATA_GENERATION(
      opts()
          .appSetting("mms.featureFlag.billing.testMeterDataGeneration")
          .scope(Scope.ORGANIZATION)),
  ATLAS_DBCHECK_ENABLE_MID_COLLECTION_RESUMABILITY(
      opts()
          .appSetting("mms.featureFlag.atlasDbcheckEnableMidCollectionResumability")
          .scope(Scope.GROUP)),
  IA_SLOW_LOGS_READS(opts().appSetting("mms.featureFlag.iaSlowLogsReads").scope(Scope.GROUP)),
  ATLAS_PRIORITIZE_HAVING_PRIMARY_ON_UPSCALED_NODE(
      opts()
          .appSetting("mms.featureFlag.atlasPrioritizeHavingPrimaryOnUpscaledNode")
          .scope(Scope.GROUP)),
  ATLAS_DISK_WARMING_SUPPORT(
      opts().appSetting("mms.featureFlag.atlasDiskWarmingSupport").scope(Scope.GROUP)),
  ATLAS_IGNORE_DISK_WARMING_STATE(
      opts().appSetting("mms.featureFlag.atlasIgnoreDiskWarmingState").scope(Scope.GROUP)),
  STREAMS_ENABLE_AZURE(opts().appSetting("mms.featureFlag.streamsEnableAzure").scope(Scope.GROUP)),
  STREAMS_ENABLE_ADDITIONAL_AZURE_REGIONS(
      opts().appSetting("mms.featureFlag.streamsEnableAdditionalAzureRegions").scope(Scope.GROUP)),
  STREAMS_AZURE_PRIVATE_LINK(
      opts().appSetting("mms.featureFlag.streamsAzurePrivateLink").scope(Scope.GROUP)),
  STREAMS_AZURE_CONFLUENT_PRIVATE_LINK(
      opts().appSetting("mms.featureFlag.streamsAzureConfluentPrivateLink").scope(Scope.GROUP)),
  STREAMS_AWS_PRIVATE_LINK(
      opts().appSetting("mms.featureFlag.streamsAWSPrivateLink").scope(Scope.GROUP)),
  STREAMS_AWS_KINESIS_PRIVATE_LINK(
      opts().appSetting("mms.featureFlag.streamsAWSKinesisPrivateLink").scope(Scope.GROUP)),
  STREAMS_GCP_PRIVATE_LINK(
      opts().appSetting("mms.featureFlag.streamsGCPPrivateLink").scope(Scope.GROUP)),
  STREAMS_GCP_CONFLUENT_PRIVATE_LINK(
      opts().appSetting("mms.featureFlag.streamsGCPConfluentPrivateLink").scope(Scope.GROUP)),
  STREAMS_AWS_S3_PRIVATE_LINK(
      opts().appSetting("mms.featureFlag.streamsAWSS3PrivateLink").scope(Scope.GROUP)),
  STREAMS_AWS_KINESIS_UX(
      opts().appSetting("mms.featureFlag.streamsAWSKinesisUX").scope(Scope.GROUP)),
  STREAMS_TRANSIT_GATEWAY_ENABLED(
      opts().appSetting("mms.featureFlag.streamsTransitGatewayEnabled").scope(Scope.GROUP)),
  STREAMS_ENABLE_KINESIS_CONNECTION(
      opts().appSetting("mms.featureFlag.streamsEnableKinesisConnection").scope(Scope.GROUP)),
  STREAMS_CREATE_PROCESSOR_UI(
      opts().appSetting("mms.featureFlag.streamsCreateProcessorUI").scope(Scope.GROUP)),
  STREAMS_DOWNLOADABLE_LOGS(
      opts().appSetting("mms.featureFlag.streamsDownloadableLogs").scope(Scope.GROUP)),
  ATLAS_DBCHECK_WAIT_FOR_ALL_NODES_TO_OBSERVE_DBCHECK_STOP(
      opts()
          .appSetting("mms.featureFlag.atlasDbcheckWaitForAllNodesToObserveDbcheckStop")
          .scope(Scope.GROUP)),
  ALLOW_GCP_PREVIEW_REGIONS(
      opts().appSetting("mms.featureFlag.allowGCPPreviewRegions").scope(Scope.GROUP)),

  DB_ACCESS_DURING_TENANT_UPGRADE_BLOCKED(
      opts().appSetting("mms.featureFlag.dbAccessDuringTenantUpgradeBlocked").scope(Scope.GROUP)),

  DB_ACCESS_DURING_TENANT_UPGRADE_ALLOWED(
      opts().appSetting("mms.featureFlag.dbAccessDuringTenantUpgradeAllowed").scope(Scope.GROUP)),

  ATLAS_AUTOMATIC_EMBEDDED_CONFIG_TRANSITIONS(
      opts()
          .appSetting("mms.featureFlag.atlasAutomaticEmbeddedConfigTransitions")
          .scope(Scope.GROUP)),
  ALLOW_AWS_PREVIEW_REGIONS(
      opts().appSetting("mms.featureFlag.allowAWSPreviewRegions").scope(Scope.GROUP)),
  ALLOW_AZURE_PREVIEW_REGIONS(
      opts().appSetting("mms.featureFlag.allowAzurePreviewRegions").scope(Scope.GROUP)),
  VQP_BINNING(
      opts()
          .appSetting("mms.featureFlag.vqpBinning")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),
  ATLAS_AZURE_DSV5_AND_ESV5_INSTANCE_FAMILIES(
      opts()
          .appSetting("mms.featureFlag.atlasAzureDsv5AndEsv5InstanceFamilies")
          .scope(Scope.GROUP)),
  SQL_SCHEMA_MANAGEMENT(
      opts().appSetting("mms.featureFlag.sqlSchemaManagementUI").scope(Scope.GROUP)),
  ATLAS_LIVE_IMPORT_HELIX_PUSH(
      opts().appSetting("mms.featureFlag.atlasLiveImportHelixPush").scope(Scope.ORGANIZATION)),
  ATLAS_LIVE_IMPORT_HELIX_PULL(
      opts().appSetting("mms.featureFlag.atlasLiveImportHelixPull").scope(Scope.ORGANIZATION)),
  ATLAS_SHARDED_CLUSTERS_BEHIND_UNIFORM_FRONTEND(
      opts()
          .appSetting("mms.featureFlag.atlasShardedClustersBehindUniformFrontend")
          .scope(Scope.GROUP)),
  SERVICE_ACCOUNT_MANAGEMENT_ENABLED(
      opts().appSetting("mms.featureFlag.serviceAccountManagement").scope(Scope.ORGANIZATION)),
  ATLAS_MONGOMIRROR_LIVE_IMPORT_HELIX(
      opts()
          .appSetting("mms.featureFlag.atlasMongomirrorLiveImportHelix")
          .scope(Scope.ORGANIZATION)),
  ATLAS_LIVE_IMPORT_LARGE_NODE_POOL(
      opts().appSetting("mms.featureFlag.atlasLiveImportLargeNodePool").scope(Scope.GROUP)),
  ATLAS_ENABLE_ADDITIONAL_DBCHECK_VALIDATIONS(
      opts()
          .appSetting("mms.featureFlag.atlasEnableAdditionalDbcheckValidations")
          .scope(Scope.GROUP)),
  ATLAS_DEDICATED_BACKBONE_TO_REACT(
      opts().appSetting("mms.featureFlag.atlasDedicatedBackboneToReact").scope(Scope.GROUP)),
  ATLAS_DEDICATED_REACT_CLUSTER_DETAILS(
      opts().appSetting("mms.featureFlag.atlasDedicatedReactClusterDetails").scope(Scope.GROUP)),
  ATLAS_STREAMS_SP10_TIER(
      opts().appSetting("mms.featureFlag.atlasStreamsSP10InstanceTier").scope(Scope.GROUP)),
  DATA_EXPLORER_COMPASS_WEB_GLOBAL_WRITES(
      opts()
          .appSetting("mms.featureFlag.dataExplorerCompassWeb.enableGlobalWrites")
          .scope(Scope.GROUP)),
  DATA_EXPLORER_COMPASS_WEB_ENABLE_DATA_MODELING(
      opts()
          .appSetting("mms.featureFlag.dataExplorerCompassWeb.enableDataModeling")
          .scope(Scope.GROUP)),
  DISABLE_CONCURRENT_ROLLING_INDEXES(
      opts().appSetting("mms.featureFlag.disableConcurrentRollingIndexes").scope(Scope.GROUP)),
  DATA_EXPLORER_CLUSTER_CONNECTION_V2_ENDPOINT(
      opts().appSetting("mms.featureFlag.clusterConnectionV2Rollout").scope(Scope.GROUP)),
  DATA_EXPLORER_COMPASS_WEB_USER_CONTROLLED_DISABLE(
      opts()
          .appSetting("mms.featureFlag.dataExplorerCompassWeb.userControlledDisable")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),
  ATLAS_CLUSTER_SCALING_IMPROVEMENTS_PHASE1(
      opts()
          .appSetting("mms.featureFlag.atlasClusterScalingImprovementsPhase1")
          .scope(Scope.GROUP)),
  ATLAS_INDEPENDENT_SHARD_SCALING(
      opts().appSetting("mms.featureFlag.atlasIndependentShardScaling").scope(Scope.GROUP)),
  ATLAS_ASYMMETRIC_SHARD_AUTOSCALING(
      opts().appSetting("mms.featureFlag.atlasAsymmetricShardAutoscaling").scope(Scope.GROUP)),
  ATLAS_PREDICTIVE_AUTOSCALING(
      opts()
          .appSetting("mms.featureFlag.autoscaling.predictive.compute.enabled")
          .scope(Scope.GROUP)),
  ATLAS_PREDICTIVE_AUTOSCALING_M10_M20(
      opts()
          .appSetting("mms.featureFlag.autoscaling.predictive.compute.m10m20enabled")
          .scope(Scope.GROUP)),
  STREAMS_VPC_PEERING(opts().appSetting("mms.featureFlag.streamsVPCPeering").scope(Scope.GROUP)),
  DISABLE_CLOUD_NAV(opts().appSetting("mms.featureFlag.disableCloudNav").scope(Scope.ORGANIZATION)),
  STREAMS_ENABLE_ADDITIONAL_REGIONS(
      opts().appSetting("mms.featureFlag.streamsEnableAdditionalRegions").scope(Scope.GROUP)),
  ATLAS_MONGODB_8_0(opts().appSetting("mms.featureFlag.atlasMongoDB80").scope(Scope.GROUP)),
  ATLAS_MONGODB_8_2(opts().appSetting("mms.featureFlag.atlasMongoDB82").scope(Scope.GROUP)),
  STREAMS_VPC_PEERING_PREVIEW(
      opts().appSetting("mms.featureFlag.streamsVpcPeeringPreview").scope(Scope.GROUP)),
  ENCRYPTION_AT_REST_AZURE_KEY_VAULT_PRIVATE_ENDPOINT(
      opts()
          .appSetting("mms.featureFlag.encryptionAtRestAzureKeyVaultPrivateEndpoint")
          .scope(Scope.GROUP)),
  ENCRYPTION_AT_REST_AWS_KMS_PRIVATE_ENDPOINT(
      opts()
          .appSetting("mms.featureFlag.encryptionAtRestAwsKmsPrivateEndpoint")
          .scope(Scope.GROUP)),
  ENABLE_LOG_REQUEST_TIMEFRAME(
      opts().appSetting("mms.featureFlag.enableLogRequestTimeframe").scope(Scope.GROUP)),
  DISABLE_DISCOVERY_IP_ADDRESS_ALIASES(
      opts().appSetting("mms.featureFlag.disableDiscoveryIpAddressAliases").scope(Scope.GROUP)),
  ENABLED_REVAMPED_TRIGGERS_UI(
      opts().appSetting("mms.featureFlag.enabledRevampedTriggersUi").scope(Scope.GROUP)),
  ENABLE_TYPESCRIPT_IN_FUNCTIONS(
      opts().appSetting("mms.featureFlag.enableTypeScriptInFunctions").scope(Scope.GROUP)),
  DEPRECATE_ENDPOINTS_AND_DATA_API(
      opts().appSetting("mms.featureFlag.deprecateEndpointsAndDataApi").scope(Scope.GROUP)),
  // Allow cross-region and Geosharded clusters to have extended disk sizes
  ALLOW_CROSS_REGION_EXTENDED_STORAGE(
      opts().appSetting("mms.featureFlag.allowCrossRegionExtendedStorage").scope(Scope.GROUP)),
  DISABLE_DARK_READER(
      opts().appSetting("mms.featureFlag.disableDarkReader").scope(Scope.ORGANIZATION)),
  BIC_DEPRECATION(opts().appSetting("mms.featureFlag.bicDeprecation").scope(Scope.GROUP)),
  EXEMPT_FROM_BIC_DEPRECATION(
      opts().appSetting("mms.featureFlag.exemptFromBicDeprecation").scope(Scope.GROUP)),
  ATLAS_MIGRATION_HUB_CUTOVER(
      opts().appSetting("mms.featureFlag.atlasMigrationHubCutover").scope(Scope.GROUP)),
  ATLAS_FCV_DOWNGRADE_ADMIN_ACTION(
      opts().appSetting("mms.featureFlag.atlasFcvDowngradeAdminAction").scope(Scope.GROUP)),
  CUSTOMER_PINNED_FCV_AND_MDB_DOWNGRADE(
      opts().appSetting("mms.featureFlag.customerPinnedFcvAndMdbDowngrade").scope(Scope.GROUP)),
  BIC_DEPRECATION_SHOW_WARNING(
      opts().appSetting("mms.featureFlag.bicDeprecationShowWarning").scope(Scope.GROUP)),
  ATLAS_RESOURCE_POLICIES(
      opts().appSetting("mms.featureFlag.atlasResourcePolicies").scope(Scope.ORGANIZATION)),
  ATLAS_ALLOW_ONE_NODE_DOWN_IN_PACPCM(
      opts().appSetting("mms.featureFlag.atlasAllowOneNodeDownInPacpcm").scope(Scope.GROUP)),
  ATLAS_ALLOW_SEVERAL_NODES_DOWN_IN_PACPCM(
      opts().appSetting("mms.featureFlag.atlasAllowSeveralNodesDownInPacpcm").scope(Scope.GROUP)),
  ATLAS_AUTOMATION_SHORTER_QUIESCE_TIME(
      opts().appSetting("mms.featureFlag.atlasAutomationShorterQuiesceTime").scope(Scope.GROUP)),
  ATLAS_AUTOMATION_SPECIFY_FORCE_TRUE_FOR_SHUTDOWNS(
      opts()
          .appSetting("mms.featureFlag.atlasAutomationSpecifyForceTrueForShutdowns")
          .scope(Scope.GROUP)),
  ATLAS_ALLOW_UPSCALING_TO_CLEANLY_ABANDON_PLANS(
      opts()
          .appSetting("mms.featureFlag.atlasAllowUpscalingToCleanlyAbandonPlans")
          .scope(Scope.GROUP)),
  PAYMENTS_STANDALONE_CALLS_REVREC(
      opts()
          .appSetting("mms.featureFlag.payments.standaloneCalls.revRec")
          .scope(Scope.ORGANIZATION)),
  ORG_LEVEL_GEN_AI_CONTROL_SWITCH(
      opts()
          .appSetting("mms.featureFlag.orgLevelGenAiControlSwitch")
          .scope(Scope.ORGANIZATION)
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)),
  ATLAS_GCP_HYPERDISK_EXTREME(
      opts().appSetting("mms.featureFlag.atlasGcpHyperdiskExtreme").scope(Scope.GROUP)),
  RTPP_DISK_THROUGHPUT_METRICS(
      opts().appSetting("mms.featureFlag.RtppDiskThroughputMetrics").scope(Scope.GROUP)),
  ATLAS_FCV_PINNING_IN_UI(
      opts().appSetting("mms.featureFlag.atlasFcvPinningInUi").scope(Scope.GROUP)),
  ATLAS_KMIP_KEY_ROTATION_IN_MAINTENANCE_WINDOW(
      opts()
          .appSetting("mms.featureFlag.atlasKmipKeyRotationInMaintenanceWindows")
          .scope(Scope.GROUP)),
  AGENT_ROTATES_ATLAS_PROXY_LOGS(
      opts().appSetting("mms.featureFlag.agentRotatesAtlasProxyLogs").scope(Scope.GROUP)),
  LEGACY_SHARED_API_SHIM_TO_FLEX(
      opts().appSetting("mms.featureFlag.flex.legacySharedApiShim.enabled").scope(Scope.GROUP)),
  LEGACY_SERVERLESS_API_SHIM_TO_FLEX(
      opts().appSetting("mms.featureFlag.flex.legacyServerlessApiShim.enabled").scope(Scope.GROUP)),
  FASTER_FREE_FLEX_TIER_PROVISIONING(
      opts().appSetting("mms.featureFlag.fasterFreeFlexTierProvisioning").scope(Scope.GROUP)),
  AL2023_AWS_OS(opts().appSetting("mms.featureFlag.al2023AWSOs").scope(Scope.GROUP)),
  AWS_AL2023_FORCE_MIGRATE_NVME(
      opts().appSetting("mms.featureFlag.awsAL2023ForceMigrateNVMe").scope(Scope.GROUP)),
  AL2023_AZURE_OS(opts().appSetting("mms.featureFlag.al2023AzureOs").scope(Scope.GROUP)),
  AZURE_AL2023_FORCE_MIGRATE_NVME(
      opts().appSetting("mms.featureFlag.azureAL2023ForceMigrateNVMe").scope(Scope.GROUP)),
  AL2023_GCP_OS(opts().appSetting("mms.featureFlag.al2023GCPOs").scope(Scope.GROUP)),
  ATLAS_ALLOW_ENFORCE_MIN_TLS_13(
      opts().appSetting("mms.featureFlag.allowEnforceMinTls13").scope(Scope.GROUP)),
  ATLAS_TLS_13_AUTO_UPGRADE_ENABLED(
      opts().appSetting("mms.featureFlag.atlasTls13AutoUpgradeEnabled").scope(Scope.GROUP)),
  ATLAS_DEPRECATE_TLS_10_11(
      opts().appSetting("mms.featureFlag.atlasDeprecateTls10AndTls11").scope(Scope.GROUP)),
  VALIDATE_S3_CHUNKS_ON_DOWNLOAD(
      opts().appSetting("mms.featureFlag.validateS3ChunksOnDownload").scope(Scope.GROUP)),
  ENABLE_DATA_EXPLORER_GEN_AI_FEATURES(
      opts()
          .appSetting("mms.featureFlag.dataExplorerGenAIFeatures")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),
  ENABLE_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING(
      opts()
          .appSetting("mms.featureFlag.dataExplorerGenAISampleDocument")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),
  DISABLE_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING(
      opts()
          .appSetting("mms.featureFlag.disableDataExplorerGenAISampleDocumentPassing")
          .enabledByDefaultForPlanTypes(PlanTypeSet.NDS)
          .scope(Scope.GROUP)),
  ATLAS_EXCLUDE_REGION_USEAST2_FROM_AZURE_DSV5_ESV5_FAMILIES(
      opts()
          .appSetting("mms.featureFlag.atlasExcludeRegionUseast2FromAzureDsv5Esv5Families")
          .scope(Scope.GROUP)),
  CUSTOM_SESSION_TIMEOUTS(
      opts().appSetting("mms.featureFlag.customSessionTimeouts").scope(Scope.ORGANIZATION)),
  RESOURCE_TAG_SCHEMA_REVISION(
      opts().appSetting("mms.featureFlag.resourceTagSchemaRevision").scope(Scope.ORGANIZATION)),
  COMMUNICATION_SERVICE_STREAMING(
      opts().appSetting("mms.featureFlag.commServiceStreaming").scope(Scope.ORGANIZATION)),
  PROJECT_EVENTS_EVENT_SERVICE_API(
      opts().appSetting("mms.featureFlag.projectEventsApiEventService").scope(Scope.GROUP)),
  ORG_EVENTS_EVENT_SERVICE_API(
      opts().appSetting("mms.featureFlag.orgEventsApiEventService").scope(Scope.ORGANIZATION)),
  ATLAS_AUTOMATE_PRIORITY_TAKEOVER(
      opts().appSetting("mms.featureFlag.atlasAutomatePriorityTakeover").scope(Scope.GROUP)),
  AWS_USE_IPAM_IP(opts().appSetting("mms.featureFlag.awsUseIpamIp").scope(Scope.GROUP)),
  ATLAS_STREAMS_AWS_PROXY_NODES_AL2023_ENABLED(
      opts().appSetting("mms.featureFlag.streamsAWSProxyAL2023Enabled").scope(Scope.GROUP)),
  ATLAS_STREAMS_AZURE_PROXY_NODES_AL2023_ENABLED(
      opts().appSetting("mms.featureFlag.streamsAzureProxyAL2023Enabled").scope(Scope.GROUP)),
  ATLAS_SEARCH_DEDICATED_NODES_GRPC_MODE(
      opts().appSetting("mms.featureFlag.atlasSearch.dedicatedNodesGrpcMode").scope(Scope.GROUP)),
  ENABLE_API_TELEMETRY_CUSTOM_FIELDS(
      opts()
          .appSetting("mms.featureFlag.apix.enableApiTelemetryCustomFields")
          .scope(Scope.ORGANIZATION)),
  GLOBAL_SERVICE_ACCOUNT_MANAGEMENT(
      opts()
          .appSetting("mms.featureFlag.apix.globalServiceAccountManagement")
          .scope(Scope.ORGANIZATION)),
  RATELIMIT_API_MIDDLEWARE(
      opts().appSetting("mms.featureFlag.apix.ratelimit.apiMiddleware").scope(Scope.ORGANIZATION)),
  AWS_CAPACITY_AWARE_AZ_SELECTION(
      opts().appSetting("mms.featureFlag.awsCapacityAwareAZSelection").scope(Scope.GROUP)),
  AZURE_CAPACITY_AWARE_AZ_SELECTION(
      opts().appSetting("mms.featureFlag.azureCapacityAwareAZSelection").scope(Scope.GROUP)),
  GCP_CAPACITY_AWARE_AZ_SELECTION(
      opts().appSetting("mms.featureFlag.gcpCapacityAwareAZSelection").scope(Scope.GROUP)),
  CAPACITY_AWARE_PREVENTATIVE_ROLLBACK_SUPPORT(
      opts()
          .appSetting("mms.featureFlag.capacityAwareAZPreventativeRollbackSupport")
          .scope(Scope.GROUP)),
  SERVERLESS_TO_FREE_MIGRATION(
      opts().appSetting("mms.featureFlag.serverlessToFreeMigration").scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_STALE_STATE_TRANSITION(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.enableStaleStateTransition")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_RETAIN_FAILED_INDEX_DATA_ON_DISK(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.retainFailedIndexDataOnDisk")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_REMOVE_ABSENT_INDEXES_BEFORE_INITIALIZATION(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.removeAbsentIndexesBeforeInitialization")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_AL2023_AWS_OS(
      opts().appSetting("mms.featureFlag.atlasSearch.al2023AWSOs").scope(Scope.GROUP)),
  ATLAS_SEARCH_AL2023_GCP_OS(
      opts().appSetting("mms.featureFlag.atlasSearch.al2023GCPOs").scope(Scope.GROUP)),
  ATLAS_SEARCH_AL2023_AZURE_OS(
      opts().appSetting("mms.featureFlag.atlasSearch.al2023AzureOs").scope(Scope.GROUP)),
  ATLAS_SEARCH_ENFORCE_MAX_INDEX_SIZE(
      opts().appSetting("mms.featureFlag.atlasSearch.enforceMaxIndexSize").scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_CONF_CALL_GZIP_COMPRESSION(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.enableConfCallGzipCompression")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_FACETING_OVER_TOKEN_FIELDS(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.enableFacetingOverTokenFields")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_NEW_EMBEDDED_SEARCH_CAPABILITIES(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.enableNewEmbeddedSearchCapabilities")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_INDEX_PARTITIONING_LOOSE_RESTRICTION(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.indexPartitioningLooseRestriction")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_INDEX_PARTITIONING_COLOCATED(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.indexPartitioningOnColocated")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_DISABLE_STATS_WRITES_TO_INDEX_CONFIG(
      opts()
          .appSetting("mms.featureFlag.atlasSearch.disableStatsWritesToIndexConfig")
          .scope(Scope.GROUP)),
  ATLAS_SEARCH_IGNORE_ATLAS_THROTTLING(
      opts().appSetting("mms.featureFlag.atlasSearch.ignoreAtlasThrottling").scope(Scope.GROUP)),
  ATLAS_SEARCH_ENABLE_SEARCH_THROTTLING(
      opts().appSetting("mms.featureFlag.atlasSearch.enableSearchThrottling").scope(Scope.GROUP)),
  ATLAS_ENCRYPTION_AT_REST_PRIVATE_NETWORKING_UI(
      opts().appSetting("mms.featureFlag.encryptionAtRestPrivateNetworkingUI").scope(Scope.GROUP)),
  AZURE_NATIVE_USAGE_REPORTING(
      opts()
          .appSetting("mms.featureFlag.payments.partners.azureNativeUsageReporting")
          .scope(Scope.ORGANIZATION)),
  SERVERLESS_UPGRADE_TO_DEDICATED_DEBUG_MODE(
      opts()
          .appSetting("mms.featureFlag.serverlessUpgradeToDedicatedDebugMode")
          .scope(Scope.GROUP)),
  DISAGGREGATED_STORAGE_ATLAS(
      opts().appSetting("mms.featureFlag.disaggregatedStorageAtlas").scope(Scope.GROUP)),
  GCP_ARM_AXION(opts().appSetting("mms.featureFlag.gcpArmAxion").scope(Scope.GROUP)),
  AZURE_ARM_DPSV6_EPSV6(opts().appSetting("mms.featureFlag.azureArmDpsv6Epsv6").scope(Scope.GROUP)),
  HORIZONTAL_SCALING_AUTO_SHARDING(
      opts().appSetting("mms.featureFlag.horizontalScalingAutoSharding").scope(Scope.GROUP)),
  ATLAS_LIVE_MIGRATE_ENFORCE_DESTINATION_WRITE_BLOCKING(
      opts()
          .appSetting("mms.featureFlag.atlasLiveMigrateEnforceDestinationWriteBlocking")
          .scope(Scope.GROUP)),
  ATLAS_LIVE_MIGRATE_ENFORCE_SOURCE_AND_DESTINATION_WRITE_BLOCKING(
      opts()
          .appSetting("mms.featureFlag.atlasLiveMigrateEnforceSourceAndDestinationWriteBlocking")
          .scope(Scope.GROUP)),
  START_LIVE_MIGRATION_IN_MIGRATION_HUB(
      opts().appSetting("mms.featureFlag.startLiveMigrationInMigrationHub").scope(Scope.GROUP)),
  ENTERPRISE_LANDING_PAGE(
      opts().appSetting("mms.featureFlag.enterpriseLandingPage").scope(Scope.ORGANIZATION)),
  SANDBOX_ELIGIBLE(
      opts().appSetting("mms.featureFlag.enterpriseSandbox").scope(Scope.ORGANIZATION)),
  DATA_EXFILTRATION_SIDECAR_ENABLED(
      opts().appSetting("mms.featureFlag.dataExfilPreventionSidecarEnabled").scope(Scope.GROUP)),
  DATA_EXFILTRATION_SIDECAR_TO_PROXY_ROUTING_ENABLED(
      opts()
          .appSetting("mms.featureFlag.dataExfilPreventionSidecarToProxyRoutingEnabled")
          .scope(Scope.GROUP)),
  DATA_EXFILTRATION_MANAGE_SECURITY_GROUPS_ENABLED(
      opts()
          .appSetting("mms.featureFlag.dataExfiltrationManageSecurityGroupsEnabled")
          .scope(Scope.GROUP)),
  DATA_EXFILTRATION_SECURITY_GROUPS_EXCLUDE_ALLOW_ALL_RULE_ENABLED(
      opts()
          .appSetting("mms.featureFlag.dataExfiltrationSecurityGroupsExcludeAllowAllRuleEnabled")
          .scope(Scope.GROUP)),
  DATA_EXFILTRATION_PREFIX_LIST_CRON_JOBS_ENABLED(
      opts()
          .appSetting("mms.featureFlag.dataExfilPreventionPrefixListCronJobsEnabled")
          .scope(Scope.GROUP)),
  ATLAS_LIVE_MIGRATE_MONGOSYNC_EIGHT_ZERO_SUPPORT(
      opts()
          .appSetting("mms.featureFlag.atlasLiveMigrateMongosyncEightZeroSupport")
          .scope(Scope.GROUP)),
  /**
   * a noop feature flag intended to serve as a graceful stand in for unknown variants when
   * deserializing data from storage
   */
  UNKNOWN(opts().appSetting("mms.featureFlag.unknown").scope(Scope.GROUP)),
  AWS_KMS_VALIDATION_WORKLOAD_IDENTITY(
      opts().appSetting("mms.featureFlag.awsKmsValidationWorkloadIdentity").scope(Scope.GROUP)),
  ATLAS_ALLOW_ATLAS_ADMIN_FOUR_FOUR_PRIVILEGES(
      opts()
          .appSetting("mms.featureFlag.atlasAllowAtlasAdminFourFourPrivileges")
          .scope(Scope.GROUP)),
  ATLAS_AUTOHEAL_RESYNC_IMPROVEMENTS(
      opts().appSetting("mms.featureFlag.atlasAutohealResyncImprovements").scope(Scope.GROUP)),
  ATLAS_AUTOHEAL_REMOVE_ICMP_PING(
      opts().appSetting("mms.featureFlag.atlasAutohealRemoveIcmpPing").scope(Scope.GROUP)),
  GCP_N4_FAMILY(opts().appSetting("mms.featureFlag.gcpN4Family").scope(Scope.GROUP)),
  ATLAS_ACKNOWLEDGES_API_KEY_FROM_CHEF_CONF_CALL(
      opts()
          .appSetting("mms.featureFlag.atlasAcknowledgesApiKeyFromChefConfCall")
          .scope(Scope.GROUP)),
  PRIVATE_LINK_PROXY_PROTOCOL_AWS(
      opts().appSetting("mms.featureFlag.privateLinkProxyProtocolAws").scope(Scope.GROUP)),
  ENABLE_PARCA_AGENT(opts().appSetting("mms.featureFlag.enableParcaAgent").scope(Scope.GROUP)),
  SIMPLIFIED_ATLAS_DBUSER_PRIVILEGES(
      opts().appSetting("mms.featureFlag.simplifiedAtlasDBUserPrivileges").scope(Scope.GROUP)),
  METRICS_PROXY_SERVER_RECEIVE_METRICS(
      opts()
          .appSetting("mms.featureFlag.metricsProxyServerSettings.receiveMetrics")
          .scope(Scope.GROUP)),
  DEDICATED_HARDWARE_METRICS_SEND_TO_MAAS(
      opts().appSetting("mms.featureFlag.dedicatedHardwareMetrics.sendToMaaS").scope(Scope.GROUP)),
  DEDICATED_MONGO_METRICS_SEND_TO_MAAS(
      opts().appSetting("mms.featureFlag.dedicatedMongoMetrics.sendToMaaS").scope(Scope.GROUP)),
  MAAS_METRICS_CHARTS(
      opts().appSetting("mms.featureFlag.monitoring.maasMetricsCharts").scope(Scope.GROUP)),
  EAR_GCP_KMS_SECRETLESS_AUTH(
      opts().appSetting("mms.featureFlag.earGcpKmsSecretlessAuth").scope(Scope.GROUP)),
  EAR_GCP_KMS_ROLE_BASED_AUTH(
      opts().appSetting("mms.featureFlag.earGcpKmsRoleBasedAuth").scope(Scope.ORGANIZATION)),
  DATA_EXPLORER_SAVES_USER_DATA(
      opts().appSetting("mms.featureFlag.dataExplorerSavesUserData").scope(Scope.GROUP)),
  DATA_EXPLORER_ENABLE_AI_ASSISTANT(
      opts().appSetting("mms.featureFlag.enableAIAssistant").scope(Scope.GROUP)),
  QUERY_STATS_PA(opts().appSetting("mms.featureFlag.queryStatsPA").scope(Scope.GROUP)),
  ATLAS_ALWAYS_MANAGED_DEFAULT_RW_CONCERN(
      opts().appSetting("mms.featureFlag.atlasAlwaysManagedDefaultRwConcern").scope(Scope.GROUP)),
  DATA_VALIDATION_BINARY(
      opts().appSetting("mms.featureFlag.atlasDataValidationBinary").scope(Scope.GROUP)),
  MONITORING_MONGOTUNE_COLLECTOR_ENABLED(
      opts()
          .appSetting("mms.featureFlag.monitoring.mongotuneCollector.enabled")
          .scope(Scope.GROUP)),
  MONITORING_CANARY_COLLECTOR_ENABLED(
      opts().appSetting("mms.featureFlag.monitoring.canaryCollector.enabled").scope(Scope.GROUP)),
  ATLAS_TUNED_INSTANCE_INDEX_BUILD_LIMITS(
      opts().appSetting("mms.featureFlag.atlasTunedInstanceIndexBuildLimits").scope(Scope.GROUP)),
  AWS_GRAVITON4(opts().appSetting("mms.featureFlag.awsGraviton4").scope(Scope.GROUP)),
  AWS_GRAVITON4_NVME(opts().appSetting("mms.featureFlag.awsGraviton4NVMe").scope(Scope.GROUP)),
  PROMETHEUS_OVER_PRIVATE_LINK(
      opts().appSetting("mms.featureFlag.prometheusOverPrivateLink").scope(Scope.GROUP)),
  ATLAS_SQL_CLUSTER_SCHEMA_INTERFACE(
      opts().appSetting("mms.featureFlag.atlasSql.clusterSchemaInterface").scope(Scope.GROUP)),
  ATLAS_AUTOHEAL_REDUCE_SHUTDOWN_TIME(
      opts().appSetting("mms.featureFlag.atlasAutohealReduceShutdownTime").scope(Scope.GROUP)),
  CONNECTION_ESTABLISHMENT_RATE_LIMITING(
      opts().appSetting("mms.featureFlag.connectionEstablishmentRateLimiting").scope(Scope.GROUP)),
  ATLAS_AUTOHEAL_REDUCE_SHUTDOWN_TIME_HEAL_REPAIR(
      opts()
          .appSetting("mms.featureFlag.atlasAutohealReduceShutdownTimeHealRepair")
          .scope(Scope.GROUP)),
  PRIVATE_LINK_MAX_INCOMING_CONNECTIONS_MULTIPLIER(
      opts()
          .appSetting("mms.featureFlag.privateLinkMaxIncomingConnectionsMultiplier")
          .scope(Scope.GROUP)),
  ATLAS_OPTIMIZED_INITIAL_SYNC_ACROSS_REGIONS(
      opts().appSetting("mms.featureFlag.atlasOisAcrossRegions").scope(Scope.GROUP)),
  ATLAS_TURN_DISK_WARMING_OFF_FOR_AZURE_LEGACY(
      opts()
          .appSetting("mms.featureFlag.atlasTurnDiskWarmingOffForAzureLegacy")
          .scope(Scope.GROUP)),
  ATLAS_AUTOHEAL_REDUCED_THRESHOLD(
      opts().appSetting("mms.featureFlag.atlasAutohealReducedThreshold").scope(Scope.GROUP)),
  ATLAS_AZURE_EXCLUDE_CONSTRAINED_COMBOS(
      opts().appSetting("mms.featureFlag.atlasAzureExcludeConstrainedCombos").scope(Scope.GROUP)),
  PUBLIC_API_MAAS_SHADOW_TESTING_ENABLED(
      opts().appSetting("mms.featureFlag.publicApi.maasShadowTesting.enabled").scope(Scope.GROUP));

  private static final Counter UNKNOWN_VARIANTS =
      new Counter.Builder()
          .name("feature_flag_unknown_variants_total")
          .help("number of times we've failed to deserialize a variant from storage")
          .labelNames("name")
          .register();

  public static final List<FeatureFlag> EDITABLE_FEATURE_FLAGS =
      List.of(
          ATLAS_HIDE_QUICK_ACCESS_PAGE,
          DATA_EXPLORER,
          PERFORMANCE_ADVISOR,
          REALTIME,
          SCHEMA_ADVISOR,
          MANAGED_SLOW_MS,
          CUSTOMER_QUERY_SHAPE_INSIGHTS_TOGGLE,
          ENABLE_DATA_EXPLORER_GEN_AI_FEATURES,
          ENABLE_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING,
          DISABLE_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING,
          DATA_EXPLORER_COMPASS_WEB_USER_CONTROLLED_DISABLE);
  public static final List<FeatureFlag> OPERATOR_FEATURE_FLAGS =
      List.of(
          DISABLE_SCRAM_SHA1_AUTH,
          ATLAS_ALLOW_DEPRECATED_VERSIONS,
          ATLAS_LIVE_MIGRATE_SHARDED_HORIZONS,
          ATLAS_AZURE_FORCE_MIGRATION_TO_AZS,
          AWS_INTEL_OVER_GRAVITON,
          CPS_BACKUP_LOCK_MVP,
          M10_SHARDED_CLUSTERS,
          ENABLE_SCRAM_SHA256_AUTH,
          ATLAS_OPT_OUT_PERIODIC_CORRUPTION_DETECTION,
          ATLAS_INDEPENDENT_SHARD_SCALING,
          ALLOW_CROSS_REGION_EXTENDED_STORAGE,
          SERVERLESS_SHARED_UI_OPTION_ENABLED,
          ATLAS_PREDICTIVE_AUTOSCALING,
          ATLAS_PREDICTIVE_AUTOSCALING_M10_M20,
          ENABLE_MONGOTUNE,
          ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY,
          AWS_CAPACITY_AWARE_AZ_SELECTION,
          GCP_CAPACITY_AWARE_AZ_SELECTION,
          AZURE_CAPACITY_AWARE_AZ_SELECTION,
          ATLAS_TUNED_INSTANCE_INDEX_BUILD_LIMITS,
          CAPACITY_AWARE_PREVENTATIVE_ROLLBACK_SUPPORT,
          ATLAS_TURN_DISK_WARMING_OFF_FOR_AZURE_LEGACY,
          ATLAS_AZURE_EXCLUDE_CONSTRAINED_COMBOS);
  public static final Set<FeatureFlag> NON_CONFIG_SERVICE_FEATURE_FLAGS =
      Set.of(
          DISABLE_SCRAM_SHA1_AUTH,
          ENABLE_SCRAM_SHA256_AUTH,
          ATLAS_STREAMS_AWS_PROXY_NODES_AL2023_ENABLED,
          ATLAS_STREAMS_AZURE_PROXY_NODES_AL2023_ENABLED,
          ATLAS_HIDE_QUICK_ACCESS_PAGE,
          DATA_EXPLORER,
          PERFORMANCE_ADVISOR,
          REALTIME,
          SCHEMA_ADVISOR,
          MANAGED_SLOW_MS,
          CUSTOMER_QUERY_SHAPE_INSIGHTS_TOGGLE,
          ATLAS_ALLOW_DEPRECATED_VERSIONS,
          ATLAS_LIVE_MIGRATE_SHARDED_HORIZONS,
          ATLAS_AZURE_FORCE_MIGRATION_TO_AZS,
          AWS_INTEL_OVER_GRAVITON,
          CPS_BACKUP_LOCK_MVP,
          M10_SHARDED_CLUSTERS,
          ATLAS_OPT_OUT_PERIODIC_CORRUPTION_DETECTION,
          ATLAS_INDEPENDENT_SHARD_SCALING,
          ATLAS_AUTOMATIC_EMBEDDED_CONFIG_TRANSITIONS,
          CPS_EMBEDDED_CONFIG,
          ATLAS_DISK_WARMING_SUPPORT,
          ATLAS_AZURE_DSV5_AND_ESV5_INSTANCE_FAMILIES,
          REALM_SYNC_BETA,
          ATLAS_SEARCH_QUERY_TELEMETRY,
          ENCRYPTION_AT_REST_AZURE_KEY_VAULT_PRIVATE_ENDPOINT,
          ENCRYPTION_AT_REST_AWS_KMS_PRIVATE_ENDPOINT,
          ATLAS_MOUNT_AZURE_DISK_WITH_LUN,
          ATLAS_PRIORITIZE_HAVING_PRIMARY_ON_UPSCALED_NODE,
          AUTOMATION_CONFIG_PUBLISH_CHANGE_EVENT,
          AUTOMATION_CHANGE_LISTENER,
          CLASSIC_API_ACCESS,
          CLASSIC_CHARTS,
          ATLAS_AZURE_SSD_PV2_WAVE2,
          SERVERLESS_AUTO_INDEXING,
          CPS_OPLOG_IN_AZURE,
          CLUSTER_CENTRIC_QUERY_PROFILER,
          VQP_BINNING,
          CONTINUOUS_BACKUP_ALLOWED_FOR_NEW_AWS_CLUSTERS,
          CPS_GCP_AND_AZURE_NEW_CLUSTERS_ONLY_CPS,
          DEDICATED_ATLAS_SEARCH_NODES,
          ATLAS_FORCE_SKIP_FAST_PROVISION,
          ATLAS_SEARCH_RESOLVE_VIEWS,
          ONLINE_ARCHIVE_IGNORE_DATA_SIZE_THRESHOLD,
          ONLINE_ARCHIVE_V1,
          AWS_GRAVITON,
          AUTOMATION_MONGO_DEVELOPMENT_VERSIONS,
          ATLAS_USE_LEGACY_HOSTNAME_SCHEME,
          ATLAS_CN_REGIONS_ONLY,
          SERVICE_ACCOUNT_MANAGEMENT_ENABLED,
          BACKUP_PARALLEL_RESTORES,
          BACKUP_DIRECT_S3_RESTORE,
          ATLAS_LIVE_IMPORT_HELIX_PULL,
          ATLAS_DATA_LAKE_STORAGE_FORCE_V2_UPGRADE_FOR_ONLINE_ARCHIVE,
          PROMETHEUS_INTEGRATION,
          RTPP_DISK_THROUGHPUT_METRICS,
          ATLAS_GCP_HYPERDISK_EXTREME,
          ATLAS_FCV_PINNING_IN_UI,
          ATLAS_CHAIN_PAUSE_MOVES,
          CPS_SYSTEM_PROJECTS_FOR_DATA_LAKE_INGESTION_PIPELINE_EXPORTS,
          ON_DEMAND_SNAPSHOT,
          ATLAS_ALLOW_UPSCALING_TO_CLEANLY_ABANDON_PLANS,
          SALES_SOLD_WARNING_ORG_PAYMENT_STATUS,
          ATLAS_ASYMMETRIC_SHARD_AUTOSCALING,
          ATLAS_LIVE_MIGRATE_MONGOSYNC_ONE_NINE_PREVIEW_SUPPORT,
          ATLAS_LIVE_MIGRATE_MONGOSYNC_EMBEDDED_VERIFIER,
          ATLAS_LIVE_MIGRATE_DESTINATION_OPLOG_VALIDATION_FOR_VERIFIER,
          CPS_PV2_STREAMING_RESTORE,
          CPS_SYSTEM_PROJECTS_FOR_EXPORTS,
          CPS_SNAPSHOT_CONSISTENT_EXPORT_SPLIT_LARGE_COLLECTIONS,
          CPS_OPTIMIZED_DA_RESTORE_AWS,
          CPS_OPTIMIZED_DA_RESTORE_AZURE,
          CPS_OPTIMIZED_DA_RESTORE_GCP,
          CPS_QUERY_RESTORE_STATUS_FOR_DISK_SWAP_AWS,
          CPS_QUERY_RESTORE_STATUS_FOR_DISK_SWAP_AZURE,
          CPS_QUERY_RESTORE_STATUS_FOR_DISK_SWAP_GCP,
          CPS_AWS_DA_RESTORE_WITH_IO2,
          PAYMENTS_STANDALONE_CALLS_REVREC,
          ALLOW_CROSS_REGION_EXTENDED_STORAGE,
          ENABLE_RAMI_AGENT,
          ENABLE_MONGOTUNE,
          ENABLE_MONGOTUNE_WRITE_BLOCK_POLICY,
          AL2023_AWS_OS,
          AWS_AL2023_FORCE_MIGRATE_NVME,
          AL2023_AZURE_OS,
          AZURE_AL2023_FORCE_MIGRATE_NVME,
          AL2023_GCP_OS,
          ATLAS_ALLOW_ENFORCE_MIN_TLS_13,
          ATLAS_TLS_13_AUTO_UPGRADE_ENABLED,
          ATLAS_DEPRECATE_TLS_10_11,
          ATLAS_AZURE_SSD_FORCE_PV1,
          CPS_RESTORE_CROSS_PROJECT_AWS_MIGRATION,
          ATLAS_CONFIGURABLE_GP3_IOPS,
          ATLAS_DATA_FEDERATION_ON_GCP,
          ATLAS_CLOUD_PROVIDER_ACCESS_FOR_GCP,
          CPS_OPLOG_IN_GCP,
          ATLAS_MONGOMIRROR_LIVE_IMPORT_HELIX,
          ATLAS_LIVE_IMPORT_LARGE_NODE_POOL,
          CHARTS_ACTIVATION_OPTIMIZATION,
          ATLAS_SEARCH_INDEX_MANAGEMENT,
          ATLAS_LIVE_MIGRATE_MONGOSYNC_VARIABLE_SHARD_COUNT,
          ATLAS_LIVE_MIGRATE_MONGOSYNC_OLDER_VERSION_SUPPORT,
          ATLAS_DATA_FEDERATION_DEDICATED_HOSTNAMES,
          VALIDATE_S3_CHUNKS_ON_DOWNLOAD,
          ATLAS_ALLOW_ONE_NODE_DOWN_IN_PACPCM,
          ATLAS_ALLOW_SEVERAL_NODES_DOWN_IN_PACPCM,
          ATLAS_AUTOMATION_SPECIFY_FORCE_TRUE_FOR_SHUTDOWNS,
          ATLAS_AUTOMATION_SHORTER_QUIESCE_TIME,
          FINE_GRAINED_AUTH,
          FINE_GRAINED_AUTH_USER_GROUPS,
          UI_ACCESS_LIST,
          CPS_RESTORE_CROSS_PROJECT_AWS,
          CPS_RESTORE_CROSS_PROJECT_AWS_NEW_CMK,
          CPS_RESTORE_CROSS_PROJECT_GCP,
          CPS_RESTORE_CROSS_PROJECT_AZURE,
          ENABLE_DATA_EXPLORER_GEN_AI_FEATURES,
          ENABLE_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING,
          DISABLE_DATA_EXPLORER_GEN_AI_SAMPLE_DOCUMENT_PASSING,
          ATLAS_EXCLUDE_REGION_USEAST2_FROM_AZURE_DSV5_ESV5_FAMILIES,
          CPS_SNAPSHOT_AWS_PRIVATE_DOWNLOAD,
          CPS_SNAPSHOT_AZURE_PRIVATE_DOWNLOAD,
          ONLINE_ARCHIVE_V3_TIME_SERIES,
          REALM_METRICS,
          ATLAS_AZURE_SSD_PV2,
          CPS_SNAPSHOT_DISTRIBUTION_AWS,
          CPS_SNAPSHOT_DISTRIBUTION_GCP,
          CPS_SNAPSHOT_DISTRIBUTION_AZURE,
          EXTEND_MAX_ALLOWED_DISK_SIZES,
          ATLAS_DATA_REGIONALIZATION_ENABLED,
          CPS_OPLOG_MIGRATION,
          ALLOW_AWS_PREVIEW_REGIONS,
          ALLOW_AZURE_PREVIEW_REGIONS,
          ALLOW_GCP_PREVIEW_REGIONS,
          ATLAS_SERVERLESS_GRPC_SUPPORT,
          SERVERLESS_GRPC_INCREMENTAL_ROLLOUT_GLOBAL_ENABLED,
          SERVERLESS_GRPC_INCREMENTAL_ROLLOUT_TENANT_ENABLED,
          SERVERLESS_MTM_ELIGIBLE_FOR_UPGRADE_TO_DEDICATED,
          MAX_AGGREGATED_DISK_CHARTS_FOR_ATLAS,
          BACKUP_S3_OPLOG_STORE_IN_OM,
          ATLAS_AUTOMATE_PRIORITY_TAKEOVER,
          ATLAS_SEARCH_DEDICATED_NODES_GRPC_MODE,
          ATLAS_ADVANCED_REGIONALIZED_PRIVATE_ENDPOINTS,
          ATLAS_RESOURCE_POLICIES_EXTENSION_WITH_UI,
          SERVERLESS_SHARED_UI_OPTION_ENABLED,
          SERVERLESS_UPGRADE_TO_DEDICATED_DEBUG_MODE,
          ATLAS_PREDICTIVE_AUTOSCALING,
          GCP_ARM_AXION,
          AZURE_ARM_DPSV6_EPSV6,
          HORIZONTAL_SCALING_AUTO_SHARDING,
          ATLAS_PREDICTIVE_AUTOSCALING_M10_M20,
          ENTERPRISE_LANDING_PAGE,
          SANDBOX_ELIGIBLE,
          UNKNOWN,
          ATLAS_AUTOHEAL_RESYNC_IMPROVEMENTS,
          ATLAS_AUTOHEAL_REMOVE_ICMP_PING,
          GCP_N4_FAMILY,
          DATA_EXPLORER_COMPASS_WEB_USER_CONTROLLED_DISABLE,
          ATLAS_ACKNOWLEDGES_API_KEY_FROM_CHEF_CONF_CALL,
          PRIVATE_LINK_PROXY_PROTOCOL_AWS,
          PRIVATE_LINK_MAX_INCOMING_CONNECTIONS_MULTIPLIER,
          ATLAS_ALLOW_ATLAS_ADMIN_FOUR_FOUR_PRIVILEGES,
          ENABLE_PARCA_AGENT,
          AWS_CAPACITY_AWARE_AZ_SELECTION,
          GCP_CAPACITY_AWARE_AZ_SELECTION,
          CAPACITY_AWARE_PREVENTATIVE_ROLLBACK_SUPPORT,
          AZURE_CAPACITY_AWARE_AZ_SELECTION,
          ATLAS_ALWAYS_MANAGED_DEFAULT_RW_CONCERN,
          ATLAS_TUNED_INSTANCE_INDEX_BUILD_LIMITS,
          DATA_EXFILTRATION_SIDECAR_ENABLED,
          DATA_EXFILTRATION_SIDECAR_TO_PROXY_ROUTING_ENABLED,
          DATA_EXFILTRATION_MANAGE_SECURITY_GROUPS_ENABLED,
          DATA_EXFILTRATION_SECURITY_GROUPS_EXCLUDE_ALLOW_ALL_RULE_ENABLED,
          AWS_GRAVITON4,
          AWS_GRAVITON4_NVME,
          ATLAS_AUTOHEAL_REDUCE_SHUTDOWN_TIME,
          CONNECTION_ESTABLISHMENT_RATE_LIMITING,
          ATLAS_AUTOHEAL_REDUCE_SHUTDOWN_TIME_HEAL_REPAIR,
          ATLAS_TURN_DISK_WARMING_OFF_FOR_AZURE_LEGACY,
          ATLAS_AUTOHEAL_REDUCED_THRESHOLD,
          ATLAS_AZURE_EXCLUDE_CONSTRAINED_COMBOS);

  final Options _options;

  FeatureFlag(final Options pOptions) {
    _options = pOptions;
  }

  public static List<FeatureFlag> getConfigServiceFeatureFlags() {
    return Arrays.stream(values())
        .filter(f -> !NON_CONFIG_SERVICE_FEATURE_FLAGS.contains(f))
        .toList();
  }

  public static Optional<FeatureFlag> findByName(final String pName) {
    return Arrays.stream(values()).filter(n -> n.name().equals(pName)).findFirst();
  }

  public static Optional<FeatureFlag> findByAppSetting(final String pAppSetting) {
    return Arrays.stream(values())
        .filter(n -> n.getEnvironmentProperty().equals(pAppSetting))
        .findFirst();
  }

  /**
   * Util method that attempts to find a Feature Flag by app setting value ignoring case. This
   * method is used in some test classes where feature flag names may be formed dynamically based on
   * other properties e.g. cloud provider
   *
   * @param pAppSetting the app setting value used to look up the feature flag
   * @return an optional feature flag if found
   */
  @VisibleForTesting(otherwise = AccessModifier.PRIVATE)
  public static Optional<FeatureFlag> findByAppSettingIgnoreCase(final String pAppSetting) {
    return Arrays.stream(values())
        .filter(n -> n.getEnvironmentProperty().equalsIgnoreCase(pAppSetting))
        .findFirst();
  }

  private static Options opts() {
    return new Options();
  }

  public String getEnvironmentProperty() {
    return _options.getAppSetting();
  }

  public Scope getScope() {
    return _options.getScope();
  }

  public PlanType[] getEnabledByDefaultForPlanTypes() {
    return _options.getEnabledByDefaultForPlanTypes();
  }

  public boolean hasEnabledByDefaultForPlanTypes() {
    return !ArrayUtils.isEmpty(_options.getEnabledByDefaultForPlanTypes());
  }

  public enum EnvState {
    CONTROLLED("controlled"),
    DISABLED("disabled"),
    ENABLED("enabled");

    private final String _value;

    EnvState(final String pValue) {
      _value = pValue;
    }

    public static EnvState fromValue(final String pValue) {
      return EnvState.valueOf(pValue.toUpperCase());
    }

    public String getValue() {
      return _value;
    }
  }

  public enum Scope {
    GROUP,
    ORGANIZATION
  }

  private static class Options {

    private String _appSetting;
    private PlanType[] _fullyEnabledPlanTypes = new PlanType[] {};
    private Scope _scope;

    private Options() {}

    String getAppSetting() {
      return _appSetting;
    }

    public PlanType[] getEnabledByDefaultForPlanTypes() {
      return _fullyEnabledPlanTypes;
    }

    public Scope getScope() {
      return _scope;
    }

    Options appSetting(final String pEnvironmentProperty) {
      _appSetting = pEnvironmentProperty;
      return this;
    }

    public Options enabledByDefaultForPlanTypes(final PlanType... pPlanTypes) {
      _fullyEnabledPlanTypes = ArrayUtils.addAll(_fullyEnabledPlanTypes, pPlanTypes);
      return this;
    }

    public Options enabledByDefaultForPlanTypes(final PlanTypeSet pPlanTypeSet) {
      _fullyEnabledPlanTypes =
          ArrayUtils.addAll(
              _fullyEnabledPlanTypes, pPlanTypeSet.getPlanTypes().toArray(new PlanType[0]));
      return this;
    }

    public Options scope(final Scope pScope) {
      _scope = pScope;
      return this;
    }
  }

  /**
   * Converter is a {@link TypeConverter} which handles unknown {@link FeatureFlag} variants by
   * returning a fallback
   */
  public static class Converter extends TypeConverter implements SimpleValueConverter {

    Converter() {
      super(FeatureFlag.class);
    }

    @Override
    public Object decode(Class<?> targetClass, Object fromDBObject, MappedField optionalExtraInfo) {
      if (fromDBObject == null) {
        return null;
      }
      if (fromDBObject instanceof String s) {
        try {
          return FeatureFlag.valueOf(s);
        } catch (IllegalArgumentException e) {
          UNKNOWN_VARIANTS.labels(s).inc();
          return FeatureFlag.UNKNOWN;
        }
      }
      UNKNOWN_VARIANTS.labels("nonstring").inc();
      return FeatureFlag.UNKNOWN;
    }
  }
}
