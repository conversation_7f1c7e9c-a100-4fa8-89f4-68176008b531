load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "typebinding",
    srcs = glob(["**/*.java"]),
    visibility = [
        "//server/src/main:__subpackages__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/abtesting",
        "//server/src/main/com/xgen/cloud/access/accesslist",
        "//server/src/main/com/xgen/cloud/access/activity",
        "//server/src/main/com/xgen/cloud/account",
        "//server/src/main/com/xgen/cloud/activity/_public/model",
        "//server/src/main/com/xgen/cloud/admin",
        "//server/src/main/com/xgen/cloud/alerts/alert",
        "//server/src/main/com/xgen/cloud/alerts/flapping/activity",
        "//server/src/main/com/xgen/cloud/apiserviceaccount",
        "//server/src/main/com/xgen/cloud/atm/activity",
        "//server/src/main/com/xgen/cloud/authn",
        "//server/src/main/com/xgen/cloud/authz/core",
        "//server/src/main/com/xgen/cloud/authz/resource",
        "//server/src/main/com/xgen/cloud/billingplatform/activity",
        "//server/src/main/com/xgen/cloud/brs/activity",
        "//server/src/main/com/xgen/cloud/clusters",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/config",
        "//server/src/main/com/xgen/cloud/cron",
        "//server/src/main/com/xgen/cloud/email",
        "//server/src/main/com/xgen/cloud/explorer/activity",
        "//server/src/main/com/xgen/cloud/externalanalytics",
        "//server/src/main/com/xgen/cloud/fts/activity",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/monitoring/agent",
        "//server/src/main/com/xgen/cloud/monitoring/metrics",
        "//server/src/main/com/xgen/cloud/monitoring/ratelimit",
        "//server/src/main/com/xgen/cloud/nds/activity",
        "//server/src/main/com/xgen/cloud/nds/common",
        "//server/src/main/com/xgen/cloud/nds/project",
        "//server/src/main/com/xgen/cloud/nds/serverless",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/partners/common",
        "//server/src/main/com/xgen/cloud/realm/activity",
        "//server/src/main/com/xgen/cloud/search/decoupled/activity",
        "//server/src/main/com/xgen/cloud/streams",
        "//server/src/main/com/xgen/cloud/system",
        "//server/src/main/com/xgen/cloud/team",
        "//server/src/main/com/xgen/cloud/user",
        "//server/src/main/com/xgen/module/federation/activity",
        "@maven//:com_fasterxml_jackson_core_jackson_annotations",
    ],
)
