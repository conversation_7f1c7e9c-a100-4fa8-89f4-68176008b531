package com.xgen.cloud.activity.runtime.view;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;
import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.view.EventViewContext;
import com.xgen.cloud.activity._public.model.event.view.HostEventTypeView;
import com.xgen.cloud.activity._public.model.event.view.HostEventView.HostEventViewForNdsGroup;
import com.xgen.cloud.activity._public.model.event.view.HostMetricEventTypeView;
import com.xgen.cloud.activity._public.model.event.view.HostMetricEventView;
import com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView;
import com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventView;
import com.xgen.cloud.activity.runtime.view.EventViewHierarchy.DefaultEventView;
import com.xgen.cloud.alerts.alert._public.model.view.AlertAuditTypeView;
import com.xgen.cloud.alerts.alert._public.model.view.AlertAuditView;
import com.xgen.cloud.alerts.alert._public.model.view.AlertConfigAuditTypeView;
import com.xgen.cloud.alerts.alert._public.model.view.AlertConfigAuditView;
import com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.ForNdsGroup;
import com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventView.ServiceAccountEventViewForNdsGroup;
import com.xgen.cloud.atm.activity._public.audit.view.AutomationConfigEventTypeView;
import com.xgen.cloud.atm.activity._public.audit.view.AutomationConfigEventView;
import com.xgen.cloud.authz.resource._public.model.event.view.ResourceEventTypeView;
import com.xgen.cloud.authz.resource._public.model.event.view.ResourceEventView.ResourceEventViewForNdsGroup;
import com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView;
import com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventView.BillingEventViewForNdsGroup;
import com.xgen.cloud.clusters._public.model.view.ClusterEventTypeView;
import com.xgen.cloud.clusters._public.model.view.ClusterEventView.ClusterEventViewForNdsGroup;
import com.xgen.cloud.email._public.activity.event.view.SupportEventTypeView;
import com.xgen.cloud.explorer.activity._public.audit.view.IndexBuildAuditTypeView;
import com.xgen.cloud.explorer.activity._public.event.view.DataExplorerAccessedEventTypeView;
import com.xgen.cloud.explorer.activity._public.event.view.DataExplorerAccessedEventView;
import com.xgen.cloud.explorer.activity._public.event.view.DataExplorerEventTypeView;
import com.xgen.cloud.explorer.activity._public.event.view.DataExplorerEventView;
import com.xgen.cloud.externalanalytics._public.model.view.SupportCaseEventTypeView;
import com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView;
import com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditView;
import com.xgen.cloud.group._public.model.activity.view.GroupAuditTypeView;
import com.xgen.cloud.group._public.model.activity.view.GroupEventTypeView;
import com.xgen.cloud.group._public.model.activity.view.GroupIntegrationEventTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.NDSTenantEndpointServiceDeploymentAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.AWSPeerVpcAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.AtlasResourcePolicyAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.AtlasResourcePolicyAuditView.AtlasResourcePolicyAuditViewForNdsGroup;
import com.xgen.cloud.nds.activity._public.event.audit.view.AzurePeerNetworkAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.ChartsAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.ChartsAuditView;
import com.xgen.cloud.nds.activity._public.event.audit.view.ClusterConnectionAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.DataProtectionAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.ExportBucketAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.GCPPeerVpcAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditView.NDSAuditViewForNdsGroup;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditView.NDSAutoScalingAuditViewForNdsGroup;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSDataValidationAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSDbCheckAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSMaintenanceWindowAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessAutoScalingAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditView;
import com.xgen.cloud.nds.activity._public.event.audit.view.PrivateLinkAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.ServerlessDeploymentAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.SetupServerlessAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.VersionAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.view.AutoIndexingEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.DiskBackupEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.EncryptionKeyEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.NDSX509UserAuthenticationEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.OnlineArchiveEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.ProactiveOperationEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.TenantBackupEventTypeView;
import com.xgen.cloud.nds.serverless._public.model.activity.view.FlexMetricEventTypeView;
import com.xgen.cloud.nds.serverless._public.model.activity.view.ServerlessEventTypeView;
import com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView;
import com.xgen.cloud.realm.activity._public.event.view.AppServiceEventView;
import com.xgen.cloud.search.decoupled.activity._public.audit.view.SearchDeploymentAuditTypeView;
import com.xgen.cloud.search.decoupled.activity._public.audit.view.SearchDeploymentAuditView;
import com.xgen.cloud.streams._public.model.event.view.StreamProcessorEventTypeView;
import com.xgen.cloud.streams._public.model.event.view.StreamProcessorEventView.StreamProcessorEventViewForNdsGroup;
import com.xgen.cloud.streams._public.model.event.view.StreamsEventTypeView;
import com.xgen.cloud.streams._public.model.event.view.StreamsEventView.StreamsEventViewForNdsGroup;
import com.xgen.cloud.team._public.activity.event.view.TeamEventTypeView;
import com.xgen.cloud.team._public.activity.event.view.TeamEventView.TeamEventViewForNdsGroup;
import com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView;
import com.xgen.cloud.user._public.model.activity.view.ApiUserEventView.ApiUserEventViewForNdsGroup;
import com.xgen.cloud.user._public.model.activity.view.UserEventTypeView;
import com.xgen.cloud.user._public.model.activity.view.UserEventView.UserEventViewForNdsGroup;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;

public class EventViewHierarchyForNdsGroup {

  @Schema(
      enumAsRef = true,
      oneOf = {
        AlertAuditTypeView.class,
        AlertConfigAuditTypeView.class,
        ApiUserEventTypeView.ForNdsGroup.class,
        ForNdsGroup.class,
        AppServiceEventTypeView.class,
        AutoIndexingEventTypeView.class,
        AutomationConfigEventTypeView.class,
        AWSPeerVpcAuditTypeView.class,
        AzurePeerNetworkAuditTypeView.class,
        BillingEventTypeView.ForNdsGroup.class,
        ClusterConnectionAuditTypeView.ForNdsGroup.class,
        ClusterEventTypeView.ForNdsGroup.class,
        CpsBackupEventTypeView.ForNdsGroup.class,
        DataExplorerAccessedEventTypeView.class,
        DataExplorerEventTypeView.class,
        DataProtectionAuditTypeView.class,
        DiskBackupEventTypeView.class,
        EncryptionKeyEventTypeView.class,
        ExportBucketAuditTypeView.class,
        FTSIndexAuditTypeView.class,
        GCPPeerVpcAuditTypeView.class,
        GroupAuditTypeView.ForNdsGroup.class,
        GroupEventTypeView.ForNdsGroup.class,
        GroupIntegrationEventTypeView.class,
        HostEventTypeView.ForNdsGroup.class,
        HostMetricEventTypeView.class,
        IndexBuildAuditTypeView.class,
        NDSAuditTypeView.ForNdsGroup.class,
        NDSDbCheckAuditTypeView.class,
        NDSDataValidationAuditTypeView.class,
        NDSAutoScalingAuditTypeView.ForNdsGroup.class,
        NDSMaintenanceWindowAuditTypeView.ForNdsGroup.class,
        NDSServerlessAutoScalingAuditTypeView.ForNdsGroup.class,
        NDSServerlessInstanceAuditTypeView.class,
        NDSTenantEndpointAuditTypeView.class,
        NDSTenantEndpointServiceDeploymentAuditTypeView.class,
        NDSX509UserAuthenticationEventTypeView.class,
        OnlineArchiveEventTypeView.ForNdsGroup.class,
        PrivateLinkAuditTypeView.class,
        ProactiveOperationEventTypeView.class,
        ReplicaSetEventTypeView.ForNdsGroup.class,
        SearchDeploymentAuditTypeView.class,
        ServerlessDeploymentAuditTypeView.class,
        ServerlessEventTypeView.class,
        FlexMetricEventTypeView.class,
        SetupServerlessAuditTypeView.class,
        StreamsEventTypeView.ForNdsGroup.class,
        StreamProcessorEventTypeView.ForNdsGroup.class,
        SupportCaseEventTypeView.class,
        SupportEventTypeView.class,
        TeamEventTypeView.ForNdsGroup.class,
        TenantBackupEventTypeView.class,
        UserEventTypeView.ForNdsGroup.class,
        VersionAuditTypeView.ForNdsGroup.class,
        ResourceEventTypeView.ForNdsGroup.class,
        AccessEventTypeView.ForNdsGroup.class,
        ChartsAuditTypeView.class,
        AtlasResourcePolicyAuditTypeView.ForNdsGroup.class,
      })
  public interface EventTypeForNdsGroup {}

  @Schema(
      oneOf = {
        DefaultEventViewForNdsGroup.class,
        AlertAuditView.class,
        AlertConfigAuditView.class,
        ApiUserEventViewForNdsGroup.class,
        ServiceAccountEventViewForNdsGroup.class,
        AutomationConfigEventView.class,
        AppServiceEventView.class,
        BillingEventViewForNdsGroup.class,
        ClusterEventViewForNdsGroup.class,
        DataExplorerAccessedEventView.class,
        DataExplorerEventView.class,
        FTSIndexAuditView.class,
        HostEventViewForNdsGroup.class,
        HostMetricEventView.class,
        NDSAuditViewForNdsGroup.class,
        NDSAutoScalingAuditViewForNdsGroup.class,
        NDSServerlessInstanceAuditView.class,
        NDSTenantEndpointAuditView.class,
        ReplicaSetEventView.ForNdsGroup.class,
        SearchDeploymentAuditView.class,
        TeamEventViewForNdsGroup.class,
        UserEventViewForNdsGroup.class,
        ResourceEventViewForNdsGroup.class,
        StreamsEventViewForNdsGroup.class,
        StreamProcessorEventViewForNdsGroup.class,
        ChartsAuditView.class,
        AtlasResourcePolicyAuditViewForNdsGroup.class
      },
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-125-oneOf-must-have-discriminator",
                  value = "Schema predates IPA validation.")
            })
      })
  public interface EventViewForNdsGroup {}

  @Schema(
      title = "Any Other Events",
      description = "Other events which don't have extra details beside of basic one.")
  public static class DefaultEventViewForNdsGroup extends DefaultEventView {

    // private constructor for open API subclasses and jackson
    private DefaultEventViewForNdsGroup() {}

    public DefaultEventViewForNdsGroup(final Event event, final EventViewContext context) {
      super(event, context);
    }

    @SuppressWarnings("SwaggerSchemaUsageChecker")
    @Override
    @JsonProperty
    @Schema(
        type = "string",
        description = "Unique identifier of event type.",
        enumAsRef = true,
        oneOf = {
          AutoIndexingEventTypeView.class,
          AWSPeerVpcAuditTypeView.class,
          AzurePeerNetworkAuditTypeView.class,
          ClusterConnectionAuditTypeView.ForNdsGroup.class,
          CpsBackupEventTypeView.ForNdsGroup.class,
          DataProtectionAuditTypeView.class,
          DiskBackupEventTypeView.class,
          EncryptionKeyEventTypeView.class,
          ExportBucketAuditTypeView.class,
          GCPPeerVpcAuditTypeView.class,
          GroupAuditTypeView.ForNdsGroup.class,
          GroupEventTypeView.ForNdsGroup.class,
          GroupIntegrationEventTypeView.class,
          IndexBuildAuditTypeView.class,
          NDSDbCheckAuditTypeView.class,
          NDSDataValidationAuditTypeView.class,
          NDSMaintenanceWindowAuditTypeView.ForNdsGroup.class,
          NDSServerlessAutoScalingAuditTypeView.ForNdsGroup.class,
          NDSTenantEndpointServiceDeploymentAuditTypeView.class,
          NDSX509UserAuthenticationEventTypeView.class,
          OnlineArchiveEventTypeView.ForNdsGroup.class,
          PrivateLinkAuditTypeView.class,
          ProactiveOperationEventTypeView.class,
          SearchDeploymentAuditTypeView.class,
          ServerlessDeploymentAuditTypeView.class,
          ServerlessEventTypeView.class,
          FlexMetricEventTypeView.class,
          SetupServerlessAuditTypeView.class,
          StreamsEventTypeView.class,
          StreamProcessorEventTypeView.class,
          SupportCaseEventTypeView.class,
          SupportEventTypeView.class,
          TenantBackupEventTypeView.class,
          VersionAuditTypeView.ForNdsGroup.class,
          AccessEventTypeView.ForNdsGroup.class,
        },
        requiredMode = REQUIRED)
    public String getEventTypeName() {
      return super.getEventTypeName();
    }
  }
}
