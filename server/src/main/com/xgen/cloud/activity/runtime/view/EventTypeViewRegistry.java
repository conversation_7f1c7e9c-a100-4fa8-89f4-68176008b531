package com.xgen.cloud.activity.runtime.view;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.activity._public.model.event.view.EventTypeView;
import com.xgen.cloud.common.model._public.typebindings.TypeRegistry;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.inject.Inject;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;

public class EventTypeViewRegistry implements TypeRegistry<String, EventType> {

  private final Map<String, EventType> eventTypeByEventTypeViewName;
  private final Map<EventType, List<String>> eventTypeViewNameByEventType;
  private final Map<String, EventTypeView<?>> eventTypeViewByEventTypeViewName;

  @Inject
  public EventTypeViewRegistry() {
    final Set<Class<?>> registeredEventTypeViews =
        Stream.of(EventViewHierarchy.EventTypeView.class.getAnnotation(Schema.class))
            .flatMap(schema -> Stream.of(schema.oneOf()))
            .collect(toSet());
    final List<EventTypeView<?>> eventTypeViews =
        registeredEventTypeViews.stream()
            .flatMap(t -> Stream.of(t.getEnumConstants()).map(e -> (EventTypeView<?>) e))
            .collect(toList());
    this.eventTypeByEventTypeViewName =
        eventTypeViews.stream().collect(toMap(EventTypeView::name, EventTypeView::getSource));
    this.eventTypeViewByEventTypeViewName =
        eventTypeViews.stream().collect(toMap(EventTypeView::name, identity()));
    this.eventTypeViewNameByEventType =
        eventTypeViews.stream()
            .collect(groupingBy(EventTypeView::getSource, mapping(EventTypeView::name, toList())));
  }

  public Map<String, EventTypeView<?>> getEventTypeViewByEventTypeViewName() {
    return eventTypeViewByEventTypeViewName;
  }

  @Override
  public Class<?> getSuperType() {
    return EventTypeView.class;
  }

  @Override
  public EventType getTypeByKey(String key) {
    return eventTypeByEventTypeViewName.get(key);
  }

  @Override
  public String getKeyByType(EventType type) {
    final List<String> names = eventTypeViewNameByEventType.get(type);
    if (names == null) {
      return null;
    }
    return names.size() == 1
        ? names.get(0)
        : names.stream()
            .filter(
                n ->
                    !Objects.equals(
                        eventTypeByEventTypeViewName.get(n).name(),
                        eventTypeViewByEventTypeViewName.get(n).name()))
            .findFirst()
            .orElse(null);
  }
}
