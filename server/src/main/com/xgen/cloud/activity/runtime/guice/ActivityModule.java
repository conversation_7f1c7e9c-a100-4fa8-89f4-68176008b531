package com.xgen.cloud.activity.runtime.guice;

import static com.google.inject.name.Names.named;
import static com.xgen.cloud.common.view._public.transform.ViewTransformer.VIEW_HIERARCHY_SET_NAME;

import com.google.inject.AbstractModule;
import com.google.inject.TypeLiteral;
import com.google.inject.multibindings.MapBinder;
import com.google.inject.multibindings.Multibinder;
import com.xgen.cloud.abtesting._public.model.BulkAllocationAuditEvent;
import com.xgen.cloud.access.accesslist._public.model.event.GlobalAccessListAudit;
import com.xgen.cloud.access.accesslist._public.model.event.GlobalAccessListEvent;
import com.xgen.cloud.access.activity._public.audit.AccessAuditEvent;
import com.xgen.cloud.access.activity._public.event.AccessEvent;
import com.xgen.cloud.account._public.model.activity.DeviceCodeAudit;
import com.xgen.cloud.activity._public.model.event.BiConnectorEvent;
import com.xgen.cloud.activity._public.model.event.CaptainLogAudit;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.HostAudit;
import com.xgen.cloud.activity._public.model.event.HostEvent;
import com.xgen.cloud.activity._public.model.event.HostMetricEvent;
import com.xgen.cloud.activity._public.model.event.HostSecurityCheckupEvent;
import com.xgen.cloud.activity._public.model.event.ReplicaSetEvent;
import com.xgen.cloud.activity._public.model.event.SystemAlertGlobalServiceAccountEvent;
import com.xgen.cloud.activity._public.svc.event.EventCommitter;
import com.xgen.cloud.activity.runtime.typebinding.EventTypeHierarchy;
import com.xgen.cloud.activity.runtime.view.EventTypeViewRegistry;
import com.xgen.cloud.activity.runtime.view.EventViewHierarchy;
import com.xgen.cloud.admin._public.activity.AppSettingsChangeAudit;
import com.xgen.cloud.admin._public.activity.SystemConfigChangeAudit;
import com.xgen.cloud.admin._public.activity.SystemLogEvent;
import com.xgen.cloud.alerts.alert._public.model.AlertAudit;
import com.xgen.cloud.alerts.alert._public.model.AlertConfigAudit;
import com.xgen.cloud.alerts.alert._public.model.activity.MaintenanceWindowConfigAudit;
import com.xgen.cloud.alerts.flapping.activity._public.model.FlappingEvent;
import com.xgen.cloud.apiserviceaccount._public.activity.audit.GlobalServiceAccountAudit;
import com.xgen.cloud.apiserviceaccount._public.activity.audit.ServiceAccountAudit;
import com.xgen.cloud.apiserviceaccount._public.activity.event.model.GlobalServiceAccountEvent;
import com.xgen.cloud.apiserviceaccount._public.activity.event.model.ServiceAccountEvent;
import com.xgen.cloud.atm.activity._public.audit.AutomationConfigAudit;
import com.xgen.cloud.atm.activity._public.audit.LogCollectionDownloadAudit;
import com.xgen.cloud.atm.activity._public.audit.LogCollectionRequestAudit;
import com.xgen.cloud.atm.activity._public.event.AutomationConfigEvent;
import com.xgen.cloud.atm.activity._public.event.CrashLogEvent;
import com.xgen.cloud.authn._public.model.AuthnServiceAudit;
import com.xgen.cloud.authz.core._public.model.event.UserGroupAudit;
import com.xgen.cloud.authz.core._public.model.event.UserGroupEvent;
import com.xgen.cloud.authz.resource._public.model.event.ResourceAudit;
import com.xgen.cloud.authz.resource._public.model.event.ResourceEvent;
import com.xgen.cloud.billingplatform.activity._public.audit.BillingAudit;
import com.xgen.cloud.billingplatform.activity._public.event.BillingAuditorEvent;
import com.xgen.cloud.billingplatform.activity._public.event.BillingEvent;
import com.xgen.cloud.billingplatform.activity._public.event.SystemBillingEvent;
import com.xgen.cloud.brs.activity._public.model.BackupAudit;
import com.xgen.cloud.brs.activity._public.model.BackupBusyEvent;
import com.xgen.cloud.brs.activity._public.model.BackupClustershotEvent;
import com.xgen.cloud.brs.activity._public.model.BackupDeploymentConfigMissingEvent;
import com.xgen.cloud.brs.activity._public.model.BackupEvent;
import com.xgen.cloud.brs.activity._public.model.BackupSnapshotEvent;
import com.xgen.cloud.brs.activity._public.model.BackupUnsupportedEvent;
import com.xgen.cloud.brs.activity._public.model.BlockstoreJobEvent;
import com.xgen.cloud.brs.activity._public.model.RestoreStatusEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupDBLowFreeSpaceEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupDaemonEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupResizeEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupTheftEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBlockstoreEvent;
import com.xgen.cloud.brs.activity._public.model.SystemDatabaseProcessEvent;
import com.xgen.cloud.clusters._public.model.ClusterEvent;
import com.xgen.cloud.common.model._public.typebindings.TypeHierarchy;
import com.xgen.cloud.config._public.model.ConfigApplicationPropertyAuditEvent;
import com.xgen.cloud.config._public.model.ConfigFeatureFlagAuditEvent;
import com.xgen.cloud.config._public.model.ConfigNamespaceAuditEvent;
import com.xgen.cloud.cron._public.model.CronAudit;
import com.xgen.cloud.cron._public.model.SystemCronJobEvent;
import com.xgen.cloud.cron._public.model.SystemCronJobStatusEvent;
import com.xgen.cloud.email._public.activity.audit.SupportAudit;
import com.xgen.cloud.email._public.activity.event.SupportEvent;
import com.xgen.cloud.eventcommitter._public.svc.abtesting.BulkAllocationAuditEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.access.AccessAuditEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.access.AccessEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.access.GlobalAccessListAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.access.GlobalAccessListEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.access.GlobalServiceAccountAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.access.GlobalServiceAccountEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.access.ServiceAccountAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.access.ServiceAccountEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.access.SystemAlertGlobalServiceAccountEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.account.DeviceCodeAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.admin.AppSettingsChangeAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.admin.SystemConfigChangeAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.admin.SystemLogEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.alerts.AlertAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.alerts.AlertConfigAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.alerts.FlappingEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.alerts.MaintenanceWindowConfigAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.alerts.SystemAlertProcessingEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.atm.AutomationConfigAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.atm.AutomationConfigEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.atm.BiConnectorEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.atm.CrashLogEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.atm.LogCollectionDownloadAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.atm.LogCollectionRequestAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.authn.AuthnServiceAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.authz.ResourceAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.authz.ResourceEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.authz.UserGroupAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.authz.UserGroupEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.baas.DeploymentEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.baas.LimitEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.baas.LogForwarderEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.baas.RealmMetricEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.baas.SyncEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.baas.TriggerEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.BackupAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.BackupBusyEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.BackupClustershotEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.BackupDeploymentConfigMissingEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.BackupEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.BackupSnapshotEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.BackupUnsupportedEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.BlockstoreJobEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.CpsBackupEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.CpsBackupOplogDiscontiguousEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.CpsBackupRestoreEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.CpsBillingEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.DataProtectionAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.DiskBackupAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.DiskBackupEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.RestoreStatusEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.SystemBackupDBLowFreeSpaceEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.SystemBackupDaemonEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.SystemBackupEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.SystemBackupResizeEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.SystemBackupTheftEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.SystemBlockstoreEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.SystemDatabaseProcessEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.TenantBackupAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.backup.TenantBackupEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.base.EventCommitterImpl;
import com.xgen.cloud.eventcommitter._public.svc.base.EventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.billing.BillingAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.billing.BillingAuditorEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.billing.BillingEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.billing.SystemBillingEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.charts.ChartsAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.config.ConfigApplicationPropertyAuditEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.config.ConfigFeatureFlagAuditEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.config.ConfigNamespaceAuditEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.cron.CronAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.cron.SystemCronJobEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.cron.SystemCronJobStatusEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.explorer.DataExplorerAccessedAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.explorer.DataExplorerAccessedEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.explorer.DataExplorerAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.explorer.DataExplorerEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.explorer.IndexBuildAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.explorer.IndexBuildEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.federation.FederationSettingsAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.federation.FederationSettingsEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.group.GroupAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.group.GroupEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.group.GroupIntegrationAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.group.GroupIntegrationEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.host.ClusterEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.host.HostAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.host.HostEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.host.HostMetricEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.host.HostSecurityCheckupEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.host.ReplicaSetEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.host.ServerAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.monitoring.AgentEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.monitoring.MongotEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.monitoring.SystemMaasEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.ADFAAdminAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.AWSPeerVpcAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.AdminAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.AdminEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.AutoIndexingAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.AutoIndexingEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.AzurePeerNetworkAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.BumperFileRemovalEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.ClusterConnectionAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.DbCheckEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.EncryptionKeyEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.ExportBucketAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.FlexMetricEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.FlexMigrationEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.GCPPeerVpcAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.LogIngestionEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.LogUploaderDownEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.LongRunningMoveEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.MPAEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.MlabMigrationAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSAutoScalingAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSCheckMetadataConsistencyEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSDataValidationAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSDataValidationEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSDbCheckAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSDbCheckEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSMaintenanceWindowAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSServerlessAutoScalingAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSServerlessInstanceAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSServerlessTenantMigrationAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSTenantEndpointAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSTenantEndpointServiceDeploymentAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.NDSX509UserAuthenticationEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.OnlineArchiveEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.PrivateLinkAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.ProactiveOperationEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.QuotaUsageEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.ServerlessAutoIndexingAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.ServerlessDeploymentAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.ServerlessEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.ServerlessMTMClusterGrpcIncrementalRolloutAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.ServerlessMTMClusterGrpcIncrementalRolloutEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.ServerlessMetricEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.SetupServerlessAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.SystemAWSEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.SystemAzureEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.SystemGCPEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.SystemNDSEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.nds.VersionAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.org.AtlasResourcePolicyAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.org.OrgAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.org.OrgEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.partner.PartnerAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.partner.PartnerEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.ratelimit.RateLimitSuspensionAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.search.FTSIndexAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.search.SearchDeploymentAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.streams.StreamProcessorEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.streams.StreamsEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.support.CaptainLogAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.support.SupportAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.support.SupportCaseAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.support.SupportCaseEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.support.SupportEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.team.TeamAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.team.TeamEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.user.ApiUserAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.user.ApiUserEventTranslator;
import com.xgen.cloud.eventcommitter._public.svc.user.UserAuditTranslator;
import com.xgen.cloud.eventcommitter._public.svc.user.UserEventTranslator;
import com.xgen.cloud.eventcommitter.runtime.guice.EventCommitterModule;
import com.xgen.cloud.explorer.activity._public.audit.DataExplorerAccessedAudit;
import com.xgen.cloud.explorer.activity._public.audit.DataExplorerAudit;
import com.xgen.cloud.explorer.activity._public.audit.IndexBuildAudit;
import com.xgen.cloud.explorer.activity._public.event.DataExplorerAccessedEvent;
import com.xgen.cloud.explorer.activity._public.event.DataExplorerEvent;
import com.xgen.cloud.explorer.activity._public.event.IndexBuildEvent;
import com.xgen.cloud.externalanalytics._public.model.SupportCaseAudit;
import com.xgen.cloud.externalanalytics._public.model.SupportCaseEvent;
import com.xgen.cloud.fts.activity._public.audit.FTSIndexAudit;
import com.xgen.cloud.group._public.model.activity.GroupAudit;
import com.xgen.cloud.group._public.model.activity.GroupEvent;
import com.xgen.cloud.group._public.model.activity.GroupIntegrationAudit;
import com.xgen.cloud.group._public.model.activity.GroupIntegrationEvent;
import com.xgen.cloud.monitoring.agent._public.model.event.AgentEvent;
import com.xgen.cloud.monitoring.metrics._public.model.activity.MongotEvent;
import com.xgen.cloud.monitoring.metrics._public.model.activity.SystemMaasEvent;
import com.xgen.cloud.monitoring.ratelimit._public.audit.RateLimitSuspensionAudit;
import com.xgen.cloud.nds.activity._public.event.AdminEvent;
import com.xgen.cloud.nds.activity._public.event.AutoIndexingEvent;
import com.xgen.cloud.nds.activity._public.event.BumperFileRemovalEvent;
import com.xgen.cloud.nds.activity._public.event.CpsBackupEvent;
import com.xgen.cloud.nds.activity._public.event.CpsBackupOplogDiscontiguousEvent;
import com.xgen.cloud.nds.activity._public.event.CpsBackupRestoreEvent;
import com.xgen.cloud.nds.activity._public.event.CpsBillingEvent;
import com.xgen.cloud.nds.activity._public.event.DbCheckEvent;
import com.xgen.cloud.nds.activity._public.event.DiskBackupEvent;
import com.xgen.cloud.nds.activity._public.event.EncryptionKeyEvent;
import com.xgen.cloud.nds.activity._public.event.FlexMigrationEvent;
import com.xgen.cloud.nds.activity._public.event.LogIngestionEvent;
import com.xgen.cloud.nds.activity._public.event.LogUploaderDownEvent;
import com.xgen.cloud.nds.activity._public.event.MPAEvent;
import com.xgen.cloud.nds.activity._public.event.NDSX509UserAuthenticationEvent;
import com.xgen.cloud.nds.activity._public.event.OnlineArchiveEvent;
import com.xgen.cloud.nds.activity._public.event.ProactiveOperationEvent;
import com.xgen.cloud.nds.activity._public.event.ServerlessMTMClusterGrpcIncrementalRolloutEvent;
import com.xgen.cloud.nds.activity._public.event.SystemAWSEvent;
import com.xgen.cloud.nds.activity._public.event.SystemAzureEvent;
import com.xgen.cloud.nds.activity._public.event.SystemGCPEvent;
import com.xgen.cloud.nds.activity._public.event.SystemNDSEvent;
import com.xgen.cloud.nds.activity._public.event.TenantBackupEvent;
import com.xgen.cloud.nds.activity._public.event.audit.ADFAAdminAudit;
import com.xgen.cloud.nds.activity._public.event.audit.AWSPeerVpcAudit;
import com.xgen.cloud.nds.activity._public.event.audit.AdminAudit;
import com.xgen.cloud.nds.activity._public.event.audit.AtlasResourcePolicyAudit;
import com.xgen.cloud.nds.activity._public.event.audit.AutoIndexingAudit;
import com.xgen.cloud.nds.activity._public.event.audit.AzurePeerNetworkAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ChartsAudit;
import com.xgen.cloud.nds.activity._public.event.audit.DataProtectionAudit;
import com.xgen.cloud.nds.activity._public.event.audit.DiskBackupAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ExportBucketAudit;
import com.xgen.cloud.nds.activity._public.event.audit.GCPPeerVpcAudit;
import com.xgen.cloud.nds.activity._public.event.audit.MlabMigrationAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAutoScalingAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSCheckMetadataConsistencyEvent;
import com.xgen.cloud.nds.activity._public.event.audit.NDSDataValidationAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSDataValidationEvent;
import com.xgen.cloud.nds.activity._public.event.audit.NDSDbCheckAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSDbCheckEvent;
import com.xgen.cloud.nds.activity._public.event.audit.NDSMaintenanceWindowAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSServerlessAutoScalingAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSServerlessInstanceAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSServerlessTenantMigrationAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSTenantEndpointAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSTenantEndpointServiceDeploymentAudit;
import com.xgen.cloud.nds.activity._public.event.audit.PrivateLinkAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ServerAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ServerlessAutoIndexingAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ServerlessDeploymentAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ServerlessMTMClusterGrpcIncrementalRolloutAudit;
import com.xgen.cloud.nds.activity._public.event.audit.SetupServerlessAudit;
import com.xgen.cloud.nds.activity._public.event.audit.TenantBackupAudit;
import com.xgen.cloud.nds.activity._public.event.audit.VersionAudit;
import com.xgen.cloud.nds.common._public.model.LongRunningMoveEvent;
import com.xgen.cloud.nds.project._public.model.cloudprovidermonitoring.QuotaUsageEvent;
import com.xgen.cloud.nds.project._public.model.event.ClusterConnectionAudit;
import com.xgen.cloud.nds.serverless._public.model.activity.FlexMetricEvent;
import com.xgen.cloud.nds.serverless._public.model.activity.ServerlessEvent;
import com.xgen.cloud.nds.serverless._public.model.activity.ServerlessMetricEvent;
import com.xgen.cloud.organization._public.model.activity.OrgAudit;
import com.xgen.cloud.organization._public.model.activity.OrgEvent;
import com.xgen.cloud.partners.common._public.activity.PartnerAudit;
import com.xgen.cloud.partners.common._public.activity.PartnerEvent;
import com.xgen.cloud.realm.activity._public.event.DeploymentEvent;
import com.xgen.cloud.realm.activity._public.event.LimitEvent;
import com.xgen.cloud.realm.activity._public.event.LogForwarderEvent;
import com.xgen.cloud.realm.activity._public.event.RealmMetricEvent;
import com.xgen.cloud.realm.activity._public.event.SyncEvent;
import com.xgen.cloud.realm.activity._public.event.TriggerEvent;
import com.xgen.cloud.search.decoupled.activity._public.audit.SearchDeploymentAudit;
import com.xgen.cloud.streams._public.model.event.StreamProcessorEvent;
import com.xgen.cloud.streams._public.model.event.StreamsEvent;
import com.xgen.cloud.system._public.model.SystemAlertProcessingEvent;
import com.xgen.cloud.team._public.activity.audit.TeamAudit;
import com.xgen.cloud.team._public.activity.event.TeamEvent;
import com.xgen.cloud.user._public.model.activity.ApiUserAudit;
import com.xgen.cloud.user._public.model.activity.ApiUserEvent;
import com.xgen.cloud.user._public.model.activity.UserAudit;
import com.xgen.cloud.user._public.model.activity.UserEvent;
import com.xgen.module.federation.activity._public.audit.FederationSettingsAudit;
import com.xgen.module.federation.activity._public.event.FederationSettingsEvent;

public class ActivityModule extends AbstractModule {

  @Override
  protected void configure() {
    bind(EventTypeViewRegistry.class).asEagerSingleton();
    final Multibinder<TypeHierarchy> viewHierarchyBinder =
        Multibinder.newSetBinder(binder(), TypeHierarchy.class, named(VIEW_HIERARCHY_SET_NAME));
    viewHierarchyBinder.addBinding().to(EventViewHierarchy.class);
    final Multibinder<TypeHierarchy> typeHierarchyBinder =
        Multibinder.newSetBinder(binder(), TypeHierarchy.class);
    typeHierarchyBinder.addBinding().to(EventTypeHierarchy.class);
    typeHierarchyBinder.addBinding().to(EventViewHierarchy.class);
    bind(EventCommitter.class).to(EventCommitterImpl.class);

    MapBinder<String, EventTranslator<? extends Event>> eventTranslators =
        MapBinder.newMapBinder(binder(), new TypeLiteral<>() {}, new TypeLiteral<>() {});

    // Host based events
    eventTranslators.addBinding(HostEvent.TYPE_NAME).toInstance(HostEventTranslator.INSTANCE);
    eventTranslators.addBinding(HostAudit.TYPE_NAME).toInstance(HostAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(HostMetricEvent.TYPE_NAME)
        .toInstance(HostMetricEventTranslator.INSTANCE);
    eventTranslators.addBinding(StreamsEvent.TYPE_NAME).toInstance(StreamsEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(StreamProcessorEvent.TYPE_NAME)
        .toInstance(StreamProcessorEventTranslator.INSTANCE);
    eventTranslators.addBinding(ServerAudit.TYPE_NAME).toInstance(ServerAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(HostSecurityCheckupEvent.TYPE_NAME)
        .toInstance(HostSecurityCheckupEventTranslator.INSTANCE);

    eventTranslators
        .addBinding(ReplicaSetEvent.TYPE_NAME)
        .toInstance(ReplicaSetEventTranslator.INSTANCE);
    eventTranslators.addBinding(ClusterEvent.TYPE_NAME).toInstance(ClusterEventTranslator.INSTANCE);

    // User based events
    eventTranslators.addBinding(UserEvent.TYPE_NAME).toInstance(UserEventTranslator.INSTANCE);
    eventTranslators.addBinding(UserAudit.TYPE_NAME).toInstance(UserAuditTranslator.INSTANCE);
    eventTranslators.addBinding(ApiUserEvent.TYPE_NAME).toInstance(ApiUserEventTranslator.INSTANCE);
    eventTranslators.addBinding(ApiUserAudit.TYPE_NAME).toInstance(ApiUserAuditTranslator.INSTANCE);

    eventTranslators.addBinding(GroupAudit.TYPE_NAME).toInstance(GroupAuditTranslator.INSTANCE);
    eventTranslators.addBinding(GroupEvent.TYPE_NAME).toInstance(GroupEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(GroupIntegrationAudit.TYPE_NAME)
        .toInstance(GroupIntegrationAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(GroupIntegrationEvent.TYPE_NAME)
        .toInstance(GroupIntegrationEventTranslator.INSTANCE);

    eventTranslators.addBinding(OrgAudit.TYPE_NAME).toInstance(OrgAuditTranslator.INSTANCE);
    eventTranslators.addBinding(OrgEvent.TYPE_NAME).toInstance(OrgEventTranslator.INSTANCE);

    eventTranslators.addBinding(TeamEvent.TYPE_NAME).toInstance(TeamEventTranslator.INSTANCE);
    eventTranslators.addBinding(TeamAudit.TYPE_NAME).toInstance(TeamAuditTranslator.INSTANCE);

    eventTranslators
        .addBinding(SystemBillingEvent.TYPE_NAME)
        .toInstance(SystemBillingEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(BillingAuditorEvent.TYPE_NAME)
        .toInstance(BillingAuditorEventTranslator.INSTANCE);

    // NDS Events
    eventTranslators.addBinding(NDSAudit.TYPE_NAME).toInstance(NDSAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemNDSEvent.TYPE_NAME)
        .toInstance(SystemNDSEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemAWSEvent.TYPE_NAME)
        .toInstance(SystemAWSEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemGCPEvent.TYPE_NAME)
        .toInstance(SystemGCPEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemAzureEvent.TYPE_NAME)
        .toInstance(SystemAzureEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSMaintenanceWindowAudit.TYPE_NAME)
        .toInstance(NDSMaintenanceWindowAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSAutoScalingAudit.TYPE_NAME)
        .toInstance(NDSAutoScalingAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(GCPPeerVpcAudit.TYPE_NAME)
        .toInstance(GCPPeerVpcAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(AWSPeerVpcAudit.TYPE_NAME)
        .toInstance(AWSPeerVpcAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(PrivateLinkAudit.TYPE_NAME)
        .toInstance(PrivateLinkAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(AzurePeerNetworkAudit.TYPE_NAME)
        .toInstance(AzurePeerNetworkAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(EncryptionKeyEvent.TYPE_NAME)
        .toInstance(EncryptionKeyEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(MlabMigrationAudit.TYPE_NAME)
        .toInstance(MlabMigrationAuditTranslator.INSTANCE);
    eventTranslators.addBinding(VersionAudit.TYPE_NAME).toInstance(VersionAuditTranslator.INSTANCE);
    eventTranslators.addBinding(AdminAudit.TYPE_NAME).toInstance(AdminAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(QuotaUsageEvent.TYPE_NAME)
        .toInstance(QuotaUsageEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(BumperFileRemovalEvent.TYPE_NAME)
        .toInstance(BumperFileRemovalEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(LogUploaderDownEvent.TYPE_NAME)
        .toInstance(LogUploaderDownEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(AtlasResourcePolicyAudit.TYPE_NAME)
        .toInstance(AtlasResourcePolicyAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(LogIngestionEvent.TYPE_NAME)
        .toInstance(LogIngestionEventTranslator.INSTANCE);
    eventTranslators.addBinding(MPAEvent.TYPE_NAME).toInstance(MPAEventTranslator.INSTANCE);

    // Serverless Events
    eventTranslators
        .addBinding(NDSServerlessInstanceAudit.TYPE_NAME)
        .toInstance(NDSServerlessInstanceAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSServerlessTenantMigrationAudit.TYPE_NAME)
        .toInstance(NDSServerlessTenantMigrationAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSServerlessAutoScalingAudit.TYPE_NAME)
        .toInstance(NDSServerlessAutoScalingAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(ServerlessDeploymentAudit.TYPE_NAME)
        .toInstance(ServerlessDeploymentAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(SetupServerlessAudit.TYPE_NAME)
        .toInstance(SetupServerlessAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(ServerlessEvent.TYPE_NAME)
        .toInstance(ServerlessEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(ServerlessMetricEvent.TYPE_NAME)
        .toInstance(ServerlessMetricEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(ServerlessAutoIndexingAudit.TYPE_NAME)
        .toInstance(ServerlessAutoIndexingAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(ServerlessMTMClusterGrpcIncrementalRolloutEvent.TYPE_NAME)
        .toInstance(ServerlessMTMClusterGrpcIncrementalRolloutEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(ServerlessMTMClusterGrpcIncrementalRolloutAudit.TYPE_NAME)
        .toInstance(ServerlessMTMClusterGrpcIncrementalRolloutAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSTenantEndpointAudit.TYPE_NAME)
        .toInstance(NDSTenantEndpointAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSTenantEndpointServiceDeploymentAudit.TYPE_NAME)
        .toInstance(NDSTenantEndpointServiceDeploymentAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSX509UserAuthenticationEvent.TYPE_NAME)
        .toInstance(NDSX509UserAuthenticationEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSDataValidationEvent.TYPE_NAME)
        .toInstance(NDSDataValidationEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSDataValidationAudit.TYPE_NAME)
        .toInstance(NDSDataValidationAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSDbCheckEvent.TYPE_NAME)
        .toInstance(NDSDbCheckEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSDbCheckAudit.TYPE_NAME)
        .toInstance(NDSDbCheckAuditTranslator.INSTANCE);
    eventTranslators.addBinding(DbCheckEvent.TYPE_NAME).toInstance(DbCheckEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(NDSCheckMetadataConsistencyEvent.TYPE_NAME)
        .toInstance(NDSCheckMetadataConsistencyEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(ClusterConnectionAudit.TYPE_NAME)
        .toInstance(ClusterConnectionAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(OnlineArchiveEvent.TYPE_NAME)
        .toInstance(OnlineArchiveEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(ADFAAdminAudit.TYPE_NAME)
        .toInstance(ADFAAdminAuditTranslator.INSTANCE);

    // Flex events
    eventTranslators
        .addBinding(FlexMigrationEvent.TYPE_NAME)
        .toInstance(FlexMigrationEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(FlexMetricEvent.TYPE_NAME)
        .toInstance(FlexMetricEventTranslator.INSTANCE);

    // Backup events
    eventTranslators
        .addBinding(BackupClustershotEvent.TYPE_NAME)
        .toInstance(BackupClustershotEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(BackupUnsupportedEvent.TYPE_NAME)
        .toInstance(BackupUnsupportedEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(BackupSnapshotEvent.TYPE_NAME)
        .toInstance(BackupSnapshotEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(RestoreStatusEvent.TYPE_NAME)
        .toInstance(RestoreStatusEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(BackupBusyEvent.TYPE_NAME)
        .toInstance(BackupBusyEventTranslator.INSTANCE);
    eventTranslators.addBinding(BackupAudit.TYPE_NAME).toInstance(BackupAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(BlockstoreJobEvent.TYPE_NAME)
        .toInstance(BlockstoreJobEventTranslator.INSTANCE);
    eventTranslators.addBinding(BackupEvent.TYPE_NAME).toInstance(BackupEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(BackupDeploymentConfigMissingEvent.TYPE_NAME)
        .toInstance(BackupDeploymentConfigMissingEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(CpsBackupOplogDiscontiguousEvent.TYPE_NAME)
        .toInstance(CpsBackupOplogDiscontiguousEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(CpsBackupRestoreEvent.TYPE_NAME)
        .toInstance(CpsBackupRestoreEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(CpsBackupEvent.TYPE_NAME)
        .toInstance(CpsBackupEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(CpsBillingEvent.TYPE_NAME)
        .toInstance(CpsBillingEventTranslator.INSTANCE);

    eventTranslators
        .addBinding(DiskBackupEvent.TYPE_NAME)
        .toInstance(DiskBackupEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(DiskBackupAudit.TYPE_NAME)
        .toInstance(DiskBackupAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(TenantBackupEvent.TYPE_NAME)
        .toInstance(TenantBackupEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(TenantBackupAudit.TYPE_NAME)
        .toInstance(TenantBackupAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(DataProtectionAudit.TYPE_NAME)
        .toInstance(DataProtectionAuditTranslator.INSTANCE);

    eventTranslators
        .addBinding(SystemBackupDaemonEvent.TYPE_NAME)
        .toInstance(SystemBackupDaemonEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemBackupEvent.TYPE_NAME)
        .toInstance(SystemBackupEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemBackupResizeEvent.TYPE_NAME)
        .toInstance(SystemBackupResizeEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemBackupTheftEvent.TYPE_NAME)
        .toInstance(SystemBackupTheftEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemBackupDBLowFreeSpaceEvent.TYPE_NAME)
        .toInstance(SystemBackupDBLowFreeSpaceEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemBlockstoreEvent.TYPE_NAME)
        .toInstance(SystemBlockstoreEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemDatabaseProcessEvent.TYPE_NAME)
        .toInstance(SystemDatabaseProcessEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(ExportBucketAudit.TYPE_NAME)
        .toInstance(ExportBucketAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(LongRunningMoveEvent.TYPE_NAME)
        .toInstance(LongRunningMoveEventTranslator.INSTANCE);

    // BaaS Events
    eventTranslators
        .addBinding(DeploymentEvent.TYPE_NAME)
        .toInstance(DeploymentEventTranslator.INSTANCE);
    eventTranslators.addBinding(LimitEvent.TYPE_NAME).toInstance(LimitEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(LogForwarderEvent.TYPE_NAME)
        .toInstance(LogForwarderEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(RealmMetricEvent.TYPE_NAME)
        .toInstance(RealmMetricEventTranslator.INSTANCE);
    eventTranslators.addBinding(SyncEvent.TYPE_NAME).toInstance(SyncEventTranslator.INSTANCE);
    eventTranslators.addBinding(TriggerEvent.TYPE_NAME).toInstance(TriggerEventTranslator.INSTANCE);

    // Indexing Events
    eventTranslators
        .addBinding(AutoIndexingAudit.TYPE_NAME)
        .toInstance(AutoIndexingAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(AutoIndexingEvent.TYPE_NAME)
        .toInstance(AutoIndexingEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(IndexBuildAudit.TYPE_NAME)
        .toInstance(IndexBuildAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(IndexBuildEvent.TYPE_NAME)
        .toInstance(IndexBuildEventTranslator.INSTANCE);

    eventTranslators
        .addBinding(DataExplorerAccessedAudit.TYPE_NAME)
        .toInstance(DataExplorerAccessedAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(DataExplorerAccessedEvent.TYPE_NAME)
        .toInstance(DataExplorerAccessedEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(DataExplorerEvent.TYPE_NAME)
        .toInstance(DataExplorerEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(DataExplorerAudit.TYPE_NAME)
        .toInstance(DataExplorerAuditTranslator.INSTANCE);

    // Federation Events
    eventTranslators
        .addBinding(FederationSettingsAudit.TYPE_NAME)
        .toInstance(FederationSettingsAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(FederationSettingsEvent.TYPE_NAME)
        .toInstance(FederationSettingsEventTranslator.INSTANCE);

    // Access Events
    eventTranslators
        .addBinding(GlobalAccessListAudit.TYPE_NAME)
        .toInstance(GlobalAccessListAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(GlobalAccessListEvent.TYPE_NAME)
        .toInstance(GlobalAccessListEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(AuthnServiceAudit.TYPE_NAME)
        .toInstance(AuthnServiceAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(ServiceAccountEvent.TYPE_NAME)
        .toInstance(ServiceAccountEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(ServiceAccountAudit.TYPE_NAME)
        .toInstance(ServiceAccountAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(GlobalServiceAccountEvent.TYPE_NAME)
        .toInstance(GlobalServiceAccountEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemAlertGlobalServiceAccountEvent.TYPE_NAME)
        .toInstance(SystemAlertGlobalServiceAccountEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(GlobalServiceAccountAudit.TYPE_NAME)
        .toInstance(GlobalServiceAccountAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(AccessAuditEvent.TYPE_NAME)
        .toInstance(AccessAuditEventTranslator.INSTANCE);
    eventTranslators.addBinding(AccessEvent.TYPE_NAME).toInstance(AccessEventTranslator.INSTANCE);

    // Billing and Payments Events
    eventTranslators.addBinding(BillingEvent.TYPE_NAME).toInstance(BillingEventTranslator.INSTANCE);
    eventTranslators.addBinding(BillingAudit.TYPE_NAME).toInstance(BillingAuditTranslator.INSTANCE);
    eventTranslators.addBinding(PartnerEvent.TYPE_NAME).toInstance(PartnerEventTranslator.INSTANCE);
    eventTranslators.addBinding(PartnerAudit.TYPE_NAME).toInstance(PartnerAuditTranslator.INSTANCE);

    // Cron Events
    eventTranslators
        .addBinding(SystemCronJobEvent.TYPE_NAME)
        .toInstance(SystemCronJobEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemCronJobStatusEvent.TYPE_NAME)
        .toInstance(SystemCronJobStatusEventTranslator.INSTANCE);
    eventTranslators.addBinding(CronAudit.TYPE_NAME).toInstance(CronAuditTranslator.INSTANCE);

    // Misc Admin Events
    eventTranslators
        .addBinding(AppSettingsChangeAudit.TYPE_NAME)
        .toInstance(AppSettingsChangeAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemLogEvent.TYPE_NAME)
        .toInstance(SystemLogEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemConfigChangeAudit.TYPE_NAME)
        .toInstance(SystemConfigChangeAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemMaasEvent.TYPE_NAME)
        .toInstance(SystemMaasEventTranslator.INSTANCE);

    // Alert Events
    eventTranslators.addBinding(AlertAudit.TYPE_NAME).toInstance(AlertAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(AlertConfigAudit.TYPE_NAME)
        .toInstance(AlertConfigAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(FlappingEvent.TYPE_NAME)
        .toInstance(FlappingEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SystemAlertProcessingEvent.TYPE_NAME)
        .toInstance(SystemAlertProcessingEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(MaintenanceWindowConfigAudit.TYPE_NAME)
        .toInstance(MaintenanceWindowConfigAuditTranslator.INSTANCE);

    // Support Events
    eventTranslators.addBinding(SupportAudit.TYPE_NAME).toInstance(SupportAuditTranslator.INSTANCE);
    eventTranslators.addBinding(SupportEvent.TYPE_NAME).toInstance(SupportEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(SupportCaseAudit.TYPE_NAME)
        .toInstance(SupportCaseAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(SupportCaseEvent.TYPE_NAME)
        .toInstance(SupportCaseEventTranslator.INSTANCE);

    // AuthZ Events
    eventTranslators
        .addBinding(ResourceEvent.TYPE_NAME)
        .toInstance(ResourceEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(ResourceAudit.TYPE_NAME)
        .toInstance(ResourceAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(UserGroupEvent.TYPE_NAME)
        .toInstance(UserGroupEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(UserGroupAudit.TYPE_NAME)
        .toInstance(UserGroupAuditTranslator.INSTANCE);

    eventTranslators
        .addBinding(RateLimitSuspensionAudit.TYPE_NAME)
        .toInstance(RateLimitSuspensionAuditTranslator.INSTANCE);

    eventTranslators.addBinding(AgentEvent.TYPE_NAME).toInstance(AgentEventTranslator.INSTANCE);

    // Automation Events
    eventTranslators
        .addBinding(AutomationConfigAudit.TYPE_NAME)
        .toInstance(AutomationConfigAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(AutomationConfigEvent.TYPE_NAME)
        .toInstance(AutomationConfigEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(LogCollectionRequestAudit.TYPE_NAME)
        .toInstance(LogCollectionRequestAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(LogCollectionDownloadAudit.TYPE_NAME)
        .toInstance(LogCollectionDownloadAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(BiConnectorEvent.TYPE_NAME)
        .toInstance(BiConnectorEventTranslator.INSTANCE);

    // Account Events
    eventTranslators
        .addBinding(DeviceCodeAudit.TYPE_NAME)
        .toInstance(DeviceCodeAuditTranslator.INSTANCE);

    // Atlas Search Events
    eventTranslators
        .addBinding(FTSIndexAudit.TYPE_NAME)
        .toInstance(FTSIndexAuditTranslator.INSTANCE);
    eventTranslators
        .addBinding(SearchDeploymentAudit.TYPE_NAME)
        .toInstance(SearchDeploymentAuditTranslator.INSTANCE);
    eventTranslators.addBinding(MongotEvent.TYPE_NAME).toInstance(MongotEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(CrashLogEvent.TYPE_NAME)
        .toInstance(CrashLogEventTranslator.INSTANCE);

    eventTranslators
        .addBinding(ConfigNamespaceAuditEvent.TYPE_NAME)
        .toInstance(ConfigNamespaceAuditEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(ConfigApplicationPropertyAuditEvent.TYPE_NAME)
        .toInstance(ConfigApplicationPropertyAuditEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(ConfigFeatureFlagAuditEvent.TYPE_NAME)
        .toInstance(ConfigFeatureFlagAuditEventTranslator.INSTANCE);

    eventTranslators
        .addBinding(ProactiveOperationEvent.TYPE_NAME)
        .toInstance(ProactiveOperationEventTranslator.INSTANCE);
    eventTranslators
        .addBinding(CaptainLogAudit.TYPE_NAME)
        .toInstance(CaptainLogAuditTranslator.INSTANCE);

    eventTranslators
        .addBinding(BulkAllocationAuditEvent.TYPE_NAME)
        .toInstance(BulkAllocationAuditEventTranslator.INSTANCE);

    // Atlas Charts events
    eventTranslators.addBinding(ChartsAudit.TYPE_NAME).toInstance(ChartsAuditTranslator.INSTANCE);
    eventTranslators.addBinding(AdminEvent.TYPE_NAME).toInstance(AdminEventTranslator.INSTANCE);

    install(new EventCommitterModule());
  }
}
