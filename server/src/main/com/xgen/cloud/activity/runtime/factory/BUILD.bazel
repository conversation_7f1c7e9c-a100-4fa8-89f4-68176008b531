load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "factory",
    srcs = glob(["**/*.java"]),
    visibility = [
        "//server/src/main:__subpackages__",
    ],
    deps = [
        "//server/src/main",  # keep: gazelle cannot resolve this dep in all cases, since it uses filegroups.
        "//server/src/main/com/xgen/cloud/activity/_public/model",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/svc/mms/model/event",
        "//third_party:guava",
        "@maven//:com_fasterxml_jackson_core_jackson_databind",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:org_mongodb_bson",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
