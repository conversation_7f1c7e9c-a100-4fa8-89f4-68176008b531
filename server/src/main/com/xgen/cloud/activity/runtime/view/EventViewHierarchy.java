package com.xgen.cloud.activity.runtime.view;

import static com.fasterxml.jackson.annotation.JsonTypeInfo.As.EXISTING_PROPERTY;
import static com.fasterxml.jackson.annotation.JsonTypeInfo.Id.NAME;
import static com.xgen.cloud.abtesting._public.view.BulkAllocationAuditEventTypeView.Name.AB_TEST_BULK_ALLOCATION_SUBMITTED;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_DATA_LAKE_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_ENVOY_SERVERLESS_FILTERS_ACCESS_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_ENVOY_SERVERLESS_HEALTH_CHECK_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_ENVOY_SERVERLESS_HTTP_CONNECTION_MANAGER_ACCESS_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_ENVOY_SERVERLESS_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_ENVOY_SERVERLESS_TCP_PROXY_ACCESS_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_MIGRATION_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_MLAB_SHARED_MIGRATION_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_MONGODUMP_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_MONGOD_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_MONGOMIRROR_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_MONGORESTORE_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_MONGOSQLD_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_MONGOS_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_MONGOT_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_PROXY_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_SEARCH_ENVOY_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_TENANT_UPGRADE_SNAPSHOTS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ATLAS_USER_IDENTITY_SERVICE_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.AUTOMATION_AGENT_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.AUTOMATION_AGENT_LOGS_CSV_DOWNLOAD;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.BACKUP_AGENT_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.BACKUP_AGENT_LOGS_CSV_DOWNLOAD;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.BACKUP_MONGOD_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.CPS_BACKUP_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.DB_CHECK_HEALTHLOG_DOWNLOAD;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.DB_CHECK_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.DB_CHECK_LOGS_CSV_DOWNLOAD;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.DOWNLOADED_MONITORING_GROUP_MONGOD_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.DOWNLOADED_MONITORING_HOST_SPECIFIC_MONGOD_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.EMPLOYEE_DOWNLOADED_CLUSTER_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ENVOY_SERVERLESS_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.FTDC_AUTOMATION_AGENT_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.FTDC_AUTOMATION_AGENT_LOGS_CSV_DOWNLOAD;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.MONGODB_ACCESS_HISTORY;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.MONITORING_AGENT_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.MONITORING_AGENT_LOGS_CSV_DOWNLOAD;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.MONITORING_DAILY_PING;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.MONITORING_GROUP_PING;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.MONITORING_LATEST_HOST_SPECIFIC_PING;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.MONITORING_MONGOD_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ONLINE_ARCHIVE_AGENT_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.ONLINE_ARCHIVE_AGENT_LOGS_CSV_DOWNLOAD;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.PERFORMANCE_ADVISOR;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.PIT_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.PUBLIC_API_LATEST_MONITORING_GROUP_PING;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.PUBLIC_API_LATEST_MONITORING_HOST_SPECIFIC_PING;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.PUBLIC_API_MANAGED_SLOW_MS_FEATURE_FLAG;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.PUBLIC_API_SEARCH_QUERY_TELEMETRY_FLAG;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.QUERY_SHAPE_INSIGHTS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.REAL_TIME_PERFORMANCE_PANEL;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.TOGGLEABLE_FEATURE_FLAG;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.VIEWED_AUTOMATION_MONGOD_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.VIEWED_MONITORING_HOST_SPECIFIC_MONGOD_LOGS;
import static com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView.Name.VISUAL_PROFILER;
import static com.xgen.cloud.activity._public.model.event.view.BiConnectorEventTypeView.Name.BI_CONNECTOR_DOWN;
import static com.xgen.cloud.activity._public.model.event.view.BiConnectorEventTypeView.Name.BI_CONNECTOR_UP;
import static com.xgen.cloud.activity._public.model.event.view.CaptainLogAuditTypeView.Name.CAPTAIN_LOG;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.ADD_HOST_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.ADD_HOST_TO_REPLICA_SET_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.ALERT_HOST_SSH_SESSION_STARTED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.ATTEMPT_KILLOP_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.ATTEMPT_KILLSESSION_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.AUTO_CREATED_INDEX_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.DB_PROFILER_DISABLE_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.DB_PROFILER_ENABLE_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.DELETE_HOST_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.DISABLE_HOST_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HIDE_AND_DISABLE_HOST_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HIDE_HOST_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_DISK_SPACE_INSUFFICIENT_FOR_SEARCH_INDEX_REBUILD;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_DOWN;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_DOWNGRADED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_ENOUGH_DISK_SPACE;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_EXPOSED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_HAS_INDEX_SUGGESTIONS;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_HAS_INDEX_SUGGESTIONS_WITHDRAWN;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_IP_CHANGED_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_LOCKED_DOWN;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_MONGOT_APPROACHING_STOP_REPLICATION;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_MONGOT_CRASHING_OOM;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_MONGOT_CRASHING_OOM_MTM;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_MONGOT_RECOVERED_OOM;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_MONGOT_RECOVERED_OOM_MTM;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_MONGOT_RESUME_REPLICATION;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_MONGOT_STOP_REPLICATION;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_MONGOT_SUFFICIENT_DISK_SPACE;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_NOT_ENOUGH_DISK_SPACE;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_NOW_PRIMARY;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_NOW_SECONDARY;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_NOW_STANDALONE;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_RECOVERED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_RECOVERING;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_RESTARTED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_ROLLBACK;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_SECURITY_CHECKUP_MET;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_SECURITY_CHECKUP_NOT_MET;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_SSH_SESSION_ENDED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_SSL_CERTIFICATE_CURRENT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_SSL_CERTIFICATE_STALE;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_UP;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_UPGRADED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_VERSION_BEHIND;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_VERSION_CHANGED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_VERSION_CURRENT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.HOST_X509_CERTIFICATE_CERTIFICATE_GENERATED_FOR_SUPPORT_ACCESS;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.NDS_CLOUD_PROVIDER_CONSOLE_INFORMATION_DOWNLOADED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.NDS_HOST_LOGS_DOWNLOADED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.NEW_HOST;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.PAUSE_HOST_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.PUSH_BASED_LOG_EXPORT_DROPPED_LOG;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.PUSH_BASED_LOG_EXPORT_RESUMED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.PUSH_BASED_LOG_EXPORT_STOPPED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.REMOVE_HOST_FROM_REPLICA_SET_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.RESUME_HOST_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.SSH_KEY_NDS_HOST_ACCESS_ATTEMPT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.SSH_KEY_NDS_HOST_ACCESS_GRANTED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.SSH_KEY_NDS_HOST_ACCESS_LEVEL_CHANGED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.SSH_KEY_NDS_HOST_ACCESS_REFRESHED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.SSH_KEY_NDS_HOST_ACCESS_REQUESTED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.UNDELETE_HOST_AUDIT;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.VERSION_BEHIND;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.VERSION_CHANGED;
import static com.xgen.cloud.activity._public.model.event.view.HostEventTypeView.Name.VERSION_CURRENT;
import static com.xgen.cloud.activity._public.model.event.view.HostMetricEventTypeView.Name.INSIDE_METRIC_THRESHOLD;
import static com.xgen.cloud.activity._public.model.event.view.HostMetricEventTypeView.Name.OUTSIDE_METRIC_THRESHOLD;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.CONFIGURATION_CHANGED;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.ENOUGH_HEALTHY_MEMBERS;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.FLEX_PROXIES_REPORTING;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.FLEX_PROXIES_STOPPED_REPORTING;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.MEMBER_ADDED;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.MEMBER_REMOVED;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.NO_PRIMARY;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.ONE_PRIMARY;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.PRIMARY_ELECTED;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.REPLICATION_OPLOG_WINDOW_HEALTHY;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.REPLICATION_OPLOG_WINDOW_RUNNING_OUT;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.SERVERLESS_PROXIES_REPORTING;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.SERVERLESS_PROXIES_STOPPED_REPORTING;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.TOO_FEW_HEALTHY_MEMBERS;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.TOO_MANY_ELECTIONS;
import static com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView.Name.TOO_MANY_UNHEALTHY_MEMBERS;
import static com.xgen.cloud.activity.runtime.view.EventViewHierarchy.EVENT_TYPE_FIELD;
import static com.xgen.cloud.alerts.alert._public.model.view.AlertAuditTypeView.Name.ALERT_ACKNOWLEDGED_AUDIT;
import static com.xgen.cloud.alerts.alert._public.model.view.AlertAuditTypeView.Name.ALERT_UNACKNOWLEDGED_AUDIT;
import static com.xgen.cloud.alerts.alert._public.model.view.AlertConfigAuditTypeView.Name.ALERT_CONFIG_ADDED_AUDIT;
import static com.xgen.cloud.alerts.alert._public.model.view.AlertConfigAuditTypeView.Name.ALERT_CONFIG_CHANGED_AUDIT;
import static com.xgen.cloud.alerts.alert._public.model.view.AlertConfigAuditTypeView.Name.ALERT_CONFIG_DELETED_AUDIT;
import static com.xgen.cloud.alerts.alert._public.model.view.AlertConfigAuditTypeView.Name.ALERT_CONFIG_DISABLED_AUDIT;
import static com.xgen.cloud.alerts.alert._public.model.view.AlertConfigAuditTypeView.Name.ALERT_CONFIG_ENABLED_AUDIT;
import static com.xgen.cloud.alerts.alert._public.model.view.MaintenanceWindowConfigAuditTypeView.Name.MAINTENANCE_WINDOW_ADDED_AUDIT;
import static com.xgen.cloud.alerts.alert._public.model.view.MaintenanceWindowConfigAuditTypeView.Name.MAINTENANCE_WINDOW_CHANGED_AUDIT;
import static com.xgen.cloud.alerts.alert._public.model.view.MaintenanceWindowConfigAuditTypeView.Name.MAINTENANCE_WINDOW_DELETED_AUDIT;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_ACCESS_LIST_ENTRY_ADDED;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_ACCESS_LIST_ENTRY_DELETED;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_ADDED_TO_GROUP;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_CREATED;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_DELETED;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_DETAILS_CHANGED;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_REMOVED_FROM_GROUP;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_ROLES_CHANGED;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_SECRET_ADDED;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_SECRET_DELETED;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_UI_IP_ACCESS_LIST_INHERITANCE_DISABLED;
import static com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView.Name.SERVICE_ACCOUNT_UI_IP_ACCESS_LIST_INHERITANCE_ENABLED;
import static com.xgen.cloud.atm.activity._public.audit.view.AutomationConfigEventTypeView.Name.AUTOMATION_CONFIG_PUBLISHED_AUDIT;
import static com.xgen.cloud.authz.core._public.model.event.view.UserGroupEventTypeView.Name.USER_GROUP_CREATED;
import static com.xgen.cloud.authz.core._public.model.event.view.UserGroupEventTypeView.Name.USER_GROUP_DELETED;
import static com.xgen.cloud.authz.core._public.model.event.view.UserGroupEventTypeView.Name.USER_GROUP_MEMBER_ADDED;
import static com.xgen.cloud.authz.core._public.model.event.view.UserGroupEventTypeView.Name.USER_GROUP_MEMBER_REMOVED;
import static com.xgen.cloud.authz.core._public.model.event.view.UserGroupEventTypeView.Name.USER_GROUP_NAME_CHANGED;
import static com.xgen.cloud.authz.core._public.model.event.view.UserGroupEventTypeView.Name.USER_GROUP_POLICIES_CHANGED;
import static com.xgen.cloud.authz.resource._public.model.event.view.ResourceEventTypeView.Name.CLUSTER_TAGS_MODIFIED;
import static com.xgen.cloud.authz.resource._public.model.event.view.ResourceEventTypeView.Name.GROUP_TAGS_MODIFIED;
import static com.xgen.cloud.authz.resource._public.model.event.view.ResourceEventTypeView.Name.TAGS_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.ACCOUNT_DOWNGRADED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.ACCOUNT_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.ACCOUNT_UPGRADED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.AWS_BILLING_ACCOUNT_CREDIT_ISSUED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.AWS_USAGE_REPORTED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.AZURE_BILLING_ACCOUNT_CREDIT_ISSUED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.AZURE_USAGE_REPORTED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.BECAME_PAYING_ORG;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.BILLABLE_HOSTS_INCREASED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.BILLING_EMAIL_ADDRESS_ADDED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.BILLING_EMAIL_ADDRESS_CHANGED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.BILLING_EMAIL_ADDRESS_REMOVED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.BRAINTREE_CHARGE_FAILED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CHARGE_FAILED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CHARGE_PENDING_REVERSAL;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CHARGE_PROCESSING;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CHARGE_SUCCEEDED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CHECK_PAYMENT_RECEIVED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_AMOUNT_CENTS_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_AMOUNT_REMAINING_CENTS_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_AWS_CUSTOMER_ID_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_AWS_PRODUCT_CODE_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_AZURE_PRIVATE_PLAN_ID_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_AZURE_SUBSCRIPTION_ID_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_CARD_ABOUT_TO_EXPIRE;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_CARD_CURRENT;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_ELASTIC_INVOICING_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_END_DATE_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_GCP_MARKETPLACE_ENTITLEMENT_ID_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_ISSUED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_PULLED_FWD;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_SFOLID_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_START_DATE_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_TOTAL_BILLED_CENTS_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.CREDIT_TYPE_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.DAILY_BILLING_EXECUTED_THROUGH_API;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.DAILY_BILL_OVER_THRESHOLD;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.DAILY_BILL_UNDER_THRESHOLD;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.DISCOUNT_APPLIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.DUPLICATE_SUBSCRIPTION_USAGE;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.ELASTIC_INVOICING_MODE_ACTIVATED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.ELASTIC_INVOICING_MODE_DEACTIVATED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.EVERGREEN_DEAL_CANCELLED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.EVERGREEN_PRIORITY_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.GCP_BILLING_ACCOUNT_CREDIT_ISSUED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.GCP_USAGE_REPORTED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.GRACE_PERIOD_ACTIVATED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.GRACE_PERIOD_NO_LONGER_IN_EFFECT;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.INITIATE_SALESFORCE_SERVICE_CLOUD_SYNC;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.INVOICE_ADDRESS_ADDED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.INVOICE_ADDRESS_CHANGED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.INVOICE_AMOUNTS_RECALCULATED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.INVOICE_BILLED_DOES_NOT_EQUAL_LINE_ITEMS_TOTAL;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.INVOICE_BILLED_EQUALS_LINE_ITEMS_TOTAL;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.INVOICE_CLOSED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.INVOICE_STATUS_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.LEGACY_REBILL_EXECUTED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.METER_USAGE_DELETED_THROUGH_API;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.METER_USAGE_UNDELETED_THROUGH_API;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.MISSING_PAYMENT_METHOD;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.MULTIPLE_INVOICES_FOR_MONTH;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.NEW_LINKED_ORG;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.NO_DUPLICATE_SUBSCRIPTION_USAGE;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.NO_STALE_PENDING_INVOICES;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.ONE_INVOICE_FOR_MONTH;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.ONE_PENDING_INVOICE;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.ORG_LINKED_TO_PAYING_ORG;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.ORG_UNLINKED_FROM_PAYING_ORG;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.ORG_UNLINK_CANCELLED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.ORG_UNLINK_REQUESTED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PAYMENT_FORGIVEN;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PAYMENT_METHODS_REMOVED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PAYMENT_METHOD_ADDED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PAYMENT_UPDATED_THROUGH_API;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PENDING_DEAL_ACTIVATION_ADDED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PENDING_DEAL_ACTIVATION_CANCELED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PENDING_DEAL_ACTIVATION_FAILED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PENDING_DEAL_APPLIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PENDING_INVOICE_OVER_THRESHOLD;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PENDING_INVOICE_UNDER_THRESHOLD;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PREPAID_PLAN_ACTIVATED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PREPAID_PLAN_MISSING_SKU;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PREPAID_PLAN_MODIFIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PREPAID_PLAN_NOT_MISSING_SKU;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PROJECT_LEVEL_SUPPORT_ENABLED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.PROMO_CODE_APPLIED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.REFUND_ISSUED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.STALE_PENDING_INVOICES;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.SUPPORT_PLAN_ACTIVATED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.SUPPORT_PLAN_CANCELLATION_SCHEDULED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.SUPPORT_PLAN_CANCELLED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.TARGETED_REBILL_EXECUTED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.TARGETED_REBILL_EXECUTED_THROUGH_API;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.TERMINATE_PAID_SERVICES;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.TOO_MANY_PENDING_INVOICES;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.UNLINKED_ORG;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.VERCEL_USAGE_REPORTED;
import static com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView.Name.WIRE_TRANSFER_PAYMENT_RECEIVED;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_DEPLOYMENT_CONFIG_IS_MISSING;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_DEPLOYMENT_CONFIG_IS_PRESENT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_IN_UNEXPECTED_STATE;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_JOB_NOT_BUSY;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_JOB_TOO_BUSY;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_LATE_GROOM_JOB;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_LATE_INTEGRITY_CHECK_JOB;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_LATE_TRACKING_JOB;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_NOT_LATE_GROOM_JOB;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_NOT_LATE_INTEGRITY_CHECK_JOB;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_NOT_LATE_TRACKING_JOB;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_RECOVERED;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_SUPPORTED;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_TOO_MANY_HEAD_START_ATTEMPTS;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_TOO_MANY_RETRIES;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BACKUP_UNSUPPORTED;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BAD_CLUSTERSHOTS;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BLOCKSTORE_JOB_RECOVERED;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.BLOCKSTORE_JOB_TOO_MANY_RETRIES;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.CLUSTERSHOT_DELETED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.CLUSTERSHOT_EXPIRY_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.CLUSTER_BLACKLIST_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.CLUSTER_CHECKPOINT_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.CLUSTER_CREDENTIAL_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.CLUSTER_DENYLIST_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.CLUSTER_SNAPSHOT_SCHEDULE_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.CLUSTER_STATE_CHANGED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.CLUSTER_STORAGE_ENGINE_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.CONSISTENT_BACKUP_CONFIGURATION;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.DAEMON_AVAILABLE_FOR_QUERYABLE_RESTORE_JOB;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.DISABLE_BACKUP_PRIVATELINK_EVENT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.ENABLE_BACKUP_PRIVATELINK_EVENT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.GOOD_CLUSTERSHOT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.INCONSISTENT_BACKUP_CONFIGURATION;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.INITIAL_SYNC_FINISHED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.INITIAL_SYNC_STARTED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.INTERNAL_DIAGNOSTIC_RESTORE_REQUESTED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.LATE_SNAPSHOT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.LATE_WTC_SNAPSHOT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.NO_DAEMON_AVAILABLE_FOR_QUERYABLE_RESTORE_JOB;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.NO_RS_BIND_ERROR;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.ONDEMAND_SNAPSHOT_REQUESTED;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.OPLOG_BEHIND;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.OPLOG_CURRENT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.RESTORE_REQUESTED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.RESYNC_PERFORMED;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.RESYNC_REQUIRED;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.RS_BIND_ERROR;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.RS_BLACKLIST_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.RS_CREDENTIAL_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.RS_DENYLIST_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.RS_ROTATE_MASTER_KEY_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.RS_SNAPSHOT_SCHEDULE_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.RS_STATE_CHANGED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.RS_STORAGE_ENGINE_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.SNAPSHOT_DELETED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.SNAPSHOT_EXPIRY_UPDATED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.SYNC_PENDING_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.SYNC_REQUIRED_AUDIT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.SYNC_SLICE_HAS_NOT_PROGRESSED;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.SYNC_SLICE_PROGRESSED;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.TIMELY_SNAPSHOT;
import static com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView.Name.TIMELY_WTC_SNAPSHOT;
import static com.xgen.cloud.clusters._public.model.view.ClusterEventTypeView.Name.CLUSTER_AGENT_HAS_PERSISTENT_MOVE_COLLECTION_ERROR;
import static com.xgen.cloud.clusters._public.model.view.ClusterEventTypeView.Name.CLUSTER_AGENT_HAS_RS_RECONFIG_ERROR;
import static com.xgen.cloud.clusters._public.model.view.ClusterEventTypeView.Name.CLUSTER_AGENT_IN_CRASH_LOOP;
import static com.xgen.cloud.clusters._public.model.view.ClusterEventTypeView.Name.CLUSTER_MONGOS_IS_MISSING;
import static com.xgen.cloud.clusters._public.model.view.ClusterEventTypeView.Name.CLUSTER_MONGOS_IS_PRESENT;
import static com.xgen.cloud.config._public.view.ConfigApplicationPropertyAuditEventTypeView.Name.CONFIG_APPLICATION_PROPERTY_CREATED;
import static com.xgen.cloud.config._public.view.ConfigApplicationPropertyAuditEventTypeView.Name.CONFIG_APPLICATION_PROPERTY_DELETED;
import static com.xgen.cloud.config._public.view.ConfigApplicationPropertyAuditEventTypeView.Name.CONFIG_APPLICATION_PROPERTY_UPDATED;
import static com.xgen.cloud.config._public.view.ConfigFeatureFlagAuditEventTypeView.Name.CONFIG_FEATURE_FLAG_UPDATED;
import static com.xgen.cloud.config._public.view.ConfigNamespaceAuditEventTypeView.Name.CONFIG_NAMESPACE_CREATED;
import static com.xgen.cloud.config._public.view.ConfigNamespaceAuditEventTypeView.Name.CONFIG_NAMESPACE_DELETED;
import static com.xgen.cloud.config._public.view.ConfigNamespaceAuditEventTypeView.Name.CONFIG_NAMESPACE_UPDATED;
import static com.xgen.cloud.explorer.activity._public.event.view.DataExplorerAccessedEventTypeView.Name.DATA_EXPLORER;
import static com.xgen.cloud.explorer.activity._public.event.view.DataExplorerAccessedEventTypeView.Name.DATA_EXPLORER_CRUD;
import static com.xgen.cloud.explorer.activity._public.event.view.DataExplorerAccessedEventTypeView.Name.DATA_EXPLORER_CRUD_ATTEMPT;
import static com.xgen.cloud.explorer.activity._public.event.view.DataExplorerAccessedEventTypeView.Name.DATA_EXPLORER_CRUD_ERROR;
import static com.xgen.cloud.explorer.activity._public.event.view.DataExplorerEventTypeView.Name.DATA_EXPLORER_SESSION_CREATED;
import static com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView.Name.FTS_INDEXES_RESTORED;
import static com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView.Name.FTS_INDEXES_RESTORE_FAILED;
import static com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView.Name.FTS_INDEXES_SYNONYM_MAPPING_INVALID;
import static com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView.Name.FTS_INDEX_BUILD_COMPLETE;
import static com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView.Name.FTS_INDEX_BUILD_FAILED;
import static com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView.Name.FTS_INDEX_CLEANED_UP;
import static com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView.Name.FTS_INDEX_CREATED;
import static com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView.Name.FTS_INDEX_DELETED;
import static com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView.Name.FTS_INDEX_DELETION_FAILED;
import static com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView.Name.FTS_INDEX_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.AtlasResourcePolicyAuditTypeView.Name.RESOURCE_POLICY_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.AtlasResourcePolicyAuditTypeView.Name.RESOURCE_POLICY_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.AtlasResourcePolicyAuditTypeView.Name.RESOURCE_POLICY_MODIFIED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.AtlasResourcePolicyAuditTypeView.Name.RESOURCE_POLICY_VIOLATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ADMIN_CLUSTER_LOCK_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ADMIN_NOTE_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_AUTO_DEFER_DISABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_AUTO_DEFER_ENABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_DEFERRED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_PROTECTED_HOURS_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_PROTECTED_HOURS_MODIFIED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_PROTECTED_HOURS_REMOVED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_RESET_BY_ADMIN;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_SCHEDULED_FOR_NEXT_WINDOW;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_START_ASAP;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_WINDOW_ADDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_WINDOW_MODIFIED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_MAINTENANCE_WINDOW_REMOVED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_SQL_SCHEDULED_UPDATE_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_SQL_SCHEDULED_UPDATE_MODIFIED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ATLAS_SQL_SCHEDULED_UPDATE_REMOVED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.AUDIT_LOG_CONFIGURATION_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.AUTO_HEALING_ACTION;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.AUTO_HEALING_CANNOT_REPAIR_INTERNAL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.AUTO_HEALING_CANNOT_RESYNC_INTERNAL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.AUTO_HEALING_FIXED_INTERNAL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.AUTO_HEALING_REQUESTED_CRITICAL_INSTANCE_POWER_CYCLE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.AUTO_HEALING_REQUESTED_INSTANCE_REPLACEMENT;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.AUTO_HEALING_REQUESTED_NODE_RESYNC;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.AZURE_CLUSTER_PREFERRED_STORAGE_TYPE_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.AZ_BALANCING_OVERRIDE_MODIFIED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.BAAS_RELEVANT_CLUSTER_UPDATE_COMPLETED_INTERNAL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLEAR_UNPROVISIONED_TARGET_GROUPS_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLOUD_PROVIDER_ACCESS_AWS_IAM_ROLE_ADDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLOUD_PROVIDER_ACCESS_AWS_IAM_ROLE_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLOUD_PROVIDER_ACCESS_AWS_IAM_ROLE_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLOUD_PROVIDER_ACCESS_AZURE_SERVICE_PRINCIPAL_ADDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLOUD_PROVIDER_ACCESS_AZURE_SERVICE_PRINCIPAL_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLOUD_PROVIDER_ACCESS_AZURE_SERVICE_PRINCIPAL_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLOUD_PROVIDER_ACCESS_GCP_SERVICE_ACCOUNT_ADDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLOUD_PROVIDER_ACCESS_GCP_SERVICE_ACCOUNT_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLOUD_PROVIDER_ACCESS_GCP_SERVICE_ACCOUNT_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLOUD_PROVIDER_THROTTLE_STATE_MODIFIED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_AUTOMATICALLY_PAUSED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_AUTOMATION_CONFIG_PUBLISHED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_BLOCK_WRITE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_CANCELING_SHARD_DRAIN_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_DELETE_SUBMITTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_DELETE_SUBMITTED_INTERNAL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_FORCE_PLANNED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_FORCE_RECONFIG_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IMPORT_ACKNOWLEDGED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IMPORT_CANCELLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IMPORT_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IMPORT_CUTOVER;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IMPORT_EXPIRED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IMPORT_EXTENDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IMPORT_RESTART_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IMPORT_STARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IMPORT_VALIDATION_FAIL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IMPORT_VALIDATION_SUCCESS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_ADMIN_BACKUP_SNAPSHOT_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_AGENT_API_KEY_ROTATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_CONFIG_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_DISABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_ENABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_FAMILY_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_REPLACED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_REPLACE_CLEARED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_RESTARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_RESYNC_CLEARED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_RESYNC_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_SSL_REVOKED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_SSL_ROTATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_SSL_ROTATED_PER_CLUSTER;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_STOP_START;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_INSTANCE_UPDATE_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IP_MIGRATED_FINAL_ROUND;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IP_MIGRATED_FIRST_ROUND;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IP_MIGRATED_SECOND_ROUND;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_IP_ROLLED_BACK;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_LINKED_TO_VERCEL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_MIGRATE_BACK_TO_AWS_MANAGED_IP_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_MONGOT_PROCESS_ARGS_UPDATE_SUBMITTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_OPLOG_RESIZED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_OS_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_PREFERRED_CPU_ARCHITECTURE_MODIFIED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_READY;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_REGIONAL_OUTAGE_SIMULATION_CANCELLED_CLUSTER_PAUSE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_REGIONAL_OUTAGE_SIMULATION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_REGIONAL_OUTAGE_SIMULATION_END_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_REGIONAL_OUTAGE_SIMULATION_FAILED_TO_START;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_REGIONAL_OUTAGE_SIMULATION_STARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_RESURRECTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_ROLLING_RESYNC_CANCELED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_ROLLING_RESYNC_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_ROLLING_RESYNC_FAILED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_ROLLING_RESYNC_STARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_SERVER_PARAMETERS_UPDATE_SUBMITTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_SET_CPU_SOCKET_BINDING;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_THROTTLE_STATE_MODIFIED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_UNBLOCK_WRITE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_UNLINKED_FROM_VERCEL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_UPDATE_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_UPDATE_STARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_UPDATE_STARTED_INTERNAL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_UPDATE_SUBMITTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CLUSTER_UPDATE_SUBMITTED_INTERNAL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CONTAINER_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CONTAINER_SUBNETS_UPDATE_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.CUSTOMER_X509_CRL_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.DATADOG_LOG_STREAMING_CONFIGURATION_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.DATADOG_LOG_STREAMING_DISABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.DATADOG_LOG_STREAMING_ENABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.DATA_API_SETUP_FOR_VERCEL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.DATA_FEDERATION_QUERY_LIMIT_CONFIGURED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.DATA_FEDERATION_QUERY_LIMIT_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.DATA_LAKE_QUERY_LOGS_DOWNLOADED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.DATA_PROCESSING_REGION_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.DEVICE_SYNC_DEBUG_ACCESS_GRANTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.DEVICE_SYNC_DEBUG_ACCESS_REVOKED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.DEVICE_SYNC_DEBUG_X509_CERT_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.EMPLOYEE_ACCESS_GRANTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.EMPLOYEE_ACCESS_REVOKED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ENCRYPTION_AT_REST_CONFIGURATION_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ENCRYPTION_AT_REST_CONFIGURATION_VALIDATION_FAILED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ENCRYPTION_AT_REST_CONFIGURATION_VALIDATION_SUCCEEDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.EXTERNAL_MAINTENANCE_CANCELED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.EXTERNAL_MAINTENANCE_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.EXTERNAL_MAINTENANCE_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.EXTRA_MAINTENANCE_DEFERRAL_GRANTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.FEATURE_FLAG_MAINTENANCE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.FEDERATED_DATABASE_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.FEDERATED_DATABASE_QUERY_LOGS_DOWNLOADED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.FEDERATED_DATABASE_REMOVED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.FEDERATED_DATABASE_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.FLEX_UPGRADE_STARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.FORCE_MIGRATE_FROM_AVAILABILITY_SETS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.FREE_UPGRADE_STARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.FTDC_SETTINGS_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.GROUP_AUTOMATION_CONFIG_PUBLISHED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.INDEPENDENT_SHARD_AUTO_SCALING_AVAILABLE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.INDEPENDENT_SHARD_SCALING_AVAILABLE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.INDEPENDENT_SHARD_SCALING_CLUSTER_MIGRATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.INDEPENDENT_SHARD_SCALING_CLUSTER_ROLLED_BACK;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.INGESTION_PIPELINE_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.INGESTION_PIPELINE_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.INGESTION_PIPELINE_DESTROYED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.INGESTION_PIPELINE_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.JOB_PRIORITY_CONFIG_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.JOB_PRIORITY_CONFIG_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.JOB_PRIORITY_CONFIG_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.JOB_PRIORITY_SETTING_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.JOB_PRIORITY_SETTING_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.JOB_PRIORITY_SETTING_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.KMIP_KEY_ROTATION_SCHEDULED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.LOG_STREAMING_CONFIGURATION_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.LOG_STREAMING_DISABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.LOG_STREAMING_ENABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MATERIAL_CLUSTER_UPDATE_COMPLETED_INTERNAL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGODB_LOGS_DOWNLOADED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGODB_ROLE_ADDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGODB_ROLE_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGODB_ROLE_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGODB_USER_ADDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGODB_USER_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGODB_USER_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGODB_USER_X509_CERT_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGODB_USER_X509_CERT_REVOKED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGOSQLD_LOGS_DOWNLOADED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGOTUNE_DISABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGOTUNE_ENABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGOTUNE_POLICIES_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGOTUNE_POLICY_DISABLED_BY_ADMIN;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGOTUNE_POLICY_ENABLED_BY_ADMIN;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGOTUNE_WRITE_BLOCK_POLICY_ELIGIBLE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGOTUNE_WRITE_BLOCK_POLICY_INELIGIBLE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONGOT_LOGS_DOWNLOADED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MONITORING_AGENT_OVERRIDES;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.MOVE_SKIPPED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.NDS_SET_CHEF_TARBALL_URI;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.NDS_SET_IMAGE_OVERRIDES;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.NETWORK_PERMISSION_ENTRY_ADDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.NETWORK_PERMISSION_ENTRY_REMOVED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.NETWORK_PERMISSION_ENTRY_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.NODE_ROLLING_RESYNC_SCHEDULED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.NRA_REPLACEMENT_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_ACTIVE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_DATA_EXPIRATION_RULE_DISABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_DATA_EXPIRATION_RULE_ENABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_DATA_EXPIRATION_RULE_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_DELETE_AFTER_DATE_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_ORPHANED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_PAUSED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_PAUSE_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_QUERY_LOGS_DOWNLOADED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_V3_MIGRATION_FAILED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_V3_MIGRATION_STARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ONLINE_ARCHIVE_V3_MIGRATION_SUCCEEDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.ORG_LIMIT_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.OS_MAINTENANCE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.OS_MAINTENANCE_REPLACEMENT;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.OS_MAINTENANCE_RESTART;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.OS_TUNE_FILE_OVERRIDES;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PENDING_INDEXES_CANCELED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PENDING_INDEXES_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PLANNING_FAILURE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PLAN_ABANDONED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PLAN_ASAP_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PLAN_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PLAN_FAILURE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PLAN_FAILURE_COUNT_RESET;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PLAN_STARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PRIVATE_NETWORK_ENDPOINT_ENTRY_ADDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PRIVATE_NETWORK_ENDPOINT_ENTRY_REMOVED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PRIVATE_NETWORK_ENDPOINT_ENTRY_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROCESS_RESTART_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROJECT_BYPASSED_MAINTENANCE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROJECT_ENABLE_EXTENDED_STORAGE_SIZES_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROJECT_LIMIT_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROJECT_LIVE_IMPORT_OVERRIDES_ADDED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROJECT_LIVE_IMPORT_OVERRIDES_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROJECT_LIVE_IMPORT_OVERRIDES_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROJECT_OPERATIONAL_LIMIT_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROJECT_SCHEDULED_MAINTENANCE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROJECT_SCHEDULED_MAINTENANCE_OUTSIDE_OF_PROTECTED_HOURS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROJECT_THROTTLE_STATE_MODIFIED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROXY_PANICKED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROXY_PROTOCOL_FOR_PRIVATE_LINK_MODE_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PROXY_RESTARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PUSH_BASED_LOG_EXPORT_CONFIGURATION_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PUSH_BASED_LOG_EXPORT_DISABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.PUSH_BASED_LOG_EXPORT_ENABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.QUERY_ENGINE_TENANT_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.QUERY_ENGINE_TENANT_REMOVED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.QUERY_ENGINE_TENANT_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.QUEUED_ADMIN_ACTION_CANCELLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.QUEUED_ADMIN_ACTION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.QUEUED_ADMIN_ACTION_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.QUEUED_ADMIN_ACTION_FAILED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.REGIONALIZED_PRIVATE_ENDPOINT_MODE_DISABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.REGIONALIZED_PRIVATE_ENDPOINT_MODE_ENABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.RELOAD_SSL_ON_PROCESSES;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.RELOAD_SSL_ON_PROCESSES_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.RESTRICTED_EMPLOYEE_ACCESS_BYPASS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.REVOKED_EMPLOYEE_ACCESS_BYPASS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.S3_LOG_STREAMING_CONFIGURATION_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.S3_LOG_STREAMING_DISABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.S3_LOG_STREAMING_ENABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.SAMPLE_DATASET_LOAD_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.SCHEDULED_MAINTENANCE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.SERVERLESS_UPGRADE_STARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.SERVERLESS_UPGRADE_TO_DEDICATED_FAILED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.SERVERLESS_UPGRADE_TO_DEDICATED_SUCCESSFUL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.SET_ENSURE_CLUSTER_CONNECTIVITY_AFTER_FOR_CLUSTER;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.SPLUNK_LOG_STREAMING_CONFIGURATION_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.SPLUNK_LOG_STREAMING_DISABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.SPLUNK_LOG_STREAMING_ENABLED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.SSL_CERTIFICATE_ISSUED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STEP_SKIPPED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAMS_AUDIT_LOG_CONFIGURATION_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_AWS_KINESIS_DATA_STREAMS_CONNECTION_ACCESSED_BY_APP_INTEGRATION;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_HTTPS_CONNECTION_ACCESSED_BY_APP_INTEGRATION;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_KAFKA_CONNECTION_ACCESSED_BY_APP_INTEGRATION;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_S3_CONNECTION_ACCESSED_BY_APP_INTEGRATION;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_TENANT_AUDIT_LOGS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_TENANT_AUDIT_LOGS_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_TENANT_CONNECTIONS_LISTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_TENANT_CONNECTION_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_TENANT_CONNECTION_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_TENANT_CONNECTION_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_TENANT_CONNECTION_VIEWED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_TENANT_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_TENANT_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.STREAM_TENANT_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.TENANT_CLUSTER_LIMIT_MODIFIED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.TENANT_CLUSTER_UPGRADE_FROM_MTM;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.TENANT_RESTORE_FAILED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.TENANT_SNAPSHOT_FAILED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.TENANT_UPGRADE_TO_SERVERLESS_FAILED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.TENANT_UPGRADE_TO_SERVERLESS_SUCCESSFUL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.TEST_FAILOVER_REQUESTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.UIS_PANICKED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.UNSET_USER_WRITE_BLOCK_MODE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.UPDATE_BUMPER_FILES;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.USER_SECURITY_SETTINGS_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView.Name.X509_USER_CERTIFICATE_GENERATED_BY_APP_INTEGRATION;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_INITIATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_INITIATED_ANALYTICS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_INITIATED_BASE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_MAX_INSTANCE_SIZE_FAIL_ANALYTICS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_MAX_INSTANCE_SIZE_FAIL_BASE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_OPLOG_FAIL_ANALYTICS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_OPLOG_FAIL_BASE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_SCALE_DOWN_FAIL_ANALYTICS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_SCALE_DOWN_FAIL_BASE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_SKIPPED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_SKIPPED_ANALYTICS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_SKIPPED_BASE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_TRIGGERED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_TRIGGERED_ANALYTICS;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_TRIGGERED_BASE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.COMPUTE_AUTO_SCALE_UNNECESSARY;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.DISK_AUTO_SCALE_INITIATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.DISK_AUTO_SCALE_MAX_DISK_SIZE_FAIL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.DISK_AUTO_SCALE_OPLOG_FAIL;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.PREDICTIVE_COMPUTE_AUTO_SCALE_INITIATED_BASE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.PREDICTIVE_COMPUTE_AUTO_SCALE_MAX_INSTANCE_SIZE_FAIL_BASE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView.Name.PREDICTIVE_COMPUTE_AUTO_SCALE_OPLOG_FAIL_BASE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditTypeView.Name.SERVERLESS_INSTANCE_BLOCKED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditTypeView.Name.SERVERLESS_INSTANCE_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditTypeView.Name.SERVERLESS_INSTANCE_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditTypeView.Name.SERVERLESS_INSTANCE_DELETE_SUBMITTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditTypeView.Name.SERVERLESS_INSTANCE_READY;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditTypeView.Name.SERVERLESS_INSTANCE_UNBLOCKED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditTypeView.Name.SERVERLESS_INSTANCE_UPDATE_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditTypeView.Name.SERVERLESS_INSTANCE_UPDATE_STARTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditTypeView.Name.SERVERLESS_INSTANCE_UPDATE_SUBMITTED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView.Name.TENANT_ENDPOINT_AVAILABLE;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView.Name.TENANT_ENDPOINT_CREATED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView.Name.TENANT_ENDPOINT_DELETED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView.Name.TENANT_ENDPOINT_DELETING;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView.Name.TENANT_ENDPOINT_EXPIRED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView.Name.TENANT_ENDPOINT_FAILED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView.Name.TENANT_ENDPOINT_INITIATING;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView.Name.TENANT_ENDPOINT_RESERVATION_FAILED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView.Name.TENANT_ENDPOINT_RESERVED;
import static com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView.Name.TENANT_ENDPOINT_UPDATED;
import static com.xgen.cloud.nds.activity._public.event.view.ADFAAdminAuditTypeView.Name.ADFA_CREATE_OR_UPDATE_ANDON_CORD_ACTION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.ADFAAdminAuditTypeView.Name.ADFA_LIST_ANDON_CORDS_ACTION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.ADFAAdminAuditTypeView.Name.ADFA_LIST_CURRENT_OPS_ACTION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.ADFAAdminAuditTypeView.Name.ADFA_LIST_DATA_SETS_ACTION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.ADFAAdminAuditTypeView.Name.ADFA_TENANT_DELETE_STORAGE_CONFIG_ACTION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.ADFAAdminAuditTypeView.Name.ADFA_TENANT_GET_CONFIG_ACTION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.ADFAAdminAuditTypeView.Name.ADFA_TENANT_GET_SETTINGS_ACTION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.ADFAAdminAuditTypeView.Name.ADFA_TENANT_GET_STORAGE_CONFIG_ACTION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.ADFAAdminAuditTypeView.Name.ADFA_TENANT_SET_SETTINGS_ACTION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_AUTO_EXPORT_FAILED;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_CONCURRENT_SNAPSHOT_FAILED_WILL_RETRY;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_COPY_SNAPSHOT_FAILED;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_COPY_SNAPSHOT_FAILED_WILL_RETRY;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_COPY_SNAPSHOT_STARTED;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_COPY_SNAPSHOT_SUCCESSFUL;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_EXPORT_FAILED;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_EXPORT_SUCCESSFUL;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_OPLOG_BEHIND;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_OPLOG_CAUGHT_UP;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_OPLOG_NOT_CONTIGUOUS;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_PREV_SNAPSHOT_OLD;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_RESTORE_FAILED;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_RESTORE_SUCCESSFUL;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_SNAPSHOT_BEHIND;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_SNAPSHOT_DOWNLOAD_REQUEST_FAILED;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_SNAPSHOT_FAILED;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_SNAPSHOT_FALLBACK_FAILED;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_SNAPSHOT_FALLBACK_SUCCESSFUL;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_SNAPSHOT_IN_QUEUE;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_SNAPSHOT_IN_TIME;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_SNAPSHOT_PROCESSING;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_SNAPSHOT_STARTED;
import static com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView.Name.CPS_SNAPSHOT_SUCCESSFUL;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_CLUSTER_MIGRATION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_CLUSTER_MIGRATION_ERRORED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_CLUSTER_MIGRATION_ROLLBACK_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_CLUSTER_MIGRATION_ROLLBACK_ERRORED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_CLUSTER_MIGRATION_ROLLBACK_STARTED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_CLUSTER_MIGRATION_STARTED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_HOLDER_GROUP_MIGRATION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_HOLDER_GROUP_MIGRATION_ERRORED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_HOLDER_GROUP_MIGRATION_ROLLBACK_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_HOLDER_GROUP_MIGRATION_ROLLBACK_ERRORED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_HOLDER_GROUP_MIGRATION_ROLLBACK_STARTED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_MTM_HOLDER_GROUP_MIGRATION_STARTED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_TENANT_MIGRATION_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_TENANT_MIGRATION_ERRORED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_TENANT_MIGRATION_ROLLBACK_COMPLETED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_TENANT_MIGRATION_ROLLBACK_ERRORED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_TENANT_MIGRATION_ROLLBACK_STARTED;
import static com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView.Name.FLEX_TENANT_MIGRATION_STARTED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ALL_ORG_USERS_HAVE_MFA;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.CUSTOM_SESSION_TIMEOUT_MODIFIED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.GROUP_MOVED_FROM_ORG;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_ACTIVATED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_ADMIN_LOCKED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_ADMIN_SUSPENDED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_ALL_PAID_SERVICES_TERMINATED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_ALL_PAID_SERVICES_TERMINATION_REQUESTED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CLUSTERS_DELETED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CLUSTERS_PAUSED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_COMPANY_NAME_OFAC_HIT;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CONNECTED_TO_MLAB;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CONNECTED_TO_VERCEL;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CONNECTION_UNINSTALLED_FROM_VERCEL;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CREATED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CREDIT_CARD_ABOUT_TO_EXPIRE;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CREDIT_CARD_ADDED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CREDIT_CARD_CURRENT;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CREDIT_CARD_UPDATED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CUSTOM_POLICY_CREATED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CUSTOM_POLICY_DELETED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_CUSTOM_POLICY_UPDATED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_DAILY_BILL_OVER_THRESHOLD;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_DAILY_BILL_UNDER_THRESHOLD;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_DELETED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_DISCONNECTED_FROM_MLAB;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_DISCONNECTED_TO_VERCEL;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_EDITED_UI_IP_ACCESS_LIST_ENTRIES;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_EMBARGO_CONFIRMED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_EMPLOYEE_ACCESS_RESTRICTED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_EMPLOYEE_ACCESS_UNRESTRICTED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_GEN_AI_DISABLED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_GEN_AI_ENABLED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_GROUP_CHARGES_OVER_THRESHOLD;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_GROUP_CHARGES_UNDER_THRESHOLD;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_IDP_CERTIFICATE_ABOUT_TO_EXPIRE;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_IDP_CERTIFICATE_CURRENT;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_INVOICE_OVER_THRESHOLD;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_INVOICE_UNDER_THRESHOLD;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_IP_ACCESS_LIST_DELETED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_LOCKED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_MONGODB_VERSION_EOL_EXTENSION_ACCEPTED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_MONGODB_VERSION_EOL_EXTENSION_CANCELLED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_MONGODB_VERSION_EOL_EXTENSION_PENDING;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_NO_FINANCIAL_PROTECTION;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_OVERRIDE_PAYMENT_METHOD_ADDED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_PAYPAL_CANCELLED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_PAYPAL_LINKED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_PAYPAL_UPDATED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_PRIVATE_ENDPOINTS_DELETED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_PUBLIC_API_ACCESS_LIST_NOT_REQUIRED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_PUBLIC_API_ACCESS_LIST_REQUIRED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_RENAMED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_SERVICE_ACCOUNT_MAX_SECRET_VALIDITY_EDITED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_SERVICE_ACCOUNT_SECRETS_EXPIRED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_SERVICE_ACCOUNT_SECRETS_EXPIRING;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_SERVICE_ACCOUNT_SECRETS_NO_LONGER_EXPIRED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_SERVICE_ACCOUNT_SECRETS_NO_LONGER_EXPIRING;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_SFDC_ACCOUNT_ID_CHANGED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_STREAMS_CROSS_GROUP_DISABLED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_STREAMS_CROSS_GROUP_ENABLED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_SUSPENDED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_SUSPENSION_DATE_CHANGED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_TEMPORARILY_ACTIVATED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_TWO_FACTOR_AUTH_OPTIONAL;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_TWO_FACTOR_AUTH_REQUIRED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_UI_IP_ACCESS_LIST_DISABLED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_UI_IP_ACCESS_LIST_ENABLED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_UNDER_FINANCIAL_PROTECTION;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_UNEMBARGOED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.ORG_USERS_WITHOUT_MFA;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.SANDBOX_CONFIG_DELETED;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.SANDBOX_DISABLED_FOR_ORG;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.SANDBOX_ENABLED_FOR_ORG;
import static com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView.Name.SECURITY_CONTACT_MODIFIED;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.DEPLOYMENT_FAILURE;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.DEPLOYMENT_MODEL_CHANGE_FAILURE;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.DEPLOYMENT_MODEL_CHANGE_SUCCESS;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.INSIDE_REALM_METRIC_THRESHOLD;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.LOG_FORWARDER_FAILURE;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.OUTSIDE_REALM_METRIC_THRESHOLD;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.REQUEST_RATE_LIMIT;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.SUCCESSFUL_DEPLOY;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.SYNC_FAILURE;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.TRIGGER_AUTO_RESUMED;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.TRIGGER_FAILURE;
import static com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView.Name.URL_CONFIRMATION;
import static com.xgen.cloud.search.decoupled.activity._public.audit.view.SearchDeploymentAuditTypeView.Name.SEARCH_DEPLOYMENT_CREATED;
import static com.xgen.cloud.search.decoupled.activity._public.audit.view.SearchDeploymentAuditTypeView.Name.SEARCH_DEPLOYMENT_DELETED;
import static com.xgen.cloud.search.decoupled.activity._public.audit.view.SearchDeploymentAuditTypeView.Name.SEARCH_DEPLOYMENT_UPDATED;
import static com.xgen.cloud.streams._public.model.event.view.StreamProcessorEventTypeView.Name.INSIDE_STREAM_PROCESSOR_METRIC_THRESHOLD;
import static com.xgen.cloud.streams._public.model.event.view.StreamProcessorEventTypeView.Name.OUTSIDE_STREAM_PROCESSOR_METRIC_THRESHOLD;
import static com.xgen.cloud.streams._public.model.event.view.StreamProcessorEventTypeView.Name.STREAM_PROCESSOR_STATE_IS_FAILED;
import static com.xgen.cloud.streams._public.model.event.view.StreamsEventTypeView.Name.MAX_PROCESSOR_COUNT_REACHED;
import static com.xgen.cloud.team._public.activity.event.view.TeamEventTypeView.Name.TEAM_ADDED_TO_GROUP;
import static com.xgen.cloud.team._public.activity.event.view.TeamEventTypeView.Name.TEAM_CREATED;
import static com.xgen.cloud.team._public.activity.event.view.TeamEventTypeView.Name.TEAM_DELETED;
import static com.xgen.cloud.team._public.activity.event.view.TeamEventTypeView.Name.TEAM_NAME_CHANGED;
import static com.xgen.cloud.team._public.activity.event.view.TeamEventTypeView.Name.TEAM_REMOVED_FROM_GROUP;
import static com.xgen.cloud.team._public.activity.event.view.TeamEventTypeView.Name.TEAM_ROLES_MODIFIED;
import static com.xgen.cloud.team._public.activity.event.view.TeamEventTypeView.Name.TEAM_UPDATED;
import static com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView.Name.API_KEY_ACCESS_LIST_ENTRY_ADDED;
import static com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView.Name.API_KEY_ACCESS_LIST_ENTRY_DELETED;
import static com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView.Name.API_KEY_ADDED_TO_GROUP;
import static com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView.Name.API_KEY_CREATED;
import static com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView.Name.API_KEY_DELETED;
import static com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView.Name.API_KEY_DESCRIPTION_CHANGED;
import static com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView.Name.API_KEY_REMOVED_FROM_GROUP;
import static com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView.Name.API_KEY_ROLES_CHANGED;
import static com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView.Name.API_KEY_UI_IP_ACCESS_LIST_INHERITANCE_DISABLED;
import static com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView.Name.API_KEY_UI_IP_ACCESS_LIST_INHERITANCE_ENABLED;
import static com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView.Name.TEMP_GLOBAL_API_KEY_CREATED;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.ACCOUNT_LOCKED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.ACCOUNT_UNLOCKED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.GROUP_INVITATION_DELETED;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.INITIATE_SFDC_SYNC;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.INVITED_TO_GROUP;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.INVITED_TO_ORG;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.INVITED_TO_TEAM;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.JOINED_GROUP;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.JOINED_ORG;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.JOINED_TEAM;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.JOIN_GROUP_REQUEST_APPROVED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.JOIN_GROUP_REQUEST_DENIED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.LEGACY_2FA_RESET_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.LEGACY_2FA_RESET_EMAIL_SENT_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.LEGACY_2FA_UPDATED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.MULTI_FACTOR_AUTH_RESET_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.MULTI_FACTOR_AUTH_RESET_EMAIL_SENT_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.MULTI_FACTOR_AUTH_UPDATED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.ORG_FLEX_CONSULTING_PURCHASED;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.ORG_FLEX_CONSULTING_PURCHASE_FAILED;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.ORG_INVITATION_DELETED;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.PASSWORD_FORCE_RESET_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.PASSWORD_RESET_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.PASSWORD_RESET_EMAIL_SENT_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.PASSWORD_RESET_FAILED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.PASSWORD_RESET_FORM_VIEWED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.PASSWORD_RESET_LINK_GENERATED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.PASSWORD_UPDATED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.REMOVED_FROM_GROUP;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.REMOVED_FROM_ORG;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.REMOVED_FROM_TEAM;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.REQUESTED_TO_JOIN_GROUP;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.SELF_SERVE_DELETION_REQUESTED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.SUCCESSFUL_DEVICE_CONFIRMATION_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.SUCCESSFUL_LOGIN_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.UNSUCCESSFUL_LOGIN_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_ACCOUNT_EMAIL_ADDRESS_CHANGED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_CANCELED_EMAIL_CHANGE_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_CANCELED_EMAIL_VALIDATION_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_CREATED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_DELETED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_EMAIL_ADDRESS_CHANGED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_EMAIL_VERIFIED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_FIRST_NAME_LAST_NAME_CHANGED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_HARD_DELETED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_NAME_OFAC_HIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_POLICY_ASSIGNMENTS_CHANGED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_PROFILE_CHANGED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_REQUESTED_EMAIL_ADDRESS_CHANGE_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_RESTORED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_ROLES_CHANGED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_SALESFORCE_CONTACT_ID_CHANGED_AUDIT;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_UNEMBARGOED;
import static com.xgen.cloud.user._public.model.activity.view.UserEventTypeView.Name.USER_VERIFICATION_EMAIL_SENT_AUDIT;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.xgen.cloud.abtesting._public.view.BulkAllocationAuditEventTypeView;
import com.xgen.cloud.abtesting._public.view.BulkAllocationAuditEventView;
import com.xgen.cloud.access.accesslist._public.model.event.view.GlobalAccessListEventTypeView;
import com.xgen.cloud.access.activity._public.event.view.AccessEventTypeView;
import com.xgen.cloud.access.activity._public.event.view.AccessEventView;
import com.xgen.cloud.account._public.model.activity.view.DeviceCodeAuditTypeView;
import com.xgen.cloud.activity._public.model.audit.view.EventWithAuditInfoView;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.activity._public.model.event.view.BiConnectorEventTypeView;
import com.xgen.cloud.activity._public.model.event.view.BiConnectorEventView;
import com.xgen.cloud.activity._public.model.event.view.CaptainLogAuditTypeView;
import com.xgen.cloud.activity._public.model.event.view.CaptainLogAuditView;
import com.xgen.cloud.activity._public.model.event.view.EventViewContext;
import com.xgen.cloud.activity._public.model.event.view.HostEventTypeView;
import com.xgen.cloud.activity._public.model.event.view.HostEventView;
import com.xgen.cloud.activity._public.model.event.view.HostMetricEventTypeView;
import com.xgen.cloud.activity._public.model.event.view.HostMetricEventView;
import com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventTypeView;
import com.xgen.cloud.activity._public.model.event.view.ReplicaSetEventView;
import com.xgen.cloud.activity._public.model.event.view.SystemAlertGlobalServiceAccountEventTypeView;
import com.xgen.cloud.activity.runtime.view.EventViewHierarchy.DefaultEventView;
import com.xgen.cloud.admin._public.activity.view.AppSettingsChangeAuditTypeView;
import com.xgen.cloud.admin._public.activity.view.SystemConfigChangeAuditTypeView;
import com.xgen.cloud.admin._public.activity.view.SystemLogEventTypeView;
import com.xgen.cloud.alerts.alert._public.model.view.AlertAuditTypeView;
import com.xgen.cloud.alerts.alert._public.model.view.AlertAuditView;
import com.xgen.cloud.alerts.alert._public.model.view.AlertConfigAuditTypeView;
import com.xgen.cloud.alerts.alert._public.model.view.AlertConfigAuditView;
import com.xgen.cloud.alerts.alert._public.model.view.MaintenanceWindowConfigAuditTypeView;
import com.xgen.cloud.alerts.alert._public.model.view.MaintenanceWindowConfigAuditView;
import com.xgen.cloud.alerts.flapping.activity._public.model.view.FlappingEventTypeView;
import com.xgen.cloud.apiserviceaccount._public.activity.event.view.GlobalServiceAccountEventTypeView;
import com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventTypeView;
import com.xgen.cloud.apiserviceaccount._public.activity.event.view.ServiceAccountEventView;
import com.xgen.cloud.atm.activity._public.audit.view.AutomationConfigEventTypeView;
import com.xgen.cloud.atm.activity._public.audit.view.AutomationConfigEventView;
import com.xgen.cloud.atm.activity._public.audit.view.LogCollectionRequestAuditTypeView;
import com.xgen.cloud.atm.activity._public.event.view.CrashLogEventTypeView;
import com.xgen.cloud.authn._public.model.view.AuthnServiceAuditTypeView;
import com.xgen.cloud.authz.core._public.model.event.view.UserGroupEventTypeView;
import com.xgen.cloud.authz.core._public.model.event.view.UserGroupEventView;
import com.xgen.cloud.authz.resource._public.model.event.view.ResourceEventTypeView;
import com.xgen.cloud.authz.resource._public.model.event.view.ResourceEventView;
import com.xgen.cloud.billingplatform.activity._public.event.view.BillingAuditorEventTypeView;
import com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventTypeView;
import com.xgen.cloud.billingplatform.activity._public.event.view.BillingEventView;
import com.xgen.cloud.billingplatform.activity._public.event.view.SystemBillingEventTypeView;
import com.xgen.cloud.brs.activity._public.model.view.BackupEventTypeView;
import com.xgen.cloud.brs.activity._public.model.view.BackupEventView;
import com.xgen.cloud.brs.activity._public.model.view.SystemBackupDaemonEventTypeView;
import com.xgen.cloud.brs.activity._public.model.view.SystemBackupEventTypeView;
import com.xgen.cloud.brs.activity._public.model.view.SystemBlockstoreEventTypeView;
import com.xgen.cloud.brs.activity._public.model.view.SystemDatabaseProcessEventTypeView;
import com.xgen.cloud.clusters._public.model.view.ClusterEventTypeView;
import com.xgen.cloud.clusters._public.model.view.ClusterEventView;
import com.xgen.cloud.common.model._public.typebindings.TypeHierarchy;
import com.xgen.cloud.common.model._public.typebindings.TypeRegistry;
import com.xgen.cloud.config._public.view.ConfigApplicationPropertyAuditEventTypeView;
import com.xgen.cloud.config._public.view.ConfigApplicationPropertyAuditEventView;
import com.xgen.cloud.config._public.view.ConfigFeatureFlagAuditEventTypeView;
import com.xgen.cloud.config._public.view.ConfigFeatureFlagAuditEventView;
import com.xgen.cloud.config._public.view.ConfigNamespaceAuditEventTypeView;
import com.xgen.cloud.config._public.view.ConfigNamespaceAuditEventView;
import com.xgen.cloud.cron._public.model.view.CronAuditTypeView;
import com.xgen.cloud.cron._public.model.view.SystemCronJobEventTypeView;
import com.xgen.cloud.cron._public.model.view.SystemCronJobStatusEventTypeView;
import com.xgen.cloud.email._public.activity.event.view.SupportEventTypeView;
import com.xgen.cloud.explorer.activity._public.audit.view.IndexBuildAuditTypeView;
import com.xgen.cloud.explorer.activity._public.event.view.DataExplorerAccessedEventTypeView;
import com.xgen.cloud.explorer.activity._public.event.view.DataExplorerAccessedEventView;
import com.xgen.cloud.explorer.activity._public.event.view.DataExplorerEventTypeView;
import com.xgen.cloud.explorer.activity._public.event.view.DataExplorerEventView;
import com.xgen.cloud.externalanalytics._public.model.view.SupportCaseEventTypeView;
import com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditTypeView;
import com.xgen.cloud.fts.activity._public.audit.view.FTSIndexAuditView;
import com.xgen.cloud.group._public.model.activity.view.GroupAuditTypeView;
import com.xgen.cloud.group._public.model.activity.view.GroupEventTypeView;
import com.xgen.cloud.group._public.model.activity.view.GroupIntegrationEventTypeView;
import com.xgen.cloud.monitoring.agent._public.model.event.view.AgentEventTypeView;
import com.xgen.cloud.monitoring.metrics._public.model.activity.view.MongotEventTypeView;
import com.xgen.cloud.monitoring.metrics._public.model.activity.view.SystemMaasEventTypeView;
import com.xgen.cloud.monitoring.ratelimit._public.audit.RateLimitSuspensionAuditEventTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.NDSTenantEndpointServiceDeploymentAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.AWSPeerVpcAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.AdminAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.AtlasResourcePolicyAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.AtlasResourcePolicyAuditView;
import com.xgen.cloud.nds.activity._public.event.audit.view.AzurePeerNetworkAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.ChartsAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.ClusterConnectionAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.DataProtectionAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.ExportBucketAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.GCPPeerVpcAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.MlabMigrationAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSAuditView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSAutoScalingAuditView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSDataValidationAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSDbCheckAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSMaintenanceWindowAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessAutoScalingAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessInstanceAuditView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSServerlessTenantMigrationAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.NDSTenantEndpointAuditView;
import com.xgen.cloud.nds.activity._public.event.audit.view.PrivateLinkAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.ServerlessDeploymentAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.SetupServerlessAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.audit.view.VersionAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.view.ADFAAdminAuditTypeView;
import com.xgen.cloud.nds.activity._public.event.view.ADFAAdminAuditView;
import com.xgen.cloud.nds.activity._public.event.view.AdminEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.AutoIndexingEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.BumperFileRemovalEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.CpsBackupEventView;
import com.xgen.cloud.nds.activity._public.event.view.CpsBillingEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.DbCheckEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.DiskBackupEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.EncryptionKeyEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.FlexMigrationEventView;
import com.xgen.cloud.nds.activity._public.event.view.LogIngestionEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.LogUploaderDownEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.LongRunningMoveEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.MPAEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.NDSCheckMetadataConsistencyEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.NDSX509UserAuthenticationEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.OnlineArchiveEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.ProactiveOperationEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.ServerlessMTMClusterGrpcIncrementalRolloutEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.SystemAWSEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.SystemAzureEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.SystemGCPEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.SystemNDSEventTypeView;
import com.xgen.cloud.nds.activity._public.event.view.TenantBackupEventTypeView;
import com.xgen.cloud.nds.project._public.model.cloudprovidermonitoring.view.QuotaUsageEventTypeView;
import com.xgen.cloud.nds.serverless._public.model.activity.view.FlexMetricEventTypeView;
import com.xgen.cloud.nds.serverless._public.model.activity.view.ServerlessEventTypeView;
import com.xgen.cloud.organization._public.model.activity.view.OrgEventTypeView;
import com.xgen.cloud.organization._public.model.activity.view.OrgEventView;
import com.xgen.cloud.partners.common._public.activity.view.PartnerEventTypeView;
import com.xgen.cloud.realm.activity._public.event.view.AppServiceEventTypeView;
import com.xgen.cloud.realm.activity._public.event.view.AppServiceEventView;
import com.xgen.cloud.search.decoupled.activity._public.audit.view.SearchDeploymentAuditTypeView;
import com.xgen.cloud.search.decoupled.activity._public.audit.view.SearchDeploymentAuditView;
import com.xgen.cloud.streams._public.model.event.view.StreamProcessorEventTypeView;
import com.xgen.cloud.streams._public.model.event.view.StreamProcessorEventView;
import com.xgen.cloud.streams._public.model.event.view.StreamsEventTypeView;
import com.xgen.cloud.streams._public.model.event.view.StreamsEventView;
import com.xgen.cloud.system._public.model.view.SystemAlertProcessingEventTypeView;
import com.xgen.cloud.team._public.activity.event.view.TeamEventTypeView;
import com.xgen.cloud.team._public.activity.event.view.TeamEventView;
import com.xgen.cloud.user._public.model.activity.view.ApiUserEventTypeView;
import com.xgen.cloud.user._public.model.activity.view.ApiUserEventView;
import com.xgen.cloud.user._public.model.activity.view.UserEventTypeView;
import com.xgen.cloud.user._public.model.activity.view.UserEventView;
import com.xgen.module.federation.activity._public.event.view.FederationSettingsEventTypeView;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.inject.Inject;

@JsonTypeInfo(
    use = NAME,
    property = EVENT_TYPE_FIELD,
    include = EXISTING_PROPERTY,
    defaultImpl = DefaultEventView.class)
@JsonSubTypes({
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_DATA_LAKE_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_MIGRATION_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_MLAB_SHARED_MIGRATION_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_MONGODUMP_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_MONGOD_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_MONGOMIRROR_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_MONGORESTORE_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_MONGOSQLD_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_MONGOS_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_MONGOT_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_SEARCH_ENVOY_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_PROXY_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_TENANT_UPGRADE_SNAPSHOTS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = AUTOMATION_AGENT_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = AUTOMATION_AGENT_LOGS_CSV_DOWNLOAD),
  @JsonSubTypes.Type(value = AccessEventView.class, name = BACKUP_AGENT_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = BACKUP_AGENT_LOGS_CSV_DOWNLOAD),
  @JsonSubTypes.Type(value = AccessEventView.class, name = FTDC_AUTOMATION_AGENT_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = FTDC_AUTOMATION_AGENT_LOGS_CSV_DOWNLOAD),
  @JsonSubTypes.Type(value = AccessEventView.class, name = BACKUP_MONGOD_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = CPS_BACKUP_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = DOWNLOADED_MONITORING_GROUP_MONGOD_LOGS),
  @JsonSubTypes.Type(
      value = AccessEventView.class,
      name = DOWNLOADED_MONITORING_HOST_SPECIFIC_MONGOD_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ENVOY_SERVERLESS_LOGS),
  @JsonSubTypes.Type(
      value = AccessEventView.class,
      name = ATLAS_ENVOY_SERVERLESS_FILTERS_ACCESS_LOGS),
  @JsonSubTypes.Type(
      value = AccessEventView.class,
      name = ATLAS_ENVOY_SERVERLESS_HEALTH_CHECK_LOGS),
  @JsonSubTypes.Type(
      value = AccessEventView.class,
      name = ATLAS_ENVOY_SERVERLESS_HTTP_CONNECTION_MANAGER_ACCESS_LOGS),
  @JsonSubTypes.Type(
      value = AccessEventView.class,
      name = ATLAS_ENVOY_SERVERLESS_TCP_PROXY_ACCESS_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_ENVOY_SERVERLESS_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ENVOY_SERVERLESS_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = MONGODB_ACCESS_HISTORY),
  @JsonSubTypes.Type(value = AccessEventView.class, name = MONITORING_AGENT_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = MONITORING_AGENT_LOGS_CSV_DOWNLOAD),
  @JsonSubTypes.Type(value = AccessEventView.class, name = MONITORING_DAILY_PING),
  @JsonSubTypes.Type(value = AccessEventView.class, name = MONITORING_GROUP_PING),
  @JsonSubTypes.Type(value = AccessEventView.class, name = MONITORING_LATEST_HOST_SPECIFIC_PING),
  @JsonSubTypes.Type(value = AccessEventView.class, name = MONITORING_MONGOD_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ONLINE_ARCHIVE_AGENT_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ONLINE_ARCHIVE_AGENT_LOGS_CSV_DOWNLOAD),
  @JsonSubTypes.Type(value = AccessEventView.class, name = PERFORMANCE_ADVISOR),
  @JsonSubTypes.Type(value = AccessEventView.class, name = PIT_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = PUBLIC_API_LATEST_MONITORING_GROUP_PING),
  @JsonSubTypes.Type(
      value = AccessEventView.class,
      name = PUBLIC_API_LATEST_MONITORING_HOST_SPECIFIC_PING),
  @JsonSubTypes.Type(value = AccessEventView.class, name = PUBLIC_API_MANAGED_SLOW_MS_FEATURE_FLAG),
  @JsonSubTypes.Type(value = AccessEventView.class, name = REAL_TIME_PERFORMANCE_PANEL),
  @JsonSubTypes.Type(value = AccessEventView.class, name = TOGGLEABLE_FEATURE_FLAG),
  @JsonSubTypes.Type(value = AccessEventView.class, name = VIEWED_AUTOMATION_MONGOD_LOGS),
  @JsonSubTypes.Type(
      value = AccessEventView.class,
      name = VIEWED_MONITORING_HOST_SPECIFIC_MONGOD_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = VISUAL_PROFILER),
  @JsonSubTypes.Type(value = AccessEventView.class, name = QUERY_SHAPE_INSIGHTS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = PUBLIC_API_SEARCH_QUERY_TELEMETRY_FLAG),
  @JsonSubTypes.Type(value = AccessEventView.class, name = ATLAS_USER_IDENTITY_SERVICE_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = DB_CHECK_LOGS),
  @JsonSubTypes.Type(value = AccessEventView.class, name = DB_CHECK_LOGS_CSV_DOWNLOAD),
  @JsonSubTypes.Type(value = AccessEventView.class, name = DB_CHECK_HEALTHLOG_DOWNLOAD),
  @JsonSubTypes.Type(value = AlertAuditView.class, name = ALERT_ACKNOWLEDGED_AUDIT),
  @JsonSubTypes.Type(value = AlertAuditView.class, name = ALERT_UNACKNOWLEDGED_AUDIT),
  @JsonSubTypes.Type(value = AlertConfigAuditView.class, name = ALERT_CONFIG_DISABLED_AUDIT),
  @JsonSubTypes.Type(value = AlertConfigAuditView.class, name = ALERT_CONFIG_ENABLED_AUDIT),
  @JsonSubTypes.Type(value = AlertConfigAuditView.class, name = ALERT_CONFIG_ADDED_AUDIT),
  @JsonSubTypes.Type(value = AlertConfigAuditView.class, name = ALERT_CONFIG_DELETED_AUDIT),
  @JsonSubTypes.Type(value = AlertConfigAuditView.class, name = ALERT_CONFIG_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = ApiUserEventView.class, name = API_KEY_CREATED),
  @JsonSubTypes.Type(value = ApiUserEventView.class, name = API_KEY_DELETED),
  @JsonSubTypes.Type(value = ApiUserEventView.class, name = TEMP_GLOBAL_API_KEY_CREATED),
  @JsonSubTypes.Type(value = ApiUserEventView.class, name = API_KEY_ACCESS_LIST_ENTRY_ADDED),
  @JsonSubTypes.Type(value = ApiUserEventView.class, name = API_KEY_ACCESS_LIST_ENTRY_DELETED),
  @JsonSubTypes.Type(value = ApiUserEventView.class, name = API_KEY_ROLES_CHANGED),
  @JsonSubTypes.Type(value = ApiUserEventView.class, name = API_KEY_DESCRIPTION_CHANGED),
  @JsonSubTypes.Type(value = ApiUserEventView.class, name = API_KEY_ADDED_TO_GROUP),
  @JsonSubTypes.Type(value = ApiUserEventView.class, name = API_KEY_REMOVED_FROM_GROUP),
  @JsonSubTypes.Type(
      value = ApiUserEventView.class,
      name = API_KEY_UI_IP_ACCESS_LIST_INHERITANCE_ENABLED),
  @JsonSubTypes.Type(
      value = ApiUserEventView.class,
      name = API_KEY_UI_IP_ACCESS_LIST_INHERITANCE_DISABLED),
  @JsonSubTypes.Type(value = ServiceAccountEventView.class, name = SERVICE_ACCOUNT_CREATED),
  @JsonSubTypes.Type(value = ServiceAccountEventView.class, name = SERVICE_ACCOUNT_DELETED),
  @JsonSubTypes.Type(value = ServiceAccountEventView.class, name = SERVICE_ACCOUNT_ROLES_CHANGED),
  @JsonSubTypes.Type(value = ServiceAccountEventView.class, name = SERVICE_ACCOUNT_DETAILS_CHANGED),
  @JsonSubTypes.Type(value = ServiceAccountEventView.class, name = SERVICE_ACCOUNT_ADDED_TO_GROUP),
  @JsonSubTypes.Type(
      value = ServiceAccountEventView.class,
      name = SERVICE_ACCOUNT_REMOVED_FROM_GROUP),
  @JsonSubTypes.Type(
      value = ServiceAccountEventView.class,
      name = SERVICE_ACCOUNT_ACCESS_LIST_ENTRY_ADDED),
  @JsonSubTypes.Type(
      value = ServiceAccountEventView.class,
      name = SERVICE_ACCOUNT_ACCESS_LIST_ENTRY_DELETED),
  @JsonSubTypes.Type(value = ServiceAccountEventView.class, name = SERVICE_ACCOUNT_SECRET_ADDED),
  @JsonSubTypes.Type(value = ServiceAccountEventView.class, name = SERVICE_ACCOUNT_SECRET_DELETED),
  @JsonSubTypes.Type(
      value = ServiceAccountEventView.class,
      name = SERVICE_ACCOUNT_UI_IP_ACCESS_LIST_INHERITANCE_ENABLED),
  @JsonSubTypes.Type(
      value = ServiceAccountEventView.class,
      name = SERVICE_ACCOUNT_UI_IP_ACCESS_LIST_INHERITANCE_DISABLED),
  @JsonSubTypes.Type(
      value = StreamProcessorEventView.class,
      name = STREAM_PROCESSOR_STATE_IS_FAILED),
  @JsonSubTypes.Type(value = StreamsEventView.class, name = MAX_PROCESSOR_COUNT_REACHED),
  @JsonSubTypes.Type(
      value = StreamProcessorEventView.class,
      name = INSIDE_STREAM_PROCESSOR_METRIC_THRESHOLD),
  @JsonSubTypes.Type(
      value = StreamProcessorEventView.class,
      name = OUTSIDE_STREAM_PROCESSOR_METRIC_THRESHOLD),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = URL_CONFIRMATION),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = SUCCESSFUL_DEPLOY),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = DEPLOYMENT_FAILURE),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = DEPLOYMENT_MODEL_CHANGE_SUCCESS),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = DEPLOYMENT_MODEL_CHANGE_FAILURE),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = REQUEST_RATE_LIMIT),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = LOG_FORWARDER_FAILURE),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = INSIDE_REALM_METRIC_THRESHOLD),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = OUTSIDE_REALM_METRIC_THRESHOLD),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = SYNC_FAILURE),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = TRIGGER_FAILURE),
  @JsonSubTypes.Type(value = AppServiceEventView.class, name = TRIGGER_AUTO_RESUMED),
  @JsonSubTypes.Type(
      value = AutomationConfigEventView.class,
      name = AUTOMATION_CONFIG_PUBLISHED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = OPLOG_CURRENT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = OPLOG_BEHIND),
  @JsonSubTypes.Type(value = BackupEventView.class, name = RESYNC_PERFORMED),
  @JsonSubTypes.Type(value = BackupEventView.class, name = RESYNC_REQUIRED),
  @JsonSubTypes.Type(value = BackupEventView.class, name = NO_RS_BIND_ERROR),
  @JsonSubTypes.Type(value = BackupEventView.class, name = RS_BIND_ERROR),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_RECOVERED),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_TOO_MANY_RETRIES),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_TOO_MANY_HEAD_START_ATTEMPTS),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_IN_UNEXPECTED_STATE),
  @JsonSubTypes.Type(value = BackupEventView.class, name = TIMELY_SNAPSHOT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = LATE_SNAPSHOT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = TIMELY_WTC_SNAPSHOT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = LATE_WTC_SNAPSHOT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_UNSUPPORTED),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_SUPPORTED),
  @JsonSubTypes.Type(value = BackupEventView.class, name = ENABLE_BACKUP_PRIVATELINK_EVENT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = DISABLE_BACKUP_PRIVATELINK_EVENT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = CONSISTENT_BACKUP_CONFIGURATION),
  @JsonSubTypes.Type(value = BackupEventView.class, name = INCONSISTENT_BACKUP_CONFIGURATION),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_DEPLOYMENT_CONFIG_IS_MISSING),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_DEPLOYMENT_CONFIG_IS_PRESENT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = ONDEMAND_SNAPSHOT_REQUESTED),
  @JsonSubTypes.Type(
      value = BackupEventView.class,
      name = DAEMON_AVAILABLE_FOR_QUERYABLE_RESTORE_JOB),
  @JsonSubTypes.Type(
      value = BackupEventView.class,
      name = NO_DAEMON_AVAILABLE_FOR_QUERYABLE_RESTORE_JOB),
  @JsonSubTypes.Type(value = BackupEventView.class, name = GOOD_CLUSTERSHOT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BAD_CLUSTERSHOTS),
  @JsonSubTypes.Type(value = BackupEventView.class, name = SYNC_SLICE_PROGRESSED),
  @JsonSubTypes.Type(value = BackupEventView.class, name = SYNC_SLICE_HAS_NOT_PROGRESSED),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_JOB_NOT_BUSY),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_JOB_TOO_BUSY),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_NOT_LATE_TRACKING_JOB),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_LATE_TRACKING_JOB),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_NOT_LATE_INTEGRITY_CHECK_JOB),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_LATE_INTEGRITY_CHECK_JOB),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_NOT_LATE_GROOM_JOB),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BACKUP_LATE_GROOM_JOB),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BLOCKSTORE_JOB_RECOVERED),
  @JsonSubTypes.Type(value = BackupEventView.class, name = BLOCKSTORE_JOB_TOO_MANY_RETRIES),
  @JsonSubTypes.Type(value = BackupEventView.class, name = INITIAL_SYNC_STARTED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = INITIAL_SYNC_FINISHED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = RS_STATE_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = CLUSTER_STATE_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = RESTORE_REQUESTED_AUDIT),
  @JsonSubTypes.Type(
      value = BackupEventView.class,
      name = INTERNAL_DIAGNOSTIC_RESTORE_REQUESTED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = SYNC_REQUIRED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = SYNC_PENDING_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = CLUSTERSHOT_DELETED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = SNAPSHOT_DELETED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = RS_CREDENTIAL_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = CLUSTER_CREDENTIAL_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = RS_BLACKLIST_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = RS_DENYLIST_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = CLUSTER_BLACKLIST_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = CLUSTER_DENYLIST_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = RS_SNAPSHOT_SCHEDULE_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = CLUSTER_SNAPSHOT_SCHEDULE_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = CLUSTER_CHECKPOINT_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = RS_STORAGE_ENGINE_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = CLUSTER_STORAGE_ENGINE_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = RS_ROTATE_MASTER_KEY_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = SNAPSHOT_EXPIRY_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = BackupEventView.class, name = CLUSTERSHOT_EXPIRY_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_SNAPSHOT_STARTED),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_SNAPSHOT_PROCESSING),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_SNAPSHOT_IN_TIME),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_SNAPSHOT_SUCCESSFUL),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_SNAPSHOT_FAILED),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_SNAPSHOT_IN_QUEUE),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_SNAPSHOT_BEHIND),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_SNAPSHOT_FALLBACK_SUCCESSFUL),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_SNAPSHOT_FALLBACK_FAILED),
  @JsonSubTypes.Type(
      value = CpsBackupEventView.class,
      name = CPS_CONCURRENT_SNAPSHOT_FAILED_WILL_RETRY),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_COPY_SNAPSHOT_STARTED),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_COPY_SNAPSHOT_FAILED),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_COPY_SNAPSHOT_FAILED_WILL_RETRY),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_COPY_SNAPSHOT_SUCCESSFUL),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_PREV_SNAPSHOT_OLD),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_RESTORE_SUCCESSFUL),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_EXPORT_SUCCESSFUL),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_OPLOG_CAUGHT_UP),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_OPLOG_BEHIND),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_OPLOG_NOT_CONTIGUOUS),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_RESTORE_FAILED),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_EXPORT_FAILED),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_AUTO_EXPORT_FAILED),
  @JsonSubTypes.Type(value = CpsBackupEventView.class, name = CPS_SNAPSHOT_DOWNLOAD_REQUEST_FAILED),
  @JsonSubTypes.Type(value = BiConnectorEventView.class, name = BI_CONNECTOR_UP),
  @JsonSubTypes.Type(value = BiConnectorEventView.class, name = BI_CONNECTOR_DOWN),
  @JsonSubTypes.Type(value = BillingEventView.class, name = NO_DUPLICATE_SUBSCRIPTION_USAGE),
  @JsonSubTypes.Type(value = BillingEventView.class, name = DUPLICATE_SUBSCRIPTION_USAGE),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_CARD_CURRENT),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_CARD_ABOUT_TO_EXPIRE),
  @JsonSubTypes.Type(value = BillingEventView.class, name = INVOICE_BILLED_EQUALS_LINE_ITEMS_TOTAL),
  @JsonSubTypes.Type(
      value = BillingEventView.class,
      name = INVOICE_BILLED_DOES_NOT_EQUAL_LINE_ITEMS_TOTAL),
  @JsonSubTypes.Type(value = BillingEventView.class, name = INVOICE_STATUS_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = INVOICE_AMOUNTS_RECALCULATED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = NO_STALE_PENDING_INVOICES),
  @JsonSubTypes.Type(value = BillingEventView.class, name = STALE_PENDING_INVOICES),
  @JsonSubTypes.Type(value = BillingEventView.class, name = ONE_PENDING_INVOICE),
  @JsonSubTypes.Type(value = BillingEventView.class, name = TOO_MANY_PENDING_INVOICES),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PAYMENT_METHOD_ADDED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = MISSING_PAYMENT_METHOD),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PREPAID_PLAN_NOT_MISSING_SKU),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PREPAID_PLAN_MISSING_SKU),
  @JsonSubTypes.Type(value = BillingEventView.class, name = BILLABLE_HOSTS_INCREASED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = ONE_INVOICE_FOR_MONTH),
  @JsonSubTypes.Type(value = BillingEventView.class, name = MULTIPLE_INVOICES_FOR_MONTH),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CHARGE_SUCCEEDED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CHARGE_FAILED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CHARGE_PROCESSING),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CHARGE_PENDING_REVERSAL),
  @JsonSubTypes.Type(value = BillingEventView.class, name = BRAINTREE_CHARGE_FAILED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = GRACE_PERIOD_ACTIVATED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = GRACE_PERIOD_NO_LONGER_IN_EFFECT),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PENDING_DEAL_ACTIVATION_ADDED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PENDING_DEAL_ACTIVATION_CANCELED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PENDING_DEAL_APPLIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PENDING_DEAL_ACTIVATION_FAILED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = INVOICE_CLOSED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CHECK_PAYMENT_RECEIVED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = WIRE_TRANSFER_PAYMENT_RECEIVED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PENDING_INVOICE_UNDER_THRESHOLD),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PENDING_INVOICE_OVER_THRESHOLD),
  @JsonSubTypes.Type(value = BillingEventView.class, name = DAILY_BILL_UNDER_THRESHOLD),
  @JsonSubTypes.Type(value = BillingEventView.class, name = DAILY_BILL_OVER_THRESHOLD),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PAYMENT_METHODS_REMOVED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = DISCOUNT_APPLIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_ISSUED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = AWS_BILLING_ACCOUNT_CREDIT_ISSUED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = GCP_BILLING_ACCOUNT_CREDIT_ISSUED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_PULLED_FWD),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_END_DATE_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_SFOLID_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PROMO_CODE_APPLIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PAYMENT_FORGIVEN),
  @JsonSubTypes.Type(value = BillingEventView.class, name = REFUND_ISSUED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = ACCOUNT_DOWNGRADED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = ACCOUNT_UPGRADED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = ACCOUNT_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = SUPPORT_PLAN_ACTIVATED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = SUPPORT_PLAN_CANCELLED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = SUPPORT_PLAN_CANCELLATION_SCHEDULED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PREPAID_PLAN_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = INITIATE_SALESFORCE_SERVICE_CLOUD_SYNC),
  @JsonSubTypes.Type(value = BillingEventView.class, name = INVOICE_ADDRESS_CHANGED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = INVOICE_ADDRESS_ADDED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PREPAID_PLAN_ACTIVATED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = ELASTIC_INVOICING_MODE_ACTIVATED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = ELASTIC_INVOICING_MODE_DEACTIVATED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = AWS_USAGE_REPORTED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = AZURE_USAGE_REPORTED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = GCP_USAGE_REPORTED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = VERCEL_USAGE_REPORTED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = BECAME_PAYING_ORG),
  @JsonSubTypes.Type(value = BillingEventView.class, name = NEW_LINKED_ORG),
  @JsonSubTypes.Type(value = BillingEventView.class, name = UNLINKED_ORG),
  @JsonSubTypes.Type(value = BillingEventView.class, name = ORG_LINKED_TO_PAYING_ORG),
  @JsonSubTypes.Type(value = BillingEventView.class, name = ORG_UNLINKED_FROM_PAYING_ORG),
  @JsonSubTypes.Type(value = BillingEventView.class, name = ORG_UNLINK_REQUESTED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = ORG_UNLINK_CANCELLED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = TERMINATE_PAID_SERVICES),
  @JsonSubTypes.Type(value = BillingEventView.class, name = METER_USAGE_DELETED_THROUGH_API),
  @JsonSubTypes.Type(value = BillingEventView.class, name = METER_USAGE_UNDELETED_THROUGH_API),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PAYMENT_UPDATED_THROUGH_API),
  @JsonSubTypes.Type(value = BillingEventView.class, name = BILLING_EMAIL_ADDRESS_ADDED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = LEGACY_REBILL_EXECUTED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = TARGETED_REBILL_EXECUTED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = EVERGREEN_DEAL_CANCELLED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = BILLING_EMAIL_ADDRESS_CHANGED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = BILLING_EMAIL_ADDRESS_REMOVED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = AZURE_BILLING_ACCOUNT_CREDIT_ISSUED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_START_DATE_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_ELASTIC_INVOICING_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_TYPE_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_AMOUNT_CENTS_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_AMOUNT_REMAINING_CENTS_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_TOTAL_BILLED_CENTS_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_AWS_CUSTOMER_ID_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_AWS_PRODUCT_CODE_MODIFIED),
  @JsonSubTypes.Type(
      value = BillingEventView.class,
      name = CREDIT_GCP_MARKETPLACE_ENTITLEMENT_ID_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_AZURE_SUBSCRIPTION_ID_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = CREDIT_AZURE_PRIVATE_PLAN_ID_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = EVERGREEN_PRIORITY_MODIFIED),
  @JsonSubTypes.Type(value = BillingEventView.class, name = TARGETED_REBILL_EXECUTED_THROUGH_API),
  @JsonSubTypes.Type(value = BillingEventView.class, name = DAILY_BILLING_EXECUTED_THROUGH_API),
  @JsonSubTypes.Type(value = BillingEventView.class, name = PROJECT_LEVEL_SUPPORT_ENABLED),
  @JsonSubTypes.Type(value = ClusterEventView.class, name = CLUSTER_MONGOS_IS_PRESENT),
  @JsonSubTypes.Type(value = ClusterEventView.class, name = CLUSTER_MONGOS_IS_MISSING),
  @JsonSubTypes.Type(value = ClusterEventView.class, name = CLUSTER_AGENT_IN_CRASH_LOOP),
  @JsonSubTypes.Type(value = ClusterEventView.class, name = CLUSTER_AGENT_HAS_RS_RECONFIG_ERROR),
  @JsonSubTypes.Type(
      value = ClusterEventView.class,
      name = CLUSTER_AGENT_HAS_PERSISTENT_MOVE_COLLECTION_ERROR),
  @JsonSubTypes.Type(value = DataExplorerAccessedEventView.class, name = DATA_EXPLORER),
  @JsonSubTypes.Type(
      value = DataExplorerAccessedEventView.class,
      name = DATA_EXPLORER_CRUD_ATTEMPT),
  @JsonSubTypes.Type(value = DataExplorerAccessedEventView.class, name = DATA_EXPLORER_CRUD_ERROR),
  @JsonSubTypes.Type(value = DataExplorerAccessedEventView.class, name = DATA_EXPLORER_CRUD),
  @JsonSubTypes.Type(value = DataExplorerEventView.class, name = DATA_EXPLORER_SESSION_CREATED),
  @JsonSubTypes.Type(value = FTSIndexAuditView.class, name = FTS_INDEX_DELETION_FAILED),
  @JsonSubTypes.Type(value = FTSIndexAuditView.class, name = FTS_INDEX_BUILD_COMPLETE),
  @JsonSubTypes.Type(value = FTSIndexAuditView.class, name = FTS_INDEX_BUILD_FAILED),
  @JsonSubTypes.Type(value = FTSIndexAuditView.class, name = FTS_INDEX_CREATED),
  @JsonSubTypes.Type(value = FTSIndexAuditView.class, name = FTS_INDEX_UPDATED),
  @JsonSubTypes.Type(value = FTSIndexAuditView.class, name = FTS_INDEX_DELETED),
  @JsonSubTypes.Type(value = FTSIndexAuditView.class, name = FTS_INDEX_CLEANED_UP),
  @JsonSubTypes.Type(value = FTSIndexAuditView.class, name = FTS_INDEXES_RESTORED),
  @JsonSubTypes.Type(value = FTSIndexAuditView.class, name = FTS_INDEXES_RESTORE_FAILED),
  @JsonSubTypes.Type(value = FTSIndexAuditView.class, name = FTS_INDEXES_SYNONYM_MAPPING_INVALID),
  @JsonSubTypes.Type(value = SearchDeploymentAuditView.class, name = SEARCH_DEPLOYMENT_CREATED),
  @JsonSubTypes.Type(value = SearchDeploymentAuditView.class, name = SEARCH_DEPLOYMENT_UPDATED),
  @JsonSubTypes.Type(value = SearchDeploymentAuditView.class, name = SEARCH_DEPLOYMENT_DELETED),
  @JsonSubTypes.Type(value = HostEventView.class, name = ADD_HOST_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = ADD_HOST_TO_REPLICA_SET_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = ATTEMPT_KILLOP_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = ATTEMPT_KILLSESSION_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = AUTO_CREATED_INDEX_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = DB_PROFILER_DISABLE_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = DB_PROFILER_ENABLE_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = DELETE_HOST_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = DISABLE_HOST_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = HIDE_AND_DISABLE_HOST_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = HIDE_HOST_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_NOT_ENOUGH_DISK_SPACE),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_ENOUGH_DISK_SPACE),
  @JsonSubTypes.Type(
      value = HostEventView.class,
      name = HOST_DISK_SPACE_INSUFFICIENT_FOR_SEARCH_INDEX_REBUILD),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_DOWN),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_DOWNGRADED),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_EXPOSED),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_HAS_INDEX_SUGGESTIONS),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_HAS_INDEX_SUGGESTIONS_WITHDRAWN),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_IP_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_LOCKED_DOWN),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_MONGOT_CRASHING_OOM),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_MONGOT_CRASHING_OOM_MTM),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_MONGOT_RECOVERED_OOM),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_MONGOT_RECOVERED_OOM_MTM),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_MONGOT_STOP_REPLICATION),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_MONGOT_RESUME_REPLICATION),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_MONGOT_APPROACHING_STOP_REPLICATION),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_MONGOT_SUFFICIENT_DISK_SPACE),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_NOW_PRIMARY),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_NOW_SECONDARY),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_NOW_STANDALONE),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_RECOVERED),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_RECOVERING),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_RESTARTED),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_ROLLBACK),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_SECURITY_CHECKUP_MET),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_SECURITY_CHECKUP_NOT_MET),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_SSL_CERTIFICATE_CURRENT),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_SSL_CERTIFICATE_STALE),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_UP),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_UPGRADED),
  @JsonSubTypes.Type(
      value = HostEventView.class,
      name = NDS_CLOUD_PROVIDER_CONSOLE_INFORMATION_DOWNLOADED),
  @JsonSubTypes.Type(value = HostEventView.class, name = NDS_HOST_LOGS_DOWNLOADED),
  @JsonSubTypes.Type(value = HostEventView.class, name = NEW_HOST),
  @JsonSubTypes.Type(value = HostEventView.class, name = PAUSE_HOST_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = REMOVE_HOST_FROM_REPLICA_SET_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = RESUME_HOST_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = SSH_KEY_NDS_HOST_ACCESS_ATTEMPT),
  @JsonSubTypes.Type(value = HostEventView.class, name = SSH_KEY_NDS_HOST_ACCESS_GRANTED),
  @JsonSubTypes.Type(value = HostEventView.class, name = SSH_KEY_NDS_HOST_ACCESS_REFRESHED),
  @JsonSubTypes.Type(value = HostEventView.class, name = SSH_KEY_NDS_HOST_ACCESS_REQUESTED),
  @JsonSubTypes.Type(value = HostEventView.class, name = SSH_KEY_NDS_HOST_ACCESS_LEVEL_CHANGED),
  @JsonSubTypes.Type(value = HostEventView.class, name = ALERT_HOST_SSH_SESSION_STARTED),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_SSH_SESSION_ENDED),
  @JsonSubTypes.Type(
      value = HostEventView.class,
      name = HOST_X509_CERTIFICATE_CERTIFICATE_GENERATED_FOR_SUPPORT_ACCESS),
  @JsonSubTypes.Type(value = HostEventView.class, name = UNDELETE_HOST_AUDIT),
  @JsonSubTypes.Type(value = HostEventView.class, name = VERSION_BEHIND),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_VERSION_BEHIND),
  @JsonSubTypes.Type(value = HostEventView.class, name = VERSION_CHANGED),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_VERSION_CHANGED),
  @JsonSubTypes.Type(value = HostEventView.class, name = VERSION_CURRENT),
  @JsonSubTypes.Type(value = HostEventView.class, name = HOST_VERSION_CURRENT),
  @JsonSubTypes.Type(value = HostEventView.class, name = PUSH_BASED_LOG_EXPORT_RESUMED),
  @JsonSubTypes.Type(value = HostEventView.class, name = PUSH_BASED_LOG_EXPORT_STOPPED),
  @JsonSubTypes.Type(value = HostEventView.class, name = PUSH_BASED_LOG_EXPORT_DROPPED_LOG),
  @JsonSubTypes.Type(value = HostMetricEventView.class, name = INSIDE_METRIC_THRESHOLD),
  @JsonSubTypes.Type(value = HostMetricEventView.class, name = OUTSIDE_METRIC_THRESHOLD),
  @JsonSubTypes.Type(
      value = MaintenanceWindowConfigAuditView.class,
      name = MAINTENANCE_WINDOW_ADDED_AUDIT),
  @JsonSubTypes.Type(
      value = MaintenanceWindowConfigAuditView.class,
      name = MAINTENANCE_WINDOW_DELETED_AUDIT),
  @JsonSubTypes.Type(
      value = MaintenanceWindowConfigAuditView.class,
      name = MAINTENANCE_WINDOW_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_RESURRECTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_READY),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_UPDATE_SUBMITTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_UPDATE_SUBMITTED_INTERNAL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_PROCESS_ARGS_UPDATE_SUBMITTED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLUSTER_MONGOT_PROCESS_ARGS_UPDATE_SUBMITTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_SERVER_PARAMETERS_UPDATE_SUBMITTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_AUTOMATICALLY_PAUSED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_UPDATE_STARTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_UPDATE_STARTED_INTERNAL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_UPDATE_COMPLETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MATERIAL_CLUSTER_UPDATE_COMPLETED_INTERNAL),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = BAAS_RELEVANT_CLUSTER_UPDATE_COMPLETED_INTERNAL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_DELETE_SUBMITTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_DELETE_SUBMITTED_INTERNAL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IMPORT_STARTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IMPORT_ACKNOWLEDGED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IMPORT_CANCELLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IMPORT_EXPIRED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IMPORT_EXTENDED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IMPORT_CUTOVER),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IMPORT_COMPLETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IMPORT_VALIDATION_SUCCESS),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IMPORT_VALIDATION_FAIL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IMPORT_RESTART_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PROJECT_LIVE_IMPORT_OVERRIDES_ADDED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PROJECT_LIVE_IMPORT_OVERRIDES_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PROJECT_LIVE_IMPORT_OVERRIDES_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_OPLOG_RESIZED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_RESTARTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_STOP_START),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_RESYNC_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_RESYNC_CLEARED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_UPDATE_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_REPLACED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_REPLACE_CLEARED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_CONFIG_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_AGENT_API_KEY_ROTATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_SSL_ROTATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_SSL_ROTATED_PER_CLUSTER),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_SSL_REVOKED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = RELOAD_SSL_ON_PROCESSES),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = RELOAD_SSL_ON_PROCESSES_REQUESTED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLUSTER_INSTANCE_ADMIN_BACKUP_SNAPSHOT_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = UPDATE_BUMPER_FILES),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = DATA_LAKE_QUERY_LOGS_DOWNLOADED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = FEDERATED_DATABASE_QUERY_LOGS_DOWNLOADED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_QUERY_LOGS_DOWNLOADED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGODB_LOGS_DOWNLOADED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGOSQLD_LOGS_DOWNLOADED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGOT_LOGS_DOWNLOADED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGODB_USER_ADDED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGODB_USER_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGODB_USER_X509_CERT_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGODB_USER_X509_CERT_REVOKED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGODB_USER_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGODB_ROLE_ADDED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGODB_ROLE_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGODB_ROLE_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = NETWORK_PERMISSION_ENTRY_ADDED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = NETWORK_PERMISSION_ENTRY_REMOVED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = NETWORK_PERMISSION_ENTRY_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PRIVATE_NETWORK_ENDPOINT_ENTRY_ADDED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PRIVATE_NETWORK_ENDPOINT_ENTRY_REMOVED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PRIVATE_NETWORK_ENDPOINT_ENTRY_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PLANNING_FAILURE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PLAN_STARTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PLAN_COMPLETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PLAN_FAILURE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PLAN_ABANDONED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PLAN_FAILURE_COUNT_RESET),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PLAN_ASAP_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = INDEPENDENT_SHARD_SCALING_AVAILABLE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = INDEPENDENT_SHARD_AUTO_SCALING_AVAILABLE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = INDEPENDENT_SHARD_SCALING_CLUSTER_MIGRATED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = INDEPENDENT_SHARD_SCALING_CLUSTER_ROLLED_BACK),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MOVE_SKIPPED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STEP_SKIPPED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PROXY_RESTARTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PROXY_PANICKED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_MAINTENANCE_PROTECTED_HOURS_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_MAINTENANCE_PROTECTED_HOURS_MODIFIED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_MAINTENANCE_PROTECTED_HOURS_REMOVED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_MAINTENANCE_WINDOW_ADDED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_MAINTENANCE_WINDOW_MODIFIED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_MAINTENANCE_WINDOW_REMOVED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_MAINTENANCE_START_ASAP),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = ATLAS_MAINTENANCE_SCHEDULED_FOR_NEXT_WINDOW),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_MAINTENANCE_DEFERRED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_MAINTENANCE_AUTO_DEFER_ENABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_MAINTENANCE_AUTO_DEFER_DISABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_MAINTENANCE_RESET_BY_ADMIN),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = SCHEDULED_MAINTENANCE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PROJECT_SCHEDULED_MAINTENANCE),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = PROJECT_SCHEDULED_MAINTENANCE_OUTSIDE_OF_PROTECTED_HOURS),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PROJECT_LIMIT_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PROJECT_OPERATIONAL_LIMIT_UPDATED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = PROJECT_ENABLE_EXTENDED_STORAGE_SIZES_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = OS_MAINTENANCE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = OS_MAINTENANCE_RESTART),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = OS_MAINTENANCE_REPLACEMENT),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = NRA_REPLACEMENT_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = FREE_UPGRADE_STARTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = FLEX_UPGRADE_STARTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = SERVERLESS_UPGRADE_STARTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = TEST_FAILOVER_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = USER_SECURITY_SETTINGS_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = AUDIT_LOG_CONFIGURATION_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STREAMS_AUDIT_LOG_CONFIGURATION_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ENCRYPTION_AT_REST_CONFIGURATION_UPDATED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = ENCRYPTION_AT_REST_CONFIGURATION_VALIDATION_FAILED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = ENCRYPTION_AT_REST_CONFIGURATION_VALIDATION_SUCCEEDED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = NDS_SET_IMAGE_OVERRIDES),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = NDS_SET_CHEF_TARBALL_URI),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = RESTRICTED_EMPLOYEE_ACCESS_BYPASS),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = REVOKED_EMPLOYEE_ACCESS_BYPASS),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = DEVICE_SYNC_DEBUG_ACCESS_GRANTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = DEVICE_SYNC_DEBUG_ACCESS_REVOKED),
  @JsonSubTypes.Type(value = AccessEventView.class, name = EMPLOYEE_DOWNLOADED_CLUSTER_LOGS),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = DEVICE_SYNC_DEBUG_X509_CERT_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = EMPLOYEE_ACCESS_GRANTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = EMPLOYEE_ACCESS_REVOKED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = QUERY_ENGINE_TENANT_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = QUERY_ENGINE_TENANT_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = QUERY_ENGINE_TENANT_REMOVED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = FEDERATED_DATABASE_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = FEDERATED_DATABASE_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = FEDERATED_DATABASE_REMOVED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = X509_USER_CERTIFICATE_GENERATED_BY_APP_INTEGRATION),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = STREAM_KAFKA_CONNECTION_ACCESSED_BY_APP_INTEGRATION),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = STREAM_HTTPS_CONNECTION_ACCESSED_BY_APP_INTEGRATION),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = STREAM_AWS_KINESIS_DATA_STREAMS_CONNECTION_ACCESSED_BY_APP_INTEGRATION),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = STREAM_S3_CONNECTION_ACCESSED_BY_APP_INTEGRATION),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = TENANT_CLUSTER_UPGRADE_FROM_MTM),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = TENANT_SNAPSHOT_FAILED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = TENANT_RESTORE_FAILED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = SAMPLE_DATASET_LOAD_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CUSTOMER_X509_CRL_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CONTAINER_SUBNETS_UPDATE_REQUESTED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLEAR_UNPROVISIONED_TARGET_GROUPS_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_PAUSE_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_PAUSED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_ACTIVE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_ORPHANED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = ONLINE_ARCHIVE_DATA_EXPIRATION_RULE_ENABLED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = ONLINE_ARCHIVE_DATA_EXPIRATION_RULE_UPDATED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = ONLINE_ARCHIVE_DATA_EXPIRATION_RULE_DISABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_DELETE_AFTER_DATE_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLOUD_PROVIDER_ACCESS_AWS_IAM_ROLE_ADDED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLOUD_PROVIDER_ACCESS_AWS_IAM_ROLE_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLOUD_PROVIDER_ACCESS_AWS_IAM_ROLE_UPDATED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLOUD_PROVIDER_ACCESS_AZURE_SERVICE_PRINCIPAL_ADDED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLOUD_PROVIDER_ACCESS_AZURE_SERVICE_PRINCIPAL_UPDATED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLOUD_PROVIDER_ACCESS_AZURE_SERVICE_PRINCIPAL_DELETED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLOUD_PROVIDER_ACCESS_GCP_SERVICE_ACCOUNT_ADDED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLOUD_PROVIDER_ACCESS_GCP_SERVICE_ACCOUNT_DELETED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLOUD_PROVIDER_ACCESS_GCP_SERVICE_ACCOUNT_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PENDING_INDEXES_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PENDING_INDEXES_CANCELED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PROCESS_RESTART_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ORG_LIMIT_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLOUD_PROVIDER_THROTTLE_STATE_MODIFIED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PROJECT_THROTTLE_STATE_MODIFIED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_THROTTLE_STATE_MODIFIED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = FORCE_MIGRATE_FROM_AVAILABILITY_SETS),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = TENANT_CLUSTER_LIMIT_MODIFIED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_SET_CPU_SOCKET_BINDING),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = AUTO_HEALING_ACTION),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = AUTO_HEALING_REQUESTED_CRITICAL_INSTANCE_POWER_CYCLE),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = AUTO_HEALING_REQUESTED_INSTANCE_REPLACEMENT),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = AUTO_HEALING_REQUESTED_NODE_RESYNC),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = AUTO_HEALING_CANNOT_REPAIR_INTERNAL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = AUTO_HEALING_CANNOT_RESYNC_INTERNAL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = AUTO_HEALING_FIXED_INTERNAL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = EXTRA_MAINTENANCE_DEFERRAL_GRANTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ADMIN_NOTE_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ADMIN_CLUSTER_LOCK_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = GROUP_AUTOMATION_CONFIG_PUBLISHED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_AUTOMATION_CONFIG_PUBLISHED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = SET_ENSURE_CLUSTER_CONNECTIVITY_AFTER_FOR_CLUSTER),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_LINKED_TO_VERCEL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_UNLINKED_FROM_VERCEL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = INGESTION_PIPELINE_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = INGESTION_PIPELINE_DESTROYED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = INGESTION_PIPELINE_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = INGESTION_PIPELINE_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = OS_TUNE_FILE_OVERRIDES),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONITORING_AGENT_OVERRIDES),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLUSTER_PREFERRED_CPU_ARCHITECTURE_MODIFIED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_FORCE_PLANNED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = DATA_PROCESSING_REGION_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_REGIONAL_OUTAGE_SIMULATION_STARTED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLUSTER_REGIONAL_OUTAGE_SIMULATION_FAILED_TO_START),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLUSTER_REGIONAL_OUTAGE_SIMULATION_END_REQUESTED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLUSTER_REGIONAL_OUTAGE_SIMULATION_COMPLETED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLUSTER_REGIONAL_OUTAGE_SIMULATION_CANCELLED_CLUSTER_PAUSE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = TENANT_UPGRADE_TO_SERVERLESS_SUCCESSFUL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = TENANT_UPGRADE_TO_SERVERLESS_FAILED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = SERVERLESS_UPGRADE_TO_DEDICATED_SUCCESSFUL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = SERVERLESS_UPGRADE_TO_DEDICATED_FAILED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = UIS_PANICKED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_FORCE_RECONFIG_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PROJECT_BYPASSED_MAINTENANCE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = FEATURE_FLAG_MAINTENANCE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = DATA_FEDERATION_QUERY_LIMIT_CONFIGURED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = DATA_FEDERATION_QUERY_LIMIT_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = DATA_API_SETUP_FOR_VERCEL),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STREAM_TENANT_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STREAM_TENANT_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STREAM_TENANT_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STREAM_TENANT_CONNECTIONS_LISTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STREAM_TENANT_CONNECTION_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STREAM_TENANT_CONNECTION_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STREAM_TENANT_CONNECTION_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STREAM_TENANT_CONNECTION_VIEWED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STREAM_TENANT_AUDIT_LOGS),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = STREAM_TENANT_AUDIT_LOGS_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = QUEUED_ADMIN_ACTION_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = QUEUED_ADMIN_ACTION_COMPLETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = QUEUED_ADMIN_ACTION_CANCELLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = QUEUED_ADMIN_ACTION_FAILED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_SQL_SCHEDULED_UPDATE_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_SQL_SCHEDULED_UPDATE_MODIFIED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ATLAS_SQL_SCHEDULED_UPDATE_REMOVED),
  @JsonSubTypes.Type(value = NDSAutoScalingAuditView.class, name = COMPUTE_AUTO_SCALE_UNNECESSARY),
  @JsonSubTypes.Type(value = NDSAutoScalingAuditView.class, name = COMPUTE_AUTO_SCALE_TRIGGERED),
  @JsonSubTypes.Type(value = NDSAutoScalingAuditView.class, name = COMPUTE_AUTO_SCALE_SKIPPED),
  @JsonSubTypes.Type(value = NDSAutoScalingAuditView.class, name = COMPUTE_AUTO_SCALE_INITIATED),
  @JsonSubTypes.Type(value = NDSAutoScalingAuditView.class, name = DISK_AUTO_SCALE_INITIATED),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = DISK_AUTO_SCALE_MAX_DISK_SIZE_FAIL),
  @JsonSubTypes.Type(value = NDSAutoScalingAuditView.class, name = DISK_AUTO_SCALE_OPLOG_FAIL),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = COMPUTE_AUTO_SCALE_TRIGGERED_BASE),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = COMPUTE_AUTO_SCALE_TRIGGERED_ANALYTICS),
  @JsonSubTypes.Type(value = NDSAutoScalingAuditView.class, name = COMPUTE_AUTO_SCALE_SKIPPED_BASE),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = COMPUTE_AUTO_SCALE_SKIPPED_ANALYTICS),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = COMPUTE_AUTO_SCALE_INITIATED_BASE),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = COMPUTE_AUTO_SCALE_INITIATED_ANALYTICS),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = COMPUTE_AUTO_SCALE_SCALE_DOWN_FAIL_BASE),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = COMPUTE_AUTO_SCALE_SCALE_DOWN_FAIL_ANALYTICS),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = COMPUTE_AUTO_SCALE_MAX_INSTANCE_SIZE_FAIL_BASE),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = COMPUTE_AUTO_SCALE_MAX_INSTANCE_SIZE_FAIL_ANALYTICS),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = COMPUTE_AUTO_SCALE_OPLOG_FAIL_BASE),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = COMPUTE_AUTO_SCALE_OPLOG_FAIL_ANALYTICS),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = PREDICTIVE_COMPUTE_AUTO_SCALE_INITIATED_BASE),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = PREDICTIVE_COMPUTE_AUTO_SCALE_MAX_INSTANCE_SIZE_FAIL_BASE),
  @JsonSubTypes.Type(
      value = NDSAutoScalingAuditView.class,
      name = PREDICTIVE_COMPUTE_AUTO_SCALE_OPLOG_FAIL_BASE),
  @JsonSubTypes.Type(
      value = NDSServerlessInstanceAuditView.class,
      name = SERVERLESS_INSTANCE_CREATED),
  @JsonSubTypes.Type(
      value = NDSServerlessInstanceAuditView.class,
      name = SERVERLESS_INSTANCE_READY),
  @JsonSubTypes.Type(
      value = NDSServerlessInstanceAuditView.class,
      name = SERVERLESS_INSTANCE_UPDATE_SUBMITTED),
  @JsonSubTypes.Type(
      value = NDSServerlessInstanceAuditView.class,
      name = SERVERLESS_INSTANCE_UPDATE_STARTED),
  @JsonSubTypes.Type(
      value = NDSServerlessInstanceAuditView.class,
      name = SERVERLESS_INSTANCE_UPDATE_COMPLETED),
  @JsonSubTypes.Type(
      value = NDSServerlessInstanceAuditView.class,
      name = SERVERLESS_INSTANCE_DELETE_SUBMITTED),
  @JsonSubTypes.Type(
      value = NDSServerlessInstanceAuditView.class,
      name = SERVERLESS_INSTANCE_DELETED),
  @JsonSubTypes.Type(
      value = NDSServerlessInstanceAuditView.class,
      name = SERVERLESS_INSTANCE_BLOCKED),
  @JsonSubTypes.Type(
      value = NDSServerlessInstanceAuditView.class,
      name = SERVERLESS_INSTANCE_UNBLOCKED),
  @JsonSubTypes.Type(value = FlexMigrationEventView.class, name = FLEX_TENANT_MIGRATION_STARTED),
  @JsonSubTypes.Type(value = FlexMigrationEventView.class, name = FLEX_TENANT_MIGRATION_COMPLETED),
  @JsonSubTypes.Type(value = FlexMigrationEventView.class, name = FLEX_TENANT_MIGRATION_ERRORED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_TENANT_MIGRATION_ROLLBACK_STARTED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_TENANT_MIGRATION_ROLLBACK_COMPLETED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_TENANT_MIGRATION_ROLLBACK_ERRORED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_CLUSTER_MIGRATION_STARTED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_CLUSTER_MIGRATION_COMPLETED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_CLUSTER_MIGRATION_ERRORED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_CLUSTER_MIGRATION_ROLLBACK_STARTED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_CLUSTER_MIGRATION_ROLLBACK_COMPLETED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_CLUSTER_MIGRATION_ROLLBACK_ERRORED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_HOLDER_GROUP_MIGRATION_STARTED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_HOLDER_GROUP_MIGRATION_COMPLETED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_HOLDER_GROUP_MIGRATION_ERRORED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_HOLDER_GROUP_MIGRATION_ROLLBACK_STARTED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_HOLDER_GROUP_MIGRATION_ROLLBACK_COMPLETED),
  @JsonSubTypes.Type(
      value = FlexMigrationEventView.class,
      name = FLEX_MTM_HOLDER_GROUP_MIGRATION_ROLLBACK_ERRORED),
  @JsonSubTypes.Type(value = NDSTenantEndpointAuditView.class, name = TENANT_ENDPOINT_CREATED),
  @JsonSubTypes.Type(value = NDSTenantEndpointAuditView.class, name = TENANT_ENDPOINT_RESERVED),
  @JsonSubTypes.Type(
      value = NDSTenantEndpointAuditView.class,
      name = TENANT_ENDPOINT_RESERVATION_FAILED),
  @JsonSubTypes.Type(value = NDSTenantEndpointAuditView.class, name = TENANT_ENDPOINT_UPDATED),
  @JsonSubTypes.Type(value = NDSTenantEndpointAuditView.class, name = TENANT_ENDPOINT_INITIATING),
  @JsonSubTypes.Type(value = NDSTenantEndpointAuditView.class, name = TENANT_ENDPOINT_AVAILABLE),
  @JsonSubTypes.Type(value = NDSTenantEndpointAuditView.class, name = TENANT_ENDPOINT_FAILED),
  @JsonSubTypes.Type(value = NDSTenantEndpointAuditView.class, name = TENANT_ENDPOINT_DELETING),
  @JsonSubTypes.Type(value = NDSTenantEndpointAuditView.class, name = TENANT_ENDPOINT_DELETED),
  @JsonSubTypes.Type(value = NDSTenantEndpointAuditView.class, name = TENANT_ENDPOINT_EXPIRED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CREATED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = CUSTOM_SESSION_TIMEOUT_MODIFIED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = SECURITY_CONTACT_MODIFIED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CREDIT_CARD_ADDED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CREDIT_CARD_UPDATED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CREDIT_CARD_CURRENT),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CREDIT_CARD_ABOUT_TO_EXPIRE),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_PAYPAL_LINKED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_PAYPAL_UPDATED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_PAYPAL_CANCELLED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_OVERRIDE_PAYMENT_METHOD_ADDED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_ACTIVATED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_TEMPORARILY_ACTIVATED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_SUSPENSION_DATE_CHANGED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_SUSPENDED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_ADMIN_SUSPENDED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_ADMIN_LOCKED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CLUSTERS_DELETED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CLUSTERS_PAUSED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_IP_ACCESS_LIST_DELETED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_IP_ACCESS_LIST_DELETED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_IP_ACCESS_LIST_DELETED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_PRIVATE_ENDPOINTS_DELETED),
  @JsonSubTypes.Type(
      value = OrgEventView.class,
      name = ORG_ALL_PAID_SERVICES_TERMINATION_REQUESTED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_ALL_PAID_SERVICES_TERMINATED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_LOCKED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_UNDER_FINANCIAL_PROTECTION),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_NO_FINANCIAL_PROTECTION),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_COMPANY_NAME_OFAC_HIT),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_EMBARGO_CONFIRMED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_UNEMBARGOED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_DELETED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_RENAMED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ALL_ORG_USERS_HAVE_MFA),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_USERS_WITHOUT_MFA),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_INVOICE_UNDER_THRESHOLD),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_INVOICE_OVER_THRESHOLD),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_DAILY_BILL_UNDER_THRESHOLD),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_DAILY_BILL_OVER_THRESHOLD),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_GROUP_CHARGES_UNDER_THRESHOLD),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_GROUP_CHARGES_OVER_THRESHOLD),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_TWO_FACTOR_AUTH_REQUIRED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_TWO_FACTOR_AUTH_OPTIONAL),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_PUBLIC_API_ACCESS_LIST_REQUIRED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_PUBLIC_API_ACCESS_LIST_NOT_REQUIRED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_EMPLOYEE_ACCESS_RESTRICTED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_EMPLOYEE_ACCESS_UNRESTRICTED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_SFDC_ACCOUNT_ID_CHANGED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CONNECTED_TO_MLAB),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_DISCONNECTED_FROM_MLAB),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_IDP_CERTIFICATE_CURRENT),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_IDP_CERTIFICATE_ABOUT_TO_EXPIRE),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CONNECTED_TO_VERCEL),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_DISCONNECTED_TO_VERCEL),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CONNECTION_UNINSTALLED_FROM_VERCEL),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_UI_IP_ACCESS_LIST_ENABLED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_UI_IP_ACCESS_LIST_DISABLED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_EDITED_UI_IP_ACCESS_LIST_ENTRIES),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CUSTOM_POLICY_CREATED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CUSTOM_POLICY_UPDATED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_CUSTOM_POLICY_DELETED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_MONGODB_VERSION_EOL_EXTENSION_ACCEPTED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_MONGODB_VERSION_EOL_EXTENSION_PENDING),
  @JsonSubTypes.Type(
      value = OrgEventView.class,
      name = ORG_MONGODB_VERSION_EOL_EXTENSION_CANCELLED),
  @JsonSubTypes.Type(
      value = OrgEventView.class,
      name = ORG_SERVICE_ACCOUNT_MAX_SECRET_VALIDITY_EDITED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_SERVICE_ACCOUNT_SECRETS_EXPIRED),
  @JsonSubTypes.Type(
      value = OrgEventView.class,
      name = ORG_SERVICE_ACCOUNT_SECRETS_NO_LONGER_EXPIRED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_SERVICE_ACCOUNT_SECRETS_EXPIRING),
  @JsonSubTypes.Type(
      value = OrgEventView.class,
      name = ORG_SERVICE_ACCOUNT_SECRETS_NO_LONGER_EXPIRING),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_GEN_AI_ENABLED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_GEN_AI_DISABLED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = GROUP_MOVED_FROM_ORG),
  @JsonSubTypes.Type(value = OrgEventView.class, name = SANDBOX_ENABLED_FOR_ORG),
  @JsonSubTypes.Type(value = OrgEventView.class, name = SANDBOX_DISABLED_FOR_ORG),
  @JsonSubTypes.Type(value = OrgEventView.class, name = SANDBOX_CONFIG_DELETED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_STREAMS_CROSS_GROUP_ENABLED),
  @JsonSubTypes.Type(value = OrgEventView.class, name = ORG_STREAMS_CROSS_GROUP_DISABLED),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = CONFIGURATION_CHANGED),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = ENOUGH_HEALTHY_MEMBERS),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = MEMBER_ADDED),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = MEMBER_REMOVED),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = NO_PRIMARY),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = ONE_PRIMARY),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = PRIMARY_ELECTED),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = REPLICATION_OPLOG_WINDOW_HEALTHY),
  @JsonSubTypes.Type(
      value = ReplicaSetEventView.class,
      name = REPLICATION_OPLOG_WINDOW_RUNNING_OUT),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = SERVERLESS_PROXIES_REPORTING),
  @JsonSubTypes.Type(
      value = ReplicaSetEventView.class,
      name = SERVERLESS_PROXIES_STOPPED_REPORTING),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = FLEX_PROXIES_REPORTING),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = FLEX_PROXIES_STOPPED_REPORTING),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = TOO_FEW_HEALTHY_MEMBERS),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = TOO_MANY_ELECTIONS),
  @JsonSubTypes.Type(value = ReplicaSetEventView.class, name = TOO_MANY_UNHEALTHY_MEMBERS),
  @JsonSubTypes.Type(value = ResourceEventView.class, name = TAGS_MODIFIED),
  @JsonSubTypes.Type(value = ResourceEventView.class, name = GROUP_TAGS_MODIFIED),
  @JsonSubTypes.Type(value = ResourceEventView.class, name = CLUSTER_TAGS_MODIFIED),
  @JsonSubTypes.Type(value = TeamEventView.class, name = TEAM_CREATED),
  @JsonSubTypes.Type(value = TeamEventView.class, name = TEAM_DELETED),
  @JsonSubTypes.Type(value = TeamEventView.class, name = TEAM_UPDATED),
  @JsonSubTypes.Type(value = TeamEventView.class, name = TEAM_NAME_CHANGED),
  @JsonSubTypes.Type(value = TeamEventView.class, name = TEAM_ADDED_TO_GROUP),
  @JsonSubTypes.Type(value = TeamEventView.class, name = TEAM_REMOVED_FROM_GROUP),
  @JsonSubTypes.Type(value = TeamEventView.class, name = TEAM_ROLES_MODIFIED),
  @JsonSubTypes.Type(value = UserGroupEventView.class, name = USER_GROUP_CREATED),
  @JsonSubTypes.Type(value = UserGroupEventView.class, name = USER_GROUP_DELETED),
  @JsonSubTypes.Type(value = UserGroupEventView.class, name = USER_GROUP_MEMBER_ADDED),
  @JsonSubTypes.Type(value = UserGroupEventView.class, name = USER_GROUP_MEMBER_REMOVED),
  @JsonSubTypes.Type(value = UserGroupEventView.class, name = USER_GROUP_NAME_CHANGED),
  @JsonSubTypes.Type(value = UserGroupEventView.class, name = USER_GROUP_POLICIES_CHANGED),
  @JsonSubTypes.Type(value = UserEventView.class, name = ACCOUNT_LOCKED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = ACCOUNT_UNLOCKED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = INITIATE_SFDC_SYNC),
  @JsonSubTypes.Type(value = UserEventView.class, name = GROUP_INVITATION_DELETED),
  @JsonSubTypes.Type(value = UserEventView.class, name = ORG_INVITATION_DELETED),
  @JsonSubTypes.Type(value = UserEventView.class, name = INVITED_TO_GROUP),
  @JsonSubTypes.Type(value = UserEventView.class, name = INVITED_TO_ORG),
  @JsonSubTypes.Type(value = UserEventView.class, name = INVITED_TO_TEAM),
  @JsonSubTypes.Type(value = UserEventView.class, name = JOINED_GROUP),
  @JsonSubTypes.Type(value = UserEventView.class, name = JOINED_ORG),
  @JsonSubTypes.Type(value = UserEventView.class, name = JOINED_TEAM),
  @JsonSubTypes.Type(value = UserEventView.class, name = JOIN_GROUP_REQUEST_APPROVED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = JOIN_GROUP_REQUEST_DENIED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = LEGACY_2FA_RESET_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = LEGACY_2FA_RESET_EMAIL_SENT_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = LEGACY_2FA_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = MULTI_FACTOR_AUTH_RESET_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = MULTI_FACTOR_AUTH_RESET_EMAIL_SENT_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = MULTI_FACTOR_AUTH_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = ORG_FLEX_CONSULTING_PURCHASED),
  @JsonSubTypes.Type(value = UserEventView.class, name = ORG_FLEX_CONSULTING_PURCHASE_FAILED),
  @JsonSubTypes.Type(value = UserEventView.class, name = PASSWORD_RESET_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = PASSWORD_RESET_EMAIL_SENT_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = PASSWORD_RESET_FAILED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = PASSWORD_RESET_FORM_VIEWED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = PASSWORD_UPDATED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = REMOVED_FROM_GROUP),
  @JsonSubTypes.Type(value = UserEventView.class, name = REMOVED_FROM_ORG),
  @JsonSubTypes.Type(value = UserEventView.class, name = REMOVED_FROM_TEAM),
  @JsonSubTypes.Type(value = UserEventView.class, name = REQUESTED_TO_JOIN_GROUP),
  @JsonSubTypes.Type(value = UserEventView.class, name = SELF_SERVE_DELETION_REQUESTED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = SUCCESSFUL_DEVICE_CONFIRMATION_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = SUCCESSFUL_LOGIN_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = UNSUCCESSFUL_LOGIN_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_ACCOUNT_EMAIL_ADDRESS_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_CANCELED_EMAIL_CHANGE_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_CANCELED_EMAIL_VALIDATION_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_CREATED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_DELETED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_EMAIL_ADDRESS_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_EMAIL_VERIFIED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_FIRST_NAME_LAST_NAME_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_HARD_DELETED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_NAME_OFAC_HIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_PROFILE_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_REQUESTED_EMAIL_ADDRESS_CHANGE_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_RESTORED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_ROLES_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_SALESFORCE_CONTACT_ID_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_UNEMBARGOED),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_VERIFICATION_EMAIL_SENT_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = PASSWORD_RESET_LINK_GENERATED_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = PASSWORD_FORCE_RESET_AUDIT),
  @JsonSubTypes.Type(value = UserEventView.class, name = USER_POLICY_ASSIGNMENTS_CHANGED_AUDIT),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_ROLLING_RESYNC_STARTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_ROLLING_RESYNC_COMPLETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_ROLLING_RESYNC_FAILED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = NODE_ROLLING_RESYNC_SCHEDULED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_ROLLING_RESYNC_CANCELED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_OS_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_FAMILY_UPDATED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = AZURE_CLUSTER_PREFERRED_STORAGE_TYPE_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_V3_MIGRATION_STARTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_V3_MIGRATION_SUCCEEDED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = ONLINE_ARCHIVE_V3_MIGRATION_FAILED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PUSH_BASED_LOG_EXPORT_ENABLED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = PUSH_BASED_LOG_EXPORT_CONFIGURATION_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = PUSH_BASED_LOG_EXPORT_DISABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = LOG_STREAMING_ENABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = LOG_STREAMING_CONFIGURATION_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = LOG_STREAMING_DISABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = DATADOG_LOG_STREAMING_ENABLED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = DATADOG_LOG_STREAMING_CONFIGURATION_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = DATADOG_LOG_STREAMING_DISABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = SPLUNK_LOG_STREAMING_ENABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = SPLUNK_LOG_STREAMING_CONFIGURATION_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = SPLUNK_LOG_STREAMING_DISABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = S3_LOG_STREAMING_ENABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = S3_LOG_STREAMING_CONFIGURATION_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = S3_LOG_STREAMING_DISABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CONTAINER_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = REGIONALIZED_PRIVATE_ENDPOINT_MODE_ENABLED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = REGIONALIZED_PRIVATE_ENDPOINT_MODE_DISABLED),
  @JsonSubTypes.Type(value = ConfigNamespaceAuditEventView.class, name = CONFIG_NAMESPACE_CREATED),
  @JsonSubTypes.Type(value = ConfigNamespaceAuditEventView.class, name = CONFIG_NAMESPACE_UPDATED),
  @JsonSubTypes.Type(value = ConfigNamespaceAuditEventView.class, name = CONFIG_NAMESPACE_DELETED),
  @JsonSubTypes.Type(value = ConfigNamespaceAuditEventView.class, name = CONFIG_NAMESPACE_DELETED),
  @JsonSubTypes.Type(
      value = ConfigApplicationPropertyAuditEventView.class,
      name = CONFIG_APPLICATION_PROPERTY_CREATED),
  @JsonSubTypes.Type(
      value = ConfigApplicationPropertyAuditEventView.class,
      name = CONFIG_APPLICATION_PROPERTY_UPDATED),
  @JsonSubTypes.Type(
      value = ConfigApplicationPropertyAuditEventView.class,
      name = CONFIG_APPLICATION_PROPERTY_DELETED),
  @JsonSubTypes.Type(
      value = ConfigFeatureFlagAuditEventView.class,
      name = CONFIG_FEATURE_FLAG_UPDATED),
  @JsonSubTypes.Type(value = CaptainLogAuditView.class, name = CAPTAIN_LOG),
  @JsonSubTypes.Type(value = AtlasResourcePolicyAuditView.class, name = RESOURCE_POLICY_CREATED),
  @JsonSubTypes.Type(value = AtlasResourcePolicyAuditView.class, name = RESOURCE_POLICY_MODIFIED),
  @JsonSubTypes.Type(value = AtlasResourcePolicyAuditView.class, name = RESOURCE_POLICY_DELETED),
  @JsonSubTypes.Type(value = AtlasResourcePolicyAuditView.class, name = RESOURCE_POLICY_VIOLATED),
  @JsonSubTypes.Type(
      value = BulkAllocationAuditEventView.class,
      name = AB_TEST_BULK_ALLOCATION_SUBMITTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_DISABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_INSTANCE_ENABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_BLOCK_WRITE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_UNBLOCK_WRITE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = KMIP_KEY_ROTATION_SCHEDULED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = SSL_CERTIFICATE_ISSUED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = EXTERNAL_MAINTENANCE_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = EXTERNAL_MAINTENANCE_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = EXTERNAL_MAINTENANCE_CANCELED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = JOB_PRIORITY_CONFIG_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = JOB_PRIORITY_CONFIG_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = JOB_PRIORITY_CONFIG_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = JOB_PRIORITY_SETTING_CREATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = JOB_PRIORITY_SETTING_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = JOB_PRIORITY_SETTING_DELETED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_CANCELING_SHARD_DRAIN_REQUESTED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = CLUSTER_MIGRATE_BACK_TO_AWS_MANAGED_IP_REQUESTED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IP_MIGRATED_FIRST_ROUND),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IP_MIGRATED_SECOND_ROUND),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IP_MIGRATED_FINAL_ROUND),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = CLUSTER_IP_ROLLED_BACK),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = AZ_BALANCING_OVERRIDE_MODIFIED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = FTDC_SETTINGS_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGOTUNE_ENABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGOTUNE_DISABLED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGOTUNE_POLICIES_UPDATED),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGOTUNE_POLICY_ENABLED_BY_ADMIN),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGOTUNE_POLICY_DISABLED_BY_ADMIN),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = UNSET_USER_WRITE_BLOCK_MODE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGOTUNE_WRITE_BLOCK_POLICY_ELIGIBLE),
  @JsonSubTypes.Type(value = NDSAuditView.class, name = MONGOTUNE_WRITE_BLOCK_POLICY_INELIGIBLE),
  @JsonSubTypes.Type(value = ADFAAdminAuditView.class, name = ADFA_LIST_DATA_SETS_ACTION_COMPLETED),
  @JsonSubTypes.Type(
      value = ADFAAdminAuditView.class,
      name = ADFA_LIST_ANDON_CORDS_ACTION_COMPLETED),
  @JsonSubTypes.Type(
      value = ADFAAdminAuditView.class,
      name = ADFA_CREATE_OR_UPDATE_ANDON_CORD_ACTION_COMPLETED),
  @JsonSubTypes.Type(
      value = ADFAAdminAuditView.class,
      name = ADFA_TENANT_GET_SETTINGS_ACTION_COMPLETED),
  @JsonSubTypes.Type(
      value = ADFAAdminAuditView.class,
      name = ADFA_TENANT_SET_SETTINGS_ACTION_COMPLETED),
  @JsonSubTypes.Type(
      value = ADFAAdminAuditView.class,
      name = ADFA_TENANT_GET_STORAGE_CONFIG_ACTION_COMPLETED),
  @JsonSubTypes.Type(
      value = ADFAAdminAuditView.class,
      name = ADFA_TENANT_DELETE_STORAGE_CONFIG_ACTION_COMPLETED),
  @JsonSubTypes.Type(
      value = ADFAAdminAuditView.class,
      name = ADFA_TENANT_GET_CONFIG_ACTION_COMPLETED),
  @JsonSubTypes.Type(
      value = ADFAAdminAuditView.class,
      name = ADFA_LIST_CURRENT_OPS_ACTION_COMPLETED),
  @JsonSubTypes.Type(
      value = NDSAuditView.class,
      name = PROXY_PROTOCOL_FOR_PRIVATE_LINK_MODE_UPDATED),
})
public class EventViewHierarchy implements TypeHierarchy, TypeRegistry<String, EventType> {

  public static final String EVENT_TYPE_FIELD = "eventTypeName";

  private final EventTypeViewRegistry eventTypeViewRegistry;

  @Inject
  public EventViewHierarchy(final EventTypeViewRegistry eventTypeViewRegistry) {
    this.eventTypeViewRegistry = eventTypeViewRegistry;
  }

  @Override
  public Class<?> getSuperType() {
    return com.xgen.cloud.activity._public.model.event.view.EventView.class;
  }

  @Override
  public EventType getTypeByKey(String key) {
    return eventTypeViewRegistry.getTypeByKey(key);
  }

  @Override
  public String getKeyByType(EventType type) {
    return eventTypeViewRegistry.getKeyByType(type);
  }

  @Schema(
      title = "string",
      enumAsRef = true,
      oneOf = {
        AccessEventTypeView.class,
        ADFAAdminAuditTypeView.class,
        AdminAuditTypeView.class,
        AgentEventTypeView.class,
        AlertAuditTypeView.class,
        AlertConfigAuditTypeView.class,
        ApiUserEventTypeView.class,
        ServiceAccountEventTypeView.class,
        AppServiceEventTypeView.class,
        AppSettingsChangeAuditTypeView.class,
        AuthnServiceAuditTypeView.class,
        AutoIndexingEventTypeView.class,
        AutomationConfigEventTypeView.class,
        AWSPeerVpcAuditTypeView.class,
        AzurePeerNetworkAuditTypeView.class,
        BackupEventTypeView.class,
        BiConnectorEventTypeView.class,
        BillingEventTypeView.class,
        BillingAuditorEventTypeView.class,
        BulkAllocationAuditEventTypeView.class,
        ClusterConnectionAuditTypeView.class,
        ClusterEventTypeView.class,
        ConfigNamespaceAuditEventTypeView.class,
        ConfigApplicationPropertyAuditEventTypeView.class,
        ConfigFeatureFlagAuditEventTypeView.class,
        CpsBackupEventTypeView.class,
        CpsBillingEventTypeView.class,
        CrashLogEventTypeView.class,
        CronAuditTypeView.class,
        DataExplorerAccessedEventTypeView.class,
        DataExplorerEventTypeView.class,
        DataProtectionAuditTypeView.class,
        DeviceCodeAuditTypeView.class,
        DiskBackupEventTypeView.class,
        EncryptionKeyEventTypeView.class,
        ExportBucketAuditTypeView.class,
        FederationSettingsEventTypeView.class,
        FlappingEventTypeView.class,
        FTSIndexAuditTypeView.class,
        GCPPeerVpcAuditTypeView.class,
        GlobalAccessListEventTypeView.class,
        GlobalServiceAccountEventTypeView.class,
        SystemAlertGlobalServiceAccountEventTypeView.class,
        GroupAuditTypeView.class,
        GroupEventTypeView.class,
        GroupIntegrationEventTypeView.class,
        HostEventTypeView.class,
        HostMetricEventTypeView.class,
        IndexBuildAuditTypeView.class,
        LogCollectionRequestAuditTypeView.class,
        LongRunningMoveEventTypeView.class,
        MaintenanceWindowConfigAuditTypeView.class,
        MlabMigrationAuditTypeView.class,
        MongotEventTypeView.class,
        NDSAuditTypeView.class,
        NDSDbCheckAuditTypeView.class,
        NDSDataValidationAuditTypeView.class,
        NDSAutoScalingAuditTypeView.class,
        NDSMaintenanceWindowAuditTypeView.class,
        NDSServerlessAutoScalingAuditTypeView.class,
        NDSServerlessInstanceAuditTypeView.class,
        NDSServerlessTenantMigrationAuditTypeView.class,
        NDSTenantEndpointAuditTypeView.class,
        NDSTenantEndpointServiceDeploymentAuditTypeView.class,
        NDSX509UserAuthenticationEventTypeView.class,
        OnlineArchiveEventTypeView.class,
        OrgEventTypeView.class,
        PartnerEventTypeView.class,
        PrivateLinkAuditTypeView.class,
        ProactiveOperationEventTypeView.class,
        QuotaUsageEventTypeView.class,
        ReplicaSetEventTypeView.class,
        ResourceEventTypeView.class,
        SearchDeploymentAuditTypeView.class,
        ServerlessDeploymentAuditTypeView.class,
        ServerlessEventTypeView.class,
        FlexMetricEventTypeView.class,
        ServerlessMTMClusterGrpcIncrementalRolloutEventTypeView.class,
        SetupServerlessAuditTypeView.class,
        StreamsEventTypeView.class,
        StreamProcessorEventTypeView.class,
        SupportCaseEventTypeView.class,
        SupportEventTypeView.class,
        SystemAlertProcessingEventTypeView.class,
        SystemAWSEventTypeView.class,
        SystemAzureEventTypeView.class,
        SystemBackupDaemonEventTypeView.class,
        SystemBackupEventTypeView.class,
        SystemBillingEventTypeView.class,
        SystemBlockstoreEventTypeView.class,
        SystemConfigChangeAuditTypeView.class,
        SystemCronJobEventTypeView.class,
        SystemCronJobStatusEventTypeView.class,
        SystemDatabaseProcessEventTypeView.class,
        SystemGCPEventTypeView.class,
        SystemLogEventTypeView.class,
        SystemNDSEventTypeView.class,
        SystemMaasEventTypeView.class,
        TeamEventTypeView.class,
        TenantBackupEventTypeView.class,
        UserEventTypeView.class,
        UserGroupEventTypeView.class,
        VersionAuditTypeView.class,
        DbCheckEventTypeView.class,
        NDSCheckMetadataConsistencyEventTypeView.class,
        RateLimitSuspensionAuditEventTypeView.class,
        CaptainLogAuditTypeView.class,
        StreamProcessorEventTypeView.class,
        BumperFileRemovalEventTypeView.class,
        AtlasResourcePolicyAuditTypeView.class,
        LogIngestionEventTypeView.class,
        MPAEventTypeView.class,
        FlexMigrationEventTypeView.class,
        ChartsAuditTypeView.class,
        LogUploaderDownEventTypeView.class,
        AdminEventTypeView.class,
      })
  public interface EventTypeView {}

  @Schema(
      oneOf = {
        DefaultEventView.class,
        AccessEventView.class,
        ADFAAdminAuditView.class,
        AlertAuditView.class,
        AlertConfigAuditView.class,
        ApiUserEventView.class,
        ServiceAccountEventView.class,
        AppServiceEventView.class,
        AutomationConfigEventView.class,
        BackupEventView.class,
        BiConnectorEventView.class,
        BillingEventView.class,
        BulkAllocationAuditEventView.class,
        ClusterEventView.class,
        ConfigNamespaceAuditEventView.class,
        ConfigApplicationPropertyAuditEventView.class,
        ConfigFeatureFlagAuditEventView.class,
        DataExplorerAccessedEventView.class,
        DataExplorerEventView.class,
        FTSIndexAuditView.class,
        HostEventView.class,
        HostMetricEventView.class,
        MaintenanceWindowConfigAuditView.class,
        NDSAuditView.class,
        NDSAutoScalingAuditView.class,
        NDSServerlessInstanceAuditView.class,
        NDSTenantEndpointAuditView.class,
        OrgEventView.class,
        ReplicaSetEventView.class,
        ResourceEventView.class,
        SearchDeploymentAuditView.class,
        TeamEventView.class,
        UserGroupEventView.class,
        UserEventView.class,
        CpsBackupEventView.class,
        CaptainLogAuditView.class,
        StreamsEventView.class,
        StreamProcessorEventView.class,
        AtlasResourcePolicyAuditView.class,
        FlexMigrationEventView.class
      })
  public interface EventView {}

  @Schema(
      title = "Any Other Events",
      description = "Other events which don't have extra details beside of basic one.")
  public static class DefaultEventView extends EventWithAuditInfoView {

    // protected constructor for open API subclasses and jackson
    protected DefaultEventView() {}

    public DefaultEventView(final Event event, final EventViewContext context) {
      super(event, context);
    }

    @SuppressWarnings("SwaggerSchemaUsageChecker")
    @Override
    @JsonProperty
    @Schema(
        type = "string",
        description = "Unique identifier of event type.",
        enumAsRef = true,
        oneOf = {
          AdminAuditTypeView.class,
          ADFAAdminAuditTypeView.class,
          AgentEventTypeView.class,
          AppSettingsChangeAuditTypeView.class,
          AuthnServiceAuditTypeView.class,
          AutoIndexingEventTypeView.class,
          AWSPeerVpcAuditTypeView.class,
          AzurePeerNetworkAuditTypeView.class,
          BillingAuditorEventTypeView.class,
          BulkAllocationAuditEventTypeView.class,
          ClusterConnectionAuditTypeView.class,
          ConfigNamespaceAuditEventTypeView.class,
          ConfigApplicationPropertyAuditEventTypeView.class,
          ConfigFeatureFlagAuditEventTypeView.class,
          CpsBackupEventTypeView.class,
          CpsBillingEventTypeView.class,
          CrashLogEventTypeView.class,
          CronAuditTypeView.class,
          DataProtectionAuditTypeView.class,
          DeviceCodeAuditTypeView.class,
          DiskBackupEventTypeView.class,
          EncryptionKeyEventTypeView.class,
          ExportBucketAuditTypeView.class,
          FederationSettingsEventTypeView.class,
          FlappingEventTypeView.class,
          FTSIndexAuditTypeView.class,
          GCPPeerVpcAuditTypeView.class,
          GlobalAccessListEventTypeView.class,
          GlobalServiceAccountEventTypeView.class,
          GroupAuditTypeView.class,
          GroupEventTypeView.class,
          GroupIntegrationEventTypeView.class,
          IndexBuildAuditTypeView.class,
          LogCollectionRequestAuditTypeView.class,
          LongRunningMoveEventTypeView.class,
          LogCollectionRequestAuditTypeView.class,
          MlabMigrationAuditTypeView.class,
          MongotEventTypeView.class,
          NDSDbCheckAuditTypeView.class,
          NDSDataValidationAuditTypeView.class,
          NDSMaintenanceWindowAuditTypeView.class,
          NDSServerlessAutoScalingAuditTypeView.class,
          NDSServerlessTenantMigrationAuditTypeView.class,
          NDSTenantEndpointServiceDeploymentAuditTypeView.class,
          NDSX509UserAuthenticationEventTypeView.class,
          OnlineArchiveEventTypeView.class,
          PartnerEventTypeView.class,
          PrivateLinkAuditTypeView.class,
          ProactiveOperationEventTypeView.class,
          QuotaUsageEventTypeView.class,
          ResourceEventTypeView.class,
          SearchDeploymentAuditTypeView.class,
          ServerlessDeploymentAuditTypeView.class,
          ServerlessEventTypeView.class,
          FlexMetricEventTypeView.class,
          ServerlessMTMClusterGrpcIncrementalRolloutEventTypeView.class,
          SetupServerlessAuditTypeView.class,
          SupportCaseEventTypeView.class,
          SupportEventTypeView.class,
          SystemAlertProcessingEventTypeView.class,
          SystemAWSEventTypeView.class,
          SystemAzureEventTypeView.class,
          SystemBackupDaemonEventTypeView.class,
          SystemBackupEventTypeView.class,
          SystemBillingEventTypeView.class,
          SystemBlockstoreEventTypeView.class,
          SystemConfigChangeAuditTypeView.class,
          SystemCronJobEventTypeView.class,
          SystemCronJobStatusEventTypeView.class,
          SystemDatabaseProcessEventTypeView.class,
          SystemGCPEventTypeView.class,
          SystemLogEventTypeView.class,
          SystemNDSEventTypeView.class,
          SystemMaasEventTypeView.class,
          TenantBackupEventTypeView.class,
          UserGroupEventTypeView.class,
          VersionAuditTypeView.class,
          DbCheckEventTypeView.class,
          NDSCheckMetadataConsistencyEventTypeView.class,
          RateLimitSuspensionAuditEventTypeView.class,
          CaptainLogAuditTypeView.class,
          BumperFileRemovalEventTypeView.class,
          LogIngestionEventTypeView.class,
          MPAEventTypeView.class,
          ChartsAuditTypeView.class,
          LogUploaderDownEventTypeView.class,
          SystemAlertGlobalServiceAccountEventTypeView.class,
          AdminEventTypeView.class
        },
        requiredMode = RequiredMode.REQUIRED)
    public String getEventTypeName() {
      return super.getEventTypeName();
    }
  }
}
