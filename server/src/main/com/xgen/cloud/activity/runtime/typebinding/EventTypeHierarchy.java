package com.xgen.cloud.activity.runtime.typebinding;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.xgen.cloud.abtesting._public.model.BulkAllocationAuditEvent;
import com.xgen.cloud.access.accesslist._public.model.event.GlobalAccessListAudit;
import com.xgen.cloud.access.accesslist._public.model.event.GlobalAccessListEvent;
import com.xgen.cloud.access.activity._public.audit.AccessAuditEvent;
import com.xgen.cloud.access.activity._public.event.AccessEvent;
import com.xgen.cloud.account._public.model.activity.DeviceCodeAudit;
import com.xgen.cloud.activity._public.model.event.BiConnectorEvent;
import com.xgen.cloud.activity._public.model.event.CaptainLogAudit;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.HostAudit;
import com.xgen.cloud.activity._public.model.event.HostEvent;
import com.xgen.cloud.activity._public.model.event.HostMetricEvent;
import com.xgen.cloud.activity._public.model.event.HostSecurityCheckupEvent;
import com.xgen.cloud.activity._public.model.event.ReplicaSetEvent;
import com.xgen.cloud.activity._public.model.event.SystemAlertGlobalServiceAccountEvent;
import com.xgen.cloud.admin._public.activity.AppSettingsChangeAudit;
import com.xgen.cloud.admin._public.activity.SystemConfigChangeAudit;
import com.xgen.cloud.admin._public.activity.SystemLogEvent;
import com.xgen.cloud.alerts.alert._public.model.AlertAudit;
import com.xgen.cloud.alerts.alert._public.model.AlertConfigAudit;
import com.xgen.cloud.alerts.alert._public.model.activity.MaintenanceWindowConfigAudit;
import com.xgen.cloud.alerts.flapping.activity._public.model.FlappingEvent;
import com.xgen.cloud.apiserviceaccount._public.activity.audit.GlobalServiceAccountAudit;
import com.xgen.cloud.apiserviceaccount._public.activity.audit.ServiceAccountAudit;
import com.xgen.cloud.apiserviceaccount._public.activity.event.model.GlobalServiceAccountEvent;
import com.xgen.cloud.apiserviceaccount._public.activity.event.model.ServiceAccountEvent;
import com.xgen.cloud.atm.activity._public.audit.AutomationConfigAudit;
import com.xgen.cloud.atm.activity._public.audit.LogCollectionDownloadAudit;
import com.xgen.cloud.atm.activity._public.audit.LogCollectionRequestAudit;
import com.xgen.cloud.atm.activity._public.event.AutomationConfigEvent;
import com.xgen.cloud.atm.activity._public.event.CrashLogEvent;
import com.xgen.cloud.authn._public.model.AuthnServiceAudit;
import com.xgen.cloud.authz.core._public.model.event.UserGroupAudit;
import com.xgen.cloud.authz.core._public.model.event.UserGroupEvent;
import com.xgen.cloud.authz.resource._public.model.event.ResourceAudit;
import com.xgen.cloud.authz.resource._public.model.event.ResourceEvent;
import com.xgen.cloud.billingplatform.activity._public.audit.BillingAudit;
import com.xgen.cloud.billingplatform.activity._public.event.BillingAuditorEvent;
import com.xgen.cloud.billingplatform.activity._public.event.BillingEvent;
import com.xgen.cloud.billingplatform.activity._public.event.SystemBillingEvent;
import com.xgen.cloud.brs.activity._public.model.BackupAudit;
import com.xgen.cloud.brs.activity._public.model.BackupBusyEvent;
import com.xgen.cloud.brs.activity._public.model.BackupClustershotEvent;
import com.xgen.cloud.brs.activity._public.model.BackupDeploymentConfigMissingEvent;
import com.xgen.cloud.brs.activity._public.model.BackupEvent;
import com.xgen.cloud.brs.activity._public.model.BackupSnapshotEvent;
import com.xgen.cloud.brs.activity._public.model.BackupUnsupportedEvent;
import com.xgen.cloud.brs.activity._public.model.BlockstoreJobEvent;
import com.xgen.cloud.brs.activity._public.model.RestoreStatusEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupDBLowFreeSpaceEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupDaemonEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupResizeEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBackupTheftEvent;
import com.xgen.cloud.brs.activity._public.model.SystemBlockstoreEvent;
import com.xgen.cloud.brs.activity._public.model.SystemDatabaseProcessEvent;
import com.xgen.cloud.clusters._public.model.ClusterEvent;
import com.xgen.cloud.common.model._public.typebindings.TypeHierarchy;
import com.xgen.cloud.config._public.model.ConfigApplicationPropertyAuditEvent;
import com.xgen.cloud.config._public.model.ConfigFeatureFlagAuditEvent;
import com.xgen.cloud.config._public.model.ConfigNamespaceAuditEvent;
import com.xgen.cloud.cron._public.model.CronAudit;
import com.xgen.cloud.cron._public.model.SystemCronJobEvent;
import com.xgen.cloud.cron._public.model.SystemCronJobStatusEvent;
import com.xgen.cloud.email._public.activity.audit.SupportAudit;
import com.xgen.cloud.email._public.activity.event.SupportEvent;
import com.xgen.cloud.explorer.activity._public.audit.DataExplorerAccessedAudit;
import com.xgen.cloud.explorer.activity._public.audit.DataExplorerAudit;
import com.xgen.cloud.explorer.activity._public.audit.IndexBuildAudit;
import com.xgen.cloud.explorer.activity._public.event.DataExplorerAccessedEvent;
import com.xgen.cloud.explorer.activity._public.event.DataExplorerEvent;
import com.xgen.cloud.explorer.activity._public.event.IndexBuildEvent;
import com.xgen.cloud.externalanalytics._public.model.SupportCaseAudit;
import com.xgen.cloud.externalanalytics._public.model.SupportCaseEvent;
import com.xgen.cloud.fts.activity._public.audit.FTSIndexAudit;
import com.xgen.cloud.group._public.model.activity.GroupAudit;
import com.xgen.cloud.group._public.model.activity.GroupEvent;
import com.xgen.cloud.group._public.model.activity.GroupIntegrationAudit;
import com.xgen.cloud.group._public.model.activity.GroupIntegrationEvent;
import com.xgen.cloud.monitoring.agent._public.model.event.AgentEvent;
import com.xgen.cloud.monitoring.metrics._public.model.activity.MongotEvent;
import com.xgen.cloud.monitoring.metrics._public.model.activity.SystemMaasEvent;
import com.xgen.cloud.monitoring.ratelimit._public.audit.RateLimitSuspensionAudit;
import com.xgen.cloud.nds.activity._public.event.AdminEvent;
import com.xgen.cloud.nds.activity._public.event.AutoIndexingEvent;
import com.xgen.cloud.nds.activity._public.event.BumperFileRemovalEvent;
import com.xgen.cloud.nds.activity._public.event.CpsBackupEvent;
import com.xgen.cloud.nds.activity._public.event.CpsBackupOplogDiscontiguousEvent;
import com.xgen.cloud.nds.activity._public.event.CpsBackupRestoreEvent;
import com.xgen.cloud.nds.activity._public.event.CpsBillingEvent;
import com.xgen.cloud.nds.activity._public.event.DbCheckEvent;
import com.xgen.cloud.nds.activity._public.event.DiskBackupEvent;
import com.xgen.cloud.nds.activity._public.event.EncryptionKeyEvent;
import com.xgen.cloud.nds.activity._public.event.FlexMigrationEvent;
import com.xgen.cloud.nds.activity._public.event.LogIngestionEvent;
import com.xgen.cloud.nds.activity._public.event.LogUploaderDownEvent;
import com.xgen.cloud.nds.activity._public.event.MPAEvent;
import com.xgen.cloud.nds.activity._public.event.NDSX509UserAuthenticationEvent;
import com.xgen.cloud.nds.activity._public.event.OnlineArchiveEvent;
import com.xgen.cloud.nds.activity._public.event.ProactiveOperationEvent;
import com.xgen.cloud.nds.activity._public.event.ServerlessMTMClusterGrpcIncrementalRolloutEvent;
import com.xgen.cloud.nds.activity._public.event.SystemAWSEvent;
import com.xgen.cloud.nds.activity._public.event.SystemAzureEvent;
import com.xgen.cloud.nds.activity._public.event.SystemGCPEvent;
import com.xgen.cloud.nds.activity._public.event.SystemNDSEvent;
import com.xgen.cloud.nds.activity._public.event.TenantBackupEvent;
import com.xgen.cloud.nds.activity._public.event.audit.ADFAAdminAudit;
import com.xgen.cloud.nds.activity._public.event.audit.AWSPeerVpcAudit;
import com.xgen.cloud.nds.activity._public.event.audit.AdminAudit;
import com.xgen.cloud.nds.activity._public.event.audit.AtlasResourcePolicyAudit;
import com.xgen.cloud.nds.activity._public.event.audit.AutoIndexingAudit;
import com.xgen.cloud.nds.activity._public.event.audit.AzurePeerNetworkAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ChartsAudit;
import com.xgen.cloud.nds.activity._public.event.audit.DataProtectionAudit;
import com.xgen.cloud.nds.activity._public.event.audit.DiskBackupAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ExportBucketAudit;
import com.xgen.cloud.nds.activity._public.event.audit.GCPPeerVpcAudit;
import com.xgen.cloud.nds.activity._public.event.audit.MlabMigrationAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAutoScalingAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSCheckMetadataConsistencyEvent;
import com.xgen.cloud.nds.activity._public.event.audit.NDSDataValidationAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSDataValidationEvent;
import com.xgen.cloud.nds.activity._public.event.audit.NDSDbCheckAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSDbCheckEvent;
import com.xgen.cloud.nds.activity._public.event.audit.NDSMaintenanceWindowAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSServerlessAutoScalingAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSServerlessInstanceAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSServerlessTenantMigrationAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSTenantEndpointAudit;
import com.xgen.cloud.nds.activity._public.event.audit.NDSTenantEndpointServiceDeploymentAudit;
import com.xgen.cloud.nds.activity._public.event.audit.PrivateLinkAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ServerAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ServerlessAutoIndexingAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ServerlessDeploymentAudit;
import com.xgen.cloud.nds.activity._public.event.audit.ServerlessMTMClusterGrpcIncrementalRolloutAudit;
import com.xgen.cloud.nds.activity._public.event.audit.SetupServerlessAudit;
import com.xgen.cloud.nds.activity._public.event.audit.TenantBackupAudit;
import com.xgen.cloud.nds.activity._public.event.audit.VersionAudit;
import com.xgen.cloud.nds.common._public.model.LongRunningMoveEvent;
import com.xgen.cloud.nds.project._public.model.cloudprovidermonitoring.QuotaUsageEvent;
import com.xgen.cloud.nds.project._public.model.event.ClusterConnectionAudit;
import com.xgen.cloud.nds.serverless._public.model.activity.FlexMetricEvent;
import com.xgen.cloud.nds.serverless._public.model.activity.ServerlessEvent;
import com.xgen.cloud.nds.serverless._public.model.activity.ServerlessMetricEvent;
import com.xgen.cloud.organization._public.model.activity.OrgAudit;
import com.xgen.cloud.organization._public.model.activity.OrgEvent;
import com.xgen.cloud.partners.common._public.activity.PartnerAudit;
import com.xgen.cloud.partners.common._public.activity.PartnerEvent;
import com.xgen.cloud.realm.activity._public.event.DeploymentEvent;
import com.xgen.cloud.realm.activity._public.event.LimitEvent;
import com.xgen.cloud.realm.activity._public.event.LogForwarderEvent;
import com.xgen.cloud.realm.activity._public.event.RealmMetricEvent;
import com.xgen.cloud.realm.activity._public.event.SyncEvent;
import com.xgen.cloud.realm.activity._public.event.TriggerEvent;
import com.xgen.cloud.search.decoupled.activity._public.audit.SearchDeploymentAudit;
import com.xgen.cloud.streams._public.model.event.StreamProcessorEvent;
import com.xgen.cloud.streams._public.model.event.StreamsEvent;
import com.xgen.cloud.system._public.model.SystemAlertProcessingEvent;
import com.xgen.cloud.team._public.activity.audit.TeamAudit;
import com.xgen.cloud.team._public.activity.event.TeamEvent;
import com.xgen.cloud.user._public.model.activity.ApiUserAudit;
import com.xgen.cloud.user._public.model.activity.ApiUserEvent;
import com.xgen.cloud.user._public.model.activity.UserAudit;
import com.xgen.cloud.user._public.model.activity.UserEvent;
import com.xgen.module.federation.activity._public.audit.FederationSettingsAudit;
import com.xgen.module.federation.activity._public.event.FederationSettingsEvent;

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = Event.TYPE_FIELD)
@JsonSubTypes({
  @JsonSubTypes.Type(value = ADFAAdminAudit.class, name = ADFAAdminAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AgentEvent.class, name = AgentEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = BillingEvent.class, name = BillingEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = ClusterEvent.class, name = ClusterEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = HostEvent.class, name = HostEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = HostMetricEvent.class, name = HostMetricEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = HostSecurityCheckupEvent.class,
      name = HostSecurityCheckupEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = ReplicaSetEvent.class, name = ReplicaSetEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = UserEvent.class, name = UserEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = BackupEvent.class, name = BackupEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = BackupUnsupportedEvent.class, name = BackupUnsupportedEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = CpsBackupEvent.class, name = CpsBackupEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = CpsBackupRestoreEvent.class, name = CpsBackupRestoreEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = CpsBillingEvent.class, name = CpsBillingEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = ExportBucketAudit.class, name = ExportBucketAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = CpsBackupOplogDiscontiguousEvent.class,
      name = CpsBackupOplogDiscontiguousEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = DiskBackupEvent.class, name = DiskBackupEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = DataProtectionAudit.class, name = DataProtectionAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = TenantBackupEvent.class, name = TenantBackupEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = BackupBusyEvent.class, name = BackupBusyEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = BackupClustershotEvent.class, name = BackupClustershotEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = RestoreStatusEvent.class, name = RestoreStatusEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = GroupEvent.class, name = GroupEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = OrgEvent.class, name = OrgEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = AutomationConfigEvent.class, name = AutomationConfigEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = BackupAudit.class, name = BackupAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = BackupSnapshotEvent.class, name = BackupSnapshotEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = BillingAuditorEvent.class, name = BillingAuditorEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = DiskBackupAudit.class, name = DiskBackupAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = TenantBackupAudit.class, name = TenantBackupAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = BillingAudit.class, name = BillingAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = HostAudit.class, name = HostAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AccessAuditEvent.class, name = AccessAuditEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = DataExplorerAccessedAudit.class,
      name = DataExplorerAccessedAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = DataExplorerAudit.class, name = DataExplorerAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = ServerAudit.class, name = ServerAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = UserAudit.class, name = UserAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = GroupAudit.class, name = GroupAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = OrgAudit.class, name = OrgAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AlertConfigAudit.class, name = AlertConfigAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = MaintenanceWindowConfigAudit.class,
      name = MaintenanceWindowConfigAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AlertAudit.class, name = AlertAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AutomationConfigAudit.class, name = AutomationConfigAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = TeamAudit.class, name = TeamAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = TeamEvent.class, name = TeamEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = UserGroupAudit.class, name = UserGroupAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = UserGroupEvent.class, name = UserGroupEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SystemBackupEvent.class, name = SystemBackupEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = SystemBackupDBLowFreeSpaceEvent.class,
      name = SystemBackupDBLowFreeSpaceEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = SystemBackupDaemonEvent.class,
      name = SystemBackupDaemonEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SystemBackupTheftEvent.class, name = SystemBackupTheftEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = SystemBackupResizeEvent.class,
      name = SystemBackupResizeEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SystemCronJobEvent.class, name = SystemCronJobEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = SystemDatabaseProcessEvent.class,
      name = SystemDatabaseProcessEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SystemBillingEvent.class, name = SystemBillingEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SystemBlockstoreEvent.class, name = SystemBlockstoreEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = NDSAudit.class, name = NDSAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = NDSAutoScalingAudit.class, name = NDSAutoScalingAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = NDSServerlessInstanceAudit.class,
      name = NDSServerlessInstanceAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = FTSIndexAudit.class, name = FTSIndexAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = SearchDeploymentAudit.class, name = SearchDeploymentAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = SystemConfigChangeAudit.class,
      name = SystemConfigChangeAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = StreamsEvent.class, name = StreamsEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = StreamProcessorEvent.class, name = StreamProcessorEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SystemLogEvent.class, name = SystemLogEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = AWSPeerVpcAudit.class, name = AWSPeerVpcAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = GCPPeerVpcAudit.class, name = GCPPeerVpcAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AzurePeerNetworkAudit.class, name = AzurePeerNetworkAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = SupportCaseAudit.class, name = SupportCaseAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = SupportCaseEvent.class, name = SupportCaseEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = BlockstoreJobEvent.class, name = BlockstoreJobEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = AccessEvent.class, name = AccessEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = DataExplorerAccessedEvent.class,
      name = DataExplorerAccessedEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = DataExplorerEvent.class, name = DataExplorerEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SystemNDSEvent.class, name = SystemNDSEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = SystemAlertProcessingEvent.class,
      name = SystemAlertProcessingEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = SystemCronJobStatusEvent.class,
      name = SystemCronJobStatusEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SystemAWSEvent.class, name = SystemAWSEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SystemGCPEvent.class, name = SystemGCPEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SystemAzureEvent.class, name = SystemAzureEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = EncryptionKeyEvent.class, name = EncryptionKeyEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = BiConnectorEvent.class, name = BiConnectorEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = LogCollectionRequestAudit.class,
      name = LogCollectionRequestAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = LogCollectionDownloadAudit.class,
      name = LogCollectionDownloadAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AppSettingsChangeAudit.class, name = AppSettingsChangeAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = ApiUserEvent.class, name = ApiUserEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = ApiUserAudit.class, name = ApiUserAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = ServiceAccountEvent.class, name = ServiceAccountEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = ServiceAccountAudit.class, name = ServiceAccountAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = GlobalServiceAccountEvent.class,
      name = GlobalServiceAccountEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = GlobalServiceAccountAudit.class,
      name = GlobalServiceAccountAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = SystemAlertGlobalServiceAccountEvent.class,
      name = SystemAlertGlobalServiceAccountEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = MlabMigrationAudit.class, name = MlabMigrationAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = GlobalAccessListEvent.class, name = GlobalAccessListEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = GlobalAccessListAudit.class, name = GlobalAccessListAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = NDSX509UserAuthenticationEvent.class,
      name = NDSX509UserAuthenticationEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = OnlineArchiveEvent.class, name = OnlineArchiveEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = GroupIntegrationEvent.class, name = GroupIntegrationEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = GroupIntegrationAudit.class, name = GroupIntegrationAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = PrivateLinkAudit.class, name = PrivateLinkAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = NDSMaintenanceWindowAudit.class,
      name = NDSMaintenanceWindowAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = NDSDbCheckAudit.class, name = NDSDbCheckAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = NDSDataValidationAudit.class, name = NDSDataValidationAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = NDSDbCheckEvent.class, name = NDSDbCheckEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = NDSDataValidationEvent.class, name = NDSDataValidationEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = FederationSettingsEvent.class,
      name = FederationSettingsEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = FederationSettingsAudit.class,
      name = FederationSettingsAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AutoIndexingAudit.class, name = AutoIndexingAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = ServerlessAutoIndexingAudit.class,
      name = ServerlessAutoIndexingAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AutoIndexingEvent.class, name = AutoIndexingEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = IndexBuildAudit.class, name = IndexBuildAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = IndexBuildEvent.class, name = IndexBuildEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = MongotEvent.class, name = MongotEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = CrashLogEvent.class, name = CrashLogEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = CronAudit.class, name = CronAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = VersionAudit.class, name = VersionAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = LongRunningMoveEvent.class, name = LongRunningMoveEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = NDSServerlessTenantMigrationAudit.class,
      name = NDSServerlessTenantMigrationAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = NDSServerlessAutoScalingAudit.class,
      name = NDSServerlessAutoScalingAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AdminAudit.class, name = AdminAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = SyncEvent.class, name = SyncEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = DeploymentEvent.class, name = DeploymentEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = TriggerEvent.class, name = TriggerEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = LogForwarderEvent.class, name = LogForwarderEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = LimitEvent.class, name = LimitEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = RealmMetricEvent.class, name = RealmMetricEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = ServerlessDeploymentAudit.class,
      name = ServerlessDeploymentAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = SetupServerlessAudit.class, name = SetupServerlessAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = NDSTenantEndpointServiceDeploymentAudit.class,
      name = NDSTenantEndpointServiceDeploymentAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = NDSTenantEndpointAudit.class, name = NDSTenantEndpointAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = ServerlessEvent.class, name = ServerlessEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = ServerlessMetricEvent.class, name = ServerlessMetricEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = FlexMetricEvent.class, name = FlexMetricEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SupportEvent.class, name = SupportEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SupportAudit.class, name = SupportAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = ProactiveOperationEvent.class,
      name = ProactiveOperationEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = PartnerEvent.class, name = PartnerEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = PartnerAudit.class, name = PartnerAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = DeviceCodeAudit.class, name = DeviceCodeAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = QuotaUsageEvent.class, name = QuotaUsageEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = ClusterConnectionAudit.class, name = ClusterConnectionAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = FlappingEvent.class, name = FlappingEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = ResourceEvent.class, name = ResourceEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = ResourceAudit.class, name = ResourceAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = ServerlessMTMClusterGrpcIncrementalRolloutEvent.class,
      name = ServerlessMTMClusterGrpcIncrementalRolloutEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = ServerlessMTMClusterGrpcIncrementalRolloutAudit.class,
      name = ServerlessMTMClusterGrpcIncrementalRolloutAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = RateLimitSuspensionAudit.class,
      name = RateLimitSuspensionAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AuthnServiceAudit.class, name = AuthnServiceAudit.TYPE_NAME),
  @JsonSubTypes.Type(
      value = ConfigNamespaceAuditEvent.class,
      name = ConfigNamespaceAuditEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = ConfigApplicationPropertyAuditEvent.class,
      name = ConfigApplicationPropertyAuditEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = ConfigFeatureFlagAuditEvent.class,
      name = ConfigFeatureFlagAuditEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = CaptainLogAudit.class, name = CaptainLogAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = DbCheckEvent.class, name = DbCheckEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = BackupDeploymentConfigMissingEvent.class,
      name = BackupDeploymentConfigMissingEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = BumperFileRemovalEvent.class, name = BumperFileRemovalEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = LogUploaderDownEvent.class, name = LogUploaderDownEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = AtlasResourcePolicyAudit.class,
      name = AtlasResourcePolicyAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = LogIngestionEvent.class, name = LogIngestionEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = MPAEvent.class, name = MPAEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = FlexMigrationEvent.class, name = FlexMigrationEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = BulkAllocationAuditEvent.class,
      name = BulkAllocationAuditEvent.TYPE_NAME),
  @JsonSubTypes.Type(
      value = NDSCheckMetadataConsistencyEvent.class,
      name = NDSCheckMetadataConsistencyEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = ChartsAudit.class, name = ChartsAudit.TYPE_NAME),
  @JsonSubTypes.Type(value = AdminEvent.class, name = AdminEvent.TYPE_NAME),
  @JsonSubTypes.Type(value = SystemMaasEvent.class, name = SystemMaasEvent.TYPE_NAME)
})
public class EventTypeHierarchy implements TypeHierarchy {
  @Override
  public Class<?> getSuperType() {
    return Event.class;
  }
}
