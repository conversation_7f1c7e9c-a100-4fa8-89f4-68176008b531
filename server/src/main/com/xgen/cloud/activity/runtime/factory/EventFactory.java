package com.xgen.cloud.activity.runtime.factory;

import static com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;
import static com.xgen.cloud.activity._public.model.event.Event.CREATED_AT_FIELD;
import static com.xgen.cloud.activity._public.model.event.Event.EVENT_TYPE_FIELD;
import static com.xgen.cloud.activity._public.model.event.Event.GROUP_ID_FIELD;
import static com.xgen.cloud.activity._public.model.event.Event.GROUP_NAME_FIELD;
import static com.xgen.cloud.activity._public.model.event.Event.HIDDEN_FIELD;
import static com.xgen.cloud.activity._public.model.event.Event.ID_FIELD;
import static com.xgen.cloud.activity._public.model.event.Event.ORG_ID_FIELD;
import static com.xgen.cloud.activity._public.model.event.Event.ORG_NAME_FIELD;
import static java.lang.String.format;
import static java.util.Collections.emptyMap;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toMap;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.BiMap;
import com.google.common.collect.ImmutableBiMap;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.EventTypeInfo;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.svc.mms.model.event.EventTypeMapping;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EventFactory {

  public static final String TYPE_FIELD = "type";

  private static final String REQUIRED_FIELD_MSG = "either %s, %s is required field";
  private static final String UNSUPPORTED_EVENT_TYPE_MSG = "%s is not supported event type";
  private static final String NO_ENUM_CONSTANT_MSG = "No enum constant";
  private static final String UNABLE_CREATE_EVENT_MSG = "Unable to create event with given params";

  private static final BiMap<String, String> FIELD_ALIASES =
      ImmutableBiMap.of(TYPE_FIELD, EVENT_TYPE_FIELD);
  private static final Logger LOG = LoggerFactory.getLogger(EventFactory.class);

  private final ObjectMapper objectMapper;

  public EventFactory(ObjectMapper objectMapper) {
    this.objectMapper = objectMapper.copy().configure(FAIL_ON_UNKNOWN_PROPERTIES, false);
  }

  @SuppressWarnings({"unchecked"})
  public <T extends Event> T createEvent(
      final Map<String, Object> eventParams, final Organization organization, final Group group) {

    Map<String, Object> allEventParams = new HashMap<>(eventParams);
    allEventParams.computeIfAbsent(ID_FIELD, k -> ObjectId.get());
    allEventParams.putAll(
        FIELD_ALIASES.keySet().stream()
            .filter(eventParams::containsKey)
            .filter(k -> !eventParams.containsKey(FIELD_ALIASES.get(k)))
            .map(k -> Pair.of(FIELD_ALIASES.get(k), eventParams.get(k)))
            .collect(toMap(Pair::getKey, Pair::getValue)));
    allEventParams.putAll(
        ofNullable(group)
            .map(g -> Map.of(GROUP_ID_FIELD, group.getId(), GROUP_NAME_FIELD, group.getName()))
            .orElse(emptyMap()));
    allEventParams.putAll(
        ofNullable(organization)
            .map(o -> Map.of(ORG_ID_FIELD, o.getId(), ORG_NAME_FIELD, o.getName()))
            .orElse(emptyMap()));
    allEventParams.computeIfAbsent(Event.TYPE_FIELD, k -> deriveTypeName(allEventParams));
    allEventParams.putIfAbsent(HIDDEN_FIELD, false);
    allEventParams.computeIfAbsent(CREATED_AT_FIELD, k -> new Date());

    return (T) convertValue(allEventParams).validate();
  }

  private Event convertValue(Map<String, Object> allEventParams) {
    try {
      return objectMapper.convertValue(allEventParams, Event.class);
    } catch (Exception e) {
      LOG.error("Unexpected error", e);
      throw new IllegalArgumentException(UNABLE_CREATE_EVENT_MSG);
    }
  }

  private String deriveTypeName(Map<String, Object> allEventParams) {
    try {
      return ofNullable(allEventParams.get(EVENT_TYPE_FIELD))
          .map(etf -> EventTypeMapping.valueOf((String) etf))
          .map(
              etm ->
                  ofNullable(etm.getEventType().getInfo())
                      .map(EventTypeInfo::getTypeName)
                      .orElseThrow(
                          () ->
                              new IllegalArgumentException(
                                  format(UNSUPPORTED_EVENT_TYPE_MSG, etm.name()))))
          .orElseThrow(
              () ->
                  new NullPointerException(
                      format(
                          REQUIRED_FIELD_MSG,
                          EVENT_TYPE_FIELD,
                          FIELD_ALIASES.inverse().get(EVENT_TYPE_FIELD))));
    } catch (IllegalArgumentException e) {
      LOG.error("Unexpected error", e);
      throw e.getMessage().startsWith(NO_ENUM_CONSTANT_MSG)
          ? new IllegalArgumentException(
              format(UNSUPPORTED_EVENT_TYPE_MSG, allEventParams.get(EVENT_TYPE_FIELD)))
          : e;
    }
  }
}
