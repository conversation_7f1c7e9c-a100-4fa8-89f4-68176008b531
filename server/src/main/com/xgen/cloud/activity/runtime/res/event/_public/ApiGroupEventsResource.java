package com.xgen.cloud.activity.runtime.res.event._public;

import static com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc.isFeatureFlagEnabled;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;
import static org.apache.http.HttpStatus.SC_OK;

import com.mongodb.MongoSocketReadTimeoutException;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.access.role._public.model.RoleSet.NAME;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.EventScope;
import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.activity._public.model.event.view.EventViewContext;
import com.xgen.cloud.activity.runtime.view.EventViewHierarchyForNdsGroup.EventTypeForNdsGroup;
import com.xgen.cloud.activity.runtime.view.EventViewHierarchyForNdsGroup.EventViewForNdsGroup;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.db.legacy._public.cursor.ModelCursor;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.res._public.pagination.PaginationHandler;
import com.xgen.cloud.common.util._public.time.TimeUtils2;
import com.xgen.cloud.common.view._public.base.ApiListView;
import com.xgen.cloud.common.view._public.base.View;
import com.xgen.cloud.common.view._public.transform.ViewTransformer;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.api.res.common.ApiBaseResourceHelper;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.mms.model.event.EventTypeMapping;
import com.xgen.svc.mms.model.grouptype.GroupType;
import com.xgen.svc.mms.res.filter.annotation.RateLimiter;
import com.xgen.svc.mms.svc.alert.ActivityFeedSvc;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.prometheus.client.Histogram;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.Explode;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.enums.ParameterStyle;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.Nullable;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

@Singleton
@Path("/api/{app:public|atlas}/v1.0/groups/{groupId}/events")
public class ApiGroupEventsResource extends ApiBaseResource {

  private final ActivityFeedSvc _activityFeedSvc;
  private final com.xgen.cloud.activityfeed._public.svc.ActivityFeedSvc
      _eventServiceActivityFeedSvc;
  private final AuthzSvc _authzSvc;
  private final AppSettings _appSettings;
  private final ViewTransformer _viewTransformer;

  private static final String EVENT_SOURCE = "event_source";
  private static final String API_VERSION = "api_version";
  private static final String MIN_DATE_MONTHS_FROM_NOW = "min_date_months";
  private static final String MAX_DATE_MONTHS_FROM_NOW = "max_date_months";
  public static final Histogram EVENT_SERVICE_GET_GROUP_EVENT_DURATION_SECONDS =
      PromMetricsSvc.registerHistogram(
          "mms_api_group_events_get_event",
          "Time in seconds get one event from mms",
          PromMetricsSvc.getHistogramExpBucket(.001, 2, 14),
          EVENT_SOURCE,
          API_VERSION);

  public static final Histogram EVENT_SERVICE_LIST_GROUP_EVENTS_DURATION_SECONDS =
      PromMetricsSvc.registerHistogram(
          "mms_api_group_events_list_events",
          "Time in seconds get a list of events event from mms",
          PromMetricsSvc.getHistogramExpBucket(.001, 2, 14),
          EVENT_SOURCE,
          API_VERSION,
          MIN_DATE_MONTHS_FROM_NOW,
          MAX_DATE_MONTHS_FROM_NOW);

  @Inject
  public ApiGroupEventsResource(
      final AppSettings settings,
      final ActivityFeedSvc activityFeedSvc,
      final com.xgen.cloud.activityfeed._public.svc.ActivityFeedSvc eventServiceActivityFeedSvc,
      final AuthzSvc authzSvc,
      final ViewTransformer viewTransformer) {
    super(settings);
    _activityFeedSvc = activityFeedSvc;
    _eventServiceActivityFeedSvc = eventServiceActivityFeedSvc;
    _authzSvc = authzSvc;
    _appSettings = settings;
    _viewTransformer = viewTransformer;
  }

  @GET
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_READ_ONLY, NAME.GLOBAL_CRASH_LOG_ANALYST})
  @Operation(
      summary = "Return Events from One Project",
      operationId = "listProjectEvents",
      description =
          """
Returns events for the specified project. Events identify significant database, billing, or security activities or status changes. To use this resource, the requesting Service Account or API Key must have the Project Read Only role.

This resource remains under revision and may change.""",
      tags = {"Events"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "includeCount"),
        @Parameter(ref = "itemsPerPage"),
        @Parameter(ref = "pageNum"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "clusterNames",
            description = "Human-readable label that identifies the cluster.",
            in = ParameterIn.QUERY,
            array =
                @ArraySchema(
                    schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
                    extensions = {
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name = "xgen-IPA-124-array-max-items",
                                value = "Schema predates IPA validation."),
                          })
                    }),
            explode = Explode.TRUE,
            style = ParameterStyle.FORM),
        @Parameter(
            name = "eventType",
            description =
                """
                Category of incident recorded at this moment in time.

                **IMPORTANT**: The complete list of event type values changes frequently.""",
            in = ParameterIn.QUERY,
            array =
                @ArraySchema(
                    schema = @Schema(ref = "EventTypeForNdsGroup"),
                    extensions = {
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name = "xgen-IPA-124-array-max-items",
                                value = "Schema predates IPA validation."),
                          })
                    })),
        @Parameter(
            name = "excludedEventType",
            description =
                """
Category of event that you would like to exclude from query results, such as CLUSTER_CREATED

**IMPORTANT**: Event type names change frequently. Verify that you specify the event type correctly by checking the complete list of event types.""",
            in = ParameterIn.QUERY,
            array =
                @ArraySchema(
                    schema = @Schema(ref = "EventTypeForNdsGroup"),
                    extensions = {
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name = "xgen-IPA-124-array-max-items",
                                value = "Schema predates IPA validation."),
                          })
                    })),
        @Parameter(
            name = "oneEventType",
            description =
                """
                Category of incident recorded at this moment in time.

                **IMPORTANT**: The complete list of event type values changes frequently.""",
            in = ParameterIn.DEFAULT,
            schema = @Schema(implementation = EventTypeForNdsGroup.class)),
        @Parameter(
            name = "includeRaw",
            description =
                "Flag that indicates whether to include the raw document in the output. The raw"
                    + " document contains additional meta information about the event.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "boolean", defaultValue = "false")),
        @Parameter(
            name = "maxDate",
            description =
                "Date and time from when MongoDB Cloud stops returning events. This parameter uses"
                    + " the ISO 8601"
                    + " timestamp format in UTC.",
            in = ParameterIn.QUERY,
            schema =
                @Schema(
                    type = "string",
                    format = "date-time",
                    externalDocs =
                        @ExternalDocumentation(
                            description = "ISO 8601",
                            url = "https://en.wikipedia.org/wiki/ISO_8601"))),
        @Parameter(
            name = "minDate",
            description =
                "Date and time from when MongoDB Cloud starts returning events. This parameter"
                    + " uses the ISO 8601"
                    + " timestamp format in UTC.",
            in = ParameterIn.QUERY,
            schema =
                @Schema(
                    type = "string",
                    format = "date-time",
                    externalDocs =
                        @ExternalDocumentation(
                            description = "ISO 8601",
                            url = "https://en.wikipedia.org/wiki/ISO_8601")))
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = GroupPaginatedEventView.class))),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      })
  @RateLimiter(RateLimiter.Feature.EVENTS)
  @SuppressWarnings("try")
  public Response getAllEvents(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AppUser pCurrentUser,
      @Parameter(hidden = true) @QueryParam("minDate") final String pMinDate,
      @Parameter(hidden = true) @QueryParam("maxDate") final String pMaxDate,
      @Parameter(hidden = true) @QueryParam("eventType") final List<EventTypeMapping> pEventTypes,
      @Parameter(hidden = true) @QueryParam("excludedEventType")
          final List<EventTypeMapping> pExcludedEventTypes,
      @Parameter(hidden = true) @QueryParam("oneEventType") final EventTypeMapping pEventType,
      @Parameter(hidden = true) @QueryParam("clusterNames") final List<String> pClusterNames,
      @Parameter(hidden = true) @QueryParam("includeRaw") final boolean pIncludeRaw,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope)
      throws SvcException {
    final Date minDate = getDateFromString(pMinDate);
    final Date maxDate = getDateFromString(pMaxDate);
    if (pMinDate != null && minDate == null) {
      throw ApiErrorCode.INVALID_DATE_FORMAT.exception(pEnvelope, pMinDate);
    }
    if (pMaxDate != null && maxDate == null) {
      throw ApiErrorCode.INVALID_DATE_FORMAT.exception(pEnvelope, pMaxDate);
    }

    final List<String> finalClusterNames =
        pClusterNames == null ? new ArrayList<>() : pClusterNames;

    final boolean isNDS = pGroup.getGroupType() == GroupType.NDS;
    if (!isNDS && !finalClusterNames.isEmpty()) {
      throw ApiErrorCode.FILTER_BY_CLUSTER_NOT_ALLOWED.exception(pEnvelope);
    }

    final List<EventType> eventTypes =
        pEventTypes.stream().map(EventTypeMapping::getEventType).collect(Collectors.toList());
    final List<EventType> excludedEventTypes =
        pExcludedEventTypes.stream()
            .map(EventTypeMapping::getEventType)
            .collect(Collectors.toList());

    int minDateMonthsInPast = getMonthsInPast(minDate);
    int maxDateMonthsInPast = getMonthsInPast(maxDate);

    if (isFeatureFlagEnabled(
        FeatureFlag.PROJECT_EVENTS_EVENT_SERVICE_API, _appSettings, null, pGroup)) {
      try (final Histogram.Timer timer =
          EVENT_SERVICE_LIST_GROUP_EVENTS_DURATION_SECONDS
              .labels(
                  "event-service",
                  "newest",
                  String.valueOf(minDateMonthsInPast),
                  String.valueOf(maxDateMonthsInPast))
              .startTimer()) {
        return doListFromEventService(
            pRequest,
            pGroup,
            pCurrentUser,
            minDate,
            maxDate,
            finalClusterNames,
            eventTypes,
            excludedEventTypes,
            pIncludeRaw,
            pEnvelope);
      }
    } else {
      try (final Histogram.Timer timer =
          EVENT_SERVICE_LIST_GROUP_EVENTS_DURATION_SECONDS
              .labels(
                  "mms",
                  "newest",
                  String.valueOf(minDateMonthsInPast),
                  String.valueOf(maxDateMonthsInPast))
              .startTimer()) {
        return doListFromMMS(
            pGroup,
            pCurrentUser,
            pRequest,
            eventTypes,
            excludedEventTypes,
            finalClusterNames,
            minDate,
            maxDate,
            pEnvelope,
            isNDS,
            pIncludeRaw);
      }
    }
  }

  private Response doListFromEventService(
      HttpServletRequest request,
      Group group,
      AppUser currentUser,
      Date minDate,
      Date maxDate,
      List<String> clusterNames,
      List<EventType> includedEventTypes,
      List<EventType> excludedEventTypes,
      boolean includeRaw,
      boolean envelope)
      throws SvcException {
    int pageNum = PaginationHandler.processPageNum(request);
    int itemsPerPage = getPaginationHandler().processItemsPerPage(request);
    // TODO CLOUDP-335544: Event service needs to support a skip option :(
    int skip = getPaginationHandler().getPaginationSkip(pageNum);

    try {
      List<Event> events =
          _eventServiceActivityFeedSvc.getEventsAsMmsEvents(
              group.getId(),
              minDate,
              maxDate,
              clusterNames,
              includedEventTypes,
              excludedEventTypes,
              getAuthzSvc().isGlobalReadOnly(currentUser),
              itemsPerPage);

      _viewTransformer.context(
          EventViewContext.builder()
              .settings(_appSettings)
              .isAtlas(isAtlas(request))
              .hasGlobalReadOnly(getAuthzSvc().isGlobalReadOnly(currentUser))
              .selfLinkOnly(true)
              .includeRaw(includeRaw)
              .scope(EventScope.GROUP)
              .build());

      List<View> views = new ArrayList<>();
      for (Event e : events) {
        views.add(_viewTransformer.transform(e));
      }

      return handlePagination(request, views, envelope, SC_OK);
    } catch (StatusRuntimeException grpcException) {
      boolean includeCount =
          processIncludeCount(ApiBaseResourceHelper.parseQueryParameters(request.getQueryString()));
      if (grpcException.getStatus().getCode().equals(Status.Code.DEADLINE_EXCEEDED)) {
        if (includeCount) {
          // Paging can fail due to count() taking too long
          throw ApiErrorCode.TIMEOUT_WHILE_PAGING.exception(envelope);
        } else {
          throw ApiErrorCode.TIMEOUT.exception(envelope);
        }
      }
      throw grpcException;
    }
  }

  private Response doListFromMMS(
      Group group,
      AppUser currentUser,
      HttpServletRequest request,
      List<EventType> eventTypes,
      List<EventType> excludedEventTypes,
      List<String> finalClusterNames,
      Date minDate,
      Date maxDate,
      Boolean envelope,
      boolean isNDS,
      boolean includeRaw) {
    // The startDate parameter in EventDao.findAllForGroup means lessThanOrEq
    // while endDate parameter in EventDao.findAllForGroup means greaterThanOrEq
    // So minDate maps to endDate and maxDate maps to startDate here
    final ModelCursor<Event> eventList =
        getActivityFeedSvc()
            .getGroupEventsModelCursor(
                group.getId(),
                0,
                0,
                eventTypes,
                excludedEventTypes,
                finalClusterNames,
                minDate,
                maxDate,
                getAuthzSvc().isGlobalReadOnly(currentUser),
                envelope,
                isNDS);
    try {
      _viewTransformer.context(
          EventViewContext.builder()
              .settings(_appSettings)
              .isAtlas(isAtlas(request))
              .hasGlobalReadOnly(getAuthzSvc().isGlobalReadOnly(currentUser))
              .selfLinkOnly(true)
              .includeRaw(includeRaw)
              .scope(EventScope.GROUP)
              .build());
      return handlePagination(request, eventList, _viewTransformer::transform, envelope);
    } catch (final MongoSocketReadTimeoutException e) {
      boolean includeCount =
          processIncludeCount(ApiBaseResourceHelper.parseQueryParameters(request.getQueryString()));
      if (includeCount) {
        // Paging can fail due to count() taking too long
        throw ApiErrorCode.TIMEOUT_WHILE_PAGING.exception(envelope);
      }
      throw ApiErrorCode.TIMEOUT.exception(envelope);
    }
  }

  /** See https://github.com/swagger-api/swagger-core/issues/3496 */
  public static class GroupPaginatedEventView extends ApiListView<EventViewForNdsGroup> {}

  @GET
  @Path("/{eventId}")
  @Produces({MediaType.APPLICATION_JSON})
  @RolesAllowed({RoleSet.NAME.GROUP_READ_ONLY})
  @Operation(
      summary = "Return One Event from One Project",
      operationId = "getProjectEvent",
      description =
          "Returns one event for the specified project. Events identify significant database,"
              + " billing, or security activities or status changes. To use this resource, the"
              + " requesting Service Account or API Key must have the Project Read Only role. Use"
              + " the Return Events from One Project endpoint to retrieve all events to which"
              + " the authenticated user has access.\n\n"
              + "This resource remains under revision and may change.",
      externalDocs =
          @ExternalDocumentation(
              description = "Return Events from One Project",
              url =
                  "https://www.mongodb.com/docs/atlas/reference/api-resources-spec/v2/#tag/Events/operation/listProjectEvents"),
      tags = {"Events"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "eventId",
            description =
                "Unique 24-hexadecimal digit string that identifies the event that you want to"
                    + " return.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.OBJECT_ID_REGEX),
            required = true),
        @Parameter(
            name = "includeRaw",
            description =
                "Flag that indicates whether to include the raw document in the output. The raw"
                    + " document contains additional meta information about the event.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "boolean", defaultValue = "false"))
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = MediaType.APPLICATION_JSON,
                    schema = @Schema(implementation = EventViewForNdsGroup.class))),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      })
  @SuppressWarnings("try")
  public Response getEvent(
      @Context final HttpServletRequest request,
      @Context final Group group,
      @Context final AppUser currentUser,
      @Parameter(hidden = true) @PathParam("eventId") final ObjectId eventId,
      @Parameter(hidden = true) @QueryParam("includeRaw") final boolean includeRaw,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope) {
    final Event event;

    if (isFeatureFlagEnabled(
        FeatureFlag.PROJECT_EVENTS_EVENT_SERVICE_API, _appSettings, null, group)) {
      try (final Histogram.Timer timer =
          EVENT_SERVICE_GET_GROUP_EVENT_DURATION_SECONDS
              .labels("event-service", "newest")
              .startTimer()) {
        event = _eventServiceActivityFeedSvc.getEventAsMmsEvent(eventId);
      }
    } else {
      try (final Histogram.Timer timer =
          EVENT_SERVICE_GET_GROUP_EVENT_DURATION_SECONDS.labels("mms", "newest").startTimer()) {
        event = getActivityFeedSvc().getEventById(eventId);
      }
    }

    if (event == null || !group.getId().equals(event.getGroupId())) {
      throw ApiErrorCode.EVENT_NOT_FOUND.exception(envelope, eventId, group.getId());
    }
    if (event.isHidden() && !getAuthzSvc().isGlobalReadOnly(currentUser)) {
      throw ApiErrorCode.USER_UNAUTHORIZED.exception(envelope);
    }
    _viewTransformer.context(
        EventViewContext.builder()
            .settings(_appSettings)
            .isAtlas(isAtlas(request))
            .hasGlobalReadOnly(getAuthzSvc().isGlobalReadOnly(currentUser))
            .selfLinkOnly(false)
            .includeRaw(includeRaw)
            .scope(EventScope.GROUP)
            .build());
    final View view = _viewTransformer.transform(event);
    if (view == null) {
      throw ApiErrorCode.EVENT_NOT_FOUND.exception(envelope);
    }
    return new ApiResponseBuilder(envelope).ok().content(view).build();
  }

  private ActivityFeedSvc getActivityFeedSvc() {
    return _activityFeedSvc;
  }

  @Nullable
  private Date getDateFromString(final String pDateStr) {
    try {
      return TimeUtils2.fromISOString(pDateStr);
    } catch (final IllegalArgumentException exception) {
      return null;
    }
  }

  private int getMonthsInPast(@Nullable final Date date) {
    if (date == null) {
      return 0;
    }

    LocalDate localDate = date.toInstant().atZone(ZoneId.of("UTC")).toLocalDate();

    LocalDate now = LocalDate.now();

    return Period.between(localDate, now).getMonths();
  }

  public AuthzSvc getAuthzSvc() {
    return _authzSvc;
  }
}
