load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "dao",
    srcs = glob(["**/*.java"]),
    deny_warnings = True,
    visibility = [
        "//server/src/main/com/xgen/cloud/alerts/alert/_public/svc:__pkg__",
        "//server/src/main/com/xgen/cloud/alerts/notify/_public/svc:__pkg__",
        "//server/src/main/com/xgen/cloud/eventbackfill/_public/svc:__pkg__",
        "//server/src/main/com/xgen/cloud/slack/messages/_public/svc:__pkg__",
    ],
    deps = [
        "//server/src/main/com/xgen/cloud/activity/_public/model",
        "//server/src/main/com/xgen/cloud/common/appsettings",
        "//server/src/main/com/xgen/cloud/common/constants",
        "//server/src/main/com/xgen/cloud/common/dao/codec",
        "//server/src/main/com/xgen/cloud/common/db/legacy",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/svc/core/dao/base",
        "//third_party:driverwrappers",
        "//third_party:guava",
        "//third_party:morphia",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:org_apache_commons_commons_collections4",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:org_mongodb_bson",
        "@maven//:org_mongodb_mongodb_driver_core",
        "@maven//:org_mongodb_mongodb_driver_legacy",
        "@maven//:org_mongodb_mongodb_driver_sync",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
