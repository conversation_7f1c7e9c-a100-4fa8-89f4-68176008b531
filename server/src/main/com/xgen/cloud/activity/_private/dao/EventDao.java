/* (C) Copyright 2013, MongoDB, Inc. */

package com.xgen.cloud.activity._private.dao;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Suppliers;
import com.mongodb.BasicDBObject;
import com.mongodb.Cursor;
import com.mongodb.DBObject;
import com.mongodb.ReadPreference;
import com.mongodb.WriteConcern;
import com.xgen.cloud.activity._public.model.audit.Audit;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.activity._public.model.event.HostEvent;
import com.xgen.cloud.activity._public.model.event.HostEvent.Type;
import com.xgen.cloud.activity._public.model.event.ReplicaSetEvent;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.db.legacy._public.cursor.ModelCursor;
import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.cloud.common.model._public.annotation.MethodCallPromTimed;
import com.xgen.cloud.common.util._public.util.AggregationUtils;
import com.xgen.svc.core.dao.base.BaseDao;
import com.xgen.svc.core.dao.base.MongoIndex;
import dev.morphia.AdvancedDatastore;
import dev.morphia.Datastore;
import dev.morphia.InsertOptions;
import dev.morphia.Morphia;
import dev.morphia.mapping.Mapper;
import dev.morphia.query.FindOptions;
import dev.morphia.query.Query;
import dev.morphia.query.Sort;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class EventDao extends BaseDao {
  private static final String CLUSTER_NAME_FIELD = "clusterName";

  private static final String CONNECTION_NAME = "mmsdbevents";

  private static final Logger LOG = LoggerFactory.getLogger(EventDao.class);

  private static final List<HostEvent.Type> HOST_EVENT_TYPES =
      Arrays.asList(
          HostEvent.Type.HOST_RESTARTED,
          HostEvent.Type.HOST_NOW_PRIMARY,
          HostEvent.Type.HOST_NOW_SECONDARY);

  private static final Set<EventType> HIDDEN_NDS_EVENTS = Set.of(Type.HOST_IP_CHANGED_AUDIT);
  private final AppSettings _appSettings;

  private final Supplier<AdvancedDatastore> morphiaDataStoreForDbName =
      Suppliers.memoizeWithExpiration(this::createMorphiaDsForDbName, 30, TimeUnit.SECONDS);

  @Inject
  public EventDao(final MongoSvc pMongoSvc, final AppSettings pAppSettings) {
    super(pMongoSvc, CONNECTION_NAME, Event.DB_NAME, Event.COLLECTION_NAME);
    _appSettings = pAppSettings;
  }

  @VisibleForTesting
  public EventDao(
      final MongoSvc mongoSvc,
      final AppSettings appSettings,
      final String dbName,
      final String connectionName) {
    super(mongoSvc, connectionName, dbName, Event.COLLECTION_NAME);
    _appSettings = appSettings;
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_save_duration_seconds",
      help = "Duration in seconds to save one Event document")
  public ObjectId save(final Event event) {
    return (ObjectId)
        getMorphiaDs()
            .save(event, new InsertOptions().writeConcern(WriteConcern.ACKNOWLEDGED))
            .getId();
  }

  public void log(final Event event) {
    LOG.info("Logged SIEM Event: {}", getMorphia().toDBObject(event));
  }

  public void insert(final Collection<Event> events) {
    getMorphiaDs().save(events);
  }

  public Event findById(final ObjectId pId) {
    return getMorphiaDs()
        .find(Event.class)
        .filter(ID, pId)
        .first(getSecondaryPreferredFindOptions());
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_org_ids_by_event_type_duration_seconds",
      help = "Duration in seconds to find org ids by type")
  public Set<ObjectId> findOrgIdsByEventType(final EventType pEventType) {
    return getMorphiaDs()
        .createQuery(Event.class)
        .disableValidation()
        .field(Event.EVENT_TYPE_FIELD)
        .equal(pEventType)
        .project(Event.ORG_ID_FIELD, true)
        .project(Event.TYPE_FIELD, true)
        .find(getSecondaryPreferredFindOptions())
        .toList()
        .stream()
        .map(Event::getOrgId)
        .collect(Collectors.toSet());
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_events_by_type_duration_seconds",
      help = "Duration in seconds to find all events by type")
  public List<Event> findByDate(final EventType pEventType, final Date pStartTime) {
    return getMorphiaDs()
        .createQuery(Event.class)
        .disableValidation()
        // base class doesn't have "et" field, but all subclasses do
        .field(Event.EVENT_TYPE_FIELD)
        .equal(pEventType)
        .field(Event.CREATED_AT_FIELD)
        .greaterThanOrEq(pStartTime)
        .order(Sort.descending(Event.CREATED_AT_FIELD))
        .find(getSecondaryPreferredFindOptions())
        .toList();
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_all_events_duration_seconds",
      help = "Duration in seconds to find all events")
  public List<Event> findAll(
      final int pLimit,
      final List<String> types,
      final Date startDate,
      final Date endDate,
      final boolean readPrimary,
      final boolean sortAscending) {
    if (pLimit < 1) {
      throw new IllegalStateException("Limit cannot be smaller than 1");
    }
    final Query<Event> query = getMorphiaDs().createQuery(Event.class).disableValidation();
    if (types != null && types.size() > 0) {
      query.field(Event.EVENT_TYPE_FIELD).in(types);
    }

    if (endDate != null) {
      query.field(Event.CREATED_AT_FIELD).lessThanOrEq(endDate);
    }
    if (startDate != null) {
      query.field(Event.CREATED_AT_FIELD).greaterThanOrEq(startDate);
    }
    return query
        .order(sortAscending ? Sort.ascending(ID) : Sort.descending(ID))
        .find(
            (readPrimary ? getPrimaryFindOptions() : getSecondaryPreferredFindOptions())
                .limit(pLimit))
        .toList();
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_model_cursor_duration_seconds",
      help = "Duration in seconds to find a model cursor")
  public ModelCursor<Event> findAllModelCursor(
      final Collection<EventType> pTypes,
      final Date pStartTime,
      final Date pEndTime,
      final boolean sortAscending) {
    final Query<Event> query = getMorphiaDs().createQuery(Event.class).disableValidation();
    if (pStartTime != null) {
      query.field(Event.CREATED_AT_FIELD).greaterThanOrEq(pStartTime);
    }
    if (pEndTime != null) {
      query.field(Event.CREATED_AT_FIELD).lessThanOrEq(pEndTime);
    }
    if (pTypes != null && !pTypes.isEmpty()) {
      final Collection<String> eventTypes =
          pTypes.stream().map(EventType::name).collect(Collectors.toList());
      query.field(Event.EVENT_TYPE_FIELD).in(eventTypes);
    }

    if (sortAscending) {
      query.order(Sort.ascending(Event.CREATED_AT_FIELD));
    } else {
      query.order(Sort.descending(Event.CREATED_AT_FIELD));
    }

    return findModelCursor(query, getSecondaryPreferredFindOptions(), Event.class);
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_all_before_id_duration_seconds",
      help = "Duration in seconds to find all events before an id")
  public List<Event> findAllBeforeId(
      final int pLimit,
      final ObjectId pEndId,
      final List<String> pTypes,
      final Date pStartDate,
      final Date pEndDate) {
    if (pLimit < 1) {
      throw new IllegalStateException("Limit cannot be smaller than 1");
    }
    final Query<Event> query = getMorphiaDs().createQuery(Event.class).disableValidation();

    if (pTypes != null && pTypes.size() > 0) {
      query.field(Event.EVENT_TYPE_FIELD).in(pTypes);
    }
    if (pEndDate != null) {
      query.field(Event.CREATED_AT_FIELD).lessThanOrEq(pEndDate);
    }
    if (pStartDate != null) {
      query.field(Event.CREATED_AT_FIELD).greaterThanOrEq(pStartDate);
    }
    return query
        .field(ID)
        .lessThan(pEndId)
        .order(Sort.descending(ID))
        .find(getSecondaryPreferredFindOptions().limit(pLimit))
        .toList();
  }

  List<Event> findAllForGroupWithoutHidden(
      final ObjectId pGroupId,
      final int pOffset,
      final int pLimit,
      final List<String> pTypes,
      final Date pStartTime,
      final Date pEndTime,
      final boolean pIsNDS) {
    return findAllForGroup(
        pGroupId, pOffset, pLimit, pTypes, pStartTime, pEndTime, null, true, false, pIsNDS);
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_all_for_group_duration_seconds",
      help = "Duration in seconds to find events for a group")
  public List<Event> findAllForGroup(
      final ObjectId pGroupId,
      final int pOffset,
      final int pLimit,
      final List<String> pTypes,
      final Date pStartTime,
      final Date pEndTime,
      final Set<String> pClusterNames,
      final boolean pExcludeHidden,
      final boolean pAscending,
      final boolean pIsNDS) {
    final Query<Event> query =
        getMorphiaDs()
            .createQuery(Event.class)
            .disableValidation()
            .field(Event.GROUP_ID_FIELD)
            .equal(pGroupId);

    if (pStartTime != null) {
      query.field(Event.CREATED_AT_FIELD).greaterThanOrEq(pStartTime);
    }
    if (pEndTime != null) {
      query.field(Event.CREATED_AT_FIELD).lessThanOrEq(pEndTime);
    }

    if (pExcludeHidden) {
      query.field(Event.HIDDEN_FIELD).notEqual(true);
    }

    if (pTypes != null && !pTypes.isEmpty()) {
      query.field(Event.EVENT_TYPE_FIELD).in(pTypes);
    }

    final List<String> hiddenNDSEvents =
        HIDDEN_NDS_EVENTS.stream()
            .map(EventType::toString)
            .filter(event -> pTypes != null && !pTypes.contains(event))
            .collect(Collectors.toList());
    if (pIsNDS && !hiddenNDSEvents.isEmpty()) {
      query.field(Event.EVENT_TYPE_FIELD).notIn(hiddenNDSEvents);
    }

    if (pClusterNames != null && !pClusterNames.isEmpty()) {
      query.criteria(CLUSTER_NAME_FIELD).in(pClusterNames);
    }

    final Sort order =
        pAscending
            ? Sort.ascending(Event.CREATED_AT_FIELD)
            : Sort.descending(Event.CREATED_AT_FIELD);
    return query
        .order(order)
        .find(
            new FindOptions()
                .readPreference(ReadPreference.secondaryPreferred())
                .skip(pOffset)
                .limit(pLimit))
        .toList();
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_all_as_db_obj_duration_seconds",
      help = "Duration in seconds to find events as db objects")
  public List<DBObject> findAllDbObj(
      final int pLimit, final int pAgeLimit, final List<String> pUnexportedEventTypes) {
    final BasicDBObject query =
        new BasicDBObject(
            Event.CREATED_AT_FIELD,
            new BasicDBObject(
                com.xgen.svc.core.dao.base.BaseDao.GTE, DateUtils.addDays(new Date(), -pAgeLimit)));
    query.append(Event.EVENT_TYPE_FIELD, new BasicDBObject(NIN, pUnexportedEventTypes));
    return getDbCollection()
        .find(query)
        .sort(new BasicDBObject(Event.CREATED_AT_FIELD, -1))
        .limit(pLimit)
        .toArray();
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_all_for_group_as_db_obj_duration_seconds",
      help = "Duration in seconds to find events for a group as db objects")
  public List<DBObject> findAllForGroupDbObj(
      final ObjectId pGroupId,
      final int pLimit,
      final int pAgeLimit,
      final List<String> pUnexportedEventTypes) {
    final BasicDBObject query = new BasicDBObject(Event.GROUP_ID_FIELD, pGroupId);
    query.append(
        Event.CREATED_AT_FIELD,
        new BasicDBObject(
            com.xgen.svc.core.dao.base.BaseDao.GTE, DateUtils.addDays(new Date(), -pAgeLimit)));
    query.append(Event.EVENT_TYPE_FIELD, new BasicDBObject(NIN, pUnexportedEventTypes));
    return getDbCollection()
        .find(query)
        .sort(new BasicDBObject(Event.CREATED_AT_FIELD, -1))
        .limit(pLimit)
        .toArray();
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_all_model_cursor_for_group_duration_seconds",
      help = "Duration in seconds to find events for a group")
  public ModelCursor<Event> findAllModelCursorForGroup(
      final ObjectId pGroupId,
      final int pOffset,
      final int pLimit,
      final Collection<EventType> pTypes,
      final Collection<EventType> pExcludedTypes,
      final Set<String> pClusterNames,
      final Date pStartTime,
      final Date pEndTime,
      final boolean pExcludeHidden,
      final boolean pAscending,
      final boolean pIsNDS) {
    final Query<Event> query =
        getMorphiaDs()
            .createQuery(Event.class)
            .disableValidation()
            .field(Event.GROUP_ID_FIELD)
            .equal(pGroupId);

    if (pStartTime != null) {
      query.field(Event.CREATED_AT_FIELD).greaterThanOrEq(pStartTime);
    }
    if (pEndTime != null) {
      query.field(Event.CREATED_AT_FIELD).lessThanOrEq(pEndTime);
    }

    if (pExcludeHidden) {
      query.field(Event.HIDDEN_FIELD).notEqual(true);
    }

    if (pTypes != null && !pTypes.isEmpty()) {
      final Collection<String> eventTypes =
          pTypes.stream().map(EventType::name).collect(Collectors.toList());
      query.field(Event.EVENT_TYPE_FIELD).in(eventTypes);
    }

    if (!CollectionUtils.isEmpty(pExcludedTypes)) {
      final Collection<String> excludedTypes =
          pExcludedTypes.stream().map(EventType::name).collect(Collectors.toList());
      query.field(Event.EVENT_TYPE_FIELD).notIn(excludedTypes);
    }

    final List<EventType> hiddenNDSEvents =
        HIDDEN_NDS_EVENTS.stream()
            .filter(event -> pTypes != null && !pTypes.contains(event))
            .collect(Collectors.toList());
    if (pIsNDS && !hiddenNDSEvents.isEmpty()) {
      query.field(Event.EVENT_TYPE_FIELD).notIn(hiddenNDSEvents);
    }

    if (pClusterNames != null && !pClusterNames.isEmpty()) {
      query.criteria(CLUSTER_NAME_FIELD).in(pClusterNames);
    }

    final Sort order =
        pAscending
            ? Sort.ascending(Event.CREATED_AT_FIELD)
            : Sort.descending(Event.CREATED_AT_FIELD);
    query.order(order);
    return findModelCursor(
        query,
        new FindOptions()
            .readPreference(ReadPreference.secondaryPreferred())
            .skip(pOffset)
            .limit(pLimit),
        Event.class);
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_by_event_type_for_group_duration_seconds",
      help = "Duration in seconds to find events by type for a group")
  public List<Event> findByEventTypeForGroup(final ObjectId pGroupId, final EventType pEventType) {
    return getMorphiaDs()
        .createQuery(Event.class)
        .disableValidation() // base class doesn't have "et" field, but all subclasses do
        .field(Event.GROUP_ID_FIELD)
        .equal(pGroupId)
        .field(Event.EVENT_TYPE_FIELD)
        .equal(pEventType)
        .order(Sort.ascending(Event.CREATED_AT_FIELD))
        .find(getSecondaryPreferredFindOptions())
        .toList();
  }

  public Event findEarliestByEventTypeForGroup(
      final ObjectId pGroupId, final EventType pEventType) {
    return getMorphiaDs()
        .createQuery(Event.class)
        .disableValidation() // base class doesn't have "et" field, but all subclasses do
        .field(Event.GROUP_ID_FIELD)
        .equal(pGroupId)
        .field(Event.EVENT_TYPE_FIELD)
        .equal(pEventType)
        .order(Sort.ascending(Event.CREATED_AT_FIELD))
        .first(getSecondaryPreferredFindOptions());
  }

  public Event findMostRecentByEventTypeForCluster(
      final ObjectId pGroupId, final String pClusterName, final EventType pEventType) {
    return getMorphiaDs()
        .createQuery(Event.class)
        .disableValidation() // base class doesn't have "et" field, but all subclasses do
        .field(Event.GROUP_ID_FIELD)
        .equal(pGroupId)
        .field(CLUSTER_NAME_FIELD)
        .equal(pClusterName)
        .field(Event.EVENT_TYPE_FIELD)
        .equal(pEventType)
        .order(Sort.descending(Event.CREATED_AT_FIELD))
        .first(getSecondaryPreferredFindOptions());
  }

  public List<Event> findByEventTypeMostRecentFirst(final EventType pEventType) {
    return getMorphiaDs()
        .createQuery(Event.class)
        .disableValidation() // base class doesn't have "et" field, but all subclasses do
        .field(Event.EVENT_TYPE_FIELD)
        .equal(pEventType)
        .order(Sort.descending(Event.CREATED_AT_FIELD))
        .find(getSecondaryPreferredFindOptions())
        .toList();
  }

  public List<Event> findByEventTypeForGroupBefore(
      final ObjectId pGroupId, final EventType pEventType, final Date pStartTime) {
    return getMorphiaDs()
        .createQuery(Event.class)
        .disableValidation() // base class doesn't have "et" field, but all subclasses do
        .field(Event.GROUP_ID_FIELD)
        .equal(pGroupId)
        .field(Event.EVENT_TYPE_FIELD)
        .equal(pEventType)
        .field(Event.CREATED_AT_FIELD)
        .greaterThanOrEq(pStartTime)
        .order(Sort.ascending(Event.CREATED_AT_FIELD))
        .find(getSecondaryPreferredFindOptions())
        .toList();
  }

  public List<Event> findAllForGroupWithoutHidden(
      final ObjectId pGroupId, final int pOffset, final int pLimit, final Date pEndTime) {
    return getMorphiaDs()
        .createQuery(Event.class)
        .field(Event.GROUP_ID_FIELD)
        .equal(pGroupId)
        .field(Event.CREATED_AT_FIELD)
        .lessThanOrEq(pEndTime)
        .order(Sort.descending(Event.CREATED_AT_FIELD))
        .find(
            new FindOptions()
                .readPreference(ReadPreference.secondaryPreferred())
                .skip(pOffset)
                .limit(pLimit))
        .toList();
  }

  public List<Event> findAllForOrganizationWithoutHidden(
      final ObjectId pOrgId,
      final int pLimit,
      final List<String> pTypes,
      final Date pStartTime,
      final Date pEndTime,
      final List<EventType> pOrganizationOverrideEventTypes) {
    return findAllForOrganization(
        pOrgId,
        pLimit,
        pTypes,
        pStartTime,
        pEndTime,
        pOrganizationOverrideEventTypes,
        true,
        false,
        null,
        null);
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_all_for_org_duration_seconds",
      help = "Duration in seconds to find events for an org")
  public List<Event> findAllForOrganization(
      final ObjectId pOrgId,
      final int pLimit,
      final List<String> pTypes,
      final Date pStartTime,
      final Date pEndTime,
      final List<EventType> pOrganizationOverrideEventTypes,
      final boolean pExcludeHidden,
      final boolean pGetPriorEvents,
      final ObjectId pBeforeId,
      final ObjectId pAfterId) {
    final Query<Event> query =
        getMorphiaDs()
            .createQuery(Event.class)
            .disableValidation()
            .field(Event.ORG_ID_FIELD)
            .equal(pOrgId);

    if (pBeforeId != null) {
      final Event beforeEvent =
          getMorphiaDs()
              .createQuery(Event.class)
              .field(ID)
              .equal(pBeforeId)
              .find(getSecondaryPreferredFindOptions())
              .next();
      final Date beforeDate = beforeEvent.getCreatedAt();
      query.field(Event.CREATED_AT_FIELD).greaterThanOrEq(beforeDate);
    } else if (pAfterId != null) {
      final Event afterEvent =
          getMorphiaDs()
              .createQuery(Event.class)
              .field(ID)
              .equal(pAfterId)
              .find(getSecondaryPreferredFindOptions())
              .next();
      final Date afterDate = afterEvent.getCreatedAt();
      query.field(Event.CREATED_AT_FIELD).lessThanOrEq(afterDate);
    }

    if (pExcludeHidden) {
      query.field(Event.HIDDEN_FIELD).notEqual(true);
    }

    if (pStartTime != null) {
      query.field(Event.CREATED_AT_FIELD).greaterThanOrEq(pStartTime);
    }

    if (pEndTime != null) {
      query.field(Event.CREATED_AT_FIELD).lessThanOrEq(pEndTime);
    }

    if (pTypes != null && !pTypes.isEmpty()) {
      query.field(Event.EVENT_TYPE_FIELD).in(pTypes);
    }

    // This "or" statement is used to include team audits and group audits that have orgIds and also
    // have groupIds
    query.or(
        query.criteria(Event.GROUP_ID_FIELD).doesNotExist(),
        query.criteria(Event.EVENT_TYPE_FIELD).in(pOrganizationOverrideEventTypes));
    final Sort order = pGetPriorEvents ? Sort.ascending("cre") : Sort.descending("cre");
    return query.order(order).find(getSecondaryPreferredFindOptions().limit(pLimit)).toList();
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_all_for_org_as_model_cursor_duration_seconds",
      help = "Duration in seconds to find events for an org as a model cursor")
  public ModelCursor<Event> findAllModelCursorForOrganization(
      final ObjectId pOrgId,
      final Collection<EventType> pTypes,
      final Date pStartTime,
      final Date pEndTime,
      final List<EventType> pOrganizationOverrideEventTypes,
      final boolean pExcludeHidden) {
    final Query<Event> query =
        getMorphiaDs()
            .createQuery(Event.class)
            .disableValidation()
            .field(Event.ORG_ID_FIELD)
            .equal(pOrgId);
    if (pExcludeHidden) {
      query.field(Event.HIDDEN_FIELD).notEqual(true);
    }

    if (pStartTime != null) {
      query.field(Event.CREATED_AT_FIELD).greaterThanOrEq(pStartTime);
    }

    if (pEndTime != null) {
      query.field(Event.CREATED_AT_FIELD).lessThanOrEq(pEndTime);
    }

    if (pTypes != null && !pTypes.isEmpty()) {
      final Collection<String> eventTypes =
          pTypes.stream().map(EventType::name).collect(Collectors.toList());
      query.field(Event.EVENT_TYPE_FIELD).in(eventTypes);
    }

    // Team/group audits are sometimes saved with both an orgId and a groupId. We only want to
    // return the ones that explicitly have an "ORG" scope
    query.or(
        query.criteria(Event.GROUP_ID_FIELD).doesNotExist(),
        query.criteria(Event.EVENT_TYPE_FIELD).in(pOrganizationOverrideEventTypes));
    query.order(Sort.descending(Event.CREATED_AT_FIELD));

    final FindOptions findOptions =
        getSecondaryPreferredFindOptions()
            .hint(
                new BasicDBObject(Event.ORG_ID_FIELD, 1)
                    .append(Event.EVENT_TYPE_FIELD, 1)
                    .append(Event.GROUP_ID_FIELD, 1)
                    .append(Event.HIDDEN_FIELD, 1)
                    .append(Event.CREATED_AT_FIELD, -1));
    return findModelCursor(query, findOptions, Event.class);
  }

  public List<Event> findByEventTypeForOrganization(
      final ObjectId pOrgId, final EventType pEventType) {
    return getMorphiaDs()
        .createQuery(Event.class)
        .disableValidation() // base doesn't have "et" field, but all subclasses do
        .field(Event.ORG_ID_FIELD)
        .equal(pOrgId)
        .field(Event.EVENT_TYPE_FIELD)
        .equal(pEventType)
        .order(Sort.ascending(Event.CREATED_AT_FIELD))
        .find(getSecondaryPreferredFindOptions())
        .toList();
  }

  @MethodCallPromTimed(
      name = "mms_core_dao_event_find_after_id_with_skip_duration_seconds",
      help = "Duration in seconds to find all events after an id")
  public Optional<Event> findAfterIdWithSkip(final ObjectId pStartId, final int pSkip) {
    return Optional.ofNullable(
        getMorphiaDs()
            .createQuery(Event.class)
            .disableValidation()
            .field(ID)
            .greaterThanOrEq(pStartId)
            .project(Event.TYPE_FIELD, true)
            .order(Sort.ascending(ID))
            .first(getSecondaryPreferredFindOptions().skip(pSkip)));
  }

  public List<ReplicaSetEvent> findReplicaSetEventsInTimeRange(
      final ObjectId pGroupId,
      final Date pStart,
      final Date pEnd,
      final EventType pEventType,
      final ObjectId pClusterId) {

    return getMorphiaDs()
        .createQuery(ReplicaSetEvent.class)
        .disableValidation()
        .field(Event.GROUP_ID_FIELD)
        .equal(pGroupId)
        .field(Event.EVENT_TYPE_FIELD)
        .equal(pEventType)
        .field(Event.CREATED_AT_FIELD)
        .greaterThanOrEq(pStart)
        .field(Event.CREATED_AT_FIELD)
        .lessThanOrEq(pEnd)
        .field(ReplicaSetEvent.CLUSTER_ID_FIELD)
        .equal(pClusterId)
        .find(getSecondaryPreferredFindOptions())
        .toList();
  }

  // Used to display annotations on host charts
  public List<HostEvent> findHostEventsInTimeRange(
      final ObjectId pGroupId, final String pHostId, final Date pStart, final Date pEnd) {
    return getMorphiaDs()
        .createQuery(HostEvent.class)
        .field(Event.GROUP_ID_FIELD)
        .equal(pGroupId)
        .field(Event.EVENT_TYPE_FIELD)
        .in(HOST_EVENT_TYPES)
        .field(Event.CREATED_AT_FIELD)
        .greaterThanOrEq(pStart)
        .field(Event.CREATED_AT_FIELD)
        .lessThanOrEq(pEnd)
        .field(HostEvent.HOST_ID_FIELD)
        .equal(pHostId)
        .find(getSecondaryPreferredFindOptions())
        .toList();
  }

  public void deleteByGroup(final ObjectId pGroupId) {
    final Query<Event> query =
        getMorphiaDs().createQuery(Event.class).field(Event.GROUP_ID_FIELD).equal(pGroupId);
    getMorphiaDs().delete(query);
  }

  @SuppressWarnings("unchecked")
  public Set<ObjectId> findNDSGroupSSHUniqueKeyAccessGrantsByUserSince(
      final ObjectId pUserId, final Date pSince) {
    final DBObject match =
        newDbObj(
            MATCH,
            newDbObj(Event.EVENT_TYPE_FIELD, HostEvent.Type.SSH_KEY_NDS_HOST_ACCESS_GRANTED.name())
                .append(Event.CREATED_AT_FIELD, newDbObj(GTE, pSince))
                .append(Audit.USER_ID_FIELD, pUserId));
    final DBObject group =
        newDbObj(
            GROUP,
            newDbObj(ID, true)
                .append("groupIds", newDbObj(ADD_TO_SET, '$' + Event.GROUP_ID_FIELD)));

    final List<DBObject> pipeline = Arrays.asList(match, group);
    try (final Cursor cursor =
        getDbCollection().aggregate(pipeline, AggregationUtils.defaultOptions())) {
      if (cursor.hasNext()) {
        final DBObject o = cursor.next();
        return new HashSet<>((Collection<ObjectId>) o.get("groupIds"));
      }
    }

    return Collections.emptySet();
  }

  @Override
  public List<MongoIndex> getIndexes() {
    final List<MongoIndex> list = super.getIndexes();
    list.add(
        new MongoIndex(this)
            .name("groupIdIdx")
            .key(Event.GROUP_ID_FIELD)
            .key(Event.CREATED_AT_FIELD, -1));
    list.add(new MongoIndex(this).key(Event.ORG_ID_FIELD).key(Event.CREATED_AT_FIELD, -1));

    list.add(
        new MongoIndex(this)
            .key(Event.GROUP_ID_FIELD)
            .key(Event.EVENT_TYPE_FIELD)
            .key(Event.HIDDEN_FIELD)
            .key(Event.CREATED_AT_FIELD, -1));

    list.add(
        new MongoIndex(this)
            .key(Event.ORG_ID_FIELD)
            .key(Event.EVENT_TYPE_FIELD)
            .key(Event.GROUP_ID_FIELD)
            .key(Event.HIDDEN_FIELD)
            .key(Event.CREATED_AT_FIELD, -1));
    list.add(
        new MongoIndex(this)
            .name("eventTypeIdx")
            .key(Event.EVENT_TYPE_FIELD)
            .key(Event.CREATED_AT_FIELD, -1));
    list.add(
        new MongoIndex(this)
            .partialFilterExpression(
                new BasicDBObject(Event.RESOURCE_IDS_FIELD, new BasicDBObject(EXISTS, true)))
            .key(Event.GROUP_ID_FIELD)
            .key(Event.RESOURCE_IDS_FIELD)
            .key(Event.CREATED_AT_FIELD, -1));
    list.add(
        new MongoIndex(this)
            .partialFilterExpression(
                new BasicDBObject(Event.RESOURCE_IDS_FIELD, new BasicDBObject(EXISTS, true)))
            .key(Event.GROUP_ID_FIELD)
            .key(Event.EVENT_TYPE_FIELD)
            .key(Event.RESOURCE_IDS_FIELD)
            .key(Event.CREATED_AT_FIELD, -1));
    // TODO (James): remove this index
    list.add(
        new MongoIndex(this)
            .partialFilterExpression(
                new BasicDBObject(CLUSTER_NAME_FIELD, new BasicDBObject(EXISTS, true)))
            .key(Event.GROUP_ID_FIELD)
            .key(CLUSTER_NAME_FIELD)
            .key(Event.CREATED_AT_FIELD, -1));
    // TODO (James): remove this index
    list.add(
        new MongoIndex(this)
            .partialFilterExpression(
                new BasicDBObject(CLUSTER_NAME_FIELD, new BasicDBObject(EXISTS, true)))
            .key(Event.GROUP_ID_FIELD)
            .key(Event.EVENT_TYPE_FIELD)
            .key(CLUSTER_NAME_FIELD)
            .key(Event.CREATED_AT_FIELD, -1));

    return list;
  }

  private AppSettings getAppSettings() {
    return _appSettings;
  }

  public Optional<Event> findPairedStartEvent(
      final ObjectId pGroupId,
      final String pClusterName,
      final Date pCurrentDate,
      final String pEventType,
      final ObjectId pPlanId) {

    final Date lowerBound = Date.from(pCurrentDate.toInstant().minus(2, ChronoUnit.DAYS));
    final Optional<Event> returnValue;

    final List<Event> list =
        getMorphiaDs()
            .createQuery(Event.class)
            .disableValidation() // base class doesn't have "et" field, but all subclasses do
            .field(Event.GROUP_ID_FIELD)
            .equal(pGroupId)
            .field(Event.EVENT_TYPE_FIELD)
            .equal(pEventType)
            .field(CLUSTER_NAME_FIELD) // Constant not used in relevant events
            .equal(pClusterName)
            .field(Event.CREATED_AT_FIELD)
            .greaterThan(lowerBound)
            .field("planId")
            .equal(pPlanId)
            .find(getSecondaryPreferredFindOptions())
            .toList();
    if (!list.isEmpty()) {
      if (list.size() > 1) {
        LOG.warn(
            "Found {} prior events for group {} clusterName {} eventType {} planId {}",
            list.size(),
            pGroupId,
            pClusterName,
            pEventType,
            pPlanId);
      }
      return Optional.of(list.get(0));
    }
    return Optional.empty();
  }

  /**
   * The default Morphia datastore is created by connection name and attempts to use the same
   * connection name as the db name for the connection. This dao uses a connection name that does
   * not match the expected DB name. When Morphia is removed from this class, this override can be
   * removed as well.
   */
  @Override
  protected AdvancedDatastore getMorphiaDs() {
    return morphiaDataStoreForDbName.get();
  }

  AdvancedDatastore createMorphiaDsForDbName() {
    final Mapper mapper = getMorphia().getMapper();
    final Morphia morphia = new Morphia(mapper);
    final Datastore datastore = morphia.createDatastore(getMongo(), getDbName());
    datastore.setDefaultWriteConcern(WriteConcern.ACKNOWLEDGED);
    return (AdvancedDatastore) datastore;
  }

  // Pass-through method because we need access to Morphia for the EventServiceBackfill script
  @Override
  public Morphia getMorphia() {
    return super.getMorphia();
  }
}
