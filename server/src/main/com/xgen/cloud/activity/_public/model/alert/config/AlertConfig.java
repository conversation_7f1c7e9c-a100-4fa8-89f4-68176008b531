package com.xgen.cloud.activity._public.model.alert.config;

import static com.xgen.cloud.activity._public.model.alert.Alert.Status.TRACKING;
import static com.xgen.cloud.activity._public.model.alert.config.AlertConfigSource.DEFAULT;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.activity._public.model.alert.Alert;
import com.xgen.cloud.activity._public.model.event.EventScope;
import com.xgen.cloud.activity._public.model.event.EventType;
import com.xgen.cloud.common.eventseverity._public.model.EventSeverity;
import com.xgen.cloud.common.group._public.view.GroupInfoView;
import com.xgen.cloud.common.model._public.annotation.WithGenEncryptField;
import com.xgen.cloud.common.model._public.rules.Validation;
import com.xgen.cloud.common.model._public.rules.Validators;
import com.xgen.cloud.notification._public.model.AdminNotification;
import com.xgen.cloud.notification._public.model.GroupNotification;
import com.xgen.cloud.notification._public.model.Notification;
import com.xgen.cloud.notification._public.model.OrgNotification;
import com.xgen.cloud.notification._public.model.TeamNotification;
import com.xgen.cloud.notification._public.model.UserNotification;
import com.xgen.svc.mms.model.grouptype.GroupType;
import dev.morphia.annotations.Embedded;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Id;
import dev.morphia.annotations.PrePersist;
import dev.morphia.annotations.Property;
import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;

/**
 * Contains all configuration details for an alert, including the trigger (the condition that causes
 * the alert to fire), the sources (which resources to monitor - hosts, replica sets, clusters,
 * etc.), and the notifications (who to tell and when).
 */
@Entity(value = AlertConfig.COLLECTION_NAME, noClassnameStored = true, queryNonPrimary = false)
@WithGenEncryptField(DB = AlertConfig.DB_NAME, Collection = AlertConfig.COLLECTION_NAME)
public abstract class AlertConfig implements Validation {

  public interface MatcherField {

    String getFieldName();
  }

  public static final String DB_NAME = "mmsdbconfig";
  public static final String COLLECTION_NAME = "config.alertConfigs";

  // Field name constants
  public static final String TYPE_FIELD = "_t";
  public static final String DATE_CREATED_FIELD = "cre";
  public static final String DATE_UPDATED_FIELD = "upd";
  public static final String GROUP_ID_FIELD = "cid";
  public static final String ORG_ID_FIELD = "orgId";
  public static final String ENABLED_FIELD = "enabled";
  public static final String EVENT_TYPE_FIELD = "et";
  public static final String MATCHERS_FIELD = "matchers";
  public static final String NOTIFY_FIELD = "notify";
  public static final String GROUPS_FIELD = "groups";
  public static final String FOR_ALL_GROUPS_FIELD = "forAllGroups";
  public static final String SCOPE_FIELD = "ac";
  public static final String GROUP_TYPE_FIELD = "groupType";
  public static final String TAGS_FIELD = "tags";
  public static final String SOURCE_FIELD = "source";
  public static final String SEVERITY_OVERRIDE_FIELD = "severityOverride";

  @JsonProperty("id")
  @Id
  private ObjectId _id;

  @JsonProperty(DATE_CREATED_FIELD)
  @Property(DATE_CREATED_FIELD)
  private Date _createdAt;

  @JsonProperty(DATE_UPDATED_FIELD)
  @Property(DATE_UPDATED_FIELD)
  private Date _updatedAt;

  // JsonProperty annotation specified in JsonTypeInfo
  @Property(TYPE_FIELD)
  private String _typeName;

  // Group that owns this alert configuration
  @JsonProperty(GROUP_ID_FIELD)
  @Property(GROUP_ID_FIELD)
  private ObjectId _groupId;

  // Org that owns this alert configuration
  @JsonProperty(ORG_ID_FIELD)
  @Property(ORG_ID_FIELD)
  private ObjectId _orgId;

  // If not enabled, this alert is effectively ignored
  @JsonProperty(ENABLED_FIELD)
  @Property(ENABLED_FIELD)
  private boolean _enabled;

  // List of matching rules for which EventSources will trigger the event
  @JsonProperty(MATCHERS_FIELD)
  @Embedded(MATCHERS_FIELD)
  private List<Matcher> _matchers;

  // Notification rules to apply when the alert is triggered
  @JsonProperty(NOTIFY_FIELD)
  @Embedded(NOTIFY_FIELD)
  private List<Notification> _notifications;

  @JsonProperty(GROUPS_FIELD)
  @Embedded(GROUPS_FIELD)
  private List<GroupInfoView> _groups;

  @JsonProperty(FOR_ALL_GROUPS_FIELD)
  @Property(FOR_ALL_GROUPS_FIELD)
  private Boolean _forAllGroups;

  @JsonProperty(SCOPE_FIELD)
  @Property(SCOPE_FIELD)
  private EventScope _scope;

  @JsonProperty(GROUP_TYPE_FIELD)
  @Property(GROUP_TYPE_FIELD)
  private GroupType _groupType;

  @JsonProperty(TAGS_FIELD)
  @Property(TAGS_FIELD)
  private List<String> _tags;

  @JsonProperty(SOURCE_FIELD)
  @Property(SOURCE_FIELD)
  private AlertConfigSource _source;

  @JsonProperty(SEVERITY_OVERRIDE_FIELD)
  @Property(SEVERITY_OVERRIDE_FIELD)
  private EventSeverity severityOverride;

  protected AlertConfig() {
    // Morphia/Jackson constructor
  }

  protected AlertConfig(final String pTypeName) {
    _typeName = pTypeName;
  }

  protected AlertConfig(final Builder pBuilder) {
    _typeName = pBuilder.typeName;
    _id = pBuilder.id;
    _groupId = pBuilder.groupId;
    _orgId = pBuilder.orgId;
    _enabled = pBuilder.enabled;
    _matchers = pBuilder.matchers;
    _notifications = pBuilder.notifications;
    _createdAt = pBuilder.createdAt;
    _updatedAt = pBuilder.updatedAt;
    _groups = pBuilder.groups;
    _forAllGroups = pBuilder.forAllGroups;
    _scope = pBuilder.scope;
    _groupType = pBuilder.groupType;
    _tags = pBuilder.tags;
    _source = pBuilder.source;
    severityOverride = pBuilder.severityOverride;
  }

  public abstract Builder copier();

  public abstract @Nullable EventType getEventType();

  /**
   * Is the event type for this alert configuration alertable? An alertable event type is one which
   * represents the opening of an alert that requires action to be closed. Informational alerts are
   * the opposite: they are one-time events that have no persistent state.
   */
  public boolean isAlertableEventType() {
    final EventType eventType = getEventType();
    return eventType != null && eventType.getInfo().getResolvingEventType() != null;
  }

  public ObjectId getId() {
    return _id;
  }

  public Date getCreatedAt() {
    return _createdAt;
  }

  public Date getUpdatedAt() {
    return _updatedAt;
  }

  public String getTypeName() {
    return _typeName;
  }

  public ObjectId getGroupId() {
    return _groupId;
  }

  public ObjectId getOrgId() {
    return _orgId;
  }

  public AlertConfigSource getSource() {
    return _source;
  }

  public EventSeverity getSeverityOverride() {
    return severityOverride;
  }

  public boolean isEnabled() {
    return _enabled;
  }

  public boolean isDisabled() {
    return !_enabled;
  }

  public GroupType getGroupType() {
    return _groupType;
  }

  public List<Matcher> getMatchers() {
    if (_matchers == null) {
      return Collections.emptyList();
    } else {
      return Collections.unmodifiableList(_matchers);
    }
  }

  public List<Notification> getNotifications() {
    if (_notifications == null) {
      return Collections.emptyList();
    } else {
      return Collections.unmodifiableList(_notifications);
    }
  }

  public Notification getNotificationByNotificationId(ObjectId notificationId) {
    return _notifications.stream()
        .filter(n -> n.getId().equals(notificationId))
        .findFirst()
        .orElse(null);
  }

  public boolean hasNotifications() {
    return _notifications != null && !_notifications.isEmpty();
  }

  /**
   * Determine if this alert config is valid. Currently if config has any notifications it is valid
   */
  public boolean isValid() {
    return hasNotifications();
  }

  /**
   * Get the list of groups to be included or excluded for a global alert. When {@link
   * #isForAllGroups} returns true, this function returns a list of groups to be excluded, and the
   * alert will apply to all groups except those returned. When {@link #isForAllGroups} returns
   * false, this function returns a list of groups to be included, and the alert will only apply to
   * those groups that are returned.
   */
  public List<GroupInfoView> getGroups() {
    return _groups == null ? Collections.emptyList() : Collections.unmodifiableList(_groups);
  }

  /**
   * Check if a global alert applies to all groups, possibly except for those specified. When this
   * function returns true, {@link #getGroups} returns a list of groups to be excluded, and the
   * alert will apply to all groups except those in the list. When this function returns false,
   * {@link #getGroups} returns a list of groups to be included, and the alert will apply only to
   * those groups specified in the list.
   */
  public boolean isForAllGroups() {
    return _forAllGroups != null && _forAllGroups;
  }

  /** Check if an alert config should be checked for a specified group. */
  public boolean isForGroup(
      final ObjectId pGroupId, final List<String> pGroupTags, final GroupType pGroupType) {
    if (isGroupAlertConfig()) {
      return getGroupId().equals(pGroupId);
    } else if (isGlobalAlertConfig()) {
      // If the alert is marked as for all groups, then the group list contains a list of groups to
      // exclude, and we
      // process the alert for all other groups except those listed. Otherwise, it is a list of
      // groups to include and
      // we should only process the alert for groups that are listed.
      final boolean groupInList =
          getGroups().stream().anyMatch(g -> Objects.equals(g.getGroupId(), pGroupId));
      final boolean isForGroup = isForAllGroups() != groupInList;

      if (!isForGroup) {
        return false;
      }

      if (!isForAllGroups()) {
        return true;
      }

      // the remainder of the filtering options only apply when isForAllGroups()
      final boolean doTagsMatch = pGroupTags.containsAll(getTags());

      if (!doTagsMatch) {
        return false;
      }

      // If groupType is not specified then isForGroup is the result. Otherwise we have to
      // consider the groupType.
      return _groupType == null || _groupType == pGroupType;
    }

    return false;
  }

  /** Check if an alert config should be checked for a specific organization. */
  public boolean isForOrg(final ObjectId pOrgId) {
    if (isOrgAlertConfig()) {
      return getOrgId().equals(pOrgId);
    }
    return false;
  }

  public boolean isGlobalAlertConfig() {
    return _scope != null && _scope == EventScope.GLOBAL;
  }

  public boolean isSystemAlertConfig() {
    return _scope != null && _scope == EventScope.SYSTEM;
  }

  public boolean isGroupAlertConfig() {
    return _scope == null || _scope == EventScope.GROUP;
  }

  public boolean isOrgAlertConfig() {
    return _scope == null || _scope == EventScope.ORG;
  }

  public boolean isRealmAlertConfig() {
    return false;
  }

  public EventScope getScope() {
    return _scope;
  }

  public List<String> getTags() {
    return _tags != null ? Collections.unmodifiableList(_tags) : List.of();
  }

  /**
   * Is there a Group notification for this alert configuration? NOTE: this doesn't take a groupId
   * parameter, because you should only be able to add a single group notification for the group
   * that owns the alert config.
   */
  public boolean hasGroupNotification() {
    for (final Notification notif : getNotifications()) {
      if (notif instanceof GroupNotification) {
        return true;
      }
    }
    return false;
  }

  /** Is there a User notification for the specified username? */
  public boolean hasUserNotification(final String pUsername) {
    for (final Notification notif : getNotifications()) {
      if (notif instanceof UserNotification
          && ((UserNotification) notif).getUsername().equals(pUsername)) {
        return true;
      }
    }
    return false;
  }

  /** Is there a team notification for the specified teamId? */
  public boolean hasTeamNotification(final ObjectId pTeamId) {
    for (final Notification notif : getNotifications()) {
      if (notif instanceof TeamNotification
          && ((TeamNotification) notif).getTeamId().equals(pTeamId)) {
        return true;
      }
    }
    return false;
  }

  public boolean hasEscalationService() {
    return getNotifications().stream().anyMatch(Notification::isEscalationService);
  }

  // Implementation of Validation interface
  @Override
  public Map<String, Object> validate() {
    final Map<String, Object> errors = new HashMap<String, Object>();

    Validators.notNull(EVENT_TYPE_FIELD, getEventType(), errors);

    Validators.validCollection(MATCHERS_FIELD, getMatchers(), errors);

    Validators.sizeBounds(NOTIFY_FIELD, getNotifications(), 1, null, errors);
    Validators.validCollection(NOTIFY_FIELD, getNotifications(), errors);

    return errors;
  }

  public boolean shouldTrackBeforeOpeningAlert() {
    return getMinimumSendDelayInMinutes() > 0;
  }

  /**
   * Returns the smallest minimum, positive delay in minutes. If returned value is 0, then all
   * notification delays are zero.
   *
   * @return minimum positive delay in minutes
   */
  int getMinimumSendDelayInMinutes() {
    return getNotifications().stream()
        .mapToInt(Notification::getSendDelayInMinutes)
        .min()
        .orElse(0);
  }

  public boolean shouldOpenTrackingAlert(final Alert pAlert, final Date pNow) {
    return pAlert.getStatus() == TRACKING
        && pNow.after(DateUtils.addMinutes(pAlert.getCreatedAt(), getMinimumSendDelayInMinutes()));
  }

  @PrePersist
  public void updateTimestamps() {
    final Date now = new Date();
    if (_createdAt == null) {
      _createdAt = now;
    }
    _updatedAt = now;
  }

  /**
   * Determines whether or not the specified object meets all the matcher criteria.
   *
   * @param pObj object to test
   * @return true if the object is a match; false otherwise
   */
  public boolean matches(final Object pObj) {
    if (getMatchers() == null) {
      return true;
    }
    for (final Matcher matcher : getMatchers()) {
      if (!matcher.matches(pObj)) {
        return false;
      }
    }
    return true;
  }

  /**
   * Filters a list of objects for those that meet the matcher criteria.
   *
   * @param pList the list of objects to filter
   * @return a list of matching objects
   */
  public <T> List<T> findMatches(final Collection<T> pList) {
    final List<T> result = new ArrayList<>();
    for (final T obj : pList) {
      if (matches(obj)) {
        result.add(obj);
      }
    }
    return result;
  }

  /**
   * Filters a list of alert configurations by type.
   *
   * @param pAlertConfigs list of configurations to filter
   * @param pAlertConfigClass alert config type to match on
   * @param pObj object to match on
   */
  public static <T extends AlertConfig, U extends AlertConfig> List<U> filterByTypeAndTarget(
      final List<T> pAlertConfigs, final Class<U> pAlertConfigClass, final Object pObj) {
    final List<U> filtered = new ArrayList<>();
    for (final T config : pAlertConfigs) {
      if (config.getClass() == pAlertConfigClass && config.matches(pObj)) {
        filtered.add(pAlertConfigClass.cast(config));
      }
    }
    return Collections.unmodifiableList(filtered);
  }

  /**
   * Filters a list of alert configurations by event type for a specific target. So, given a list of
   * alert configs, it can find those of a specific host event type for which the host object
   * satisfies all the matcher\ criteria.
   *
   * @param pAlertConfigs list of configurations to filter
   * @param pEventType event type to match on
   * @param pObj object to match on
   */
  public static <T extends AlertConfig> List<T> filterByEventTypeAndTarget(
      final List<T> pAlertConfigs, final EventType pEventType, final Object pObj) {
    final List<T> filtered = new ArrayList<>();
    for (final T config : pAlertConfigs) {
      if (pEventType.equals(config.getEventType()) && config.matches(pObj)) {
        filtered.add(config);
      }
    }
    return Collections.unmodifiableList(filtered);
  }

  /**
   * Filters a list of alert configurations by EventType.
   *
   * @param pAlertConfigs list of configs to filter
   * @param pEventType type of event to look for
   * @return list of AlertConfigs of the specified type (or an empty list)
   */
  public static <T extends AlertConfig> List<T> filterByEventType(
      final List<T> pAlertConfigs, final EventType pEventType) {
    return filterByEventTypes(pAlertConfigs, Set.of(pEventType));
  }

  /**
   * Filters a list of alert configurations by EventType.
   *
   * @param pAlertConfigs list of configs to filter
   * @param pEventTypes type of events to look for
   * @return list of AlertConfigs of the specified type (or an empty list)
   */
  public static <T extends AlertConfig> List<T> filterByEventTypes(
      final List<T> pAlertConfigs, final Set<EventType> pEventTypes) {
    if (pAlertConfigs == null || pAlertConfigs.isEmpty()) {
      return Collections.emptyList();
    }
    final List<T> filtered = new ArrayList<>();
    for (final T config : pAlertConfigs) {
      if (pEventTypes.contains(config.getEventType())) {
        filtered.add(config);
      }
    }
    return Collections.unmodifiableList(filtered);
  }

  /**
   * Filter a list of alert configurations for a specific target.
   *
   * @param pAlertConfigs list of configs to filter
   * @param pObj type of event to look for
   * @return list of configs that match the specified target (or an empty list)
   */
  public static <T extends AlertConfig> List<T> filterByTarget(
      final List<T> pAlertConfigs, final Object pObj) {
    final List<T> filtered = new ArrayList<>();
    for (final T config : pAlertConfigs) {
      if (config.matches(pObj)) {
        filtered.add(config);
      }
    }
    return Collections.unmodifiableList(filtered);
  }

  public static Set<ObjectId> extractIds(final List<? extends AlertConfig> pAlertConfigs) {
    final Set<ObjectId> set = new HashSet<>();
    for (final AlertConfig config : pAlertConfigs) {
      set.add(config.getId());
    }
    return set;
  }

  public static List<AlertConfig> filterInformationalAlertConfig(
      final Collection<AlertConfig> pAlertConfigs) {
    return pAlertConfigs.stream()
        .filter(AlertConfig::isAlertableEventType)
        .collect(Collectors.toCollection(ArrayList::new));
  }

  /**
   * Builder for creating alert configuration objects.
   *
   * <p>AlertConfig definitions inheriting from this `Builder` class MUST implement a constructor of
   * the form: Builder(EventType, ObjectId).
   */
  @SuppressWarnings("unchecked")
  public abstract static class Builder {

    private ObjectId id;
    private final String typeName;
    private ObjectId groupId;
    private ObjectId orgId;
    private boolean enabled;
    private List<Matcher> matchers;
    private List<Notification> notifications;
    private Date createdAt;
    private Date updatedAt;
    private List<GroupInfoView> groups = new ArrayList<>();
    private boolean forAllGroups;
    private EventScope scope;
    private GroupType groupType;
    private final List<String> tags = new ArrayList<>();
    private AlertConfigSource source;
    private EventSeverity severityOverride;

    protected Builder(final AlertConfig pAlertConfig) {
      id = pAlertConfig.getId();
      typeName = pAlertConfig.getTypeName();
      groupId = pAlertConfig.getGroupId();
      orgId = pAlertConfig.getOrgId();
      enabled = pAlertConfig.isEnabled();
      matchers = pAlertConfig.getMatchers();
      notifications = pAlertConfig.getNotifications();
      createdAt = pAlertConfig.getCreatedAt();
      updatedAt = pAlertConfig.getUpdatedAt();
      groups.addAll(pAlertConfig.getGroups());
      forAllGroups = pAlertConfig.isForAllGroups();
      scope = pAlertConfig.getScope();
      groupType = pAlertConfig.getGroupType();
      tags.addAll(pAlertConfig.getTags());
      source = pAlertConfig.getSource();
      severityOverride = pAlertConfig.getSeverityOverride();
    }

    protected Builder(final String pTypeName, final ObjectId pId) {
      typeName = pTypeName;
      id = pId;
      enabled = true;
      createdAt = updatedAt = new Date();
      groups = new ArrayList<>();
      forAllGroups = false;
      source = DEFAULT;
    }

    public <T extends Builder> T id(final ObjectId pId) {
      id = pId;
      return (T) this;
    }

    public <T extends Builder> T createdAt(final Date pCreatedAt) {
      createdAt = pCreatedAt;
      return (T) this;
    }

    public <T extends Builder> T updatedAt(final Date pUpdatedAt) {
      updatedAt = pUpdatedAt;
      return (T) this;
    }

    public <T extends Builder> T groupId(final ObjectId pGroupId) {
      groupId = pGroupId;
      scope = EventScope.GROUP;
      return (T) this;
    }

    public <T extends Builder> T orgId(final ObjectId pOrgId) {
      orgId = pOrgId;
      if (groupId == null) {
        scope = EventScope.ORG;
      }
      return (T) this;
    }

    public <T extends Builder> T groupType(final GroupType pGroupType) {
      groupType = pGroupType;
      return (T) this;
    }

    public <T extends Builder> T atlasGroupId(final ObjectId groupId) {
      return groupId(groupId).groupType(GroupType.NDS);
    }

    public <T extends Builder> T enabled(final boolean pEnabled) {
      enabled = pEnabled;
      return (T) this;
    }

    public <T extends Builder> T matchers(final List<Matcher> pMatchers) {
      matchers = pMatchers;
      return (T) this;
    }

    public <T extends Builder> T notifications(final List<Notification> pNotifications) {
      notifications = pNotifications;
      return (T) this;
    }

    public <T extends Builder> T notification(final Notification pNotification) {
      notifications = Collections.singletonList(pNotification);
      return (T) this;
    }

    /**
     * Add a list of groups to be included or excluded for a global alert configuration.
     *
     * @param pGroups the list of groups to add
     * @see AlertConfig#getGroups
     */
    public void groups(final GroupInfoView... pGroups) {
      checkGlobalAlertConfigState();
      groups.addAll(Arrays.asList(pGroups));
      scope = EventScope.GLOBAL;
    }

    /**
     * Add a single group to be included or excluded for a global alert configuration.
     *
     * @param pGroup the group to add
     * @see AlertConfig#getGroups
     */
    public void addGroup(final GroupInfoView pGroup) {
      checkGlobalAlertConfigState();
      groups.add(pGroup);
      scope = EventScope.GLOBAL;
    }

    /**
     * Mark the alert configuration as applying to all groups. If this function is called, any
     * groups specified using {@link #groups} or {@link #addGroup} will be excluded, and the alert
     * will apply to all groups except those specified.
     *
     * @see AlertConfig#isForAllGroups
     */
    public <T extends Builder> T forAllGroups() {
      checkGlobalAlertConfigState();
      forAllGroups = true;
      scope = EventScope.GLOBAL;
      return (T) this;
    }

    public void scope(final EventScope pScope) {
      scope = pScope;
    }

    private void checkGlobalAlertConfigState() {
      if (groupId != null) {
        throw new IllegalStateException("Global alert cannot have specific group ID");
      }
    }

    public void severityOverride(final EventSeverity severityOverride) {
      this.severityOverride = severityOverride;
    }

    public void addTag(final String pTag) {
      tags.add(pTag);
    }

    public void clearTags() {
      tags.clear();
    }

    public void addTags(final List<String> pTags) {
      tags.addAll(pTags);
    }

    public <T extends Builder> T source(final AlertConfigSource pSource) {
      source = pSource;
      return (T) this;
    }

    public <T extends Builder> T groupNotification(
        final ObjectId pGroupId,
        final String pGroupName,
        final int pSendIntervalMinutes,
        final int pSendDelayMinutes) {
      return groupNotification(pGroupId, pGroupName, pSendIntervalMinutes, pSendDelayMinutes, null);
    }

    public <T extends Builder> T groupNotification(
        final ObjectId pGroupId,
        final String pGroupName,
        final int pSendIntervalMinutes,
        final int pSendDelayMinutes,
        final List<Role> pReceivingRoles) {
      final Notification notification =
          new GroupNotification(
              pGroupId,
              pGroupName,
              true,
              false,
              pSendIntervalMinutes,
              pSendDelayMinutes,
              pReceivingRoles);
      notification(notification);
      return (T) this;
    }

    public <T extends Builder> T orgNotification(
        final ObjectId pOrgId,
        final String pOrgName,
        final int pSendIntervalMinutes,
        final int pSendDelayMinutes) {
      return orgNotification(pOrgId, pOrgName, pSendIntervalMinutes, pSendDelayMinutes, null);
    }

    public <T extends Builder> T orgNotification(
        final ObjectId pOrgId,
        final String pOrgName,
        final int pSendIntervalMinutes,
        final int pSendDelayMinutes,
        final List<Role> pReceivingRoles) {
      final Notification notification =
          new OrgNotification(
              pOrgId,
              pOrgName,
              true,
              false,
              pSendIntervalMinutes,
              pSendDelayMinutes,
              pReceivingRoles);
      notification(notification);
      return (T) this;
    }

    public <T extends Builder> T adminNotification() {
      return adminNotification(0, 0);
    }

    public <T extends Builder> T adminNotification(
        final int pSendIntervalMinutes, final int pSendDelayMinutes) {
      final Notification notification =
          new AdminNotification(pSendIntervalMinutes, pSendDelayMinutes);
      return notification(notification);
    }

    public abstract AlertConfig build();
  }
}
