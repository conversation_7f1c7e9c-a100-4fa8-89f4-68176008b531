package com.xgen.cloud.atm.core._public.view;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.deployment._public.model.AuthMechanism;
import io.swagger.v3.oas.annotations.Hidden;
import java.util.List;

@Hidden
public class ImportDeploymentRequestView {

  @JsonProperty("seedHostport")
  private String seedHostport;

  @JsonProperty("requiredProcesses")
  private List<String> requiredProcesses;

  @JsonProperty("timeouts")
  private TimeoutsView timeouts;

  @JsonProperty("authMechanism")
  private AuthMechanism authMechanism;

  @JsonProperty("username")
  private String adminUsername;

  @JsonProperty("password")
  private String adminPassword;

  @JsonProperty("adminDb")
  private String adminDb;

  @JsonProperty("pemKeyFilePath")
  private String pemKeyFilePath;

  @JsonProperty("pemKeyFilePassword")
  private String pemKeyFilePassword;

  @JsonProperty("adminKerberosKeytab")
  private String adminKerberosKeytab;

  @JsonProperty("adminLdapGroupDn")
  private String adminLdapGroupDn;

  @JsonProperty("saslServiceName")
  private String saslServiceName;

  @JsonProperty("caFilePath")
  private String caPath;

  @JsonProperty("clusterCaFilePath")
  private String clusterCaPath;

  @JsonProperty("clientCertificateMode")
  private String clientCertificateMode;

  public ImportDeploymentRequestView() {}

  public AuthMechanism getAuthMechanism() {
    return authMechanism;
  }

  public void setAuthMechanism(AuthMechanism authMechanism) {
    this.authMechanism = authMechanism;
  }

  public String getAdminUsername() {
    return adminUsername;
  }

  public void setAdminUsername(String adminUsername) {
    this.adminUsername = adminUsername;
  }

  public String getAdminPassword() {
    return adminPassword;
  }

  public void setAdminPassword(String adminPassword) {
    this.adminPassword = adminPassword;
  }

  public String getSeedHostport() {
    return seedHostport;
  }

  public void setSeedHostport(String seedHostport) {
    this.seedHostport = seedHostport;
  }

  public List<String> getRequiredProcesses() {
    return requiredProcesses;
  }

  public void setRequiredProcesses(List<String> requiredProcesses) {
    this.requiredProcesses = requiredProcesses;
  }

  public String getPemKeyFilePath() {
    return pemKeyFilePath;
  }

  public void setPemKeyFilePath(String pemKeyFilePath) {
    this.pemKeyFilePath = pemKeyFilePath;
  }

  public String getPemKeyFilePassword() {
    return pemKeyFilePassword;
  }

  public void setPemKeyFilePassword(String pemKeyFilePassword) {
    this.pemKeyFilePassword = pemKeyFilePassword;
  }

  public String getCaPath() {
    return caPath;
  }

  public void setCaPath(String caPath) {
    this.caPath = caPath;
  }

  public String getClusterCaPath() {
    return clusterCaPath;
  }

  public void setClusterCaPath(String clusterCaPath) {
    this.clusterCaPath = clusterCaPath;
  }

  public String getClientCertificateMode() {
    return clientCertificateMode;
  }

  public void setClientCertificateMode(String clientCertificateMode) {
    this.clientCertificateMode = clientCertificateMode;
  }

  public String getAdminDb() {
    return adminDb;
  }

  public void setAdminDb(String adminDb) {
    this.adminDb = adminDb;
  }

  public String getAdminKerberosKeytab() {
    return adminKerberosKeytab;
  }

  public void setAdminKerberosKeytab(String adminKerberosKeytab) {
    this.adminKerberosKeytab = adminKerberosKeytab;
  }

  public String getAdminLdapGroupDn() {
    return adminLdapGroupDn;
  }

  public void setAdminLdapGroupDn(String adminLdapGroupDn) {
    this.adminLdapGroupDn = adminLdapGroupDn;
  }

  public String getSaslServiceName() {
    return saslServiceName;
  }

  public void setSaslServiceName(String saslServiceName) {
    this.saslServiceName = saslServiceName;
  }

  public TimeoutsView getTimeouts() {
    return timeouts;
  }

  public void setTimeouts(TimeoutsView timeouts) {
    this.timeouts = timeouts;
  }

  public static class TimeoutsView {
    @JsonProperty("seedHostConnectionSec")
    private Integer timeoutSeedHostConnectionSec;

    @JsonProperty("processesDiscoverySec")
    private Integer timeoutHostsDiscoverySec;

    @JsonProperty("automationImported")
    private Integer timeoutAutomationImported;

    @JsonProperty("goalStateSec")
    private Integer timeoutGoalStateSec;

    public Integer getTimeoutSeedHostConnectionSec() {
      return timeoutSeedHostConnectionSec;
    }

    public void setTimeoutSeedHostConnectionSec(Integer timeoutSeedHostConnectionSec) {
      this.timeoutSeedHostConnectionSec = timeoutSeedHostConnectionSec;
    }

    public Integer getTimeoutHostsDiscoverySec() {
      return timeoutHostsDiscoverySec;
    }

    public void setTimeoutHostsDiscoverySec(Integer timeoutHostsDiscoverySec) {
      this.timeoutHostsDiscoverySec = timeoutHostsDiscoverySec;
    }

    public Integer getTimeoutAutomationImported() {
      return timeoutAutomationImported;
    }

    public void setTimeoutAutomationImported(Integer timeoutAutomationImported) {
      this.timeoutAutomationImported = timeoutAutomationImported;
    }

    public Integer getTimeoutGoalStateSec() {
      return timeoutGoalStateSec;
    }

    public void setTimeoutGoalStateSec(Integer timeoutGoalStateSec) {
      this.timeoutGoalStateSec = timeoutGoalStateSec;
    }
  }
}
