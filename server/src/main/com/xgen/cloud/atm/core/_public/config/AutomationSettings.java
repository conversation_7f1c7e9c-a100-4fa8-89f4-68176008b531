package com.xgen.cloud.atm.core._public.config;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

@Singleton
public class AutomationSettings {
  private final AppSettings settings;

  public static final String AUTOMATION_IMPORT_DEPLOYMENTS_JOB_INTERVAL_SEC =
      "mms.automation.importDeployments.jobFrequencyIntervalSec";
  public static final String AUTOMATION_IMPORT_DEPLOYMENTS_RETENTION_DAYS =
      "mms.automation.importDeployments.retentionDays";
  public static final String AUTOMATION_IMPORT_DEPLOYMENTS_DEFAULT_TIMEOUT_SEED_HOST_CONNECTION =
      "mms.automation.importDeployments.defaultTimeoutSeedHostConnectionSec";
  public static final String AUTOMATION_IMPORT_DEPLOYMENTS_DEFAULT_TIMEOUT_DISCOVERY =
      "mms.automation.importDeployments.defaultTimeoutDiscoverySec";
  public static final String AUTOMATION_IMPORT_DEPLOYMENTS_DEFAULT_TIMEOUT_AUTOMATION_IMPORT =
      "mms.automation.importDeployments.defaultTimeoutAutomationImportSec";
  public static final String AUTOMATION_IMPORT_DEPLOYMENTS_DEFAULT_TIMEOUT_GOAL_STATE =
      "mms.automation.importDeployments.defaultTimeoutGoalStateSec";

  @Inject
  public AutomationSettings(AppSettings pSettings) {
    settings = pSettings;
  }

  public int getAutomationImportDeploymentTimeoutSeedHostConnectionSec() {
    return settings.getIntProp(
        AUTOMATION_IMPORT_DEPLOYMENTS_DEFAULT_TIMEOUT_SEED_HOST_CONNECTION, 2 * 60);
  }

  public int getAutomationImportDeploymentTimeoutDiscoverySec() {
    return settings.getIntProp(AUTOMATION_IMPORT_DEPLOYMENTS_DEFAULT_TIMEOUT_DISCOVERY, 5 * 60);
  }

  public int getAutomationImportDeploymentTimeoutAutomationImportSec() {
    return settings.getIntProp(
        AUTOMATION_IMPORT_DEPLOYMENTS_DEFAULT_TIMEOUT_AUTOMATION_IMPORT, 5 * 60);
  }

  public int getAutomationImportDeploymentTimeoutGoalStateSec() {
    return settings.getIntProp(AUTOMATION_IMPORT_DEPLOYMENTS_DEFAULT_TIMEOUT_GOAL_STATE, 10 * 60);
  }

  public int getAutomationImportDeploymentJobIntervalSec() {
    return settings.getIntProp(AUTOMATION_IMPORT_DEPLOYMENTS_JOB_INTERVAL_SEC, 10);
  }

  public int getAutomationImportDeploymentRetentionDays() {
    return settings.getIntProp(AUTOMATION_IMPORT_DEPLOYMENTS_RETENTION_DAYS, 90);
  }
}
