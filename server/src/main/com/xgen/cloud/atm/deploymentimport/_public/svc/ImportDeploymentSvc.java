package com.xgen.cloud.atm.deploymentimport._public.svc;

import static com.xgen.cloud.atm.core._private.dao.ImportDeploymentDao.Mutators.deploymentItemId;
import static com.xgen.cloud.atm.core._private.dao.ImportDeploymentDao.Mutators.errorMessage;
import static com.xgen.cloud.atm.core._private.dao.ImportDeploymentDao.Mutators.hostMonitoringId;
import static com.xgen.cloud.atm.core._private.dao.ImportDeploymentDao.Mutators.importRequestId;
import static com.xgen.cloud.atm.core._private.dao.ImportDeploymentDao.Mutators.state;
import static com.xgen.cloud.atm.core._public.model.ImportDeployment.State.AWAIT_SEED_HOST_CONNECTED;
import static com.xgen.cloud.common.util._public.util.BaseHostUtils.extractHostname;
import static com.xgen.cloud.common.util._public.util.BaseHostUtils.extractPortIfExists;

import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import com.mongodb.ReadPreference;
import com.xgen.cloud.atm.core._private.dao.ImportDeploymentDao;
import com.xgen.cloud.atm.core._public.config.AutomationSettings;
import com.xgen.cloud.atm.core._public.model.DeploymentItemType;
import com.xgen.cloud.atm.core._public.model.ImportDeployment;
import com.xgen.cloud.atm.core._public.model.ImportDeployment.State;
import com.xgen.cloud.atm.core._public.model.LastAgentStatus;
import com.xgen.cloud.atm.core._public.svc.AuthAndTlsSettingsSvc;
import com.xgen.cloud.atm.core._public.svc.DeploymentBuilderSvc;
import com.xgen.cloud.atm.core._public.svc.DeploymentStatusSvc;
import com.xgen.cloud.atm.core._public.svc.LastAgentStatusSvc;
import com.xgen.cloud.atm.core._public.view.AgentAuthAndTlsSettingsView;
import com.xgen.cloud.atm.core._public.view.AuthAndTlsSettingsView;
import com.xgen.cloud.atm.core._public.view.ImportDeploymentRequestView;
import com.xgen.cloud.atm.core._public.view.TlsSettingsView;
import com.xgen.cloud.atm.deploymentimport._public.model.ImportRequest;
import com.xgen.cloud.atm.deploymentimport._public.view.ImportConflictResolutionView;
import com.xgen.cloud.atm.deploymentimport._public.view.ImportMergeConflictsView;
import com.xgen.cloud.atm.publish._public.svc.AutomationConfigPublishingSvc;
import com.xgen.cloud.common.auditInfoHelper._public.helper.AuditInfoHelpers;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.deployment._public.model.AuthMechanism;
import com.xgen.cloud.deployment._public.model.AutomationConfig;
import com.xgen.cloud.deployment._public.model.Deployment;
import com.xgen.cloud.deployment._public.model.TLS;
import com.xgen.cloud.deployment._public.model.ValidationException;
import com.xgen.cloud.group._private.dao.GroupDao;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.monitoring.lifecycle._public.svc.HostClusterLifecycleSvc;
import com.xgen.cloud.monitoring.topology._public.model.Host;
import com.xgen.cloud.monitoring.topology._public.model.HostType;
import com.xgen.cloud.monitoring.topology._public.model.PartialHost;
import com.xgen.cloud.monitoring.topology._public.model.errors.HostException;
import com.xgen.cloud.monitoring.topology._public.svc.CanonicalHostSvc;
import com.xgen.cloud.monitoring.topology._public.svc.HostSvc;
import com.xgen.cloud.organization._private.dao.OrganizationDao;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.svc.core.model.api.FormResponse;
import io.prometheus.client.Counter;
import io.prometheus.client.Gauge;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeoutException;
import java.util.function.BiFunction;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class ImportDeploymentSvc {

  private static final Logger LOG = LoggerFactory.getLogger(ImportDeploymentSvc.class);

  private static final EnumSet<AuthMechanism> AUTH_MECHANISMS_SUPPORTING_USERNAME =
      EnumSet.of(
          AuthMechanism.MONGODB_CR,
          AuthMechanism.SCRAM_SHA_256,
          AuthMechanism.SCRAM_SHA_1,
          AuthMechanism.PLAIN);
  private static final EnumSet<AuthMechanism> AUTH_MECHANISMS_NOT_REQUIRING_EXTERNAL_ADMINDB =
      EnumSet.of(AuthMechanism.MONGODB_CR, AuthMechanism.SCRAM_SHA_256, AuthMechanism.SCRAM_SHA_1);

  private final AutomationSettings automationSettings;
  private final AutomationConfigPublishingSvc automationConfigPublishingSvc;
  private final ImportDeploymentDao importDeploymentDao;
  private final HostClusterLifecycleSvc hostClusterLifecycleSvc;
  private final GroupDao groupDao;
  private final UserSvc userSvc;
  private final OrganizationDao organizationDao;
  private final HostSvc hostSvc;
  private final CanonicalHostSvc canonicalHostSvc;
  private final ImportRequestSvc importRequestSvc;
  private final DeploymentDiscoverySvc deploymentDiscoverySvc;
  private final DeploymentBuilderSvc deploymentBuilderSvc;
  private final LastAgentStatusSvc lastAgentStatusSvc;
  private final PerGroupLockManager perGroupLockManager;
  private final AuthAndTlsSettingsSvc authAndTlsSettingsSvc;

  private static final Gauge REQUESTS_ACTIVE_GAUGE =
      Gauge.build()
          .name("mms_automation_importrequests_active")
          .help("The number of import deployment requests currently in non-finalized state")
          .register();
  private static final Counter REQUESTS_PRUNED_COUNTER =
      Counter.build()
          .name("mms_automation_importrequests_pruned")
          .help("Total number of import deployment requests pruned")
          .register();
  private static final Counter REQUESTS_COMPLETED_COUNTER =
      Counter.build()
          .name("mms_automation_importrequests_completed")
          .help("Total number of import deployment requests completed")
          .labelNames("state")
          .register();
  private static final Counter REQUESTS_CREATED_COUNTER =
      Counter.build()
          .name("mms_automation_importrequests_created")
          .help("Total number of import deployment requests created")
          .register();
  private static final Counter JOB_RUNS_COUNTER =
      Counter.build()
          .name("mms_automation_importrequests_jobruns")
          .help("Total automation import deployment jobs running")
          .register();

  @Inject
  public ImportDeploymentSvc(
      final AutomationSettings automationSettings,
      final AutomationConfigPublishingSvc automationConfigPublishingSvc,
      final ImportDeploymentDao importDeploymentRequestDao,
      final HostClusterLifecycleSvc hostClusterLifecycleSvc,
      final GroupDao groupDao,
      final UserSvc userSvc,
      final OrganizationDao organizationDao,
      final HostSvc hostSvc,
      final CanonicalHostSvc canonicalHostSvc,
      final ImportRequestSvc importRequestSvc,
      final DeploymentDiscoverySvc deploymentDiscoverySvc,
      final DeploymentBuilderSvc deploymentBuilderSvc,
      final LastAgentStatusSvc lastAgentStatusSvc,
      final PerGroupLockManager perGroupLockManager,
      final AuthAndTlsSettingsSvc authAndTlsSettingsSvc) {
    this.automationSettings = automationSettings;
    this.automationConfigPublishingSvc = automationConfigPublishingSvc;
    this.importDeploymentDao = importDeploymentRequestDao;
    this.hostClusterLifecycleSvc = hostClusterLifecycleSvc;
    this.groupDao = groupDao;
    this.userSvc = userSvc;
    this.organizationDao = organizationDao;
    this.hostSvc = hostSvc;
    this.canonicalHostSvc = canonicalHostSvc;
    this.importRequestSvc = importRequestSvc;
    this.deploymentDiscoverySvc = deploymentDiscoverySvc;
    this.deploymentBuilderSvc = deploymentBuilderSvc;
    this.lastAgentStatusSvc = lastAgentStatusSvc;
    this.perGroupLockManager = perGroupLockManager;
    this.authAndTlsSettingsSvc = authAndTlsSettingsSvc;
  }

  public void handleWork() {
    LOG.info("Start handleWork for import deployments");
    JOB_RUNS_COUNTER.inc();

    final List<ImportDeployment> requests = importDeploymentDao.findAllActiveImportDeployments();
    REQUESTS_ACTIVE_GAUGE.set(requests.size());
    processImportRequests(requests);
  }

  public void handleWork(final ObjectId groupId) {
    final List<ImportDeployment> requests =
        importDeploymentDao.findActiveImportDeploymentsByGroupId(groupId);
    processImportRequests(requests);
  }

  public List<ImportDeployment> getRequests(final ObjectId groupId) {
    return importDeploymentDao.findByGroupId(groupId);
  }

  public Optional<ImportDeployment> getRequest(final ObjectId requestId) {
    return importDeploymentDao.find(requestId);
  }

  public void submitImportDeploymentRequest(
      final Group group, final AppUser appUser, final ImportDeploymentRequestView importRequest)
      throws SvcException {

    final Organization org = organizationDao.findById(group.getId());
    final AutomationConfig publishedConfig =
        getAutomationConfig(group.getId(), appUser, org, group);

    final Deployment deployment =
        deploymentBuilderSvc.buildDeployment(group, publishedConfig.getDeployment());

    ImportDeployment importDeployment = createImportDeploymentRecord(importRequest, group, appUser);

    validateRequest(importRequest);
    validateAgentWithMonitoringIsUp(group, deployment);
    validateConflictingProcessRequests(importRequest, group);
    validateTlsAuth(importDeployment, publishedConfig);

    // The importDeployment implicitly starts with PENDING state.
    importDeploymentDao.insert(importDeployment);

    REQUESTS_CREATED_COUNTER.inc();
  }

  public void deleteRequest(final ObjectId pId) {
    LOG.debug("Deleting import request with id: {}", pId);
    boolean delete = importDeploymentDao.delete(pId);
    if (!delete) {
      LOG.error("Attempted to delete import request with id: {}, but it wasn't found", pId);
    }
  }

  public void cancelImportRequest(final String requestId, final Group group) {
    final Optional<ImportDeployment> maybeImportDeployment =
        importDeploymentDao.find(new ObjectId(requestId));

    final ImportDeployment importDeployment =
        maybeImportDeployment.orElseThrow(
            () ->
                new IllegalStateException(
                    String.format("ImportDeployment with Id=%s not found", requestId)));

    final State state = importDeployment.state();
    if (!ImportDeployment.IN_PROGRESS_STATES.contains(state)) {
      LOG.debug(
          "ImportDeployment's state is not one of listed in IN_PROGRESS_STATES: {}. Can't cancel",
          state);
      throw new IllegalStateException("ImportDeployment is in finished state. Can't cancel");
    }

    importDeploymentDao.updateSelectively(importDeployment.id(), state(State.CANCELLED));

    cleanupInternalProcesses(importDeployment, group);
  }

  public void cleanupImportRequest(String requestId, Group group) {
    final Optional<ImportDeployment> maybeImportDeployment =
        importDeploymentDao.find(new ObjectId(requestId));

    final ImportDeployment importDeployment =
        maybeImportDeployment.orElseThrow(
            () ->
                new IllegalStateException(
                    String.format("ImportDeployment with Id=%s not found", requestId)));

    final State state = importDeployment.state();
    if (state != State.FAILED) {
      LOG.debug("ImportDeployment's state is not in FAILED state: {}. Can't cleanup", state);
      throw new IllegalStateException("ImportDeployment is not in FAILED state. Can't cleanup");
    }

    cleanupInternalProcesses(importDeployment, group);
  }

  public void pruneImportDeploymentRequests() {
    final List<ImportDeployment> requests = importDeploymentDao.findAll();

    final int ttlDays = automationSettings.getAutomationImportDeploymentRetentionDays();

    // Clean records in terminal state and older than 90 days
    List<ObjectId> ids =
        requests.stream()
            .filter(ImportDeployment::isFinalized)
            .filter(
                record ->
                    ChronoUnit.DAYS.between(lastTouchedAt(record).toInstant(), Instant.now())
                        > ttlDays)
            .map(ImportDeployment::id)
            .toList();

    importDeploymentDao.deleteBulk(ids);
    REQUESTS_PRUNED_COUNTER.inc(ids.size());
  }

  private void cleanupInternalProcesses(
      final ImportDeployment importDeployment, final Group group) {
    // Remove host from monitoring
    if (importDeployment.hostId() != null) {
      try {
        hostClusterLifecycleSvc.deleteHost(
            importDeployment.hostId(), group.getId(), importDeployment.seedHostport());
      } catch (HostException e) {
        throw new RuntimeException(e);
      }
    }

    // Remove import request
    if (importDeployment.importRequestId() != null) {
      importRequestSvc.deleteRequest(group, importDeployment.importRequestId());
    }
  }

  private void processImportRequests(final List<ImportDeployment> requests) {
    Map<ObjectId, ImportDeployment> inProgressAndGrouped =
        requests.stream()
            // These should be filtered and sorted on database side
            .filter((req) -> !req.isFinalized())
            .collect(
                Collectors.toMap(
                    ImportDeployment::groupId,
                    req -> req,
                    (first, second) -> first // keep the oldest record first
                    ));

    inProgressAndGrouped.forEach(
        (groupId, importDeployment) -> {
          LOG.info("Processing group {}: {}", groupId, importDeployment);

          perGroupLockManager.ensureOnePerGroupOrLock(
              groupId.toString(),
              () -> {
                try {
                  LOG.info("Item is in state={}", importDeployment.state());
                  switch (importDeployment.state()) {
                    case PENDING:
                      processPending(importDeployment);
                      break;
                    case AWAIT_SEED_HOST_CONNECTED:
                      checkTimeout(
                          importDeployment,
                          importDeployment.timeouts().timeoutSeedHostConnectionSec(),
                          automationSettings
                              ::getAutomationImportDeploymentTimeoutSeedHostConnectionSec);
                      processAwaitSeedHostConnected(importDeployment);
                      break;
                    case AWAIT_HOSTS_DISCOVERED:
                      checkTimeout(
                          importDeployment,
                          importDeployment.timeouts().timeoutProcessesDiscoverySec(),
                          automationSettings::getAutomationImportDeploymentTimeoutDiscoverySec);
                      processAwaitHostsDiscovered(importDeployment);
                      break;
                    case AWAIT_AUTOMATION_IMPORTED:
                      checkTimeout(
                          importDeployment,
                          importDeployment.timeouts().timeoutAutomationImported(),
                          automationSettings
                              ::getAutomationImportDeploymentTimeoutAutomationImportSec);
                      processAwaitAutomationImported(importDeployment);
                      break;
                    case AWAIT_GOAL_STATE:
                      checkTimeout(
                          importDeployment,
                          importDeployment.timeouts().timeoutGoalStateSec(),
                          automationSettings::getAutomationImportDeploymentTimeoutGoalStateSec);
                      processAwaitGoalState(importDeployment);
                      break;
                    case SUCCESS:
                    case FAILED:
                    case CANCELLED:
                      throw new IllegalStateException(
                          "At this stage we shouldn't see records with state: "
                              + importDeployment.state());
                    default:
                      throw new IllegalStateException("Unknown state: " + importDeployment.state());
                  }
                } catch (SvcException e) {
                  LOG.error("Failed to process", e);
                  throw new RuntimeException(e);
                } catch (TimeoutException e) {
                  importDeploymentDao.updateSelectively(
                      importDeployment.id(),
                      state(State.FAILED),
                      errorMessage("Operation timeouted on step: " + importDeployment.state()));
                  REQUESTS_COMPLETED_COUNTER.labels(State.FAILED.name()).inc();
                }
              });
        });
  }

  private void processPending(final ImportDeployment importDeployment) throws SvcException {
    try {
      ObjectId groupId = importDeployment.groupId();
      ObjectId userId = importDeployment.userId();
      final Organization org = organizationDao.findById(groupId);

      final Group group = groupDao.findById(groupId);
      final AppUser user = userSvc.findById(userId);

      final AutomationConfig config =
          automationConfigPublishingSvc.findCurrentOrEmpty(groupId, user.getId());
      automationConfigPublishingSvc.ensureDefaultAgentTemplates(config, false);

      boolean isSslEnabled = StringUtils.isNotBlank(importDeployment.caPath());
      boolean requiresAuthUpdate =
          isSslEnabled
              || !AUTH_MECHANISMS_NOT_REQUIRING_EXTERNAL_ADMINDB.contains(
                  importDeployment.authMechanism());

      if (requiresAuthUpdate) {
        final AuthAndTlsSettingsView authAndTlsSettings =
            authAndTlsSettingsSvc.getAuthAndTlsSettings(group, user, config);

        final AgentAuthAndTlsSettingsView agentAuth =
            new AgentAuthAndTlsSettingsView.Builder()
                .pemKeyFilePath(importDeployment.adminPemKeyPath())
                .kerberosKeytabPath(importDeployment.adminKerberosKeytab())
                .ldapGroupDn(importDeployment.adminLdapGroupDn())
                .pemKeyFilePassword(importDeployment.adminPemKeyPwd())
                .username(importDeployment.adminUsername())
                .password(importDeployment.adminPassword())
                .build();

        Set<AuthMechanism> merged =
            new HashSet<AuthMechanism>(authAndTlsSettings.getAgentAuthMechanisms());
        merged.add(importDeployment.authMechanism());

        TlsSettingsView tlsSettings = authAndTlsSettings.getTlsSettings();

        TlsSettingsView updatededTlsSettings =
            new TlsSettingsView.Builder()
                .caFilePath(
                    StringUtils.firstNonBlank(
                        importDeployment.caPath(), tlsSettings.getCaFilePath()))
                .clusterCAFilePath(
                    StringUtils.firstNonBlank(
                        importDeployment.clusterCaPath(), tlsSettings.getClusterCAFilePath()))
                .clientCertificateMode(
                    importDeployment.authMechanism() == AuthMechanism.MONGODB_X509
                        ? TLS.ClientCertificateMode.REQUIRE
                        : tlsSettings.getClientCertificateMode())
                .build();

        final AuthAndTlsSettingsView settingsWithSsl =
            new AuthAndTlsSettingsView.Builder()
                .agentAuthMechanisms(merged.stream().toList())
                .deploymentAuthMechanisms(merged.stream().toList())
                .kerberosServiceName(importDeployment.saslServiceName())
                .nativeLdapSettings(authAndTlsSettings.getNativeLdapSettings())
                .tlsSettings(updatededTlsSettings)
                .tlsProcessSettings(authAndTlsSettings.getTlsProcessSettings())
                .automationAgentSettings(agentAuth)
                .backupAgentSettings(agentAuth)
                .monitoringAgentSettings(agentAuth)
                .isAuthoritativeSet(false)
                .build();
        authAndTlsSettingsSvc.updateAuthAndTlsSettings(config, settingsWithSsl);

        automationConfigPublishingSvc.saveDraft(config, user, org, group);
        automationConfigPublishingSvc.publish(org, group, user);
      }

      final Integer port = extractPortIfExists(importDeployment.seedHostport());
      if (port == null) {
        throw new IllegalStateException(
            String.format("Can't extract port from '%s'", importDeployment.seedHostport()));
      }

      var isTlsEnabled = importDeployment.adminPemKeyPath() != null;

      // Start monitoring the seed node
      final FormResponse hostInfo =
          hostClusterLifecycleSvc.addHost(
              group,
              new PartialHost(
                      group.getId(),
                      extractHostname(importDeployment.seedHostport()),
                      port,
                      group.getEnableAllHostProfilers(),
                      group.getSuppressMongosAutoDiscovery())
                  .setAuthMechanism(toMonitoringAuthMechanism(importDeployment.authMechanism()))
                  .setUsername(
                      AUTH_MECHANISMS_SUPPORTING_USERNAME.contains(importDeployment.authMechanism())
                          ? StringUtils.trimToNull(importDeployment.adminUsername())
                          : null)
                  .setPassword(StringUtils.trimToNull(importDeployment.adminPassword()))
                  .setSslEnabled(isTlsEnabled)
                  .setEnableProfiler(group.getEnableAllHostProfilers()),
              AuditInfoHelpers.fromIngestion());

      if (hostInfo.hasError()) {
        LOG.error("Can't add host to monitoring: {}", hostInfo.getErrorCode());
        throw new RuntimeException("Can't add host to monitoring: " + hostInfo.getErrorCode());
      }

      final String hostMonitoringId = hostInfo.getNewObjId();

      importDeploymentDao.updateSelectively(
          importDeployment.id(),
          state(AWAIT_SEED_HOST_CONNECTED),
          hostMonitoringId(hostMonitoringId));
    } catch (Exception e) {
      importDeploymentDao.updateSelectively(
          importDeployment.id(),
          state(State.FAILED),
          errorMessage("Import process failed transitioning to AWAIT_SEED_HOST_CONNECTED state"));
      REQUESTS_COMPLETED_COUNTER.labels(State.FAILED.name()).inc();
      LOG.error("Failed to process PENDING state", e);
      throw e;
    }
  }

  private void processAwaitSeedHostConnected(final ImportDeployment importDeployment) {
    try {
      String hostId = importDeployment.hostId();
      Preconditions.checkState(hostId != null, "importDeployment.hostId is null");
      Optional<HostStatusInfo> addedHostStatus =
          getAddedHostStatus(importDeployment.groupId(), hostId);

      if (addedHostStatus.isPresent() && addedHostStatus.get().lastPing() != null) {
        importDeploymentDao.updateSelectively(
            importDeployment.id(),
            state(State.AWAIT_HOSTS_DISCOVERED),
            deploymentItemId(addedHostStatus.get().deploymentItemId()));
      }
    } catch (Exception e) {
      importDeploymentDao.updateSelectively(
          importDeployment.id(),
          state(State.FAILED),
          errorMessage("Import process failed transitioning to AWAIT_HOSTS_DISCOVERED state"));
      REQUESTS_COMPLETED_COUNTER.labels(State.FAILED.name()).inc();
      throw e;
    }
  }

  private void processAwaitHostsDiscovered(final ImportDeployment importDeployment)
      throws SvcException {
    final ObjectId groupId = importDeployment.groupId();
    final ObjectId userId = importDeployment.userId();
    final Organization org = organizationDao.findById(groupId);

    final Group group = groupDao.findById(groupId);
    final AppUser user = userSvc.findById(userId);

    final AutomationConfig config = getAutomationConfig(groupId, user, org, group);

    final Deployment deploymentWithMonitoring =
        deploymentBuilderSvc.buildDeployment(group, config.getDeployment());

    // Check discovered processes
    final Set<String> discoveredProcesses =
        deploymentDiscoverySvc.getDiscoveredHostnamePorts(group, deploymentWithMonitoring);
    LOG.debug("Discovered processes = {}", discoveredProcesses);

    boolean allDiscovered =
        importDeployment.requiredProcesses().stream().allMatch(discoveredProcesses::contains);

    try {
      if (allDiscovered) {
        // Ensure the monitoring and backup agent are still alive
        validateAgentWithMonitoringIsUp(group, deploymentWithMonitoring);

        Optional<ItemInfo> itemById =
            getItemById(deploymentWithMonitoring, importDeployment.deploymentItemId());
        if (itemById.isPresent()) {
          final ImportRequest importRequest = new ImportRequest();

          importRequest.setGroupId(groupId);

          importRequest.setDeploymentItemId(importDeployment.deploymentItemId());
          importRequest.setDeploymentItemType(itemById.get().type());
          importRequest.setDeploymentItemName(itemById.get().name());

          importRequest.setAuthMechanism(importDeployment.authMechanism());
          if (AUTH_MECHANISMS_SUPPORTING_USERNAME.contains(importDeployment.authMechanism())) {
            importRequest.setAdminUsername(importDeployment.adminUsername());
            importRequest.setSaslServiceName(importDeployment.saslServiceName());
            importRequest.setAdminPemKeyPath(importDeployment.adminPemKeyPath());
            importRequest.setCaPath(importDeployment.caPath());
          }
          importRequest.setAdminPassword(importDeployment.adminPassword());
          // Originally it was only about MNDODB_CR and SHA256, but seems like we have to do same
          // for SHA1 too.
          importRequest.setAdminDb(
              StringUtils.firstNonBlank(
                  importDeployment.adminDb(),
                  AUTH_MECHANISMS_NOT_REQUIRING_EXTERNAL_ADMINDB.contains(
                          importDeployment.authMechanism())
                      ? "admin"
                      : "$external"));

          importRequest.setAdminLdapGroupDN(importDeployment.adminLdapGroupDn());
          importRequest.setEnforceProcessesInManualMode(false);
          importRequest.setSyncUsersAndRoles(true);
          importRequest.setAdminPemKeyPwd(importDeployment.adminPemKeyPwd());

          importRequestSvc.submitRequestForItem(group, user, importRequest);

          importDeploymentDao.updateSelectively(
              importDeployment.id(),
              state(State.AWAIT_AUTOMATION_IMPORTED),
              importRequestId(importRequest.getId()));
        } else {
          LOG.error(
              "There is no item in deployment found by id: {}",
              importDeployment.deploymentItemId());
        }
      } else {
        LOG.debug("Not all processes discovered. Keep waiting.");
      }
    } catch (Exception e) {
      importDeploymentDao.updateSelectively(
          importDeployment.id(),
          state(State.FAILED),
          errorMessage("Import process failed transitioning to AWAIT_AUTOMATION_IMPORTED state"));
      REQUESTS_COMPLETED_COUNTER.labels(State.FAILED.name()).inc();
      throw e;
    }
  }

  private void processAwaitAutomationImported(final ImportDeployment importDeployment)
      throws SvcException {
    ObjectId groupId = importDeployment.groupId();
    ObjectId userId = importDeployment.userId();
    final Organization org = organizationDao.findById(groupId);

    final Group group = groupDao.findById(groupId);
    final AppUser user = userSvc.findById(userId);

    try {
      final ObjectId importRqId = importDeployment.importRequestId();
      final ImportRequest importRequest = importRequestSvc.getImportRequest(importRqId);
      if (ImportRequestSvc.isImportComplete(importRequest)) {
        LOG.debug("Import has completed.");

        ImportMergeConflictsView mergeConflicts =
            importRequestSvc.getMergeConflicts(group, user, importRqId);
        if (!mergeConflicts.getRoleConflicts().isEmpty()
            || !mergeConflicts.getUserConflicts().isEmpty()) {
          throw new RuntimeException("There are merge conflicts. Can't import");
        }

        importRequestSvc.completeImportForItem(
            org, group, user, importRqId, new ImportConflictResolutionView());
        importRequestSvc.deleteRequest(group, importRqId);

        automationConfigPublishingSvc.publish(org, group, user);
        importDeploymentDao.updateSelectively(importDeployment.id(), state(State.AWAIT_GOAL_STATE));
      } else {
        LOG.debug("Import request not finished yet. Keep waiting.");
      }
    } catch (Exception e) {
      LOG.error("Failed trying to move to AWAIT_GOAL_STATE", e);
      importDeploymentDao.updateSelectively(
          importDeployment.id(),
          state(State.FAILED),
          errorMessage("Import process failed transitioning to AWAIT_GOAL_STATE state"));
      REQUESTS_COMPLETED_COUNTER.labels(State.FAILED.name()).inc();
      throw e;
    }
  }

  private void processAwaitGoalState(ImportDeployment importDeployment) {
    final ObjectId groupId = importDeployment.groupId();
    if (isInGoalState(groupId)) {
      importDeploymentDao.updateSelectively(importDeployment.id(), state(State.SUCCESS));
      REQUESTS_COMPLETED_COUNTER.labels(State.SUCCESS.name()).inc();
    }
  }

  private void checkTimeout(
      final ImportDeployment importDeployment, int timeoutSeconds, Provider<Integer> provider)
      throws TimeoutException {

    Date baseTimestamp = lastTouchedAt(importDeployment);

    if (timeoutSeconds == 0) {
      timeoutSeconds = provider.get();
    }

    long secondsBetween = ChronoUnit.SECONDS.between(baseTimestamp.toInstant(), Instant.now());

    if (secondsBetween > timeoutSeconds) {
      LOG.error(
          "Deployment Import job {} has exceeded the timeout {}s for state {}",
          importDeployment.id(),
          timeoutSeconds,
          importDeployment.state());
      throw new TimeoutException();
    }
  }

  private AuthMechanism toAutomationAuthMechanism(
      final com.xgen.cloud.monitoring.topology._public.model.auth.AuthMechanism authMechanism) {
    return switch (authMechanism) {
      case PLAIN -> AuthMechanism.PLAIN;
      case GSSAPI -> AuthMechanism.GSSAPI;
      case MONGODB_CR -> AuthMechanism.MONGODB_CR;
      case MONGODB_X509 -> AuthMechanism.MONGODB_X509;
    };
  }

  private com.xgen.cloud.monitoring.topology._public.model.auth.AuthMechanism
      toMonitoringAuthMechanism(final AuthMechanism authMechanism) {
    return switch (authMechanism) {
      case PLAIN -> com.xgen.cloud.monitoring.topology._public.model.auth.AuthMechanism.PLAIN;
      case SCRAM_SHA_1 ->
          com.xgen.cloud.monitoring.topology._public.model.auth.AuthMechanism.MONGODB_CR;
      case SCRAM_SHA_256 ->
          com.xgen.cloud.monitoring.topology._public.model.auth.AuthMechanism.MONGODB_CR;
      case GSSAPI -> com.xgen.cloud.monitoring.topology._public.model.auth.AuthMechanism.GSSAPI;
      case MONGODB_CR ->
          com.xgen.cloud.monitoring.topology._public.model.auth.AuthMechanism.MONGODB_CR;
      case MONGODB_X509 ->
          com.xgen.cloud.monitoring.topology._public.model.auth.AuthMechanism.MONGODB_X509;
      case MONGODB_AWS -> null;
      case MONGODB_OIDC -> null;
    };
  }

  private AutomationConfig getAutomationConfig(
      final ObjectId groupId, final AppUser user, final Organization org, final Group group) {
    final AutomationConfig config =
        automationConfigPublishingSvc.findCurrentOrEmpty(groupId, user.getId());

    automationConfigPublishingSvc.setInUseDeploymentVersions(config, org, group);
    automationConfigPublishingSvc.ensureDefaultAgentTemplates(config, false);

    return config;
  }

  @SafeVarargs
  @SuppressWarnings("varargs")
  public static <T> Optional<T> firstPresent(final Supplier<Optional<T>>... fns) {
    return Arrays.stream(fns)
        .map(Supplier::get)
        .filter(Optional::isPresent)
        .map(Optional::get)
        .findFirst();
  }

  public record ItemInfo(DeploymentItemType type, String name) {}

  private Optional<ItemInfo> getItemById(
      final Deployment deployment, final String deploymentItemId) {
    return firstPresent(
        () ->
            deployment.getSharding().stream()
                .filter(
                    shardedCluster ->
                        Objects.equals(
                            deploymentItemId,
                            shardedCluster.getMonitoringState().getClusterId().toString()))
                .map(
                    shardedCluster ->
                        new ItemInfo(DeploymentItemType.CLUSTER, shardedCluster.getName()))
                .findFirst(),
        () ->
            deployment.getReplicaSets().stream()
                .filter(
                    replicaSet ->
                        Objects.equals(
                            deploymentItemId,
                            replicaSet.getMonitoringState().getClusterId().toString()))
                .map(replicaSet -> new ItemInfo(DeploymentItemType.REPLICA_SET, replicaSet.getId()))
                .findFirst(),
        () ->
            deployment.getProcesses().stream()
                .filter(
                    process ->
                        Objects.equals(process.getMonitoringState().getHostId(), deploymentItemId))
                .map(process -> new ItemInfo(DeploymentItemType.STANDALONE, process.getName()))
                .findFirst());
  }

  public boolean isInGoalState(final ObjectId pGroupId) {
    final AutomationConfig config = automationConfigPublishingSvc.findPublishedOrEmpty(pGroupId);
    final List<LastAgentStatus> statuses =
        lastAgentStatusSvc.findForGroupIdAndHosts(
            pGroupId, config.getDeployment().getManagedHostnames());

    final boolean isInGoalState = DeploymentStatusSvc.isInGoalState(config, statuses);
    if (isInGoalState) {
      LOG.debug("All agents in goal state");
      return true;
    } else {
      LOG.debug("All agents have not yet reached goal state. Job will wait and retry.");
      return false;
    }
  }

  public record HostStatusInfo(Date lastPing, String deploymentItemId) {}

  private Optional<HostStatusInfo> getAddedHostStatus(final ObjectId groupId, final String hostId) {
    final Host host = hostSvc.findHostById(hostId, groupId);
    final String preferredId;

    Date lastPing = null;
    String deploymentItemId = null;

    // Account for cases in which the initial added host was re-added by ingestion (happens when
    // seeded with IP or localhost)
    if (host == null) {
      return Optional.empty();
    } else if (host.isEnabled()) {
      preferredId = host.getId();
    } else {
      preferredId =
          canonicalHostSvc.findPrimaryHostId(
              groupId, host.getName(), host.getPort(), ReadPreference.primary());
    }

    final Host preferredHost = hostSvc.findHostById(preferredId, groupId);

    if (preferredHost == null) {
      return Optional.empty();
    } else if (preferredHost.hasLastPing() && preferredHost.hasHostTypes()) {
      lastPing = preferredHost.getLastPing();

      final List<Integer> hostTypes = preferredHost.getHostTypes();

      if (HostType.codesRepresentCluster(hostTypes) && preferredHost.hasParentClusterId()) {
        deploymentItemId = preferredHost.getParentClusterId().toString();
      } else if (HostType.codesRepresentReplicaSet(hostTypes) && preferredHost.hasCluster()) {
        deploymentItemId = preferredHost.getCluster().getId().toString();
      } else if (preferredHost.getIsStandalone()) {
        deploymentItemId = preferredId;
      } else {
        throw new IllegalStateException("Can't find deploymentItemId");
      }
    }

    return Optional.of(new HostStatusInfo(lastPing, deploymentItemId));
  }

  private void validateConflictingProcessRequests(
      final ImportDeploymentRequestView importRequest, final Group group) throws SvcException {
    List<ImportDeployment> openImportDeploymentsByGroupId =
        importDeploymentDao.findActiveImportDeploymentsByGroupId(group.getId());

    // Seed node or one of destination hosts
    for (ImportDeployment importDeployment : openImportDeploymentsByGroupId) {
      validateIntersectionWithEnqueued(importDeployment, importRequest);
    }
  }

  private void validateIntersectionWithEnqueued(
      final ImportDeployment importDeployment, final ImportDeploymentRequestView importRequest)
      throws SvcException {
    if (Objects.equals(importDeployment.seedHostport(), importRequest.getSeedHostport())) {
      throw new SvcException(
          CommonErrorCode.VALIDATION_ERROR,
          "There is another import request enqueued with same seed host. If you want to amend the"
              + " previous one please cancel it first");
    }

    // Should be zero intersection between required processes
    HashSet<String> processes = Sets.newHashSet(importDeployment.requiredProcesses());
    processes.addAll(importRequest.getRequiredProcesses());

    if (processes.size()
        != importDeployment.requiredProcesses().size()
            + importRequest.getRequiredProcesses().size()) {
      throw new SvcException(
          CommonErrorCode.VALIDATION_ERROR,
          "There is another import request enqueued having required processes. If you want to amend"
              + " the previous one please cancel it first");
    }
  }

  private void validateAgentWithMonitoringIsUp(final Group group, final Deployment deployment)
      throws SvcException {
    List<DeploymentDiscoverySvc.AgentView> automationAgents =
        deploymentDiscoverySvc.getAutomationAgents(group);
    Collection<DeploymentDiscoverySvc.AgentView> monitoringAgents =
        deploymentDiscoverySvc.getMonitoringAgents(deployment, group);

    if (automationAgents.isEmpty()) {
      throw new ValidationException(
          "No automation agent detected. The agent should be installed and configured for the"
              + " deployment");
    }

    if (monitoringAgents.isEmpty()) {
      throw new ValidationException(
          "No monitoring detected. The monitoring should be enabled for the deployment.");
    }
  }

  private void validateTlsAuth(
      final ImportDeployment importDeployment, final AutomationConfig publishedConfig)
      throws SvcException {

    // For Auth check the AuthMechanism.
    if (importDeployment.authMechanism() == null) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER, "Missing or malformed authMechanism");
    }

    var desiredTlsEnabled = StringUtils.isNotBlank(importDeployment.caPath());
    var existingTlsEnabled = publishedConfig.getDeployment().getTLS().isEnabled();
    var existingHasProcesses = !publishedConfig.getDeployment().getProcesses().isEmpty();

    if (existingHasProcesses) {
      if (desiredTlsEnabled != existingTlsEnabled) {
        throw new SvcException(
            CommonErrorCode.INVALID_PARAMETER,
            "Unable to enable/disable TLS settings on a group that already has deployments");
      } else if (desiredTlsEnabled) {
        String incompatibleParam =
            compareTlsSettings(publishedConfig.getDeployment().getTLS(), importDeployment);
        throw new SvcException(
            CommonErrorCode.INVALID_PARAMETER,
            "Unable to change TLS settings on a group that already has deployments. Conflicting"
                + " param: "
                + incompatibleParam);
      }
    }
  }

  private void validateRequest(final ImportDeploymentRequestView importRequest)
      throws SvcException {
    if (StringUtils.isBlank(importRequest.getSeedHostport())
        || !isValidHostnamePort(importRequest.getSeedHostport())) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER, "Missing or malformed seedHostport");
    }

    if (importRequest.getRequiredProcesses() == null
        || importRequest.getRequiredProcesses().isEmpty()) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER, "Missing requiredProcesses in request");
    }

    for (String hostnameAndPort : importRequest.getRequiredProcesses()) {
      if (!isValidHostnamePort(hostnameAndPort)) {
        throw new SvcException(
            CommonErrorCode.INVALID_PARAMETER,
            "Each string in requiredProcesses collection should be in form of <hostname:port>");
      }
    }

    // Timeouts
    ImportDeploymentRequestView.TimeoutsView timeouts = importRequest.getTimeouts();
    if (timeouts != null) {
      validateTimeout(timeouts.getTimeoutSeedHostConnectionSec());
      validateTimeout(timeouts.getTimeoutHostsDiscoverySec());
      validateTimeout(timeouts.getTimeoutAutomationImported());
      validateTimeout(timeouts.getTimeoutGoalStateSec());
    }
  }

  private void validateTimeout(Integer value) throws SvcException {
    if (value == null) {
      return;
    }

    if (value < 60 || value > 24 * 60 * 60) {
      throw new SvcException(
          CommonErrorCode.INVALID_PARAMETER,
          "Timeout should be in range of [60..86400] seconds. 1min is minimum and 24hours is"
              + " maximum");
    }
  }

  public static Pair<String, Integer> extractHostnameAndPort(final String pHostnamePort) {
    final String hostname = extractHostname(pHostnamePort);
    final Integer port = extractPortIfExists(pHostnamePort);
    if (port != null && !hostname.isEmpty()) {
      return Pair.of(hostname, port);
    }
    return null;
  }

  public static boolean isValidHostnamePort(String hostnameAndPort) {
    return hostnameAndPort != null && extractHostnameAndPort(hostnameAndPort) != null;
  }

  private ImportDeployment createImportDeploymentRecord(
      final ImportDeploymentRequestView importDeploymentView,
      final Group group,
      final AppUser appUser)
      throws SvcException {

    final ImportDeployment.Builder importDeploymentBuilder =
        new ImportDeployment.Builder()
            .withGroupId(group.getId())
            .withUserId(appUser.getId())
            .withSeedHostport(importDeploymentView.getSeedHostport())
            .withRequiredProcesses(importDeploymentView.getRequiredProcesses())
            .withAuthMechanism(importDeploymentView.getAuthMechanism())
            .withAdminUsername(importDeploymentView.getAdminUsername())
            .withAdminPassword(importDeploymentView.getAdminPassword())
            .withAdminPemKeyPath(importDeploymentView.getPemKeyFilePath())
            .withAdminPemKeyPwd(importDeploymentView.getPemKeyFilePassword())
            .withClusterCaPath(importDeploymentView.getClusterCaPath())
            .withCaPath(importDeploymentView.getCaPath())
            .withAdminDb(importDeploymentView.getAdminDb())
            .withAdminKerberosKeytab(importDeploymentView.getAdminKerberosKeytab())
            .withAdminLdapGroupDn(importDeploymentView.getAdminLdapGroupDn())
            .withSaslServiceName(importDeploymentView.getSaslServiceName());

    if (importDeploymentView.getTimeouts() != null) {
      importDeploymentBuilder.withTimeouts(
          ObjectUtils.defaultIfNull(
              importDeploymentView.getTimeouts().getTimeoutSeedHostConnectionSec(), 0),
          ObjectUtils.defaultIfNull(
              importDeploymentView.getTimeouts().getTimeoutHostsDiscoverySec(), 0),
          ObjectUtils.defaultIfNull(
              importDeploymentView.getTimeouts().getTimeoutAutomationImported(), 0),
          ObjectUtils.defaultIfNull(
              importDeploymentView.getTimeouts().getTimeoutGoalStateSec(), 0));
    }

    return importDeploymentBuilder.build();
  }

  private String compareTlsSettings(final TLS tls, final ImportDeployment importDeployment) {
    BiFunction<String, String, Boolean> isEffectivelyEqual =
        (String string1, String string2) ->
            Objects.equals(StringUtils.trimToNull(string1), StringUtils.trimToNull(string2));

    if (!isEffectivelyEqual.apply(importDeployment.caPath(), tls.getCAFilePath())) {
      return "CA path";
    }

    if (!isEffectivelyEqual.apply(importDeployment.clusterCaPath(), tls.getClusterCAFilePath())) {
      return "Cluster CA path";
    }

    if (!isEffectivelyEqual.apply(
        importDeployment.adminPemKeyPath(), tls.getAutoPEMKeyFilePath())) {
      return "PEM key path";
    }

    if (!isEffectivelyEqual.apply(importDeployment.adminPemKeyPwd(), tls.getAutoPEMKeyFilePwd())) {
      return "PEM key password";
    }
    return null;
  }

  private static Date lastTouchedAt(ImportDeployment importDeployment) {
    final List<ImportDeployment.TransitionRecord> transitionRecords =
        importDeployment.transitionsHistory();

    Date baseTimestamp;
    if (transitionRecords.isEmpty()) {
      LOG.error("No transition records found. Use createdAt as basis for calculating timeouts");
      baseTimestamp = importDeployment.createdAt();
    } else {
      final ImportDeployment.TransitionRecord lastTransition =
          transitionRecords.get(transitionRecords.size() - 1);

      if (lastTransition.state() != importDeployment.state()) {
        LOG.error(
            "Mismatch of the ImportDeployment's state and the last record of transition history."
                + " Continuing using the last record anyways as a last known state transition");
      }
      baseTimestamp = lastTransition.transitionedAt();
    }
    return baseTimestamp;
  }
}
