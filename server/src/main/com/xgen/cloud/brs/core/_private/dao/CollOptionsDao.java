package com.xgen.cloud.brs.core._private.dao;

import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.DefaultDBEncoder;
import com.xgen.cloud.brs.core._public.model.CollOptions;
import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.common.mongo._public.mongo.Namespace;
import com.xgen.svc.core.dao.base.MongoIndex;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.List;
import java.util.stream.Collectors;
import org.bson.BSONObject;
import org.bson.types.ObjectId;

@Singleton
public class CollOptionsDao extends BaseBackupDao {

  @Inject
  public CollOptionsDao(MongoSvc mongoSvc) {
    super(mongoSvc, "backupsnapshot", "collinfo");
  }

  @Override
  public List<MongoIndex> getIndexes() {
    final List<MongoIndex> list = super.getIndexes();
    list.add(
        new MongoIndex(this).key("groupId", 1).key("rsId", 1).key("syncId", 1).key("namespace", 1));

    return list;
  }

  public void upsert(
      ObjectId groupId, String rsId, ObjectId syncId, Namespace namespace, BSONObject collinfo) {
    DBObject query =
        new BasicDBObject()
            .append("groupId", groupId)
            .append("rsId", rsId)
            .append("syncId", syncId)
            .append("namespace", namespace.getNamespace());

    DBObject toSet = new BasicDBObject("collinfo", new DefaultDBEncoder().encode(collinfo));
    DBObject update = new BasicDBObject("$set", toSet);

    upsertOneReplicaSafe(query, update);
  }

  public List<CollOptions> getForSync(ObjectId groupId, String rsId, ObjectId syncId) {
    DBObject query = new BasicDBObject();
    query.put("groupId", groupId);
    query.put("rsId", rsId);
    query.put("syncId", syncId);

    try (DBCursor cursor = getDbCollection().find(query)) {
      return cursor.toArray().stream().map(d -> new CollOptions(d)).collect(Collectors.toList());
    }
  }

  public void removeForSync(final ObjectId groupId, final String rsId, final ObjectId syncId) {
    final DBObject query = new BasicDBObject();
    query.put("groupId", groupId);
    query.put("rsId", rsId);
    query.put("syncId", syncId);

    getDbCollection().remove(query, getReplicaSafeWriteConcern());
  }

  public DBCursor findAllIdsBefore(final ObjectId pId) {
    final BasicDBObject query = new BasicDBObject("_id", new BasicDBObject(LT, pId));
    final BasicDBObject fields = new BasicDBObject("_id", 1);
    final BasicDBObject sort = new BasicDBObject("_id", 1);

    return getDbCollection().find(query, fields).sort(sort);
  }
}
