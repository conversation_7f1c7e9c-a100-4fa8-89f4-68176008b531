load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "model",
    srcs = glob(["**/*.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/activity",
        "//server/src/main/com/xgen/cloud/common/eventseverity",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/notification",
        "//server/src/main/com/xgen/cloud/openapi",
        "//server/src/main/com/xgen/svc/mms/model/grouptype",
        "//third_party:morphia",
        "@maven//:com_fasterxml_jackson_core_jackson_annotations",
        "@maven//:io_swagger_core_v3_swagger_annotations_jakarta",
        "@maven//:org_apache_commons_commons_collections4",
        "@maven//:org_mongodb_bson",
    ],
)
