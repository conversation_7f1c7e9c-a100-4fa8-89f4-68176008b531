package com.xgen.cloud.clusters._public.model.view;

import com.xgen.cloud.activity._public.model.event.view.EventTypeView;
import com.xgen.cloud.clusters._public.model.ClusterEvent;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title = "Cluster Event Types")
public enum ClusterEventTypeView implements EventTypeView<ClusterEvent.Type> {
  CLUSTER_MONGOS_IS_PRESENT,
  CLUSTER_MONGOS_IS_MISSING,
  CLUSTER_AGENT_IN_CRASH_LOOP,
  CLUSTER_AGENT_HAS_RS_RECONFIG_ERROR,
  CLUSTER_AGENT_HAS_PERSISTENT_MOVE_COLLECTION_ERROR;

  public interface Name {
    String CLUSTER_MONGOS_IS_PRESENT = "CLUSTER_MONGOS_IS_PRESENT";
    String CLUSTER_MONGOS_IS_MISSING = "CLUSTER_MONGOS_IS_MISSING";
    String CLUSTER_AGENT_IN_CRASH_LOOP = "CLUSTER_AGENT_IN_CRASH_LOOP";
    String CLUSTER_AGENT_HAS_RS_RECONFIG_ERROR = "CLUSTER_AGENT_HAS_RS_RECONFIG_ERROR";
    String CLUSTER_AGENT_HAS_PERSISTENT_MOVE_COLLECTION_ERROR =
        "CLUSTER_AGENT_HAS_PERSISTENT_MOVE_COLLECTION_ERROR";
  }

  @Schema(title = "Cluster Event Types", name = "ClusterEventTypeViewAlertable")
  @SuppressWarnings("SwaggerSchemaUsageChecker")
  public enum Alertable {
    CLUSTER_MONGOS_IS_MISSING,
    CLUSTER_AGENT_IN_CRASH_LOOP,
    CLUSTER_AGENT_HAS_RS_RECONFIG_ERROR,
    CLUSTER_AGENT_HAS_PERSISTENT_MOVE_COLLECTION_ERROR;
  }

  @Schema(title = "Cluster Event Types", name = "ClusterEventTypeViewForNdsGroup")
  @SuppressWarnings("SwaggerSchemaUsageChecker")
  public enum ForNdsGroup {
    CLUSTER_MONGOS_IS_PRESENT,
    CLUSTER_MONGOS_IS_MISSING;

    @Schema(title = "Cluster Event Types", name = "ClusterEventTypeViewForNdsGroupAlertable")
    @SuppressWarnings("SwaggerSchemaUsageChecker")
    public enum Alertable {
      CLUSTER_MONGOS_IS_MISSING;
    }
  }
}
