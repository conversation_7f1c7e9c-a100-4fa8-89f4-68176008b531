package com.xgen.cloud.clusters._public.model.view;

import static com.xgen.cloud.clusters._public.model.ClusterAlertConfig.MatcherField.CLUSTER_NAME;
import static com.xgen.cloud.common.util._public.util.EnumUtils.tryValueOf;
import static java.util.Optional.ofNullable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.activity._public.model.alert.config.Matcher;
import com.xgen.cloud.activity._public.model.alert.config.view.matcher.MatcherView;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.AccessMode;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.Map;
import org.apache.commons.collections4.BidiMap;
import org.apache.commons.collections4.bidimap.DualHashBidiMap;

@Schema(
    name = "ClusterMatcher",
    description = "Rules to apply when comparing an cluster against this alert configuration.",
    title = "Matchers")
public class ClusterMatcherView extends MatcherView {

  private static final BidiMap<String, ClusterMatcherField> CLUSTER_MATCHER_FIELD_MAP =
      new DualHashBidiMap<>(Map.of(CLUSTER_NAME.getFieldName(), ClusterMatcherField.CLUSTER_NAME));

  // private constructor for open API subclasses and jackson
  private ClusterMatcherView() {}

  public ClusterMatcherView(final Matcher matcher) {
    super(matcher);
    fieldName =
        ofNullable(CLUSTER_MATCHER_FIELD_MAP.get(matcher.getField()))
            .map(ClusterMatcherField::name)
            .orElseThrow(this::unsupportedMatcherFieldException);
  }

  @Override
  @JsonProperty
  @Schema(
      description =
          "Name of the parameter in the target object that MongoDB Cloud checks. The parameter"
              + " must match all rules for MongoDB Cloud to check for alert configurations.",
      accessMode = AccessMode.READ_WRITE,
      enumAsRef = true,
      implementation = ClusterMatcherField.class,
      requiredMode = RequiredMode.REQUIRED,
      example = "CLUSTER_NAME")
  public String getFieldName() {
    return fieldName;
  }

  public static Matcher toMatcher(final MatcherView matcherView) {
    final String fieldName =
        ofNullable(matcherView.getFieldName())
            .map(tryValueOf(ClusterMatcherField.class))
            .map(f -> CLUSTER_MATCHER_FIELD_MAP.inverseBidiMap().get(f))
            .orElseThrow(matcherView::unsupportedMatcherFieldException);
    return new Matcher(fieldName, matcherView.getOperator(), matcherView.getValue());
  }

  @Schema(title = "Cluster Matcher Fields")
  public enum ClusterMatcherField {
    CLUSTER_NAME
  }
}
