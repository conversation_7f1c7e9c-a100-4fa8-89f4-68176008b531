package com.xgen.cloud.clusters._public.model.view;

import static com.xgen.cloud.activity._public.model.alert.view.AlertView.AlertViewLinkRel.CLUSTER;
import static com.xgen.cloud.clusters._public.model.view.ClusterEventTypeView.Name.CLUSTER_MONGOS_IS_MISSING;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.CLUSTER_NAME_REGEX;
import static io.swagger.v3.oas.annotations.media.Schema.AccessMode.READ_ONLY;
import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;
import static java.lang.String.format;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.activity._public.model.alert.view.AlertView;
import com.xgen.cloud.activity._public.model.alert.view.AlertViewContext;
import com.xgen.cloud.clusters._public.model.ClusterAlert;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import org.bson.types.ObjectId;

@Schema(
    title = "Cluster Alerts",
    description =
        "Cluster alert notifies different activities and conditions about cluster of mongod hosts.")
public class ClusterAlertView extends AlertView {

  /*
  2022-03-28 - @jwilliams-mongo - Undocumented in the Atlas API docs as of this date.
  */
  @JsonProperty
  @Hidden
  @Schema(
      description =
          "Unique 24-hexadecimal character string that identifies the cluster to which this alert"
              + " applies. MongoDB Cloud returns this parameter only for alerts of events that"
              + " impact backups, replica sets, or sharded clusters.",
      accessMode = READ_ONLY)
  private ObjectId clusterId;

  @JsonProperty
  @Schema(
      type = "string",
      description =
          "Human-readable label that identifies the cluster to which this alert applies. This"
              + " resource returns this parameter for alerts of events impacting backups, replica"
              + " sets, or sharded clusters.",
      accessMode = READ_ONLY,
      pattern = CLUSTER_NAME_REGEX,
      example = "cluster1")
  private String clusterName;

  /*
    2022-01-07 - @atsansone - Undocumented in the Atlas API docs as of this date.
  */
  @JsonProperty @Hidden private String shardName;

  // private constructor for open API subclasses and jackson
  private ClusterAlertView() {}

  public ClusterAlertView(final ClusterAlert alert, final AlertViewContext context) {
    super(alert, context);
    shardName = alert.getShardName();
    if (context != null && !context.isAtlas()) {
      clusterName = alert.getClusterName();
      clusterId = alert.getClusterId();
    } else {
      clusterName = context != null ? context.getNdsClusterName(alert) : null;
    }

    addLinks(context);
  }

  private void addLinks(final AlertViewContext context) {
    if (context != null && !context.isAtlas()) {
      if (clusterId != null) {
        addRelativeLink(CLUSTER, format(CLUSTER.getHrefPattern(), getGroupId(), clusterId));
      }
    }
  }

  @Override
  @JsonProperty
  @Schema(
      type = "string",
      description = "Incident that triggered this alert.",
      enumAsRef = true,
      implementation = ClusterEventTypeView.Alertable.class,
      example = CLUSTER_MONGOS_IS_MISSING,
      accessMode = READ_ONLY,
      requiredMode = REQUIRED)
  public String getEventTypeName() {
    return super.getEventTypeName();
  }

  public ObjectId getClusterId() {
    return clusterId;
  }

  public String getClusterName() {
    return clusterName;
  }

  public String getShardName() {
    return shardName;
  }

  @Schema(
      title = "Cluster Alerts",
      description =
          "Cluster alert notifies different activities and conditions about cluster of mongod"
              + " hosts.")
  public static class ClusterAlertViewForNdsGroup extends ClusterAlertView {

    @Override
    @JsonProperty
    @Schema(
        type = "string",
        description = "Incident that triggered this alert.",
        enumAsRef = true,
        implementation = ClusterEventTypeView.ForNdsGroup.Alertable.class,
        example = CLUSTER_MONGOS_IS_MISSING,
        accessMode = READ_ONLY,
        requiredMode = REQUIRED)
    public String getEventTypeName() {
      return super.getEventTypeName();
    }
  }
}
