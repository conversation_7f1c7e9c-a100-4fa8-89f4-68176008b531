package com.xgen.cloud.clusters._public.model.view;

import static com.xgen.cloud.activity._public.model.event.view.EventView.EventViewLinkRel.CLUSTER;
import static com.xgen.cloud.clusters._public.model.view.ClusterEventTypeView.Name.CLUSTER_MONGOS_IS_PRESENT;
import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;
import static java.lang.String.format;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.activity._public.model.event.view.EventView;
import com.xgen.cloud.activity._public.model.event.view.EventViewContext;
import com.xgen.cloud.clusters._public.model.ClusterEvent;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import org.bson.types.ObjectId;

@Schema(
    title = "Cluster Events",
    description = "Cluster event identifies different activities about cluster of mongod hosts.")
public class ClusterEventView extends EventView {

  @Hidden @JsonProperty private ObjectId clusterId;

  @Hidden @JsonProperty private String clusterName;

  @JsonProperty
  @Schema(
      type = "string",
      description = "Human-readable label of the shard associated with the event.",
      accessMode = Schema.AccessMode.READ_ONLY,
      example = "event-sh-01")
  private String shardName;

  // private constructor for open API subclasses and jackson
  private ClusterEventView() {}

  public ClusterEventView(final ClusterEvent event, final EventViewContext context) {
    super(event, context);
    if (context != null && !context.isAtlas()) {
      clusterId = event.getClusterId();
    }
    clusterName = event.getClusterName();
    shardName = event.getShardName();

    addLinks(context);
  }

  private void addLinks(final EventViewContext context) {
    if (context != null && !context.isSelfLinkOnly() && clusterId != null && getGroupId() != null) {
      addRelativeLink(CLUSTER, format(CLUSTER.getHrefPattern(), getGroupId(), clusterId));
    }
  }

  @Override
  @JsonProperty
  @Schema(
      type = "string",
      description = "Unique identifier of event type.",
      enumAsRef = true,
      implementation = ClusterEventTypeView.class,
      example = CLUSTER_MONGOS_IS_PRESENT,
      requiredMode = REQUIRED)
  public String getEventTypeName() {
    return super.getEventTypeName();
  }

  public ObjectId getClusterId() {
    return clusterId;
  }

  public String getClusterName() {
    return clusterName;
  }

  public String getShardName() {
    return shardName;
  }

  @Schema(
      title = "Cluster Events",
      description = "Cluster event identifies different activities about cluster of mongod hosts.")
  public static class ClusterEventViewForNdsGroup extends ClusterEventView {
    @Override
    @JsonProperty
    @Schema(
        type = "string",
        description = "Unique identifier of event type.",
        enumAsRef = true,
        implementation = ClusterEventTypeView.ForNdsGroup.class,
        example = CLUSTER_MONGOS_IS_PRESENT,
        requiredMode = REQUIRED)
    public String getEventTypeName() {
      return super.getEventTypeName();
    }
  }
}
