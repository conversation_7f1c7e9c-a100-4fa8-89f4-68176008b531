package com.xgen.cloud.organization.runtime.res;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.alerts.defaults._public.svc.DefaultAlertConfigSvc;
import com.xgen.cloud.authz.core._public.client.ActorClient;
import com.xgen.cloud.authz.core._public.client.AuthorizationClient;
import com.xgen.cloud.authz.core._public.client.PolicyClient;
import com.xgen.cloud.authz.core._public.utils.ConversionUtils;
import com.xgen.cloud.authz.core._public.wrapper.AuthorizationClientProvider;
import com.xgen.cloud.authz.resource._public.client.ResourceClient;
import com.xgen.cloud.authz.resource._public.wrapper.ResourceClientProvider;
import com.xgen.cloud.authz.shared._public.exceptions.AuthzServiceClientException;
import com.xgen.cloud.billing._public.svc.IPlanSvc;
import com.xgen.cloud.billing._public.svc.exception.BillingErrorCode;
import com.xgen.cloud.common.access._public.annotation.Auth;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.access._public.annotation.UiCall.GroupSource;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.authz._public.model.AuthzRequestInfo;
import com.xgen.cloud.common.authz._public.view.PolicyAssignmentView;
import com.xgen.cloud.common.constants._public.model.actions.ActionConstants;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.common.util._public.util.SetUtils;
import com.xgen.cloud.configlimit._public.svc.ConfigLimitSvc;
import com.xgen.cloud.externalanalytics._public.model.OrgGenAiFeaturesDisabledEvent;
import com.xgen.cloud.externalanalytics._public.model.OrgGenAiFeaturesEnabledEvent;
import com.xgen.cloud.externalanalytics._public.model.OrgStreamCrossProjectDisabledEvent;
import com.xgen.cloud.externalanalytics._public.model.OrgStreamCrossProjectEnabledEvent;
import com.xgen.cloud.externalanalytics._public.model.SecurityContactModifiedEvent;
import com.xgen.cloud.externalanalytics._public.model.SecurityContactModifiedEvent.Properties.SecurityContactStatus;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.federation._public.model.SamlIdentityProvider.Status;
import com.xgen.cloud.federation._public.svc.FederationSettingsSvc;
import com.xgen.cloud.group._public.svc.GroupNameSvc;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.invitation._public.model.Invitation;
import com.xgen.cloud.invitation._public.svc.InvitationSvc;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited;
import com.xgen.cloud.monitoring.ratelimit._public.annotation.RateLimited.Type;
import com.xgen.cloud.nds.common._public.model.RegionUsageRestrictions;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.organization._private.view.CustomSessionTimeoutsView;
import com.xgen.cloud.organization._private.view.OrgCreationForm;
import com.xgen.cloud.organization._private.view.UiIpAccessListView;
import com.xgen.cloud.organization._private.view.UpdateSandboxConfigView;
import com.xgen.cloud.organization._private.view.ValidateIpAddressView;
import com.xgen.cloud.organization._public.model.OrgUiIpAccessListEnableForm;
import com.xgen.cloud.organization._public.model.OrgUiIpAccessListEntry;
import com.xgen.cloud.organization._public.model.OrgUiIpAccessListEntryForm;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.organization._public.model.OrganizationAdminView;
import com.xgen.cloud.organization._public.model.RemoveVercelIntegrationPayload;
import com.xgen.cloud.organization._public.svc.OrgUiIpAccessListSvc;
import com.xgen.cloud.organization._public.svc.OrganizationSearchSvc;
import com.xgen.cloud.organization._public.svc.OrganizationSvc;
import com.xgen.cloud.organization._public.svc.OrganizationVercelIntegrationSvc;
import com.xgen.cloud.organization._public.view.OrgInfoView;
import com.xgen.cloud.organizationsettings._public.svc.OrgSecurityContactEmailSvc;
import com.xgen.cloud.organizationsettings._public.svc.OrgSessionTimeoutEmailSvc;
import com.xgen.cloud.payments.common._public.model.VATStatus;
import com.xgen.cloud.sandbox._public.model.SandboxConfig;
import com.xgen.cloud.sandbox._public.svc.SandboxConfigSvc;
import com.xgen.cloud.services.authz.proto.ResourceMessage;
import com.xgen.cloud.sfdc._public.exception.SalesforceErrorCode;
import com.xgen.cloud.sfdc._public.util.SalesforceUtils;
import com.xgen.cloud.sfsc._public.svc.SalesforceServiceCloudSvc;
import com.xgen.cloud.ui._public.view.ResourceView;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.svc.core.model.api.SimpleApiResponse;
import com.xgen.svc.mms.form.OrgRenameForm;
import com.xgen.svc.mms.res.cors.AllowCORS;
import com.xgen.svc.mms.res.cors.KnownCrossOrigin;
import com.xgen.svc.mms.res.view.organization.OrgFederationSettingsView;
import com.xgen.svc.mms.res.view.user.UserView;
import com.xgen.svc.mms.svc.AccountClosureSvc;
import com.xgen.svc.mms.svc.NDSOrgSvc;
import com.xgen.svc.mms.svc.ResourceTransformationSvc;
import com.xgen.svc.mms.svc.common.GroupErrorCode;
import com.xgen.svc.mms.svc.common.OrgErrorCode;
import com.xgen.svc.mms.svc.vercel.VercelEmailSvc;
import com.xgen.svc.nds.svc.project.NDSGroupSvc;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.FormParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpStatus;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings("NullAway") // suppressed to enable nullaway to address another issue
@Path("/orgs")
@Singleton
public class OrganizationResource extends ApiBaseResource {

  private static final Logger LOG = LoggerFactory.getLogger(OrganizationResource.class);
  public static final String VERCEL_SIGNATURE_HEADER = "x-vercel-signature";
  static final String RATE_LIMIT_POLICY_PREFIX = "mms.organization";
  private static final int SEARCH_MAX_RETURN_SIZE = 50;
  private final AccountClosureSvc accountClosureSvc;
  private final OrganizationSearchSvc organizationSearchSvc;
  private final OrganizationSvc organizationSvc;
  private final NDSOrgSvc ndsOrgSvc;
  private final InvitationSvc invitationSvc;
  private final GroupSvc groupSvc;
  private final UserSvc userSvc;
  private final SalesforceServiceCloudSvc salesforceServiceCloudSvc;
  private final ConfigLimitSvc configLimitSvc;
  private final FederationSettingsSvc federationSettingsSvc;
  private final GroupNameSvc groupNameSvc;
  private final AuthzSvc authzSvc;
  private final NDSGroupSvc ndsGroupSvc;
  private final AppSettings appSettings;
  private final VercelEmailSvc vercelEmailSvc;
  private final IPlanSvc planSvc;
  private final OrgUiIpAccessListSvc orgUiIpAccessListSvc;
  private final DefaultAlertConfigSvc defaultAlertConfigSvc;
  private final ActorClient actorClient;
  private final AuthorizationClientProvider authorizationClientProvider;
  private final OrganizationVercelIntegrationSvc organizationVercelIntegrationSvc;
  private final SegmentEventSvc segmentEventSvc;
  private final PolicyClient policyClient;
  private final ResourceClientProvider resourceClientProvider;
  private final ResourceTransformationSvc resourceTransformationSvc;
  private final OrgSecurityContactEmailSvc orgSecurityContactEmailSvc;
  private final OrgSessionTimeoutEmailSvc orgSessionTimeoutEmailSvc;
  private final SandboxConfigSvc sandboxConfigSvc;

  @Inject
  public OrganizationResource(
      AccountClosureSvc pAccountClosureSvc,
      OrganizationSvc pOrganizationSvc,
      OrganizationSearchSvc pOrganizationSearchSvc,
      NDSOrgSvc pNdsOrgSvc,
      InvitationSvc pInvitationSvc,
      GroupSvc pGroupSvc,
      UserSvc pUserSvc,
      SalesforceServiceCloudSvc pSalesforceServiceCloudSvc,
      ConfigLimitSvc pConfigLimitSvc,
      FederationSettingsSvc pFederationSettingsSvc,
      GroupNameSvc pGroupNameSvc,
      AuthzSvc pAuthzSvc,
      NDSGroupSvc pNDSGroupSvc,
      AppSettings pAppSettings,
      VercelEmailSvc pVercelEmailSvc,
      IPlanSvc pPlanSvc,
      OrgUiIpAccessListSvc pOrgUiIpAccessListSvc,
      DefaultAlertConfigSvc pDefaultAlertConfigSvc,
      ActorClient pActorClient,
      AuthorizationClientProvider pAuthorizationClientProvider,
      OrganizationVercelIntegrationSvc organizationVercelIntegrationSvc,
      SegmentEventSvc segmentEventSvc,
      PolicyClient pPolicyClient,
      ResourceClientProvider pResourceClientProvider,
      ResourceTransformationSvc pResourceTransformationSvc,
      OrgSecurityContactEmailSvc orgSecurityContactEmailSvc,
      OrgSessionTimeoutEmailSvc orgSessionTimeoutEmailSvc,
      SandboxConfigSvc pSandboxConfigSvc) {
    super(pAppSettings);
    accountClosureSvc = pAccountClosureSvc;
    organizationSvc = pOrganizationSvc;
    organizationSearchSvc = pOrganizationSearchSvc;
    ndsOrgSvc = pNdsOrgSvc;
    invitationSvc = pInvitationSvc;
    groupSvc = pGroupSvc;
    userSvc = pUserSvc;
    salesforceServiceCloudSvc = pSalesforceServiceCloudSvc;
    configLimitSvc = pConfigLimitSvc;
    federationSettingsSvc = pFederationSettingsSvc;
    groupNameSvc = pGroupNameSvc;
    authzSvc = pAuthzSvc;
    ndsGroupSvc = pNDSGroupSvc;
    appSettings = pAppSettings;
    vercelEmailSvc = pVercelEmailSvc;
    planSvc = pPlanSvc;
    orgUiIpAccessListSvc = pOrgUiIpAccessListSvc;
    defaultAlertConfigSvc = pDefaultAlertConfigSvc;
    actorClient = pActorClient;
    authorizationClientProvider = pAuthorizationClientProvider;
    this.organizationVercelIntegrationSvc = organizationVercelIntegrationSvc;
    this.segmentEventSvc = segmentEventSvc;
    policyClient = pPolicyClient;
    resourceClientProvider = pResourceClientProvider;
    resourceTransformationSvc = pResourceTransformationSvc;
    this.orgSecurityContactEmailSvc = orgSecurityContactEmailSvc;
    this.orgSessionTimeoutEmailSvc = orgSessionTimeoutEmailSvc;
    sandboxConfigSvc = pSandboxConfigSvc;
  }

  @POST
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(
      roles = RoleSet.ANY_AUTHENTICATED_USER,
      groupSource = GroupSource.NONE,
      appSettingsSetupCall = true)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  public Response createOrganization(
      @Context HttpServletRequest pRequest,
      @Context AppUser pUser,
      @Context AuditInfo pAuditInfo,
      OrgCreationForm pForm)
      throws SvcException {
    Organization org = ndsOrgSvc.createOrganizationFromForm(pForm, pUser, pAuditInfo);

    return Response.ok().entity(org).build();
  }

  @GET
  @Path("/{orgId}/previouslyNDSDeveloper")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  public Response previouslyNDSDeveloper(@Context Organization pOrganization) {
    return Response.ok().entity(planSvc.previouslyNDSDeveloper(pOrganization.getId())).build();
  }

  @GET
  @Path("/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  public Response getOrganization(@Context Organization pOrganization) {
    return Response.ok().entity(pOrganization).build();
  }

  @GET
  @Path("/{orgId}/resources")
  @Feature(FeatureFlag.FINE_GRAINED_AUTH)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  public Response allResources(
      @Context final HttpServletRequest pRequest,
      @Context Organization pOrganization,
      @Context AppUser pUser,
      @QueryParam("envelope") final Boolean pEnvelope)
      throws SvcException {
    ObjectId orgId = pOrganization.getId();
    boolean isAuthorized =
        Stream.of(ActionConstants.ORG_OWNER, ActionConstants.ORG_USER_ADMIN)
            .anyMatch(
                action ->
                    getAuthorizationClient()
                        .isAuthorized(new AuthzRequestInfo(pUser.getId(), action, orgId)));

    if (!isAuthorized) {
      throw new SvcException(
          CommonErrorCode.FORBIDDEN,
          "Missing permissions to fetch all groups and clusters within organization");
    }

    List<ResourceMessage> resourceMessages =
        getResourceClient()
            .getResourcesByOrgId(pOrganization.getId().toString())
            .getResourcesList();
    List<ResourceView<?>> resourceViews = resourceTransformationSvc.getResources(resourceMessages);

    return handlePagination(pRequest, resourceViews, pEnvelope);
  }

  @GET
  @Path("/orgData")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.GLOBAL_ORG_READ_ONLY, groupSource = GroupSource.NONE)
  public Response getAllOrganizations() {

    List<Organization> organizations = organizationSvc.findAllActiveList(100);
    List<ObjectId> orgIds =
        organizations.stream().map(Organization::getId).collect(Collectors.toList());

    Map<ObjectId, Integer> groupCountPerOrg = groupSvc.countGroupsPerOrg(orgIds);
    Map<ObjectId, Integer> userCountPerOrg = userSvc.countLocalUsersPerOrg(orgIds);
    Map<ObjectId, Date> lastLoggedInPerOrg = userSvc.lastLoggedInForOrgs(orgIds);

    List<OrganizationAdminView> organizationAdminViews = new ArrayList<>();
    for (Organization org : organizations) {
      organizationAdminViews.add(
          new OrganizationAdminView.Builder()
              .id(org.getId().toString())
              .name(org.getName())
              .planType(planSvc.getCurrentPlanType(org.getId()))
              .billingStatus(org.getPaymentStatus().getStatus().toString())
              .numGroups(groupCountPerOrg.getOrDefault(org.getId(), 0))
              .numUsers(userCountPerOrg.getOrDefault(org.getId(), 0))
              .lastLoggedIn(lastLoggedInPerOrg.get(org.getId()))
              .build());
    }

    return Response.ok().entity(organizationAdminViews).build();
  }

  @PATCH
  @Path("/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response renameOrganization(
      @Context Organization pOrganization, OrgRenameForm pForm, @Context AuditInfo pAuditInfo)
      throws SvcException {
    ndsOrgSvc.renameOrganization(pOrganization.getId(), pForm.getName(), new Date(), pAuditInfo);
    Organization updatedOrg = organizationSvc.findById(pOrganization.getId());
    return Response.ok().entity(updatedOrg).build();
  }

  @DELETE
  @Path("/{orgId}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response deleteOrganization(
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      @Context HttpServletRequest pRequest)
      throws SvcException {
    try {
      accountClosureSvc.deleteOrganization(pOrganization.getId(), new Date(), pAuditInfo, pRequest);

    } catch (SvcException e) {
      if (e.getErrorCode() != OrgErrorCode.ORG_INVOICE_LOCKED_ON_DELETE) {
        throw e;
      } else {
        Response.status(HttpStatus.SC_CONFLICT)
            .entity(Collections.singletonMap("msg", OrgErrorCode.ORG_INVOICE_LOCKED_ON_DELETE))
            .build();
      }
    }
    return Response.ok().build();
  }

  @PATCH
  @Path("/{orgId}/acceptCNRegionsTermsOfService")
  @Feature(FeatureFlag.ATLAS_CN_REGIONS_ONLY)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response acceptCNRegionsTermsOfService(@Context Organization pOrganization) {
    organizationSvc.acceptCNRegionsTermsOfService(pOrganization.getId());
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/{orgId}/sfscSync")
  @UiCall(roles = RoleSet.GLOBAL_ORG_READ_ONLY, groupSource = GroupSource.NONE)
  public Response syncOrg(@Context Organization pOrganization) throws Exception {
    if (!appSettings.isSfscSyncEnabled()) {
      throw new SvcException(SalesforceErrorCode.FORBIDDEN_ENVIRONMENT);
    }

    salesforceServiceCloudSvc.sfscSyncOrg(pOrganization.getId());
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/{orgId}/federationSettings")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  public Response getFederationSettings(@PathParam("orgId") ObjectId pOrgId) {
    return federationSettingsSvc
        .findByConnectedOrgId(pOrgId)
        .map(
            federationSettings -> {
              var connectedOrgConfig = federationSettings.getOrgConfigByOrgId(pOrgId).orElseThrow();

              var hasRoleMappings =
                  connectedOrgConfig.getRoleMappings() != null
                      && !connectedOrgConfig.getRoleMappings().isEmpty();

              var federatedDomains =
                  connectedOrgConfig.getUiAccessIdentityProviderId() == null
                      ? Collections.<String>emptySet()
                      : federationSettings
                          .getSamlIdentityProviderByOktaIdpId(
                              connectedOrgConfig.getUiAccessIdentityProviderId())
                          .orElseThrow()
                          .getAssociatedDomains();

              var identityProviderId = connectedOrgConfig.getUiAccessIdentityProviderId();
              var identityProvider =
                  federationSettings.getSamlIdentityProviderByOktaIdpId(identityProviderId);
              var identityProviderStatus =
                  identityProvider.isPresent()
                      ? identityProvider.get().getStatus()
                      : Status.INACTIVE;
              var view =
                  new OrgFederationSettingsView(
                      federationSettings.getId(),
                      identityProviderId,
                      identityProviderStatus,
                      hasRoleMappings,
                      federatedDomains);

              return Response.ok(view).build();
            })
        .orElse(Response.status(HttpStatus.SC_NOT_FOUND).build());
  }

  @POST
  @Path("/{orgId}/validateGroupName")
  @UiCall(roles = RoleSet.ORG_GROUP_CREATOR, groupSource = GroupSource.NONE)
  @AllowCORS(KnownCrossOrigin.ACCOUNT)
  public Response validateGroupName(
      @Context Organization pOrganization, @FormParam("groupName") String pGroupName)
      throws SvcException {
    String groupName = groupSvc.normalize(pGroupName, false);
    if (groupNameSvc.groupNameExistsNameCaseInsensitive(pOrganization.getId(), groupName)) {
      throw new SvcException(GroupErrorCode.GROUP_NAME_NOT_AVAILABLE);
    }
    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/{orgId}/setMultiFactorAuthRequired")
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response setMultiFactorAuthRequired(
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      @FormParam("multiFactorAuthRequired") boolean pMultiFactorAuthRequired) {
    organizationSvc.setMultiFactorAuthRequired(
        pOrganization.getId(), pMultiFactorAuthRequired, pAuditInfo);
    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/{orgId}/setPublicApiAccessListRequired")
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response setPublicApiAccessListRequired(
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      @FormParam("publicApiAccessListRequired") boolean pPublicApiAccessListRequired) {
    organizationSvc.setPublicApiAccessListRequired(
        pOrganization.getId(), pPublicApiAccessListRequired, pAuditInfo);
    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/{orgId}/setStreamsCrossGroupEnabled")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response setStreamsCrossGroupEnabled(
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      boolean streamsCrossGroupEnabled)
      throws SvcException {

    if (appSettings.getAppEnv().isGovCloud()) {
      throw new SvcException(
          NDSErrorCode.ORG_SETTING_UNSUPPORTED_FOR_GOV,
          "Enable Atlas Stream Processing Across Projects");
    }

    organizationSvc.setStreamsCrossGroupEnabled(
        pOrganization.getId(), streamsCrossGroupEnabled, pAuditInfo);

    if (streamsCrossGroupEnabled) {
      segmentEventSvc.submitEvent(
          OrgStreamCrossProjectEnabledEvent.builder()
              .userId(pAuditInfo.getAppUserId())
              .organizationId(pOrganization.getId())
              .organizationName(pOrganization.getName())
              .build());
    } else {
      segmentEventSvc.submitEvent(
          OrgStreamCrossProjectDisabledEvent.builder()
              .userId(pAuditInfo.getAppUserId())
              .organizationId(pOrganization.getId())
              .organizationName(pOrganization.getName())
              .build());
    }

    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/{orgId}/setGenAiFeaturesEnabled")
  @Produces({MediaType.APPLICATION_JSON})
  @Feature(FeatureFlag.ORG_LEVEL_GEN_AI_CONTROL_SWITCH)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response setGenAiFeaturesEnabled(
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      boolean genAiFeaturesEnabled)
      throws SvcException {

    if (appSettings.getAppEnv().isGovCloud()) {
      throw new SvcException(
          NDSErrorCode.ORG_SETTING_UNSUPPORTED_FOR_GOV,
          "Enable Atlas features that use generative AI");
    }

    organizationSvc.setGenAiFeaturesEnabled(
        pOrganization.getId(), genAiFeaturesEnabled, pAuditInfo);

    if (genAiFeaturesEnabled) {
      segmentEventSvc.submitEvent(
          OrgGenAiFeaturesEnabledEvent.builder()
              .userId(pAuditInfo.getAppUserId())
              .organizationId(pOrganization.getId())
              .organizationName(pOrganization.getName())
              .build());
    } else {
      segmentEventSvc.submitEvent(
          OrgGenAiFeaturesDisabledEvent.builder()
              .userId(pAuditInfo.getAppUserId())
              .organizationId(pOrganization.getId())
              .organizationName(pOrganization.getName())
              .build());
    }

    return SimpleApiResponse.ok().build();
  }

  @PATCH
  @Path("/{orgId}/securityContact")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "setSecurityContact",
      types = {Type.USER})
  public Response setSecurityContact(
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      @Context AppUser pAppUser,
      @FormParam("email") String pEmail)
      throws SvcException {
    organizationSvc.setSecurityContact(pOrganization, pEmail, pAuditInfo);
    orgSecurityContactEmailSvc.sendSecurityContactUpdatedEmails(
        pEmail,
        pOrganization.getSecurityContact(),
        pAppUser.getName(),
        pAppUser.getId(),
        pOrganization.getId(),
        pOrganization.getName());

    if (OrganizationSvc.isSecurityContactChanged(pEmail, pOrganization.getSecurityContact())) {
      segmentEventSvc.submitEvent(
          SecurityContactModifiedEvent.builder(
                  pOrganization.getId(),
                  StringUtils.isBlank(pEmail)
                      ? SecurityContactStatus.DELETED
                      : StringUtils.isBlank(pOrganization.getSecurityContact())
                          ? SecurityContactStatus.ADDED
                          : SecurityContactStatus.UPDATED)
              .build());
    }

    return SimpleApiResponse.ok().build();
  }

  @PATCH
  @Path("/{orgId}/customSessionTimeouts")
  @Feature(FeatureFlag.CUSTOM_SESSION_TIMEOUTS)
  @Consumes({MediaType.APPLICATION_JSON})
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  @RateLimited(
      policyPrefix = RATE_LIMIT_POLICY_PREFIX,
      name = "customSessionTimeouts",
      types = {Type.USER},
      maxHitsPerPeriod = 10,
      minutesPeriod = 5,
      enabled = true)
  public Response customSessionTimeouts(
      @Context Organization pOrganization,
      CustomSessionTimeoutsView customSessionTimeoutsView,
      @Context AppUser pAppUser,
      @Context AuditInfo auditInfo)
      throws SvcException {
    Integer idleSessionTimeoutInSecondsInput =
        customSessionTimeoutsView.getIdleSessionTimeoutInSeconds();
    Integer absoluteSessionTimeoutInSecondsInput =
        customSessionTimeoutsView.getAbsoluteSessionTimeoutInSeconds();

    organizationSvc.setSessionTimeoutsByOrgId(
        pOrganization.getId(),
        absoluteSessionTimeoutInSecondsInput,
        idleSessionTimeoutInSecondsInput,
        auditInfo);

    if (idleSessionTimeoutInSecondsInput != null || absoluteSessionTimeoutInSecondsInput != null) {
      orgSessionTimeoutEmailSvc.sendSessionTimeoutUpdatedEmails(
          pAppUser.getName(), pAppUser.getId(), pOrganization.getId(), pOrganization.getName());
    }

    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/{orgId}/setRestrictEmployeeAccess")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response setRestrictEmployeeAccess(
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      @FormParam("restrictEmployeeAccess") boolean pRestrictEmployeeAccess) {
    organizationSvc.setRestrictEmployeeAccess(
        pOrganization.getId(), pRestrictEmployeeAccess, pAuditInfo);
    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/validateOrgName")
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response validateOrgName(@FormParam("orgName") String pOrgName) throws SvcException {
    organizationSvc.normalizeAndValidate(pOrgName);
    return SimpleApiResponse.ok().build();
  }

  /**
   * This endpoint is used in POST from the Create New Organization page to validate the config
   * limits before creating intended entities/performing additions.
   */
  @GET
  @Path("/validateConfigLimits")
  @Produces({MediaType.APPLICATION_JSON})
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response validateConfigLimits(@QueryParam("usernames[]") List<String> pUsernames)
      throws SvcException {
    configLimitSvc.validateDefaultMaxUsersPerOrg(pUsernames.size());
    // As new organization (i.e. null orgId) validation will apply to all usernames
    for (String username : pUsernames) {
      configLimitSvc.validateMaxOrgsPerUser(1, username, null);
    }
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/{orgId}/usersAndInvites")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  public Response getOrgUsersAndInvites(@Context Organization pOrganization) {
    Map<String, UserView> userViews =
        ndsOrgSvc.getOrgUserViewList(pOrganization.getId()).stream()
            .collect(toMap(UserView::getUsername, identity()));

    invitationSvc
        .findByOrgId(pOrganization.getId())
        // consolidate invitations per user and create a UserView for each
        .stream()
        .collect(groupingBy(Invitation::getUsername))
        .forEach(
            (username, invitationList) ->
                userViews.putIfAbsent(username, UserView.createFromInvitations(invitationList)));

    Map<String, Object> result = new HashMap<>();
    result.put("users", userViews.values());

    return Response.ok().entity(result).build();
  }

  @GET
  @Path("/{orgId}/users")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  public Response getOrgUsers(@Context Organization pOrganization) {
    List<UserView> users = ndsOrgSvc.getOrgUserViewList(pOrganization.getId());
    return Response.ok().entity(users).build();
  }

  @GET
  @Path("/{orgId}/users/{username}")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  public Response getOrgUser(
      @Context Organization pOrganization, @PathParam("username") String pUsername)
      throws SvcException {
    AppUser user = userSvc.findByUsername(pUsername);
    if (user == null || !user.hasOrgId(pOrganization.getId())) {
      throw new SvcException(AppUserErrorCode.USERNAME_NOT_FOUND);
    }
    UserView view =
        new UserView.Builder()
            .username(user.getUsername())
            .lastAuth(user.getLastAuth())
            .timeZoneId(user.getTimeZoneDisplay())
            .created(user.getCreated())
            .roles(user.getOrgRoles(pOrganization.getId()))
            .status(UserView.Status.CONFIRMED)
            .multiFactorAuth(user.getMultiFactorAuth())
            .hasAccountMultiFactorAuth(user.hasAccountMultiFactorAuth())
            .emailLastVerified(user.getEmailLastVerified())
            .build();
    return Response.ok().entity(view).build();
  }

  @PATCH
  @Path("/{orgId}/users/{username}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  public Response editOrgUser(
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      @PathParam("username") String pUsername,
      UserView pUserView)
      throws SvcException {
    AppUser user = userSvc.findByUsername(pUsername);
    if (user == null || !user.hasOrgId(pOrganization.getId())) {
      throw new SvcException(AppUserErrorCode.USERNAME_NOT_FOUND);
    }
    if (pUserView.getRoles().isEmpty()) {
      throw new SvcException(AppUserErrorCode.CANNOT_REMOVE_ALL_USER_ROLES);
    }
    List<Role> oldRoles = user.getOrgRoles(pOrganization.getId());
    Set<Role> newRoles = new HashSet<>(pUserView.getRoles());
    Set<Role> removedRoles = new HashSet<>(oldRoles);
    removedRoles.removeAll(newRoles);
    userSvc.updateUserRoleInOrganization(
        user, pOrganization.getId(), newRoles, removedRoles, false, pAuditInfo);
    return getOrgUser(pOrganization, pUsername);
  }

  @DELETE
  @Path("/{orgId}/users/{username}")
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  public Response deleteOrgUser(
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      @PathParam("username") String pUsername)
      throws SvcException {
    AppUser user = userSvc.findByUsername(pUsername);
    if (user == null || !user.hasOrgId(pOrganization.getId())) {
      throw new SvcException(AppUserErrorCode.USERNAME_NOT_FOUND);
    }
    userSvc.removeUserFromOrganization(user, pOrganization.getId(), pAuditInfo);

    return Response.ok().build();
  }

  @GET
  @Path("/{orgId}/invitations")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  public Response getOrgInvitations(@Context Organization pOrganization) {
    List<Invitation> invitations = invitationSvc.findByOrgId(pOrganization.getId());
    return Response.ok().entity(invitations).build();
  }

  @DELETE
  @Path("/{orgId}/invitations/{username}")
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  public Response deleteOrgInvitation(
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      @PathParam("username") String pUsername) {
    invitationSvc.removeByOrgIdAndUsername(pOrganization.getId(), pUsername, pAuditInfo);
    return Response.ok().build();
  }

  @POST
  @Path("/{orgId}/validateUser")
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  public Response validateUsername(
      @Context Organization pOrganization, @FormParam("username") String pUsername)
      throws SvcException {
    invitationSvc.validateAddUserToOrg(pUsername, pOrganization.getId());
    return SimpleApiResponse.ok().build();
  }

  @POST
  @Path("/validateNewUser")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response validateNewUsername(@FormParam("username") String pUsername) throws SvcException {
    invitationSvc.validateUsername(pUsername);
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/v2/search")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ANY_AUTHENTICATED_USER, groupSource = GroupSource.NONE)
  public Response v2Search(
      @Context AppUser pUser,
      @QueryParam("term") String pTerm,
      @QueryParam("ndsOnly") boolean pNdsOnly,
      @QueryParam("sourceGroupId") ObjectId pSourceGroupId,
      @QueryParam("filterAWSCNRegionOnlyGroups") boolean pFilterAWSCNRegionOnlyGroups) {
    boolean isGlobalUser = authzSvc.isGlobalReadOnly(pUser);

    // If the "filterAWSCNRegionOnlyGroups" query parameter is set, we need to ensure that if the
    // source is AWS CN-only, we only return AWS CN-only groups. Alternatively, if the source group
    // is not AWS CN-only, we should filter out AWS CN-only groups.
    boolean sourceGroupUsesCNRegionsOnly =
        pFilterAWSCNRegionOnlyGroups
            && pSourceGroupId != null
            && groupSvc.findById(pSourceGroupId).useCNRegionsOnly();

    Optional<NDSGroup> sourceGroup = ndsGroupSvc.find(pSourceGroupId);

    RegionUsageRestrictions regionUsageRestrictions =
        sourceGroup.isPresent()
            ? sourceGroup.get().getRegionUsageRestrictions()
            : RegionUsageRestrictions.NONE;

    if (StringUtils.isBlank(pTerm)) {
      if (isGlobalUser) {
        return Response.ok().entity(Collections.emptyList()).build();
      } else {
        return Response.ok()
            .entity(
                ndsOrgSvc.getOrgsForNonGlobalUser(
                    pUser,
                    SEARCH_MAX_RETURN_SIZE,
                    pNdsOnly,
                    sourceGroupUsesCNRegionsOnly,
                    pFilterAWSCNRegionOnlyGroups,
                    regionUsageRestrictions))
            .build();
      }
    }
    if (isGlobalUser) {
      if (SalesforceUtils.isValidActivationCode(appSettings.getAppEnv(), pTerm)) {
        // This may be a valid activation code.
        List<OrgInfoView> orgs =
            ndsOrgSvc.searchOrgsByActivationCode(pTerm, pUser, SEARCH_MAX_RETURN_SIZE, pNdsOnly);
        if (!orgs.isEmpty()) {
          return Response.ok().entity(orgs).build();
        }
      }

      if (ObjectId.isValid(pTerm)) {
        // This may be a valid org ID or group ID.
        List<OrgInfoView> orgs =
            ndsOrgSvc.searchOrgsAndGroupsById(
                pTerm,
                pUser,
                SEARCH_MAX_RETURN_SIZE,
                pNdsOnly,
                sourceGroupUsesCNRegionsOnly,
                pFilterAWSCNRegionOnlyGroups,
                regionUsageRestrictions);
        if (!orgs.isEmpty()) {
          return Response.ok().entity(orgs).build();
        }
      }
    }
    List<OrgInfoView> prefixSearchResults =
        organizationSearchSvc.performantSearchOrgsAndGroupsByPrefix(
            pTerm,
            pUser,
            SEARCH_MAX_RETURN_SIZE,
            pNdsOnly,
            sourceGroupUsesCNRegionsOnly,
            pFilterAWSCNRegionOnlyGroups,
            regionUsageRestrictions);
    prefixSearchResults.sort(Comparator.comparing(OrgInfoView::getOrgName));
    // prefix search gives nothing, try full regex search instead
    List<OrgInfoView> toReturn =
        prefixSearchResults.size() > 0
            ? prefixSearchResults
            : organizationSearchSvc.performantSearchOrgsAndGroupsByScan(
                pTerm,
                pUser,
                SEARCH_MAX_RETURN_SIZE,
                pNdsOnly,
                sourceGroupUsesCNRegionsOnly,
                pFilterAWSCNRegionOnlyGroups,
                regionUsageRestrictions);
    return Response.ok().entity(toReturn).build();
  }

  @POST
  @Path("/{orgId}/addMultipleUsers")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  public Response addMultipleUsers(
      @Context AuditInfo pAuditInfo,
      @Context AppUser pRequestUser,
      @Context Organization pOrganization,
      List<UserView> pInvitedUsers)
      throws SvcException {
    List<String> invitedUsernames =
        pInvitedUsers.stream().map(UserView::getUsername).collect(Collectors.toList());
    configLimitSvc.validateMaxUsersPerOrg(
        userSvc.getUsersNotInOrInvitedToOrg(invitedUsernames, pOrganization.getId()).size(),
        pOrganization.getId());
    for (String username : invitedUsernames) {
      configLimitSvc.validateMaxOrgsPerUser(1, username, pOrganization.getId());
    }
    for (UserView user : pInvitedUsers) {
      validateRolesAndPoliciesNotAssignedTogether(user);
      invitationSvc.validateAddUserToOrg(user.getUsername(), pOrganization.getId());
      if (pOrganization.hasEnabledFeatureFlag(FeatureFlag.FINE_GRAINED_AUTH)) {
        validatePoliciesExistInOrg(user.getPolicyAssignments(), pOrganization.getId());
      } else {
        invitationSvc.validateRolesForOrg(user.getRoles(), pOrganization.getId());
      }
    }

    for (UserView userInvited : pInvitedUsers) {
      AppUser userToAdd = userSvc.findByUsername(userInvited.getUsername());
      if (userToAdd != null && userToAdd.hasOrgId(pOrganization.getId())) {
        // User already in the organization! Silently ignore it.
        continue;
      }

      if (userInvited.getUsername().equals(pRequestUser.getUsername())) {
        // If user is inviting self, add user right away.
        userSvc.addUserToOrganization(
            userInvited.getUsername(), pOrganization.getId(), userInvited.getRoles(), pAuditInfo);
        assignPoliciesToUser(userInvited.getUserId(), userInvited.getPolicyAssignments());
        continue;
      }

      if (appSettings.isBypassInvitationsEnabled() && userToAdd != null) {
        // if we are bypassing invitations and the user currently exists, we should add the user
        // immediately.
        userSvc.addUserToOrganization(
            userToAdd, pOrganization.getId(), userInvited.getRoles(), pAuditInfo);
        assignPoliciesToUser(userInvited.getUserId(), userInvited.getPolicyAssignments());
        continue;
      }

      invitationSvc.createOrUpdateInvitation(
          pRequestUser,
          StringUtils.trimToNull(userInvited.getUsername()),
          pOrganization,
          SetUtils.fromList(userInvited.getRoles()),
          userInvited.getPolicyAssignments(),
          userInvited.getTeamIds(),
          pAuditInfo);
    }
    return SimpleApiResponse.ok().build();
  }

  @DELETE
  @Path("/{orgId}/users/self/{username}")
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  public Response deleteSelf(
      @Context Organization pOrganization,
      @Context AppUser pUser,
      @Context AuditInfo pAuditInfo,
      @PathParam("username") String pUsername)
      throws SvcException {
    AppUser user = userSvc.findByUsername(pUsername);
    if (user == null || !user.hasOrgId(pOrganization.getId())) {
      throw new SvcException(AppUserErrorCode.USERNAME_NOT_FOUND);
    }
    if (!pUser.equals(user)) {
      throw new SvcException(AppUserErrorCode.INVALID_USERNAME);
    }
    userSvc.removeUserFromOrganization(user, pOrganization.getId(), pAuditInfo);
    return Response.ok().build();
  }

  @GET
  @Path("/{orgId}/vatStatus")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  public Response getVATStatus(@Context Organization pOrganization) {
    return Response.ok(pOrganization.getVATStatus()).build();
  }

  @PUT
  @Path("/{orgId}/vatStatus")
  @UiCall(roles = RoleSet.ORG_BILLING_ADMIN, groupSource = GroupSource.NONE)
  public Response setVATStatus(@Context Organization pOrganization, VATStatus pVATStatus)
      throws SvcException {
    if (!pVATStatus.equals(VATStatus.DISMISSED)) {
      throw new SvcException(BillingErrorCode.VAT_STATUS_MAY_ONLY_BE_DISMISSED);
    }
    if (pOrganization.getVATStatus().equals(VATStatus.INVALID)) {
      organizationSvc.setVATStatus(pOrganization.getId(), pVATStatus);
    }
    return SimpleApiResponse.ok().build();
  }

  @GET
  @Path("/{orgId}/domainUsageStats")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_MEMBER, groupSource = GroupSource.NONE)
  public Response getDomainUsageStats(@PathParam("orgId") ObjectId pOrgId) {
    return Response.ok(organizationSvc.getOrgUserDomainStats(pOrgId)).build();
  }

  /**
   * @deprecated to be replaced by {@link
   *     com.xgen.cloud.partners.vercel.webhook.runtime.res.VercelWebhookResource#postWebhook
   *     VercelWebhookResource#postWebhook}.
   */
  @Deprecated
  @POST
  @Path("/webhook/vercel/removeIntegration")
  @Consumes(MediaType.APPLICATION_JSON)
  public Response removeVercelIntegrationWebhook(
      @Context HttpServletRequest pRequest, String pPayload, @Context AuditInfo pAuditInfo)
      throws Exception {
    if (organizationVercelIntegrationSvc.validateRemoveIntegrationWebhookRequest(
        pPayload, pRequest.getHeader(VERCEL_SIGNATURE_HEADER))) {
      Optional<Organization> integrationRemovedForOrg =
          organizationVercelIntegrationSvc.handleRemoveIntegration(pPayload, pAuditInfo);

      integrationRemovedForOrg.ifPresent(
          organization -> sendVercelUninstallEmail(organization, pPayload));
    }

    return Response.ok().build();
  }

  @PATCH
  @Feature(FeatureFlag.UI_ACCESS_LIST)
  @Path("/{orgId}/uiAccessList")
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response setOrgUiIpAccessListEnabled(
      @PathParam("orgId") ObjectId pOrgId,
      @Context AppUser appUser,
      @Context AuditInfo auditInfo,
      OrgUiIpAccessListEnableForm form) {
    orgUiIpAccessListSvc.enableUiAccessListForOrg(
        pOrgId, appUser.getId(), appUser.getFirstAndLastName(), form.getEnabled(), auditInfo);
    return Response.ok().build();
  }

  @GET
  @Feature(FeatureFlag.UI_ACCESS_LIST)
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/{orgId}/uiAccessList/validation")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_ORG_READ_ONLY},
      groupSource = GroupSource.NONE)
  public Response validateIpInput(
      @QueryParam("ip") String ipAddress, @Context HttpServletRequest pRequest) {
    ValidateIpAddressView view;
    try {
      orgUiIpAccessListSvc.validateIpAddress(ipAddress);
      boolean includesUsersIpAddress =
          orgUiIpAccessListSvc.includesRequestIpAddress(ipAddress, pRequest.getRemoteAddr());
      view = ValidateIpAddressView.success(includesUsersIpAddress);
    } catch (SvcException exn) {
      view = ValidateIpAddressView.invalidFormat();
    }
    return Response.ok().entity(view).build();
  }

  @GET
  @Feature(FeatureFlag.UI_ACCESS_LIST)
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/{orgId}/uiAccessList")
  @UiCall(
      roles = {RoleSet.ORG_OWNER, RoleSet.GLOBAL_ORG_READ_ONLY},
      groupSource = GroupSource.NONE)
  public Response getOrgIpAccessList(
      @PathParam("orgId") ObjectId pOrgId, @Context HttpServletRequest pRequest) {
    List<OrgUiIpAccessListEntry> entries = orgUiIpAccessListSvc.findIpsByOrgId(pOrgId);
    UiIpAccessListView view = new UiIpAccessListView();
    entries.forEach(
        entry -> {
          boolean includesUserIp =
              orgUiIpAccessListSvc.includesRequestIpAddress(
                  entry.getIpAddress(), pRequest.getRemoteAddr());
          view.addIpEntry(entry, includesUserIp);
        });
    return Response.ok().entity(view).build();
  }

  @POST
  @Feature(FeatureFlag.UI_ACCESS_LIST)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/{orgId}/uiAccessList")
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response enableOrgIpAccessList(
      @Context Organization pOrganization,
      @Context AppUser pAppUser,
      @Context AuditInfo pAuditInfo,
      @Context HttpServletRequest pRequest,
      OrgUiIpAccessListEntryForm form) {
    defaultAlertConfigSvc.addOrgUiIpAccessListAlertConfigsIfMissing(pOrganization, pAuditInfo);
    List<OrgUiIpAccessListEntry> entriesCreated =
        orgUiIpAccessListSvc.addNewIpEntries(
            form,
            pAppUser.getId(),
            pAppUser.getFirstAndLastName(),
            pOrganization.getId(),
            pAuditInfo);
    if (!entriesCreated.isEmpty()) {
      orgUiIpAccessListSvc.enableUiAccessListForOrg(
          pOrganization.getId(),
          pAppUser.getId(),
          pAppUser.getFirstAndLastName(),
          true,
          pAuditInfo);
      return getOrgIpAccessList(pOrganization.getId(), pRequest);
    } else {
      return Response.serverError().build();
    }
  }

  @PUT
  @Feature(FeatureFlag.UI_ACCESS_LIST)
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  @Path("/{orgId}/uiAccessList")
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response updateOrgIpAccessList(
      @PathParam("orgId") ObjectId pOrgId,
      @Context AppUser pAppUser,
      @Context AuditInfo pAuditInfo,
      @Context HttpServletRequest pRequest,
      OrgUiIpAccessListEntryForm form)
      throws SvcException {
    boolean succeed =
        orgUiIpAccessListSvc.editUiIpAccessEntries(
            pOrgId, pAppUser.getId(), pAppUser.getFirstAndLastName(), form, pAuditInfo);
    if (succeed) {
      return getOrgIpAccessList(pOrgId, pRequest);
    } else {
      return Response.serverError().build();
    }
  }

  @POST
  @Path("/{orgId}/setMaxServiceAccountSecretValidityInHours")
  @Feature(FeatureFlag.SERVICE_ACCOUNT_MANAGEMENT_ENABLED)
  @UiCall(roles = RoleSet.ORG_USER_ADMIN, groupSource = GroupSource.NONE)
  public Response setMaxServiceAccountSecretValidityInHours(
      @Context Organization pOrganization,
      @Context AuditInfo pAuditInfo,
      @FormParam("maxSecretValidityInHours") Integer pMaxSecretExpiryAfterHours)
      throws SvcException {

    organizationSvc.validateMaxServiceAccountSecretValidityInHours(pMaxSecretExpiryAfterHours);
    organizationSvc.setMaxServiceAccountSecretValidityInHours(
        pOrganization.getId(), pMaxSecretExpiryAfterHours, pAuditInfo);

    return Response.noContent().build();
  }

  @GET
  @Path("/{orgId}/sandboxConfig")
  @Produces(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response getSandboxConfig(@Context Organization organization) {
    final ObjectId orgId = organization.getId();
    final Optional<SandboxConfig> sandboxConfigOpt = sandboxConfigSvc.getSandboxConfig(orgId);

    return Response.ok(sandboxConfigOpt.orElse(null)).build();
  }

  @POST
  @Path("/{orgId}/sandboxConfig")
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response createSandboxConfig(
      @Context Organization organization, @Context AuditInfo auditInfo) {
    final ObjectId orgId = organization.getId();
    sandboxConfigSvc.createSandboxConfig(orgId, auditInfo);

    final Optional<SandboxConfig> sandboxConfigOpt = sandboxConfigSvc.getSandboxConfig(orgId);
    if (sandboxConfigOpt.isEmpty()) {
      return Response.status(HttpStatus.SC_NOT_FOUND)
          .entity(String.format("Could not find sandbox config with orgId %s.", orgId))
          .build();
    }

    return Response.ok(sandboxConfigOpt.get()).build();
  }

  @PATCH
  @Path("/{orgId}/sandboxConfig")
  @Produces(MediaType.APPLICATION_JSON)
  @Consumes(MediaType.APPLICATION_JSON)
  @UiCall(roles = RoleSet.ORG_OWNER, groupSource = GroupSource.NONE)
  public Response updateSandboxConfig(
      @Context Organization organization,
      @Context AuditInfo auditInfo,
      UpdateSandboxConfigView updateSandboxConfigView) {
    final ObjectId orgId = organization.getId();

    final Optional<SandboxConfig> existingSandboxConfigOpt =
        sandboxConfigSvc.getSandboxConfig(orgId);
    if (existingSandboxConfigOpt.isEmpty()) {
      return Response.status(HttpStatus.SC_NOT_FOUND)
          .entity(String.format("Could not find sandbox config with orgId %s.", orgId))
          .build();
    }

    // Only one of these values will be true per request
    final boolean shouldUpdateClusterTemplate =
        updateSandboxConfigView.getClusterTemplate() != null;
    final boolean shouldUpdateIsSandboxEnabled =
        updateSandboxConfigView.getIsSandboxEnabled() != null;

    if (shouldUpdateIsSandboxEnabled) {
      // Only try to update if different from current value
      if (updateSandboxConfigView.getIsSandboxEnabled()
          != existingSandboxConfigOpt.get().getIsSandboxEnabled()) {
        sandboxConfigSvc.updateIsSandboxEnabled(
            orgId, updateSandboxConfigView.getIsSandboxEnabled(), auditInfo);
      }
    }

    if (shouldUpdateClusterTemplate) {
      sandboxConfigSvc.updateClusterTemplate(orgId, updateSandboxConfigView.getClusterTemplate());
    }

    final Optional<SandboxConfig> updatedSandboxConfigOpt =
        sandboxConfigSvc.getSandboxConfig(orgId);
    if (updatedSandboxConfigOpt.isEmpty()) {
      return Response.status(HttpStatus.SC_NOT_FOUND)
          .entity(String.format("Could not find sandbox config with orgId %s.", orgId))
          .build();
    }

    return Response.ok(updatedSandboxConfigOpt.get()).build();
  }

  private void assignPoliciesToUser(String userId, List<PolicyAssignmentView> assignments)
      throws AuthzServiceClientException {
    if (CollectionUtils.isEmpty(assignments)) {
      return;
    }
    actorClient.editPolicyAssignments(
        userId,
        assignments.stream().map(ConversionUtils::mapPolicyAssignmentViewToMessage).toList(),
        Collections.emptyList());
  }

  private void validatePoliciesExistInOrg(List<PolicyAssignmentView> assignments, ObjectId orgId)
      throws AuthzServiceClientException {
    if (CollectionUtils.isEmpty(assignments)) {
      return;
    }
    policyClient.validatePoliciesExistInOrg(assignments, orgId);
  }

  private void validateRolesAndPoliciesNotAssignedTogether(UserView user) throws SvcException {
    if (user.getPolicyAssignments() != null
        && !user.getPolicyAssignments().isEmpty()
        && user.getRoles() != null
        && !user.getRoles().isEmpty()) {
      throw new SvcException(AppUserErrorCode.CANNOT_ASSIGN_ROLE_AND_POLICY_TOGETHER);
    }
  }

  private AuthorizationClient getAuthorizationClient() {
    return authorizationClientProvider.get();
  }

  private ResourceClient getResourceClient() {
    return resourceClientProvider.get();
  }

  private void sendVercelUninstallEmail(Organization pOrganization, String pPayload) {
    // This shouldn't happen due to the checks in OrganizationVercelIntegrationSvc
    if (pOrganization.getVercelInfo() == null) {
      LOG.warn(
          "Could not send Vercel uninstall email due to org {} VercelInfo being null",
          pOrganization.getId());
      return;
    }
    String initiatorUserId = new RemoveVercelIntegrationPayload(pPayload).getUserId();
    // send email to all org owners
    Set<String> orgOwnerEmails =
        userSvc.findLocalOwnersByOrgId(pOrganization.getId()).stream()
            .map(AppUser::getPrimaryEmail)
            .collect(Collectors.toSet());
    vercelEmailSvc.sendIntegrationUninstalledEmail(
        initiatorUserId, pOrganization.getVercelInfo(), pOrganization, orgOwnerEmails);
  }
}
