package com.xgen.cloud.search.decoupled.autoembedding._public.model;

import java.util.List;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonProperty;

/**
 * Models the v1 schema of an auto embedding provider for Atlas Search.
 *
 * @param id unique ID for the autoembedding.
 * @param providerParams the autoembedding's workload provider parameters.
 */
public record AutoEmbeddingV1(
    @BsonId Id id, @BsonProperty(FieldDefs.PROVIDER_PARAMS) ProviderParams providerParams) {

  public static final String DEFAULT_MODEL_NAME = "VOYAGE-3-LARGE";

  public AutoEmbeddingV1 withProviderParams(ProviderParams providerParams) {
    return new AutoEmbeddingV1(id, providerParams);
  }

  /**
   * Uniquely identifies autoembedding instances.
   *
   * @param embeddingProvider the embedding provider for the underlying model.
   * @param embeddingModelName the embedding model to use.
   */
  public record Id(
      @BsonProperty(FieldDefs.EMBEDDING_PROVIDER) EmbeddingProvider embeddingProvider,
      @BsonProperty(FieldDefs.MODEL_NAME) String embeddingModelName,
      @BsonProperty(FieldDefs.VERSION) Version version) {

    /**
     * Constructs an {@link Id} from a {@link EmbeddingProvider}, embedding model and {@link
     * Version}.
     *
     * @param embeddingModelName the embedding name.
     */
    public static Id fromEmbeddingModelNameDefaultProvider(String embeddingModelName) {
      return new Id(EmbeddingProvider.VOYAGE, embeddingModelName, Version.V1);
    }
  }

  /** Holds workload service parameter specific information for the autoembedding */
  public record ProviderParams(
      @BsonProperty(FieldDefs.IS_DEFAULT_MODEL) Boolean isDefaultModel,
      @BsonProperty(FieldDefs.REGION) List<String> region,
      @BsonProperty(FieldDefs.MODEL_CONFIG) ModelConfig modelConfig,
      @BsonProperty(FieldDefs.ERROR_HANDLING_CONFIG) ErrorHandlingConfig errorHandlingConfig,
      @BsonProperty(FieldDefs.CREDENTIALS) EmbeddingCredentials credentials,
      @BsonProperty(FieldDefs.QUERY_WORKLOAD) WorkloadParams query,
      @BsonProperty(FieldDefs.CHANGE_STREAM_WORKLOAD) WorkloadParams changeStream,
      @BsonProperty(FieldDefs.COLLECTION_SCAN_WORKLOAD) WorkloadParams collectionScan) {
    /**
     * Creates a new embedding specific service param
     *
     * @param region of the embedding
     * @param modelConfig the configuration for the underlying embedding model
     * @param errorHandlingConfig the configuration used in error handling
     * @param credentials the credentials used for the embedding provider
     * @param query query workload information
     * @param changeStream change stream workload information
     * @param collectionScan collection scan workload information
     * @return {@link ProviderParams} for the auto embedding
     */
    public static ProviderParams create(
        Boolean isDefaultModel,
        List<String> region,
        ModelConfig modelConfig,
        ErrorHandlingConfig errorHandlingConfig,
        EmbeddingCredentials credentials,
        WorkloadParams query,
        WorkloadParams changeStream,
        WorkloadParams collectionScan) {
      return new ProviderParams(
          isDefaultModel,
          region,
          modelConfig,
          errorHandlingConfig,
          credentials,
          query,
          changeStream,
          collectionScan);
    }

    public ProviderParams withDefault(Boolean isDefaultModel) {
      return new ProviderParams(
          isDefaultModel,
          region,
          modelConfig,
          errorHandlingConfig,
          credentials,
          query,
          changeStream,
          collectionScan);
    }

    @BsonDiscriminator
    public interface ModelConfig {}

    @BsonDiscriminator("config_voyage")
    public static class DefaultModelConfig implements ModelConfig {
      @BsonProperty(FieldDefs.OUTPUT_DIMENSIONS)
      public OutputDimensions dimensions;

      @BsonProperty(FieldDefs.TRUNCATION)
      public TruncationOption truncation;

      @BsonProperty(FieldDefs.BATCH_SIZE)
      public Integer batchSize;

      @BsonProperty(FieldDefs.BATCH_TOKEN_LIMIT)
      public Integer batchTokenLimit;

      // Required for BSON deserialization
      public DefaultModelConfig() {}

      public DefaultModelConfig(
          OutputDimensions dimensions,
          TruncationOption truncation,
          Integer batchSize,
          Integer batchTokenLimit) {
        this.dimensions = dimensions;
        this.truncation = truncation;
        this.batchSize = batchSize;
        this.batchTokenLimit = batchTokenLimit;
      }

      public OutputDimensions getDimensions() {
        return dimensions;
      }

      public TruncationOption getTruncation() {
        return truncation;
      }

      public Integer getBatchSize() {
        return batchSize;
      }

      public Integer getBatchTokenLimit() {
        return batchTokenLimit;
      }

      public record OutputDimensions(
          @BsonProperty(FieldDefs.DIMENSION_VALUES) List<Integer> dimensions,
          @BsonProperty(FieldDefs.DEFAULT_DIMENSION) Integer defaultDimension) {
        public static OutputDimensions create(List<Integer> dimensions, Integer defaultDimension) {
          return new OutputDimensions(dimensions, defaultDimension);
        }

        // Very basic validation to ensure we have at least one dimension
        public Integer getDimension() {
          if (defaultDimension != null) {
            return defaultDimension;
          } else if (dimensions != null && !dimensions.isEmpty()) {
            return dimensions.get(0);
          } else {
            throw new IllegalArgumentException("No default dimension specified.");
          }
        }
      }
    }

    public record ErrorHandlingConfig(
        @BsonProperty(FieldDefs.MAX_RETRIES) Integer maxRetries,
        @BsonProperty(FieldDefs.INITIAL_RETRY_WAIT) Integer initialRetryWaitMs,
        @BsonProperty(FieldDefs.MAX_RETRY_WAIT) Integer maxRetryWaitMs,
        @BsonProperty(FieldDefs.JITTER) Double jitter) {
      public static ErrorHandlingConfig create(
          Integer maxRetries, Integer initialRetryWaitMs, Integer maxRetryWaitMs, Double jitter) {
        return new ErrorHandlingConfig(maxRetries, initialRetryWaitMs, maxRetryWaitMs, jitter);
      }
    }

    @BsonDiscriminator
    public interface EmbeddingCredentials {}

    @BsonDiscriminator("credential_voyage")
    public static class VoyageEmbeddingCredentials implements EmbeddingCredentials {
      @BsonProperty(FieldDefs.TOKEN)
      public String apiTokenVar;

      // Required for BSON deserialization
      public VoyageEmbeddingCredentials() {}

      public VoyageEmbeddingCredentials(String apiTokenVar) {
        this.apiTokenVar = apiTokenVar;
      }

      public String getApiTokenVar() {
        return apiTokenVar;
      }
    }

    @BsonDiscriminator("credential_aws")
    public static class AwsEmbeddingCredentials implements EmbeddingCredentials {
      @BsonProperty(FieldDefs.ASSUMED_ROLE_ARN)
      public String assumedRoleArn;

      // Required for BSON deserialization
      public AwsEmbeddingCredentials() {}

      public AwsEmbeddingCredentials(String assumedRoleArn) {
        this.assumedRoleArn = assumedRoleArn;
      }
    }

    public record WorkloadParams(
        @BsonProperty(FieldDefs.REGION) List<String> region,
        @BsonProperty(FieldDefs.MODEL_CONFIG) ModelConfig modelConfig,
        @BsonProperty(FieldDefs.ERROR_HANDLING_CONFIG) ErrorHandlingConfig errorHandlingConfig,
        @BsonProperty(FieldDefs.CREDENTIALS) EmbeddingCredentials credentials) {}
  }

  /** Defines the BSON fields for serialization and deserialization. */
  public static class FieldDefs {
    public static final String ID = "_id";
    public static final String EMBEDDING_PROVIDER = "embeddingProvider";
    public static final String MODEL_NAME = "modelName";
    public static final String VERSION = "version";

    public static final String PROVIDER_PARAMS = "embeddingProviderParams";
    public static final String IS_DEFAULT_MODEL = "isDefault";
    public static final String REGION = "region";

    public static final String MODEL_CONFIG = "modelConfig";
    public static final String DIMENSION_VALUES = "values";
    public static final String DEFAULT_DIMENSION = "default";
    public static final String TRUNCATION = "truncation";
    public static final String BATCH_SIZE = "batchSize";
    public static final String BATCH_TOKEN_LIMIT = "batchTokenLimit";

    public static final String ERROR_HANDLING_CONFIG = "errorHandlingConfig";
    public static final String MAX_RETRIES = "maxRetries";
    public static final String INITIAL_RETRY_WAIT = "initialRetryWaitMs";
    public static final String MAX_RETRY_WAIT = "maxRetryWaitMs";
    public static final String JITTER = "jitter";
    public static final String OUTPUT_DIMENSIONS = "outputDimensions";

    public static final String CREDENTIALS = "credentials";
    public static final String TOKEN = "apiTokenVar";
    public static final String ASSUMED_ROLE_ARN = "assumedRoleArn";

    public static final String QUERY_WORKLOAD = "query";
    public static final String CHANGE_STREAM_WORKLOAD = "changeStream";
    public static final String COLLECTION_SCAN_WORKLOAD = "collectionScan";
  }

  public enum EmbeddingProvider {
    VOYAGE,
    AWS
  }

  public enum Version {
    V1
  }

  public enum TruncationOption {
    NONE,
    START,
    END
  }
}
