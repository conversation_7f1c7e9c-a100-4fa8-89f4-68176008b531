package com.xgen.cloud.services.authz.runtime.guice;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.xgen.cloud.services.authz._private.model.Actor;
import com.xgen.cloud.services.authz._private.model.ResourceId;
import com.xgen.cloud.services.authz._private.model.UserGroup;
import com.xgen.cloud.services.authz._private.svc.ActorCacheLoader;
import com.xgen.cloud.services.authz._private.svc.ResourceFamilyCacheBulkLoader;
import com.xgen.cloud.services.authz._private.svc.ResourceFamilyCacheLoader;
import com.xgen.cloud.services.authz._private.svc.UserGroupCacheLoader;
import com.xgen.cloud.services.authz._private.svc.externalcache.redis.Cache;
import com.xgen.cloud.services.authz._private.svc.externalcache.redis.DefaultCacheSettings;
import com.xgen.cloud.services.authz._private.svc.externalcache.redis.MemoryBackedReadThroughRedisCache;
import com.xgen.cloud.services.authz._private.svc.externalcache.redis.RedisCacheClient;
import com.xgen.cloud.services.authz._private.svc.externalcache.redis.RedisClientSettings;
import com.xgen.cloud.services.authz._private.svc.serialization.DefaultSerializer;
import com.xgen.cloud.services.authz._private.svc.serialization.KryoSerializer;
import com.xgen.cloud.services.authz._private.svc.serialization.Lz4CompressionSerializer;
import com.xgen.cloud.services.authz._private.svc.serialization.Serializer;
import com.xgen.cloud.services.authz._private.svc.serialization.SerializerType;
import com.xgen.cloud.services.authz._private.svc.serialization.TimedSerializer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;
import java.time.Clock;
import java.time.Duration;
import java.util.ArrayList;
import org.apache.commons.configuration2.ImmutableConfiguration;
import org.bson.types.ObjectId;

public class CacheModule extends AbstractModule {

  @Provides
  @Singleton
  public Cache<String, Boolean> provideAuthorizationCache(
      ImmutableConfiguration settings,
      Provider<RedisCacheClient<byte[], byte[]>> redisCacheClientProvider,
      MeterRegistry meterRegistry,
      Serializer serializer,
      Clock clock) {
    return new MemoryBackedReadThroughRedisCache<>(
        redisCacheClientProvider,
        new AuthResultCacheSettings(settings),
        null, // we will only use in-line supplier
        null, // we will only use in-line function
        meterRegistry,
        serializer,
        clock);
  }

  @Provides
  @Singleton
  public Cache<String, Actor> provideActorCache(
      ImmutableConfiguration settings,
      Provider<RedisCacheClient<byte[], byte[]>> redisCacheClientProvider,
      ActorCacheLoader cacheLoader,
      MeterRegistry meterRegistry,
      Serializer serializer,
      Clock clock) {
    return new MemoryBackedReadThroughRedisCache<>(
        redisCacheClientProvider,
        new ActorCacheSettings(settings),
        cacheLoader,
        null, // we only read one actor at a time currently
        meterRegistry,
        serializer,
        clock);
  }

  @Provides
  @Singleton
  public Cache<ObjectId, UserGroup> provideUserGroupCache(
      ImmutableConfiguration settings,
      Provider<RedisCacheClient<byte[], byte[]>> redisCacheClientProvider,
      UserGroupCacheLoader cacheLoader,
      MeterRegistry meterRegistry,
      Serializer serializer,
      Clock clock) {
    return new MemoryBackedReadThroughRedisCache<>(
        redisCacheClientProvider,
        new UserGroupCacheSettings(settings),
        null, // we only do bulk readers at the moment
        cacheLoader,
        meterRegistry,
        serializer,
        clock);
  }

  @Provides
  @Singleton
  public Cache<ResourceId, ArrayList<ResourceId>> providerResourceFamilyCache(
      ImmutableConfiguration settings,
      Provider<RedisCacheClient<byte[], byte[]>> redisCacheClientProvider,
      ResourceFamilyCacheLoader cacheLoader,
      ResourceFamilyCacheBulkLoader bulkCacheLoader,
      MeterRegistry meterRegistry,
      Serializer serializer,
      Clock clock) {
    return new MemoryBackedReadThroughRedisCache<>(
        redisCacheClientProvider,
        new ResourceFamilyCacheSettings(settings),
        cacheLoader,
        bulkCacheLoader,
        meterRegistry,
        serializer,
        clock);
  }

  /*
   * These cache key and value types are likely to be needed elsewhere
   * in the future. At which point, we can get rid of the test cache or
   * we can move to named injection
   */
  @Provides
  @Singleton
  public Cache<String, String> provideTestCache(
      ImmutableConfiguration settings,
      Provider<RedisCacheClient<byte[], byte[]>> redisCacheClientProvider,
      MeterRegistry meterRegistry,
      Serializer serializer,
      Clock clock) {
    return new MemoryBackedReadThroughRedisCache<>(
        redisCacheClientProvider,
        new TestCacheSettings(settings),
        key -> key + "-value",
        null, // we have no bulk loads at this point
        meterRegistry,
        serializer,
        clock);
  }

  @Provides
  @Singleton
  public Serializer provideSerializer(RedisClientSettings redisSettings) {
    SerializerType serializerType = redisSettings.getSerializerType();

    Serializer base =
        switch (serializerType) {
          case LZ4_FRAME -> new Lz4CompressionSerializer();
          case KRYO -> new KryoSerializer();
          case DEFAULT -> new DefaultSerializer();
        };

    // decorate serializer to get metrics
    return new TimedSerializer(base, serializerType);
  }

  private static class AuthResultCacheSettings extends DefaultCacheSettings {

    public AuthResultCacheSettings(ImmutableConfiguration appSettings) {
      super("authzResultCache", appSettings);
    }

    @Override
    public String cacheKeyPrefix() {
      return "authResult:";
    }

    @Override
    public String metricLabel() {
      return "authz_result_cache";
    }
  }

  private static class ActorCacheSettings extends DefaultCacheSettings {

    public ActorCacheSettings(ImmutableConfiguration appSettings) {
      super("actorCache", appSettings);
    }

    @Override
    public String cacheKeyPrefix() {
      return "actor:";
    }

    @Override
    public String metricLabel() {
      return "actor_cache";
    }
  }

  private static class UserGroupCacheSettings extends DefaultCacheSettings {

    @Inject
    public UserGroupCacheSettings(ImmutableConfiguration appSettings) {
      super("userGroupCache", appSettings);
    }

    @Override
    public String cacheKeyPrefix() {
      return "usergroup:";
    }

    @Override
    public String metricLabel() {
      return "user_group_cache";
    }
  }

  private static class ResourceFamilyCacheSettings extends DefaultCacheSettings {

    public ResourceFamilyCacheSettings(ImmutableConfiguration appSettings) {
      super("resourceFamilyCache", appSettings);
    }

    @Override
    public Duration localCacheTtl() {
      return getDuration(cacheName + ".localCache.ttl.base", Duration.ofMinutes(30));
    }

    @Override
    public long localCacheMaxCapacity() {
      return appSettings.getLong(cacheName + ".localCache.capacity.max", 100_000);
    }

    @Override
    public Duration externalCacheTtl() {
      return getDuration(cacheName + ".external.ttl.base", Duration.ofMinutes(15));
    }

    @Override
    public String cacheKeyPrefix() {
      return "resourceFamily:";
    }

    @Override
    public String metricLabel() {
      return "resource_family_cache";
    }
  }

  private static class TestCacheSettings extends DefaultCacheSettings {

    public TestCacheSettings(ImmutableConfiguration appSettings) {
      super("testCache", appSettings);
    }

    @Override
    public String cacheKeyPrefix() {
      return "testCache:";
    }

    @Override
    public String metricLabel() {
      return "test_cache";
    }
  }
}
