package com.xgen.cloud.services.communication._private.model.enums;

import com.xgen.cloud.services.communication._private.model.telecom.TwilioStatus;
import com.xgen.cloud.services.communication._private.model.telecom.TwilioVoiceStatus;

/**
 * The discriminator for various communication providers
 *
 * <p>Typical Flows should be
 *
 * <p>For async requests: CREATED -> REQUESTED -> PENDING -> SUCCEEDED, or CREATED -> REQUESTED ->
 * PENDING -> FAILED
 *
 * <p>For sync requests: CREATED -> REQUESTED -> SUCCEEDED, or CREATED -> REQUESTED -> FAILED
 *
 * <p>Although there is also an UNKNOWN category for the case where something unknown occurs.
 */
public enum Status {
  CREATED, // Internal message has been created. Initial state for all messages.
  FETCHED, // The message has been fetched for bulk sending. This status only occurs on bulk send.
  REQUESTED, // The third party provider has received a request to send.
  PENDING, // The message is being sent by the third party provider.
  FAILED, // The third party provider failed to send the message.
  SUCCEEDED, // The third party provider succeeded in sending the message.
  UNKNOWN, // We do not know the result of the third party provider.
  READ, // We have confirmation that the user has opened the message.
  UNSPECIFIED; // For symmetry when transforming to and from protos.

  public static Status fromProtoStatus(
      final com.xgen.cloud.services.communication.proto.Status protoStatus) {
    if (protoStatus == null) {
      return null;
    }
    return valueOf(protoStatus.name().replaceFirst("STATUS_", ""));
  }

  public static Status fromTwilioStatus(final TwilioStatus twilioStatus) {
    return switch (twilioStatus) {
      case ACCEPTED, QUEUED, SCHEDULED, SENT -> Status.PENDING;
      case FAILED, UNDELIVERED -> Status.FAILED;
      case DELIVERED, READ -> Status.SUCCEEDED;
      case UNKNOWN -> Status.UNKNOWN;
    };
  }

  public static Status fromTwilioVoiceStatus(final TwilioVoiceStatus twilioStatus) {
    return switch (twilioStatus) {
      case QUEUED, RINGING -> Status.PENDING;
      case BUSY, FAILED, NO_ANSWER, CANCELED -> Status.FAILED;
      case IN_PROGRESS, COMPLETED -> Status.SUCCEEDED;
      case UNKNOWN -> Status.UNKNOWN;
    };
  }
}
