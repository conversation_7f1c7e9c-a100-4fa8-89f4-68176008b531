package com.xgen.cloud.services.communication._private.model.enums;

public class CommunicationException extends RuntimeException {
  private static final long serialVersionUID = 1L;
  private final ErrorCode errorCode;

  public CommunicationException(final ErrorCode errorCode) {
    super(errorCode.getUserReadableMessage());
    this.errorCode = errorCode;
  }

  public ErrorCode getErrorCode() {
    return errorCode;
  }
}
