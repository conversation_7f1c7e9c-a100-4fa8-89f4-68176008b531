package com.xgen.cloud.services.communication._private.model.enums;

/** The discriminator for various communication providers */
public enum ProviderType {
  DATADOG,
  EMAIL,
  HIP_CHAT,
  LOG,
  MICROSOFT_TEAMS,
  OPS_GENIE,
  PAGERDUTY,
  SLACK,
  SMS,
  VICTOR_OPS,
  VOICE,
  WEBHOOK;

  /** Integration */
  public static final String INTEGRATION_DATADOG = "INTEGRATION_DATADOG";

  public static final String INTEGRATION_EMAIL = "INTEGRATION_EMAIL";
  public static final String INTEGRATION_HIP_CHAT = "INTEGRATION_HIP_CHAT";
  public static final String INTEGRATION_LOG = "INTEGRATION_LOG";
  public static final String INTEGRATION_MICROSOFT_TEAMS = "INTEGRATION_MICROSOFT_TEAMS";
  public static final String INTEGRATION_OPS_GENIE = "INTEGRATION_OPS_GENIE";
  public static final String INTEGRATION_PAGERDUTY = "INTEGRATION_PAGERDUTY";
  public static final String INTEGRATION_SLACK = "INTEGRATION_SLACK";
  public static final String INTEGRATION_SMS = "INTEGRATION_SMS";
  public static final String INTEGRATION_VICTOR_OPS = "INTEGRATION_VICTOR_OPS";
  public static final String INTEGRATION_VOICE = "INTEGRATION_VOICE";
  public static final String INTEGRATION_WEBHOOK = "INTEGRATION_WEBHOOK";

  /** Message */
  public static final String MESSAGE_DATADOG = "MESSAGE_DATADOG";

  public static final String MESSAGE_EMAIL = "MESSAGE_EMAIL";
  public static final String MESSAGE_HIP_CHAT = "MESSAGE_HIP_CHAT";
  public static final String MESSAGE_LOG = "MESSAGE_LOG";
  public static final String MESSAGE_MICROSOFT_TEAMS = "MESSAGE_MICROSOFT_TEAMS";
  public static final String MESSAGE_OPS_GENIE = "MESSAGE_OPS_GENIE";
  public static final String MESSAGE_PAGERDUTY = "MESSAGE_PAGERDUTY";
  public static final String MESSAGE_SLACK = "MESSAGE_SLACK";
  public static final String MESSAGE_SMS = "MESSAGE_SMS";
  public static final String MESSAGE_VICTOR_OPS = "MESSAGE_VICTOR_OPS";
  public static final String MESSAGE_VOICE = "MESSAGE_VOICE";
  public static final String MESSAGE_WEBHOOK = "MESSAGE_WEBHOOK";

  /** Credential */
  public static final String CREDENTIALS_DATADOG = "CREDENTIALS_DATADOG";

  public static final String CREDENTIALS_EMAIL = "CREDENTIALS_EMAIL";
  public static final String CREDENTIALS_HIP_CHAT = "CREDENTIALS_HIP_CHAT";
  public static final String CREDENTIALS_LOG = "CREDENTIALS_LOG";
  public static final String CREDENTIALS_MICROSOFT_TEAMS = "CREDENTIALS_MICROSOFT_TEAMS";
  public static final String CREDENTIALS_OPS_GENIE = "CREDENTIALS_OPS_GENIE";
  public static final String CREDENTIALS_PAGERDUTY = "CREDENTIALS_PAGERDUTY";
  public static final String CREDENTIALS_SLACK = "CREDENTIALS_SLACK";
  public static final String CREDENTIALS_SMS = "CREDENTIALS_SMS";
  public static final String CREDENTIALS_VICTOR_OPS = "CREDENTIALS_VICTOR_OPS";
  public static final String CREDENTIALS_VOICE = "CREDENTIALS_VOICE";
  public static final String CREDENTIALS_WEBHOOK = "CREDENTIALS_WEBHOOK";

  /** Channel */
  public static final String CHANNEL_DATADOG = "CHANNEL_DATADOG";

  public static final String CHANNEL_EMAIL = "CHANNEL_EMAIL";
  public static final String CHANNEL_HIP_CHAT = "CHANNEL_HIP_CHAT";
  public static final String CHANNEL_LOG = "CHANNEL_LOG";
  public static final String CHANNEL_MICROSOFT_TEAMS = "CHANNEL_MICROSOFT_TEAMS";
  public static final String CHANNEL_OPS_GENIE = "CHANNEL_OPS_GENIE";
  public static final String CHANNEL_PAGERDUTY = "CHANNEL_PAGERDUTY";
  public static final String CHANNEL_SLACK = "CHANNEL_SLACK";
  public static final String CHANNEL_SMS = "CHANNEL_SMS";
  public static final String CHANNEL_VICTOR_OPS = "CHANNEL_VICTOR_OPS";
  public static final String CHANNEL_VOICE = "CHANNEL_VOICE";
  public static final String CHANNEL_WEBHOOK = "CHANNEL_WEBHOOK";

  public static ProviderType fromLegacyIntegrationString(final String legacyIntegrationString) {
    if ("PAGER_DUTY".equals(legacyIntegrationString)) {
      return ProviderType.PAGERDUTY;
    }
    return switch (legacyIntegrationString) {
      case "DATADOG",
              "EMAIL",
              "HIP_CHAT",
              "LOG",
              "MICROSOFT_TEAMS",
              "OPS_GENIE",
              "SLACK",
              "SMS",
              "VICTOR_OPS",
              "VOICE",
              "WEBHOOK" ->
          ProviderType.valueOf(legacyIntegrationString);
      default -> null;
    };
  }

  public static ProviderType fromProtoProviderType(
      final com.xgen.cloud.services.communication.proto.ProviderType protoProviderType) {
    if (protoProviderType == null) {
      return null;
    }
    return valueOf(protoProviderType.name().replaceFirst("PROVIDER_TYPE_", ""));
  }

  public com.xgen.cloud.services.communication.proto.ProviderType toProtoProviderType() {
    return com.xgen.cloud.services.communication.proto.ProviderType.valueOf(
        "PROVIDER_TYPE_" + name());
  }

  // TLDR is that BsonDiscriminator doesn't work with enums, so we need to have strings that just
  // happen to equal enums to enable polymorphism. :sigh:
  //
  // And they need to be unique by use case. Just spiffy.
  //
  // But what this means is that for each enum, we not only need to include a completely identical
  // final static String corresponding to each enum, but we need to further namespace each one for
  // each model that it's on. https://jira.mongodb.org/browse/JAVA-5566 will fix part of this.
  public String toDbString(final ProviderUseCase useCase) {
    return useCase.name() + "_" + this.name();
  }

  public String toLegacyIntegrationString() {
    if (ProviderType.PAGERDUTY.equals(this)) {
      return "PAGER_DUTY";
    }
    return this.name();
  }
}
