package com.xgen.cloud.services.communication.runtime.grpc;

import com.google.protobuf.Empty;
import com.xgen.cloud.services.communication._private.job.SenderJobHandlerExecutor;
import com.xgen.cloud.services.communication._private.model.enums.ProviderType;
import com.xgen.cloud.services.communication._private.svc.CommunicationTestingSvc;
import com.xgen.cloud.services.communication._private.svc.ExternalMessageDataSvc;
import com.xgen.cloud.services.communication._private.svc.PublisherSvc;
import com.xgen.cloud.services.communication._private.svc.RestrictionSvc;
import com.xgen.cloud.services.communication._private.svc.StatusSvc;
import com.xgen.cloud.services.communication.proto.CommunicationServiceGrpc.CommunicationServiceImplBase;
import com.xgen.cloud.services.communication.proto.CreatePhoneNumberRestrictionRequest;
import com.xgen.cloud.services.communication.proto.GetIntegrationValidityForProjectRequest;
import com.xgen.cloud.services.communication.proto.GetIntegrationValidityForProjectResponse;
import com.xgen.cloud.services.communication.proto.GetMessagesRequest;
import com.xgen.cloud.services.communication.proto.GetMessagesResponse;
import com.xgen.cloud.services.communication.proto.PublishMessageRequest;
import com.xgen.cloud.services.communication.proto.PublishMessageResponse;
import com.xgen.cloud.services.communication.proto.PublishTestMessageRequest;
import com.xgen.cloud.services.communication.proto.PublishTestMessageResponse;
import com.xgen.cloud.services.communication.proto.RemovePhoneNumberRestrictionRequest;
import com.xgen.cloud.services.communication.proto.SearchRestrictedPhoneNumbersRequest;
import com.xgen.cloud.services.communication.proto.SearchRestrictedPhoneNumbersResponse;
import com.xgen.cloud.services.communication.proto.SendMessageBatchRequest;
import com.xgen.cloud.services.communication.proto.UpdateStatusByExternalIdRequest;
import com.xgen.cloud.services.communication.proto.UpdateStatusRequest;
import com.xgen.cloud.services.core.error._public.BasicServiceErrors;
import com.xgen.cloud.services.core.error._public.ServiceException;
import io.grpc.stub.StreamObserver;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class CommunicationServiceImpl extends CommunicationServiceImplBase {
  private static final Logger LOG = LoggerFactory.getLogger(CommunicationServiceImpl.class);
  private final PublisherSvc publisherSvc;
  private final CommunicationTestingSvc communicationTestingSvc;
  private final ExternalMessageDataSvc externalMessageDataSvc;
  private final RestrictionSvc smsRestrictionSvc;
  private final StatusSvc statusSvc;
  private final SenderJobHandlerExecutor senderJobHandlerExecutor;

  @Inject
  public CommunicationServiceImpl(
      final PublisherSvc publisherSvc,
      final CommunicationTestingSvc communicationTestingSvc,
      final ExternalMessageDataSvc externalMessageDataSvc,
      final RestrictionSvc smsRestrictionSvc,
      final StatusSvc statusSvc,
      final SenderJobHandlerExecutor senderJobHandlerExecutor) {
    this.publisherSvc = publisherSvc;
    this.communicationTestingSvc = communicationTestingSvc;
    this.externalMessageDataSvc = externalMessageDataSvc;
    this.smsRestrictionSvc = smsRestrictionSvc;
    this.statusSvc = statusSvc;
    this.senderJobHandlerExecutor = senderJobHandlerExecutor;
  }

  private void throwWrappedServiceInternalException(
      final String methodName, final Exception exception) {
    final ObjectId traceId = new ObjectId();
    final String methodAndTrace =
        String.format("methodName=\"%s\" traceId=\"%s\"", methodName, traceId);
    // Log the full error internally.
    LOG.error("COMMUNICATION_SERVICE_EXCEPTION {}", methodAndTrace, exception);
    // Send an abbreviated error message that's guaranteed to not have credentials or anything back
    // to the client.
    throw new ServiceException(
        BasicServiceErrors.INTERNAL, "COMMUNICATION_SERVICE_EXCEPTION_EXTERNAL " + methodAndTrace);
  }

  @Override
  public void publishMessage(
      final PublishMessageRequest publishMessageRequest,
      final StreamObserver<PublishMessageResponse> responseObserver) {
    try {
      final ObjectId messageId = publisherSvc.publish(publishMessageRequest);
      responseObserver.onNext(
          PublishMessageResponse.newBuilder().setId(messageId.toHexString()).build());
      responseObserver.onCompleted();
    } catch (final Exception exception) {
      throwWrappedServiceInternalException("publishMessage", exception);
    }
  }

  @Override
  public void publishTestMessage(
      final PublishTestMessageRequest publishMessageRequest,
      final StreamObserver<PublishTestMessageResponse> responseObserver) {
    try {
      final ObjectId messageId =
          communicationTestingSvc.publishTestMessage(
              ProviderType.fromProtoProviderType(publishMessageRequest.getProviderType()));
      responseObserver.onNext(
          PublishTestMessageResponse.newBuilder().setId(messageId.toHexString()).build());
      responseObserver.onCompleted();
    } catch (final Exception exception) {
      throwWrappedServiceInternalException("publishTestMessage", exception);
    }
  }

  @Override
  public void getMessages(
      final GetMessagesRequest getMessagesRequest,
      final StreamObserver<GetMessagesResponse> responseObserver) {
    try {
      final GetMessagesResponse response = externalMessageDataSvc.search(getMessagesRequest);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (final Exception exception) {
      throwWrappedServiceInternalException("getMessages", exception);
    }
  }

  @Override
  public void getIntegrationValidityForProject(
      final GetIntegrationValidityForProjectRequest request,
      final StreamObserver<GetIntegrationValidityForProjectResponse> responseObserver) {
    try {
      final GetIntegrationValidityForProjectResponse response =
          externalMessageDataSvc.validate(request);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (final Exception exception) {
      throwWrappedServiceInternalException("getMessages", exception);
    }
  }

  @Override
  public void searchRestrictedPhoneNumbers(
      final SearchRestrictedPhoneNumbersRequest request,
      final StreamObserver<SearchRestrictedPhoneNumbersResponse> responseObserver) {
    try {
      final SearchRestrictedPhoneNumbersResponse response =
          smsRestrictionSvc.searchRestrictedPhoneNumbers(request);
      responseObserver.onNext(response);
      responseObserver.onCompleted();
    } catch (final Exception exception) {
      throwWrappedServiceInternalException("searchRestrictedPhoneNumbers", exception);
    }
  }

  @Override
  public void createPhoneNumberRestriction(
      final CreatePhoneNumberRestrictionRequest request,
      final StreamObserver<Empty> responseObserver) {
    try {
      smsRestrictionSvc.createPhoneNumberRestriction(request);
      responseObserver.onNext(Empty.newBuilder().build());
      responseObserver.onCompleted();
    } catch (final Exception exception) {
      throwWrappedServiceInternalException("createPhoneNumberRestriction", exception);
    }
  }

  @Override
  public void removePhoneNumberRestriction(
      final RemovePhoneNumberRestrictionRequest request,
      final StreamObserver<Empty> responseObserver) {
    try {
      smsRestrictionSvc.removePhoneNumberRestriction(request);
      responseObserver.onNext(Empty.newBuilder().build());
      responseObserver.onCompleted();
    } catch (final Exception exception) {
      throwWrappedServiceInternalException("removePhoneNumberRestriction", exception);
    }
  }

  @Override
  public void updateStatus(
      final UpdateStatusRequest request, final StreamObserver<Empty> responseObserver) {
    try {
      statusSvc.updateStatus(request);
      responseObserver.onNext(Empty.newBuilder().build());
      responseObserver.onCompleted();
    } catch (final Exception exception) {
      throwWrappedServiceInternalException("updateStatus", exception);
    }
  }

  @Override
  public void updateStatusByExternalId(
      final UpdateStatusByExternalIdRequest request, final StreamObserver<Empty> responseObserver) {
    try {
      statusSvc.updateStatusByExternalId(request);
      responseObserver.onNext(Empty.newBuilder().build());
      responseObserver.onCompleted();
    } catch (final Exception exception) {
      throwWrappedServiceInternalException("updateStatusByExternalId", exception);
    }
  }

  @Override
  public void sendMessageBatch(
      final SendMessageBatchRequest request, final StreamObserver<Empty> responseObserver) {
    senderJobHandlerExecutor.run(request);
    responseObserver.onNext(Empty.newBuilder().build());
    responseObserver.onCompleted();
  }
}
