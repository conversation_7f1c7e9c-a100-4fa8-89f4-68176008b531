package com.xgen.cloud.services.communication._private.model.datadog;

import com.datadog.api.client.v1.model.EventCreateRequest;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.services.communication._private.model.PublishMessageRequestTransformer;
import com.xgen.cloud.services.communication._private.model.base.Payload;
import com.xgen.cloud.services.communication._private.model.enums.DatadogEventType;
import jakarta.annotation.Nonnull;
import java.time.Instant;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;

/** Representation of a datadog message body */
public class DatadogPayload implements Payload {
  public static final String TITLE_FIELD = "title";
  public static final String TEXT_FIELD = "text";
  public static final String ALERT_TYPE_FIELD = "alertType";
  public static final String HOST_FIELD = "host";
  public static final String DATE_HAPPENED_FIELD = "dateHappened";
  public static final String AGGREGATION_KEY_FIELD = "aggregationKey";
  public static final String SOURCE_TYPE_NAME_FIELD = "sourceTypeName";
  public static final String TAGS_FIELD = "tags";

  @BsonProperty(TITLE_FIELD)
  @JsonProperty(TITLE_FIELD)
  protected String title;

  @BsonProperty(TEXT_FIELD)
  @JsonProperty(TEXT_FIELD)
  protected String text;

  @BsonProperty(ALERT_TYPE_FIELD)
  @JsonProperty(ALERT_TYPE_FIELD)
  protected DatadogEventType alertType;

  @BsonProperty(HOST_FIELD)
  @JsonProperty(HOST_FIELD)
  protected String host;

  @BsonProperty(DATE_HAPPENED_FIELD)
  @JsonProperty(DATE_HAPPENED_FIELD)
  protected Instant dateHappened;

  @BsonProperty(AGGREGATION_KEY_FIELD)
  @JsonProperty(AGGREGATION_KEY_FIELD)
  protected String aggregationKey;

  @BsonProperty(SOURCE_TYPE_NAME_FIELD)
  @JsonProperty(SOURCE_TYPE_NAME_FIELD)
  protected String sourceTypeName;

  @BsonProperty(TAGS_FIELD)
  @JsonProperty(TAGS_FIELD)
  protected List<String> tags;

  @BsonCreator
  public DatadogPayload(
      @Nonnull @BsonProperty(TITLE_FIELD) final String title,
      @Nonnull @BsonProperty(TEXT_FIELD) final String text,
      @BsonProperty(ALERT_TYPE_FIELD) final DatadogEventType alertType,
      @BsonProperty(HOST_FIELD) final String host,
      @BsonProperty(DATE_HAPPENED_FIELD) final Instant dateHappened,
      @BsonProperty(AGGREGATION_KEY_FIELD) final String aggregationKey,
      @BsonProperty(SOURCE_TYPE_NAME_FIELD) final String sourceTypeName,
      @BsonProperty(TAGS_FIELD) final List<String> tags) {
    this.title = title;
    this.text = text;
    this.alertType = alertType;
    this.host = host;
    this.dateHappened = dateHappened;
    this.aggregationKey = aggregationKey;
    this.sourceTypeName = sourceTypeName;
    this.tags = tags;
  }

  public static DatadogPayload fromProto(
      final com.xgen.cloud.services.communication.proto.DatadogPayload proto) {
    return new DatadogPayload(
        proto.getTitle(),
        proto.getText(),
        proto.hasAlertType()
            ? PublishMessageRequestTransformer.fromProtoDatadogEventType(proto.getAlertType())
            : null,
        proto.hasHost() ? proto.getHost() : null,
        proto.hasDateHappenedEpochMillis()
            ? Instant.ofEpochMilli(proto.getDateHappenedEpochMillis())
            : null,
        proto.hasAggregationKey() ? proto.getAggregationKey() : null,
        proto.hasSourceTypeName() ? proto.getSourceTypeName() : null,
        proto.getTagsList());
  }

  @BsonIgnore
  public static DatadogPayload fromProto(
      final com.xgen.cloud.services.communication.proto.Payload payloadProto) {
    if (!payloadProto.hasDatadogPayload()) {
      throw new IllegalArgumentException("Payload is not a Datadog payload.");
    }
    final com.xgen.cloud.services.communication.proto.DatadogPayload protoDatadogPayload =
        payloadProto.getDatadogPayload();
    return new DatadogPayload(
        protoDatadogPayload.getTitle(),
        protoDatadogPayload.getText(),
        protoDatadogPayload.hasAlertType()
            ? PublishMessageRequestTransformer.fromProtoDatadogEventType(
                protoDatadogPayload.getAlertType())
            : null,
        protoDatadogPayload.hasHost() ? protoDatadogPayload.getHost() : null,
        protoDatadogPayload.hasDateHappenedEpochMillis()
            ? Instant.ofEpochMilli(protoDatadogPayload.getDateHappenedEpochMillis())
            : null,
        protoDatadogPayload.hasAggregationKey() ? protoDatadogPayload.getAggregationKey() : null,
        protoDatadogPayload.hasSourceTypeName() ? protoDatadogPayload.getSourceTypeName() : null,
        protoDatadogPayload.getTagsList());
  }

  @BsonIgnore
  public static com.xgen.cloud.services.communication.proto.Payload toProto(
      final DatadogPayload payload) {
    com.xgen.cloud.services.communication.proto.DatadogPayload.Builder protoDatadogPayload =
        com.xgen.cloud.services.communication.proto.DatadogPayload.newBuilder();
    if (payload.getTitle() != null) {
      protoDatadogPayload = protoDatadogPayload.setTitle(payload.getTitle());
    }
    if (payload.getText() != null) {
      protoDatadogPayload = protoDatadogPayload.setText(payload.getText());
    }
    if (payload.getAlertType() != null) {
      protoDatadogPayload =
          protoDatadogPayload.setAlertType(
              PublishMessageRequestTransformer.toProtoDatadogEventType(payload.getAlertType()));
    }
    if (payload.getHost() != null) {
      protoDatadogPayload = protoDatadogPayload.setHost(payload.getHost());
    }
    if (payload.getDateHappened() != null) {
      protoDatadogPayload =
          protoDatadogPayload.setDateHappenedEpochMillis(payload.getDateHappened().toEpochMilli());
    }
    if (payload.getAggregationKey() != null) {
      protoDatadogPayload = protoDatadogPayload.setAggregationKey(payload.getAggregationKey());
    }
    if (payload.getSourceTypeName() != null) {
      protoDatadogPayload = protoDatadogPayload.setSourceTypeName(payload.getSourceTypeName());
    }
    if (payload.getTags() != null) {
      protoDatadogPayload = protoDatadogPayload.addAllTags(payload.getTags());
    }

    return com.xgen.cloud.services.communication.proto.Payload.newBuilder()
        .setDatadogPayload(protoDatadogPayload)
        .build();
  }

  @BsonProperty(TITLE_FIELD)
  public String getTitle() {
    return title;
  }

  @BsonProperty(TEXT_FIELD)
  public String getText() {
    return text;
  }

  @BsonProperty(ALERT_TYPE_FIELD)
  public DatadogEventType getAlertType() {
    return alertType;
  }

  @BsonProperty(HOST_FIELD)
  public String getHost() {
    return host;
  }

  @BsonProperty(DATE_HAPPENED_FIELD)
  public Instant getDateHappened() {
    return dateHappened;
  }

  @BsonProperty(AGGREGATION_KEY_FIELD)
  public String getAggregationKey() {
    return aggregationKey;
  }

  @BsonProperty(SOURCE_TYPE_NAME_FIELD)
  public String getSourceTypeName() {
    return sourceTypeName;
  }

  @BsonProperty(TAGS_FIELD)
  public List<String> getTags() {
    return tags;
  }

  @BsonIgnore
  public EventCreateRequest toEventCreateRequest() {
    final EventCreateRequest request = new EventCreateRequest().title(getTitle()).text(getText());

    if (getAlertType() != null) {
      request.alertType(getAlertType() == null ? null : getAlertType().toDatadogEventAlertType());
    }

    if (getHost() != null) {
      request.host(getHost());
    }

    if (getDateHappened() != null) {
      request.dateHappened(getDateHappened().toEpochMilli() / 1000);
    }

    if (getAggregationKey() != null) {
      request.aggregationKey(getAggregationKey());
    }

    if (getSourceTypeName() != null) {
      request.sourceTypeName(getSourceTypeName());
    }

    if (getTags() != null && !getTags().isEmpty()) {
      request.tags(getTags());
    }

    return request;
  }

  @Override
  @BsonIgnore
  public boolean equals(final Object untypedOther) {
    if (untypedOther == this) {
      return true;
    }

    if (untypedOther == null || untypedOther.getClass() != getClass()) {
      return false;
    }

    final DatadogPayload other = (DatadogPayload) untypedOther;
    return new EqualsBuilder()
        .append(getTitle(), other.getTitle())
        .append(getText(), other.getText())
        .append(getAlertType(), other.getAlertType())
        .append(getHost(), other.getHost())
        .append(getDateHappened(), other.getDateHappened())
        .append(getAggregationKey(), other.getAggregationKey())
        .append(getSourceTypeName(), other.getSourceTypeName())
        .append(getTags(), other.getTags())
        .build();
  }

  @Override
  @BsonIgnore
  public int hashCode() {
    return new HashCodeBuilder()
        .append(getTitle())
        .append(getText())
        .append(getAlertType())
        .append(getHost())
        .append(getDateHappened())
        .append(getAggregationKey())
        .append(getSourceTypeName())
        .append(getTags())
        .toHashCode();
  }

  public static class Builder {
    private String title;
    private String text;
    private DatadogEventType alertType;
    private String host;
    private Instant dateHappened;
    private String aggregationKey;
    private String sourceTypeName;
    private List<String> tags;

    public Builder() {}

    public Builder title(final String title) {
      this.title = title;
      return this;
    }

    public Builder text(final String text) {
      this.text = text;
      return this;
    }

    public Builder alertType(final DatadogEventType alertType) {
      this.alertType = alertType;
      return this;
    }

    public Builder host(final String host) {
      this.host = host;
      return this;
    }

    public Builder dateHappened(final Instant dateHappened) {
      this.dateHappened = dateHappened;
      return this;
    }

    public Builder aggregationKey(final String aggregationKey) {
      this.aggregationKey = aggregationKey;
      return this;
    }

    public Builder sourceTypeName(final String sourceTypeName) {
      this.sourceTypeName = sourceTypeName;
      return this;
    }

    public Builder tags(final List<String> tags) {
      this.tags = tags;
      return this;
    }

    private void validate() {
      if (StringUtils.isBlank(title) || StringUtils.isBlank(text)) {
        throw new IllegalArgumentException(
            "Title and text are both non-nullable in datadog payload.");
      }
    }

    public DatadogPayload build() {
      validate();
      return new DatadogPayload(
          title, text, alertType, host, dateHappened, aggregationKey, sourceTypeName, tags);
    }
  }
}
