package com.xgen.cloud.services.communication._private.model.email;

import com.xgen.cloud.services.communication._private.model.base.Response;
import com.xgen.cloud.services.communication._private.model.enums.ErrorCode;

/** Representation of the response of a email message send response */
public class EmailResponse extends Response<EmailResponseBody> {
  public final EmailResponseBody responseBody;

  public EmailResponse(final String externalId, final ErrorCode errorCode) {
    this.responseBody = new EmailResponseBody(externalId, errorCode);
  }

  @Override
  public EmailResponseBody getResponseBody() {
    return responseBody;
  }
}
