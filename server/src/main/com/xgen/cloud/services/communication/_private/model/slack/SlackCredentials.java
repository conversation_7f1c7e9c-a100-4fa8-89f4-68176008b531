package com.xgen.cloud.services.communication._private.model.slack;

import com.xgen.cloud.common.dao.codec._public.encrypted.string.EncryptedString;
import com.xgen.cloud.common.model._public.annotation.GenEncryptField;
import com.xgen.cloud.common.model._public.annotation.WithGenEncryptField;
import com.xgen.cloud.services.communication._private.model.PublishMessageRequestTransformer;
import com.xgen.cloud.services.communication._private.model.base.Credentials;
import com.xgen.cloud.services.communication._private.model.enums.ProviderType;
import java.time.Instant;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/** Representation of slack credentials */
@BsonDiscriminator(key = Credentials.PROVIDER_TYPE_FIELD, value = ProviderType.CREDENTIALS_SLACK)
@WithGenEncryptField(DB = Credentials.DB_NAME, Collection = Credentials.COLLECTION_NAME)
public class SlackCredentials extends Credentials {

  @BsonProperty(CREDENTIALS_FIELD)
  @GenEncryptField(CREDENTIALS_FIELD)
  protected EncryptedString credentials;

  @BsonCreator
  public SlackCredentials(
      @BsonId final ObjectId id,
      @BsonProperty(CREDENTIALS_FIELD) EncryptedString credentials,
      @BsonProperty(CREATED_AT_FIELD) Instant createdAt,
      @BsonProperty(UPDATED_AT_FIELD) Instant updatedAt) {
    super(id, createdAt, updatedAt);
    this.credentials = credentials;
  }

  public SlackCredentials(final String unencryptedCredentials) {
    super(new ObjectId(), Instant.now(), Instant.now());
    this.credentials =
        unencryptedCredentials != null
            ? new EncryptedString(StringUtils.trimToEmpty(unencryptedCredentials))
            : null;
  }

  @BsonIgnore
  public static SlackCredentials fromProto(
      final com.xgen.cloud.services.communication.proto.Credentials protoCredentials) {
    final com.xgen.cloud.services.communication.proto.SlackCredentials protoSlackCredentials =
        protoCredentials.getSlackCredentials();
    return new SlackCredentials(
        new ObjectId(protoCredentials.getId()),
        new EncryptedString(
            PublishMessageRequestTransformer.deserializeEncryptedString(
                protoSlackCredentials.getEncryptedApiToken())),
        Instant.ofEpochMilli(protoCredentials.getCreatedAtEpochMilli()),
        Instant.ofEpochMilli(protoCredentials.getUpdatedAtEpochMilli()));
  }

  @BsonIgnore
  public static com.xgen.cloud.services.communication.proto.Credentials toProto(
      final SlackCredentials credentials) {
    com.xgen.cloud.services.communication.proto.SlackCredentials.Builder protoSlackCredentials =
        com.xgen.cloud.services.communication.proto.SlackCredentials.newBuilder();
    if (credentials.getDecryptedCredentials() != null) {
      protoSlackCredentials =
          protoSlackCredentials.setEncryptedApiToken(
              PublishMessageRequestTransformer.serializeEncryptedString(
                  credentials.getDecryptedCredentials()));
    }

    return getBaseCredentialsBuilder(credentials)
        .setSlackCredentials(protoSlackCredentials)
        .build();
  }

  @BsonIgnore
  @Override
  public ProviderType getProviderType() {
    return ProviderType.SLACK;
  }

  @BsonIgnore
  public String getToken() {
    return this.credentials == null ? null : this.credentials.getValue();
  }

  @BsonProperty(CREDENTIALS_FIELD)
  public EncryptedString getCredentials() {
    return credentials;
  }

  @BsonIgnore
  public String getDecryptedCredentials() {
    return getCredentials() != null ? getCredentials().getValue() : null;
  }

  @Override
  @BsonIgnore
  public boolean equals(final Object untypedOther) {
    if (untypedOther == this) {
      return true;
    }

    if (untypedOther == null || untypedOther.getClass() != getClass()) {
      return false;
    }

    final Credentials parentOther = (Credentials) untypedOther;
    final SlackCredentials other = (SlackCredentials) untypedOther;
    return super.equals(parentOther)
        && new EqualsBuilder()
            .append(getDecryptedCredentials(), other.getDecryptedCredentials())
            .build();
  }

  @Override
  @BsonIgnore
  public int hashCode() {
    return new HashCodeBuilder()
        .append(super.hashCode())
        .append(getDecryptedCredentials())
        .toHashCode();
  }
}
