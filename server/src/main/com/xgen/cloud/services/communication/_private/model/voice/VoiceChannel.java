package com.xgen.cloud.services.communication._private.model.voice;

import com.xgen.cloud.services.communication._private.model.base.Channel;
import com.xgen.cloud.services.communication._private.model.enums.ProviderType;
import java.time.Instant;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/** Representation of a voice messaging channel */
@BsonDiscriminator(key = Channel.PROVIDER_TYPE_FIELD, value = ProviderType.CHANNEL_VOICE)
public class VoiceChannel extends Channel {

  @Override
  @BsonIgnore
  public ProviderType getProviderType() {
    return ProviderType.VOICE;
  }

  @BsonCreator
  public VoiceChannel(
      @BsonId final ObjectId id,
      @BsonProperty(CREATED_AT_FIELD) Instant createdAt,
      @BsonProperty(UPDATED_AT_FIELD) Instant updatedAt) {
    super(id, createdAt, updatedAt);
  }

  public VoiceChannel() {
    super(new ObjectId(), Instant.now(), Instant.now());
  }
}
