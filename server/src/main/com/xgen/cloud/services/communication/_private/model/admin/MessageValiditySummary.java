package com.xgen.cloud.services.communication._private.model.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.services.communication._private.model.PublishMessageRequestTransformer;
import com.xgen.cloud.services.communication._private.model.enums.ErrorCode;
import com.xgen.cloud.services.communication._private.model.enums.Status;
import com.xgen.cloud.services.communication.proto.ErrorCodeCount;
import com.xgen.cloud.services.communication.proto.IntegrationValidity;
import com.xgen.cloud.services.communication.proto.StatusCount;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

/** Representation of an interface for sending messages through a third party */
public class MessageValiditySummary {
  public static final String INTEGRATION_ID_FIELD = "integrationId";
  public static final String STATUSES_FIELD = "statuses";
  public static final String ERROR_CODES_FIELD = "errorCodes";
  public static final String INVALIDITY_CERTAINTY_FIELD = "invalidityCertainty";
  public static final String INVALIDITY_REASON_FIELD = "invalidityReason";

  @JsonProperty(INTEGRATION_ID_FIELD)
  protected ObjectId integrationId;

  @JsonProperty(STATUSES_FIELD)
  protected Map<Status, Integer> statuses;

  @JsonProperty(ERROR_CODES_FIELD)
  protected Map<ErrorCode, Integer> errorCodes;

  @JsonProperty(INVALIDITY_CERTAINTY_FIELD)
  protected Double invalidityCertainty;

  @JsonProperty(INVALIDITY_REASON_FIELD)
  public String getInvalidityReason() {
    final Entry<ErrorCode, Integer> maxEntry =
        errorCodes == null
            ? null
            : errorCodes.entrySet().stream()
                .sorted((x, y) -> x.getValue() > y.getValue() ? 1 : -1)
                .findFirst()
                .orElse(null);
    if (maxEntry == null || maxEntry.getValue() == null || maxEntry.getValue() < 1) {
      return null;
    }
    final Double maxErrors = maxEntry.getValue().doubleValue();
    final Double totalErrors =
        errorCodes.values().stream()
            .filter(Objects::nonNull)
            .map(value -> value.doubleValue())
            .reduce(0.0, (value0, value1) -> value0 + value1);
    final Double totalMessages =
        statuses == null
            ? 0.0
            : statuses.values().stream()
                .filter(Objects::nonNull)
                .map(value -> value.doubleValue())
                .reduce(0.0, (value0, value1) -> value0 + value1);

    final ErrorCode mostCommonErrorCode = maxEntry.getKey();
    final String mostCommonErrorMessage = mostCommonErrorCode.getUserReadableMessage();
    final long percentage = Math.round(100.0 * maxErrors / totalErrors);

    final String totalErrorMessage =
        ""
            + Math.round(totalErrors)
            + " of your last "
            + Math.round(totalMessages)
            + " messages have failed to send. "
            + percentage
            + "% of your error messages have been \""
            + mostCommonErrorMessage
            + "\".";
    return totalErrorMessage;
  }

  public MessageValiditySummary(final ObjectId integrationId) {
    this.integrationId = integrationId;
    this.statuses = new HashMap<Status, Integer>();
    this.errorCodes = new HashMap<ErrorCode, Integer>();
    this.invalidityCertainty = 0.0;
  }

  public MessageValiditySummary(
      final ObjectId integrationId,
      final Map<Status, Integer> statuses,
      final Map<ErrorCode, Integer> errorCodes,
      final Double invalidityCertainty) {
    this.integrationId = integrationId;
    this.statuses = statuses;
    this.errorCodes = errorCodes;
    this.invalidityCertainty = invalidityCertainty;
  }

  public void incorporateValidity(final MessageValidity validity) {
    if (validity == null) {
      return;
    }
    if (validity.status != null) {
      if (!this.statuses.containsKey(validity.status)) {
        this.statuses.put(validity.status, 0);
      }
      this.statuses.put(validity.status, this.statuses.get(validity.status) + 1);
      // Simple heuristic that I just made up. The key points are that you generally need at least
      // 3-4 points to hit anything approaching certainty, that successes are very strong
      // indicators of not being broken, and that more recent values are more important.
      switch (validity.status) {
        case FAILED -> invalidityCertainty += ((1 - invalidityCertainty) / 4);
        case SUCCEEDED -> invalidityCertainty /= 2;
        case CREATED -> {}
        default -> invalidityCertainty /= 1.1;
      }
    }
    if (validity.errorCode != null) {
      if (!this.errorCodes.containsKey(validity.errorCode)) {
        this.errorCodes.put(validity.errorCode, 0);
      }
      this.errorCodes.put(validity.errorCode, this.errorCodes.get(validity.errorCode) + 1);
    }
  }

  public static IntegrationValidity toProto(final MessageValiditySummary messageValiditySummary) {
    final String invalidityReason = messageValiditySummary.getInvalidityReason();
    IntegrationValidity.Builder proto =
        IntegrationValidity.newBuilder()
            .setIntegrationId(messageValiditySummary.integrationId.toHexString())
            .addAllStatusCounts(
                messageValiditySummary.statuses == null
                    ? Collections.emptyList()
                    : messageValiditySummary.statuses.entrySet().stream()
                        .map(
                            entry ->
                                StatusCount.newBuilder()
                                    .setStatus(
                                        PublishMessageRequestTransformer.toProtoStatus(
                                            entry.getKey()))
                                    .setCount(entry.getValue())
                                    .build())
                        .toList())
            .addAllErrorCodeCounts(
                messageValiditySummary.errorCodes == null
                    ? Collections.emptyList()
                    : messageValiditySummary.errorCodes.entrySet().stream()
                        .map(
                            entry ->
                                ErrorCodeCount.newBuilder()
                                    .setErrorCode(
                                        PublishMessageRequestTransformer.toProtoErrorCode(
                                            entry.getKey()))
                                    .setCount(entry.getValue())
                                    .build())
                        .toList());
    if (messageValiditySummary.invalidityCertainty != null) {
      proto = proto.setInvalidityCertainty(messageValiditySummary.invalidityCertainty);
    }
    if (invalidityReason != null) {
      proto = proto.setInvalidityReason(invalidityReason);
    }
    return proto.build();
  }

  public static MessageValiditySummary fromProto(final IntegrationValidity proto) {
    if (proto == null) {
      return null;
    }
    final ObjectId integrationId = new ObjectId(proto.getIntegrationId());
    return new MessageValiditySummary(
        integrationId,
        proto.getStatusCountsList() == null
            ? Collections.emptyMap()
            : proto.getStatusCountsList().stream()
                .collect(
                    Collectors.toMap(
                        (final StatusCount statusCount) ->
                            PublishMessageRequestTransformer.fromProtoStatus(
                                statusCount.getStatus()),
                        (final StatusCount statusCount) -> statusCount.getCount())),
        proto.getErrorCodeCountsList() == null
            ? Collections.emptyMap()
            : proto.getErrorCodeCountsList().stream()
                .collect(
                    Collectors.toMap(
                        (final ErrorCodeCount errorCodeCount) ->
                            PublishMessageRequestTransformer.fromProtoErrorCode(
                                errorCodeCount.getErrorCode()),
                        (final ErrorCodeCount errorCodeCount) -> errorCodeCount.getCount())),
        proto.hasInvalidityCertainty() ? proto.getInvalidityCertainty() : null);
  }
}
