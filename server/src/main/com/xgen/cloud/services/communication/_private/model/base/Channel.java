package com.xgen.cloud.services.communication._private.model.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.services.communication._private.model.PublishMessageRequestTransformer;
import com.xgen.cloud.services.communication._private.model.base.Message.FieldDefs;
import java.time.Instant;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/** Representation of an interface for sending messages through a third party */
@BsonDiscriminator(key = Channel.PROVIDER_TYPE_FIELD)
public abstract class Channel extends ProviderDependentObject {
  public static final String DB_NAME = "mmsdb";
  public static final String COLLECTION_NAME = "communication.channels";

  public static final String ID_FIELD = "_id";
  public static final String PROVIDER_TYPE_FIELD = "providerType";
  public static final String CREATED_AT_FIELD = "createdAt";
  public static final String UPDATED_AT_FIELD = "updatedAt";

  @BsonId
  @JsonProperty(ID_FIELD)
  protected ObjectId id;

  @BsonProperty(FieldDefs.CREATED_AT_FIELD)
  @JsonProperty(FieldDefs.CREATED_AT_FIELD)
  protected Instant createdAt;

  @BsonProperty(FieldDefs.UPDATED_AT_FIELD)
  @JsonProperty(FieldDefs.UPDATED_AT_FIELD)
  protected Instant updatedAt;

  @BsonCreator
  public Channel(
      @BsonId final ObjectId id,
      @BsonProperty(FieldDefs.CREATED_AT_FIELD) Instant createdAt,
      @BsonProperty(FieldDefs.UPDATED_AT_FIELD) Instant updatedAt) {
    this.id = id;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }

  @BsonIgnore
  protected static com.xgen.cloud.services.communication.proto.Channel.Builder
      getBaseChannelBuilder(final Channel channel) {
    com.xgen.cloud.services.communication.proto.Channel.Builder protoChannel =
        com.xgen.cloud.services.communication.proto.Channel.newBuilder();
    if (channel.getId() != null) {
      protoChannel = protoChannel.setId(channel.getId().toHexString());
    }
    if (channel.getProviderType() != null) {
      protoChannel =
          protoChannel.setProviderType(
              PublishMessageRequestTransformer.toProtoProviderType(channel.getProviderType()));
    }
    if (channel.getCreatedAt() != null) {
      protoChannel = protoChannel.setCreatedAtEpochMilli(channel.getCreatedAt().toEpochMilli());
    }
    if (channel.getUpdatedAt() != null) {
      protoChannel = protoChannel.setUpdatedAtEpochMilli(channel.getUpdatedAt().toEpochMilli());
    }
    return protoChannel;
  }

  @BsonId
  public ObjectId getId() {
    return this.id;
  }

  public Instant getCreatedAt() {
    return this.createdAt;
  }

  public Instant getUpdatedAt() {
    return this.updatedAt;
  }

  @Override
  @BsonIgnore
  public boolean equals(final Object untypedOther) {
    if (untypedOther == this) {
      return true;
    }

    if (untypedOther == null || untypedOther.getClass() != getClass()) {
      return false;
    }

    final Channel other = (Channel) untypedOther;
    return new EqualsBuilder()
        .append(getId(), other.getId())
        .append(getProviderType(), other.getProviderType())
        .append(getCreatedAt(), other.getCreatedAt())
        .append(getUpdatedAt(), other.getUpdatedAt())
        .build();
  }

  @Override
  @BsonIgnore
  public int hashCode() {
    return new HashCodeBuilder()
        .append(getId())
        .append(getProviderType())
        .append(getCreatedAt())
        .append(getUpdatedAt())
        .toHashCode();
  }
}
