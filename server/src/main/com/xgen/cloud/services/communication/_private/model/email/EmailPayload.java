package com.xgen.cloud.services.communication._private.model.email;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.services.communication._private.model.base.Payload;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;

/** Representation of a email message body */
public class EmailPayload implements Payload {
  public static final String FROM_ADDRESS_FIELD = "fromAddress";
  public static final String REPLY_ADDRESSES_FIELD = "replyAddresses";
  public static final String SUBJECT_FIELD = "subject";
  public static final String HTML_FIELD = "html";
  public static final String TEXT_FIELD = "text";
  public static final String ATTACHMENTS_FIELD = "attachments";
  public static final String TEMPLATE_NAME_FIELD = "templateName";

  @BsonProperty(FROM_ADDRESS_FIELD)
  @JsonProperty(FROM_ADDRESS_FIELD)
  protected String fromAddress;

  @BsonProperty(REPLY_ADDRESSES_FIELD)
  @JsonProperty(REPLY_ADDRESSES_FIELD)
  protected List<String> replyAddresses;

  @BsonProperty(RECIPIENT_FIELD)
  @JsonProperty(RECIPIENT_FIELD)
  protected String recipient;

  @BsonProperty(SUBJECT_FIELD)
  @JsonProperty(SUBJECT_FIELD)
  protected String subject;

  @BsonProperty(HTML_FIELD)
  @JsonProperty(HTML_FIELD)
  protected String html;

  @BsonProperty(TEXT_FIELD)
  @JsonProperty(TEXT_FIELD)
  protected String text;

  @BsonProperty(ATTACHMENTS_FIELD)
  @JsonProperty(ATTACHMENTS_FIELD)
  protected List<EmailAttachment> attachments;

  @BsonProperty(TEMPLATE_NAME_FIELD)
  @JsonProperty(TEMPLATE_NAME_FIELD)
  protected String templateName;

  @BsonCreator
  public EmailPayload(
      @BsonProperty(FROM_ADDRESS_FIELD) final String fromAddress,
      @BsonProperty(REPLY_ADDRESSES_FIELD) final List<String> replyAddresses,
      @BsonProperty(RECIPIENT_FIELD) final String recipient,
      @BsonProperty(SUBJECT_FIELD) final String subject,
      @BsonProperty(HTML_FIELD) final String html,
      @BsonProperty(TEXT_FIELD) final String text,
      @BsonProperty(ATTACHMENTS_FIELD) final List<EmailAttachment> attachments,
      @BsonProperty(TEMPLATE_NAME_FIELD) final String templateName) {
    this.fromAddress = fromAddress;
    this.replyAddresses = replyAddresses;
    this.recipient = recipient;
    this.subject = subject;
    this.html = html;
    this.text = text;
    this.attachments = attachments;
    this.templateName = templateName;
  }

  public static EmailPayload fromProto(
      final com.xgen.cloud.services.communication.proto.Payload payloadProto) {
    if (!payloadProto.hasEmailPayload()) {
      throw new IllegalArgumentException("Payload is not an Email payload.");
    }
    final com.xgen.cloud.services.communication.proto.EmailPayload emailPayloadProto =
        payloadProto.getEmailPayload();
    final List<EmailAttachment> emailAttachments = new ArrayList<>();
    for (com.xgen.cloud.services.communication.proto.EmailAttachment attachment :
        emailPayloadProto.getAttachmentsList()) {
      emailAttachments.add(EmailAttachment.fromProto(attachment));
    }

    return new EmailPayload(
        emailPayloadProto.getFromAddress(),
        emailPayloadProto.getReplyAddressesList(),
        emailPayloadProto.getRecipient(),
        emailPayloadProto.hasSubject() ? emailPayloadProto.getSubject() : null,
        emailPayloadProto.hasHtml() ? emailPayloadProto.getHtml() : null,
        emailPayloadProto.hasText() ? emailPayloadProto.getText() : null,
        emailAttachments,
        emailPayloadProto.hasTemplateName() ? emailPayloadProto.getTemplateName() : null);
  }

  @BsonIgnore
  public static com.xgen.cloud.services.communication.proto.Payload toProto(
      final EmailPayload payload) {
    com.xgen.cloud.services.communication.proto.EmailPayload.Builder protoEmailPayload =
        com.xgen.cloud.services.communication.proto.EmailPayload.newBuilder();
    if (payload.getFromAddress() != null) {
      protoEmailPayload = protoEmailPayload.setFromAddress(payload.getFromAddress());
    }
    if (payload.getReplyAddresses() != null) {
      protoEmailPayload = protoEmailPayload.addAllReplyAddresses(payload.getReplyAddresses());
    }
    if (payload.getRecipient() != null) {
      protoEmailPayload = protoEmailPayload.setRecipient(payload.getRecipient());
    }
    if (payload.getSubject() != null) {
      protoEmailPayload = protoEmailPayload.setSubject(payload.getSubject());
    }
    if (payload.getHtml() != null) {
      protoEmailPayload = protoEmailPayload.setHtml(payload.getHtml());
    }
    if (payload.getText() != null) {
      protoEmailPayload = protoEmailPayload.setText(payload.getText());
    }
    if (payload.getAttachments() != null) {
      protoEmailPayload =
          protoEmailPayload.addAllAttachments(
              payload.getAttachments().stream()
                  .map(EmailAttachment::toProto)
                  .collect(Collectors.toList()));
    }
    if (payload.getTemplateName() != null) {
      protoEmailPayload = protoEmailPayload.setTemplateName(payload.getTemplateName());
    }

    return com.xgen.cloud.services.communication.proto.Payload.newBuilder()
        .setEmailPayload(protoEmailPayload)
        .build();
  }

  @BsonProperty(FROM_ADDRESS_FIELD)
  public String getFromAddress() {
    return fromAddress;
  }

  @BsonProperty(REPLY_ADDRESSES_FIELD)
  public List<String> getReplyAddresses() {
    return replyAddresses;
  }

  @BsonProperty(RECIPIENT_FIELD)
  public String getRecipient() {
    return recipient;
  }

  @BsonProperty(SUBJECT_FIELD)
  public String getSubject() {
    return subject;
  }

  @BsonProperty(HTML_FIELD)
  public String getHtml() {
    return html;
  }

  @BsonProperty(TEXT_FIELD)
  public String getText() {
    return text;
  }

  @BsonProperty(ATTACHMENTS_FIELD)
  public List<EmailAttachment> getAttachments() {
    return attachments;
  }

  @BsonProperty(TEMPLATE_NAME_FIELD)
  public String getTemplateName() {
    return templateName;
  }

  @Override
  @BsonIgnore
  public boolean equals(final Object untypedOther) {
    if (untypedOther == this) {
      return true;
    }

    if (untypedOther == null || untypedOther.getClass() != getClass()) {
      return false;
    }

    final EmailPayload other = (EmailPayload) untypedOther;
    return new EqualsBuilder()
        .append(getFromAddress(), other.getFromAddress())
        .append(getReplyAddresses(), other.getReplyAddresses())
        .append(getRecipient(), other.getRecipient())
        .append(getSubject(), other.getSubject())
        .append(getHtml(), other.getHtml())
        .append(getText(), other.getText())
        .append(getAttachments(), other.getAttachments())
        .append(getTemplateName(), other.getTemplateName())
        .build();
  }

  @Override
  @BsonIgnore
  public int hashCode() {
    return new HashCodeBuilder()
        .append(getFromAddress())
        .append(getReplyAddresses())
        .append(getRecipient())
        .append(getSubject())
        .append(getHtml())
        .append(getText())
        .append(getAttachments())
        .append(getTemplateName())
        .toHashCode();
  }
}
