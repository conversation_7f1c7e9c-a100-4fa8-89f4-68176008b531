package com.xgen.cloud.services.communication._private.model.datadog;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.common.constants._public.model.communication.DatadogRegion;
import com.xgen.cloud.common.dao.codec._public.encrypted.string.EncryptedString;
import com.xgen.cloud.common.model._public.annotation.GenEncryptField;
import com.xgen.cloud.common.model._public.annotation.WithGenEncryptField;
import com.xgen.cloud.services.communication._private.model.PublishMessageRequestTransformer;
import com.xgen.cloud.services.communication._private.model.base.Credentials;
import com.xgen.cloud.services.communication._private.model.enums.ProviderType;
import java.time.Instant;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/** Representation of datadog credentials */
@BsonDiscriminator(key = Credentials.PROVIDER_TYPE_FIELD, value = ProviderType.CREDENTIALS_DATADOG)
@WithGenEncryptField(DB = Credentials.DB_NAME, Collection = Credentials.COLLECTION_NAME)
public class DatadogCredentials extends Credentials {
  public static final String REGION_FIELD = "region";
  public static final String CUSTOM_ENDPOINT_FIELD = "customEndpoint";

  // The region in which the credential is effective
  @BsonProperty(REGION_FIELD)
  @JsonProperty(REGION_FIELD)
  protected DatadogRegion region;

  @BsonProperty(CREDENTIALS_FIELD)
  @GenEncryptField(CREDENTIALS_FIELD)
  protected EncryptedString credentials;

  @BsonProperty(CUSTOM_ENDPOINT_FIELD)
  @GenEncryptField(CUSTOM_ENDPOINT_FIELD)
  protected String customEndpoint;

  @BsonCreator
  public DatadogCredentials(
      @BsonId final ObjectId id,
      @BsonProperty(REGION_FIELD) final DatadogRegion region,
      @BsonProperty(CREDENTIALS_FIELD) final EncryptedString credentials,
      @BsonProperty(CUSTOM_ENDPOINT_FIELD) final String customEndpoint,
      @BsonProperty(CREATED_AT_FIELD) final Instant createdAt,
      @BsonProperty(UPDATED_AT_FIELD) final Instant updatedAt) {
    super(id, createdAt, updatedAt);
    this.region = region;
    this.credentials = credentials;
    this.customEndpoint = customEndpoint;
  }

  public DatadogCredentials(final DatadogRegion region, final String unencryptedCredentials) {
    this(region, unencryptedCredentials, null);
  }

  public DatadogCredentials(
      final DatadogRegion region,
      final String unencryptedCredentials,
      final String customEndpoint) {
    super(new ObjectId(), Instant.now(), Instant.now());
    this.region = region;
    this.credentials =
        unencryptedCredentials != null
            ? new EncryptedString(StringUtils.trimToEmpty(unencryptedCredentials))
            : null;
    this.customEndpoint = customEndpoint;
  }

  public static DatadogCredentials fromProto(
      final com.xgen.cloud.services.communication.proto.Credentials protoCredentials) {
    final com.xgen.cloud.services.communication.proto.DatadogCredentials protoDatadogCredentials =
        protoCredentials.getDatadogCredentials();
    return new DatadogCredentials(
        new ObjectId(protoCredentials.getId()),
        PublishMessageRequestTransformer.fromProtoDatadogRegion(
            protoDatadogCredentials.getRegion()),
        new EncryptedString(
            PublishMessageRequestTransformer.deserializeEncryptedString(
                protoDatadogCredentials.getEncryptedApiKey())),
        protoDatadogCredentials.hasCustomEndpoint()
            ? protoDatadogCredentials.getCustomEndpoint()
            : null,
        Instant.ofEpochMilli(protoCredentials.getCreatedAtEpochMilli()),
        Instant.ofEpochMilli(protoCredentials.getUpdatedAtEpochMilli()));
  }

  public static com.xgen.cloud.services.communication.proto.Credentials toProto(
      final DatadogCredentials credentials) {
    com.xgen.cloud.services.communication.proto.DatadogCredentials.Builder protoDatadogCredentials =
        com.xgen.cloud.services.communication.proto.DatadogCredentials.newBuilder();
    if (credentials.getRegion() != null) {
      protoDatadogCredentials =
          protoDatadogCredentials.setRegion(
              PublishMessageRequestTransformer.toProtoDatadogRegion(credentials.getRegion()));
    }
    if (credentials.getDecryptedCredentials() != null) {
      protoDatadogCredentials =
          protoDatadogCredentials.setEncryptedApiKey(
              PublishMessageRequestTransformer.serializeEncryptedString(
                  credentials.getDecryptedCredentials()));
    }
    if (credentials.getCustomEndpoint() != null) {
      protoDatadogCredentials =
          protoDatadogCredentials.setCustomEndpoint(credentials.getCustomEndpoint());
    }

    return getBaseCredentialsBuilder(credentials)
        .setDatadogCredentials(protoDatadogCredentials)
        .build();
  }

  @BsonIgnore
  @Override
  public ProviderType getProviderType() {
    return ProviderType.DATADOG;
  }

  @BsonProperty(REGION_FIELD)
  public DatadogRegion getRegion() {
    return region;
  }

  @BsonIgnore
  public String getToken() {
    return this.credentials == null ? null : this.credentials.getValue();
  }

  @BsonIgnore
  public String getCustomEndpoint() {
    return this.customEndpoint;
  }

  @BsonProperty(CREDENTIALS_FIELD)
  public EncryptedString getCredentials() {
    return credentials;
  }

  @BsonIgnore
  public String getDecryptedCredentials() {
    return getCredentials() != null ? getCredentials().getValue() : null;
  }

  @Override
  @BsonIgnore
  public boolean equals(final Object untypedOther) {
    if (untypedOther == this) {
      return true;
    }

    if (untypedOther == null || untypedOther.getClass() != getClass()) {
      return false;
    }

    final Credentials parentOther = (Credentials) untypedOther;
    final DatadogCredentials other = (DatadogCredentials) untypedOther;
    return super.equals(parentOther)
        && new EqualsBuilder()
            .append(getDecryptedCredentials(), other.getDecryptedCredentials())
            .append(getRegion(), other.getRegion())
            .append(getCustomEndpoint(), other.getCustomEndpoint())
            .build();
  }

  @Override
  @BsonIgnore
  public int hashCode() {
    return new HashCodeBuilder()
        .append(super.hashCode())
        .append(getDecryptedCredentials())
        .append(getRegion())
        .append(getCustomEndpoint())
        .toHashCode();
  }
}
