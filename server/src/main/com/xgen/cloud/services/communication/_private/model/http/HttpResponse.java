package com.xgen.cloud.services.communication._private.model.http;

import static net.logstash.logback.argument.StructuredArguments.kv;

import com.xgen.cloud.services.communication._private.model.base.Response;
import com.xgen.cloud.services.communication._private.model.enums.ErrorCode;
import java.io.IOException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** Representation of the response of an http message send request */
public class HttpResponse extends Response<HttpResponseBody> {
  private static final Logger LOG = LoggerFactory.getLogger(HttpResponse.class);

  private final HttpResponseBody responseBody;

  public HttpResponse(final String externalId, final ErrorCode errorCode, final String message) {
    this.responseBody = new HttpResponseBody(externalId, errorCode, message);
  }

  public static HttpResponse fromResponse(final CloseableHttpResponse response) {
    final int statusCode = response.getStatusLine().getStatusCode();
    if (statusCode >= 200 && statusCode < 300) {
      return new HttpResponse(null, null, null);
    }
    final String message = response.getStatusLine().getReasonPhrase();
    final ErrorCode errorCode = ErrorCode.fromHttpErrorCode(statusCode);

    String entityString = null;
    try {
      entityString = EntityUtils.toString(response.getEntity());
    } catch (IOException e) {
      LOG.info(
          "Unable to parse response entity: {} {} {}",
          kv("errorType", "HTTP_ERROR"),
          kv("errorCode", errorCode),
          kv("message", String.valueOf(message)));
    }

    LOG.info(
        "Got an HTTP exception: {} {} {}",
        kv("errorType", "HTTP_ERROR"),
        kv("errorCode", errorCode),
        kv("message", String.valueOf(message)));

    return new HttpResponse(null, errorCode, entityString);
  }

  @Override
  public HttpResponseBody getResponseBody() {
    return responseBody;
  }
}
