package com.xgen.cloud.services.communication._private.model.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.services.communication._private.model.PublishMessageRequestTransformer;
import java.time.Instant;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/** Representation of third party authorization credentials */
@BsonDiscriminator(key = Credentials.PROVIDER_TYPE_FIELD)
public abstract class Credentials extends ProviderDependentObject {
  public static final String DB_NAME = "mmsdb";
  public static final String COLLECTION_NAME = "communication.credentials";

  public static final String ID_FIELD = "_id";
  public static final String PROVIDER_TYPE_FIELD = "providerType";
  public static final String CREDENTIALS_FIELD = "credentials";
  public static final String CREATED_AT_FIELD = "createdAt";
  public static final String UPDATED_AT_FIELD = "updatedAt";

  @BsonId
  @JsonProperty(ID_FIELD)
  protected ObjectId id;

  @BsonProperty(CREATED_AT_FIELD)
  @JsonProperty(CREATED_AT_FIELD)
  protected Instant createdAt;

  @BsonProperty(UPDATED_AT_FIELD)
  @JsonProperty(UPDATED_AT_FIELD)
  protected Instant updatedAt;

  @BsonCreator
  public Credentials(
      @BsonId final ObjectId id,
      @BsonProperty(CREATED_AT_FIELD) Instant createdAt,
      @BsonProperty(UPDATED_AT_FIELD) Instant updatedAt) {
    this.id = id;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }

  @BsonIgnore
  protected static com.xgen.cloud.services.communication.proto.Credentials.Builder
      getBaseCredentialsBuilder(final Credentials credentials) {
    com.xgen.cloud.services.communication.proto.Credentials.Builder protoCredentials =
        com.xgen.cloud.services.communication.proto.Credentials.newBuilder();
    if (credentials.getId() != null) {
      protoCredentials = protoCredentials.setId(credentials.getId().toHexString());
    }
    if (credentials.getProviderType() != null) {
      protoCredentials =
          protoCredentials.setProviderType(
              PublishMessageRequestTransformer.toProtoProviderType(credentials.getProviderType()));
    }
    if (credentials.getCreatedAt() != null) {
      protoCredentials =
          protoCredentials.setCreatedAtEpochMilli(credentials.getCreatedAt().toEpochMilli());
    }
    if (credentials.getUpdatedAt() != null) {
      protoCredentials =
          protoCredentials.setUpdatedAtEpochMilli(credentials.getUpdatedAt().toEpochMilli());
    }
    return protoCredentials;
  }

  @BsonId
  public ObjectId getId() {
    return this.id;
  }

  public Instant getCreatedAt() {
    return this.createdAt;
  }

  public Instant getUpdatedAt() {
    return this.updatedAt;
  }

  @Override
  @BsonIgnore
  public boolean equals(final Object untypedOther) {
    if (untypedOther == this) {
      return true;
    }

    if (untypedOther == null || untypedOther.getClass() != getClass()) {
      return false;
    }

    final Credentials other = (Credentials) untypedOther;
    return new EqualsBuilder()
        .append(getId(), other.getId())
        .append(getProviderType(), other.getProviderType())
        .append(getCreatedAt(), other.getCreatedAt())
        .append(getUpdatedAt(), other.getUpdatedAt())
        .build();
  }

  @Override
  @BsonIgnore
  public int hashCode() {
    return new HashCodeBuilder()
        .append(getId())
        .append(getProviderType())
        .append(getCreatedAt())
        .append(getUpdatedAt())
        .toHashCode();
  }
}
