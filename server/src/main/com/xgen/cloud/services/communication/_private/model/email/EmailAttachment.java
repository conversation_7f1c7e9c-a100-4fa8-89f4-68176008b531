package com.xgen.cloud.services.communication._private.model.email;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.protobuf.ByteString;
import com.xgen.cloud.services.communication._private.model.base.Payload;
import jakarta.activation.DataHandler;
import jakarta.activation.DataSource;
import jakarta.mail.Part;
import jakarta.mail.internet.MimeBodyPart;
import jakarta.mail.util.ByteArrayDataSource;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.Binary;

/** Representation of a email message body */
public class EmailAttachment implements Payload {
  public static final String FILE_NAME_FIELD = "fileName";
  public static final String MIME_TYPE_FIELD = "mimeType";
  public static final String DATA_FIELD = "data";

  @BsonProperty(FILE_NAME_FIELD)
  @JsonProperty(FILE_NAME_FIELD)
  protected String fileName;

  @BsonProperty(MIME_TYPE_FIELD)
  @JsonProperty(MIME_TYPE_FIELD)
  protected String mimeType;

  @BsonProperty(DATA_FIELD)
  @JsonProperty(DATA_FIELD)
  protected Binary data;

  @BsonCreator
  public EmailAttachment(
      @BsonProperty(FILE_NAME_FIELD) final String fileName,
      @BsonProperty(MIME_TYPE_FIELD) final String mimeType,
      @BsonProperty(DATA_FIELD) final Binary data) {
    this.fileName = fileName;
    this.mimeType = mimeType;
    this.data = data;
  }

  public EmailAttachment(final String fileName, final String mimeType, final byte[] data) {
    this(fileName, mimeType, new Binary(data));
  }

  public static EmailAttachment fromProto(
      final com.xgen.cloud.services.communication.proto.EmailAttachment proto) {
    return new EmailAttachment(
        proto.hasFileName() ? proto.getFileName() : null,
        proto.hasMimeType() ? proto.getMimeType() : null,
        proto.hasData() ? new Binary(proto.getData().toByteArray()) : null);
  }

  public static com.xgen.cloud.services.communication.proto.EmailAttachment toProto(
      final EmailAttachment emailAttachment) {
    com.xgen.cloud.services.communication.proto.EmailAttachment.Builder protoEmailAttachment =
        com.xgen.cloud.services.communication.proto.EmailAttachment.newBuilder();
    if (emailAttachment.getFileName() != null) {
      protoEmailAttachment = protoEmailAttachment.setFileName(emailAttachment.getFileName());
    }
    if (emailAttachment.getMimeType() != null) {
      protoEmailAttachment = protoEmailAttachment.setMimeType(emailAttachment.getMimeType());
    }
    if (emailAttachment.getData() != null) {
      protoEmailAttachment =
          protoEmailAttachment.setData(ByteString.copyFrom(emailAttachment.getData().getData()));
    }
    return protoEmailAttachment.build();
  }

  @BsonProperty(FILE_NAME_FIELD)
  public String getFileName() {
    return fileName;
  }

  @BsonProperty(MIME_TYPE_FIELD)
  public String getMimeType() {
    return mimeType;
  }

  @BsonProperty(DATA_FIELD)
  public Binary getData() {
    return data;
  }

  @BsonIgnore
  public MimeBodyPart toMimeBodyPart() throws Exception {
    final MimeBodyPart jsonPart = new MimeBodyPart();

    DataSource ds = new ByteArrayDataSource(data.getData(), mimeType);
    jsonPart.setDataHandler(new DataHandler(ds));
    jsonPart.setFileName(fileName);
    jsonPart.setDisposition(Part.ATTACHMENT);

    return jsonPart;
  }

  @Override
  @BsonIgnore
  public boolean equals(final Object untypedOther) {
    if (untypedOther == this) {
      return true;
    }

    if (untypedOther == null || untypedOther.getClass() != getClass()) {
      return false;
    }

    final EmailAttachment other = (EmailAttachment) untypedOther;
    return new EqualsBuilder()
        .append(getFileName(), other.getFileName())
        .append(getMimeType(), other.getMimeType())
        .append(getData(), other.getData())
        .build();
  }

  @Override
  @BsonIgnore
  public int hashCode() {
    return new HashCodeBuilder()
        .append(getFileName())
        .append(getMimeType())
        .append(getData())
        .toHashCode();
  }
}
