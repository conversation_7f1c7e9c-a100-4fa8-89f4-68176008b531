package com.xgen.cloud.services.communication._private.model.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.services.communication.proto.GetIntegrationValidityForProjectResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;

/** Representation of the response of an http message send request */
public class ValidityData {
  @JsonProperty public final Map<ObjectId, MessageValiditySummary> validityBreakdown;

  public ValidityData(final List<MessageValidity> messages) {
    this.validityBreakdown = new HashMap<>();
    messages.forEach(
        messageValidity -> {
          final ObjectId integrationId = messageValidity.getIntegrationId();
          if (integrationId != null) {
            if (!validityBreakdown.containsKey(integrationId)) {
              validityBreakdown.put(integrationId, new MessageValiditySummary(integrationId));
            }
            validityBreakdown.get(integrationId).incorporateValidity(messageValidity);
          }
        });
  }

  public ValidityData(final Map<ObjectId, MessageValiditySummary> validityBreakdown) {
    this.validityBreakdown = validityBreakdown;
  }

  public static GetIntegrationValidityForProjectResponse toProto(final ValidityData validityData) {
    if (validityData == null) {
      return null;
    }

    return GetIntegrationValidityForProjectResponse.newBuilder()
        .addAllIntegrationValidities(
            validityData.validityBreakdown == null
                ? List.of()
                : validityData.validityBreakdown.values().stream()
                    .map(MessageValiditySummary::toProto)
                    .toList())
        .build();
  }

  public static ValidityData fromProto(final GetIntegrationValidityForProjectResponse proto) {
    if (proto == null) {
      return null;
    }

    return new ValidityData(
        proto.getIntegrationValiditiesList().stream()
            .map(MessageValiditySummary::fromProto)
            .collect(
                Collectors.toMap(
                    (final MessageValiditySummary messageValiditySummary) ->
                        messageValiditySummary.integrationId,
                    (final MessageValiditySummary messageValiditySummary) ->
                        messageValiditySummary)));
  }
}
