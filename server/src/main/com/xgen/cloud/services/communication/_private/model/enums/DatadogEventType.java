package com.xgen.cloud.services.communication._private.model.enums;

import com.datadog.api.client.v1.model.EventAlertType;
import java.util.Arrays;
import org.bson.codecs.pojo.annotations.BsonCreator;

public enum DatadogEventType {
  ERROR,
  WARNING,
  INFO,
  SUCCESS,
  USER_UPDATE,
  RECOMMENDATION,
  SNAPSHOT;

  @BsonCreator
  public static DatadogEventType fromString(final String dbValue) {
    return Arrays.stream(values())
        .filter(eventType -> eventType.name().equals(dbValue))
        .findFirst()
        .orElse(null);
  }

  public EventAlertType toDatadogEventAlertType() {
    return EventAlertType.fromValue(this.name());
  }
}
