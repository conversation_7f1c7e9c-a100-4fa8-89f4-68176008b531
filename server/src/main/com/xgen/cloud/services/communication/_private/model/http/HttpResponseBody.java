package com.xgen.cloud.services.communication._private.model.http;

import com.xgen.cloud.services.communication._private.model.enums.ErrorCode;

/** Representation of the response of an http send request */
public class HttpResponseBody {
  private final String externalId;
  private final ErrorCode errorCode;
  private final String message;

  public HttpResponseBody(
      final String externalId, final ErrorCode errorCode, final String message) {
    this.externalId = externalId;
    this.errorCode = errorCode;
    this.message = message;
  }

  public String getExternalId() {
    return externalId;
  }

  public ErrorCode getErrorCode() {
    return errorCode;
  }

  public String getMessage() {
    return message;
  }
}
