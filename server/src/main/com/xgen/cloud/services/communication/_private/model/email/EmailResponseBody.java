package com.xgen.cloud.services.communication._private.model.email;

import com.xgen.cloud.services.communication._private.model.enums.ErrorCode;

/** Representation of the response of a email message send request */
public class EmailResponseBody {
  private final String externalId;
  private final ErrorCode errorCode;

  public EmailResponseBody(final String externalId, final ErrorCode errorCode) {
    this.externalId = externalId;
    this.errorCode = errorCode;
  }

  public String getExternalId() {
    return externalId;
  }

  public ErrorCode getErrorCode() {
    return errorCode;
  }
}
