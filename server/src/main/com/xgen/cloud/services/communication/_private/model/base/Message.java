package com.xgen.cloud.services.communication._private.model.base;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.common.resource._public.model.ResourceId;
import com.xgen.cloud.services.communication._private.model.PublishMessageRequestTransformer;
import com.xgen.cloud.services.communication._private.model.base.Message.FieldDefs;
import com.xgen.cloud.services.communication._private.model.enums.ErrorCode;
import com.xgen.cloud.services.communication._private.model.enums.Status;
import jakarta.annotation.Nonnull;
import java.time.Instant;
import java.util.List;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/** Representation of an interface for sending messages through a third party */
@BsonDiscriminator(key = FieldDefs.PROVIDER_TYPE_FIELD)
public abstract class Message extends ProviderDependentObject {
  public static final String DB_NAME = "mmsdb";
  public static final String COLLECTION_NAME = "communication.messages";

  @BsonId
  @JsonProperty(FieldDefs.ID_FIELD)
  public ObjectId id;

  @BsonProperty(FieldDefs.CREDENTIAL_ID_FIELD)
  @JsonProperty(FieldDefs.CREDENTIAL_ID_FIELD)
  protected ObjectId credentialId;

  @BsonProperty(FieldDefs.CHANNEL_ID_FIELD)
  @JsonProperty(FieldDefs.CHANNEL_ID_FIELD)
  protected ObjectId channelId;

  @BsonProperty(FieldDefs.STATUS_FIELD)
  @JsonProperty(FieldDefs.STATUS_FIELD)
  protected Status status;

  @BsonProperty(FieldDefs.ERROR_CODE_FIELD)
  @JsonProperty(FieldDefs.ERROR_CODE_FIELD)
  protected ErrorCode errorCode;

  @BsonProperty(FieldDefs.RESOURCES_FIELD)
  @JsonProperty(FieldDefs.RESOURCES_FIELD)
  protected List<ResourceId> resources;

  @BsonProperty(FieldDefs.SOURCE_FIELD)
  @JsonProperty(FieldDefs.SOURCE_FIELD)
  protected Source source;

  @Nonnull
  @BsonProperty(FieldDefs.REMAINING_RETRIES_FIELD)
  @JsonProperty(FieldDefs.REMAINING_RETRIES_FIELD)
  protected Integer remainingRetries;

  @BsonProperty(FieldDefs.EXTERNAL_ID_FIELD)
  @JsonProperty(FieldDefs.EXTERNAL_ID_FIELD)
  protected String externalId;

  @BsonProperty(FieldDefs.CREATED_AT_FIELD)
  @JsonProperty(FieldDefs.CREATED_AT_FIELD)
  protected Instant createdAt;

  @BsonProperty(FieldDefs.UPDATED_AT_FIELD)
  @JsonProperty(FieldDefs.UPDATED_AT_FIELD)
  protected Instant updatedAt;

  @BsonProperty(FieldDefs.EXTERNAL_FAILURE_TRACKER_ID_FIELD)
  @JsonProperty(FieldDefs.EXTERNAL_FAILURE_TRACKER_ID_FIELD)
  protected String externalFailureTrackerId;

  @BsonCreator
  public Message(
      @BsonId final ObjectId id,
      @BsonProperty(FieldDefs.CREDENTIAL_ID_FIELD) final ObjectId credentialId,
      @BsonProperty(FieldDefs.CHANNEL_ID_FIELD) final ObjectId channelId,
      @BsonProperty(FieldDefs.RESOURCES_FIELD) final List<ResourceId> resources,
      @BsonProperty(FieldDefs.SOURCE_FIELD) final Source source,
      @BsonProperty(FieldDefs.REMAINING_RETRIES_FIELD) final Integer remainingRetries,
      @BsonProperty(FieldDefs.STATUS_FIELD) final Status status,
      @BsonProperty(FieldDefs.EXTERNAL_ID_FIELD) final String externalId,
      @BsonProperty(FieldDefs.ERROR_CODE_FIELD) final ErrorCode errorCode,
      @BsonProperty(FieldDefs.CREATED_AT_FIELD) final Instant createdAt,
      @BsonProperty(FieldDefs.UPDATED_AT_FIELD) final Instant updatedAt,
      @BsonProperty(FieldDefs.EXTERNAL_FAILURE_TRACKER_ID_FIELD)
          final String externalFailureTrackerId) {
    this.id = id;
    this.credentialId = credentialId;
    this.channelId = channelId;
    this.resources = resources;
    this.remainingRetries = remainingRetries == null ? 0 : remainingRetries;
    this.source = source;
    this.status = status;
    this.externalId = externalId;
    this.errorCode = errorCode;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.externalFailureTrackerId = externalFailureTrackerId;
  }

  // Used for creating from code.
  // Adds provider type and transforms to gson
  public Message(
      @Nonnull final ObjectId id,
      final ObjectId credentialId,
      final ObjectId channelId,
      @Nonnull final List<ResourceId> resources,
      @Nonnull final Source source,
      final int remainingRetries) {
    this(
        id,
        credentialId,
        channelId,
        resources,
        source,
        remainingRetries,
        Status.CREATED,
        null,
        null,
        Instant.now(),
        Instant.now(),
        null);
  }

  // Used for creating from code with externalFailureTrackerId.
  public Message(
      @Nonnull final ObjectId id,
      final ObjectId credentialId,
      final ObjectId channelId,
      @Nonnull final List<ResourceId> resources,
      @Nonnull final Source source,
      final int remainingRetries,
      final String externalFailureTrackerId) {
    this(
        id,
        credentialId,
        channelId,
        resources,
        source,
        remainingRetries,
        Status.CREATED,
        null,
        null,
        Instant.now(),
        Instant.now(),
        externalFailureTrackerId);
  }

  @BsonIgnore
  public static com.xgen.cloud.services.communication.proto.Message.Builder getBaseMessageBuilder(
      final Message message) {
    com.xgen.cloud.services.communication.proto.Message.Builder protoMessage =
        com.xgen.cloud.services.communication.proto.Message.newBuilder();

    if (message.getId() != null) {
      protoMessage = protoMessage.setId(message.getId().toHexString());
    }
    if (message.getProviderType() != null) {
      protoMessage = protoMessage.setProviderType(message.getProviderType().toProtoProviderType());
    }
    if (message.getChannelId() != null) {
      protoMessage = protoMessage.setChannelId(message.getChannelId().toHexString());
    }
    if (message.getCredentialId() != null) {
      protoMessage = protoMessage.setCredentialId(message.getCredentialId().toHexString());
    }
    if (message.getResources() != null) {
      protoMessage =
          protoMessage.addAllResourceIds(
              PublishMessageRequestTransformer.toProtoResourceIds(message.getResources()));
    }
    if (message.getSource() != null) {
      protoMessage =
          protoMessage.setSource(
              PublishMessageRequestTransformer.toProtoSource(message.getSource()));
    }
    if (message.getExternalId() != null) {
      protoMessage = protoMessage.setExternalId(message.getExternalId());
    }
    if (message.getErrorCode() != null) {
      protoMessage =
          protoMessage.setErrorCode(
              PublishMessageRequestTransformer.toProtoErrorCode(message.getErrorCode()));
    }
    if (message.getStatus() != null) {
      protoMessage =
          protoMessage.setStatus(
              PublishMessageRequestTransformer.toProtoStatus(message.getStatus()));
    }
    if (message.getCreatedAt() != null) {
      protoMessage = protoMessage.setCreatedAtEpochMillis(message.getCreatedAt().toEpochMilli());
    }
    if (message.getUpdatedAt() != null) {
      protoMessage = protoMessage.setUpdatedAtEpochMillis(message.getUpdatedAt().toEpochMilli());
    }
    if (message.getExternalFailureTrackerId() != null) {
      protoMessage =
          protoMessage.setExternalFailureTrackerId(message.getExternalFailureTrackerId());
    }

    return protoMessage;
  }

  @BsonId
  public ObjectId getId() {
    return this.id;
  }

  public ObjectId getCredentialId() {
    return this.credentialId;
  }

  public ObjectId getChannelId() {
    return this.channelId;
  }

  public List<ResourceId> getResources() {
    return this.resources;
  }

  public Source getSource() {
    return this.source;
  }

  public int getRemainingRetries() {
    return this.remainingRetries == null ? 0 : this.remainingRetries;
  }

  public Status getStatus() {
    return this.status;
  }

  public ErrorCode getErrorCode() {
    return this.errorCode;
  }

  public String getExternalId() {
    return this.externalId;
  }

  public Instant getCreatedAt() {
    return this.createdAt;
  }

  public Instant getUpdatedAt() {
    return this.updatedAt;
  }

  public String getExternalFailureTrackerId() {
    return this.externalFailureTrackerId;
  }

  @Override
  @BsonIgnore
  public boolean equals(final Object untypedOther) {
    if (untypedOther == this) {
      return true;
    }

    if (untypedOther == null || untypedOther.getClass() != getClass()) {
      return false;
    }

    final Message other = (Message) untypedOther;
    return new EqualsBuilder()
        .append(getId(), other.getId())
        .append(getProviderType(), other.getProviderType())
        .append(getChannelId(), other.getChannelId())
        .append(getCredentialId(), other.getCredentialId())
        .append(getRemainingRetries(), other.getRemainingRetries())
        .append(getExternalId(), other.getExternalId())
        .append(getSource(), other.getSource())
        .append(getStatus(), other.getStatus())
        .append(getResources(), other.getResources())
        .append(getCreatedAt(), other.getCreatedAt())
        .append(getUpdatedAt(), other.getUpdatedAt())
        .append(getExternalFailureTrackerId(), other.getExternalFailureTrackerId())
        .build();
  }

  @Override
  @BsonIgnore
  public int hashCode() {
    return new HashCodeBuilder()
        .append(getId())
        .append(getProviderType())
        .append(getChannelId())
        .append(getCredentialId())
        .append(getRemainingRetries())
        .append(getExternalId())
        .append(getSource())
        .append(getStatus())
        .append(getResources())
        .append(getCreatedAt())
        .append(getUpdatedAt())
        .append(getExternalFailureTrackerId())
        .toHashCode();
  }

  public static class FieldDefs {
    public static final String ID_FIELD = "_id";
    public static final String PROVIDER_TYPE_FIELD = "providerType";
    public static final String CREDENTIAL_ID_FIELD = "credentialId";
    public static final String CHANNEL_ID_FIELD = "channelId";
    public static final String PAYLOAD_FIELD = "payload";
    public static final String RESOURCES_FIELD = "resources";
    public static final String STATUS_FIELD = "status";
    public static final String ERROR_CODE_FIELD = "errorCode";
    public static final String EXTERNAL_ID_FIELD = "externalId";
    public static final String SOURCE_FIELD = "source";
    public static final String REMAINING_RETRIES_FIELD = "remainingRetries";
    public static final String CREATED_AT_FIELD = "createdAt";
    public static final String UPDATED_AT_FIELD = "updatedAt";
    public static final String EXTERNAL_FAILURE_TRACKER_ID_FIELD = "externalFailureTrackerId";
  }
}
