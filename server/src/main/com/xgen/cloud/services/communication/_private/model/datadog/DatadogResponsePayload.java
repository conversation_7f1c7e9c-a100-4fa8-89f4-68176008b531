package com.xgen.cloud.services.communication._private.model.datadog;

import com.datadog.api.client.v1.model.Event;
import com.xgen.cloud.services.communication._private.model.enums.ErrorCode;

/** Representation of the response of a datadog message send request */
public class DatadogResponsePayload {

  private final Event event;

  private final ErrorCode errorCode;

  public DatadogResponsePayload(final Event event, final ErrorCode errorCode) {
    this.event = event;
    this.errorCode = errorCode;
  }

  public Event getEvent() {
    return event;
  }

  public ErrorCode getErrorCode() {
    return errorCode;
  }
}
