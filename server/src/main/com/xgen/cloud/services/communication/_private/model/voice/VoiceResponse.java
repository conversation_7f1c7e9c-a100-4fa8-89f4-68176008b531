package com.xgen.cloud.services.communication._private.model.voice;

import com.xgen.cloud.services.communication._private.model.base.Response;
import com.xgen.cloud.services.communication._private.model.enums.ErrorCode;

/** Representation of the response of a voice message send request */
public class VoiceResponse extends Response<VoiceResponseBody> {
  public final VoiceResponseBody body;

  public VoiceResponse(final String externalId, final ErrorCode errorCode) {
    this.body = new VoiceResponseBody(externalId, errorCode);
  }

  @Override
  public VoiceResponseBody getResponseBody() {
    return body;
  }
}
