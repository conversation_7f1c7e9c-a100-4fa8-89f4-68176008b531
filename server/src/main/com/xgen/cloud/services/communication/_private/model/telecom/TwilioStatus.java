package com.xgen.cloud.services.communication._private.model.telecom;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.List;
import java.util.Optional;

// Helpful guide with a nice diagram here:
// https://www.twilio.com/docs/messaging/guides/outbound-message-status-in-status-callbacks
public enum TwilioStatus {
  ACCEPTED("accepted"), // The message is has been accepted into the pipeline.
  QUEUED("queued"), // The message is queued for sending.
  SCHEDULED("scheduled"), // The message is scheduled to send.

  SENT("sent"), // Successful send to carrier.
  FAILED("failed"), // Failure when sending to carrier.

  DELIVERED("delivered"), // If the message has been delivered to the handset.
  UNDELIVERED("undelivered"), // The message may or may not have successfully reached the user.

  READ("read"), // the user has read the message
  UNKNOWN("unknown"); // Catch-all, if we don't recognize the enum;

  public String value;

  TwilioStatus(final String name) {
    this.value = name;
  }

  @JsonCreator
  public static TwilioStatus fromString(final String str) {
    final List<TwilioStatus> enumValues = List.of(TwilioStatus.values());
    Optional<TwilioStatus> optional =
        enumValues.stream().filter(status -> status.value.equals(str)).findFirst();
    return optional.orElse(TwilioStatus.UNKNOWN);
  }

  @JsonValue
  public String toString() {
    return this.value;
  }

  public List<TwilioStatus> getPossibleFutureStatuses() {
    final List<TwilioStatus> presendList = List.of(SENT, FAILED, DELIVERED, UNDELIVERED, READ);
    final List<TwilioStatus> sendList = List.of(DELIVERED, UNDELIVERED, READ);
    final List<TwilioStatus> deliverList = List.of(READ);
    final List<TwilioStatus> readList = List.of();
    return switch (this) {
      case ACCEPTED -> presendList;
      case QUEUED -> presendList;
      case SCHEDULED -> presendList;
      case SENT -> sendList;
      case FAILED -> sendList;
      case DELIVERED -> deliverList;
      case UNDELIVERED -> deliverList;
      case READ -> readList;
      case UNKNOWN -> presendList;
    };
  }
}
