package com.xgen.cloud.services.communication._private.model.enums;

import com.xgen.cloud.services.communication._private.model.telecom.TwilioVoiceStatus;
import java.util.List;
import org.apache.commons.lang.StringUtils;

public enum ErrorCode {
  /** GLOBAL */
  UNKNOWN("", "An unknown error occurred."),
  INVALID_MESSAGE("", "The message was invalid."),

  /** HTTP */
  HTTP_MOVED_PERMANENTLY("301", "The URL of the requested resource has been changed permanently."),
  HTTP_FOUND("302", "The requested resource has been temporarily moved to a different URL."),
  HTTP_BAD_REQUEST("400", "The request was malformed."),
  HTTP_UNAUTHORIZED("401", "Authorization was not valid."),
  HTTP_FORBIDDEN("403", "Resource unavailable with the provided credentials."),
  HTTP_NOT_FOUND("404", "Cannot find the requested resource."),
  HTTP_METHOD_NOT_ALLOWED("405", "Method Not Allowed."),
  HTTP_GONE("410", "Gone."),
  HTTP_URL_TOO_LONG("414", "URI Too Long."),
  HTTP_UNSUPPORTED_MEDIA_TYPE("415", "Unsupported Media Type."),
  HTTP_RANGE_NOT_SATISFIABLE("416", "Range Not Satisfiable."),
  HTTP_EXPECTATION_FAILED("417", "Expectation Failed."),
  HTTP_IM_A_TEAPOT("418", "I'm a teapot."),
  HTTP_MISDIRECTED_REQUEST("421", "Misdirected Request."),
  HTTP_UNPROCESSABLE_CONTENT("422", "Unprocessable Content."),
  HTTP_LOCKED("423", "Locked."),
  HTTP_FAILED_DEPENDENCY("424", "Failed Dependency."),
  HTTP_TOO_EARLY("425", "Too Early."),
  HTTP_UPGRADE_REQUIRED("426", "Upgrade Required."),
  HTTP_PRECONDITION_REQUIRED("428", "Precondition Required."),
  HTTP_RATE_LIMITED("429", "Too Many Requests."),
  HTTP_REQUEST_HEADER_FIELDS_TOO_LARGE("431", "Request Header Fields Too Large."),
  HTTP_UNAVAILABLE_FOR_LEGAL_REASONS("451", "Unavailable For Legal Reasons."),
  HTTP_INTERNAL_SERVER_ERROR("500", "Internal server error."),
  HTTP_NOT_IMPLEMENTED("501", "Not Implemented."),
  HTTP_BAD_GATEWAY("502", "Bad gateway."),
  HTTP_SERVICE_UNAVAILABLE("503", "Service unavailable."),
  HTTP_GATEWAY_TIMEOUT("504", "Gateway timeout."),
  HTTP_HTTP_VERSION_NOT_SUPPORTED("505", "HTTP Version Not Supported."),
  HTTP_VARIANT_ALSO_NEGOTIATES("506", "Variant Also Negotiates."),
  HTTP_INSUFFICIENT_STORAGE("507", "Insufficient Storage."),
  HTTP_LOOP_DETECTED("508", "Loop Detected."),
  HTTP_NOT_EXTENDED("510", "Not Extended."),
  HTTP_NETWORK_AUTHENTICATION_REQUIRED("511", "Network Authentication Required."),

  /** DATADOG */
  DATADOG_FORBIDDEN("Forbidden", "Key is invalid or does not contain the proper permissions."),
  DATADOG_EVENT_TOO_FAR_PAST(
      "Event too far in the past", "The event being modified is too far in the past"),
  DATADOG_EVENT_OVER_18_HOURS_AGO(
      "EVENT_TOO_FAR_PAST_INTERNAL",
      "Datadog only allows the publishing of events within 18 hours of the event occurring."),
  DATADOG_SOCKET_TIMEOUT(
      "DATADOG_SOCKET_TIMEOUT", "There was a socket timeout while hitting datadog's servers."),

  /** EMAIL */
  EMAIL_RATE_LIMITED("EMAIL_RATE_LIMITED", "Email rate limited."),
  EMAIL_INVALID_ADDRESS("EMAIL_INVALID_ADDRESS", "Email address is invalid."),
  EMAIL_INTERRUPTED("EMAIL_INTERRUPTED", "The email send request was interrupted."),
  EMAIL_TIMED_OUT("EMAIL_TIMED_OUT", "The email send request timed out."),
  EMAIL_UNKNOWN_HOST("EMAIL_UNKNOWN_HOST", "The email provider host could not be reached."),
  EMAIL_NETWORK_UNREACHABLE("network is unreachable", "A network failure occurred."),
  EMAIL_INVALID_ADDRESS_ILLEGAL_SEMICOLON(
      "illegal semicolon", "Email address is invalid. Illegal semicolon."),
  EMAIL_INVALID_ADDRESS_DOMAIN_STARTS_WITH_DOT(
      "domain starts with dot", "Email address is invalid. Domain starts with dot."),
  EMAIL_INVALID_ADDRESS_DOMAIN_ENDS_WITH_DOT(
      "domain ends with dot", "Email address is invalid. Domain ends with dot."),
  EMAIL_INVALID_ADDRESS_DOMAIN_CONTAINS_ILLEGAL_CHARACTER(
      "domain contains illegal character",
      "Email address is invalid. Domain contains illegal character."),
  EMAIL_INVALID_ADDRESS_DOMAIN_CONTAINS_CONTROL_OR_WHITESPACE(
      "domain contains control or whitespace",
      "Email address is invalid. Domain contains control or whitespace."),
  EMAIL_INVALID_ADDRESS_LOCAL_ADDRESS_STARTS_WITH_DOT(
      "local address starts with dot", "Email address is invalid. Local address starts with dot."),
  EMAIL_INVALID_ADDRESS_LOCAL_ADDRESS_CONTAINS_DOT_DOT(
      "local address contains dot-dot",
      "Email address is invalid. Local address contains dot-dot."),
  EMAIL_INVALID_ADDRESS_LOCAL_ADDRESS_CONTAINS_ILLEGAL_CHARACTER(
      "local address contains illegal character",
      "Email address is invalid. Local address contains illegal character."),
  EMAIL_INVALID_ADDRESS_LOCAL_ADDRESS_CONTAINS_CONTROL_OR_WHITESPACE(
      "local address contains control or whitespace",
      "Email address is invalid. Local address contains control or whitespace."),
  EMAIL_INVALID_ADDRESS_ILLEGAL_ADDRESS(
      "illegal address", "Email address is invalid. Illegal address."),

  /** VOICE */
  VOICE_LINE_BUSY("LINE_BUSY", "The line was busy."),
  VOICE_FAILED("FAILED", "The call failed, likely due to an invalid phone number."),
  VOICE_NO_ANSWER("NO_ANSWER", "The user did not answer the call."),
  VOICE_CANCELED("VOICE_CANCELED", "The call was canceled."),

  /** SMS */
  SMS_NULL_MOBILE_NUMBER("NULL_MOBILE_NUMBER", "Mobile number cannot be null."),
  SMS_INVALID_MOBILE_NUMBER("INVALID_MOBILE_NUMBER", "Mobile number is invalid."),
  SMS_INVALID_PHONE_NUMBER("INVALID_PHONE_NUMBER", "Phone number is invalid."),
  SMS_INTERNAL_DISABLED("INTERNAL_DISABLED", "SMS is not available in this environment."),
  SMS_INTERNAL_INVALID_PHONE_NUMBER(
      "INTERNAL_INVALID_PHONE_NUMBER", "Phone number has failed repeatedly and was restricted."),
  SMS_INTERNAL_RATE_LIMITED("INTERNAL_RATE_LIMITED", "SMS was rate limited."),
  SMS_INTERNAL_FAILED_TO_INITIALIZE(
      "INTERNAL_FAILED_TO_INITIALIZE", "Twilio is not configured correctly. Cannot send SMS."),
  SMS_NO_INTERNATIONAL_CALLS(
      "NO_INTERNATIONAL_CALLS", "International calls cannot be made against this number."),
  SMS_SERVER_ERROR("SERVER_ERROR", "Our SMS provider has encountered a service error."),

  SMS_RESTRICTED(
      "RESTRICTED", "SMS has been restricted to this number, due to prior failure volume."),

  SMS_THROTTLED("THROTTLED", "A maximum of 5 sms per minute may be sent to the same recipient."),

  SMS_SANITIZE_FAILED("SANITIZE_FAILED", "Phone number could not be sanitized."),

  SMS_INVALID_TO_NUMBER("21211", "Invalid Phone Number"),

  SMS_COUNTRY_NOT_ENABLED("21408", "SMS is not valid in this country."),

  SMS_OPTED_OUT(
      "21610", "The phone number in question has opted out of sms messaging via the STOP command."),

  SMS_TO_NUMBER_INVALID("21614", "The recipient phone number is not a valid phone number"),

  SMS_UNREACHABLE_DESTINATION_HANDSET("30003", "Unreachable destination handset."),

  SMS_MESSAGE_BLOCKED("30004", "Message blocked"),

  SMS_UNKNOWN_DESTINATION_HANDSET("30005", "Unknown destination handset."),

  SMS_LANDLIINE_OR_UNREACHABLE("30006", "Phone is a landline or unreachable carrier."),

  SMS_UNKNOWN("30008", "Our SMS provider has encountered an unknown error."),

  /** MICROSOFT_TEAMS */
  MICROSOFT_TEAMS_IO_FAILURE("IO_FAILURE", "A network error occured while sending the request."),

  /** PAGERDUTY */
  PAGERDUTY_NOT_OKAY("NOT_OKAY", "Response was not okay."),

  /** SLACK */
  SLACK_TIMEOUT("timeout", "The request timed out."),
  SLACK_AS_USER_NOT_SUPPORTED(
      "as_user_not_supported", "The as_user parameter does not function with workspace apps."),
  SLACK_CHANNEL_NOT_FOUND("channel_not_found", "Value passed for channel was invalid."),
  SLACK_DUPLICATE_CHANNEL_NOT_FOUND(
      "duplicate_channel_not_found", "Channel associated with client_msg_id was invalid."),
  SLACK_DUPLICATE_MESSAGE_NOT_FOUND(
      "duplicate_message_not_found", "No duplicate message exists associated with client_msg_id."),
  SLACK_EKM_ACCESS_DENIED(
      "ekm_access_denied", "Administrators have suspended the ability to post a message."),
  SLACK_INVALID_BLOCKS("invalid_blocks", "Blocks submitted with this message are not valid."),
  SLACK_INVALID_BLOCKS_FORMAT(
      "invalid_blocks_format",
      "The blocks is not a valid JSON object or doesn't match the Block Kit syntax."),
  SLACK_INVALID_METADATA_FORMAT("invalid_metadata_format", "Invalid metadata format provided"),
  SLACK_INVALID_METADATA_SCHEMA("invalid_metadata_schema", "Invalid metadata schema provided."),
  SLACK_IS_ARCHIVED("is_archived", "Channel has been archived."),
  SLACK_MESSAGE_LIMIT_EXCEEDED(
      "message_limit_exceeded",
      "Members on this team are sending too many messages. For more details, see"
          + " https://slack.com/help/articles/115002422943-Usage-limits-for-free-workspaces."),
  SLACK_MESSAGES_TAB_DISABLED("messages_tab_disabled", "Messages tab for the app is disabled."),
  SLACK_METADATA_MUST_BE_SENT_FROM_APP(
      "metadata_must_be_sent_from_app",
      "Message metadata can only be posted or updated using an app-level token"),
  SLACK_METADATA_TOO_LARGE("metadata_too_large", "Metadata exceeds size limit."),
  SLACK_MESSAGE_TOO_LONG("msg_too_long", "Message text is too long."),
  SLACK_NO_TEXT("no_text", "No message text provided."),
  SLACK_NOT_IN_CHANNEL("not_in_channel", "Cannot post user messages to a channel they are not in."),
  SLACK_RATE_LIMITED(
      "rate_limited",
      "Application has posted too many messages, read the Rate Limit documentation for more"
          + " information."),
  SLACK_RESTRICTED_ACTION(
      "restricted_action", "A workspace preference prevents the authenticated user from posting."),
  SLACK_RESTRICTED_ACTION_NON_THREADABLE_CHANNEL(
      "restricted_action_non_threadable_channel",
      "Cannot post thread replies into a non_threadable channel."),
  SLACK_RESTRICTED_ACTION_READ_ONLY_CHANNEL(
      "restricted_action_read_only_channel", "Cannot post any message into a read-only channel."),
  SLACK_RESTRICTED_ACTION_THREAD_LOCKED(
      "restricted_action_thread_locked",
      "Cannot post replies to a thread that has been locked by admins."),
  SLACK_RESTRICTED_ACTION_THREAD_ONLY_CHANNEL(
      "restricted_action_thread_only_channel",
      "Cannot post top-level messages into a thread-only channel."),
  SLACK_CONNECT_CANVAS_SHARING_BLOCKED(
      "slack_connect_canvas_sharing_blocked",
      "Admin has disabled Canvas File sharing in all Slack Connect communications."),
  SLACK_CONNECT_FILE_LINK_SHARING_BLOCKED(
      "slack_connect_file_link_sharing_blocked",
      "Admin has disabled Slack File sharing in all Slack Connect communications."),
  SLACK_CONNECT_LISTS_SHARING_BLOCKED(
      "slack_connect_lists_sharing_blocked",
      "Admin has disabled Lists sharing in all Slack Connect communications."),
  SLACK_TEAM_ACCESS_NOT_GRANTED(
      "team_access_not_granted",
      "The token used is not granted the specific workspace access required to complete this"
          + " request."),
  SLACK_TOO_MANY_ATTACHMENTS(
      "too_many_attachments",
      "Too many attachments were provided with this message. A maximum of 100 attachments are"
          + " allowed on a message."),
  SLACK_TOO_MANY_CONTACT_CARDS(
      "too_many_contact_cards",
      "Too many contact_cards were provided with this message. A maximum of 10 contact cards are"
          + " allowed on a message."),
  SLACK_CANNOT_REPLY_TO_MESSAGE(
      "cannot_reply_to_message", "This message type cannot have thread replies."),
  SLACK_MISSING_FILE_DATA(
      "missing_file_data", "Attempted to share a file but some required data was missing."),
  SLACK_ATTACHMENT_PAYLOAD_LIMIT_EXCEEDED(
      "attachment_payload_limit_exceeded", "Attachment payload size is too long."),
  SLACK_ACCESS_DENIED("access_denied", "Access to a resource specified in the request is denied."),
  SLACK_ACCOUNT_INACTIVE(
      "account_inactive",
      "Authentication token is for a deleted user or workspace when using a bot token."),
  SLACK_ENDPOINT_DEPRECATED("deprecated_endpoint", "The endpoint has been deprecated."),
  SLACK_ENTERPRISE_RESTRICTED(
      "enterprise_is_restricted", "The method cannot be called from an Enterprise."),
  SLACK_INVALID_AUTH(
      "invalid_auth",
      "Some aspect of authentication cannot be validated. Either the provided token is invalid or"
          + " the request originates from an IP address disallowed from making the request."),
  SLACK_METHOD_DEPRECATED("method_deprecated", "The method has been deprecated."),
  SLACK_MISSING_SCOPE(
      "missing_scope",
      "The token used is not granted the specific scope permissions required to complete this"
          + " request."),
  SLACK_NOT_ALLOWED_TOKEN_TYPE(
      "not_allowed_token_type", "The token type used in this request is not allowed."),
  SLACK_NOT_AUTHED("not_authed", "No authentication token provided."),
  SLACK_NO_PERMISSION(
      "no_permission",
      "The workspace token used in this request does not have the permissions necessary to complete"
          + " the request. Make sure your app is a member of the conversation it's attempting to"
          + " post a message to."),
  SLACK_ORG_LOGIN_REQUIRED(
      "org_login_required",
      "The workspace is undergoing an enterprise migration and will not be available until"
          + " migration is complete."),
  SLACK_TOKEN_EXPIRED("token_expired", "Authentication token has expired."),
  SLACK_TOKEN_REVOKED(
      "token_revoked",
      "Authentication token is for a deleted user or workspace or the app has been removed when"
          + " using a user token."),
  SLACK_TWO_FACTOR_SETUP_REQUIRED("two_factor_setup_required", "Two factor setup is required."),
  SLACK_ACCESS_LIMITED("accesslimited", "Access to this method is limited on the current network."),
  SLACK_FATAL_ERROR(
      "fatal_error",
      "The server could not complete your operation(s) without encountering a catastrophic error."
          + " It's possible some aspect of the operation succeeded before the error was raised."),
  SLACK_INTERNAL_ERROR(
      "internal_error",
      "The server could not complete your operation(s) without encountering an error, likely due to"
          + " a transient issue on our end. It's possible some aspect of the operation succeeded"
          + " before the error was raised."),
  SLACK_INVALID_ARGUMENT_NAME(
      "invalid_arg_name",
      "The method was passed an argument whose name falls outside the bounds of accepted or"
          + " expected values. This includes very long names and names with non-alphanumeric"
          + " characters other than _. If you get this error, it is typically an indication that"
          + " you have made a very malformed API call."),
  SLACK_INVALID_ARGUMENTS(
      "invalid_arguments",
      "The method was either called with invalid arguments or some detail about the arguments"
          + " passed is invalid, which is more likely when using complex arguments like blocks or"
          + " attachments."),
  SLACK_INVALID_ARRAY_ARGUMENT(
      "invalid_array_arg",
      "The method was passed an array as an argument. Please only input valid strings."),
  SLACK_INVALID_CHARACTER_SET(
      "invalid_charset",
      "The method was called via a POST request, but the charset specified in the Content-Type"
          + " header was invalid. Valid charset names are: utf-8 iso-8859-1."),
  SLACK_INVALID_FORM_DATA(
      "invalid_form_data",
      "The method was called via a POST request with Content-Type application/x-www-form-urlencoded"
          + " or multipart/form-data, but the form data was either missing or syntactically"
          + " invalid."),
  SLACK_INVALID_POST_TYPE(
      "invalid_post_type",
      "The method was called via a POST request, but the specified Content-Type was invalid. Valid"
          + " types are: application/json application/x-www-form-urlencoded multipart/form-data"
          + " text/plain."),
  SLACK_MISSING_POST_TYPE(
      "missing_post_type",
      "The method was called via a POST request and included a data payload, but the request did"
          + " not include a Content-Type header."),
  // This seems to just be identical except for the string. :sigh:
  SLACK_RATE_LIMITED2(
      "ratelimited",
      "The request has been ratelimited. Refer to the Retry-After header for when to retry the"
          + " request."),
  SLACK_REQUEST_TIMEOUT(
      "request_timeout",
      "The method was called via a POST request, but the POST data was either missing or"
          + " truncated."),
  SLACK_SERVICE_UNAVAILABLE("service_unavailable", "The service is temporarily unavailable"),
  SLACK_TEAM_ADDED_TO_ORG(
      "team_added_to_org",
      "The workspace associated with your request is currently undergoing migration to an"
          + " Enterprise Organization. Web API and other platform operations will be intermittently"
          + " unavailable until the transition is complete."),
  SLACK_LEGACY_CUSTOM_BOTS_DEPRECATED(
      "legacy_custom_bots_deprecated",
      "As of March 31st, 2025, Slack has removed support for legacy custom bots. Please update your"
          + " credentials."),
  UNSPECIFIED(
      "UNSPECIFIED", "Not specified."); // For symmetry when transforming to and from protos.

  private final String externalId;
  private final String userReadableMessage;

  ErrorCode(final String externalId, final String userReadableMessage) {
    this.externalId = externalId;
    this.userReadableMessage = userReadableMessage;
  }

  public boolean isInternal() {
    return StringUtils.isBlank(externalId);
  }

  public String getUserReadableMessage() {
    return userReadableMessage;
  }

  public static ErrorCode fromDatadogMessage(final String datadogErrorMessage) {
    if (StringUtils.isBlank(datadogErrorMessage)) {
      return UNKNOWN;
    }
    for (final ErrorCode errorCodeCandidate : ErrorCode.values()) {
      if (errorCodeCandidate != null
          && errorCodeCandidate.name().startsWith("DATADOG_")
          && datadogErrorMessage.contains(errorCodeCandidate.externalId)) {
        return errorCodeCandidate;
      }
    }
    return UNKNOWN;
  }

  public static ErrorCode fromTelecomSvcError(final String telecomSvcErrorCode) {
    if (StringUtils.isBlank(telecomSvcErrorCode)) {
      return UNKNOWN;
    }
    return switch (telecomSvcErrorCode) {
      case "INVALID_MOBILE_NUMBER" -> SMS_INVALID_MOBILE_NUMBER;
      case "INVALID_PHONE_NUMBER" -> SMS_INVALID_PHONE_NUMBER;
      case "NO_INTERNATIONAL_CALLS" -> SMS_NO_INTERNATIONAL_CALLS;
      case "SERVER_ERROR" -> SMS_SERVER_ERROR;
      case "NUMBER_DOES_NOT_EXIST" -> SMS_TO_NUMBER_INVALID;
      default -> UNKNOWN;
    };
  }

  public static ErrorCode fromTwilioErrorCode(final Integer twilioErrorCode) {
    if (twilioErrorCode == null) {
      return null;
    }
    final String twilioErrorCodeString = twilioErrorCode.toString();
    for (final ErrorCode errorCodeCandidate : ErrorCode.values()) {
      if (errorCodeCandidate != null
          && errorCodeCandidate.name().startsWith("SMS_")
          && errorCodeCandidate.externalId.equals(twilioErrorCodeString)) {
        return errorCodeCandidate;
      }
    }
    return UNKNOWN;
  }

  public static ErrorCode fromTwilioVoiceStatus(final TwilioVoiceStatus twilioVoiceStatus) {
    if (twilioVoiceStatus == null) {
      return null;
    }

    return switch (twilioVoiceStatus) {
      case QUEUED, RINGING, IN_PROGRESS, COMPLETED -> null;
      case BUSY -> VOICE_LINE_BUSY;
      case FAILED -> VOICE_FAILED;
      case NO_ANSWER -> VOICE_NO_ANSWER;
      case CANCELED -> VOICE_CANCELED;
      case UNKNOWN -> UNKNOWN;
    };
  }

  public static ErrorCode fromHttpErrorCode(final int httpErrorCode) {
    final String stringErrorCode = Integer.toString(httpErrorCode);

    if (StringUtils.isBlank(stringErrorCode)) {
      return UNKNOWN;
    }
    for (final ErrorCode errorCodeCandidate : ErrorCode.values()) {
      if (errorCodeCandidate != null
          && errorCodeCandidate.name().startsWith("HTTP_")
          && errorCodeCandidate.externalId.equals(stringErrorCode)) {
        return errorCodeCandidate;
      }
    }
    return UNKNOWN;
  }

  // These are error codes that are determined by matching the SES error message string, because
  // they do not correspond to a specific error type. For example, we might get an
  // <IllegalArgumentException/Exception/Some other random exception class; they aren't very
  // consistent> containing any of these, and if so, the only way to determine which is to string
  // match. Not ideal, but it's demonstrably better than not trying, given that we fall back to
  // unknown anyway.
  static final List<ErrorCode> emailUnknownErrorCodes =
      List.of(
          EMAIL_NETWORK_UNREACHABLE,
          EMAIL_INVALID_ADDRESS_ILLEGAL_SEMICOLON,
          EMAIL_INVALID_ADDRESS_DOMAIN_STARTS_WITH_DOT,
          EMAIL_INVALID_ADDRESS_DOMAIN_ENDS_WITH_DOT,
          EMAIL_INVALID_ADDRESS_DOMAIN_CONTAINS_ILLEGAL_CHARACTER,
          EMAIL_INVALID_ADDRESS_DOMAIN_CONTAINS_CONTROL_OR_WHITESPACE,
          EMAIL_INVALID_ADDRESS_LOCAL_ADDRESS_STARTS_WITH_DOT,
          EMAIL_INVALID_ADDRESS_LOCAL_ADDRESS_CONTAINS_DOT_DOT,
          EMAIL_INVALID_ADDRESS_LOCAL_ADDRESS_CONTAINS_ILLEGAL_CHARACTER,
          EMAIL_INVALID_ADDRESS_LOCAL_ADDRESS_CONTAINS_CONTROL_OR_WHITESPACE,
          EMAIL_INVALID_ADDRESS_ILLEGAL_ADDRESS);

  public static ErrorCode fromUnknownEmailErrorMessage(final String errorMessage) {
    if (errorMessage == null) {
      return UNKNOWN;
    }

    final String lowerCaseErrorMessage = errorMessage.toLowerCase();
    for (final ErrorCode errorCodeCandidate : emailUnknownErrorCodes) {
      if (lowerCaseErrorMessage.contains(errorCodeCandidate.externalId)) {
        return errorCodeCandidate;
      }
    }

    return UNKNOWN;
  }

  public static ErrorCode fromSlackMessage(final String slackErrorMessage) {
    if (StringUtils.isBlank(slackErrorMessage)) {
      return UNKNOWN;
    }
    for (final ErrorCode errorCodeCandidate : ErrorCode.values()) {
      if (errorCodeCandidate != null
          && errorCodeCandidate.name().startsWith("SLACK_")
          && errorCodeCandidate.externalId.contains(slackErrorMessage)) {
        return errorCodeCandidate;
      }
    }
    return UNKNOWN;
  }

  public static ErrorCode fromString(final String stringErrorCode) {
    if (StringUtils.isBlank(stringErrorCode)) {
      return null;
    }
    for (final ErrorCode errorCodeCandidate : ErrorCode.values()) {
      if (errorCodeCandidate != null && errorCodeCandidate.name().equals(stringErrorCode)) {
        return errorCodeCandidate;
      }
    }
    return UNKNOWN;
  }

  public void throwException() {
    throw new CommunicationException(this);
  }
}
