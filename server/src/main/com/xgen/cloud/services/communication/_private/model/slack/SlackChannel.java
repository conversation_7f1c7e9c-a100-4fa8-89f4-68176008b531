package com.xgen.cloud.services.communication._private.model.slack;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.services.communication._private.model.base.Channel;
import com.xgen.cloud.services.communication._private.model.enums.ProviderType;
import java.time.Instant;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/** Representation of a slack messaging channel */
@BsonDiscriminator(key = Channel.PROVIDER_TYPE_FIELD, value = ProviderType.CHANNEL_SLACK)
public class SlackChannel extends Channel {
  public static final String CHANNEL_NAME_FIELD = "channel";

  @BsonProperty(CHANNEL_NAME_FIELD)
  @JsonProperty(CHANNEL_NAME_FIELD)
  protected String channelName;

  @BsonCreator
  public SlackChannel(
      @BsonId final ObjectId id,
      @BsonProperty(CHANNEL_NAME_FIELD) String channelName,
      @BsonProperty(CREATED_AT_FIELD) Instant createdAt,
      @BsonProperty(UPDATED_AT_FIELD) Instant updatedAt) {
    super(id, createdAt, updatedAt);
    this.channelName = channelName;
  }

  public SlackChannel(final String channelName) {
    super(new ObjectId(), Instant.now(), Instant.now());
    this.channelName = channelName;
  }

  @BsonIgnore
  public static SlackChannel fromProto(
      final com.xgen.cloud.services.communication.proto.Channel protoChannel) {
    final com.xgen.cloud.services.communication.proto.SlackChannel protoSlackChannel =
        protoChannel.getSlackChannel();
    return new SlackChannel(
        new ObjectId(protoChannel.getId()),
        protoSlackChannel.getChannelName(),
        Instant.ofEpochMilli(protoChannel.getCreatedAtEpochMilli()),
        Instant.ofEpochMilli(protoChannel.getUpdatedAtEpochMilli()));
  }

  @BsonIgnore
  public static com.xgen.cloud.services.communication.proto.Channel toProto(
      final SlackChannel channel) {
    com.xgen.cloud.services.communication.proto.SlackChannel.Builder protoSlackChannel =
        com.xgen.cloud.services.communication.proto.SlackChannel.newBuilder();
    if (channel.getChannelName() != null) {
      protoSlackChannel = protoSlackChannel.setChannelName(channel.getChannelName());
    }

    return getBaseChannelBuilder(channel).setSlackChannel(protoSlackChannel).build();
  }

  @Override
  @BsonIgnore
  public ProviderType getProviderType() {
    return ProviderType.SLACK;
  }

  public String getChannelName() {
    return channelName;
  }

  @Override
  @BsonIgnore
  public boolean equals(final Object untypedOther) {
    if (untypedOther == this) {
      return true;
    }

    if (untypedOther == null || untypedOther.getClass() != getClass()) {
      return false;
    }

    final Channel parentOther = (Channel) untypedOther;
    final SlackChannel other = (SlackChannel) untypedOther;
    return super.equals(parentOther)
        && new EqualsBuilder().append(getChannelName(), other.getChannelName()).build();
  }

  @Override
  @BsonIgnore
  public int hashCode() {
    return new HashCodeBuilder().append(super.hashCode()).append(getChannelName()).toHashCode();
  }
}
