package com.xgen.cloud.services.communication._private.model.voice;

import com.xgen.cloud.services.communication._private.model.base.Credentials;
import com.xgen.cloud.services.communication._private.model.enums.ProviderType;
import java.time.Instant;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/** Representation of voice credentials */
@BsonDiscriminator(key = Credentials.PROVIDER_TYPE_FIELD, value = ProviderType.CREDENTIALS_VOICE)
public class VoiceCredentials extends Credentials {
  @BsonIgnore
  @Override
  public ProviderType getProviderType() {
    return ProviderType.VOICE;
  }

  @BsonCreator
  public VoiceCredentials(
      @BsonId final ObjectId id,
      @BsonProperty(CREATED_AT_FIELD) Instant createdAt,
      @BsonProperty(UPDATED_AT_FIELD) Instant updatedAt) {
    super(id, createdAt, updatedAt);
  }

  public VoiceCredentials() {
    super(new ObjectId(), Instant.now(), Instant.now());
  }
}
