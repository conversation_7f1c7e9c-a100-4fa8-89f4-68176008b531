package com.xgen.cloud.services.communication._private.model.slack;

import com.slack.api.methods.response.chat.ChatPostMessageResponse;
import com.xgen.cloud.services.communication._private.model.base.Response;

/** Representation of the response of a slack message send request */
public class SlackResponse extends Response<ChatPostMessageResponse> {
  private final ChatPostMessageResponse responseBody;

  public SlackResponse(final ChatPostMessageResponse responseBody) {
    this.responseBody = responseBody;
  }

  @Override
  public ChatPostMessageResponse getResponseBody() {
    return responseBody;
  }
}
