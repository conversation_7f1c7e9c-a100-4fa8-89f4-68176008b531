package com.xgen.cloud.services.communication._private.model.slack;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.common.resource._public.model.ResourceId;
import com.xgen.cloud.services.communication._private.model.PublishMessageRequestTransformer;
import com.xgen.cloud.services.communication._private.model.base.Message;
import com.xgen.cloud.services.communication._private.model.base.Message.FieldDefs;
import com.xgen.cloud.services.communication._private.model.base.MessageSubclass;
import com.xgen.cloud.services.communication._private.model.base.Source;
import com.xgen.cloud.services.communication._private.model.enums.ErrorCode;
import com.xgen.cloud.services.communication._private.model.enums.ProviderType;
import com.xgen.cloud.services.communication._private.model.enums.Status;
import java.time.Instant;
import java.util.List;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.bson.codecs.pojo.annotations.BsonCreator;
import org.bson.codecs.pojo.annotations.BsonDiscriminator;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonIgnore;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/** Representation of a slack message */
@BsonDiscriminator(key = FieldDefs.PROVIDER_TYPE_FIELD, value = ProviderType.MESSAGE_SLACK)
public class SlackMessage extends Message implements MessageSubclass<SlackPayload> {

  private final SlackPayload payload;

  @BsonCreator
  public SlackMessage(
      @BsonId final ObjectId id,
      @BsonProperty(FieldDefs.CREDENTIAL_ID_FIELD) final ObjectId credentialId,
      @BsonProperty(FieldDefs.CHANNEL_ID_FIELD) final ObjectId channelId,
      @BsonProperty(FieldDefs.PAYLOAD_FIELD) final SlackPayload payload,
      @BsonProperty(FieldDefs.RESOURCES_FIELD) final List<ResourceId> resources,
      @BsonProperty(FieldDefs.SOURCE_FIELD) final Source source,
      @BsonProperty(FieldDefs.REMAINING_RETRIES_FIELD) final Integer remainingRetries,
      @BsonProperty(FieldDefs.STATUS_FIELD) final Status status,
      @BsonProperty(FieldDefs.EXTERNAL_ID_FIELD) final String externalId,
      @BsonProperty(FieldDefs.ERROR_CODE_FIELD) ErrorCode errorCode,
      @BsonProperty(FieldDefs.CREATED_AT_FIELD) final Instant createdAt,
      @BsonProperty(FieldDefs.UPDATED_AT_FIELD) final Instant updatedAt,
      @BsonProperty(FieldDefs.EXTERNAL_FAILURE_TRACKER_ID_FIELD)
          final String externalFailureTrackerId) {
    super(
        id,
        credentialId,
        channelId,
        resources,
        source,
        remainingRetries,
        status,
        externalId,
        errorCode,
        createdAt,
        updatedAt,
        externalFailureTrackerId);
    this.payload = payload;
  }

  public SlackMessage(
      final com.xgen.cloud.services.communication.proto.PublishMessageRequest publishMessageRequest,
      final ObjectId credentialId,
      final ObjectId channelId) {
    super(
        new ObjectId(publishMessageRequest.getId()),
        credentialId,
        channelId,
        PublishMessageRequestTransformer.fromProtoResourceIds(
            publishMessageRequest.getResourceIdsList()),
        PublishMessageRequestTransformer.fromProtoSource(publishMessageRequest.getSource()),
        (int) publishMessageRequest.getRemainingRetries());
    this.payload = SlackPayload.fromProto(publishMessageRequest.getPayload());
  }

  public static com.xgen.cloud.services.communication.proto.Message toProto(
      final SlackMessage message) {
    return getBaseMessageBuilder(message)
        .setPayload(SlackPayload.toProto(message.getPayload()))
        .build();
  }

  public static SlackMessage fromProto(
      final com.xgen.cloud.services.communication.proto.Message protoMessage) {
    return new SlackMessage(
        new ObjectId(protoMessage.getId()),
        protoMessage.hasCredentialId() ? new ObjectId(protoMessage.getCredentialId()) : null,
        protoMessage.hasChannelId() ? new ObjectId(protoMessage.getChannelId()) : null,
        SlackPayload.fromProto(protoMessage.getPayload()),
        PublishMessageRequestTransformer.fromProtoResourceIds(protoMessage.getResourceIdsList()),
        protoMessage.hasSource()
            ? PublishMessageRequestTransformer.fromProtoSource(protoMessage.getSource())
            : null,
        protoMessage.hasRemainingRetries() ? protoMessage.getRemainingRetries() : null,
        PublishMessageRequestTransformer.fromProtoStatus(protoMessage.getStatus()),
        protoMessage.hasExternalId() ? protoMessage.getExternalId() : null,
        protoMessage.hasErrorCode()
            ? PublishMessageRequestTransformer.fromProtoErrorCode(protoMessage.getErrorCode())
            : null,
        Instant.ofEpochMilli(protoMessage.getCreatedAtEpochMillis()),
        Instant.ofEpochMilli(protoMessage.getUpdatedAtEpochMillis()),
        protoMessage.hasExternalFailureTrackerId()
            ? protoMessage.getExternalFailureTrackerId()
            : null);
  }

  @Override
  @BsonIgnore
  @JsonProperty(FieldDefs.PROVIDER_TYPE_FIELD)
  public ProviderType getProviderType() {
    return ProviderType.SLACK;
  }

  @Override
  @BsonProperty(FieldDefs.PAYLOAD_FIELD)
  @JsonProperty(FieldDefs.PAYLOAD_FIELD)
  public SlackPayload getPayload() {
    return payload;
  }

  @Override
  @BsonIgnore
  public boolean equals(final Object untypedOther) {
    if (untypedOther == this) {
      return true;
    }

    if (untypedOther == null || untypedOther.getClass() != getClass()) {
      return false;
    }

    final Message parentOther = (Message) untypedOther;
    final SlackMessage other = (SlackMessage) untypedOther;
    return super.equals(parentOther)
        && new EqualsBuilder()
            .append(getId(), other.getId())
            .append(getPayload(), other.getPayload())
            .build();
  }

  @Override
  @BsonIgnore
  public int hashCode() {
    return new HashCodeBuilder().append(super.hashCode()).append(getPayload()).toHashCode();
  }
}
