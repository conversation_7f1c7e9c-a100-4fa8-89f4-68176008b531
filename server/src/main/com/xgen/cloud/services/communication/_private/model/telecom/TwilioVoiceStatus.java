package com.xgen.cloud.services.communication._private.model.telecom;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.List;
import java.util.Optional;

// Helpful guide here: https://www.twilio.com/docs/voice/api/call-resource#call-status-values
public enum TwilioVoiceStatus {
  QUEUED("queued"), // The call is ready and waiting in line before dialing.
  RINGING("ringing"), // The call is currently ringing.
  IN_PROGRESS("in-progress"), // The call was answered and is currently in progress.
  COMPLETED("completed"), // The call was answered and has ended normally.
  BUSY("busy"), // 	The caller received a busy signal.
  FAILED("failed"), // The call could not be completed as dialed, most likely because the provided
  // number was invalid.
  NO_ANSWER("no-answer"), // There was no answer or the call was rejected.
  CANCELED("canceled"), // The call was hung up while it was queued or ringing.
  UNKNOWN("unknown"); // Catch-all, if we don't recognize the enum;

  public String value;

  TwilioVoiceStatus(final String name) {
    this.value = name;
  }

  @JsonCreator
  public static TwilioVoiceStatus fromString(final String str) {
    final List<TwilioVoiceStatus> enumValues = List.of(TwilioVoiceStatus.values());
    Optional<TwilioVoiceStatus> optional =
        enumValues.stream().filter(status -> status.value.equals(str)).findFirst();
    return optional.orElse(TwilioVoiceStatus.UNKNOWN);
  }

  @JsonValue
  public String toString() {
    return this.value;
  }

  public List<TwilioVoiceStatus> getPossibleFutureStatuses() {
    final List<TwilioVoiceStatus> presendList =
        List.of(RINGING, IN_PROGRESS, COMPLETED, BUSY, FAILED, NO_ANSWER, CANCELED, UNKNOWN);
    final List<TwilioVoiceStatus> sendList =
        List.of(IN_PROGRESS, COMPLETED, BUSY, FAILED, NO_ANSWER, CANCELED, UNKNOWN);
    final List<TwilioVoiceStatus> deliverList =
        List.of(COMPLETED, BUSY, FAILED, NO_ANSWER, CANCELED, UNKNOWN);
    final List<TwilioVoiceStatus> finishedList = List.of();
    return switch (this) {
      case QUEUED, UNKNOWN -> presendList;
      case RINGING -> sendList;
      case IN_PROGRESS -> deliverList;
      case COMPLETED, BUSY, FAILED, NO_ANSWER, CANCELED -> finishedList;
    };
  }
}
