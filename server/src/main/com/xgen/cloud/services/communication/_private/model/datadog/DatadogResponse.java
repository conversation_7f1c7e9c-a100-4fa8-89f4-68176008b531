package com.xgen.cloud.services.communication._private.model.datadog;

import com.datadog.api.client.v1.model.Event;
import com.xgen.cloud.services.communication._private.model.base.Response;
import com.xgen.cloud.services.communication._private.model.enums.ErrorCode;

/** Representation of the response of a datadog message send request */
public class DatadogResponse extends Response<DatadogResponsePayload> {

  private final DatadogResponsePayload body;

  public DatadogResponse(final Event event, final ErrorCode errorCode) {
    this.body = new DatadogResponsePayload(event, errorCode);
  }

  @Override
  public DatadogResponsePayload getResponseBody() {
    return body;
  }
}
