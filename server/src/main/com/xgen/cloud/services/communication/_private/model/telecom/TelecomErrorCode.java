package com.xgen.cloud.services.communication._private.model.telecom;

import com.xgen.cloud.common.model._public.error.ErrorCode;

public enum TelecomErrorCode implements ErrorCode {
  INVALID_URL(
      "The provided url was invalid, and must include a protocol, hostname, file path, "
          + "url-encoded query parameters, and must be reachable over public internet."),
  INVALID_PHONE_NUMBER(
      "Phone numbers must use E.164 format ([+] [country code] [ number with area code]."),
  NO_INTERNATIONAL_CALLS(
      "International calls are not permitted under the current geographic permissions."),
  INVALID_MOBILE_NUMBER(
      "SMS cannot be sent to the provided mobile number. "
          + "Confirm that the number is not a landline and is in E.164 format."),

  // Internal Error Codes
  LOCAL_URL_UNREACHABLE(
      "Cannot send voice call via localhost. "
          + "All URLs provided to <PERSON><PERSON><PERSON> must be accessible over the public internet. "
          + "To test sending calls locally, ensure your ngrok client is running correctly. "),
  NUMBER_DOES_NOT_EXIST(
      "The destination number you are trying to reach is unknown and may no longer exist."),
  RATE_LIMITED,
  NO_PHONE_NUMBER,
  UNKNOWN,
  SUCCESS;

  private final String message;

  private final String messageFormat;

  private TelecomErrorCode(final String message, final String messageFormat) {
    this.message = message;
    this.messageFormat = messageFormat;
  }

  private TelecomErrorCode(final String message) {
    this(message, null);
  }

  private TelecomErrorCode() {
    this(null, null);
  }

  @Override
  public String getMessage() {
    return message == null ? name() : message;
  }

  @Override
  public String formatMessage(final Object... pParams) {
    return messageFormat == null ? getMessage() : String.format(messageFormat, pParams);
  }
}
