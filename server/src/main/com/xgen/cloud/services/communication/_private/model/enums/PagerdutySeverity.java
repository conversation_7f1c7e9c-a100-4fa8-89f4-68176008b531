package com.xgen.cloud.services.communication._private.model.enums;

// Pagerduty's event severities are documented here:
// https://support.pagerduty.com/docs/dynamic-notifications
public enum PagerdutySeverity {
  // critical - A failure in the system's primary application.
  // Uses high-urgency notification rules and escalates if not acknowledged.
  CRITICAL("critical"),
  // error - Any error which is fatal to the operation, but not the service or application.
  // Uses high-urgency notification rules and escalates if not acknowledged.
  ERROR("error"),
  // warning - May indicate that an error will occur if action is not taken.
  // Uses low-urgency notification rules and does not automatically escalate.
  WARNING("warning"),
  // info - Normal operational messages that require no action.
  // Uses low-urgency notification rules and does not automatically escalate.
  INFO("info");

  private final String name;

  PagerdutySeverity(String name) {
    this.name = name;
  }

  @Override
  public String toString() {
    return this.name;
  }

  public static PagerdutySeverity fromString(final String value) {
    for (final PagerdutySeverity severityCandidate : values()) {
      if (severityCandidate.name.equals(value)) {
        return severityCandidate;
      }
    }
    throw new Error(String.format("No matching PagerdutySeverity for string %s", value));
  }
}
