package com.xgen.cloud.services.config._private.model.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.protobuf.Message;
import com.xgen.cloud.common.event._public.model.SerializableEvent;
import com.xgen.events.schemas.config.v1.ConfigNamespaceAuditEventMessage;
import jakarta.validation.constraints.NotNull;
import java.util.List;

public class ConfigNamespaceAudit implements SerializableEvent {

  public static final String EVENT_TYPE_PREFIX = "/events/mms/config/";

  public static final String TYPE_FIELD = "type";
  public static final String NAMESPACE_NAME_FIELD = "namespaceName";
  public static final String AUTH_ROLES_FIELD = "authRoles";
  public static final String UPDATED_AUTH_ROLES_FIELD = "updatedAuthRoles";

  @JsonProperty(TYPE_FIELD)
  private String eventType;

  @JsonProperty(NAMESPACE_NAME_FIELD)
  private String namespaceName;

  @JsonProperty(AUTH_ROLES_FIELD)
  private List<String> authRoles;

  @JsonProperty(UPDATED_AUTH_ROLES_FIELD)
  private List<String> updatedAuthRoles;

  public ConfigNamespaceAudit(
      @NotNull String eventType,
      @NotNull String namespaceName,
      List<String> authRoles,
      List<String> updatedAuthRoles) {
    if (eventType == null) {
      throw new IllegalArgumentException("Namespace event type cannot be null");
    }
    if (namespaceName == null) {
      throw new IllegalArgumentException("Namespace name cannot be null");
    }
    this.eventType = eventType;
    this.namespaceName = namespaceName;
    this.authRoles = authRoles;
    this.updatedAuthRoles = updatedAuthRoles;
  }

  @Override
  public String getMMSEventType() {
    return eventType;
  }

  @Override
  public String getEventType() {
    return EVENT_TYPE_PREFIX + getMMSEventType();
  }

  @Override
  public Message toProto() {
    final ConfigNamespaceAuditEventMessage.Builder messageBuilder =
        ConfigNamespaceAuditEventMessage.newBuilder().setNamespaceName(namespaceName);
    if (authRoles != null) {
      messageBuilder.addAllAuthRoles(authRoles);
    }
    if (updatedAuthRoles != null) {
      messageBuilder.addAllUpdatedAuthRoles(updatedAuthRoles);
    }
    return messageBuilder.build();
  }

  @Override
  public String toString() {
    return "ConfigNamespaceAudit{"
        + "eventType='"
        + eventType
        + '\''
        + ", namespaceName='"
        + namespaceName
        + '\''
        + ", authRoles="
        + authRoles
        + ", updatedAuthRoles="
        + updatedAuthRoles
        + '}';
  }
}
