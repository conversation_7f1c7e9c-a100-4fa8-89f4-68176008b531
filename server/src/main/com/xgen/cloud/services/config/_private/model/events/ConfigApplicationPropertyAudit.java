package com.xgen.cloud.services.config._private.model.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.protobuf.Message;
import com.xgen.cloud.common.event._public.model.SerializableEvent;
import com.xgen.events.schemas.config.v1.ConfigApplicationPropertyAuditEventMessage;
import jakarta.validation.constraints.NotNull;

public class ConfigApplicationPropertyAudit implements SerializableEvent {
  public static final String EVENT_TYPE_PREFIX = "/events/mms/config/";

  public static final String TYPE_FIELD = "type";
  public static final String NAME_FIELD = "name";
  public static final String VALUE_FIELD = "value";
  public static final String UPDATED_VALUE_FIELD = "updatedValue";
  public static final String NAMESPACE_FIELD = "namespace";

  @JsonProperty(TYPE_FIELD)
  private String eventType;

  @JsonProperty(NAME_FIELD)
  private String name;

  @JsonProperty(VALUE_FIELD)
  private String value;

  @JsonProperty(UPDATED_VALUE_FIELD)
  private String updatedValue;

  @JsonProperty(NAMESPACE_FIELD)
  private String namespace;

  public ConfigApplicationPropertyAudit(
      @NotNull String eventType,
      @NotNull String name,
      @NotNull String value,
      String updatedValue,
      @NotNull String namespace) {
    if (eventType == null) {
      throw new IllegalArgumentException("Application property event type cannot be null");
    }
    if (name == null) {
      throw new IllegalArgumentException("Application property name cannot be null");
    }
    if (value == null) {
      throw new IllegalArgumentException("Application property value cannot be null");
    }
    if (namespace == null) {
      throw new IllegalArgumentException("Application property namespace cannot be null");
    }
    this.eventType = eventType;
    this.name = name;
    this.value = value;
    this.updatedValue = updatedValue;
    this.namespace = namespace;
  }

  @Override
  public String getMMSEventType() {
    return eventType;
  }

  @Override
  public String getEventType() {
    return EVENT_TYPE_PREFIX + getMMSEventType();
  }

  @Override
  public Message toProto() {
    ConfigApplicationPropertyAuditEventMessage.Builder messageBuilder =
        ConfigApplicationPropertyAuditEventMessage.newBuilder()
            .setName(name)
            .setValue(value)
            .setNamespace(namespace);

    if (updatedValue != null) {
      messageBuilder.setUpdatedValue(updatedValue);
    }

    return messageBuilder.build();
  }

  @Override
  public String toString() {
    return "ConfigApplicationPropertyAudit{"
        + "eventType='"
        + eventType
        + '\''
        + ", name='"
        + name
        + '\''
        + ", value='"
        + value
        + '\''
        + ", updatedValue='"
        + updatedValue
        + '\''
        + ", namespace='"
        + namespace
        + '\''
        + '}';
  }
}
