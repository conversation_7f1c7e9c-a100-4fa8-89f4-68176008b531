package com.xgen.cloud.services.config._private.model.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.protobuf.Message;
import com.xgen.cloud.common.event._public.model.SerializableEvent;
import com.xgen.events.schemas.config.v1.ConfigFeatureFlagAuditEventMessage;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

public class ConfigFeatureFlagAudit implements SerializableEvent {

  public static final String EVENT_TYPE_PREFIX = "/events/mms/config/";

  public static final String TYPE_FIELD = "type";
  public static final String NAME_FIELD = "name";
  public static final String NAMESPACE_FIELD = "namespace";
  public static final String ALLOW_LIST_FIELD = "allowList";
  public static final String BLOCK_LIST_FIELD = "blockList";
  public static final String ROLLOUT_PERCENTAGE_FIELD = "rolloutPercentage";
  public static final String EMERGENCY_PHASE_FIELD = "emergencyPhase";
  public static final String UPDATED_ALLOW_LIST_FIELD = "updatedAllowList";
  public static final String UPDATED_BLOCK_LIST_FIELD = "updatedBlockList";
  public static final String UPDATED_ROLLOUT_PERCENTAGE_FIELD = "updatedRolloutPercentage";
  public static final String UPDATED_EMERGENCY_PHASE_FIELD = "updatedEmergencyPhase";

  @JsonProperty(TYPE_FIELD)
  private String eventType;

  @JsonProperty(NAME_FIELD)
  private String name;

  @JsonProperty(NAMESPACE_FIELD)
  private String namespace;

  @JsonProperty(ALLOW_LIST_FIELD)
  private List<String> allowList;

  @JsonProperty(UPDATED_ALLOW_LIST_FIELD)
  private List<String> updatedAllowList;

  @JsonProperty(BLOCK_LIST_FIELD)
  private List<String> blockList;

  @JsonProperty(UPDATED_BLOCK_LIST_FIELD)
  private List<String> updatedBlockList;

  @JsonProperty(ROLLOUT_PERCENTAGE_FIELD)
  private Integer rolloutPercentage;

  @JsonProperty(UPDATED_ROLLOUT_PERCENTAGE_FIELD)
  private Integer updatedRolloutPercentage;

  @JsonProperty(EMERGENCY_PHASE_FIELD)
  private String emergencyPhase;

  @JsonProperty(UPDATED_EMERGENCY_PHASE_FIELD)
  private String updatedEmergencyPhase;

  public ConfigFeatureFlagAudit(
      @NotNull String eventType,
      @NotNull String name,
      @NotNull String namespace,
      List<String> allowList,
      List<String> updatedAllowList,
      List<String> blockList,
      List<String> updatedBlockList,
      Integer rolloutPercentage,
      Integer updatedRolloutPercentage,
      String emergencyPhase,
      String updatedEmergencyPhase) {
    if (eventType == null) {
      throw new IllegalArgumentException("Feature flag event type cannot be null");
    }
    if (name == null) {
      throw new IllegalArgumentException("Feature flag name cannot be null");
    }
    if (namespace == null) {
      throw new IllegalArgumentException("Feature flag namespace cannot be null");
    }
    this.eventType = eventType;
    this.name = name;
    this.namespace = namespace;
    this.allowList = allowList;
    this.updatedAllowList = updatedAllowList;
    this.blockList = blockList;
    this.updatedBlockList = updatedBlockList;
    this.rolloutPercentage = rolloutPercentage;
    this.updatedRolloutPercentage = updatedRolloutPercentage;
    this.emergencyPhase = emergencyPhase;
    this.updatedEmergencyPhase = updatedEmergencyPhase;
  }

  public ConfigFeatureFlagAudit(Builder builder) {
    this(
        builder.eventType,
        builder.name,
        builder.namespace,
        builder.allowList,
        builder.updatedAllowList,
        builder.blockList,
        builder.updatedBlockList,
        builder.rolloutPercentage,
        builder.updatedRolloutPercentage,
        builder.emergencyPhase,
        builder.updatedEmergencyPhase);
  }

  @Override
  public String getMMSEventType() {
    return eventType;
  }

  @Override
  public String getEventType() {
    return EVENT_TYPE_PREFIX + getMMSEventType();
  }

  @Override
  public Message toProto() {
    final ConfigFeatureFlagAuditEventMessage.Builder messageBuilder =
        ConfigFeatureFlagAuditEventMessage.newBuilder().setName(name).setNamespace(namespace);
    Optional.ofNullable(allowList).ifPresent(messageBuilder::addAllAllowList);
    Optional.ofNullable(blockList).ifPresent(messageBuilder::addAllBlockList);
    Optional.ofNullable(updatedAllowList).ifPresent(messageBuilder::addAllUpdatedAllowList);
    Optional.ofNullable(updatedBlockList).ifPresent(messageBuilder::addAllUpdatedBlockList);
    Optional.ofNullable(rolloutPercentage).ifPresent(messageBuilder::setRolloutPercentage);
    Optional.ofNullable(updatedRolloutPercentage)
        .ifPresent(messageBuilder::setUpdatedRolloutPercentage);
    Optional.ofNullable(emergencyPhase).ifPresent(messageBuilder::setEmergencyPhase);
    Optional.ofNullable(updatedEmergencyPhase).ifPresent(messageBuilder::setUpdatedEmergencyPhase);
    return messageBuilder.build();
  }

  public static Builder builder(String eventType) {
    return new Builder(eventType);
  }

  @Override
  public String toString() {
    return "ConfigFeatureFlagAudit{"
        + "eventType='"
        + eventType
        + '\''
        + ", name='"
        + name
        + '\''
        + ", namespace='"
        + namespace
        + '\''
        + ", allowList="
        + allowList
        + ", updatedAllowList="
        + updatedAllowList
        + ", blockList="
        + blockList
        + ", updatedBlockList="
        + updatedBlockList
        + ", rolloutPercentage="
        + rolloutPercentage
        + ", updatedRolloutPercentage="
        + updatedRolloutPercentage
        + ", emergencyPhase='"
        + emergencyPhase
        + '\''
        + ", updatedEmergencyPhase='"
        + updatedEmergencyPhase
        + '\''
        + '}';
  }

  public String getName() {
    return name;
  }

  public String getNamespace() {
    return namespace;
  }

  public List<String> getAllowList() {
    return allowList;
  }

  public List<String> getUpdatedAllowList() {
    return updatedAllowList;
  }

  public List<String> getBlockList() {
    return blockList;
  }

  public List<String> getUpdatedBlockList() {
    return updatedBlockList;
  }

  public Integer getRolloutPercentage() {
    return rolloutPercentage;
  }

  public Integer getUpdatedRolloutPercentage() {
    return updatedRolloutPercentage;
  }

  public String getEmergencyPhase() {
    return emergencyPhase;
  }

  public String getUpdatedEmergencyPhase() {
    return updatedEmergencyPhase;
  }

  public static class Builder {

    private String eventType;
    private String name;
    private String namespace;
    private List<String> allowList;
    private List<String> updatedAllowList;
    private List<String> blockList;
    private List<String> updatedBlockList;
    private Integer rolloutPercentage;
    private Integer updatedRolloutPercentage;
    private String emergencyPhase;
    private String updatedEmergencyPhase;

    private Builder(final String eventType) {
      this.eventType = eventType;
    }

    public Builder name(final String name) {
      this.name = name;
      return this;
    }

    public Builder namespace(final String namespace) {
      this.namespace = namespace;
      return this;
    }

    public Builder allowList(final List<String> allowList) {
      this.allowList = allowList;
      return this;
    }

    public Builder updatedAllowList(final List<String> updatedAllowList) {
      this.updatedAllowList = updatedAllowList;
      return this;
    }

    public Builder blockList(final List<String> blockList) {
      this.blockList = blockList;
      return this;
    }

    public Builder updatedBlockList(final List<String> updatedBlockList) {
      this.updatedBlockList = updatedBlockList;
      return this;
    }

    public Builder rolloutPercentage(final Integer rolloutPercentage) {
      this.rolloutPercentage = rolloutPercentage;
      return this;
    }

    public Builder updatedRolloutPercentage(final Integer updatedRolloutPercentage) {
      this.updatedRolloutPercentage = updatedRolloutPercentage;
      return this;
    }

    public Builder emergencyPhase(final String emergencyPhase) {
      this.emergencyPhase = emergencyPhase;
      return this;
    }

    public Builder updatedEmergencyPhase(final String updatedEmergencyPhase) {
      this.updatedEmergencyPhase = updatedEmergencyPhase;
      return this;
    }

    public ConfigFeatureFlagAudit build() {
      return new ConfigFeatureFlagAudit(this);
    }
  }
}
