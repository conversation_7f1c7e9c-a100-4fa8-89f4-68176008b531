{"networks": {"gwproxy.10gen.cc": {"providers": {"aws": {"regions": {"eu-west-1": {"gateway_cidrs": ["63.32.220.59/32", "34.253.255.89/32"]}, "us-east-1": {"gateway_cidrs": ["54.174.253.93/32", "34.227.70.30/32"]}}}, "azure": {"regions": {"australiaeast": {"gateway_cidrs": ["20.92.135.107/32", "20.211.141.43/32"]}, "brazilsouth": {"gateway_cidrs": ["20.201.67.112/32", "20.201.66.124/32"]}, "centralindia": {"gateway_cidrs": ["20.204.223.182/32", "20.207.110.214/32"]}, "centralus": {"gateway_cidrs": ["20.221.81.10/32", "20.221.82.144/32"]}, "eastasia": {"gateway_cidrs": ["20.239.1.170/32", "20.239.1.31/32"]}, "eastus2": {"gateway_cidrs": ["20.85.97.231/32", "20.85.97.69/32"]}, "northeurope": {"gateway_cidrs": ["20.223.8.196/32", "20.223.9.105/32"]}, "southeastasia": {"gateway_cidrs": ["20.212.42.244/32", "20.212.45.231/32"]}, "westeurope": {"gateway_cidrs": ["20.126.225.202/32", "20.126.226.49/32"]}, "westus": {"gateway_cidrs": ["13.88.41.84/32"]}}}, "gcp": {"regions": {"asia-south1": {"gateway_cidrs": ["34.93.68.205/32"]}, "europe-west1": {"gateway_cidrs": ["104.155.23.172/32"]}, "us-central1": {"gateway_cidrs": ["35.239.90.136/32"]}, "us-east4": {"gateway_cidrs": ["34.145.182.203/32"]}, "us-west1": {"gateway_cidrs": ["34.168.113.178/32"]}}}}}, "mongodb-stage.net": {"providers": {"aws": {"regions": {"af-south-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "brazilsouth"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}], "service_ports": {"https": 30810, "mongod": 30604, "mongos": 30605}}, "ap-east-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30811, "mongod": 30284, "mongos": 30285}}, "ap-northeast-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30812, "mongod": 30286, "mongos": 30287}}, "ap-northeast-2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30813, "mongod": 30288, "mongos": 30289}}, "ap-northeast-3": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30978, "mongod": 30979, "mongos": 30980}}, "ap-south-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30814, "mongod": 30290, "mongos": 30291}}, "ap-south-2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 31305, "mongod": 31306, "mongos": 31307}}, "ap-southeast-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "southeastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30815, "mongod": 30292, "mongos": 30293}}, "ap-southeast-2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "australiaeast"}], "service_ports": {"https": 30816, "mongod": 30294, "mongos": 30295}}, "ap-southeast-3": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "southeastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 31158, "mongod": 31159, "mongos": 31160}}, "ap-southeast-4": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "australiaeast"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 31308, "mongod": 31309, "mongos": 31310}}, "ap-southeast-5": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "southeastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 31503, "mongod": 31504, "mongos": 31505}}, "ap-southeast-7": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "southeastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 31554, "mongod": 31555, "mongos": 31556}}, "ca-central-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30817, "mongod": 30296, "mongos": 30297}}, "ca-west-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "west<PERSON>"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-west1"}], "service_ports": {"https": 31401, "mongod": 31402, "mongos": 31403}}, "cn-north-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30818, "mongod": 30640, "mongos": 30641}}, "cn-northwest-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30819, "mongod": 30642, "mongos": 30643}}, "eu-central-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30820, "mongod": 30298, "mongos": 30299}}, "eu-central-2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31311, "mongod": 31312, "mongos": 31313}}, "eu-north-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30821, "mongod": 30300, "mongos": 30301}}, "eu-south-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30822, "mongod": 30606, "mongos": 30607}}, "eu-south-2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31314, "mongod": 31315, "mongos": 31316}}, "eu-west-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "northeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30823, "mongod": 30302, "mongos": 30303}}, "eu-west-2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30824, "mongod": 30304, "mongos": 30305}}, "eu-west-3": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}], "service_ports": {"https": 30825, "mongod": 30306, "mongos": 30307}}, "il-central-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31377, "mongod": 31378, "mongos": 31379}}, "me-central-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 31317, "mongod": 31318, "mongos": 31319}}, "me-south-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30826, "mongod": 30308, "mongos": 30309}}, "mx-central-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralus"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-central1"}], "service_ports": {"https": 31557, "mongod": 31558, "mongos": 31559}}, "sa-east-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "brazilsouth"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30827, "mongod": 30310, "mongos": 30311}}, "us-east-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30828, "mongod": 30312, "mongos": 30313}}, "us-east-2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30829, "mongod": 30314, "mongos": 30315}}, "us-west-1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "west<PERSON>"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-west1"}], "service_ports": {"https": 30830, "mongod": 30316, "mongos": 30317}}, "us-west-2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "west<PERSON>"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-west1"}], "service_ports": {"https": 30831, "mongod": 30318, "mongos": 30319}}}}, "azure": {"regions": {"australiacentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "australiaeast"}], "service_ports": {"https": 31044, "mongod": 31045, "mongos": 31046}}, "australiacentral2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "australiaeast"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}], "service_ports": {"https": 31047, "mongod": 31048, "mongos": 31049}}, "australiaeast": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "australiaeast"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30832, "mongod": 30320, "mongos": 30321}}, "australiasoutheast": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "australiaeast"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30833, "mongod": 30322, "mongos": 30323}}, "brazilsouth": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "brazilsouth"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30834, "mongod": 30324, "mongos": 30325}}, "brazilsoutheast": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "brazilsouth"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 31050, "mongod": 31051, "mongos": 31052}}, "canadacentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30835, "mongod": 30326, "mongos": 30327}}, "canadaeast": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}], "service_ports": {"https": 30836, "mongod": 30328, "mongos": 30329}}, "centralindia": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30837, "mongod": 30330, "mongos": 30331}}, "centralus": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralus"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-central1"}], "service_ports": {"https": 30838, "mongod": 30332, "mongos": 30333}}, "centraluseuap": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralus"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-central1"}], "service_ports": {"https": 31461, "mongod": 31462, "mongos": 31463}}, "eastasia": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30839, "mongod": 30334, "mongos": 30335}}, "eastus": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30840, "mongod": 30336, "mongos": 30337}}, "eastus2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30841, "mongod": 30338, "mongos": 30339}}, "eastus2euap": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 31473, "mongod": 31474, "mongos": 31475}}, "francecentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30842, "mongod": 30340, "mongos": 30341}}, "francesouth": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31053, "mongod": 31054, "mongos": 31055}}, "germanynorth": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30843, "mongod": 30608, "mongos": 30609}}, "germanywestcentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30844, "mongod": 30342, "mongos": 30343}}, "israelcentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31434, "mongod": 31435, "mongos": 31436}}, "italynorth": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}], "service_ports": {"https": 31437, "mongod": 31438, "mongos": 31439}}, "japaneast": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30845, "mongod": 30344, "mongos": 30345}}, "japanwest": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30846, "mongod": 30346, "mongos": 30347}}, "jioindiacentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}], "service_ports": {"https": 31356, "mongod": 31357, "mongos": 31358}}, "jioindiawest": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 31359, "mongod": 31360, "mongos": 31361}}, "koreacentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30847, "mongod": 30348, "mongos": 30349}}, "koreasouth": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30848, "mongod": 30350, "mongos": 30351}}, "mexicocentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-central1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralus"}], "service_ports": {"https": 31485, "mongod": 31486, "mongos": 31487}}, "newzealandnorth": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "australiaeast"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-west1"}], "service_ports": {"https": 31545, "mongod": 31546, "mongos": 31547}}, "northcentralus": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralus"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-central1"}], "service_ports": {"https": 30849, "mongod": 30352, "mongos": 30353}}, "northeurope": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "northeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30850, "mongod": 30354, "mongos": 30355}}, "norwayeast": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30851, "mongod": 30356, "mongos": 30357}}, "norwaywest": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31056, "mongod": 31057, "mongos": 31058}}, "polandcentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}], "service_ports": {"https": 31362, "mongod": 31363, "mongos": 31364}}, "qatarcentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}], "service_ports": {"https": 31365, "mongod": 31366, "mongos": 31367}}, "southafricanorth": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30852, "mongod": 30358, "mongos": 30359}}, "southafricawest": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "brazilsouth"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 31059, "mongod": 31060, "mongos": 31061}}, "southcentralus": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralus"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-central1"}], "service_ports": {"https": 30853, "mongod": 30360, "mongos": 30361}}, "southeastasia": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "southeastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30854, "mongod": 30362, "mongos": 30363}}, "southindia": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30855, "mongod": 30364, "mongos": 30365}}, "spaincentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "northeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31497, "mongod": 31498, "mongos": 31499}}, "swedencentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}], "service_ports": {"https": 31062, "mongod": 31063, "mongos": 31064}}, "swedensouth": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31065, "mongod": 31066, "mongos": 31067}}, "switzerlandnorth": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30856, "mongod": 30366, "mongos": 30367}}, "switzerlandwest": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30857, "mongod": 30368, "mongos": 30369}}, "uaecentral": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30858, "mongod": 30370, "mongos": 30371}}, "uaenorth": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30859, "mongod": 30372, "mongos": 30373}}, "uksouth": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30860, "mongod": 30374, "mongos": 30375}}, "ukwest": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "northeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30861, "mongod": 30376, "mongos": 30377}}, "westcentralus": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralus"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-central1"}], "service_ports": {"https": 30862, "mongod": 30610, "mongos": 30611}}, "westeurope": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30863, "mongod": 30378, "mongos": 30379}}, "westindia": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30864, "mongod": 30380, "mongos": 30381}}, "westus": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-west1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "west<PERSON>"}], "service_ports": {"https": 30865, "mongod": 30382, "mongos": 30383}}, "westus2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-west1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "west<PERSON>"}], "service_ports": {"https": 30866, "mongod": 30384, "mongos": 30385}}, "westus3": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "west<PERSON>"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-west1"}], "service_ports": {"https": 31068, "mongod": 31069, "mongos": 31070}}}}, "gcp": {"regions": {"africa-south1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31449, "mongod": 31450, "mongos": 31451}}, "asia-east1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30867, "mongod": 30386, "mongos": 30387}}, "asia-east2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30868, "mongod": 30388, "mongos": 30389}}, "asia-northeast1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}], "service_ports": {"https": 30869, "mongod": 30390, "mongos": 30391}}, "asia-northeast2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}], "service_ports": {"https": 30870, "mongod": 30392, "mongos": 30393}}, "asia-northeast3": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30871, "mongod": 30572, "mongos": 30573}}, "asia-south1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30872, "mongod": 30394, "mongos": 30395}}, "asia-south2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 31008, "mongod": 31009, "mongos": 31010}}, "asia-southeast1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "southeastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30873, "mongod": 30396, "mongos": 30397}}, "asia-southeast2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "southeastasia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 30874, "mongod": 30612, "mongos": 30613}}, "australia-southeast1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "australiaeast"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}], "service_ports": {"https": 30875, "mongod": 30398, "mongos": 30399}}, "australia-southeast2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "australiaeast"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 31011, "mongod": 31012, "mongos": 31013}}, "europe-central2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30990, "mongod": 30991, "mongos": 30992}}, "europe-north1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}], "service_ports": {"https": 30876, "mongod": 30400, "mongos": 30401}}, "europe-southwest1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "northeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31164, "mongod": 31165, "mongos": 31166}}, "europe-west1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30877, "mongod": 30402, "mongos": 30403}}, "europe-west10": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31389, "mongod": 31390, "mongos": 31391}}, "europe-west12": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31245, "mongod": 31246, "mongos": 31247}}, "europe-west2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30878, "mongod": 30404, "mongos": 30405}}, "europe-west3": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}], "service_ports": {"https": 30879, "mongod": 30406, "mongos": 30407}}, "europe-west4": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30880, "mongod": 30408, "mongos": 30409}}, "europe-west6": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 30881, "mongod": 30410, "mongos": 30411}}, "europe-west8": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}], "service_ports": {"https": 31188, "mongod": 31189, "mongos": 31190}}, "europe-west9": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31191, "mongod": 31192, "mongos": 31193}}, "me-central1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}], "service_ports": {"https": 31248, "mongod": 31249, "mongos": 31250}}, "me-central2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralindia"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "asia-south1"}], "service_ports": {"https": 31404, "mongod": 31405, "mongos": 31406}}, "me-west1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "eu-west-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "westeurope"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "europe-west1"}], "service_ports": {"https": 31251, "mongod": 31252, "mongos": 31253}}, "northamerica-northeast1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30882, "mongod": 30412, "mongos": 30413}}, "northamerica-northeast2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 31026, "mongod": 31027, "mongos": 31028}}, "northamerica-south1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralus"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-central1"}], "service_ports": {"https": 31518, "mongod": 31519, "mongos": 31520}}, "southamerica-east1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "brazilsouth"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30883, "mongod": 30414, "mongos": 30415}}, "southamerica-west1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "brazilsouth"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 31032, "mongod": 31033, "mongos": 31034}}, "us-central1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralus"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-central1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}], "service_ports": {"https": 30884, "mongod": 30416, "mongos": 30417}}, "us-east1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30885, "mongod": 30418, "mongos": 30419}}, "us-east4": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 30886, "mongod": 30420, "mongos": 30421}}, "us-east5": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "eastus2"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-east4"}], "service_ports": {"https": 31254, "mongod": 31255, "mongos": 31256}}, "us-south1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "centralus"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-central1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}], "service_ports": {"https": 31257, "mongod": 31258, "mongos": 31259}}, "us-west1": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "west<PERSON>"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-west1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}], "service_ports": {"https": 30887, "mongod": 30422, "mongos": 30423}}, "us-west2": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "west<PERSON>"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-west1"}], "service_ports": {"https": 30888, "mongod": 30424, "mongos": 30425}}, "us-west3": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}, {"name": "gwproxy.10gen.cc", "provider": "azure", "region": "west<PERSON>"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-west1"}], "service_ports": {"https": 30889, "mongod": 30614, "mongos": 30615}}, "us-west4": {"allowed_gateways": [{"name": "gwproxy.10gen.cc", "provider": "azure", "region": "west<PERSON>"}, {"name": "gwproxy.10gen.cc", "provider": "gcp", "region": "us-west1"}, {"name": "gwproxy.10gen.cc", "provider": "aws", "region": "us-east-1"}], "service_ports": {"https": 30890, "mongod": 30616, "mongos": 30617}}}}}}}}