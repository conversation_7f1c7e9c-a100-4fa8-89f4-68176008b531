package com.xgen.cloud.services.websocket._private.svc;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableList;
import com.mongodb.BasicDBObject;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCommandException;
import com.mongodb.MongoCredential;
import com.mongodb.MongoSocketException;
import com.mongodb.MongoTimeoutException;
import com.mongodb.ReadPreference;
import com.mongodb.ServerAddress;
import com.mongodb.client.ListCollectionsIterable;
import com.mongodb.client.MongoDatabase;
import com.mongodb.connection.ClusterConnectionMode;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.access.rolecheck._public.svc.RoleSetSvc;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.auditInfo._public.model.EventSource;
import com.xgen.cloud.common.constants._public.model.user.UserApiKeyType;
import com.xgen.cloud.common.constants._public.model.user.UserType;
import com.xgen.cloud.common.driverwrappers._public.legacy.MongoClient;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.security._public.util.TLSUtil;
import com.xgen.cloud.common.security._public.util.TLSUtil.PEMKeyFile;
import com.xgen.cloud.event._public.client.EventClientWrapper;
import com.xgen.cloud.event._public.model.CreateEventRequestDto;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.cloudprovider._public.model.InstanceSize;
import com.xgen.cloud.nds.cloudprovider._public.model.NodeType;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.project._public.model.Cluster;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.nds.project._public.model.NDSGroup;
import com.xgen.cloud.nds.project._public.model.dbusers.INDSDBRole;
import com.xgen.cloud.nds.project._public.model.dbusers.NDSDBRole;
import com.xgen.cloud.nds.project._public.model.gateway.ClusterHostAddresses;
import com.xgen.cloud.nds.project._public.model.gateway.HostAddresses;
import com.xgen.cloud.nds.project._public.model.gateway.PortType;
import com.xgen.cloud.nds.project._public.util.NDSClusterConnectionUtil;
import com.xgen.cloud.nds.project._public.util.NDSClusterConnectionUtil.ClusterHostAddressReader;
import com.xgen.cloud.nds.project._public.util.NDSX509Util;
import com.xgen.cloud.nds.project._public.view.AggregatedViewInfoView;
import com.xgen.cloud.nds.project._public.view.NamespaceWithUUIDView;
import com.xgen.cloud.nds.project._public.view.SearchAggregationRequestView;
import com.xgen.cloud.nds.project._public.view.SearchAggregationResponseView;
import com.xgen.cloud.services.core.base._public.config.Environment;
import com.xgen.cloud.services.core.base._public.config.ServiceSettings;
import com.xgen.cloud.services.event.proto.SourceMessage;
import com.xgen.cloud.services.event.proto.UserMessage;
import com.xgen.cloud.services.event.proto.Visibility;
import com.xgen.cloud.services.websocket._private.dao.ClusterDescriptionDao;
import com.xgen.cloud.services.websocket._private.dao.NDSGroupDao;
import com.xgen.cloud.services.websocket._private.dao.ReplicaSetHardwareDao;
import com.xgen.cloud.services.websocket._private.util.GatewayProxyUtil;
import com.xgen.cloud.services.websocket._private.util.MetricsUtil;
import com.xgen.cloud.services.websocket._private.util.MongoCommand;
import com.xgen.cloud.services.websocket._public.view.ClusterConnectionEvent;
import com.xgen.cloud.services.websocket._public.view.MessageResponse;
import com.xgen.cloud.services.websocket._public.view.MongoCmdMessage;
import com.xgen.cloud.services.websocket._public.view.MongoCmdMessage.Command;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.websocket._public.exceptions.WebSocketErrorCode;
import com.xgen.cloud.websocket._public.exceptions.WebSocketException;
import io.prometheus.client.Histogram.Timer;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import javax.net.ssl.SSLContext;
import org.bson.BsonDocument;
import org.bson.Document;
import org.bson.UuidRepresentation;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class ClusterConnectionMongoCommandSvc {
  private static final Logger LOG = LoggerFactory.getLogger(ClusterConnectionMongoCommandSvc.class);
  private static final int DEFAULT_SAMPLE_SIZE = 100;
  private static final int DEFAULT_RETURN_SIZE = 100;
  private static final Set<String> SYSTEM_DB_NAMES = Set.of("admin", "config", "local");
  private static final String SYSTEM_COLLECTION_PREFIX = "system.";

  private static final Set<Command> AUDIT_HIDDEN_LIST = Set.of(Command.HAS_NAMESPACES);

  public static final Map<MongoCmdMessage.Command, String> COMMAND_TO_METHOD_MAPPINGS =
      Map.of(
          Command.LIST_DATABASES,
          "listDatabaseNames",
          Command.LIST_NAMESPACES,
          "listNamespaces",
          Command.LIST_NAMESPACES_WITH_UUID,
          "listNamespacesWithUUID",
          Command.LIST_AGGREGATED_VIEW_INFOS,
          "listAggregatedViewInfos",
          Command.HAS_NAMESPACES,
          "hasNamespaces",
          Command.AGGREGATE,
          "runSearchAggregation",
          Command.SAMPLE_COLLECTION_FIELD_NAMES,
          "sampleCollectionFieldNames");
  public static final String X509_CERT_VALID_MINUTES_KEY = "x509CertValidMinutes";

  private final ServiceSettings _settings;
  private final NDSGroupDao _ndsGroupDao;
  private final ClusterDescriptionDao _clusterDescriptionDao;
  private final ReplicaSetHardwareDao _replicaSetHardwareDao;
  private final RoleSetSvc _roleSetSvc;
  private final ClusterHostAddressReader _clusterHostAddressReader;
  private final ExecutorService _closeMongoClientsThreadPool;
  private final Map<ObjectId, Map<String, MongoClient>> _cachedMongoClients =
      new ConcurrentHashMap<>();
  private final Map<String, ObjectId> _clusterUniqueIdsMap = new ConcurrentHashMap<>();
  private final Map<String, String[]> _clusterMetricsLabelValues = new ConcurrentHashMap<>();

  private final EventClientWrapper _eventClientWrapper;

  @Inject
  public ClusterConnectionMongoCommandSvc(
      final ServiceSettings pSettings,
      final NDSGroupDao pGroupDao,
      final ClusterDescriptionDao pClusterDescriptionDao,
      final ReplicaSetHardwareDao pReplicaSetHardwareDao,
      final EventClientWrapper pEventClientWrapper,
      final RoleSetSvc pRoleSetSvc,
      final GatewayProxyUtil pGatewayProxyUtil) {
    _settings = pSettings;
    _ndsGroupDao = pGroupDao;
    _clusterDescriptionDao = pClusterDescriptionDao;
    _replicaSetHardwareDao = pReplicaSetHardwareDao;
    _roleSetSvc = pRoleSetSvc;
    _clusterHostAddressReader = new ClusterHostAddressReader(pGatewayProxyUtil::getPort);
    _closeMongoClientsThreadPool = Executors.newFixedThreadPool(8);
    _eventClientWrapper = pEventClientWrapper;
  }

  public MessageResponse runCommand(
      final AppUser pUser,
      final Group pGroup,
      final MongoCmdMessage pMongoCmdMessage,
      final AuditInfo pAuditInfo)
      throws SvcException {
    final ClusterConnectionEvent.Builder auditBuilder;
    try {
      auditBuilder = pMongoCmdMessage.validate();
    } catch (SvcException pE) {
      return new MessageResponse(400, pMongoCmdMessage.getUuid(), pE);
    }
    final String methodToCall = COMMAND_TO_METHOD_MAPPINGS.get(pMongoCmdMessage.getCommand());
    if (methodToCall == null) {
      throw new WebSocketException(
          WebSocketErrorCode.INVALID_COMMAND, pMongoCmdMessage.getCommand());
    }
    MetricsUtil.MONGO_COMMAND_REQUEST_COUNTER.inc();
    final INDSDBRole dbRole = NDSDBRole.factoryCreate("admin", "readAnyDatabase");
    final MongoClient client =
        getMongoClient(
            pGroup.getId(), pMongoCmdMessage.getClusterName(), pUser.getUsername(), dbRole);
    auditBuilder.clusterUniqueId(
        _clusterUniqueIdsMap.get(
            getClusterUniqueIdKey(pGroup.getId(), pMongoCmdMessage.getClusterName(), dbRole)));
    auditBuilder.clusterName(pMongoCmdMessage.getClusterName());

    Visibility visibility =
        AUDIT_HIDDEN_LIST.contains(pMongoCmdMessage.getCommand())
            ? Visibility.VISIBILITY_ADMIN
            : Visibility.VISIBILITY_ALL;

    final CreateEventRequestDto createEventRequest =
        CreateEventRequestDto.builder()
            .eventPayload(auditBuilder.build())
            .source(createSource(pAuditInfo, pUser))
            .projectId(pGroup.getId())
            .visibility(visibility)
            .build();

    try {
      _eventClientWrapper.createEvent(createEventRequest);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }

    final String clusterUniqueIdKey =
        getClusterUniqueIdKey(pGroup.getId(), pMongoCmdMessage.getClusterName(), dbRole);
    final String[] clusterLabelValues =
        _clusterMetricsLabelValues.getOrDefault(clusterUniqueIdKey, new String[] {"", ""});
    final Timer commandTimer =
        startDurationTimer(
            MetricsUtil.getLabelValuesForMetrics(
                pMongoCmdMessage.getCommand().name(), clusterLabelValues));
    try {
      final Method commandMethod =
          ClusterConnectionMongoCommandSvc.class.getDeclaredMethod(
              methodToCall, MongoClient.class, MongoCmdMessage.class);
      final MongoCommand commandAnnotation = commandMethod.getAnnotation(MongoCommand.class);
      if (commandAnnotation == null) {
        throw new SvcException(CommonErrorCode.FORBIDDEN, "cluster data");
      }
      final boolean authorized =
          Arrays.stream(commandAnnotation.roles())
              .anyMatch(
                  roleSet -> _roleSetSvc.doRoleCheck(roleSet, pUser, pGroup.getOrgId(), pGroup));
      if (!authorized) {
        throw new SvcException(CommonErrorCode.FORBIDDEN, "cluster data");
      }
      final MongoCmdMessage messageWithGroupId =
          new MongoCmdMessage(pMongoCmdMessage, pGroup.getId());
      return (MessageResponse) commandMethod.invoke(this, client, messageWithGroupId);
    } catch (NoSuchMethodException pE) {
      LOG.error("No command method for message {}", pMongoCmdMessage, pE);
      throw new WebSocketException(
          WebSocketErrorCode.INVALID_COMMAND, pMongoCmdMessage.getCommand());
    } catch (IllegalAccessException | InvocationTargetException pE) {
      final Class<?> actualExceptionClazz = pE.getCause().getClass();
      final String possibleMongotError;
      if (MongoCommandException.class.equals(actualExceptionClazz)
          && !(possibleMongotError =
                  NDSClusterConnectionUtil.extractMongotError(
                      (MongoCommandException) pE.getCause()))
              .isEmpty()) {
        MetricsUtil.MONGOT_EXCEPTION_COUNTER.labels(clusterLabelValues).inc();
        return new MessageResponse(
            400,
            pMongoCmdMessage.getUuid(),
            new SvcException(CommonErrorCode.OPERATION_ERROR, possibleMongotError));
      }

      LOG.error(
          "Fail to run command in group {} with message {}", pGroup.getId(), pMongoCmdMessage, pE);
      removeMongoClientFromCache(pGroup.getId(), pMongoCmdMessage.getClusterName(), dbRole);
      try {
        client.close();
      } catch (Exception pCloseException) {
        LOG.error("Fail to close mongo client", pCloseException);
      }
      MetricsUtil.MONGO_COMMAND_EXCEPTION_COUNTER.labels(clusterLabelValues).inc();

      final SvcException exception;
      if (MongoTimeoutException.class.equals(actualExceptionClazz)) {
        exception = new SvcException(CommonErrorCode.TIMEOUT, pE);
      } else if (MongoSocketException.class.equals(actualExceptionClazz)
          || MongoCommandException.class.equals(actualExceptionClazz)) {
        exception = new SvcException(CommonErrorCode.OPERATION_ERROR, pE);
      } else {
        exception =
            new WebSocketException(WebSocketErrorCode.COMMAND_ERROR, pMongoCmdMessage.getCommand());
      }
      return new MessageResponse(pMongoCmdMessage.getUuid(), exception);
    } finally {
      PromMetricsSvc.recordTimer(commandTimer);
    }
  }

  @MongoCommand(roles = RoleSet.GROUP_DATA_ACCESS_ANY)
  private MessageResponse listDatabaseNames(
      final MongoClient pClient, final MongoCmdMessage pMongoCmdMessage) {
    return new MessageResponse(
        pMongoCmdMessage.getUuid(), ImmutableList.copyOf(pClient.listDatabaseNames()));
  }

  @MongoCommand(roles = RoleSet.GROUP_DATA_ACCESS_ANY)
  private MessageResponse listNamespaces(
      final MongoClient pClient, final MongoCmdMessage pMongoCmdMessage) {
    final List<String> namespaces = new ArrayList<>();
    for (final String databaseName : pClient.listDatabaseNames()) {
      if (isSystemDatabase(databaseName)) {
        continue;
      }

      final MongoDatabase database = pClient.getDatabase(databaseName);
      for (final String collectionName : database.listCollectionNames()) {
        if (isSystemCollection(collectionName)) {
          continue;
        }
        final String namespace = String.format("%s.%s", databaseName, collectionName);
        namespaces.add(namespace);
      }
    }
    return new MessageResponse(pMongoCmdMessage.getUuid(), namespaces);
  }

  @MongoCommand(roles = RoleSet.GROUP_DATA_ACCESS_ANY)
  private MessageResponse listNamespacesWithUUID(
      final MongoClient pClient, final MongoCmdMessage pMongoCmdMessage) {
    final List<NamespaceWithUUIDView> namespacesWithUUID = new ArrayList<>();
    for (final String databaseName : pClient.listDatabaseNames()) {
      if (isSystemDatabase(databaseName)) {
        continue;
      }

      final MongoDatabase database = pClient.getDatabase(databaseName);
      for (final Document collection : database.listCollections()) {
        final String collectionName = collection.getString("name");
        if (isSystemCollection(collectionName)) {
          continue;
        }
        final String namespace = String.format("%s.%s", database.getName(), collectionName);

        String uuid = "";
        if ((!collection.containsKey("info")
            || !((Document) collection.get("info")).containsKey("uuid"))) {
          if (collection.getString("type").equals("collection")) {
            LOG.warn(
                "UUID not defined for namespace={}, groupId={}, clusterName={}",
                namespace,
                pMongoCmdMessage.getGroupId(),
                pMongoCmdMessage.getClusterName());
          }
        } else {
          uuid = ((Document) collection.get("info")).get("uuid").toString();
        }

        namespacesWithUUID.add(new NamespaceWithUUIDView(namespace, uuid));
      }
    }
    return new MessageResponse(pMongoCmdMessage.getUuid(), namespacesWithUUID);
  }

  @MongoCommand(roles = RoleSet.GROUP_DATA_ACCESS_ANY)
  private MessageResponse listAggregatedViewInfos(
      final MongoClient pClient, final MongoCmdMessage pMongoCmdMessage) {
    final List<AggregatedViewInfoView> listAggregatedViewInfo = new ArrayList<>();

    for (final String databaseName : pClient.listDatabaseNames()) {
      if (isSystemDatabase(databaseName)) {
        continue;
      }

      final MongoDatabase database = pClient.getDatabase(databaseName);
      final ListCollectionsIterable<Document> collections = database.listCollections();
      for (final Document collection : collections) {
        final String collectionName = collection.getString("name");
        if (isSystemCollection(collectionName)) {
          continue;
        }

        if (collection.getString("type").equals("view")) { // if collection is a view
          if (collection.containsKey("options")
              && ((Document) collection.get("options")).containsKey("viewOn")
              && ((Document) collection.get("options")).containsKey("pipeline")) {
            final String viewOn = ((Document) collection.get("options")).getString("viewOn");
            AggregatedViewInfoView viewInformation =
                getViewInformation(
                    collections, collection, database.getName(), collectionName, viewOn);
            listAggregatedViewInfo.add(viewInformation);
          }
        }
      }
    }

    return new MessageResponse(pMongoCmdMessage.getUuid(), listAggregatedViewInfo);
  }

  private AggregatedViewInfoView getViewInformation(
      ListCollectionsIterable<Document> collections,
      Document view,
      String database,
      String name,
      String viewOn) {
    List<Document> effectivePipeline = new ArrayList<>();
    String rootCollectionUUID = "";
    boolean searchQueryable = true;
    while (true) {
      if (view.getString("type").equals("collection")) { // reached root collection
        final String rootCollectionName = view.getString("name");
        if (view.containsKey("info") && ((Document) view.get("info")).containsKey("uuid")) {
          rootCollectionUUID = ((Document) view.get("info")).get("uuid").toString();
        }

        List<BsonDocument> bsonPipeline =
            effectivePipeline.stream().map(Document::toBsonDocument).toList();
        return new AggregatedViewInfoView(
            database,
            name,
            viewOn,
            rootCollectionName,
            rootCollectionUUID,
            bsonPipeline.toString(),
            searchQueryable);
      }
      Document currentViewOptions = view.get("options", Document.class);
      List<Document> pipeline = currentViewOptions.getList("pipeline", Document.class);
      if (searchQueryable && !isPipelineSearchQueryable(pipeline)) {
        searchQueryable = false;
      }
      effectivePipeline.addAll(0, pipeline);

      boolean parentFound = false;
      for (Document collection : collections) {
        if (collection.getString("name").equals(currentViewOptions.getString("viewOn"))) {
          view = collection;
          parentFound = true;
          break;
        }
      }

      if (!parentFound) {
        return new AggregatedViewInfoView(
            database, name, viewOn, "", rootCollectionUUID, "", false);
      }
    }
  }

  private boolean isPipelineSearchQueryable(List<Document> pipeline) {
    for (Document stage : pipeline) { // loop through all stages
      String stageKey = stage.keySet().iterator().next(); // get stage
      if (!(stageKey.equals("$addFields")
          || stageKey.equals("$set")
          || stageKey.equals("$match"))) {
        return false; // not search queryable if stage is not $addFields, $set, or "$match")
      }

      if (stageKey.equals("$match")) {
        Document matchStage = stage.get("$match", Document.class);
        Set<String> allKeys = matchStage.keySet();
        if (!(allKeys.size() == 1
            && matchStage.containsKey("$expr"))) { // stage is not $match w/ $expr
          return false; // not search queryable if stage is $match without $expr
        }
      }
    }
    return true;
  }

  @MongoCommand(roles = RoleSet.GROUP_DATA_ACCESS_ANY)
  private MessageResponse hasNamespaces(
      final MongoClient pClient, final MongoCmdMessage pMongoCmdMessage) {
    for (final String databaseName : pClient.listDatabaseNames()) {
      if (isSystemDatabase(databaseName)) {
        continue;
      }
      final MongoDatabase database = pClient.getDatabase(databaseName);
      for (final String collectionName : database.listCollectionNames()) {
        if (!isSystemCollection(collectionName)) {
          return new MessageResponse(pMongoCmdMessage.getUuid(), true);
        }
      }
    }
    return new MessageResponse(pMongoCmdMessage.getUuid(), false);
  }

  @VisibleForTesting
  public static boolean isSystemDatabase(final String pDatabaseName) {
    return SYSTEM_DB_NAMES.contains(pDatabaseName);
  }

  @VisibleForTesting
  public static boolean isSystemCollection(final String pCollectionName) {
    return pCollectionName.startsWith(SYSTEM_COLLECTION_PREFIX);
  }

  @MongoCommand(roles = RoleSet.GROUP_DATA_ACCESS_ANY)
  private MessageResponse runSearchAggregation(
      final MongoClient pClient, final MongoCmdMessage pMongoCmdMessage)
      throws ExecutionException, InterruptedException {
    final String databaseName = pMongoCmdMessage.getArgs().getDatabaseName();
    final String collectionName = pMongoCmdMessage.getArgs().getCollectionName();
    final SearchAggregationRequestView requestView =
        pMongoCmdMessage.getArgs().getSearchAggregationRequestView();
    final boolean hasOnlyLimitStage = requestView.getPipeline().size() == 1;

    final CompletableFuture<Optional<Double>> executionTimeFuture =
        hasOnlyLimitStage
            ? null
            : NDSClusterConnectionUtil.getSearchAggregationExecutionTimeSeconds(
                pClient, databaseName, requestView.getExplainCmd(collectionName));
    final List<String> documents =
        NDSClusterConnectionUtil.runSearchAggregation(
            pClient, databaseName, collectionName, requestView.getPipeline());
    final Optional<Double> executionTime =
        hasOnlyLimitStage ? Optional.empty() : executionTimeFuture.get();

    return new MessageResponse(
        pMongoCmdMessage.getUuid(),
        new SearchAggregationResponseView(documents, executionTime.orElse(null)));
  }

  @MongoCommand(roles = RoleSet.GROUP_DATA_ACCESS_ANY)
  private MessageResponse sampleCollectionFieldNames(
      final MongoClient pClient, final MongoCmdMessage pMongoCmdMessage) {
    final String databaseName = pMongoCmdMessage.getArgs().getDatabaseName();
    final String collectionName = pMongoCmdMessage.getArgs().getCollectionName();
    return new MessageResponse(
        pMongoCmdMessage.getUuid(),
        NDSClusterConnectionUtil.sampleCollectionFieldNames(
            pClient, databaseName, collectionName, DEFAULT_SAMPLE_SIZE, DEFAULT_RETURN_SIZE));
  }

  protected MongoClient getMongoClient(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pUsername,
      final INDSDBRole pDbRole)
      throws SvcException {
    final MongoClient cachedClient = getMongoClientFromCache(pGroupId, pClusterName, pDbRole);
    if (cachedClient != null) {
      MetricsUtil.REUSE_CACHED_MONGO_CLIENT_COUNTER.inc();
      return cachedClient;
    }
    final MongoClient client = createMongoClient(pGroupId, pClusterName, pUsername, pDbRole);

    final String clusterUniqueIdKey = getClusterUniqueIdKey(pGroupId, pClusterName, pDbRole);
    final String[] clusterLabelValues =
        _clusterMetricsLabelValues.getOrDefault(clusterUniqueIdKey, new String[] {"", ""});
    final Timer pingTimer =
        startDurationTimer(
            MetricsUtil.getLabelValuesForMetrics(
                MetricsUtil.Steps.PING_DATABASE, clusterLabelValues));
    try {
      client.getDatabase(pDbRole.getDatabase()).runCommand(new BasicDBObject("ping", 1));
    } catch (Exception pE) {
      client.close();
      MetricsUtil.MONGO_CONNECTION_EXCEPTION_COUNTER.labels(clusterLabelValues).inc();
      throw new SvcException(WebSocketErrorCode.MONGO_CONNECTION_FAILED, pE, pClusterName);
    } finally {
      PromMetricsSvc.recordTimer(pingTimer);
    }

    cacheMongoClient(pGroupId, pClusterName, pDbRole, client);
    final MongoClient clientFromCache = getMongoClientFromCache(pGroupId, pClusterName, pDbRole);
    if (client != clientFromCache) {
      client.close();
    }
    MetricsUtil.CREATE_NEW_MONGO_CLIENT_COUNTER.inc();
    return clientFromCache;
  }

  protected MongoClient createMongoClient(
      final ObjectId pGroupId,
      final String pClusterName,
      final String pUsername,
      final INDSDBRole pDbRole)
      throws SvcException {
    final NDSGroup group =
        _ndsGroupDao
            .findById(pGroupId)
            .orElseThrow(() -> new SvcException(NDSErrorCode.INVALID_GROUP_ID));
    final ClusterDescription clusterDescription =
        _clusterDescriptionDao
            .findById(pGroupId, pClusterName)
            .orElseThrow(() -> new SvcException(NDSErrorCode.CLUSTER_NOT_FOUND));
    final Cluster cluster =
        Cluster.getCluster(
            clusterDescription, _replicaSetHardwareDao.findByCluster(pGroupId, pClusterName));
    final String clusterUniqueIdKey = getClusterUniqueIdKey(pGroupId, pClusterName, pDbRole);
    _clusterUniqueIdsMap.put(clusterUniqueIdKey, clusterDescription.getUniqueId());
    final String instanceSize =
        clusterDescription
            .getMaxInstanceSize(NodeType.ELECTABLE)
            .map(InstanceSize::getPrintableName)
            .orElse("unknown");
    _clusterMetricsLabelValues.put(
        clusterUniqueIdKey,
        new String[] {clusterDescription.getHighestPriorityRegion().getName(), instanceSize});

    final List<String> dbHosts = getMongoDbHosts(group, cluster);
    final List<ServerAddress> serverAddresses =
        dbHosts.stream().map(ServerAddress::new).collect(Collectors.toList());

    final Timer timer =
        startDurationTimer(
            MetricsUtil.getLabelValuesForMetrics(
                MetricsUtil.Steps.GENERATE_X509_CERT,
                _clusterMetricsLabelValues.get(clusterUniqueIdKey)));
    try {
      final String dbUsername =
          NDSX509Util.getDNForManagedX509User(
              String.join("/", pUsername, pDbRole.getRoleNameWithDatabase()));
      final SSLContext sslContext = getX509SslContext(group, dbUsername, pDbRole);
      final MongoClientOptions clientOptions =
          MongoClientOptions.builder()
              .connectionsPerHost(1)
              .uuidRepresentation(UuidRepresentation.STANDARD)
              .connectTimeout(10000)
              .socketTimeout(60000)
              .serverSelectionTimeout(10000)
              .readPreference(ReadPreference.primaryPreferred())
              .sslEnabled(true)
              .sslContext(sslContext)
              .applicationName("atlas-cluster-connection-mongo-command-service")
              .build();
      final MongoCredential credential = MongoCredential.createMongoX509Credential(dbUsername);
      if (clusterDescription.isServerlessTenantCluster()) {
        final MongoClientSettings mongoClientSettings =
            clientOptions.asMongoClientSettings(
                serverAddresses, null, ClusterConnectionMode.LOAD_BALANCED, credential);
        return new MongoClient(new com.mongodb.MongoClient(mongoClientSettings));
      } else {
        return new MongoClient(serverAddresses, credential, clientOptions);
      }
    } finally {
      PromMetricsSvc.recordTimer(timer);
    }
  }

  protected List<String> getMongoDbHosts(final NDSGroup pGroup, final Cluster pCluster)
      throws SvcException {
    final ClusterDescription clusterDescription = pCluster.getClusterDescription();
    if (Environment.LOCAL == getEnvironment()) {
      return clusterDescription.isServerlessTenantCluster()
              || clusterDescription.isFlexWithServerlessNetworking()
          ? clusterDescription.getLoadBalancedHostname().map(List::of).orElseGet(List::of)
          : List.of(clusterDescription.getMongoDBUriHosts());
    }

    final ClusterHostAddresses clusterHostAddresses =
        _clusterHostAddressReader.getClusterHostAddresses(pGroup, pCluster);
    final List<String> meshHosts =
        (clusterHostAddresses.getPortTypeHostAddresses().containsKey(PortType.MONGOS)
                ? clusterHostAddresses.getHostAddresses(PortType.MONGOS)
                : clusterHostAddresses.getHostAddresses(PortType.MONGOD))
            .stream().map(HostAddresses::getMeshHostAddress).collect(Collectors.toList());
    LOG.info("meshHosts: {}", String.join(", ", meshHosts));
    if (meshHosts.isEmpty()) {
      // MESH host names have not yet been back-filled for this cluster
      throw new SvcException(
          NDSErrorCode.CLUSTER_MISSING_MESH_HOSTS,
          clusterDescription.getName(),
          pGroup.getGroupId());
    }
    return meshHosts;
  }

  protected SSLContext getX509SslContext(
      final NDSGroup pGroup, final String pDbUsername, final INDSDBRole pDbRole) {

    final Instant now = Instant.now();
    final Date notBeforeDate = Date.from(now.minus(Duration.ofMinutes(1)));
    final Date notAfterDate =
        Date.from(now.plus(Duration.ofMinutes(_settings.getInt(X509_CERT_VALID_MINUTES_KEY, 60))));

    final PEMKeyFile x509Cert =
        NDSX509Util.generateX509Cert(
            pGroup,
            pDbUsername,
            notBeforeDate,
            notAfterDate,
            NDSX509Util.getRoleExtensions(List.of(pDbRole)));
    return TLSUtil.getX509SslContext(x509Cert.getCAChain(), x509Cert.getKey(), null);
  }

  protected void closeMongoClientsForGroup(final ObjectId pGroupId) {
    final Map<String, MongoClient> clientsForGroup = _cachedMongoClients.remove(pGroupId);
    if (clientsForGroup != null) {
      LOG.info("Close {} mongo clients in group {}", clientsForGroup.size(), pGroupId);
      clientsForGroup.forEach(
          (clusterKey, mongoClient) ->
              _closeMongoClientsThreadPool.submit(
                  () -> {
                    try {
                      mongoClient.close();
                    } catch (Exception pCloseException) {
                      LOG.error(
                          "Fail to close mongo client for cluster {} in group {}",
                          pGroupId,
                          clusterKey,
                          pCloseException);
                    }
                  }));
    }
  }

  private MongoClient getMongoClientFromCache(
      final ObjectId pGroupId, final String pClusterName, final INDSDBRole pDBRole) {
    final Map<String, MongoClient> clientsForGroup = _cachedMongoClients.get(pGroupId);
    if (clientsForGroup == null) {
      return null;
    }
    return clientsForGroup.get(getCachedClusterKey(pClusterName, pDBRole));
  }

  private void cacheMongoClient(
      final ObjectId pGroupId,
      final String pClusterName,
      final INDSDBRole pDBRole,
      final MongoClient pClient) {
    _cachedMongoClients.compute(
        pGroupId,
        (groupId, cachedClients) -> {
          Map<String, MongoClient> clientsForGroup =
              (cachedClients == null) ? new ConcurrentHashMap<>() : cachedClients;
          clientsForGroup.compute(
              getCachedClusterKey(pClusterName, pDBRole),
              (clientKey, existingClient) -> {
                if (existingClient == null) {
                  return pClient;
                }
                return existingClient; // reuse the cached client if exists
              });
          return clientsForGroup;
        });
  }

  private void removeMongoClientFromCache(
      final ObjectId pGroupId, final String pClusterName, final INDSDBRole pDBRole) {
    Optional.ofNullable(_cachedMongoClients.get(pGroupId))
        .ifPresent(
            clientsForGroup -> clientsForGroup.remove(getCachedClusterKey(pClusterName, pDBRole)));
    final String clusterUniqueIdKey = getClusterUniqueIdKey(pGroupId, pClusterName, pDBRole);
    _clusterUniqueIdsMap.remove(clusterUniqueIdKey);
    _clusterMetricsLabelValues.remove(clusterUniqueIdKey);
  }

  private String getCachedClusterKey(final String pClusterName, final INDSDBRole pDBRole) {
    return String.format("%s-%s", pClusterName, pDBRole.getRoleNameWithDatabase());
  }

  private String getClusterUniqueIdKey(
      final ObjectId pGroupId, final String pClusterName, final INDSDBRole pDBRole) {
    return String.format("%s-%s", pGroupId, getCachedClusterKey(pClusterName, pDBRole));
  }

  private SourceMessage createSource(AuditInfo auditInfo, AppUser appUser) {
    SourceMessage.Builder sourceBuilder = SourceMessage.newBuilder();

    Optional.ofNullable(auditInfo)
        .map(AuditInfo::getEventSource)
        .map(EventSource::toProtoEventSource)
        .ifPresent(sourceBuilder::setSourceType);

    UserMessage.Builder userBuilder = UserMessage.newBuilder();

    Optional.ofNullable(appUser)
        .ifPresent(
            user -> {
              Optional.ofNullable(user.getId())
                  .map(Object::toString)
                  .ifPresent(userBuilder::setUserId);
              Optional.ofNullable(user.getUsername()).ifPresent(userBuilder::setUsername);
            });

    Optional.ofNullable(auditInfo)
        .ifPresent(
            info -> {
              userBuilder.setAdminUser(info.userIsMmsAdmin());
              Optional.ofNullable(info.getRemoteAddr()).ifPresent(userBuilder::setRemoteAddr);
              Optional.ofNullable(info.getUserType())
                  .map(UserType::toProtoUserType)
                  .ifPresent(userBuilder::setUserType);
              Optional.ofNullable(info.getUserApiKeyType())
                  .map(UserApiKeyType::toProtoUserApiKeyType)
                  .ifPresent(userBuilder::setUserApiKeyType);
            });

    return sourceBuilder.setUser(userBuilder.build()).build();
  }

  @VisibleForTesting
  protected Map<ObjectId, Map<String, MongoClient>> getCachedMongoClients() {
    return _cachedMongoClients;
  }

  @VisibleForTesting
  public void prepareCachedMongoClientsForTest(
      final ObjectId pGroupId,
      final String pClusterName,
      final INDSDBRole pDBRole,
      final MongoClient pClient) {
    if (Environment.TEST != getEnvironment()) {
      return;
    }
    cacheMongoClient(pGroupId, pClusterName, pDBRole, pClient);
  }

  @VisibleForTesting
  public void prepareClusterUniqueIdsMapForTest(
      final ObjectId pGroupId,
      final String pClusterName,
      final INDSDBRole pDBRole,
      final ObjectId pClusterUniqueId,
      final String[] pClusterLabelValues) {
    if (Environment.TEST != getEnvironment()) {
      return;
    }
    _clusterUniqueIdsMap.put(
        getClusterUniqueIdKey(pGroupId, pClusterName, pDBRole), pClusterUniqueId);
    _clusterMetricsLabelValues.put(
        getClusterUniqueIdKey(pGroupId, pClusterName, pDBRole), pClusterLabelValues);
  }

  @VisibleForTesting
  public Timer startDurationTimer(final String... pLabelValues) {
    return PromMetricsSvc.startTimer(
        MetricsUtil.CLUSTER_CONNECTION_REQUEST_DURATION_HISTOGRAM, pLabelValues);
  }

  @VisibleForTesting
  Environment getEnvironment() {
    return Environment.from(_settings).orElse(Environment.LOCAL);
  }
}
