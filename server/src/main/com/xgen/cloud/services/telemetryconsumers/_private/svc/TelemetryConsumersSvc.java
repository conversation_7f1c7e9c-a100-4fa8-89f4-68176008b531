package com.xgen.cloud.services.telemetryconsumers._private.svc;

import com.xgen.cloud.common.util._public.util.LogUtils;
import com.xgen.cloud.services.core.base._public.config.ServiceSettings;
import com.xgen.cloud.services.metrics._public.model.usecase.StreamApplication;
import com.xgen.cloud.services.metrics._public.model.usecase.TelemetryConsumerSetting;
import com.xgen.cloud.services.metrics._public.model.usecase.Usecase;
import com.xgen.cloud.services.telemetryconsumers._private.cache.MetricCache;
import com.xgen.cloud.services.telemetryconsumers._private.consumers.integrations.DataWarehouseConsumer;
import com.xgen.cloud.services.telemetryconsumers._private.consumers.integrations.DatadogConsumer;
import com.xgen.cloud.services.telemetryconsumers._private.consumers.kcl.KinesisConsumer;
import com.xgen.cloud.services.telemetryconsumers._private.metric.IntegrationLevel;
import com.xgen.cloud.services.telemetryconsumers._private.model.IntegrationType;
import com.xgen.cloud.services.telemetryconsumers._private.senders.datadog.DatadogSender;
import com.xgen.svc.mms.util.http.HttpUtils;
import com.xgen.svc.mms.util.http.HttpUtilsBuilder;
import jakarta.inject.Inject;
import java.time.Duration;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TelemetryConsumersSvc {

  private static final Logger LOG = LoggerFactory.getLogger(TelemetryConsumersSvc.class);

  private final List<KinesisConsumer> consumers = new LinkedList<>();

  // protected for testing
  protected ExecutorService threadPool;

  @Inject
  public TelemetryConsumersSvc(
      final ServiceSettings serviceSettings,
      final MetricCache metricCache,
      final DatadogSender datadogSender) {
    Runtime.getRuntime().addShutdownHook(new Thread(this::shutdown));
    final List<Usecase> usecasePayload = fetchUsecases(serviceSettings);
    if (usecasePayload.isEmpty()) {
      LOG.error("Unable to fetch usecases, telemetry consumers will not be started");
      return;
    }
    LOG.info(
        "Initializing consumers with usecases {}", LogUtils.entries("usecases", usecasePayload));
    for (final Usecase usecase : usecasePayload) {
      if (usecase.getTelemetryConsumers() == null || usecase.getTelemetryConsumers().isEmpty()) {
        LOG.info(
            "Skipping this usecase since no stream settings were found {}",
            LogUtils.entries("usecase", usecase));
        continue;
      }
      if (usecase.getPublishGranularityMillis() != null) {
        metricCache.putPublishGranularity(
            usecase.getUuid(), Duration.ofMillis(usecase.getPublishGranularityMillis()));
      }
      for (final TelemetryConsumerSetting streamSetting : usecase.getTelemetryConsumers()) {
        if (streamSetting.getStreamApplications() == null
            || streamSetting.getStreamApplications().isEmpty()) {
          LOG.warn(
              "Skipping this stream setting since no application names were found {}",
              LogUtils.entries("usecase", usecase, "streamSetting", streamSetting));
          continue;
        }
        processStreamApplications(serviceSettings, streamSetting, usecase, datadogSender);
      }
    }
    if (!consumers.isEmpty()) {
      this.threadPool = Executors.newFixedThreadPool(consumers.size());
      for (final KinesisConsumer consumer : consumers) {
        threadPool.submit(consumer::start);
      }
    } else {
      LOG.warn(
          "No consumers were created. The service will be active but no consumers will be"
              + " running");
    }
  }

  private void processStreamApplications(
      final ServiceSettings serviceSettings,
      final TelemetryConsumerSetting streamSetting,
      final Usecase usecase,
      final DatadogSender datadogSender) {
    for (final StreamApplication application : streamSetting.getStreamApplications()) {
      try {
        IntegrationType integrationType = IntegrationType.fromName(application.getSink());
        switch (integrationType) {
          case DATADOG:
            consumers.add(
                new DatadogConsumer(
                    serviceSettings,
                    streamSetting.getStreamName(),
                    usecase.getUuid(),
                    application,
                    IntegrationLevel.GROUP,
                    datadogSender));
            break;
          case DATA_WAREHOUSE:
            consumers.add(
                new DataWarehouseConsumer(
                    serviceSettings,
                    streamSetting.getStreamName(),
                    usecase.getUuid(),
                    application,
                    null));
            break;
        }
      } catch (Exception e) {
        LOG.warn(
            "Invalid sink type {} for application {} in usecase {}",
            application.getSink(),
            application.getApplicationName(),
            usecase.getUuid(),
            e);
      }
    }
  }

  /** See {@link com.xgen.cloud.services.metrics.runtime.res.UsecaseResource#getAllUsecases} */
  static List<Usecase> fetchUsecases(final ServiceSettings serviceSettings) {
    final String path = "/usecase/v1/";
    final String baseUrl = serviceSettings.getString("client.customer-metrics.http.address");
    try (final HttpUtils client = HttpUtilsBuilder.builder().build()) {
      return client
          .get()
          .host(baseUrl)
          .path(path)
          .returnGeneric(List.class, Usecase.class)
          .expectedReturnStatus(HttpStatus.SC_OK)
          .send();
    } catch (final Exception e) {
      LOG.error("Unable to fetch usecases {}", LogUtils.entries("url", baseUrl, "path", path), e);
      return Collections.emptyList();
    }
  }

  private void shutdown() {
    for (final KinesisConsumer consumer : consumers) {
      if (consumer != null) {
        consumer.shutdown();
      }
    }
    if (threadPool != null) {
      threadPool.shutdown();
    }
  }
}
