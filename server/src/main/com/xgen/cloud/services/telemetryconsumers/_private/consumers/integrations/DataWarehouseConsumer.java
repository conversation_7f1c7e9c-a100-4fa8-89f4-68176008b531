package com.xgen.cloud.services.telemetryconsumers._private.consumers.integrations;

import com.amazonaws.services.kinesis.model.Record;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.common.util._public.util.ListUtils;
import com.xgen.cloud.common.util._public.util.LogUtils;
import com.xgen.cloud.services.core.base._public.config.ServiceSettings;
import com.xgen.cloud.services.metrics._public.model.usecase.StreamApplication;
import com.xgen.cloud.services.metrics._public.model.usecase.StreamApplicationKeys.DataWarehouseKeys;
import com.xgen.cloud.services.telemetryconsumers._private.consumers.kcl.KinesisConsumer;
import com.xgen.cloud.services.telemetryconsumers._private.metric.IntegrationLevel;
import com.xgen.cloud.services.telemetryconsumers._private.model.IntegrationType;
import configservicesdk.com.xgen.devtools.configservicesdk.Secret;
import io.prometheus.client.Counter;
import io.prometheus.client.Histogram;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.kinesis.KinesisClient;
import software.amazon.awssdk.services.kinesis.model.PutRecordsRequest;
import software.amazon.awssdk.services.kinesis.model.PutRecordsRequestEntry;

/**
 * This class forwards records to a Kinesis stream that is owned by the data engineering team. It is
 * used to send telemetry data to a data warehouse for further analysis.
 */
public class DataWarehouseConsumer extends KinesisConsumer {

  private static final Logger LOG = LoggerFactory.getLogger(DataWarehouseConsumer.class);
  private static final String ACCESS_KEY_SETTINGS_KEY = "integration.data_warehouse.accessKey";
  private static final String SECRET_KEY_SETTINGS_KEY = "integration.data_warehouse.secretKey";
  private static final String STREAM_APPLICATION_DWH_STREAM_NAME_SETTINGS_KEY =
      "dataWarehouseStreamName";
  private static final long FIVE_MB = FileUtils.ONE_MB * 5;
  private static final int MAX_RECORDS_PER_REQUEST = 500;

  private static final Counter ATTEMPTED_RECORDS =
      PromMetricsSvc.registerCounter(
          "telemetryconsumer_data_warehouse_attempted_records_total",
          "Number of attempted records to be published to Kinesis",
          "streamName",
          "application");

  private static final Counter SUCCESSFUL_RECORDS =
      PromMetricsSvc.registerCounter(
          "telemetryconsumer_data_warehouse_successful_records_total",
          "Number of successful records published to Kinesis",
          "streamName",
          "application");

  private static final Counter FAILED_RECORDS =
      PromMetricsSvc.registerCounter(
          "telemetryconsumer_data_warehouse_failed_records_total",
          "Number of failed records published to Kinesis",
          "streamName",
          "application",
          "error");

  private static final Histogram LATENCY =
      PromMetricsSvc.registerHistogram(
          "telemetryconsumer_data_warehouse_latency_seconds",
          "Latency of publishing records to Kinesis",
          PromMetricsSvc.getHistogramLinearBucket(0.15, 0.05, 8),
          "streamName",
          "application");

  private final KinesisClient kinesisClient;

  // The name of the Kinesis stream where data will be sent to
  private final String dataWarehouseStreamName;
  private boolean willStart = true;

  public DataWarehouseConsumer(
      ServiceSettings serviceSettings,
      String streamName,
      UUID usecaseUuid,
      StreamApplication streamApplication,
      IntegrationLevel integrationLevel) {
    super(
        IntegrationType.DATA_WAREHOUSE,
        serviceSettings,
        streamName,
        usecaseUuid,
        streamApplication,
        integrationLevel);
    dataWarehouseStreamName =
        (String)
            streamApplication.getSinkSettings().get(DataWarehouseKeys.STREAM_NAME_SETTINGS_KEY);
    final String accessKey =
        serviceSettings.get(Secret.class, ACCESS_KEY_SETTINGS_KEY).toPlainTextValue();
    final String secretKey =
        serviceSettings.get(Secret.class, SECRET_KEY_SETTINGS_KEY).toPlainTextValue();
    kinesisClient =
        KinesisClient.builder()
            .region(Region.US_EAST_1)
            .credentialsProvider(
                StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKey, secretKey)))
            .build();
    if (dataWarehouseStreamName == null || dataWarehouseStreamName.isEmpty()) {
      LOG.error(
          "No stream name found for DataWarehouse integration - not starting consumer {}",
          LogUtils.entries(
              "streamName", streamName,
              "usecaseUuid", usecaseUuid,
              "streamApplication", streamApplication));
      willStart = false;
    }
  }

  @Override
  public void processRecords(List<Record> records) {
    PromMetricsSvc.incrementCounter(
        ATTEMPTED_RECORDS, records.size(), getStreamName(), getApplication().getApplicationName());
    // Filter out records that are greater than 1MB since Kinesis will reject these records anyway
    records =
        records.stream()
            .filter(
                record ->
                    record.getData().remaining() + record.getPartitionKey().length()
                        < FileUtils.ONE_MB)
            .toList();
    // Partition the batch of entries such that NO partition is greater than the 5MB batch limit
    // that Kinesis imposes. Each record will likely land in a different shard, but we use the
    // PutRecords API to batch the records and reduce the number of API calls to Kinesis.
    final List<PutRecordsRequestEntry> recordsRequestEntries =
        records.stream()
            .map(
                record ->
                    PutRecordsRequestEntry.builder()
                        .partitionKey(record.getPartitionKey())
                        .data(SdkBytes.fromByteBuffer(record.getData()))
                        .build())
            .toList();
    List<List<PutRecordsRequestEntry>> partitionedBatch =
        ListUtils.partitionList(
            recordsRequestEntries,
            FIVE_MB,
            (record) -> record.data().asByteArrayUnsafe().length + record.partitionKey().length());
    // Further partition each batch to ensure there are no more than 500 entries per request
    // This is a limit imposed by the Kinesis PutRecords API
    partitionedBatch =
        partitionedBatch.stream()
            .flatMap(partition -> Lists.partition(partition, MAX_RECORDS_PER_REQUEST).stream())
            .toList();
    for (final List<PutRecordsRequestEntry> partition : partitionedBatch) {
      final var timer =
          PromMetricsSvc.startTimer(
              LATENCY, getStreamName(), getApplication().getApplicationName());
      try (timer) {
        var response =
            kinesisClient.putRecords(
                PutRecordsRequest.builder()
                    .streamName(dataWarehouseStreamName)
                    .records(partition)
                    .build());
        int failedRecords = Objects.requireNonNullElse(response.failedRecordCount(), 0);
        PromMetricsSvc.incrementCounter(
            SUCCESSFUL_RECORDS,
            partition.size() - failedRecords,
            getStreamName(),
            getApplication().getApplicationName());
        response.records().stream()
            .filter(
                record ->
                    !Strings.isNullOrEmpty(record.errorCode())
                        && !Strings.isNullOrEmpty(record.errorMessage()))
            .forEach(
                failedRecord -> {
                  PromMetricsSvc.incrementCounter(
                      FAILED_RECORDS,
                      getStreamName(),
                      getApplication().getApplicationName(),
                      failedRecord.errorCode());
                  LOG.debug(
                      "Failed processing record {}",
                      LogUtils.entries(
                          "streamName", getStreamName(),
                          "errorCode", failedRecord.errorCode(),
                          "errorMessage", failedRecord.errorMessage(),
                          "shardId", failedRecord.shardId()));
                });
      } catch (final Exception e) {
        LOG.error(
            "Failed to process records {}",
            LogUtils.entries(
                "errorMessage", e.getMessage(),
                "streamName", getStreamName(),
                "application", getApplication().getApplicationName()),
            e);
        PromMetricsSvc.incrementCounter(
            FAILED_RECORDS,
            partition.size(),
            getStreamName(),
            getApplication().getApplicationName(),
            e.getClass().getSimpleName());
      }
    }
  }

  @Override
  public void start() {
    if (willStart) {
      super.start();
    }
  }
}
