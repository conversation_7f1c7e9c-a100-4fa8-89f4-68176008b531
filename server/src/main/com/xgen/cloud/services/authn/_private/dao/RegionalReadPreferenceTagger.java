package com.xgen.cloud.services.authn._private.dao;

import static net.logstash.logback.argument.StructuredArguments.kv;

import com.mongodb.ReadPreference;
import com.mongodb.Tag;
import com.mongodb.TagSet;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import com.xgen.cloud.nds.azure._public.model.AzureRegionName;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionName;
import com.xgen.cloud.nds.gcp._public.model.GCPRegionName;
import com.xgen.cloud.services.core.base._public.config.HelixVariables;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Sets region tags on a {@link ReadPreference} based on the service {@link HelixVariables} so that
 * reads may be routed to replicas within the same region as the service deployment.
 *
 * <p><a
 * href="https://www.mongodb.com/docs/manual/core/read-preference-tags/#std-label-replica-set-read-preference-tag-sets">
 * MongoDB - Read Preference Tags</a>
 */
@Singleton
public class RegionalReadPreferenceTagger {

  private static final String REGION_TAG = "region";
  private static final Logger LOG = LoggerFactory.getLogger(RegionalReadPreferenceTagger.class);
  private final HelixVariables variables;

  @Inject
  public RegionalReadPreferenceTagger(HelixVariables variables) {
    this.variables = variables;
  }

  /**
   * Adds a region tag to the given {@link ReadPreference}.
   *
   * <p>Note: A read preference of primary cannot be used with read preference tagging.
   *
   * @param readPreference the read preference
   * @return the {@link ReadPreference} with a region tag added
   * @throws IllegalArgumentException when an invalid read preference such as primary is given
   */
  public ReadPreference tag(ReadPreference readPreference) {
    if (ReadPreference.primary().getName().equals(readPreference.getName())) {
      throw new IllegalArgumentException(
          "Read preference tags cannot be used with primary preference");
    }
    Optional<String> maybeAtlasDataRegion = getAtlasDataRegion(variables);
    if (maybeAtlasDataRegion.isEmpty()) {
      LOG.warn(
          "Could not find Atlas data region for read preference tagging - {} {}",
          kv("helixRegion", variables.region()),
          kv("helixCloudProvider", variables.cloudProvider()));
      return readPreference;
    }
    String atlasDataRegion = maybeAtlasDataRegion.get();
    LOG.debug("Setting regional read preference tag - region:{}", kv("value", atlasDataRegion));
    // An empty TagSet is added last to ensure we can fall back to any replica in the event none are
    // available with the desired regional tag instead of having an error happen
    return readPreference.withTagSetList(
        List.of(new TagSet(new Tag(REGION_TAG, atlasDataRegion)), new TagSet()));
  }

  private Optional<String> getAtlasDataRegion(HelixVariables helixVariables) {
    if (helixVariables.region() == null || helixVariables.cloudProvider() == null) {
      return Optional.empty();
    }
    // Normalizing these to lowercase to account for potential changes in the way Helix sets them
    String helixRegion = helixVariables.region().toLowerCase();
    String helixCloudProvider = helixVariables.cloudProvider().toLowerCase();
    CloudProvider cloudProvider = CloudProvider.findByNameIgnoreCase(helixCloudProvider);
    Optional<? extends RegionName> cloudRegionName =
        switch (cloudProvider) {
          case AWS -> AWSRegionName.findByValue(helixRegion);
          case GCP -> GCPRegionName.findByValue(helixRegion);
          case AZURE -> AzureRegionName.findByValue(helixRegion);
          default -> Optional.empty();
        };
    return cloudRegionName.map(RegionName::getName);
  }
}
