package com.xgen.cloud.services.authn._private.dao;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gt;
import static com.mongodb.client.model.Filters.lt;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Sorts.descending;
import static com.xgen.cloud.services.authn._private.model.CloudJwtSigningKey.FieldDefs.CREATED_DATE;
import static com.xgen.cloud.services.authn._private.model.CloudJwtSigningKey.FieldDefs.KEY_ID;
import static com.xgen.cloud.services.authn._private.model.CloudJwtSigningKey.FieldDefs.NOT_AFTER;
import static com.xgen.cloud.services.authn._private.model.CloudJwtSigningKey.FieldDefs.NOT_BEFORE;

import com.mongodb.ReadPreference;
import com.mongodb.WriteConcern;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.FindOneAndUpdateOptions;
import com.mongodb.client.model.ReturnDocument;
import com.mongodb.client.model.Updates;
import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.cloud.common.db.mongo._public.index.MongoIndex;
import com.xgen.cloud.services.authn._private.model.CloudJwtSigningKey;
import com.xgen.cloud.services.authn._private.model.JsonWebKey;
import com.xgen.cloud.services.core.base._public.config.Environment;
import io.micrometer.core.annotation.Timed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.configuration2.ImmutableConfiguration;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Timed(value = "authn_dao_methods_duration_seconds", histogram = true)
@Singleton
public class CloudJwtSigningKeyDao extends BaseDao<CloudJwtSigningKey> {
  private static final Logger LOG = LoggerFactory.getLogger(CloudJwtSigningKeyDao.class);
  private final ImmutableConfiguration settings;
  private final RegionalReadPreferenceTagger regionalReadPreferenceTagger;

  @Inject
  public CloudJwtSigningKeyDao(
      MongoClientContainer container,
      CodecRegistry codecRegistry,
      ImmutableConfiguration settings,
      RegionalReadPreferenceTagger regionalReadPreferenceTagger) {
    super(container, CloudJwtSigningKey.DB_NAME, CloudJwtSigningKey.COLLECTION_NAME, codecRegistry);
    this.regionalReadPreferenceTagger = regionalReadPreferenceTagger;
    this.settings = settings;

    seedDefaults();
  }

  public Optional<CloudJwtSigningKey> findByKeyId(String pKeyId) {
    return Optional.ofNullable(
        collectionWithRegionalSecondaryPreferred().find(eq(KEY_ID, pKeyId)).first());
  }

  public List<CloudJwtSigningKey> getActiveKeys() {
    Date currentTime = new Date();

    // Find keys where the current time is less than the notAfter field, or keys where the notAfter
    // field is null
    Bson notAfterQuery = or(gt(NOT_AFTER, currentTime), eq(NOT_AFTER, null));

    // Find keys where the current time is greater than the notBefore field
    Bson notBeforeQuery = lt(NOT_BEFORE, currentTime);

    // Find keys that aren't our rollout token exchange key, this is a special purpose key
    // only to be used under specific circumstances
    Bson notExchangeQuery = ne(KEY_ID, EXCHANGE_JWK.keyId());

    return collectionWithRegionalSecondaryPreferred()
        .find(and(notAfterQuery, notBeforeQuery, notExchangeQuery))
        .sort(descending(CREATED_DATE))
        .into(new ArrayList<>());
  }

  /**
   * Return all unexpired keys. Keys are not guaranteed to be currently active, as they may have
   * "notBefore" timestamps greater than the current time. Results are unordered.
   *
   * @return a list of unexpired CloudJwtSigningAwsKey's
   */
  public List<CloudJwtSigningKey> getNonexpiredKeys() {
    return collectionWithRegionalSecondaryPreferred()
        .find(or(gt(NOT_AFTER, new Date()), eq(NOT_AFTER, null)))
        .into(new ArrayList<>());
  }

  /**
   * @return list of keys whose notAfter time is past the current time
   */
  public List<CloudJwtSigningKey> getExpiredKeys() {
    return collectionWithRegionalSecondaryPreferred()
        .find(lt(NOT_AFTER, new Date()))
        .into(new ArrayList<>());
  }

  /**
   * @return the most recently added key - note: this does not necessarily mean the key is active
   */
  public Optional<CloudJwtSigningKey> getMostRecentKey() {
    return Optional.ofNullable(
        collectionWithRegionalSecondaryPreferred().find().sort(descending(CREATED_DATE)).first());
  }

  public void updateNotAfterDate(ObjectId cloudJwtSigningAWSKeyId, Date notAfter) {
    getCollection()
        .findOneAndUpdate(
            eq(cloudJwtSigningAWSKeyId),
            Updates.set(NOT_AFTER, notAfter),
            new FindOneAndUpdateOptions().returnDocument(ReturnDocument.AFTER));
  }

  public void updatePublicKey(String keyId, JsonWebKey publicKey) {
    getCollection()
        .withWriteConcern(WriteConcern.MAJORITY)
        .updateOne(eq(KEY_ID, keyId), Updates.set("publicKey", publicKey));
  }

  public void deleteKey(ObjectId cloudJwtSigningAWSKeyId) {
    getCollection().deleteOne(eq(cloudJwtSigningAWSKeyId));
  }

  @Override
  public List<MongoIndex> getIndexes() {
    List<MongoIndex> indexes = super.getIndexes();
    indexes.add(new MongoIndex.Builder().key(KEY_ID).unique().build());
    indexes.add(new MongoIndex.Builder().key(CREATED_DATE).build());

    return indexes;
  }

  // seed data for local development
  private static final String DEFAULT_LOCAL_KEY_ID = "7a8afa4b-5c2d-4166-ad92-c3eec399171f";
  private static final CloudJwtSigningKey DEFAULT_LOCAL_JWK =
      new CloudJwtSigningKey(
          ObjectId.get(),
          DEFAULT_LOCAL_KEY_ID,
          "us-east-1",
          Date.from(Instant.EPOCH),
          Date.from(Instant.EPOCH),
          null,
          new JsonWebKey(
              "EC",
              "P-521",
              "AZWTVBjVKA4kNbwHBmBpvbfnk-MH0IaxQXRfkQN8O2dcLhqUHm7JROKtF_4QLvWrVnWjqBzF473ptatGqdFzY6mD",
              "AbQPHPtEG_ww6ChjJ2NrDRiIc46tQvjhrePGkzY-ADw9vbWr4cGI69SnJyiPHxQ3OwYxCEf6qrqXDFQ2N4B7KsOn",
              DEFAULT_LOCAL_KEY_ID));

  // seed data for ExchangeKeyManager
  public static final String EXCHANGE_KEY_ID = "exchange-7e927db1-c2aa-460e-a3f3-054a47a98cc1";
  public static final CloudJwtSigningKey EXCHANGE_JWK =
      new CloudJwtSigningKey(
          ObjectId.get(),
          EXCHANGE_KEY_ID,
          "us-east-1",
          Date.from(Instant.EPOCH),
          Date.from(Instant.EPOCH),
          null,
          new JsonWebKey(
              "EC",
              "P-521",
              "ANBrI4-nbLWUiJ8fNSeQDtFkQ5qgi7HVhvcOQB8QezNIMN9qWJH3Ag0dnGnQZEcEKkOCyBJZfpnlFA3bWdCAjeaV",
              "Aejjw1hL2V1om_CmpUZMmBMPm19BpbIPHO3aTvUoXAn0y2UJ5BkcRjOTfUdcCQrZNn-7_sH91X0mqvNgtEit4xPw",
              EXCHANGE_KEY_ID));

  private static final Set<String> SEED_KEYS = Set.of(DEFAULT_LOCAL_KEY_ID, EXCHANGE_KEY_ID);

  public static boolean isSeedKey(CloudJwtSigningKey key) {
    return SEED_KEYS.contains(key.keyId());
  }

  private MongoCollection<CloudJwtSigningKey> collectionWithRegionalSecondaryPreferred() {
    return getCollection()
        .withReadPreference(regionalReadPreferenceTagger.tag(ReadPreference.secondaryPreferred()));
  }

  private void seedDefaults() {
    // local kms
    if (Environment.from(settings).orElse(Environment.LOCAL).isLocal()) {
      LOG.info("Deployment is in a local environment. Populating local signing key...");
      findByKeyId(DEFAULT_LOCAL_JWK.keyId())
          .ifPresentOrElse(
              existing -> {
                if (existing.publicKey() == null && DEFAULT_LOCAL_JWK.publicKey() != null) {
                  LOG.info("Backfilling public key for Static Local KMS signing key...");
                  updatePublicKey(DEFAULT_LOCAL_JWK.keyId(), DEFAULT_LOCAL_JWK.publicKey());
                }
              },
              () -> {
                LOG.info("Static Local KMS signing key not found in local db. Adding...");
                getCollection().insertOne(DEFAULT_LOCAL_JWK);
              });
    }

    // cross env exchange
    findByKeyId(EXCHANGE_JWK.keyId())
        .ifPresentOrElse(
            existing -> {
              if (existing.publicKey() == null && EXCHANGE_JWK.publicKey() != null) {
                LOG.info("Backfilling public key for Static Exchange KMS signing key...");
                updatePublicKey(EXCHANGE_JWK.keyId(), EXCHANGE_JWK.publicKey());
              }
            },
            () -> {
              LOG.info("Static Exchange KMS signing key not found in local db. Adding...");
              getCollection().insertOne(EXCHANGE_JWK);
            });
  }
}
