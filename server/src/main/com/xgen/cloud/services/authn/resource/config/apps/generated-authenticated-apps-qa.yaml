---
apps:
- name: "ACCOUNT"
  hosts:
  - value: "account-qa.mongodb.com"
    matchType: "EXACT"
  unauthenticatedRoutes:
  - path: "/"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/404"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/account(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/addSessionId(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/mfa/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/auth/mfa/verify(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/mfa/verify/resend(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/verify(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/auth/verify(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/verify/cancel(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/account/awsMpRegistrationRedirect(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/azureMpAuthProxy(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/azureMpRegistrationRedirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/connect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/connect/success(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/device/callback(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/eloqua(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/email/verify(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/encourage/mfa(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/gcpMpRegistrationRedirect(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/link/partner/consent(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/link/partner/consent(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/link/partner/vercel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/login(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/login/mfa(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/login/vercel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/logout(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/oauth(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/oidc/callback(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/oidc/start(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/profile/passwordReset(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register/cli(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register/success(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register/vercel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/mfa/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/password(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/password/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/university/password(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/resetPasswordComplete(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/resetPasswordRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/security/mfa(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/sso/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/support/reset/password/redirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/tos(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/unauthed/resend/verification/email(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/vercel/(.+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/verify/email/([^/]+)/([^/]+)/redirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/admin/nds/cipherSuiteOptions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/admin/nds/cloudProviders/([^/]+)/options(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/admin/nds/envoyInstances/([^/]+)/([^/]+)/collectMetrics(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/admin/nds/groups/([^/]+)/cipherSuiteOptions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/accessLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/biConnectors/status/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/changes/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/changes/v1/([^/]+)/testEvent(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/changes/v1/([^/]+)/testInvalidate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/clientMetadata/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/conf/auditOnly/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/conf/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/conf/v1/current/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/fullstatus/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/jobs/completed/multipart/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/jobs/completed/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/jobs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/jobs/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/liveExportMongoClientJobs/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)/mongoMirrorLogs(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)/mongosyncLogs(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/log/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/logIngestion/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/logIngestion/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/maintainedProcessesCrashLogs/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/batch/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/collStatsStorage/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/fts/batch/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/fts/batch/v2/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/queryStats/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/realtime/v2/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/tokenizedQueryShapeStats/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/migration/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/modules/status/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/mongodLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/mongotuneLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/pble/v1/([^/]+)/([^/]+)/pbleStatus(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/resharding/pendingOperations/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/resharding/pendingOperations/v1/([^/]+)/([^/]+)/marker/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/resharding/recentMarker/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/resharding/updateMarker/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/resharding/updateOperation/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/searchLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/settings/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/settings/v1/oneWayTokenizationParameters/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/shadowcluster/v1/recordingStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/streams/private/([^/]+)/getDNSRecords(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/streams/private/([^/]+)/vpcproxy/instance/status(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/agents/cps/checkpoints/batches/([^/]+)/cluster/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/agents/cps/checkpoints/killCursor/([^/]+)/cluster/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/agents/cps/checkpoints/v2/files/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/checkpoints/v2/files/([^/]+)/snapshot/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/checkpoints/wtcExtendComplete/([^/]+)/cluster/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/auth(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/collRestoreStates(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/collRestoreStates(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/overallState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/overallState(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/exports/([^/]+)/systemClusterExportJobs/([^/]+)/exportStatuses(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/metrics/([^/]+)/updateAttachStats/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/commonPointFound/([^/]+)/([^/]+)/([^/]+)/([^/]+)\\:([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/commonPointNotFound/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/agents/cps/oplog/invalidateSlices/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/slice/([^/]+)/([^/]+)\\:([^/]+)/([^/]+)\\:([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/oplog/slice/batch/v2/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/unknownOplogId/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/ai/v1/hello/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/automation/mongodb\\-releases/hybrid/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/automation/mongodb\\-releases/hybrid/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "HEAD"
  - path: "/automation/mongodb\\-releases/local/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/archive/([^/]+)/files(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/file/([^/]+)/blocks(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/file/([^/]+)/blocks/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/files(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/thirdParty/nodeState/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/thirdParty/oplogPaths/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/thirdParty/readyForCopyFiles/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/abortParallelRestore/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/chunk/([^/]+)/([^/]+)\\.tar/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/chunk/([^/]+)/([^/]+)\\.tar\\.gz/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/filteredFileList/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/finishParallelRestore/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/progress/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/v2/pull/([^/]+)/([^/]+)/([^/]+)\\.tar(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v2/pull/([^/]+)/([^/]+)/([^/]+)\\.tar\\.gz(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v3/pull/([^/]+)/([^/]+)\\.tar(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v3/pull/([^/]+)/([^/]+)\\.tar\\.gz(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v5/oplog/automation/([^/]+)/([^/]+)/([^/]+)\\:([^/]+)/([^/]+)\\\
      :([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/files(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/files/([^/]+)/blocks(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/files/([^/]+)/blocks/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/heartbeat(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/result(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/s3PreSignedUrl(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/activationCode/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/metadata(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/meterIds(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/opportunityChange/webhook/jira(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/aws/subscription(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/azure/subscription(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/azure/subscription/sales\\-sold(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/gcp/subscription(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/vercel/webhook(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/pricing(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/cluster/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/export/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/exportAllChunkCleanupComplete/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/exportChunkCleanupComplete/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/exportRecoveryComplete/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/pit/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/renewAwsStorageSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewAzureStorageSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewAzureStorageSessionForExport(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewGCPStorageSessionForExport(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewGcpStorageSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/serverlessPit/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/hostObservedDbCheckStartStops(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/hoststats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/opstats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/timestamp(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/cancel(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/getCrossShardObservations(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/nds/fts/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/nds/fts/([^/]+)/crash(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/nds/hostEncryptionConf/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/dataLandingZoneBucketCredentials(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/getArchiveRunInfo(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/jobId/([^/]+)/getJobErrors(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/jobId/([^/]+)/getJobProgress(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/lastRunStats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/markOAPaused(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/newOperation(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/pauseOA(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/registerOAFiles(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/setVersion(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/uploadDataFiles(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/tempCredentials(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/automation/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/backup/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/biconnector/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/client\\-pit/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/kmipProxy/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/monitoring/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/snapshot\\-volume/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/temp/S3/(.*)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/temp/S3/(.*)(/)?"
    matchType: "REGEX"
    httpMethod: "HEAD"
  - path: "/ecosystem(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/ecosystem/((.+)?)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/ecosystem/sitemap\\.xml(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/link/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/open/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/v2/link/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/v2/open/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/federation/discovery/idps(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/freemonitoring/cluster/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/freemonitoring/mongo/metrics(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/freemonitoring/mongo/register(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/group/checkGroupName(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/learn\\-more(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/learn\\-more/automation(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/learn\\-more/automation/quick\\-start(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/([^/]+)/explorer/([^/]+)/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/privacy\\-policy(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/registerForAtlas(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/support(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/terms\\-of\\-service(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/backupIngestion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/health(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/pool/agent(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/pool/offline(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/pool/ui(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/backup/files/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/backup/ipAllowedAccessList/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/backup/restoreJob/([^/]+)/exportStatus/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/backup/restoreJob/([^/]+)/status/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/backup/serverRunning/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/nodeAction/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/restoreStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/secrets/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/serverStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/chefcallback/updateAppliedPolicyVersion/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/chefcallback/updateBumperFileStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/chefcallback/uptime/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/aws/region/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/aws/roles/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/ftdc/aws/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/gcp/roles/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudchefconf/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/cloudchefconf/([^/]+)/([^/]+)/reboot(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudchefconf/([^/]+)/([^/]+)/replace(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudchefconf/([^/]+)/([^/]+)/tempCreds/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/cloudchefconf/([^/]+)/policy/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/cloudchefconf/([^/]+)/policyMetadata/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/compass/versions/latestStable(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/dataLakes/([^/]+)/regionMapping/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/dataValidation/groups/([^/]+)/clusters/([^/]+)/instances/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/diskWarmer/config/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/diskWarmer/progress/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/mongosh/versions/latestStable(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/networkRoutingSidecarConf/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/proxy/backups/([^/]+)/reset(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/backups/([^/]+)/restores/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/nds/proxy/backups/([^/]+)/snapshots/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/nds/proxy/conf/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/conf/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/conf/([^/]+)/([^/]+)/ack(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/errors/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/reporting/([^/]+)/operations(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/statsd/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/security/cert/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/cert/sls/([^/]+)/cert/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/cert/sls/([^/]+)/status/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/certManager/([^/]+)/([^/]+)/reload(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/security/certManager/config/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/csr/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/security/csr/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/sls/csr/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/sshkeyauth/([^/]+)/clusters/([^/]+)/appservices/x509readonlyaccess(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/sshkeyauth/([^/]+)/x509supportaccess(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/conf/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/errors/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/keys/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/uis/keys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/uis/keys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/metrics/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/removeKeys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/rotateKeys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/okta/hooks/acsError(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/oktaEventHooks/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/oktaEventHooks/emailStatusChange(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/oktaEventHooks/orgRateLimitViolation(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/oktaEventHooks/orgRateLimitWarning(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/orgs/webhook/vercel/removeIntegration(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/ping/proxy/v1/([^/]+)/batch(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/ping/proxy/v1/([^/]+)/metrics/status/batch(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/ping/proxy/v1/([^/]+)/serverless/metrics/status/batch(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/pricing(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/private/unauth/account/webhook/mfa/sms(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/prometheus/v1\\.0/groups/([^/]+)/discovery(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/robots\\.txt(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/backup(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/backup\\-offer(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/mms(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/monitoring(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/sitemap\\.xml(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/sso/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/sso/([^/]+)/debug(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/static/(.+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/stories(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/support(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/system/health(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/([^/]+)/serverlessLoadBalancingDeployment/isProvisioned(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/([^/]+)/snapshot(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/alert/html/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/alert/text/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/auth/groups/([^/]+)/clusters/([^/]+)/independentShardScalingMode(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/automation/mongodb\\-releases/available(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/automation/mongodb\\-releases/available/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/backup/config/oplogStores(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/backup/config/oplogStores/oplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/oplogStores/s3OplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/backup/config/snapshotStores/blockstore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/fileSystemStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore/fileBlockS3Keys(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore/immutable(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore/putFile(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/restore/jobs/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/canQueryAppDb(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/cluster/([^/]+)/setNextWTCClustershotTimestamp/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/clusterDescription/([^/]+)/([^/]+)/loadBalancedHostname(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/communication/search(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/configService/refreshStore(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/dataExport/([^/]+)/allowedFields(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/dataPlaneAccessRequests/([^/]+)/closeTestTicket(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/dataPlaneAccessRequests/createTestTicket(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/defaultTenantClusterVersions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/emailByRecipient(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/enableFeatureFlag/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/envoyInstances/([^/]+)/sshPrivateKeyAndPassphrase(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/flexMTM/([^/]+)/([^/]+)/sentinel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/forceACMEFailover/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/forceException(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/forwardedIp(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/getFirstAwsRegion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/getNdsOktaTestAuthorizationServerApiKey(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/govCloudActivationCode(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/clearTailTargetHostname(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/commonPointsNeeded(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/customBuilds(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/getLastOplogPushAfterRollbackJob(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/getLastOplogPushAndLatestOplogSlice(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/minBlockSize(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/modifyLastOplogPushAndDropOplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/modifyOplogMetadata(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/oplogSliceIntegrityCheckResult(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/oplogTailTarget(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/queryableMountStatus(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/scheduleOplogSliceIntegrityCheckJob(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/setBlockstoreMinBlockSize/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/setNextWTCSnapshotTimestamp/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/snapshots(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/verifyCommonPointState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/wtFileStats(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/wtcBackupStatus(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/wtcJustRestored(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/access/([^/]+)/expire(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/backupGroupConfig(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/clearTailTargetHostname(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/commonPointsNeeded(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/createDate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/getLastOplogPushAfterRollbackJob(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/getLastOplogPushAndLatestOplogSlice(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/hardwares/providers/([^/]+)/regions/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/host/([^/]+)/health/expire(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/host/([^/]+)/lastUpdate/expire(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/instanceHorizonsFromShardsAndConfig(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/modifyLastOplogPushAndDropOplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/modifyOplogMetadata(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/oplogSliceIntegrityCheckResult(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/oplogTailTarget(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/pausedDate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/queryableMountStatus(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/scheduleOplogSliceIntegrityCheckJob(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/verifyCommonPointState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/clusters/([^/]+)/snapshots(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/container/([^/]+)/setContainerLastUpdateDate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/encryptionAtRest(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/encryptionAtRest/([^/]+)/approvePrivateEndpoints(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/healthCheckDate/updateNow(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/planningDate/updateNow(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/saveHeadAndQueryableLogs(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/setcohort(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/m0ClusterActivity(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/m0PauseSnapshot(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/removeM0ClusterActivity(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/setNextBackupDateNow(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/setUserNotifiedAboutPauseDate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/useCNRegionsOnly(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/useGovRegionsOnly(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/verifyParallelRestore/([^/]+)/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/groups/([^/]+)/clusters/([^/]+)/hosts/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/groups/([^/]+)/isContainerAlive(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/groups/([^/]+)/plannerInfo(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/isNDSGovUSEnabled(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/lastPlanStartedDate(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/latestVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/latestVersionForMajor/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/liveImport/([^/]+)/errorMessage(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/liveImportCount(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/liveImportReservedPorts/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/logs/access(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/mfa/bypassEncouragement(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/mfa/org/requireMfa(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/mfa/unsnoozeMfaEncouragement(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/mpa/([^/]+)/approve(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/namer(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/aws/([^/]+)/privateEndpoint/([^/]+)/privateLinkHostname(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/aws/([^/]+)/privateEndpoint/([^/]+)/targetGroupArnForOptimizedPrivateEndpoint(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/dbUsers(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/([^/]+)/liveImport/([^/]+)/pids(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/nds/([^/]+)/liveImport/([^/]+)/pids(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/liveImport/([^/]+)/pods(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/pushBasedLogExport/forceCredentialsRotation(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/([^/]+)/pushBasedLogExport/tempCredentials(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/refreshJwks(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/hasOplogMigration(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/hasOplogSlice(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/job/updatePitWindow/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/oplogCoversPitWindow(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/oplogSlicesEncrypted(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/billing/generateLineItems/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/cloudProviderResourceObservers/dump(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/cloudProviderResourceObservers/register(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusterUpgradeToServerless/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/([^/]+)/serverless/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/continuousDeliveryVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latest(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latest/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latestLts(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latestLtsWithSuccessor(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/customMongoDbBuild(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/customMongoDbBuilds/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/nds/deleteOrphans(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/nds/diskWarming/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/encryptionAtRest/validate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/([^/]+)/([^/]+)/initialize(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/([^/]+)/allMigrations(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/flexMigration/([^/]+)/mtm/([^/]+)/([^/]+)/initiate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/([^/]+)/mtm/([^/]+)/([^/]+)/rollback(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/mtm/([^/]+)/([^/]+)/status(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/flexMigration/tenant/([^/]+)/([^/]+)/status(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/automationConfigVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/autoScalingMode(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/cloudProvider/([^/]+)/region/([^/]+)/hostnames(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/getAWSInstancesState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/hackInstanceOs(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/nodeType/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/backdoorCreateForBackfill(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/backfillArchiveJob(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/recoverDeletedOA(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/submitOnlineArchiveArchiveRestorationRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/submitOnlineArchiveRestoreOnlyJobIdsRestorationRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/submitV3Migration(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/serverless/metrics/daily(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/setMaxDiskSizeGB(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/stopAWSInstances(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/flexMeterUsages(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/lastSuccessfulMeterReportDate(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/limits(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/limits(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/maintenanceWindow(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/nextBillingMeterCheckDate(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/serverlessMeterUsages(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/tagResourceGroupDNR(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/x509access/atlasAdmin(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mongotune/version(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mtm/([^/]+)/capacity(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mtm/([^/]+)/capacity(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/mtm/([^/]+)/incrementCapacity(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/mtm/fastSharedRecords(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/mtm/fastSharedRecords/([^/]+)/count(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mtm/serverless/([^/]+)/loadBalancingDeployment(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/plans/findMostRecent/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/plans/setFailPoint(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/private/hosts/reboot(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/proxy/([^/]+)/([^/]+)/agentVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/proxy/([^/]+)/([^/]+)/allagentspinged(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/sampleArchivedClusters(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/sampleDatasetLoad/bucketData(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/mockedMetrics(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/updateLinkedMTMs(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/updateMTMLoad(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/updateTenantLoad(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/serverless/instance/([^/]+)/([^/]+)/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/mtm/([^/]+)/([^/]+)/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/serverlessProxy/([^/]+)/allHaveMetrics(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/serverlessProxy/([^/]+)/allProvisioned(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverlessUpgradeToDedicated/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/sshKeys/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/usedDiskSpace/([^/]+)/([^/]+)/hostname/([^/]+)/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/paymentStatusEmail(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/ping(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/precedingVersion/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/previousLatestVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/processExperimentUpdates(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/resetACMEFailover(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/test/utils/serverless/([^/]+)/disableEnvoyBypass(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverless/([^/]+)/enableEnvoyBypass(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverless/([^/]+)/envoyBypassNetworkOpened(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/serverless/([^/]+)/groupDisableEnvoyBypassAllTenants(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverless/([^/]+)/groupEnvoyBypassAllTenants(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverlessMTM/([^/]+)/([^/]+)/sentinel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/serverlessMTM/([^/]+)/([^/]+)/setSentinelDelete(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/sessions/tokens/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/setupServerlessMTMGroupName/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/sharedClusterVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/slowms/group/([^/]+)/host/([^/]+)/hostMeasurements(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/slowms/group/([^/]+)/metadata(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/slowms/host/([^/]+)/metadata(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/snapshots/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/systemTime(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/user/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/user/([^/]+)/2fa/reset(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/([^/]+)/2fa/setLastAuth/minutes/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/addMigrationUser(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/bypassEmailVerification(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/createCloudPerfApiKey/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/registerCall(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/v1/auth(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/v1/getMMSTopDiskUsageStats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/vercel/createIntegration(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/test/utils/vercel/deleteIntegration(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/verifyDirectS3Restore/([^/]+)/restoreJob/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/verifyParallelRestore/([^/]+)/restoreJob/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/versions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/uiMsgs(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/unsupportedBrowser(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/unsupportedBrowser/acknowledge(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/activationCode/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/authCodeCreationTimestamp(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/detectSession(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/external/postRegister(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/external/register(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/invitation/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/invitation/([^/]+)/redirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/login(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/logout(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/mfa/reset(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/mfa/reset/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/mfa/voicecall/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/oidc/callback(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/oidc/start(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/partnerIntegrationsData/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/refreshSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/register(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/register/landing(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/registerCall(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/reset/password/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/resetComplete(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/resetPassword(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/resetRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/v1/auth(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/version(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/webhook/billing/braintree/paymentMethod(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhook/billing/stripe/updateCard/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhook/sentry/alerts(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/internal/inboundMessageReceived(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/twilio/inboundMessageReceived(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/twilio/messageStatusUpdate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/twilio/voiceStatusUpdate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  redirectQueryParam: "fromURI"
  token:
    tokenCookieDomain: null
    failureResponseMode: "HANDLE"
- name: "MMS"
  hosts:
  - value: "cloud-qa.mongodb.com"
    matchType: "EXACT"
  unauthenticatedRoutes:
  - path: "/"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/404"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/account(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/addSessionId(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/mfa/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/auth/mfa/verify(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/mfa/verify/resend(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/verify(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/auth/verify(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/verify/cancel(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/account/awsMpRegistrationRedirect(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/azureMpAuthProxy(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/azureMpRegistrationRedirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/connect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/connect/success(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/device/callback(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/eloqua(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/email/verify(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/encourage/mfa(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/gcpMpRegistrationRedirect(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/link/partner/consent(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/link/partner/consent(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/link/partner/vercel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/login(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/login/mfa(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/login/vercel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/logout(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/oauth(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/oidc/callback(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/oidc/start(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/profile/passwordReset(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register/cli(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register/success(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register/vercel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/mfa/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/password(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/password/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/university/password(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/resetPasswordComplete(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/resetPasswordRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/security/mfa(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/sso/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/support/reset/password/redirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/tos(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/unauthed/resend/verification/email(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/vercel/(.+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/verify/email/([^/]+)/([^/]+)/redirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/admin/nds/cipherSuiteOptions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/admin/nds/cloudProviders/([^/]+)/options(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/admin/nds/envoyInstances/([^/]+)/([^/]+)/collectMetrics(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/admin/nds/groups/([^/]+)/cipherSuiteOptions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/accessLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/biConnectors/status/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/changes/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/changes/v1/([^/]+)/testEvent(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/changes/v1/([^/]+)/testInvalidate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/clientMetadata/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/conf/auditOnly/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/conf/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/conf/v1/current/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/fullstatus/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/jobs/completed/multipart/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/jobs/completed/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/jobs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/jobs/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/liveExportMongoClientJobs/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)/mongoMirrorLogs(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)/mongosyncLogs(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/log/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/logIngestion/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/logIngestion/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/maintainedProcessesCrashLogs/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/batch/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/collStatsStorage/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/fts/batch/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/fts/batch/v2/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/queryStats/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/realtime/v2/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/tokenizedQueryShapeStats/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/migration/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/modules/status/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/mongodLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/mongotuneLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/pble/v1/([^/]+)/([^/]+)/pbleStatus(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/resharding/pendingOperations/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/resharding/pendingOperations/v1/([^/]+)/([^/]+)/marker/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/resharding/recentMarker/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/resharding/updateMarker/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/resharding/updateOperation/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/searchLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/settings/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/settings/v1/oneWayTokenizationParameters/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/shadowcluster/v1/recordingStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/streams/private/([^/]+)/getDNSRecords(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/streams/private/([^/]+)/vpcproxy/instance/status(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/agents/cps/checkpoints/batches/([^/]+)/cluster/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/agents/cps/checkpoints/killCursor/([^/]+)/cluster/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/agents/cps/checkpoints/v2/files/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/checkpoints/v2/files/([^/]+)/snapshot/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/checkpoints/wtcExtendComplete/([^/]+)/cluster/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/auth(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/collRestoreStates(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/collRestoreStates(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/overallState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/overallState(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/exports/([^/]+)/systemClusterExportJobs/([^/]+)/exportStatuses(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/metrics/([^/]+)/updateAttachStats/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/commonPointFound/([^/]+)/([^/]+)/([^/]+)/([^/]+)\\:([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/commonPointNotFound/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/agents/cps/oplog/invalidateSlices/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/slice/([^/]+)/([^/]+)\\:([^/]+)/([^/]+)\\:([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/oplog/slice/batch/v2/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/unknownOplogId/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/ai/v1/hello/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/automation/mongodb\\-releases/hybrid/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/automation/mongodb\\-releases/hybrid/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "HEAD"
  - path: "/automation/mongodb\\-releases/local/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/archive/([^/]+)/files(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/file/([^/]+)/blocks(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/file/([^/]+)/blocks/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/files(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/thirdParty/nodeState/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/thirdParty/oplogPaths/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/thirdParty/readyForCopyFiles/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/abortParallelRestore/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/chunk/([^/]+)/([^/]+)\\.tar/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/chunk/([^/]+)/([^/]+)\\.tar\\.gz/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/filteredFileList/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/finishParallelRestore/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/progress/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/v2/pull/([^/]+)/([^/]+)/([^/]+)\\.tar(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v2/pull/([^/]+)/([^/]+)/([^/]+)\\.tar\\.gz(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v3/pull/([^/]+)/([^/]+)\\.tar(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v3/pull/([^/]+)/([^/]+)\\.tar\\.gz(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v5/oplog/automation/([^/]+)/([^/]+)/([^/]+)\\:([^/]+)/([^/]+)\\\
      :([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/files(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/files/([^/]+)/blocks(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/files/([^/]+)/blocks/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/heartbeat(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/result(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/s3PreSignedUrl(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/activationCode/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/metadata(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/meterIds(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/opportunityChange/webhook/jira(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/aws/subscription(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/azure/subscription(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/azure/subscription/sales\\-sold(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/gcp/subscription(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/vercel/webhook(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/pricing(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/cluster/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/export/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/exportAllChunkCleanupComplete/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/exportChunkCleanupComplete/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/exportRecoveryComplete/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/pit/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/renewAwsStorageSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewAzureStorageSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewAzureStorageSessionForExport(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewGCPStorageSessionForExport(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewGcpStorageSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/serverlessPit/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/hostObservedDbCheckStartStops(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/hoststats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/opstats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/timestamp(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/cancel(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/getCrossShardObservations(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/nds/fts/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/nds/fts/([^/]+)/crash(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/nds/hostEncryptionConf/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/dataLandingZoneBucketCredentials(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/getArchiveRunInfo(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/jobId/([^/]+)/getJobErrors(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/jobId/([^/]+)/getJobProgress(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/lastRunStats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/markOAPaused(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/newOperation(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/pauseOA(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/registerOAFiles(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/setVersion(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/uploadDataFiles(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/tempCredentials(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/automation/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/backup/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/biconnector/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/client\\-pit/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/kmipProxy/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/monitoring/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/snapshot\\-volume/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/temp/S3/(.*)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/temp/S3/(.*)(/)?"
    matchType: "REGEX"
    httpMethod: "HEAD"
  - path: "/ecosystem(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/ecosystem/((.+)?)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/ecosystem/sitemap\\.xml(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/link/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/open/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/v2/link/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/v2/open/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/federation/discovery/idps(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/freemonitoring/cluster/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/freemonitoring/mongo/metrics(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/freemonitoring/mongo/register(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/group/checkGroupName(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/learn\\-more(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/learn\\-more/automation(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/learn\\-more/automation/quick\\-start(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/([^/]+)/explorer/([^/]+)/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/privacy\\-policy(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/registerForAtlas(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/support(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/terms\\-of\\-service(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/backupIngestion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/health(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/pool/agent(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/pool/offline(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/pool/ui(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/backup/files/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/backup/ipAllowedAccessList/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/backup/restoreJob/([^/]+)/exportStatus/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/backup/restoreJob/([^/]+)/status/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/backup/serverRunning/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/nodeAction/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/restoreStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/secrets/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/serverStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/chefcallback/updateAppliedPolicyVersion/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/chefcallback/updateBumperFileStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/chefcallback/uptime/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/aws/region/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/aws/roles/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/ftdc/aws/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/gcp/roles/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudchefconf/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/cloudchefconf/([^/]+)/([^/]+)/reboot(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudchefconf/([^/]+)/([^/]+)/replace(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudchefconf/([^/]+)/([^/]+)/tempCreds/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/cloudchefconf/([^/]+)/policy/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/cloudchefconf/([^/]+)/policyMetadata/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/compass/versions/latestStable(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/dataLakes/([^/]+)/regionMapping/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/dataValidation/groups/([^/]+)/clusters/([^/]+)/instances/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/diskWarmer/config/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/diskWarmer/progress/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/mongosh/versions/latestStable(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/networkRoutingSidecarConf/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/proxy/backups/([^/]+)/reset(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/backups/([^/]+)/restores/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/nds/proxy/backups/([^/]+)/snapshots/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/nds/proxy/conf/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/conf/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/conf/([^/]+)/([^/]+)/ack(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/errors/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/reporting/([^/]+)/operations(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/statsd/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/security/cert/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/cert/sls/([^/]+)/cert/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/cert/sls/([^/]+)/status/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/certManager/([^/]+)/([^/]+)/reload(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/security/certManager/config/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/csr/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/security/csr/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/sls/csr/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/sshkeyauth/([^/]+)/clusters/([^/]+)/appservices/x509readonlyaccess(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/sshkeyauth/([^/]+)/x509supportaccess(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/conf/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/errors/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/keys/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/uis/keys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/uis/keys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/metrics/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/removeKeys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/rotateKeys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/okta/hooks/acsError(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/oktaEventHooks/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/oktaEventHooks/emailStatusChange(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/oktaEventHooks/orgRateLimitViolation(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/oktaEventHooks/orgRateLimitWarning(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/orgs/webhook/vercel/removeIntegration(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/ping/proxy/v1/([^/]+)/batch(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/ping/proxy/v1/([^/]+)/metrics/status/batch(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/ping/proxy/v1/([^/]+)/serverless/metrics/status/batch(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/pricing(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/private/unauth/account/webhook/mfa/sms(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/prometheus/v1\\.0/groups/([^/]+)/discovery(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/robots\\.txt(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/backup(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/backup\\-offer(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/mms(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/monitoring(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/sitemap\\.xml(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/sso/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/sso/([^/]+)/debug(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/static/(.+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/stories(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/support(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/system/health(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/([^/]+)/serverlessLoadBalancingDeployment/isProvisioned(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/([^/]+)/snapshot(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/alert/html/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/alert/text/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/auth/groups/([^/]+)/clusters/([^/]+)/independentShardScalingMode(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/automation/mongodb\\-releases/available(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/automation/mongodb\\-releases/available/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/backup/config/oplogStores(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/backup/config/oplogStores/oplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/oplogStores/s3OplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/backup/config/snapshotStores/blockstore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/fileSystemStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore/fileBlockS3Keys(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore/immutable(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore/putFile(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/restore/jobs/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/canQueryAppDb(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/cluster/([^/]+)/setNextWTCClustershotTimestamp/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/clusterDescription/([^/]+)/([^/]+)/loadBalancedHostname(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/communication/search(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/configService/refreshStore(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/dataExport/([^/]+)/allowedFields(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/dataPlaneAccessRequests/([^/]+)/closeTestTicket(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/dataPlaneAccessRequests/createTestTicket(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/defaultTenantClusterVersions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/emailByRecipient(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/enableFeatureFlag/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/envoyInstances/([^/]+)/sshPrivateKeyAndPassphrase(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/flexMTM/([^/]+)/([^/]+)/sentinel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/forceACMEFailover/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/forceException(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/forwardedIp(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/getFirstAwsRegion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/getNdsOktaTestAuthorizationServerApiKey(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/govCloudActivationCode(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/clearTailTargetHostname(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/commonPointsNeeded(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/customBuilds(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/getLastOplogPushAfterRollbackJob(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/getLastOplogPushAndLatestOplogSlice(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/minBlockSize(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/modifyLastOplogPushAndDropOplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/modifyOplogMetadata(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/oplogSliceIntegrityCheckResult(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/oplogTailTarget(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/queryableMountStatus(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/scheduleOplogSliceIntegrityCheckJob(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/setBlockstoreMinBlockSize/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/setNextWTCSnapshotTimestamp/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/snapshots(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/verifyCommonPointState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/wtFileStats(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/wtcBackupStatus(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/wtcJustRestored(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/access/([^/]+)/expire(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/backupGroupConfig(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/clearTailTargetHostname(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/commonPointsNeeded(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/createDate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/getLastOplogPushAfterRollbackJob(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/getLastOplogPushAndLatestOplogSlice(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/hardwares/providers/([^/]+)/regions/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/host/([^/]+)/health/expire(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/host/([^/]+)/lastUpdate/expire(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/instanceHorizonsFromShardsAndConfig(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/modifyLastOplogPushAndDropOplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/modifyOplogMetadata(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/oplogSliceIntegrityCheckResult(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/oplogTailTarget(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/pausedDate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/queryableMountStatus(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/scheduleOplogSliceIntegrityCheckJob(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/verifyCommonPointState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/clusters/([^/]+)/snapshots(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/container/([^/]+)/setContainerLastUpdateDate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/encryptionAtRest(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/encryptionAtRest/([^/]+)/approvePrivateEndpoints(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/healthCheckDate/updateNow(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/planningDate/updateNow(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/saveHeadAndQueryableLogs(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/setcohort(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/m0ClusterActivity(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/m0PauseSnapshot(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/removeM0ClusterActivity(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/setNextBackupDateNow(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/setUserNotifiedAboutPauseDate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/useCNRegionsOnly(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/useGovRegionsOnly(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/verifyParallelRestore/([^/]+)/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/groups/([^/]+)/clusters/([^/]+)/hosts/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/groups/([^/]+)/isContainerAlive(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/groups/([^/]+)/plannerInfo(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/isNDSGovUSEnabled(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/lastPlanStartedDate(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/latestVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/latestVersionForMajor/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/liveImport/([^/]+)/errorMessage(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/liveImportCount(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/liveImportReservedPorts/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/logs/access(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/mfa/bypassEncouragement(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/mfa/org/requireMfa(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/mfa/unsnoozeMfaEncouragement(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/mpa/([^/]+)/approve(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/namer(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/aws/([^/]+)/privateEndpoint/([^/]+)/privateLinkHostname(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/aws/([^/]+)/privateEndpoint/([^/]+)/targetGroupArnForOptimizedPrivateEndpoint(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/dbUsers(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/([^/]+)/liveImport/([^/]+)/pids(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/nds/([^/]+)/liveImport/([^/]+)/pids(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/liveImport/([^/]+)/pods(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/pushBasedLogExport/forceCredentialsRotation(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/([^/]+)/pushBasedLogExport/tempCredentials(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/refreshJwks(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/hasOplogMigration(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/hasOplogSlice(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/job/updatePitWindow/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/oplogCoversPitWindow(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/oplogSlicesEncrypted(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/billing/generateLineItems/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/cloudProviderResourceObservers/dump(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/cloudProviderResourceObservers/register(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusterUpgradeToServerless/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/([^/]+)/serverless/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/continuousDeliveryVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latest(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latest/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latestLts(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latestLtsWithSuccessor(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/customMongoDbBuild(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/customMongoDbBuilds/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/nds/deleteOrphans(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/nds/diskWarming/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/encryptionAtRest/validate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/([^/]+)/([^/]+)/initialize(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/([^/]+)/allMigrations(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/flexMigration/([^/]+)/mtm/([^/]+)/([^/]+)/initiate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/([^/]+)/mtm/([^/]+)/([^/]+)/rollback(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/mtm/([^/]+)/([^/]+)/status(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/flexMigration/tenant/([^/]+)/([^/]+)/status(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/automationConfigVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/autoScalingMode(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/cloudProvider/([^/]+)/region/([^/]+)/hostnames(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/getAWSInstancesState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/hackInstanceOs(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/nodeType/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/backdoorCreateForBackfill(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/backfillArchiveJob(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/recoverDeletedOA(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/submitOnlineArchiveArchiveRestorationRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/submitOnlineArchiveRestoreOnlyJobIdsRestorationRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/submitV3Migration(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/serverless/metrics/daily(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/setMaxDiskSizeGB(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/stopAWSInstances(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/flexMeterUsages(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/lastSuccessfulMeterReportDate(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/limits(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/limits(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/maintenanceWindow(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/nextBillingMeterCheckDate(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/serverlessMeterUsages(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/tagResourceGroupDNR(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/x509access/atlasAdmin(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mongotune/version(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mtm/([^/]+)/capacity(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mtm/([^/]+)/capacity(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/mtm/([^/]+)/incrementCapacity(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/mtm/fastSharedRecords(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/mtm/fastSharedRecords/([^/]+)/count(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mtm/serverless/([^/]+)/loadBalancingDeployment(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/plans/findMostRecent/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/plans/setFailPoint(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/private/hosts/reboot(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/proxy/([^/]+)/([^/]+)/agentVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/proxy/([^/]+)/([^/]+)/allagentspinged(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/sampleArchivedClusters(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/sampleDatasetLoad/bucketData(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/mockedMetrics(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/updateLinkedMTMs(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/updateMTMLoad(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/updateTenantLoad(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/serverless/instance/([^/]+)/([^/]+)/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/mtm/([^/]+)/([^/]+)/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/serverlessProxy/([^/]+)/allHaveMetrics(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/serverlessProxy/([^/]+)/allProvisioned(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverlessUpgradeToDedicated/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/sshKeys/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/usedDiskSpace/([^/]+)/([^/]+)/hostname/([^/]+)/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/paymentStatusEmail(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/ping(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/precedingVersion/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/previousLatestVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/processExperimentUpdates(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/resetACMEFailover(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/test/utils/serverless/([^/]+)/disableEnvoyBypass(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverless/([^/]+)/enableEnvoyBypass(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverless/([^/]+)/envoyBypassNetworkOpened(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/serverless/([^/]+)/groupDisableEnvoyBypassAllTenants(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverless/([^/]+)/groupEnvoyBypassAllTenants(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverlessMTM/([^/]+)/([^/]+)/sentinel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/serverlessMTM/([^/]+)/([^/]+)/setSentinelDelete(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/sessions/tokens/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/setupServerlessMTMGroupName/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/sharedClusterVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/slowms/group/([^/]+)/host/([^/]+)/hostMeasurements(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/slowms/group/([^/]+)/metadata(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/slowms/host/([^/]+)/metadata(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/snapshots/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/systemTime(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/user/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/user/([^/]+)/2fa/reset(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/([^/]+)/2fa/setLastAuth/minutes/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/addMigrationUser(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/bypassEmailVerification(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/createCloudPerfApiKey/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/registerCall(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/v1/auth(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/v1/getMMSTopDiskUsageStats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/vercel/createIntegration(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/test/utils/vercel/deleteIntegration(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/verifyDirectS3Restore/([^/]+)/restoreJob/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/verifyParallelRestore/([^/]+)/restoreJob/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/versions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/uiMsgs(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/unsupportedBrowser(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/unsupportedBrowser/acknowledge(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/activationCode/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/authCodeCreationTimestamp(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/detectSession(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/external/postRegister(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/external/register(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/invitation/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/invitation/([^/]+)/redirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/login(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/logout(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/mfa/reset(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/mfa/reset/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/mfa/voicecall/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/oidc/callback(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/oidc/start(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/partnerIntegrationsData/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/refreshSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/register(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/register/landing(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/registerCall(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/reset/password/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/resetComplete(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/resetPassword(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/resetRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/v1/auth(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/version(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/webhook/billing/braintree/paymentMethod(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhook/billing/stripe/updateCard/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhook/sentry/alerts(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/internal/inboundMessageReceived(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/twilio/inboundMessageReceived(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/twilio/messageStatusUpdate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/twilio/voiceStatusUpdate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  redirectQueryParam: "n"
  token:
    tokenCookieDomain: ".cloud-qa.mongodb.com"
    failureResponseMode: "HANDLE"
- name: "MMS Regional"
  hosts:
  - value: "us-east-1.cloud-qa.mongodb.com"
    matchType: "EXACT"
  - value: "eu-west-1.cloud-qa.mongodb.com"
    matchType: "EXACT"
  unauthenticatedRoutes:
  - path: "/"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/404"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/account(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/addSessionId(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/mfa/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/auth/mfa/verify(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/mfa/verify/resend(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/verify(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/auth/verify(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/auth/verify/cancel(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/account/awsMpRegistrationRedirect(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/azureMpAuthProxy(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/azureMpRegistrationRedirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/connect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/connect/success(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/device/callback(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/eloqua(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/email/verify(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/encourage/mfa(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/gcpMpRegistrationRedirect(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/link/partner/consent(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/link/partner/consent(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/link/partner/vercel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/login(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/login/mfa(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/login/vercel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/logout(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/oauth(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/oidc/callback(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/oidc/start(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/profile/passwordReset(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register/cli(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register/success(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/register/vercel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/mfa/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/password(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/password/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/reset/university/password(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/resetPasswordComplete(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/resetPasswordRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/security/mfa(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/sso/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/support/reset/password/redirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/tos(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/unauthed/resend/verification/email(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/account/vercel/(.+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/account/verify/email/([^/]+)/([^/]+)/redirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/admin/nds/cipherSuiteOptions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/admin/nds/cloudProviders/([^/]+)/options(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/admin/nds/envoyInstances/([^/]+)/([^/]+)/collectMetrics(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/admin/nds/groups/([^/]+)/cipherSuiteOptions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/accessLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/biConnectors/status/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/changes/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/changes/v1/([^/]+)/testEvent(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/changes/v1/([^/]+)/testInvalidate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/clientMetadata/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/conf/auditOnly/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/conf/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/conf/v1/current/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/fullstatus/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/jobs/completed/multipart/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/jobs/completed/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/jobs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/jobs/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/liveExportMongoClientJobs/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)/mongoMirrorLogs(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/liveExports/v1/([^/]+)/([^/]+)/mongosyncLogs(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/log/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/logIngestion/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/logIngestion/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/maintainedProcessesCrashLogs/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/batch/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/collStatsStorage/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/fts/batch/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/fts/batch/v2/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/queryStats/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/realtime/v2/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/tokenizedQueryShapeStats/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/metrics/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/migration/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/modules/status/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/mongodLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/mongotuneLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/pble/v1/([^/]+)/([^/]+)/pbleStatus(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/resharding/pendingOperations/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/resharding/pendingOperations/v1/([^/]+)/([^/]+)/marker/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/resharding/recentMarker/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/resharding/updateMarker/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/resharding/updateOperation/v1/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/searchLogs/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/automation/settings/v1/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/settings/v1/oneWayTokenizationParameters/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/automation/shadowcluster/v1/recordingStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/api/streams/private/([^/]+)/getDNSRecords(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/api/streams/private/([^/]+)/vpcproxy/instance/status(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/agents/cps/checkpoints/batches/([^/]+)/cluster/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/agents/cps/checkpoints/killCursor/([^/]+)/cluster/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/agents/cps/checkpoints/v2/files/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/checkpoints/v2/files/([^/]+)/snapshot/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/checkpoints/wtcExtendComplete/([^/]+)/cluster/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/auth(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/collRestoreStates(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/collRestoreStates(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/overallState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/collrestore/([^/]+)/collectionRestore/([^/]+)/overallState(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/exports/([^/]+)/systemClusterExportJobs/([^/]+)/exportStatuses(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/metrics/([^/]+)/updateAttachStats/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/commonPointFound/([^/]+)/([^/]+)/([^/]+)/([^/]+)\\:([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/commonPointNotFound/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/agents/cps/oplog/invalidateSlices/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/slice/([^/]+)/([^/]+)\\:([^/]+)/([^/]+)\\:([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/agents/cps/oplog/slice/batch/v2/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/agents/cps/oplog/unknownOplogId/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/ai/v1/hello/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/automation/mongodb\\-releases/hybrid/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/automation/mongodb\\-releases/hybrid/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "HEAD"
  - path: "/automation/mongodb\\-releases/local/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/archive/([^/]+)/files(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/file/([^/]+)/blocks(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/file/([^/]+)/blocks/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/files(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/thirdParty/nodeState/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/thirdParty/oplogPaths/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/([^/]+)/thirdParty/readyForCopyFiles/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/abortParallelRestore/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/chunk/([^/]+)/([^/]+)\\.tar/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/chunk/([^/]+)/([^/]+)\\.tar\\.gz/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/filteredFileList/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/finishParallelRestore/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/progress/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/restore/v2/pull/([^/]+)/([^/]+)/([^/]+)\\.tar(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v2/pull/([^/]+)/([^/]+)/([^/]+)\\.tar\\.gz(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v3/pull/([^/]+)/([^/]+)\\.tar(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v3/pull/([^/]+)/([^/]+)\\.tar\\.gz(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/restore/v5/oplog/automation/([^/]+)/([^/]+)/([^/]+)\\:([^/]+)/([^/]+)\\\
      :([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/files(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/files/([^/]+)/blocks(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/files/([^/]+)/blocks/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/heartbeat(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/result(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/backup/snapshot\\-validation\\-jobs/([^/]+)/s3PreSignedUrl(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/activationCode/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/metadata(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/meterIds(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/billing/opportunityChange/webhook/jira(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/aws/subscription(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/azure/subscription(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/azure/subscription/sales\\-sold(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/gcp/subscription(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/partners/notification/vercel/webhook(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/billing/pricing(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/cluster/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/export/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/exportAllChunkCleanupComplete/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/exportChunkCleanupComplete/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/exportRecoveryComplete/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/pit/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/cps/([^/]+)/renewAwsStorageSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewAzureStorageSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewAzureStorageSessionForExport(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewGCPStorageSessionForExport(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/renewGcpStorageSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/cps/([^/]+)/serverlessPit/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/hostObservedDbCheckStartStops(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/hoststats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/opstats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/([^/]+)/timestamp(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/cancel(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/dbcheck/([^/]+)/([^/]+)/getCrossShardObservations(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/nds/fts/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/nds/fts/([^/]+)/crash(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/nds/hostEncryptionConf/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/dataLandingZoneBucketCredentials(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/getArchiveRunInfo(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/jobId/([^/]+)/getJobErrors(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/jobId/([^/]+)/getJobProgress(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/lastRunStats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/markOAPaused(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/newOperation(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/pauseOA(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/registerOAFiles(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/setVersion(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/archive/([^/]+)/uploadDataFiles(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/conf/onlinearchive/([^/]+)/cluster/([^/]+)/tempCredentials(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/automation/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/backup/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/biconnector/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/client\\-pit/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/kmipProxy/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/monitoring/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/agent/snapshot\\-volume/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/temp/S3/(.*)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/download/temp/S3/(.*)(/)?"
    matchType: "REGEX"
    httpMethod: "HEAD"
  - path: "/ecosystem(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/ecosystem/((.+)?)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/ecosystem/sitemap\\.xml(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/link/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/open/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/v2/link/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/email/v2/open/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/federation/discovery/idps(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/freemonitoring/cluster/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/freemonitoring/mongo/metrics(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/freemonitoring/mongo/register(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/group/checkGroupName(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/learn\\-more(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/learn\\-more/automation(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/learn\\-more/automation/quick\\-start(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/([^/]+)/explorer/([^/]+)/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/privacy\\-policy(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/registerForAtlas(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/support(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/links/terms\\-of\\-service(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/backupIngestion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/health(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/pool/agent(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/pool/offline(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/monitor/pool/ui(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/backup/files/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/backup/ipAllowedAccessList/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/backup/restoreJob/([^/]+)/exportStatus/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/backup/restoreJob/([^/]+)/status/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/backup/serverRunning/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/nodeAction/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/restoreStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/secrets/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/chefcallback/serverStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/chefcallback/updateAppliedPolicyVersion/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/chefcallback/updateBumperFileStatus/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/chefcallback/uptime/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/aws/region/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/aws/roles/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/ftdc/aws/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudProviderAccess/agent/([^/]+)/credentials/gcp/roles/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudchefconf/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/cloudchefconf/([^/]+)/([^/]+)/reboot(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudchefconf/([^/]+)/([^/]+)/replace(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/cloudchefconf/([^/]+)/([^/]+)/tempCreds/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/cloudchefconf/([^/]+)/policy/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/cloudchefconf/([^/]+)/policyMetadata/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/compass/versions/latestStable(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/dataLakes/([^/]+)/regionMapping/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/dataValidation/groups/([^/]+)/clusters/([^/]+)/instances/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/diskWarmer/config/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/diskWarmer/progress/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/mongosh/versions/latestStable(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/networkRoutingSidecarConf/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/proxy/backups/([^/]+)/reset(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/backups/([^/]+)/restores/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/nds/proxy/backups/([^/]+)/snapshots/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/nds/proxy/conf/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/conf/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/conf/([^/]+)/([^/]+)/ack(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/errors/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/reporting/([^/]+)/operations(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/proxy/statsd/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/security/cert/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/cert/sls/([^/]+)/cert/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/cert/sls/([^/]+)/status/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/certManager/([^/]+)/([^/]+)/reload(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/security/certManager/config/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/csr/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/security/csr/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/security/sls/csr/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/sshkeyauth/([^/]+)/clusters/([^/]+)/appservices/x509readonlyaccess(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/sshkeyauth/([^/]+)/x509supportaccess(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/conf/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/errors/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/keys/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/uis/keys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/nds/uis/keys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/metrics/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/removeKeys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/nds/uis/rotateKeys/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/okta/hooks/acsError(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/oktaEventHooks/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/oktaEventHooks/emailStatusChange(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/oktaEventHooks/orgRateLimitViolation(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/oktaEventHooks/orgRateLimitWarning(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/orgs/webhook/vercel/removeIntegration(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/ping/proxy/v1/([^/]+)/batch(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/ping/proxy/v1/([^/]+)/metrics/status/batch(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/ping/proxy/v1/([^/]+)/serverless/metrics/status/batch(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/pricing(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/private/unauth/account/webhook/mfa/sms(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/prometheus/v1\\.0/groups/([^/]+)/discovery(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/robots\\.txt(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/backup(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/backup\\-offer(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/mms(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/signup/monitoring(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/sitemap\\.xml(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/sso/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/sso/([^/]+)/debug(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/static/(.+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/stories(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/support(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/system/health(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/([^/]+)/serverlessLoadBalancingDeployment/isProvisioned(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/([^/]+)/snapshot(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/alert/html/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/alert/text/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/auth/groups/([^/]+)/clusters/([^/]+)/independentShardScalingMode(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/automation/mongodb\\-releases/available(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/automation/mongodb\\-releases/available/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/backup/config/oplogStores(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/backup/config/oplogStores/oplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/oplogStores/s3OplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/backup/config/snapshotStores/blockstore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/fileSystemStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore/fileBlockS3Keys(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore/immutable(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/config/snapshotStores/s3blockstore/putFile(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/backup/restore/jobs/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/canQueryAppDb(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/cluster/([^/]+)/setNextWTCClustershotTimestamp/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/clusterDescription/([^/]+)/([^/]+)/loadBalancedHostname(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/communication/search(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/configService/refreshStore(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/dataExport/([^/]+)/allowedFields(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/dataPlaneAccessRequests/([^/]+)/closeTestTicket(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/dataPlaneAccessRequests/createTestTicket(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/defaultTenantClusterVersions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/emailByRecipient(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/enableFeatureFlag/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/envoyInstances/([^/]+)/sshPrivateKeyAndPassphrase(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/flexMTM/([^/]+)/([^/]+)/sentinel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/forceACMEFailover/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/forceException(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/forwardedIp(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/getFirstAwsRegion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/getNdsOktaTestAuthorizationServerApiKey(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/govCloudActivationCode(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/clearTailTargetHostname(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/commonPointsNeeded(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/customBuilds(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/getLastOplogPushAfterRollbackJob(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/getLastOplogPushAndLatestOplogSlice(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/minBlockSize(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/modifyLastOplogPushAndDropOplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/modifyOplogMetadata(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/oplogSliceIntegrityCheckResult(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/oplogTailTarget(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/queryableMountStatus(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/scheduleOplogSliceIntegrityCheckJob(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/setBlockstoreMinBlockSize/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/setNextWTCSnapshotTimestamp/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/([^/]+)/snapshots(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/verifyCommonPointState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/wtFileStats(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/wtcBackupStatus(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/([^/]+)/wtcJustRestored(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/access/([^/]+)/expire(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/backupGroupConfig(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/clearTailTargetHostname(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/commonPointsNeeded(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/createDate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/getLastOplogPushAfterRollbackJob(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/getLastOplogPushAndLatestOplogSlice(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/hardwares/providers/([^/]+)/regions/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/host/([^/]+)/health/expire(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/host/([^/]+)/lastUpdate/expire(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/instanceHorizonsFromShardsAndConfig(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/modifyLastOplogPushAndDropOplogStore(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/modifyOplogMetadata(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/oplogSliceIntegrityCheckResult(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/oplogTailTarget(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/pausedDate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/queryableMountStatus(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/scheduleOplogSliceIntegrityCheckJob(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/cluster/([^/]+)/verifyCommonPointState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/clusters/([^/]+)/snapshots(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/container/([^/]+)/setContainerLastUpdateDate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/encryptionAtRest(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/encryptionAtRest/([^/]+)/approvePrivateEndpoints(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/healthCheckDate/updateNow(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/planningDate/updateNow(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/saveHeadAndQueryableLogs(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/setcohort(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/m0ClusterActivity(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/m0PauseSnapshot(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/removeM0ClusterActivity(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/setNextBackupDateNow(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/tenant/cluster/([^/]+)/setUserNotifiedAboutPauseDate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/group/([^/]+)/useCNRegionsOnly(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/useGovRegionsOnly(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/group/([^/]+)/verifyParallelRestore/([^/]+)/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/groups/([^/]+)/clusters/([^/]+)/hosts/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/groups/([^/]+)/isContainerAlive(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/groups/([^/]+)/plannerInfo(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/isNDSGovUSEnabled(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/lastPlanStartedDate(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/latestVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/latestVersionForMajor/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/liveImport/([^/]+)/errorMessage(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/liveImportCount(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/liveImportReservedPorts/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/logs/access(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/mfa/bypassEncouragement(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/mfa/org/requireMfa(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/mfa/unsnoozeMfaEncouragement(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/mpa/([^/]+)/approve(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/namer(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/aws/([^/]+)/privateEndpoint/([^/]+)/privateLinkHostname(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/aws/([^/]+)/privateEndpoint/([^/]+)/targetGroupArnForOptimizedPrivateEndpoint(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/dbUsers(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/([^/]+)/liveImport/([^/]+)/pids(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/nds/([^/]+)/liveImport/([^/]+)/pids(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/liveImport/([^/]+)/pods(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/pushBasedLogExport/forceCredentialsRotation(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/([^/]+)/pushBasedLogExport/tempCredentials(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/([^/]+)/refreshJwks(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/hasOplogMigration(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/hasOplogSlice(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/job/updatePitWindow/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/oplogCoversPitWindow(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/backup/([^/]+)/([^/]+)/oplogSlicesEncrypted(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/billing/generateLineItems/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/cloudProviderResourceObservers/dump(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/cloudProviderResourceObservers/register(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusterUpgradeToServerless/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/([^/]+)/serverless/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/continuousDeliveryVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latest(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latest/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latestLts(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/clusters/mongoDBVersion/latestLtsWithSuccessor(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/customMongoDbBuild(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/customMongoDbBuilds/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/nds/deleteOrphans(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/nds/diskWarming/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/encryptionAtRest/validate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/([^/]+)/([^/]+)/initialize(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/([^/]+)/allMigrations(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/flexMigration/([^/]+)/mtm/([^/]+)/([^/]+)/initiate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/([^/]+)/mtm/([^/]+)/([^/]+)/rollback(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/flexMigration/mtm/([^/]+)/([^/]+)/status(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/flexMigration/tenant/([^/]+)/([^/]+)/status(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/automationConfigVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/autoScalingMode(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/cloudProvider/([^/]+)/region/([^/]+)/hostnames(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/getAWSInstancesState(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/hackInstanceOs(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/nodeType/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/backdoorCreateForBackfill(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/backfillArchiveJob(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/recoverDeletedOA(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/submitOnlineArchiveArchiveRestorationRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/submitOnlineArchiveRestoreOnlyJobIdsRestorationRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/onlineArchive/([^/]+)/submitV3Migration(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/serverless/metrics/daily(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/setMaxDiskSizeGB(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/clusters/([^/]+)/stopAWSInstances(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/flexMeterUsages(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/lastSuccessfulMeterReportDate(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/limits(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/limits(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/maintenanceWindow(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/nextBillingMeterCheckDate(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/groups/([^/]+)/serverlessMeterUsages(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/groups/([^/]+)/tagResourceGroupDNR(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/groups/([^/]+)/x509access/atlasAdmin(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mongotune/version(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mtm/([^/]+)/capacity(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mtm/([^/]+)/capacity(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/mtm/([^/]+)/incrementCapacity(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/mtm/fastSharedRecords(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/mtm/fastSharedRecords/([^/]+)/count(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/mtm/serverless/([^/]+)/loadBalancingDeployment(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/plans/findMostRecent/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/plans/setFailPoint(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/private/hosts/reboot(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/proxy/([^/]+)/([^/]+)/agentVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/proxy/([^/]+)/([^/]+)/allagentspinged(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/sampleArchivedClusters(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/sampleDatasetLoad/bucketData(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/mockedMetrics(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/updateLinkedMTMs(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/updateMTMLoad(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/serverless/([^/]+)/([^/]+)/updateTenantLoad(/)?"
    matchType: "REGEX"
    httpMethod: "PATCH"
  - path: "/test/utils/nds/serverless/instance/([^/]+)/([^/]+)/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/mtm/([^/]+)/([^/]+)/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/serverlessProxy/([^/]+)/allHaveMetrics(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverless/serverlessProxy/([^/]+)/allProvisioned(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/serverlessUpgradeToDedicated/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/sshKeys/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/nds/usedDiskSpace/([^/]+)/([^/]+)/hostname/([^/]+)/metrics/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/paymentStatusEmail(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/ping(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/precedingVersion/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/previousLatestVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/processExperimentUpdates(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/resetACMEFailover(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/test/utils/serverless/([^/]+)/disableEnvoyBypass(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverless/([^/]+)/enableEnvoyBypass(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverless/([^/]+)/envoyBypassNetworkOpened(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/serverless/([^/]+)/groupDisableEnvoyBypassAllTenants(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverless/([^/]+)/groupEnvoyBypassAllTenants(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/serverlessMTM/([^/]+)/([^/]+)/sentinel(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/serverlessMTM/([^/]+)/([^/]+)/setSentinelDelete(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/sessions/tokens/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/setupServerlessMTMGroupName/([^/]+)/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/sharedClusterVersion(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/slowms/group/([^/]+)/host/([^/]+)/hostMeasurements(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/slowms/group/([^/]+)/metadata(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/slowms/host/([^/]+)/metadata(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/snapshots/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/systemTime(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/user/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/user/([^/]+)/2fa/reset(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/([^/]+)/2fa/setLastAuth/minutes/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/addMigrationUser(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/bypassEmailVerification(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/createCloudPerfApiKey/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/registerCall(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/user/v1/auth(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/v1/getMMSTopDiskUsageStats(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/test/utils/vercel/createIntegration(/)?"
    matchType: "REGEX"
    httpMethod: "PUT"
  - path: "/test/utils/vercel/deleteIntegration(/)?"
    matchType: "REGEX"
    httpMethod: "DELETE"
  - path: "/test/utils/verifyDirectS3Restore/([^/]+)/restoreJob/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/verifyParallelRestore/([^/]+)/restoreJob/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/test/utils/versions(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/uiMsgs(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/unsupportedBrowser(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/unsupportedBrowser/acknowledge(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/activationCode/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/authCodeCreationTimestamp(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/detectSession(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/external/postRegister(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/external/register(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/invitation/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/invitation/([^/]+)/redirect(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/login(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/logout(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/mfa/reset(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/mfa/reset/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/mfa/voicecall/([^/]+)/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/oidc/callback(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/oidc/start(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/partnerIntegrationsData/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/refreshSession(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/register(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/register/landing(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/registerCall(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/reset/password/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/resetComplete(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/resetPassword(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/user/resetRequest(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/user/v1/auth(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/version(/)?"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/webhook/billing/braintree/paymentMethod(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhook/billing/stripe/updateCard/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhook/sentry/alerts(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/internal/inboundMessageReceived(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/twilio/inboundMessageReceived(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/twilio/messageStatusUpdate/([^/]+)(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/webhooks/incoming/twilio/voiceStatusUpdate(/)?"
    matchType: "REGEX"
    httpMethod: "POST"
  redirectQueryParam: "n"
  token:
    tokenCookieDomain: ".cloud-qa.mongodb.com"
    failureResponseMode: "HANDLE"
- name: "CCS"
  hosts:
  - value: "cluster-connection.cloud-qa.mongodb.com"
    matchType: "EXACT"
  unauthenticatedRoutes: []
  redirectQueryParam: "fromUri"
  token:
    tokenCookieDomain: ".cloud-qa.mongodb.com"
    failureResponseMode: "HANDLE"
- name: "BAAS"
  hosts:
  - value: "services.cloud-qa.mongodb.com"
    matchType: "EXACT"
  - value: ".+\\.services.cloud-qa\\.mongodb\\.com"
    matchType: "REGEX"
  unauthenticatedRoutes:
  - path: " /api/client/v2.0/(.+)"
    matchType: "REGEX"
    httpMethod: "*"
  - path: "/api/admin/v3.0/auth/providers"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/admin/v3.0/auth/providers/(.+)/login"
    matchType: "REGEX"
    httpMethod: "*"
  - path: "/api/admin/v3.0/auth/session"
    matchType: "EXACT"
    httpMethod: "DELETE"
  - path: "/api/admin/v3.0/auth/session"
    matchType: "EXACT"
    httpMethod: "POST"
  - path: "/api/admin/v3.0/cli/info"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/admin/v3.0/deploy/(.+)/auth"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/api/admin/v3.0/deploy/(.+)/auth_by_group"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/api/admin/v3.0/deploy/(.+)/webhook"
    matchType: "REGEX"
    httpMethod: "POST"
  - path: "/api/admin/v3.0/provider_regions"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/admin/v3.0/provider_regions/nearest"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/private/v1.0/analytics/reports/admin_requests"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/private/v1.0/analytics/reports/apps"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/private/v1.0/analytics/reports/apps/lookup"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/private/v1.0/analytics/reports/client_requests"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/private/v1.0/app/(.+)"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/api/private/v1.0/app_metrics"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/private/v1.0/billing/metrics/apps"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/private/v1.0/billing/reports/apps"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/private/v1.0/default_m0_version"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/private/v1.0/event/cluster/delete/submitted"
    matchType: "EXACT"
    httpMethod: "POST"
  - path: "/api/private/v1.0/event/cluster/update/completed"
    matchType: "EXACT"
    httpMethod: "POST"
  - path: "/api/private/v1.0/groups/(.+)/apps/(.+)/track_event"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/api/private/v1.0/provider/(.+)/atlas_regions/(.+)/nearest_app_region"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/api/private/v1.0/provider/(.+)/service/(.+)/regions"
    matchType: "REGEX"
    httpMethod: "GET"
  - path: "/api/private/v1.0/spa/recaptcha/verify"
    matchType: "EXACT"
    httpMethod: "POST"
  - path: "/api/private/v1.0/spa/session/validate"
    matchType: "EXACT"
    httpMethod: "GET"
  - path: "/api/private/v1.0/version"
    matchType: "EXACT"
    httpMethod: "GET"
  redirectQueryParam: "fromUri"
  token:
    tokenCookieDomain: ".cloud-qa.mongodb.com"
    failureResponseMode: "PASSTHROUGH"
- name: "WSS"
  hosts:
  - value: "atlas-cluster-ws.us-east-1.aws.cloud-qa.mongodb.com"
    matchType: "EXACT"
  - value: "atlas-cluster-ws.cloud-qa.mongodb.com"
    matchType: "EXACT"
  unauthenticatedRoutes: []
  redirectQueryParam: "fromUri"
  token:
    tokenCookieDomain: ".cloud-qa.mongodb.com"
    failureResponseMode: "PASSTHROUGH"
- name: "BAAS_LEGACY"
  hosts:
  - value: "realm-qa.mongodb.com"
    matchType: "EXACT"
  - value: ".+\\.realm-qa\\.mongodb\\.com"
    matchType: "REGEX"
  - value: "stitch-qa.mongodb.com"
    matchType: "EXACT"
  - value: ".+\\.stitch-qa\\.mongodb\\.com"
    matchType: "REGEX"
  unauthenticatedRoutes:
  - path: ".*"
    matchType: "REGEX"
    httpMethod: "*"
  redirectQueryParam: null
  token:
    tokenCookieDomain: null
    failureResponseMode: "PASSTHROUGH"
