package com.xgen.cloud.services.payments.modules.selfServeProcessing.runtime.grpc;

import static com.xgen.cloud.event._public.interceptor.EventSourceMetadataInterceptor.EVENT_SOURCE_CONTEXT_KEY;
import static java.util.Objects.requireNonNull;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.google.protobuf.Empty;
import com.xgen.cloud.payments.processing._public.model.RefundType;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.converter.RevenueRefundReasonGrpcConverter;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentResult;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundRequest;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.svc.PendingReversalCancellationSvc;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.svc.RefundPaymentSvc;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.svc.RefundRequestFactory;
import com.xgen.cloud.services.payments.proto.FullRefundInvoiceRequest;
import com.xgen.cloud.services.payments.proto.FullRefundInvoiceResponse;
import com.xgen.cloud.services.payments.proto.FullRefundPaymentRequest;
import com.xgen.cloud.services.payments.proto.FullRefundPaymentResponse;
import com.xgen.cloud.services.payments.proto.FullRefundPaymentWithoutForgivingChargesRequest;
import com.xgen.cloud.services.payments.proto.FullRefundPaymentWithoutForgivingChargesResponse;
import com.xgen.cloud.services.payments.proto.FullTaxRefundInvoiceRequest;
import com.xgen.cloud.services.payments.proto.FullTaxRefundInvoiceResponse;
import com.xgen.cloud.services.payments.proto.FullTaxRefundPaymentRequest;
import com.xgen.cloud.services.payments.proto.FullTaxRefundPaymentResponse;
import com.xgen.cloud.services.payments.proto.PartialRefundPaymentRequest;
import com.xgen.cloud.services.payments.proto.PartialRefundPaymentResponse;
import com.xgen.cloud.services.payments.proto.PartialTaxRefundPaymentRequest;
import com.xgen.cloud.services.payments.proto.PartialTaxRefundPaymentResponse;
import com.xgen.cloud.services.payments.proto.RefundProcessingServiceGrpc;
import io.grpc.stub.StreamObserver;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.concurrent.CompletableFuture;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public final class RefundProcessingServiceImpl
    extends RefundProcessingServiceGrpc.RefundProcessingServiceImplBase {

  private static final Logger LOG = LoggerFactory.getLogger(RefundProcessingServiceImpl.class);

  private final RefundPaymentSvc refundPaymentSvc;
  private final RefundRequestFactory refundRequestFactory;
  private final PendingReversalCancellationSvc pendingReversalCancellationSvc;

  @Inject
  public RefundProcessingServiceImpl(
      RefundPaymentSvc refundPaymentSvc,
      RefundRequestFactory refundRequestFactory,
      PendingReversalCancellationSvc pendingReversalCancellationSvc) {
    this.refundPaymentSvc = refundPaymentSvc;
    this.refundRequestFactory = refundRequestFactory;
    this.pendingReversalCancellationSvc = pendingReversalCancellationSvc;
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.RefundProcessingClient#fullRefundInvoice
   *     RefundProcessingClient#fullRefundInvoice
   */
  @Override
  public void fullRefundInvoice(
      FullRefundInvoiceRequest request,
      StreamObserver<FullRefundInvoiceResponse> responseObserver) {
    try {
      RefundPaymentResult result =
          refundPaymentSvc.refundPaymentsOnInvoice(
              new ObjectId(request.getInvoiceId()),
              RefundType.FULL,
              request.getRefundReason(),
              RevenueRefundReasonGrpcConverter.fromGrpc(request.getRevenueRefundReason()),
              requireNonNull(EVENT_SOURCE_CONTEXT_KEY.get()));

      responseObserver.onNext(
          FullRefundInvoiceResponse.newBuilder().setResult(result.toGrpcResponse()).build());
      responseObserver.onCompleted();
    } catch (Exception ex) {
      LOG.error(
          "Unexpected exception while doing FULL refund for invoice. {}",
          kv("invoiceId", request.getInvoiceId()),
          ex);
      responseObserver.onError(ex);
    }
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.RefundProcessingClient#fullTaxRefundInvoice
   *     RefundProcessingClient#fullTaxRefundInvoice
   */
  @Override
  public void fullTaxRefundInvoice(
      FullTaxRefundInvoiceRequest request,
      StreamObserver<FullTaxRefundInvoiceResponse> responseObserver) {
    try {
      RefundPaymentResult result =
          refundPaymentSvc.refundPaymentsOnInvoice(
              new ObjectId(request.getInvoiceId()),
              RefundType.FULL_TAX,
              request.getRefundReason(),
              null,
              requireNonNull(EVENT_SOURCE_CONTEXT_KEY.get()));

      responseObserver.onNext(
          FullTaxRefundInvoiceResponse.newBuilder().setResult(result.toGrpcResponse()).build());
      responseObserver.onCompleted();
    } catch (Exception ex) {
      LOG.error(
          "Unexpected exception while doing FULL_TAX tax refund for invoice. {}",
          kv("invoiceId", request.getInvoiceId()),
          ex);
      responseObserver.onError(ex);
    }
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.RefundProcessingClient#fullRefundPayment
   *     RefundProcessingClient#fullRefundPayment
   */
  @Override
  public void fullRefundPayment(
      FullRefundPaymentRequest request,
      StreamObserver<FullRefundPaymentResponse> responseObserver) {
    try {
      RefundRequest refundRequest =
          refundRequestFactory.fullRefund(
              new ObjectId(request.getPaymentId()),
              request.getRefundReason(),
              RevenueRefundReasonGrpcConverter.fromGrpc(request.getRevenueRefundReason()));
      RefundPaymentResult result =
          refundPaymentSvc.refundPayment(
              refundRequest, requireNonNull(EVENT_SOURCE_CONTEXT_KEY.get()));

      responseObserver.onNext(
          FullRefundPaymentResponse.newBuilder().setResult(result.toGrpcResponse()).build());
      responseObserver.onCompleted();
    } catch (Exception ex) {
      LOG.error(
          "Unexpected exception while doing FULL refund for payment. {}",
          kv("paymentId", request.getPaymentId()),
          ex);
      responseObserver.onError(ex);
    }
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.RefundProcessingClient#fullRefundPaymentWithoutForgivingCharges
   *     RefundProcessingClient#fullRefundPaymentWithoutForgivingCharges
   */
  @Override
  public void fullRefundPaymentWithoutForgivingCharges(
      FullRefundPaymentWithoutForgivingChargesRequest request,
      StreamObserver<FullRefundPaymentWithoutForgivingChargesResponse> responseObserver) {
    try {
      RefundRequest refundRequest =
          refundRequestFactory.fullRefundWithoutForgivingCharges(
              new ObjectId(request.getPaymentId()),
              request.getRefundReason(),
              RevenueRefundReasonGrpcConverter.fromGrpc(request.getRevenueRefundReason()));
      RefundPaymentResult result =
          refundPaymentSvc.refundPayment(
              refundRequest, requireNonNull(EVENT_SOURCE_CONTEXT_KEY.get()));

      responseObserver.onNext(
          FullRefundPaymentWithoutForgivingChargesResponse.newBuilder()
              .setResult(result.toGrpcResponse())
              .build());
      responseObserver.onCompleted();
    } catch (Exception ex) {
      LOG.error(
          "Unexpected exception while doing FULL refund for payment without forgiving charges. {}",
          kv("paymentId", request.getPaymentId()),
          ex);
      responseObserver.onError(ex);
    }
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.RefundProcessingClient#partialRefundPayment
   *     RefundProcessingClient#partialRefundPayment
   */
  @Override
  public void partialRefundPayment(
      PartialRefundPaymentRequest request,
      StreamObserver<PartialRefundPaymentResponse> responseObserver) {
    try {
      RefundRequest refundRequest =
          refundRequestFactory.partialRefund(
              new ObjectId(request.getPaymentId()),
              request.getPartialRefundAmountCents(),
              request.getRefundReason(),
              RevenueRefundReasonGrpcConverter.fromGrpc(request.getRevenueRefundReason()));
      RefundPaymentResult result =
          refundPaymentSvc.refundPayment(
              refundRequest, requireNonNull(EVENT_SOURCE_CONTEXT_KEY.get()));

      responseObserver.onNext(
          PartialRefundPaymentResponse.newBuilder().setResult(result.toGrpcResponse()).build());
      responseObserver.onCompleted();
    } catch (Exception ex) {
      LOG.error(
          "Unexpected exception while doing PARTIAL refund for payment. {}",
          kv("paymentId", request.getPaymentId()),
          ex);
      responseObserver.onError(ex);
    }
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.RefundProcessingClient#partialTaxRefundPayment
   *     RefundProcessingClient#partialTaxRefundPayment
   */
  @Override
  public void partialTaxRefundPayment(
      PartialTaxRefundPaymentRequest request,
      StreamObserver<PartialTaxRefundPaymentResponse> responseObserver) {
    try {
      RefundRequest refundRequest =
          refundRequestFactory.partialTaxRefund(
              new ObjectId(request.getPaymentId()),
              request.getTaxRefundAmountCents(),
              request.getRefundReason());
      RefundPaymentResult result =
          refundPaymentSvc.refundPayment(
              refundRequest, requireNonNull(EVENT_SOURCE_CONTEXT_KEY.get()));

      responseObserver.onNext(
          PartialTaxRefundPaymentResponse.newBuilder().setResult(result.toGrpcResponse()).build());
      responseObserver.onCompleted();
    } catch (Exception ex) {
      LOG.error(
          "Unexpected exception while doing PARTIAL refund for payment. {}",
          kv("paymentId", request.getPaymentId()),
          ex);
      responseObserver.onError(ex);
    }
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.RefundProcessingClient#fullTaxRefundPayment
   *     RefundProcessingClient#fullTaxRefundPayment
   */
  @Override
  public void fullTaxRefundPayment(
      FullTaxRefundPaymentRequest request,
      StreamObserver<FullTaxRefundPaymentResponse> responseObserver) {
    try {
      RefundRequest refundRequest =
          refundRequestFactory.fullTaxRefund(
              new ObjectId(request.getPaymentId()), request.getRefundReason());
      RefundPaymentResult result =
          refundPaymentSvc.refundPayment(
              refundRequest, requireNonNull(EVENT_SOURCE_CONTEXT_KEY.get()));

      responseObserver.onNext(
          FullTaxRefundPaymentResponse.newBuilder().setResult(result.toGrpcResponse()).build());
      responseObserver.onCompleted();
    } catch (Exception ex) {
      LOG.error(
          "Unexpected exception while doing FULL_TAX refund for payment. {}",
          kv("paymentId", request.getPaymentId()),
          ex);
      responseObserver.onError(ex);
    }
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.RefundProcessingClient#cancelOrRefundPendingReversals
   *     RefundProcessingClient#cancelOrRefundPendingReversals
   */
  @Override
  public void cancelOrRefundPendingReversals(
      Empty request, StreamObserver<Empty> responseObserver) {
    try {
      // Run async because it's a long-running operation
      CompletableFuture.runAsync(pendingReversalCancellationSvc::cancelOrRefundPendingReversals)
          .exceptionally(
              e -> {
                LOG.error("Failed to cancel or refund pending reversals", e);
                return null;
              });
      responseObserver.onNext(Empty.getDefaultInstance());
      responseObserver.onCompleted();
    } catch (Exception ex) {
      LOG.error("Unexpected exception while cancelling or refunding pending reversals", ex);
      responseObserver.onError(ex);
    }
  }
}
