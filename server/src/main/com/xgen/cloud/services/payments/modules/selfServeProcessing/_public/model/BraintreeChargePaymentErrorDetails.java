package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.payments.exception._public.braintree.BraintreeChargeException;
import com.xgen.cloud.services.payments.proto.BraintreeErrorDetails;

public class BraintreeChargePaymentErrorDetails extends ChargePaymentErrorDetails {

  private final String processorResponseCode;
  private final String responseMessage;

  public BraintreeChargePaymentErrorDetails(
      String processorResponseCode,
      String responseMessage,
      ChargePaymentErrorCode chargePaymentErrorCode,
      Object... params) {
    super(chargePaymentErrorCode, params);
    this.processorResponseCode = processorResponseCode;
    this.responseMessage = responseMessage;
  }

  public String getProcessorResponseCode() {
    return processorResponseCode;
  }

  public String getResponseMessage() {
    return responseMessage;
  }

  /** {@inheritDoc} */
  @JsonIgnore
  @Override
  public SvcException reconstructException() {
    return new BraintreeChargeException(processorResponseCode, responseMessage);
  }

  @JsonIgnore
  @Override
  public com.xgen.cloud.services.payments.proto.ChargePaymentErrorDetails toGrpcResponse() {
    com.xgen.cloud.services.payments.proto.ChargePaymentErrorDetails.Builder builder =
        super.toGrpcResponse().toBuilder();

    if (responseMessage != null) {
      BraintreeErrorDetails.Builder braintreeBuilder = BraintreeErrorDetails.newBuilder();
      braintreeBuilder.setBraintreeResponseMessage(responseMessage);

      if (processorResponseCode != null) {
        braintreeBuilder.setBraintreeErrorCode(processorResponseCode);
      }
      builder.setBraintreeErrorDetails(braintreeBuilder.build());
    }
    return builder.build();
  }
}
