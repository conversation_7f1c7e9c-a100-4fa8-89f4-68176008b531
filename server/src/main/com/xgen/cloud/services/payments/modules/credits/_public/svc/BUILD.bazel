load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "svc",
    srcs = glob(["**/*.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/billingplatform/invoice",
        "//server/src/main/com/xgen/cloud/services/payments/modules/common",
        "//server/src/main/com/xgen/cloud/services/payments/modules/credits/_private/dao",
        "//server/src/main/com/xgen/cloud/services/payments/modules/credits/_private/model",
        "//server/src/main/com/xgen/cloud/services/payments/modules/credits/_public/model",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "@maven//:io_projectreactor_reactor_core",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:org_mongodb_bson",
        "@maven//:org_mongodb_mongodb_driver_reactivestreams",
    ],
)
