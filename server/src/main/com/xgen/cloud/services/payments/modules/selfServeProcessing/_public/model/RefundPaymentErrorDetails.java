package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.stripe.exception.CardException;
import com.stripe.exception.StripeException;
import com.xgen.cloud.common.model._public.error.SvcException;
import java.util.Arrays;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class RefundPaymentErrorDetails {
  protected final RefundPaymentErrorCode errorCode;
  protected final String[] messageParams;

  public RefundPaymentErrorDetails(RefundPaymentErrorCode errorCode, Object... params) {
    this.errorCode = errorCode;
    this.messageParams = Arrays.stream(params).map(String::valueOf).toArray(String[]::new);
  }

  public static RefundPaymentErrorDetails of(StripeException stripeException) {
    if (stripeException instanceof CardException cardException) {
      return new StripeCardRefundPaymentErrorDetails(
          cardException.getCode(),
          cardException.getDeclineCode(),
          RefundPaymentErrorCode.STRIPE_CARD_EXCEPTION);
    }
    return new StripeRefundPaymentErrorDetails(
        stripeException.getCode(), RefundPaymentErrorCode.STRIPE_EXCEPTION);
  }

  @JsonProperty
  public RefundPaymentErrorCode getErrorCode() {
    return errorCode;
  }

  @JsonProperty
  public String getErrorMessage() {
    return errorCode.formatMessage((Object[]) messageParams);
  }

  @JsonIgnore
  public Object[] getMessageParams() {
    return messageParams;
  }

  @Override
  public String toString() {
    return new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
        .append("errorCode", errorCode)
        .append("messageParams", Arrays.asList(messageParams))
        .toString();
  }

  public static RefundPaymentErrorDetails fromGrpcResponse(
      com.xgen.cloud.services.payments.proto.RefundPaymentErrorDetails response) {
    String[] params = response.getMessageParamsList().toArray(new String[0]);
    Object[] paramObjects = Arrays.copyOf(params, params.length, Object[].class);
    RefundPaymentErrorCode errorCode =
        RefundPaymentErrorCode.fromGrpcResponse(response.getErrorCode());

    return switch (response.getSubtypeCase()) {
      case BRAINTREE_ERROR_DETAILS, SUBTYPE_NOT_SET ->
          new RefundPaymentErrorDetails(errorCode, paramObjects);
      case STRIPE_ERROR_DETAILS ->
          switch (response.getStripeErrorDetails().getSubtypeCase()) {
            case SUBTYPE_NOT_SET ->
                new StripeRefundPaymentErrorDetails(
                    response.getStripeErrorDetails().getStripeErrorCode(), errorCode, paramObjects);
            case STRIPE_CARD_ERROR_DETAILS ->
                new StripeCardRefundPaymentErrorDetails(
                    response.getStripeErrorDetails().getStripeErrorCode(),
                    response
                        .getStripeErrorDetails()
                        .getStripeCardErrorDetails()
                        .getCardDeclineCode(),
                    errorCode,
                    paramObjects);
          };
    };
  }

  @JsonIgnore
  public com.xgen.cloud.services.payments.proto.RefundPaymentErrorDetails toGrpcResponse() {
    return com.xgen.cloud.services.payments.proto.RefundPaymentErrorDetails.newBuilder()
        .setErrorCode(RefundPaymentErrorCode.toGrpcResponse(errorCode))
        .addAllMessageParams(Arrays.asList(messageParams))
        .build();
  }

  @JsonIgnore
  public SvcException reconstructException() {
    return new SvcException(this.getErrorCode(), this.getMessageParams());
  }
}
