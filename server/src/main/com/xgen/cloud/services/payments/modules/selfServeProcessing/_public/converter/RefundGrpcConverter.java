package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.converter;

import com.xgen.cloud.common.util._public.time.TimeUtils;
import com.xgen.svc.mms.model.billing.Refund;
import org.bson.types.ObjectId;

public class RefundGrpcConverter {
  public static com.xgen.cloud.services.payments.proto.Refund toGrpc(Refund refund) {
    com.xgen.cloud.services.payments.proto.Refund.Builder builder =
        com.xgen.cloud.services.payments.proto.Refund.newBuilder()
            .setId(refund.getId().toHexString())
            .setOrgId(refund.getOrgId().toHexString())
            .setInvoiceId(refund.getInvoiceId().toHexString())
            .setPaymentId(refund.getPaymentId().toHexString())
            .setCreated(TimeUtils.toISOString(refund.getCreated()))
            .setAmountCents(refund.getAmountCents())
            .setAmountTaxCents(refund.getAmountTaxCents());

    if (refund.getRevenueRefundReason() != null) {
      builder.setRevenueRefundReason(
          RevenueRefundReasonGrpcConverter.toGrpc(refund.getRevenueRefundReason()));
    }

    if (refund.getReason() != null) {
      builder.setReason(refund.getReason());
    }
    if (refund.getCreditId() != null) {
      builder.setCreditId(refund.getCreditId().toHexString());
    }
    if (refund.getStripeRefundId() != null) {
      builder.setStripeRefundId(refund.getStripeRefundId());
    }
    if (refund.getBraintreeRefundId() != null) {
      builder.setBraintreeRefundId(refund.getBraintreeRefundId());
    }
    return builder.build();
  }

  public static Refund fromGrpc(com.xgen.cloud.services.payments.proto.Refund refund) {
    Refund.Builder builder =
        new Refund.Builder()
            .id(new ObjectId(refund.getId()))
            .orgId(new ObjectId(refund.getOrgId()))
            .invoiceId(new ObjectId(refund.getInvoiceId()))
            .paymentId(new ObjectId(refund.getPaymentId()))
            .created(TimeUtils.fromISOString(refund.getCreated()))
            .amountCents(refund.getAmountCents())
            .amountTaxCents(refund.getAmountTaxCents());

    if (refund.hasRevenueRefundReason()) {
      builder.revRecReason(
          RevenueRefundReasonGrpcConverter.fromGrpc(refund.getRevenueRefundReason()));
    }

    if (refund.hasReason()) {
      builder.reason(refund.getReason());
    }
    if (refund.hasCreditId()) {
      builder.creditId(new ObjectId(refund.getCreditId()));
    }
    if (refund.hasStripeRefundId()) {
      builder.stripeRefundId(refund.getStripeRefundId());
    }
    if (refund.hasBraintreeRefundId()) {
      builder.braintreeRefundId(refund.getBraintreeRefundId());
    }
    return builder.build();
  }
}
