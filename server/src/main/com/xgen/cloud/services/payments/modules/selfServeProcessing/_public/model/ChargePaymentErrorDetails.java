package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.services.payments.proto.StripeErrorDetails;
import java.util.Arrays;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class ChargePaymentErrorDetails {
  protected final ChargePaymentErrorCode errorCode;
  protected final String[] messageParams;

  public ChargePaymentErrorDetails(ChargePaymentErrorCode errorCode, Object... params) {
    this.errorCode = errorCode;
    this.messageParams = Arrays.stream(params).map(String::valueOf).toArray(String[]::new);
  }

  public static ChargePaymentErrorDetails fromGrpcResponse(
      com.xgen.cloud.services.payments.proto.ChargePaymentErrorDetails response) {
    String[] params = response.getMessageParamsList().toArray(new String[0]);
    Object[] paramObjects = Arrays.copyOf(params, params.length, Object[].class);
    ChargePaymentErrorCode errorCode =
        ChargePaymentErrorCode.fromGrpcResponse(response.getErrorCode());

    return switch (response.getSubtypeCase()) {
      case BRAINTREE_ERROR_DETAILS ->
          new BraintreeChargePaymentErrorDetails(
              response.getBraintreeErrorDetails().hasBraintreeErrorCode()
                  ? response.getBraintreeErrorDetails().getBraintreeErrorCode()
                  : null,
              response.getBraintreeErrorDetails().getBraintreeResponseMessage(),
              errorCode,
              paramObjects);
      case SUBTYPE_NOT_SET -> new ChargePaymentErrorDetails(errorCode, paramObjects);
      case STRIPE_ERROR_DETAILS ->
          switch (response.getStripeErrorDetails().getSubtypeCase()) {
            case SUBTYPE_NOT_SET ->
                new StripeChargePaymentErrorDetails(
                    readStripeErrorCode(response.getStripeErrorDetails()),
                    readStripeErrorMessage(response.getStripeErrorDetails()),
                    errorCode,
                    paramObjects);
            case STRIPE_CARD_ERROR_DETAILS ->
                new StripeCardChargePaymentErrorDetails(
                    readStripeErrorCode(response.getStripeErrorDetails()),
                    readStripeErrorMessage(response.getStripeErrorDetails()),
                    response
                        .getStripeErrorDetails()
                        .getStripeCardErrorDetails()
                        .getCardDeclineCode(),
                    errorCode,
                    paramObjects);
          };
    };
  }

  private static String readStripeErrorCode(StripeErrorDetails details) {
    return details.hasStripeErrorCode() ? details.getStripeErrorCode() : null;
  }

  private static String readStripeErrorMessage(StripeErrorDetails details) {
    return details.hasStripeErrorMessage() ? details.getStripeErrorMessage() : null;
  }

  @JsonIgnore
  public com.xgen.cloud.services.payments.proto.ChargePaymentErrorDetails toGrpcResponse() {
    return com.xgen.cloud.services.payments.proto.ChargePaymentErrorDetails.newBuilder()
        .setErrorCode(ChargePaymentErrorCode.toGrpcResponse(errorCode))
        .addAllMessageParams(Arrays.asList(messageParams))
        .build();
  }

  @JsonProperty
  public ChargePaymentErrorCode getErrorCode() {
    return errorCode;
  }

  @JsonProperty
  public String getErrorMessage() {
    return errorCode.formatMessage((Object[]) messageParams);
  }

  @JsonIgnore
  public Object[] getMessageParams() {
    return messageParams;
  }

  @Override
  public String toString() {
    return new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
        .append("errorCode", errorCode)
        .append("messageParams", Arrays.asList(messageParams))
        .toString();
  }

  /**
   * Currently the UI relies on a response payload containing error details that originates from
   * uncaught SvcExceptions being handled by ApiErrorHandler. Jowever, the payments standalone
   * service no longer throws SvcExceptions anymore. To work around this until we can refactor the
   * UI code, we have logic in each implementation of ChargePaymentErrorDetails that converts this
   * into the original SvcException so behavior is preserved.
   *
   * <p>In the case of StripeSvcException the frontend expects stripeErrorCode and declineCode
   *
   * <p>In the case of BraintreeChargeException, the frontend expects a human-readable error message
   * constructed from the braintree response
   *
   * <p>For all other errors we just show the errorcode + error message w/ interpolated message
   * params.
   */
  @JsonIgnore
  public SvcException reconstructException() {
    return new SvcException(this.getErrorCode(), this.getMessageParams());
  }
}
