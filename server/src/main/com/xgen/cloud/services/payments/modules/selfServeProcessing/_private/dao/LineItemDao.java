package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao;

import com.xgen.svc.mms.model.billing.LineItem;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/** DAO interface for LineItem operations in the self-serve processing module. */
public interface LineItemDao {

  /**
   * Gets credit line items between the specified min and max line item IDs (inclusive).
   *
   * @param invoiceId The invoice ID
   * @param minLineItemId The minimum line item ID (inclusive)
   * @param maxLineItemId The maximum line item ID (inclusive)
   * @return Flux of credit line items between the specified IDs
   */
  Flux<LineItem> getCreditLineItemsBetweenLineItemIds(
      ObjectId invoiceId, ObjectId minLineItemId, ObjectId maxLineItemId);

  /**
   * Deletes a line item by its ID.
   *
   * @param lineItemId The ID of the line item to delete
   * @param reason The reason for deletion (for audit purposes)
   * @return Mono that completes when the deletion is done
   */
  Mono<Void> deleteLineItem(ObjectId lineItemId, String reason);

  /**
   * Finds a line item by its ID.
   *
   * @param lineItemId The ID of the line item to find
   * @return Mono containing the line item if found, empty if not found
   */
  Mono<LineItem> findById(ObjectId lineItemId);
}
