package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao;

import com.mongodb.reactivestreams.client.MongoClient;
import com.xgen.cloud.group._public.model.CompanyAddress;
import com.xgen.cloud.payments.common._public.dao.ReactiveBaseDao;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.types.ObjectId;
import reactor.core.publisher.Mono;

@Singleton
public class CompanyAddressDaoImpl extends ReactiveBaseDao<CompanyAddress> {

  @Inject
  public CompanyAddressDaoImpl(
      @Named(CompanyAddress.DB_NAME) MongoClient client, CodecRegistry codecRegistry) {
    super(
        client,
        CompanyAddress.DB_NAME,
        CompanyAddress.COLLECTION_NAME,
        codecRegistry,
        CompanyAddress.class);
  }

  public Mono<CompanyAddress> findById(ObjectId id) {
    return find(id);
  }
}
