package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;

import com.mongodb.client.model.Filters;
import com.mongodb.reactivestreams.client.MongoClient;
import com.xgen.cloud.payments.common._public.dao.ReactiveBaseDao;
import com.xgen.svc.mms.model.billing.LineItem;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/** Implementation of LineItemDao for self-serve processing module. */
@Singleton
public class LineItemDaoImpl extends ReactiveBaseDao<LineItem> implements LineItemDao {

  @Inject
  public LineItemDaoImpl(@Named(LineItem.DB_NAME) MongoClient client, CodecRegistry codecRegistry) {
    super(client, LineItem.DB_NAME, LineItem.COLLECTION_NAME, codecRegistry, LineItem.class);
  }

  @Override
  public Flux<LineItem> getCreditLineItemsBetweenLineItemIds(
      ObjectId invoiceId, ObjectId minLineItemId, ObjectId maxLineItemId) {
    return find(
        Filters.and(
            eq(LineItem.INVOICE_ID_FIELD, invoiceId),
            gte(ID_FIELD, minLineItemId),
            lte(ID_FIELD, maxLineItemId),
            ne(LineItem.CREDIT_ID_FIELD, null)));
  }

  @Override
  public Mono<Void> deleteLineItem(ObjectId lineItemId, String reason) {
    return deleteOneMajority(eq(ID_FIELD, lineItemId)).then();
  }

  @Override
  public Mono<LineItem> findById(ObjectId lineItemId) {
    return findOne(eq(ID_FIELD, lineItemId));
  }
}
