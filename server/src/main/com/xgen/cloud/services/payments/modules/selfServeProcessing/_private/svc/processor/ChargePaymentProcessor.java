package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.processor;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.ChargePaymentResult;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import java.util.HashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class ChargePaymentProcessor {
  static final String SELF_SERVE_PS_NOTE = "MDB Self Serve PS";
  private static final Logger LOG = LoggerFactory.getLogger(ChargePaymentProcessor.class);

  public abstract ChargePaymentResult chargePaymentMethod(
      Organization pOrganization,
      Invoice pInvoice,
      Payment pPayment,
      PaymentMethod pPaymentMethod,
      boolean pIdempotent,
      boolean pOffSession)
      throws SvcException;

  public abstract ChargePaymentResult pollProcessingPayment(
      Organization org, Payment payment, Invoice invoice) throws SvcException;

  public abstract void handleFailedLockAcquisition(Payment payment) throws SvcException;

  void logChargeFailureForAtlas(
      Organization organization, Payment payment, String chargeFailureCode) {
    HashMap<String, Object> context = new HashMap<>();
    context.put("paymentId", payment.getId());
    context.put("orgId", organization.getId());
    context.put("paymentMethodId", payment.getPaymentMethodId());
    context.put("amount", payment.getAmountBilledCents());
    context.put("reason", chargeFailureCode);
    // We want to know about failed charges for Atlas groups ASAP, so there's a Splunk alert that
    // will check for this
    LOG.warn("Atlas charge failed: {}", context);
  }
}
