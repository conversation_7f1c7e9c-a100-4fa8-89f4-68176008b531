package com.xgen.cloud.services.payments.modules.credits._private.dao;

import com.mongodb.reactivestreams.client.ClientSession;
import com.xgen.cloud.services.payments.modules.common._public.model.DateRangeSpec;
import com.xgen.cloud.services.payments.modules.common._public.model.SortSpec;
import com.xgen.cloud.services.payments.modules.credits._private.model.AppliedCredit;
import com.xgen.cloud.services.payments.modules.credits._public.model.AppliedCreditAggregate;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Set;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * Data Access Object for managing AppliedCredit records in the database.
 *
 * <p>This DAO provides reactive operations for storing and retrieving credit application records,
 * including idempotent insertion to handle duplicate requests gracefully.
 */
public interface AppliedCreditDao {

  /**
   * Applies a credit usage record with idempotent behavior.
   *
   * <p>This method ensures that duplicate credit applications are handled gracefully through
   * database-level composite indexes. Idempotency is achieved by relying on unique constraints on
   * the combination of business identifiers rather than using a separate idempotent key field.
   *
   * <p>If a record with the same combination of these fields already exists, the database
   * constraint will prevent insertion and the operation will complete successfully without
   * inserting a duplicate.
   *
   * <p>Example usage:
   *
   * <pre>{@code
   * AppliedCredit appliedCredit = AppliedCredit.builder()
   *     .orgId(orgId)
   *     .groupId(groupId)
   *     .invoiceId(invoiceId)
   *     .creditId(creditId)
   *     .billDate(billDate)
   *     .usageCents(amountCents)
   *     .build();
   *
   * return appliedCreditDao.applyAppliedCreditIdempotent(appliedCredit);
   * }</pre>
   *
   * @param appliedCredit AppliedCredit record to insert (must not be null)
   * @param session
   * @return A Mono with the number of credits applied in cents (will be empty if a duplicate is
   *     ignored)
   */
  Mono<Long> insertAppliedCredit(AppliedCredit appliedCredit, @Nullable ClientSession session);

  /**
   * Un-applies credits for the specified invoices and credits with optional filtering.
   *
   * @param invoiceIds Set of invoice IDs to filter by (empty set means no filter)
   * @param creditIds Set of credit IDs to filter by (empty set means no filter)
   * @param dateRangeSpec Optional date range filter for bill dates
   * @param hasOverCommitmentFlag Optional filter for over-commitment flag (null means no filter)
   * @param session Optional client session for transaction support
   * @param reason Optional reason for unapplying the credits (null means no reason)
   * @return A Mono containing the total number of credits unapplied in cents. Returns empty if no
   *     records were found to unapply.
   */
  Mono<Long> unApplyCredit(
      Set<ObjectId> invoiceIds,
      Set<ObjectId> creditIds,
      @Nullable DateRangeSpec dateRangeSpec,
      @Nullable Boolean hasOverCommitmentFlag,
      @Nullable ClientSession session,
      @Nullable String reason);

  /**
   * Retrieves credit usage data for the specified invoices and credits within a date range. Results
   * are aggregated by the specified grouping fields.
   *
   * @param invoiceIds Set of invoice IDs to filter by (empty set means no filter)
   * @param creditIds Set of credit IDs to filter by (empty set means no filter)
   * @param dateRangeSpec Optional date range filter for bill dates
   * @param groupingFields Fields to group the aggregation by
   * @param sortSpec Optional sorting specification
   * @param hasOverCommitmentFlag Optional filter for over-commitment flag (null means no filter)
   * @return Flux of aggregated credit usage data
   */
  Flux<AppliedCreditAggregate> findCreditUsages(
      Set<ObjectId> invoiceIds,
      Set<ObjectId> creditIds,
      @Nullable DateRangeSpec dateRangeSpec,
      Set<GroupingField> groupingFields,
      @Nullable SortSpec sortSpec,
      @Nullable Boolean hasOverCommitmentFlag);

  Mono<Long> getTotalAppliedCreditCents(
      Set<ObjectId> invoiceIds,
      Set<ObjectId> creditIds,
      @Nullable DateRangeSpec dateRangeSpec,
      @Nullable Boolean hasOverCommitmentFlag);

  /**
   * Finds all applied credits for the specified invoice ID within the given bill date range.
   *
   * <p>This method is useful for retrieving detailed credit usage information for a specific
   * invoice.
   *
   * @param invoiceId The invoice ID to find applied credits for (must not be null)
   * @param billDates The set of bill dates to filter by (must not be null)
   * @return A Flux containing all applied credits for the given invoice ID within the date range
   */
  Flux<AppliedCredit> findByInvoiceIdAndBillDateRange(ObjectId invoiceId, Set<LocalDate> billDates);

  /** Supported sorting fields for credit usage queries. */
  enum SortField {
    BILL_DATE(AppliedCredit.BILL_DATE_FIELD);

    private final String fieldName;

    SortField(String fieldName) {
      this.fieldName = fieldName;
    }

    public String getFieldName() {
      return fieldName;
    }

    public static SortField fromFieldName(String fieldName) {
      return Arrays.stream(values())
          .filter(field -> field.fieldName.equals(fieldName))
          .findFirst()
          .orElseThrow(() -> new IllegalArgumentException("Invalid sort field: " + fieldName));
    }
  }

  /** Supported grouping fields for credit usage aggregation. */
  enum GroupingField {
    /** Group by invoice ID field. */
    INVOICE_ID(AppliedCredit.INVOICE_ID_FIELD),
    /** Group by group ID field. */
    GROUP_ID(AppliedCredit.GROUP_ID_FIELD),
    /** Group by credit ID field. */
    CREDIT_ID(AppliedCredit.CREDIT_ID_FIELD),
    /** Group by over-commitment flag field. */
    OVER_COMMITMENT_FLAG(AppliedCredit.OVER_COMMITMENT_FLAG_FIELD),
    /** Group by bill date field. */
    BILL_DATE(AppliedCredit.BILL_DATE_FIELD);

    private final String fieldName;

    GroupingField(String fieldName) {
      this.fieldName = fieldName;
    }

    public String getFieldName() {
      return fieldName;
    }
  }
}
