package com.xgen.cloud.services.payments.modules.paymentMethod.runtime.grpc;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.localDateTimeOf;
import static com.xgen.cloud.event._public.interceptor.EventSourceMetadataInterceptor.EVENT_SOURCE_CONTEXT_KEY;
import static com.xgen.cloud.services.payments.proto.CreateOrUpdatePaymentMethodResponse.UpdateStatus.UPDATE_STATUS_FAILED;
import static com.xgen.cloud.services.payments.proto.CreateOrUpdatePaymentMethodResponse.UpdateStatus.UPDATE_STATUS_SUCCESSFUL;
import static java.util.Objects.requireNonNull;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.google.protobuf.Empty;
import com.xgen.cloud.services.event.proto.SourceMessage;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.converter.GrpcBillingAddressToBillingAddressConverter;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.converter.GrpcPartnerTypeToPartnerTypeConverter;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.converter.PaymentMethodErrorDetailsToGrpcConverter;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.converter.PaymentMethodToGrpcPaymentMethodConverter;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.model.FindOrCreateMpSelfServePaymentMethodResult;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.model.PaymentMethodErrorDetails;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.svc.AddOrUpdatePaymentMethodSvc;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.svc.BraintreeWebhookSvc;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.svc.PaymentMethodSvc;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.svc.StripeWebhookSvc;
import com.xgen.cloud.services.payments.proto.BillingAccount;
import com.xgen.cloud.services.payments.proto.CreateAdminOverridePaymentMethodRequest;
import com.xgen.cloud.services.payments.proto.CreateAdminOverridePaymentMethodResponse;
import com.xgen.cloud.services.payments.proto.CreateOffSessionSetupIntentRequest;
import com.xgen.cloud.services.payments.proto.CreateOffSessionSetupIntentResponse;
import com.xgen.cloud.services.payments.proto.CreateOrUpdatePaymentMethodRequest;
import com.xgen.cloud.services.payments.proto.CreateOrUpdatePaymentMethodResponse;
import com.xgen.cloud.services.payments.proto.DeactivateExpiredAdminOverridePaymentMethodsResponse;
import com.xgen.cloud.services.payments.proto.DeleteAdminOverridePaymentMethodsForOrgRequest;
import com.xgen.cloud.services.payments.proto.DeleteAdminOverridePaymentMethodsForOrgResponse;
import com.xgen.cloud.services.payments.proto.DeleteAndDetachPaymentMethodsRequest;
import com.xgen.cloud.services.payments.proto.DeleteAndDetachPaymentMethodsResponse;
import com.xgen.cloud.services.payments.proto.FindAvailableSelfServeMpPaymentMethodByOrgIdRequest;
import com.xgen.cloud.services.payments.proto.FindAvailableSelfServeMpPaymentMethodByOrgIdResponse;
import com.xgen.cloud.services.payments.proto.FindAvailableSelfServeMpPaymentMethodByPartnerIdRequest;
import com.xgen.cloud.services.payments.proto.FindAvailableSelfServeMpPaymentMethodByPartnerIdResponse;
import com.xgen.cloud.services.payments.proto.FindAwsSelfServePaymentMethodIdsByAwsSubscriptionIdRequest;
import com.xgen.cloud.services.payments.proto.FindAwsSelfServePaymentMethodIdsByAwsSubscriptionIdResponse;
import com.xgen.cloud.services.payments.proto.FindOrCreatePendingSelfServeMpPaymentMethodRequest;
import com.xgen.cloud.services.payments.proto.FindOrCreatePendingSelfServeMpPaymentMethodResponse;
import com.xgen.cloud.services.payments.proto.GetActivePaymentMethodRequest;
import com.xgen.cloud.services.payments.proto.GetActivePaymentMethodResponse;
import com.xgen.cloud.services.payments.proto.GetAllPaymentMethodsByOrgIdRequest;
import com.xgen.cloud.services.payments.proto.GetAllPaymentMethodsByOrgIdResponse;
import com.xgen.cloud.services.payments.proto.GetLatestSelfServeMpPaymentMethodByOrgIdRequest;
import com.xgen.cloud.services.payments.proto.GetLatestSelfServeMpPaymentMethodByOrgIdResponse;
import com.xgen.cloud.services.payments.proto.GetMostRecentBeforeOrEqualToDateRequest;
import com.xgen.cloud.services.payments.proto.GetMostRecentBeforeOrEqualToDateResponse;
import com.xgen.cloud.services.payments.proto.GetPaymentMethodByCreditIdRequest;
import com.xgen.cloud.services.payments.proto.GetPaymentMethodByCreditIdResponse;
import com.xgen.cloud.services.payments.proto.GetPaymentMethodByIdRequest;
import com.xgen.cloud.services.payments.proto.GetPaymentMethodByIdResponse;
import com.xgen.cloud.services.payments.proto.HandleBraintreeCustomerWebhookEventRequest;
import com.xgen.cloud.services.payments.proto.HandleBraintreeCustomerWebhookEventResponse;
import com.xgen.cloud.services.payments.proto.HandleStripeCardUpdatedWebhookEventRequest;
import com.xgen.cloud.services.payments.proto.HasCreditAvailableAsPaymentMethodRequest;
import com.xgen.cloud.services.payments.proto.HasCreditAvailableAsPaymentMethodResponse;
import com.xgen.cloud.services.payments.proto.HasEffectivePaymentMethodRequest;
import com.xgen.cloud.services.payments.proto.HasEffectivePaymentMethodResponse;
import com.xgen.cloud.services.payments.proto.MarkAsDeletedRequest;
import com.xgen.cloud.services.payments.proto.MarkAsDeletedResponse;
import com.xgen.cloud.services.payments.proto.PartnerType;
import com.xgen.cloud.services.payments.proto.PaymentMethodServiceGrpc.PaymentMethodServiceImplBase;
import com.xgen.cloud.services.payments.proto.ReplaceActivePaymentMethodByOrgIdRequest;
import com.xgen.cloud.services.payments.proto.ReplaceActivePaymentMethodByOrgIdResponse;
import com.xgen.cloud.services.payments.proto.UpdateBillingAddressRequest;
import com.xgen.cloud.services.payments.proto.UpdateBillingAddressResponse;
import com.xgen.cloud.services.payments.proto.UpdateCreditIdRequest;
import com.xgen.cloud.services.payments.proto.UpdateCreditIdResponse;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Clock;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

@Singleton
public class PaymentMethodServiceImpl extends PaymentMethodServiceImplBase {

  private static final Logger LOG = LoggerFactory.getLogger(PaymentMethodServiceImpl.class);
  private final PaymentMethodSvc paymentMethodSvc;
  private final PaymentMethodToGrpcPaymentMethodConverter converter;
  private final GrpcBillingAddressToBillingAddressConverter billingAddressConverter;
  private final GrpcPartnerTypeToPartnerTypeConverter partnerTypeConverter;
  private final StripeWebhookSvc stripeWebhookSvc;
  private final BraintreeWebhookSvc braintreeWebhookSvc;
  private final Clock clock;
  private final AddOrUpdatePaymentMethodSvc addOrUpdatePaymentMethod;
  private final PaymentMethodErrorDetailsToGrpcConverter errorDetailsConverter;

  @Inject
  public PaymentMethodServiceImpl(
      PaymentMethodSvc paymentMethodSvc,
      PaymentMethodToGrpcPaymentMethodConverter converter,
      GrpcBillingAddressToBillingAddressConverter billingAddressConverter,
      GrpcPartnerTypeToPartnerTypeConverter partnerTypeConverter,
      StripeWebhookSvc stripeWebhookSvc,
      BraintreeWebhookSvc braintreeWebhookSvc,
      Clock clock,
      AddOrUpdatePaymentMethodSvc addOrUpdatePaymentMethod,
      PaymentMethodErrorDetailsToGrpcConverter errorDetailsConverter) {
    this.paymentMethodSvc = paymentMethodSvc;
    this.converter = converter;
    this.billingAddressConverter = billingAddressConverter;
    this.stripeWebhookSvc = stripeWebhookSvc;
    this.partnerTypeConverter = partnerTypeConverter;
    this.braintreeWebhookSvc = braintreeWebhookSvc;
    this.clock = clock;
    this.addOrUpdatePaymentMethod = addOrUpdatePaymentMethod;
    this.errorDetailsConverter = errorDetailsConverter;
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#findById
   *     PaymentMethodClient#findById
   */
  @Override
  public void getPaymentMethodById(
      GetPaymentMethodByIdRequest request,
      StreamObserver<GetPaymentMethodByIdResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .findById(new ObjectId(request.getId()))
        .map(converter::convert)
        .map(p -> GetPaymentMethodByIdResponse.newBuilder().setPaymentMethod(p).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while getting active payment method. {}",
                    kv("id", request.getId()),
                    e))
        .switchIfEmpty(Mono.error(Status.NOT_FOUND.asRuntimeException()))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#findOrCreatePendingSelfServeMpPaymentMethod
   *     PaymentMethodClient#findOrCreatePendingSelfServeMpPaymentMethod
   */
  @Override
  public void findOrCreatePendingSelfServeMpPaymentMethod(
      FindOrCreatePendingSelfServeMpPaymentMethodRequest request,
      StreamObserver<FindOrCreatePendingSelfServeMpPaymentMethodResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .findOrCreatePendingSelfServeMpPaymentMethod(
            new ObjectId(request.getOrgId()),
            partnerTypeConverter.convert(request.getPartnerType()),
            request.getPartnerId())
        .map(FindOrCreateMpSelfServePaymentMethodResult::toGrpc)
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while finding or creating Mp self serve payment method"
                        + " {}",
                    kv("orgId", request.getOrgId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#getActivePaymentMethod
   *     PaymentMethodClient#getActivePaymentMethod
   */
  @Override
  public void getActivePaymentMethod(
      GetActivePaymentMethodRequest request,
      StreamObserver<GetActivePaymentMethodResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .getActivePaymentMethod(new ObjectId(request.getOrgId()), request.getUseEffectiveOrgId())
        .map(converter::convert)
        .map(p -> GetActivePaymentMethodResponse.newBuilder().setPaymentMethod(p).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while getting active payment method. {}",
                    kv("orgId", request.getOrgId()),
                    e))
        .switchIfEmpty(Mono.error(Status.NOT_FOUND.asRuntimeException()))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#markAsDeleted
   *     PaymentMethodClient#markAsDeleted
   */
  @Override
  public void markAsDeleted(
      MarkAsDeletedRequest request, StreamObserver<MarkAsDeletedResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .markDeleted(new ObjectId(request.getPaymentMethodId()), new Date())
        .map(b -> MarkAsDeletedResponse.newBuilder().setUpdated(b).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while marking payment method as deleted. {}",
                    kv("paymentMethodId", request.getPaymentMethodId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#getAllPaymentMethodsForOrg
   *     PaymentMethodClient#getAllPaymentMethodsForOrg
   */
  @Override
  public void getAllPaymentMethodsByOrgId(
      GetAllPaymentMethodsByOrgIdRequest request,
      StreamObserver<GetAllPaymentMethodsByOrgIdResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .getAllPaymentMethods(
            new ObjectId(request.getOrgId()), request.getUseEffectivePayingOrgId())
        .collectList()
        .map(
            paymentMethods ->
                GetAllPaymentMethodsByOrgIdResponse.newBuilder()
                    .addAllPaymentMethods(paymentMethods.stream().map(converter::convert).toList())
                    .build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while getting all payment methods. {}",
                    kv("orgId", request.getOrgId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  private void validateRequest(GetAllPaymentMethodsByOrgIdRequest request) {
    if (request.getOrgId().isBlank() || !ObjectId.isValid(request.getOrgId())) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription("Please provide a valid ObjectId orgId"));
    }
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#createAdminOverridePaymentMethod
   *     PaymentMethodClient#createAdminOverridePaymentMethod
   */
  @Override
  public void createAdminOverridePaymentMethod(
      CreateAdminOverridePaymentMethodRequest request,
      StreamObserver<CreateAdminOverridePaymentMethodResponse> responseObserver) {
    validateRequest(request);
    SourceMessage source = requireNonNull(EVENT_SOURCE_CONTEXT_KEY.get());

    paymentMethodSvc
        .createAdminOverridePaymentMethod(
            new ObjectId(request.getOrgId()),
            localDateTimeOf(request.getExpirationDate()),
            LocalDateTime.now(),
            source)
        .map(b -> CreateAdminOverridePaymentMethodResponse.newBuilder().setCreated(b).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while creating admin override payment method. {}",
                    kv("orgId", request.getOrgId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  private void validateRequest(CreateAdminOverridePaymentMethodRequest request) {
    if (request.getOrgId().isBlank()
        || !ObjectId.isValid(request.getOrgId())
        || !request.hasExpirationDate()) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Please provide a valid ObjectId orgId and expiration date"));
    }
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#findLatestPartnerPaymentMethodByOrgId
   *     PaymentMethodClient#findLatestPartnerPaymentMethodByOrgId
   */
  @Override
  public void getLatestSelfServeMpPaymentMethodByOrgId(
      GetLatestSelfServeMpPaymentMethodByOrgIdRequest request,
      StreamObserver<GetLatestSelfServeMpPaymentMethodByOrgIdResponse> responseObserver) {
    validateRequest(request);

    Mono<PaymentMethod> paymentMethodMono =
        request.getPartnerType() == PartnerType.PARTNER_TYPE_UNSPECIFIED
            ? paymentMethodSvc.getLatestSelfServeMpPaymentMethodByOrgId(
                new ObjectId(request.getOrgId()))
            : paymentMethodSvc.getLatestSelfServeMpPaymentMethodByOrgId(
                new ObjectId(request.getOrgId()),
                partnerTypeConverter.convert(request.getPartnerType()));

    paymentMethodMono
        .map(converter::convert)
        .map(
            p ->
                GetLatestSelfServeMpPaymentMethodByOrgIdResponse.newBuilder()
                    .setPaymentMethod(p)
                    .build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while finding payment method. {}",
                    kv("orgId", request.getOrgId()),
                    e))
        .switchIfEmpty(Mono.error(Status.NOT_FOUND.asRuntimeException()))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#replaceActivePaymentMethodByOrgId
   *     PaymentMethodClient#replaceActivePaymentMethodByOrgId
   */
  @Override
  public void replaceActivePaymentMethodByOrgId(
      ReplaceActivePaymentMethodByOrgIdRequest request,
      StreamObserver<ReplaceActivePaymentMethodByOrgIdResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .replaceActiveByOrgId(
            new ObjectId(request.getNewPaymentMethodId()), new ObjectId(request.getOrgId()))
        .map(b -> ReplaceActivePaymentMethodByOrgIdResponse.newBuilder().setUpdated(b).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while replacing active payment method. {} {}",
                    kv("newPaymentMethodId", request.getNewPaymentMethodId()),
                    kv("orgId", request.getOrgId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#getPaymentMethodByCreditId
   *     PaymentMethodClient#getPaymentMethodByCreditId
   */
  @Override
  public void getPaymentMethodByCreditId(
      GetPaymentMethodByCreditIdRequest request,
      StreamObserver<GetPaymentMethodByCreditIdResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .findByCreditId(new ObjectId(request.getCreditId()))
        .map(converter::convert)
        .map(p -> GetPaymentMethodByCreditIdResponse.newBuilder().setPaymentMethod(p).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while getting payment method. {}",
                    kv("creditId", request.getCreditId()),
                    e))
        .switchIfEmpty(Mono.error(Status.NOT_FOUND.asRuntimeException()))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#hasCreditAvailableAsPaymentMethod
   *     PaymentMethodClient#hasCreditAvailableAsPaymentMethod
   */
  @Override
  public void hasCreditAvailableAsPaymentMethod(
      HasCreditAvailableAsPaymentMethodRequest request,
      StreamObserver<HasCreditAvailableAsPaymentMethodResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .hasCreditAvailableAsPaymentMethod(
            new ObjectId(request.getOrgId()), LocalDateTime.parse(request.getNow()))
        .map(
            b ->
                HasCreditAvailableAsPaymentMethodResponse.newBuilder()
                    .setCreditIsAvailableAsPaymentMethod(b)
                    .build())
        .doOnError(
            e ->
                LOG.error(
                    "unexpected error while checking if org has credit available as a payment"
                        + " method. {}",
                    kv("orgId", request.getOrgId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#updateCreditId
   *     PaymentMethodClient#updateCreditId
   */
  @Override
  public void updateCreditId(
      UpdateCreditIdRequest request, StreamObserver<UpdateCreditIdResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .updateCreditId(
            new ObjectId(request.getPaymentMethodId()), new ObjectId(request.getCreditId()))
        .map(b -> UpdateCreditIdResponse.newBuilder().setPaymentMethodUpdated(b).build())
        .doOnError(
            e ->
                LOG.error(
                    "unexpected exception while updating the credit id. {} {}",
                    kv("creditId", request.getCreditId()),
                    kv("paymentMethodId", request.getPaymentMethodId())))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#deleteAndDetachPaymentMethods
   *     PaymentMethodClient#deleteAndDetachPaymentMethods
   */
  @Override
  public void deleteAndDetachPaymentMethods(
      DeleteAndDetachPaymentMethodsRequest request,
      StreamObserver<DeleteAndDetachPaymentMethodsResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .deleteAndDetachPaymentMethods(
            new ObjectId(request.getOrgId()), LocalDateTime.parse(request.getNow()))
        .map(b -> DeleteAndDetachPaymentMethodsResponse.newBuilder().setSuccessful(b).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while deleting and detaching payment methods from org."
                        + " {}",
                    kv("orgId", request.getOrgId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#getMostRecentBeforeOrEqualToDate
   *     PaymentMethodClient#getMostRecentBeforeOrEqualToDate
   */
  @Override
  public void getMostRecentBeforeOrEqualToDate(
      GetMostRecentBeforeOrEqualToDateRequest request,
      StreamObserver<GetMostRecentBeforeOrEqualToDateResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .getMostRecentBeforeOrEqualToDate(
            new ObjectId(request.getOrgId()), LocalDateTime.parse(request.getDate()))
        .map(converter::convert)
        .map(p -> GetMostRecentBeforeOrEqualToDateResponse.newBuilder().setPaymentMethod(p).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while getting most recent payment method. {} {}",
                    kv("orgId", request.getOrgId()),
                    kv("date", request.getDate()),
                    e))
        .switchIfEmpty(Mono.error(Status.NOT_FOUND.asRuntimeException()))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  private void validateRequest(GetMostRecentBeforeOrEqualToDateRequest req) {
    if (req.getOrgId().isBlank() || !ObjectId.isValid(req.getOrgId()) || req.getDate().isBlank()) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription("Provide a valid orgId and date in ISO format."));
    }
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#updateBillingAddress
   *     PaymentMethodClient#updateBillingAddress
   */
  @Override
  public void updateBillingAddress(
      UpdateBillingAddressRequest request,
      StreamObserver<UpdateBillingAddressResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .updateBillingAddress(
            new ObjectId(request.getPaymentMethodId()),
            billingAddressConverter.convert(request.getBillingAddress()))
        .map(b -> UpdateBillingAddressResponse.newBuilder().setUpdated(b).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while updating payment method billing address. {}",
                    kv("paymentMethodId", request.getPaymentMethodId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#findAwsSelfServePaymentMethodIdsByAwsSubscriptionId
   *     PaymentMethodClient#findAwsSelfServePaymentMethodIdsByAwsSubscriptionId
   */
  @Override
  public void findAwsSelfServePaymentMethodIdsByAwsSubscriptionId(
      FindAwsSelfServePaymentMethodIdsByAwsSubscriptionIdRequest request,
      StreamObserver<FindAwsSelfServePaymentMethodIdsByAwsSubscriptionIdResponse>
          responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .findAwsSelfServePaymentMethodIdsByAwsSubscriptionId(
            new ObjectId(request.getAwsSubscriptionId()))
        .collectList()
        .map(
            ids ->
                FindAwsSelfServePaymentMethodIdsByAwsSubscriptionIdResponse.newBuilder()
                    .addAllPaymentMethodIds(ids.stream().map(ObjectId::toHexString).toList())
                    .build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while finding aws self serve payment method ids. {}",
                    kv("awsSubscriptionId", request.getAwsSubscriptionId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#deleteAdminOverridePaymentMethodsForOrg
   *     PaymentMethodClient#deleteAdminOverridePaymentMethodsForOrg
   */
  @Override
  public void deleteAdminOverridePaymentMethodsForOrg(
      DeleteAdminOverridePaymentMethodsForOrgRequest request,
      StreamObserver<DeleteAdminOverridePaymentMethodsForOrgResponse> responseObserver) {

    validateRequest(request);
    paymentMethodSvc
        .deleteAdminOverridePaymentMethodsForOrg(
            new ObjectId(request.getOrgId()), LocalDateTime.now())
        .map(
            result ->
                DeleteAdminOverridePaymentMethodsForOrgResponse.newBuilder()
                    .setDeleted(result)
                    .build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while deleting admin override payment methods for org."
                        + " {}",
                    kv("orgId", request.getOrgId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#deactivateExpiredAdminOverridePaymentMethods
   *     PaymentMethodClient#deactivateExpiredAdminOverridePaymentMethods
   */
  @Override
  public void deactivateExpiredAdminOverridePaymentMethods(
      com.google.protobuf.Empty request,
      StreamObserver<DeactivateExpiredAdminOverridePaymentMethodsResponse> responseObserver) {
    paymentMethodSvc
        .deactivateExpiredAdminOverridePaymentMethods(
            LocalDateTime.ofInstant(clock.instant(), ZoneId.systemDefault()))
        .map(
            result ->
                DeactivateExpiredAdminOverridePaymentMethodsResponse.newBuilder()
                    .setSuccessful(result)
                    .build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while deactivating expired admin override payment"
                        + " methods.",
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#handleStripeCardUpdatedWebhookEvent
   *     PaymentMethodClient#handleStripeCardUpdatedWebhookEvent
   */
  @Override
  public void handleStripeCardUpdatedWebhookEvent(
      HandleStripeCardUpdatedWebhookEventRequest request, StreamObserver<Empty> responseObserver) {
    validateRequest(request);

    stripeWebhookSvc
        .handleStripeCardUpdatedWebhookEvent(
            getBillingAccount(request.getBillingAccount()),
            request.getWebhookEventSignature(),
            request.getWebhookEventPayload())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while handling stripe card updated webhook event. {} {}"
                        + " {}",
                    kv("signature", request.getWebhookEventSignature()),
                    kv("payload", request.getWebhookEventPayload()),
                    kv("billingAccount", request.getBillingAccount()),
                    e))
        .doOnSuccess(
            ignored -> {
              responseObserver.onNext(Empty.getDefaultInstance());
              responseObserver.onCompleted();
            })
        .subscribe(ignored -> {}, responseObserver::onError);
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#hasEffectivePaymentMethod
   *     PaymentMethodClient#hasEffectivePaymentMethod
   */
  @Override
  public void hasEffectivePaymentMethod(
      HasEffectivePaymentMethodRequest request,
      StreamObserver<HasEffectivePaymentMethodResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .hasEffectivePaymentMethod(new ObjectId(request.getOrgId()), LocalDateTime.now())
        .map(
            b ->
                HasEffectivePaymentMethodResponse.newBuilder()
                    .setHasEffectivePaymentMethod(b)
                    .build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while checking if org has effective payment method. {}",
                    kv("orgId", request.getOrgId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#createOffSessionSetupIntent
   *     PaymentMethodClient#createOffSessionSetupIntent
   */
  @Override
  public void createOffSessionSetupIntent(
      CreateOffSessionSetupIntentRequest request,
      StreamObserver<CreateOffSessionSetupIntentResponse> responseObserver) {
    com.xgen.svc.mms.model.billing.BillingAccount billingAccount =
        getBillingAccount(request.getBillingAccount());

    paymentMethodSvc
        .createStripeOffSessionSetupIntent(billingAccount, LocalDateTime.now())
        .map(
            clientSecret ->
                CreateOffSessionSetupIntentResponse.newBuilder()
                    .setStripeSetupClientSecret(clientSecret)
                    .build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while creating off session setup intent. {}",
                    kv("billingAccount", request.getBillingAccount()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  private void validateRequest(DeleteAdminOverridePaymentMethodsForOrgRequest request) {
    if (request.getOrgId().isBlank() || !ObjectId.isValid(request.getOrgId())) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription("Provide a valid orgId"));
    }
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#findAvailableSelfServeMpPaymentMethodByOrgId
   *     PaymentMethodClient#findAvailableSelfServeMpPaymentMethodByOrgId
   */
  @Override
  public void findAvailableSelfServeMpPaymentMethodByOrgId(
      FindAvailableSelfServeMpPaymentMethodByOrgIdRequest request,
      StreamObserver<FindAvailableSelfServeMpPaymentMethodByOrgIdResponse> responseObserver) {
    validateRequest(request);

    Mono<PaymentMethod> paymentMethodMono =
        request.getPartnerType() == PartnerType.PARTNER_TYPE_UNSPECIFIED
            ? paymentMethodSvc.findAvailableSelfServeMpPaymentMethod(
                new ObjectId(request.getOrgId()))
            : paymentMethodSvc.findAvailableSelfServeMpPaymentMethod(
                new ObjectId(request.getOrgId()),
                partnerTypeConverter.convert(request.getPartnerType()));

    paymentMethodMono
        .map(
            paymentMethod ->
                FindAvailableSelfServeMpPaymentMethodByOrgIdResponse.newBuilder()
                    .setPaymentMethod(converter.convert(paymentMethod))
                    .build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while fetching available selfserve marketplace payment"
                        + " method by OrgId. {} {}",
                    kv("orgId", request.getOrgId()),
                    kv("partnerType", request.getPartnerType()),
                    e))
        .switchIfEmpty(Mono.error(Status.NOT_FOUND.asRuntimeException()))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#findAvailableSelfServeMpPaymentMethodByPartnerId
   *     PaymentMethodClient#findAvailableSelfServeMpPaymentMethodByPartnerId
   */
  @Override
  public void findAvailableSelfServeMpPaymentMethodByPartnerId(
      FindAvailableSelfServeMpPaymentMethodByPartnerIdRequest request,
      StreamObserver<FindAvailableSelfServeMpPaymentMethodByPartnerIdResponse> responseObserver) {
    validateRequest(request);

    paymentMethodSvc
        .findAvailableSelfServeMpPaymentMethod(
            partnerTypeConverter.convert(request.getPartnerType()), request.getPartnerId())
        .map(
            paymentMethod ->
                FindAvailableSelfServeMpPaymentMethodByPartnerIdResponse.newBuilder()
                    .setPaymentMethod(converter.convert(paymentMethod))
                    .build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while fetching available selfserve marketplace payment"
                        + " method by partnerId. {} {}",
                    kv("partnerId", request.getPartnerId()),
                    kv("partnerType", request.getPartnerType()),
                    e))
        .switchIfEmpty(Mono.error(Status.NOT_FOUND.asRuntimeException()))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#handleBraintreeWebhookEvent
   *     PaymentMethodClient#handleBraintreeWebhookEvent
   */
  @Override
  public void handleBraintreeCustomerWebhookEvent(
      HandleBraintreeCustomerWebhookEventRequest request,
      StreamObserver<HandleBraintreeCustomerWebhookEventResponse> responseObserver) {
    validateRequest(request);

    braintreeWebhookSvc
        .handleBraintreeCustomerWebhookEvent(
            request.getWebhookEventPayload(),
            request.getWebhookEventSignature(),
            LocalDateTime.now())
        .map(
            orgId ->
                HandleBraintreeCustomerWebhookEventResponse.newBuilder()
                    .setOrgId(orgId.toString())
                    .setDeactivated(true)
                    .build())
        .switchIfEmpty(
            Mono.just(
                HandleBraintreeCustomerWebhookEventResponse.newBuilder()
                    .setDeactivated(false)
                    .build()))
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while handling Braintree customer webhook event. {} {}",
                    kv("payload", request.getWebhookEventPayload()),
                    kv("signature", request.getWebhookEventSignature()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see
   *     com.xgen.cloud.payments.grpc._public.client.PaymentMethodClient#createOrUpdatePaymentMethod
   *     PaymentMethodClient#createOrUpdatePaymentMethod
   */
  @Override
  public void createOrUpdatePaymentMethod(
      CreateOrUpdatePaymentMethodRequest request,
      StreamObserver<CreateOrUpdatePaymentMethodResponse> responseObserver) {
    validateRequest(request);
    SourceMessage source = requireNonNull(EVENT_SOURCE_CONTEXT_KEY.get());

    try {
      var result =
          addOrUpdatePaymentMethod.createOrUpdatePaymentMethod(
              getBillingAccount(request.getBillingAccount()),
              new ObjectId(request.getOrgId()),
              source,
              request.getStripePaymentMethodId(),
              request.getStripeSetupIntentId(),
              request.getLinkBraintree(),
              request.hasVatNumber() ? request.getVatNumber() : null,
              request.hasBillingAddress()
                  ? billingAddressConverter.convert(request.getBillingAddress())
                  : null,
              clock,
              request.getIpAddressCountryCode(),
              request.getIsGlobalReadonly());

      if (result.isSuccessful()) {
        PaymentMethod paymentMethod = result.getPaymentMethod().orElseThrow();
        CreateOrUpdatePaymentMethodResponse response =
            CreateOrUpdatePaymentMethodResponse.newBuilder()
                .setPaymentMethod(converter.convert(paymentMethod))
                .setStatus(UPDATE_STATUS_SUCCESSFUL)
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
      } else {
        PaymentMethodErrorDetails errorDetails = result.getErrorDetails().orElseThrow();

        CreateOrUpdatePaymentMethodResponse response =
            CreateOrUpdatePaymentMethodResponse.newBuilder()
                .setStatus(UPDATE_STATUS_FAILED)
                .setErrorDetails(errorDetailsConverter.convert(errorDetails))
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
      }

    } catch (Exception e) {
      LOG.error(
          "Unexpected exception while creating or updating payment method. {} {}",
          kv("orgId", request.getOrgId()),
          kv("billingAccount", request.getBillingAccount()),
          e);

      responseObserver.onError(e);
    }
  }

  private void validateRequest(FindAvailableSelfServeMpPaymentMethodByPartnerIdRequest request) {
    if (request.getPartnerType() == PartnerType.UNRECOGNIZED
        || request.getPartnerType() == PartnerType.PARTNER_TYPE_UNSPECIFIED
        || request.getPartnerId().isBlank()) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Please provide a valid partner id and partner type"));
    }
  }

  private void validateRequest(HasEffectivePaymentMethodRequest request) {
    if (request.getOrgId().isBlank() || !ObjectId.isValid(request.getOrgId())) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription("Provide a valid orgId"));
    }
  }

  private void validateRequest(HandleStripeCardUpdatedWebhookEventRequest req) {
    if (req.getWebhookEventSignature().isBlank() || req.getWebhookEventPayload().isBlank()) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription("signature and payload must be populated"));
    }
  }

  private void validateRequest(UpdateBillingAddressRequest req) {
    if (req.getPaymentMethodId().isBlank()
        || !ObjectId.isValid(req.getPaymentMethodId())
        || !req.hasBillingAddress()) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Provide a valid payment method id and billing address"));
    }
  }

  private void validateRequest(DeleteAndDetachPaymentMethodsRequest req) {
    if (req.getOrgId().isBlank() || !ObjectId.isValid(req.getOrgId()) || req.getNow().isBlank()) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Provide a valid orgId and now local date time."));
    }
  }

  private void validateRequest(FindAwsSelfServePaymentMethodIdsByAwsSubscriptionIdRequest req) {
    if (req.getAwsSubscriptionId().isBlank() || !ObjectId.isValid(req.getAwsSubscriptionId())) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription("Provide a valid aws subscription id."));
    }
  }

  private void validateRequest(UpdateCreditIdRequest request) {
    if (request.getCreditId().isBlank() || request.getPaymentMethodId().isBlank()) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Provide a valid creditId and payment method id."));
    }
  }

  private void validateRequest(HandleBraintreeCustomerWebhookEventRequest request) {
    if (request.getWebhookEventPayload().isBlank()
        || request.getWebhookEventSignature().isBlank()) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Provide a valid payload and signature for the Braintree webhook event"));
    }
  }

  private void validateRequest(GetLatestSelfServeMpPaymentMethodByOrgIdRequest req) {
    if (req.getOrgId().isBlank()
        || !ObjectId.isValid(req.getOrgId())
        || req.getPartnerType() == PartnerType.UNRECOGNIZED) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Please provide a valid org id and valid partnerType"));
    }
  }

  private void validateRequest(GetPaymentMethodByCreditIdRequest req) {
    if (req.getCreditId().isBlank() || !ObjectId.isValid(req.getCreditId())) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription("Please provide a valid ObjectId credit id"));
    }
  }

  private void validateRequest(GetPaymentMethodByIdRequest req) {
    if (req.getId().isBlank() || !ObjectId.isValid(req.getId())) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Please provide a valid ObjectId payment method id"));
    }
  }

  private void validateRequest(ReplaceActivePaymentMethodByOrgIdRequest req) {
    if (req.getOrgId().isBlank()
        || !ObjectId.isValid(req.getOrgId())
        || req.getNewPaymentMethodId().isBlank()
        || !ObjectId.isValid(req.getNewPaymentMethodId())) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Please provide a valid paymentMethodId and orgId"));
    }
  }

  private void validateRequest(GetActivePaymentMethodRequest req) {
    if (req.getOrgId().isBlank() || !ObjectId.isValid(req.getOrgId())) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription("Please provide a valid ObjectId orgId"));
    }
  }

  private void validateRequest(FindOrCreatePendingSelfServeMpPaymentMethodRequest request) {
    if (request.getOrgId().isBlank()
        || !ObjectId.isValid(request.getOrgId())
        || request.getPartnerType() == PartnerType.UNRECOGNIZED
        || request.getPartnerType() == PartnerType.PARTNER_TYPE_UNSPECIFIED
        || request.getPartnerId().isBlank()) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Please provide a valid ObjectId orgId and a partner id and specify valid partner"
                  + " type"));
    }
  }

  private void validateRequest(HasCreditAvailableAsPaymentMethodRequest req) {
    if (req.getOrgId().isBlank() || req.getNow().isBlank()) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Please provide a valid ObjectId orgId and now timestamp"));
    }
  }

  private void validateRequest(MarkAsDeletedRequest req) {
    if (req.getPaymentMethodId().isBlank() || !ObjectId.isValid(req.getPaymentMethodId())) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription(
              "Please provide a valid ObjectId paymentMethodId"));
    }
  }

  private void validateRequest(FindAvailableSelfServeMpPaymentMethodByOrgIdRequest req) {
    if (req.getOrgId().isBlank() || !ObjectId.isValid(req.getOrgId())) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription("Please provide a valid ObjectId orgId"));
    }
  }

  private void validateRequest(CreateOrUpdatePaymentMethodRequest req) {
    if (req.getOrgId().isBlank() || !ObjectId.isValid(req.getOrgId())) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription("Please provide a valid ObjectId orgId"));
    }

    if (req.getBillingAccount() == BillingAccount.BILLING_ACCOUNT_UNSPECIFIED) {
      throw new StatusRuntimeException(
          Status.INVALID_ARGUMENT.withDescription("Please provide a valid billing account"));
    }
  }

  private com.xgen.svc.mms.model.billing.BillingAccount getBillingAccount(
      BillingAccount billingAccount) {
    return switch (billingAccount) {
      case BILLING_ACCOUNT_MONGODB_INC -> com.xgen.svc.mms.model.billing.BillingAccount.MONGODB_INC;
      case BILLING_ACCOUNT_MONGODB_LTD -> com.xgen.svc.mms.model.billing.BillingAccount.MONGODB_LTD;
      default ->
          throw new StatusRuntimeException(
              Status.INVALID_ARGUMENT.withDescription("Invalid billing account"));
    };
  }
}
