package com.xgen.cloud.services.payments.modules.credits._private.model;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;
import org.bson.codecs.pojo.annotations.BsonId;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

public class AppliedCredit {

  public static final String DB_NAME = "mmsdbbilling";
  public static final String COLLECTION_NAME = "appliedCredits";

  public static final String ID_FIELD = "_id";
  public static final String ORG_ID_FIELD = "orgId";
  public static final String GROUP_ID_FIELD = "groupId";
  public static final String INVOICE_ID_FIELD = "invoiceId";
  public static final String CREDIT_ID_FIELD = "creditId";
  public static final String BILL_DATE_FIELD = "billDate";
  public static final String APPLIED_CREDITS_CENTS_FIELD = "appliedCreditsCents";
  public static final String OVER_COMMITMENT_FLAG_FIELD = "overCommitmentFlag";
  public static final String CREATED_AT_FIELD = "createdAt";
  public static final String DELETED_AT_FIELD = "deletedAt";
  public static final String DELETED_REASON_FIELD = "deletedReason";

  @BsonId private ObjectId id;

  @BsonProperty(ORG_ID_FIELD)
  private ObjectId orgId;

  @BsonProperty(GROUP_ID_FIELD)
  private ObjectId groupId;

  @BsonProperty(INVOICE_ID_FIELD)
  private ObjectId invoiceId;

  @BsonProperty(CREDIT_ID_FIELD)
  private ObjectId creditId;

  @BsonProperty(BILL_DATE_FIELD)
  private LocalDate billDate;

  @BsonProperty(APPLIED_CREDITS_CENTS_FIELD)
  private long appliedCreditsCents;

  @BsonProperty(OVER_COMMITMENT_FLAG_FIELD)
  private boolean overCommitmentFlag;

  @BsonProperty(CREATED_AT_FIELD)
  private LocalDateTime createdAt;

  @BsonProperty(DELETED_AT_FIELD)
  private LocalDateTime deletedAt;

  @BsonProperty(DELETED_REASON_FIELD)
  private String deletedReason;

  public AppliedCredit() {}

  public AppliedCredit(Builder builder) {
    this.id = builder.id;
    this.orgId = builder.orgId;
    this.groupId = builder.groupId;
    this.invoiceId = builder.invoiceId;
    this.creditId = builder.creditId;
    this.billDate = builder.billDate;
    this.appliedCreditsCents = builder.appliedCreditsCents;
    this.overCommitmentFlag = builder.overCommitmentFlag;
    this.createdAt = builder.createdAt;
    this.deletedAt = builder.deletedAt;
    this.deletedReason = builder.deletedReason;
  }

  public ObjectId getId() {
    return id;
  }

  public ObjectId getOrgId() {
    return orgId;
  }

  public ObjectId getGroupId() {
    return groupId;
  }

  public ObjectId getInvoiceId() {
    return invoiceId;
  }

  public ObjectId getCreditId() {
    return creditId;
  }

  public LocalDate getBillDate() {
    return billDate;
  }

  public long getAppliedCreditsCents() {
    return appliedCreditsCents;
  }

  public boolean isOverCommitmentFlag() {
    return overCommitmentFlag;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public LocalDateTime getDeletedAt() {
    return deletedAt;
  }

  public String getDeletedReason() {
    return deletedReason;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    AppliedCredit that = (AppliedCredit) o;
    return appliedCreditsCents == that.appliedCreditsCents
        && overCommitmentFlag == that.overCommitmentFlag
        && Objects.equals(id, that.id)
        && Objects.equals(orgId, that.orgId)
        && Objects.equals(groupId, that.groupId)
        && Objects.equals(invoiceId, that.invoiceId)
        && Objects.equals(creditId, that.creditId)
        && Objects.equals(billDate, that.billDate)
        && Objects.equals(createdAt, that.createdAt)
        && Objects.equals(deletedAt, that.deletedAt)
        && Objects.equals(deletedReason, that.deletedReason);
  }

  @Override
  public int hashCode() {
    return Objects.hash(
        id,
        orgId,
        groupId,
        invoiceId,
        creditId,
        billDate,
        appliedCreditsCents,
        overCommitmentFlag,
        createdAt,
        deletedAt,
        deletedReason);
  }

  @Override
  public String toString() {
    return "AppliedCredit{"
        + "id="
        + id
        + ", orgId="
        + orgId
        + ", groupId="
        + groupId
        + ", invoiceId="
        + invoiceId
        + ", creditId="
        + creditId
        + ", billDate="
        + billDate
        + ", appliedCreditsCents="
        + appliedCreditsCents
        + ", overCommitmentFlag="
        + overCommitmentFlag
        + ", createdAt="
        + createdAt
        + ", deletedAt="
        + deletedAt
        + ", deletedReason='"
        + deletedReason
        + '\''
        + '}';
  }

  public static Builder builder() {
    return new Builder();
  }

  public Builder toBuilder() {
    return new Builder(this);
  }

  public static class Builder {
    private ObjectId id;
    private ObjectId orgId;
    private ObjectId groupId;
    private ObjectId invoiceId;
    private ObjectId creditId;
    private LocalDate billDate;
    private long appliedCreditsCents;
    private boolean overCommitmentFlag;
    private LocalDateTime createdAt;
    private LocalDateTime deletedAt;
    private String deletedReason;

    public Builder() {}

    public Builder(AppliedCredit appliedCredit) {
      this.id = appliedCredit.id;
      this.orgId = appliedCredit.orgId;
      this.groupId = appliedCredit.groupId;
      this.invoiceId = appliedCredit.invoiceId;
      this.creditId = appliedCredit.creditId;
      this.billDate = appliedCredit.billDate;
      this.appliedCreditsCents = appliedCredit.appliedCreditsCents;
      this.overCommitmentFlag = appliedCredit.overCommitmentFlag;
      this.createdAt = appliedCredit.createdAt;
      this.deletedAt = appliedCredit.deletedAt;
      this.deletedReason = appliedCredit.deletedReason;
    }

    public Builder id(ObjectId id) {
      this.id = id;
      return this;
    }

    public Builder orgId(ObjectId orgId) {
      this.orgId = orgId;
      return this;
    }

    public Builder groupId(ObjectId groupId) {
      this.groupId = groupId;
      return this;
    }

    public Builder invoiceId(ObjectId invoiceId) {
      this.invoiceId = invoiceId;
      return this;
    }

    public Builder creditId(ObjectId creditId) {
      this.creditId = creditId;
      return this;
    }

    public Builder billDate(LocalDate billDate) {
      this.billDate = billDate;
      return this;
    }

    public Builder appliedCreditsCents(long appliedCreditsCents) {
      this.appliedCreditsCents = appliedCreditsCents;
      return this;
    }

    public Builder overCommitmentFlag(boolean overCommitmentFlag) {
      this.overCommitmentFlag = overCommitmentFlag;
      return this;
    }

    public Builder createdAt(LocalDateTime createdAt) {
      this.createdAt = createdAt;
      return this;
    }

    public Builder deletedAt(LocalDateTime deletedAt) {
      this.deletedAt = deletedAt;
      return this;
    }

    public Builder deletedReason(String deletedReason) {
      this.deletedReason = deletedReason;
      return this;
    }

    public AppliedCredit build() {
      return new AppliedCredit(this);
    }
  }
}
