load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "guice",
    srcs = glob(["**/*.java"]),
    visibility = ["//server/src/main/com/xgen/cloud/services/payments:__subpackages__"],
    deps = [
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_private/dao",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_private/svc",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/svc",
        "@maven//:com_google_inject_guice",
    ],
)
