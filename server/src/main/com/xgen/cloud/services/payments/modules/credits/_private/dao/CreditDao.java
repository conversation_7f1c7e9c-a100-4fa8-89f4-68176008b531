package com.xgen.cloud.services.payments.modules.credits._private.dao;

import com.google.common.annotations.Beta;
import com.mongodb.reactivestreams.client.ClientSession;
import com.xgen.svc.mms.model.billing.Credit;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Set;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/** DAO interface for Credit operations. */
public interface CreditDao {

  Mono<Credit> findById(ObjectId creditId, @Nullable ClientSession session);

  /**
   * Finds credits by their IDs in a single batch operation.
   *
   * @param creditIds Set of credit IDs to fetch (duplicates are ignored)
   * @param session
   * @return A Flux of Credit objects matching the provided IDs
   */
  Flux<Credit> findCreditsByIds(Set<ObjectId> creditIds, @Nullable ClientSession session);

  /**
   * Applies the specified amount to a credit by decreasing the amount remaining.
   *
   * @param creditId
   * @param amountCents The amount to apply (positive value decreases remaining amount)
   * @param session
   * @return A Mono containing the number of credits applied in cents
   */
  Mono<Long> applyCreditAmount(
      ObjectId creditId, long amountCents, @Nullable ClientSession session);

  /**
   * Finds unapplied credits for the specified organization and bill date. Returns either credits
   * with amountRemainingCents > 0 or credits whose type accepts negative drawdown.
   *
   * @param orgId
   * @param billDate
   * @return A Flux of Credit objects matching the criteria
   */
  Flux<Credit> findAvailableCredits(ObjectId orgId, LocalDate billDate);

  /**
   * Finds credits by their activation code.
   *
   * @param activationCode
   * @return A Flux of Credit objects matching the activation code
   */
  Flux<Credit> findByActivationCode(String activationCode);

  Flux<Credit> findByOrgId(ObjectId orgId);

  /** TODO: Remove when all credit application is performed through PSS */
  @Beta
  Mono<Void> cloneAmountRemainingCentsField(Collection<ObjectId> creditIds);
}
