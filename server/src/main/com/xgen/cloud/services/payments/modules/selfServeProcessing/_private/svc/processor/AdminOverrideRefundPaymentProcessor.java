package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.processor;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.payments.salestax.calculation._public.svc.TaxTransactionSvc;
import com.xgen.cloud.payments.standalone.common._public.client.AuditClient;
import com.xgen.cloud.payments.standalone.common._public.client.RefundProcessedEventClient;
import com.xgen.cloud.services.event.proto.SourceMessage;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.PaymentDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.RefundDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentResult;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundRequest;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Clock;
import org.apache.commons.lang.NotImplementedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class AdminOverrideRefundPaymentProcessor extends RefundPaymentProcessor {
  private static final Logger LOG =
      LoggerFactory.getLogger(AdminOverrideRefundPaymentProcessor.class);

  @Inject
  public AdminOverrideRefundPaymentProcessor(
      RefundDao refundDao,
      PaymentDao paymentDao,
      TaxTransactionSvc taxTransactionSvc,
      AuditClient auditClient,
      RefundProcessedEventClient refundProcessedEventClient,
      Clock clock) {
    super(refundDao, paymentDao, taxTransactionSvc, auditClient, refundProcessedEventClient, clock);
  }

  @Override
  public RefundPaymentResult refundPayment(
      Invoice pInvoice, RefundRequest refundRequest, SourceMessage source) {
    throw new NotImplementedException(
        String.format("refundPayment method not implemented for %s", this.getClass()));
  }
}
