package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao;

import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.StripeApiError;
import com.xgen.svc.mms.model.billing.BillingAccount;
import com.xgen.svc.mms.model.billing.Payment;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;
import org.bson.types.ObjectId;

public interface PaymentDao {
  Payment findById(ObjectId id);

  Stream<Payment> findNewAndFailedAndProcessingPayments(
      int maxAttempts, int maxProcessingAttempts, Date lastRetry, Date maxCreatedAt);

  Payment findNewAndFailedAndProcessingPaymentByPaymentId(ObjectId paymentId);

  void updateFailedChargeAttempts(ObjectId paymentId, Date now);

  List<Payment> findTooManyRetriesPayments(
      int maxAttempts, int maxProcessingAttempts, ObjectId orgId);

  List<Payment> findAllPendingReversal();

  void setPaymentStatus(ObjectId id, Payment.Status paymentStatus, Date date);

  void clearPaymentMethodInfo(ObjectId paymentId, Date date);

  void saveSuccessfulStripeCharge(
      ObjectId paymentId,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      String stripePaymentIntentId,
      long amountPaidCents,
      String stripeChargeId,
      String cardLast4,
      Date cardExpiration,
      Date date);

  void saveStripeCharge(
      ObjectId paymentId,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      String stripePaymentIntentId,
      long amountPaidCents,
      String stripeChargeId,
      String cardLast4,
      Date cardExpiration,
      Payment.Status newStatus,
      Date date);

  void saveProcessingStripeCharge(
      ObjectId paymentId,
      ObjectId paymentMethodId,
      String cardLast4,
      BillingAccount billingAccount,
      String stripePaymentIntentId,
      LocalDateTime date);

  void saveFailedStripeCharge(
      ObjectId paymentId,
      Payment.Status paymentStatus,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      String stripePaymentIntentId,
      String stripePaymentIntentStatus,
      @Nullable StripeApiError stripeApiError,
      String cardLast4,
      Date cardExpiration,
      Date date);

  void updateStripeConversionRate(ObjectId paymentId, BigDecimal conversionRate);

  boolean anyNewOrFailedOrProcessingByInvoiceId(ObjectId invoiceId);

  /**
   * sets the chargeLock field to now on the payment object iff the charge lock field isn't already
   * set. returns false if we fail to set the chargeLock field, which will happen if it is already
   * set.
   */
  boolean setChargeLock(ObjectId paymentId, LocalDateTime now);

  void unsetChargeLock(ObjectId paymentId);

  void saveAdminOverrideCharge(ObjectId paymentId, ObjectId paymentMethodId);

  void setBraintreeTransactionId(ObjectId paymentId, String transactionId);

  void saveSuccessfulBraintreeCharge(
      ObjectId paymentId,
      String transactionId,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      Date date);

  void saveFailedBraintreeCharge(
      ObjectId paymentId,
      String chargeFailureMessage,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      Date date);

  void markBraintreePaymentAsPaid(String transactionId, Long amount, Date date);

  void setBraintreeRefundId(ObjectId paymentId, String refundId);

  List<Payment> findByOrgId(ObjectId orgId);

  List<Payment> findByInvoiceId(ObjectId invoiceId, boolean includeCancelled);

  void applyRefundToPayment(
      ObjectId paymentId,
      long newAmountPaidCents,
      Date date,
      Payment.Status updatedPaymentStatus,
      String refundReason);
}
