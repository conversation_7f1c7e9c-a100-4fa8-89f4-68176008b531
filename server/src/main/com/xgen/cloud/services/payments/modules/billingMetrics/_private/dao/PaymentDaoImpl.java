package com.xgen.cloud.services.payments.modules.billingMetrics._private.dao;

import static com.mongodb.client.model.Filters.eq;
import static com.xgen.svc.mms.model.billing.PaymentMethod.STRIPE_MANDATE_FIELD;

import com.mongodb.client.model.ReplaceOptions;
import com.mongodb.client.result.UpdateResult;
import com.mongodb.reactivestreams.client.MongoClient;
import com.xgen.cloud.common.util._public.util.DriverUtils;
import com.xgen.cloud.group._public.model.BillingAddress;
import com.xgen.cloud.payments.common._public.dao.ReactiveBaseDao;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.svc.mms.model.billing.FailedChargeAttempts;
import com.xgen.svc.mms.model.billing.IndiaPaymentMetricsSummary;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import com.xgen.svc.mms.model.billing.PaymentMethodMandate;
import com.xgen.svc.mms.model.billing.PaymentStripe;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public class PaymentDaoImpl extends ReactiveBaseDao<Payment> {

  @Inject
  public PaymentDaoImpl(@Named(Payment.DB_NAME) MongoClient client, CodecRegistry codecRegistry) {
    super(client, Payment.DB_NAME, Payment.COLLECTION_NAME, codecRegistry, Payment.class);
  }

  public Mono<Payment> findById(ObjectId id) {
    return findOne(eq(ID_FIELD, id));
  }

  public Mono<UpdateResult> save(Payment payment) {
    return Mono.from(
        replaceOneMajority(
            new Document(Payment.ID_FIELD, payment.getId()),
            payment,
            new ReplaceOptions().upsert(true)));
  }

  public Flux<IndiaPaymentMetricsSummary> getIndiaMetrics(
      LocalDateTime minCreatedDate, LocalDateTime maxCreatedDate) {
    /*
    {"$match": {"created": {"$gte": {"$date": "2025-02-01T00:00:00Z"}, "$lt": {"$date": "2025-03-01T00:00:00Z"}}, "paymentMethod": "CREDIT_CARD"}}
    {"$lookup": {"from": "paymentMethods", "localField": "paymentMethodId", "foreignField": "_id", "as": "pm"}}
    {"$unwind": {"path": "$pm", "preserveNullAndEmptyArrays": false}}
    {"$match": {"$or": [{"pm.billingAddress.country": "IN"}, {"stripe.conversionRate": {"$exists": true}}]}}
    {"$addFields": {"totalBilledINR": {"$multiply": ["$stripe.conversionRate", "$amountBilledCents"]}, "totalPaidINR": {"$multiply": ["$stripe.conversionRate", "$amountPaidCents"]}, "mandateRegisteredCurrency": {"$cond": [{"$gt": ["$stripe.conversionRate", null]}, true, false]}}}
    {"$addFields": {"moreThan15kInr": {"$cond": [{"$gt": ["$totalBilledINR", 1500000]}, true, false]}, "moreThan5kInr": {"$cond": [{"$gt": ["$totalBilledINR", 500000]}, true, false]}}}
    {"$group": {"_id": {"status": "$status", "failedChargeAttempts": {"$ifNull": ["$failedChargeAttempts.attempts", 0]}, "numTimesProcessing": "$numTimesTransitionedToProcessing", "moreThan15kInr": "$moreThan15kInr", "moreThan5kInr": "$moreThan5kInr", "mandateStatusPm": "$pm.stripeMandate.status", "mandateRegisteredCurrency": "$mandateRegisteredCurrency"}, "countPayments": {"$sum": 1}, "totalBilledCents": {"$sum": "$amountBilledCents"}, "totalPaidCents": {"$sum": "$amountPaidCents"}, "totalSubTotal": {"$sum": "$subtotalCents"}, "totalTax": {"$sum": "$salesTaxCents"}, "totalBilledINR": {"$sum": "$totalBilledINR"}, "totalPaidINR": {"$sum": "$totalPaidINR"}}}
    {"$addFields": {"moreThan15kInr": "$_id.moreThan15kInr", "moreThan5kInr": "$_id.moreThan5kInr", "status": "$_id.status", "mandateRegisteredCurrency": "$_id.mandateRegisteredCurrency", "mandateStatusPm": "$_id.mandateStatusPm", "failedChargeAttempts": "$_id.failedChargeAttempts", "numTimesProcessing": "$_id.numTimesProcessing"}}
    */
    Document matchStage =
        new Document(
            MATCH,
            new Document(
                    Payment.CREATED_FIELD,
                    new Document(GTE, minCreatedDate).append(LT, maxCreatedDate))
                .append(Payment.PAYMENT_METHOD_FIELD, PaymentMethodType.CREDIT_CARD.name()));

    Document lookupStagePaymentMethod =
        new Document(
            LOOKUP,
            new Document(FROM, PaymentMethod.COLLECTION_NAME)
                .append(LOCAL_FIELD, Payment.PAYMENT_METHOD_ID_FIELD)
                .append(FOREIGN_FIELD, PaymentMethod.ID_FIELD)
                .append(AS, "pm"));

    Document unwindStagePaymentMethod =
        new Document(
            UNWIND, new Document("path", "$pm").append("preserveNullAndEmptyArrays", false));

    Document matchCountryOrConversionRateStage =
        new Document(
            MATCH,
            new Document(
                OR,
                Arrays.asList(
                    new Document(
                        "pm."
                            + PaymentMethod.BILLING_ADDRESS_FIELD
                            + "."
                            + BillingAddress.COUNTRY_FIELD,
                        "IN"),
                    new Document(
                        Payment.STRIPE_FIELD + "." + PaymentStripe.CONVERSION_RATE_FIELD,
                        new Document(EXISTS, true)))));

    Document addFieldsStage =
        new Document(
            ADD_FIELDS,
            new Document(
                    IndiaPaymentMetricsSummary.TOTAL_AMOUNT_BILLED_INR_FIELD,
                    new Document(
                        "$toLong",
                        new Document(
                            MULTIPLY,
                            Arrays.asList(
                                "$"
                                    + Payment.STRIPE_FIELD
                                    + "."
                                    + PaymentStripe.CONVERSION_RATE_FIELD,
                                "$" + Payment.AMOUNT_BILLED_CENTS_FIELD))))
                .append(
                    IndiaPaymentMetricsSummary.TOTAL_AMOUNT_PAID_INR_FIELD,
                    new Document(
                        "$toLong",
                        new Document(
                            MULTIPLY,
                            Arrays.asList(
                                "$"
                                    + Payment.STRIPE_FIELD
                                    + "."
                                    + PaymentStripe.CONVERSION_RATE_FIELD,
                                "$" + Payment.AMOUNT_PAID_CENTS_FIELD))))
                .append(
                    IndiaPaymentMetricsSummary.MANDATE_REGISTERED_CURRENCY_FIELD,
                    new Document(
                        COND,
                        Arrays.asList(
                            new Document(
                                GT,
                                Arrays.asList(
                                    "$"
                                        + Payment.STRIPE_FIELD
                                        + "."
                                        + PaymentStripe.CONVERSION_RATE_FIELD,
                                    null)),
                            true,
                            false))));
    Document addFieldsStageTwo =
        new Document(
            ADD_FIELDS,
            new Document(
                    IndiaPaymentMetricsSummary.MORE_THAN_15K_INR_FIELD,
                    new Document(
                        COND,
                        Arrays.asList(
                            new Document(
                                GT,
                                Arrays.asList(
                                    "$" + IndiaPaymentMetricsSummary.TOTAL_AMOUNT_BILLED_INR_FIELD,
                                    1500000)),
                            true,
                            false)))
                .append(
                    IndiaPaymentMetricsSummary.MORE_THAN_5K_INR_FIELD,
                    new Document(
                        COND,
                        Arrays.asList(
                            new Document(
                                GT,
                                Arrays.asList(
                                    "$" + IndiaPaymentMetricsSummary.TOTAL_AMOUNT_BILLED_INR_FIELD,
                                    500000)),
                            true,
                            false))));

    Document groupStage =
        new Document(
            GROUP,
            new Document(
                    "_id",
                    new Document(Payment.STATUS_FIELD, "$" + Payment.STATUS_FIELD)
                        .append(
                            IndiaPaymentMetricsSummary.FAILED_CHARGE_ATTEMPTS_COUNT,
                            new Document(
                                "$ifNull",
                                List.of(
                                    "$"
                                        + Payment.FAILED_CHARGE_ATTEMPTS_FIELD
                                        + "."
                                        + FailedChargeAttempts.ATTEMPTS_FIELD,
                                    0)))
                        .append(
                            IndiaPaymentMetricsSummary.NUM_TIMES_PROCESSING_COUNT,
                            "$" + Payment.NUM_TIMES_TRANSITIONED_TO_PROCESSING_FIELD)
                        .append(
                            IndiaPaymentMetricsSummary.MORE_THAN_15K_INR_FIELD,
                            "$" + IndiaPaymentMetricsSummary.MORE_THAN_15K_INR_FIELD)
                        .append(
                            IndiaPaymentMetricsSummary.MORE_THAN_5K_INR_FIELD,
                            "$" + IndiaPaymentMetricsSummary.MORE_THAN_5K_INR_FIELD)
                        .append(
                            IndiaPaymentMetricsSummary.PM_MANDATE_STATUS_FIELD,
                            "$pm." + STRIPE_MANDATE_FIELD + "." + PaymentMethodMandate.STATUS_FIELD)
                        .append(
                            IndiaPaymentMetricsSummary.MANDATE_REGISTERED_CURRENCY_FIELD,
                            "$" + IndiaPaymentMetricsSummary.MANDATE_REGISTERED_CURRENCY_FIELD))
                .append(IndiaPaymentMetricsSummary.COUNT_PAYMENTS_FIELD, new Document(SUM, 1))
                .append(
                    IndiaPaymentMetricsSummary.TOTAL_AMOUNT_BILLED_CENTS_FIELD,
                    new Document(SUM, "$" + Payment.AMOUNT_BILLED_CENTS_FIELD))
                .append(
                    IndiaPaymentMetricsSummary.TOTAL_AMOUNT_PAID_CENTS_FIELD,
                    new Document(SUM, "$" + Payment.AMOUNT_PAID_CENTS_FIELD))
                .append(
                    IndiaPaymentMetricsSummary.TOTAL_SUBTOTAL_FIELD,
                    new Document(SUM, "$" + Payment.SUBTOTAL_CENTS_FIELD))
                .append(
                    IndiaPaymentMetricsSummary.TOTAL_TAX_FIELD,
                    new Document(SUM, "$" + Payment.SALES_TAX_CENTS_FIELD))
                .append(
                    IndiaPaymentMetricsSummary.TOTAL_AMOUNT_BILLED_INR_FIELD,
                    new Document(
                        SUM, "$" + IndiaPaymentMetricsSummary.TOTAL_AMOUNT_BILLED_INR_FIELD))
                .append(
                    IndiaPaymentMetricsSummary.TOTAL_AMOUNT_PAID_INR_FIELD,
                    new Document(
                        SUM, "$" + IndiaPaymentMetricsSummary.TOTAL_AMOUNT_PAID_INR_FIELD)));

    Document addFieldsFromId =
        new Document(
            ADD_FIELDS,
            new Document()
                .append(
                    IndiaPaymentMetricsSummary.MORE_THAN_15K_INR_FIELD,
                    $("_id." + IndiaPaymentMetricsSummary.MORE_THAN_15K_INR_FIELD))
                .append(
                    IndiaPaymentMetricsSummary.MORE_THAN_5K_INR_FIELD,
                    $("_id." + IndiaPaymentMetricsSummary.MORE_THAN_5K_INR_FIELD))
                .append(
                    IndiaPaymentMetricsSummary.STATUS_FIELD,
                    $("_id." + IndiaPaymentMetricsSummary.STATUS_FIELD))
                .append(
                    IndiaPaymentMetricsSummary.MANDATE_REGISTERED_CURRENCY_FIELD,
                    $("_id." + IndiaPaymentMetricsSummary.MANDATE_REGISTERED_CURRENCY_FIELD))
                .append(
                    IndiaPaymentMetricsSummary.PM_MANDATE_STATUS_FIELD,
                    $("_id." + IndiaPaymentMetricsSummary.PM_MANDATE_STATUS_FIELD))
                .append(
                    IndiaPaymentMetricsSummary.FAILED_CHARGE_ATTEMPTS_COUNT,
                    $("_id." + IndiaPaymentMetricsSummary.FAILED_CHARGE_ATTEMPTS_COUNT))
                .append(
                    IndiaPaymentMetricsSummary.NUM_TIMES_PROCESSING_COUNT,
                    $("_id." + IndiaPaymentMetricsSummary.NUM_TIMES_PROCESSING_COUNT)));
    List<Document> pipeline =
        List.of(
            matchStage,
            lookupStagePaymentMethod,
            unwindStagePaymentMethod,
            matchCountryOrConversionRateStage,
            addFieldsStage,
            addFieldsStageTwo,
            groupStage,
            addFieldsFromId);

    return aggregate(
        pipeline, IndiaPaymentMetricsSummary.class, DriverUtils.SECONDARY_PREFERRED_MINIMUM);
  }
}
