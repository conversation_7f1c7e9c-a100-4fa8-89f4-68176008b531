load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "grpc",
    srcs = glob(["**/*.java"]),
    include_default_deps = False,
    visibility = ["//server/src/main/com/xgen/cloud/services/payments:__subpackages__"],
    deps = [
        "//server/src/main/com/xgen/cloud/event",
        "//server/src/main/com/xgen/cloud/payments/processing",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/converter",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/model",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/proto:java_grpc",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/svc",
        "@com_google_protobuf//java/core",
        "@io_grpc_grpc_java//stub",
        "@maven//:io_projectreactor_reactor_core",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:net_logstash_logback_logstash_logback_encoder",
        "@maven//:org_mongodb_bson",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
