load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "dao",
    srcs = glob(["**/*.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/billingplatform/invoice",
        "//server/src/main/com/xgen/cloud/common/dao/base",
        "//server/src/main/com/xgen/cloud/common/db/mongo",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/payments/common",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/model",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//third_party:guava",
        "@maven//:io_projectreactor_reactor_core",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:org_mongodb_bson",
        "@maven//:org_mongodb_mongodb_driver_core",
        "@maven//:org_mongodb_mongodb_driver_reactivestreams",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
