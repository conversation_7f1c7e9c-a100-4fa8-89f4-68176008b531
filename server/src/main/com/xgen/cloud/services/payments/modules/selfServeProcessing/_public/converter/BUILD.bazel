load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "converter",
    srcs = glob(["**/*.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/proto:java_grpc",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "@maven//:org_mongodb_bson",
    ],
)
