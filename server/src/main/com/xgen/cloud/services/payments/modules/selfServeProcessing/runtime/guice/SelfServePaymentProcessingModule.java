package com.xgen.cloud.services.payments.modules.selfServeProcessing.runtime.guice;

import com.google.inject.AbstractModule;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.CreditDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.CreditDaoImpl;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.LineItemDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.LineItemDaoImpl;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.PaymentDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.PaymentDaoImpl;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.ChargePaymentSvcImpl;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.CreditApplicationSvc;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.CreditApplicationSvcImpl;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.PendingReversalCancellationSvcImpl;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.RefundPaymentSvcImpl;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.RefundQuerySvcImpl;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.RefundRequestFactoryImpl;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.svc.ChargePaymentSvc;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.svc.PendingReversalCancellationSvc;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.svc.RefundPaymentSvc;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.svc.RefundQuerySvc;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.svc.RefundRequestFactory;

public class SelfServePaymentProcessingModule extends AbstractModule {
  @Override
  protected void configure() {
    bind(PaymentDao.class).to(PaymentDaoImpl.class);
    bind(CreditDao.class).to(CreditDaoImpl.class);
    bind(LineItemDao.class).to(LineItemDaoImpl.class);
    bind(CreditApplicationSvc.class).to(CreditApplicationSvcImpl.class);
    bind(ChargePaymentSvc.class).to(ChargePaymentSvcImpl.class);
    bind(RefundPaymentSvc.class).to(RefundPaymentSvcImpl.class);
    bind(RefundQuerySvc.class).to(RefundQuerySvcImpl.class);
    bind(RefundRequestFactory.class).to(RefundRequestFactoryImpl.class);
    bind(PendingReversalCancellationSvc.class).to(PendingReversalCancellationSvcImpl.class);
  }
}
