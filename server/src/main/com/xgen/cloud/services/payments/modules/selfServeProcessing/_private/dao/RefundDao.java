package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao;

import static com.mongodb.client.model.Accumulators.sum;
import static com.mongodb.client.model.Aggregates.group;
import static com.mongodb.client.model.Aggregates.match;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Sorts.descending;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.cloud.common.db.mongo._public.index.MongoIndex;
import com.xgen.svc.mms.model.billing.Refund;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.ArrayList;
import java.util.List;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

@Singleton
public class RefundDao extends BaseDao<Refund> {

  @Inject
  public RefundDao(MongoClientContainer container, CodecRegistry codecRegistry) {
    super(container, Refund.DB_NAME, Refund.COLLECTION_NAME, codecRegistry);
  }

  @Override
  public List<MongoIndex> getIndexes() {
    return List.of(
        MongoIndex.builder().key(Refund.INVOICE_ID_FIELD).build(),
        MongoIndex.builder().key(Refund.PAYMENT_ID_FIELD).build(),
        MongoIndex.builder().key(Refund.CREATED_FIELD).build());
  }

  public void save(Refund refund) {
    insertReplicaSafe(refund);
  }

  public Refund findById(ObjectId id) {
    return find(id).orElse(null);
  }

  public List<Refund> findByPaymentId(ObjectId paymentId) {
    Bson filter = eq(Refund.PAYMENT_ID_FIELD, paymentId);

    return getCollection()
        .find(filter)
        .sort(descending(Refund.CREATED_FIELD))
        .into(new ArrayList<>());
  }

  public long getAmountTaxRefundedCentsByPaymentId(ObjectId paymentId) {
    Bson match = match(eq(Refund.PAYMENT_ID_FIELD, paymentId));
    Bson group =
        group(null, sum(Refund.AMOUNT_TAX_CENTS_FIELD, "$" + Refund.AMOUNT_TAX_CENTS_FIELD));

    BasicDBObject result =
        getCollection().aggregate(List.of(match, group), BasicDBObject.class).first();

    return result != null ? result.getLong(Refund.AMOUNT_TAX_CENTS_FIELD, 0) : 0;
  }

  public List<Refund> findByInvoiceId(ObjectId invoiceId) {
    Bson filter = eq(Refund.INVOICE_ID_FIELD, invoiceId);

    return getCollection()
        .find(filter)
        .sort(descending(Refund.CREATED_FIELD))
        .into(new ArrayList<>());
  }
}
