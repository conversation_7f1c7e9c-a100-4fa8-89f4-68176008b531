package com.xgen.cloud.services.payments.modules.selfServeProcessing.runtime.grpc;

import com.google.protobuf.Empty;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.ChargePaymentResult;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.svc.ChargePaymentSvc;
import com.xgen.cloud.services.payments.proto.ChargeSelfServeDirectPaymentRequest;
import com.xgen.cloud.services.payments.proto.ChargeSelfServeDirectPaymentResponse;
import com.xgen.cloud.services.payments.proto.ConfirmSelfServeDirectPaymentRequest;
import com.xgen.cloud.services.payments.proto.ConfirmSelfServeDirectPaymentResponse;
import com.xgen.cloud.services.payments.proto.PaymentProcessingServiceGrpc;
import io.grpc.stub.StreamObserver;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.concurrent.CompletableFuture;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

@Singleton
public final class PaymentProcessingServiceImpl
    extends PaymentProcessingServiceGrpc.PaymentProcessingServiceImplBase {

  private static final Logger LOG = LoggerFactory.getLogger(PaymentProcessingServiceImpl.class);

  private final ChargePaymentSvc chargePaymentSvc;

  @Inject
  public PaymentProcessingServiceImpl(ChargePaymentSvc chargePaymentSvc) {
    this.chargePaymentSvc = chargePaymentSvc;
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.PaymentProcessingClient#chargePayment
   *     PaymentProcessingClient#chargePayment
   */
  @Override
  public void chargeSelfServeDirectPayment(
      ChargeSelfServeDirectPaymentRequest request,
      StreamObserver<ChargeSelfServeDirectPaymentResponse> responseObserver) {
    Mono.fromCallable(() -> new ObjectId(request.getPaymentId()))
        .flatMap(
            paymentId ->
                chargePaymentSvc.chargePayment(
                    paymentId,
                    request.getIsOffSession(),
                    request.getIsIdempotent(),
                    request.getSkipFailedAttemptUpdate()))
        .map(ChargePaymentResult::toGrpcResponse)
        .map(r -> ChargeSelfServeDirectPaymentResponse.newBuilder().setResult(r).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while charging payment. PaymentId={}",
                    request.getPaymentId(),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.PaymentProcessingClient#chargeInvoices
   *     PaymentProcessingClient#chargeInvoices
   */
  @Override
  public void chargeSelfServeDirectInvoices(
      com.google.protobuf.Empty request,
      io.grpc.stub.StreamObserver<com.google.protobuf.Empty> responseObserver) {
    try {
      // Run async because it's a long-running operation
      CompletableFuture.runAsync(chargePaymentSvc::chargeInvoices)
          .exceptionally(
              e -> {
                LOG.error("Error charging self serve direct invoices", e);
                return null;
              });
      responseObserver.onNext(Empty.getDefaultInstance());
      responseObserver.onCompleted();
    } catch (Exception ex) {
      LOG.error("Unexpected exception while charging invoices.", ex);
      responseObserver.onError(ex);
    }
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.PaymentProcessingClient#confirmPayment
   *     PaymentProcessingClient#confirmPayment
   */
  @Override
  public void confirmSelfServeDirectPayment(
      ConfirmSelfServeDirectPaymentRequest request,
      StreamObserver<ConfirmSelfServeDirectPaymentResponse> responseObserver) {
    try {
      ObjectId paymentOid = new ObjectId(request.getPaymentId());
      ChargePaymentResult result = chargePaymentSvc.confirmPayment(paymentOid);

      responseObserver.onNext(
          ConfirmSelfServeDirectPaymentResponse.newBuilder()
              .setResult(result.toGrpcResponse())
              .build());
      responseObserver.onCompleted();
    } catch (Exception ex) {
      LOG.error(
          "Unexpected exception while confirming payment. PaymentId={}",
          request.getPaymentId(),
          ex);
      responseObserver.onError(ex);
    }
  }
}
