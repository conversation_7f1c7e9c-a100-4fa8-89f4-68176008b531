syntax = "proto3";

// leaf of the full qualified package name is used as a serviceId
package com.xgen.cloud.services.payments;

import "google/protobuf/empty.proto";
import "server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/proto/common.proto";

option java_multiple_files = true;
option java_outer_classname = "PaymentProcessingProto";
option java_package = "com.xgen.cloud.services.payments.proto";

// Based on https://developers.google.com/protocol-buffers/docs/style
service PaymentProcessingService {
  rpc ChargeSelfServeDirectPayment(ChargeSelfServeDirectPaymentRequest) returns (ChargeSelfServeDirectPaymentResponse) {}
  rpc ChargeSelfServeDirectInvoices(google.protobuf.Empty) returns (google.protobuf.Empty) {}
  rpc ConfirmSelfServeDirectPayment(ConfirmSelfServeDirectPaymentRequest) returns (ConfirmSelfServeDirectPaymentResponse) {}
}

message ChargeSelfServeDirectPaymentRequest {
  string payment_id = 1;
  bool is_off_session = 2;
  bool is_idempotent = 3;
  bool skip_failed_attempt_update = 4;
}

message ConfirmSelfServeDirectPaymentRequest {
  string payment_id = 1;
}

message ChargeSelfServeDirectPaymentResponse {
  ChargePaymentResult result = 1;
}

message ConfirmSelfServeDirectPaymentResponse {
  ChargePaymentResult result = 1;
}

message ChargePaymentResult {
  ChargePaymentStatus status = 1;
  optional ChargePaymentErrorDetails error_details = 2;
  optional PaymentAuthenticationRequest auth_request = 3;
}

message ChargePaymentErrorDetails {
  ChargePaymentErrorCode error_code = 1;
  repeated string message_params = 2;

  oneof subtype {
    StripeErrorDetails stripe_error_details = 3;
    BraintreeErrorDetails braintree_error_details = 4;
  }
}

message PaymentAuthenticationRequest {
  string stripe_payment_intent_client_secret = 1;
}

enum ChargePaymentStatus {
  CHARGE_PAYMENT_STATUS_UNSPECIFIED = 0;
  CHARGE_PAYMENT_STATUS_SUCCESSFUL = 1;
  CHARGE_PAYMENT_STATUS_PROCESSING = 2;
  CHARGE_PAYMENT_STATUS_FAILED = 3;
  CHARGE_PAYMENT_STATUS_ACTION_REQUIRED = 4;
}

enum ChargePaymentErrorCode {
  CHARGE_PAYMENT_ERROR_CODE_UNSPECIFIED = 0;
  CHARGE_PAYMENT_ERROR_CODE_PROCESSING_PAYMENTS_NOT_SUPPORTED_BY_PAYMENT_PROCESSOR = 1;
  CHARGE_PAYMENT_ERROR_CODE_NO_PAYMENT_PROCESSOR = 2;
  CHARGE_PAYMENT_ERROR_CODE_MISSING_PAYMENT_METHOD = 3;
  CHARGE_PAYMENT_ERROR_CODE_MISSING_PAYMENT_METHOD_ID_FOR_PROCESSING_PAYMENT = 4;
  CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_POLL_NON_PROCESSING_PAYMENT = 5;
  CHARGE_PAYMENT_ERROR_CODE_STRIPE_EXCEPTION = 6;
  CHARGE_PAYMENT_ERROR_CODE_STRIPE_CARD_EXCEPTION = 7;
  CHARGE_PAYMENT_ERROR_CODE_PAYPAL_EXCEPTION = 8;
  CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_CALCULATE_MANDATE_MAXIMUM = 9;
  CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_CALCULATE_SALES_TAX = 10;
  CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_LOCK_INVOICE = 11;
  CHARGE_PAYMENT_ERROR_CODE_PAYMENT_PROCESSOR_EXCEPTION = 12;
  CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_PROCESS_PROCESSING_PAYMENT = 13;
  CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_PROCESS_CREATED_PAYMENT = 14;
  CHARGE_PAYMENT_ERROR_CODE_PAYMENT_NOT_RELATED_TO_CREDIT_CARD = 15;
  CHARGE_PAYMENT_ERROR_CODE_PAYMENT_METHOD_CHANGED = 16;
  CHARGE_PAYMENT_ERROR_CODE_FAILED_PAYMENT_RETRY_FAILED = 17;
  CHARGE_PAYMENT_ERROR_CODE_PAYMENT_INTENT_NOT_IN_REQUIRES_CONFIRM = 18;
  CHARGE_PAYMENT_ERROR_CODE_PAYMENT_NOT_IN_FAILED_AUTHENTICATION_STATUS = 19;
  CHARGE_PAYMENT_ERROR_CODE_UNKNOWN_ERROR = 20;
}
