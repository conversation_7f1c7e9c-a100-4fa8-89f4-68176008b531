package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.services.payments.proto.RefundPaymentStatus;
import jakarta.annotation.Nullable;
import java.util.Objects;
import java.util.Optional;

public class RefundPaymentResult {
  @JsonProperty private final Status status;
  @JsonProperty private final RefundPaymentErrorDetails errorDetails;

  private RefundPaymentResult(Status status, @Nullable RefundPaymentErrorDetails errorDetails) {
    this.status = status;
    this.errorDetails = errorDetails;
  }

  public static RefundPaymentResult success() {
    return new RefundPaymentResult(Status.SUCCESSFUL, null);
  }

  public static RefundPaymentResult failure(RefundPaymentErrorDetails errorDetails) {
    return new RefundPaymentResult(Status.FAILED, errorDetails);
  }

  public static RefundPaymentResult failure(RefundPaymentErrorCode errorCode, Object... params) {

    return new RefundPaymentResult(
        Status.FAILED, new RefundPaymentErrorDetails(Objects.requireNonNull(errorCode), params));
  }

  /**
   * @return Not empty for {@link Status#FAILED}.
   */
  @JsonIgnore
  public Optional<RefundPaymentErrorDetails> getErrorDetails() {
    return Optional.ofNullable(errorDetails);
  }

  @JsonIgnore
  public boolean isSuccessful() {
    return status == Status.SUCCESSFUL;
  }

  @JsonIgnore
  public boolean isFailed() {
    return status == Status.FAILED;
  }

  public Status getStatus() {
    return status;
  }

  public static RefundPaymentResult fromGrpcResponse(
      com.xgen.cloud.services.payments.proto.RefundPaymentResult response) {
    return switch (response.getStatus()) {
      case REFUND_PAYMENT_STATUS_SUCCESSFUL -> success();
      case REFUND_PAYMENT_STATUS_FAILED ->
          failure(RefundPaymentErrorDetails.fromGrpcResponse(response.getErrorDetails()));
      case REFUND_PAYMENT_STATUS_UNSPECIFIED, UNRECOGNIZED ->
          throw new IllegalStateException("Unknown charge response status: " + response);
    };
  }

  @JsonIgnore
  public com.xgen.cloud.services.payments.proto.RefundPaymentResult toGrpcResponse() {
    com.xgen.cloud.services.payments.proto.RefundPaymentResult.Builder builder =
        com.xgen.cloud.services.payments.proto.RefundPaymentResult.newBuilder()
            .setStatus(RefundPaymentResult.Status.toGrpcResponse(status));
    if (getErrorDetails().isPresent()) {
      builder.setErrorDetails(getErrorDetails().get().toGrpcResponse());
    }

    return builder.build();
  }

  public enum Status {
    SUCCESSFUL,
    FAILED;

    public static RefundPaymentStatus toGrpcResponse(RefundPaymentResult.Status status) {
      return switch (status) {
        case SUCCESSFUL -> RefundPaymentStatus.REFUND_PAYMENT_STATUS_SUCCESSFUL;
        case FAILED -> RefundPaymentStatus.REFUND_PAYMENT_STATUS_FAILED;
      };
    }
  }
}
