package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.stripe.exception.StripeException;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.payments.exception._public.stripe.StripeSvcException;
import com.xgen.cloud.services.payments.proto.StripeErrorDetails;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class StripeRefundPaymentErrorDetails extends RefundPaymentErrorDetails {
  protected final String stripeErrorCode;

  public StripeRefundPaymentErrorDetails(
      String stripeErrorCode, RefundPaymentErrorCode errorCode, Object... params) {
    super(errorCode, params);
    this.stripeErrorCode = stripeErrorCode;
  }

  @JsonProperty
  public String getStripeErrorCode() {
    return stripeErrorCode;
  }

  @JsonIgnore
  @Override
  public com.xgen.cloud.services.payments.proto.RefundPaymentErrorDetails toGrpcResponse() {
    com.xgen.cloud.services.payments.proto.RefundPaymentErrorDetails.Builder builder =
        super.toGrpcResponse().toBuilder();

    if (stripeErrorCode != null) {
      builder.setStripeErrorDetails(
          StripeErrorDetails.newBuilder().setStripeErrorCode(stripeErrorCode).build());
    }

    return builder.build();
  }

  @Override
  public String toString() {
    return new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
        .appendSuper(super.toString())
        .append("stripeErrorCode", stripeErrorCode)
        .toString();
  }

  /** {@inheritDoc} */
  @JsonIgnore
  @Override
  public SvcException reconstructException() {
    return StripeSvcException.getException(
        new StripeException(null, null, this.getStripeErrorCode(), null) {});
  }
}
