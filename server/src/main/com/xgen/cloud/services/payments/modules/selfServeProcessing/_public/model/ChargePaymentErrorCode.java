package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model;

import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_FAILED_PAYMENT_RETRY_FAILED;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_CALCULATE_MANDATE_MAXIMUM;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_CALCULATE_SALES_TAX;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_LOCK_INVOICE;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_POLL_NON_PROCESSING_PAYMENT;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_PROCESS_CREATED_PAYMENT;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_PROCESS_PROCESSING_PAYMENT;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_MISSING_PAYMENT_METHOD;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_MISSING_PAYMENT_METHOD_ID_FOR_PROCESSING_PAYMENT;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_NO_PAYMENT_PROCESSOR;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_PAYMENT_INTENT_NOT_IN_REQUIRES_CONFIRM;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_PAYMENT_METHOD_CHANGED;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_PAYMENT_NOT_IN_FAILED_AUTHENTICATION_STATUS;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_PAYMENT_NOT_RELATED_TO_CREDIT_CARD;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_PAYMENT_PROCESSOR_EXCEPTION;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_PAYPAL_EXCEPTION;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_PROCESSING_PAYMENTS_NOT_SUPPORTED_BY_PAYMENT_PROCESSOR;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_STRIPE_CARD_EXCEPTION;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_STRIPE_EXCEPTION;
import static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode.CHARGE_PAYMENT_ERROR_CODE_UNKNOWN_ERROR;

import com.xgen.cloud.common.model._public.error.ErrorCode;

public enum ChargePaymentErrorCode implements ErrorCode {
  PROCESSING_PAYMENTS_NOT_SUPPORTED_BY_PAYMENT_PROCESSOR(
      "Payment processing not supported by payment processor",
      "Payment processing not supported by payment processor: %s"),
  NO_PAYMENT_PROCESSOR(
      "No payment processor for payment method", "No payment processor for payment method: %s"),

  MISSING_PAYMENT_METHOD("Missing payment method"),
  MISSING_PAYMENT_METHOD_ID_FOR_PROCESSING_PAYMENT(
      "PROCESSING payment does not have paymentMethodId set"),
  FAILED_TO_POLL_NON_PROCESSING_PAYMENT(
      "Failed to poll non-processing payment",
      "Failed to poll non-processing payment. Payment status: %s"),
  STRIPE_EXCEPTION("Stripe exception"),
  STRIPE_CARD_EXCEPTION("Stripe card exception"),
  PAYPAL_EXCEPTION("PayPal exception"),
  FAILED_TO_CALCULATE_MANDATE_MAXIMUM("Failed to calculate mandate maximum"),
  FAILED_TO_CALCULATE_SALES_TAX("Failed to calculate sales tax"),
  FAILED_TO_LOCK_INVOICE("Failed to lock invoice"),
  PAYMENT_PROCESSOR_EXCEPTION("Payment processor exception"),
  FAILED_TO_PROCESS_PROCESSING_PAYMENT(
      "The processing payment intent transitioned into another state",
      "The processing payment intent transitioned into %s state"),
  FAILED_TO_PROCESS_CREATED_PAYMENT(
      "The created payment intent is in another state",
      "The created payment intent is in %s state"),
  PAYMENT_NOT_RELATED_TO_CREDIT_CARD("Payment is not related to a credit card"),
  PAYMENT_NOT_IN_FAILED_AUTHENTICATION_STATUS("The payment might have already been confirmed."),
  PAYMENT_METHOD_CHANGED("The payment method was changed"),
  PAYMENT_INTENT_NOT_IN_REQUIRES_CONFIRM("The payment intent is not in the requries_confirm state"),
  FAILED_PAYMENT_RETRY_FAILED(
      "Some payment(s) could not be charged. Please enter a different payment method."),
  UNKNOWN_ERROR;

  private final String message;

  private final String messageFormat;

  ChargePaymentErrorCode(String message, String messageFormat) {
    this.message = message;
    this.messageFormat = messageFormat;
  }

  ChargePaymentErrorCode(String message) {
    this(message, null);
  }

  ChargePaymentErrorCode() {
    this(null, null);
  }

  public static ChargePaymentErrorCode fromGrpcResponse(
      com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode errorCode) {
    return switch (errorCode) {
      case CHARGE_PAYMENT_ERROR_CODE_PROCESSING_PAYMENTS_NOT_SUPPORTED_BY_PAYMENT_PROCESSOR ->
          PROCESSING_PAYMENTS_NOT_SUPPORTED_BY_PAYMENT_PROCESSOR;
      case CHARGE_PAYMENT_ERROR_CODE_NO_PAYMENT_PROCESSOR -> NO_PAYMENT_PROCESSOR;
      case CHARGE_PAYMENT_ERROR_CODE_MISSING_PAYMENT_METHOD -> MISSING_PAYMENT_METHOD;
      case CHARGE_PAYMENT_ERROR_CODE_MISSING_PAYMENT_METHOD_ID_FOR_PROCESSING_PAYMENT ->
          MISSING_PAYMENT_METHOD_ID_FOR_PROCESSING_PAYMENT;
      case CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_POLL_NON_PROCESSING_PAYMENT ->
          FAILED_TO_POLL_NON_PROCESSING_PAYMENT;
      case CHARGE_PAYMENT_ERROR_CODE_STRIPE_EXCEPTION -> STRIPE_EXCEPTION;
      case CHARGE_PAYMENT_ERROR_CODE_STRIPE_CARD_EXCEPTION -> STRIPE_CARD_EXCEPTION;
      case CHARGE_PAYMENT_ERROR_CODE_PAYPAL_EXCEPTION -> PAYPAL_EXCEPTION;
      case CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_CALCULATE_MANDATE_MAXIMUM ->
          FAILED_TO_CALCULATE_MANDATE_MAXIMUM;
      case CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_CALCULATE_SALES_TAX -> FAILED_TO_CALCULATE_SALES_TAX;
      case CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_LOCK_INVOICE -> FAILED_TO_LOCK_INVOICE;
      case CHARGE_PAYMENT_ERROR_CODE_PAYMENT_PROCESSOR_EXCEPTION -> PAYMENT_PROCESSOR_EXCEPTION;
      case CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_PROCESS_PROCESSING_PAYMENT ->
          FAILED_TO_PROCESS_PROCESSING_PAYMENT;
      case CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_PROCESS_CREATED_PAYMENT ->
          FAILED_TO_PROCESS_CREATED_PAYMENT;
      case CHARGE_PAYMENT_ERROR_CODE_PAYMENT_NOT_RELATED_TO_CREDIT_CARD ->
          PAYMENT_NOT_RELATED_TO_CREDIT_CARD;
      case CHARGE_PAYMENT_ERROR_CODE_PAYMENT_METHOD_CHANGED -> PAYMENT_METHOD_CHANGED;
      case CHARGE_PAYMENT_ERROR_CODE_FAILED_PAYMENT_RETRY_FAILED -> FAILED_PAYMENT_RETRY_FAILED;
      case CHARGE_PAYMENT_ERROR_CODE_PAYMENT_INTENT_NOT_IN_REQUIRES_CONFIRM ->
          PAYMENT_INTENT_NOT_IN_REQUIRES_CONFIRM;
      case CHARGE_PAYMENT_ERROR_CODE_PAYMENT_NOT_IN_FAILED_AUTHENTICATION_STATUS ->
          PAYMENT_NOT_IN_FAILED_AUTHENTICATION_STATUS;
      case UNRECOGNIZED,
              CHARGE_PAYMENT_ERROR_CODE_UNSPECIFIED,
              CHARGE_PAYMENT_ERROR_CODE_UNKNOWN_ERROR ->
          UNKNOWN_ERROR;
    };
  }

  public static com.xgen.cloud.services.payments.proto.ChargePaymentErrorCode toGrpcResponse(
      ChargePaymentErrorCode errorCode) {
    return switch (errorCode) {
      case PROCESSING_PAYMENTS_NOT_SUPPORTED_BY_PAYMENT_PROCESSOR ->
          CHARGE_PAYMENT_ERROR_CODE_PROCESSING_PAYMENTS_NOT_SUPPORTED_BY_PAYMENT_PROCESSOR;
      case NO_PAYMENT_PROCESSOR -> CHARGE_PAYMENT_ERROR_CODE_NO_PAYMENT_PROCESSOR;
      case MISSING_PAYMENT_METHOD -> CHARGE_PAYMENT_ERROR_CODE_MISSING_PAYMENT_METHOD;
      case MISSING_PAYMENT_METHOD_ID_FOR_PROCESSING_PAYMENT ->
          CHARGE_PAYMENT_ERROR_CODE_MISSING_PAYMENT_METHOD_ID_FOR_PROCESSING_PAYMENT;
      case FAILED_TO_POLL_NON_PROCESSING_PAYMENT ->
          CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_POLL_NON_PROCESSING_PAYMENT;
      case STRIPE_EXCEPTION -> CHARGE_PAYMENT_ERROR_CODE_STRIPE_EXCEPTION;
      case STRIPE_CARD_EXCEPTION -> CHARGE_PAYMENT_ERROR_CODE_STRIPE_CARD_EXCEPTION;
      case PAYPAL_EXCEPTION -> CHARGE_PAYMENT_ERROR_CODE_PAYPAL_EXCEPTION;
      case FAILED_TO_CALCULATE_MANDATE_MAXIMUM ->
          CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_CALCULATE_MANDATE_MAXIMUM;
      case FAILED_TO_CALCULATE_SALES_TAX -> CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_CALCULATE_SALES_TAX;
      case FAILED_TO_LOCK_INVOICE -> CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_LOCK_INVOICE;
      case PAYMENT_PROCESSOR_EXCEPTION -> CHARGE_PAYMENT_ERROR_CODE_PAYMENT_PROCESSOR_EXCEPTION;
      case FAILED_TO_PROCESS_PROCESSING_PAYMENT ->
          CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_PROCESS_PROCESSING_PAYMENT;
      case FAILED_TO_PROCESS_CREATED_PAYMENT ->
          CHARGE_PAYMENT_ERROR_CODE_FAILED_TO_PROCESS_CREATED_PAYMENT;
      case PAYMENT_NOT_RELATED_TO_CREDIT_CARD ->
          CHARGE_PAYMENT_ERROR_CODE_PAYMENT_NOT_RELATED_TO_CREDIT_CARD;
      case PAYMENT_METHOD_CHANGED -> CHARGE_PAYMENT_ERROR_CODE_PAYMENT_METHOD_CHANGED;
      case FAILED_PAYMENT_RETRY_FAILED -> CHARGE_PAYMENT_ERROR_CODE_FAILED_PAYMENT_RETRY_FAILED;
      case PAYMENT_INTENT_NOT_IN_REQUIRES_CONFIRM ->
          CHARGE_PAYMENT_ERROR_CODE_PAYMENT_INTENT_NOT_IN_REQUIRES_CONFIRM;
      case PAYMENT_NOT_IN_FAILED_AUTHENTICATION_STATUS ->
          CHARGE_PAYMENT_ERROR_CODE_PAYMENT_NOT_IN_FAILED_AUTHENTICATION_STATUS;
      case UNKNOWN_ERROR -> CHARGE_PAYMENT_ERROR_CODE_UNKNOWN_ERROR;
    };
  }

  @Override
  public String getMessage() {
    return message == null ? name() : message;
  }

  @Override
  public String formatMessage(Object... params) {
    return messageFormat == null ? getMessage() : String.format(messageFormat, params);
  }
}
