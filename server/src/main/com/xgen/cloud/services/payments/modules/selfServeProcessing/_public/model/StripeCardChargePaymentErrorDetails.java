package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.stripe.exception.CardException;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.payments.exception._public.stripe.StripeSvcException;
import com.xgen.cloud.services.payments.proto.ChargePaymentErrorDetails;
import com.xgen.cloud.services.payments.proto.StripeCardErrorDetails;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class StripeCardChargePaymentErrorDetails extends StripeChargePaymentErrorDetails {
  private final String cardDeclineCode;

  public StripeCardChargePaymentErrorDetails(
      String stripeErrorCode,
      String stripeErrorMessage,
      String cardDeclineCode,
      ChargePaymentErrorCode errorCode,
      Object... params) {
    super(stripeErrorCode, stripeErrorMessage, errorCode, params);
    this.cardDeclineCode = cardDeclineCode;
  }

  @JsonProperty
  public String getCardDeclineCode() {
    return cardDeclineCode;
  }

  @JsonIgnore
  @Override
  public ChargePaymentErrorDetails toGrpcResponse() {
    ChargePaymentErrorDetails.Builder builder = super.toGrpcResponse().toBuilder();

    if (cardDeclineCode != null) {
      StripeCardErrorDetails cardErrorDetails =
          StripeCardErrorDetails.newBuilder().setCardDeclineCode(cardDeclineCode).build();

      builder.setStripeErrorDetails(
          builder.getStripeErrorDetails().toBuilder().setStripeCardErrorDetails(cardErrorDetails));
    }

    return builder.build();
  }

  @Override
  public String toString() {
    return new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
        .appendSuper(super.toString())
        .append("cardDeclineCode", cardDeclineCode)
        .toString();
  }

  /** {@inheritDoc} */
  @JsonIgnore
  @Override
  public SvcException reconstructException() {
    return StripeSvcException.getException(
        new CardException(
            this.getStripeErrorMessage(),
            null,
            this.getStripeErrorCode(),
            null,
            this.getCardDeclineCode(),
            null,
            null,
            null));
  }
}
