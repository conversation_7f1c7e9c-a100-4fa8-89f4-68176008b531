package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gt;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.lt;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.nin;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.inc;
import static com.mongodb.client.model.Updates.set;
import static com.mongodb.client.model.Updates.unset;

import com.google.common.collect.Sets;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.UpdateOptions;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.cloud.common.util._public.time.DateTimeUtils;
import com.xgen.cloud.payments.common._public.model.PaymentMethodType;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.StripeApiError;
import com.xgen.svc.mms.model.billing.BillingAccount;
import com.xgen.svc.mms.model.billing.FailedChargeAttempts;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.Payment.Status;
import com.xgen.svc.mms.model.billing.PaymentBraintree;
import com.xgen.svc.mms.model.billing.PaymentIntentStatus;
import com.xgen.svc.mms.model.billing.PaymentStripe;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class PaymentDaoImpl extends BaseDao<Payment> implements PaymentDao {

  private static final Logger LOG = LoggerFactory.getLogger(PaymentDaoImpl.class);

  @Inject
  public PaymentDaoImpl(MongoClientContainer container, CodecRegistry codecRegistry) {
    super(container, Payment.DB_NAME, Payment.COLLECTION_NAME, codecRegistry);
  }

  @Override
  public Payment findById(ObjectId id) {
    return getCollection().find(eq(ID_FIELD, id)).first();
  }

  @Override
  public Stream<Payment> findNewAndFailedAndProcessingPayments(
      int maxAttempts, int maxProcessingAttempts, Date lastRetry, Date maxCreatedAt) {
    Bson filter =
        and(
            gt(Payment.AMOUNT_BILLED_CENTS_FIELD, 0L),
            nin(
                Payment.PAYMENT_METHOD_FIELD,
                Sets.union(
                    PaymentMethodType.NETSUITE_PAYMENT_METHOD_TYPES,
                    PaymentMethodType.SELF_SERVE_MARKETPLACE_TYPES)),
            ne(Payment.INVOICE_TYPE_FIELD, Invoice.Type.CONSULTING),
            lte(Payment.CREATED_FIELD, maxCreatedAt),
            or(
                in(Payment.STATUS_FIELD, List.of(Payment.Status.CREATED, Status.PROCESSING)),
                and(
                    in(
                        Payment.STATUS_FIELD,
                        List.of(Payment.Status.FAILED, Payment.Status.FAILED_AUTHENTICATION)),
                    lt(
                        Payment.FAILED_CHARGE_ATTEMPTS_FIELD
                            + "."
                            + FailedChargeAttempts.LAST_ATTEMPTED_CHARGE_FIELD,
                        lastRetry),
                    lt(
                        Payment.FAILED_CHARGE_ATTEMPTS_FIELD
                            + "."
                            + FailedChargeAttempts.ATTEMPTS_FIELD,
                        maxAttempts),
                    or(
                        lt(
                            Payment.NUM_TIMES_TRANSITIONED_TO_PROCESSING_FIELD,
                            maxProcessingAttempts),
                        eq(Payment.NUM_TIMES_TRANSITIONED_TO_PROCESSING_FIELD, null))),
                and(
                    in(
                        Payment.STATUS_FIELD,
                        List.of(Payment.Status.FAILED, Status.FAILED_AUTHENTICATION)),
                    eq(Payment.FAILED_CHARGE_ATTEMPTS_FIELD, null))));

    return StreamSupport.stream(getCollection().find(filter).spliterator(), false);
  }

  @Override
  public Payment findNewAndFailedAndProcessingPaymentByPaymentId(ObjectId paymentId) {
    Bson filter =
        and(
            eq(ID_FIELD, paymentId),
            in(
                Payment.STATUS_FIELD,
                List.of(
                    Status.FAILED,
                    Status.CREATED,
                    Status.FAILED_AUTHENTICATION,
                    Status.PROCESSING)));
    return getCollection().find(filter).first();
  }

  @Override
  public void updateFailedChargeAttempts(ObjectId paymentId, Date now) {
    updateOneReplicaSafe(
        eq(ID_FIELD, paymentId),
        combine(
            set(
                Payment.FAILED_CHARGE_ATTEMPTS_FIELD
                    + "."
                    + FailedChargeAttempts.LAST_ATTEMPTED_CHARGE_FIELD,
                now),
            inc(
                Payment.FAILED_CHARGE_ATTEMPTS_FIELD + "." + FailedChargeAttempts.ATTEMPTS_FIELD,
                1)),
        new UpdateOptions().upsert(false));
  }

  @Override
  public List<Payment> findTooManyRetriesPayments(
      int maxAttempts, int maxProcessingAttempts, ObjectId orgId) {
    Bson filter =
        and(
            gt(Payment.AMOUNT_BILLED_CENTS_FIELD, 0L),
            eq(Payment.ORG_ID_FIELD, orgId),
            eq(Payment.STATUS_FIELD, Payment.Status.FAILED),
            or(
                gte(
                    Payment.FAILED_CHARGE_ATTEMPTS_FIELD
                        + "."
                        + FailedChargeAttempts.ATTEMPTS_FIELD,
                    maxAttempts),
                gte(Payment.NUM_TIMES_TRANSITIONED_TO_PROCESSING_FIELD, maxProcessingAttempts)));

    return getCollection().find(filter).into(new ArrayList<>());
  }

  @Override
  public List<Payment> findAllPendingReversal() {
    Bson filter = eq(Payment.STATUS_FIELD, Status.PENDING_REVERSAL);
    return getCollection().find(filter).into(new ArrayList<>());
  }

  @Override
  public void setPaymentStatus(ObjectId id, Status paymentStatus, Date date) {
    updateOneReplicaSafe(
        eq(ID_FIELD, id),
        combine(
            set(Payment.STATUS_FIELD, paymentStatus),
            set(Payment.UPDATED_FIELD, date),
            unset(Payment.CHARGE_LOCK_FIELD)),
        new UpdateOptions().upsert(false));
  }

  @Override
  public void clearPaymentMethodInfo(ObjectId paymentId, Date date) {
    updateOneReplicaSafe(
        eq(ID_FIELD, paymentId),
        combine(
            set(Payment.STATUS_FIELD, Status.FAILED),
            set(Payment.UPDATED_FIELD, date),
            unset(Payment.BRAINTREE_FIELD),
            unset(Payment.STRIPE_FIELD),
            unset(Payment.PAYMENT_METHOD_FIELD),
            unset(Payment.PAYMENT_METHOD_ID_FIELD),
            unset(Payment.BILLING_ACCOUNT_FIELD),
            unset(Payment.CARD_EXPIRATION_FIELD),
            unset(Payment.CARD_LAST4_FIELD)),
        new UpdateOptions().upsert(false));
  }

  @Override
  public void saveSuccessfulStripeCharge(
      ObjectId paymentId,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      String stripePaymentIntentId,
      long amountPaidCents,
      String stripeChargeId,
      String cardLast4,
      Date cardExpiration,
      Date date) {
    saveStripeCharge(
        paymentId,
        paymentMethodId,
        billingAccount,
        stripePaymentIntentId,
        amountPaidCents,
        stripeChargeId,
        cardLast4,
        cardExpiration,
        Status.PAID,
        date);
  }

  @Override
  public void saveStripeCharge(
      ObjectId paymentId,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      String stripePaymentIntentId,
      long amountPaidCents,
      String stripeChargeId,
      String cardLast4,
      Date cardExpiration,
      Payment.Status newStatus,
      Date date) {
    updateOneReplicaSafe(
        eq(ID_FIELD, paymentId),
        combine(
            set(Payment.STATUS_FIELD, newStatus),
            set(Payment.UPDATED_FIELD, date),
            set(Payment.PAYMENT_METHOD_ID_FIELD, paymentMethodId),
            set(Payment.BILLING_ACCOUNT_FIELD, billingAccount),
            set(Payment.AMOUNT_PAID_CENTS_FIELD, amountPaidCents),
            set(Payment.PAYMENT_METHOD_FIELD, PaymentMethodType.CREDIT_CARD),
            set(Payment.CARD_LAST4_FIELD, cardLast4),
            set(Payment.CARD_EXPIRATION_FIELD, cardExpiration),
            set(
                Payment.STRIPE_FIELD + "." + PaymentStripe.PAYMENT_INTENT_ID_FIELD,
                stripePaymentIntentId),
            set(
                Payment.STRIPE_FIELD + "." + PaymentStripe.PAYMENT_INTENT_STATUS_FIELD,
                PaymentIntentStatus.SUCCEEDED),
            set(Payment.STRIPE_FIELD + "." + PaymentStripe.CHARGE_ID_FIELD, stripeChargeId),
            unset(Payment.STRIPE_FIELD + "." + PaymentStripe.CHARGE_FAILURE_CODE_FIELD),
            unset(Payment.BRAINTREE_FIELD),
            unset(Payment.CHARGE_LOCK_FIELD)),
        new UpdateOptions().upsert(false));
  }

  @Override
  public void saveProcessingStripeCharge(
      ObjectId paymentId,
      ObjectId paymentMethodId,
      String cardLast4,
      BillingAccount billingAccount,
      String stripePaymentIntentId,
      LocalDateTime date) {
    updateOneReplicaSafe(
        eq(ID_FIELD, paymentId),
        combine(
            set(Payment.STATUS_FIELD, Status.PROCESSING),
            inc(Payment.NUM_TIMES_TRANSITIONED_TO_PROCESSING_FIELD, 1),
            set(Payment.UPDATED_FIELD, DateTimeUtils.dateOf(date)),
            set(Payment.PAYMENT_METHOD_ID_FIELD, paymentMethodId),
            set(Payment.CARD_LAST4_FIELD, cardLast4),
            set(Payment.BILLING_ACCOUNT_FIELD, billingAccount),
            set(Payment.PAYMENT_METHOD_FIELD, PaymentMethodType.CREDIT_CARD),
            set(
                Payment.STRIPE_FIELD + "." + PaymentStripe.PAYMENT_INTENT_ID_FIELD,
                stripePaymentIntentId),
            set(
                Payment.STRIPE_FIELD + "." + PaymentStripe.PAYMENT_INTENT_STATUS_FIELD,
                PaymentIntentStatus.PROCESSING),
            set(Payment.STRIPE_FIELD + "." + PaymentStripe.PROCESSING_START_DATE_FIELD, date),
            unset(Payment.BRAINTREE_FIELD),
            unset(Payment.CHARGE_LOCK_FIELD)),
        new UpdateOptions().upsert(false));
  }

  @Override
  public void saveFailedStripeCharge(
      ObjectId paymentId,
      Status paymentStatus,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      String stripePaymentIntentId,
      String stripePaymentIntentStatus,
      @Nullable StripeApiError stripeApiError,
      String cardLast4,
      Date cardExpiration,
      Date date) {
    List<Bson> updateOps =
        new ArrayList<>(
            List.of(
                set(Payment.STATUS_FIELD, paymentStatus),
                set(Payment.UPDATED_FIELD, date),
                set(Payment.PAYMENT_METHOD_ID_FIELD, paymentMethodId),
                set(Payment.BILLING_ACCOUNT_FIELD, billingAccount),
                set(Payment.PAYMENT_METHOD_FIELD, PaymentMethodType.CREDIT_CARD),
                set(Payment.CARD_LAST4_FIELD, cardLast4),
                set(Payment.CARD_EXPIRATION_FIELD, cardExpiration),
                unset(Payment.BRAINTREE_FIELD),
                unset(Payment.CHARGE_LOCK_FIELD)));

    if (stripePaymentIntentId == null) {
      updateOps.add(unset(Payment.STRIPE_FIELD + "." + PaymentStripe.PAYMENT_INTENT_ID_FIELD));
      updateOps.add(unset(Payment.STRIPE_FIELD + "." + PaymentStripe.PAYMENT_INTENT_STATUS_FIELD));
    } else {
      updateOps.add(
          set(
              Payment.STRIPE_FIELD + "." + PaymentStripe.PAYMENT_INTENT_ID_FIELD,
              stripePaymentIntentId));
      updateOps.add(
          set(
              Payment.STRIPE_FIELD + "." + PaymentStripe.PAYMENT_INTENT_STATUS_FIELD,
              stripePaymentIntentStatus));
    }

    if (stripeApiError == null || stripeApiError.getApiErrorCode() == null) {
      updateOps.add(unset(Payment.STRIPE_FIELD + "." + PaymentStripe.CHARGE_FAILURE_CODE_FIELD));
    } else {
      updateOps.add(
          set(
              Payment.STRIPE_FIELD + "." + PaymentStripe.CHARGE_FAILURE_CODE_FIELD,
              stripeApiError.getApiErrorCode()));
    }

    if (stripeApiError == null || stripeApiError.getApiDeclineCode() == null) {
      updateOps.add(unset(Payment.STRIPE_FIELD + "." + PaymentStripe.CHARGE_DECLINE_CODE_FIELD));
    } else {
      updateOps.add(
          set(
              Payment.STRIPE_FIELD + "." + PaymentStripe.CHARGE_DECLINE_CODE_FIELD,
              stripeApiError.getApiDeclineCode()));
    }

    updateOneReplicaSafe(
        eq(ID_FIELD, paymentId), combine(updateOps), new UpdateOptions().upsert(false));
  }

  @Override
  public void updateStripeConversionRate(ObjectId paymentId, BigDecimal conversionRate) {
    updateOneReplicaSafe(
        eq(ID_FIELD, paymentId),
        set(Payment.STRIPE_FIELD + "." + PaymentStripe.CONVERSION_RATE_FIELD, conversionRate),
        new UpdateOptions().upsert(false));
  }

  @Override
  public boolean anyNewOrFailedOrProcessingByInvoiceId(ObjectId invoiceId) {
    Bson filter =
        and(
            eq(Payment.INVOICE_ID_FIELD, invoiceId),
            in(
                Payment.STATUS_FIELD,
                List.of(
                    Status.FAILED,
                    Status.CREATED,
                    Status.FAILED_AUTHENTICATION,
                    Status.PROCESSING)),
            ne(Payment.INVOICE_TYPE_FIELD, Invoice.Type.CONSULTING));

    return getCollection().find(filter).first() != null;
  }

  /** {@inheritDoc} */
  @Override
  public boolean setChargeLock(ObjectId paymentId, LocalDateTime now) {
    return updateOneMajority(
                and(eq(ID_FIELD, paymentId), eq(Payment.CHARGE_LOCK_FIELD, null)),
                set(Payment.CHARGE_LOCK_FIELD, now),
                new UpdateOptions().upsert(false))
            .getModifiedCount()
        > 0;
  }

  @Override
  public void unsetChargeLock(ObjectId paymentId) {
    updateOneMajority(
        eq(ID_FIELD, paymentId),
        unset(Payment.CHARGE_LOCK_FIELD),
        new UpdateOptions().upsert(false));
  }

  @Override
  public void saveAdminOverrideCharge(ObjectId paymentId, ObjectId adminOverridePaymentMethodId) {
    updateOneReplicaSafe(
        eq(ID_FIELD, paymentId),
        combine(
            set(Payment.PAYMENT_METHOD_FIELD, PaymentMethodType.ADMIN_OVERRIDE),
            set(Payment.PAYMENT_METHOD_ID_FIELD, adminOverridePaymentMethodId)),
        new UpdateOptions().upsert(false));
  }

  @Override
  public void setBraintreeTransactionId(ObjectId paymentId, String transactionId) {
    updateOneReplicaSafe(
        eq(ID_FIELD, paymentId),
        set(Payment.BRAINTREE_FIELD + "." + PaymentBraintree.TRANSACTION_ID_FIELD, transactionId),
        new UpdateOptions().upsert(false));
  }

  @Override
  public void saveSuccessfulBraintreeCharge(
      ObjectId paymentId,
      String transactionId,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      Date date) {
    saveBraintreeCharge(
        paymentId, Payment.Status.PAID, paymentMethodId, billingAccount, transactionId, null, date);
  }

  @Override
  public void saveFailedBraintreeCharge(
      ObjectId paymentId,
      String chargeFailureMessage,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      Date date) {
    saveBraintreeCharge(
        paymentId,
        Payment.Status.FAILED,
        paymentMethodId,
        billingAccount,
        null,
        chargeFailureMessage,
        date);
  }

  private void saveBraintreeCharge(
      ObjectId paymentId,
      Payment.Status status,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      String transactionId,
      String chargeFailureMessage,
      Date date) {
    List<Bson> updateOps =
        new ArrayList<>(
            List.of(
                set(Payment.STATUS_FIELD, status),
                set(Payment.UPDATED_FIELD, date),
                set(Payment.PAYMENT_METHOD_ID_FIELD, paymentMethodId),
                set(Payment.BILLING_ACCOUNT_FIELD, billingAccount),
                set(Payment.PAYMENT_METHOD_FIELD, PaymentMethodType.PAYPAL),
                unset(Payment.STRIPE_FIELD),
                unset(Payment.CHARGE_LOCK_FIELD)));

    if (transactionId != null) {
      updateOps.add(
          set(
              Payment.BRAINTREE_FIELD + "." + PaymentBraintree.TRANSACTION_ID_FIELD,
              transactionId));
    }
    if (chargeFailureMessage != null) {
      updateOps.add(
          set(
              Payment.BRAINTREE_FIELD + "." + PaymentBraintree.CHARGE_FAILURE_MESSAGE_FIELD,
              chargeFailureMessage));
    }

    updateOneReplicaSafe(
        eq(ID_FIELD, paymentId), combine(updateOps), new UpdateOptions().upsert(false));
  }

  @Override
  public void markBraintreePaymentAsPaid(String transactionId, Long amount, Date date) {
    List<Bson> updateOps =
        new ArrayList<>(
            List.of(
                set(Payment.STATUS_FIELD, Payment.Status.PAID),
                set(Payment.UPDATED_FIELD, date),
                unset(Payment.CHARGE_LOCK_FIELD)));

    if (amount != null) {
      updateOps.add(set(Payment.AMOUNT_PAID_CENTS_FIELD, amount));
    }

    updateOneReplicaSafe(
        eq(Payment.BRAINTREE_FIELD + "." + PaymentBraintree.TRANSACTION_ID_FIELD, transactionId),
        combine(updateOps),
        new UpdateOptions().upsert(false));
  }

  @Override
  public void setBraintreeRefundId(ObjectId paymentId, String refundId) {
    updateOneReplicaSafe(
        eq(ID_FIELD, paymentId),
        set(Payment.BRAINTREE_FIELD + "." + PaymentBraintree.REFUND_ID_FIELD, refundId),
        new UpdateOptions().upsert(false));
  }

  @Override
  public List<Payment> findByOrgId(ObjectId orgId) {
    return getCollection().find(eq(Payment.ORG_ID_FIELD, orgId)).into(new ArrayList<>());
  }

  @Override
  public List<Payment> findByInvoiceId(ObjectId invoiceId, boolean includeCancelled) {
    List<Bson> filters = new ArrayList<>();
    filters.add(eq(Payment.INVOICE_ID_FIELD, invoiceId));

    if (!includeCancelled) {
      filters.add(ne(Payment.STATUS_FIELD, Payment.Status.CANCELLED));
    }

    return getCollection()
        .find(and(filters))
        .sort(descending(Payment.CREATED_FIELD))
        .into(new ArrayList<>());
  }

  /**
   * Update a payment based on a refund.
   *
   * @param paymentId - id of a payment that is being refunded
   * @param newAmountPaidCents - updated paid amount after the refund is applied
   * @param date - date of the payment update op
   * @param updatedPaymentStatus - new status of the payment after refund
   * @param refundReason - reason for the refund
   */
  @Override
  public void applyRefundToPayment(
      ObjectId paymentId,
      long newAmountPaidCents,
      Date date,
      Status updatedPaymentStatus,
      String refundReason) {
    Bson filter = eq(ID_FIELD, paymentId);

    List<Bson> updates = new ArrayList<>();
    Bson mainUpdates =
        combine(
            set(Payment.AMOUNT_PAID_CENTS_FIELD, newAmountPaidCents),
            set(Payment.UPDATED_FIELD, date),
            set(
                Payment.UPDATE_NOTE_FIELD,
                setOrAppendForStringField(Payment.UPDATE_NOTE_FIELD, refundReason)));

    if (newAmountPaidCents != 0) {
      updates.add(mainUpdates);
    } else {
      updates.add(combine(mainUpdates, set(Payment.STATUS_FIELD, updatedPaymentStatus)));
    }
    updates.add(Aggregates.unset(Payment.CHARGE_LOCK_FIELD));

    getCollection()
        .withWriteConcern(getReplicaSafeWriteConcern())
        .updateOne(filter, updates, new UpdateOptions().upsert(false));
  }
}
