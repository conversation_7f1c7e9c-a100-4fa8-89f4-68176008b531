syntax = "proto3";

package com.xgen.cloud.services.payments;

option java_multiple_files = true;
option java_package = "com.xgen.cloud.services.payments.proto";

message BraintreeErrorDetails {
  optional string braintree_error_code = 1;
  string braintree_response_message = 2;
}

message StripeErrorDetails {
  optional string stripe_error_code = 1;

  oneof subtype {
    StripeCardErrorDetails stripe_card_error_details = 2;
  }
  optional string stripe_error_message = 3;
}

message StripeCardErrorDetails {
  string card_decline_code = 1;
}

enum RevenueRefundReason {
  REVENUE_REFUND_REASON_UNSPECIFIED = 0;
  REVENUE_REFUND_REASON_ACCIDENTAL_USAGE = 1;
  REVENUE_REFUND_REASON_ACTIVATION_CODE = 2;
  REVENUE_REFUND_REASON_BILLING_CORRECTION = 3;
  REVENUE_REFUND_REASON_ELASTIC_INVOICING = 4;
  REVENUE_REFUND_REASON_VAT_REFUND = 5;
  REVENUE_REFUND_REASON_SALES_TAX_REFUND = 6;
  REVENUE_REFUND_REASON_EMPLOYEE_USE = 7;
  REVENUE_REFUND_REASON_CREDIT_REBALANCING = 8;
  REVENUE_REFUND_REASON_CREDIT_CARD_CHANGED = 9;
}
