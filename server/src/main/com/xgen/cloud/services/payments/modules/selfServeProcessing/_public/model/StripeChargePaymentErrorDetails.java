package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.stripe.exception.StripeException;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.payments.exception._public.stripe.StripeSvcException;
import com.xgen.cloud.services.payments.proto.StripeErrorDetails;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class StripeChargePaymentErrorDetails extends ChargePaymentErrorDetails {
  protected final String stripeErrorCode;
  protected final String stripeErrorMessage;

  public StripeChargePaymentErrorDetails(
      String stripeErrorCode,
      String stripeErrorMessage,
      ChargePaymentErrorCode errorCode,
      Object... params) {
    super(errorCode, params);
    this.stripeErrorCode = stripeErrorCode;
    this.stripeErrorMessage = stripeErrorMessage;
  }

  @JsonProperty
  public String getStripeErrorCode() {
    return stripeErrorCode;
  }

  @JsonProperty
  public String getStripeErrorMessage() {
    return stripeErrorMessage;
  }

  @JsonIgnore
  @Override
  public com.xgen.cloud.services.payments.proto.ChargePaymentErrorDetails toGrpcResponse() {
    com.xgen.cloud.services.payments.proto.ChargePaymentErrorDetails.Builder builder =
        super.toGrpcResponse().toBuilder();

    StripeErrorDetails.Builder stripeErrorDetails = StripeErrorDetails.newBuilder();
    if (stripeErrorCode != null) {
      stripeErrorDetails.setStripeErrorCode(stripeErrorCode);
    }
    if (stripeErrorMessage != null) {
      stripeErrorDetails.setStripeErrorMessage(stripeErrorMessage);
    }

    return builder.setStripeErrorDetails(stripeErrorDetails).build();
  }

  @Override
  public String toString() {
    return new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
        .appendSuper(super.toString())
        .append("stripeErrorCode", stripeErrorCode)
        .toString();
  }

  /** {@inheritDoc} */
  @JsonIgnore
  @Override
  public SvcException reconstructException() {
    return StripeSvcException.getException(
        new StripeException(
            this.getStripeErrorMessage(), null, this.getStripeErrorCode(), null) {});
  }
}
