package com.xgen.cloud.services.payments.modules.selfServeProcessing.runtime.grpc;

import static net.logstash.logback.argument.StructuredArguments.kv;

import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.converter.RefundGrpcConverter;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.svc.RefundQuerySvc;
import com.xgen.cloud.services.payments.proto.GetRefundsByInvoiceIdRequest;
import com.xgen.cloud.services.payments.proto.GetRefundsByInvoiceIdResponse;
import com.xgen.cloud.services.payments.proto.GetRefundsByPaymentIdRequest;
import com.xgen.cloud.services.payments.proto.GetRefundsByPaymentIdResponse;
import com.xgen.cloud.services.payments.proto.RefundQueryingServiceGrpc;
import io.grpc.stub.StreamObserver;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

@Singleton
public final class RefundQueryingServiceImpl
    extends RefundQueryingServiceGrpc.RefundQueryingServiceImplBase {

  private static final Logger LOG = LoggerFactory.getLogger(RefundQueryingServiceImpl.class);

  private final RefundQuerySvc refundQuerySvc;

  @Inject
  public RefundQueryingServiceImpl(RefundQuerySvc refundQuerySvc) {
    this.refundQuerySvc = refundQuerySvc;
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.RefundQueryingClient#getRefundsByInvoiceId
   *     RefundQueryingClient#getRefundsByInvoiceId
   */
  @Override
  public void getRefundsByInvoiceId(
      GetRefundsByInvoiceIdRequest request,
      StreamObserver<GetRefundsByInvoiceIdResponse> responseObserver) {
    Mono.fromCallable(() -> new ObjectId(request.getInvoiceId()))
        .flatMapMany(refundQuerySvc::getByInvoiceId)
        .map(RefundGrpcConverter::toGrpc)
        .collectList()
        .map(refunds -> GetRefundsByInvoiceIdResponse.newBuilder().addAllRefunds(refunds).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while getting refunds by invoice. {}",
                    kv("invoiceId", request.getInvoiceId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }

  /**
   * @see com.xgen.cloud.payments.grpc._public.client.RefundQueryingClient#getRefundsByPaymentId
   *     RefundQueryingClient#getRefundsByPaymentId
   */
  @Override
  public void getRefundsByPaymentId(
      GetRefundsByPaymentIdRequest request,
      StreamObserver<GetRefundsByPaymentIdResponse> responseObserver) {
    Mono.fromCallable(() -> new ObjectId(request.getPaymentId()))
        .flatMapMany(refundQuerySvc::getByPaymentId)
        .map(RefundGrpcConverter::toGrpc)
        .collectList()
        .map(refunds -> GetRefundsByPaymentIdResponse.newBuilder().addAllRefunds(refunds).build())
        .doOnError(
            e ->
                LOG.error(
                    "Unexpected exception while getting refunds by payment. {}",
                    kv("paymentId", request.getPaymentId()),
                    e))
        .subscribe(
            responseObserver::onNext, responseObserver::onError, responseObserver::onCompleted);
  }
}
