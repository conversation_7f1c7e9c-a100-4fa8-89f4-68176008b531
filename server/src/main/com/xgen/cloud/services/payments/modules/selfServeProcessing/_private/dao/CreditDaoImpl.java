package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.inc;

import com.mongodb.reactivestreams.client.MongoClient;
import com.xgen.cloud.payments.common._public.dao.ReactiveBaseDao;
import com.xgen.svc.mms.model.billing.Credit;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.types.ObjectId;
import reactor.core.publisher.Mono;

public class CreditDaoImpl extends ReactiveBaseDao<Credit> implements CreditDao {
  @Inject
  public CreditDaoImpl(@Named(Credit.DB_NAME) MongoClient client, CodecRegistry codecRegistry) {
    super(client, Credit.DB_NAME, Credit.COLLECTION_NAME, codecRegistry, Credit.class);
  }

  @Override
  public Mono<Void> addTotalBilledCents(ObjectId creditId, long totalBilledCents) {
    return updateOneMajority(
            eq(Credit.ID, creditId), inc(Credit.TOTAL_BILLED_CENTS_FIELD, totalBilledCents))
        .then();
  }

  @Override
  public Mono<Void> addAmountRemainingCents(ObjectId creditId, long amountRemainingCents) {
    return updateOneMajority(
            eq(Credit.ID, creditId), inc(Credit.AMOUNT_REMAINING_CENTS_FIELD, amountRemainingCents))
        .then();
  }

  @Override
  public Mono<Credit> findById(ObjectId creditId) {
    return findOne(eq(Credit.ID, creditId));
  }
}
