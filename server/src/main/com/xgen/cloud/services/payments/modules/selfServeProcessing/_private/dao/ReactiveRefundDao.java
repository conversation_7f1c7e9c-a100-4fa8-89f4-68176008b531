package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao;

import static com.mongodb.client.model.Filters.eq;
import static com.xgen.svc.mms.model.billing.Refund.COLLECTION_NAME;
import static com.xgen.svc.mms.model.billing.Refund.DB_NAME;

import com.mongodb.reactivestreams.client.MongoClient;
import com.xgen.cloud.payments.common._public.dao.ReactiveBaseDao;
import com.xgen.svc.mms.model.billing.Refund;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;

@Singleton
public class ReactiveRefundDao extends ReactiveBaseDao<Refund> {

  @Inject
  public ReactiveRefundDao(@Named(DB_NAME) MongoClient client, CodecRegistry codecRegistry) {
    super(client, DB_NAME, COLLECTION_NAME, codecRegistry, Refund.class);
  }

  public Flux<Refund> findByInvoiceId(ObjectId invoiceId) {
    return find(eq(Refund.INVOICE_ID_FIELD, invoiceId));
  }

  public Flux<Refund> findByPaymentId(ObjectId paymentId) {
    return find(eq(Refund.PAYMENT_ID_FIELD, paymentId));
  }
}
