syntax = "proto3";

// leaf of the full qualified package name is used as a serviceId
package com.xgen.cloud.services.payments;

import "google/protobuf/empty.proto";
import "server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/proto/common.proto";

option java_multiple_files = true;
option java_outer_classname = "RefundProcessingProto";
option java_package = "com.xgen.cloud.services.payments.proto";

// Based on https://developers.google.com/protocol-buffers/docs/style
service RefundProcessingService {
  rpc FullRefundInvoice(FullRefundInvoiceRequest) returns (FullRefundInvoiceResponse) {}
  rpc FullTaxRefundInvoice(FullTaxRefundInvoiceRequest) returns (FullTaxRefundInvoiceResponse) {}

  rpc FullRefundPayment(FullRefundPaymentRequest) returns (FullRefundPaymentResponse) {}
  rpc FullRefundPaymentWithoutForgivingCharges(FullRefundPaymentWithoutForgivingChargesRequest) returns (FullRefundPaymentWithoutForgivingChargesResponse) {}
  rpc PartialRefundPayment(PartialRefundPaymentRequest) returns (PartialRefundPaymentResponse) {}
  rpc PartialTaxRefundPayment(PartialTaxRefundPaymentRequest) returns (PartialTaxRefundPaymentResponse) {}
  rpc FullTaxRefundPayment(FullTaxRefundPaymentRequest) returns (FullTaxRefundPaymentResponse) {}

  rpc CancelOrRefundPendingReversals(google.protobuf.Empty) returns (google.protobuf.Empty) {}
}

message FullRefundInvoiceRequest {
  string invoice_id = 1;
  string refund_reason = 2;
  RevenueRefundReason revenue_refund_reason = 3;
}

message FullRefundInvoiceResponse {
  RefundPaymentResult result = 1;
}

message FullTaxRefundInvoiceRequest {
  reserved 3;
  reserved "revenue_refund_reason";

  string invoice_id = 1;
  string refund_reason = 2;
}

message FullTaxRefundInvoiceResponse {
  RefundPaymentResult result = 1;
}

message FullRefundPaymentRequest {
  string payment_id = 1;
  string refund_reason = 2;
  RevenueRefundReason revenue_refund_reason = 3;
}

message FullRefundPaymentResponse {
  RefundPaymentResult result = 1;
}

message FullRefundPaymentWithoutForgivingChargesRequest {
  string payment_id = 1;
  string refund_reason = 2;
  RevenueRefundReason revenue_refund_reason = 3;
}

message FullRefundPaymentWithoutForgivingChargesResponse {
  RefundPaymentResult result = 1;
}

message PartialRefundPaymentRequest {
  string payment_id = 1;
  int64 partial_refund_amount_cents = 2;
  string refund_reason = 3;
  RevenueRefundReason revenue_refund_reason = 4;
}

message PartialRefundPaymentResponse {
  RefundPaymentResult result = 1;
}

message PartialTaxRefundPaymentRequest {
  reserved 4;
  reserved "revenue_refund_reason";

  string payment_id = 1;
  int64 tax_refund_amount_cents = 2;
  string refund_reason = 3;
}

message PartialTaxRefundPaymentResponse {
  RefundPaymentResult result = 1;
}

message FullTaxRefundPaymentRequest {
  string payment_id = 1;
  string refund_reason = 2;
  RevenueRefundReason revenue_refund_reason = 3;
}

message FullTaxRefundPaymentResponse {
  RefundPaymentResult result = 1;
}

message RefundPaymentResult {
  RefundPaymentStatus status = 1;
  optional RefundPaymentErrorDetails error_details = 2;
}

message RefundPaymentErrorDetails {
  RefundPaymentErrorCode error_code = 1;
  repeated string message_params = 2;

  oneof subtype {
    StripeErrorDetails stripe_error_details = 3;
    BraintreeErrorDetails braintree_error_details = 4;
  }
}

enum RefundPaymentStatus {
  REFUND_PAYMENT_STATUS_UNSPECIFIED = 0;
  REFUND_PAYMENT_STATUS_SUCCESSFUL = 1;
  REFUND_PAYMENT_STATUS_FAILED = 2;
}

enum RefundPaymentErrorCode {
  REFUND_PAYMENT_ERROR_CODE_UNSPECIFIED = 0;
  REFUND_PAYMENT_ERROR_CODE_PAYMENT_NOT_FOUND = 1;
  REFUND_PAYMENT_ERROR_CODE_WRONG_PAYMENT_METHOD_TO_REFUND = 2;
  REFUND_PAYMENT_ERROR_CODE_REASON_REQUIRED = 3;
  REFUND_PAYMENT_ERROR_CODE_REVENUE_REASON_REQUIRED = 4;
  REFUND_PAYMENT_ERROR_CODE_WRONG_PAYMENT_STATUS_FOR_REFUND = 5;
  REFUND_PAYMENT_ERROR_CODE_REFUND_AMOUNT_EXCEEDS_REMAINING = 6;
  REFUND_PAYMENT_ERROR_CODE_INVALID_REFUND_TYPE = 7;
  REFUND_PAYMENT_ERROR_CODE_LOCKING_INVOICE_FAILED = 8;
  REFUND_PAYMENT_ERROR_CODE_REFUND_FAILED = 9;
  REFUND_PAYMENT_ERROR_CODE_BRAINTREE_TRANSACTION_NOT_FOUND = 10;
  REFUND_PAYMENT_ERROR_CODE_ZERO_AMOUNT = 11;
  REFUND_PAYMENT_ERROR_CODE_NO_BRAINTREE_DATA = 12;
  REFUND_PAYMENT_ERROR_CODE_NO_STRIPE_DATA = 13;
  REFUND_PAYMENT_ERROR_CODE_STRIPE_EXCEPTION = 14;
  REFUND_PAYMENT_ERROR_CODE_STRIPE_CARD_EXCEPTION = 15;
  REFUND_PAYMENT_ERROR_CODE_TAX_ALREADY_REFUNDED = 16;
  REFUND_PAYMENT_ERROR_CODE_TAX_REFUND_AMOUNT_EXCEEDS_PAYMENT_TAX = 17;
  REFUND_PAYMENT_ERROR_CODE_PARTIAL_REFUNDS_NOT_SUPPORTED = 18;
  REFUND_PAYMENT_ERROR_CODE_UNKNOWN_ERROR = 19;
  REFUND_PAYMENT_ERROR_CODE_NO_VERCEL_DATA = 20;
  REFUND_PAYMENT_ERROR_CODE_VERCEL_INSTALLATION_DELETED_AND_FINALIZED = 21;
}
