package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model;

import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_BRAINTREE_TRANSACTION_NOT_FOUND;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_INVALID_REFUND_TYPE;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_LOCKING_INVOICE_FAILED;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_NO_BRAINTREE_DATA;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_NO_STRIPE_DATA;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_NO_VERCEL_DATA;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_PARTIAL_REFUNDS_NOT_SUPPORTED;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_PAYMENT_NOT_FOUND;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_REASON_REQUIRED;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_REFUND_AMOUNT_EXCEEDS_REMAINING;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_REFUND_FAILED;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_REVENUE_REASON_REQUIRED;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_STRIPE_CARD_EXCEPTION;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_STRIPE_EXCEPTION;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_TAX_ALREADY_REFUNDED;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_TAX_REFUND_AMOUNT_EXCEEDS_PAYMENT_TAX;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_UNKNOWN_ERROR;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_VERCEL_INSTALLATION_DELETED_AND_FINALIZED;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_WRONG_PAYMENT_METHOD_TO_REFUND;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_WRONG_PAYMENT_STATUS_FOR_REFUND;
import static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode.REFUND_PAYMENT_ERROR_CODE_ZERO_AMOUNT;

import com.xgen.cloud.common.model._public.error.ErrorCode;

public enum RefundPaymentErrorCode implements ErrorCode {
  PAYMENT_NOT_FOUND(
      "The payment to refund was not found or is not associated with the current organization"),
  WRONG_PAYMENT_METHOD_TO_REFUND("The payment method for this payment does not support refunds"),
  REASON_REQUIRED("A reason must be specified for all refunds."),
  REVENUE_REASON_REQUIRED("Revenue Reason must be specified."),
  WRONG_PAYMENT_STATUS_FOR_REFUND("The payment is not in a state that can be refunded"),
  REFUND_AMOUNT_EXCEEDS_REMAINING(
      "The requested refund amount exceeds the remaining unrefunded charges."),
  INVALID_REFUND_TYPE("Invalid refund type specified"),
  LOCKING_INVOICE_FAILED("Failed to lock the invoice for refund"),
  REFUND_FAILED("The requested refund has failed.", "Refund failed: %s"),
  BRAINTREE_TRANSACTION_NOT_FOUND("The transaction you specified is not found."),
  ZERO_AMOUNT("Refund requested for $0.00"),
  NO_BRAINTREE_DATA(
      "Attempt to issue a refund for a payment with no Braintree data",
      "Attempt to issue a refund for a payment with no Braintree data: invoiceId=%s"
          + " paymentId=%s amountCents=%s"),
  NO_STRIPE_DATA(
      "Attempt to issue a refund for a payment with no Stripe data",
      "Attempt to issue a refund for a payment with no Stripe data: "
          + "invoiceId=%s paymentId=%s, amountCents=%s"),
  STRIPE_EXCEPTION("Stripe exception"),
  STRIPE_CARD_EXCEPTION("Stripe card exception"),
  TAX_ALREADY_REFUNDED("Tax has already been refunded on the payment"),
  TAX_REFUND_AMOUNT_EXCEEDS_PAYMENT_TAX("Tax refund amount cents exceeds tax on payment"),
  UNKNOWN_ERROR("An unknown error occurred", "An unknown error occurred: %s"),
  NO_VERCEL_DATA_IN_ENTITY("Entity has no Vercel data"),
  PARTIAL_REFUND_NOT_SUPPORTED("Only full refunds can be issued for this payment method"),
  VERCEL_INSTALLATION_DELETED_AND_FINALIZED(
      "Refunds are not allowed for installations deleted more than 24 hours ago");

  private final String message;

  private final String messageFormat;

  RefundPaymentErrorCode(String message, String messageFormat) {
    this.message = message;
    this.messageFormat = messageFormat;
  }

  RefundPaymentErrorCode(String message) {
    this(message, null);
  }

  RefundPaymentErrorCode() {
    this(null, null);
  }

  public static RefundPaymentErrorCode fromGrpcResponse(
      com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode errorCode) {
    return switch (errorCode) {
      case REFUND_PAYMENT_ERROR_CODE_PAYMENT_NOT_FOUND -> PAYMENT_NOT_FOUND;
      case REFUND_PAYMENT_ERROR_CODE_WRONG_PAYMENT_METHOD_TO_REFUND ->
          WRONG_PAYMENT_METHOD_TO_REFUND;
      case REFUND_PAYMENT_ERROR_CODE_REASON_REQUIRED -> REASON_REQUIRED;
      case REFUND_PAYMENT_ERROR_CODE_REVENUE_REASON_REQUIRED -> REVENUE_REASON_REQUIRED;
      case REFUND_PAYMENT_ERROR_CODE_WRONG_PAYMENT_STATUS_FOR_REFUND ->
          WRONG_PAYMENT_STATUS_FOR_REFUND;
      case REFUND_PAYMENT_ERROR_CODE_REFUND_AMOUNT_EXCEEDS_REMAINING ->
          REFUND_AMOUNT_EXCEEDS_REMAINING;
      case REFUND_PAYMENT_ERROR_CODE_INVALID_REFUND_TYPE -> INVALID_REFUND_TYPE;
      case REFUND_PAYMENT_ERROR_CODE_LOCKING_INVOICE_FAILED -> LOCKING_INVOICE_FAILED;
      case REFUND_PAYMENT_ERROR_CODE_REFUND_FAILED -> REFUND_FAILED;
      case REFUND_PAYMENT_ERROR_CODE_BRAINTREE_TRANSACTION_NOT_FOUND ->
          BRAINTREE_TRANSACTION_NOT_FOUND;
      case REFUND_PAYMENT_ERROR_CODE_ZERO_AMOUNT -> ZERO_AMOUNT;
      case REFUND_PAYMENT_ERROR_CODE_NO_BRAINTREE_DATA -> NO_BRAINTREE_DATA;
      case REFUND_PAYMENT_ERROR_CODE_NO_STRIPE_DATA -> NO_STRIPE_DATA;
      case REFUND_PAYMENT_ERROR_CODE_STRIPE_EXCEPTION -> STRIPE_EXCEPTION;
      case REFUND_PAYMENT_ERROR_CODE_STRIPE_CARD_EXCEPTION -> STRIPE_CARD_EXCEPTION;
      case REFUND_PAYMENT_ERROR_CODE_TAX_ALREADY_REFUNDED -> TAX_ALREADY_REFUNDED;
      case REFUND_PAYMENT_ERROR_CODE_TAX_REFUND_AMOUNT_EXCEEDS_PAYMENT_TAX ->
          TAX_REFUND_AMOUNT_EXCEEDS_PAYMENT_TAX;
      case REFUND_PAYMENT_ERROR_CODE_PARTIAL_REFUNDS_NOT_SUPPORTED -> PARTIAL_REFUND_NOT_SUPPORTED;
      case REFUND_PAYMENT_ERROR_CODE_NO_VERCEL_DATA -> NO_VERCEL_DATA_IN_ENTITY;
      case REFUND_PAYMENT_ERROR_CODE_VERCEL_INSTALLATION_DELETED_AND_FINALIZED ->
          VERCEL_INSTALLATION_DELETED_AND_FINALIZED;
      case UNRECOGNIZED,
              REFUND_PAYMENT_ERROR_CODE_UNSPECIFIED,
              REFUND_PAYMENT_ERROR_CODE_UNKNOWN_ERROR ->
          UNKNOWN_ERROR;
    };
  }

  public static com.xgen.cloud.services.payments.proto.RefundPaymentErrorCode toGrpcResponse(
      RefundPaymentErrorCode errorCode) {
    return switch (errorCode) {
      case PAYMENT_NOT_FOUND -> REFUND_PAYMENT_ERROR_CODE_PAYMENT_NOT_FOUND;
      case WRONG_PAYMENT_METHOD_TO_REFUND ->
          REFUND_PAYMENT_ERROR_CODE_WRONG_PAYMENT_METHOD_TO_REFUND;
      case REASON_REQUIRED -> REFUND_PAYMENT_ERROR_CODE_REASON_REQUIRED;
      case REVENUE_REASON_REQUIRED -> REFUND_PAYMENT_ERROR_CODE_REVENUE_REASON_REQUIRED;
      case WRONG_PAYMENT_STATUS_FOR_REFUND ->
          REFUND_PAYMENT_ERROR_CODE_WRONG_PAYMENT_STATUS_FOR_REFUND;
      case REFUND_AMOUNT_EXCEEDS_REMAINING ->
          REFUND_PAYMENT_ERROR_CODE_REFUND_AMOUNT_EXCEEDS_REMAINING;
      case INVALID_REFUND_TYPE -> REFUND_PAYMENT_ERROR_CODE_INVALID_REFUND_TYPE;
      case LOCKING_INVOICE_FAILED -> REFUND_PAYMENT_ERROR_CODE_LOCKING_INVOICE_FAILED;
      case REFUND_FAILED -> REFUND_PAYMENT_ERROR_CODE_REFUND_FAILED;
      case BRAINTREE_TRANSACTION_NOT_FOUND ->
          REFUND_PAYMENT_ERROR_CODE_BRAINTREE_TRANSACTION_NOT_FOUND;
      case ZERO_AMOUNT -> REFUND_PAYMENT_ERROR_CODE_ZERO_AMOUNT;
      case NO_BRAINTREE_DATA -> REFUND_PAYMENT_ERROR_CODE_NO_BRAINTREE_DATA;
      case NO_STRIPE_DATA -> REFUND_PAYMENT_ERROR_CODE_NO_STRIPE_DATA;
      case STRIPE_EXCEPTION -> REFUND_PAYMENT_ERROR_CODE_STRIPE_EXCEPTION;
      case STRIPE_CARD_EXCEPTION -> REFUND_PAYMENT_ERROR_CODE_STRIPE_CARD_EXCEPTION;
      case TAX_ALREADY_REFUNDED -> REFUND_PAYMENT_ERROR_CODE_TAX_ALREADY_REFUNDED;
      case TAX_REFUND_AMOUNT_EXCEEDS_PAYMENT_TAX ->
          REFUND_PAYMENT_ERROR_CODE_TAX_REFUND_AMOUNT_EXCEEDS_PAYMENT_TAX;
      case NO_VERCEL_DATA_IN_ENTITY -> REFUND_PAYMENT_ERROR_CODE_NO_VERCEL_DATA;
      case PARTIAL_REFUND_NOT_SUPPORTED -> REFUND_PAYMENT_ERROR_CODE_PARTIAL_REFUNDS_NOT_SUPPORTED;
      case VERCEL_INSTALLATION_DELETED_AND_FINALIZED ->
          REFUND_PAYMENT_ERROR_CODE_VERCEL_INSTALLATION_DELETED_AND_FINALIZED;
      case UNKNOWN_ERROR -> REFUND_PAYMENT_ERROR_CODE_UNKNOWN_ERROR;
    };
  }

  @Override
  public String getMessage() {
    return message == null ? name() : message;
  }

  @Override
  public String formatMessage(Object... params) {
    return messageFormat == null ? getMessage() : String.format(messageFormat, params);
  }
}
