syntax = "proto3";

// leaf of the full qualified package name is used as a serviceId
package com.xgen.cloud.services.payments;

import "server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/proto/common.proto";

option java_multiple_files = true;
option java_outer_classname = "RefundQueryingProto";
option java_package = "com.xgen.cloud.services.payments.proto";

// Based on https://developers.google.com/protocol-buffers/docs/style
service RefundQueryingService {
  rpc GetRefundsByInvoiceId(GetRefundsByInvoiceIdRequest) returns (GetRefundsByInvoiceIdResponse) {}
  rpc GetRefundsByPaymentId(GetRefundsByPaymentIdRequest) returns (GetRefundsByPaymentIdResponse) {}
}

message GetRefundsByInvoiceIdRequest {
  string invoice_id = 1;
}

message GetRefundsByInvoiceIdResponse {
  repeated Refund refunds = 1;
}

message GetRefundsByPaymentIdRequest {
  string payment_id = 1;
}

message GetRefundsByPaymentIdResponse {
  repeated Refund refunds = 1;
}

message Refund {
  string id = 1;
  string org_id = 2;
  string invoice_id = 3;
  string payment_id = 4;
  string created = 5;
  optional string reason = 6;
  optional string credit_id = 7;
  int64 amount_cents = 8;
  optional string stripe_refund_id = 9;
  optional string braintree_refund_id = 10;
  optional RevenueRefundReason revenue_refund_reason = 11;
  int64 amount_tax_cents = 12;
}
