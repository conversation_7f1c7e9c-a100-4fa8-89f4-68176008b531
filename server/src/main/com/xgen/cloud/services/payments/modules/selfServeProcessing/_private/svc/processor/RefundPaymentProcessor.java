package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.processor;

import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.event._public.model.CreateEventRequestDto;
import com.xgen.cloud.payments.events._public.model.RefundProcessedEvent;
import com.xgen.cloud.payments.salestax.calculation._public.svc.TaxTransactionSvc;
import com.xgen.cloud.payments.standalone.common._public.client.AuditClient;
import com.xgen.cloud.payments.standalone.common._public.client.RefundProcessedEventClient;
import com.xgen.cloud.services.event.proto.SourceMessage;
import com.xgen.cloud.services.event.proto.Visibility;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.PaymentDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.RefundDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentResult;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundRequest;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.Payment.Status;
import com.xgen.svc.mms.model.billing.Refund;
import java.time.Clock;
import java.time.LocalDateTime;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class RefundPaymentProcessor {
  private static final Logger LOG = LoggerFactory.getLogger(RefundPaymentProcessor.class);

  protected final RefundDao refundDao;
  protected final PaymentDao paymentDao;
  protected final TaxTransactionSvc taxTransactionSvc;
  protected final AuditClient auditClient;
  protected final RefundProcessedEventClient refundProcessedEventClient;
  protected final Clock clock;

  public RefundPaymentProcessor(
      RefundDao refundDao,
      PaymentDao paymentDao,
      TaxTransactionSvc taxTransactionSvc,
      AuditClient auditClient,
      RefundProcessedEventClient refundProcessedEventClient,
      Clock clock) {
    this.refundDao = refundDao;
    this.paymentDao = paymentDao;
    this.taxTransactionSvc = taxTransactionSvc;
    this.auditClient = auditClient;
    this.refundProcessedEventClient = refundProcessedEventClient;
    this.clock = clock;
  }

  public abstract RefundPaymentResult refundPayment(
      Invoice invoice, RefundRequest refundRequest, SourceMessage source);

  protected void handleRefundSucceeded(
      Invoice invoice, RefundRequest refundRequest, Refund refund, SourceMessage source) {
    refundDao.save(refund);

    Status updatedPaymentStatus;
    if (refundRequest.hasIntentToForgiveCharges()) {
      updatedPaymentStatus = Status.FORGIVEN;
    } else {
      // Invoices that are submitted but not paid should not be considered refunded. This will
      // ensure rev rec doesn’t include these amounts in its refund aggregation calculation.
      // Currently, the only self-serve payment method that can be in the INVOICED/FAILED state is
      // Vercel.
      updatedPaymentStatus =
          refundRequest.getPayment().getStatus() == Status.INVOICED
                  || refundRequest.getPayment().getStatus() == Status.FAILED
              ? Status.CANCELLED
              : Status.REFUNDED;
    }
    // Ensure amountPaidCents doesn't go negative, especially for INVOICED payments
    long currentAmountPaid = refundRequest.getPayment().getAmountPaidCents();
    long refundAmount = refundRequest.getRefundAmountCents();
    // Vercel payments in INVOICED status may have $0 amount paid, but we still want to refund
    // the full amount billed.
    long newAmountPaidCents = Math.max(0L, currentAmountPaid - refundAmount);

    paymentDao.applyRefundToPayment(
        refundRequest.getPayment().getId(),
        newAmountPaidCents,
        new Date(),
        updatedPaymentStatus,
        refundRequest.getRefundReason());

    RefundProcessedEvent event =
        RefundProcessedEvent.builder()
            .setPaymentId(refundRequest.getPayment().getId())
            .setInvoiceId(invoice.getId())
            .setOrgId(refundRequest.getPayment().getOrgId())
            .setRefundAmountCents(refundRequest.getRefundAmountCents())
            .setRefundReason(refundRequest.getRefundReason())
            .setProcessedAt(LocalDateTime.now(clock))
            .build();
    auditClient.createEvent(
        CreateEventRequestDto.builder()
            .eventPayload(event)
            .orgId(event.getOrgId())
            .visibility(Visibility.VISIBILITY_ALL)
            .source(source)
            .build());

    long totalTaxRefundedCents =
        refundDao.getAmountTaxRefundedCentsByPaymentId(refundRequest.getPayment().getId());
    if (refundRequest.getPayment().getSalesTaxStatus() != Payment.SalesTaxStatus.REFUNDED
        && (refundRequest.getRefundAmountCents() == refundRequest.getPayment().getAmountPaidCents()
            || totalTaxRefundedCents == refundRequest.getPayment().getSalesTaxCents())) {
      // void the transaction in AvaTax when the entire payment has been refunded or if tax was
      // fully refunded
      taxTransactionSvc.refundSalesTax(refundRequest.getPayment());
    }

    refundProcessedEventClient.publishEvent(event);
  }
}
