load("//server/src/main:rules.bzl", "main_library")

main_library(
    name = "svc",
    srcs = glob(["**/*.java"]),
    deps = [
        "//server/src/main/com/xgen/cloud/billing",
        "//server/src/main/com/xgen/cloud/billingplatform/common",
        "//server/src/main/com/xgen/cloud/billingplatform/invoice",
        "//server/src/main/com/xgen/cloud/billingplatform/process/invoicelocking",
        "//server/src/main/com/xgen/cloud/billingshared/common/util",
        "//server/src/main/com/xgen/cloud/common/auditInfo",
        "//server/src/main/com/xgen/cloud/common/model",
        "//server/src/main/com/xgen/cloud/common/retry",
        "//server/src/main/com/xgen/cloud/common/util",
        "//server/src/main/com/xgen/cloud/event",
        "//server/src/main/com/xgen/cloud/group",
        "//server/src/main/com/xgen/cloud/organization",
        "//server/src/main/com/xgen/cloud/partners/vercel/billing",
        "//server/src/main/com/xgen/cloud/partners/vercel/sdk/client",
        "//server/src/main/com/xgen/cloud/partners/vercel/sdk/model",
        "//server/src/main/com/xgen/cloud/payments/braintree",
        "//server/src/main/com/xgen/cloud/payments/common",
        "//server/src/main/com/xgen/cloud/payments/currency",
        "//server/src/main/com/xgen/cloud/payments/events",
        "//server/src/main/com/xgen/cloud/payments/exception",
        "//server/src/main/com/xgen/cloud/payments/processing",
        "//server/src/main/com/xgen/cloud/payments/salestax/calculation",
        "//server/src/main/com/xgen/cloud/payments/salestax/validation",
        "//server/src/main/com/xgen/cloud/payments/standalone/common",
        "//server/src/main/com/xgen/cloud/payments/standalone/locking",
        "//server/src/main/com/xgen/cloud/payments/standalone/partners/common",
        "//server/src/main/com/xgen/cloud/payments/stripe",
        "//server/src/main/com/xgen/cloud/services/payments/modules/paymentMethod",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_private/dao",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_private/logging",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_private/model",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_private/utils",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/model",
        "//server/src/main/com/xgen/cloud/services/payments/modules/selfServeProcessing/_public/svc",
        "//server/src/main/com/xgen/svc/mms/model/billing",
        "//systems/events:event_schemas_java_proto",  # keep
        "//systems/events:java_api_grpc_v0",  # keep: provides generated java classes `com.xgen.cloud.services.event.proto.*`, gazelle should not remove this.
        "//third_party:guava",
        "@maven//:com_braintreepayments_gateway_braintree_java",
        "@maven//:com_nimbusds_oauth2_oidc_sdk",
        "@maven//:com_stripe_stripe_java",
        "@maven//:com_xgen_devtools_configservicesdk",
        "@maven//:commons_lang_commons_lang",
        "@maven//:io_micrometer_micrometer_core",
        "@maven//:io_projectreactor_reactor_core",
        "@maven//:io_prometheus_simpleclient",
        "@maven//:jakarta_annotation_jakarta_annotation_api",
        "@maven//:jakarta_inject_jakarta_inject_api",
        "@maven//:net_javacrumbs_shedlock_shedlock_core",
        "@maven//:net_logstash_logback_logstash_logback_encoder",
        "@maven//:org_apache_commons_commons_lang3",
        "@maven//:org_mongodb_bson",
        "@maven//:org_slf4j_slf4j_api",
    ],
)
