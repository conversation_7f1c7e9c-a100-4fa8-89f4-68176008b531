package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc;

import com.xgen.svc.mms.model.billing.LineItem;
import java.util.List;
import reactor.core.publisher.Mono;

/** Service interface for credit application operations in the self-serve processing module. */
public interface CreditApplicationSvc {

  /**
   * Un-applies credit and returns the amount of credit unapplied.
   *
   * @param creditLineItems List of credit line items to unapply
   * @param reason Reason for unapplying the credits
   * @return Mono containing the amount of credit un-applied in cents
   */
  Mono<Long> unApplyCredits(List<LineItem> creditLineItems, String reason);
}
