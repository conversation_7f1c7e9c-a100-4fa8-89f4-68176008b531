package com.xgen.cloud.services.payments.modules.credits._public.svc;

import com.xgen.cloud.services.payments.modules.common._public.model.DateRangeSpec;
import com.xgen.cloud.services.payments.modules.common._public.model.SortSpec;
import com.xgen.cloud.services.payments.modules.credits._private.dao.AppliedCreditDao.GroupingField;
import com.xgen.cloud.services.payments.modules.credits._private.model.AppliedCredit;
import com.xgen.cloud.services.payments.modules.credits._public.model.AppliedCreditAggregate;
import com.xgen.cloud.services.payments.modules.credits._public.model.AppliedCreditWithMetadata;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.util.Set;
import org.bson.types.ObjectId;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface AppliedCreditQuerySvc {

  /**
   * Retrieves credit usage data for the specified invoices and credits within a date range. Results
   * are aggregated by the specified grouping fields. This method is useful for generating credit
   * usage reports and analyzing credit consumption patterns.
   *
   * @param invoiceIds Collection of invoice IDs to filter by (optional, can be null or empty)
   * @param creditIds Collection of credit IDs to filter by (optional, can be null or empty)
   * @param dateRangeSpec Date range specification with comparison operators (optional, can be null)
   * @param groupingFields Set of GroupingField enums to group by (must be non-empty)
   * @param sortSpec Optional sort specification for ordering results (null for no sorting)
   * @param hasOverCommitmentFlag Optional filter for over-commitment flag (null means no filter)
   * @return A Flux of CreditUsageAggregate records matching the criteria, optionally sorted
   */
  Flux<AppliedCreditAggregate> getCreditUsageData(
      Set<ObjectId> invoiceIds,
      Set<ObjectId> creditIds,
      @Nullable DateRangeSpec dateRangeSpec,
      Set<GroupingField> groupingFields,
      @Nullable SortSpec sortSpec,
      @Nullable Boolean hasOverCommitmentFlag);

  /**
   * Retrieves credit usage data grouped by credit ID with metadata automatically included. This is
   * a convenience method for the common use case of getting usage data per credit with metadata.
   * Results are grouped by credit ID only
   *
   * @param invoiceIds Collection of invoice IDs to filter by
   * @param creditIds Collection of credit IDs to filter by
   * @return A Flux of CreditUsageWithMetadata records grouped by credit ID with metadata included
   */
  Flux<AppliedCreditWithMetadata> getCreditUsageWithMetadata(
      Set<ObjectId> invoiceIds, Set<ObjectId> creditIds);

  /**
   * Retrieves the total amount of applied credits in cents for the specified filters. This method
   * provides an efficient way to get the sum of all applied credits without needing to aggregate
   * individual records.
   *
   * @param invoiceIds Collection of invoice IDs to filter by (optional, can be null or empty)
   * @param creditIds Collection of credit IDs to filter by (optional, can be null or empty)
   * @param dateRangeSpec Date range specification with comparison operators (optional, can be null)
   * @param hasOverCommitmentFlag Filter by over commitment flag (optional, can be null)
   * @return A Mono containing the total applied credits amount in cents
   */
  Mono<Long> getTotalAppliedCreditCents(
      Set<ObjectId> invoiceIds,
      Set<ObjectId> creditIds,
      @Nullable DateRangeSpec dateRangeSpec,
      @Nullable Boolean hasOverCommitmentFlag);

  /**
   * Retrieves all applied credits for the specified invoice ID within the given bill date range.
   *
   * <p>This method is useful for retrieving detailed credit usage information for a specific
   * invoice.
   *
   * @param invoiceId The invoice ID to retrieve applied credits for (must not be null)
   * @param billDates The set of bill dates to filter by (must not be null or empty)
   * @return A Flux containing all applied credits for the given invoice ID within the date range
   */
  Flux<AppliedCredit> getAppliedCreditsForInvoice(ObjectId invoiceId, Set<LocalDate> billDates);
}
