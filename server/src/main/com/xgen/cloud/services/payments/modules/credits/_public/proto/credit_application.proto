syntax = "proto3";

package com.xgen.cloud.services.payments;

import "server/src/main/com/xgen/cloud/services/payments/modules/credits/_public/proto/credit_application_common.proto";

option java_multiple_files = true;
option java_outer_classname = "CreditUsageProto";
option java_package = "com.xgen.cloud.services.payments.proto";

service CreditApplicationService {
  rpc ApplyCredits(ApplyCreditsRequest) returns (ApplyCreditsResponse);

  rpc UnApplyCredits(UnApplyCreditsRequest) returns (UnApplyCreditsResponse);

  rpc GetAppliedCredits(GetAppliedCreditsRequest) returns (GetAppliedCreditsResponse);

  // returns items grouped only by credit id
  rpc GetAppliedCreditsWithMetadata(GetAppliedCreditsWithMetadataRequest) returns (GetAppliedCreditsWithMetadataResponse);

  rpc GetAppliedCreditsTotal(GetAppliedCreditsTotalRequest) returns (GetAppliedCreditsTotalResponse);

  // applies specified credits to an invoice for a specific bill date range
  // currently only used by retroactive credit application
  // reserved for internal/payment team use only -- may be removed in the future
  rpc ApplyCreditsInternal(ApplyCreditsInternalRequest) returns (ApplyCreditsInternalResponse);

  // applies specified credits to a set of invoices based on a line item filter
  rpc BackfillAppliedCredits(BackfillAppliedCreditsRequest) returns (BackfillAppliedCreditsResponse);
}

message ApplyCreditsRequest {
  repeated string invoice_ids = 1;
  string bill_date = 3; // ISO 8601 date string
}

message ApplyCreditsResponse {
  repeated AppliedCreditSummary applied_credit_summary = 1;
}

message UnApplyCreditsRequest {
  repeated string credit_ids = 1;
  repeated string invoice_ids = 2;
  DateRangeFilter date_range_filter = 3;
  optional bool has_over_commitment_flag = 6;
  string reason = 7;
}

message UnApplyCreditsResponse {
  int64 unapplied_cents_total = 1;
}

message GetAppliedCreditsRequest {
  repeated string credit_ids = 1;
  repeated string invoice_ids = 2;
  DateRangeFilter date_range_filter = 3;
  GroupingFilter grouping_filter = 4;
  SortFilter sort_filter = 5;
  optional bool has_over_commitment_flag = 6;
}

message GetAppliedCreditsResponse {
  repeated AppliedCreditAggregate credit_usage = 1;
}

message GetAppliedCreditsWithMetadataRequest {
  repeated string credit_ids = 1;
  repeated string invoice_ids = 2;
}

message GetAppliedCreditsWithMetadataResponse {
  repeated AppliedCreditAggregate credit_usage = 1;
}

message GetAppliedCreditsTotalRequest {
  repeated string credit_ids = 1;
  repeated string invoice_ids = 2;
  DateRangeFilter date_range_filter = 3;
  optional bool has_over_commitment_flag = 6;
}

message GetAppliedCreditsTotalResponse {
  int64 total_cents = 1;
}

message ApplyCreditsInternalRequest {
  string invoice_id = 1;
  string start_bill_date = 3; // ISO 8601 date string
  string end_bill_date = 4; // ISO 8601 date string

  // apply usage to specific credits
  repeated string credit_id = 20;
}

message ApplyCreditsInternalResponse {
  int64 applied_cents_total = 1;
}

message BackfillAppliedCreditsRequest {
  repeated string invoice_ids = 1;

  oneof credit_identifier {
    string credit_id = 2;
    string activation_code = 3;
  }

  oneof line_item_filter {
    string payment_id = 4; // apply to all line items associated with this payment
    string line_item_id = 5; // apply to all line items after this line item
  }

  repeated string exclude_payment_ids_from_validation = 7;
  bool backfill_linked_invoices = 8;
  bool allow_credits_from_other_orgs = 9;
}

message BackfillAppliedCreditsResponse {
  repeated AppliedCreditSummary applied_credit_summary = 1;
}
