package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao;

import com.xgen.svc.mms.model.billing.Credit;
import org.bson.types.ObjectId;
import reactor.core.publisher.Mono;

public interface CreditDao {
  Mono<Void> addTotalBilledCents(ObjectId creditId, long totalBilledCents);

  Mono<Void> addAmountRemainingCents(ObjectId creditId, long amountRemainingCents);

  Mono<Credit> findById(ObjectId creditId);
}
