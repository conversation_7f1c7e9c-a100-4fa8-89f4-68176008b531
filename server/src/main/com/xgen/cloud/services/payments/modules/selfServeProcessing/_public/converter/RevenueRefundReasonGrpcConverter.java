package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.converter;

import static com.xgen.cloud.services.payments.proto.RevenueRefundReason.REVENUE_REFUND_REASON_ACCIDENTAL_USAGE;
import static com.xgen.cloud.services.payments.proto.RevenueRefundReason.REVENUE_REFUND_REASON_ACTIVATION_CODE;
import static com.xgen.cloud.services.payments.proto.RevenueRefundReason.REVENUE_REFUND_REASON_BILLING_CORRECTION;
import static com.xgen.cloud.services.payments.proto.RevenueRefundReason.REVENUE_REFUND_REASON_CREDIT_CARD_CHANGED;
import static com.xgen.cloud.services.payments.proto.RevenueRefundReason.REVENUE_REFUND_REASON_CREDIT_REBALANCING;
import static com.xgen.cloud.services.payments.proto.RevenueRefundReason.REVENUE_REFUND_REASON_ELASTIC_INVOICING;
import static com.xgen.cloud.services.payments.proto.RevenueRefundReason.REVENUE_REFUND_REASON_EMPLOYEE_USE;
import static com.xgen.cloud.services.payments.proto.RevenueRefundReason.REVENUE_REFUND_REASON_SALES_TAX_REFUND;
import static com.xgen.cloud.services.payments.proto.RevenueRefundReason.REVENUE_REFUND_REASON_VAT_REFUND;

import com.xgen.svc.mms.model.billing.RevenueRefundReason;

public class RevenueRefundReasonGrpcConverter {
  public static com.xgen.cloud.services.payments.proto.RevenueRefundReason toGrpc(
      RevenueRefundReason revenueRefundReason) {
    return switch (revenueRefundReason) {
      case ACCIDENTAL_USAGE -> REVENUE_REFUND_REASON_ACCIDENTAL_USAGE;
      case ACTIVATION_CODE -> REVENUE_REFUND_REASON_ACTIVATION_CODE;
      case BILLING_CORRECTION -> REVENUE_REFUND_REASON_BILLING_CORRECTION;
      case ELASTIC_INVOICING -> REVENUE_REFUND_REASON_ELASTIC_INVOICING;
      case VAT_REFUND -> REVENUE_REFUND_REASON_VAT_REFUND;
      case SALES_TAX_REFUND -> REVENUE_REFUND_REASON_SALES_TAX_REFUND;
      case EMPLOYEE_USE -> REVENUE_REFUND_REASON_EMPLOYEE_USE;
      case CREDIT_REBALANCING -> REVENUE_REFUND_REASON_CREDIT_REBALANCING;
      case CREDIT_CARD_CHANGED -> REVENUE_REFUND_REASON_CREDIT_CARD_CHANGED;
    };
  }

  public static RevenueRefundReason fromGrpc(
      com.xgen.cloud.services.payments.proto.RevenueRefundReason revenueRefundReason) {
    return switch (revenueRefundReason) {
      case REVENUE_REFUND_REASON_ACCIDENTAL_USAGE -> RevenueRefundReason.ACCIDENTAL_USAGE;
      case REVENUE_REFUND_REASON_ACTIVATION_CODE -> RevenueRefundReason.ACTIVATION_CODE;
      case REVENUE_REFUND_REASON_BILLING_CORRECTION -> RevenueRefundReason.BILLING_CORRECTION;
      case REVENUE_REFUND_REASON_ELASTIC_INVOICING -> RevenueRefundReason.ELASTIC_INVOICING;
      case REVENUE_REFUND_REASON_VAT_REFUND -> RevenueRefundReason.VAT_REFUND;
      case REVENUE_REFUND_REASON_SALES_TAX_REFUND -> RevenueRefundReason.SALES_TAX_REFUND;
      case REVENUE_REFUND_REASON_EMPLOYEE_USE -> RevenueRefundReason.EMPLOYEE_USE;
      case REVENUE_REFUND_REASON_CREDIT_REBALANCING -> RevenueRefundReason.CREDIT_REBALANCING;
      case REVENUE_REFUND_REASON_CREDIT_CARD_CHANGED -> RevenueRefundReason.CREDIT_CARD_CHANGED;
      case UNRECOGNIZED, REVENUE_REFUND_REASON_UNSPECIFIED ->
          throw new IllegalArgumentException(
              "Unknown revenue refund reason: " + revenueRefundReason);
    };
  }
}
