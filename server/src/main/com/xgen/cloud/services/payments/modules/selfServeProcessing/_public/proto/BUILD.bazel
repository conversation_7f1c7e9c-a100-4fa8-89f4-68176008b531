load("@com_google_protobuf//bazel:proto_library.bzl", "proto_library")
load("@rules_proto_grpc//java:defs.bzl", "java_grpc_library")
load("//scripts/buf:defs.bzl", "buf_test")

filegroup(
    name = "srcs",
    srcs = glob(["*.proto"]),
    visibility = [
        "//scripts/buf:__subpackages__",
        "//server/src/main:__pkg__",
    ],
)

proto_library(
    name = "proto",
    srcs = glob(["*.proto"]),
    visibility = ["//server/src/main/com/xgen/cloud/services/payments:__subpackages__"],
    deps = [
        "@com_google_protobuf//:empty_proto",
    ],
)

java_grpc_library(
    name = "java_grpc",
    protos = [":proto"],
    visibility = [
        "//server/src/main/com/xgen/cloud:__subpackages__",
        "//server/src/test/com/xgen/cloud:__subpackages__",
        "//server/src/unit/com/xgen/cloud:__subpackages__",
    ],
)

buf_test(
    name = "buf_test",
    debug = True,
    protos = [
        ":srcs",
    ],
)
