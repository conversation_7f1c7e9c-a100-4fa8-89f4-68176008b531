package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.processor;

import static com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.ChargePaymentErrorCode.FAILED_TO_CALCULATE_MANDATE_MAXIMUM;
import static com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.ChargePaymentErrorCode.FAILED_TO_PROCESS_CREATED_PAYMENT;
import static com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.ChargePaymentErrorCode.FAILED_TO_PROCESS_PROCESSING_PAYMENT;
import static net.logstash.logback.argument.StructuredArguments.entries;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.google.common.annotations.VisibleForTesting;
import com.stripe.exception.CardException;
import com.stripe.exception.IdempotencyException;
import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.model.PaymentIntent;
import com.stripe.param.PaymentIntentCreateParams.CaptureMethod;
import com.xgen.cloud.billing._public.svc.exception.BillingErrorCode;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.common.auditInfo._public.model.EventSource;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.location.Country;
import com.xgen.cloud.event._public.model.CreateEventRequestDto;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.payments.common._public.config.CommonConfig;
import com.xgen.cloud.payments.currency._public.model.Currency;
import com.xgen.cloud.payments.currency._public.svc.CurrencyConversionSvc;
import com.xgen.cloud.payments.events._public.model.CardChargeFailedEvent;
import com.xgen.cloud.payments.events._public.model.CardChargeProcessingEvent;
import com.xgen.cloud.payments.events._public.model.CardChargeSuccessfulEvent;
import com.xgen.cloud.payments.events._public.model.PaymentProcessedEvent;
import com.xgen.cloud.payments.events._public.model.StripeCharge;
import com.xgen.cloud.payments.exception._public.stripe.constant.StripeApiErrorCode;
import com.xgen.cloud.payments.processing._public.svc.ChargePaymentRateLimiterProvider;
import com.xgen.cloud.payments.standalone.common._public.client.AuditClient;
import com.xgen.cloud.payments.standalone.common._public.client.InvoiceClient;
import com.xgen.cloud.payments.standalone.common._public.client.OrganizationClient;
import com.xgen.cloud.payments.standalone.common._public.client.PaymentMethodRestClient;
import com.xgen.cloud.payments.standalone.common._public.client.PaymentProcessedEventClient;
import com.xgen.cloud.payments.stripe._public.client.StripeInterface;
import com.xgen.cloud.services.event.proto.SourceMessage;
import com.xgen.cloud.services.event.proto.Visibility;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.svc.PaymentMethodSvc;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.PaymentDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.logging.PaymentsLoggerMarker;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.utils.StripeMetadataUtils;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.ChargePaymentErrorCode;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.ChargePaymentResult;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.PaymentAuthenticationRequest;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.StripeApiError;
import com.xgen.svc.mms.model.billing.BillingAccount;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.Payment.Status;
import com.xgen.svc.mms.model.billing.PaymentIntentStatus;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import io.prometheus.client.Counter;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import jakarta.inject.Singleton;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class StripeChargePaymentProcessor extends ChargePaymentProcessor {

  private static final Logger LOG = LoggerFactory.getLogger(StripeChargePaymentProcessor.class);

  private static final String INDIA_LABEL = "is_india";
  private static final String OFF_SESSION_LABEL = "off_session";
  private static final String NEW_PAYMENT_STATUS_LABEL = "new_payment_status";
  private static final String FIRST_ATTEMPT_LABEL = "is_first_attempt_to_charge_payment";
  private static final String MANDATE_STATUS_LABEL = "has_active_mandate";
  private static final String MORE_THAN_5K_INR = "more_than_5k_inr";

  private static final String[] PAYMENT_INTENT_CHARGE_LABELS = {
    INDIA_LABEL,
    OFF_SESSION_LABEL,
    NEW_PAYMENT_STATUS_LABEL,
    FIRST_ATTEMPT_LABEL,
    MANDATE_STATUS_LABEL,
    MORE_THAN_5K_INR,
  };

  private static final Counter STRIPE_PAYMENT_INTENT_COUNTER =
      Counter.build()
          .name("billing_payment_payment_intent_charged_updated_total")
          .labelNames(PAYMENT_INTENT_CHARGE_LABELS)
          .help("counter for payment intents created in stripe.")
          .register();

  private final StripeInterface stripeInterface;
  private final PaymentDao paymentDao;
  private final ChargePaymentRateLimiterProvider rateLimiterProvider;
  private final CurrencyConversionSvc currencyConversionSvc;

  private final CommonConfig commonConfig;
  private final PaymentProcessedEventClient paymentProcessedEventClient;
  private final AuditClient auditClient;

  private final PaymentMethodRestClient paymentMethodClient;

  private final InvoiceClient invoiceClient;
  private final OrganizationClient organizationClient;
  private final Provider<PaymentMethodSvc> paymentMethodSvcProvider;

  @Inject
  public StripeChargePaymentProcessor(
      StripeInterface stripeInterface,
      PaymentDao paymentDao,
      ChargePaymentRateLimiterProvider rateLimiterProvider,
      CurrencyConversionSvc currencyConversionSvc,
      CommonConfig commonConfig,
      PaymentProcessedEventClient paymentProcessedEventClient,
      AuditClient auditClient,
      PaymentMethodRestClient paymentMethodClient,
      InvoiceClient invoiceClient,
      OrganizationClient organizationClient,
      Provider<PaymentMethodSvc> paymentMethodSvcProvider) {
    this.stripeInterface = stripeInterface;
    this.paymentDao = paymentDao;
    this.rateLimiterProvider = rateLimiterProvider;
    this.currencyConversionSvc = currencyConversionSvc;
    this.commonConfig = commonConfig;
    this.paymentProcessedEventClient = paymentProcessedEventClient;
    this.auditClient = auditClient;
    this.paymentMethodClient = paymentMethodClient;
    this.invoiceClient = invoiceClient;
    this.organizationClient = organizationClient;
    this.paymentMethodSvcProvider = paymentMethodSvcProvider;
  }

  private static HashMap<String, Object> buildLogContext(
      Payment payment,
      PaymentMethod paymentMethod,
      Invoice invoice,
      boolean offSession,
      String operation) {
    HashMap<String, Object> context = new HashMap<>();
    context.put("paymentId", payment.getId());
    context.put("paymentMethodId", paymentMethod.getId());
    context.put("billingAccount", paymentMethod.getBillingAccount());
    context.put("offSession", offSession);
    context.put("orgId", payment.getOrgId());
    context.put("invoiceStart", invoice.getStartDate());
    context.put("initialPaymentStatus", payment.getStatus());
    context.put("hasActiveMandate", paymentMethod.hasActiveMandate());
    context.put("operation", operation);
    return context;
  }

  private static Payment.Status getFailedStripePaymentStatus(
      String stripePaymentIntentStatus, @Nullable StripeApiError stripeApiError) {
    return PaymentIntentStatus.REQUIRES_ACTION.equals(stripePaymentIntentStatus)
            || stripeApiError != null
                && StripeApiErrorCode.AUTHENTICATION_REQUIRED.equals(
                    stripeApiError.getApiErrorCode())
        ? Payment.Status.FAILED_AUTHENTICATION
        : Payment.Status.FAILED;
  }

  public static void logStripeException(StripeException stripeException, String logMessage) {
    // IdempotencyException exceptions happen when we retry a payment within 24 hours of the last
    // attempt, and the amount on the new payment intent differs from the original. We log at warn
    // because the issue will go away 24 hours after the original attempt to charge the payment.
    if (stripeException instanceof IdempotencyException) {
      LOG.warn(logMessage, stripeException);
    } else if (stripeException.getCode() == null) {
      LOG.error(logMessage, stripeException);
    } else if (StripeApiErrorCode.WARN.contains(stripeException.getCode())) {
      LOG.warn(logMessage, stripeException);
    } else if (StripeApiErrorCode.FATAL.contains(stripeException.getCode())) {
      LOG.error(PaymentsLoggerMarker.FATAL.getMarker(), logMessage, stripeException);
    } else {
      LOG.error(logMessage, stripeException);
    }
  }

  private boolean shouldRegisterMandateWithPaymentIntent(
      boolean offSession, Invoice invoice, PaymentMethod paymentMethod, Map<String, Object> context)
      throws StripeException {

    // only register mandates during on-session payments for monthly invoices.
    // Which is only when the customer clicks "retry" from the invoice ui.

    if (offSession) {
      return false;
    }

    if (invoice.getType() != Invoice.Type.MONTHLY) {
      return false;
    }

    // Only the MONGODB_LTD account supports INR transactions.
    if (paymentMethod.getBillingAccount() != BillingAccount.MONGODB_LTD) {
      return false;
    }

    com.stripe.model.PaymentMethod stripePaymentMethod =
        stripeInterface.getPaymentMethod(
            paymentMethod.getBillingAccount(), paymentMethod.getStripePaymentMethodId());

    // only register mandates for cards issued from india
    if (stripePaymentMethod.getCard() == null
        || !Country.INDIA.getCode().equalsIgnoreCase(stripePaymentMethod.getCard().getCountry())) {
      LOG.info("not registering new mandate. {}", entries(context));
      return false;
    }

    LOG.info("India on-session payment. registering new mandate. {}", entries(context));
    // always register a new mandate, even if the existing one is active.
    return true;
  }

  /**
   * Intended to prevent double charging. if the payments charge lock has expired, this means the
   * thread that previously attempted to charge this payment was interrupted before receiving a
   * response from stripe. This method checks stripe to see if such a charge attempt exists & is
   * successful.
   *
   * @param payment
   * @param paymentMethod
   * @return
   * @throws StripeException
   */
  private Optional<PaymentIntent> getPaymentIntentForStuckPayment(
      Payment payment, PaymentMethod paymentMethod) throws StripeException {
    // this check can pass because this payment was not refreshed after it was locked during this
    // charge attempt.
    if (!payment.isLocked()) {
      return Optional.empty();
    }
    rateLimiterProvider.get(paymentMethod.getType().getPaymentProcessorType()).acquire();
    return stripeInterface
        .getPaymentIntentsFromPaymentId(payment.getId(), paymentMethod.getBillingAccount())
        .stream()
        .filter(
            p ->
                PaymentIntentStatus.SUCCEEDED.equals(p.getStatus())
                    || PaymentIntentStatus.PROCESSING.equals(p.getStatus()))
        .findFirst();
  }

  @VisibleForTesting
  BigDecimal setConversionRate(
      Payment payment, Currency toCurrency, LocalDateTime fxRateSelectionTime) throws SvcException {
    if (!toCurrency.isSupportedPaymentCurrency()) {
      throw new SvcException(BillingErrorCode.CANNOT_CHARGE_UNSUPPORTED_CURRENCY);
    }
    if (payment.getStatus() == Status.PROCESSING) {
      return BigDecimal.ONE;
    }
    // no need to convert USD to USD
    if (toCurrency == Currency.USD) {
      paymentDao.updateStripeConversionRate(payment.getId(), BigDecimal.ONE);
      return BigDecimal.ONE;
    }
    try {
      BigDecimal conversionRate =
          currencyConversionSvc.getConversionRate(Currency.USD, toCurrency, fxRateSelectionTime);
      paymentDao.updateStripeConversionRate(payment.getId(), conversionRate);
      return conversionRate;
    } catch (Exception e) {
      throw new SvcException(BillingErrorCode.FAILED_TO_GET_CONVERSION_RATE, e);
    }
  }

  private Map<String, String> getMetadataForStripePayment(Invoice invoice, Payment payment) {
    Map<String, String> metadata = StripeMetadataUtils.getMetadataForPayment(payment);

    if (invoice.getType() != null && invoice.getType().equals(Invoice.Type.CONSULTING)) {
      metadata.put("description", ChargePaymentProcessor.SELF_SERVE_PS_NOTE);
    }
    return metadata;
  }

  /**
   * We only want to force auth for cards issued from Indian card-issuers. We store the country of
   * the card issuer in the PaymentMethod.creditCardIssuanceCountry field. However, we only started
   * populating this field in July 2020, so we fallback to the billing address's country when that
   * field is not present.
   */
  private boolean isIndiaPaymentMethod(PaymentMethod paymentMethod) {
    String indiaCountryCode = "IN";
    if (paymentMethod.getCreditCardIssuanceCountry() != null) {
      return indiaCountryCode.equals(paymentMethod.getCreditCardIssuanceCountry());
    }
    // if PaymentMethod.creditCardIssuanceCountry is null, the payment method was created before we
    // started setting that field, so fall back to the billing address.
    return paymentMethod.getBillingAddress() != null
        && indiaCountryCode.equals(paymentMethod.getBillingAddress().getCountry());
  }

  @VisibleForTesting
  PaymentIntent createStripePaymentIntent(
      Organization organization,
      Invoice invoice,
      Payment payment,
      PaymentMethod paymentMethod,
      boolean offSession,
      boolean idempotent,
      Long newMandateMaximum)
      throws StripeException, SvcException {
    HashMap<String, Object> context =
        buildLogContext(payment, paymentMethod, invoice, offSession, "createStripePaymentIntent");
    if (invoice == null || payment == null) {
      String centsString = payment != null ? "totalCents=" + payment.getAmountBilledCents() : "";
      throw new IllegalStateException(
          "Attempt to issue a charge without an invoice or payment: "
              + "orgId="
              + organization.getId()
              + centsString);
    }
    long salesTax = payment.getSalesTaxCents();
    long amountBilled = payment.getAmountBilledCents();

    if (paymentMethod.getCurrency() != Currency.USD) {
      LOG.info(
          "encountered non-usd payment. converting payment amount to payment method currency. {}",
          entries(context));
      BigDecimal conversionRate =
          setConversionRate(payment, paymentMethod.getCurrency(), LocalDateTime.now());
      salesTax = conversionRate.multiply(BigDecimal.valueOf(salesTax)).longValue();
      amountBilled = conversionRate.multiply(BigDecimal.valueOf(amountBilled)).longValue();
      context.put("paymentAmountBilled", payment.getAmountBilledCents());
      context.put("paymentSalesTax", payment.getSalesTaxCents());
      context.put("convertedAmountBilled", amountBilled);
      context.put("convertedSalesTax", salesTax);
      context.put("conversionRate", conversionRate);
      context.put("currency", paymentMethod.getCurrency());
      LOG.info("converted payment amount to local currency. {}", entries(context));
    }

    ObjectId paymentId = payment.getId();
    Map<String, String> metadata = getMetadataForStripePayment(invoice, payment);

    // only MONTHLY invoices support retries. Flex Consulting purchases aren't set up to handle
    // completing the purchase upon successful retry.
    boolean shouldForce3DSAuth =
        !offSession
            && isIndiaPaymentMethod(paymentMethod)
            && invoice.getType() == Invoice.Type.MONTHLY;
    if (shouldForce3DSAuth) {
      LOG.info("forcing 3DS auth for retry attempt. {}", entries(context));
    }

    boolean setupFuturePayments = invoice.getType() == Invoice.Type.MONTHLY && !offSession;
    return stripeInterface.createPaymentIntent(
        paymentMethod.getBillingAccount(),
        paymentMethod.getStripePaymentMethodId(),
        paymentMethod.getStripeCustomerId(),
        amountBilled - salesTax,
        salesTax,
        organization.getName(),
        commonConfig.getFullSiteName(),
        offSession,
        CaptureMethod.AUTOMATIC,
        idempotent ? paymentId.toString() : null,
        metadata,
        shouldForce3DSAuth,
        paymentMethod.getCurrency().name(),
        paymentMethod.hasActiveMandate() ? paymentMethod.getStripeMandate().getId() : null,
        newMandateMaximum,
        setupFuturePayments);
  }

  public void saveFailedStripeCharge(
      ObjectId paymentId,
      ObjectId paymentMethodId,
      BillingAccount billingAccount,
      String stripePaymentIntentId,
      String stripePaymentIntentStatus,
      @Nullable StripeApiError stripeApiError,
      String cardLast4,
      Date cardExpiration,
      Date date) {
    Payment.Status paymentStatus =
        getFailedStripePaymentStatus(stripePaymentIntentStatus, stripeApiError);

    paymentDao.saveFailedStripeCharge(
        paymentId,
        paymentStatus,
        paymentMethodId,
        billingAccount,
        stripePaymentIntentId,
        stripePaymentIntentStatus,
        stripeApiError,
        cardLast4,
        cardExpiration,
        date);
  }

  private void incrementPaymentIntentCounter(
      PaymentMethod pm, Payment oldPayment, Payment.Status newPaymentStatus, boolean offSession) {
    boolean isIndia = isIndiaPaymentMethod(pm);
    boolean moreThan15kInr = false;

    if (isIndia && pm.getCurrency() == Currency.INR) {
      Payment newPayment = paymentDao.findById(oldPayment.getId());
      if (newPayment.getStripe() != null && newPayment.getStripe().getConversionRate() != null) {
        long amountBilledInInr =
            newPayment
                .getStripe()
                .getConversionRate()
                .multiply(BigDecimal.valueOf(newPayment.getAmountBilledCents()))
                .longValue();
        moreThan15kInr = amountBilledInInr > 1500000;
      }
    }

    STRIPE_PAYMENT_INTENT_COUNTER
        .labels(
            String.valueOf(isIndia),
            String.valueOf(offSession),
            newPaymentStatus.name(),
            String.valueOf(oldPayment.getFailedChargeAttempts() == null),
            String.valueOf(pm.hasActiveMandate()),
            String.valueOf(moreThan15kInr))
        .inc();
  }

  void handleCardChargeFailed(
      Organization organization,
      Invoice invoice,
      Payment payment,
      PaymentIntent paymentIntent,
      @Nullable StripeApiError stripeApiError,
      PaymentMethod paymentMethod,
      boolean offSession) {
    String piStatus = paymentIntent == null ? null : paymentIntent.getStatus();
    saveFailedStripeCharge(
        payment.getId(),
        paymentMethod.getId(),
        paymentMethod.getBillingAccount(),
        paymentIntent == null ? null : paymentIntent.getId(),
        piStatus,
        stripeApiError,
        paymentMethod.getCardLast4(),
        paymentMethod.getCardExpiration(),
        new Date());

    incrementPaymentIntentCounter(
        paymentMethod, payment, getFailedStripePaymentStatus(piStatus, stripeApiError), offSession);

    boolean isOnSessionRequiringAction =
        paymentIntent != null
            && paymentIntent.getStatus().equals(PaymentIntentStatus.REQUIRES_ACTION);

    PaymentProcessedEvent event =
        new CardChargeFailedEvent(
            payment,
            stripeApiError == null ? null : stripeApiError.getApiErrorCode(),
            isOnSessionRequiringAction,
            paymentMethod.getCardLast4(),
            paymentMethod.getCurrency());

    if (!isOnSessionRequiringAction) {
      // Only log activity feed and send email if this is not an on-session payment requiring auth.
      // For on-session payment, skip sending email for now as user will be asked to authenticate.
      // If payment still fails after authentication, we will then send the email.

      logChargeFailureForAtlas(
          organization, payment, stripeApiError == null ? null : stripeApiError.toString());

      auditClient.createEvent(
          CreateEventRequestDto.builder()
              .eventPayload(event)
              .orgId(event.getOrgId())
              .visibility(Visibility.VISIBILITY_ALL)
              .source(
                  SourceMessage.newBuilder()
                      .setSourceType(EventSource.SYSTEM.toProtoEventSource())
                      .build())
              .build());
    }

    paymentProcessedEventClient.publishEvent(event);
  }

  private ChargePaymentResult processPaymentIntentForPayment(
      PaymentIntent paymentIntent,
      Payment payment,
      Organization org,
      Invoice invoice,
      PaymentMethod paymentMethod,
      HashMap<String, Object> context,
      boolean offSession)
      throws SvcException {
    context.put(
        "newIntentStatus",
        paymentIntent != null && paymentIntent.getStatus() != null
            ? paymentIntent.getStatus()
            : "null");
    LOG.info("payment intent created. {}", entries(context));
    // grab updated payment to reflect changes written to payment while charging it.
    Payment updatedPayment = paymentDao.findById(payment.getId());
    if (paymentIntent.getStatus().equals(PaymentIntentStatus.SUCCEEDED)) {
      // immediately mark the payment as paid, so we do not double charge the customer if there are
      // any errors processing the successful payment.
      paymentDao.setPaymentStatus(updatedPayment.getId(), Status.PAID, new Date());

      handleCardChargeSucceeded(
          org, invoice, updatedPayment, paymentMethod, paymentIntent, offSession);
      return ChargePaymentResult.success();
    }

    if (paymentIntent.getStatus().equals(PaymentIntentStatus.PROCESSING)) {
      handleCardChargeProcessing(
          updatedPayment, paymentMethod, paymentIntent, LocalDateTime.now(), context);
      return ChargePaymentResult.processing();
    }

    StripeApiError stripeApiError =
        paymentIntent.getLastPaymentError() != null
            ? StripeApiError.from(paymentIntent.getLastPaymentError())
            : null;
    handleCardChargeFailed(
        org, invoice, payment, paymentIntent, stripeApiError, paymentMethod, offSession);

    // Only Monthly invoices support requests to complete 3DS.
    if (paymentIntent.getStatus().equals(PaymentIntentStatus.REQUIRES_ACTION)
        && invoice.getType() == Invoice.Type.MONTHLY) {
      return ChargePaymentResult.actionRequired(
          PaymentAuthenticationRequest.builder()
              .stripePaymentIntentClientSecret(paymentIntent.getClientSecret())
              .build());
    } else if (payment.getStatus() == Status.PROCESSING) {
      // throw an expected exception type for processing failures.
      // we need to do this because transitions to FAILED from PROCESSING do not throw exceptions.
      // While transitions to FAILED from CREATED do.
      // This is because we only fetch the payment intent for PROCESSING payments, which will not
      // throw an error if the intent is in a terminal failure state.
      return ChargePaymentResult.failure(
          FAILED_TO_PROCESS_PROCESSING_PAYMENT, paymentIntent.getStatus());
    }
    return ChargePaymentResult.failure(
        FAILED_TO_PROCESS_CREATED_PAYMENT, paymentIntent.getStatus());
  }

  @Override
  public ChargePaymentResult chargePaymentMethod(
      Organization organization,
      Invoice invoice,
      Payment payment,
      PaymentMethod paymentMethod,
      boolean isIdempotent,
      boolean isOffSession)
      throws SvcException {
    PaymentIntent paymentIntent = null;

    HashMap<String, Object> context =
        buildLogContext(payment, paymentMethod, invoice, isOffSession, "chargeCreditCard");

    LOG.info("charging credit card for payment. {}", entries(context));
    try {
      boolean shouldRegisterNewMandate =
          shouldRegisterMandateWithPaymentIntent(isOffSession, invoice, paymentMethod, context);

      // whenever a new mandate is registered we need to charge the payment in INR.
      // so update the payment method's currency if we are going to register a new mandate.
      // if mandate registration fails, we will still charge payments in INR. This is intended.
      Long newMandateMaximum = null;
      context.put("shouldRegisterNewMandate", shouldRegisterNewMandate);
      if (shouldRegisterNewMandate) {
        paymentMethod =
            paymentMethodSvcProvider
                .get()
                .updateCurrency(paymentMethod.getId(), Currency.INR)
                .block();
        try {
          newMandateMaximum = paymentMethodClient.calculateMandateMaximum(LocalDateTime.now());
          context.put("newMandateMaximum", newMandateMaximum);
          LOG.info("calculated the new mandate's maximum. {}", entries(context));
        } catch (Exception e) {
          LOG.error(
              "failed to charge payment due to failure to calculate new mandate maximum. {}",
              entries(context),
              e);
          return ChargePaymentResult.failure(FAILED_TO_CALCULATE_MANDATE_MAXIMUM);
        }
      }
      Optional<PaymentIntent> stuckPaymentIntent =
          getPaymentIntentForStuckPayment(payment, paymentMethod);
      if (stuckPaymentIntent.isPresent()) {
        paymentIntent = stuckPaymentIntent.get();
      } else {
        paymentIntent =
            createStripePaymentIntent(
                organization,
                invoice,
                payment,
                paymentMethod,
                isOffSession,
                isIdempotent,
                newMandateMaximum);
      }

    } catch (CardException e) {
      handleCardChargeFailed(
          organization,
          invoice,
          payment,
          null,
          StripeApiError.from(e),
          paymentMethod,
          isOffSession);

      String logMessage =
          String.format(
              "Unexpected stripe error while charging payment: payment=%s", payment.getId());
      logStripeException(e, logMessage);
      return ChargePaymentResult.failure(e);
    } catch (StripeException e) {
      String logMessage =
          String.format(
              "Unexpected Stripe error while charging payment: invoiceId=%s, paymentId=%s",
              invoice.getId(), payment.getId());
      incrementPaymentIntentCounter(paymentMethod, payment, payment.getStatus(), isOffSession);
      paymentDao.unsetChargeLock(payment.getId());
      logStripeException(e, logMessage);
      return ChargePaymentResult.failure(e);
    }

    return processPaymentIntentForPayment(
        paymentIntent, payment, organization, invoice, paymentMethod, context, isOffSession);
  }

  @Override
  public ChargePaymentResult pollProcessingPayment(
      Organization org, Payment payment, Invoice invoice) throws SvcException {
    PaymentMethod paymentMethod =
        paymentMethodSvcProvider.get().findById(payment.getPaymentMethodId()).block();
    HashMap<String, Object> context =
        buildLogContext(payment, paymentMethod, invoice, true, "pollProcessingPayment");
    PaymentIntent paymentIntent = null;
    try {
      paymentIntent =
          stripeInterface.getPaymentIntent(
              paymentMethod.getBillingAccount(), payment.getStripe().getPaymentIntentId());
    } catch (StripeException e) {
      String logMessage =
          String.format(
              "Unexpected Stripe error while polling payment: invoiceId=%s, paymentId=%s",
              invoice.getId(), payment.getId());
      logStripeException(e, logMessage);
      return ChargePaymentResult.failure(e);
    }
    return processPaymentIntentForPayment(
        paymentIntent, payment, org, invoice, paymentMethod, context, true);
  }

  @Override
  public void handleFailedLockAcquisition(Payment payment) throws SvcException {
    LOG.info(
        "failed to acquire lock for payment. Proceeding with charge anyway because we will"
            + " check stripe to see if a payment intent already exists. {}",
        kv("paymentId", payment.getId()));
  }

  public long getAmountPaidInUSDFromStripePaymentIntent(
      PaymentIntent paymentIntent, Charge charge, Payment payment) {
    long amountCents = paymentIntent.getAmount();
    if (charge.getCurrency() != null && !charge.getCurrency().equalsIgnoreCase("USD")) {
      amountCents =
          BigDecimal.valueOf(amountCents)
              .divide(payment.getStripe().getConversionRate(), RoundingMode.HALF_EVEN)
              .longValue();
    }
    return amountCents;
  }

  void handleCardChargeSucceeded(
      Organization organization,
      Invoice invoice,
      Payment payment,
      PaymentMethod paymentMethod,
      PaymentIntent paymentIntent,
      boolean offSession) {
    Charge charge = getCharge(paymentIntent);
    long amountCentsUsd = getAmountPaidInUSDFromStripePaymentIntent(paymentIntent, charge, payment);

    paymentDao.saveSuccessfulStripeCharge(
        payment.getId(),
        paymentMethod.getId(),
        paymentMethod.getBillingAccount(),
        paymentIntent.getId(),
        amountCentsUsd,
        charge.getId(),
        paymentMethod.getCardLast4(),
        paymentMethod.getCardExpiration(),
        new Date());
    incrementPaymentIntentCounter(paymentMethod, payment, Status.PAID, offSession);

    PaymentProcessedEvent event =
        new CardChargeSuccessfulEvent(
            payment,
            amountCentsUsd,
            new StripeCharge(
                charge.getCurrency(),
                charge.getAmount(),
                charge.getPaymentMethodDetails().getCard().getLast4()));

    auditClient.createEvent(
        CreateEventRequestDto.builder()
            .eventPayload(event)
            .orgId(event.getOrgId())
            .visibility(Visibility.VISIBILITY_ALL)
            .source(
                SourceMessage.newBuilder()
                    .setSourceType(EventSource.SYSTEM.toProtoEventSource())
                    .build())
            .build());

    paymentProcessedEventClient.publishEvent(event);
  }

  private void handleCardChargeProcessing(
      Payment payment,
      PaymentMethod paymentMethod,
      PaymentIntent paymentIntent,
      LocalDateTime now,
      Map<String, Object> context) {

    // only set the processingStartDate field for payments which were not already PROCESSING
    boolean isNewlyProcessingPayment = payment.getStatus() != Status.PROCESSING;
    if (isNewlyProcessingPayment) {
      LOG.info("payment intent transitioned into processing status. {}", entries(context));

      CardChargeProcessingEvent event =
          new CardChargeProcessingEvent(payment, paymentMethod.getCardLast4());

      auditClient.createEvent(
          CreateEventRequestDto.builder()
              .eventPayload(event)
              .orgId(event.getOrgId())
              .visibility(Visibility.VISIBILITY_ALL)
              .source(
                  SourceMessage.newBuilder()
                      .setSourceType(EventSource.SYSTEM.toProtoEventSource())
                      .build())
              .build());

      paymentDao.saveProcessingStripeCharge(
          payment.getId(),
          paymentMethod.getId(),
          paymentMethod.getCardLast4(),
          paymentMethod.getBillingAccount(),
          paymentIntent.getId(),
          now);
      incrementPaymentIntentCounter(paymentMethod, payment, Status.PROCESSING, true);
      return;
    }
    incrementPaymentIntentCounter(paymentMethod, payment, Status.PROCESSING, true);
    LOG.info("payment intent is still in processing status. {}", entries(context));
  }

  public ChargePaymentResult confirmStripePayment(Payment payment) {
    if (payment.getStatus() != Status.FAILED_AUTHENTICATION) {
      return ChargePaymentResult.failure(
          ChargePaymentErrorCode.PAYMENT_NOT_IN_FAILED_AUTHENTICATION_STATUS);
    }
    PaymentMethod activePaymentMethod =
        paymentMethodSvcProvider
            .get()
            .getActivePaymentMethodSync(payment.getOrgId(), true)
            .orElse(null);
    if (activePaymentMethod == null) {
      return ChargePaymentResult.failure(ChargePaymentErrorCode.MISSING_PAYMENT_METHOD);
    }
    if (!activePaymentMethod.getId().equals(payment.getPaymentMethodId())) {
      return ChargePaymentResult.failure(ChargePaymentErrorCode.PAYMENT_METHOD_CHANGED);
    }
    Invoice invoice = invoiceClient.getInvoice(payment.getInvoiceId());
    Organization organization = organizationClient.getOrganization(payment.getOrgId());
    PaymentIntent paymentIntent = null;
    try {
      paymentIntent =
          stripeInterface.getPaymentIntent(
              activePaymentMethod.getBillingAccount(), payment.getStripe().getPaymentIntentId());

      if (!paymentIntent.getStatus().equals(PaymentIntentStatus.REQUIRES_CONFIRMATION)) {
        return ChargePaymentResult.failure(
            ChargePaymentErrorCode.PAYMENT_INTENT_NOT_IN_REQUIRES_CONFIRM);
      }
      PaymentIntent updatedPaymentIntent =
          stripeInterface.confirmPaymentIntent(
              activePaymentMethod.getBillingAccount(), paymentIntent);
      if (updatedPaymentIntent.getStatus().equals(PaymentIntentStatus.SUCCEEDED)) {
        handleCardChargeSucceeded(
            organization, invoice, payment, activePaymentMethod, updatedPaymentIntent, false);
        return ChargePaymentResult.success();
      } else {
        LOG.error(
            "The payment intent status is {} after confirming, for orgId={}, invoiceId={},"
                + " paymentId={}",
            updatedPaymentIntent.getStatus(),
            organization.getId(),
            invoice.getId(),
            payment.getId());
        handleCardChargeFailed(
            organization, invoice, payment, updatedPaymentIntent, null, activePaymentMethod, false);
        return ChargePaymentResult.failure(ChargePaymentErrorCode.FAILED_PAYMENT_RETRY_FAILED);
      }
    } catch (CardException e) {
      handleCardChargeFailed(
          organization,
          invoice,
          payment,
          paymentIntent,
          StripeApiError.from(e),
          activePaymentMethod,
          false);
      return ChargePaymentResult.failure(e);
    } catch (StripeException e) {
      String logMessage =
          String.format(
              "An error occurred when retrieving/confirming a payment intent for orgId=%s,"
                  + " invoiceId=%s, paymentId=%s",
              organization.getId(), invoice.getId(), payment.getId());
      logStripeException(e, logMessage);
      return ChargePaymentResult.failure(e);
    }
  }

  @Nullable
  public Charge getCharge(PaymentIntent pPaymentIntent) {
    // for now there's always a single charge created for a payment intent
    return pPaymentIntent.getLatestChargeObject();
  }
}
