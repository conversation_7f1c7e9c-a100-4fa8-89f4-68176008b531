package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.processor;

import static com.xgen.cloud.common.util._public.time.DateTimeUtils.dateOf;
import static net.logstash.logback.argument.StructuredArguments.e;
import static net.logstash.logback.argument.StructuredArguments.entries;

import com.nimbusds.oauth2.sdk.token.AccessToken;
import com.nimbusds.oauth2.sdk.token.BearerAccessToken;
import com.xgen.cloud.billingplatform.common._public.model.Result;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.billingshared.common.util._public.svc.SchedulerFactory;
import com.xgen.cloud.partners.vercel.billing._public.config.VercelBillingConfig;
import com.xgen.cloud.partners.vercel.billing._public.svc.VercelInstallationSvc;
import com.xgen.cloud.partners.vercel.sdk.client._public.exception.VercelApiRequestException;
import com.xgen.cloud.partners.vercel.sdk.client._public.svc.VercelMarketplaceApiClient;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.installation.VercelInstallation;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.UpdateInvoiceAction;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.invoice.UpdateInvoiceRequest;
import com.xgen.cloud.payments.salestax.calculation._public.svc.TaxTransactionSvc;
import com.xgen.cloud.payments.standalone.common._public.client.AuditClient;
import com.xgen.cloud.payments.standalone.common._public.client.RefundProcessedEventClient;
import com.xgen.cloud.payments.standalone.partners.common._public.util.PartnerUtils;
import com.xgen.cloud.services.event.proto.SourceMessage;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.svc.PaymentMethodSvc;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.CreditDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.LineItemDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.PaymentDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.RefundDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.CreditApplicationSvc;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentErrorCode;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentErrorDetails;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentResult;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundRequest;
import com.xgen.svc.mms.model.billing.LineItem;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import com.xgen.svc.mms.model.billing.Refund;
import com.xgen.svc.mms.model.billing.VercelPaymentContext;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.math.BigDecimal;
import java.time.Clock;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

@Singleton
public class VercelRefundPaymentProcessor extends RefundPaymentProcessor {
  private static final Scheduler VERCEL_REFUND_PAYMENT_PROCESSOR_SCHEDULER =
      SchedulerFactory.newBoundedElastic("VercelRefundPaymentProcessor");
  private static final Logger LOG = LoggerFactory.getLogger(VercelRefundPaymentProcessor.class);

  private final VercelMarketplaceApiClient vercelMarketplaceApiClient;
  private final PaymentMethodSvc paymentMethodSvc;
  private final VercelInstallationSvc vercelInstallationSvc;
  private final VercelBillingConfig vercelBillingConfig;
  private final CreditDao creditDao;
  private final CreditApplicationSvc creditApplicationSvc;
  private final LineItemDao lineItemDao;

  @Inject
  public VercelRefundPaymentProcessor(
      RefundDao refundDao,
      PaymentDao paymentDao,
      TaxTransactionSvc taxTransactionSvc,
      AuditClient auditClient,
      VercelMarketplaceApiClient vercelMarketplaceApiClient,
      PaymentMethodSvc paymentMethodSvc,
      RefundProcessedEventClient refundProcessedEventClient,
      Clock clock,
      VercelInstallationSvc vercelInstallationSvc,
      VercelBillingConfig vercelBillingConfig,
      CreditDao creditDao,
      CreditApplicationSvc creditApplicationSvc,
      LineItemDao lineItemDao) {
    super(refundDao, paymentDao, taxTransactionSvc, auditClient, refundProcessedEventClient, clock);
    this.vercelMarketplaceApiClient = vercelMarketplaceApiClient;
    this.paymentMethodSvc = paymentMethodSvc;
    this.vercelInstallationSvc = vercelInstallationSvc;
    this.vercelBillingConfig = vercelBillingConfig;
    this.creditDao = creditDao;
    this.creditApplicationSvc = creditApplicationSvc;
    this.lineItemDao = lineItemDao;
  }

  @Override
  public RefundPaymentResult refundPayment(
      Invoice invoice, RefundRequest refundRequest, SourceMessage source) {

    Result<VercelRefundResult, RefundPaymentErrorDetails> result =
        vercelRefund(
            refundRequest.getPayment(),
            refundRequest.getRefundAmountCents(),
            refundRequest.getRefundReason());

    if (result.hasError()) {
      return RefundPaymentResult.failure(result.getError());
    }

    VercelRefundResult vercelRefundResult = result.getValue();

    Refund refund =
        new Refund.Builder()
            .orgId(refundRequest.getPayment().getOrgId())
            .invoiceId(refundRequest.getPayment().getInvoiceId())
            .paymentId(refundRequest.getPayment().getId())
            .created(dateOf(vercelRefundResult.createdDate()))
            .reason(refundRequest.getRefundReason())
            .revRecReason(refundRequest.getRevenueRefundReason())
            .amountCents(refundRequest.getRefundAmountCents())
            .amountTaxCents(refundRequest.getTaxRefundAmountCents())
            .vercel(
                VercelPaymentContext.builder()
                    .invoiceId(vercelRefundResult.invoiceId())
                    .refundRequestedAt(vercelRefundResult.createdDate())
                    .build())
            .build();

    // If not forgiving charges,
    // delete credit line items and reduce credit's totalBilledCents and amountRemainingCents
    if (!refundRequest.hasIntentToForgiveCharges()) {
      getCreditLineItemsForPayment(refundRequest.getPayment())
          .collectList()
          .flatMap(
              creditLineItems -> {
                if (creditLineItems.isEmpty()) {
                  return Mono.empty();
                }
                return creditApplicationSvc
                    .unApplyCredits(
                        creditLineItems,
                        "Vercel refund - payment ID: " + refundRequest.getPayment().getId())
                    .doOnSuccess(
                        ignored -> {
                          Map<String, Object> context = new HashMap<>();
                          context.put("creditLineItemCount", creditLineItems.size());
                          context.put("paymentId", refundRequest.getPayment().getId());
                          LOG.info(
                              "Deleted credit line items for Vercel refund. {}", entries(context));
                        });
              })
          // Reduce the credit's totalBilledCents by the refund amount
          .then(
              creditDao.addTotalBilledCents(
                  refundRequest.getPayment().getCreditId(), -refundRequest.getRefundAmountCents()))
          .subscribeOn(VERCEL_REFUND_PAYMENT_PROCESSOR_SCHEDULER)
          .block();
    }

    handleRefundSucceeded(invoice, refundRequest, refund, source);
    return RefundPaymentResult.success();
  }

  /**
   * Processes a refund through Vercel's API.
   *
   * @param payment The payment to refund
   * @param amountCents The amount to refund in cents
   * @param reason The reason for the refund
   * @return Result containing either the Vercel refund result or error details
   */
  private Result<VercelRefundResult, RefundPaymentErrorDetails> vercelRefund(
      Payment payment, Long amountCents, String reason) {

    Map<String, Object> logContext = new LinkedHashMap<>();
    logContext.put("paymentId", payment.getId());
    logContext.put("amountCents", amountCents);
    logContext.put("reason", reason);

    LOG.info("Processing Vercel refund for payment. {}", e(logContext));

    // Validate that payment has Vercel context
    VercelPaymentContext vercelContext = payment.getVercel();
    if (vercelContext == null || vercelContext.getInvoiceId() == null) {
      return Result.error(
          new RefundPaymentErrorDetails(
              RefundPaymentErrorCode.NO_VERCEL_DATA_IN_ENTITY,
              payment.getInvoiceId(),
              payment.getId(),
              amountCents,
              "Payment has no Vercel invoice ID"));
    }
    logContext.put("vercelInvoiceId", vercelContext.getInvoiceId());

    // Get payment method to retrieve installation ID
    PaymentMethod paymentMethod =
        paymentMethodSvc
            .findById(payment.getPaymentMethodId())
            .subscribeOn(VERCEL_REFUND_PAYMENT_PROCESSOR_SCHEDULER)
            .blockOptional()
            .orElseThrow();
    String installationId = paymentMethod.getVercelInstallationId();
    if (installationId == null) {
      return Result.error(
          new RefundPaymentErrorDetails(
              RefundPaymentErrorCode.NO_VERCEL_DATA_IN_ENTITY,
              payment.getInvoiceId(),
              payment.getId(),
              amountCents,
              "Payment method has no Vercel installation ID"));
    }
    logContext.put("vercelInstallationId", installationId);

    // Check if installation is deleted and if it's been more than 24 hours since deletion
    VercelInstallation installation =
        vercelInstallationSvc
            .findByInstallationId(installationId)
            .orElseThrow(
                () ->
                    new RuntimeException("Failed to fetch Vercel installation " + installationId));

    if (installation.isDeleted()) {
      LocalDateTime deletedAt = installation.deletedAt();
      @SuppressWarnings("DataFlowIssue") // isDeleted() checks for null
      Duration timeSinceDeletion = Duration.between(deletedAt, LocalDateTime.now(clock));

      logContext.put("installationDeletedAt", deletedAt);
      logContext.put("timeSinceDeletionHours", timeSinceDeletion);

      // todo: update to use config for cutoff duration (after Caro's PR is merged)
      Duration cutoffDuration = Duration.ofHours(24);
      if (timeSinceDeletion.compareTo(cutoffDuration) > 0) {
        LOG.warn(
            "Refund denied: Vercel installation deleted more than 24 hours ago."
                + " This log is for visibility only and may be changed to WARN. {}",
            e(logContext));
        return Result.error(
            new RefundPaymentErrorDetails(
                RefundPaymentErrorCode.VERCEL_INSTALLATION_DELETED_AND_FINALIZED,
                payment.getInvoiceId(),
                payment.getId(),
                amountCents,
                "Installation was deleted more than 24 hours ago"));
      }
    }

    // Convert cents to dollars for Vercel API
    BigDecimal refundAmountDollars = PartnerUtils.centsToDollars(amountCents);

    // Create refund request
    UpdateInvoiceRequest refundRequest =
        UpdateInvoiceRequest.builder()
            .action(UpdateInvoiceAction.REFUND)
            .reason(reason)
            .total(refundAmountDollars)
            .build();

    try {
      // Fetch access token for vercel client call
      AccessToken accessToken =
          new BearerAccessToken(
              installation.accessTokenDecrypted(vercelBillingConfig.getAccessTokenKey()));

      // Submit refund to Vercel
      vercelMarketplaceApiClient
          .updateInvoice(installationId, vercelContext.getInvoiceId(), refundRequest, accessToken)
          .subscribeOn(VERCEL_REFUND_PAYMENT_PROCESSOR_SCHEDULER)
          .block();

      LOG.info("Successfully submitted Vercel refund. {}", e(logContext));

      // Return success result with refund details
      return Result.of(
          new VercelRefundResult(vercelContext.getInvoiceId(), LocalDateTime.now(clock)));

    } catch (VercelApiRequestException e) {
      LOG.error("Failed to submit Vercel refund. {}", e(logContext), e);

      return Result.error(
          new RefundPaymentErrorDetails(
              RefundPaymentErrorCode.REFUND_FAILED,
              payment.getInvoiceId(),
              payment.getId(),
              amountCents,
              "Vercel API error: " + e.getMessage()));
    } catch (Exception e) {
      LOG.error("Unexpected error during Vercel refund. {}", e(logContext), e);

      return Result.error(
          new RefundPaymentErrorDetails(
              RefundPaymentErrorCode.REFUND_FAILED,
              payment.getInvoiceId(),
              payment.getId(),
              amountCents,
              "Unexpected error: " + e.getMessage()));
    }
  }

  /**
   * Gets credit line items that were applied between the payment's min and max line item IDs.
   *
   * @param payment The payment to get credit line items for
   * @return Flux of credit line items associated with the payment
   */
  private Flux<LineItem> getCreditLineItemsForPayment(Payment payment) {
    if (payment.getMinLineItemId() == null || payment.getMaxLineItemId() == null) {
      Map<String, Object> logContext = new HashMap<>();
      logContext.put("paymentId", payment.getId());
      LOG.warn(
          "Payment has null min or max line item ID, cannot retrieve credit line items. {}",
          entries(logContext));
      return Flux.empty();
    }

    return lineItemDao.getCreditLineItemsBetweenLineItemIds(
        payment.getInvoiceId(), payment.getMinLineItemId(), payment.getMaxLineItemId());
  }

  private record VercelRefundResult(String invoiceId, LocalDateTime createdDate) {}
}
