package com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.stripe.exception.CardException;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.payments.exception._public.stripe.StripeSvcException;
import com.xgen.cloud.services.payments.proto.RefundPaymentErrorDetails;
import com.xgen.cloud.services.payments.proto.StripeCardErrorDetails;
import com.xgen.cloud.services.payments.proto.StripeErrorDetails;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

public class StripeCardRefundPaymentErrorDetails extends StripeRefundPaymentErrorDetails {
  private final String cardDeclineCode;

  public StripeCardRefundPaymentErrorDetails(
      String stripeErrorCode,
      String cardDeclineCode,
      RefundPaymentErrorCode errorCode,
      Object... params) {
    super(stripeErrorCode, errorCode, params);
    this.cardDeclineCode = cardDeclineCode;
  }

  @JsonProperty
  public String getCardDeclineCode() {
    return cardDeclineCode;
  }

  @JsonIgnore
  @Override
  public RefundPaymentErrorDetails toGrpcResponse() {
    RefundPaymentErrorDetails.Builder builder = super.toGrpcResponse().toBuilder();

    if (stripeErrorCode != null) {
      StripeErrorDetails.Builder stripeBuilder =
          StripeErrorDetails.newBuilder().setStripeErrorCode(stripeErrorCode);

      if (cardDeclineCode != null) {
        stripeBuilder.setStripeCardErrorDetails(
            StripeCardErrorDetails.newBuilder().setCardDeclineCode(cardDeclineCode).build());
      }
      builder.setStripeErrorDetails(stripeBuilder.build());
    }

    return builder.build();
  }

  @Override
  public String toString() {
    return new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
        .appendSuper(super.toString())
        .append("cardDeclineCode", cardDeclineCode)
        .toString();
  }

  /** {@inheritDoc} */
  @JsonIgnore
  @Override
  public SvcException reconstructException() {
    return StripeSvcException.getException(
        new CardException(
            null,
            null,
            this.getStripeErrorCode(),
            null,
            this.getCardDeclineCode(),
            null,
            null,
            null));
  }
}
