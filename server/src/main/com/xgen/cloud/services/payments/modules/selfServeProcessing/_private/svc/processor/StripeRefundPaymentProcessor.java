package com.xgen.cloud.services.payments.modules.selfServeProcessing._private.svc.processor;

import com.stripe.exception.StripeException;
import com.stripe.model.Charge;
import com.stripe.model.PaymentIntent;
import com.xgen.cloud.billingplatform.common._public.model.Result;
import com.xgen.cloud.billingplatform.invoice._public.model.Invoice;
import com.xgen.cloud.payments.currency._public.model.Currency;
import com.xgen.cloud.payments.salestax.calculation._public.svc.TaxTransactionSvc;
import com.xgen.cloud.payments.standalone.common._public.client.AuditClient;
import com.xgen.cloud.payments.standalone.common._public.client.RefundProcessedEventClient;
import com.xgen.cloud.payments.stripe._public.client.StripeInterface;
import com.xgen.cloud.services.event.proto.SourceMessage;
import com.xgen.cloud.services.payments.modules.paymentMethod._public.svc.PaymentMethodSvc;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.PaymentDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.dao.RefundDao;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._private.utils.StripeMetadataUtils;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentErrorCode;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentErrorDetails;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundPaymentResult;
import com.xgen.cloud.services.payments.modules.selfServeProcessing._public.model.RefundRequest;
import com.xgen.svc.mms.model.billing.Payment;
import com.xgen.svc.mms.model.billing.PaymentMethod;
import com.xgen.svc.mms.model.billing.Refund;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.math.BigDecimal;
import java.time.Clock;
import java.util.Date;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class StripeRefundPaymentProcessor extends RefundPaymentProcessor {

  private static final Logger LOG = LoggerFactory.getLogger(StripeRefundPaymentProcessor.class);

  private final StripeInterface stripeInterface;
  private final PaymentMethodSvc paymentMethodSvc;
  private final StripeChargePaymentProcessor stripeChargePaymentProcessor;

  @Inject
  public StripeRefundPaymentProcessor(
      RefundDao refundDao,
      PaymentDao paymentDao,
      TaxTransactionSvc taxTransactionSvc,
      AuditClient auditClient,
      StripeInterface stripeInterface,
      PaymentMethodSvc paymentMethodSvc,
      StripeChargePaymentProcessor stripeChargePaymentProcessor,
      RefundProcessedEventClient refundProcessedEventClient,
      Clock clock) {
    super(refundDao, paymentDao, taxTransactionSvc, auditClient, refundProcessedEventClient, clock);
    this.stripeInterface = stripeInterface;
    this.paymentMethodSvc = paymentMethodSvc;
    this.stripeChargePaymentProcessor = stripeChargePaymentProcessor;
  }

  private static Date convertStripeDate(Long pStripeDate) {
    if (pStripeDate == null) {
      throw new IllegalArgumentException("Cannot convert null Stripe date.");
    }
    return new Date(pStripeDate * 1000);
  }

  @Override
  public RefundPaymentResult refundPayment(
      Invoice pInvoice, RefundRequest refundRequest, SourceMessage source) {

    Result<com.stripe.model.Refund, RefundPaymentErrorDetails> result =
        stripeRefund(
            refundRequest.getPayment(),
            refundRequest.getRefundAmountCents(),
            refundRequest.getRefundReason());

    if (result.hasError()) {
      return RefundPaymentResult.failure(result.getError());
    }

    com.stripe.model.Refund stripeRefund = result.getValue();

    Refund refund =
        new Refund.Builder()
            .orgId(refundRequest.getPayment().getOrgId())
            .invoiceId(refundRequest.getPayment().getInvoiceId())
            .paymentId(refundRequest.getPayment().getId())
            .created(convertStripeDate(stripeRefund.getCreated()))
            .reason(refundRequest.getRefundReason())
            .revRecReason(refundRequest.getRevenueRefundReason())
            .amountCents(refundRequest.getRefundAmountCents())
            .amountTaxCents(refundRequest.getTaxRefundAmountCents())
            .stripeRefundId(stripeRefund.getId())
            .build();

    handleRefundSucceeded(pInvoice, refundRequest, refund, source);
    return RefundPaymentResult.success();
  }

  private Result<com.stripe.model.Refund, RefundPaymentErrorDetails> stripeRefund(
      Payment payment, Long amountCents, String reason) {
    if (payment.getStripe() == null) {
      return Result.error(
          new RefundPaymentErrorDetails(
              RefundPaymentErrorCode.NO_STRIPE_DATA,
              payment.getInvoiceId(),
              payment.getId(),
              amountCents));
    }

    Map<String, Object> metadata =
        StripeMetadataUtils.getMetadataForRefund(payment, amountCents, reason);
    Result<Long, RefundPaymentErrorDetails> amountToRefundInLocalCurrency =
        getAmountCentsInLocalCurrencyForRefund(payment, amountCents);

    if (amountToRefundInLocalCurrency.hasError()) {
      return Result.error(amountToRefundInLocalCurrency.getError());
    }

    try {
      return Result.of(
          stripeInterface.createRefund(
              payment.getBillingAccount(),
              payment.getStripe().getChargeId(),
              amountToRefundInLocalCurrency.getValue(),
              metadata));
    } catch (StripeException e) {
      return Result.error(RefundPaymentErrorDetails.of(e));
    }
  }

  private Result<Long, RefundPaymentErrorDetails> getAmountCentsInLocalCurrencyForRefund(
      Payment payment, long amountCents) {
    if (!payment.wasChargedInNonUsdCurrency()) {
      return Result.of(amountCents);
    }
    PaymentMethod paymentMethod = paymentMethodSvc.findById(payment.getPaymentMethodId()).block();
    PaymentIntent paymentIntent;
    try {
      paymentIntent =
          stripeInterface.getPaymentIntent(
              paymentMethod.getBillingAccount(), payment.getStripe().getPaymentIntentId());
    } catch (StripeException e) {
      return Result.error(RefundPaymentErrorDetails.of(e));
    }
    Charge charge = stripeChargePaymentProcessor.getCharge(paymentIntent);
    if (!Currency.USD.name().equalsIgnoreCase(charge.getCurrency())) {
      LOG.info(
          "encountered refund for payment in non-usd currency. paymentId={} currency={}",
          paymentIntent.getId(),
          charge.getCurrency());
      if (!paymentMethod.getCurrency().name().equalsIgnoreCase(charge.getCurrency())) {
        LOG.error(
            "encountered refund for payment with conflicting charge and payment method currencies."
                + " This should never happen. paymentId={} chargeCurrency={}",
            payment.getId(),
            charge.getCurrency());
        return Result.error(new RefundPaymentErrorDetails(RefundPaymentErrorCode.REFUND_FAILED));
      }
      if (payment.getStripe().getConversionRate() == null) {
        LOG.error(
            "encountered refund for payment with non-usd currency but no conversion rate set. This"
                + " should never happen. paymentId={}",
            payment.getId());
        return Result.error(new RefundPaymentErrorDetails(RefundPaymentErrorCode.REFUND_FAILED));
      }
      long amountInLocalCurrency =
          payment
              .getStripe()
              .getConversionRate()
              .multiply(BigDecimal.valueOf(amountCents))
              .longValue();
      LOG.info(
          "converted refund request amount for non-usd payment. paymentId={} conversionRate={}"
              + " currency={} usdAmountToRefund={} localAmountToRefund={} usdAmountBilled={}",
          payment.getId(),
          payment.getStripe().getConversionRate(),
          paymentMethod.getCurrency(),
          amountCents,
          amountInLocalCurrency,
          payment.getAmountBilledCents());
      return Result.of(amountInLocalCurrency);
    }
    return Result.of(amountCents);
  }
}
