package com.xgen.cloud.services.metrics._private.svc.victoriametrics;

import static com.xgen.cloud.services.metrics._private.model.CustomerMetricsServiceConstants.SERVICE_METRIC_PREFIX;
import static com.xgen.cloud.services.metrics._private.util.OtelUtils.parseOtelKeyValues;
import static com.xgen.cloud.services.metrics._private.util.PrometheusNormalizationUtils.normalizeLabel;
import static com.xgen.cloud.services.metrics._private.util.PrometheusNormalizationUtils.normalizeNameWithUnderscoreEscapingWithSuffixes;
import static com.xgen.cloud.services.metrics._public.model.usecase.retention.RetentionPolicy.DEFAULT_RETENTION_POLICY;

import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc;
import com.xgen.cloud.services.metrics._private.model.OtelMetricsMsg;
import com.xgen.cloud.services.metrics._private.model.OtelMetricsMsgEvent;
import com.xgen.cloud.services.metrics._private.util.PrometheusNormalizationUtils;
import com.xgen.cloud.services.metrics._public.model.usecase.PersistenceSettings.TranslationStrategy;
import com.xgen.cloud.services.metrics._public.model.usecase.Usecase;
import com.xgen.cloud.services.metrics._public.model.usecase.retention.RetentionPolicy;
import com.xgen.cloud.services.metrics._public.svc.processors.filter.MetricNameFilter;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.common.AttributesBuilder;
import io.opentelemetry.api.trace.SpanContext;
import io.opentelemetry.api.trace.TraceFlags;
import io.opentelemetry.api.trace.TraceState;
import io.opentelemetry.proto.common.v1.KeyValue;
import io.opentelemetry.proto.metrics.v1.AggregationTemporality;
import io.opentelemetry.proto.metrics.v1.Exemplar;
import io.opentelemetry.proto.metrics.v1.Histogram;
import io.opentelemetry.proto.metrics.v1.HistogramDataPoint;
import io.opentelemetry.proto.metrics.v1.Metric;
import io.opentelemetry.proto.metrics.v1.NumberDataPoint;
import io.opentelemetry.proto.metrics.v1.ScopeMetrics;
import io.opentelemetry.proto.metrics.v1.Sum;
import io.opentelemetry.sdk.common.InstrumentationScopeInfo;
import io.opentelemetry.sdk.metrics.data.DoubleExemplarData;
import io.opentelemetry.sdk.metrics.data.DoublePointData;
import io.opentelemetry.sdk.metrics.data.GaugeData;
import io.opentelemetry.sdk.metrics.data.HistogramData;
import io.opentelemetry.sdk.metrics.data.HistogramPointData;
import io.opentelemetry.sdk.metrics.data.LongPointData;
import io.opentelemetry.sdk.metrics.data.MetricData;
import io.opentelemetry.sdk.metrics.data.PointData;
import io.opentelemetry.sdk.metrics.data.SumData;
import io.opentelemetry.sdk.metrics.internal.data.ImmutableDoubleExemplarData;
import io.opentelemetry.sdk.metrics.internal.data.ImmutableDoublePointData;
import io.opentelemetry.sdk.metrics.internal.data.ImmutableGaugeData;
import io.opentelemetry.sdk.metrics.internal.data.ImmutableHistogramData;
import io.opentelemetry.sdk.metrics.internal.data.ImmutableHistogramPointData;
import io.opentelemetry.sdk.metrics.internal.data.ImmutableLongPointData;
import io.opentelemetry.sdk.metrics.internal.data.ImmutableMetricData;
import io.opentelemetry.sdk.metrics.internal.data.ImmutableSumData;
import io.opentelemetry.sdk.resources.Resource;
import io.prometheus.client.Counter;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for converting OtelMetricsMsgEvent to MetricData. Used in conjunction with
 * VictoriaMetricsOtelWriterSvc for simplified metrics processing.
 */
public class OtelMetricsConverterUtil {

  private static final Logger LOG = LoggerFactory.getLogger(OtelMetricsConverterUtil.class);

  private static final Counter FILTERED_SAMPLES_TOTAL =
      PromMetricsSvc.registerCounter(
          SERVICE_METRIC_PREFIX + "_filtered_otel_exporter_total",
          "Number of otel metrics filtered due to label authz checks",
          "usecase",
          "usecaseId",
          "isBackfill");

  private static final Counter NAME_FILTERED_SAMPLES_TOTAL =
      PromMetricsSvc.registerCounter(
          SERVICE_METRIC_PREFIX + "_name_filtered_otel_exporter_total",
          "Number of otel metrics filtered due to metric name filters",
          "usecase",
          "usecaseId");

  private static final Counter VALID_SAMPLES_TOTAL =
      PromMetricsSvc.registerCounter(
          SERVICE_METRIC_PREFIX + "_valid_otel_exporter_total",
          "Number of otel metrics after rate limits and authz checks",
          "usecase",
          "usecaseId",
          "isBackfill");

  private static final io.prometheus.client.Histogram OTEL_CONVERTER_HANDLE_METHOD_TIME_SECONDS =
      PromMetricsSvc.registerHistogram(
          SERVICE_METRIC_PREFIX + "_otel_victoria_metrics_converter_handle_duration_seconds",
          "Latency of handleOtelMetricsMsgEvent method in OtelMetricsConverterUtil",
          PromMetricsSvc.getHistogramExpBucket(0.001, 2, 10),
          "isBackfill");

  private static final String HISTOGRAM_BUCKET_POSTFIX = "_bucket";
  private static final String HISTOGRAM_SUM_POSTFIX = "_sum";
  private static final String HISTOGRAM_COUNT_POSTFIX = "_count";

  /** Converts an OtelMetricsMsgEvent to a list of MetricData objects. */
  public static List<MetricData> handleOtelMetricsMsgEvent(
      final OtelMetricsMsgEvent msg, final String isBackfillLabel, final MetricNameFilter filter) {
    final io.prometheus.client.Histogram.Timer handleMethodTimer =
        OTEL_CONVERTER_HANDLE_METHOD_TIME_SECONDS.labels(isBackfillLabel).startTimer();

    try {
      // converts the payload to a list of time series
      List<MetricData> metricData = parseToMetricDataList(msg);

      final long samplesCountBeforeFiltering = metricData.size();

      // filter time series by metric name based on usecase filter configuration
      metricData = filterMetricDataByName(metricData, msg.getUsecase(), filter);
      final long samplesCountAfterNameFilter = metricData.size();

      // Track name-filtered samples
      if (samplesCountBeforeFiltering > samplesCountAfterNameFilter) {
        PromMetricsSvc.incrementCounter(
            NAME_FILTERED_SAMPLES_TOTAL,
            samplesCountBeforeFiltering - samplesCountAfterNameFilter,
            msg.getUsecase().getName(),
            msg.getUsecase().getUuid().toString());
      }

      // filter time series that don't have the required label names and values sourced from the jwt
      metricData = filterMetricDataByRequiredLabels(metricData, msg.getRequiredLabels());

      // Track label-filtered samples
      if (samplesCountAfterNameFilter > metricData.size()) {
        PromMetricsSvc.incrementCounter(
            FILTERED_SAMPLES_TOTAL,
            samplesCountAfterNameFilter - metricData.size(),
            msg.getUsecase().getName(),
            msg.getUsecase().getUuid().toString(),
            isBackfillLabel);
      }

      PromMetricsSvc.incrementCounter(
          VALID_SAMPLES_TOTAL,
          metricData.size(),
          msg.getUsecase().getName(),
          msg.getUsecase().getUuid().toString(),
          isBackfillLabel);

      return metricData;
    } finally {
      handleMethodTimer.observeDuration();
    }
  }

  private static AttributesBuilder getAttributesBuilder(
      final Usecase usecase,
      final String metricName,
      final List<KeyValue> keyValues,
      final List<KeyValue> resourceKeyValues,
      final List<KeyValue> scopeKeyValues) {
    AttributesBuilder builder = Attributes.builder();

    // Parse all key-value pairs and add them directly to avoid intermediate map operations
    final Map<String, String> keyValuesMap = parseOtelKeyValues(keyValues);
    for (Map.Entry<String, String> kv : keyValuesMap.entrySet()) {
      builder.put(kv.getKey(), kv.getValue());
    }

    final Map<String, String> resourceKeyValuesMap = parseOtelKeyValues(resourceKeyValues);
    for (Map.Entry<String, String> kv : resourceKeyValuesMap.entrySet()) {
      builder.put(kv.getKey(), kv.getValue());
    }

    final Map<String, String> scopeKeyValuesMap = parseOtelKeyValues(scopeKeyValues);
    for (Map.Entry<String, String> kv : scopeKeyValuesMap.entrySet()) {
      builder.put(kv.getKey(), kv.getValue());
    }

    builder.put("__name__", metricName);
    builder.put("__usecase__", usecase.getUuid().toString());

    // add retention policy as a label that will be honored by victoria metrics to enforce rollup
    // and retention
    final RetentionPolicy policy =
        usecase.getRetentionPolicies().stream().findFirst().orElse(DEFAULT_RETENTION_POLICY);
    builder.put("__retention_policy__", policy.getAsString());
    return builder;
  }

  private static List<DoubleExemplarData> exemplarListToDoubleExemplarDataList(
      final Usecase usecase,
      final String metricName,
      final List<Exemplar> list,
      final List<KeyValue> resourceKeyValues,
      final List<KeyValue> scopeKeyValues) {
    List<DoubleExemplarData> result = new ArrayList<>(list.size());
    for (final Exemplar exemplar : list) {
      io.opentelemetry.api.common.AttributesBuilder builder =
          getAttributesBuilder(
              usecase,
              metricName,
              exemplar.getFilteredAttributesList(),
              resourceKeyValues,
              scopeKeyValues);
      result.add(
          ImmutableDoubleExemplarData.create(
              builder.build(),
              exemplar.getTimeUnixNano(),
              createSpanContext(exemplar),
              exemplar.getAsDouble()));
    }
    return result;
  }

  private static SpanContext createSpanContext(final Exemplar value) {
    return SpanContext.create(
        value.getTraceId().toString(Charset.defaultCharset()),
        value.getSpanId().toString(Charset.defaultCharset()),
        TraceFlags.getSampled(),
        TraceState.getDefault());
  }

  private static Optional<MetricData> getMetricDataSum(
      final Usecase usecase,
      final String metricName,
      final ScopeMetrics scope,
      final Metric metric,
      final List<NumberDataPoint> points,
      final List<KeyValue> resourceKeyValues,
      final List<KeyValue> scopeKeyValues) {

    final List<DoublePointData> doublePointData =
        convertToDoublePointData(usecase, metricName, points, resourceKeyValues, scopeKeyValues);
    final SumData<DoublePointData> sumDataPoints =
        ImmutableSumData.create(
            true,
            io.opentelemetry.sdk.metrics.data.AggregationTemporality.CUMULATIVE,
            doublePointData);

    return Optional.of(
        ImmutableMetricData.createDoubleSum(
            Resource.empty(),
            InstrumentationScopeInfo.create(scope.getScope().getName()),
            metricName,
            metric.getDescription(),
            metric.getUnit(),
            sumDataPoints));
  }

  private static Optional<MetricData> getMetricDataGauge(
      final Usecase usecase,
      final String metricName,
      final ScopeMetrics scope,
      final Metric metric,
      final List<NumberDataPoint> points,
      final List<KeyValue> resourceKeyValues,
      final List<KeyValue> scopeKeyValues) {

    final List<DoublePointData> doublePointData =
        convertToDoublePointData(usecase, metricName, points, resourceKeyValues, scopeKeyValues);
    final GaugeData<DoublePointData> gaugeDataPoints = ImmutableGaugeData.create(doublePointData);

    return Optional.of(
        ImmutableMetricData.createDoubleGauge(
            Resource.empty(),
            InstrumentationScopeInfo.create(scope.getScope().getName()),
            metricName,
            metric.getDescription(),
            metric.getUnit(),
            gaugeDataPoints));
  }

  private static List<DoublePointData> convertToDoublePointData(
      final Usecase usecase,
      final String metricName,
      final List<NumberDataPoint> points,
      final List<KeyValue> resourceKeyValues,
      final List<KeyValue> scopeKeyValues) {
    List<DoublePointData> doublePointData = new ArrayList<>();
    for (NumberDataPoint point : points) {
      io.opentelemetry.api.common.AttributesBuilder builder =
          getAttributesBuilder(
              usecase, metricName, point.getAttributesList(), resourceKeyValues, scopeKeyValues);
      double value = 0D;
      if (point.hasAsDouble()) {
        value = point.getAsDouble();
      } else if (point.hasAsInt()) {
        value = (double) point.getAsInt();
      }
      DoublePointData dp =
          ImmutableDoublePointData.create(
              point.getStartTimeUnixNano(), point.getTimeUnixNano(), builder.build(), value);
      doublePointData.add(dp);
    }
    return doublePointData;
  }

  private static List<MetricData> filterMetricDataByRequiredLabels(
      final List<MetricData> timeseriesVals, final Map<String, String> requiredLabels) {
    if (requiredLabels.isEmpty()) {
      return timeseriesVals;
    }

    final List<MetricData> filteredTimeseriesVal = new ArrayList<>();
    for (final MetricData metric : timeseriesVals) {
      final Map<String, String> labels = getLabels(metric);
      final boolean allMatch =
          requiredLabels.entrySet().stream()
              .allMatch(
                  e -> {
                    String normalizedKey = normalizeLabel(e.getKey());
                    return labels.containsKey(normalizedKey)
                        && labels.get(normalizedKey).equals(e.getValue());
                  });
      if (allMatch) {
        filteredTimeseriesVal.add(metric);
      }
    }
    return filteredTimeseriesVal;
  }

  /**
   * Filters metrics by name using the usecase's filter configuration.
   *
   * @param metricDataList the list of metric data to filter
   * @param usecase the usecase containing filter configuration
   * @return filtered list of metric data
   */
  static List<MetricData> filterMetricDataByName(
      final List<MetricData> metricDataList, final Usecase usecase, final MetricNameFilter filter) {
    if (metricDataList.isEmpty() || filter == null) {
      return metricDataList;
    }
    if (!filter.hasFiltersForUsecase(usecase.getUuid())) {
      return metricDataList;
    }

    final List<MetricData> filteredMetrics = new ArrayList<>();
    for (final MetricData metricData : metricDataList) {
      if (filter.shouldIncludeMetric(metricData.getName(), usecase.getUuid())) {
        filteredMetrics.add(metricData);
      }
    }
    return filteredMetrics;
  }

  /***
   * Extracts the labels from the metric data
   * @param metricData
   * @return
   */
  private static Map<String, String> getLabels(final MetricData metricData) {
    final Map<String, String> labels = new HashMap<>();
    for (PointData point : metricData.getData().getPoints()) {
      for (Map.Entry<AttributeKey<?>, Object> kv : point.getAttributes().asMap().entrySet()) {
        labels.put(kv.getKey().toString(), kv.getValue().toString());
      }
    }
    return labels;
  }

  static String normalizeName(final Metric pMetric, final TranslationStrategy translationStrategy) {
    try {
      return switch (translationStrategy) {
        case UNDERSCORE_ESCAPING_WITH_SUFFIXES ->
            normalizeNameWithUnderscoreEscapingWithSuffixes(pMetric);
        case NO_UTF8_ESCAPING_WITH_SUFFIXES ->
            PrometheusNormalizationUtils.normalizeNameWithNoUTF8EscapingWithSuffixes(pMetric);
        default -> pMetric.getName().strip(); // Fallback to no normalization
      };
    } catch (final IllegalArgumentException ex) {
      // Log the error and fallback to the original metric name
      LOG.debug(
          "Failed to normalize metric name '{}' with strategy '{}': {}. Using original name.",
          pMetric.getName(),
          translationStrategy,
          ex.getMessage());
      return pMetric.getName().strip();
    }
  }

  private static List<MetricData> parseToMetricDataList(final OtelMetricsMsgEvent msgEvent) {
    List<MetricData> result = new ArrayList<>();
    final Usecase usecase = msgEvent.getUsecase();
    TranslationStrategy translationStrategy = TranslationStrategy.UNDERSCORE_ESCAPING_WITH_SUFFIXES;
    if (usecase != null
        && usecase.getPersistenceSettings() != null
        && usecase.getPersistenceSettings().getTranslationStrategy() != null) {
      translationStrategy = usecase.getPersistenceSettings().getTranslationStrategy();
    }
    OtelMetricsMsg msg = msgEvent.getOTelMetricData();

    List<KeyValue> resourceAttributesList = msg.getResource().getAttributesList();

    for (final ScopeMetrics scope : msg.getScopes()) {
      List<KeyValue> scopeAttributesList = scope.getScope().getAttributesList();
      for (final Metric metric : scope.getMetricsList()) {
        String metricName = normalizeName(metric, translationStrategy);
        switch (metric.getDataCase()) {
          case SUM:
            final Sum sum = metric.getSum();
            if (sum.getAggregationTemporality()
                .equals(AggregationTemporality.AGGREGATION_TEMPORALITY_DELTA)) {
              LOG.debug("Unexpected data case: delta monotonic sums are not supported. Skipping");
              break;
            }
            if (!sum.getDataPointsList().isEmpty()) {
              getMetricDataSum(
                      msgEvent.getUsecase(),
                      metricName,
                      scope,
                      metric,
                      sum.getDataPointsList(),
                      resourceAttributesList,
                      scopeAttributesList)
                  .ifPresent(result::add);
            }
            break;
          case GAUGE:
            if (!metric.getGauge().getDataPointsList().isEmpty()) {
              getMetricDataGauge(
                      msgEvent.getUsecase(),
                      metricName,
                      scope,
                      metric,
                      metric.getGauge().getDataPointsList(),
                      resourceAttributesList,
                      scopeAttributesList)
                  .ifPresent(result::add);
            }
            break;
          case HISTOGRAM:
            handleHistogramMetric(
                msgEvent,
                scope,
                metric,
                metricName,
                resourceAttributesList,
                scopeAttributesList,
                result);
            break;
        }
      }
    }

    return result;
  }

  /**
   * Convert a list of histogram metric data points into the three series (bucket, counter, sum) to
   * adhere to the Victoria Metrics definition of a histogram. Splitting each histogram data point
   * into the three time series is done manually, since the OpenTelemetry exporter does not do this
   * by default.
   *
   * @param msgEvent wrapper for the OpenTelemetry metrics payload.
   * @param scope the OpenTelemetry metrics scope.
   * @param metric the metric payload containing the histogram data point(s).
   * @param metricName the name of the metric.
   * @param resourceAttributesList the list of resource attributes.
   * @param scopeAttributesList the list of scope attributes.
   * @param result the result metric data list to append to.
   */
  private static void handleHistogramMetric(
      OtelMetricsMsgEvent msgEvent,
      ScopeMetrics scope,
      Metric metric,
      String metricName,
      List<KeyValue> resourceAttributesList,
      List<KeyValue> scopeAttributesList,
      List<MetricData> result) {
    final String histogramBucketName = metricName + HISTOGRAM_BUCKET_POSTFIX;
    final Histogram histogram = metric.getHistogram();
    if (histogram
        .getAggregationTemporality()
        .equals(AggregationTemporality.AGGREGATION_TEMPORALITY_DELTA)) {
      LOG.debug("Unexpected data case: delta histograms are not supported. Skipping");
      return;
    }
    if (!histogram.getDataPointsList().isEmpty()) {
      final String histogramCountName = metricName + HISTOGRAM_COUNT_POSTFIX;
      final String histogramSumName = metricName + HISTOGRAM_SUM_POSTFIX;
      io.opentelemetry.sdk.metrics.data.AggregationTemporality aggregationTemporality =
          io.opentelemetry.sdk.metrics.data.AggregationTemporality.CUMULATIVE;

      List<HistogramDataPoint> list = histogram.getDataPointsList();
      List<HistogramPointData> collection = new ArrayList<>(list.size());

      for (HistogramDataPoint histogramDataPoint : list) {
        AttributesBuilder attributesBuilderBuckets =
            getAttributesBuilder(
                msgEvent.getUsecase(),
                histogramBucketName,
                histogramDataPoint.getAttributesList(),
                resourceAttributesList,
                scopeAttributesList);
        collection.add(
            ImmutableHistogramPointData.create(
                histogramDataPoint.getStartTimeUnixNano(),
                histogramDataPoint.getTimeUnixNano(),
                attributesBuilderBuckets.build(),
                histogramDataPoint.getSum(),
                histogramDataPoint.getMin() > 0,
                histogramDataPoint.getMin(),
                histogramDataPoint.getMax() > 0,
                histogramDataPoint.getMax(),
                histogramDataPoint.getExplicitBoundsList(),
                histogramDataPoint.getBucketCountsList(),
                exemplarListToDoubleExemplarDataList(
                    msgEvent.getUsecase(),
                    histogramBucketName,
                    histogramDataPoint.getExemplarsList(),
                    resourceAttributesList,
                    scopeAttributesList)));

        AttributesBuilder attributesBuilderCount =
            getAttributesBuilder(
                msgEvent.getUsecase(),
                histogramCountName,
                histogramDataPoint.getAttributesList(),
                resourceAttributesList,
                scopeAttributesList);
        LongPointData pointCount =
            ImmutableLongPointData.create(
                histogramDataPoint.getStartTimeUnixNano(),
                histogramDataPoint.getTimeUnixNano(),
                attributesBuilderCount.build(),
                histogramDataPoint.getCount());
        SumData<LongPointData> dataCount =
            ImmutableSumData.create(
                true,
                io.opentelemetry.sdk.metrics.data.AggregationTemporality.CUMULATIVE,
                List.of(pointCount));
        result.add(
            ImmutableMetricData.createLongSum(
                Resource.empty(),
                InstrumentationScopeInfo.create(scope.getScope().getName()),
                histogramCountName,
                metric.getDescription(),
                metric.getUnit(),
                dataCount));

        AttributesBuilder attributesBuilderSum =
            getAttributesBuilder(
                msgEvent.getUsecase(),
                histogramSumName,
                histogramDataPoint.getAttributesList(),
                resourceAttributesList,
                scopeAttributesList);
        DoublePointData pointSum =
            ImmutableDoublePointData.create(
                histogramDataPoint.getStartTimeUnixNano(),
                histogramDataPoint.getTimeUnixNano(),
                attributesBuilderSum.build(),
                histogramDataPoint.getSum());
        SumData<DoublePointData> dataSum =
            ImmutableSumData.create(
                true,
                io.opentelemetry.sdk.metrics.data.AggregationTemporality.CUMULATIVE,
                List.of(pointSum));
        result.add(
            ImmutableMetricData.createDoubleSum(
                Resource.empty(),
                InstrumentationScopeInfo.create(scope.getScope().getName()),
                histogramSumName,
                metric.getDescription(),
                metric.getUnit(),
                dataSum));
      }

      HistogramData data = ImmutableHistogramData.create(aggregationTemporality, collection);
      result.add(
          ImmutableMetricData.createDoubleHistogram(
              Resource.empty(),
              InstrumentationScopeInfo.create(scope.getScope().getName()),
              histogramBucketName,
              metric.getDescription(),
              metric.getUnit(),
              data));
    }
  }
}
