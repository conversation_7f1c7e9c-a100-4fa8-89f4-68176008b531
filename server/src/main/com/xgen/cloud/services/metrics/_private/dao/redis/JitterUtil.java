package com.xgen.cloud.services.metrics._private.dao.redis;

import java.time.Duration;

public class JitterUtil {
  /**
   * Adds jitter to a duration by adding a random value between -jitterFactor * duration and
   * jitterFactor * duration.
   *
   * @param duration the duration to add jitter to
   * @param jitterFactor the factor to multiply the duration by to get the range of jitter
   * @return the duration with jitter added
   */
  public static Duration addJitter(final Duration duration, final double jitterFactor) {
    long millis = duration.toMillis();
    long jitter = (long) (millis * jitterFactor * (Math.random() - 0.5));
    return Duration.ofMillis(millis + jitter);
  }
}
