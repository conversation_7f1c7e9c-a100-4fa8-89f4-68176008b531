usecase:
  name: dedicated-mongo-metrics
  uuid: a152094e-259b-5d73-bd16-e49e3fd6899c
  mongoTeam: Cloud Insights & Telemetry I # the team who owns this use case
  contactEmailAddress: <EMAIL>
  publishGranularityMillis: 10000 # specifies the highest desired publish granularity, optional
  isAccessibleToInternalGrafana: true
  retention: [ 'raw:7d' ] # specifies how long to retain metrics of each granularity, optional
  limits:
    ingest:
      cardinality:
        activeSeries: # soft and hard limits for number of active time series
          soft:
            thresholdValue: 60_000_000
          hard:
            thresholdValue: 100_000_000
        totalSeries: # soft and hard limits for number of time series store since usecase creation
          soft:
            thresholdValue: 300_000_000
          hard:
            thresholdValue: 600_000_000
      samples: # soft and hard limits for number of metrics samples sent by metrics publisher in a time period
        windowResolution: minute
        windowLength: 1
        soft:
          thresholdValue: 40_000_000 # 2900 metrics per process per 10 seconds
        hard:
          thresholdValue: 80_000_000
      requestsPerPublisherHost: # soft and hard limits for number of requests sent by a metrics publisher host in a time period
        windowResolution: minute
        windowLength: 1
        soft:
          thresholdValue: 10
        hard:
          thresholdValue: 20
  persistence:
    translationStrategy: NoTranslation
    processors:
      filter:
        metrics:
          include:
            - name: "prefix-metrics"
              match_type: "prefix"
              metric_names:
                - "mongodb.serverStatus.asserts."
                - "mongodb.serverStatus.metrics.document."
                - "mongodb.serverStatus.opcounters."
                - "mongodb.derived."
                - "mongodb.serverStatus.opcountersRepl."
                - "mongodb.serverStatus.catalogStats."
            - name: "strict-match-metrics-for-atlas-ui"
              match_type: "strict"
              metric_names:
                - "mongodb.serverStatus.connections.totalCreated"
                - "mongodb.serverStatus.metrics.query.sort.spillToDisk"
                - "mongodb.serverStatus.metrics.ttl.deletedDocuments"
                - "mongodb.serverStatus.mem.resident"
                - "mongodb.serverStatus.mem.virtual"
                - "mongodb.serverStatus.wiredTiger.cache.bytes_read_into_cache"
                - "mongodb.serverStatus.wiredTiger.cache.bytes_written_from_cache"
                - "mongodb.serverStatus.wiredTiger.cache.bytes_currently_in_the_cache"
                - "mongodb.serverStatus.wiredTiger.cache.tracked_dirty_bytes_in_the_cache"
                - "mongodb.serverStatus.wiredTiger.cache.maximum_bytes_configured"
                - "mongodb.serverStatus.wiredTiger.cache.pages_read_into_cache"
                - "mongodb.serverStatus.wiredTiger.cache.pages_requested_from_the_cache"
                - "mongodb.serverStatus.connections.current"
                - "mongodb.serverStatus.metrics.cursor.open.total"
                - "mongodb.serverStatus.metrics.cursor.timedout"
                - "mongodb.serverStatus.network.numRequests"
                - "mongodb.serverStatus.network.bytesIn"
                - "mongodb.serverStatus.network.bytesOut"
                - "mongodb.serverStatus.opLatencies.reads.latency"
                - "mongodb.serverStatus.opLatencies.reads.ops"
                - "mongodb.serverStatus.opLatencies.writes.latency"
                - "mongodb.serverStatus.opLatencies.writes.ops"
                - "mongodb.serverStatus.opLatencies.commands.latency"
                - "mongodb.serverStatus.opLatencies.commands.ops"
                - "mongodb.serverStatus.opLatencies.transactions.latency"
                - "mongodb.serverStatus.opLatencies.transactions.ops"
                - "mongodb.serverStatus.extra_info.page_faults"
                - "mongodb.serverStatus.metrics.queryExecutor.scanned"
                - "mongodb.serverStatus.metrics.queryExecutor.scannedObjects"
                - "mongodb.serverStatus.globalLock.currentQueue.total"
                - "mongodb.serverStatus.globalLock.currentQueue.readers"
                - "mongodb.serverStatus.globalLock.currentQueue.writers"
                - "mongodb.serverStatus.metrics.operation.scanAndOrder"
                - "mongodb.serverStatus.metrics.operation.killedDueToMaxTimeMsExpired"
                - "mongodb.serverStatus.globalLock.totalTime"
                - "mongodb.serverStatus.globalLock.lockTime"
                - "mongodb.serverStatus.queues.execution.read.available"
                - "mongodb.serverStatus.queues.execution.write.available"
                - "mongodb.serverStatus.queues.execution.read.normalPriority.queueLength"
                - "mongodb.serverStatus.queues.execution.write.normalPriority.queueLength"
                - "mongodb.serverStatus.flowControl.timeAcquiringMicros"
                - "mongodb.serverStatus.wiredTiger.concurrentTransactions.read.available"
                - "mongodb.serverStatus.wiredTiger.concurrentTransactions.write.available"
                - "mongodb.serverStatus.tcmalloc.generic.heap_size"
                - "mongodb.serverStatus.tcmalloc.generic.current_allocated_bytes"
                - "mongodb.serverStatus.opWorkingTime.commands.latency"
                - "mongodb.serverStatus.opWorkingTime.commands.ops"
                - "mongodb.serverStatus.opWorkingTime.reads.latency"
                - "mongodb.serverStatus.opWorkingTime.reads.ops"
                - "mongodb.serverStatus.opWorkingTime.transactions.latency"
                - "mongodb.serverStatus.opWorkingTime.transactions.ops"
                - "mongodb.serverStatus.opWorkingTime.writes.latency"
                - "mongodb.serverStatus.opWorkingTime.writes.ops"
                - "mongodb.serverStatus.oplog.earliestOptime"
                - "mongodb.serverStatus.oplog.latestOptime"
                - "mongodb.serverStatus.indexStats.count"
                - "mongodb.oplog.rsStats.size"
            - name: "strict-match-metrics-for-internal-grafana"
              match_type: "strict"
              metric_names:
                - "mongodb.serverStatus.repl.buffer.apply.count"
                - "mongodb.serverStatus.repl.apply.batches.num"
                - "mongodb.serverStatus.repl.apply.batches.totalMillis"
                - "mongodb.serverStatus.repl.initialSync.failedAttempts"
                - "mongodb.serverStatus.repl.initialSync.completed"
                - "mongodb.serverStatus.metrics.getLastError.wtime.totalMillis"
                - "mongodb.serverStatus.metrics.getLastError.wtime.num"
                - "mongodb.serverStatus.metrics.ttl.passes"
                - "mongodb.serverStatus.indexBuilds.total"
                - "mongodb.serverStatus.indexBuilds.commit"
                - "mongodb.serverStatus.indexBulkBuilder.numSorted"
                - "mongodb.serverStatus.indexBulkBuilder.bytesSorted"
                - "mongodb.serverStatus.indexBulkBuilder.bytesSpilled"
                - "mongodb.serverStatus.indexBulkBuilder.bytesSpilledUncompressed"
                - "mongodb.serverStatus.indexBulkBuilder.memUsage"
                - "mongodb.serverStatus.indexBulkBuilder.spilledRanges"
                - "mongodb.replStatus.syncSourceId"
                - "mongodb.replStatus.myState"
                - "mongodb.replStatus.members.pingMs"


