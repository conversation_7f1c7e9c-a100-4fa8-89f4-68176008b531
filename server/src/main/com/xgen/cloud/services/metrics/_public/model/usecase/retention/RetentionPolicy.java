package com.xgen.cloud.services.metrics._public.model.usecase.retention;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Set;

public class RetentionPolicy {
  private static final Set<String> VALID_POLICIES = Set.of("raw:7d", "raw:5d,1h:63d,1d:3y");

  public static final RetentionPolicy DEFAULT_RETENTION_POLICY =
      new RetentionPolicy("raw:5d,1h:63d,1d:3y");

  private String policy;

  public RetentionPolicy(String retentionPolicy) {
    if (!VALID_POLICIES.contains(retentionPolicy)) {
      throw new IllegalArgumentException(
          "Invalid retention policy. Must be one of: " + VALID_POLICIES);
    }
    this.policy = retentionPolicy;
  }

  @JsonProperty("policy")
  public String getAsString() {
    return policy;
  }
}
