package com.xgen.cloud.partnerintegrations.vercelnative._public.svc;

import com.xgen.cloud.partnerintegrations.vercelnative._public.model.InstalledProduct;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelData;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeInstallation;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.UserMetadata;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceNotification;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceStatus;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.stream.Stream;
import org.bson.types.ObjectId;

public interface VercelNativeInstallationsDaoSvc {
  /**
   * Finds an installation by installation ID, or an empty Optional
   *
   * @param installationId The ID of the Vercel Native installation
   */
  Optional<VercelNativeInstallation> findInstallation(String installationId);

  /**
   * Finds an installation by project ID
   *
   * @param projectId The project ID of the project linked in the Vercel Native installation
   */
  Optional<VercelNativeInstallation> findInstallation(ObjectId projectId);

  /**
   * Finds all active Vercel Native installations (where uninstalledAt is null)
   *
   * @return a stream of active installations
   */
  Stream<VercelNativeInstallation> findAllActiveInstallations();

  /**
   * Finds a specific Atlas cluster within a Vercel Native installation.
   *
   * @param installation the installation to search in
   * @param projectId the Atlas project ID
   * @param clusterName the cluster name
   * @return the cluster, if found.
   */
  Optional<InstalledProduct> findClusterInInstallation(
      VercelNativeInstallation installation, ObjectId projectId, String clusterName);

  /**
   * Finds an existing installation by installation ID. Throws an exception if the installation is
   * not found or is uninstalled.
   *
   * @param installationId The installation ID of the Vercel Native integration
   */
  VercelNativeInstallation findExistingInstallation(String installationId)
      throws NoSuchElementException;

  /**
   * Finds an existing project in a Vercel Native installation by id, or an empty Optional
   *
   * @param installation the installation to search in
   * @param projectId the project id to search for
   */
  Optional<InstalledProduct> findExistingProjectById(
      VercelNativeInstallation installation, ObjectId projectId);

  /**
   * Finds an existing project in a Vercel Native installation by the initialProjectName, or an
   * empty Optional
   *
   * @param installation the installation to search in
   * @param initialProjectName the initial project name to search for
   */
  Optional<InstalledProduct> findExistingProjectByName(
      VercelNativeInstallation installation, String initialProjectName);

  /**
   * Finds an existing resource in a Vercel Native installation with a specific resource ID. Throws
   * an exception if the resource is not found or is uninstalled.
   *
   * @param installation the installation to search in
   * @param resourceId the resource ID to search for
   */
  InstalledProduct findExistingResource(VercelNativeInstallation installation, String resourceId)
      throws NoSuchElementException;

  /**
   * Finds an existing resource in a Vercel Native installation with a specific resource ID. Throws
   * an exception if the resource is not found. Takes in a third parameter to determine if it should
   * return uninstalled resources or not.
   *
   * @param installation the installation to search in
   * @param resourceId the resource ID to search for
   * @param includeUninstalledResource whether to include uninstalled resources
   */
  InstalledProduct findExistingResource(
      VercelNativeInstallation installation, String resourceId, boolean includeUninstalledResource)
      throws NoSuchElementException;

  /**
   * Finds an existing resource in a Vercel Native installation with a specific idempotency key.
   *
   * @param installation the installation to search in
   * @param idempotencyKey the idempotency key to search for
   */
  Optional<InstalledProduct> findExistingResourceByIdempotencyKey(
      VercelNativeInstallation installation,
      String idempotencyKey,
      InstalledProduct.RequestIdempotencyKeyTypes operationType);

  /**
   * Finds all active installed products within a Vercel Native installation
   *
   * @param installation the installation to search in
   * @return a list of active installed products within the installation
   */
  List<InstalledProduct> findActiveInstalledProductsInInstallation(
      VercelNativeInstallation installation);

  /**
   * Checks if an installation is within resource limit bounds.
   *
   * @param installationId The ID of the Vercel Native installation
   */
  boolean isInstallationAtOrAboveResourceLimits(String installationId);

  /**
   * Checks if an installation is within resource limit bounds.
   *
   * @param installation A Vercel Native Installation object
   */
  boolean isInstallationAtOrAboveResourceLimits(VercelNativeInstallation installation);

  /**
   * Update or Create a Vercel Native installation. Suffixed with "Db" to differentiate it from the
   * "upsertInstallation" Marketplace API function.
   *
   * @param installationId The ID of the Vercel Native installation
   * @param installation The installation to upsert
   */
  void upsertInstallationDb(String installationId, VercelNativeInstallation installation);

  /**
   * Upserts the VercelData for an installation. This is intended for use in the upsertInstallation
   * flow, so this can create a new document in the collection if needed.
   *
   * @param installationId the Vercel-provided installationId of the installation to update (NOT an
   *     ObjectId)
   * @param vercelData the VercelData to upsert
   */
  void upsertVercelDataForInstallation(String installationId, VercelData vercelData);

  /**
   * Sets the organization ID for a Vercel Native installation. If there is no matching installation
   * for the given ID, then no update is made and will throw an error.
   *
   * @param installationId The ID of the Vercel Native installation
   * @param organizationId The organization ID to set
   * @return the updated installation
   */
  VercelNativeInstallation setOrganizationIdForInstallation(
      String installationId, ObjectId organizationId);

  /**
   * Sets the idempotency key for a given request type within the installedProducts array of a given
   * Vercel Native installation. Will make a new InstalledProducts entry unless a vercelResourceId
   * is provided.
   *
   * @param installation the installation to update
   * @param initialProjectName the initialProjectName of the resource. Used to check if there's an
   *     existing doc for the resource already.
   * @param metadata the user metadata to set. Used only in the CREATE operation; ignored otherwise.
   * @param idempotencyKey the idempotency key value to set. Can be null to unset.
   * @param operationType the type of operation to set the idempotency key for
   * @return the updated installation
   */
  VercelNativeInstallation setRequestIdempotencyKeyAndRequestDetails(
      VercelNativeInstallation installation,
      String initialProjectName,
      UserMetadata metadata,
      String idempotencyKey,
      InstalledProduct.RequestIdempotencyKeyTypes operationType);

  /**
   * Sets a new project ID and initial project name within the installedProducts array of a given
   * Vercel Native installation. Intended for use with the CREATE operation and requires the
   * installation to have a partially installed product with CREATE idempotency keys.
   *
   * @param installation the installation to update
   * @param projectId the new project ID
   * @param initialProjectName the project name at time of association
   * @param createIdempotencyKey the idempotency key for the CREATE operation, used to find the
   *     right resource in the installation
   * @return the updated installation
   */
  VercelNativeInstallation setProjectIdAndNameForInstallationOnCreate(
      VercelNativeInstallation installation,
      ObjectId projectId,
      String initialProjectName,
      String createIdempotencyKey);

  /**
   * Updates an Installed Product that matches the requestIdempotencyKey and operationType. If no
   * matching product is found, then nothing is updated. Only use this if a more targeted update
   * method (such as "updateInstalledProductUserMetadata") doesn't exist.
   *
   * @param requestIdempotencyKey the idempotency key to search for. Technically installedProduct
   *     should have this value too, but good to be explicit by having this as a separate param.
   * @param operationType the operation type (of the idempotency key) to search for. Technically
   *     installedProduct * should have this value too, but good to be explicit by having this as a
   *     separate param.
   * @param installedProduct the updated installed product. Needs to at least have vercelResourceId,
   *     which is used to look up the product after updating.
   * @return the updated installed product
   */
  InstalledProduct updateInstalledProductByIdempotencyKey(
      String requestIdempotencyKey,
      InstalledProduct.RequestIdempotencyKeyTypes operationType,
      InstalledProduct installedProduct);

  /**
   * Updates an Installed Product that matches the projectId. If no matching product is found, then
   * nothing is updated.
   *
   * @param projectId the project ID to search for
   * @param installedProduct the updated installed product
   * @return the updated installed product
   */
  InstalledProduct updateInstalledProductByProjectId(
      ObjectId projectId, InstalledProduct installedProduct);

  /**
   * Updates the userMetadata for an installed product and if provided, the Status and notification
   * properties too
   *
   * @param vercelResourceId the Vercel Resource ID of the installed product
   * @param newUserMetadata the new user metadata to set
   * @param newStatus the new status to set, can be null
   * @param newNotification the new notification to set, can be null
   * @return the updated installed product
   */
  InstalledProduct updateInstalledProductUserMetadata(
      ObjectId vercelResourceId,
      UserMetadata newUserMetadata,
      @Nullable VercelResourceStatus newStatus,
      @Nullable VercelResourceNotification newNotification);

  /**
   * Soft deletes a Vercel Native installation by its installation ID
   *
   * @param installationId the Vercel-provided installationId of the installation to delete (NOT an
   *     ObjectId)
   */
  void softDeleteInstallation(String installationId);

  /**
   * Soft deletes a specific resource within a Vercel Native installation
   *
   * @param resourceId the Vercel Resource ID of the resource
   */
  void softDeleteResource(String resourceId);
}
