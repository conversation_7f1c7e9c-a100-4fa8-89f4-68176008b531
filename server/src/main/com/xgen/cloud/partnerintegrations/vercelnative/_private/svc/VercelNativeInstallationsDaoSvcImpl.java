package com.xgen.cloud.partnerintegrations.vercelnative._private.svc;

import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.FREE_CLUSTER_LIMIT;
import static net.logstash.logback.argument.StructuredArguments.e;

import com.xgen.cloud.common.retry._public.RetryBackoffUtil;
import com.xgen.cloud.partnerintegrations.vercelnative._private.dao.VercelNativeInstallationDao;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.AtlasResources;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.InstalledProduct;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelData;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeInstallation;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeInstallationsDaoSvc;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.UserMetadata;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceNotification;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceStatus;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.util.retry.RetryBackoffSpec;

@Singleton
public class VercelNativeInstallationsDaoSvcImpl
    implements VercelNativeInstallationsDaoSvc, RetryBackoffUtil {

  private static final Logger LOG =
      LoggerFactory.getLogger(VercelNativeInstallationsDaoSvcImpl.class);

  private static final int RETRY_MAX_ATTEMPTS = 3;
  private static final Duration RETRY_MIN_BACKOFF = Duration.ofSeconds(1);

  private final VercelNativeInstallationDao vercelNativeInstallationDao;

  @Inject
  public VercelNativeInstallationsDaoSvcImpl(
      VercelNativeInstallationDao vercelNativeInstallationDao) {
    this.vercelNativeInstallationDao = vercelNativeInstallationDao;
  }

  @Override
  public Optional<VercelNativeInstallation> findInstallation(String installationId) {
    final Map<String, Object> logContext = new HashMap<>(Map.of("installationId", installationId));
    return callWithRetryBackoff(
        () -> vercelNativeInstallationDao.findByInstallationId(installationId),
        retryBackoffSpec("Retried findByInstallationId", logContext));
  }

  @Override
  public Optional<VercelNativeInstallation> findInstallation(ObjectId projectId) {
    final Map<String, Object> logContext = new HashMap<>(Map.of("projectId", projectId));
    return callWithRetryBackoff(
        () -> vercelNativeInstallationDao.findByProjectId(projectId),
        retryBackoffSpec("Retried findByProjectId", logContext));
  }

  @Override
  public Stream<VercelNativeInstallation> findAllActiveInstallations() {
    final Map<String, Object> logContext = Map.of();
    return callWithRetryBackoff(
        vercelNativeInstallationDao::findAllActiveInstallations,
        retryBackoffSpec("Retried findAllActiveInstallations", logContext));
  }

  @Override
  public Optional<InstalledProduct> findClusterInInstallation(
      VercelNativeInstallation installation, ObjectId projectId, String clusterName) {

    if (installation.atlasResources() == null
        || installation.atlasResources().installedProducts() == null) {
      return Optional.empty();
    }

    // note that this finds the matching cluster, even if it's uninstalled
    return installation.atlasResources().installedProducts().stream()
        .filter(
            cluster ->
                cluster.projectId().equals(projectId) && cluster.clusterName().equals(clusterName))
        .findFirst();
  }

  @WithSpan
  @Override
  public VercelNativeInstallation findExistingInstallation(String installationId)
      throws NoSuchElementException {
    final Map<String, Object> logContext = new HashMap<>(Map.of("installationId", installationId));
    final VercelNativeInstallation installation =
        callWithRetryBackoff(
                () -> vercelNativeInstallationDao.findByInstallationId(installationId),
                retryBackoffSpec("Retried findByInstallationId", logContext))
            .orElseThrow(
                () -> {
                  LOG.error("No installation found with id {}", installationId);
                  return new NoSuchElementException(
                      String.format("No installation found with id %s", installationId));
                });

    if (installation.uninstalledAt() != null) {
      LOG.error("Installation {} is uninstalled", installationId);
      throw new IllegalStateException(
          String.format("No active installation with id %s", installationId));
    }

    return installation;
  }

  @Override
  public Optional<InstalledProduct> findExistingProjectById(
      VercelNativeInstallation installation, ObjectId projectId) {
    if (installation.atlasResources() == null
        || installation.atlasResources().installedProducts() == null) {
      return Optional.empty();
    }

    // note that this finds an InstalledProduct with the matching project, even if it's uninstalled
    return installation.atlasResources().installedProducts().stream()
        .filter(cluster -> cluster.projectId() != null && cluster.projectId().equals(projectId))
        .findFirst();
  }

  @WithSpan
  @Override
  public Optional<InstalledProduct> findExistingProjectByName(
      VercelNativeInstallation installation, String initialProjectName) {
    if (installation.atlasResources() == null
        || installation.atlasResources().installedProducts() == null) {
      return Optional.empty();
    }

    // note that this finds an InstalledProduct with the matching project, even if it's uninstalled
    return installation.atlasResources().installedProducts().stream()
        .filter(
            cluster ->
                cluster.initialProjectName() != null
                    && cluster.initialProjectName().equals(initialProjectName))
        .findFirst();
  }

  @Override
  public InstalledProduct findExistingResource(
      VercelNativeInstallation installation, String resourceId) throws NoSuchElementException {
    return findExistingResource(installation, resourceId, false);
  }

  @Override
  public InstalledProduct findExistingResource(
      VercelNativeInstallation installation, String resourceId, boolean includeUninstalledResource)
      throws NoSuchElementException {
    final InstalledProduct resource =
        Optional.ofNullable(installation.atlasResources())
            .map(AtlasResources::installedProducts)
            .orElse(List.of())
            .stream()
            .filter(
                cluster ->
                    cluster.vercelResourceId() != null
                        && cluster.vercelResourceId().toString().equals(resourceId))
            .findFirst()
            .orElseThrow(
                () -> {
                  LOG.error("No resource found with id {}", resourceId);
                  return new NoSuchElementException(
                      String.format("No resource found with id %s", resourceId));
                });

    if (resource.uninstalledAt() != null && !includeUninstalledResource) {
      LOG.error("Resource {} is uninstalled", resourceId);
      throw new IllegalStateException(String.format("No active resource with id %s", resourceId));
    }

    return resource;
  }

  @WithSpan
  @Override
  public Optional<InstalledProduct> findExistingResourceByIdempotencyKey(
      VercelNativeInstallation installation,
      String idempotencyKey,
      InstalledProduct.RequestIdempotencyKeyTypes operationType) {
    return Optional.ofNullable(installation.atlasResources())
        .map(AtlasResources::installedProducts)
        .orElse(List.of())
        .stream()
        .filter(
            product ->
                product.requestIdempotencyKeys() != null
                    && Objects.equals(
                        product.requestIdempotencyKeys().get(operationType.name()), idempotencyKey))
        .findFirst();
  }

  @Override
  public List<InstalledProduct> findActiveInstalledProductsInInstallation(
      VercelNativeInstallation installation) {
    if (installation.atlasResources() == null) return List.of();
    return installation.atlasResources().installedProducts().stream()
        .filter(product -> product.uninstalledAt() == null)
        .toList();
  }

  @Override
  public boolean isInstallationAtOrAboveResourceLimits(VercelNativeInstallation installation) {
    return installation.atlasResources() != null
        && installation.atlasResources().installedProducts() != null
        && installation.atlasResources().installedProducts().stream()
                .filter(
                    cluster ->
                        cluster.userMetadata() != null
                            && cluster.userMetadata().clusterTier() == UserMetadata.ClusterTier.FREE
                            && cluster.uninstalledAt() == null)
                .count()
            >= FREE_CLUSTER_LIMIT;
  }

  // overload that does an installation lookup
  @Override
  public boolean isInstallationAtOrAboveResourceLimits(String installationId) {
    return findInstallation(installationId)
        .map(this::isInstallationAtOrAboveResourceLimits)
        .orElse(true);
  }

  @Override
  public void upsertInstallationDb(String installationId, VercelNativeInstallation installation) {
    final Map<String, Object> logContext = new HashMap<>(Map.of("installationId", installationId));

    callWithRetryBackoff(
        () -> {
          vercelNativeInstallationDao.upsertInstallation(installationId, installation);
          return null;
        },
        retryBackoffSpec("Retried upsertInstallation", logContext));

    LOG.info("Updated installation: {}", e(logContext));
  }

  @Override
  public void upsertVercelDataForInstallation(String installationId, VercelData vercelData) {
    final Map<String, Object> logContext =
        new HashMap<>(
            Map.of("installationId", installationId, "vercelData.teamName", vercelData.teamName()));

    callWithRetryBackoff(
        () -> {
          vercelNativeInstallationDao.upsertVercelDataForInstallation(installationId, vercelData);
          return null;
        },
        retryBackoffSpec("Retried upsertVercelDataForInstallation", logContext));

    LOG.info("Updated vercel data for installation: {}", e(logContext));
  }

  @Override
  public VercelNativeInstallation setOrganizationIdForInstallation(
      String installationId, ObjectId organizationId) {
    final Map<String, Object> logContext =
        new HashMap<>(
            Map.of(
                "installationId", installationId,
                "organizationId", organizationId));

    final Optional<VercelNativeInstallation> installationMaybe =
        callWithRetryBackoff(
            () -> {
              vercelNativeInstallationDao.updateInstallationOrganizationId(
                  installationId, organizationId);

              return vercelNativeInstallationDao.findByInstallationId(installationId);
            },
            retryBackoffSpec("Retried updateInstallationOrganizationId", logContext));

    LOG.info("Updated installation with organization id: {}", e(logContext));

    return installationMaybe.orElseThrow(
        () -> {
          LOG.error(
              "No installation found by installation id after trying to update organization id: {}",
              e(logContext));
          return new NoSuchElementException(
              String.format("No installation found with id %s", installationId));
        });
  }

  @Override
  public VercelNativeInstallation setRequestIdempotencyKeyAndRequestDetails(
      VercelNativeInstallation installation,
      String initialProjectName,
      @Nullable UserMetadata metadata,
      @Nullable String idempotencyKey,
      InstalledProduct.RequestIdempotencyKeyTypes operationType) {
    final Map<String, Object> logContext =
        new HashMap<>(
            Map.of(
                "installationId",
                installation.id(),
                "initialProjectName",
                initialProjectName,
                "operationType",
                operationType));

    if (idempotencyKey != null) {
      logContext.put("idempotencyKey", idempotencyKey);
    }

    if (metadata != null) {
      logContext.put("metadata", metadata);
    }

    // atlasResources could be null if this is the first resource being created
    VercelNativeInstallation installationWithAtlasResources = installation;
    final boolean shouldInitializeAtlasResources = installation.atlasResources() == null;
    if (shouldInitializeAtlasResources) {
      installationWithAtlasResources =
          installation.toBuilder()
              .atlasResources(AtlasResources.builder().installedProducts(List.of()).build())
              .build();
    }

    // check for existing doc with key
    final Optional<InstalledProduct> existingResourceWithKey =
        findExistingResourceByIdempotencyKey(
            installationWithAtlasResources, idempotencyKey, operationType);

    if (existingResourceWithKey.isPresent()) {
      return installation;
    }

    // check for existing doc for project
    final Optional<InstalledProduct> resourceMaybe =
        findExistingProjectByName(installationWithAtlasResources, initialProjectName);
    final boolean shouldInitializeInstalledProduct = resourceMaybe.isEmpty();

    final Optional<VercelNativeInstallation> installationMaybe;
    if (operationType == InstalledProduct.RequestIdempotencyKeyTypes.CREATE) {
      if (!shouldInitializeInstalledProduct) {
        final InstalledProduct existingProduct = resourceMaybe.get();
        if (existingProduct.createdAt() != null) {
          LOG.error(
              "Resource {} already was created at {}, no need to update idempotency key: {}",
              initialProjectName,
              existingProduct.createdAt(),
              e(logContext));
          throw new IllegalStateException(
              String.format(
                  "Resource %s already was created at %s, no need to update idempotency key",
                  initialProjectName, existingProduct.createdAt()));
        }
      }

      installationMaybe =
          callWithRetryBackoff(
              () -> {
                vercelNativeInstallationDao
                    .updateInstalledProductIdempotencyKeyAndUserMetadataForCreate(
                        installation.vercelData().installationId(),
                        initialProjectName,
                        idempotencyKey,
                        metadata,
                        shouldInitializeAtlasResources,
                        shouldInitializeInstalledProduct);
                return vercelNativeInstallationDao.findByInstallationId(
                    installation.vercelData().installationId());
              },
              retryBackoffSpec(
                  "Retried updateInstalledProductIdempotencyKeyAndUserMetadataForCreate",
                  logContext));
    } else {
      throw new UnsupportedOperationException(
          "This method is not implemented for non-CREATE operations");
    }

    LOG.info("Updated installation with idempotency key: {}", e(logContext));

    return installationMaybe.orElseThrow(
        () -> {
          LOG.error(
              "No installation found by installation id after trying to update idempotency key: {}",
              e(logContext));
          return new NoSuchElementException(
              String.format("No installation found with id %s", installation.id()));
        });
  }

  @Override
  public VercelNativeInstallation setProjectIdAndNameForInstallationOnCreate(
      VercelNativeInstallation installation,
      ObjectId projectId,
      String initialProjectName,
      String createIdempotencyKey) {
    final Map<String, Object> logContext =
        new HashMap<>(
            Map.of(
                "installationId", installation.id(),
                "projectId", projectId,
                "initialProjectName", initialProjectName,
                "createIdempotencyKey", createIdempotencyKey));

    if ((findExistingProjectById(installation, projectId)).isPresent()) {
      LOG.warn(
          "Installation {} already has project {} installed, skipping",
          installation.id(),
          projectId);
      return installation;
    }

    if (installation.atlasResources() == null
        || installation.atlasResources().organizationId() == null) {
      LOG.error(
          "Installation {} does not have an org yet, cannot set project {}",
          installation.id(),
          projectId);
      throw new IllegalStateException(
          String.format(
              "Installation %s does not have an org yet, cannot set project %s",
              installation.id(), projectId));
    }

    final Optional<VercelNativeInstallation> installationMaybe =
        callWithRetryBackoff(
            () -> {
              vercelNativeInstallationDao.updateInstalledProductProjectIdAndNameByIdempotencyKey(
                  createIdempotencyKey,
                  InstalledProduct.RequestIdempotencyKeyTypes.CREATE,
                  projectId,
                  initialProjectName);
              return vercelNativeInstallationDao.findByProjectId(projectId);
            },
            retryBackoffSpec(
                "Retried updateInstalledProductProjectIdAndNameByIdempotencyKey", logContext));

    LOG.info("Updated installation with project id and name: {}", e(logContext));

    return installationMaybe.orElseThrow(
        () -> {
          LOG.error(
              "No installation found with the matching project id after trying to update project id"
                  + " and name: {}",
              e(logContext));
          return new NoSuchElementException(
              String.format("No installation found with project id %s", projectId));
        });
  }

  @Override
  public InstalledProduct updateInstalledProductByIdempotencyKey(
      String requestIdempotencyKey,
      InstalledProduct.RequestIdempotencyKeyTypes operationType,
      InstalledProduct installedProduct) {
    final Map<String, Object> logContext =
        new HashMap<>(
            Map.of(
                "requestIdempotencyKey", requestIdempotencyKey,
                "operationType", operationType,
                "installedProduct", installedProduct));

    // if we didn't want this requirement, we could add a "findByRequestIdempotencyKey" method and
    // use that below instead.
    // Given that I believe all our current usages of this method will have the vercelResourceId in
    // installedProduct, I am using that prop for now.
    if (installedProduct.vercelResourceId() == null) {
      LOG.error("vercelResourceId is required in the installedProduct update: {}", e(logContext));
      throw new IllegalStateException(
          "vercelResourceId is required in the installedProduct update");
    }

    logContext.put("vercelResourceId", installedProduct.vercelResourceId());

    final Optional<VercelNativeInstallation> installationMaybe =
        callWithRetryBackoff(
            () -> {
              vercelNativeInstallationDao.updateInstalledProductByIdempotencyKey(
                  requestIdempotencyKey, operationType, installedProduct);

              return vercelNativeInstallationDao.findByVercelResourceId(
                  installedProduct.vercelResourceId());
            },
            retryBackoffSpec("Retried updateInstalledProductByIdempotencyKey", logContext));

    return findUpdatedResourceFromOptionalInstallation(
        installationMaybe, installedProduct.vercelResourceId().toString(), null, logContext);
  }

  @Override
  public InstalledProduct updateInstalledProductByProjectId(
      ObjectId projectId, InstalledProduct installedProduct) {
    final Map<String, Object> logContext =
        new HashMap<>(
            Map.of(
                "projectId", projectId,
                "installedProduct", installedProduct));

    final Optional<VercelNativeInstallation> installationMaybe =
        callWithRetryBackoff(
            () -> {
              vercelNativeInstallationDao.updateInstalledProductByProjectId(
                  projectId, installedProduct);

              return vercelNativeInstallationDao.findByProjectId(projectId);
            },
            retryBackoffSpec("Retried updateInstalledProductByProjectId", logContext));

    return findUpdatedResourceFromOptionalInstallation(
        installationMaybe, null, projectId.toString(), logContext);
  }

  @Override
  public InstalledProduct updateInstalledProductUserMetadata(
      ObjectId resourceId,
      UserMetadata newUserMetadata,
      @Nullable VercelResourceStatus newStatus,
      @Nullable VercelResourceNotification newNotification) {
    final Map<String, Object> logContext =
        new HashMap<>(Map.of("resourceId", resourceId, "newUserMetadata", newUserMetadata));

    if (newStatus != null) {
      logContext.put("newStatus", newStatus);
    }
    if (newNotification != null) {
      logContext.put("newNotification", newNotification);
    }

    final Optional<VercelNativeInstallation> installationMaybe =
        callWithRetryBackoff(
            () -> {
              vercelNativeInstallationDao.updateInstalledProductUserMetadata(
                  resourceId, newUserMetadata, newStatus, newNotification);

              return vercelNativeInstallationDao.findByVercelResourceId(resourceId);
            },
            retryBackoffSpec("Retried updateInstalledProductUserMetadata", logContext));

    return findUpdatedResourceFromOptionalInstallation(
        installationMaybe, resourceId.toString(), null, logContext);
  }

  @Override
  public void softDeleteInstallation(String installationId) {
    final Map<String, Object> logContext = new HashMap<>(Map.of("installationId", installationId));
    callWithRetryBackoff(
        () -> {
          vercelNativeInstallationDao.softDeleteInstallation(installationId);
          return null;
        },
        retryBackoffSpec("Retried softDeleteInstallation", logContext));

    LOG.info("Soft-deleted installation: {}", e(logContext));
  }

  @Override
  public void softDeleteResource(String resourceId) {
    final Map<String, Object> logContext = new HashMap<>(Map.of("resourceId", resourceId));
    callWithRetryBackoff(
        () -> {
          vercelNativeInstallationDao.softDeleteInstalledProduct(new ObjectId(resourceId));
          return null;
        },
        retryBackoffSpec("Retried softDeleteResource", logContext));

    LOG.info("Soft-deleted resource: {}", e(logContext));
  }

  private RetryBackoffSpec retryBackoffSpec(String message, Map<String, Object> logContext) {
    return defaultRetryBackoffSpec(RETRY_MAX_ATTEMPTS, RETRY_MIN_BACKOFF, message, logContext);
  }

  private InstalledProduct findUpdatedResourceFromOptionalInstallation(
      Optional<VercelNativeInstallation> installationMaybe,
      @Nullable String vercelResourceId,
      @Nullable String projectId,
      Map<String, Object> logContext) {
    if (installationMaybe.isEmpty()) {
      LOG.error("No installation found after trying to update resource: {}", e(logContext));
      throw new NoSuchElementException("No installation found after trying to update resource");
    }

    final VercelNativeInstallation installation = installationMaybe.get();
    logContext.put("installationId", installation.vercelData().installationId());
    LOG.info("Updated installation with installed product: {}", e(logContext));

    if (vercelResourceId != null) {
      return findExistingResource(installation, vercelResourceId);
    } else {
      final Optional<InstalledProduct> resourceMaybe =
          findExistingProjectById(installation, new ObjectId(projectId));
      if (resourceMaybe.isEmpty()) {
        LOG.error("No matching resource found after trying to update resource: {}", e(logContext));
        throw new NoSuchElementException("No resource found after trying to update resource");
      }

      return resourceMaybe.get();
    }
  }
}
