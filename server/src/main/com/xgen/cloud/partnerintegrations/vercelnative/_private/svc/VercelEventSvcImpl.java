package com.xgen.cloud.partnerintegrations.vercelnative._private.svc;

import static com.xgen.cloud.common.metrics._public.constants.MonitoringConstants.ATLAS_GROWTH_PROM_NAMESPACE;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.COUNTER_AUDIT_TYPE_LABEL_NAME;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.COUNTER_ERROR_LABEL_NAME;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.COUNTER_STATUS_FAILURE_VALUE;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.COUNTER_STATUS_LABEL_NAME;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.COUNTER_STATUS_SUCCESS_ERROR_MESSAGE_VALUE;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.COUNTER_STATUS_SUCCESS_VALUE;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.E2E_TESTING_INTEGRATION_SLUG;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.VERCEL_RESOURCE_PENDING_NOTIFICATION_MESSAGE;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.constant.Constants.VERCEL_RESOURCE_PENDING_NOTIFICATION_TITLE;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelEventJobHandler.ACCESS_TOKEN_FIELD;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelEventJobHandler.INSTALLATION_ID_FIELD;
import static com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelEventJobHandler.VERCEL_RESOURCE_FIELD;
import static net.logstash.logback.argument.StructuredArguments.e;

import com.mongodb.BasicDBObject;
import com.xgen.cloud.common.jobqueue._public.model.Job;
import com.xgen.cloud.common.jobqueue._public.model.JobHandlerEnum;
import com.xgen.cloud.common.jobqueue._public.svc.JobsProcessorSvc;
import com.xgen.cloud.nds.activity._public.event.audit.NDSAudit;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import com.xgen.cloud.partnerintegrations.vercelnative._public.config.VercelNativeConfig;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.InstalledProduct;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeInstallation;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelEventSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeInstallationsDaoSvc;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.UserMetadata;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResource;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceNotification;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceNotificationLevel;
import com.xgen.cloud.partners.vercel.sdk.model._public.model.resource.VercelResourceStatus;
import io.prometheus.client.Counter;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class VercelEventSvcImpl implements VercelEventSvc {
  private static final Logger LOG = LoggerFactory.getLogger(VercelEventSvcImpl.class);

  private static final Counter VERCEL_EVENT_SVC_COUNTER =
      Counter.build()
          .name("vercel_event_svc_total")
          .help("Total Vercel Event Service operations")
          .labelNames(
              COUNTER_STATUS_LABEL_NAME, COUNTER_ERROR_LABEL_NAME, COUNTER_AUDIT_TYPE_LABEL_NAME)
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();

  // Default job configuration
  private static final int DEFAULT_RETRIES = 10;
  private static final Duration DEFAULT_TIMEOUT = Duration.ofMinutes(5);

  private final VercelNativeInstallationsDaoSvc vercelNativeInstallationsDaoSvc;
  private final VercelNativeConfig vercelNativeConfig;
  private final JobsProcessorSvc jobsProcessorSvc;

  @Inject
  public VercelEventSvcImpl(
      VercelNativeInstallationsDaoSvc vercelNativeInstallationsDaoSvc,
      VercelNativeConfig vercelNativeConfig,
      JobsProcessorSvc jobsProcessorSvc) {
    this.vercelNativeInstallationsDaoSvc = vercelNativeInstallationsDaoSvc;
    this.vercelNativeConfig = vercelNativeConfig;
    this.jobsProcessorSvc = jobsProcessorSvc;
  }

  @Override
  public void sendProviderUpdate(ClusterDescription clusterDescription, NDSAudit.Type auditType) {
    final Map<String, Object> logContext = new LinkedHashMap<>();
    logContext.put("clusterName", clusterDescription.getName());
    logContext.put("auditType", auditType.name());

    LOG.info("Processing provider update for cluster: {}", e(logContext));

    try {
      // Validate and get installation and cluster
      final ValidationResult validationResult =
          validateClusterForUpdate(clusterDescription, auditType, logContext);
      if (!validationResult.isValid()) {
        return; // Early return if validation fails (warnings already logged)
      }

      final VercelNativeInstallation installation = validationResult.installation();
      final InstalledProduct installedProduct = validationResult.installedProduct();
      final VercelResourceStatus newStatus = validationResult.newStatus();

      // Create notification for specific flows (cluster paused, deleted, error)
      final VercelResourceNotification notification =
          createNotificationIfNeeded(clusterDescription, newStatus);

      // Update the installedProduct in the database
      final InstalledProduct updatedInstalledProduct =
          updateInstalledProductInDb(
              installation,
              installedProduct,
              newStatus,
              notification,
              clusterDescription,
              logContext);

      // Get the encrypted access token
      final String accessToken = installation.vercelData().accessToken();

      final String vercelProductId = setVercelProductId(installation, logContext);

      if (vercelNativeConfig.isLoadTestingScenario()) {
        LOG.info(
            "Is load testing scenario, skipping job submission for Vercel API for cluster: {}",
            e(logContext));
      } else {
        // Create VercelResource object
        VercelResource vercelResource =
            VercelResource.builder()
                .id(updatedInstalledProduct.vercelResourceId().toString())
                .productId(vercelProductId)
                .metadata(updatedInstalledProduct.userMetadata())
                .name(clusterDescription.getName())
                .status(newStatus)
                .notification(notification)
                .build();

        logContext.put("vercelResourceId", updatedInstalledProduct.vercelResourceId());

        // Submit a job to the JobsProcessorSvc
        submitProviderUpdateJob(
            vercelResource, accessToken, installation.vercelData().installationId(), logContext);
      }

      // Increment success counter
      incrementSuccessCounter(auditType);
    } catch (Exception e) {
      LOG.error("Error processing provider update for cluster: {}", e(logContext), e);
      incrementFailureCounter("general_error", auditType);
      throw new RuntimeException("Failed to send provider update", e);
    }
  }

  protected void incrementSuccessCounter(NDSAudit.Type auditType) {
    VERCEL_EVENT_SVC_COUNTER
        .labels(
            COUNTER_STATUS_SUCCESS_VALUE,
            COUNTER_STATUS_SUCCESS_ERROR_MESSAGE_VALUE,
            auditType.name())
        .inc();
  }

  protected void incrementFailureCounter(String errorType, NDSAudit.Type auditType) {
    VERCEL_EVENT_SVC_COUNTER
        .labels(COUNTER_STATUS_FAILURE_VALUE, errorType, auditType.name())
        .inc();
  }

  /** Record to hold validation results for cluster update operations. */
  private record ValidationResult(
      boolean isValid,
      VercelNativeInstallation installation,
      InstalledProduct installedProduct,
      VercelResourceStatus newStatus) {

    static ValidationResult invalid() {
      return new ValidationResult(false, null, null, null);
    }

    static ValidationResult valid(
        VercelNativeInstallation installation,
        InstalledProduct installedProduct,
        VercelResourceStatus newStatus) {
      return new ValidationResult(true, installation, installedProduct, newStatus);
    }
  }

  /**
   * Validates that the cluster can be updated and returns the installation, cluster, and new status
   * if valid.
   */
  private ValidationResult validateClusterForUpdate(
      ClusterDescription clusterDescription,
      NDSAudit.Type auditType,
      Map<String, Object> logContext) {
    // Find the vercelNativeInstallations document by project ID
    Optional<VercelNativeInstallation> installationOpt =
        vercelNativeInstallationsDaoSvc.findInstallation(clusterDescription.getGroupId());
    logContext.put("projectId", clusterDescription.getGroupId());

    if (installationOpt.isEmpty()) {
      LOG.error("No Vercel Native installation found for project ID: {}", e(logContext));
      incrementFailureCounter("installation_not_found", auditType);
      return ValidationResult.invalid();
    }

    VercelNativeInstallation installation = installationOpt.get();
    logContext.put("installationId", installation.vercelData().installationId());

    // Check if the installation has atlas resources
    if (installation.atlasResources() == null) {
      LOG.error(
          "No Atlas resources found in Vercel Native installation with installation ID: {}",
          e(logContext));
      incrementFailureCounter("no_atlas_resources", auditType);
      return ValidationResult.invalid();
    }

    // Find the specific InstalledProduct in the installation
    // Note that this MAY be an incomplete InstalledProduct doc, if the full provisionResource flow
    // did not successfully save the resource to the DB
    // However, this should at least have project ID and project name
    final Optional<InstalledProduct> clusterOpt =
        vercelNativeInstallationsDaoSvc.findExistingProjectById(
            installation, clusterDescription.getGroupId());
    logContext.put("clusterName", clusterDescription.getName());

    if (clusterOpt.isEmpty()) {
      LOG.error(
          "Project/cluster not found in Vercel Native installation with installation ID: {}",
          e(logContext));
      incrementFailureCounter("cluster_not_found", auditType);
      return ValidationResult.invalid();
    }

    InstalledProduct installedProduct = clusterOpt.get();

    // Determine the new status based on cluster description
    VercelResourceStatus newStatus = mapClusterStateToVercelStatus(clusterDescription);

    // Validate that if we're not updating to UNINSTALLED, then uninstalledAt should be null
    if (newStatus != VercelResourceStatus.UNINSTALLED && installedProduct.uninstalledAt() != null) {
      LOG.error(
          "Cluster {} has uninstalledAt set ({}) but status is being updated to {} (not"
              + " UNINSTALLED). Skipping update as this indicates an inconsistent state."
              + " logContext: {}",
          installedProduct.clusterName(),
          installedProduct.uninstalledAt(),
          newStatus,
          e(logContext));
      incrementFailureCounter("invalid_uninstalled_state", auditType);
      return ValidationResult.invalid();
    }

    return ValidationResult.valid(installation, installedProduct, newStatus);
  }

  private VercelResourceStatus mapClusterStateToVercelStatus(
      ClusterDescription clusterDescription) {
    if (clusterDescription.isDeletedOrDeleteRequested()) {
      return VercelResourceStatus.UNINSTALLED;
    } else if (clusterDescription.isPaused()) {
      return VercelResourceStatus.SUSPENDED;
      // the hosts check is what NDSClusterSvc uses to determine if the audit type is CLUSTER_READY
    } else if (clusterDescription.getMongoDBUriHosts().length == 0
        || clusterDescription.getState() == ClusterDescription.State.IDLE) {
      return VercelResourceStatus.READY;
    } else {
      return VercelResourceStatus.PENDING;
    }
  }

  private InstalledProduct updateInstalledProductInDb(
      VercelNativeInstallation installation,
      InstalledProduct installedProduct,
      VercelResourceStatus newStatus,
      VercelResourceNotification notification,
      ClusterDescription clusterDescription,
      Map<String, Object> logContext) {
    logContext.put("oldStatus", installedProduct.status());
    logContext.put("newStatus", newStatus);

    LOG.info("Updating cluster data for cluster: {}", e(logContext));

    return saveUpdatedInstalledProduct(
        installedProduct, newStatus, notification, clusterDescription, logContext);
  }

  private VercelResourceNotification createNotificationIfNeeded(
      ClusterDescription clusterDescription, VercelResourceStatus status) {

    return switch (status) {
      case SUSPENDED -> // paused cluster
          VercelResourceNotification.builder()
              .level(VercelResourceNotificationLevel.WARN)
              .title("Cluster Paused")
              .message(String.format("Cluster '%s' has been paused", clusterDescription.getName()))
              .build();
      case UNINSTALLED -> // deleted cluster
          VercelResourceNotification.builder()
              .level(VercelResourceNotificationLevel.INFO)
              .title("Cluster Deleted")
              .message(String.format("Cluster '%s' has been deleted", clusterDescription.getName()))
              .build();
      case ERROR ->
          VercelResourceNotification.builder()
              .level(VercelResourceNotificationLevel.ERROR)
              .title("Cluster Error")
              .message(
                  String.format(
                      "Cluster '%s' encountered an error. Please see Atlas for more details.",
                      clusterDescription.getName()))
              .href(
                  String.format(
                      "https://cloud.mongodb.com/v2/%s#/clusters", clusterDescription.getGroupId()))
              .build();
      case PENDING -> // provisioning or upgrading cluster
          VercelResourceNotification.builder()
              .level(VercelResourceNotificationLevel.INFO)
              // we use centralized constants for this one because it's also used in another spot
              // (VercelNativeResourcesSvcImpl)
              .title(VERCEL_RESOURCE_PENDING_NOTIFICATION_TITLE)
              .message(VERCEL_RESOURCE_PENDING_NOTIFICATION_MESSAGE)
              .build();
      default -> null; // No notification needed for other states (READY, RESUMED)
    };
  }

  private void submitProviderUpdateJob(
      VercelResource vercelResource,
      String accessToken,
      String installationId,
      Map<String, Object> logContext) {
    LOG.info("Submitting provider update job for resource: {}", e(logContext));

    BasicDBObject jobParams =
        new BasicDBObject()
            .append(VERCEL_RESOURCE_FIELD, vercelResource.toDBObject())
            .append(ACCESS_TOKEN_FIELD, accessToken)
            .append(INSTALLATION_ID_FIELD, installationId);

    Job job =
        new Job.Builder(JobHandlerEnum.VERCEL_EVENT_JOB_HANDLER, jobParams)
            .retriesRemaining(DEFAULT_RETRIES)
            .intervalUntilRerun(DEFAULT_TIMEOUT)
            .build();
    ObjectId jobId = jobsProcessorSvc.submitJob(job);

    LOG.info(
        "Provider update job submitted with ID: {} and resource {}; logContext: {}",
        jobId,
        jobParams.get(VERCEL_RESOURCE_FIELD),
        e(logContext));
  }

  /**
   * Helper method to update a specific cluster within an installation and return the updated
   * installed product.
   */
  private InstalledProduct saveUpdatedInstalledProduct(
      InstalledProduct baseCluster,
      VercelResourceStatus newStatus,
      VercelResourceNotification notification,
      ClusterDescription clusterDescription,
      Map<String, Object> logContext) {
    final Instant now = Instant.now();
    // Create the updated cluster with new status and updatedAt timestamp
    final InstalledProduct.Builder updatedClusterBuilder =
        baseCluster.toBuilder().status(newStatus).notification(notification).updatedAt(now);

    // address edge case where baseCluster is an incomplete InstalledProduct document, possibly due
    // to not being saved to the DB properly
    if (baseCluster.createdAt() == null) {
      updatedClusterBuilder.vercelResourceId(ObjectId.get());
      updatedClusterBuilder.clusterName(clusterDescription.getName());
      updatedClusterBuilder.createdAt(now);
    }

    switch (newStatus) {
      case UNINSTALLED -> {
        updatedClusterBuilder.uninstalledAt(Instant.now());
        LOG.info("Updating cluster uninstalledAt: {}", e(logContext));
      }
      case READY -> {
        // if cluster is ready, sync up cluster tier info with the Atlas ClusterDescription (covers
        // edge cases like Atlas UI-triggered cluster upgrades)
        UserMetadata.ClusterTier clusterTier;
        if (clusterDescription.isFreeTenantCluster()) {
          clusterTier = UserMetadata.ClusterTier.FREE;
        } else if (clusterDescription.isFlexTenantCluster()) {
          clusterTier = UserMetadata.ClusterTier.FLEX;
        } else {
          clusterTier = UserMetadata.ClusterTier.M10;
        }

        // we don't allow changing region, so use the existing region
        UserMetadata updatedUserMetadata =
            new UserMetadata(clusterTier, baseCluster.userMetadata().vercelRegion());

        updatedClusterBuilder.userMetadata(updatedUserMetadata);

        logContext.put("clusterTier", clusterTier);
        LOG.info("Setting cluster tier: {}", e(logContext));
      }
      default -> {}
    }

    InstalledProduct updatedCluster = updatedClusterBuilder.build();

    vercelNativeInstallationsDaoSvc.updateInstalledProductByProjectId(
        updatedCluster.projectId(), updatedCluster);

    return updatedCluster;
  }

  private String setVercelProductId(
      VercelNativeInstallation installation, Map<String, Object> logContext) {
    if (installation.vercelData().installationUrl().contains(E2E_TESTING_INTEGRATION_SLUG)) {
      logContext.put("vercelProductId", vercelNativeConfig.getVercelProductIdForE2ETesting());
      LOG.info("Setting vercelProductId to E2E testing value: {}", e(logContext));

      return vercelNativeConfig.getVercelProductIdForE2ETesting();
    } else {
      // no need to log in this case because this is the default case and expected
      return vercelNativeConfig.getVercelProductId();
    }
  }
}
