package com.xgen.cloud.partnerintegrations.vercelnative._private.svc;

import static com.xgen.cloud.common.metrics._public.constants.MonitoringConstants.IAM_PROM_NAMESPACE;
import static com.xgen.cloud.partnerintegrations.common._public.model.VercelUserToken.NOT_PROVIDED;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.google.common.annotations.VisibleForTesting;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.JWTParser;
import com.nimbusds.oauth2.sdk.token.AccessToken;
import com.nimbusds.oauth2.sdk.token.BearerAccessToken;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.authn._public.client.PartnerIdentityClient;
import com.xgen.cloud.authn._public.client.PartnerIdentityClientProvider;
import com.xgen.cloud.authn._public.exceptions.AuthnServiceException;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.authn._public.model.PartnerIdentityType;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.partnerintegrations.common._public.model.IntegrationType;
import com.xgen.cloud.partnerintegrations.common._public.model.PartnerIntegrationsData;
import com.xgen.cloud.partnerintegrations.common._public.model.TentativePartnerAccountLink;
import com.xgen.cloud.partnerintegrations.common._public.model.VercelRole;
import com.xgen.cloud.partnerintegrations.common._public.model.VercelSystemToken;
import com.xgen.cloud.partnerintegrations.common._public.model.VercelToken;
import com.xgen.cloud.partnerintegrations.common._public.model.VercelUserToken;
import com.xgen.cloud.partnerintegrations.common._public.svc.TentativePartnerAccountLinkSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.config.VercelNativeConfig;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.AtlasResources;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelData;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.VercelNativeInstallation;
import com.xgen.cloud.partnerintegrations.vercelnative._public.model.enums.VercelNativeIntegrationErrorCode;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelAuthSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeAccountSvc;
import com.xgen.cloud.partnerintegrations.vercelnative._public.svc.VercelNativeInstallationsDaoSvc;
import com.xgen.cloud.partners.vercel.sdk.client._public.exception.VercelApiRequestException;
import com.xgen.cloud.partners.vercel.sdk.client._public.svc.VercelMarketplaceApiClient;
import com.xgen.cloud.services.authn.proto.v1.PartnerIdentity;
import com.xgen.cloud.services.authn.proto.v1.PartnerIdentityMapping;
import com.xgen.cloud.user._private.svc.OrgAccessSvc;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.PartnerIntegrationUserSource;
import com.xgen.cloud.user._public.model.UserRegistrationForm;
import com.xgen.cloud.user._public.svc.UserSvc;
import io.micrometer.core.annotation.Timed;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.prometheus.client.Counter;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

/**
 * Provides various methods to handle IAM Workload Identity business logic around the installation,
 * uninstallation, permission sync, user provisioning, partner identity management, JWT decoding,
 * and rollback for the Vercel Native Integration flows.
 */
@Singleton
@Timed(value = "vercel_native_account_svc_methods_duration_seconds", histogram = true)
public class VercelNativeAccountSvcImpl implements VercelNativeAccountSvc {
  public static final String ACCESS_TOKEN_FIELD = "accessToken";
  private static final Logger LOG = LoggerFactory.getLogger(VercelNativeAccountSvcImpl.class);
  private static final int MAX_API_CONCURRENCY = 10;
  private static final int MAX_DB_CONCURRENCY = 10;
  private static final Scheduler SCHEDULER = Schedulers.boundedElastic();

  private final UserSvc userSvc;
  private final VercelAuthSvc vercelAuthSvc;
  private final PartnerIdentityClient partnerIdentityClient;
  private final VercelNativeInstallationsDaoSvc installationsSvc;
  private final VercelMarketplaceApiClient vercelMarketplaceApiClient;
  private final VercelNativeConfig vercelNativeConfig;
  private final OrgAccessSvc orgAccessSvc;
  private final TentativePartnerAccountLinkSvc tentativePartnerAccountLinkSvc;

  private static final Counter VERCEL_NATIVE_INSTALLATION_COUNTER =
      Counter.build()
          .name("vercel_native_installation_total")
          .help("Total Vercel Native Integration installation calls")
          .namespace(IAM_PROM_NAMESPACE)
          .register();
  private static final Counter VERCEL_NATIVE_INSTALLATION_ERROR_COUNTER =
      Counter.build()
          .name("vercel_native_installation_error_total")
          .help("Total Vercel Native Integration installation errors")
          .namespace(IAM_PROM_NAMESPACE)
          .register();
  private static final Counter VERCEL_NATIVE_UNINSTALLATION_COUNTER =
      Counter.build()
          .name("vercel_native_uninstallation_total")
          .help("Total Vercel Native Integration uninstallation calls")
          .namespace(IAM_PROM_NAMESPACE)
          .register();
  private static final Counter VERCEL_NATIVE_UNINSTALLATION_ERROR_COUNTER =
      Counter.build()
          .name("vercel_native_uninstallation_error_total")
          .help("Total Vercel Native Integration uninstallation errors")
          .namespace(IAM_PROM_NAMESPACE)
          .register();
  private static final Counter VERCEL_NATIVE_OIDC_CALLBACK_COUNTER =
      Counter.build()
          .name("vercel_native_oidc_callback_total")
          .help("Total Vercel Native Integration OIDC callback calls")
          .namespace(IAM_PROM_NAMESPACE)
          .register();
  private static final Counter VERCEL_NATIVE_OIDC_CALLBACK_ERROR_COUNTER =
      Counter.build()
          .name("vercel_native_oidc_callback_error_total")
          .help("Total Vercel Native Integration OIDC callback errors")
          .namespace(IAM_PROM_NAMESPACE)
          .register();
  private static final Counter VERCEL_NATIVE_ROLLBACK_COUNTER =
      Counter.build()
          .name("vercel_native_rollback_total")
          .help("Total Vercel Native Integration rollback calls")
          .namespace(IAM_PROM_NAMESPACE)
          .register();
  private static final Counter VERCEL_NATIVE_ROLLBACK_ERROR_COUNTER =
      Counter.build()
          .name("vercel_native_rollback_error_total")
          .help("Total Vercel Native Integration rollback errors")
          .namespace(IAM_PROM_NAMESPACE)
          .register();

  @Inject
  public VercelNativeAccountSvcImpl(
      UserSvc userSvc,
      VercelAuthSvc vercelAuthSvc,
      PartnerIdentityClientProvider partnerIdentityClientProvider,
      VercelNativeInstallationsDaoSvc installationsSvc,
      VercelMarketplaceApiClient vercelMarketplaceApiClient,
      VercelNativeConfig vercelNativeConfig,
      OrgAccessSvc orgAccessSvc,
      TentativePartnerAccountLinkSvc tentativePartnerAccountLinkSvc) {
    this.userSvc = userSvc;
    this.vercelAuthSvc = vercelAuthSvc;
    this.partnerIdentityClient = partnerIdentityClientProvider.get();
    this.installationsSvc = installationsSvc;
    this.vercelMarketplaceApiClient = vercelMarketplaceApiClient;
    this.vercelNativeConfig = vercelNativeConfig;
    this.orgAccessSvc = orgAccessSvc;
    this.tentativePartnerAccountLinkSvc = tentativePartnerAccountLinkSvc;
  }

  private static <T> Mono<T> getMono(Callable<T> callable) {
    return Mono.fromCallable(callable).subscribeOn(SCHEDULER);
  }

  private static Mono<Object> getMono(Runnable runnable) {
    return Mono.fromRunnable(runnable).subscribeOn(SCHEDULER);
  }

  @Override
  public boolean isUserLinkedToVercelNativeIntegration(String username) {
    AppUser user = userSvc.findByUsername(username);

    return user != null
        && user.getPartnerIntegrationsData() != null
        && user.getPartnerIntegrationsData().getIntegrationType() == IntegrationType.VERCEL_NATIVE;
  }

  @Override
  public boolean isUserLinkedToOtherIntegrationType(String username) {
    AppUser user = userSvc.findByUsername(username);

    return user != null
        && user.getPartnerIntegrationsData() != null
        && user.getPartnerIntegrationsData().getIntegrationType() != IntegrationType.VERCEL_NATIVE;
  }

  @WithSpan
  @Override
  public boolean hasExistingPartnerIdentityMapping(String username, String installationId) {
    try {
      return partnerIdentityClient
          .getMappingByEmail(username)
          .map(
              mapping ->
                  mapping.getIdentitiesList().stream()
                      .anyMatch(identity -> identity.getIntegrationId().equals(installationId)))
          .orElse(false);
    } catch (Exception e) {
      LOG.error(
          "Error checking existing partner identity mapping for user {}: {}",
          username,
          e.getMessage(),
          e);
      // Return false on error to allow normal flow to proceed
      return false;
    }
  }

  @Override
  public AppUser installVercelIntegration(
      HttpServletRequest requestHeader, AuditInfo auditInfo, String installationId)
      throws SvcException {
    incrementVercelNativeInstallationCounter();

    JWTClaimsSet claimsSet = vercelAuthSvc.parseVercelJwtFromRequest(requestHeader);
    final VercelUserToken vercelUserToken = VercelUserToken.from(claimsSet);

    try {
      validateUserisNotAlreadyLinkedToOtherPartnerIntegrationType(
          vercelUserToken.email(), installationId);

      AppUser user =
          getOrProvisionUser(
              auditInfo,
              vercelUserToken.email(),
              vercelUserToken.firstName(),
              vercelUserToken.lastName());

      createOrUpdatePartnerIdentityMappings(
          user.getUsername(), installationId, vercelUserToken.userId());

      return user;
    } catch (SvcException e) {
      LOG.error("Error during the installation of Vercel integration {}", installationId, e);

      incrementVercelNativeInstallationErrorCounter();

      if (e.getErrorCode().equals(AppUserErrorCode.DUPLICATE_USERNAME)) {
        throw new SvcException(
            VercelNativeIntegrationErrorCode
                .VERCEL_NATIVE_USER_PROVISIONING_FAILED_DUE_TO_EXISTING_OKTA_ACCOUNT,
            vercelUserToken.email());
      } else if (e.getErrorCode().equals(AppUserErrorCode.USER_DELETED)) {
        throw new SvcException(
            VercelNativeIntegrationErrorCode
                .VERCEL_NATIVE_USER_PROVISIONING_FAILED_DUE_TO_DELETION_PENDING,
            vercelUserToken.email());
      }

      throw new SvcException(CommonErrorCode.SERVER_ERROR, e);
    }
  }

  @Override
  public void uninstallVercelIntegration(String installationId, AuditInfo auditInfo)
      throws SvcException {
    incrementVercelNativeUninstallationCounter();

    try {
      if (vercelNativeConfig.isLoadTestingScenario()) {
        LOG.info(
            "Is load testing scenario, skipping sync with Vercel API for installation {}",
            installationId);
      } else {
        syncVercelRolesWithAtlasRoles(installationId, auditInfo, Optional.empty());
      }

      syncPartnerIdentityMappingsAndIntegrationsData(installationId);
    } catch (Exception e) {
      LOG.error("Error during the uninstallation of Vercel integration {}", installationId, e);

      incrementVercelNativeUninstallationErrorCounter();

      throw new SvcException(CommonErrorCode.SERVER_ERROR, e);
    }
  }

  @Override
  public void handleOidcCallback(String username, AuditInfo auditInfo) throws SvcException {
    incrementVercelNativeOidcCallbackCounter();

    Optional<PartnerIdentityMapping> mappings = partnerIdentityClient.getMappingByEmail(username);

    if (mappings.isEmpty()) {
      LOG.error("User {} is linked to Vercel Native but has no partner identities", username);
      incrementVercelNativeOidcCallbackErrorCounter();
      throw new UncheckedSvcException(
          CommonErrorCode.BAD_REQUEST,
          "No partner identities were found during OIDC callback for user {}" + username);
    }

    Set<String> installationsToSync =
        mappings.stream()
            .flatMap(mapping -> mapping.getIdentitiesList().stream())
            .map(PartnerIdentity::getIntegrationId)
            .collect(Collectors.toSet());

    Flux.fromIterable(installationsToSync)
        .flatMap(
            installationId ->
                getMono(
                    () -> {
                      syncVercelRolesWithAtlasRoles(
                          installationId, auditInfo, Optional.of(username));
                    }),
            getMaxDbConcurrency())
        .doOnError(
            e -> {
              LOG.error("Error during OIDC callback for user {}", username, e);
              incrementVercelNativeOidcCallbackErrorCounter();
            })
        .onErrorMap(e -> new UncheckedSvcException(CommonErrorCode.SERVER_ERROR, e))
        .blockLast();
  }

  @Override
  public void syncVercelRolesWithAtlasRoles(
      String installationId, AuditInfo auditInfo, Optional<String> username) {
    VercelNativeInstallation installation =
        installationsSvc.findExistingInstallation(installationId);

    if (installation == null) {
      throw new UncheckedSvcException(
          CommonErrorCode.BAD_REQUEST,
          "A VercelNativeInstallation was not found for installationId " + installationId);
    }

    AccessToken accessToken = createBearerAccessToken(installation.vercelData().accessToken());

    Optional<ObjectId> orgIdOpt = getOrgId(installationId);

    if (orgIdOpt.isEmpty()) {
      return;
    }

    getMono(
            () ->
                partnerIdentityClient.getMappingsByIntegration(installationId).stream()
                    .filter(
                        mapping -> username.isEmpty() || mapping.getEmail().equals(username.get()))
                    .collect(Collectors.toList()))
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            partnerIdentityMapping ->
                Flux.fromIterable(partnerIdentityMapping.getIdentitiesList())
                    .filter(identity -> identity.getIntegrationId().equals(installationId))
                    .flatMap(
                        identity ->
                            vercelMarketplaceApiClient
                                .getMemberInformation(
                                    installationId, identity.getExternalId(), accessToken)
                                .subscribeOn(SCHEDULER)
                                .onErrorResume(
                                    VercelApiRequestException.class,
                                    e -> {
                                      handleVercelApiUserNotFoundError(
                                          installationId,
                                          partnerIdentityMapping,
                                          identity,
                                          e,
                                          auditInfo);
                                      return Mono.empty();
                                    }),
                        getMaxApiConcurrency())
                    .flatMap(
                        response ->
                            getMono(
                                () ->
                                    assignRoleInAtlasOrg(
                                        response.role(),
                                        partnerIdentityMapping.getEmail(),
                                        orgIdOpt.get(),
                                        auditInfo)),
                        getMaxDbConcurrency()))
        .doOnError(
            e -> {
              LOG.error(
                  "Error during permission sync for Vercel integration {}", installationId, e);
            })
        .onErrorMap(e -> new UncheckedSvcException(CommonErrorCode.SERVER_ERROR, e))
        .blockLast();
  }

  @Override
  public AppUser upsertVercelApiUser(String token, PartnerIdentityType type, AuditInfo auditInfo)
      throws SvcException {
    final JWTClaimsSet claimsSet;
    try {
      claimsSet = JWTParser.parse(token).getJWTClaimsSet();
    } catch (ParseException e) {
      // If the JWT from Vercel is not decoding, we want to be notified so use error
      LOG.error("Invalid Partner JWT passed from authn", e);
      throw new SvcException(CommonErrorCode.BAD_REQUEST, "Invalid Partner JWT");
    }
    final VercelToken vercelToken =
        switch (type) {
          case VERCEL_NATIVE_HUMAN_USER -> VercelUserToken.from(claimsSet);
          case VERCEL_NATIVE_SYSTEM_USER -> VercelSystemToken.from(claimsSet);
          default ->
              throw new SvcException(CommonErrorCode.BAD_REQUEST, "Invalid PartnerIdentityType");
        };
    final String username = vercelToken.getApiUsername();
    final Optional<ObjectId> orgId = getOrgId(vercelToken.installationId());
    final Set<RoleAssignment> roleAssignments =
        orgId.map(vercelToken::getRoleAssignments).orElse(Set.of());
    return userSvc.upsertPartnerApiUser(username, roleAssignments, auditInfo);
  }

  @Override
  public Optional<AtlasResources> findLoginTargetResource(
      String installationId, @Nullable String resourceId) {
    return installationsSvc
        .findInstallation(installationId)
        .map(VercelNativeInstallation::atlasResources)
        .map(resources -> filterResourcesByResourceId(resources, resourceId));
  }

  /** Filters the installed products to only include the one matching the provided resourceId. */
  private AtlasResources filterResourcesByResourceId(
      AtlasResources resources, @Nullable String resourceId) {
    if (resourceId == null) {
      return new AtlasResources(resources.organizationId(), List.of());
    }
    ObjectId targetResourceId = new ObjectId(resourceId);
    var matchingProducts =
        resources.installedProducts().stream()
            .filter(product -> targetResourceId.equals(product.vercelResourceId()))
            .toList();

    return new AtlasResources(resources.organizationId(), matchingProducts);
  }

  public void confirmVercelLink(String email, String sessionId) throws SvcException {
    TentativePartnerAccountLink link =
        tentativePartnerAccountLinkSvc
            .getTentativeLinkBySessionId(sessionId)
            .filter(tentativeLink -> tentativeLink.email().equals(email))
            .orElseThrow(
                () ->
                    new SvcException(
                        CommonErrorCode.BAD_REQUEST,
                        "Invalid Tentative Partner Link Confirmation"));
    tentativePartnerAccountLinkSvc.confirmTentativeLink(link.sessionId());
  }

  @Override
  public void createPartnerIdentityMappingFromTentativeLink(
      TentativePartnerAccountLink tentativeLink) throws SvcException {
    try {
      AppUser userResult = setAppUserPartnerIntegrationDataIfMissing(tentativeLink.email());
      if (userResult == null) {
        throw new SvcException(
            CommonErrorCode.SERVER_ERROR, "Failed to add partner integration data to user");
      }
      LOG.debug("Successfully added partner integration data for user {}", tentativeLink.email());

      createOrUpdatePartnerIdentityMappings(
          tentativeLink.email(), tentativeLink.integrationId(), tentativeLink.externalId());
      LOG.debug(
          "Successfully created partner identity mapping for user {} with integration {} and"
              + " external ID {}",
          tentativeLink.email(),
          tentativeLink.integrationId(),
          tentativeLink.externalId());
    } catch (AuthnServiceException e) {
      LOG.error(
          "Error creating partner identity mapping for user {} with integration {} and external ID"
              + " {}",
          tentativeLink.email(),
          tentativeLink.integrationId(),
          tentativeLink.externalId(),
          e);
      throw new SvcException(CommonErrorCode.SERVER_ERROR, e);
    }
  }

  /** Gets the ID of the Atlas Organization associated with a Vercel Installation. */
  private Optional<ObjectId> getOrgId(String installationId) {
    return installationsSvc
        .findInstallation(installationId)
        .flatMap(
            installation ->
                Optional.ofNullable(installation.atlasResources())
                    .map(AtlasResources::organizationId));
  }

  /**
   * Checks if a user is linked to another partner integration type. If so, then throw an exception
   * and log an error.
   */
  private void validateUserisNotAlreadyLinkedToOtherPartnerIntegrationType(
      String username, String installationId) throws SvcException {
    if (isUserLinkedToOtherIntegrationType(username)) {
      LOG.error(
          String.format(
              "User %s tried to initiate installation for %s but is already linked to another"
                  + " partner integration type",
              username, installationId));
      throw new SvcException(
          CommonErrorCode.BAD_REQUEST, "User already linked to another partner integration type");
    }
  }

  /**
   * Checks if an AppUser already exists for this username. Otherwise, create a UserRegistrationForm
   * and call userSvcOkta to provision a new user through the integration user registration flow. If
   * the existing user does not already have the partnerIntegrationsData field set, then set it.
   */
  private AppUser getOrProvisionUser(
      AuditInfo auditInfo, String username, String firstName, String lastName) throws SvcException {

    AppUser appUserResult = setAppUserPartnerIntegrationDataIfMissing(username);
    if (appUserResult != null) {
      if (appUserResult.isDeleted()) {
        throw new SvcException(AppUserErrorCode.USER_DELETED, username);
      }
      return appUserResult;
    }

    UserRegistrationForm userRegistrationForm =
        createUserRegistrationForm(username, firstName, lastName);

    return userSvc.registerFromPartner(
        userRegistrationForm,
        auditInfo,
        PartnerIntegrationUserSource.VERCEL_NATIVE_PROGRAMMATIC_USER);
  }

  private AppUser setAppUserPartnerIntegrationDataIfMissing(String username) {
    return userSvc
        .findByUsernameOpt(username)
        .map(
            user -> {
              if (user.getPartnerIntegrationsData() == null) {
                userSvc.setPartnerIntegrationsData(
                    user.getId(),
                    new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false));
              }
              return user;
            })
        .orElse(null);
  }

  /** Creates the required form to register a new staged user through the UserSvc flow. */
  private UserRegistrationForm createUserRegistrationForm(
      String username, String firstName, String lastName) {
    UserRegistrationForm userRegistrationForm = new UserRegistrationForm();

    userRegistrationForm.setUsername(username);
    userRegistrationForm.setFirstName(firstName);
    userRegistrationForm.setLastName(lastName);
    userRegistrationForm.setPhoneNumber(NOT_PROVIDED);
    userRegistrationForm.setCountry(NOT_PROVIDED);
    userRegistrationForm.setJobResponsibility(NOT_PROVIDED);

    return userRegistrationForm;
  }

  /**
   * Creates a new PartnerIdentity using the installation ID and user ID from Vercel, then checks if
   * there's already an existing PartnerIdentityMapping for this username. If so, then append the
   * new PartnerIdentity to the identities list and update it. Otherwise, create a new mapping. If
   * the same PartnerIdentity already exists on the user (i.e from a downstream retry), then skip
   * this method.
   */
  private void createOrUpdatePartnerIdentityMappings(
      String username, String installationId, String vercelUserId) throws AuthnServiceException {
    PartnerIdentity identityToAdd =
        PartnerIdentity.newBuilder()
            .setIntegrationId(installationId)
            .setExternalId(vercelUserId)
            .build();

    Optional<PartnerIdentityMapping> partnerIdentityMappingOptional =
        partnerIdentityClient.getMappingByEmail(username);

    if (partnerIdentityMappingOptional.isPresent()
        && !partnerIdentityMappingOptional.get().getIdentitiesList().contains(identityToAdd)) {
      partnerIdentityClient.updateMapping(
          PartnerIdentityMapping.newBuilder()
              .setEmail(username)
              .addAllIdentities(
                  Stream.concat(
                          partnerIdentityMappingOptional.get().getIdentitiesList().stream(),
                          Stream.of(identityToAdd))
                      .toList())
              .build());
    } else if (partnerIdentityMappingOptional.isEmpty()) {
      partnerIdentityClient.createMapping(username, List.of(identityToAdd));
    }
  }

  /**
   * For each partner integration user associated with the installation, delete the linked partner
   * identity and clear their partner integrations data. To improve performance for installations
   * with a large number of users, this method uses a bounded elastic executor thread pool. The
   * maximum number of concurrent DB operations are controlled by the MAX_DB_CONCURRENCY constant.
   * If an operation fails, then this method will suspend execution of the remaining threads and
   * trigger a rollback for the affected users.
   */
  private void syncPartnerIdentityMappingsAndIntegrationsData(String installationId) {
    List<Pair<String, String>> deletedPartnerIdentities = new CopyOnWriteArrayList<>();
    List<String> modifiedUsers = new CopyOnWriteArrayList<>();

    getMono(() -> partnerIdentityClient.getMappingsByIntegration(installationId))
        .flatMapMany(Flux::fromIterable)
        .flatMap(
            partnerIdentityMapping ->
                Flux.fromIterable(partnerIdentityMapping.getIdentitiesList())
                    .filter(identity -> identity.getIntegrationId().equals(installationId))
                    .flatMap(
                        identity ->
                            getMono(
                                    () ->
                                        deletePartnerIdentity(
                                            installationId,
                                            identity.getExternalId(),
                                            partnerIdentityMapping.getEmail()))
                                .doOnSuccess(
                                    result ->
                                        deletedPartnerIdentities.add(
                                            Pair.of(
                                                identity.getExternalId(),
                                                partnerIdentityMapping.getEmail())))
                                .thenReturn(partnerIdentityMapping.getEmail())),
            getMaxDbConcurrency())
        .collectList()
        .flatMapMany(
            emails ->
                Flux.fromIterable(emails)
                    .concatMap(
                        email ->
                            getMono(() -> clearPartnerIntegrationsData(email))
                                .doOnSuccess(result -> modifiedUsers.add(email)),
                        getMaxDbConcurrency()))
        .doOnError(
            error ->
                rollbackPartnerIdentitySync(
                    installationId, deletedPartnerIdentities, modifiedUsers))
        .doOnError(
            e -> {
              LOG.error(
                  "Error during partner identity mappings sync when uninstalling Vercel integration"
                      + " {}",
                  kv("installationId", installationId),
                  e);
            })
        .onErrorMap(e -> new UncheckedSvcException(CommonErrorCode.SERVER_ERROR, e))
        .blockLast();
  }

  /**
   * When a rollback is triggered during a partner identity sync, then add back the partner identity
   * with the provided installationId for the deleted partner identities and set the
   * PartnerIntegrationsData field back to Vercel Native for the modified users.
   */
  private void rollbackPartnerIdentitySync(
      String installationId,
      List<Pair<String, String>> deletedPartnerIdentities,
      List<String> modifiedUsers) {
    incrementVercelNativeRollbackCounter();

    try {
      for (Pair<String, String> deletedPartnerIdentity : deletedPartnerIdentities) {
        createOrUpdatePartnerIdentityMappings(
            deletedPartnerIdentity.getRight(), installationId, deletedPartnerIdentity.getLeft());
      }

      for (String username : modifiedUsers) {
        AppUser appUser = userSvc.findByUsername(username);
        if (appUser.getPartnerIntegrationsData() == null) {
          userSvc.setPartnerIntegrationsData(
              appUser.getId(), new PartnerIntegrationsData(IntegrationType.VERCEL_NATIVE, false));
        }
      }
    } catch (Exception e) {
      LOG.error(
          "Error during partner identity sync rollback for Vercel integration {} on deleted partner"
              + " identities {} and modified users {}",
          installationId,
          deletedPartnerIdentities,
          modifiedUsers,
          e);

      incrementVercelNativeRollbackErrorCounter();

      throw new RuntimeException(e);
    }
  }

  /**
   * Finds the partner identity mapping associated with the user. If the mapping contains an
   * identity linked to the current Vercel integration, then call the partner identity client to
   * delete it.
   */
  private void deletePartnerIdentity(String installationId, String vercelUserId, String username) {
    try {
      PartnerIdentity identityToRemove =
          PartnerIdentity.newBuilder()
              .setIntegrationId(installationId)
              .setExternalId(vercelUserId)
              .build();

      Optional<PartnerIdentityMapping> partnerIdentityMappingOptional =
          partnerIdentityClient.getMappingByEmail(username);

      if (partnerIdentityMappingOptional.isPresent()
          && partnerIdentityMappingOptional.get().getIdentitiesList().contains(identityToRemove)) {
        partnerIdentityClient.deleteMappingByEmail(username, List.of(identityToRemove));
      }
    } catch (AuthnServiceException e) {
      LOG.error("Error when trying to delete partner identity for user {}", username, e);
      throw new UncheckedSvcException(e);
    }
  }

  /**
   * Finds the partner identity mapping associated with this user. If the mapping no longer has any
   * partner identities linked to it, then clear the user's partner integrations data.
   */
  private void clearPartnerIntegrationsData(String username) {
    try {
      Optional<PartnerIdentityMapping> partnerIdentityMappingOptional =
          partnerIdentityClient.getMappingByEmail(username);

      if (partnerIdentityMappingOptional.isPresent()
          && partnerIdentityMappingOptional.get().getIdentitiesList().isEmpty()) {
        AppUser user = userSvc.findByUsername(username);
        userSvc.clearPartnerIntegrationsData(user.getId());
      }
    } catch (Exception e) {
      LOG.error("Error when trying to clear partner integrations data for user {}", username, e);
      throw new RuntimeException(e);
    }
  }

  /**
   * Maps a VercelRole to its corresponding Atlas role and replaces the user's organization roles
   * with the result.
   */
  private void assignRoleInAtlasOrg(
      VercelRole role, String username, ObjectId orgId, AuditInfo auditInfo) {
    try {
      Set<Role> roles =
          switch (role) {
            case ADMIN -> Set.of(Role.ORG_OWNER);
            case USER -> Set.of(Role.ORG_READ_ONLY);
            default -> {
              throw new UncheckedSvcException(CommonErrorCode.BAD_REQUEST, "Invalid role {}", role);
            }
          };
      orgAccessSvc.replaceUserRolesInOrganization(
          userSvc.findByUsername(username), orgId, roles, auditInfo);
    } catch (SvcException e) {
      LOG.error("Error during role assignment for user {} in organization {}", username, orgId, e);
      throw new UncheckedSvcException(e);
    }
  }

  /**
   * If the user is not found by the Vercel API, then delete the associated partner identity mapping
   * for that integration, clear their partner integrations data, and remove them from the Atlas
   * organization. Continue with the rest of the threads if these operations succeed. Otherwise,
   * throw an exception to suspend the remaining threads. If the user is the last ORG_OWNER, then
   * they will be left with the role.
   */
  private void handleVercelApiUserNotFoundError(
      String installationId,
      PartnerIdentityMapping partnerIdentityMapping,
      PartnerIdentity identity,
      VercelApiRequestException vercelError,
      AuditInfo auditInfo) {
    if (vercelError.getStatusCode() != 404) {
      throw vercelError;
    }

    try {
      ObjectId orgId =
          getOrgId(installationId)
              .orElseThrow(
                  () ->
                      new SvcException(
                          CommonErrorCode.BAD_REQUEST,
                          "No organization found for Vercel integration " + installationId));

      partnerIdentityClient.deleteMappingByEmail(
          partnerIdentityMapping.getEmail(), List.of(identity));

      clearPartnerIntegrationsData(partnerIdentityMapping.getEmail());

      orgAccessSvc.removeUserFromOrganization(
          userSvc.findByUsername(partnerIdentityMapping.getEmail()), orgId, auditInfo);
    } catch (Exception e) {
      if (e.getMessage() != null
          && e.getMessage().equals(AppUserErrorCode.CANNOT_DELETE_LAST_ORG_OWNER.getMessage())) {
        return;
      }
      LOG.error(
          "Error when trying to handle Vercel API exception for user {}",
          partnerIdentityMapping.getEmail(),
          e);
      throw new RuntimeException(e);
    }
  }

  /**
   * Decrypts the encrypted access token using the configured Partner Integration Encryption Key and
   * creates an AccessToken, which can be used for calling the Vercel Marketplace API.
   */
  @VisibleForTesting
  protected AccessToken createBearerAccessToken(String encryptedAccessToken) {
    return new BearerAccessToken(
        VercelData.decryptAccessToken(
            encryptedAccessToken, vercelNativeConfig.getPartnerIntegrationEncryptionKey()));
  }

  /** Gets the maximum number of concurrent API operations. */
  @VisibleForTesting
  protected int getMaxApiConcurrency() {
    return MAX_API_CONCURRENCY;
  }

  /** Gets the maximum number of concurrent DB operations. */
  @VisibleForTesting
  protected int getMaxDbConcurrency() {
    return MAX_DB_CONCURRENCY;
  }

  /** Increments the VERCEL_NATIVE_INSTALLATION_COUNTER. */
  @VisibleForTesting
  protected void incrementVercelNativeInstallationCounter() {
    VERCEL_NATIVE_INSTALLATION_COUNTER.inc();
  }

  /** Increments the VERCEL_NATIVE_INSTALLATION_ERROR_COUNTER. */
  @VisibleForTesting
  protected void incrementVercelNativeInstallationErrorCounter() {
    VERCEL_NATIVE_INSTALLATION_ERROR_COUNTER.inc();
  }

  /** Increments the VERCEL_NATIVE_UNINSTALLATION_COUNTER. */
  @VisibleForTesting
  protected void incrementVercelNativeUninstallationCounter() {
    VERCEL_NATIVE_UNINSTALLATION_COUNTER.inc();
  }

  /** Increments the VERCEL_NATIVE_UNINSTALLATION_ERROR_COUNTER. */
  @VisibleForTesting
  protected void incrementVercelNativeUninstallationErrorCounter() {
    VERCEL_NATIVE_UNINSTALLATION_ERROR_COUNTER.inc();
  }

  /** Increments the VERCEL_NATIVE_OIDC_CALLBACK_COUNTER. */
  @VisibleForTesting
  protected void incrementVercelNativeOidcCallbackCounter() {
    VERCEL_NATIVE_OIDC_CALLBACK_COUNTER.inc();
  }

  /** Increments the VERCEL_NATIVE_OIDC_CALLBACK_ERROR_COUNTER. */
  @VisibleForTesting
  protected void incrementVercelNativeOidcCallbackErrorCounter() {
    VERCEL_NATIVE_OIDC_CALLBACK_ERROR_COUNTER.inc();
  }

  /** Increments the VERCEL_NATIVE_ROLLBACK_COUNTER. */
  @VisibleForTesting
  protected void incrementVercelNativeRollbackCounter() {
    VERCEL_NATIVE_ROLLBACK_COUNTER.inc();
  }

  /** Increments the VERCEL_NATIVE_ROLLBACK_ERROR_COUNTER. */
  @VisibleForTesting
  protected void incrementVercelNativeRollbackErrorCounter() {
    VERCEL_NATIVE_ROLLBACK_ERROR_COUNTER.inc();
  }
}
