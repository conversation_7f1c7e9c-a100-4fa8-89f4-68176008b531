/* (C) Copyright 2012, MongoDB, Inc. */

package com.xgen.cloud.user._public.svc;

import com.mongodb.ReadPreference;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.activity._public.model.event.Event;
import com.xgen.cloud.authz.shared._public.exceptions.AuthzServiceClientException;
import com.xgen.cloud.common.appsettings._public.model.ThemePreference;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.authz._public.view.PolicyAssignmentView;
import com.xgen.cloud.common.constants._public.model.user.LockAccountReason;
import com.xgen.cloud.common.constants._public.model.user.UserType;
import com.xgen.cloud.common.db.legacy._public.cursor.ModelCursor;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.model._public.time.TimeFormat;
import com.xgen.cloud.common.passwordreset._public.model.AuthType;
import com.xgen.cloud.partnerintegrations.common._public.model.PartnerIntegrationsData;
import com.xgen.cloud.partners.registration._public.model.MarketplaceRegistrationData;
import com.xgen.cloud.team._public.model.Team;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.PartnerIntegrationUserSource;
import com.xgen.cloud.user._public.model.ProjectLandingPage;
import com.xgen.cloud.user._public.model.UserEditForm;
import com.xgen.cloud.user._public.model.UserRegistrationForm;
import com.xgen.cloud.user._public.model.activity.ApiUserEvent;
import com.xgen.cloud.user._public.model.activity.UserEvent;
import com.xgen.cloud.user._public.view.BasicAppUserInfoView;
import com.xgen.module.account.res.view.PersonalInfoView;
import com.xgen.svc.mms.model.auth.SessionTimeoutInfo;
import com.xgen.svc.mms.model.auth.StrictestSessionTimeouts;
import com.xgen.svc.mms.model.auth.UiAuthMethod;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.bson.types.ObjectId;

public interface UserSvc {

  AppUser findById(ObjectId pUserId);

  List<AppUser> findByIds(Set<ObjectId> pUserIds);

  List<AppUser> findLocalOwnersByGroupId(ObjectId pGroupId);

  List<AppUser> findLocalUsersByGroupId(ObjectId pGroupId);

  ModelCursor<AppUser> findModelCursorForLocalUsersWithImplicitGroupAccess(
      ObjectId pOrgId, ObjectId pGroupId, Collection<ObjectId> pTeamIds);

  List<AppUser> findLocalOwnersByOrgId(ObjectId pOrgId);

  List<AppUser> findLocalOwnersByOrgId(ObjectId pOrgId, ReadPreference pReadPreference);

  List<AppUser> findLocalOwnersByOrgIds(List<ObjectId> pOrgIds);

  List<AppUser> findLocalOwnersByOrgIds(List<ObjectId> pOrgIds, ReadPreference pReadPreference);

  List<AppUser> findLocalUsersByTeamId(ObjectId pTeamId);

  List<AppUser> findLocalUsersByGroupIdAndTeamIds(ObjectId pGroupId, Set<ObjectId> pTeamIds);

  List<AppUser> findLocalUsersByGroupIdsAndTeamIds(
      List<ObjectId> groupIds, Collection<ObjectId> teamIds);

  List<AppUser> findApiUsersByGroupId(ObjectId pGroupId);

  List<AppUser> findApiUsersByOrgId(ObjectId pOrgId);

  List<AppUser> findActiveApiUsers(int pSkip, int pLimit);

  List<AppUser> findAllGlobalApiUsers();

  List<AppUser> findByPrimaryEmail(String pEmail);

  List<BasicAppUserInfoView> findLocalUserBasicInfoByGroupIds(Collection<ObjectId> groupIds);

  AppUser findOneWithSalesforceContactId(ObjectId pOrgId);

  Optional<AppUser> findByUsernameOpt(String pUsername);

  AppUser findByUsername(String pUsername);

  AppUser findByUsernameOrBackup(String pUsername);

  AppUser findByUsernameBackup(String pUsernameBackup);

  Optional<AppUser> findLocalUserInOrgById(ObjectId pUserId, ObjectId pOrgId);

  Optional<AppUser> findLocalUserInOrgByUsername(String pUsername, ObjectId pOrgId);

  Optional<AppUser> findLocalUserInGroupById(ObjectId pUserId, ObjectId pGroupId);

  List<AppUser> search(
      int pSkip, int pLimit, String pFilter, boolean pIncludeDeleted, Set<UserType> pUserTypes);

  List<AppUser> findAllLocalPaged(int pSkip, int pLimit, boolean pIncludeDeleted);

  List<AppUser> findByUsernamePaged(
      int pSkip, int pLimit, String pFilter, boolean pIncludeDeleted, Set<UserType> pTypes);

  AppUser findByUiAuthCode(String pUiAuthCode);

  AppUser findOneLocalUserWithEmployerByOrgIds(Collection<ObjectId> pOrgIds);

  List<AppUser> findByOrgId(ObjectId pOrgId);

  List<AppUser> findByDomainNames(Set<String> pDomainNames, int pLimit, ObjectId pAfterUserId);

  Optional<AppUser> findByThirdPartyId(String id);

  Optional<AppUser> findBySubject(String subject);

  List<AppUser> findAllUsersByType(Set<UserType> pTypes);

  long countApiUsersByOrgId(ObjectId pOrgId);

  AppUser upsertPartnerApiUser(
      String pUsername, Set<RoleAssignment> pRoleAssignments, AuditInfo pAuditInfo)
      throws SvcException;

  AppUser createApiUser(String pUsername, Set<RoleAssignment> pRoleAssignments, ObjectId pOrgId)
      throws SvcException;

  int removeApiUser(ObjectId pApiUserId);

  int hardDeleteApiUser(ObjectId pApiUserId);

  /* Note: pSecondsBeforeExpiration should be null if the session mode is not IDLE*/
  String addUiAuthCode(
      String pUsername,
      UiAuthMethod pUiAuthMethod,
      @Nullable String pCode,
      @Nullable String pRegion,
      @Nullable Integer pSecondsBeforeExpiration,
      @Nullable SessionTimeoutInfo pIdleSessionTimeoutInfo,
      @Nullable SessionTimeoutInfo pAbsoluteSessionTimeoutInfo,
      @Nullable String oktaSessionId);

  void updateAuthCodeExpiresAtDate(
      String pUiAuthCode, int pSecondsBeforeExpiration, Date pCurrentDate);

  /** Sign a user out. */
  void signOut(String pUiAuthCode) throws SvcException;

  /** Trigger the password reset. */
  void sendResetEmail(AuthType pResetType, String pUsername, AuditInfo pAuditInfo, boolean pAtlas)
      throws SvcException;

  /* Ensure the user exists in the MMS database with the given information. This does so by fetching
   * the user from the DB  by username, if they exist the user if updated with the passed in information
   * otherwise the user is created with the passed in information */
  AppUser ensureLocalUser(
      String pUsername,
      String pPassword,
      String pFirstName,
      String pLastName,
      List<ObjectId> pGroupIds,
      String pInvitationToken,
      Set<Role> pGlobalRoles,
      AuditInfo pAuditInfo,
      ObjectId userIdOverride)
      throws SvcException;

  /** Handle the reset password form. */
  void resetPassword(
      String pUsername,
      String pPassword,
      String pPasswordConfirm,
      String pTempId,
      AuditInfo pAuditInfo)
      throws SvcException;

  /** Handle the reset multi factor auth form. */
  void resetMultiFactorAuth(
      String pUsername, String pPassword, String pApiKey, String pTempId, AuditInfo pAuditInfo)
      throws SvcException;

  /** Handle the reset multi factor auth form for Atlas users */
  void resetMultiFactorAuthForAtlas(
      String pUsername,
      String pPassword,
      String pMongoUsername,
      String pMongoPassword,
      String pTempId,
      AuditInfo pAuditInfo)
      throws SvcException;

  /**
   * Generate user password reset token.
   *
   * @param isForced if this reset was forced by weak password detection
   */
  String generatePasswordResetToken(String pUsername, String pIpAddress, boolean isForced);

  void processInvitation(AppUser pUser, String pInvitationToken, AuditInfo pAuditInfo)
      throws SvcException;

  /** Remove a user from an organization. */
  void removeUserFromOrganization(AppUser pUser, ObjectId pOrgId, AuditInfo pAuditInfo)
      throws SvcException;

  /** Remove a user from a team */
  void removeUserFromTeam(AppUser pUser, Team pTeam, AuditInfo pAuditInfo) throws SvcException;

  /** Remove an invited user from a team */
  void removeTeamFromInvitation(String username, ObjectId orgId, Team pTeam, AuditInfo pAuditInfo)
      throws SvcException;

  /** Add a user. */
  AppUser addUserToGroup(
      String pUsername,
      ObjectId pGroupId,
      Collection<Role> pRoles,
      Collection<PolicyAssignmentView> pPolicyAssignmentViews,
      AuditInfo pAuditInfo)
      throws SvcException;

  AppUser addUserToGroup(
      String pUsername, ObjectId pGroupId, Collection<Role> pRoles, AuditInfo pAuditInfo)
      throws SvcException;

  AppUser addUserToGroup(
      AppUser pUser,
      ObjectId pGroupId,
      Collection<Role> pRoles,
      Collection<PolicyAssignmentView> pPolicyAssignmentViews,
      AuditInfo pAuditInfo)
      throws SvcException;

  AppUser addUserToGroup(
      AppUser pUser, ObjectId pGroupId, Collection<Role> pRoles, AuditInfo pAuditInfo)
      throws SvcException;

  /** Add a user to an organization. */
  AppUser addUserToOrganization(
      String pUsername, ObjectId pOrgId, Collection<Role> pRoles, AuditInfo pAuditInfo)
      throws SvcException;

  AppUser addUserToOrganization(
      AppUser pUser, ObjectId pOrgId, Collection<Role> pRoles, AuditInfo pAuditInfo)
      throws SvcException;

  Optional<Set<ObjectId>> getUserOrgAllowList(String pUsername);

  boolean canUserCreateOrgs(AppUser pUser);

  /** Add a user to a team. */
  void addUserToTeam(AppUser pUser, Team pTeam, AuditInfo pAuditInfo) throws SvcException;

  /** Add a user to a team without user group sync. */
  void addUserToTeamWithoutUserGroupSync(AppUser pUser, Team pTeam, AuditInfo pAuditInfo)
      throws SvcException;

  /** Update a user's role in an organization */
  AppUser updateUserRoleInOrganization(
      AppUser pUser,
      ObjectId pOrgId,
      Set<Role> pNewRoles,
      Set<Role> pRemovedRoles,
      boolean pAllowUpdatingChartsUser,
      AuditInfo pAuditInfo)
      throws SvcException;

  AppUser replaceUserRolesInOrganization(
      AppUser user, ObjectId orgId, Set<Role> roles, AuditInfo auditInfo) throws SvcException;

  AppUser addUserRoleInOrganization(
      AppUser appUser, ObjectId orgId, Role role, AuditInfo pAuditInfo) throws SvcException;

  AppUser addUserRoleInGroup(AppUser appUser, ObjectId groupId, Role role, AuditInfo auditInfo)
      throws SvcException;

  AppUser removeUserRoleInOrganization(
      AppUser appUser, ObjectId orgId, Role role, AuditInfo auditInfo) throws SvcException;

  AppUser removeUserRoleInGroup(AppUser appUser, ObjectId groupId, Role role, AuditInfo auditInfo)
      throws SvcException;

  AppUser replaceUserRolesInGroup(
      AppUser user, ObjectId groupId, Set<Role> roles, AuditInfo auditInfo) throws SvcException;

  /** Count the number of users with ORG_OWNER role for a given organization. */
  long countOrgOwners(ObjectId pOrgId);

  /** Hard delete user. User should be in soft delete state. */
  void hardDeleteUser(AppUser pUser, AuditInfo pAuditInfo) throws SvcException;

  /**
   * Mark a user as deleted (soft delete) and returns the resulting soft-deleted user
   *
   * @throws UnsupportedOperationException if this cannot be done by an implementation.
   */
  AppUser softDeleteUser(AppUser pUser, AuditInfo pAuditInfo) throws SvcException;

  /** Mark a deleted user as restored from soft delete. */
  void undeleteUser(ObjectId pUserId, AuditInfo pAuditInfo) throws SvcException;

  /** Mark a deleted user as restored from soft delete. */
  void undeleteUser(AppUser pAppUser, AuditInfo pAuditInfo) throws SvcException;

  void validateRegistrationForm(UserRegistrationForm pForm) throws SvcException;

  /** Register a new user. */
  AppUser register(UserRegistrationForm pForm, AuditInfo pAuditInfo) throws SvcException;

  /** Register a new user from a partner integration with specified user source */
  AppUser registerFromPartner(
      UserRegistrationForm pForm, AuditInfo pAuditInfo, PartnerIntegrationUserSource pUserSource)
      throws SvcException;

  void updateAppProfile(
      AppUser pAppUser,
      PersonalInfoView pPersonalInfo,
      AuditInfo pAuditInfo,
      HttpServletRequest pRequest)
      throws SvcException;

  void updatePhoneNumber(AppUser pAppUser, String pPhoneNumber) throws SvcException;

  List<AppUser> findLocalUsersEligibleForSalesforceSync(List<ObjectId> orgIds);

  List<AppUser> findRecentLocalUsersEligibleForSalesforceSync();

  void updateSalesforceContactId(
      String pUsername, String pSalesforceContactId, String pReason, AuditInfo pAuditInfo);

  void updateSalesforceContactId(ObjectId pUserId, String pSalesforceContactId);

  void updateSalesforceLeadId(ObjectId pUserId, String pSalesforceLeadId);

  void updateEmailAddress(AppUser pAppUser, String pEmailAddress, AuditInfo pAuditInfo)
      throws SvcException;

  void updateEmailAddress(String pUsername, String pEmailAddress, AuditInfo pAuditInfo)
      throws SvcException;

  void updateFirstNameAndLastName(
      AppUser pAppUser, String pFirstName, String pLastName, AuditInfo pAuditInfo)
      throws SvcException;

  void updateEmployer(ObjectId pUserId, String pEmployer);

  void updateLoginInfo(ObjectId pUserId, String pUserAddr, String pHostname);

  void resetRoleAssignments(AppUser pAppUser, Set<RoleAssignment> pRoles, AuditInfo pAuditInfo)
      throws AuthzServiceClientException;

  void resetRoleAssignments(ObjectId pUserId, Set<RoleAssignment> pRoles)
      throws AuthzServiceClientException;

  void updateRoleAssignments(AppUser pAppUser, Set<RoleAssignment> pRoles, AuditInfo pAuditInfo)
      throws SvcException;

  Set<RoleAssignment> getDirectAndTeamRoleAssignments(AppUser pAppUser);

  Event createGroupUserAudit(
      UserEvent.Type pEventType, AppUser pUser, ObjectId pGroupId, AuditInfo pAuditInfo);

  Event createGroupUserAudit(
      ApiUserEvent.Type pEventType, AppUser pUser, ObjectId pGroupId, AuditInfo pAuditInfo);

  Event createOrgUserAudit(
      UserEvent.Type pEventType, AppUser pUser, ObjectId pOrgId, AuditInfo pAuditInfo);

  void auditRolesChanged(AppUser pAppUser, Set<RoleAssignment> pRoles, AuditInfo pAuditInfo);

  void updateCurrentOrgId(AppUser pAppUser, ObjectId pActiveOrgId);

  void unsetCurrentOrgId(ObjectId pUserId);

  void updateCurrentGroupId(AppUser pAppUser, ObjectId pActiveGroupId);

  void unsetCurrentGroupId(ObjectId pUserId);

  void addOrgId(AppUser pUser, ObjectId pOrgId, Role pRole, AuditInfo pAuditInfo)
      throws AuthzServiceClientException;

  void addGroupId(AppUser pUser, ObjectId pGroupId, Role pRole, AuditInfo pAuditInfo)
      throws AuthzServiceClientException;

  void addGroupRoleAssignments(AppUser pUser, ObjectId pGroupId, Set<Role> pRoles)
      throws AuthzServiceClientException;

  void removeGroupRoleAssignments(String pUsername, ObjectId pGroupId, Set<Role> pRoles);

  /** Called by the login resource to authenticate user. */
  AppUser authenticate(
      String pUsername,
      String pPassword,
      String pInviteToken,
      boolean pValidReCaptchaResponse,
      AuditInfo pAuditInfo)
      throws SvcException;

  void verifyMultiFactorAuth(
      AppUser pUser,
      String pAuthCode,
      String pUiAuthCode,
      String pInviteToken,
      String pIdpId,
      AuditInfo pAuditInfo)
      throws SvcException;

  boolean checkPasswordIsValid(String pUsername, String pPassword) throws SvcException;

  void completeLogin(AppUser pUser, String pInviteToken, String pIdpId, AuditInfo pAuditInfo)
      throws SvcException;

  /** Called to sync user. An optional method. */
  void syncUser(ObjectId pUserId);

  void syncUser(ObjectId pUserId, AuditInfo auditInfo);

  void changeDisplayChartAnnotations(ObjectId pGroupId);

  AppUser editUserDetails(UserEditForm userEditForm, AuditInfo pAuditInfo) throws SvcException;

  void lockUser(AppUser user, AuditInfo auditInfo) throws SvcException;

  void unlockUser(AppUser user, AuditInfo auditInfo) throws SvcException;

  void clearLegacyAuth(AppUser user, AuditInfo auditInfo) throws SvcException;

  void clearAccountMfa(AppUser user, AuditInfo auditInfo) throws SvcException;

  /** Clear all user sessions for a given Okta user ID */
  void clearUserSessions(String oktaUserId) throws SvcException;

  AppUser editUserGlobalRole(String pUsername, Role role, boolean enableRole, AuditInfo pAuditInfo)
      throws SvcException;

  boolean isUserRegistrationEnabled();

  boolean isUserEditable();

  boolean isUserGlobalRoleEditable();

  /** Return a simple name for the actual implementation. */
  String getName();

  /** Lock a user account. This method automatically audit the action. */
  void lockAccount(AppUser pUser, LockAccountReason pReason, AuditInfo pAuditInfo)
      throws SvcException;

  void resetFailedLoginCount(ObjectId pUserId);

  int incrementFailedLoginCount(ObjectId pUserId);

  boolean usernameExists(String pUsername) throws SvcException;

  boolean isValidUsername(String pUsername);

  int countLocalUsers();

  long countLocalUsersByGroupId(ObjectId pGroupId);

  long countLocalUsersByGroupIdAndRole(ObjectId pGroupId, Role pRole);

  long countLocalUsersByGroupIdAndTeamIds(ObjectId pGroupId, Set<ObjectId> teamIds);

  long countLocalUsersByTeamId(ObjectId pTeamId);

  Map<ObjectId, Integer> countLocalUsersPerOrg(List<ObjectId> pOrganizationIds);

  long countLocalUsersByRoleAndOrgId(Role pRole, ObjectId pOrgId);

  void removeAllOrgUsers(ObjectId pOrgId, AuditInfo pAuditInfo) throws SvcException;

  void removeAllTeamUsers(ObjectId pTeamId, AuditInfo pAuditInfo) throws SvcException;

  void removeAllTeamUsersWithValidationAndSyncBypass(ObjectId pTeamId, AuditInfo pAuditInfo)
      throws SvcException;

  void removeGroupId(AppUser pUser, ObjectId pGroupId) throws AuthzServiceClientException;

  void checkPassword(AppUser pUser, String pPassword, String pUiAuthCode) throws SvcException;

  void validateUsernameDoesNotHaveRestrictedDomain(String pUsername) throws SvcException;

  void setLastAuthDate(ObjectId pUserId, Date pLastAuthDate);

  void updateLastAuthDate(ObjectId pUserId, String pUiAuthCode);

  void checkLastAuthMins(AppUser pUser, String pUiAuthCode, long pMaxLastAuthMins)
      throws SvcException;

  boolean hasOrgWithMultiFactorAuthRequired(AppUser pAppUser);

  boolean hasOrgWithPublicApiAllowListRequired(AppUser pAppUser);

  boolean userRequiresMultiFactorAuthSetup(AppSettings pSettings, AppUser pAppUser);

  /**
   * Check if a new set of organization roles can be applied to the user. We might not allow
   * removing the last {@link Role#ORG_OWNER} from an organization.
   */
  boolean canUpdateOrgRoles(AppUser pUser, ObjectId pOrgId, Set<Role> pNewRoles);

  void conversionPostProcess(ObjectId pUserId);

  boolean hasUsers();

  boolean userExistsInGroup(String pUsername, ObjectId pGroupId);

  boolean userExistsOrIsInvitedToGroup(String pUsername, ObjectId pGroupId);

  boolean userExistsInOrganization(String pUsername, ObjectId pOrgId);

  boolean userExistsOrIsInvitedToOrg(String pUsername, ObjectId pOrgId);

  List<AppUser> findByTeamId(ObjectId pTeamId);

  Set<RoleAssignment> reconcileOrgMembership(AppUser pUser, boolean pAllowUpdatingChartsUser)
      throws SvcException;

  String getPrimaryEmail(String pUsername);

  Set<String> getUsersNotInOrInvitedToOrg(List<String> pUsernames, ObjectId pOrgId);

  Set<String> getUsersNotInOrInvitedToGroup(List<String> pUsernames, ObjectId pGroupId);

  Set<String> getUniqueUsersInOrg(ObjectId pOrgId);

  List<AppUser> findAllLocalUsersByOrgId(ObjectId pOrgId);

  DBCursor findAllLocalIdThroughCursor();

  void removeAllUiAuthCodes(AppUser pAppUser) throws SvcException;

  void validatePersonalInformation(PersonalInfoView pPersonalInfo, String pEmailAddress)
      throws SvcException;

  void validateMobilePhoneNumber(String pPhoneNumber) throws SvcException;

  void validateEmailAddress(String pEmailAddress) throws SvcException;

  void updateUiAuthCodeMfaValidated(String attribute);

  boolean isFederatedUser(String pUsername) throws SvcException;

  Optional<String> getUserIdentityProviderId(String pUsername) throws SvcException;

  Optional<String> getUserIdentityProviderIdFromDb(String pUsername);

  /** This method will attempt to get an AppUser instance for an Okta username if available */
  Optional<AppUser> getAppUserByOktaUsername(String username, AuditInfo auditInfo)
      throws SvcException;

  boolean shouldUserDomainBypassFederation(String username);

  String generatePlaceholderEmployer(String pFirstName);

  Optional<String> getDomainFromEmailAddress(String pEmailAddress);

  void updateUsername(String pOldUsername, String pNewUsername, String pOktaId) throws SvcException;

  Optional<Set<ObjectId>> getUserViewableIds(AppUser pAppUser);

  boolean isMongoDBEmployee(AppUser pUser);

  boolean isMongoDBEmployee(String username);

  boolean isEmployeeFederationBypassUser(String username);

  void verifyEmail(String pOktaUserId) throws SvcException;

  boolean isEmailVerified(String pOktaUserId) throws SvcException;

  boolean hasVerifiedOwner(ObjectId pOrgId);

  void setMarketplaceRegistrationData(
      ObjectId pUserId, MarketplaceRegistrationData pMarketplaceRegistrationData);

  void unsetMarketplaceRegistrationData(ObjectId pUserId);

  void setLastPageView(ObjectId pUserId, Date pTime);

  void auditGroupRolesChanged(
      AppUser pAppUser, ObjectId groupId, List<Role> pRoles, AuditInfo pAuditInfo);

  void setGoToDisabled(ObjectId pUserId, boolean isDisabled);

  void setMfaSetupRequiredAtLogin(ObjectId pUserId, boolean mfaSetupRequired);

  void unsetMfaSetupRequiredAtLogin(ObjectId pUserId);

  void setFlaggedForPersonalizationWizard(
      ObjectId pUserId, boolean pFlaggedForPersonalizationWizard);

  void setDefaultTimeZoneId(ObjectId pUserId, String pTimeZoneId, String pTimeZoneDisp);

  void setDefaultChartGranularity(ObjectId pUserId, long pGranularity);

  void setDefaultChartZoom(ObjectId pUserId, long pZoom);

  void setDateFormatCode(ObjectId pUserId, String pDateFormatCode);

  void setTimeFormatCode(ObjectId userId, TimeFormat format);

  void setProjectLandingPage(ObjectId userId, ProjectLandingPage projectLandingPage);

  void setThemePreference(ObjectId pUserId, ThemePreference pThemePreference);

  void setOptedIntoCloudNav(ObjectId pUserId, boolean pShowCloudNav);

  void setIsOptedIntoDataExplorerGenAI(ObjectId pUserId, boolean pIsOptedIntoDataExplorerGenAI);

  void setSeparateOpcounterCharts(ObjectId pUserId, boolean pAck);

  void setChartRefreshRate(ObjectId pUserId, int pCode);

  Set<ObjectId> getOrgsWithInternalUsersByOrgIds(Collection<ObjectId> pOrgIds);

  Map<ObjectId, Date> lastLoggedInForOrgs(List<ObjectId> pOrganizationIds);

  void unsetLastDb(ObjectId pUserId);

  void setLastDb(ObjectId pUserId, String pDbName);

  void createNewUserTrackingRequestWhenUserHaveOneOrg(
      String pAnonymousId,
      AppUser pUser,
      ObjectId pOrgId,
      String pSignupSource,
      String pSignupMethod,
      String pContext,
      HttpServletRequest pRequest);

  void submitDeviceGrantSegmentEvent(AppUser user, HttpServletRequest request);

  void setShouldApplyOrgUiAccessListForApi(
      ObjectId pUserId, boolean pShouldApplyOrgUiAccessListForApi);

  boolean isUserMissingMfa(AppUser appUser);

  /*
  Methods to bypass cyclic dependencies for authz syncs
   */
  void createActorProxy(String id, Collection<RoleAssignment> roleAssignments) throws SvcException;

  void processUserAccessChangeProxy(
      AppUser appUser,
      Collection<RoleAssignment> roleAssignmentsToAdd,
      Collection<RoleAssignment> roleAssignmentsToRemove)
      throws AuthzServiceClientException;

  void processUserAccessChangeProxyWithUsername(
      AppUser appUser,
      String username,
      Collection<RoleAssignment> roleAssignmentsToAdd,
      Collection<RoleAssignment> roleAssignmentsToRemove)
      throws AuthzServiceClientException;

  void deleteActorProxy(String id) throws SvcException;

  boolean doesNameContainInvalidCharacters(String name);

  /**
   * Synchronize roleMappings for federated users
   *
   * @param pUser user to synchronize
   * @param pIdpId IDPId for the user
   * @param pAuditInfo audit methods
   */
  void syncFederatedUserRoleMappings(AppUser pUser, String pIdpId, AuditInfo pAuditInfo)
      throws SvcException;

  /**
   * Sets the partner integrations data for the user e.g. pending ANIS user
   *
   * @param userId the user's ID
   * @param partnerIntegrationsData the partner integrations data object to set
   */
  void setPartnerIntegrationsData(ObjectId userId, PartnerIntegrationsData partnerIntegrationsData);

  /**
   * Clears the partner integrations data field for the user.
   *
   * @param userId the user's ID
   */
  void clearPartnerIntegrationsData(ObjectId userId);

  /**
   * Retrieves the strictest session timeouts for the given user's current session.
   *
   * <p>This method extracts the UI auth code from the request, finds the corresponding auth code
   * object for the user, and returns the session timeout information if available.
   *
   * @param pRequest the HTTP servlet request containing the UI auth code
   * @param pAppUser the authenticated user
   * @return an Optional containing the StrictestSessionTimeouts if found, empty otherwise
   * @throws SvcException if no active session is found
   */
  Optional<StrictestSessionTimeouts> getStrictestSessionTimeouts(
      HttpServletRequest pRequest, AppUser pAppUser) throws SvcException;

  /**
   * This method is meant for cases where code needs to refer to the actual instance that reflects
   * the <code>mms.userSvcClass</code> property value, and designed to workaround the fact that
   * <code>UserModule</code> binds <code>UserSvcProxy</code> as the implementation for resolving
   * <code>UserSvc</code> injections. This is mainly useful if the calling code needs to cast <code>
   * UserSvc</code> as e.g. <code>UserSvcOkta</code> for calling implementation specific code.
   */
  UserSvc asRuntimeInstance();
}
