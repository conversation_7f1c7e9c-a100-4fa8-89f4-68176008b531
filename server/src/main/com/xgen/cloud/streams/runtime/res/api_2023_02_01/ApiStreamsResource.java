package com.xgen.cloud.streams.runtime.res.api_2023_02_01;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.OPERATION_ID_OVERRIDE;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.VERB_OVERRIDE;

import com.fasterxml.jackson.annotation.JsonView;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.apiusagedata._public.svc.ApiTelemetryAttributeSvc;
import com.xgen.cloud.apiusagedata._public.svc.ApiTelemetrySvc.CustomTelemetryFieldKey;
import com.xgen.cloud.billing._public.svc.exception.BillingErrorCode;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.util._public.util.ValidationUtils;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.common.view._public.base.ApiListView;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorConnectionsListedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorInstanceDescribedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorInstancesListedEvent;
import com.xgen.cloud.externalanalytics._public.svc.SegmentEventSvc;
import com.xgen.cloud.featureFlag._public.svc.FeatureFlagSvc;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.nds.common._public.model.error.NDSErrorCode;
import com.xgen.cloud.nds.datalake._public.model.ui.NDSDataLakeDataProcessRegionView;
import com.xgen.cloud.nds.streams._public.model.AWSKinesisDataStreamsConnection;
import com.xgen.cloud.nds.streams._public.model.KafkaConnection;
import com.xgen.cloud.nds.streams._public.model.StreamsConnection;
import com.xgen.cloud.nds.streams._public.model.StreamsConnectionType;
import com.xgen.cloud.nds.streams._public.model.StreamsPrivateLink;
import com.xgen.cloud.nds.streams._public.model.Tier;
import com.xgen.cloud.nds.streams._public.model.ui.StreamConfigView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsSampleConnectionView;
import com.xgen.cloud.nds.streams._public.model.ui.StreamsTenantView;
import com.xgen.cloud.nds.streams._public.model.ui.kafka.networking.Type;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.cloud.streams._public.model.view.ApiStreamsConnectionView;
import com.xgen.cloud.streams._public.model.view.ApiStreamsDataProcessRegionView;
import com.xgen.cloud.streams._public.model.view.ApiStreamsPrivateLinkView;
import com.xgen.cloud.streams._public.model.view.ApiStreamsTenantView;
import com.xgen.cloud.streams._public.model.view.ApiTierView;
import com.xgen.cloud.streams._public.model.view.vpc.ApiStreamsVPCPeeringChallengeView;
import com.xgen.cloud.streams._public.model.view.vpc.ApiStreamsVPCPeeringConnectionView;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import com.xgen.svc.nds.svc.StreamsSvc;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.StreamingOutput;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
@Path("/api/atlas/v2/groups/{groupId}/streams")
public class ApiStreamsResource extends ApiBaseResource {
  private static final Logger LOG = LoggerFactory.getLogger(ApiStreamsResource.class);
  private final StreamsSvc _streamsSvc;
  private final SegmentEventSvc _segmentEventSvc;
  private final ApiTelemetryAttributeSvc _apiTelemetryAttributeSvc;
  private final FeatureFlagSvc _featureFlagSvc;

  @Inject
  public ApiStreamsResource(
      AppSettings pSettings,
      StreamsSvc pStreamsSvc,
      SegmentEventSvc pSegmentSvc,
      ApiTelemetryAttributeSvc pApiTelemetryAttributeSvc,
      FeatureFlagSvc pFeatureFlagSvc) {

    super(pSettings);
    _streamsSvc = pStreamsSvc;
    _segmentEventSvc = pSegmentSvc;
    _apiTelemetryAttributeSvc = pApiTelemetryAttributeSvc;
    _featureFlagSvc = pFeatureFlagSvc;
  }

  public static Response createTenant(
      final StreamsSvc pStreamsSvc,
      final Group pGroup,
      final AuditInfo pAuditInfo,
      final Boolean pEnvelope,
      final ApiStreamsTenantView pView) {
    final String tenantName = pView.getName();

    if (!ValidationUtils.isValidTenantName(tenantName)) {
      return ApiErrorCode.VALIDATION_ERROR.response(pEnvelope, "Invalid tenant name");
    }

    final ApiStreamsDataProcessRegionView dataProcessRegion = pView.getDataProcessRegion();

    if (dataProcessRegion == null) {
      return ApiErrorCode.VALIDATION_ERROR.response(
          pEnvelope,
          String.format("Must provide a valid %s", ApiStreamsTenantView.DATA_PROCESS_REGION));
    }

    final StreamsTenantView tenantView =
        new StreamsTenantView(
            tenantName,
            false,
            dataProcessRegion.toNDSDataLakeDataProcessRegionView(),
            pView.getStreamConfig() == null
                ? new StreamConfigView(Tier.SP30)
                : pView.getStreamConfig().toStreamConfigView());

    boolean createSampleSolarConnection = false;
    if (pView.getSampleConnections() != null) {
      createSampleSolarConnection = pView.getSampleConnections().getSolarSampleConnection();
    }

    try {
      final StreamsTenantView created =
          pStreamsSvc.createTenant(tenantView, pGroup, pAuditInfo, createSampleSolarConnection);
      return new ApiResponseBuilder(pEnvelope)
          .ok()
          .content(new ApiStreamsTenantView(created))
          .build();
    } catch (SvcException e) {
      LOG.error(
          "Error creating stream instance named {} in project {}", tenantName, pGroup.getId(), e);
      if (e.getErrorCode() == NDSErrorCode.DATA_LAKE_UNSUPPORTED_CLOUD_PROVIDER) {
        throw ApiErrorCode.STREAM_UNSUPPORTED_CLOUD_PROVIDER.exception(
            pEnvelope, tenantView.getDataProcessRegion().getCloudProvider());
      }
      if (e.getErrorCode() == NDSErrorCode.STREAM_TENANT_INVALID_TIER) {
        ApiTierView disallowedTier;
        if (pView.getStreamConfig() != null && pView.getStreamConfig().getTier() != null) {
          disallowedTier = pView.getStreamConfig().getTier();
        } else {
          // We default to SP30 if not given a stream config, so this is unlikely to occur.
          disallowedTier = ApiTierView.SP10;
        }
        throw ApiErrorCode.STREAM_UNSUPPORTED_INSTANCE_TIER.exception(pEnvelope, disallowedTier);
      }

      if (e.getErrorCode() == NDSErrorCode.STREAM_WORKSPACE_FEATURE_FLAG_MISSING) {
        throw ApiErrorCode.STREAM_WORKSPACES_NOT_ENABLED.exception(
            pEnvelope, tenantView.getDataProcessRegion().getCloudProvider());
      }

      if (e.getErrorCode() == BillingErrorCode.PAYMENT_METHOD_MISSING) {
        throw ApiErrorCode.NO_PAYMENT_INFORMATION_FOUND.exception(pEnvelope, pGroup.getId());
      }

      throw handledSvcException(e, pGroup, pEnvelope, tenantName, null);
    }
  }

  private static WebApplicationException handledSvcException(
      SvcException e, Group pGroup, Boolean pEnvelope, String pTenantName, String pConnectionName) {
    if (e.getErrorCode() == NDSErrorCode.INVALID_ARGUMENT) {
      return ApiErrorCode.VALIDATION_ERROR.exception(pEnvelope, e.getMessage());
    }

    if (e.getErrorCode().equals(NDSErrorCode.STREAM_TENANT_NAME_ALREADY_EXISTS)) {
      return ApiErrorCode.STREAM_TENANT_NAME_ALREADY_EXISTS.exception(pEnvelope, pTenantName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_TENANT_NOT_FOUND_FOR_NAME)) {
      return ApiErrorCode.STREAM_TENANT_NOT_FOUND_FOR_NAME.exception(
          pEnvelope, pGroup.getId(), pTenantName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_TENANT_HAS_STREAM_PROCESSORS)) {
      return ApiErrorCode.STREAM_TENANT_HAS_STREAM_PROCESSORS.exception(pEnvelope, pTenantName);
    }

    if (e.getErrorCode().equals(NDSErrorCode.STREAM_CONNECTION_NAME_ALREADY_EXISTS)) {
      return ApiErrorCode.STREAM_CONNECTION_NAME_ALREADY_EXISTS.exception(
          pEnvelope, pConnectionName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_CONNECTION_NOT_FOUND)) {
      return ApiErrorCode.STREAM_CONNECTION_NOT_FOUND_FOR_NAME.exception(
          pEnvelope, pConnectionName, pGroup.getId(), pTenantName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_CONNECTION_TYPE_CANNOT_BE_MODIFIED)) {
      return ApiErrorCode.STREAM_CONNECTION_TYPE_CANNOT_BE_MODIFIED.exception(pEnvelope);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_NETWORKING_ACCESS_TYPE_CANNOT_BE_MODIFIED)) {
      return ApiErrorCode.STREAM_NETWORKING_ACCESS_TYPE_CANNOT_BE_MODIFIED.exception(
          pEnvelope, pTenantName, pConnectionName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_CONNECTION_HAS_STREAM_PROCESSORS)) {
      return ApiErrorCode.STREAM_CONNECTION_HAS_STREAM_PROCESSORS.exception(
          pEnvelope, pConnectionName, pTenantName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_KAFKA_CONNECTION_IS_DEPLOYING)) {
      return ApiErrorCode.STREAM_KAFKA_CONNECTION_IS_DEPLOYING.exception(
          pEnvelope, pConnectionName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.CLOUD_PROVIDER_ACCESS_ROLE_NOT_FOUND)) {
      return ApiErrorCode.STREAM_AWS_IAM_ROLE_NOT_FOUND.exception(pEnvelope, pGroup.getId());
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_AWS_IAM_ROLE_NOT_AUTHORIZED)) {
      return ApiErrorCode.STREAM_AWS_CONNECTION_NOT_AUTHORIZED.exception(
          pEnvelope, pConnectionName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_BAD_REQUEST_AWS_ERROR)) {
      return ApiErrorCode.STREAM_AWS_BAD_REQUEST.exception(pEnvelope, e.getMessage());
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_UPSTREAM_SERVICE_FAILURE_ERROR)) {
      return ApiErrorCode.STREAM_UPSTREAM_SERVICE_FAILURE.exception(pEnvelope);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_AWSLAMBDA_NOT_SUPPORTED_IN_CLOUD_PROVIDER)) {
      return ApiErrorCode.STREAM_AWSLAMBDA_NOT_SUPPORTED_IN_CLOUD_PROVIDER.exception(pEnvelope);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_VPC_PROXY_DEPLOYMENT_IS_NOT_SUPPORTED)) {
      return ApiErrorCode.STREAM_VPC_PROXY_DEPLOYMENT_IS_NOT_SUPPORTED.exception(pEnvelope);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_PRIVATE_LINK_IS_NOT_SUPPORTED)) {
      return ApiErrorCode.STREAM_PRIVATE_LINK_IS_NOT_SUPPORTED.exception(pEnvelope);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_PRIVATE_LINK_ALREADY_EXISTS)) {
      // For this error code, the service endpoint is propagated via pConnectionName
      return ApiErrorCode.STREAM_PRIVATE_LINK_ALREADY_EXISTS.exception(pEnvelope, pConnectionName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_PRIVATE_LINK_ALREADY_EXISTS_IN_REGION)) {
      // For this error code, the service endpoint is propagated via pConnectionName.
      // The service endpoint contains both region and vendor information.
      return ApiErrorCode.STREAM_PRIVATE_LINK_ALREADY_EXISTS.exception(pEnvelope, pConnectionName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_PRIVATE_LINK_IN_USE)) {
      return ApiErrorCode.STREAM_PRIVATE_LINK_IN_USE.exception(pEnvelope, pConnectionName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_PRIVATE_NETWORK_PROVIDER_MISMATCH)) {
      return ApiErrorCode.STREAM_PRIVATE_LINK_IN_USE.exception(pEnvelope, pConnectionName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_PRIVATE_LINK_NOT_FOUND)) {
      return ApiErrorCode.STREAM_CONNECTION_NOT_FOUND_FOR_ID.exception(pEnvelope, pGroup.getId());
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_PRIVATE_LINK_IS_NOT_READY)) {
      return ApiErrorCode.STREAM_PRIVATE_LINK_IS_NOT_READY.exception(pEnvelope, pConnectionName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_PRIVATE_LINK_VENDOR_INCOMPATIBLE)) {
      return ApiErrorCode.STREAM_CONNECTION_VENDOR_NOT_COMPATIBLE.exception(
          pEnvelope, pConnectionName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_TENANT_INVALID_BOOTSTRAP_SERVER)) {
      return ApiErrorCode.VALIDATION_ERROR.exception(pEnvelope, e.getMessage());
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_VPC_PROXY_DEPLOYMENT_IS_DELETING)) {
      return ApiErrorCode.STREAM_VPC_PROXY_DEPLOYMENT_IS_DELETING.exception(pEnvelope, pTenantName);
    }
    if (e.getErrorCode().equals(NDSErrorCode.CLUSTER_NOT_FOUND)) {
      return ApiErrorCode.VALIDATION_ERROR.exception(pEnvelope, e.getMessage());
    }
    if (e.getErrorCode().equals(NDSErrorCode.ROLE_NOT_PROVIDED)) {
      return ApiErrorCode.VALIDATION_ERROR.exception(pEnvelope, e.getMessage());
    }
    if (e.getErrorCode().equals(NDSErrorCode.CUSTOM_ROLE_NOT_FOUND)) {
      return ApiErrorCode.VALIDATION_ERROR.exception(pEnvelope, e.getMessage());
    }
    if (e.getErrorCode().equals(NDSErrorCode.UNSUPPORTED_ROLE)) {
      return ApiErrorCode.VALIDATION_ERROR.exception(pEnvelope, e.getMessage());
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_CLUSTER_PROJECT_ID_NOT_FOUND)) {
      return ApiErrorCode.VALIDATION_ERROR.exception(pEnvelope, e.getMessage());
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_CROSS_PROJECT_NOT_ENABLED)) {
      return ApiErrorCode.VALIDATION_ERROR.exception(pEnvelope, e.getMessage());
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_PROJECT_NOT_ALLOWED)) {
      return ApiErrorCode.VALIDATION_ERROR.exception(pEnvelope, e.getMessage());
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_USER_TYPE_NOT_SUPPORTED)) {
      return ApiErrorCode.VALIDATION_ERROR.exception(pEnvelope, e.getMessage());
    }
    if (e.getErrorCode().equals(NDSErrorCode.STREAM_INVALID_ORG_ID)) {
      return ApiErrorCode.VALIDATION_ERROR.exception(pEnvelope, e.getMessage());
    }

    return handleUnexpectedException(e, pGroup, pEnvelope, LOG);
  }

  @POST
  @Path("/privateLinkConnections")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-102-path-alternate-resource-name-path-param",
            value = "API predates IPA validation."),
      })
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
  })
  @Operation(
      summary = "Create One Private Link Connection",
      operationId = "createGroupStreamPrivateLinkConnection",
      description =
          "Creates one Private Link in the specified project. To use this resource, the requesting"
              + " Service Account or API Key must have the Project Owner or Project Stream"
              + " Processing Owner role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId")
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    schema = @Schema(implementation = ApiStreamsPrivateLinkView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "unauthorized"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description =
                  "Details to create one Private Link connection for a project." + " project.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2023_02_01_JSON,
                      schema = @Schema(implementation = ApiStreamsPrivateLinkView.class))),
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2024-10-02",
                  value =
                      "The MongoDB Atlas Streams Processing Private Link API is now exposed as part"
                          + " of private preview, but is subject to change until GA.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(
                  name = OPERATION_ID_OVERRIDE,
                  value = "createPrivateLinkConnection")
            }),
      })
  public Response createPrivateLinkConnection(
      @Context final Group pGroup,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiStreamsPrivateLinkView pView) {
    ObjectId correlationId = new ObjectId();
    try {
      LOG.atInfo()
          .setMessage(
              String.format(
                  "STREAMS: Creating private link for provider %s and vendor %s",
                  pView.getProvider(), pView.getVendor()))
          .addKeyValue("correlationId", correlationId)
          .addKeyValue("groupId", pGroup.getId())
          .log();
      var createdId = _streamsSvc.createPrivateLinkConnection(pView, pGroup, correlationId);
      LOG.atInfo()
          .setMessage(
              String.format(
                  "STREAMS: Created private link for provider %s and vendor %s",
                  pView.getProvider(), pView.getVendor()))
          .addKeyValue("correlationId", correlationId)
          .addKeyValue("groupId", pGroup.getId())
          .log();
      return new ApiResponseBuilder(pEnvelope).ok().content(pView.setId(createdId)).build();
    } catch (SvcException e) {
      LOG.atError()
          .setMessage(
              String.format(
                  "STREAMS: Error creating private link for provider %s and vendor %s",
                  pView.getProvider(), pView.getVendor()))
          .addKeyValue("correlationId", correlationId)
          .addKeyValue("groupId", pGroup.getId())
          .setCause(e)
          .log();
      throw handledSvcException(e, pGroup, pEnvelope, null, pView.getServiceEndpointId());
    }
  }

  @DELETE
  @Path("/privateLinkConnections/{connectionId}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
  })
  @Operation(
      summary = "Delete One Private Link Connection",
      operationId = "deleteGroupStreamPrivateLinkConnection",
      description =
          "Deletes one Private Link in the specified project. To use this resource, the requesting"
              + " Service Account or API Key must have the Project Owner or Project Stream"
              + " Processing Owner role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "connectionId",
            description = "Unique ID that identifies the Private Link connection.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "202",
            description = "Accepted",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          })
                    })),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2024-10-02",
                  value =
                      "The MongoDB Atlas Streams Processing Private Link API is now exposed as part"
                          + " of private preview, but is subject to change until GA.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(
                  name = OPERATION_ID_OVERRIDE,
                  value = "deletePrivateLinkConnection")
            }),
      })
  public Response deletePrivateLinkConnection(
      @Context final Group pGroup,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("connectionId") final String pConnectionId) {
    try {
      _streamsSvc.deletePrivateLinkConnection(pGroup.getId(), new ObjectId(pConnectionId));
      return new ApiResponseBuilder(pEnvelope).accepted().build();
    } catch (SvcException e) {
      LOG.error("Error deleting Private Link for project {}", pGroup.getId(), e);
      throw handledSvcException(e, pGroup, pEnvelope, null, pConnectionId);
    }
  }

  @GET
  @Path("/privateLinkConnections")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-102-path-alternate-resource-name-path-param",
            value = "API predates IPA validation."),
      })
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
    RoleSet.NAME.GROUP_DATA_ACCESS_ANY
  })
  @Operation(
      summary = "Return All Private Link Connections",
      operationId = "listGroupStreamPrivateLinkConnections",
      description =
          "Returns all Private Link connections for the specified project.To use this resource, the"
              + " requesting Service Account or API Key must have the Project Data Access roles,"
              + " Project Owner role or Project Stream Processing Owner role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "groupId"),
        @Parameter(ref = "envelope"),
        @Parameter(ref = "itemsPerPage"),
        @Parameter(ref = "pageNum"),
        @Parameter(ref = "pretty"),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = PaginatedApiStreamsPrivateLinkView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                        })
                  }),
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError"),
      },
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2024-10-02",
                  value =
                      "The MongoDB Atlas Streams Processing Private Link API is now exposed as part"
                          + " of private preview, but is subject to change until GA.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "listPrivateLinkConnections")
            }),
      })
  public Response getPrivateLinkConnections(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    try {

      final List<ApiStreamsPrivateLinkView> connections =
          _streamsSvc.getPrivateLinkConnections(pGroup.getId()).stream()
              .map(ApiStreamsPrivateLinkView::getView)
              .collect(Collectors.toList());

      return this.handlePagination(pRequest, connections, pEnvelope);
    } catch (SvcException e) {
      LOG.error("Error fetching Private Link connections in project {}", pGroup.getId(), e);
      throw handledSvcException(e, pGroup, pEnvelope, null, null);
    }
  }

  @GET
  @Path("/privateLinkConnections/{connectionId}")
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_READ_ONLY,
  })
  @Operation(
      summary = "Return One Private Link Connection",
      operationId = "getGroupStreamPrivateLinkConnection",
      description =
          "Returns the details of one Private Link connection within the project. To use this"
              + " resource, the requesting Service Account or API Key must have the Project Read"
              + " Only role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "connectionId",
            description = "Unique ID that identifies the Private Link connection.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = ApiStreamsPrivateLinkView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                        })
                  }),
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError"),
      },
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2024-10-02",
                  value =
                      "The MongoDB Atlas Streams Processing Private Link API is now exposed as part"
                          + " of private preview, but is subject to change until GA.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "getPrivateLinkConnection")
            }),
      })
  public Response getPrivateLinkConnection(
      @Context final Group pGroup,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("connectionId") final String pConnectionId) {
    try {
      if (!ObjectId.isValid(pConnectionId)) {
        throw ApiErrorCode.STREAM_CONNECTION_NOT_FOUND_FOR_ID.exception(
            pEnvelope, pGroup.getId(), pConnectionId);
      }
      ObjectId connectionId = new ObjectId(pConnectionId);
      final Optional<StreamsPrivateLink> connection =
          _streamsSvc.getPrivateLinkConnection(pGroup.getId(), connectionId);
      if (connection.isEmpty()) {
        throw ApiErrorCode.STREAM_CONNECTION_NOT_FOUND_FOR_ID.exception(
            pEnvelope, pGroup.getId(), pConnectionId);
      }
      return new ApiResponseBuilder(pEnvelope)
          .ok()
          .content(ApiStreamsPrivateLinkView.getView(connection.get()))
          .build();
    } catch (SvcException e) {
      LOG.error(
          "Error fetching Private Link connection with id {} in project {}",
          pConnectionId,
          pGroup.getId(),
          e);
      throw handledSvcException(e, pGroup, pEnvelope, null, pConnectionId);
    }
  }

  @GET
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_READ_ONLY,
  })
  @Operation(
      summary = "Return All Stream Instances in One Project",
      operationId = "listGroupStreamWorkspaces",
      description = "Returns all stream instances for the specified project.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "groupId"),
        @Parameter(ref = "envelope"),
        @Parameter(ref = "itemsPerPage"),
        @Parameter(ref = "pageNum"),
        @Parameter(ref = "pretty"),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = PaginatedApiStreamsTenantView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                        })
                  }),
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError"),
      },
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2023-09-11",
                  value =
                      "The MongoDB Atlas Streams Processing API is now exposed as part of private"
                          + " preview, but is subject to change until GA.")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "listWorkspaces"),
              @ExtensionProperty(name = "customMethod", value = "false", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "listStreamWorkspaces")
            }),
      })
  public Response getGroupTenants(
      @Context final AuditInfo pAuditInfo,
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    try {
      final List<ApiStreamsTenantView> tenants =
          _streamsSvc.findByGroupId(pGroup.getId()).stream()
              .map(ApiStreamsTenantView::new)
              .toList();

      _segmentEventSvc.submitEvent(
          StreamProcessorInstancesListedEvent.builder()
              .userId(pAuditInfo.getAppUserId())
              .groupId(pGroup.getId())
              .organizationId(pGroup.getOrgId())
              .build());

      return handlePagination(pRequest, tenants, pEnvelope);
    } catch (SvcException e) {
      LOG.error("Error fetching stream instances for project {}", pGroup.getId(), e);
      throw handleUnexpectedException(e, pGroup, pEnvelope, LOG);
    }
  }

  @GET
  @Path("/{tenantName}")
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
    RoleSet.NAME.GROUP_DATA_ACCESS_ANY
  })
  @Operation(
      summary = "Return One Stream Instance",
      operationId = "getGroupStreamWorkspace",
      description =
          "Returns the details of one stream instance within the specified project. To use this"
              + " resource, the requesting Service Account or API Key must have the Project Data"
              + " Access roles, Project Owner role or Project Stream Processing Owner role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "tenantName",
            description = "Human-readable label that identifies the stream instance to return.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(
            name = "includeConnections",
            description =
                "Flag to indicate whether connections information should be included in the stream"
                    + " instance.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "boolean")),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    schema = @Schema(implementation = ApiStreamsTenantView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          }),
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                                value = "API predates IPA validation."),
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2023-09-11",
                  value =
                      "The MongoDB Atlas Streams Processing API is now exposed as part of private"
                          + " preview, but is subject to change until GA.")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "getWorkspace"),
              @ExtensionProperty(name = "customMethod", value = "false", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "getStreamWorkspace")
            }),
      })
  public Response getTenant(
      @Context final HttpServletRequest pRequest,
      @Context final AuditInfo pAuditInfo,
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName,
      @Parameter(hidden = true) @QueryParam("includeConnections")
          final Boolean pIncludeConnections) {
    try {
      final boolean includeConn = pIncludeConnections != null && pIncludeConnections;
      StreamsTenantView tenant =
          includeConn
              ? _streamsSvc.findTenantWithConnections(
                  pGroup, pTenantName, true, pAuditInfo, pAppUser)
              : _streamsSvc.findTenantOnly(pGroup.getId(), pTenantName);
      final ApiStreamsTenantView view = new ApiStreamsTenantView(tenant);

      _segmentEventSvc.submitEvent(
          StreamProcessorInstanceDescribedEvent.builder()
              .userId(pAuditInfo.getAppUserId())
              .groupId(pGroup.getId())
              .organizationId(pGroup.getOrgId())
              .tenant(tenant.getTenantId())
              .build());

      _apiTelemetryAttributeSvc.putCustomAttribute(
          pRequest, CustomTelemetryFieldKey.TENANT_NAME, pTenantName);

      return new ApiResponseBuilder(pEnvelope).ok().content(view).build();
    } catch (SvcException e) {
      LOG.error(
          "Error fetching stream instance named {} in project {}", pTenantName, pGroup.getId(), e);
      throw handledSvcException(e, pGroup, pEnvelope, pTenantName, null);
    }
  }

  @POST
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
    RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN
  })
  @Operation(
      summary = "Create One Stream Instance",
      operationId = "createGroupStreamWorkspace",
      description =
          "Creates one stream instance in the specified project. To use this resource, the"
              + " requesting Service Account or API Key must have the Project Data Access Admin"
              + " role, Project Owner role or Project Stream Processing Owner role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    schema = @Schema(implementation = ApiStreamsTenantView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description = "Details to create one streams instance in the specified project.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2023_02_01_JSON,
                      schema = @Schema(implementation = ApiStreamsTenantView.class),
                      extensions = {
                        @Extension(
                            name = IPA_EXCEPTION,
                            properties = {
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-106-create-method-request-body-is-request-suffixed-object",
                                  value = "Content predates IPA validation."),
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-106-create-method-request-has-no-readonly-fields",
                                  value = "Content predates IPA validation.")
                            })
                      })),
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2023-09-11",
                  value =
                      "The MongoDB Atlas Streams Processing API is now exposed as part of private"
                          + " preview, but is subject to change until GA.")
            }),
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-106-create-method-response-code-is-201",
                  value = "API predates IPA validation.")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "createWorkspace"),
              @ExtensionProperty(name = "customMethod", value = "false", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "createStreamWorkspace")
            }),
      })
  public Response createTenant(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @JsonView(ApiStreamsTenantView.Views.BaseTenant.class) final ApiStreamsTenantView pView) {

    // TODO: this is required because /privateLinkConnections, /accountDetails,
    //  and /vpcPeeringConnections overlaps with /{tenantName}
    // See: CLOUDP-283605
    if (pView != null
        && List.of("accountDetails", "privateLinkConnections", "vpcPeeringConnections")
            .contains(pView.getName())) {

      return ApiErrorCode.VALIDATION_ERROR.response(
          pEnvelope, "Invalid tenant name: %s", pView.getName());
    }

    if (pView != null) {
      _apiTelemetryAttributeSvc.putCustomAttribute(
          pRequest, CustomTelemetryFieldKey.TENANT_NAME, pView.getName());
    } else {
      return new ApiResponseBuilder(pEnvelope).badRequest("request body cannot be empty").build();
    }

    return ApiStreamsResource.createTenant(_streamsSvc, pGroup, pAuditInfo, pEnvelope, pView);
  }

  @PATCH
  @Path("/{tenantName}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
    RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN
  })
  @Operation(
      summary = "Update One Stream Instance",
      operationId = "updateGroupStreamWorkspace",
      description =
          "Update one stream instance in the specified project. To use this resource, the"
              + " requesting Service Account or API Key must have the Project Data Access Admin"
              + " role, Project Owner role or Project Stream Processing Owner role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "tenantName",
            description = "Human-readable label that identifies the stream instance to update.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    schema = @Schema(implementation = ApiStreamsTenantView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description =
                  "Details of the new data process region to update in the streams instance.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2023_02_01_JSON,
                      schema = @Schema(implementation = ApiStreamsDataProcessRegionView.class),
                      extensions = {
                        @Extension(
                            name = IPA_EXCEPTION,
                            properties = {
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-107-update-method-request-has-no-readonly-fields",
                                  value = "Content predates IPA validation."),
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-107-update-method-request-body-is-get-method-response",
                                  value = "Content predates IPA validation."),
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-107-update-method-request-body-is-update-request-suffixed-object",
                                  value = "API predates IPA validation."),
                            })
                      })),
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2023-09-11",
                  value =
                      "The MongoDB Atlas Streams Processing API is now exposed as part of private"
                          + " preview, but is subject to change until GA.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "updateStreamWorkspace")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "updateWorkspace"),
              @ExtensionProperty(name = "customMethod", value = "false", parseValue = true)
            })
      })
  public Response updateTenant(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName,
      final ApiStreamsDataProcessRegionView pView) {
    NDSDataLakeDataProcessRegionView regionView = pView.toNDSDataLakeDataProcessRegionView();
    try {
      StreamsTenantView updated =
          _streamsSvc.updateTenant(pGroup, pTenantName, regionView, pAuditInfo);

      _apiTelemetryAttributeSvc.putCustomAttribute(
          pRequest, CustomTelemetryFieldKey.TENANT_NAME, pTenantName);

      return new ApiResponseBuilder(pEnvelope)
          .ok()
          .content(new ApiStreamsTenantView(updated))
          .build();
    } catch (SvcException e) {
      if (e.getErrorCode() == NDSErrorCode.DATA_LAKE_UNSUPPORTED_CLOUD_PROVIDER) {
        throw ApiErrorCode.STREAM_UNSUPPORTED_CLOUD_PROVIDER.exception(
            pEnvelope, regionView.getCloudProvider());
      }
      throw handledSvcException(e, pGroup, pEnvelope, pTenantName, null);
    }
  }

  @DELETE
  @Path("/{tenantName}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
    RoleSet.NAME.GROUP_DATA_ACCESS_ADMIN,
  })
  @Operation(
      summary = "Delete One Stream Instance",
      operationId = "deleteGroupStreamWorkspace",
      description =
          "Delete one stream instance in the specified project. To use this resource, the"
              + " requesting Service Account or API Key must have the Project Data Access Admin"
              + " role, Project Owner role or Project Stream Processing Owner role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "tenantName",
            description = "Human-readable label that identifies the stream instance to delete.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "202",
            description = "Accepted",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          })
                    })),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-108-delete-method-return-204-response",
                  value = "Long running operations do not need to return HTTP 204.")
            }),
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2023-09-11",
                  value =
                      "The MongoDB Atlas Streams Processing API is now exposed as part of private"
                          + " preview, but is subject to change until GA.")
            }),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "deleteWorkspace"),
              @ExtensionProperty(name = "customMethod", value = "false", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "deleteStreamWorkspace")
            }),
      })
  public Response deleteTenant(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName) {
    try {
      _streamsSvc.deleteTenant(pGroup, pTenantName, pAuditInfo, false);

      _apiTelemetryAttributeSvc.putCustomAttribute(
          pRequest, CustomTelemetryFieldKey.TENANT_NAME, pTenantName);

      return new ApiResponseBuilder(pEnvelope).accepted().build();
    } catch (SvcException e) {
      throw handledSvcException(e, pGroup, pEnvelope, pTenantName, null);
    }
  }

  @GET
  @Path("/{tenantName}/connections")
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
    RoleSet.NAME.GROUP_DATA_ACCESS_ANY
  })
  @Operation(
      summary = "Return All Connections of the Stream Instances",
      operationId = "listGroupStreamConnections",
      description =
          "Returns all connections of the stream instance for the specified project.To use this"
              + " resource, the requesting Service Account or API Key must have the Project Data"
              + " Access roles, Project Owner role or Project Stream Processing Owner role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "groupId"),
        @Parameter(ref = "envelope"),
        @Parameter(ref = "itemsPerPage"),
        @Parameter(ref = "pageNum"),
        @Parameter(ref = "pretty"),
        @Parameter(
            name = "tenantName",
            description = "Human-readable label that identifies the stream instance.",
            schema = @Schema(type = "string"),
            in = ParameterIn.PATH,
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = PaginatedApiStreamsConnectionView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                        })
                  }),
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError"),
      },
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2023-09-11",
                  value =
                      "The MongoDB Atlas Streams Processing API is now exposed as part of private"
                          + " preview, but is subject to change until GA.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "listStreamConnections")
            }),
      })
  public Response getConnections(
      @Context final HttpServletRequest pRequest,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope) {
    try {
      final StreamsTenantView tenant =
          _streamsSvc.findTenantWithConnections(pGroup, pTenantName, true, pAuditInfo, pAppUser);
      List<ApiStreamsConnectionView> connections =
          new ApiStreamsTenantView(tenant).getConnections();

      _segmentEventSvc.submitEvent(
          StreamProcessorConnectionsListedEvent.builder()
              .userId(pAuditInfo.getAppUserId())
              .groupId(pGroup.getId())
              .organizationId(pGroup.getOrgId())
              .tenant(tenant.getTenantId())
              .build());
      return this.handlePagination(pRequest, connections, pEnvelope);
    } catch (SvcException e) {
      LOG.error(
          "Error fetching connections for stream named {} in project {}",
          pTenantName,
          pGroup.getId(),
          e);
      throw handledSvcException(e, pGroup, pEnvelope, pTenantName, null);
    }
  }

  @GET
  @Path("/{tenantName}/connections/{connectionName}")
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_READ_ONLY,
  })
  @Operation(
      summary = "Return One Stream Connection",
      operationId = "getGroupStreamConnection",
      description =
          "Returns the details of one stream connection within the specified stream instance. To"
              + " use this resource, the requesting Service Account or API Key must have the"
              + " Project Read Only role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "tenantName",
            description = "Human-readable label that identifies the stream instance to return.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(
            name = "connectionName",
            description = "Human-readable label that identifies the stream connection to return.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  schema = @Schema(implementation = ApiStreamsConnectionView.class),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                        }),
                    @Extension(
                        name = IPA_EXCEPTION,
                        properties = {
                          @ExtensionProperty(
                              name = "xgen-IPA-104-get-method-returns-response-suffixed-object",
                              value = "API predates IPA validation."),
                        })
                  }),
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError"),
      },
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-108-delete-method-return-204-response",
                  value = "Schema predates IPA validation.")
            }),
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2023-09-11",
                  value =
                      "The MongoDB Atlas Streams Processing API is now exposed as part of private"
                          + " preview, but is subject to change until GA.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "getStreamConnection")
            }),
      })
  public Response getConnection(
      @Context final Group pGroup,
      @Context final AppUser pAppUser,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName,
      @Parameter(hidden = true) @PathParam("connectionName") final String pConnectionName) {
    try {
      final StreamsConnectionView connection =
          _streamsSvc.findConnection(pGroup, pTenantName, pConnectionName, pAppUser);
      final ApiStreamsConnectionView apiView = ApiStreamsConnectionView.fromConnection(connection);
      return new ApiResponseBuilder(pEnvelope).ok().content(apiView).build();
    } catch (SvcException e) {
      LOG.error(
          "Error fetching connections for stream named {} in project {}",
          pTenantName,
          pGroup.getId(),
          e);
      throw handledSvcException(e, pGroup, pEnvelope, pTenantName, pConnectionName);
    }
  }

  @POST
  @Path("/{tenantName}/connections")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
  })
  @Operation(
      summary = "Create One Stream Connection",
      operationId = "createGroupStreamConnection",
      description =
          "Creates one connection for a stream instance in the specified project. To use this"
              + " resource, the requesting Service Account or API Key must have the Project Owner"
              + " or Project Stream Processing Owner role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "tenantName",
            description = "Human-readable label that identifies the stream instance.",
            schema = @Schema(type = "string"),
            in = ParameterIn.PATH,
            required = true)
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    schema = @Schema(implementation = ApiStreamsConnectionView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "unauthorized"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description =
                  "Details to create one connection for a streams instance in the specified"
                      + " project.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2023_02_01_JSON,
                      schema = @Schema(implementation = ApiStreamsConnectionView.class),
                      extensions = {
                        @Extension(
                            name = IPA_EXCEPTION,
                            properties = {
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-106-create-method-request-body-is-request-suffixed-object",
                                  value = "Content predates IPA validation."),
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-106-create-method-request-has-no-readonly-fields",
                                  value = "Content predates IPA validation.")
                            })
                      })),
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2023-09-11",
                  value =
                      "The MongoDB Atlas Streams Processing API is now exposed as part of private"
                          + " preview, but is subject to change until GA.")
            }),
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-106-create-method-response-code-is-201",
                  value = "API predates IPA validation.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "createStreamConnection")
            }),
      })
  public Response createConnection(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      final ApiStreamsConnectionView pView) {

    StreamsConnection connection =
        pView.toConnection().toModel(new ObjectId(), new ObjectId(), new Date(), new Date());

    if ((pView.getType() == StreamsConnectionType.Sample
            && !StreamsSampleConnectionView.isSampleConnection(pView.getName()))
        || pView.getName() == null
        || pView.getName().isBlank()
        || !ValidationUtils.ASCII_PRINTABLE_CHARACTERS.matcher(pView.getName()).matches()) {
      return new ApiResponseBuilder(pEnvelope).badRequest("Invalid connection name").build();
    }

    if (pView.getType() == StreamsConnectionType.AWSKinesisDataStreams) {
      if (!_featureFlagSvc.isFeatureFlagEnabled(
          FeatureFlag.STREAMS_ENABLE_KINESIS_CONNECTION, null, pGroup)) {
        return new ApiResponseBuilder(pEnvelope)
            .notImplemented("AWSKinesisDataStreams connection is not enabled")
            .build();
      }
      AWSKinesisDataStreamsConnection kinesisConn = (AWSKinesisDataStreamsConnection) connection;
      if (kinesisConn.getNetworking() != null
          && kinesisConn.getNetworking().getAccess().getType() == Type.PRIVATE_LINK
          && !_featureFlagSvc.isFeatureFlagEnabled(
              FeatureFlag.STREAMS_AWS_KINESIS_PRIVATE_LINK, null, pGroup)) {
        return new ApiResponseBuilder(pEnvelope)
            .notImplemented("AWSKinesisDataStreams PrivateLink connection is not enabled")
            .build();
      }
    }

    if (pView.getType() == StreamsConnectionType.Kafka) {
      try {

        KafkaConnection kafkaConn = (KafkaConnection) connection;
        if (kafkaConn.getSecurity() == null) {
          return new ApiResponseBuilder(pEnvelope)
              .badRequest("Security field must be provided for Kafka connections.")
              .build();
        }
        if (kafkaConn.getNetworking().getAccess().getType() == Type.TRANSIT_GATEWAY
            && !_featureFlagSvc.isFeatureFlagEnabled(
                FeatureFlag.STREAMS_TRANSIT_GATEWAY_ENABLED, null, pGroup)) {
          return new ApiResponseBuilder(pEnvelope)
              .notImplemented("Invalid connection type")
              .build();
        }
      } catch (Exception e) {
        LOG.error(
            "Error converting Kafka connection for stream named {} in project {}",
            pView.getName(),
            pGroup.getId(),
            e);
        return new ApiResponseBuilder(pEnvelope).badRequest("Invalid connection request").build();
      }
    }

    try {
      var createdConnection =
          _streamsSvc.createConnection(
              pView.toConnection(), pTenantName, pGroup, pAuditInfo, pAppUser);
      return new ApiResponseBuilder(pEnvelope)
          .ok()
          .content(ApiStreamsConnectionView.fromConnection(createdConnection))
          .build();
    } catch (SvcException e) {
      LOG.error(
          "Error creating connection {} for stream named {} in project {}",
          pView.getName(),
          pTenantName,
          pGroup.getId(),
          e);
      throw handledSvcException(e, pGroup, pEnvelope, pTenantName, pView.getName());
    }
  }

  @PATCH
  @Path("/{tenantName}/connections/{connectionName}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
  })
  @Operation(
      summary = "Update One Stream Connection",
      operationId = "updateGroupStreamConnection",
      description =
          "Update one connection for the specified stream instance in the specified project. To use"
              + " this resource, the requesting Service Account or API Key must have the Project"
              + " Owner role or Project Stream Processing Owner role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "tenantName",
            description = "Human-readable label that identifies the stream instance.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(
            name = "connectionName",
            description = "Human-readable label that identifies the stream connection.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    schema = @Schema(implementation = ApiStreamsConnectionView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          })
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description =
                  "Details to update one connection for a streams instance in the specified"
                      + " project.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2023_02_01_JSON,
                      schema = @Schema(implementation = ApiStreamsConnectionView.class),
                      extensions = {
                        @Extension(
                            name = IPA_EXCEPTION,
                            properties = {
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-107-update-method-request-has-no-readonly-fields",
                                  value = "Content predates IPA validation."),
                              @ExtensionProperty(
                                  name =
                                      "xgen-IPA-107-update-method-request-body-is-update-request-suffixed-object",
                                  value = "API predates IPA validation.")
                            })
                      })),
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2023-09-11",
                  value =
                      "The MongoDB Atlas Streams Processing API is now exposed as part of private"
                          + " preview, but is subject to change until GA.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "updateStreamConnection")
            }),
      })
  public Response updateConnection(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName,
      @Parameter(hidden = true) @PathParam("connectionName") final String pConnectionName,
      final ApiStreamsConnectionView pView) {

    try {
      StreamsConnectionView updated =
          _streamsSvc.updateConnection(
              pView.toConnection(), pConnectionName, pTenantName, pGroup, pAuditInfo, pAppUser);
      return new ApiResponseBuilder(pEnvelope)
          .ok()
          .content(ApiStreamsConnectionView.fromConnection(updated))
          .build();
    } catch (SvcException e) {
      LOG.error(
          "Error updating connection {} for stream named {} in project {}",
          pConnectionName,
          pTenantName,
          pGroup.getId(),
          e);
      throw handledSvcException(e, pGroup, pEnvelope, pTenantName, pView.getName());
    }
  }

  @DELETE
  @Path("/{tenantName}/connections/{connectionName}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
  })
  @Operation(
      summary = "Delete One Stream Connection",
      operationId = "deleteGroupStreamConnection",
      description =
          "Delete one connection of the specified stream instance. To use this resource, the"
              + " requesting Service Account or API Key must have the Project Owner role or Project"
              + " Stream Processing Owner role.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "tenantName",
            description = "Human-readable label that identifies the stream instance.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(
            name = "connectionName",
            description = "Human-readable label that identifies the stream connection.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "202",
            description = "Accepted",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          })
                    })),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-108-delete-method-return-204-response",
                  value = "Long running operations do not need to return HTTP 204.")
            }),
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = "x-xgen-changelog",
            properties = {
              @ExtensionProperty(
                  name = "2023-09-11",
                  value =
                      "The MongoDB Atlas Streams Processing API is now exposed as part of private"
                          + " preview, but is subject to change until GA.")
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "deleteStreamConnection")
            }),
      })
  public Response deleteConnection(
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Context final AppUser pAppUser,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName,
      @Parameter(hidden = true) @PathParam("connectionName") final String pConnectionName) {
    try {
      _streamsSvc.deleteConnection(pConnectionName, pTenantName, pGroup, pAuditInfo, pAppUser);
      return new ApiResponseBuilder(pEnvelope).accepted().build();
    } catch (SvcException e) {
      LOG.error(
          "Error deleting connection {} for stream tenant named {} in project {}",
          pConnectionName,
          pTenantName,
          pGroup.getId(),
          e);
      throw handledSvcException(e, pGroup, pEnvelope, pTenantName, pConnectionName);
    }
  }

  @GET
  @Path("/vpcPeeringConnections")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-102-path-alternate-resource-name-path-param",
            value = "API predates IPA validation."),
      })
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_READ_ONLY,
  })
  @Operation(
      summary = "Return All VPC Peering Connections",
      operationId = "listGroupStreamVpcPeeringConnections",
      description = "Returns a list of incoming VPC Peering Connections.",
      tags = {"Streams"},
      parameters = {
        @Parameter(
            name = "requesterAccountId",
            description = "The Account ID of the VPC Peering connection/s.",
            in = ParameterIn.QUERY,
            schema = @Schema(type = "string"),
            required = true),
        @Parameter(ref = "groupId"),
        @Parameter(ref = "envelope"),
        @Parameter(ref = "itemsPerPage"),
        @Parameter(ref = "pageNum"),
        @Parameter(ref = "pretty"),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = VersionMediaType.V_2023_02_01_JSON,
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                        })
                  }),
            }),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError"),
      },
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "listVpcPeeringConnections")
            }),
      })
  public Response getVPCPeeringConnections(
      @Context final Group pGroup,
      @Context final HttpServletRequest pRequest,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @QueryParam("requesterAccountId")
          final String pRequesterAccountId) {

    try {
      if (StringUtils.isBlank(pRequesterAccountId)) {
        throw new SvcException(
            NDSErrorCode.INVALID_ARGUMENT, "The \"requesterAccountId\" parameter cannot be empty.");
      }

      List<ApiStreamsVPCPeeringConnectionView> connections =
          _streamsSvc.getVPCPeeringConnections(pGroup.getId()).stream()
              .filter(conn -> Objects.equals(conn.getRequesterAccountId(), pRequesterAccountId))
              .map(ApiStreamsVPCPeeringConnectionView::new)
              .toList();

      return handlePagination(pRequest, connections, pEnvelope);
    } catch (SvcException e) {
      throw handledSvcException(e, pGroup, pEnvelope, null, null);
    }
  }

  @DELETE
  @Path("/vpcPeeringConnections/{id}")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
  })
  @Operation(
      summary = "Delete One VPC Peering Connection",
      operationId = "deleteGroupStreamVpcPeeringConnection",
      description = "Deletes an incoming VPC Peering connection.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "id",
            description = "The VPC Peering Connection id.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "202",
            description = "Accepted",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          })
                    })),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "deleteVpcPeeringConnection")
            }),
      })
  public Response deleteVPCPeeringConnection(
      @Context final Group pGroup,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("id") final String pVPCConnectionId) {

    _streamsSvc.requestDeleteVPCPeeringConnection(pGroup.getId(), pVPCConnectionId);

    return new ApiResponseBuilder(pEnvelope).accepted().build();
  }

  @POST
  @Path("/vpcPeeringConnections/{id}:accept")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
  })
  @Operation(
      summary = "Accept One Incoming VPC Peering Connection",
      operationId = "acceptGroupStreamVpcPeeringConnection",
      description = "Requests the acceptance of an incoming VPC Peering connection.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "id",
            description = "The VPC Peering Connection id.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "202",
            description = "Accepted",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          })
                    })),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      requestBody =
          @RequestBody(
              required = true,
              description =
                  "Challenge values for VPC Peering requester account ID, and requester VPC ID.",
              content =
                  @Content(
                      mediaType = VersionMediaType.V_2023_02_01_JSON,
                      schema = @Schema(implementation = ApiStreamsVPCPeeringChallengeView.class))),
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "acceptVpcPeeringConnection")
            }),
      })
  public Response acceptVPCPeeringConnection(
      @Context final Group pGroup,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("id") final String pVPCConnectionId,
      final ApiStreamsVPCPeeringChallengeView pChallengeView) {

    _streamsSvc.requestAcceptVPCPeeringConnection(
        pGroup.getId(),
        pVPCConnectionId,
        pChallengeView.getRequesterVpcId(),
        pChallengeView.getRequesterAccountId());

    return new ApiResponseBuilder(pEnvelope).accepted().build();
  }

  @POST
  @Path("/vpcPeeringConnections/{id}:reject")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(VersionMediaType.V_2023_02_01_JSON)
  @RolesAllowed({
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN,
  })
  @Operation(
      summary = "Reject One Incoming VPC Peering Connection",
      operationId = "rejectGroupStreamVpcPeeringConnection",
      description = "Requests the rejection of an incoming VPC Peering connection.",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "id",
            description = "The VPC Peering Connection id.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "202",
            description = "Accepted",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2023_02_01_JSON,
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                          })
                    })),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "rejectVpcPeeringConnection")
            }),
      })
  public Response rejectVPCPeeringConnection(
      @Context final Group pGroup,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @PathParam("id") final String pVPCConnectionId) {

    _streamsSvc.requestRejectVPCPeeringConnection(pGroup.getId(), pVPCConnectionId);

    return new ApiResponseBuilder(pEnvelope).accepted().build();
  }

  @GET
  @Path("/{tenantName}/auditLogs")
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-113-singleton-should-have-update-method",
            value = "API predates IPA validation.")
      })
  @Produces("application/vnd.atlas.2023-02-01+gzip")
  @RolesAllowed({
    RoleSet.NAME.GROUP_DATA_ACCESS_ANY,
    RoleSet.NAME.GROUP_STREAM_PROCESSING_OWNER,
    RoleSet.NAME.ORG_STREAM_PROCESSING_ADMIN
  })
  @Operation(
      summary = "Download Audit Logs for One Atlas Stream Processing Instance",
      operationId = "downloadGroupStreamAuditLogs",
      description =
          "Downloads the audit logs for the specified Atlas Streams Processing instance. By"
              + " default, logs cover periods of 30 days. To use this resource, the requesting"
              + " Service Account or API Key must have the Project Data Access roles, Project Owner"
              + " role or Project Stream Processing Owner role. The API does not support direct"
              + " calls with the json response schema. You must request a gzip response schema"
              + " using an accept header of the format: \"Accept:"
              + " application/vnd.atlas.YYYY-MM-DD+gzip\".",
      tags = {"Streams"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "endDate",
            description =
                "Timestamp that specifies the end point for the range of log messages to download. "
                    + " MongoDB Cloud expresses this timestamp in the number of seconds that"
                    + " have elapsed since the UNIX epoch.",
            in = ParameterIn.QUERY,
            schema =
                @Schema(
                    type = "integer",
                    pattern = OpenApiConst.EPOCH_TIME_SECONDS_MIN,
                    example = "1636481348")),
        @Parameter(
            name = "startDate",
            description =
                "Timestamp that specifies the starting point for the range of log messages to"
                    + " download. MongoDB Cloud expresses this timestamp in the number of seconds"
                    + " that have elapsed since the UNIX epoch.",
            in = ParameterIn.QUERY,
            schema =
                @Schema(
                    type = "integer",
                    pattern = OpenApiConst.EPOCH_TIME_SECONDS_MIN,
                    example = "1636466948")),
        @Parameter(
            name = "tenantName",
            description = "Human-readable label that identifies the stream instance.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string"),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content = {
              @Content(
                  mediaType = "application/vnd.atlas.2023-02-01+gzip",
                  schema =
                      @Schema(
                          type = "string",
                          format = "binary",
                          description = "Compressed archive labeled `auditLogs.gz` downloads"),
                  extensions = {
                    @Extension(
                        properties = {
                          @ExtensionProperty(name = "x-xgen-version", value = "2023-02-01")
                        }),
                  })
            }),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "409", ref = "conflict"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "Atlas Streams")}),
        @Extension(
            name = VERB_OVERRIDE,
            properties = {
              @ExtensionProperty(name = "verb", value = "download"),
              @ExtensionProperty(name = "customMethod", value = "True", parseValue = true)
            }),
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "downloadAuditLogs")
            }),
      })
  public Response getAuditLogsForTenant(
      @Context final HttpServletResponse pResponse,
      @Context final Group pGroup,
      @Context final AuditInfo pAuditInfo,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean pEnvelope,
      @Parameter(hidden = true) @QueryParam("startDate") final Long pStartTimestamp,
      @Parameter(hidden = true) @QueryParam("endDate") final Long pEndTimestamp,
      @Parameter(hidden = true) @PathParam("tenantName") final String pTenantName) {

    // Default to last 30 days of logs
    final LocalDateTime now = LocalDateTime.now();
    final LocalDateTime startDate =
        pStartTimestamp == null
            ? now.minusDays(30)
            : LocalDateTime.ofEpochSecond(pStartTimestamp, 0, ZoneOffset.UTC);
    final LocalDateTime endDate =
        pEndTimestamp == null ? now : LocalDateTime.ofEpochSecond(pEndTimestamp, 0, ZoneOffset.UTC);

    if (startDate.isAfter(endDate)) {
      throw ApiErrorCode.START_DATE_AFTER_END_DATE.exception(pEnvelope);
    }

    try {
      StreamingOutput stream =
          _streamsSvc.getAuditLogsStream(
              pResponse,
              pAuditInfo,
              pGroup,
              pTenantName,
              startDate.toInstant(ZoneOffset.UTC),
              endDate.toInstant(ZoneOffset.UTC));
      return Response.ok(stream).build();
    } catch (SvcException e) {
      throw handledSvcException(e, pGroup, pEnvelope, pTenantName, null);
    }
  }

  @Schema(name = "PaginatedApiStreamsTenantView")
  public static final class PaginatedApiStreamsTenantView
      extends ApiListView<ApiStreamsTenantView> {}

  @Schema(name = "PaginatedApiStreamsConnectionView")
  public static final class PaginatedApiStreamsConnectionView
      extends ApiListView<ApiStreamsConnectionView> {}

  @Schema(name = "PaginatedApiStreamsPrivateLinkView")
  public static final class PaginatedApiStreamsPrivateLinkView
      extends ApiListView<ApiStreamsPrivateLinkView> {}
}
