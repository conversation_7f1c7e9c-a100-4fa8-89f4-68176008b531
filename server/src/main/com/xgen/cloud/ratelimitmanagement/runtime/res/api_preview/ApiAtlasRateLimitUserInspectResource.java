package com.xgen.cloud.ratelimitmanagement.runtime.res.api_preview;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;

import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.ratelimit.api._public.annotation.RateLimit;
import com.xgen.cloud.common.ratelimit.api._public.constants.RateLimitAnnotationConstants;
import com.xgen.cloud.common.ratelimit.api._public.filter.RateLimitContext;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.ratelimitmanagement.runtime.model.ApiAtlasRateLimitInspectionResponseView;
import com.xgen.cloud.ratelimitmanagement.runtime.svc.RateLimitInspectionSvc;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Atlas v2 API resource for inspecting rate limiting bucket states for the user scope. Provides
 * internal endpoints for rate limiting feature rollout visibility and debugging
 */
@Path("/api/atlas/v2/ratelimits")
@Extension(
    name = IPA_EXCEPTION,
    properties = {
      @ExtensionProperty(
          name = "xgen-IPA-104-resource-has-GET",
          value = "The API is in private preview. We will fix this before moving to stable."),
    })
@Singleton
public class ApiAtlasRateLimitUserInspectResource extends ApiBaseResource {

  private static final Logger LOG =
      LoggerFactory.getLogger(ApiAtlasRateLimitUserInspectResource.class);

  private final RateLimitInspectionSvc inspectionService;

  @Inject
  public ApiAtlasRateLimitUserInspectResource(
      AppSettings settings, RateLimitInspectionSvc inspectionService) {
    super(settings);
    this.inspectionService = inspectionService;
  }

  @GET
  @Produces(VersionMediaType.V_PREVIEW_JSON)
  @RateLimit(RateLimitAnnotationConstants.RATE_LIMITS_INSPECTION)
  @Operation(
      operationId = "listUserRatelimits",
      summary = "Return Rate Limit State for One User",
      description = "Retrieve rate limiting bucket state for the current user.",
      tags = {"Rate Limit"},
      parameters = {
        @Parameter(ref = "envelope"),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_PREVIEW_JSON,
                    schema =
                        @Schema(implementation = ApiAtlasRateLimitInspectionResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_PREVIEW_EXTENSION_TYPE)
                          }),
                      @Extension(
                          name = "x-xgen-preview",
                          properties = {
                            @ExtensionProperty(name = "public", value = "false"),
                            @ExtensionProperty(name = "name", value = "rate-limit"),
                          }),
                      @Extension(
                          name = IPA_EXCEPTION,
                          properties = {
                            @ExtensionProperty(
                                name = "xgen-IPA-110-collections-use-paginated-prefix",
                                value =
                                    "The API is in private preview. We will fix this before moving"
                                        + " to stable."),
                            @ExtensionProperty(
                                name = "xgen-IPA-110-collections-response-define-links-array",
                                value =
                                    "The API is in private preview. We will fix this before moving"
                                        + " to stable."),
                            @ExtensionProperty(
                                name = "xgen-IPA-110-collections-response-define-results-array",
                                value =
                                    "The API is in private preview. We will fix this before moving"
                                        + " to stable."),
                          }),
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "apix")}),
        @Extension(
            name = "x-xgen-hidden-env",
            properties = {@ExtensionProperty(name = "envs", value = "qa,stage,prod")}),
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-110-collections-request-has-itemsPerPage-query-param",
                  value =
                      "The API is in private preview. We will fix this before moving"
                          + " to stable."),
              @ExtensionProperty(
                  name = "xgen-IPA-105-valid-operation-id",
                  value =
                      "Op ID collision with unauth ratelimits. The API is in private preview. We"
                          + " will fix this before moving to stable."),
              @ExtensionProperty(
                  name = "xgen-IPA-110-collections-request-has-pageNum-query-param",
                  value =
                      "The API is in private preview. We will fix this before moving"
                          + " to stable."),
            }),
      })
  public Response getRateLimitState(
      @Context HttpServletResponse response,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelopee) {
    try {
      // For user scope, we don't have a specific ID to pass
      ApiAtlasRateLimitInspectionResponseView inspectionResponse =
          inspectionService.getRateLimitState(
              RateLimitContext.RateLimitScope.USER, null, null, response);
      return new ApiResponseBuilder(envelopee).ok().content(inspectionResponse).build();
    } catch (UncheckedSvcException e) {
      if (e.getErrorCode() == CommonErrorCode.NOT_FOUND) {
        return new ApiResponseBuilder(envelopee)
            .notImplemented("Rate limiting is disabled")
            .build();
      }
      LOG.error("Error getting user rate limit state", e);
      throw e;
    }
  }
}
