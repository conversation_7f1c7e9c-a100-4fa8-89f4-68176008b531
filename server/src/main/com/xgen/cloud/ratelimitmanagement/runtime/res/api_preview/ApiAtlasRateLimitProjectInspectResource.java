package com.xgen.cloud.ratelimitmanagement.runtime.res.api_preview;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.ratelimit.api._public.annotation.RateLimit;
import com.xgen.cloud.common.ratelimit.api._public.constants.RateLimitAnnotationConstants;
import com.xgen.cloud.common.ratelimit.api._public.filter.RateLimitContext;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.ratelimitmanagement.runtime.model.ApiAtlasRateLimitInspectionResponseView;
import com.xgen.cloud.ratelimitmanagement.runtime.svc.RateLimitInspectionSvc;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Atlas v2 API resource for inspecting rate limiting bucket states for the project scope. Provides
 * internal endpoints for rate limiting feature rollout visibility and debugging
 */
@Path("/api/atlas/v2/groups/{groupId}/ratelimits")
@Extension(
    name = IPA_EXCEPTION,
    properties = {
      @ExtensionProperty(
          name = "xgen-IPA-113-singleton-should-have-update-method",
          value = "The API is in private preview. We will fix this before moving to stable."),
    })
@Singleton
public class ApiAtlasRateLimitProjectInspectResource extends ApiBaseResource {

  private static final Logger LOG =
      LoggerFactory.getLogger(ApiAtlasRateLimitProjectInspectResource.class);

  private final RateLimitInspectionSvc inspectionService;

  @Inject
  public ApiAtlasRateLimitProjectInspectResource(
      AppSettings settings, RateLimitInspectionSvc inspectionService) {
    super(settings);
    this.inspectionService = inspectionService;
  }

  @GET
  @Produces(VersionMediaType.V_PREVIEW_JSON)
  @RolesAllowed({RoleSet.NAME.GROUP_OWNER, RoleSet.NAME.GROUP_READ_ONLY})
  @RateLimit(RateLimitAnnotationConstants.RATE_LIMITS_INSPECTION)
  @Operation(
      operationId = "getGroupRatelimits",
      summary = "Return Rate Limit State for One Group",
      description = "Retrieve rate limiting bucket state for the specified group.",
      tags = {"Rate Limit"},
      parameters = {
        @Parameter(ref = "groupId"),
        @Parameter(ref = "envelope"),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_PREVIEW_JSON,
                    schema =
                        @Schema(implementation = ApiAtlasRateLimitInspectionResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_PREVIEW_EXTENSION_TYPE)
                          }),
                      @Extension(
                          name = "x-xgen-preview",
                          properties = {
                            @ExtensionProperty(name = "public", value = "false"),
                            @ExtensionProperty(name = "name", value = "rate-limit"),
                          }),
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "apix")}),
        @Extension(
            name = "x-xgen-hidden-env",
            properties = {@ExtensionProperty(name = "envs", value = "qa,stage,prod")}),
      })
  public Response getGroupRateLimitState(
      @Context Group group,
      @Context Organization org,
      @Context HttpServletResponse response,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelopee) {

    try {
      ApiAtlasRateLimitInspectionResponseView inspectionResponse =
          inspectionService.getRateLimitState(
              RateLimitContext.RateLimitScope.GROUP, group.getId().toHexString(), org, response);
      return new ApiResponseBuilder(envelopee).ok().content(inspectionResponse).build();
    } catch (UncheckedSvcException e) {
      if (e.getErrorCode() == CommonErrorCode.NOT_FOUND) {
        return new ApiResponseBuilder(envelopee)
            .notImplemented("Rate limiting is disabled")
            .build();
      }
      LOG.error("Error getting group rate limit state for groupId: {}", group.getId(), e);
      throw e;
    }
  }
}
