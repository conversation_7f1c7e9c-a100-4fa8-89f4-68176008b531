package com.xgen.cloud.ratelimitmanagement.runtime.res.api_preview;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;

import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.model._public.error.CommonErrorCode;
import com.xgen.cloud.common.model._public.error.UncheckedSvcException;
import com.xgen.cloud.common.ratelimit.api._public.annotation.RateLimit;
import com.xgen.cloud.common.ratelimit.api._public.constants.RateLimitAnnotationConstants;
import com.xgen.cloud.common.ratelimit.api._public.filter.RateLimitContext;
import com.xgen.cloud.common.res._public.base.ApiBaseResource;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.ratelimitmanagement.runtime.model.ApiAtlasRateLimitInspectionResponseView;
import com.xgen.cloud.ratelimitmanagement.runtime.svc.RateLimitInspectionSvc;
import com.xgen.svc.mms.api.res.common.ApiResponseBuilder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Atlas v2 API resource for inspecting rate limiting bucket states for the org scope. Provides
 * internal endpoints for rate limiting feature rollout visibility and debugging
 */
@Path("/api/atlas/v2/orgs/{orgId}/ratelimits")
@Extension(
    name = IPA_EXCEPTION,
    properties = {
      @ExtensionProperty(
          name = "xgen-IPA-113-singleton-should-have-update-method",
          value = "The API is in private preview. We will fix this before moving to stable."),
    })
@Singleton
public class ApiAtlasRateLimitOrgInspectResource extends ApiBaseResource {

  private static final Logger LOG =
      LoggerFactory.getLogger(ApiAtlasRateLimitOrgInspectResource.class);

  private final RateLimitInspectionSvc inspectionService;

  @Inject
  public ApiAtlasRateLimitOrgInspectResource(
      AppSettings settings, RateLimitInspectionSvc inspectionService) {
    super(settings);
    this.inspectionService = inspectionService;
  }

  @GET
  @Produces(VersionMediaType.V_PREVIEW_JSON)
  @RolesAllowed({RoleSet.NAME.ORG_OWNER, RoleSet.NAME.ORG_READ_ONLY})
  @RateLimit(RateLimitAnnotationConstants.RATE_LIMITS_INSPECTION)
  @Operation(
      operationId = "getOrgRatelimits",
      summary = "Return Rate Limit State for One Organization",
      description = "Retrieve rate limiting bucket state for the specified organization.",
      tags = {"Rate Limit"},
      parameters = {
        @Parameter(ref = "orgId"),
        @Parameter(ref = "envelope"),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_PREVIEW_JSON,
                    schema =
                        @Schema(implementation = ApiAtlasRateLimitInspectionResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_PREVIEW_EXTENSION_TYPE)
                          }),
                      @Extension(
                          name = "x-xgen-preview",
                          properties = {
                            @ExtensionProperty(name = "public", value = "false"),
                            @ExtensionProperty(name = "name", value = "rate-limit"),
                          }),
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(properties = {@ExtensionProperty(name = "x-xgen-owner-team", value = "apix")}),
        @Extension(
            name = "x-xgen-hidden-env",
            properties = {@ExtensionProperty(name = "envs", value = "qa,stage,prod")}),
      })
  public Response getOrganizationRateLimitState(
      @Context Organization organization,
      @Context HttpServletResponse response,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelopee) {
    try {
      ApiAtlasRateLimitInspectionResponseView inspectionResponse =
          inspectionService.getRateLimitState(
              RateLimitContext.RateLimitScope.ORGANIZATION,
              organization.getId().toHexString(),
              organization,
              response);
      return new ApiResponseBuilder(envelopee).ok().content(inspectionResponse).build();
    } catch (UncheckedSvcException e) {
      if (e.getErrorCode() == CommonErrorCode.NOT_FOUND) {
        return new ApiResponseBuilder(envelopee)
            .notImplemented("Rate limiting is disabled")
            .build();
      }
      LOG.error(
          "Error getting organization rate limit state for orgId: {}", organization.getId(), e);
      throw e;
    }
  }
}
