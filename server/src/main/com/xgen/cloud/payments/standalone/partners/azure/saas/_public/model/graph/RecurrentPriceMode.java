package com.xgen.cloud.payments.standalone.partners.azure.saas._public.model.graph;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum RecurrentPriceMode {
  FLAT_RATE("flatRate"),
  PER_USER("perUser"),
  ;

  /** The actual serialized value for a {@link RecurrentPriceMode} instance. */
  private final String value;

  RecurrentPriceMode(String value) {
    this.value = value;
  }

  /**
   * Parses a serialized value to a {@link RecurrentPriceMode} instance.
   *
   * @param value the serialized value to parse.
   * @return the parsed {@link RecurrentPriceMode} object.
   * @throws IllegalArgumentException unexpected value provided for serialization.
   */
  @JsonCreator
  public static RecurrentPriceMode fromString(String value) {
    for (RecurrentPriceMode item : RecurrentPriceMode.values()) {
      if (item.getValue().equalsIgnoreCase(value) || item.toString().equalsIgnoreCase(value)) {
        return item;
      }
    }
    throw new IllegalArgumentException(String.format("Unexpected value '%s'", value));
  }

  @JsonValue
  public String getValue() {
    return value;
  }
}
