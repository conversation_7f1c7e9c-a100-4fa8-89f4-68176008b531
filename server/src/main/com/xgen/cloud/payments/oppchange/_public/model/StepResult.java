package com.xgen.cloud.payments.oppchange._public.model;

import static java.util.Objects.requireNonNullElse;

import jakarta.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * Represents the result of a step in a multistep process.
 *
 * <p>Use the builder to create instances of this record.
 */
public record StepResult(
    boolean succeeded, @NotNull List<String> errors, @NotNull Map<String, Object> context) {

  @SuppressWarnings({"DataFlowIssue"})
  public StepResult {
    errors = requireNonNullElse(errors, List.of());
    context = requireNonNullElse(context, Map.of());
  }

  public static Builder builder() {
    return new Builder();
  }

  /**
   * Creates a new Builder pre-populated with the current values of this StepResult.
   *
   * @return a new Builder instance with current values
   */
  public Builder toBuilder() {
    Builder builder = new Builder();
    if (succeeded) {
      builder.success();
    }
    if (!errors.isEmpty()) {
      builder.errors(errors);
    }
    if (!context.isEmpty()) {
      builder.context(context);
    }
    return builder;
  }

  @Override
  public String toString() {
    return new ToStringBuilder(this, ToStringStyle.JSON_STYLE)
        .append("succeeded", succeeded)
        .append("errors", errors)
        .append("context", context)
        .toString();
  }

  public static final class Builder {
    private boolean succeeded;
    private final List<String> errors = new ArrayList<>();
    private final Map<String, Object> context = new LinkedHashMap<>();

    private Builder() {}

    public Builder success() {
      this.succeeded = true;
      return this;
    }

    public Builder errors(String error) {
      this.errors.add(error);
      return this;
    }

    public Builder errors(List<String> errors) {
      this.errors.addAll(errors);
      return this;
    }

    public Builder context(Map<String, Object> context) {
      this.context.putAll(context);
      return this;
    }

    public StepResult build() {
      return new StepResult(succeeded && errors.isEmpty(), errors, context);
    }
  }
}
