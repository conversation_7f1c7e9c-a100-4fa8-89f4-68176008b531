package com.xgen.cloud.customermetrics._public.util.otel;

import static com.xgen.cloud.monitoring.metrics._public.model.RawRollupMetric.parseGroupId;

import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.monitoring.common._public.model.rrd.DataSourceType;
import com.xgen.cloud.monitoring.metrics._public.model.DiskMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.DiskPartitionMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.DiskPartitionsMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.ProcessCPU;
import com.xgen.cloud.monitoring.metrics._public.model.ProcessMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.RawMetric;
import com.xgen.cloud.monitoring.metrics._public.model.SystemCPU;
import com.xgen.cloud.monitoring.metrics._public.model.SystemMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.SystemMemory;
import com.xgen.cloud.monitoring.metrics._public.model.SystemMemoryMeasurement;
import com.xgen.cloud.monitoring.metrics._public.model.SystemNetwork;
import com.xgen.cloud.monitoring.metrics._public.model.SystemVM;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.KeyValue;
import io.opentelemetry.proto.metrics.v1.Gauge;
import io.opentelemetry.proto.metrics.v1.Metric;
import io.opentelemetry.proto.metrics.v1.MetricsData;
import io.opentelemetry.proto.metrics.v1.NumberDataPoint;
import io.opentelemetry.proto.metrics.v1.ResourceMetrics;
import io.opentelemetry.proto.metrics.v1.ScopeMetrics;
import io.opentelemetry.proto.metrics.v1.Sum;
import io.opentelemetry.proto.resource.v1.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MeasurementToOtelConverterUtils {

  private static final Logger LOG = LoggerFactory.getLogger(MeasurementToOtelConverterUtils.class);

  // Type names for groups of metrics
  private static final String HARDWARE_METRIC_TYPE_NAME = "hardware_";

  // Subtype names for different metric categories
  private static final String DISK_METRIC_SUBTYPE_NAME = "disk_metrics_";
  private static final String PLATFORM_SUBTYPE_NAME = "platform_";
  private static final String PROCESS_CPU_SUBTYPE_NAME = "process_cpu_";
  private static final String SYSTEM_CPU_SUBTYPE_NAME = "system_cpu_";
  private static final String SYSTEM_MEMORY_SUBTYPE_NAME = "system_memory_";
  private static final String SYSTEM_NETWORK_SUBTYPE_NAME = "system_network_";
  private static final String SYSTEM_VM_SUBTYPE_NAME = "system_vm_";

  public record MetricBuilderParameters<T>(
      String metricName,
      DataSourceType dataSourceType,
      Function<T, Number> getter,
      Optional<String> apiMeasurementName) {}

  public static final List<MetricBuilderParameters<DiskPartitionMeasurement>> DISK_PARAMETERS =
      List.of(
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "disk_percent_free",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getDiskPercentFree,
              Optional.of("DISK_PARTITION_SPACE_PERCENT_FREE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "disk_percent_used",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getDiskPercentUsed,
              Optional.of("DISK_PARTITION_SPACE_PERCENT_USED")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "disk_space_free_bytes",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getDiskSpaceFree,
              Optional.of("DISK_PARTITION_SPACE_FREE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "disk_space_used_bytes",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getDiskSpaceUsed,
              Optional.of("DISK_PARTITION_SPACE_USED")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + DISK_METRIC_SUBTYPE_NAME
                  + "max_aggregated_read_latency_milliseconds",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxAggregatedReadLatency,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + DISK_METRIC_SUBTYPE_NAME
                  + "max_aggregated_write_latency_milliseconds",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxAggregatedWriteLatency,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "max_disk_percent_free",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxDiskPercentFree,
              Optional.of("MAX_DISK_PARTITION_SPACE_PERCENT_FREE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "max_disk_percent_used",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxDiskPercentUsed,
              Optional.of("MAX_DISK_PARTITION_SPACE_PERCENT_USED")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "max_disk_queue_depth",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxDiskQueueDepth,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "max_disk_space_free_bytes",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxDiskSpaceFree,
              Optional.of("MAX_DISK_PARTITION_SPACE_FREE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "max_disk_space_used_bytes",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxDiskSpaceUsed,
              Optional.of("MAX_DISK_PARTITION_SPACE_USED")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "max_read_iops",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxReadIOPS,
              Optional.of("MAX_DISK_PARTITION_IOPS_READ")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + DISK_METRIC_SUBTYPE_NAME
                  + "max_read_latency_milliseconds",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxReadLatency,
              Optional.of("MAX_DISK_PARTITION_LATENCY_READ")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "max_total_iops",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxTotalIOPS,
              Optional.of("MAX_DISK_PARTITION_IOPS_TOTAL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "max_write_iops",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxWriteIOPS,
              Optional.of("MAX_DISK_PARTITION_IOPS_WRITE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + DISK_METRIC_SUBTYPE_NAME
                  + "max_write_latency_milliseconds",
              DataSourceType.GAUGE,
              DiskPartitionMeasurement::getMaxWriteLatency,
              Optional.of("MAX_DISK_PARTITION_LATENCY_WRITE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "physical_read_count_total",
              DataSourceType.COUNTER,
              DiskPartitionMeasurement::getPhysicalReadCount,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "physical_write_count_total",
              DataSourceType.COUNTER,
              DiskPartitionMeasurement::getPhysicalWriteCount,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "read_count_total",
              DataSourceType.COUNTER,
              DiskPartitionMeasurement::getDiskReadCount,
              Optional.of("DISK_PARTITION_IOPS_READ")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + DISK_METRIC_SUBTYPE_NAME
                  + "read_throughout_bytes_per_second",
              DataSourceType.COUNTER,
              DiskPartitionMeasurement::getDiskReadThroughput,
              Optional.of("DISK_PARTITION_THROUGHPUT_READ")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "read_time_milliseconds_total",
              DataSourceType.COUNTER,
              DiskPartitionMeasurement::getDiskReadTime,
              Optional.of("DISK_PARTITION_LATENCY_READ")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "sectors_read_total",
              DataSourceType.COUNTER,
              DiskPartitionMeasurement::getDiskSectorsRead,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "sectors_written_total",
              DataSourceType.COUNTER,
              DiskPartitionMeasurement::getDiskSectorsWritten,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + DISK_METRIC_SUBTYPE_NAME
                  + "weighted_time_io_milliseconds_total",
              DataSourceType.COUNTER,
              DiskPartitionMeasurement::getWeightedTimeIO,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + DISK_METRIC_SUBTYPE_NAME + "write_count_total",
              DataSourceType.COUNTER,
              DiskPartitionMeasurement::getDiskWriteCount,
              Optional.of("DISK_PARTITION_IOPS_WRITE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + DISK_METRIC_SUBTYPE_NAME
                  + "write_throughout_bytes_per_second",
              DataSourceType.COUNTER,
              DiskPartitionMeasurement::getDiskWriteThroughput,
              Optional.of("DISK_PARTITION_THROUGHPUT_WRITE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + DISK_METRIC_SUBTYPE_NAME
                  + "write_time_milliseconds_total",
              DataSourceType.COUNTER,
              DiskPartitionMeasurement::getDiskWriteTime,
              Optional.of("DISK_PARTITION_LATENCY_WRITE")));

  public static final List<MetricBuilderParameters<ProcessCPU>> PROCESS_CPU_PARAMETERS =
      List.of(
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + PROCESS_CPU_SUBTYPE_NAME
                  + "children_kernel_milliseconds_total",
              DataSourceType.COUNTER,
              ProcessCPU::getCpuChildrenKernel,
              Optional.of("PROCESS_CPU_CHILDREN_KERNEL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + PROCESS_CPU_SUBTYPE_NAME
                  + "children_user_milliseconds_total",
              DataSourceType.COUNTER,
              ProcessCPU::getCpuChildrenUser,
              Optional.of("PROCESS_CPU_CHILDREN_USER")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + PROCESS_CPU_SUBTYPE_NAME + "kernel_milliseconds_total",
              DataSourceType.COUNTER,
              ProcessCPU::getCpuKernel,
              Optional.of("PROCESS_CPU_KERNEL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + PROCESS_CPU_SUBTYPE_NAME
                  + "max_children_kernel_milliseconds",
              DataSourceType.GAUGE,
              ProcessCPU::getMaxCpuChildrenKernel,
              Optional.of("MAX_PROCESS_CPU_CHILDREN_KERNEL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + PROCESS_CPU_SUBTYPE_NAME
                  + "max_children_user_milliseconds",
              DataSourceType.GAUGE,
              ProcessCPU::getMaxCpuChildrenUser,
              Optional.of("MAX_PROCESS_CPU_CHILDREN_USER")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + PROCESS_CPU_SUBTYPE_NAME + "max_kernel_milliseconds",
              DataSourceType.GAUGE,
              ProcessCPU::getMaxCpuKernel,
              Optional.of("MAX_PROCESS_CPU_KERNEL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + PROCESS_CPU_SUBTYPE_NAME
                  + "max_normalized_children_kernel_milliseconds",
              DataSourceType.GAUGE,
              ProcessCPU::getMaxNormalizedCpuChildrenKernel,
              Optional.of("MAX_PROCESS_NORMALIZED_CPU_CHILDREN_KERNEL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + PROCESS_CPU_SUBTYPE_NAME
                  + "max_normalized_children_user_milliseconds",
              DataSourceType.GAUGE,
              ProcessCPU::getMaxNormalizedCpuChildrenUser,
              Optional.of("MAX_PROCESS_NORMALIZED_CPU_CHILDREN_USER")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + PROCESS_CPU_SUBTYPE_NAME
                  + "max_normalized_kernel_milliseconds",
              DataSourceType.GAUGE,
              ProcessCPU::getMaxNormalizedCpuKernel,
              Optional.of("MAX_PROCESS_NORMALIZED_CPU_KERNEL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + PROCESS_CPU_SUBTYPE_NAME
                  + "max_normalized_user_milliseconds",
              DataSourceType.GAUGE,
              ProcessCPU::getMaxNormalizedCpuUser,
              Optional.of("MAX_PROCESS_NORMALIZED_CPU_USER")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + PROCESS_CPU_SUBTYPE_NAME + "max_user_milliseconds",
              DataSourceType.GAUGE,
              ProcessCPU::getMaxCpuUser,
              Optional.of("MAX_PROCESS_CPU_USER")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + PROCESS_CPU_SUBTYPE_NAME + "user_milliseconds_total",
              DataSourceType.COUNTER,
              ProcessCPU::getCpuUser,
              Optional.of("PROCESS_CPU_USER")));

  public static final List<MetricBuilderParameters<SystemCPU>> SYSTEM_CPU_PARAMETERS =
      List.of(
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "guest_milliseconds_total",
              DataSourceType.COUNTER,
              SystemCPU::getCpuGuest,
              Optional.of("SYSTEM_CPU_GUEST")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "guest_nice_milliseconds_total",
              DataSourceType.COUNTER,
              SystemCPU::getCpuGuestNice,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "idle_milliseconds_total",
              DataSourceType.COUNTER,
              SystemCPU::getCpuIdle,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "io_wait_milliseconds_total",
              DataSourceType.COUNTER,
              SystemCPU::getCpuIowait,
              Optional.of("SYSTEM_CPU_IOWAIT")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "irq_milliseconds_total",
              DataSourceType.COUNTER,
              SystemCPU::getCpuIrq,
              Optional.of("SYSTEM_CPU_IRQ")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "kernel_milliseconds_total",
              DataSourceType.COUNTER,
              SystemCPU::getCpuKernel,
              Optional.of("SYSTEM_CPU_KERNEL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "max_guest_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxCpuGuest,
              Optional.of("MAX_SYSTEM_CPU_GUEST")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "max_guest_nice_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxCpuGuestNice,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "max_idle_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxCpuIdle,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "max_io_wait_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxCpuIowait,
              Optional.of("MAX_SYSTEM_CPU_IOWAIT")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "max_irq_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxCpuIrq,
              Optional.of("MAX_SYSTEM_CPU_IRQ")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "max_kernel_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxCpuKernel,
              Optional.of("MAX_SYSTEM_CPU_KERNEL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "max_nice_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxCpuNice,
              Optional.of("MAX_SYSTEM_CPU_NICE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "max_soft_irq_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxCpuSoftIrq,
              Optional.of("MAX_SYSTEM_CPU_SOFTIRQ")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "max_steal_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxCpuSteal,
              Optional.of("MAX_SYSTEM_CPU_STEAL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "max_user_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxCpuUser,
              Optional.of("MAX_SYSTEM_CPU_USER")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + SYSTEM_CPU_SUBTYPE_NAME
                  + "max_normalized_guest_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxNormalizedCpuGuest,
              Optional.of("MAX_SYSTEM_NORMALIZED_CPU_GUEST")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + SYSTEM_CPU_SUBTYPE_NAME
                  + "max_normalized_guest_nice_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxNormalizedCpuGuestNice,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + SYSTEM_CPU_SUBTYPE_NAME
                  + "max_normalized_idle_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxNormalizedCpuIdle,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + SYSTEM_CPU_SUBTYPE_NAME
                  + "max_normalized_io_wait_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxNormalizedCpuIowait,
              Optional.of("MAX_SYSTEM_NORMALIZED_CPU_IOWAIT")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + SYSTEM_CPU_SUBTYPE_NAME
                  + "max_normalized_irq_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxNormalizedCpuIrq,
              Optional.of("MAX_SYSTEM_NORMALIZED_CPU_IRQ")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + SYSTEM_CPU_SUBTYPE_NAME
                  + "max_normalized_kernel_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxNormalizedCpuKernel,
              Optional.of("MAX_SYSTEM_NORMALIZED_CPU_KERNEL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + SYSTEM_CPU_SUBTYPE_NAME
                  + "max_normalized_nice_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxNormalizedCpuNice,
              Optional.of("MAX_SYSTEM_NORMALIZED_CPU_NICE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + SYSTEM_CPU_SUBTYPE_NAME
                  + "max_normalized_soft_irq_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxNormalizedCpuSoftIrq,
              Optional.of("MAX_SYSTEM_NORMALIZED_CPU_SOFTIRQ")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + SYSTEM_CPU_SUBTYPE_NAME
                  + "max_normalized_steal_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxNormalizedCpuSteal,
              Optional.of("MAX_SYSTEM_NORMALIZED_CPU_STEAL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + SYSTEM_CPU_SUBTYPE_NAME
                  + "max_normalized_user_milliseconds",
              DataSourceType.GAUGE,
              SystemCPU::getMaxNormalizedCpuUser,
              Optional.of("MAX_SYSTEM_NORMALIZED_CPU_USER")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "nice_milliseconds_total",
              DataSourceType.COUNTER,
              SystemCPU::getCpuNice,
              Optional.of("SYSTEM_CPU_NICE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "soft_irq_milliseconds_total",
              DataSourceType.COUNTER,
              SystemCPU::getCpuSoftIrq,
              Optional.of("SYSTEM_CPU_SOFTIRQ")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "steal_milliseconds_total",
              DataSourceType.COUNTER,
              SystemCPU::getCpuSteal,
              Optional.of("SYSTEM_CPU_STEAL")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_CPU_SUBTYPE_NAME + "user_milliseconds_total",
              DataSourceType.COUNTER,
              SystemCPU::getCpuUser,
              Optional.of("SYSTEM_CPU_USER")));

  public static final List<MetricBuilderParameters<SystemMemory>> SYSTEM_MEMORY_PARAMETERS =
      List.of(
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "buffers_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getBuffers,
              Optional.of("SYSTEM_MEMORY_BUFFERS")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "cached_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getCached,
              Optional.of("SYSTEM_MEMORY_CACHED")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "cma_free_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getCmaFree,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "cma_total_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getCmaTotal,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "direct_map_1g_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getDirectMap1G,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "max_buffers_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMaxBuffers,
              Optional.of("MAX_SYSTEM_MEMORY_BUFFERS")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "max_cached_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMaxCached,
              Optional.of("MAX_SYSTEM_MEMORY_CACHED")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME
                  + SYSTEM_MEMORY_SUBTYPE_NAME
                  + "max_mem_available_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMaxMemAvailable,
              Optional.of("MAX_SYSTEM_MEMORY_AVAILABLE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "max_mem_free_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMaxMemFree,
              Optional.of("MAX_SYSTEM_MEMORY_FREE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "max_mem_used_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMaxMemUsed,
              Optional.of("MAX_SYSTEM_MEMORY_USED")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "max_shared_mem_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMaxMemShared,
              Optional.of("MAX_SYSTEM_MEMORY_SHARED")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "max_swap_free_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMaxSwapFree,
              Optional.of("MAX_SWAP_USAGE_FREE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "max_swap_used_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMaxSwapUsed,
              Optional.of("MAX_SWAP_USAGE_USED")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "mem_available_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMemAvailable,
              Optional.of("SYSTEM_MEMORY_AVAILABLE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "mem_free_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMemFree,
              Optional.of("SYSTEM_MEMORY_FREE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "mem_total_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMemTotal,
              Optional.empty()),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "shared_mem_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getMemShared,
              Optional.of("SYSTEM_MEMORY_SHARED")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "swap_free_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getSwapFree,
              Optional.of("SWAP_USAGE_FREE")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_MEMORY_SUBTYPE_NAME + "swap_total_kilobytes",
              DataSourceType.GAUGE,
              SystemMemory::getSwapTotal,
              Optional.empty()));

  public static final List<MetricBuilderParameters<SystemNetwork>> SYSTEM_NETWORK_PARAMETERS =
      List.of(
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_NETWORK_SUBTYPE_NAME + "bytes_in_bytes_total",
              DataSourceType.COUNTER,
              SystemNetwork::getNetworkBytesIn,
              Optional.of("SYSTEM_NETWORK_IN")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_NETWORK_SUBTYPE_NAME + "bytes_out_bytes_total",
              DataSourceType.COUNTER,
              SystemNetwork::getNetworkBytesOut,
              Optional.of("SYSTEM_NETWORK_OUT")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_NETWORK_SUBTYPE_NAME + "max_bytes_in_bytes",
              DataSourceType.GAUGE,
              SystemNetwork::getMaxNetworkBytesIn,
              Optional.of("MAX_SYSTEM_NETWORK_IN")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_NETWORK_SUBTYPE_NAME + "max_bytes_out_bytes",
              DataSourceType.GAUGE,
              SystemNetwork::getMaxNetworkBytesOut,
              Optional.of("MAX_SYSTEM_NETWORK_OUT")));

  public static final List<MetricBuilderParameters<SystemVM>> SYSTEM_VM_PARAMETERS =
      List.of(
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_VM_SUBTYPE_NAME + "max_page_swap_in",
              DataSourceType.GAUGE,
              SystemVM::getMaxVMSwapInRate,
              Optional.of("MAX_SWAP_IO_IN")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_VM_SUBTYPE_NAME + "max_page_swap_out",
              DataSourceType.GAUGE,
              SystemVM::getMaxVMSwapOutRate,
              Optional.of("MAX_SWAP_IO_OUT")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_VM_SUBTYPE_NAME + "page_swap_in_total",
              DataSourceType.COUNTER,
              SystemVM::getVMSwapIn,
              Optional.of("SWAP_IO_IN")),
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + SYSTEM_VM_SUBTYPE_NAME + "page_swap_out_total",
              DataSourceType.COUNTER,
              SystemVM::getVMSwapOut,
              Optional.of("SWAP_IO_OUT")));

  private static final List<MetricBuilderParameters<ProcessMeasurement>> PROCESS_PARAMETERS =
      List.of(
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + PLATFORM_SUBTYPE_NAME + "num_logical_cpus",
              DataSourceType.GAUGE,
              ProcessMeasurement::getNumLogicalCpus,
              Optional.empty()));

  private static final List<MetricBuilderParameters<SystemMeasurement>> SYSTEM_PARAMETERS =
      List.of(
          new MetricBuilderParameters<>(
              HARDWARE_METRIC_TYPE_NAME + PLATFORM_SUBTYPE_NAME + "num_logical_cpus",
              DataSourceType.GAUGE,
              SystemMeasurement::getNumLogicalCpus,
              Optional.empty()));

  /**
   * Convert a list of Measurements to OTel payloads (byte[]) in preparation to send them to MaaS
   *
   * @param measurements - the Measurements to convert. As of now, only DiskMeasurement and
   *     SystemMemoryMeasurement are supported RawMetric types.
   * @return the OTel payload (byte array) representation of the metrics
   */
  public static byte[] convertToOtel(final List<RawMetric> measurements) {
    // NOTE: the builder will take on the last RawMetric's resource attributes (hostId, groupId)
    final MetricsData.Builder metricsDataBuilder = MetricsData.newBuilder();
    for (final RawMetric rawMetric : measurements) {
      getResourceBuilderFromMeasurement(metricsDataBuilder.addResourceMetricsBuilder(), rawMetric);
    }
    return metricsDataBuilder.build().toByteArray();
  }

  @VisibleForTesting
  public static void getResourceBuilderFromMeasurement(
      final ResourceMetrics.Builder resourceMetricsBuilder, final RawMetric rawMetric) {
    resourceMetricsBuilder.setResource(getResource(rawMetric));
    final List<Metric> metrics = new ArrayList<>();
    final ScopeMetrics.Builder scopeMetricsBuilder =
        resourceMetricsBuilder.addScopeMetricsBuilder();

    if (rawMetric instanceof DiskMeasurement diskMeasurement) {
      final DiskPartitionsMeasurement diskPartitionsMeasurement =
          diskMeasurement.getDiskPartitions();
      for (final String partitionName : diskPartitionsMeasurement.getPartitionNames()) {
        final DiskPartitionMeasurement partition =
            diskPartitionsMeasurement.getPartitionMeasurement(partitionName);
        metrics.addAll(collectMetrics(partition, DISK_PARAMETERS));
      }
    } else if (rawMetric instanceof SystemMemoryMeasurement systemMemoryMeasurement) {
      metrics.addAll(
          collectMetrics(systemMemoryMeasurement.getSystemMemory(), SYSTEM_MEMORY_PARAMETERS));
    } else if (rawMetric instanceof ProcessMeasurement processMeasurement) {
      metrics.addAll(collectMetrics(processMeasurement.getProcessCPU(), PROCESS_CPU_PARAMETERS));
      metrics.addAll(collectMetrics(processMeasurement.getSystemCPU(), SYSTEM_CPU_PARAMETERS));
      metrics.addAll(
          collectMetrics(processMeasurement.getSystemNetwork(), SYSTEM_NETWORK_PARAMETERS));
      metrics.addAll(collectMetrics(processMeasurement.getSystemVM(), SYSTEM_VM_PARAMETERS));
      metrics.addAll(collectMetrics(processMeasurement, PROCESS_PARAMETERS));
    } else if (rawMetric instanceof SystemMeasurement systemMeasurement) {
      metrics.addAll(collectMetrics(systemMeasurement.getSystemCPU(), SYSTEM_CPU_PARAMETERS));
      metrics.addAll(
          collectMetrics(systemMeasurement.getSystemNetwork(), SYSTEM_NETWORK_PARAMETERS));
      metrics.addAll(collectMetrics(systemMeasurement.getSystemVM(), SYSTEM_VM_PARAMETERS));
      metrics.addAll(collectMetrics(systemMeasurement, SYSTEM_PARAMETERS));
    } else {
      LOG.error("Unsupported measurement type: {}", rawMetric.getClass().getName());
    }
    scopeMetricsBuilder.addAllMetrics(metrics);
  }

  private static <T extends RawMetric> List<Metric> collectMetrics(
      final T measurement, final List<MetricBuilderParameters<T>> parameters) {
    final List<Metric> metrics = new ArrayList<>();
    for (MetricBuilderParameters<T> param : parameters) {
      getMetricBuilder(param.metricName(), param.dataSourceType(), measurement, param.getter())
          .ifPresent(metrics::add);
    }
    return metrics;
  }

  private static <T extends RawMetric> Optional<Metric> getMetricBuilder(
      final String metricName,
      final DataSourceType dataSourceType,
      final T measurement,
      final Function<T, Number> getter) {
    final Number metricValue = getter.apply(measurement);
    if (metricValue == null) {
      return Optional.empty();
    }

    return switch (dataSourceType) {
      case COUNTER ->
          Optional.of(
              Metric.newBuilder()
                  .setName(metricName)
                  .setSum(
                      Sum.newBuilder()
                          .addDataPoints(
                              getDataPointBuilder(measurement, metricValue.doubleValue()))
                          .build())
                  .build());
      case GAUGE ->
          Optional.of(
              Metric.newBuilder()
                  .setName(metricName)
                  .setGauge(
                      Gauge.newBuilder()
                          .addDataPoints(
                              getDataPointBuilder(measurement, metricValue.doubleValue()).build()))
                  .build());
      default -> Optional.empty();
    };
  }

  private static NumberDataPoint.Builder getDataPointBuilder(
      final RawMetric rawMetric, final double pValue) {
    final long sampleTimeNanos = TimeUnit.MILLISECONDS.toNanos(rawMetric.getSampleTime().getTime());
    final NumberDataPoint.Builder dataPointBuilder =
        NumberDataPoint.newBuilder().setTimeUnixNano(sampleTimeNanos).setAsDouble(pValue);
    if (rawMetric instanceof DiskPartitionMeasurement diskPartitionMeasurement) {
      dataPointBuilder.addAttributes(
          0,
          KeyValue.newBuilder()
              .setKey("partition")
              .setValue(
                  AnyValue.newBuilder()
                      .setStringValue(diskPartitionMeasurement.getPartitionName())));
    }
    return dataPointBuilder;
  }

  private static Resource getResource(final RawMetric rawMetric) {
    final Optional<String> idOpt = getId(rawMetric);
    final Optional<String> hostIdOpt = getHostId(rawMetric);

    final List<KeyValue> attributes = new ArrayList<>();
    if (idOpt.isPresent()) {
      final String groupId = parseGroupId(idOpt.get()).toString();
      attributes.add(
          KeyValue.newBuilder()
              .setKey("groupId")
              .setValue(AnyValue.newBuilder().setStringValue(groupId).build())
              .build());
    } else {
      LOG.debug(
          "Missing id for rawMetric: {}, hostId: {}",
          rawMetric.getClass().getSimpleName(),
          idOpt.orElse("missing"));
    }
    if (hostIdOpt.isPresent()) {
      attributes.add(
          KeyValue.newBuilder()
              .setKey("hostId")
              .setValue(AnyValue.newBuilder().setStringValue(hostIdOpt.get()).build())
              .build());
    } else {
      LOG.debug(
          "Missing hostId for rawMetric: {}, id: {}",
          rawMetric.getClass().getSimpleName(),
          hostIdOpt.orElse("missing"));
    }

    // If both are missing, return an empty Resource
    if (attributes.isEmpty()) {
      LOG.error(
          "Missing groupId and hostId for rawMetric: {}", rawMetric.getClass().getSimpleName());
      return Resource.getDefaultInstance();
    }

    return Resource.newBuilder().addAllAttributes(attributes).build();
  }

  private static Optional<String> getId(final RawMetric rawMetric) {
    if (rawMetric instanceof DiskMeasurement diskMeasurement) {
      return Optional.of(diskMeasurement.getId());
    } else if (rawMetric instanceof SystemMemoryMeasurement systemMemoryMeasurement) {
      return Optional.of(systemMemoryMeasurement.getId());
    } else if (rawMetric instanceof ProcessMeasurement processMeasurement) {
      return Optional.of(processMeasurement.getId());
    } else if (rawMetric instanceof SystemMeasurement systemMeasurement) {
      return Optional.of(systemMeasurement.getId());
    }
    LOG.error("Unsupported measurement type: {}", rawMetric.getClass().getSimpleName());
    return Optional.empty();
  }

  private static Optional<String> getHostId(final RawMetric rawMetric) {
    if (rawMetric instanceof DiskMeasurement diskMeasurement) {
      return Optional.of(diskMeasurement.getHostId());
    } else if (rawMetric instanceof SystemMemoryMeasurement systemMemoryMeasurement) {
      return Optional.of(systemMemoryMeasurement.getHostname());
    } else if (rawMetric instanceof ProcessMeasurement processMeasurement) {
      return Optional.of(processMeasurement.getHostId());
    } else if (rawMetric instanceof SystemMeasurement systemMeasurement) {
      return Optional.of(systemMeasurement.getHostname());
    }
    LOG.error("Unsupported measurement type: {}", rawMetric.getClass().getSimpleName());
    return Optional.empty();
  }
}
