package com.xgen.cloud.authz_tools._public.tools;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.authz_tools._private.model.EndpointInfo;
import com.xgen.cloud.common.access._public.annotation.UiCall;
import com.xgen.cloud.common.constants._public.model.resources.ResourceTypeConstants;
import com.xgen.cloud.openrewrite._public.runner.RewriteRunner;
import com.xgen.cloud.openrewrite._public.runner.RewriteRunner.Timer;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotEmpty;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.Set;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.openrewrite.ExecutionContext;
import org.openrewrite.TreeVisitor;
import org.openrewrite.java.JavaIsoVisitor;
import org.openrewrite.java.JavaTemplate;
import org.openrewrite.java.tree.Expression;
import org.openrewrite.java.tree.J;
import org.openrewrite.java.tree.J.Annotation;
import org.openrewrite.java.tree.J.ClassDeclaration;
import org.openrewrite.java.tree.J.MethodDeclaration;
import org.openrewrite.java.tree.Space;
import org.reflections.util.ClasspathHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.event.Level;

/**
 * For more information see: <a
 * href="https://wiki.corp.mongodb.com/pages/viewpage.action?pageId=315004119">Atlas IAM - Endpoint
 * Actions</a>
 *
 * <p>To run: bazel run //server/src/main/com/xgen/cloud/authz_tools:GenerateEndpointActionsTool --
 * --rewrite
 */
public class GenerateEndpointActionsTool {
  private static final Logger LOG = LoggerFactory.getLogger(GenerateEndpointActionsTool.class);

  private static final String ANNOTATION_PACKAGE_NAME =
      "com.xgen.cloud.common.access._public.annotation";
  private static final String ANNOTATION_NAME = "Auth";
  private static final String ENDPOINT_ACTION_ANNOTATION_ARGUMENT = "endpointAction";
  private static final String DEFAULT_ANNOTATION_INDENTATION = " ".repeat(2);
  private static final String[] AUTH_FILE_FILTER = new String[] {"@Auth"};
  private static final String[] UI_CALL_ROLES_ALLOWED_FILE_FILTER =
      new String[] {"@UiCall", "@RolesAllowed"};
  private static final String ROLES_ALLOWED_SIMPLE_NAME = "RolesAllowed";
  private static final String UI_CALL_SIMPLE_NAME = "UiCall";
  private static final Set<String> HTTP_METHODS =
      Set.of("@GET", "@PUT", "@PATCH", "@POST", "@DELETE", "@HEAD", "@OPTIONS");
  private static final Pattern CONSTANT_PATTERN = Pattern.compile("[A-Z]+_[A-Z_]+");
  private static final String ENDPOINT_ACTION_NAMESPACE_PREFIX = "epa";
  private static Level logLevel = Level.INFO; // Default log level
  private static final String PACKAGE_SCOPE = "com.xgen";

  /**
   * Endpoints that are excluded from EPA validation when running the --check mode.
   * <p>
   * ONLY FOR TEMPORARILY USE - Add endpoints that cause unexpected failures and remove them after
   * fixing the issue.
   */
  private static final Set<String> EXCLUDED_ENDPOINTS = Set.of(
      // Example: "com.xgen.module.federation.res.FederationSettingsResource.delete"

      // Temporarily adding a test endpoint to demonstrate file-level exclusion
      "com.xgen.cloud.authz_tools._public.tools.fixtures.MissingEpaValidRolesAllowedOnMethodResource.missing"
  );

  public static void main(String[] args) {
    List<String> argsList = Arrays.asList(args);

    if (argsList.contains("--help") || argsList.isEmpty()) {
      System.out.println(
          """
Usage for GenerateEndpointActionsTool:

bazel run //server/src/main/com/xgen/cloud/authz_tools:GenerateEndpointActionsTool\
 -- [options...]

Where options can be:
    --help                         Show this help
    --check                        Check all @Auth annotations
    --fix                          Fix all @Auth annotations
    --level=[INFO|ERROR]           Set the log level for the tool (default: INFO)
    --auth-only                    Consider only files with existing @Auth
                                   (e.g. the ones annotated for Private Preview)

To run the generator directly:
    --rewrite                      Run the generator and rewrite files
    --dry                          Dry-run the generator showing the diff
    --limit=N                      Limit processing to N number of files\
 (default=0, unbounded)
    --filter=File1.java,File2.Java Only run on the given filenames
    --source-sets=main,unit,test   Select which source sets to run through\
 (default=main)
""");
      return;
    }
    if (argsList.contains("--fix") || argsList.contains("--check")) {
      if (argsList.contains("--level=" + Level.ERROR)) {
        logLevel = Level.ERROR;
      }
      boolean checkEndpointsWithAuthAnnotationOnly = argsList.contains("--auth-only");
      List<String> javaFilesWithErrors = check(checkEndpointsWithAuthAnnotationOnly);
      if (!javaFilesWithErrors.isEmpty() && logLevel == Level.ERROR) {
        throw new IllegalStateException("Endpoint Actions errors detected.");
      }
      if (argsList.contains("--fix")) {
        RewriteRunner.runWithCLI(
            new String[] {"--rewrite", "--filter=" + String.join(",", javaFilesWithErrors)},
            // Only process files with @Auth annotations if --auth-only is set
            argsList.contains("--auth-only") ? AUTH_FILE_FILTER : new String[] {},
            UI_CALL_ROLES_ALLOWED_FILE_FILTER,
            getSupplier());
      }
      return;
    }

    // Rewrite mode
    RewriteRunner.runWithCLI(
        args,
        // Only process files with @Auth annotations if --auth-only is set
        argsList.contains("--auth-only") ? AUTH_FILE_FILTER : new String[] {},
        // Only process files with either @UiCall and @RolesAllowed
        UI_CALL_ROLES_ALLOWED_FILE_FILTER,
        getSupplier());
  }

  /**
   * Checks for missing, duplicate, or unexpected EPAs.
   *
   * @return List of Java files with errors found during the checks. This list can be passed in as
   *     the file filter for the fix method.
   */
  public static List<String> check(boolean checkEndpointsWithAuthAnnotationOnly) {
    LOG.info("=== Running @Auth Checks ===\n");
    LOG.info("Reference wiki: https://wiki.corp.mongodb.com/x/15TGEg");
    LOG.info("Severity level: {}\n", logLevel.toString());
    Timer timer = new Timer().start();
    // Get all EPA qualifying methods, regardless of if they currently have an EPA
    List<Method> allEpaQualifyingMethods =
        EndpointInfo.getMethodsWithEpaQualifyingUiCallOrRolesAllowedAnnotation(
            checkEndpointsWithAuthAnnotationOnly,
            PACKAGE_SCOPE,
            ClasspathHelper.forPackage(PACKAGE_SCOPE));
    // Get methods that qualify but are currently missing an EPA
    List<Method> methodsMissingEpa =
        EndpointInfo.getMethodsMissingEndpointAnnotation(allEpaQualifyingMethods);
    // Get EndpointInfo for methods that currently have an EPA
    List<EndpointInfo> endpointsWithEpa =
        EndpointInfo.getEndpointInfoForCurrentMethodsWithEpa(allEpaQualifyingMethods);
    Set<Method> methodsWithErrors = new HashSet<>();
    LOG.info("--- Looking for duplicate EPAs ---\n");
    Map<String, EndpointInfo> endpointAnnotations = new HashMap<>();
    int countDuplicates = 0;
    for (EndpointInfo endpointInfo : endpointsWithEpa) {
      if (endpointAnnotations.putIfAbsent(endpointInfo.epa(), endpointInfo) != null) {
        countDuplicates++;
        logAtLevel("⚠️ Found methods with duplicate @Auth values:");
        logAtLevel(endpointInfo.epa());
        logMethods(
            List.of(endpointAnnotations.get(endpointInfo.epa()).method(), endpointInfo.method()),
            2);
      }
    }
    if (countDuplicates == 0) {
      LOG.info("✅ No duplicate EPAs found\n");
    }

    LOG.info("--- Looking for unexpected EPAs ---\n");
    int countUnexpected = 0;
    for (EndpointInfo endpointInfo : endpointsWithEpa) {
      String expectedEpa = getEndpointActionForMethod(endpointInfo.method());
      if (!endpointAnnotations.containsKey(expectedEpa)) {
        countUnexpected++;
        logAtLevel(
            "⚠️  Expected to find @Auth(\"{}\") on {} but found" + " @Auth(\"{}\")",
            expectedEpa,
            EndpointInfo.fullyQualifiedMethodName(endpointInfo.method()),
            endpointInfo.epa());
        methodsWithErrors.add(endpointInfo.method());
      } else if (!endpointAnnotations.get(expectedEpa).method().equals(endpointInfo.method())) {
        // Found a valid EPA, but it is on a different method than expected
        countUnexpected++;
        logAtLevel(
            "⚠️  Expected to find @Auth(\"{}\") on {} but was on {}",
            endpointInfo.epa(),
            EndpointInfo.fullyQualifiedMethodName(endpointAnnotations.get(expectedEpa).method()),
            EndpointInfo.fullyQualifiedMethodName(endpointInfo.method()));
        methodsWithErrors.add(endpointInfo.method());
      }
    }

    if (countUnexpected == 0) {
      LOG.info("✅ No unexpected EPAs found\n");
    }

    LOG.info("--- Looking for methods missing EPAs ---\n");

    // Separate excluded endpoints from actual errors
    List<Method> excludedMethodsMissingEpa = methodsMissingEpa.stream()
        .filter(method -> EXCLUDED_ENDPOINTS.contains(EndpointInfo.fullyQualifiedMethodName(method)))
        .toList();
    List<Method> nonExcludedMethodsMissingEpa = methodsMissingEpa.stream()
        .filter(method -> !EXCLUDED_ENDPOINTS.contains(EndpointInfo.fullyQualifiedMethodName(method)))
        .toList();

    int countMissing = methodsMissingEpa.size();
    int countExcludedMissing = excludedMethodsMissingEpa.size();
    int countNonExcludedMissing = nonExcludedMethodsMissingEpa.size();

    if (!methodsMissingEpa.isEmpty()) {
      logAtLevel("⚠️  Found {} methods missing @Auth:", methodsMissingEpa.size());
      if (!nonExcludedMethodsMissingEpa.isEmpty()) {
        logAtLevel("   Non-excluded methods missing @Auth ({}):", countNonExcludedMissing);
        logMethods(nonExcludedMethodsMissingEpa, 10);
      }
      if (!excludedMethodsMissingEpa.isEmpty()) {
        LOG.info("   Excluded methods missing @Auth ({}) - not causing failure:", countExcludedMissing);
        logMethods(excludedMethodsMissingEpa, 10);
      }
      // Only add non-excluded methods to the errors list
      methodsWithErrors.addAll(nonExcludedMethodsMissingEpa);
    }
    if (countMissing == 0) {
      LOG.info("✅ No missing EPAs found\n");
    } else if (countNonExcludedMissing == 0) {
      LOG.info("✅ No non-excluded missing EPAs found\n");
    }

    // Get files that contain any excluded endpoints - these files will be completely skipped
    Set<String> filesWithExcludedEndpoints = excludedMethodsMissingEpa.stream()
        .map(method -> method.getDeclaringClass().getSimpleName() + ".java")
        .collect(Collectors.toSet());

    // Only include files that don't contain any excluded endpoints
    // This prevents --fix from trying to process files with excluded endpoints
    List<String> javaFilesWithErrors =
        methodsWithErrors.stream()
            .map(method -> method.getDeclaringClass().getSimpleName() + ".java")
            .filter(fileName -> !filesWithExcludedEndpoints.contains(fileName))
            .distinct()
            .sorted()
            .toList();

    LOG.info("--- Stats ---\n");
    LOG.info("Total methods requiring EPAs: {}", allEpaQualifyingMethods.size());
    LOG.info("Duplicate EPAs: {}", countDuplicates);
    LOG.info("Unexpected EPAs: {}", countUnexpected);
    LOG.info("Missing EPAs: {} (excluded: {}, non-excluded: {})", countMissing, countExcludedMissing, countNonExcludedMissing);
    if (!EXCLUDED_ENDPOINTS.isEmpty()) {
      LOG.info("Excluded endpoints: {}", EXCLUDED_ENDPOINTS.size());
    }
    LOG.info("Run time: {} ms\n", timer.stop().getMillis());

    if (!javaFilesWithErrors.isEmpty()) {
      logAtLevel("--- Required Actions ---");
    }
    if (countDuplicates > 0) {
      logAtLevel(
          "⚠️  Action: Duplicate endpoint actions likely indicate a resource method is"
              + " overloaded.");
      logAtLevel("Fix or reach out to #ask-atlas-iam if that is not the case.");
    }
    if (countUnexpected > 0 || countMissing > 0) {
      logAtLevel("⚠️  Action: Fix unexpected or missing endpoint actions with:");
      logAtLevel(
          "bazel run"
              + " //server/src/main/com/xgen/cloud/common/authz_tools:GenerateEndpointActionsTool"
              + " -- --fix");
    }
    return javaFilesWithErrors;
  }

  public static String getEndpointActionForMethod(Method method) {
    return getEndpointActionName(
        getRoleSetBasedResourceTypeForMethod(method),
        method.getDeclaringClass().getPackageName(),
        method.getDeclaringClass().getSimpleName(),
        method.getName(),
        getHttpMethodForMethod(method));
  }

  public static String getRoleSetBasedResourceTypeForMethod(Method method) {
    UiCall uiCall = method.getAnnotation(UiCall.class);
    // If not present on method, check for a class level annotation
    RolesAllowed rolesAllowed =
        Optional.ofNullable(method.getAnnotation(RolesAllowed.class))
            .orElse(method.getDeclaringClass().getAnnotation(RolesAllowed.class));
    List<RoleSet> uiCallRoleSets =
        uiCall != null ? Arrays.stream(uiCall.roles()).toList() : List.of();
    List<RoleSet> rolesAllowedRoleSets =
        rolesAllowed != null
            ? Arrays.stream(rolesAllowed.value()).map(RoleSet::valueOf).toList()
            : List.of();
    if (!uiCallRoleSets.isEmpty() && !rolesAllowedRoleSets.isEmpty()) {
      throw new IllegalStateException(
          "Cannot have role sets from both UICall and RolesAllowed "
              + "annotations on the same method: "
              + EndpointInfo.fullyQualifiedMethodName(method));
    }
    List<RoleSet> roleSets = uiCallRoleSets.isEmpty() ? rolesAllowedRoleSets : uiCallRoleSets;
    if (roleSets.isEmpty()) {
      throw new IllegalStateException(
          "Cannot determine resource type for method "
              + method.getName()
              + "- no role sets found in @UiCall or @RolesAllowed annotations.");
    }
    return epaResourceTypeForRoleSets(roleSets);
  }

  public static String getHttpMethodForMethod(Method method) {
    for (java.lang.annotation.Annotation annotation : method.getDeclaredAnnotations()) {
      if (HTTP_METHODS.contains("@" + annotation.annotationType().getSimpleName())) {
        return annotation.annotationType().getSimpleName();
      }
    }
    throw new IllegalStateException(
        "Method "
            + EndpointInfo.fullyQualifiedMethodName(method)
            + " has auth annotations but is not annotated with any valid HTTP method: "
            + HTTP_METHODS.stream().sorted().toList());
  }

  public static Supplier<TreeVisitor<?, ExecutionContext>> getSupplier() {
    return () ->
        new JavaIsoVisitor<>() {
          private String className = "";
          private String packageName = "";
          private List<RoleSet> roleSetsFromRolesAllowedOnClass = new ArrayList<>();

          public J.ClassDeclaration visitClassDeclaration(
              J.ClassDeclaration classDecl, ExecutionContext executionContext) {
            if (className.isEmpty()) {
              className = classDecl.getSimpleName();
              if (classDecl.getType() != null) {
                packageName = classDecl.getType().getPackageName();
              }
              roleSetsFromRolesAllowedOnClass = getAnyRoleSetsFromRolesAllowedOnClass(classDecl);
            }
            return super.visitClassDeclaration(classDecl, executionContext);
          }

          @Override
          public J.MethodDeclaration visitMethodDeclaration(
              J.MethodDeclaration methodDecl, ExecutionContext executionContext) {
            String httpMethod = getHttpMethodAnnotation(methodDecl);
            // Only add annotation if there is an HTTP method annotation
            if (httpMethod.isEmpty()) {
              return methodDecl;
            }

            // Only add annotation if there is a qualifying @UiCall or @RolesAllowed annotation
            // on the method or @RolesAllowed is on the class
            if (!methodHasQualifyingUiCallOrRolesAllowedAnnotation(methodDecl)
                && roleSetsFromRolesAllowedOnClass.isEmpty()) {
              return methodDecl;
            }

            // Determine the resource parent based on @UiCall or @RolesAllowed
            String roleSetBasedResourceType =
                getRoleSetBasedResourceType(methodDecl, roleSetsFromRolesAllowedOnClass);

            // Ensure the import for the annotation is added
            maybeAddImport(ANNOTATION_PACKAGE_NAME + "." + ANNOTATION_NAME, null, false);

            // Get a version of the method without any existing endpoint action annotation
            MethodDeclaration methodWithoutAnnotation =
                methodDecl.withLeadingAnnotations(
                    methodDecl.getLeadingAnnotations().stream()
                        .filter(annotation -> !annotation.getSimpleName().equals(ANNOTATION_NAME))
                        .toList());

            // Create a new list of annotations
            List<Annotation> annotations =
                new ArrayList<>(methodWithoutAnnotation.getLeadingAnnotations());

            String endpointActionName =
                getEndpointActionName(
                    roleSetBasedResourceType,
                    packageName,
                    className,
                    methodDecl.getSimpleName(),
                    httpMethod);

            // Build the endpoint action annotation by simulating a method declaration with the
            // annotation added through JavaTemplate
            String annotationTemplate =
                String.format(
                    "@%s(%s = \"%s\")",
                    ANNOTATION_NAME, ENDPOINT_ACTION_ANNOTATION_ARGUMENT, endpointActionName);
            MethodDeclaration hypotheticalMethodWithAnnotation =
                JavaTemplate.builder(annotationTemplate)
                    .build()
                    .apply(
                        updateCursor(methodWithoutAnnotation),
                        methodWithoutAnnotation
                            .getCoordinates()
                            .addAnnotation(Comparator.comparing(J.Annotation::getSimpleName)));

            // Retrieve the @Auth annotation and adjust the indentation to be similar
            // to the other annotations
            Annotation authAnnotation =
                hypotheticalMethodWithAnnotation.getLeadingAnnotations().stream()
                    .filter(annotation -> annotation.getSimpleName().equals(ANNOTATION_NAME))
                    .map(
                        annotation ->
                            annotation.withPrefix(getAnnotationWhitespace(methodWithoutAnnotation)))
                    .findFirst()
                    .orElseThrow();

            // Add the new annotation to the end of the existing ones
            annotations.add(authAnnotation);

            // Return the method with the new set of annotations
            return methodWithoutAnnotation.withLeadingAnnotations(annotations);
          }
        };
  }

  private static List<RoleSet> getAnyRoleSetsFromRolesAllowedOnClass(ClassDeclaration classDecl) {
    Matcher annotationMatcher =
        CONSTANT_PATTERN.matcher(
            classDecl.getLeadingAnnotations().stream()
                .filter(x -> x.getSimpleName().equals(ROLES_ALLOWED_SIMPLE_NAME))
                .findFirst()
                .map(Annotation::toString)
                .orElse(""));
    List<RoleSet> roleSets = new ArrayList<>();
    while (annotationMatcher.find()) {
      RoleSet roleSet;
      try {
        roleSet = RoleSet.valueOf(annotationMatcher.group());
        if (roleSet == RoleSet.ANY_AUTHENTICATED_USER) {
          continue;
        }
        roleSets.add(roleSet);
      } catch (IllegalArgumentException e) {
        // Keep going if the annotation contains a string that is not a valid RoleSet
      }
    }
    return roleSets;
  }

  private static boolean methodHasQualifyingUiCallOrRolesAllowedAnnotation(
      MethodDeclaration methodDecl) {
    return methodDecl.getLeadingAnnotations().stream()
        .filter(
            x -> Arrays.asList(UI_CALL_ROLES_ALLOWED_FILE_FILTER).contains("@" + x.getSimpleName()))
        .anyMatch(
            x -> {
              List<Expression> arguments = x.getArguments();
              // null is okay because @UICall can have the default value of GROUP_OWNER for roles
              // and @RolesAllowed can't be declared without a value
              return arguments == null
                  || arguments.stream()
                      .noneMatch(
                          arg -> {
                            String argStr = arg.toString();
                            // No need to add an EPA on endpoints with @UiCall(auth = false)
                            // or endpoints without specific authorization logic
                            return argStr.equals("auth = false")
                                || argStr.contains(RoleSet.ANY_AUTHENTICATED_USER.name());
                          });
            });
  }

  private static String getRoleSetBasedResourceType(
      MethodDeclaration methodDecl, List<RoleSet> roleSetsFromRolesAllowedOnClass) {
    // The way this works is, it takes a method declaration, find its @UiCall or
    // @RolesAllowed annotation, and looks for all strings of the form CONSTANT_CASE.
    // It then reverse-lookups those against RoleSets and for the final list of role sets,
    // and calls epaResourceTypeForRoleSets to determine the resource type

    Matcher annotationMatcher =
        CONSTANT_PATTERN.matcher(
            methodDecl.getLeadingAnnotations().stream()
                .filter(
                    x ->
                        Arrays.asList(UI_CALL_ROLES_ALLOWED_FILE_FILTER)
                            .contains("@" + x.getSimpleName()))
                .findFirst()
                .map(Annotation::toString)
                .orElse(""));
    List<RoleSet> roleSets = new ArrayList<>();
    while (annotationMatcher.find()) {
      RoleSet roleSet;
      try {
        roleSet = RoleSet.valueOf(annotationMatcher.group());
        roleSets.add(roleSet);
      } catch (IllegalArgumentException e) {
        // Keep going if the annotation contains a string that is not a valid RoleSet
      }
    }

    // Add GROUP_OWNER if there's a plain @UICall annotation (no arguments)
    // because it is the default role set
    boolean hasPlainUICall =
        methodDecl.getLeadingAnnotations().stream()
            .anyMatch(
                x ->
                    x.getSimpleName().equals(UI_CALL_SIMPLE_NAME)
                        && (x.getArguments() == null || x.getArguments().isEmpty()));
    if (hasPlainUICall) {
      roleSets.add(RoleSet.GROUP_READ_ONLY);
    }

    if (roleSets.isEmpty() && roleSetsFromRolesAllowedOnClass.isEmpty()) {
      throw new IllegalStateException(
          "Cannot determine resource type for method "
              + methodDecl.getSimpleName()
              + " - no role sets found in @UiCall or @RolesAllowed annotations.");
    }
    // If there are no role sets from the method, use the class level role sets
    return roleSets.isEmpty()
        ? epaResourceTypeForRoleSets(roleSetsFromRolesAllowedOnClass)
        : epaResourceTypeForRoleSets(roleSets);
  }

  private static String getHttpMethodAnnotation(J.MethodDeclaration methodDecl) {
    return methodDecl.getLeadingAnnotations().stream()
        .filter(x -> HTTP_METHODS.contains("@" + x.getSimpleName()))
        .findFirst()
        .map(Annotation::getSimpleName)
        .orElse("");
  }

  private static String getEndpointActionName(
      String resourceType,
      String packageName,
      String className,
      String methodName,
      String httpMethod) {

    Pattern pattern = Pattern.compile("(\\d{4}_\\d{2}_\\d{2})");
    Matcher matcher = pattern.matcher(packageName);
    String version = matcher.find() ? matcher.group(1) : "";
    String classNameWithVersion = className + (version.isEmpty() ? "" : "_" + version);

    // Combine EPA namespace prefix and all of the above into a single dot-separated action name.
    return "%s.%s.%s.%s.%s"
        .formatted(
            ENDPOINT_ACTION_NAMESPACE_PREFIX,
            resourceType,
            classNameWithVersion,
            methodName,
            httpMethod);
  }

  private static Space getAnnotationWhitespace(MethodDeclaration methodDecl) {
    // Try to follow the whitespace convention of the surrounding annotations by
    // adopting their indentation.
    return Space.build(
        methodDecl.getLeadingAnnotations().stream()
            .map(x -> x.getPrefix().getWhitespace())
            .filter(x -> !x.isEmpty())
            .min(Comparator.comparingInt(String::length))
            .orElse("\n" + DEFAULT_ANNOTATION_INDENTATION),
        List.of());
  }

  private static void logAtLevel(String message, Object... params) {
    LOG.makeLoggingEventBuilder(logLevel).log(message, params);
  }

  private static void logMethods(List<Method> methods, int maximum) {
    for (Method method : methods.stream().limit(maximum).toList()) {
      logAtLevel(" * {}", EndpointInfo.fullyQualifiedMethodName(method));
    }
    if (methods.size() > maximum) {
      logAtLevel(" ... and {} more method(s)", methods.size() - maximum);
    }
  }

  private static String epaResourceTypeForRoleSets(@NotEmpty List<RoleSet> roleSets) {
    // Determine the most granular role level across all role sets
    Role.Level mostGranularRoleLevel =
        roleSets.stream()
            .map(RoleSet::getMostGranularRoleLevel)
            .max(Comparator.comparingInt(Enum::ordinal))
            .orElseThrow(() -> new NoSuchElementException("stream cannot be empty"));
    // Map the final level to the corresponding epa resource type
    return switch (mostGranularRoleLevel) {
      case GROUP -> ResourceTypeConstants.PROJECT.getType();
      case ORG -> ResourceTypeConstants.ORGANIZATION.getType();
      default -> ResourceTypeConstants.GLOBAL.getType();
    };
  }


}
