package com.xgen.cloud.authz_tools._public.tools.constants;

import java.nio.file.Path;
import java.nio.file.Paths;

public class AuthzDirectoryConstants {
  public static final Path WORKSPACE_DIR = getWorkspaceDir();
  public static final Path AUTHZ_RESOURCE_DIR =
      WORKSPACE_DIR.resolve("server/src/main/com/xgen/cloud/services/authz/resource/");
  public static final Path AUTHZ_COMMON_CONSTANTS_DIR =
      WORKSPACE_DIR.resolve("server/src/main/com/xgen/cloud/common/constants/_public/model/");
  public static final Path MANAGED_POLICIES_DIR = AUTHZ_RESOURCE_DIR.resolve("managed_policies");
  public static final Path CEDAR_DIR = AUTHZ_RESOURCE_DIR.resolve("cedar");

  public static Path getWorkspaceDir() {
    String workspaceDir = System.getenv("BUILD_WORKSPACE_DIRECTORY");
    if (workspaceDir == null) {
      return Paths.get(""); // Default value for testing or fallback
    }
    return Paths.get(workspaceDir);
  }
}
