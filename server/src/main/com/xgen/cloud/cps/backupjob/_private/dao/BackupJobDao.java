package com.xgen.cloud.cps.backupjob._private.dao;

import static com.xgen.cloud.common.mongo._public.mongo.DbUtils.joinFields;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.AutoExportSettingsFields.BUCKET_ID;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.AutoExportSettingsFields.FREQUENCY_TYPE;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.ExtraRetentionSettingFields.RETENTION_DAYS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.AUTO_EXPORT_ENABLED;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.CLUSTER_HEALTHY_DATE;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.CLUSTER_NAME;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.CLUSTER_STORAGE_SYSTEM;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.CLUSTER_TYPE;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.CLUSTER_UNIQUE_ID;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.COPY_SETTINGS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.DISK_BACKUP_STATE;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.EXPORT_SETTINGS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.EXTRA_RETENTION_SETTINGS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.IS_CLUSTER_HEALTHY;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.NEXT_SNAPSHOT_DATE;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.OPLOG_STATUS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.PIT_ENABLED;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.PIT_SETTINGS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.POLICIES;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.PREFERRED_SNAPSHOT_VOLUMES;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.PROJECT_ID;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.REFERENCE_TIME_IN_MINS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.RESTORE_JOB_IDS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.RESTORE_WINDOW_DAYS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.RETAIN_BACKUPS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.RUNTIME_OVERRIDES;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.SLICE_SCHEMA_MIRGRATION_COMPLETED;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.USE_ORG_AND_GROUP_NAMES_IN_EXPORT_PREFIX;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.FieldDefs.VERSION;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.OplogMigrationFields.START_DATE;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.OplogMigrationFields.STORAGE;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.BLOB_STORE_CONFIG_ID;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.CMK;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.COPY_STORAGES;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.COPY_STORAGES_MIGRATION;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.KEY_CIPHERTEXT;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.KEY_CIPHERTEXT_HASH;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.LAST_CONTIGUOUS_CHECK_END_TIMESTAMP;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.LAST_ROTATED_DATE;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.LAST_VALIDATED_DATE;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.NEXT_GEN_START_TS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.OPLOG_MIGRATION;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.REGIONAL_METADATA_STORE_CONFIG_ID;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.REGION_NAME;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.ROLLBACK_STATUS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.SHOULD_RECOVER_OPLOGS;
import static com.xgen.cloud.cps.backupjob._private.dao.BackupJobDao.PitSettingFields.VERSIONED_CMK;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.ReadPreference;
import com.mongodb.WriteResult;
import com.xgen.cloud.common.db.legacy._public.cursor.ModelCursor;
import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.cloud.common.driverwrappers._public.legacy.DBCursor;
import com.xgen.cloud.common.metrics._public.annotations.PromMethodMetrics;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc.DaoOpType;
import com.xgen.cloud.common.metrics._public.svc.PromMetricsSvc.DaoOperation;
import com.xgen.cloud.common.util._public.util.DriverUtils;
import com.xgen.cloud.cps.backupjob._public.model.BackupFrequencyType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob.AutoExportSettings;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob.ClusterType;
import com.xgen.cloud.cps.backupjob._public.model.BackupJob.DiskBackupState;
import com.xgen.cloud.cps.backupjob._public.model.CopySetting;
import com.xgen.cloud.cps.backupjob._public.model.ExtraRetentionSetting;
import com.xgen.cloud.cps.backupjob._public.model.Policy;
import com.xgen.cloud.cps.backupjob._public.model.PolicyItem;
import com.xgen.cloud.cps.pit._public.model.CpsOplogId;
import com.xgen.cloud.cps.pit._public.model.OplogMigration;
import com.xgen.cloud.cps.pit._public.model.PitSetting;
import com.xgen.cloud.cps.pit._public.model.PitStorage;
import com.xgen.cloud.cps.pit._public.model.RollbackStatus;
import com.xgen.cloud.cps.pit._public.model.RollbackStatus.RollbackState;
import com.xgen.cloud.nds.cloudprovider._public.model.CloudProvider;
import com.xgen.cloud.nds.cloudprovider._public.model.RegionNameHelper;
import com.xgen.cloud.nds.project._public.model.ClusterDescription.StorageSystem;
import com.xgen.svc.core.dao.base.BaseDao;
import com.xgen.svc.core.dao.base.MongoIndex;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.BSONTimestamp;
import org.bson.types.ObjectId;

@Singleton
public class BackupJobDao extends BaseDao {
  public static final String CONNECTION_NAME = "atlasbackup";
  public static final String DB_NAME = "nds";
  public static final String COLLECTION_NAME = "config.nds.backup.jobs";

  private static final String DURATION_METRIC_NAME =
      "mms_nds_backup_job_dao_query_duration_seconds";
  private static final String DURATION_METRIC_DESCRIPTION =
      "duration of queries to the config.nds.backup.jobs collection";

  @Inject
  public BackupJobDao(final MongoSvc pMongoSvc) {
    super(pMongoSvc, CONNECTION_NAME, DB_NAME, COLLECTION_NAME);
  }

  @Override
  public List<MongoIndex> getIndexes() {
    final List<MongoIndex> list = super.getIndexes();
    list.add(new MongoIndex(this).key(PROJECT_ID).key(CLUSTER_NAME).unique());
    list.add(new MongoIndex(this).key(PROJECT_ID).key(CLUSTER_UNIQUE_ID).unique());
    list.add(new MongoIndex(this).key(PROJECT_ID).key(DISK_BACKUP_STATE).key(NEXT_SNAPSHOT_DATE));
    list.add(new MongoIndex(this).key(BaseDao.ID).key(DISK_BACKUP_STATE));
    list.add(
        new MongoIndex(this)
            .key(PROJECT_ID)
            .key(CLUSTER_NAME)
            .key(DISK_BACKUP_STATE)
            .partialFilterExpression(
                new BasicDBObject(DISK_BACKUP_STATE, DiskBackupState.ACTIVE.getValue()))
            .unique());

    return list;
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.CREATE)
  public ObjectId create(
      final ObjectId projectId,
      final String clusterName,
      final ObjectId clusterUniqueId,
      final ClusterType clusterType,
      @Nullable final StorageSystem clusterStorageSystem,
      final int pitWindowDays,
      final List<PolicyItem> policyItemList,
      final boolean newRegionMappingEnabled,
      final Date nextSnapshotDate) {
    final ObjectId id = new ObjectId();
    final BasicDBObject job =
        new BasicDBObject()
            .append(BaseDao.ID, id)
            .append(FieldDefs.PROJECT_ID, projectId)
            .append(FieldDefs.CLUSTER_NAME, clusterName)
            .append(FieldDefs.CLUSTER_UNIQUE_ID, clusterUniqueId)
            .append(FieldDefs.CLUSTER_TYPE, clusterType.getValue())
            .append(
                FieldDefs.CLUSTER_STORAGE_SYSTEM,
                clusterStorageSystem != null
                    ? clusterStorageSystem.name()
                    : StorageSystem.LOCAL.name())
            .append(
                FieldDefs.REFERENCE_TIME_IN_MINS,
                BackupJob.calculateReferenceTime(nextSnapshotDate))
            .append(FieldDefs.NEXT_SNAPSHOT_DATE, nextSnapshotDate)
            .append(FieldDefs.RESTORE_WINDOW_DAYS, pitWindowDays)
            .append(FieldDefs.DISK_BACKUP_STATE, DiskBackupState.ACTIVE.getValue())
            .append(FieldDefs.POLICIES, createPolicyList(policyItemList))
            .append(FieldDefs.NEW_REGION_MAPPING_ENABLED, newRegionMappingEnabled)
            .append(VERSION, 1);

    saveMajority(job);
    return id;
  }

  public static DBObject toDbObj(final BackupJob backupJob) {
    final DBObject job =
        new BasicDBObject()
            .append(BaseDao.ID, backupJob.getId())
            .append(PROJECT_ID, backupJob.getProjectId())
            .append(CLUSTER_NAME, backupJob.getClusterName())
            .append(CLUSTER_UNIQUE_ID, backupJob.getClusterUniqueId())
            .append(CLUSTER_TYPE, backupJob.getClusterType().getValue())
            .append(
                CLUSTER_STORAGE_SYSTEM,
                (backupJob.getStorageSystem() != null
                    ? backupJob.getStorageSystem()
                    : StorageSystem.LOCAL.name()))
            .append(REFERENCE_TIME_IN_MINS, backupJob.getReferenceTime())
            .append(NEXT_SNAPSHOT_DATE, backupJob.getNextSnapshotDate())
            .append(RESTORE_WINDOW_DAYS, backupJob.getRestoreWindowDays())
            .append(DISK_BACKUP_STATE, backupJob.getDiskBackupState().getValue())
            .append(
                RESTORE_JOB_IDS,
                backupJob.getRestoreJobIds() != null ? backupJob.getRestoreJobIds() : null)
            .append(POLICIES, toDbObj(backupJob.getPolicies()))
            .append(PIT_ENABLED, backupJob.isPitEnabled())
            .append(SLICE_SCHEMA_MIRGRATION_COMPLETED, backupJob.isSliceSchemaMigrationCompleted())
            .append(VERSION, backupJob.getVersion())
            .append(PIT_SETTINGS, toDbObj(backupJob.getPitSettings()))
            .append(OPLOG_STATUS, oplogStatusToDbObj(backupJob.getOplogStatus()))
            .append(EXPORT_SETTINGS, fromAutoExportSettings(backupJob.getAutoExportSettings()))
            .append(AUTO_EXPORT_ENABLED, backupJob.isAutoExportEnabled())
            .append(
                USE_ORG_AND_GROUP_NAMES_IN_EXPORT_PREFIX,
                backupJob.shouldUseOrgAndGroupNamesInExportPrefix())
            .append(RUNTIME_OVERRIDES, backupJob.getRuntimeOverrides())
            .append(RETAIN_BACKUPS, backupJob.shouldRetainBackups())
            .append(
                COPY_SETTINGS,
                backupJob.getCopySettings() != null
                    ? backupJob.getCopySettings().stream()
                        .map(CopySetting::toDBObject)
                        .collect(Collectors.toList())
                    : null)
            .append(
                EXTRA_RETENTION_SETTINGS,
                backupJob.getExtraRetentionSettings() != null
                    ? backupJob.getExtraRetentionSettings().stream()
                        .map(ExtraRetentionSetting::toDBObject)
                        .collect(Collectors.toList())
                    : null);
    return job;
  }

  private static DBObject toDbObj(final RollbackStatus rollback) {
    if (rollback == null) {
      return null;
    }
    return new BasicDBObject()
        .append(RollbackFields.STATE, rollback.getStateDesc())
        .append(RollbackFields.COMMON_POINT_TS, rollback.getCommonPointTs())
        .append(RollbackFields.COMMON_POINT_HASH, rollback.getCommonPointHash())
        .append(RollbackFields.COMMON_POINT_TERM, rollback.getCommonPointTerm());
  }

  private static RollbackStatus toRollbackStatus(final BasicDBObject obj) {
    if (obj == null) {
      return null;
    }
    return new RollbackStatus(
        RollbackState.fromDesc((String) obj.get(RollbackFields.STATE)),
        (BSONTimestamp) obj.get(RollbackFields.COMMON_POINT_TS),
        (Long) obj.get(RollbackFields.COMMON_POINT_HASH),
        (Long) obj.get(RollbackFields.COMMON_POINT_TERM));
  }

  private static OplogMigration toOplogMigration(final BasicDBObject obj) {
    if (obj == null) {
      return null;
    }
    return new OplogMigration(
        toPitStorageList((BasicDBList) obj.get(STORAGE)), obj.getDate(START_DATE));
  }

  private static List<PitStorage> toPitStorageList(final BasicDBList list) {
    if (list == null) {
      return new ArrayList<>();
    }
    return list.stream()
        .map(obj -> (BasicDBObject) obj)
        .map(
            obj ->
                new PitStorage(
                    obj.getString(PitStorageFields.STORAGEMETADATA_STORE_CONFIG_ID),
                    obj.getString(PitStorageFields.BLOB_STORE_CONFIG_ID),
                    obj.getString(PitStorageFields.BLOB_STORE_REGION_NAME)))
        .collect(Collectors.toList());
  }

  private static BasicDBObject toDbObj(final CpsOplogId cpsOplogId) {
    if (cpsOplogId == null) {
      return null;
    }
    return new BasicDBObject()
        .append(OplogFields.LAST_TS, cpsOplogId.getTs())
        .append(OplogFields.LAST_HASH, cpsOplogId.getHash())
        .append(OplogFields.LAST_TERM, cpsOplogId.getTerm());
  }

  private static CpsOplogId toOplogId(final BasicDBObject obj) {
    if (obj == null) {
      return null;
    }
    return new CpsOplogId(
        (BSONTimestamp) obj.get(OplogFields.LAST_TS),
        (Long) obj.get(OplogFields.LAST_HASH),
        (Long) obj.get(OplogFields.LAST_TERM));
  }

  private BasicDBList createPolicyList(final List<PolicyItem> policyItemList) {
    final ObjectId id = new ObjectId();
    final BasicDBList policyList = new BasicDBList();
    policyList.add(
        new BasicDBObject()
            .append(Policy.FieldDefs.ID, id)
            .append(Policy.FieldDefs.POLICY_ITEMS, createPolicyItemList(policyItemList)));
    return policyList;
  }

  public static BasicDBList createPolicyItemList(final List<PolicyItem> policyItemList) {
    final BasicDBList policyItemListDB = new BasicDBList();
    for (PolicyItem item : policyItemList) {
      BasicDBObject policy =
          createDefaultPolicyItem(item); // default policy items don't have nextSnapShotDate
      policyItemListDB.add(policy);
    }

    return policyItemListDB;
  }

  static BasicDBList policyItemsToDbList(final List<PolicyItem> policyItems) {
    final BasicDBList policyItemDbList = new BasicDBList();
    policyItems.forEach(item -> policyItemDbList.add(policyItemToDB(item)));

    return policyItemDbList;
  }

  private static DBObject toDbObj(final List<Policy> policies) {
    final Policy policy = policies.get(0);
    final BasicDBList policyDbList = new BasicDBList();
    final BasicDBList policyItemDbList = policyItemsToDbList(policy.getPolicyItems());

    final BasicDBObject policyObj =
        new BasicDBObject()
            .append(Policy.FieldDefs.ID, policy.getId())
            .append(Policy.FieldDefs.POLICY_ITEMS, policyItemDbList);
    policyDbList.add(policyObj);
    return policyDbList;
  }

  static BasicDBObject policyItemToDB(final PolicyItem pPolicyItem) {
    if (pPolicyItem == null) {
      return null;
    }
    return new BasicDBObject()
        .append(PolicyItem.FieldDefs.ID, pPolicyItem.getId())
        .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, pPolicyItem.getFrequencyInterval())
        .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, pPolicyItem.getFrequencyType().name())
        .append(
            PolicyItem.FieldDefs.RETENTION_IN_MILLIS,
            pPolicyItem.getRetention().toMillis()) // conversion to millis
        .append(PolicyItem.FieldDefs.RETENTION_UNIT, pPolicyItem.getRetentionUnit().name())
        .append(PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE, pPolicyItem.getNextSnapshotDate());
  }

  private static BasicDBObject createDefaultPolicyItem(final PolicyItem pPolicyItem) {
    return new BasicDBObject()
        .append(PolicyItem.FieldDefs.ID, new ObjectId())
        .append(PolicyItem.FieldDefs.FREQUENCY_INTERVAL, pPolicyItem.getFrequencyInterval())
        .append(PolicyItem.FieldDefs.FREQUENCY_TYPE, pPolicyItem.getFrequencyType().name())
        .append(
            PolicyItem.FieldDefs.RETENTION_IN_MILLIS,
            pPolicyItem.getRetention().toMillis()) // conversion to millis
        .append(PolicyItem.FieldDefs.RETENTION_UNIT, pPolicyItem.getRetentionUnit().name())
        .append(
            PolicyItem.FieldDefs.NEXT_SNAPSHOT_DATE,
            new Date()); // next snapshot date is set to now
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.DELETE)
  public void remove(final ObjectId pBackupJobId) {
    final DBObject query = new BasicDBObject().append(BaseDao.ID, pBackupJobId);
    removeMajority(query);
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<BackupJob> find(final ObjectId pProjectId, final String pClusterName) {
    final DBObject query =
        new BasicDBObject().append(PROJECT_ID, pProjectId).append(CLUSTER_NAME, pClusterName);
    final BasicDBObject doc = findOne(query);
    return doc == null ? Optional.empty() : Optional.of(toJob(doc));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<BackupJob> findByClusterUniqueId(
      final ObjectId pProjectId, final ObjectId pClusterUniqueId) {
    final DBObject query =
        new BasicDBObject()
            .append(PROJECT_ID, pProjectId)
            .append(CLUSTER_UNIQUE_ID, pClusterUniqueId);
    final BasicDBObject doc = findOne(query);
    return doc == null ? Optional.empty() : Optional.of(toJob(doc));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<BackupJob> findActive(final ObjectId pProjectId) {
    return getDbCollection()
        .find(
            new BasicDBObject()
                .append(FieldDefs.PROJECT_ID, pProjectId)
                .append(DISK_BACKUP_STATE, DiskBackupState.ACTIVE.getValue()))
        .toArray()
        .stream()
        .map(BasicDBObject.class::cast)
        .map(pDBObject -> toJob(pDBObject))
        .collect(Collectors.toList());
  }

  private Optional<BackupJob> findActive(
      final ObjectId pProjectId, final String pClusterName, final ReadPreference pReadPreference) {
    final DBObject query =
        new BasicDBObject()
            .append(PROJECT_ID, pProjectId)
            .append(CLUSTER_NAME, pClusterName)
            .append(DISK_BACKUP_STATE, DiskBackupState.ACTIVE.getValue());
    final BasicDBObject doc = findOne(query, pReadPreference);
    return doc == null ? Optional.empty() : Optional.of(toJob(doc));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<BackupJob> findActive(final ObjectId pProjectId, final String pClusterName) {
    return findActive(pProjectId, pClusterName, ReadPreference.primary());
  }

  /**
   * This method is only to be used by services such as CpsConfSvc, CpsSliceResource for Atlas
   * dedicated cluster that can tolerate potentially stale data when reading from secondary
   *
   * @param pProjectId
   * @param pClusterName
   * @return
   */
  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<BackupJob> findActiveFromSecondary(
      final ObjectId pProjectId, final String pClusterName) {
    return findActive(pProjectId, pClusterName, DriverUtils.SECONDARY_PREFERRED_MINIMUM);
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<BackupJob> findActiveByClusterUniqueId(
      final ObjectId pProjectId, final ObjectId pClusterUniqueId) {
    final DBObject query =
        new BasicDBObject()
            .append(PROJECT_ID, pProjectId)
            .append(CLUSTER_UNIQUE_ID, pClusterUniqueId)
            .append(DISK_BACKUP_STATE, DiskBackupState.ACTIVE.getValue());
    final BasicDBObject doc = findOne(query);
    return doc == null ? Optional.empty() : Optional.of(toJob(doc));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<BackupJob> find(final ObjectId pId) {
    final BasicDBObject doc = findOne(new BasicDBObject(BaseDao.ID, pId));
    if (doc == null) {
      return Optional.empty();
    }
    return Optional.of(toJob(doc));
  }

  public List<BackupJob> findForProject(final ObjectId pProjectId) {
    return findForProject(pProjectId, null);
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<BackupJob> findForProject(
      final ObjectId pProjectId, final ReadPreference pReadPreference) {
    final ReadPreference readPreference =
        pReadPreference != null ? pReadPreference : getDbCollection().getReadPreference();
    return getDbCollection()
        .find(new BasicDBObject().append(FieldDefs.PROJECT_ID, pProjectId))
        .setReadPreference(readPreference)
        .toArray()
        .stream()
        .map(BasicDBObject.class::cast)
        .map(BackupJobDao::toJob)
        .collect(Collectors.toList());
  }

  /**
   * Get a cursor for all PIT-enabled backup jobs. At the time of writing, this is used in many CPS
   * cron jobs. E.g. cron jobs to "invalidate" (mark as ready for deletion) old oplog entries,
   * "purge" old oplog entries (i.e. delete them from object storage).
   *
   * <p>This query excludes DISAGGREGATED_STORAGE clusters because we do not need to process their
   * oplogs manually like we do for CloudProvider clusters, their oplog entries are an inherent part
   * of the storage architecture and the oplog entries' lifecycle is not managed by CPS and its cron
   * jobs.
   *
   * @return the BackupJob cursor for pit-enabled, non-DISAGGREGATED_STORAGE clusters.
   */
  public ModelCursor<BackupJob> findPitEnabledNoCursorTimeout() {
    final DBObject query =
        new BasicDBObject()
            .append(FieldDefs.PIT_ENABLED, true)
            .append(
                CLUSTER_STORAGE_SYSTEM,
                new BasicDBObject(NE, StorageSystem.DISAGGREGATED_STORAGE.name()));
    final ModelCursor<BackupJob> modelCursor =
        findModelCursor(query, pDBObject -> toJob((BasicDBObject) pDBObject));
    modelCursor.noCursorTimeoutOption(true);
    return modelCursor;
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<BackupJob> findPitEnabledForGroup(final ObjectId groupId) {
    return getDbCollection()
        .find(
            new BasicDBObject()
                .append(FieldDefs.PROJECT_ID, groupId)
                .append(FieldDefs.PIT_ENABLED, true))
        .toArray()
        .stream()
        .map(BasicDBObject.class::cast)
        .map(BackupJobDao::toJob)
        .collect(Collectors.toList());
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<BackupJob> findPitEnabledForCluster(
      final ObjectId groupId, final ObjectId clusterUniqueId) {
    final BasicDBObject obj =
        findOne(
            new BasicDBObject()
                .append(FieldDefs.PROJECT_ID, groupId)
                .append(FieldDefs.CLUSTER_UNIQUE_ID, clusterUniqueId)
                .append(FieldDefs.PIT_ENABLED, true));

    return obj == null ? Optional.empty() : Optional.of(toJob(obj));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Set<ObjectId> findPitEnabledGroups() {
    final List<?> pitEnabledGroups =
        getDbCollection()
            .distinct(PROJECT_ID, new BasicDBObject().append(FieldDefs.PIT_ENABLED, true));
    return pitEnabledGroups.stream().map(ObjectId.class::cast).collect(Collectors.toSet());
  }

  // Do not use this query for production. This query will not use an index.
  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public List<BackupJob> findPitEnabledReplicasetForOplogMigrationTool() {
    return getDbCollection()
        .find(
            new BasicDBObject()
                .append(BackupJobDao.FieldDefs.CLUSTER_TYPE, ClusterType.REPLICA_SET.getValue())
                .append(FieldDefs.PIT_ENABLED, true))
        .sort(new BasicDBObject(BaseDao.ID, 1))
        .toArray()
        .stream()
        .map(BasicDBObject.class::cast)
        .map(BackupJobDao::toJob)
        .collect(Collectors.toList());
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.FIND)
  public Optional<Date> getNextSnapshotDateForProject(
      final ObjectId pProjectId, final Date pLastPlanningDate) {
    final DBObject query =
        new BasicDBObject()
            .append(FieldDefs.PROJECT_ID, pProjectId)
            .append(FieldDefs.NEXT_SNAPSHOT_DATE, new BasicDBObject(BaseDao.GT, pLastPlanningDate))
            .append(FieldDefs.DISK_BACKUP_STATE, DiskBackupState.ACTIVE.getValue());
    try (final DBCursor cursor =
        getDbCollection()
            .find(query)
            .sort(new BasicDBObject(FieldDefs.NEXT_SNAPSHOT_DATE, 1))
            .limit(1)) {
      return cursor.hasNext()
          ? Optional.of(toJob((BasicDBObject) cursor.next()).getNextSnapshotDate())
          : Optional.empty();
    }
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public Optional<BackupJob> updatePitWindow(
      final ObjectId groupId, final ObjectId clusterUniqueId, final double pitWindowDays) {
    final DBObject query =
        new BasicDBObject().append(PROJECT_ID, groupId).append(CLUSTER_UNIQUE_ID, clusterUniqueId);
    final DBObject update =
        new BasicDBObject(SET, new BasicDBObject(RESTORE_WINDOW_DAYS, pitWindowDays));
    final DBObject doc =
        getDbCollection()
            .findAndModify(
                query,
                null,
                null,
                false,
                update,
                true, // returnNew
                false,
                getMajorityWriteConcern());
    return doc == null ? Optional.empty() : Optional.of(toJob(toBasic(doc)));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setNextSnapshotDate(final ObjectId pId, final Date pDate) {
    final DBObject query = new BasicDBObject(BaseDao.ID, pId);
    final DBObject set = new BasicDBObject(FieldDefs.NEXT_SNAPSHOT_DATE, pDate);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setReferenceTime(final ObjectId pId, final int pReferenceTimeInMins) {
    final DBObject query = new BasicDBObject(BaseDao.ID, pId);
    final DBObject set = new BasicDBObject(FieldDefs.REFERENCE_TIME_IN_MINS, pReferenceTimeInMins);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setClusterUnHealthy(final ObjectId pId) {
    final DBObject query = new BasicDBObject(BaseDao.ID, pId);
    final DBObject set = new BasicDBObject(FieldDefs.IS_CLUSTER_HEALTHY, false);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setClusterHealthy(final ObjectId pId, final Date date) {
    final DBObject query = new BasicDBObject(BaseDao.ID, pId);
    final DBObject set =
        new BasicDBObject()
            .append(FieldDefs.IS_CLUSTER_HEALTHY, true)
            .append(FieldDefs.CLUSTER_HEALTHY_DATE, date);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateRestoreJobIds(
      final ObjectId projectId, final String clusterName, final List<ObjectId> restoreJobIds) {
    final DBObject query =
        new BasicDBObject().append(PROJECT_ID, projectId).append(CLUSTER_NAME, clusterName);
    final DBObject set = new BasicDBObject().append(RESTORE_JOB_IDS, restoreJobIds);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean updateOplogStatus(
      final ObjectId jid, final String rsId, final CpsOplogId cpsOplogId) {
    final DBObject query = new BasicDBObject(BaseDao.ID, jid);
    final BasicDBObject newOplogStatusObj = toDbObj(cpsOplogId);
    final BasicDBObject set =
        new BasicDBObject().append(joinFields(OPLOG_STATUS, rsId), newOplogStatusObj);

    final WriteResult result = updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
    return result.getN() == 1;
  }

  public boolean setShouldRecoverOplogsToFalse(final ObjectId jid, final String rsId) {
    final DBObject query = new BasicDBObject(BaseDao.ID, jid);
    final BasicDBObject set =
        new BasicDBObject().append(joinFields(PIT_SETTINGS, rsId, SHOULD_RECOVER_OPLOGS), false);

    final WriteResult result = updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
    return result.getN() == 1;
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean clearOplogStatusAndSetNextGenStartTs(
      final ObjectId jId, final String rsId, final CpsOplogId cpsOplogId) {
    final DBObject query = new BasicDBObject(BaseDao.ID, jId);
    final BasicDBObject nextGenStartTsObj = toDbObj(cpsOplogId);
    final BasicDBObject set =
        new BasicDBObject()
            .append(joinFields(PIT_SETTINGS, rsId, NEXT_GEN_START_TS), nextGenStartTsObj)
            .append(joinFields(OPLOG_STATUS, rsId), null);
    final WriteResult result = updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
    return result.getN() == 1;
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateRollbackStatus(
      final ObjectId jobId, final String rsId, RollbackStatus rollbackStatus) {
    final DBObject query = new BasicDBObject(ID, jobId);
    final DBObject toSet =
        new BasicDBObject(joinFields(PIT_SETTINGS, rsId, ROLLBACK_STATUS), toDbObj(rollbackStatus));
    final DBObject inc = new BasicDBObject(VERSION, 1);
    updateOneMajority(query, new BasicDBObject(SET, toSet).append(INC, inc));
  }

  public void markTerminating(final ObjectId pId) {
    markDiskBackupState(pId, DiskBackupState.TERMINATING);
  }

  public void markPaused(final ObjectId pId) {
    markDiskBackupState(pId, DiskBackupState.PAUSED);
  }

  public void markRetaining(final ObjectId pId) {
    markDiskBackupState(pId, DiskBackupState.RETAINING);
  }

  public void markActive(final ObjectId pId) {
    markDiskBackupState(pId, DiskBackupState.ACTIVE);
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  private void markDiskBackupState(final ObjectId pId, final DiskBackupState diskBackupState) {
    final DBObject query =
        new BasicDBObject()
            .append(BaseDao.ID, pId)
            .append(DISK_BACKUP_STATE, new BasicDBObject(BaseDao.NE, diskBackupState.getValue()));
    final DBObject update =
        new BasicDBObject(
            BaseDao.SET, new BasicDBObject(DISK_BACKUP_STATE, diskBackupState.getValue()));
    updateOneMajority(query, update);
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateReplicaSetToShardedClusterJob(
      final ObjectId pProjectId, final String pClusterName) {
    final DBObject query =
        new BasicDBObject().append(PROJECT_ID, pProjectId).append(CLUSTER_NAME, pClusterName);
    final DBObject update =
        new BasicDBObject()
            .append(BaseDao.SET, new BasicDBObject(CLUSTER_TYPE, ClusterType.SHARDED.getValue()));
    updateOneMajority(query, update);
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void enableAutoExportSettings(
      final ObjectId pId, final AutoExportSettings autoExportSettings) {
    final DBObject query = new BasicDBObject(BaseDao.ID, pId);
    final DBObject set =
        new BasicDBObject()
            .append(EXPORT_SETTINGS, fromAutoExportSettings(autoExportSettings))
            .append(AUTO_EXPORT_ENABLED, true);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void disableAutoExportSettings(final ObjectId pId) {
    final DBObject query = new BasicDBObject(BaseDao.ID, pId);
    final DBObject set =
        new BasicDBObject().append(EXPORT_SETTINGS, null).append(AUTO_EXPORT_ENABLED, false);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateUseOrgAndGroupNamesInExportPrefix(
      final ObjectId pId, final boolean updateUseOrgAndGroupNamesInExportPrefix) {
    final DBObject query = new BasicDBObject(BaseDao.ID, pId);
    final DBObject set =
        new BasicDBObject()
            .append(
                USE_ORG_AND_GROUP_NAMES_IN_EXPORT_PREFIX, updateUseOrgAndGroupNamesInExportPrefix);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  /**
   * Makes sure that nextSnapshotDate on top level is always the earliest nextSnapshotDate among the
   * policyItems
   */
  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updatePolicyItems(
      final ObjectId pId, final ObjectId pPolicyId, final List<PolicyItem> pPolicyItems) {

    final Date minNextSnapshotDate =
        pPolicyItems.stream()
            .min(Comparator.comparing(PolicyItem::getNextSnapshotDate))
            .map(PolicyItem::getNextSnapshotDate)
            .orElse(null);

    final DBObject query =
        new BasicDBObject(BaseDao.ID, pId)
            .append(joinFields(FieldDefs.POLICIES, BaseDao.ID), pPolicyId);

    final BasicDBList policyItemsList = new BasicDBList();
    pPolicyItems.forEach(item -> policyItemsList.add(item.toDBObject()));
    final DBObject set =
        new BasicDBObject(
                joinFields(FieldDefs.POLICIES, "$", Policy.FieldDefs.POLICY_ITEMS), policyItemsList)
            .append(FieldDefs.NEXT_SNAPSHOT_DATE, minNextSnapshotDate);

    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void enablePitIfDisabled(final ObjectId jobId) {
    final DBObject query =
        new BasicDBObject(BaseDao.ID, jobId).append(PIT_ENABLED, new BasicDBObject(NE, true));
    final DBObject set =
        new BasicDBObject(PIT_ENABLED, true).append(SLICE_SCHEMA_MIRGRATION_COMPLETED, true);
    final DBObject inc = new BasicDBObject(VERSION, 1);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set).append(INC, inc));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateRetainBackups(final ObjectId jobId, final boolean shouldRetainBackups) {
    final DBObject query = new BasicDBObject(BaseDao.ID, jobId);
    final DBObject set = new BasicDBObject(RETAIN_BACKUPS, shouldRetainBackups);
    final DBObject inc = new BasicDBObject(VERSION, 1);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set).append(INC, inc));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void disablePit(final ObjectId pId, boolean updateCopySettingsToo) {
    final DBObject query = new BasicDBObject(BaseDao.ID, pId);
    final BasicDBObject set =
        new BasicDBObject()
            .append(FieldDefs.PIT_ENABLED, false)
            .append(FieldDefs.PIT_SETTINGS, null);

    if (updateCopySettingsToo) {
      // these array updates fail if the field doesn't exist
      set.append(
          joinFields(FieldDefs.COPY_SETTINGS, "$[]", CopySettingField.SHOULD_COPY_OPLOGS), false);
    }

    final DBObject inc = new BasicDBObject(VERSION, 1);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set).append(INC, inc));
  }

  /**
   * Returns a boolean on if the update was successful or not.
   *
   * @param backupJobId id of the backup job
   * @param pitSettings mappinng of rsId to the PitSetting * @param pitSettings mappinng of rsId to
   *     the PitSetting
   * @param version version of the backup job at the time of fetching it from the db
   * @return a boolean of whether a successful update happened or not
   */
  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public boolean updatePitSettings(
      final ObjectId backupJobId, final Map<String, PitSetting> pitSettings, final long version) {
    final DBObject query = new BasicDBObject(BaseDao.ID, backupJobId).append(VERSION, version);
    final DBObject set = new BasicDBObject(PIT_SETTINGS, toDbObj(pitSettings));
    final DBObject inc = new BasicDBObject(VERSION, 1);
    return updateOneMajority(query, new BasicDBObject(BaseDao.SET, set).append(INC, inc)).getN()
        != 0;
  }

  private static PitSetting toPitSetting(final BasicDBObject obj) {
    if (obj == null) {
      return null;
    }

    return PitSetting.builder()
        .oplogStatus(toOplogId((BasicDBObject) obj.get(PitSettingFields.OPLOG_STATUS)))
        .nextGenStartTs(toOplogId((BasicDBObject) obj.get(NEXT_GEN_START_TS)))
        .rollbackStatus(toRollbackStatus((BasicDBObject) obj.get(ROLLBACK_STATUS)))
        .regionalMetadataStoreConfigId(obj.getString(REGIONAL_METADATA_STORE_CONFIG_ID))
        .blobStoreConfigId(obj.getString(BLOB_STORE_CONFIG_ID))
        .regionName(obj.getString(REGION_NAME))
        .lastContiguousCheckEndTimestamp(
            (BSONTimestamp) obj.get(LAST_CONTIGUOUS_CHECK_END_TIMESTAMP))
        .keyCiphertext(obj.getString(KEY_CIPHERTEXT))
        .keyCiphertextHash(obj.getString(KEY_CIPHERTEXT_HASH))
        .lastRotatedDate(obj.getDate(LAST_ROTATED_DATE))
        .lastValidatedDate(obj.getDate(LAST_VALIDATED_DATE))
        .cmk(obj.getString(CMK))
        .versionedCmk(obj.getString(VERSIONED_CMK))
        .oplogMigration(toOplogMigration((BasicDBObject) obj.get(OPLOG_MIGRATION)))
        .copyStorages(toPitStorageList((BasicDBList) obj.get(COPY_STORAGES)))
        .copyStoragesMigration(toPitStorageList((BasicDBList) obj.get(COPY_STORAGES_MIGRATION)))
        .shouldRecoverOplogs(obj.getBoolean(SHOULD_RECOVER_OPLOGS, false))
        .build();
  }

  private static Map<String, PitSetting> toPitSettings(final BasicDBObject obj) {
    final Map<String, PitSetting> pitSettings = new HashMap<>();
    if (obj == null) {
      return pitSettings;
    }
    obj.forEach(
        (key, value) -> {
          pitSettings.put(key, toPitSetting((BasicDBObject) value));
        });
    return pitSettings;
  }

  private static Map<String, CpsOplogId> toOplogStatus(final BasicDBObject obj) {
    final Map<String, CpsOplogId> oplogStatus = new HashMap<>();
    if (obj == null) {
      return oplogStatus;
    }

    obj.forEach(
        (key, value) -> {
          oplogStatus.put(key, toOplogId((BasicDBObject) value));
        });
    return oplogStatus;
  }

  private static AutoExportSettings toAutoExportSettings(final BasicDBObject obj) {
    if (obj == null) {
      return null;
    }
    return new AutoExportSettings(
        BackupFrequencyType.fromValue(obj.getString(FREQUENCY_TYPE)), obj.getObjectId(BUCKET_ID));
  }

  private static List<ExtraRetentionSetting> toExtraRetentionSettings(final BasicDBList objList) {
    if (objList == null) {
      return null;
    }
    return objList.stream()
        .map(obj -> toExtraRetentionSetting((BasicDBObject) obj))
        .collect(Collectors.toList());
  }

  private static Set<String> toPreferredSnapshotVolumes(final BasicDBList objList) {
    if (objList == null) {
      return null;
    }

    return objList.stream().map(object -> (String) object).collect(Collectors.toSet());
  }

  private static ExtraRetentionSetting toExtraRetentionSetting(final BasicDBObject obj) {
    if (obj == null) {
      return null;
    }
    return new ExtraRetentionSetting(
        obj.getInt(RETENTION_DAYS), BackupFrequencyType.valueOf(obj.getString(FREQUENCY_TYPE)));
  }

  private static List<CopySetting> toCopySettings(final BasicDBList objList) {
    if (objList == null) {
      return new ArrayList<>();
    }
    return objList.stream()
        .map(obj -> toCopySetting((BasicDBObject) obj))
        .collect(Collectors.toList());
  }

  private static CopySetting toCopySetting(final BasicDBObject obj) {
    if (obj == null) {
      return null;
    }
    final CloudProvider cloudProvider =
        CloudProvider.valueOf(obj.getString(CopySettingField.CLOUD_PROVIDER));

    return obj.get(CopySettingField.ZONE_ID) != null
        ? new CopySetting(
            cloudProvider,
            RegionNameHelper.findByValue(cloudProvider, obj.getString(CopySettingField.REGION_NAME))
                .get(),
            obj.getObjectId(CopySettingField.REPLICATION_SPEC_ID),
            obj.getObjectId(CopySettingField.ZONE_ID),
            obj.getBoolean(CopySettingField.SHOULD_COPY_OPLOGS),
            ((BasicDBList) obj.get(CopySettingField.FREQUENCIES))
                .stream()
                    .map(freq -> BackupFrequencyType.valueOf((String) freq))
                    .collect(Collectors.toList()))
        : new CopySetting(
            cloudProvider,
            RegionNameHelper.findByValue(cloudProvider, obj.getString(CopySettingField.REGION_NAME))
                .get(),
            obj.getObjectId(CopySettingField.REPLICATION_SPEC_ID),
            obj.getObjectId(CopySettingField.ZONE_ID),
            obj.getBoolean(CopySettingField.SHOULD_COPY_OPLOGS),
            ((BasicDBList) obj.get(CopySettingField.FREQUENCIES))
                .stream()
                    .map(freq -> BackupFrequencyType.valueOf((String) freq))
                    .collect(Collectors.toList()));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setCopySettings(final ObjectId backupJobId, final List<CopySetting> copySettings) {
    final DBObject query = new BasicDBObject(BaseDao.ID, backupJobId);

    final BasicDBList copySettingsList = new BasicDBList();
    copySettings.forEach(cs -> copySettingsList.add(cs.toDBObject()));
    final DBObject set = new BasicDBObject(FieldDefs.COPY_SETTINGS, copySettingsList);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setLastCheckedContiguousOplogTimestamps(
      final BackupJob backupJob, final String rsId, final BSONTimestamp end) {
    final String pitSetting = joinFields(FieldDefs.PIT_SETTINGS, rsId);
    final DBObject query =
        new BasicDBObject(BaseDao.ID, backupJob.getId())
            .append(pitSetting, new BasicDBObject().append(BaseDao.NE, null));
    final DBObject set =
        new BasicDBObject()
            .append(joinFields(pitSetting, LAST_CONTIGUOUS_CHECK_END_TIMESTAMP), end);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void updateLastValidatedDate(final BackupJob backupJob, final String rsId) {
    final DBObject query =
        new BasicDBObject(BaseDao.ID, backupJob.getId())
            .append(PIT_SETTINGS, new BasicDBObject().append(BaseDao.NE, null));
    final String pitSetting = joinFields(FieldDefs.PIT_SETTINGS, rsId);
    final DBObject set =
        new BasicDBObject().append(joinFields(pitSetting, LAST_VALIDATED_DATE), new Date());
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set));
  }

  private static DBObject toDbObj(final Map<String, PitSetting> pitSettings) {
    if (pitSettings == null) {
      return null;
    }

    final BasicDBObject ret = new BasicDBObject();
    pitSettings.forEach(
        (k, v) -> {
          ret.append(k, toDbObj(v));
        });
    return ret;
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void setExtraRetentionSettings(
      final ObjectId backupJobId, final List<ExtraRetentionSetting> extraRetentionSettings) {
    final DBObject query = new BasicDBObject(BaseDao.ID, backupJobId);
    final DBObject set =
        new BasicDBObject(
            EXTRA_RETENTION_SETTINGS,
            extraRetentionSettings.stream()
                .map(ExtraRetentionSetting::toDBObject)
                .collect(Collectors.toList()));
    final DBObject inc = new BasicDBObject(VERSION, 1);
    updateOneMajority(query, new BasicDBObject(BaseDao.SET, set).append(INC, inc));
  }

  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.UPDATE)
  public void deleteExtraRetention(final ObjectId pGroupId) {
    final DBObject query =
        new BasicDBObject(PROJECT_ID, pGroupId)
            .append(EXTRA_RETENTION_SETTINGS, new BasicDBObject().append(BaseDao.NE, null));

    final BasicDBObject set = new BasicDBObject().append(EXTRA_RETENTION_SETTINGS, null);

    final DBObject inc = new BasicDBObject(VERSION, 1);
    updateAllMajority(query, new BasicDBObject(BaseDao.SET, set).append(INC, inc));
  }

  private static DBObject oplogStatusToDbObj(final Map<String, CpsOplogId> oplogStatus) {
    if (oplogStatus == null) {
      return new BasicDBObject();
    }

    final BasicDBObject ret = new BasicDBObject();
    oplogStatus.forEach(
        (k, v) -> {
          ret.append(k, toDbObj(v));
        });
    return ret;
  }

  private static DBObject toDbObj(final PitSetting p) {
    return new BasicDBObject()
        .append(ROLLBACK_STATUS, toDbObj(p.getRollbackStatus()))
        .append(PitSettingFields.OPLOG_STATUS, toDbObj(p.getOplogId()))
        .append(NEXT_GEN_START_TS, toDbObj(p.getNextGenStartTs()))
        .append(REGIONAL_METADATA_STORE_CONFIG_ID, p.getRegionalMetadataStoreConfigId())
        .append(BLOB_STORE_CONFIG_ID, p.getBlobStoreId())
        .append(REGION_NAME, p.getRegionName())
        .append(LAST_CONTIGUOUS_CHECK_END_TIMESTAMP, p.getLastContiguousCheckEndTimestamp())
        .append(KEY_CIPHERTEXT, p.getKeyCiphertext())
        .append(KEY_CIPHERTEXT_HASH, p.getKeyCiphertextHash())
        .append(LAST_ROTATED_DATE, p.getLastRotatedDate())
        .append(LAST_VALIDATED_DATE, p.getLastValidatedDate())
        .append(OPLOG_MIGRATION, toDbObj(p.getOplogMigration()))
        .append(COPY_STORAGES, pitStorageListToDbObj(p.getCopyStorages()))
        .append(COPY_STORAGES_MIGRATION, pitStorageListToDbObj(p.getCopyStoragesMigration()))
        .append(CMK, p.getCmk())
        .append(VERSIONED_CMK, p.getVersionedCmk())
        .append(SHOULD_RECOVER_OPLOGS, p.isShouldRecoverOplogs());
  }

  private static DBObject toDbObj(final OplogMigration o) {
    if (o == null) {
      return null;
    }
    return new BasicDBObject()
        .append(STORAGE, pitStorageListToDbObj(o.getStorage()))
        .append(START_DATE, o.getStartDate());
  }

  private static DBObject pitStorageListToDbObj(final List<PitStorage> storages) {
    final BasicDBList storageList = new BasicDBList();
    for (final PitStorage storageItem : storages) {
      final BasicDBObject newItem =
          new BasicDBObject()
              .append(
                  PitStorageFields.STORAGEMETADATA_STORE_CONFIG_ID,
                  storageItem.getMetadataStoreConfigId())
              .append(PitStorageFields.BLOB_STORE_CONFIG_ID, storageItem.getBlobStoreConfigId())
              .append(
                  PitStorageFields.BLOB_STORE_REGION_NAME, storageItem.getBlobStoreRegionName());
      storageList.add(newItem);
    }
    return storageList;
  }

  private static DBObject fromAutoExportSettings(final AutoExportSettings e) {
    if (e == null) {
      return null;
    }
    final BasicDBObject basicDBObject =
        new BasicDBObject()
            .append(FREQUENCY_TYPE, e.getFrequency().getValue())
            .append(BUCKET_ID, e.getExportBucketId());

    return basicDBObject;
  }

  static BackupJob toJob(final BasicDBObject obj) {
    final BasicDBList dbRestoreJobIds =
        obj.get(RESTORE_JOB_IDS) != null
            ? (BasicDBList) obj.get(RESTORE_JOB_IDS)
            : new BasicDBList();

    return BackupJob.builder()
        .id(obj.getObjectId(ID))
        .projectId(obj.getObjectId(PROJECT_ID))
        .clusterName(obj.getString(CLUSTER_NAME))
        .clusterUniqueId(obj.getObjectId(CLUSTER_UNIQUE_ID))
        .clusterType(ClusterType.fromValue(obj.getString(CLUSTER_TYPE)))
        .clusterStorageSystem(
            StorageSystem.filter(obj.getString(CLUSTER_STORAGE_SYSTEM, StorageSystem.LOCAL.name())))
        .diskBackupState(DiskBackupState.fromValue(obj.getString(DISK_BACKUP_STATE)))
        .referenceTimeInMins(obj.getInt(REFERENCE_TIME_IN_MINS))
        .nextSnapshotDate(obj.getDate(NEXT_SNAPSHOT_DATE))
        .restoreWindowDays(obj.getDouble(RESTORE_WINDOW_DAYS))
        .policies(
            ((BasicDBList) obj.get(POLICIES))
                .stream().map(doc -> new Policy((BasicDBObject) doc)).collect(Collectors.toList()))
        .runtimeOverrides((DBObject) obj.get(RUNTIME_OVERRIDES))
        .pitEnabled(obj.getBoolean(PIT_ENABLED))
        .sliceSchemaMigrationCompleted(
            getBooleanWithNullAllowed(obj, SLICE_SCHEMA_MIRGRATION_COMPLETED))
        .version(obj.getInt(VERSION))
        .pitSettings(toPitSettings((BasicDBObject) obj.get(PIT_SETTINGS)))
        .oplogStatus(toOplogStatus((BasicDBObject) obj.get(OPLOG_STATUS)))
        .autoExportEnabled(obj.getBoolean(AUTO_EXPORT_ENABLED))
        .autoExportSettings(toAutoExportSettings((BasicDBObject) obj.get(EXPORT_SETTINGS)))
        .useOrgAndGroupNamesInExportPrefix(
            obj.getBoolean(USE_ORG_AND_GROUP_NAMES_IN_EXPORT_PREFIX, false))
        .clusterHealthyDate(obj.getDate(CLUSTER_HEALTHY_DATE))
        .isClusterHealthy(obj.getBoolean(IS_CLUSTER_HEALTHY))
        .retainBackups(obj.getBoolean(RETAIN_BACKUPS))
        .copySettings(toCopySettings((BasicDBList) obj.get(COPY_SETTINGS)))
        .extraRetentionSettings(
            toExtraRetentionSettings((BasicDBList) obj.get(EXTRA_RETENTION_SETTINGS)))
        .preferredSnapshotVolumes(
            toPreferredSnapshotVolumes((BasicDBList) obj.get(PREFERRED_SNAPSHOT_VOLUMES)))
        .restoreJobIds(
            dbRestoreJobIds.stream().map(ObjectId.class::cast).collect(Collectors.toList()))
        .build();
  }

  // used for testing
  @PromMethodMetrics(
      name = DURATION_METRIC_NAME,
      help = DURATION_METRIC_DESCRIPTION,
      labelNames = DaoOpType.LABEL,
      labelValues = DaoOperation.CREATE)
  public void insertForTests(final BackupJob pBackupJob) {
    DBObject dbObj = BackupJobDao.toDbObj(pBackupJob);
    saveMajority(dbObj);
  }

  private static Boolean getBooleanWithNullAllowed(final BasicDBObject obj, final String field) {
    Object value = obj.get(field);
    return value != null ? (Boolean) value : null;
  }

  public static class FieldDefs {
    public static final String ID = "_id";
    public static final String PROJECT_ID = "projectId";
    public static final String CLUSTER_NAME = "clusterName";
    public static final String CLUSTER_UNIQUE_ID = "clusterUniqueId";
    public static final String CLUSTER_TYPE = "clusterType";
    public static final String CLUSTER_STORAGE_SYSTEM = "clusterStorageSystem";
    public static final String PROVIDERS = "providers";
    public static final String DISK_BACKUP_STATE = "diskBackupState";
    public static final String REFERENCE_TIME_IN_MINS = "referenceTimeInMins";
    public static final String NEXT_SNAPSHOT_DATE = "nextSnapshotDate";
    public static final String RESTORE_WINDOW_DAYS = "restoreWindowDays";
    public static final String POLICIES = "policies";
    public static final String EXTRA_RETENTION_SETTINGS = "extraRetentionSettings";
    public static final String NEW_REGION_MAPPING_ENABLED = "newRegionMappingEnabled";
    public static final String COPY_SETTINGS = "copySettings";

    public static final String PIT_SETTINGS = "pitSettings";

    public static final String OPLOG_STATUS = "oplogStatus";
    public static final String RUNTIME_OVERRIDES = "runtimeOverrides";
    public static final String RETAIN_BACKUPS = "retainBackups";
    public static final String PIT_ENABLED = "pitEnabled";
    public static final String SLICE_SCHEMA_MIRGRATION_COMPLETED = "sliceSchemaMigrationCompleted";
    public static final String VERSION = "version";

    public static final String EXPORT_SETTINGS = "exportSettings";
    public static final String AUTO_EXPORT_ENABLED = "autoExportEnabled";
    public static final String USE_ORG_AND_GROUP_NAMES_IN_EXPORT_PREFIX =
        "useOrgAndGroupNamesInExportPrefix";

    public static final String RESTORE_JOB_IDS = "restoreJobIds";

    public static final String IS_CLUSTER_HEALTHY = "isClusterHealthy";
    public static final String CLUSTER_HEALTHY_DATE = "clusterHealthyDate";

    /*
     Fields under runtimeOverrides
    */
    public static final String TAIL_MIN_SLICE_SIZE_BYTES_OVERRIDE = "tailMinSliceSizeBytes";
    public static final String TAIL_MAX_SLICE_SIZE_BYTES_OVERRIDE = "tailMaxSliceSizeBytes";
    public static final String TAIL_SLICE_REQUEST_QUEUE_PARALLELISM_OVERRIDE =
        "tailSliceRequestQueueParallelism";
    public static final String TAIL_SLICE_BUFFER_LENGTH_OVERRIDE = "tailSliceBufferLength";
    public static final String TAIL_SLICING_TIMEOUT_SECS_OVERRIDE = "tailSlicingTimeoutSecs";
    public static final String TAIL_READER_BUFFER_LENGTH_OVERRIDE = "tailReaderBufferLength";
    public static final String TAIL_MAX_AWAIT_TIME_SECONDS_OVERRIDE = "tailMaxAwaitTimeSeconds";
    public static final String STREAMING_RESTORE_IOPS_OVERRIDE = "streamingRestoreIops";
    public static final String EXPORT_IOPS_OVERRIDE = "exportIops";
    public static final String AWS_EXPORT_VM_INSTANCE_SIZE = "awsExportVmInstanceSize";
    public static final String AZURE_EXPORT_VM_INSTANCE_SIZE = "azureExportVmInstanceSize";
    public static final String GCP_EXPORT_VM_INSTANCE_SIZE = "gcpExportVmInstanceSize";
    public static final String PREFERRED_SNAPSHOT_VOLUMES = "preferredSnapshotVolumes";
  }

  public static class RollbackFields {
    public static final String STATE = "state";
    public static final String COMMON_POINT_TS = "commonPointTs";
    public static final String COMMON_POINT_HASH = "commonPointHash";
    public static final String COMMON_POINT_TERM = "commonPointTerm";
  }

  public static class OplogFields {
    public static final String LAST_TS = "lastTs";
    public static final String LAST_HASH = "lastHash";
    public static final String LAST_TERM = "lastTerm";
  }

  public static class ExtraRetentionSettingFields {
    public static final String RETENTION_DAYS = "retentionDays";
    public static final String FREQUENCY_TYPE = "frequencyType";
  }

  public static class PitSettingFields {
    public static final String REGIONAL_METADATA_STORE_CONFIG_ID = "regionalMetadataStoreConfigId";
    public static final String BLOB_STORE_CONFIG_ID = "blobStoreConfigId";
    public static final String REGION_NAME = "regionName";
    // We no longer write new data to this oplogStatus field.
    public static final String OPLOG_STATUS = "oplogStatus";
    public static final String NEXT_GEN_START_TS = "nextGenStartTs";
    public static final String ROLLBACK_STATUS = "rollbackStatus";
    public static final String LAST_CONTIGUOUS_CHECK_END_TIMESTAMP =
        "lastContiguousCheckEndTimestamp";
    public static final String KEY_CIPHERTEXT = "keyCipherText";
    public static final String KEY_CIPHERTEXT_HASH = "keyCipherTextHash";
    public static final String LAST_ROTATED_DATE = "lastRotatedDate";
    public static final String LAST_VALIDATED_DATE = "lastValidatedDate";
    public static final String CMK = "cmk";
    public static final String VERSIONED_CMK = "versionedCmk";
    public static final String OPLOG_MIGRATION = "oplogMigration";
    public static final String COPY_STORAGES = "copyStorages";
    public static final String COPY_STORAGES_MIGRATION = "copyStoragesMigration";
    public static final String SHOULD_RECOVER_OPLOGS = "shouldRecoverOplogs";
  }

  public static class CopySettingField {
    public static final String CLOUD_PROVIDER = "cloudProvider";
    public static final String REGION_NAME = "regionName";
    public static final String REPLICATION_SPEC_ID = "replicationSpecId";
    public static final String ZONE_ID = "zoneId";
    public static final String SHOULD_COPY_OPLOGS = "shouldCopyOplogs";
    public static final String FREQUENCIES = "frequencies";
  }

  public static class OplogMigrationFields {
    public static final String STORAGE = "storage";
    public static final String START_DATE = "startDate";
  }

  public static class PitStorageFields {
    public static final String STORAGEMETADATA_STORE_CONFIG_ID = "metadataStoreConfigId";
    public static final String BLOB_STORE_CONFIG_ID = "blobStoreConfigId";
    public static final String BLOB_STORE_REGION_NAME = "blobStoreRegionName";
  }

  public static class AutoExportSettingsFields {
    public static final String FREQUENCY_TYPE = "frequencyType";
    public static final String BUCKET_ID = "bucketId";
  }
}
