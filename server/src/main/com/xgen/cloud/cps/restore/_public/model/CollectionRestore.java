package com.xgen.cloud.cps.restore._public.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mongodb.BasicDBObject;
import java.util.Date;

@JsonIgnoreProperties(ignoreUnknown = true)
public class CollectionRestore {
  @JsonProperty(FieldDefs.COLLECTION_RESTORE_REQUEST)
  private final CollectionRestoreRequest collectionRestoreRequest;

  // Total document number of all supported collections
  @JsonProperty(FieldDefs.TOTAL_DOC_NUM)
  private long totalDocNum;

  @JsonProperty(FieldDefs.RESTORED_DOC_NUM)
  private long restoredDocNum;

  // Total collection number of all valid collections
  @JsonProperty(FieldDefs.TOTAL_VALID_COLLECTION_NUM)
  private int totalValidCollectionNum;

  // Total size in bytes of all supported collections
  @JsonProperty(FieldDefs.TOTAL_SIZE_BYTES)
  private long totalSizeBytes;

  @JsonProperty(FieldDefs.UNSUPPORTED_COLLECTION_NUM)
  private int unsupportedCollectionNum;

  @JsonProperty(FieldDefs.NOT_FOUND_COLLECTION_NUM)
  private int notFoundCollectionNum;

  @JsonProperty(FieldDefs.NOT_FOUND_DATABASE_NUM)
  private int notFoundDatabaseNum;

  @JsonProperty(FieldDefs.RESTORED_COLLECTION_NUM)
  private int restoredCollectionNum;

  @JsonProperty(FieldDefs.FAILED_COLLECTION_NUM)
  private int failedCollectionNum;

  @JsonProperty(FieldDefs.OVERALL_RESTORE_STATE)
  private OverallState overallRestoreState;

  @JsonProperty(FieldDefs.LAST_UPDATED_DATE)
  private Date lastUpdatedDate;

  @JsonProperty(FieldDefs.COLLECTION_RESTORE_METRICS)
  private final CollectionRestoreMetrics collectionRestoreMetrics;

  @JsonProperty(FieldDefs.CLEANUP_START_DATE)
  private Date cleanupStartDate;

  @JsonProperty(FieldDefs.CLEANUP_END_DATE)
  private Date cleanupEndDate;

  public CollectionRestore(final CollectionRestoreRequest collectionRestoreRequest) {
    this.collectionRestoreRequest = collectionRestoreRequest;
    this.overallRestoreState = OverallState.INITIALIZING;
    collectionRestoreMetrics = new CollectionRestoreMetrics();
  }

  public CollectionRestoreRequest getCollectionRestoreRequest() {
    return collectionRestoreRequest;
  }

  public long getTotalDocNum() {
    return totalDocNum;
  }

  public long getRestoredDocNum() {
    return restoredDocNum;
  }

  public int getTotalValidCollectionNum() {
    return totalValidCollectionNum;
  }

  public long getTotalSizeBytes() {
    return totalSizeBytes;
  }

  public int getUnsupportedCollectionNum() {
    return unsupportedCollectionNum;
  }

  public int getNotFoundCollectionNum() {
    return notFoundCollectionNum;
  }

  public int getNotFoundDatabaseNum() {
    return notFoundDatabaseNum;
  }

  public int getFailedCollectionNum() {
    return failedCollectionNum;
  }

  public int getRestoredCollectionNum() {
    return restoredCollectionNum;
  }

  public OverallState getOverallRestoreState() {
    return overallRestoreState;
  }

  public Date getLastUpdatedDate() {
    return lastUpdatedDate;
  }

  public Date getCleanupStartDate() {
    return cleanupStartDate;
  }

  public Date getCleanupEndDate() {
    return cleanupEndDate;
  }

  /** Drop temp collections if the restore is failed or canceled and cleanup has not started */
  public boolean shouldDropTempCollections() {
    return cleanupStartDate == null && (isFailed() || isCanceled());
  }

  /**
   * Temporary collections needs to be cleaned up, either by renaming them while finalizing the
   * restore or by dropping them if the restore is failed or canceled
   */
  public boolean needsCleanup() {
    return cleanupStartDate == null
        && (overallRestoreState == OverallState.FINALIZING || isFailed() || isCanceled());
  }

  public CollectionRestoreMetrics getCollectionRestoreMetrics() {
    return collectionRestoreMetrics;
  }

  public CollectionRestore setTotalDocNum(long totalDocNum) {
    this.totalDocNum = totalDocNum;
    return this;
  }

  public CollectionRestore setRestoredDocNum(long restoredDocNum) {
    this.restoredDocNum = restoredDocNum;
    return this;
  }

  public CollectionRestore setTotalValidCollectionNum(int totalValidCollectionNum) {
    this.totalValidCollectionNum = totalValidCollectionNum;
    return this;
  }

  public CollectionRestore setTotalSizeBytes(long totalSizeBytes) {
    this.totalSizeBytes = totalSizeBytes;
    return this;
  }

  public CollectionRestore setUnsupportedCollectionNum(int unsupportedCollectionNum) {
    this.unsupportedCollectionNum = unsupportedCollectionNum;
    return this;
  }

  public CollectionRestore setNotfoundCollectionNum(int notFoundCollectionNum) {
    this.notFoundCollectionNum = notFoundCollectionNum;
    return this;
  }

  public CollectionRestore setNotfoundDatabaseNum(int notFoundDatabaseNum) {
    this.notFoundDatabaseNum = notFoundDatabaseNum;
    return this;
  }

  public CollectionRestore setFailedCollectionNum(int failedCollectionNum) {
    this.failedCollectionNum = failedCollectionNum;
    return this;
  }

  public CollectionRestore setRestoredCollectionNum(int restoredCollectionNum) {
    this.restoredCollectionNum = restoredCollectionNum;
    return this;
  }

  public CollectionRestore setOverallRestoreState(OverallState overallRestoreState) {
    this.overallRestoreState = overallRestoreState;
    return this;
  }

  public CollectionRestore setLastUpdatedDate(Date lastUpdatedDate) {
    this.lastUpdatedDate = lastUpdatedDate;
    return this;
  }

  public CollectionRestore setCleanupStartDate(Date cleanupStartDate) {
    this.cleanupStartDate = cleanupStartDate;
    return this;
  }

  public CollectionRestore setCleanupEndDate(Date cleanupEndDate) {
    this.cleanupEndDate = cleanupEndDate;
    return this;
  }

  public CollectionRestore(BasicDBObject dbObject) {
    if (dbObject != null) {
      this.collectionRestoreRequest =
          new CollectionRestoreRequest(
              (BasicDBObject) dbObject.get(FieldDefs.COLLECTION_RESTORE_REQUEST));
      this.totalDocNum = dbObject.getLong(FieldDefs.TOTAL_DOC_NUM, 0);
      this.restoredDocNum = dbObject.getLong(FieldDefs.RESTORED_DOC_NUM, 0);
      this.totalValidCollectionNum = dbObject.getInt(FieldDefs.TOTAL_VALID_COLLECTION_NUM, 0);
      this.totalSizeBytes = dbObject.getLong(FieldDefs.TOTAL_SIZE_BYTES, 0);
      this.unsupportedCollectionNum = dbObject.getInt(FieldDefs.UNSUPPORTED_COLLECTION_NUM, 0);
      this.notFoundCollectionNum = dbObject.getInt(FieldDefs.NOT_FOUND_COLLECTION_NUM, 0);
      this.notFoundDatabaseNum = dbObject.getInt(FieldDefs.NOT_FOUND_DATABASE_NUM, 0);
      this.restoredCollectionNum = dbObject.getInt(FieldDefs.RESTORED_COLLECTION_NUM, 0);
      this.failedCollectionNum = dbObject.getInt(FieldDefs.FAILED_COLLECTION_NUM, 0);
      this.overallRestoreState =
          OverallState.valueOf(
              dbObject.getString(
                  FieldDefs.OVERALL_RESTORE_STATE, OverallState.INITIALIZING.name()));
      this.lastUpdatedDate = dbObject.getDate(FieldDefs.LAST_UPDATED_DATE, null);
      this.collectionRestoreMetrics =
          new CollectionRestoreMetrics(
              (BasicDBObject) dbObject.get(FieldDefs.COLLECTION_RESTORE_METRICS));
      this.cleanupStartDate = dbObject.getDate(FieldDefs.CLEANUP_START_DATE);
      this.cleanupEndDate = dbObject.getDate(FieldDefs.CLEANUP_END_DATE);
    } else {
      this.collectionRestoreRequest = null;
      this.collectionRestoreMetrics = new CollectionRestoreMetrics();
    }
  }

  public BasicDBObject toDBObject() {
    final BasicDBObject dbObject =
        new BasicDBObject()
            .append(
                FieldDefs.COLLECTION_RESTORE_REQUEST, this.collectionRestoreRequest.toDBObject())
            .append(FieldDefs.TOTAL_DOC_NUM, this.totalDocNum)
            .append(FieldDefs.RESTORED_DOC_NUM, this.restoredDocNum)
            .append(FieldDefs.TOTAL_VALID_COLLECTION_NUM, this.totalValidCollectionNum)
            .append(FieldDefs.TOTAL_SIZE_BYTES, this.totalSizeBytes)
            .append(FieldDefs.UNSUPPORTED_COLLECTION_NUM, this.unsupportedCollectionNum)
            .append(FieldDefs.NOT_FOUND_COLLECTION_NUM, this.notFoundCollectionNum)
            .append(FieldDefs.NOT_FOUND_DATABASE_NUM, this.notFoundDatabaseNum)
            .append(FieldDefs.RESTORED_COLLECTION_NUM, this.restoredCollectionNum)
            .append(FieldDefs.FAILED_COLLECTION_NUM, this.failedCollectionNum)
            .append(FieldDefs.OVERALL_RESTORE_STATE, this.overallRestoreState);

    if (this.getLastUpdatedDate() != null) {
      dbObject.append(FieldDefs.LAST_UPDATED_DATE, this.lastUpdatedDate);
    }

    if (this.getCleanupStartDate() != null) {
      dbObject.append(FieldDefs.CLEANUP_START_DATE, this.cleanupStartDate);
    }

    if (this.getCleanupEndDate() != null) {
      dbObject.append(FieldDefs.CLEANUP_END_DATE, this.cleanupEndDate);
    }

    if (this.getCollectionRestoreRequest() != null) {
      dbObject.append(
          FieldDefs.COLLECTION_RESTORE_REQUEST, this.getCollectionRestoreRequest().toDBObject());
    }

    if (this.collectionRestoreMetrics != null) {
      dbObject.append(
          FieldDefs.COLLECTION_RESTORE_METRICS, this.collectionRestoreMetrics.toDBObject());
    }

    return dbObject;
  }

  public enum OverallState {
    INITIALIZING,
    IN_PROGRESS,
    PARTIAL_FAILED_IN_PROGRESS,
    FINALIZING, // data transfer has done, but renaming is not done yet
    SUCCESSFUL,
    CANCELED,
    PARTIAL_FAILED,
    FAILED
  }

  public boolean isInProgress() {
    return getOverallRestoreState() == OverallState.IN_PROGRESS
        || getOverallRestoreState() == OverallState.FINALIZING
        || getOverallRestoreState() == OverallState.PARTIAL_FAILED_IN_PROGRESS;
  }

  public boolean isFailed() {
    return getOverallRestoreState() == OverallState.FAILED
        || getOverallRestoreState() == OverallState.PARTIAL_FAILED;
  }

  public boolean isCanceled() {
    return getOverallRestoreState() == OverallState.CANCELED;
  }

  public boolean isFinalState() {
    return getOverallRestoreState() == OverallState.SUCCESSFUL || isFailed() || isCanceled();
  }

  public static class FieldDefs {
    public static final String COLLECTION_RESTORE_REQUEST = "collectionRestoreRequest";
    public static final String TOTAL_DOC_NUM = "totalDocNum";
    public static final String RESTORED_DOC_NUM = "restoredDocNum";
    public static final String TOTAL_VALID_COLLECTION_NUM = "totalValidCollectionNum";
    public static final String TOTAL_SIZE_BYTES = "totalSizeBytes";
    public static final String UNSUPPORTED_COLLECTION_NUM = "unsupportedCollectionNum";
    public static final String NOT_FOUND_COLLECTION_NUM = "notFoundCollectionNum";
    public static final String NOT_FOUND_DATABASE_NUM = "notFoundDatabaseNum";
    public static final String FAILED_COLLECTION_NUM = "failedCollectionNum";
    public static final String RESTORED_COLLECTION_NUM = "restoredCollectionNum";
    public static final String OVERALL_RESTORE_STATE = "overallRestoreState";
    public static final String LAST_UPDATED_DATE = "lastUpdatedDate";
    public static final String COLLECTION_RESTORE_METRICS = "collectionRestoreMetrics";
    public static final String CLEANUP_START_DATE = "cleanupStartDate";
    public static final String CLEANUP_END_DATE = "cleanupEndDate";
  }
}
