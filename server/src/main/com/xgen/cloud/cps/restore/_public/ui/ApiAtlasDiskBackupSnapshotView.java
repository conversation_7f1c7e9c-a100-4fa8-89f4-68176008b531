package com.xgen.cloud.cps.restore._public.ui;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.common.view._public.base.ApiView;
import com.xgen.cloud.common.view._public.base.LinkRelView;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.DiscriminatorMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.AccessMode;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.bson.types.ObjectId;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(
    name = "DiskBackupSnapshot",
    discriminatorProperty = ApiAtlasDiskBackupSnapshotView.TYPE,
    discriminatorMapping = {
      @DiscriminatorMapping(value = "replicaSet", schema = ApiAtlasDiskBackupReplicaSetView.class),
      @DiscriminatorMapping(
          value = "shardedCluster",
          schema = ApiAtlasDiskBackupShardedClusterSnapshotView.class)
    },
    extensions = {
      @Extension(
          name = IPA_EXCEPTION,
          properties = {
            @ExtensionProperty(
                name = "xgen-IPA-125-discriminator-must-accompany-oneOf-anyOf-allOf",
                value = "Schema predates IPA validation.")
          })
    })
public class ApiAtlasDiskBackupSnapshotView extends ApiView {

  public static final String ID_FIELD = "id";
  public static final String CREATED_AT_FIELD = "createdAt";
  public static final String STORAGE_SIZE_BYTES_FIELD = "storageSizeBytes";
  public static final String EXPIRES_AT_FIELD = "expiresAt";
  public static final String MONGOD_VERSION_FIELD = "mongodVersion";
  public static final String MASTER_KEY_UUID_FIELD = "masterKeyUUID";
  public static final String TYPE = "type";
  public static final String STATUS = "status";
  public static final String DESCRIPTION = "description";
  public static final String SNAPSHOT_TYPE = "snapshotType";
  public static final String FREQUENCY_TYPE_FIELD = "frequencyType";
  public static final String POLICY_ITEMS_FIELD = "policyItems";
  public static final String EXTRA_RETENTION_DAYS_FIELD = "extraRetentionDays";

  @JsonProperty(ID_FIELD)
  @Schema(
      name = ID_FIELD,
      description = "Unique 24-hexadecimal digit string that identifies the snapshot.",
      accessMode = Schema.AccessMode.READ_ONLY)
  private final ObjectId _id;

  @JsonProperty(CREATED_AT_FIELD)
  @Schema(
      name = CREATED_AT_FIELD,
      description =
          "Date and time when MongoDB Cloud took the snapshot. This parameter expresses its value"
              + " in the ISO 8601 timestamp format in UTC.",
      accessMode = Schema.AccessMode.READ_ONLY)
  private final Date _created;

  @JsonProperty(EXPIRES_AT_FIELD)
  @Schema(
      name = EXPIRES_AT_FIELD,
      description =
          "Date and time when MongoDB Cloud deletes the snapshot. This parameter expresses its"
              + " value in the ISO 8601 timestamp format in UTC.",
      accessMode = Schema.AccessMode.READ_ONLY)
  private final Date _expires;

  @JsonProperty(MONGOD_VERSION_FIELD)
  @Schema(
      name = MONGOD_VERSION_FIELD,
      type = "string",
      description = "Version of the MongoDB host that this snapshot backs up.",
      accessMode = Schema.AccessMode.READ_ONLY,
      pattern = OpenApiConst.MONGODB_VERSION_REGEX)
  private final String _mongodVersion;

  @JsonProperty(MASTER_KEY_UUID_FIELD)
  @Schema(
      name = MASTER_KEY_UUID_FIELD,
      type = "string",
      format = "uuid",
      description =
          "Unique string that identifies the Amazon Web Services (AWS) Key Management Service (KMS)"
              + " Customer Master Key (CMK) used to encrypt the snapshot. The resource returns this"
              + " value when `\"encryptionEnabled\" : true`.",
      accessMode = Schema.AccessMode.READ_ONLY,
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-112-field-names-are-camel-case",
                  value = "Schema predates IPA validation.")
            })
      })
  private final String _masterKeyUUID;

  @JsonProperty(TYPE)
  @Schema(
      name = TYPE,
      type = "string",
      description =
          "Human-readable label that categorizes the cluster as a replica set or sharded cluster.",
      accessMode = Schema.AccessMode.READ_ONLY,
      allowableValues = {"replicaSet", "shardedCluster"},
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-123-enum-values-must-be-upper-snake-case",
                  value = "Schema predates IPA validation.")
            })
      })
  private final String _type;

  @JsonProperty(STATUS)
  @Schema(
      name = STATUS,
      type = "string",
      description =
          "Human-readable label that indicates the stage of the backup process for this snapshot.",
      accessMode = Schema.AccessMode.READ_ONLY,
      allowableValues = {"queued", "inProgress", "completed", "failed"},
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-123-enum-values-must-be-upper-snake-case",
                  value = "Schema predates IPA validation.")
            })
      })
  private final String _status;

  @JsonProperty(DESCRIPTION)
  @Schema(
      name = DESCRIPTION,
      type = "string",
      description =
          "Human-readable phrase or sentence that explains the purpose of the snapshot. "
              + "The resource returns this parameter when `\"status\": \"onDemand\"`.",
      accessMode = AccessMode.READ_ONLY)
  private final String _description;

  @JsonProperty(SNAPSHOT_TYPE)
  @Schema(
      name = SNAPSHOT_TYPE,
      type = "string",
      description = "Human-readable label that identifies when this snapshot triggers.",
      accessMode = Schema.AccessMode.READ_ONLY,
      allowableValues = {"onDemand", "scheduled", "fallback"},
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-123-enum-values-must-be-upper-snake-case",
                  value = "Schema predates IPA validation.")
            })
      })
  private final String _snapshotType;

  @JsonProperty(STORAGE_SIZE_BYTES_FIELD)
  @Schema(
      name = STORAGE_SIZE_BYTES_FIELD,
      description = "Number of bytes taken to store the backup at time of snapshot.",
      accessMode = Schema.AccessMode.READ_ONLY)
  private Long _storageSizeBytes;

  @JsonProperty(FREQUENCY_TYPE_FIELD)
  @Schema(
      name = FREQUENCY_TYPE_FIELD,
      type = "string",
      description = "Human-readable label that identifies how often this snapshot triggers.",
      accessMode = Schema.AccessMode.READ_ONLY,
      allowableValues = {"hourly", "daily", "weekly", "monthly", "yearly"},
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-123-enum-values-must-be-upper-snake-case",
                  value = "Schema predates IPA validation.")
            })
      })
  private String _frequencyType;

  @JsonProperty(POLICY_ITEMS_FIELD)
  @ArraySchema(
      extensions = {
        @Extension(
            name = IPA_EXCEPTION,
            properties = {
              @ExtensionProperty(
                  name = "xgen-IPA-124-array-max-items",
                  value = "Schema predates IPA validation."),
            })
      },
      arraySchema =
          @Schema(
              name = POLICY_ITEMS_FIELD,
              description = "List that contains unique identifiers for the policy items.",
              accessMode = Schema.AccessMode.READ_ONLY),
      schema =
          @Schema(
              type = "string",
              description = "Unique 24-hexadecimal digit string that identifies one policy item.",
              pattern = OpenApiConst.OBJECT_ID_REGEX,
              example = OpenApiConst.OBJECT_ID_EXAMPLE))
  private List<ObjectId> _policyItems;

  @Hidden
  @JsonProperty(EXTRA_RETENTION_DAYS_FIELD)
  @Schema(
      name = EXTRA_RETENTION_DAYS_FIELD,
      description = "Extra retention days for the snapshot.",
      accessMode = AccessMode.READ_ONLY)
  private Integer _extraRetentionDays;

  public ApiAtlasDiskBackupSnapshotView(
      final AppSettings pSettings,
      final BackupSnapshot pBackupSnapshot,
      final SnapshotTypeView pType) {
    super(pSettings);
    _id = pBackupSnapshot.getId();
    _created = pBackupSnapshot.getSnapshotInitiationDate();
    _storageSizeBytes = pBackupSnapshot.getUsedDiskSpace();
    _expires = pBackupSnapshot.getScheduledDeletionDate();
    _mongodVersion =
        Optional.ofNullable(pBackupSnapshot.getMongoDBVersionOrFCVIfDifferent())
            .map(Version::getVersion)
            .orElse(null);
    _masterKeyUUID =
        BackupSnapshot.resolveEncryptionAtRestKeyId(pBackupSnapshot.getSnapshotEncryptionDetails());
    _type = pType.getSnapshotType();
    _status = pBackupSnapshot.getSnapshotStatus().toString();
    _description = pBackupSnapshot.getDescription().orElse(null);
    _snapshotType = pBackupSnapshot.getType().toString();
    _frequencyType = pBackupSnapshot.getFrequencyType().getValue();
    _policyItems = pBackupSnapshot.getPolicyItems();

    _extraRetentionDays =
        pBackupSnapshot.getProtectionEndDate() != null
                && pBackupSnapshot.getScheduledDeletionDate() != null
            ? (int)
                TimeUnit.DAYS.convert(
                    pBackupSnapshot.getScheduledDeletionDate().getTime()
                        - pBackupSnapshot.getProtectionEndDate().getTime(),
                    TimeUnit.MILLISECONDS)
            : null;

    addLinks(pBackupSnapshot);
  }

  void addLinks(final BackupSnapshot pBackupSnapshot) {
    final boolean isAtlas = true;
    addLink(
        LinkRelView.SELF,
        String.format(
            "/groups/%s/clusters/%s/backup/snapshots/%s",
            pBackupSnapshot.getProjectId(),
            pBackupSnapshot.getClusterName(),
            pBackupSnapshot.getId()),
        isAtlas);

    addLink(
        LinkRelView.CLUSTER,
        String.format(
            "/groups/%s/clusters/%s",
            pBackupSnapshot.getProjectId(), pBackupSnapshot.getClusterName()),
        isAtlas);
  }

  public void setExtraRetentionDays(Integer extraRetentionDays) {
    _extraRetentionDays = extraRetentionDays;
  }

  public enum SnapshotTypeView {
    REPLICA_SET("replicaSet"),
    SHARDED_CLUSTER("shardedCluster");

    private final String _snapshotType;

    SnapshotTypeView(final String pSnapshotType) {
      _snapshotType = pSnapshotType;
    }

    public String getSnapshotType() {
      return _snapshotType;
    }
  }
}
