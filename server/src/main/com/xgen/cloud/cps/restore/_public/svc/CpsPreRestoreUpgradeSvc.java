package com.xgen.cloud.cps.restore._public.svc;

import com.xgen.cloud.common.mongo._public.mongo.FeatureCompatibilityVersion;
import com.xgen.cloud.common.mongo._public.mongo.VersionUtils.Version;
import com.xgen.cloud.cps.core._public.svc.CpsFeatureFlagSvc;
import com.xgen.cloud.cps.restore._private.dao.BackupRestoreJobDao;
import com.xgen.cloud.cps.restore._public.model.BackupSnapshot;
import com.xgen.cloud.cps.restore._public.model.CpsRestoreMetadata.PreRestoreUpgradeStatus;
import com.xgen.cloud.deployment._public.model.Process;
import com.xgen.cloud.nds.project._public.model.ClusterDescription;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.List;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class CpsPreRestoreUpgradeSvc {

  private static final Logger LOG = LoggerFactory.getLogger(CpsPreRestoreUpgradeSvc.class);

  private final CpsFeatureFlagSvc cpsFeatureFlagSvc;
  private final BackupRestoreJobDao backupRestoreJobDao;

  @Inject
  public CpsPreRestoreUpgradeSvc(
      CpsFeatureFlagSvc cpsFeatureFlagSvc, BackupRestoreJobDao backupRestoreJobDao) {
    this.cpsFeatureFlagSvc = cpsFeatureFlagSvc;
    this.backupRestoreJobDao = backupRestoreJobDao;
  }

  /**
   * Determines whether a pre-restore upgrade is needed before performing a CPS restore operation.
   * Uses AutomationConfig processes as the source of truth for MongoDB versions.
   *
   * <p>A pre-restore upgrade is required when there are version compatibility differences between
   * the backup snapshot and the target cluster that need to be resolved before the restore can
   * proceed safely. This method evaluates Feature Compatibility Version (FCV) and MongoDB binary
   * version differences to make this determination.
   *
   * <p>Pre-restore upgrades are only applicable when all the conditions apply:
   *
   * <ul>
   *   <li>Non-PIT (Point-in-Time) restores
   *   <li>Continuous Delivery (CD) clusters
   *   <li>Target FCV is 1 minor version lower than the snapshot FCV (binary versions must be same
   *       minor version)
   * </ul>
   *
   * @param targetCluster the cluster description of the restore target
   * @param snapshot the backup snapshot being restored
   * @param targetProcesses the list of processes from the target cluster's automation config
   * @return true if a pre-restore upgrade is needed, false otherwise
   */
  public boolean needsPreRestoreUpgrade(
      ClusterDescription targetCluster, BackupSnapshot snapshot, List<Process> targetProcesses) {
    if (!cpsFeatureFlagSvc.isCpsPreRestoreUpgradeEnabled(targetCluster.getGroupId())) {
      return false;
    }
    if (!targetCluster.isDedicatedCluster()) {
      return false;
    }
    if (targetCluster.getVersionReleaseSystem()
        != ClusterDescription.VersionReleaseSystem.CONTINUOUS) {
      return false;
    }

    final String snapshotFcv = snapshot.getFcv();
    // null FCVs should only be present in versions <= 3.2, but checking null just in case
    if (snapshotFcv == null) {
      LOG.atInfo()
          .setMessage("Snapshot FCV is null. No restore upgrade needed.")
          .addKeyValue("snapshotId", snapshot.getId())
          .log();
      return false;
    }

    final Version snapshotBinaryVersion = snapshot.getMongoDbVersion();
    if (snapshotBinaryVersion == null) {
      LOG.atInfo()
          .setMessage("Snapshot binary version is null. No restore upgrade needed.")
          .addKeyValue("snapshotId", snapshot.getId())
          .log();
      return false;
    }

    final FeatureCompatibilityVersion snapshotFcvVersion =
        FeatureCompatibilityVersion.fromString(snapshotFcv);
    if (snapshotFcvVersion == null) {
      LOG.atInfo()
          .setMessage("Invalid snapshot FCV format. No restore upgrade needed.")
          .addKeyValue("snapshotFcv", snapshotFcv)
          .log();
      return false;
    }

    // for each process, validate FCV and binary versions are compatible
    String targetFcv = null;
    for (final Process process : targetProcesses) {
      final Version processVersion = Version.fromString(process.getVersion());
      final String processFcv = process.getFeatureCompatibilityVersion();
      if (processFcv == null) {
        LOG.atInfo()
            .setMessage("Process FCV is null. No restore upgrade needed.")
            .addKeyValue("targetClusterName", targetCluster.getName())
            .addKeyValue("processName", process.getName())
            .log();
        return false;
      }

      // Ensure all processes have the same FCV
      if (targetFcv == null) {
        targetFcv = processFcv;
      } else if (!targetFcv.equals(processFcv)) {
        LOG.atWarn()
            .setMessage("Inconsistent FCV across processes. No restore upgrade needed.")
            .addKeyValue("targetClusterName", targetCluster.getName())
            .addKeyValue("expectedFcv", targetFcv)
            .addKeyValue("processFcv", processFcv)
            .addKeyValue("processName", process.getName())
            .log();
        return false;
      }

      final FeatureCompatibilityVersion targetFcvVersion =
          FeatureCompatibilityVersion.fromString(processFcv);
      if (targetFcvVersion == null) {
        LOG.atInfo()
            .setMessage("Invalid process FCV format. No restore upgrade needed.")
            .addKeyValue("processFcv", processFcv)
            .addKeyValue("processName", process.getName())
            .log();
        return false;
      }

      // Check if this process needs pre-restore upgrade
      boolean processNeedsUpgrade = false;

      // Validation: Target FCV is 1 minor version lower than snapshot FCV (binary versions must be
      // same minor versions)
      if (processVersion.getMajor() == snapshotBinaryVersion.getMajor()
          && processVersion.getMinor() == snapshotBinaryVersion.getMinor()) {
        final int fcvDiff = snapshotFcvVersion.getMinor() - targetFcvVersion.getMinor();
        if (fcvDiff == 1) {
          processNeedsUpgrade = true;
        }
      }

      // If any process doesn't need upgrade, then cluster doesn't need upgrade
      if (!processNeedsUpgrade) {
        return false;
      }
    }
    // All processes need upgrade
    return true;
  }

  /**
   * Updates the pre-restore upgrade status for a restore job.
   *
   * @param restoreJobId the ID of the restore job to update
   * @param status the new pre-restore upgrade status
   */
  public void updatePreRestoreUpgradeStatus(
      final ObjectId restoreJobId, final PreRestoreUpgradeStatus status) {
    backupRestoreJobDao.updatePreRestoreUpgradeStatus(restoreJobId, status);
  }
}
