package com.xgen.cloud.cps.restore._public.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xgen.cloud.cps.restore._public.model.AttachStats.FieldDefs;
import io.swagger.v3.oas.annotations.Hidden;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Hidden
public record AttachStatsView(
    @JsonProperty(FieldDefs.MONGO_STARTUP_STATS) Map<String, Map<String, Long>> mongoStartupStats,
    @JsonProperty(FieldDefs.MONGO_SHUTDOWN_STATS) Map<String, Map<String, Long>> mongoShutdownStats,
    @JsonProperty(FieldDefs.DETACH_VOLUME_DURATION_MILLISECONDS)
        Long detachVolumeDurationMilliseconds,
    @JsonProperty(FieldDefs.CREATE_VOLUME_FROM_SNAPSHOT_DURATION_SECONDS)
        Long createVolumeFromSnapshotDurationSeconds,
    @JsonProperty(FieldDefs.ATTACH_VOLUME_DURATION_MILLISECONDS)
        Long attachVolumeDurationMilliseconds,
    @JsonProperty(FieldDefs.MOUNT_VOLUME_WITH_SNAPSHOT_DATA_DURATION_MILLISECONDS)
        Long mountVolumeWithSnapshotDataDurationMilliseconds,
    @JsonProperty(FieldDefs.DISK_TYPE) String diskType,
    @JsonProperty(FieldDefs.BOUNCE_STOP_IF_UP_WITH_FORCE_KILL_DURATION_SECONDS)
        Long bounceStopIfUpWithForceKillDurationSeconds,
    @JsonProperty(FieldDefs.GET_DIRECT_ATTACH_FILE_LIST_DURATION_SECONDS)
        Long getDirectAttachFileListDurationSeconds,
    @JsonProperty(FieldDefs.DISK_PRE_WARM_DURATION_SECONDS) Long diskPreWarmDurationSeconds,
    @JsonProperty(FieldDefs.DISK_PRE_WARM_THROUGHPUT_MB_PER_SECOND)
        Double diskPreWarmThroughputMbPerSecond,
    @JsonProperty(FieldDefs.PIT_META_READER_DURATION_SECONDS) Long pitMetaReaderDurationSeconds,
    @JsonProperty(FieldDefs.PIT_PULLER_DURATION_SECONDS) Long pitPullerDurationSeconds,
    @JsonProperty(FieldDefs.PIT_OP_PROVIDER_DURATION_SECONDS) Long pitOpProviderDurationSeconds,
    @JsonProperty(FieldDefs.PIT_INSERT_OPS_DURATION_SECONDS) Long pitInsertOpsDurationSeconds) {

  private AttachStatsView(final AttachStats attachStats) {
    this(
        attachStats.mongoStartupStats(),
        attachStats.mongoShutdownStats(),
        attachStats.detachVolumeDurationMilliseconds(),
        attachStats.createVolumeFromSnapshotDurationSeconds(),
        attachStats.attachVolumeDurationMilliseconds(),
        attachStats.mountVolumeWithSnapshotDataDurationMilliseconds(),
        attachStats.diskType(),
        attachStats.bounceStopIfUpWithForceKillDurationSeconds(),
        attachStats.getDirectAttachFileListDurationSeconds(),
        attachStats.diskPreWarmDurationSeconds(),
        attachStats.diskPreWarmThroughputMbPerSecond(),
        attachStats.pitMetaReaderDurationSeconds(),
        attachStats.pitPullerDurationSeconds(),
        attachStats.pitOpProviderDurationSeconds(),
        attachStats.pitInsertOpsDurationSeconds());
  }

  public static AttachStatsView fromAttachStats(final AttachStats attachStats) {
    return attachStats == null ? null : new AttachStatsView(attachStats);
  }
}
