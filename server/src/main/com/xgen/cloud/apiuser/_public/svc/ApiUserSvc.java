package com.xgen.cloud.apiuser._public.svc;

import static com.xgen.cloud.common.util._public.auth.UsernameUtils.normalizeUsername;
import static com.xgen.cloud.user._public.model.activity.ApiUserEvent.Type.API_KEY_ACCESS_LIST_ENTRY_ADDED;
import static com.xgen.cloud.user._public.model.activity.ApiUserEvent.Type.API_KEY_ACCESS_LIST_ENTRY_DELETED;
import static com.xgen.cloud.user._public.model.activity.ApiUserEvent.Type.API_KEY_ADDED_TO_GROUP;
import static com.xgen.cloud.user._public.model.activity.ApiUserEvent.Type.API_KEY_CREATED;
import static com.xgen.cloud.user._public.model.activity.ApiUserEvent.Type.API_KEY_DELETED;
import static com.xgen.cloud.user._public.model.activity.ApiUserEvent.Type.API_KEY_DESCRIPTION_CHANGED;
import static com.xgen.cloud.user._public.model.activity.ApiUserEvent.Type.API_KEY_REMOVED_FROM_GROUP;
import static com.xgen.cloud.user._public.model.activity.ApiUserEvent.Type.API_KEY_ROLES_CHANGED;
import static com.xgen.cloud.user._public.model.activity.ApiUserEvent.Type.TEMP_GLOBAL_API_KEY_CREATED;
import static java.util.stream.Collectors.toCollection;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.RoleAssignment;
import com.xgen.cloud.activity._public.svc.event.AuditSvc;
import com.xgen.cloud.apiuser._public.view.ApiUserRoleAssignmentView;
import com.xgen.cloud.authz.sync._public.wrapper.AuthzMmsSyncClientWrapper;
import com.xgen.cloud.billing._public.svc.IPlanSvc;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.auditInfo._public.model.AuditInfo;
import com.xgen.cloud.common.constants._public.model.user.UserType;
import com.xgen.cloud.common.model._public.error.ServerError;
import com.xgen.cloud.common.model._public.error.SvcException;
import com.xgen.cloud.common.user._public.model.AppUserErrorCode;
import com.xgen.cloud.common.util._public.util.SetUtils;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.rolecheck._public.svc.RoleCheckSvc;
import com.xgen.cloud.user._public.model.ApiUser;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.model.UserAllowList;
import com.xgen.cloud.user._public.model.UserApiKey;
import com.xgen.cloud.user._public.model.activity.ApiUserAudit;
import com.xgen.cloud.user._public.model.activity.ApiUserEvent;
import com.xgen.cloud.user._public.model.activity.ApiUserEvent.Type;
import com.xgen.cloud.user._public.svc.UserAllowListSvc;
import com.xgen.cloud.user._public.svc.UserApiKeySvc;
import com.xgen.cloud.user._public.svc.UserSvc;
import com.xgen.module.liveimport.dao.TargetOrgDao;
import com.xgen.module.liveimport.model.TargetOrg;
import com.xgen.svc.mms.model.billing.OrgPlan;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class ApiUserSvc {
  private static final Logger LOG = LoggerFactory.getLogger(ApiUserSvc.class);

  // maximum attempts that will be made for unique API Username generation before throwing an error
  private static final int ALLOWED_USERNAME_GENERATION_ATTEMPTS = 3;

  private final UserApiKeySvc userApiKeySvc;
  private final UserAllowListSvc userAllowListSvc;
  private final IPlanSvc planSvc;
  private final AuditSvc auditSvc;
  private final TargetOrgDao targetOrgDao;
  private final GroupSvc groupSvc;
  private final AppSettings appSettings;
  private final UserSvc userSvc;
  private final AuthzMmsSyncClientWrapper authzMmsSyncClientWrapper;

  @Inject
  public ApiUserSvc(
      AppSettings pAppSettings,
      UserApiKeySvc pUserApiKeySvc,
      UserAllowListSvc pUserAllowListSvc,
      IPlanSvc pPlanSvc,
      AuditSvc pAuditSvc,
      TargetOrgDao pTargetOrgDao,
      GroupSvc pGroupSvc,
      UserSvc pUserSvc,
      AuthzMmsSyncClientWrapper pAuthzMmsSyncClientWrapper) {
    appSettings = pAppSettings;
    userApiKeySvc = pUserApiKeySvc;
    userAllowListSvc = pUserAllowListSvc;
    planSvc = pPlanSvc;
    auditSvc = pAuditSvc;
    targetOrgDao = pTargetOrgDao;
    groupSvc = pGroupSvc;
    userSvc = pUserSvc;
    authzMmsSyncClientWrapper = pAuthzMmsSyncClientWrapper;
  }

  @Nullable
  public ApiUser findByUserId(ObjectId pApiUserId) {
    return getApiUser(pApiUserId);
  }

  public ApiUser findByUsername(String pUsername) {
    String trimmedUsername = StringUtils.trimToEmpty(pUsername);
    return getApiUser(trimmedUsername);
  }

  public List<ApiUser> findApiUsersByOrgId(ObjectId pOrgId, boolean pIncludeInternalApiUsers) {
    List<AppUser> orgApiAppUsers = userSvc.findApiUsersByOrgId(pOrgId);
    return getApiUsersFromAppUsers(orgApiAppUsers, pIncludeInternalApiUsers);
  }

  public List<ApiUser> findApiUsersByGroupId(ObjectId pGroupId, boolean pIncludeInternalApiUsers) {
    List<AppUser> groupApiAppUsers = userSvc.findApiUsersByGroupId(pGroupId);
    return getApiUsersFromAppUsers(groupApiAppUsers, pIncludeInternalApiUsers);
  }

  public List<ApiUser> findActiveApiUsers(int pSkip, int pLimit) {
    return userSvc.findActiveApiUsers(pSkip, pLimit).stream()
        .map(this::getApiUser)
        .filter(Objects::nonNull)
        .map(ApiUser::redact)
        .collect(toList());
  }

  public List<ApiUser> findGlobalApiUsers() {
    List<AppUser> globalApiAppUsers = userSvc.findAllGlobalApiUsers();
    return getApiUsersFromAppUsers(globalApiAppUsers, true);
  }

  public void validateHasActiveBackingUser(ObjectId backingUserId) throws SvcException {
    AppUser backingUser = userSvc.findById(backingUserId);
    if (backingUser == null) {
      throw new SvcException(AppUserErrorCode.API_KEY_UNAUTHORIZED);
    }
    if (backingUser.isSoftDeleted() || backingUser.isHardDeleted()) {
      throw new SvcException(AppUserErrorCode.USER_DELETED);
    }
  }

  public void validateIsAuthorized(ApiUser pApiUser) throws SvcException {
    if (pApiUser == null) {
      throw new SvcException(AppUserErrorCode.API_KEY_NOT_FOUND);
    }
  }

  public long countApiUsersByOrgId(ObjectId pOrgId) {
    return userSvc.countApiUsersByOrgId(pOrgId);
  }

  public ApiUser createGroupApiUser(
      String pUsername,
      String pDescription,
      Organization pOrganization,
      Group pGroup,
      List<Role> pRoles,
      AuditInfo pAuditInfo)
      throws SvcException {

    OrgPlan plan = planSvc.getCurrentPlan(pOrganization.getId());
    for (Role role : pRoles) {
      if (!RoleCheckSvc.isRoleAvailableForGroup(
          role, appSettings, pOrganization, pGroup, plan.getPlanType())) {
        throw new SvcException(AppUserErrorCode.INVALID_ROLE_IN_GROUP, role, pGroup.getId());
      }
    }

    Set<RoleAssignment> roleAssignments =
        pRoles.stream().map(role -> RoleAssignment.forGroup(role, pGroup.getId())).collect(toSet());

    roleAssignments.add(RoleAssignment.forOrg(Role.ORG_MEMBER, pOrganization.getId()));

    return createOrganizationApiUser(pUsername, pDescription, roleAssignments, null, pAuditInfo);
  }

  public ApiUser createOrganizationApiUser(
      String pUsername,
      String pDescription,
      Set<RoleAssignment> pRoleAssignments,
      Organization pOrganization,
      AuditInfo pAuditInfo)
      throws SvcException {
    if (userSvc.findByUsername(pUsername) != null) {
      throw new SvcException(AppUserErrorCode.USER_ALREADY_EXISTS);
    }

    String normalizeUsername = normalizeUsername(pUsername);
    if (StringUtils.isEmpty(normalizeUsername)) {
      throw new SvcException(AppUserErrorCode.INVALID_API_PUBLIC_KEY);
    }

    validateApiUserKeyDescription(pDescription);

    Set<ObjectId> orgIds =
        pRoleAssignments.stream()
            .map(RoleAssignment::getOrgId)
            .filter(Objects::nonNull)
            .collect(toSet());

    ObjectId orgId = pOrganization == null ? null : pOrganization.getId();

    if (orgId != null) {
      orgIds.add(orgId);
    }

    if (orgIds.isEmpty()) {
      throw new SvcException(AppUserErrorCode.API_KEY_MUST_BELONG_TO_AN_ORG);
    } else if (orgIds.size() > 1) {
      throw new SvcException(AppUserErrorCode.API_KEY_CANNOT_BELONG_TO_MULTIPLE_ORGS);
    }

    AppUser backingUser = userSvc.createApiUser(normalizeUsername, pRoleAssignments, orgId);
    UserApiKey backingKey = userApiKeySvc.add(backingUser.getId(), pDescription);

    ApiUser apiUser = getApiUser(backingUser, backingKey);
    auditApiUserCreation(apiUser, pAuditInfo);

    return apiUser;
  }

  public ApiUser createGlobalApiUser(
      String pDescription, Set<RoleAssignment> pGlobalRoleAssignments, AuditInfo pAuditInfo)
      throws SvcException {
    // the public api does not rely on a preexisting public key
    String username = generateApiUserUsername();
    return createGlobalApiUser(username, pDescription, pGlobalRoleAssignments, pAuditInfo);
  }

  public ApiUser createGlobalApiUser(
      String pUsername,
      String pDescription,
      Set<RoleAssignment> pGlobalRoleAssignments,
      AuditInfo pAuditInfo)
      throws SvcException {
    AppUser existingUser = userSvc.findByUsername(pUsername);
    if (existingUser != null) {
      throw new SvcException(AppUserErrorCode.USER_ALREADY_EXISTS);
    }

    String normalizeUsername = normalizeUsername(pUsername);
    if (StringUtils.isEmpty(normalizeUsername)) {
      throw new SvcException(AppUserErrorCode.INVALID_API_PUBLIC_KEY);
    }

    validateApiUserKeyDescription(pDescription);

    AppUser backingUser = userSvc.createApiUser(normalizeUsername, pGlobalRoleAssignments, null);
    UserApiKey backingKey = userApiKeySvc.add(backingUser.getId(), pDescription);

    ApiUser apiUser = getApiUser(backingUser, backingKey);
    auditApiUserCreation(apiUser, pAuditInfo);

    return apiUser;
  }

  public ApiUser createGlobalApiUser(
      String pUsername,
      UUID pPassword,
      String pDescription,
      Set<RoleAssignment> pGlobalRoleAssignments,
      AuditInfo pAuditInfo)
      throws SvcException {
    AppUser existingUser = userSvc.findByUsername(pUsername);
    if (existingUser != null) {
      throw new SvcException(AppUserErrorCode.USER_ALREADY_EXISTS);
    }

    String normalizeUsername = normalizeUsername(pUsername);
    if (StringUtils.isEmpty(normalizeUsername)) {
      throw new SvcException(AppUserErrorCode.INVALID_API_PUBLIC_KEY);
    }

    validateApiUserKeyDescription(pDescription);

    AppUser backingUser = userSvc.createApiUser(normalizeUsername, pGlobalRoleAssignments, null);
    UserApiKey backingKey = userApiKeySvc.add(backingUser.getId(), pPassword, pDescription);

    ApiUser apiUser = getApiUser(backingUser, backingKey);
    auditApiUserCreation(apiUser, pAuditInfo);

    return apiUser;
  }

  public ApiUser createTemporaryGlobalAtlasAdminApiUser(
      String pReason, Date pExpiresAt, AuditInfo pAuditInfo) throws SvcException {
    String publicKey = generateApiUserUsername();
    Set<RoleAssignment> globalRoleAssignment =
        Set.of(RoleAssignment.forGlobal(Role.GLOBAL_ATLAS_ADMIN));

    AppUser backingUser = userSvc.createApiUser(publicKey, globalRoleAssignment, null);

    String description =
        String.format(
            "Temporary global atlas admin API key generated. Reason provided: %s", pReason);

    UserApiKey backingKey =
        userApiKeySvc.addTempGlobal(backingUser.getId(), description, pExpiresAt);
    ApiUser apiUser = getApiUser(backingUser, backingKey);
    auditTemporaryGlobalApiUserCreation(apiUser, pExpiresAt, pAuditInfo);

    return apiUser;
  }

  public ApiUser createTemporaryGlobalApiUser(String pReason, Date pExpiresAt, AuditInfo pAuditInfo)
      throws SvcException {
    String publicKey = generateApiUserUsername();
    Set<RoleAssignment> globalRoleAssignment =
        Set.of(RoleAssignment.forGlobal(Role.GLOBAL_READ_ONLY));

    AppUser backingUser = userSvc.createApiUser(publicKey, globalRoleAssignment, null);

    String description =
        String.format("Temporary global API key generated. Reason provided: %s", pReason);

    UserApiKey backingKey =
        userApiKeySvc.addTempGlobal(backingUser.getId(), description, pExpiresAt);
    ApiUser apiUser = getApiUser(backingUser, backingKey);
    auditTemporaryGlobalApiUserCreation(apiUser, pExpiresAt, pAuditInfo);

    return apiUser;
  }

  public String generateApiUserUsername() throws SvcException {
    String apiUsername = RandomStringUtils.randomAlphabetic(8).toLowerCase();
    int retryCount = 0;

    // If a unique username is not generated in 3 attempts, then throw an exception
    while (userSvc.findByUsername(apiUsername) != null) {
      if (retryCount < ALLOWED_USERNAME_GENERATION_ATTEMPTS) {
        apiUsername = RandomStringUtils.randomAlphabetic(8);
        retryCount++;
      } else {
        throw new ServerError();
      }
    }

    return apiUsername;
  }

  public ApiUser setGlobalRoles(ApiUser pApiUser, Set<Role> pNewRoles, AuditInfo pAuditInfo)
      throws SvcException {
    if (pNewRoles.isEmpty()) {
      throw new SvcException(AppUserErrorCode.CANNOT_REMOVE_ALL_API_KEY_ROLES);
    }

    for (Role role : pNewRoles) {
      if (!role.isGlobal()) {
        throw new SvcException(AppUserErrorCode.INVALID_ROLE_FOR_GLOBAL_KEY, role);
      }
    }

    // Gather existing role assignments, filtering out assignments for global roles
    var roleAssignments =
        pApiUser.getRoleAssignments().stream()
            .filter(roleAssignment -> !roleAssignment.getRole().isGlobal())
            .collect(toCollection(HashSet::new));

    // Add the new global role assignments
    roleAssignments.addAll(pNewRoles.stream().map(RoleAssignment::forGlobal).collect(toSet()));

    if (roleAssignments.equals(pApiUser.getRoleAssignments())) {
      return pApiUser;
    }

    userSvc.resetRoleAssignments(pApiUser.getBackingUser(), roleAssignments, pAuditInfo);
    auditApiUserRolesChanged(pApiUser, null, null, pNewRoles, pAuditInfo);
    return getApiUser(pApiUser.getUserId());
  }

  public void setOrgRoles(
      ApiUser pApiUser, Organization pOrganization, Set<Role> pNewRoles, AuditInfo pAuditInfo)
      throws SvcException {

    for (Role role : pNewRoles) {
      if (!role.isOrgSpecific()) {
        throw new SvcException(AppUserErrorCode.INVALID_ROLE_IN_ORG);
      }
    }

    if (CollectionUtils.isEmpty(pNewRoles)) {
      throw new SvcException(AppUserErrorCode.CANNOT_REMOVE_ALL_API_KEY_ROLES);
    }

    Set<RoleAssignment> updatedRoles =
        Stream.concat(
                pApiUser.getRoleAssignments().stream()
                    .filter(
                        roleAssignment ->
                            roleAssignment.getOrgId() == null
                                || !roleAssignment.getOrgId().equals(pOrganization.getId())),
                pNewRoles.stream().map(role -> RoleAssignment.forOrg(role, pOrganization.getId())))
            .collect(toSet());

    if (SetUtils.areEqual(updatedRoles, pApiUser.getRoleAssignments())) {
      return;
    }

    userSvc.resetRoleAssignments(pApiUser.getBackingUser(), updatedRoles, pAuditInfo);
    auditApiUserRolesChanged(pApiUser, pOrganization, null, pNewRoles, pAuditInfo);
  }

  public void addApiUsersToGroup(
      Organization pOrganization,
      Group pGroup,
      List<ApiUserRoleAssignmentView> pApiUserAssignments,
      AuditInfo pAuditInfo)
      throws SvcException {
    if (CollectionUtils.isEmpty(pApiUserAssignments)) {
      return;
    }

    Set<ObjectId> apiUserIds =
        pApiUserAssignments.stream().map(ApiUserRoleAssignmentView::getApiUserId).collect(toSet());
    List<AppUser> backingUsers = userSvc.findByIds(apiUserIds);

    for (AppUser backingUser : backingUsers) {
      if (!backingUser.getOrgIds().contains(pOrganization.getId())) {
        throw new SvcException(AppUserErrorCode.API_KEY_NOT_FOUND, backingUser.getId());
      }
      if (backingUser.getGroupIds().contains(pGroup.getId())) {
        throw new SvcException(AppUserErrorCode.API_KEY_ALREADY_IN_GROUP);
      }
    }

    OrgPlan plan = planSvc.getCurrentPlan(pOrganization.getId());
    Optional<Role> invalidRole =
        pApiUserAssignments.stream()
            .map(ApiUserRoleAssignmentView::getRoles)
            .flatMap(Collection::stream)
            .filter(
                role ->
                    !RoleCheckSvc.isRoleAvailableForGroup(
                        role, appSettings, pOrganization, pGroup, plan.getPlanType()))
            .findFirst();

    for (var role : invalidRole.stream().toList()) {
      throw new SvcException(AppUserErrorCode.INVALID_ROLE_IN_GROUP, role, pGroup.getId());
    }

    for (ApiUserRoleAssignmentView roleAssignment : pApiUserAssignments) {
      ApiUser apiUser = getApiUser(roleAssignment.getApiUserId());
      if (apiUser != null) {
        userSvc.addUserToGroup(
            apiUser.getBackingUser(), pGroup.getId(), roleAssignment.getRoles(), pAuditInfo);
      }
    }
  }

  public void removeApiUsersFromGroup(Group pGroup, AuditInfo pAuditInfo) throws SvcException {
    List<AppUser> finalAppUsers = userSvc.findApiUsersByGroupId(pGroup.getId());

    String warningMessage = "User {} is deleted but has group IDs {} attached to it.";
    finalAppUsers.stream()
        .filter(AppUser::isDeleted)
        .forEach(appUser -> LOG.warn(warningMessage, appUser.getId(), appUser.getGroupIds()));

    for (AppUser appUser : finalAppUsers) {
      if (!appUser.isDeleted()) {
        ObjectId id = appUser.getId();
        ApiUser apiUser = findByUserId(id);
        removeApiUserFromGroup(apiUser, pGroup, pAuditInfo);
      }
    }
  }

  public void removeApiUserFromGroup(ApiUser pApiUser, Group pGroup, AuditInfo pAuditInfo)
      throws SvcException {
    groupSvc.removeUserFromGroup(pApiUser.getBackingUser(), pGroup, pAuditInfo);
  }

  public void setGroupRoles(
      ApiUser pApiUser,
      Organization pOrganization,
      Group pGroup,
      Set<Role> pNewRoles,
      AuditInfo pAuditInfo)
      throws SvcException {
    if (CollectionUtils.isEmpty(pNewRoles)) {
      throw new SvcException(AppUserErrorCode.CANNOT_REMOVE_ALL_API_KEY_ROLES);
    }

    OrgPlan plan = planSvc.getCurrentPlan(pOrganization.getId());
    for (Role role : pNewRoles) {
      if (!RoleCheckSvc.isRoleAvailableForGroup(
          role, appSettings, pOrganization, pGroup, plan.getPlanType())) {
        throw new SvcException(AppUserErrorCode.INVALID_ROLE_IN_GROUP, role, pGroup.getId());
      }
    }

    Set<RoleAssignment> updatedRoles =
        Stream.concat(
                pApiUser.getRoleAssignments().stream()
                    .filter(
                        roleAssignment ->
                            roleAssignment.getGroupId() == null
                                || !roleAssignment.getGroupId().equals(pGroup.getId())),
                pNewRoles.stream().map(role -> RoleAssignment.forGroup(role, pGroup.getId())))
            .collect(toSet());

    if (SetUtils.areEqual(updatedRoles, pApiUser.getRoleAssignments())) {
      return;
    }

    userSvc.resetRoleAssignments(pApiUser.getBackingUser(), updatedRoles, pAuditInfo);
    auditApiUserRolesChanged(pApiUser, null, pGroup, pNewRoles, pAuditInfo);
  }

  public void updateDescription(ApiUser pApiUser, String pDescription, AuditInfo pAuditInfo)
      throws SvcException {
    validateApiUserKeyDescription(pDescription);

    if (pApiUser.getDesc().equals(pDescription)) {
      return;
    }

    userApiKeySvc.updateDescription(pApiUser.getUserId(), pApiUser.getUserApiKeyId(), pDescription);
    auditApiUserDescriptionChanged(pApiUser, pDescription, pAuditInfo);
  }

  public void removeAllOrgApiUsers(ObjectId pOrgId, AuditInfo pAuditInfo) throws SvcException {
    List<ApiUser> apiUsersToRemove =
        userSvc.findApiUsersByOrgId(pOrgId).stream()
            // Don't remove global API users
            // After CLOUDP-60974, global API users should no longer be added to organizations,
            // But there might be existing organizations with global API users added.
            .filter(appUser -> appUser.getGlobalRoles().isEmpty())
            .map(this::getApiUser)
            .toList();

    for (ApiUser apiUser : apiUsersToRemove) {
      deleteApiUser(apiUser, pAuditInfo);
    }
  }

  /**
   * Soft deletes the API backing user, removes related API keys and entities, and performs audit
   * logging. NOTE: CLOUDP-341570: A significant portion of production users are soft-deleted API
   * users, most of whom no longer have API keys. TODO: Consider hard deletion where appropriate.
   */
  public void deleteApiUser(ApiUser pApiUser, AuditInfo pAuditInfo) throws SvcException {
    deleteApiUser(pApiUser, pAuditInfo, false);
  }

  /**
   * Hard deletes the API backing user, removes related API keys and entities, and performs audit
   * logging.
   */
  public void hardDeleteApiUser(ApiUser pApiUser, AuditInfo pAuditInfo) throws SvcException {
    deleteApiUser(pApiUser, pAuditInfo, true);
  }

  /**
   * Deletes the API backing user, either softly or permanently based on the parameter. Also removes
   * all related API keys and entities, and performs audit logging.
   *
   * <p>Soft delete: Marks the API user as deleted and clears all organization, group, and role
   * assignments. NOTE: CLOUDP-341570: A significant portion of production users are soft-deleted
   * API users, most of whom no longer have API keys.
   *
   * <p>Hard delete: completely removes the API user document from the database.
   *
   * <p>Consider hard deletion in the use cases where api user would always have one api key to
   * prevent soft deleted user bloat.
   */
  private void deleteApiUser(ApiUser pApiUser, AuditInfo pAuditInfo, boolean hardDeleteBackingUser)
      throws SvcException {
    if (pApiUser == null) {
      throw new SvcException(AppUserErrorCode.API_KEY_NOT_FOUND);
    }

    if (appSettings.isPushLiveMigrationsEnabled()) {
      Optional<TargetOrg> targetOrg = targetOrgDao.findByPublicApiKeyId(pApiUser.getUserApiKeyId());
      if (targetOrg.isPresent()) {
        throw new SvcException(
            AppUserErrorCode.CANNOT_DELETE_API_KEY_LINKED_TO_TARGET_ORG,
            targetOrg.get().getTargetOrgId());
      }
    }

    int numUserDeleted =
        hardDeleteBackingUser
            ? userSvc.hardDeleteApiUser(pApiUser.getUserId())
            : userSvc.removeApiUser(pApiUser.getUserId());
    if (numUserDeleted > 0) {
      // Ensure audit is performed if the backing user is deleted, since this makes the API key
      // unusable. This prevents missing audit logs due to potential failures in the below calls.
      auditApiUserDeleted(pApiUser, pAuditInfo);
    }
    // For API keys without a backing user, the deprecated GPAK CRON job may attempt cleanup by
    // passing only the userId in a minimal ApiUser object
    String actorOrUserId =
        (pApiUser.getBackingUser() != null)
            ? pApiUser.getBackingUser().getActorId()
            : pApiUser.getUserId().toString();

    authzMmsSyncClientWrapper.deleteActor(actorOrUserId, pAuditInfo);
    userApiKeySvc.removeByUserId(pApiUser.getUserId());
  }

  public UserAllowList addAllowListEntry(ApiUser pApiUser, String pIpAddress, AuditInfo pAuditInfo)
      throws SvcException {
    String trimmedIpAddress = StringUtils.trimToNull(pIpAddress);

    UserAllowList allowListEntry =
        userAllowListSvc.add(pApiUser.getUserId(), UserType.API, trimmedIpAddress);
    auditApiUserAllowListEvent(pApiUser, pIpAddress, API_KEY_ACCESS_LIST_ENTRY_ADDED, pAuditInfo);

    return allowListEntry;
  }

  public void deleteAccessListEntries(ApiUser apiUser, AuditInfo pAuditInfo) {
    List<UserAllowList> accessLists = userAllowListSvc.findByUserId(apiUser.getUserId());
    accessLists.forEach(
        accessList -> {
          deleteAllowListEntry(apiUser, accessList.getId(), pAuditInfo);
        });
  }

  public void deleteAllowListEntry(
      ApiUser apiUser, ObjectId allowListEntryId, AuditInfo auditInfo) {

    var userAllowList = userAllowListSvc.findById(allowListEntryId);

    if (userAllowList == null || !userAllowList.getUserId().equals(apiUser.getUserId())) {
      return;
    }

    userAllowListSvc.remove(userAllowList.getId());
    auditApiUserAllowListEvent(
        apiUser, userAllowList.getIpAddress(), API_KEY_ACCESS_LIST_ENTRY_DELETED, auditInfo);
  }

  public void setShouldApplyOrgUiAccessListForApi(
      ApiUser pApiUser,
      ObjectId orgId,
      boolean pShouldApplyOrgUiAccessListForApi,
      AuditInfo pAuditInfo) {

    userSvc.setShouldApplyOrgUiAccessListForApi(
        pApiUser.getUserId(), pShouldApplyOrgUiAccessListForApi);

    Type type =
        pShouldApplyOrgUiAccessListForApi
            ? Type.API_KEY_UI_IP_ACCESS_LIST_INHERITANCE_ENABLED
            : Type.API_KEY_UI_IP_ACCESS_LIST_INHERITANCE_DISABLED;

    ApiUserAudit.Builder auditBuilder = initAuditBuilder(pApiUser, type, pAuditInfo);
    auditBuilder.orgId(orgId);
    auditSvc.saveAuditEvent(auditBuilder.build());
  }

  public ApiUser createApiUser(
      String pUsername,
      String pDescription,
      Organization pOrganization,
      @Nullable Group pGroup,
      List<Role> pRoles,
      AuditInfo pAuditInfo)
      throws SvcException {
    Set<RoleAssignment> roleAssignments =
        pRoles.stream()
            .map(
                role -> {
                  if (role.isOrgSpecific()) {
                    return RoleAssignment.forOrg(role, pOrganization.getId());
                  } else if (pGroup != null && role.isGroupSpecific()) {
                    return RoleAssignment.forGroup(role, pGroup.getId());
                  }
                  return RoleAssignment.forGlobal(role);
                })
            .collect(toSet());

    return createOrganizationApiUser(
        pUsername, pDescription, roleAssignments, pOrganization, pAuditInfo);
  }

  private void validateApiUserKeyDescription(String pDesc) throws SvcException {
    if (StringUtils.isEmpty(pDesc)) {
      throw new SvcException(AppUserErrorCode.API_KEY_REQUIRES_DESCRIPTION);
    }

    if (pDesc.length() > UserApiKey.MAX_API_KEY_DESC_LENGTH) {
      throw new SvcException(AppUserErrorCode.INVALID_API_KEY_DESCRIPTION);
    }
  }

  private void auditApiUserCreation(ApiUser pApiUser, AuditInfo pAuditInfo) {
    ApiUserAudit.Builder auditBuilder = initAuditBuilder(pApiUser, API_KEY_CREATED, pAuditInfo);
    auditBuilder.orgId(pApiUser.getOrgId());

    // Track the project that created the API Key in case we later want to scope the
    // API Key to the project that it was created in.
    pApiUser.getRoleAssignments().stream()
        .map(RoleAssignment::getGroupId)
        .filter(Objects::nonNull)
        .findFirst()
        .ifPresent(auditBuilder::projectOriginId);

    auditSvc.saveAuditEvent(auditBuilder.build());

    auditApiUserForEachGroup(pApiUser, API_KEY_ADDED_TO_GROUP, pAuditInfo);
  }

  private void auditApiUserForEachGroup(
      ApiUser pApiUser, ApiUserEvent.Type eventType, AuditInfo pAuditInfo) {
    ApiUserAudit.Builder auditBuilder = initAuditBuilder(pApiUser, eventType, pAuditInfo);
    pApiUser.getRoleAssignments().stream()
        .map(RoleAssignment::getGroupId)
        .filter(Objects::nonNull)
        .forEach(
            (ObjectId groupId) -> {
              auditBuilder.groupId(groupId);
              auditSvc.saveAuditEvent(auditBuilder.build());
            });
  }

  private void auditTemporaryGlobalApiUserCreation(
      ApiUser pApiUser, Date pExpiresAt, AuditInfo pAuditInfo) {
    ApiUserAudit.Builder auditBuilder =
        initAuditBuilder(pApiUser, TEMP_GLOBAL_API_KEY_CREATED, pAuditInfo);
    auditBuilder.expiresAt(pExpiresAt);
    auditSvc.saveAuditEvent(auditBuilder.build());
  }

  private void auditApiUserRolesChanged(
      ApiUser pApiUser,
      Organization pOrganization,
      Group pGroup,
      Set<Role> pNewRoles,
      AuditInfo pAuditInfo) {
    ApiUserAudit.Builder auditBuilder =
        initAuditBuilder(pApiUser, API_KEY_ROLES_CHANGED, pAuditInfo);
    auditBuilder.newRoles(pNewRoles);

    if (pOrganization != null) {
      auditBuilder.orgId(pOrganization.getId());
    }

    if (pGroup != null) {
      auditBuilder.groupId(pGroup.getId());
    }

    auditSvc.saveAuditEvent(auditBuilder.build());
  }

  private void auditApiUserDescriptionChanged(
      ApiUser pApiUser, String pDescription, AuditInfo pAuditInfo) {
    ApiUserAudit.Builder auditBuilder =
        initAuditBuilder(pApiUser, API_KEY_DESCRIPTION_CHANGED, pAuditInfo);
    auditBuilder.description(pDescription);
    auditBuilder.orgId(pApiUser.getOrgId());
    auditSvc.saveAuditEvent(auditBuilder.build());
  }

  private void auditApiUserDeleted(ApiUser pApiUser, AuditInfo pAuditInfo) {
    ApiUserAudit.Builder auditBuilder = initAuditBuilder(pApiUser, API_KEY_DELETED, pAuditInfo);
    auditBuilder.orgId(pApiUser.getOrgId());
    auditSvc.saveAuditEvent(auditBuilder.build());
    auditApiUserForEachGroup(pApiUser, API_KEY_REMOVED_FROM_GROUP, pAuditInfo);
  }

  private void auditApiUserAllowListEvent(
      ApiUser pApiUser, String pIpAddress, Type pAuditType, AuditInfo pAuditInfo) {
    var auditBuilder = initAuditBuilder(pApiUser, pAuditType, pAuditInfo);
    auditBuilder.ipAddress(pIpAddress);
    auditBuilder.orgId(pApiUser.getOrgId());

    auditSvc.saveAuditEvent(auditBuilder.build());
  }

  private ApiUserAudit.Builder initAuditBuilder(
      ApiUser pApiUser, Type pType, AuditInfo pAuditInfo) {

    ApiUserAudit.Builder auditBuilder = new ApiUserAudit.Builder(pType, pAuditInfo);
    auditBuilder.publicKey(pApiUser.getUsername());

    return auditBuilder;
  }

  private ApiUser getApiUser(ObjectId pApiUserId) {
    AppUser appUser = userSvc.findById(pApiUserId);
    return appUser == null ? null : getApiUser(appUser);
  }

  private ApiUser getApiUser(String pUsername) {
    AppUser appUser = userSvc.findByUsername(pUsername);
    return appUser == null ? null : getApiUser(appUser);
  }

  private ApiUser getApiUser(AppUser pBackingUser) {
    if (pBackingUser.isDeleted()) {
      return null;
    }

    List<UserApiKey> userApiKeys = userApiKeySvc.findByUserId(pBackingUser.getId());

    if (CollectionUtils.isEmpty(userApiKeys)) {
      String msg =
          String.format(
              "Active API backing user %s was found without a UserApiKey. Active API backing users"
                  + " should always have a one UserApiKey.",
              pBackingUser.getId());
      LOG.error(
          "Active API backing user {} was found without a UserApiKey. Active API backing users"
              + " should always have a one UserApiKey.",
          pBackingUser.getId());
      throw new RuntimeException(msg, new SvcException(AppUserErrorCode.API_KEY_NOT_FOUND));
    }

    return getApiUser(pBackingUser, userApiKeys.get(0));
  }

  private ApiUser getApiUser(AppUser pBackingUser, UserApiKey pBackingKey) {
    if (pBackingUser.isDeleted()) {
      return null;
    }

    ApiUser.ApiUserBuilder apiUserBuilder =
        ApiUser.builder()
            .username(pBackingUser.getUsername())
            .privateKey(pBackingKey.getKey())
            .desc(pBackingKey.getDescription())
            .userId(pBackingUser.getId())
            .userApiKeyId(pBackingKey.getId())
            .created(pBackingUser.getCreated())
            .expiresAt(pBackingKey.getExpiresAt())
            .lastUsed(pBackingKey.getLastUsed())
            .keyType(pBackingKey.getKeyType())
            .roleAssignments(pBackingUser.getRoles())
            .backingUser(pBackingUser)
            .shouldApplyOrgUiAccessListForApi(pBackingUser.getShouldApplyOrgUiAccessListForApi());

    List<ObjectId> orgIds = pBackingUser.getOrgIds();
    if (!CollectionUtils.isEmpty(orgIds)) {
      apiUserBuilder.orgId(orgIds.get(0));
    }

    return apiUserBuilder.build();
  }

  private List<ApiUser> getApiUsersFromAppUsers(
      List<AppUser> pAppUsers, boolean pIncludeInternalApiUsers) {
    if (CollectionUtils.isEmpty(pAppUsers)) {
      return List.of();
    }

    // Workaround to ensure only one db call. It takes into account that there is guaranteed to be
    // one apiKey per appUser

    Map<ObjectId, AppUser> appUsersById =
        pAppUsers.stream().collect(Collectors.toMap(AppUser::getId, appUser -> appUser));
    List<UserApiKey> userApiKeys =
        userApiKeySvc.findByUserIds(appUsersById.keySet(), pIncludeInternalApiUsers);

    return userApiKeys.stream()
        .map(userApiKey -> getApiUser(appUsersById.get(userApiKey.getUserId()), userApiKey))
        .filter(Objects::nonNull)
        .sorted(Comparator.comparing(ApiUser::getUsername))
        .collect(Collectors.toList());
  }
}
