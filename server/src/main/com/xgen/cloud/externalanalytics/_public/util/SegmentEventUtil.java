package com.xgen.cloud.externalanalytics._public.util;

import static com.xgen.cloud.common.metrics._public.constants.MonitoringConstants.ATLAS_GROWTH_PROM_NAMESPACE;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.mongodb.BasicDBObject;
import com.mongodb.ReadPreference;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.TeamRoleAssignment;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.jackson._public.CustomJacksonJsonProvider;
import com.xgen.cloud.externalanalytics._public.helper.SegmentTrackingHelper;
import com.xgen.cloud.externalanalytics._public.model.AccountAuthMfaFactorsEvent;
import com.xgen.cloud.externalanalytics._public.model.AccountAuthMfaVerificationEvent;
import com.xgen.cloud.externalanalytics._public.model.AccountEmailVerifiedEvent;
import com.xgen.cloud.externalanalytics._public.model.AccountMfaEnrollmentChangeEvent;
import com.xgen.cloud.externalanalytics._public.model.ActiveOrganizationUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.model.AtlasCliEvent;
import com.xgen.cloud.externalanalytics._public.model.AutoIndexingDisabledEvent;
import com.xgen.cloud.externalanalytics._public.model.AutoIndexingEnabledEvent;
import com.xgen.cloud.externalanalytics._public.model.AutoIndexingFailedIndexBuildEvent;
import com.xgen.cloud.externalanalytics._public.model.AutoIndexingIndexDroppedEvent;
import com.xgen.cloud.externalanalytics._public.model.AutoIndexingIndexNotCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.AutoIndexingRecommendedEvent;
import com.xgen.cloud.externalanalytics._public.model.AutoIndexingToggledOnEvent;
import com.xgen.cloud.externalanalytics._public.model.AzureNativeUserPasswordSetEvent;
import com.xgen.cloud.externalanalytics._public.model.BillingCostExplorerQueryEvent;
import com.xgen.cloud.externalanalytics._public.model.BillingDocumentDownloadEvent;
import com.xgen.cloud.externalanalytics._public.model.BillingGetInvoicesEvent;
import com.xgen.cloud.externalanalytics._public.model.BillingUsageDetailsCsvExportEvent;
import com.xgen.cloud.externalanalytics._public.model.BillingUsageDetailsQueryEvent;
import com.xgen.cloud.externalanalytics._public.model.ChartsActivationFailedEvent;
import com.xgen.cloud.externalanalytics._public.model.ChurnSurveyEvent;
import com.xgen.cloud.externalanalytics._public.model.CloudManagerOrganizationRequestSupportEvent;
import com.xgen.cloud.externalanalytics._public.model.ClusterDraftAbandonedEvent;
import com.xgen.cloud.externalanalytics._public.model.ClusterDraftSavedEvent;
import com.xgen.cloud.externalanalytics._public.model.ClusterEvent;
import com.xgen.cloud.externalanalytics._public.model.ClusterMoreConfigurationOptionsEditedEvent;
import com.xgen.cloud.externalanalytics._public.model.ClusterProvisionedEvent;
import com.xgen.cloud.externalanalytics._public.model.CsvInvoiceExportEvent;
import com.xgen.cloud.externalanalytics._public.model.CustomDbRoleAddedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataFederationQueryLimitEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelineCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelineDatasetDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelineDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelinePausedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelineResumedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakePipelineUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataLakeQueryLogsDownloadedEvent;
import com.xgen.cloud.externalanalytics._public.model.DataProtectionEvent;
import com.xgen.cloud.externalanalytics._public.model.DatabaseUserEvent;
import com.xgen.cloud.externalanalytics._public.model.EmailChangeRequestCompletedEvent;
import com.xgen.cloud.externalanalytics._public.model.EmailChangedEvent;
import com.xgen.cloud.externalanalytics._public.model.EmailLinkClickedEvent;
import com.xgen.cloud.externalanalytics._public.model.EmailOpenedEvent;
import com.xgen.cloud.externalanalytics._public.model.EmailSentEvent;
import com.xgen.cloud.externalanalytics._public.model.ExperimentViewed;
import com.xgen.cloud.externalanalytics._public.model.FederatedOrgAddedEvent;
import com.xgen.cloud.externalanalytics._public.model.FirstTimeCloudLoginEvent;
import com.xgen.cloud.externalanalytics._public.model.FlexConsultingPurchaseEvent;
import com.xgen.cloud.externalanalytics._public.model.GetInvoiceEvent;
import com.xgen.cloud.externalanalytics._public.model.IPWhitelistEntryAddedEvent;
import com.xgen.cloud.externalanalytics._public.model.Identify;
import com.xgen.cloud.externalanalytics._public.model.IdentityProviderActivatedEvent;
import com.xgen.cloud.externalanalytics._public.model.IdentityProviderDeactivatedEvent;
import com.xgen.cloud.externalanalytics._public.model.IdentityProviderEvent;
import com.xgen.cloud.externalanalytics._public.model.InvitationAcceptedEvent;
import com.xgen.cloud.externalanalytics._public.model.InvitationEvent;
import com.xgen.cloud.externalanalytics._public.model.InvitationSentEvent;
import com.xgen.cloud.externalanalytics._public.model.LogStreamingEvent;
import com.xgen.cloud.externalanalytics._public.model.MdbMcpEvent;
import com.xgen.cloud.externalanalytics._public.model.MekoEvents;
import com.xgen.cloud.externalanalytics._public.model.MigrationEvent;
import com.xgen.cloud.externalanalytics._public.model.MigrationOrgLinkEvent;
import com.xgen.cloud.externalanalytics._public.model.NaturalLanguageQuerySubmissionEvent;
import com.xgen.cloud.externalanalytics._public.model.OnDemandIngestionTriggeredEvent;
import com.xgen.cloud.externalanalytics._public.model.OrgGenAiFeaturesDisabledEvent;
import com.xgen.cloud.externalanalytics._public.model.OrgGenAiFeaturesEnabledEvent;
import com.xgen.cloud.externalanalytics._public.model.OrgStreamCrossProjectDisabledEvent;
import com.xgen.cloud.externalanalytics._public.model.OrgStreamCrossProjectEnabledEvent;
import com.xgen.cloud.externalanalytics._public.model.OrganizationCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.OrganizationDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.PerformanceAdvisorRunEvent;
import com.xgen.cloud.externalanalytics._public.model.PersonalizationWizardEvent;
import com.xgen.cloud.externalanalytics._public.model.PersonalizationWizardShownEvent;
import com.xgen.cloud.externalanalytics._public.model.PushBasedLogExportEvent;
import com.xgen.cloud.externalanalytics._public.model.RegionalOutageSimulationEvent;
import com.xgen.cloud.externalanalytics._public.model.RegistrationSucceededEvent;
import com.xgen.cloud.externalanalytics._public.model.SalesSoldProductActivatedEvent;
import com.xgen.cloud.externalanalytics._public.model.SampleDatasetLoadedEvent;
import com.xgen.cloud.externalanalytics._public.model.SchemaAdvisorLoadedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchAlertAcknowledgedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchAlertOpenedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchAnalyzerDefinitionUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchDeploymentCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchDeploymentDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchDeploymentUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchIndexBuildFailedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchIndexCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchIndexDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.SearchIndexUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.model.SecurityContactModifiedEvent;
import com.xgen.cloud.externalanalytics._public.model.SegmentEvent;
import com.xgen.cloud.externalanalytics._public.model.SegmentGroupEvent;
import com.xgen.cloud.externalanalytics._public.model.SegmentGroupEvent.Traits;
import com.xgen.cloud.externalanalytics._public.model.SelfServePaymentMethodAddedEvent;
import com.xgen.cloud.externalanalytics._public.model.SelfServeProductActivatedEvent;
import com.xgen.cloud.externalanalytics._public.model.ServerlessAlertAcknowledgedEvent;
import com.xgen.cloud.externalanalytics._public.model.ServerlessAlertOpenedEvent;
import com.xgen.cloud.externalanalytics._public.model.ServerlessAutoIndexingConstraintNotMetEvent;
import com.xgen.cloud.externalanalytics._public.model.ServerlessAutoIndexingDroppedIndexEvent;
import com.xgen.cloud.externalanalytics._public.model.ServerlessAutoIndexingFailedIndexEvent;
import com.xgen.cloud.externalanalytics._public.model.ServerlessAutoIndexingRecommendedIndexesEvent;
import com.xgen.cloud.externalanalytics._public.model.ServerlessAutoIndexingToggledEvent;
import com.xgen.cloud.externalanalytics._public.model.SharedTierMetricsReportingEvent;
import com.xgen.cloud.externalanalytics._public.model.SignedInEvent;
import com.xgen.cloud.externalanalytics._public.model.SmartLinkRedirectedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorConnectionCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorConnectionDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorConnectionUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorConnectionsListedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorInstanceCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorInstanceDeletedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorInstanceDescribedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorInstanceUpdatedEvent;
import com.xgen.cloud.externalanalytics._public.model.StreamProcessorInstancesListedEvent;
import com.xgen.cloud.externalanalytics._public.model.SupportPortalRedirectEvent;
import com.xgen.cloud.externalanalytics._public.model.TeamAccessGrantedToProject;
import com.xgen.cloud.externalanalytics._public.model.TeamCreatedEvent;
import com.xgen.cloud.externalanalytics._public.model.TestAssignmentAllocatedEvent;
import com.xgen.cloud.externalanalytics._public.model.TextIndexDetectedEvent;
import com.xgen.cloud.externalanalytics._public.model.TextSlowQueryPerformedEvent;
import com.xgen.cloud.externalanalytics._public.model.UserAddedToGroupEvent;
import com.xgen.cloud.externalanalytics._public.model.VerificationEmailSentEvent;
import com.xgen.cloud.externalanalytics._public.model.VpcPeerEvent;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.cloud.user._public.svc.UserSvc;
import io.prometheus.client.Counter;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;
import org.bson.types.ObjectId;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Singleton
public class SegmentEventUtil {

  public static final String INVALID_TRACKING_EVENT_TYPE_ERROR_NAME =
      "segmentEventUtil_tracking_event_name_error_total";

  public static final String AUID_ABSENCE_NAME = "segmentEventUtil_auid_absence_total";
  public static final String OKTA_ID_NULL_NAME = "segmentEventUtil_okta_id_null_total";
  public static final String ORG_ID_NULL_NAME = "segmentEventUtil_org_id_null_total";
  private static final Counter INVALID_TRACKING_EVENT_TYPE_ERROR_COUNTER =
      Counter.build()
          .name(INVALID_TRACKING_EVENT_TYPE_ERROR_NAME)
          .help("getSegmentEventFromBasicDBObject counter for invalid tracking event type error")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();
  private static final String FALLBACK_ANONYMOUS_ID_NAME =
      "segmentEventUtil_fallback_anonymous_id_used_total";
  private static final Counter FALLBACK_ANONYMOUS_ID_COUNTER =
      Counter.build()
          .name(FALLBACK_ANONYMOUS_ID_NAME)
          .help("counter for when an event must fall back to the default Segment user id")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();
  private static final String FIND_USER_FOR_GROUP_NAME =
      "segmentEventUtil_find_user_for_group_error_total";
  private static final Counter FIND_USER_FOR_GROUP_ERROR =
      Counter.build()
          .name(FIND_USER_FOR_GROUP_NAME)
          .help("Unable to extrapolate user from group data")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .labelNames("eventType")
          .register();
  private static final Counter AUID_ABSENCE_COUNTER =
      Counter.build()
          .name(AUID_ABSENCE_NAME)
          .help("counter hashing AppUser::getOktaUserId to get auid in SegmentEventUtil")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();
  private static final Counter OKTA_ID_NULL_COUNTER =
      Counter.build()
          .name(OKTA_ID_NULL_NAME)
          .help(
              "counter for using Cloud ID as the primary ID when Okta ID is null in"
                  + " SegmentEventUtil")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .register();

  private static final Counter ORG_ID_NULL_COUNTER =
      Counter.build()
          .name(ORG_ID_NULL_NAME)
          .help(
              "counter for tracking when we have an empty orgId when trying to retrieve a user in"
                  + " SegmentEventUtil")
          .namespace(ATLAS_GROWTH_PROM_NAMESPACE)
          .labelNames("sourceFunction")
          .register();

  private static final Logger LOG = LoggerFactory.getLogger(SegmentEventUtil.class);
  private static final String _fallbackSegmentUserId = "5cddc5f71f1be920943ddf51";
  private final UserSvc _userSvc;
  private final GroupSvc _groupSvc;
  private final AppSettings _appSettings;
  private final ObjectMapper _mapper;

  @Inject
  public SegmentEventUtil(
      final UserSvc userSvc, final GroupSvc groupSvc, final AppSettings appSettings) {
    _userSvc = userSvc;
    _groupSvc = groupSvc;
    _appSettings = appSettings;
    _mapper =
        CustomJacksonJsonProvider.createObjectMapper().setSerializationInclusion(Include.NON_NULL);
  }

  /**
   * If the user is from a local environment, we will use a hard-coded user. Else if, the userId is
   * specified we will use it to look up a user and return user if it exists. Otherwise, we will
   * attempt to use groupId to return a group owner, or orgId to return an org owner
   */
  public AppUser getSegmentTrackingUser(final SegmentEvent segmentEvent) {
    return getUserForLocal()
        .or(() -> Optional.ofNullable(segmentEvent.getUserId()).map(_userSvc::findById))
        .or(
            () ->
                getSegmentTrackingUserForGroup(
                    segmentEvent.getContext().getGroupId(), segmentEvent.getEvent()))
        .or(
            () ->
                getSegmentTrackingUserForOrganization(
                    segmentEvent.getProperties().getOrgId(), "getSegmentTrackingUser"))
        .orElse(null);
  }

  public Optional<AppUser> getUserForLocal() {
    if (_appSettings.getSegmentDefaultUserId() != null) {
      final Date now = new Date();
      final AppUser segmentUser = new AppUser();
      segmentUser.setId(new ObjectId(_appSettings.getSegmentDefaultUserId()));
      segmentUser.setCreated(now);
      segmentUser.setPrimaryEmail("<EMAIL>");
      segmentUser.setFirstName("Local");
      segmentUser.setLastName("Segment");
      segmentUser.setPhoneNumber("************");
      segmentUser.setEmployer("The Company");
      segmentUser.setJobResponsibility("The Boss");
      segmentUser.setIsoA2CountryCode("US");
      segmentUser.setOktaUserId("localOktaUserId");
      return Optional.of(segmentUser);
    }

    return Optional.empty();
  }

  public Optional<AppUser> getSegmentTrackingUserForGroup(
      final ObjectId groupId, final String eventType) {
    if (groupId == null) {
      LOG.warn("No groupId available for eventType={}", eventType);
      return Optional.empty();
    }
    final List<AppUser> groupOwners = _userSvc.findLocalOwnersByGroupId(groupId);

    // If the group has group owners, use one of them
    if (!groupOwners.isEmpty()) {
      return Optional.of(groupOwners.get(0));
    }

    // Otherwise, use any user from a team with the GROUP_OWNER role
    final Group group = _groupSvc.findById(groupId);

    if (group == null) {
      // Do not log errors or increment counter if the event is "Account Email Verified Event"
      if (!eventType.equals(AccountEmailVerifiedEvent.EVENT_TYPE)) {
        LOG.warn(
            "Unable to find group for groupId={} when looking up group owner for Segment,"
                + " eventType={}",
            groupId,
            eventType);
        incrementFindUserForGroupErrorCounter(eventType);
      }
      return Optional.empty();
    }

    Optional<TeamRoleAssignment> ownerTeam = Optional.empty();
    try {
      ownerTeam =
          group.getTeams().stream()
              .filter(t -> t.getRoles().contains(Role.GROUP_OWNER))
              .findFirst();
    } catch (final Exception e) {
      LOG.error(
          "Error finding team for groupId={} when looking up group owner for Segment, eventType={}",
          groupId,
          eventType,
          e);
    }

    // Fall back to org owner if no group owner teams
    if (ownerTeam.isEmpty()) {
      Optional<AppUser> orgOwner =
          getSegmentTrackingUserForOrganization(group.getOrgId(), "getSegmentTrackingUserForGroup");

      if (orgOwner.isEmpty()) {
        incrementFindUserForGroupErrorCounter(eventType);
      }

      return orgOwner;
    }

    final List<AppUser> teamMembers = _userSvc.findLocalUsersByTeamId(ownerTeam.get().getId());

    return teamMembers.stream().findFirst();
  }

  /**
   * Will return the first user it finds of the below (in order):
   *
   * <ol>
   *   <li>non-api owner user with Okta ID
   *   <li>non-api user with Okta ID from a team with GROUP_OWNER role
   *   <li>api user with Okta ID
   *   <li>non-api owner user
   *   <li>non-api user from a team with GROUP_OWNER role
   *   <li>org owner
   *   <li>null
   * </ol>
   */
  public Optional<AppUser> getSegmentTrackingUserForGroup(final Group pGroup) {
    if (pGroup == null || pGroup.getId() == null) {
      LOG.warn("Cannot look up segment tracking user from null group.");
      return Optional.empty();
    }
    final List<AppUser> nonApiOwners = _userSvc.findLocalOwnersByGroupId(pGroup.getId());

    // Return the first non-api owner user with Okta ID if they exists.
    final Optional<AppUser> nonApiOwnerWithOktaId =
        nonApiOwners.stream().filter(o -> o.getOktaUserId() != null).findFirst();
    if (nonApiOwnerWithOktaId.isPresent()) {
      return nonApiOwnerWithOktaId;
    }

    // Else, return the first non-api user with Okta ID from a team with GROUP_OWNER role, if they
    // exist.
    final Optional<TeamRoleAssignment> ownerTeam =
        pGroup.getTeams().stream().filter(t -> t.getRoles().contains(Role.GROUP_OWNER)).findFirst();
    List<AppUser> nonApiTeamMembers = null;
    if (ownerTeam.isPresent()) {
      nonApiTeamMembers = _userSvc.findLocalUsersByTeamId(ownerTeam.get().getId());
      final Optional<AppUser> localTeamMemberWithOktaId =
          nonApiTeamMembers.stream().filter(o -> o.getOktaUserId() != null).findFirst();
      if (localTeamMemberWithOktaId.isPresent()) {
        return localTeamMemberWithOktaId;
      }
    }

    // Else, return the first api user with Okta ID if they exist.
    final Optional<AppUser> apiUserWithOktaId =
        _userSvc.findApiUsersByGroupId(pGroup.getId()).stream()
            .filter(o -> o.getOktaUserId() != null)
            .findFirst();
    if (apiUserWithOktaId.isPresent()) {
      return apiUserWithOktaId;
    }

    // Else, return the first non-api owner user if they exist.
    // As we've already established no users in `nonApiTeamMembers` have an Okta ID, this returned
    // user will not have an Okta ID.
    if (!nonApiOwners.isEmpty()) {
      return Optional.of(nonApiOwners.get(0));
    }

    // Else, return the first non-api user from a team with GROUP_OWNER role if they exist.
    if (nonApiTeamMembers != null && !nonApiTeamMembers.isEmpty()) {
      return Optional.of(nonApiTeamMembers.get(0));
    }

    // Else, fall back to org owner if available
    return getSegmentTrackingUserForOrganization(
        pGroup.getOrgId(), "getSegmentTrackingUserForGroup");
  }

  public Optional<AppUser> getSegmentTrackingUserForOrganization(
      final ObjectId orgId, final String functionName) {
    if (orgId != null) {
      return _userSvc.findLocalOwnersByOrgId(orgId, ReadPreference.secondaryPreferred()).stream()
          .findFirst();
    } else {
      LOG.warn("No orgId provided, skipping findLocalOwnersByOrgId");

      incrementNullOrgIdCounter(functionName);
      return Optional.empty();
    }
  }

  public String getUserTrackingId(final AppUser pAppUser) {
    if (pAppUser == null) {
      return null;
    }

    final String auid = pAppUser.getAuid();
    if (auid != null) {
      return auid;
    }
    LOG.info("Okta ID is null for user={}, using Cloud user ID instead", pAppUser.getId());
    return pAppUser.getId().toString();
  }

  public JSONObject getJSONObjectFromSegmentEvent(final SegmentEvent segmentEvent)
      throws JsonProcessingException {
    return new JSONObject(_mapper.writeValueAsString(segmentEvent));
  }

  public JSONObject getJSONObjectFromIdentify(final Identify identify)
      throws JsonProcessingException {
    return new JSONObject(_mapper.writeValueAsString(identify));
  }

  public SegmentEvent getSegmentEventFromBasicDBObject(final BasicDBObject basicDBObject) {
    final String event = basicDBObject.getString(SegmentEvent.FieldDefs.EVENT);

    switch (event) {
      case InvitationSentEvent.EVENT_TYPE:
        return new InvitationSentEvent(basicDBObject);
      case InvitationAcceptedEvent.EVENT_TYPE:
        return new InvitationAcceptedEvent(basicDBObject);
      case InvitationEvent.EVENT_TYPE:
        return new InvitationEvent(basicDBObject);
      case ActiveOrganizationUpdatedEvent.EVENT_TYPE:
        return new ActiveOrganizationUpdatedEvent(basicDBObject);
      case AccountEmailVerifiedEvent.EVENT_TYPE:
        return new AccountEmailVerifiedEvent(basicDBObject);
      case AtlasCliEvent.CLI_EVENT_TYPE, AtlasCliEvent.DOCKER_EVENT_TYPE:
        return new AtlasCliEvent(basicDBObject);
      case MekoEvents.CLUSTERS_EVENT_TYPE,
          MekoEvents.DEPLOYMENTS_EVENT_TYPE,
          MekoEvents.OPERATORS_EVENT_TYPE:
        return new MekoEvents(basicDBObject);
      case MdbMcpEvent.MONGO_DB_MCP_EVENT:
        return new MdbMcpEvent(basicDBObject);
      case ChurnSurveyEvent.EVENT_TYPE:
        return new ChurnSurveyEvent(basicDBObject);
      case EmailSentEvent.EVENT_TYPE:
        return new EmailSentEvent(basicDBObject);
      case EmailOpenedEvent.EVENT_TYPE:
        return new EmailOpenedEvent(basicDBObject);
      case EmailLinkClickedEvent.EVENT_TYPE:
        return new EmailLinkClickedEvent(basicDBObject);
      case EmailChangeRequestCompletedEvent.EVENT_TYPE:
        return new EmailChangeRequestCompletedEvent(basicDBObject);
      case SmartLinkRedirectedEvent.EVENT_TYPE:
        return new SmartLinkRedirectedEvent(basicDBObject);
      case TestAssignmentAllocatedEvent.REAL_ASSIGNMENT_EVENT_NAME:
      case TestAssignmentAllocatedEvent.GHOST_ASSIGNMENT_EVENT_NAME:
        return new TestAssignmentAllocatedEvent(basicDBObject);
      case VerificationEmailSentEvent.EVENT_TYPE:
        return new VerificationEmailSentEvent(basicDBObject);
      case PersonalizationWizardEvent.PERSONALIZATION_WIZARD_FORM_EVENT_NAME:
      case PersonalizationWizardEvent.PERSONALIZATION_WIZARD_FORM_UPDATED_EVENT_NAME:
        return new PersonalizationWizardEvent(basicDBObject);
      case ExperimentViewed.REAL_ASSIGNMENT_EVENT_NAME:
      case ExperimentViewed.GHOST_ASSIGNMENT_EVENT_NAME:
        return new ExperimentViewed(basicDBObject);
      case PersonalizationWizardShownEvent.EVENT_TYPE:
        return new PersonalizationWizardShownEvent(basicDBObject);
      case ClusterProvisionedEvent.EVENT_TYPE:
        return new ClusterProvisionedEvent(basicDBObject);
      case ClusterDraftAbandonedEvent.EVENT_TYPE:
        return new ClusterDraftAbandonedEvent(basicDBObject);
      case ClusterDraftSavedEvent.EVENT_TYPE:
        return new ClusterDraftSavedEvent(basicDBObject);
      case SampleDatasetLoadedEvent.EVENT_TYPE:
        return new SampleDatasetLoadedEvent(basicDBObject);
      case RegionalOutageSimulationEvent.START_EVENT_TYPE:
      case RegionalOutageSimulationEvent.END_EVENT_TYPE:
        return new RegionalOutageSimulationEvent(basicDBObject);
      case DataFederationQueryLimitEvent.EVENT_TYPE:
        return new DataFederationQueryLimitEvent(basicDBObject);
      case DataProtectionEvent.ENABLE_EVENT_TYPE:
      case DataProtectionEvent.DISABLE_EVENT_TYPE:
      case DataProtectionEvent.UPDATE_EVENT_TYPE:
      case DataProtectionEvent.DISABLEMENT_APPROVAL_EVENT_TYPE:
        return new DataProtectionEvent(basicDBObject);
      case ChartsActivationFailedEvent.EVENT_TYPE:
        return new ChartsActivationFailedEvent(basicDBObject);
      case ServerlessAutoIndexingRecommendedIndexesEvent.EVENT_TYPE:
        return new ServerlessAutoIndexingRecommendedIndexesEvent(basicDBObject);
      case ServerlessAutoIndexingFailedIndexEvent.EVENT_TYPE:
        return new ServerlessAutoIndexingFailedIndexEvent(basicDBObject);
      case ServerlessAutoIndexingToggledEvent.EVENT_TYPE:
        return new ServerlessAutoIndexingToggledEvent(basicDBObject);
      case ServerlessAutoIndexingDroppedIndexEvent.EVENT_TYPE:
        return new ServerlessAutoIndexingDroppedIndexEvent(basicDBObject);
      case ServerlessAutoIndexingConstraintNotMetEvent.EVENT_TYPE:
        return new ServerlessAutoIndexingConstraintNotMetEvent(basicDBObject);
      case FirstTimeCloudLoginEvent.EVENT_TYPE:
        return new FirstTimeCloudLoginEvent(basicDBObject);
      case BillingCostExplorerQueryEvent.QUERY_CREATED_EVENT_TYPE:
      case BillingCostExplorerQueryEvent.QUERY_DOWNLOADED_EVENT_TYPE:
        return new BillingCostExplorerQueryEvent(basicDBObject);
      case CsvInvoiceExportEvent.EVENT_TYPE:
        return new CsvInvoiceExportEvent(basicDBObject);
      case GetInvoiceEvent.EVENT_TYPE:
        return new GetInvoiceEvent(basicDBObject);
      case BillingGetInvoicesEvent.EVENT_TYPE:
        return new BillingGetInvoicesEvent(basicDBObject);
      case BillingDocumentDownloadEvent.EVENT_TYPE:
        return new BillingDocumentDownloadEvent(basicDBObject);
      case PushBasedLogExportEvent.PUSH_BASED_LOG_EXPORT_ENABLED_EVENT_TYPE:
      case PushBasedLogExportEvent.PUSH_BASED_LOG_EXPORT_UPDATED_EVENT_TYPE:
      case PushBasedLogExportEvent.PUSH_BASED_LOG_EXPORT_DISABLED_EVENT_TYPE:
        return new PushBasedLogExportEvent(basicDBObject);
      case LogStreamingEvent.LOG_STREAMING_ENABLED_EVENT_TYPE:
      case LogStreamingEvent.LOG_STREAMING_DISABLED_EVENT_TYPE:
      case LogStreamingEvent.LOG_STREAMING_UPDATED_EVENT_TYPE:
        return new LogStreamingEvent(basicDBObject);
      case CustomDbRoleAddedEvent.EVENT_TYPE:
        return new CustomDbRoleAddedEvent(basicDBObject);
      case StreamProcessorInstanceCreatedEvent.EVENT_TYPE:
        return new StreamProcessorInstanceCreatedEvent(basicDBObject);
      case StreamProcessorInstanceUpdatedEvent.EVENT_TYPE:
        return new StreamProcessorInstanceUpdatedEvent(basicDBObject);
      case StreamProcessorInstanceDeletedEvent.EVENT_TYPE:
        return new StreamProcessorInstanceDeletedEvent(basicDBObject);
      case StreamProcessorInstancesListedEvent.EVENT_TYPE:
        return new StreamProcessorInstancesListedEvent(basicDBObject);
      case StreamProcessorInstanceDescribedEvent.EVENT_TYPE:
        return new StreamProcessorInstanceDescribedEvent(basicDBObject);
      case StreamProcessorConnectionsListedEvent.EVENT_TYPE:
        return new StreamProcessorConnectionsListedEvent(basicDBObject);
      case StreamProcessorConnectionDeletedEvent.EVENT_TYPE:
        return new StreamProcessorConnectionDeletedEvent(basicDBObject);
      case StreamProcessorConnectionCreatedEvent.EVENT_TYPE:
        return new StreamProcessorConnectionCreatedEvent(basicDBObject);
      case StreamProcessorConnectionUpdatedEvent.EVENT_TYPE:
        return new StreamProcessorConnectionUpdatedEvent(basicDBObject);
      case NaturalLanguageQuerySubmissionEvent.EVENT_TYPE:
        return new NaturalLanguageQuerySubmissionEvent(basicDBObject);
      case BillingUsageDetailsQueryEvent.EVENT_TYPE:
        return new BillingUsageDetailsQueryEvent(basicDBObject);
      case BillingUsageDetailsCsvExportEvent.EVENT_TYPE:
        return new BillingUsageDetailsCsvExportEvent(basicDBObject);
      case OrganizationCreatedEvent.EVENT_TYPE:
        return new OrganizationCreatedEvent(basicDBObject);
      case OrganizationDeletedEvent.EVENT_TYPE:
        return new OrganizationDeletedEvent(basicDBObject);
      case IPWhitelistEntryAddedEvent.EVENT_TYPE:
        return new IPWhitelistEntryAddedEvent(basicDBObject);
      case EmailChangedEvent.EVENT_TYPE:
        return new EmailChangedEvent(basicDBObject);
      case AutoIndexingRecommendedEvent.EVENT_TYPE:
        return new AutoIndexingRecommendedEvent(basicDBObject);
      case AutoIndexingDisabledEvent.EVENT_TYPE:
        return new AutoIndexingDisabledEvent(basicDBObject);
      case AutoIndexingEnabledEvent.EVENT_TYPE:
        return new AutoIndexingEnabledEvent(basicDBObject);
      case AutoIndexingFailedIndexBuildEvent.EVENT_TYPE:
        return new AutoIndexingFailedIndexBuildEvent(basicDBObject);
      case AutoIndexingIndexDroppedEvent.EVENT_TYPE:
        return new AutoIndexingIndexDroppedEvent(basicDBObject);
      case AutoIndexingIndexNotCreatedEvent.EVENT_TYPE:
        return new AutoIndexingIndexNotCreatedEvent(basicDBObject);
      case AutoIndexingToggledOnEvent.EVENT_TYPE:
        return new AutoIndexingToggledOnEvent(basicDBObject);
      case CloudManagerOrganizationRequestSupportEvent.EVENT_TYPE:
        return new CloudManagerOrganizationRequestSupportEvent(basicDBObject);
      case ClusterMoreConfigurationOptionsEditedEvent.EVENT_TYPE:
        return new ClusterMoreConfigurationOptionsEditedEvent(basicDBObject);
      case DataLakePipelineCreatedEvent.EVENT_TYPE:
        return new DataLakePipelineCreatedEvent(basicDBObject);
      case DataLakePipelineDatasetDeletedEvent.EVENT_TYPE:
        return new DataLakePipelineDatasetDeletedEvent(basicDBObject);
      case DataLakePipelineDeletedEvent.EVENT_TYPE:
        return new DataLakePipelineDeletedEvent(basicDBObject);
      case DataLakePipelinePausedEvent.EVENT_TYPE:
        return new DataLakePipelinePausedEvent(basicDBObject);
      case DataLakePipelineResumedEvent.EVENT_TYPE:
        return new DataLakePipelineResumedEvent(basicDBObject);
      case DataLakePipelineUpdatedEvent.EVENT_TYPE:
        return new DataLakePipelineUpdatedEvent(basicDBObject);
      case DataLakeQueryLogsDownloadedEvent.EVENT_TYPE:
        return new DataLakeQueryLogsDownloadedEvent(basicDBObject);
      case DatabaseUserEvent.DATABASE_USER_ADDED_EVENT_TYPE:
      case DatabaseUserEvent.DATABASE_USER_UPDATED_EVENT_TYPE:
      case DatabaseUserEvent.DATABASE_USER_DELETED_EVENT_TYPE:
        return new DatabaseUserEvent(basicDBObject);
      case FederatedOrgAddedEvent.EVENT_TYPE:
        return new FederatedOrgAddedEvent(basicDBObject);
      case FlexConsultingPurchaseEvent.EVENT_TYPE:
        return new FlexConsultingPurchaseEvent(basicDBObject);
      case IdentityProviderEvent.IDENTITY_PROVIDER_CREATED_EVENT_TYPE:
      case IdentityProviderEvent.IDENTITY_PROVIDER_DELETED_EVENT_TYPE:
        return new IdentityProviderEvent(basicDBObject);
      case IdentityProviderActivatedEvent.EVENT_TYPE:
        return new IdentityProviderActivatedEvent(basicDBObject);
      case IdentityProviderDeactivatedEvent.EVENT_TYPE:
        return new IdentityProviderDeactivatedEvent(basicDBObject);
      case OnDemandIngestionTriggeredEvent.EVENT_TYPE:
        return new OnDemandIngestionTriggeredEvent(basicDBObject);
      case PerformanceAdvisorRunEvent.EVENT_TYPE:
        return new PerformanceAdvisorRunEvent(basicDBObject);
      case RegistrationSucceededEvent.EVENT_TYPE:
        return new RegistrationSucceededEvent(basicDBObject);
      case SalesSoldProductActivatedEvent.EVENT_TYPE:
        return new SalesSoldProductActivatedEvent(basicDBObject);
      case SchemaAdvisorLoadedEvent.EVENT_TYPE:
        return new SchemaAdvisorLoadedEvent(basicDBObject);
      case SearchAlertOpenedEvent.EVENT_TYPE:
        return new SearchAlertOpenedEvent(basicDBObject);
      case SearchAlertAcknowledgedEvent.EVENT_TYPE:
        return new SearchAlertAcknowledgedEvent(basicDBObject);
      case SearchAnalyzerDefinitionUpdatedEvent.EVENT_TYPE:
        return new SearchAnalyzerDefinitionUpdatedEvent(basicDBObject);
      case SearchIndexCreatedEvent.EVENT_TYPE:
        return new SearchIndexCreatedEvent(basicDBObject);
      case SearchIndexBuildFailedEvent.EVENT_TYPE:
        return new SearchIndexBuildFailedEvent(basicDBObject);
      case SearchIndexDeletedEvent.EVENT_TYPE:
        return new SearchIndexDeletedEvent(basicDBObject);
      case SearchIndexUpdatedEvent.EVENT_TYPE:
        return new SearchIndexUpdatedEvent(basicDBObject);
      case SearchDeploymentCreatedEvent.EVENT_TYPE:
        return new SearchDeploymentCreatedEvent(basicDBObject);
      case SearchDeploymentDeletedEvent.EVENT_TYPE:
        return new SearchDeploymentDeletedEvent(basicDBObject);
      case SearchDeploymentUpdatedEvent.EVENT_TYPE:
        return new SearchDeploymentUpdatedEvent(basicDBObject);
      case SecurityContactModifiedEvent.EVENT_TYPE:
        return new SecurityContactModifiedEvent(basicDBObject);
      case SelfServePaymentMethodAddedEvent.EVENT_TYPE:
        return new SelfServePaymentMethodAddedEvent(basicDBObject);
      case SelfServeProductActivatedEvent.EVENT_TYPE:
        return new SelfServeProductActivatedEvent(basicDBObject);
      case ServerlessAlertAcknowledgedEvent.EVENT_TYPE:
        return new ServerlessAlertAcknowledgedEvent(basicDBObject);
      case ServerlessAlertOpenedEvent.EVENT_TYPE:
        return new ServerlessAlertOpenedEvent(basicDBObject);
      case SharedTierMetricsReportingEvent.EVENT_TYPE:
        return new SharedTierMetricsReportingEvent(basicDBObject);
      case SignedInEvent.EVENT_TYPE:
        return new SignedInEvent(basicDBObject);
      case SupportPortalRedirectEvent.EVENT_TYPE:
        return new SupportPortalRedirectEvent(basicDBObject);
      case TeamAccessGrantedToProject.EVENT_TYPE:
        return new TeamAccessGrantedToProject(basicDBObject);
      case TeamCreatedEvent.EVENT_TYPE:
        return new TeamCreatedEvent(basicDBObject);
      case TextIndexDetectedEvent.EVENT_TYPE:
        return new TextIndexDetectedEvent(basicDBObject);
      case TextSlowQueryPerformedEvent.EVENT_TYPE:
        return new TextSlowQueryPerformedEvent(basicDBObject);
      case MigrationOrgLinkEvent.EVENT_TYPE:
        return new MigrationOrgLinkEvent(basicDBObject);
      case MigrationEvent.VALIDATION_SUCCEEDED_EVENT_TYPE:
      case MigrationEvent.VALIDATION_FAILED_EVENT_TYPE:
      case MigrationEvent.MIGRATION_STARTED_EVENT_TYPE:
      case MigrationEvent.MIGRATION_CUTOVER_READY_EVENT_TYPE:
      case MigrationEvent.MIGRATION_SUCCEEDED_EVENT_TYPE:
      case MigrationEvent.MIGRATION_FAILED_EVENT_TYPE:
      case MigrationEvent.MIGRATION_CANCELLED_EVENT_TYPE:
      case MigrationEvent.MIGRATION_EXPIRED_EVENT_TYPE:
        final MigrationEvent migrationEvent = new MigrationEvent(basicDBObject);
        hydrateMigrationEvent(migrationEvent);
        return migrationEvent;
      case VpcPeerEvent.VPC_PEER_REQUESTED_EVENT_TYPE:
      case VpcPeerEvent.VPC_PEER_ADDED_EVENT_TYPE:
        return new VpcPeerEvent(basicDBObject);
      case SegmentGroupEvent.EVENT_TYPE:
        return new SegmentGroupEvent(basicDBObject);
      case ClusterEvent.CLUSTER_CREATED_EVENT_TYPE:
      case ClusterEvent.CLUSTER_DELETED_EVENT_TYPE:
      case ClusterEvent.CLUSTER_PAUSED_EVENT_TYPE:
      case ClusterEvent.CLUSTER_RESUMED_EVENT_TYPE:
      case ClusterEvent.CLUSTER_EDITED_EVENT_TYPE:
      case ClusterEvent.RESTRICTED_EMPLOYEE_ACCESS_BYPASSED:
      case ClusterEvent.EMPLOYEE_ACCESS_BYPASS_REVOKED:
      case ClusterEvent.DEVICE_SYNC_DEBUG_ACCESS_GRANTED:
      case ClusterEvent.DEVICE_SYNC_DEBUG_ACCESS_REVOKED:
      case ClusterEvent.CLUSTER_DATABASE_LOG_ACCESS_GRANTED:
      case ClusterEvent.CLUSTER_DATABASE_LOG_ACCESS_REVOKED:
        return new ClusterEvent(basicDBObject);
      case OrgStreamCrossProjectDisabledEvent.EVENT_TYPE:
        return new OrgStreamCrossProjectDisabledEvent(basicDBObject);
      case OrgStreamCrossProjectEnabledEvent.EVENT_TYPE:
        return new OrgStreamCrossProjectEnabledEvent(basicDBObject);
      case OrgGenAiFeaturesDisabledEvent.EVENT_TYPE:
        return new OrgGenAiFeaturesDisabledEvent(basicDBObject);
      case OrgGenAiFeaturesEnabledEvent.EVENT_TYPE:
        return new OrgGenAiFeaturesEnabledEvent(basicDBObject);
      case UserAddedToGroupEvent.EVENT_TYPE:
        return new UserAddedToGroupEvent(basicDBObject);
      case AccountAuthMfaFactorsEvent.EVENT_TYPE:
        return new AccountAuthMfaFactorsEvent(basicDBObject);
      case AccountMfaEnrollmentChangeEvent.EVENT_TYPE:
        return new AccountMfaEnrollmentChangeEvent(basicDBObject);
      case AccountAuthMfaVerificationEvent.EVENT_TYPE:
        return new AccountAuthMfaVerificationEvent(basicDBObject);
      case AzureNativeUserPasswordSetEvent.EVENT_TYPE:
        return new AzureNativeUserPasswordSetEvent(basicDBObject);
      default:
        incrementInvalidTrackingEventTypeErrorCounter();
        throw new IllegalStateException(
            String.format("invalid Segment tracking event type encountered: %s", event));
    }
  }

  private void hydrateMigrationEvent(final MigrationEvent migrationEvent) {
    SegmentTrackingHelper.SegmentGroupType sourceGroupType =
        SegmentTrackingHelper.SegmentGroupType.UNKNOWN;
    final ObjectId sourceGroupId = migrationEvent.getProperties().getSourceGroupId();
    if (sourceGroupId != null) {
      final Group sourceGroup = _groupSvc.findById(sourceGroupId);
      sourceGroupType =
          sourceGroup == null
              ? SegmentTrackingHelper.SegmentGroupType
                  .OPS_MANAGER // defaults to Ops Manager if not found
              : SegmentTrackingHelper.SegmentGroupType.fromGroupType(sourceGroup.getGroupType());
    }
    migrationEvent.getProperties().setSourceGroupType(sourceGroupType.name());
  }

  protected void incrementAuidAbsenceCounter() {
    AUID_ABSENCE_COUNTER.inc();
  }

  private Traits hydrateUserIds(final AppUser appUser, final Traits orgTraits) {
    final Traits traits = orgTraits == null ? new Traits() : orgTraits;
    // Add cloud_user_id to traits or properties regardless of auid
    traits.setCloudUserId(appUser.getId());
    if (appUser.getAuid() != null) {
      traits.setAuid(appUser.getAuid());
    } else {
      // Set anonymousId to cloud user id if auid is null
      traits.setAnonymousId(getAnonymousIdForUser(appUser));
      LOG.info("AUID absent for userId={}", appUser.getId());
      incrementAuidAbsenceCounter();
    }
    return traits;
  }

  protected void incrementOktaIdIsNull() {
    OKTA_ID_NULL_COUNTER.inc();
  }

  protected String handleAuid(final AppUser appUser) {
    if (appUser == null) {
      return null;
    }

    final String auid = appUser.getAuid();
    if (auid == null) {
      LOG.info("Okta ID is null for user={}, using AnonymousId instead", appUser.getId());
      incrementOktaIdIsNull();
      return null;
    }
    return auid;
  }

  public SegmentGroupEvent buildSegmentGroupEvent(
      Organization organization, AppUser appUser, String domain, String eventName) {
    LOG.info(
        "Building Segment Group Event For Event Name:{} AppUser:{} OrgId: {}",
        eventName,
        appUser == null ? null : appUser.getUsername(),
        organization.getId());
    final String auid = handleAuid(appUser);
    final Traits orgTraits = getBasicGroupEventOrgTraits(organization);
    orgTraits.setOrgDomain(domain);
    final Traits traits = appUser == null ? orgTraits : hydrateUserIds(appUser, orgTraits);
    final SegmentGroupEvent segmentGroupEvent = SegmentGroupEvent.builder().traits(traits).build();
    if (auid == null) {
      final String anonymousId = getAnonymousIdForUser(appUser);
      segmentGroupEvent.setAnonymousId(anonymousId);
      LOG.info(
          "Unable to find valid primary analytics ID for user. Assigning anonymousId={} to user",
          anonymousId);
    } else {
      segmentGroupEvent.setUserId(auid);
    }
    return segmentGroupEvent;
  }

  public Traits getBasicGroupEventOrgTraits(Organization organization) {
    final Traits traits = new Traits();
    final SimpleDateFormat formatter =
        new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"); // ISO8601 format
    formatter.setTimeZone(TimeZone.getTimeZone("UTC"));

    traits.setOrganizationName(organization.getName());
    traits.setOrganizationId(organization.getId());
    traits.setOrgCreatorId(organization.getCreator());
    traits.setCreatedAt(formatter.format(organization.getCreated()));
    return traits;
  }

  public String getAnonymousIdForUser(final AppUser appUser) {
    if (appUser == null) {
      final String fallbackUserId =
          _appSettings.getSegmentDefaultUserId() != null
              ? _appSettings.getSegmentDefaultUserId()
              : _fallbackSegmentUserId;
      LOG.info("AppUser is null. Assigning anonymousId={} to user", fallbackUserId);
      incrementFallbackAnonymousIdCounter();
      return fallbackUserId;
    }

    return appUser.getId().toString();
  }

  @VisibleForTesting
  protected void incrementInvalidTrackingEventTypeErrorCounter() {
    INVALID_TRACKING_EVENT_TYPE_ERROR_COUNTER.inc();
  }

  @VisibleForTesting
  protected void incrementFallbackAnonymousIdCounter() {
    FALLBACK_ANONYMOUS_ID_COUNTER.inc();
  }

  @VisibleForTesting
  protected void incrementFindUserForGroupErrorCounter(final String eventType) {
    FIND_USER_FOR_GROUP_ERROR.labels(eventType).inc();
  }

  @VisibleForTesting
  protected void incrementNullOrgIdCounter(final String label) {
    ORG_ID_NULL_COUNTER.labels(label).inc();
  }
}
