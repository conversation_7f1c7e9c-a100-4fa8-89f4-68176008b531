package com.xgen.cloud.billingimport.gcp._private.dao;

import static com.mongodb.client.model.Accumulators.addToSet;
import static com.mongodb.client.model.Accumulators.max;
import static com.mongodb.client.model.Aggregates.group;
import static com.mongodb.client.model.Aggregates.match;
import static com.mongodb.client.model.Aggregates.project;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.lt;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Projections.computed;
import static com.mongodb.client.model.Projections.excludeId;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Accumulators;
import com.xgen.cloud.billingimport.gcp._public.dao.GcpSnapshotAggregatedUsageDao;
import com.xgen.cloud.billingimport.gcp._public.model.GCPFetchedBillingUniqueUsage;
import com.xgen.cloud.billingimport.gcp._public.model.GcpSnapshotAggregatedUsage;
import com.xgen.cloud.billingimport.gcp._public.model.GcpSnapshotAggregatedUsage.FieldDefs;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.db.legacy._public.svc.MongoSvc;
import com.xgen.svc.core.dao.base.BaseTDao;
import com.xgen.svc.core.dao.base.MongoIndex;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.bson.Document;
import org.bson.codecs.pojo.Convention;
import org.bson.codecs.pojo.Conventions;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

@Singleton
public class GcpSnapshotAggregatedUsageDaoImpl extends BaseTDao<GcpSnapshotAggregatedUsage>
    implements GcpSnapshotAggregatedUsageDao {

  private static final String ID = "_id";
  public static final String DB_NAME = "mmsdbcloudproviders";
  public static final String COLLECTION_NAME = "gcpSnapshotAggregatedUsage";
  private final AppSettings appSettings;

  @Inject
  public GcpSnapshotAggregatedUsageDaoImpl(
      final MongoSvc pMongoSvc, final AppSettings appSettings) {
    super(pMongoSvc, DB_NAME, COLLECTION_NAME);
    this.appSettings = appSettings;
  }

  @Override
  public List<MongoIndex> getIndexes() {
    final List<MongoIndex> list = super.getIndexes();
    if (!appSettings.getAppEnv().isLocalOrTest()) {
      // TTL to automatically delete old data. Index also used to delete data from failed jobs.
      list.add(
          MongoIndex.newWithoutValidation().key(FieldDefs.DATE_FIELD).ttl(Duration.ofDays(31 * 3)));
    }
    // To get usages for list of groupIds and date field range
    list.add(
        MongoIndex.newWithoutValidation().key(FieldDefs.GROUP_ID_FIELD).key(FieldDefs.DATE_FIELD));
    return list;
  }

  /**
   * Find by time range list.
   *
   * <p>[ { $match: { groupId: { $in: groupIds, }, $and: [ { date: { $gte: pStartTime, }, }, { date:
   * { $lt: pEndTime, }, }, ], }, }, { $group: { _id: { groupId: "$groupId", usageDate:
   * "$usageDate", clusterUniqueId: "$clusterUniqueId", clusterName: "$clusterName", gcpRegionName:
   * "$gcpRegionName", }, quantity: { $sum: "$quantity", }, }, }, { $project: { _id: 0, groupId:
   * "$_id.groupId", usageDate: "$_id.usageDate", clusterUniqueId: "$_id.clusterUniqueId",
   * clusterName: "$_id.clusterName", gcpRegionName: "$_id.gcpRegionName", quantity: "$quantity", },
   * }, ]
   *
   * @param pGroupIds list of group ids
   * @param pStartTime import start time
   * @param pEndTime import end time
   * @return the list
   */
  public List<GcpSnapshotAggregatedUsage> findByTimeRange(
      final Collection<ObjectId> pGroupIds, final Date pStartTime, final Date pEndTime) {
    return getCollection()
        .aggregate(
            Arrays.asList(
                match(
                    and(
                        in(FieldDefs.GROUP_ID_FIELD, pGroupIds),
                        gte(FieldDefs.DATE_FIELD, pStartTime),
                        lt(FieldDefs.DATE_FIELD, pEndTime))),
                group(
                    new Document(FieldDefs.GROUP_ID_FIELD, "$" + FieldDefs.GROUP_ID_FIELD)
                        .append(FieldDefs.USAGE_DATE_FIELD, "$" + FieldDefs.USAGE_DATE_FIELD)
                        .append(
                            FieldDefs.CLUSTER_UNIQUE_ID_FIELD,
                            "$" + FieldDefs.CLUSTER_UNIQUE_ID_FIELD)
                        .append(FieldDefs.CLUSTER_NAME_FIELD, "$" + FieldDefs.CLUSTER_NAME_FIELD)
                        .append(
                            FieldDefs.GCP_REGION_NAME_FIELD, "$" + FieldDefs.GCP_REGION_NAME_FIELD),
                    Accumulators.sum(FieldDefs.QUANTITY_FIELD, "$" + FieldDefs.QUANTITY_FIELD)),
                project(
                    new Document(FieldDefs.QUANTITY_FIELD, 1)
                        .append(ID, 0)
                        .append(FieldDefs.GROUP_ID_FIELD, "$" + ID + "." + FieldDefs.GROUP_ID_FIELD)
                        .append(
                            FieldDefs.USAGE_DATE_FIELD, "$" + ID + "." + FieldDefs.USAGE_DATE_FIELD)
                        .append(
                            FieldDefs.CLUSTER_UNIQUE_ID_FIELD,
                            "$" + ID + "." + FieldDefs.CLUSTER_UNIQUE_ID_FIELD)
                        .append(
                            FieldDefs.CLUSTER_NAME_FIELD,
                            "$" + ID + "." + FieldDefs.CLUSTER_NAME_FIELD)
                        .append(
                            FieldDefs.GCP_REGION_NAME_FIELD,
                            "$" + ID + "." + FieldDefs.GCP_REGION_NAME_FIELD))))
        .into(new ArrayList<>());
  }

  public void deleteOnOrAfterDate(final Date pDate) {
    getCollection().deleteMany(gte(FieldDefs.DATE_FIELD, pDate));
  }

  /**
   * Get all group IDs for which we have raw usage in a specified timeframe.
   *
   * @param startTime import start time
   * @param endTime import end time
   * @param maxUsageDateIncl the max usage date
   * @return the set of groupIds
   */
  public Set<ObjectId> findGroupIds(
      final Date startTime, final Date endTime, final Date maxUsageDateIncl) {

    List<Bson> query =
        Arrays.asList(gte(FieldDefs.DATE_FIELD, startTime), lt(FieldDefs.DATE_FIELD, endTime));

    if (maxUsageDateIncl != null) {
      query.add(lte(FieldDefs.USAGE_DATE_FIELD, maxUsageDateIncl));
    }

    Bson match = and(query);

    return find(match).stream()
        .map(GcpSnapshotAggregatedUsage::getGroupId)
        .collect(Collectors.toSet());
  }

  /*
   [
     {
       '$match': {
         date: {
           '$gt': ISODate(
           '2025-01-17T00:00:00.000Z'
           )
         }
       }
     },
     {
       '$group': {
         _id: 'snapshot',
         uniqueUsageDates: {
           '$addToSet': '$usageDate'
         },
         lastImportedStartDate: {
           '$max': '$date'
         }
       }
     },
     {
       '$project': {
         _id: 0,
         type: '$_id',
         uniqueUsageDates: 1,
         lastImportedStartDate: 1
       }
     }
   ]
  */
  @Override
  public Map<String, GCPFetchedBillingUniqueUsage> findUsageDatesAndLastStartDateByTypeFromDate(
      final Date startDate) {

    return getTDocumentCollection(GCPFetchedBillingUniqueUsage.class)
        .aggregate(
            Arrays.asList(
                match(gte(FieldDefs.DATE_FIELD, startDate)),
                group(
                    SNAPSHOT_TYPE,
                    addToSet("uniqueUsageDates", "$" + FieldDefs.USAGE_DATE_FIELD),
                    max("lastImportedStartDate", "$" + FieldDefs.DATE_FIELD)),
                project(
                    fields(
                        excludeId(),
                        computed("type", "$_id"),
                        include("uniqueUsageDates", "lastImportedStartDate")))))
        .into(new ArrayList<>())
        .stream()
        .collect(Collectors.toMap(GCPFetchedBillingUniqueUsage::getType, Function.identity()));
  }

  private <TDocument> MongoCollection<TDocument> getTDocumentCollection(
      final Class<TDocument> documentClass) {
    return getDatabase(getMongo(), getDbName()).getCollection(getCollectionName(), documentClass);
  }

  @Override
  protected List<Convention> getPojoCodecConventions() {
    // excludes the SerializeAllConvention to make ID generation work
    final ArrayList<Convention> conventions = new ArrayList<>(Conventions.DEFAULT_CONVENTIONS);
    conventions.add(Conventions.SET_PRIVATE_FIELDS_CONVENTION);
    return conventions;
  }
}
