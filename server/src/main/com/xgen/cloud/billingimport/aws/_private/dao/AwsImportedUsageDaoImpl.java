package com.xgen.cloud.billingimport.aws._private.dao;

import static com.google.common.base.Preconditions.checkArgument;
import static com.mongodb.client.model.Accumulators.addToSet;
import static com.mongodb.client.model.Accumulators.first;
import static com.mongodb.client.model.Accumulators.max;
import static com.mongodb.client.model.Accumulators.sum;
import static com.mongodb.client.model.Aggregates.group;
import static com.mongodb.client.model.Aggregates.limit;
import static com.mongodb.client.model.Aggregates.match;
import static com.mongodb.client.model.Aggregates.project;
import static com.mongodb.client.model.Aggregates.sort;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.exists;
import static com.mongodb.client.model.Filters.gt;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.lt;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.not;
import static com.mongodb.client.model.Projections.computed;
import static com.mongodb.client.model.Projections.exclude;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static com.mongodb.client.model.Sorts.descending;
import static com.xgen.cloud.billingimport.aws._private.dao.AwsBillingImportDaoMetrics.WRITE_DURATION_HISTOGRAM;
import static com.xgen.cloud.billingimport.cloudprovider._public.aws.AwsImportedUsage.AWS_REGION_NAME;
import static com.xgen.cloud.billingimport.cloudprovider._public.aws.AwsImportedUsage.BILLING_PERIOD_START_DATE;
import static com.xgen.cloud.billingimport.cloudprovider._public.aws.AwsImportedUsage.CLUSTER_UNIQUE_ID;
import static com.xgen.cloud.billingimport.cloudprovider._public.aws.AwsImportedUsage.END_DATE;
import static com.xgen.cloud.billingimport.cloudprovider._public.aws.AwsImportedUsage.GROUP_ID;
import static com.xgen.cloud.billingimport.cloudprovider._public.aws.AwsImportedUsage.QUANTITY;
import static com.xgen.cloud.billingimport.cloudprovider._public.aws.AwsImportedUsage.RSID;
import static com.xgen.cloud.billingimport.cloudprovider._public.aws.AwsImportedUsage.START_DATE;
import static com.xgen.cloud.billingimport.cloudprovider._public.aws.AwsImportedUsage.USAGE_DATE;
import static com.xgen.cloud.billingimport.common._public.model.FetchedBillingUniqueUsage.ID;
import static com.xgen.cloud.billingimport.common._public.model.FetchedBillingUniqueUsage.LAST_IMPORTED_START_DATE;
import static com.xgen.cloud.billingimport.common._public.model.FetchedBillingUniqueUsage.UNIQUE_USAGE_DATES;

import com.google.common.annotations.VisibleForTesting;
import com.mongodb.ReadConcern;
import com.mongodb.ReadPreference;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.InsertOneResult;
import com.xgen.cloud.billingimport.aws._public.dao.AwsImportedUsageDao;
import com.xgen.cloud.billingimport.aws._public.model.FetchedAwsPastMaxUsage;
import com.xgen.cloud.billingimport.cloudprovider._public.aws.AwsImportedUsage;
import com.xgen.cloud.billingimport.cloudprovider._public.aws.AwsImportedUsageType;
import com.xgen.cloud.billingimport.common._public.model.FetchedBillingUniqueUsage;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.cloud.common.db.mongo._public.index.MongoIndex;
import com.xgen.cloud.common.model._public.annotation.MethodCallPromTimed;
import com.xgen.cloud.nds.aws._public.model.AWSRegionName;
import io.prometheus.client.Histogram;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

@Singleton
public class AwsImportedUsageDaoImpl extends BaseDao<AwsImportedUsage>
    implements AwsImportedUsageDao {
  private static final String PROM_DAO_NAME = "aws_billing_usage_dao_duration_seconds";
  private static final String PROM_DAO_HELP = "Histogram for AwsImportedUsageDao methods seconds";
  private static final String DAO_METHOD_LABEL = "dao_method";

  public static final String DB_NAME = "mmsdbcloudproviders";
  public static final String COLLECTION_NAME = "awsImportedUsage";
  private final AppSettings appSettings;

  @Inject
  public AwsImportedUsageDaoImpl(
      MongoClientContainer container, CodecRegistry codecRegistry, AppSettings appSettings) {
    super(container, DB_NAME, COLLECTION_NAME, codecRegistry);
    this.appSettings = appSettings;
  }

  @Override
  @SuppressWarnings("try")
  public InsertOneResult insertReplicaSafe(AwsImportedUsage usage) {
    try (Histogram.Timer timer =
        WRITE_DURATION_HISTOGRAM.labels("AwsImportedUsageDao", "insertReplicaSafe").startTimer()) {

      // Timer automatically records duration when try block exits
      return insertReplicaSafeWithoutMetrics(usage);
    } catch (Exception e) {
      // Ensure metric collection failures don't impact core functionality
      // Fall back to original implementation without metrics
      return insertReplicaSafeWithoutMetrics(usage);
    }
  }

  private InsertOneResult insertReplicaSafeWithoutMetrics(AwsImportedUsage usage) {
    return super.insertReplicaSafe(usage);
  }

  @Override
  @SuppressWarnings("try")
  public long updateManyMajority(Bson filter, Bson update) {
    try (Histogram.Timer timer =
        WRITE_DURATION_HISTOGRAM.labels("AwsImportedUsageDao", "updateManyMajority").startTimer()) {

      // Timer automatically records duration when try block exits
      return updateManyMajorityWithoutMetrics(filter, update);
    } catch (Exception e) {
      // Ensure metric collection failures don't impact core functionality
      // Fall back to original implementation without metrics
      return updateManyMajorityWithoutMetrics(filter, update);
    }
  }

  private long updateManyMajorityWithoutMetrics(Bson filter, Bson update) {
    return super.updateManyMajority(filter, update);
  }

  @Override
  @SuppressWarnings("try")
  public DeleteResult deleteManyMajority(Bson query) {
    try (Histogram.Timer timer =
        WRITE_DURATION_HISTOGRAM.labels("AwsImportedUsageDao", "deleteManyMajority").startTimer()) {

      // Timer automatically records duration when try block exits
      return deleteManyMajorityWithoutMetrics(query);
    } catch (Exception e) {
      // Ensure metric collection failures don't impact core functionality
      // Fall back to original implementation without metrics
      return deleteManyMajorityWithoutMetrics(query);
    }
  }

  private DeleteResult deleteManyMajorityWithoutMetrics(Bson query) {
    return super.deleteManyMajority(query);
  }

  public AwsImportedUsageDaoImpl(
      MongoClientContainer container,
      CodecRegistry codecRegistry,
      String dbName,
      String collectionName,
      AppSettings appSettings) {
    super(container, dbName, collectionName, codecRegistry);
    this.appSettings = appSettings;
  }

  @Override
  public List<MongoIndex> getIndexes() {
    final List<MongoIndex> list = super.getIndexes();
    if (!appSettings.getAppEnv().isLocalOrTest()) {
      list.add(MongoIndex.builder().key(START_DATE).ttl(Duration.ofDays(31 * 3)).build());
    }
    list.add(MongoIndex.builder().key(START_DATE).key(GROUP_ID).key(AwsImportedUsage.TYPE).build());
    list.add(MongoIndex.builder().key(BILLING_PERIOD_START_DATE).key(START_DATE).build());
    list.add(
        MongoIndex.builder()
            .background()
            .key(GROUP_ID)
            .key(AwsImportedUsage.TYPE)
            .key(BILLING_PERIOD_START_DATE)
            .key(START_DATE)
            .build());
    return list;
  }

  private static String concatWithId(final String field) {
    return ID_FIELD + "." + field;
  }

  @Override
  public Stream<ObjectId> getDistinctGroupIds(
      final Collection<AwsImportedUsageType> usageTypes, final Date startDate, final Date endTime) {

    final Bson match = buildQueryByTypeAndTimeRange(usageTypes, startDate, endTime);
    final Bson projection = new Document(GROUP_ID, true);

    FindIterable<Document> iterator =
        getCollection()
            .withReadConcern(ReadConcern.MAJORITY)
            .find(match, Document.class)
            .projection(projection);

    return StreamSupport.stream(iterator.spliterator(), false)
        .map(doc -> doc.getObjectId(GROUP_ID));
  }

  private Bson buildQueryByTypeAndTimeRange(
      final Collection<AwsImportedUsageType> types, final Date startDate, final Date endTime) {

    return and(eq(START_DATE, startDate), eq(END_DATE, endTime), in(AwsImportedUsage.TYPE, types));
  }

  @Override
  public List<AwsImportedUsage> findInTimeRangeByGroupIdAndType(
      final ObjectId groupId,
      final AwsImportedUsageType type,
      final Date startDate,
      final Date endTime) {
    Objects.requireNonNull(groupId);
    Objects.requireNonNull(type);
    Objects.requireNonNull(startDate);
    Objects.requireNonNull(endTime);
    return findInTimeRangeByGroupIdsAndTypes(
        List.of(groupId), Set.of(type), startDate, endTime, false);
  }

  @MethodCallPromTimed(
      name = PROM_DAO_NAME,
      help = PROM_DAO_HELP,
      labelNames = DAO_METHOD_LABEL,
      labelValues = "findInTimeRangeByGroupIdsTypeAndUsageDate")
  @Override
  public List<AwsImportedUsage> findInTimeRangeByGroupIdsTypeAndUsageDate(
      final Collection<ObjectId> groupIds,
      final Collection<AwsImportedUsageType> types,
      final Date startDate,
      final Date endDate) {
    checkArgument(groupIds != null && !groupIds.isEmpty());
    checkArgument(types != null && !types.isEmpty());
    Objects.requireNonNull(startDate);
    Objects.requireNonNull(endDate);
    return findInTimeRangeByGroupIdsAndTypes(groupIds, types, startDate, endDate, true);
  }

  private Bson groupId(String field) {
    return computed(field, $(concatWithId(field)));
  }

  /**
   * Finds usages for specified groupIds, types, start and end dates and groups the usage quantity
   * by billing period + group id + region + cluster id + rs id + (optional if groupByUsageDate flag
   * is set) usageDate
   */
  @VisibleForTesting
  public List<AwsImportedUsage> findInTimeRangeByGroupIdsAndTypes(
      final Collection<ObjectId> groupIds,
      final Collection<AwsImportedUsageType> types,
      final Date startDate,
      final Date endDate,
      final boolean groupByUsageDate) {
    checkArgument(groupIds != null && !groupIds.isEmpty());
    checkArgument(types != null && !types.isEmpty());
    Objects.requireNonNull(startDate);
    Objects.requireNonNull(endDate);
    /*

      groupId = ...;
      type = ...;
      startDate = ...;
      endDate = ...;

      db.awsImportedUsage.aggregate([
          {$match: {
            groupId: {$in: groupIds},
            type: {$in: types},
            startDate: startDate,
            endDate: endDate}
          },
          {
              $group: {
                     _id: {
                     clusterUniqueId: "$clusterUniqueId",
                     awsRegionName: "$awsRegionName",
                     billingPeriodStartDate: "$billingPeriodStartDate",
                     rsId: "$rsId",
                     groupId: "$groupId",
                     type: "$type",
                     usageDate: "$usageDate" // optional if groupByUsageDate flag is true
                 },
                 quantity: {$sum: '$quantity'},
                 startDate: {$first: '$startDate'},
                 endDate: {$first: '$endDate'},
             }
         },
          {
              $project: {
                  clusterUniqueId: '$_id.clusterUniqueId',
                  awsRegionName: '$_id.awsRegionName',
                  groupId: '$_id.groupId',
                  type: '$_id.type',
                  billingPeriodStartDate: '$_id.billingPeriodStartDate',
                  rsId: '$_id.rsId',
                  usageDate: '$_id.usageDate', // optional if groupByUsageDate flag is true
                  quantity: '$quantity',
                  startDate: '$startDate',
                  endDate: '$endDate'
              }
          }
      ])
    *
    */

    final Bson matchStage =
        match(and(buildQueryByTypeAndTimeRange(types, startDate, endDate), in(GROUP_ID, groupIds)));

    final Document groupIdFields =
        new Document(CLUSTER_UNIQUE_ID, $(CLUSTER_UNIQUE_ID))
            .append(AWS_REGION_NAME, $(AWS_REGION_NAME))
            .append(BILLING_PERIOD_START_DATE, $(BILLING_PERIOD_START_DATE))
            .append(RSID, $(RSID))
            .append(GROUP_ID, $(GROUP_ID))
            .append(AwsImportedUsage.TYPE, $(AwsImportedUsage.TYPE));

    if (groupByUsageDate) {
      groupIdFields.append(USAGE_DATE, $(USAGE_DATE));
    }

    final Bson groupStage =
        group(
            groupIdFields,
            sum(QUANTITY, $(QUANTITY)),
            first(START_DATE, $(START_DATE)),
            first(END_DATE, $(END_DATE)));

    final ArrayList<Bson> fields = new ArrayList<>();
    fields.add(groupId(CLUSTER_UNIQUE_ID));
    fields.add(groupId(AWS_REGION_NAME));
    fields.add(groupId(GROUP_ID));
    fields.add(groupId(AwsImportedUsage.TYPE));
    fields.add(groupId(BILLING_PERIOD_START_DATE));
    fields.add(groupId(RSID));
    fields.add(include(QUANTITY, START_DATE, END_DATE));
    fields.add(exclude(ID_FIELD));

    if (groupByUsageDate) {
      fields.add(computed(USAGE_DATE, $(concatWithId(USAGE_DATE))));
    }

    final Bson projectStage = project(fields(fields));

    final Bson sortStage = sort(Sorts.ascending(START_DATE));

    final AggregateIterable<AwsImportedUsage> iterator =
        getCollection()
            .aggregate(List.of(matchStage, groupStage, projectStage, sortStage))
            .allowDiskUse(true);

    return StreamSupport.stream(iterator.spliterator(), false).toList();
  }

  @Override
  public AwsImportedUsage findMaxUsageSoFar(
      final ObjectId groupId,
      final AwsImportedUsageType type,
      final Date startDate,
      final Date billingPeriodStartDate,
      final ObjectId clusterUniqueId,
      final AWSRegionName regionName,
      final String rsId) {
    return findMaxUsageSoFar(
        groupId,
        type,
        startDate,
        billingPeriodStartDate,
        clusterUniqueId,
        regionName,
        rsId,
        Optional.empty());
  }

  @MethodCallPromTimed(
      name = PROM_DAO_NAME,
      help = PROM_DAO_HELP,
      labelNames = DAO_METHOD_LABEL,
      labelValues = "findMaxUsageSoFarForUsageDate")
  @Override
  public AwsImportedUsage findMaxUsageSoFarForUsageDate(
      final ObjectId groupId,
      final AwsImportedUsageType type,
      final Date startDate,
      final Date billingPeriodStartDate,
      final ObjectId clusterUniqueId,
      final AWSRegionName regionName,
      final String rsId,
      final Date usageDate) {
    return findMaxUsageSoFar(
        groupId,
        type,
        startDate,
        billingPeriodStartDate,
        clusterUniqueId,
        regionName,
        rsId,
        Optional.ofNullable(usageDate));
  }

  /**
   * Finds maximum usage received previously (with the {@link AwsImportedUsage#getStartDate()} less
   * than startDate param) that matches passed in parameters - groupId, type, billing period,
   * clusterId, rsId, region, and optionally usageDate. Performs the following aggregation:
   *
   * <pre>
   * [
   *  {$match: {
   *    startDate: {$lt: startDate},
   *    groupId: groupId,
   *    type: type,
   *    billingPeriodStartDate: billingPeriodStartDate,
   *    awsRegionName: awsRegionName,
   *    clusterUniqueId: clusterUniqueId,
   *    rsId: rsId,
   *    usageDate: usageDate
   *  }},
   *  {$group: {
   *    _id: {
   *      startDate: startDate
   *    },
   *    quantity: {$sum: "$quantity"},
   *    startDate: {$first: "$startDate"},
   *    usageDate: {$first: "$usageDate"},
   *    billingPeriodStartDate: {$first: "$billingPeriodStartDate"},
   *    groupId: {$first: "$groupId"},
   *    type: {$first: "$type"},
   *    awsRegionName: {$first: "$awsRegionName"},
   *    clusterUniqueId: {$first: "$clusterUniqueId"},
   *    usageDate: {$first: "$usageDate"},
   *    rsId: {$first: "$rsId"}
   *  }},
   *  {$project: {
   *    quantity: "$quantity",
   *    startDate: "$_id.startDate",
   *    usageDate: "$usageDate",
   *    billingPeriodStartDate: "$billingPeriodStartDate",
   *    groupId: "$groupId",
   *    type: "$type",
   *    awsRegionName: "$awsRegionName",
   *    clusterUniqueId: "$clusterUniqueId",
   *    rsId: "$rsId"
   *  }},
   *  {$sort: {
   *    quantity: -1,
   *    startDate: -1
   *  }},
   *  {$limit: 1}
   * ]
   *
   * </pre>
   */
  private AwsImportedUsage findMaxUsageSoFar(
      final ObjectId groupId,
      final AwsImportedUsageType type,
      final Date startDate,
      final Date billingPeriodStartDate,
      final ObjectId clusterUniqueId,
      final AWSRegionName regionName,
      final String rsId,
      final Optional<Date> usageDate) {

    final ArrayList<Bson> query =
        new ArrayList<>(
            List.of(
                lt(START_DATE, startDate),
                eq(GROUP_ID, groupId),
                eq(AwsImportedUsage.TYPE, type),
                eq(BILLING_PERIOD_START_DATE, billingPeriodStartDate),
                eq(AWS_REGION_NAME, regionName),
                eq(CLUSTER_UNIQUE_ID, clusterUniqueId)));

    usageDate.ifPresent(date -> query.add(eq(USAGE_DATE, date)));

    if (clusterUniqueId == null) {
      query.add(not(exists(CLUSTER_UNIQUE_ID)));
    } else {
      query.add(eq(CLUSTER_UNIQUE_ID, clusterUniqueId));
    }

    if (!StringUtils.isBlank(rsId)) {
      query.add(eq(RSID, rsId));
    }

    final Bson matchStage = match(and(query));

    final Bson groupStage =
        group(
            new Document(START_DATE, $(START_DATE)),
            sum(QUANTITY, $(QUANTITY)),
            first(START_DATE, $(START_DATE)),
            first(USAGE_DATE, $(USAGE_DATE)),
            first(BILLING_PERIOD_START_DATE, $(BILLING_PERIOD_START_DATE)),
            first(GROUP_ID, $(GROUP_ID)),
            first(AwsImportedUsage.TYPE, $(AwsImportedUsage.TYPE)),
            first(AWS_REGION_NAME, $(AWS_REGION_NAME)),
            first(CLUSTER_UNIQUE_ID, $(CLUSTER_UNIQUE_ID)),
            first(RSID, $(RSID)));

    final Bson projectStage =
        project(
            fields(
                include(
                    QUANTITY,
                    START_DATE,
                    USAGE_DATE,
                    BILLING_PERIOD_START_DATE,
                    GROUP_ID,
                    AwsImportedUsage.TYPE,
                    AWS_REGION_NAME,
                    CLUSTER_UNIQUE_ID,
                    RSID),
                exclude(ID_FIELD)));

    final Bson sortStage1 = sort(descending(QUANTITY));
    final Bson sortStage2 = sort(descending(START_DATE));

    final Bson limitStage = limit(1);

    final AggregateIterable<AwsImportedUsage> iterator =
        getCollection()
            .aggregate(
                List.of(matchStage, groupStage, projectStage, sortStage1, sortStage2, limitStage))
            .allowDiskUse(true);

    return StreamSupport.stream(iterator.spliterator(), false).findFirst().orElse(null);
  }

  /**
   * Find the maximum past usage aggregated by a "usage key" (combination of
   * usageDate/groupId/region/ type/clusterUniqueId/rsId) for one group.
   *
   * <pre>
   * [
   *  {$match: {
   *    startDate: {$lt: startDate},
   *    groupId: groupId,
   *    type: {$in: types},
   *    billingPeriodStartDate: {$in: billingPeriodStartDate},
   *  }},
   *  {$group: {
   *    _id: {
   *      startDate: "$startDate",
   *      usageDate: "$usageDate",
   *      type: "$type",
   *      awsRegionName: "$awsRegionName",
   *      clusterUniqueId: "$clusterUniqueId",
   *      billingPeriodStartDate: "$billingPeriodStartDate",
   *      rsId: "$rsId"
   *    },
   *    quantity: {$sum: "$quantity"},
   *    groupId: {$first: "$groupId"},
   *  }},
   *
   *  {$group: {
   *    _id: {
   *      usageDate: "$_id.usageDate",
   *      type: "$_id.type",
   *      awsRegionName: "$_id.awsRegionName",
   *      clusterUniqueId: "$_id.clusterUniqueId",
   *      rsId: "$_id.rsId"
   *      groupId: "$groupId",
   *    },
   *    quantity: {$max: "$quantity"}
   *  }}
   * ]
   * </pre>
   */
  @Override
  public List<FetchedAwsPastMaxUsage> aggregateMaxPastUsage(
      final ObjectId groupId,
      final Set<AwsImportedUsageType> types,
      final Date startDate,
      final Collection<Date> billingPeriodStartDate) {

    final Bson matchStage =
        match(
            and(
                lt(START_DATE, startDate),
                eq(GROUP_ID, groupId),
                in(AwsImportedUsage.TYPE, types),
                in(BILLING_PERIOD_START_DATE, billingPeriodStartDate)));

    final Document groupIdFields1 =
        new Document(START_DATE, $(START_DATE))
            .append(USAGE_DATE, $(USAGE_DATE))
            .append(BILLING_PERIOD_START_DATE, $(BILLING_PERIOD_START_DATE))
            .append(AWS_REGION_NAME, $(AWS_REGION_NAME))
            .append(CLUSTER_UNIQUE_ID, $(CLUSTER_UNIQUE_ID))
            .append(RSID, $(RSID))
            .append(AwsImportedUsage.TYPE, $(AwsImportedUsage.TYPE));

    final Bson groupStage1 =
        group(groupIdFields1, sum(QUANTITY, $(QUANTITY)), first(GROUP_ID, $(GROUP_ID)));

    final Document groupIdFields2 =
        new Document(USAGE_DATE, $(concatWithId(USAGE_DATE)))
            .append(GROUP_ID, $(GROUP_ID))
            .append(AWS_REGION_NAME, $(concatWithId(AWS_REGION_NAME)))
            .append(CLUSTER_UNIQUE_ID, $(concatWithId(CLUSTER_UNIQUE_ID)))
            .append(RSID, $(concatWithId(RSID)))
            .append(AwsImportedUsage.TYPE, $(concatWithId(AwsImportedUsage.TYPE)));

    final Bson groupStage2 = group(groupIdFields2, max(QUANTITY, $(QUANTITY)));

    final AggregateIterable<FetchedAwsPastMaxUsage> iterator =
        getCollection()
            .aggregate(List.of(matchStage, groupStage1, groupStage2), FetchedAwsPastMaxUsage.class)
            .allowDiskUse(true);

    return StreamSupport.stream(iterator.spliterator(), false).toList();
  }

  @Override
  @SuppressWarnings("try")
  public DeleteResult deleteOnOrAfterStartDate(
      final Date startDate, final Date billingPeriodStartDate) {
    try (Histogram.Timer timer =
        WRITE_DURATION_HISTOGRAM
            .labels("AwsImportedUsageDao", "deleteOnOrAfterStartDate")
            .startTimer()) {

      // Timer automatically records duration when try block exits
      return deleteManyMajorityWithoutMetrics(startDate, billingPeriodStartDate);
    } catch (Exception e) {
      // Ensure metric collection failures don't impact core functionality
      // Fall back to original implementation without metrics
      return deleteManyMajorityWithoutMetrics(startDate, billingPeriodStartDate);
    }
  }

  private DeleteResult deleteManyMajorityWithoutMetrics(
      Date startDate, Date billingPeriodStartDate) {
    return deleteManyMajority(
        and(gte(START_DATE, startDate), eq(BILLING_PERIOD_START_DATE, billingPeriodStartDate)));
  }

  @Override
  public List<AwsImportedUsage> findByUsageDate(
      final Date usageDateStart,
      final Date usageDateEnd,
      final Set<AwsImportedUsageType> usageTypes,
      final List<ObjectId> groupIds) {
    checkArgument(usageTypes != null && !usageTypes.isEmpty());
    checkArgument(groupIds != null && !groupIds.isEmpty());
    Objects.requireNonNull(usageDateStart);
    Objects.requireNonNull(usageDateEnd);

    return find(
        and(
            gte(USAGE_DATE, usageDateStart),
            lt(USAGE_DATE, usageDateEnd),
            in(AwsImportedUsage.TYPE, usageTypes),
            in(GROUP_ID, groupIds)));
  }

  /**
   * Finds the unique usage dates and the last imported start date for each AwsImportedUsageType
   * starting from the specified start date.
   *
   * <p>Performs the following aggregation:
   * <pre>{@code
   * db.awsImportedUsage.aggregate([
   *   {
   *     $match: {
   *       usageDate: { $gte: startDate },
   *       startDate: { $gte: startDate },
   *     }
   *   },
   *   {
   *     $group: {
   *       _id: "$type",
   *       uniqueUsageDates: { $addToSet: "$usageDate" },
   *       lastImportedStartDate: { $max: "$startDate" }
   *     },
   *    {
   *     $project: {
   *       _id: null,
   *       type: $_id.type,
   *       uniqueUsageDates: 1,
   *       lastImportedStartDate: 1
   *     }
   *   }
   * ])
   * </pre>
   *
   * @param startDate the start date to filter usage records (inclusive).
   * @return a map of types with unique usage dates and the last imported start date.
   */
  @Override
  public Map<AwsImportedUsageType, FetchedBillingUniqueUsage>
      findUsageDatesAndLastStartDateByTypeFromDate(final LocalDate startDate) {
    // Ensure startDate is not null
    Objects.requireNonNull(startDate, "Start date must not be null.");

    final Bson matchStage = match(and(gte(USAGE_DATE, startDate), gte(START_DATE, startDate)));

    final Document groupIdFields = new Document(AwsImportedUsage.TYPE, $(AwsImportedUsage.TYPE));

    final Bson groupStage =
        group(
            groupIdFields,
            addToSet(UNIQUE_USAGE_DATES, $(USAGE_DATE)),
            max(LAST_IMPORTED_START_DATE, $(START_DATE)));

    final Bson projectStage =
        project(
            fields(
                groupId(AwsImportedUsage.TYPE),
                include(UNIQUE_USAGE_DATES, LAST_IMPORTED_START_DATE),
                exclude(ID_FIELD)));

    final AggregateIterable<FetchedBillingUniqueUsage> iterator =
        getCollection()
            .aggregate(
                List.of(matchStage, groupStage, projectStage), FetchedBillingUniqueUsage.class)
            .allowDiskUse(true);

    return StreamSupport.stream(iterator.spliterator(), false)
        .collect(
            Collectors.toMap(
                usage -> AwsImportedUsageType.valueOf(usage.getType()), Function.identity()));
  }

  /**
   * Performs the following aggregation and returns a map of usage date to the aggregated quantity
   * sum:
   *
   * <pre>
   *   db.awsImportedUsage.aggregate([
   *    {$match: {
   *      startDate: startDate,
   *      type: {$in: types}
   *    }},
   *    {$group: {
   *      _id: "$usageDate",
   *      quantity: {$sum: "$quantity"}
   *    }}
   *   ])
   * </pre>
   */
  @Override
  public Map<Date, Double> getQuantitySumByUsageDate(
      final Date startDate, final Collection<AwsImportedUsageType> types) {
    return getQuantitySumByUsageDate(startDate, types, Collections.emptyList());
  }

  /**
   * Performs the following aggregation and returns a map of usage date to the aggregated quantity
   * sum:
   *
   * <pre>
   *   db.awsImportedUsage.aggregate([
   *    {$match: {
   *      startDate: startDate,
   *      type: {$in: types},
   *      ignoreIfFieldsAreNull: {$ne: null},
   *      ...
   *    }},
   *    {$group: {
   *      _id: "$usageDate",
   *      quantity: {$sum: "$quantity"}
   *    }}
   *   ])
   * </pre>
   */
  @Override
  public Map<Date, Double> getQuantitySumByUsageDate(
      final Date startDate,
      final Collection<AwsImportedUsageType> types,
      final Collection<String> ignoreIfFieldsAreNull) {
    Objects.requireNonNull(startDate);
    checkArgument(types != null && !types.isEmpty());

    final ArrayList<Bson> query =
        new ArrayList<>(List.of(eq(START_DATE, startDate), in(AwsImportedUsage.TYPE, types)));

    ignoreIfFieldsAreNull.forEach(field -> query.add(ne(field, null)));

    final Bson matchStage = match(and(query));

    final Bson groupStage =
        group(new Document(USAGE_DATE, $(USAGE_DATE)), sum(QUANTITY, $(QUANTITY)));

    final AggregateIterable<Document> iterator =
        getCollection()
            .withReadConcern(ReadConcern.LOCAL)
            .withReadPreference(ReadPreference.secondaryPreferred(90L, TimeUnit.SECONDS))
            .aggregate(List.of(matchStage, groupStage), Document.class);

    return StreamSupport.stream(iterator.spliterator(), false)
        .collect(
            Collectors.toMap(
                usage -> usage.get(ID_FIELD, Document.class).getDate(USAGE_DATE),
                usage -> usage.getDouble(QUANTITY)));
  }

  /**
   * Returns the maximum past aggregated quantity by usage date
   *
   * <pre>
   * [
   *  {$match: {
   *    startDate: {$lt: startDate},
   *    type: {$in: types},
   *    mandatoryNotNullFieldNames: {$ne: null},
   *  }},
   *  {$group: {
   *    _id: {
   *      startDate: "$startDate",
   *      usageDate: "$usageDate",
   *    },
   *    quantity: {$sum: "$quantity"},
   *  }},
   *  {$group: {
   *    _id: {
   *      usageDate: "$_id.usageDate",
   *    },
   *    quantity: {$max: "$quantity"}
   *  }}
   * ]
   * </pre>
   */
  @Override
  public Map<Date, Double> getMaxPastQuantityByUsageDate(
      final Date startDate,
      final Collection<AwsImportedUsageType> types,
      final Collection<String> mandatoryNotNullFieldNames) {
    Objects.requireNonNull(startDate);
    checkArgument(types != null && !types.isEmpty());

    final Bson matchStage =
        match(
            Filters.and(
                Stream.concat(
                        Stream.of(
                            lt(START_DATE, startDate),
                            // Setting lower bound for past imports
                            gt(START_DATE, DateUtils.addDays(startDate, -7)),
                            in(AwsImportedUsage.TYPE, types)),
                        mandatoryNotNullFieldNames.stream().map(field -> Filters.ne(field, null)))
                    .toList()));

    final Bson groupStageSum =
        group(
            new Document(START_DATE, $(START_DATE)).append(USAGE_DATE, $(USAGE_DATE)),
            sum(QUANTITY, $(QUANTITY)));

    final Bson groupStageMax =
        group(new Document(USAGE_DATE, $(concatWithId(USAGE_DATE))), max(QUANTITY, $(QUANTITY)));

    final AggregateIterable<Document> iterator =
        getCollection()
            .withReadConcern(ReadConcern.LOCAL)
            .withReadPreference(ReadPreference.secondaryPreferred(90L, TimeUnit.SECONDS))
            .aggregate(List.of(matchStage, groupStageSum, groupStageMax), Document.class)
            .allowDiskUse(true);

    return StreamSupport.stream(iterator.spliterator(), false)
        .collect(
            Collectors.toMap(
                document -> document.get(ID, Document.class).getDate(USAGE_DATE),
                document -> document.getDouble(QUANTITY)));
  }
}
