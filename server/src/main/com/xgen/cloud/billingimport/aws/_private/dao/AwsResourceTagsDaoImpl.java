package com.xgen.cloud.billingimport.aws._private.dao;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;
import static net.logstash.logback.argument.StructuredArguments.kv;

import com.mongodb.MongoBulkWriteException;
import com.mongodb.ReadPreference;
import com.mongodb.bulk.BulkWriteInsert;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.bulk.BulkWriteUpsert;
import com.mongodb.client.ClientSession;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.BulkWriteOptions;
import com.mongodb.client.model.UpdateOneModel;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.result.UpdateResult;
import com.xgen.cloud.billingimport.aws._public.config.AwsImportedUsageConfig;
import com.xgen.cloud.billingimport.aws._public.dao.AwsResourceTagsDao;
import com.xgen.cloud.common.constants._public.model.metrics.MetricNamespaceConstants;
import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.svc.mms.model.billing.AWSResourceTags;
import io.prometheus.client.Counter;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/** AWS Resource Tags DAO implementation using the mmsdbcloudproviders database. */
@Singleton
public class AwsResourceTagsDaoImpl extends BaseDao<AWSResourceTags>
    implements AwsResourceTagsDao, DaoLoggingHelpers {

  public static final String DB_NAME = "mmsdbcloudproviders";
  public static final String COLLECTION_NAME = "awsResourceTagsV2";

  private final Logger LOG;

  /** Counter for the number of documents processed. */
  private static final Counter TAGS_DOCS_PROCESSED_COUNTER =
      Counter.build()
          .namespace(MetricNamespaceConstants.BILLING_NAMESPACE)
          .name("aws_resource_tags_v2_dao_documents_processed_total")
          .help("Counter for the number of documents processed by operation (op).")
          .labelNames("op")
          .register();

  private final AwsImportedUsageConfig awsImportedUsageConfig;

  @Inject
  public AwsResourceTagsDaoImpl(
      MongoClientContainer container,
      CodecRegistry codecRegistry,
      AwsImportedUsageConfig awsImportedUsageConfig) {
    super(container, DB_NAME, COLLECTION_NAME, codecRegistry);
    this.LOG = LoggerFactory.getLogger(AwsResourceTagsDaoImpl.class);
    this.awsImportedUsageConfig = awsImportedUsageConfig;
    initCounters();
  }

  private void initCounters() {
    for (OpLabel value : OpLabel.values()) {
      TAGS_DOCS_PROCESSED_COUNTER.labels(value.name()).inc(0);
    }
  }

  public Optional<AWSResourceTags> findById(String resourceId) {
    TAGS_DOCS_PROCESSED_COUNTER.labels(OpLabel.FIND_BY_ID.name()).inc();
    return Optional.ofNullable(getCollection().find(eq("_id", resourceId)).first());
  }

  public Stream<AWSResourceTags> findByIds(Collection<String> resourceIds) {
    if (resourceIds.isEmpty()) {
      return Stream.of();
    }
    TAGS_DOCS_PROCESSED_COUNTER.labels(OpLabel.FIND_BY_IDS.name()).inc(resourceIds.size());
    LOG.debug("Finding batch: aws resource tags: {}", kv("batchSize", resourceIds.size()));
    return stream(
        getCollection()
            .withReadPreference(ReadPreference.secondaryPreferred(90L, TimeUnit.SECONDS))
            .find(in("_id", resourceIds))
            .batchSize(awsImportedUsageConfig.getTagsCursorBatchSize())
            .cursor());
  }

  public Optional<UpdateResult> upsertOne(AWSResourceTags tags) {
    return upsertOne(tags, null);
  }

  public Optional<UpdateResult> upsertOne(AWSResourceTags tags, ClientSession session) {
    UpdateOneModel<AWSResourceTags> model = getUpdateOneModelUpsert(tags);
    if (model.getUpdate() == null) {
      return Optional.empty();
    }
    TAGS_DOCS_PROCESSED_COUNTER.labels(OpLabel.UPSERT_ONE.name()).inc();

    MongoCollection<AWSResourceTags> collection =
        getCollection().withWriteConcern(getReplicaSafeWriteConcern());

    UpdateResult result =
        session == null
            ? collection.updateOne(model.getFilter(), model.getUpdate(), model.getOptions())
            : collection.updateOne(
                session, model.getFilter(), model.getUpdate(), model.getOptions());

    return Optional.of(result);
  }

  /**
   * Bulk upserts documents and then finds them by the result ids.
   *
   * @throws UnsupportedOperationException – if the db write was unacknowledged.
   */
  public Stream<AWSResourceTags> bulkUpsertAndFindByIds(List<AWSResourceTags> documents) {
    Optional<BulkWriteResult> bulkWriteResult = bulkUpsert(documents);
    if (bulkWriteResult.isPresent()) {
      List<String> upsertedIds =
          Stream.concat(
                  bulkWriteResult.get().getInserts().stream().map(BulkWriteInsert::getId),
                  bulkWriteResult.get().getUpserts().stream().map(BulkWriteUpsert::getId))
              .distinct()
              .map(bsonValue -> bsonValue.asString().getValue())
              .toList();
      return findByIds(upsertedIds);
    }
    return Stream.empty();
  }

  /**
   * Builds write queries from the provided documents, then bulk writes them as a single database
   * request.
   *
   * @param documents collection of objects to upsert
   * @return the BulkWriteResult if the request operation did not throw an exception.
   * @throws com.mongodb.MongoException If there's an exception running the operation.
   * @throws MongoBulkWriteException if there's an exception in the bulk write operation
   */
  public Optional<BulkWriteResult> bulkUpsert(List<AWSResourceTags> documents) {
    return bulkUpsert(documents, null);
  }

  public Optional<BulkWriteResult> bulkUpsert(
      List<AWSResourceTags> documents, ClientSession session) {
    if (documents == null || documents.isEmpty()) {
      return Optional.empty();
    }
    LOG.debug("Writing batch: aws resource tags: {}", kv("batchSize", documents.size()));
    List<UpdateOneModel<AWSResourceTags>> pipeline =
        documents.stream().map(this::getUpdateOneModelUpsert).toList();
    BulkWriteOptions bulkWriteOptions = new BulkWriteOptions().ordered(false);
    try {
      MongoCollection<AWSResourceTags> collection =
          getCollection().withWriteConcern(getReplicaSafeWriteConcern());

      BulkWriteResult result =
          session == null
              ? collection.bulkWrite(pipeline, bulkWriteOptions)
              : collection.bulkWrite(session, pipeline, bulkWriteOptions);

      TAGS_DOCS_PROCESSED_COUNTER.labels(OpLabel.BULK_WRITE.name()).inc(documents.size());
      logBulkWriteResult(documents.size(), result);
      return Optional.of(result);
    } catch (MongoBulkWriteException e) {
      logBulkWriteException(documents.size(), e);
      throw e;
    }
  }

  /** Initializes an UpdateOneModel for upserting a document. */
  private UpdateOneModel<AWSResourceTags> getUpdateOneModelUpsert(AWSResourceTags tags) {
    List<Bson> updates = new ArrayList<>();
    updates.add(set(AWSResourceTags.GROUP_ID_FIELD, tags.getGroupId()));
    if (tags.getClusterUniqueId() != null) {
      updates.add(set(AWSResourceTags.CLUSTER_UNIQUE_ID_FIELD, tags.getClusterUniqueId()));
    }
    if (tags.getRsId() != null) {
      updates.add(set(AWSResourceTags.RSID_FIELD, tags.getRsId()));
    }
    Bson update = combine(updates);
    Bson filter = eq("_id", tags.getId());
    UpdateOptions options = new UpdateOptions().upsert(true);
    return new UpdateOneModel<>(filter, update, options);
  }

  /** Labels for prometheus metrics. */
  private enum OpLabel {
    BULK_WRITE,
    FIND_BY_ID,
    FIND_BY_IDS,
    UPSERT_ONE,
  }
}
