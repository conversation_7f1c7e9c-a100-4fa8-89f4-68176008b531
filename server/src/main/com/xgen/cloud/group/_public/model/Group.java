package com.xgen.cloud.group._public.model;

import static com.xgen.cloud.common.constants._public.model.project.GroupConstants.DEFAULT_AGENT_RESOURCE_MONITOR_COLLECTION_INTERVAL_IN_SECONDS;
import static com.xgen.cloud.common.constants._public.model.project.GroupConstants.DEFAULT_AGENT_RESOURCE_MONITOR_CPU_PROFILE_DURATION_IN_SECONDS;
import static com.xgen.cloud.common.constants._public.model.project.GroupConstants.DEFAULT_AGENT_RESOURCE_MONITOR_CPU_THRESHOLD_IN_PCT;
import static com.xgen.cloud.common.constants._public.model.project.GroupConstants.DEFAULT_AGENT_RESOURCE_MONITOR_HEAP_THRESHOLD_IN_MB;
import static com.xgen.cloud.common.constants._public.model.project.GroupConstants.DEFAULT_AGENT_RESOURCE_MONITOR_NORMALIZED_CPU_THRESHOLD_IN_PCT;
import static com.xgen.cloud.common.constants._public.model.project.GroupConstants.DEFAULT_AGENT_RESOURCE_MONITOR_RSS_THRESHOLD_IN_PCT;
import static com.xgen.cloud.common.constants._public.model.project.GroupConstants.DEFAULT_AGENT_RESOURCE_MONITOR_THRESHOLD_CROSS_BACKOFF_IN_MINUTES;
import static com.xgen.cloud.common.constants._public.model.project.GroupConstants.DEFAULT_AGENT_RESOURCE_MONITOR_TOP_CPU_ITEMS;
import static com.xgen.cloud.common.constants._public.model.project.GroupConstants.DEFAULT_AGENT_RESOURCE_MONITOR_TOP_HEAP_ITEMS;
import static com.xgen.cloud.common.constants._public.model.project.GroupConstants.DEFAULT_MONGODB_SHUTDOWN_TIMEOUT_IN_MINUTES;
import static com.xgen.cloud.common.util._public.util.DriverUtils.reverseObjectId;
import static com.xgen.svc.mms.api.view.integrations.constants.ApiIntegrationViewConstants.IntegrationType;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.annotations.VisibleForTesting;
import com.xgen.cloud.access.role._public.model.GroupTeamRoleObject;
import com.xgen.cloud.access.role._public.model.Role;
import com.xgen.cloud.access.role._public.model.TeamRoleAssignment;
import com.xgen.cloud.access.role._public.model.mapping.IdpMapping;
import com.xgen.cloud.common.brs._public.model.GroupBackupConfig;
import com.xgen.cloud.common.constants._public.model.notification.DatadogNotificationConstants.Region;
import com.xgen.cloud.common.constants._public.model.notification.OpsGenieNotificationConstants;
import com.xgen.cloud.common.constants._public.model.notification.PagerDutyNotificationConstants;
import com.xgen.cloud.common.entity._public.model.EntityType;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.featureFlag._public.model.ToggleableFeatureFlag;
import com.xgen.cloud.common.group._public.model.PreferredHostname;
import com.xgen.cloud.common.model._public.annotation.WithGenEncryptField;
import com.xgen.cloud.common.util._public.time.TZUtils;
import com.xgen.cloud.common.util._public.util.AgentType;
import com.xgen.cloud.common.util._public.util.AgentVersion;
import com.xgen.cloud.legacyintegration._public.model.DatadogIntegration;
import com.xgen.cloud.legacyintegration._public.model.HipChatIntegration;
import com.xgen.cloud.legacyintegration._public.model.Integration;
import com.xgen.cloud.legacyintegration._public.model.MicrosoftTeamsIntegration;
import com.xgen.cloud.legacyintegration._public.model.OpsGenieIntegration;
import com.xgen.cloud.legacyintegration._public.model.PagerDutyIntegration;
import com.xgen.cloud.legacyintegration._public.model.SlackIntegration;
import com.xgen.cloud.legacyintegration._public.model.VictorOpsIntegration;
import com.xgen.cloud.legacyintegration._public.model.WebhookIntegration;
import com.xgen.cloud.partnerintegrations.common._public.model.PartnerIntegrationsData;
import com.xgen.svc.mms.model.agent.constants.AgentLogLevel;
import com.xgen.svc.mms.model.grouptype.GroupType;
import dev.morphia.annotations.Embedded;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Id;
import dev.morphia.annotations.Property;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Nullable;
import java.security.Principal;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang.StringUtils;
import org.bson.codecs.pojo.annotations.BsonProperty;
import org.bson.types.ObjectId;

/**
 * A Group is the internal storage representation of an Atlas Project.
 *
 * <p>In MongoDB Atlas, a project is a grouping of related resources, such as clusters, that you can
 * manage together. Projects allow you to organize and control access to these resources. Users must
 * belong to a project to access its clusters. You can create multiple projects within an
 * organization, and users can belong to multiple projects. Each project can have different members
 * and roles assigned to manage access and permissions effectively.
 *
 * <p>see also the <a href="https://www.mongodb.com/docs/atlas/tutorial/manage-projects/">Atlas
 * documentation</a>
 */
@Entity(value = Group.COLLECTION_NAME, noClassnameStored = true, queryNonPrimary = false)
@WithGenEncryptField(DB = Group.DB_NAME, Collection = Group.COLLECTION_NAME)
@Hidden
public class Group
    implements Principal, GroupTeamRoleObject, com.xgen.cloud.common.entity._public.model.Entity {

  public static final String DB_NAME = "mmsdbconfig";
  public static final String COLLECTION_NAME = "config.customers";
  public static final String NAME_FIELD = "n";
  public static final String REVERSED_GROUP_ID_FIELD = "rcid";
  public static final String TEAMS_FIELD = "teams";
  public static final String NAME_LOWERCASE_FIELD = "nl";
  public static final String NAME_TOKENS_FIELD = "nts";
  public static final String GROUP_TYPE_FIELD = "groupType";
  public static final String GROUP_VISIBILITY_FIELD = "groupVisibility";
  public static final String DISABLE_DBSTATS_FIELD = "disableDbstats";
  public static final String DEFAULT_TIME_ZONE_ID_FIELD = "tzid";
  public static final String DEFAULT_TIME_ZONE_DISPLAY_FIELD = "tzdisp";
  public static final String CREATED_FIELD = "cre";
  public static final String PREFERRED_HOSTNAMES_FIELD = "cph";
  public static final String EXPERIMENTS_FIELD = "experiments";
  public static final String HOST_COUNTS_FIELD = "hostCounts";
  public static final String REPLICA_SET_COUNT_FIELD = "replicaSetCount";
  public static final String SHARD_COUNT_FIELD = "shardCount";
  public static final String ACTIVE_AGENT_COUNT_FIELD = "activeAgentCount";
  public static final String LAST_ACTIVE_AGENT_FIELD = "lastActiveAgent";
  public static final String BACKUP_CONFIG_FIELD = "brs";
  public static final String SUMMARY_STATISTICS_FIELD = "summaryStats";
  public static final String FEATURE_FLAGS_FIELD = "featureFlags";
  public static final String HAS_ADDED_HOSTS_FIELD = "hasAddedHosts";
  public static final String HAS_ADDED_AUTOMATION_AGENTS_FIELD = "hasAddedAutomationAgents";
  public static final String DEFAULT_WAIT_PROCESS_DOWN_TIMEOUT_FIELD =
      "defaultWaitProcessDownTimeout";
  public static final String AGENT_RESOURCE_MONITOR_COLLECTION_INTERVAL_FIELD =
      "agentResourceMonitorCollectionInterval";
  public static final String AGENT_RESOURCE_MONITOR_HEAP_THRESHOLD_FIELD =
      "agentResourceMonitorHeapThreshold";
  public static final String AGENT_RESOURCE_MONITOR_RSS_THRESHOLD_FIELD =
      "agentResourceMonitorRssThreshold";
  public static final String AGENT_RESOURCE_MONITOR_CPU_THRESHOLD_FIELD =
      "agentResourceMonitorCPUThreshold";
  public static final String AGENT_RESOURCE_MONITOR_NORMALIZED_CPU_THRESHOLD_FIELD =
      "agentResourceMonitorNormalizedCPUThreshold";
  public static final String AGENT_RESOURCE_MONITOR_TOP_HEAP_ITEMS_FIELD =
      "agentResourceMonitorTopHeapItems";
  public static final String AGENT_RESOURCE_MONITOR_TOP_CPU_ITEMS_FIELD =
      "agentResourceMonitorTopCPUItems";
  public static final String AGENT_RESOURCE_MONITOR_CPU_PROFILE_DURATION_FIELD =
      "agentResourceMonitorCPUProfileDuration";
  public static final String AGENT_RESOURCE_MONITOR_THRESHOLD_CROSS_BACKOFF_DURATION_FIELD =
      "agentResourceMonitorThresholdCrossBackoffDuration";
  public static final String MONGOS_DEACTIVATION_THRESHOLD_FIELD = "mongosDeactivationThreshold";
  public static final String SUPPRESS_MONGOS_AUTO_DISCOVERY_FIELD = "suppressMongosAutoDiscovery";
  public static final String NEW_RELIC_LICENSE_KEY = "newRelicLicenseKey";
  public static final String NEW_RELIC_INSIGHTS_WRITE_TOKEN = "newRelicInsightsWriteToken";
  public static final String NEW_RELIC_INSIGHTS_READ_TOKEN = "newRelicInsightsReadToken";
  public static final String NEW_RELIC_INSIGHTS_ACCOUNT_ID = "newRelicInsightsAccountId";
  public static final String AUTOMATION_AGENT_LOG_LEVEL_FIELD = "automationAgentLogLevel";
  public static final String AUTOMATION_AGENT_HTTP_LOG_LEVEL_FIELD = "automationAgentHttpLogLevel";
  public static final String BACKUP_AGENT_LOG_LEVEL_FIELD = "backupAgentLogLevel";
  public static final String MONITORING_AGENT_LOG_LEVEL_FIELD = "monitoringAgentLogLevel";
  public static final String IDP_MAPPING_FIELD = "idpMapping";
  public static final String ENABLE_ALL_HOST_PROFILERS_FIELD = "enableAllHostProfilers";
  public static final String INCLUDE_JSON_WITH_ALERTS_FIELD = "includeJsonWithAlerts";
  public static final String RATE_LIMIT_FIELD = "rateLimit";
  public static final String SALESFORCE_ACCOUNT_ID_FIELD = "salesforceAccountId";
  public static final String SALESFORCE_PROJECT_ID_FIELD = "salesforceProjectId";
  public static final String SALESFORCE_BILLING_CONTACT_ID_FIELD = "salesforceBillingContactId";
  public static final String GA_CLIENT_ID_FIELD = "gaClientId";
  public static final String STATUS_FIELD = "status";
  public static final String PROMETHEUS_CONFIG_FIELD = "prom";
  public static final String EXEMPT_FROM_BILLING_ON_INDEXSIZE_FIELD =
      "exemptFromBillingOnIndexSize";
  public static final String IS_INTERCOM_ELIGIBLE_FIELD = "isIntercomEligible";
  public static final String TAGS_FIELD = "tags";
  public static final String DISCOVERY_INCLUDES_FQDN_FIELD = "fqdn";
  public static final String ALERTS_PAUSED_FIELD = "alertsPaused";
  public static final String USE_CN_REGIONS_ONLY = "useCNRegionsOnly";
  public static final String CREATED_WITH_DEFAULT_ALERT_SETTINGS =
      "createdWithDefaultAlertSettings";

  public static final String JSON_NAME_FIELD = "name";
  public static final String JSON_DISABLE_DBSTATS_FIELD = "disableDbstats";
  public static final String JSON_HAS_ACTIVE_UI_PROPERTY = "hasActiveUI";
  public static final String JSON_PREFERRED_HOSTNAMES_FIELD = "preferredHostnames";
  public static final String JSON_SUMMARY_STATISTICS_FIELD = "summaryStatistics";

  public static final String STORAGE_CONFIG = "storageConfig";
  public static final String LATEST_MONITORING_AGENT_VERSION_EXPERIENCED_FIELD = "lmav";
  public static final String ORG_ID_FIELD = "orgId";

  public static final String LICENSES_ACCEPTED_FIELD = "licensesAccepted";
  public static final String LICENSES_REQUIRED_FIELD = "licensesRequired";

  public static final String LAST_CLUSTER_ACTIVE_SAMPLE_TIME = "lastClusterActiveSampleTime";
  public static final String CANONICAL_HOSTS_TTL_MINUTES_FIELD = "chttl";
  public static final String MAX_M0_CLUSTERS = "maxM0ClustersForGroup";
  public static final String INTEGRATIONS_FIELD = "integrations";
  public static final String PROJECT_OVERVIEW_FIELD = "isProjectOverviewEnabled";
  public static final String ENABLE_CURRENT_IP_WARNING_FIELD = "enableCurrentIpWarning";

  // Support for legacy operators that only send the tag. This will create the equivalent feature
  // control policies when the group is made
  public static final String LEGACY_OPERATOR_SUPPORT_TAG_EXTERNALLY_MANAGED_KUBERNETES =
      "EXTERNALLY_MANAGED_BY_KUBERNETES";

  public static final String PARTNER_INTEGRATIONS_DATA_FIELD = "partnerIntegrationsData";

  @Id
  @JsonProperty("id")
  private ObjectId id;

  /**
   * groups/project/customers is sharded, so we needed a shard key. based on the criteria for shard
   * key, nothing fits except for this "rcid" which is a reversed object ID that no longer runs into
   * the linearly increasing issue that object Ids have
   *
   * <p>see also <a
   * href="https://docs.google.com/document/d/1cKKadoASlyEfBuu83Blc9KTsDssjKTnc2iMOQaGOyAw/edit">this
   * doc</a>
   */
  @Property(REVERSED_GROUP_ID_FIELD)
  @BsonProperty(REVERSED_GROUP_ID_FIELD)
  private String rcid;

  @Property(NAME_FIELD)
  @JsonProperty(JSON_NAME_FIELD)
  @BsonProperty(NAME_FIELD)
  private String name;

  @JsonProperty(TEAMS_FIELD)
  @Embedded(TEAMS_FIELD)
  @BsonProperty(TEAMS_FIELD)
  private List<TeamRoleAssignment> teams;

  @Property(NAME_LOWERCASE_FIELD)
  @BsonProperty(NAME_LOWERCASE_FIELD)
  private String nameLowercase;

  @Property(NAME_TOKENS_FIELD)
  @BsonProperty(NAME_TOKENS_FIELD)
  private List<String> nameTokens;

  @JsonProperty(GROUP_TYPE_FIELD)
  @Property(GROUP_TYPE_FIELD)
  @BsonProperty(GROUP_TYPE_FIELD)
  private GroupType groupType;

  @JsonProperty(GROUP_VISIBILITY_FIELD)
  @Property(GROUP_VISIBILITY_FIELD)
  @BsonProperty(GROUP_VISIBILITY_FIELD)
  @Nullable
  private GroupVisibility groupVisibility;

  @Property(DISABLE_DBSTATS_FIELD)
  @BsonProperty(DISABLE_DBSTATS_FIELD)
  private Boolean disableDbstats;

  @JsonProperty("defaultTimeZoneId")
  @Property(DEFAULT_TIME_ZONE_ID_FIELD)
  @BsonProperty(DEFAULT_TIME_ZONE_ID_FIELD)
  private String defaultTimeZoneId;

  @JsonProperty("defaultTimeZoneDisplay")
  @Property(DEFAULT_TIME_ZONE_DISPLAY_FIELD)
  @BsonProperty(DEFAULT_TIME_ZONE_DISPLAY_FIELD)
  @SuppressWarnings("unused") // this field is still used by frontend code
  private String defaultTimeZoneDisplay;

  @JsonProperty(CREATED_FIELD)
  @Property(CREATED_FIELD)
  @BsonProperty(CREATED_FIELD)
  private Date created;

  @Embedded(PREFERRED_HOSTNAMES_FIELD)
  @JsonProperty(JSON_PREFERRED_HOSTNAMES_FIELD)
  @BsonProperty(PREFERRED_HOSTNAMES_FIELD)
  private List<PreferredHostname> preferredHostnames;

  @Embedded(EXPERIMENTS_FIELD)
  @JsonProperty(EXPERIMENTS_FIELD)
  @BsonProperty(EXPERIMENTS_FIELD)
  @SuppressWarnings("unused") // Unclear if this is still used by frontend code
  private List<ExperimentRegistration> experimentRegistrations;

  @Embedded(HOST_COUNTS_FIELD)
  @BsonProperty(HOST_COUNTS_FIELD)
  private GroupHostCount hostCounts;

  @Property(REPLICA_SET_COUNT_FIELD)
  @BsonProperty(REPLICA_SET_COUNT_FIELD)
  private int replicaSetCount = 0;

  @Property(SHARD_COUNT_FIELD)
  @BsonProperty(SHARD_COUNT_FIELD)
  private int shardCount = 0;

  @JsonProperty(ACTIVE_AGENT_COUNT_FIELD)
  @Property(ACTIVE_AGENT_COUNT_FIELD)
  @BsonProperty(ACTIVE_AGENT_COUNT_FIELD)
  private Integer activeAgentCount = 0;

  @Property(LAST_ACTIVE_AGENT_FIELD)
  @BsonProperty(LAST_ACTIVE_AGENT_FIELD)
  private Date lastActiveAgent;

  @Embedded(BACKUP_CONFIG_FIELD)
  @BsonProperty(BACKUP_CONFIG_FIELD)
  private GroupBackupConfig backupConfig;

  @Embedded(SUMMARY_STATISTICS_FIELD)
  @BsonProperty(SUMMARY_STATISTICS_FIELD)
  private GroupSummaryStatistics groupSummaryStatistics;

  @Embedded(FEATURE_FLAGS_FIELD)
  @JsonProperty(FEATURE_FLAGS_FIELD)
  @BsonProperty(FEATURE_FLAGS_FIELD)
  private List<ToggleableFeatureFlag> featureFlags;

  @JsonProperty(HAS_ADDED_HOSTS_FIELD)
  @Property(HAS_ADDED_HOSTS_FIELD)
  @BsonProperty(HAS_ADDED_HOSTS_FIELD)
  private Boolean hasAddedHosts;

  @Property(HAS_ADDED_AUTOMATION_AGENTS_FIELD)
  @BsonProperty(HAS_ADDED_AUTOMATION_AGENTS_FIELD)
  @SuppressWarnings("unused") // This field is still used; see GroupDao.setHasAddedAutomationAgents
  private Boolean hasAddedAutomationAgents;

  @Property(MONGOS_DEACTIVATION_THRESHOLD_FIELD)
  @BsonProperty(MONGOS_DEACTIVATION_THRESHOLD_FIELD)
  private Long mongosDeactivationThreshold;

  @Property(DEFAULT_WAIT_PROCESS_DOWN_TIMEOUT_FIELD)
  @BsonProperty(DEFAULT_WAIT_PROCESS_DOWN_TIMEOUT_FIELD)
  private Long defaultWaitProcessDownTimeout;

  @Property(AGENT_RESOURCE_MONITOR_COLLECTION_INTERVAL_FIELD)
  @BsonProperty(AGENT_RESOURCE_MONITOR_COLLECTION_INTERVAL_FIELD)
  private Long agentResourceMonitorCollectionInterval;

  @Property(AGENT_RESOURCE_MONITOR_HEAP_THRESHOLD_FIELD)
  @BsonProperty(AGENT_RESOURCE_MONITOR_HEAP_THRESHOLD_FIELD)
  private Long agentResourceMonitorHeapThreshold;

  @Property(AGENT_RESOURCE_MONITOR_RSS_THRESHOLD_FIELD)
  @BsonProperty(AGENT_RESOURCE_MONITOR_RSS_THRESHOLD_FIELD)
  private Long agentResourceMonitorRssThreshold;

  @Property(AGENT_RESOURCE_MONITOR_CPU_THRESHOLD_FIELD)
  @BsonProperty(AGENT_RESOURCE_MONITOR_CPU_THRESHOLD_FIELD)
  private Long agentResourceMonitorCPUThreshold;

  @Property(AGENT_RESOURCE_MONITOR_NORMALIZED_CPU_THRESHOLD_FIELD)
  @BsonProperty(AGENT_RESOURCE_MONITOR_NORMALIZED_CPU_THRESHOLD_FIELD)
  private Long agentResourceMonitorNormalizedCPUThreshold;

  @Property(AGENT_RESOURCE_MONITOR_TOP_HEAP_ITEMS_FIELD)
  @BsonProperty(AGENT_RESOURCE_MONITOR_TOP_HEAP_ITEMS_FIELD)
  private Long agentResourceMonitorTopHeapItems;

  @Property(AGENT_RESOURCE_MONITOR_TOP_CPU_ITEMS_FIELD)
  @BsonProperty(AGENT_RESOURCE_MONITOR_TOP_CPU_ITEMS_FIELD)
  private Long agentResourceMonitorTopCPUItems;

  @Property(AGENT_RESOURCE_MONITOR_CPU_PROFILE_DURATION_FIELD)
  @BsonProperty(AGENT_RESOURCE_MONITOR_CPU_PROFILE_DURATION_FIELD)
  @SuppressWarnings("unused") // In use by frontend; see GroupResource
  private Long agentResourceMonitorCPUProfileDuration;

  @Property(AGENT_RESOURCE_MONITOR_THRESHOLD_CROSS_BACKOFF_DURATION_FIELD)
  @BsonProperty(AGENT_RESOURCE_MONITOR_THRESHOLD_CROSS_BACKOFF_DURATION_FIELD)
  @SuppressWarnings("unused") // In use by frontend; see GroupResource
  private Long agentResourceMonitorThresholdCrossBackoffDuration;

  @JsonProperty(SUPPRESS_MONGOS_AUTO_DISCOVERY_FIELD)
  @Property(SUPPRESS_MONGOS_AUTO_DISCOVERY_FIELD)
  @BsonProperty(SUPPRESS_MONGOS_AUTO_DISCOVERY_FIELD)
  private Boolean suppressMongosAutoDiscovery;

  @JsonProperty(NEW_RELIC_LICENSE_KEY)
  @Property(NEW_RELIC_LICENSE_KEY)
  @BsonProperty(NEW_RELIC_LICENSE_KEY)
  private String newRelicLicenseKey;

  @JsonProperty(NEW_RELIC_INSIGHTS_WRITE_TOKEN)
  @Property(NEW_RELIC_INSIGHTS_WRITE_TOKEN)
  @BsonProperty(NEW_RELIC_INSIGHTS_WRITE_TOKEN)
  private String newRelicInsightsWriteToken;

  @JsonProperty(NEW_RELIC_INSIGHTS_READ_TOKEN)
  @Property(NEW_RELIC_INSIGHTS_READ_TOKEN)
  @BsonProperty(NEW_RELIC_INSIGHTS_READ_TOKEN)
  private String newRelicInsightsReadToken;

  @JsonProperty(NEW_RELIC_INSIGHTS_ACCOUNT_ID)
  @Property(NEW_RELIC_INSIGHTS_ACCOUNT_ID)
  @BsonProperty(NEW_RELIC_INSIGHTS_ACCOUNT_ID)
  private String newRelicInsightsAccountId;

  @Property(AUTOMATION_AGENT_LOG_LEVEL_FIELD)
  @BsonProperty(AUTOMATION_AGENT_LOG_LEVEL_FIELD)
  private String automationAgentLogLevel;

  @Property(AUTOMATION_AGENT_HTTP_LOG_LEVEL_FIELD)
  @BsonProperty(AUTOMATION_AGENT_HTTP_LOG_LEVEL_FIELD)
  private String automationAgentHttpLogLevel;

  @Property(BACKUP_AGENT_LOG_LEVEL_FIELD)
  @BsonProperty(BACKUP_AGENT_LOG_LEVEL_FIELD)
  private String backupAgentLogLevel;

  @Property(MONITORING_AGENT_LOG_LEVEL_FIELD)
  @BsonProperty(MONITORING_AGENT_LOG_LEVEL_FIELD)
  private String monitoringAgentLogLevel;

  @Embedded(IDP_MAPPING_FIELD)
  @BsonProperty(IDP_MAPPING_FIELD)
  private Set<IdpMapping> idpMapping;

  @Property(GA_CLIENT_ID_FIELD)
  @BsonProperty(GA_CLIENT_ID_FIELD)
  private String clientId;

  @Property(ENABLE_ALL_HOST_PROFILERS_FIELD)
  @JsonProperty(ENABLE_ALL_HOST_PROFILERS_FIELD)
  @BsonProperty(ENABLE_ALL_HOST_PROFILERS_FIELD)
  private Boolean enableAllHostProfilers = Boolean.FALSE;

  @Property(INCLUDE_JSON_WITH_ALERTS_FIELD)
  @BsonProperty(INCLUDE_JSON_WITH_ALERTS_FIELD)
  private final Boolean includeJsonWithAlerts = Boolean.FALSE;

  @Embedded(RATE_LIMIT_FIELD)
  @BsonProperty(RATE_LIMIT_FIELD)
  private GroupRateLimit groupRateLimit;

  @Property(SALESFORCE_ACCOUNT_ID_FIELD)
  @BsonProperty(SALESFORCE_ACCOUNT_ID_FIELD)
  private String salesforceAccountId;

  @Property(SALESFORCE_PROJECT_ID_FIELD)
  @BsonProperty(SALESFORCE_PROJECT_ID_FIELD)
  private String salesforceProjectId;

  @Property(SALESFORCE_BILLING_CONTACT_ID_FIELD)
  @BsonProperty(SALESFORCE_BILLING_CONTACT_ID_FIELD)
  private String salesforceBillingContactId;

  @JsonProperty(STATUS_FIELD)
  @Embedded(STATUS_FIELD)
  @BsonProperty(STATUS_FIELD)
  private GroupStatus status;

  @JsonProperty(PROMETHEUS_CONFIG_FIELD)
  @Embedded(PROMETHEUS_CONFIG_FIELD)
  @BsonProperty(PROMETHEUS_CONFIG_FIELD)
  private PrometheusConfig prometheusConfig;

  @Property(EXEMPT_FROM_BILLING_ON_INDEXSIZE_FIELD)
  @BsonProperty(EXEMPT_FROM_BILLING_ON_INDEXSIZE_FIELD)
  private Boolean exemptFromBillingOnIndexSize;

  @JsonProperty(STORAGE_CONFIG)
  @Embedded(STORAGE_CONFIG)
  @BsonProperty(STORAGE_CONFIG)
  private GroupStorageConfig storageConfig;

  @Property(TAGS_FIELD)
  @BsonProperty(TAGS_FIELD)
  private List<String> tags;

  @Property(DISCOVERY_INCLUDES_FQDN_FIELD)
  @BsonProperty(DISCOVERY_INCLUDES_FQDN_FIELD)
  private Boolean discoveryIncludesFQDN;

  @Property(LATEST_MONITORING_AGENT_VERSION_EXPERIENCED_FIELD)
  @JsonProperty(LATEST_MONITORING_AGENT_VERSION_EXPERIENCED_FIELD)
  @BsonProperty(LATEST_MONITORING_AGENT_VERSION_EXPERIENCED_FIELD)
  private String latestMonitoringAgentVersionExperienced;

  @Property(ORG_ID_FIELD)
  @BsonProperty(ORG_ID_FIELD)
  private ObjectId orgId;

  @Property(LICENSES_ACCEPTED_FIELD)
  @BsonProperty(LICENSES_ACCEPTED_FIELD)
  private Set<String> licensesAccepted;

  @Property(LICENSES_REQUIRED_FIELD)
  @BsonProperty(LICENSES_REQUIRED_FIELD)
  private Set<String> licensesRequired;

  @Property(ALERTS_PAUSED_FIELD)
  @JsonProperty(ALERTS_PAUSED_FIELD)
  private boolean alertsPaused;

  @Property(CREATED_WITH_DEFAULT_ALERT_SETTINGS)
  @BsonProperty(CREATED_WITH_DEFAULT_ALERT_SETTINGS)
  private Boolean createdWithDefaultAlertSettings;

  @JsonProperty(USE_CN_REGIONS_ONLY)
  @Property(USE_CN_REGIONS_ONLY)
  @BsonProperty(USE_CN_REGIONS_ONLY)
  private boolean useCNRegionsOnly;

  @JsonProperty(LAST_CLUSTER_ACTIVE_SAMPLE_TIME)
  @Property(LAST_CLUSTER_ACTIVE_SAMPLE_TIME)
  @BsonProperty(LAST_CLUSTER_ACTIVE_SAMPLE_TIME)
  private Date lastClusterActiveSampleTime;

  @Property(CANONICAL_HOSTS_TTL_MINUTES_FIELD)
  @JsonProperty(CANONICAL_HOSTS_TTL_MINUTES_FIELD)
  @BsonProperty(CANONICAL_HOSTS_TTL_MINUTES_FIELD)
  private long canonicalHostsTTLMinutes;

  @Property(MAX_M0_CLUSTERS)
  @JsonProperty(MAX_M0_CLUSTERS)
  @BsonProperty(MAX_M0_CLUSTERS)
  private int maxM0Clusters;

  @Embedded(INTEGRATIONS_FIELD)
  @JsonProperty(INTEGRATIONS_FIELD)
  @BsonProperty(INTEGRATIONS_FIELD)
  private Map<String, List<Integration>> integrations;

  @Property(PROJECT_OVERVIEW_FIELD)
  @JsonProperty(PROJECT_OVERVIEW_FIELD)
  @BsonProperty(PROJECT_OVERVIEW_FIELD)
  private Boolean isProjectOverviewEnabled;

  @Property(ENABLE_CURRENT_IP_WARNING_FIELD)
  @JsonProperty(ENABLE_CURRENT_IP_WARNING_FIELD)
  @BsonProperty(ENABLE_CURRENT_IP_WARNING_FIELD)
  private Boolean enableCurrentIpWarning;

  @Embedded(PARTNER_INTEGRATIONS_DATA_FIELD)
  @JsonProperty(PARTNER_INTEGRATIONS_DATA_FIELD)
  @BsonProperty(PARTNER_INTEGRATIONS_DATA_FIELD)
  private PartnerIntegrationsData partnerIntegrationsData;

  /**
   * Is the most recent contact for this group within the specified duration? Note that this method
   * simply checks the group's `lastActiveAgent` field, which is a cached value that is only updated
   * periodically (roughly once a day). Thus, passing a pDuration less than 24 hours will not give
   * an accurate result.
   *
   * @param pDuration duration to allow for last ping
   * @param pNow current instant
   * @return true if the last agent ping is within the specified duration, false otherwise
   */
  public boolean isLastAgentContactWithin(Duration pDuration, Instant pNow) {
    Date lastActiveAgent = getLastActiveAgent();
    if (lastActiveAgent == null) {
      return false;
    }
    Instant earliestInstant = pNow.minus(pDuration);
    return lastActiveAgent.toInstant().isAfter(earliestInstant);
  }

  public void setId(ObjectId pV) {
    id = pV;
    rcid = reverseObjectId(pV);
  }

  @Override
  public ObjectId getId() {
    return id;
  }

  public String getReversedGroupId() {
    return rcid;
  }

  /** Sets name, lowercase name */
  @BsonProperty(NAME_FIELD)
  public void setName(String pV) {
    name = pV;
    nameLowercase = name.toLowerCase();
  }

  @Override
  @BsonProperty(NAME_FIELD)
  public String getName() {
    return name;
  }

  @Override
  public List<TeamRoleAssignment> getTeams() {
    if (teams == null) {
      return Collections.emptyList();
    }
    return teams;
  }

  public List<TeamRoleAssignment> addTeams(List<TeamRoleAssignment> pTeamRoleAssignments) {
    teams = Stream.of(getTeams(), pTeamRoleAssignments).flatMap(Collection::stream).toList();
    return teams;
  }

  public void setTeams(List<TeamRoleAssignment> pTeams) {
    teams = pTeams;
  }

  public Set<ObjectId> getTeamIds() {
    if (teams == null) {
      return Collections.emptySet();
    }
    return teams.stream().map(TeamRoleAssignment::getId).collect(Collectors.toSet());
  }

  public Set<Role> getRolesByTeamIds(Collection<ObjectId> pTeamIds) {
    return teams.stream()
        .filter(tra -> pTeamIds.contains(tra.getId()))
        .flatMap(tra -> tra.getRoles().stream())
        .collect(Collectors.toSet());
  }

  public boolean isTeamGroupOwner(ObjectId pTeamId) {
    return getRolesByTeamIds(Collections.singletonList(pTeamId)).contains(Role.GROUP_OWNER);
  }

  public void setOrgId(ObjectId pV) {
    orgId = pV;
  }

  public ObjectId getOrgId() {
    return orgId;
  }

  public String getNameLowercase() {
    return nameLowercase;
  }

  public List<String> getNameTokens() {
    return nameTokens;
  }

  public void setNameTokens(List<String> pNameTokens) {
    nameTokens = pNameTokens;
  }

  public GroupType getGroupType() {
    return groupType;
  }

  public boolean isAtlas() {
    return getGroupType() == GroupType.NDS;
  }

  public boolean isCloudManager() {
    return groupType == GroupType.CLOUD;
  }

  public boolean isOpsManager() {
    return groupType == GroupType.ONPREM;
  }

  public void setGroupType(GroupType pGroupType) {
    groupType = pGroupType;
  }

  @Nullable
  public GroupVisibility getGroupVisibility() {
    return groupVisibility;
  }

  public void setGroupVisibility(GroupVisibility pGroupVisibility) {
    groupVisibility = pGroupVisibility;
  }

  public void setDisableDbstats(Boolean pV) {
    disableDbstats = pV;
  }

  @JsonProperty(JSON_DISABLE_DBSTATS_FIELD)
  public boolean getDisableDbstats() {
    return disableDbstats != null && disableDbstats;
  }

  public void setPreferredHostnames(List<PreferredHostname> pV) {
    preferredHostnames = pV;
  }

  public List<PreferredHostname> getPreferredHostnames() {
    return preferredHostnames;
  }

  public boolean hasPreferredHostnames() {
    return (preferredHostnames != null && !preferredHostnames.isEmpty());
  }

  public void addPreferredHostname(PreferredHostname pV) {
    if (preferredHostnames == null) {
      preferredHostnames = new ArrayList<>();
    }
    preferredHostnames.add(pV);
  }

  public boolean matchesPreferredHostname(String pHostname) {
    return hasPreferredHostnames()
        && getPreferredHostnames().stream().anyMatch(p -> p.matches(pHostname));
  }

  public void setCreated(Date pV) {
    created = pV;
  }

  public Date getCreated() {
    return created;
  }

  public boolean hasCreated() {
    return (created != null);
  }

  public void setDefaultTimeZoneId(String pV) {
    defaultTimeZoneId = pV;
  }

  public String getDefaultTimeZoneId() {
    return defaultTimeZoneId;
  }

  public boolean hasDefaultTimeZoneId() {
    return (defaultTimeZoneId != null);
  }

  public String getDefaultTimeZoneDisplay() {
    return defaultTimeZoneDisplay;
  }

  @JsonProperty("defaultTimeZoneDisplayShort")
  public String getDefaultTimeZoneShort() {
    if (getDefaultTimeZoneId() == null) {
      return null;
    }
    return TZUtils.getTimeZoneDisplayShort(getDefaultTimeZoneId());
  }

  public void setHostCounts(GroupHostCount pV) {
    hostCounts = pV;
  }

  public GroupHostCount getHostCounts() {
    return hostCounts;
  }

  public Map<String, List<Integration>> getAllIntegrations() {
    return integrations == null ? new HashMap<>() : integrations;
  }

  @VisibleForTesting
  public void setDefaultIntegrations(Map<String, List<Integration>> integrationMap) {
    if (integrationMap != null) {
      integrationMap.forEach(
          (type, integrations) -> {
            integrations.forEach(
                integration -> {
                  Map<String, Object> errors = integration.validate();
                  if (!integration.validate().isEmpty()) {
                    throw new IllegalArgumentException(errors.toString());
                  }
                });
          });
    }
    integrations = integrationMap;
  }

  @VisibleForTesting
  public void setDefaultIntegration(Integration integration) {
    Map<String, Object> errors = integration.validate();

    if (!integration.validate().isEmpty()) {
      throw new IllegalArgumentException(errors.toString());
    }
    setDefaultIntegrationWithoutValidation(integration);
  }

  @VisibleForTesting
  public void setDefaultIntegrationWithoutValidation(Integration integration) {
    if (integrations == null) {
      integrations = new HashMap<>();
    }

    integrations.put(integration.getTypeName(), List.of(integration));
  }

  public List<Integration> getIntegrations(String integrationType) {
    List<Integration> integrations = getAllIntegrations().get(integrationType);
    return integrations == null ? Collections.emptyList() : integrations;
  }

  public Integration getIntegrationById(ObjectId integrationId) {
    if (integrationId == null) {
      return null;
    }
    List<Integration> integrations =
        getAllIntegrations().values().stream()
            .filter(Objects::nonNull)
            .flatMap(Collection::stream)
            .filter(Objects::nonNull)
            .toList();
    return integrations.stream()
        .filter(integration -> integrationId.equals(integration.getId()))
        .findAny()
        .orElse(null);
  }

  public Optional<Integration> getIntegration(String integrationType) {
    List<Integration> integrations = getIntegrations(integrationType);
    return integrations.isEmpty() ? Optional.empty() : Optional.of(integrations.get(0));
  }

  public boolean hasIntegrations(String integrationType) {
    List<Integration> integrations = getIntegrations(integrationType);
    return !integrations.isEmpty();
  }

  public boolean hasHostCounts() {
    return hostCounts != null;
  }

  public void setReplicaSetCount(int pV) {
    replicaSetCount = pV;
  }

  public int getReplicaSetCount() {
    return replicaSetCount;
  }

  public void setShardCount(int pV) {
    shardCount = pV;
  }

  public int getShardCount() {
    return shardCount;
  }

  public void setActiveAgentCount(Integer pV) {
    activeAgentCount = pV;
  }

  public int getActiveAgentCount() {
    return activeAgentCount != null ? activeAgentCount : 0;
  }

  public void setLastActiveAgent(Date pV) {
    lastActiveAgent = pV;
  }

  public Date getLastActiveAgent() {
    return lastActiveAgent;
  }

  public Set<IdpMapping> getIdpMapping() {
    if (idpMapping == null) {
      idpMapping = new HashSet<>();
    }
    return idpMapping;
  }

  public void setIdpMapping(Set<IdpMapping> pValues) {
    idpMapping = pValues;
  }

  @JsonProperty(JSON_SUMMARY_STATISTICS_FIELD)
  public GroupSummaryStatistics getSummaryStatistics() {
    return groupSummaryStatistics == null ? new GroupSummaryStatistics() : groupSummaryStatistics;
  }

  // Created for testing purposes
  public void setSummaryStatistics(GroupSummaryStatistics pGroupSummaryStatistics) {
    groupSummaryStatistics = pGroupSummaryStatistics;
  }

  public GroupBackupConfig getBackupConfig() {
    /* Temporary (hopefully) work around.
     * Morphia doesn't save empty documents so _backupConfig will be null
     *  if there are no replica sets or clusters configured
     */
    return ensureBackupConfig();
  }

  public void setBackupConfig(GroupBackupConfig pBackupConfig) {
    backupConfig = pBackupConfig;
  }

  public GroupBackupConfig ensureBackupConfig() {
    if (backupConfig == null) {
      backupConfig = new GroupBackupConfig();
    }
    return backupConfig;
  }

  @JsonProperty("hasActiveBackups")
  public boolean hasActiveBackups() {
    GroupBackupConfig config = getBackupConfig();
    return config.hasActiveMembers();
  }

  // Adds flag if does not exist, otherwise toggles it according to state of
  // given flag.
  private void addToggleableFeatureFlag(ToggleableFeatureFlag pFlag) {
    if (featureFlags == null) {
      featureFlags = new LinkedList<>();
    }

    for (ToggleableFeatureFlag flag : featureFlags) {
      if (flag.getFlag().equals(pFlag.getFlag())) {
        flag.setEnabled(pFlag.getEnabled());
        return;
      }
    }

    featureFlags.add(pFlag);
  }

  // NOTE: the following two write methods should not be used to directly set / unset
  // feature flags (with the exception, perhaps, of unit testing). Please use
  // FeatureFlagSvc.enableFeature / FeatureFlagSvc.disableFeature instead.
  public void enableFeatureFlag(FeatureFlag pFlag) {
    ToggleableFeatureFlag enabledFlag = ToggleableFeatureFlag.enabledFlag(pFlag);
    this.addToggleableFeatureFlag(enabledFlag);
  }

  public void disableFeatureFlag(FeatureFlag pFlag) {
    ToggleableFeatureFlag disabledFlag = ToggleableFeatureFlag.disabledFlag(pFlag);
    this.addToggleableFeatureFlag(disabledFlag);
  }

  public boolean removeFeatureFlag(FeatureFlag pFlag) {
    if (featureFlags != null) {
      return featureFlags.removeIf(f -> f.getFlag().equals(pFlag));
    }
    return false;
  }

  public boolean hasTeamInGroup(ObjectId pTeamId) {
    return getTeamIds().contains(pTeamId);
  }

  private boolean hasFlag(FeatureFlag pFlag, boolean pEnabled) {
    if (featureFlags != null) {
      return featureFlags.stream()
          .anyMatch(f -> f.getFlag().equals(pFlag) && f.getEnabled() == pEnabled);
    }

    return false;
  }

  public List<ToggleableFeatureFlag> getFeatureFlags() {
    return featureFlags;
  }

  public Optional<ToggleableFeatureFlag> getFeatureFlag(FeatureFlag pFlag) {
    if (featureFlags != null) {
      return featureFlags.stream().filter(f -> f.getFlag().equals(pFlag)).findFirst();
    }
    return Optional.empty();
  }

  // NOTE: the following two read methods should not be used to directly check whether or
  // not a feature is enabled (with the exception, perhaps, of unit testing). Please
  // use FeatureFlag.enabled() instead.
  public boolean hasEnabledFeatureFlag(FeatureFlag pFlag) {
    return hasFlag(pFlag, true);
  }

  public boolean hasDisabledFeatureFlag(FeatureFlag pFlag) {
    return hasFlag(pFlag, false);
  }

  public void setMongosDeactivationThreshold(Long pV) {
    mongosDeactivationThreshold = pV;
  }

  public Long getMongosDeactivationThreshold() {
    return mongosDeactivationThreshold;
  }

  public long getMongoDbDefaultShutdownTimeout() {
    return defaultWaitProcessDownTimeout != null
        ? defaultWaitProcessDownTimeout
        : DEFAULT_MONGODB_SHUTDOWN_TIMEOUT_IN_MINUTES;
  }

  public void setMongoDbDefaultShutdownTimeout(Long pV) {
    defaultWaitProcessDownTimeout = pV;
  }

  public void setAgentResourceMonitorCollectionInterval(Long pV) {
    agentResourceMonitorCollectionInterval = pV;
  }

  public Long getAgentResourceMonitorCollectionInterval() {
    return agentResourceMonitorCollectionInterval != null
        ? agentResourceMonitorCollectionInterval
        : DEFAULT_AGENT_RESOURCE_MONITOR_COLLECTION_INTERVAL_IN_SECONDS;
  }

  public Long getAgentResourceMonitorHeapThreshold() {
    return agentResourceMonitorHeapThreshold != null
        ? agentResourceMonitorHeapThreshold
        : DEFAULT_AGENT_RESOURCE_MONITOR_HEAP_THRESHOLD_IN_MB;
  }

  public void setAgentResourceMonitorHeapThreshold(Long pV) {
    this.agentResourceMonitorHeapThreshold = pV;
  }

  public Long getAgentResourceMonitorRssThreshold() {
    return agentResourceMonitorRssThreshold != null
        ? agentResourceMonitorRssThreshold
        : DEFAULT_AGENT_RESOURCE_MONITOR_RSS_THRESHOLD_IN_PCT;
  }

  public void setAgentResourceMonitorRssThreshold(Long pV) {
    this.agentResourceMonitorRssThreshold = pV;
  }

  public Long getAgentResourceMonitorCPUThreshold() {
    return agentResourceMonitorCPUThreshold != null
        ? agentResourceMonitorCPUThreshold
        : DEFAULT_AGENT_RESOURCE_MONITOR_CPU_THRESHOLD_IN_PCT;
  }

  public void setAgentResourceMonitorCPUThreshold(Long pV) {
    this.agentResourceMonitorCPUThreshold = pV;
  }

  public Long getAgentResourceMonitorNormalizedCPUThreshold() {
    return agentResourceMonitorNormalizedCPUThreshold != null
        ? agentResourceMonitorNormalizedCPUThreshold
        : DEFAULT_AGENT_RESOURCE_MONITOR_NORMALIZED_CPU_THRESHOLD_IN_PCT;
  }

  public void setAgentResourceMonitorNormalizedCPUThreshold(Long pV) {
    this.agentResourceMonitorNormalizedCPUThreshold = pV;
  }

  public Long getAgentResourceMonitorTopHeapItems() {
    return agentResourceMonitorTopHeapItems != null
        ? agentResourceMonitorTopHeapItems
        : DEFAULT_AGENT_RESOURCE_MONITOR_TOP_HEAP_ITEMS;
  }

  public Long getAgentResourceMonitorTopCPUItems() {
    return agentResourceMonitorTopCPUItems != null
        ? agentResourceMonitorTopCPUItems
        : DEFAULT_AGENT_RESOURCE_MONITOR_TOP_CPU_ITEMS;
  }

  public Long getAgentResourceMonitorCPUProfileDuration() {
    return agentResourceMonitorCPUProfileDuration != null
        ? agentResourceMonitorCPUProfileDuration
        : DEFAULT_AGENT_RESOURCE_MONITOR_CPU_PROFILE_DURATION_IN_SECONDS;
  }

  public Long getAgentResourceMonitorThresholdCrossBackoffDuration() {
    return agentResourceMonitorThresholdCrossBackoffDuration != null
        ? agentResourceMonitorThresholdCrossBackoffDuration
        : DEFAULT_AGENT_RESOURCE_MONITOR_THRESHOLD_CROSS_BACKOFF_IN_MINUTES;
  }

  public void setSuppressMongosAutoDiscovery(Boolean pV) {
    suppressMongosAutoDiscovery = pV;
  }

  public boolean getSuppressMongosAutoDiscovery() {
    return suppressMongosAutoDiscovery != null && suppressMongosAutoDiscovery;
  }

  public boolean isHasAddedAutomationAgents() {
    return hasAddedAutomationAgents != null && hasAddedAutomationAgents;
  }

  public boolean getEnableAllHostProfilers() {
    return (enableAllHostProfilers != null) ? enableAllHostProfilers : false;
  }

  public void setEnableAllHostProfilers(boolean pV) {
    enableAllHostProfilers = pV;
  }

  public boolean getIncludeJsonWithAlerts() {
    return includeJsonWithAlerts != null ? includeJsonWithAlerts : false;
  }

  public boolean hasPagerDutyIntegration() {
    return hasIntegrations(PagerDutyIntegration.TYPE);
  }

  public String getRedactedPagerDutyServiceKey() {
    if (hasIntegrations(PagerDutyIntegration.TYPE)) {
      return ((PagerDutyIntegration) getIntegration(PagerDutyIntegration.TYPE).get())
          .getRedactedServiceKey();
    }
    return null;
  }

  public PagerDutyNotificationConstants.Region getPagerDutyRegion() {
    if (hasIntegrations(PagerDutyIntegration.TYPE)) {
      return ((PagerDutyIntegration) getIntegration(PagerDutyIntegration.TYPE).get()).getRegion();
    }
    return null;
  }

  public boolean hasHipChatIntegration() {
    return hasIntegrations(HipChatIntegration.TYPE)
        || (StringUtils.isNotBlank(getHipChatRoomName())
            && StringUtils.isNotBlank(getRedactedHipChatNotificationToken()));
  }

  public String getHipChatRoomName() {
    if (hasIntegrations(HipChatIntegration.TYPE)) {
      return ((HipChatIntegration) getIntegration(HipChatIntegration.TYPE).get()).getRoomName();
    }

    return null;
  }

  public String getRedactedHipChatNotificationToken() {
    if (hasIntegrations(HipChatIntegration.TYPE)) {
      return ((HipChatIntegration) getIntegration(HipChatIntegration.TYPE).get())
          .getRedactedNotificationToken();
    }

    return null;
  }

  public String getRedactedMicrosoftTeamsWebhookUrl() {
    List<Integration> integrations = getIntegrations(MicrosoftTeamsIntegration.TYPE);
    if (!integrations.isEmpty()) {
      return ((MicrosoftTeamsIntegration) integrations.get(0)).getRedactedWebhookUrl();
    }

    return null;
  }

  public boolean hasMicrosoftTeamsIntegration() {
    return hasIntegrations(MicrosoftTeamsIntegration.TYPE);
  }

  public boolean hasSlackIntegration() {
    return hasIntegrations(SlackIntegration.TYPE);
  }

  public boolean hasSlackOAuth2() {
    List<Integration> integrations = getIntegrations(SlackIntegration.TYPE);
    if (!integrations.isEmpty()) {
      return ((SlackIntegration) integrations.get(0)).hasTeamName();
    }
    return false;
  }

  public boolean hasNewRelicPluginSettings() {
    return StringUtils.isNotBlank(newRelicLicenseKey);
  }

  public boolean hasNewRelicInsightsWriteSettings() {
    return StringUtils.isNotBlank(newRelicInsightsWriteToken);
  }

  public boolean hasNewRelicInsightsAccountId() {
    return StringUtils.isNotBlank(newRelicInsightsAccountId);
  }

  public boolean hasNewRelicInsightsReadSettings() {
    return StringUtils.isNotBlank(newRelicInsightsReadToken);
  }

  public boolean hasNewRelicIntegration() {
    return hasNewRelicInsightsReadSettings()
        && hasNewRelicInsightsWriteSettings()
        && hasNewRelicPluginSettings()
        && hasNewRelicInsightsAccountId();
  }

  public String getRedactedSlackApiToken() {
    List<Integration> integrations = getIntegrations(SlackIntegration.TYPE);
    if (!integrations.isEmpty()) {
      return ((SlackIntegration) integrations.get(0)).getRedactedApiToken();
    }
    return null;
  }

  public String getSlackTeamName() {
    List<Integration> integrations = getIntegrations(SlackIntegration.TYPE);
    if (!integrations.isEmpty()) {
      return ((SlackIntegration) integrations.get(0)).getTeamName();
    }
    return null;
  }

  public String getSlackChannelName() {
    List<Integration> integrations = getIntegrations(SlackIntegration.TYPE);
    if (!integrations.isEmpty()) {
      return ((SlackIntegration) integrations.get(0)).getChannelName();
    }
    return null;
  }

  public String getNewRelicLicenseKey() {
    return newRelicLicenseKey;
  }

  public void setNewRelicLicenseKey(String pNewRelicLicenseKey) {
    newRelicLicenseKey = pNewRelicLicenseKey;
  }

  public String getNewRelicInsightsWriteToken() {
    return newRelicInsightsWriteToken;
  }

  public void setNewRelicInsightsWriteToken(String pNewRelicInsightsWriteToken) {
    newRelicInsightsWriteToken = pNewRelicInsightsWriteToken;
  }

  public String getNewRelicInsightsReadToken() {
    return newRelicInsightsReadToken;
  }

  public void setNewRelicInsightsReadToken(String pNewRelicInsightsReadToken) {
    newRelicInsightsReadToken = pNewRelicInsightsReadToken;
  }

  public String getNewRelicInsightsAccountId() {
    return newRelicInsightsAccountId;
  }

  public void setNewRelicInsightsAccountId(String pAccountId) {
    newRelicInsightsAccountId = pAccountId;
  }

  public String getSalesforceAccountId() {
    return salesforceAccountId;
  }

  public void setSalesforceAccountId(String pSalesforceAccountId) {
    salesforceAccountId = pSalesforceAccountId;
  }

  public String getSalesforceBillingContactId() {
    return salesforceBillingContactId;
  }

  public void setSalesforceBillingContactId(String pSalesforceBillingContactId) {
    salesforceBillingContactId = pSalesforceBillingContactId;
  }

  public String getDecryptedWebhookUrl() {
    if (hasIntegrations(WebhookIntegration.TYPE)) {
      return ((WebhookIntegration) getIntegration(WebhookIntegration.TYPE).get())
          .getDecryptedWebhookUrl();
    }

    return null;
  }

  public String getRedactedWebhookUrl() {
    if (hasIntegrations(WebhookIntegration.TYPE)) {
      return ((WebhookIntegration) getIntegration(WebhookIntegration.TYPE).get())
          .getRedactedWebhookUrl();
    }

    return null;
  }

  public boolean hasWebhookUrl() {
    return StringUtils.isNotBlank(getRedactedWebhookUrl());
  }

  public String getDecryptedWebhookSecret() {
    if (hasIntegrations(WebhookIntegration.TYPE)) {
      return ((WebhookIntegration) getIntegration(WebhookIntegration.TYPE).get())
          .getDecryptedWebhookSecret();
    }
    return null;
  }

  public String getRedactedWebhookSecret() {
    if (hasIntegrations(WebhookIntegration.TYPE)) {
      return ((WebhookIntegration) getIntegration(WebhookIntegration.TYPE).get())
          .getRedactedWebhookSecret();
    }
    return null;
  }

  public boolean hasWebhookIntegration() {
    return hasIntegrations(WebhookIntegration.TYPE);
  }

  public String getDecryptedDatadogApiKey() {
    if (hasIntegrations(DatadogIntegration.TYPE)) {
      return ((DatadogIntegration) getIntegration(DatadogIntegration.TYPE).get())
          .getDecryptedApiKey();
    }
    return null;
  }

  public String getRedactedDatadogApiKey() {
    if (hasIntegrations(DatadogIntegration.TYPE)) {
      return ((DatadogIntegration) getIntegration(DatadogIntegration.TYPE).get())
          .getRedactedApiKey();
    }
    return null;
  }

  public boolean hasDatadogIntegration() {
    return hasIntegrations(DatadogIntegration.TYPE);
  }

  public Region getDatadogRegion() {
    if (hasIntegrations(DatadogIntegration.TYPE)) {
      return ((DatadogIntegration) getIntegration(DatadogIntegration.TYPE).get()).getRegion();
    }
    return null;
  }

  public String getDatadogCustomEndpoint() {
    if (hasIntegrations(DatadogIntegration.TYPE)) {
      return ((DatadogIntegration) getIntegration(DatadogIntegration.TYPE).get())
          .getCustomEndpoint();
    }
    return null;
  }

  public boolean getDbStatsIntegrationEnabled() {
    if (hasIntegrations(DatadogIntegration.TYPE)) {
      return ((DatadogIntegration) getIntegration(DatadogIntegration.TYPE).get())
          .getDbStatsIntegrationEnabled();
    }
    return false;
  }

  public boolean getCollStatsIntegrationEnabled() {
    if (hasIntegrations(DatadogIntegration.TYPE)) {
      return ((DatadogIntegration) getIntegration(DatadogIntegration.TYPE).get())
          .getCollStatsIntegrationEnabled();
    }
    return false;
  }

  public boolean getQueryStatsIntegrationEnabled() {
    if (hasIntegrations(DatadogIntegration.TYPE)) {
      return ((DatadogIntegration) getIntegration(DatadogIntegration.TYPE).get())
          .getQueryStatsIntegrationEnabled();
    }
    return false;
  }

  public boolean getSendUserProvidedResourceTagsEnabledForDatadog() {
    if (hasIntegrations(DatadogIntegration.TYPE)) {
      return ((DatadogIntegration) getIntegration(DatadogIntegration.TYPE).get())
          .getSendUserProvidedResourceTagsEnabled();
    }
    return false;
  }

  public boolean getSendUserProvidedResourceTagsEnabledForPrometheus() {
    if (hasPrometheusIntegration()) {
      return getPromConfig().IsSendUserProvidedResourceTagsEnabled() != null
          && getPromConfig().IsSendUserProvidedResourceTagsEnabled();
    }

    return false;
  }

  public String getRedactedVictorOpsApiKey() {
    if (hasIntegrations(VictorOpsIntegration.TYPE)) {
      return ((VictorOpsIntegration) getIntegration(VictorOpsIntegration.TYPE).get())
          .getRedactedApiKey();
    }
    return null;
  }

  public String getVictorOpsRoutingKey() {
    if (hasIntegrations(VictorOpsIntegration.TYPE)) {
      return ((VictorOpsIntegration) getIntegration(VictorOpsIntegration.TYPE).get())
          .getRoutingKey();
    }
    return null;
  }

  public boolean hasVictorOpsIntegration() {
    return hasIntegrations(VictorOpsIntegration.TYPE);
  }

  public String getRedactedOpsgenieApiKey() {
    if (hasIntegrations(OpsGenieIntegration.TYPE)) {
      return ((OpsGenieIntegration) getIntegration(OpsGenieIntegration.TYPE).get())
          .getRedactedApiKey();
    }
    return null;
  }

  public OpsGenieNotificationConstants.Region getOpsGenieRegion() {
    if (hasIntegrations(OpsGenieIntegration.TYPE)) {
      return ((OpsGenieIntegration) getIntegration(OpsGenieIntegration.TYPE).get()).getRegion();
    }
    return null;
  }

  public boolean hasOpsGenieIntegration() {
    return hasIntegrations(OpsGenieIntegration.TYPE);
  }

  public boolean hasS3LogExportIntegration() {
    // Check if there's an S3 log export configuration in the external log sinks
    // This would need to be implemented based on the actual data model
    // For now, return false to indicate no S3 log export integration
    return false;
  }

  public boolean hasSplunkLogExportIntegration() {
    // Check if there's a Splunk log export configuration in the external log sinks
    // This would need to be implemented based on the actual data model
    // For now, return false to indicate no Splunk log export integration
    return false;
  }

  public boolean hasDatadogLogExportIntegration() {
    // Check if there's a Datadog log export configuration in the external log sinks
    // This would need to be implemented based on the actual data model
    // For now, return false to indicate no Datadog log export integration
    return false;
  }

  public boolean hasIntegrationSettings(IntegrationType pType) {
    return switch (pType) {
      case PAGER_DUTY -> hasPagerDutyIntegration();
      case MICROSOFT_TEAMS -> hasMicrosoftTeamsIntegration();
      case SLACK -> hasSlackIntegration();
      case DATADOG -> hasDatadogIntegration();
      case NEW_RELIC -> hasNewRelicIntegration();
      case OPS_GENIE -> hasOpsGenieIntegration();
      case VICTOR_OPS -> hasVictorOpsIntegration();
      case WEBHOOK -> hasWebhookIntegration();
      case HIP_CHAT -> hasHipChatIntegration();
      case PROMETHEUS -> hasPrometheusIntegration();
      case S3_LOG_EXPORT -> hasS3LogExportIntegration();
      case SPLUNK_LOG_EXPORT -> hasSplunkLogExportIntegration();
      case DATADOG_LOG_EXPORT -> hasDatadogLogExportIntegration();
    };
  }

  public List<String> getTags() {
    return tags != null ? Collections.unmodifiableList(tags) : Collections.emptyList();
  }

  public void setTags(List<String> pTags) {
    if (pTags != null) {
      tags = Collections.unmodifiableList(pTags);
    }
  }

  public void addTags(String... tags) {
    if (tags != null) {
      this.tags.addAll(Arrays.asList(tags));
    }
  }

  /**
   * Whether Monitoring Discovery should include serverStatus.advisoryHostFQDNs. Grandfathered in to
   * avoid unnecessary hostname changes for existing Groups.
   *
   * @return true for Groups created after v20160823, which have value set to true.
   */
  public boolean isDiscoveryIncludesFQDN() {
    return discoveryIncludesFQDN != null && discoveryIncludesFQDN;
  }

  public void setDiscoveryIncludesFQDN(Boolean pDiscoveryIncludesFQDN) {
    discoveryIncludesFQDN = pDiscoveryIncludesFQDN;
  }

  @Nullable
  public AgentLogLevel getAgentLogLevel(AgentType agentType) {
    return switch (agentType) {
      case AUTOMATION -> AgentLogLevel.of(automationAgentLogLevel);
      case BACKUP -> AgentLogLevel.of(backupAgentLogLevel);
      case MONITORING -> AgentLogLevel.of(monitoringAgentLogLevel);
      default -> null;
    };
  }

  public AgentLogLevel getAgentHttpLogLevel(AgentType agentType) {
    if (agentType == AgentType.AUTOMATION) {
      return AgentLogLevel.of(automationAgentHttpLogLevel);
    }

    return null;
  }

  public GroupStorageConfig getGroupStorageConfig() {
    if (storageConfig == null) {
      storageConfig = new GroupStorageConfig();
    }
    return storageConfig;
  }

  public void setGroupStorageConfig(GroupStorageConfig pStorageConfig) {
    storageConfig = pStorageConfig;
  }

  public boolean isRegisteredBackupCustomer() {
    return getBackupConfig().hasActiveMembers();
  }

  public void setGroupRateLimit(GroupRateLimit pGroupRateLimit) {
    groupRateLimit = pGroupRateLimit;
  }

  public GroupRateLimit getGroupRateLimit() {
    return groupRateLimit;
  }

  public boolean hasGroupRateLimit() {
    return groupRateLimit != null;
  }

  public void setGaClientId(String pClientId) {
    clientId = pClientId;
  }

  public String getGaClientId() {
    return clientId;
  }

  public void setStatus(GroupStatus pStatus) {
    status = pStatus;
  }

  public GroupStatus getStatus() {
    return status;
  }

  @JsonProperty(JSON_HAS_ACTIVE_UI_PROPERTY)
  public boolean hasActiveUI() {
    return status == null || status.hasActiveUI();
  }

  public boolean isExemptFromBillingOnIndexSize() {
    return exemptFromBillingOnIndexSize != null && exemptFromBillingOnIndexSize;
  }

  public void setExemptFromBillingOnIndexSize(boolean pExempt) {
    exemptFromBillingOnIndexSize = pExempt;
  }

  public boolean shouldSkipPingProcessing() {
    return getStatus() != null && GroupStatus.CLOSED_TYPES.contains(getStatus().getStatus());
  }

  public boolean useCNRegionsOnly() {
    return useCNRegionsOnly;
  }

  public void setUseCNRegionsOnly(boolean pUseCNRegionsOnly) {
    useCNRegionsOnly = pUseCNRegionsOnly;
  }

  public void setCanonicalHostsTTLMinutes(long pCanonicalHostsTTLMinutes) {
    canonicalHostsTTLMinutes = pCanonicalHostsTTLMinutes;
  }

  public long getCanonicalHostsTTLMinutes() {
    return canonicalHostsTTLMinutes;
  }

  public void setMaxM0Clusters(int pMaxM0Clusters) {
    maxM0Clusters = pMaxM0Clusters;
  }

  public int getMaxM0s() {
    return maxM0Clusters;
  }

  public boolean is(GroupStatus.Type pType) {
    return getStatus() != null && getStatus().getStatus() == pType;
  }

  public void activate() {
    setStatus(new GroupStatus(GroupStatus.Type.ACTIVE, new Date()));
  }

  public void close() {
    setStatus(new GroupStatus(GroupStatus.Type.CLOSED, new Date()));
  }

  public void kill() {
    setStatus(new GroupStatus(GroupStatus.Type.DEAD, new Date()));
  }

  public boolean isClosing() {
    return is(GroupStatus.Type.CLOSING);
  }

  public boolean isClosed() {
    return is(GroupStatus.Type.CLOSED);
  }

  public boolean isActive() {
    return is(GroupStatus.Type.ACTIVE);
  }

  public String getHomepageUri() {
    if (isAtlas()) {
      return "/v2/" + id + "#/clusters";
    } else {
      return "/v2/" + id + "#/deployment";
    }
  }

  public String getHomeCenterUri() {
    return "/v2/" + id + "#/overview";
  }

  public Set<String> getLicensesAccepted() {
    return licensesAccepted;
  }

  public Set<String> getLicensesRequired() {
    return licensesRequired;
  }

  @JsonProperty(IS_INTERCOM_ELIGIBLE_FIELD)
  public boolean isIntercomEligible() {
    return getGroupType() != GroupType.ONPREM;
  }

  public String getLatestMonitoringAgentVersionExperienced() {
    return latestMonitoringAgentVersionExperienced;
  }

  public boolean isAlertsPaused() {
    return alertsPaused;
  }

  public void setAlertsPaused(boolean pAlertsPaused) {
    alertsPaused = pAlertsPaused;
  }

  public void setLatestMonitoringAgentVersionExperienced(
      String pLatestMonitoringAgentVersionExperienced) {
    latestMonitoringAgentVersionExperienced = pLatestMonitoringAgentVersionExperienced;
  }

  public boolean isLatestMonitoringAgentVersionExperiencedIsAtLeast(AgentVersion pMinVersion) {
    String latestMonitoringAgentVersion = getLatestMonitoringAgentVersionExperienced();
    if (latestMonitoringAgentVersion != null) {
      AgentVersion latestAgentVersion =
          new AgentVersion(latestMonitoringAgentVersion, AgentType.MONITORING);
      return pMinVersion.isOlderThanOrEqual(latestAgentVersion);
    }
    return false;
  }

  @Override
  public boolean equals(Object pOther) {
    if (pOther == null) {
      return false;
    } else if (pOther == this) {
      return true;
    } else if (getClass() != pOther.getClass()) {
      return false;
    }
    Group that = (Group) pOther;
    return Objects.equals(this.id, that.id);
  }

  @Override
  public int hashCode() {
    return Objects.hashCode(id);
  }

  @Override
  public String toString() {
    return "Group{" + "_id=" + id + ", _name='" + name + '\'' + '}';
  }

  @Override
  public EntityType getEntityType() {
    return EntityType.GROUP;
  }

  public void setPromConfig(PrometheusConfig pPrometheus) {
    prometheusConfig = pPrometheus;
  }

  public PrometheusConfig getPromConfig() {
    return prometheusConfig;
  }

  public boolean hasPrometheusIntegration() {
    return prometheusConfig != null;
  }

  public Date getLastClusterActiveSampleTime() {
    return lastClusterActiveSampleTime;
  }

  public void setLastClusterActiveSampleTime(Date sampleTime) {
    lastClusterActiveSampleTime = sampleTime;
  }

  public Boolean getIsProjectOverviewEnabled() {
    return isProjectOverviewEnabled != null ? isProjectOverviewEnabled : false;
  }

  public void setIsProjectOverviewEnabled(Boolean _isProjectOverviewEnabled) {
    this.isProjectOverviewEnabled = _isProjectOverviewEnabled;
  }

  public boolean getEnableCurrentIpWarning() {
    return enableCurrentIpWarning != null ? enableCurrentIpWarning : false;
  }

  public void setEnableCurrentIpWarning(boolean pV) {
    enableCurrentIpWarning = pV;
  }

  public boolean isSystemProject() {
    return GroupVisibility.INTERNAL.equals(groupVisibility);
  }

  public void setCreatedWithDefaultAlertSettings(boolean createdWithDefaultAlertSettings) {
    this.createdWithDefaultAlertSettings = createdWithDefaultAlertSettings;
  }

  /**
   * Returns whether this group was created with default alert settings. For groups created before
   * this field existed, defaults to true to maintain backward compatibility.
   */
  public boolean getCreatedWithDefaultAlertSettings() {
    return createdWithDefaultAlertSettings != null ? createdWithDefaultAlertSettings : true;
  }

  public void setPartnerIntegrationsData(PartnerIntegrationsData partnerIntegrationsData) {
    this.partnerIntegrationsData = partnerIntegrationsData;
  }

  @Nullable
  public PartnerIntegrationsData getPartnerIntegrationsData() {
    return partnerIntegrationsData;
  }
}
