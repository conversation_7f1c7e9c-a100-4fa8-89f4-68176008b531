package com.xgen.cloud.sandbox._private.dao;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;

import com.mongodb.client.result.DeleteResult;
import com.xgen.cloud.common.dao.base._public.impl.BaseDao;
import com.xgen.cloud.common.db.mongo._public.container.MongoClientContainer;
import com.xgen.cloud.common.db.mongo._public.index.MongoIndex;
import com.xgen.cloud.sandbox._public.model.SandboxClusterTemplate;
import com.xgen.cloud.sandbox._public.model.SandboxConfig;
import com.xgen.cloud.sandbox._public.model.SandboxConfig.FieldDefs;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import java.util.List;
import java.util.Optional;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

@Singleton
public class SandboxConfigDao extends BaseDao<SandboxConfig> {
  public static final String CONNECTION_NAME = "ui";
  public static final String DB_NAME = "enterpriseSandbox";
  public static final String COLLECTION_NAME = "configs";

  @Inject
  public SandboxConfigDao(MongoClientContainer container, CodecRegistry codecRegistry) {
    super(container, CONNECTION_NAME, DB_NAME, COLLECTION_NAME, codecRegistry);
  }

  @Override
  public List<MongoIndex> getIndexes() {
    List<MongoIndex> indexes = super.getIndexes();
    indexes.add(MongoIndex.builder().key(SandboxConfig.FieldDefs.ORGANIZATION_ID).unique().build());
    return indexes;
  }

  public Optional<SandboxConfig> findByOrganizationId(ObjectId organizationId) {
    return Optional.ofNullable(
        getCollection().find(eq(SandboxConfig.FieldDefs.ORGANIZATION_ID, organizationId)).first());
  }

  public Optional<SandboxConfig> findByIds(ObjectId orgId, ObjectId sandboxId) {
    return Optional.ofNullable(
        getCollection()
            .find(and(eq(FieldDefs.ID, sandboxId), eq(FieldDefs.ORGANIZATION_ID, orgId)))
            .first());
  }

  public void createConfig(SandboxConfig config) {
    getCollection().insertOne(config);
  }

  public void updateIsSandboxEnabled(ObjectId organizationId, boolean isSandboxEnabled) {

    final Bson query = eq(SandboxConfig.FieldDefs.ORGANIZATION_ID, organizationId);

    final Bson updates =
        combine(
            set(SandboxConfig.FieldDefs.IS_SANDBOX_ENABLED, isSandboxEnabled),
            set(FieldDefs.IS_LANDING_PAGE_ENABLED, isSandboxEnabled));

    getCollection().updateOne(query, updates);
  }

  public void updateClusterTemplate(
      ObjectId organizationId, SandboxClusterTemplate clusterTemplate) {
    final Bson query = eq(SandboxConfig.FieldDefs.ORGANIZATION_ID, organizationId);

    final Bson updates = set(FieldDefs.CLUSTER_TEMPLATE, clusterTemplate);

    getCollection().updateOne(query, updates);
  }

  public DeleteResult deleteConfig(ObjectId organizationId) {
    final Bson query = eq(SandboxConfig.FieldDefs.ORGANIZATION_ID, organizationId);

    return getCollection().deleteOne(query);
  }
}
