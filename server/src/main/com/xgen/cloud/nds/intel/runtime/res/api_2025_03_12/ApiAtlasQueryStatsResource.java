package com.xgen.cloud.nds.intel.runtime.res.api_2025_03_12;

import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.IPA_EXCEPTION;
import static com.xgen.cloud.openapi._public.constant.OpenApiConst.Extensions.OPERATION_ID_OVERRIDE;

import com.xgen.cloud.access.activity._public.event.AccessEvent.Type;
import com.xgen.cloud.access.authz._public.svc.AuthzSvc;
import com.xgen.cloud.access.role._public.model.RoleSet;
import com.xgen.cloud.billingplatform.model.plan._public.model.PlanTypeSet;
import com.xgen.cloud.common.appsettings._public.svc.AppSettings;
import com.xgen.cloud.common.featureFlag._public.annotation.Feature;
import com.xgen.cloud.common.featureFlag._public.model.FeatureFlag;
import com.xgen.cloud.common.versioning._public.constants.VersionMediaType;
import com.xgen.cloud.group._public.model.Group;
import com.xgen.cloud.group._public.svc.GroupSvc;
import com.xgen.cloud.monitoring.querystats._public.model.QueryShapeStatistic;
import com.xgen.cloud.monitoring.querystats._public.svc.QueryStatsSvc;
import com.xgen.cloud.openapi._public.constant.OpenApiConst;
import com.xgen.cloud.organization._public.model.Organization;
import com.xgen.cloud.user._public.model.AppUser;
import com.xgen.svc.mms.api.res.common.ApiErrorCode;
import com.xgen.svc.mms.api.res.monitoring.querystats.ApiBaseQueryStatsResource;
import com.xgen.svc.mms.api.view.monitoring.querystats.ApiQueryStatsDetailsResponseView;
import com.xgen.svc.mms.api.view.monitoring.querystats.ApiQueryStatsSummaryListResponseView;
import com.xgen.svc.mms.res.filter.AllowNDSCNRegionsOnlyGroups;
import com.xgen.svc.mms.res.filter.PaidInFull;
import com.xgen.svc.mms.res.filter.SubscriptionPlan;
import com.xgen.svc.mms.res.filter.annotation.AccessAudit;
import com.xgen.svc.mms.res.filter.annotation.RateLimiter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.Explode;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.enums.ParameterStyle;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import java.util.List;

@Path("/api/atlas/v2/groups/{groupId}/clusters/{clusterName}/queryShapeInsights")
@AllowNDSCNRegionsOnlyGroups
@Feature(FeatureFlag.QUERY_SHAPE_INSIGHTS)
@PaidInFull
@RolesAllowed({RoleSet.NAME.GROUP_READ_ONLY})
@Singleton
@SubscriptionPlan(PlanTypeSet.NDS)
@AccessAudit(auditEventType = Type.QUERY_SHAPE_INSIGHTS, auditableRoles = RoleSet.PII_AUDITABLE)
public class ApiAtlasQueryStatsResource extends ApiBaseQueryStatsResource {

  public static final String SHA256_HASH_REGEX = "^([a-fA-F0-9]{64})$";

  @Inject
  public ApiAtlasQueryStatsResource(
      final AppSettings appSettings,
      final QueryStatsSvc queryStatsSvc,
      final AuthzSvc authzSvc,
      final GroupSvc groupSvc) {
    super(appSettings, queryStatsSvc, authzSvc, groupSvc);
  }

  @GET
  @Path("/summaries")
  @Produces({VersionMediaType.V_2025_03_12_JSON})
  @RateLimiter(RateLimiter.Feature.QUERY_SHAPE_INSIGHTS)
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-102-path-alternate-resource-name-path-param",
            value = "Base API path conflicts with other API resources."),
      })
  @Operation(
      summary = "Return Query Statistic Summaries",
      operationId = "listGroupClusterQueryShapeInsightSummaries",
      description =
          "Returns a list of query shape statistics summaries for a given cluster. Query shape"
              + " statistics provide performance insights about MongoDB queries, helping users"
              + " identify problematic query patterns and potential optimizations.",
      tags = {"Query Shape Insights"},
      parameters = {
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies the cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(
            name = "since",
            description =
                "Date and time from which to retrieve query shape statistics. This parameter"
                    + " expresses its value in the number of milliseconds that have elapsed since"
                    + " the [UNIX epoch](https://en.wikipedia.org/wiki/Unix_time).\n\n"
                    + "- If you don't specify the **until** parameter, the endpoint returns data"
                    + " covering from the **since** value and the current time.\n"
                    + "- If you specify neither the **since** nor the **until** parameters, the"
                    + " endpoint returns data from the previous 24 hours.",
            in = ParameterIn.QUERY,
            schema =
                @Schema(
                    type = "integer",
                    format = "int64",
                    minimum = OpenApiConst.EPOCH_TIME_MILLISEC_MIN),
            extensions = {
              @Extension(
                  name = IPA_EXCEPTION,
                  properties = {
                    @ExtensionProperty(
                        name = "xgen-IPA-117-description-should-not-use-inline-links",
                        value = "Parameters don't support externalDocs."),
                  })
            }),
        @Parameter(
            name = "until",
            description =
                "Date and time up until which to retrieve query shape statistics. This"
                    + " parameter expresses its value in the number of milliseconds that have"
                    + " elapsed since the [UNIX"
                    + " epoch](https://en.wikipedia.org/wiki/Unix_time).\n\n"
                    + "- If you specify the **until** parameter, you must specify the **since**"
                    + " parameter.\n"
                    + "- If you specify neither the **since** nor the **until** parameters, the"
                    + " endpoint returns data from the previous 24 hours.",
            in = ParameterIn.QUERY,
            schema =
                @Schema(
                    type = "integer",
                    format = "int64",
                    minimum = OpenApiConst.EPOCH_TIME_MILLISEC_MIN),
            extensions = {
              @Extension(
                  name = IPA_EXCEPTION,
                  properties = {
                    @ExtensionProperty(
                        name = "xgen-IPA-117-description-should-not-use-inline-links",
                        value = "Parameters don't support externalDocs."),
                  })
            }),
        @Parameter(
            name = "processIds",
            description =
                "ProcessIds from which to retrieve query shape statistics. A processId is a"
                    + " combination of host and port that serves the MongoDB process. The host must"
                    + " be the hostname, FQDN, IPv4 address, or IPv6 address of the host that runs"
                    + " the MongoDB process (`mongod` or `mongos`). The port must be the IANA port"
                    + " on which the MongoDB process listens for requests. To include multiple"
                    + " processIds, pass the parameter multiple times delimited with an ampersand"
                    + " (`&`) between each processId.",
            in = ParameterIn.QUERY,
            array =
                @ArraySchema(
                    schema = @Schema(type = "string", pattern = OpenApiConst.HOST_AND_PORT_REGEX),
                    maxItems = 10),
            explode = Explode.TRUE,
            style = ParameterStyle.FORM),
        @Parameter(
            name = "namespaces",
            description =
                "Namespaces from which to retrieve query shape statistics. A namespace consists of"
                    + " one database and one collection resource written as `.`:"
                    + " `<database>.<collection>`. To include multiple namespaces, pass the"
                    + " parameter multiple times delimited with an ampersand (`&`) between each"
                    + " namespace. Omit this parameter to return results for all namespaces.",
            in = ParameterIn.QUERY,
            array = @ArraySchema(schema = @Schema(type = "string"), maxItems = 10),
            explode = Explode.TRUE,
            style = ParameterStyle.FORM),
        @Parameter(
            name = "commands",
            description =
                "Retrieve query shape statistics matching specified MongoDB commands. To include"
                    + " multiple commands, pass the parameter multiple times delimited with an"
                    + " ampersand (`&`) between each command. The currently supported parameters"
                    + " are find, distinct, and aggregate. Omit this parameter to return results"
                    + " for all supported commands.",
            in = ParameterIn.QUERY,
            array =
                @ArraySchema(
                    arraySchema =
                        @Schema(
                            description =
                                "MongoDB commands from which to retrieve query statistics. To"
                                    + " include multiple commands, pass the parameter multiple"
                                    + " times delimited with an ampersand (`&`) between each"
                                    + " command. Omit this parameter to return results for all"
                                    + " supported commands."),
                    schema =
                        @Schema(
                            type = "string",
                            allowableValues = {"find", "distinct", "aggregate"}),
                    extensions =
                        @Extension(
                            name = IPA_EXCEPTION,
                            properties = {
                              @ExtensionProperty(
                                  name = "xgen-IPA-123-enum-values-must-be-upper-snake-case",
                                  value =
                                      "the values returned from the database for the command"
                                          + " parameter are in lower case"),
                            }),
                    maxItems = 3,
                    uniqueItems = true),
            explode = Explode.TRUE,
            style = ParameterStyle.FORM),
        @Parameter(
            name = "nSummaries",
            description = "Maximum number of query statistic summaries to return.",
            in = ParameterIn.QUERY,
            schema =
                @Schema(
                    type = "integer",
                    format = "int64",
                    defaultValue = "100",
                    minimum = "1",
                    maximum = "100")),
        @Parameter(
            name = "series",
            description =
                "Query shape statistics data series to retrieve. A series represents a specific"
                    + " metric about query execution. To include multiple series, pass the"
                    + " parameter multiple times delimited with an ampersand (`&`) between each"
                    + " series. Omit this parameter to return results for all available series.",
            in = ParameterIn.QUERY,
            array =
                @ArraySchema(
                    arraySchema =
                        @Schema(
                            description =
                                "Query shape statistics data series to retrieve. A series"
                                    + " represents a specific metric about query execution. To"
                                    + " include multiple series, pass the parameter multiple times"
                                    + " delimited with an ampersand (`&`) between each series. Omit"
                                    + " this parameter to return results for all available"
                                    + " series."),
                    schema =
                        @Schema(
                            type = "string",
                            allowableValues = {
                              "TOTAL_EXECUTION_TIME",
                              "AVG_EXECUTION_TIME",
                              "EXECUTION_COUNT",
                              "KEYS_EXAMINED",
                              "DOCS_EXAMINED",
                              "DOCS_RETURNED",
                              "TOTAL_TIME_TO_RESPONSE",
                              "BYTES_READ",
                              "KEYS_EXAMINED_RETURNED",
                              "DOCS_EXAMINED_RETURNED",
                              "LAST_EXECUTION_TIME",
                              "P50_EXECUTION_TIME",
                              "P90_EXECUTION_TIME",
                              "P99_EXECUTION_TIME"
                            }),
                    maxItems = 14,
                    uniqueItems = true),
            explode = Explode.TRUE,
            style = ParameterStyle.FORM),
        @Parameter(
            name = "queryShapeHashes",
            description =
                "A list of SHA256 hashes of desired query shapes, output by MongoDB commands like"
                    + " $queryStats and $explain or slow query logs. To include multiple series,"
                    + " pass the parameter multiple times delimited with an ampersand (`&`) between"
                    + " each series. Omit this parameter to return results for all available"
                    + " series.",
            in = ParameterIn.QUERY,
            array =
                @ArraySchema(
                    arraySchema =
                        @Schema(
                            description =
                                "A list of SHA256 hashes of desired query shapes, output by MongoDB"
                                    + " commands like $queryStats and $explain or slow query logs."
                                    + " To include multiple series, pass the parameter multiple"
                                    + " times delimited with an ampersand (`&`) between each"
                                    + " series. Omit this parameter to return results for all"
                                    + " available series."),
                    schema = @Schema(type = "string", pattern = SHA256_HASH_REGEX),
                    maxItems = 10)),
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2025_03_12_JSON,
                    schema = @Schema(implementation = ApiQueryStatsSummaryListResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_2025_03_12_EXTENSION_TYPE)
                          }),
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "listQueryShapeSummaries")
            })
      })
  public Response getSummary(
      @Context final HttpServletRequest request,
      @Context final Organization organization,
      @Context final Group group,
      @Context final AppUser currentUser,
      @Parameter(hidden = true) @PathParam("groupId") final String groupId,
      @Parameter(hidden = true) @PathParam("clusterName") final String clusterName,
      @Parameter(hidden = true) @QueryParam("since") final Long since,
      @Parameter(hidden = true) @QueryParam("until") final Long until,
      @Parameter(hidden = true) @QueryParam("series[]") final List<QueryShapeStatistic> series,
      @Parameter(hidden = true) @QueryParam("queryShapeHashes[]")
          final List<String> queryShapeHashes,
      @Parameter(hidden = true) @QueryParam("processIds[]") final List<String> hostnamePorts,
      @Parameter(hidden = true) @QueryParam("namespaces[]") final List<String> namespaces,
      @Parameter(hidden = true) @QueryParam("commands[]") final List<String> commands,
      @Parameter(hidden = true) @QueryParam("nSummaries") final Integer limit,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope)
      throws Exception {
    try {
      ApiBaseQueryStatsResource.validateHostnamePorts(hostnamePorts);
      return super.getSummary(
          request,
          currentUser,
          group,
          clusterName,
          since,
          until,
          series,
          queryShapeHashes,
          hostnamePorts,
          namespaces,
          commands,
          limit,
          envelope);
    } catch (IllegalArgumentException e) {
      throw ApiErrorCode.INVALID_QUERY_PARAMETER.exception(true, e.getMessage());
    } catch (Exception e) {
      throw ApiErrorCode.UNEXPECTED_ERROR.exception(true);
    }
  }

  @GET
  @Path("/{queryShapeHash}/details")
  @Produces({VersionMediaType.V_2025_03_12_JSON})
  @RateLimiter(RateLimiter.Feature.QUERY_SHAPE_INSIGHTS)
  @Extension(
      name = IPA_EXCEPTION,
      properties = {
        @ExtensionProperty(
            name = "xgen-IPA-113-singleton-should-have-update-method",
            value = "No update method needed for this singleton resource."),
      })
  @Operation(
      summary = "Return Query Shape Details",
      operationId = "getGroupClusterQueryShapeInsightDetails",
      description = "Returns the metadata and statistics summary for a given query shape hash.",
      tags = {"Query Shape Insights"},
      parameters = {
        @Parameter(ref = "envelope"),
        @Parameter(ref = "pretty"),
        @Parameter(ref = "groupId"),
        @Parameter(
            name = "clusterName",
            description = "Human-readable label that identifies the cluster.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = OpenApiConst.CLUSTER_NAME_REGEX),
            required = true),
        @Parameter(
            name = "since",
            description =
                "Date and time from which to retrieve query shape statistics. This parameter"
                    + " expresses its value in the number of milliseconds that have elapsed since"
                    + " the [UNIX epoch](https://en.wikipedia.org/wiki/Unix_time).\n\n"
                    + "- If you don't specify the **until** parameter, the endpoint returns data"
                    + " covering from the **since** value and the current time.\n"
                    + "- If you specify neither the **since** nor the **until** parameters, the"
                    + " endpoint returns data from the previous 24 hours.",
            in = ParameterIn.QUERY,
            schema =
                @Schema(
                    type = "integer",
                    format = "int64",
                    minimum = OpenApiConst.EPOCH_TIME_MILLISEC_MIN),
            extensions = {
              @Extension(
                  name = IPA_EXCEPTION,
                  properties = {
                    @ExtensionProperty(
                        name = "xgen-IPA-117-description-should-not-use-inline-links",
                        value = "Parameters don't support externalDocs."),
                  })
            }),
        @Parameter(
            name = "until",
            description =
                "Date and time up until which to retrieve query shape statistics. This"
                    + " parameter expresses its value in the number of milliseconds that have"
                    + " elapsed since the [UNIX"
                    + " epoch](https://en.wikipedia.org/wiki/Unix_time).\n\n"
                    + "- If you specify the **until** parameter, you must specify the **since**"
                    + " parameter.\n"
                    + "- If you specify neither the **since** nor the **until** parameters, the"
                    + " endpoint returns data from the previous 24 hours.",
            in = ParameterIn.QUERY,
            schema =
                @Schema(
                    type = "integer",
                    format = "int64",
                    minimum = OpenApiConst.EPOCH_TIME_MILLISEC_MIN),
            extensions = {
              @Extension(
                  name = IPA_EXCEPTION,
                  properties = {
                    @ExtensionProperty(
                        name = "xgen-IPA-117-description-should-not-use-inline-links",
                        value = "Parameters don't support externalDocs."),
                  })
            }),
        @Parameter(
            name = "processIds",
            description =
                "ProcessIds from which to retrieve query shape statistics. A processId is a"
                    + " combination of host and port that serves the MongoDB process. The host must"
                    + " be the hostname, FQDN, IPv4 address, or IPv6 address of the host that runs"
                    + " the MongoDB process (`mongod` or `mongos`). The port must be the IANA port"
                    + " on which the MongoDB process listens for requests. To include multiple"
                    + " processIds, pass the parameter multiple times delimited with an ampersand"
                    + " (`&`) between each processId.",
            in = ParameterIn.QUERY,
            array =
                @ArraySchema(
                    schema = @Schema(type = "string", pattern = OpenApiConst.HOST_AND_PORT_REGEX),
                    maxItems = 10),
            explode = Explode.TRUE,
            style = ParameterStyle.FORM),
        @Parameter(
            name = "queryShapeHash",
            description =
                "A SHA256 hash of a query shape, output by MongoDB commands like $queryStats and"
                    + " $explain or slow query logs.",
            in = ParameterIn.PATH,
            schema = @Schema(type = "string", pattern = SHA256_HASH_REGEX),
            required = true),
      },
      responses = {
        @ApiResponse(
            responseCode = "200",
            description = "OK",
            content =
                @Content(
                    mediaType = VersionMediaType.V_2025_03_12_JSON,
                    schema = @Schema(implementation = ApiQueryStatsDetailsResponseView.class),
                    extensions = {
                      @Extension(
                          properties = {
                            @ExtensionProperty(
                                name = "x-xgen-version",
                                value = VersionMediaType.V_2025_03_12_EXTENSION_TYPE)
                          }),
                    })),
        @ApiResponse(responseCode = "400", ref = "badRequest"),
        @ApiResponse(responseCode = "401", ref = "unauthorized"),
        @ApiResponse(responseCode = "403", ref = "forbidden"),
        @ApiResponse(responseCode = "404", ref = "notFound"),
        @ApiResponse(responseCode = "500", ref = "internalServerError")
      },
      extensions = {
        @Extension(
            properties = {
              @ExtensionProperty(name = OPERATION_ID_OVERRIDE, value = "getQueryShapeDetails")
            })
      })
  public Response getDetails(
      @Context final HttpServletRequest request,
      @Context final Organization organization,
      @Context final Group group,
      @Context final AppUser currentUser,
      @Parameter(hidden = true) @PathParam("groupId") final String groupId,
      @Parameter(hidden = true) @PathParam("clusterName") final String clusterName,
      @Parameter(hidden = true) @PathParam("queryShapeHash") final String queryShapeHash,
      @Parameter(hidden = true) @QueryParam("since") final Long since,
      @Parameter(hidden = true) @QueryParam("until") final Long until,
      @Parameter(hidden = true) @QueryParam("processIds[]") final List<String> hostnamePorts,
      @Parameter(hidden = true) @QueryParam("envelope") final Boolean envelope)
      throws Exception {
    try {
      ApiBaseQueryStatsResource.validateHostnamePorts(hostnamePorts);
      return super.getDetails(
          currentUser, group, clusterName, since, until, queryShapeHash, hostnamePorts, envelope);
    } catch (IllegalArgumentException e) {
      throw ApiErrorCode.INVALID_QUERY_PARAMETER.exception(true, e.getMessage());
    } catch (Exception e) {
      throw ApiErrorCode.UNEXPECTED_ERROR.exception(true);
    }
  }
}
